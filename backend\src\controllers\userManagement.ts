import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { decryptUserDataAsync, decryptProfilDataAsync } from '../utils/encryption';
import { queueEmail } from '../services/emailQueueService';
import { redis } from '../config/redis';

// Cache TTL pour les données utilisateur
const USER_CACHE_TTL = 300; // 5 minutes
const STATS_CACHE_TTL = 900; // 15 minutes

/**
 * Récupérer la liste des utilisateurs avec pagination et filtres
 */
export const getUsers = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = '',
      status = '',
      userType = '',
      sortBy = 'created_at',
      sortOrder = 'desc',
      verificationStatus = '',
      subscriptionType = '',
      lastActivity = ''
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = Math.min(parseInt(limit as string), 100); // Limite max 100
    const offset = (pageNum - 1) * limitNum;

    // Construction de la requête de base avec toutes les relations
    let query = supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        user_type,
        profil_actif,
        email_verifier,
        profil_verifier,
        identite_verifier,
        entreprise_verifier,
        assurance_verifier,
        is_online,
        last_activity,
        date_inscription,
        created_at,
        updated_at,
        is_anonymized,
        suspension_reason,
        suspended_until,
        user_profil (
          nom,
          prenom,
          ville,
          code_postal,
          photo_url,
          mode_vacance,
          profil_visible,
          seo_indexable,
          created_at,
          updated_at
        ),
        user_jobi (
          montant,
          updated_at
        ),
        user_ai_credits (
          credits,
          updated_at
        ),
        user_abo (
          type_abonnement,
          statut,
          date_debut,
          date_fin,
          renouvellement_auto,
          created_at
        )
      `, { count: 'exact' });

    // Application des filtres
    if (search) {
      // Recherche dans email, nom, prénom (attention au chiffrement)
      query = query.or(`email.ilike.%${search}%`);
    }

    if (role) {
      query = query.eq('role', role);
    }

    if (status === 'active') {
      query = query.eq('profil_actif', true);
    } else if (status === 'inactive') {
      query = query.eq('profil_actif', false);
    } else if (status === 'suspended') {
      query = query.not('suspended_until', 'is', null);
    } else if (status === 'anonymized') {
      query = query.eq('is_anonymized', true);
    }

    if (userType) {
      query = query.eq('user_type', userType);
    }

    if (verificationStatus === 'verified') {
      query = query.eq('profil_verifier', true);
    } else if (verificationStatus === 'pending') {
      query = query.eq('profil_verifier', false).eq('profil_actif', true);
    }

    if (lastActivity) {
      const activityDate = new Date();
      if (lastActivity === '24h') {
        activityDate.setHours(activityDate.getHours() - 24);
      } else if (lastActivity === '7d') {
        activityDate.setDate(activityDate.getDate() - 7);
      } else if (lastActivity === '30d') {
        activityDate.setDate(activityDate.getDate() - 30);
      }
      query = query.gte('last_activity', activityDate.toISOString());
    }

    // Tri et pagination
    query = query
      .order(sortBy as string, { ascending: sortOrder === 'asc' })
      .range(offset, offset + limitNum - 1);

    const { data: users, error, count } = await query;

    if (error) {
      logger.error('Erreur lors de la récupération des utilisateurs:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des utilisateurs',
        toastType: 'error'
      });
    }

    // Déchiffrement des données sensibles et transformation des relations
    const decryptedUsers = await Promise.all(
      (users || []).map(async (user) => {
        try {
          const decryptedUser = await decryptUserDataAsync(user);
          
          // Transformer user_profil (tableau vers objet unique)
          let transformedUser = { ...decryptedUser };
          if (user.user_profil && user.user_profil.length > 0) {
            const decryptedProfil = await decryptProfilDataAsync(user.user_profil[0]);
            transformedUser.user_profil = decryptedProfil;
          }
          
          // Transformer user_jobi (tableau vers objet unique)
          if (user.user_jobi && user.user_jobi.length > 0) {
            transformedUser.user_jobi = user.user_jobi[0];
          }
          
          // Transformer user_ai_credits (tableau vers objet unique)
          if (user.user_ai_credits && user.user_ai_credits.length > 0) {
            transformedUser.user_ai_credits = user.user_ai_credits[0];
          }
          
          // user_abo reste un tableau car un utilisateur peut avoir plusieurs abonnements
          
          return transformedUser;
        } catch (error) {
          logger.warn(`Erreur de déchiffrement pour l'utilisateur ${user.id}:`, error);
          return user;
        }
      })
    );

    const totalPages = Math.ceil((count || 0) / limitNum);

    const response = {
      success: true,
      data: {
        users: decryptedUsers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: count || 0,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        },
        filters: {
          search,
          role,
          status,
          userType,
          verificationStatus,
          subscriptionType,
          lastActivity,
          sortBy,
          sortOrder
        }
      }
    };

    res.json(response);

  } catch (error) {
    logger.error('Erreur dans getUsers:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des utilisateurs',
      toastType: 'error'
    });
  }
};

/**
 * Récupérer les détails complets d'un utilisateur
 */
export const getUserDetails = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur requis',
        toastType: 'error'
      });
    }

    // Vérifier le cache
    const cacheKey = `user_details:${userId}`;
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    // Récupération parallèle de toutes les données utilisateur
    const [
      userResult,
      profilResult,
      abonnementsResult,
      jobiResult,
      jobiHistoriqueResult,
      transactionsResult,
      servicesResult,
      missionsResult,
      missionCandidaturesResult,
      reviewsReceivedResult,
      reviewsGivenResult,
      badgesResult,
      loginHistoryResult,
      notificationsResult,
      aiCreditsResult,
      aiCreditsHistoriqueResult,
      galleriesResult,
      featuredPhotosResult,
      invoicesResult,
      clientsResult,
      messagesResult,
      reportedContentResult,
      aiGenerationStatsResult
    ] = await Promise.all([
      // Données utilisateur de base
      supabase.from('users').select('*').eq('id', userId).single(),

      // Profil utilisateur
      supabase.from('user_profil').select('*').eq('user_id', userId).single(),

      // Abonnements
      supabase.from('user_abo').select('*').eq('user_id', userId).order('created_at', { ascending: false }),

      // Jobi
      supabase.from('user_jobi').select('*').eq('user_id', userId).single(),

      // Historique Jobi (50 derniers)
      supabase.from('user_jobi_historique').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Transactions (50 dernières)
      supabase.from('user_transac').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Services
      supabase.from('user_services').select('*').eq('user_id', userId).order('created_at', { ascending: false }),

      // Missions créées (50 dernières)
      supabase.from('user_missions').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Candidatures aux missions (50 dernières)
      supabase.from('user_mission_candidature').select(`
        *,
        mission:user_missions(titre, category_id, budget, statut)
      `).eq('jobbeur_id', userId).order('created_at', { ascending: false }).limit(50),

      // Avis reçus (50 derniers)
      supabase.from('user_reviews').select(`
        *,
        author:users!user_reviews_author_id_fkey(email)
      `).eq('target_user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Avis donnés (50 derniers)
      supabase.from('user_reviews').select(`
        *,
        target:users!user_reviews_target_user_id_fkey(email)
      `).eq('author_id', userId).order('created_at', { ascending: false }).limit(50),

      // Badges
      supabase.from('user_badges').select('*').eq('user_id', userId).order('date_obtention', { ascending: false }),

      // Historique de connexion (50 derniers)
      supabase.from('user_login_history').select('*').eq('user_id', userId).order('login_date', { ascending: false }).limit(50),

      // Notifications (100 dernières)
      supabase.from('user_notifications').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(100),

      // Crédits IA
      supabase.from('user_ai_credits').select('*').eq('user_id', userId).single(),

      // Historique crédits IA (50 derniers)
      supabase.from('user_ai_credits_historique').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Galeries photos
      supabase.from('user_gallery').select(`
        *,
        user_gallery_photos(*)
      `).eq('user_id', userId).order('created_at', { ascending: false }),

      // Photos mises en avant
      supabase.from('user_featured_photos').select('*').eq('user_id', userId).order('created_at', { ascending: false }),

      // Factures/Devis
      supabase.from('invoices').select('*').eq('user_id', userId).order('date_creation', { ascending: false }).limit(50),

      // Clients (si c'est un jobbeur)
      supabase.from('clients').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50),

      // Messages (conversations récentes) - récupération simple d'abord
      supabase.from('user_messages_conversations').select(`
        *,
        user_messages(*)
      `).or(`user1_id.eq.${userId},user2_id.eq.${userId}`).order('updated_at', { ascending: false }).limit(20),

      // Contenus signalés
      supabase.from('reported_content').select('*').eq('reported_by', userId).order('created_at', { ascending: false }).limit(20),

      // Statistiques génération IA
      supabase.from('ai_generation_stats').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(50)
    ]);

    if (userResult.error || !userResult.data) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    // Déchiffrement des données sensibles
    const decryptedUser = await decryptUserDataAsync(userResult.data);
    const decryptedProfil = profilResult.data ? await decryptProfilDataAsync(profilResult.data) : null;

    // Enrichir les conversations avec les données utilisateur
    let enrichedMessages = messagesResult.data || [];
    if (enrichedMessages.length > 0) {
      enrichedMessages = await Promise.all(enrichedMessages.map(async (conversation: any) => {
        // Déterminer l'autre utilisateur
        const otherUserId = conversation.user1_id === userId ? conversation.user2_id : conversation.user1_id;
        
        if (otherUserId && otherUserId !== 'null') {
          try {
            // Récupérer les données de l'autre utilisateur
            const { data: otherUserData, error: otherUserError } = await supabase
              .from('users')
              .select('id, email')
              .eq('id', otherUserId)
              .single();

            const { data: otherUserProfil, error: otherProfilError } = await supabase
              .from('user_profil')
              .select('prenom, nom, photo_url')
              .eq('user_id', otherUserId)
              .single();

            if (otherUserData && !otherUserError) {
              // Déchiffrer les données de l'autre utilisateur
              const decryptedOtherUser = await decryptUserDataAsync(otherUserData);
              const decryptedOtherProfil = otherUserProfil && !otherProfilError ? await decryptProfilDataAsync(otherUserProfil) : null;

              // Créer l'objet utilisateur enrichi
              const enrichedOtherUser = {
                id: decryptedOtherUser.id,
                email: decryptedOtherUser.email,
                prenom: decryptedOtherProfil?.prenom || null,
                nom: decryptedOtherProfil?.nom || null,
                first_name: decryptedOtherProfil?.prenom || null,
                last_name: decryptedOtherProfil?.nom || null,
                avatar_url: decryptedOtherProfil?.photo_url || null
              };

              // Ajouter les données utilisateur à la conversation
              if (conversation.user1_id === userId) {
                conversation.user2 = enrichedOtherUser;
              } else {
                conversation.user1 = enrichedOtherUser;
              }
              conversation.otherUser = enrichedOtherUser;
            }
          } catch (error) {
            logger.error(`Erreur lors de l'enrichissement de la conversation ${conversation.id}:`, error);
          }
        }

        return conversation;
      }));
    }

    const response = {
      success: true,
      data: {
        user: decryptedUser,
        profil: decryptedProfil,
        abonnements: abonnementsResult.data || [],
        jobi: jobiResult.data || { montant: 0 },
        jobiHistorique: jobiHistoriqueResult.data || [],
        transactions: transactionsResult.data || [],
        services: servicesResult.data || [],
        missions: missionsResult.data || [],
        missionCandidatures: missionCandidaturesResult.data || [],
        reviewsReceived: reviewsReceivedResult.data || [],
        reviewsGiven: reviewsGivenResult.data || [],
        badges: badgesResult.data || [],
        loginHistory: loginHistoryResult.data || [],
        notifications: notificationsResult.data || [],
        aiCredits: aiCreditsResult.data || { credits: 0 },
        aiCreditsHistorique: aiCreditsHistoriqueResult.data || [],
        galleries: galleriesResult.data || [],
        featuredPhotos: featuredPhotosResult.data || [],
        invoices: invoicesResult.data || [],
        clients: clientsResult.data || [],
        messages: enrichedMessages,
        reportedContent: reportedContentResult.data || [],
        aiGenerationStats: aiGenerationStatsResult.data || []
      }
    };

    // Mettre en cache pour 5 minutes
    await redis.setex(cacheKey, USER_CACHE_TTL, JSON.stringify(response));

    res.json(response);

  } catch (error) {
    logger.error('Erreur dans getUserDetails:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des détails utilisateur',
      toastType: 'error'
    });
  }
};

/**
 * Récupérer les statistiques complètes d'un utilisateur
 */
export const getUserStats = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { period = 'all' } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'ID utilisateur requis',
        toastType: 'error'
      });
    }

    // Vérifier le cache
    const cacheKey = `user_stats:${userId}:${period}`;
    const cachedStats = await redis.get(cacheKey);
    if (cachedStats) {
      return res.json(JSON.parse(cachedStats));
    }

    // Calculer les dates selon la période
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default: // 'all'
        startDate.setFullYear(2020); // Date de début du service
        break;
    }

    // Récupérer les informations de base de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        created_at,
        last_activity,
        is_online,
        user_profil (
          nom,
          prenom,
          telephone,
          ville,
          profil_visible
        )
      `)
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    // 1. Statistiques des missions
    const { data: missionsData, error: missionsError } = await supabase
      .from('user_missions')
      .select('id, statut, budget, created_at')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    const missions = missionsData || [];
    const missionsByStatus = missions.reduce((acc: any, mission: any) => {
      acc[mission.statut] = (acc[mission.statut] || 0) + 1;
      return acc;
    }, {});

    const totalMissionBudget = missions.reduce((sum: number, mission: any) => {
      return sum + (parseFloat(mission.budget) || 0);
    }, 0);

    // 2. Statistiques des candidatures
    const { data: candidaturesData, error: candidaturesError } = await supabase
      .from('user_mission_candidature')
      .select('id, statut, montant_propose, created_at, payment_status')
      .eq('jobbeur_id', userId)
      .gte('created_at', startDate.toISOString());

    const candidatures = candidaturesData || [];
    const candidaturesByStatus = candidatures.reduce((acc: any, candidature: any) => {
      acc[candidature.statut] = (acc[candidature.statut] || 0) + 1;
      return acc;
    }, {});

    const totalEarnings = candidatures
      .filter((c: any) => c.statut === 'acceptee' && c.payment_status === 'paid')
      .reduce((sum: number, candidature: any) => {
        return sum + (parseFloat(candidature.montant_propose) || 0);
      }, 0);

    // 3. Statistiques des avis
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('user_reviews')
      .select('id, note, created_at')
      .eq('target_user_id', userId)
      .eq('statut', 'visible')
      .gte('created_at', startDate.toISOString());

    const reviews = reviewsData || [];
    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum: number, review: any) => sum + review.note, 0) / reviews.length 
      : 0;

    // 4. Statistiques de connexion
    const { data: loginData, error: loginError } = await supabase
      .from('user_login_history')
      .select('id, login_date, ip_address')
      .eq('user_id', userId)
      .gte('login_date', startDate.toISOString())
      .order('login_date', { ascending: false });

    const logins = loginData || [];
    const uniqueIPs = new Set(logins.map((login: any) => login.ip_address)).size;
    const avgConnectionsPerDay = period !== 'all' 
      ? logins.length / Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)))
      : 0;

    // 5. Statistiques Jobi
    const { data: jobiData, error: jobiError } = await supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', userId)
      .single();

    const currentJobi = jobiData?.montant || 0;

    const { data: jobiHistoryData, error: jobiHistoryError } = await supabase
      .from('user_jobi_historique')
      .select('montant, date_creation')
      .eq('user_id', userId)
      .gte('date_creation', startDate.toISOString());

    const jobiHistory = jobiHistoryData || [];
    const jobiIn = jobiHistory
      .filter((h: any) => h.montant > 0)
      .reduce((sum: number, h: any) => sum + h.montant, 0);
    const jobiOut = Math.abs(jobiHistory
      .filter((h: any) => h.montant < 0)
      .reduce((sum: number, h: any) => sum + h.montant, 0));

    // 6. Statistiques AI Credits
    const { data: aiCreditsData, error: aiCreditsError } = await supabase
      .from('user_ai_credits')
      .select('credits_disponibles')
      .eq('user_id', userId)
      .single();

    const currentAiCredits = aiCreditsData?.credits_disponibles || 0;

    const { data: aiUsageData, error: aiUsageError } = await supabase
      .from('user_ai_credits_historique')
      .select('credits_utilises, created_at')
      .eq('user_id', userId)
      .eq('operation_type', 'utilisation')
      .gte('created_at', startDate.toISOString());

    const aiUsage = aiUsageData || [];
    const totalAiCreditsUsed = aiUsage.reduce((sum: number, usage: any) => sum + (usage.credits_utilises || 0), 0);

    // 7. Évolution des connexions (pour le graphique)
    const connectionEvolution = [];
    if (period !== 'all') {
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      for (let i = 0; i < Math.min(days, 30); i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        const dayStart = new Date(date);
        dayStart.setHours(0, 0, 0, 0);
        const dayEnd = new Date(date);
        dayEnd.setHours(23, 59, 59, 999);
        
        const dayLogins = logins.filter((login: any) => {
          const loginDate = new Date(login.login_date);
          return loginDate >= dayStart && loginDate <= dayEnd;
        }).length;

        connectionEvolution.push({
          date: date.toISOString().split('T')[0],
          count: dayLogins
        });
      }
    }

    // Construire la réponse
    const stats = {
      general: {
        registrationDate: userData.created_at,
        lastActivity: userData.last_activity,
        isOnline: userData.is_online,
        accountAge: Math.floor((endDate.getTime() - new Date(userData.created_at).getTime()) / (1000 * 60 * 60 * 24))
      },
      missions: {
        total: missions.length,
        byStatus: missionsByStatus,
        totalBudget: totalMissionBudget
      },
      candidatures: {
        total: candidatures.length,
        byStatus: candidaturesByStatus,
        totalEarnings: totalEarnings
      },
      reviews: {
        total: reviews.length,
        averageRating: averageRating
      },
      activity: {
        loginCount: logins.length,
        uniqueIPs: uniqueIPs,
        avgConnectionsPerDay: avgConnectionsPerDay,
        avgMissionsPerDay: period !== 'all' 
          ? missions.length / Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)))
          : 0,
        activityRate: reviews.length > 0 ? (averageRating / 5) : 0
      },
      financial: {
        totalMissionBudget: totalMissionBudget,
        totalTransactionAmount: totalEarnings,
        currentJobi: currentJobi,
        jobiTransactions: {
          in: jobiIn,
          out: jobiOut
        },
        currentAiCredits: currentAiCredits,
        aiCreditUsage: totalAiCreditsUsed
      },
      connections: {
        evolution: connectionEvolution,
        uniqueIPs: uniqueIPs,
        avgPerDay: avgConnectionsPerDay
      }
    };

    const response = {
      success: true,
      data: {
        stats,
        period: period,
        timeRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        }
      }
    };

    // Mettre en cache pour 15 minutes
    await redis.setex(cacheKey, STATS_CACHE_TTL, JSON.stringify(response));

    res.json(response);

  } catch (error) {
    logger.error('Erreur dans getUserStats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des statistiques utilisateur',
      toastType: 'error'
    });
  }
};

/**
 * Gestion des Jobi - Ajouter/Retirer des crédits
 */
export const manageJobi = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { action, montant, description, sendNotification = true, sendEmail = true } = req.body;
    const adminUserId = req.user?.userId;


    if (!userId || !action || !montant) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action, montant)',
        toastType: 'error'
      });
    }

    const montantNum = parseFloat(montant);
    if (isNaN(montantNum) || montantNum <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Le montant doit être un nombre positif',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur cible
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    // Récupérer le solde actuel
    const { data: currentJobi } = await supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', userId)
      .single();

    const currentBalance = currentJobi?.montant || 0;
    let newBalance = currentBalance;

    if (action === 'add') {
      newBalance = currentBalance + montantNum;
    } else if (action === 'remove') {
      if (currentBalance < montantNum) {
        return res.status(400).json({
          success: false,
          message: 'Solde insuffisant pour effectuer cette opération',
          toastType: 'error'
        });
      }
      newBalance = currentBalance - montantNum;
    } else {
      return res.status(400).json({
        success: false,
        message: 'Action invalide (add ou remove)',
        toastType: 'error'
      });
    }

    // Mettre à jour le solde - utiliser une transaction pour éviter les doublons
    let updateError = null;
    
    if (currentJobi) {
      // Mettre à jour l'enregistrement existant
      const { error } = await supabase
        .from('user_jobi')
        .update({
          montant: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
      updateError = error;
    } else {
      // Créer un nouvel enregistrement
      const { error } = await supabase
        .from('user_jobi')
        .insert({
          user_id: userId,
          montant: newBalance,
          profil_complet: false,
          date_creation: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      updateError = error;
    }

    if (updateError) {
      logger.error('Erreur lors de la mise à jour du solde Jobi:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du solde',
        toastType: 'error'
      });
    }

    // Ajouter à l'historique
    const { error: historyError } = await supabase
      .from('user_jobi_historique')
      .insert({
        user_id: userId,
        titre: action === 'add' ? 'Crédit administrateur' : 'Débit administrateur',
        description: description || `${action === 'add' ? 'Ajout' : 'Retrait'} de ${montantNum} Jobi par un administrateur`,
        montant: action === 'add' ? montantNum : -montantNum,
        message: description || null
      });

    if (historyError) {
      logger.error('Erreur lors de l\'ajout à l\'historique Jobi:', historyError);
    }

    // Envoyer une notification
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'jobi',
          title: action === 'add' ? 'Crédit Jobi reçu' : 'Débit Jobi effectué',
          content: `${action === 'add' ? 'Vous avez reçu' : 'Un débit de'} ${montantNum} Jobi a été effectué sur votre compte par un administrateur.${description ? ` Motif: ${description}` : ''}`,
          link: '/dashboard/portefeuille',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email
    if (sendEmail && targetUser.email) {
      try {
        await queueEmail(
          targetUser.email,
          `${action === 'add' ? 'Crédit' : 'Débit'} Jobi sur votre compte JobPartiel`,
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">${action === 'add' ? 'Crédit' : 'Débit'} Jobi</h2>
            <p>Bonjour,</p>
            <p>${action === 'add' ? 'Un crédit de' : 'Un débit de'} <strong>${montantNum} Jobi</strong> a été effectué sur votre compte par un administrateur.</p>
            ${description ? `<p><strong>Motif :</strong> ${description}</p>` : ''}
            <p><strong>Nouveau solde :</strong> ${newBalance} Jobi</p>
            <p>Vous pouvez consulter votre portefeuille dans votre espace personnel.</p>
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email Jobi:', emailError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_stats:${userId}:*`);

    res.json({
      success: true,
      message: `${action === 'add' ? 'Crédit' : 'Débit'} de ${montantNum} Jobi effectué avec succès`,
      data: {
        action,
        montant: montantNum,
        previousBalance: currentBalance,
        newBalance,
        description,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageJobi:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion des Jobi',
      toastType: 'error'
    });
  }
};

/**
 * Gestion des crédits IA - Ajouter/Retirer des crédits
 */
export const manageAiCredits = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { action, credits, description, sendNotification = true, sendEmail = true } = req.body;
    const adminUserId = req.user?.userId;

    if (!userId || !action || !credits) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants (userId, action, credits)',
        toastType: 'error'
      });
    }

    const creditsNum = parseInt(credits);
    if (isNaN(creditsNum) || creditsNum <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Le nombre de crédits doit être un nombre entier positif',
        toastType: 'error'
      });
    }

    // Récupérer les informations de l'utilisateur cible
    const { data: targetUser, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !targetUser) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé',
        toastType: 'error'
      });
    }

    // Récupérer le solde actuel des crédits IA
    const { data: currentCredits } = await supabase
      .from('user_ai_credits')
      .select('credits')
      .eq('user_id', userId)
      .single();

    const currentBalance = currentCredits?.credits || 0;
    let newBalance = currentBalance;

    if (action === 'add') {
      newBalance = currentBalance + creditsNum;
    } else if (action === 'remove') {
      if (currentBalance < creditsNum) {
        return res.status(400).json({
          success: false,
          message: 'Crédits insuffisants pour effectuer cette opération',
          toastType: 'error'
        });
      }
      newBalance = currentBalance - creditsNum;
    } else {
      return res.status(400).json({
        success: false,
        message: 'Action invalide (add ou remove)',
        toastType: 'error'
      });
    }

    // Mettre à jour le solde des crédits IA - utiliser une transaction pour éviter les doublons
    let updateError = null;
    
    if (currentCredits) {
      // Mettre à jour l'enregistrement existant
      const { error } = await supabase
        .from('user_ai_credits')
        .update({
          credits: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
      updateError = error;
    } else {
      // Créer un nouvel enregistrement
      const { error } = await supabase
        .from('user_ai_credits')
        .insert({
          user_id: userId,
          credits: newBalance,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      updateError = error;
    }

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour des crédits',
        toastType: 'error'
      });
    }

    // Ajouter à l'historique
    const { error: historyError } = await supabase
      .from('user_ai_credits_historique')
      .insert({
        user_id: userId,
        operation_type: action === 'add' ? 'credit' : 'debit',
        credits: action === 'add' ? creditsNum : -creditsNum,
        description: description || `${action === 'add' ? 'Ajout' : 'Retrait'} de ${creditsNum} crédits IA par un administrateur`,
        admin_user_id: adminUserId
      });

    if (historyError) {
      logger.error('Erreur lors de l\'ajout à l\'historique des crédits IA:', historyError);
    }

    // Envoyer une notification
    if (sendNotification) {
      const { error: notifError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type: 'ai_credits',
          title: action === 'add' ? 'Crédits IA reçus' : 'Crédits IA débités',
          content: `${action === 'add' ? 'Vous avez reçu' : 'Un débit de'} ${creditsNum} crédits IA a été effectué sur votre compte par un administrateur.${description ? ` Motif: ${description}` : ''}`,
          link: '/dashboard/ai-credits',
          is_read: false,
          is_archived: false
        });

      if (notifError) {
        logger.error('Erreur lors de l\'envoi de la notification:', notifError);
      }
    }

    // Envoyer un email
    if (sendEmail && targetUser.email) {
      try {
        await queueEmail(
          targetUser.email,
          `${action === 'add' ? 'Crédit' : 'Débit'} de crédits IA sur votre compte JobPartiel`,
          `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #FF6B2C;">${action === 'add' ? 'Crédit' : 'Débit'} de crédits IA</h2>
            <p>Bonjour,</p>
            <p>${action === 'add' ? 'Un crédit de' : 'Un débit de'} <strong>${creditsNum} crédits IA</strong> a été effectué sur votre compte par un administrateur.</p>
            ${description ? `<p><strong>Motif :</strong> ${description}</p>` : ''}
            <p><strong>Nouveau solde :</strong> ${newBalance} crédits IA</p>
            <p>Vous pouvez consulter vos crédits IA dans votre espace personnel.</p>
            <p>Cordialement,<br>L'équipe JobPartiel</p>
          </div>
          `
        );
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email crédits IA:', emailError);
      }
    }

    // Invalider le cache
    await redis.del(`user_details:${userId}`);
    await redis.del(`user_stats:${userId}:*`);

    res.json({
      success: true,
      message: `${action === 'add' ? 'Crédit' : 'Débit'} de ${creditsNum} crédits IA effectué avec succès`,
      data: {
        action,
        credits: creditsNum,
        previousBalance: currentBalance,
        newBalance,
        description,
        adminUserId,
        timestamp: new Date().toISOString()
      },
      toastType: 'success'
    });

  } catch (error) {
    logger.error('Erreur dans manageAiCredits:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la gestion des crédits IA',
      toastType: 'error'
    });
  }
};
