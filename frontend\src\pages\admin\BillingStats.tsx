import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Button,
  CircularProgress,
  Chip,
  Alert,
  useTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { Helmet } from 'react-helmet-async';
import {
  FileText,
  TrendingUp,
  RefreshCw,
  DollarSign,
  CheckCircle,
  Clock,
  BarChart2,
  <PERSON><PERSON>hart as PieChartIcon,
  FileCheck
} from 'lucide-react';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import logger from '../../utils/logger';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Palette de couleurs pour les graphiques
const CHART_COLORS = [
  COLORS.primary,
  COLORS.secondary,
  COLORS.tertiary,
  COLORS.accent,
  COLORS.info,
  COLORS.success,
  COLORS.warning,
  '#9C27B0', // Violet
  '#00BCD4', // Cyan
  '#795548', // Marron
];

// Interface pour les statistiques de facturation
interface InvoiceStats {
  total: number;
  montantTotal: number;
  parStatut: {
    [key: string]: number;
  };
  parType: {
    [key: string]: number;
  };
  evolution: Array<{
    mois: string;
    montant: number;
    nombre: number;
  }>;
}

// Styles personnalisés
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

const ChipStyled = styled(Chip)(({ theme }) => ({
  borderRadius: '6px',
  fontWeight: 500,
  height: '24px',
  '&.MuiChip-colorPrimary': {
    backgroundColor: `${COLORS.primary}30`,
    color: COLORS.primary,
  },
  '&.MuiChip-colorSecondary': {
    backgroundColor: `${COLORS.info}30`,
    color: COLORS.info,
  },
  '&.MuiChip-colorSuccess': {
    backgroundColor: `${COLORS.success}30`,
    color: COLORS.success,
  },
  '&.MuiChip-colorError': {
    backgroundColor: `${COLORS.error}30`,
    color: COLORS.error,
  },
  '&.MuiChip-colorWarning': {
    backgroundColor: `${COLORS.warning}30`,
    color: COLORS.warning,
  },
}));

// Traduction des statuts
const statusTranslations: Record<string, string> = {
  'brouillon': 'Brouillon',
  'envoye': 'Envoyé',
  'accepte': 'Accepté',
  'refuse': 'Refusé',
  'facture': 'Facturé',
  'paye': 'Payé',
  'partiellement_paye': 'Partiellement payé',
  'annule': 'Annulé',
  'en_retard': 'En retard'
};

// Traduction des types de documents
const typeTranslations: Record<string, string> = {
  'devis': 'Devis',
  'facture': 'Facture',
  'avoir': 'Avoir'
};

// Fonction pour formater les montants
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount);
};

// Fonction pour obtenir la couleur en fonction du statut
const getStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    'brouillon': COLORS.neutral,
    'envoye': COLORS.info,
    'accepte': COLORS.success,
    'refuse': COLORS.error,
    'facture': COLORS.primary,
    'paye': COLORS.success,
    'partiellement_paye': COLORS.warning,
    'annule': COLORS.error,
    'en_retard': COLORS.error
  };
  
  return statusColors[status] || COLORS.neutral;
};

const BillingStats: React.FC = () => {
  const theme = useTheme();
  
  const [timeRange, setTimeRange] = useState<string>('30');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<InvoiceStats | null>(null);
  
  // Fonction pour récupérer les statistiques
  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${API_CONFIG.baseURL}/api/advanced-stats?timeRange=${timeRange}`, {
        withCredentials: true,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      if (response.data && response.data.invoiceStats) {
        setStats(response.data.invoiceStats);
      } else {
        setError('Aucune donnée de facturation disponible');
      }
    } catch (err: any) {
      logger.error('Erreur lors de la récupération des statistiques de facturation:', err);
      setError(err.response?.data?.message || 'Erreur lors de la récupération des statistiques');
      notify('Erreur lors de la récupération des statistiques', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Charger les statistiques au chargement de la page et lors du changement de période
  useEffect(() => {
    fetchStats();
  }, [timeRange]);
  
  // Préparer les données pour les graphiques
  const prepareStatusData = () => {
    if (!stats) return [];
    
    return Object.entries(stats.parStatut).map(([status, count]) => ({
      name: statusTranslations[status] || status,
      value: count,
      color: getStatusColor(status)
    }));
  };
  
  const prepareTypeData = () => {
    if (!stats) return [];
    
    return Object.entries(stats.parType).map(([type, count], index) => ({
      name: typeTranslations[type] || type,
      value: count,
      color: CHART_COLORS[index % CHART_COLORS.length]
    }));
  };
  
  const prepareEvolutionData = () => {
    if (!stats) return [];
    
    return stats.evolution.map(item => ({
      name: item.mois,
      montant: item.montant,
      nombre: item.nombre
    }));
  };
  
  const statusData = prepareStatusData();
  const typeData = prepareTypeData();
  const evolutionData = prepareEvolutionData();
  
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Helmet>
        <title>Statistiques de Facturation | JobPartiel Admin</title>
      </Helmet>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4, flexWrap: 'wrap' }}>
        <PageTitle>Statistiques de Facturation</PageTitle>
        
        <Box sx={{ display: 'flex', gap: 2, mt: { xs: 2, sm: 0 } }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="time-range-label">Période</InputLabel>
            <Select
              labelId="time-range-label"
              value={timeRange}
              label="Période"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="7">7 jours</MenuItem>
              <MenuItem value="30">30 jours</MenuItem>
              <MenuItem value="90">90 jours</MenuItem>
              <MenuItem value="365">1 an</MenuItem>
            </Select>
          </FormControl>
          
          <StyledButton
            variant="outlined"
            startIcon={<RefreshCw size={18} />}
            onClick={fetchStats}
            disabled={loading}
          >
            Actualiser
          </StyledButton>
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress size={60} sx={{ color: COLORS.primary }} />
        </Box>
      ) : stats ? (
        <>
          {/* KPI Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <MetricCard>
                <IconBox>
                  <FileText size={24} />
                </IconBox>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Total Documents
                </Typography>
                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {stats.total}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Devis, factures et avoirs
                </Typography>
              </MetricCard>
            </Grid>
            
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <MetricCard>
                <IconBox color={COLORS.success}>
                  <DollarSign size={24} />
                </IconBox>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Montant Total
                </Typography>
                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {formatCurrency(stats.montantTotal)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Chiffre d'affaires global
                </Typography>
              </MetricCard>
            </Grid>
            
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <MetricCard>
                <IconBox color={COLORS.info}>
                  <CheckCircle size={24} />
                </IconBox>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Factures Payées
                </Typography>
                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {stats.parStatut['paye'] || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {stats.parStatut['paye'] ? ((stats.parStatut['paye'] / stats.total) * 100).toFixed(1) : 0}% du total
                </Typography>
              </MetricCard>
            </Grid>
            
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <MetricCard>
                <IconBox color={COLORS.warning}>
                  <Clock size={24} />
                </IconBox>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  En Attente
                </Typography>
                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {(stats.parStatut['envoye'] || 0) + (stats.parStatut['partiellement_paye'] || 0)}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Factures non finalisées
                </Typography>
              </MetricCard>
            </Grid>
          </Grid>
          
          {/* Charts */}
          <Grid container spacing={3}>
            {/* Évolution mensuelle */}
            <Grid size={{ xs: 12, lg: 8 }}>
              <StyledPaper>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TrendingUp size={20} color={COLORS.primary} />
                  <SectionTitle sx={{ ml: 1 }}>Évolution Mensuelle</SectionTitle>
                </Box>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={evolutionData}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" />
                      <YAxis yAxisId="left" orientation="left" stroke={COLORS.primary} />
                      <YAxis yAxisId="right" orientation="right" stroke={COLORS.info} />
                      <RechartsTooltip
                        formatter={(value: any, name: string) => {
                          if (name === 'montant') return [formatCurrency(value), 'Montant'];
                          return [value, 'Nombre'];
                        }}
                      />
                      <Legend />
                      <Area
                        yAxisId="left"
                        type="monotone"
                        dataKey="montant"
                        name="Montant"
                        stroke={COLORS.primary}
                        fill={`${COLORS.primary}30`}
                        activeDot={{ r: 8 }}
                      />
                      <Area
                        yAxisId="right"
                        type="monotone"
                        dataKey="nombre"
                        name="Nombre"
                        stroke={COLORS.info}
                        fill={`${COLORS.info}30`}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </StyledPaper>
            </Grid>
            
            {/* Répartition par type */}
            <Grid size={{ xs: 12, sm: 6, lg: 4 }}>
              <StyledPaper>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PieChartIcon size={20} color={COLORS.primary} />
                  <SectionTitle sx={{ ml: 1 }}>Répartition par Type</SectionTitle>
                </Box>
                <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={typeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {typeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip formatter={(value: any) => [value, 'Documents']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </StyledPaper>
            </Grid>
            
            {/* Répartition par statut */}
            <Grid size={{ xs: 12, sm: 6, lg: 6 }}>
              <StyledPaper>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <BarChart2 size={20} color={COLORS.primary} />
                  <SectionTitle sx={{ ml: 1 }}>Répartition par Statut</SectionTitle>
                </Box>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={statusData}
                      margin={{ top: 20, right: 0, left: 0, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip formatter={(value: any) => [value, 'Documents']} />
                      <Bar dataKey="value" name="Nombre">
                        {statusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </StyledPaper>
            </Grid>
            
            {/* Statuts détaillés */}
            <Grid size={{ xs: 12, lg: 6 }}>
              <StyledPaper>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <FileCheck size={20} color={COLORS.primary} />
                  <SectionTitle sx={{ ml: 1 }}>Statuts Détaillés</SectionTitle>
                </Box>
                
                <Grid container spacing={2}>
                  {Object.entries(stats.parStatut).map(([status, count]) => (
                    <Grid size={{ xs: 6, sm: 4 }} key={status}>
                      <Box sx={{ 
                        p: 2, 
                        borderRadius: '8px', 
                        border: '1px solid #eee',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center'
                      }}>
                        <ChipStyled 
                          label={statusTranslations[status] || status} 
                          size="small" 
                          sx={{ 
                            backgroundColor: `${getStatusColor(status)}30`,
                            color: getStatusColor(status),
                            mb: 1
                          }} 
                        />
                        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                          {count}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {((count / stats.total) * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </StyledPaper>
            </Grid>
          </Grid>
        </>
      ) : (
        <Alert severity="info" sx={{ mb: 4 }}>
          Aucune donnée disponible. Veuillez vérifier votre abonnement ou réessayer plus tard.
        </Alert>
      )}
    </Container>
  );
};

export default BillingStats;