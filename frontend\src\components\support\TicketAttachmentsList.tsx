import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Link,
  Paper,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
  PictureAsPdf as PictureAsPdfIcon,
  InsertDriveFile as InsertDriveFileIcon,
  Download as DownloadIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { api } from '../../services/api';
import { logger } from '../../utils/logger';
import { useAuth } from '../../contexts/AuthContext';
import { notify } from '../Notification';
import supportTicketService from '../../services/supportTicketService';
import { useImageCompression } from '../../utils/imageCompressor';

interface Attachment {
  id: string;
  ticket_id: string;
  file_name: string;
  storage_path: string;
  file_type: string;
  file_size: number;
  created_at: string;
}

interface TicketAttachmentsListProps {
  ticketId: string;
}

const TicketAttachmentsList: React.FC<TicketAttachmentsListProps> = ({ ticketId }) => {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retentionInfo, setRetentionInfo] = useState({
    period_days: 60,
    auto_delete: true,
  });
  const { user } = useAuth();
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<Attachment | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const { compressGalleryPhoto } = useImageCompression();

  // Fonction pour nettoyer les noms de fichiers et corriger les caractères spéciaux
  const cleanFileName = (fileName: string): string => {
    try {
      // Décodage des séquences d'URL (comme %20 pour espace)
      const decodedName = decodeURIComponent(fileName);
      
      // Remplacement des caractères problématiques
      return decodedName
        .replace(/Ã©/g, 'é')
        .replace(/Ã¨/g, 'è')
        .replace(/Ã /g, 'à')
        .replace(/Ã¢/g, 'â')
        .replace(/Ãª/g, 'ê')
        .replace(/Ã®/g, 'î')
        .replace(/Ã´/g, 'ô')
        .replace(/Ã»/g, 'û')
        .replace(/Ã§/g, 'ç')
        .replace(/Ã«/g, 'ë')
        .replace(/Ã¯/g, 'ï')
        .replace(/Ã¼/g, 'ü')
        .replace(/Ã¹/g, 'ù')
        .replace(/Ã€/g, 'À')
        .replace(/Ã‰/g, 'É')
        .replace(/Ãˆ/g, 'È')
        .replace(/ÃŽ/g, 'Î')
        .replace(/Ã"/g, 'Ô')
        .replace(/Ã™/g, 'Ù')
        .replace(/Ã‡/g, 'Ç');
    } catch (e) {
      // En cas d'erreur, retourner le nom original
      return fileName;
    }
  };

  const fetchAttachments = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/support/${ticketId}/attachments`);
      
      if (response.data && response.data.success) {
        setAttachments(response.data.attachments || []);
        
        // Récupérer les informations de conservation
        if (response.data.retention_info) {
          setRetentionInfo(response.data.retention_info);
        }
      } else {
        setError('Impossible de charger les pièces jointes');
      }
    } catch (err) {
      logger.error('Erreur lors de la récupération des pièces jointes:', err);
      setError('Une erreur est survenue lors du chargement des pièces jointes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (ticketId) {
      fetchAttachments();
    }
  }, [ticketId]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return dateString;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' octets';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' Ko';
    return (bytes / (1024 * 1024)).toFixed(1) + ' Mo';
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon sx={{ color: '#FF6B2C' }} />;
    } else if (fileType === 'application/pdf') {
      return <PictureAsPdfIcon sx={{ color: '#FF6B2C' }} />;
    } else {
      return <InsertDriveFileIcon sx={{ color: '#FF6B2C' }} />;
    }
  };

  // Calculer la date d'expiration des pièces jointes
  const calculateExpirationDate = (creationDate: string) => {
    const creation = new Date(creationDate);
    const expirationDate = new Date(creation);
    expirationDate.setDate(expirationDate.getDate() + retentionInfo.period_days);
    return formatDate(expirationDate.toISOString());
  };

  // Fonction pour gérer l'upload de fichiers
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;
    
    try {
      setUploadLoading(true);
      const files = Array.from(event.target.files);
      
      // Upload des fichiers un par un
      for (const file of files) {
        let fileToUpload = file;
        if (file.type.startsWith('image/')) {
          try {
            fileToUpload = await compressGalleryPhoto(file);
          } catch (err) {
            // Si la compression échoue, on garde le fichier original
          }
        }
        await supportTicketService.uploadAttachment(ticketId, fileToUpload);
      }
      
      // Rafraîchir la liste des pièces jointes
      await fetchAttachments();
      if (files.length > 1) {
        notify(`${files.length} fichiers ont été ajoutés avec succès`);
      } else {
        notify('Le fichier a été ajouté avec succès');
      }
    } catch (error) {
      logger.error('Erreur lors de l\'upload des fichiers:', error);
      notify('Une erreur est survenue lors de l\'upload des fichiers', error as string);
    } finally {
      setUploadLoading(false);
      // Réinitialiser l'input file pour permettre de sélectionner à nouveau les mêmes fichiers
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Fonction pour supprimer une pièce jointe
  const handleDeleteClick = (attachment: Attachment) => {
    setSelectedAttachment(attachment);
    setOpenDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!selectedAttachment) return;
    
    try {
      setDeleteLoading(true);
      await api.delete(`/api/support/${ticketId}/attachments/${selectedAttachment.id}`);
      
      // Rafraîchir la liste des pièces jointes
      await fetchAttachments();
      notify('La pièce jointe a été supprimée avec succès');
    } catch (error) {
      logger.error('Erreur lors de la suppression de la pièce jointe:', error);
      notify('Une erreur est survenue lors de la suppression de la pièce jointe', error as string);
    } finally {
      setDeleteLoading(false);
      setOpenDeleteDialog(false);
      setSelectedAttachment(null);
    }
  };

  const cancelDelete = () => {
    setOpenDeleteDialog(false);
    setSelectedAttachment(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
        <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <>
      <Paper
        elevation={2}
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          mb: 3
        }}
      >
        <Box
          sx={{
            px: 3,
            py: 2,
            bgcolor: 'rgba(255, 107, 44, 0.05)',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: '1.1rem',
              color: '#FF6B2C',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <AttachFileIcon sx={{ mr: 1 }} />
            Pièces jointes ({attachments.length})
          </Typography>
          
          {isStaff && (
            <Button
              variant="text"
              startIcon={<AddIcon />}
              size="small"
              onClick={() => fileInputRef.current?.click()}
              sx={{
                color: '#FF6B2C',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.05)',
                }
              }}
              disabled={uploadLoading}
            >
              {uploadLoading ? 'Chargement...' : 'Ajouter'}
            </Button>
          )}
        </Box>

        {/* Message d'information sur la politique de suppression */}
        <Box 
          sx={{ 
            px: 3,
            py: 2, 
            bgcolor: 'rgba(255, 247, 240, 0.7)',
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
            display: 'flex',
            alignItems: 'flex-start'
          }}
        >
          <InfoIcon sx={{ color: '#FF6B2C', mr: 1.5, mt: 0.3, fontSize: '1.2rem' }} />
          <Box>
            <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.875rem' }}>
              Les pièces jointes sont conservées pendant <strong>{retentionInfo.period_days} jours</strong> et seront
              automatiquement supprimées {retentionInfo.auto_delete && 'à la fermeture/résolution du ticket ou '}
              à la fin de cette période pour des raisons environnementales et d'optimisation de l'espace de stockage.
            </Typography>
          </Box>
        </Box>

        {attachments.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="body1" color="text.secondary">
              Aucune pièce jointe pour ce ticket
            </Typography>
          </Box>
        ) : (
          <List sx={{ width: '100%', py: 0 }}>
            {attachments.map((attachment, index) => (
              <React.Fragment key={attachment.id}>
                <ListItem
                  component="div"
                  alignItems="flex-start"
                  sx={{
                    py: 1.5,
                    px: 3,
                    '&:hover': {
                      bgcolor: 'rgba(255, 107, 44, 0.04)'
                    },
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    gap: 1
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      width: '100%',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {getFileIcon(attachment.file_type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Link
                            href={attachment.storage_path}
                            target="_blank"
                            rel="noopener noreferrer"
                            sx={{
                              color: '#FF6B2C',
                              textDecoration: 'none',
                              '&:hover': {
                                textDecoration: 'underline'
                              },
                              fontWeight: 500,
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5
                            }}
                          >
                            {cleanFileName(attachment.file_name)}
                            <DownloadIcon sx={{ fontSize: '0.9rem', ml: 0.5 }} />
                          </Link>
                        }
                        secondary={
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              flexWrap: 'wrap',
                              gap: 1,
                              mt: 0.5
                            }}
                          >
                            <Chip
                              label={formatFileSize(attachment.file_size)}
                              size="small"
                              variant="outlined"
                              sx={{ borderRadius: '4px', height: 24 }}
                            />
                            <Typography variant="body2" color="text.secondary">
                              Ajouté le {formatDate(attachment.created_at)}
                            </Typography>
                            <Chip
                              label={`Expire le ${calculateExpirationDate(attachment.created_at)}`}
                              size="small"
                              sx={{ 
                                borderRadius: '4px',
                                height: 24, 
                                bgcolor: 'rgba(255, 107, 44, 0.1)',
                                color: 'text.secondary',
                                fontStyle: 'italic',
                                fontSize: '0.75rem'
                              }}
                            />
                          </Box>
                        }
                      />
                    </Box>
                    
                    {isStaff && (
                      <Tooltip title="Supprimer">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteClick(attachment)}
                          sx={{
                            color: 'rgba(220, 0, 0, 0.7)',
                            '&:hover': {
                              color: 'rgba(220, 0, 0, 0.9)',
                              backgroundColor: 'rgba(220, 0, 0, 0.1)'
                            }
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </ListItem>
                {index < attachments.length - 1 && (
                  <Divider variant="inset" component="li" sx={{ ml: 9 }} />
                )}
              </React.Fragment>
            ))}
          </List>
        )}
      </Paper>
      
      {/* Input file caché pour l'upload */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
        multiple
        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt"
      />

      {/* Boîte de dialogue de confirmation de suppression */}
      <Dialog
        open={openDeleteDialog}
        onClose={cancelDelete}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Confirmer la suppression
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Êtes-vous sûr de vouloir supprimer cette pièce jointe ?
            <br />
            <strong>{selectedAttachment?.file_name}</strong>
            <br />
            Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={cancelDelete} color="primary">
            Annuler
          </Button>
          <Button 
            onClick={confirmDelete} 
            color="error"
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={16} /> : <DeleteIcon />}
          >
            {deleteLoading ? 'Suppression...' : 'Supprimer'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TicketAttachmentsList; 