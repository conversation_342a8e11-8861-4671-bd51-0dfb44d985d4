import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSeoPromotionContext } from './SeoPromotionProvider';
import { useAuth } from '../../contexts/AuthContext';

interface SeoPromotionBannerProps {
  className?: string;
}

const SeoPromotionBanner: React.FC<SeoPromotionBannerProps> = ({ className = '' }) => {
  const { isAuthenticated, user } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  const { forceShowPromotion, userStats } = useSeoPromotionContext();

  // Fonction pour vérifier si l'utilisateur est inscrit depuis au moins 2 jours
  const isUserRegisteredForAtLeast2Days = () => {
    if (!user?.date_inscription) {
      // logger.info ('🔍 SEO Banner: date_inscription non disponible', { user: user?.id, date_inscription: user?.date_inscription });
      return false;
    }

    const registrationDate = new Date(user.date_inscription);
    const now = new Date();
    const daysDifference = (now.getTime() - registrationDate.getTime()) / (1000 * 60 * 60 * 24);

    // logger.info('🔍 SEO Banner: Vérification ancienneté', {
    //   userId: user.id,
    //   date_inscription: user.date_inscription,
    //   daysDifference: daysDifference.toFixed(2),
    //   isEligible: daysDifference >= 2
    // });

    return daysDifference >= 2;
  };

  useEffect(() => {
    // Ne pas afficher la bannière si l'utilisateur n'est pas connecté
    if (!isAuthenticated) {
      return;
    }

    // Ne pas afficher la bannière si l'utilisateur n'est pas inscrit depuis au moins 2 jours
    if (!isUserRegisteredForAtLeast2Days()) {
      return;
    }

    // Vérifier si on doit afficher la bannière
    if (userStats && !userStats.seoIndexable && !isDismissed) {
      // Afficher la bannière après un délai
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 3000); // 3 secondes après le chargement

      return () => clearTimeout(timer);
    }
  }, [userStats, isDismissed, isAuthenticated, user]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    // Sauvegarder dans localStorage pour ne pas re-afficher pendant 24h
    localStorage.setItem('seoBannerDismissed', new Date().toISOString());
  };

  const handleOpenModal = () => {
    setIsVisible(false);
    forceShowPromotion('notification');
  };

  // Vérifier si la bannière a été fermée récemment
  useEffect(() => {
    const dismissed = localStorage.getItem('seoBannerDismissed');
    if (dismissed) {
      const dismissedDate = new Date(dismissed);
      const now = new Date();
      const hoursSinceDismissed = (now.getTime() - dismissedDate.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceDismissed < 24) {
        setIsDismissed(true);
      }
    }
  }, []);

  // Ne pas afficher si l'utilisateur n'est pas connecté ou si les conditions ne sont pas remplies
  if (!isAuthenticated || !userStats || userStats.seoIndexable || isDismissed) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className={`fixed bottom-4 right-4 bg-gradient-to-r from-[#FF6B2C] to-[#FF7A35] text-white p-4 rounded-lg shadow-lg max-w-sm z-40 ${className}`}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-sm mb-1">
                📈 Boostez votre visibilité !
              </h3>
              <p className="text-xs opacity-90 mb-3">
                Activez le référencement Google pour recevoir 3x plus de demandes de missions.
              </p>
              <div className="flex space-x-2">
                <button
                  onClick={handleOpenModal}
                  className="bg-white text-[#FF6B2C] px-3 py-1 rounded text-xs font-medium hover:bg-gray-100 transition-colors"
                >
                  En savoir plus
                </button>
                <button
                  onClick={handleDismiss}
                  className="text-white opacity-75 hover:opacity-100 text-xs underline"
                >
                  Plus tard
                </button>
              </div>
            </div>
            <button
              onClick={handleDismiss}
              className="flex-shrink-0 text-white opacity-75 hover:opacity-100 transition-opacity"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SeoPromotionBanner;
