import { Router } from 'express';
import { reviewsController } from '../controllers/reviews';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import { body } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation';
import { asyncHand<PERSON> } from '../utils/inputValidation';
import { checkProfileVisibility } from '../middleware/checkProfileVisibility';

const router = Router();

// Rate limiter pour les avis
const reviewsLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requêtes maximum
  message: {
    message: 'Trop de requêtes pour les avis. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Validation pour la création/modification d'avis
const reviewValidation = [
  body('mission_id').isUUID().withMessage('ID mission invalide'),
  body('note').isInt({ min: 1, max: 5 }).withMessage('La note doit être entre 1 et 5'),
  body('commentaire').optional().isString().trim().escape(),
  body('qualites').isArray({ max: 5 }).withMessage('Maximum 5 qualités d\'avis'),
  body('qualites.*').isString().trim(),
  body('defauts').optional().isArray({ max: 5 }).withMessage('Maximum 5 points à améliorer'),
  body('defauts.*').optional().isString().trim()
];

// Validation pour la réponse à un avis
const replyValidation = [
  body('reponse').isString().trim().notEmpty().withMessage('La réponse ne peut pas être vide')
];

// Routes
router.post('/',
  authMiddleware.authenticateToken,
  checkProfileVisibility,
  reviewsLimiter,
  reviewValidation,
  handleValidationErrors,
  reviewsController.createReview
);

router.put('/:id',
  authMiddleware.authenticateToken,
  checkProfileVisibility,
  reviewsLimiter,
  reviewValidation,
  handleValidationErrors,
  asyncHandler(reviewsController.updateReview)
);

router.delete('/:id',
  authMiddleware.authenticateToken,
  checkProfileVisibility,
  reviewsLimiter,
  asyncHandler(reviewsController.deleteReview)
);

router.post('/:id/reply',
  authMiddleware.authenticateToken,
  checkProfileVisibility,
  reviewsLimiter,
  replyValidation,
  handleValidationErrors,
  asyncHandler(reviewsController.replyToReview)
);

// Route pour modifier une réponse
router.put('/:id/reply',
  authMiddleware.authenticateToken,
  checkProfileVisibility,
  reviewsLimiter,
  replyValidation,
  handleValidationErrors,
  asyncHandler(reviewsController.updateReplyToReview)
);

// Route pour récupérer un avis spécifique
router.get('/:id',
  authMiddleware.authenticateToken,
  reviewsLimiter,
  asyncHandler(reviewsController.getReviewById)
);

// Route pour récupérer les avis d'un utilisateur
router.get('/user/:userId',
  reviewsLimiter,
  asyncHandler(reviewsController.getUserReviews)
);

// Route pour récupérer les avis envoyés par un utilisateur
router.get('/user/:userId/sent',
  reviewsLimiter,
  asyncHandler(reviewsController.getSentReviews)
);

router.get('/user/:userId/reviews', asyncHandler(reviewsController.getUserReviews));
router.get('/user/:userId/qualites-stats', asyncHandler(reviewsController.getQualitesStats));

// Route pour vérifier si un avis existe pour une mission
router.get('/check/:missionId',
  authMiddleware.authenticateToken,
  reviewsLimiter,
  asyncHandler(reviewsController.checkReviewExists)
);

// Route pour uploader une photo d'avis
router.post('/:id/photo',
  authMiddleware.authenticateToken,
  reviewsLimiter,
  asyncHandler(reviewsController.uploadReviewPhoto)
);

// Route pour supprimer une photo d'avis
router.delete('/:id/photo',
  authMiddleware.authenticateToken,
  reviewsLimiter,
  asyncHandler(reviewsController.deleteReviewPhoto)
);

export default router;