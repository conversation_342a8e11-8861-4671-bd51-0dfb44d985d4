import React from 'react';
import { motion } from 'framer-motion';

interface SubscriptionLimitsSectionProps {
  profil: any;
  interventionRadius: number;
  servicesLimit: number;
  galleriesLimit: number;
  franceEntiere: boolean;
}

const SubscriptionLimitsSection: React.FC<SubscriptionLimitsSectionProps> = ({
  profil,
  interventionRadius,
  servicesLimit,
  galleriesLimit,
  franceEntiere
}) => {
  return (
    <motion.section
      className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-briefcase h-6 w-6 text-[#FF6B2C]"><path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path><rect width="20" height="14" x="2" y="6" rx="2"></rect></svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-800">Vos limites d'abonnement</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Type d'abonnement */}
        <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user h-6 w-6 text-[#FF6B2C]"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
          <div>
            <div className="text-sm text-gray-600 font-medium">Type d'abonnement</div>
            <div className="mt-1 inline-block px-2 py-1 rounded text-white text-sm font-semibold" style={{ background: profil.isPremium ? '#FF6B2C' : '#A0AEC0' }}>
              {profil.isPremium ? 'Premium' : 'Gratuit'}
            </div>
          </div>
        </div>
        {/* Rayon d'intervention maximum */}
        <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin h-6 w-6 text-[#FF6B2C]"><path d="M12 21s8-4.5 8-10A8 8 0 0 0 4 11c0 5.5 8 10 8 10z"></path><circle cx="12" cy="11" r="3"></circle></svg>
          <div>
            <div className="text-sm text-gray-600 font-medium">Rayon d'intervention maximum</div>
            <div className="mt-1 text-gray-800 font-semibold">{interventionRadius >= 9999 ? 'Illimité' : `${interventionRadius} km`}</div>
          </div>
        </div>
        {/* Nombre de services inclus */}
        <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-briefcase h-6 w-6 text-[#FF6B2C]"><rect width="20" height="14" x="2" y="7" rx="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>
          <div>
            <div className="text-sm text-gray-600 font-medium">Services inclus</div>
            <div className="mt-1 text-gray-800 font-semibold">{servicesLimit}</div>
          </div>
        </div>
        {/* Nombre de galeries incluses */}
        <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-image h-6 w-6 text-[#FF6B2C]"><rect width="18" height="14" x="3" y="5" rx="2"></rect><circle cx="8.5" cy="10.5" r="1.5"></circle><path d="M21 15l-5-5L5 21"></path></svg>
          <div>
            <div className="text-sm text-gray-600 font-medium">Galeries incluses</div>
            <div className="mt-1 text-gray-800 font-semibold">{galleriesLimit}</div>
          </div>
        </div>
        {/* France entière */}
        <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-globe h-6 w-6 text-[#FF6B2C]"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10A15.3 15.3 0 0 1 12 2z"></path></svg>
          <div>
            <div className="text-sm text-gray-600 font-medium">Option France entière</div>
            <div className="mt-1">{franceEntiere ? (<span className="inline-block px-2 py-1 rounded text-white text-sm font-semibold bg-green-500">Disponible</span>) : (<span className="inline-block px-2 py-1 rounded text-white text-sm font-semibold bg-gray-400">Non disponible</span>)}</div>
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default SubscriptionLimitsSection; 