import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom'; // Ajout de useLocation
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../services/api';
import { notify } from '../../../components/Notification';
import ModalPortal from '../../../components/ModalPortal';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  CircularProgress,
  List,
  ListItemAvatar,
  Avatar,
  Button,
  Paper,
  styled,
  LinearProgress,
  Tooltip,
  Alert,
  Divider,
  Chip
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import SendIcon from '@mui/icons-material/Send';
import InfoIcon from '@mui/icons-material/Info';
import PersonSearchIcon from '@mui/icons-material/PersonSearch';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import PersonIcon from '@mui/icons-material/Person';
import StarIcon from '@mui/icons-material/Star';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import { motion } from 'framer-motion';
import logger from '@/utils/logger';
import { useSubscription } from '@/hooks/useSubscription';
import { fetchCsrfToken } from '@/services/csrf';

// Types
interface NewConversationDialogProps {
  open: boolean;
  onClose: () => void;
}

interface User {
  id: string;
  usertag?: string;
  avatar_url?: string;
  nom?: string;
  prenom?: string;
  type?: 'favori' | 'offre_recue' | 'offre_envoyee' | 'jobi_echange';
  dernierEchange?: {
    montant: number;
    date: string;
    type: 'envoi' | 'reception';
  };
}

interface JobiHistorique {
  id: string;
  montant: number;
  solde_apres: number;
  date_creation: string;
  description: string;
}

// Composants stylisés
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  position: 'relative',
  width: '95%',
  maxWidth: 850,
  maxHeight: '90vh',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px',
  boxShadow: '0 10px 30px rgba(0,0,0,0.12)',
  background: theme.palette.background.paper,
  margin: 'auto'
}));

const ContentBox = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  paddingBottom: theme.spacing(2)
}));

const ButtonBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: theme.spacing(3),
  paddingTop: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  position: 'sticky',
  bottom: 0,
  zIndex: 1
}));

const UserAvatar = styled(Avatar)({
  backgroundColor: '#FF6B2C',
  width: 40,
  height: 40
});

const MessageField = styled(TextField)(({ theme }) => ({
  marginTop: theme.spacing(2),
  '& .MuiOutlinedInput-root': {
    borderRadius: 8,
    transition: 'all 0.3s',
    '&:hover': {
      borderColor: '#FF6B2C',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: '#FF6B2C',
      borderWidth: 2
    },
  }
}));

const SendButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(2),
  backgroundColor: '#FF6B2C',
  padding: '8px 24px',
  borderRadius: '8px',
  fontWeight: 600,
  '&:hover': {
    backgroundColor: '#FF7A35',
  },
  '&.Mui-disabled': {
    backgroundColor: 'rgba(255, 107, 44, 0.3)',
    color: '#fff',
  }
}));

const CancelButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(2),
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  }
}));

const ProgressContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

const StyledProgress = styled(LinearProgress)<{ islimit: string }>(({ theme, islimit }) => ({
  flexGrow: 1,
  height: 10,
  borderRadius: 5,
  backgroundColor: theme.palette.grey[200],
  '& .MuiLinearProgress-bar': {
    backgroundColor: islimit === 'true' ? '#FF3B30' : '#FF6B2C',
    transition: 'transform 1s ease-in-out'
  },
  marginRight: theme.spacing(1),
}));

const SearchTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 8,
    transition: 'all 0.3s',
    backgroundColor: theme.palette.grey[50],
    '&:hover': {
      backgroundColor: theme.palette.grey[100],
    },
    '&.Mui-focused': {
      backgroundColor: theme.palette.grey[50],
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: '#FF6B2C',
        borderWidth: 2
      }
    }
  }
}));

const LimitChip = styled(Chip)(() => ({
  backgroundColor: '#FF3B30',
  color: '#FFFFFF',
  fontWeight: 'bold',
  '& .MuiChip-icon': {
    color: '#FFFFFF'
  }
}));

// Nouvelle fonction pour identifier les utilisateurs avec qui on a échangé des Jobi
const identifyJobiExchangeUsers = (historique: JobiHistorique[]): User[] => {
  const jobiUsers: User[] = [];
  
  // Parcourir l'historique pour identifier les transferts et réceptions
  historique.forEach(transaction => {
    // Analyse différents formats possibles de description
    let userTag: string | null = null;
    let montant = Math.abs(transaction.montant);
    let type: 'envoi' | 'reception' = transaction.montant < 0 ? 'envoi' : 'reception';
    
    // Regex pour différents formats de description
    const transferRegex = /Transfert de (\d+) Jobi à @([^\s)]+)/;
    const receptionRegex = /Réception de (\d+) Jobi de @([^\s)]+)/;
    const altTransferRegex = /Envoi de (\d+) Jobi à @([^\s)]+)/;
    const altReceptionRegex = /Reçu (\d+) Jobi de @([^\s)]+)/;
    
    const description = transaction.description || '';
    
    // Tester les différents formats
    let match = description.match(transferRegex);
    if (match) {
      userTag = match[2];
      montant = parseInt(match[1], 10);
      type = 'envoi';
    } else {
      match = description.match(receptionRegex);
      if (match) {
        userTag = match[2];
        montant = parseInt(match[1], 10);
        type = 'reception';
      } else {
        match = description.match(altTransferRegex);
        if (match) {
          userTag = match[2];
          montant = parseInt(match[1], 10);
          type = 'envoi';
        } else {
          match = description.match(altReceptionRegex);
          if (match) {
            userTag = match[2];
            montant = parseInt(match[1], 10);
            type = 'reception';
          } else {
            // Fallback pour tout autre format (notamment @usertag)
            const tagMatch = description.match(/@([^\s)]+)/);
            if (tagMatch) {
              userTag = tagMatch[1];
              // Type par défaut basé sur le signe du montant
              type = transaction.montant < 0 ? 'envoi' : 'reception';
            }
          }
        }
      }
    }
    
    if (userTag) {
      // Vérifier si l'utilisateur existe déjà dans notre liste
      const existingUserIndex = jobiUsers.findIndex(u => u.usertag === userTag);
      
      if (existingUserIndex >= 0) {
        // Si la transaction est plus récente, mettre à jour
        const existingDate = new Date(jobiUsers[existingUserIndex].dernierEchange?.date || '');
        const newDate = new Date(transaction.date_creation);
        
        if (newDate > existingDate) {
          jobiUsers[existingUserIndex].dernierEchange = {
            montant,
            date: transaction.date_creation,
            type
          };
        }
      } else {
        // Ajouter un nouvel utilisateur
        jobiUsers.push({
          id: '', // Sera mis à jour lors de la fusion avec les données utilisateur
          usertag: userTag,
          avatar_url: undefined, // Utiliser undefined au lieu de null
          type: 'jobi_echange',
          dernierEchange: {
            montant,
            date: transaction.date_creation,
            type
          }
        });
      }
    }
  });
  
  logger.info('Utilisateurs avec échanges Jobi identifiés:', jobiUsers);
  return jobiUsers;
};

const NewConversationDialog: React.FC<NewConversationDialogProps> = ({ open, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [message, setMessage] = useState('');
  const [currentUserId, setCurrentUserId] = useState<string>('');
  const [isLoadingKnownUsers, setIsLoadingKnownUsers] = useState(false);
  const [showSearchAndList, setShowSearchAndList] = useState(true);
  
  // Ref pour le délai de recherche
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getOptionPremiumUtilisateur } = useSubscription();

  // Récupérer l'ID de l'utilisateur actuel lorsque le modal s'ouvre
  useEffect(() => {
    let isMounted = true;
    
    if (open) {

      const init = async () => {
        try {
          if (isMounted) {
            // Charger les utilisateurs connus une fois l'ID utilisateur récupéré
            await loadKnownUsers();
          }
        } catch (error) {
          logger.info('Erreur d\'initialisation:', error);
        }
      };
      
      init();
    }
    
    // Nettoyage pour éviter les mises à jour sur un composant démonté
    return () => {
      isMounted = false;
    };
  }, [open]); // Uniquement quand le modal est ouvert/fermé

  // Effet pour charger les informations pré-remplies du localStorage
  useEffect(() => {
    if (open) {
      const newMessageInfo = localStorage.getItem('newMessageInfo');
      if (newMessageInfo) {
        const { recipientId, recipientName, initialMessage } = JSON.parse(newMessageInfo);

        // Rechercher l'utilisateur spécifique
        const searchSpecificUser = async () => {
          try {
            logger.info('Recherche de l\'utilisateur spécifique:', { recipientId, recipientName });

            // Faire une recherche directe par ID utilisateur
            const response = await api.get('/api/users/search', {
              params: { query: recipientId, searchById: true }
            });

            if (response.data?.users?.[0]) {
              const user = response.data.users[0];
              logger.info('Utilisateur trouvé:', user);
              setSelectedUser(user);
              setMessage(initialMessage || `Bonjour ${user.prenom || ''},\n\nJe souhaite vous contacter pour discuter d'une mission.\n\nNote : Le spam et les messages non sollicités sont interdits sur JobPartiel. Merci de respecter nos conditions d'utilisation sous peine de bannissement.`);
              setShowSearchAndList(false); // Masquer la recherche et la liste

              // Mettre à jour le cache avec cet utilisateur pour qu'il apparaisse dans les résultats
              queryClient.setQueryData(['users', 'search', 'recent'], {
                users: [user],
                success: true
              });
            } else {
              // Si l'utilisateur n'est pas trouvé par ID, essayer par nom
              const nameResponse = await api.get('/api/users/search', {
                params: { query: recipientName || recipientId }
              });

              if (nameResponse.data?.users?.length > 0) {
                const user = nameResponse.data.users[0];
                logger.info('Utilisateur trouvé par nom:', user);
                setSelectedUser(user);
                setMessage(initialMessage || `Bonjour ${user.prenom || ''},\n\nJe souhaite vous contacter pour discuter d'une mission.\n\nNote : Le spam et les messages non sollicités sont interdits sur JobPartiel. Merci de respecter nos conditions d'utilisation sous peine de bannissement.`);
                setShowSearchAndList(false);

                // Mettre à jour le cache
                queryClient.setQueryData(['users', 'search', 'recent'], {
                  users: [user],
                  success: true
                });
              } else {
                logger.info('Utilisateur non trouvé, affichage de la liste normale');
                setShowSearchAndList(true);
                await loadKnownUsers();
              }
            }
          } catch (error) {
            logger.info('Erreur lors de la recherche de l\'utilisateur spécifique:', error);
            setShowSearchAndList(true);
            await loadKnownUsers();
          }
        };

        searchSpecificUser();
        localStorage.removeItem('newMessageInfo');
      } else {
        // Si pas d'info dans le localStorage, afficher la recherche et la liste
        setShowSearchAndList(true);
        loadKnownUsers();
      }
    }
  }, [open]);

  // Calculer la clé de requête utilisée pour la recherche avec délai
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  
  // Effet pour gérer le délai de recherche
  useEffect(() => {
    if (searchQuery.length === 0) {
      setDebouncedSearchQuery('');
      return;
    }
    
    if (searchQuery.length >= 2) {
      const timeoutId = setTimeout(() => {
        setDebouncedSearchQuery(searchQuery);
      }, 1000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery]);
  
  const queryParam = useMemo(() => {
    return (!debouncedSearchQuery || debouncedSearchQuery.length < 2) ? 'recent' : debouncedSearchQuery;
  }, [debouncedSearchQuery]);

  // Chargement des utilisateurs connus (récents, favoris, échanges)
  const loadKnownUsers = async () => {
    try {
      setIsLoadingKnownUsers(true);
      
      // Utiliser l'endpoint dédié aux utilisateurs connus comme dans JobiExchangeModal
      const response = await api.get('/api/users/recup_users_connus_jobi');
      
      if (response.data.success && response.data.users) {
        
        // Filtrer l'utilisateur courant
        const filteredUsers = response.data.users.filter((user: User) => 
          user.id !== currentUserId
        );
        
        logger.info('Utilisateurs filtrés (sans utilisateur courant):', filteredUsers.length);
        
        // Obtenir l'historique Jobi pour enrichir les données
        try {
          const historiqueResponse = await api.get('/api/jobi/historique');
          
          if (historiqueResponse.data.success && historiqueResponse.data.historique) {
            const historique = historiqueResponse.data.historique;
            logger.info('Historique Jobi récupéré:', historique.length, 'transactions');
            
            // Identifier les utilisateurs avec qui on a échangé des Jobi
            const jobiUsers = identifyJobiExchangeUsers(historique);
            logger.info('Utilisateurs Jobi identifiés:', jobiUsers.length);
            
            // Enrichir les données utilisateurs avec les informations d'échange Jobi
            filteredUsers.forEach((user: User) => {
              const jobiExchangeInfo = jobiUsers.find(u => u.usertag === user.usertag);
              if (jobiExchangeInfo) {
                user.type = 'jobi_echange';
                user.dernierEchange = jobiExchangeInfo.dernierEchange;
              }
            });
          }
        } catch (histoError) {
          logger.info('Erreur lors du chargement de l\'historique Jobi:', histoError);
        }
        
        // Trier les utilisateurs : d'abord les échanges Jobi, puis les autres
        const sortedUsers = [...filteredUsers].sort((a: User, b: User) => {
          if (a.type === 'jobi_echange' && b.type !== 'jobi_echange') return -1;
          if (a.type !== 'jobi_echange' && b.type === 'jobi_echange') return 1;
          return 0;
        });
        
        logger.info('Utilisateurs triés prêts à être affichés:', sortedUsers.length);
        
        // Mise à jour du state avec les utilisateurs filtrés et enrichis
        queryClient.setQueryData(['users', 'search', queryParam], { 
          users: sortedUsers,
          success: true
        });
      }
    } catch (error) {
      logger.info('Erreur lors du chargement des utilisateurs connus:', error);
      
      // En cas d'erreur, essayer avec l'API de recherche standard
      try {
        const fallbackResponse = await api.get('/api/users/search', {
          params: { query: queryParam }
        });
        
        if (fallbackResponse.data.success && fallbackResponse.data.users) {
          const filteredUsers = fallbackResponse.data.users.filter((user: User) => 
            user.id !== currentUserId
          );
          
          queryClient.setQueryData(['users', 'search', queryParam], { 
            users: filteredUsers,
            success: true
          });
        }
      } catch (fallbackError) {
        logger.info('Erreur lors de la recherche de secours:', fallbackError);
        queryClient.setQueryData(['users', 'search', queryParam], { 
          users: [],
          success: true
        });
      }
    } finally {
      setIsLoadingKnownUsers(false);
    }
  };

  // Recherche d'utilisateurs
  const {
    data: searchResults,
    isLoading: isUsersLoading,
    error: usersError
  } = useQuery({
    queryKey: ['users', 'search', queryParam],
    queryFn: async () => {
      try {
        logger.info('Exécution de la requête de recherche avec:', queryParam);
        
        const response = await api.get('/api/users/search', {
          params: { query: queryParam }
        });
        
        if (response.data && response.data.success) {
          // Filtrer l'utilisateur courant des résultats
          const filteredUsers = response.data.users.filter((user: User) => 
            user.id !== currentUserId
          );
          
          logger.info('Résultats de recherche (après filtrage):', filteredUsers.length);
          
          return { success: true, users: filteredUsers };
        }
        
        return { success: true, users: [] };
      } catch (error) {
        logger.info("Erreur lors de la recherche d'utilisateurs:", error);
        return { success: true, users: [] };
      }
    },
    // Toujours actif, mais avec des paramètres adaptés en fonction de la longueur de la chaîne
    enabled: open,
  });

  // Mutation pour créer une conversation
  const createConversationMutation = useMutation({
    mutationFn: async (data: { recipient_id: string; initial_message: string }) => {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      return api.post('/api/messages', data, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
    },
    onSuccess: (response) => {
      // Extraire l'ID de la conversation créée
      const conversationId = response.data.data?.id;
      onClose();
      
      // Invalider le cache des conversations pour forcer le rafraîchissement
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['conversationCount'] });
      
      // Rediriger vers la nouvelle conversation avec le préfixe /dashboard
      if (conversationId) {
        setTimeout(() => {
          navigate(`/dashboard/messages/${conversationId}`);
        }, 1000);
        notify('Conversation créée avec succès', 'success');
      } else {
        logger.info('ID de conversation manquant dans la réponse:', response.data);
        notify('Conversation créée mais impossible de naviguer', 'warning');
      }
    },
    onError: (error: any) => {
      logger.info('Erreur lors de la création de la conversation:', error);
      notify(error?.response?.data?.error || 'Erreur lors de la création de la conversation', 'error');
    }
  });

  // Récupérer le nombre de conversations
  const { data: conversationCount, isLoading: isCountLoading } = useQuery({
    queryKey: ['conversationCount'],
    queryFn: async () => {
      const response = await api.get('/api/messages/count-conversations');
      return response.data.data;
    },
    enabled: open
  });

  // Limite réelle selon l'abonnement
  const conversationLimit = getOptionPremiumUtilisateur('conversations_messages_prives') || 1;
  const currentCount = conversationCount?.monthly_count || 0;
  const isLimitReached = currentCount >= conversationLimit;
  const monthlyPercentage = (currentCount / conversationLimit) * 100;

  // Gérer la recherche avec délai de 1 seconde
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setSearchQuery(newQuery);
    setSelectedUser(null);
    
    // Annuler le timeout précédent s'il existe
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Si la recherche est vidée, charger immédiatement les utilisateurs connus
    if (newQuery.length === 0) {
      loadKnownUsers();
      return;
    }
    
    // Ajouter un délai de 1 seconde avant de déclencher la recherche
    searchTimeoutRef.current = setTimeout(() => {
      // La recherche sera déclenchée automatiquement par useQuery
      // grâce au changement de queryParam
      logger.info('Recherche déclenchée après délai pour:', newQuery);
    }, 1000);
  };

  // Effacer la recherche
  const handleClearSearch = () => {
    // Annuler le timeout de recherche s'il existe
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }
    
    setSearchQuery('');
    setSelectedUser(null);
    setMessage('');
    setShowSearchAndList(true);
  };

  // Gérer la fermeture de la modal
  const handleClose = () => {
    // Nettoyer le timeout lors de la fermeture
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }
    
    handleClearSearch();
    onClose();
  };

  // Sélectionner un utilisateur
  const handleSelectUser = (user: User) => {
    setSelectedUser(user);
  };

  // Créer la conversation
  const handleCreateConversation = () => {
    if (!selectedUser) {
      notify('Veuillez sélectionner un destinataire', 'warning');
      return;
    }

    if (!message.trim()) {
      notify('Veuillez entrer un message', 'warning');
      return;
    }

    const conversationData = {
      recipient_id: selectedUser.id,
      initial_message: message.trim()
    };

    logger.info('Envoi des données pour créer la conversation:', conversationData);

    createConversationMutation.mutate(conversationData);
  };

  const users = searchResults?.users || [];

  // Grouper les utilisateurs par type
  const groupedUsers = users.reduce((acc: Record<string, User[]>, user: User) => {
    const type = user.type || 'favori';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(user);
    return acc;
  }, {} as Record<string, User[]>);

  // Obtenir l'icône selon le type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'favori':
        return <StarIcon fontSize="small" sx={{ color: '#FF9500' }} />;
      case 'offre_recue':
        return <ArrowDownwardIcon fontSize="small" sx={{ color: '#34C759' }} />;
      case 'offre_envoyee':
        return <ArrowUpwardIcon fontSize="small" sx={{ color: '#FF3B30' }} />;
      case 'jobi_echange':
        return <CurrencyExchangeIcon fontSize="small" sx={{ color: '#FF6B2C' }} />;
      default:
        return <PersonIcon fontSize="small" sx={{ color: '#8E8E93' }} />;
    }
  };

  // Obtenir le libellé selon le type
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'favori':
        return 'Favoris';
      case 'offre_recue':
        return 'Offres reçues';
      case 'offre_envoyee':
        return 'Offres envoyées';
      case 'jobi_echange':
        return 'Échanges de Jobi';
      default:
        return 'Autres';
    }
  };

  // Formater le nom de l'utilisateur
  const formatUserName = (user: User): string | null => {
    if (user.prenom && user.nom) {
      // Prendre le premier caractère du nom
      const initialNom = user.nom.charAt(0).toUpperCase();
      return `${user.prenom} ${initialNom}.`;
    }
    return null;
  };

  // Pour gérer les sections dépliées
  const [expandedSections, setExpandedSections] = useState<string[]>(['favori', 'offre_recue', 'offre_envoyee', 'jobi_echange']);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section) 
        : [...prev, section]
    );
  };

  return (
    <ModalPortal isOpen={open} onBackdropClick={handleClose}>
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        style={{ width: '100%', maxWidth: '680px', margin: '0 auto' }}
      >
        <StyledPaper>
          <Typography variant="h5" gutterBottom fontWeight="bold" sx={{ color: '#272727' }}>
            Nouvelle conversation
          </Typography>

          {isCountLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2, my: 1 }}>
              <CircularProgress size={24} sx={{ color: '#FF6B2C' }} />
            </Box>
          ) : (
            <>
              <ContentBox>
                <Box sx={{ mb: 3 }}>
                  {conversationCount?.count > 0 && conversationCount.count >= 40 && (
                    <Typography 
                      variant="body2" 
                      color="error" 
                      sx={{ 
                        mb: 2,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        p: 2,
                        borderRadius: 1,
                        bgcolor: 'error.light',
                        color: 'error.contrastText'
                      }}
                    >
                      <WarningAmberIcon fontSize="small" />
                      Vous avez actuellement {conversationCount.count} conversation{(conversationCount.count > 1 ? 's' : '')} sur 50 maximum. La plus ancienne sera automatiquement supprimée si vous dépassez cette limite.
                    </Typography>
                  )}
                  
                  <Box sx={{ 
                    mb: 2, 
                    mt: 2,
                    p: 2, 
                    borderRadius: 2, 
                    bgcolor: isLimitReached ? 'rgba(255, 59, 48, 0.08)' : 'rgba(255, 107, 44, 0.05)',
                    border: isLimitReached ? '1px solid rgba(255, 59, 48, 0.3)' : 'none'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography 
                        variant="subtitle2" 
                        fontWeight="medium" 
                        color={isLimitReached ? '#FF3B30' : 'text.secondary'}
                        sx={{ display: 'flex', alignItems: 'center' }}
                      >
                        {isLimitReached && (
                          <WarningAmberIcon fontSize="small" sx={{ mr: 1, color: '#FF3B30' }} />
                        )}
                        Conversations créées ce mois-ci
                        {isLimitReached && (
                          <LimitChip 
                            size="small" 
                            icon={<WarningAmberIcon />} 
                            label="LIMITE ATTEINTE" 
                            sx={{ ml: 2 }}
                          />
                        )}
                      </Typography>
                      <Typography 
                        variant="body2" 
                        fontWeight="bold"
                        color={isLimitReached ? '#FF3B30' : monthlyPercentage > 80 ? '#FF9500' : 'text.secondary'}
                      >
                        {currentCount}/{conversationLimit}
                      </Typography>
                    </Box>
                    
                    <ProgressContainer>
                      <StyledProgress 
                        variant="determinate" 
                        value={Math.min(monthlyPercentage, 100)}
                        islimit={isLimitReached ? 'true' : 'false'}
                      />
                      <Tooltip title="Votre limite de conversations mensuelles dépend de votre abonnement. Une fois cette limite atteinte, vous ne pourrez plus créer de nouvelles conversations ce mois-ci.">
                        <InfoIcon fontSize="small" color="action" />
                      </Tooltip>
                    </ProgressContainer>
                    
                    {isLimitReached && (
                      <Alert 
                        severity="warning" 
                        variant="outlined" 
                        sx={{ mt: 2, fontSize: '0.85rem' }}
                      >
                        Vous avez atteint votre limite mensuelle de création de conversations ({conversationLimit}). Veuillez mettre à niveau votre abonnement pour créer des conversations sans limite.
                      </Alert>
                    )}
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                {showSearchAndList && (
                  <>
                    <Box sx={{ mb: 3 }}>
                      <Typography 
                        variant="subtitle2" 
                        gutterBottom 
                        fontWeight="medium" 
                        color="text.secondary"
                        sx={{ display: 'flex', alignItems: 'center', mb: 1 }}
                      >
                        <PersonSearchIcon sx={{ mr: 1, fontSize: 20 }} />
                        Rechercher un utilisateur
                      </Typography>
                      <SearchTextField
                        fullWidth
                        variant="outlined"
                        placeholder="Nom, prénom ou @usertag..."
                        size="small"
                        value={searchQuery}
                        onChange={handleSearchChange}
                        disabled={isLimitReached}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon color="action" />
                            </InputAdornment>
                          ),
                          endAdornment: searchQuery && (
                            <InputAdornment position="end">
                              <IconButton size="small" onClick={handleClearSearch}>
                                <ClearIcon fontSize="small" />
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                    </Box>

                    {isUsersLoading || isLoadingKnownUsers ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                        <CircularProgress size={24} sx={{ color: '#FF6B2C' }} />
                      </Box>
                    ) : usersError ? (
                      <Typography color="error">
                        Une erreur est survenue lors de la recherche des utilisateurs.
                      </Typography>
                    ) : users.length > 0 ? (
                      <Box sx={{ maxHeight: 320, overflow: 'auto', mb: 2, bgcolor: 'rgba(0,0,0,0.01)', borderRadius: 2, p: 1 }}>
                        {(['favori', 'offre_recue', 'offre_envoyee', 'jobi_echange'] as const).map(type => {
                          if (!groupedUsers[type]?.length) return null;
                          
                          return (
                            <Box key={type} sx={{ mb: 1, bgcolor: 'white', borderRadius: 1, overflow: 'hidden', border: '1px solid rgba(0,0,0,0.06)' }}>
                              <Box 
                                sx={{ 
                                  px: 2, 
                                  py: 1.5, 
                                  display: 'flex', 
                                  justifyContent: 'space-between',
                                  alignItems: 'center',
                                  bgcolor: 'rgba(255,107,44,0.03)',
                                  borderBottom: expandedSections.includes(type) ? '1px solid rgba(0,0,0,0.06)' : 'none',
                                  cursor: 'pointer',
                                }}
                                onClick={() => toggleSection(type)}
                              >
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  {getTypeIcon(type)}
                                  <Typography 
                                    variant="subtitle2" 
                                    sx={{ ml: 1, fontWeight: 500, color: 'text.primary' }}
                                  >
                                    {getTypeLabel(type)}
                                  </Typography>
                                  <Chip 
                                    size="small" 
                                    label={groupedUsers[type].length} 
                                    sx={{ ml: 1, fontSize: '0.7rem', height: 20, bgcolor: 'rgba(0,0,0,0.05)' }}
                                  />
                                </Box>
                                {expandedSections.includes(type) ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
                              </Box>
                              
                              {expandedSections.includes(type) && (
                                <List disablePadding>
                                  {groupedUsers[type].map((user: User) => (
                                    <Box
                                      key={user.id}
                                      onClick={() => !isLimitReached && handleSelectUser(user)}
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        padding: '10px 16px',
                                        borderBottom: '1px solid rgba(0,0,0,0.03)',
                                        cursor: isLimitReached ? 'not-allowed' : 'pointer',
                                        backgroundColor: selectedUser?.id === user.id ? 'rgba(255, 107, 44, 0.08)' : 'transparent',
                                        '&:hover': {
                                          backgroundColor: isLimitReached 
                                            ? undefined 
                                            : selectedUser?.id === user.id 
                                              ? 'rgba(255, 107, 44, 0.12)' 
                                              : 'rgba(255, 107, 44, 0.05)',
                                        },
                                        transition: 'all 0.2s ease',
                                        opacity: isLimitReached ? 0.6 : 1
                                      }}
                                    >
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <ListItemAvatar>
                                          <UserAvatar src={user.avatar_url}>
                                            {(user.prenom?.[0] || '') + (user.nom?.[0] || '')}
                                          </UserAvatar>
                                        </ListItemAvatar>
                                        <Box>
                                          {formatUserName(user) ? (
                                            <>
                                              <Typography variant="body2" fontWeight="medium" color="text.primary">
                                                {formatUserName(user)}
                                              </Typography>
                                              <Typography variant="caption" color="text.secondary">
                                                {user.usertag ? `@${user.usertag}` : '@utilisateur'}
                                              </Typography>
                                            </>
                                          ) : (
                                            <Typography variant="body2" fontWeight="medium" color="text.primary">
                                              {user.usertag ? `@${user.usertag}` : '@utilisateur'}
                                            </Typography>
                                          )}
                                        </Box>
                                      </Box>
                                      
                                      {/* Afficher le dernier échange pour les utilisateurs ayant échangé des Jobi */}
                                      {user.type === 'jobi_echange' && user.dernierEchange && (
                                        <Box sx={{ textAlign: 'right' }}>
                                          <Typography 
                                            variant="body2" 
                                            fontWeight="medium" 
                                            color={user.dernierEchange.type === 'envoi' ? '#FF3B30' : '#34C759'}
                                          >
                                            {user.dernierEchange.type === 'envoi' ? '-' : '+'}{user.dernierEchange.montant} Jobi
                                          </Typography>
                                          <Typography variant="caption" color="text.secondary">
                                            {new Date(user.dernierEchange.date).toLocaleDateString()}
                                          </Typography>
                                        </Box>
                                      )}
                                    </Box>
                                  ))}
                                </List>
                              )}
                            </Box>
                          );
                        })}
                        
                        {/* Message si aucun utilisateur trouvé avec le critère */}
                        {!Object.keys(groupedUsers).length && searchQuery.length >= 2 && (
                          <Box sx={{ p: 3, textAlign: 'center' }}>
                            <Typography color="text.secondary">
                              Aucun utilisateur trouvé pour "{searchQuery}"
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    ) : searchQuery.length >= 2 ? (
                      <Box sx={{ py: 3, textAlign: 'center' }}>
                        <Typography color="text.secondary" align="center" sx={{ py: 1 }}>
                          Aucun utilisateur trouvé pour cette recherche.
                        </Typography>
                      </Box>
                    ) : (
                      <Box sx={{ py: 3, textAlign: 'center' }}>
                        <Typography color="text.secondary" align="center" sx={{ py: 1 }}>
                          Recherchez un utilisateur pour démarrer une conversation.
                        </Typography>
                      </Box>
                    )}
                  </>
                )}

                {selectedUser && !isLimitReached && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      Envoyer un message à{' '}
                      <Typography component="span" fontWeight="bold" sx={{ ml: 0.5, color: '#FF6B2C' }}>
                        {selectedUser.prenom} {selectedUser.nom ? selectedUser.nom.charAt(0) + '.' : ''}
                      </Typography>
                    </Typography>
                    <MessageField
                      multiline
                      rows={4}
                      fullWidth
                      variant="outlined"
                      placeholder="Écrivez votre message ici..."
                      value={message}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        if (newValue.length <= 350) {
                          setMessage(newValue);
                        }
                      }}
                      inputProps={{
                        maxLength: 350
                      }}
                      InputProps={{
                        endAdornment: message && (
                          <InputAdornment position="end">
                            <IconButton 
                              size="small" 
                              onClick={() => setMessage('')}
                              sx={{ 
                                position: 'absolute',
                                right: 8,
                                top: 8,
                                color: 'text.secondary',
                                '&:hover': {
                                  color: '#FF6B2C'
                                }
                              }}
                            >
                              <ClearIcon fontSize="small" />
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                    />
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'flex-end', 
                      mt: 1,
                      px: 1
                    }}>
                      <Typography 
                        variant="caption" 
                        color={message.length >= 350 ? 'error' : 'text.secondary'}
                        sx={{ 
                          fontSize: '0.75rem',
                          fontWeight: message.length >= 350 ? 'bold' : 'normal'
                        }}
                      >
                        {message.length}/350 caractères
                      </Typography>
                    </Box>
                  </Box>
                )}
              </ContentBox>

              <ButtonBox>
                <CancelButton 
                  variant="text" 
                  onClick={handleClose}
                >
                  Annuler
                </CancelButton>
                <SendButton
                  variant="contained"
                  disabled={!selectedUser || !message.trim() || createConversationMutation.isPending || isLimitReached}
                  endIcon={createConversationMutation.isPending ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                  onClick={handleCreateConversation}
                >
                  {createConversationMutation.isPending ? 'Envoi...' : 'Envoyer'}
                </SendButton>
              </ButtonBox>
            </>
          )}
        </StyledPaper>
      </motion.div>
    </ModalPortal>
  );
};

export default NewConversationDialog;