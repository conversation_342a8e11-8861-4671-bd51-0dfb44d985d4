import { queueEmail } from './emailQueueService';
import logger from '../utils/logger';

/**
 * Envoie un email de confirmation d'achat de crédits IA
 * @param email Email du destinataire
 * @param credits Nombre de crédits achetés
 * @param price Prix payé
 * @param oldBalance Ancien solde de crédits IA
 * @param newBalance Nouveau solde de crédits IA
 * @param isStripe Indique si l'achat a été fait via Stripe (true) ou Jobi (false)
 */
export const sendAiCreditsEmail = async (email: string, credits: number, price: number, oldBalance: number, newBalance: number, isStripe: boolean = false): Promise<boolean> => {
  try {
    const subject = 'Confirmation d\'achat de crédits IA';
    const currency = isStripe ? '€' : 'Jobi';

    const html = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirmation d'achat de crédits IA</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Arial', sans-serif; background-color: #f5f5f5; color: #333333;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.05); margin-top: 20px; margin-bottom: 20px;">
          <!-- En-tête -->
          <tr>
            <td align="center" bgcolor="#FF6B2C" style="padding: 30px 0;">
              <img src="https://jobpartiel.fr/images/logo.png" alt="JobPartiel Logo" style="max-width: 180px; height: auto;">
            </td>
          </tr>

          <!-- Titre principal -->
          <tr>
            <td align="center" style="padding: 30px 30px 20px 30px;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td align="center" style="padding-bottom: 20px;">
                    <span style="font-size: 28px; color: #FF6B2C; margin: 0;">🤖 Confirmation d'achat de crédits IA</span>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Contenu principal -->
          <tr>
            <td style="padding: 0 30px 30px 30px;">
              <p style="font-size: 16px; line-height: 24px; margin-bottom: 20px;">Bonjour,</p>

              <p style="font-size: 16px; line-height: 24px; margin-bottom: 20px;">
                Nous vous confirmons l'achat de <span style="color: #FF6B2C; font-weight: bold;">${credits} crédits IA</span> pour un montant de <span style="color: #FF6B2C; font-weight: bold;">${price} ${currency}</span>.
              </p>

              <p style="font-size: 16px; line-height: 24px; margin-bottom: 25px;">
                Ces crédits vous permettent d'utiliser notre service d'intelligence artificielle pour générer du contenu (biographies, missions, etc.) sur la plateforme JobPartiel.
              </p>

              <!-- Carte récapitulative -->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #FFF8F3; border-radius: 8px; border: 1px solid #FFE4BA; margin-bottom: 30px;">
                <tr>
                  <td style="padding: 20px;">
                    <p style="font-size: 18px; font-weight: bold; color: #333333; margin: 0 0 15px 0;">
                      📊 Détails de la transaction
                    </p>

                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA;">
                          <span style="font-size: 14px; color: #666666;">Crédits achetés:</span>
                        </td>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA; text-align: right;">
                          <span style="font-size: 16px; font-weight: bold; color: #333333;">${credits}</span>
                        </td>
                      </tr>
                      <tr>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA;">
                          <span style="font-size: 14px; color: #666666;">Prix payé:</span>
                        </td>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA; text-align: right;">
                          <span style="font-size: 16px; font-weight: bold; color: #333333;">${price} ${currency}</span>
                        </td>
                      </tr>
                      <tr>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA;">
                          <span style="font-size: 14px; color: #666666;">Date:</span>
                        </td>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA; text-align: right;">
                          <span style="font-size: 16px; color: #333333;">${new Date().toLocaleDateString('fr-FR')}</span>
                        </td>
                      </tr>
                      <tr>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA;">
                          <span style="font-size: 14px; color: #666666;">Ancien solde:</span>
                        </td>
                        <td width="50%" style="padding: 8px 0; border-bottom: 1px solid #FFE4BA; text-align: right;">
                          <span style="font-size: 16px; color: #333333;">${oldBalance} crédits</span>
                        </td>
                      </tr>
                      <tr>
                        <td width="50%" style="padding: 8px 0;">
                          <span style="font-size: 14px; color: #666666;">Nouveau solde:</span>
                        </td>
                        <td width="50%" style="padding: 8px 0; text-align: right;">
                          <span style="font-size: 16px; font-weight: bold; color: #FF6B2C;">${newBalance} crédits</span>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>

              <!-- Bouton d'action -->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 25px;">
                <tr>
                  <td align="center">
                    <a href="https://jobpartiel.fr/dashboard/ai-credits" style="display: inline-block; padding: 12px 24px; background-color: #FF6B2C; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: bold; font-size: 16px;">
                      Voir mon solde de crédits
                    </a>
                  </td>
                </tr>
              </table>

              <p style="font-size: 16px; line-height: 24px; margin-bottom: 20px;">
                Merci de votre confiance et à bientôt sur JobPartiel !
              </p>

              <!-- Carte d'utilisation des crédits -->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #F0F7FF; border-radius: 8px; border: 1px solid #D0E2FF; margin-bottom: 30px;">
                <tr>
                  <td style="padding: 20px;">
                    <p style="font-size: 16px; font-weight: bold; color: #333333; margin: 0 0 15px 0;">
                      💡 Comment utiliser vos crédits IA
                    </p>
                    <ul style="padding-left: 20px; margin: 0;">
                      <li style="font-size: 14px; line-height: 22px; margin-bottom: 8px;">
                        Générateur de biographie professionnelle pour votre profil
                      </li>
                      <li style="font-size: 14px; line-height: 22px; margin-bottom: 8px;">
                        Création de descriptions détaillées pour vos missions
                      </li>
                      <li style="font-size: 14px; line-height: 22px; margin-bottom: 8px;">
                        Création de mission.
                      </li>
                      <li style="font-size: 14px; line-height: 22px; margin-bottom: 8px;">
                        Génération de réponses personnalisées aux messages
                      </li>
                      <li style="font-size: 14px; line-height: 22px; margin-bottom: 8px;">
                        Réponse intelligente aux commentaires sur les missions
                      </li>
                    </ul>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Pied de page -->
          <tr>
            <td bgcolor="#F5F5F5" style="padding: 20px;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td style="font-size: 12px; line-height: 18px; color: #666666; text-align: center;">
                    <p style="margin: 0 0 10px 0;">Cet email est envoyé automatiquement, merci de ne pas y répondre.</p>
                    <p style="margin: 0;">Si vous avez des questions, n'hésitez pas à contacter notre <a href="https://jobpartiel.fr/contact" style="color: #FF6B2C; text-decoration: underline;">service client</a>.</p>
                  </td>
                </tr>
                <tr>
                  <td align="center" style="padding-top: 20px;">
                    <table border="0" cellpadding="0" cellspacing="0">
                      <tr>
                        <td style="font-size: 12px; line-height: 18px; color: #666666; text-align: center; padding: 0 10px;">
                          <a href="https://jobpartiel.fr" style="color: #FF6B2C; text-decoration: none; font-weight: bold;">
                            © ${new Date().getFullYear()} JobPartiel
                          </a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;

    await queueEmail(
      email,
      subject,
      html
    );

    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation d\'achat de crédits IA:', error);
    return false;
  }
};
