import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { api } from '../../services/api';
import { logger } from '../../utils/logger';
import { CheckCircle, XCircle } from 'lucide-react';

const VerifyNewsletter = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('Vérification de votre email...');
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Récupérer le token depuis l'URL
        const params = new URLSearchParams(location.search);
        const token = params.get('token');

        if (!token) {
          setStatus('error');
          setMessage('Token de vérification manquant. Veuillez vérifier le lien que vous avez reçu par email.');
          return;
        }

        // Appeler l'API pour vérifier l'email
        const response = await api.get(`/api/newsletter/verify?token=${token}`);

        if (response.data.success) {
          setStatus('success');
          setMessage(response.data.message || 'Votre email a été vérifié avec succès. Vous êtes maintenant abonné à notre newsletter !');
        } else {
          setStatus('error');
          setMessage(response.data.message || 'Une erreur est survenue lors de la vérification de votre email.');
        }
      } catch (error) {
        logger.error('Erreur lors de la vérification de l\'email:', error);
        setStatus('error');
        setMessage('Une erreur est survenue lors de la vérification de votre email. Veuillez réessayer plus tard.');
      }
    };

    verifyEmail();
  }, [location.search]);

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center px-4 py-12">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
        <div className="text-center">
          {status === 'loading' && (
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF7A35] mx-auto mb-4"></div>
          )}
          
          {status === 'success' && (
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          )}
          
          {status === 'error' && (
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          )}
          
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {status === 'loading' ? 'Vérification en cours' : 
             status === 'success' ? 'Vérification réussie' : 'Erreur de vérification'}
          </h1>
          
          <p className="text-gray-600 mb-6">{message}</p>
          
          <button
            onClick={handleGoHome}
            className="bg-[#FF7A35] text-white py-2 px-6 rounded-lg hover:bg-[#FF8F35] transition-colors focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:ring-opacity-50"
          >
            Retour à l'accueil
          </button>
        </div>
      </div>
    </div>
  );
};

export default VerifyNewsletter;
