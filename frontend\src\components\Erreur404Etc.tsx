import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from './Navbar';
import Footer from './Footer';
import { motion } from 'framer-motion';

const errorMessages: Record<number, string> = {
  404: "Oups! La page que vous cherchez n'existe pas",
  500: "Erreur interne du serveur",
  403: "Vous devez vous connecter pour accéder à cette page",
  401: "Accès non autorisé",
  400: "Requête invalide",
};

interface ErrorPageProps {
  code: number;
}

const Erreur404Etc: React.FC<ErrorPageProps> = ({ code }) => {
  const message = errorMessages[code] || "Une erreur inattendue s'est produite";
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (code === 403) {
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            navigate('/login');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [code, navigate]);

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA]">
      <Navbar />
      <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-lg w-full"
        >
          <div className="bg-white rounded-2xl shadow-lg p-8 border border-[#FF7A35]/10 relative overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#FF6B2C] to-[#ff965e]"></div>
            
            {code !== 403 && (
              <motion.h1 
                className="text-8xl font-extrabold text-[#FF7A35]"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                {code}
              </motion.h1>
            )}
            
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <p className="text-2xl mt-4 font-semibold text-gray-800">{message}</p>
              <p className="mt-3 text-gray-600 mb-6">
                {code === 401 && "Vous n'avez pas les permissions nécessaires pour accéder à cette ressource."}
                {code === 404 && "La page que vous recherchez semble introuvable."}
                {code === 500 && "Nos serveurs rencontrent un problème. Veuillez réessayer plus tard."}
              </p>
              
              {code === 403 && (
                <div className="flex justify-center mt-4 mb-2">
                  <div className="bg-[#FFF8F3] rounded-lg p-4 border border-[#FF7A35]/20">
                    <p className="text-gray-700">
                      Vous serez redirigé vers la page de connexion dans{" "}
                      <span className="font-bold text-[#FF7A35]">{countdown}</span> secondes.
                    </p>
                  </div>
                </div>
              )}
              
              {code !== 403 && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <a
                    href="/"
                    className="mt-6 px-8 py-3 bg-gradient-to-r from-[#FF6B2C] to-[#ff965e] text-white rounded-full shadow-lg hover:shadow-xl transition duration-300 inline-block font-medium"
                  >
                    Retour à l'accueil
                  </a>
                </motion.div>
              )}
            </motion.div>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  );
};

export default Erreur404Etc;
