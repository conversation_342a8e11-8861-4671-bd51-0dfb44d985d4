import React from 'react';
import ModalPortal from '../ModalPortal';
import { motion } from 'framer-motion';

interface ConfirmDeleteReviewModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const ConfirmDeleteReviewModal: React.FC<ConfirmDeleteReviewModalProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <div
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-xl p-6 max-w-md w-full mx-auto my-auto shadow-xl relative"
        >
          <h3 className="text-xl font-semibold text-gray-800 mb-4">Confirmer la suppression</h3>
          <div className="space-y-4 mb-6 overflow-y-auto max-h-[60vh]">
            <p className="text-gray-600">
              Êtes-vous sûr de vouloir supprimer cet avis ? Cette action est irréversible.
            </p>
            <div className="flex items-start gap-2 p-3 bg-orange-50 rounded-lg border border-orange-100">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FF6B2C] mt-0.5 shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-[#FF6B2C]">
                La suppression de cet avis entraînera la perte d'1 Jobi de votre solde.
              </p>
            </div>
          </div>
          <div className="flex justify-end gap-3 pt-2 border-t border-gray-100">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={onConfirm}
              className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center gap-2"
            >
              <span>Supprimer</span>
            </button>
          </div>
        </motion.div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmDeleteReviewModal; 