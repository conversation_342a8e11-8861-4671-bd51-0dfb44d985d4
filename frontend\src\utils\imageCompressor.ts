/* Optimiser  et compresse les images sur le frontend avant l'upload par default en JPEG */

import { logger } from "./logger";

interface CompressedImage {
  file: File;
  width: number;
  height: number;
}

export const compressImage = async (
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<CompressedImage> => {
  const {
    // Par défaut, on optimise les images pour le profil et la galerie si rien n'est spécifié
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 0.80, // 0.80 = 80% de qualité
  } = options;

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);

    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        // Calculer les nouvelles dimensions en conservant le ratio
        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width);
          width = maxWidth;
        }

        if (height > maxHeight) {
          width = Math.round((width * maxHeight) / height);
          height = maxHeight;
        }

        // Créer un canvas pour la compression
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Impossible de créer le contexte 2D'));
          return;
        }

        // Dessiner l'image redimensionnée
        ctx.drawImage(img, 0, 0, width, height);

        // Convertir en JPEG avec la qualité spécifiée
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Échec de la compression'));
              return;
            }

            // Créer un nouveau fichier avec le même nom mais en .jpg
            const fileName = file.name.replace(/\.[^/.]+$/, '') + '.jpg';
            const compressedFile = new File([blob], fileName, {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });

            resolve({
              file: compressedFile,
              width,
              height,
            });
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Erreur lors du chargement de l\'image'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Erreur lors de la lecture du fichier'));
    };
  });
};

// Hook personnalisé pour la compression d'images
export const compressProfilPhoto = async (file: File): Promise<File> => {
  const compressed = await compressImage(file, {
    maxWidth: 400, // Compresser les images de profil à 400x400
    maxHeight: 400, // Compresser les images de profil à 400x400
    quality: 0.73 // Compresser les images de profil à 73% de qualité
  });
  logger.info('Image Profil compressée :', compressed.file + ' ' + compressed.width + ' ' + compressed.height); 
  return compressed.file;
};

export const compressGalleryPhoto = async (file: File): Promise<File> => {
  const compressed = await compressImage(file, {
    maxWidth: 800, // Compresser les images de galerie à 800x800
    maxHeight: 800, // Compresser les images de galerie à 800x800
    quality: 0.73 // Compresser les images de galerie à 73% de qualité
  });
  logger.info('Image Galerie compressée :', compressed.file + ' ' + compressed.width + ' ' + compressed.height); 
  return compressed.file;
};

export const compressBannerPhoto = async (file: File): Promise<File> => {
  const compressed = await compressImage(file, {
    maxWidth: 1152, // Largeur bannière
    maxHeight: 640, // Hauteur bannière
    quality: 0.80   // Qualité bannière
  });
  logger.info('Image Bannière compressée :', compressed.file + ' ' + compressed.width + ' ' + compressed.height); 
  return compressed.file;
};

// Hook personnalisé (peut encore être utilisé si nécessaire ailleurs, mais les fonctions sont maintenant exportables directement)
export const useImageCompression = () => {
  // Retourne les fonctions exportées
  return {
    compressProfilPhoto,
    compressGalleryPhoto,
    compressBannerPhoto
  };
};
