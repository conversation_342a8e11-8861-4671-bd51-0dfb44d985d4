// Types pour le système de gestion des utilisateurs

export interface User {
  id: string;
  email: string;
  role: 'jobutil' | 'jobmodo' | 'jobpadm';
  user_type: 'particulier' | 'entreprise' | 'professionnel';
  profil_actif: boolean;
  email_verifier: boolean;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  is_online: boolean;
  last_activity: string | null;
  date_inscription: string;
  created_at: string;
  updated_at: string;
  is_anonymized: boolean;
  suspension_reason?: string | null;
  suspended_until?: string | null;
}

export interface UserProfil {
  id: string;
  user_id: string;
  nom: string;
  prenom: string;
  telephone: string | null;
  ville: string | null;
  code_postal: string | null;
  adresse: string | null;
  photo_url: string | null;
  mode_vacance: boolean;
  profil_visible: boolean;
  seo_indexable: boolean;
  intervention_zone: any | null;
  created_at: string;
  updated_at: string;
}

export interface UserAbonnement {
  id: string;
  user_id: string;
  type_abonnement: 'basic' | 'premium' | 'pro';
  statut: 'actif' | 'suspendu' | 'expire' | 'annule';
  date_debut: string;
  date_fin: string;
  montant: number;
  renouvellement_auto: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserJobi {
  id: string;
  user_id: string;
  montant: number;
  created_at: string;
  updated_at: string;
}

export interface UserJobiHistorique {
  id: string;
  user_id: string;
  titre: string;
  description: string;
  montant: number;
  message: string | null;
  created_at: string;
}

export interface UserAiCredits {
  id: string;
  user_id: string;
  credits: number;
  created_at: string;
  updated_at: string;
}

export interface UserAiCreditsHistorique {
  id: string;
  user_id: string;
  operation_type: string;
  montant: number;
  solde_avant: number;
  solde_apres: number;
  description: string;
  reference: string | null;
  created_at: string;
}

export interface UserMission {
  id: string;
  user_id: string;
  titre: string;
  description: string;
  statut: 'brouillon' | 'publiee' | 'en_cours' | 'terminee' | 'annulee';
  budget: number;
  created_at: string;
  updated_at: string;
}

export interface UserReview {
  id: string;
  user_id: string;
  target_user_id: string;
  note: number;
  commentaire: string;
  created_at: string;
}

export interface UserBadge {
  id: string;
  user_id: string;
  nom_badge: string;
  date_obtention: string;
}

export interface UserLoginHistory {
  id: string;
  user_id: string;
  login_date: string;
  ip_address: string;
}

export interface UserNotification {
  id: string;
  user_id: string;
  titre: string;
  message: string;
  type: string;
  lu: boolean;
  created_at: string;
}

export interface UserGallery {
  id: string;
  user_id: string;
  nom: string;
  description: string;
  created_at: string;
  user_gallery_photos?: UserGalleryPhoto[];
}

export interface UserGalleryPhoto {
  id: string;
  gallery_id: string;
  photo_url: string;
  created_at: string;
}

export interface UserFeaturedPhoto {
  id: string;
  user_id: string;
  photo_url: string;
  created_at: string;
}

export interface UserTransaction {
  id: string;
  user_id: string;
  type: string;
  montant: number;
  created_at: string;
}

// Types pour les détails complets d'un utilisateur
export interface UserDetailData {
  user: User;
  profil: UserProfil | null;
  abonnements: UserAbonnement[];
  jobi: UserJobi;
  jobiHistorique: UserJobiHistorique[];
  transactions: UserTransaction[];
  services: any[];
  missions: UserMission[];
  reviews: UserReview[];
  badges: UserBadge[];
  loginHistory: UserLoginHistory[];
  notifications: UserNotification[];
  aiCredits: UserAiCredits;
  aiCreditsHistorique: UserAiCreditsHistorique[];
  galleries: UserGallery[];
  featuredPhotos: UserFeaturedPhoto[];
}

// Types pour les statistiques utilisateur
export interface UserStats {
  missions: {
    total: number;
    parStatut: Record<string, number>;
    budgetTotal: number;
    evolution: Array<{ date: string; count: number }>;
  };
  transactions: {
    total: number;
    montantTotal: number;
    parType: Record<string, number>;
    evolution: Array<{ date: string; montant: number }>;
  };
  jobi: {
    transactions: number;
    evolution: Array<{ date: string; montant: number }>;
  };
  aiCredits: {
    utilisations: number;
    evolution: Array<{ date: string; credits: number }>;
  };
  connexions: {
    total: number;
    evolution: Array<{ date: string; connexions: number }>;
    ipsUniques: number;
  };
  reviews: {
    total: number;
    noteMoyenne: number;
    evolution: Array<{ date: string; note: number }>;
  };
}

// Types pour les filtres de recherche
export interface UserFilters {
  search: string;
  role: string;
  status: string;
  userType: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Types pour la pagination
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Types pour les réponses API
export interface UserListResponse {
  success: boolean;
  data: {
    users: User[];
    pagination: Pagination;
  };
  message?: string;
}

export interface UserDetailResponse {
  success: boolean;
  data: UserDetailData;
  message?: string;
}

export interface UserStatsResponse {
  success: boolean;
  data: UserStats;
  message?: string;
}

// Types pour les actions administratives
export interface JobiManagementRequest {
  action: 'add' | 'remove';
  montant: number;
  description?: string;
  sendNotification?: boolean;
  sendEmail?: boolean;
}

export interface AiCreditsManagementRequest {
  action: 'add' | 'remove';
  credits: number;
  description?: string;
  sendNotification?: boolean;
  sendEmail?: boolean;
}

export interface SubscriptionManagementRequest {
  action: 'create' | 'extend' | 'change_type' | 'suspend' | 'activate';
  type_abonnement?: 'basic' | 'premium' | 'pro';
  duree_jours?: number;
  description?: string;
  sendNotification?: boolean;
  sendEmail?: boolean;
}

export interface UserActionRequest {
  action: 'suspend' | 'unsuspend' | 'verify_profile' | 'verify_identity' | 'verify_company' | 'verify_insurance' | 'activate' | 'deactivate';
  reason?: string;
  duration?: number;
  sendNotification?: boolean;
  sendEmail?: boolean;
}

// Types pour les réponses d'actions
export interface ActionResponse {
  success: boolean;
  message: string;
  data?: any;
  toastType: 'success' | 'error' | 'warning' | 'info';
}

// Types pour les statistiques rapides
export interface QuickStats {
  totalUsers: number;
  activeUsers: number;
  suspendedUsers: number;
  verifiedUsers: number;
}