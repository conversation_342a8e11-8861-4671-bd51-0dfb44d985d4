// Résultats de recherche avec filtres - Version refonte complète inspirée de ServicesPage

import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Skeleton,
  Alert,
  Pagination,
  MenuItem,
  TextField,
  InputAdornment,
  Avatar,
  Paper,
  Stack,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Fade,
  Grow,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Search,
  LocationOn,
  Verified,
  FilterList,
  Star,
  Favorite,
  FavoriteBorder,
  Share,
  ViewModule,
  ViewList,
  SearchOff,
  TrendingUp,
  Security,
  FlashOn,
  AutoAwesome,
  History,
  Clear,
  Map
} from '@mui/icons-material';
import { API_CONFIG } from '../../config/api';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../dashboard/services/types';
import SearchResultsMap from '../../components/services/SearchResultsMap';
import { normalizeString, validateCity, isPostalCode, searchCitiesWithSuggestions } from '../../utils/geocoding';
import CitySearchInput from '../../components/services/CitySearchInput';
import { generateSearchResultsSEO, updatePageMetadata, injectStructuredData, generateDetailedSchemas } from '../../utils/seoUtils';
import SEOHead from '../../components/services/SEOHead';
import PhotoSwiper from '../../components/services/PhotoSwiper';
import logger from '@/utils/logger';

// Couleurs exactes de JobPartiel - Version optimisée
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowHover: 'rgba(0, 0, 0, 0.15)',
  glassBorder: 'rgba(255, 255, 255, 0.18)',
  gradient: {
    primary: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 50%, #FF965E 100%)',
    secondary: 'linear-gradient(135deg, #FFE4BA 0%, #FFF8F3 100%)',
    glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)'
  }
};

interface Provider {
  user_id: string;
  slug: string;
  nom: string;
  prenom: string;
  ville: string;
  code_postal: string;
  bio: string;
  photo_url: string;
  slogan: string;
  updated_at: string;
  intervention_zone?: {
    center: [number, number];
    radius: number;
    adresse?: string;
    france_entiere?: boolean;
  };
  featured_photos?: Array<{
    id: string;
    photo_url: string;
    caption: string;
    created_at: string;
  }>;
  galleries?: Array<{
    id: string;
    name: string;
    description: string;
    cover_image: string;
    photos: Array<{
      id: string;
      photo_url: string;
      caption: string;
      order_index: number;
    }>;
  }>;
  users: Array<{
    user_type: string;
    profil_actif: boolean;
    profil_verifier: boolean;
    user_services: Array<{
      id: string;
      titre: string;
      description: string;
      category_id: string;
      subcategory_id: string;
      tarif_horaire: number;
      statut: string;
    }>;
    user_reviews?: Array<{
      id: string;
      note: number;
      commentaire: string;
      date_creation: string;
      statut: string;
    }>;
  }>;
}

const SearchResultsPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // États principaux
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // États UI améliorés
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Filtres - Initialisation à partir des noms dans l'URL
  const [searchTerm, setSearchTerm] = useState(() => {
    // Si on a une catégorie et/ou sous-catégorie dans l'URL, ne pas mettre le terme de recherche
    // pour éviter d'afficher le nom de la sous-catégorie dans le champ de recherche
    const hasCategory = searchParams.get('category');
    const hasSubcategory = searchParams.get('subcategory');
    if (hasCategory || hasSubcategory) {
      return '';
    }
    return searchParams.get('q') || '';
  });
  const [selectedCity, setSelectedCity] = useState(searchParams.get('city') || '');
  const [selectedCategory, setSelectedCategory] = useState(() => {
    const categoryName = searchParams.get('category');
    if (categoryName) {
      const categoryObj = SERVICE_CATEGORIES.find(cat => cat.nom === categoryName);
      return categoryObj ? categoryObj.id : '';
    }
    return '';
  });
  const [selectedSubcategory, setSelectedSubcategory] = useState(() => {
    const subcategoryName = searchParams.get('subcategory');
    if (subcategoryName) {
      const subcategoryObj = SERVICE_SUBCATEGORIES.find(sub => sub.nom === subcategoryName);
      return subcategoryObj ? subcategoryObj.id : '';
    }
    return '';
  });
  const [budgetRange, setBudgetRange] = useState(searchParams.get('budget') || '');
  const [sortBy, setSortBy] = useState('relevance');

  // États pour le système de recherche intelligent (ajouté)
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [searchError, setSearchError] = useState<string>('');
  const [suggestedTerm, setSuggestedTerm] = useState<string>('');
  const [showMap, setShowMap] = useState(false);

  // États pour les suggestions de ville
  const [cityValidationError, setCityValidationError] = useState<string>('');

  useEffect(() => {
    loadSearchResults();
  }, [searchParams]);

  const loadSearchResults = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (searchParams.get('q')) params.append('q', searchParams.get('q')!);
      if (searchParams.get('category')) params.append('category', searchParams.get('category')!);
      if (searchParams.get('subcategory')) params.append('subcategory', searchParams.get('subcategory')!);
      if (searchParams.get('city')) params.append('city', searchParams.get('city')!);
      if (searchParams.get('budget')) params.append('budget', searchParams.get('budget')!);
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/services/search?${params.toString()}`,
        {
          method: 'GET',
          headers: API_CONFIG.headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error('Erreur lors de la recherche');
      }

      const data = await response.json();
      setProviders(data.providers || []);
      setPagination(data.pagination || pagination);

    } catch (err) {
      console.error('Erreur lors de la recherche:', err);
      setError('Erreur lors de la recherche');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = async () => {
    // Réinitialiser les erreurs
    setSearchError('');
    setSuggestedTerm('');
    setCityValidationError('');

    // Valider le terme de recherche s'il est fourni
    if (searchTerm.trim()) {
      const validation = validateSearchTerm(searchTerm.trim());

      if (!validation.isValid) {
        if (validation.suggestion) {
          setSearchError(`Service non trouvé. Vouliez-vous dire "${validation.suggestion}" ?`);
          setSuggestedTerm(validation.suggestion);
        } else {
          setSearchError('Service non reconnu. Veuillez choisir un service dans les suggestions.');
        }
        return; // Ne pas effectuer la recherche
      }
    }

    // Valider la ville si elle est fournie
    if (selectedCity.trim()) {
      const isCityValid = await validateCityInput(selectedCity.trim());
      if (!isCityValid) {
        return; // Ne pas effectuer la recherche si la ville n'est pas valide
      }
    }

    const params = new URLSearchParams();
    if (searchTerm) params.append('q', searchTerm);
    if (selectedCity) params.append('city', selectedCity);

    // Envoyer les noms au lieu des IDs pour les catégories
    if (selectedCategory) {
      const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory);
      if (categoryObj) params.append('category', categoryObj.nom);
    }
    if (selectedSubcategory) {
      const subcategoryObj = SERVICE_SUBCATEGORIES.find(sub => sub.id === selectedSubcategory);
      if (subcategoryObj) params.append('subcategory', subcategoryObj.nom);
    }

    if (budgetRange) params.append('budget', budgetRange);

    setSearchParams(params);
  };

  const handleProviderClick = (provider: Provider) => {
    navigate(`/profil/${provider.slug}`);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPagination(prev => ({ ...prev, page: value }));
    const params = new URLSearchParams(searchParams);
    params.set('page', value.toString());
    setSearchParams(params);
  };

  // Hooks et effets optimisés
  useEffect(() => {
    // Optimisation SEO - Mettre à jour les métadonnées de la page
    const seoMetadata = generateSearchResultsSEO(searchParams);
    updatePageMetadata(seoMetadata);

    // Injecter les données structurées JSON-LD pour le SEO
    const query = searchParams.get('q') || '';
    const city = searchParams.get('city') || '';
    const category = searchParams.get('category') || '';
    const subcategory = searchParams.get('subcategory') || '';

    const structuredData = generateDetailedSchemas(
      subcategory || category || query || 'recherche',
      city || 'france',
      providers
    );
    injectStructuredData(structuredData.service);

    // Animation d'entrée de page
    const timer = setTimeout(() => setIsPageLoaded(true), 100);

    // Charger les favoris depuis localStorage
    const savedFavorites = localStorage.getItem('jobpartiel_profile_favorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }

    // Charger les recherches récentes
    const savedRecent = localStorage.getItem('jobpartiel_recent_searches');
    if (savedRecent) {
      setRecentSearches(JSON.parse(savedRecent));
    }

    return () => clearTimeout(timer);
  }, [searchParams, providers]);

  // Importer la fonction de normalisation depuis les utilitaires
  // (supprimée car maintenant importée depuis geocoding.ts)

  // Fonction de calcul de pertinence
  const calculateRelevance = (searchTerm: string, subcategory: any): number => {
    const normalizedSearch = normalizeString(searchTerm);
    const normalizedName = normalizeString(subcategory.nom);

    let score = 0;

    if (normalizedName.includes(normalizedSearch)) {
      if (normalizedName === normalizedSearch) {
        score += 100;
      } else if (normalizedName.startsWith(normalizedSearch)) {
        score += 80;
      } else {
        score += 60;
      }
    }

    if (subcategory.synonymes) {
      for (const syn of subcategory.synonymes) {
        const normalizedSyn = normalizeString(syn);
        if (normalizedSyn.includes(normalizedSearch)) {
          if (normalizedSyn === normalizedSearch) {
            score += 100;
          } else if (normalizedSyn.startsWith(normalizedSearch)) {
            score += 80;
          } else {
            score += 60;
          }
          break;
        }
      }
    }

    return score;
  };

  // Fonction de recherche simple et fluide
  const handleSimpleSearch = (query: string) => {
    const searchTermValue = query.toLowerCase().trim();

    if (!searchTermValue) {
      setSearchSuggestions([]);
      return;
    }

    const matchingSubcategories = SERVICE_SUBCATEGORIES
      .map(sub => ({
        subcategory: sub,
        relevance: calculateRelevance(searchTermValue, sub)
      }))
      .filter(({ relevance }) => relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 8) // Augmenter le nombre de suggestions
      .map(({ subcategory }) => subcategory.nom);

    setSearchSuggestions(matchingSubcategories);
  };

  // Fonction pour valider si le terme de recherche correspond à une catégorie/sous-catégorie
  const validateSearchTerm = (searchTerm: string): { isValid: boolean; suggestion?: string } => {
    const normalizedSearch = normalizeString(searchTerm);

    // Recherche exacte dans les sous-catégories
    const exactMatch = SERVICE_SUBCATEGORIES.find(sub => {
      const normalizedName = normalizeString(sub.nom);
      if (normalizedName === normalizedSearch) return true;

      // Vérifier les synonymes
      if (sub.synonymes) {
        return sub.synonymes.some(syn => normalizeString(syn) === normalizedSearch);
      }
      return false;
    });

    if (exactMatch) {
      return { isValid: true };
    }

    // Recherche de correspondance partielle pour suggérer
    const partialMatches = SERVICE_SUBCATEGORIES
      .map(sub => ({
        subcategory: sub,
        relevance: calculateRelevance(searchTerm, sub)
      }))
      .filter(({ relevance }) => relevance > 50) // Seuil plus élevé pour les suggestions
      .sort((a, b) => b.relevance - a.relevance);

    if (partialMatches.length > 0) {
      return {
        isValid: false,
        suggestion: partialMatches[0].subcategory.nom
      };
    }

    return { isValid: false };
  };

  // Fonction pour ajouter à l'historique de recherche
  const addToSearchHistory = (searchTermValue: string) => {
    if (!searchTermValue.trim()) return;

    setRecentSearches(prev => {
      const newRecent = [searchTermValue, ...prev.filter(item => item !== searchTermValue)].slice(0, 5);
      localStorage.setItem('jobpartiel_recent_searches', JSON.stringify(newRecent));
      return newRecent;
    });
  };

  // Fonction pour valider la ville ou code postal saisie (plus permissive)
  const validateCityInput = async (cityName: string) => {
    if (!cityName.trim()) {
      setCityValidationError('');
      return true;
    }

    try {
      const trimmedInput = cityName.trim();

      // Vérifier d'abord si c'est un code postal
      if (isPostalCode(trimmedInput)) {
        // Si c'est un code postal, rechercher directement
        const postalResults = await searchCitiesWithSuggestions(trimmedInput, 1);
        if (postalResults.length > 0) {
          setCityValidationError('');
          return true;
        } else {
          setCityValidationError('Code postal non trouvé. Veuillez vérifier.');
          return false;
        }
      }

      // Pour les noms de ville, être plus permissif
      // Accepter si la ville fait au moins 2 caractères
      if (trimmedInput.length >= 2) {
        // Essayer de valider avec l'API
        const validCity = await validateCity(trimmedInput);

        if (validCity) {
          // Ville trouvée dans l'API
          setCityValidationError('');
          return true;
        } else {
          // Ville non trouvée dans l'API, mais on accepte quand même
          // L'utilisateur peut avoir tapé une ville valide qui n'est pas dans notre base
          setCityValidationError(''); // Pas d'erreur, on laisse passer
          return true;
        }
      } else {
        // Moins de 2 caractères
        setCityValidationError('Veuillez saisir au moins 2 caractères.');
        return false;
      }
    } catch (error) {
      console.error('Erreur lors de la validation de ville:', error);
      // En cas d'erreur, on accepte quand même si c'est au moins 2 caractères
      if (cityName.trim().length >= 2) {
        setCityValidationError('');
        return true;
      } else {
        setCityValidationError('Veuillez saisir au moins 2 caractères.');
        return false;
      }
    }
  };

  // Fonctions utilitaires pour les catégories
  const getSubcategoriesForCategory = (categoryId: string) => {
    return SERVICE_SUBCATEGORIES.filter(sub => sub.categoryId === categoryId);
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory(''); // Réinitialiser la sous-catégorie
  };

  // Fonction pour récupérer le nom de la sous-catégorie à partir de son ID
  const getSubcategoryName = (subcategoryId: string): string => {
    const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === subcategoryId);
    return subcategory ? subcategory.nom : 'Service non spécifié';
  };

  // Fonctions utilitaires
  const toggleFavorite = (providerId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(providerId)) {
        newFavorites.delete(providerId);
      } else {
        newFavorites.add(providerId);
      }
      localStorage.setItem('jobpartiel_profile_favorites', JSON.stringify([...newFavorites]));
      return newFavorites;
    });
  };

  const handleShare = async (provider: Provider) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${provider.prenom} ${provider.nom.charAt(0)}. - Jobbeur`,
          text: `Découvrez ${provider.prenom}, jobbeur qualifié`,
          url: window.location.href
        });
      } catch (err) {
        logger.error('Erreur lors du partage:', err);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // Filtrage et tri des providers
  const filteredProviders = useMemo(() => {
    let filtered = [...providers];

    // Tri
    switch (sortBy) {
      case 'price_asc':
        filtered.sort((a, b) => {
          const priceA = Math.min(...(a.users[0]?.user_services?.map(s => s.tarif_horaire) || [25]));
          const priceB = Math.min(...(b.users[0]?.user_services?.map(s => s.tarif_horaire) || [25]));
          return priceA - priceB;
        });
        break;
      case 'price_desc':
        filtered.sort((a, b) => {
          const priceA = Math.min(...(a.users[0]?.user_services?.map(s => s.tarif_horaire) || [25]));
          const priceB = Math.min(...(b.users[0]?.user_services?.map(s => s.tarif_horaire) || [25]));
          return priceB - priceA;
        });
        break;
      case 'name':
        filtered.sort((a, b) => a.prenom.localeCompare(b.prenom));
        break;
      default: // relevance
        break;
    }

    return filtered;
  }, [providers, sortBy]);

  if (loading) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        position: 'relative'
      }}>
        {/* Barre de progression de chargement */}
        <Fade in={loading}>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 9999,
              background: COLORS.white,
              boxShadow: `0 4px 20px ${COLORS.shadow}`
            }}
          >
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 4,
                background: COLORS.lightGray,
                '& .MuiLinearProgress-bar': {
                  background: COLORS.gradient.primary,
                  borderRadius: '2px'
                }
              }}
            />
          </Box>
        </Fade>

        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Skeleton variant="text" width="60%" height={80} sx={{ mb: 2 }} />
          <Skeleton variant="text" width="40%" height={40} sx={{ mb: 4 }} />

          <Grid container spacing={3}>
            {[...Array(6)].map((_, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <Card sx={{ borderRadius: 4, overflow: 'hidden' }}>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" width="80%" height={30} />
                    <Skeleton variant="text" width="60%" height={20} />
                    <Skeleton variant="text" width="40%" height={20} />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Container maxWidth="md">
          <Alert
            severity="error"
            sx={{
              borderRadius: 4,
              boxShadow: `0 8px 30px ${COLORS.shadow}`,
              fontSize: '1.1rem'
            }}
          >
            {error}
          </Alert>
        </Container>
      </Box>
    );
  }

  // Générer les métadonnées SEO
  const seoMetadata = generateSearchResultsSEO(searchParams);

  return (
    <>
      <SEOHead
        title={seoMetadata.title}
        description={seoMetadata.description}
        keywords={seoMetadata.keywords}
        canonical={seoMetadata.canonical}
        ogImage={seoMetadata.ogImage}
        ogType={seoMetadata.ogType}
        ogLocale={seoMetadata.ogLocale}
        twitterCard={seoMetadata.twitterCard}
        twitterImage={seoMetadata.twitterImage}
        structuredData={generateDetailedSchemas(
          searchParams.get('subcategory') || searchParams.get('category') || searchParams.get('q') || 'recherche',
          searchParams.get('city') || 'france',
          providers
        ).service}
      />
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        position: 'relative',
        overflow: 'hidden',
        paddingTop: { xs: '80px', md: '100px', lg: '120px'} // Espace pour le header fixe
      }}>
      {/* Styles d'animation globaux */}
      <style>
        {`
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
          }
        `}
      </style>

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 1, py: { xs: 2, md: 4 } }}>
        {/* Hero Section avec titre et stats */}
        <Fade in={isPageLoaded} timeout={800}>
          <Box textAlign="center" sx={{ mb: { xs: 6, md: 8 } }}>
            <Typography
              variant={isMobile ? "h3" : "h1"}
              component="h1"
              sx={{
                fontSize: { xs: '2.5rem', md: '4rem', lg: '4.5rem' },
                fontWeight: 900,
                background: COLORS.gradient.primary,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 3,
                position: 'relative',
                letterSpacing: '-0.02em',
                lineHeight: 1.1
              }}
            >
              Résultats de recherche
            </Typography>

            <Typography
              variant={isMobile ? "h6" : "h4"}
              sx={{
                color: COLORS.neutral,
                mb: 4,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                fontWeight: 500,
                maxWidth: '800px',
                mx: 'auto',
                lineHeight: 1.6
              }}
            >
              {pagination.total} professionnel{pagination.total > 1 ? 's' : ''} trouvé{pagination.total > 1 ? 's' : ''}
              <br />
              <Box component="span" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                correspondant à vos critères
              </Box>
            </Typography>

            {/* Stats de confiance */}
            <Stack
              direction={isMobile ? "column" : "row"}
              spacing={isMobile ? 2 : 4}
              justifyContent="center"
              sx={{ mb: 6 }}
            >
              {[
                {
                  icon: <Verified />,
                  value: "100%",
                  label: "Jobbeurs vérifiés",
                  color: COLORS.success
                },
                {
                  icon: <Security />,
                  value: "24/7",
                  label: "Support client",
                  color: COLORS.primary
                },
                {
                  icon: <FlashOn />,
                  value: "< 1h",
                  label: "Réponse rapide",
                  color: COLORS.warning
                }
              ].map((stat, index) => (
                <Paper
                  key={index}
                  elevation={0}
                  sx={{
                    p: 3,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    bgcolor: COLORS.white,
                    border: `2px solid ${COLORS.borderColor}`,
                    borderRadius: 3,
                    minWidth: isMobile ? 'auto' : '200px',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: stat.color,
                      boxShadow: `0 8px 30px ${stat.color}20`,
                      transform: 'translateY(-4px)'
                    }
                  }}
                >
                  <Avatar
                    sx={{
                      bgcolor: `${stat.color}15`,
                      color: stat.color,
                      width: 48,
                      height: 48
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 800, color: stat.color, mb: 0.5 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
                      {stat.label}
                    </Typography>
                  </Box>
                </Paper>
              ))}
            </Stack>
          </Box>
        </Fade>

        {/* Barre de filtres et recherche */}
        <Fade in={isPageLoaded} timeout={1000}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 4 },
              mb: 6,
              background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
              border: `3px solid ${COLORS.borderColor}`,
              borderRadius: 5,
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: `0 8px 30px rgba(0, 0, 0, 0.08)`
            }}
          >
            <Stack spacing={3}>
              {/* En-tête des filtres */}
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Typography variant="h5" sx={{
                  fontWeight: 700,
                  color: COLORS.primary,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <FilterList />
                  Affiner votre recherche
                </Typography>

                <Stack direction="row" spacing={1}>
                  <Tooltip title="Voir sur la carte">
                    <IconButton
                      onClick={() => setShowMap(true)}
                      sx={{
                        color: COLORS.primary,
                        '&:hover': { bgcolor: `${COLORS.primary}10` }
                      }}
                    >
                      <Map />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={viewMode === 'grid' ? 'Vue liste' : 'Vue grille'}>
                    <IconButton
                      onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                      sx={{
                        color: COLORS.primary,
                        '&:hover': { bgcolor: `${COLORS.primary}10` }
                      }}
                    >
                      {viewMode === 'grid' ? <ViewList /> : <ViewModule />}
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Box>

              {/* Filtres */}
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <Box sx={{ position: 'relative' }}>
                    <TextField
                      fullWidth
                      placeholder="Rechercher un jobbeur..."
                      value={searchTerm}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSearchTerm(value);
                        handleSimpleSearch(value);
                        // Réinitialiser les erreurs quand l'utilisateur tape
                        if (searchError) {
                          setSearchError('');
                          setSuggestedTerm('');
                        }
                      }}
                      onFocus={() => setIsSearchFocused(true)}
                      onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          setIsSearchFocused(false);
                          addToSearchHistory(searchTerm);
                          handleFilterChange();
                        }
                        if (e.key === 'Escape') {
                          setSearchTerm('');
                          setSearchSuggestions([]);
                          setIsSearchFocused(false);
                        }
                      }}
                      slotProps={{
                        input: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <Search sx={{ color: COLORS.primary }} />
                            </InputAdornment>
                          ),
                          endAdornment: searchTerm && (
                            <InputAdornment position="end">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSearchTerm('');
                                  setSearchSuggestions([]);
                                }}
                                sx={{
                                  color: 'text.secondary',
                                  '&:hover': {
                                    color: COLORS.primary,
                                    bgcolor: `${COLORS.primary}10`
                                  }
                                }}
                              >
                                <Clear />
                              </IconButton>
                            </InputAdornment>
                          )
                        }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          bgcolor: COLORS.white,
                          border: `2px solid ${COLORS.borderColor}`,
                          '&:hover': {
                            borderColor: COLORS.primary
                          },
                          '&.Mui-focused': {
                            borderColor: COLORS.primary,
                            boxShadow: `0 0 0 3px ${COLORS.primary}20`
                          },
                          '& fieldset': { border: 'none' }
                        }
                      }}
                    />

                    {/* Suggestions intelligentes */}
                    {isSearchFocused && searchSuggestions.length > 0 && (
                      <Fade in={isSearchFocused && searchSuggestions.length > 0}>
                        <Box
                          sx={{
                            position: 'absolute',
                            top: '100%',
                            left: 0,
                            right: 0,
                            zIndex: 9999,
                            marginTop: '4px'
                          }}
                        >
                          <Paper
                            elevation={8}
                            sx={{
                              borderRadius: 3,
                              border: `2px solid ${COLORS.borderColor}`,
                              bgcolor: COLORS.white,
                              boxShadow: `0 12px 40px ${COLORS.shadow}`,
                              overflow: 'hidden'
                            }}
                          >
                            <Box sx={{ p: 2 }}>
                              <Typography variant="caption" sx={{
                                color: COLORS.neutral,
                                fontWeight: 600,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                mb: 1
                              }}>
                                <AutoAwesome sx={{ fontSize: 14 }} />
                                Suggestions
                              </Typography>
                              <Stack spacing={0.5}>
                                {searchSuggestions.map((suggestion) => (
                                  <Box
                                    key={suggestion}
                                    onClick={() => {
                                      setSearchTerm(suggestion);
                                      setIsSearchFocused(false);
                                      addToSearchHistory(suggestion);
                                      setSearchSuggestions([]);
                                    }}
                                    sx={{
                                      p: 1.5,
                                      borderRadius: 2,
                                      cursor: 'pointer',
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 1,
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        bgcolor: `${COLORS.primary}08`,
                                        color: COLORS.primary,
                                        transform: 'translateX(4px)'
                                      }
                                    }}
                                  >
                                    <Search sx={{ fontSize: 16, opacity: 0.6 }} />
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                      {suggestion}
                                    </Typography>
                                  </Box>
                                ))}
                              </Stack>

                              {/* Historique de recherche */}
                              {recentSearches.length > 0 && (
                                <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${COLORS.borderColor}` }}>
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      fontWeight: 600,
                                      color: COLORS.neutral,
                                      textTransform: 'uppercase',
                                      letterSpacing: 0.5,
                                      mb: 1,
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 0.5
                                    }}
                                  >
                                    <History sx={{ fontSize: 14 }} />
                                    Recherches récentes
                                  </Typography>
                                  <Stack spacing={0.5}>
                                    {recentSearches.map((search) => (
                                      <Box
                                        key={search}
                                        onClick={() => {
                                          setSearchTerm(search);
                                          setIsSearchFocused(false);
                                          addToSearchHistory(search);
                                          setSearchSuggestions([]);
                                        }}
                                        sx={{
                                          p: 1.5,
                                          borderRadius: 2,
                                          cursor: 'pointer',
                                          display: 'flex',
                                          alignItems: 'center',
                                          gap: 1,
                                          transition: 'all 0.2s ease',
                                          '&:hover': {
                                            bgcolor: `${COLORS.secondary}08`,
                                            color: COLORS.secondary,
                                            transform: 'translateX(4px)'
                                          }
                                        }}
                                      >
                                        <History sx={{ fontSize: 16, opacity: 0.6 }} />
                                        <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                                          {search}
                                        </Typography>
                                      </Box>
                                    ))}
                                  </Stack>
                                </Box>
                              )}
                            </Box>
                          </Paper>
                        </Box>
                      </Fade>
                    )}
                  </Box>

                  {/* Message d'erreur de validation */}
                  {searchError && (
                    <Box sx={{ mt: 2 }}>
                      <Alert
                        severity="warning"
                        sx={{
                          borderRadius: 3,
                          '& .MuiAlert-message': {
                            width: '100%'
                          }
                        }}
                        action={
                          suggestedTerm && (
                            <Button
                              color="inherit"
                              size="small"
                              onClick={() => {
                                setSearchTerm(suggestedTerm);
                                setSearchError('');
                                setSuggestedTerm('');
                                addToSearchHistory(suggestedTerm);
                              }}
                              sx={{
                                fontWeight: 600,
                                '&:hover': {
                                  bgcolor: 'rgba(255, 255, 255, 0.2)'
                                }
                              }}
                            >
                              Utiliser "{suggestedTerm}"
                            </Button>
                          )
                        }
                      >
                        {searchError}
                      </Alert>
                    </Box>
                  )}
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <CitySearchInput
                    value={selectedCity}
                    onChange={setSelectedCity}
                    placeholder="Ville..."
                    onValidationChange={(isValid) => {
                      if (!isValid) {
                        setCityValidationError('Ville non trouvée. Veuillez vérifier l\'orthographe.');
                      } else {
                        setCityValidationError('');
                      }
                    }}
                    error={cityValidationError}
                    size="medium"
                  />

                  {/* Message d'erreur de validation de ville */}
                  {cityValidationError && (
                    <Box sx={{ mt: 1 }}>
                      <Alert
                        severity="warning"
                        sx={{
                          borderRadius: 2,
                          fontSize: '0.85rem',
                          py: 0.5
                        }}
                      >
                        {cityValidationError}
                      </Alert>
                    </Box>
                  )}
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <TextField
                    fullWidth
                    select
                    label="Catégorie"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="">Toutes les catégories</MenuItem>
                    {SERVICE_CATEGORIES
                      .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                      .map((category) => (
                        <MenuItem key={category.id} value={category.id}>
                          {category.nom}
                        </MenuItem>
                      ))}
                  </TextField>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <TextField
                    fullWidth
                    select
                    label="Sous-catégorie"
                    value={selectedSubcategory}
                    onChange={(e) => setSelectedSubcategory(e.target.value)}
                    disabled={!selectedCategory}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="">Toutes les sous-catégories</MenuItem>
                    {selectedCategory && getSubcategoriesForCategory(selectedCategory)
                      .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                      .map((subcategory) => (
                        <MenuItem key={subcategory.id} value={subcategory.id}>
                          {subcategory.nom}
                        </MenuItem>
                      ))}
                  </TextField>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <TextField
                    fullWidth
                    select
                    label="Budget horaire"
                    value={budgetRange}
                    onChange={(e) => setBudgetRange(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="">Tous les budgets</MenuItem>
                    <MenuItem value="0-15">0€ - 15€/h</MenuItem>
                    <MenuItem value="15-25">15€ - 25€/h</MenuItem>
                    <MenuItem value="25-35">25€ - 35€/h</MenuItem>
                    <MenuItem value="35-50">35€ - 50€/h</MenuItem>
                    <MenuItem value="50+">50€+/h</MenuItem>
                  </TextField>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <TextField
                    fullWidth
                    select
                    label="Trier par"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="relevance">Pertinence</MenuItem>
                    <MenuItem value="price_asc">Prix croissant</MenuItem>
                    <MenuItem value="price_desc">Prix décroissant</MenuItem>
                    <MenuItem value="name">Nom A-Z</MenuItem>
                  </TextField>
                </Grid>
              </Grid>

              {/* Boutons d'action */}
              <Stack direction={isMobile ? "column" : "row"} spacing={2}>
                <Button
                  variant="contained"
                  onClick={handleFilterChange}
                  sx={{
                    background: COLORS.gradient.primary,
                    borderRadius: 4,
                    px: 4,
                    py: 1.5,
                    fontWeight: 600,
                    boxShadow: `0 4px 15px ${COLORS.primary}30`,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 6px 20px ${COLORS.primary}40`
                    }
                  }}
                >
                  Appliquer les filtres
                </Button>

                <Button
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCity('');
                    setSelectedCategory('');
                    setSelectedSubcategory('');
                    setBudgetRange('');
                    setSearchParams(new URLSearchParams());
                  }}
                  sx={{
                    borderColor: COLORS.primary,
                    color: COLORS.primary,
                    borderRadius: 4,
                    px: 4,
                    py: 1.5,
                    fontWeight: 600,
                    '&:hover': {
                      borderColor: COLORS.secondary,
                      color: COLORS.secondary,
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  Réinitialiser
                </Button>
              </Stack>
            </Stack>
          </Paper>
        </Fade>

        {/* Section des résultats */}
        <Fade in={isPageLoaded} timeout={1200}>
          <Box sx={{ mb: 6 }}>
            {filteredProviders.length === 0 ? (
              <Box textAlign="center" py={8}>
                <SearchOff sx={{ fontSize: 80, color: COLORS.neutral, mb: 3 }} />
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: COLORS.primary,
                    mb: 2
                  }}
                >
                  Aucun résultat trouvé
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4, fontSize: '1.1rem' }}>
                  Essayez de modifier vos critères de recherche ou explorez nos catégories de services.
                </Typography>
                <Stack direction={isMobile ? "column" : "row"} spacing={2} justifyContent="center">
                  <Button
                    variant="contained"
                    onClick={() => navigate('/services')}
                    sx={{
                      background: COLORS.gradient.primary,
                      borderRadius: 4,
                      px: 4,
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      boxShadow: `0 8px 25px ${COLORS.primary}30`,
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: `0 12px 35px ${COLORS.primary}40`
                      }
                    }}
                  >
                    Voir tous les services
                  </Button>

                  <Button
                    variant="outlined"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCity('');
                      setSelectedCategory('');
                      setSelectedSubcategory('');
                      setBudgetRange('');
                      setSearchParams(new URLSearchParams());
                    }}
                    sx={{
                      borderColor: COLORS.primary,
                      color: COLORS.primary,
                      borderRadius: 4,
                      px: 4,
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      '&:hover': {
                        borderColor: COLORS.secondary,
                        color: COLORS.secondary,
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    Réinitialiser la recherche
                  </Button>
                </Stack>
              </Box>
            ) : (
              <>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: COLORS.primary,
                    mb: 4,
                    textAlign: 'center',
                    fontSize: { xs: '1.8rem', md: '2.2rem' }
                  }}
                >
                  {filteredProviders.length} Jobbeur{filteredProviders.length > 1 ? 's' : ''} trouvé{filteredProviders.length > 1 ? 's' : ''}
                </Typography>

                {/* Message pour échantillon aléatoire */}
                {providers.length > 0 && (providers as any)[0]?.isRandomSample && (
                  <Alert
                    severity="info"
                    sx={{
                      mb: 4,
                      borderRadius: 3,
                      bgcolor: `${COLORS.primary}10`,
                      border: `1px solid ${COLORS.primary}30`
                    }}
                  >
                    <Typography variant="body2">
                      <strong>Recherche trop générale :</strong> Voici une sélection aléatoire de jobbeurs disponibles.
                      Précisez votre recherche (service + ville) pour des résultats plus ciblés.
                    </Typography>
                  </Alert>
                )}

                <Grid container spacing={4}>
                  {filteredProviders.map((provider) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={provider.user_id}>
                      <Grow in={isPageLoaded} timeout={800 + Math.random() * 400}>
                        <Card
                          sx={{
                            height: '100%',
                                                        borderRadius: 5,
                            overflow: 'hidden',
                            background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                            border: `3px solid ${COLORS.borderColor}`,
                            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                            cursor: 'pointer',
                            position: 'relative',
                            display: 'flex',
                            flexDirection: 'column',
                            '&:hover': {
                              transform: 'translateY(-8px) scale(1.02)',
                              borderColor: COLORS.primary,
                              boxShadow: `0 20px 60px ${COLORS.primary}25`,
                              '& .provider-avatar': {
                                transform: 'scale(1.1)',
                                boxShadow: `0 8px 25px ${COLORS.primary}40`
                              },
                              '& .provider-actions': {
                                opacity: 1,
                                transform: 'translateY(0)'
                              }
                            }
                          }}
                          onClick={() => window.open(`/profil/${provider.slug}`, '_blank')}
                        >
                          {/* Badge de vérification */}
                          {provider.users[0]?.profil_verifier && (
                            <Box
                              sx={{
                                position: 'absolute',
                                top: 16,
                                right: 16,
                                zIndex: 2,
                                bgcolor: COLORS.success,
                                color: COLORS.white,
                                borderRadius: '50%',
                                width: 40,
                                height: 40,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                boxShadow: `0 4px 15px ${COLORS.success}40`
                              }}
                            >
                              <Verified fontSize="small" />
                            </Box>
                          )}

                          {/* Actions flottantes */}
                          <Box
                            className="provider-actions"
                            sx={{
                              position: 'absolute',
                              top: 16,
                              left: 16,
                              zIndex: 2,
                              opacity: 0,
                              transform: 'translateY(-10px)',
                              transition: 'all 0.3s ease',
                              display: 'flex',
                              gap: 1
                            }}
                          >
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleFavorite(provider.user_id);
                              }}
                              sx={{
                                bgcolor: COLORS.white,
                                color: favorites.has(provider.user_id) ? COLORS.error : COLORS.neutral,
                                boxShadow: `0 4px 15px ${COLORS.shadow}`,
                                '&:hover': {
                                  bgcolor: COLORS.white,
                                  transform: 'scale(1.1)'
                                }
                              }}
                            >
                              {favorites.has(provider.user_id) ? <Favorite fontSize="small" /> : <FavoriteBorder fontSize="small" />}
                            </IconButton>

                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleShare(provider);
                              }}
                              sx={{
                                bgcolor: COLORS.white,
                                color: COLORS.primary,
                                boxShadow: `0 4px 15px ${COLORS.shadow}`,
                                '&:hover': {
                                  bgcolor: COLORS.white,
                                  transform: 'scale(1.1)'
                                }
                              }}
                            >
                              <Share fontSize="small" />
                            </IconButton>
                          </Box>

                          {/* Swiper de photos */}
                          <PhotoSwiper provider={provider} />

                          <CardContent sx={{ padding: '24px!important', flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                            {/* Nom et localisation */}
                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="h6"
                                sx={{
                                  fontWeight: 700,
                                  color: COLORS.primary,
                                  mb: 0.5,
                                  fontSize: '1.2rem'
                                }}
                              >
                                {provider.prenom} {provider.nom.charAt(0)}.
                              </Typography>

                              <Box display="flex" alignItems="center" gap={0.5} sx={{ mb: 1 }}>
                                <LocationOn sx={{ fontSize: 16, color: COLORS.neutral }} />
                                <Typography variant="body2" color="text.secondary">
                                  {provider.ville} ({provider.code_postal})
                                </Typography>
                              </Box>

                              {/* Avis et note */}
                              {provider.users[0]?.user_reviews && provider.users[0].user_reviews.length > 0 && (
                                <Box display="flex" alignItems="center" gap={1} sx={{ mb: 1 }}>
                                  <Box display="flex" alignItems="center" gap={0.5}>
                                    {[...Array(5)].map((_, index) => {
                                      const reviews = provider.users[0].user_reviews!;
                                      const avgRating = reviews.reduce((acc: number, review: any) => acc + review.note, 0) / reviews.length;
                                      return (
                                        <Star
                                          key={index}
                                          sx={{
                                            fontSize: 16,
                                            color: index < Math.round(avgRating) ? COLORS.warning : COLORS.lightGray
                                          }}
                                        />
                                      );
                                    })}
                                  </Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600, color: COLORS.primary }}>
                                    {(() => {
                                      const reviews = provider.users[0].user_reviews!;
                                      const avgRating = reviews.reduce((acc: number, review: any) => acc + review.note, 0) / reviews.length;
                                      return avgRating.toFixed(1);
                                    })()}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    ({provider.users[0].user_reviews.length} avis)
                                  </Typography>
                                </Box>
                              )}
                            </Box>

                            {/* Slogan */}
                            {provider.slogan && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontStyle: 'italic',
                                    color: COLORS.neutral,
                                  mb: 2,
                                    fontSize: '0.9rem',
                                  lineHeight: 1.4
                                  }}
                                >
                                  "{provider.slogan}"
                                </Typography>
                              )}

                            {/* Services */}
                            <Box sx={{ mb: 2, flexGrow: 1 }}>
                              <Typography variant="body2" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                                Services proposés :
                              </Typography>
                              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                                {provider.users[0]?.user_services?.slice(0, 2).map((service, index) => (
                                  <Chip
                                    key={index}
                                    label={getSubcategoryName(service.subcategory_id)}
                                    size="small"
                                    sx={{
                                      bgcolor: `${COLORS.primary}15`,
                                      color: COLORS.primary,
                                      fontWeight: 600,
                                      fontSize: '0.75rem',
                                      '&:hover': {
                                        bgcolor: `${COLORS.primary}25`
                                      }
                                    }}
                                  />
                                ))}
                                {(provider.users[0]?.user_services?.length || 0) > 2 && (
                                  <Chip
                                    label={`+${(provider.users[0]?.user_services?.length || 0) - 2}`}
                                    size="small"
                                    sx={{
                                      bgcolor: `${COLORS.secondary}15`,
                                      color: COLORS.secondary,
                                      fontWeight: 600,
                                      fontSize: '0.75rem'
                                    }}
                                  />
                                )}
                              </Stack>
                            </Box>

                            {/* Prix et CTA */}
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                pt: 2,
                                borderTop: `2px solid ${COLORS.borderColor}`
                              }}
                            >
                              <Box>
                                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                                  À partir de
                                </Typography>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontWeight: 800,
                                    color: COLORS.primary,
                                    fontSize: '1.1rem'
                                  }}
                                >
                                  {Math.min(...(provider.users[0]?.user_services?.map((s: any) => s.tarif_horaire) || [25]))}€/h
                                </Typography>
                              </Box>

                              <Button
                                variant="contained"
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent the card's onClick from triggering
                                  window.open(`/profil/${provider.slug}`, '_blank');
                                }}
                                sx={{
                                  background: COLORS.gradient.primary,
                                  borderRadius: 3,
                                  px: 3,
                                  py: 1,
                                  fontWeight: 600,
                                  fontSize: '0.85rem',
                                  boxShadow: `0 4px 15px ${COLORS.primary}30`,
                                  '&:hover': {
                                    transform: 'translateY(-2px)',
                                    boxShadow: `0 6px 20px ${COLORS.primary}40`
                                  }
                                }}
                              >
                                Contacter
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grow>
                    </Grid>
                  ))}
                </Grid>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <Box display="flex" justifyContent="center" mt={6}>
                    <Pagination
                      count={pagination.totalPages}
                      page={pagination.page}
                      onChange={handlePageChange}
                      color="primary"
                      size="large"
                      sx={{
                        '& .MuiPaginationItem-root': {
                          borderRadius: 3,
                          fontWeight: 600,
                          '&.Mui-selected': {
                            background: COLORS.gradient.primary,
                            color: COLORS.white,
                            '&:hover': {
                              background: COLORS.gradient.primary
                            }
                          }
                        }
                      }}
                    />
                  </Box>
                )}
              </>
            )}
          </Box>
        </Fade>

        {/* Section SEO dynamique avec données réelles */}
        {filteredProviders.length > 0 && (
          <Fade in={isPageLoaded} timeout={1400}>
            <Box sx={{ mt: 8, mb: 6 }}>
              <Divider sx={{ mb: 6, borderColor: COLORS.borderColor }} />

              {/* En-tête de section moderne */}
              <Box textAlign="center" sx={{ mb: 6 }}>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: COLORS.primary,
                    mb: 2,
                    fontSize: { xs: '2rem', md: '2.5rem' },
                    position: 'relative'
                  }}
                >
                  Explorez plus de services
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: -8,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: 120,
                      height: 4,
                      background: COLORS.gradient.primary,
                      borderRadius: 2
                    }}
                  />
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    color: COLORS.neutral,
                    fontWeight: 500,
                    maxWidth: '600px',
                    mx: 'auto'
                  }}
                >
                  Découvrez d'autres jobbeurs et services dans votre région
                </Typography>
              </Box>

              <Grid container spacing={4}>
                {/* Villes proches dynamiques */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      height: '100%',
                      background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.primary}05 100%)`,
                      border: `3px solid ${COLORS.primary}20`,
                      borderRadius: 5,
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        borderColor: COLORS.primary,
                        boxShadow: `0 12px 40px ${COLORS.primary}20`,
                        transform: 'translateY(-4px)'
                      }
                    }}
                  >
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h5" sx={{
                        fontWeight: 700,
                        color: COLORS.primary,
                        mb: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }}>
                        <LocationOn />
                        {searchTerm ? `${searchTerm} dans les villes proches` : 'Jobbeurs dans les villes proches'}
                      </Typography>
                      <Typography variant="body2" sx={{ color: COLORS.neutral, fontWeight: 500 }}>
                        Trouvez des professionnels dans votre région
                      </Typography>
                    </Box>
                    
                    <Grid container spacing={2}>
                      {Array.from(new Set(filteredProviders.map(p => p.ville)))
                        .filter(ville => ville !== selectedCity)
                        .slice(0, 6)
                        .map((ville) => (
                          <Grid size={{ xs: 6, sm: 4 }} key={ville}>
                            <Paper
                              elevation={0}
                              onClick={() => {
                                setSelectedCity(ville);
                                const params = new URLSearchParams();
                                if (searchTerm) params.append('q', searchTerm);
                                params.append('city', ville);
                                if (selectedCategory) {
                                  const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory);
                                  if (categoryObj) params.append('category', categoryObj.nom);
                                }
                                if (selectedSubcategory) {
                                  const subcategoryObj = SERVICE_SUBCATEGORIES.find(sub => sub.id === selectedSubcategory);
                                  if (subcategoryObj) params.append('subcategory', subcategoryObj.nom);
                                }
                                if (budgetRange) params.append('budget', budgetRange);
                                setSearchParams(params);
                              }}
                              sx={{
                                p: 2,
                                textAlign: 'center',
                                cursor: 'pointer',
                                bgcolor: COLORS.white,
                                border: `2px solid ${COLORS.borderColor}`,
                                borderRadius: 3,
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  borderColor: COLORS.primary,
                                  bgcolor: `${COLORS.primary}08`,
                                  transform: 'translateY(-2px)',
                                  boxShadow: `0 8px 25px ${COLORS.primary}20`
                                }
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 600,
                                  color: COLORS.primary,
                                  fontSize: '0.9rem'
                                }}
                              >
                                {searchTerm ? `${searchTerm}` : 'Services'}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  color: COLORS.neutral,
                                  display: 'block',
                                  mt: 0.5
                                }}
                              >
                                {ville}
                              </Typography>
                            </Paper>
                          </Grid>
                        ))}
                    </Grid>
                  </Paper>
                </Grid>

                {/* Services similaires dynamiques */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      height: '100%',
                      background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.secondary}05 100%)`,
                      border: `3px solid ${COLORS.secondary}20`,
                      borderRadius: 5,
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        borderColor: COLORS.secondary,
                        boxShadow: `0 12px 40px ${COLORS.secondary}20`,
                        transform: 'translateY(-4px)'
                      }
                    }}
                  >
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h5" sx={{
                        fontWeight: 700,
                        color: COLORS.secondary,
                        mb: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        flexDirection: { xs: 'column', sm: 'row' },
                        textAlign: { xs: 'center', sm: 'left' }
                      }}>
                        <TrendingUp sx={{ fontSize: { xs: '2rem', sm: '1.5rem' } }} />
                        <Box component="span" sx={{ fontSize: { xs: '1.2rem', sm: '1.25rem' } }}>
                          Services similaires {selectedCity ? `à ${selectedCity}` : ''}
                        </Box>
                      </Typography>
                      <Typography variant="body2" sx={{ 
                        color: COLORS.neutral, 
                        fontWeight: 500,
                        textAlign: { xs: 'center', sm: 'left' }
                      }}>
                        Découvrez d'autres services populaires
                      </Typography>
                    </Box>
                    
                    <Grid container spacing={2}>
                      {SERVICE_SUBCATEGORIES
                        .filter(sub => {
                          if (selectedSubcategory && sub.id === selectedSubcategory) return false;
                          if (selectedCategory) return sub.categoryId === selectedCategory;
                          return ['1-1', '2-1', '3-1', '4-1', '5-1', '6-1'].includes(sub.id);
                        })
                        .slice(0, 6)
                        .map((subcategory) => (
                          <Grid size={{ xs: 6, sm: 4 }} key={subcategory.id}>
                            <Paper
                              elevation={0}
                              onClick={() => {
                                setSearchTerm('');
                                setSelectedSubcategory(subcategory.id);
                                setSelectedCategory(subcategory.categoryId);
                                const params = new URLSearchParams();
                                if (selectedCity) params.append('city', selectedCity);
                                const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId);
                                if (categoryObj) params.append('category', categoryObj.nom);
                                params.append('subcategory', subcategory.nom);
                                if (budgetRange) params.append('budget', budgetRange);
                                setSearchParams(params);
                              }}
                              sx={{
                                p: 2,
                                textAlign: 'center',
                                cursor: 'pointer',
                                bgcolor: COLORS.white,
                                border: `2px solid ${COLORS.borderColor}`,
                                borderRadius: 3,
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  borderColor: COLORS.secondary,
                                  bgcolor: `${COLORS.secondary}08`,
                                  transform: 'translateY(-2px)',
                                  boxShadow: `0 8px 25px ${COLORS.secondary}20`
                                }
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 600,
                                  color: COLORS.secondary,
                                  fontSize: '0.85rem',
                                  lineHeight: 1.3
                                }}
                              >
                                {subcategory.nom}
                              </Typography>
                            </Paper>
                          </Grid>
                        ))}
                    </Grid>
                  </Paper>
                </Grid>
              </Grid>

              {/* Autres services populaires - Design moderne */}
              <Box sx={{ mt: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.tertiary}05 100%)`,
                    border: `3px solid ${COLORS.tertiary}20`,
                    borderRadius: 5,
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      borderColor: COLORS.tertiary,
                      boxShadow: `0 12px 40px ${COLORS.tertiary}20`,
                      transform: 'translateY(-4px)'
                    }
                  }}
                >
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h5" sx={{
                      fontWeight: 700,
                      color: COLORS.tertiary,
                      mb: 1,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      flexDirection: { xs: 'column', sm: 'row' },
                      textAlign: { xs: 'center', sm: 'left' }
                    }}>
                      <AutoAwesome sx={{ fontSize: { xs: '2rem', sm: '1.5rem' } }} />
                      <Box component="span" sx={{ fontSize: { xs: '1.2rem', sm: '1.25rem' } }}>
                        Autres services populaires
                      </Box>
                    </Typography>
                    <Typography variant="body2" sx={{ 
                      color: COLORS.neutral, 
                      fontWeight: 500,
                      textAlign: { xs: 'center', sm: 'left' }
                    }}>
                      Les services les plus demandés sur JobPartiel
                    </Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    {SERVICE_SUBCATEGORIES
                      .filter(sub => ['1-1', '1-2', '2-1', '2-2', '3-1', '3-2', '4-1', '4-2', '5-1', '5-2'].includes(sub.id))
                      .map((subcategory) => (
                        <Grid size={{ xs: 6, sm: 4, md: 3, lg: 2.4 }} key={subcategory.id}>
                          <Paper
                            elevation={0}
                            onClick={() => {
                              setSearchTerm('');
                              setSelectedSubcategory(subcategory.id);
                              setSelectedCategory(subcategory.categoryId);
                              const params = new URLSearchParams();
                              if (selectedCity) params.append('city', selectedCity);
                              const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId);
                              if (categoryObj) params.append('category', categoryObj.nom);
                              params.append('subcategory', subcategory.nom);
                              if (budgetRange) params.append('budget', budgetRange);
                              setSearchParams(params);
                            }}
                            sx={{
                              p: 2,
                              textAlign: 'center',
                              cursor: 'pointer',
                              bgcolor: COLORS.white,
                              border: `2px solid ${COLORS.borderColor}`,
                              borderRadius: 3,
                              transition: 'all 0.3s ease',
                              '&:hover': {
                                borderColor: COLORS.tertiary,
                                bgcolor: `${COLORS.tertiary}08`,
                                transform: 'translateY(-2px)',
                                boxShadow: `0 8px 25px ${COLORS.tertiary}20`
                              }
                            }}
                          >
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 600,
                                color: COLORS.tertiary,
                                fontSize: '0.8rem',
                                lineHeight: 1.3
                              }}
                            >
                              {subcategory.nom}
                            </Typography>
                          </Paper>
                        </Grid>
                      ))}
                  </Grid>
                </Paper>
              </Box>
            </Box>
          </Fade>
        )}
      </Container>

      {/* Composant de carte */}
      <SearchResultsMap
        providers={filteredProviders}
        onProviderClick={handleProviderClick}
        isVisible={showMap}
        onClose={() => setShowMap(false)}
      />
    </Box>
    </>
  );
};

export default SearchResultsPage;
