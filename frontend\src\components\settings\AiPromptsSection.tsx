import React, { useState, useEffect, useRef } from 'react';
import { Save, Info, RefreshCw, X, Sparkles, AlertTriangle, Clock } from 'lucide-react';
import { useAiGeneration, GenerationType } from '../../hooks/useAiGeneration';
import { notify } from '../../components/Notification';
import { CircularProgress, Tooltip, Button } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import logger from '../../utils/logger';
import { badges as badgesList } from '../../pages/dashboard/profil/badges';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../../pages/dashboard/services/types';
import { useAiCredits } from '../../hooks/useAiCredits';
import AiGenerationSystem from '../../components/ai/AiGenerationSystem';

const promptTypes = [
  {
    id: 'default_prompt',
    name: 'Prompt par défaut',
    description: 'Votre prompt personnalisé par défaut qui sera utilisé sur toute la plateforme.',
    defaultPrompt: `Voici des informations sur moi afin de créer du contenu pour JobPartiel.

    Voici mes infos :
    - Je m'appelle {{prenom}} {{nom}}
    - Je suis {{metier}}
    - J'habite à {{ville}}
    - Mes compétences : {{competences}}
    - Mon expérience : {{experience}}

    Je veux que tu écrives avec :
    1. Un ton amical et professionnel
    2. Des phrases simples et claires
    3. Une touche personnelle qui me ressemble
    4. Mes points forts mis en avant naturellement

    Utilise ces informations pour créer un texte qui donne envie aux gens de me contacter !`
  },
  {
    id: 'biography',
    name: 'Génération de biographie',
    description: 'Personnalisez le prompt utilisé pour générer votre biographie professionnelle.',
    defaultPrompt: `Peux-tu écrire une courte biographie pour mon profil JobPartiel ? (maximum 900 caractères)

    Voici mes infos :
    - Je m'appelle {{prenom}} {{nom}}
    - Je suis {{metier}}
    - J'habite à {{ville}}
    - Mes compétences : {{competences}}
    - Mon expérience : {{experience}}
    - Mes badges : {{badges}}
    - Ma note : {{note_moyenne}}/5 ({{nombre_avis}} avis)

    J'aimerais que :
    - Le texte soit professionnel mais sympathique
    - Tu mettes en avant mes points forts
    - Tu donnes envie aux gens de me contacter
    - Tu utilises un langage simple et direct`
  },
  {
    id: 'service_description',
    name: 'Description de service',
    description: 'Personnalisez le prompt utilisé pour générer des descriptions de services.',
    defaultPrompt: `Peux-tu décrire mon service de manière attractive ?

    Mon service :
    - Type : {{type_service}}
    - Catégorie : {{categorie}}
    - Titre : {{titre}}
    - Tarif : {{tarif}}
    - Équipement : {{equipement}}

    Mes infos :
    - Je m'appelle {{prenom}} {{nom}}
    - Mon expérience : {{experience}}
    - Ma note : {{note_moyenne}}/5 ({{nombre_avis}} avis)

    J'aimerais :
    - Une description claire et accrocheuse
    - Mettre en avant la qualité de mon service
    - Expliquer simplement ce que je propose
    - Donner envie de me contacter`
  },
  {
    id: 'mission_post',
    name: 'Description de mission',
    description: 'Personnalisez le prompt utilisé pour créer des descriptions de missions.',
    defaultPrompt: `Peux-tu m'aider à décrire ma mission pour attirer les bons jobbeurs ?

    Ma mission :
    - Titre : {{titre_mission}}
    - Type : {{categorie}}
    - Lieu : {{ville}}
    - Budget : {{budget}}
    - Date souhaitée : {{date_mission}}
    - Durée estimée : {{duree_estimee}}

    Je voudrais :
    - Expliquer clairement ce que je recherche
    - Préciser les compétences nécessaires
    - Donner toutes les infos pratiques
    - Rester simple et direct`
  },
  {
    id: 'review_response',
    name: 'Réponse aux avis',
    description: 'Personnalisez le prompt utilisé pour générer des réponses aux avis clients.',
    defaultPrompt: `Aide-moi à répondre à cet avis client simplement et gentiment.

    Voici l'avis :
    - Note : {{note}}/5
    - Commentaire : {{commentaire}}
    - Service : {{service_concerne}}

    Je veux :
    - Dire merci pour l'avis
    - Être sympa et positif
    - Montrer que j'écoute le client
    - Utiliser des phrases courtes et simples
    - Rester naturel, comme si je parlais à un ami
    - Maximum 3-4 phrases au total`
  },
  {
    id: 'mission_offer',
    name: 'Offre de mission',
    description: 'Personnalisez le prompt utilisé pour créer des offres de mission convaincantes.',
    defaultPrompt: `Peux-tu m'aider à répondre à cette mission de manière convaincante ?

    La mission :
    - Titre : {{titre_mission}}
    - Description : {{description_mission}}
    - Budget : {{budget_mission}}
    - Lieu : {{ville_mission}}
    - Date : {{date_mission}}

    Mes atouts :
    - Expérience : {{experience}}
    - Compétences : {{competences}}
    - Note moyenne : {{note_moyenne}}/5
    - Badges : {{badges}}

    Je voudrais :
    - Montrer que je comprends bien le besoin
    - Mettre en avant mes compétences pertinentes
    - Expliquer comment je peux aider
    - Donner envie de me choisir

    Répondre dans ce format :
    - Format SANS HTML : N'utilise aucunes balises html comme <p>, <strong>, <em>. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, mais seulement du texte brut.`
  },
  {
    id: 'comment',
    name: 'Commentaires',
    description: 'Personnalisez le prompt utilisé pour générer des commentaires sur les missions.',
    defaultPrompt: `Aide-moi à rédiger un commentaire pertinent et constructif pour cette mission de jobbing sur JobPartiel.

    La mission :
    - Titre : {{titre_mission}}
    - Description : {{description_mission}}
    - Catégorie : {{categorie_mission}}
    - Lieu : {{ville_mission}}

    Mon profil :
    - Je m'appelle {{prenom}}
    - Mes compétences : {{competences}}
    - Mon expérience : {{experience}}
    - Ma note moyenne : {{note_moyenne}}/5

    Je voudrais :
    - Un commentaire court et précis (maximum 150 caractères)
    - Poser une question pertinente ou faire une remarque constructive
    - Rester professionnel et courtois
    - Montrer mon intérêt pour la mission
    - Terminer toujours la phrase par un reste de phrase interrogative

    Format : Texte simple sans HTML, sans markdown, sans listes à puces.`
  }
];

interface BadgeInfo {
  id: string;
  title: string;
  description: string;
}

interface ServiceInfo {
  type_service: string;
  categorie: string;
  sous_categorie: string;
  titre: string;
  tarif: number;
  equipement: string;
  tarif_horaire: number;
}

interface UserProfileData {
  nom?: string;
  prenom?: string;
  metier?: string;
  experience?: string;
  competences?: string;
  ville?: string;
  code_postal?: string;
  disponibilite?: string;
  badges?: BadgeInfo[];
  note_moyenne?: number;
  nombre_avis?: number;
  services?: ServiceInfo[];
  type_service?: string;
  categorie?: string;
  sous_categorie?: string;
  titre?: string;
  tarif?: number;
  equipement?: string;
  tarif_habituel?: number;
  bio?: string;
}

// Fonction pour remplir le template du prompt avec les données utilisateur
const fillPromptWithData = (template: string, data: UserProfileData): string => {
  let filled = template;
  Object.entries(data).forEach(([key, value]) => {
    if (key === 'badges' && Array.isArray(value) && value.length > 0) {
      const badgesValue = value as BadgeInfo[];
      const badgesText = badgesValue.map((badge: BadgeInfo) =>
        `${badge.title} (${badge.description})`
      ).join(', ');
      filled = filled.replace(new RegExp(`{{${key}}}`, 'g'), badgesText || `[${key} non spécifié]`);
    } else if (Array.isArray(value)) {
      filled = filled.replace(new RegExp(`{{${key}}}`, 'g'), value.join(', ') || `[${key} non spécifié]`);
    } else if (value !== undefined && value !== null) {
      filled = filled.replace(new RegExp(`{{${key}}}`, 'g'), String(value) || `[${key} non spécifié]`);
    } else {
      filled = filled.replace(new RegExp(`{{${key}}}`, 'g'), `[${key} non spécifié]`);
    }
  });
  return filled;
};

// Fonction pour supprimer les balises HTML
const cleanHtmlTags = (text: string): string => {
  if (!text) return '';
  return text.replace(/<[^>]*>/g, '');
};

const AiPromptsSection: React.FC = () => {
  const { prompts, loading, saveUserPrompt, deleteUserPrompt, generating } = useAiGeneration();
  const { isAuthenticated } = useAuth();
  const { credits, isRateLimited, refetch } = useAiCredits();
  const [selectedType, setSelectedType] = useState<GenerationType>('default_prompt');
  const [promptText, setPromptText] = useState<string>('');
  const [saving, setSaving] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<UserProfileData>({});
  const [loadingProfile, setLoadingProfile] = useState<boolean>(false);
  const [previewPrompt, setPreviewPrompt] = useState<string>('');
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const previewSectionRef = useRef<HTMLDivElement>(null);
  const [isHighlighted, setIsHighlighted] = useState(false);

  // Référence pour stocker temporairement le contenu généré
  const tempContentRef = useRef<string | null>(null);

  // Récupérer les données du profil utilisateur
  const fetchUserProfile = async () => {
    if (!isAuthenticated) return;

    try {
      setLoadingProfile(true);
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil`, {
        headers,
        withCredentials: true
      });

      if (response.data && response.data.profil && response.data.profil.data) {
        const userData = response.data;
        const userProfile = userData.profil.data;

        // Récupérer les badges
        let userBadges: BadgeInfo[] = [];
        try {
          // Récupérer les badges de l'utilisateur
          const badgesResponse = await axios.get(`${API_CONFIG.baseURL}/api/user-badges/by-profile/${userData.id}`, {
            headers,
            withCredentials: true
          });
          const userBadgesData = badgesResponse.data?.data || [];

          // Log des badges bruts récupérés depuis l'API
          logger.info('Badges bruts récupérés depuis l\'API:', userBadgesData);

          // Enrichir les badges avec leurs informations complètes depuis la liste des badges
          const enrichedBadges: BadgeInfo[] = userBadgesData.map((badge: any) => {
            const badgeId = badge.badge_id;
            // Rechercher le badge dans la liste des badges définie dans ce fichier
            const badgeInfo = badgesList.find((b: any) => b.id === badgeId);

            if (badgeInfo) {
              return {
                id: badgeId,
                title: badgeInfo.title,
                description: badgeInfo.description
              };
            }

            // Si le badge n'est pas trouvé dans notre liste, utiliser un titre générique
            return {
              id: badgeId,
              title: `Badge ${badgeId}`,
              description: 'Description non disponible'
            };
          });

          // Log pour déboguer
          logger.info('Badges enrichis:', enrichedBadges);

          // Mettre à jour la variable userBadges
          userBadges = enrichedBadges;
        } catch (error) {
          logger.warn('Erreur lors de la récupération des badges:', error);
          // Continuer malgré l'erreur
        }

        // Récupérer les avis
        let reviewStats = { average: 0, count: 0 };
        try {
          // Utiliser la route des badges pour récupérer les statistiques d'avis
          const reviewsResponse = await axios.get(`${API_CONFIG.baseURL}/api/user-badges/profile-stats/${userData.id}`, {
            headers,
            withCredentials: true
          });

          if (reviewsResponse.data && reviewsResponse.data.data) {
            reviewStats = {
              average: reviewsResponse.data.data.avg_rating || 0,
              count: reviewsResponse.data.data.reviews_count || 0
            };
          }
        } catch (error) {
          logger.warn('Erreur lors de la récupération des statistiques d\'avis:', error);
          // Continuer malgré l'erreur
        }

        // Récupérer les services
        let services = [];
        try {
          // Récupérer d'abord le slug de l'utilisateur
          const slug = userData.profil.data.slug;

          if (slug) {
            const servicesResponse = await axios.get(`${API_CONFIG.baseURL}/api/services/user/${slug}`, {
              headers,
              withCredentials: true
            });
            services = servicesResponse.data || [];
          } else {
            // Si pas de slug, essayer de récupérer les services via la route protégée
            const servicesResponse = await axios.get(`${API_CONFIG.baseURL}/api/services/user`, {
              headers,
              withCredentials: true
            });
            services = servicesResponse.data || [];
          }

          // Trouver la catégorie et sous-catégorie pour chaque service
          const enrichedServices = services.map((service: { category_id: string; subcategory_id: string; titre: string; tarif_horaire: number; equipement?: string }) => {
            const category = SERVICE_CATEGORIES.find(cat => cat.id === service.category_id);
            const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === service.subcategory_id);
            return {
              ...service,
              type_service: category?.nom || 'Non spécifié',
              categorie: category?.nom || 'Non spécifié',
              sous_categorie: subcategory?.nom || 'Non spécifié',
              titre: service.titre || 'Non spécifié',
              tarif: service.tarif_horaire || 'Non spécifié',
              equipement: service.equipement || 'Non spécifié'
            };
          });

          services = enrichedServices;
        } catch (error) {
          logger.warn('Erreur lors de la récupération des services:', error);
          // Continuer malgré l'erreur
        }

        // Log avant de setter le state
        logger.info('Préparation des données pour setProfileData:', {
          nom: userProfile.nom || '',
          prenom: userProfile.prenom || '',
          metier: userProfile.profession || '',
          experience: userProfile.experience || '',
          competences: userProfile.skills || '',
          ville: userProfile.ville || '',
          code_postal: userProfile.code_postal || '',
          disponibilite: userProfile.availability || '',
          badges: userBadges,
          note_moyenne: reviewStats.average || 0,
          nombre_avis: reviewStats.count || 0,
          services: services,
          tarif_habituel: services.length > 0 ? services[0].tarif_horaire : '',
          bio: userProfile.bio || ''
        });

        setProfileData({
          nom: userProfile.nom || '',
          prenom: userProfile.prenom || '',
          metier: userProfile.profession || '',
          experience: userProfile.experience || '',
          competences: userProfile.skills || '',
          ville: userProfile.ville || '',
          code_postal: userProfile.code_postal || '',
          disponibilite: userProfile.availability || '',
          badges: userBadges,
          note_moyenne: reviewStats.average || 0,
          nombre_avis: reviewStats.count || 0,
          services: services,
          type_service: services.map((s: ServiceInfo) => s.type_service).join(' | '),
          categorie: services.map((s: ServiceInfo) => s.categorie).join(' | '),
          sous_categorie: services.map((s: ServiceInfo) => s.sous_categorie).join(' | '),
          titre: services.map((s: ServiceInfo) => s.titre).join(' | '),
          tarif: services.map((s: ServiceInfo) => `${s.titre}: ${s.tarif_horaire}€/h`).join(' | '),
          equipement: services.map((s: ServiceInfo) => s.equipement).join(' | '),
          tarif_habituel: services.length > 0 ? services[0].tarif_horaire : '',
          bio: userProfile.bio || ''
        });

        // Log détaillé des données du profil pour déboguer
        logger.info('Données du profil récupérées avec succès:', {
          nom: userProfile.nom,
          prenom: userProfile.prenom,
          badges: userBadges
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération des données du profil:', error);
      notify('Erreur lors de la récupération des données du profil. Certaines fonctionnalités peuvent être limitées.', 'error');
    } finally {
      setLoadingProfile(false);
    }
  };

  // Charger les données du profil au montage du composant
  useEffect(() => {
    fetchUserProfile();
  }, [isAuthenticated]);

  // Charger le prompt pour le type sélectionné
  useEffect(() => {
    const selectedPrompt = prompts.find(p => p.type === selectedType);
    if (selectedPrompt) {
      setPromptText(selectedPrompt.prompt);
    } else {
      // Utiliser le prompt par défaut si aucun prompt personnalisé n'existe
      const defaultPrompt = promptTypes.find(p => p.id === selectedType)?.defaultPrompt || '';
      setPromptText(defaultPrompt);
    }
  }, [selectedType, prompts]);

  // Générer l'aperçu du prompt avec les données du profil
  const generatePreview = () => {
    let filledPrompt = fillPromptWithData(promptText, profileData);
    const cleanedFilledPrompt = cleanHtmlTags(filledPrompt);

    setPreviewPrompt(cleanedFilledPrompt);
    setShowPreview(true);
  };

  const handleSavePrompt = async () => {
    let filledPrompt = fillPromptWithData(promptText, profileData);
    const cleanedFilledPrompt = cleanHtmlTags(filledPrompt);

    setPreviewPrompt(cleanedFilledPrompt);
    setShowPreview(true);

    // Vérifier que l'aperçu (nettoyé) a été généré et n'est pas vide
    if (!cleanedFilledPrompt || !cleanedFilledPrompt.trim()) {
      notify('L\'aperçu du prompt ne peut pas être vide. Veuillez générer un aperçu avec vos données.', 'error');
      return;
    }

    setSaving(true);
    try {
      // Enregistrer le prompt rempli et nettoyé
      await saveUserPrompt(selectedType, cleanedFilledPrompt);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    const defaultPrompt = promptTypes.find(p => p.id === selectedType)?.defaultPrompt || '';
    setPromptText(defaultPrompt);
    notify('Prompt réinitialisé à sa valeur par défaut', 'info');

    // Supprimer le prompt personnalisé de la base de données
    await deleteUserPrompt(selectedType);
  };

  const toggleExpand = (id: string) => {
    if (expanded === id) {
      setExpanded(null);
    } else {
      setExpanded(id);
    }
  };

  // Ouvrir la modal de confirmation pour la génération IA
  const handleOpenGenerateModal = () => {
    if (credits <= 0) {
      notify('Vous n\'avez pas assez de crédits IA. Veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
      return;
    }
    // Générer d'abord l'aperçu avec les données de l'utilisateur
    generatePreview();
    // Puis ouvrir la modal de confirmation
    setShowConfirmModal(true);
  };

  // Gestionnaire appelé quand la génération est terminée (modale fermée)
  const handleGenerationComplete = () => {
    // Traiter le contenu généré
    if (tempContentRef.current) {
      const content = tempContentRef.current;

      // Mettre à jour l'aperçu
      setShowPreview(true);
      setPreviewPrompt(content);

      // Activer l'effet de highlight
      setIsHighlighted(true);
      setTimeout(() => setIsHighlighted(false), 4000);

      // Faire défiler vers la section d'aperçu
      requestAnimationFrame(() => {
        setTimeout(() => {
          if (previewSectionRef.current) {
            previewSectionRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        }, 300);
      });

      // Réinitialiser la référence après utilisation
      tempContentRef.current = null;
    }
  };

  // Afficher un message spécifique pour le rate limit
  if (isRateLimited) {
    return (
      <div className="flex flex-col justify-center items-center h-80 bg-white p-6 rounded-xl shadow-md">
        <AlertTriangle className="text-amber-500 mb-4" size={48} />
        <h2 className="text-xl font-semibold mb-2 text-gray-800">Trop de requêtes</h2>
        <p className="text-gray-600 text-center mb-6 max-w-md">
          Vous avez atteint la limite de requêtes autorisées. Veuillez patienter quelques minutes avant de réessayer.
        </p>
        <div className="flex items-center space-x-2">
          <Clock className="text-gray-500" size={20} />
          <span className="text-gray-500">Réessayez dans 1 minute</span>
        </div>
        <Button
          variant="contained"
          onClick={refetch}
          startIcon={<RefreshCw size={16} />}
          sx={{
            mt: 4,
            bgcolor: '#FF6B2C',
            '&:hover': { bgcolor: '#FF7A35' },
            borderRadius: '8px',
            textTransform: 'none',
            fontWeight: 500
          }}
        >
          Réessayer maintenant
        </Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <CircularProgress size={40} thickness={5} sx={{ color: '#FF6B2C' }} />
      </div>
    );
  }

  // Afficher un indicateur de chargement pour les données du profil
  const renderProfileLoadingIndicator = () => {
    if (loadingProfile) {
      return (
        <div className="flex items-center space-x-2 text-sm text-gray-500 mt-2">
          <CircularProgress size={16} thickness={5} sx={{ color: '#FF6B2C' }} />
          <span>Chargement de vos données...</span>
        </div>
      );
    }
    return null;
  };

  return (
    <>
      <style>
        {`
          @keyframes highlightPulse {
            0% {
              box-shadow: 0 0 0 0 rgba(255, 107, 44, 0.4);
              transform: scale(1);
            }
            25% {
              transform: scale(1.01);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(255, 107, 44, 0);
              transform: scale(1.01);
            }
            85% {
              transform: scale(0.99);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(255, 107, 44, 0);
              transform: scale(1);
            }
          }
          .highlight-pulse {
            animation: highlightPulse 2s cubic-bezier(0.4, 0, 0.2, 1);
            border-color: #FF6B2C;
            background-color: #FFF8F3;
            transition: all 0.3s ease;
            transform-origin: center;
            will-change: transform, box-shadow;
          }
          @keyframes gradientSlide {
            0% {
              background-position: 100% 0%;
            }
            100% {
              background-position: 0% 0%;
            }
          }
          .animated-bg {
            background: linear-gradient(90deg, #FFF8F3 0%, #FFDCBA 50%, #FFF8F3 100%);
            background-size: 200% 100%;
            animation: gradientSlide 2s infinite ease-in-out;
          }
        `}
      </style>

      <div className="space-y-6">

        <div className="space-y-4">
          <div>
            <label htmlFor="promptType" className="block text-sm font-medium text-gray-700 mb-1">
              Type de prompt
            </label>
            <select
              id="promptType"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as GenerationType)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent"
            >
              {promptTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <label htmlFor="promptText" className="block text-sm font-medium text-gray-700">
                Prompt personnalisé
              </label>
              <button
                type="button"
                onClick={handleReset}
                className="text-xs text-[#FF6B2C] hover:text-[#FF7A35] transition-colors"
              >
                Réinitialiser
              </button>
            </div>
            <textarea
              id="promptText"
              value={promptText}
              onChange={(e) => setPromptText(e.target.value)}
              rows={8}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent resize-none"
              placeholder="Entrez votre prompt personnalisé ici..."
            />
          </div>

          {renderProfileLoadingIndicator()}

          <div className="flex flex-col sm:flex-row justify-between gap-4 mt-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <button
                type="button"
                onClick={generatePreview}
                disabled={loadingProfile}
                className="flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className="mr-2 h-5 w-5" />
                Aperçu avec mes données
              </button>

              <button
                type="button"
                onClick={handleOpenGenerateModal}
                disabled={loadingProfile || generating}
                className="flex items-center justify-center px-4 py-2 bg-[#FFE4BA] text-[#FF6B2C] rounded-md hover:bg-[#FFD4A0] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {generating ? (
                  <>
                    <CircularProgress size={20} thickness={5} sx={{ color: '#FF6B2C', marginRight: '8px' }} />
                    Génération en cours ...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-5 w-5" />
                    Générer un aperçu par IA
                  </>
                )}
              </button>
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleSavePrompt}
                disabled={saving || !promptText || !promptText.trim()}
                className="flex items-center justify-center px-4 py-2 bg-[#FFE4BA] text-[#FF6B2C] rounded-md hover:bg-[#FFD4A0] transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
              >
                {saving ? (
                  <CircularProgress size={20} thickness={5} sx={{ color: '#FF6B2C' }} />
                ) : (
                  <>
                    <Save className="mr-2 h-5 w-5" />
                    Enregistrer le prompt
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Aperçu du prompt avec les données réelles */}
          {showPreview && (
            <div
              ref={previewSectionRef}
              className={`mt-6 border rounded-lg p-4 transition-all duration-300 transform-gpu ${
                isHighlighted
                  ? 'highlight-pulse border-[#FF6B2C] bg-[#FFF8F3]'
                  : 'border-[#FFE4BA] bg-[#FFF8F3]'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className={`text-sm font-medium ${
                  isHighlighted ? 'text-[#FF6B2C]' : 'text-gray-700'
                } transition-colors duration-300`}>
                  Aperçu avec vos données (sera enregistré)
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className={`bg-white p-3 rounded-md border text-sm text-gray-700 whitespace-pre-wrap ${
                isHighlighted ? 'border-[#FF6B2C]' : 'border-gray-200'
              } transition-colors duration-300`}>
                {previewPrompt}
              </div>
            </div>
          )}
        </div>

        <div className="bg-white p-6 rounded-xl shadow-md">
          <h2 className="text-xl font-semibold mb-4">Exemples de prompts</h2>
          <div className="space-y-4">
            {promptTypes.map((type) => (
              <div key={type.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div
                  className="flex justify-between items-center p-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => toggleExpand(type.id)}
                >
                  <h3 className="font-medium text-gray-800">{type.name}</h3>
                  <button className="text-gray-500">
                    {expanded === type.id ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                </div>
                {expanded === type.id && (
                  <div className="p-3 border-t border-gray-200 bg-white">
                    <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                    <div className="bg-gray-50 p-3 rounded-md">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
                        {type.defaultPrompt}
                      </pre>
                    </div>
                    <div className="mt-2 flex justify-end">
                      <Tooltip title="Utiliser ce prompt">
                        <button
                          onClick={() => {
                            setSelectedType(type.id as GenerationType);
                            setPromptText(type.defaultPrompt);
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                          }}
                          className="text-xs text-[#FF6B2C] hover:text-[#FF7A35] transition-colors"
                        >
                          Utiliser ce prompt
                        </button>
                      </Tooltip>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-md">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Info className="mr-2 text-[#FF6B2C]" size={24} />
            Comment utiliser les prompts IA ?
          </h2>
          <div className="space-y-4 text-gray-700">
            <p>
              Un prompt, c'est simplement une façon de dire à l'IA ce que vous voulez qu'elle écrive. C'est comme donner des instructions à quelqu'un qui va rédiger un texte pour vous.
            </p>
            <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA]">
              <h3 className="font-medium mb-2">Les 3 parties d'un bon prompt :</h3>
              <ol className="list-decimal pl-6 space-y-2">
                <li><span className="font-medium">Vos informations</span> : Ce sont les détails que l'IA doit connaître (nom, expérience, compétences...)</li>
                <li><span className="font-medium">Ce que vous voulez</span> : Le type de texte que vous souhaitez (bio, description de service...)</li>
                <li><span className="font-medium">Le style souhaité</span> : Comment vous voulez que ce soit écrit (professionnel, amical, direct...)</li>
              </ol>
            </div>
            <div className="mt-4">
              <h3 className="font-medium mb-2">Comment personnaliser un prompt :</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Utilisez <code className="bg-gray-100 px-1 py-0.5 rounded">{"{{mot}}"}</code> pour les informations qui changent (votre nom, ville...)</li>
                <li>Gardez vos instructions simples et claires</li>
                <li>Précisez le ton que vous souhaitez (amical, professionnel...)</li>
                <li>Indiquez ce qui est important pour vous (mettre en avant certains points...)</li>
              </ul>
            </div>
            <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mt-4">
              <h3 className="font-medium mb-2">Exemple simple :</h3>
              <pre className="bg-white p-3 rounded-md text-sm">
{`Peux-tu écrire une bio pour mon profil ?

Mes infos :
- Je m'appelle {{prenom}} {{nom}}
- Je suis {{metier}}
- Mon expérience : {{experience}}

Je voudrais :
- Un texte professionnel mais sympa
- Qui donne envie de me contacter
- Pas trop long (environ 3-4 phrases)`}
              </pre>
            </div>
            <p className="mt-4">
              <span className="font-medium text-[#FF6B2C]">Conseil :</span> Commencez avec les prompts par défaut et modifiez-les petit à petit selon vos besoins. N'hésitez pas à faire plusieurs essais !
            </p>
          </div>
        </div>


        <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mb-6">
            <div className="flex items-start space-x-3">
              <Info className="text-[#FF6B2C] h-5 w-5 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm text-gray-700 mb-2">
                  Personnalisez les instructions données à l'IA pour chaque type de génération.
                  Ces prompts déterminent comment l'IA va générer votre contenu. Vous pouvez spécifier
                  le ton, le style, la longueur et d'autres préférences.
                </p>
                <p className="text-sm text-gray-700">
                  Utilisez <code className="bg-gray-100 px-1 py-0.5 rounded">{"{{nom}}"}</code>, <code className="bg-gray-100 px-1 py-0.5 rounded">{"{{prenom}}"}</code>, etc. pour insérer vos données dans le prompt.
                </p>
                <div className="flex justify-end mt-2">
                  <button
                    onClick={fetchUserProfile}
                    disabled={loadingProfile}
                    className="flex items-center text-xs text-[#FF6B2C] hover:text-[#FF7A35] transition-colors"
                  >
                    {loadingProfile ? (
                      <CircularProgress size={12} thickness={5} sx={{ color: '#FF6B2C', marginRight: '4px' }} />
                    ) : (
                      <RefreshCw className="mr-1 h-3 w-3" />
                    )}
                    Rafraîchir mes données
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Utilisation du nouveau système de génération IA */}
      {showConfirmModal && (
        <AiGenerationSystem
          type={selectedType}
          prompt={previewPrompt}
          originalPrompt={promptText}
          onComplete={(content) => {
            setShowConfirmModal(false);

            if (content) {
              // Si on a du contenu, c'est que la génération est terminée
              tempContentRef.current = content;

              // Appeler handleGenerationComplete après un court délai pour s'assurer que la référence est mise à jour
              setTimeout(() => {
                handleGenerationComplete();
              }, 0);
            }
          }}
          onCancel={() => {
            setShowConfirmModal(false);
          }}
          maxDuration={15000}
        />
      )}
    </>
  );
};

export default AiPromptsSection;
