import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Checkbox, 
  FormControlLabel, 
  FormGroup,
  Divider,
  Paper,
  Avatar,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import ModalPortal from './ModalPortal';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { notify } from './Notification';
import logger from '@/utils/logger';
import SecurityIcon from '@mui/icons-material/Security';
import HandshakeIcon from '@mui/icons-material/Handshake';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { fetchCsrfToken } from '../services/csrf';

// Styles personnalisés
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: '500px',
  width: '100%',
  borderRadius: '12px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  maxHeight: '90vh',
  display: 'flex',
  flexDirection: 'column'
}));

const ContentBox = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  marginBottom: theme.spacing(3)
}));

const ButtonBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: 'auto',
  paddingTop: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.grey[200]}`,
  gap: theme.spacing(0),
  flexWrap: 'wrap',
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    '& > button': {
      width: '100%',
      marginTop: theme.spacing(1)
    }
  }
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  padding: '10px 20px',
  fontWeight: 'bold',
  textTransform: 'none',
  minWidth: '200px',
  [theme.breakpoints.down('sm')]: {
    minWidth: '100%'
  }
}));

const PreviewBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(2),
  borderRadius: '8px',
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.grey[200]}`,
}));

interface ContactInfoModalProps {
  open: boolean;
  onClose: () => void;
  jobbeurId: string;
  missionId: string;
  proposalId: string;
  isAccepted: boolean;
  jobbeurName: string;
  jobbeurPhotoUrl?: string;
}

interface UserInfo {
  id: string;
  email: string;
  profil: {
    data: {
      nom: string;
      prenom: string;
      telephone?: string;
      telephone_prive?: boolean;
      adresse?: string;
      numero?: string;
      ville?: string;
      code_postal?: string;
      pays?: string;
      photo_url?: string;
    }
  }
}

const ContactInfoModal: React.FC<ContactInfoModalProps> = ({
  open,
  onClose,
  jobbeurId,
  missionId,
  proposalId,
  jobbeurName,
  jobbeurPhotoUrl
}) => {
  const theme = useTheme();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [buttonCooldown, setButtonCooldown] = useState(false);
  const [cooldownTimer, setCooldownTimer] = useState(0);

  // États pour les informations à partager
  const [shareEmail, setShareEmail] = useState(true);
  const [sharePhone, setSharePhone] = useState(true);
  const [shareAddress, setShareAddress] = useState(false);
  const [shareName, setShareName] = useState(true);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  // Gérer le cooldown du bouton
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (buttonCooldown && cooldownTimer > 0) {
      interval = setInterval(() => {
        setCooldownTimer((prev) => prev - 1);
      }, 1000);
    } else if (cooldownTimer === 0) {
      setButtonCooldown(false);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [buttonCooldown, cooldownTimer]);

  // Récupérer les informations de l'utilisateur
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        setLoading(true);
        const headers = await getCommonHeaders();
        const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil`, {
          headers,
          withCredentials: true
        });
        
        setUserInfo(response.data);
      } catch (error) {
        logger.error('Erreur lors de la récupération des informations utilisateur:', error);
        setError('Impossible de récupérer vos informations. Veuillez réessayer.');
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchUserInfo();
    }
  }, [open]);

  // Fonction pour ouvrir la boîte de dialogue de confirmation
  const handleOpenConfirmDialog = () => {
    setIsConfirmDialogOpen(true);
  };

  // Fonction pour fermer la boîte de dialogue de confirmation
  const handleCloseConfirmDialog = () => {
    setIsConfirmDialogOpen(false);
  };

  // Fonction pour capitaliser la première lettre
  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  };

  // Fonction pour envoyer les informations de contact
  const handleSendContactInfo = async () => {
    // Fermer la boîte de dialogue de confirmation
    setIsConfirmDialogOpen(false);
    
    // Activer le cooldown du bouton
    setButtonCooldown(true);
    setCooldownTimer(10);
    
    try {
      setLoading(true);
      
      // Préparer les informations à envoyer
      const contactInfo = {
        email: shareEmail ? userInfo?.email : undefined,
        telephone: sharePhone && userInfo?.profil?.data?.telephone ? userInfo.profil.data.telephone : undefined,
        nom_prenom: shareName ? {
          nom: capitalizeFirstLetter(userInfo?.profil?.data?.nom || ''),
          prenom: capitalizeFirstLetter(userInfo?.profil?.data?.prenom || '')
        } : undefined,
        adresse: shareAddress ? {
          numero: userInfo?.profil?.data?.numero,
          rue: userInfo?.profil?.data?.adresse,
          ville: userInfo?.profil?.data?.ville,
          code_postal: userInfo?.profil?.data?.code_postal,
          pays: userInfo?.profil?.data?.pays
        } : undefined
      };
      
      // Construire l'URL correctement
      const url = `${API_CONFIG.baseURL}/api/missions/${missionId}/proposals/${proposalId}/contact-info`;
      logger.info('Envoi des informations de contact', { url, missionId, proposalId, jobbeurId });
      
      // Envoyer les informations via l'API
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.post(
        url,
        { contactInfo, jobbeurId },
        {
          headers,
          withCredentials: true
        }
      );
      
      // Créer une notification pour le jobbeur via le backend
      // La notification sera créée côté serveur
      
      notify('Informations de contact envoyées avec succès', 'success');
      onClose();
    } catch (error: any) {
      logger.error('Erreur lors de l\'envoi des informations de contact:', error);
      
      // Vérifier si c'est une erreur 429 (trop de requêtes)
      if (error.response && error.response.status === 429) {
        notify(error.response.data.message || 'Vous avez déjà envoyé les informations de contact récemment. Veuillez attendre 60 minutes avant de réessayer.', 'warning', 8000);
      } else {
        notify('Erreur lors de l\'envoi des informations de contact', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  // Prévisualisation des informations à envoyer
  const renderPreview = () => {
    if (!userInfo) return null;
    
    // Vérifier si au moins une information est sélectionnée
    const hasSelectedInfo = shareName || shareEmail || (sharePhone && userInfo.profil?.data?.telephone) || shareAddress;
    if (!hasSelectedInfo) return null;
    
    return (
      <PreviewBox>
        <Typography variant="subtitle2" gutterBottom>
          Aperçu des informations qui seront envoyées :
        </Typography>
        
        {shareName && (
          <Box mb={1}>
            <Typography variant="body2" color="textSecondary">Nom et prénom :</Typography>
            <Typography variant="body2">{capitalizeFirstLetter(userInfo.profil?.data?.nom || '')} {capitalizeFirstLetter(userInfo.profil?.data?.prenom || '')}</Typography>
          </Box>
        )}
        
        {shareEmail && (
          <Box mb={1}>
            <Typography variant="body2" color="textSecondary">Email :</Typography>
            <Typography variant="body2">{userInfo.email}</Typography>
          </Box>
        )}
        
        {sharePhone && userInfo.profil?.data?.telephone && (
          <Box mb={1}>
            <Typography variant="body2" color="textSecondary">Téléphone :</Typography>
            <Typography variant="body2">{userInfo.profil.data.telephone}</Typography>
          </Box>
        )}
        
        {shareAddress && (
          <Box>
            <Typography variant="body2" color="textSecondary">Adresse :</Typography>
            <Typography variant="body2">
              {userInfo.profil?.data?.numero} {userInfo.profil?.data?.adresse}, {userInfo.profil?.data?.code_postal} {userInfo.profil?.data?.ville}, {userInfo.profil?.data?.pays}
            </Typography>
          </Box>
        )}
      </PreviewBox>
    );
  };

  return (
    <>
      <ModalPortal isOpen={open} onBackdropClick={onClose}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3 }}
        >
          <StyledPaper>
            <Typography variant="h6" gutterBottom>
              Partager vos informations de contact
            </Typography>
            
            <Typography variant="body2" color="textSecondary" paragraph>
              Sélectionnez les informations que vous souhaitez partager avec <strong>{jobbeurName}</strong> pour qu'il puisse vous contacter.
            </Typography>

            <Typography variant="body2" paragraph sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
              Ce partage d'informations est entièrement facultatif. Vous pouvez fermer cette fenêtre si vous préférez ne pas partager vos coordonnées maintenant.
            </Typography>
            
            <ContentBox>
              {loading && !userInfo ? (
                <Box display="flex" justifyContent="center" my={4}>
                  <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
                </Box>
              ) : error ? (
                <Typography color="error" align="center" my={2}>
                  {error}
                </Typography>
              ) : (
                <>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar 
                      src={jobbeurPhotoUrl} 
                      alt={jobbeurName}
                      sx={{ width: 56, height: 56, mr: 2 }}
                    />
                    <Box>
                      <Typography variant="subtitle1">
                        {jobbeurName}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox 
                          checked={shareName} 
                          onChange={(e) => setShareName(e.target.checked)}
                          sx={{ 
                            color: '#FF6B2C',
                            '&.Mui-checked': {
                              color: '#FF6B2C',
                            },
                          }}
                        />
                      }
                      label="Partager mon nom et prénom"
                    />
                    
                    <FormControlLabel
                      control={
                        <Checkbox 
                          checked={shareEmail} 
                          onChange={(e) => setShareEmail(e.target.checked)}
                          sx={{ 
                            color: '#FF6B2C',
                            '&.Mui-checked': {
                              color: '#FF6B2C',
                            },
                          }}
                        />
                      }
                      label="Partager mon email"
                    />
                    
                    <FormControlLabel
                      control={
                        <Checkbox 
                          checked={sharePhone} 
                          onChange={(e) => setSharePhone(e.target.checked)}
                          disabled={!userInfo?.profil?.data?.telephone}
                          sx={{ 
                            color: '#FF6B2C',
                            '&.Mui-checked': {
                              color: '#FF6B2C',
                            },
                          }}
                        />
                      }
                      label={
                        userInfo?.profil?.data?.telephone 
                          ? "Partager mon numéro de téléphone" 
                          : "Numéro de téléphone non disponible"
                      }
                    />
                    
                    <FormControlLabel
                      control={
                        <Checkbox 
                          checked={shareAddress} 
                          onChange={(e) => setShareAddress(e.target.checked)}
                          sx={{ 
                            color: '#FF6B2C',
                            '&.Mui-checked': {
                              color: '#FF6B2C',
                            },
                          }}
                        />
                      }
                      label="Partager mon adresse complète"
                    />
                  </FormGroup>
                  
                  {renderPreview()}
                </>
              )}
            </ContentBox>
            
            <ButtonBox>
              <StyledButton
                variant="outlined"
                onClick={onClose}
                sx={{ 
                  color: '#FF6B2C', 
                  borderColor: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF7A35',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)'
                  }
                }}
              >
                Annuler
              </StyledButton>
              
              <StyledButton
                variant="contained"
                onClick={handleOpenConfirmDialog}
                disabled={loading || buttonCooldown || (!shareName && !shareEmail && !sharePhone && !shareAddress)}
                sx={{ 
                  bgcolor: '#FF6B2C', 
                  '&:hover': { bgcolor: '#FF7A35' },
                  '&.Mui-disabled': { bgcolor: '#FFE4BA', color: '#FF965E' },
                  whiteSpace: 'nowrap'
                }}
              >
                {loading ? (
                  <CircularProgress size={24} sx={{ color: 'white' }} />
                ) : buttonCooldown ? (
                  `Patienter (${cooldownTimer}s)`
                ) : (
                  'Envoyer mes informations'
                )}
              </StyledButton>
            </ButtonBox>
          </StyledPaper>
        </motion.div>
      </ModalPortal>

      {/* Boîte de dialogue de confirmation */}
      <Dialog
        open={isConfirmDialogOpen}
        onClose={handleCloseConfirmDialog}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: '12px',
            maxWidth: '700px',
            width: '100%',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle id="confirm-dialog-title" sx={{ color: '#FF6B2C', fontWeight: 'bold' }}>
          Confirmation d'envoi des informations
        </DialogTitle>
        
        <DialogContent sx={{ overflowY: 'auto' }}>
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2" fontWeight="medium">
              Une fois envoyées, vos informations de contact ne pourront plus être retirées. Assurez-vous de vouloir les partager avec ce jobbeur.
            </Typography>
          </Alert>
          
          <Alert severity="info" sx={{ mb: 3, '& .MuiAlert-icon': { color: '#FF6B2C' }, '& .MuiAlert-message': { color: '#FF6B2C' } }}>
            <Typography variant="body2" fontWeight="medium">
              Rappel : ce partage d'informations est entièrement facultatif. Vous pouvez annuler à tout moment.
            </Typography>
          </Alert>
          
          <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
            Avant de partager vos informations, voici quelques conseils importants :
          </Typography>
          
          <List>
            <ListItem alignItems="flex-start" sx={{ py: 1 }}>
              <ListItemIcon sx={{ minWidth: '40px' }}>
                <SecurityIcon sx={{ color: '#FF6B2C' }} />
              </ListItemIcon>
              <ListItemText 
                primary="Sécurité" 
                secondary="Pour votre première rencontre, privilégiez un lieu public. Vérifiez les avis et le profil du jobbeur avant de communiquer vos informations personnelles."
              />
            </ListItem>
            
            <ListItem alignItems="flex-start" sx={{ py: 1 }}>
              <ListItemIcon sx={{ minWidth: '40px' }}>
                <HandshakeIcon sx={{ color: '#FF6B2C' }} />
              </ListItemIcon>
              <ListItemText 
                primary="Bienveillance" 
                secondary="Privilégiez une communication respectueuse et bienveillante. Une bonne relation de confiance est la clé d'une collaboration réussie."
              />
            </ListItem>
            
            <ListItem alignItems="flex-start" sx={{ py: 1 }}>
              <ListItemIcon sx={{ minWidth: '40px' }}>
                <TipsAndUpdatesIcon sx={{ color: '#FF6B2C' }} />
              </ListItemIcon>
              <ListItemText 
                primary="Astuce" 
                secondary="Confirmez par écrit les détails de la mission (horaires, tarifs, tâches) pour éviter tout malentendu. Utilisez la messagerie de la plateforme pour garder une trace des échanges."
              />
            </ListItem>
            
            <ListItem alignItems="flex-start" sx={{ py: 1 }}>
              <ListItemIcon sx={{ minWidth: '40px' }}>
                <WarningAmberIcon sx={{ color: '#FF6B2C' }} />
              </ListItemIcon>
              <ListItemText 
                primary="Paiement" 
                secondary="Privilégiez les échanges/trocs via la plateforme JobPartiel avec vos Jobi pour bénéficier de notre protection."
              />
            </ListItem>
          </List>
        </DialogContent>
        
        <DialogActions sx={{ 
          display: 'flex',
          gap: 1,
          flexWrap: 'wrap',
          '& > button': {
            margin: 0
          },
          '& > :not(style) ~ :not(style)': {
            marginLeft: 0
          },
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            '& > button': {
              width: '100%',
              margin: '4px 0'
            }
          }
        }}>
          <Button 
            onClick={handleCloseConfirmDialog} 
            sx={{ 
              color: '#FF6B2C', 
              borderColor: '#FF6B2C',
              minWidth: { xs: '100%', sm: 'auto' },
              '&:hover': {
                borderColor: '#FF7A35',
                backgroundColor: 'rgba(255, 107, 44, 0.04)'
              }
            }}
            variant="outlined"
          >
            Annuler
          </Button>
          <Button 
            onClick={handleSendContactInfo} 
            sx={{ 
              bgcolor: '#FF6B2C', 
              color: 'white', 
              '&:hover': { bgcolor: '#FF7A35' },
              minWidth: { xs: '100%', sm: 'auto' }
            }}
            variant="contained"
            autoFocus
          >
            J'ai compris, envoyer mes informations à {jobbeurName}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ContactInfoModal; 