import { Request, Response } from 'express';
import { hashPassword, comparePasswords } from '../utils/password';
import {
  sendVerificationEmail,
  sendWelcomeEmail,
  sendPasswordResetEmail,
  sendPasswordChangedEmail,
  sendLoginAttemptsWarningEmail,
  sendPasswordExpirationWarningEmail,
  sendLoginNotificationEmail,
  sendSuspensionEmail,
  sendReferrerNotificationEmail,
  sendReferralWelcomeEmail,
  sendEmailChangeNotification,
  sendEmailChangeConfirmation,
  sendTwoFactorAuthEmail
} from '../services/emailService';
import { dbService } from '../services/db';
import { tokenService } from '../services/tokenService';
import { cookieUtils } from '../utils/cookieUtils';
import dns from 'dns';
import * as process from 'process';
import logger from '../utils/logger';
import axios from 'axios'; // Import axios
import config from '../config'; // Import config
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { canSendEmail, sendEmailWithPreferenceCheck } from '../utils/emailPreferences';
import { redis } from '../config/redis';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

type UserType = 'jobbeur' | 'non-jobbeur';

// Fonction pour masquer partiellement une adresse email
const maskEmail = (email: string): string => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;

  // Masquer une partie du nom d'utilisateur
  let maskedUsername = '';
  if (username.length <= 3) {
    maskedUsername = username[0] + '*'.repeat(username.length - 1);
  } else {
    maskedUsername = username.substring(0, 2) + '*'.repeat(Math.min(username.length - 2, 3));
    if (username.length > 5) {
      maskedUsername += username.substring(username.length - 2);
    }
  }

  // Masquer une partie du domaine
  const domainParts = domain.split('.');
  const tld = domainParts.pop() || '';
  const domainName = domainParts.join('.');

  let maskedDomain = '';
  if (domainName.length <= 3) {
    maskedDomain = domainName[0] + '*'.repeat(domainName.length - 1);
  } else {
    maskedDomain = domainName[0] + '*'.repeat(Math.min(domainName.length - 1, 3));
    if (domainName.length > 4) {
      maskedDomain += domainName.substring(domainName.length - 1);
    }
  }

  return `${maskedUsername}@${maskedDomain}.${tld}`;
};

// Fonction pour formater le nom et prénom sous la forme "Jean D."
const formatName = (prenom: string | null, nom: string | null): string => {
  if (!prenom && !nom) return '';

  const prenomFormatted = prenom ? prenom.trim() : '';
  const nomFormatted = nom ? nom.trim() : '';

  if (!prenomFormatted && !nomFormatted) return '';
  if (!prenomFormatted) return nomFormatted;
  if (!nomFormatted) return prenomFormatted;

  // Format "Jean D." pour Jean Dupont
  return `${prenomFormatted} ${nomFormatted.charAt(0).toUpperCase()}.`;
};

// Définir une fonction utilitaire pour les options de cookie auth
function getAuthCookieOptions() {
  const isProduction = process.env.NODE_ENV === 'production';
  return {
    httpOnly: true,
    secure: isProduction,
    sameSite: isProduction ? 'none' as 'none' : 'strict' as 'strict',
    path: '/',
    ...(isProduction ? { domain: '.jobpartiel.fr' } : {})
  };
}

// Fonction commune pour le processus post-inscription (Jobi, crédits IA, parrainage, etc.)
export const processPostRegistration = async (
  newUser: any,
  referrerId: string | null = null,
  ipAddress: string = '',
  skipEmailVerification: boolean = false
) => {
  try {
    // Gestion du parrainage si un code était présent
    if (referrerId) {
      try {
        // Créer l'entrée de parrainage
        const { error: referralError } = await dbService.supabase
          .from('user_referrals')
          .insert({
            referrer_id: referrerId,
            referred_id: newUser.id,
            reward_amount: 20,
            status: 'pending'
          });

        if (referralError) {
          logger.error('Erreur lors de la création de l\'entrée de parrainage:', referralError);
        } else {
          // Mettre à jour le champ referred_by dans la table users
          const { error: updateError } = await dbService.supabase
            .from('users')
            .update({ referred_by: referrerId })
            .eq('id', newUser.id);

          if (updateError) {
            logger.error('Erreur lors de la mise à jour du champ referred_by:', updateError);
          } else {
            logger.info('Champ referred_by mis à jour avec succès');
          }

          // Récupérer les informations du parrain
          const { data: referrerData, error: referrerError } = await dbService.supabase
            .from('users')
            .select('id, email')
            .eq('id', referrerId)
            .single();

          const { data: referrerProfile, error: referrerProfileError } = await dbService.supabase
            .from('user_profil')
            .select('nom, prenom')
            .eq('user_id', referrerId)
            .single();

          // Déchiffrer les données du profil du parrain
          const decryptedReferrerProfile = referrerProfile ? await decryptProfilDataAsync(referrerProfile) : null;

          if (!referrerError && referrerData && !referrerProfileError && decryptedReferrerProfile) {
            // Déchiffrer les données du parrain
            const decryptedReferrerData = await decryptUserDataAsync(referrerData);

            // Récupérer le profil du filleul (nouvellement créé)
            const { data: newUserProfile, error: newUserProfileError } = await dbService.supabase
              .from('user_profil')
              .select('prenom, nom')
              .eq('user_id', newUser.id)
              .single();

            const decryptedNewUserProfile = newUserProfileError ? null : await decryptProfilDataAsync(newUserProfile);

            // Compter le nombre total de parrainages du parrain
            const { count, error: countError } = await dbService.supabase
              .from('user_referrals')
              .select('id', { count: 'exact', head: true })
              .eq('referrer_id', referrerId);

            const totalReferrals = countError ? 1 : (count || 1);

            // Envoyer les emails de parrainage
            try {
              // Format des noms pour l'affichage (utiliser les données déchiffrées)
              const formattedReferrerName = formatName(decryptedReferrerProfile.prenom, decryptedReferrerProfile.nom);
              const formattedReferredName = decryptedNewUserProfile ?
                formatName(decryptedNewUserProfile.prenom, decryptedNewUserProfile.nom) : null;

              // Utiliser le nom formaté ou l'email masqué si le nom n'est pas disponible
              const displayReferredName = formattedReferredName || maskEmail(newUser.email);
              const displayReferrerName = formattedReferrerName || maskEmail(decryptedReferrerData.email);

              // Email au parrain
              await sendReferrerNotificationEmail(decryptedReferrerData.email, {
                referredName: displayReferredName,
                rewardAmount: 20, // Montant de la récompense en Jobi lors d'un parrainage
                totalReferrals
              });

              // Email au filleul
              await sendReferralWelcomeEmail(newUser.email, {
                referrerName: displayReferrerName,
                rewardAmount: 20 // Montant de la récompense en Jobi lors d'un parrainage
              });

              // Envoyer une notification au parrain
              const { error: notificationError1 } = await dbService.supabase
                .from('user_notifications')
                .insert({
                  user_id: referrerId,
                  type: 'system',
                  title: 'Nouveau filleul !',
                  content: `${displayReferredName} vient de s'inscrire grâce à votre code de parrainage.`,
                  is_read: false,
                  is_archived: false
                });

              // Envoyer une notification au filleul
              const { error: notificationError2 } = await dbService.supabase
                .from('user_notifications')
                .insert({
                  user_id: newUser.id,
                  type: 'system',
                  title: skipEmailVerification ? 'Parrainage confirmé !' : 'Bienvenue sur Job Partiel !',
                  content: `Vous avez été parrainé par ${displayReferrerName}${skipEmailVerification ? '. Vous recevrez 20 Jobi après votre première mission validée.' : ' Ajoutez votre première mission pour recevoir 20 Jobis !'}`,
                  is_read: false,
                  is_archived: false
                });

              if (notificationError1) {
                logger.error('Erreur lors de l\'envoi de la notification au parrain:', notificationError1);
              }
              if (notificationError2) {
                logger.error('Erreur lors de l\'envoi de la notification au filleul:', notificationError2);
              }

              logger.info('Emails et notifications de parrainage envoyés avec succès');
            } catch (emailError) {
              logger.error('Erreur lors de l\'envoi des emails de parrainage:', emailError);
            }
          }
        }
      } catch (referralError) {
        logger.error('Erreur lors de la gestion du parrainage:', referralError);
      }
    }

    // Créditer le compte de 10 Jobi à l'inscription et l'ajouter dans l'historique des jobi
    try {
      await dbService.createJobiEntrySupplement(newUser.id, {
        montant: 10,
        titre: 'Inscription',
        description: '10 Jobi offerts à l\'inscription'
      });
      logger.info('Jobi ajoutés avec succès');

      // Ajouter une notification pour les 10 Jobi reçus
      const { error: notifError } = await dbService.supabase
        .from('user_notifications')
        .insert([{
          user_id: newUser.id,
          type: 'jobi',
          title: 'Bienvenue sur JobPartiel !',
          content: 'Vous avez reçu 10 Jobi pour votre inscription. Les Jobi vous permettent d\'accéder à des fonctionnalités supplémentaires sur la plateforme, de les échanger contre des services ou de les convertir en bon d\'achat.',
          link: '/dashboard/jobi',
          is_read: false,
          is_archived: false
        }]);

      if (notifError) {
        logger.error('Erreur lors de la création de la notification pour les Jobi d\'inscription:', notifError);
      }
    } catch (error) {
      logger.error('Erreur lors de l\'ajout des Jobi à la création de l\'utilisateur:', error);
      throw new Error('Erreur lors de l\'ajout des Jobi à la création de l\'utilisateur.');
    }

    // Créditer le compte de 10 crédits IA à l'inscription
    try {
      // Créer une entrée dans la table user_ai_credits
      const { error: aiCreditsError } = await dbService.supabase
        .from('user_ai_credits')
        .insert({
          user_id: newUser.id,
          credits: 10,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (aiCreditsError) {
        logger.error('Erreur lors de l\'ajout des crédits IA à l\'inscription:', aiCreditsError);
      } else {
        logger.info('Crédits IA ajoutés avec succès');

        // Ajouter dans l'historique des crédits IA
        const { error: historyError } = await dbService.supabase
          .from('user_ai_credits_historique')
          .insert({
            user_id: newUser.id,
            operation_type: 'offert_admin',
            montant: 10,
            solde_avant: 0,
            solde_apres: 10,
            description: 'Crédits IA offerts à l\'inscription',
            created_at: new Date().toISOString()
          });

        if (historyError) {
          logger.error('Erreur lors de l\'ajout dans l\'historique des crédits IA:', historyError);
        }

        // Ajouter une notification pour les crédits IA offerts
        const { error: aiNotifError } = await dbService.supabase
          .from('user_notifications')
          .insert({
            user_id: newUser.id,
            type: 'system',
            title: 'Crédits IA offerts !',
            content: 'Vous avez reçu 10 crédits IA pour votre inscription. Ces crédits vous permettent d\'utiliser les fonctionnalités d\'intelligence artificielle de la plateforme.',
            link: '/dashboard/ai-credits',
            is_read: false,
            is_archived: false
          });

        if (aiNotifError) {
          logger.error('Erreur lors de la création de la notification pour les crédits IA:', aiNotifError);
        }

        // Mettre à jour le cache Redis pour les crédits IA
        try {
          const cacheKey = `ai_credits:${newUser.id}`;
          await redis.setex(cacheKey, 60 * 60, '10'); // Cache pour 1 heure
        } catch (redisError) {
          logger.error('Erreur lors de la mise à jour du cache Redis pour les crédits IA:', redisError);
        }
      }
    } catch (error) {
      logger.error('Erreur lors de l\'ajout des crédits IA à l\'inscription:', error);
    }

    // Journaliser l'action d'inscription
    if (ipAddress) {
      await logUserActivity(
        newUser.id,
        'user_register',
        newUser.id,
        'user',
        {
          user_type: newUser.user_type,
          referred_by: referrerId || undefined,
          registration_method: skipEmailVerification ? 'google' : 'email'
        },
        ipAddress
      );
    }

    // Créer le token de vérification email seulement si nécessaire (pas pour Google)
    if (!skipEmailVerification) {
      const { verificationLink, token: verificationToken } = await dbService.createEmailVerification(newUser.id);
      logger.info('Token de vérification créé:', { verificationToken });

      // Envoyer l'email de vérification
      try {
        await sendVerificationEmail(newUser.email, {
          token: verificationToken,
          verificationLink: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${verificationToken}`
        });
        logger.info('Email de vérification envoyé avec succès');
      } catch (emailError: any) {
        logger.error('Erreur lors de l\'envoi de l\'email de vérification:', emailError);
        throw emailError;
      }
    }

    // Envoyer l'email de bienvenue seulement pour Google (email déjà vérifié)
    if (skipEmailVerification) {
      try {
        await sendWelcomeEmail(newUser.email);
        logger.info('Email de bienvenue envoyé avec succès (Google)');
      } catch (emailError: any) {
        logger.error('Erreur lors de l\'envoi de l\'email de bienvenue (Google):', emailError);
        // On continue le processus même si l'email de bienvenue échoue
      }
    }

    return { success: true };
  } catch (error) {
    logger.error('Erreur lors du processus post-inscription:', error);
    throw error;
  }
};

export class AuthController
{
  async inscription(req: Request, res: Response) {
    try {
      logger.info('Début du processus d\'inscription:', {
        email: req.body.email,
        userType: req.body.userType
      });

      const { email, password, userType, referralCode } = req.body;

      // Validation des champs obligatoires
      if (!email || !password) {
        logger.warn('Champs manquants lors de l\'inscription:', {
          hasEmail: !!email,
          hasPassword: !!password
        });
        return res.status(400).json({
          message: 'Email et mot de passe sont requis',
          error: 'MISSING_CREDENTIALS',
          needsVerification: false,
          toastType: 'error'
        });
      }

      // Valider le format de l'email et le domaine
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        logger.warn('Format d\'email invalide:', { email });
        return res.status(400).json({
          message: 'Format d\'email invalide',
          needsVerification: false,
          toastType: 'error'
        });
      }

      // Extraire et vérifier le domaine
      logger.info('Vérification du domaine email');
      const domain = email.split('@')[1];
      try {
        await dns.promises.resolve(domain, 'MX');
        logger.info('Domaine email valide:', { domain });
      } catch (error) {
        logger.error('Domaine email invalide:', { domain, error });
        return res.status(400).json({
          message: 'L\'adresse email fournie n\'est pas valide. Veuillez vérifier le domaine.',
          needsVerification: false,
          toastType: 'error'
        });
      }

      // Vérifier si l'utilisateur existe déjà
      logger.info('Vérification de l\'existence de l\'utilisateur');
      try {
        const existingUser = await dbService.getUserByEmail(email);
        if (existingUser) {
          logger.warn('Tentative d\'inscription avec un email existant:', { email });
          return res.status(400).json({
            message: 'Cet email est déjà utilisé, connectez-vous ou réinitialisez votre mot de passe',
            needsVerification: false,
            toastType: 'error'
          });
        }
      } catch (error) {
        logger.error('Erreur lors de la vérification de l\'email:', error);
        return res.status(400).json({ message: 'Erreur lors de la vérification de l\'email.' });
      }

      // Vérifier le code de parrainage s'il est fourni
      let referrerId = null;
      if (referralCode) {
        try {
          logger.info('Vérification du code de parrainage:', { referralCode });
          const { data: referrerData, error: referrerError } = await dbService.supabase
            .from('users')
            .select('id')
            .eq('referral_code', referralCode)
            .single();

          if (referrerError || !referrerData) {
            logger.warn('Code de parrainage invalide:', { referralCode, error: referrerError });
            return res.status(400).json({
              message: 'Code de parrainage invalide',
              needsVerification: false,
              toastType: 'error'
            });
          }

          referrerId = referrerData.id;
          logger.info('Code de parrainage valide, utilisateur trouvé:', { referrerId });
        } catch (error) {
          logger.error('Erreur lors de la vérification du code de parrainage:', error);
          // On continue l'inscription même si le code de parrainage est invalide
        }
      }

      logger.info('Hashage du mot de passe');
      // Hacher le mot de passe
      const passwordHash = await hashPassword(password);

      // Créer l'utilisateur avec le type correct
      const userData = {
        email,
        password_hash: passwordHash,
        user_type: userType || 'non-jobbeur',
        role: 'jobutil',
        email_verifier: false,
        profil_verifier: false,
        identite_verifier: false,
        entreprise_verifier: false,
        assurance_verifier: false,
        profil_actif: true,
        date_inscription: new Date().toISOString(),
        referred_by: referrerId
      };

      logger.info('Tentative de création de l\'utilisateur');

      try {
        // Créer l'utilisateur
        const newUser = await dbService.createUser(userData);

        logger.info('Utilisateur créé avec succès:', { userId: newUser.id });

        // Utiliser la fonction commune pour le processus post-inscription
        try {
          await processPostRegistration(
            newUser,
            referrerId,
            getIpFromRequest(req),
            false // skipEmailVerification = false pour l'inscription classique
          );
        } catch (postRegError: any) {
          logger.error('Erreur lors du processus post-inscription:', postRegError);

          // Si l'erreur est liée à un domaine invalide
          if (postRegError.message?.includes('Domain not found')) {
            return res.status(400).json({
              message: 'L\'adresse email fournie n\'est pas valide. Veuillez vérifier le domaine.',
              needsVerification: false,
              toastType: 'error'
            });
          }

          // Si l'adresse email n'existe pas (erreur SMTP explicite)
          if (
            postRegError.message?.includes('Recipient address rejected: User unknown in virtual mailbox table') ||
            postRegError.message?.includes('550 5.1.1')
          ) {
            return res.status(400).json({
              message: 'Attention ! L\'adresse email saisie n\'existe pas. Veuillez vérifier votre saisie. Aucun email ne vous a été envoyé.',
              needsVerification: false,
              toastType: 'error'
            });
          }

          // Pour les autres erreurs d'email
          return res.status(500).json({
            message: 'Une erreur est survenue lors de l\'envoi de l\'email de vérification. Veuillez réessayer.',
            needsVerification: false,
            toastType: 'error'
          });
        }

        logger.info('Inscription terminée avec succès');

        return res.status(201).json({
          user: {
            id: newUser.id,
            email: newUser.email,
            userType: newUser.user_type,
            email_verifier: newUser.email_verifier,
            profil_actif: newUser.profil_actif,
            role: newUser.role
          },
          // token: accessToken,
          message: 'Inscription réussie. Un email de vérification a été envoyé à votre adresse email.',
          needsVerification: true,
          toastType: 'success'
        });

      } catch (createError) {
        logger.error('Erreur lors de la création de l\'utilisateur:', createError);
        throw createError;
      }

    } catch (error) {
      logger.error('Erreur globale lors de l\'inscription:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue lors de l\'inscription',
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        needsVerification: false,
        toastType: 'error'
      });
    }
  }

  async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      // Validation des champs
      if (!email || !password) {
        return res.status(400).json({
          message: 'Email et mot de passe sont requis',
          toastType: 'error'
        });
      }

      // Récupérer l'utilisateur
      const userRaw = await dbService.getUserByEmail(email);

      if (!userRaw) {
        return res.status(401).json({
          message: 'L\'adresse email ou le mot de passe que vous avez entré est incorrect, ou le compte est introuvable.',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Vérifier si le compte a été créé avec Google et n'a pas de mot de passe
      if (user.google_id && (!user.password_hash || user.password_hash === '')) {
        logger.info('Tentative de connexion avec email/mot de passe pour un compte Google', {
          email,
          userId: user.id
        });

        return res.status(400).json({
          success: false,
          message: 'Ce compte a été créé avec Google. Veuillez vous connecter en utilisant le bouton Google.',
          toastType: 'warning'
        });
      }

      // Vérifier si l'email est vérifié et si on peut envoyer un nouvel email de vérification
      if (!user.email_verifier) {
        // Vérifier si on peut envoyer un nouvel email
        const { canSendEmail, remainingSeconds } = await dbService.checkLastEmailSent(user.id, 'email_verification');

        let message = 'Veuillez vérifier votre email avant de continuer.';

        if (canSendEmail) {
          // Générer un nouveau token de vérification
          const { verificationLink, token: verificationToken } = await dbService.createEmailVerification(user.id);

          // Envoyer l'email de vérification
          await sendVerificationEmail(email, {
            token: verificationToken,
            verificationLink: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${verificationToken}`
          });

          message = 'Votre email n\'est pas vérifié. Nous vous avons envoyé un nouvel email de vérification.';
        } else if (remainingSeconds) {
          message = `Un email de vérification a déjà été envoyé. Vous pourrez en demander un nouveau dans ${Math.ceil(remainingSeconds / 60)} minutes.`;
        }

        return res.status(403).json({
          success: false,
          message: message,
          email_verifier: false,
          requiresVerification: true,
          toastType: 'warning',
          user:
          {
            id: user.id,
            email: user.email,
            profil_actif: user.profil_actif,
            role: user.role,
            email_verifier: user.email_verifier
          }
        });
      }

      // Vérifier si le profil est inactif
      if (!user.profil_actif) {
        // Si la suspension est temporaire et la date est dépassée, réactiver automatiquement
        if (user.suspended_until && new Date(user.suspended_until) < new Date()) {
          await dbService.updateUser(user.id, {
            profil_actif: true,
            suspended_until: null,
            suspension_reason: null
          });
          user.profil_actif = true;
          user.suspended_until = null;
          user.suspension_reason = null;
          logger.info('Suspension expirée, utilisateur réactivé automatiquement', { userId: user.id });
          // On continue le login normalement
        } else {
          const lastSuspensionEmailSent = req.cookies['lastSuspensionEmailSent'];
          const now = new Date().getTime();
          const cookieAge = lastSuspensionEmailSent ? now - new Date(lastSuspensionEmailSent).getTime() : 0;
          const twentyFourHours = 24 * 60 * 60 * 1000;
          if (!lastSuspensionEmailSent || cookieAge > twentyFourHours) {
            await sendSuspensionEmail(user.email, user.suspension_reason || 'Contactez le support pour plus d\'informations.');
            res.cookie('lastSuspensionEmailSent', new Date().toISOString(), {
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax',
              path: '/',
              maxAge: twentyFourHours // 24 heures
            });
            logger.info('Email de suspension envoyé à :', user.email);
          } else {
            logger.info('Email de suspension déja envoyé.');
          }
          let suspensionMsg = '';
          if (user.suspension_reason) {
            if (user.suspended_until) {
              // Suspension temporaire avec date de fin
              const date = new Date(user.suspended_until);
              const dateStr = date.toLocaleDateString('fr-FR');
              suspensionMsg = `Votre compte a été temporairement suspendu jusqu'au ${dateStr} pour la raison suivante : ${user.suspension_reason}`;
            } else {
              // Bannissement définitif avec raison
              suspensionMsg = `Votre compte a été définitivement suspendu pour la raison suivante : ${user.suspension_reason}`;
            }
          } else {
            // Bannissement sans raison spécifique
            suspensionMsg = `Votre compte a été suspendu. Veuillez contacter notre support pour plus d'informations.`;
          }
          return res.status(423).json({
            message: suspensionMsg,
            toastType: 'error'
          });
        }
      }

      // Vérifier si le compte est verrouillé temporairement (quand trop de tentatives de connexion)
      if (user.account_locked_until && new Date(user.account_locked_until) > new Date())
      {
        const remainingTime = Math.ceil((new Date(user.account_locked_until).getTime() - new Date().getTime()) / (1000 * 60));
        return res.status(423).json({
          message: `Compte temporairement bloqué. Réessayez dans ${remainingTime} minutes`,
           toastType: 'error'
        });
      }

      // Vérifier le mot de passe
      if (!user.password_hash || typeof user.password_hash !== 'string') {
        // Si le password_hash n'est pas une chaîne valide, on considère que c'est un compte Google
        logger.info('Tentative de connexion avec un compte Google sans mot de passe valide', {
          email,
          userId: user.id,
          hasGoogleId: !!user.google_id
        });

        return res.status(400).json({
          success: false,
          message: 'Ce compte a été créé avec Google. Veuillez vous connecter en utilisant le bouton Google.',
          toastType: 'warning'
        });
      }

      // Maintenant on peut comparer les mots de passe en toute sécurité
      const passwordMatch = await comparePasswords(password, user.password_hash);
      if (!passwordMatch) {
        // Gérer les tentatives de connexion échouées
        const result = await dbService.incrementLoginAttempts(email);

        // Envoyer un email d'avertissement si nécessaire
        if (result.remainingAttempts <= 2 || result.locked) {
          await sendLoginAttemptsWarningEmail(
            email,
            5 - result.remainingAttempts,
            result.locked,
            result.lockDuration
          );
        }

        if (result.locked) {
          return res.status(423).json({
            message: `Compte bloqué pour ${result.lockDuration} minutes suite à trop de tentatives`,
               toastType: 'error'
          });
        }

        return res.status(401).json({
          message: `Email ou mot de passe incorrect. ${result.remainingAttempts} tentatives restantes`,
          toastType: 'error'
        });
      }

      // Réinitialiser les tentatives de connexion
      await dbService.resetLoginAttempts(user.id);

      // Vérifier si le mot de passe a expiré
      const passwordStatus = await dbService.checkPasswordExpiration(user.id);
      if (passwordStatus.expired) {
        return res.status(401).json({
          message: 'Votre mot de passe a expiré. Veuillez le réinitialiser.',
          toastType: 'error',
          passwordExpired: true
        });
      } else if (passwordStatus.daysUntilExpiration && passwordStatus.daysUntilExpiration <= 30) {
        // Envoyer un email d'avertissement si le mot de passe expire bientôt
        try {
          await sendPasswordExpirationWarningEmail(user.email, passwordStatus.daysUntilExpiration);
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email de rappel d\'expiration de mot de passe:', emailError);
        }

        // Avertir si le mot de passe expire bientôt
        res.locals.passwordWarning = `Votre mot de passe expirera dans ${passwordStatus.daysUntilExpiration} jours`;
      }

      // Récupérer l'ancienne date de login
      const oldLoginDateISO = user.last_login;
      const oldLoginDate = oldLoginDateISO
      ? new Date(oldLoginDateISO).toLocaleString('fr-FR', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
      : null; // Convertir la date en format FR

      // Mise à jour du champ last_login
      const currentDate = new Date().toISOString(); // Obtient la date actuelle au format ISO
      await dbService.updateUser(user.id, { last_login: currentDate });

      // Récupérer et scanner l'ip pour obtenir la ville du visiteur
      const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
      const ipInfoResponse = await axios.get(`https://ipinfo.io/${ip}?token=5f86e075034206`);
      const ipInfoData = ipInfoResponse.data;

      logger.info('response ip info visiteur :', ipInfoData);

      // Ajouter l'entrée dans l'historique
      await dbService.createLoginHistory({
        user_id: user.id,
        ip_address: ipInfoData.ip,
        city: ipInfoData.city,
        country: ipInfoData.country,
        region: ipInfoData.region,
        postal_code: ipInfoData.postal
      });

      // Ne garder que les 150 dernières entrées
      await dbService.cleanLoginHistory(user.id, 150);

      // Vérifier si l'authentification à deux facteurs est activée
      if (user.two_factor_enabled) {
        // Générer un token pour l'authentification à deux facteurs
        const twoFactorData = await dbService.createTwoFactorToken(user.id);

        if (!twoFactorData) {
          return res.status(500).json({
            message: 'Erreur lors de la génération du code de vérification',
            toastType: 'error'
          });
        }

        // Envoyer l'email avec le code de vérification
        try {
          await sendTwoFactorAuthEmail(user.email, {
            token: twoFactorData.token,
            twoFactorLink: twoFactorData.twoFactorLink
          });

          // Stocker temporairement l'ID de l'utilisateur pour la vérification du code
          res.cookie('two_factor_pending', user.id, {
            ...getAuthCookieOptions(),
            maxAge: 10 * 60 * 1000 // 10 minutes
          });

          // Répondre avec une demande de vérification à deux facteurs
          return res.status(200).json({
            success: true,
            requiresTwoFactor: true,
            message: 'Un code de vérification a été envoyé à votre adresse email',
            toastType: 'info',
            email: user.email,
            maskedEmail: maskEmail(user.email)
          });
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email d\'authentification à deux facteurs:', emailError);
          return res.status(500).json({
            message: 'Erreur lors de l\'envoi du code de vérification',
            toastType: 'error'
          });
        }
      }

      // Si l'authentification à deux facteurs n'est pas activée, continuer normalement
      // Générer les tokens
      const { accessToken, refreshToken } = await tokenService.generateTokens(
        user.id,
        user.email,
        user.user_type
      );

      // Définir les cookies sécurisés
      res.cookie('access_token', accessToken, getAuthCookieOptions());
      res.cookie('refresh_token', refreshToken, getAuthCookieOptions());

      // Envoyer un email de notification de connexion si nécessaire
      try {
        // Vérifier si on peut envoyer un nouvel email de notification de connexion via la DB
        const { canSendEmail, remainingSeconds } = await dbService.checkLastEmailSent(user.id, 'newLogin');

        if (canSendEmail) {
          // Les préférences de notification sont vérifiées dans sendEmailWithPreferenceCheck
          await sendEmailWithPreferenceCheck(
            user.id,
            'connexion', // Assurez-vous que cela correspond à la clé dans notification_preferences
            async () => {
              logger.info('Envoi de l\'email de notification de nouvelle connexion', { userId: user.id, email: user.email });
              await sendLoginNotificationEmail(user.email, ipInfoData.city, ipInfoData.ip, ipInfoData.postal, ipInfoData.country, ipInfoData.region);
            }
          );
        } else {
          logger.info(`Envoi de l'email de notification de connexion limité. Temps restant: ${remainingSeconds}s`, { userId: user.id });
        }
      } catch (error) {
        logger.error('Erreur lors de la tentative d\'envoi de l\'email de notification de connexion:', { error: error instanceof Error ? error.message : 'Erreur inconnue', userId: user.id });
      }

      // Préparer la réponse
      const userResponse = {
        id: user.id,
        email: user.email,
        last_login: oldLoginDate,
        userType: user.user_type,
        email_verifier: user.email_verifier,
        profil_actif: user.profil_actif,
        role: user.role
      };

      const response = {
        user: userResponse,
        token: accessToken,
        success: true,
        toastType: 'success',
        passwordWarning: res.locals.passwordWarning || null // Correction de la déclaration de la propriété passwordWarning
      };

      // Ajouter l'avertissement si le mot de passe expire bientôt
      if (res.locals.passwordWarning) {
        response.passwordWarning = res.locals.passwordWarning;
      }

      // Journaliser l'action de connexion
      await logUserActivity(
        user.id,
        'user_login',
        user.id,
        'user',
        {
          ip_address: getIpFromRequest(req),
          city: ipInfoData?.city,
          country: ipInfoData?.country,
          user_agent: req.headers['user-agent']
        },
        getIpFromRequest(req)
      );

      res.status(200).json(response);
    } catch (error) {
      logger.error('Erreur lors de la connexion:', error);
      res.status(500).json({
        message: 'Une erreur est survenue lors de cette connexion.',
        toastType: 'error'
      });
    }
  }

  async logout(req: Request, res: Response) {
    try {
      const accessToken = req.cookies['access_token'];
      const refreshToken = req.cookies['refresh_token'];
      const normalToken = req.cookies['token'];
      const _token = req.cookies['_token'];

      // Invalider les tokens
      if (accessToken) {
        await tokenService.blacklistToken(accessToken, 'logout');
      }
      if (refreshToken) {
        await tokenService.blacklistToken(refreshToken, 'logout');
      }
      if (normalToken) {
        await tokenService.blacklistToken(normalToken, 'logout');
      }
      if (_token) {
        await tokenService.blacklistToken(_token, 'logout');
      }

      // Options de base pour les cookies
      const cookieOptions = getAuthCookieOptions();

      // Effacer tous les cookies possibles avec différentes configurations
      res.clearCookie('access_token', cookieOptions);
      res.clearCookie('refresh_token', cookieOptions);
      res.clearCookie('token', cookieOptions);
      res.clearCookie('_token', cookieOptions);

      // Si l'utilisateur est authentifié, journaliser l'action de déconnexion
      if (req.user?.userId) {
        await logUserActivity(
          req.user.userId,
          'user_logout',
          req.user.userId,
          'user',
          {
            ip_address: getIpFromRequest(req)
          },
          getIpFromRequest(req)
        );
      }

      return res.status(200).json({
        message: 'Déconnexion réussie',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la déconnexion:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue lors de la déconnexion',
        success: false,
        toastType: 'error'
      });
    }
  }

  async verifyEmail(req: Request, res: Response) {
    try {
      // Récupérer le token depuis les paramètres, le corps de la requête ou la query string
      const token = req.params.token || req.body.token || req.query.token as string;

      logger.info('Tentative de vérification d\'email avec token:', { tokenLength: token ? token.length : 0 });

      if (!token) {
        logger.warn('Token de vérification manquant');
        return res.status(400).json({
          success: false,
          message: 'Token de vérification manquant',
          code: 'TOKEN_MISSING',
          toastType: 'error'
        });
      }

      // Récupérer l'utilisateur AVANT de vérifier le token
      const userRaw = await dbService.getUserByToken(token);
      if (!userRaw) {
        logger.warn('Token de vérification invalide, utilisateur non trouvé', { token: token.substring(0, 10) + '...' });
        return res.status(400).json({
          success: false,
          message: 'Token de vérification invalide ou expiré',
          code: 'TOKEN_INVALID',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Vérifier si l'email est déjà validé et s'il n'y a pas de changement d'email en cours
      if (user.email_verifier && !user.temp_email) {
        // Rediriger ou répondre selon le type de requête
        if (req.method === 'GET') {
          return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?alreadyVerified=true`);
        } else {
          return res.json({
            success: true,
            alreadyVerified: true,
            code: 'ALREADY_VERIFIED',
            message: 'Votre email est déjà vérifié',
            toastType: 'info'
          });
        }
      }

      // Vérifier le token
      const verified = await dbService.verifyEmail(token);
      if (!verified) {
        return res.status(400).json({
          success: false,
          message: 'Token de vérification invalide ou expiré',
          code: 'TOKEN_EXPIRED',
          toastType: 'error'
        });
      }

      // Vérifier s'il s'agit d'un changement d'email
      if (user.temp_email) {
        // Stocker l'ancien email pour notification
        const oldEmail = user.email;

        // Mettre à jour l'email de l'utilisateur
        await dbService.updateUser(verified.user_id, {
          email: user.temp_email,
          temp_email: null,
          email_verifier: true,
          profil_actif: true
        });

        // Envoyer des notifications de changement d'email via la file d'attente
        try {
          // Notification à l'ancienne adresse
          await sendEmailChangeNotification(oldEmail, {
            oldEmail: oldEmail,
            newEmail: user.temp_email
          });

          // Confirmation à la nouvelle adresse
          await sendEmailChangeConfirmation(user.temp_email);

          // Journaliser l'action de vérification d'email
          await logUserActivity(
            verified.user_id,
            'email_verified',
            verified.user_id,
            'user',
            {
              ip_address: getIpFromRequest(req)
            },
            getIpFromRequest(req)
          );

          // Ajouter une notification utilisateur dans la base de données
          try {
            const { data: userNotification, error: notifError } = await dbService.supabase
              .from('user_notifications')
              .insert({
                user_id: verified.user_id,
                type: 'system',
                title: 'Changement d\'email',
                content: `Votre adresse email a été changée avec succès de ${oldEmail} à ${user.temp_email}`,
                link: '',
                is_read: false,
                is_archived: false
              })
              .select()
              .single();

            if (notifError) {
              logger.error('Erreur lors de l\'ajout de notification utilisateur pour changement d\'email:', notifError);
            } else {
              logger.info('Notification ajoutée avec succès pour le changement d\'email');
            }
          } catch (notifError) {
            logger.error('Erreur lors de l\'ajout de notification utilisateur pour changement d\'email:', notifError);
            // Continue execution even if notification fails
          }
        } catch (emailError) {
          logger.error('Erreur lors de l\'ajout des notifications de changement d\'email à la file d\'attente:', emailError);
          // On continue le processus même si l'envoi des notifications échoue
        }

        // Rediriger ou répondre selon le type de requête
        if (req.method === 'GET') {
          return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?emailChanged=true`);
        } else {
          return res.json({
            success: true,
            emailChanged: true,
            alreadyVerified: false,
            message: 'Votre adresse email a été mise à jour avec succès',
            toastType: 'success'
          });
        }
      } else {
        // Activation normale du compte pour une nouvelle inscription
        await dbService.updateUser(verified.user_id, {
          email_verifier: true,
          profil_actif: true
        });

        // Journaliser l'action de vérification d'email
        await logUserActivity(
          verified.user_id,
          'email_verified',
          verified.user_id,
          'user',
          {
            ip_address: getIpFromRequest(req)
          },
          getIpFromRequest(req)
        );

        // Ajouter une notification utilisateur dans la base de données
        try {
          const { data: userNotification, error: notifError } = await dbService.supabase
            .from('user_notifications')
            .insert({
              user_id: verified.user_id,
              type: 'system',
              title: 'Email vérifié',
              content: 'Votre adresse email a été vérifiée avec succès. Votre compte est maintenant activé.',
              link: '',
              is_read: false,
              is_archived: false
            })
            .select()
            .single();

          if (notifError) {
            logger.error('Erreur lors de l\'ajout de notification utilisateur pour vérification d\'email:', notifError);
          } else {
            logger.info('Notification ajoutée avec succès pour la vérification d\'email');
          }
        } catch (notifError) {
          logger.error('Erreur lors de l\'ajout de notification utilisateur pour vérification d\'email:', notifError);
          // Continue execution even if notification fails
        }

        // Envoyer l'email de bienvenue
        try {
          await sendWelcomeEmail(user.email);
        } catch (emailError: any) {
          logger.error('Erreur lors de l\'envoi de l\'email de bienvenue:', emailError);
          // On continue le processus même si l'email de bienvenue échoue
          if (emailError.details?.lastError?.includes('Domain not found')) {
            res.status(400).json({
              success: false,
              message: 'L\'adresse email fournie n\'est pas valide. Veuillez vérifier le domaine.',
              code: 'INVALID_EMAIL_DOMAIN',
              toastType: 'error'
            });
          }
        }

        // Rediriger ou répondre selon le type de requête
        if (req.method === 'GET') {
          return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?verified=true`);
        } else {
          return res.json({
            success: true,
            alreadyVerified: false,
            message: 'Votre email a été vérifié avec succès',
            toastType: 'success'
          });
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la vérification d\'email:', error);

      // Gérer la redirection en cas d'erreur pour les requêtes GET
      if (req.method === 'GET') {
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?verified=false`);
      }

      // Pour les requêtes POST, renvoyer une réponse JSON avec l'erreur
      res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la vérification de l\'email',
        code: 'VERIFICATION_ERROR',
        toastType: 'error'
      });
    }
  }

  async resendVerification(req: Request, res: Response) {
    try {
      const { email } = req.body;

      logger.info('🔍 Tentative de renvoi de l\'email de vérification', { email });

      if (!email) {
        logger.warn('❌ Aucun email fourni');
        res.status(400).json({
          message: 'Email requis',
          toastType: 'error'
        });
        return;
      }

      // Vérifier si l'utilisateur existe
      const userRaw = await dbService.getUserByEmail(email);
      if (!userRaw) {
        logger.warn('❌ Utilisateur non trouvé', { email });
        return res.status(404).json({
          message: 'Utilisateur non trouvé',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Vérifier si l'email est déjà vérifié
      if (user.email_verifier) {
        logger.warn('⚠️ Email déjà vérifié', { email });
        res.status(400).json({
          message: 'Cet email est déjà vérifié',
          toastType: 'warning'
        });
        return;
      }

      // Vérifier si on peut envoyer un nouvel email de vérification pour l'email indiqué
      const { canSendEmail, remainingSeconds } = await dbService.checkLastEmailSent(user.id, 'email_verification');

      if (!canSendEmail && remainingSeconds) {
        logger.warn('⏳ Cooldown actif', { email, remainingSeconds });
        return res.status(429).json({
          message: `Un email de vérification a déjà été envoyé à ${email}. Merci de consulter votre boîte mail. Vous pourrez demander un nouvel envoi dans ${Math.ceil(remainingSeconds / 60)} minutes.`,
          toastType: 'warning'
        });
      }

      // Générer un nouveau token de vérification
      const { verificationLink, token: verificationToken } = await dbService.createEmailVerification(user.id);
      logger.info('🔑 Nouveau token de vérification généré', {
        userId: user.id,
        tokenLength: verificationToken.length,
        verificationLink
      });

      try {
        // Envoyer l'email de vérification
        await sendVerificationEmail(email, {
          token: verificationToken,
          verificationLink: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${verificationToken}`
        });
        logger.info('✅ Email de vérification envoyé avec succès', {
          email,
          verificationLink
        });

        // Journaliser l'action de demande de renvoi d'email de vérification
        await logUserActivity(
          user.id,
          'email_verification_resend',
          user.id,
          'user',
          {
            email: email,
            verification_requested: true
          },
          getIpFromRequest(req)
        );

        res.status(200).json({
          message: 'Email de vérification envoyé avec succès',
          verificationLink,
          toastType: 'success'
        });
      } catch (emailError) {
        logger.error('❌ Erreur lors de l\'envoi de l\'email', {
          email,
          error: emailError instanceof Error ? emailError.message : emailError,
          errorStack: emailError instanceof Error ? emailError.stack : 'Pas de stack trace'
        });

        res.status(500).json({
          message: 'Une erreur est survenue lors de l\'envoi de l\'email de vérification. Ressayez plus tard',
          toastType: 'error'
        });
      }
    } catch (error) {
      logger.error('❌ Erreur lors du renvoi de l\'email de vérification', {
        error: error instanceof Error ? error.message : error,
        errorStack: error instanceof Error ? error.stack : 'Pas de stack trace'
      });
      res.status(500).json({
        message: 'Une erreur est survenue lors de l\'envoi de l\'email de vérification. Ressayez plus tard',
        toastType: 'error'
      });
    }
  }

  async forgotPassword(req: Request, res: Response) {
    try {
      const { email } = req.body;

      // Vérifier si l'utilisateur existe
      const userRaw = await dbService.getUserByEmail(email);
      if (!userRaw) {
        return res.status(200).json({
          message: 'Si une adresse email existe, vous allez recevoir un email avec les instructions de réinitialisation de mot de passe.',
          toastType: 'warning'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Si l'email n'est pas vérifié, on redirige vers la vérification d'email
      if (user.email_verifier === false) {
        return res.status(429).json({
          message: 'Votre email n\'est pas encore vérifié. Veuillez verifier votre email avant de poursuivre.',
          requiresVerification: true,
          toastType: 'warning'
        });
      }

      // Vérifier si on peut envoyer un nouvel email de réinitialisation
      const { canSendEmail, remainingSeconds } = await dbService.checkLastEmailSent(user.id, 'password_reset');

      if (!canSendEmail && remainingSeconds !== undefined) {
        return res.status(429).json({
          message: `Un email de réinitialisation a déjà été envoyé à ${email}. Merci de consulter votre boîte mail. Vous pourrez demander un nouvel envoi dans ${Math.ceil(remainingSeconds / 60)} minutes si nécessaire.`,
          toastType: 'warning'
        });
      }

      // Créer un token de réinitialisation
      const { resetLink } = await dbService.createPasswordReset(user.id);

      const urlParams = new URLSearchParams(new URL(resetLink).search);
      const resetToken = urlParams.get('token');

      // Vérification si resetToken est null
      if (!resetToken) {
        return res.status(400).json({
          message: 'Le token de réinitialisation est invalide.',
          toastType: 'error'
        });
      }

      // Envoyer l'email avec le token
      await sendPasswordResetEmail(user.email, resetToken);

      // Journaliser l'action de demande de réinitialisation de mot de passe
      await logUserActivity(
        user.id,
        'password_reset_request',
        user.id,
        'user',
        {
          email: user.email,
          reset_requested: true
        },
        getIpFromRequest(req)
      );

      return res.status(200).json({
        message: 'Un email de réinitialisation a été envoyé à votre adresse ' + user.email,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la réinitialisation du mot de passe:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue lors de l\'envoi de l\'email de réinitialisation. Ressayez plus tard',
        toastType: 'error'
      });
    }
  }

  async resetPassword(req: Request, res: Response) {
    try {
      const { token, password } = req.body;

      try {
        // Vérifier et supprimer le token
        const reset = await dbService.verifyPasswordReset(token, true);

        if (!reset) {
          return res.status(400).json({
            message: 'Token de réinitialisation invalide ou expiré',
            toastType: 'error'
          });
        }

        // Récupérer l'utilisateur
        const userRaw = await dbService.getUserById(reset.user_id);
        if (!userRaw) {
          logger.error('Utilisateur non trouvé', { userId: reset.user_id });
          throw new Error('USER_NOT_FOUND');
        }

        // Décrypter les données utilisateur
        const user = await decryptUserDataAsync(userRaw);

        // Hacher le nouveau mot de passe
        const passwordHash = await hashPassword(password);

        // Mettre à jour le mot de passe et sa date d'expiration
        const oneYearFromNow = new Date();
        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

        await dbService.updatePassword(reset.user_id, passwordHash, oneYearFromNow);

        // Ajouter une notification utilisateur dans la base de données
        try {
          const { data: userNotification, error: notifError } = await dbService.supabase
            .from('user_notifications')
            .insert({
              user_id: reset.user_id,
              type: 'system',
              title: 'Mot de passe réinitialisé',
              content: 'Votre mot de passe a été réinitialisé avec succès. Si vous n\'êtes pas à l\'origine de cette action, veuillez contacter notre support immédiatement.',
              link: '',
              is_read: false,
              is_archived: false
            })
            .select()
            .single();

          if (notifError) {
            logger.error('Erreur lors de l\'ajout de notification utilisateur pour réinitialisation de mot de passe:', notifError);
          } else {
            logger.info('Notification ajoutée avec succès pour la réinitialisation de mot de passe');
          }
        } catch (notifError) {
          logger.error('Erreur lors de l\'ajout de notification utilisateur pour réinitialisation de mot de passe:', notifError);
          // Continue execution even if notification fails
        }

        // Envoyer l'email de confirmation
        try {
          await sendPasswordChangedEmail(user.email);
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email de confirmation:', emailError);
        }

        // Réinitialiser les tentatives de connexion
        await dbService.resetLoginAttempts(reset.user_id);

        // Journaliser l'action de réinitialisation de mot de passe
        await logUserActivity(
          reset.user_id,
          'password_reset',
          reset.user_id,
          'user',
          {
            ip_address: getIpFromRequest(req)
          },
          getIpFromRequest(req)
        );

        // Générer les tokens
        const { accessToken, refreshToken } = await tokenService.generateTokens(
          user.id,
          user.email,
          user.user_type
        );

        // Définir les cookies sécurisés
        res.cookie('access_token', accessToken, getAuthCookieOptions());
        res.cookie('refresh_token', refreshToken, getAuthCookieOptions());

        return res.status(200).json({
          message: 'Votre mot de passe a été mis à jour avec succès',
          toastType: 'success'
        });

      } catch (error: any) {
        logger.error('Erreur lors de la réinitialisation', { error });
        if (error.message === 'TOKEN_EXPIRED' || error.message === 'TOKEN_NOT_FOUND') {
          return res.status(400).json({
            message: 'Le lien de réinitialisation est invalide ou a expiré. Veuillez faire une nouvelle demande.',
            toastType: 'error'
          });
        }
        throw error;
      }

    } catch (error) {
      logger.error('Erreur système lors de la réinitialisation du mot de passe:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue lors de la réinitialisation du mot de passe. Ressayez plus tard',
        toastType: 'error'
      });
    }
  }

  async validateResetToken(req: Request, res: Response) {
    try {
      const { token } = req.body;

      await dbService.verifyPasswordReset(token);

      return res.json({
        valid: true,
        message: 'Token valide'
      });

    } catch (error) {
      logger.error('Erreur lors de la validation du token:', error);

      let message = 'Une erreur est survenue lors de la validation du token. Réessayez plus tard.';

      if (error instanceof Error) {
        switch (error.message) {
          case 'TOKEN_MISSING':
            message = 'Le token de réinitialisation est manquant.';
            break;
          case 'TOKEN_INVALID_FORMAT':
            message = 'Le format du token est invalide.';
            break;
          case 'TOKEN_NOT_FOUND':
            message = 'Le token de réinitialisation n\'existe pas ou a déjà été utilisé.';
            break;
          case 'TOKEN_EXPIRED':
            message = 'Le token de réinitialisation a expiré.';
            break;
          case 'DATABASE_ERROR':
            message = 'Une erreur technique est survenue. Réessayez plus tard.';
            break;
        }
      }

      return res.status(400).json({
        valid: false,
        message
      });
    }
  }

  async me(req: Request, res: Response) {
    try {
      // Récupérer l'utilisateur à partir du token décodé
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false
        });
      }

      // Récupérer les informations de l'utilisateur depuis la base de données
      const userRaw = await dbService.getUserById(userId);

      if (!userRaw) {
        return res.status(404).json({
          message: 'Utilisateur non trouvé',
          success: false
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Retourner les informations de l'utilisateur (sans données sensibles)
      return res.status(200).json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          profil_actif: user.profil_actif,
          role: user.role,
          nom: user.profil?.data?.nom || null,
          prenom: user.profil?.data?.prenom || null,
          slug: user.profil?.data?.slug || null, // Pour la redirection vers le profil public ou privé
          date_inscription: user.date_inscription, // Ajouter la date d'inscription pour la bannière SEO
        }
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', error);
      return res.status(500).json({
        message: 'Erreur interne du serveur',
        success: false
      });
    }
  }

  // Permet de vérifier si le profil est actif lorsqu'il est authentifié et navigue sur le site et de le déconnecter si le profil vient d'être désactivé (suspendu, banni, supprimé etc pour la modération)
  async verifyProfil(req: Request, res: Response) {
    try {
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({
          message: 'ID utilisateur manquant',
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer l'utilisateur depuis la base de données
      const userRaw = await dbService.getUserById(userId);

      if (!userRaw) {
        return res.status(404).json({
          message: 'Utilisateur non trouvé',
          code: 'USER_NOT_FOUND',
          success: false,
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Vérifier si le profil est actif
      if (!user.profil_actif) {
        return res.status(403).json({
          message: 'Votre profil a été désactivé. Contactez le support pour plus d\'informations.',
          code: 'PROFIL_DISABLED',
          success: false,
          toastType: 'error'
        });
      }

      // Si tout est OK, retourner les informations de l'utilisateur
      return res.status(200).json({
        success: true,
        user: {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          profil_actif: user.profil_actif,
          role: user.role,
          status: 'ACTIVE',
          nom: user.profil?.data?.nom || null,
          prenom: user.profil?.data?.prenom || null
        }
      });

    } catch (error) {
      logger.error('Erreur lors de la vérification du profil:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue lors de la vérification du profil',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Nouvelle méthode pour la redirection vers Google OAuth
  async googleAuth(req: Request, res: Response) {
    // Cette méthode est gérée par passport.authenticate dans les routes
    // Pas besoin d'ajouter de code ici
  }

  // Méthode pour vérifier le code d'authentification à deux facteurs
  async verifyTwoFactorAuth(req: Request, res: Response) {
    try {
      const { token } = req.body;

      // Récupérer l'ID de l'utilisateur soit du cookie (lors de la connexion)
      // soit de l'utilisateur authentifié (lors de l'activation dans les paramètres)
      let userId = req.cookies['two_factor_pending'];

      // Si l'ID n'est pas dans le cookie, essayer de le récupérer de l'utilisateur authentifié
      if (!userId && req.user && req.user.userId) {
        userId = req.user.userId;
      }

      if (!token) {
        return res.status(400).json({
          success: false,
          message: 'Code de vérification manquant',
          toastType: 'error'
        });
      }

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: 'Session de vérification expirée ou invalide',
          toastType: 'error'
        });
      }

      // Vérifier le token
      const verified = await dbService.verifyTwoFactorToken(token);
      if (!verified) {
        return res.status(400).json({
          success: false,
          message: 'Code de vérification invalide ou expiré',
          toastType: 'error'
        });
      }

      // Vérifier que le token appartient à l'utilisateur
      if (verified.user_id !== userId) {
        return res.status(400).json({
          success: false,
          message: 'Code de vérification invalide pour cet utilisateur',
          toastType: 'error'
        });
      }

      // Récupérer l'utilisateur
      const userRaw = await dbService.getUserById(userId);
      if (!userRaw) {
        return res.status(400).json({
          success: false,
          message: 'Utilisateur non trouvé',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Vérifier si c'est une vérification lors de la connexion ou lors de l'activation
      // Lors de l'activation, l'utilisateur est déjà connecté et a activé 2FA mais n'a pas encore vérifié
      // Lors de la connexion, l'utilisateur a déjà activé et vérifié 2FA auparavant

      // Récupérer l'utilisateur pour vérifier son statut 2FA
      const userForStatusRaw = await dbService.getUserById(userId);
      const userForStatus = userForStatusRaw ? await decryptUserDataAsync(userForStatusRaw) : null;

      // Si 2FA est activé mais pas encore vérifié, c'est une activation initiale
      const isActivation = userForStatus?.two_factor_enabled === true && userForStatus?.two_factor_verified === false;

      logger.info('Vérification du type d\'authentification à deux facteurs', {
        isActivation,
        hasUser: !!req.user,
        userId: req.user?.userId || 'non défini',
        cookieUserId: userId,
        twoFactorEnabled: userForStatus?.two_factor_enabled,
        twoFactorVerified: userForStatus?.two_factor_verified
      });

      if (isActivation) {
        // Marquer l'authentification à deux facteurs comme vérifiée
        await dbService.verifyTwoFactorAuth(userId);

        // Récupérer l'utilisateur pour obtenir son email et ses informations
        const userDetailsRaw = await dbService.getUserById(userId);
        const userDetails = userDetailsRaw ? await decryptUserDataAsync(userDetailsRaw) : null;
        logger.info('Envoi de l\'email de vérification 2FA', {
          userId,
          hasEmail: !!userDetails?.email,
          emailMasked: userDetails?.email ? maskEmail(userDetails.email) : null,
          hasProfile: !!userDetails?.profil,
          firstName: userDetails?.profil?.data?.prenom || 'utilisateur'
        });

        if (userDetails && userDetails.email) {
          try {
            // Envoyer un email de confirmation de vérification
            const { sendTwoFactorVerifiedEmail } = await import('../services/emailServiceTwoFactorEmail');
            await sendTwoFactorVerifiedEmail({
              email: userDetails.email,
              firstName: userDetails.profil?.data?.prenom || 'utilisateur',
              lastName: userDetails.profil?.data?.nom || '',
              ip: getIpFromRequest(req),
              userAgent: req.headers['user-agent']
            });
            logger.info('Email de vérification 2FA envoyé avec succès', { userId });

            // Ajouter une notification
            const { createTwoFactorVerifiedNotification } = await import('../services/twoFactorNotificationService');
            await createTwoFactorVerifiedNotification(userId);
            logger.info('Notification de vérification 2FA créée avec succès', { userId });
          } catch (error) {
            logger.error('Erreur lors de l\'envoi de l\'email ou de la création de la notification de vérification 2FA', {
              userId,
              error: error instanceof Error ? error.message : 'Erreur inconnue',
              stack: error instanceof Error ? error.stack : undefined
            });
          }
        }

        // Journaliser l'action de vérification de 2FA
        await logUserActivity(
          userId,
          'two_factor_verified',
          userId,
          'user',
          {
            ip_address: getIpFromRequest(req)
          },
          getIpFromRequest(req)
        );

        // Ajouter une entrée dans l'historique de l'utilisateur
        const { addTwoFactorVerifiedToHistory } = await import('../services/twoFactorNotificationService');
        await addTwoFactorVerifiedToHistory(userId);

        // Nous avons déjà envoyé un email de vérification et une notification plus haut,
        // donc nous n'avons pas besoin d'envoyer un autre email d'activation ici.

        // Répondre avec succès
        res.status(200).json({
          success: true,
          message: 'Authentification à deux facteurs vérifiée avec succès',
          toastType: 'success'
        });
      } else {
        // C'est une vérification lors de la connexion
        // Générer les tokens
        const { accessToken, refreshToken } = await tokenService.generateTokens(
          user.id,
          user.email,
          user.user_type
        );

        // Définir les cookies sécurisés
        res.cookie('access_token', accessToken, getAuthCookieOptions());
        res.cookie('refresh_token', refreshToken, getAuthCookieOptions());

        // Supprimer le cookie temporaire
        res.clearCookie('two_factor_pending', getAuthCookieOptions());

        // Journaliser l'action de connexion avec 2FA
        await logUserActivity(
          user.id,
          'user_login_2fa',
          user.id,
          'user',
          {
            ip_address: getIpFromRequest(req),
            user_agent: req.headers['user-agent']
          },
          getIpFromRequest(req)
        );

        // Ajouter une entrée dans l'historique de l'utilisateur
        const { addTwoFactorVerifiedToHistory } = await import('../services/twoFactorNotificationService');
        await addTwoFactorVerifiedToHistory(user.id);

        // Préparer la réponse
        const userResponse = {
          id: user.id,
          email: user.email,
          userType: user.user_type,
          email_verifier: user.email_verifier,
          profil_actif: user.profil_actif,
          role: user.role
        };

        res.status(200).json({
          user: userResponse,
          token: accessToken,
          success: true,
          message: 'Authentification à deux facteurs réussie',
          toastType: 'success'
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'authentification à deux facteurs:', error);
      res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la vérification',
        toastType: 'error'
      });
    }
  }

  // Méthode pour activer l'authentification à deux facteurs
  async enableTwoFactorAuth(req: Request, res: Response) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      const userId = req.user.userId;

      // Activer l'authentification à deux facteurs
      const success = await dbService.enableTwoFactorAuth(userId);
      if (!success) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de l\'activation de l\'authentification à deux facteurs',
          toastType: 'error'
        });
      }

      // Générer un token pour la vérification initiale
      const twoFactorData = await dbService.createTwoFactorToken(userId);
      if (!twoFactorData) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération du code de vérification',
          toastType: 'error'
        });
      }

      // Récupérer l'email de l'utilisateur
      const userRaw = await dbService.getUserById(userId);
      if (!userRaw) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations utilisateur',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);
      if (!user.email) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de l\'email utilisateur',
          toastType: 'error'
        });
      }

      // Envoyer l'email avec le code de vérification
      const { sendTwoFactorVerificationEmail } = await import('../services/emailServiceTwoFactorEmail');
      await sendTwoFactorVerificationEmail({
        email: user.email,
        firstName: user.profil?.data?.prenom || 'utilisateur',
        lastName: user.profil?.data?.nom || '',
        token: twoFactorData.token,
        twoFactorLink: twoFactorData.twoFactorLink,
        ip: getIpFromRequest(req),
        userAgent: req.headers['user-agent']
      });

      // Définir un cookie pour la vérification
      res.cookie('two_factor_pending', userId, getAuthCookieOptions());

      // Journaliser l'action
      await logUserActivity(
        userId,
        'two_factor_enabled',
        userId,
        'user',
        {
          ip_address: getIpFromRequest(req)
        },
        getIpFromRequest(req)
      );

      // Ajouter une entrée dans l'historique de l'utilisateur
      const { addTwoFactorEnabledToHistory } = await import('../services/twoFactorNotificationService');
      await addTwoFactorEnabledToHistory(userId);

      res.status(200).json({
        success: true,
        message: 'Authentification à deux facteurs activée. Un code de vérification a été envoyé à votre adresse email.',
        toastType: 'success',
        email: user.email,
        maskedEmail: maskEmail(user.email)
      });
    } catch (error) {
      logger.error('Erreur lors de l\'activation de l\'authentification à deux facteurs:', error);
      res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de l\'activation',
        toastType: 'error'
      });
    }
  }

  // Méthode pour désactiver l'authentification à deux facteurs
  async disableTwoFactorAuth(req: Request, res: Response) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      const userId = req.user.userId;

      // Récupérer l'utilisateur pour obtenir son email et ses informations
      const userRaw = await dbService.getUserById(userId);
      if (!userRaw) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations utilisateur',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);
      if (!user.email) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de l\'email utilisateur',
          toastType: 'error'
        });
      }

      // Désactiver l'authentification à deux facteurs
      const success = await dbService.disableTwoFactorAuth(userId);
      if (!success) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la désactivation de l\'authentification à deux facteurs',
          toastType: 'error'
        });
      }

      // Journaliser l'action
      await logUserActivity(
        userId,
        'two_factor_disabled',
        userId,
        'user',
        {
          ip_address: getIpFromRequest(req)
        },
        getIpFromRequest(req)
      );

      // Ajouter une entrée dans l'historique de l'utilisateur
      const {
        addTwoFactorDisabledToHistory,
        createTwoFactorDisabledNotification
      } = await import('../services/twoFactorNotificationService');

      await addTwoFactorDisabledToHistory(userId);
      await createTwoFactorDisabledNotification(userId);

      // Envoyer un email de confirmation de désactivation
      const { sendTwoFactorDisabledEmail } = await import('../services/emailServiceTwoFactorEmail');
      await sendTwoFactorDisabledEmail({
        email: user.email,
        firstName: user.profil?.data?.prenom || 'utilisateur',
        lastName: user.profil?.data?.nom || '',
        ip: getIpFromRequest(req),
        userAgent: req.headers['user-agent']
      });

      res.status(200).json({
        success: true,
        message: 'Authentification à deux facteurs désactivée',
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la désactivation de l\'authentification à deux facteurs:', error);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la désactivation',
        toastType: 'error'
      });
    }
  }

  // Méthode pour vérifier l'état de l'authentification à deux facteurs
  async getTwoFactorStatus(req: Request, res: Response) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      const userId = req.user.userId;

      // Récupérer l'utilisateur
      const userRaw = await dbService.getUserById(userId);
      if (!userRaw) {
        return res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé',
          toastType: 'error'
        });
      }

      // Décrypter les données utilisateur
      const user = await decryptUserDataAsync(userRaw);

      // Masquer l'email pour l'affichage
      const maskedEmail = user.email ? maskEmail(user.email) : null;

      res.status(200).json({
        success: true,
        enabled: user.two_factor_enabled || false,
        verified: user.two_factor_verified || false,
        maskedEmail: maskedEmail
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération du statut de l\'authentification à deux facteurs:', error);
      res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la récupération du statut',
        toastType: 'error'
      });
    }
  }

  // Méthode pour gérer le callback de Google OAuth
  async googleCallback(req: Request, res: Response) {
    try {
      // Authentification gérée par middleware Passport
      // À ce stade, l'utilisateur est déjà authentifié

      // Récupérer l'utilisateur depuis la requête (ajouté par Passport)
      const user = req.user as any;

      if (!user) {
        logger.error('Google callback: No user found in request');
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/login?error=google_auth_failed`);
      }

      logger.info('Google authentication successful', {
        userId: user.id,
        email: user.email
      });

      // Générer les tokens d'accès et de rafraîchissement
      const tokens = await tokenService.generateTokens(user.id, user.email, user.user_type);

      // Définir les cookies avec les tokens
      res.cookie('access_token', tokens.accessToken, getAuthCookieOptions());
      res.cookie('refresh_token', tokens.refreshToken, getAuthCookieOptions());

      // Ajouter le token dans l'URL pour les navigateurs qui bloquent les cookies tiers
      return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard?token=${tokens.accessToken}`);
    } catch (error) {
      logger.error('Google callback error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/login?error=google_auth_failed`);
    }
  }
}
