/* Optimiser  et compresse les images sur le backend avant l'upload par default en JPEG, le fait aussi sur le frontend, on garde les deux par mesure de sécurité */

import sharp from 'sharp';

interface ProcessedImage {
  buffer: Buffer;
  format: string;
  width: number;
  height: number;
}

// Fonction utilitaire pour générer un overlay "IA"
const createIaTagOverlay = async (width: number, height: number): Promise<Buffer> => {
  // Largeur du tag = 15% de l'image, hauteur fixe
  const tagWidth = Math.round(width * 0.20);
  const tagHeight = 30;
  const tagX = Math.round((width - tagWidth) / 2); // Centré horizontalement
  const tagY = height - tagHeight - 36; // 36px de marge en bas
  // SVG : rectangle orange + texte IA centré + jobpartiel.fr en bas à droite
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect x="${tagX}" y="${tagY}" width="${tagWidth}" height="${tagHeight}" rx="16" fill="#FF6B2C" opacity="0.85" />
      <text x="${width/2}" y="${tagY + tagHeight/2}" dominant-baseline="middle" text-anchor="middle" font-size="15" font-family="Arial, Helvetica, sans-serif" font-weight="bold" fill="#FFF" opacity="0.85">Générée par IA</text>
      <text x="${width - 32}" y="${height - 24}" text-anchor="end" font-size="18" font-family="'Segoe UI', Arial, Helvetica, sans-serif" font-weight="600" fill="#FFF" opacity="0.7">jobpartiel.fr</text>
    </svg>
  `;
  return Buffer.from(svg);
};

export const processImage = async (
  buffer: Buffer,
  mimeType: string,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    addIaTag?: boolean; // Ajout du tag IA
  } = {}
): Promise<ProcessedImage> => {
  const {
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 80,
    addIaTag = false,
  } = options;

  try {
    // Obtenir les métadonnées de l'image
    const metadata = await sharp(buffer).metadata();

  if (!metadata.width || !metadata.height) {
    throw new Error('Impossible de lire les dimensions de l\'image');
  }

  // Calculer les nouvelles dimensions en conservant le ratio
  let width = metadata.width;
  let height = metadata.height;

  if (width > maxWidth) {
    height = Math.round((height * maxWidth) / width);
    width = maxWidth;
  }

  if (height > maxHeight) {
    width = Math.round((width * maxHeight) / height);
    height = maxHeight;
  }

  // Déterminer le format de sortie
  const outputFormat = determineOutputFormat(mimeType);
  let processedImage = sharp(buffer).resize(width, height, {
    fit: 'inside',
    withoutEnlargement: true
  });

  // Ajout du tag IA si demandé
  if (addIaTag) {
    const overlay = await createIaTagOverlay(width, height);
    processedImage = processedImage.composite([
      { input: overlay, top: 0, left: 0 }
    ]);
  }

  // Appliquer les options de compression selon le format
  switch (outputFormat) {
    case 'jpeg':
      processedImage = processedImage.jpeg({
        quality,
        progressive: true,
        chromaSubsampling: '4:2:0',
      });
      break;
    case 'png':
      processedImage = processedImage.png({
        quality,
        progressive: true,
        compressionLevel: 9,
      });
      break;
    case 'webp':
      processedImage = processedImage.webp({
        quality,
        lossless: false,
        nearLossless: true,
        smartSubsample: true,
      });
      break;
  }

  const outputBuffer = await processedImage.toBuffer();

  return {
    buffer: outputBuffer,
    format: outputFormat,
    width,
    height
  };
  } catch (error: any) {
    console.error('Erreur lors du traitement de l\'image avec Sharp:', error);

    // En cas d'erreur avec Sharp, retourner l'image originale sans traitement
    // Cela évite que le serveur plante à cause de problèmes de polices ou autres
    const fallbackMetadata = { width: maxWidth, height: maxHeight };

    return {
      buffer: buffer, // Image originale non traitée
      format: determineOutputFormat(mimeType),
      width: fallbackMetadata.width,
      height: fallbackMetadata.height
    };
  }
};

// Fonction pour déterminer le format de sortie optimal
export const determineOutputFormat = (mimeType: string): 'jpeg' | 'png' | 'webp' => {
  if (mimeType.includes('webp')) return 'webp';
  if (mimeType.includes('png')) return 'png';
  return 'jpeg';
};