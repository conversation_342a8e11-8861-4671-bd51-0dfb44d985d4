// Ce fichier est dans le backend !!! 
// Il faut utiliser les api pour faire des requetes !!

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';

// Charger les variables d'environnement depuis le bon chemin
const NODE_ENV = process.env.NODE_ENV || 'development';
dotenv.config({ path: path.resolve(__dirname, `../../.env.${NODE_ENV}`) });

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Supabase configuration missing : missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
}

console.log('Initialisation du client Supabase avec service role key');

export const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);
