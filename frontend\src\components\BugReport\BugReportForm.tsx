import React, { useState, useEffect, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  CircularProgress,
  Snackbar,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid,
  useTheme,
  useMediaQuery,
  InputAdornment,
  Checkbox,
  keyframes
} from '@mui/material';
import {
  BugReportCreateRequest,
  ReportType
} from '../../types/bugReports';
import { bugReportService } from '../../services/bugReportService';
import TitleIcon from '@mui/icons-material/Title';
import SendIcon from '@mui/icons-material/Send';
import LockIcon from '@mui/icons-material/Lock';
import InfoIcon from '@mui/icons-material/Info';
import DescriptionIcon from '@mui/icons-material/Description';
import ListAltIcon from '@mui/icons-material/ListAlt';
import logger from '@/utils/logger';

interface BugReportFormProps {
  initialReportType?: ReportType;
  onClose?: () => void;
}

const BugReportForm: React.FC<BugReportFormProps> = ({ initialReportType = 'bug', onClose }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const [showScrollIndicator, setShowScrollIndicator] = useState(true);
  const formContainerRef = useRef<HTMLDivElement>(null);
  
  // Animation de rebond pour l'indicateur de défilement
  const bounceAnimation = keyframes`
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  `;
  
  // Animation de fondu pour l'indicateur de défilement
  const fadeAnimation = keyframes`
    0% {
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    80% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  `;

  // Effet pour masquer l'indicateur de défilement après 5 secondes ou au premier défilement
  useEffect(() => {
    // Cacher l'indicateur après 5 secondes
    const timer = setTimeout(() => {
      setShowScrollIndicator(false);
    }, 5000);

    // Fonction pour gérer l'événement de défilement
    const handleScroll = () => {
      // Masquer immédiatement l'indicateur dès le premier scroll
      setShowScrollIndicator(false);
      
      // Nettoyage après le premier défilement
      if (formContainerRef.current) {
        formContainerRef.current.removeEventListener('scroll', handleScroll);
      }
      
      // Annuler le timer si l'utilisateur commence à défiler avant les 5 secondes
      clearTimeout(timer);
    };

    // Ajouter l'écouteur d'événement de défilement
    if (formContainerRef.current) {
      formContainerRef.current.addEventListener('scroll', handleScroll);
    }

    // Nettoyage à la destruction du composant
    return () => {
      clearTimeout(timer);
      if (formContainerRef.current) {
        formContainerRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);
  
  const { control, handleSubmit, reset, formState: { errors } } = useForm<BugReportCreateRequest>({
    defaultValues: {
      report_type: initialReportType
    }
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const onSubmit = async (data: BugReportCreateRequest) => {
    try {
      setLoading(true);
      setError(null);
      
      // Ajouter automatiquement les informations du navigateur
      const browserInfo = bugReportService.getBrowserInfo();
      
      // Nettoyer les données en supprimant les champs vides
      const cleanedData = { ...data };
      
      // Supprimer les champs vides pour éviter les erreurs de validation
      if (cleanedData.reproduction_steps === '') {
        delete cleanedData.reproduction_steps;
      }
      
      // S'assurer que is_private est bien un booléen
      cleanedData.is_private = !!cleanedData.is_private;
      
      // Ajouter automatiquement les informations du système d'exploitation
      const enhancedData = {
        ...cleanedData,
        browser_info: browserInfo,
        os_info: browserInfo.os
      };
      
      // Envoyer les données au serveur
      const response = await bugReportService.create(enhancedData);
      setSuccess(true);
      
      // Personnaliser le message de réussite selon la visibilité du rapport
      const privacyMessage = enhancedData.is_private 
        ? "Votre rapport privé a été créé avec succès ! Il n'est visible que par vous et l'équipe de modération."
        : "Votre rapport a été soumis avec succès ! Il sera publié après modération.";
      
      setSuccessMessage(privacyMessage);
      reset();
      
      // Fermer la modal et rediriger vers la page des rapports de bugs après une soumission réussie
      setTimeout(() => {
        if (onClose) onClose();
        navigate('/dashboard/bug-reports');
      }, 1500);
    } catch (err: any) {
      logger.error('Erreur lors de la soumission du rapport de bug:', err);
      setError(err.response?.data?.error || err.response?.data || 'Une erreur est survenue lors de la soumission du rapport');
      setErrorMessage(err.response?.data?.error || 'Une erreur est survenue lors de la soumission du rapport');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box 
      component="form" 
      onSubmit={handleSubmit(onSubmit)} 
      sx={{ 
        width: '100%', 
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        height: 'auto',
        maxHeight: 'none',
        overflow: 'visible',
        position: 'relative',
        '& .MuiTextField-root, & .MuiFormControl-root': {
          mb: { xs: 3, sm: 2.5 }
        },
        '& .MuiOutlinedInput-root': {
          borderRadius: '8px',
          transition: 'all 0.2s ease',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(255, 122, 53, 0.5)'
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(255, 107, 44, 0.7)',
            borderWidth: 1.5,
            boxShadow: '0 0 0 4px rgba(255, 107, 44, 0.1)'
          },
          '& .MuiOutlinedInput-input': {
            padding: '14px 16px'
          },
          '& .MuiOutlinedInput-inputMultiline': {
            minHeight: '40px', // Hauteur minimale pour tous les champs multilignes
            resize: 'vertical', // Permet à l'utilisateur de redimensionner verticalement
            overflow: 'auto' // Permet le défilement dans le champ
          }
        },
        '& .MuiInputLabel-root': {
          fontSize: '0.95rem',
          '&.Mui-focused': {
            color: 'rgba(255, 107, 44, 0.8)'
          }
        },
        '& .MuiRadio-root.Mui-checked': {
          color: 'rgba(255, 107, 44, 0.8)'
        },
        '& .MuiFormHelperText-root': {
          marginTop: '4px',
          fontWeight: 500
        }
      }}
    >
      <Box 
        ref={formContainerRef}
        sx={{ 
          flex: 1, 
          overflowY: 'auto',
          pr: { xs: 0.5, sm: 0 }, 
          pb: { xs: 2, sm: 0 },
          height: '100%',
          position: 'relative',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '10px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
          },
        }}
      >
        {/* Indicateur de défilement */}
        {showScrollIndicator && onClose && (
          <Box
            sx={{
              position: 'absolute',
              bottom: '20px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 20,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              animation: `${fadeAnimation} 5s ease-in-out forwards`,
              pointerEvents: 'none'
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: 'white',
                fontWeight: 600,
                backgroundColor: '#FF6B2C',
                px: 2,
                py: 0.7,
                borderRadius: '20px',
                boxShadow: '0 2px 5px rgba(0,0,0,0.15)',
                mb: 1
              }}
            >
              Faites défiler pour voir plus
            </Typography>
            <Box
              sx={{
                width: '30px',
                height: '30px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                animation: `${bounceAnimation} 2s infinite`,
                color: 'rgba(255, 107, 44, 0.9)',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '50%',
                boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
              }}
            >
              <Box
                component="span"
                sx={{
                  width: '10px',
                  height: '10px',
                  borderRight: '2px solid currentColor',
                  borderBottom: '2px solid currentColor',
                  transform: 'rotate(45deg)',
                  mt: '-5px'
                }}
              />
            </Box>
          </Box>
        )}
        
        <Card sx={{ 
          boxShadow: 'none', 
          backgroundColor: 'transparent'
        }}>
          <CardContent sx={{ p: 0 }}>
            <Box sx={{ mb: 2, mt: 1 }}>
              <Controller
                name="report_type"
                control={control}
                defaultValue="bug"
                rules={{ required: 'Veuillez sélectionner un type de rapport' }}
                render={({ field }) => (
                  <FormControl 
                    component="fieldset" 
                    error={!!errors.report_type}
                    sx={{
                      display: 'none'
                    }}
                  >
                    <FormLabel 
                      component="legend"
                      sx={{
                        color: '#FF6B2C',
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        fontWeight: 600,
                        mb: 1,
                        '&.Mui-focused': {
                          color: '#FF6B2C'
                        }
                      }}
                    >
                      Type de rapport
                    </FormLabel>
                    <RadioGroup 
                      {...field} 
                      row={!isMobile} 
                      sx={{
                        '& .MuiFormControlLabel-root': {
                          mr: { xs: 0, sm: 4 },
                          ml: { xs: 0, sm: 0 },
                          width: { xs: '100%', sm: 'auto' },
                          '& .MuiTypography-root': {
                            fontWeight: 500
                          }
                        },
                        '& .MuiRadio-root': {
                          padding: '8px',
                          '&.Mui-checked': {
                            color: '#FF6B2C'
                          }
                        }
                      }}
                    >
                      <FormControlLabel 
                        value="bug" 
                        control={<Radio size="small" />} 
                        label="Bug" 
                      />
                      <FormControlLabel 
                        value="improvement" 
                        control={<Radio size="small" />} 
                        label="Suggestion d'amélioration" 
                      />
                    </RadioGroup>
                    {errors.report_type && (
                      <FormHelperText error sx={{ mt: 1 }}>{errors.report_type.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Box>

            <Grid container spacing={{ xs: 1.5, sm: 2 }}>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" sx={{ 
                  mb: 0.25, 
                  fontWeight: 600, 
                  color: 'rgba(0, 0, 0, 0.7)',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <InfoIcon sx={{ fontSize: '1rem', mr: 0.5, color: 'rgba(255, 107, 44, 0.8)' }} />
                  Informations principales
                </Typography>
                <Box 
                  sx={{ 
                    height: '2px', 
                    width: '40px', 
                    backgroundColor: '#FF6B2C',
                    mb: 1
                  }} 
                />
              </Grid>

              <Grid size={{ xs: 12 }}>
                <Controller
                  name="title"
                  control={control}
                  defaultValue=""
                  rules={{ 
                    required: 'Veuillez saisir un titre', 
                    minLength: {
                      value: 5,
                      message: 'Le titre doit contenir au moins 5 caractères'
                    },
                    maxLength: {
                      value: 70,
                      message: 'Le titre ne doit pas dépasser 70 caractères'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Titre du rapport"
                      variant="outlined"
                      error={!!errors.title}
                      helperText={errors.title ? errors.title.message : ''}
                      onChange={(e) => {
                        // Capitaliser la première lettre
                        const value = e.target.value;
                        if (value.length > 0) {
                          const capitalized = value.charAt(0).toUpperCase() + value.slice(1);
                          field.onChange(capitalized);
                        } else {
                          field.onChange(value);
                        }
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <TitleIcon sx={{ color: 'rgba(255, 107, 44, 0.7)' }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="category"
                  control={control}
                  defaultValue="interface"
                  rules={{ required: 'La catégorie est requise' }}
                  render={({ field }) => (
                    <FormControl 
                      fullWidth 
                      error={!!errors.category}
                      sx={{
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: 'rgba(255, 107, 44, 0.8)'
                        },
                        '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255, 107, 44, 0.7)',
                          borderWidth: 1.5,
                          boxShadow: '0 0 0 4px rgba(255, 107, 44, 0.1)'
                        }
                      }}
                    >
                      <InputLabel>Catégorie</InputLabel>
                      <Select 
                        {...field} 
                        label="Catégorie"
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              borderRadius: '8px',
                              mt: 1,
                              boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
                            }
                          }
                        }}
                      >
                        <MenuItem value="interface">Interface</MenuItem>
                        <MenuItem value="fonctionnalite">Fonctionnalité</MenuItem>
                        <MenuItem value="paiement">Paiement</MenuItem>
                        <MenuItem value="securite">Sécurité</MenuItem>
                        <MenuItem value="autre">Autre</MenuItem>
                      </Select>
                      {errors.category && (
                        <FormHelperText>{errors.category.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Controller
                  name="priority"
                  control={control}
                  defaultValue="moyenne"
                  rules={{ required: 'La priorité est requise' }}
                  render={({ field }) => (
                    <FormControl 
                      fullWidth 
                      error={!!errors.priority}
                      sx={{
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: 'rgba(255, 107, 44, 0.8)'
                        },
                        '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255, 107, 44, 0.7)',
                          borderWidth: 1.5,
                          boxShadow: '0 0 0 4px rgba(255, 107, 44, 0.1)'
                        }
                      }}
                    >
                      <InputLabel>Priorité</InputLabel>
                      <Select 
                        {...field} 
                        label="Priorité"
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              borderRadius: '8px',
                              mt: 1,
                              boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
                            }
                          }
                        }}
                      >
                        <MenuItem value="faible">Faible</MenuItem>
                        <MenuItem value="moyenne">Moyenne</MenuItem>
                        <MenuItem value="elevee">Élevée</MenuItem>
                        <MenuItem value="critique">Critique</MenuItem>
                      </Select>
                      {errors.priority && (
                        <FormHelperText>{errors.priority.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              {/* Option pour rendre le rapport privé - placée après catégorie et priorité */}
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" sx={{ 
                  mb: 0.75, 
                  mt: { xs: 1, sm: 1.5 },
                  fontWeight: 600, 
                  color: 'rgba(0, 0, 0, 0.7)',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <LockIcon sx={{ fontSize: '1rem', mr: 0.5, color: 'rgba(255, 107, 44, 0.8)' }} />
                  Confidentialité du rapport
                  <Box component="span" sx={{ 
                    ml: 1,
                    fontSize: '0.7rem',
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    px: 0.8,
                    py: 0.1,
                    borderRadius: '4px',
                    fontWeight: 500
                  }}>
                    Important
                  </Box>
                </Typography>
                <Box 
                  sx={{ 
                    height: '2px', 
                    width: '40px', 
                    backgroundColor: '#FF6B2C',
                    mb: 1.5
                  }} 
                />
                
                <Controller
                  name="is_private"
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <Box 
                      sx={{
                        mt: { xs: 0.5, sm: 1 },
                        mb: { xs: 1.5, sm: 2 },
                        display: 'flex',
                        alignItems: 'center',
                        position: 'relative',
                        backgroundColor: field.value ? 'rgba(255, 107, 44, 0.08)' : 'rgba(255, 107, 44, 0.05)',
                        borderRadius: '8px',
                        padding: '10px 14px',
                        border: field.value 
                          ? '2px solid rgba(255, 107, 44, 0.7)' 
                          : '1px solid rgba(255, 107, 44, 0.2)',
                        boxShadow: field.value 
                          ? '0 0 8px rgba(255, 107, 44, 0.15)' 
                          : 'none',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          backgroundColor: field.value 
                            ? 'rgba(255, 107, 44, 0.12)' 
                            : 'rgba(255, 107, 44, 0.1)',
                          borderColor: field.value 
                            ? 'rgba(255, 107, 44, 0.9)' 
                            : 'rgba(255, 107, 44, 0.3)'
                        }
                      }}
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.checked);
                            }}
                            sx={{
                              color: field.value ? 'rgba(255, 107, 44, 0.9)' : 'rgba(255, 107, 44, 0.7)',
                              '&.Mui-checked': {
                                color: 'rgba(255, 107, 44, 0.9)',
                              },
                              padding: '4px',
                              marginRight: '4px'
                            }}
                          />
                        }
                        label={
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LockIcon sx={{ fontSize: '1rem', marginRight: '4px', color: field.value ? 'rgba(255, 107, 44, 0.9)' : 'rgba(255, 107, 44, 0.7)' }} />
                              <Typography
                                component="span"
                                sx={{
                                  fontSize: { xs: '0.85rem', sm: '0.9rem' },
                                  fontWeight: 500,
                                  color: field.value ? 'rgba(0, 0, 0, 0.85)' : 'rgba(0, 0, 0, 0.75)'
                                }}
                              >
                                Rapport privé
                              </Typography>
                            </Box>
                            <Typography
                              component="span"
                              sx={{
                                fontSize: { xs: '0.75rem', sm: '0.8rem' },
                                fontWeight: 400,
                                color: field.value ? 'rgba(255, 107, 44, 0.8)' : 'rgba(0, 0, 0, 0.6)',
                                ml: 1.5,
                                mt: 0.5,
                                fontStyle: 'italic'
                              }}
                            >
                              {field.value 
                                ? "Ce rapport sera visible uniquement par vous et l'équipe d'administration" 
                                : "Ce rapport sera visible publiquement après modération par l'équipe d'administration"}
                            </Typography>
                          </Box>
                        }
                        sx={{
                          marginLeft: '-8px',
                          marginRight: 0
                        }}
                      />
                    </Box>
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" sx={{ 
                  mb: 0.25, 
                  fontWeight: 600, 
                  color: 'rgba(0, 0, 0, 0.7)',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  mt: { xs: 0.5, sm: 1 },
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <DescriptionIcon sx={{ fontSize: '1rem', mr: 0.5, color: 'rgba(255, 107, 44, 0.8)' }} />
                  Description détaillée
                </Typography>
                <Box 
                  sx={{ 
                    height: '2px', 
                    width: '40px', 
                    backgroundColor: '#FF6B2C',
                    mb: 1
                  }} 
                />
                <Controller
                  name="description"
                  control={control}
                  defaultValue=""
                  rules={{ 
                    required: 'Veuillez saisir une description', 
                    minLength: {
                      value: 10,
                      message: 'La description doit contenir au moins 10 caractères'
                    }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={isMobile ? 4 : 6}
                      minRows={isMobile ? 4 : 6}
                      maxRows={15} // Maximum pour éviter un champ trop grand
                      label="Description détaillée"
                      variant="outlined"
                      error={!!errors.description}
                      helperText={
                        errors.description 
                          ? errors.description.message 
                          : 'Décrivez le problème ou l\'amélioration de manière précise.'
                      }
                      placeholder="Décrivez ici le bug ou votre suggestion en détail..."
                      InputProps={{
                        sx: {
                          '& .MuiOutlinedInput-input': {
                            fontSize: '0.95rem',
                            lineHeight: '1.5',
                            padding: { xs: '10px 14px', sm: '14px 16px' }
                          }
                        }
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid size={{ xs: 12 }}>
                <Controller
                  name="reproduction_steps"
                  control={control}
                  defaultValue=""
                  rules={{ 
                    maxLength: {
                      value: 1000,
                      message: 'Les étapes de reproduction ne doivent pas dépasser 1000 caractères'
                    }
                  }}
                  render={({ field }) => (
                    <>
                      <Typography variant="subtitle2" sx={{ 
                        mb: 0.75, 
                        fontWeight: 600, 
                        color: 'rgba(0, 0, 0, 0.7)',
                        fontSize: { xs: '0.8rem', sm: '0.875rem' },
                        mt: { xs: 1, sm: 1.5 },
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                        <ListAltIcon sx={{ fontSize: '1rem', mr: 0.5, color: 'rgba(255, 107, 44, 0.8)' }} />
                        Étapes de reproduction
                      </Typography>
                      <Box 
                        sx={{ 
                          height: '2px', 
                          width: '40px', 
                          backgroundColor: '#FF6B2C',
                          mb: 1.5
                        }} 
                      />
                      <TextField
                        {...field}
                        label="Étapes pour reproduire le bug (optionnel)"
                        variant="outlined"
                        size="small"
                        multiline
                        rows={isMobile ? 3 : 4}
                        minRows={isMobile ? 3 : 4}
                        maxRows={10}
                        fullWidth
                        error={!!errors.reproduction_steps}
                        helperText={errors.reproduction_steps?.message}
                        onChange={(e) => {
                          // Capitaliser la première lettre de chaque phrase
                          const value = e.target.value;
                          if (value.length > 0) {
                            // Regex pour trouver le début de chaque phrase (après un point, point d'exclamation, etc.)
                            const capitalizedValue = value.replace(
                              /(^\s*|[.!?]\s+)([a-zàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð])/g,
                              (match, separator, letter) => {
                                return separator + letter.toUpperCase();
                              }
                            );
                            field.onChange(capitalizedValue);
                          } else {
                            field.onChange(value);
                          }
                        }}
                        inputProps={{ 
                          maxLength: 1000,
                          style: { minHeight: isMobile ? '60px' : '80px' }
                        }}
                        InputProps={{
                          sx: {
                            backgroundColor: 'white',
                            fontSize: isMobile ? '0.85rem' : '0.9rem',
                            '& .MuiOutlinedInput-input': {
                              overflow: 'auto',
                              resize: 'vertical'
                            }
                          }
                        }}
                      />
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 0.25, mb: 0.5 }}>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: (field.value?.length || 0) > 1000 ? 'error.main' : 'text.secondary',
                            fontSize: '0.7rem'
                          }}
                        >
                          {field.value?.length || 0}/1000 caractères
                        </Typography>
                      </Box>
                    </>
                  )}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>

      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          mt: 3,
          pt: 2,
          pb: 2,
          px: { xs: 0, sm: 1 },
          backgroundColor: 'white',
          borderTop: '1px solid rgba(0, 0, 0, 0.1)',
          boxShadow: '0px -2px 8px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Texte informatif - visible sur toutes les tailles d'écran */}
        <Typography 
          variant="caption" 
          sx={{ 
            color: 'text.secondary',
            fontSize: { xs: '0.65rem', sm: '0.7rem' },
            mb: { xs: 2, sm: 0 },
            textAlign: 'center',
            order: { xs: 1, sm: 2 },
            mt: { sm: 2 },
            display: { xs: 'block', sm: 'none' }
          }}
        >
          * Les champs obligatoires sont marqués
        </Typography>

        {/* Conteneur des boutons */}
        <Box sx={{ 
          display: 'flex',
          order: { xs: 2, sm: 1 },
          justifyContent: 'space-between',
          width: '100%',
        }}>
          {/* Version desktop du texte informatif - aligné à gauche */}
          <Typography 
            variant="caption" 
            sx={{ 
              color: 'text.secondary',
              fontSize: '0.7rem',
              alignSelf: 'center',
              display: { xs: 'none', sm: 'block' }
            }}
          >
            * Les champs obligatoires sont marqués
          </Typography>

          {/* Conteneur des boutons d'action */}
          <Box sx={{ 
            display: 'flex', 
            gap: { xs: 1, sm: 1.5 },
            width: { xs: '100%', sm: 'auto' },
            justifyContent: { xs: 'space-between', sm: 'flex-end' },
            ml: { sm: 'auto' },
            px: { xs: 1, sm: 0 }
          }}>
            {onClose && (
              <Button
                variant="outlined"
                onClick={onClose}
                disabled={loading}
                size={isMobile ? "medium" : "medium"}
                sx={{
                  borderColor: 'rgba(0, 0, 0, 0.23)',
                  color: 'rgba(0, 0, 0, 0.7)',
                  flex: { xs: 1, sm: 'none' },
                  '&:hover': {
                    borderColor: 'rgba(0, 0, 0, 0.5)',
                    backgroundColor: 'rgba(0, 0, 0, 0.04)'
                  }
                }}
              >
                Annuler
              </Button>
            )}
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              size={isMobile ? "medium" : "medium"}
              startIcon={loading ? <CircularProgress size={16} /> : <SendIcon fontSize={isMobile ? "small" : "medium"} />}
              sx={{
                backgroundColor: '#FF6B2C',
                flex: { xs: 1, sm: 'none' },
                '&:hover': {
                  backgroundColor: '#FF7A35'
                },
                fontWeight: 600,
                boxShadow: '0 2px 8px rgba(255, 107, 44, 0.25)',
                py: { xs: 1, sm: 0.7 }
              }}
            >
              {loading ? 'Envoi...' : 'Soumettre'}
            </Button>
          </Box>
        </Box>
      </Box>

      <Snackbar
        open={!!successMessage}
        autoHideDuration={5000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
        ContentProps={{
          sx: {
            backgroundColor: '#4caf50',
            color: 'white'
          }
        }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />

      <Snackbar
        open={!!errorMessage}
        autoHideDuration={5000}
        onClose={() => setErrorMessage(null)}
        message={errorMessage}
        ContentProps={{
          sx: {
            backgroundColor: '#f44336',
            color: 'white'
          }
        }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
  );
};

export default BugReportForm; 