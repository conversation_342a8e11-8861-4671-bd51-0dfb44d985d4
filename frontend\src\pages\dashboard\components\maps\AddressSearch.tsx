import React, { useState } from 'react';
import DOMPurify from 'dompurify';
import { notify } from '@/components/Notification';
import logger from '@/utils/logger';

interface AddressSearchProps {
  onAddressSelect: (center: [number, number]) => void;
  searchAddress: string;
  onSearchAddressChange: (address: string) => void;
  onSearch: () => void;
}

const AddressSearch: React.FC<AddressSearchProps> = ({
  onAddressSelect,
  searchAddress,
  onSearchAddressChange,
  onSearch
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [charCount, setCharCount] = useState(0);
  const [isSearchCooldown, setIsSearchCooldown] = useState(false);

  const handleAddressSearch = async () => {
    if (isSearchCooldown) {
      return;
    }

    try {
      setIsLoading(true);
      setIsSearchCooldown(true);

      const cleanAddress = DOMPurify.sanitize(searchAddress);
      if (!cleanAddress) {
        notify(`Aucune adresse saisie. Caractères saisis : ${charCount}. Veuillez entrer une adresse pour continuer.`, 'error', 12000);
        return;
      }
      logger.info('Adresse saisie:', cleanAddress);
      if (cleanAddress.length < 10) {
        notify(`L'adresse doit contenir au moins 10 caractères. Caractères actuellement saisis : ${charCount}.`, 'error', 12000);
        return;
      }

      // Appel de la fonction onSearch fournie par le parent
      onSearch();

      const response = await fetch(
        `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(cleanAddress)}&limit=1`
      );
      const data = await response.json();

      if (data && data.features && data.features.length > 0) {
        const feature = data.features[0];
        const { coordinates } = feature.geometry;
        const properties = feature.properties;

        // Extraction du numéro de l'adresse saisie
        const numberMatch = cleanAddress.match(/^\d+/);
        const inputNumber = numberMatch ? numberMatch[0] : '';

        // Utilisation directe des détails d'adresse structurés
        const addressInfo = {
          number: inputNumber || properties.housenumber || '',
          road: properties.street || properties.name || '',
          city: properties.city || '',
          postcode: properties.postcode || '',
          state: properties.context || '',
          country: 'France'
        };

        // Validation des champs requis
        if (!addressInfo.road || !addressInfo.city || !addressInfo.postcode) {
          notify('Adresse introuvable. Veuillez vérifier les informations d\'adresse saisie et essayer de nouveau. L\'ortographe est-elle correcte ?', 'error', 12000);
          return;
        }

        // Si tout est OK, on notifie et on envoie les données
        notify('Adresse trouvée : ' + inputNumber + ' ' + addressInfo.road + ', ' + addressInfo.city + ', ' + addressInfo.postcode + ', ' + addressInfo.country + '. Vous pouvez cliquer sur "Enregistrer la zone".', 'success', 20000);
        logger.info('Adresse trouvée:', addressInfo);

        onAddressSelect([coordinates[1], coordinates[0]]); // Inverser lat/lng
      } else {
        logger.warn('Adresse non trouvée dans la réponse de l API. Voici la reponse:', data);
        notify('Adresse introuvable. Veuillez vérifier les informations d\'adresse saisie et essayer de nouveau. L\'ortographe est-elle correcte ?', 'error', 12000);
      }
    } catch (error) {
      logger.warn('Erreur:', error);
      notify('Erreur lors de la recherche. Veuillez essayer plus tard.', 'error', 12000);
    } finally {
      setIsLoading(false);
      // Réinitialisation du cooldown après 2 secondes
      setTimeout(() => {
        setIsSearchCooldown(false);
      }, 1000);
    }
  };

  return (
    <div className="flex flex-col gap-2 mb-4">
      <input
        type="text"
        placeholder="Entrez votre adresse afin de trouver les coordonnées"
        className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent text-black"
        value={searchAddress}
        onChange={(e) => {
          onSearchAddressChange(e.target.value);
          setCharCount(e.target.value.length);
        }}
      />
      <button
        className={`w-full px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] disabled:opacity-50 ${
          isSearchCooldown ? 'cursor-not-allowed' : ''
        }`}
        onClick={handleAddressSearch}
        disabled={isLoading || isSearchCooldown}
      >
        {isSearchCooldown ? 'Recherche en cours ...' : isLoading ? 'Recherche en cours ...' : 'Rechercher'}
      </button>
    </div>
  );
};

export default AddressSearch;
