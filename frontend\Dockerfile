# Build stage
FROM node:20-alpine as build

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apk add --no-cache python3 make g++ vips-dev git

# Copy package files
COPY frontend/package*.json ./

# Nettoyer le cache npm et installer les dépendances
RUN npm cache clean --force && \
    npm install --platform=linuxmusl --arch=x64 sharp && \
    npm install && \
    npm install @rollup/rollup-linux-x64-musl --force

# Copy project files
COPY frontend/ .

# Set environment variables
ARG VITE_API_URL
ENV VITE_API_URL=${VITE_API_URL}

# Build le projet sans la conversion WebP
RUN npm run build

# Production stage
FROM nginx:1.22.1-alpine

# Install required packages
RUN apk add --no-cache curl

# Copier les assets statiques
COPY --from=build /app/dist /usr/share/nginx/html
COPY --from=build /app/public /usr/share/nginx/html/public

# Recursively copy images while preserving directory structure
RUN mkdir -p /usr/share/nginx/html/images && \
    cd /usr/share/nginx/html/public && \
    find . -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.webp" \) -exec sh -c 'mkdir -p "/usr/share/nginx/html/images/$(dirname "{}")"; cp "{}" "/usr/share/nginx/html/images/{}"' \;

# Copy nginx configuration
COPY frontend/nginx.conf /etc/nginx/conf.d/default.conf

# Create directory for nginx pid file
RUN mkdir -p /run/nginx

# Expose port 3000
EXPOSE 3000

# Add healthcheck
HEALTHCHECK --interval=10s --timeout=3s --start-period=3s --retries=10 \
    CMD curl -f http://localhost:3000/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
