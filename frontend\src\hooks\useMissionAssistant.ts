import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { notify } from '../components/Notification';
import axios from 'axios';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../pages/dashboard/services/types';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';

interface UseMissionAssistantProps {
  onStepComplete?: (stepData: any) => void;
  onError?: (error: any) => void;
}

interface UseMissionAssistantReturn {
  isProcessing: boolean;
  progress: number;
  currentStep: string;
  credits: number;
  isRateLimited: boolean;
  generateStep: (step: string, userInput: string, currentData: any) => Promise<any>;
  generateAllSteps: (userInput: string, initialData: any) => Promise<any>;
}

export const useMissionAssistant = ({
  onStepComplete,
  onError
}: UseMissionAssistantProps = {}): UseMissionAssistantReturn => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('Initialisation...');
  const [credits, setCredits] = useState(0);
  const [isRateLimited, setIsRateLimited] = useState(false);

  // Récupérer les crédits IA de l'utilisateur
  useEffect(() => {
    if (user?.id) {
      const fetchCredits = async () => {
        try {
          const headers = await getCommonHeaders();
          const response = await axios.get(`${API_CONFIG.baseURL}/api/ai-credits`, {
            headers,
            withCredentials: true
          });

          if (response.data && response.data.success) {
            setCredits(response.data.credits);
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des crédits IA:', error);
        }
      };

      fetchCredits();
    }
  }, [user?.id]);

  // Fonction pour générer une étape de mission
  const generateStep = useCallback(async (step: string, userInput: string, currentData: any) => {
    if (!user?.id) {
      notify('Vous devez être connecté pour utiliser cette fonctionnalité', 'error');
      return null;
    }

    // Vérifier si l'utilisateur a suffisamment de crédits pour l'étape initiale
    if (step === 'Description du besoin') {
      // La génération complète coûte 5 crédits
      const isFullGeneration = false;
      const creditCost = isFullGeneration ? 5 : 1;

      if (credits < creditCost) {
        notify(`Vous n'avez pas assez de crédits IA. Cette opération nécessite ${creditCost} crédit${creditCost > 1 ? 's' : ''}. Veuillez en acheter dans le menu "Intelligence Artificielle"`, 'error');
        return null;
      }
    }

    setIsProcessing(true);
    setProgress(0);
    setCurrentStep(`Génération de l'étape : ${step}`);

    // Simuler la progression
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + 1, 95);

        // Mettre à jour les étapes en fonction de la progression
        if (newProgress < 20) {
          setCurrentStep(`Analyse de votre demande pour l'étape : ${step}`);
        } else if (newProgress < 40) {
          setCurrentStep(`Traitement des informations pour : ${step}`);
        } else if (newProgress < 60) {
          setCurrentStep(`Génération des données pour : ${step}`);
        } else if (newProgress < 80) {
          setCurrentStep(`Optimisation du résultat pour : ${step}`);
        } else {
          setCurrentStep(`Finalisation de l'étape : ${step}`);
        }

        return newProgress;
      });
    }, 100);

    try {
      const headers = await getCommonHeaders();
      // Déterminer si c'est une génération complète
      const isFullGeneration = false;

      const response = await axios.post(`${API_CONFIG.baseURL}/api/mission-assistant/generate-step`,
        {
          step,
          userInput,
          currentData,
          isFullGeneration
        },
        {
          headers,
          withCredentials: true
        }
      );

      clearInterval(interval);
      setProgress(100);
      setCurrentStep(`Étape ${step} terminée !`);

      // Mettre à jour les crédits si nécessaire
      if (response.data.creditsRemaining !== undefined) {
        setCredits(response.data.creditsRemaining);
      }

      // Attendre un peu pour montrer la progression à 100%
      await new Promise(resolve => setTimeout(resolve, 500));

      setIsProcessing(false);

      if (response.data.success && response.data.content) {
        logger.info('Réponse de l\'API reçue:', response.data);
        logger.info('Contenu brut:', response.data.content);

        // Essayer de parser le contenu JSON
        try {
          // Vérifier si le contenu est déjà un objet (déjà parsé par axios)
          let jsonData;
          if (typeof response.data.content === 'object' && response.data.content !== null) {
            logger.info('Le contenu est déjà un objet JSON');
            jsonData = response.data.content;
          } else {
            logger.info('Tentative de parsing du contenu JSON');
            jsonData = JSON.parse(response.data.content);
          }

          logger.info('Données JSON parsées:', jsonData);

          // Correction pour le champ date_mission vide quand il y a des time_slots
          if (jsonData.has_time_preference === true &&
              jsonData.date_mission === "" &&
              jsonData.time_slots &&
              jsonData.time_slots.length > 0) {
            logger.info('Correction de date_mission vide avec la date du premier créneau');
            jsonData.date_mission = jsonData.time_slots[0].date;
          }

          // Conversion du payment_method pour être compatible avec le backend
          if (jsonData.payment_method) {
            // Convertir 'euros' en 'direct_only' et 'jobi' en 'jobi_only'
            if (jsonData.payment_method === 'euros') {
              logger.info('Conversion de payment_method "euros" en "direct_only"');
              jsonData.payment_method = 'direct_only';
            } else if (jsonData.payment_method === 'jobi') {
              logger.info('Conversion de payment_method "jobi" en "jobi_only"');
              jsonData.payment_method = 'jobi_only';
            }
          }

          // Traitement spécial pour le résumé si c'est un JSON échappé
          if (jsonData.summary && typeof jsonData.summary === 'string') {
            try {
              // Vérifier si le résumé est un JSON échappé
              if (jsonData.summary.startsWith('{') && jsonData.summary.endsWith('}')) {
                logger.info('Détection d\'un résumé au format JSON échappé, tentative de parsing');
                const parsedSummary = JSON.parse(jsonData.summary);

                // Si le parsing a réussi et que c'est un objet avec les mêmes propriétés que nos données
                if (parsedSummary && typeof parsedSummary === 'object' && parsedSummary.titre) {
                  logger.info('Conversion du résumé JSON en texte formaté');

                  // Créer un résumé textuel à partir des données
                  const categoryName = SERVICE_CATEGORIES.find(c => c.id === parsedSummary.category_id)?.nom || 'Non définie';
                  const subcategoryName = SERVICE_SUBCATEGORIES.find(sc => sc.id === parsedSummary.subcategory_id)?.nom || 'Non définie';

                  let formattedSummary = `Vous avez créé une mission de ${subcategoryName} (${categoryName}) intitulée "${parsedSummary.titre}".\n\n`;
                  formattedSummary += `Description: ${parsedSummary.description}\n\n`;

                  // Convertir le payment_method pour l'affichage
                  let paymentMethodText = 'Euros';
                  if (parsedSummary.payment_method === 'jobi' || parsedSummary.payment_method === 'jobi_only') {
                    paymentMethodText = 'Jobi';
                  } else if (parsedSummary.payment_method === 'both') {
                    paymentMethodText = 'Euros ou Jobi';
                  }

                  if (parsedSummary.budget_defini) {
                    formattedSummary += `Budget: ${parsedSummary.budget} ${paymentMethodText} (fixe)\n`;
                  } else {
                    formattedSummary += `Budget estimatif: ${parsedSummary.budget} ${paymentMethodText}\n`;
                  }

                  formattedSummary += `Localisation: ${parsedSummary.adresse}, ${parsedSummary.code_postal} ${parsedSummary.ville}\n\n`;

                  if (parsedSummary.has_time_preference && parsedSummary.time_slots && parsedSummary.time_slots.length > 0) {
                    formattedSummary += `Horaires souhaités:\n`;
                    parsedSummary.time_slots.forEach((slot: any) => {
                      formattedSummary += `- ${slot.date} de ${slot.start} à ${slot.end}\n`;
                    });
                  }

                  // Remplacer le résumé JSON par notre texte formaté
                  jsonData.summary = formattedSummary;
                }
              }
            } catch (e) {
              logger.info('Le résumé n\'est pas un JSON échappé, on le garde tel quel');
            }
          }

          // Vérifier si les données sont valides pour l'étape "Catégorie et service"
          if (step === 'Catégorie et service') {
            logger.info('Vérification des données pour l\'étape Catégorie et service');
            if (!jsonData.category_id || jsonData.category_id === 'ID de la catégorie' ||
                !jsonData.subcategory_id || jsonData.subcategory_id === 'ID de la sous-catégorie') {
              console.error('IDs de catégorie ou sous-catégorie invalides:', jsonData);
              notify('L\'IA n\'a pas pu déterminer la catégorie et la sous-catégorie. Veuillez réessayer avec plus de détails.', 'error');
              if (onError) onError(new Error('IDs invalides'));
              return null;
            }
          }

          // Extraction des informations importantes de la description pour les étapes suivantes
          if (step === 'Description du besoin' && jsonData.description) {
            logger.info('Extraction des informations de la description pour utilisation future');
            const description = jsonData.description.toLowerCase();

            // Extraction de budget mentionné avec support pour différents formats
            const budgetRegex = /(\d+)\s*(?:€|euros|jobi)/gi;
            const budgetMatches = [...description.matchAll(budgetRegex)];

            if (budgetMatches.length > 0) {
              logger.info(`Budgets mentionnés dans la description:`, budgetMatches);

              // Stocker cette information pour l'étape Budget
              if (!jsonData.extracted_data) {
                jsonData.extracted_data = {};
              }

              // Prendre le montant le plus élevé comme référence
              const amounts = budgetMatches.map(match => parseInt(match[1], 10));
              jsonData.extracted_data.budget = Math.max(...amounts);

              // Déterminer la méthode de paiement
              const mentionsJobi = description.includes('jobi');
              const mentionsEuros = description.includes('euro') || description.includes('€');
              const mentionsChoice = description.includes('ou') ||
                                   description.includes('au choix') ||
                                   description.includes('peu importe');

              if ((mentionsJobi && mentionsEuros) || mentionsChoice) {
                jsonData.extracted_data.payment_method = 'both';
              } else if (mentionsJobi) {
                jsonData.extracted_data.payment_method = 'jobi';
              } else if (mentionsEuros) {
                jsonData.extracted_data.payment_method = 'euros';
              }

              logger.info('Méthode de paiement extraite:', jsonData.extracted_data.payment_method);
            }
          }

          if (onStepComplete) {
            onStepComplete(jsonData);
          }
          return jsonData;
        } catch (parseError) {
          console.error('Erreur lors du parsing du contenu JSON:', parseError);
          console.error('Contenu qui a causé l\'erreur:', response.data.content);
          notify('Format de réponse non reconnu', 'error');
          if (onError) onError(parseError);
          return null;
        }
      } else {
        notify(response.data.message || 'Erreur lors de la génération', 'error');
        if (onError) onError(new Error(response.data.message));
        return null;
      }
    } catch (error: any) {
      clearInterval(interval);
      setIsProcessing(false);

      // Vérifier si c'est une erreur de limite de taux
      if (error.response && error.response.status === 429) {
        setIsRateLimited(true);
        notify('Trop de requêtes. Veuillez réessayer dans quelques instants.', 'error');
      } else {
        notify(error.response?.data?.message || 'Erreur lors de la génération', 'error');
      }

      if (onError) onError(error);
      return null;
    }
  }, [user?.id, credits, onStepComplete, onError]);

  // Fonction pour générer toutes les étapes de mission en une fois
  const generateAllSteps = useCallback(async (userInput: string, initialData: any) => {
    if (!user?.id) {
      notify('Vous devez être connecté pour utiliser cette fonctionnalité', 'error');
      return null;
    }

    // La génération complète coûte 5 crédits
    const creditCost = 5;

    if (credits < creditCost) {
      notify(`Vous n'avez pas assez de crédits IA. La génération complète nécessite ${creditCost} crédits. Veuillez en acheter dans le menu "Intelligence Artificielle".`, 'error');
      return null;
    }

    // Définir les étapes à générer dans l'ordre
    const steps = [
      'Description du besoin',
      'Catégorie et service',
      'Budget',
      'Localisation',
      'Horaires'
    ];

    // Initialiser les données accumulées
    let accumulatedData = { ...initialData };
    let finalResult = null;

    try {
      // Générer chaque étape séquentiellement
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];

        setIsProcessing(true);
        setProgress(0);
        setCurrentStep(`Génération de l'étape ${i + 1}/${steps.length}: ${step}`);

        // Pour la première étape, nous devons passer isFullGeneration=true
        // Pour les étapes suivantes, nous utilisons la fonction normale
        let stepResult;

        if (i === 0) {
          // Appeler directement l'API pour la première étape avec isFullGeneration=true
          try {
            setIsProcessing(true);
            setProgress(0);
            setCurrentStep(`Génération de l'étape ${i + 1}/${steps.length}: ${step}`);

            const headers = await getCommonHeaders();
            const response = await axios.post(`${API_CONFIG.baseURL}/api/mission-assistant/generate-step`,
              {
                step,
                userInput,
                currentData: accumulatedData,
                isFullGeneration: true
              },
              {
                headers,
                withCredentials: true
              }
            );

            if (response.data.success && response.data.content) {
              // Mettre à jour les crédits si nécessaire
              if (response.data.creditsRemaining !== undefined) {
                setCredits(response.data.creditsRemaining);
              }

              try {
                // Vérifier si le contenu est déjà un objet (déjà parsé par axios)
                let jsonData;
                if (typeof response.data.content === 'object' && response.data.content !== null) {
                  jsonData = response.data.content;
                } else {
                  jsonData = JSON.parse(response.data.content);
                }

                stepResult = jsonData;
              } catch (parseError) {
                console.error('Erreur lors du parsing du contenu JSON:', parseError);
                notify('Format de réponse non reconnu', 'error');
                if (onError) onError(parseError);
                return null;
              }
            } else {
              notify(response.data.message || 'Erreur lors de la génération', 'error');
              if (onError) onError(new Error(response.data.message));
              return null;
            }
          } catch (error: any) {
            setIsProcessing(false);

            // Vérifier si c'est une erreur de limite de taux
            if (error.response && error.response.status === 429) {
              setIsRateLimited(true);
              notify('Trop de requêtes. Veuillez réessayer dans quelques instants.', 'error');
            } else {
              notify(error.response?.data?.message || 'Erreur lors de la génération', 'error');
            }

            if (onError) onError(error);
            return null;
          }
        } else {
          // Pour les étapes suivantes, utiliser la fonction normale
          stepResult = await generateStep(step, userInput, accumulatedData);
        }

        // Si une étape échoue, arrêter le processus
        if (!stepResult) {
          notify(`Erreur lors de la génération de l'étape: ${step}`, 'error');
          return null;
        }

        // Mettre à jour les données accumulées avec les résultats de cette étape
        accumulatedData = {
          ...accumulatedData,
          ...stepResult
        };

        // Mettre à jour la progression
        setProgress(Math.round(((i + 1) / steps.length) * 100));

        // Pour la dernière étape, stocker le résultat final
        if (i === steps.length - 1) {
          finalResult = accumulatedData;
        }
      }

      // Réinitialiser l'état de traitement
      setIsProcessing(false);
      setCurrentStep('Génération complète terminée !');

      // Appeler le callback avec les données finales
      if (onStepComplete && finalResult) {
        onStepComplete(finalResult);
      }

      return finalResult;
    } catch (error) {
      setIsProcessing(false);
      notify('Une erreur est survenue lors de la génération complète', 'error');
      if (onError) onError(error);
      return null;
    }
  }, [user?.id, credits, generateStep, onStepComplete, onError]);

  return {
    isProcessing,
    progress,
    currentStep,
    credits,
    isRateLimited,
    generateStep,
    generateAllSteps
  };
};
