import { useState, useCallback } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import { notify } from '../components/Notification';
import logger from '../utils/logger';

// Types pour les résultats de modération
export interface ModerationResult {
  isSafe: boolean;
  score: number;
  categories: {
    harassment: boolean;
    hateSpeech: boolean;
    sexualContent: boolean;
    violence: boolean;
    selfHarm: boolean;
    illegalActivity: boolean;
    spam: boolean;
    phoneSpam: boolean;
    addressSpam: boolean;
    unknownRisk: boolean;
  };
  flaggedText?: string[];
  moderationId: string;
}

/**
 * Hook pour la modération de contenu
 * Permet d'analyser du texte pour détecter du contenu inapproprié
 */
export const useContentModeration = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ModerationResult | null>(null);

  /**
   * Analyse un texte pour détecter du contenu inapproprié
   * @param text Le texte à analyser
   * @param type Le type de contenu (mission, comment, profile)
   * @param contentId L'ID du contenu (optionnel)
   * @returns Le résultat de la modération
   */
  const moderateContent = useCallback(async (
    text: string,
    type: 'mission' | 'comment' | 'profile' | 'titre_service' | 'description_service' | 'gallery_name' | 'gallery_description' | 'mission_title' | 'mission_description' | 'review',
    contentId?: string
  ): Promise<ModerationResult | null> => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer le token CSRF
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      // Appeler l'API de modération
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/content-moderation/analyze`,
        {
          text,
          type,
          contentId
        },
        {
          headers,
          withCredentials: true
        }
      );

      // Vérifier la réponse
      if (response.data.success) {
        const moderationResult = response.data.data as ModerationResult;
        setResult(moderationResult);
        return moderationResult;
      } else {
        throw new Error(response.data.message || 'Erreur lors de la modération du contenu');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la modération du contenu';
      setError(errorMessage);
      logger.error('Erreur lors de la modération du contenu:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Vérifie si un texte est sûr et affiche une notification si ce n'est pas le cas
   * Utile pour les formulaires avant soumission
   * @param text Le texte à analyser
   * @param type Le type de contenu
   * @param contentId L'ID du contenu (optionnel)
   * @returns true si le contenu est sûr, false sinon
   */
  const validateContentSafety = useCallback(async (
    text: string,
    type: 'mission' | 'comment' | 'profile' | 'titre_service' | 'description_service' | 'gallery_name' | 'gallery_description' | 'mission_title' | 'mission_description' | 'review',
    contentId?: string
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      // Appeler la fonction de modération
      const result = await moderateContent(text, type, contentId);

      // Si pas de résultat, considérer comme sûr pour ne pas bloquer l'utilisateur
      if (!result) {
        return true;
      }

      // Si le contenu n'est pas sûr, afficher une notification avec un message plus précis
      if (!result.isSafe) {
        // Déterminer le type de problème en fonction des catégories détectées
        let message = 'Votre contenu contient du texte inapproprié. Veuillez le modifier avant de continuer.';

        // Vérifier les catégories spécifiques pour personnaliser le message
        if (result.categories) {
          if (result.categories.phoneSpam || result.categories.addressSpam) {
            message = 'Votre message contient des informations de contact personnelles (téléphone ou adresse). Pour votre sécurité, ces informations ne sont pas autorisées.';
          } else if (result.categories.hateSpeech) {
            message = 'Votre message contient des propos haineux ou discriminatoires. Ces propos sont strictement interdits sur notre plateforme.';
          } else if (result.categories.violence) {
            message = 'Votre message contient des références à la violence. Ce type de contenu n\'est pas autorisé sur notre plateforme.';
          } else if (result.categories.selfHarm) {
            message = 'Votre message contient des références préoccupantes. Si vous avez besoin d\'aide, contactez le 3114 (prévention suicide) ou le 15 en cas d\'urgence.';
          } else if (result.categories.sexualContent) {
            message = 'Votre message contient des références à caractère sexuel. Ce type de contenu n\'est pas approprié sur notre plateforme.';
          } else if (result.categories.illegalActivity) {
            message = 'Votre message fait référence à des activités illégales. Ce type de contenu est strictement interdit et pourra faire l\'objet d\'un signalement aux autorités.';
          } else if (result.categories.spam) {
            message = 'Votre message ressemble à du contenu promotionnel ou du spam. Veuillez le modifier avant de continuer.';
          } else if (result.categories.harassment) {
            message = 'Votre message contient du harcèlement. Ce type de comportement n\'est pas toléré sur notre plateforme.';
          } else if (result.score >= 0.8) {
            message = 'Votre message a été détecté comme potentiellement inapproprié. Veuillez vérifier le ton et le contenu avant de continuer.';
          } else if (result.categories.unknownRisk) {
            message = 'Votre message a été détecté comme potentiellement inapproprié. Veuillez vérifier le ton et le contenu avant de continuer.';
          }
        }

        notify(message, 'warning');
        return false;
      }

      return true;
    } catch (err) {
      logger.error('Erreur lors de la validation du contenu:', err);
      // En cas d'erreur, on considère le contenu comme sûr pour ne pas bloquer l'utilisateur
      return true;
    } finally {
      setLoading(false);
    }
  }, [moderateContent]);

  /**
   * Récupère l'historique des modérations
   */
  const getModerationHistory = useCallback(async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les en-têtes communs
      const headers = await getCommonHeaders();

      // Appeler l'API pour récupérer l'historique
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/content-moderation/history`,
        {
          headers,
          withCredentials: true,
          params: { page, limit }
        }
      );

      // Vérifier la réponse
      if (response.data.success) {
        return {
          data: response.data.data,
          pagination: response.data.pagination
        };
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération de l\'historique');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la récupération de l\'historique';
      setError(errorMessage);
      logger.error('Erreur lors de la récupération de l\'historique de modération:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    moderateContent,
    validateContentSafety,
    getModerationHistory,
    loading,
    error,
    result
  };
};

export default useContentModeration;
