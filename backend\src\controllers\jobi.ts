import { Request, Response } from 'express';
import { dbService } from '../services/db';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { sendReferralRewardEmail } from '../services/emailService';
import { decryptProfilDataAsync } from '../utils/encryption';

interface JobiHistorique {
  id: string;
  titre: string;
  description: string;
  montant: number;
  message?: string; // Message optionnel pour les transferts
  date_creation: string;
}

export class JobiController {
  // Récupérer le solde de Jobi de l'utilisateur
  async getSolde(req: Request, res: Response) {
    try {
      const userId = req.user?.userId; // Attention, req.user?.userId; obligatoire dans ce format ! Pas userid !!

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Vérifier si le solde est en cache
      const cacheKey = `jobi_balance_${userId}`;
      const cachedBalance = await redis.get(cacheKey);

      if (cachedBalance) {
        logger.info('🚀 Solde Jobi récupéré depuis le cache:', cachedBalance);
        res.status(200).json({
          message: 'Solde récupéré avec succès',
          success: true,
          montant: JSON.parse(cachedBalance).montant,
          profil_complet: JSON.parse(cachedBalance).profil_complet,
          toastType: 'success'
        });
        return;
      }

      // Récupérer l'entrée unique pour cet utilisateur
      const { data: jobiData, error } = await dbService.supabase
        .from('user_jobi')
        .select('profil_complet, montant')
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Erreur lors de la récupération du solde Jobi:', error);
        return res.status(500).json({
          message: 'Erreur lors de la récupération du solde Jobi',
          success: false,
          toastType: 'error'
        });
      }

      logger.info('🚀 Récupération du solde Jobi sans cache :', jobiData);

      // Vérifier si des données ont été trouvées
      if (!jobiData) {
        // Aucun enregistrement trouvé, retourner un solde de 0
        res.status(200).json({
          message: 'Solde récupéré avec succès',
          success: true,
          montant: 0,
          profil_complet: false,
          toastType: 'success'
        });
        return;
      }

      if (jobiData?.profil_complet) {
        logger.info('🚀 Profil complet :', jobiData.profil_complet);
      }

      // Mettre en cache le solde et le profil complet
      await redis.set(cacheKey, JSON.stringify({ 
        montant: jobiData.montant, 
        profil_complet: jobiData.profil_complet 
      }), 'EX', 600); // Cache pour 10 minutes

      res.status(200).json({
        message: 'Solde récupéré avec succès',
        success: true,
        montant: jobiData.montant,
        profil_complet: jobiData.profil_complet,
        toastType: 'success'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération du solde Jobi:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Récupérer l'historique des transactions Jobi
  async getHistorique(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const offset = parseInt(req.query.offset as string) || 0;

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      const cacheKey = `historique-jobi:${userId}:${offset}`;
      let cachedHistorique;

      // Récupération de l'historique depuis le cache Redis si disponible
      cachedHistorique = await redis.get(cacheKey);

      if (cachedHistorique) {
        logger.info('Historique Jobi récupéré depuis le cache:', { userId, offset });
        res.status(200).json({
          success: true,
          historique: JSON.parse(cachedHistorique)
        });
        return;
      } else {
        logger.info('Historique Jobi non rencontré dans le cache', { userId, offset });
      }

      const { data: historique, error } = await dbService.supabase
        .from('user_jobi_historique')
        .select('*')
        .eq('user_id', userId)
        .order('date_creation', { ascending: false })
        .range(offset, offset + 9);

      if (error) {
        logger.error('Erreur lors de la récupération de l\'historique Jobi:', error);
        return res.status(500).json({
          message: 'Erreur lors de la récupération de l\'historique Jobi',
          success: false,
          toastType: 'error'
        });
      }

      // Nettoyer les données avec sanitizeHtml
      const cleanHistorique = historique.map((item: JobiHistorique) => ({
        ...item,
        titre: (item.titre),
        description: (item.description)
      }));

      // Mettre à jour le cache avec le nouvel historique et une durée d'expiration de 1 heure
      await redis.setex(cacheKey, 600, JSON.stringify(cleanHistorique));

      res.status(200).json({
        success: true,
        historique: cleanHistorique
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique Jobi:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Récupérer les statistiques mensuelles des transactions Jobi
  async getMonthlyStats(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      
      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }
      
      const cacheKey = `jobi-monthly-stats:${userId}`;
      let cachedStats;
      
      // Récupération des stats depuis le cache Redis si disponible
      cachedStats = await redis.get(cacheKey);
      
      if (cachedStats) {
        logger.info('Statistiques mensuelles Jobi récupérées depuis le cache:', { userId });
        res.status(200).json({
          success: true,
          stats: JSON.parse(cachedStats)
        });
        return;
      }
      
      // Obtenir le premier jour du mois courant
      const currentDate = new Date();
      const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const firstDayString = firstDay.toISOString();
      
      // Compter le nombre de transactions pour le mois en cours
      const { count: transactionCount, error: transactionError } = await dbService.supabase
        .from('user_jobi_historique')
        .select('id', { count: 'exact', head: false })
        .eq('user_id', userId)
        .gte('date_creation', firstDayString);
      
      if (transactionError) {
        logger.error('Erreur lors de la récupération des statistiques mensuelles Jobi:', transactionError);
        return res.status(500).json({
          message: 'Erreur lors de la récupération des statistiques mensuelles Jobi',
          success: false,
          toastType: 'error'
        });
      }
      
      // Récupérer la somme totale des montants (positifs et négatifs) pour le mois en cours
      const { data: transactionSums, error: sumsError } = await dbService.supabase
        .rpc('sum_jobi_transactions_by_month', {
          user_id_param: userId,
          month_param: currentDate.getMonth() + 1,
          year_param: currentDate.getFullYear()
        });
      
      let totalIn = 0;
      let totalOut = 0;
      
      if (!sumsError && transactionSums) {
        // Si la procédure stockée existe
        totalIn = transactionSums.total_in || 0;
        totalOut = Math.abs(transactionSums.total_out || 0);
      } else {
        // Fallback si la procédure stockée n'existe pas
        const { data: transactions, error: fetchError } = await dbService.supabase
          .from('user_jobi_historique')
          .select('montant')
          .eq('user_id', userId)
          .gte('date_creation', firstDayString);
          
        if (!fetchError && transactions) {
          transactions.forEach((transaction: { montant: number }) => {
            if (transaction.montant > 0) {
              totalIn += transaction.montant;
            } else if (transaction.montant < 0) {
              totalOut += Math.abs(transaction.montant);
            }
          });
        }
      }
      
      const stats = {
        transactionCount: transactionCount || 0,
        totalIn,
        totalOut
      };
      
      // Mettre à jour le cache avec les nouvelles stats et une durée d'expiration de 30 minutes
      await redis.setex(cacheKey, 1800, JSON.stringify(stats));
      
      res.status(200).json({
        success: true,
        stats
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques mensuelles Jobi:', error);
      return res.status(500).json({
        message: 'Une erreur est survenue',
        success: false,
        toastType: 'error'
      });
    }
  }
}

export const getJobiBalance = async (req: Request, res: Response, next: Function) => {
  try {
    const userId = req.params.userId;
    const cacheKey = `jobiBalance:${userId}`;

    // Vérifier le cache
    const cachedBalance = await redis.get(cacheKey);
    if (cachedBalance) {
      return res.json({ balance: parseInt(cachedBalance) });
    }

    // Si pas en cache, récupérer depuis la base de données
    const { data: jobiData, error } = await dbService.supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', userId)
      .single();

    if (error) {
      return next(error);
    }

    const balance = jobiData?.montant || 0;
    
    // Mettre en cache pour 5 minutes
    await redis.setex(cacheKey, 600, balance.toString());
    
    return res.json({ balance });
  } catch (error) {
    logger.error('Erreur lors de la récupération du solde Jobi:', error);
    return next(error);
  }
};

// Fonction pour masquer partiellement une adresse email
const maskEmail = (email: string): string => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  
  // Masquer une partie du nom d'utilisateur
  let maskedUsername = '';
  if (username.length <= 3) {
    maskedUsername = username[0] + '*'.repeat(username.length - 1);
  } else {
    maskedUsername = username.substring(0, 2) + '*'.repeat(Math.min(username.length - 2, 3));
    if (username.length > 5) {
      maskedUsername += username.substring(username.length - 2);
    }
  }
  
  // Masquer une partie du domaine
  const domainParts = domain.split('.');
  const tld = domainParts.pop() || '';
  const domainName = domainParts.join('.');
  
  let maskedDomain = '';
  if (domainName.length <= 3) {
    maskedDomain = domainName[0] + '*'.repeat(domainName.length - 1);
  } else {
    maskedDomain = domainName[0] + '*'.repeat(Math.min(domainName.length - 1, 3));
    if (domainName.length > 4) {
      maskedDomain += domainName.substring(domainName.length - 1);
    }
  }
  
  return `${maskedUsername}@${maskedDomain}.${tld}`;
};

// Fonction pour formater le nom et prénom sous la forme "Jean D."
const formatName = (prenom: string | null, nom: string | null): string => {
  if (!prenom && !nom) return '';
  
  const prenomFormatted = prenom ? prenom.trim() : '';
  const nomFormatted = nom ? nom.trim() : '';
  
  if (!prenomFormatted && !nomFormatted) return '';
  if (!prenomFormatted) return nomFormatted;
  if (!nomFormatted) return prenomFormatted;
  
  // Format "Jean D." pour Jean Dupont
  return `${prenomFormatted} ${nomFormatted.charAt(0).toUpperCase()}.`;
};

// Méthode pour récompenser un parrainage lorsqu'une mission est validée
export const rewardReferral = async (userId: string): Promise<boolean> => {
  try {
    logger.info('Début de rewardReferral pour userId:', userId);

    // Vérifier si l'utilisateur a été parrainé
    const { data: userData, error: userError } = await dbService.supabase
      .from('users')
      .select('id, email, referred_by, referral_completed, user_type')
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des données utilisateur:', userError);
      return false;
    }

    if (!userData) {
      logger.error('Utilisateur non trouvé:', userId);
      return false;
    }

    logger.info('Données utilisateur récupérées:', { 
      userId: userData.id,
      email: userData.email,
      referred_by: userData.referred_by, 
      referral_completed: userData.referral_completed,
      user_type: userData.user_type
    });

    // Si l'utilisateur n'a pas été parrainé ou a déjà reçu sa récompense, on ne fait rien
    if (!userData.referred_by || userData.referral_completed) {
      logger.info('Pas de parrainage à récompenser ou déjà récompensé:', { 
        referred_by: userData.referred_by, 
        referral_completed: userData.referral_completed,
        user_type: userData.user_type
      });
      return false;
    }

    // Récupérer l'entrée de parrainage
    const { data: referralData, error: referralError } = await dbService.supabase
      .from('user_referrals')
      .select('id, status, reward_amount')
      .eq('referrer_id', userData.referred_by)
      .eq('referred_id', userId)
      .maybeSingle();

    if (referralError) {
      logger.error('Erreur lors de la récupération des données de parrainage:', referralError);
      return false;
    }

    if (!referralData) {
      logger.info('Aucune entrée de parrainage trouvée pour:', { 
        referrer_id: userData.referred_by, 
        referred_id: userId 
      });
      return false;
    }

    logger.info('Données de parrainage récupérées:', referralData);

    // Si le parrainage a déjà été récompensé, on ne fait rien
    if (referralData.status === 'rewarded') {
      logger.info('Parrainage déjà récompensé:', { referralId: referralData.id });
      return false;
    }

    // Mettre à jour le statut du parrainage
    const { error: updateReferralError } = await dbService.supabase
      .from('user_referrals')
      .update({
        status: 'rewarded',
        completed_at: new Date().toISOString(),
        rewarded_at: new Date().toISOString()
      })
      .eq('id', referralData.id);

    if (updateReferralError) {
      logger.error('Erreur lors de la mise à jour du statut du parrainage:', updateReferralError);
      return false;
    }

    logger.info('Statut du parrainage mis à jour avec succès');

    // Mettre à jour le statut de l'utilisateur
    const { error: updateUserError } = await dbService.supabase
      .from('users')
      .update({
        referral_completed: true
      })
      .eq('id', userId);

    if (updateUserError) {
      logger.error('Erreur lors de la mise à jour du statut de l\'utilisateur:', updateUserError);
      return false;
    }

    logger.info('Statut de l\'utilisateur mis à jour avec succès');

    // Invalider les caches Redis liés aux parrainages
    const referrerCacheKey = `referrals:${userData.referred_by}`;
    await redis.del(referrerCacheKey);
    
    // Invalider le cache du solde Jobi pour les deux utilisateurs
    await redis.del(`jobi_balance_${userId}`);
    await redis.del(`jobi_balance_${userData.referred_by}`);
    
    // Invalider le cache de l'historique Jobi pour les deux utilisateurs
    await redis.del(`historique-jobi:${userId}:0`);
    await redis.del(`historique-jobi:${userData.referred_by}:0`);

    // Récompenser l'utilisateur parrainé
    await dbService.createJobiEntrySupplement(
      userId, 
      { 
        montant: referralData.reward_amount, 
        titre: 'Récompense de parrainage', 
        description: 'Récompense pour avoir complété votre première mission après parrainage' 
      }
    );

    logger.info('Jobi crédités au filleul');

    // Récompenser le parrain
    await dbService.createJobiEntrySupplement(
      userData.referred_by, 
      { 
        montant: referralData.reward_amount, 
        titre: 'Récompense de parrainage', 
        description: 'Récompense pour avoir parrainé un utilisateur qui a complété sa première mission' 
      }
    );

    logger.info('Jobi crédités au parrain');

    // Envoyer une notification au parrain
    const { error: notificationError1 } = await dbService.supabase
      .from('user_notifications')
      .insert({
        user_id: userData.referred_by,
        type: 'jobi',
        title: 'Parrainage récompensé !',
        content: `Votre filleul a complété sa première mission. Vous avez reçu ${referralData.reward_amount} Jobis de récompense !`,
        is_read: false,
        is_archived: false
      });

    if (notificationError1) {
      logger.error('Erreur lors de l\'envoi de la notification au parrain:', notificationError1);
    } else {
      logger.info('Notification envoyée au parrain');
    }

    // Envoyer une notification à l'utilisateur parrainé
    const { error: notificationError2 } = await dbService.supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: 'jobi',
        title: 'Parrainage récompensé !',
        content: `Vous avez complété votre première mission après parrainage. Vous avez reçu ${referralData.reward_amount} Jobis de récompense !`,
        is_read: false,
        is_archived: false
      });

    if (notificationError2) {
      logger.error('Erreur lors de l\'envoi de la notification à l\'utilisateur parrainé:', notificationError2);
    } else {
      logger.info('Notification envoyée au filleul');
    }

    // Récupérer les informations des utilisateurs pour les emails
    try {
      // Récupérer les emails des utilisateurs
      const { data: referrerUser, error: referrerUserError } = await dbService.supabase
        .from('users')
        .select('email')
        .eq('id', userData.referred_by)
        .single();

      const { data: referredUser, error: referredUserError } = await dbService.supabase
        .from('users')
        .select('email')
        .eq('id', userId)
        .single();

      if (referrerUserError || !referrerUser || referredUserError || !referredUser) {
        logger.error('Erreur lors de la récupération des emails:', { referrerUserError, referredUserError });
      } else {
        logger.info('Emails des utilisateurs récupérés:', {
          referrer: referrerUser.email,
          referred: referredUser.email
        });

        // Récupérer les noms des utilisateurs
        const { data: referrerProfile, error: referrerProfileError } = await dbService.supabase
          .from('user_profil')
          .select('nom, prenom')
          .eq('user_id', userData.referred_by)
          .single();

        const { data: referredProfile, error: referredProfileError } = await dbService.supabase
          .from('user_profil')
          .select('nom, prenom')
          .eq('user_id', userId)
          .single();

        // Déchiffrer les données de profil
        const decryptedReferrerProfile = referrerProfile ? await decryptProfilDataAsync(referrerProfile) : null;
        const decryptedReferredProfile = referredProfile ? await decryptProfilDataAsync(referredProfile) : null;

        // Récupérer le titre de la mission
        const { data: missionData, error: missionError } = await dbService.supabase
          .from('user_mission_candidature')
          .select('mission_id')
          .eq('jobbeur_id', userId)
          .eq('statut', 'acceptée')
          .order('updated_at', { ascending: false })
          .limit(1)
          .single();

        let missionTitle = 'une mission';
        if (!missionError && missionData) {
          const { data: mission, error: missionTitleError } = await dbService.supabase
            .from('user_missions')
            .select('titre')
            .eq('id', missionData.mission_id)
            .single();

          if (!missionTitleError && mission) {
            missionTitle = mission.titre;
          }
        }

        // Envoyer les emails
        if (!referrerProfileError && decryptedReferrerProfile && !referredProfileError && decryptedReferredProfile) {
          // Format des noms pour l'affichage
          const formattedReferrerName = formatName(decryptedReferrerProfile.prenom, decryptedReferrerProfile.nom);
          const formattedReferredName = formatName(decryptedReferredProfile.prenom, decryptedReferredProfile.nom);
          
          // Utiliser le nom formaté ou l'email masqué si le nom n'est pas disponible
          const displayReferrerName = formattedReferrerName || maskEmail(referrerUser.email);
          const displayReferredName = formattedReferredName || maskEmail(referredUser.email);

          // Utiliser le même format court pour les noms complets
          const fullReferrerName = formattedReferrerName;
          const fullReferredName = formattedReferredName;

          logger.info('Envoi des emails de récompense de parrainage:', {
            referrer: referrerUser.email,
            referred: referredUser.email,
            referrerName: displayReferrerName,
            referredName: displayReferredName
          });

          await sendReferralRewardEmail(
            { 
              referrer: referrerUser.email, 
              referred: referredUser.email 
            },
            {
              referrerName: displayReferrerName,
              referredName: displayReferredName,
              fullReferrerName: fullReferrerName || displayReferrerName,
              fullReferredName: fullReferredName || displayReferredName,
              rewardAmount: referralData.reward_amount,
              missionTitle
            }
          );

          logger.info('Emails de récompense de parrainage envoyés avec succès');
        } else {
          logger.error('Erreur lors de la récupération des profils:', {
            referrerProfileError,
            referredProfileError
          });
        }
      }
    } catch (emailError) {
      logger.error('Erreur lors de l\'envoi des emails de récompense de parrainage:', emailError);
    }

    logger.info('Parrainage récompensé avec succès:', { 
      referralId: referralData.id, 
      referrerId: userData.referred_by, 
      referredId: userId,
      rewardAmount: referralData.reward_amount
    });

    return true;
  } catch (error) {
    logger.error('Erreur lors de la récompense du parrainage:', error);
    return false;
  }
};