import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  styled,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Building,
  DollarSign,
  Briefcase,
  Star,
  Image,
  History,
  BarChart2,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Wifi,
  Award,
  Zap,
  Plus,
  Minus,
  RefreshCw,
  Save,
  Edit,
  X
} from 'lucide-react';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';
import UserStatsCharts from './UserStatsCharts';
import UserPhotosManagement from './UserPhotosManagement';
import UserBadgesManagement from './UserBadgesManagement';
import UserMessagesManagement from './UserMessagesManagement';
import UserAdvancedReport from './UserAdvancedReport';
import UserMissionsManagement from './UserMissionsManagement';
import UserSubscriptionManagement from './UserSubscriptionManagement';
import UserActivityHistory from './UserActivityHistory';
import { useUserManagement } from '../../hooks/useUserManagement';
import { useAuth } from '../../contexts/AuthContext';
import { logger } from '@/utils/logger';
import ConfirmationModal from '../planning/ConfirmationModal';

// Module augmentation for Material-UI Chip colors
declare module '@mui/material/Chip' {
  interface ChipPropsColorOverrides {
    neutral: true;
  }
}

// Interface pour le formulaire de modification du profil
interface UserProfileEditFormProps {
  userDetails: UserDetailData;
  onSave: (data: any) => void;
  onCancel: () => void;
  isLoading: boolean;
}

// Composant de formulaire de modification du profil
const UserProfileEditForm: React.FC<UserProfileEditFormProps> = ({
  userDetails,
  onSave,
  onCancel,
  isLoading
}) => {
  const { user: currentUser } = useAuth(); // Pour vérifier les permissions
  const isAdmin = currentUser?.role === 'jobpadm';

  const [formData, setFormData] = useState({
    // Données utilisateur
    email: '',
    role: 'jobutil',
    user_type: 'non-jobbeur',
    profil_actif: false,
    email_verifier: false,
    profil_verifier: false,
    identite_verifier: false,
    entreprise_verifier: false,
    assurance_verifier: false,

    // Données profil de base
    nom: '',
    prenom: '',
    telephone: '',
    telephone_prive: true,
    numero: '',
    adresse: '',
    ville: '',
    code_postal: '',
    pays: 'France',
    bio: '',
    slogan: '',
    mode_vacance: false,
    profil_visible: true,
    seo_indexable: false,

    // Données entreprise
    type_de_profil: 'particulier',
    nom_entreprise: '',
    prenom_entreprise: '',
    statut_entreprise: '',
    siren_entreprise: '',
    code_ape_entreprise: '',
    categorie_entreprise: '',
    effectif_entreprise: ''
  });

  // Tracker les champs modifiés
  const [modifiedFields, setModifiedFields] = useState<Set<string>>(new Set());
  const [initialData, setInitialData] = useState(formData);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Fonction pour récupérer les données complètes du profil depuis l'API
  const fetchCompleteUserData = async () => {
    try {
      setIsLoadingData(true);
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userDetails.id}`,
        {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des données complètes');
      }

      const data = await response.json();

      if (data.success) {
        const backendData = data.data;
        
        // Initialiser le formulaire avec toutes les données disponibles
        const completeFormData = {
          // Données utilisateur
          email: backendData.user.email || '',
          role: backendData.user.role || 'jobutil',
          user_type: backendData.user.user_type || 'non-jobbeur',
          profil_actif: backendData.user.profil_actif || false,
          email_verifier: backendData.user.email_verifier || false,
          profil_verifier: backendData.user.profil_verifier || false,
          identite_verifier: backendData.user.identite_verifier || false,
          entreprise_verifier: backendData.user.entreprise_verifier || false,
          assurance_verifier: backendData.user.assurance_verifier || false,

          // Données profil de base
          nom: backendData.profil?.nom || '',
          prenom: backendData.profil?.prenom || '',
          telephone: backendData.profil?.telephone || '',
          telephone_prive: backendData.profil?.telephone_prive !== undefined ? backendData.profil.telephone_prive : true,
          numero: backendData.profil?.numero || '',
          adresse: backendData.profil?.adresse || '',
          ville: backendData.profil?.ville || '',
          code_postal: backendData.profil?.code_postal || '',
          pays: backendData.profil?.pays || 'France',
          bio: backendData.profil?.bio || '',
          slogan: backendData.profil?.slogan || '',
          mode_vacance: backendData.profil?.mode_vacance || false,
          profil_visible: backendData.profil?.profil_visible !== undefined ? backendData.profil.profil_visible : true,
          seo_indexable: backendData.profil?.seo_indexable || false,

          // Données entreprise
          type_de_profil: backendData.profil?.type_de_profil || 'particulier',
          nom_entreprise: backendData.profil?.nom_entreprise || '',
          prenom_entreprise: backendData.profil?.prenom_entreprise || '',
          statut_entreprise: backendData.profil?.statut_entreprise || '',
          siren_entreprise: backendData.profil?.siren_entreprise || '',
          code_ape_entreprise: backendData.profil?.code_ape_entreprise || '',
          categorie_entreprise: backendData.profil?.categorie_entreprise || '',
          effectif_entreprise: backendData.profil?.effectif_entreprise || ''
        };

        setFormData(completeFormData);
        setInitialData(completeFormData);
        setModifiedFields(new Set()); // Réinitialiser les champs modifiés
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des données complètes:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // Initialiser les données au premier rendu et quand userDetails change
  useEffect(() => {
    if (userDetails?.id) {
      fetchCompleteUserData();
    }
  }, [userDetails?.id]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Marquer le champ comme modifié si la valeur a changé
      if (initialData[field as keyof typeof initialData] !== value) {
        setModifiedFields(prev => new Set([...prev, field]));
      } else {
        setModifiedFields(prev => {
          const newSet = new Set(prev);
          newSet.delete(field);
          return newSet;
        });
      }

      return newData;
    });
  };

  const handleSubmit = () => {
    // Envoyer seulement les champs modifiés
    const modifiedData: any = {};
    modifiedFields.forEach(field => {
      modifiedData[field] = formData[field as keyof typeof formData];
    });

    // Ajouter la liste des champs modifiés
    modifiedData.modifiedFields = Array.from(modifiedFields);

    onSave(modifiedData);
  };

  return (
    <Box>
      {isLoadingData && (
        <Box display="flex" justifyContent="center" alignItems="center" p={3}>
          <CircularProgress size={24} sx={{ color: COLORS.primary, mr: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Chargement des données du profil...
          </Typography>
        </Box>
      )}
      
      <Grid container spacing={2}>
        {/* Informations de base */}
        <Grid size={{ xs: 12 }}>
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600 }}>
            Informations de base
          </Typography>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Prénom"
            value={formData.prenom}
            onChange={(e) => handleInputChange('prenom', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Nom"
            value={formData.nom}
            onChange={(e) => handleInputChange('nom', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Téléphone"
            value={formData.telephone}
            onChange={(e) => handleInputChange('telephone', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Ville"
            value={formData.ville}
            onChange={(e) => handleInputChange('ville', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Code postal"
            value={formData.code_postal}
            onChange={(e) => handleInputChange('code_postal', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Numéro de rue"
            value={formData.numero}
            onChange={(e) => handleInputChange('numero', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12 }}>
          <TextField
            fullWidth
            label="Adresse complète"
            value={formData.adresse}
            onChange={(e) => handleInputChange('adresse', e.target.value)}
            size="small"
            multiline
            rows={2}
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <TextField
            fullWidth
            label="Pays"
            value={formData.pays}
            onChange={(e) => handleInputChange('pays', e.target.value)}
            size="small"
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12 }}>
          <TextField
            fullWidth
            label="Bio"
            value={formData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            size="small"
            multiline
            rows={3}
            placeholder="Description du profil utilisateur..."
            disabled={isLoadingData}
          />
        </Grid>

        <Grid size={{ xs: 12 }}>
          <TextField
            fullWidth
            label="Slogan"
            value={formData.slogan}
            onChange={(e) => handleInputChange('slogan', e.target.value)}
            size="small"
            placeholder="Slogan personnalisé de l'utilisateur..."
            disabled={isLoadingData}
          />
        </Grid>

        {/* Paramètres du compte */}
        <Grid size={{ xs: 12 }}>
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600, mt: 2 }}>
            Paramètres du compte
          </Typography>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth size="small">
            <InputLabel>Rôle</InputLabel>
            <Select
              value={formData.role}
              label="Rôle"
              onChange={(e) => handleInputChange('role', e.target.value)}
              disabled={!isAdmin || isLoadingData}
            >
              <MenuItem value="jobutil">Utilisateur</MenuItem>
              <MenuItem value="jobmodo">Modérateur</MenuItem>
              <MenuItem value="jobpadm">Administrateur</MenuItem>
            </Select>
          </FormControl>
          {!isAdmin && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
              Seuls les administrateurs peuvent modifier les rôles
            </Typography>
          )}
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth size="small">
            <InputLabel>Type d'utilisateur</InputLabel>
            <Select
              value={formData.user_type}
              label="Type d'utilisateur"
              onChange={(e) => handleInputChange('user_type', e.target.value)}
              disabled={isLoadingData}
            >
              <MenuItem value="non-jobbeur">Non-jobbeur</MenuItem>
              <MenuItem value="jobbeur">Jobbeur</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Vérifications */}
        <Grid size={{ xs: 12 }}>
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600, mt: 2 }}>
            Statuts de vérification
          </Typography>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.profil_actif}
                onChange={(e: any) => handleInputChange('profil_actif', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Profil actif"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.email_verifier}
                onChange={(e: any) => handleInputChange('email_verifier', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Email vérifié"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.profil_verifier}
                onChange={(e: any) => handleInputChange('profil_verifier', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Profil vérifié"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.identite_verifier}
                onChange={(e: any) => handleInputChange('identite_verifier', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Identité vérifiée"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.entreprise_verifier}
                onChange={(e: any) => handleInputChange('entreprise_verifier', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Entreprise vérifiée"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.assurance_verifier}
                onChange={(e: any) => handleInputChange('assurance_verifier', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Assurance vérifiée"
          />
        </Grid>

        {/* Paramètres de visibilité et autres */}
        <Grid size={{ xs: 12 }}>
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600, mt: 2 }}>
            Paramètres de visibilité et autres
          </Typography>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.profil_visible}
                onChange={(e: any) => handleInputChange('profil_visible', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Profil visible publiquement"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.seo_indexable}
                onChange={(e: any) => handleInputChange('seo_indexable', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Indexable par les moteurs de recherche"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.mode_vacance}
                onChange={(e: any) => handleInputChange('mode_vacance', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Mode vacances"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.telephone_prive}
                onChange={(e: any) => handleInputChange('telephone_prive', e.target.checked)}
                color="primary"
                disabled={isLoadingData}
              />
            }
            label="Téléphone privé"
          />
        </Grid>

        {/* Type de profil */}
        <Grid size={{ xs: 12 }}>
          <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600, mt: 2 }}>
            Type de profil
          </Typography>
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth size="small">
            <InputLabel>Type de profil</InputLabel>
            <Select
              value={formData.type_de_profil}
              label="Type de profil"
              onChange={(e) => handleInputChange('type_de_profil', e.target.value)}
              disabled={isLoadingData}
            >
              <MenuItem value="particulier">Particulier</MenuItem>
              <MenuItem value="entreprise">Entreprise</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Champs entreprise (affichés seulement si type entreprise) */}
        {formData.type_de_profil === 'entreprise' && (
          <>
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary, fontWeight: 600, mt: 2 }}>
                Informations entreprise
              </Typography>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Nom de l'entreprise"
                value={formData.nom_entreprise}
                onChange={(e) => handleInputChange('nom_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Prénom du dirigeant"
                value={formData.prenom_entreprise}
                onChange={(e) => handleInputChange('prenom_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="SIREN"
                value={formData.siren_entreprise}
                onChange={(e) => handleInputChange('siren_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Code APE"
                value={formData.code_ape_entreprise}
                onChange={(e) => handleInputChange('code_ape_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Statut juridique"
                value={formData.statut_entreprise}
                onChange={(e) => handleInputChange('statut_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Effectif"
                value={formData.effectif_entreprise}
                onChange={(e) => handleInputChange('effectif_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Catégorie"
                value={formData.categorie_entreprise}
                onChange={(e) => handleInputChange('categorie_entreprise', e.target.value)}
                size="small"
                disabled={isLoadingData}
              />
            </Grid>
          </>
        )}

        {/* Indicateur des champs modifiés */}
        {modifiedFields.size > 0 && (
          <Grid size={{ xs: 12 }}>
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>{modifiedFields.size}</strong> champ(s) modifié(s) : {Array.from(modifiedFields).join(', ')}
              </Typography>
            </Alert>
          </Grid>
        )}

        {/* Boutons d'action */}
        <Grid size={{ xs: 12 }}>
          <Box display="flex" gap={2} justifyContent="flex-end" mt={2}>
            <Button
              variant="outlined"
              onClick={onCancel}
              disabled={isLoading}
              startIcon={<X size={16} />}
            >
              Annuler
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={isLoading || modifiedFields.size === 0}
              startIcon={isLoading ? <CircularProgress size={16} /> : <Save size={16} />}
              sx={{
                backgroundColor: COLORS.primary,
                '&:hover': {
                  backgroundColor: COLORS.secondary
                },
                '&:disabled': {
                  backgroundColor: '#ccc'
                }
              }}
            >
              {isLoading ? 'Sauvegarde...' : `Sauvegarder${modifiedFields.size > 0 ? ` (${modifiedFields.size})` : ''}`}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Define a type for the allowed chip colors that matches MUI's Chip color prop
type ChipColor = 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default' | 'neutral'; // Added 'neutral' for our custom color

// Mapping from ChipColor to actual hex codes from COLORS
const CHIP_COLOR_MAP: Record<ChipColor, string> = {
  primary: COLORS.primary,
  secondary: COLORS.secondary,
  error: COLORS.error,
  info: COLORS.info,
  warning: COLORS.warning,
  success: COLORS.success,
  neutral: COLORS.neutral,
  default: COLORS.neutral, // Default to neutral if no specific color is provided or it's 'default'
};

// Styles personnalisés pour les composants
const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '16px',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
    overflow: 'hidden',
  },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  backgroundColor: COLORS.white,
  borderBottom: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(2, 3),
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: COLORS.lightGray,
}));

const StyledDialogActions = styled(DialogActions)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderTop: `1px solid ${COLORS.borderColor}`,
  backgroundColor: COLORS.white,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${COLORS.borderColor}`,
  '& .MuiTabs-indicator': {
    backgroundColor: COLORS.primary,
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: 600,
    fontSize: '0.9rem',
    minWidth: 'auto',
    padding: theme.spacing(1.5, 2),
    '&.Mui-selected': {
      color: COLORS.primary,
    },
  },
}));

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  height: '100%',
  padding: '14px',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.1rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  position: 'relative',
  paddingLeft: theme.spacing(1),
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '4px',
    height: '18px',
    backgroundColor: COLORS.primary,
    borderRadius: '2px',
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

const StyledChip = styled(Chip)<{ color?: ChipColor }>(({ theme, color = 'default' }) => {
  const selectedColor = CHIP_COLOR_MAP[color] || CHIP_COLOR_MAP.default; // Fallback to default if not found

  return {
    borderRadius: '6px',
    fontWeight: 500,
    fontSize: '0.75rem',
    height: '24px',
    '&.MuiChip-filled': {
      backgroundColor: `${selectedColor}15`,
      color: selectedColor,
    },
    '&.MuiChip-outlined': {
      borderColor: selectedColor,
      color: selectedColor,
    },
  };
});

interface User {
  id: string;
  email: string;
  role: string;
  user_type: string;
  profil_actif: boolean;
  email_verifier: boolean;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  is_online: boolean;
  last_activity: string;
  date_inscription: string;
  created_at: string;
  is_anonymized: boolean;
  suspension_reason?: string;
  suspended_until?: string;
  user_profil?: {
    nom: string;
    prenom: string;
    telephone: string;
    ville: string;
    photo_url: string;
    mode_vacance: boolean;
    profil_visible: boolean;
  };
  user_abo?: {
    type_abonnement: string;
    statut: string;
    date_debut: string;
    date_fin: string;
  }[];
  user_jobi?: {
    montant: number;
  };
}

interface UserDetailData {
  id: string;
  name: string;
  email: string;
  phone?: string;
  city?: string;
  avatar?: string;
  role: string;
  userType: string;
  status: string;
  isOnline: boolean;
  registrationDate: string;
  lastActivity?: string;
  verifications?: {
    email: boolean;
    profile: boolean;
    identity: boolean;
    company: boolean;
    insurance: boolean;
  };
  finances?: {
    jobi: number;
    aiCredits: number;
    subscription?: {
      id: string;
      type: 'basic' | 'premium' | 'pro';
      status: 'active' | 'expired' | 'cancelled' | 'suspended';
      start_date: string;
      end_date: string;
      auto_renew: boolean;
      price: number;
      features: string[];
      created_at: string;
      cancelled_at?: string;
      cancellation_reason?: string;
    };
    subscriptionHistory?: Array<{
      id: string;
      type: 'basic' | 'premium' | 'pro';
      status: 'active' | 'expired' | 'cancelled' | 'suspended';
      start_date: string;
      end_date: string;
      auto_renew: boolean;
      price: number;
      features: string[];
      created_at: string;
      cancelled_at?: string;
      cancellation_reason?: string;
    }>;
    transactions: {
      total: number;
      recent: Array<{
        id: string;
        date: string;
        type: string;
        amount: number;
        status: string;
      }>;
    };
  };
  jobiHistory?: Array<{
    id: string;
    titre: string;
    description: string;
    montant: number;
    message?: string;
    date_creation: string;
  }>;
  aiCreditsHistory?: Array<{
    id: string;
    operation_type: string;
    montant: number;
    solde_avant: number;
    solde_apres: number;
    description?: string;
    reference?: string;
    created_at: string;
  }>;
  missions: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    status: 'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled';
    budget: number;
    created_at: string;
    start_date?: string;
    end_date?: string;
    location: string;
    client_name: string;
    client_id: string;
    applications_count: number;
    candidatures: Array<any>;
    selected_freelancer?: {
      id: string;
      name: string;
      avatar?: string;
    };
    completion_percentage?: number;
    rating?: number;
    review?: string;
  }>;
  reviews: {
    total: number;
    average: number;
    recent: Array<{
      id: string;
      rating: number;
      comment: string;
      date: string;
      from: {
        id: string;
        name: string;
      };
    }>;
  };
  badges: Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    earnedDate: string;
  }>;
  photos: Array<{
    id: string;
    url: string;
    uploadDate: string;
    type: string;
  }>;
  galleries: Array<{
    id: string;
    name: string;
    description: string;
    cover_image: string;
    status: string;
    created_at: string;
    user_gallery_photos?: Array<any>;
  }>;
  featuredPhotos: Array<{
    id: string;
    photo_url: string;
    caption: string;
    created_at: string;
  }>;
  conversations: Array<{
    id: string;
    user1_id: string;
    user2_id: string;
    updated_at: string;
    is_moderated: boolean;
    user_messages?: Array<{
      id: string;
      sender_id: string;
      content: string;
      created_at: string;
    }>;
  }>;
  history: Array<{
    id: string;
    action: string;
    date: string;
    details: string;
  }>;
}

interface UserDetailModalProps {
  open: boolean;
  user: User;
  onClose: () => void;
  onUserUpdate: () => void;
}

// Fonction pour transformer les types d'abonnement vers le format attendu par UserSubscriptionManagement
const transformSubscriptionForComponent = (subscription: any) => {
  // Transformation des statuts vers le format attendu par UserSubscriptionManagement
  const statusMapping: Record<string, string> = {
    'active': 'actif',
    'expired': 'inactif',
    'cancelled': 'inactif',
    'suspended': 'suspendu'
  };

  return {
    ...subscription,
    status: statusMapping[subscription.status] || subscription.status,
    type: subscription.type === 'basic' ? 'basic' : 
          subscription.type === 'premium' ? 'premium' : 
          subscription.type === 'pro' ? 'pro' : 'gratuit'
  };
};

const UserDetailModal: React.FC<UserDetailModalProps> = ({ open, user, onClose, onUserUpdate }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [financeSubTab, setFinanceSubTab] = useState(0);
  const [userDetails, setUserDetails] = useState<UserDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // États pour les actions administratives
  const [jobiAmount, setJobiAmount] = useState<number>(0);
  const [jobiAction, setJobiAction] = useState<'add' | 'remove'>('add');
  const [jobiDescription, setJobiDescription] = useState<string>('Gestion administrative des Jobi');
  
  const [aiCreditsAmount, setAiCreditsAmount] = useState<number>(0);
  const [aiCreditsAction, setAiCreditsAction] = useState<'add' | 'remove'>('add');
  const [aiCreditsDescription, setAiCreditsDescription] = useState<string>('Gestion administrative des crédits IA');
  
  // États pour les modales de confirmation
  const [jobiConfirmModalOpen, setJobiConfirmModalOpen] = useState<boolean>(false);
  const [aiCreditsConfirmModalOpen, setAiCreditsConfirmModalOpen] = useState<boolean>(false);
  
  const [subscriptionType, setSubscriptionType] = useState<string>('');
  const [subscriptionDuration, setSubscriptionDuration] = useState<number>(1);
  const [subscriptionAction, setSubscriptionAction] = useState<'create' | 'change' | 'extend'>('create');
  
  const [userActionType, setUserActionType] = useState<'suspend' | 'activate' | 'delete'>('suspend');
  const [userActionReason, setUserActionReason] = useState<string>('');

  // États pour la modification du profil
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isLoadingProfileUpdate, setIsLoadingProfileUpdate] = useState(false);

  // Hook pour la gestion des utilisateurs
  const { getUserMissions } = useUserManagement();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}`,
        {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des détails de l\'utilisateur');
      }

      const data = await response.json();

      if (data.success) {
        // Transformer les données du backend vers le format attendu par le frontend
        const backendData = data.data;
        const transformedData: UserDetailData = {
          id: backendData.user.id,
          name: backendData.profil ? `${backendData.profil.prenom || ''} ${backendData.profil.nom || ''}`.trim() : backendData.user.email,
          email: backendData.user.email,
          phone: backendData.profil?.telephone || undefined,
          city: backendData.profil?.ville || undefined,
          avatar: backendData.profil?.photo_url || undefined,
          role: backendData.user.role || 'jobutil',
          userType: backendData.user.user_type || 'non-jobbeur',
          status: backendData.user.profil_actif ? 'active' : 'inactive',
          isOnline: backendData.user.is_online || false,
          registrationDate: backendData.user.date_inscription || backendData.user.created_at,
          lastActivity: backendData.user.last_activity,
          verifications: {
            email: backendData.user.email_verifier || false,
            profile: backendData.user.profil_verifier || false,
            identity: backendData.user.identite_verifier || false,
            company: backendData.user.entreprise_verifier || false,
            insurance: backendData.user.assurance_verifier || false,
          },
          finances: {
            jobi: backendData.jobi?.montant || 0,
            aiCredits: backendData.aiCredits?.credits || 0,
            subscription: backendData.abonnements && backendData.abonnements.length > 0 ? {
              id: backendData.abonnements[0].id || '',
              type: backendData.abonnements[0].type_abonnement,
              status: backendData.abonnements[0].statut || 'active',
              start_date: backendData.abonnements[0].date_debut,
              end_date: backendData.abonnements[0].date_fin,
              auto_renew: backendData.abonnements[0].renouvellement_auto || false,
              price: backendData.abonnements[0].montant || 0,
              features: backendData.abonnements[0].options?.features || [],
              created_at: backendData.abonnements[0].created_at || new Date().toISOString(),
            } : undefined,
            subscriptionHistory: backendData.abonnements?.map((abo: any) => ({
              id: abo.id || '',
              type: abo.type_abonnement,
              status: abo.statut || 'active',
              start_date: abo.date_debut,
              end_date: abo.date_fin,
              auto_renew: abo.renouvellement_auto || false,
              price: abo.montant || 0,
              features: abo.options?.features || [],
              created_at: abo.created_at || new Date().toISOString(),
            })) || [],
            transactions: {
              total: backendData.transactions?.length || 0,
              recent: (backendData.transactions || []).slice(0, 10).map((t: any) => ({
                id: t.id,
                date: t.created_at,
                type: t.type || 'Transaction',
                amount: t.montant || 0,
                status: t.statut || 'completed'
              }))
            }
          },
          jobiHistory: backendData.jobiHistorique || [],
          aiCreditsHistory: backendData.aiCreditsHistorique || [],
          missions: [], // Les missions seront récupérées via l'API
          reviews: {
            total: backendData.reviewsReceived?.length || 0,
            average: backendData.reviewsReceived?.length > 0
              ? backendData.reviewsReceived.reduce((acc: number, r: any) => acc + (r.note || 0), 0) / backendData.reviewsReceived.length
              : 0,
            recent: (backendData.reviewsReceived || []).slice(0, 5).map((r: any) => ({
              id: r.id,
              rating: r.note || 0,
              comment: r.commentaire || '',
              date: r.created_at,
              from: {
                id: r.author_id,
                name: r.author?.email || 'Utilisateur anonyme'
              }
            }))
          },
          badges: backendData.badges || [],
          photos: [], // À implémenter si nécessaire
          galleries: backendData.galleries || [],
          featuredPhotos: backendData.featuredPhotos || [],
          conversations: backendData.messages || [],
          history: backendData.loginHistory?.map((h: any) => ({
            id: h.id,
            action: 'Connexion',
            date: h.login_date,
            details: `Connexion depuis ${h.ip_address || 'IP inconnue'}`
          })) || []
        };

        // Récupérer les vraies missions de l'utilisateur
        const missionsResult = await getUserMissions(transformedData.id);
        if (missionsResult.success && missionsResult.data) {
          transformedData.missions = missionsResult.data.map((mission: any) => ({
            id: mission.id,
            title: mission.title,
            description: mission.description,
            category: mission.category,
            status: mission.status === 'en_cours' ? 'in_progress' :
                   mission.status === 'terminee' ? 'completed' :
                   mission.status === 'annulee' ? 'cancelled' :
                   mission.status === 'en_attente' ? 'published' : 'draft',
            budget: mission.budget || 0,
            created_at: mission.created_at,
            start_date: mission.date_mission,
            end_date: mission.date_mission,
            location: mission.location || mission.ville || mission.adresse,
            client_name: 'Client', // Le créateur de la mission est l'utilisateur lui-même
            client_id: transformedData.id,
            applications_count: mission.applications_count || 0,
            candidatures: mission.candidatures || [],
            selected_freelancer: mission.candidatures?.find((c: any) => c.statut === 'acceptée') ? {
              id: mission.candidatures.find((c: any) => c.statut === 'acceptée').jobbeur_id,
              name: mission.candidatures.find((c: any) => c.statut === 'acceptée').jobbeur?.nom || 'Jobbeur'
            } : undefined,
            completion_percentage: mission.status === 'terminee' ? 100 :
                                 mission.status === 'en_cours' ? 50 : 0,
            rating: undefined, // À implémenter si nécessaire
            review: undefined
          }));
        }

        setUserDetails(transformedData);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des détails de l\'utilisateur');
      }
    } catch (apiError) {
      logger.warn('Erreur lors de la récupération des détails de l\'utilisateur:', apiError);
      setError(apiError instanceof Error ? apiError.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && user.id) {
      fetchUserDetails();
    }
  }, [open, user.id]);

  // Fonction pour ouvrir la modale de confirmation pour la gestion des Jobi
  const openJobiConfirmModal = () => {
    if (jobiAmount <= 0) return;
    setJobiConfirmModalOpen(true);
  };

  // Fonction pour exécuter l'action de gestion des Jobi après confirmation
  const executeJobiManagement = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}/jobi`,
        {
          method: 'POST',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            action: jobiAction,
            montant: jobiAmount, // Le backend attend 'montant' pas 'amount'
            description: jobiDescription,
            sendNotification: true,
            sendEmail: true
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de la gestion des Jobi: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Afficher une notification de succès
        const actionText = jobiAction === 'add' ? 'ajoutés' : 'retirés';
        const message = `${jobiAmount} Jobi ont été ${actionText} avec succès au compte de l'utilisateur.`;
        
        // Vous pouvez utiliser votre système de notification ici
        // Par exemple: showNotification({ message, type: 'success' });
        
        // Mettre à jour les détails de l'utilisateur
        fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
        // Réinitialiser les champs
        setJobiAmount(0);
        setJobiDescription('Gestion administrative des Jobi');
        // Fermer la modale de confirmation
        setJobiConfirmModalOpen(false);
      } else {
        throw new Error(data.message || 'Erreur lors de la gestion des Jobi');
      }
    } catch (error) {
      console.error('Erreur lors de la gestion des Jobi:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };
  
  // Fonction pour gérer le clic sur le bouton de gestion des Jobi
  const handleJobiManagement = () => {
    openJobiConfirmModal();
  };

  // Fonction pour ouvrir la modale de confirmation pour la gestion des crédits AI
  const openAiCreditsConfirmModal = () => {
    if (aiCreditsAmount <= 0) return;
    setAiCreditsConfirmModalOpen(true);
  };

  // Fonction pour exécuter l'action de gestion des crédits AI après confirmation
  const executeAiCreditsManagement = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}/ai-credits`,
        {
          method: 'POST',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            action: aiCreditsAction,
            credits: aiCreditsAmount, // Le backend attend 'credits' pas 'amount'
            description: aiCreditsDescription,
            sendNotification: true,
            sendEmail: true
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de la gestion des crédits AI: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Afficher une notification de succès
        const actionText = aiCreditsAction === 'add' ? 'ajoutés' : 'retirés';
        const message = `${aiCreditsAmount} crédits AI ont été ${actionText} avec succès au compte de l'utilisateur.`;
        
        // Vous pouvez utiliser votre système de notification ici
        // Par exemple: showNotification({ message, type: 'success' });
        
        // Mettre à jour les détails de l'utilisateur
        fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
        // Réinitialiser les champs
        setAiCreditsAmount(0);
        setAiCreditsDescription('Gestion administrative des crédits IA');
        // Fermer la modale de confirmation
        setAiCreditsConfirmModalOpen(false);
      } else {
        throw new Error(data.message || 'Erreur lors de la gestion des crédits AI');
      }
    } catch (error) {
      console.error('Erreur lors de la gestion des crédits AI:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };
  
  // Fonction pour gérer le clic sur le bouton de gestion des crédits AI
  const handleAiCreditsManagement = () => {
    openAiCreditsConfirmModal();
  };

  const handleSubscriptionManagement = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}/subscription`,
        {
          method: 'POST',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            action: subscriptionAction,
            type: subscriptionType,
            duration: subscriptionDuration
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de la gestion de l'abonnement: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Mettre à jour les détails de l'utilisateur
        fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
      } else {
        throw new Error(data.message || 'Erreur lors de la gestion de l\'abonnement');
      }
    } catch (error) {
      console.error('Erreur lors de la gestion de l\'abonnement:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}/status`,
        {
          method: 'POST',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            action: userActionType,
            reason: userActionReason
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de l'action sur l'utilisateur: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Mettre à jour les détails de l'utilisateur
        fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
      } else {
        throw new Error(data.message || 'Erreur lors de l\'action sur l\'utilisateur');
      }
    } catch (error) {
      console.error('Erreur lors de l\'action sur l\'utilisateur:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  const handleForceVerification = async (field: string, value: boolean) => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/users/verification/force-status`,
        {
          method: 'POST',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            userId: user.id,
            field: field,
            value: value
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de la mise à jour de la vérification: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Mettre à jour les détails de l'utilisateur
        await fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
      } else {
        throw new Error(data.error || 'Erreur lors de la mise à jour de la vérification');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la vérification:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async (profileData: any) => {
    try {
      setIsLoadingProfileUpdate(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${user.id}/profile`,
        {
          method: 'PUT',
          headers: headers,
          credentials: 'include',
          body: JSON.stringify({
            ...profileData,
            reason: 'Modification administrative du profil',
            sendNotification: true,
            sendEmail: true
          })
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur lors de la mise à jour du profil: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Mettre à jour les détails de l'utilisateur
        await fetchUserDetails();
        // Déclencher la mise à jour de la liste des utilisateurs
        onUserUpdate();
        // Fermer le mode édition
        setIsEditingProfile(false);
      } else {
        throw new Error(data.message || 'Erreur lors de la mise à jour du profil');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setIsLoadingProfileUpdate(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Non disponible';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusColor = (status: string | undefined | null): ChipColor => {
    if (!status) {
      return 'neutral';
    }
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'suspended':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'neutral';
    }
  };

  const getStatusLabel = (status: string | undefined | null) => {
    if (!status) {
      return 'Inconnu';
    }
    switch (status.toLowerCase()) {
      case 'active':
        return 'Actif';
      case 'suspended':
        return 'Suspendu';
      case 'pending':
        return 'En attente';
      default:
        return status;
    }
  };

  const getUserTypeLabel = (userType: string | undefined | null) => {
    switch ((userType || '').toLowerCase()) {
      case 'freelancer':
        return 'Freelance';
      case 'company':
        return 'Entreprise';
      case 'client':
        return 'Client';
      default:
        return userType;
    }
  };

  const getUserTypeIcon = (userType: string | undefined | null) => {
    switch ((userType || '').toLowerCase()) {
      case 'freelancer':
        return <User size={16} />;
      case 'company':
        return <Building size={16} />;
      case 'client':
        return <Briefcase size={16} />;
      default:
        return <User size={16} />;
    }
  };

  const getRoleLabel = (role: string | undefined | null) => {
    if (!role) {
      return 'Inconnu';
    }
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Administrateur';
      case 'moderator':
        return 'Modérateur';
      case 'user':
        return 'Utilisateur';
      default:
        return role;
    }
  };

  if (loading && !userDetails) {
    return (
      <StyledDialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <StyledDialogTitle>
          Chargement des détails de l'utilisateur...
        </StyledDialogTitle>
        <StyledDialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress sx={{ color: COLORS.primary }} />
          </Box>
        </StyledDialogContent>
      </StyledDialog>
    );
  }

  if (error) {
    return (
      <StyledDialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <StyledDialogTitle>
          Erreur
        </StyledDialogTitle>
        <StyledDialogContent>
          <Alert 
            severity="error" 
            sx={{ 
              borderRadius: '12px',
              border: `1px solid ${COLORS.error}20`
            }}
          >
            {error}
          </Alert>
        </StyledDialogContent>
        <StyledDialogActions>
          <StyledButton onClick={onClose} variant="outlined">
            Fermer
          </StyledButton>
        </StyledDialogActions>
      </StyledDialog>
    );
  }

  return (
    <StyledDialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      {userDetails ? (
        <>
          <StyledDialogTitle>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box display="flex" alignItems="center">
                <Avatar 
                  src={userDetails.avatar} 
                  alt={userDetails.name}
                  sx={{ 
                    width: 48, 
                    height: 48, 
                    marginRight: 2,
                    border: `2px solid ${COLORS.primary}`
                  }}
                >
                  {userDetails.name ? userDetails.name.charAt(0) : ''}
                </Avatar>
                <Box>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="h6" component="div">
                      {userDetails.name}
                    </Typography>
                    {userDetails.isOnline && (
                      <Tooltip title="En ligne">
                        <Wifi size={16} color={COLORS.success} />
                      </Tooltip>
                    )}
                  </Box>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" color="text.secondary">
                      {userDetails.email}
                    </Typography>
                    <StyledChip 
                      label={getRoleLabel(userDetails.role)} 
                      size="small" 
                      color={(userDetails.role && userDetails.role.toLowerCase() === 'admin') ? 'error' : 'info'}
                      variant="filled"
                    />
                    <StyledChip 
                      label={getStatusLabel(userDetails.status)} 
                      size="small" 
                      color={getStatusColor(userDetails.status) as ChipColor}
                      variant="filled"
                    />
                  </Box>
                </Box>
              </Box>
              <IconButton onClick={onClose}>
                <XCircle size={20} />
              </IconButton>
            </Box>
          </StyledDialogTitle>
          
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <StyledTabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
              <Tab label="Profil" icon={<User size={16} />} iconPosition="start" />
              <Tab label="Finances" icon={<DollarSign size={16} />} iconPosition="start" />
              <Tab label="Abonnements" icon={<Award size={16} />} iconPosition="start" />
              <Tab label="Missions" icon={<Briefcase size={16} />} iconPosition="start" />
              <Tab label="Avis & Badges" icon={<Star size={16} />} iconPosition="start" />
              <Tab label="Photos & Galeries" icon={<Image size={16} />} iconPosition="start" />
              <Tab label="Messages" icon={<Mail size={16} />} iconPosition="start" />
              <Tab label="Historique" icon={<History size={16} />} iconPosition="start" />
              <Tab label="Statistiques" icon={<BarChart2 size={16} />} iconPosition="start" />
              <Tab label="Rapport Avancé" icon={<AlertTriangle size={16} />} iconPosition="start" />
              <Tab label="Actions Admin" icon={<Settings size={16} />} iconPosition="start" />
            </StyledTabs>
          </Box>
          
          <StyledDialogContent>
            {/* Onglet Profil */}
            {activeTab === 0 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <StyledCard>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <SectionTitle>Informations personnelles</SectionTitle>
                        <Button
                          size="small"
                          startIcon={isEditingProfile ? <X size={16} /> : <Edit size={16} />}
                          onClick={() => setIsEditingProfile(!isEditingProfile)}
                          sx={{
                            color: isEditingProfile ? COLORS.error : COLORS.primary,
                            '&:hover': {
                              backgroundColor: isEditingProfile ? `${COLORS.error}15` : `${COLORS.primary}15`
                            }
                          }}
                        >
                          {isEditingProfile ? 'Annuler' : 'Modifier'}
                        </Button>
                      </Box>

                      {isEditingProfile ? (
                        <UserProfileEditForm
                          userDetails={userDetails}
                          onSave={handleSaveProfile}
                          onCancel={() => setIsEditingProfile(false)}
                          isLoading={isLoadingProfileUpdate}
                        />
                      ) : (
                        <List>
                          <ListItem>
                            <ListItemIcon>
                              <User size={20} color={COLORS.primary} />
                            </ListItemIcon>
                            <ListItemText
                              primary="Nom complet"
                              secondary={userDetails.name}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Mail size={20} color={COLORS.primary} />
                            </ListItemIcon>
                            <ListItemText
                              primary="Email"
                              secondary={userDetails.email}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Phone size={20} color={COLORS.primary} />
                            </ListItemIcon>
                            <ListItemText
                              primary="Téléphone"
                              secondary={userDetails.phone || 'Non renseigné'}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <MapPin size={20} color={COLORS.primary} />
                            </ListItemIcon>
                            <ListItemText
                              primary="Ville"
                              secondary={userDetails.city || 'Non renseignée'}
                            />
                          </ListItem>
                        </List>
                      )}
                    </CardContent>
                  </StyledCard>
                </Grid>
                
                <Grid size={{ xs: 12, md: 6 }}>  
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Statut du compte</SectionTitle>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            {getUserTypeIcon(userDetails.userType)}
                          </ListItemIcon>
                          <ListItemText 
                            primary="Type d'utilisateur" 
                            secondary={
                                <StyledChip 
                                  label={getUserTypeLabel(userDetails.userType)} 
                                  size="small" 
                                  color={'primary'}
                                  variant="filled"
                                />
                            } 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Calendar size={20} color={COLORS.primary} />
                          </ListItemIcon>
                          <ListItemText 
                            primary="Date d'inscription" 
                            secondary={formatDate(userDetails.registrationDate)} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Clock size={20} color={COLORS.primary} />
                          </ListItemIcon>
                          <ListItemText 
                            primary="Dernière activité" 
                            secondary={userDetails.lastActivity ? formatDate(userDetails.lastActivity) : 'Jamais connecté'} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <Wifi size={20} color={userDetails.isOnline ? COLORS.success : COLORS.neutral} />
                          </ListItemIcon>
                          <ListItemText 
                            primary="Statut de connexion" 
                            secondary={
                                <StyledChip 
                                  label={userDetails.isOnline ? 'En ligne' : 'Hors ligne'} 
                                  size="small" 
                                  color={userDetails.isOnline ? 'success' : 'neutral'}
                                  variant="filled"
                                />
                            } 
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </StyledCard>
                </Grid>
                
                <Grid size={{ xs: 12 }}> 
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Vérifications</SectionTitle>
                      <Alert severity="warning" sx={{ mb: 2, bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 600, border: '1px solid #FF965E' }}>
                        Cette section est hyper critique et peut faire bugger le profil. À utiliser qu'en cas de nécessité absolue !
                      </Alert>
                      <Grid container spacing={2}>
                        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              alignItems: 'center',
                              p: 2,
                              borderRadius: '8px',
                              backgroundColor: userDetails.verifications?.email ? `${COLORS.success}10` : `${COLORS.error}10`,
                              border: `1px solid ${userDetails.verifications?.email ? `${COLORS.success}30` : `${COLORS.error}30`}`
                            }}
                          >
                            {userDetails.verifications?.email ? (
                              <CheckCircle size={32} color={COLORS.success} />
                            ) : (
                              <XCircle size={32} color={COLORS.error} />
                            )}
                            <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                              Email
                            </Typography>
                            <Typography variant="caption" align="center" color="text.secondary" sx={{ mb: 1 }}>
                              {userDetails.verifications?.email ? 'Vérifié' : 'Non vérifié'}
                            </Typography>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={userDetails.verifications?.email ? 'true' : 'false'}
                                onChange={(e) => handleForceVerification('email_verifier', e.target.value === 'true')}
                                sx={{ 
                                  '& .MuiSelect-select': { 
                                    py: 0.3,
                                    fontSize: '0.75rem'
                                  }
                                }}
                              >
                                <MenuItem value="true">Vérifié</MenuItem>
                                <MenuItem value="false">Non vérifié</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                        </Grid>
                        
                        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              alignItems: 'center',
                              p: 2,
                              borderRadius: '8px',
                              backgroundColor: userDetails.verifications?.profile ? `${COLORS.success}10` : `${COLORS.error}10`,
                              border: `1px solid ${userDetails.verifications?.profile ? `${COLORS.success}30` : `${COLORS.error}30`}`
                            }}
                          >
                            {userDetails.verifications?.profile ? (
                              <CheckCircle size={32} color={COLORS.success} />
                            ) : (
                              <XCircle size={32} color={COLORS.error} />
                            )}
                            <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                              Profil
                            </Typography>
                            <Typography variant="caption" align="center" color="text.secondary" sx={{ mb: 1 }}>
                              {userDetails.verifications?.profile ? 'Complet' : 'Incomplet'}
                            </Typography>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={userDetails.verifications?.profile ? 'true' : 'false'}
                                onChange={(e) => handleForceVerification('profil_verifier', e.target.value === 'true')}
                                sx={{ 
                                  '& .MuiSelect-select': { 
                                    py: 0.3,
                                    fontSize: '0.75rem'
                                  }
                                }}
                              >
                                <MenuItem value="true">Vérifié</MenuItem>
                                <MenuItem value="false">Non vérifié</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                        </Grid>
                        
                        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              alignItems: 'center',
                              p: 2,
                              borderRadius: '8px',
                              backgroundColor: userDetails.verifications?.identity ? `${COLORS.success}10` : `${COLORS.error}10`,
                              border: `1px solid ${userDetails.verifications?.identity ? `${COLORS.success}30` : `${COLORS.error}30`}`
                            }}
                          >
                            {userDetails.verifications?.identity ? (
                              <CheckCircle size={32} color={COLORS.success} />
                            ) : (
                              <XCircle size={32} color={COLORS.error} />
                            )}
                            <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                              Identité
                            </Typography>
                            <Typography variant="caption" align="center" color="text.secondary" sx={{ mb: 1 }}>
                              {userDetails.verifications?.identity ? 'Vérifiée' : 'Non vérifiée'}
                            </Typography>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={userDetails.verifications?.identity ? 'true' : 'false'}
                                onChange={(e) => handleForceVerification('identite_verifier', e.target.value === 'true')}
                                sx={{ 
                                  '& .MuiSelect-select': { 
                                    py: 0.3,
                                    fontSize: '0.75rem'
                                  }
                                }}
                              >
                                <MenuItem value="true">Vérifié</MenuItem>
                                <MenuItem value="false">Non vérifié</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                        </Grid>
                        
                        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              alignItems: 'center',
                              p: 2,
                              borderRadius: '8px',
                              backgroundColor: userDetails.verifications?.company ? `${COLORS.success}10` : `${COLORS.error}10`,
                              border: `1px solid ${userDetails.verifications?.company ? `${COLORS.success}30` : `${COLORS.error}30`}`
                            }}
                          >
                            {userDetails.verifications?.company ? (
                              <CheckCircle size={32} color={COLORS.success} />
                            ) : (
                              <XCircle size={32} color={COLORS.error} />
                            )}
                            <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                              Entreprise
                            </Typography>
                            <Typography variant="caption" align="center" color="text.secondary" sx={{ mb: 1 }}>
                              {userDetails.verifications?.company ? 'Vérifiée' : 'Non vérifiée'}
                            </Typography>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={userDetails.verifications?.company ? 'true' : 'false'}
                                onChange={(e) => handleForceVerification('entreprise_verifier', e.target.value === 'true')}
                                sx={{ 
                                  '& .MuiSelect-select': { 
                                    py: 0.3,
                                    fontSize: '0.75rem'
                                  }
                                }}
                              >
                                <MenuItem value="true">Vérifié</MenuItem>
                                <MenuItem value="false">Non vérifié</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                        </Grid>
                        
                        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              flexDirection: 'column', 
                              alignItems: 'center',
                              p: 2,
                              borderRadius: '8px',
                              backgroundColor: userDetails.verifications?.insurance ? `${COLORS.success}10` : `${COLORS.error}10`,
                              border: `1px solid ${userDetails.verifications?.insurance ? `${COLORS.success}30` : `${COLORS.error}30`}`
                            }}
                          >
                            {userDetails.verifications?.insurance ? (
                              <CheckCircle size={32} color={COLORS.success} />
                            ) : (
                              <XCircle size={32} color={COLORS.error} />
                            )}
                            <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                              Assurance
                            </Typography>
                            <Typography variant="caption" align="center" color="text.secondary" sx={{ mb: 1 }}>
                              {userDetails.verifications?.insurance ? 'Vérifiée' : 'Non vérifiée'}
                            </Typography>
                            <FormControl size="small" sx={{ minWidth: 100 }}>
                              <Select
                                value={userDetails.verifications?.insurance ? 'true' : 'false'}
                                onChange={(e) => handleForceVerification('assurance_verifier', e.target.value === 'true')}
                                sx={{ 
                                  '& .MuiSelect-select': { 
                                    py: 0.3,
                                    fontSize: '0.75rem'
                                  }
                                }}
                              >
                                <MenuItem value="true">Vérifié</MenuItem>
                                <MenuItem value="false">Non vérifié</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </StyledCard>
                </Grid>


              </Grid>
            )}
            
            {/* Onglet Finances */}
            {activeTab === 1 && (
              <Box>
                {/* Sous-onglets pour les finances */}
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs 
                    value={financeSubTab} 
                    onChange={(e, newValue) => setFinanceSubTab(newValue)}
                    variant="scrollable"
                    scrollButtons="auto"
                  >
                    <Tab label="Vue d'ensemble" />
                    <Tab label="Historique Jobi" />
                    <Tab label="Historique Crédits IA" />
                    <Tab label="Détails Abonnement" />
                  </Tabs>
                </Box>

                {/* Vue d'ensemble */}
                {financeSubTab === 0 && (
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <StyledCard>
                        <CardContent>
                          <SectionTitle>Solde et Crédits</SectionTitle>
                          <Grid container spacing={3}>
                            <Grid size={{ xs: 12, sm: 6 }}>
                              <Box 
                                sx={{ 
                                  p: 2, 
                                  borderRadius: '12px', 
                                  backgroundColor: `${COLORS.primary}10`,
                                  border: `1px solid ${COLORS.primary}30`,
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                <DollarSign size={32} color={COLORS.primary} />
                                <Typography variant="h4" fontWeight="bold" color={COLORS.primary} sx={{ mt: 1 }}>
                                  {userDetails?.finances?.jobi || 0}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Jobi disponibles
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                              <Box 
                                sx={{ 
                                  p: 2, 
                                  borderRadius: '12px', 
                                  backgroundColor: `${COLORS.warning}10`,
                                  border: `1px solid ${COLORS.warning}30`,
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                <Zap size={32} color={COLORS.warning} />
                                <Typography variant="h4" fontWeight="bold" color={COLORS.warning} sx={{ mt: 1 }}>
                                  {userDetails?.finances?.aiCredits || 0}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Crédits AI disponibles
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </CardContent>
                      </StyledCard>
                    </Grid>
                    
                    <Grid size={{ xs: 12, md: 6 }}>
                      <StyledCard>
                        <CardContent>
                          <SectionTitle>Abonnement Actuel</SectionTitle>
                          {userDetails?.finances?.subscription ? (
                            <Box>
                              <Box 
                                sx={{ 
                                  p: 2, 
                                  borderRadius: '12px', 
                                  backgroundColor: `${COLORS.info}10`,
                                  border: `1px solid ${COLORS.info}30`,
                                  mb: 2
                                }}
                              >
                                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                                  <Box display="flex" alignItems="center">
                                    <Award size={24} color={COLORS.info} style={{ marginRight: '8px' }} />
                                    <Typography variant="h6" color={COLORS.info}>
                                      {userDetails.finances.subscription.type}
                                    </Typography>
                                  </Box>
                                  <StyledChip
                                    label={userDetails.finances.subscription.auto_renew ? 'Renouvellement auto' : 'Sans renouvellement'}
                                    size="small"
                                    color={userDetails.finances.subscription.auto_renew ? 'success' : 'warning'}
                                    variant="filled"
                                  />
                                </Box>
                                
                                {/* Prix de l'abonnement */}
                                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                                  <Typography variant="body2" color="text.secondary">
                                    Prix mensuel
                                  </Typography>
                                  <Typography variant="h6" color={COLORS.primary} fontWeight="bold">
                                    {userDetails.finances.subscription.price ? 
                                      `${userDetails.finances.subscription.price}€/mois` : 
                                      'Gratuit'
                                    }
                                  </Typography>
                                </Box>
                              </Box>
                              
                              <Grid container spacing={2}>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body2" color="text.secondary">
                                    Date de début
                                  </Typography>
                                  <Typography variant="body1">
                                    {formatDate(userDetails.finances.subscription.start_date)}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body2" color="text.secondary">
                                    Date de fin
                                  </Typography>
                                  <Typography variant="body1">
                                    {formatDate(userDetails.finances.subscription.end_date)}
                                  </Typography>
                                </Grid>
                              </Grid>

                              {/* Fonctionnalités incluses */}
                              {userDetails.finances.subscription.features && userDetails.finances.subscription.features.length > 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Fonctionnalités incluses:
                                  </Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                    {userDetails.finances.subscription.features.map((feature, index) => (
                                      <StyledChip
                                        key={index}
                                        label={feature}
                                        size="small"
                                        color="success"
                                        variant="outlined"
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          ) : (
                            <Box 
                              sx={{ 
                                p: 3, 
                                borderRadius: '12px', 
                                backgroundColor: `${COLORS.neutral}10`,
                                border: `1px solid ${COLORS.neutral}30`,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <AlertTriangle size={32} color={COLORS.neutral} />
                              <Typography variant="body1" sx={{ mt: 1 }}>
                                Aucun abonnement actif
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </StyledCard>
                    </Grid>
                    
                    <Grid size={{ xs: 12 }}>
                      <StyledCard>
                        <CardContent>
                          <Grid container spacing={1} alignItems="center">
                            <Grid size={{ xs: 12, sm: 6 }}>
                              <SectionTitle>Transactions récentes</SectionTitle>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }} sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
                              <Typography variant="body2" color="text.secondary">
                                Total: {userDetails?.finances?.transactions?.total || 0} transactions
                              </Typography>
                            </Grid>
                          </Grid>
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, fontStyle: 'italic' }}>
                            Ce sont les propres transactions de l'utilisateur enregistrées dans son tableau de bord, section Paiements et finances.
                          </Typography>
                          
                          {userDetails?.finances?.transactions?.recent && userDetails.finances.transactions.recent.length > 0 ? (
                            <Box sx={{ overflowX: 'auto' }}>
                              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                <thead>
                                  <tr>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>ID</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Date</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Type</th>
                                    <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Montant</th>
                                    <th style={{ textAlign: 'center', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Statut</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {userDetails.finances.transactions.recent.map((transaction) => (
                                    <tr key={transaction.id}>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>{transaction.id}</td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>{formatDate(transaction.date)}</td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>{transaction.type}</td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'right' }}>
                                        {new Intl.NumberFormat('fr-FR', {
                                          style: 'currency',
                                          currency: 'EUR'
                                        }).format(transaction.amount)}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'center' }}>
                                        <StyledChip 
                                          label={transaction.status} 
                                          size="small" 
                                          color={transaction.status.toLowerCase() === 'completed' ? 'success' : 
                                                 transaction.status.toLowerCase() === 'pending' ? 'warning' : 
                                                 transaction.status.toLowerCase() === 'failed' ? 'error' : 
                                                 'neutral'}
                                          variant="filled"
                                        />
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </Box>
                          ) : (
                            <Box 
                              sx={{ 
                                p: 3, 
                                borderRadius: '12px', 
                                backgroundColor: `${COLORS.neutral}10`,
                                border: `1px solid ${COLORS.neutral}30`,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <AlertTriangle size={32} color={COLORS.neutral} />
                              <Typography variant="body1" sx={{ mt: 1 }}>
                                Aucune transaction récente
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </StyledCard>
                    </Grid>
                  </Grid>
                )}

                {/* Historique Jobi */}
                {financeSubTab === 1 && (
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12 }}>
                      <StyledCard>
                        <CardContent>
                          <SectionTitle>Historique des transactions Jobi</SectionTitle>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Historique complet de toutes les transactions Jobi de l'utilisateur (gains, dépenses, transferts, etc.)
                          </Typography>
                          
                          {userDetails?.jobiHistory && userDetails.jobiHistory.length > 0 ? (
                            <Box sx={{ overflowX: 'auto' }}>
                              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                <thead>
                                  <tr>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Date</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Titre</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Description</th>
                                    <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Montant</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Message</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {userDetails.jobiHistory.map((transaction) => (
                                    <tr key={transaction.id}>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {formatDate(transaction.date_creation)}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {transaction.titre}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {transaction.description}
                                      </td>
                                      <td style={{ 
                                        padding: '12px 16px', 
                                        borderBottom: `1px solid ${COLORS.borderColor}`, 
                                        textAlign: 'right',
                                        color: transaction.montant >= 0 ? COLORS.success : COLORS.error,
                                        fontWeight: 'bold'
                                      }}>
                                        {transaction.montant >= 0 ? '+' : ''}{transaction.montant} Jobi
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {transaction.message || '-'}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </Box>
                          ) : (
                            <Box 
                              sx={{ 
                                p: 3, 
                                borderRadius: '12px', 
                                backgroundColor: `${COLORS.neutral}10`,
                                border: `1px solid ${COLORS.neutral}30`,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <AlertTriangle size={32} color={COLORS.neutral} />
                              <Typography variant="body1" sx={{ mt: 1 }}>
                                Aucune transaction Jobi trouvée
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </StyledCard>
                    </Grid>
                  </Grid>
                )}

                {/* Historique Crédits IA */}
                {financeSubTab === 2 && (
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12 }}>
                      <StyledCard>
                        <CardContent>
                          <SectionTitle>Historique des crédits IA</SectionTitle>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Historique complet de toutes les opérations sur les crédits IA (achats, utilisations, bonus, etc.)
                          </Typography>
                          
                          {userDetails?.aiCreditsHistory && userDetails.aiCreditsHistory.length > 0 ? (
                            <Box sx={{ overflowX: 'auto' }}>
                              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                <thead>
                                  <tr>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Date</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Type d'opération</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Description</th>
                                    <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Montant</th>
                                    <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Solde avant</th>
                                    <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Solde après</th>
                                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Référence</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {userDetails.aiCreditsHistory.map((operation) => (
                                    <tr key={operation.id}>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {formatDate(operation.created_at)}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        <StyledChip
                                          label={operation.operation_type}
                                          size="small"
                                          color={
                                            operation.operation_type === 'utilisation' ? 'error' :
                                            operation.operation_type === 'achat_jobi' || operation.operation_type === 'achat_stripe' ? 'success' :
                                            operation.operation_type === 'offert_abonnement' || operation.operation_type === 'offert_admin' ? 'info' :
                                            'neutral'
                                          }
                                          variant="filled"
                                        />
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {operation.description || '-'}
                                      </td>
                                      <td style={{ 
                                        padding: '12px 16px', 
                                        borderBottom: `1px solid ${COLORS.borderColor}`, 
                                        textAlign: 'right',
                                        color: operation.montant >= 0 ? COLORS.success : COLORS.error,
                                        fontWeight: 'bold'
                                      }}>
                                        {operation.montant >= 0 ? '+' : ''}{operation.montant}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'right' }}>
                                        {operation.solde_avant}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'right' }}>
                                        {operation.solde_apres}
                                      </td>
                                      <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                        {operation.reference || '-'}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </Box>
                          ) : (
                            <Box 
                              sx={{ 
                                p: 3, 
                                borderRadius: '12px', 
                                backgroundColor: `${COLORS.neutral}10`,
                                border: `1px solid ${COLORS.neutral}30`,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <AlertTriangle size={32} color={COLORS.neutral} />
                              <Typography variant="body1" sx={{ mt: 1 }}>
                                Aucune opération sur les crédits IA trouvée
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </StyledCard>
                    </Grid>
                  </Grid>
                )}

                {/* Détails Abonnement */}
                {financeSubTab === 3 && (
                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12 }}>
                      <StyledCard>
                        <CardContent>
                          <SectionTitle>Détails complets de l'abonnement</SectionTitle>
                          
                          {userDetails?.finances?.subscription ? (
                            <Box>
                              {/* Informations principales */}
                              <Grid container spacing={3} sx={{ mb: 3 }}>
                                <Grid size={{ xs: 12, md: 6 }}>
                                  <Box 
                                    sx={{ 
                                      p: 3, 
                                      borderRadius: '12px', 
                                      backgroundColor: `${COLORS.info}10`,
                                      border: `1px solid ${COLORS.info}30`
                                    }}
                                  >
                                    <Typography variant="h5" color={COLORS.info} gutterBottom>
                                      Plan {userDetails.finances.subscription.type}
                                    </Typography>
                                    <Typography variant="h4" color={COLORS.primary} fontWeight="bold">
                                      {userDetails.finances.subscription.price ? 
                                        `${userDetails.finances.subscription.price}€/mois` : 
                                        'Gratuit'
                                      }
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                      {userDetails.finances.subscription.auto_renew ? 
                                        'Renouvellement automatique activé' : 
                                        'Renouvellement automatique désactivé'
                                      }
                                    </Typography>
                                  </Box>
                                </Grid>
                                
                                <Grid size={{ xs: 12, md: 6 }}>
                                  <Box 
                                    sx={{ 
                                      p: 3, 
                                      borderRadius: '12px', 
                                      backgroundColor: `${COLORS.background}`,
                                      border: `1px solid ${COLORS.borderColor}`
                                    }}
                                  >
                                    <Typography variant="subtitle1" gutterBottom>
                                      Période d'abonnement
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 1 }}>
                                      <strong>Début:</strong> {formatDate(userDetails.finances.subscription.start_date)}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 1 }}>
                                      <strong>Fin:</strong> {formatDate(userDetails.finances.subscription.end_date)}
                                    </Typography>
                                    <Typography variant="body1">
                                      <strong>Créé le:</strong> {formatDate(userDetails.finances.subscription.created_at)}
                                    </Typography>
                                  </Box>
                                </Grid>
                              </Grid>

                              {/* Fonctionnalités détaillées */}
                              {userDetails.finances.subscription.features && userDetails.finances.subscription.features.length > 0 && (
                                <Box sx={{ mb: 3 }}>
                                  <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary }}>
                                    Fonctionnalités incluses
                                  </Typography>
                                  <Grid container spacing={2}>
                                    {userDetails.finances.subscription.features.map((feature, index) => (
                                      <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                                        <Box 
                                          sx={{ 
                                            p: 2, 
                                            borderRadius: '8px', 
                                            backgroundColor: `${COLORS.success}10`,
                                            border: `1px solid ${COLORS.success}30`,
                                            display: 'flex',
                                            alignItems: 'center'
                                          }}
                                        >
                                          <CheckCircle size={20} color={COLORS.success} style={{ marginRight: '8px' }} />
                                          <Typography variant="body2">
                                            {feature}
                                          </Typography>
                                        </Box>
                                      </Grid>
                                    ))}
                                  </Grid>
                                </Box>
                              )}

                              {/* Historique des abonnements */}
                              {userDetails?.finances?.subscriptionHistory && userDetails.finances.subscriptionHistory.length > 0 && (
                                <Box>
                                  <Typography variant="h6" gutterBottom sx={{ color: COLORS.primary }}>
                                    Historique des abonnements
                                  </Typography>
                                  <Box sx={{ overflowX: 'auto' }}>
                                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                      <thead>
                                        <tr>
                                          <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Type</th>
                                          <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Statut</th>
                                          <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Début</th>
                                          <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Fin</th>
                                          <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Prix</th>
                                          <th style={{ textAlign: 'center', padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>Auto-renouv.</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {userDetails.finances.subscriptionHistory.map((sub) => (
                                          <tr key={sub.id}>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                              <StyledChip
                                                label={sub.type}
                                                size="small"
                                                color={sub.type === 'premium' ? 'info' : sub.type === 'pro' ? 'primary' : 'neutral'}
                                                variant="filled"
                                              />
                                            </td>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                              <StyledChip
                                                label={sub.status}
                                                size="small"
                                                color={getStatusColor(sub.status)}
                                                variant="filled"
                                              />
                                            </td>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                              {formatDate(sub.start_date)}
                                            </td>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}` }}>
                                              {formatDate(sub.end_date)}
                                            </td>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'right' }}>
                                              {sub.price ? `${sub.price}€` : 'Gratuit'}
                                            </td>
                                            <td style={{ padding: '12px 16px', borderBottom: `1px solid ${COLORS.borderColor}`, textAlign: 'center' }}>
                                              {sub.auto_renew ? (
                                                <CheckCircle size={16} color={COLORS.success} />
                                              ) : (
                                                <XCircle size={16} color={COLORS.error} />
                                              )}
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          ) : (
                            <Box 
                              sx={{ 
                                p: 4, 
                                borderRadius: '12px', 
                                backgroundColor: `${COLORS.neutral}10`,
                                border: `1px solid ${COLORS.neutral}30`,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <AlertTriangle size={48} color={COLORS.neutral} />
                              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                                Aucun abonnement actif
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Cet utilisateur n'a pas d'abonnement en cours
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </StyledCard>
                    </Grid>
                  </Grid>
                )}
              </Box>
            )}

            {/* Onglet Abonnements */}
            {activeTab === 2 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserSubscriptionManagement
                    userId={userDetails.id}
                    subscription={userDetails.finances?.subscription ? transformSubscriptionForComponent(userDetails.finances.subscription) : undefined}
                    subscriptionHistory={(userDetails.finances?.subscriptionHistory || []).map(transformSubscriptionForComponent)}
                    onUpdate={() => {
                      fetchUserDetails();
                      onUserUpdate();
                    }}
                  />
                </Grid>
              </Grid>
            )}

            {/* Onglet Missions */}
            {activeTab === 3 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserMissionsManagement
                    userId={userDetails.id}
                    missions={userDetails.missions || []}
                    onUpdate={() => {
                      fetchUserDetails();
                      onUserUpdate();
                    }}
                  />
                </Grid>
              </Grid>
            )}

            {/* Onglet Avis & Badges */}
            {activeTab === 4 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserBadgesManagement
                    userId={userDetails.id}
                    badges={userDetails.badges || []}
                    onUpdate={() => {
                      fetchUserDetails();
                      onUserUpdate();
                    }}
                  />
                </Grid>
              </Grid>
            )}

            {/* Onglet Photos & Galeries */}
            {activeTab === 5 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserPhotosManagement
                    userId={userDetails.id}
                    galleries={userDetails.galleries || []}
                    featuredPhotos={userDetails.featuredPhotos || []}
                    onUpdate={() => {
                      fetchUserDetails();
                      onUserUpdate();
                    }}
                  />
                </Grid>
              </Grid>
            )}

            {/* Onglet Messages */}
            {activeTab === 6 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserMessagesManagement
                    userId={userDetails.id}
                    conversations={userDetails.conversations || []}
                    onUpdate={() => {
                      fetchUserDetails();
                      onUserUpdate();
                    }}
                  />
                </Grid>
              </Grid>
            )}

            {/* Onglet Historique */}
            {activeTab === 7 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserActivityHistory userId={userDetails.id} />
                </Grid>
              </Grid>
            )}

            {/* Onglet Statistiques */}
            {activeTab === 8 && (
              <UserStatsCharts userId={userDetails.id} />
            )}

            {/* Onglet Rapport Avancé */}
            {activeTab === 9 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <UserAdvancedReport userId={userDetails.id} />
                </Grid>
              </Grid>
            )}

            {/* Onglet Actions Admin */}
            {activeTab === 10 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Gestion des Jobi</SectionTitle>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Ajoutez ou retirez des Jobi au compte de l'utilisateur. Les Jobi sont la monnaie virtuelle de la plateforme.
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        <Grid container spacing={2}>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Action</InputLabel>
                              <Select
                                value={jobiAction}
                                onChange={(e) => setJobiAction(e.target.value as 'add' | 'remove')}
                                label="Action"
                                sx={{
                                  bgcolor: jobiAction === 'add' ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)',
                                }}
                              >
                                <MenuItem value="add">Ajouter</MenuItem>
                                <MenuItem value="remove">Retirer</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                              label="Montant"
                              type="number"
                              value={jobiAmount}
                              onChange={(e) => setJobiAmount(parseInt(e.target.value) || 0)}
                              fullWidth
                              InputProps={{
                                inputProps: { min: 0 },
                                startAdornment: <DollarSign size={16} style={{ marginRight: '8px', opacity: 0.7 }} />
                              }}
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: '8px',
                                  bgcolor: 'rgba(52, 152, 219, 0.05)'
                                }
                              }}
                            />
                          </Grid>
                          <Grid size={{ xs: 12 }}>
                            <TextField
                              label="Description (optionnelle)"
                              value={jobiDescription}
                              onChange={(e) => setJobiDescription(e.target.value)}
                              fullWidth
                              margin="normal"
                              placeholder="Raison de l'ajout ou du retrait des Jobi"
                              sx={{
                                mb: 2,
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: '8px',
                                }
                              }}
                            />
                            <StyledButton
                              variant="contained"
                              onClick={handleJobiManagement}
                              startIcon={jobiAction === 'add' ? <Plus size={16} /> : <Minus size={16} />}
                              fullWidth
                              disabled={jobiAmount <= 0}
                              sx={{
                                bgcolor: jobiAction === 'add' ? '#2ecc71' : '#e74c3c',
                                '&:hover': {
                                  bgcolor: jobiAction === 'add' ? '#27ae60' : '#c0392b',
                                }
                              }}
                            >
                              {jobiAction === 'add' ? 'Ajouter des Jobi' : 'Retirer des Jobi'}
                            </StyledButton>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>
                
                <Grid size={{ xs: 12, md: 6 }}>
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Gestion des crédits AI</SectionTitle>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Ajoutez ou retirez des crédits AI au compte de l'utilisateur. Les crédits AI permettent d'utiliser les fonctionnalités d'intelligence artificielle.
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        <Grid container spacing={2}>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Action</InputLabel>
                              <Select
                                value={aiCreditsAction}
                                onChange={(e) => setAiCreditsAction(e.target.value as 'add' | 'remove')}
                                label="Action"
                                sx={{
                                  bgcolor: aiCreditsAction === 'add' ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)',
                                }}
                              >
                                <MenuItem value="add">Ajouter</MenuItem>
                                <MenuItem value="remove">Retirer</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                              label="Montant"
                              type="number"
                              value={aiCreditsAmount}
                              onChange={(e) => setAiCreditsAmount(parseInt(e.target.value) || 0)}
                              fullWidth
                              InputProps={{
                                inputProps: { min: 0 },
                                startAdornment: <Zap size={16} style={{ marginRight: '8px', opacity: 0.7 }} />
                              }}
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: '8px',
                                  bgcolor: 'rgba(155, 89, 182, 0.05)'
                                }
                              }}
                            />
                          </Grid>
                          <Grid size={{ xs: 12 }}>
                            <TextField
                              label="Description (optionnelle)"
                              value={aiCreditsDescription}
                              onChange={(e) => setAiCreditsDescription(e.target.value)}
                              fullWidth
                              margin="normal"
                              placeholder="Raison de l'ajout ou du retrait des crédits AI"
                              sx={{
                                mb: 2,
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: '8px',
                                }
                              }}
                            />
                            <StyledButton
                              variant="contained"
                              onClick={handleAiCreditsManagement}
                              startIcon={aiCreditsAction === 'add' ? <Plus size={16} /> : <Minus size={16} />}
                              fullWidth
                              disabled={aiCreditsAmount <= 0}
                              sx={{
                                bgcolor: aiCreditsAction === 'add' ? '#9b59b6' : '#e74c3c',
                                '&:hover': {
                                  bgcolor: aiCreditsAction === 'add' ? '#8e44ad' : '#c0392b',
                                }
                              }}
                            >
                              {aiCreditsAction === 'add' ? 'Ajouter des crédits AI' : 'Retirer des crédits AI'}
                            </StyledButton>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>
                
                <Grid size={{ xs: 12, md: 6 }}>
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Gestion de l'abonnement</SectionTitle>
                      <Box sx={{ mt: 2 }}>
                        <Grid container spacing={2}>
                          <Grid size={{ xs: 12 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Action</InputLabel>
                              <Select
                                value={subscriptionAction}
                                onChange={(e) => setSubscriptionAction(e.target.value as 'create' | 'change' | 'extend')}
                                label="Action"
                              >
                                <MenuItem value="create">Créer un abonnement</MenuItem>
                                <MenuItem value="change">Changer d'abonnement</MenuItem>
                                <MenuItem value="extend">Prolonger l'abonnement</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Type d'abonnement</InputLabel>
                              <Select
                                value={subscriptionType}
                                onChange={(e) => setSubscriptionType(e.target.value)}
                                label="Type d'abonnement"
                                disabled={subscriptionAction === 'extend'}
                              >
                                <MenuItem value="Basic">Basic</MenuItem>
                                <MenuItem value="Premium">Premium</MenuItem>
                                <MenuItem value="Business">Business</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12, sm: 6 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Durée (mois)</InputLabel>
                              <Select
                                value={subscriptionDuration}
                                onChange={(e) => setSubscriptionDuration(Number(e.target.value))}
                                label="Durée (mois)"
                              >
                                <MenuItem value={1}>1 mois</MenuItem>
                                <MenuItem value={3}>3 mois</MenuItem>
                                <MenuItem value={6}>6 mois</MenuItem>
                                <MenuItem value={12}>12 mois</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12 }}>
                            <StyledButton
                              variant="contained"
                              onClick={handleSubscriptionManagement}
                              startIcon={<Save size={16} />}
                              fullWidth
                              disabled={true} // TODO: Implementer la gestion des abonnements
                            >
                              {subscriptionAction === 'create' ? 'Créer l\'abonnement' : 
                               subscriptionAction === 'change' ? 'Changer l\'abonnement' : 
                               'Prolonger l\'abonnement'}
                            </StyledButton>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>
                
                <Grid size={{ xs: 12, md: 6 }}>
                  <StyledCard>
                    <CardContent>
                      <SectionTitle>Actions sur l'utilisateur</SectionTitle>
                      <Box sx={{ mt: 2 }}>
                        <Grid container spacing={2}>
                          <Grid size={{ xs: 12 }}>
                            <StyledFormControl fullWidth>
                              <InputLabel>Action</InputLabel>
                              <Select
                                value={userActionType}
                                onChange={(e) => setUserActionType(e.target.value as 'suspend' | 'activate' | 'delete')}
                                label="Action"
                              >
                                <MenuItem value="suspend">Suspendre l'utilisateur</MenuItem>
                                <MenuItem value="activate">Activer l'utilisateur</MenuItem>
                                <MenuItem value="delete">Supprimer l'utilisateur</MenuItem>
                              </Select>
                            </StyledFormControl>
                          </Grid>
                          <Grid size={{ xs: 12 }}>
                            <TextField
                              label="Raison"
                              multiline
                              rows={3}
                              value={userActionReason}
                              onChange={(e) => setUserActionReason(e.target.value)}
                              fullWidth
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: '8px',
                                }
                              }}
                            />
                          </Grid>
                          <Grid size={{ xs: 12 }}>
                            <StyledButton
                              variant="contained"
                              onClick={handleUserAction}
                              startIcon={userActionType === 'suspend' ? <AlertTriangle size={16} /> : 
                                        userActionType === 'activate' ? <CheckCircle size={16} /> : 
                                        <XCircle size={16} />}
                              fullWidth
                              color={userActionType === 'delete' ? 'error' : 'primary'}
                              sx={{
                                backgroundColor: userActionType === 'delete' ? COLORS.error : 
                                                userActionType === 'suspend' ? COLORS.warning : 
                                                COLORS.success,
                                '&:hover': {
                                  backgroundColor: userActionType === 'delete' ? '#d32f2f' : 
                                                 userActionType === 'suspend' ? '#f57c00' : 
                                                 '#388e3c',
                                }
                              }}
                            >
                              {userActionType === 'suspend' ? 'Suspendre l\'utilisateur' : 
                               userActionType === 'activate' ? 'Activer l\'utilisateur' : 
                               'Supprimer l\'utilisateur'}
                            </StyledButton>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>
              </Grid>
            )}
          </StyledDialogContent>
          
          <StyledDialogActions>
            <StyledButton onClick={onClose} variant="outlined">
              Fermer
            </StyledButton>
            <StyledButton 
              onClick={() => {
                fetchUserDetails();
                onUserUpdate();
              }} 
              variant="outlined"
              startIcon={<RefreshCw size={16} />}
            >
              Actualiser
            </StyledButton>
          </StyledDialogActions>
        </>
      ) : (
        <Box></Box>
      )}
      
      {/* Modales de confirmation */}
      <ConfirmationModal
        open={jobiConfirmModalOpen}
        onClose={() => setJobiConfirmModalOpen(false)}
        onConfirm={executeJobiManagement}
        title={`Confirmation ${jobiAction === 'add' ? 'd\'ajout' : 'de retrait'} de Jobi`}
        message={`Êtes-vous sûr de vouloir ${jobiAction === 'add' ? 'ajouter' : 'retirer'} ${jobiAmount} Jobi `}
        confirmLabel={jobiAction === 'add' ? 'Ajouter' : 'Retirer'}
        cancelLabel="Annuler"
      />
      
      <ConfirmationModal
        open={aiCreditsConfirmModalOpen}
        onClose={() => setAiCreditsConfirmModalOpen(false)}
        onConfirm={executeAiCreditsManagement}
        title={`Confirmation ${aiCreditsAction === 'add' ? 'd\'ajout' : 'de retrait'} de crédits AI`}
        message={`Êtes-vous sûr de vouloir ${aiCreditsAction === 'add' ? 'ajouter' : 'retirer'} ${aiCreditsAmount} crédits AI`}
        confirmLabel={aiCreditsAction === 'add' ? 'Ajouter' : 'Retirer'}
        cancelLabel="Annuler"
      />
    </StyledDialog>
  );
};

export default UserDetailModal;
