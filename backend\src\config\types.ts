// Types pour la configuration de l'application
export interface SmtpConfig {
  host: string | undefined;
  port: number;
  secure: boolean;
  auth: {
    user: string | undefined;
    pass: string | undefined;
  };
  from: string | undefined;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
  refreshSecret: string;
  refreshExpiresIn: string;
  socketExpiresIn?: string; // Ajout de socketExpiresIn à JwtConfig
}

export interface PasswordConfig {
  salt: string;
  expiryDays: number;
  warningDays: number;
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  loginWindowMs: number;
  loginMax: number;
  adminWindowMs: number;
  adminMax: number;
}

export interface SecurityConfig {
  maxFailedAttempts: number;
  maxIPsPerUser: number;
  maxUsersPerIP: number;
  suspiciousCountries: string[];
  locationChangeThreshold: number;
  logRetentionDays: number;
  alertRetentionDays: number;
}

export interface CorsConfig {
  origin: string[];
  credentials: boolean;
  methods: string[];
  allowedHeaders: string[];
  exposedHeaders: string[];
  preflightContinue: boolean;
  optionsSuccessStatus: number;
}

export interface SessionConfig {
  secret: string;
}

export interface AppConfig {
  env: string;
  isProduction: boolean;
  isDevelopment: boolean;
  port: number;
  frontendUrl: string;
  apiUrl: string;

  // Services externes
  supabase: {
    url: string | undefined;
    serviceRoleKey: string | undefined;
  };

  // Configuration des services
  smtp: SmtpConfig;
  jwt: JwtConfig;
  password: PasswordConfig;
  rateLimit: RateLimitConfig;
  security: SecurityConfig;
  redis: {
    host: string;
    port: number;
    password?: string;
    tls?: boolean;
  };
  session: SessionConfig;
}
