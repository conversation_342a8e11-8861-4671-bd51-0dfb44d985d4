import { useState, useEffect, useCallback } from 'react';
import supportTicketService, { TicketStats } from '../services/supportTicketService';
import { logger } from '../utils/logger';
import { useAuth } from '../contexts/AuthContext';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';

// Interface des données reçues du backend
interface BackendTicketStats {
  total: number;
  by_status: Record<string, number>;
  by_priority: Record<string, number>;
  by_category: Record<string, number>;
  by_date: Record<string, number>;
  open_tickets: number;
  resolved_tickets: number;
  average_resolution_time: number;
  average_first_response_time: number;
  average_response_time: number;
  average_satisfaction: number;
  sla_compliance_rate: number;
  tickets_by_agent: Record<string, number>;
  resolution_by_category: Record<string, number>;
  peak_hours: Record<string, number>;
  tags_distribution: Record<string, number>;
}

export interface TicketStatsWithHistory extends TicketStats {
  averageResponseTime: number;
  averageFirstResponseTime: number;
  averageSatisfaction: number;
  slaComplianceRate: number;
  ticketsOverTime: Array<{ date: string; count: number }>;
  ticketsByAgent: Array<{ agent: string; count: number }>;
  resolutionByCategory: Array<{ category: string; time: number }>;
  peakHours: Array<{ hour: string; count: number }>;
  tagsDistribution: Array<{ tag: string; count: number }>;
  weeklyTrends: Array<{ week: string; count: number }>;
  monthlyComparison: Array<{ month: string; thisYear: number; lastYear: number }>;
}

interface UseTicketStatsResult {
  stats: TicketStatsWithHistory;
  loading: boolean;
  error: Error | null;
  refreshStats: () => Promise<void>;
  timeRange: string;
  setTimeRange: (range: string) => void;
  startDate: Date | null;
  endDate: Date | null;
  setStartDate: (date: Date | null) => void;
  setEndDate: (date: Date | null) => void;
  useCustomDateRange: boolean;
  setUseCustomDateRange: (use: boolean) => void;
}

// État initial des statistiques
const initialStats: TicketStatsWithHistory = {
  totalTickets: 0,
  openTickets: 0,
  resolvedTickets: 0,
  averageResponseTime: 0,
  averageFirstResponseTime: 0,
  averageSatisfaction: 0,
  slaComplianceRate: 0,
  ticketsByStatus: [],
  ticketsByPriority: [],
  ticketsByCategory: [],
  ticketsOverTime: [],
  ticketsByAgent: [],
  resolutionByCategory: [],
  peakHours: [],
  tagsDistribution: [],
  weeklyTrends: [],
  monthlyComparison: []
};

/**
 * Hook pour récupérer et gérer les statistiques des tickets de support
 * @returns Les statistiques des tickets, l'état de chargement, les erreurs et la fonction pour rafraîchir les données
 */
export const useTicketStats = (): UseTicketStatsResult => {
  const { user } = useAuth();
  const [stats, setStats] = useState<TicketStatsWithHistory>(initialStats);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [timeRange, setTimeRange] = useState<string>('7');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [useCustomDateRange, setUseCustomDateRange] = useState<boolean>(false);

  /**
   * Récupère les statistiques des tickets depuis l'API
   */
  const fetchStats = useCallback(async () => {
    try {
      // Vérifier si l'utilisateur a les droits d'accès
      if (!user || (user.role !== 'jobpadm' && user.role !== 'jobmodo')) {
        setError(new Error("Vous n'avez pas les droits nécessaires pour accéder à ces statistiques"));
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      let response;
      
      // Utilisation du sélecteur de date personnalisé ou de la période prédéfinie
      if (useCustomDateRange && startDate && endDate) {
        // Format ISO pour les dates
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        
        logger.info(`Récupération des statistiques avec dates personnalisées: ${formattedStartDate} - ${formattedEndDate}`);
        
        // Récupérer les statistiques avec dates personnalisées
        response = await supportTicketService.getTicketStatsCustom(formattedStartDate, formattedEndDate);
      } else {
        logger.info(`Récupération des statistiques pour les ${timeRange} derniers jours`);
        
        // Récupérer les statistiques via notre service API avec la période sélectionnée
        response = await supportTicketService.getTicketStats(timeRange);
      }
      
      const backendStats = response as unknown as BackendTicketStats;
      
      // Logs de débogage
      logger.info('Données reçues du backend:', backendStats);
      
      // Log spécifique pour le problème d'évolution des tickets
      logger.info("Données d'évolution par date brutes:", backendStats.by_date);
      
      // Convertir byDate en un tableau trié pour le débogage
      const sortedDates = Object.entries(backendStats.by_date || {})
        .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
        .map(([date, count]) => ({ date, count }));
      
      logger.info('Évolution par date (triée chronologiquement):', sortedDates);

      // Convertir les données du backend au format attendu par le frontend
      const formattedStats: TicketStatsWithHistory = {
        totalTickets: backendStats.total || 0,
        openTickets: backendStats.open_tickets || 0,
        resolvedTickets: backendStats.resolved_tickets || 0,
        averageResponseTime: backendStats.average_response_time || 0,
        averageFirstResponseTime: backendStats.average_first_response_time || 0,
        averageSatisfaction: backendStats.average_satisfaction || 0,
        slaComplianceRate: backendStats.sla_compliance_rate || 0,
        
        // Transformer les objets en tableaux pour les graphiques avec logs
        ticketsByStatus: Object.entries(backendStats.by_status || {}).map(([status, count]) => {
          logger.info('Status mapping:', { status, count });
          return { 
            status: status === 'en_attente' ? 'En attente' : 
                    status === 'nouveau' ? 'Nouveau' :
                    status === 'en_cours' ? 'En cours' :
                    status === 'resolu' ? 'Résolu' : status,
            count: count as number 
          };
        }),
        
        ticketsByPriority: Object.entries(backendStats.by_priority || {}).map(([priority, count]) => {
          logger.info('Priority mapping:', { priority, count });
          return { 
            priority: priority === 'basse' ? 'Basse' :
                     priority === 'normale' ? 'Normale' :
                     priority === 'haute' ? 'Haute' :
                     priority === 'urgente' ? 'Urgente' : priority,
            count: count as number 
          };
        }),
        
        ticketsByCategory: Object.entries(backendStats.by_category || {}).map(([category, count]) => {
          logger.info('Category mapping:', { category, count });
          return { 
            category: category === 'technique' ? 'Technique' :
                     category === 'facturation' ? 'Facturation' :
                     category === 'compte' ? 'Compte' :
                     category === 'mission' ? 'Mission' :
                     category === 'autre' ? 'Autre' : category,
            count: count as number 
          };
        }),
        
        // Évolution temporelle des tickets avec log
        ticketsOverTime: Object.entries(backendStats.by_date || {})
          .map(([date, count]) => {
            const formattedDate = format(parseISO(date), 'dd/MM', { locale: fr });
            logger.info('Date mapping:', { original: date, formatted: formattedDate, count });
            return {
              date: formattedDate,
              count: count as number,
              originalDate: date // Conserver la date originale pour le tri
            };
          })
          .sort((a, b) => {
            // Trier par date originale (format ISO) pour un tri chronologique fiable
            return new Date(a.originalDate).getTime() - new Date(b.originalDate).getTime();
          })
          .map(({ date, count }) => ({ date, count })), // Retirer originalDate après le tri

        // Distribution par agent avec log
        ticketsByAgent: Object.entries(backendStats.tickets_by_agent || {})
          .map(([agent, count]) => {
            logger.info('Agent mapping:', { agent, count });
            return { 
              agent: agent === 'Non assigné' ? 'Non assigné' : agent,
              count: count as number 
            };
          })
          .sort((a, b) => b.count - a.count),

        // Temps de résolution par catégorie
        resolutionByCategory: Object.entries(backendStats.resolution_by_category || {})
          .map(([category, time]) => ({
            category: category === 'technique' ? 'Technique' :
                     category === 'facturation' ? 'Facturation' :
                     category === 'compte' ? 'Compte' :
                     category === 'mission' ? 'Mission' :
                     category === 'autre' ? 'Autre' : category,
            time: time as number
          })),

        // Heures de pointe
        peakHours: Object.entries(backendStats.peak_hours || {})
          .map(([hour, count]) => ({
            hour: `${hour}h`,
            count: count as number
          }))
          .sort((a, b) => Number(a.hour.replace('h', '')) - Number(b.hour.replace('h', ''))),

        // Distribution des tags
        tagsDistribution: Object.entries(backendStats.tags_distribution || {})
          .map(([tag, count]) => ({
            tag,
            count: count as number
          }))
          .sort((a, b) => b.count - a.count),

        weeklyTrends: [],
        monthlyComparison: []
      };

      logger.info('Statistiques formatées:', formattedStats);
      setStats(formattedStats);
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques:', error);
      setError(error instanceof Error ? error : new Error('Une erreur est survenue'));
    } finally {
      setLoading(false);
    }
  }, [user, timeRange, startDate, endDate, useCustomDateRange]);

  // Charger les statistiques au montage du composant et lorsque la période ou les dates personnalisées changent
  useEffect(() => {
    fetchStats();
  }, [fetchStats, timeRange, startDate, endDate, useCustomDateRange]);

  return {
    stats,
    loading,
    error,
    refreshStats: fetchStats,
    timeRange,
    setTimeRange,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    useCustomDateRange,
    setUseCustomDateRange
  };
};

export default useTicketStats; 