import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Tooltip, useTheme } from '@mui/material';
import { differenceInMilliseconds, addHours } from 'date-fns';

interface SLACountdownProps {
  createdAt: string;
  priority: string;
  status: string;
}

const prioritySLA: Record<string, number> = {
  urgente: 2, // 2 heures
  elevee: 4, // 4 heures
  normale: 24, // 24 heures
  faible: 48, // 48 heures
};

const SLACountdown: React.FC<SLACountdownProps> = ({ createdAt, priority, status }) => {
  const theme = useTheme();
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [progress, setProgress] = useState<number>(100);

  useEffect(() => {
    if (status === 'resolu' || status === 'ferme') {
      setTimeLeft(0);
      setProgress(100);
      return;
    }

    const slaHours = prioritySLA[priority] || 24;
    const deadline = addHours(new Date(createdAt), slaHours);
    const totalTime = slaHours * 60 * 60 * 1000; // Convertir les heures en millisecondes

    const updateCountdown = () => {
      const now = new Date();
      const diff = differenceInMilliseconds(deadline, now);
      
      if (diff <= 0) {
        setTimeLeft(0);
        setProgress(0);
        return;
      }

      setTimeLeft(diff);
      setProgress((diff / totalTime) * 100);
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [createdAt, priority, status]);

  const formatTimeLeft = (ms: number): string => {
    if (ms <= 0) return 'Expiré';

    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const getColor = () => {
    if (progress > 50) return theme.palette.success.main;
    if (progress > 25) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  if (status === 'resolu' || status === 'ferme') {
    return (
      <Typography variant="body2" color="success.main">
        Résolu
      </Typography>
    );
  }

  return (
    <Tooltip
      title={`Temps restant avant l'expiration du SLA (${prioritySLA[priority]}h)`}
      arrow
    >
      <Box
        sx={{
          position: 'relative',
          display: 'inline-flex',
          alignItems: 'center',
        }}
      >
        <CircularProgress
          variant="determinate"
          value={progress}
          size={32}
          thickness={4}
          sx={{
            color: getColor(),
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            },
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography
            variant="caption"
            component="div"
            color="text.secondary"
            sx={{ fontSize: '0.6rem', fontWeight: 'bold' }}
          >
            {formatTimeLeft(timeLeft)}
          </Typography>
        </Box>
      </Box>
    </Tooltip>
  );
};

export default SLACountdown; 