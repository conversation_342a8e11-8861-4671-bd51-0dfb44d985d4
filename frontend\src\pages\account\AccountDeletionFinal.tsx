import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Paper, 
  Typography, 
  Button, 
  Box, 
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { 
  DeleteForever, 
  CheckCircle, 
  Cancel,
  PersonOff,
  Security,
  Gavel,
  ArrowBack
} from '@mui/icons-material';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import logger from '../../utils/logger';

const AccountDeletionFinal: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [confirmationChecked, setConfirmationChecked] = useState(false);
  
  const token = searchParams.get('token');
  const userId = searchParams.get('userId');

  useEffect(() => {
    if (!token || !userId) {
      notify('Paramètres de suppression invalides', 'error');
      navigate('/');
    }
  }, [token, userId, navigate]);

  const handleFinalDeletion = async () => {
    if (!confirmationChecked) {
      notify('Veuillez confirmer que vous comprenez les conséquences', 'warning');
      return;
    }

    setLoading(true);
    
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/users/delete/execute`, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          token,
          userId
        })
      });

      const data = await response.json();

      if (data.success) {
        notify(data.message, 'success');
        // Rediriger vers une page de confirmation finale
        setTimeout(() => {
          window.location.href = '/account/deleted';
        }, 3000);
      } else {
        notify(data.message, 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la suppression finale:', error);
      notify('Une erreur est survenue lors de la suppression', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/parametres');
  };

  if (!token || !userId) {
    return null;
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <DeleteForever sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" component="h1" gutterBottom color="error">
            Suppression définitive de votre compte
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Dernière étape avant l'anonymisation de vos données
          </Typography>
        </Box>

        <Alert severity="error" sx={{ mb: 4 }}>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            ⚠️ ATTENTION : Cette action est irréversible et définitive
          </Typography>
        </Alert>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PersonOff sx={{ mr: 1, color: 'error.main' }} />
            Ce qui sera anonymisé immédiatement :
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon><Cancel sx={{ color: 'error.main' }} /></ListItemIcon>
              <ListItemText primary="Nom, prénom et informations personnelles" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Cancel sx={{ color: 'error.main' }} /></ListItemIcon>
              <ListItemText primary="Adresse email (remplacée par une adresse anonyme)" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Cancel sx={{ color: 'error.main' }} /></ListItemIcon>
              <ListItemText primary="Numéro de téléphone et adresse postale" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Cancel sx={{ color: 'error.main' }} /></ListItemIcon>
              <ListItemText primary="Photo de profil et bannière personnalisées" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Cancel sx={{ color: 'error.main' }} /></ListItemIcon>
              <ListItemText primary="Accès à votre compte (déconnexion définitive)" />
            </ListItem>
          </List>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Gavel sx={{ mr: 1, color: 'warning.main' }} />
            Ce qui sera conservé de manière anonyme (obligations légales) :
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon><CheckCircle sx={{ color: 'success.main' }} /></ListItemIcon>
              <ListItemText 
                primary="Historique des transactions financières" 
                secondary="Requis pour la comptabilité et les obligations fiscales (7 ans)"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle sx={{ color: 'success.main' }} /></ListItemIcon>
              <ListItemText 
                primary="Avis et évaluations (anonymisés)" 
                secondary="Nécessaires pour maintenir la confiance de la communauté"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle sx={{ color: 'success.main' }} /></ListItemIcon>
              <ListItemText 
                primary="Données statistiques agrégées" 
                secondary="Utilisées pour améliorer nos services (sans identification possible)"
              />
            </ListItem>
          </List>
        </Box>

        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Security sx={{ mr: 1, color: 'info.main' }} />
            Vos droits RGPD :
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Conformément au Règlement Général sur la Protection des Données (RGPD), cette anonymisation 
              respecte votre droit à l'effacement tout en permettant à JobPartiel de respecter ses 
              obligations légales de conservation de certaines données.
            </Typography>
          </Alert>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={confirmationChecked}
                onChange={(e) => setConfirmationChecked(e.target.checked)}
                color="error"
              />
            }
            label={
              <Typography variant="body2">
                Je comprends que cette action est irréversible et que mes données personnelles 
                seront définitivement anonymisées. J'accepte que certaines données soient conservées 
                de manière anonyme pour respecter les obligations légales de JobPartiel.
              </Typography>
            }
          />
        </Box>

        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
          <Button
            variant="outlined"
            onClick={handleCancel}
            startIcon={<ArrowBack />}
            fullWidth
            sx={{ order: { xs: 2, sm: 1 } }}
          >
            Annuler la suppression
          </Button>
          
          <Button
            onClick={handleFinalDeletion}
            variant="contained"
            color="error"
            disabled={loading || !confirmationChecked}
            fullWidth
            sx={{ order: { xs: 1, sm: 2 } }}
            startIcon={loading ? <CircularProgress size={20} /> : <DeleteForever />}
          >
            {loading ? 'Suppression en cours...' : 'Supprimer définitivement mon compte'}
          </Button>
        </Box>

        <Alert severity="warning" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Dernière chance :</strong> Une fois cette action confirmée, il sera impossible 
            de récupérer votre compte ou vos données personnelles.
          </Typography>
        </Alert>
      </Paper>
    </Container>
  );
};

export default AccountDeletionFinal;
