import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, HelpCircle, Clock, Users, CheckCircle, XIcon, UserCheck, Building2, FileCheck } from 'lucide-react';
import { InformationsEntreprise } from '../../pages/dashboard/components/InformationsEntreprise';

interface StatsAndProfessionalInfoSectionProps {
  isOwnProfil: boolean;
  profil: any;
  profileStats: any;
  date_inscription: string;
  formatDate: (dateString: string) => string;
  isDocumentExpiringSoon: (dateValidation?: string) => boolean;
  setVerificationInitialType: (type: string | undefined) => void;
  setIsVerificationModalOpen: (open: boolean) => void;
  getCommonHeaders: () => Promise<any>;
  axios: any;
  setProfil: (cb: (prev: any) => any) => void;
  notify: (msg: string, type?: string) => void;
  logger: any;
  apiBaseUrl: string;
  invalidateProfilCompletionCache: () => void;
  recalculateProfilCompletion: (profil: any) => void;
}

const StatsAndProfessionalInfoSection: React.FC<StatsAndProfessionalInfoSectionProps> = ({
  isOwnProfil,
  profil,
  profileStats,
  date_inscription,
  formatDate,
  isDocumentExpiringSoon,
  setVerificationInitialType,
  setIsVerificationModalOpen,
  getCommonHeaders,
  axios,
  setProfil,
  notify,
  logger,
  apiBaseUrl,
  invalidateProfilCompletionCache,
  recalculateProfilCompletion
}) => {
  return (
    <motion.section
      className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-briefcase h-6 w-6 text-[#FF6B2C]"><path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path><rect width="20" height="14" x="2" y="6" rx="2"></rect></svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-800">Informations du profil</h2>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Statistiques */}
        <div className="w-full">
          {!isOwnProfil && (
            <div className="bg-[#FFF8F3] rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Statistiques</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-[#FF6B2C]" />
                    <span className="text-sm text-gray-600">Membre depuis</span>
                    <div className="relative group">
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-[#FF6B2C] cursor-help" />
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 w-48 bg-white rounded-lg shadow-lg text-xs text-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10">
                        Date de création du compte sur JobPartiel.
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-medium">
                    {profileStats && profileStats.registration_date ? formatDate(profileStats.registration_date) : (date_inscription ? formatDate(date_inscription) : 'N/A')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-[#FF6B2C]" />
                    <span className="text-sm text-gray-600">Délai de réponse moyen</span>
                    <div className="relative group">
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-[#FF6B2C] cursor-help" />
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 w-48 bg-white rounded-lg shadow-lg text-xs text-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10">
                        Temps moyen entre la réception d'une demande de mission et votre première réponse.
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-medium">
                    {profileStats && profileStats.avg_response_time > 0 ?
                      (profileStats.avg_response_time >= 3600 ?
                        `${Math.round(profileStats.avg_response_time / 3600)} heures` :
                        `${Math.round(profileStats.avg_response_time / 60)} minutes`)
                      : '—'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Users className="h-5 w-5 text-[#FF6B2C]" />
                    <span className="text-sm text-gray-600">Mises en relation</span>
                    <div className="relative group">
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-[#FF6B2C] cursor-help" />
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 w-48 bg-white rounded-lg shadow-lg text-xs text-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-10">
                        Nombre de clients distincts avec qui vous avez collaboré ou échangé sur des missions.
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-medium">
                    {profileStats ? profileStats.connections_count : '—'}
                  </span>
                </div>
              </div>
            </div>
          )}
          {/* Statut des vérifications */}
          <div className="bg-[#FFF8F3] rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Vérifications</h3>
            <div className="space-y-4">
              {/* Identité - toujours visible */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <UserCheck className="h-5 w-5 text-[#FF6B2C]" />
                  <span className="text-sm text-gray-600">Identité</span>
                </div>
                {/* Bloc vérification identité */}
                <div className="flex flex-col items-end">
                  {profil?.identite_verifier ? (
                    <div className="flex items-center text-green-500">
                      <CheckCircle className="h-5 w-5 mr-1" />
                      <span className="text-sm">Vérifié</span>
                    </div>
                  ) : (
                    !isOwnProfil && (
                      <div className="flex items-center text-red-500">
                        <XIcon className="h-5 w-5 mr-1" />
                        <span className="text-sm">Non vérifié</span>
                      </div>
                    )
                  )}
                  {profil?.companyInfo?.date_validation_document_identite && (
                    <span className="text-xs text-gray-500 mt-1">
                      Expire le : {new Date(new Date(profil.companyInfo.date_validation_document_identite).setFullYear(new Date(profil.companyInfo.date_validation_document_identite).getFullYear() + 1)).toLocaleDateString()}
                    </span>
                  )}
                  {/* Si la date est absente ou expirée, afficher un bouton de renouvellement */}
                  {(!profil?.companyInfo?.date_validation_document_identite || isDocumentExpiringSoon(profil?.companyInfo?.date_validation_document_identite)) && isOwnProfil && (
                    <button className="text-[#FF6B2C] text-sm font-medium" onClick={() => { setVerificationInitialType('identity'); setIsVerificationModalOpen(true); }}>
                      {profil?.identite_verifier ? 'Renouveler' : 'Vérifier maintenant'}
                    </button>
                  )}
                </div>
              </div>
              {/* Entreprise et Assurance - uniquement pour les profils professionnels */}
              {profil?.companyInfo?.type_de_profil === 'entreprise' && (
                <>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Building2 className="h-5 w-5 text-[#FF6B2C]" />
                      <span className="text-sm text-gray-600">Entreprise</span>
                    </div>
                    <div className="flex flex-col items-end">
                      {profil?.entreprise_verifier ? (
                        <div className="flex items-center text-green-500">
                          <CheckCircle className="h-5 w-5 mr-1" />
                          <span className="text-sm">Vérifié</span>
                        </div>
                      ) : (
                        !isOwnProfil && (
                          <div className="flex items-center text-red-500">
                            <XIcon className="h-5 w-5 mr-1" />
                            <span className="text-sm">Non vérifié</span>
                          </div>
                        )
                      )}
                      {profil?.companyInfo?.date_validation_document_entreprise && (
                        <span className="text-xs text-gray-500 mt-1">
                          Expire le : {new Date(new Date(profil.companyInfo.date_validation_document_entreprise).setFullYear(new Date(profil.companyInfo.date_validation_document_entreprise).getFullYear() + 1)).toLocaleDateString()}
                        </span>
                      )}
                      {(!profil?.companyInfo?.date_validation_document_entreprise || isDocumentExpiringSoon(profil?.companyInfo?.date_validation_document_entreprise)) && isOwnProfil && (
                        <button className="text-[#FF6B2C] text-sm font-medium" onClick={() => { setVerificationInitialType('kbis'); setIsVerificationModalOpen(true); }}>
                          {profil?.entreprise_verifier ? 'Renouveler' : 'Vérifier maintenant'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FileCheck className="h-5 w-5 text-[#FF6B2C]" />
                      <span className="text-sm text-gray-600">Assurance</span>
                    </div>
                    <div className="flex flex-col items-end">
                      {profil?.assurance_verifier ? (
                        <div className="flex items-center text-green-500">
                          <CheckCircle className="h-5 w-5 mr-1" />
                          <span className="text-sm">Vérifié</span>
                        </div>
                      ) : (
                        !isOwnProfil && (
                          <div className="flex items-center text-red-500">
                            <XIcon className="h-5 w-5 mr-1" />
                            <span className="text-sm">Non vérifié</span>
                          </div>
                        )
                      )}
                      {profil?.companyInfo?.date_validation_document_assurance && (
                        <span className="text-xs text-gray-500 mt-1">
                          Expire le : {new Date(new Date(profil.companyInfo.date_validation_document_assurance).setFullYear(new Date(profil.companyInfo.date_validation_document_assurance).getFullYear() + 1)).toLocaleDateString()}
                        </span>
                      )}
                      {(!profil?.companyInfo?.date_validation_document_assurance || isDocumentExpiringSoon(profil?.companyInfo?.date_validation_document_assurance)) && isOwnProfil && (
                        <button className="text-[#FF6B2C] text-sm font-medium" onClick={() => { setVerificationInitialType('assurance'); setIsVerificationModalOpen(true); }}>
                          {profil?.assurance_verifier ? 'Renouveler' : 'Vérifier maintenant'}
                        </button>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        {/* Informations entreprise // Profil */}
        {profil?.companyInfo && (
          <InformationsEntreprise
            initialData={{
              type_de_profil: profil.companyInfo.type_de_profil,
              nom_entreprise: profil.companyInfo.nom_entreprise,
              prenom_entreprise: profil.companyInfo.prenom_entreprise,
              statut_entreprise: profil.companyInfo.statut_entreprise,
              siren_entreprise: profil.companyInfo.siren_entreprise,
              code_ape_entreprise: profil.companyInfo.code_ape_entreprise,
              categorie_entreprise: profil.companyInfo.categorie_entreprise,
              effectif_entreprise: profil.companyInfo.effectif_entreprise,
              date_insee_creation_entreprise: profil.companyInfo.date_insee_creation_entreprise,
              date_categorie_entreprise: profil.companyInfo.date_categorie_entreprise,
              date_derniere_mise_a_jour_entreprise_insee: profil.companyInfo.date_derniere_mise_a_jour_entreprise_insee,
              date_derniere_mise_a_jour_du_client_entreprise: profil.companyInfo.date_derniere_mise_a_jour_du_client_entreprise,
              date_validation_document_identite: profil.companyInfo.date_validation_document_identite || '',
              date_validation_document_entreprise: profil.companyInfo.date_validation_document_entreprise || '',
              date_validation_document_assurance: profil.companyInfo.date_validation_document_assurance || ''
            }}
            isOwnProfil={isOwnProfil}
            onUpdate={async (data: any) => {
              try {
                const headers = await getCommonHeaders();
                const response = await axios.put(
                  `${apiBaseUrl}/api/users/updateProfil`,
                  {
                    companyInfo: {
                      type_de_profil: data.type_de_profil,
                      nom_entreprise: data.nom_entreprise,
                      prenom_entreprise: data.prenom_entreprise,
                      statut_entreprise: data.statut_entreprise,
                      siren_entreprise: data.siren_entreprise,
                      code_ape_entreprise: data.code_ape_entreprise,
                      categorie_entreprise: data.categorie_entreprise,
                      effectif_entreprise: data.effectif_entreprise,
                      date_insee_creation_entreprise: data.date_insee_creation_entreprise,
                      date_categorie_entreprise: data.date_categorie_entreprise,
                      date_derniere_mise_a_jour_entreprise_insee: data.date_derniere_mise_a_jour_entreprise_insee,
                      date_derniere_mise_a_jour_du_client_entreprise: data.date_derniere_mise_a_jour_du_client_entreprise
                    },
                  },
                  {
                    headers,
                    withCredentials: true
                  }
                );

                if (response.data.success) {
                  setProfil(prev => {
                    if (!prev) return null;
                    return {
                      ...prev,
                      companyInfo: {
                        ...prev.companyInfo,
                        ...data
                      }
                    };
                  });
                  invalidateProfilCompletionCache();
                  recalculateProfilCompletion({ ...profil, companyInfo: { ...profil.companyInfo, ...data } });
                  notify('Informations du profil mis à jour avec succès', 'success');
                }
              } catch (error) {
                logger.error('Erreur lors de la mise à jour des informations entreprise:', error);
                notify('Erreur lors de la mise à jour des informations entreprise', 'error');
              }
            }}
          />
        )}
      </div>
    </motion.section>
  );
};

export default StatsAndProfessionalInfoSection; 