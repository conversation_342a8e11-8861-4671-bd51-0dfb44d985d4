import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Switch,
  Button,
  Paper,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import SecurityIcon from '@mui/icons-material/Security';
import InfoIcon from '@mui/icons-material/Info';
import api from '../../services/api';

const TwoFactorSettings: React.FC = () => {
  const [enabled, setEnabled] = useState(false);
  const [verified, setVerified] = useState(false);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dialogError, setDialogError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [openVerifyDialog, setOpenVerifyDialog] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [maskedEmail, setMaskedEmail] = useState('');

  useEffect(() => {
    fetchTwoFactorStatus();
  }, []);

  const fetchTwoFactorStatus = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/auth/two-factor/status');

      if (response.data.success) {
        setEnabled(response.data.enabled);
        setVerified(response.data.verified);

        // Récupérer l'email masqué s'il est disponible
        if (response.data.maskedEmail) {
          setMaskedEmail(response.data.maskedEmail);
        }
      }
    } catch (error: any) {
      setError('Erreur lors de la récupération du statut de l\'authentification à deux facteurs');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async () => {
    try {
      setActionLoading(true);
      setError(null);
      setSuccess(null);

      if (enabled) {
        // Désactiver l'authentification à deux facteurs
        const response = await api.post('/api/auth/two-factor/disable');

        if (response.data.success) {
          setEnabled(false);
          setVerified(false);
          setSuccess('Authentification à deux facteurs désactivée avec succès');
        }
      } else {
        // Activer l'authentification à deux facteurs
        const response = await api.post('/api/auth/two-factor/enable');

        if (response.data.success) {
          setEnabled(true);
          setMaskedEmail(response.data.maskedEmail);
          setOpenVerifyDialog(true);
          setSuccess('Un code de vérification a été envoyé à votre adresse email');
        }
      }
    } catch (error: any) {
      setError(
        error.response?.data?.message ||
        'Erreur lors de la modification des paramètres d\'authentification à deux facteurs'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const handleVerifySubmit = async () => {
    try {
      setActionLoading(true);
      setDialogError(null);

      const response = await api.post('/api/auth/verify-two-factor', { token: verificationCode });

      if (response.data.success) {
        setVerified(true);
        setOpenVerifyDialog(false);
        setSuccess('Authentification à deux facteurs activée et vérifiée avec succès');
      } else {
        setDialogError('Code de vérification invalide');
      }
    } catch (error: any) {
      setDialogError(
        error.response?.data?.message ||
        'Erreur lors de la vérification du code'
      );
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress sx={{ color: '#FF7A35' }} />
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2, border: '1px solid #e0e0e0' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <SecurityIcon sx={{ mr: 1, color: '#FF7A35' }} />
        <Typography variant="h6">Authentification à deux facteurs</Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 2,
        backgroundColor: enabled && !verified
          ? 'rgba(255, 152, 0, 0.05)'
          : enabled && verified
            ? 'rgba(255, 122, 53, 0.05)'
            : 'rgba(0, 0, 0, 0.02)',
        borderRadius: 1,
        mb: 2,
        border: enabled && !verified ? '1px dashed #FF9800' : 'none'
      }}>
        <Box>
          <Typography variant="body1" sx={{
            fontWeight: 500,
            color: enabled && !verified ? '#FF9800' : 'inherit'
          }}>
            {enabled && verified
              ? 'Activé et vérifié'
              : enabled && !verified
                ? 'En attente de vérification'
                : 'Désactivé'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {enabled && verified
              ? 'Un code de vérification sera envoyé par email à chaque connexion'
              : enabled && !verified
                ? 'Veuillez vérifier le code envoyé à votre email pour finaliser l\'activation'
                : 'Activez cette option pour renforcer la sécurité de votre compte'}
          </Typography>
          {enabled && !verified && (
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                // Si l'email masqué n'est pas défini, récupérer le statut 2FA
                if (!maskedEmail) {
                  fetchTwoFactorStatus();
                }
                setOpenVerifyDialog(true);
              }}
              sx={{
                mt: 1,
                borderColor: '#FF9800',
                color: '#FF9800',
                '&:hover': {
                  borderColor: '#FF9800',
                  backgroundColor: 'rgba(255, 152, 0, 0.04)',
                },
              }}
            >
              Vérifier maintenant
            </Button>
          )}
        </Box>
        <Switch
          checked={enabled}
          onChange={handleToggle}
          disabled={actionLoading}
          color="primary"
          sx={{
            '& .MuiSwitch-switchBase.Mui-checked': {
              color: enabled && verified ? '#FF7A35' : '#FF9800',
            },
            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
              backgroundColor: enabled && verified ? '#FF7A35' : '#FF9800',
            },
          }}
        />
      </Box>

      <Box sx={{
        p: 2,
        backgroundColor: 'rgba(0, 0, 0, 0.02)',
        borderRadius: 1,
        display: 'flex',
        alignItems: 'flex-start'
      }}>
        <InfoIcon sx={{ mr: 1, color: 'text.secondary', mt: 0.5 }} />
        <Typography variant="body2" color="text.secondary">
          L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.
          Lorsqu'elle est activée, vous devrez saisir un code de vérification envoyé à votre adresse email
          en plus de votre mot de passe lors de la connexion.
        </Typography>
      </Box>

      <Dialog
        open={openVerifyDialog}
        onClose={() => {
          if (!actionLoading) {
            setOpenVerifyDialog(false);
            setDialogError(null);
          }
        }}
        slotProps={{
          paper: {
            sx: {
              borderRadius: '12px',
              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
              overflow: 'visible'
            }
          }
        }}
      >
        <DialogTitle sx={{
          color: '#FF7A35',
          fontWeight: 600,
          borderBottom: dialogError ? '1px solid rgba(255, 0, 0, 0.1)' : 'none'
        }}>
          Vérification du code
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mt: 2, mb: 2 }}>
            Un code de vérification a été envoyé à {maskedEmail || 'votre adresse email'}
          </Typography>

          {dialogError && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                borderRadius: '8px',
                '& .MuiAlert-icon': {
                  color: '#f44336'
                }
              }}
            >
              {dialogError}
            </Alert>
          )}

          <TextField
            label="Code de vérification"
            variant="outlined"
            fullWidth
            value={verificationCode}
            onChange={(e) => {
              // Limiter à 6 caractères
              if (e.target.value.length <= 6) {
                setVerificationCode(e.target.value);
              }
            }}
            margin="normal"
            placeholder="Entrez le code à 6 chiffres"
            autoFocus
            error={!!dialogError}
            helperText={dialogError ? "Veuillez vérifier le code et réessayer" : ""}
          />

          <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Le code est valide pendant 10 minutes. Si vous ne l'avez pas reçu, vérifiez votre dossier de spam.
            </Typography>

            <Button
              variant="text"
              size="small"
              onClick={async () => {
                try {
                  setActionLoading(true);
                  setDialogError(null);

                  // Renvoyer le code de vérification
                  const response = await api.post('/api/auth/two-factor/enable');

                  if (response.data.success) {
                    setMaskedEmail(response.data.maskedEmail);
                    setDialogError(null);
                    // Afficher un message de succès dans la modal
                    setSuccess('Un nouveau code de vérification a été envoyé à votre adresse email');
                  }
                } catch (error: any) {
                  setDialogError(
                    error.response?.data?.message ||
                    "Erreur lors de l'envoi du code de vérification"
                  );
                } finally {
                  setActionLoading(false);
                }
              }}
              disabled={actionLoading}
              sx={{
                alignSelf: 'flex-start',
                color: '#FF7A35',
                '&:hover': {
                  backgroundColor: 'rgba(255, 122, 53, 0.04)',
                },
              }}
            >
              Renvoyer le code
            </Button>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button
            onClick={() => {
              setOpenVerifyDialog(false);
              setDialogError(null);
            }}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleVerifySubmit}
            variant="contained"
            disabled={actionLoading || !verificationCode}
            sx={{
              backgroundColor: '#FF7A35',
              '&:hover': {
                backgroundColor: '#FF6B2C',
              },
              '&.Mui-disabled': {
                backgroundColor: 'rgba(255, 122, 53, 0.3)',
              }
            }}
          >
            {actionLoading ? <CircularProgress size={24} color="inherit" /> : 'Vérifier'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default TwoFactorSettings;
