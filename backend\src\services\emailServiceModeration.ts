import { EmailServiceError } from './emailService';
import { logSecurity } from './logger';
import { LogEventType } from '../types/logger';
import { supabase } from '../config/supabase';
import { queueEmail } from './emailQueueService';
import config from '../config';
import logger from '../utils/logger';
import { decryptUserDataAsync } from '../utils/encryption';

const DEFAULT_AVATAR = `${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  // Vérifier le format de base
  if (!emailRegex.test(email)) {
    return false;
  }
  
  // Exclure les emails anonymisés pour le RGPD
  if (email.includes('@supprime.local')) {
    return false;
  }
  
  return true;
};

export const sendModerationActionEmail = async (email: string, data: {
  contentType: string;
  action: 'mask' | 'delete' | 'validated' | 'rejected' | 'restauré';
  genericComment?: string;
  adminComment: string;
  cguUrl: string;
  missionTitle?: string;
  userComment?: string;
  messageContent?: string;
  conversationId?: string;
  recipientName?: string;
}) => {
  if (!isValidEmail(email)) {
    throw new EmailServiceError(
      `L'adresse email '${email}' n'est pas valide.`,
      'INVALID_EMAIL',
      { email }
    );
  }

  let actionLabel = data.action === 'mask' ? 'modéré' : data.action === 'delete' ? 'supprimé' : data.action === 'validated' ? 'validé' : data.action === 'rejected' ? 'rejeté' : 'restauré';
  let actionText = data.action === 'mask'
    ? `Votre ${data.contentType} a été masqué par l'équipe de modération.`
    : data.action === 'delete'
    ? `Votre ${data.contentType} a été supprimé par l'équipe de modération.`
    : data.action === 'validated'
    ? `Votre ${data.contentType} a été validé par l'équipe de modération.`
    : data.action === 'rejected'
    ? `Votre ${data.contentType} a été rejeté par l'équipe de modération.`
    : `Votre ${data.contentType} a été restauré par l'équipe de modération.`;

  const mailOptions = {
    from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
    to: email,
    subject: `🚨 Modération de votre contenu sur JobPartiel` ,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <h2 style="color: #FF7A35; text-align: center; font-size: 24px; margin-bottom: 20px;">Votre ${data.contentType} a été ${actionLabel}</h2>
          <p style="color: #374151; font-size: 16px; line-height: 1.6; text-align: center;">${actionText}</p>
          ${data.missionTitle ? `<div style='background: #FFF8F3; border-left: 3px solid #FF7A35; padding: 12px; margin: 18px 0;'><b>Mission concernée :</b><br>${data.missionTitle}</div>` : ''}
          ${data.userComment ? `<div style='background: #FFE4BA; border-left: 3px solid #FF7A35; padding: 12px; margin: 18px 0;'><b>Commentaire signalé :</b><br>${data.userComment}</div>` : ''}
          ${data.messageContent ? `<div style='background: #FFE4BA; border-left: 3px solid #FF7A35; padding: 12px; margin: 18px 0;'><b>Message modéré :</b><br>${data.messageContent}</div>` : ''}
          ${data.recipientName ? `<div style='background: #FFF8F3; border-left: 3px solid #FF7A35; padding: 12px; margin: 18px 0;'><b>Conversation avec :</b> ${data.recipientName}</div>` : ''}
          ${data.conversationId ? `<div style='background: #FFF8F3; border-left: 3px solid #FF7A35; padding: 12px; margin: 18px 0;'><b>ID de la conversation :</b> ${data.conversationId}</div>` : ''}
          ${data.genericComment ? `<div style="background: #FFF8F3; color: #FF7A35; padding: 18px 20px; margin: 30px 0 18px 0; border-radius: 8px; text-align: center; font-size: 16px; border-left: 4px solid #FF7A35;">${data.genericComment.replace(/\n/g, '<br>')}</div>` : ''}
          ${data.adminComment ? `
            <div style="background: #EF4444; color: #fff; padding: 22px 24px; margin: 36px 0 24px 0; border-radius: 8px; text-align: center; letter-spacing: 0.5px; box-shadow: 0 2px 8px rgba(0,0,0,0.07);">
              <div style="font-size: 32px; margin-bottom: 8px;">⚠️</div>
              <div style="font-size: 13px; font-weight: bold; text-transform: uppercase; letter-spacing: 1.5px; margin-bottom: 6px;">Message du staff de modération :</div>
              <div style="font-size: 18px; font-weight: normal; color: #fff;">${data.adminComment}</div>
            </div>
          ` : ''}
          <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Rappel des règles et sanctions possibles :</h3>
            <ul style="color: #374151; font-size: 15px; line-height: 1.7;">
              <li>Respectez les autres membres et la charte de la plateforme</li>
              <li>Tout contenu inapproprié, insultant, discriminant ou hors sujet peut être modéré</li>
              <li>En cas de récidive, des sanctions supplémentaires peuvent être appliquées (suspension, suppression du compte...)</li>
            </ul>
            <p>Consultez les <a href="${data.cguUrl}" style="color: #FF7A35; text-decoration: underline;">Conditions Générales d'Utilisation</a> pour plus de détails.</p>
          </div>
          <p style="color: #4B5563; font-size: 14px; text-align: center;">Pour toute question, contactez notre support : <a href="mailto:<EMAIL>" style="color: #FF7A35;"><EMAIL></a></p>
        </div>
      </div>
    `
  };

  await queueEmail(mailOptions.to, mailOptions.subject, mailOptions.html);
  logSecurity.info(LogEventType.EMAIL_SENT, 'Email de modération ajouté à la file d\'attente', { to: email });
};

/**
 * Envoie un email à l'utilisateur pour l'informer du statut de vérification entreprise
 * @param userId string
 * @param isValid boolean
 * @param motif string (optionnel, pour refus)
 */
export const sendEntrepriseVerificationStatus = async function(userId: string, isValid: boolean, motif?: string) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;

  // Détection du type de document à partir du motif (ex: 'Type : Attestation d\'assurance')
  let typeLabel = '';
  if (motif) {
    const typeMatch = motif.match(/Type\s*:\s*([^\n]+)/i);
    if (typeMatch && typeMatch[1]) {
      typeLabel = typeMatch[1].trim();
    }
  }

  const subject = isValid
    ? typeLabel
      ? `🎉 ${typeLabel} validé${typeLabel.endsWith('e') ? 'e' : ''} sur JobPartiel !`
      : '🎉 Félicitations, votre entreprise est vérifiée sur JobPartiel !'
    : typeLabel
      ? `❌ ${typeLabel} refusé${typeLabel.endsWith('e') ? 'e' : ''} sur JobPartiel`
      : '❌ Votre vérification entreprise a été refusée sur JobPartiel';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';

  // Fonction utilitaire pour formater le motif/refus
  function formatMotif(motif?: string) {
    if (!motif) return '<span style="color:#FF7A35;">Non précisé</span>';
    // On coupe sur les \n ou on split sur "Motif :" si plusieurs motifs concaténés
    let lines = motif.split(/\n|Motif :/g).map(l => l.trim()).filter(Boolean);
    // On retire la ligne qui commence par 'Type :'
    lines = lines.filter(l => !l.toLowerCase().startsWith('type :'));
    // Si le motif contient des infos structurées (Nom du fichier, Motif), on les met chacune sur une ligne
    if (lines.length > 1) {
      return lines.map(l => `<div style='margin-bottom:2px;'>${l}</div>`).join('');
    }
    // Sinon, on retourne le motif brut
    return `<span style='color:#FF7A35;'>${lines[0]}</span>`;
  }

  const html = isValid
    ? `
      <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
            <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
              <span style="font-size: 40px; color: white;">🎉</span>
            </div>
            <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">${typeLabel ? `${typeLabel} validé${typeLabel.endsWith('e') ? 'e' : ''}` : 'Votre entreprise est maintenant vérifiée !'}</h2>
            <p style="color: #374151; font-size: 16px; margin: 0;">Bravo ! Votre document a été validé avec succès.</p>
          </div>
          <div style="text-align: center; margin: 35px 0;">
            <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Voir mon profil</a>
          </div>
          <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Et maintenant ?</h3>
            <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
              <li>Votre profil gagne en visibilité et en confiance auprès des clients.</li>
              <li>Vous pouvez accéder à toutes les fonctionnalités réservées aux entreprises vérifiées.</li>
              <li>En cas de besoin, notre équipe support est à votre écoute.</li>
            </ul>
          </div>
          <div style="text-align: center; color: #718096; font-size: 14px;">
            <p style="margin-bottom: 10px;">Cordialement,</p>
            <p style="margin: 0;">L'équipe JobPartiel</p>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
            <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      </div>
    `
    : `
      <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
          </div>
          <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
            <div style="background-color: #EF4444; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
              <span style="font-size: 40px; color: white;">❌</span>
            </div>
            <h2 style="color: #EF4444; font-size: 28px; margin: 0 0 10px 0;">${typeLabel ? `${typeLabel} refusé${typeLabel.endsWith('e') ? 'e' : ''}` : 'Vérification entreprise refusée'}</h2>
            <p style="color: #374151; font-size: 16px; margin: 0;">Malheureusement, votre document n'a pas pu être validé.</p>
            <div style="background: #FFE4BA; color: #FF7A35; padding: 18px 20px; margin: 30px 0 18px 0; border-radius: 8px; text-align: left; font-size: 16px; border-left: 4px solid #FF7A35;">
              <b>Motif du refus :</b><br>
              ${formatMotif(motif)}
            </div>
          </div>
          <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
            <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Que faire maintenant ?</h3>
            <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
              <li>Vérifiez que vos documents sont lisibles, à jour et bien conformes.</li>
              <li>Vous pouvez soumettre à nouveau vos documents depuis votre espace personnel.</li>
              <li>Pour toute question ou aide, contactez notre support : <a href="mailto:<EMAIL>" style="color: #FF7A35;"><EMAIL></a></li>
            </ul>
          </div>
          <div style="text-align: center; margin: 35px 0;">
            <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Accéder à mon espace</a>
          </div>
          <div style="text-align: center; color: #718096; font-size: 14px;">
            <p style="margin-bottom: 10px;">Cordialement,</p>
            <p style="margin: 0;">L'équipe JobPartiel</p>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
            <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      </div>
    `;
  await queueEmail(to, subject, html);
};

/**
 * Envoie un email à l'utilisateur pour l'informer que tous ses documents de vérification entreprise ont été supprimés car trop nombreux
 */
export const sendAllEntrepriseDocsDeletedEmail = async function(userId: string) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Décrypter les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const subject = 'Tous vos documents de vérification entreprise ont été supprimés';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <h2 style="color: #FF7A35; text-align: center; font-size: 24px; margin-bottom: 20px;">Trop de documents envoyés</h2>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; text-align: center;">Vous avez envoyé trop de documents pour la vérification de votre entreprise.<br>Tous vos documents ont été supprimés afin de recommencer proprement.</p>
        <div style="background: #FFE4BA; color: #FF7A35; padding: 18px 20px; margin: 30px 0 18px 0; border-radius: 8px; text-align: center; font-size: 16px; border-left: 4px solid #FF7A35;">
          Merci de ne déposer qu'un ou deux fichiers par catégorie (Kbis, Assurance, Identité, etc.) pour faciliter la vérification.<br>En cas de doute, contactez le support.
        </div>
        <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
          <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Rappel des règles :</h3>
          <ul style="color: #374151; font-size: 15px; line-height: 1.7;">
            <li>Un seul document par type est généralement suffisant</li>
            <li>Les documents doivent être lisibles et à jour</li>
            <li>En cas de problème, contactez le support</li>
          </ul>
        </div>
        <p style="color: #4B5563; font-size: 14px; text-align: center;">Pour toute question, contactez notre support : <a href="mailto:<EMAIL>" style="color: #FF7A35;"><EMAIL></a></p>
      </div>
    </div>
  `;
  await queueEmail(to, subject, html);
};

/**
 * Envoie un email à l'utilisateur pour l'informer que son profil est désormais totalement vérifié (profil_verifier = true)
 * @param userId string
 */
export const sendProfilVerifierEmail = async function(userId: string) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  // Récupérer les dates d'expiration des documents
  const { data: profil, error: profilError } = await supabase
    .from('user_profil')
    .select('date_validation_document_identite, date_validation_document_entreprise, date_validation_document_assurance')
    .eq('user_id', userId)
    .single();
  // Fonction utilitaire pour formater la date d'expiration (1 an après la validation)
  function formatExpiration(dateStr: string | undefined) {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    date.setFullYear(date.getFullYear() + 1);
    return date.toLocaleDateString('fr-FR');
  }
  let expirationHtml = '';
  if (profil) {
    const expIdentite = formatExpiration(profil.date_validation_document_identite);
    const expEntreprise = formatExpiration(profil.date_validation_document_entreprise);
    const expAssurance = formatExpiration(profil.date_validation_document_assurance);
    if (expIdentite || expEntreprise || expAssurance) {
      expirationHtml = `<div style="background: #FFF8F3; color: #FF7A35; padding: 16px; border-radius: 8px; margin: 24px 0 0 0; font-size: 15px; border-left: 4px solid #FF7A35; text-align:left; max-width: 400px; margin-left:auto; margin-right:auto;">
        <b>Dates d'expiration de vos documents :</b><ul style='margin: 8px 0 0 0; padding-left: 18px;'>
        ${expIdentite ? `<li>Identité : <b>${expIdentite}</b></li>` : ''}
        ${expEntreprise ? `<li>Entreprise : <b>${expEntreprise}</b></li>` : ''}
        ${expAssurance ? `<li>Assurance : <b>${expAssurance}</b></li>` : ''}
        </ul>
      </div>`;
    }
  }
  const to = decryptedUser.email;
  const subject = '🎉 Félicitations, votre profil est maintenant vérifié sur JobPartiel !';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';
  const html = `
    <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
          <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
            <span style="font-size: 40px; color: white;">🎉</span>
          </div>
          <h2 style="color: #FF7A35; font-size: 28px; margin: 0 0 10px 0;">Votre profil est maintenant vérifié !</h2>
          <p style="color: #374151; font-size: 16px; margin: 0;">Bravo ! Toutes vos vérifications ont été validées.<br>Votre profil est désormais certifié sur JobPartiel.</p>
          <div style="background: #FFE4BA; color: #FF7A35; padding: 16px; border-radius: 8px; margin: 24px 0 0 0; font-size: 16px; border-left: 4px solid #FF7A35;">
            Votre profil vérifié est valable 1 an.<br>Il faudra le revalider avant la fin de cette période pour conserver la certification.
          </div>
          ${expirationHtml}
        </div>
        <div style="text-align: center; margin: 35px 0;">
          <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Voir mon profil</a>
        </div>
        <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
          <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Et maintenant ?</h3>
          <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
            <li>Votre profil gagne en visibilité et en confiance auprès des clients.</li>
            <li>Vous pouvez accéder à toutes les fonctionnalités réservées aux profils vérifiés.</li>
            <li>En cas de besoin, notre équipe support est à votre écoute.</li>
          </ul>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;
  await queueEmail(to, subject, html);
};

/**
 * Envoie un email à l'utilisateur pour lui demander de remplir ses informations entreprise
 * @param userId string
 */
export const sendEntrepriseRemindInfosEmail = async function(userId: string) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const subject = 'Merci de compléter vos informations entreprise sur JobPartiel';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <h2 style="color: #FF7A35; text-align: center; font-size: 24px; margin-bottom: 20px;">Complétez vos informations entreprise</h2>
        <p style="color: #374151; font-size: 16px; line-height: 1.6; text-align: center;">Pour que votre entreprise puisse être vérifiée, merci de remplir toutes les informations obligatoires dans votre espace personnel.</p>
        <div style="background: #FFE4BA; color: #FF7A35; padding: 18px 20px; margin: 30px 0 18px 0; border-radius: 8px; text-align: center; font-size: 16px; border-left: 4px solid #FF7A35;">
          Sans ces informations, la validation de vos documents ne pourra pas être effectuée.<br>
          <b>Merci de compléter votre profil entreprise dès que possible.</b>
        </div>
        <div style="text-align: center; margin: 35px 0;">
          <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Compléter mes informations</a>
        </div>
        <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
          <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Pourquoi ces informations sont-elles nécessaires ?</h3>
          <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
            <li>La vérification entreprise permet d'accéder à toutes les fonctionnalités de la plateforme.</li>
            <li>Un profil complet inspire confiance aux clients et partenaires.</li>
            <li>En cas de question, notre équipe support est à votre écoute.</li>
          </ul>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;
  await queueEmail(to, subject, html);
  logSecurity.info(LogEventType.EMAIL_SENT, 'Email de rappel infos entreprise ajouté à la file d\'attente', { to });
};

/**
 * Envoie un email de rappel d'expiration de document à l'utilisateur et une notification interne
 * @param userId string
 * @param docType 'identite' | 'entreprise' | 'assurance'
 * @param expirationDate string (format ISO)
 * @param daysLeft number (jours restants avant expiration)
 */
export const sendProfilExpirationWarningEmail = async function(userId: string, docType: 'identite' | 'entreprise' | 'assurance', expirationDate: string, daysLeft: number) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const docLabel = docType === 'identite' ? "votre pièce d'identité" : docType === 'entreprise' ? "votre justificatif d'entreprise" : "votre attestation d'assurance";
  const subject = `⏰ Rappel : ${docLabel} expire bientôt`;
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';
  const expirationStr = new Date(expirationDate).toLocaleDateString('fr-FR');
  const html = `
    <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
          <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
            <span style="font-size: 40px; color: white;">⏰</span>
          </div>
          <h2 style="color: #FF7A35; font-size: 24px; margin: 0 0 10px 0;">Attention : ${docLabel} expire bientôt</h2>
          <p style="color: #374151; font-size: 16px; margin: 0;">Votre document expire dans <b>${daysLeft} jour${daysLeft > 1 ? 's' : ''}</b> (le <b>${expirationStr}</b>).</p>
          <div style="background: #FFE4BA; color: #FF7A35; padding: 16px; border-radius: 8px; margin: 24px 0 0 0; font-size: 16px; border-left: 4px solid #FF7A35;">
            Merci de déposer un nouveau document avant cette date pour conserver votre statut vérifié.
          </div>
        </div>
        <div style="text-align: center; margin: 35px 0;">
          <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Mettre à jour mon document</a>
        </div>
        <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
          <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Pourquoi ce rappel ?</h3>
          <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
            <li>Un document expiré entraîne la perte du statut vérifié.</li>
            <li>Votre profil aura moins de confiance auprès des clients de la plateforme.</li>
            <li>Pour toute question, notre équipe support est à votre écoute.</li>
          </ul>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;
  await queueEmail(to, subject, html);

  // Créer une notification interne
  const notifTitle = `Rappel : ${docLabel} expire bientôt`;
  const notifContent = `Votre document expire dans ${daysLeft} jour${daysLeft > 1 ? 's' : ''} (le ${expirationStr}). Merci de le renouveler pour conserver votre statut vérifié.`;
  await supabase.from('user_notifications').insert({
    user_id: userId,
    type: 'profile',
    title: notifTitle,
    content: notifContent,
    link: '/dashboard/profil',
    is_read: false,
    is_archived: false
  });
};

/**
 * Envoie un email à l'utilisateur pour l'informer que son profil n'est plus vérifié (profil_verifier = false)
 * @param userId string
 */
export const sendProfilInvalidatedEmail = async function(userId: string) {
  // Récupérer l'email de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();
  if (error || !user?.email) return;

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const subject = 'Votre profil n\'est plus vérifié sur JobPartiel';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';
  const html = `
    <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
          <div style="background-color: #FF7A35; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
            <span style="font-size: 40px; color: white;">⚠️</span>
          </div>
          <h2 style="color: #FF7A35; font-size: 24px; margin: 0 0 10px 0;">Votre profil n'est plus vérifié</h2>
          <p style="color: #374151; font-size: 16px; margin: 0;">Votre statut vérifié a expiré ou un de vos documents n'est plus valide.<br>Pour retrouver la certification, merci de renouveler vos documents dans votre espace personnel.</p>
          <div style="background: #FFE4BA; color: #FF7A35; padding: 16px; border-radius: 8px; margin: 24px 0 0 0; font-size: 16px; border-left: 4px solid #FF7A35;">
            Vous pouvez déposer de nouveaux documents à tout moment pour redevenir vérifié.
          </div>
        </div>
        <div style="text-align: center; margin: 35px 0;">
          <a href="${frontendUrl}/dashboard/profil" style="display: inline-block; background-color: #FF7A35; color: white; padding: 14px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(255, 122, 53, 0.2);">Accéder à mon profil</a>
        </div>
        <div style="background: #FFF8F3; padding: 20px; border-radius: 8px; margin: 30px 0; border: 1px solid #FFE4BA;">
          <h3 style="color: #FF7A35; margin-bottom: 10px; font-size: 18px;">Pourquoi ce changement ?</h3>
          <ul style="color: #374151; font-size: 15px; line-height: 1.7; text-align: left; max-width: 90%; margin: 0 auto;">
            <li>Un ou plusieurs de vos documents sont arrivés à expiration ou ont été supprimés.</li>
            <li>Votre profil n'est plus certifié tant que les documents ne sont pas renouvelés.</li>
            <li>Pour toute question, notre équipe support est à votre écoute.</li>
          </ul>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;
  await queueEmail(to, subject, html);
};

/**
 * Envoie un email à l'utilisateur pour l'informer que son profil a été restauré suite au rejet d'un signalement
 * @param userId string
 * @param adminComment string - Commentaire optionnel du modérateur
 */
export const sendProfilRestaureEmail = async function(userId: string, adminComment?: string) {
  // Récupérer l'email et les informations de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();

  if (error || !user?.email) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de la récupération de l\'email de l\'utilisateur pour notification de profil restauré', {
      userId,
      error: error?.message
    });
    return;
  }

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const subject = '✅ Votre profil a été vérifié et restauré sur JobPartiel';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';

  const html = `
    <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
          <div style="background-color: #4CAF50; display: inline-block; border-radius: 50%; width: 80px; height: 80px; line-height: 80px; text-align: center; margin-bottom: 15px;">
            <span style="font-size: 40px; color: white;">✅</span>
          </div>
          <h1 style="color: #4CAF50; font-size: 24px; margin-bottom: 10px;">Votre profil a été vérifié et restauré</h1>
          <p style="color: #4A5568; font-size: 16px; line-height: 1.5;">Suite à l'examen de votre profil par notre équipe de modération, nous avons déterminé que le signalement n'était pas fondé.</p>
        </div>
        <div style="color: #4A5568; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
          <p>Bonjour,</p>
          <p>Nous vous informons que votre profil a été vérifié par notre équipe de modération suite à un signalement.</p>
          <p>Après examen, nous avons constaté que votre profil respecte bien les normes de notre plateforme. <strong>Votre profil est à nouveau visible par tous les utilisateurs.</strong></p>
          ${adminComment ? `
            <div style="background: #FFF8F3; color: #4CAF50; padding: 18px 20px; margin: 30px 0 18px 0; border-radius: 8px; text-align: center; font-size: 16px; border-left: 4px solid #4CAF50;">
              <div style="font-size: 13px; font-weight: bold; text-transform: uppercase; letter-spacing: 1.5px; margin-bottom: 6px;">Message du staff de modération :</div>
              <div style="font-size: 16px; font-weight: normal; color: #4A5568;">${adminComment}</div>
            </div>
          ` : ''}
          <div style="text-align: center; margin: 30px 0;">
            <a href="${frontendUrl}/dashboard/profil" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600;">Voir mon profil</a>
          </div>
          <p>Pour consulter nos conditions d'utilisation et comprendre les normes de notre plateforme, veuillez cliquer sur le lien ci-dessous :</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${frontendUrl}/conditions-generales" style="color: #4CAF50; text-decoration: underline;">Consulter les conditions d'utilisation</a>
          </div>
          <p>Si vous avez des questions ou besoin d'assistance, n'hésitez pas à contacter notre équipe de support.</p>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe de modération JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;

  try {
    await queueEmail(to, subject, html);

    // Créer une notification interne
    await supabase.from('user_notifications').insert({
      user_id: userId,
      type: 'profile',
      title: 'Votre profil a été vérifié et restauré',
      content: 'Suite à l\'examen de votre profil par notre équipe de modération, nous avons déterminé que le signalement n\'était pas fondé. Votre profil est à nouveau visible par tous les utilisateurs.',
      link: '/dashboard/profil',
      is_read: false,
      is_archived: false
    });

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de profil restauré envoyé avec succès', {
      to,
      userId
    });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de profil restauré', {
      userId,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
  }
};

/**
 * Envoie un email à l'utilisateur pour l'informer que son profil a été masqué par la modération
 * @param userId string
 * @param isAutomatic boolean - Indique si le masquage est automatique suite à des signalements
 */
export const sendProfilMasqueEmail = async function(userId: string, isAutomatic: boolean = false) {
  // Récupérer l'email et les informations de l'utilisateur
  const { data: user, error } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();

  if (error || !user?.email) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de la récupération de l\'email de l\'utilisateur pour notification de profil masqué', {
      userId,
      error: error?.message
    });
    return;
  }

  // Déchiffrer les données utilisateur
  const decryptedUser = await decryptUserDataAsync(user);
  const to = decryptedUser.email;
  const subject = '🚨 Votre profil a été masqué sur JobPartiel';
  const frontendUrl = process.env.FRONTEND_URL || 'https://jobpartiel.fr';

  // Texte différent selon que le masquage est automatique ou manuel
  const reasonText = isAutomatic
    ? "Suite à plusieurs signalements d'autres utilisateurs de la plateforme, votre profil a été temporairement masqué."
    : "Suite à une vérification par notre équipe de modération, votre profil a été temporairement masqué.";

  const html = `
    <div style="font-family: Arial, sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
      <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <img src="${DEFAULT_AVATAR}" alt="Job Partiel" style="max-width: 100%; height: auto;">
        </div>
        <div style="text-align: center; background-color: #FFF8F3; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
          <h1 style="color: #FF7A35; font-size: 24px; margin-bottom: 10px;">Votre profil a été masqué</h1>
          <p style="color: #4A5568; font-size: 16px; line-height: 1.5;">${reasonText}</p>
        </div>
        <div style="color: #4A5568; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
          <p>Bonjour,</p>
          <p>Nous vous informons que votre profil a été masqué sur JobPartiel car il ne respecte pas les normes de notre plateforme.</p>
          <p>Votre profil n'est plus visible par les autres utilisateurs jusqu'à ce que vous apportiez les modifications nécessaires.</p>
          <p>Voici les points à vérifier dans votre profil :</p>
          <ul style="padding-left: 20px; margin-bottom: 20px;">
            <li>Vérifiez que votre photo de profil est appropriée et professionnelle</li>
            <li>Assurez-vous que votre biographie ne contient pas de contenu inapproprié</li>
            <li>Vérifiez que vos informations personnelles sont correctes et complètes</li>
            <li>Assurez-vous que vos galeries et photos respectent nos conditions d'utilisation</li>
          </ul>
          <p style="font-weight: bold; color: #FF7A35;">Une fois les modifications effectuées, vous devez ouvrir un ticket support pour demander une revalidation de votre profil.</p>
          <div style="text-align: center; margin: 30px 0;">
            <div style="display: inline-block; margin-bottom: 10px; width: 100%;">
              <a href="${frontendUrl}/dashboard/profil" style="background-color: #FF7A35; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; margin: 5px; min-width: 200px; text-align: center;">Modifier mon profil</a>
            </div>
            <div style="display: inline-block; width: 100%;">
              <a href="${frontendUrl}/dashboard/support/new" style="background-color: #4A5568; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; margin: 5px; min-width: 200px; text-align: center;">Ouvrir un ticket support</a>
            </div>
          </div>
          <p>Pour consulter nos conditions d'utilisation et comprendre les normes de notre plateforme, veuillez cliquer sur le lien ci-dessous :</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${frontendUrl}/conditions-generales" style="color: #FF7A35; text-decoration: underline;">Consulter les conditions d'utilisation</a>
          </div>
          <p>Si vous avez des questions ou besoin d'assistance, n'hésitez pas à contacter notre équipe de support.</p>
        </div>
        <div style="text-align: center; color: #718096; font-size: 14px;">
          <p style="margin-bottom: 10px;">Cordialement,</p>
          <p style="margin: 0;">L'équipe de modération JobPartiel</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E2E8F0; font-size: 12px; color: #A0AEC0; text-align: center;">
          <p>&copy; ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `;

  try {
    await queueEmail(to, subject, html);

    // Créer une notification interne
    await supabase.from('user_notifications').insert({
      user_id: userId,
      type: 'profile',
      title: 'Votre profil a été masqué',
      content: isAutomatic
        ? 'Suite à plusieurs signalements d\'autres utilisateurs, votre profil a été temporairement masqué. Veuillez le mettre à jour et ouvrir un ticket support pour demander une revalidation.'
        : 'Suite à une vérification par notre équipe de modération, votre profil a été temporairement masqué. Veuillez le mettre à jour et ouvrir un ticket support pour demander une revalidation.',
      link: '/dashboard/profil',
      is_read: false,
      is_archived: false
    });

    logSecurity.info(LogEventType.EMAIL_SENT, 'Email de notification de profil masqué envoyé avec succès', {
      to,
      userId,
      isAutomatic
    });
  } catch (error) {
    logSecurity.error(LogEventType.EMAIL_ERROR, 'Erreur lors de l\'envoi de l\'email de notification de profil masqué', {
      userId,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
  }
};