/* ForgotPassword (Mot de passe oublié) :
C'est la première étape du processus de réinitialisation
L'utilisateur saisit son email
Le système envoie un email avec un lien de réinitialisation
Fonction principale : requestPasswordReset(email)
*/

import { useNavigate, Link } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Mail, ArrowRight } from 'lucide-react';
import { createAuthService } from '../../services/auth';
import { logger } from '../../utils/logger';
import ResendVerification from '../../components/ResendVerification';
import { useNotification } from '/src/components/Notification';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import DOMPurify from 'dompurify';

const getCookie = (name: string): string | undefined => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
};

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showResendVerification, setShowResendVerification] = useState(false);
  const [cooldown, setCooldown] = useState(0);
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [countdown, setCountdown] = useState(6);
  const navigate = useNavigate();
  const { notify } = useNotification();
  const authService = createAuthService(notify);

  // Un seul useEffect pour gérer les deux timers
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (success) {
      // Timer de redirection après succès (10 secondes)
      timer = setInterval(() => {
        setCooldown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            navigate('/login');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [success, navigate]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isButtonDisabled) {
      interval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            setIsButtonDisabled(false);
            return 6;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isButtonDisabled]);

  const validateEmail = () => {
    if (!email) {
      setError('Veuillez entrer une adresse email pour continuer.');
      return false;
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('L\'adresse email que vous avez entrée n\'est pas valide.');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (cooldown > 0) {
      notify(`Veuillez attendre ${cooldown} secondes avant de réessayer`, 'warning');
      return;
    }

    if (isButtonDisabled) return;

    setIsButtonDisabled(true);
    setCountdown(6);

    const sanitizedEmail = DOMPurify.sanitize(email);

    if (!validateEmail()) return;

    setIsLoading(true);
    setError('');

    try {
      const result = await authService.requestPasswordReset(sanitizedEmail);
      
      if (result.success) {
        setSuccess(true);
        setCooldown(10); // Définit le countdown à 10 secondes pour la redirection
      }
    } catch (error: any) {
      logger.error('Password reset request error:', error);
      
      if (error.message?.includes('Compte non vérifié')) {
        setShowResendVerification(true);
        notify('Votre compte n\'est pas vérifié.', 'error');
      } else {
        notify('Une erreur est survenue.', 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Mot de Passe Oublié - JobPartiel.fr</title>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="keywords" content="mot de passe oublié, JobPartiel.fr" />
        <meta property="og:title" content="Mot de Passe Oublié - JobPartiel.fr" />
        <meta property="og:description" content="Réinitialisez votre mot de passe JobPartiel.fr en entrant votre adresse email." />
        <meta property="og:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
        <meta property="og:url" content="https://jobpartiel.fr/forgot-password" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="Mot de Passe Oublié - JobPartiel.fr" />
        <meta name="twitter:description" content="Réinitialisez votre mot de passe JobPartiel.fr en entrant votre adresse email." />
        <meta name="twitter:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
        <link rel="canonical" href="https://jobpartiel.fr/forgot-password" />
        <meta name="description" content="Réinitialisez votre mot de passe JobPartiel.fr en entrant votre adresse email." />
        <meta name="robots" content="index, follow" />
      </Helmet>
      <div className="max-w-md w-full space-y-8 p-8 bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl">
        {showResendVerification ? (
          <>
            <div>
              <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
                Vérification requise
              </h2>
            </div>
            <ResendVerification />
            <div className="text-center mt-4">
              <Link 
                to="/login"
                className="inline-flex items-center text-sm font-medium text-[#FF7A35] hover:text-[#FF8F59] transition-colors duration-300"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Retour à la connexion
              </Link>
            </div>
          </>
        ) : success ? (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 backdrop-blur-sm">
            <div className="w-full max-w-md p-6 mx-auto bg-white rounded-2xl shadow-2xl animate-fade-in">
              <div className="flex items-center justify-center w-24 h-24 mx-auto mb-6 bg-[#FF7A35]/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-16 h-16 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Email envoyé</h3>
                <p className="text-sm text-gray-600 mb-6">
                  Si un compte existe avec cette adresse email, vous recevrez un lien de réinitialisation de mot de passe dans quelques minutes.
                </p>
                <div className="flex space-x-4 justify-center">
                  <button 
                    onClick={() => navigate('/login')}
                    className="px-8 py-3 text-sm font-semibold text-white transition-all duration-300 bg-[#FF7A35] rounded-lg hover:bg-[#FF8F59] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] transition-all duration-300"
                  >
                    Retour à la connexion
                  </button>
                </div>
                <div className="text-center text-sm text-gray-600 mt-4">
                  Redirection vers la connexion dans {cooldown} secondes...
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div>
              <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
                Réinitialisation du mot de passe
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Entrez votre adresse email pour recevoir un lien de réinitialisation
              </p>
            </div>
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Adresse email</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    placeholder="Entrez votre adresse email"
                    className={`block w-full pl-10 pr-3 py-2.5 border ${
                      error ? 'border-red-300' : 'border-gray-300'
                    } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-[#FF7A35] sm:text-sm`}
                    value={email}
                    onChange={(e) => {
                      setEmail(DOMPurify.sanitize(e.target.value));
                      setError('');
                    }}
                  />
                </div>
                {error && (
                  <div className="mt-2 flex items-center space-x-2 text-sm text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span>{error}</span>
                  </div>
                )}
              </div>

              <div>
                <button 
                  type="submit"
                  disabled={isLoading || cooldown > 0 || isButtonDisabled}
                  className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#FF7A35] hover:bg-[#FF8F59] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] transition-all duration-300 ${
                    isLoading || cooldown > 0 || isButtonDisabled ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isLoading ? 'Envoi en cours...' : 
                   cooldown > 0 ? `Redirection dans ${cooldown}s` : 
                   isButtonDisabled ? `Réessayer dans ${countdown} secondes` : 
                   'Envoyer le lien de réinitialisation'}
                </button>
              </div>

              <div className="text-center mt-4">
                <Link 
                  to="/login"
                  className="inline-flex items-center text-sm font-medium text-[#FF7A35] hover:text-[#FF8F59] transition-colors duration-300"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Retour à la connexion
                </Link>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default ForgotPassword;
