import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Paper,
  IconButton,
  Tooltip as MuiTooltip,
  useTheme,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { format, subDays } from 'date-fns';
import { fr } from 'date-fns/locale';
import vpsService from '../../services/vpsService';
import { formatBytes } from '../../utils/formatters';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import StorageIcon from '@mui/icons-material/Storage';
import MemoryIcon from '@mui/icons-material/Memory';
import SpeedIcon from '@mui/icons-material/Speed';
import TimerIcon from '@mui/icons-material/Timer';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import RefreshIcon from '@mui/icons-material/Refresh';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { TabPanel, TabContext } from '@mui/lab';
import { logger } from '@/utils/logger';

interface VPSMetrics {
  cpu_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  ram_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  disk_space: {
    unit: string;
    usage: Record<string, number>;
  };
  outgoing_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  incoming_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  uptime: {
    unit: string;
    usage: Record<string, number>;
  };
}

interface VPSInfo {
  hostname: string;
  state: string;
  cpus: number;
  memory: number;
  disk: number;
  bandwidth: number;
}

interface VPSHistory {
  id: number;
  created_at: string;
  name: string;
  state: string;
}

const PERIOD_OPTIONS = [
  { value: 1, label: '1 jour' },
  { value: 2, label: '2 jours' },
  { value: 3, label: '3 jours' },
  { value: 7, label: '7 jours' },
  { value: 15, label: '15 jours' },
  { value: 30, label: '30 jours' },
  { value: 45, label: '45 jours' },
  { value: 60, label: '60 jours' },
  { value: 75, label: '75 jours' },
  { value: 90, label: '90 jours' },
];

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
};

// Ajout des constantes de style
const CARD_STYLES = {
  borderRadius: 2,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.1)',
  },
};

const GRADIENT_OVERLAY = {
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
    borderRadius: 'inherit',
  },
};

const VPSMonitoring: React.FC = () => {
  const theme = useTheme();
  const [vpsInfo, setVpsInfo] = useState<VPSInfo | null>(null);
  const [metrics, setMetrics] = useState<VPSMetrics | null>(null);
  const [history, setHistory] = useState<VPSHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<number>(7);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  const fetchData = async () => {
    try {
      setRefreshing(true);
      const dateTo = new Date();
      const dateFrom = subDays(dateTo, selectedPeriod);
      
      const [infoResponse, metricsResponse, historyResponse] = await Promise.all([
        vpsService.getVPSInfo(),
        vpsService.getVPSMetrics(dateFrom, dateTo),
        vpsService.getVPSHistory(),
      ]);

      setVpsInfo(infoResponse[0]);
      setMetrics(metricsResponse);
      setHistory(historyResponse.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erreur lors de la récupération des données VPS');
      logger.info(err);
    } finally {
      setRefreshing(false);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 300000);
    return () => clearInterval(interval);
  }, [selectedPeriod]);

  const handlePeriodChange = (event: any) => {
    setSelectedPeriod(event.target.value);
  };

  const handleRestartClick = () => {
    setConfirmOpen(true);
  };

  const handleConfirmRestart = async () => {
    setConfirmOpen(false);
    try {
      await vpsService.restartVPS();
      fetchData();
    } catch (err) {
      setError('Erreur lors du redémarrage du VPS');
      logger.info(err);
    }
  };

  const handleCancelRestart = () => {
    setConfirmOpen(false);
  };

  const formatMetricsData = (metricsData: { unit: string; usage: Record<string, number> } | undefined) => {
    if (!metricsData) return [];
    return Object.entries(metricsData.usage).map(([timestamp, value]) => ({
      time: format(new Date(parseInt(timestamp) * 1000), 'dd/MM HH:mm', { locale: fr }),
      value: value as number,
    }));
  };

  const formatUptimeData = (uptimeData: { unit: string; usage: Record<string, number> } | undefined) => {
    if (!uptimeData) return [];
    return Object.entries(uptimeData.usage).map(([timestamp, value]) => ({
      time: format(new Date(parseInt(timestamp) * 1000), 'dd/MM HH:mm', { locale: fr }),
      value: Math.round((value as number) / 1000 / 60),
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const getStatusColor = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return theme.palette.success.main;
      case 'stopped':
        return theme.palette.error.main;
      default:
        return theme.palette.warning.main;
    }
  };

  const getStatusIcon = (state: string) => {
    switch (state.toLowerCase()) {
      case 'running':
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'stopped':
        return <WarningIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
    }
  };

  return (
    <TabContext value={activeTab}>
      <Box sx={{ 
        width: '100%', 
        bgcolor: 'background.paper',
        borderRadius: 3,
        overflow: 'hidden',
        boxShadow: '0 0 40px rgba(0,0,0,0.03)'
      }}>
        <Box sx={{ 
          borderBottom: 1, 
          borderColor: 'divider', 
          px: 4, 
          pt: 3,
          background: `linear-gradient(135deg, ${COLORS.background} 0%, white 100%)`
        }}>
          <Grid container alignItems="center" spacing={3}>
            <Grid size={{ xs: 12 }}>
              <Box display="flex" alignItems="center" gap={3}>
                <Box sx={{
                  background: COLORS.primary,
                  borderRadius: '50%',
                  p: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <StorageIcon sx={{ fontSize: 40, color: 'white' }} />
                </Box>
                <Box>
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      color: COLORS.primary,
                      fontWeight: 600,
                      letterSpacing: '-0.5px'
                    }} 
                    gutterBottom
                  >
                    Monitoring VPS
                  </Typography>
                  {vpsInfo && (
                    <Box display="flex" alignItems="center" gap={1.5}>
                      {getStatusIcon(vpsInfo.state)}
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          color: getStatusColor(vpsInfo.state),
                          fontWeight: 500 
                        }}
                      >
                        {vpsInfo.state.toUpperCase()}
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          color: COLORS.tertiary,
                          fontWeight: 500 
                        }}
                      >
                        • {vpsInfo.hostname}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Grid>
            <Grid size={{ xs: 'auto' }}>
              <Box display="flex" gap={2}>
                <FormControl size="small">
                  <InputLabel>Période</InputLabel>
                  <Select
                    value={selectedPeriod}
                    label="Période"
                    onChange={handlePeriodChange}
                    sx={{ 
                      minWidth: 120,
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0,0,0,0.1)'
                      }
                    }}
                  >
                    {PERIOD_OPTIONS.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <MuiTooltip title="Rafraîchir">
                  <IconButton 
                    onClick={fetchData}
                    disabled={refreshing}
                    sx={{
                      bgcolor: 'rgba(0,0,0,0.04)',
                      '&:hover': {
                        bgcolor: 'rgba(0,0,0,0.08)'
                      }
                    }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </MuiTooltip>
                <MuiTooltip title="Redémarrer">
                  <IconButton
                    onClick={handleRestartClick}
                    sx={{
                      color: COLORS.warning,
                      bgcolor: 'rgba(255,193,7,0.1)',
                      '&:hover': {
                        bgcolor: 'rgba(255,193,7,0.2)'
                      }
                    }}
                  >
                    <RestartAltIcon />
                  </IconButton>
                </MuiTooltip>
              </Box>
            </Grid>
          </Grid>

          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            sx={{ 
              mt: 3,
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                minWidth: 120,
                '&.Mui-selected': {
                  color: COLORS.primary
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: COLORS.primary
              }
            }}
          >
            <Tab label="Vue d'ensemble" value="overview" />
            <Tab label="Performances" value="performance" />
            <Tab label="Historique" value="history" />
          </Tabs>
        </Box>

        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mx: 4, 
              mt: 3,
              borderRadius: 2 
            }}
          >
            {error}
          </Alert>
        )}

        <TabPanel value="overview">
          <Box p={4}>
            <Grid container spacing={3}>
              {vpsInfo && (
                <>
                  <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                    <Paper 
                      sx={{ 
                        ...CARD_STYLES,
                        ...GRADIENT_OVERLAY,
                        p: 3,
                        height: '100%',
                        background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                        color: 'white'
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={2.5}>
                        <Box sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          borderRadius: '50%',
                          p: 1,
                          display: 'flex'
                        }}>
                          <SpeedIcon />
                        </Box>
                        <Box>
                          <Typography variant="overline" sx={{ opacity: 0.8 }}>CPU</Typography>
                          <Typography variant="h3" sx={{ fontWeight: 600 }}>{vpsInfo.cpus}</Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>Cœurs</Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                    <Paper 
                      sx={{ 
                        ...CARD_STYLES,
                        ...GRADIENT_OVERLAY,
                        p: 3,
                        height: '100%',
                        background: `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
                        color: 'white'
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={2.5}>
                        <Box sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          borderRadius: '50%',
                          p: 1,
                          display: 'flex'
                        }}>
                          <MemoryIcon />
                        </Box>
                        <Box>
                          <Typography variant="overline" sx={{ opacity: 0.8 }}>Mémoire</Typography>
                          <Typography variant="h3" sx={{ fontWeight: 600 }}>{formatBytes(vpsInfo.memory).split(' ')[0]}</Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>{formatBytes(vpsInfo.memory).split(' ')[1]}</Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                    <Paper 
                      sx={{ 
                        ...CARD_STYLES,
                        ...GRADIENT_OVERLAY,
                        p: 3,
                        height: '100%',
                        background: `linear-gradient(135deg, ${theme.palette.success.light} 0%, ${theme.palette.success.main} 100%)`,
                        color: 'white'
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={2.5}>
                        <Box sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          borderRadius: '50%',
                          p: 1,
                          display: 'flex'
                        }}>
                          <StorageIcon />
                        </Box>
                        <Box>
                          <Typography variant="overline" sx={{ opacity: 0.8 }}>Disque</Typography>
                          <Typography variant="h3" sx={{ fontWeight: 600 }}>{formatBytes(vpsInfo.disk).split(' ')[0]}</Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>{formatBytes(vpsInfo.disk).split(' ')[1]}</Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                    <Paper 
                      sx={{ 
                        ...CARD_STYLES,
                        ...GRADIENT_OVERLAY,
                        p: 3,
                        height: '100%',
                        background: `linear-gradient(135deg, ${theme.palette.warning.light} 0%, ${theme.palette.warning.main} 100%)`,
                        color: 'white'
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={2.5}>
                        <Box sx={{
                          bgcolor: 'rgba(255,255,255,0.2)',
                          borderRadius: '50%',
                          p: 1,
                          display: 'flex'
                        }}>
                          <NetworkCheckIcon />
                        </Box>
                        <Box>
                          <Typography variant="overline" sx={{ opacity: 0.8 }}>Bande Passante</Typography>
                          <Typography variant="h3" sx={{ fontWeight: 600 }}>{formatBytes(vpsInfo.bandwidth).split(' ')[0]}</Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>{formatBytes(vpsInfo.bandwidth).split(' ')[1]}/mois</Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                </>
              )}

              {metrics && (
                <>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card sx={CARD_STYLES}>
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Box sx={{
                              bgcolor: `${theme.palette.primary.main}15`,
                              borderRadius: '50%',
                              p: 1,
                              display: 'flex'
                            }}>
                              <SpeedIcon sx={{ color: theme.palette.primary.main }} />
                            </Box>
                            <Typography variant="h6" sx={{ fontWeight: 500 }}>
                              Utilisation CPU
                            </Typography>
                          </Box>
                          <Typography 
                            variant="h4" 
                            sx={{ 
                              color: theme.palette.primary.main,
                              fontWeight: 600 
                            }}
                          >
                            {formatMetricsData(metrics.cpu_usage).slice(-1)[0]?.value.toFixed(1)}%
                          </Typography>
                        </Box>
                        <ResponsiveContainer width="100%" height={250}>
                          <LineChart data={formatMetricsData(metrics.cpu_usage)}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke={theme.palette.primary.main}
                              name="CPU %"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card sx={CARD_STYLES}>
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box display="flex" alignItems="center" gap={2}>
                            <Box sx={{
                              bgcolor: `${theme.palette.secondary.main}15`,
                              borderRadius: '50%',
                              p: 1,
                              display: 'flex'
                            }}>
                              <MemoryIcon sx={{ color: theme.palette.secondary.main }} />
                            </Box>
                            <Typography variant="h6" sx={{ fontWeight: 500 }}>
                              Utilisation RAM
                            </Typography>
                          </Box>
                          <Typography 
                            variant="h4" 
                            sx={{ 
                              color: theme.palette.secondary.main,
                              fontWeight: 600 
                            }}
                          >
                            {formatBytes(formatMetricsData(metrics.ram_usage).slice(-1)[0]?.value || 0)}
                          </Typography>
                        </Box>
                        <ResponsiveContainer width="100%" height={250}>
                          <LineChart data={formatMetricsData(metrics.ram_usage)}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis tickFormatter={(value) => formatBytes(value)} />
                            <Tooltip formatter={(value: any) => formatBytes(value)} />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke={theme.palette.secondary.main}
                              name="RAM"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value="performance">
          <Box p={3}>
            <Grid container spacing={3}>
              {metrics && (
                <>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card sx={CARD_STYLES}>
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Box sx={{
                            bgcolor: `${theme.palette.success.main}15`,
                            borderRadius: '50%',
                            p: 1,
                            display: 'flex'
                          }}>
                            <StorageIcon sx={{ color: theme.palette.success.main }} />
                          </Box>
                          <Typography variant="h6" sx={{ fontWeight: 500 }}>
                            Espace Disque
                          </Typography>
                        </Box>
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart data={formatMetricsData(metrics.disk_space)}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis tickFormatter={(value) => formatBytes(value)} />
                            <Tooltip formatter={(value: any) => formatBytes(value)} />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke={theme.palette.success.main}
                              name="Espace utilisé"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid size={{ xs: 12, md: 6 }}>
                    <Card sx={CARD_STYLES}>
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Box sx={{
                            bgcolor: `${theme.palette.warning.main}15`,
                            borderRadius: '50%',
                            p: 1,
                            display: 'flex'
                          }}>
                            <NetworkCheckIcon sx={{ color: theme.palette.warning.main }} />
                          </Box>
                          <Typography variant="h6" sx={{ fontWeight: 500 }}>
                            Trafic Réseau
                          </Typography>
                        </Box>
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" allowDuplicatedCategory={false} />
                            <YAxis tickFormatter={(value) => formatBytes(value)} />
                            <Tooltip formatter={(value: any) => formatBytes(value)} />
                            <Legend />
                            <Line
                              type="monotone"
                              data={formatMetricsData(metrics.outgoing_traffic)}
                              dataKey="value"
                              stroke={theme.palette.error.main}
                              name="Sortant"
                              dot={false}
                            />
                            <Line
                              type="monotone"
                              data={formatMetricsData(metrics.incoming_traffic)}
                              dataKey="value"
                              stroke={theme.palette.success.main}
                              name="Entrant"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid size={{ xs: 12 }}>
                    <Card sx={CARD_STYLES}>
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Box sx={{
                            bgcolor: `${theme.palette.info.main}15`,
                            borderRadius: '50%',
                            p: 1,
                            display: 'flex'
                          }}>
                            <TimerIcon sx={{ color: theme.palette.info.main }} />
                          </Box>
                          <Typography variant="h6" sx={{ fontWeight: 500 }}>
                            Temps de Fonctionnement
                          </Typography>
                        </Box>
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart data={formatUptimeData(metrics.uptime)}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis />
                            <Tooltip formatter={(value: any) => `${value} minutes`} />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke={theme.palette.info.main}
                              name="Uptime"
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value="history">
          <Box p={3}>
            <Card sx={CARD_STYLES}>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="center" gap={2} mb={3}>
                  <Box sx={{
                    bgcolor: `${theme.palette.primary.main}15`,
                    borderRadius: '50%',
                    p: 1,
                    display: 'flex'
                  }}>
                    <RestartAltIcon sx={{ color: theme.palette.primary.main }} />
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 500 }}>
                    Historique des Actions
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  {history.map((action) => (
                    <Grid size={{ xs: 12 }} key={action.id}>
                      <Paper 
                        sx={{ 
                          p: 2,
                          borderRadius: 1,
                          bgcolor: 'background.paper',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}
                        elevation={1}
                      >
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            {format(new Date(action.created_at), 'dd/MM/yyyy HH:mm', {
                              locale: fr,
                            })}
                          </Typography>
                          <Typography>{action.name}</Typography>
                        </Box>
                        <Chip 
                          label={action.state}
                          color={action.state === 'success' ? 'success' : 'warning'}
                          size="small"
                        />
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Box>
        </TabPanel>
      </Box>

      <Dialog
        open={confirmOpen}
        onClose={handleCancelRestart}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" sx={{ color: COLORS.primary }}>
          Confirmer le redémarrage
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Êtes-vous sûr de vouloir redémarrer le VPS ? Cette opération peut entraîner une interruption temporaire du service.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={handleCancelRestart} 
            sx={{ color: COLORS.secondary }}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleConfirmRestart} 
            sx={{ color: COLORS.error }}
            autoFocus
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
    </TabContext>
  );
};

export default VPSMonitoring; 


