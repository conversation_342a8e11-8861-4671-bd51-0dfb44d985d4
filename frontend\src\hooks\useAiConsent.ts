import { useState, useEffect, useCallback } from 'react';
import { getCookie, setCookie, deleteCookie } from '../utils/cookieUtils';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { useAuth } from '../contexts/AuthContext';
import logger from '../utils/logger';

const COOKIE_NAME = 'ai_consent_accepted';
const COOKIE_EXPIRY = 365 * 24 * 60 * 60; // 1 an en secondes

interface UseAiConsentReturn {
  hasConsent: boolean;
  isLoading: boolean;
  error: string | null;
  checkConsent: () => Promise<boolean>;
  setConsent: (firstName: string, lastName: string) => Promise<boolean>;
  removeConsent: () => Promise<boolean>;
}

/**
 * Hook pour gérer le consentement à l'utilisation de l'IA
 */
export const useAiConsent = (): UseAiConsentReturn => {
  const { isAuthenticated, user } = useAuth();
  const [hasConsent, setHasConsent] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Vérifie si l'utilisateur a déjà donné son consentement
   */
  const checkConsent = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated) {
      setIsLoading(false);
      setHasConsent(false);
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Vérifier d'abord le cookie local
      const cookieConsent = getCookie(COOKIE_NAME);
      if (cookieConsent === 'true') {
        setHasConsent(true);
        setIsLoading(false);
        return true;
      }

      // Si pas de cookie, vérifier en base de données
      const headers = await getCommonHeaders();
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/user/ai-consent/status`,
        {
          headers,
          withCredentials: true
        }
      );

      const hasServerConsent = response.data?.hasConsent || false;
      
      // Si consentement en base mais pas de cookie, on met à jour le cookie
      if (hasServerConsent) {
        setCookie(COOKIE_NAME, 'true', COOKIE_EXPIRY, true);
      }
      
      setHasConsent(hasServerConsent);
      setIsLoading(false);
      return hasServerConsent;
    } catch (err) {
      logger.error('Erreur lors de la vérification du consentement IA:', err);
      setError('Impossible de vérifier votre consentement IA');
      setIsLoading(false);
      return false;
    }
  }, [isAuthenticated]);

  /**
   * Enregistre le consentement de l'utilisateur
   */
  const setConsent = async (firstName: string, lastName: string): Promise<boolean> => {
    if (!isAuthenticated) {
      setError('Vous devez être connecté pour donner votre consentement');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const headers = await getCommonHeaders();
      await axios.post(
        `${API_CONFIG.baseURL}/api/user/ai-consent`,
        { firstName, lastName },
        {
          headers,
          withCredentials: true
        }
      );

      // Mettre à jour le cookie
      setCookie(COOKIE_NAME, 'true', COOKIE_EXPIRY, true);
      
      setHasConsent(true);
      setIsLoading(false);
      return true;
    } catch (err) {
      logger.error('Erreur lors de l\'enregistrement du consentement IA:', err);
      setError('Une erreur est survenue lors de l\'enregistrement de votre consentement');
      setIsLoading(false);
      return false;
    }
  };

  /**
   * Supprime le consentement de l'utilisateur
   */
  const removeConsent = async (): Promise<boolean> => {
    setError(null);
    if (!isAuthenticated) {
      setError('Vous devez être connecté pour supprimer votre consentement');
      return false;
    }
    try {
      const headers = await getCommonHeaders();
      await axios.delete(`${API_CONFIG.baseURL}/api/user/ai-consent`, {
        headers,
        withCredentials: true
      });
      setHasConsent(false);
      // Supprimer le cookie local
      deleteCookie(COOKIE_NAME);
      // Notifier le reste de l'app
      window.dispatchEvent(new CustomEvent('ai-consent-updated'));
      return true;
    } catch (err) {
      logger.error('Erreur lors de la suppression du consentement IA:', err);
      setError('Une erreur est survenue lors de la suppression de votre consentement');
      return false;
    }
  };

  // Vérifier le consentement au chargement du hook
  useEffect(() => {
    if (isAuthenticated) {
      checkConsent();
    } else {
      setIsLoading(false);
      setHasConsent(false);
    }
  }, [isAuthenticated, checkConsent]);

  // Ajout : écoute l'event 'ai-consent-updated' pour re-checker le consentement sans reload
  useEffect(() => {
    const handleConsentUpdated = () => {
      checkConsent();
    };
    window.addEventListener('ai-consent-updated', handleConsentUpdated);
    return () => {
      window.removeEventListener('ai-consent-updated', handleConsentUpdated);
    };
  }, [checkConsent]);

  return {
    hasConsent,
    isLoading,
    error,
    checkConsent,
    setConsent,
    removeConsent
  };
};

export default useAiConsent;
