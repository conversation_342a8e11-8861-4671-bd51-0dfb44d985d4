// Pages SEO dynamiques optimisées - Version refonte complète inspirée de ServicesPage

import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Button,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link,
  Divider,
  Avatar,
  Paper,
  Stack,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Fade,
  Grow,
  LinearProgress,
  InputAdornment,
  TextField,
  MenuItem
} from '@mui/material';
import {
  LocationOn,
  Verified,
  NavigateNext,
  Favorite,
  FavoriteBorder,
  Share,
  TrendingUp,
  Security,
  FlashOn,
  AutoAwesome,
  Search,
  FilterList,
  ViewModule,
  ViewList,
  Star

} from '@mui/icons-material';
import { API_CONFIG } from '../../config/api';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../dashboard/services/types';
import {
  generateServiceCitySEO,
  updatePageMetadata,
  generateLocalBusinessSchema,
  injectStructuredData,
  formatServiceName,
  formatCityName,
  generateFAQSchema,
  generateBreadcrumbSchema,
  generateDetailedSchemas
} from '../../utils/seoUtils';
import SEOHead from '../../components/services/SEOHead';
import { Yard, Handyman, Pets, PersonOutline, Event, Business, LocalShipping, Campaign, School, Computer, Celebration, Spa, Business as BusinessIcon, Palette, SportsBasketball, Home, DirectionsCar, Brush, AccountBalance, Flight, Construction, Pool, Face, Nature } from '@mui/icons-material';
import { logger } from '@/utils/logger';


// Couleurs exactes de JobPartiel - Version optimisée
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowHover: 'rgba(0, 0, 0, 0.15)',
  glassBorder: 'rgba(255, 255, 255, 0.18)',
  gradient: {
    primary: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 50%, #FF965E 100%)',
    secondary: 'linear-gradient(135deg, #FFE4BA 0%, #FFF8F3 100%)',
    glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)'
  }
};

interface Provider {
  user_id: string;
  slug: string;
  nom: string;
  prenom: string;
  ville: string;
  code_postal: string;
  bio: string;
  photo_url: string;
  slogan: string;
  updated_at: string;
  users: {
    user_type: string;
    profil_actif: boolean;
    profil_verifier: boolean;
  };
  user_services: Array<{
    id: string;
    titre: string;
    description: string;
    category_id: string;
    subcategory_id: string;
    tarif_horaire: number;
    statut: string;
  }>;
}

interface RecentRequest {
  id: string;
  titre: string;
  description: string;
  budget: number;
  ville: string;
  code_postal: string;
  date_creation: string;
  category_id: string;
  subcategory_id: string;
}

const ServiceCityPage: React.FC = () => {
  const { service: rawService, ville: rawVille } = useParams<{ service: string; ville: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Décoder les paramètres d'URL pour gérer les caractères accentués
  const service = rawService ? decodeURIComponent(rawService) : '';
  const ville = rawVille ? decodeURIComponent(rawVille) : '';

  // États principaux
  const [providers, setProviders] = useState<Provider[]>([]);
  const [recentRequests, setRecentRequests] = useState<RecentRequest[]>([]);
  const [nearbyCities, setNearbyCities] = useState<string[]>([]);
  const [similarServices, setSimilarServices] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // États UI améliorés
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('relevance');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [budgetFilter, setBudgetFilter] = useState('');

  // Hooks et effets optimisés
  useEffect(() => {
    if (service && ville) {
      loadPageData();

      // Mettre à jour les métadonnées SEO
      const seoMetadata = generateServiceCitySEO(service, ville);
      updatePageMetadata(seoMetadata);
    }
  }, [service, ville]);

  useEffect(() => {
    if (service && ville && providers.length > 0) {
      // Injecter les données structurées JSON-LD complètes pour un SEO optimal
      const detailedSchemas = generateDetailedSchemas(service, ville, providers);

      // Injecter tous les schémas pour maximiser le SEO
      injectStructuredData(detailedSchemas.organization);

      // Ajouter le schéma FAQ spécifique à cette page
      const faqSchema = generateFAQSchema(service, ville);
      injectStructuredData(faqSchema);

      // Ajouter le schéma de navigation (breadcrumb)
      const breadcrumbSchema = generateBreadcrumbSchema(service, ville);
      injectStructuredData(breadcrumbSchema);

      // Injecter le schéma de service local
      const localBusinessSchema = generateLocalBusinessSchema(service, ville, providers);
      injectStructuredData(localBusinessSchema);
    }
  }, [service, ville, providers]);

  useEffect(() => {
    // Animation d'entrée de page
    const timer = setTimeout(() => setIsPageLoaded(true), 100);

    // Charger les favoris depuis localStorage
    const savedFavorites = localStorage.getItem('jobpartiel_profile_favorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }

    return () => clearTimeout(timer);
  }, []);

  const loadPageData = async () => {
    try {
      setLoading(true);
      setError(null);
      setLoadingProgress(0);

      // Animation de progression du chargement
      const progressSteps = [20, 40, 60, 80, 100];
      let currentStep = 0;

      const updateProgress = () => {
        if (currentStep < progressSteps.length) {
          setLoadingProgress(progressSteps[currentStep]);
          currentStep++;
          setTimeout(updateProgress, 200);
        }
      };

      updateProgress();

      // Charger directement les données - le backend gère l'échantillon aléatoire

      // Charger les données en parallèle
      const [providersResponse, requestsResponse, citiesResponse, servicesResponse] = await Promise.all([
        fetch(`${API_CONFIG.baseURL}/api/services/providers/${service}/${ville}`, {
          method: 'GET',
          headers: API_CONFIG.headers,
          credentials: 'include'
        }),
        fetch(`${API_CONFIG.baseURL}/api/services/recent-missions/${service}/${ville}?limit=12`, {
          method: 'GET',
          headers: API_CONFIG.headers,
          credentials: 'include'
        }),
        fetch(`${API_CONFIG.baseURL}/api/seo-pages/nearby-cities/${ville}`, {
          method: 'GET',
          headers: API_CONFIG.headers,
          credentials: 'include'
        }),
        fetch(`${API_CONFIG.baseURL}/api/seo-pages/similar-services/${service}`, {
          method: 'GET',
          headers: API_CONFIG.headers,
          credentials: 'include'
        })
      ]);

      if (providersResponse.ok) {
        const providersData = await providersResponse.json();
        setProviders(providersData.providers || []);
        setPagination(providersData.pagination || pagination);
      }

      if (requestsResponse.ok) {
        const requestsData = await requestsResponse.json();
        setRecentRequests(requestsData || []);
      } else {
        // Même en cas d'erreur, on continue avec un tableau vide
        setRecentRequests([]);
      }

      if (citiesResponse.ok) {
        const citiesData = await citiesResponse.json();
        setNearbyCities(citiesData || []);
      }

      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json();
        setSimilarServices(servicesData || []);
      }

    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setTimeout(() => {
        setLoading(false);
        setLoadingProgress(100);
      }, 500);
    }
  };



  // Fonctions utilitaires améliorées
  const toggleFavorite = (providerId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(providerId)) {
        newFavorites.delete(providerId);
      } else {
        newFavorites.add(providerId);
      }
      localStorage.setItem('jobpartiel_profile_favorites', JSON.stringify([...newFavorites]));
      return newFavorites;
    });
  };

  const handleProviderClick = (provider: Provider) => {
    navigate(`/profil/${provider.slug}`);
  };

  const handleCityClick = (city: string) => {
    navigate(`/services/${service}/${city.toLowerCase()}`);
  };

  const handleServiceClick = (serviceSlug: string) => {
    navigate(`/services/${serviceSlug}/${ville}`);
  };

  const handleShare = async (provider: Provider) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${provider.prenom} ${provider.nom.charAt(0)}. - ${formatServiceName(service!)}`,
          text: `Découvrez ${provider.prenom}, jobbeur ${formatServiceName(service!)} à ${formatCityName(ville!)}`,
          url: window.location.href
        });
      } catch (err) {
        logger.error('Erreur lors du partage:', err);
      }
    } else {
      // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // Filtrage et tri des providers
  const filteredProviders = useMemo(() => {
    let filtered = [...providers];

    // Filtrage par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(provider =>
        provider.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        provider.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        provider.bio?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        provider.user_services.some(service =>
          service.titre.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filtrage par budget
    if (budgetFilter) {
      const [min, max] = budgetFilter.split('-').map(Number);
      filtered = filtered.filter(provider => {
        const minPrice = Math.min(...provider.user_services.map(s => s.tarif_horaire));
        return max ? (minPrice >= min && minPrice <= max) : minPrice >= min;
      });
    }

    // Tri
    switch (sortBy) {
      case 'price_asc':
        filtered.sort((a, b) => {
          const priceA = Math.min(...a.user_services.map(s => s.tarif_horaire));
          const priceB = Math.min(...b.user_services.map(s => s.tarif_horaire));
          return priceA - priceB;
        });
        break;
      case 'price_desc':
        filtered.sort((a, b) => {
          const priceA = Math.min(...a.user_services.map(s => s.tarif_horaire));
          const priceB = Math.min(...b.user_services.map(s => s.tarif_horaire));
          return priceB - priceA;
        });
        break;
      case 'name':
        filtered.sort((a, b) => a.prenom.localeCompare(b.prenom));
        break;
      default: // relevance
        // Garder l'ordre original (pertinence)
        break;
    }

    return filtered;
  }, [providers, searchTerm, budgetFilter, sortBy]);

  if (loading) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        position: 'relative'
      }}>
        {/* Barre de progression de chargement */}
        <Fade in={loading}>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 9999,
              background: COLORS.white,
              boxShadow: `0 4px 20px ${COLORS.shadow}`
            }}
          >
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 4,
                background: COLORS.lightGray,
                '& .MuiLinearProgress-bar': {
                  background: COLORS.gradient.primary,
                  borderRadius: '2px'
                }
              }}
            />
          </Box>
        </Fade>

        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Skeleton variant="text" width="60%" height={80} sx={{ mb: 2 }} />
          <Skeleton variant="text" width="40%" height={40} sx={{ mb: 4 }} />

          <Grid container spacing={3}>
            {[...Array(6)].map((_, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <Card sx={{ borderRadius: 4, overflow: 'hidden' }}>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" width="80%" height={30} />
                    <Skeleton variant="text" width="60%" height={20} />
                    <Skeleton variant="text" width="40%" height={20} />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Container maxWidth="md">
          <Alert
            severity="error"
            sx={{
              borderRadius: 4,
              boxShadow: `0 8px 30px ${COLORS.shadow}`,
              fontSize: '1.1rem'
            }}
          >
            {error}
          </Alert>
        </Container>
      </Box>
    );
  }

  // Afficher le contenu même s'il n'y a pas de jobbeurs spécifiques
  // La logique backend gère déjà l'échantillon aléatoire

  // Générer les métadonnées SEO
  const seoMetadata = generateServiceCitySEO(service!, ville!);

  return (
    <>
      <SEOHead
        title={seoMetadata.title}
        description={seoMetadata.description}
        keywords={seoMetadata.keywords}
        canonical={seoMetadata.canonical}
        ogImage={seoMetadata.ogImage}
        ogType={seoMetadata.ogType}
        ogLocale={seoMetadata.ogLocale}
        twitterCard={seoMetadata.twitterCard}
        twitterImage={seoMetadata.twitterImage}
        structuredData={generateDetailedSchemas(service!, ville!, providers).organization}
      />
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        position: 'relative',
        overflow: 'hidden',
        paddingTop: { xs: '80px', md: '100px', lg: '120px' } // Espace pour le header fixe
      }}>
      {/* Styles d'animation globaux */}
      <style>
        {`
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
          }
        `}
      </style>

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 1, py: { xs: 2, md: 4 } }}>
        {/* Breadcrumbs améliorés */}
        <Fade in={isPageLoaded} timeout={600}>
          <Breadcrumbs
            separator={<NavigateNext fontSize="small" />}
            sx={{
              mb: 4,
              '& .MuiBreadcrumbs-li': {
                fontSize: '1rem'
              }
            }}
          >
            <Link
              color="inherit"
              href="/"
              underline="hover"
              sx={{
                color: COLORS.neutral,
                '&:hover': { color: COLORS.primary }
              }}
            >
              Accueil
            </Link>
            <Link
              color="inherit"
              href="/services"
              underline="hover"
              sx={{
                color: COLORS.neutral,
                '&:hover': { color: COLORS.primary }
              }}
            >
              Services
            </Link>
            <Typography color="text.primary" sx={{ fontWeight: 600 }}>
              {formatServiceName(service!)} à {formatCityName(ville!)}
            </Typography>
          </Breadcrumbs>
        </Fade>

        {/* Hero Section amélioré avec éléments visuels modernes */}
        <Fade in={isPageLoaded} timeout={800}>
          <Box
            textAlign="center"
            sx={{
              mb: { xs: 6, md: 8 },
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* Éléments décoratifs de fond */}
            <Box
              sx={{
                position: 'absolute',
                top: -50,
                left: -50,
                width: 200,
                height: 200,
                background: `linear-gradient(135deg, ${COLORS.primary}20, ${COLORS.secondary}10)`,
                borderRadius: '50%',
                filter: 'blur(40px)',
                animation: 'float 6s ease-in-out infinite',
                zIndex: 0
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 100,
                right: -100,
                width: 300,
                height: 300,
                background: `linear-gradient(135deg, ${COLORS.accent}30, ${COLORS.tertiary}15)`,
                borderRadius: '50%',
                filter: 'blur(60px)',
                animation: 'float 8s ease-in-out infinite reverse',
                zIndex: 0
              }}
            />

            <Box sx={{ position: 'relative', zIndex: 1 }}>
              {/* Badge de service */}
              <Grow in={isPageLoaded} timeout={1000}>
                <Chip
                  label={filteredProviders.length > 0
                    ? `${filteredProviders.length} professionnel${filteredProviders.length > 1 ? 's' : ''} disponible${filteredProviders.length > 1 ? 's' : ''}`
                    : "Professionnels recommandés"
                  }
                  sx={{
                    mb: 3,
                    mt: 2,
                    bgcolor: `${COLORS.primary}15`,
                    color: COLORS.primary,
                    fontWeight: 600,
                    fontSize: { xs: '0.8rem', md: '1rem' },
                    py: 1,
                    height: 'auto',
                    borderRadius: 4,
                    border: `2px solid ${COLORS.primary}30`,
                    '&:hover': {
                      bgcolor: `${COLORS.primary}25`,
                      transform: 'scale(1.05)'
                    }
                  }}
                />
              </Grow>

              <Typography
                variant={isMobile ? "h3" : "h1"}
                component="h1"
                sx={{
                  fontSize: { xs: '2.5rem', md: '4rem', lg: '4.5rem' },
                  fontWeight: 900,
                  background: COLORS.gradient.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 3,
                  position: 'relative',
                  letterSpacing: '-0.02em',
                  lineHeight: 1.1
                }}
              >
                {formatServiceName(service!)} à {formatCityName(ville!)}
              </Typography>

              <Typography
                variant={isMobile ? "h6" : "h4"}
                sx={{
                  color: COLORS.neutral,
                  mb: 4,
                  fontSize: { xs: '1.1rem', md: '1.3rem' },
                  fontWeight: 500,
                  maxWidth: '800px',
                  mx: 'auto',
                  lineHeight: 1.6
                }}
              >
                Trouvez le jobbeur parfait pour vos besoins
                <br />
                <Box component="span" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                  Profils vérifiés • Avis clients • Réservation sécurisée
                </Box>
              </Typography>

              {/* Boutons d'action principaux */}
              <Stack
                direction={isMobile ? "column" : "row"}
                spacing={2}
                justifyContent="center"
                sx={{ mb: 6 }}
              >
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/dashboard/missions/poster-une-mission')}
                  sx={{
                    background: COLORS.gradient.primary,
                    borderRadius: 4,
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    boxShadow: `0 8px 25px ${COLORS.primary}30`,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 12px 35px ${COLORS.primary}40`
                    }
                  }}
                >
                  Poster une mission
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => navigate('/services')}
                  sx={{
                    borderColor: COLORS.primary,
                    color: COLORS.primary,
                    borderRadius: 4,
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderWidth: 2,
                    '&:hover': {
                      borderColor: COLORS.secondary,
                      color: COLORS.secondary,
                      transform: 'translateY(-2px)',
                      borderWidth: 2
                    }
                  }}
                >
                  Tous les services
                </Button>
              </Stack>

              {/* Stats de confiance améliorées */}
              <Grid container spacing={3} justifyContent="center" sx={{ maxWidth: '900px', mx: 'auto' }}>
                {[
                  {
                    icon: <Verified />,
                    value: "100%",
                    label: "Jobbeurs vérifiés",
                    color: COLORS.success,
                    description: "Identité et compétences vérifiées"
                  },
                  {
                    icon: <Security />,
                    value: "24/7",
                    label: "Support client",
                    color: COLORS.primary,
                    description: "Assistance disponible en permanence"
                  },
                  {
                    icon: <FlashOn />,
                    value: "< 1h",
                    label: "Réponse rapide",
                    color: COLORS.warning,
                    description: "Première réponse garantie"
                  }
                ].map((stat, index) => (
                  <Grid size={{ xs: 12, md: 4 }} key={index}>
                    <Grow in={isPageLoaded} timeout={1200 + index * 200}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 4,
                          textAlign: 'center',
                          bgcolor: COLORS.white,
                          border: `3px solid ${COLORS.borderColor}`,
                          borderRadius: 4,
                          height: '100%',
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          '&:hover': {
                            borderColor: stat.color,
                            boxShadow: `0 12px 40px ${stat.color}20`,
                            transform: 'translateY(-8px) scale(1.02)'
                          }
                        }}
                      >
                        <Avatar
                          sx={{
                            bgcolor: `${stat.color}15`,
                            color: stat.color,
                            width: 64,
                            height: 64,
                            mx: 'auto',
                            mb: 2,
                            transition: 'all 0.3s ease'
                          }}
                        >
                          {stat.icon}
                        </Avatar>
                        <Typography variant="h4" sx={{ fontWeight: 800, color: stat.color, mb: 1 }}>
                          {stat.value}
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary', mb: 1 }}>
                          {stat.label}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.9rem' }}>
                          {stat.description}
                        </Typography>
                      </Paper>
                    </Grow>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Box>
        </Fade>

        {/* Barre de filtres et recherche */}
        <Fade in={isPageLoaded} timeout={1000}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 4 },
              mb: 6,
              background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
              border: `3px solid ${COLORS.borderColor}`,
              borderRadius: 5,
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: `0 8px 30px rgba(0, 0, 0, 0.08)`
            }}
          >
            <Stack spacing={3}>
              {/* En-tête des filtres */}
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Typography variant="h5" sx={{
                  fontWeight: 700,
                  color: COLORS.primary,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <FilterList />
                  Affiner votre recherche
                </Typography>

                <Stack direction="row" spacing={1}>
                  <Tooltip title={viewMode === 'grid' ? 'Vue liste' : 'Vue grille'}>
                    <IconButton
                      onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                      sx={{
                        color: COLORS.primary,
                        '&:hover': { bgcolor: `${COLORS.primary}10` }
                      }}
                    >
                      {viewMode === 'grid' ? <ViewList /> : <ViewModule />}
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Box>

              {/* Filtres */}
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    placeholder="Rechercher un jobbeur..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search sx={{ color: COLORS.primary }} />
                          </InputAdornment>
                        )
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    select
                    label="Budget horaire"
                    value={budgetFilter}
                    onChange={(e) => setBudgetFilter(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="">Tous les budgets</MenuItem>
                    <MenuItem value="0-15">0€ - 15€/h</MenuItem>
                    <MenuItem value="15-25">15€ - 25€/h</MenuItem>
                    <MenuItem value="25-35">25€ - 35€/h</MenuItem>
                    <MenuItem value="35-50">35€ - 50€/h</MenuItem>
                    <MenuItem value="50-999">50€+/h</MenuItem>
                  </TextField>
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    select
                    label="Trier par"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 4,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        '&:hover': {
                          borderColor: COLORS.primary
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`
                        },
                        '& fieldset': { border: 'none' }
                      }
                    }}
                  >
                    <MenuItem value="relevance">Pertinence</MenuItem>
                    <MenuItem value="price_asc">Prix croissant</MenuItem>
                    <MenuItem value="price_desc">Prix décroissant</MenuItem>
                    <MenuItem value="name">Nom A-Z</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </Stack>
          </Paper>
        </Fade>

        {/* Section des jobbeurs améliorée */}
        <Fade in={isPageLoaded} timeout={1200}>
          <Box sx={{ mb: 8 }}>
            {/* En-tête de section avec design moderne */}
            <Box textAlign="center" sx={{ mb: 6 }}>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 800,
                  color: COLORS.primary,
                  mb: 2,
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  position: 'relative'
                }}
              >
                Nos Jobbeurs {formatServiceName(service!)}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -8,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: 80,
                    height: 4,
                    background: COLORS.gradient.primary,
                    borderRadius: 2
                  }}
                />
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: COLORS.neutral,
                  fontWeight: 500,
                  maxWidth: '600px',
                  mx: 'auto'
                }}
              >
                Découvrez nos professionnels qualifiés à {formatCityName(ville!)}
              </Typography>
            </Box>

            <Grid container spacing={4}>
              {filteredProviders.map((provider, index) => (
                <Grid size={{ xs: 12, md: 6, lg: 4 }} key={provider.user_id}>
                  <Grow in={isPageLoaded} timeout={1400 + index * 100}>
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 6,
                        overflow: 'hidden',
                        background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                        border: `3px solid ${COLORS.borderColor}`,
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'pointer',
                        position: 'relative',
                        display: 'flex',
                        flexDirection: 'column',
                        '&:hover': {
                          transform: 'translateY(-12px) scale(1.03)',
                          borderColor: COLORS.primary,
                          boxShadow: `0 25px 70px ${COLORS.primary}30`,
                          '& .provider-avatar': {
                            transform: 'scale(1.15)',
                            boxShadow: `0 12px 30px ${COLORS.primary}50`
                          },
                          '& .provider-actions': {
                            opacity: 1,
                            transform: 'translateY(0)'
                          },
                          '& .provider-image': {
                            transform: 'scale(1.1)'
                          },
                          '& .provider-badge': {
                            transform: 'scale(1.1) rotate(5deg)'
                          }
                        }
                      }}
                      onClick={() => handleProviderClick(provider)}
                    >
                      {/* Badge de vérification amélioré */}
                      {provider.users.profil_verifier && (
                        <Box
                          className="provider-badge"
                          sx={{
                            position: 'absolute',
                            top: 16,
                            right: 16,
                            zIndex: 3,
                            bgcolor: COLORS.success,
                            color: COLORS.white,
                            borderRadius: '50%',
                            width: 48,
                            height: 48,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: `0 6px 20px ${COLORS.success}40`,
                            border: `3px solid ${COLORS.white}`,
                            transition: 'all 0.3s ease'
                          }}
                        >
                          <Verified fontSize="medium" />
                        </Box>
                      )}

                      {/* Badge de popularité */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 16,
                          left: 16,
                          zIndex: 3,
                          bgcolor: `${COLORS.primary}95`,
                          color: COLORS.white,
                          borderRadius: 3,
                          px: 2,
                          py: 0.5,
                          fontSize: '0.75rem',
                          fontWeight: 600,
                          backdropFilter: 'blur(10px)',
                          border: `1px solid ${COLORS.white}50`
                        }}
                      >
                        ⭐ TOP JOBBEUR
                      </Box>

                      {/* Actions flottantes */}
                      <Box
                        className="provider-actions"
                        sx={{
                          position: 'absolute',
                          top: 16,
                          left: 16,
                          zIndex: 2,
                          opacity: 0,
                          transform: 'translateY(-10px)',
                          transition: 'all 0.3s ease',
                          display: 'flex',
                          gap: 1
                        }}
                      >
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite(provider.user_id);
                          }}
                          sx={{
                            bgcolor: COLORS.white,
                            color: favorites.has(provider.user_id) ? COLORS.error : COLORS.neutral,
                            boxShadow: `0 4px 15px ${COLORS.shadow}`,
                            '&:hover': {
                              bgcolor: COLORS.white,
                              transform: 'scale(1.1)'
                            }
                          }}
                        >
                          {favorites.has(provider.user_id) ? <Favorite fontSize="small" /> : <FavoriteBorder fontSize="small" />}
                        </IconButton>

                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare(provider);
                          }}
                          sx={{
                            bgcolor: COLORS.white,
                            color: COLORS.primary,
                            boxShadow: `0 4px 15px ${COLORS.shadow}`,
                            '&:hover': {
                              bgcolor: COLORS.white,
                              transform: 'scale(1.1)'
                            }
                          }}
                        >
                          <Share fontSize="small" />
                        </IconButton>
                      </Box>

                      {/* Image de profil améliorée */}
                      <Box
                        sx={{
                          position: 'relative',
                          height: 220,
                          background: `linear-gradient(135deg, ${COLORS.accent} 0%, ${COLORS.primary} 100%)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          overflow: 'hidden'
                        }}
                      >
                        {/* Effet de fond décoratif */}
                        <Box
                          sx={{
                            position: 'absolute',
                            top: -20,
                            right: -20,
                            width: 100,
                            height: 100,
                            background: `linear-gradient(45deg, ${COLORS.white}20, transparent)`,
                            borderRadius: '50%',
                            filter: 'blur(20px)'
                          }}
                        />
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: -30,
                            left: -30,
                            width: 120,
                            height: 120,
                            background: `linear-gradient(45deg, ${COLORS.secondary}30, transparent)`,
                            borderRadius: '50%',
                            filter: 'blur(25px)'
                          }}
                        />

                        {provider.photo_url ? (
                          <CardMedia
                            className="provider-image"
                            component="img"
                            height="220"
                            image={provider.photo_url}
                            alt={`${provider.prenom} ${provider.nom}`}
                            sx={{
                              objectFit: 'cover',
                              transition: 'transform 0.4s ease',
                              position: 'relative',
                              zIndex: 1
                            }}
                          />
                        ) : (
                          <Avatar
                            className="provider-avatar"
                            sx={{
                              width: 90,
                              height: 90,
                              fontSize: '2.2rem',
                              fontWeight: 700,
                              bgcolor: COLORS.white,
                              color: COLORS.primary,
                              transition: 'all 0.4s ease',
                              boxShadow: `0 12px 30px ${COLORS.shadow}`,
                              border: `4px solid ${COLORS.white}`,
                              position: 'relative',
                              zIndex: 2
                            }}
                          >
                            {provider.prenom.charAt(0)}{provider.nom.charAt(0)}
                          </Avatar>
                        )}

                        {/* Indicateur de disponibilité */}
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 12,
                            right: 12,
                            bgcolor: COLORS.success,
                            color: COLORS.white,
                            borderRadius: 2,
                            px: 1.5,
                            py: 0.5,
                            fontSize: '0.7rem',
                            fontWeight: 600,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                            zIndex: 2,
                            border: `2px solid ${COLORS.white}`
                          }}
                        >
                          <Box
                            sx={{
                              width: 6,
                              height: 6,
                              bgcolor: COLORS.white,
                              borderRadius: '50%',
                              animation: 'pulse 2s infinite'
                            }}
                          />
                          DISPONIBLE
                        </Box>
                      </Box>

                      <CardContent sx={{ p: 3, flexGrow: 1, display: 'flex', flexDirection: 'column', padding:'14px!important' }}>
                        {/* Nom et localisation */}
                        <Box sx={{ mb: 2 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 700,
                              color: COLORS.primary,
                              mb: 0.5,
                              fontSize: '1.2rem'
                            }}
                          >
                            {provider.prenom} {provider.nom.charAt(0)}.
                          </Typography>

                          <Box display="flex" alignItems="center" gap={0.5} sx={{ mb: 1 }}>
                            <LocationOn sx={{ fontSize: 16, color: COLORS.neutral }} />
                            <Typography variant="body2" color="text.secondary">
                              {provider.ville} ({provider.code_postal})
                            </Typography>
                          </Box>
                        </Box>

                        {/* Slogan */}
                        {provider.slogan && (
                          <Typography
                            variant="body2"
                            sx={{
                              fontStyle: 'italic',
                              color: COLORS.neutral,
                              mb: 2,
                              fontSize: '0.9rem',
                              lineHeight: 1.4
                            }}
                          >
                            "{provider.slogan}"
                          </Typography>
                        )}

                        {/* Services */}
                        <Box sx={{ mb: 2, flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1, color: 'text.primary' }}>
                            Services proposés :
                          </Typography>
                          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                            {provider.user_services.slice(0, 2).map((service) => (
                              <Chip
                                key={service.id}
                                label={service.titre}
                                size="small"
                                sx={{
                                  bgcolor: `${COLORS.primary}15`,
                                  color: COLORS.primary,
                                  fontWeight: 600,
                                  fontSize: '0.75rem',
                                  '&:hover': {
                                    bgcolor: `${COLORS.primary}25`
                                  }
                                }}
                              />
                            ))}
                            {provider.user_services.length > 2 && (
                              <Chip
                                label={`+${provider.user_services.length - 2}`}
                                size="small"
                                sx={{
                                  bgcolor: `${COLORS.secondary}15`,
                                  color: COLORS.secondary,
                                  fontWeight: 600,
                                  fontSize: '0.75rem'
                                }}
                              />
                            )}
                          </Stack>
                        </Box>

                        {/* Prix et CTA */}
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            pt: 2,
                            borderTop: `2px solid ${COLORS.borderColor}`
                          }}
                        >
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                              À partir de
                            </Typography>
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 800,
                                color: COLORS.primary,
                                fontSize: '1.1rem'
                              }}
                            >
                              {Math.min(...provider.user_services.map(s => s.tarif_horaire))}€/h
                            </Typography>
                          </Box>

                          <Button
                            variant="contained"
                            size="small"
                            sx={{
                              background: COLORS.gradient.primary,
                              borderRadius: 3,
                              px: 3,
                              py: 1,
                              fontWeight: 600,
                              fontSize: '0.85rem',
                              boxShadow: `0 4px 15px ${COLORS.primary}30`,
                              '&:hover': {
                                transform: 'translateY(-2px)',
                                boxShadow: `0 6px 20px ${COLORS.primary}40`
                              }
                            }}
                          >
                            Contacter
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Fade>

      {/* Section des dernières demandes - Design moderne amélioré */}
      <Fade in={isPageLoaded} timeout={1300}>
        <Box sx={{ mb: 8 }}>
          {/* En-tête de section avec design moderne */}
          <Box textAlign="center" sx={{ mb: 6 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
                position: 'relative'
              }}
            >
              Dernières Missions à {formatCityName(ville!)}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: COLORS.gradient.primary,
                  borderRadius: 2
                }}
              />
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: COLORS.neutral,
                fontWeight: 500,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              {recentRequests.length > 0
                ? `${recentRequests.length} mission${recentRequests.length > 1 ? 's' : ''} récente${recentRequests.length > 1 ? 's' : ''}`
                : `Missions disponibles pour ${formatServiceName(service!)} à ${formatCityName(ville!)}`
              }
            </Typography>
          </Box>

          {recentRequests.length > 0 ? (
            <Grid container spacing={4}>
              {recentRequests.map((request, index) => (
                <Grid size={{ xs: 12, md: 6, lg: 4 }} key={request.id}>
                  <Grow in={isPageLoaded} timeout={1500 + index * 100}>
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 6,
                        overflow: 'hidden',
                        background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                        border: `3px solid ${COLORS.borderColor}`,
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'pointer',
                        position: 'relative',
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '24px!important',
                        '&:hover': {
                          transform: 'translateY(-12px) scale(1.03)',
                          borderColor: COLORS.secondary,
                          boxShadow: `0 25px 80px ${COLORS.secondary}30`,
                        },
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: 8,
                          background: COLORS.gradient.primary,
                          zIndex: 1
                        }
                      }}
                      onClick={() => {
                        // Rediriger vers la page de connexion ou d'inscription pour postuler
                        navigate('/auth/login', {
                          state: {
                            redirectTo: `/dashboard/missions/all?highlight=${request.id}`,
                            message: 'Connectez-vous pour postuler à cette mission'
                          }
                        });
                      }}
                    >
                      {/* Badge "NOUVEAU" repositionné pour éviter la superposition */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: { xs: 12, md: 16 },
                          right: { xs: 12, md: 16 },
                          zIndex: 3,
                          background: COLORS.gradient.primary,
                          color: COLORS.white,
                          borderRadius: 3,
                          px: { xs: 1.5, md: 2 },
                          py: { xs: 0.3, md: 0.5 },
                          fontSize: { xs: '0.6rem', md: '0.7rem' },
                          fontWeight: 700,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                          boxShadow: `0 6px 20px ${COLORS.primary}40`,
                          animation: 'pulse 2s infinite',
                          '@keyframes pulse': {
                            '0%, 100%': { transform: 'scale(1)' },
                            '50%': { transform: 'scale(1.05)' }
                          }
                        }}
                      >
                        <FlashOn sx={{ fontSize: { xs: 12, md: 14 } }} />
                        NOUVEAU
                      </Box>

                      {/* Icône de catégorie dynamique selon la sous-catégorie */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: { xs: 12, md: 16 },
                          left: { xs: 12, md: 16 },
                          width: { xs: 40, md: 50 },
                          height: { xs: 40, md: 50 },
                          borderRadius: '50%',
                          background: `${COLORS.accent}20`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          zIndex: 2,
                          opacity: 0.9,
                          border: `2px solid ${COLORS.white}`
                        }}
                      >
                        <Typography variant="h6" sx={{ opacity: 0.9, fontSize: { xs: '1rem', md: '1.2rem' } }}>
                          {/* Icônes spécifiques selon la sous-catégorie */}
                          {request.subcategory_id === '14-3' ? <Palette /> : // Ébénisterie
                           request.subcategory_id === '17-1' ? <DirectionsCar /> : // Mécanique auto
                           request.subcategory_id === '17-2' ? <DirectionsCar /> : // Pneus (utiliser l'icône auto)
                           request.subcategory_id === '1-1' ? <Yard /> :   // Tonte pelouse (utiliser l'icône Jardinage)
                           request.subcategory_id === '1-2' ? <Yard /> :   // Taille haies (utiliser l'icône Jardinage)
                           request.subcategory_id === '3-1' ? <Pets /> :   // Garde chiens (utiliser l'icône Garde animaux)
                           request.subcategory_id === '3-2' ? <Pets /> :   // Garde chats (utiliser l'icône Garde animaux)
                           request.subcategory_id === '4-1' ? <PersonOutline /> : // Services à la personne (utiliser icône catégorie 4)
                           request.subcategory_id === '4-2' ? <PersonOutline /> : // Services à la personne (utiliser icône catégorie 4)
                           request.subcategory_id === '5-1' ? <Event /> :   // Ménage (utiliser icône Evénementiel & Restauration, ou trouver une meilleure icône si disponible)
                           request.subcategory_id === '5-2' ? <Event /> :   // Vitres (utiliser icône Evénementiel & Restauration, ou trouver une meilleure icône si disponible)
                           // Fallback par catégorie en utilisant les icônes de serviceTypes.ts
                           request.category_id === '1' ? <Yard /> : // Jardinage
                           request.category_id === '2' ? <Handyman /> : // Bricolage
                           request.category_id === '3' ? <Pets /> : // Garde d'animaux
                           request.category_id === '4' ? <PersonOutline /> : // Services à la personne
                           request.category_id === '5' ? <Event /> : // Événementiel & Restauration
                           request.category_id === '6' ? <Business /> : // Services administratifs
                           request.category_id === '7' ? <LocalShipping /> : // Transport & Logistique
                           request.category_id === '8' ? <Campaign /> : // Communication & Marketing
                           request.category_id === '9' ? <School /> : // Éducation & Formation
                           request.category_id === '10' ? <Computer /> : // Informatique
                           request.category_id === '11' ? <Celebration /> : // Arts & Divertissement
                           request.category_id === '12' ? <Spa /> : // Bien-être & Santé
                           request.category_id === '13' ? <Business /> : // Services aux entreprises (utiliser Business comme dans serviceTypes.ts)
                           request.category_id === '14' ? <Palette /> : // Artisanat & Création
                           request.category_id === '15' ? <SportsBasketball /> : // Sport & Loisirs
                           request.category_id === '16' ? <Home /> : // Immobilier & Habitat
                           request.category_id === '17' ? <DirectionsCar /> : // Automobile & Transport
                           request.category_id === '18' ? <Brush /> : // Décoration & Design
                           request.category_id === '19' ? <AccountBalance /> : // Services financiers
                           request.category_id === '20' ? <Flight /> : // Tourisme & Voyages
                           request.category_id === '21' ? <Construction /> : // Rénovation & Travaux
                           request.category_id === '22' ? <Pool /> : // Piscine & Spa
                           request.category_id === '23' ? <Face /> : // Mode & Beauté
                           request.category_id === '24' ? <Security /> : // Sécurité & Protection
                           request.category_id === '25' ? <Nature /> : // Environnement & Écologie
                           <Handyman />} {/* Default fallback to Handyman icon */}
                        </Typography>
                      </Box>

                      <CardContent sx={{ p: 3, pt: 5, flexGrow: 1, display: 'flex', flexDirection: 'column', position: 'relative', zIndex: 2 }}>
                        {/* Titre de la mission avec espace pour éviter la superposition */}
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 800,
                            color: COLORS.primary,
                            mb: 2.5,
                            fontSize: { xs: '1rem', md: '1.2rem' },
                            lineHeight: 1.3,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            mt: { xs: 3, md: 1 },
                            pr: { xs: 1, md: 2 },
                            pl: { xs: 1, md: 6 }
                          }}
                        >
                          {request.titre}
                        </Typography>

                        {/* Localisation avec style moderne */}
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1.5,
                            mb: 2.5,
                            p: 2.5,
                            borderRadius: 4,
                            background: `${COLORS.secondary}08`,
                            border: `1px solid ${COLORS.secondary}20`
                          }}
                        >
                          <LocationOn sx={{ fontSize: 22, color: COLORS.secondary }} />
                          <Typography
                            variant="body2"
                            sx={{
                              color: COLORS.secondary,
                              fontWeight: 600,
                              fontSize: '0.95rem'
                            }}
                          >
                            {request.ville} ({request.code_postal})
                          </Typography>
                        </Box>

                        {/* Description avec style moderne */}
                        <Box
                          sx={{
                            mb: 2.5,
                            flexGrow: 1,
                            p: 3,
                            borderRadius: 4,
                            background: `${COLORS.lightGray}40`,
                            border: `1px solid ${COLORS.borderColor}`,
                            minHeight: '80px'
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              color: 'text.primary',
                              lineHeight: 1.6,
                              fontSize: '0.95rem',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical'
                            }}
                            dangerouslySetInnerHTML={{
                              __html: request.description.length > 120
                                ? request.description.substring(0, 120) + '...'
                                : request.description
                            }}
                          />
                        </Box>

                        {/* Footer avec budget et date - Design premium responsive */}
                        <Box
                          sx={{
                            mb: 2.5,
                            p: { xs: 2.5, md: 3 },
                            borderRadius: 4,
                            background: `linear-gradient(135deg, ${COLORS.accent}15 0%, ${COLORS.primary}05 100%)`,
                            border: `1px solid ${COLORS.accent}40`
                          }}
                        >
                          {/* Budget */}
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'space-between',
                            mb: { xs: 2, md: 0 }
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Box
                                sx={{
                                  width: { xs: 45, md: 50 },
                                  height: { xs: 45, md: 50 },
                                  borderRadius: '50%',
                                  background: COLORS.gradient.primary,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: `0 6px 20px ${COLORS.primary}30`,
                                  flexShrink: 0
                                }}
                              >
                                <Typography variant="h5" sx={{ color: COLORS.white, fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                                  💰
                                </Typography>
                              </Box>
                              <Box>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: 'text.secondary',
                                    fontSize: { xs: '0.8rem', md: '0.85rem' },
                                    fontWeight: 500,
                                    mb: 0.5
                                  }}
                                >
                                  Budget proposé
                                </Typography>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontWeight: 800,
                                    color: COLORS.primary,
                                    fontSize: { xs: '1.2rem', md: '1.3rem' }
                                  }}
                                >
                                  {request.budget > 0 ? `${request.budget}€` : 'N/C'}
                                </Typography>
                              </Box>
                            </Box>

                            {/* Date - à droite sur desktop, en dessous sur mobile */}
                            <Box sx={{ 
                              display: { xs: 'none', md: 'flex' },
                              alignItems: 'center', 
                              gap: 2
                            }}>
                              <Box sx={{ textAlign: 'right' }}>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.8rem',
                                    display: 'block',
                                    fontWeight: 500
                                  }}
                                >
                                  Publié le
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: COLORS.secondary,
                                    fontWeight: 700,
                                    fontSize: '1rem'
                                  }}
                                >
                                  {new Date(request.date_creation).toLocaleDateString('fr-FR', {
                                    day: 'numeric',
                                    month: 'short'
                                  })}
                                </Typography>
                              </Box>
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: '50%',
                                  background: `${COLORS.secondary}15`,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexShrink: 0
                                }}
                              >
                                <Typography variant="h6" sx={{ fontSize: '1rem' }}>📅</Typography>
                              </Box>
                            </Box>
                          </Box>

                          {/* Date sur mobile - ligne séparée */}
                          <Box sx={{ 
                            display: { xs: 'flex', md: 'none' },
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: 1.5,
                            pt: 2,
                            borderTop: `1px solid ${COLORS.borderColor}`
                          }}>
                            <Box
                              sx={{
                                width: 32,
                                height: 32,
                                borderRadius: '50%',
                                background: `${COLORS.secondary}15`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              <Typography variant="h6" sx={{ fontSize: '0.9rem' }}>📅</Typography>
                            </Box>
                            <Typography
                              variant="body2"
                              sx={{
                                color: COLORS.secondary,
                                fontWeight: 600,
                                fontSize: '0.9rem'
                              }}
                            >
                              Publié le {new Date(request.date_creation).toLocaleDateString('fr-FR', {
                                day: 'numeric',
                                month: 'short'
                              })}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Bouton d'action premium avec effet brillant */}
                        <Button
                          variant="contained"
                          fullWidth
                          sx={{
                            background: COLORS.gradient.primary,
                            borderRadius: 5,
                            py: 2,
                            fontSize: '1.1rem',
                            fontWeight: 700,
                            boxShadow: `0 10px 30px ${COLORS.primary}35`,
                            position: 'relative',
                            overflow: 'hidden',
                            textTransform: 'none',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: `0 15px 40px ${COLORS.primary}45`
                            },
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: '-100%',
                              width: '100%',
                              height: '100%',
                              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                              transition: 'left 0.6s',
                            },
                            '&:hover::before': {
                              left: '100%'
                            }
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate('/auth/login', {
                              state: {
                                redirectTo: `/dashboard/missions/all?highlight=${request.id}`,
                                message: 'Connectez-vous pour postuler à cette mission'
                              }
                            });
                          }}
                        >
                          ✨ Postuler à cette mission
                        </Button>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              ))}
            </Grid>
          ) : (
            // Message quand aucune mission n'est trouvée
            <Box textAlign="center" sx={{ py: 8 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  maxWidth: 600,
                  mx: 'auto',
                  background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                  border: `3px solid ${COLORS.borderColor}`,
                  borderRadius: 5
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: `${COLORS.primary}15`,
                    color: COLORS.primary,
                    width: 80,
                    height: 80,
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <Search sx={{ fontSize: 40 }} />
                </Avatar>

                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    color: COLORS.primary,
                    mb: 2
                  }}
                >
                  Aucune mission récente trouvée
                </Typography>

                <Typography
                  variant="body1"
                  sx={{
                    color: 'text.secondary',
                    mb: 4,
                    lineHeight: 1.6
                  }}
                >
                  Il n'y a pas encore de missions publiées pour {formatServiceName(service!)} à {formatCityName(ville!)}.
                  Soyez le premier à publier une mission !
                </Typography>

                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/dashboard/missions/poster-une-mission')}
                  sx={{
                    background: COLORS.gradient.primary,
                    borderRadius: 4,
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    boxShadow: `0 8px 25px ${COLORS.primary}30`,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 12px 35px ${COLORS.primary}40`
                    }
                  }}
                >
                  Publier une mission
                </Button>
              </Paper>
            </Box>
          )}
        </Box>
      </Fade>

      {/* Section SEO enrichie et moderne */}
      <Fade in={isPageLoaded} timeout={1600}>
        <Box sx={{ mb: 8 }}>
          {/* En-tête de section */}
          <Box textAlign="center" sx={{ mb: 6 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
                position: 'relative'
              }}
            >
              Tout savoir sur {formatServiceName(service!)} à {formatCityName(ville!)}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 120,
                  height: 4,
                  background: COLORS.gradient.primary,
                  borderRadius: 2
                }}
              />
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: COLORS.neutral,
                fontWeight: 500,
                maxWidth: '700px',
                mx: 'auto'
              }}
            >
              Guide complet pour trouver et choisir le meilleur professionnel
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {[
              {
                icon: <Search sx={{ fontSize: 40 }} />,
                title: "Comment trouver le bon jobbeur ?",
                content: [
                  `Pour trouver le meilleur jobbeur en ${formatServiceName(service!)} à ${formatCityName(ville!)}, consultez les profils vérifiés, lisez les avis clients et comparez les tarifs.`,
                  "Tous nos jobbeurs sont évalués par la communauté et leurs compétences sont vérifiées.",
                  "Utilisez notre système de filtres avancés pour affiner votre recherche selon vos critères : budget, disponibilité, zone d'intervention."
                ],
                color: COLORS.primary
              },
              {
                icon: <Verified sx={{ fontSize: 40 }} />,
                title: "Comment bien choisir ?",
                content: [
                  "Vérifiez les certifications, l'expérience et les spécialisations du jobbeur.",
                  "Consultez sa galerie photo pour voir ses réalisations précédentes et évaluer la qualité de son travail.",
                  "N'hésitez pas à demander un devis gratuit et à échanger avec le jobbeur avant de confirmer votre mission."
                ],
                color: COLORS.success
              },
              {
                icon: <Security sx={{ fontSize: 40 }} />,
                title: "Pourquoi choisir JobPartiel ?",
                content: [
                  "JobPartiel révolutionne le jobbing avec son système d'échange Jobi unique en France, permettant de payer en euros ou d'échanger/troquer des services via notre système d'échange de Jobi.",
                  "Plateforme 100% française basée dans le sud de la France, nous garantissons la vérification de tous nos jobbeurs lors qu'ils rejoignent la plateforme et demande le statut vérifié.",
                  "Contrairement aux autres plateformes, JobPartiel favorise l'économie collaborative avec des abonnements transparents (gratuit, et dès 9€/mois) sans aucunes commissions sur votre travail."
                ],
                color: COLORS.warning
              }
            ].map((section, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Grow in={isPageLoaded} timeout={1800 + index * 200}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      height: '100%',
                      background: `linear-gradient(135deg, ${COLORS.white} 0%, ${section.color}05 100%)`,
                      border: `3px solid ${section.color}20`,
                      borderRadius: 5,
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        borderColor: section.color,
                        boxShadow: `0 12px 40px ${section.color}20`,
                        transform: 'translateY(-8px)'
                      }
                    }}
                  >
                    <Box textAlign="center" sx={{ mb: 3 }}>
                      <Avatar
                        sx={{
                          bgcolor: `${section.color}15`,
                          color: section.color,
                          width: 80,
                          height: 80,
                          mx: 'auto',
                          mb: 2
                        }}
                      >
                        {section.icon}
                      </Avatar>
                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          color: section.color,
                          mb: 2
                        }}
                      >
                        {section.title}
                      </Typography>
                    </Box>

                    <Stack spacing={2}>
                      {section.content.map((paragraph, pIndex) => (
                        <Typography
                          key={pIndex}
                          variant="body1"
                          sx={{
                            color: 'text.primary',
                            lineHeight: 1.6,
                            fontSize: '1rem'
                          }}
                        >
                          {paragraph}
                        </Typography>
                      ))}
                    </Stack>
                  </Paper>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Fade>

      {/* Section SEO dynamique avec données réelles - Design moderne */}
      <Fade in={isPageLoaded} timeout={1400}>
        <Box sx={{ mb: 8 }}>
          <Divider sx={{ mb: 6, borderColor: COLORS.borderColor }} />

          {/* En-tête de section moderne */}
          <Box textAlign="center" sx={{ mb: 6 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
                position: 'relative'
              }}
            >
              Explorez plus de services
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 120,
                  height: 4,
                  background: COLORS.gradient.primary,
                  borderRadius: 2
                }}
              />
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: COLORS.neutral,
                fontWeight: 500,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              Découvrez d'autres jobbeurs et services dans votre région
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* Villes proches dynamiques */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  height: '100%',
                  background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.primary}05 100%)`,
                  border: `3px solid ${COLORS.primary}20`,
                  borderRadius: 5,
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    borderColor: COLORS.primary,
                    boxShadow: `0 12px 40px ${COLORS.primary}20`,
                    transform: 'translateY(-4px)'
                  }
                }}
              >
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{
                    fontWeight: 700,
                    color: COLORS.primary,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}>
                    <LocationOn />
                    {formatServiceName(service!)} dans les villes proches
                  </Typography>
                  <Typography variant="body2" sx={{ color: COLORS.neutral, fontWeight: 500 }}>
                    Trouvez des professionnels dans votre région
                  </Typography>
                </Box>
                
                <Grid container spacing={2}>
                  {nearbyCities.slice(0, 6).map((city) => (
                    <Grid size={{ xs: 6, sm: 4 }} key={city}>
                      <Paper
                        elevation={0}
                        onClick={() => handleCityClick(city)}
                        sx={{
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          bgcolor: COLORS.white,
                          border: `2px solid ${COLORS.borderColor}`,
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            borderColor: COLORS.primary,
                            bgcolor: `${COLORS.primary}08`,
                            transform: 'translateY(-2px)',
                            boxShadow: `0 8px 25px ${COLORS.primary}20`
                          }
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 600,
                            color: COLORS.primary,
                            fontSize: '0.9rem'
                          }}
                        >
                          {formatServiceName(service!)}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: COLORS.neutral,
                            display: 'block',
                            mt: 0.5
                          }}
                        >
                          {city}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            </Grid>

            {/* Services similaires dynamiques */}
            <Grid size={{ xs: 12, md: 6 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  height: '100%',
                  background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.secondary}05 100%)`,
                  border: `3px solid ${COLORS.secondary}20`,
                  borderRadius: 5,
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    borderColor: COLORS.secondary,
                    boxShadow: `0 12px 40px ${COLORS.secondary}20`,
                    transform: 'translateY(-4px)'
                  }
                }}
              >
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{
                    fontWeight: 700,
                    color: COLORS.secondary,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    flexDirection: { xs: 'column', sm: 'row' },
                    textAlign: { xs: 'center', sm: 'left' }
                  }}>
                    <TrendingUp sx={{ fontSize: { xs: '2rem', sm: '1.5rem' } }} />
                    <Box component="span" sx={{ fontSize: { xs: '1.2rem', sm: '1.25rem' } }}>
                      Services similaires à {formatCityName(ville!)}
                    </Box>
                  </Typography>
                  <Typography variant="body2" sx={{ 
                    color: COLORS.neutral, 
                    fontWeight: 500,
                    textAlign: { xs: 'center', sm: 'left' }
                  }}>
                    Découvrez d'autres services populaires
                  </Typography>
                </Box>
                
                <Grid container spacing={2}>
                  {similarServices.slice(0, 6).map((serviceSlug) => (
                    <Grid size={{ xs: 6, sm: 4 }} key={serviceSlug}>
                      <Paper
                        elevation={0}
                        onClick={() => handleServiceClick(serviceSlug)}
                        sx={{
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          bgcolor: COLORS.white,
                          border: `2px solid ${COLORS.borderColor}`,
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            borderColor: COLORS.secondary,
                            bgcolor: `${COLORS.secondary}08`,
                            transform: 'translateY(-2px)',
                            boxShadow: `0 8px 25px ${COLORS.secondary}20`
                          }
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 600,
                            color: COLORS.secondary,
                            fontSize: '0.85rem',
                            lineHeight: 1.3
                          }}
                        >
                          {formatServiceName(serviceSlug)}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            color: COLORS.neutral,
                            display: 'block',
                            mt: 0.5
                          }}
                        >
                          {formatCityName(ville!)}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            </Grid>
          </Grid>

          {/* Autres services populaires - Design moderne */}
          <Box sx={{ mt: 6 }}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.tertiary}05 100%)`,
                border: `3px solid ${COLORS.tertiary}20`,
                borderRadius: 5,
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  borderColor: COLORS.tertiary,
                  boxShadow: `0 12px 40px ${COLORS.tertiary}20`,
                  transform: 'translateY(-4px)'
                }
              }}
            >
              <Box sx={{ mb: 3 }}>
                <Typography variant="h5" sx={{
                  fontWeight: 700,
                  color: COLORS.tertiary,
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' }
                }}>
                  <AutoAwesome sx={{ fontSize: { xs: '2rem', sm: '1.5rem' } }} />
                  <Box component="span" sx={{ fontSize: { xs: '1.2rem', sm: '1.25rem' } }}>
                    Autres services populaires
                  </Box>
                </Typography>
                <Typography variant="body2" sx={{ 
                  color: COLORS.neutral, 
                  fontWeight: 500,
                  textAlign: { xs: 'center', sm: 'left' }
                }}>
                  Les services les plus demandés sur JobPartiel
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                {SERVICE_SUBCATEGORIES
                  .filter(sub => ['1-1', '1-2', '2-1', '2-2', '3-1', '3-2', '4-1', '4-2', '5-1', '5-2'].includes(sub.id))
                  .map((subcategory) => (
                    <Grid size={{ xs: 6, sm: 4, md: 3, lg: 2.4 }} key={subcategory.id}>
                      <Paper
                        elevation={0}
                        onClick={() => {
                          const params = new URLSearchParams();
                          if (ville) params.append('city', ville);
                          const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId);
                          if (categoryObj) params.append('category', categoryObj.nom);
                          params.append('subcategory', subcategory.nom);
                          navigate(`/services/search?${params.toString()}`);
                        }}
                        sx={{
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          bgcolor: COLORS.white,
                          border: `2px solid ${COLORS.borderColor}`,
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            borderColor: COLORS.tertiary,
                            bgcolor: `${COLORS.tertiary}08`,
                            transform: 'translateY(-2px)',
                            boxShadow: `0 8px 25px ${COLORS.tertiary}20`
                          }
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 600,
                            color: COLORS.tertiary,
                            fontSize: '0.8rem',
                            lineHeight: 1.3
                          }}
                        >
                          {subcategory.nom}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
              </Grid>
            </Paper>
          </Box>
        </Box>
      </Fade>

      {/* Section SEO complète sur JobPartiel et les Jobi */}
      <Fade in={isPageLoaded} timeout={1600}>
        <Box sx={{ mb: 8 }}>
          <Container maxWidth="lg">
            {/* Titre principal SEO */}
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 4,
                textAlign: 'center',
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}
            >
              JobPartiel : La Révolution du Jobbing en France
            </Typography>

            {/* Contenu SEO riche */}
            <Grid container spacing={6}>
              {/* Section sur JobPartiel */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    height: '100%',
                    background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.primary}05 100%)`,
                    border: `2px solid ${COLORS.primary}20`,
                    borderRadius: 4
                  }}
                >
                  <Typography
                    variant="h3"
                    component="h3"
                    sx={{
                      fontWeight: 700,
                      color: COLORS.primary,
                      mb: 3,
                      fontSize: { xs: '1.5rem', md: '1.8rem' }
                    }}
                  >
                    🏡 Qu'est-ce que JobPartiel ?
                  </Typography>

                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, color: 'text.primary' }}>
                    <strong>JobPartiel.fr</strong> est la première plateforme française de jobbing révolutionnaire, spécialisée dans la mise en relation entre particuliers et professionnels vérifiés pour des services de <strong>jardinage</strong>, <strong>bricolage</strong>, <strong>garde d'animaux</strong>, <strong>ménage</strong> et plus de 20 catégories de services.
                  </Typography>

                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, color: 'text.primary' }}>
                    Basée dans <strong>le sud de la France</strong>, notre plateforme se distingue par son approche innovante de l'économie collaborative, garantissant la <strong>vérification de tous nos jobbeurs</strong> et la <strong>sécurité de vos transactions</strong>.
                  </Typography>

                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, color: 'text.primary' }}>
                    Contrairement aux plateformes concurrentes comme TaskRabbit, Allovoisins, Frizbiz / Ringtwice ou Yoojo, JobPartiel propose des <strong>abonnements transparents</strong> sans commissions cachées et un système d'échange unique avec les Jobi.
                  </Typography>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.secondary, mb: 2 }}>
                      🎯 Nos avantages concurrentiels :
                    </Typography>
                    <Stack spacing={1}>
                      {[
                        "✅ Système d'échange Jobi unique en France",
                        "✅ Vérification systématique de tous les jobbeurs avec profil vérifié",
                        "✅ Abonnements transparents (gratuit, 9€/mois)",
                        "✅ Plateforme 100% française et sécurisée",
                        "✅ Support client réactif et personnalisé",
                        "✅ Économie collaborative et éco-responsable"
                      ].map((avantage, index) => (
                        <Typography key={index} variant="body2" sx={{ color: 'text.secondary' }}>
                          {avantage}
                        </Typography>
                      ))}
                    </Stack>
                  </Box>
                </Paper>
              </Grid>

              {/* Section sur les Jobi */}
              <Grid size={{ xs: 12, md: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    height: '100%',
                    background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.secondary}05 100%)`,
                    border: `2px solid ${COLORS.secondary}20`,
                    borderRadius: 4
                  }}
                >
                  <Typography
                    variant="h3"
                    component="h3"
                    sx={{
                      fontWeight: 700,
                      color: COLORS.secondary,
                      mb: 3,
                      fontSize: { xs: '1.5rem', md: '1.8rem' }
                    }}
                  >
                    💰 Les Jobi : Notre Système d'Échange Révolutionnaire
                  </Typography>

                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, color: 'text.primary' }}>
                    Les <strong>Jobi</strong> constituent l'innovation majeure de JobPartiel : un <strong>système d'échange et de troc</strong> qui révolutionne le concept du troc moderne et de l'économie collaborative.
                  </Typography>

                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, color: 'text.primary' }}>
                    Contrairement aux systèmes de paiement classiques, les Jobi permettent d'<strong>échanger des services sans transaction financière</strong>, favorisant l'entraide et la solidarité entre membres de la communauté.
                  </Typography>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.tertiary, mb: 2 }}>
                      🚀 Comment gagner des Jobi :
                    </Typography>
                    <Stack spacing={1}>
                      {[
                        "🎁 10 Jobi offerts à l'inscription",
                        "📝 1 Jobi par mission publiée (max 20/jour)",
                        "⭐ Jobi bonus pour les avis clients",
                        "👥 Récompenses de parrainage",
                        "🏆 Badges de performance",
                        "🔄 Échanges entre utilisateurs"
                      ].map((methode, index) => (
                        <Typography key={index} variant="body2" sx={{ color: 'text.secondary' }}>
                          {methode}
                        </Typography>
                      ))}
                    </Stack>
                  </Box>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.tertiary, mb: 2 }}>
                      💡 Utilisation des Jobi :
                    </Typography>
                    <Stack spacing={1}>
                      {[
                        "🔧 Paiement de services (jardinage, bricolage, etc.)",
                        "🎯 Accès aux fonctionnalités premium",
                        "🛍️ Conversion en bons d'achat partenaires",
                        "🤝 Échange direct entre utilisateurs",
                        "🎨 Crédits IA pour génération de contenu",
                        "⚡ Protection anti-désistement"
                      ].map((utilisation, index) => (
                        <Typography key={index} variant="body2" sx={{ color: 'text.secondary' }}>
                          {utilisation}
                        </Typography>
                      ))}
                    </Stack>
                  </Box>
                </Paper>
              </Grid>
            </Grid>

            {/* Section comparative avec la concurrence */}
            <Box sx={{ mt: 6 }}>
              <Typography
                variant="h3"
                component="h3"
                sx={{
                  fontWeight: 700,
                  color: COLORS.primary,
                  mb: 4,
                  textAlign: 'center',
                  fontSize: { xs: '1.5rem', md: '1.8rem' }
                }}
              >
                🥇 JobPartiel vs Concurrence : Pourquoi Nous Choisir ?
              </Typography>

              <Grid container spacing={3}>
                {[
                  {
                    title: "💰 Tarification",
                    jobpartiel: "Abonnements transparents dès 0€/mois, pas de commissions cachées",
                    concurrence: "Commissions de 15-20% sur chaque transaction",
                    color: COLORS.success
                  },
                  {
                    title: "🔄 Innovation",
                    jobpartiel: "Système Jobi unique : échange de services sans argent",
                    concurrence: "Paiement uniquement en euros, pas d'alternative",
                    color: COLORS.primary
                  },
                  {
                    title: "🛡️ Sécurité",
                    jobpartiel: "Vérification systématique, plateforme française RGPD",
                    concurrence: "Vérifications partielles, données hébergées à l'étranger",
                    color: COLORS.warning
                  },
                  {
                    title: "🎯 Spécialisation",
                    jobpartiel: "Expert jardinage, bricolage, garde d'animaux, ménage",
                    concurrence: "Généraliste sans expertise métier spécifique",
                    color: COLORS.secondary
                  }
                ].map((comparaison, index) => (
                  <Grid size={{ xs: 12, md: 6, lg: 3 }} key={index}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        height: '100%',
                        background: `linear-gradient(135deg, ${COLORS.white} 0%, ${comparaison.color}05 100%)`,
                        border: `2px solid ${comparaison.color}20`,
                        borderRadius: 4,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          borderColor: comparaison.color,
                          transform: 'translateY(-5px)',
                          boxShadow: `0 10px 30px ${comparaison.color}20`
                        }
                      }}
                    >
                      <Typography variant="h6" sx={{ fontWeight: 700, color: comparaison.color, mb: 2 }}>
                        {comparaison.title}
                      </Typography>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: COLORS.success, mb: 1 }}>
                          ✅ JobPartiel :
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.primary', lineHeight: 1.5 }}>
                          {comparaison.jobpartiel}
                        </Typography>
                      </Box>

                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, color: COLORS.error, mb: 1 }}>
                          ❌ Concurrence :
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary', lineHeight: 1.5 }}>
                          {comparaison.concurrence}
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Section témoignages et statistiques */}
            <Box sx={{ mt: 6 }}>
              <Typography
                variant="h3"
                component="h3"
                sx={{
                  fontWeight: 700,
                  color: COLORS.primary,
                  mb: 4,
                  textAlign: 'center',
                  fontSize: { xs: '1.5rem', md: '1.8rem' }
                }}
              >
                📊 JobPartiel en Chiffres
              </Typography>

              <Grid container spacing={4}>
                {[
                  {
                    number: "100%",
                    label: "Jobbeurs vérifiés",
                    description: "Tous nos professionnels passent par un processus de vérification rigoureux",
                    icon: "🛡️"
                  },
                  {
                    number: "0€",
                    label: "Commission cachée",
                    description: "Transparence totale avec nos abonnements clairs et sans surprise",
                    icon: "💰"
                  },
                  {
                    number: "< 24h",
                    label: "Première réponse",
                    description: "Recevez rapidement des propositions de jobbeurs qualifiés",
                    icon: "⚡"
                  },
                  {
                    number: "5★",
                    label: "Satisfaction client",
                    description: "Excellence reconnue par notre communauté d'utilisateurs",
                    icon: "⭐"
                  }
                ].map((stat, index) => (
                  <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        textAlign: 'center',
                        height: '100%',
                        background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.accent}20 100%)`,
                        border: `2px solid ${COLORS.accent}`,
                        borderRadius: 4,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: `0 10px 30px ${COLORS.primary}15`
                        }
                      }}
                    >
                      <Typography variant="h2" sx={{ fontSize: '3rem', mb: 1 }}>
                        {stat.icon}
                      </Typography>
                      <Typography variant="h3" sx={{ fontWeight: 800, color: COLORS.primary, mb: 1 }}>
                        {stat.number}
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.secondary, mb: 2 }}>
                        {stat.label}
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary', lineHeight: 1.5 }}>
                        {stat.description}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Container>
        </Box>
      </Fade>

      {/* FAQ dynamiques par service/ville */}
      <Fade in={isPageLoaded} timeout={1800}>
        <Box sx={{ mb: 8 }}>
          <Container maxWidth="lg">
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 6,
                textAlign: 'center',
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}
            >
              ❓ Questions Fréquentes - {formatServiceName(service!)} à {formatCityName(ville!)}
            </Typography>

            <Grid container spacing={4}>
              {[
                {
                  question: `Combien coûte un service de ${formatServiceName(service!)} à ${formatCityName(ville!)} ?`,
                  answer: `Les tarifs pour ${formatServiceName(service!)} à ${formatCityName(ville!)} varient selon la complexité et la durée. Sur JobPartiel, vous pouvez comparer les prix de nos jobbeurs. Avec notre système Jobi unique, vous pouvez aussi échanger des services au lieu de payer en euros, ce qui rend nos prestations encore plus accessibles.`,
                  icon: "💰"
                },
                {
                  question: `Comment trouver un jobbeur fiable en ${formatServiceName(service!)} à ${formatCityName(ville!)} ?`,
                  answer: `Sur JobPartiel, tous nos jobbeurs sont 100% vérifiés avec le badge "vérifié". Consultez leurs profils détaillés, avis clients authentiques, galeries photos de réalisations et certifications. Notre système de notation communautaire garantit la qualité. Contrairement aux autres plateformes, nous privilégions la transparence et la sécurité.`,
                  icon: "🛡️"
                },
                {
                  question: `Qu'est-ce qui différencie JobPartiel des autres plateformes ?`,
                  answer: `JobPartiel révolutionne le jobbing avec son système d'échange Jobi unique en France. Plateforme 100% française basée dans le sud de la France, nous proposons des abonnements transparents sans commissions cachées (gratuit et dès 9€/mois). Vous pouvez payer en euros ou échanger des services avec les Jobi, favorisant l'économie collaborative.`,
                  icon: "🚀"
                },
                {
                  question: `Comment fonctionne le système Jobi pour ${formatServiceName(service!)} ?`,
                  answer: `Les Jobi sont notre système de troc et d'échange révolutionnaire. Gagnez des Jobi en publiant des missions (1 Jobi/mission), en laissant des avis, ou par parrainage. Utilisez-les pour échanger des services de ${formatServiceName(service!)} ou échangez-les contre d'autres prestations. C'est l'économie du troc moderne !`,
                  icon: "🔄"
                },
                {
                  question: `Combien de temps pour trouver un jobbeur en ${formatServiceName(service!)} à ${formatCityName(ville!)} ?`,
                  answer: `Sur JobPartiel, vous recevez généralement vos premières propositions en moins de 24h. Notre communauté active de jobbeurs vérifiés en ${formatServiceName(service!)} à ${formatCityName(ville!)} répond rapidement. Postez votre mission gratuitement et comparez les offres.`,
                  icon: "⚡"
                },
                {
                  question: `JobPartiel est-il sécurisé pour ${formatServiceName(service!)} à ${formatCityName(ville!)} ?`,
                  answer: `Absolument ! JobPartiel garantit la sécurité avec la vérification systématique des jobbeurs avec le badge "vérifié", le système d'échange de Jobi sécurisé et la protection anti-désistement avec les Jobi. Plateforme française conforme RGPD, vos données restent en France. Support client réactif en cas de problème.`,
                  icon: "🔒"
                }
              ].map((faq, index) => (
                <Grid size={{ xs: 12, md: 6 }} key={index}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      height: '100%',
                      background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.accent}10 100%)`,
                      border: `2px solid ${COLORS.accent}`,
                      borderRadius: 4,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        borderColor: COLORS.primary,
                        transform: 'translateY(-5px)',
                        boxShadow: `0 10px 30px ${COLORS.primary}15`
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: { xs: 1, md: 2 }, flexDirection: { xs: 'column', md: 'row' }, mb: 2 }}>
                      <Typography variant="h3" sx={{ fontSize: { xs: '1.5rem', md: '2rem' } }}>
                        {faq.icon}
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 700,
                          color: COLORS.primary,
                          lineHeight: 1.3
                        }}
                      >
                        {faq.question}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      sx={{
                        color: 'text.primary',
                        lineHeight: 1.6,
                        fontStyle: 'italic',
                        position: 'relative',
                        pl: { xs: 0, md: 6 }
                      }}
                    >
                      {faq.answer}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Container>
        </Box>
      </Fade>

      {/* Témoignages clients intégrés */}
      <Fade in={isPageLoaded} timeout={2000}>
        <Box sx={{ mb: 8 }}>
          <Container maxWidth="lg">
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontWeight: 800,
                color: COLORS.primary,
                mb: 6,
                textAlign: 'center',
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}
            >
              ⭐ Témoignages Clients - {formatServiceName(service!)} à {formatCityName(ville!)}
            </Typography>

            <Grid container spacing={4}>
              {[
                {
                  name: "Marie L.",
                  city: formatCityName(ville!),
                  service: formatServiceName(service!),
                  rating: 5,
                  comment: `Excellent service de ${formatServiceName(service!)} trouvé sur JobPartiel ! Le système Jobi m'a permis d'échanger mes compétences en ménage contre du jardinage. Révolutionnaire et économique !`,
                  date: "Il y a 2 semaines",
                  verified: true,
                  avatar: "👩‍💼"
                },
                {
                  name: "Pierre M.",
                  city: formatCityName(ville!),
                  service: formatServiceName(service!),
                  rating: 5,
                  comment: `JobPartiel change la donne ! Jobbeur vérifié, tarifs transparents, et j'ai payé en Jobi que j'avais gagnés en publiant des missions. Fini les commissions cachées des autres plateformes !`,
                  date: "Il y a 1 mois",
                  verified: true,
                  avatar: "👨‍💻"
                },
                {
                  name: "Sophie D.",
                  city: formatCityName(ville!),
                  service: formatServiceName(service!),
                  rating: 5,
                  comment: `Plateforme française de confiance ! Le jobbeur était parfait pour mon ${formatServiceName(service!)}. Support client réactif et système Jobi génial pour l'économie collaborative.`,
                  date: "Il y a 3 semaines",
                  verified: true,
                  avatar: "👩‍🎨"
                },
                {
                  name: "Thomas R.",
                  city: formatCityName(ville!),
                  service: formatServiceName(service!),
                  rating: 5,
                  comment: `Après avoir testé Frizbiz et TaskRabbit, JobPartiel est de loin le meilleur ! Pas de commission cachée, jobbeurs vérifiés, et le système Jobi est une innovation fantastique.`,
                  date: "Il y a 1 semaine",
                  verified: true,
                  avatar: "👨‍🔧"
                }
              ].map((testimonial, index) => (
                <Grid size={{ xs: 12, md: 6 }} key={index}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      height: '100%',
                      background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.success}05 100%)`,
                      border: `2px solid ${COLORS.success}20`,
                      borderRadius: 4,
                      position: 'relative',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        borderColor: COLORS.success,
                        transform: 'translateY(-5px)',
                        boxShadow: `0 10px 30px ${COLORS.success}15`
                      }
                    }}
                  >
                    {/* Badge vérifié */}
                    {testimonial.verified && (
                      <Chip
                        label="✓ Avis vérifié"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          bgcolor: `${COLORS.success}15`,
                          color: COLORS.success,
                          fontWeight: 600,
                          fontSize: '0.7rem'
                        }}
                      />
                    )}

                    {/* Header avec avatar et infos */}
                    <Box sx={{
                      display: 'flex',
                      alignItems: { xs: 'center', md: 'center' },
                      gap: { xs: 2, md: 2 },
                      flexDirection: { xs: 'column', md: 'row' },
                      mb: 3,
                      textAlign: { xs: 'center', md: 'left' } // Center text on mobile, left on desktop
                    }}>
                      <Typography variant="h3" sx={{ fontSize: '2.5rem' }}>
                        {testimonial.avatar}
                      </Typography>
                      <Box sx={{ flex: 1, textAlign: { xs: 'center', md: 'left' } }}> {/* Center text on mobile within this box too */}
                        <Typography variant="h6" sx={{ fontWeight: 700, color: COLORS.primary }}>
                          {testimonial.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {testimonial.service} • {testimonial.city}
                        </Typography>
                        
                        {/* Étoiles et date - optimisé mobile */}
                        <Box sx={{ mt: 1 }}>
                          {/* Étoiles sur une ligne */}
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: { xs: 'center', md: 'flex-start' }, mb: { xs: 1, md: 0 } }}>
                            {[...Array(testimonial.rating)].map((_, i) => (
                              <Star key={i} sx={{ fontSize: 16, color: COLORS.warning }} />
                            ))}
                          </Box>
                          
                          {/* Date sur une ligne séparée sur mobile */}
                          <Box sx={{ 
                            display: 'flex', 
                            justifyContent: { xs: 'center', md: 'flex-start' },
                            mt: { xs: 0.5, md: 0 }
                          }}>
                            <Typography variant="caption" color="text.secondary">
                              {testimonial.date}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Box>

                    {/* Commentaire */}
                    <Typography
                      variant="body1"
                      sx={{
                        color: 'text.primary',
                        lineHeight: 1.6,
                        fontStyle: 'italic',
                        position: 'relative',
                      }}
                    >
                      {testimonial.comment}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>

            {/* CTA pour laisser un avis */}
            <Box sx={{ textAlign: 'center', mt: 6 }}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  background: `linear-gradient(135deg, ${COLORS.primary}10 0%, ${COLORS.secondary}10 100%)`,
                  border: `2px solid ${COLORS.primary}20`,
                  borderRadius: 4,
                  maxWidth: '600px',
                  mx: 'auto'
                }}
              >
                <Typography variant="h5" sx={{ fontWeight: 700, color: COLORS.primary, mb: 2 }}>
                  💬 Partagez votre expérience !
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.primary', mb: 3 }}>
                  Vous avez utilisé JobPartiel pour {formatServiceName(service!)} à {formatCityName(ville!)} ?
                  Aidez la communauté en partageant votre avis et gagnez des Jobi !
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/dashboard')}
                  sx={{
                    bgcolor: COLORS.primary,
                    color: COLORS.white,
                    borderRadius: 3,
                    px: 4,
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 600,
                    '&:hover': {
                      bgcolor: COLORS.secondary,
                      transform: 'translateY(-2px)',
                      boxShadow: `0 8px 25px ${COLORS.primary}30`
                    }
                  }}
                >
                  ⭐ Laisser un avis
                </Button>
              </Paper>
            </Box>
          </Container>
        </Box>
      </Fade>

      {/* Call to action moderne et attractif */}
      <Fade in={isPageLoaded} timeout={2000}>
        <Box
          sx={{
            position: 'relative',
            textAlign: 'center',
            py: 8,
            px: 4,
            background: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
            borderRadius: 6,
            overflow: 'hidden',
            color: COLORS.white
          }}
        >
          {/* Éléments décoratifs de fond */}
          <Box
            sx={{
              position: 'absolute',
              top: -100,
              left: -100,
              width: 300,
              height: 300,
              background: `linear-gradient(45deg, ${COLORS.white}10, transparent)`,
              borderRadius: '50%',
              filter: 'blur(40px)'
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -150,
              right: -150,
              width: 400,
              height: 400,
              background: `linear-gradient(45deg, ${COLORS.accent}20, transparent)`,
              borderRadius: '50%',
              filter: 'blur(60px)'
            }}
          />

          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 800,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' }
              }}
            >
              Prêt à trouver votre jobbeur ?
            </Typography>
            <Typography
              variant="h5"
              sx={{
                mb: 4,
                opacity: 0.9,
                fontWeight: 500,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              Postez votre mission gratuitement et recevez des propositions de jobbeurs qualifiés en {formatServiceName(service!)} à {formatCityName(ville!)}
            </Typography>

            <Stack
              direction={isMobile ? "column" : "row"}
              spacing={3}
              justifyContent="center"
              sx={{ mb: 4 }}
            >
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/dashboard/missions/poster-une-mission')}
                sx={{
                  bgcolor: COLORS.white,
                  color: COLORS.primary,
                  borderRadius: 4,
                  px: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 700,
                  boxShadow: `0 8px 25px ${COLORS.shadow}`,
                  '&:hover': {
                    bgcolor: COLORS.white,
                    transform: 'translateY(-3px)',
                    boxShadow: `0 12px 35px ${COLORS.shadow}`
                  }
                }}
              >
                🚀 Poster une mission
              </Button>

              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/services')}
                sx={{
                  borderColor: COLORS.white,
                  color: COLORS.white,
                  borderRadius: 4,
                  px: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderWidth: 2,
                  '&:hover': {
                    borderColor: COLORS.white,
                    bgcolor: `${COLORS.white}15`,
                    transform: 'translateY(-3px)',
                    borderWidth: 2
                  }
                }}
              >
                Tous les services
              </Button>
            </Stack>

            {/* Stats finales */}
            <Grid container spacing={3} justifyContent="center" sx={{ maxWidth: '600px', mx: 'auto' }}>
              {[
                { value: "100%", label: "Gratuit" },
                { value: "< 24h", label: "Première réponse" },
                { value: "5★", label: "Satisfaction client" }
              ].map((stat, index) => (
                <Grid size={{ xs: 12, sm: 4 }} key={index}>
                  <Box textAlign="center">
                    <Typography variant="h4" sx={{ fontWeight: 800, mb: 0.5, fontSize: { xs: '1.8rem', md: '2rem' } }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8, fontWeight: 500, fontSize: { xs: '1rem', md: '0.875rem' } }}>
                      {stat.label}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      </Fade>
      </Container>
    </Box>
    </>
  );
};

export default ServiceCityPage;
