// Fonction pour définir un cookie
export const setCookie = (name: string, value: string, maxAge?: number, secure: boolean = false) => {
  const cookieOptions = [
    `${name}=${encodeURIComponent(value)}`,
    'path=/',
    maxAge ? `max-age=${maxAge}` : '',
    maxAge ? `expires=${new Date(Date.now() + maxAge * 1000).toUTCString()}` : '',
    'SameSite=Strict',
    secure ? 'Secure' : ''
  ].filter(Boolean).join('; ');

  document.cookie = cookieOptions;
};

// Fonction pour obtenir la valeur d'un cookie
export const getCookie = (name: string): string | null => {
  const cookies = document.cookie.split('; ');
  for (let cookie of cookies) {
    const [cookieName, ...cookieValue] = cookie.split('=');
    if (cookieName === name) {
      return decodeURIComponent(cookieValue.join('=')); // Gère les valeurs contenant '='
    }
  }
  return null;
};

// Fonction pour supprimer un cookie
export const removeCookie = (name: string, secure: boolean = false, domain?: string) => {
  let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Strict`;
  
  if (secure) {
    cookieString += '; Secure';
  }
  
  if (domain) {
    cookieString += `; Domain=${domain}`;
  }
  
  document.cookie = cookieString;
};

// Alias pour compatibilité
export const deleteCookie = removeCookie;
