import React, { useRef, createRef } from 'react';
import { Box, Grid, Skeleton, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { Mission } from './missionsApi';
import MissionCard, { MissionCardRef } from './MissionCard';

// Composant pour la ligne de connexion entre les missions
const MissionConnector: React.FC<{ isLastItem: boolean }> = ({ isLastItem }) => {
  if (isLastItem) return null;
  
  return (
    <Box sx={{ 
      position: 'relative', 
      height: '120px', 
      width: '100%', 
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      my: 0,
      '@keyframes pulse': {
        '0%': {
          opacity: 0.7,
        },
        '50%': {
          opacity: 1,
        },
        '100%': {
          opacity: 0.7,
        },
      },
      '@media (min-width: 768px)': {
        height: '70px',
      },
    }}>
      {/* <PERSON>gne pointillée verticale */}
      <Box sx={{
        position: 'absolute',
        top: '0',
        height: '100%',
        width: '2px',
        background: 'linear-gradient(to bottom, #FF6B2C 50%, transparent 50%)',
        backgroundSize: '4px 4px',
        opacity: 0.7,
        animation: 'pulse 2s infinite'
      }} />
      
      {/* Cercle central */}
      <Box sx={{
        position: 'absolute',
        top: '50%',
        transform: 'translateY(-50%)',
        width: '28px',
        height: '28px',
        borderRadius: '50%',
        backgroundColor: '#FFF8F3',
        border: '2px solid #FF6B2C',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        boxShadow: '0 0 0 4px rgba(255, 107, 44, 0.1)',
        zIndex: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-50%) scale(1.1)',
          boxShadow: '0 0 0 6px rgba(255, 107, 44, 0.15)'
        }
      }}>
        <Box sx={{
          width: '10px',
          height: '10px',
          borderRadius: '50%',
          backgroundColor: '#FF6B2C',
          animation: 'pulse 2s infinite'
        }} />
      </Box>
      
      {/* Petits cercles décoratifs */}
      <Box sx={{
        position: 'absolute',
        top: '25%',
        left: 'calc(50% + 20px)',
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: '#FFE4BA',
        border: '1px solid #FF6B2C',
        opacity: 0.5
      }} />
      
      <Box sx={{
        position: 'absolute',
        top: '70%',
        left: 'calc(50% - 25px)',
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        backgroundColor: '#FFE4BA',
        border: '1px solid #FF6B2C',
        opacity: 0.3
      }} />
    </Box>
  );
};

const NoMissionsBox = styled(Box)({
  textAlign: 'center',
  padding: '48px',
  backgroundColor: 'white',
  borderRadius: '16px',
  border: '2px dashed #FFE4BA',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
    borderRadius: '16px 16px 0 0',
  },
});

interface MissionsLayoutProps {
  missions: Mission[];
  loading: boolean;
  emptyMessage: string | React.ReactNode;
  emptyDescription?: string;
  emptyStateComponent?: React.ReactNode;
  isOwner?: boolean;
  onUpdate: (mission: Mission) => void;
  onReject?: (missionId: string) => Promise<void>;
  headerContent?: React.ReactNode;
  showRejectedOverlay?: boolean;
  footerContent?: React.ReactNode;
  highlightedMissionId?: string | null;
  showStatus?: boolean;
  onMakeProposal: (mission: Mission, missionCardRef?: React.RefObject<MissionCardRef | null>) => void;
}

const MissionsLayout: React.FC<MissionsLayoutProps> = ({
  missions,
  loading,
  emptyMessage,
  emptyDescription,
  emptyStateComponent,
  isOwner = false,
  onUpdate,
  onReject,
  headerContent,
  showRejectedOverlay = false,
  footerContent,
  highlightedMissionId,
  showStatus = true,
  onMakeProposal
}) => {
  // Créer un ref pour chaque MissionCard
  const missionRefs = useRef<{ [key: string]: React.RefObject<MissionCardRef | null> }>({});
  
  // Initialiser les refs pour chaque mission
  missions.forEach(mission => {
    if (!missionRefs.current[mission.id]) {
      missionRefs.current[mission.id] = createRef<MissionCardRef | null>();
    }
  });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {[1, 2, 3].map((i) => (
          <Skeleton 
            key={i} 
            variant="rectangular" 
            height={200} 
            sx={{ 
              borderRadius: '16px',
              backgroundColor: '#FFF8F3',
              '&::after': {
                background: 'linear-gradient(90deg, #FFF8F3, #FFE4BA, #FFF8F3)',
              },
            }} 
          />
        ))}
      </Box>
    );
  }

  return (
    <Box>
      {headerContent}

      {missions.length === 0 ? (
        <motion.div
          // initial={{ opacity: 0, y: 20 }}
          // animate={{ opacity: 1, y: 0 }}
          // transition={{ duration: 0.3 }}
          style={{ 
            willChange: 'transform, opacity',
            transform: 'translateZ(0)'
          }}
        >
          {emptyStateComponent || (
            <NoMissionsBox>
              <Typography variant="h6" color="textSecondary" gutterBottom>
                {emptyMessage}
              </Typography>
              {emptyDescription && (
                <Typography variant="body2" color="textSecondary">
                  {emptyDescription}
                </Typography>
              )}
            </NoMissionsBox>
          )}
        </motion.div>
      ) : (
        <>
          <Grid container spacing={1} sx={{ width: '100%', margin: 0, padding: 0 }}>
            {missions.map((mission, index) => (
              <React.Fragment key={mission.id}>
                <Grid size={12} sx={{ padding: 0 }}>
                  <motion.div
                    initial={mission.id === highlightedMissionId ? { scale: 0.95, opacity: 0 } : false}
                    animate={mission.id === highlightedMissionId ? 
                      { scale: 1, opacity: 1 } : 
                      false
                    }
                    transition={{ 
                      duration: 0.3,
                      type: "tween",
                      ease: "easeOut"
                    }}
                    style={{ 
                      willChange: 'transform, opacity',
                      transform: 'translateZ(0)'
                    }}
                  >
                    <MissionCard 
                      ref={missionRefs.current[mission.id]}
                      mission={mission}
                      isOwner={isOwner}
                      onUpdate={onUpdate}
                      onReject={onReject}
                      showRejectedOverlay={showRejectedOverlay}
                      isHighlighted={mission.id === highlightedMissionId}
                      showStatus={showStatus}
                      onMakeProposal={() => onMakeProposal(mission, missionRefs.current[mission.id])}
                    />
                  </motion.div>
                </Grid>
                {/* Ajouter le connecteur entre les missions sauf pour la dernière */}
                <Grid size={12} sx={{ 
                  padding: 0, 
                  display: { xs: 'block', md: 'block' } // Visible uniquement sur mobile
                }}>
                  <MissionConnector isLastItem={index === missions.length - 1} />
                </Grid>
              </React.Fragment>
            ))}
          </Grid>
          {footerContent}
        </>
      )}
    </Box>
  );
};

export default MissionsLayout; 