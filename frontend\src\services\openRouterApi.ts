import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { notify } from '../components/Notification';
import logger from '../utils/logger';

// Interface pour les informations de l'API OpenRouter
export interface OpenRouterInfo {
  data: {
    label?: string;
    usage?: number; // Nombre de crédits utilisés
    limit?: number | null; // Limite de crédits pour la clé, ou null si illimitée
    is_free_tier?: boolean; // Si l'utilisateur a déjà payé pour des crédits
    rate_limit?: {
      requests?: number; // Nombre de requêtes autorisées...
      interval?: string; // dans cet intervalle, ex: "10s"
    };
  };
  source?: string; // Source des données (cache ou API)
}

// Interface pour les crédits OpenRouter
export interface OpenRouterCredits {
  data: {
    total_credits?: number; // Total des crédits achetés
    total_usage?: number; // Total des crédits utilisés
  };
  source?: string; // Source des données (cache ou API)
}

// Interface pour les appels quotidiens
export interface DailyCallsInfo {
  data: {
    count: number; // Nombre d'appels effectués aujourd'hui
    date: string; // Date du jour
  };
}

// Interface pour les informations sur les modèles
export interface ModelsInfo {
  data: {
    free_model: string; // Modèle gratuit utilisé pour le texte
    paid_model: string; // Modèle payant utilisé pour le texte
    vision_free_model: string; // Modèle gratuit utilisé pour les images
    vision_paid_model: string; // Modèle payant utilisé pour les images
    daily_calls_limit: number; // Limite d'appels quotidiens
  };
}

/**
 * Récupère les informations sur la clé API OpenRouter
 * (crédits restants, limites, etc.)
 */
export const getOpenRouterInfo = async (): Promise<OpenRouterInfo | null> => {
  try {
    const response = await axios.get(
      `${API_CONFIG.baseURL}/api/openrouter/info`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      }
    );

    if (response.data.success) {
      return response.data;
    } else {
      notify('Erreur lors de la récupération des informations OpenRouter', 'error');
      return null;
    }
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des informations OpenRouter', {
      error: error.message,
      stack: error.stack
    });

    notify('Erreur lors de la récupération des informations OpenRouter', 'error');
    return null;
  }
};

/**
 * Récupère les crédits OpenRouter (total et utilisés)
 */
export const getOpenRouterCredits = async (): Promise<OpenRouterCredits | null> => {
  try {
    const response = await axios.get(
      `${API_CONFIG.baseURL}/api/openrouter/credits`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      }
    );

    if (response.data.success) {
      return response.data;
    } else {
      notify('Erreur lors de la récupération des crédits OpenRouter', 'error');
      return null;
    }
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des crédits OpenRouter', {
      error: error.message,
      stack: error.stack
    });

    notify('Erreur lors de la récupération des crédits OpenRouter', 'error');
    return null;
  }
};

/**
 * Récupère les informations sur les modèles utilisés
 */
export const getModelsInfo = async (): Promise<ModelsInfo | null> => {
  try {
    const response = await axios.get(
      `${API_CONFIG.baseURL}/api/openrouter/models`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      }
    );

    if (response.data.success) {
      return response.data;
    } else {
      notify('Erreur lors de la récupération des informations sur les modèles', 'error');
      return null;
    }
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des informations sur les modèles', {
      error: error.message,
      stack: error.stack
    });

    notify('Erreur lors de la récupération des informations sur les modèles', 'error');
    return null;
  }
};

/**
 * Vide le cache des appels quotidiens pour forcer une nouvelle requête
 * Cela garantit que les deux endpoints utilisent exactement la même valeur
 */
export const clearDailyCallsCache = async (): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${API_CONFIG.baseURL}/api/openrouter/stats/clear-daily-calls-cache`,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      }
    );

    if (response.data.success) {
      notify('Cache des appels quotidiens vidé avec succès', 'success');
      return true;
    } else {
      notify('Erreur lors de la suppression du cache des appels quotidiens', 'error');
      return false;
    }
  } catch (error: any) {
    logger.error('Erreur lors de la suppression du cache des appels quotidiens', {
      error: error.message,
      stack: error.stack
    });

    notify('Erreur lors de la suppression du cache des appels quotidiens', 'error');
    return false;
  }
};

/**
 * Récupère le nombre exact de requêtes du jour (heure locale) via l'endpoint dédié
 */
export const getDailyRequestCount = async (): Promise<number> => {
  try {
    const response = await axios.get(
      `${API_CONFIG.baseURL}/api/openrouter/stats/daily-count`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      }
    );
    if (response.data.success && typeof response.data.count === 'number') {
      return response.data.count;
    }
    notify('Erreur lors de la récupération du nombre de requêtes du jour', 'error');
    return 0;
  } catch (error: any) {
    logger.error('Erreur lors de la récupération du nombre de requêtes du jour', {
      error: error.message,
      stack: error.stack
    });
    notify('Erreur lors de la récupération du nombre de requêtes du jour', 'error');
    return 0;
  }
};
