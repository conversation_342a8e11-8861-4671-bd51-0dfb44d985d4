import React, { useRef } from 'react';
import { Document as DocType } from '../../../services/billingService';
import { ArrowLeft, Printer, Download } from 'lucide-react';

interface DocumentPreviewProps {
  document: DocType;
  onBack: () => void;
  onDownload: () => void;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({ document, onBack, onDownload }) => {
  const printRef = useRef<HTMLDivElement>(null);

  // Formater les dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Imprimer le document
  const handlePrint = () => {
    window.print();
  };

  // Obtenir le titre du document
  const getDocumentTitle = () => {
    switch (document.type) {
      case 'devis': return 'Devis';
      case 'facture': return 'Facture';
      case 'avoir': return 'Avoir';
      default: return 'Document';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Barre d'actions (ne s'imprime pas) */}
      <div className="p-6 border-b border-gray-200 print:hidden">
        <div className="flex justify-between items-center">
          <button
            onClick={onBack}
            className="text-gray-600 hover:text-gray-900 flex items-center"
          >
            <ArrowLeft size={16} className="mr-1" />
            Retour à l'édition
          </button>

          <div className="flex space-x-3">
            <button
              onClick={handlePrint}
              className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] inline-flex items-center"
            >
              <Printer size={16} className="mr-2" />
              Imprimer
            </button>
            <button
              onClick={onDownload}
              className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 inline-flex items-center"
            >
              <Download size={16} className="mr-2" />
              Télécharger PDF
            </button>
          </div>
        </div>
      </div>

      {/* Document à imprimer */}
      <div 
        ref={printRef} 
        className="p-8 max-w-4xl mx-auto my-6 bg-white"
      >
        {/* Votre entreprise */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Votre Entreprise</h1>
            <p className="text-gray-600 mt-1">123 rue des Entrepreneurs</p>
            <p className="text-gray-600">75000 Paris</p>
            <p className="text-gray-600 mt-1"><EMAIL></p>
            <p className="text-gray-600">+33 1 23 45 67 89</p>
            <p className="text-gray-600 mt-1">SIRET: 123 456 789 00010</p>
            <p className="text-gray-600">TVA: FR12 123 456 789</p>
          </div>
          
          <div className="text-right">
            <h2 className="text-xl font-bold text-[#FF7A35]">
              {getDocumentTitle()} {document.number}
            </h2>
            <p className="text-gray-600 mt-1">
              Date: {formatDate(document.date_creation)}
            </p>
            {document.date_validite && (
              <p className="text-gray-600">
                Valable jusqu'au: {formatDate(document.date_validite)}
              </p>
            )}
            <p className="mt-2 inline-block px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
              {document.statut}
            </p>
          </div>
        </div>

        {/* Informations du client */}
        <div className="border-t border-b border-gray-200 py-4 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-gray-600 mb-1">Facturé à:</h3>
              <p className="font-semibold text-gray-800">{document.client_name}</p>
              {document.client_address && (
                <p className="text-gray-600">{document.client_address}</p>
              )}
              {document.client_email && (
                <p className="text-gray-600 mt-1">{document.client_email}</p>
              )}
              {document.client_phone && (
                <p className="text-gray-600">{document.client_phone}</p>
              )}
              {document.forme_juridique && (
                <p className="text-gray-600 mt-1">Forme juridique: {document.forme_juridique as string}</p>
              )}
              {document.code_ape && (
                <p className="text-gray-600">Code APE: {document.code_ape as string}</p>
              )}
              {document.client_siret && (
                <p className="text-gray-600 mt-1">SIRET: {document.client_siret}</p>
              )}
              {document.client_tva && (
                <p className="text-gray-600">TVA: {document.client_tva}</p>
              )}
            </div>
            
            {document.description && (
              <div className="max-w-xs text-right">
                <h3 className="text-gray-600 mb-1">Description:</h3>
                <p className="text-gray-800 whitespace-pre-line">{document.description}</p>
              </div>
            )}
          </div>
        </div>

        {/* Tableau des articles */}
        <table className="w-full border-collapse mb-6">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-4 py-2 border-b border-gray-200 text-left text-gray-600">Description</th>
              <th className="px-4 py-2 border-b border-gray-200 text-right text-gray-600">Qté</th>
              <th className="px-4 py-2 border-b border-gray-200 text-right text-gray-600">Prix unitaire</th>
              <th className="px-4 py-2 border-b border-gray-200 text-right text-gray-600">TVA</th>
              <th className="px-4 py-2 border-b border-gray-200 text-right text-gray-600">Total HT</th>
            </tr>
          </thead>
          <tbody>
            {document.invoice_items && document.invoice_items.map((item, index) => (
              <tr key={item.id || index} className="border-b border-gray-200">
                <td className="px-4 py-3 text-gray-800 whitespace-pre-line">{item.description}</td>
                <td className="px-4 py-3 text-right text-gray-800">
                  {item.quantite} {item.unite}
                </td>
                <td className="px-4 py-3 text-right text-gray-800">
                  {item.prix_unitaire.toFixed(2)} €
                </td>
                <td className="px-4 py-3 text-right text-gray-800">
                  {item.taux_tva}%
                </td>
                <td className="px-4 py-3 text-right text-gray-800">
                  {item.montant_ht.toFixed(2)} €
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Totaux */}
        <div className="flex justify-end mb-8">
          <div className="w-64">
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="text-gray-600">Total HT:</span>
              <span className="font-medium">{document.total_ht !== undefined ? document.total_ht.toFixed(2) : '0.00'} €</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="text-gray-600">Total TVA:</span>
              <span className="font-medium">{document.total_tva !== undefined ? document.total_tva.toFixed(2) : '0.00'} €</span>
            </div>
            <div className="flex justify-between py-2 border-b-2 border-gray-200">
              <span className="font-bold">Total TTC:</span>
              <span className="font-bold text-[#FF7A35]">{document.total_ttc !== undefined ? document.total_ttc.toFixed(2) : '0.00'} €</span>
            </div>
          </div>
        </div>

        {/* Informations de paiement */}
        {(document.mode_paiement || document.conditions_paiement) && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-800 mb-2">Informations de paiement</h3>
            <div className="grid grid-cols-2 gap-4">
              {document.mode_paiement && (
                <div>
                  <p className="text-gray-600">Mode de paiement:</p>
                  <p className="text-gray-800">{document.mode_paiement}</p>
                </div>
              )}
              {document.conditions_paiement && (
                <div>
                  <p className="text-gray-600">Conditions de paiement:</p>
                  <p className="text-gray-800">{document.conditions_paiement}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Mentions légales */}
        {(document.mentions_legales || document.mentions_tva || document.penalite_retard || document.indemnite_recouvrement) && (
          <div className="border-t border-gray-200 pt-4 text-sm text-gray-600">
            <h3 className="font-semibold text-gray-800 mb-2">Mentions légales</h3>
            {document.mentions_legales && <p className="mb-1">{document.mentions_legales}</p>}
            {document.mentions_tva && <p className="mb-1">{document.mentions_tva}</p>}
            {document.penalite_retard && <p className="mb-1">Pénalités de retard: {document.penalite_retard}</p>}
            {document.indemnite_recouvrement && <p className="mb-1">Indemnité forfaitaire de recouvrement: {document.indemnite_recouvrement}</p>}
          </div>
        )}

        {/* Notes */}
        {document.notes && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <h3 className="font-semibold text-gray-800 mb-2">Notes</h3>
            <p className="text-gray-800 whitespace-pre-line">{document.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentPreview; 