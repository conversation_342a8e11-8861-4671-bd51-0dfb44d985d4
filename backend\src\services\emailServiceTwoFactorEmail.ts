import logger from '../utils/logger';
import {
  EmailServiceError,
  sendEmailWithRetry as queueEmail
} from './emailService';
import config from '../config';

// Constantes pour les sujets et icônes d'emails
const EMAIL_ICONS = {
  twoFactorEnabled: '🔐',
  twoFactorDisabled: '🔓',
  twoFactorAuth: '🔒',
  twoFactorVerified: '✅'
};

const EMAIL_SUBJECTS = {
  twoFactorEnabled: 'Authentification à deux facteurs activée',
  twoFactorDisabled: 'Authentification à deux facteurs désactivée',
  twoFactorAuth: 'Code de vérification pour votre connexion',
  twoFactorVerified: 'Authentification à deux facteurs vérifiée avec succès'
};

// Fonction pour valider le format de l'email
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  // Vérifier le format de base
  if (!emailRegex.test(email)) {
    return false;
  }
  
  // Exclure les emails anonymisés pour le RGPD
  if (email.includes('@supprime.local')) {
    return false;
  }
  
  return true;
};

// Messages d'erreur constants
const EMAIL_ERRORS = {
  INVALID_EMAIL: {
    code: 'INVALID_EMAIL',
    message: (email: string) => `L'adresse email '${email}' n'est pas valide.`
  }
};

// Fonction pour envoyer un email de confirmation d'activation de l'authentification à deux facteurs
export const sendTwoFactorEnabledEmail = async (data: {
  email: string;
  firstName?: string;
  lastName?: string;
  ip?: string;
  userAgent?: string;
  location?: string;
}) => {
  try {
    if (!isValidEmail(data.email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(data.email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: data.email }
      );
    }

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const securitySettingsUrl = `${baseUrl}/dashboard/profil/securite`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: data.email,
      subject: `${EMAIL_ICONS.twoFactorEnabled} ${EMAIL_SUBJECTS.twoFactorEnabled}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://jobpartiel.fr/logo.png" alt="JobPartiel Logo" style="max-width: 150px;">
          </div>
          <h2 style="color: #FF7A35; text-align: center;">Authentification à deux facteurs activée</h2>
          <p>Bonjour ${data.firstName || 'utilisateur'},</p>
          <p>Nous vous confirmons que l'authentification à deux facteurs a été activée avec succès sur votre compte JobPartiel.</p>

          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0;"><strong>Qu'est-ce que cela signifie ?</strong></p>
            <p style="margin: 10px 0 0 0;">À chaque connexion, vous recevrez désormais un code de vérification par email pour confirmer votre identité. Cette mesure supplémentaire renforce considérablement la sécurité de votre compte.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; font-size: 14px;"><strong>Détails de l'activation :</strong></p>
            <p style="margin: 5px 0; font-size: 14px;">Date et heure : ${new Date().toLocaleString('fr-FR')}</p>
            ${data.ip ? `<p style="margin: 5px 0; font-size: 14px;">Adresse IP : ${data.ip}</p>` : ''}
            ${data.location ? `<p style="margin: 5px 0; font-size: 14px;">Localisation : ${data.location}</p>` : ''}
            ${data.userAgent ? `<p style="margin: 5px 0; font-size: 14px;">Appareil : ${data.userAgent}</p>` : ''}
          </div>

          <p style="margin-top: 20px;">Si vous n'êtes pas à l'origine de cette activation, veuillez immédiatement :</p>
          <ol>
            <li>Vous connecter à votre compte</li>
            <li>Accéder aux paramètres de sécurité</li>
            <li>Désactiver l'authentification à deux facteurs</li>
            <li>Changer votre mot de passe</li>
          </ol>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${securitySettingsUrl}" style="background-color: #FF7A35; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; word-break: break-word; max-width: 100%; text-align: center;">Gérer mes paramètres de sécurité</a>
          </div>

          <p>Cordialement,<br>L'équipe JobPartiel</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #888;">
            <p>Si vous ne parvenez pas à cliquer sur le bouton, copiez et collez l'URL suivante dans votre navigateur :</p>
            <p style="word-break: break-all;">${securitySettingsUrl}</p>
            <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions);
    logger.info('Email de confirmation d\'activation de l\'authentification à deux facteurs envoyé avec succès', { to: data.email });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation d\'activation de l\'authentification à deux facteurs', { error, email: data.email });
    throw error;
  }
};

// Fonction pour envoyer un email de confirmation de désactivation de l'authentification à deux facteurs
export const sendTwoFactorDisabledEmail = async (data: {
  email: string;
  firstName?: string;
  lastName?: string;
  ip?: string;
  userAgent?: string;
  location?: string;
}) => {
  try {
    if (!isValidEmail(data.email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(data.email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: data.email }
      );
    }

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const securitySettingsUrl = `${baseUrl}/dashboard/profil/securite`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: data.email,
      subject: `${EMAIL_ICONS.twoFactorDisabled} ${EMAIL_SUBJECTS.twoFactorDisabled}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://jobpartiel.fr/logo.png" alt="JobPartiel Logo" style="max-width: 150px;">
          </div>
          <h2 style="color: #FF7A35; text-align: center;">Authentification à deux facteurs désactivée</h2>
          <p>Bonjour ${data.firstName || 'utilisateur'},</p>
          <p>Nous vous informons que l'authentification à deux facteurs a été désactivée sur votre compte JobPartiel.</p>

          <div style="background-color: #fff8f3; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="margin: 0;"><strong>Attention :</strong></p>
            <p style="margin: 10px 0 0 0;">Votre compte est maintenant moins sécurisé. Nous vous recommandons vivement de réactiver cette fonctionnalité pour une protection optimale.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; font-size: 14px;"><strong>Détails de la désactivation :</strong></p>
            <p style="margin: 5px 0; font-size: 14px;">Date et heure : ${new Date().toLocaleString('fr-FR')}</p>
            ${data.ip ? `<p style="margin: 5px 0; font-size: 14px;">Adresse IP : ${data.ip}</p>` : ''}
            ${data.location ? `<p style="margin: 5px 0; font-size: 14px;">Localisation : ${data.location}</p>` : ''}
            ${data.userAgent ? `<p style="margin: 5px 0; font-size: 14px;">Appareil : ${data.userAgent}</p>` : ''}
          </div>

          <p style="margin-top: 20px;">Si vous n'êtes pas à l'origine de cette désactivation, il est possible que votre compte ait été compromis. Nous vous recommandons de :</p>
          <ol>
            <li>Changer immédiatement votre mot de passe</li>
            <li>Réactiver l'authentification à deux facteurs</li>
            <li>Vérifier les activités récentes sur votre compte</li>
            <li>Contacter notre support si vous constatez des activités suspectes</li>
          </ol>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${securitySettingsUrl}" style="background-color: #FF7A35; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; word-break: break-word; max-width: 100%; text-align: center;">Gérer mes paramètres de sécurité</a>
          </div>

          <p>Cordialement,<br>L'équipe JobPartiel</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #888;">
            <p>Si vous ne parvenez pas à cliquer sur le bouton, copiez et collez l'URL suivante dans votre navigateur :</p>
            <p style="word-break: break-all;">${securitySettingsUrl}</p>
            <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      `
    };

    await queueEmail(mailOptions);
    logger.info('Email de confirmation de désactivation de l\'authentification à deux facteurs envoyé avec succès', { to: data.email });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation de désactivation de l\'authentification à deux facteurs', { error, email: data.email });
    throw error;
  }
};

// Fonction pour envoyer un email de confirmation de vérification de l'authentification à deux facteurs
export const sendTwoFactorVerifiedEmail = async (data: {
  email: string;
  firstName?: string;
  lastName?: string;
  ip?: string;
  userAgent?: string;
  location?: string;
}) => {
  try {
    if (!isValidEmail(data.email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(data.email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: data.email }
      );
    }

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const securitySettingsUrl = `${baseUrl}/dashboard/profil/securite`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: data.email,
      subject: `${EMAIL_ICONS.twoFactorVerified} ${EMAIL_SUBJECTS.twoFactorVerified}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://jobpartiel.fr/logo.png" alt="Job Partiel" style="max-width: 150px;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Authentification à deux facteurs activée</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour ${data.firstName || 'utilisateur'},
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Nous vous confirmons que l'authentification à deux facteurs a été activée et vérifiée avec succès sur votre compte JobPartiel. Votre compte est maintenant mieux protégé contre les accès non autorisés.
          </p>

          <div style="background-color: #f0f7ff; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #4285F4;">
            <h3 style="color: #4285F4; margin-top: 0;">Conseils de sécurité</h3>
            <ul style="padding-left: 20px; margin-bottom: 0;">
              <li style="margin-bottom: 10px;">Lors de vos prochaines connexions, vous devrez saisir un code de vérification envoyé à votre adresse email.</li>
              <li style="margin-bottom: 10px;">Ne partagez jamais vos codes de vérification avec qui que ce soit, même s'ils prétendent faire partie de l'équipe JobPartiel.</li>
              <li style="margin-bottom: 10px;">Assurez-vous que votre adresse email est sécurisée avec un mot de passe fort.</li>
              <li style="margin-bottom: 10px;">Si vous changez d'adresse email, n'oubliez pas de mettre à jour vos paramètres d'authentification à deux facteurs.</li>
              <li>Si vous recevez un code de vérification sans avoir tenté de vous connecter, cela peut indiquer que quelqu'un essaie d'accéder à votre compte. Changez immédiatement votre mot de passe.</li>
            </ul>
          </div>

          <p style="font-size: 16px;">
            Vous pouvez gérer vos paramètres de sécurité à tout moment depuis votre <a href="${securitySettingsUrl}" style="color: #FF7A35; text-decoration: none; font-weight: bold;">page de paramètres de sécurité</a>.
          </p>

          <div style="background-color: #fff8f3; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="margin: 0;"><strong>Important :</strong></p>
            <p style="margin: 10px 0 0 0;">Si vous n'avez pas activé l'authentification à deux facteurs sur votre compte, veuillez nous contacter immédiatement en contactant notre support.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; font-size: 14px;"><strong>Détails de l'activation :</strong></p>
            <p style="margin: 5px 0; font-size: 14px;">Date et heure : ${new Date().toLocaleString('fr-FR')}</p>
            ${data.ip ? `<p style="margin: 5px 0; font-size: 14px;">Adresse IP : ${data.ip}</p>` : ''}
            ${data.location ? `<p style="margin: 5px 0; font-size: 14px;">Localisation : ${data.location}</p>` : ''}
            ${data.userAgent ? `<p style="margin: 5px 0; font-size: 14px;">Appareil : ${data.userAgent}</p>` : ''}
          </div>

          <p>Cordialement,<br>L'équipe JobPartiel</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #888;">
            <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      </div>
      `
    };

    await queueEmail(mailOptions);
    logger.info('Email de confirmation de vérification de l\'authentification à deux facteurs envoyé avec succès', { to: data.email });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email de confirmation de vérification de l\'authentification à deux facteurs', { error, email: data.email });
    throw error;
  }
};

// Fonction pour envoyer un email avec le code de vérification pour l'authentification à deux facteurs
export const sendTwoFactorVerificationEmail = async (data: {
  email: string;
  firstName?: string;
  lastName?: string;
  token: string;
  twoFactorLink?: string;
  ip?: string;
  userAgent?: string;
  location?: string;
}) => {
  try {
    if (!isValidEmail(data.email)) {
      throw new EmailServiceError(
        EMAIL_ERRORS.INVALID_EMAIL.message(data.email),
        EMAIL_ERRORS.INVALID_EMAIL.code,
        { email: data.email }
      );
    }

    const twoFactorLink = data.twoFactorLink ||
      `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-two-factor?token=${data.token}`;

    const mailOptions = {
      from: `"Job Partiel" <${process.env.SMTP_FROM}>` || '<EMAIL>',
      to: data.email,
      subject: `${EMAIL_ICONS.twoFactorAuth} ${EMAIL_SUBJECTS.twoFactorAuth}`,
      html: `
      <div style="font-family: 'Arial', sans-serif; width: 100%; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%); padding: 40px 0;">
        <div style="background-color: white; padding: 40px 20px; border-radius: 16px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="https://jobpartiel.fr/logo.png" alt="Job Partiel" style="max-width: 150px;">
          </div>

          <h2 style="color: #FF7A35; text-align: center; font-size: 28px; margin-bottom: 25px;">Code de vérification</h2>

          <p style="line-height: 1.8; color: #374151; font-size: 16px;">
            Bonjour ${data.firstName || 'utilisateur'},
          </p>

          <p style="font-size: 16px; margin-top: 15px;">
            Vous avez demandé à vous connecter à votre compte Job Partiel. Pour finaliser votre connexion, veuillez utiliser le code de vérification ci-dessous :
          </p>

          <div style="text-align: center; margin: 25px 0;">
            <div style="
              display: inline-block;
              background-color: #f8f9fa;
              padding: 15px 30px;
              border-radius: 8px;
              font-size: 32px;
              font-weight: bold;
              letter-spacing: 5px;
              color: #FF7A35;
              border: 2px solid #FFE4BA;
            ">${data.token}</div>
          </div>

          <div style="background-color: #fff8f3; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #FF7A35;">
            <p style="margin: 0;"><strong>Important :</strong></p>
            <p style="margin: 10px 0 0 0;">Ce code est valable pendant 10 minutes. Si vous n'avez pas demandé ce code, veuillez ignorer cet email et sécuriser votre compte.</p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; font-size: 14px;"><strong>Détails de la demande :</strong></p>
            <p style="margin: 5px 0; font-size: 14px;">Date et heure : ${new Date().toLocaleString('fr-FR')}</p>
            ${data.ip ? `<p style="margin: 5px 0; font-size: 14px;">Adresse IP : ${data.ip}</p>` : ''}
            ${data.location ? `<p style="margin: 5px 0; font-size: 14px;">Localisation : ${data.location}</p>` : ''}
            ${data.userAgent ? `<p style="margin: 5px 0; font-size: 14px;">Appareil : ${data.userAgent}</p>` : ''}
          </div>

          <p>Cordialement,<br>L'équipe JobPartiel</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; font-size: 12px; color: #888;">
            <p>© ${new Date().getFullYear()} JobPartiel. Tous droits réservés.</p>
          </div>
        </div>
      </div>
      `
    };

    await queueEmail(mailOptions);
    logger.info('Email avec code de vérification pour l\'authentification à deux facteurs envoyé avec succès', { to: data.email });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de l\'email avec code de vérification pour l\'authentification à deux facteurs', { error, email: data.email });
    throw error;
  }
};