import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const inputDir = path.join(__dirname, '../public/images');
const outputDir = path.join(__dirname, '../public/images');

// Configuration de compression
const COMPRESSION_LEVELS = {
  low: { quality: 70, effort: 4 },     // Petite taille, qualité réduite
  medium: { quality: 80, effort: 4 },  // Bon équilibre qualité/taille
  high: { quality: 90, effort: 9 }     // Haute qualité, plus grande taille
};

// Niveau de compression par défaut
const DEFAULT_COMPRESSION = 'low';

function convertToWebP(inputFile, outputFile, compressionLevel = DEFAULT_COMPRESSION) {
  const config = COMPRESSION_LEVELS[compressionLevel];
  
  sharp(inputFile)
    .webp({ 
      quality: config.quality, 
      effort: config.effort 
    })
    .toFile(outputFile)
    .then(() => {
      console.info(`Converted ${inputFile} to WebP (${compressionLevel} quality)`);
    })
    .catch((error) => {
      console.error(`Error converting ${inputFile}:`, error);
    });
}

function processDirectory(dir, compressionLevel = DEFAULT_COMPRESSION, convertedFiles = new Set()) {
  fs.readdir(dir, { withFileTypes: true }, (err, entries) => {
    if (err) {
      console.error('Error reading directory:', err);
      return;
    }

    entries.forEach(entry => {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        // Traitement récursif des sous-dossiers
        processDirectory(fullPath, compressionLevel, convertedFiles);
      } else {
        const inputFile = fullPath;
        const outputFile = path.join(outputDir, path.relative(inputDir, fullPath).replace(/\.[^.]+$/, '.webp'));
        
        // Check if file is an image and hasn't been converted yet
        if (['.jpg', '.jpeg', '.png'].includes(path.extname(entry.name).toLowerCase()) && 
            !convertedFiles.has(inputFile)) {
          convertToWebP(inputFile, outputFile, compressionLevel);
          convertedFiles.add(inputFile);
        }
      }
    });
  });
}

// Permet de spécifier un niveau de compression via une variable d'environnement
const compressionLevel = process.env.IMAGE_COMPRESSION || DEFAULT_COMPRESSION;

// Créer un ensemble global pour suivre les fichiers convertis
const convertedFiles = new Set();

processDirectory(inputDir, compressionLevel, convertedFiles);

// Aussi pour le sous-dossier services
const servicesDir = path.join(inputDir, 'services');
processDirectory(servicesDir, compressionLevel, convertedFiles);
