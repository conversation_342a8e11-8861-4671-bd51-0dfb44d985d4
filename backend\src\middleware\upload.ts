import { Request, Response, NextFunction } from 'express';
import { UploadedFile } from 'express-fileupload';
import logger from '../utils/logger';

// Types
export interface FileRequest extends Request {
  files?: {
    [key: string]: UploadedFile | UploadedFile[];
  } | null;
}

// Middleware de validation des fichiers pour les messages
export const validateMessageAttachments = (req: FileRequest, res: Response, next: NextFunction) => {
  // Si pas de fichier, on continue simplement
  if (!req.files) {
    return next();
  }

  // Fonction de validation pour un seul fichier
  const validateSingleFile = (file: UploadedFile) => {
    // Types MIME autorisés
    const allowedMimes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (!allowedMimes.includes(file.mimetype)) {
      return {
        isValid: false,
        message: 'Type de fichier non autorisé'
      };
    }

    // Vérification de la taille (5MB)
    if (file.size > 5 * 1024 * 1024) {
      return {
        isValid: false,
        message: 'La taille du fichier ne doit pas dépasser 5MB'
      };
    }

    return { isValid: true };
  };

  // Récupérer les fichiers
  const files = req.files.files;
  if (Array.isArray(files)) {
    // Vérifier le nombre maximum de fichiers
    if (files.length > 5) {
      res.status(400).json({
        success: false,
        message: 'Maximum 5 fichiers autorisés'
      });
    }

    // Vérifier chaque fichier
    for (const file of files) {
      const validation = validateSingleFile(file);
      if (!validation.isValid) {
        res.status(400).json({
          success: false,
          message: validation.message
        });
      }
    }
  } else if (files) {
    // Vérifier un fichier unique
    const validation = validateSingleFile(files);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        message: validation.message
      });
    }
  }

  next();
}; 