import React, { useState } from 'react';
import axios from 'axios';
import { notify } from '@/components/Notification';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { CompanyInfo } from '@/types/company';
import logger from '@/utils/logger';
import ModalPortal from '@/components/ModalPortal';
import { Building2, User, AlertCircle, Info, Clock } from 'lucide-react';
import { useCreateNotification } from '../../../hooks/useCreateNotification';

const statutJuridiqueMap: { [key: string]: string } = {
  '1000': 'Entrepreneur individuel',
  '1100': 'Artisan-commerçant',
  '1200': 'Commerçant',
  '1300': 'Artisan',
  '1400': 'Officier public ou ministériel',
  '1500': 'Profession libérale',
  '1600': 'Exploitant agricole',
  '1700': 'Agent commercial',
  '1800': 'Asso<PERSON><PERSON> gérant de Société',
  '1900': '(Autre) Personne physique',
  '2110': 'Indivision entre personnes physiques',
  '2120': 'Indivision avec personne morale',
  '2210': 'Société créée de fait entre personnes physiques',
  '2220': 'Société créée de fait avec personne morale',
  '2310': 'Société en participation entre personnes physiques',
  '2320': 'Société en participation avec personne morale',
  '2385': 'Société en participation de professions libérales',
  '2400': 'Fiducie',
  '2700': 'Paroisse hors zone concordataire',
  '2900': 'Autre groupement de droit privé non doté de la personnalité morale',
  '5191': 'Société de caution mutuelle',
  '5192': 'Société coopérative de banque populaire',
  '5193': 'Caisse de crédit maritime mutuel',
  '5194': 'Caisse (fédérale) de crédit mutuel',
  '5195': 'Association coopérative inscrite (droit local Alsace Moselle)',
  '5196': 'Caisse d\'épargne et de prévoyance à forme coopérative',
  '5202': 'Société en nom collectif',
  '5203': 'Société en nom collectif coopérative',
  '5306': 'Société en commandite simple',
  '5307': 'Société en commandite simple coopérative',
  '5308': 'Société en commandite par actions',
  '5309': 'Société en commandite par actions coopérative',
  '5385': 'Société d\'exercice libéral en commandite par actions',
  '5410': 'SARL nationale',
  '5415': 'SARL d\'économie mixte',
  '5422': 'SARL immobilière pour le commerce et l\'industrie (SICOMI)',
  '5426': 'SARL immobilière de gestion',
  '5430': 'SARL d\'aménagement foncier et d\'équipement rural (SAFER)',
  '5431': 'SARL mixte d\'intérêt agricole (SMIA)',
  '5432': 'SARL d\'intérêt collectif agricole (SICA)',
  '5442': 'SARL d\'attribution',
  '5443': 'SARL coopérative de construction',
  '5451': 'SARL coopérative de consommation',
  '5453': 'SARL coopérative artisanale',
  '5454': 'SARL coopérative d\'intérêt maritime',
  '5455': 'SARL coopérative de transport',
  '5458': 'SARL coopérative ouvrière de production (SCOP)',
  '5459': 'SARL union de sociétés coopératives',
  '5460': 'Autre SARL coopérative',
  '5485': 'Société d\'exercice libéral à responsabilité limitée',
  '5498': 'SARL unipersonnelle',
  '5499': 'Société à responsabilité limitée (sans autre indication)',
  '5510': 'SA nationale à conseil d\'administration',
  '5515': 'SA d\'économie mixte à conseil d\'administration',
  '5520': 'Fonds à forme sociétale à conseil d\'administration',
  '5522': 'SA immobilière pour le commerce et l\'industrie (SICOMI)',
  '5525': 'SA immobilière d\'investissement',
  '5530': 'SA d\'aménagement foncier et d\'équipement rural (SAFER)',
  '5531': 'Société anonyme mixte d\'intérêt agricole (SMIA)',
  '5542': 'SA d\'attribution',
  '5543': 'SA coopérative de construction',
  '5546': 'SA de HLM',
  '5547': 'SA coopérative de production de HLM',
  '5548': 'SA de crédit immobilier',
  '5551': 'SA coopérative de consommation',
  '5552': 'SA coopérative de commerçants-détaillants',
  '5553': 'SA coopérative artisanale',
  '5554': 'SA coopérative d\'intérêt maritime',
  '5555': 'SA coopérative de transport',
  '5558': 'SA coopérative ouvrière de production (SCOP)',
  '5559': 'SA union de sociétés coopératives',
  '5560': 'Autre SA coopérative',
  '5585': 'Société d\'exercice libéral à forme anonyme',
  '5599': 'SA (sans autre indication)',
  '5605': 'SA à participation ouvrière à directoire',
  '5610': 'SA nationale à directoire',
  '5615': 'SA d\'économie mixte à directoire',
  '5620': 'Fonds à forme sociétale à directoire',
  '5632': 'SA d\'intérêt collectif agricole (SICA)',
  '5642': 'SA d\'attribution à directoire',
  '5643': 'SA coopérative de construction à directoire',
  '5646': 'SA de HLM à directoire',
  '5647': 'Société coopérative de production de HLM anonyme à directoire',
  '5648': 'SA de crédit immobilier à directoire',
  '5651': 'SA coopérative de consommation à directoire',
  '5652': 'SA coopérative de commerçants-détaillants à directoire',
  '5653': 'SA coopérative artisanale à directoire',
  '5654': 'SA coopérative d\'intérêt maritime à directoire',
  '5655': 'SA coopérative de transport à directoire',
  '5658': 'SA coopérative ouvrière de production (SCOP) à directoire',
  '5659': 'SA union de sociétés coopératives à directoire',
  '5660': 'Autre SA coopérative à directoire',
  '5685': 'Société d\'exercice libéral à forme anonyme à directoire',
  '5699': 'SA à directoire (sans autre indication)',
  '5710': 'SAS, société par actions simplifiée',
  '5720': 'Société par actions simplifiée à associé unique (SASU)',
  '5785': 'Société d\'exercice libéral par action simplifiée',
  '5800': 'Société européenne',
  '6100': 'Caisse d\'Épargne et de Prévoyance',
  '6210': 'Groupement européen d\'intérêt économique (GEIE)',
  '6220': 'Groupement d\'intérêt économique (GIE)',
  '6316': 'Coopérative d\'utilisation de matériel agricole en commun (CUMA)',
  '6317': 'Société coopérative agricole',
  '6318': 'Union de sociétés coopératives agricoles',
  '6411': 'Société d\'assurance à forme mutuelle',
  '6511': 'Sociétés Interprofessionnelles de Soins Ambulatoires',
  '6521': 'Société civile de placement collectif immobilier (SCPI)',
  '6532': 'Société civile d\'intérêt collectif agricole (SICA)',
  '6533': 'Groupement agricole d\'exploitation en commun (GAEC)',
  '6534': 'Groupement foncier agricole',
  '6535': 'Groupement agricole foncier',
  '6536': 'Groupement forestier',
  '6537': 'Groupement pastoral',
  '6538': 'Groupement foncier et rural',
  '6539': 'Société civile foncière',
  '6540': 'Société civile immobilière',
  '6541': 'Société civile immobilière de construction-vente',
  '6542': 'Société civile d\'attribution',
  '6543': 'Société civile coopérative de construction',
  '6544': 'Société civile immobilière d\'accession progressive à la propriété',
  '6551': 'Société civile coopérative de consommation',
  '6554': 'Société civile coopérative d\'intérêt maritime',
  '6558': 'Société civile coopérative entre médecins',
  '6560': 'Autre société civile coopérative',
  '6561': 'SCP d\'avocats',
  '6562': 'SCP d\'avocats aux conseils',
  '6563': 'SCP d\'avoués d\'appel',
  '6564': 'SCP d\'huissiers',
  '6565': 'SCP de notaires',
  '6566': 'SCP de commissaires-priseurs',
  '6567': 'SCP de greffiers de tribunal de commerce',
  '6568': 'SCP de conseils juridiques',
  '6569': 'SCP de commissaires aux comptes',
  '6571': 'SCP de médecins',
  '6572': 'SCP de dentistes',
  '6573': 'SCP d\'infirmiers',
  '6574': 'SCP de masseurs-kinésithérapeutes',
  '6575': 'SCP de directeurs de laboratoire d\'analyse médicale',
  '6576': 'SCP de vétérinaires',
  '6577': 'SCP de géomètres experts',
  '6578': 'SCP d\'architectes',
  '6585': 'Autre société civile professionnelle',
  '6588': 'Société civile laitière',
  '6589': 'Société civile de moyens',
  '6595': 'Caisse locale de crédit mutuel',
  '6596': 'Caisse de crédit agricole mutuel',
  '6597': 'Société civile d\'exploitation agricole',
  '6598': 'Exploitation agricole à responsabilité limitée',
  '6599': 'Autre société civile',
  '6901': 'Autre personne de droit privé inscrite au RCS'
};

// Fonction pour formater le SIREN avec des espaces
const formatSIREN = (siren: string): string => {
  return siren.replace(/(\d{3})(?=\d)/g, '$1 ').trim();
};

interface Props {
  initialData?: CompanyInfo;
  onUpdate: (data: CompanyInfo) => void;
  lastProfilUpdate?: string; // Date de la dernière mise à jour du profil
  isOwnProfil?: boolean; // Ajout de la prop isOwnProfil
}

export const InformationsEntreprise: React.FC<Props> = ({ initialData, onUpdate, isOwnProfil = true }) => {
  const { createSystemNotification } = useCreateNotification();
  const [companyData, setCompanyData] = useState<CompanyInfo>(initialData || {
    type_de_profil: '',
    siren_entreprise: '',
    nom_entreprise: '',
    prenom_entreprise: '',
    statut_entreprise: '',
    code_ape_entreprise: '',
    categorie_entreprise: '',
    date_insee_creation_entreprise: '',
    date_categorie_entreprise: '',
    date_derniere_mise_a_jour_entreprise_insee: '',
    date_derniere_mise_a_jour_du_client_entreprise: '',
    effectif_entreprise: ''
  });

  const [tempData, setTempData] = useState<CompanyInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isConfirmingSiren, setIsConfirmingSiren] = useState(false);
  const [isConfirmationChecked, setIsConfirmationChecked] = useState(false);
  const [isConfirmingProfilType, setIsConfirmingProfilType] = useState(false);

  const handleProfilTypeChange = async (type: 'particulier' | 'entreprise') => {
    // Si on passe à particulier, pas de vérification de délai
    if (type === 'particulier') {
      setIsConfirmingProfilType(true);
      setTempData({
        ...companyData,
        type_de_profil: type,
        siren_entreprise: '',
        nom_entreprise: '',
        prenom_entreprise: '',
        statut_entreprise: '',
        code_ape_entreprise: '',
        categorie_entreprise: '',
        effectif_entreprise: '',
        date_insee_creation_entreprise: '',
        date_categorie_entreprise: '',
        date_derniere_mise_a_jour_entreprise_insee: '',
        date_derniere_mise_a_jour_du_client_entreprise: new Date().toISOString()
      });
      return;
    }

    // Vérification du délai pour passer à professionnel
    if (initialData?.date_derniere_mise_a_jour_du_client_entreprise) {
      const lastUpdate = new Date(initialData.date_derniere_mise_a_jour_du_client_entreprise);
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      if (lastUpdate > sixMonthsAgo) {
        const daysRemaining = Math.ceil((lastUpdate.getTime() - sixMonthsAgo.getTime()) / (1000 * 60 * 60 * 24));
        notify(`Vous devez attendre encore ${daysRemaining} jours avant de pouvoir passer à un profil professionnel. En cas de besoin urgent, contactez le support.`, 'error');
        return;
      }
    }

    setIsConfirmingProfilType(true);
    setTempData({
      ...companyData,
      type_de_profil: type,
      date_derniere_mise_a_jour_du_client_entreprise: new Date().toISOString()
    });
  };

  const searchSIREN = async (siren: string) => {
    try {
      logger.info('Recherche du SIREN frontend : ', siren);

      setIsLoading(true);
      const response = await axios.get(`${API_CONFIG.baseURL}/api/sirene/siren/${siren}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });

      if (response.data.success) {
        const apiData = response.data.data;
        
        // Récupérer la période la plus récente (première période du tableau)
        const periodeCourante = apiData.uniteLegale?.periodesUniteLegale?.[0] || {};
        
        // Récupérer le code juridique et son libellé
        const codeJuridique = periodeCourante.categorieJuridiqueUniteLegale?.toString() || '';
        const libelleJuridique = statutJuridiqueMap[codeJuridique] || `Code ${codeJuridique}`;
        
        // Créer un nouvel objet avec les données formatées
        const formattedData: CompanyInfo = {
          type_de_profil: 'entreprise',
          siren_entreprise: formatSIREN(siren),
          nom_entreprise: apiData.uniteLegale?.denominationUniteLegale || 
                         periodeCourante.denominationUniteLegale || 
                         periodeCourante.nomUniteLegale ||
                         apiData.uniteLegale?.nomUniteLegale || '',
          prenom_entreprise: apiData.uniteLegale?.prenom1UniteLegale || '',
          statut_entreprise: libelleJuridique,
          code_ape_entreprise: periodeCourante.activitePrincipaleUniteLegale || '',
          categorie_entreprise: apiData.uniteLegale?.categorieEntreprise || '',
          effectif_entreprise: apiData.uniteLegale?.trancheEffectifsUniteLegale || '',
          date_insee_creation_entreprise: apiData.uniteLegale?.dateCreationUniteLegale || '',
          date_categorie_entreprise: apiData.uniteLegale?.anneeCategorieEntreprise ? `${apiData.uniteLegale.anneeCategorieEntreprise}-01-01` : '',
          date_derniere_mise_a_jour_entreprise_insee: apiData.uniteLegale?.dateDernierTraitementUniteLegale || '',
          date_derniere_mise_a_jour_du_client_entreprise: new Date().toISOString()
        };

        logger.info('Données formatées de l\'API SIRENE:', formattedData);
        setTempData(formattedData);
        setIsConfirmingSiren(true);
        notify('Informations SIRENE trouvées avec succès', 'success');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erreur lors de la recherche du SIREN';
      notify(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSirenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Supprimer tous les caractères non numériques et les espaces
    const value = e.target.value.replace(/[^\d]/g, '').slice(0, 9);
    // Formater avec des espaces pour l'affichage
    setCompanyData(prev => ({ ...prev, siren_entreprise: formatSIREN(value) }));
    
    if (value.length === 9) {
      // Pour la recherche, utiliser le SIREN sans espaces
      searchSIREN(value);
    }
  };

  const handleSubmit = async () => {
    if (!tempData) {
      notify('Aucune donnée à enregistrer', 'error');
      return;
    }

    if (!isConfirmationChecked) {
      notify('Veuillez cocher la case de confirmation', 'error');
      return;
    }

    try {
      // Préparer les données selon le type de profil
      let updatedData: CompanyInfo & { } = {
        ...tempData
      };

      // On réinitialise les vérifications au changement de statut
      updatedData = {
        type_de_profil: tempData.type_de_profil,
        siren_entreprise: tempData.siren_entreprise,
        nom_entreprise: tempData.nom_entreprise,
        prenom_entreprise: tempData.prenom_entreprise,
        statut_entreprise: tempData.statut_entreprise,
        code_ape_entreprise: tempData.code_ape_entreprise,
        categorie_entreprise: tempData.categorie_entreprise,
        effectif_entreprise: tempData.effectif_entreprise,
        date_insee_creation_entreprise: tempData.date_insee_creation_entreprise,
        date_categorie_entreprise: tempData.date_categorie_entreprise,
        date_derniere_mise_a_jour_entreprise_insee: tempData.date_derniere_mise_a_jour_entreprise_insee,
        date_derniere_mise_a_jour_du_client_entreprise: tempData.date_derniere_mise_a_jour_du_client_entreprise
      };

      // Envoyer toutes les données formatées
      await onUpdate(updatedData);
      setCompanyData({ ...companyData, ...updatedData });
      setTempData(null);
      setIsConfirmingSiren(false);
      setIsConfirmingProfilType(false);
      setIsConfirmationChecked(false);

      // Message spécifique selon le type de changement
      if (updatedData.type_de_profil === 'particulier') {
        notify('Votre profil a été changé en particulier. Vous pourrez passer en statut professionnel après 6 mois. En cas de problème, contactez-nous.', 'success');
        await createSystemNotification(
          'Profil changé',
          'Votre profil a été changé en particulier. Vous pourrez passer en statut professionnel après 6 mois. En cas de problème, contactez-nous.',
          `/dashboard/profil`
        );
      } else {
        notify('Votre profil a été changé en professionnel. Vous devez maintenant faire vérifier votre profil afin de rassurer les clients.', 'success');
        await createSystemNotification(
          'Profil changé',
          'Votre profil a été changé en professionnel. Vous devez maintenant faire vérifier votre profil afin de rassurer les clients.',
          `/dashboard/profil`
        );
      }
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du profil:', error);
      notify('Erreur lors de la mise à jour du profil', 'error');
    }
  };

  const canModifySiren = !initialData?.siren_entreprise;
  const canChangeProfilType = !initialData?.type_de_profil;

  return (
    <div className="bg-[#FFF8F3] rounded-xl p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Informations profil</h3>
      <div className="space-y-4">
        {/* Sélection du type de profil */}
        {canChangeProfilType ? (
          <div>
            <label className="block text-sm font-medium text-gray-600 mb-3">
              Type de profil
            </label>
            {isOwnProfil ? (
              <>
                {!companyData.type_de_profil && (
                  <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
                      <div className="text-sm text-red-700">
                        <p className="font-medium">Sélection requise</p>
                        <p>Veuillez choisir votre type de profil pour continuer. Ce choix déterminera les informations nécessaires pour compléter votre profil.</p>
                      </div>
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button
                    onClick={() => handleProfilTypeChange('particulier')}
                    className={`flex items-center justify-center gap-3 p-4 rounded-lg border-2 transition-all duration-300 ${
                      companyData.type_de_profil === 'particulier'
                        ? 'border-[#FF6B2C] bg-[#FFF8F3]'
                        : 'border-gray-200 hover:border-[#FF6B2C] hover:bg-[#FFF8F3]'
                    }`}
                  >
                    <User className={`h-6 w-6 ${companyData.type_de_profil === 'particulier' ? 'text-[#FF6B2C]' : 'text-gray-400'}`} />
                    <span className={`font-medium ${companyData.type_de_profil === 'particulier' ? 'text-[#FF6B2C]' : 'text-gray-600'}`}>
                      Particulier
                    </span>
                  </button>
                  <button
                    onClick={() => handleProfilTypeChange('entreprise')}
                    className={`flex items-center justify-center gap-3 p-4 rounded-lg border-2 transition-all duration-300 ${
                      companyData.type_de_profil === 'entreprise'
                        ? 'border-[#FF6B2C] bg-[#FFF8F3]'
                        : 'border-gray-200 hover:border-[#FF6B2C] hover:bg-[#FFF8F3]'
                    }`}
                  >
                    <Building2 className={`h-6 w-6 ${companyData.type_de_profil === 'entreprise' ? 'text-[#FF6B2C]' : 'text-gray-400'}`} />
                    <span className={`font-medium ${companyData.type_de_profil === 'entreprise' ? 'text-[#FF6B2C]' : 'text-gray-600'}`}>
                      Entreprise
                    </span>
                  </button>
                </div>
              </>
            ) : (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <div className="flex items-start gap-2">
                  {companyData.type_de_profil ? (
                    <>
                      {companyData.type_de_profil === 'particulier' ? (
                        <User className="h-5 w-5 text-gray-400 shrink-0 mt-0.5" />
                      ) : (
                        <Building2 className="h-5 w-5 text-gray-400 shrink-0 mt-0.5" />
                      )}
                      <p className="text-sm text-gray-600">
                        {companyData.type_de_profil === 'particulier' ? 'Profil Particulier' : 'Profil Entreprise'}
                      </p>
                    </>
                  ) : (
                    <>
                      <Info className="h-5 w-5 text-gray-400 shrink-0 mt-0.5" />
                      <p className="text-sm text-gray-600">
                        Cette partie du profil n'a pas encore été complétée par l'utilisateur.
                      </p>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-4 bg-white rounded-lg">
              {companyData.type_de_profil === 'particulier' ? (
                <>
                  <User className="h-6 w-6 text-[#FF6B2C]" />
                  <span className="text-sm text-gray-600">Profil Particulier</span>
                </>
              ) : (
                <>
                  <Building2 className="h-6 w-6 text-[#FF6B2C]" />
                  <span className="text-sm font-medium text-gray-800">Profil Entreprise</span>
                </>
              )}
            </div>
            {companyData.type_de_profil === 'particulier' && initialData?.date_derniere_mise_a_jour_du_client_entreprise && isOwnProfil && (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Clock className="h-5 w-5 text-gray-500 shrink-0 mt-0.5" />
                    <div className="text-sm text-gray-600">
                      {(() => {
                        const lastUpdate = new Date(initialData.date_derniere_mise_a_jour_du_client_entreprise);
                        const nextPossibleUpdate = new Date(lastUpdate);
                        nextPossibleUpdate.setMonth(nextPossibleUpdate.getMonth() + 6);
                        const today = new Date();
                        const daysRemaining = Math.ceil((nextPossibleUpdate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

                        if (daysRemaining <= 0) {
                          return (
                            <p>
                              <span className="text-sm">
                                Vous pouvez maintenant passer à un profil professionnel quand vous le souhaitez. 
                              </span>
                              <span className="block mt-1 text-xs text-gray-500">
                                Le statut professionnel vous permettra de bénéficier de nouvelles fonctionnalités.
                              </span>
                            </p>
                          );
                        }

                        return (
                          <p>
                            <span className="text-">
                              Changement à un profil professionnel possible à partir du{' '}
                              <span className="font-medium">{nextPossibleUpdate.toLocaleDateString()} ({daysRemaining} jours restants)</span>
                              <span className="block mt-1 text-xs text-gray-500">
                                Ce délai de sécurité est mis en place pour protéger nos utilisateurs contre la fraude à l'usurpation d'entreprise. Si vous avez besoin d'un accès plus rapide, contactez notre support avec vos justificatifs d'entreprise.
                              </span>
                            </span>
                          </p>
                        );
                      })()}
                    </div>
                  </div>
                </div>
                {(() => {
                  const lastUpdate = new Date(initialData.date_derniere_mise_a_jour_du_client_entreprise);
                  const nextPossibleUpdate = new Date(lastUpdate);
                  nextPossibleUpdate.setMonth(nextPossibleUpdate.getMonth() + 6);
                  const today = new Date();
                  const daysRemaining = Math.ceil((nextPossibleUpdate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

                  if (daysRemaining <= 0) {
                    return (
                      <button
                        onClick={() => handleProfilTypeChange('entreprise')}
                        className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                      >
                        <Building2 className="h-5 w-5" />
                        <span>Passer à un profil professionnel</span>
                      </button>
                    );
                  }
                  return null;
                })()}
              </div>
            )}
            {companyData.type_de_profil === 'entreprise' && isOwnProfil && (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-5 w-5 text-gray-500 shrink-0 mt-0.5" />
                    <div className="text-sm text-gray-600">
                      <p>
                        Vous pouvez passer à un profil particulier à tout moment.
                        <span className="block mt-1 text-xs text-gray-500">
                          Vos informations d'entreprise seront temporairement masquées et vous pourrez revenir en professionnel après un délai de 6 mois.
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => handleProfilTypeChange('particulier')}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <User className="h-5 w-5" />
                  <span>Passer à un profil particulier</span>
                </button>
              </div>
            )}
            {/* Affichage des données actuelles */}
            {!tempData && companyData.siren_entreprise && companyData.type_de_profil === 'entreprise' && (
              <div className="mt-4 space-y-2">
                <p><span className="font-medium">SIREN :</span> {companyData.siren_entreprise}</p>
                <p><span className="font-medium">Nom :</span> {companyData.nom_entreprise || '-'}</p>
                <p><span className="font-medium">Prénom :</span> {companyData.prenom_entreprise || '-'}</p>
                <p><span className="font-medium">Statut :</span> {companyData.statut_entreprise || '-'}</p>
                <p><span className="font-medium">Code APE :</span> {companyData.code_ape_entreprise || '-'}</p>
                <p><span className="font-medium">Catégorie :</span> {companyData.categorie_entreprise || '-'}</p>
                <p><span className="font-medium">Effectif :</span> {companyData.effectif_entreprise || '-'}</p>
                <p><span className="font-medium">Création entreprise :</span> {companyData.date_insee_creation_entreprise ? new Date(companyData.date_insee_creation_entreprise).toLocaleDateString() : '-'}</p>
                <p><span className="font-medium">Adjonction activité :</span> {companyData.date_categorie_entreprise ? new Date(companyData.date_categorie_entreprise).toLocaleDateString() : '-'}</p>
                <p><span className="font-medium">Dernière mise à jour INSEE :</span> {companyData.date_derniere_mise_a_jour_entreprise_insee ? new Date(companyData.date_derniere_mise_a_jour_entreprise_insee).toLocaleDateString() : '-'}</p>
                <p><span className="font-medium">Actualisé le :</span> {companyData.date_derniere_mise_a_jour_du_client_entreprise ? new Date(companyData.date_derniere_mise_a_jour_du_client_entreprise).toLocaleDateString() : '-'}</p>
              </div>
            )}
          </div>
        )}

        {/* SIREN input - uniquement affiché si type entreprise */}
        {(companyData.type_de_profil === 'entreprise' && canModifySiren) && (
          <div>
            <label className="block text-sm font-medium text-gray-600 mb-1">
              SIREN
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={companyData.siren_entreprise}
                onChange={handleSirenChange}
                className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF965E] focus:border-transparent"
                placeholder="Entrez votre SIREN (9 chiffres)"
              />
              {isLoading && (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#FF6B2C]"></div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Modal de confirmation du type de profil */}
      {isConfirmingProfilType && tempData && !isConfirmingSiren && (
        <ModalPortal>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Confirmer le type de profil</h3>
              <div className="space-y-4">
                <div className="bg-[#FFF8F3] p-4 rounded-lg">
                  <div className="flex items-center gap-3 mb-4">
                    {tempData.type_de_profil === 'particulier' ? (
                      <>
                        <User className="h-6 w-6 text-[#FF6B2C]" />
                        <span className="font-medium text-gray-800">Profil Particulier</span>
                      </>
                    ) : (
                      <>
                        <Building2 className="h-6 w-6 text-[#FF6B2C]" />
                        <span className="font-medium text-gray-800">Profil Entreprise</span>
                      </>
                    )}
                  </div>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">
                      {tempData.type_de_profil === 'particulier' 
                        ? "En tant que particulier, vous n'aurez pas besoin de fournir de SIREN ou d'informations d'entreprise. Vous pourrez continuer à utiliser JobPartiel normalement. Le délai de 6 mois entre les changements de statut est une mesure de sécurité pour protéger nos utilisateurs."
                        : "En tant qu'entreprise, vous pourrez fournir votre SIREN et les informations de votre entreprise. Vous aurez également la possibilité de faire vérifier votre profil pour gagner en visibilité auprès des utilisateurs de JobPartiel."}
                    </p>
                    {/* Avertissement pour le changement de type */}
                    <div className="flex items-start gap-2 p-3 bg-yellow-50 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-yellow-500 shrink-0 mt-0.5" />
                      <div className="text-sm text-yellow-700">
                        <p className="font-medium">Important :</p>
                        <ul className="list-disc ml-4 mt-1 space-y-1">
                          {tempData.type_de_profil === 'particulier' ? (
                            <>
                              <li>Un délai de 6 mois est requis avant de repasser en professionnel (mesure anti-fraude)</li>
                              <li>En cas d'erreur ou de besoin urgent, contactez le support pour accélérer le processus</li>
                              <li>Vos informations d'entreprise seront supprimées (SIREN, etc.)</li>
                            </>
                          ) : (
                            <>
                              <li>Vous pourrez renseigner vos informations d'entreprise (SIREN, etc.)</li>
                              <li>Pour plus de visibilité, vous pourrez faire vérifier votre profil par l'équipe JobPartiel</li>
                              <li>La vérification inclut : identité, entreprise et assurance professionnelle</li>
                              <li>Un badge "Profil Vérifié" sera affiché sur votre profil après vérification</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 flex items-start bg-red-100 p-3 rounded-lg">
                  <input
                    type="checkbox"
                    checked={isConfirmationChecked}
                    onChange={(e) => setIsConfirmationChecked(e.target.checked)}
                    className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md"
                  />
                  <div className="text-sm text-gray-600">
                    <p>Je confirme avoir lu et compris les implications de ce changement.</p>
                    <p className="mt-1 text-red-600 font-medium">
                      {tempData.type_de_profil === 'particulier'
                        ? "Le délai de 6 mois peut être réduit en contactant le support en cas de besoin."
                        : "Toutes vos vérifications devront être refaites si vous souhaitez un profil vérifié."}
                    </p>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      setIsConfirmingProfilType(false);
                      setTempData(null);
                      setIsConfirmationChecked(false);
                    }}
                    className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleSubmit}
                    className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                  >
                    Confirmer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modal de confirmation SIREN */}
      {isConfirmingSiren && tempData && (
        <ModalPortal>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Confirmer les informations de l'entreprise</h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500 mb-2">Veuillez vérifier attentivement les informations suivantes :</p>
                  <div className="bg-[#FFF8F3] p-4 rounded-lg space-y-2">
                    <p><span className="font-medium">SIREN :</span> {tempData.siren_entreprise}</p>
                    <p><span className="font-medium">Nom :</span> {tempData.nom_entreprise || '-'}</p>
                    <p><span className="font-medium">Prénom :</span> {tempData.prenom_entreprise || '-'}</p>
                    <p><span className="font-medium">Statut :</span> {tempData.statut_entreprise || '-'}</p>
                    <p><span className="font-medium">Code APE :</span> {tempData.code_ape_entreprise || '-'}</p>
                    <p><span className="font-medium">Catégorie :</span> {tempData.categorie_entreprise || '-'}</p>
                    <p><span className="font-medium">Effectif :</span> {tempData.effectif_entreprise || '-'}</p>
                  </div>
                </div>

                <div className="mt-4 flex items-start bg-red-100 p-3 rounded-lg">
                  <input
                    type="checkbox"
                    checked={isConfirmationChecked}
                    onChange={(e) => setIsConfirmationChecked(e.target.checked)}
                    className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md"
                  />
                  <div className="text-sm text-gray-600">
                    <p>Je confirme que ces informations sont correctes.</p>
                    <p className="mt-1 text-red-600 font-medium">
                      Attention : Une fois validé, le SIREN ne pourra être modifié que sur demande au support.
                    </p>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      setIsConfirmingSiren(false);
                      setTempData(null);
                      setIsConfirmationChecked(false);
                    }}
                    className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleSubmit}
                    className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                  >
                    Confirmer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
};