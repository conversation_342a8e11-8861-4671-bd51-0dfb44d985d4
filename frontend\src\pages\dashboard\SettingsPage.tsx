import logger from '@/utils/logger';
import { useState, useEffect } from 'react';
import DOMPurify from 'dompurify';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';
import { UserCog, Eye, EyeOff, Shield, Palmtree, Users, Share2, X, Mail, Bot } from 'lucide-react';
import { useRefresh } from './components/RefreshDashboard';
import { useNavigate } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { Typography, Box, Tabs, Tab } from '@mui/material';
import VerifierForceMotDePasse from '../auth/VerifierForceMotDePasse';
import ReferralListModal from './components/ReferralListModal';
import { setCookie, getCookie, removeCookie } from '../../utils/cookieUtils';
import ModalPortal from '../../components/ModalPortal';
import { motion } from 'framer-motion';
import EmailPreferencesSection from '../../components/settings/EmailPreferencesSection';
import AiPromptsSection from '../../components/settings/AiPromptsSection';
import { useSeoPromotionContext } from '../../components/SEOBanniere/SeoPromotionProvider';

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
  [theme.breakpoints.between('sm', 'md')]: {
    fontSize: '1.75rem',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '0.9rem',
  textTransform: 'none',
  minHeight: '48px',
  borderRadius: '8px 8px 0 0',
  marginRight: '4px',
  transition: 'all 0.2s ease',
  [theme.breakpoints.up('sm')]: {
    minWidth: '160px',
  },
  '&.Mui-selected': {
    color: '#FF6B2C',
    backgroundColor: 'rgba(255, 107, 44, 0.04)',
  },
  '&:hover:not(.Mui-selected)': {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    color: '#FF6B2C',
  }
}));

const StyledTabs = styled(Tabs)({
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF6B2C',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    borderBottom: '1px solid #E2E8F0',
  }
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const COOLDOWN_DURATION = 30; // 30 secondes de cooldown pour les actions sensibles
const EMAIL_CHANGE_COOLDOWN = 120; // 120 secondes (2 minutes) de cooldown pour le changement d'email

export default function SettingsPage() {
  const { refresh } = useRefresh();
  const navigate = useNavigate();
  const { refreshStats } = useSeoPromotionContext();
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    app: true
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [userType, setUserType] = useState<'jobbeur' | 'non-jobbeur'>('jobbeur');
  const [countdown, setCountdown] = useState<number>(0);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);

  const [notificationToggleDisabled] = useState<boolean>(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [isVacationMode, setIsVacationMode] = useState<boolean>(false);
  const [vacationCooldown, setVacationCooldown] = useState<boolean>(false);
  const [vacationCooldownTime, setVacationCooldownTime] = useState<number>(0);
  const [emailCooldown, setEmailCooldown] = useState<number>(0);
  const [emailButtonDisabled, setEmailButtonDisabled] = useState<boolean>(false);
  const [seoIndexable, setSeoIndexable] = useState<boolean>(false);
  const [seoLoading, setSeoLoading] = useState<boolean>(false);

  const [formData, setFormData] = useState({
    currentPassword: DOMPurify.sanitize(''),
    newPassword: DOMPurify.sanitize(''),
    confirmPassword: DOMPurify.sanitize('')
  });

  const [referralData, setReferralData] = useState({
    referralCode: '',
    referralLink: '',
    copied: false
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const [showReferralListModal, setShowReferralListModal] = useState(false);
  const [showLinkedInOptions, setShowLinkedInOptions] = useState(false);
  const [hasNewReferral, setHasNewReferral] = useState(false);
  const [showEmailPreferencesModal, setShowEmailPreferencesModal] = useState(false);

  const [showSocialOptions, setShowSocialOptions] = useState({
    facebook: false,
    twitter: false,
    linkedin: false,
    whatsapp: false
  });

  const [showEmailModal, setShowEmailModal] = useState<boolean>(false);
  const [emailFormData, setEmailFormData] = useState({
    newEmail: DOMPurify.sanitize(''),
    password: DOMPurify.sanitize('')
  });
  const [showEmailPassword, setShowEmailPassword] = useState<boolean>(false);

  const [currentTab, setCurrentTab] = useState(0);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        await fetchCsrfToken();

        // Récupérer les données du profil
        const response = await axios.get('/api/users/profil', API_CONFIG);
        if (response.data) {
          // logger.info('Récupération des données page paramètres :', response.data);

          const sanitizedUserType = DOMPurify.sanitize(response.data.user_type);
          setUserType(sanitizedUserType as 'jobbeur' | 'non-jobbeur');

          // Récupérer l'email de l'utilisateur
          if (response.data.email) {
            setUserEmail(DOMPurify.sanitize(response.data.email));
          }

          // Récupérer le mode vacances
          if (response.data.profil && response.data.profil.data && response.data.profil.data.mode_vacance !== undefined) {
            setIsVacationMode(response.data.profil.data.mode_vacance);
            // logger.info('Mode vacances récupéré:', response.data.profil.data.mode_vacance);
          }

          // Récupérer les préférences SEO
          if (response.data.profil && response.data.profil.data) {
            // Si seo_indexable existe, utiliser sa valeur, sinon utiliser false par défaut (RGPD)
            const seoValue = response.data.profil.data.seo_indexable !== undefined
              ? response.data.profil.data.seo_indexable
              : false;
            setSeoIndexable(seoValue);
            logger.info('Préférences SEO récupérées:', seoValue);
          } else {
            // Si pas de profil, utiliser false par défaut
            setSeoIndexable(false);
          }
        }

        // Récupérer les préférences email
        const headers = await getCommonHeaders();
        const emailPrefsResponse = await axios.get('/api/users/email-preferences', {
          ...API_CONFIG,
          headers: {
            ...API_CONFIG.headers,
            ...headers
          },
          withCredentials: true
        });

        if (emailPrefsResponse.data.success && emailPrefsResponse.data.preferences) {
          const emailEnabled = emailPrefsResponse.data.preferences.email_enabled !== undefined
            ? emailPrefsResponse.data.preferences.email_enabled
            : true;

          setNotifications(prev => ({
            ...prev,
            email: emailEnabled
          }));
          // logger.info('Préférences email récupérées:', emailPrefsResponse.data.preferences);
        } else {
          logger.error('Réponse invalide des préférences email:', emailPrefsResponse.data);
        }

      } catch (error) {
        logger.error('Erreur lors de la récupération des données utilisateur:', error);
        notify('Erreur lors de la récupération des données utilisateur', 'error');
      }
    };

    const fetchReferralCode = async () => {
      try {
        const response = await axios.get('/api/jobi/referral-code', API_CONFIG);
        if (response.data && response.data.success) {
          setReferralData({
            referralCode: response.data.referralCode,
            referralLink: response.data.referralLink,
            copied: false
          });

          // Vérifier s'il y a de nouveaux filleuls
          if (response.data.hasNewReferral) {
            setHasNewReferral(true);
          }
        }
      } catch (err) {
        logger.error('Erreur lors de la récupération du code de parrainage:', err);
      }
    };

    fetchUserData();
    fetchReferralCode();
  }, []);

  // Ajouter un gestionnaire de clic pour fermer le menu déroulant LinkedIn
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // Vérifier si le clic est en dehors du menu LinkedIn
      if (showLinkedInOptions && !target.closest('.linkedin-dropdown')) {
        setShowLinkedInOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showLinkedInOptions]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else {
      setIsButtonDisabled(false);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Vérifier le cooldown au chargement
  useEffect(() => {
    const storedCooldown = getCookie('emailChangeCooldown');
    if (storedCooldown) {
      const cooldownTime = parseInt(storedCooldown);
      const now = Date.now();
      if (cooldownTime > now) {
        setEmailCooldown(Math.ceil((cooldownTime - now) / 1000));
        setEmailButtonDisabled(true);
      } else {
        removeCookie('emailChangeCooldown');
        setEmailCooldown(0);
        setEmailButtonDisabled(false);
      }
    }
  }, []);

  // Mettre à jour le countdown toutes les secondes
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (emailCooldown > 0) {
      intervalId = setInterval(() => {
        const storedCooldown = getCookie('emailChangeCooldown');
        if (storedCooldown) {
          const cooldownTime = parseInt(storedCooldown);
          const now = Date.now();
          if (cooldownTime > now) {
            setEmailCooldown(Math.ceil((cooldownTime - now) / 1000));
            setEmailButtonDisabled(true);
          } else {
            removeCookie('emailChangeCooldown');
            setEmailCooldown(0);
            setEmailButtonDisabled(false);
          }
        }
      }, 1000);
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [emailCooldown]);

  const handleToggle = async () => {
    if (isButtonDisabled) return;

    try {
      setIsButtonDisabled(true);
      setCountdown(10);

      const newType = userType === 'jobbeur' ? 'non-jobbeur' : 'jobbeur';
      logger.info('Tentative de mise à jour du type utilisateur', { newType });

      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put('/api/users/updateType', {
        user_type: newType
      }, {
        ...API_CONFIG,
        headers
      });

      if (response.data.success) {
        setUserType(newType);
        refresh();
        notify(`Vous êtes maintenant ${newType === 'jobbeur' ? 'jobbeur' : 'non-jobbeur'}, le menu à été mis à jour afin de vous faciliter la navigation.`, 'success');
        logger.info('Tentative de mise à jour du type utilisateur effectuée avec succès', { newType });
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erreur lors du changement de type';
      notify(errorMessage, 'error');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: DOMPurify.sanitize(value) }));
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    // Vérifier que les mots de passe correspondent
    if (formData.newPassword !== formData.confirmPassword) {
      notify('Les mots de passe ne correspondent pas', 'error');
      return;
    }

    // Vérifier que le mot de passe n'est pas vide
    if (!formData.currentPassword || !formData.newPassword) {
      notify('Veuillez remplir tous les champs', 'error');
      return;
    }

    setIsButtonDisabled(true);
    setCountdown(COOLDOWN_DURATION);

    try {
      // Récupérer le token CSRF avant d'envoyer la requête
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post('/api/users/change-password', {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      }, {
        ...API_CONFIG,
        headers
      });

      if (response.data.success) {
        notify('Mot de passe modifié avec succès', 'success');
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        notify(response.data.message || 'Erreur lors de la modification du mot de passe', 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la modification du mot de passe:', error);
      notify(error.response?.data?.message || 'Erreur lors de la modification du mot de passe', 'error');
    } finally {
      // Démarrer le compte à rebours
      let remainingTime = COOLDOWN_DURATION;
      const intervalId = setInterval(() => {
        remainingTime -= 1;
        setCountdown(remainingTime);

        if (remainingTime <= 0) {
          clearInterval(intervalId);
          setIsButtonDisabled(false);
        }
      }, 1000);
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await axios.post('/api/users/delete/request', {}, {
        ...API_CONFIG,
        headers
      });

      if (response.data.success) {
        logger.info('Demande de suppression envoyée avec succès');
        notify(response.data.message, 'success');
        setShowDeleteConfirm(false);
      }
    } catch (err: any) {
      logger.error('Erreur lors de la demande de suppression du compte:', err);
      notify(err.response?.data?.message || 'Erreur lors de la demande de suppression du compte', 'error');
    }
  };

  const handleEmailNotificationToggle = async () => {
    if (notificationToggleDisabled) {
      return;
    }

    // Au lieu de modifier la valeur, ouvrir simplement le modal des préférences
    setShowEmailPreferencesModal(true);
  };

  const handleSeoToggle = async () => {
    if (seoLoading) return;

    try {
      setSeoLoading(true);
      const newSeoValue = !seoIndexable;

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await axios.put('/api/users/update-seo-preferences', {
        seo_indexable: newSeoValue
      }, {
        ...API_CONFIG,
        headers: {
          ...API_CONFIG.headers,
          ...headers,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        setSeoIndexable(newSeoValue);

        // Rafraîchir les stats SEO pour s'assurer de la cohérence
        await refreshStats();

        notify(
          newSeoValue
            ? 'Votre profil sera maintenant référencé sur Google'
            : 'Votre profil ne sera plus référencé sur Google',
          'success'
        );
      } else {
        notify('Erreur lors de la mise à jour des préférences SEO', 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour des préférences SEO:', error);
      notify(error.response?.data?.message || 'Erreur lors de la mise à jour des préférences SEO', 'error');
    } finally {
      setSeoLoading(false);
    }
  };

  const handleCopyReferralCode = () => {
    navigator.clipboard.writeText(referralData.referralCode);
    setReferralData(prev => ({ ...prev, copied: true }));
    setTimeout(() => {
      setReferralData(prev => ({ ...prev, copied: false }));
    }, 2000);
    notify('Code de parrainage copié dans le presse-papier', 'success');
  };

  const handleCopyReferralLink = () => {
    navigator.clipboard.writeText(referralData.referralLink);
    setReferralData(prev => ({ ...prev, copied: true }));
    setTimeout(() => {
      setReferralData(prev => ({ ...prev, copied: false }));
    }, 2000);
    notify('Lien de parrainage copié dans le presse-papier', 'success');
  };

  const handleShareOnFacebook = () => {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralData.referralLink)}`;
    window.open(url, '_blank', 'width=600,height=400');
    notify('Partage sur Facebook ouvert dans une nouvelle fenêtre', 'success');
  };

  const handleShareOnTwitter = () => {
    const text = `🌱 Découvrez JobPartiel.fr ! La plateforme qui révolutionne les services à domicile 🏡

Jardinage, bricolage, garde d'animaux... avec des prestataires vérifiés et un paiement sécurisé !

💰 Utilisez mon code : ${referralData.referralCode} et gagnez 20 Jobis !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank', 'width=600,height=400');
    notify('Partage sur Twitter ouvert dans une nouvelle fenêtre', 'success');
  };

  const handleShareOnLinkedIn = () => {
    // LinkedIn ne permet pas de définir un texte personnalisé via l'API de partage
    // On utilise seulement l'URL, l'utilisateur devra ajouter son message manuellement
    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(referralData.referralLink)}`;
    window.open(url, '_blank', 'width=600,height=400');
    notify('Partage sur LinkedIn ouvert. N\'oubliez pas d\'ajouter un message personnalisé avant de partager !', 'info');
  };

  const handleCopySocialText = (platform: string) => {
    let text = '';
    switch (platform) {
      case 'facebook':
        text = `🌱 Découvrez JobPartiel.fr, la plateforme qui révolutionne les services à domicile ! 🏡

Je viens de m'inscrire sur JobPartiel.fr, une plateforme innovante qui met en relation prestataires et clients pour des services de jardinage, bricolage, garde d'animaux, etc.

✅ Des prestataires vérifiés et de confiance
✅ Système d'échange/troc Jobi de JobPartiel.fr
✅ Interface simple et intuitive
✅ Service client réactif

Utilisez mon code de parrainage : ${referralData.referralCode}
Nous recevrons tous les deux 20 Jobis lorsque vous compléterez votre première mission !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}

Rejoignez-moi sur JobPartiel.fr et simplifiez-vous la vie ! 💪`;
        break;
      case 'twitter':
        text = `🌱 Découvrez JobPartiel.fr ! La plateforme qui révolutionne les services à domicile 🏡

Jardinage, bricolage, garde d'animaux... avec des prestataires vérifiés et un paiement sécurisé !

💰 Utilisez mon code : ${referralData.referralCode} et gagnez 20 Jobis !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}`;
        break;
      case 'linkedin':
        text = `🌱 Découvrez JobPartiel.fr, la plateforme qui révolutionne les services à domicile ! 🏡

Je souhaite partager avec mon réseau professionnel cette plateforme innovante qui met en relation prestataires et clients pour des services de jardinage, bricolage et garde d'animaux.

✅ Des prestataires vérifiés et de confiance
✅ Système d'échange/troc Jobi de JobPartiel.fr
✅ Interface simple et intuitive
✅ Opportunités de revenus complémentaires

Que vous cherchiez à proposer vos services ou à trouver un jobbeur de qualité, JobPartiel.fr est la solution !

Utilisez mon code de parrainage : ${referralData.referralCode}
Nous recevrons tous les deux 20 Jobis lorsque vous compléterez votre première mission !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}`;
        break;
      case 'whatsapp':
        text = `🌱 Découvrez JobPartiel.fr, la plateforme qui révolutionne les services à domicile ! 🏡

Je viens de m'inscrire sur JobPartiel.fr et je pense que ça pourrait t'intéresser !

✅ Services de jardinage, bricolage et garde d'animaux
✅ Des prestataires vérifiés et de confiance
✅ Système d'échange/troc Jobi de JobPartiel.fr
✅ Service client réactif
✅ Possibilité de gagner un revenu complémentaire

Utilise mon code de parrainage : ${referralData.referralCode}

Nous recevrons tous les deux 20 Jobis lorsque tu compléteras ta première mission !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}

Rejoins-moi sur JobPartiel.fr et simplifie-toi la vie ! 💪`;
        break;
    }
    navigator.clipboard.writeText(text);
    notify(`Message pour ${platform.charAt(0).toUpperCase() + platform.slice(1)} copié dans le presse-papier. Collez-le lors du partage !`, 'success');
    setShowSocialOptions(prev => ({ ...prev, [platform]: false }));
  };

  const handleShareOnWhatsApp = () => {
    const text = `🌱 Découvrez JobPartiel.fr, la plateforme qui révolutionne les services à domicile ! 🏡

Je viens de m'inscrire sur JobPartiel.fr et je pense que ça pourrait t'intéresser !

✅ Services de jardinage, bricolage, garde d'animaux, etc.
✅ Des prestataires vérifiés et de confiance
✅ Système d'échange/troc Jobi de JobPartiel.fr
✅ Service client réactif
✅ Possibilité de gagner un revenu complémentaire

Utilise mon code de parrainage : ${referralData.referralCode}

Nous recevrons tous les deux 20 Jobis lorsque tu compléteras ta première mission !

Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}

Rejoins-moi sur JobPartiel.fr et simplifie-toi la vie ! 💪`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank', 'width=600,height=400');
    notify('Partage sur WhatsApp ouvert dans une nouvelle fenêtre', 'success');
  };

  const handleShareViaEmail = () => {
    const subject = 'Découvrez JobPartiel.fr - La plateforme de services à domicile qui va vous simplifier la vie !';
    const body = `Bonjour,

Je viens de découvrir JobPartiel.fr, une plateforme innovante qui révolutionne les services à domicile, et je pense que cela pourrait vous intéresser.

✨ Pourquoi choisir JobPartiel.fr ?
• Services variés : jardinage, bricolage, garde d'animaux, etc.
• Des prestataires soigneusement sélectionnés et vérifiés
• Système d'échange/troc Jobi de JobPartiel.fr
• Service client réactif et à l'écoute
• Interface intuitive et facile à utiliser
• Possibilité de gagner un revenu complémentaire si vous proposez vos services

🎁 Offre spéciale :
Utilisez mon code de parrainage : ${referralData.referralCode}

Nous recevrons tous les deux 20 Jobis lorsque vous compléterez votre première mission !

🔗 Lien d'inscription : ${decodeURIComponent(referralData.referralLink)}

Que vous cherchiez un jobbeur de confiance pour vos travaux ou que vous souhaitiez proposer vos services et générer un revenu complémentaire, JobPartiel.fr est la solution qu'il vous faut !

Rejoignez-nous et découvrez une nouvelle façon de gérer vos services à domicile !

À bientôt sur JobPartiel.fr ! 🏡`;
    const url = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = url;
    notify('Partage par email ouvert', 'success');
  };

  // Fonction pour gérer le mode vacances
  const handleVacationMode = async () => {
    if (vacationCooldown) {
      notify(`Veuillez attendre ${vacationCooldownTime} secondes avant de changer le mode vacances`, 'warning');
      return;
    }

    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/users/updateProfil`,
        {
          mode_vacance: !isVacationMode
        },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        setIsVacationMode(!isVacationMode);
        notify(
          !isVacationMode
            ? 'Mode vacances activé. Les utilisateurs seront informés de votre absence temporaire.'
            : 'Mode vacances désactivé. Les utilisateurs ne verront plus le message d\'absence.',
          'success'
        );

        // Activer le cooldown
        setVacationCooldown(true);
        setVacationCooldownTime(15);
        const timer = setInterval(() => {
          setVacationCooldownTime((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              setVacationCooldown(false);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du mode vacances:', error);
      notify('Erreur lors de la mise à jour du mode vacances', 'error');
    }
  };

  const handleEmailInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEmailFormData(prev => ({ ...prev, [name]: DOMPurify.sanitize(value) }));
  };

  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault();

    // Vérifier s'il y a un cooldown actif
    const storedCooldown = getCookie('emailChangeCooldown');
    if (storedCooldown) {
      const cooldownTime = parseInt(storedCooldown);
      const now = Date.now();
      if (cooldownTime > now) {
        const remainingTime = Math.ceil((cooldownTime - now) / 1000);
        notify(`Veuillez attendre ${Math.ceil(remainingTime / 60)} minutes avant de demander un nouveau changement d'email`, 'warning');
        return;
      }
    }

    if (!emailFormData.newEmail || !emailFormData.password) {
      notify('Veuillez remplir tous les champs', 'error');
      return;
    }

    // Vérifier que l'email est valide
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailFormData.newEmail)) {
      notify('Veuillez entrer une adresse email valide', 'error');
      return;
    }

    try {
      // Récupérer le token CSRF avant d'envoyer la requête
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post('/api/users/change-email', {
        newEmail: emailFormData.newEmail,
        password: emailFormData.password
      }, {
        ...API_CONFIG,
        headers
      });

      if (response.data.success) {
        notify(response.data.message, 'success');
        setShowEmailModal(false);
        setEmailFormData({
          newEmail: '',
          password: ''
        });

        // Définir un cooldown de 2 minutes
        const cooldownExpiry = Date.now() + EMAIL_CHANGE_COOLDOWN * 1000;
        setCookie('emailChangeCooldown', cooldownExpiry.toString(), EMAIL_CHANGE_COOLDOWN);
        setEmailCooldown(EMAIL_CHANGE_COOLDOWN);
        setEmailButtonDisabled(true);
      } else {
        notify(response.data.message, 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors du changement d\'email:', error);
      notify(error.response?.data?.message || 'Erreur lors du changement d\'email', 'error');
    }
  };

  // Fonction pour mettre à jour l'état du bouton de notification d'emails après
  // modification des préférences dans le modal
  const handlePreferencesUpdate = (allEnabled: boolean) => {
    setNotifications(prev => ({
      ...prev,
      email: allEnabled
    }));
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <div className="space-y-6 px-2 md:px-0">
      <PageTitle variant="h1">Paramètres</PageTitle>

      <Box sx={{ width: '100%' }}>
        <StyledTabs
          value={currentTab}
          onChange={handleTabChange}
          aria-label="paramètres tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <StyledTab
            icon={<UserCog className="w-4 h-4" />}
            iconPosition="start"
            label="Profil"
          />
          <StyledTab
            icon={<Shield className="w-4 h-4" />}
            iconPosition="start"
            label="Sécurité"
          />
          <StyledTab
            icon={<Mail className="w-4 h-4" />}
            iconPosition="start"
            label="Notifications"
          />
          <StyledTab
            icon={<Users className="w-4 h-4" />}
            iconPosition="start"
            label="Parrainage"
          />
          <StyledTab
            icon={<Bot className="w-4 h-4" />}
            iconPosition="start"
            label="IA Prompts"
          />
          <StyledTab
            icon={<UserCog className="w-4 h-4" />}
            iconPosition="start"
            label="Paramètres avancés"
          />
        </StyledTabs>

        {/* Onglet Profil */}
        <TabPanel value={currentTab} index={0}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Section Email */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Mail className="mr-2 text-[#FF6B2C]" size={24} />
                Email
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Adresse Email Actuelle</label>
                  <div className="flex items-center flex-col md:flex-row gap-2 md:gap-3">
                    <div
                      className="w-full flex-1 relative cursor-pointer group email-clickable-container"
                      onClick={() => !emailButtonDisabled && setShowEmailModal(true)}
                      title={emailButtonDisabled ? `Veuillez patienter ${emailCooldown} secondes avant de pouvoir changer d'email` : "Cliquez pour modifier votre adresse email"}
                    >
                      <input
                        type="email"
                        className={`w-full rounded-md border-gray-300 shadow-sm focus:border-[#FF7A35] focus:ring-[#FF7A35] text-sm font-medium text-[#FF6B2C] ${!emailButtonDisabled ? 'cursor-pointer hover:bg-gray-50' : ''} pointer-events-none`}
                        value={userEmail}
                        readOnly
                      />
                      {!emailButtonDisabled && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-gray-500">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                        </div>
                      )}
                    </div>
                    <button
                      className={`py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md ${
                        emailButtonDisabled
                          ? 'text-gray-500 bg-gray-200 cursor-not-allowed'
                          : 'text-white bg-[#FF7A35] hover:bg-[#ff6b2c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]'
                      }`}
                      onClick={() => setShowEmailModal(true)}
                      disabled={emailButtonDisabled}
                      title={emailButtonDisabled ? `Veuillez patienter ${emailCooldown} secondes avant de pouvoir changer d'email` : "Modifier votre adresse email"}
                    >
                      {emailButtonDisabled
                        ? `Modifier (${emailCooldown} S.)`
                        : 'Modifier'}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Pour des raisons de sécurité, la modification de l'email nécessite une vérification.
                  </p>
                </div>
              </div>
            </div>

            {/* Section Type de compte */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <UserCog className="mr-2 text-[#FF6B2C]" size={24} />
                Type de compte
              </h2>
              <div className="space-y-4">
                <div className="flex flex-col p-4 bg-[#FFF8F3] rounded-xl gap-3">
                  <div>
                    <h3 className="font-medium text-sm">Statut actuel</h3>
                    <p className="text-sm text-gray-500">
                      Vous êtes actuellement en tant que <span className="text-[#FF6B2C] font-semibold">{userType === 'jobbeur' ? 'jobbeur' : 'non-jobbeur'}</span>
                    </p>
                  </div>
                  <button
                    onClick={handleToggle}
                    disabled={isButtonDisabled}
                    className={`w-full px-4 py-2 rounded-lg transition-all duration-300 ${
                      isButtonDisabled
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'flex items-center px-4 py-2 mt-0 bg-[#FF6B2C] text-white rounded-md hover:bg-[#FF7A35] transition-colors shadow-sm hover:shadow-md w-full justify-center'
                    } text-white text-sm whitespace-normal text-center`}
                  >
                    {isButtonDisabled
                      ? `Attendez ${countdown} secondes`
                      : `Passer en tant que ${userType === 'jobbeur' ? 'non-jobbeur' : 'jobbeur'}`
                    }
                  </button>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Cette action mettra à jour le menu pour refléter les différences entre les fonctionnalités des jobbeurs et des non-jobbeurs, tout en vous permettant d'accéder à différentes fonctionnalités et à être visible sur l'annuaire de la plateforme.
              </p>
            </div>

            {/* Section Mode Vacances */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Palmtree className="mr-2 text-[#FF6B2C]" size={24} />
                Mode Vacances
              </h2>
              <div className="space-y-4">
                <div className="flex flex-col p-4 bg-[#FFF8F3] rounded-xl gap-3">
                  <div>
                    <h3 className="font-medium text-sm">Statut actuel</h3>
                    <p className="text-sm text-gray-500">
                      {isVacationMode
                        ? "Les utilisateurs sont informés que vous êtes en vacances et que vos délais de réponse peuvent être plus longs."
                        : "Les utilisateurs ne sont pas informés que vous êtes en vacances."}
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium ${isVacationMode ? 'text-[#FF6B2C]' : 'text-gray-500'}`}>
                      {vacationCooldown ? `${vacationCooldownTime}s` : isVacationMode ? 'Activé' : 'Désactivé'}
                    </span>
                    <button
                      onClick={handleVacationMode}
                      disabled={vacationCooldown}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-1 ${
                        isVacationMode ? 'bg-[#FF6B2C]' : 'bg-gray-200'
                      } ${vacationCooldown ? 'cursor-not-allowed opacity-50' : 'hover:shadow-md'}`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-sm transition-transform duration-300 ${
                          isVacationMode ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Activez le mode vacances pour informer les autres utilisateurs que vous êtes temporairement indisponible. Cela n'empêche pas les utilisateurs de vous contacter, mais les avertit que vos délais de réponse peuvent être plus longs.
              </p>
            </div>
          </div>
        </TabPanel>

        {/* Onglet Sécurité */}
        <TabPanel value={currentTab} index={1}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Section Mot de passe */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Shield className="mr-2 text-[#FF6B2C]" size={24} />
                Mot de passe
              </h2>
              <form onSubmit={handlePasswordChange} className="space-y-4">
                <input
                  type="text"
                  name="username"
                  autoComplete="username"
                  style={{ display: 'none' }}
                  defaultValue={userEmail}
                />
                <div>
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">Mot de passe actuel</label>
                  <div className="relative">
                    <input
                      type={showPasswords.current ? "text" : "password"}
                      id="currentPassword"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] pr-10"
                      required
                      autoComplete="off"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                    >
                      {showPasswords.current ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                </div>
                <div>
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">Nouveau mot de passe</label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? "text" : "password"}
                      id="newPassword"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] pr-10"
                      required
                      autoComplete="new-password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                    >
                      {showPasswords.new ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  {formData.newPassword && (
                    <div className="mt-2">
                      <VerifierForceMotDePasse motDePasse={formData.newPassword} />
                    </div>
                  )}
                </div>
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">Confirmer le nouveau mot de passe</label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? "text" : "password"}
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] pr-10"
                      required
                      autoComplete="new-password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                      onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                    >
                      {showPasswords.confirm ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  {formData.newPassword && formData.confirmPassword && formData.newPassword !== formData.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">Les mots de passe ne correspondent pas</p>
                  )}
                </div>
                <button
                  type="submit"
                  disabled={isButtonDisabled}
                  className={`w-full py-2 px-4 rounded-md text-white font-medium ${isButtonDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#FF6B2C] hover:bg-[#FF7A35]'} transition-colors`}
                >
                  {isButtonDisabled ? `Veuillez patienter (${countdown}s)` : 'Changer le mot de passe'}
                </button>
              </form>
            </div>

            {/* Section Authentification à deux facteurs */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Shield className="mr-2 text-[#FF6B2C]" size={24} />
                Authentification à deux facteurs
              </h2>
              <div className="space-y-4">
                <div className="flex flex-col p-4 bg-[#FFF8F3] rounded-xl gap-3">
                  <div>
                    <h3 className="font-medium text-sm">Sécurité renforcée</h3>
                    <p className="text-sm text-gray-500">
                      Renforcez la sécurité de votre compte en activant l'authentification à deux facteurs
                    </p>
                  </div>
                  <button
                    onClick={() => navigate('/dashboard/profil/securite')}
                    className="flex items-center px-4 py-2 mt-0 bg-[#FF6B2C] text-white rounded-md hover:bg-[#FF7A35] transition-colors shadow-sm hover:shadow-md w-full justify-center"
                  >
                    <Shield className="mr-2" size={16} />
                    Gérer l'authentification à deux facteurs
                  </button>
                </div>
              </div>
            </div>
          </div>
        </TabPanel>

        {/* Onglet Notifications */}
        <TabPanel value={currentTab} index={2}>
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 text-[#FF6B2C]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Notifications
            </h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Notifications par email</p>
                  <p className="text-sm text-gray-500">Recevoir des notifications par email</p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleEmailNotificationToggle}
                    disabled={notificationToggleDisabled}
                    className={`relative inline-flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                      notificationToggleDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    } ${
                      notifications.email
                        ? 'bg-[#FF6B2C] text-white hover:bg-[#FF7A35]'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    <span className="font-medium text-sm">
                      {notifications.email ? 'Activé' : 'Désactivé'}
                    </span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-2 h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between opacity-50">
                <div>
                  <h3 className="text-sm font-medium text-gray-700">Notifications SMS</h3>
                  <p className="text-sm text-gray-500">Recevoir les notifications par SMS (Non disponible pour l'instant)</p>
                </div>
                <button
                  disabled={true}
                  className="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-not-allowed transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] bg-gray-200"
                >
                  <span className="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-0" />
                </button>
              </div>

              <div className="flex items-center justify-between opacity-50">
                <div>
                  <h3 className="text-sm font-medium text-gray-700">Notifications application</h3>
                  <p className="text-sm text-gray-500">Recevoir les notifications dans l'application (Non disponible pour l'instant)</p>
                </div>
                <button
                  disabled={true}
                  className="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-not-allowed transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] bg-gray-200"
                >
                  <span className="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-0" />
                </button>
              </div>
            </div>
          </div>
        </TabPanel>

        {/* Onglet Parrainage */}
        <TabPanel value={currentTab} index={3}>
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h2 className="text-xl font-semibold mb-4 flex items-center justify-between">
              <div className="flex items-center">
                <Users className="mr-2 text-[#FF6B2C]" size={24} />
                Parrainage
              </div>
              {hasNewReferral && (
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <span className="relative flex h-2 w-2 mr-1">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                    </span>
                    Nouveau filleul !
                  </span>
                </div>
              )}
            </h2>
            <div className="space-y-4">
              <div>
                <p className="font-medium">Votre code de parrainage</p>
                <p className="text-sm text-gray-500 mb-2">Partagez ce code avec vos amis pour gagner des Jobis</p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={referralData.referralCode}
                    readOnly
                    className="bg-gray-100 p-2 rounded-l-md flex-grow text-sm"
                  />
                  <button
                    onClick={handleCopyReferralCode}
                    className="bg-[#FF6B2C] text-white p-2 rounded-r-md hover:bg-[#FF7A35] transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="transform scale-90">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div>
                <p className="font-medium">Lien de parrainage</p>
                <p className="text-sm text-gray-500 mb-2">Partagez ce lien pour que vos amis s'inscrivent avec votre code</p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={referralData.referralLink}
                    readOnly
                    className="bg-gray-100 p-2 rounded-l-md flex-grow text-sm"
                  />
                  <button
                    onClick={handleCopyReferralLink}
                    className="bg-[#FF6B2C] text-white p-2 rounded-r-md hover:bg-[#FF7A35] transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm">
                  <span className="font-semibold">Comment ça marche :</span> Lorsqu'un ami s'inscrit avec votre code et complète sa première mission, vous recevez tous les deux 20 Jobis !
                </p>
                {hasNewReferral && (
                  <div className="bg-[#FFF8F3] p-3 rounded-lg my-3 border border-green-200 flex items-center">
                    <div className="mr-2 flex-shrink-0">
                      <span className="flex h-3 w-3">
                        <span className="animate-ping absolute inline-flex h-3 w-3 rounded-full bg-green-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                      </span>
                    </div>
                    <p className="text-sm text-gray-800">
                      <span className="font-semibold">Bonne nouvelle !</span> Vous avez des nouveaux filleuls. Cliquez ci-dessous pour les voir.
                    </p>
                  </div>
                )}
                <button
                  onClick={() => setShowReferralListModal(true)}
                  className="flex items-center px-4 py-2 mt-4 bg-[#FF6B2C] text-white rounded-md hover:bg-[#FF7A35] transition-colors shadow-sm hover:shadow-md w-full justify-center"
                >
                  <Users size={18} className="mr-2" />
                  <span className="font-medium">Voir mes filleuls actuels et mes récompenses</span>
                </button>
                <p className="text-sm text-gray-500 mt-2 text-center">
                  Suivez la progression de vos parrainages et consultez vos récompenses
                </p>
              </div>

              {/* Nouvelle section pour le partage sur les réseaux sociaux */}
              <div className="mt-4">
                <p className="font-medium flex items-center">
                  <Share2 size={16} className="mr-1" />
                  Partager sur les réseaux sociaux
                </p>
                <p className="text-sm text-gray-500 mb-3">Partagez votre lien de parrainage sur vos réseaux sociaux</p>

                <div className="grid grid-cols-3 gap-2 mb-3">
                  {/* Facebook */}
                  <div className="relative social-dropdown">
                    <button
                      onClick={() => setShowSocialOptions(prev => ({ ...prev, facebook: !prev.facebook }))}
                      className="flex items-center justify-center p-2 w-full bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-[#1877F2] hover:text-white hover:border-[#1877F2] transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="currentColor" className="transform scale-90">
                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                      </svg>
                      <span className="ml-2 text-sm">Facebook</span>
                    </button>

                    {showSocialOptions.facebook && (
                      <div className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg py-1 text-sm">
                        <button
                          onClick={handleShareOnFacebook}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                          Partager
                        </button>
                        <button
                          onClick={() => handleCopySocialText('facebook')}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                          </svg>
                          Copier le texte
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Twitter */}
                  <div className="relative social-dropdown">
                    <button
                      onClick={() => setShowSocialOptions(prev => ({ ...prev, twitter: !prev.twitter }))}
                      className="flex items-center justify-center p-2 w-full bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-[#1DA1F2] hover:text-white hover:border-[#1DA1F2] transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="currentColor" className="transform scale-90">
                        <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                      </svg>
                      <span className="ml-2 text-sm">Twitter</span>
                    </button>

                    {showSocialOptions.twitter && (
                      <div className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg py-1 text-sm">
                        <button
                          onClick={handleShareOnTwitter}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                          Partager
                        </button>
                        <button
                          onClick={() => handleCopySocialText('twitter')}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                          </svg>
                          Copier le texte
                        </button>
                      </div>
                    )}
                  </div>

                  {/* LinkedIn */}
                  <div className="relative social-dropdown">
                    <button
                      onClick={() => setShowSocialOptions(prev => ({ ...prev, linkedin: !prev.linkedin }))}
                      className="flex items-center justify-center p-2 w-full bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-[#0A66C2] hover:text-white hover:border-[#0A66C2] transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="currentColor" className="transform scale-90">
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                      </svg>
                      <span className="ml-2 text-sm">LinkedIn</span>
                    </button>

                    {showSocialOptions.linkedin && (
                      <div className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg py-1 text-sm">
                        <button
                          onClick={handleShareOnLinkedIn}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                          Partager
                        </button>
                        <button
                          onClick={() => handleCopySocialText('linkedin')}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                          </svg>
                          Copier le texte
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  {/* WhatsApp */}
                  <div className="relative social-dropdown">
                    <button
                      onClick={() => setShowSocialOptions(prev => ({ ...prev, whatsapp: !prev.whatsapp }))}
                      className="flex items-center justify-center p-2 w-full bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-[#25D366] hover:text-white hover:border-[#25D366] transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="currentColor" className="transform scale-90">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347"/>
                        <path d="M12.007 0C5.465 0 0.107 5.334 0.104 11.893c0 2.096.547 4.142 1.588 5.945L0.057 24l6.305-1.654c1.737.947 3.7 1.447 5.684 1.447C18.605 23.793 24 18.44 24 11.893 24 5.334 18.572 0 12.007 0zm0 21.686A9.874 9.874 0 016.975 20.31l-.361-.214-3.741.981.998-3.648-.235-.374a9.925 9.925 0 01-1.51-5.262c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884z"/>
                      </svg>
                      <span className="ml-2 text-sm">WhatsApp</span>
                    </button>

                    {showSocialOptions.whatsapp && (
                      <div className="absolute z-10 mt-1 w-full bg-white rounded-md shadow-lg py-1 text-sm">
                        <button
                          onClick={handleShareOnWhatsApp}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                          Partager
                        </button>
                        <button
                          onClick={() => handleCopySocialText('whatsapp')}
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                          </svg>
                          Copier le texte
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Email */}
                  <button
                    onClick={handleShareViaEmail}
                    className="flex items-center justify-center p-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-[#EA4335] hover:text-white hover:border-[#EA4335] transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    <span className="ml-2 text-sm">Email</span>
                  </button>
                </div>

                <div className="bg-[#FFF8F3] p-3 rounded-lg border border-[#FFE4BA] mt-4 mb-3">
                  <p className="text-sm font-medium text-[#FF6B2C] mb-1">Bonus de partage</p>
                  <p className="text-sm text-gray-700">
                    Recevez <span className="font-semibold text-[#FF6B2C]">5 Jobis</span> pour chaque partage validé sur les réseaux sociaux !
                    <span className="block mt-1 text-xs text-gray-500">
                      Pour être validé, votre partage doit inclure une capture d'écran et le lien du post.
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </TabPanel>

        {/* Onglet IA */}
        <TabPanel value={currentTab} index={4}>
          <AiPromptsSection />
        </TabPanel>

        {/* Onglet Paramètres avancés */}
        <TabPanel value={currentTab} index={5}>
          <div className="space-y-8">
            {/* Section Référencement SEO */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 text-[#FF6B2C]" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="M21 21l-4.35-4.35"></path>
                </svg>
                Référencement Google
              </h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Autoriser le référencement de mon profil</p>
                    <p className="text-sm text-gray-500">
                      Permettre à Google et aux autres moteurs de recherche de référencer votre profil public
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleSeoToggle}
                      disabled={seoLoading}
                      className={`relative inline-flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                        seoLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      } ${
                        seoIndexable
                          ? 'bg-[#FF6B2C] text-white hover:bg-[#FF7A35]'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      <span className="font-medium text-sm">
                        {seoLoading ? 'Mise à jour...' : (seoIndexable ? 'Activé' : 'Désactivé')}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ml-2 h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="bg-[#FFF8F3] p-3 rounded-lg border border-[#FFE4BA]">
                  <p className="text-sm text-gray-700">
                    <span className="font-semibold text-[#FF6B2C]">Avantages du référencement :</span>
                  </p>
                  <ul className="text-sm text-gray-700 mt-2 space-y-1">
                    <li>• Votre profil apparaîtra dans les résultats de recherche Google</li>
                    <li>• Augmentation de votre visibilité auprès de nouveaux clients</li>
                    <li>• Plus d'opportunités de missions</li>
                    <li>• Amélioration de votre présence en ligne</li>
                  </ul>
                  <p className="text-xs text-gray-500 mt-2">
                    Vous pouvez désactiver cette option à tout moment. Les changements peuvent prendre quelques jours à être pris en compte par Google.
                  </p>
                </div>
              </div>
            </div>

            {/* Section Suppression de compte */}
            <div className="bg-white p-6 rounded-xl shadow-md">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 text-red-500" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
                Suppression du compte
              </h2>
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-amber-800 mb-1">Suppression conforme RGPD</h3>
                    <p className="text-sm text-amber-700">
                      Conformément au RGPD, vos données personnelles seront anonymisées et non supprimées définitivement.
                      Cela nous permet de conserver une trace pour des raisons légales tout en protégeant votre vie privée.
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-600 mb-4 space-y-2">
                <p><strong>Ce qui sera anonymisé :</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Nom, prénom et informations personnelles</li>
                  <li>Adresse email (remplacée par une adresse anonyme)</li>
                  <li>Numéro de téléphone et adresse</li>
                  <li>Photo de profil (remplacée par l'avatar par défaut)</li>
                </ul>
                <p className="mt-3"><strong>Ce qui sera conservé de manière anonyme :</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Historique des transactions (pour la comptabilité)</li>
                  <li>Avis et évaluations (anonymisés)</li>
                  <li>Données statistiques agrégées</li>
                </ul>
              </div>
              {!showDeleteConfirm ? (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="w-full py-3 px-4 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors font-medium"
                >
                  Demander la suppression de mon compte
                </button>
              ) : (
                <div className="space-y-4">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <h4 className="text-sm font-medium text-red-800 mb-1">Demande de suppression par email</h4>
                        <p className="text-sm text-red-700">
                          Un email de confirmation sera envoyé à votre adresse. Vous devrez ensuite vous re-authentifier pour confirmer la suppression de votre compte.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={handleDeleteAccount}
                      className="flex-1 py-3 px-4 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors font-medium"
                    >
                      Envoyer l'email de confirmation
                    </button>
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className="flex-1 py-3 px-4 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors font-medium"
                    >
                      Annuler
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPanel>
      </Box>

      {/* Modal pour afficher la liste des filleuls */}
      <ReferralListModal
        isOpen={showReferralListModal}
        onClose={() => setShowReferralListModal(false)}
      />

      {/* Modal de changement d'email */}
      <ModalPortal
        isOpen={showEmailModal}
        onBackdropClick={() => setShowEmailModal(false)}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white p-6 rounded-xl shadow-xl w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-gray-800">
              Changer d'adresse email
              {emailCooldown > 0 && (
                <span className="ml-2 text-sm font-normal text-[#FF6B2C]">
                  (Prochain changement dans {Math.floor(emailCooldown / 60)}:{(emailCooldown % 60).toString().padStart(2, '0')})
                </span>
              )}
            </h3>
            <button
              onClick={() => setShowEmailModal(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <form onSubmit={handleEmailChange} className="space-y-4">
            <div>
              <label htmlFor="newEmail" className="block text-sm font-medium text-gray-700 mb-1">Nouvelle adresse email</label>
              <input
                type="email"
                id="newEmail"
                name="newEmail"
                value={emailFormData.newEmail}
                onChange={handleEmailInputChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C]"
                required
              />
            </div>
            <div>
              <label htmlFor="emailPassword" className="block text-sm font-medium text-gray-700 mb-1">Mot de passe actuel du compte JobPartiel</label>
              <div className="relative">
                <input
                  type={showEmailPassword ? "text" : "password"}
                  id="emailPassword"
                  name="password"
                  value={emailFormData.password}
                  onChange={handleEmailInputChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] pr-10"
                  required
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                  onClick={() => setShowEmailPassword(!showEmailPassword)}
                >
                  {showEmailPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>
            <div className="bg-[#FFF8F3] p-3 rounded-lg border border-[#FFE4BA] mt-4">
              <p className="text-sm text-gray-700">
                <span className="font-semibold text-[#FF6B2C]">Important:</span> Après validation, un email de vérification sera envoyé à votre nouvelle adresse.
                Vous devrez cliquer sur le lien de vérification pour confirmer le changement.
              </p>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowEmailModal(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#FF6B2C] text-white rounded-md hover:bg-[#FF7A35] transition-colors"
              >
                Confirmer
              </button>
            </div>
          </form>
        </motion.div>
      </ModalPortal>

      {/* Modalportal pour afficher les préférences d'email */}
      {showEmailPreferencesModal && (
        <ModalPortal
          onBackdropClick={() => setShowEmailPreferencesModal(false)}
          closeOnBackdropClick={true}
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-xl shadow-xl max-w-2xl w-[95%] max-h-[85vh] overflow-y-auto"
          >
            <div className="p-4 border-b flex justify-between items-center">
              <div className="flex items-center">
                <Mail className="text-[#FF6B2C] mr-2" size={20} />
                <h3 className="text-lg font-semibold text-gray-800">Préférences de notifications par email</h3>
              </div>
              <button
                onClick={() => setShowEmailPreferencesModal(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-4">
              <EmailPreferencesSection
                onPreferencesUpdate={handlePreferencesUpdate}
                onClose={() => setShowEmailPreferencesModal(false)}
              />
            </div>
          </motion.div>
        </ModalPortal>
      )}
    </div>
  );
}
