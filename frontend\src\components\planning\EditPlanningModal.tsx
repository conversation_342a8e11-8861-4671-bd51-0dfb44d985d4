import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, <PERSON>alogActions, Button, TextField, Typography, IconButton, Box, FormControl, RadioGroup, FormControlLabel, Radio, InputAdornment } from '@mui/material';
import { X, Clock, CalendarDays, PenSquare, Calendar, Trash2, RefreshCw, EyeOff, Eye, Coins } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import DOMPurify from 'dompurify';
import { PlanningFormData, planningFormSchema, Mission } from '../../types/planning';
import ModalPortal from '../../components/ModalPortal';
import { logger } from '@/utils/logger';
import { format } from 'date-fns';
import TiptapEditor from '../TiptapEditor';

interface EditPlanningModalProps {
  open: boolean;
  mission: Mission;
  onClose: () => void;
  onSubmit: (data: PlanningFormData) => void;
  onDelete: () => void;
  onRestore?: () => void;
  isHidden?: boolean;
  onOpenRecap?: () => void;
}

const EditPlanningModal: React.FC<EditPlanningModalProps> = ({
  open,
  mission,
  onClose,
  onSubmit,
  onDelete,
  onRestore,
  isHidden = false,
  onOpenRecap
}) => {
  // États pour suivre les compteurs de caractères
  const [titleLength, setTitleLength] = useState(0);
  const [descriptionLength, setDescriptionLength] = useState(0);

  // Sanitize les données de la mission à l'affichage
  const sanitizedMission = {
    ...mission,
    title: DOMPurify.sanitize(mission.title),
    description: mission.description ? DOMPurify.sanitize(mission.description) : '',
    mission: mission.mission ? {
      ...mission.mission,
      title: DOMPurify.sanitize(mission.mission.title),
      description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
    } : mission.mission
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
    control,
    watch,
    setValue
  } = useForm<PlanningFormData>({
    resolver: zodResolver(planningFormSchema),
    defaultValues: {
      title: sanitizedMission.title,
      description: sanitizedMission.description || '',
      date: sanitizedMission.date,
      start_time: sanitizedMission.start_time,
      end_time: sanitizedMission.end_time,
      mission_id: sanitizedMission.mission?.id,
      // Inclure les valeurs pour les missions manuelles ou propositions
      montant_propose: sanitizedMission.montant_propose || sanitizedMission.proposition?.montant_propose || 0,
      payment_method: sanitizedMission.payment_method || sanitizedMission.proposition?.payment_method || 'jobi_only'
    }
  });

  // Initialiser les compteurs de caractères avec les valeurs existantes
  useEffect(() => {
    setTitleLength(sanitizedMission.title.length);
    
    // Extraire le texte pour le comptage des caractères
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = sanitizedMission.description || '';
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    setDescriptionLength(textContent.length);
  }, [sanitizedMission]);

  // Surveiller le mode de paiement choisi (pour les missions manuelles)
  const selectedPaymentMethod = watch('payment_method');
  // Surveiller la description pour activer/désactiver le bouton de prévisualisation

  const handleClose = () => {
    onClose();
  };

  const handleFormSubmit = (data: PlanningFormData) => {
    try {
      // Sanitize les données avant soumission
      const sanitizedData = {
        ...data,
        title: DOMPurify.sanitize(data.title),
        description: data.description ? DOMPurify.sanitize(data.description) : undefined,
        // S'assurer que montant_propose est un nombre
        montant_propose: typeof data.montant_propose === 'string' 
          ? parseFloat(data.montant_propose) || 0 
          : data.montant_propose || 0
      };
      
      // Appeler la fonction de soumission passée par le parent
      onSubmit(sanitizedData);
    } catch (error) {
      logger.info('Erreur lors de la soumission du formulaire:');
    }
  };
  
  // Gestionnaire pour le clic sur le bouton Enregistrer
  const handleSaveButtonClick = (e: React.MouseEvent) => {
    // e.preventDefault(); // Empêcher le comportement par défaut
    // e.stopPropagation(); // Empêcher la propagation
    
    try {
      // Récupérer les valeurs actuelles du formulaire
      const formValues = getValues();
      
      // Vérifier si le formulaire est valide
      if (!formValues.title || !formValues.date || !formValues.start_time || !formValues.end_time) {
        logger.info('Formulaire invalide:', {formValues});
        return;
      }
      
      // S'assurer que montant_propose est un nombre
      if (formValues.montant_propose) {
        formValues.montant_propose = typeof formValues.montant_propose === 'string' 
          ? parseFloat(formValues.montant_propose) || 0 
          : formValues.montant_propose;
      }
      
      // Soumettre manuellement
      handleFormSubmit(formValues);
    } catch (error) {
      logger.info('Erreur lors du traitement du clic sur Enregistrer:');
    }
  };

  const getTimeAsMinutes = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Gestionnaire pour le champ titre
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Sanitize l'entrée
    const sanitizedValue = DOMPurify.sanitize(e.target.value);
    
    // Limiter à 120 caractères
    const truncatedValue = sanitizedValue.slice(0, 120);
    
    // Mettre à jour le champ et le compteur
    if (sanitizedValue !== e.target.value) {
      e.target.value = truncatedValue;
    }
    
    setTitleLength(truncatedValue.length);
    
    // Mettre à jour le champ dans le formulaire
    setValue('title', truncatedValue);
  };

  // Gestionnaire pour le contenu de l'éditeur TipTap
  const handleEditorChange = (content: string) => {
    // Le contenu HTML est déjà nettoyé par TiptapEditor
    setValue('description', content);
    
    // Extraire le texte pour le comptage des caractères
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    setDescriptionLength(textContent.length);
  };

  return (
    <ModalPortal isOpen={open} onBackdropClick={handleClose}>
      <Dialog 
        open={true} 
        maxWidth="sm" 
        fullWidth
        hideBackdrop={true}
        onClose={(event, reason) => {
          if (reason === 'backdropClick') {
            handleClose();
          }
        }}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            backgroundColor: '#fff',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
            overflow: 'auto',
            overflowY: 'scroll',
            m: {
              xs: '8px',
              sm: '16px',
              md: '32px'
            },
            minWidth: '600px',
            display: 'flex',
            flexDirection: 'column',
            width: {
              xs: 'calc(100% - 16px)',
              sm: 'auto'
            },
            height: {
              xs: 'auto',
              sm: 'auto',
              md: 'auto'
            },
            maxHeight: {
              xs: '90dvh',
              sm: 'calc(100vh - 100px)'
            },
            '@media (max-width: 650px)': {
              minWidth: 'calc(100% - 16px)',
              margin: '8px'
            }
          }
        }}
      >
        <form onSubmit={handleSubmit(handleFormSubmit)} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <DialogTitle 
            sx={{ 
              backgroundColor: isHidden ? '#777' : '#FF6B2C', 
              color: 'white', 
              fontWeight: 700,
              fontSize: { xs: '1rem', sm: '1.1rem' },
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: { xs: '14px 16px', sm: '18px 24px' },
              flexShrink: 0
            }}
          >
            {isHidden ? 'Mission masquée' : 'Modifier la mission'}
            <IconButton 
              onClick={handleClose} 
              sx={{ 
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
              size="small"
            >
              <X size={18} />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ 
            padding: { xs: '20px 16px', sm: '28px 24px' }, 
            background: '#fff',
            overflow: 'auto',
            flexGrow: 1
          }}>
            <Box className="space-y-5" sx={{ mt: 1 }}>
              {isHidden && (
                <Box 
                  sx={{
                    backgroundColor: 'rgba(120, 120, 120, 0.1)',
                    padding: '12px 16px',
                    borderRadius: '10px',
                    borderLeft: '3px solid #777',
                    color: '#555',
                    marginBottom: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5
                  }}
                >
                  <EyeOff size={18} style={{ color: '#777' }} />
                  <Typography variant="body2">
                    Cette mission est actuellement masquée du planning. Vous pouvez la restaurer pour la rendre à nouveau visible.
                  </Typography>
                </Box>
              )}
              
              {mission.mission?.id ? (
                <Box 
                  sx={{
                    backgroundColor: 'rgba(255, 107, 44, 0.06)',
                    padding: '12px 16px',
                    borderRadius: '10px',
                    borderLeft: '3px solid #FF6B2C',
                    color: '#555',
                    marginBottom: '24px',
                    marginTop: isHidden ? 0 : '12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    opacity: isHidden ? 0.7 : 1
                  }}
                >
                  <Calendar size={18} style={{ color: '#FF6B2C' }} />
                  <Typography variant="body2">
                    Mission liée au JobPartiel: <strong>{mission.mission?.title}</strong>
                  </Typography>
                </Box>
              ) : null}

              <Box className="mb-5">
                <Box className="flex items-center gap-2 mb-2.5">
                  <PenSquare size={18} style={{ color: isHidden ? '#777' : '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Titre de la mission
                  </Typography>
                </Box>
                <TextField
                  {...register('title')}
                  disabled={isHidden}
                  fullWidth
                  error={!!errors.title}
                  helperText={errors.title?.message || `${titleLength}/120 caractères`}
                  onChange={handleTitleChange}
                  inputProps={{ maxLength: 120 }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '10px',
                      '&:hover fieldset': {
                        borderColor: isHidden ? '#ccc' : '#FF965E',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isHidden ? '#999' : '#FF6B2C',
                      },
                    },
                    '& .MuiFormHelperText-root': {
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginRight: 0,
                    },
                  }}
                />
              </Box>

              <Box className="mb-5">
                <Box className="flex flex-col space-y-1.5 mb-1">
                  <Typography variant="subtitle2" color="text.primary" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                    Description <span className="text-gray-400 font-normal">(optionnel)</span>
                  </Typography>
                  <Box className="flex items-center justify-between">
                    <Typography variant="caption" color="text.secondary">
                      Donnez tous les détails nécessaires à la réalisation de la mission
                    </Typography>
                    <Typography variant="caption" color={descriptionLength > 1200 ? "error" : "text.secondary"}>
                      {descriptionLength}/1200
                    </Typography>
                  </Box>
                </Box>
                
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TiptapEditor
                      content={field.value || ''}
                      onChange={handleEditorChange}
                      placeholder="Décrivez la mission en détail..."
                      className="rounded-md border border-gray-300 focus-within:border-primary-500"
                      maxLength={1200}
                      readOnly={isHidden}
                    />
                  )}
                />
                
                {errors.description && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                    {errors.description.message}
                  </Typography>
                )}
              </Box>

              <Box className="mb-5">
                <Box className="flex items-center gap-2 mb-2.5">
                  <CalendarDays size={18} style={{ color: isHidden ? '#777' : '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Date de la mission
                  </Typography>
                </Box>
                <TextField
                  {...register('date')}
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.date}
                  helperText={errors.date?.message}
                  disabled={isHidden}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '10px',
                      '&:hover fieldset': {
                        borderColor: isHidden ? '#ccc' : '#FF965E',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isHidden ? '#999' : '#FF6B2C',
                      },
                    },
                  }}
                />
              </Box>

              <Box className="mb-3">
                <Box className="flex items-center gap-2 mb-2.5">
                  <Clock size={18} style={{ color: isHidden ? '#777' : '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Horaires
                  </Typography>
                </Box>
                <Box className="grid grid-cols-2 gap-4">
                  <TextField
                    {...register('start_time', {
                      onChange: (e) => {
                        // Si la valeur est vide, utiliser la valeur par défaut ou la valeur actuelle
                        if (!e.target.value && !isHidden) {
                          e.target.value = sanitizedMission.start_time || '08:00';
                        }
                      }
                    })}
                    label="Heure de début"
                    type="time"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    error={!!errors.start_time}
                    helperText={errors.start_time?.message}
                    disabled={isHidden}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '10px',
                        '&:hover fieldset': {
                          borderColor: isHidden ? '#ccc' : '#FF965E',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: isHidden ? '#999' : '#FF6B2C',
                        },
                      },
                      '& .MuiInputLabel-root': {
                        color: '#666',
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: isHidden ? '#999' : '#FF6B2C',
                      },
                    }}
                  />
                  <TextField
                    {...register('end_time', {
                      onChange: (e) => {
                        // Si la valeur est vide, utiliser la valeur par défaut ou la valeur actuelle
                        if (!e.target.value && !isHidden) {
                          e.target.value = sanitizedMission.end_time || '18:00';
                        }
                      }
                    })}
                    label="Heure de fin"
                    type="time"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    error={!!errors.end_time || (!isHidden && !!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || sanitizedMission.end_time) <= getTimeAsMinutes(watch('start_time') || sanitizedMission.start_time))}
                    helperText={errors.end_time?.message || 
                      (!isHidden && !!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || sanitizedMission.end_time) <= getTimeAsMinutes(watch('start_time') || sanitizedMission.start_time)
                        ? "L'heure de fin doit être après l'heure de début" 
                        : undefined)}
                    disabled={isHidden}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '10px',
                        '&:hover fieldset': {
                          borderColor: isHidden ? '#ccc' : '#FF965E',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: isHidden ? '#999' : '#FF6B2C',
                        },
                      },
                      '& .MuiInputLabel-root': {
                        color: '#666',
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: isHidden ? '#999' : '#FF6B2C',
                      },
                    }}
                  />
                </Box>
                {/* Message d'erreur conditionnel - affiché seulement si les deux champs ont des valeurs et ne sont pas masqués */}
                {(!isHidden && !!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || sanitizedMission.end_time) <= getTimeAsMinutes(watch('start_time') || sanitizedMission.start_time)) && (
                  <Box sx={{ mt: 1, color: '#d32f2f', fontSize: '0.75rem', fontWeight: 400 }}>
                    L'heure de fin doit être postérieure à l'heure de début
                  </Box>
                )}
              </Box>

              {/* Section de tarif - visible pour toutes les missions non masquées */}
              {!isHidden && (
                <Box className="mb-5">
                  <Box className="flex items-center gap-2 mb-2.5">
                    <Coins size={18} style={{ color: '#FF6B2C' }} />
                    <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                      Mode de paiement et tarif
                    </Typography>
                  </Box>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      {sanitizedMission.mission?.id 
                        ? "Modifier le mode de paiement et le tarif pour cette mission" 
                        : "Sélectionnez le mode de paiement pour cette mission"}
                    </Typography>
                    
                    <Controller
                      name="payment_method"
                      control={control}
                      render={({ field }) => (
                        <FormControl component="fieldset">
                          <RadioGroup
                            {...field}
                            sx={{
                              '& .MuiFormControlLabel-root': {
                                marginBottom: '8px'
                              }
                            }}
                          >
                            <FormControlLabel
                              value="jobi_only"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Jobi uniquement
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Échange de services via système Jobi
                                  </Typography>
                                </Box>
                              }
                            />
                            <FormControlLabel
                              value="direct_only"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Paiement direct
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Paiement en euros directement au prestataire
                                  </Typography>
                                </Box>
                              }
                            />
                            <FormControlLabel
                              value="both"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Mode hybride
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Combinaison de Jobi et paiement direct
                                  </Typography>
                                </Box>
                              }
                            />
                          </RadioGroup>
                        </FormControl>
                      )}
                    />
                  </Box>
                  
                  <Box sx={{ mt: 2 }}>
                    <Controller
                      name="montant_propose"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          type="number"
                          label="Montant proposé"
                          fullWidth
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                {selectedPaymentMethod === 'jobi_only' ? 'J' : 
                                 selectedPaymentMethod === 'direct_only' ? '€' : 'J/€'}
                              </InputAdornment>
                            ),
                          }}
                          inputProps={{ 
                            min: 0,
                            step: 1 
                          }}
                          // Convertir la valeur en nombre
                          onChange={(e) => {
                            const value = e.target.value === '' ? 0 : Number(e.target.value);
                            field.onChange(value);
                          }}
                          error={!!errors.montant_propose}
                          helperText={errors.montant_propose?.message || (
                            selectedPaymentMethod === 'jobi_only' 
                              ? 'Montant en Jobi' 
                              : selectedPaymentMethod === 'direct_only' 
                                ? 'Montant en euros' 
                                : 'Montant combiné Jobi/euros'
                          )}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '10px',
                              '&:hover fieldset': {
                                borderColor: '#FF965E',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#FF6B2C',
                              },
                            },
                          }}
                        />
                      )}
                    />
                  </Box>
                </Box>
              )}

              {/* Afficher un résumé pour les missions masquées ou avec proposition */}
              {(sanitizedMission.proposition || sanitizedMission.montant_propose || isHidden) && (
                <Box 
                  sx={{ 
                    mt: 4, 
                    mb: 3, 
                    p: 2, 
                    bgcolor: 'rgba(0,0,0,0.02)', 
                    borderRadius: '10px',
                    opacity: isHidden ? 0.7 : 1
                  }}
                >
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1.5 }}>
                    Résumé de la mission
                  </Typography>
                  
                  {/* Dates et heures */}
                  <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Clock size={14} />
                    <Typography variant="body2">
                      {format(new Date(sanitizedMission.date), 'dd/MM/yyyy')} • {sanitizedMission.start_time.substring(0, 5)} - {sanitizedMission.end_time.substring(0, 5)}
                    </Typography>
                  </Box>

                  {/* Affichage du prix modifié quand il existe */}
                  {sanitizedMission.montant_propose && sanitizedMission.montant_propose > 0 && sanitizedMission.proposition && (
                    <Box sx={{ mt: 2, mb: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Coins size={14} color="#FF6B2C" />
                      <Typography variant="body2">
                        Prix actuel: <strong>{sanitizedMission.montant_propose} {sanitizedMission.payment_method === 'direct_only' ? '€' : 'J'}</strong>
                        {sanitizedMission.payment_method && (
                          <Typography component="span" variant="caption" sx={{ ml: 1, color: '#666' }}>
                            ({sanitizedMission.payment_method === 'jobi_only' 
                              ? 'Jobi uniquement' 
                              : sanitizedMission.payment_method === 'direct_only'
                                ? 'Paiement direct' 
                                : 'Mode hybride'})
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  )}

                  {/* Informations sur le tarif pour proposition */}
                  {sanitizedMission.proposition?.montant_propose && (
                    <Box sx={{ 
                      mt: sanitizedMission.montant_propose && sanitizedMission.montant_propose > 0 ? 0.5 : 2, 
                      mb: 1, 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 1,
                      opacity: sanitizedMission.montant_propose && sanitizedMission.montant_propose > 0 ? 0.7 : 1
                    }}>
                      <Coins size={14} />
                      <Typography variant="body2">
                        {sanitizedMission.montant_propose && sanitizedMission.montant_propose > 0 
                          ? "Prix initial: " 
                          : "Prix: "}
                        <strong>{sanitizedMission.proposition.montant_propose} {sanitizedMission.proposition?.payment_method === 'direct_only' ? '€' : 'J'}</strong>
                        {sanitizedMission.proposition?.payment_method && (
                          <Typography component="span" variant="caption" sx={{ ml: 1, color: '#666' }}>
                            ({sanitizedMission.proposition.payment_method === 'jobi_only' 
                              ? 'Jobi uniquement' 
                              : sanitizedMission.proposition.payment_method === 'direct_only'
                                ? 'Paiement direct' 
                                : 'Mode hybride'})
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  )}
                  
                  {/* Informations sur le tarif pour mission manuelle (sans proposition) */}
                  {!sanitizedMission.proposition && sanitizedMission.montant_propose && sanitizedMission.montant_propose > 0 && (
                    <Box sx={{ mt: 2, mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Coins size={14} />
                      <Typography variant="body2">
                        Prix: <strong>{sanitizedMission.montant_propose} {sanitizedMission.payment_method === 'direct_only' ? '€' : 'J'}</strong>
                        {sanitizedMission.payment_method && (
                          <Typography component="span" variant="caption" sx={{ ml: 1, color: '#666' }}>
                            ({sanitizedMission.payment_method === 'jobi_only' 
                              ? 'Jobi uniquement' 
                              : sanitizedMission.payment_method === 'direct_only'
                                ? 'Paiement direct' 
                                : 'Mode hybride'})
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions sx={{ 
            px: { xs: 2, sm: 3 }, 
            py: { xs: 2, sm: 2 }, 
            backgroundColor: '#f9f9f9', 
            justifyContent: 'space-between',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 2, sm: 0 },
            flexShrink: 0
          }}>
            <Box sx={{ 
              display: 'flex', 
              gap: 1,
              width: { xs: '100%', sm: 'auto' },
              flexWrap: { xs: 'wrap', sm: 'nowrap' },
              '& .MuiButton-root': {
                flex: { xs: 1, sm: 'initial' },
                minWidth: { xs: 0, sm: 'auto' }
              }
            }}>
              {isHidden ? (
                <Button
                  type="button"
                  onClick={onRestore}
                  startIcon={<RefreshCw size={18} />}
                  sx={{
                    backgroundColor: '#4caf50',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#43a047',
                    },
                    textTransform: 'none',
                    fontWeight: 600,
                    borderRadius: '8px',
                    boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  Restaurer
                </Button>
              ) : (
                <Button
                  type="button"
                  color="error"
                  onClick={onDelete}
                  startIcon={<Trash2 size={18} />}
                  sx={{
                    textTransform: 'none',
                    fontWeight: 500,
                    borderRadius: '8px'
                  }}
                >
                  {!mission.mission?.id ? "Supprimer" : "Masquer"}
                </Button>
              )}
              
              {onOpenRecap && (
                <Button
                  type="button"
                  onClick={onOpenRecap}
                  startIcon={<Eye size={18} />}
                  sx={{
                    backgroundColor: '#FF965E',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#FF7A35',
                    },
                    textTransform: 'none',
                    fontWeight: 600,
                    borderRadius: '8px',
                    boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  Voir récapitulatif
                </Button>
              )}
            </Box>
            
            <Box sx={{ 
              display: 'flex', 
              gap: 1,
              width: { xs: '100%', sm: 'auto' },
              flexDirection: { xs: 'row', sm: 'row' },
              '& .MuiButton-root': {
                flex: { xs: 1, sm: 'initial' }
              }
            }}>
              <Button
                type="button"
                onClick={handleClose}
                sx={{
                  borderColor: '#ddd',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#ccc',
                    backgroundColor: 'rgba(0, 0, 0, 0.03)',
                  },
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  borderRadius: '8px'
                }}
              >
                Annuler
              </Button>
              
              {!isHidden && (
                <Button
                  type="button"
                  onClick={handleSaveButtonClick}
                  disabled={!isHidden && !!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || sanitizedMission.end_time) <= getTimeAsMinutes(watch('start_time') || sanitizedMission.start_time)}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#FF7A35',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: '#ccc',
                      color: '#666'
                    },
                    textTransform: 'none',
                    fontWeight: 600,
                    borderRadius: '8px',
                    boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  Enregistrer
                </Button>
              )}
            </Box>
          </DialogActions>
        </form>
      </Dialog>
    </ModalPortal>
  );
};

export default EditPlanningModal; 