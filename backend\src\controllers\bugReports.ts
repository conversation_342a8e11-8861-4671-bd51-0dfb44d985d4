import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import {
  BugReport,
  BugReportCreateDTO,
  BugReportUpdateDTO,
  BugReportVoteDTO
} from '../types/bugReports';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { sendNewBugReportEmail, sendBugReportCommentEmail, sendBugReportStatusChangeEmail } from '../services/emailService';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

interface BugReportComment {
  id: string;
  user_id: string;
  user_email?: string;
  message: string;
  is_admin: boolean;
  created_at: string;
}

// Constantes pour la durée du cache
const CACHE_DURATION = 300; // 5 minutes en secondes

// Fonction utilitaire pour générer la clé de cache
const generateCacheKey = (prefix: string, params: any): string => {
  return `${prefix}:${JSON.stringify(params)}`;
};

// Fonction utilitaire pour invalider le cache lié aux rapports de bugs
const invalidateBugReportCache = async (reportId?: string, userId?: string) => {
  try {
    // Récupérer toutes les clés du cache
    const keys = await redis.keys('bug_reports:*');
    
    // Filtrer les clés à supprimer
    const keysToDelete = keys.filter((key: string) => {
      // Supprimer toutes les clés liées aux rapports généraux
      if (key.startsWith('bug_reports:all:')) {
        return true;
      }
      
      // Si reportId est fourni, supprimer les clés spécifiques à ce rapport
      if (reportId && key.includes(`"reportId":"${reportId}"`)) {
        return true;
      }
      
      // Si userId est fourni, supprimer les clés spécifiques à cet utilisateur
      if (userId && key.includes(`"userId":"${userId}"`)) {
        return true;
      }
      
      return false;
    });
    
    // Supprimer les clés filtrées
    if (keysToDelete.length > 0) {
      await redis.del(...keysToDelete);
      logger.info(`Cache des rapports de bugs invalidé: ${keysToDelete.length} clés supprimées`);
    }
    
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'invalidation du cache des rapports de bugs:', error);
    return false;
  }
};

// Tableau des destinataires pour les notifications de bug reports
// Pour ajouter ou modifier les emails, il suffit de modifier ce tableau
const BUG_REPORT_NOTIFICATION_RECIPIENTS: string[] = [
  '<EMAIL>'
];

// Fonction pour envoyer une notification à un utilisateur
const sendNotification = async (userId: string, title: string, content: string, link?: string) => {
  try {
    // Insérer la notification dans la table user_notifications
    const { error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        title,
        content,
        link,
        type: 'bug_report',
        is_read: false
      });

    if (error) {
      logger.error('Erreur lors de l\'envoi de la notification:', error);
    }
  } catch (error) {
    logger.error('Erreur lors de l\'envoi de la notification:', error);
  }
};

// Récupérer tous les rapports de bug
export const getBugReports = async (req: Request, res: Response) => {
  try {
    const { 
      type, 
      category, 
      status, 
      priority, 
      search,
      user_id,
      page = 1, 
      limit = 10, 
      order = 'created_at',
      direction = 'desc',
      show_pending = false,
      include_private = false
    } = req.query;

    // Identifier l'utilisateur s'il est authentifié
    const userId = req.user?.userId || 'anonymous';
    const userRole = req.user?.role || 'anonymous';
    const isAdminOrModo = userRole === 'jobpadm' || userRole === 'jobmodo';

    // Générer une clé de cache unique basée sur tous les paramètres de la requête
    const cacheKey = generateCacheKey('bug_reports:all', {
      userId,
      userRole,
      type,
      category,
      status,
      priority,
      search,
      user_id,
      page,
      limit,
      order,
      direction,
      show_pending,
      include_private
    });

    // Vérifier si les données sont en cache
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      logger.info('Données des rapports de bugs récupérées depuis le cache Redis');
      res.json(JSON.parse(cachedData));
      return;
    }

    // Utiliser une requête simple sans jointures complexes
    let query = supabase
      .from('bug_reports')
      .select('*, votes:bug_report_votes(id, user_id, vote_type, comment, created_at)');

    // Filtres
    if (type) query = query.eq('report_type', type);
    if (category) query = query.eq('category', category);
    if (status) query = query.eq('status', status);
    if (priority) query = query.eq('priority', priority);
    if (user_id) query = query.eq('user_id', user_id);

    // Filtre pour cacher les rapports en attente de modération sauf pour les admins/modos
    // ou si demandé explicitement via le paramètre show_pending
    if (!isAdminOrModo && show_pending !== 'true') {
      // Exclure les rapports en attente de modération pour les utilisateurs normaux
      // sauf pour les rapports créés par l'utilisateur courant
      query = query.not('status', 'eq', 'attente_moderation');
    }

    // Filtre pour cacher les rapports privés sauf pour les admins/modos
    // ou le propriétaire du rapport
    if (!isAdminOrModo && include_private !== 'true') {
      if (userId !== 'anonymous') {
        // Si l'utilisateur est connecté, il peut voir ses propres rapports privés
        query = query.or(`is_private.eq.false,user_id.eq.${userId}`);
      } else {
        // Utilisateur anonyme: ne voit que les rapports publics
        query = query.eq('is_private', false);
      }
    }

    // Filtre de recherche
    if (search) {
      const searchTerm = String(search).trim().toLowerCase();
      // Recherche dans le titre ou la description avec ilike (insensible à la casse)
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    query = query.range(startIndex, startIndex + Number(limit) - 1);

    // Tri
    query = query.order(order as string, { ascending: direction === 'asc' });

    // Exécuter la requête
    const { data, error, count } = await query;

    if (error) {
      logger.error(`Erreur lors de la récupération des rapports de bug: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la récupération des rapports de bug' });
    }

    // Enrichir les données avec les informations utilisateur
    const enrichedData = await Promise.all((data || []).map(async (report) => {
      // Récupérer les informations de l'auteur du rapport
      if (report.user_id) {
        const { data: userData } = await supabase
          .from('users')
          .select('id, email, role, user_profil:user_profil(nom, prenom)')
          .eq('id', report.user_id)
          .single();

        if (userData) {
          // Déchiffrer les données utilisateur et profil
          const decryptedUserData = await decryptUserDataAsync(userData);
          if (userData.user_profil && userData.user_profil.length > 0) {
            const decryptedProfilData = await decryptProfilDataAsync(userData.user_profil[0]);
            decryptedUserData.user_profil = [decryptedProfilData];
          }
          report.user = decryptedUserData;
        }
      }

      // Récupérer les informations de l'utilisateur assigné
      if (report.assigned_to) {
        const { data: assignedData } = await supabase
          .from('users')
          .select('id, email, role, user_profil:user_profil(nom, prenom)')
          .eq('id', report.assigned_to)
          .single();

        if (assignedData) {
          // Déchiffrer les données utilisateur et profil
          const decryptedAssignedData = await decryptUserDataAsync(assignedData);
          if (assignedData.user_profil && assignedData.user_profil.length > 0) {
            const decryptedProfilData = await decryptProfilDataAsync(assignedData.user_profil[0]);
            decryptedAssignedData.user_profil = [decryptedProfilData];
          }
          report.assigned = decryptedAssignedData;
        }
      }

      return report;
    }));

    // Formater les données pour la réponse
    const response = {
      data: enrichedData || [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0
      }
    };

    // Mettre en cache les résultats pour 5 minutes
    await redis.set(cacheKey, JSON.stringify(response), 'EX', CACHE_DURATION);
    logger.info('Données des rapports de bugs mises en cache');

    // Renvoyer les données
    res.json(response);
  } catch (error) {
    logger.error('Erreur lors de la récupération des rapports de bug:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Récupérer un rapport de bug par son ID
export const getBugReportById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    // Identifier l'utilisateur s'il est authentifié
    const userId = req.user?.userId || 'anonymous';
    const userRole = req.user?.role || 'anonymous';
    const isAdminOrModo = userRole === 'jobpadm' || userRole === 'jobmodo';

    // Générer une clé de cache unique pour ce rapport
    const cacheKey = generateCacheKey('bug_reports:detail', {
      reportId: id,
      userId,
      userRole
    });

    // Vérifier si les données sont en cache
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      logger.info(`Détails du rapport de bug #${id} récupérés depuis le cache Redis`);
      return res.json(JSON.parse(cachedData));
    }

    // Utiliser une requête simple sans jointures complexes
    const { data: report, error } = await supabase
      .from('bug_reports')
      .select('*, votes:bug_report_votes(id, user_id, vote_type, comment, created_at)')
      .eq('id', id)
      .single();

    if (error) {
      logger.error(`Erreur lors de la récupération du rapport de bug: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la récupération du rapport de bug' });
    }

    if (!report) {
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Vérifier les permissions pour les rapports privés
    if (report.is_private && !isAdminOrModo && report.user_id !== userId) {
      return res.status(403).json({ error: 'Vous n\'avez pas l\'autorisation de consulter ce rapport privé' });
    }

    // Vérifier les permissions pour les rapports en attente de modération
    if (report.status === 'attente_moderation' && !isAdminOrModo && report.user_id !== userId) {
      return res.status(403).json({ error: 'Ce rapport est en attente de modération et n\'est pas encore public' });
    }

    // Récupérer les informations de l'utilisateur qui a créé le rapport
    if (report.user_id) {
      const { data: userData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', report.user_id)
        .single();

      if (userData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedUserData = await decryptUserDataAsync(userData);
        if (userData.user_profil && userData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(userData.user_profil[0]);
          decryptedUserData.user_profil = [decryptedProfilData];
        }
        report.user = decryptedUserData;
      }
    }

    // Récupérer les informations de l'utilisateur assigné
    if (report.assigned_to) {
      const { data: assignedData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', report.assigned_to)
        .single();

      if (assignedData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedAssignedData = await decryptUserDataAsync(assignedData);
        if (assignedData.user_profil && assignedData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(assignedData.user_profil[0]);
          decryptedAssignedData.user_profil = [decryptedProfilData];
        }
        report.assigned = decryptedAssignedData;
      }
    }

    // Mettre en cache les résultats pour 5 minutes
    await redis.set(cacheKey, JSON.stringify(report), 'EX', CACHE_DURATION);
    logger.info(`Détails du rapport de bug #${id} mis en cache`);

    return res.json(report);
  } catch (error) {
    logger.error('Erreur lors de la récupération du rapport de bug:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Fonction pour notifier les destinataires par email d'un nouveau rapport de bug
const notifyRecipientsByEmail = async (report: BugReport, reporterEmail?: string) => {
  try {
    // Vérifier si un email a déjà été envoyé pour ce rapport dans la dernière heure
    const lastEmailKey = `bug_report:${report.id}:last_email`;
    const lastEmailTime = await redis.get(lastEmailKey);
    
    let shouldSendEmail = true;

    if (lastEmailTime) {
      const timeSinceLastEmail = Date.now() - parseInt(lastEmailTime);
      if (timeSinceLastEmail < 3600000) { // 1 heure en millisecondes
        logger.info(`Email non envoyé pour le rapport #${report.id} - Dernier email envoyé il y a moins d'une heure`);
        shouldSendEmail = false;
      }
    }

    if (shouldSendEmail) {
      // Récupérer les emails des destinataires depuis le tableau
      const recipientEmails = BUG_REPORT_NOTIFICATION_RECIPIENTS;
      
      if (recipientEmails.length === 0) {
        logger.info('Aucun destinataire configuré pour les notifications de rapports de bug');
        return;
      }

      // Préparer les données pour l'email
      const typeReport = report.report_type === 'bug' ? 'bug' : 'amélioration';
      
      // Envoyer un email à chaque destinataire
      for (const email of recipientEmails) {
        try {
          await sendNewBugReportEmail(email, {
            reportTitle: report.title,
            reportDescription: report.description,
            reportType: report.report_type,
            category: report.category,
            priority: report.priority,
            reportId: report.id,
            reporterEmail,
            isPrivate: report.is_private
        });
          logger.info(`Email de notification envoyé à ${email} pour le rapport #${report.id}`);
        } catch (error) {
          logger.error(`Erreur lors de l'envoi de l'email à ${email}:`, error);
        }
      }

      // Mettre à jour le timestamp du dernier email envoyé
      await redis.set(lastEmailKey, Date.now().toString(), 'EX', 3600); // Expire après 1 heure
    }
  } catch (error) {
    logger.error('Erreur lors de la notification des destinataires par email:', error);
  }
};

// Fonction pour notifier d'un nouveau commentaire
const notifyForNewComment = async (reportId: string, comment: BugReportComment) => {
  try {
    // Vérifier si un email a déjà été envoyé pour ce rapport dans les 5 dernières minutes
    const lastEmailKey = `bug_report:${reportId}:comment:last_email`;
    const lastEmailTime = await redis.get(lastEmailKey);
    
    let shouldSendEmail = true;

    if (lastEmailTime) {
      const timeSinceLastEmail = Date.now() - parseInt(lastEmailTime);
      if (timeSinceLastEmail < 300000) { // 5 minutes en millisecondes
        logger.info(`Email non envoyé pour le commentaire sur le rapport #${reportId} - Dernier email envoyé il y a moins de 5 minutes`);
        shouldSendEmail = false;
      }
    }

    if (shouldSendEmail) {
      // Récupérer le rapport
      const { data: report, error: reportError } = await supabase
        .from('bug_reports')
        .select('title, user_id')
        .eq('id', reportId)
        .single();

      if (reportError || !report) {
        logger.error('Erreur lors de la récupération du rapport:', reportError);
        return;
      }

      // Si le commentaire est fait par un admin, notifier le propriétaire du rapport
      if (comment.is_admin && report.user_id) {
        const { data: user, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', report.user_id)
          .single();

        if (!userError && user) {
          try {
            await sendBugReportCommentEmail(user.email, {
              reportTitle: report.title,
              commentMessage: comment.message,
              reportId,
              commenterId: comment.user_id,
              commenterEmail: 'Support JobPartiel',
              isAdmin: true
            });
            logger.info(`Email de notification envoyé à ${user.email} pour le commentaire sur le rapport #${reportId}`);
          } catch (error) {
            logger.error(`Erreur lors de l'envoi de l'email à ${user.email}:`, error);
          }
        }
      } else {
        // Récupérer le nom et prénom de l'utilisateur qui a commenté
        const { data: commenter, error: commenterError } = await supabase
          .from('users')
          .select(`
            email,
            user_profil:user_profil(nom, prenom)
          `)
          .eq('id', comment.user_id)
          .single();

        // Formatter le nom (Prénom N.)
        let commenterName = comment.user_email || 'Utilisateur anonyme';

        if (commenter?.user_profil && commenter.user_profil.length > 0) {
          // Déchiffrer les données du profil
          const decryptedProfilData = await decryptProfilDataAsync(commenter.user_profil[0]);
          const prenom = decryptedProfilData.prenom || '';
          const nom = decryptedProfilData.nom || '';

          if (prenom || nom) {
            commenterName = `${prenom} ${nom ? nom.charAt(0) + '.' : ''}`.trim();
          }
        }
        
        // Si le commentaire est fait par un utilisateur, notifier les admins
        for (const email of BUG_REPORT_NOTIFICATION_RECIPIENTS) {
          try {
            await sendBugReportCommentEmail(email, {
              reportTitle: report.title,
              commentMessage: comment.message,
              reportId,
              commenterId: comment.user_id,
              commenterEmail: commenterName,
              isAdmin: false
            });
            logger.info(`Email de notification envoyé à ${email} pour le commentaire sur le rapport #${reportId}`);
          } catch (error) {
            logger.error(`Erreur lors de l'envoi de l'email à ${email}:`, error);
          }
        }
      }

      // Mettre à jour le timestamp du dernier email envoyé
      await redis.set(lastEmailKey, Date.now().toString(), 'EX', 300); // Expire après 5 minutes
    }
  } catch (error) {
    logger.error('Erreur lors de la notification du commentaire:', error);
  }
};

// Créer un nouveau rapport de bug
export const createBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    const reportData: BugReportCreateDTO = req.body;
    
    // Vérifier si le rapport est privé (explicitement converti en booléen)
    const isPrivate = reportData.is_private === true;

    // Validation de base
    if (!reportData.title || !reportData.description || !reportData.report_type || !reportData.category || !reportData.priority) {
      return res.status(400).json({ error: 'Veuillez remplir tous les champs obligatoires' });
    }

    // Récupérer l'email et le profil de l'utilisateur pour la notification
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        email,
        user_profil:user_profil(
          nom,
          prenom
        )
      `)
      .eq('id', userId)
      .single();

    let userEmail = null;
    let userPrenom = null;
    let userNom = null;

    if (user) {
      userEmail = user.email;

      if (user.user_profil && user.user_profil.length > 0) {
        // Déchiffrer les données du profil
        const decryptedProfilData = await decryptProfilDataAsync(user.user_profil[0]);
        userPrenom = decryptedProfilData.prenom || null;
        userNom = decryptedProfilData.nom || null;
      }
    }

    // Formatter le nom (Prénom N.)
    let userDisplayName = userEmail;
    if (userPrenom || userNom) {
      userDisplayName = `${userPrenom || ''} ${userNom ? userNom.charAt(0) + '.' : ''}`.trim();
    }

    // Insertion dans la base de données
    const { data, error } = await supabase
      .from('bug_reports')
      .insert({
        ...reportData,
        user_id: userId,
        status: 'attente_moderation',
        is_private: isPrivate
      })
      .select()
      .single();

    if (error) {
      logger.error(`Erreur lors de la création du rapport de bug: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la création du rapport de bug' });
    }

    // Ajouter l'entrée dans l'historique pour la création
    const historyEntry = {
      bug_report_id: data.id,
      updated_by: userId,
      old_status: null,
      new_status: 'attente_moderation',
      comment: 'Création du rapport',
      action_type: 'creation'
    };

    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    if (historyError) {
      logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
    }

    // Journaliser l'action de création d'un rapport de bug
    if (!userId) return res.status(401).json({ error: 'Authentification requise' });
    await logUserActivity(
      userId as string,
      'bug_report_create',
      data.id,
      'bug_report',
      { 
        title: data.title,
        report_type: data.report_type,
        category: data.category,
        priority: data.priority,
        is_private: isPrivate
      },
      getIpFromRequest(req)
    );

    // Message de confirmation personnalisé selon la visibilité du rapport
    const privacyMessage = isPrivate 
      ? "Ce rapport est privé et ne sera visible que par vous et l'équipe de modération."
      : "Ce rapport sera visible publiquement après modération.";

    // Notifier les administrateurs par notification interne
    await notifyAdminsAboutNewReport(data);
    
    // Notifier les administrateurs par email pour modération
    await notifyRecipientsByEmail(data, userDisplayName);

    // Notifier l'utilisateur qui a créé le rapport
    if (!userId) return res.status(401).json({ error: 'Authentification requise' });
    await sendNotification(
      userId as string,
      `Votre rapport a été soumis avec succès`,
      `Votre rapport "${data.title}" a été soumis avec succès. ${privacyMessage}`,
      `/dashboard/bug-reports/${data.id}`
    );

    // Après avoir créé le rapport avec succès, invalider le cache des rapports
    await invalidateBugReportCache();

    return res.status(201).json({
      ...data,
      message: `Rapport créé avec succès. ${privacyMessage}`
    });
  } catch (error) {
    logger.error('Erreur lors de la création du rapport de bug:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Mettre à jour un rapport de bug
export const updateBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    const updateData = req.body as any; // Utiliser any temporairement pour accéder aux propriétés

    // Vérifier les droits d'accès
    if (!userId) {
      logger.warn('Authentification requise');
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Récupérer le rapport existant
    const { data: existingReport, error: fetchError } = await supabase
      .from('bug_reports')
      .select('user_id, status, title, admin_comment, assigned_to') // Ajout des champs manquants
      .eq('id', id)
      .single();

    if (fetchError) {
      logger.error(`Erreur lors de la récupération du rapport existant: ${JSON.stringify(fetchError)}`);
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Vérifier que l'utilisateur est l'auteur du rapport ou un administrateur
    if (existingReport.user_id !== userId && userRole !== 'jobpadm') {
      logger.warn(`Accès non autorisé: user_id=${userId}, rapport user_id=${existingReport.user_id}, role=${userRole}`);
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à modifier ce rapport' });
    }

    // Si l'utilisateur n'est pas admin et qu'il est l'auteur du rapport, supprimer l'option is_private
    if (userRole !== 'jobpadm' && existingReport.user_id === userId) {
      delete updateData.is_private;
    }

    // Si le statut change, stocker l'ancien statut pour notifier
    const statusChanged = updateData.status && updateData.status !== existingReport.status;
    const oldStatus = existingReport.status;

    // Si le rapport est résolu, ajouter la date de résolution
    if (updateData.status === 'resolu' && existingReport.status !== 'resolu') {
      updateData.resolved_at = new Date().toISOString();
    }

    // Si le statut change, enregistrer dans l'historique
    if (statusChanged) {
      const historyEntry = {
        bug_report_id: id,
        updated_by: userId,
        old_status: oldStatus,
        new_status: updateData.status,
        comment: updateData.admin_comment || `Statut modifié de ${oldStatus} à ${updateData.status}`
      };

      const { error: historyError } = await supabase
        .from('bug_report_history')
        .insert(historyEntry);

      if (historyError) {
        logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
      }
    }

    // Enregistrer les autres modifications dans l'historique
    interface Changes {
      [key: string]: {
        old: any;
        new: any;
      };
    }

    const changes: Changes = {};
    const fieldsToTrack = [
      'title', 'description', 'report_type', 'category', 
      'priority', 'reproduction_steps', 'browser_info', 
      'os_info', 'assigned_to', 'is_private'
    ] as const;

    fieldsToTrack.forEach(field => {
      if (updateData[field] !== undefined && updateData[field] !== (existingReport as any)[field]) {
        changes[field] = {
          old: (existingReport as any)[field],
          new: updateData[field]
        };
      }
    });

    // Si d'autres champs que le statut ont été modifiés, enregistrer dans l'historique
    if (Object.keys(changes).length > 0) {
      const historyEntry = {
        bug_report_id: id,
        updated_by: userId,
        old_status: existingReport.status,
        new_status: existingReport.status, // Pas de changement de statut
        comment: `Modification des champs : ${Object.keys(changes).join(', ')}. Détails : ${JSON.stringify(changes)}`
      };

      const { error: updateHistoryError } = await supabase
        .from('bug_report_history')
        .insert(historyEntry);

      if (updateHistoryError) {
        logger.error(`Erreur lors de l'enregistrement de l'historique des modifications: ${JSON.stringify(updateHistoryError)}`);
      }
    }

    // Filtrer les données pour ne garder que les champs valides
    const validColumns = [
      'title', 'description', 'report_type', 'category', 'priority', 
      'status', 'admin_comment', 'reproduction_steps', 'browser_info', 
      'os_info', 'assigned_to', 'resolved_at'
    ];

    // Ajouter is_private aux colonnes valides si l'utilisateur est admin
    if (userRole === 'jobpadm' || userRole === 'jobmodo') {
      validColumns.push('is_private');
    }

    // Créer un objet filtré conforme à BugReportUpdateDTO
    const filteredUpdateData: Partial<BugReportUpdateDTO> = {};
    
    if (updateData.title !== undefined) filteredUpdateData.title = updateData.title;
    if (updateData.description !== undefined) filteredUpdateData.description = updateData.description;
    if (updateData.report_type !== undefined) filteredUpdateData.report_type = updateData.report_type;
    if (updateData.category !== undefined) filteredUpdateData.category = updateData.category;
    if (updateData.priority !== undefined) filteredUpdateData.priority = updateData.priority;
    if (updateData.status !== undefined) filteredUpdateData.status = updateData.status;
    if (updateData.admin_comment !== undefined) filteredUpdateData.admin_comment = updateData.admin_comment;
    if (updateData.reproduction_steps !== undefined) filteredUpdateData.reproduction_steps = updateData.reproduction_steps;
    if (updateData.browser_info !== undefined) filteredUpdateData.browser_info = updateData.browser_info;
    if (updateData.os_info !== undefined) filteredUpdateData.os_info = updateData.os_info;
    if (updateData.assigned_to !== undefined) filteredUpdateData.assigned_to = updateData.assigned_to;
    if (updateData.resolved_at !== undefined) filteredUpdateData.resolved_at = updateData.resolved_at;
    // Seuls les administrateurs peuvent modifier le statut privé
    if (userRole === 'jobpadm' && updateData.is_private !== undefined) {
      filteredUpdateData.is_private = updateData.is_private;
    }

    // Mise à jour du rapport avec les données filtrées
    const { error } = await supabase
      .from('bug_reports')
      .update(filteredUpdateData)
      .eq('id', id);

    if (error) {
      logger.error(`Erreur lors de la mise à jour du rapport: ${JSON.stringify(error)}`);
      return res.status(500).json({ error: 'Erreur lors de la mise à jour du rapport: ' + error.message });
    }
    
    // 2. Récupérer les données mises à jour séparément
    const { data: updatedReport, error: getError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (getError) {
      logger.error(`Erreur lors de la récupération du rapport mis à jour: ${JSON.stringify(getError)}`);
      return res.status(500).json({ error: 'Erreur lors de la récupération du rapport mis à jour' });
    }
    
    // Créer un objet pour stocker le résultat final
    const resultReport = { ...updatedReport };

    if (updatedReport) {
      // Récupérer les informations de l'utilisateur qui a créé le rapport
      if (updatedReport.user_id) {
        logger.info(`Récupération des infos utilisateur pour l'ID: ${updatedReport.user_id}`);
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, email, role, user_profil:user_profil(nom, prenom)')
          .eq('id', updatedReport.user_id)
          .single();

        if (userError) {
          logger.error(`Erreur lors de la récupération des infos utilisateur: ${JSON.stringify(userError)}`);
        } else if (userData) {
          logger.info(`Infos utilisateur récupérées: ${JSON.stringify(userData)}`);
          // Déchiffrer les données utilisateur et profil
          const decryptedUserData = await decryptUserDataAsync(userData);
          if (userData.user_profil && userData.user_profil.length > 0) {
            const decryptedProfilData = await decryptProfilDataAsync(userData.user_profil[0]);
            decryptedUserData.user_profil = [decryptedProfilData];
          }
          resultReport.user = decryptedUserData;
        }
      }

      // Récupérer les informations de l'utilisateur assigné
      if (updatedReport.assigned_to) {
        logger.info(`Récupération des infos assigné pour l'ID: ${updatedReport.assigned_to}`);
        const { data: assignedData, error: assignedError } = await supabase
          .from('users')
          .select('id, email, role, user_profil:user_profil(nom, prenom)')
          .eq('id', updatedReport.assigned_to)
          .single();

        if (assignedError) {
          logger.error(`Erreur lors de la récupération des infos assigné: ${JSON.stringify(assignedError)}`);
        } else if (assignedData) {
          logger.info(`Infos assigné récupérées: ${JSON.stringify(assignedData)}`);
          // Déchiffrer les données utilisateur et profil
          const decryptedAssignedData = await decryptUserDataAsync(assignedData);
          if (assignedData.user_profil && assignedData.user_profil.length > 0) {
            const decryptedProfilData = await decryptProfilDataAsync(assignedData.user_profil[0]);
            decryptedAssignedData.user_profil = [decryptedProfilData];
          }
          resultReport.assigned = decryptedAssignedData;
        }
      }

      // Si le statut a changé, notifier l'utilisateur
      if (statusChanged) {
        logger.info('Notification du changement de statut...');
        try {
          await notifyUserAboutStatusChange(resultReport, oldStatus);
          logger.info('Notification envoyée avec succès');
        } catch (notifyError) {
          logger.error(`Erreur lors de la notification: ${JSON.stringify(notifyError)}`);
        }
      }
    }

    // Après la mise à jour, invalider le cache pour ce rapport et les listes
    await invalidateBugReportCache(id, req.user?.userId);

    // Journaliser l'action de mise à jour du rapport de bug
    if (!userId) return res.status(401).json({ error: 'Authentification requise' });
    await logUserActivity(
      userId as string,
      'bug_report_update',
      id,
      'bug_report',
      { 
        ...filteredUpdateData,
        statusChanged: statusChanged ? `${oldStatus} -> ${filteredUpdateData.status}` : null
      },
      getIpFromRequest(req)
    );

    return res.json(resultReport);
  } catch (error) {
    logger.error(`Erreur globale lors de la mise à jour du rapport: ${JSON.stringify(error)}`);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Supprimer un rapport de bug
export const deleteBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;

    // Vérifier les droits d'accès
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Récupérer le rapport existant
    const { data: existingReport, error: fetchError } = await supabase
      .from('bug_reports')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError || !existingReport) {
      logger.error(`Erreur lors de la récupération du rapport existant: ${fetchError?.message}`, { fetchError });
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Vérifier que l'utilisateur est l'auteur du rapport ou un administrateur
    if (existingReport.user_id !== userId && userRole !== 'jobpadm') {
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à supprimer ce rapport' });
    }

    // Supprimer le rapport
    const { error } = await supabase
      .from('bug_reports')
      .delete()
      .eq('id', id);

    if (error) {
      logger.error(`Erreur lors de la suppression du rapport: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la suppression du rapport' });
    }

    // Après la suppression, invalider le cache
    await invalidateBugReportCache(id, req.user?.userId);

    // Journaliser l'action de suppression du rapport de bug
    if (!userId) return res.status(401).json({ error: 'Authentification requise' });
    await logUserActivity(
      userId as string,
      'bug_report_delete',
      id,
      'bug_report',
      { message: 'Rapport supprimé' },
      getIpFromRequest(req)
    );

    return res.json({ message: 'Rapport supprimé avec succès' });
  } catch (error) {
    logger.error('Erreur lors de la suppression du rapport:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Voter pour un rapport de bug
export const voteBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const { id } = req.params;
    const voteData: BugReportVoteDTO = req.body;

    // Vérifier l'authentification
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Vérifier que le rapport existe
    const { data: report, error: reportError } = await supabase
      .from('bug_reports')
      .select('id, report_type')
      .eq('id', id)
      .single();

    if (reportError || !report) {
      logger.error(`Erreur lors de la vérification du rapport: ${reportError?.message}`, { reportError });
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Vérifier que c'est une suggestion d'amélioration
    if (report.report_type !== 'improvement') {
      return res.status(400).json({ error: 'Seules les suggestions d\'amélioration peuvent être votées' });
    }

    // Vérifier si l'utilisateur a déjà voté
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('bug_report_votes')
      .select('id, vote_type')
      .eq('bug_report_id', id)
      .eq('user_id', userId)
      .maybeSingle();

    if (voteCheckError) {
      logger.error(`Erreur lors de la vérification du vote existant: ${voteCheckError.message}`, { voteCheckError });
      return res.status(500).json({ error: 'Erreur lors de la vérification du vote existant' });
    }

    let voteResult;

    // Si l'utilisateur a déjà voté, mettre à jour son vote
    if (existingVote) {
      const { data, error } = await supabase
        .from('bug_report_votes')
        .update({
          vote_type: voteData.vote_type,
          comment: voteData.comment,
          created_at: new Date().toISOString()
        })
        .eq('id', existingVote.id)
        .select()
        .single();

      if (error) {
        logger.error(`Erreur lors de la mise à jour du vote: ${error.message}`, { error });
        return res.status(500).json({ error: 'Erreur lors de la mise à jour du vote' });
      }

      voteResult = data;
    } else {
      // Sinon, créer un nouveau vote
      const { data, error } = await supabase
        .from('bug_report_votes')
        .insert({
          bug_report_id: id,
          user_id: userId,
          vote_type: voteData.vote_type,
          comment: voteData.comment
        })
        .select()
        .single();

      if (error) {
        logger.error(`Erreur lors de la création du vote: ${error.message}`, { error });
        return res.status(500).json({ error: 'Erreur lors de la création du vote' });
      }

      voteResult = data;
    }

    // Récupérer le nombre de votes
    const { data: voteCount, error: countError } = await supabase.rpc('get_bug_report_votes', { report_id: id });

    if (countError) {
      logger.error(`Erreur lors du comptage des votes: ${countError.message}`, { countError });
      return res.status(500).json({ error: 'Erreur lors du comptage des votes' });
    }

    // Dans la fonction voteBugReport, après le vote réussi
    if (voteResult) {
      // Ajouter l'entrée dans l'historique pour le vote
      const historyEntry = {
        bug_report_id: id,
        updated_by: userId,
        old_status: null,
        new_status: null,
        comment: `Vote ${voteData.vote_type} ajouté${voteData.comment ? ' avec commentaire' : ''}`,
        action_type: 'vote',
        details: {
          vote_type: voteData.vote_type,
          has_comment: !!voteData.comment
        }
      };

      const { error: historyError } = await supabase
        .from('bug_report_history')
        .insert(historyEntry);

      if (historyError) {
        logger.error(`Erreur lors de l'enregistrement de l'historique du vote: ${JSON.stringify(historyError)}`);
      }
    }

    return res.json({
      vote: voteResult,
      vote_count: voteCount[0]
    });
  } catch (error) {
    logger.error('Erreur lors du vote:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Supprimer un vote
export const deleteVote = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const { id } = req.params;

    // Vérifier l'authentification
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Supprimer le vote
    const { error } = await supabase
      .from('bug_report_votes')
      .delete()
      .eq('bug_report_id', id)
      .eq('user_id', userId);

    if (error) {
      logger.error(`Erreur lors de la suppression du vote: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la suppression du vote' });
    }

    // Récupérer le nombre de votes mis à jour
    const { data: voteCount, error: countError } = await supabase.rpc('get_bug_report_votes', { report_id: id });

    if (countError) {
      logger.error(`Erreur lors du comptage des votes: ${countError.message}`, { countError });
      return res.status(500).json({ error: 'Erreur lors du comptage des votes' });
    }

    // Dans la fonction deleteVote, après la suppression du vote
    if (!error) {
      // Ajouter l'entrée dans l'historique pour la suppression du vote
      const historyEntry = {
        bug_report_id: id,
        updated_by: userId,
        old_status: null,
        new_status: null,
        comment: 'Vote supprimé',
        action_type: 'vote_delete'
      };

      const { error: historyError } = await supabase
        .from('bug_report_history')
        .insert(historyEntry);

      if (historyError) {
        logger.error(`Erreur lors de l'enregistrement de l'historique de suppression du vote: ${JSON.stringify(historyError)}`);
      }
    }

    return res.json({
      success: true,
      message: 'Vote supprimé avec succès',
      vote_count: voteCount[0]
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression du vote:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Récupérer l'historique d'un rapport de bug
export const getBugReportHistory = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;

    // Calculer l'offset pour la pagination
    const startIndex = (Number(page) - 1) * Number(limit);

    const { data, error, count } = await supabase
      .from('bug_report_history')
      .select(`
        *,
        updater:users!updated_by(id, email)
      `, { count: 'exact' })
      .eq('bug_report_id', id)
      .order('created_at', { ascending: false })
      .range(startIndex, startIndex + Number(limit) - 1);

    if (error) {
      logger.error(`Erreur lors de la récupération de l'historique: ${error.message}`, { error });
      return res.status(500).json({ error: 'Erreur lors de la récupération de l\'historique' });
    }

    return res.json({
      data: data || [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / Number(limit))
      }
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Récupérer les statistiques des rapports de bug
export const getBugReportStats = async (req: Request, res: Response) => {
  try {
    // Statistiques par statut en utilisant la bonne syntaxe d'agrégation de Supabase
    const { data: statusData, error: statusError } = await supabase
      .from('bug_reports')
      .select('status')
      .order('status');

    if (statusError) {
      logger.error(`Erreur lors de la récupération des statistiques par statut: ${statusError.message}`, { statusError });
      return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }

    // Traiter les données pour obtenir les statistiques par statut
    const statusStats = statusData ? processStatsData(statusData, 'status') : [];

    // Statistiques par type
    const { data: typeData, error: typeError } = await supabase
      .from('bug_reports')
      .select('report_type')
      .order('report_type');

    if (typeError) {
      logger.error(`Erreur lors de la récupération des statistiques par type: ${typeError.message}`, { typeError });
      return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }

    // Traiter les données pour obtenir les statistiques par type
    const typeStats = typeData ? processStatsData(typeData, 'report_type') : [];

    // Statistiques par catégorie
    const { data: categoryData, error: categoryError } = await supabase
      .from('bug_reports')
      .select('category')
      .order('category');

    if (categoryError) {
      logger.error(`Erreur lors de la récupération des statistiques par catégorie: ${categoryError.message}`, { categoryError });
      return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }

    // Traiter les données pour obtenir les statistiques par catégorie
    const categoryStats = categoryData ? processStatsData(categoryData, 'category') : [];

    // Statistiques par priorité
    const { data: priorityData, error: priorityError } = await supabase
      .from('bug_reports')
      .select('priority')
      .order('priority');

    if (priorityError) {
      logger.error(`Erreur lors de la récupération des statistiques par priorité: ${priorityError.message}`, { priorityError });
      return res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
    }

    // Traiter les données pour obtenir les statistiques par priorité
    const priorityStats = priorityData ? processStatsData(priorityData, 'priority') : [];

    return res.json({
      status: statusStats,
      type: typeStats,
      category: categoryStats,
      priority: priorityStats
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Fonction utilitaire pour traiter les données et générer les statistiques
function processStatsData(data: any[], field: string) {
  const counts: Record<string, number> = {};
  
  // Compter les occurrences de chaque valeur
  data.forEach(item => {
    const value = item[field] || 'undefined';
    counts[value] = (counts[value] || 0) + 1;
  });
  
  // Formater les données pour le frontend
  return Object.entries(counts).map(([key, count]) => ({
    [field]: key,
    count
  }));
}

// Fonctions utilitaires

// Notifier les administrateurs d'un nouveau rapport de bug
async function notifyAdminsAboutNewReport(report: BugReport) {
  try {
    // Récupérer les administrateurs
    const { data: admins, error } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'jobpadm');

    if (error || !admins) {
      logger.error(`Erreur lors de la récupération des administrateurs: ${error?.message}`, { error });
      return;
    }

    // Notifier chaque administrateur
    for (const admin of admins) {
      await sendNotification(
        admin.id,
        `Nouveau rapport de bug: ${report.title}`,
        `Un nouveau rapport de bug a été créé: ${report.description.substring(0, 100)}...`,
        `/dashboard/bug-reports/${report.id}`
      );
    }
  } catch (error) {
    logger.error('Erreur lors de la notification des administrateurs:', error);
  }
}

// Notifier l'utilisateur du changement de statut de son rapport
async function notifyUserAboutStatusChange(report: BugReport, oldStatus?: string) {
  try {
    if (!report.user_id) return;

    // Titre de la notification selon le statut
    let title = 'Mise à jour de votre rapport de bug';
    let message = '';

    switch (report.status) {
      case 'en_cours':
        title = 'Votre rapport est en cours de traitement';
        message = `Notre équipe a commencé à travailler sur votre rapport "${report.title}".`;
        break;
      case 'resolu':
        title = 'Votre rapport a été résolu';
        message = `Bonne nouvelle ! Votre rapport "${report.title}" a été résolu par notre équipe.`;
        break;
      case 'rejete':
        title = 'Votre rapport n\'a pas été retenu';
        message = `Nous sommes désolés, mais votre rapport "${report.title}" n'a pas été retenu.`;
        break;
      case 'ferme':
        title = 'Votre rapport a été fermé';
        message = `Votre rapport "${report.title}" a été fermé.`;
        break;
      case 'reouvert':
        title = 'Votre rapport a été réouvert';
        message = `Votre rapport "${report.title}" a été réouvert pour un examen complémentaire.`;
        break;
      case 'nouveau':
        title = 'Votre rapport a été approuvé et publié';
        message = `Votre rapport "${report.title}" a été approuvé par notre équipe et est maintenant visible publiquement.`;
        break;
      case 'attente_moderation':
        title = 'Votre rapport est en attente de modération';
        message = `Votre rapport "${report.title}" est en attente de validation par notre équipe avant d'être publié.`;
        break;
      default:
        message = `Le statut de votre rapport "${report.title}" a été mis à jour.`;
    }

    // Ajouter le commentaire de l'administrateur s'il existe
    if (report.admin_comment) {
      message += ` Commentaire de l'équipe : "${report.admin_comment}"`;
    }

    // Créer la notification interne
    await sendNotification(
      report.user_id,
      title,
      message,
      `/dashboard/bug-reports/${report.id}`
    );

    // Envoyer également une notification par email
    // Récupérer l'email de l'utilisateur
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', report.user_id)
      .single();

    if (userError) {
      logger.error(`Erreur lors de la récupération des informations de l'utilisateur: ${JSON.stringify(userError)}`);
      return;
    }

    // Formater le statut pour le message email (première lettre en majuscule)
    const statusDisplay: Record<string, string> = {
      'en_cours': 'En cours',
      'resolu': 'Résolu',
      'rejete': 'Rejeté',
      'ferme': 'Fermé',
      'reouvert': 'Réouvert',
      'nouveau': 'Nouveau',
      'attente_moderation': 'En attente de modération'
    };
    
    const displayStatus = statusDisplay[report.status] || report.status.charAt(0).toUpperCase() + report.status.slice(1);
    const oldDisplayStatus = oldStatus ? (statusDisplay[oldStatus] || oldStatus.charAt(0).toUpperCase() + oldStatus.slice(1)) : 'Précédent';

    // Toujours envoyer un email quelle que soit la configuration
    if (user && user.email) {
      try {
        await sendBugReportStatusChangeEmail(user.email, {
          reportTitle: report.title,
          oldStatus: oldDisplayStatus,
          newStatus: displayStatus,
          adminComment: report.admin_comment || '',
          reportId: report.id
        });
        logger.info(`Email de notification de changement de statut envoyé à ${user.email} pour le rapport ${report.id}`);
      } catch (error) {
        logger.error(`Erreur lors de l'envoi de l'email: ${JSON.stringify(error)}`);
      }
    } else {
      logger.warn(`Pas d'email disponible pour l'utilisateur ${report.user_id}`);
    }
  } catch (error) {
    logger.error('Erreur lors de la notification de l\'utilisateur:', error);
  }
}

export const getBugReportComments = async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.user?.userId;
  const { page = 1, limit = 10 } = req.query;

  if (!userId) {
    return res.status(401).json({ error: 'Utilisateur non authentifié' });
  }

  try {
    // Vérifier si le rapport existe
    const report = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (report.error) {
      logger.error('Erreur lors de la récupération du rapport:', report.error);
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }

    // Calculer l'offset pour la pagination
    const startIndex = (Number(page) - 1) * Number(limit);

    // Récupérer les commentaires avec pagination
    const { data: comments, error, count } = await supabase
      .from('bug_report_comments')
      .select(`
        *,
        user:users(
          id, 
          email,
          role,
          user_profil:user_profil(nom, prenom)
        ),
        parent:bug_report_comments!parent_comment_id(
          id,
          message,
          user_id,
          is_admin,
          parent_user:users!bug_report_comments_user_id_fkey(
            id, 
            email,
            role,
            user_profil:user_profil(nom, prenom)
          )
        )
      `, { count: 'exact' })
      .eq('bug_report_id', id)
      .order('created_at', { ascending: false })
      .range(startIndex, startIndex + Number(limit) - 1);

    if (error) {
      logger.error('Erreur lors de la récupération des commentaires:', error);
      return res.status(500).json({ error: 'Erreur lors de la récupération des commentaires' });
    }

    // Traiter les données pour faciliter l'affichage
    const processedComments = await Promise.all((comments || []).map(async (comment) => {
      // Déchiffrer les données utilisateur du commentaire
      if (comment.user && comment.user.length > 0) {
        const decryptedUserData = await decryptUserDataAsync(comment.user[0]);
        if (comment.user[0].user_profil && comment.user[0].user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(comment.user[0].user_profil[0]);
          decryptedUserData.user_profil = [decryptedProfilData];
        }
        comment.user = [decryptedUserData];
      }

      let parentInfo = null;

      // Formater le nom de l'utilisateur parent (Prénom I.)
      if (comment.parent) {
        const parentUser = comment.parent.parent_user?.[0];
        if (parentUser) {
          // Déchiffrer les données du parent
          const decryptedParentUser = await decryptUserDataAsync(parentUser);
          if (parentUser.user_profil && parentUser.user_profil.length > 0) {
            const decryptedParentProfil = await decryptProfilDataAsync(parentUser.user_profil[0]);
            decryptedParentUser.user_profil = [decryptedParentProfil];
          }

          const parentPrenom = decryptedParentUser.user_profil?.[0]?.prenom || '';
          const parentNom = decryptedParentUser.user_profil?.[0]?.nom || '';
          let parentDisplayName = decryptedParentUser.email || 'Utilisateur inconnu';

          if (parentPrenom || parentNom) {
            parentDisplayName = `${parentPrenom} ${parentNom ? parentNom.charAt(0) + '.' : ''}`.trim();
          }

          parentInfo = {
            id: comment.parent.id,
            message: comment.parent.message,
            user_id: comment.parent.user_id,
            user_email: parentDisplayName
          };
        }
      }

      return {
        ...comment,
        parent_info: parentInfo
      };
    }));

    res.status(200).json({
      data: processedComments || [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / Number(limit))
      }
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des commentaires:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const addBugReportComment = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { message, parent_comment_id } = req.body;
  const userId = req.user?.userId;
  const userRole = req.user?.role;

  if (!userId) {
    return res.status(401).json({ error: 'Utilisateur non authentifié' });
  }

  try {
    // Vérifier si le rapport existe
    const report = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (report.error) {
      logger.error('Erreur lors de la récupération du rapport:', report.error);
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }

    // Si un parent_comment_id est fourni, vérifier qu'il existe bien
    if (parent_comment_id) {
      const { data: parentComment, error: parentError } = await supabase
        .from('bug_report_comments')
        .select('*')
        .eq('id', parent_comment_id)
        .eq('bug_report_id', id) // Le commentaire parent doit appartenir au même rapport
        .single();

      if (parentError || !parentComment) {
        logger.error('Erreur: le commentaire parent est invalide:', parentError);
        return res.status(400).json({ error: 'Le commentaire parent spécifié est invalide' });
      }
    }

    // Récupérer l'email et le profil de l'utilisateur qui commente
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        email,
        user_profil:user_profil(
          nom,
          prenom
        )
      `)
      .eq('id', userId)
      .single();

    let userEmail = null;
    let userPrenom = null;
    let userNom = null;

    if (user) {
      // Déchiffrer les données utilisateur
      const decryptedUserData = await decryptUserDataAsync(user);
      userEmail = decryptedUserData.email;

      if (user.user_profil && user.user_profil.length > 0) {
        // Déchiffrer les données du profil
        const decryptedProfilData = await decryptProfilDataAsync(user.user_profil[0]);
        userPrenom = decryptedProfilData.prenom || null;
        userNom = decryptedProfilData.nom || null;
      }
    }

    // Déterminer si l'utilisateur est admin
    const isAdmin = userRole === 'jobpadm' || userRole === 'jobmodo';

    // Ajouter le commentaire
    const { data: comment, error } = await supabase
      .from('bug_report_comments')
      .insert({
        bug_report_id: id,
        user_id: userId,
        parent_comment_id: parent_comment_id || null,
        message,
        is_admin: isAdmin
      })
      .select(`
        *,
        user:users(
          id, 
          email, 
          user_profil:user_profil(nom, prenom)
        )
      `)
      .single();

    if (error) {
      logger.error('Erreur lors de l\'ajout du commentaire:', error);
      return res.status(500).json({ error: 'Erreur lors de l\'ajout du commentaire' });
    }

    // Vérifier si un email a déjà été envoyé récemment pour ce rapport
    const lastEmailKey = `bug_report:${id}:comment:last_email`;
    const lastEmailTime = await redis.get(lastEmailKey);
    
    let shouldSendEmail = true;

    if (lastEmailTime) {
      const timeSinceLastEmail = Date.now() - parseInt(lastEmailTime);
      if (timeSinceLastEmail < 300000) { // 5 minutes en millisecondes
        logger.info(`Email non envoyé pour le commentaire sur le rapport #${id} - Dernier email envoyé il y a moins de 5 minutes`);
        shouldSendEmail = false;
      }
    }

    if (shouldSendEmail) {
      // Si c'est une réponse à un commentaire, notifier l'auteur du commentaire parent
      if (parent_comment_id) {
        const { data: parentCommentData } = await supabase
          .from('bug_report_comments')
          .select(`
            user_id,
            user:users!inner(id, email)
          `)
          .eq('id', parent_comment_id)
          .single();

        if (parentCommentData && parentCommentData.user_id !== userId) {
          // Notification interne à l'auteur du commentaire parent
          await sendNotification(
            parentCommentData.user_id,
            'Réponse à votre commentaire',
            `Quelqu'un a répondu à votre commentaire sur le rapport "${report.data.title}"`,
            `/dashboard/bug-reports/${id}`
          );

          // Notification par email si activée
          const userInfo = parentCommentData.user && parentCommentData.user[0];
          if (userInfo && userInfo.email) {
            try {
              // Formatter le nom (Prénom N.)
              let commenterName = userEmail || 'Utilisateur anonyme';
              if (userPrenom || userNom) {
                commenterName = `${userPrenom || ''} ${userNom ? userNom.charAt(0) + '.' : ''}`.trim();
              }
              
              await sendBugReportCommentEmail(userInfo.email, {
                reportTitle: report.data.title,
                commentMessage: message,
                reportId: id,
                commenterId: userId as string,
                commenterEmail: isAdmin ? 'Support JobPartiel' : commenterName,
                isAdmin: isAdmin
              });
              logger.info(`Email de notification envoyé à ${userInfo.email} pour la réponse au commentaire`);
            } catch (error) {
              logger.error(`Erreur lors de l'envoi de l'email à ${userInfo.email}:`, error);
            }
          }
        }
      }

      // Si c'est un administrateur qui commente, notifier l'utilisateur qui a créé le rapport
      if (isAdmin && userId !== report.data.user_id) {
        try {
          // Notification interne
          await sendNotification(
            report.data.user_id,
            'Nouveau commentaire sur votre rapport',
            `Le Support JobPartiel a commenté votre rapport "${report.data.title}"`,
            `/dashboard/bug-reports/${id}`
          );
          
          // Notification par email
          await notifyForNewComment(
            id,
            comment
          );
        } catch (notifError) {
          logger.error('Erreur lors de l\'envoi de la notification:', notifError);
        }
      } 
      // Si c'est un utilisateur qui commente sur un rapport, notifier l'administrateur
      else if (!isAdmin) {
        // Récupérer les administrateurs
        const { data: admins, error: adminsError } = await supabase
          .from('users')
          .select('id, email')
          .eq('role', 'jobpadm');

        if (!adminsError && admins) {
          // Créer un Set pour éviter les doublons d'emails
          const notifiedEmails = new Set();
          
          // Formatter le nom (Prénom N.)
          let commenterName = userEmail || 'Utilisateur anonyme';
          if (userPrenom || userNom) {
            commenterName = `${userPrenom || ''} ${userNom ? userNom.charAt(0) + '.' : ''}`.trim();
          }
          
          for (const admin of admins) {
            // Notification interne
            await sendNotification(
              admin.id,
              'Nouveau commentaire utilisateur sur un rapport',
              `Un utilisateur a commenté le rapport "${report.data.title}"`,
              `/admin/bug-reports/${id}`
            );
            
            // Notification par email si l'admin a activé les notifications et n'a pas déjà été notifié
            if (!notifiedEmails.has(admin.email)) {
              try {
                await sendBugReportCommentEmail(admin.email, {
                  reportTitle: report.data.title,
                  commentMessage: message,
                  reportId: id,
                  commenterId: userId as string,
                  commenterEmail: commenterName,
                  isAdmin: false
                });
                notifiedEmails.add(admin.email);
              } catch (error) {
                logger.error(`Erreur lors de l'envoi de l'email à ${admin.email}:`, error);
              }
            }
          }
        }
      }
      
      // Mettre à jour le timestamp du dernier email envoyé
      await redis.set(lastEmailKey, Date.now().toString(), 'EX', 300); // Expire après 5 minutes
    }

    // Notifier l'utilisateur qui a posté le commentaire
    if (!userId) return res.status(401).json({ error: 'Utilisateur non authentifié' });
    await sendNotification(
      userId as string,
      `Votre commentaire a été publié`,
      `Votre commentaire sur le rapport "${report.data.title}" a été publié avec succès.`,
      `/dashboard/bug-reports/${id}`
    );

    // Après avoir ajouté le commentaire, invalider le cache pour ce rapport
    await invalidateBugReportCache(id, req.user?.userId);

    // Journaliser l'action d'ajout d'un commentaire
    if (!userId) return res.status(401).json({ error: 'Utilisateur non authentifié' });
    await logUserActivity(
      userId as string,
      'bug_report_comment_add',
      id,
      'bug_report',
      { 
        comment_id: comment.id,
        is_reply: parent_comment_id ? true : false,
        is_admin: isAdmin
      },
      getIpFromRequest(req)
    );

    // Ajouter l'entrée dans l'historique pour l'ajout du commentaire
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: null,
      new_status: null,
      comment: `Nouveau commentaire ajouté${comment.is_admin ? ' par un administrateur' : ''}`,
      action_type: 'comment_add'
    };

    await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    return res.status(201).json(comment);
  } catch (error) {
    logger.error('Erreur lors de l\'ajout du commentaire:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Supprimer un commentaire
export const deleteBugReportComment = async (req: Request, res: Response) => {
  const { id, commentId } = req.params;
  const userId = req.user?.userId;
  const userRole = req.user?.role;

  if (!userId) {
    return res.status(401).json({ error: 'Utilisateur non authentifié' });
  }

  try {
    // Récupérer le commentaire
    const { data: comment, error: commentError } = await supabase
      .from('bug_report_comments')
      .select('*')
      .eq('id', commentId)
      .single();

    if (commentError || !comment) {
      return res.status(404).json({ error: 'Commentaire non trouvé' });
    }

    // Vérifier les droits (auteur du commentaire ou admin)
    if (comment.user_id !== userId && userRole !== 'jobpadm') {
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à supprimer ce commentaire' });
    }

    // Vérifier si le commentaire a moins de 5 minutes (sauf pour les admins)
    if (userRole !== 'jobpadm') {
      const commentDate = new Date(comment.created_at);
      const now = new Date();
      const diffInMinutes = (now.getTime() - commentDate.getTime()) / 1000 / 60;
      
      if (diffInMinutes > 5) {
        return res.status(403).json({ error: 'Vous ne pouvez plus supprimer ce commentaire après 5 minutes' });
      }
    }

    // Supprimer le commentaire
    const { error: deleteError } = await supabase
      .from('bug_report_comments')
      .delete()
      .eq('id', commentId);

    if (deleteError) {
      logger.error('Erreur lors de la suppression du commentaire:', deleteError);
      return res.status(500).json({ error: 'Erreur lors de la suppression du commentaire' });
    }

    // Après la suppression, invalider le cache pour ce rapport
    await invalidateBugReportCache(id, req.user?.userId);

    // Journaliser l'action de suppression d'un commentaire
    if (!userId) return res.status(401).json({ error: 'Utilisateur non authentifié' });
    await logUserActivity(
      userId as string,
      'bug_report_comment_delete',
      id,
      'bug_report',
      { 
        comment_id: commentId,
        is_admin: userRole === 'jobpadm' || userRole === 'jobmodo'
      },
      getIpFromRequest(req)
    );

    // Ajouter l'entrée dans l'historique pour la suppression du commentaire
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: null,
      new_status: null,
      comment: `Commentaire supprimé${userRole === 'jobpadm' ? ' par un administrateur' : ''}`,
      action_type: 'comment_delete'
    };

    await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    res.status(200).json({ message: 'Commentaire supprimé avec succès' });
  } catch (error) {
    logger.error('Erreur lors de la suppression du commentaire:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Mettre à jour un commentaire
export const updateBugReportComment = async (req: Request, res: Response) => {
  const { id, commentId } = req.params;
  const { message } = req.body;
  const userId = req.user?.userId;
  const userRole = req.user?.role;

  if (!userId) {
    return res.status(401).json({ error: 'Utilisateur non authentifié' });
  }

  try {
    // Récupérer le commentaire
    const { data: comment, error: commentError } = await supabase
      .from('bug_report_comments')
      .select('*')
      .eq('id', commentId)
      .single();

    if (commentError || !comment) {
      return res.status(404).json({ error: 'Commentaire non trouvé' });
    }

    // Vérifier les droits (auteur du commentaire ou admin)
    if (comment.user_id !== userId && userRole !== 'jobpadm') {
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à modifier ce commentaire' });
    }

    // Vérifier si le commentaire a moins de 5 minutes (sauf pour les admins)
    if (userRole !== 'jobpadm') {
      const commentDate = new Date(comment.created_at);
      const now = new Date();
      const diffInMinutes = (now.getTime() - commentDate.getTime()) / 1000 / 60;
      
      if (diffInMinutes > 5) {
        return res.status(403).json({ error: 'Vous ne pouvez plus modifier ce commentaire après 5 minutes' });
      }
    }

    // Mettre à jour le commentaire
    const { data: updatedComment, error: updateError } = await supabase
      .from('bug_report_comments')
      .update({ message })
      .eq('id', commentId)
      .select(`
        *,
        user:users(id, email)
      `)
      .single();

    if (updateError) {
      logger.error('Erreur lors de la mise à jour du commentaire:', updateError);
      return res.status(500).json({ error: 'Erreur lors de la mise à jour du commentaire' });
    }

    // Après la mise à jour, invalider le cache pour ce rapport
    await invalidateBugReportCache(id, req.user?.userId);

    // Journaliser l'action de mise à jour d'un commentaire
    if (!userId) return res.status(401).json({ error: 'Utilisateur non authentifié' });
    await logUserActivity(
      userId as string,
      'bug_report_comment_update',
      id,
      'bug_report',
      { 
        comment_id: commentId,
        is_admin: userRole === 'jobpadm' || userRole === 'jobmodo'
      },
      getIpFromRequest(req)
    );

    // Ajouter l'entrée dans l'historique pour la modification du commentaire
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: null,
      new_status: null,
      comment: `Commentaire modifié${comment.is_admin ? ' par un administrateur' : ''}`,
      action_type: 'comment_update'
    };

    await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    res.status(200).json(updatedComment);
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du commentaire:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Supprimer plusieurs commentaires (administrateur uniquement)
export const deleteMultipleBugReportComments = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { commentIds } = req.body;
  const userId = req.user?.userId;
  const userRole = req.user?.role;

  if (!userId || userRole !== 'jobpadm') {
    return res.status(403).json({ error: 'Action réservée aux administrateurs' });
  }

  if (!Array.isArray(commentIds) || commentIds.length === 0) {
    return res.status(400).json({ error: 'Liste de commentaires invalide' });
  }

  try {
    // Vérifier que le rapport existe
    const { data: report, error: reportError } = await supabase
      .from('bug_reports')
      .select('id')
      .eq('id', id)
      .single();

    if (reportError || !report) {
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }

    // Supprimer les commentaires en une seule requête
    const { error: deleteError } = await supabase
      .from('bug_report_comments')
      .delete()
      .in('id', commentIds);

    if (deleteError) {
      logger.error('Erreur lors de la suppression multiple de commentaires:', deleteError);
      return res.status(500).json({ error: 'Erreur lors de la suppression des commentaires' });
    }

    // Après la suppression, invalider le cache
    await invalidateBugReportCache(id, req.user?.userId);

    logger.info(`${commentIds.length} commentaires supprimés du rapport ${id} par l'administrateur ${userId}`);
    res.status(200).json({ 
      message: `${commentIds.length} commentaires supprimés avec succès`,
      deletedCount: commentIds.length
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression multiple de commentaires:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Fermer un rapport de bug (admin et modo uniquement)
export const closeBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    const { admin_comment } = req.body;

    // Vérifier les droits d'accès
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Vérifier que l'utilisateur est un administrateur ou modérateur
    if (userRole !== 'jobpadm' && userRole !== 'jobmodo') {
      logger.warn(`Accès non autorisé: user_id=${userId}, role=${userRole}`);
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à fermer ce rapport' });
    }

    // Récupérer le rapport existant
    const { data: existingReport, error: fetchError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingReport) {
      logger.error(`Erreur lors de la récupération du rapport existant: ${JSON.stringify(fetchError)}`);
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Préparation des données pour la mise à jour
    const updateData: Partial<BugReportUpdateDTO> = {
      status: 'ferme',
      admin_comment: admin_comment || existingReport.admin_comment
    };

    // Mise à jour du rapport
    const { error } = await supabase
      .from('bug_reports')
      .update(updateData)
      .eq('id', id);

    if (error) {
      logger.error(`Erreur lors de la fermeture du rapport: ${JSON.stringify(error)}`);
      return res.status(500).json({ error: 'Erreur lors de la fermeture du rapport: ' + error.message });
    }

    // Enregistrer dans l'historique
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: existingReport.status,
      new_status: 'ferme',
      comment: admin_comment || null
    };

    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    if (historyError) {
      logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
    }
    
    // Récupérer les données mises à jour
    const { data: updatedReport, error: getError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (getError) {
      logger.error(`Erreur lors de la récupération du rapport mis à jour: ${JSON.stringify(getError)}`);
      return res.status(500).json({ error: 'Erreur lors de la récupération du rapport mis à jour' });
    }
    
    // Créer un objet pour stocker le résultat final
    const resultReport = { ...updatedReport };

    // Récupérer les informations de l'utilisateur qui a créé le rapport
    if (updatedReport.user_id) {
      const { data: userData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', updatedReport.user_id)
        .single();

      if (userData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedUserData = await decryptUserDataAsync(userData);
        if (userData.user_profil && userData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(userData.user_profil[0]);
          decryptedUserData.user_profil = [decryptedProfilData];
        }
        resultReport.user = decryptedUserData;
      }
    }

    // Récupérer les informations de l'utilisateur assigné
    if (updatedReport.assigned_to) {
      const { data: assignedData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', updatedReport.assigned_to)
        .single();

      if (assignedData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedAssignedData = await decryptUserDataAsync(assignedData);
        if (assignedData.user_profil && assignedData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(assignedData.user_profil[0]);
          decryptedAssignedData.user_profil = [decryptedProfilData];
        }
        resultReport.assigned = decryptedAssignedData;
      }
    }

    // Notifier l'utilisateur du changement de statut
    if (updatedReport.user_id) {
      try {
        // Récupérer l'ancien statut à partir des données existantes
        const oldStatus = existingReport?.status;
        await notifyUserAboutStatusChange(updatedReport, oldStatus);
        logger.info(`Notification envoyée à l'utilisateur ${updatedReport.user_id} pour le rapport ${id}`);
      } catch (notifyError) {
        logger.error(`Erreur lors de la notification: ${JSON.stringify(notifyError)}`);
      }
    }

    // Après la mise à jour, invalider le cache
    await invalidateBugReportCache(id, req.user?.userId);

    res.json({
      success: true,
      message: 'Rapport fermé avec succès',
      report: resultReport
    });
    return;
  } catch (error) {
    logger.error(`Erreur globale lors de la fermeture du rapport: ${JSON.stringify(error)}`);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Rouvrir un rapport de bug (admin et modo uniquement)
export const reopenBugReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    const { admin_comment } = req.body;

    // Vérifier les droits d'accès
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Vérifier que l'utilisateur est un administrateur ou modérateur
    if (userRole !== 'jobpadm' && userRole !== 'jobmodo') {
      logger.warn(`Accès non autorisé: user_id=${userId}, role=${userRole}`);
      return res.status(403).json({ error: 'Vous n\'êtes pas autorisé à rouvrir ce rapport' });
    }

    // Récupérer le rapport existant
    const { data: existingReport, error: fetchError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !existingReport) {
      logger.error(`Erreur lors de la récupération du rapport existant: ${JSON.stringify(fetchError)}`);
      return res.status(404).json({ error: 'Rapport de bug non trouvé' });
    }

    // Vérifier que le rapport est bien fermé ou résolu ou rejeté
    if (existingReport.status !== 'ferme' && existingReport.status !== 'resolu' && existingReport.status !== 'rejete') {
      return res.status(400).json({ error: 'Ce rapport ne peut pas être rouvert car il n\'est pas fermé, résolu ou rejeté' });
    }

    // Préparation des données pour la mise à jour
    const updateData: Partial<BugReportUpdateDTO> = {
      status: 'reouvert',
      admin_comment: admin_comment || existingReport.admin_comment
    };

    // Mise à jour du rapport
    const { error } = await supabase
      .from('bug_reports')
      .update(updateData)
      .eq('id', id);

    if (error) {
      logger.error(`Erreur lors de la réouverture du rapport: ${JSON.stringify(error)}`);
      return res.status(500).json({ error: 'Erreur lors de la réouverture du rapport: ' + error.message });
    }

    // Enregistrer dans l'historique
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: existingReport.status,
      new_status: 'reouvert',
      comment: admin_comment || null
    };

    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    if (historyError) {
      logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
    }
    
    // Récupérer les données mises à jour
    const { data: updatedReport, error: getError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (getError) {
      logger.error(`Erreur lors de la récupération du rapport mis à jour: ${JSON.stringify(getError)}`);
      return res.status(500).json({ error: 'Erreur lors de la récupération du rapport mis à jour' });
    }
    
    // Créer un objet pour stocker le résultat final
    const resultReport = { ...updatedReport };

    // Récupérer les informations de l'utilisateur qui a créé le rapport
    if (updatedReport.user_id) {
      const { data: userData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', updatedReport.user_id)
        .single();

      if (userData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedUserData = await decryptUserDataAsync(userData);
        if (userData.user_profil && userData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(userData.user_profil[0]);
          decryptedUserData.user_profil = [decryptedProfilData];
        }
        resultReport.user = decryptedUserData;
      }
    }

    // Récupérer les informations de l'utilisateur assigné
    if (updatedReport.assigned_to) {
      const { data: assignedData } = await supabase
        .from('users')
        .select('id, email, role, user_profil:user_profil(nom, prenom)')
        .eq('id', updatedReport.assigned_to)
        .single();

      if (assignedData) {
        // Déchiffrer les données utilisateur et profil
        const decryptedAssignedData = await decryptUserDataAsync(assignedData);
        if (assignedData.user_profil && assignedData.user_profil.length > 0) {
          const decryptedProfilData = await decryptProfilDataAsync(assignedData.user_profil[0]);
          decryptedAssignedData.user_profil = [decryptedProfilData];
        }
        resultReport.assigned = decryptedAssignedData;
      }
    }

    // Notifier l'utilisateur du changement de statut
    if (updatedReport.user_id) {
      try {
        // Récupérer l'ancien statut à partir des données existantes
        const oldStatus = existingReport?.status;
        await notifyUserAboutStatusChange(updatedReport, oldStatus);
        logger.info(`Notification envoyée à l'utilisateur ${updatedReport.user_id} pour le rapport ${id}`);
      } catch (notifyError) {
        logger.error(`Erreur lors de la notification: ${JSON.stringify(notifyError)}`);
      }
    }

    // Après la mise à jour, invalider le cache
    await invalidateBugReportCache(id, req.user?.userId);

    return res.json({
      success: true,
      message: 'Rapport rouvert avec succès',
      report: resultReport
    });
  } catch (error) {
    logger.error(`Erreur globale lors de la réouverture du rapport: ${JSON.stringify(error)}`);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
}; 

/**
 * Clôture plusieurs rapports de bug simultanément (admin/modo uniquement)
 */
export const closeMultipleBugReports = async (req: Request, res: Response) => {
  try {
    const { bugReportIds, adminComment, status } = req.body;

    // Validation de base
    if (!bugReportIds || !Array.isArray(bugReportIds) || bugReportIds.length === 0) {
      return res.status(400).json({ error: 'La liste des IDs de rapports est requise' });
    }

    // Validation du statut
    const validStatuses = ['nouveau', 'en_cours', 'resolu', 'rejete', 'ferme', 'reouvert'];
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Un statut valide est requis' });
    }

    // Récupération de l'utilisateur (admin/modo)
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }

    // Récupérer les statuts actuels avant la mise à jour
    const { data: currentReports, error: fetchError } = await supabase
      .from('bug_reports')
      .select('id, status')
      .in('id', bugReportIds);

    if (fetchError) {
      logger.error('Erreur lors de la récupération des statuts actuels:', fetchError);
      return res.status(500).json({ error: 'Erreur lors de la récupération des statuts actuels' });
    }

    // Stocker les statuts actuels dans un objet pour référence rapide
    const currentStatuses: Record<string, string> = {};
    if (currentReports) {
      currentReports.forEach(report => {
        currentStatuses[report.id] = report.status;
      });
    }

    // Configurer la variable d'environnement pour le trigger
    await supabase.rpc('set_config', {
      key: 'app.current_user_id',
      value: userId
    });

    // Mise à jour de tous les rapports
    const { data, error } = await supabase
      .from('bug_reports')
      .update({
        status: status,
        admin_comment: adminComment || `Statut modifié par un administrateur/modérateur: ${status}`,
        updated_at: new Date().toISOString()
      })
      .in('id', bugReportIds)
      .select();

    if (error) {
      logger.error('Erreur lors de la clôture multiple des rapports:', error);
      return res.status(500).json({ error: 'Erreur lors de la clôture multiple des rapports' });
    }

    // Invalider le cache pour tous les rapports concernés
    await Promise.all(bugReportIds.map((id: string) => invalidateBugReportCache(id)));

    // Notifier les utilisateurs concernés pour chaque rapport
    if (data && data.length > 0) {
      for (const report of data) {
        const oldStatus = currentStatuses[report.id];
        await notifyUserAboutStatusChange(report, oldStatus);
      }
    }

    res.status(200).json({
      success: true,
      message: `${data?.length || 0} rapports de bug ont été clôturés avec succès`,
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la clôture multiple des rapports:', error);
    return res.status(500).json({ error: 'Erreur lors de la clôture multiple des rapports' });
  }
};

// Approuver un rapport de bug en attente de modération
export const approveReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    
    // Vérifier les permissions (admin ou modérateur uniquement)
    if (!userId || (userRole !== 'jobpadm' && userRole !== 'jobmodo')) {
      return res.status(403).json({ error: 'Permission refusée. Seuls les administrateurs et modérateurs peuvent approuver des rapports.' });
    }
    
    // Récupérer le rapport existant
    const { data: report, error: fetchError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError || !report) {
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }
    
    // Vérifier que le rapport est bien en attente de modération
    if (report.status !== 'attente_moderation') {
      return res.status(400).json({ error: 'Ce rapport n\'est pas en attente de modération' });
    }
    
    // Mettre à jour le statut du rapport
    const { data: updatedReport, error: updateError } = await supabase
      .from('bug_reports')
      .update({
        status: 'nouveau',
        admin_comment: req.body.admin_comment || 'Rapport approuvé par la modération',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (updateError) {
      logger.error(`Erreur lors de l'approbation du rapport #${id}:`, updateError);
      return res.status(500).json({ error: 'Erreur lors de l\'approbation du rapport' });
    }
    
    // Enregistrer dans l'historique
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: report.status,
      new_status: 'nouveau',
      comment: req.body.admin_comment || 'Rapport approuvé par la modération'
    };

    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    if (historyError) {
      logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
    }
    
    // Notifier l'utilisateur du changement de statut
    await notifyUserAboutStatusChange(updatedReport, report.status);
    
    // Invalider le cache
    await invalidateBugReportCache(id, report.user_id);
    
    res.status(200).json({
      message: 'Rapport approuvé avec succès',
      report: updatedReport
    });
  } catch (error) {
    logger.error('Erreur lors de l\'approbation du rapport:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Rejeter un rapport de bug en attente de modération
export const rejectReport = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    
    // Vérifier les permissions (admin ou modérateur uniquement)
    if (!userId || (userRole !== 'jobpadm' && userRole !== 'jobmodo')) {
      return res.status(403).json({ error: 'Permission refusée. Seuls les administrateurs et modérateurs peuvent rejeter des rapports.' });
    }
    
    // Récupérer le rapport existant
    const { data: report, error: fetchError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError || !report) {
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }
    
    // Vérifier que le rapport est bien en attente de modération
    if (report.status !== 'attente_moderation') {
      return res.status(400).json({ error: 'Ce rapport n\'est pas en attente de modération' });
    }
    
    // Mettre à jour le statut du rapport
    const { data: updatedReport, error: updateError } = await supabase
      .from('bug_reports')
      .update({
        status: 'rejete',
        admin_comment: req.body.admin_comment || 'Rapport rejeté par la modération',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (updateError) {
      logger.error(`Erreur lors du rejet du rapport #${id}:`, updateError);
      return res.status(500).json({ error: 'Erreur lors du rejet du rapport' });
    }
    
    // Enregistrer dans l'historique
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: report.status,
      new_status: 'rejete',
      comment: req.body.admin_comment || 'Rapport rejeté par la modération'
    };

    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntry);

    if (historyError) {
      logger.error(`Erreur lors de l'enregistrement de l'historique: ${JSON.stringify(historyError)}`);
    }
    
    // Notifier l'utilisateur du changement de statut
    await notifyUserAboutStatusChange(updatedReport, report.status);
    
    // Invalider le cache
    await invalidateBugReportCache(id, report.user_id);
    
    res.status(200).json({
      message: 'Rapport rejeté avec succès',
      report: updatedReport
    });
  } catch (error) {
    logger.error('Erreur lors du rejet du rapport:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

// Modifier la visibilité d'un rapport (privé/public)
export const toggleReportVisibility = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const { id } = req.params;
    const { is_private } = req.body;
    
    // Vérifier que is_private est un booléen
    if (typeof is_private !== 'boolean') {
      return res.status(400).json({ error: 'Le paramètre is_private doit être un booléen' });
    }
    
    // Vérifier l'authentification
    if (!userId) {
      return res.status(401).json({ error: 'Authentification requise' });
    }
    
    // Récupérer le rapport existant
    const { data: report, error: fetchError } = await supabase
      .from('bug_reports')
      .select('*')
      .eq('id', id)
      .single();
      
    if (fetchError || !report) {
      return res.status(404).json({ error: 'Rapport non trouvé' });
    }
    
    // Vérifier les permissions: seul le propriétaire du rapport ou un admin/modo peut modifier la visibilité
    const isAdminOrModo = userRole === 'jobpadm' || userRole === 'jobmodo';
    if (report.user_id !== userId && !isAdminOrModo) {
      return res.status(403).json({ error: 'Vous n\'avez pas les droits pour modifier ce rapport' });
    }
    
    // Mettre à jour la visibilité
    const { data: updatedReport, error: updateError } = await supabase
      .from('bug_reports')
      .update({
        is_private: is_private,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
      
    if (updateError) {
      logger.error(`Erreur lors de la modification de la visibilité du rapport #${id}:`, updateError);
      return res.status(500).json({ error: 'Erreur lors de la modification de la visibilité du rapport' });
    }
    
    // Invalider le cache
    await invalidateBugReportCache(id, report.user_id);
    
    // Message de confirmation
    const visibilityMessage = is_private 
      ? "Ce rapport est maintenant privé et ne sera visible que par vous et l'équipe de modération." 
      : "Ce rapport est maintenant public et sera visible par tous les utilisateurs.";
    
    // Notifier l'utilisateur du changement
    if (userId === report.user_id) {
      await sendNotification(
        userId as string,
        `Visibilité de votre rapport modifiée`,
        `La visibilité de votre rapport "${report.title}" a été modifiée. ${visibilityMessage}`,
        `/dashboard/bug-reports/${id}`
      );
    }
    
    res.status(200).json({
      message: `Visibilité du rapport modifiée avec succès. ${visibilityMessage}`,
      report: updatedReport
    });

    // Ajouter l'entrée dans l'historique pour le changement de visibilité
    const historyEntry = {
      bug_report_id: id,
      updated_by: userId,
      old_status: null,
      new_status: null,
      comment: `Visibilité du rapport modifiée : ${is_private ? 'privé' : 'public'}`,
      action_type: 'visibility_change'
    };

    await supabase
      .from('bug_report_history')
      .insert(historyEntry);
  } catch (error) {
    logger.error('Erreur lors de la modification de la visibilité du rapport:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

/**
 * Supprimer plusieurs rapports de bug en même temps (admin/modérateur uniquement)
 */
export const deleteMultipleBugReports = async (req: Request, res: Response) => {
  try {
    const { bugReportIds, raison } = req.body;
    
    // Vérifier que bugReportIds est un tableau non vide
    if (!Array.isArray(bugReportIds) || bugReportIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Veuillez fournir un tableau d\'identifiants de rapports à supprimer'
      });
    }
    
    // Obtenir l'ID et le rôle de l'utilisateur connecté
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    
    // Vérifier que l'utilisateur est admin ou modérateur
    if (!userId || (userRole !== 'jobpadm' && userRole !== 'jobmodo')) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas les droits nécessaires pour effectuer cette action'
      });
    }
    
    // Récupérer les rapports à supprimer
    const { data: reportsToDelete, error: reportsError } = await supabase
      .from('bug_reports')
      .select('id, title, user_id')
      .in('id', bugReportIds);
    
    if (reportsError) {
      console.error('Erreur lors de la récupération des rapports :', reportsError);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la récupération des rapports'
      });
    }
    
    // Si aucun rapport n'a été trouvé
    if (!reportsToDelete || reportsToDelete.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Aucun rapport correspondant n\'a été trouvé'
      });
    }
    
    // Enregistrer l'action dans l'historique des rapports avant de les supprimer
    const historyEntries = reportsToDelete.map(report => ({
      bug_report_id: report.id,
      action: 'suppression_definitive',
      description: `Rapport supprimé par ${userRole === 'jobpadm' ? 'un administrateur' : 'un modérateur'}`,
      details: { 
        titre: report.title,
        raison: raison || 'Aucune raison spécifiée'
      },
      performed_by: userId
    }));
    
    // Insérer les entrées d'historique
    const { error: historyError } = await supabase
      .from('bug_report_history')
      .insert(historyEntries);
    
    if (historyError) {
      console.error('Erreur lors de l\'enregistrement de l\'historique :', historyError);
      // On continue malgré l'erreur d'historique
    }
    
    // Supprimer les rapports
    const { error: deleteError } = await supabase
      .from('bug_reports')
      .delete()
      .in('id', bugReportIds);
    
    if (deleteError) {
      console.error('Erreur lors de la suppression des rapports :', deleteError);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la suppression des rapports'
      });
    }
    
    // Invalider le cache pour tous les rapports concernés
    await Promise.all(bugReportIds.map((id: string) => invalidateBugReportCache(id)));
    
    // Enregistrer l'action dans les logs
    console.log(`${reportsToDelete.length} rapports supprimés par l'utilisateur ${userId} (${userRole})`);
    
    // Retourner une réponse de succès
    res.status(200).json({
      success: true,
      message: `${reportsToDelete.length} rapport(s) ont été supprimés avec succès`,
      count: reportsToDelete.length
    });
    
  } catch (error) {
    console.error('Erreur lors de la suppression multiple des rapports :', error);
    return res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de la suppression des rapports'
    });
  }
};
  
