import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  TableSortLabel
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Shield as ShieldIcon,
  Refresh as RefreshIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import {
  Users,
  UserCheck,
  UserX,
  Mail,
  MapPin,
  Shield,
  Clock,
  BadgeCheck,
  CreditCard
} from 'lucide-react';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';
import UserDetailModal from '../../components/admin/UserDetailModal';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
  }
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

interface User {
  id: string;
  email: string;
  role: string;
  user_type: string;
  profil_actif: boolean;
  email_verifier: boolean;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  is_online: boolean;
  last_activity: string;
  date_inscription: string;
  created_at: string;
  is_anonymized: boolean;
  suspension_reason?: string;
  suspended_until?: string;
  user_profil?: {
    nom: string;
    prenom: string;
    telephone: string;
    ville: string;
    photo_url: string;
    mode_vacance: boolean;
    profil_visible: boolean;
  };
  user_abo?: {
    type_abonnement: string;
    statut: string;
    date_debut: string;
    date_fin: string;
  }[];
  user_jobi?: {
    montant: number;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

const UserManagement: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'created_at', direction: 'desc' });
  
  // Filtres et pagination
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('active');
  const [userTypeFilter, setUserTypeFilter] = useState('');
  const [pagination, setPagination] = useState<Pagination>({
    page: 0,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Statistiques rapides
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    suspendedUsers: 0,
    verifiedUsers: 0,
    onlineUsers: 0,
    premiumUsers: 0
  });

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const params = new URLSearchParams({
        page: (pagination.page + 1).toString(),
        limit: pagination.limit.toString(),
        search,
        role: roleFilter,
        status: statusFilter,
        userType: userTypeFilter,
        sortBy: sortConfig.key,
        sortOrder: sortConfig.direction
      });

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users?${params}`,
        {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des utilisateurs');
      }

      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data.users);
        setPagination(prev => ({
          ...prev,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages
        }));

        // Calculer les statistiques
        const totalUsers = data.data.pagination.total;
        const activeUsers = data.data.users.filter((u: User) => u.profil_actif).length;
        const suspendedUsers = data.data.users.filter((u: User) => u.suspended_until).length;
        const verifiedUsers = data.data.users.filter((u: User) => u.profil_verifier).length;
        const onlineUsers = data.data.users.filter((u: User) => u.is_online).length;
        const premiumUsers = data.data.users.filter((u: User) => 
          u.user_abo && u.user_abo.length > 0 && 
          ['premium', 'pro'].includes(u.user_abo[0].type_abonnement)
        ).length;

        setStats({
          totalUsers,
          activeUsers,
          suspendedUsers,
          verifiedUsers,
          onlineUsers,
          premiumUsers
        });
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des utilisateurs');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, search, roleFilter, statusFilter, userTypeFilter, sortConfig]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handlePageChange = (_event: unknown, newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPagination(prev => ({
      ...prev,
      limit: parseInt(event.target.value, 10),
      page: 0
    }));
  };

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setModalOpen(true);
  };

  const handleSort = (key: string) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getStatusChip = (user: User) => {
    if (user.suspended_until) {
      return <Chip 
        label="Suspendu" 
        color="error" 
        size="small" 
        icon={<BlockIcon />} 
        sx={{ 
          borderRadius: '8px',
          fontWeight: 500,
          '& .MuiChip-icon': { fontSize: '0.875rem' }
        }}
      />;
    }
    if (!user.profil_actif) {
      return <Chip 
        label="Inactif" 
        color="warning" 
        size="small" 
        icon={<WarningIcon />} 
        sx={{ 
          borderRadius: '8px',
          fontWeight: 500,
          '& .MuiChip-icon': { fontSize: '0.875rem' }
        }}
      />;
    }
    return <Chip 
      label="Actif" 
      color="success" 
      size="small" 
      icon={<CheckCircleIcon />} 
      sx={{ 
        borderRadius: '8px',
        fontWeight: 500,
        '& .MuiChip-icon': { fontSize: '0.875rem' }
      }}
    />;
  };

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'particulier':
        return <PersonIcon sx={{ color: COLORS.info }} />;
      case 'entreprise':
        return <BusinessIcon sx={{ color: COLORS.warning }} />;
      case 'professionnel':
        return <ShieldIcon sx={{ color: COLORS.primary }} />;
      default:
        return <PersonIcon sx={{ color: COLORS.neutral }} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && users.length === 0) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', flexWrap: 'wrap' }}>
        <Box>
          <PageTitle variant="h4">
            Gestion des Utilisateurs
          </PageTitle>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Gérez et surveillez tous les utilisateurs de la plateforme
          </Typography>
        </Box>
        <StyledButton
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={() => fetchUsers()}
          disabled={loading}
          sx={{ mt: isMobile ? 2 : 0 }}
        >
          Actualiser
        </StyledButton>
      </Box>

      {/* Statistiques rapides */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>  
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox>
                <Users size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Total Utilisateurs
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="#2D3748">
                {stats.totalUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.success}>
                <UserCheck size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Utilisateurs Actifs
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.success}>
                {stats.activeUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.error}>
                <UserX size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Utilisateurs Suspendus
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.error}>
                {stats.suspendedUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.info}>
                <BadgeCheck size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Profils Vérifiés
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.info}>
                {stats.verifiedUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.success}>
                <Clock size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                En ligne
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.success}>
                {stats.onlineUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.primary}>
                <CreditCard size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Abonnés Premium
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.primary}>
                {stats.premiumUsers.toLocaleString()}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
      </Grid>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 3, 
            borderRadius: '12px',
            border: `1px solid ${COLORS.error}20`
          }}
        >
          {error}
        </Alert>
      )}

      {/* Filtres */}
      <StyledPaper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FilterListIcon sx={{ mr: 1, color: COLORS.primary }} />
          <Typography variant="h6" fontWeight="600">Filtres</Typography>
        </Box>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, md: 3 }}>
            <TextField
              fullWidth
              label="Rechercher"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                sx: { borderRadius: '8px' }
              }}
              variant="outlined"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <StyledFormControl fullWidth>
              <InputLabel>Rôle</InputLabel>
              <Select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                label="Rôle"
              >
                <MenuItem value="">Tous</MenuItem>
                <MenuItem value="jobutil">Utilisateur</MenuItem>
                <MenuItem value="jobmodo">Modérateur</MenuItem>
                <MenuItem value="jobpadm">Administrateur</MenuItem>
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <StyledFormControl fullWidth>
              <InputLabel>Statut</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Statut"
              >
                <MenuItem value="">Tous</MenuItem>
                <MenuItem value="active">Actif</MenuItem>
                <MenuItem value="inactive">Inactif</MenuItem>
                <MenuItem value="suspended">Suspendu</MenuItem>
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <StyledFormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={userTypeFilter}
                onChange={(e) => setUserTypeFilter(e.target.value)}
                label="Type"
              >
                <MenuItem value="">Tous</MenuItem>
                <MenuItem value="particulier">Particulier</MenuItem>
                <MenuItem value="entreprise">Entreprise</MenuItem>
                <MenuItem value="professionnel">Professionnel</MenuItem>
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 3 }}>
            <StyledButton
              variant="outlined"
              onClick={() => {
                setSearch('');
                setRoleFilter('');
                setStatusFilter('');
                setUserTypeFilter('');
                setPagination(prev => ({ ...prev, page: 0 }));
              }}
              fullWidth
            >
              Réinitialiser
            </StyledButton>
          </Grid>
        </Grid>
      </StyledPaper>

      {/* Tableau des utilisateurs */}
      <StyledPaper>
        <StyledTableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'email'}
                    direction={sortConfig.key === 'email' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('email')}
                  >
                    Utilisateur
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'user_type'}
                    direction={sortConfig.key === 'user_type' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('user_type')}
                  >
                    Type
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'role'}
                    direction={sortConfig.key === 'role' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('role')}
                  >
                    Rôle
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'profil_actif'}
                    direction={sortConfig.key === 'profil_actif' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('profil_actif')}
                  >
                    Statut
                  </TableSortLabel>
                </TableCell>
                <TableCell>Vérifications</TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'user_abo'}
                    direction={sortConfig.key === 'user_abo' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('user_abo')}
                  >
                    Abonnement
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'user_jobi'}
                    direction={sortConfig.key === 'user_jobi' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('user_jobi')}
                  >
                    Jobi
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortConfig.key === 'created_at'}
                    direction={sortConfig.key === 'created_at' ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort('created_at')}
                  >
                    Inscription
                  </TableSortLabel>
                </TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar
                        src={user.user_profil?.photo_url}
                        sx={{ 
                          width: 40, 
                          height: 40,
                          border: user.is_online ? `2px solid ${COLORS.success}` : 'none',
                        }}
                      >
                        {user.user_profil?.prenom?.[0] || user.email[0].toUpperCase()}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {user.user_profil?.prenom} {user.user_profil?.nom}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {user.email}
                        </Typography>
                        {user.user_profil?.ville && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            <MapPin size={12} style={{ verticalAlign: 'middle', marginRight: '2px' }} /> {user.user_profil.ville}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getUserTypeIcon(user.user_type)}
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {user.user_type}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.role === 'jobpadm' ? 'Admin' : user.role === 'jobmodo' ? 'Modo' : 'User'}
                      color={user.role === 'jobpadm' ? 'error' : user.role === 'jobmodo' ? 'warning' : 'default'}
                      size="small"
                      sx={{ 
                        borderRadius: '8px',
                        fontWeight: 500
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" flexDirection="column" gap={1}>
                      {getStatusChip(user)}
                      {user.is_online && (
                        <Chip 
                          label="En ligne" 
                          color="success" 
                          size="small" 
                          variant="outlined"
                          sx={{ 
                            borderRadius: '8px',
                            fontWeight: 500,
                            fontSize: '0.7rem'
                          }} 
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={0.5}>
                      {user.email_verifier && (
                        <Tooltip title="Email vérifié">
                          <Mail size={16} color={COLORS.success} />
                        </Tooltip>
                      )}
                      {user.profil_verifier && (
                        <Tooltip title="Profil vérifié">
                          <UserCheck size={16} color={COLORS.primary} />
                        </Tooltip>
                      )}
                      {user.identite_verifier && (
                        <Tooltip title="Identité vérifiée">
                          <BadgeCheck size={16} color={COLORS.info} />
                        </Tooltip>
                      )}
                      {user.entreprise_verifier && (
                        <Tooltip title="Entreprise vérifiée">
                          <BusinessIcon sx={{ fontSize: 16, color: COLORS.warning }} />
                        </Tooltip>
                      )}
                      {user.assurance_verifier && (
                        <Tooltip title="Assurance vérifiée">
                          <Shield size={16} color={COLORS.error} />
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {user.user_abo && user.user_abo.length > 0 ? (
                      <Chip
                        label={user.user_abo[0].type_abonnement}
                        color={
                          user.user_abo[0].type_abonnement === 'premium' ? 'primary' :
                          user.user_abo[0].type_abonnement === 'pro' ? 'secondary' : 'default'
                        }
                        size="small"
                        sx={{ 
                          borderRadius: '8px',
                          fontWeight: 500,
                          textTransform: 'capitalize'
                        }}
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Aucun
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold" color="primary">
                      {user.user_jobi?.montant || 0} J
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(user.date_inscription || user.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="Voir les détails">
                        <IconButton
                          size="small"
                          onClick={() => handleUserClick(user)}
                          sx={{ 
                            color: COLORS.primary,
                            '&:hover': { backgroundColor: `${COLORS.primary}15` }
                          }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Modifier">
                        <IconButton
                          size="small"
                          onClick={() => handleUserClick(user)}
                          sx={{ 
                            color: COLORS.info,
                            '&:hover': { backgroundColor: `${COLORS.info}15` }
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </StyledTableContainer>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {pagination.total} utilisateurs au total
          </Typography>
          <TablePagination
            component="div"
            count={pagination.total}
            page={pagination.page}
            onPageChange={handlePageChange}
            rowsPerPage={pagination.limit}
            onRowsPerPageChange={handleRowsPerPageChange}
            rowsPerPageOptions={[10, 20, 50, 100]}
            labelRowsPerPage="Lignes par page:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`
            }
            sx={{
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                margin: 0,
              },
              '.MuiTablePagination-toolbar': {
                padding: 0,
              }
            }}
          />
        </Box>
      </StyledPaper>

      {/* Modal de détails utilisateur */}
      {selectedUser && (
        <UserDetailModal
          user={selectedUser}
          open={modalOpen}
          onClose={() => {
            setModalOpen(false);
            setSelectedUser(null);
          }}
          onUserUpdate={fetchUsers}
        />
      )}
    </Container>
  );
};

export default UserManagement;
