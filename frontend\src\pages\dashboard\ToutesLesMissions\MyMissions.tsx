import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, CircularProgress, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { missionsApi, Mission } from './missionsApi';
import { notify } from '../../../components/Notification';
import MissionsLayout from './MissionsLayout';
import { FilterBar } from './FilterBar/FilterBar';
import logger from '../../../utils/logger';

const NoMissionsBox = styled(Box)({
  textAlign: 'center',
  padding: '48px',
  backgroundColor: 'white',
  borderRadius: '16px',
  border: '2px dashed #FFE4BA',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
    borderRadius: '16px 16px 0 0',
  },
});

const LoadingIndicator = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  padding: '20px',
});

const MyMissions: React.FC = () => {
  const navigate = useNavigate();
  const [missions, setMissions] = useState<Mission[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);

  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [budgetFilters, setBudgetFilters] = useState<string[]>([]);
  const [paymentFilters, setPaymentFilters] = useState<string[]>([]);
  const [categoryFilters, setCategoryFilters] = useState<string[]>([]);
  const [subcategoryFilters, setSubcategoryFilters] = useState<string[]>([]);
  const [likedFilter, setLikedFilter] = useState<string[]>([]);
  const [isFilterBarExpanded, setIsFilterBarExpanded] = useState(false);

  const toggleFilter = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees') => {
    const setters: Record<string, React.Dispatch<React.SetStateAction<string[]>>> = {
      status: setStatusFilters,
      budget: setBudgetFilters,
      payment: setPaymentFilters,
      category: setCategoryFilters,
      subcategory: setSubcategoryFilters,
      liked: setLikedFilter
    };

    // Gestion spéciale pour les filtres de paiement et budget (un seul choix possible)
    if (category === 'payment' || category === 'budget') {
      const setter = setters[category];
      setter((prev: string[]) => {
        if (prev.includes(filter)) {
          return []; // Si on clique sur un filtre déjà actif, on le désactive
        }
        return [filter]; // Sinon on active uniquement ce filtre
      });
      return;
    }

    // Pour les statuts, on permet la sélection multiple sauf pour urgent
    if (category === 'status') {
      setStatusFilters((prev: string[]) => {
        if (filter === 'urgent') {
          // Gestion spéciale pour le filtre urgent
          const hasUrgent = prev.includes('urgent');
          if (hasUrgent) {
            // Si urgent est déjà sélectionné, on le retire en gardant les autres
            return prev.filter(f => f !== 'urgent');
          } else {
            // Si urgent n'est pas sélectionné, on l'ajoute aux autres
            return [...prev, 'urgent'];
          }
        } else {
          // Pour les autres statuts, on permet la sélection multiple
          if (prev.includes(filter)) {
            return prev.filter(f => f !== filter);
          }
          return [...prev, filter];
        }
      });
      return;
    }

    // Gestion normale pour les autres filtres (permettant la sélection multiple)
    if (setters[category]) {
      setters[category]((prev: string[]) => {
        if (prev.includes(filter)) {
          return prev.filter((f: string) => f !== filter);
        }
        return [...prev, filter];
      });
    }
  };

  const isFilterActive = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees'): boolean => {
    const filters: Record<string, string[]> = {
      status: statusFilters,
      budget: budgetFilters,
      payment: paymentFilters,
      category: categoryFilters,
      subcategory: subcategoryFilters,
      liked: likedFilter
    };
    return filters[category]?.includes(filter) || false;
  };

  const handleCategoryFilterChange = (categories: string[], subcategories: string[]) => {
    setCategoryFilters(categories);
    setSubcategoryFilters(subcategories);
  };

  const fetchMissions = async (pageNumber: number, isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const limit = 6; // Limite constante par page

      // Séparer les statuts normaux et le statut urgent
      const normalStatuses = statusFilters.filter(status => ['en_cours', 'terminee', 'annulee', 'en_moderation'].includes(status));
      const isUrgentSelected = statusFilters.includes('urgent');

      logger.info('Statuts filtrés:', {
        normalStatuses,
        isUrgentSelected,
        statusFilters
      });

      // Construire les paramètres de filtrage
      const filterParams: Record<string, any> = {
        status: normalStatuses,
        search: searchTerm,
        categories: categoryFilters.length > 0 ? categoryFilters : undefined,
        subcategories: subcategoryFilters.length > 0 ? subcategoryFilters : undefined,
        budget_types: budgetFilters.length > 0 ? budgetFilters : undefined,
        payment_methods: paymentFilters.length > 0 ? paymentFilters : undefined,
        is_urgent: isUrgentSelected || undefined,
        liked: likedFilter.includes('liked') || undefined,
        sort_by: 'date_creation' // Ajouter un tri explicite par date de création
      };

      // Supprimer les paramètres undefined et les tableaux vides
      Object.keys(filterParams).forEach(key => {
        if (filterParams[key] === undefined || (Array.isArray(filterParams[key]) && filterParams[key].length === 0)) {
          delete filterParams[key];
        }
      });

      logger.info('Filtres nettoyés envoyés à l\'API:', filterParams);

      const response = await missionsApi.getUserMissions(pageNumber, limit, filterParams);

      logger.info('Réponse de l\'API:', {
        data: response.data,
        total: response.total,
        hasMore: response.hasMore,
        appliedFilters: filterParams
      });

      const newMissions = response.data || [];

      if (isLoadMore) {
        setMissions(prev => [...prev, ...newMissions]);
      } else {
        setMissions(newMissions);
      }

      // Mettre à jour hasMore en fonction du nombre total de missions
      setHasMore(response.total > (pageNumber * limit));
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      notify('Erreur lors de la récupération des missions', 'error');
      if (!isLoadMore) {
        setMissions([]);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Charger les missions initiales et lors des changements de filtres
  useEffect(() => {
    setPage(1);
    fetchMissions(1);
  }, [
    statusFilters,
    budgetFilters,
    paymentFilters,
    categoryFilters,
    subcategoryFilters,
    searchTerm,
    likedFilter
  ]);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '900px',
      threshold: 0.1, // Réduire le seuil pour déclencher plus tôt
    };

    const observer = new IntersectionObserver((entries) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loading && !loadingMore) {
        setPage(prevPage => {
          const nextPage = prevPage + 1;
          fetchMissions(nextPage, true);
          return nextPage;
        });
      }
    }, options);

    observerRef.current = observer;

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, loadingMore]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleMissionUpdate = (updatedMission: Mission) => {
    setMissions(prev => prev.map(mission =>
      mission.id === updatedMission.id ? updatedMission : mission
    ));
  };

  const resetAllFilters = () => {
    setStatusFilters([]);
    setBudgetFilters([]);
    setPaymentFilters([]);
    setCategoryFilters([]);
    setSubcategoryFilters([]);
    setLikedFilter([]);
    setSearchTerm('');
  };

  const hasActiveFilters = () => {
    return statusFilters.length > 0 ||
           budgetFilters.length > 0 ||
           paymentFilters.length > 0 ||
           categoryFilters.length > 0 ||
           subcategoryFilters.length > 0 ||
           likedFilter.length > 0 ||
           searchTerm.length > 0;
  };

  const footerContent = hasMore && !loading && (
    <LoadingIndicator ref={loadingRef}>
      {loadingMore && <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />}
    </LoadingIndicator>
  );

  // Extraire le contenu du header dans une variable pour éviter la duplication
  const headerContent = (
    <>
      <FilterBar
        statusFilters={statusFilters}
        budgetFilters={budgetFilters}
        paymentFilters={paymentFilters}
        categoryFilters={categoryFilters}
        subcategoryFilters={subcategoryFilters}
        toggleFilter={toggleFilter}
        isFilterActive={isFilterActive}
        onCategoryFilterChange={handleCategoryFilterChange}
        showStatusFilters={true}
        showLikedFilter={false}
        isExpanded={isFilterBarExpanded}
        onToggleExpand={(expanded: boolean) => setIsFilterBarExpanded(expanded)}
        searchTerm={searchTerm}
        onSearch={handleSearch}
        showSearchField={true}
      />

      {/* Bouton de réinitialisation des filtres */}
      {hasActiveFilters() && (
        <Box sx={{
          padding: {
            xs: '0 0 16px 0',
            sm: '0 0 16px 0'
          },
          display: 'flex',
          justifyContent: 'flex-start'
        }}>
          <Button
            onClick={resetAllFilters}
            startIcon={<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
            </svg>}
            sx={{
              color: '#FF6B2C',
              borderColor: '#FFE4BA',
              '&:hover': {
                backgroundColor: '#FFF8F3',
                borderColor: '#FF6B2C'
              }
            }}
            variant="outlined"
            size="small"
          >
            Réinitialiser les filtres
          </Button>
        </Box>
      )}
    </>
  );

  const getEmptyMessage = () => {
    if (hasActiveFilters()) {
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '20px'
        }}>
          <div>Aucune mission ne correspond à vos critères de recherche</div>
          <button
            onClick={resetAllFilters}
            style={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold',
              transition: 'background-color 0.3s',
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#FF965E'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#FF6B2C'}
          >
            Réinitialiser les filtres
          </button>
        </Box>
      );
    }
    return "Vous n'avez pas encore créé de mission";
  };

  if (missions.length === 0 && !loading) {
    return (
      <>
        {headerContent}
        <motion.div>
          <NoMissionsBox>
            <Typography variant="h6" color="textSecondary" gutterBottom>
              {getEmptyMessage()}
            </Typography>
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                variant="contained"
                sx={{
                  mt: 2,
                  backgroundColor: '#FF6B2C',
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 'bold',
                  px: 4,
                  py: 1.5,
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
                }}
                onClick={() => navigate('/dashboard/missions/poster-une-mission')}
              >
                Poster une mission
              </Button>
            </motion.div>
          </NoMissionsBox>
        </motion.div>
      </>
    );
  }

  return (
    <MissionsLayout
      missions={missions}
      loading={loading}
      emptyMessage={getEmptyMessage()}
      onUpdate={handleMissionUpdate}
      headerContent={headerContent}
      footerContent={footerContent}
      isOwner={true}
      showStatus={true}
      onMakeProposal={() => {}}
    />
  );
};

export default MyMissions;