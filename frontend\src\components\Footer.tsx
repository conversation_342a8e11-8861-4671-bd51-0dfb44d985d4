import { ReactElement } from 'react';
import { Facebook, Linkedin, Phone, Mail, MapPin, Instagram, Twitter } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useState, FormEvent, Suspense, lazy } from 'react';
import ModalPortal from './ModalPortal';
import GavelIcon from '@mui/icons-material/Gavel';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
import InfoIcon from '@mui/icons-material/Info';
import CookieIcon from '@mui/icons-material/Cookie';
import { api } from '../services/api';
import { notify } from '../components/Notification';

const MentionsLegales = lazy(() => import('../pages/mentions-legales'));
const ConditionsGenerales = lazy(() => import('../pages/conditions-generales'));
const PolitiqueConfidentialite = lazy(() => import('../pages/politique-confidentialite'));
const CookiesPage = lazy(() => import('../pages/cookies'));

export default function Footer() {
  const { user } = useAuth();
  const [openLegal, setOpenLegal] = useState<null | string>(null);
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [isNewsletterSubmitting, setIsNewsletterSubmitting] = useState(false);
  const [newsletterMessage, setNewsletterMessage] = useState('');
  const [newsletterSuccess, setNewsletterSuccess] = useState(false);

  const handleNewsletterSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!newsletterEmail) {
      setNewsletterMessage('Veuillez entrer une adresse email valide');
      setNewsletterSuccess(false);
      return;
    }

    try {
      setIsNewsletterSubmitting(true);
      setNewsletterMessage('');

      const response = await api.post('/api/newsletter/subscribe', { email: newsletterEmail });

      if (response.data.success) {
        setNewsletterSuccess(true);
        setNewsletterMessage(response.data.message || 'Merci pour votre inscription ! Veuillez vérifier votre email pour confirmer votre abonnement.');
        setNewsletterEmail('');
        notify('Vérifiez votre email pour confirmer votre abonnement à la newsletter', 'success');
      } else {
        setNewsletterSuccess(false);
        setNewsletterMessage(response.data.message || 'Une erreur est survenue. Veuillez réessayer.');
      }
    } catch (error) {
      setNewsletterSuccess(false);
      setNewsletterMessage('Une erreur est survenue. Veuillez réessayer plus tard.');
      console.error('Erreur lors de l\'abonnement à la newsletter:', error);
    } finally {
      setIsNewsletterSubmitting(false);
    }
  };

  const serviceCategories = [
    {
      title: "Services à la personne",
      items: [
        {
          name: "Garde d'enfants",
          subItems: ["Baby-sitting", "Aide aux devoirs"]
        },
        {
          name: "Aide aux personnes âgées",
          subItems: ["Compagnie et soutien", "Assistance aux tâches quotidiennes"]
        },
        {
          name: "Ménage et repassage",
          subItems: ["Nettoyage résidentiel", "Repassage et entretien du linge"]
        }
      ]
    },
    {
      title: "Événementiel & Restauration",
      items: [
        {
          name: "Organisation d'événements",
          subItems: ["Planification de fêtes", "Gestion de conférences"]
        },
        {
          name: "Animation d'événements",
          subItems: ["Animation de soirées", "Promotion sur les réseaux sociaux"]
        },
        {
          name: "Restauration pour événements",
          subItems: ["Service de traiteur", "Organisation de buffets"]
        }
      ]
    },
    {
      title: "Services administratifs",
      items: [
        {
          name: "Secrétariat temporaire",
          subItems: ["Gestion des appels et emails", "Organisation de documents"]
        },
        {
          name: "Assistance à la gestion",
          subItems: ["Coordination des équipes", "Suivi des échéances et des budgets"]
        },
        {
          name: "Saisie de données",
          subItems: ["Entrée de données", "Création de bases de données"]
        }
      ]
    },
    {
      title: "Transport & Logistique",
      items: [
        {
          name: "Services de transport",
          subItems: ["Livraison de colis", "Transport de marchandises"]
        },
        {
          name: "Gestion logistique",
          subItems: ["Inventaire et suivi", "Expédition des marchandises"]
        },
        {
          name: "Services de déménagement",
          subItems: ["Déménagement résidentiel", "Déménagement de bureaux"]
        }
      ]
    },
    {
      title: "Communication & Marketing",
      items: [
        {
          name: "Rédaction et traduction",
          subItems: ["Rédaction de contenu", "Traduction de documents"]
        },
        {
          name: "Gestion de réseaux sociaux",
          subItems: ["Création de contenu", "Analyse des performances"]
        },
        {
          name: "Support client",
          subItems: ["Assistance par chat", "Gestion des tickets de support"]
        }
      ]
    },
    {
      title: "Éducation & Formation",
      items: [
        {
          name: "Cours particuliers",
          subItems: ["Mathématiques", "Langues étrangères"]
        },
        {
          name: "Coaching personnel",
          subItems: ["Coaching de vie", "Conseils en carrière"]
        },
        {
          name: "Formation professionnelle",
          subItems: ["Ateliers de compétences", "Programmes de certification"]
        }
      ]
    },
    {
      title: "Services numériques",
      items: [
        {
          name: "Développement web",
          subItems: ["Création de sites web", "Développement d'applications"]
        },
        {
          name: "Support informatique",
          subItems: ["Réparation d'ordinateurs", "Maintenance de systèmes"]
        },
        {
          name: "Design graphique",
          subItems: ["Création de visuels", "Conception de supports marketing"]
        }
      ]
    },
    {
      title: "Arts & Divertissement",
      items: [
        {
          name: "Animation pour enfants",
          subItems: ["Activités ludiques", "Ateliers créatifs"]
        },
        {
          name: "Musique et spectacle",
          subItems: ["Concerts", "Performances artistiques"]
        },
        {
          name: "Photographie et vidéo",
          subItems: ["Séances photo", "Captation vidéo d'événements"]
        }
      ]
    }
  ];

  const footerLinks = {
    company: [
      { name: "À propos", href: "#" },
      { name: "Comment ça marche", href: "#" },
      { name: "Nos tarifs", href: "#" },
      { name: "Blog", href: "#" },
      { name: "Contact", href: "#" }
    ],
    legal: [
      { name: "Conditions Générales", key: "conditions-generales" },
      { name: "Politique de confidentialité", key: "politique-confidentialite" },
      { name: "Mentions légales", key: "mentions-legales" },
      { name: "Cookies", key: "cookies" }
    ]
  };

  const legalTitles: Record<string, string> = {
    'mentions-legales': 'Mentions légales',
    'conditions-generales': 'Conditions Générales',
    'politique-confidentialite': 'Politique de confidentialité',
    'cookies': 'Cookies',
  };

  const legalIcons: Record<string, ReactElement> = {
    'mentions-legales': <InfoIcon sx={{ color: '#FF7A35', fontSize: 32, mr: 1 }} />,
    'conditions-generales': <GavelIcon sx={{ color: '#FF7A35', fontSize: 32, mr: 1 }} />,
    'politique-confidentialite': <PrivacyTipIcon sx={{ color: '#FF7A35', fontSize: 32, mr: 1 }} />,
    'cookies': <CookieIcon sx={{ color: '#FF7A35', fontSize: 32, mr: 1 }} />,
  };

  const legalSubtitles: Record<string, string> = {
    'mentions-legales': "Informations légales et éditeur du site.",
    'conditions-generales': "Règles d'utilisation de la plateforme.",
    'politique-confidentialite': "Comment nous protégeons vos données personnelles.",
    'cookies': "Gestion et utilisation des cookies sur le site.",
  };

  const renderLegalContent = () => {
    switch (openLegal) {
      case 'mentions-legales':
        return <MentionsLegales />;
      case 'conditions-generales':
        return <ConditionsGenerales />;
      case 'politique-confidentialite':
        return <PolitiqueConfidentialite />;
      case 'cookies':
        return <CookiesPage />;
      default:
        return null;
    }
  };

  return (
    <footer>
      {/* Call to Action Section */}
      {!user && (
        <div className="bg-gradient-to-r from-[#ff7a35] to-[#ffa149] py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="text-3xl font-bold text-white mb-8">
              Prêt à commencer ? Inscrivez-vous aujourd'hui !
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/inscription"
                className="bg-white text-[#ff7a35] px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl font-medium"
              >
                Je m'inscris en tant que JOBBEUR
              </Link>
              <a
                href="#"
                className="bg-white text-[#ff7a35] px-8 py-3 rounded-full hover:bg-opacity-90 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl font-medium"
              >
                Je recherche un JOBBEUR
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Main Footer Section */}
      <div className="bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Services Search Section */}
          {!user && (
            <div className="py-12 border-b border-[#ff7a35]/10">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Services populaires</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Découvrez notre sélection de services les plus recherchés par nos utilisateurs
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {serviceCategories.map((category, index) => (
                  <div key={index} className="bg-white/50 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="font-semibold text-[#ff7a35] mb-4">{category.title}</div>
                    <ul className="space-y-3">
                      {category.items.map((item, idx) => (
                        <li key={idx} className="space-y-2">
                          <div className="font-medium text-gray-800">{item.name}</div>
                          <ul className="ml-4 space-y-1">
                            {item.subItems.map((subItem, subIdx) => (
                              <li key={subIdx}>
                                <a href="#" className="text-gray-600 hover:text-[#ff7a35] text-sm transition-colors">
                                  {subItem}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Main Content Grid */}
          <div className="py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            {/* Quick Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Liens rapides</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link, index) => (
                  <li key={index}>
                    <a href={link.href} className="text-gray-600 hover:text-[#ff7a35] transition-colors flex items-center group">
                      <span className="w-1 h-1 bg-[#ff7a35] rounded-full mr-2 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Contactez-nous</h3>
              <ul className="space-y-4">
                <li>
                  <a href="tel:0785551945" className="flex items-center space-x-3 text-gray-600 hover:text-[#ff7a35] transition-colors group">
                    <div className="bg-[#ff7a35] bg-opacity-10 p-2 rounded-full group-hover:bg-opacity-20 transition-all">
                      <Phone className="h-5 w-5 text-[#ff7a35]" />
                    </div>
                    <span>07 85 55 19 45</span>
                  </a>
                </li>
                <li>
                  <a href="mailto:<EMAIL>" className="flex items-center space-x-3 text-gray-600 hover:text-[#ff7a35] transition-colors group">
                    <div className="bg-[#ff7a35] bg-opacity-10 p-2 rounded-full group-hover:bg-opacity-20 transition-all">
                      <Mail className="h-5 w-5 text-[#ff7a35]" />
                    </div>
                    <span><EMAIL></span>
                  </a>
                </li>
                <li>
                  <div className="flex items-center space-x-3 text-gray-600">
                    <div className="bg-[#ff7a35] bg-opacity-10 p-2 rounded-full">
                      <MapPin className="h-5 w-5 text-[#ff7a35]" />
                    </div>
                    <span>Paris, France</span>
                  </div>
                </li>
              </ul>
            </div>

            {/* Newsletter */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Newsletter</h3>
              <p className="text-gray-600 text-sm">
                Restez informé des dernières actualités et offres spéciales.
              </p>
              <form className="space-y-3" onSubmit={handleNewsletterSubmit}>
                <input
                  type="email"
                  placeholder="Votre adresse email"
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#ff7a35] focus:border-transparent"
                  value={newsletterEmail}
                  onChange={(e) => setNewsletterEmail(e.target.value)}
                  required
                />
                <button
                  type="submit"
                  className="w-full bg-[#ff7a35] text-white py-2 px-4 rounded-lg hover:bg-[#ff8f35] transition-colors"
                  disabled={isNewsletterSubmitting}
                >
                  {isNewsletterSubmitting ? (
                    <span className="flex items-center justify-center">
                      <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                      Envoi...
                    </span>
                  ) : (
                    "S'abonner"
                  )}
                </button>
                {newsletterMessage && (
                  <p className={`text-sm ${newsletterSuccess ? 'text-green-600' : 'text-red-600'}`}>
                    {newsletterMessage}
                  </p>
                )}
              </form>
            </div>

            {/* Logo and Social Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Suivez-nous</h3>
              <div className="flex items-center space-x-4">
                <a href="#" className="bg-[#ff7a35] p-3 rounded-full text-white hover:bg-[#ff8f35] transition-all duration-300 hover:scale-110 hover:shadow-lg">
                  <Facebook className="h-6 w-6" />
                </a>
                <a href="#" className="bg-[#ff7a35] p-3 rounded-full text-white hover:bg-[#ff8f35] transition-all duration-300 hover:scale-110 hover:shadow-lg">
                  <Instagram className="h-6 w-6" />
                </a>
                <a href="#" className="bg-[#ff7a35] p-3 rounded-full text-white hover:bg-[#ff8f35] transition-all duration-300 hover:scale-110 hover:shadow-lg">
                  <Twitter className="h-6 w-6" />
                </a>
                <a href="#" className="bg-[#ff7a35] p-3 rounded-full text-white hover:bg-[#ff8f35] transition-all duration-300 hover:scale-110 hover:shadow-lg">
                  <Linkedin className="h-6 w-6" />
                </a>
              </div>
              <div className="hidden md:block">
                <p className="text-gray-600 text-sm leading-relaxed">
                  JobPartiel.fr est votre plateforme de confiance pour trouver des missions flexibles
                  et des professionnels qualifiés près de chez vous.
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="py-6 border-t border-[#ff7a35]/10">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="text-gray-500 text-sm">
                © {new Date().getFullYear()} JobPartiel.fr. Tous droits réservés.
              </div>
              <div className="flex flex-wrap justify-center gap-x-6 gap-y-2">
                {footerLinks.legal.map((link, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setOpenLegal(link.key)}
                    className="text-gray-500 hover:text-[#ff7a35] text-sm transition-colors bg-transparent border-none p-2 m-0 cursor-pointer min-w-[44px] min-h-[44px] touch-manipulation"
                    style={{ background: 'none', WebkitTapHighlightColor: 'transparent' }}
                  >
                    {link.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Modale pour les pages légales */}
      <ModalPortal isOpen={!!openLegal} onBackdropClick={() => setOpenLegal(null)}>
        <div className="bg-white rounded-2xl shadow-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto p-0">
          <div className="flex flex-col gap-1 px-6 py-4 border-b border-[#ff7a35]/20 bg-gradient-to-r from-[#FFF8F3] to-[#FFE4BA] rounded-t-2xl">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                {openLegal && legalIcons[openLegal]}
                <h2 className="text-2xl md:text-3xl font-bold text-[#FF7A35]">
                  {openLegal ? legalTitles[openLegal] : ''}
                </h2>
              </div>
              <button onClick={() => setOpenLegal(null)} className="text-gray-400 hover:text-[#ff7a35] text-2xl font-bold focus:outline-none">&times;</button>
            </div>
            {openLegal && (
              <div className="text-sm md:text-base text-gray-600 font-medium mt-1 mb-0 pl-1">
                {legalSubtitles[openLegal]}
              </div>
            )}
          </div>
          <div>
            <Suspense fallback={<div className="p-8 text-center text-[#ff7a35]">Chargement...</div>}>
              {renderLegalContent()}
            </Suspense>
          </div>
        </div>
      </ModalPortal>
    </footer>
  );
}
