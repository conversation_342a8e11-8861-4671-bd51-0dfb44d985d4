/* Permet de connecter un utilisateur avec son compte Google */

import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { dbService } from '../services/db';
import logger from '../utils/logger';
import { processPostRegistration } from '../controllers/auth';

// Étendre les types de session pour inclure le code de parrainage
declare module 'express-session' {
  interface SessionData {
    referralCode?: string;
  }
}

// Chargement des variables d'environnement
const googleClientID = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';

// Utilisez l'URI de callback exactement comme configuré dans la console Google Cloud
// Vérifiez quelle est l'URI configurée dans votre projet Google Cloud
const callbackURL = process.env.GOOGLE_CALLBACK_URL || 
  (process.env.NODE_ENV === 'production' 
    ? 'https://api.jobpartiel.fr/api/auth/google/callback'
    : 'http://localhost:3001/api/auth/google/callback');

if (!googleClientID || !googleClientSecret) {
  logger.warn('Google OAuth keys missing', {
    hasClientId: !!googleClientID,
    hasClientSecret: !!googleClientSecret
  });
}

// Configuration de la stratégie Google OAuth
passport.use(new GoogleStrategy({
  clientID: googleClientID,
  clientSecret: googleClientSecret,
  callbackURL: callbackURL,
  passReqToCallback: true
}, async (req, accessToken, refreshToken, profile, done) => {
  try {
    logger.info('Google authentication attempt', { 
      profileId: profile.id,
      email: profile.emails?.[0]?.value 
    });

    // Vérifier si l'utilisateur existe déjà avec cet email Google
    const email = profile.emails?.[0]?.value;
    
    if (!email) {
      logger.error('Google profile missing email', { profileId: profile.id });
      return done(null, false, { message: 'Email requis pour l\'authentification Google' });
    }

    // Vérifier si un utilisateur avec cet email existe déjà
    const existingUser = await dbService.getUserByEmail(email);
    
    if (existingUser) {
      // Utilisateur existant - mettre à jour les informations Google si nécessaire
      if (!existingUser.google_id) {
        // L'utilisateur existe mais n'était pas encore lié à Google
        logger.info('Linking existing user to Google account', { 
          userId: existingUser.id, 
          googleId: profile.id 
        });
        
        const { data, error } = await dbService.supabase
          .from('users')
          .update({ 
            google_id: profile.id,
            google_data: profile,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingUser.id)
          .select()
          .single();
          
        if (error) {
          logger.error('Failed to update user with Google information', { error });
          return done(error);
        }
        
        return done(null, data);
      }
      
      logger.info('User authenticated via Google', { userId: existingUser.id });
      return done(null, existingUser);
    } else {
      // Créer un nouvel utilisateur
      logger.info('Creating new user from Google authentication', { 
        email, 
        googleId: profile.id 
      });
      
      // Vérifier le code de parrainage s'il est présent en session
      let referrerId = null;
      const referralCode = req.session?.referralCode;
      if (referralCode) {
        try {
          logger.info('Vérification du code de parrainage Google:', { referralCode });
          const { data: referrerData, error: referrerError } = await dbService.supabase
            .from('users')
            .select('id')
            .eq('referral_code', referralCode)
            .single();

          if (referrerError || !referrerData) {
            logger.warn('Code de parrainage invalide pour utilisateur Google:', { referralCode, error: referrerError });
          } else {
            referrerId = referrerData.id;
            logger.info('Code de parrainage valide pour utilisateur Google, utilisateur trouvé:', { referrerId });
          }
        } catch (error) {
          logger.error('Erreur lors de la vérification du code de parrainage Google:', error);
        }
      }

      const userData = {
        email,
        google_id: profile.id,
        google_data: profile,
        user_type: 'non-jobbeur' as 'jobbeur' | 'non-jobbeur',
        role: 'jobutil',
        email_verifier: true,  // L'email est déjà vérifié par Google
        profil_verifier: false,
        identite_verifier: false,
        entreprise_verifier: false,
        assurance_verifier: false,
        profil_actif: true,
        date_inscription: new Date().toISOString(),
        referred_by: referrerId
      };
      
      const newUser = await dbService.createUser(userData);
      
      if (!newUser) {
        logger.error('Failed to create user from Google authentication');
        return done(null, false, { message: 'Échec de la création de l\'utilisateur' });
      }
      
      logger.info('New user created from Google authentication', {
        userId: newUser.id
      });

      // Pré-remplir le profil utilisateur avec les données disponibles dans le profil Google
      try {
        const firstName = profile._json?.given_name || '';
        const lastName = profile._json?.family_name || '';
        const photoUrl = profile._json?.picture || '';

        const profilData: {
          prenom: string;
          nom: string;
          photo_url: string;
        } = {
          prenom: firstName,
          nom: lastName,
          photo_url: photoUrl,
          // Ajout d'autres champs pertinents selon besoin
        };

        // Ne créer un profil que si au moins un champ a des données
        if (firstName || lastName || photoUrl) {
          await dbService.updateUserProfil(newUser.id, profilData);
          logger.info('Profil utilisateur pré-rempli avec les données Google', {
            userId: newUser.id,
            fieldsAdded: Object.keys(profilData).filter(key => !!profilData[key as keyof typeof profilData])
          });
        }
      } catch (profilError) {
        // Log l'erreur mais ne pas échouer l'authentification
        logger.error('Erreur lors de la création du profil utilisateur avec les données Google', {
          error: profilError instanceof Error ? profilError.message : 'Unknown error',
          userId: newUser.id
        });
      }

      // Utiliser la fonction commune pour le processus post-inscription
      try {
        await processPostRegistration(
          newUser,
          referrerId,
          '', // Pas d'IP disponible dans le contexte Passport
          true // skipEmailVerification = true pour Google (email déjà vérifié)
        );
        logger.info('Processus post-inscription Google terminé avec succès');
      } catch (processError) {
        logger.error('Erreur lors du processus post-inscription Google:', processError);
        // On continue l'authentification même si certains processus échouent
      }

      return done(null, newUser);
    }
  } catch (error) {
    logger.error('Google authentication error', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return done(error as Error);
  }
}));

// Sérialisation et désérialisation des utilisateurs
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await dbService.getUserById(id);
    done(null, user);
  } catch (error) {
    done(error);
  }
});

export default passport; 