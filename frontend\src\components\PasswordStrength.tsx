import React, { useState, useEffect } from 'react';
import { useZxcvbn } from '../hooks/useDynamicImport';

interface PasswordStrengthProps {
  password: string;
}

export const PasswordStrength: React.FC<PasswordStrengthProps> = ({ password }) => {
  const { module: zxcvbn, loading } = useZxcvbn();
  const [score, setScore] = useState(0);

  useEffect(() => {
    if (!loading && zxcvbn && password) {
      const result = zxcvbn(password);
      setScore(result.score);
    }
  }, [password, loading, zxcvbn]);

  if (loading || !password) {
    return null;
  }

  const getStrengthColor = () => {
    switch (score) {
      case 0: return 'bg-red-500';
      case 1: return 'bg-orange-500';
      case 2: return 'bg-yellow-500';
      case 3: return 'bg-lime-500';
      case 4: return 'bg-green-500';
      default: return 'bg-gray-200';
    }
  };

  return (
    <div className="mt-2">
      <div className="h-2 w-full bg-gray-200 rounded-full">
        <div 
          className={`h-full ${getStrengthColor()} rounded-full transition-all duration-300`}
          style={{ width: `${(score + 1) * 20}%` }}
        />
      </div>
      <p className="text-sm text-gray-600 mt-1">
        Force du mot de passe : {
          score === 0 ? 'Très faible' :
          score === 1 ? 'Faible' :
          score === 2 ? 'Moyen' :
          score === 3 ? 'Fort' :
          'Très fort'
        }
      </p>
    </div>
  );
};
