export interface GalleryFolder {
  id: string;
  name: string;
  description?: string;
  cover_image?: string;
  coverImage?: string;
  status?: 'actif' | 'inactif';
  imagesCount: number;
  createdAt: string;
}

export interface ProfilData {
  galleryFolders: GalleryFolder[];
}

export interface GalleryImage {
  id: string;
  gallery_id: string;
  photo_url: string;
  caption?: string;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface GalleryModalData {
  name: string;
  description: string;
  cover_image?: File;
}

export interface PhotoUploadResponse {
  success: boolean;
  message: string;
  photo?: GalleryImage;
}

export interface PhotosResponse {
  success: boolean;
  photos: GalleryImage[];
}

export interface PhotoUpdateData {
  caption?: string;
  order?: number;
}