import React, { useEffect, useState } from 'react';
import DOMPurify from 'dompurify';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';

interface NotificationPopupProps {
    message: string;
    onClose: () => void;
    isJobbeur: boolean;
}

const NotificationPopup: React.FC<NotificationPopupProps> = ({ message, onClose, isJobbeur }) => {
    const [showModal, setShowModal] = useState(false);

    useEffect(() => {
        if (isJobbeur) {
            setShowModal(true);
        }
    }, [isJobbeur]);

    const handleClose = () => {
        setShowModal(false);
        onClose();
    };

    if (!showModal) {
        return null;
    }

    return createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center animate-fade-in">
            <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md border border-gray-300 animate-fade-in transition-opacity duration-300">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-semibold text-gray-800">Section réservée aux jobbeurs</h2>
                    <button onClick={handleClose} className="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <X className="h-5 w-5" />
                    </button>
                </div>
                <div className="flex items-center mb-4">
                    <div className="notification-icon-wrapper mr-3 rounded-full flex items-center justify-center bg-[#FFE4BA] p-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="#FF7A35" className="notification-icon h-8 w-8">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>
                    </div>
                    <div className="notification-content">
                        <p className="notification-message text-gray-800 p-2 mb-1 text-sm" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(message) }}></p>
                    </div>
                </div>
                <div className="flex justify-center mt-4">
                    <button onClick={handleClose} className="bg-[#FF6B2C] text-white font-semibold py-2 px-4 rounded hover:bg-[#FF7A35] focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-opacity-50">
                        Fermer
                    </button>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default NotificationPopup;
