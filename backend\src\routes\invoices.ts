import { Router } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import * as invoiceController from '../controllers/invoices';
import { companySettingsController } from '../controllers/companySettingsController';
import { getBillingStats, exportBillingStatsToExcel, exportBillingStatsToPDF } from '../controllers/billingStats';
import { rateLimit } from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

// Configuration des rate limiters
const companySettingsLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 secondes
  max: 50, // 50 requêtes par fenêtre
  message: {
    message: 'Trop de requêtes sur les paramètres d\'entreprise. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const invoiceCrudLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes par fenêtre
  message: {
    message: 'Trop de requêtes sur les factures. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const pdfGenerationLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 requêtes par fenêtre
  message: {
    message: 'Trop de générations de PDF. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const emailSendingLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 requêtes par fenêtre
  message: {
    message: 'Trop d\'envois d\'emails. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const receivedDocumentsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // 50 requêtes par fenêtre
  message: {
    message: 'Trop de requêtes sur les documents reçus. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const quoteActionLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // 20 requêtes par fenêtre
  message: {
    message: 'Trop de requêtes sur les actions de devis. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Limiter pour les exports
const exportLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requêtes par fenêtre
  message: {
    message: 'Trop d\'exports. Veuillez réessayer dans quelques instants.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// --- Ajout d'un asyncHandler simple pour Express 5 ---
function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any> | void
): (req: Request, res: Response, next: NextFunction) => void {
  return function (req: Request, res: Response, next: NextFunction): void {
  Promise.resolve(fn(req, res, next)).catch(next);
};
}
// ---------------------------------------------------

const router = Router();

// Routes pour les paramètres d'entreprise
router.get('/company-settings', authMiddleware.authenticateToken, companySettingsLimiter, asyncHandler(companySettingsController.getCompanySettings));
router.post('/company-settings', authMiddleware.authenticateToken, companySettingsLimiter, asyncHandler(companySettingsController.updateCompanySettings));

// Route pour les statistiques de facturation (DOIT être avant /:id)
router.get('/stats', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(getBillingStats));

// Routes pour exporter les statistiques
router.get('/stats/export/excel', authMiddleware.authenticateToken, exportLimiter, asyncHandler(exportBillingStatsToExcel));
router.get('/stats/export/pdf', authMiddleware.authenticateToken, exportLimiter, asyncHandler(exportBillingStatsToPDF));

// Routes pour les factures et devis
router.get('/', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.getInvoices));
router.get('/:id', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.getInvoiceById));
router.post('/', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.createInvoice));
router.put('/:id', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.updateInvoice));
router.delete('/:id', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.deleteInvoice));

// Obtenir l'historique d'un document
router.get('/:id/history', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.getInvoiceHistory));

// Routes spécifiques /received/* (doivent être avant les routes génériques)
// Générer un PDF pour un document reçu
router.get('/received/:id/pdf', authMiddleware.authenticateToken, pdfGenerationLimiter, asyncHandler(invoiceController.generateReceivedPDF));

// Récupérer les devis reçus (où l'email du client correspond à l'email de l'utilisateur)
router.get('/received/quotes', authMiddleware.authenticateToken, receivedDocumentsLimiter, asyncHandler(invoiceController.getReceivedQuotes));

// Récupérer les factures reçues (où l'email du client correspond à l'email de l'utilisateur)
router.get('/received/invoices', authMiddleware.authenticateToken, receivedDocumentsLimiter, asyncHandler(invoiceController.getReceivedInvoices));

// Récupérer les avoirs reçus (où l'email du client correspond à l'email de l'utilisateur)
router.get('/received/credit-notes', authMiddleware.authenticateToken, receivedDocumentsLimiter, asyncHandler(invoiceController.getReceivedCreditNotes));

// Routes génériques (après les routes spécifiques)
// Générer un PDF
router.get('/:id/pdf', authMiddleware.authenticateToken, pdfGenerationLimiter, asyncHandler(invoiceController.generatePDF));

// Envoyer par email
router.post('/:id/send', authMiddleware.authenticateToken, emailSendingLimiter, asyncHandler(invoiceController.sendInvoiceByEmail));

// Dupliquer un document
router.post('/:id/duplicate', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.duplicateInvoice));

// Convertir un devis en facture
router.post('/:id/convert-to-invoice', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.convertToInvoice));

// Accepter un devis (nécessite une authentification - seul le destinataire peut accepter)
router.post('/:id/accept-quote', authMiddleware.authenticateToken, quoteActionLimiter, asyncHandler(invoiceController.acceptQuote));

// Refuser un devis (nécessite une authentification - seul le destinataire peut refuser)
router.post('/:quoteId/reject-quote', authMiddleware.authenticateToken, quoteActionLimiter, asyncHandler(invoiceController.rejectQuote));

// Créer un avoir
router.post('/:id/credit-note', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.createCreditNote));

// Mettre à jour le statut d'une facture
router.patch('/:id/status', authMiddleware.authenticateToken, invoiceCrudLimiter, asyncHandler(invoiceController.updateInvoiceStatus));

// Route pour récupérer les informations de l'entreprise d'un utilisateur
router.get('/user-company-info/:userId', authMiddleware.authenticateToken, companySettingsLimiter, asyncHandler(invoiceController.getUserCompanyInfo));

export default router; 