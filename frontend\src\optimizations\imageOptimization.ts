// Configuration pour le lazy loading des images
export const lazyLoadImage = (
  imageElement: HTMLImageElement,
  src: string,
  options: IntersectionObserverInit = {}
) => {
  const defaultOptions = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        imageElement.src = src;
        observer.unobserve(imageElement);
      }
    });
  }, { ...defaultOptions, ...options });

  observer.observe(imageElement);
};

// Fonction pour optimiser la taille des images
export const optimizeImageSize = (file: File, maxWidth = 1200): Promise<Blob> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width);
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            resolve(blob!);
          },
          'image/jpeg',
          0.8
        );
      };
      img.src = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  });
};

// Fonction pour précharger les images critiques
export const preloadCriticalImages = (imagePaths: string[]) => {
  imagePaths.forEach(path => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = path;
    document.head.appendChild(link);
  });
};

// Fonction pour générer des srcset pour les images responsives
export const generateSrcSet = (imagePath: string): string => {
  const sizes = [320, 640, 960, 1280];
  return sizes
    .map(size => `${imagePath}?w=${size} ${size}w`)
    .join(', ');
}; 