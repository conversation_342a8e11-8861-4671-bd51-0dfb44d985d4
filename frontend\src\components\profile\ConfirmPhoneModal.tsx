import React from 'react';
import ModalPortal from '../ModalPortal';
import { Lock as LockIcon } from 'lucide-react';

interface ConfirmPhoneModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  previousPhone: string;
  previousPhonePrive: boolean;
  tempPhone: string;
  tempPhonePrive: boolean;
}

const ConfirmPhoneModal: React.FC<ConfirmPhoneModalProps> = ({
  open,
  onClose,
  onConfirm,
  previousPhone,
  previousPhonePrive,
  tempPhone,
  tempPhonePrive,
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
          <h3 className="text-lg font-semibold mb-4">Confirmer la modification</h3>
          <div className="space-y-4 overflow-y-auto flex-grow pr-2">
            <div>
              <p className="text-sm text-gray-500 mb-2">Vos données actuelles :</p>
              <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through">
                {previousPhone || 'Aucun numéro'}
                <div className="text-sm text-gray-500 mt-1 flex items-center gap-1">
                  <span>État :</span>
                  {previousPhonePrive ? (
                    <span className="flex items-center gap-1">
                      Privé <LockIcon className="h-4 w-4 text-gray-400" />
                    </span>
                  ) : (
                    <span>Public</span>
                  )}
                </div>
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">Vos nouvelles données :</p>
              <div className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg">
                {tempPhone}
                <div className="text-sm text-gray-500 mt-1 flex items-center gap-1">
                  <span>État :</span>
                  {tempPhonePrive ? (
                    <span className="flex items-center gap-1 text-orange-500">
                      Privé <LockIcon className="h-4 w-4" />
                    </span>
                  ) : (
                    <span>Public</span>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-4 flex items-start bg-red-100 p-2 rounded">
              <input
                type="checkbox"
                id="confirmation-checkbox-phone"
                className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md"
                // Le checked et onChange seront gérés dans le parent (Profile.tsx)
              />
              <label htmlFor="confirmation-checkbox-phone" className="text-sm">
                Vous devez cocher cette case pour confirmer la modification.
              </label>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={onConfirm}
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
            >
              Confirmer
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmPhoneModal; 