import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { decryptProfilDataAsync } from '../utils/encryption';

interface Coordinates {
  lat: number;
  lng: number;
}

interface ServiceZone {
  user_id: string;
  category_id: string;
  subcategory_id: string;
  intervention_zone: {
    center: [number, number];
    radius: number;
    adresse?: string;
    france_entiere?: boolean;
  };
}

export class DistanceService {
  // Calcul de la distance entre deux points en km (formule de Haversine)
  private static calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRad(point2.lat - point1.lat);
    const dLon = this.toRad(point2.lng - point1.lng);
    const lat1 = this.toRad(point1.lat);
    const lat2 = this.toRad(point2.lat);

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private static toRad(value: number): number {
    return value * Math.PI / 180;
  }

  // Récupérer les jobbeurs correspondant à une mission
  public static async findMatchingJobbers(
    missionId: string,
    categoryId: string,
    subcategoryId: string,
    missionLocation: Coordinates
  ): Promise<string[]> {
    try {
      // Vérifier si le résultat est en cache
      const cacheKey = `mission:${missionId}:matches`;
      const cachedResult = await redis.get(cacheKey);
      
      if (cachedResult) {
        logger.info('Résultats trouvés en cache pour la mission', { missionId });
        return JSON.parse(cachedResult);
      }

      // 1. Récupérer tous les services actifs correspondant à la catégorie/sous-catégorie
      const { data: services, error: servicesError } = await supabase
        .from('user_services')
        .select('user_id')
        .eq('category_id', categoryId)
        .eq('subcategory_id', subcategoryId)
        .eq('statut', 'actif');

      if (servicesError) {
        logger.error('Erreur lors de la récupération des services', { error: servicesError });
        throw servicesError;
      }

      if (!services || services.length === 0) {
        return [];
      }

      // 2. Récupérer les profils des jobbeurs avec leurs zones d'intervention
      const { data: profiles, error: profilesError } = await supabase
        .from('user_profil')
        .select('user_id, intervention_zone')
        .in('user_id', services.map(s => s.user_id))
        .not('intervention_zone', 'is', null);

      if (profilesError) {
        logger.error('Erreur lors de la récupération des profils', { error: profilesError });
        throw profilesError;
      }

      // 3. Filtrer les jobbeurs en fonction de la distance
      const matchingJobbers = [];
      
      for (const profile of profiles) {
        // Déchiffrer les données du profil
        const decryptedProfile = await decryptProfilDataAsync(profile);
        
        // Vérifier si le jobbeur a une zone d'intervention valide
        if (!decryptedProfile?.intervention_zone?.center || !decryptedProfile?.intervention_zone?.radius) {
          continue;
        }

        // Si le jobbeur couvre toute la France, on le garde
        if (decryptedProfile.intervention_zone.france_entiere) {
          matchingJobbers.push(profile.user_id);
          continue;
        }

        // Sinon, on vérifie la distance
        const jobberLocation: Coordinates = {
          lat: decryptedProfile.intervention_zone.center[0],
          lng: decryptedProfile.intervention_zone.center[1]
        };

        const distance = this.calculateDistance(missionLocation, jobberLocation);
        if (distance <= decryptedProfile.intervention_zone.radius) {
          matchingJobbers.push(profile.user_id);
        }
      }

      // Mettre en cache le résultat pour 1 heure
      await redis.setex(cacheKey, 3600, JSON.stringify(matchingJobbers));

      logger.info('Jobbeurs correspondants trouvés', { 
        missionId, 
        totalServices: services.length,
        totalProfiles: profiles.length,
        matchingCount: matchingJobbers.length 
      });

      return matchingJobbers;
    } catch (error) {
      logger.error('Erreur lors de la recherche des jobbeurs correspondants', { error });
      throw error;
    }
  }

  // Vérifier si un jobbeur correspond à une mission
  public static async checkJobberMatch(
    jobberId: string,
    missionLocation: Coordinates,
    categoryId: string,
    subcategoryId: string
  ): Promise<boolean> {
    try {
      const cacheKey = `jobber:${jobberId}:mission:${categoryId}:${subcategoryId}:match`;
      const cachedResult = await redis.get(cacheKey);

      if (cachedResult !== null) {
        return cachedResult === 'true';
      }

      // 1. Vérifier si le jobbeur a le service correspondant
      const { data: service, error: serviceError } = await supabase
        .from('user_services')
        .select('id')
        .eq('user_id', jobberId)
        .eq('category_id', categoryId)
        .eq('subcategory_id', subcategoryId)
        .eq('statut', 'actif')
        .single();

      if (serviceError || !service) {
        await redis.setex(cacheKey, 3600, 'false');
        return false;
      }

      // 2. Vérifier la zone d'intervention du jobbeur
      const { data: profile, error: profileError } = await supabase
        .from('user_profil')
        .select('intervention_zone')
        .eq('user_id', jobberId)
        .single();

      if (profileError || !profile || !profile.intervention_zone) {
        await redis.setex(cacheKey, 3600, 'false');
        return false;
      }

      // Déchiffrer les données du profil
      const decryptedProfile = await decryptProfilDataAsync(profile);

      if (!decryptedProfile?.intervention_zone) {
        await redis.setex(cacheKey, 3600, 'false');
        return false;
      }

      // Si le jobbeur couvre toute la France
      if (decryptedProfile.intervention_zone.france_entiere) {
        await redis.setex(cacheKey, 3600, 'true');
        return true;
      }

      // Vérifier la distance
      if (!decryptedProfile.intervention_zone.center || !decryptedProfile.intervention_zone.radius) {
        await redis.setex(cacheKey, 3600, 'false');
        return false;
      }

      const jobberLocation: Coordinates = {
        lat: decryptedProfile.intervention_zone.center[0],
        lng: decryptedProfile.intervention_zone.center[1]
      };

      const distance = this.calculateDistance(missionLocation, jobberLocation);
      const matches = distance <= decryptedProfile.intervention_zone.radius;

      await redis.setex(cacheKey, 3600, matches.toString());
      return matches;
    } catch (error) {
      logger.error('Erreur lors de la vérification de correspondance du jobbeur', { error });
      return false;
    }
  }
} 