import React, { useState, useEffect, useCallback } from 'react';
import { useNavi<PERSON>, use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
  Alert,
  useTheme,
  SelectChangeEvent,
  Breadcrumbs,
  useMediaQuery,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Snackbar,
  CircularProgress,
  Divider,
  Stack,
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import TagSelector from '../../components/support/TagSelector';
import useTickets from '../../hooks/useTickets';
import supportTicketService, { 
  Tag, 
  Ticket,
  Attachment
} from '../../services/supportTicketService';
import { logger } from '../../utils/logger';
import { API_CONFIG } from '../../config/api';
import { getMultipartHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import HomeIcon from '@mui/icons-material/Home';
import SupportIcon from '@mui/icons-material/Support';
import ListAltIcon from '@mui/icons-material/ListAlt';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import AddIcon from '@mui/icons-material/Add';
import UploadIcon from '@mui/icons-material/Upload';
import DeleteIcon from '@mui/icons-material/Delete';
import ImageIcon from '@mui/icons-material/Image';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import InfoIcon from '@mui/icons-material/Info';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import imageCompression from 'browser-image-compression';

const SupportTicketForm: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { ticketId } = useParams();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isAdminRoute = window.location.pathname.includes('/admin/');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [existingAttachments, setExistingAttachments] = useState<Attachment[]>([]);
  const [isAttachmentsLoading, setIsAttachmentsLoading] = useState(false);
  const [attachmentsError, setAttachmentsError] = useState<string | null>(null);
  const [deletingAttachmentId, setDeletingAttachmentId] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [compressionProgress, setCompressionProgress] = useState<number>(0);
  
  // Vérifier si l'utilisateur est admin ou modo
  const isAdminOrModo = user?.role === 'jobpadm' || user?.role === 'jobmodo';
  
  const [ticket, setTicket] = useState<Ticket>({
    id: '',
    title: '',
    description: '',
    priority: 'normale',
    category: 'technique',
    status: 'nouveau',
    created_at: '',
    updated_at: '',
    user_id: '',
    tags: [],
    repondu: false,
  });

  // Utiliser notre hook pour les opérations de tickets
  const { getTicket, updateTicket } = useTickets();

  // Vérifier si l'utilisateur est connecté
  useEffect(() => {
    if (!user) {
      navigate('/login', { replace: true });
    }
  }, [user, navigate]);

  useEffect(() => {
    if (ticketId) {
      fetchTicket();
      fetchExistingAttachments();
    }
  }, [ticketId]);

  const fetchExistingAttachments = async () => {
    if (!ticketId) return;
    
    try {
      setIsAttachmentsLoading(true);
      setAttachmentsError(null);
      
      const response = await supportTicketService.getTicketAttachments(ticketId);
      
      // Log pour déboguer le format de la réponse
      logger.info('Réponse de l\'API getTicketAttachments:', 
        typeof response, 
        Array.isArray(response) ? 'est un tableau' : 'n\'est pas un tableau'
      );
      
      // S'assurer que nous avons bien un tableau d'attachements
      let attachments: Attachment[] = [];
      
      if (Array.isArray(response)) {
        attachments = response;
      } else if (response && typeof response === 'object') {
        // Si c'est un objet qui contient potentiellement un tableau data
        const anyResponse = response as any;
        if (anyResponse.data && Array.isArray(anyResponse.data)) {
          attachments = anyResponse.data;
        }
      }
      
      setExistingAttachments(attachments);
      logger.info(`${attachments.length} pièces jointes récupérées pour le ticket ${ticketId}`);
    } catch (error) {
      logger.error('Erreur lors de la récupération des pièces jointes:', error);
      setAttachmentsError('Impossible de charger les pièces jointes existantes');
      setExistingAttachments([]); // Réinitialiser à un tableau vide en cas d'erreur
    } finally {
      setIsAttachmentsLoading(false);
    }
  };

  const fetchTicket = async () => {
    try {
      if (!ticketId) return;
      
      setLoading(true);
      const ticketData = await getTicket(ticketId);

      if (ticketData) {
        setTicket(ticketData);
      } else {
        setError('Ticket non trouvé');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du ticket:', error);
      setError('Impossible de charger le ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: keyof Ticket) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    setTicket(prev => ({ ...prev, [field]: event.target.value }));
  };

  const handleTagsChange = (newTags: Tag[]) => {
    setTicket(prev => ({ ...prev, tags: newTags }));
  };

  const showNotification = (message: string, severity: 'success' | 'error') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles) return;

    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf', 'text/plain'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    let filesToAdd: File[] = [];
    let totalImages = Array.from(selectedFiles).filter(f => f.type.startsWith('image/')).length;
    let compressedCount = 0;
    if (totalImages > 0) setCompressionProgress(1);
    for (const file of Array.from(selectedFiles)) {
      if (!allowedMimeTypes.includes(file.type)) {
        showNotification(`Le format ${file.type} n'est pas supporté. Formats acceptés : JPG, PNG, WEBP, PDF, TXT`, 'error');
        continue;
      }
      if (file.size > maxSize) {
        showNotification(`Le fichier ${file.name} dépasse la taille maximale de 5MB`, 'error');
        continue;
      }
      if (file.type.startsWith('image/')) {
        try {
          const compressed = await imageCompression(file, {
            maxSizeMB: 1.5,
            maxWidthOrHeight: 1920,
            useWebWorker: true,
            onProgress: (progress) => {
              setCompressionProgress(Math.round(((compressedCount + progress / 100) / totalImages) * 100));
            }
          });
          compressedCount++;
          setCompressionProgress(Math.round((compressedCount / totalImages) * 100));
          filesToAdd.push(compressed);
        } catch (err) {
          showNotification(`Erreur lors de la compression de l'image ${file.name}`, 'error');
        }
      } else {
        filesToAdd.push(file);
      }
    }
    setCompressionProgress(0);
    if (files.length + filesToAdd.length > 5) {
      showNotification('Vous ne pouvez pas ajouter plus de 5 pièces jointes', 'error');
      filesToAdd = filesToAdd.slice(0, 5 - files.length);
    }
    setFiles(prev => [...prev, ...filesToAdd]);
  }, [files]);

  const handleFileRemove = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      if (ticketId) {
        // Mise à jour du ticket
        // Préparation des données à envoyer en fonction du rôle
        const ticketData: Partial<Ticket> = {
          title: ticket.title,
          description: ticket.description,
        };
        
        // Ajout des champs réservés aux admins/modos
        if (isAdminOrModo) {
          ticketData.priority = ticket.priority;
          ticketData.category = ticket.category;
          ticketData.status = ticket.status;
          ticketData.tags = ticket.tags;
        }
        
        const updatedTicket = await updateTicket(ticketId, ticketData);

        // Si le ticket est mis à jour avec succès et qu'il y a des fichiers à uploader
        if (updatedTicket && files.length > 0) {
          // Uploader les fichiers un par un
          const uploadPromises = files.map(async (file) => {
            try {
              await supportTicketService.uploadAttachment(ticketId, file);
              return true;
            } catch (error) {
              logger.error('Erreur lors de l\'upload du fichier:', file.name, error);
              return false;
            }
          });
          
          await Promise.all(uploadPromises);
          showNotification('Ticket mis à jour avec les nouvelles pièces jointes', 'success');
        } else if (updatedTicket) {
          showNotification('Ticket mis à jour avec succès', 'success');
        } else {
          throw new Error('Erreur lors de la mise à jour du ticket');
        }

        navigate(isAdminRoute ? `/admin/support/ticket/${ticketId}` : `/dashboard/support/ticket/${ticketId}`);
      } else {
        // Création d'un nouveau ticket sans fichiers
        try {
          // Créer d'abord le ticket sans les pièces jointes
          const ticketData = {
            title: ticket.title,
            description: ticket.description,
            priority: ticket.priority,
            category: ticket.category
          };
          
          // Utiliser le service existant pour créer le ticket
          const createdTicket = await supportTicketService.createTicket(ticketData);
          
          // Si le ticket est créé avec succès et qu'il y a des fichiers à uploader
          if (createdTicket && createdTicket.id && files.length > 0) {
            // Uploader les fichiers un par un
            const uploadPromises = files.map(async (file) => {
              try {
                // Créer un nouveau FormData pour chaque fichier
                const fileFormData = new FormData();
                fileFormData.append('files', file);
                
                // S'assurer d'avoir un token CSRF valide
                await fetchCsrfToken();
                
                // Obtenir les headers pour l'authentification
                const uploadHeaders = await getMultipartHeaders();
                
                // Appel API pour uploader le fichier
                const response = await fetch(`${API_CONFIG.baseURL}/api/support/${createdTicket.id}/attachments`, {
                  method: 'POST',
                  headers: uploadHeaders,
                  body: fileFormData,
                  credentials: 'include'
                });
                
                if (!response.ok) {
                  logger.error('Échec d\'upload pour le fichier:', file.name);
                }
                
                return response.ok;
              } catch (error) {
                logger.error('Erreur lors de l\'upload du fichier:', file.name, error);
                return false;
              }
            });
            
            // Attendre tous les uploads
            await Promise.all(uploadPromises);
          }
          
          // Rediriger vers la page du ticket créé
          navigate(isAdminRoute ? `/admin/support/ticket/${createdTicket.id}` : `/dashboard/support/ticket/${createdTicket.id}`);
        } catch (error) {
          logger.error('Erreur lors de la création du ticket:', error);
          setError('Une erreur est survenue lors de la création du ticket');
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde du ticket:', error);
      setError('Une erreur est survenue lors de la sauvegarde du ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleExistingAttachmentRemove = async (attachmentId: string) => {
    try {
      setDeletingAttachmentId(attachmentId);
      await supportTicketService.deleteAttachment(ticketId!, attachmentId);
      
      // Mettre à jour la liste des pièces jointes existantes
      setExistingAttachments(prev => prev.filter(attachment => attachment.id !== attachmentId));
      showNotification('Pièce jointe supprimée avec succès', 'success');
    } catch (error) {
      logger.error('Erreur lors de la suppression de la pièce jointe:', error);
      showNotification('Erreur lors de la suppression de la pièce jointe', 'error');
    } finally {
      setDeletingAttachmentId(null);
    }
  };

  return (
    <Box 
      sx={{padding: '24px'}}
    >
      <Box 
        sx={{width: '100%'}}
      >
        {/* Fil d'Ariane */}
        <Breadcrumbs 
          aria-label="breadcrumb"
          sx={{ 
            mb: 3, 
            background: 'rgba(255, 255, 255, 0.7)',
            py: 1.5, 
            px: 2, 
            borderRadius: 2,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Button
            component={Link}
            to={isAdminRoute ? "/admin" : "/dashboard"}
            size="small"
            startIcon={<HomeIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            {isAdminRoute ? "Administration" : "Tableau de bord"}
          </Button>
          <Button
            component={Link}
            to={isAdminRoute ? "/admin/support/tickets" : "/dashboard/support/tickets"}
            size="small"
            startIcon={<ListAltIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            Tickets
          </Button>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
            <SupportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
            {ticketId ? 'Modifier le ticket' : 'Créer un nouveau ticket'}
          </Typography>
        </Breadcrumbs>

        {/* Titre et boutons d'action */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', md: 'center' }, 
            gap: { xs: 2, md: 0 },
            mb: 4
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box 
              sx={{ 
                backgroundColor: 'rgba(255, 107, 44, 0.1)', 
                borderRadius: '50%',
                p: 1.5,
                display: 'flex',
                mr: 2
              }}
            >
              <SupportIcon 
                sx={{ 
                  color: '#FF6B2C', 
                  fontSize: { xs: '2rem', sm: '2.5rem' },
                  filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
                }} 
              />
            </Box>
            <Box>
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 700, 
                  color: '#333',
                  fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
                  lineHeight: 1.2
                }}
              >
                {isMobile ? (ticketId ? 'Modifier ticket' : 'Créer ticket') : (ticketId ? 'Modifier le ticket' : 'Créer un nouveau ticket')}
              </Typography>
              <Typography
                variant="subtitle1"
                color="text.secondary"
                sx={{ mt: 0.5, fontWeight: 400 }}
              >
                {ticketId ? 'Modifiez les informations du ticket de support' : 'Créez un nouveau ticket pour l\'équipe de support'}
              </Typography>
            </Box>
          </Box>

          <Button
            component={Link}
            to={isAdminRoute ? "/admin/support/tickets" : "/dashboard/support/tickets"}
            variant="outlined"
            fullWidth={isMobile}
            startIcon={<FormatListBulletedIcon />}
            sx={{ 
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              fontWeight: 500,
              px: 2,
              py: 1.2,
              borderRadius: '8px',
              '&:hover': {
                borderColor: '#FF6B2C',
                color: '#FF6B2C',
                bgcolor: 'rgba(255, 107, 44, 0.05)',
              },
            }}
          >
            {isMobile ? 'Retour à la liste' : 'Liste des tickets'}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Formulaire */}
        <Paper
          elevation={isMobile ? 2 : 3}
          sx={{
            borderRadius: { xs: '12px', sm: '16px' },
            overflow: 'hidden',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
            position: 'relative',
            minHeight: 'auto',
            display: 'flex',
            flexDirection: 'column',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '6px',
              background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
            },
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            '&:hover': {
              transform: 'translateY(-3px)',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          <Box sx={{ 
            p: { xs: 2, sm: 3, md: 4 },
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'visible',
          }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    label="Titre"
                    value={ticket.title}
                    onChange={handleChange('title')}
                    required
                    placeholder="Décrivez brièvement votre problème"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255, 107, 44, 0.5)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#FF6B2C',
                        }
                      }
                    }}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={ticket.description}
                    onChange={handleChange('description')}
                    required
                    multiline
                    rows={4}
                    placeholder="Décrivez en détail votre problème"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255, 107, 44, 0.5)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#FF6B2C',
                        }
                      }
                    }}
                  />
                </Grid>
                
                {/* Champs visibles uniquement pour les admins et modos */}
                {(isAdminOrModo || !ticketId) && (
                  <>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth required sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(255, 107, 44, 0.5)',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#FF6B2C',
                          }
                        }
                      }}>
                        <InputLabel>Priorité</InputLabel>
                        <Select
                          value={ticket.priority}
                          label="Priorité"
                          onChange={handleChange('priority')}
                        >
                          <MenuItem value="faible">Faible</MenuItem>
                          <MenuItem value="normale">Normale</MenuItem>
                          <MenuItem value="elevee">Élevée</MenuItem>
                          <MenuItem value="urgente">Urgente</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth required sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'rgba(255, 107, 44, 0.5)',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#FF6B2C',
                          }
                        }
                      }}>
                        <InputLabel>Catégorie</InputLabel>
                        <Select
                          value={ticket.category}
                          label="Catégorie"
                          onChange={handleChange('category')}
                        >
                          <MenuItem value="technique">Technique</MenuItem>
                          <MenuItem value="facturation">Facturation</MenuItem>
                          <MenuItem value="compte">Compte</MenuItem>
                          <MenuItem value="mission">Mission</MenuItem>
                          <MenuItem value="autre">Autre</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}
                
                {ticketId && isAdminOrModo && (
                  <Grid size={{ xs: 12, md: 6 }}>
                    <FormControl fullWidth required sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255, 107, 44, 0.5)',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#FF6B2C',
                        }
                      }
                    }}>
                      <InputLabel>Statut</InputLabel>
                      <Select
                        value={ticket.status}
                        label="Statut"
                        onChange={handleChange('status')}
                      >
                        <MenuItem value="nouveau">Nouveau</MenuItem>
                        <MenuItem value="en_cours">En cours</MenuItem>
                        <MenuItem value="resolu">Résolu</MenuItem>
                        <MenuItem value="ferme">Fermé</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}
                
                {isAdminOrModo && (
                  <Grid size={12}>
                    <TagSelector
                      selectedTags={ticket.tags || []}
                      onTagsChange={handleTagsChange}
                      ticketId={ticketId}
                    />
                  </Grid>
                )}
                
                {/* Section des pièces jointes - visible pour tous */}
                <Grid size={12}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h6" sx={{ color: '#FF6B2C', fontSize: '1rem', mb: 1 }}>
                      Pièces jointes
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block', mb: 1 }}>
                      Formats acceptés : JPG, PNG, WEBP, PDF, TXT (max 5MB par fichier, 5 fichiers maximum)
                    </Typography>
                    
                    {/* Message sur la politique de conservation des pièces jointes */}
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 2, 
                        mb: 2, 
                        bgcolor: 'rgba(255, 247, 240, 0.7)',
                        border: '1px solid rgba(255, 107, 44, 0.15)',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'flex-start'
                      }}
                    >
                      <InfoIcon sx={{ color: '#FF6B2C', mr: 1.5, mt: 0.3, fontSize: '1.2rem' }} />
                      <Box>
                        <Typography variant="body2" sx={{ color: '#333', fontWeight: 500, mb: 0.5 }}>
                          Politique de conservation des fichiers
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.875rem', mb: 1 }}>
                          Pour des raisons environnementales et d'optimisation de nos serveurs, les pièces jointes sont automatiquement supprimées :
                        </Typography>
                        <Box component="div" sx={{ color: 'text.secondary', fontSize: '0.875rem', ml: 1 }}>
                          <ul style={{ margin: '0', paddingLeft: '20px' }}>
                            <li>Après 60 jours (2 mois) à compter de leur téléversement</li>
                            <li>Immédiatement à la résolution ou fermeture du ticket</li>
                          </ul>
                        </Box>
                        <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.875rem', mt: 1, fontStyle: 'italic' }}>
                          Cette mesure contribue à réduire notre empreinte carbone et à optimiser l'espace de stockage.
                        </Typography>
                      </Box>
                    </Paper>
                    
                    <input
                      type="file"
                      accept=".jpg,.jpeg,.png,.webp,.pdf,.txt,image/jpeg,image/png,image/webp,application/pdf,text/plain"
                      multiple
                      onChange={handleFileSelect}
                      style={{ display: 'none' }}
                      id="file-upload"
                    />

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {/* Affichage des pièces jointes existantes pour l'édition */}
                      {ticketId && (
                        <Box sx={{ mb: 2 }}>
                          <Typography 
                            variant="subtitle2" 
                            sx={{ 
                              color: 'text.secondary', 
                              mb: 1, 
                              display: 'flex', 
                              alignItems: 'center' 
                            }}
                          >
                            <AttachFileIcon sx={{ fontSize: '1rem', mr: 0.5 }} />
                            Pièces jointes existantes
                          </Typography>
                          
                          {isAttachmentsLoading ? (
                            <Box sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
                              <CircularProgress size={20} sx={{ mr: 2, color: '#FF6B2C' }} />
                              <Typography variant="body2" color="text.secondary">
                                Chargement des pièces jointes...
                              </Typography>
                            </Box>
                          ) : attachmentsError ? (
                            <Alert severity="error" sx={{ mb: 2 }}>
                              {attachmentsError}
                            </Alert>
                          ) : !Array.isArray(existingAttachments) ? (
                            <Alert severity="error" sx={{ mb: 2 }}>
                              Format de données incorrect pour les pièces jointes
                            </Alert>
                          ) : existingAttachments.length === 0 ? (
                            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', py: 1 }}>
                              Aucune pièce jointe
                            </Typography>
                          ) : (
                            <List dense sx={{ bgcolor: 'rgba(0, 0, 0, 0.02)', borderRadius: '8px', mb: 2 }}>
                              {existingAttachments.map((attachment) => (
                                <ListItem
                                  key={attachment.id}
                                  secondaryAction={
                                    <IconButton 
                                      edge="end" 
                                      onClick={() => handleExistingAttachmentRemove(attachment.id)}
                                      disabled={!!deletingAttachmentId}
                                      sx={{ 
                                        color: '#e74c3c',
                                        '&:hover': { bgcolor: 'rgba(231, 76, 60, 0.08)' }
                                      }}
                                    >
                                      {deletingAttachmentId === attachment.id ? (
                                        <CircularProgress size={20} sx={{ color: '#e74c3c' }} />
                                      ) : (
                                        <DeleteIcon />
                                      )}
                                    </IconButton>
                                  }
                                >
                                  <ListItemIcon sx={{ minWidth: '40px' }}>
                                    {attachment.file_type?.startsWith('image/') ? (
                                      <ImageIcon sx={{ color: '#FF6B2C' }} />
                                    ) : attachment.file_type === 'application/pdf' ? (
                                      <PictureAsPdfIcon sx={{ color: '#FF6B2C' }} />
                                    ) : (
                                      <InsertDriveFileIcon sx={{ color: '#FF6B2C' }} />
                                    )}
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={attachment.file_name}
                                    secondary={`${(attachment.file_size / 1024 / 1024).toFixed(2)} MB`}
                                    primaryTypographyProps={{ 
                                      variant: 'body2',
                                      style: { fontWeight: 500 }
                                    }}
                                    secondaryTypographyProps={{ 
                                      variant: 'caption' 
                                    }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          )}
                          
                          <Divider sx={{ my: 2 }} />
                        </Box>
                      )}
                      
                      <label htmlFor="file-upload">
                        <Button
                          component="span"
                          variant="outlined"
                          startIcon={<UploadIcon />}
                          sx={{
                            color: '#FF6B2C',
                            borderColor: '#FF6B2C',
                            '&:hover': {
                              borderColor: '#FF6B2C',
                              backgroundColor: 'rgba(255, 107, 44, 0.04)'
                            }
                          }}
                        >
                          Ajouter des fichiers
                        </Button>
                      </label>

                      {files.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <List>
                            {files.map((file, index) => (
                              <ListItem
                                key={index}
                                secondaryAction={
                                  <IconButton edge="end" onClick={() => handleFileRemove(index)}>
                                    <DeleteIcon />
                                  </IconButton>
                                }
                              >
                                <ListItemIcon>
                                  {file.type.startsWith('image/') ? (
                                    <ImageIcon sx={{ color: '#FF6B2C' }} />
                                  ) : file.type === 'application/pdf' ? (
                                    <PictureAsPdfIcon sx={{ color: '#FF6B2C' }} />
                                  ) : (
                                    <InsertDriveFileIcon sx={{ color: '#FF6B2C' }} />
                                  )}
                                </ListItemIcon>
                                <ListItemText
                                  primary={file.name}
                                  secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Grid>
                
                <Grid size={12} sx={{ mt: 2 }}>
                  <Stack 
                    direction={{ xs: 'column-reverse', sm: 'row' }} 
                    justifyContent={{ xs: 'center', sm: 'space-between' }}
                    spacing={2}
                  >
                    <Button
                      variant="outlined"
                      color="inherit"
                      onClick={() => navigate(-1)}
                      sx={{ 
                        borderColor: 'rgba(0, 0, 0, 0.2)',
                        color: 'text.secondary',
                        borderRadius: '8px',
                        py: 1.2,
                        fontWeight: 500,
                        width: { xs: '100%', sm: 'auto' },
                        '&:hover': {
                          borderColor: 'rgba(0, 0, 0, 0.5)',
                          backgroundColor: 'rgba(0, 0, 0, 0.03)'
                        }
                      }}
                    >
                      Annuler
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      startIcon={!loading && !ticketId ? <AddIcon /> : undefined}
                      sx={{
                        bgcolor: '#FF6B2C',
                        borderRadius: '8px',
                        py: 1.2,
                        px: 3,
                        fontWeight: 600,
                        width: { xs: '100%', sm: 'auto' },
                        boxShadow: '0 4px 10px rgba(255, 107, 44, 0.2)',
                        '&:hover': {
                          bgcolor: '#FF7A35',
                          boxShadow: '0 6px 15px rgba(255, 107, 44, 0.3)',
                        },
                      }}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <CircularProgress
                            size={24}
                            sx={{ color: 'white', mr: 1 }}
                          />
                          {ticketId ? 'Mise à jour...' : 'Création...'}
                        </>
                      ) : (
                        ticketId ? 'Mettre à jour' : 'Créer le ticket'
                      )}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Paper>
      </Box>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity}>
          {snackbarMessage}
        </Alert>
      </Snackbar>

      {/* Barre de progression compression image */}
      {compressionProgress > 0 && compressionProgress < 100 && (
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${compressionProgress}%` }} />
          <div className="text-xs text-center mt-1 text-gray-500">Compression en cours… {compressionProgress}%</div>
        </div>
      )}
    </Box>
  );
};

export default SupportTicketForm; 