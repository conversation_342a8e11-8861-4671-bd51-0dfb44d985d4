import React, { useEffect, useState, useRef } from 'react';
import { Award, Star, Clock, MessageCircle, Users, PieChart, Megaphone, Sun, Leaf, Clock3, Briefcase, Trophy as TrophyIcon, ThumbsUp, Heart, Zap, Shield, BellRing, BookOpen, Target, MapPin, Medal, User, PartyPopper, Building, Truck, Mail, GraduationCap, Laptop, Droplet, Scissors, Lock, Trees, Music, Activity, Home, Car, Palette, DollarSign, Map, Hammer, Pencil, Share2, X, Check, Info } from 'lucide-react';
import { notify } from '../../../components/Notification';
import axios from 'axios';
import { getCommonHeaders } from '../../../utils/headers';
import { API_CONFIG } from '../../../config/api';
import logger from '../../../utils/logger';
import { fetchCsrfToken } from '../../../services/csrf';

export interface Badge {
  id: string;
  title: string;
  description: string;
  detailedDescription: string;
  icon: React.ReactNode;
  condition: (profil: any) => boolean;
  recompense_jobi: number;
  backgroundColor: string;
  iconColor: string;
  is_lifetime?: boolean;
}

export const badges: Badge[] = [
  {
    id: 'top-jobbeur-2025',
    title: 'Top Jobbeur 2025',
    description: 'Fait partie des jobbeurs les mieux notés',
    detailedDescription: 'La distinction suprême ! Ce badge prestigieux est réservé aux jobbeurs d\'élite qui maintiennent une note exceptionnelle de 4,8/5 ou plus sur au moins 100 avis. Il symbolise l\'excellence absolue et vous place parmi les meilleurs prestataires de JobPartiel.',
    icon: <Award className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 220,
    condition: (profil) => (profil?.avg_rating >= 4.8 && profil?.reviews_count >= 100),
    is_lifetime: true
  },
  {
    id: 'super-reactif',
    title: 'Super Réactif',
    description: 'Répond aux demandes en moins de 2h en moyenne',
    detailedDescription: 'La rapidité est votre force ! Ce badge distingue les jobbeurs qui répondent aux demandes en moins de 2 heures en moyenne. Cette réactivité exceptionnelle augmente significativement vos chances d\'être sélectionné pour les missions et reflète votre professionnalisme.',
    icon: <Zap className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 50,
    condition: (profil) => typeof profil?.avg_response_time === 'number' && profil.avg_response_time > 0 && profil.avg_response_time <= 120
  },
  {
    id: 'excellent-communicant',
    title: 'Excellent Communicant',
    description: 'Répond à plus de 95% des messages',
    detailedDescription: 'La communication est la clé du succès ! Ce badge récompense les jobbeurs qui répondent à plus de 95% des messages qu\'ils reçoivent. Cette fiabilité dans les échanges rassure les clients et permet d\'établir des relations professionnelles solides et durables.',
    icon: <MessageCircle className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 5,
    condition: (profil) => profil?.response_rate >= 0.95
  },
  {
    id: 'reseau-etendu',
    title: 'Réseau Étendu',
    description: 'A établi plus de 100 connexions',
    detailedDescription: 'Vous êtes un véritable networker ! Ce badge célèbre les jobbeurs qui ont établi plus de 100 connexions sur la plateforme. Ce réseau étendu témoigne de votre capacité à créer des liens professionnels solides et à développer votre activité de manière organique.',
    icon: <Share2 className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => profil?.connections_count >= 100
  },
  {
    id: 'fidele-jobbeur',
    title: 'Fidèle Jobbeur',
    description: 'Membre depuis plus d\'un an',
    detailedDescription: 'La loyauté récompensée ! Ce badge honore les jobbeurs qui font partie de la communauté JobPartiel depuis plus d\'un an. Votre engagement sur la durée témoigne de votre satisfaction et de votre confiance envers la plateforme.',
    icon: <Clock className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 50,
    condition: (profil) => {
      if (!profil?.registration_date) return false;
      const regDate = new Date(profil.registration_date);
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      return regDate <= oneYearAgo;
    }
  },
  {
    id: 'pilier-communaute',
    title: 'Pilier de la Communauté',
    description: 'A participé à plus de 50 missions',
    detailedDescription: 'Un contributeur essentiel ! Ce badge récompense les jobbeurs ayant participé à plus de 50 missions sur la plateforme. Votre implication constante et votre contribution significative font de vous un membre respecté et indispensable de la communauté JobPartiel.',
    icon: <Users className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 75,
    condition: (profil) => profil?.missions_count >= 50
  },
  {
    id: 'service-excellence',
    title: 'Service d\'Excellence',
    description: 'Maintient une note parfaite de 5/5 sur au moins 20 avis',
    detailedDescription: 'La perfection incarnée ! Ce badge distingue les jobbeurs qui maintiennent la note parfaite de 5/5 sur au moins 20 avis. Cette excellence constante témoigne de votre engagement sans faille envers la qualité et la satisfaction client à chaque mission.',
    icon: <Star className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 67,
    condition: (profil) => profil?.perfect_rating_count >= 20
  },
  {
    id: 'ponctualite-exemplaire',
    title: 'Ponctualité Exemplaire',
    description: 'N\'a jamais été en retard sur les délais convenus',
    detailedDescription: 'La fiabilité à toute épreuve ! Ce badge célèbre les jobbeurs qui n\'ont jamais été en retard sur les délais convenus pour leurs missions. Cette ponctualité irréprochable est une qualité essentielle qui inspire confiance et prouve votre professionnalisme à chaque intervention.',
    icon: <Clock3 className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => profil?.on_time_completion_count >= 15
  },
  {
    id: 'multi-services',
    title: 'Multi-Services',
    description: 'Propose au moins 5 services différents',
    detailedDescription: 'La polyvalence récompensée ! Ce badge honore les jobbeurs qui proposent au moins 5 services différents sur la plateforme. Cette diversité de compétences vous permet de répondre à une large gamme de besoins clients et multiplie vos opportunités de missions.',
    icon: <PieChart className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 25,
    condition: (profil) => {
      if (!profil?.services_offered) return false;
      return profil.services_offered.length >= 5;
    }
  },
  {
    id: 'specialiste-polyvalent',
    title: 'Spécialiste Polyvalent',
    description: 'A complété des missions dans au moins 3 catégories',
    detailedDescription: 'L\'adaptabilité est votre force ! Ce badge distingue les jobbeurs qui ont complété des missions dans au moins 3 catégories différentes. Cette polyvalence témoigne de votre capacité à vous adapter à différents contextes et à élargir constamment vos compétences.',
    icon: <Sun className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 20,
    condition: (profil) => profil?.different_categories_count >= 3
  },
  {
    id: 'en-pleine-expansion',
    title: 'En Pleine Expansion',
    description: 'A augmenté son volume d\'activité de 50% en 3 mois',
    detailedDescription: 'Une croissance impressionnante ! Ce badge célèbre les jobbeurs qui ont augmenté leur volume d\'activité de 50% en seulement 3 mois. Cette progression remarquable démontre votre capacité à développer rapidement votre présence sur la plateforme et à attirer toujours plus de clients.',
    icon: <TrophyIcon className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => {
      // Vérifier l'ancienneté d'inscription (plus de 60 jours)
      const registrationDate = new Date(profil?.registration_date);
      const sixtyDaysAgo = new Date();
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

      // Vérifier la croissance d'activité
      return profil?.activity_growth_percentage >= 50 && registrationDate < sixtyDaysAgo;
    },
    is_lifetime: true
  },
  {
    id: 'croissance-exceptionnelle',
    title: 'Une croissance Exceptionnelle',
    description: 'A augmenté son volume d\'activité de 75% en 3 mois',
    detailedDescription: 'Une performance extraordinaire ! Ce badge récompense les jobbeurs qui ont réussi à augmenter leur volume d\'activité de 75% en seulement 3 mois. Cette croissance exceptionnelle témoigne de votre capacité à vous démarquer et à développer votre activité de manière remarquable.',
    icon: <TrophyIcon className="h-5 w-5 text-[#FFD700]" />, // Couleur or pour marquer le prestige
    backgroundColor: '#FFF8F3',
    iconColor: '#FFD700',
    recompense_jobi: 50, // Récompense plus élevée pour une performance plus importante
    condition: (profil) => {
      // Vérifier l'ancienneté d'inscription (plus de 60 jours)
      const registrationDate = new Date(profil?.registration_date);
      const sixtyDaysAgo = new Date();
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

      // Vérifier la croissance d'activité
      return profil?.activity_growth_percentage >= 75 && registrationDate < sixtyDaysAgo;
    },
    is_lifetime: true
  },
  {
    id: 'ambassadeur-jobpartiel',
    title: 'Ambassadeur JobPartiel',
    description: 'A parrainé 5 nouveaux utilisateurs',
    detailedDescription: 'Merci de faire grandir notre communauté ! Ce badge récompense les jobbeurs qui ont parrainé avec succès au moins 5 nouveaux utilisateurs. Votre engagement à promouvoir JobPartiel témoigne de votre satisfaction et de votre confiance envers la plateforme.',
    icon: <Megaphone className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 100,
    condition: (profil) => profil?.referral_count >= 5
  },
  {
    id: 'heros-ete',
    title: 'Héros de l\'Été',
    description: 'A complété 10 missions durant l\'été',
    detailedDescription: 'L\'été est une période cruciale ! Ce badge salue les jobbeurs qui ont complété au moins 10 missions pendant la saison estivale (juin à septembre). Votre disponibilité durant cette période de forte demande est particulièrement appréciée par les clients en recherche de services.',
    icon: <Sun className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => {
      if (!profil?.summer_missions_count) return false;
      const currentYear = new Date().getFullYear();
      return profil.summer_missions_count?.[currentYear] >= 10;
    }
  },
  {
    id: 'champion-ecologie',
    title: 'Champion de l\'Écologie',
    description: 'Spécialiste des services écologiques et durables',
    detailedDescription: 'Le développement durable est crucial ! Ce badge distingue les jobbeurs ayant réalisé au moins 10 missions écologiques ou respectueuses de l\'environnement. Votre engagement pour des pratiques durables contribue à un avenir plus vert et attire une clientèle de plus en plus soucieuse de l\'environnement.',
    icon: <Leaf className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => (profil?.environnement_ecologie_missions_count >= 10) || (profil?.gardening_missions_count >= 20)
  },
  {
    id: 'client-satisfait',
    title: 'Satisfaction Garantie',
    description: 'A reçu 10 avis 5 étoiles consécutifs',
    detailedDescription: 'Une constance remarquable ! Ce badge récompense les jobbeurs qui ont reçu 10 avis à 5 étoiles consécutifs. Cette performance extraordinaire démontre votre capacité à maintenir un niveau d\'excellence constant et à dépasser systématiquement les attentes de vos clients.',
    icon: <ThumbsUp className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => profil?.consecutive_five_star_reviews >= 10
  },
  {
    id: 'coup-de-coeur',
    title: 'Coup de Cœur',
    description: 'A été ajouté aux favoris par 20 utilisateurs',
    detailedDescription: 'Vous êtes populaire ! Ce badge distingue les jobbeurs qui ont été ajoutés aux favoris par au moins 20 utilisateurs différents. Cette popularité témoigne de la qualité de vos services et de votre capacité à créer une relation de confiance durable avec vos clients.',
    icon: <Heart className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 25,
    condition: (profil) => profil?.favorite_count >= 20
  },
  {
    id: 'pionnier',
    title: 'Pionnier',
    description: 'Parmi les 100 premiers utilisateurs de la plateforme',
    detailedDescription: 'Vous étiez là dès le début ! Ce badge exclusif honore les 100 premiers utilisateurs inscrits sur JobPartiel. En tant que membre fondateur, vous avez contribué à établir les bases de notre communauté et à façonner l\'avenir de la plateforme.',
    icon: <Zap className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 67,
    condition: (profil) => profil?.user_id <= 100
  },
  {
    id: 'fiabilite-premium',
    title: 'Fiabilité Premium',
    description: 'Aucune annulation de mission depuis 6 mois',
    detailedDescription: 'La fiabilité est votre force ! Ce badge récompense les jobbeurs n\'ayant annulé aucune mission depuis au moins 6 mois, avec un minimum de 10 missions réalisées. Votre engagement à honorer vos promesses inspire confiance et rassure les clients potentiels sur votre professionnalisme.',
    icon: <Shield className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 80,
    condition: (profil) => {
      if (profil?.missions_count < 10) return false;
      
      if (profil?.cancelled_missions_count === 0) return true;
      
      if (!profil?.last_cancellation_date) return false;
      
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      return new Date(profil.last_cancellation_date) < sixMonthsAgo;
    }
  },
  {
    id: 'premier-de-la-classe',
    title: 'Premier de la Classe',
    description: 'A suivi 5 formations sur la plateforme',
    detailedDescription: 'L\'apprentissage est la clé du succès ! Ce badge récompense les jobbeurs qui ont complété au moins 5 formations sur la plateforme. Votre volonté de vous former continuellement témoigne de votre professionnalisme et de votre désir constant d\'améliorer la qualité de vos services.',
    icon: <BookOpen className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => profil?.completed_trainings_count >= 5
  },
  {
    id: 'toujours-present',
    title: 'Toujours Présent',
    description: 'Connexion à la plateforme 5 jours par semaine',
    detailedDescription: 'La régularité paie ! Ce badge est attribué aux jobbeurs qui se connectent à la plateforme au moins 5 jours par semaine. Votre présence assidue vous permet de répondre rapidement aux demandes et augmente significativement vos chances d\'obtenir des missions.',
    icon: <BellRing className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 5,
    condition: (profil) => profil?.weekly_connection_days >= 5
  },
  {
    id: 'expert-jardinier',
    title: 'Expert Jardinier',
    description: 'A complété 30 missions de jardinage',
    detailedDescription: 'Vous avez la main verte ! Ce badge salue les jobbeurs ayant complété 30 missions de jardinage. Votre expertise dans ce domaine est maintenant reconnue, ce qui vous positionne comme un professionnel de confiance pour tous les besoins d\'entretien et d\'aménagement d\'espaces verts.',
    icon: <Leaf className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.gardening_missions_count >= 30
  },
  {
    id: 'bricoleur-or',
    title: 'Bricoleur en Or',
    description: 'A complété 30 missions de bricolage',
    detailedDescription: 'Vos compétences manuelles sont impressionnantes ! Ce badge récompense les jobbeurs qui ont terminé 30 missions de bricolage. Cette expertise démontre votre capacité à résoudre une multitude de problèmes pratiques, faisant de vous un jobbeur incontournable pour tous types de travaux manuels.',
    icon: <Briefcase className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.diy_missions_count >= 30
  },
  {
    id: 'ami-des-animaux',
    title: 'Ami des Animaux',
    description: 'A complété 30 missions de garde d\'animaux',
    detailedDescription: 'Les animaux vous adorent ! Ce badge distingue les jobbeurs ayant réalisé 30 missions de garde d\'animaux. Votre passion et votre fiabilité dans ce domaine sensible rassurent les propriétaires soucieux du bien-être de leurs compagnons pendant leur absence.',
    icon: <Heart className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.pet_care_missions_count >= 30
  },
  {
    id: 'aidant-pro',
    title: 'Aidant Professionnel',
    description: 'A complété 30 missions de services à la personne',
    detailedDescription: 'Votre empathie fait la différence ! Ce badge honore les jobbeurs ayant réalisé 30 missions d\'aide à la personne. Cette reconnaissance souligne votre capacité à apporter un soutien de qualité aux personnes vulnérables ou en situation de dépendance, un service essentiel nécessitant patience et bienveillance.',
    icon: <User className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.service_personne_missions_count >= 30
  },
  {
    id: 'maestro-evenementiel',
    title: 'Maestro Événementiel',
    description: 'A complété 30 missions événementielles',
    detailedDescription: 'Le sens de la fête à l\'état pur ! Ce badge distingue les jobbeurs ayant organisé 30 missions d\'événementiel ou de restauration. Votre talent pour faire de chaque occasion un moment mémorable est désormais reconnu, faisant de vous un partenaire privilégié pour tous types d\'événements.',
    icon: <PartyPopper className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.evenement_restau_missions_count >= 30
  },
  {
    id: 'as-administration',
    title: 'As de l\'Administration',
    description: 'A complété 30 missions administratives',
    detailedDescription: 'La rigueur administrative est votre force ! Ce badge récompense les jobbeurs ayant réalisé 30 missions de services administratifs. Votre expertise dans ce domaine complexe témoigne de votre organisation et de votre capacité à naviguer efficacement dans les méandres bureaucratiques.',
    icon: <Building className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.services_admin_missions_count >= 30
  },
  {
    id: 'logisticien-expert',
    title: 'Logisticien Expert',
    description: 'A complété 30 missions de transport et logistique',
    detailedDescription: 'La mobilité est votre domaine ! Ce badge distingue les jobbeurs ayant réalisé 30 missions de transport et logistique. Votre efficacité dans l\'organisation des déplacements et la gestion des livraisons fait de vous un partenaire fiable pour toutes les questions de mobilité et logistique.',
    icon: <Truck className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.transport_logistics_missions_count >= 30
  },
  {
    id: 'communicant-talentueux',
    title: 'Communicant Talentueux',
    description: 'A complété 30 missions de communication et marketing',
    detailedDescription: 'Votre créativité communique pour vous ! Ce badge récompense les jobbeurs ayant réalisé 30 missions de communication et marketing. Votre talent pour créer des contenus percutants et déployer des stratégies efficaces vous positionne comme un expert incontournable dans ce domaine dynamique.',
    icon: <Mail className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.communication_marketing_missions_count >= 30
  },
  {
    id: 'formateur-emerite',
    title: 'Formateur Émérite',
    description: 'A complété 30 missions d\'éducation et formation',
    detailedDescription: 'Le partage du savoir est votre vocation ! Ce badge honore les jobbeurs ayant réalisé 30 missions d\'éducation et formation. Votre capacité à transmettre des connaissances et à accompagner les apprenants fait de vous un formateur recherché et apprécié sur la plateforme.',
    icon: <GraduationCap className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.education_training_missions_count >= 30
  },
  {
    id: 'tech-master',
    title: 'Tech Master',
    description: 'A complété 30 missions informatiques',
    detailedDescription: 'La technologie n\'a pas de secret pour vous ! Ce badge distingue les jobbeurs ayant réalisé 30 missions informatiques. Votre expertise technique et votre capacité à résoudre des problèmes complexes font de vous une ressource précieuse pour tous les besoins numériques.',
    icon: <Laptop className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.informatique_missions_count >= 30
  },
  {
    id: 'artiste-accompli',
    title: 'Artiste Accompli',
    description: 'A complété 30 missions d\'arts et spectacles',
    detailedDescription: 'L\'art est votre mode d\'expression ! Ce badge célèbre les jobbeurs ayant réalisé 30 missions dans le domaine des arts et spectacles. Votre créativité et votre talent artistique sont désormais reconnus sur la plateforme, faisant de vous un partenaire privilégié pour tous les projets créatifs.',
    icon: <Music className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.arts_spectacles_missions_count >= 30
  },
  {
    id: 'maitre-bien-etre',
    title: 'Maître du Bien-être',
    description: 'A complété 30 missions de bien-être et santé',
    detailedDescription: 'Le bien-être est votre priorité ! Ce badge honore les jobbeurs ayant réalisé 30 missions dans le domaine du bien-être et de la santé. Votre capacité à créer des environnements propices à la détente et au ressourcement fait de vous un spécialiste reconnu du bien-être.',
    icon: <Activity className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.bien_etre_sante_missions_count >= 30
  },
  {
    id: 'consultant-pro',
    title: 'Consultant Pro',
    description: 'A complété 30 missions de services aux entreprises',
    detailedDescription: 'L\'expertise professionnelle à votre service ! Ce badge distingue les jobbeurs ayant réalisé 30 missions de services aux entreprises. Votre compréhension approfondie des enjeux d\'entreprise et votre capacité à proposer des solutions adaptées font de vous un partenaire stratégique incontournable.',
    icon: <Briefcase className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.services_entreprises_missions_count >= 30
  },
  {
    id: 'artisan-createur',
    title: 'Artisan Créateur',
    description: 'A complété 30 missions d\'artisanat et création',
    detailedDescription: 'Vos mains créent des merveilles ! Ce badge célèbre les jobbeurs ayant réalisé 30 missions d\'artisanat et création. Votre savoir-faire unique et votre créativité font de vous un artisan reconnu, capable de transformer des idées en réalisations concrètes et originales.',
    icon: <Pencil className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.artisanat_creation_missions_count >= 30
  },
  {
    id: 'coach-sportif',
    title: 'Coach Sportif',
    description: 'A complété 30 missions de sport et loisirs',
    detailedDescription: 'Le mouvement est votre passion ! Ce badge honore les jobbeurs ayant réalisé 30 missions dans le domaine des sports et loisirs. Votre énergie et votre capacité à motiver les autres font de vous un coach recherché pour toutes les activités sportives et récréatives.',
    icon: <Activity className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.sport_loisirs_missions_count >= 30
  },
  {
    id: 'expert-immobilier',
    title: 'Expert Immobilier',
    description: 'A complété 30 missions d\'immobilier et habitat',
    detailedDescription: 'L\'habitat n\'a pas de secret pour vous ! Ce badge récompense les jobbeurs ayant réalisé 30 missions dans le domaine de l\'immobilier et de l\'habitat. Votre connaissance du marché et votre capacité à comprendre les besoins de chacun font de vous une référence dans ce secteur exigeant.',
    icon: <Home className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.immobilier_habitat_missions_count >= 30
  },
  {
    id: 'pro-automobile',
    title: 'Pro de l\'Automobile',
    description: 'A complété 30 missions d\'automobile et transport',
    detailedDescription: 'La mécanique est votre domaine d\'excellence ! Ce badge distingue les jobbeurs ayant réalisé 30 missions liées à l\'automobile et au transport. Votre expertise technique et votre passion pour les véhicules font de vous un spécialiste recherché pour tous les services automobiles.',
    icon: <Car className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.automobile_transport_missions_count >= 30
  },
  {
    id: 'designer-interieur',
    title: 'Designer d\'Intérieur',
    description: 'A complété 30 missions de décoration et design',
    detailedDescription: 'L\'esthétique est votre signature ! Ce badge célèbre les jobbeurs ayant réalisé 30 missions de décoration et design. Votre œil pour les détails et votre sens artistique vous permettent de transformer n\'importe quel espace en un environnement harmonieux et fonctionnel.',
    icon: <Palette className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.decoration_design_missions_count >= 30
  },
  {
    id: 'conseiller-financier',
    title: 'Conseiller Financier',
    description: 'A complété 30 missions de services financiers',
    detailedDescription: 'La finance maîtrisée ! Ce badge distingue les jobbeurs ayant réalisé 30 missions de services financiers. Votre rigueur, votre éthique et votre compréhension des enjeux financiers font de vous un conseiller de confiance pour accompagner les clients dans leurs décisions économiques.',
    icon: <DollarSign className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.services_financiers_missions_count >= 30
  },
  {
    id: 'guide-voyage',
    title: 'Guide de Voyage',
    description: 'A complété 30 missions de tourisme et voyages',
    detailedDescription: 'L\'aventure est votre quotidien ! Ce badge récompense les jobbeurs ayant réalisé 30 missions dans le domaine du tourisme et des voyages. Votre connaissance des destinations et votre capacité à créer des expériences mémorables font de vous un guide indispensable pour les aventuriers.',
    icon: <Map className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.tourisme_voyages_missions_count >= 30
  },
  {
    id: 'maitre-renovation',
    title: 'Maître de la Rénovation',
    description: 'A complété 30 missions de rénovation et travaux',
    detailedDescription: 'La transformation des espaces est votre spécialité ! Ce badge honore les jobbeurs ayant réalisé 30 missions de rénovation et travaux. Votre expertise technique et votre vision d\'ensemble vous permettent de donner une nouvelle vie à tous types d\'environnements.',
    icon: <Hammer className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.renovation_travaux_missions_count >= 30
  },
  {
    id: 'piscine-expert',
    title: 'Expert Piscine & Spa',
    description: 'A complété 30 missions d\'entretien de piscine et spa',
    detailedDescription: 'L\'eau est votre élément ! Ce badge distingue les jobbeurs ayant réalisé 30 missions d\'entretien de piscine et spa. Votre expertise technique et votre rigueur garantissent des espaces aquatiques parfaitement entretenus et sécurisés pour les moments de détente.',
    icon: <Droplet className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.piscine_spa_missions_count >= 30
  },
  {
    id: 'style-guru',
    title: 'Guru du Style',
    description: 'A complété 30 missions de mode et beauté',
    detailedDescription: 'L\'élégance est votre signature ! Ce badge célèbre les jobbeurs ayant réalisé 30 missions dans le domaine de la mode et de la beauté. Votre sens du style et votre capacité à sublimer chaque personne font de vous un expert recherché pour tous les conseils esthétiques.',
    icon: <Scissors className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.mode_beaute_missions_count >= 30
  },
  {
    id: 'gardien-securite',
    title: 'Gardien de la Sécurité',
    description: 'A complété 30 missions de sécurité et protection',
    detailedDescription: 'La sécurité avant tout ! Ce badge récompense les jobbeurs ayant réalisé 30 missions de sécurité et protection. Votre vigilance, votre professionnalisme et votre sens des responsabilités font de vous un garant essentiel de la tranquillité d\'esprit de vos clients.',
    icon: <Lock className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.securite_protection_missions_count >= 30
  },
  {
    id: 'eco-protecteur',
    title: 'Éco-Protecteur',
    description: 'A complété 30 missions environnementales',
    detailedDescription: 'La planète vous remercie ! Ce badge honore les jobbeurs ayant réalisé 30 missions environnementales. Votre engagement pour l\'écologie et le développement durable contribue concrètement à la préservation de notre environnement et inspire d\'autres à suivre votre exemple.',
    icon: <Trees className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.environnement_ecologie_missions_count >= 30
  },
  {
    id: 'grand-voyageur',
    title: 'Grand Voyageur',
    description: 'A proposé ses services dans 5 villes différentes',
    detailedDescription: 'La mobilité est votre force ! Ce badge récompense les jobbeurs qui ont proposé leurs services dans au moins 5 villes différentes. Cette flexibilité géographique témoigne de votre adaptabilité et élargit considérablement votre potentiel de missions.',
    icon: <MapPin className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 25,
    condition: (profil) => {
      if (!profil?.cities_worked_in) return false;
      return new Set(profil.cities_worked_in).size >= 5;
    }
  },
  {
    id: 'objectif-atteint',
    title: 'Objectif Atteint',
    description: 'A atteint ses objectifs mensuels 3 mois consécutifs',
    detailedDescription: 'La persévérance paie ! Ce badge distingue les jobbeurs qui ont atteint leurs objectifs mensuels pendant 3 mois consécutifs. Cette régularité dans l\'accomplissement de vos objectifs démontre votre engagement constant et votre capacité à planifier efficacement votre activité.',
    icon: <Target className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 33,
    condition: (profil) => profil?.consecutive_goals_achieved >= 3
  },
  {
    id: 'membre-premium',
    title: 'Membre Premium',
    description: 'Est actuellement abonné Premium',
    detailedDescription: 'Un investissement qui paie ! Ce badge distingue les jobbeurs qui ont souscrit à l\'abonnement Premium, démontrant ainsi leur engagement sérieux envers la plateforme. Ce statut vous donne accès à des fonctionnalités exclusives et une visibilité accrue auprès des clients potentiels.',
    icon: <Medal className="h-5 w-5 text-[#FF6B2C]" />,
    backgroundColor: '#FFF8F3',
    iconColor: '#FF6B2C',
    recompense_jobi: 30,
    condition: (profil) => profil?.is_currently_premium === true
  }
];

// Type pour les badges stockés en base de données
interface UserBadge {
  badge_id: string;
  user_id: string;
  recompense_recu: boolean;
  is_lifetime: boolean;
  created_at?: string;
  updated_at?: string;
}

interface BadgesDisplayProps {
  profil: any;
}

export const BadgesDisplay: React.FC<BadgesDisplayProps> = ({ profil }) => {
  const [userBadges, setUserBadges] = useState<Record<string, UserBadge | null>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedBadge, setSelectedBadge] = useState<Badge | null>(null);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showAllBadgesModal, setShowAllBadgesModal] = useState<boolean>(false);
  const [badgeFilter, setBadgeFilter] = useState<'all' | 'earned' | 'remaining' | 'lifetime'>('all');
  const [tooltipPosition, setTooltipPosition] = useState<{ x: number; y: number } | null>(null);
  const [isTouchDevice, setIsTouchDevice] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [expandedBadgeId, setExpandedBadgeId] = useState<string | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const tooltipTimeout = useRef<NodeJS.Timeout | null>(null);

  // Détecter si c'est un appareil tactile et la taille de l'écran
  useEffect(() => {
    const checkTouchDevice = () => {
      setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
    };
    
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkTouchDevice();
    checkScreenSize();
    
    // Ajouter un écouteur pour les changements de taille d'écran
    window.addEventListener('resize', checkScreenSize);
    
    // Nettoyage
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  // Récupérer tous les badges de l'utilisateur en une seule requête
  const fetchUserBadges = async () => {
    try {
      // S'assurer que profil est valide
      if (!profil) {
        logger.error('Profil invalide:', profil);
        setLoading(false);
        return;
      }

      // Utiliser user_id s'il existe, sinon utiliser l'id du profil
      const userId = profil.user_id || profil.id;

      if (!userId) {
        logger.error('Profil sans identifiant valide:', profil);
        setLoading(false);
        return;
      }

      const headers = await getCommonHeaders();
      // On récupère les badges du profil affiché, pas de l'utilisateur connecté
      const response = await axios.get(`${API_CONFIG.baseURL}/api/user-badges/by-profile/${userId}`, {
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        // Transformer la liste en objet pour un accès rapide par badge_id
        const badgesMap: Record<string, UserBadge | null> = {};
        
        // Initialisation avec tous les badges à null (non obtenus)
        badges.forEach(badge => {
          badgesMap[badge.id] = null;
        });
        
        // Mettre à jour avec les badges obtenus
        response.data.data.forEach((badge: UserBadge) => {
          badgesMap[badge.badge_id] = badge;
        });
        
        setUserBadges(badgesMap);

        logger.info('Badges récupérés avec succès : ' + badgesMap);
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération des badges:', error);
    } finally {
      setLoading(false);
    }
  };

  // Gérer les badges obtenus/perdus en batch pour le profil consulté
  const processAllBadges = async () => {
    if (!profil || loading) return;

    // Utiliser user_id s'il existe, sinon utiliser l'id du profil
    const userId = profil.user_id || profil.id;

    if (!userId) {
      logger.error('Profil sans identifiant valide pour le traitement des badges:', profil);
      return;
    }

    logger.info('Vérification des badges pour le profil : ' + profil.id);
    
    try {
      const headers = await getCommonHeaders();
      
      // Vérifier d'abord si une vérification complète est nécessaire
      const verifResponse = await axios.get(
        `${API_CONFIG.baseURL}/api/user-badges/derniere-verif/${userId}`,
        { headers, withCredentials: true }
      );
      
      if (verifResponse.data.success && !verifResponse.data.data.doit_verifier_badge) {
        logger.info('Vérification des badges inutile, dernière vérification récente:', verifResponse.data.data.derniere_verif);
        return; // Sortir sans vérifier si la dernière vérification date de moins de 24h
      }
      
      logger.info('Vérification complète des badges requise. Dernière vérification:', verifResponse.data.data.derniere_verif || 'jamais effectuée');
      
      // 1. Récupérer d'abord les statistiques complètes pour les badges
      const statsResponse = await axios.get(
        `${API_CONFIG.baseURL}/api/user-badges/profile-stats/${userId}`,
        { headers, withCredentials: true }
      );
      
      if (!statsResponse.data.success) {
        logger.error('Erreur lors de la récupération des statistiques de badges:', statsResponse.data);
        return;
      }
      
      // 2. Utiliser ces statistiques pour évaluer les conditions des badges
      const badgeStats = statsResponse.data.data;
      
      // DEBUG: Loguer les statistiques pour comprendre les conditions des badges
      // logger.info('STATISTIQUES BADGES:', JSON.stringify(badgeStats, null, 2));
      
      // DEBUG: Évaluer chaque condition de badge et loguer le résultat
      // logger.info('ÉVALUATION DES CONDITIONS DE BADGES:');
      // badges.forEach(badge => {
        // try {
          // const meetsCriteria = badge.condition(badgeStats);
          // logger.info(`Badge ${badge.id} (${badge.title}): ${meetsCriteria ? 'OBTENU' : 'NON OBTENU'}`);
          
          // Pour les badges non obtenus, vérifier les propriétés requises
          // if (!meetsCriteria) {
            // Extraire le texte de la condition pour analyse
            // const conditionStr = badge.condition.toString();
            // Identifier les propriétés utilisées dans la condition
            // const propsMatch = conditionStr.match(/profil\?\.([\w\.]+)/g) || [];
            // const props = propsMatch.map(p => p.replace('profil?.', ''));
            
            // if (props.length > 0) {
              // Afficher les valeurs actuelles de ces propriétés
              // const propsValues = props.map(prop => {
              //   const path = prop.split('.');
              //   let value = badgeStats;
              //   for (const key of path) {
              //     value = value?.[key];
              //     if (value === undefined) break;
              //   }
              //   return `${prop}: ${value !== undefined ? JSON.stringify(value) : 'undefined'}`;
              // });
              
              // logger.info(`Valeurs pour ${badge.id}: ${propsValues.join(', ')}`);
            // }
          // }
        // } catch (error) {
        //   logger.error(`Erreur lors de l'évaluation du badge ${badge.id}:`, error);
        // }
      // });
      
      // 3. Envoyer les résultats au backend pour mise à jour
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/user-badges/process-for-profile/${userId}`,
        {
          badges: badges.map(badge => ({
            badge_id: badge.id,
            title: badge.title,
            description: badge.description,
            detailedDescription: badge.detailedDescription,
            meets_condition: badge.condition(badgeStats),
            recompense_jobi: badge.recompense_jobi,
            is_lifetime: badge.is_lifetime || false
          }))
        },
        { headers, withCredentials: true }
      );
      
      if (response.data.success) {
        // logger.info('Badges traités avec succès pour le profil', profil.id);
        
        // Mettre à jour l'affichage des badges
        await fetchUserBadges();
        
        // Afficher les notifications pour les nouveaux badges obtenus
        if (response.data.newBadges && response.data.newBadges.length > 0) {
          response.data.newBadges.forEach((badge: any) => {
            notify(
              `Le profil a obtenu le badge "${badge.title}"${badge.is_lifetime ? ' (à vie)' : ''} et gagné ${badge.recompense_jobi} Jobi !`,
              'success'
            );
          });
        }
      }
    } catch (error) {
      logger.error('Erreur lors du traitement des badges pour le profil:', error);
    }
  };

  // Charger les badges au chargement initial
  useEffect(() => {
    if (profil) {
      fetchUserBadges();
    }
  }, [profil?.id]); // Ne recharger que si l'ID du profil change

  // Traiter les badges après chargement
  useEffect(() => {
    if (!loading) {
      processAllBadges();
    }
  }, [loading, profil]);

  // Gérer le survol des badges
  const handleBadgeHover = (event: React.MouseEvent, badge: Badge) => {
    if (isTouchDevice || showModal) return; // Ne pas afficher le tooltip sur appareil tactile ou si le modal est ouvert
    
    const rect = event.currentTarget.getBoundingClientRect();
    // Positionnement du tooltip centré au-dessus du badge
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setSelectedBadge(badge);
    
    // Annuler tout timeout précédent
    if (tooltipTimeout.current) {
      clearTimeout(tooltipTimeout.current);
    }
  };

  const handleBadgeLeave = () => {
    if (isTouchDevice || showModal) return;
    
    // Retarder la disparition du tooltip pour une meilleure UX
    tooltipTimeout.current = setTimeout(() => {
      setTooltipPosition(null);
      setSelectedBadge(null);
    }, 300);
  };

  // Gérer le clic sur un badge (surtout pour mobile)
  const handleBadgeClick = (badge: Badge) => {
    setSelectedBadge(badge);
    setShowModal(true);
  };

  // Empêcher le tooltip de disparaître lorsque la souris entre dedans
  const handleTooltipHover = () => {
    if (tooltipTimeout.current) {
      clearTimeout(tooltipTimeout.current);
    }
  };

  // Fermer le tooltip quand la souris quitte le tooltip
  const handleTooltipLeave = () => {
    if (!isTouchDevice) {
      tooltipTimeout.current = setTimeout(() => {
        setTooltipPosition(null);
        setSelectedBadge(null);
      }, 300);
    }
  };

  // Fermer le modal
  const closeModal = () => {
    setShowModal(false);
    setSelectedBadge(null);
  };

  // Fermer le modal des badges
  const closeAllBadgesModal = () => {
    setShowAllBadgesModal(false);
  };

  // Filtrer les badges selon le mode sélectionné
  const getFilteredBadges = () => {
    let filteredBadges;
    switch (badgeFilter) {
      case 'earned':
        filteredBadges = badges.filter(badge => !!userBadges[badge.id]);
        break;
      case 'remaining':
        filteredBadges = badges.filter(badge => !userBadges[badge.id]);
        break;
      case 'lifetime':
        filteredBadges = badges.filter(badge => badge.is_lifetime === true);
        break;
      case 'all':
      default:
        // Pour "tous", on trie d'abord les badges obtenus puis les badges non obtenus
        const earned = badges.filter(badge => !!userBadges[badge.id]);
        const remaining = badges.filter(badge => !userBadges[badge.id]);
        filteredBadges = [...earned, ...remaining];
        break;
    }
    return filteredBadges;
  };

  // Filtrer les badges obtenus pour l'affichage principal
  const earnedBadges = badges.filter(badge => !!userBadges[badge.id]);

  // Calcul pour la pagination
  const badgesPerPage = isMobile ? 2 : 6;
  const totalPages = Math.ceil(earnedBadges.length / badgesPerPage);
  const paginatedBadges = earnedBadges.slice(
    (currentPage - 1) * badgesPerPage,
    currentPage * badgesPerPage
  );

  // Navigation dans la pagination
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (earnedBadges.length === 0) return null;

  return (
    <div className="mt-6 relative">
      <div className="flex flex-wrap gap-4">
        {paginatedBadges.map((badge) => (
          <div
            key={badge.id}
            className="flex items-center gap-3 bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
            onMouseEnter={(e) => handleBadgeHover(e, badge)}
            onMouseLeave={handleBadgeLeave}
            onClick={() => handleBadgeClick(badge)}
            aria-label={`Badge ${badge.title} - ${badge.description}`}
          >
            <div className="p-2 rounded-lg" style={{ backgroundColor: badge.backgroundColor }}>
              <span className="h-6 w-6 text-[#FF6B2C] flex items-center justify-center">
                {badge.icon}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">{badge.title}</h3>
              <p className="text-sm text-gray-600">{badge.description}</p>
            </div>
          </div>
        ))}
      </div>
      
      {/* Contrôles de pagination - visible uniquement si plus de 4 badges */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-4 gap-2">
          <button 
            onClick={goToPreviousPage} 
            disabled={currentPage === 1}
            className={`p-2 rounded-full ${currentPage === 1 ? 'text-gray-400' : 'text-[#FF6B2C] hover:bg-gray-100'}`}
            aria-label="Page précédente"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </button>
          
          <span className="text-sm text-gray-600">
            Page {currentPage} sur {totalPages}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={currentPage === totalPages}
            className={`p-2 rounded-full ${currentPage === totalPages ? 'text-gray-400' : 'text-[#FF6B2C] hover:bg-gray-100'}`}
            aria-label="Page suivante"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 18l6-6-6-6" />
            </svg>
          </button>
        </div>
      )}
      
      <div 
        className="mt-2 text-sm text-gray-600 flex items-center justify-center gap-1 cursor-pointer hover:underline"
        onClick={() => setShowAllBadgesModal(true)}
      >
        <span>{earnedBadges.length} badge{earnedBadges.length > 1 ? 's' : ''} obtenu{earnedBadges.length > 1 ? 's' : ''} sur {badges.length} disponibles</span>
        <Info size={14} className="text-[#FF6B2C]" />
      </div>

      {/* Tooltip pour desktop */}
      {!isTouchDevice && !showModal && tooltipPosition && selectedBadge && (
        <div 
          ref={tooltipRef}
          className="absolute z-50 bg-white p-4 rounded-lg shadow-lg max-w-md transform -translate-x-1/2"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y - 15}px`,
            pointerEvents: "auto"
          }}
          onMouseEnter={handleTooltipHover}
          onMouseLeave={handleTooltipLeave}
        >
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 rounded-lg" style={{ backgroundColor: selectedBadge.backgroundColor }}>
              <span className="h-6 w-6 text-[#FF6B2C] flex items-center justify-center">
                {React.cloneElement(selectedBadge.icon as React.ReactElement)}
              </span>
            </div>
            <h3 className="font-semibold text-gray-800">
              {selectedBadge.title}
              {selectedBadge.is_lifetime && 
                <span className="ml-2 px-2 py-0.5 bg-[#FFE4BA] text-[#FF6B2C] text-xs rounded-full">
                  à vie
                </span>
              }
            </h3>
          </div>
          <p className="text-gray-600">{selectedBadge.detailedDescription}</p>
          <div className="mt-2 text-sm text-[#FF6B2C] font-semibold">
            Récompense: {selectedBadge.recompense_jobi} Jobi
          </div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-4 h-4 bg-white"></div>
        </div>
      )}

      {/* Modal pour mobile */}
      {showModal && selectedBadge && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          onClick={closeModal}
        >
          <div 
            className="bg-white rounded-xl w-full max-w-md max-h-[80vh] overflow-y-auto relative"
            onClick={(e) => e.stopPropagation()} // Empêche la propagation du clic vers le parent
          >
            <div className="p-6">
              <button 
                onClick={closeModal}
                className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 bg-white rounded-full p-1 shadow-sm z-10"
                aria-label="Fermer"
              >
                <X size={20} />
              </button>
              <div className="flex items-center gap-3 mb-4 pr-8">
                <div className="p-3 rounded-lg" style={{ backgroundColor: selectedBadge.backgroundColor }}>
                  <span className="h-8 w-8 text-[#FF6B2C] flex items-center justify-center">
                    {React.cloneElement(selectedBadge.icon as React.ReactElement)}
                  </span>
                </div>
                <h3 className="font-semibold text-gray-800 text-lg">
                  {selectedBadge.title}
                  {selectedBadge.is_lifetime && 
                    <span className="ml-2 px-2 py-0.5 bg-[#FFE4BA] text-[#FF6B2C] text-xs rounded-full">
                      à vie
                    </span>
                  }
                </h3>
              </div>
              <p className="text-gray-600 mb-2">{selectedBadge.detailedDescription}</p>
              {!selectedBadge.is_lifetime && (
                <div className="mb-4 p-2 bg-gray-50 rounded-lg text-sm text-gray-600 flex items-start gap-2">
                  <Info size={14} className="text-[#FF6B2C] mt-0.5 flex-shrink-0" />
                  <p>Ce badge est dynamique et peut être perdu si vous ne respectez plus les conditions requises. La récompense associée sera également retirée.</p>
                </div>
              )}
              <div className="mt-3 font-semibold text-[#FF6B2C]">
                Récompense obtenue: {selectedBadge.recompense_jobi} Jobi
                {selectedBadge.is_lifetime && 
                  <span className="ml-2 text-sm font-normal text-gray-600">(conservée à vie)</span>
                }
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal pour afficher tous les badges */}
      {showAllBadgesModal && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2 sm:p-4"
          onClick={closeAllBadgesModal}
        >
          <div 
            className="bg-white rounded-xl w-full max-w-2xl h-[95vh] sm:h-[90vh] overflow-y-auto relative"
            onClick={(e) => e.stopPropagation()} // Empêche la propagation du clic vers le parent
          >
            <div className="p-4 sm:p-6">
              <button 
                onClick={closeAllBadgesModal}
                className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 bg-white rounded-full p-1 shadow-sm z-10"
                aria-label="Fermer"
              >
                <X size={20} />
              </button>

              <h2 className="text-xl font-semibold text-gray-800 mb-2 pr-8">Tous les badges JobPartiel</h2>
              
              <div className="bg-gray-50 p-3 rounded-lg mb-4 text-sm text-gray-700">
                <div className="flex items-start gap-2">
                  <Info size={16} className="text-[#FF6B2C] mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="mb-2">
                      <strong>À propos des badges et des récompenses :</strong>
                    </p>
                    <p className="mb-2">
                      La plupart des badges sont <span className="font-medium">dynamiques</span> et peuvent être perdus si vous ne respectez plus les conditions requises. Dans ce cas, les récompenses en Jobi associées seront également retirées de votre compte.
                    </p>
                    <p>
                      Les badges marqués <span className="px-2 py-0.5 bg-[#FFE4BA] text-[#FF6B2C] text-xs rounded-full">à vie</span> sont permanents et leurs récompenses ne peuvent jamais être perdues, même si vous ne remplissez plus les conditions initiales.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2 mb-4">
                <button 
                  className={`px-3 py-1.5 rounded-lg ${badgeFilter === 'all' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setBadgeFilter('all')}
                >
                  Tous ({badges.length})
                </button>
                <button 
                  className={`px-3 py-1.5 rounded-lg ${badgeFilter === 'earned' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setBadgeFilter('earned')}
                >
                  Obtenus ({earnedBadges.length})
                </button>
                <button 
                  className={`px-3 py-1.5 rounded-lg ${badgeFilter === 'remaining' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setBadgeFilter('remaining')}
                >
                  À obtenir ({badges.length - earnedBadges.length})
                </button>
                <button 
                  className={`px-3 py-1.5 rounded-lg ${badgeFilter === 'lifetime' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                  onClick={() => setBadgeFilter('lifetime')}
                >
                  À vie ({badges.filter(badge => badge.is_lifetime === true).length})
                </button>
              </div>

              <div className="pb-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {getFilteredBadges().map((badge) => {
                    const isEarned = !!userBadges[badge.id];
                    const isExpanded = expandedBadgeId === badge.id;
                    return (
                      <div 
                        key={badge.id}
                        className={`flex flex-col p-4 rounded-xl ${isEarned ? 'bg-white shadow-sm' : 'bg-gray-50'} cursor-pointer transition-all duration-300 ${isExpanded ? 'shadow-md' : ''}`}
                        onClick={() => {
                          if (expandedBadgeId === badge.id) {
                            setExpandedBadgeId(null); // Refermer si déjà ouvert
                          } else {
                            setExpandedBadgeId(badge.id); // Ouvrir celui-ci
                          }
                        }}
                      >
                        <div className="flex items-start gap-3">
                          <div 
                            className={`p-2 rounded-lg flex-shrink-0 ${isEarned ? '' : 'opacity-50'}`} 
                            style={{ backgroundColor: badge.backgroundColor }}
                          >
                            <span className="h-6 w-6 text-[#FF6B2C] flex items-center justify-center">
                              {badge.icon}
                            </span>
                            {isEarned && (
                              <div className="mt-1 flex justify-center">
                                <Check size={14} className="text-green-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <h3 className={`font-semibold text-gray-800 ${isEarned ? '' : 'text-gray-500'}`}>
                              {badge.title}
                              {badge.is_lifetime && 
                                <span className="ml-2 px-2 py-0.5 bg-[#FFE4BA] text-[#FF6B2C] text-xs rounded-full">
                                  à vie
                                </span>
                              }
                            </h3>
                            <p className={`text-sm ${isEarned ? 'text-gray-600' : 'text-gray-500'}`}>{badge.description}</p>
                            {isExpanded ? (
                              <>
                                <p className="text-sm leading-relaxed text-gray-600 mt-3">{badge.detailedDescription}</p>
                                {!badge.is_lifetime && (
                                  <p className="text-xs text-gray-500 mt-2 italic">
                                    Ce badge est dynamique et peut être perdu si la condition n'est plus respectée.
                                  </p>
                                )}
                                <div className="mt-3 flex items-center justify-between">
                                  <span className="text-sm text-[#FF6B2C] font-semibold">
                                    {isEarned ? "Récompense obtenue: " : "Récompense: "}
                                    {badge.recompense_jobi} Jobi
                                  </span>
                                  <button 
                                    className="text-gray-500 hover:text-gray-700"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setExpandedBadgeId(null);
                                    }}
                                  >
                                    <X size={16} />
                                  </button>
                                </div>
                              </>
                            ) : (
                              <p className="text-xs text-gray-500 mt-2 line-clamp-2">{badge.detailedDescription}</p>
                            )}
                            {!isExpanded && (
                              <div className="mt-1 text-xs text-[#FF6B2C] font-semibold">
                                {isEarned ? "Récompense obtenue: " : "Récompense: "}
                                {badge.recompense_jobi} Jobi
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};