<?php
session_start();
require_once 'config.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION[SESSION_KEY]) || $_SESSION[SESSION_KEY] !== true) {
    header('Location: login.php');
    exit;
}

// Gestion de la déconnexion
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Liste des documents disponibles
$documents = [
    'JobPartiel_Executive_Summary.html' => 'Executive Summary',
    'JobPartiel_Business_Plan.html' => 'Business Plan',
    'JobPartiel_Plan_Financier.html' => 'Plan Financier',
    'JobPartiel_Equipe_Fondatrice.html' => 'Équipe Fondatrice',
    'JobPartiel_Statuts_Juridiques.html' => 'Statuts Juridiques',
    'Pitch Deck.pdf' => 'Pitch Deck (PDF)',
    'Pitch Deck.pptx' => 'Pitch Deck (PowerPoint)',
    'README.md' => 'Documentation'
];

// Gestion de l'affichage des documents HTML
if (isset($_GET['view'])) {
    $filename = $_GET['view'];
    // Vérifier que le fichier est dans la liste autorisée et qu'il existe
    if (array_key_exists($filename, $documents) && file_exists($filename) && pathinfo($filename, PATHINFO_EXTENSION) === 'html') {
        // Lire et afficher le contenu du fichier HTML
        $content = file_get_contents($filename);
        echo $content;
        exit;
    } else {
        // Fichier non autorisé ou inexistant
        header('HTTP/1.0 404 Not Found');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documents Investisseurs - JobPartiel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            height: 50px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        /* Media queries pour le header responsive */
        @media (max-width: 768px) {
            .header {
                padding: 15px 0;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .logo {
                height: 40px;
            }
            
            .user-info {
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }
            
            .user-info span {
                font-size: 14px;
            }
            
            .logout-btn {
                padding: 10px 20px;
                width: auto;
                min-width: 120px;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 10px 0;
            }
            
            .header-content {
                padding: 0 15px;
            }
            
            .logo {
                height: 35px;
            }
            
            .user-info span {
                font-size: 13px;
            }
            
            .logout-btn {
                padding: 8px 16px;
                font-size: 14px;
            }
        }
        
        .container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .welcome {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .welcome h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .welcome p {
            color: #666;
            line-height: 1.6;
        }
        
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .document-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .document-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .document-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 20px;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .document-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.2s ease;
        }
        
        .document-link:hover {
            transform: translateY(-2px);
        }
        
        .document-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .document-link {
            flex: 1;
        }
        
        .download-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            gap: 5px;
            flex: 1;
        }
        
        .download-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .download-btn i {
            font-size: 14px;
        }
        
        .download-all-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .security-notice {
            background: linear-gradient(135deg, #FEF3C7 0%, #FFF8DC 100%);
            border: 1px solid #F59E0B;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }
        
        .security-notice p {
            margin: 5px 0;
            color: #92400E;
            font-size: 14px;
        }
        
        .security-notice strong {
            color: #78350F;
        }
        
        .download-all-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            margin-top: 10px;
        }
        
        .download-all-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .download-all-btn i {
            font-size: 18px;
        }
        
        /* Media queries pour la grille de documents responsive */
        @media (max-width: 768px) {
            .documents-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }
            
            .document-card {
                padding: 20px;
            }
            
            .document-title {
                font-size: 16px;
            }
            
            .document-link {
                padding: 8px 16px;
                font-size: 14px;
            }
            
            .download-btn {
                padding: 8px 12px;
                font-size: 14px;
            }
            
            .download-all-btn {
                padding: 12px 20px;
                font-size: 15px;
            }
        }
        
        @media (max-width: 480px) {
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .document-card {
                padding: 20px 15px;
            }
            
            .document-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            
            .document-title {
                font-size: 15px;
            }
            
            .document-actions {
                flex-direction: column;
                gap: 8px;
            }
            
            .document-link {
                padding: 10px 15px;
                font-size: 14px;
                width: 100%;
                text-align: center;
            }
            
            .download-btn {
                padding: 10px 15px;
                font-size: 14px;
                width: 100%;
                text-align: center;
            }
            
            .download-all-section {
                padding: 20px 15px;
            }
            
            .download-all-btn {
                padding: 12px 20px;
                font-size: 14px;
                width: 100%;
            }
        }
        
        @media (max-width: 320px) {
            .container {
                padding: 0 15px;
            }
            
            .welcome {
                padding: 20px 15px;
            }
            
            .document-card {
                padding: 15px;
            }
        }
        
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            border-top: 1px solid #eee;
            margin-top: 60px;
        }
        
        /* Modal de confidentialité */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        
        .confidentiality-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .modal-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        
        .modal-icon {
            font-size: 60px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        .modal-text {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .modal-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            color: #856404;
        }
        
        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn-accept {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-accept:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-decline {
            background: #dc3545;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-decline:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .page-content {
            display: none;
        }
        
        @media (max-width: 768px) {
            .modal-content {
                padding: 30px 20px;
                width: 95%;
            }
            
            .modal-title {
                font-size: 20px;
            }
            
            .modal-buttons {
                flex-direction: column;
            }
            
            .btn-accept, .btn-decline {
                width: 100%;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Modal de confidentialité -->
    <div id="confidentialityModal" class="confidentiality-modal">
        <div class="modal-content">
            <div class="modal-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2 class="modal-title">AVERTISSEMENT DE CONFIDENTIALITÉ</h2>
            <div class="modal-text">
                <p><strong>Les documents présents sur cette page sont strictement confidentiels.</strong></p>
                <p>En accédant à ces informations, vous vous engagez à :</p>
                <ul style="text-align: left; margin: 15px 0;">
                    <li>• Ne pas partager, copier ou diffuser ces documents</li>
                    <li>• Ne pas divulguer les informations contenues</li>
                    <li>• Utiliser ces données uniquement dans le cadre de votre évaluation d'investissement</li>
                    <li>• Respecter la propriété intellectuelle de JobPartiel</li>
                </ul>
            </div>
            <div class="modal-warning">
                <strong>⚠️ ATTENTION :</strong> Toute violation de cette confidentialité pourra faire l'objet de poursuites judiciaires.
            </div>
            <div class="modal-buttons">
                <button class="btn-accept" onclick="acceptConfidentiality()">J'accepte et je m'engage</button>
                <button class="btn-decline" onclick="declineConfidentiality()">Je refuse</button>
            </div>
        </div>
    </div>
    
    <div class="page-content">
    <div class="header">
        <div class="header-content">
            <img src="../logo_banniere_job_partiel_grand.webp" alt="JobPartiel" class="logo">
            <div class="user-info">
                <span>Connecté en tant qu'investisseur</span>
                <a href="?logout=1" class="logout-btn">Déconnexion</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome">
            <h1>Bienvenue dans l'espace investisseurs</h1>
            <p>Vous avez accès à tous les documents confidentiels concernant le projet JobPartiel. Ces informations sont strictement réservées aux investisseurs potentiels et partenaires autorisés.</p>
        </div>
        
        <div class="download-all-section">
            <h2>Téléchargement Complet</h2>
            <p>Téléchargez tous les documents en une seule fois</p>
            <div class="security-notice">
                <p><strong>⚠️ Information importante :</strong></p>
                <p>• Le mot de passe pour accéder aux documents est le même que celui de la page de connexion</p>
                <p>• Ces fichiers ne doivent pas être partagés sans autorisation</p>
            </div>
            <a href="download.php?file=JobPartiel_Dossier_Complet.zip" class="download-all-btn">
                <i class="fas fa-download"></i>
                Télécharger le Pack Complet
            </a>
        </div>
        
        <div class="documents-grid">
            <?php foreach ($documents as $filename => $title): ?>
                <?php if (file_exists($filename)): ?>
                    <div class="document-card">
                        <div class="document-icon">
                            <?php 
                            $ext = pathinfo($filename, PATHINFO_EXTENSION);
                            echo match($ext) {
                                'pdf' => '📄',
                                'pptx' => '📊',
                                'html' => '🌐',
                                'md' => '📝',
                                default => '📋'
                            };
                            ?>
                        </div>
                        <div class="document-title"><?= htmlspecialchars($title) ?></div>
                        <div class="document-actions">
                            <?php if (pathinfo($filename, PATHINFO_EXTENSION) === 'html'): ?>
                                <a href="?view=<?= urlencode($filename) ?>" class="document-link" target="_blank">Consulter</a>
                            <?php else: ?>
                                <a href="<?= htmlspecialchars($filename) ?>" class="document-link" target="_blank">Consulter</a>
                            <?php endif; ?>
                            <?php if (in_array($filename, ['Pitch Deck.pdf', 'Pitch Deck.pptx'])): ?>
                                <a href="<?= htmlspecialchars($filename) ?>" download class="download-btn" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Télécharger
                                </a>
                            <?php else: ?>
                                <a href="download.php?file=<?= urlencode($filename) ?>" class="download-btn" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Télécharger
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="footer">
        <p>© 2025 JobPartiel - Documents confidentiels réservés aux investisseurs</p>
    </div>
    </div> <!-- Fin page-content -->
    
    <script>
        function acceptConfidentiality() {
            // Masquer la modal
            document.getElementById('confidentialityModal').style.display = 'none';
            // Afficher le contenu de la page
            document.querySelector('.page-content').style.display = 'block';
            // Enregistrer l'acceptation dans le sessionStorage
            sessionStorage.setItem('confidentialityAccepted', 'true');
        }
        
        function declineConfidentiality() {
             // Déconnecter l'utilisateur en détruisant la session
             alert('Vous devez accepter les conditions de confidentialité pour accéder aux documents. Vous allez être déconnecté.');
             window.location.href = '?logout=1';
         }
        
        // Vérifier si l'utilisateur a déjà accepté dans cette session
        window.addEventListener('DOMContentLoaded', function() {
            if (sessionStorage.getItem('confidentialityAccepted') === 'true') {
                document.getElementById('confidentialityModal').style.display = 'none';
                document.querySelector('.page-content').style.display = 'block';
            }
        });
        
        // Empêcher la fermeture de la modal en cliquant à l'extérieur
        document.getElementById('confidentialityModal').addEventListener('click', function(e) {
            if (e.target === this) {
                e.preventDefault();
                return false;
            }
        });
        
        // Empêcher la fermeture avec la touche Échap
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('confidentialityModal').style.display !== 'none') {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>