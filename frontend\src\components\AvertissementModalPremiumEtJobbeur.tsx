import React from 'react';
import { useNavigate } from 'react-router-dom';
import ModalPortal from './ModalPortal';
import { Box, Typography, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import StarIcon from '@mui/icons-material/Star';
import WorkIcon from '@mui/icons-material/Work';

interface AvertissementModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'premium' | 'jobbeur';
}

const AvertissementModalPremiumEtJobbeur: React.FC<AvertissementModalProps> = ({
  isOpen,
  onClose,
  type
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const modalContent = type === 'premium' ? {
    icon: <StarIcon sx={{ fontSize: 40, color: '#FFD700' }} />,
    title: 'Fonctionnalité Premium',
    description: 'Cette fonctionnalité est réservée aux membres Premium. Profitez d\'avantages exclusifs comme :',
    avantages: [
      'Accès à des statistiques avancées',
      'Outils de gestion professionnels',
      'Support prioritaire',
      'Visibilité accrue sur la plateforme',
      'Mise en avant de votre profil sur la plateforme',
      'Accès anticipé aux nouvelles fonctionnalités',
      'Réductions exclusives chez nos partenaires',
      'Badges de confiance pour renforcer votre crédibilité',
      'Notifications SMS instantanées',
      'Accès à des webinaires et formations exclusives',
    ],
    buttonText: 'Découvrir l\'offre Premium',
    buttonPath: '/dashboard/premium'
  } : {
    icon: <WorkIcon sx={{ fontSize: 40, color: '#FF6B2C' }} />,
    title: 'Espace Jobbeur',
    description: 'Cette section est réservée aux Jobbeurs. Passer en tant que Jobbeur vous permet de :',
    avantages: [
      'Accepter des missions',
      'Gérer votre planning',
      'Développer votre activité',
      'Accéder à des statistiques avancées',
      'Accéder à des outils de gestion professionnels',
      'Support prioritaire',
    ],
    buttonText: 'Passer en tant que Jobbeur gratuitement',
    buttonPath: '/dashboard/parametres'
  };

  const handleButtonClick = () => {
    navigate(modalContent.buttonPath);
    onClose();
  };

  return (
    <ModalPortal>
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1300
        }}
        onClick={onClose}
      >
        <Box
          sx={{
            backgroundColor: '#FFF8F3',
            borderRadius: '16px',
            padding: '24px',
            maxWidth: '500px',
            width: '90%',
            maxHeight: '90vh',
            position: 'relative',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
            cursor: 'default',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#FF6B2C',
              borderRadius: '4px',
              '&:hover': {
                background: '#FF7A35',
              },
            },
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <IconButton
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '#666'
            }}
            onClick={onClose}
          >
            <CloseIcon />
          </IconButton>

          {type === 'premium' && (
            <Box sx={{ mb: 2 }}>
              <Box
                sx={{
                  bgcolor: '#FFF8F3',
                  color: '#FF7A35',
                  border: '1.5px solid #FF7A35',
                  fontWeight: 'bold',
                  fontSize: '1.05rem',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(255, 122, 53, 0.08)',
                  textAlign: 'center',
                  py: 1.2,
                  mb: 2
                }}
              >
                🎉 <b>OFFRE DE LANCEMENT :</b> <span style={{ color: '#E16B28' }}>50% de réduction à vie</span> sur tous les abonnements pour les <b>500 premiers abonnés premium</b> avec le code <span style={{ color: '#E16B28', fontWeight: 700 }}>FIZSZQMW</span>
              </Box>
            </Box>
          )}

          <Box sx={{ textAlign: 'center', mb: 3 }}>
            {modalContent.icon}
            <Typography variant="h5" sx={{ color: '#FF6B2C', mt: 2, fontWeight: 600 }}>
              {modalContent.title}
            </Typography>
          </Box>

          <Typography sx={{ mb: 2, color: '#333' }}>
            {modalContent.description}
          </Typography>

          <Box sx={{ mb: 3 }}>
            {modalContent.avantages.map((avantage, index) => (
              <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Box
                  sx={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    backgroundColor: '#FF6B2C',
                    mr: 2
                  }}
                />
                <Typography sx={{ color: '#555' }}>{avantage}</Typography>
              </Box>
            ))}
          </Box>

          {type === 'jobbeur' && (
            <Typography sx={{ mb: 2, color: '#666', fontSize: '0.9rem', fontStyle: 'italic' }}>
              <Typography component="div">Sur la page des paramètres, cliquez sur le bouton "Passer en tant que Jobbeur" pour commencer.</Typography>
              <Typography component="div">Vous pourrez accéder à toutes les fonctionnalités de la plateforme Jobbeur.</Typography>
              <Typography component="div" sx={{ fontWeight: 'bold' }}>Cette fonctionnalité est totalement gratuite.</Typography>
            </Typography>
          )}

          <button 
            onClick={handleButtonClick}
            className={`w-full py-3 px-4 rounded-lg text-white font-medium transition-colors ${
              type === 'premium' 
                ? 'bg-[#FF6B2C] hover:bg-[#FF7A35]' 
                : 'bg-[#FF6B2C] hover:bg-[#FF7A35]'
            }`}
          >
            {modalContent.buttonText}
          </button>
        </Box>
      </Box>
    </ModalPortal>
  );
};

export default AvertissementModalPremiumEtJobbeur; 