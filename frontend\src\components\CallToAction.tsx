import { Star, Users, Clock } from 'lucide-react';

const CallToAction = () => {
  return (
    <section className="relative py-20 overflow-hidden bg-[#fffbf1]">
      {/* Fond décoratif */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute transform -rotate-12 -left-1/4 -top-1/4 w-1/2 h-1/2 bg-[#FF7A35]/20 rounded-full blur-3xl"></div>
        <div className="absolute transform rotate-12 -right-1/4 -bottom-1/4 w-1/2 h-1/2 bg-[#FF7A35]/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Rejoignez la communauté <span className="text-[#FF7A35]">JobPartiel</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Transformez votre temps libre en opportunités. Rejoignez notre plateforme innovante pour trouver des missions flexibles et rémunératrices adaptées à vos compétences.
          </p>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow text-center">
            <div className="flex items-center justify-center mb-4">
              <Users className="w-12 h-12 text-[#FF7A35]" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Illimité</h3>
            <p className="text-gray-600">Potentiel de croissance</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow text-center">
            <div className="flex items-center justify-center mb-4">
              <Star className="w-12 h-12 text-[#FF7A35]" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Objectif</h3>
            <p className="text-gray-600">Excellence 5/5</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow text-center">
            <div className="flex items-center justify-center mb-4">
              <Clock className="w-12 h-12 text-[#FF7A35]" />
            </div>
            <h3 className="text-2xl font-bold mb-2">24/7</h3>
            <p className="text-gray-600">Support disponible</p>
          </div>
        </div>

      </div>
    </section>
  );
};

export default CallToAction;