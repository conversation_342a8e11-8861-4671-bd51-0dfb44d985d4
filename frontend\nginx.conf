# Configuration du rate limiting
limit_req_zone $binary_remote_addr zone=one:10m rate=100r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=80r/s;

# Configuration de la compression optimisée
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;
gzip_proxied expired no-cache no-store private auth;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/x-javascript
    application/xml
    application/json
    application/rss+xml
    application/atom+xml
    image/svg+xml;
gzip_disable "MSIE [1-6]\.";

# Configuration Brotli (désactivée - module non disponible dans nginx:alpine)
# brotli on;
# brotli_comp_level 6;
# brotli_types
#     text/plain
#     text/css
#     application/json
#     application/javascript
#     text/xml
#     application/xml
#     application/xml+rss
#     text/javascript;

server {
    listen 80;
    server_name www.jobpartiel.fr;

    return 301 https://jobpartiel.fr$request_uri;
}

server {
    listen 3000 default_server;
    server_name _;

    # Substitution de {{CSP_NONCE}} par $request_id
    sub_filter '{{CSP_NONCE}}' '$request_id';
    sub_filter_once on;
    sub_filter_last_modified on;

    # Protection DDoS de base avec timeouts optimisés pour HTTP/2
    client_body_timeout 30s;
    client_header_timeout 30s;
    keepalive_timeout 65s 65s;
    send_timeout 30s;

    # Limiter la taille des requêtes
    client_max_body_size 10M;
    client_body_buffer_size 128k;

    # Désactiver l'affichage de la version de nginx
    server_tokens off;

    # Configuration de base SPA
    location / {
        # En-têtes de sécurité stricts
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # CSP avec sous-domaines spécifiques
        add_header Content-Security-Policy "
            default-src 'self';
            script-src 'self' 'nonce-$request_id' https:;
            style-src 'self' 'unsafe-inline' https:;
            img-src 'self' data: blob: https: *.openstreetmap.org https://api.jobpartiel.fr https://dev-api.jobpartiel.fr;
            font-src 'self' data: https://fonts.gstatic.com;
            connect-src 'self' https://api.jobpartiel.fr https://dev-api.jobpartiel.fr wss://api.jobpartiel.fr wss://dev-api.jobpartiel.fr wss://ws.jobpartiel.fr https://api-adresse.data.gouv.fr https://developers.hostinger.com;
            frame-ancestors 'none';
            base-uri 'self';
            form-action 'self';
            upgrade-insecure-requests;
        " always;

        # Politique d'autorisations
        add_header Permissions-Policy "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=(), interest-cohort=()" always;

        # Politiques inter-origines modifiées pour permettre les ressources externes
        add_header Cross-Origin-Opener-Policy "same-origin" always;
        add_header Cross-Origin-Embedder-Policy "credentialless" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;

        # Limiter l'exposition des informations du serveur
        add_header X-Powered-By "" always;

        # Protection contre certaines attaques
        if ($request_method !~ ^(GET|HEAD|POST)$) {
            return 405;
        }

        # Bloquer les requêtes potentiellement malveillantes
        if ($request_uri ~* "(base64|eval|javascript|<script>)") {
            return 403;
        }

        # Limites de requêtes plus permissives
        limit_req zone=one burst=100 nodelay;
        limit_req_status 429; # Réponse HTTP 429 pour "Too Many Requests".

        # Configuration de base pour le site
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Bloquer les fichiers sensibles
    location ~ \.(bak|swp|old|log|conf|env|git|sql|tar|gz|zip)$ {
        deny all;
        return 404;
    }

    # Configuration pour les uploads
    location /uploads/ {
        allow all;
        valid_referers none blocked jobpartiel.fr *.jobpartiel.fr;
        if ($invalid_referer) {
            return 403;
        }

        # Autoriser uniquement certaines extensions
        location ~* \.(jpg|jpeg|png|gif|svg|webp)$ {
            limit_req zone=one burst=50 nodelay;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        location ~* \.(php|exe|sh|asp|aspx|cgi|pl|py)$ {
            deny all;
            return 404;
        }
    }


    # Ajout des en-têtes COEP
    add_header 'Cross-Origin-Embedder-Policy' 'require-corp' always;
    add_header 'Cross-Origin-Opener-Policy' 'same-origin' always;

    # Types MIME pour les assets
    include /etc/nginx/mime.types;
    types {
        application/javascript mjs;
    }

    # Protection contre les injections dans les en-têtes
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Download-Options "noopen" always;
    add_header X-DNS-Prefetch-Control "off" always;

    # Cache control optimisé pour les assets statiques
    expires 30d;
    add_header Cache-Control "public, no-transform, immutable";

    # Headers de performance
    add_header X-DNS-Prefetch-Control "on" always;
    add_header X-Preload "on" always;

    # Route de healthcheck
    location = /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 'healthy\n';
    }

    # Configuration spécifique pour les images
    location /images/ {
        limit_req zone=one burst=200 nodelay;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000, immutable";

        # Servir les fichiers statiques directement
        root /usr/share/nginx/html;
        try_files $uri =404;
    }

    # Configuration pour les assets
    location /assets/ {
        limit_req zone=one burst=200 nodelay;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # Servir les fichiers statiques directement
        root /usr/share/nginx/html;
        try_files $uri =404;
    }

    # Configuration pour les fichiers publics
    location /public/ {
        limit_req zone=one burst=200 nodelay;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000, immutable";

        # Servir les fichiers statiques directement
        root /usr/share/nginx/html;
        try_files $uri =404;
    }

    # Configuration spécifique pour OpenStreetMap afin de pouvoir utiliser les tiles de la map et ne etre bloqué par le CORS
    location /osm/ {
        proxy_pass https://tile.openstreetmap.org/;
        proxy_set_header Host tile.openstreetmap.org;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        add_header Access-Control-Allow-Origin "*" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Redirection simple pour le sitemap principal
    location = /sitemap.xml {
        return 301 https://api.jobpartiel.fr/sitemap.xml;
    }

    # Redirection pour les autres sitemaps
    location ~ ^/sitemap-.*\.xml$ {
        return 301 https://api.jobpartiel.fr$request_uri;
    }

    # Configuration pour l'API avec gestion d'erreurs améliorée
    location /api/ {
        proxy_pass https://api.jobpartiel.fr/;

        # HTTP/1.1 avec optimisations pour éviter les erreurs de protocole
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Headers de proxy essentiels
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # Préservation des cookies et sessions
        proxy_set_header Cookie $http_cookie;
        proxy_pass_header Set-Cookie;

        # Gestion des erreurs de connexion
        proxy_next_upstream error timeout http_502 http_503 http_504;
        proxy_next_upstream_tries 2;

        # Timeouts optimisés pour éviter les erreurs de protocole
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # Buffer settings optimisés
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # Gestion préflight OPTIONS (sans duplication CORS)
        if ($request_method = 'OPTIONS') {
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Réponse JSON personnalisée pour l'erreur 503
    location @503_json {
        default_type application/json;
        return 503 '{"error": "Service temporairement indisponible", "status": 503}';
    }

    # Configuration spécifique pour les fichiers JS
    location ~* \.(js|mjs)$ {
        root /usr/share/nginx/html;
        expires max;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Content-Type "application/javascript" always;
        try_files $uri =404;
    }
}
