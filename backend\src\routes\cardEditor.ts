import { Router, Request, Response, NextFunction } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { validateFileUpload } from '../middleware/fileValidation';
import {
  getTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  generateRandomTemplate,
  generateMultipleTemplates,
  exportTemplate,
  exportTemplateImage,
  uploadImage,
  deleteImage,
  deleteAllImages,
  reorderTemplates,
  getGenerationStatus
} from '../controllers/cardEditorController';
import { asyncHandler } from '../utils/inputValidation';
import * as aiConsentMiddleware from '../middleware/aiConsentMiddleware';

const router = Router();

// Limiteur de requêtes pour les opérations de base
const cardEditorLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limite chaque IP à 100 requêtes par fenêtre
  standardHeaders: true,
  legacyHeaders: false,
  message: { success: false, message: 'Trop de requêtes, veuillez réessayer plus tard.' }
});

// Limiteur spécifique pour la génération aléatoire (IA)
const randomTemplateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 15, // 15 requêtes maximum par minute
  standardHeaders: true,
  legacyHeaders: false,
  message: { success: false, message: 'Trop de requêtes de génération, veuillez réessayer plus tard.' }
});

// Limiteur pour l'export
const exportLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // 20 requêtes par fenêtre
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: 'Trop de requêtes d\'export, veuillez réessayer plus tard'
  }
});

// Limiteur pour l'upload d'images
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30, // 30 requêtes par fenêtre
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    message: 'Trop de requêtes d\'upload, veuillez réessayer plus tard'
  }
});

// Toutes les routes nécessitent une authentification
router.use(authMiddleware.authenticateToken);

// Ajout d'une route pour vérifier l'état d'avancement de la génération (doit être avant toute route dynamique !)
router.get('/generation-status', asyncHandler(getGenerationStatus));

// Routes pour les templates
router.get('/', cardEditorLimiter, asyncHandler(getTemplates));
router.get('/:id', cardEditorLimiter, asyncHandler(getTemplateById));
router.post('/', cardEditorLimiter, asyncHandler(createTemplate));
router.put('/:id', cardEditorLimiter, asyncHandler(updateTemplate));
router.delete('/:id', cardEditorLimiter, asyncHandler(deleteTemplate));

// Route pour la génération aléatoire de template via l'IA
router.post('/random', randomTemplateLimiter, aiConsentMiddleware.checkAiConsent, asyncHandler(generateRandomTemplate));

// Route pour la génération de plusieurs templates via l'IA
router.post('/generate-multiple', randomTemplateLimiter, aiConsentMiddleware.checkAiConsent, asyncHandler(generateMultipleTemplates));

// Route pour l'upload d'images (express-fileupload)
router.post('/:id/upload', uploadLimiter, validateFileUpload, asyncHandler(uploadImage));

// Route pour l'export PDF
router.post('/export/:id', exportLimiter, asyncHandler(exportTemplate));

// Route pour l'export d'images (PNG/JPG)
router.post('/export-image/:id', exportLimiter, asyncHandler(exportTemplateImage));

// Route pour supprimer une image individuelle d'un template (card editor)
router.delete('/:id/image', cardEditorLimiter, asyncHandler(deleteImage));

// Route pour supprimer toutes les images d'un template (card editor)
router.delete('/:id/images', cardEditorLimiter, asyncHandler(deleteAllImages));

// Route pour réorganiser les templates
router.post('/reorder', cardEditorLimiter, asyncHandler(reorderTemplates));

export default router;
