import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { Request } from 'express';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';

/**
 * Enregistre une action utilisateur dans la table user_actions_history
 * et supprime les entrées les plus anciennes si la limite de l'abonnement est dépassée
 * @param userId - ID de l'utilisateur qui effectue l'action
 * @param actionType - Type d'action (voir formatActionType dans UserController pour les types disponibles)
 * @param resourceId - ID de la ressource concernée (optionnel)
 * @param resourceType - Type de ressource (optionnel)
 * @param details - Détails additionnels (optionnel)
 * @param ipAddress - Adresse IP de l'utilisateur (optionnel)
 * @returns
 */
export const logUserActivity = async (
  userId: string,
  actionType: string,
  resourceId?: string,
  resourceType?: string,
  details?: any,
  ipAddress?: string | undefined
): Promise<void> => {
  try {
    if (!userId || !actionType) {
      logger.warn('Tentative de logger une activité sans userId ou actionType', {
        actionType, resourceType
      });
      return;
    }

    // Traduire les types d'action en anglais vers le français
    const actionMapping: Record<string, string> = {
      'user_register': 'inscription_utilisateur',
      'user_login': 'connexion_utilisateur',
      'user_login_2fa': 'connexion_utilisateur_2fa',
      'user_logout': 'deconnexion_utilisateur',
      'email_verified': 'email_verifie',
      'email_verification_resend': 'renvoi_verification_email',
      'password_reset_request': 'demande_reinitialisation_mdp',
      'password_reset': 'reinitialisation_mdp',
      'profile_update': 'mise_a_jour_profil',
      'profile_photo_update': 'mise_a_jour_photo_profil',
      'two_factor_enabled': 'activation_2fa',
      'two_factor_disabled': 'desactivation_2fa',
      'two_factor_verified': 'verification_2fa',
      'bug_report_create': 'creation_rapport_bug',
      // Ajout des types d'actions pour les crédits IA
      'ai_credits_purchase_jobi': 'achat_credits_ia_jobi',
      'ai_credits_purchase_stripe': 'achat_credits_ia_stripe',
      'ai_credits_used': 'utilisation_credit_ia',
      'ai_credits_subscription_gift': 'credits_ia_offerts_abonnement',
      'ai_credits_admin_gift': 'credits_ia_offerts_admin',
      'ai_credits_other': 'operation_credits_ia',
      'ai_prompt_deleted': 'ai_prompt_deleted',
      'ai_prompt_saved': 'ai_prompt_saved',
      'bug_report_update': 'mise_a_jour_rapport_bug',
      'bug_report_delete': 'suppression_rapport_bug',
      'bug_report_comment_add': 'ajout_commentaire_bug',
      'bug_report_comment_delete': 'suppression_commentaire_bug',
      'bug_report_comment_update': 'modification_commentaire_bug',
      'conversation_create': 'creation_conversation',
      'mission_create': 'creation_mission',
      'mission_update': 'mise_a_jour_mission',
      'mission_delete': 'suppression_mission',
      'mission_accept': 'mission_accept',
      'mission_refuse': 'mission_refuse',
      'mission_complete': 'mission_complete',
      'mission_cancel': 'mission_cancel',
      'mission_review': 'mission_review',
      'support_ticket_create': 'support_ticket_create',
      'support_ticket_update': 'support_ticket_update',
      'support_ticket_reply': 'support_ticket_reply',
      'gallery_create': 'gallery_create',
      'gallery_update': 'gallery_update',
      'gallery_delete': 'gallery_delete',
      'gallery_photo_add': 'gallery_photo_add',
      'gallery_photo_delete': 'gallery_photo_delete',
      'gallery_photo_update': 'gallery_photo_update',
      'password_change': 'password_change',
      'email_change': 'email_change',
      'jobi_transfer': 'jobi_transfer',
      'jobi_receive': 'jobi_receive',
      'invoice_create': 'invoice_create',
      'quote_create': 'quote_create',
      'credit_note_create': 'credit_note_create',
      'invoice_update': 'invoice_update',
      'quote_update': 'quote_update',
      'credit_note_update': 'credit_note_update',
      'invoice_status_change': 'invoice_status_change',
      'quote_status_change': 'quote_status_change',
      'credit_note_status_change': 'credit_note_status_change',
      'invoice_delete': 'invoice_delete',
      'quote_delete': 'quote_delete',
      'credit_note_delete': 'credit_note_delete',
      'invoice_send': 'invoice_send',
      'quote_send': 'quote_send',
      'credit_note_send': 'credit_note_send',
      'upgrade_premium': 'upgrade_premium',
      'downgrade_gratuit': 'downgrade_gratuit',
      'renewal_abonnement': 'renewal_abonnement',
      'cancel_auto_renew': 'cancel_auto_renew',
      'reactivate_auto_renew': 'reactivate_auto_renew',
      'proposal_accept': 'proposal_accept',
      'proposal_reject': 'proposal_reject',
      'proposal_create': 'proposal_create',
      'proposal_update': 'proposal_update',
      'proposal_delete': 'proposal_delete',
      'mission_proposal_create': 'mission_proposal_create',
      'review_response': 'review_response',
      'mission_comment': 'mission_comment',
      'creation_rapport_bug': 'creation_rapport_bug',
      'image_confirmed': 'image_confirmed',
      'ban_user': 'ban_user',
      'unban_user': 'unban_user',
      'admin_photo_moderation': 'admin_photo_moderation'
    };

    const translatedAction = actionMapping[actionType] || actionType;

    // Masquer les informations sensibles dans les détails
    let sanitizedDetails = { ...details };

    if (sanitizedDetails) {
      // Masquer les adresses IP dans les détails
      if (sanitizedDetails.ip_address) {
        sanitizedDetails.ip_address = maskIpAddress(sanitizedDetails.ip_address);
      }

      // Parcourir tous les champs des détails pour masquer les IDs utilisateurs et emails
      for (const key in sanitizedDetails) {
        // Masquer tous les champs contenant "user_id", "userId" ou se terminant par "_id"
        if (
          key.includes('user_id') ||
          key.includes('userId') ||
          key.includes('target_user') ||
          (key.endsWith('_id') && typeof sanitizedDetails[key] === 'string' &&
           sanitizedDetails[key].length > 30) // Heuristique pour identifier les UUID
        ) {
          sanitizedDetails[key] = 'ID_UTILISATEUR_MASQUÉ';
        }

        // Masquer spécifiquement les IDs courants
        if (key === 'userId') sanitizedDetails[key] = 'ID_UTILISATEUR';
        if (key === 'user_id') sanitizedDetails[key] = 'ID_UTILISATEUR';
        if (key === 'expediteur_id') sanitizedDetails[key] = 'ID_EXPEDITEUR';
        if (key === 'destinataire_id') sanitizedDetails[key] = 'ID_DESTINATAIRE';
        if (key === 'target_user_id') sanitizedDetails[key] = 'ID_UTILISATEUR_CIBLE';

        // Masquer tous les champs contenant "email"
        if (key.includes('email') && typeof sanitizedDetails[key] === 'string') {
          sanitizedDetails[key] = maskEmail(sanitizedDetails[key]);
        }
      }

      // Masquer les user agents
      if (sanitizedDetails.user_agent) {
        sanitizedDetails.user_agent = sanitizedDetails.user_agent.split(' ')[0]; // Garde seulement le premier segment
      }

      // Transformer le JSON en description textuelle
      const textDescription = formatActivityDetails(translatedAction, sanitizedDetails);
      if (textDescription) {
        sanitizedDetails = { message: textDescription };
      }
    }

    // Ajouter la nouvelle entrée dans l'historique
    const { error } = await supabase
      .from('user_actions_history')
      .insert({
        user_id: userId,
        action_type: translatedAction,
        resource_id: resourceId || null,
        resource_type: resourceType || null,
        details: sanitizedDetails || null,
        ip_address: ipAddress, // Stocker l'adresse IP complète en BDD
        action_date: new Date().toISOString(),
      });

    if (error) {
      logger.error('Erreur lors de l\'enregistrement d\'une activité utilisateur', {
        error,
        actionType: translatedAction
      });
      return;
    }

    // Après l'ajout de l'entrée, vérifier si nous dépassons la limite d'abonnement
    // et supprimer les entrées les plus anciennes si nécessaire
    await cleanupOldHistoryEntries(userId);

  } catch (error) {
    logger.error('Exception lors de l\'enregistrement d\'une activité utilisateur', {
      error,
      actionType
    });
  }
};

/**
 * Transforme les détails JSON en description textuelle en français
 * @param actionType - Type d'action
 * @param details - Détails de l'action
 * @returns Description textuelle
 */
const formatActivityDetails = (actionType: string, details: any): string | null => {
  if (!details) return null;

  try {
    // Transformation des types d'action similaires pour réutiliser la logique
    let normalizedActionType = actionType;

    // Table de mapping pour normaliser les types d'actions similaires
    const actionTypeMapping: Record<string, string> = {
      // Reviews
      'review_update': 'mission_review',
      'review_create': 'mission_review',
      'review_delete': 'mission_review',
      'evaluation_mission': 'mission_review',

      // Authentification
      'connexion_utilisateur': 'user_login',
      'connexion_utilisateur_2fa': 'user_login_2fa',
      'deconnexion_utilisateur': 'user_logout',
      'inscription_utilisateur': 'user_register',

      // Authentification à deux facteurs
      'activation_2fa': 'two_factor_enabled',
      'desactivation_2fa': 'two_factor_disabled',
      'verification_2fa': 'two_factor_verified',

      // Email
      'email_verifie': 'email_verified',
      'renvoi_verification_email': 'email_verification_resend',

      // Crédits IA
      'achat_credits_ia_jobi': 'ai_credits_purchase',
      'achat_credits_ia_stripe': 'ai_credits_purchase',
      'utilisation_credit_ia': 'ai_credits_used',
      'credits_ia_offerts_abonnement': 'ai_credits_gift',
      'credits_ia_offerts_admin': 'ai_credits_gift',
      'operation_credits_ia': 'ai_credits_operation',

      // Mot de passe
      'demande_reinitialisation_mdp': 'password_reset_request',
      'reinitialisation_mdp': 'password_reset',
      'changement_mot_de_passe': 'password_change',

      // Profil
      'mise_a_jour_profil': 'profile_update',
      'mise_a_jour_photo_profil': 'profile_photo_update',
      'changement_email': 'email_change',

      // Missions
      'creation_mission': 'mission_create',
      'mise_a_jour_mission': 'mission_update',
      'suppression_mission': 'mission_delete',
      'acceptation_mission': 'mission_accept',
      'refus_mission': 'mission_refuse',
      'completion_mission': 'mission_complete',
      'annulation_mission': 'mission_cancel',

      // Galerie
      'creation_galerie': 'gallery_create',
      'mise_a_jour_galerie': 'gallery_update',
      'suppression_galerie': 'gallery_delete',
      'ajout_photo_galerie': 'gallery_photo_add',
      'suppression_photo_galerie': 'gallery_photo_delete',

      // Support
      'creation_ticket_support': 'support_ticket_create',
      'mise_a_jour_ticket_support': 'support_ticket_update',
      'reponse_ticket_support': 'support_ticket_reply',

      // Bug reports
      'creation_rapport_bug': 'creation_rapport_bug',
      'mise_a_jour_rapport_bug': 'bug_report_update',
      'suppression_rapport_bug': 'bug_report_delete',
      'ajout_commentaire_bug': 'bug_report_comment_add',
      'suppression_commentaire_bug': 'bug_report_comment_delete',
      'modification_commentaire_bug': 'bug_report_comment_update',

      // Conversation
      'creation_conversation': 'conversation_create',

      // Jobi
      'transfert_jobi': 'jobi_transfer',
      'reception_jobi': 'jobi_receive',

      // Factures et devis
      'creation_facture': 'invoice_create',
      'modification_facture': 'invoice_update',
      'modification_statut_facture': 'invoice_status_change',
      'suppression_facture': 'invoice_delete',
      'envoi_facture': 'invoice_send',

      'creation_devis': 'quote_create',
      'modification_devis': 'quote_update',
      'modification_statut_devis': 'quote_status_change',
      'suppression_devis': 'quote_delete',
      'envoi_devis': 'quote_send',

      'creation_avoir': 'credit_note_create',
      'modification_avoir': 'credit_note_update',
      'modification_statut_avoir': 'credit_note_status_change',
      'suppression_avoir': 'credit_note_delete',
      'envoi_avoir': 'credit_note_send',

      'review_response_update': 'review_response_update',
      'review_response_create': 'review_response_create',
      'review_response_delete': 'review_response_delete',
      'review_response_read': 'review_response_read',

      'passage_premium': 'upgrade_premium',
      'passage_gratuit': 'downgrade_gratuit',
      'renouvellement_abonnement': 'renewal_abonnement',
      'desactivation_renouvellement_auto': 'cancel_auto_renew',
      'reactivation_renouvellement_auto': 'reactivate_auto_renew',
      'acceptation_proposition': 'proposal_accept',
      'refus_proposition': 'proposal_reject',
      'creation_proposition': 'proposal_create',
      'mise_a_jour_proposition': 'proposal_update',
      'suppression_proposition': 'proposal_delete',
      'mission_proposal_create': 'mission_proposal_create',
      'review_response': 'review_response',
      'mission_comment': 'mission_comment',
      'support_ticket_create': 'support_ticket_create',
      'ban_user': 'ban_user',
      'unban_user': 'unban_user',
      'image_confirmed': 'image_confirmed',
      'admin_photo_moderation': 'admin_photo_moderation'
    };

    // Normaliser le type d'action si une correspondance existe
    normalizedActionType = actionTypeMapping[actionType] || actionType;

    // Variable pour stocker le type d'action original
    const originalActionType = actionType;

    switch (normalizedActionType) {
      case 'user_register':
        return `Création du compte utilisateur${details.referred_by ? ' via une invitation' : ''}`;

      case 'user_login':
        return `Connexion réussie${details.device ? ` depuis ${details.device}` : ''}`;

      case 'user_login_2fa':
        return `Connexion sécurisée avec authentification à deux facteurs${details.device ? ` depuis ${details.device}` : ''}`;

      case 'user_logout':
        return 'Déconnexion du compte';

      case 'email_verified':
        return 'Adresse email vérifiée avec succès';

      case 'email_verification_resend':
        return 'Demande de renvoi du mail de vérification';

      case 'two_factor_enabled':
        return 'Authentification à deux facteurs activée pour votre compte';

      case 'two_factor_disabled':
        return 'Authentification à deux facteurs désactivée pour votre compte';

      case 'two_factor_verified':
        return 'Authentification à deux facteurs vérifiée avec succès';

      case 'password_reset_request':
        return 'Demande de réinitialisation du mot de passe';

      case 'password_reset':
        return 'Mot de passe réinitialisé avec succès';

      case 'profile_update': {
        const updatedFields = details.updated_fields ? Object.keys(details.updated_fields).join(', ') : '';
        return `Mise à jour du profil${updatedFields ? ` (champs modifiés : ${updatedFields})` : ''}`;
      }

      case 'gallery_photos_add_multiple':
        return `Ajout de photos à la galerie`;

      case 'profile_banner_update':
        return `Mise à jour du bannière de profil`;

      case 'generate_random_card_template':
        return `Génération d'un modèle de carte aléatoire`;

      case 'auto_deactivate_card_template':
        return `Désactivation automatique du modèle de carte`;

      case 'update_card_template':
        return `Mise à jour du modèle de carte`;

      case 'demande_suppression_compte':
        return 'Demande de suppression de compte';

      case 'admin_photo_moderation':
        return `Modération de la photo de profil`;

      case 'admin_view_user_history':
        return `Affichage de l'historique de l'utilisateur ${details.target_user_email}`;

      case 'admin_photo_moderation':
        return `Modération de la photo de profil`;

      case 'profile_photo_update':
        return 'Photo de profil mise à jour';

      case 'bug_report_create':
        return `Rapport de bug créé${details.title ? ` : ${details.title}` : ''}`;

      case 'bug_report_update':
        return `Rapport de bug mis à jour${details.title ? ` : ${details.title}` : ''}`;

      case 'bug_report_delete':
        return `Rapport de bug supprimé${details.title ? ` : ${details.title}` : ''}`;

      case 'bug_report_comment_add':
        return 'Commentaire ajouté à un rapport de bug';

      case 'bug_report_comment_delete':
        return 'Commentaire supprimé d\'un rapport de bug';

      case 'bug_report_comment_update':
        return 'Commentaire modifié sur un rapport de bug';

      case 'conversation_create': {
        const recipient = details.recipient_name || details.recipient_username || details.with_user;
        return `Nouvelle conversation créée${recipient ? ` avec ${recipient}` : ''}`;
      }

      case 'mission_create':
        return `Mission créée${details.title ? ` : ${details.title}` : ''}${details.price ? ` (${details.price} Jobi)` : ''}`;

      case 'mission_update':
        return `Mission mise à jour${details.title ? ` : ${details.title}` : ''}`;

      case 'mission_accept':
        return `Mission acceptée${details.title ? ` : ${details.title}` : ''}`;

      case 'mission_refuse':
        return `Mission refusée${details.title ? ` : ${details.title}` : ''}`;

      case 'mission_complete':
        return `Mission marquée comme terminée${details.title ? ` : ${details.title}` : ''}`;

      case 'mission_cancel':
        return `Mission annulée${details.title ? ` : ${details.title}` : ''}`;

      case 'mission_review': {
        const oldNote = details.old_note;
        const newNote = details.new_note || details.note;
        const hasComment = details.has_comment;
        const defautsCount = details.defauts_count;
        const qualitesCount = details.qualites_count;
        const missionTitle = details.mission_title || details.title || '';

        // Adapter le début de la description en fonction du type d'action original
        let description = '';

        if (originalActionType === 'review_update') {
          description = `Mise à jour d'une évaluation`;
        } else if (originalActionType === 'review_create') {
          description = `Nouvelle évaluation`;
        } else if (originalActionType === 'review_delete') {
          description = `Suppression d'une évaluation`;
        } else {
          description = `Évaluation d'une mission`;
        }

        if (missionTitle) {
          description += ` pour la mission : ${missionTitle}`;
        }

        // Ajouter détails sur les notes
        if (newNote && oldNote) {
          // Cas de modification d'une note existante
          if (newNote > oldNote) {
            description += ` - Note améliorée de ${oldNote} à ${newNote}/5`;
          } else if (newNote < oldNote) {
            description += ` - Note réduite de ${oldNote} à ${newNote}/5`;
          } else {
            description += ` - Note maintenue à ${newNote}/5`;
          }
        } else if (newNote) {
          // Nouvelle note
          description += ` avec une note de ${newNote}/5`;
        }

        // Ajouter détails sur les qualités et défauts
        let aspectsMentionnes = [];
        if (qualitesCount > 0) {
          aspectsMentionnes.push(`${qualitesCount} ${qualitesCount === 1 ? 'qualité mentionnée' : 'qualités mentionnées'}`);
        }
        if (defautsCount > 0) {
          aspectsMentionnes.push(`${defautsCount} ${defautsCount === 1 ? 'défaut mentionné' : 'défauts mentionnés'}`);
        }

        if (aspectsMentionnes.length > 0) {
          description += ` avec ${aspectsMentionnes.join(' et ')}`;
        }

        // Ajouter information sur le commentaire
        if (hasComment) {
          description += ` et un commentaire détaillé`;
        }

        return description;
      }

      case 'gallery_create': {
        const title = details.title || details.gallery_name;
        return `Nouvelle galerie créée${title ? ` : ${title}` : ''}`;
      }

      case 'gallery_update': {
        const title = details.title || details.gallery_name;
        return `Galerie mise à jour${title ? ` : ${title}` : ''}`;
      }

      case 'gallery_delete': {
        const title = details.title || details.gallery_name;
        return `Galerie supprimée${title ? ` : ${title}` : ''}`;
      }

      case 'gallery_photo_add':
        return `Photo ajoutée à la galerie${details.gallery_name ? ` "${details.gallery_name}"` : ''}`;
      case 'gallery_photo_delete':
        return `Photo supprimée de la galerie${details.gallery_name ? ` "${details.gallery_name}"` : ''}`;
      case 'gallery_photo_update':
        return `Photo de galerie modifiée${details.gallery_name ? ` "${details.gallery_name}"` : ''}`;

      case 'password_change':
        return 'Mot de passe modifié';

      case 'email_change': {
        const newEmail = details.new_email ? maskEmail(details.new_email) : '';
        return `Adresse email modifiée${newEmail ? ` vers ${newEmail}` : ''}`;
      }

      case 'jobi_transfer': {
        const montant = details.montant || '?';
        const destinataire = details.destinataire_username || details.destinataire_name || 'un autre utilisateur';
        const motif = details.motif || details.description || '';
        return `Transfert de ${montant} Jobi à ${destinataire}${motif ? ` - Motif : ${motif}` : ''}`;
      }

      case 'jobi_receive': {
        const montant = details.montant || '?';
        const expediteur = details.expediteur_username || details.expediteur_name || 'd\'un autre utilisateur';
        const motif = details.motif || details.description || '';
        return `Réception de ${montant} Jobi de ${expediteur}${motif ? ` - Motif : ${motif}` : ''}`;
      }

      case 'support_ticket_create': {
        const sujet = details.title || details.subject || 'Sans titre';
        const categorie = details.category || '';
        return `Création d'un ticket de support : ${sujet}${categorie ? ` (Catégorie : ${categorie})` : ''}`;
      }

      case 'support_ticket_update': {
        const sujet = details.title || details.subject || '';
        return `Mise à jour d'un ticket de support${sujet ? ` : ${sujet}` : ''}`;
      }

      case 'support_ticket_reply': {
        const isAdmin = details.is_admin || details.isAdmin;
        const ticketId = details.ticket_id ? `#${details.ticket_id}` : '';
        return `Réponse ajoutée au ticket${ticketId}${isAdmin ? ' par un administrateur' : ' par l\'utilisateur'}`;
      }

      case 'invoice_create':
        return `Création d'une facture${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'quote_create':
        return `Création d'un devis${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'credit_note_create':
        return `Création d'un avoir${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'invoice_update':
        return `Modification d'une facture${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'quote_update':
        return `Modification d'un devis${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'credit_note_update':
        return `Modification d'un avoir${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'invoice_status_change':
        return `Changement de statut d'une facture${details.number ? ` n°${details.number}` : ''} de "${details.old_status || 'inconnu'}" à "${details.new_status || 'inconnu'}"`;

      case 'quote_status_change':
        return `Changement de statut d'un devis${details.number ? ` n°${details.number}` : ''} de "${details.old_status || 'inconnu'}" à "${details.new_status || 'inconnu'}"`;

      case 'credit_note_status_change':
        return `Changement de statut d'un avoir${details.number ? ` n°${details.number}` : ''} de "${details.old_status || 'inconnu'}" à "${details.new_status || 'inconnu'}"`;

      case 'invoice_delete':
        return `Suppression d'une facture${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'quote_delete':
        return `Suppression d'un devis${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'credit_note_delete':
        return `Suppression d'un avoir${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}`;

      case 'invoice_send':
        return `Envoi d'une facture${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}${details.email ? ` à ${details.email}` : ''}`;

      case 'quote_send':
        return `Envoi d'un devis${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}${details.email ? ` à ${details.email}` : ''}`;

      case 'credit_note_send':
        return `Envoi d'un avoir${details.number ? ` n°${details.number}` : ''}${details.client ? ` pour ${details.client}` : ''}${details.email ? ` à ${details.email}` : ''}`;

      case 'review_response_update':
        return `Mise à jour d'une réponse à un avis`;
      case 'review_response_create':
        return `Création d'une réponse à un avis`;
      case 'review_response_delete':
        return `Suppression d'une réponse à un avis`;
      case 'review_response_read':
        return `Lecture d'une réponse à un avis`;

      case 'upgrade_premium':
        return `Passage à l'abonnement Premium`;
      case 'downgrade_gratuit':
        return `Retour à l'abonnement Gratuit`;
      case 'renewal_abonnement':
        return `Renouvellement de l'abonnement`;
      case 'cancel_auto_renew':
        return `Désactivation du renouvellement automatique de l'abonnement`;
      case 'reactivate_auto_renew':
        return `Réactivation du renouvellement automatique de l'abonnement`;
      case 'proposal_accept':
        return `Proposition acceptée${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'proposal_reject':
        return `Proposition refusée${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'proposal_create':
        return `Nouvelle proposition de mission${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'proposal_update':
        return `Modification d'une proposition${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'proposal_delete':
        return `Suppression d'une proposition${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'mission_proposal_create':
        return `Nouvelle proposition de mission${details.montant ? ` (Montant : ${details.montant} Jobi)` : ''}`;
      case 'review_response':
        return `Réponse à un avis${details.message ? ` : ${details.message}` : ''}`;
      case 'mission_comment':
        const missionTitle = details.mission_title || '';
        const comment = details.comment || '';
        return `Commentaire ajouté sur la mission${missionTitle ? ` "${missionTitle}"` : ''}${comment ? ` : "${comment}"` : ''}`;

      // Cas pour les crédits IA
      case 'ai_credits_purchase':
        const montant = details.montant || 0;
        const soldeApres = details.solde_apres || 0;
        const moyenPaiement = originalActionType.includes('jobi') ? 'Jobi' : 'Stripe';
        return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA via ${moyenPaiement} (nouveau solde: ${soldeApres})`;

      case 'ai_credits_used':
        return `Utilisation d'un crédit IA${details.description ? ` : ${details.description}` : ''}`;

      case 'ai_credits_gift':
        const creditsOfferts = details.montant || 0;
        const source = originalActionType.includes('admin') ? 'l\'administrateur' : 'l\'abonnement';
        return `${creditsOfferts} crédit${creditsOfferts > 1 ? 's' : ''} IA offert${creditsOfferts > 1 ? 's' : ''} par ${source}`;

      case 'ai_credits_operation':
        return `Opération sur les crédits IA${details.description ? ` : ${details.description}` : ''}`;

      case 'creation_rapport_bug': {
        const title = details.title || details.bug_title || 'Sans titre';
        const type = details.type || details.bug_type || '';
        const severity = details.severity || details.importance || '';
        let description = `Création d'un rapport de bug : ${title}`;
        if (type) description += ` (Type : ${type})`;
        if (severity) description += ` - Importance : ${severity}`;
        return description;
      }

      case 'ban_user': {
        const reason = details.reason || '';
        const suspendedUntil = details["Débannissement prévu le"] || '';
        let description = 'Compte suspendu';
        if (reason) description += ` pour la raison suivante : ${reason}`;
        if (suspendedUntil) description += `. Réactivation prévue le ${new Date(suspendedUntil).toLocaleDateString('fr-FR')}`;
        return description;
      }

      case 'unban_user':
        return 'Compte réactivé';

      case 'ai_consent':
        if (details) {
          const detailsObj = typeof details === 'string' ? JSON.parse(details) : details;
          if (detailsObj && (detailsObj.firstName || detailsObj.lastName)) {
            return `Consentement IA donné : ${detailsObj.firstName || ''} ${detailsObj.lastName || ''}`.trim();
          }
        }
        return null;

      case 'ai_prompt_deleted':
        return `Prompt IA supprimé${details.title ? ` : ${details.title}` : ''}`;
      case 'ai_prompt_saved':
        return `Prompt IA sauvegardé${details.title ? ` : ${details.title}` : ''}`;

      case 'image_confirmed':
        return 'Image générée par IA validée';

      default:
        // Essayer de déduire le type d'action par son nom pour fournir un message plus descriptif
        if (actionType.includes('create') || actionType.includes('creation') || actionType.includes('ajout')) {
          const itemType = formatActionTypeName(actionType.replace('create', '').replace('creation', '').replace('ajout', ''));
          return `Création${itemType ? ` d'un élément de type ${itemType}` : ''}`;
        } else if (actionType.includes('update') || actionType.includes('mise_a_jour') || actionType.includes('modification')) {
          const itemType = formatActionTypeName(actionType.replace('update', '').replace('mise_a_jour', '').replace('modification', ''));
          return `Mise à jour${itemType ? ` d'un élément de type ${itemType}` : ''}`;
        } else if (actionType.includes('delete') || actionType.includes('suppression') || actionType.includes('remove')) {
          // Cas spécial pour les favoris
          if (actionType.includes('favorite') || actionType.includes('favori')) {
            const itemName = details.item_name || details.title || details.name || '';
            return `Retrait des favoris${itemName ? ` : ${itemName}` : ''}`;
          }

          const itemType = formatActionTypeName(actionType.replace('delete', '').replace('suppression', '').replace('remove', ''));
          return `Suppression${itemType ? ` d'un élément de type ${itemType}` : ''}`;
        } else if (actionType.includes('review') || actionType.includes('evaluation')) {
          const note = details.new_note || details.note;
          return `Évaluation${note ? ` avec une note de ${note}/5` : ''}`;
        } else if (actionType.includes('favorite') || actionType.includes('favori')) {
          // Nouveau cas pour l'ajout aux favoris
          const itemName = details.item_name || details.title || details.name || '';
          const actionVerb = actionType.includes('add') || actionType.includes('ajout') ? 'Ajout aux' : 'Retrait des';
          return `${actionVerb} favoris${itemName ? ` : ${itemName}` : ''}`;
        }

        // Pour les types non reconnus, créer une phrase descriptive à partir des détails
        const entries = Object.entries(details)
          .filter(([key]) => {
            // Filtrer les IDs et champs techniques pour ne garder que l'information utile
            return !(key.includes('id') || key === 'user_id' || key.includes('_id') || key === 'userId');
          })
          .map(([key, value]) => {
            const formattedKey = formatKeyName(key);
            return `${formattedKey}: ${value}`;
          });

        if (entries.length === 0) {
          // Si pas de détails exploitables, retourner un message par défaut avec le type d'action formaté
          return `Action: ${formatActionTypeName(actionType)}`;
        }

        return entries.join(', ');
    }
  } catch (error) {
    logger.warn('Erreur lors du formatage des détails d\'activité', { error, actionType });
    return null;
  }
};

/**
 * Transforme un type d'action technique en texte lisible
 */
const formatActionTypeName = (actionType: string): string => {
  // Remplacer les underscores par des espaces et mettre en forme
  return actionType
    .replace(/_/g, ' ')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .toLowerCase();
};

/**
 * Formate les noms de clés en texte lisible
 */
const formatKeyName = (key: string): string => {
  // Convertir les clés du format snake_case ou camelCase en texte lisible
  const keyMapping: Record<string, string> = {
    'montant': 'Montant',
    'title': 'Titre',
    'note': 'Note',
    'new_note': 'Nouvelle note',
    'old_note': 'Ancienne note',
    'defauts_count': 'Nombre de défauts',
    'qualites_count': 'Nombre de qualités',
    'has_comment': 'Avec commentaire',
    'device': 'Appareil',
    'reason': 'Raison',
    'type': 'Type',
    'status': 'Statut'
  };

  return keyMapping[key] || key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
};

/**
 * Masque une adresse IP (ex: *********** -> 192.168.*.*)
 */
const maskIpAddress = (ip: string): string => {
  if (!ip) return '';
  const parts = ip.split('.');
  if (parts.length === 4) {
    return `${parts[0]}.${parts[1]}.*.*`;
  }
  return ip.split(':')[0] + ':****'; // Pour IPv6
};

/**
 * Masque un email (ex: <EMAIL> -> t***@e***.com)
 */
const maskEmail = (email: string): string => {
  if (!email || !email.includes('@')) return '***@***.***';

  const [localPart, domain] = email.split('@');
  const domainParts = domain.split('.');

  const maskedLocalPart = localPart.charAt(0) + '***';
  const maskedDomain = domainParts[0].charAt(0) + '***.' +
                      domainParts[1].charAt(0) +
                      '***' +
                      (domainParts[1].length > 3 ? '.' + domainParts[1].slice(-2) : '');

  return `${maskedLocalPart}@${maskedDomain}`;
};

/**
 * Récupère l'adresse IP à partir de la requête
 */
export const getIpFromRequest = (req: Request): string => {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.socket.remoteAddress ||
    ''
  );
};

/**
 * Nettoie les anciennes entrées d'historique si la limite de l'abonnement est dépassée
 * @param userId ID de l'utilisateur
 */
async function cleanupOldHistoryEntries(userId: string): Promise<void> {
  try {
    // Récupérer les limites d'abonnement de l'utilisateur via la fonction centralisée
    const { isPremium, options } = await getUserSubscriptionLimits(userId);

    // Récupérer la limite d'historique (history_logs)
    const { default: subscriptions } = await import('../config/ConfigSubscriptions');
    const historyLimit = options.history_logs ||
      (isPremium ? subscriptions.premium.history_logs.included
                 : subscriptions.gratuit.history_logs.included);

    // Compter le nombre total d'entrées d'historique pour cet utilisateur
    const { count, error: countError } = await supabase
      .from('user_actions_history')
      .select('id', { count: 'exact' })
      .eq('user_id', userId);

    if (countError) {
      logger.error('Erreur lors du comptage des entrées d\'historique:', { error: countError, userId });
      return;
    }

    // Si le nombre d'entrées dépasse la limite, supprimer les plus anciennes
    if (count && count > (historyLimit || 20)) {
      const entriesToDelete = count - (historyLimit || 20);

      // Récupérer les IDs des entrées les plus anciennes à supprimer
      const { data: oldEntries, error: fetchError } = await supabase
        .from('user_actions_history')
        .select('id')
        .eq('user_id', userId)
        .order('action_date', { ascending: true })
        .limit(entriesToDelete);

      if (fetchError) {
        logger.error('Erreur lors de la récupération des anciennes entrées:', { error: fetchError, userId });
        return;
      }

      if (oldEntries && oldEntries.length > 0) {
        const idsToDelete = oldEntries.map(entry => entry.id);

        // Supprimer les entrées les plus anciennes
        const { error: deleteError } = await supabase
          .from('user_actions_history')
          .delete()
          .in('id', idsToDelete);

        if (deleteError) {
          logger.error('Erreur lors de la suppression des anciennes entrées:', { error: deleteError, userId });
          return;
        }

        logger.info(`${idsToDelete.length} anciennes entrées d'historique supprimées pour l'utilisateur ${userId}`);
      }
    }

    // Faire la même chose pour l'historique des connexions
    await cleanupOldLoginHistory(userId, historyLimit);

  } catch (error) {
    logger.error('Erreur lors du nettoyage des anciennes entrées d\'historique:', { error, userId });
  }
}

/**
 * Nettoie les anciennes entrées d'historique de connexion si la limite de l'abonnement est dépassée
 * @param userId ID de l'utilisateur
 * @param historyLimit Limite fournie depuis le type d'abonnement
 */
async function cleanupOldLoginHistory(userId: string, historyLimit?: number): Promise<void> {
  try {
    let limit = historyLimit || 0;

    // Si la limite n'est pas déjà fournie, récupérer les limites d'abonnement
    if (limit === 0) {
      // Récupérer les limites d'abonnement de l'utilisateur via la fonction centralisée
      const { isPremium, options } = await getUserSubscriptionLimits(userId);

      // Récupérer la limite d'historique (history_logs)
      const { default: subscriptions } = await import('../config/ConfigSubscriptions');
      limit = options.history_logs ||
        (isPremium ? subscriptions.premium.history_logs.included
                  : subscriptions.gratuit.history_logs.included);
    }

    // Compter le nombre total d'entrées d'historique de connexion pour cet utilisateur
    const { count, error: countError } = await supabase
      .from('user_login_history')
      .select('id', { count: 'exact' })
      .eq('user_id', userId);

    if (countError) {
      logger.error('Erreur lors du comptage des entrées d\'historique de connexion:', { error: countError, userId });
      return;
    }

    // Si le nombre d'entrées dépasse la limite, supprimer les plus anciennes
    if (count && count > limit) {
      const entriesToDelete = count - limit;

      // Récupérer les IDs des entrées les plus anciennes
      const { data: oldEntries, error: fetchError } = await supabase
        .from('user_login_history')
        .select('id')
        .eq('user_id', userId)
        .order('login_date', { ascending: true })
        .limit(entriesToDelete);

      if (fetchError) {
        logger.error('Erreur lors de la récupération des anciennes entrées de connexion:', { error: fetchError, userId });
        return;
      }

      if (oldEntries && oldEntries.length > 0) {
        const idsToDelete = oldEntries.map(entry => entry.id);

        // Supprimer les entrées les plus anciennes
        const { error: deleteError } = await supabase
          .from('user_login_history')
          .delete()
          .in('id', idsToDelete);

        if (deleteError) {
          logger.error('Erreur lors de la suppression des anciennes entrées de connexion:', { error: deleteError, userId });
          return;
        }

        logger.info(`${idsToDelete.length} anciennes entrées d'historique de connexion supprimées pour l'utilisateur ${userId}`);
      }
    }
  } catch (error) {
    logger.error('Erreur lors du nettoyage des anciennes entrées d\'historique de connexion:', { error, userId });
  }
}