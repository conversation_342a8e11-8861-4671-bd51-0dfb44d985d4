import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Calendar,
  Clock,
  RefreshCw,
  Info
} from 'lucide-react';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { notify } from '@/components/Notification';
import AiConsentModal from '../ai/AiConsentModal';
import logger from '@/utils/logger';
import { useAiConsent } from '@/hooks/useAiConsent';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';

interface ConsentStatus {
  hasConsent: boolean;
  firstName?: string;
  lastName?: string;
  createdAt?: string;
  updatedAt?: string;
  expiryDate?: string;
  isActive?: boolean;
  deactivatedAt?: string;
  deactivationReason?: string;
}

const AiConsentStatus: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [consentStatus, setConsentStatus] = useState<ConsentStatus | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { removeConsent } = useAiConsent();
  const [removing, setRemoving] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  useEffect(() => {
    const fetchConsentStatus = async () => {
      setLoading(true);
      setError(null);
      try {
        const headers = await getCommonHeaders();
        const response = await axios.get(
          `${API_CONFIG.baseURL}/api/user/ai-consent/details`,
          {
            headers,
            withCredentials: true
          }
        );
        setConsentStatus(response.data);
      } catch (err) {
        logger.error('Erreur lors de la récupération du statut du consentement IA:', err);
        setError('Une erreur est survenue lors de la récupération du statut de votre consentement.');
      } finally {
        setLoading(false);
      }
    };

    fetchConsentStatus();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleModalAccept = () => {
    setIsModalOpen(false);
    handleRefresh();
    notify('Votre consentement a été enregistré avec succès. Vous pouvez maintenant générer des images avec l\'IA.', 'success');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non disponible';
    try {
      return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  const getDaysUntilExpiry = (expiryDate?: string) => {
    if (!expiryDate) return null;
    try {
      const expiry = new Date(expiryDate);
      const now = new Date();
      const diffTime = expiry.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch (error) {
      return null;
    }
  };

  const daysUntilExpiry = consentStatus?.expiryDate ? getDaysUntilExpiry(consentStatus.expiryDate) : null;

  return (
    <Paper
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      elevation={0}
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        border: '1px solid #e0e0e0',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.03)'
      }}
    >
      <Box sx={{ p: { xs: 2.5, sm: 3.5 }, position: 'relative' }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          sx={{
            mb: 3,
            position: 'relative',
            display: 'inline-block',
            color: '#2D3748'
          }}
        >
          Statut du consentement IA
          <Box
            sx={{
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: '60%',
              height: 3,
              backgroundColor: '#FF6B2C',
              borderRadius: 1
            }}
          />
        </Typography>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress sx={{ color: '#FF6B2C' }} />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Statut actuel:
                  </Typography>
                  {consentStatus?.hasConsent ? (
                    <Chip
                      icon={<CheckCircle2 size={16} />}
                      label="Consentement actif"
                      color="success"
                      variant="filled"
                      size="small"
                    />
                  ) : (
                    <Chip
                      icon={<XCircle size={16} />}
                      label="Consentement non donné"
                      color="error"
                      variant="filled"
                      size="small"
                    />
                  )}
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshCw size={16} />}
                  onClick={handleRefresh}
                  sx={{
                    borderColor: '#FFE4BA',
                    color: '#FF6B2C',
                    '&:hover': { borderColor: '#FF6B2C', bgcolor: '#FFF8F3' }
                  }}
                >
                  Actualiser
                </Button>
              </Box>

              {consentStatus?.hasConsent && (
                <>
                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Calendar size={18} className="text-gray-500" />
                      <Typography variant="body2">
                        <strong>Date d'acceptation:</strong> {formatDate(consentStatus.createdAt)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Calendar size={18} className="text-gray-500" />
                      <Typography variant="body2">
                        <strong>Date d'expiration:</strong> {formatDate(consentStatus.expiryDate)}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Clock size={18} className="text-gray-500" />
                      <Typography variant="body2">
                        <strong>Temps restant:</strong> {daysUntilExpiry !== null ? `${daysUntilExpiry} jours` : 'Non disponible'}
                      </Typography>
                    </Box>
                  </Box>

                  {daysUntilExpiry !== null && daysUntilExpiry <= 14 && (
                    <Alert
                      severity="warning"
                      icon={<AlertTriangle size={20} />}
                      sx={{ mt: 2 }}
                    >
                      Votre consentement expire dans {daysUntilExpiry} jours. Pensez à le renouveler pour continuer à utiliser les fonctionnalités d'IA.
                    </Alert>
                  )}
                </>
              )}

              <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => setIsModalOpen(true)}
                  startIcon={consentStatus?.hasConsent ? <RefreshCw size={18} /> : <Info size={18} />}
                  sx={{
                    bgcolor: '#FF6B2C',
                    '&:hover': { bgcolor: '#FF7A35' },
                    py: 1.5
                  }}
                >
                  {consentStatus?.hasConsent ? 'Renouveler mon consentement' : 'Donner mon consentement'}
                </Button>
                {consentStatus?.hasConsent && (
                  <Button
                    variant="outlined"
                    color="error"
                    fullWidth
                    disabled={removing}
                    onClick={() => setConfirmOpen(true)}
                    sx={{
                      borderColor: '#FFE4BA',
                      color: '#FF6B2C',
                      mt: 1,
                      '&:hover': { borderColor: '#FF6B2C', bgcolor: '#FFF8F3' }
                    }}
                  >
                    Supprimer mon consentement
                  </Button>
                )}
              </Box>
            </Box>
          </Box>
        )}
      </Box>

      <AiConsentModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onAccept={handleModalAccept}
      />

      <Dialog
        open={confirmOpen}
        onClose={() => setConfirmOpen(false)}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            Êtes-vous sûr de vouloir supprimer votre consentement à l'utilisation de l'IA ? Cette action désactivera l'accès aux fonctionnalités IA jusqu'à un nouveau consentement.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)} color="inherit">
            Annuler
          </Button>
          <Button
            onClick={async () => {
              setConfirmOpen(false);
              setRemoving(true);
              const ok = await removeConsent();
              setRemoving(false);
              if (ok) {
                notify('Votre consentement a été supprimé avec succès.', 'success');
                handleRefresh();
              } else {
                notify('Erreur lors de la suppression du consentement.', 'error');
              }
            }}
            color="error"
            autoFocus
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default AiConsentStatus;
