import { Request, Response, NextFunction } from 'express';
import { messagingService } from '../services/messagingService';
import { validateRequest } from '../middleware/validation';
import { FileRequest } from '../middleware/upload';
import { z } from 'zod';
import { UploadedFile } from 'express-fileupload';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';

// Constantes pour les clés Redis
const CACHE_TTL = 3600; // 1 heure en secondes
const USER_CONVERSATIONS_KEY = (userId: string) => `user:${userId}:conversations`;
const USER_MESSAGES_KEY = (userId: string, conversationId: string) => `user:${userId}:conversation:${conversationId}:messages`;
const CONVERSATION_KEY = (conversationId: string) => `conversation:${conversationId}`;

// Schémas de validation
const createConversationSchema = z.object({
  recipient_id: z.string().uuid(),
  initial_message: z.string().min(1),
});

const sendMessageSchema = z.object({
  conversation_id: z.string().uuid(),
  content: z.string().min(1),
});

const updateConversationSchema = z.object({
  is_blocked: z.boolean().optional(),
  is_deleted: z.boolean().optional(),
});

const getMessagesSchema = z.object({
  page: z.number().int().min(1).optional(),
});

const getConversationsSchema = z.object({
  search: z.string().optional(),
  unread_only: z.boolean().optional(),
  show_deleted: z.boolean().optional(),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(50).optional(),
  order: z.string().optional(),
  direction: z.enum(['asc', 'desc']).optional(),
});

// Fonction d'invalidation du cache
const invalidateUserCache = async (userId: string, conversationId?: string) => {
  // logger.info(`[CACHE] Invalidation du cache pour l'utilisateur ${userId}${conversationId ? ` et la conversation ${conversationId}` : ''}`);
  // logger.info(`[CACHE] Cache des conversations supprimé pour l'utilisateur ${userId}`);


  // Supprimer toutes les variantes de la liste des conversations pour cet utilisateur
  const keys = await redis.keys(USER_CONVERSATIONS_KEY(userId) + '*');
  if (keys.length > 0) {
    await redis.del(...keys);
  }

  // Supprimer tous les caches de messages pour cette conversation
  if (conversationId) {
    const msgKeys = await redis.keys(USER_MESSAGES_KEY(userId, conversationId) + '*');
    if (msgKeys.length > 0) {
      await redis.del(...msgKeys);
    }
    await redis.del(CONVERSATION_KEY(conversationId));
    // logger.info(`[CACHE] Cache des messages supprimé pour la conversation ${conversationId}`);
  }
};

export const messagingController = {
  // Créer une nouvelle conversation
  createConversation: [
    validateRequest(createConversationSchema),
    async (req: FileRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        // Vérifier que l'utilisateur n'essaie pas de créer une conversation avec lui-même
        if (req.user!.userId === req.body.recipient_id) {
          res.status(400).json({
            success: false,
            error: "Vous ne pouvez pas créer une conversation avec vous-même."
          });
          return;
        }
        
        let attachments: UploadedFile[] = [];
        if (req.files && req.files.attachments) {
          if (Array.isArray(req.files.attachments)) {
            attachments = req.files.attachments;
          } else {
            attachments = [req.files.attachments];
          }
        }

        const conversation = await messagingService.createConversation({
          creatorId: req.user!.userId,
          participantId: req.body.recipient_id,
          initialMessage: req.body.initial_message,
          attachments
        });

        // Invalider le cache pour les deux utilisateurs
        // logger.info('[CACHE] Nouvelle conversation créée, invalidation des caches');
        await invalidateUserCache(req.user!.userId);
        await invalidateUserCache(req.body.recipient_id);

        // Journaliser l'action de création de conversation
        await logUserActivity(
          req.user!.userId,
          'conversation_create',
          conversation.id,
          'conversation',
          { 
            recipient_id: req.body.recipient_id,
            has_attachments: attachments.length > 0,
            initial_message_snippet: req.body.initial_message.length > 100 ? req.body.initial_message.substring(0, 100) + '...' : req.body.initial_message
          },
          getIpFromRequest(req)
        );

        res.status(201).json({
          success: true,
          data: conversation
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create conversation'
        });
        return;
      }
    }
  ],

  // Envoyer un message
  sendMessage: [
    validateRequest(sendMessageSchema),
    async (req: FileRequest, res: Response, next: NextFunction): Promise<void> => {
      try {
        const conversationId = req.body.conversation_id;
        const senderId = req.user!.userId;
        
        let recipientId = req.body.recipient_id || '';
        
        if (!recipientId) {
          const { data: conversation, error } = await supabase
            .from('user_messages_conversations')
            .select('user1_id, user2_id')
            .eq('id', conversationId)
            .single();
            
          if (error) {
            res.status(400).json({
              success: false,
              error: 'Conversation introuvable'
            });
            return;
          }
          
          recipientId = conversation.user1_id === senderId 
            ? conversation.user2_id 
            : conversation.user1_id;
        }
        
        // Vérifier que l'utilisateur n'essaie pas d'envoyer un message à lui-même
        if (senderId === recipientId) {
          res.status(400).json({
            success: false,
            error: "Vous ne pouvez pas vous envoyer de messages à vous-même."
          });
          return;
        }
        
        let attachments: UploadedFile[] = [];
        
        if (req.files) {
          if (req.files.files) {
            if (Array.isArray(req.files.files)) {
              attachments = req.files.files;
            } else {
              attachments = [req.files.files];
            }
          }
        }
        
        const message = await messagingService.sendMessage({
          conversationId,
          senderId,
          content: req.body.content,
          recipientId,
          attachments
        });

        // Invalider le cache pour l'expéditeur et le destinataire
        // logger.info('[CACHE] Nouveau message envoyé, invalidation des caches');
        await invalidateUserCache(senderId, conversationId);
        await invalidateUserCache(recipientId, conversationId);

        res.status(201).json({
          success: true,
          data: message
        });
      } catch (error: any) {
        console.error('DEBUG - Erreur détaillée lors de l\'envoi du message:', error);
        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Erreur lors de l\'envoi du message'
        });
        return;
      }
    }
  ],

  // Récupérer une conversation
  getConversation: async (req: Request, res: Response) => {
    try {
      const conversationId = req.params.id;
      const userId = req.user!.userId;
      
      // Vérifier le cache
      // logger.info(`[CACHE] Tentative de lecture du cache pour la conversation ${conversationId}`);
      const cachedConversation = await redis.get(CONVERSATION_KEY(conversationId));
      
      if (cachedConversation) {
        // logger.info(`[CACHE] Conversation ${conversationId} trouvée dans le cache`);
        res.json({
          success: true,
          data: JSON.parse(cachedConversation)
        });
        return;
      }
      
      //logger.info(`[CACHE] Cache miss pour la conversation ${conversationId}`);
      const result = await messagingService.getConversationById(conversationId, userId);

      if (!result.success || !result.data) {
        console.error('==== Failed to get conversation:', result.error);
        res.status(404).json({
          success: false,
          error: result.error || 'Conversation not found'
        });
        return;
      }

      // Mettre en cache
      // logger.info(`[CACHE] Mise en cache de la conversation ${conversationId}`);
      await redis.setex(
        CONVERSATION_KEY(conversationId),
        CACHE_TTL,
        JSON.stringify(result.data)
      );

      res.json({
        success: true,
        data: result.data
      });
      return;
    } catch (error) {
      console.error('Error in getConversation controller:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'An error occurred while retrieving conversation'
      });
      return;
    }
  },

  // Récupérer la liste des conversations
  getConversations: [
    async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const queryObj = {
          search: req.query.search as string,
          unread_only: req.query.unread_only === 'true',
          show_deleted: req.query.show_deleted === 'true',
          page: req.query.page ? parseInt(req.query.page as string) : undefined,
          limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
          order: req.query.order as string,
          direction: req.query.direction as 'asc' | 'desc'
        };
        
        await getConversationsSchema.parseAsync(queryObj);
        next();
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error instanceof z.ZodError 
            ? error.errors.map(e => ({ field: e.path.join('.'), message: e.message }))
            : 'Paramètres de requête invalides pour obtenir les conversations'
        });
        return;
      }
    },
    async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const userId = req.user!.userId;
        const cacheKey = USER_CONVERSATIONS_KEY(userId);
        
        // Vérifier le cache
        // logger.info(`[CACHE] Tentative de lecture du cache des conversations pour l'utilisateur ${userId}`);
        const cachedConversations = await redis.get(cacheKey);
        
        if (cachedConversations) {
          // logger.info(`[CACHE] Liste des conversations trouvée dans le cache pour l'utilisateur ${userId}`);
          res.json({
            success: true,
            data: JSON.parse(cachedConversations)
          });
          return;
        }
        
        // logger.info(`[CACHE] Cache miss pour les conversations de l'utilisateur ${userId}`);
        const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
        const offset = req.query.page ? (parseInt(req.query.page as string) - 1) * limit : 0;
        const search = req.query.search as string || '';
        const showDeleted = req.query.show_deleted === 'true';
        const unreadOnly = req.query.unread_only === 'true';
        const order = req.query.order as string || '';
        const direction = req.query.direction as 'asc' | 'desc' || '';

        // Clé de cache robuste prenant en compte tous les filtres
        const cacheKeyRobust = USER_CONVERSATIONS_KEY(userId)
          + `:showDeleted=${showDeleted}`
          + `:unreadOnly=${unreadOnly}`
          + `:search=${encodeURIComponent(search)}`
          + `:limit=${limit}`
          + `:offset=${offset}`
          + `:order=${order}`
          + `:direction=${direction}`;
        
        const cachedConversationsRobust = await redis.get(cacheKeyRobust);
        
        if (cachedConversationsRobust) {
          // logger.info(`[CACHE] Liste des conversations trouvée dans le cache pour l'utilisateur ${userId}`);
          res.json({
            success: true,
            data: JSON.parse(cachedConversationsRobust)
          });
          return;
        }
        
        // logger.info(`[CACHE] Cache miss pour les conversations de l'utilisateur ${userId}`);
        const result = await messagingService.getConversations({
          userId: req.user!.userId,
          limit,
          offset,
          search,
          showDeleted,
          unread_only: unreadOnly
        });

        // Mettre en cache
        // logger.info(`[CACHE] Mise en cache des conversations pour l'utilisateur ${userId}`);
        await redis.setex(
          cacheKeyRobust,
          CACHE_TTL,
          JSON.stringify(result.data)
        );

        res.json({
          success: true,
          data: result.data
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get conversations'
        });
        return;
      }
    }
  ],

  // Récupérer les messages d'une conversation
  getMessages: [
    async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const queryObj = {
          page: req.query.page ? parseInt(req.query.page as string) : undefined
        };
        
        await getMessagesSchema.parseAsync(queryObj);
        next();
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error instanceof z.ZodError 
            ? error.errors.map(e => ({ field: e.path.join('.'), message: e.message }))
            : 'Invalid query parameters'
        });
      }
    },
    async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const userId = req.user!.userId;
        const conversationId = req.params.conversationId;
        const cacheKey = USER_MESSAGES_KEY(userId, conversationId);
        
        // Vérifier le cache
        // logger.info(`[CACHE] Tentative de lecture du cache des messages pour la conversation ${conversationId}`);
        const cachedMessages = await redis.get(cacheKey);
        
        if (cachedMessages) {
          // logger.info(`[CACHE] Messages trouvés dans le cache pour la conversation ${conversationId}`);
          res.json({
            success: true,
            data: JSON.parse(cachedMessages)
          });
          return;
        }
        
        // logger.info(`[CACHE] Cache miss pour les messages de la conversation ${conversationId}`);
        const limit = req.query.limit ? parseInt(req.query.limit as string) : 20;
        const offset = req.query.page ? (parseInt(req.query.page as string) - 1) * limit : 0;
        
        const result = await messagingService.getMessages({
          conversationId: req.params.conversationId,
          limit,
          offset
        });

        // Mettre en cache
        // logger.info(`[CACHE] Mise en cache des messages pour la conversation ${conversationId}`);
        await redis.setex(
          cacheKey,
          CACHE_TTL,
          JSON.stringify(result.data)
        );

        res.json({
          success: true,
          data: result.data
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get messages'
        });
        return;
      }
    }
  ],

  // Marquer les messages comme lus
  markMessagesAsRead: async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const conversationId = req.params.conversationId;

      const result = await messagingService.markMessagesAsRead({
        userId,
        conversationId
      });

      if (!result.success) {
        res.status(400).json({
          success: false,
          error: result.error
        });
        return;
      }

      // Invalider le cache pour cette conversation
      // logger.info('[CACHE] Messages marqués comme lus, invalidation du cache');
      await invalidateUserCache(userId, conversationId);

      res.json({
        success: true,
        message: "Messages marked as read"
      });
      return;
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to mark messages as read'
      });
      return;
    }
  },

  // Mettre à jour une conversation (bloquer/supprimer)
  updateConversation: [
    validateRequest(updateConversationSchema),
    async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        let result;
        const userId = req.user!.userId;
        const conversationId = req.params.id;
        
        if (req.body.is_blocked !== undefined) {
          result = await messagingService.toggleBlock({
            userId,
            conversationId,
            isBlocked: req.body.is_blocked
          });
        }
        
        if (req.body.is_deleted !== undefined) {
          if (req.body.is_deleted) {
            result = await messagingService.deleteConversation({
              userId,
              conversationId
            });
          } else {
            result = await messagingService.unhideConversation({
              userId,
              conversationId
            });
          }
        }

        if (!result || !result.success) {
          res.status(400).json({
            success: false,
            error: result?.error || 'Erreur lors de la mise à jour de la conversation'
          });
          return;
        }

        // Invalider le cache pour les deux participants
        const resultGetConv = await messagingService.getConversationById(conversationId, userId);
        if (resultGetConv.success && resultGetConv.data) {
          const conv = resultGetConv.data as { user1_id: string; user2_id: string };
          const userIds = [conv.user1_id, conv.user2_id];
          for (const uid of userIds) {
            await invalidateUserCache(uid, conversationId);
          }
        }

        res.json({
          success: true,
          message: "Conversation mise à jour avec succès"
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update conversation'
        });
        return;
      }
    }
  ],

  // Marquer une conversation comme non lue
  markConversationAsUnread: async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const conversationId = req.params.conversationId;

      const result = await messagingService.markConversationAsUnread({
        userId,
        conversationId
      });

      if (!result.success) {
        res.status(400).json(result);
        return;
      }

      // Invalider le cache
      // // logger.info('[CACHE] Conversation marquée comme non lue, invalidation du cache');
      await invalidateUserCache(userId, conversationId);

      // Émettre un événement socket pour informer les clients
      const io = req.app.get('io');
      if (io) {
        io.to(`user_${userId}`).emit('conversation_unread', {
          conversation_id: conversationId
        });
      }

      res.json(result);
      return;
    } catch (error) {
      console.error('Erreur lors du marquage de la conversation comme non lue:', error);
      res.status(500).json({
        success: false,
        message: "Une erreur est survenue lors du marquage de la conversation comme non lue."
      });
      return;
    }
  },

  // Supprimer un message
  deleteMessage: async (req: Request, res: Response) => {
    try {
      const { conversationId, messageId } = req.params;
      const userId = req.user!.userId;

      const result = await messagingService.deleteMessage({
        userId,
        conversationId,
        messageId
      });

      if (!result.success) {
        res.status(400).json(result);
        return;
      }

      // Invalider le cache pour cette conversation
      // logger.info('[CACHE] Message supprimé, invalidation du cache');
      await invalidateUserCache(userId, conversationId);

      res.json(result);
      return;
    } catch (error) {
      console.error('Error deleting message:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete message'
      });
      return;
    }
  },

  // Obtenir le nombre de conversations
  getConversationCount: async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const count = await messagingService.countUserConversations(userId);
      const monthlyCount = await messagingService.countUserConversationsCreatedThisMonth(userId);
      
      const { limit } = await messagingService.canUserCreateNewConversation(userId);
      
      res.json({
        success: true,
        data: {
          count,
          total_limit: 50,
          monthly_count: monthlyCount,
          monthly_limit: limit
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Erreur lors de la récupération du nombre de conversations'
      });
      return;
    }
  },

  // Obtenir le nombre de messages non lus
  getUnreadMessageCount: async (req: Request, res: Response) => {
    try {
      const userId = req.user!.userId;
      const result = await messagingService.getUnreadMessageCount(userId);

      if (!result.success) {
        res.status(400).json({
          success: false,
          error: result.error || 'Erreur lors de la récupération du nombre de messages non lus'
        });
        return;
      }

      res.json({
        success: true,
        data: { count: result.data }
      });
    } catch (error) {
      console.error('Erreur dans getUnreadMessageCount controller:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Une erreur est survenue lors de la récupération du nombre de messages non lus'
      });
      return;
    }
  }
};
