// Types pour l'éditeur de cartes de visite et flyers

export type CardTemplateType = 'business_card' | 'business_card_landscape' | 'flyer' | 'flyer_landscape';

export interface ElementBase {
  id: string;
  type: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
  rotation?: number;
  isDragging?: boolean;
  isSelected?: boolean;
  name?: string;
}

export interface TextElement extends ElementBase {
  type: 'text';
  properties: {
    text: string;
    fontSize: number;
    fontFamily: string;
    fill: string;
    align: 'left' | 'center' | 'right';
    fontStyle?: string;
    textDecoration?: string;
    // Propriétés de contour
    stroke?: string;
    strokeWidth?: number;
    // Propriétés d'ombre
    shadowColor?: string;
    shadowBlur?: number;
    shadowOffset?: { x: number, y: number };
    // Propriétés avancées
    letterSpacing?: number;
    lineHeight?: number;
    padding?: number;
    opacity?: number;
    visible?: boolean;
    ellipsis?: boolean;
    wrap?: 'word' | 'char' | 'none';
    textShadow?: {
      color: string;
      blur: number;
      offset: { x: number, y: number };
      opacity: number;
    };
  };
}

export interface ImageElement extends ElementBase {
  type: 'image';
  properties: {
    src: string;
    cornerRadius?: number;
    filters?: {
      type: 'blur' | 'brighten' | 'contrast' | 'emboss' | 'enhance' | 'grayscale' | 'hsl' | 'hsv' | 'invert' | 'kaleidoscope' | 'mask' | 'noise' | 'pixelate' | 'rgb' | 'sepia' | 'solarize' | 'threshold';
      params: any;
    }[];
    shadow?: {
      color: string;
      blur: number;
      offsetX: number;
      offsetY: number;
      opacity: number;
    };
    visible?: boolean;
  };
}

export interface ShapeElement extends ElementBase {
  type: 'shape';
  properties: {
    shape: 'rect' | 'circle' | 'line' | 'ellipse' | 'star' | 'arrow' | 'polygon' | 'ring' | 'arc' | 'wedge' | 'path';
    fill: string;
    stroke?: string;
    strokeWidth?: number;
    cornerRadius?: number;

    // Propriétés spécifiques aux formes
    // Star
    numPoints?: number;

    // Star, Ring, Arc
    innerRadius?: number;
    outerRadius?: number;

    // Arrow, Line
    points?: number[];
    pointerLength?: number;
    pointerWidth?: number;

    // Polygon
    sides?: number;

    // Polygon, Wedge
    radius?: number;

    // Arc, Wedge
    angle?: number;

    // Path
    data?: string;
    tension?: number;
    closed?: boolean;

    // Propriétés générales
    opacity?: number;
    lineCap?: string;
    lineJoin?: string;
    dash?: number[];
    shadowColor?: string;
    shadowBlur?: number;
    shadowOffset?: { x: number, y: number };
    shadowOpacity?: number;
    visible?: boolean;

  };
}

export interface QRCodeElement extends ElementBase {
  type: 'qrcode';
  properties: {
    data: string;
    fill: string;
    background?: string;
    visible?: boolean;
  };
}

export interface DrawingElement extends ElementBase {
  type: 'drawing';
  properties: {
    points: number[];
    stroke: string;
    strokeWidth: number;
    tension: number;
    tool: 'brush' | 'eraser' | 'eraser-result' | 'polygon';
    opacity?: number;
    // Propriétés pour le résultat de la gomme
    imageSrc?: string;
    width?: number;
    height?: number;
    visible?: boolean;
  };
}

export type CardElement = TextElement | ImageElement | ShapeElement | QRCodeElement | DrawingElement;

export interface CardTemplateData {
  width: number;
  height: number;
  background_color?: string;
  background_image?: string;
  elements: CardElement[];
  id?: string; // ID optionnel pour les templates sauvegardés
}

export interface CardTemplate {
  id: string;
  user_id: string;
  name: string;
  type: CardTemplateType;
  template_data: CardTemplateData;
  is_public: boolean;
  is_ai_generated: boolean;
  created_at: string;
  updated_at: string;
}

export interface CardExport {
  id: string;
  user_id: string;
  template_id: string;
  export_type: 'pdf' | 'png' | 'jpg';
  file_path: string;
  created_at: string;
}

// Types pour les réponses API
export interface TemplatesResponse {
  success: boolean;
  data: CardTemplate[];
}

export interface TemplateResponse {
  success: boolean;
  data: CardTemplate;
}

export interface RandomTemplateResponse {
  success: boolean;
  data: CardTemplate;
  creditsUsed: number;
  creditsRemaining: number;
  includesImage?: boolean;
  includesBackgroundImage?: boolean;
}

export interface MultipleTemplatesResponse {
  success: boolean;
  templates: CardTemplateData[];
  creditsUsed: number;
  creditsRemaining: number;
  includesImage?: boolean;
  includesBackgroundImage?: boolean;
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: any[];
  requiresConsent?: boolean;
  creditsRequired?: number;
  currentCredits?: number;
  limitReached?: boolean;
  currentCount?: number;
  limit?: number;
}
