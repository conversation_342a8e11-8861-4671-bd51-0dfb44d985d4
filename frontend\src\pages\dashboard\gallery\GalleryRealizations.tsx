import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { notify } from '../../../components/Notification';
import { Pencil, Upload, Trash2, X, FolderPlus, Camera, Sparkles } from 'lucide-react';
import ModalPortal from '../../../components/ModalPortal';
import { Tooltip } from '@mui/material';
import logger from '../../../utils/logger';
import DOMPurify from 'dompurify';
import { GalleryFolder, ProfilData, GalleryImage, GalleryModalData } from './types';
import { createGallery, updateGallery, deleteGallery, toggleGalleryStatus } from './api';
import { API_CONFIG, API_URL } from '../../../config/api';
import { getMultipartHeaders, getCommonHeaders } from '../../../utils/headers';
import GalleryPhotos from './GalleryPhotos';
import './Gallery.css';
import FeaturedPhotos from './FeaturedPhotos';
import { CaptionInput } from './CaptionInput';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import imageCompression from 'browser-image-compression';
import useContentModeration from '@/hooks/useContentModeration';
import useImageModeration from '@/hooks/useImageModeration';
import ImageModerationStatus from '@/components/ImageModerationStatus';
import RejectedImageMessage from '@/components/RejectedImageMessage';
import AiGalleryImageGenerator from '../../../components/ai/AiGalleryImageGenerator';
import AiGenerationSystem from '../../../components/ai/AiGenerationSystem';

const DEFAULT_IMAGE_URL = `${API_URL}/api/storage-proxy/galerie_realisation_client/galerie-defaut-jobpartiel.jpg`;

interface GalleryRealizationsProps {
  onUpdateProfil?: (isDeleting?: boolean, galleryId?: string) => void;
  handleAddPhotoToGallery?: (galleryId: string, photo: any) => void;
  handleRemovePhotoFromGallery?: (galleryId: string, photoId: string) => void;
  handleDeleteGallery?: (galleryId: string) => void;
}

const GalleryRealizations: React.FC<GalleryRealizationsProps> = ({ onUpdateProfil, handleAddPhotoToGallery, handleRemovePhotoFromGallery, handleDeleteGallery }) => {
  const { slug } = useParams();
  const { user } = useAuth();
  const [isOwnProfil] = useState(!slug || slug === user?.profil?.data?.slug);
  const [profil, setProfil] = useState<ProfilData | null>(null);
  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [galleryModalData, setGalleryModalData] = useState<GalleryModalData>({ name: '', description: '' });
  const [selectedGallery, setSelectedGallery] = useState<GalleryFolder | null>(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [tempImageCaption, setTempImageCaption] = useState<string>('');
  const [previousImageCaption, setPreviousImageCaption] = useState<string>('');
  const [isConfirmingImageDeletion, setIsConfirmingImageDeletion] = useState(false);
  const [selectedGalleryIdForDeletion, setSelectedGalleryIdForDeletion] = useState<string | null>(null);
  const [isEditingGallery, setIsEditingGallery] = useState(false);
  const [tempCoverImage, setTempCoverImage] = useState<File | null>(null);
  const [tempGalleryName, setTempGalleryName] = useState<string>('');
  const [tempGalleryDescription, setTempGalleryDescription] = useState<string>('');
  const [isGalleryLoading, setIsGalleryLoading] = useState(true);
  const [shouldDeleteCoverImage, setShouldDeleteCoverImage] = useState(false);
  const [isViewingPhotos, setIsViewingPhotos] = useState(false);
  const [selectedGalleryForPhotos, setSelectedGalleryForPhotos] = useState<string | null>(null);
  const [subscriptionConfig, setSubscriptionConfig] = useState<any>(null);
  const { getOptionPremiumUtilisateur } = useSubscription();
  const [compressionProgress, setCompressionProgress] = useState<number>(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { validateContentSafety } = useContentModeration();
  const [isModerationLoading, setIsModerationLoading] = useState(false);
  const { moderateImage, isLoading: isImageModerationLoading } = useImageModeration();
  const [moderationFile, setModerationFile] = useState<File | null>(null);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const [moderationGalleryId, setModerationGalleryId] = useState<string | null>(null);
  const [isCreatingGallery, setIsCreatingGallery] = useState(false);
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);
  const [selectedGalleryForAi, setSelectedGalleryForAi] = useState<GalleryFolder | null>(null);
  const [descriptionToImprove, setDescriptionToImprove] = useState('');

  // Effet pour gérer le nettoyage des URLs de blob
  useEffect(() => {
    if (tempCoverImage) {
      const url = URL.createObjectURL(tempCoverImage);
      setPreviewUrl(url);
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [tempCoverImage]);

  // Effet pour gérer la création d'URL pour l'image de couverture lors de la création d'une galerie
  useEffect(() => {
    if (galleryModalData.cover_image && !isEditingGallery) {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    }
  }, [galleryModalData.cover_image, isEditingGallery, previewUrl]);

  const loadGalleries = useCallback(async (shouldUpdateProfil: boolean = false) => {
    setIsGalleryLoading(true);
    try {
      const endpoint = isOwnProfil ?
        `${API_CONFIG.baseURL}/api/users/galleries` :
        `${API_CONFIG.baseURL}/api/users/galleries/${slug}`;

      const headers = await getCommonHeaders();
      const response = await axios.get(endpoint, {
        headers,
        withCredentials: true
      });

      // Gérer les deux formats de réponse possibles
      const galleries = isOwnProfil ? response.data.galleries : response.data;

      // Attendre que le profil soit mis à jour avant de déclencher onUpdateProfil afin d'eviter les problèmes en production (react strict)
      await new Promise<void>((resolve) => {
        setProfil((prevProfil: ProfilData | null) => {
          const updatedProfil = {
            ...prevProfil,
            galleryFolders: Array.isArray(galleries) ? galleries.map((gallery: any) => ({
              id: gallery.id,
              name: gallery.name,
              description: gallery.description,
              cover_image: gallery.cover_image || DEFAULT_IMAGE_URL,
              status: gallery.status || 'actif',
              imagesCount: gallery.imagesCount,
              createdAt: gallery.createdAt
            })) : []
          } as ProfilData;

          // Résoudre la promesse après la mise à jour du profil
          resolve();
          return updatedProfil;
        });
      });

      // Déclencher le calcul de progression seulement si demandé et après la mise à jour du profil
      if (shouldUpdateProfil && onUpdateProfil && isOwnProfil) {
        onUpdateProfil(false, galleries[0]?.id);
      }

      logger.info('Galeries récupérées avec succès 2');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des galeries', error);
      notify('Erreur lors de la récupération des galeries', 'error');
    } finally {
      setIsGalleryLoading(false);
    }
  }, [isOwnProfil, slug]);

  useEffect(() => {
    loadGalleries();
    loadSubscriptionConfig();
  }, [loadGalleries]);

  const loadSubscriptionConfig = async () => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions`, {
        headers,
        withCredentials: true
      });
      if (response.data.success) {
        setSubscriptionConfig(response.data.data);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement de la configuration des abonnements:', error);
    }
  };

  const handleOpenEditGalleryModal = (gallery: GalleryFolder) => {
    setSelectedGallery(gallery);
    setTempGalleryName(gallery.name);
    setTempGalleryDescription(gallery.description || '');
    setIsEditingGallery(true);
    setIsGalleryModalOpen(true);
  };

  const getImagePreviewUrl = (image: File | string | undefined): string | undefined => {
    if (!image) return undefined;
    if (image instanceof File) {
      // Nettoyer les anciens URLs de blob
      const url = URL.createObjectURL(image);
      // Ajouter un nettoyage automatique quand l'URL n'est plus nécessaire
      setTimeout(() => URL.revokeObjectURL(url), 1000);
      return url;
    }
    // Si c'est un objet Blob (qui pourrait être converti en chaîne [object Blob])
    if (typeof image === 'object') {
      try {
        const url = URL.createObjectURL(image as any);
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        return url;
      } catch (error) {
        logger.error('Erreur lors de la création de l\'URL pour l\'objet', error);
        return DEFAULT_IMAGE_URL;
      }
    }
    return image;
  };

  const handleCreateGallery = async () => {
    setIsGalleryLoading(true);
    setIsModerationLoading(true);
    try {
      // Vérifier le contenu avec la modération
      const isNameSafe = await validateContentSafety(galleryModalData.name, 'gallery_name' as any);
      if (!isNameSafe) {
        setIsGalleryLoading(false);
        setIsModerationLoading(false);
        return;
      }
      // Vérifier la description si elle existe
      if (galleryModalData.description && galleryModalData.description.trim() !== '') {
        const isDescriptionSafe = await validateContentSafety(galleryModalData.description, 'gallery_description' as any);
        if (!isDescriptionSafe) {
          setIsGalleryLoading(false);
          setIsModerationLoading(false);
          return;
        }
      }

      const formData = new FormData();
      formData.append('name', galleryModalData.name);
      formData.append('description', galleryModalData.description);
      if (galleryModalData.cover_image) {
        formData.append('coverImage', galleryModalData.cover_image);
      }
      await createGallery(formData);
      notify('Galerie créée avec succès', 'success');
      setIsGalleryModalOpen(false);
      setGalleryModalData({ name: '', description: '' });
      loadGalleries();
    } catch (error: any) {
      logger.error('Erreur lors de la création de la galerie', error);
      notify('Erreur : ' + error.message, 'error');
    } finally {
      setIsGalleryLoading(false);
      setIsModerationLoading(false);
    }
  };

  const handleEditGallery = async (galleryId: string) => {
    setIsGalleryLoading(true);
    setIsModerationLoading(true);
    try {
      // Vérifier le contenu avec la modération
      const isNameSafe = await validateContentSafety(tempGalleryName, 'gallery_name' as any);
      if (!isNameSafe) {
        setIsGalleryLoading(false);
        setIsModerationLoading(false);
        return;
      }

      // Vérifier la description si elle existe
      if (tempGalleryDescription && tempGalleryDescription.trim() !== '') {
        const isDescriptionSafe = await validateContentSafety(tempGalleryDescription, 'gallery_description' as any);
        if (!isDescriptionSafe) {
          setIsGalleryLoading(false);
          setIsModerationLoading(false);
          return;
        }
      }

      const formData = new FormData();
      formData.append('name', tempGalleryName);
      if (tempGalleryDescription) {
        formData.append('description', tempGalleryDescription);
      }

      // Si une nouvelle image est sélectionnée
      if (tempCoverImage) {
        formData.append('coverImage', tempCoverImage);
      }

      // Si l'image doit être supprimée
      if (shouldDeleteCoverImage) {
        formData.append('deleteCoverImage', 'true');
      }

      await updateGallery(galleryId, formData);
      notify('Galerie mise à jour avec succès', 'success');
      setIsGalleryModalOpen(false);
      setSelectedGallery(null);
      setGalleryModalData({ name: '', description: '' });
      setTempGalleryName('');
      setTempGalleryDescription('');
      setTempCoverImage(null);
      setShouldDeleteCoverImage(false);
      setIsEditingGallery(false);
      setPreviewUrl(null);
      loadGalleries();
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour de la galerie', error);
      notify('Erreur lors de la mise à jour de la galerie', 'error');
    } finally {
      setIsGalleryLoading(false);
      setIsModerationLoading(false);
    }
  };

  const handleDeleteGalleryInternal = async (galleryId: string) => {
    setIsGalleryLoading(true);
    try {
      await deleteGallery(galleryId);
      notify('Galerie supprimée avec succès', 'success');
      setIsConfirmingImageDeletion(false);
      setSelectedGalleryIdForDeletion(null);

      // Mettre à jour le profil en retirant la galerie supprimée
      if (profil) {
        const updatedGalleries = profil.galleryFolders.filter(gallery => gallery.id !== galleryId);
        const updatedProfil = {
          ...profil,
          galleryFolders: updatedGalleries
        };
        setProfil(updatedProfil);
        onUpdateProfil?.(true, galleryId);
        // Notifier le parent via la prop si elle existe
        handleDeleteGallery?.(galleryId);
      }

      loadGalleries();
    } catch (error: any) {
      logger.error('Erreur lors de la suppression de la galerie', error);
      notify('Erreur lors de la suppression de la galerie', 'error');
    } finally {
      setIsGalleryLoading(false);
    }
  };

  const handleToggleGalleryStatus = async (gallery: GalleryFolder) => {
    setIsGalleryLoading(true);
    try {
      // Déterminer le nouveau statut (inverse du statut actuel)
      const newStatus = gallery.status === 'actif' ? 'inactif' : 'actif';

      // Appeler l'API pour changer le statut
      await toggleGalleryStatus(gallery.id, newStatus);

      // Mettre à jour le profil avec le nouveau statut
      if (profil) {
        const updatedGalleries = profil.galleryFolders.map(g => {
          if (g.id === gallery.id) {
            return {
              ...g,
              status: newStatus as 'actif' | 'inactif'
            };
          }
          return g;
        });

        setProfil({
          ...profil,
          galleryFolders: updatedGalleries
        });
      }

      // Afficher une notification de succès
      notify(`Galerie ${newStatus === 'actif' ? 'activée' : 'désactivée'} avec succès`, 'success');

      // Recharger les galeries pour mettre à jour l'interface
      loadGalleries();
    } catch (error: any) {
      logger.error(`Erreur lors de la ${gallery.status === 'actif' ? 'désactivation' : 'activation'} de la galerie`, error);

      // Vérifier si c'est une erreur de limite d'abonnement
      if (error.response?.status === 403 && error.response?.data?.error === 'Limite de galeries atteinte') {
        const galleryLimit = error.response.data.galleryLimit || getOptionPremiumUtilisateur('galleries') || 1;
        const activeCount = error.response.data.activeCount || 0;

        notify(
          `Vous ne pouvez pas avoir plus de ${galleryLimit} galerie(s) active(s) avec votre abonnement actuel. Vous avez déjà ${activeCount} galerie(s) active(s). Passez à un abonnement supérieur pour activer plus de galeries.`,
          'error'
        );
      } else {
        notify(`Erreur lors de la ${gallery.status === 'actif' ? 'désactivation' : 'activation'} de la galerie`, 'error');
      }
    } finally {
      setIsGalleryLoading(false);
    }
  };

  const handleConfirmImageCaptionChange = async () => {
    setIsGalleryLoading(true);
    try {
      const sanitizedCaption = DOMPurify.sanitize(tempImageCaption);
      const formData = new FormData();
      formData.append('caption', sanitizedCaption);

      const response = await axios.put(`${API_CONFIG.baseURL}/api/users/gallery/photo/${selectedImage?.id}`, formData, {
        headers: await getMultipartHeaders(),
        withCredentials: true
      });

      if (response.data && response.data.success) {
        notify('Légende de l\'image mise à jour avec succès', 'success');
        setTempImageCaption('');
        setPreviousImageCaption('');
        setIsImageModalOpen(false);
        setSelectedImage(null);
        loadGalleries();
      } else {
        throw new Error('Erreur lors de la mise à jour de la légende de l\'image');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour de la légende de l\'image', error);
      notify('Erreur lors de la mise à jour de la légende de l\'image', 'error');
    } finally {
      setIsGalleryLoading(false);
    }
  };

  const handleViewPhotos = (galleryId: string) => {
    setSelectedGalleryForPhotos(galleryId);
    setIsViewingPhotos(true);
  };

  const handleModerationWithFile = async (file: File) => {
    try {
      // Modérer l'image
      const result = await moderateImage(file, 'gallery_cover');

      // Arrêter le chargement de la modération
      setIsModerationLoading(false);
      if (result.isSafe) {
        // L'image est sûre, on peut l'utiliser
        notify('Image validée par la modération', 'success');
        // Réinitialiser les états de rejet
        setIsImageRejected(false);
        setRejectionDescription(undefined);
        setRejectionImprovementSuggestions(undefined);
        if (isCreatingGallery) {
          // Pour la création, on crée une URL pour l'aperçu
          const url = URL.createObjectURL(file);
          setPreviewUrl(url);
          setGalleryModalData((prev: GalleryModalData) => {
            logger.error('[DEBUG] setGalleryModalData in moderation', { prev, file });
            return { ...prev, cover_image: file };
          });
        } else {
          // Pour l'édition, on utilise tempCoverImage qui est géré par useEffect
          setTempCoverImage(file);
        }
        // Fermer la modale de modération
        setIsModerationModalOpen(false);
      } else {
        // L'image est inappropriée
        notify('Image refusée : ne respecte pas nos règles de modération', 'error');
        // Afficher un message plus détaillé dans la console pour le débogage
        logger.info('Image refusée par la modération', {
          description: result.description,
          contentType: 'gallery_cover'
        });
        // Mettre à jour les états pour afficher le message de rejet
        setIsImageRejected(true);
        setRejectionDescription(result.description);
        setRejectionImprovementSuggestions(result.improvementSuggestions);
        // S'assurer que la modale reste ouverte pour afficher le message détaillé
        setIsModerationModalOpen(true);
        // Ajouter un log pour le débogage
        logger.info('Modale de rejet ouverte', {
          isImageRejected: true,
          isModerationModalOpen: true,
          description: result.description
        });
      }
    } catch (err) {
      logger.error('Erreur lors de la modération de l\'image', err);
      notify('Erreur lors de la vérification de l\'image. Veuillez réessayer.', 'error');
      // En cas d'erreur, fermer la modale et réinitialiser les états
      setIsModerationLoading(false);
      setIsModerationModalOpen(false);
      if (moderationPreviewUrl) {
        URL.revokeObjectURL(moderationPreviewUrl);
        setModerationPreviewUrl(null);
      }
      setModerationFile(null);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      setRejectionImprovementSuggestions(undefined);
    } finally {
      // Ne pas fermer la modale ici, car nous voulons afficher le message de rejet si l'image est refusée
      // La modale sera fermée dans le cas où l'image est acceptée ou en cas d'erreur
      // Réinitialiser la progression de compression
      setCompressionProgress(0);
    }
  };

  const handleAddGallery = () => {
    const galleryLimit = getOptionPremiumUtilisateur('galleries') || 1;
    if (profil?.galleryFolders && profil.galleryFolders.length >= galleryLimit) {
      notify(`Vous ne pouvez pas avoir plus de ${galleryLimit} galerie(s) avec votre abonnement, vous en avez actuellement ${profil.galleryFolders.length}. Supprimez une galerie avant d'en ajouter une nouvelle ou passez à un abonnement supérieur.`, 'error');
      return;
    }
    setIsGalleryModalOpen(true);
    setSelectedGallery(null);
    setGalleryModalData({ name: '', description: '' });
    setIsCreatingGallery(true);
  };

  const handleImproveDescription = (gallery: GalleryFolder) => {
    setSelectedGalleryForAi(gallery);
    setDescriptionToImprove(gallery.description || '');
    setIsAiConfirmModalOpen(true);
  };

  const handleAiGenerationComplete = (content: string | null) => {
    if (!content) {
      setIsAiConfirmModalOpen(false);
      return;
    }
    if (isEditingGallery) {
      setTempGalleryDescription(content);
    } else {
      setGalleryModalData((prev: GalleryModalData) => ({ ...prev, description: content }));
    }
    setIsAiConfirmModalOpen(false);
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
            <Camera className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">Galerie des réalisations</h2>
        </div>
      </div>

      <div className="mb-8">
        {!profil?.galleryFolders || profil.galleryFolders.length === 0 ? (
          <motion.div
            className="bg-gradient-to-br from-[#FFF8F3] to-white rounded-xl p-8 border border-[#FFE4BA] shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="p-4 bg-white rounded-full shadow-md">
                <FolderPlus className="h-8 w-8 text-[#FF6B2C]" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800">Aucune galerie disponible</h3>
              <p className="text-gray-600 max-w-lg">
                Il semble que vous n'ayez pas encore créé de galerie.
                <br /> Commencez à ajouter vos réalisations pour mettre en valeur votre travail !
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleAddGallery}
                  className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl flex items-center space-x-3 group"
                >
                  <FolderPlus className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  <span className="font-medium">Ajouter une nouvelle galerie</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        ) : (
          <>
            <div className="jp-galleries-container mb-8 ">
              {profil?.galleryFolders.map((gallery) => (
                <div
                  key={gallery.id}
                  className="jp-gallery-item bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-200 mb-4 relative group"
                  onClick={() => handleViewPhotos(gallery.id)}
                >
                  {gallery.status === 'inactif' && (
                    <div className="absolute inset-0 bg-gray-200/80 z-[1] pointer-events-none gallery-disabled-overlay">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 gallery-disabled-message">
                        <div className="bg-gray-500 text-white px-4 py-2 rounded-md text-center">
                          Galerie désactivée
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="jp-gallery-image-container">
                    <img
                      src={gallery.cover_image || DEFAULT_IMAGE_URL}
                      alt={`Galerie ${gallery.name}`}
                      className={`w-full h-full object-cover transition-all duration-300 ${gallery.status === 'inactif' ? 'grayscale brightness-75' : ''}`}
                      loading="lazy"
                    />
                    <div className="absolute top-3 right-3 flex flex-col gap-2 items-end z-10">
                      <span className="bg-primary/90 text-white text-sm px-3 py-1 rounded-lg">
                        {gallery.imagesCount} photos
                      </span>
                    </div>
                  </div>

                  <div className="jp-gallery-content">
                    <div className="jp-gallery-header">
                      <h3 className="jp-gallery-title">
                        {gallery.name}
                      </h3>
                      {isOwnProfil && (
                        <div
                          className="jp-gallery-actions flex items-center gap-1 z-20"
                          onClick={(e) => e.stopPropagation()}
                          style={{ position: 'relative' }}
                        >
                          {gallery.status === 'inactif' ? (
                            <Tooltip title="Activer">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleGalleryStatus(gallery);
                                }}
                                className="p-2 bg-white hover:bg-white text-gray-600 hover:text-green-500 rounded-lg transition-colors shadow-sm z-20"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                  <path d="M12 5v14M5 12h14"></path>
                                </svg>
                              </button>
                            </Tooltip>
                          ) : (
                            <Tooltip title="Désactiver">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleGalleryStatus(gallery);
                                }}
                                className="p-2 bg-white/90 text-gray-600 hover:text-amber-500 rounded-lg transition-colors shadow-sm"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                  <path d="M5 12h14"></path>
                                </svg>
                              </button>
                            </Tooltip>
                          )}
                          <Tooltip title="Modifier">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleOpenEditGalleryModal(gallery);
                              }}
                              className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-[#FF6B2C] rounded-lg transition-colors shadow-sm"
                            >
                              <Pencil className="h-5 w-5" />
                            </button>
                          </Tooltip>
                          <Tooltip title="Améliorer la description avec l'IA">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleImproveDescription(gallery);
                              }}
                              className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-[#FF6B2C] rounded-lg transition-colors shadow-sm"
                            >
                              <Sparkles className="h-5 w-5" />
                            </button>
                          </Tooltip>
                          <Tooltip title="Supprimer la galerie">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsConfirmingImageDeletion(true);
                                setSelectedGalleryIdForDeletion(gallery.id);
                              }}
                              className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-red-500 rounded-lg transition-colors shadow-sm"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </Tooltip>
                        </div>
                      )}
                    </div>

                    <p className="text-sm jp-gallery-description line-clamp-2">
                      {gallery.description || 'Aucune description'}
                    </p>

                    <div className="jp-gallery-footer">
                      <span className="text-sm text-gray-500">
                        Créée le {new Date(gallery.createdAt).toLocaleDateString('fr-FR', {
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric'
                        })}
                      </span>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewPhotos(gallery.id);
                        }}
                        className={`${
                          gallery.status === 'inactif'
                            ? 'bg-gray-100 text-gray-600 border border-gray-300 hover:bg-gray-200'
                            : 'bg-primary text-white border border-primary hover:bg-primary/90'
                        } px-4 sm:px-6 md:px-8 py-1.5 rounded-lg transition-colors duration-300 text-sm sm:text-base flex-shrink-0 z-20 relative`}
                      >
                        Voir les photos
                      </motion.button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {isOwnProfil && (
              <motion.div
                className="bg-gradient-to-br from-[#FFF8F3] to-white rounded-xl p-8 border border-[#FFE4BA] shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-4 bg-white rounded-full shadow-md">
                    <FolderPlus className="h-8 w-8 text-[#FF6B2C]" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Envie d'ajouter plus de réalisations ?</h3>
                  <p className="text-gray-600 max-w-lg">
                    Mettez en valeur vos talents en ajoutant de nouvelles galeries photos.
                    Plus vous montrez vos réalisations, plus vous augmentez la confiance de vos futurs clients !
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleAddGallery}
                    className="mt-4 px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl flex items-center space-x-3 group"
                  >
                    <FolderPlus className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                    <span className="font-medium">Ajouter une nouvelle galerie</span>
                  </motion.button>
                </div>
              </motion.div>
            )}
          </>
        )}
      </div>

      {/* Section des photos mises en avant */}
      <div className="mt-12">
        <FeaturedPhotos />
      </div>

      {/* Modale pour ajouter ou modifier une galerie */}
      <AnimatePresence>
        {isGalleryModalOpen && (
          <ModalPortal>
            <div
              className="fixed inset-0 flex items-center justify-center z-50"
            >
              <div
                className="bg-white rounded-2xl p-4 sm:p-8 max-w-2xl w-full mx-4 shadow-xl max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex justify-between items-center mb-6 sticky top-0 bg-white pt-2 z-10">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800">
                      {selectedGallery ? 'Modifier la galerie' : 'Créer une nouvelle galerie'}
                    </h3>
                    <p className="text-gray-600 mt-1">Ajoutez une galerie pour mettre en valeur vos réalisations</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsGalleryModalOpen(false)}
                    className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
                  >
                    <X className="h-6 w-6" />
                  </motion.button>
                </div>

                <div className="space-y-6 px-2 md:px-0">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Nom de la galerie *
                    </label>
                    <div className="space-y-1">
                      <input
                        type="text"
                        value={isEditingGallery ? tempGalleryName : galleryModalData.name}
                        onChange={(e) => {
                          const value = e.target.value.slice(0, 30);
                          isEditingGallery
                            ? setTempGalleryName(value)
                            : setGalleryModalData((prev: GalleryModalData) => ({ ...prev, name: value }));
                        }}
                        className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF965E] focus:border-transparent transition-all duration-200"
                        placeholder="Ex: Rénovation cuisine"
                        maxLength={30}
                      />
                      <div className="flex justify-end">
                        <span className="text-sm text-gray-500">
                          {(isEditingGallery ? tempGalleryName : galleryModalData.name).length}/30 caractères
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Description
                    </label>
                    <div className="space-y-1 relative">
                      <textarea
                        value={isEditingGallery ? tempGalleryDescription : galleryModalData.description}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (isEditingGallery) {
                            setTempGalleryDescription(value);
                          } else {
                            setGalleryModalData((prev: GalleryModalData) => ({ ...prev, description: value }));
                          }
                        }}
                        className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF965E] focus:border-transparent transition-all duration-200 resize-none h-32"
                        placeholder="Décrivez cette galerie..."
                        maxLength={2000}
                      />
                      <div className="flex justify-end mt-2">
                        <button
                          type="button"
                          className={`flex items-center gap-1 px-3 py-1.5 bg-[#FF6B2C] text-white rounded-lg shadow hover:bg-[#FF7A35] transition-colors text-xs font-medium ${((isEditingGallery ? tempGalleryName : galleryModalData.name).trim().length === 0) ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => {
                            const currentDescription = isEditingGallery ? tempGalleryDescription : galleryModalData.description;
                            setDescriptionToImprove(currentDescription);
                            setIsAiConfirmModalOpen(true);
                          }}
                          title={((isEditingGallery ? tempGalleryDescription : galleryModalData.description).trim().length === 0) ? "Générer une description avec l'IA" : "Améliorer la description avec l'IA"}
                          disabled={(isEditingGallery ? tempGalleryName : galleryModalData.name).trim().length === 0}
                        >
                          <Sparkles className="h-4 w-4 mr-1" />
                          {((isEditingGallery ? tempGalleryDescription : galleryModalData.description).trim().length === 0) ? 'Générer avec l\'IA' : 'Améliorer avec l\'IA'}
                        </button>
                      </div>
                      <div className="flex justify-end">
                        <span className={`text-sm ${((isEditingGallery ? tempGalleryDescription : galleryModalData.description).length > 300) ? 'text-red-500 font-bold' : 'text-gray-500'}`}
                        >
                          {(isEditingGallery ? tempGalleryDescription : galleryModalData.description).length}/300 caractères
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Image de couverture
                    </label>
                    <div
                      className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-200 border-dashed rounded-xl hover:border-[#FF6B2C] transition-all duration-200 bg-gray-50 group cursor-pointer relative overflow-hidden"
                      onClick={() => {
                        const input = document.getElementById('cover-image') as HTMLInputElement;
                        if (input && !input.disabled) {
                          input.click();
                        }
                      }}
                    >
                      <div className="space-y-2 text-center">
                        {(isEditingGallery ? (tempCoverImage ? previewUrl : selectedGallery?.cover_image) : galleryModalData.cover_image) && (
                          <div className="relative">
                            <img
                              src={isEditingGallery
                                ? (tempCoverImage ? previewUrl : selectedGallery?.cover_image) || DEFAULT_IMAGE_URL
                                : getImagePreviewUrl(galleryModalData.cover_image) || DEFAULT_IMAGE_URL}
                              alt="Aperçu"
                              className="max-h-48 rounded-lg object-cover mx-auto"
                            />
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                if (isEditingGallery) {
                                  setTempCoverImage(null);
                                  setShouldDeleteCoverImage(true);
                                  setSelectedGallery(prev => prev ? {...prev, cover_image: undefined} : null);
                                  setPreviewUrl(null);
                                } else {
                                  setGalleryModalData(prev => ({ ...prev, cover_image: undefined }));
                                }
                              }}
                              className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-red-50 transition-colors z-10"
                            >
                              <X className="h-4 w-4 text-red-500" />
                            </button>
                          </div>
                        )}
                        {!(isEditingGallery ? (tempCoverImage || selectedGallery?.cover_image) : galleryModalData.cover_image) && (
                          <>
                            <Upload className="mx-auto h-12 w-12 text-gray-400 group-hover:text-[#FF6B2C] transition-colors" />
                            <div className="flex text-sm text-gray-600">
                              <span className="relative rounded-md font-medium text-[#FF6B2C] hover:text-[#FF7A35]">
                                Télécharger une image
                              </span>
                            </div>
                            <p className="text-xs text-gray-500">PNG, JPG, WEBP jusqu'à 5MB</p>
                            <input
                              id="cover-image"
                              type="file"
                              accept="image/*"
                              className="sr-only"
                              onChange={async (e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  // Vérifier le type et la taille du fichier
                                  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
                                  if (!allowedTypes.includes(file.type)) {
                                    notify(`Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.`, 'error');
                                    return;
                                  }

                                  const maxSize = 5 * 1024 * 1024; // 5MB
                                  if (file.size > maxSize) {
                                    const fileSizeInMB = (file.size / 1024 / 1024).toFixed(2);
                                    notify(`La taille du fichier ne doit pas dépasser 5 MB. Votre fichier fait ${fileSizeInMB} MB.`, 'error');
                                    return;
                                  }

                                  setCompressionProgress(1);
                                  try {
                                    const compressed = await imageCompression(file, {
                                      maxSizeMB: 0.4,
                                      maxWidthOrHeight: 400,
                                      useWebWorker: true,
                                      onProgress: (progress) => {
                                        setCompressionProgress(Math.round(progress));
                                      }
                                    });
                                    setCompressionProgress(100);

                                    // Créer une URL pour la prévisualisation
                                    const objectUrl = URL.createObjectURL(compressed);
                                    setModerationPreviewUrl(objectUrl);
                                    setModerationFile(compressed);
                                    setIsModerationLoading(true);
                                    setIsModerationModalOpen(true);

                                    // Stocker si on est en mode édition ou création
                                    setIsCreatingGallery(!isEditingGallery);

                                    // Stocker l'ID de la galerie si on est en mode édition
                                    if (isEditingGallery && selectedGallery) {
                                      setModerationGalleryId(selectedGallery.id);
                                    }

                                    // Lancer la modération automatiquement avec un délai plus long
                                    if (compressed) {
                                      handleModerationWithFile(compressed);
                                    }

                                    // Réinitialiser le champ de fichier
                                    e.target.value = '';
                                  } catch (err) {
                                    notify(`Erreur lors de la compression de l'image`, 'error');
                                    setCompressionProgress(0);
                                  }
                                }
                              }}
                            />
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Barre de progression compression image */}
                  {compressionProgress > 0 && compressionProgress < 100 && (
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${compressionProgress}%` }} />
                      <div className="text-xs text-center mt-1 text-gray-500">Compression en cours… {compressionProgress}%</div>
                    </div>
                  )}

                  {/* Ajout du bouton de génération IA pour l'image de couverture */}
                  <div className="mt-4">
                    <AiGalleryImageGenerator
                      onImageGenerated={async (imageUrl, imageBase64) => {
                        // On récupère l'image générée par l'IA (URL), on la convertit en fichier pour l'upload
                        const response = await fetch(imageUrl);
                        const blob = await response.blob();
                        const file = new File([blob], `cover-image-ia-${Date.now()}.jpg`, { type: 'image/jpeg' });
                        // Lancer la modération locale comme pour un upload classique
                        setModerationPreviewUrl(URL.createObjectURL(file));
                        setModerationFile(file);
                        setIsModerationLoading(true);
                        setIsModerationModalOpen(true);
                        setIsCreatingGallery(!isEditingGallery);
                        if (isEditingGallery && selectedGallery) {
                          setModerationGalleryId(selectedGallery.id);
                        }
                        handleModerationWithFile(file);
                      }}
                      galleryId={isEditingGallery && selectedGallery ? selectedGallery.id : undefined}
                      galleryName={isEditingGallery ? tempGalleryName : galleryModalData.name}
                      galleryDescription={isEditingGallery ? tempGalleryDescription : galleryModalData.description}
                      className="w-full"
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-6 border-t">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        setIsGalleryModalOpen(false);
                        setSelectedGallery(null);
                        setGalleryModalData({ name: '', description: '' });
                        setTempGalleryName('');
                        setTempGalleryDescription('');
                        setTempCoverImage(null);
                        setPreviewUrl(null);
                        setIsEditingGallery(false);
                      }}
                      className="px-6 py-3 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors font-medium"
                    >
                      Annuler
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        if (selectedGallery) {
                          handleEditGallery(selectedGallery.id);
                        } else {
                          handleCreateGallery();
                        }
                      }}
                      disabled={
                        isModerationLoading ||
                        isGalleryLoading ||
                        (isEditingGallery ? !tempGalleryName.trim() : !galleryModalData.name.trim()) ||
                        (isEditingGallery ? tempGalleryDescription.length > 300 : galleryModalData.description.length > 300)
                      }
                      className={`px-8 py-3 bg-[#FF6B2C] text-white rounded-xl transition-all duration-200 font-medium
                        ${
                          isModerationLoading ||
                          isGalleryLoading ||
                          (isEditingGallery ? !tempGalleryName.trim() : !galleryModalData.name.trim()) ||
                          (isEditingGallery ? tempGalleryDescription.length > 300 : galleryModalData.description.length > 300)
                            ? 'opacity-50 cursor-not-allowed'
                            : 'hover:bg-[#FF7A35] hover:shadow-lg'
                        }`}
                    >
                      {isModerationLoading ? 'En cours de modération' : (selectedGallery ? 'Enregistrer' : 'Créer la galerie')}
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </ModalPortal>
        )}
      </AnimatePresence>

      {/* Modale de confirmation de suppression de la galerie */}
      {isConfirmingImageDeletion && (
        <ModalPortal containerId="delete-confirmation-modal">
          <div className="fixed inset-0 flex items-center justify-center z-[9999]">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Confirmer la suppression de la galerie</h3>
              <p className="text-gray-700 mb-4">
                Êtes-vous sûr de vouloir supprimer cette galerie ? Cette action est irréversible.
              </p>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setIsConfirmingImageDeletion(false);
                    setSelectedGalleryIdForDeletion(null);
                  }}
                  className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={() => {
                    if (selectedGalleryIdForDeletion) {
                      handleDeleteGalleryInternal(selectedGalleryIdForDeletion);
                    }
                  }}
                  className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                >
                  Confirmer
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modale de modification de la légende de l'image */}
      {isImageModalOpen && selectedImage && (
        <ModalPortal>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Modifier la légende de l'image</h3>
              <div className="space-y-4">
                <CaptionInput
                  value={tempImageCaption}
                  onChange={setTempImageCaption}
                  isTextArea={true}
                />
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      setIsImageModalOpen(false);
                      setSelectedImage(null);
                      setTempImageCaption('');
                      setPreviousImageCaption('');
                    }}
                    className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleConfirmImageCaptionChange}
                    className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                  >
                    Enregistrer
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modale de visualisation des photos */}
      <AnimatePresence>
        {isViewingPhotos && selectedGalleryForPhotos && (
          <ModalPortal>
            <motion.div
              className="fixed inset-0 flex items-center justify-center z-50"
              onClick={() => {
                setIsViewingPhotos(false);
                setSelectedGalleryForPhotos(null);
              }}
            >
              <div
                className="w-full max-w-[1920px] mx-auto p-4 h-[90vh] flex flex-col"
                onClick={(e) => e.stopPropagation()}
              >
                <motion.div
                  className="relative flex flex-col bg-white rounded-2xl shadow-lg h-full overflow-hidden"
                >
                  {/* Header fixe */}
                  <div className="sticky top-0 p-6 border-b border-gray-100 bg-white rounded-t-2xl z-20">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4 overflow-hidden">
                        <div className="p-3 bg-[#FFF8F3] rounded-xl flex-shrink-0">
                          <Camera className="h-7 w-7 text-[#FF6B2C]" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h2 className="text-2xl font-bold text-gray-800 truncate">
                            {profil?.galleryFolders.find(g => g.id === selectedGalleryForPhotos)?.name}
                          </h2>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-[#FF6B2C] flex-shrink-0">•</span>
                            <span className="text-gray-500 line-clamp-2">
                              {profil?.galleryFolders.find(g => g.id === selectedGalleryForPhotos)?.description || 'Aucune description'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 flex-shrink-0 ml-4">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            setIsViewingPhotos(false);
                            setSelectedGalleryForPhotos(null);
                          }}
                          className="p-2.5 bg-white/10 hover:bg-[#FF6B2C]/10 text-[#FF6B2C] rounded-xl transition-all duration-300 border border-[#FF6B2C]/20"
                        >
                          <X className="h-5 w-5" />
                        </motion.button>
                      </div>
                    </div>
                  </div>

                  {/* Contenu scrollable */}
                  <div className="flex-1 overflow-y-auto">
                    <GalleryPhotos
                      galleryId={selectedGalleryForPhotos}
                      galleryName={profil?.galleryFolders.find(g => g.id === selectedGalleryForPhotos)?.name || ''}
                      galleryDescription={profil?.galleryFolders.find(g => g.id === selectedGalleryForPhotos)?.description || ''}
                      onClose={() => {
                        setIsViewingPhotos(false);
                        setSelectedGalleryForPhotos(null);
                      }}
                      onPhotosChange={loadGalleries}
                      onUpdateProfil={onUpdateProfil}
                      handleAddPhotoToGallery={handleAddPhotoToGallery}
                      handleRemovePhotoFromGallery={handleRemovePhotoFromGallery}
                    />
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>

      {/* Modal de modération d'image */}
      {isModerationModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay avec gestion du clic */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => {
                // Toujours permettre l'annulation, même pendant le chargement
                setIsModerationModalOpen(false);
                if (moderationPreviewUrl) {
                  URL.revokeObjectURL(moderationPreviewUrl);
                  setModerationPreviewUrl(null);
                }
                setModerationFile(null);
                setIsImageRejected(false);
                setRejectionDescription(undefined);
              }}
              aria-label="Fermer la modale"
            />
            {/* Contenu de la modale */}
            <div
              className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-4 pt-5 flex justify-between items-center border-b">
                <h3 className="text-lg font-semibold">
                  {isModerationLoading
                    ? "Analyse de sécurité en cours"
                    : isImageRejected
                      ? "Image refusée"
                      : "Modération de l'image"}
                </h3>
                <button
                  onClick={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setModerationFile(null);
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-full"
                  aria-label="Fermer"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              {/* Contenu scrollable */}
              <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
                {isModerationLoading || (!isModerationLoading && !isImageRejected) ? (
                  <ImageModerationStatus
                    isLoading={isModerationLoading}
                    imageUrl={moderationPreviewUrl || undefined}
                    title={isModerationLoading ? "Analyse de sécurité en cours" : "Vérification de l'image"}
                    onCancel={() => {
                      // Toujours permettre l'annulation, même pendant le chargement
                      setIsModerationModalOpen(false);
                      if (moderationPreviewUrl) {
                        URL.revokeObjectURL(moderationPreviewUrl);
                        setModerationPreviewUrl(null);
                      }
                      setModerationFile(null);
                      setIsImageRejected(false);
                      setRejectionDescription(undefined);
                    }}
                  />
                ) : isImageRejected ? (
                  <div className="p-6">
                    {/* Afficher l'image refusée */}
                    {moderationPreviewUrl && (
                      <div className="mb-6 flex justify-center">
                        <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                          <img
                            src={moderationPreviewUrl}
                            alt="Image refusée"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-red-900/20"></div>
                        </div>
                      </div>
                    )}

                    {/* Message de rejet détaillé */}
                    <RejectedImageMessage
                      contentType="gallery_cover"
                      description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                      improvementSuggestions={rejectionImprovementSuggestions}
                      variant="detailed"
                    />

                    {/* Bouton pour réessayer */}
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => {
                          setIsModerationModalOpen(false);
                          if (moderationPreviewUrl) {
                            URL.revokeObjectURL(moderationPreviewUrl);
                            setModerationPreviewUrl(null);
                          }
                          setModerationFile(null);
                          setIsImageRejected(false);
                          setRejectionDescription(undefined);
                          setRejectionImprovementSuggestions(undefined);

                          // Ouvrir automatiquement le sélecteur de fichier
                          setTimeout(() => {
                            const input = document.getElementById('cover-image') as HTMLInputElement;
                            if (input) {
                              input.click();
                            }
                          }, 300);
                        }}
                        className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                      >
                        Choisir une autre image
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <ImageModerationStatus
                      isLoading={false}
                      imageUrl={moderationPreviewUrl || undefined}
                      title="Modération de l'image"
                      onCancel={() => {
                        setIsModerationModalOpen(false);
                        if (moderationPreviewUrl) {
                          URL.revokeObjectURL(moderationPreviewUrl);
                          setModerationPreviewUrl(null);
                        }
                        setModerationFile(null);
                      }}
                    />
                    <div className="flex justify-center mt-6">
                      <p className="text-gray-600 text-sm">
                        Vérification automatique en cours...
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="gallery_description"
          prompt={`
            Génère une description professionnelle, engageante et concise (200-300 caractères max) pour une galerie photo.
            Nom de la galerie : ${(isEditingGallery ? tempGalleryName : galleryModalData.name).trim()}
            Description actuelle : ${descriptionToImprove.trim()}
            Instructions :
            1. Mets en avant l'originalité, la qualité ou l'ambiance de la galerie
            2. Pas de HTML, pas de markdown, pas de liste
            3. Utilise un ton humain, positif et inspirant
            4. Si la description est vide, crée une description à partir du nom de la galerie uniquement
          `}
          onComplete={handleAiGenerationComplete}
          onCancel={() => setIsAiConfirmModalOpen(false)}
        />
      )}
    </div>
  );
};

export default GalleryRealizations;