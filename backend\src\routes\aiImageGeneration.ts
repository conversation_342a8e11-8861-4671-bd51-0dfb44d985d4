import { Router } from 'express';
import { generateImageWithAI, confirmGeneratedImage } from '../controllers/aiImageGenerationController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';
import { checkAiConsent } from '../middleware/aiConsentMiddleware';

const router = Router();

// Rate limiter pour les requêtes de génération d'images IA
const aiImageGenerationLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes de génération d\'images IA. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour générer une image
router.post('/generate', aiImageGenerationLimiter, checkAiConsent, asyncHandler(generateImageWithAI));

// Route pour confirmer et déplacer une image générée
router.post('/confirm', aiImageGenerationLimiter, checkAiConsent, asyncHandler(confirmGeneratedImage));

export default router;
