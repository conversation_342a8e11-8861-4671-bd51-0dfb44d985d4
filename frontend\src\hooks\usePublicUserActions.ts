/* Sert à récupérer les actions publiques d'un utilisateur depuis l'API et la base de données supabase : user_actions_history */

import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';

export interface PublicUserAction {
  id: string;
  action_type: string;
  action_date: string;
  resource_type: string;
  details: any;
}

interface UsePublicUserActionsProps {
  userId: string;
  enabled?: boolean;
}

interface UsePublicUserActionsReturn {
  actions: PublicUserAction[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const usePublicUserActions = ({ userId, enabled = true }: UsePublicUserActionsProps): UsePublicUserActionsReturn => {
  const [actions, setActions] = useState<PublicUserAction[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserActions = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/public-actions/${userId}`, {
        headers,
        withCredentials: true
      });
      
      if (response.data.success) {
        setActions(response.data.data || []);
      } else {
        setError('Impossible de récupérer les activités récentes');
      }
    } catch (err) {
      logger.error('Erreur lors de la récupération des activités récentes:', err);
      setError('Erreur lors du chargement des activités récentes');
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  useEffect(() => {
    if (enabled && userId) {
      fetchUserActions();
    }
  }, [fetchUserActions, userId, enabled]);
  
  return {
    actions,
    loading,
    error,
    refetch: fetchUserActions
  };
}; 