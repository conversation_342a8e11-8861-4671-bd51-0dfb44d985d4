import React, { useState, useEffect, useRef, useCallback } from 'react';
import ModalPortal from "@/components/ModalPortal";
import { motion } from "framer-motion";
import DOMPurify from "dompurify";
import { Review } from '@/hooks/useReviews';
import { IconButton, Button, ToggleButtonGroup, ToggleButton } from '@mui/material';
import { Star, Calendar, Briefcase, X, InfoIcon, Edit, Euro, ChevronRight, Coins, ArrowRightLeft, Trash2, Filter } from 'lucide-react';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { notify } from '@/components/Notification';
import logger from '@/utils/logger';
import ReviewModal from '@/components/ReviewModalPopupDepotAvis';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '@/pages/dashboard/services/types';

interface ModalReviewProps {
  isOpen: boolean;
  onClose: () => void;
  profilId?: string;
  onReviewSubmitted?: () => void;
}

interface Mission {
  id: string;
  titre: string;
  description: string;
  date: string;
  montant: number;
  payment_method: string;
  hasReview: boolean;
  reviewId?: string;
  category: string;
  subcategory?: string;
  author?: {
    nom: string;
    prenom: string;
    photo_url: string;
  };
}

const ModalReview: React.FC<ModalReviewProps> = ({ isOpen, onClose, profilId, onReviewSubmitted }) => {
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [missionsPerPage] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMissionId, setSelectedMissionId] = useState<string | null>(null);
  const [isMissionSelectModalOpen, setIsMissionSelectModalOpen] = useState(false);
  const [acceptedMissions, setAcceptedMissions] = useState<Mission[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDeleteConfirmationOpen, setIsDeleteConfirmationOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);
  const [filterValue, setFilterValue] = useState<'all' | 'no_review'>('all');

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Date non définie';

    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = { 
      month: 'long' as const, 
      year: 'numeric' as const 
    };
    const formattedDate = date.toLocaleString('fr-FR', options);

    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1).toLowerCase();
  };

  const handleEditReview = async (reviewId: string) => {
    try {
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}`,
        { 
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );
      if (response.data.success) {
        // Conversion des données de l'API au format Review
        const reviewData = response.data.review;
        setSelectedReview(reviewData);
        setSelectedMissionId(reviewData.mission_id);
        setIsReviewModalOpen(true);
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'avis:', error);
      notify('Erreur lors de la récupération de l\'avis', 'error');
    }
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setSelectedReview(null);
  };

  const handleScroll = useCallback(() => {
    if (!containerRef.current || isLoading || !hasMore) {
      return;
    }

    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;

    // Si on est proche du bas (100px avant la fin) et qu'on n'est pas en train de charger
    if (distanceToBottom < 100 && !isLoading && hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  }, [isLoading, hasMore, currentPage, acceptedMissions.length, missionsPerPage]);

  // Ajout d'un debounce pour le scroll
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let scrollTimeout: NodeJS.Timeout;
    const debouncedScroll = () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => handleScroll(), 100);
    };

    container.addEventListener('scroll', debouncedScroll);
    return () => {
      container.removeEventListener('scroll', debouncedScroll);
      clearTimeout(scrollTimeout);
    };
  }, [handleScroll]);

  const fetchAcceptedCandidatures = async () => {
    try {
      setIsLoading(true);

      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/missions/propositions/sent`,
        { 
          headers: await getCommonHeaders(),
          withCredentials: true,
          params: {
            offer_status: 'acceptée',
            page: currentPage,
            limit: missionsPerPage
          }
        }
      );

      if (response.data.data && response.data.data.length > 0) {
        // Si un profilId est fourni, on filtre les missions pour cet utilisateur
        // Sinon, on prend toutes les missions
        let missionsToProcess = profilId 
          ? response.data.data.filter((proposal: any) => proposal.mission.user_id === profilId)
          : response.data.data;

        if (missionsToProcess.length > 0) {
          // Récupérer les headers une seule fois
          const headers = await getCommonHeaders();

          // Récupérer les avis existants pour chaque mission individuellement
          const reviewPromises = missionsToProcess.map((m: any) => 
            axios.get(
              `${API_CONFIG.baseURL}/api/reviews/check/${m.mission_id}`,
              { 
                headers,
                withCredentials: true
              }
            ).catch(() => ({ data: { success: false, exists: false, reviewId: null } }))
          );

          const reviewsResponses = await Promise.all(reviewPromises);
          
          // Créer un Map des avis existants avec leur mission_id
          const reviewsMap = new Map<string, any>();
          reviewsResponses.forEach((response, index) => {
            const missionId = missionsToProcess[index].mission_id;
            if (response.data.success && response.data.exists) {
              reviewsMap.set(missionId, { id: response.data.reviewId });
            }
          });

          const missions = missionsToProcess.map((proposal: any) => {
            const existingReview = reviewsMap.get(proposal.mission_id);
            const category = SERVICE_CATEGORIES.find(cat => cat.id === proposal.mission.category_id);
            const subcategory = SERVICE_SUBCATEGORIES.find(subcat => subcat.id === proposal.mission.subcategory_id);
            
            return {
              id: proposal.mission_id,
              titre: proposal.mission.titre,
              description: proposal.mission.description,
              date: proposal.date_acceptation,
              montant: proposal.montant_propose,
              payment_method: proposal.payment_method,
              category: category?.nom || 'Non catégorisée',
              subcategory: subcategory?.nom,
              hasReview: !!existingReview,
              reviewId: existingReview?.id,
              author: proposal.mission.user_profile ? {
                nom: proposal.mission.user_profile.nom,
                prenom: proposal.mission.user_profile.prenom,
                photo_url: proposal.mission.user_profile.photo_url
              } : undefined
            };
          });

          // Ajouter les nouvelles missions aux missions existantes
          setAcceptedMissions(prev => 
            currentPage === 1 ? missions : [...prev, ...missions]
          );
          
          // Mettre à jour hasMore seulement si on a reçu moins de missions que demandé
          setHasMore(missionsToProcess.length >= missionsPerPage);
          setIsMissionSelectModalOpen(true);
        } else {
          setHasMore(false);
          if (currentPage === 1) {
            notify(profilId 
              ? "Vous n'avez pas encore de missions acceptées à évaluer pour cet utilisateur." 
              : "Vous n'avez pas encore de missions acceptées à évaluer.", 
              "info"
            );
          }
        }
      } else {
        setHasMore(false);
        if (currentPage === 1) {
          notify(profilId 
            ? "Vous n'avez pas encore de missions acceptées à évaluer pour cet utilisateur." 
            : "Vous n'avez pas encore de missions acceptées à évaluer.", 
            "info"
          );
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      notify('Erreur lors de la récupération des missions', 'error');
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenReviewModal = (missionId: string) => {
    setSelectedMissionId(missionId);
    setIsMissionSelectModalOpen(false);
    setIsReviewModalOpen(true);
  };

  const handleReviewSubmitted = () => {
    fetchAcceptedCandidatures();
    if (onReviewSubmitted) {
      onReviewSubmitted();
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    try {
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}`,
        {
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Avis supprimé avec succès', 'success');
        fetchAcceptedCandidatures();
        if (onReviewSubmitted) {
          onReviewSubmitted();
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression de l\'avis:', error);
      notify('Erreur lors de la suppression de l\'avis', 'error');
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchAcceptedCandidatures();
    }
  }, [isOpen, currentPage]);

  // Filtrer les missions en fonction du filtre sélectionné
  const filteredMissions = acceptedMissions.filter(mission => 
    filterValue === 'all' || (filterValue === 'no_review' && !mission.hasReview)
  );

  return (
    <>
      <ModalPortal isOpen={isOpen} onBackdropClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-[95%] sm:w-full max-w-[600px] relative max-h-[90vh] sm:max-h-[85vh] flex flex-col overflow-hidden mx-auto"
        >
          {/* En-tête */}
          <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-100">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="p-2 bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] rounded-lg shadow-sm">
                <Star className="h-4 w-4 sm:h-5 sm:w-5 text-[#FF6B2C]" />
              </div>
              <div>
                <h2 className="text-lg sm:text-xl font-bold text-gray-800">Sélectionnez une mission à évaluer</h2>
                <p className="text-[11px] sm:text-xs text-gray-500">Choisissez la mission sur laquelle déposer un avis</p>
              </div>
            </div>
            <IconButton
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 rounded-full p-1 sm:p-2"
            >
              <X size={18} className="sm:w-5 sm:h-5" />
            </IconButton>
          </div>

          {/* Zone scrollable incluant la section d'information et le contenu */}
          <div 
            ref={containerRef}
            className="flex-1 overflow-y-auto"
          >
            {/* Section d'information */}
            <div className="bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] px-4 sm:px-6 py-4 border-b border-[#FFE4BA]">
              <div className="flex items-center space-x-2 mb-3">
                <div className="p-1.5 bg-white rounded-lg shadow-sm">
                  <InfoIcon className="h-3.5 h-3.5 sm:h-4 sm:w-4 text-[#FF6B2C]" />
                </div>
                <h6 className="text-sm sm:text-base font-semibold text-gray-800">Comment ça fonctionne ?</h6>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-[#FF6B2C] text-white flex items-center justify-center text-[10px] sm:text-xs font-semibold">1</div>
                  <p className="text-xs sm:text-sm text-gray-700">Sélectionnez une mission</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-[#FF6B2C] text-white flex items-center justify-center text-[10px] sm:text-xs font-semibold">2</div>
                  <p className="text-xs sm:text-sm text-gray-700">Donnez une note sur 5</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-[#FF6B2C] text-white flex items-center justify-center text-[10px] sm:text-xs font-semibold">3</div>
                  <p className="text-xs sm:text-sm text-gray-700">Sélectionnez les qualités</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-[#FF6B2C] text-white flex items-center justify-center text-[10px] sm:text-xs font-semibold">4</div>
                  <p className="text-xs sm:text-sm text-gray-700">Rédigez un commentaire</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-[#FF6B2C] text-white flex items-center justify-center text-[10px] sm:text-xs font-semibold">5</div>
                  <p className="text-xs sm:text-sm text-gray-700">Recevez 1 Jobi par avis</p>
                </div>
              </div>
            </div>

            {/* Filtre */}
            <div className="px-4 sm:px-6 py-3 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Filtrer :</span>
                </div>
                <ToggleButtonGroup
                  value={filterValue}
                  exclusive
                  onChange={(e, newValue) => newValue && setFilterValue(newValue)}
                  size="small"
                  className="!rounded-lg"
                >
                  <ToggleButton 
                    value="all"
                    className={`!px-3 !py-1 !text-xs !rounded-l-lg ${
                      filterValue === 'all' 
                        ? '!bg-[#FF6B2C] !text-white' 
                        : '!text-gray-600'
                    }`}
                  >
                    Toutes les missions
                  </ToggleButton>
                  <ToggleButton 
                    value="no_review"
                    className={`!px-3 !py-1 !text-xs !rounded-r-lg ${
                      filterValue === 'no_review' 
                        ? '!bg-[#FF6B2C] !text-white' 
                        : '!text-gray-600'
                    }`}
                  >
                    Sans avis
                  </ToggleButton>
                </ToggleButtonGroup>
              </div>
            </div>

            {/* Contenu des missions */}
            <div className="px-4 sm:px-6 py-4 space-y-3 sm:space-y-4">
              {filteredMissions.length === 0 && !isLoading ? (
                <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
                  <Briefcase className="w-12 h-12 sm:w-16 sm:h-16 text-gray-300 mb-4" />
                  <h3 className="text-base sm:text-lg font-semibold text-gray-700 mb-2">
                    Aucune mission à évaluer
                  </h3>
                  <p className="text-sm text-gray-500 max-w-[280px] sm:max-w-[320px]">
                    Vous n'avez pas encore de missions terminées avec ce jobeur.
                  </p>
                </div>
              ) : (
                filteredMissions.map((mission, index) => (
                  <motion.div
                    key={mission.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                    className={`${
                      mission.hasReview 
                        ? 'bg-[#FFF8F3] border-[#FFE4BA]' 
                        : index % 2 === 0 
                          ? 'bg-white border-gray-100' 
                          : 'bg-gray-50 border-gray-100'
                    } border rounded-xl p-4 sm:p-5 hover:border-[#FFE4BA] transition-all duration-200 cursor-pointer group relative`}
                    onClick={() => !mission.hasReview && handleOpenReviewModal(mission.id)}
                  >
                    {mission.hasReview && (
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] text-white px-2 sm:px-3 py-1 rounded-full text-[10px] sm:text-xs font-bold shadow-lg flex items-center gap-1 border border-white/20 backdrop-blur-sm">
                        <Star size={10} className="sm:w-3 sm:h-3" color="white" />
                        Avis publié
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          {mission.author && (
                            <>
                              <div className="relative w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden border-2 border-[#FFE4BA]">
                                <img 
                                  src={mission.author.photo_url || '/default-avatar.png'} 
                                  alt={`${mission.author.prenom} ${mission.author.nom}`}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <div className="flex flex-col">
                                <span className="text-sm text-gray-600">
                                  {mission.author.prenom} {mission.author.nom.charAt(0)}.
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                        <h3 className="text-base sm:text-lg font-semibold text-gray-800 truncate mb-1 group-hover:text-[#FF6B2C] transition-colors duration-200">
                          {mission.titre}
                        </h3>
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {mission.description.length > 70 
                            ? `${DOMPurify.sanitize(mission.description, { ALLOWED_TAGS: [] }).substring(0, 70)}...` 
                            : DOMPurify.sanitize(mission.description, { ALLOWED_TAGS: [] })}
                        </p>
                        <div className="flex flex-wrap gap-2 sm:gap-3 text-xs sm:text-sm">
                          <span className="inline-flex items-center text-gray-600">
                            <Calendar className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1" />
                            {formatDate(mission.date)}
                          </span>
                          <span className="inline-flex items-center text-gray-600">
                            {mission.payment_method === 'jobi_only' ? (
                              <Coins className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1" />
                            ) : mission.payment_method === 'direct_only' ? (
                              <Euro className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1" />
                            ) : (
                              <ArrowRightLeft className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1" />
                            )}
                            {mission.payment_method === 'jobi_only' ? (
                              `${mission.montant} Jobi`
                            ) : mission.payment_method === 'direct_only' ? (
                              `${mission.montant} €`
                            ) : (
                              `${mission.montant} € (Paiement hybride)`
                            )}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 sm:gap-3">
                        {mission.hasReview ? (
                          <div className="flex items-center gap-2">
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditReview(mission.reviewId!);
                              }}
                              className="!p-2 !text-[#FF6B2C] hover:!bg-[#FFF8F3] border border-[#FF6B2C] rounded-lg"
                            >
                              <Edit className="w-4 h-4 sm:w-[18px] sm:h-[18px]" />
                            </IconButton>
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setReviewToDelete(mission.reviewId!);
                                setIsDeleteConfirmationOpen(true);
                              }}
                              className="!p-2 !text-red-500 hover:!bg-red-50 border border-red-500 rounded-lg"
                            >
                              <Trash2 className="w-4 h-4 sm:w-[18px] sm:h-[18px]" />
                            </IconButton>
                          </div>
                        ) : (
                          <Button
                            variant="contained"
                            className="!min-w-0 !px-3 sm:!px-4 !py-1.5 !text-xs sm:!text-sm !rounded-lg !bg-[#FF6B2C] hover:!bg-[#FF965E]"
                            endIcon={<ChevronRight className="w-3.5 h-3.5 sm:w-4 sm:h-4" />}
                          >
                            Évaluer
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                        {mission.category}
                      </span>
                      {mission.subcategory && (
                        <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          {mission.subcategory}
                        </span>
                      )}
                    </div>
                  </motion.div>
                ))
              )}

              {isLoading && (
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 sm:h-10 sm:w-10 border-b-2 border-[#FF6B2C]"></div>
                </div>
              )}
            </div>
          </div>

          {/* Pied de page */}
          <div className="p-4 sm:p-6 border-t border-gray-100">
            <Button
              onClick={onClose}
              className="w-full !bg-[#FF6B2C] !text-white hover:!bg-[#FF965E] !rounded-xl !py-2.5 !text-sm sm:!text-base !font-semibold !transition-all !duration-200"
            >
              Fermer
            </Button>
          </div>
        </motion.div>
      </ModalPortal>

      {/* Modal de confirmation de suppression */}
      <ModalPortal isOpen={isDeleteConfirmationOpen} onBackdropClick={() => setIsDeleteConfirmationOpen(false)}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-[95%] sm:w-full max-w-[400px] p-4 sm:p-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <Trash2 className="w-5 h-5 text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Confirmer la suppression</h3>
          </div>

          <p className="text-sm text-gray-600 mb-4">
            Êtes-vous sûr de vouloir supprimer cet avis ? Cette action est irréversible et vous perdrez 1 Jobi.
          </p>

          <div className="flex items-center justify-end gap-3">
            <Button
              onClick={() => setIsDeleteConfirmationOpen(false)}
              variant="outlined"
              className="!text-gray-600 !border-gray-300 hover:!bg-gray-50"
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                if (reviewToDelete) {
                  handleDeleteReview(reviewToDelete);
                  setIsDeleteConfirmationOpen(false);
                  setReviewToDelete(null);
                }
              }}
              variant="contained"
              className="!bg-red-500 !text-white hover:!bg-red-600"
              startIcon={<Trash2 className="w-4 h-4" />}
            >
              Supprimer l'avis
            </Button>
          </div>
        </motion.div>
      </ModalPortal>

      <ReviewModal
        isOpen={isReviewModalOpen}
        onClose={handleCloseReviewModal}
        userId={profilId ?? ''}
        mission_id={selectedMissionId || ''}
        onReviewAdded={handleReviewSubmitted}
        reviewToEdit={selectedReview ? {
          id: selectedReview.id,
          note: selectedReview.note,
          commentaire: selectedReview.commentaire,
          qualites: selectedReview.qualites || [],
          defauts: selectedReview.defauts || []
        } : undefined}
      />
    </>
  );
};

export default ModalReview;