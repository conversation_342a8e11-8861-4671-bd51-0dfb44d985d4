import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L, { LatLngTuple } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Stack,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  LocationOn,
  Star,
  Verified,
  Close,
  ZoomIn,
  ZoomOut,
  MyLocation
} from '@mui/icons-material';

// Couleurs JobPartiel
const COLORS = {
  primary: '#FF6B2C',
  secondary: '#FF7A35',
  white: '#FFFFFF',
  neutral: '#64748B',
  success: '#4CAF50',
  warning: '#FFC107'
};

interface Provider {
  user_id: string;
  slug: string;
  nom: string;
  prenom: string;
  ville: string;
  code_postal: string;
  bio: string;
  photo_url: string;
  slogan: string;
  updated_at: string;
  intervention_zone?: {
    center: [number, number];
    radius: number;
    adresse?: string;
    france_entiere?: boolean;
  };
  users: Array<{
    user_type: string;
    profil_actif: boolean;
    profil_verifier: boolean;
    user_services: Array<{
      id: string;
      titre: string;
      description: string;
      category_id: string;
      subcategory_id: string;
      tarif_horaire: number;
      statut: string;
    }>;
    user_reviews?: Array<{
      id: string;
      note: number;
      commentaire: string;
      date_creation: string;
      statut: string;
    }>;
  }>;
}

interface SearchResultsMapProps {
  providers: Provider[];
  onProviderClick: (provider: Provider) => void;
  isVisible: boolean;
  onClose: () => void;
}

// Fonction pour ajouter du bruit aléatoire aux coordonnées (protection RGPD - 30 mètres)
const addLocationNoise = (lat: number, lng: number): [number, number] => {
  // Convertir 30 mètres en degrés (approximativement)
  // 1 degré ≈ 111 km, donc 30m ≈ 0.00027 degrés
  const noiseRange = 0.00027;
  const noiseLat = lat + (Math.random() - 0.5) * 2 * noiseRange;
  const noiseLng = lng + (Math.random() - 0.5) * 2 * noiseRange;
  return [noiseLat, noiseLng];
};

// Fonction pour extraire les coordonnées depuis intervention_zone avec bruit RGPD
const getProviderCoordinates = (provider: Provider): [number, number] | null => {
  if (provider.intervention_zone?.center && Array.isArray(provider.intervention_zone.center)) {
    const [lat, lng] = provider.intervention_zone.center;
    if (typeof lat === 'number' && typeof lng === 'number') {
      // Exclure les coordonnées par défaut de Paris (48.8566, 2.3522)
      if (Math.abs(lat - 48.8566) < 0.0001 && Math.abs(lng - 2.3522) < 0.0001) {
        return null;
      }
      // Ajouter du bruit pour respecter le RGPD
      return addLocationNoise(lat, lng);
    }
  }
  return null;
};

// Composant pour mettre à jour la vue de la carte
const MapUpdater: React.FC<{ bounds: L.LatLngBounds | null }> = ({ bounds }) => {
  const map = useMap();

  useEffect(() => {
    if (bounds && bounds.isValid()) {
      map.fitBounds(bounds, { padding: [20, 20] });
    }
  }, [bounds, map]);

  return null;
};

// Icône personnalisée pour les marqueurs
const createCustomIcon = (photoUrl?: string, isVerified?: boolean) => {
  const iconHtml = photoUrl
    ? `<div style="
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid ${COLORS.primary};
        background-image: url('${photoUrl}');
        background-size: cover;
        background-position: center;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        ${isVerified ? `<div style="
          position: absolute;
          top: -5px;
          right: -5px;
          width: 16px;
          height: 16px;
          background: ${COLORS.success};
          border-radius: 50%;
          border: 2px solid white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: white;
        ">✓</div>` : ''}
      </div>`
    : `<div style="
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid ${COLORS.primary};
        background: ${COLORS.primary};
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        ${photoUrl ? '' : '👤'}
        ${isVerified ? `<div style="
          position: absolute;
          top: -5px;
          right: -5px;
          width: 16px;
          height: 16px;
          background: ${COLORS.success};
          border-radius: 50%;
          border: 2px solid white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: white;
        ">✓</div>` : ''}
      </div>`;

  return L.divIcon({
    html: iconHtml,
    className: 'custom-marker',
    iconSize: [40, 40],
    iconAnchor: [20, 20],
    popupAnchor: [0, -20]
  });
};

const SearchResultsMap: React.FC<SearchResultsMapProps> = ({
  providers,
  onProviderClick,
  isVisible,
  onClose
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [providersWithCoords, setProvidersWithCoords] = useState<Array<Provider & { coords: [number, number] }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [bounds, setBounds] = useState<L.LatLngBounds | null>(null);
  const mapRef = useRef<L.Map | null>(null);

  // Traiter les providers avec leurs coordonnées depuis intervention_zone
  useEffect(() => {
    if (!isVisible || providers.length === 0) return;

    const processProviders = () => {
      setIsLoading(true);
      const providersWithCoords: Array<Provider & { coords: [number, number] }> = [];

      for (const provider of providers) {
        const coords = getProviderCoordinates(provider);
        if (coords) {
          providersWithCoords.push({ ...provider, coords });
        }
      }

      setProvidersWithCoords(providersWithCoords);

      // Calculer les bounds
      if (providersWithCoords.length > 0) {
        const latLngs = providersWithCoords.map(p => L.latLng(p.coords[0], p.coords[1]));
        const newBounds = L.latLngBounds(latLngs);
        setBounds(newBounds);
      }

      setIsLoading(false);
    };

    processProviders();
  }, [providers, isVisible]);

  if (!isVisible) return null;

  return (
    <Paper
      elevation={8}
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        bgcolor: COLORS.white
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: `1px solid ${COLORS.neutral}20`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          bgcolor: COLORS.primary,
          color: COLORS.white
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Carte des jobbeurs ({providersWithCoords.length}).
          </Typography>
          <Typography variant="caption" sx={{ color: COLORS.white }}>
            Afin de respecter la vie privée des jobbeurs, la zone affichée est approximative.
          </Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: COLORS.white }}>
          <Close />
        </IconButton>
      </Box>

      {/* Map Container */}
      <Box sx={{ flex: 1, position: 'relative' }}>
        {isLoading ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: 2
            }}
          >
            <Typography variant="h6" color="text.secondary">
              Chargement de la carte...
            </Typography>
          </Box>
        ) : providersWithCoords.length > 0 ? (
          <div style={{ height: '100%', width: '100%' }}>
            <MapContainer
              center={[46.603354, 1.888334] as LatLngTuple} // Centre de la France
              zoom={6}
              style={{ height: '100%', width: '100%' }}
              zoomControl={false}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; OpenStreetMap contributors'
              />
              <MapUpdater bounds={bounds} />

              {providersWithCoords.map((provider) => {
                const avgRating = provider.users[0]?.user_reviews?.length
                  ? provider.users[0].user_reviews.reduce((acc, review) => acc + review.note, 0) / provider.users[0].user_reviews.length
                  : 0;

                return (
                  <Marker
                    key={provider.user_id}
                    position={provider.coords}
                    icon={createCustomIcon(provider.photo_url, provider.users[0]?.profil_verifier)}
                  >
                  <Popup>
                    <Box sx={{ p: 1, minWidth: 200 }}>
                      <Stack spacing={1}>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Avatar
                            src={provider.photo_url}
                            sx={{ width: 40, height: 40 }}
                          >
                            {provider.prenom.charAt(0)}{provider.nom.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {provider.prenom} {provider.nom.charAt(0)}.
                            </Typography>
                            <Box display="flex" alignItems="center" gap={0.5}>
                              <LocationOn sx={{ fontSize: 14, color: COLORS.neutral }} />
                              <Typography variant="caption" color="text.secondary">
                                {provider.ville}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        {avgRating > 0 && (
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <Star sx={{ fontSize: 16, color: COLORS.warning }} />
                            <Typography variant="caption">
                              {avgRating.toFixed(1)} ({provider.users[0].user_reviews?.length} avis)
                            </Typography>
                          </Box>
                        )}

                        {provider.slogan && (
                          <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                            "{provider.slogan}"
                          </Typography>
                        )}

                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                          {provider.users[0]?.user_services?.slice(0, 2).map((service, index) => (
                            <Chip
                              key={index}
                              label={service.titre}
                              size="small"
                              sx={{
                                fontSize: '0.7rem',
                                height: 20,
                                bgcolor: `${COLORS.primary}15`,
                                color: COLORS.primary
                              }}
                            />
                          ))}
                        </Stack>

                        <Button
                          variant="contained"
                          size="small"
                          onClick={() => onProviderClick(provider)}
                          sx={{
                            bgcolor: COLORS.primary,
                            '&:hover': { bgcolor: COLORS.secondary }
                          }}
                        >
                          Voir le profil
                        </Button>
                      </Stack>
                    </Box>
                  </Popup>
                </Marker>
              );
            })}
            </MapContainer>
          </div>
        ) : (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: 2
            }}
          >
            <Typography variant="h6" color="text.secondary">
              Aucun jobbeur à afficher sur la carte
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default SearchResultsMap;
