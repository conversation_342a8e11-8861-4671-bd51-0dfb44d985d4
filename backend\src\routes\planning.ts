import { Router, Request as ExpressRequest, Response } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { supabase } from '../config/supabase';
import { z } from 'zod';
import { validateRequest } from '../middleware/validation';
import logger from '../utils/logger';
import rateLimit from 'express-rate-limit';
import { redis } from '../config/redis';
import { getUserSubscriptionLimits } from './configSubscriptions'; // Importer la fonction
import { decryptProfilDataAsync } from '../utils/encryption';

// Constantes pour le cache
const CACHE_PREFIX = 'planning:';
const CACHE_DURATION = 30 * 60; // 30 minutes en secondes

// Service de cache pour le planning
const planningCacheService = {
  // Récupère les missions du planning à partir du cache
  getPlanningItems: async (userId: string, month?: number, year?: number, yearOnly?: boolean): Promise<any[] | null> => {
    const cacheKey = `${CACHE_PREFIX}${userId}:${year || ''}:${month || ''}:${yearOnly ? 'yearOnly' : ''}`;
    const cached = await redis.get(cacheKey);
    if (cached) {
      // logger.info('Cache hit pour le planning', { userId, year, month });
      return JSON.parse(cached);
    }
    return null;
  },

  // Stocke les missions du planning dans le cache
  setPlanningItems: async (userId: string, items: any[], month?: number, year?: number, yearOnly?: boolean): Promise<void> => {
    const cacheKey = `${CACHE_PREFIX}${userId}:${year || ''}:${month || ''}:${yearOnly ? 'yearOnly' : ''}`;
    await redis.set(cacheKey, JSON.stringify(items), 'EX', CACHE_DURATION);
    // logger.info('Planning mis en cache', { userId, year, month, count: items.length });
  },

  // Récupère les missions masquées du planning
  getHiddenPlanningItems: async (userId: string, month?: number, year?: number, yearOnly?: boolean): Promise<any[] | null> => {
    const cacheKey = `${CACHE_PREFIX}hidden:${userId}:${year || ''}:${month || ''}:${yearOnly ? 'yearOnly' : ''}`;
    const cached = await redis.get(cacheKey);
    if (cached) {
      // logger.info('Cache hit pour le planning masqué', { userId, year, month });
      return JSON.parse(cached);
    }
    return null;
  },

  // Stocke les missions masquées du planning
  setHiddenPlanningItems: async (userId: string, items: any[], month?: number, year?: number, yearOnly?: boolean): Promise<void> => {
    const cacheKey = `${CACHE_PREFIX}hidden:${userId}:${year || ''}:${month || ''}:${yearOnly ? 'yearOnly' : ''}`;
    await redis.set(cacheKey, JSON.stringify(items), 'EX', CACHE_DURATION);
    // logger.info('Planning masqué mis en cache', { userId, year, month, count: items.length });
  },

  // Invalide tous les caches relatifs au planning d'un utilisateur
  invalidateUserCache: async (userId: string): Promise<void> => {
    const pattern = `${CACHE_PREFIX}${userId}:*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      // logger.info('Cache planning invalidé pour l\'utilisateur', { userId, keysCount: keys.length });
    }
    
    // Invalider également les caches masqués
    const hiddenPattern = `${CACHE_PREFIX}hidden:${userId}:*`;
    const hiddenKeys = await redis.keys(hiddenPattern);
    if (hiddenKeys.length > 0) {
      await redis.del(...hiddenKeys);
      // logger.info('Cache planning masqué invalidé pour l\'utilisateur', { userId, keysCount: hiddenKeys.length });
    }
  },

  // Invalide tout le cache relatif à une mission spécifique
  invalidateMissionCache: async (missionId: string): Promise<void> => {
    // Pour les cas où on modifie/supprime une mission spécifique, on doit invalider le cache de tous les utilisateurs
    // car plusieurs utilisateurs peuvent avoir accès à la même mission
    const keys = await redis.keys(`${CACHE_PREFIX}*`);
    if (keys.length > 0) {
      await redis.del(...keys);
      // logger.info('Cache planning invalidé pour tous les utilisateurs suite à modification de mission', { missionId, keysCount: keys.length });
    }
  }
};

// Extension du type Request pour inclure user
interface AuthenticatedRequest extends ExpressRequest {
  user?: {
    userId: string;
    email?: string;
    userType?: string;
    [key: string]: any;
  };
}

// Rate limiting pour les routes de planning
const planningRateLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes en prod
  max: 100, // Limite adaptée au besoin
  message: { error: 'Trop de requêtes sur les routes de planning, veuillez réessayer plus tard' },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: false, // Compte aussi les requêtes réussies
});

const router = Router();

// Schéma de validation pour la création/modification d'un créneau
const planningSchema = z.object({
  mission_id: z.string().uuid().optional(),
  title: z.string().min(1),
  description: z.string().optional(),
  start_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  end_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  montant_propose: z.number().min(0).optional(),
  payment_method: z.enum(['jobi_only', 'both', 'direct_only']).optional(),
  user_id: z.string().uuid().optional()
});

// Types pour les missions acceptées
interface AcceptedMission {
  mission: {
    id: string;
    titre: string;
    description?: string;
    user_id?: string;
  };
}

// Type pour la réponse de Supabase
interface SupabaseMissionResponse {
  mission: {
    id: string;
    titre: string;
    description: string | null;
    user_id?: string;
  };
}

// Type pour les missions du planning
interface PlanningSlot {
  id: string;
  mission_id?: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  date: string;
  user_id?: string;
  mission?: {
    id: string;
    titre: string;
    description: string | null;
    user_id?: string;
  };
  user?: {
    id: string;
    prenom: string;
    nom: string;
    photo_url?: string;
  };
}

// Récupérer les missions du planning (non supprimées)
router.get('/', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user?.userId) {
      res.status(401).json({ error: 'Non autorisé' });
      return;
    }

    // Récupérer les paramètres de mois et d'année de la requête
    const month = req.query.month ? parseInt(req.query.month as string) : null;
    const year = req.query.year ? parseInt(req.query.year as string) : null;
    const yearOnly = req.query.year_only === 'true';

    // Valider les paramètres
    if ((month !== null && (month < 1 || month > 12)) || 
        (year !== null && (year < 2000 || year > 2100))) {
      res.status(400).json({ error: 'Paramètres de date invalides' });
      return;
    }

    // Vérifier si les données sont en cache
    const cachedData = await planningCacheService.getPlanningItems(
      req.user.userId,
      month !== null ? month : undefined,
      year !== null ? year : undefined,
      yearOnly
    );
    if (cachedData) {
      res.json(cachedData);
      return;
    }

    // Si pas en cache, récupérer les données depuis la base de données
    // Récupérer les missions acceptées de l'utilisateur
    const { data: acceptedMissions, error: missionsError } = await supabase
      .from('user_mission_candidature')
      .select(`
        id,
        statut,
        montant_propose,
        message,
        montant_contre_offre,
        message_contre_offre,
        montant_contre_offre_jobbeur,
        message_contre_offre_jobbeur,
        payment_status,
        payment_date,
        montant_paiement,
        jobbeur_id,
        mission:user_missions!inner (
          id,
          titre,
          description,
          user_id
        )
      `)
      .eq('jobbeur_id', req.user.userId)
      .eq('statut', 'acceptée');

    if (missionsError) throw missionsError;

    // Initialiser un tableau pour stocker toutes les missions du planning
    let planningSlots: any[] = [];

    // Si l'utilisateur a des missions acceptées
    if (acceptedMissions && acceptedMissions.length > 0) {
      // Conversion sûre du type
      const typedMissions = acceptedMissions as unknown as SupabaseMissionResponse[];
      const missionIds = typedMissions.map(m => m.mission.id);

      // Créer un map des propositions pour une recherche rapide
      const propositionsMap = new Map();
      acceptedMissions.forEach((prop: any) => {
        propositionsMap.set(prop.mission.id, {
          id: prop.id,
          statut: prop.statut,
          montant_propose: prop.montant_propose,
          message: prop.message,
          montant_contre_offre: prop.montant_contre_offre,
          message_contre_offre: prop.message_contre_offre,
          montant_contre_offre_jobbeur: prop.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: prop.message_contre_offre_jobbeur,
          payment_status: prop.payment_status,
          payment_date: prop.payment_date,
          montant_paiement: prop.montant_paiement,
          jobbeur_id: prop.jobbeur_id
        });
      });

      // Préparer la requête
      let query = supabase
        .from('user_mission_planning')
        .select(`
          id,
          mission_id,
          title,
          description,
          start_time,
          end_time,
          date,
          montant_propose,
          payment_method,
          mission:user_missions (
            id,
            titre,
            description,
            user_id
          )
        `)
        .eq('is_deleted', false)
        .in('mission_id', missionIds)
        .order('date', { ascending: true });

      // Ajouter le filtre par année ou mois selon les paramètres
      if (year !== null) {
        if (yearOnly) {
          // Si yearOnly est vrai, filtrer par année uniquement
          const startDate = `${year}-01-01`;
          const endDate = `${year + 1}-01-01`;
          
          query = query
            .gte('date', startDate)
            .lt('date', endDate);
        } else if (month !== null) {
          // Filtrer par mois et année
          const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
          let endDate: string;
          
          // Déterminer le dernier jour du mois
          if (month === 12) {
            endDate = `${year + 1}-01-01`;
          } else {
            endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;
          }
          
          query = query
            .gte('date', startDate)
            .lt('date', endDate);
        }
      }

      // Exécuter la requête
      const { data: linkedSlots, error } = await query;

      if (error) throw error;
      
      if (linkedSlots) {
        planningSlots = linkedSlots;
        
        // Récupérer les informations des utilisateurs pour les missions liées
        const userIds = [...new Set(planningSlots
          .filter(slot => slot.mission && slot.mission.user_id)
          .map(slot => slot.mission.user_id))];
          
        if (userIds.length > 0) {
          // Récupérer les profils des propriétaires de mission
          const { data: userProfiles, error: profilesError } = await supabase
            .from('user_profil')
            .select('user_id, prenom, nom, photo_url')
            .in('user_id', userIds);

          if (profilesError) throw profilesError;

          // Créer un index des profils pour une recherche rapide
          const userProfilesMap = new Map();
          if (userProfiles) {
            // Déchiffrer tous les profils en parallèle
            const decryptedProfiles = await Promise.all(
              userProfiles.map(async (profile) => {
                const decryptedProfile = await decryptProfilDataAsync(profile);
                return { user_id: profile.user_id, profile: decryptedProfile };
              })
            );
            
            // Remplir la map avec les profils déchiffrés
            decryptedProfiles.forEach(({ user_id, profile }) => {
              userProfilesMap.set(user_id, profile);
            });
          }
          
          // Ajouter les informations utilisateur et proposition à chaque mission
          planningSlots = planningSlots.map(slot => {
            let updatedSlot = { ...slot };
            
            // Ajouter les informations utilisateur si disponibles
            if (slot.mission && slot.mission.user_id) {
              const userProfile = userProfilesMap.get(slot.mission.user_id);
              if (userProfile) {
                updatedSlot = {
                  ...updatedSlot,
                  user: {
                    id: slot.mission.user_id,
                    prenom: userProfile.prenom,
                    nom: userProfile.nom,
                    photo_url: userProfile.photo_url
                  }
                };
              }
            }
            
            // Ajouter les informations de proposition si disponibles
            if (slot.mission_id && propositionsMap.has(slot.mission_id)) {
              updatedSlot = {
                ...updatedSlot,
                proposition: propositionsMap.get(slot.mission_id)
              };
            }
            
            return updatedSlot;
          });
        }
      }
    }

    // Préparer la requête pour les missions manuelles
    let manualQuery = supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        user_id
      `)
      .eq('is_deleted', false)
      .is('mission_id', null)
      .eq('user_id', req.user.userId)
      .order('date', { ascending: true });

    // Ajouter le filtre par mois et année si spécifiés
    if (month !== null && year !== null) {
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      let endDate: string;
      
      // Déterminer le dernier jour du mois
      if (month === 12) {
        endDate = `${year + 1}-01-01`;
      } else {
        endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;
      }
      
      manualQuery = manualQuery
        .gte('date', startDate)
        .lt('date', endDate);
    }

    // Récupérer les missions manuelles (sans mission_id)
    const { data: manualSlots, error: manualError } = await manualQuery;

    if (manualError) throw manualError;

    // Combiner toutes les missions
    if (manualSlots && manualSlots.length > 0) {
      // Ajouter les missions manuelles au planning
      planningSlots = [...planningSlots, ...manualSlots];
    }

    // Récupérer également les missions dont l'utilisateur est propriétaire
    const { data: ownedMissionsInPlanning, error: ownedMissionsError } = await supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        mission:user_missions (
          id,
          titre,
          description,
          user_id
        )
      `)
      .eq('is_deleted', false)
      .not('mission_id', 'is', null)
      .order('date', { ascending: true });

    if (ownedMissionsError) {
      logger.error('Erreur lors de la récupération des missions propriétaires:', ownedMissionsError);
    } else if (ownedMissionsInPlanning && ownedMissionsInPlanning.length > 0) {
      // Récupérer les IDs des missions acceptées pour les exclure
      const acceptedMissionIds = acceptedMissions ? 
        acceptedMissions.map((m: any) => m.mission?.id).filter(Boolean) : [];
      
      // Récupérer les IDs des missions dont l'utilisateur est propriétaire
      const userOwnedMissionIds = await getUserOwnedMissionIds(req.user.userId);
      
      // Filtrer pour ne garder que les missions dont l'utilisateur est propriétaire
      // et qui ne sont pas déjà incluses comme missions acceptées
      const ownedSlotsFiltered = ownedMissionsInPlanning.filter(slot => 
        userOwnedMissionIds.includes(slot.mission_id) && 
        !acceptedMissionIds.includes(slot.mission_id)
      );
      
      // Récupérer les informations du propriétaire (l'utilisateur lui-même)
      const { data: userProfile } = await supabase
        .from('user_profil')
        .select('user_id, prenom, nom, photo_url')
        .eq('user_id', req.user.userId)
        .single();

      // Déchiffrer les données de profil de l'utilisateur
      const decryptedUserProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

      // Ajouter les informations du propriétaire à chaque mission
      const ownedSlotsWithUserInfo = ownedSlotsFiltered.map(slot => ({
        ...slot,
        user: decryptedUserProfile ? {
          id: decryptedUserProfile.user_id,
          prenom: decryptedUserProfile.prenom,
          nom: decryptedUserProfile.nom,
          photo_url: decryptedUserProfile.photo_url
        } : undefined
      }));

      // Ajouter les missions propriétaires au planning
      planningSlots = [...planningSlots, ...ownedSlotsWithUserInfo];
    }

    // Trier toutes les missions par date
    planningSlots.sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });

    // Mettre en cache les données récupérées
    await planningCacheService.setPlanningItems(
      req.user.userId,
      planningSlots,
      month !== null ? month : undefined,
      year !== null ? year : undefined,
      yearOnly
    );

    res.json(planningSlots);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération du planning:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération du planning' });
  }
});

// Récupérer les missions masquées du planning
router.get('/hidden', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    if (!req.user?.userId) {
      res.status(401).json({ error: 'Non autorisé' });
      return;
    }

    // Récupérer les paramètres de mois et d'année de la requête
    const month = req.query.month ? parseInt(req.query.month as string) : null;
    const year = req.query.year ? parseInt(req.query.year as string) : null;
    const yearOnly = req.query.year_only === 'true';

    // Valider les paramètres
    if ((month !== null && (month < 1 || month > 12)) || 
        (year !== null && (year < 2000 || year > 2100))) {
      res.status(400).json({ error: 'Paramètres de date invalides' });
      return;
    }

    // Vérifier si les données sont en cache
    const cachedData = await planningCacheService.getHiddenPlanningItems(
      req.user.userId,
      month !== null ? month : undefined,
      year !== null ? year : undefined,
      yearOnly
    );
    if (cachedData) {
      res.json(cachedData);
      return;
    }

    // Si pas en cache, récupérer les données depuis la base de données
    // Récupérer d'abord les missions acceptées pour avoir les mission_id et les détails des propositions
    const { data: acceptedMissions, error: missionsError } = await supabase
      .from('user_mission_candidature')
      .select(`
        id,
        statut,
        montant_propose,
        message,
        montant_contre_offre,
        message_contre_offre,
        montant_contre_offre_jobbeur,
        message_contre_offre_jobbeur,
        payment_status,
        payment_date,
        montant_paiement,
        jobbeur_id,
        mission:user_missions!inner (
          id,
          titre,
          description,
          user_id
        )
      `)
      .eq('jobbeur_id', req.user.userId)
      .eq('statut', 'acceptée');

    if (missionsError) throw missionsError;

    // Créer un map des propositions pour une recherche rapide
    const propositionsMap = new Map();
    if (acceptedMissions) {
      acceptedMissions.forEach((prop: any) => {
        propositionsMap.set(prop.mission.id, {
          id: prop.id,
          statut: prop.statut,
          montant_propose: prop.montant_propose,
          message: prop.message,
          montant_contre_offre: prop.montant_contre_offre,
          message_contre_offre: prop.message_contre_offre,
          montant_contre_offre_jobbeur: prop.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: prop.message_contre_offre_jobbeur,
          payment_status: prop.payment_status,
          payment_date: prop.payment_date,
          montant_paiement: prop.montant_paiement,
          jobbeur_id: prop.jobbeur_id
        });
      });
    }

    // Préparer la requête pour les missions masquées avec liaison
    let linkedQuery = supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        mission:user_missions (
          id,
          titre,
          description,
          user_id
        )
      `)
      .eq('is_deleted', true)
      .not('mission_id', 'is', null)
      .order('date', { ascending: true });

    // Ajouter le filtre par année ou mois selon les paramètres
    if (year !== null) {
      if (yearOnly) {
        // Si yearOnly est vrai, filtrer par année uniquement
        const startDate = `${year}-01-01`;
        const endDate = `${year + 1}-01-01`;
        
        linkedQuery = linkedQuery
          .gte('date', startDate)
          .lt('date', endDate);
      } else if (month !== null) {
        // Filtrer par mois et année
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        let endDate: string;
        
        // Déterminer le dernier jour du mois
        if (month === 12) {
          endDate = `${year + 1}-01-01`;
        } else {
          endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;
        }
        
        linkedQuery = linkedQuery
          .gte('date', startDate)
          .lt('date', endDate);
      }
    }

    // Exécuter la requête pour les missions liées masquées
    const { data: linkedSlots, error: linkedError } = await linkedQuery;

    if (linkedError) throw linkedError;

    // Préparer la requête pour les missions manuelles masquées
    let manualQuery = supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        user_id
      `)
      .eq('is_deleted', true)
      .is('mission_id', null)
      .eq('user_id', req.user.userId)
      .order('date', { ascending: true });

    // Ajouter le filtre par année ou mois selon les paramètres
    if (year !== null) {
      if (yearOnly) {
        // Si yearOnly est vrai, filtrer par année uniquement
        const startDate = `${year}-01-01`;
        const endDate = `${year + 1}-01-01`;
        
        manualQuery = manualQuery
          .gte('date', startDate)
          .lt('date', endDate);
      } else if (month !== null) {
        // Filtrer par mois et année
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        let endDate: string;
        
        // Déterminer le dernier jour du mois
        if (month === 12) {
          endDate = `${year + 1}-01-01`;
        } else {
          endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;
        }
        
        manualQuery = manualQuery
          .gte('date', startDate)
          .lt('date', endDate);
      }
    }

    // Récupérer les missions manuelles masquées
    const { data: manualSlots, error: manualError } = await manualQuery;

    if (manualError) throw manualError;

    // Combiner toutes les missions masquées
    let hiddenSlots: any[] = [];

    // Ajouter les missions liées si elles existent
    if (linkedSlots && linkedSlots.length > 0) {
      hiddenSlots = [...linkedSlots];
      
      // Récupérer les informations des utilisateurs pour les missions liées
      const userIds = [...new Set(hiddenSlots
        .filter(slot => slot.mission && slot.mission.user_id)
        .map(slot => slot.mission.user_id))];
        
      if (userIds.length > 0) {
        // Récupérer les profils des propriétaires de mission
        const { data: userProfiles, error: profilesError } = await supabase
          .from('user_profil')
          .select('user_id, prenom, nom, photo_url')
          .in('user_id', userIds);

        if (profilesError) throw profilesError;

        // Créer un index des profils pour une recherche rapide
        const userProfilesMap = new Map();
        if (userProfiles) {
          // Déchiffrer tous les profils en parallèle
          const decryptedProfiles = await Promise.all(
            userProfiles.map(async (profile) => {
              const decryptedProfile = await decryptProfilDataAsync(profile);
              return { user_id: profile.user_id, profile: decryptedProfile };
            })
          );
          
          // Remplir la map avec les profils déchiffrés
          decryptedProfiles.forEach(({ user_id, profile }) => {
            userProfilesMap.set(user_id, profile);
          });
        }
        
        // Ajouter les informations utilisateur et proposition à chaque mission
        hiddenSlots = hiddenSlots.map(slot => {
          let updatedSlot = { ...slot };
          
          // Ajouter les informations utilisateur si disponibles
          if (slot.mission && slot.mission.user_id) {
            const userProfile = userProfilesMap.get(slot.mission.user_id);
            if (userProfile) {
              updatedSlot = {
                ...updatedSlot,
                user: {
                  id: slot.mission.user_id,
                  prenom: userProfile.prenom,
                  nom: userProfile.nom,
                  photo_url: userProfile.photo_url
                }
              };
            }
          }
          
          // Ajouter les informations de proposition si disponibles
          if (slot.mission_id && propositionsMap.has(slot.mission_id)) {
            updatedSlot = {
              ...updatedSlot,
              proposition: propositionsMap.get(slot.mission_id)
            };
          }
          
          return updatedSlot;
        });
      }
    }

    // Ajouter les missions manuelles si elles existent
    if (manualSlots && manualSlots.length > 0) {
      // Ajouter simplement les missions manuelles sans chercher de propriétaire
      hiddenSlots = [...hiddenSlots, ...manualSlots];
    }

    // Trier toutes les missions par date
    hiddenSlots.sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });

    // Mettre en cache les données récupérées
    await planningCacheService.setHiddenPlanningItems(
      req.user.userId,
      hiddenSlots,
      month !== null ? month : undefined,
      year !== null ? year : undefined,
      yearOnly
    );

    res.json(hiddenSlots);
  } catch (error) {
    logger.error('Erreur lors de la récupération des missions masquées:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des missions masquées' });
  }
});

// Ajouter un créneau au planning
router.post('/', planningRateLimiter, authMiddleware.authenticateToken, validateRequest(planningSchema), async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { mission_id, title, description, start_time, end_time, date, montant_propose, payment_method } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ error: 'Utilisateur non authentifié' });
      return;
    }

    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium, options } = await getUserSubscriptionLimits(userId);
    
    // Si l'utilisateur n'est pas premium, vérifier le nombre de missions qu'il a déjà
    if (!isPremium) {
      // Compter le nombre de missions visibles (non masquées) de l'utilisateur
      const { count, error: countError } = await supabase
        .from('user_mission_planning')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_deleted', false);
      
      if (countError) {
        logger.error('Erreur lors du comptage des missions:', countError);
        throw countError;
      }
      
      // Déterminer la limite de missions pour les utilisateurs non-premium
      const { planningSlotsLimit } = await getUserSubscriptionLimits(userId);
      
      // Si l'utilisateur a déjà atteint sa limite, limiter l'ajout
      if ((count || 0) >= planningSlotsLimit) {
        res.status(403).json({ 
          error: `Vous avez atteint la limite de ${planningSlotsLimit} missions pour les comptes gratuits. Passez à la version premium pour ajouter plus de missions.`
        });
        return;
      }
    }

    // Si mission_id est fourni, vérifier que la mission existe et appartient à l'utilisateur
    if (mission_id) {
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id')
        .eq('id', mission_id)
        .single();

      if (missionError || !mission) {
        res.status(404).json({ error: 'Mission non trouvée' });
        return;
      }

      if (!req.user || mission.user_id !== req.user.userId) {
        res.status(403).json({ error: 'Accès non autorisé à cette mission' });
        return;
      }
    }

    // Préparation des données à insérer
    const insertData = {
      mission_id, // sera null si non fourni
      title,
      description,
      start_time,
      end_time,
      date
    };

    // Ajouter les champs de tarif et mode de paiement pour les missions manuelles
    if (!mission_id) {
      // Convertir le montant en nombre si c'est une chaîne
      const montantFinal = typeof montant_propose === 'string' 
        ? parseFloat(montant_propose) 
        : montant_propose;
        
      // S'assurer que le montant est un nombre valide
      const montantValidé = isNaN(montantFinal) ? 0 : montantFinal;

      Object.assign(insertData, { 
        montant_propose: montantValidé,
        payment_method: payment_method || 'jobi_only',
        user_id: userId
      });
    }

    // Ajouter le créneau
    const { data, error } = await supabase
      .from('user_mission_planning')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de l\'insertion dans Supabase:', error);
      res.status(500).json({ error: 'Erreur lors de l\'ajout au planning' });
      return;
    }

    // Invalider le cache du planning pour cet utilisateur
    await planningCacheService.invalidateUserCache(userId);
    
    // Si c'est une mission liée, invalider également tous les caches liés à cette mission
    if (mission_id) {
      await planningCacheService.invalidateMissionCache(mission_id);
    }

    res.status(201).json(data);
  } catch (error) {
    logger.error('Erreur lors de l\'ajout au planning:', error);
    res.status(500).json({ error: 'Erreur lors de l\'ajout au planning' });
  }
});

// Modifier un créneau
router.put('/:id', planningRateLimiter, authMiddleware.authenticateToken, validateRequest(planningSchema), async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;
    const { mission_id, title, description, start_time, end_time, date, montant_propose, payment_method } = req.body;

    if (!userId) {
      res.status(401).json({ error: 'Utilisateur non authentifié' });
      return;
    }

    // Vérifier que le créneau existe
    const { data: existingSlot, error: slotError } = await supabase
      .from('user_mission_planning')
      .select(`
        id, 
        mission_id, 
        is_deleted, 
        user_id,
        mission:user_missions (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (slotError || !existingSlot) {
      logger.error('Créneau non trouvé ou non masqué:', { id, error: slotError });
      res.status(404).json({ error: 'Créneau non trouvé ou non masqué' });
      return;
    }

    // Pour les missions manuelles (sans mission_id), l'autorisation est accordée automatiquement
    if (!existingSlot.mission_id) {
      // Vérifier si l'utilisateur est le propriétaire de cette mission manuelle
      if (existingSlot.user_id && existingSlot.user_id !== userId) {
        logger.warn('Accès non autorisé au créneau manuel:', {
          userId,
          creneauId: id,
          proprietaireId: existingSlot.user_id
        });
        res.status(403).json({ error: 'Accès non autorisé à ce créneau' });
        return;
      }
    } 
    // Pour les missions liées, vérifier si l'utilisateur est propriétaire OU jobbeur accepté
    else {
      // Vérifier d'abord si l'utilisateur est un jobbeur avec une candidature acceptée
      const { data: candidature, error: candidatureError } = await supabase
        .from('user_mission_candidature')
        .select('id, statut')
        .eq('mission_id', existingSlot.mission_id)
        .eq('jobbeur_id', userId)
        .eq('statut', 'acceptée')
        .single();
      
      // Si l'utilisateur n'est pas un jobbeur accepté, vérifier s'il est le propriétaire
      let isAuthorized = !!candidature;
      
      if (!isAuthorized && existingSlot.mission) {
        // Traitement de mission comme un tableau ou objet
        const missionData = Array.isArray(existingSlot.mission) 
          ? existingSlot.mission[0] 
          : existingSlot.mission;
        
        if (missionData && typeof missionData === 'object' && 'user_id' in missionData) {
          isAuthorized = missionData.user_id === userId;
        }
      }
      
      // Si l'utilisateur n'est ni propriétaire ni jobbeur accepté, refuser l'accès
      if (!isAuthorized) {
        logger.warn('Accès non autorisé au créneau:', {
          userId,
          missionId: existingSlot.mission_id
        });
        res.status(403).json({ error: 'Accès non autorisé à ce créneau' });
        return;
      }
    }

    // Mettre à jour le créneau
    const updateData = {
      mission_id,
      title,
      description,
      start_time,
      end_time,
      date
    };

    // Ajouter les champs de tarif et mode de paiement pour toutes les missions
    if (montant_propose !== undefined) {
      // Convertir le montant en nombre si c'est une chaîne
      const montantFinal = typeof montant_propose === 'string' 
        ? parseFloat(montant_propose) 
        : montant_propose;
        
      // S'assurer que le montant est un nombre valide  
      const montantValidé = isNaN(montantFinal) ? 0 : montantFinal;
      
      Object.assign(updateData, { 
        montant_propose: montantValidé
      });
    }
    
    // Ajouter le mode de paiement si fourni
    if (payment_method) {
      Object.assign(updateData, { 
        payment_method
      });
    }

    const { data, error } = await supabase
      .from('user_mission_planning')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // Invalider le cache du planning pour cet utilisateur
    await planningCacheService.invalidateUserCache(userId);
    
    // Si c'est une mission liée, invalider également tous les caches liés à cette mission
    if (mission_id || (existingSlot.mission_id && existingSlot.mission_id !== mission_id)) {
      // Invalider le cache pour l'ancienne mission_id si elle existait et a été changée
      if (existingSlot.mission_id) {
        await planningCacheService.invalidateMissionCache(existingSlot.mission_id);
      }
      
      // Invalider le cache pour la nouvelle mission_id si elle existe
      if (mission_id) {
        await planningCacheService.invalidateMissionCache(mission_id);
      }
    }

    res.json(data);
  } catch (error) {
    logger.error('Erreur lors de la modification du créneau:', error);
    res.status(500).json({ error: 'Erreur lors de la modification du créneau' });
  }
});

// Supprimer un créneau (soft delete pour les missions liées, suppression définitive pour les missions manuelles)
router.delete('/:id', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ error: 'Utilisateur non authentifié' });
      return;
    }

    // Vérifier que le créneau existe
    const { data: existingSlot, error: slotError } = await supabase
      .from('user_mission_planning')
      .select(`
        id, 
        mission_id, 
        is_deleted, 
        user_id,
        mission:user_missions (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (slotError || !existingSlot) {
      logger.error('Créneau non trouvé:', { id, error: slotError });
      res.status(404).json({ error: 'Créneau non trouvé' });
      return;
    }

    // Pour les missions manuelles (sans mission_id), supprimer directement
    if (!existingSlot.mission_id) {
      // Vérifier si l'utilisateur est le propriétaire de cette mission manuelle
      if (existingSlot.user_id && existingSlot.user_id !== userId) {
        logger.warn('Accès non autorisé pour supprimer le créneau manuel:', {
          userId,
          creneauId: id,
          proprietaireId: existingSlot.user_id
        });
        res.status(403).json({ error: 'Accès non autorisé à ce créneau' });
        return;
      }
      
      // logger.info('Suppression d\'une mission manuelle:', { id });
      
      // Suppression définitive pour les missions manuelles
      const { error } = await supabase
        .from('user_mission_planning')
        .delete()
        .eq('id', id);

      if (error) {
        logger.error('Erreur lors de la suppression définitive:', error);
        throw error;
      }
      
      // Invalider le cache du planning pour cet utilisateur
      await planningCacheService.invalidateUserCache(userId);
      
      // logger.info('Créneau supprimé définitivement');
      res.setHeader('X-Mission-Action', 'supprimé');
      res.status(204).send();
      return;
    }
    
    // Pour les missions liées, vérifier si l'utilisateur est le propriétaire ou le jobbeur accepté
    // D'abord, vérifier si l'utilisateur est un jobbeur avec une candidature acceptée
    const { data: candidature, error: candidatureError } = await supabase
      .from('user_mission_candidature')
      .select('id, statut')
      .eq('mission_id', existingSlot.mission_id)
      .eq('jobbeur_id', userId)
      .eq('statut', 'acceptée')
      .single();
      
    // Si l'utilisateur n'est pas un jobbeur accepté, vérifier s'il est le propriétaire
    let isAuthorized = !!candidature;
    
    if (!isAuthorized) {
      const { data: mission, error: missionError } = await supabase
        .from('user_missions')
        .select('id, user_id')
        .eq('id', existingSlot.mission_id)
        .single();
        
      if (!missionError && mission && mission.user_id === userId) {
        isAuthorized = true;
      }
    }
    
    // Si l'utilisateur n'est ni propriétaire ni jobbeur accepté, refuser l'accès
    if (!isAuthorized) {
      logger.warn('Accès non autorisé au créneau:', {
        userId,
        missionId: existingSlot.mission_id
      });
      res.status(403).json({ error: 'Accès non autorisé à ce créneau' });
      return;
    }
    
    // Effectuer un soft delete pour les missions liées
    const { error } = await supabase
      .from('user_mission_planning')
      .update({ is_deleted: true })
      .eq('id', id);

    if (error) {
      logger.error('Erreur lors du soft delete:', error);
      throw error;
    }
    
    // Invalider les caches de planning normal et planning masqué pour cet utilisateur
    await planningCacheService.invalidateUserCache(userId);
    
    // Invalider également le cache lié à cette mission spécifique
    await planningCacheService.invalidateMissionCache(existingSlot.mission_id);
    
    // logger.info('Créneau masqué avec succès (soft delete)');
    res.setHeader('X-Mission-Action', 'masqué');
    res.status(204).send();
  } catch (error) {
    logger.error('Erreur lors de la suppression du créneau:', error);
    res.status(500).json({ error: 'Erreur lors de la suppression du créneau' });
  }
});

// Export iCal
router.get('/ical', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.userId) {
      res.status(401).json({ error: 'Non autorisé' });
      return;
    }
    
    const userId = req.user.userId;
    // logger.info('Génération du flux iCal pour l\'utilisateur:', { userId });
    
    // Étape 1: Récupérer les missions où l'utilisateur est le propriétaire
    const { data: userMissions, error: userMissionsError } = await supabase
      .from('user_missions')
      .select('id')
      .eq('user_id', userId);
      
    if (userMissionsError) {
      logger.error('Erreur lors de la récupération des missions de l\'utilisateur:', { userId, error: userMissionsError });
      throw userMissionsError;
    }
    
    // Récupérer les IDs des missions créées par l'utilisateur
    const userMissionIds = userMissions ? userMissions.map(m => m.id) : [];
    
    // Étape 2: Récupérer les missions où l'utilisateur est jobbeur accepté
    const { data: acceptedCandidatures, error: candidaturesError } = await supabase
      .from('user_mission_candidature')
      .select('mission_id')
      .eq('jobbeur_id', userId)
      .eq('statut', 'acceptée');
      
    if (candidaturesError) {
      logger.error('Erreur lors de la récupération des candidatures acceptées:', { userId, error: candidaturesError });
      throw candidaturesError;
    }
    
    // Récupérer les IDs des missions où l'utilisateur est jobbeur accepté
    const acceptedMissionIds = acceptedCandidatures ? acceptedCandidatures.map(c => c.mission_id) : [];
    
    // Combiner les deux ensembles d'IDs
    const allMissionIds = [...new Set([...userMissionIds, ...acceptedMissionIds])];
    
    // logger.info('Missions liées à l\'utilisateur:', { 
    //   userId, 
    //   ownedCount: userMissionIds.length,
    //   acceptedCount: acceptedMissionIds.length,
    //   totalCount: allMissionIds.length
    // });
    
    // Récupérer les entrées de planning en 2 étapes
    
    // 1. D'abord les entrées liées à des missions
    let planningEntries: any[] = [];
    
    if (allMissionIds.length > 0) {
      // Récupérer les entrées de planning liées aux missions identifiées
      const { data: linkedEntries, error: linkedError } = await supabase
        .from('user_mission_planning')
        .select(`
          id,
          mission_id,
          title,
          description,
          start_time,
          end_time,
          date,
          montant_propose,
          payment_method,
          mission:user_missions (
            id,
            titre,
            description,
            user_id
          )
        `)
        .eq('is_deleted', false)
        .in('mission_id', allMissionIds)
        .order('date', { ascending: true });
        
      if (linkedError) {
        logger.error('Erreur lors de la récupération des entrées liées du planning:', { userId, error: linkedError });
        throw linkedError;
      }
      
      if (linkedEntries) {
        // Récupérer les informations des candidatures acceptées pour obtenir les montants
        const missionIds = linkedEntries.map(entry => entry.mission_id).filter(Boolean);
        const { data: candidatures } = await supabase
          .from('user_mission_candidature')
          .select(`
            id,
            mission_id,
            montant_propose,
            montant_contre_offre,
            montant_contre_offre_jobbeur,
            statut,
            payment_status,
            payment_date,
            montant_paiement,
            jobbeur_id
          `)
          .in('mission_id', missionIds)
          .eq('jobbeur_id', userId)
          .eq('statut', 'acceptée');
        
        // Créer un map des propositions pour une recherche rapide
        const propositionsMap = new Map();
        if (candidatures) {
          candidatures.forEach(candidature => {
            propositionsMap.set(candidature.mission_id, {
              id: candidature.id,
              statut: candidature.statut,
              montant_propose: candidature.montant_propose,
              montant_contre_offre: candidature.montant_contre_offre,
              montant_contre_offre_jobbeur: candidature.montant_contre_offre_jobbeur,
              payment_status: candidature.payment_status,
              payment_date: candidature.payment_date,
              montant_paiement: candidature.montant_paiement,
              // Utiliser la valeur de payment_method depuis la mission ou défaut 'jobi_only'
              payment_method: 'jobi_only',
              jobbeur_id: candidature.jobbeur_id
            });
          });
        }
        
        // Enrichir les entrées du planning avec les propositions
        planningEntries = linkedEntries.map(entry => {
          let updatedEntry = { ...entry } as any; // Utiliser 'as any' pour permettre l'ajout de propriétés arbitraires
          
          // Ajouter les informations de proposition si disponibles
          if (entry.mission_id && propositionsMap.has(entry.mission_id)) {
            updatedEntry = {
              ...updatedEntry,
              proposition: propositionsMap.get(entry.mission_id)
            };
          } 
          // Si pas de proposition mais entry a un montant_propose, l'utiliser directement
          else if (entry.montant_propose && entry.montant_propose > 0) {
            // Pas besoin de créer une proposition artificielle, car le montant est déjà dans entry
          }
          // Si entry.mission existe mais pas de proposition, utiliser les données de la mission
          else if (entry.mission && (entry.mission as any).budget && (entry.mission as any).budget_defini) {
            updatedEntry.montant_propose = (entry.mission as any).budget;
            updatedEntry.payment_method = (entry.mission as any).payment_method || 'jobi_only';
          }
          
          return updatedEntry;
        });
      }
    }
    
    // 2. Récupérer les entrées manuelles (sans mission_id)
    // Maintenant avec le filtre user_id pour ne récupérer que les entrées de l'utilisateur
    const { data: manualEntries, error: manualError } = await supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        user_id
      `)
      .eq('is_deleted', false)
      .is('mission_id', null)
      .eq('user_id', userId)
      .order('date', { ascending: true });
      
    if (manualError) {
      logger.error('Erreur lors de la récupération des entrées manuelles du planning:', { userId, error: manualError });
      throw manualError;
    }
    
    // Ajouter les entrées manuelles de l'utilisateur à l'export iCal
    if (manualEntries && manualEntries.length > 0) {
      planningEntries = [...planningEntries, ...manualEntries];
      
      // logger.info('Entrées manuelles ajoutées au feed iCal:', {
      //   userId,
      //   count: manualEntries.length
      // });
    }
    
    // logger.info('Total des entrées du planning récupérées pour feed iCal:', { 
    //   userId, 
    //   count: planningEntries.length
    // });
    
    // Limiter le nombre de créneaux exportés selon l'abonnement
    const { isPremium, planningSlotsLimit } = await getUserSubscriptionLimits(userId);
    let warning = '';
    if (!isPremium && planningSlotsLimit > 0 && planningEntries.length > planningSlotsLimit) {
      // Trier par date croissante et ne garder que la limite autorisée
      planningEntries = planningEntries
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, planningSlotsLimit);
      warning = `ATTENTION : Vous avez atteint la limite de ${planningSlotsLimit} créneaux exportés pour les comptes gratuits. Passez à l'abonnement premium pour synchroniser l'intégralité de votre planning.`;
    }

    // Générer le contenu iCal
    let icalContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//JobPartiel//Planning//FR',
      'X-WR-CALNAME:JobPartiel - Planning',
      `X-WR-CALDESC:Calendrier des missions JobPartiel${warning ? ' - ' + warning : ''}`,
      'REFRESH-INTERVAL;VALUE=DURATION:PT12H',
      'X-PUBLISHED-TTL:PT12H',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      // Définition du fuseau horaire Europe/Paris
      'BEGIN:VTIMEZONE',
      'TZID:Europe/Paris',
      'X-LIC-LOCATION:Europe/Paris',
      'BEGIN:DAYLIGHT',
      'TZOFFSETFROM:+0100',
      'TZOFFSETTO:+0200',
      'TZNAME:CEST',
      'DTSTART:19700329T020000',
      'RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU',
      'END:DAYLIGHT',
      'BEGIN:STANDARD',
      'TZOFFSETFROM:+0200',
      'TZOFFSETTO:+0100',
      'TZNAME:CET',
      'DTSTART:19701025T030000',
      'RRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU',
      'END:STANDARD',
      'END:VTIMEZONE'
    ];

    if (planningEntries.length > 0) {
      planningEntries.forEach((mission: any, idx: number) => {
        // Utiliser le titre de la mission ou le titre du créneau pour les missions manuelles
        const titre = mission.mission?.titre || mission.title;
        // Utiliser la description de la mission ou la description du créneau pour les missions manuelles
        let description = mission.mission?.description || mission.description;
        // Ajouter l'avertissement dans la description du premier événement si warning
        if (idx === 0 && warning) {
          description = `${warning}\n\n${description || ''}`;
        }
        
        // Générer un UID vraiment unique qui respecte le standard RFC 
        const uniqueId = `jobpartiel-mission-${mission.id}-${userId.slice(0, 8)}@jobpartiel.fr`;
        
        // Générer les dates au format UTC (Z) comme requis par Google Calendar
        const datestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        
        // Construire la date de début avec le fuseau horaire Europe/Paris
        // Ajouter une propriété TZID pour spécifier le fuseau horaire
        const startDate = `${mission.date.replace(/-/g, '')}T${mission.start_time.replace(/:/g, '')}00`;
        const endDate = `${mission.date.replace(/-/g, '')}T${mission.end_time.replace(/:/g, '')}00`;
        
        // Ajouter des informations spécifiques pour Google Calendar
        let descriptionContent = description || '';
        
        // Déterminer le montant à afficher (comme dans le frontend)
        let montantAffiche = 0;
        let modePayment = mission.payment_method || 'jobi_only';
        
        // Si c'est une mission manuelle ou si le montant est déjà défini dans la mission
        if (mission.montant_propose && mission.montant_propose > 0) {
          montantAffiche = mission.montant_propose;
        } 
        // Sinon, vérifier les informations de proposition
        else if (mission.proposition) {
          // Vérifier les priorités de montant selon le statut
          if (mission.proposition.statut === 'contre_offre_jobbeur' && mission.proposition.montant_contre_offre_jobbeur !== null) {
            montantAffiche = mission.proposition.montant_contre_offre_jobbeur;
          } else if (mission.proposition.statut === 'contre_offre' && mission.proposition.montant_contre_offre !== null) {
            montantAffiche = mission.proposition.montant_contre_offre;
          } else {
            montantAffiche = mission.proposition.montant_propose || 0;
          }
          
          // Récupérer également le mode de paiement de la proposition
          modePayment = mission.proposition.payment_method || modePayment;
        }
        
        // Créer le suffixe de montant pour le titre et la description
        let montantTexte = '';
        if (montantAffiche > 0) {
          const devise = modePayment === 'direct_only' ? '€' : 'J';
          montantTexte = `${montantAffiche} ${devise}`;
          
          // Ajouter le montant de façon très visible en haut de la description
          descriptionContent = `MONTANT: ${montantTexte}\n\n${descriptionContent}`;
        }
        
        // Échapper les caractères spéciaux dans la description selon les spécifications iCal
        const escapedDescription = descriptionContent
          .replace(/\\/g, '\\\\')
          .replace(/;/g, '\\;')
          .replace(/,/g, '\\,')
          .replace(/\n/g, '\\n');

        // Ajouter le montant au titre pour qu'il soit toujours visible
        const titreAvecMontant = montantTexte ? `${titre || 'Mission JobPartiel'} (${montantTexte})` : (titre || 'Mission JobPartiel');

        icalContent = icalContent.concat([
          'BEGIN:VEVENT',
          `UID:${uniqueId}`,
          `DTSTAMP:${datestamp}`,
          `DTSTART;TZID=Europe/Paris:${startDate}`,
          `DTEND;TZID=Europe/Paris:${endDate}`,
          `SUMMARY:${titreAvecMontant}`,
          `DESCRIPTION:${escapedDescription}`,
          'STATUS:CONFIRMED',
          'TRANSP:OPAQUE',
          `CREATED:${datestamp}`,
          `LAST-MODIFIED:${datestamp}`,
          `SEQUENCE:0`,
          'BEGIN:VALARM',
          'ACTION:DISPLAY',
          'DESCRIPTION:Rappel de mission JobPartiel',
          'TRIGGER:-PT1H',
          'END:VALARM',
          'END:VEVENT'
        ]);
      });
    }

    icalContent.push('END:VCALENDAR');

    // Envoyer la réponse
    res.setHeader('Content-Type', 'text/calendar; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename=jobpartiel-planning.ics');
    // Permettre la mise en cache pendant 1 heure mais autoriser les mises à jour
    res.setHeader('Cache-Control', 'public, max-age=3600, must-revalidate');
    res.send(icalContent.join('\r\n'));
    
    // logger.info('Flux iCal généré avec succès pour:', { userId: userId });
  } catch (error) {
    logger.error('Erreur lors de la génération du flux iCal:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Restaurer une mission masquée (passer is_deleted de true à false)
router.post('/:id/restore', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({ error: 'Utilisateur non authentifié' });
      return;
    }

    // Vérifier que le créneau existe et est masqué
    const { data: existingSlot, error: slotError } = await supabase
      .from('user_mission_planning')
      .select(`
        id, 
        mission_id, 
        is_deleted, 
        user_id,
        mission:user_missions (
          id,
          user_id
        )
      `)
      .eq('id', id)
      .eq('is_deleted', true)
      .single();

    if (slotError || !existingSlot) {
      logger.error('Créneau non trouvé ou non masqué:', { id, error: slotError });
      res.status(404).json({ error: 'Créneau non trouvé ou non masqué' });
      return;
    }
    
    // Vérifier les autorisations similaires à la suppression
    let isAuthorized = true;
    
    // Pour les missions liées, vérifier si l'utilisateur est le propriétaire ou le jobbeur accepté
    if (existingSlot.mission_id) {
      // D'abord, vérifier si l'utilisateur est un jobbeur avec une candidature acceptée
      const { data: candidature, error: candidatureError } = await supabase
        .from('user_mission_candidature')
        .select('id, statut')
        .eq('mission_id', existingSlot.mission_id)
        .eq('jobbeur_id', userId)
        .eq('statut', 'acceptée')
        .single();
        
      // Si l'utilisateur n'est pas un jobbeur accepté, vérifier s'il est le propriétaire
      isAuthorized = !!candidature;
      
      if (!isAuthorized && existingSlot.mission) {
        // Traitement de mission comme un tableau ou objet
        const missionData = Array.isArray(existingSlot.mission) 
          ? existingSlot.mission[0] 
          : existingSlot.mission;
        
        if (missionData && typeof missionData === 'object' && 'user_id' in missionData) {
          isAuthorized = missionData.user_id === userId;
        }
      }
    }
    
    // Si l'utilisateur n'est ni propriétaire ni jobbeur accepté, refuser l'accès
    if (!isAuthorized) {
      logger.warn('Accès non autorisé pour restaurer le créneau:', {
        userId,
        missionId: existingSlot.mission_id
      });
      res.status(403).json({ error: 'Accès non autorisé à ce créneau' });
      return;
    }
    
    // Restaurer la mission (is_deleted = false)
    const { data, error } = await supabase
      .from('user_mission_planning')
      .update({ is_deleted: false })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la restauration du créneau:', error);
      throw error;
    }
    
    // Invalider les caches de planning normal et planning masqué pour cet utilisateur
    await planningCacheService.invalidateUserCache(userId);
    
    // Si c'est une mission liée, invalider également le cache spécifique à cette mission
    if (existingSlot.mission_id) {
      await planningCacheService.invalidateMissionCache(existingSlot.mission_id);
    }
    
    // logger.info('Créneau restauré avec succès:', { id });
    res.status(200).json({
      message: 'Mission restaurée avec succès',
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la restauration du créneau:', error);
    res.status(500).json({ error: 'Erreur lors de la restauration du créneau' });
  }
});

// Importer les missions acceptées dans le planning
router.post('/import-missions', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({ error: 'Utilisateur non authentifié' });
      return;
    }
    
    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium, options } = await getUserSubscriptionLimits(userId);
    
    // Si l'utilisateur n'est pas premium, vérifier le nombre de missions qu'il a déjà
    let missionsLimit = -1; // -1 signifie pas de limite (pour les utilisateurs premium)
    
    if (!isPremium) {
      // Compter le nombre de missions visibles (non masquées) de l'utilisateur
      const { count, error: countError } = await supabase
        .from('user_mission_planning')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_deleted', false);
      
      if (countError) {
        logger.error('Erreur lors du comptage des missions:', countError);
        throw countError;
      }
      
      const currentCount = count || 0;
      const { planningSlotsLimit } = await getUserSubscriptionLimits(userId);
      
      // Si l'utilisateur a déjà atteint sa limite, empêcher l'importation
      if (currentCount >= planningSlotsLimit) {
        res.status(403).json({ 
          error: `Vous avez atteint la limite de ${planningSlotsLimit} missions pour les comptes gratuits. Passez à la version premium pour importer plus de missions.`
        });
        return;
      }
      
      // Calculer combien de missions l'utilisateur peut encore importer
      missionsLimit = planningSlotsLimit - currentCount;
    }
    
    // Définir les types pour les données de mission
    interface MissionData {
      id: string;
      titre: string;
      description: string | null;
      date_mission: string | null;
    }
    
    interface MissionCandidat {
      mission: {
        id: string;
        titre: string;
        description: string | null;
        date_mission: string | null;
      }
    }
    
    // Récupérer les missions où l'utilisateur est jobbeur accepté
    const { data: acceptedMissions, error: missionsError } = await supabase
      .from('user_mission_candidature')
      .select(`
        mission:user_missions!inner (
          id,
          titre,
          description,
          date_mission
        )
      `)
      .eq('jobbeur_id', userId)
      .eq('statut', 'acceptée');

    if (missionsError) throw missionsError;

    // Récupérer également les missions dont l'utilisateur est propriétaire
    const { data: ownedMissions, error: ownedMissionsError } = await supabase
      .from('user_missions')
      .select(`
        id,
        titre,
        description,
        date_mission
      `)
      .eq('user_id', userId)
      .in('statut', ['en_attente', 'en_cours', 'terminee']);

    if (ownedMissionsError) throw ownedMissionsError;

    // Convertir les missions en format uniforme
    const acceptedMissionsProcessed: MissionData[] = acceptedMissions ? 
      acceptedMissions.map((item: any) => ({
        id: item.mission.id,
        titre: item.mission.titre,
        description: item.mission.description,
        date_mission: item.mission.date_mission
      })) : [];
    
    // Les missions dont l'utilisateur est propriétaire sont déjà au bon format
    const ownedMissionsProcessed: MissionData[] = ownedMissions || [];
    
    // Combiner les deux ensembles de missions
    const allMissions: MissionData[] = [...acceptedMissionsProcessed, ...ownedMissionsProcessed];

    if (allMissions.length === 0) {
      res.json({ message: 'Aucune nouvelle mission à importer' });
      return;
    }

    // Vérifier quelles missions sont déjà dans le planning
    const { data: existingPlanningSlots, error: existingError } = await supabase
      .from('user_mission_planning')
      .select('mission_id');
      // Pas de filtre is_deleted pour prendre en compte toutes les missions, y compris masquées

    if (existingError) throw existingError;

    // Créer un Set des IDs de missions déjà dans le planning
    const existingMissionIds = new Set((existingPlanningSlots || []).map(slot => slot.mission_id).filter(Boolean));
    
    // Log pour déboguer les missions déjà existantes
    // logger.info('Missions déjà présentes dans le planning:', {
    //   count: existingMissionIds.size,
    //   ids: Array.from(existingMissionIds)
    // });
    
    // Log des missions disponibles à importer
    // logger.info('Missions disponibles pour import:', {
      // total: allMissions.length,
      // acceptedCount: acceptedMissionsProcessed.length,
      // ownedCount: ownedMissionsProcessed.length,
      // missions: allMissions.map(m => ({
      //   id: m.id,
      //   titre: m.titre
      // }))
    // });
    
    // Filtrer les missions qui ne sont pas encore dans le planning et éliminer les doublons
    const uniqueIds = new Set<string>();
    const uniqueMissionsToImport: MissionData[] = allMissions
      .filter(m => !existingMissionIds.has(m.id))
      .filter(m => {
        // Vérifier si nous avons déjà ajouté cette mission
        if (uniqueIds.has(m.id)) {
          return false;
        }
        uniqueIds.add(m.id);
        return true;
      });

    // Log des missions qui vont être importées
    // logger.info('Missions qui seront importées:', {
    //   count: uniqueMissionsToImport.length,
    //   missions: uniqueMissionsToImport.map(m => ({
    //     id: m.id,
    //     titre: m.titre
    //   }))
    // });

    if (uniqueMissionsToImport.length === 0) {
      res.status(204).send();
      return;
    }

    // Appliquer la limite pour les utilisateurs non-premium
    let missionsToImport = uniqueMissionsToImport;
    let limitMessage = '';
    
    if (missionsLimit > 0 && uniqueMissionsToImport.length > missionsLimit) {
      // Limiter le nombre de missions à importer
      missionsToImport = uniqueMissionsToImport.slice(0, missionsLimit);
      limitMessage = ` (limité à ${missionsLimit} mission(s) pour les comptes gratuits)`;
    }

    // Préparer les données pour l'insertion
    const planningSlots = missionsToImport.map(m => ({
      mission_id: m.id,
      title: m.titre,
      description: m.description,
      date: m.date_mission || new Date().toISOString().split('T')[0],
      start_time: '08:00',
      end_time: '18:00',
      user_id: userId
    }));

    // Insérer les nouvelles missions dans le planning
    const { data: insertedSlots, error: insertError } = await supabase
      .from('user_mission_planning')
      .insert(planningSlots)
      .select();

    if (insertError) {
      logger.error('Erreur lors de l\'import des missions dans le planning:', insertError);
      res.status(500).json({ error: 'Erreur lors de l\'import des missions dans le planning' });
      return;
    }

    // Invalider le cache du planning pour cet utilisateur
    await planningCacheService.invalidateUserCache(userId);
    
    // Invalider également les caches de toutes les missions importées
    const missionIds = missionsToImport.map(m => m.id);
    for (const missionId of missionIds) {
      await planningCacheService.invalidateMissionCache(missionId);
    }

    res.status(201).json({
      message: `${insertedSlots.length} mission(s) importée(s) avec succès${limitMessage}`,
      missions: insertedSlots,
      limitReached: !isPremium && missionsLimit > 0 && uniqueMissionsToImport.length > missionsLimit
    });

  } catch (error) {
    logger.error('Erreur lors de l\'import des missions dans le planning:', error);
    res.status(500).json({ error: 'Erreur lors de l\'import des missions dans le planning' });
  }
});

// Générer un lien de synchronisation avec Google Calendar
router.get('/google-calendar-sync', planningRateLimiter, authMiddleware.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.userId) {
      res.status(401).json({ error: 'Non autorisé' });
      return;
    }

    // Générer une URL unique pour cet utilisateur basée sur le userId
    // Ajouter un "sel" pour rendre le hash plus sécurisé (idéalement, utiliser une variable d'environnement)
    const secret = process.env.JWT_SECRET || 'jobpartiel-secure-calendar-secret';
    const hashInput = `${req.user.userId}:${secret}:${Date.now().toString().slice(0, -3)}`;
    const userIdHash = Buffer.from(hashInput).toString('base64').replace(/=/g, '');
    
    // Construire l'URL de l'endpoint iCal avec un jeton sécurisé
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://api.jobpartiel.fr'
      : 'http://localhost:8080';
    
    // L'URL du flux iCal accessible publiquement (mais sécurisée)
    const icalFeedUrl = `${baseUrl}/api/planning/ical-feed/${userIdHash}`;
    
    // Construire l'URL Google Calendar avec le flux iCal
    // Utiliser le format ?cid= qui ajoute directement le calendrier sans passer par la page de configuration
    const webcalUrl = icalFeedUrl.replace(/^https?:\/\//, 'webcal://');
    
    // Google Calendar accepte directement les URL webcal:// dans le paramètre cid
    const googleCalendarUrl = `https://calendar.google.com/calendar/r?cid=${encodeURIComponent(webcalUrl)}`;
    
    // logger.info('URL de synchronisation Google Calendar générée:', { 
    //   userId: req.user.userId,
    //   icalFeedUrl,
    //   googleCalendarUrl
    // });
    
    res.status(200).json({ 
      googleCalendarUrl,
      icalFeedUrl
    });
  } catch (error) {
    logger.error('Erreur lors de la génération de l\'URL de synchronisation Google Calendar:', error);
    res.status(500).json({ error: 'Erreur lors de la génération de l\'URL de synchronisation' });
  }
});

// Endpoint public pour le flux iCal (utilisé par Google Calendar)
router.get('/ical-feed/:userIdHash', async (req: ExpressRequest, res: Response): Promise<void> => {
  try {
    const { userIdHash } = req.params;
    
    // Décoder le userIdHash
    let decodedHash;
    try {
      decodedHash = Buffer.from(userIdHash, 'base64').toString('utf-8');
    } catch (e) {
      logger.warn('Tentative d\'accès avec un hash mal formé:', { userIdHash });
      res.status(404).send('Not Found');
      return;
    }
    
    // Le format du hash est maintenant "userId:secret:timestamp"
    const parts = decodedHash.split(':');
    if (parts.length !== 3) {
      logger.warn('Format de hash invalide:', { userIdHash });
      res.status(404).send('Not Found');
      return;
    }
    
    const userId = parts[0];
    const providedSecret = parts[1];
    const timestamp = parseInt(parts[2]);
    
    // Vérifier le secret (devrait correspondre à celui utilisé pour la génération)
    const expectedSecret = process.env.JWT_SECRET || 'jobpartiel-secure-calendar-secret';
    if (providedSecret !== expectedSecret) {
      logger.warn('Secret invalide dans le hash:', { userIdHash });
      res.status(404).send('Not Found');
      return;
    }
    
    // Vérifier si le timestamp n'est pas trop ancien (par exemple, moins de 30 jours)
    const currentTime = Math.floor(Date.now() / 1000);
    const maxAge = 30 * 24 * 60 * 60; // 30 jours en secondes
    if (currentTime - timestamp > maxAge) {
      logger.warn('Lien iCal expiré:', { userIdHash, timestamp, currentTime });
      res.status(410).send('Gone - Ce lien a expiré. Veuillez générer un nouveau lien dans l\'application JobPartiel.');
      return;
    }
    
    // Vérifier que l'utilisateur existe
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();
      
    if (userError || !user) {
      logger.warn('Tentative d\'accès au flux iCal avec un userIdHash invalide:', { userIdHash });
      res.status(404).send('Not Found');
      return;
    }
    
    // logger.info('Génération du flux iCal pour l\'utilisateur:', { userId });

    // Étape 1: Récupérer les missions où l'utilisateur est le propriétaire
    const { data: userMissions, error: userMissionsError } = await supabase
      .from('user_missions')
      .select('id')
      .eq('user_id', userId);
      
    if (userMissionsError) {
      logger.error('Erreur lors de la récupération des missions de l\'utilisateur:', { userId, error: userMissionsError });
      throw userMissionsError;
    }
    
    // Récupérer les IDs des missions créées par l'utilisateur
    const userMissionIds = userMissions ? userMissions.map(m => m.id) : [];
    
    // Étape 2: Récupérer les missions où l'utilisateur est jobbeur accepté
    const { data: acceptedCandidatures, error: candidaturesError } = await supabase
      .from('user_mission_candidature')
      .select('mission_id')
      .eq('jobbeur_id', userId)
      .eq('statut', 'acceptée');
      
    if (candidaturesError) {
      logger.error('Erreur lors de la récupération des candidatures acceptées:', { userId, error: candidaturesError });
      throw candidaturesError;
    }
    
    // Récupérer les IDs des missions où l'utilisateur est jobbeur accepté
    const acceptedMissionIds = acceptedCandidatures ? acceptedCandidatures.map(c => c.mission_id) : [];
    
    // Combiner les deux ensembles d'IDs
    const allMissionIds = [...new Set([...userMissionIds, ...acceptedMissionIds])];
    
    // logger.info('Missions liées à l\'utilisateur:', { 
    //   userId, 
    //   ownedCount: userMissionIds.length,
    //   acceptedCount: acceptedMissionIds.length,
    //   totalCount: allMissionIds.length
    // });
    
    // Récupérer les entrées de planning en 2 étapes
    
    // 1. D'abord les entrées liées à des missions
    let planningEntries: any[] = [];
    
    if (allMissionIds.length > 0) {
      // Récupérer les entrées de planning liées aux missions identifiées
      const { data: linkedEntries, error: linkedError } = await supabase
        .from('user_mission_planning')
        .select(`
          id,
          mission_id,
          title,
          description,
          start_time,
          end_time,
          date,
          montant_propose,
          payment_method,
          mission:user_missions (
            id,
            titre,
            description,
            user_id
          )
        `)
        .eq('is_deleted', false)
        .in('mission_id', allMissionIds)
        .order('date', { ascending: true });
        
      if (linkedError) {
        logger.error('Erreur lors de la récupération des entrées liées du planning:', { userId, error: linkedError });
        throw linkedError;
      }
      
      if (linkedEntries) {
        // Récupérer les informations des candidatures acceptées pour obtenir les montants
        const missionIds = linkedEntries.map(entry => entry.mission_id).filter(Boolean);
        const { data: candidatures } = await supabase
          .from('user_mission_candidature')
          .select(`
            id,
            mission_id,
            montant_propose,
            montant_contre_offre,
            montant_contre_offre_jobbeur,
            statut,
            payment_status,
            payment_date,
            montant_paiement,
            jobbeur_id
          `)
          .in('mission_id', missionIds)
          .eq('jobbeur_id', userId)
          .eq('statut', 'acceptée');
        
        // Créer un map des propositions pour une recherche rapide
        const propositionsMap = new Map();
        if (candidatures) {
          candidatures.forEach(candidature => {
            propositionsMap.set(candidature.mission_id, {
              id: candidature.id,
              statut: candidature.statut,
              montant_propose: candidature.montant_propose,
              montant_contre_offre: candidature.montant_contre_offre,
              montant_contre_offre_jobbeur: candidature.montant_contre_offre_jobbeur,
              payment_status: candidature.payment_status,
              payment_date: candidature.payment_date,
              montant_paiement: candidature.montant_paiement,
              // Utiliser la valeur de payment_method depuis la mission ou défaut 'jobi_only'
              payment_method: 'jobi_only',
              jobbeur_id: candidature.jobbeur_id
            });
          });
        }
        
        // Enrichir les entrées du planning avec les propositions
        planningEntries = linkedEntries.map(entry => {
          let updatedEntry = { ...entry } as any; // Utiliser 'as any' pour permettre l'ajout de propriétés arbitraires
          
          // Ajouter les informations de proposition si disponibles
          if (entry.mission_id && propositionsMap.has(entry.mission_id)) {
            updatedEntry = {
              ...updatedEntry,
              proposition: propositionsMap.get(entry.mission_id)
            };
          } 
          // Si pas de proposition mais entry a un montant_propose, l'utiliser directement
          else if (entry.montant_propose && entry.montant_propose > 0) {
            // Pas besoin de créer une proposition artificielle, car le montant est déjà dans entry
          }
          // Si entry.mission existe mais pas de proposition, utiliser les données de la mission
          else if (entry.mission && (entry.mission as any).budget && (entry.mission as any).budget_defini) {
            updatedEntry.montant_propose = (entry.mission as any).budget;
            updatedEntry.payment_method = (entry.mission as any).payment_method || 'jobi_only';
          }
          
          return updatedEntry;
        });
      }
    }
    
    // 2. Récupérer les entrées manuelles (sans mission_id)
    // Maintenant avec le filtre user_id pour ne récupérer que les entrées de l'utilisateur
    const { data: manualEntries, error: manualError } = await supabase
      .from('user_mission_planning')
      .select(`
        id,
        mission_id,
        title,
        description,
        start_time,
        end_time,
        date,
        montant_propose,
        payment_method,
        user_id
      `)
      .eq('is_deleted', false)
      .is('mission_id', null)
      .eq('user_id', userId)
      .order('date', { ascending: true });
      
    if (manualError) {
      logger.error('Erreur lors de la récupération des entrées manuelles du planning:', { userId, error: manualError });
      throw manualError;
    }
    
    // Ajouter les entrées manuelles de l'utilisateur à l'export iCal
    if (manualEntries && manualEntries.length > 0) {
      planningEntries = [...planningEntries, ...manualEntries];
      
      // logger.info('Entrées manuelles ajoutées au feed iCal:', {
      //   userId,
      //   count: manualEntries.length
      // });
    }
    
    // logger.info('Total des entrées du planning récupérées pour feed iCal:', { 
    //   userId, 
    //   count: planningEntries.length
    // });
    
    // Limiter le nombre de créneaux exportés selon l'abonnement
    const { isPremium, planningSlotsLimit } = await getUserSubscriptionLimits(userId);
    let warning = '';
    if (!isPremium && planningSlotsLimit > 0 && planningEntries.length > planningSlotsLimit) {
      // Trier par date croissante et ne garder que la limite autorisée
      planningEntries = planningEntries
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, planningSlotsLimit);
      warning = `ATTENTION : Vous avez atteint la limite de ${planningSlotsLimit} créneaux exportés pour les comptes gratuits. Passez à l'abonnement premium pour synchroniser l'intégralité de votre planning.`;
    }

    // Générer le contenu iCal
    let icalContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//JobPartiel//Planning//FR',
      'X-WR-CALNAME:JobPartiel - Planning',
      `X-WR-CALDESC:Calendrier des missions JobPartiel${warning ? ' - ' + warning : ''}`,
      'REFRESH-INTERVAL;VALUE=DURATION:PT12H',
      'X-PUBLISHED-TTL:PT12H',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      // Définition du fuseau horaire Europe/Paris
      'BEGIN:VTIMEZONE',
      'TZID:Europe/Paris',
      'X-LIC-LOCATION:Europe/Paris',
      'BEGIN:DAYLIGHT',
      'TZOFFSETFROM:+0100',
      'TZOFFSETTO:+0200',
      'TZNAME:CEST',
      'DTSTART:19700329T020000',
      'RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU',
      'END:DAYLIGHT',
      'BEGIN:STANDARD',
      'TZOFFSETFROM:+0200',
      'TZOFFSETTO:+0100',
      'TZNAME:CET',
      'DTSTART:19701025T030000',
      'RRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU',
      'END:STANDARD',
      'END:VTIMEZONE'
    ];

    if (planningEntries.length > 0) {
      planningEntries.forEach((mission: any, idx: number) => {
        // Utiliser le titre de la mission ou le titre du créneau pour les missions manuelles
        const titre = mission.mission?.titre || mission.title;
        // Utiliser la description de la mission ou la description du créneau pour les missions manuelles
        let description = mission.mission?.description || mission.description;
        // Ajouter l'avertissement dans la description du premier événement si warning
        if (idx === 0 && warning) {
          description = `${warning}\n\n${description || ''}`;
        }
        
        // Générer un UID vraiment unique qui respecte le standard RFC 
        const uniqueId = `jobpartiel-mission-${mission.id}-${userId.slice(0, 8)}@jobpartiel.fr`;
        
        // Générer les dates au format UTC (Z) comme requis par Google Calendar
        const datestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        
        // Construire la date de début avec le fuseau horaire Europe/Paris
        // Ajouter une propriété TZID pour spécifier le fuseau horaire
        const startDate = `${mission.date.replace(/-/g, '')}T${mission.start_time.replace(/:/g, '')}00`;
        const endDate = `${mission.date.replace(/-/g, '')}T${mission.end_time.replace(/:/g, '')}00`;
        
        // Ajouter des informations spécifiques pour Google Calendar
        let descriptionContent = description || '';
        
        // Déterminer le montant à afficher (comme dans le frontend)
        let montantAffiche = 0;
        let modePayment = mission.payment_method || 'jobi_only';
        
        // Si c'est une mission manuelle ou si le montant est déjà défini dans la mission
        if (mission.montant_propose && mission.montant_propose > 0) {
          montantAffiche = mission.montant_propose;
        } 
        // Sinon, vérifier les informations de proposition
        else if (mission.proposition) {
          // Vérifier les priorités de montant selon le statut
          if (mission.proposition.statut === 'contre_offre_jobbeur' && mission.proposition.montant_contre_offre_jobbeur !== null) {
            montantAffiche = mission.proposition.montant_contre_offre_jobbeur;
          } else if (mission.proposition.statut === 'contre_offre' && mission.proposition.montant_contre_offre !== null) {
            montantAffiche = mission.proposition.montant_contre_offre;
          } else {
            montantAffiche = mission.proposition.montant_propose || 0;
          }
          
          // Récupérer également le mode de paiement de la proposition
          modePayment = mission.proposition.payment_method || modePayment;
        }
        
        // Créer le suffixe de montant pour le titre et la description
        let montantTexte = '';
        if (montantAffiche > 0) {
          const devise = modePayment === 'direct_only' ? '€' : 'J';
          montantTexte = `${montantAffiche} ${devise}`;
          
          // Ajouter le montant de façon très visible en haut de la description
          descriptionContent = `MONTANT: ${montantTexte}\n\n${descriptionContent}`;
        }
        
        // Échapper les caractères spéciaux dans la description selon les spécifications iCal
        const escapedDescription = descriptionContent
          .replace(/\\/g, '\\\\')
          .replace(/;/g, '\\;')
          .replace(/,/g, '\\,')
          .replace(/\n/g, '\\n');

        // Ajouter le montant au titre pour qu'il soit toujours visible
        const titreAvecMontant = montantTexte ? `${titre || 'Mission JobPartiel'} (${montantTexte})` : (titre || 'Mission JobPartiel');

        icalContent = icalContent.concat([
          'BEGIN:VEVENT',
          `UID:${uniqueId}`,
          `DTSTAMP:${datestamp}`,
          `DTSTART;TZID=Europe/Paris:${startDate}`,
          `DTEND;TZID=Europe/Paris:${endDate}`,
          `SUMMARY:${titreAvecMontant}`,
          `DESCRIPTION:${escapedDescription}`,
          'STATUS:CONFIRMED',
          'TRANSP:OPAQUE',
          `CREATED:${datestamp}`,
          `LAST-MODIFIED:${datestamp}`,
          `SEQUENCE:0`,
          'BEGIN:VALARM',
          'ACTION:DISPLAY',
          'DESCRIPTION:Rappel de mission JobPartiel',
          'TRIGGER:-PT1H',
          'END:VALARM',
          'END:VEVENT'
        ]);
      });
    }

    icalContent.push('END:VCALENDAR');

    // Envoyer la réponse
    res.setHeader('Content-Type', 'text/calendar; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename=jobpartiel-planning.ics');
    // Permettre la mise en cache pendant 1 heure mais autoriser les mises à jour
    res.setHeader('Cache-Control', 'public, max-age=3600, must-revalidate');
    res.send(icalContent.join('\r\n'));
    
    // logger.info('Flux iCal généré avec succès pour:', { userId: userId });
  } catch (error) {
    logger.error('Erreur lors de la génération du flux iCal:', error);
    res.status(500).send('Internal Server Error');
  }
});

// Fonction pour récupérer les IDs des missions dont l'utilisateur est propriétaire
async function getUserOwnedMissionIds(userId: string): Promise<string[]> {
  const { data: ownedMissions, error } = await supabase
    .from('user_missions')
    .select('id')
    .eq('user_id', userId)
    .in('statut', ['en_attente', 'en_cours', 'terminee']);
  
  if (error) {
    logger.error('Erreur lors de la récupération des missions propriétaires:', error);
    return [];
  }
  
  return ownedMissions ? ownedMissions.map(m => m.id) : [];
}

export const planningRoutes = router;