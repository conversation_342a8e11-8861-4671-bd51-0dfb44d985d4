import React, { useEffect, useState } from 'react';
import {
  Box, Container, Typography, Paper, Grid, Button, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Select, MenuItem, FormControl, InputLabel, CircularProgress, TextField, Avatar
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';

const EntrepriseDocumentValidation: React.FC = () => {
  const [usersDocs, setUsersDocs] = useState<any[]>([]); // [{ user, docs: [] }]
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState('pending');
  const [typeFilter, setTypeFilter] = useState('');
  const [search, setSearch] = useState('');
  const navigate = useNavigate();

  // Récupérer les documents groupés par utilisateur
  const fetchDocs = async () => {
    setLoading(true);
    try {
      const headers = await getCommonHeaders();
      const params: any = {};
      if (statusFilter) params.status = statusFilter;
      if (typeFilter) params.type = typeFilter;
      if (search) params.search = search;
      params.page = page + 1;
      params.limit = rowsPerPage;
      // Construction de la query string
      const queryString = new URLSearchParams(params).toString();
      const res = await fetch(`${API_CONFIG.baseURL}/api/users/verification/entreprise/list?${queryString}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });
      const data = await res.json();
      const docs = data?.docs || [];
      const grouped = docs.reduce((acc: any, doc: any) => {
        let user = acc.find((u: any) => u.user.id === doc.user_id);
        if (!user) {
          user = {
            user: {
              id: doc.user_id,
              email: doc.users?.email || '',
              nom: doc.user_profil?.nom || '',
              prenom: doc.user_profil?.prenom || '',
              profil_verifier: doc.users?.profil_verifier,
              identite_verifier: doc.users?.identite_verifier,
              entreprise_verifier: doc.users?.entreprise_verifier,
              assurance_verifier: doc.users?.assurance_verifier,
            },
            docs: []
          };
          acc.push(user);
        }
        user.docs.push(doc);
        return acc;
      }, []);
      setUsersDocs(grouped);
      setTotal(grouped.length);
    } catch (e) {
      setUsersDocs([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocs();
    // eslint-disable-next-line
  }, [page, rowsPerPage, statusFilter, typeFilter]);

  return (
    <Box sx={{ background: 'linear-gradient(to bottom, #FFF8F3, white)', minHeight: '100vh', py: 4 }}>
      <Container maxWidth="lg">
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#FF6B2C', mb: 2 }}>
          Validation des documents d'entreprise
        </Typography>
        <Paper sx={{ p: 2, mb: 3, borderRadius: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Statut</InputLabel>
                <Select value={statusFilter} label="Statut" onChange={e => { setStatusFilter(e.target.value); setPage(0); }}>
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="pending">En attente</MenuItem>
                  <MenuItem value="approved">Validé</MenuItem>
                  <MenuItem value="rejected">Rejeté</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select value={typeFilter} label="Type" onChange={e => { setTypeFilter(e.target.value); setPage(0); }}>
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="kbis">Kbis</MenuItem>
                  <MenuItem value="assurance">Assurance</MenuItem>
                  <MenuItem value="identity">Identité</MenuItem>
                  <MenuItem value="autre">Autre</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 4 }}>
              <TextField
                fullWidth
                size="small"
                label="Recherche (nom, email, etc.)"
                value={search}
                onChange={e => setSearch(e.target.value)}
                onKeyDown={e => { if (e.key === 'Enter') { setPage(0); fetchDocs(); } }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 2 }}>
              <Button variant="contained" sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 600 }} onClick={() => { setPage(0); fetchDocs(); }}>
                Rechercher
              </Button>
            </Grid>
          </Grid>
        </Paper>
        <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: '#FFF8F3' }}>
                <TableRow>
                  <TableCell>Utilisateur</TableCell>
                  <TableCell>Nb documents</TableCell>
                  <TableCell>Statuts</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <CircularProgress sx={{ color: '#FF6B2C' }} />
                    </TableCell>
                  </TableRow>
                ) : usersDocs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      Aucun document à valider.
                    </TableCell>
                  </TableRow>
                ) : usersDocs.map(({ user, docs }) => (
                  <TableRow
                    key={user.id}
                    hover
                    sx={{ cursor: 'pointer' }}
                    onClick={() => navigate(`/admin/entreprise-documents/${user.id}`)}
                  >
                    <TableCell sx={{ verticalAlign: 'top', minWidth: 220 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 700 }}>
                          {user.prenom?.[0] || user.nom?.[0] || user.email?.[0] || '?'}
                        </Avatar>
                        <Box>
                          <Typography sx={{ fontWeight: 700 }}>{user.prenom} {user.nom}</Typography>
                          <Typography sx={{ color: '#888', fontSize: 13 }}>{user.email}</Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={docs.filter((d: any) => d.status === 'pending' || d.status === 'en attente').length} sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 700 }} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip label={user.profil_verifier ? 'Profil vérifié' : 'Profil non vérifié'} size="small" sx={{ bgcolor: user.profil_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                        <Chip label={user.identite_verifier ? 'Identité vérifiée' : 'Identité non vérifiée'} size="small" sx={{ bgcolor: user.identite_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                        <Chip label={user.entreprise_verifier ? 'Entreprise vérifiée' : 'Entreprise non vérifiée'} size="small" sx={{ bgcolor: user.entreprise_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                        <Chip label={user.assurance_verifier ? 'Assurance vérifiée' : 'Assurance non vérifiée'} size="small" sx={{ bgcolor: user.assurance_verifier ? '#4CAF50' : '#FFB300', color: 'white', fontWeight: 600 }} />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        sx={{ color: '#FF6B2C', fontWeight: 600 }}
                        onClick={e => { e.stopPropagation(); navigate(`/admin/entreprise-documents/${user.id}`); }}
                      >
                        Voir les documents
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={total}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={e => { setRowsPerPage(parseInt(e.target.value, 10)); setPage(0); }}
            rowsPerPageOptions={[10, 20, 50]}
            labelRowsPerPage="Utilisateurs par page"
          />
        </Paper>
      </Container>
    </Box>
  );
};

export default EntrepriseDocumentValidation; 