/* Permet de traiter la file d'attente d'emails toutes les 2 minutes */

import { supabase } from '../config/supabase';
import { sendEmailWithRetry, testSmtpConnection } from './emailService';
import { decryptDataAsync } from '../utils/encryption';
import logger from '../utils/logger';

let isProcessing = false;
const MAX_RETRY_COUNT = 3; // Nombre maximum de tentatives pour un email (réduit)
const RETRY_DELAY_HOURS = 0.25; // D<PERSON>lai entre les tentatives en heures (réduit à 15 minutes)
const MAX_WAIT_HOURS = 1; // D<PERSON>lai maximum d'attente pour un email (réduit à 1 heure)
const STUCK_EMAIL_THRESHOLD_HOURS = 2; // Emails bloqués depuis plus de 2 heures

export async function processQueue() {
  if (isProcessing) {
    logger.info('[EmailQueue] Traitement déjà en cours, ignoré');
    return;
  }

  isProcessing = true;
  logger.info('[EmailQueue] Début du traitement de la file d\'attente d\'emails');

  try {
    let totalSent = 0;
    let totalError = 0;
    
    // Vérifier le nombre total d'emails en attente dans la file
    const { count: pendingCount, error: countError } = await supabase
      .from('email_queue')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'pending');
      
    if (!countError) {
      logger.info(`[EmailQueue] Nombre total d'emails en attente dans la file: ${pendingCount}`);
    }

    // Vérifier la connexion SMTP avant de commencer
    try {
      await testSmtpConnection();
      logger.info('[EmailQueue] Connexion SMTP vérifiée avec succès');
    } catch (smtpError) {
      logger.info('[EmailQueue] Erreur de connexion SMTP, traitement interrompu:', smtpError);
      return; // Ne pas traiter les emails si la connexion SMTP échoue
    }

    // Corriger les dates futures dans created_at
    const now = new Date();
    const { data: futureEmails, error: futureError } = await supabase
      .from('email_queue')
      .select('id, created_at')
      .gt('created_at', now.toISOString());
      
    // Détecter et corriger les emails bloqués depuis trop longtemps
    const stuckThreshold = new Date();
    stuckThreshold.setHours(stuckThreshold.getHours() - STUCK_EMAIL_THRESHOLD_HOURS);
    
    const { data: stuckEmails, error: stuckError } = await supabase
      .from('email_queue')
      .select('id, created_at, retry_count')
      .eq('status', 'pending')
      .lt('created_at', stuckThreshold.toISOString())
      .gt('retry_count', 0) // Emails qui ont déjà été tentés au moins une fois
      .limit(10);
      
    if (stuckError) {
      logger.info('[EmailQueue] Erreur lors de la vérification des emails bloqués:', stuckError);
    } else if (stuckEmails && stuckEmails.length > 0) {
      logger.info(`[EmailQueue] ${stuckEmails.length} emails bloqués détectés, réinitialisation en cours...`);
      
      for (const email of stuckEmails) {
        await supabase
          .from('email_queue')
          .update({ last_retry: null }) // Réinitialiser la dernière tentative pour forcer le traitement
          .eq('id', email.id);
      }
      
      logger.info(`[EmailQueue] ${stuckEmails.length} emails bloqués réinitialisés pour traitement prioritaire`);
    }

    if (futureError) {
      logger.info('[EmailQueue] Erreur lors de la vérification des emails avec dates futures:', futureError);
    } else if (futureEmails && futureEmails.length > 0) {
      logger.info(`[EmailQueue] ${futureEmails.length} emails avec dates futures détectés, correction en cours...`);

      for (const email of futureEmails) {
        await supabase
          .from('email_queue')
          .update({ created_at: now.toISOString() })
          .eq('id', email.id);
      }

      logger.info(`[EmailQueue] ${futureEmails.length} emails avec dates futures corrigés`);
    }

    while (true) {
      // Calculer la date limite pour les emails prêts à être traités
      const retryThreshold = new Date();
      retryThreshold.setHours(retryThreshold.getHours() - RETRY_DELAY_HOURS);
      
      // Calculer la date limite pour les emails en attente depuis trop longtemps
      const maxWaitThreshold = new Date();
      maxWaitThreshold.setHours(maxWaitThreshold.getHours() - MAX_WAIT_HOURS);

      // Récupérer les emails en attente qui sont prêts à être traités
      // Priorité 1: Emails en attente depuis plus de MAX_WAIT_HOURS
      // Priorité 2: Emails qui n'ont jamais été tentés ou dont la dernière tentative est antérieure au seuil
      const { data: emails, error } = await supabase
        .from('email_queue')
        .select('*')
        .eq('status', 'pending')
        .or(`created_at.lt.${maxWaitThreshold.toISOString()},last_retry.is.null,last_retry.lt.${retryThreshold.toISOString()}`)
        .order('created_at', { ascending: true })
        .limit(20); // Augmentation de la limite pour traiter plus d'emails à chaque exécution

      if (error) {
        logger.info('[EmailQueue] Erreur récupération file email:', error);
        break;
      }

      if (!emails || emails.length === 0) break;

      for (const email of emails) {
        // Vérifier si l'email a des métadonnées de retry
        const retryCount = email.retry_count || 0;

        // Si l'email a déjà été tenté trop de fois, le marquer comme en erreur
        if (retryCount >= MAX_RETRY_COUNT) {
          await supabase
            .from('email_queue')
            .update({
              status: 'error',
              error_message: `Nombre maximum de tentatives (${MAX_RETRY_COUNT}) atteint`
            })
            .eq('id', email.id);

          // Déchiffrer l'adresse email pour les logs
          const decryptedEmailAbandoned = await decryptDataAsync(email.to_email);

          logger.info('[EmailQueue] Email abandonné après trop de tentatives', {
            id: email.id,
            to: decryptedEmailAbandoned,
            subject: email.subject,
            retryCount
          });

          totalError++;
          continue;
        }

        try {
          // Déchiffrer l'adresse email avant l'envoi
          const decryptedEmail = await decryptDataAsync(email.to_email);

          // Bloquer les emails anonymisés pour le RGPD
          if (decryptedEmail.includes('@supprime.local')) {
            await supabase
              .from('email_queue')
              .update({
                status: 'error',
                error_message: 'Email anonymisé bloqué (RGPD)'
              })
              .eq('id', email.id);

            logger.warn('[EmailQueue] Email anonymisé bloqué (RGPD)', {
              id: email.id,
              email: decryptedEmail.substring(0, 20) + '...',
              subject: email.subject
            });

            totalError++;
            continue;
          }

          // Préparer les options d'email
          const mailOptions: any = {
            to: decryptedEmail,
            subject: email.subject,
            html: email.html,
            from: `"Job Partiel" <<EMAIL>>`
          };

          // Ajouter les attachments s'ils existent
          if (email.attachments) {
            try {
              mailOptions.attachments = JSON.parse(email.attachments);
            } catch (parseError) {
              logger.error('[EmailQueue] Erreur lors du parsing des attachments:', parseError);
            }
          }

          await sendEmailWithRetry(mailOptions);

          // Marquer l'email comme envoyé
          await supabase
            .from('email_queue')
            .update({
              status: 'sent',
              sent_at: new Date().toISOString(),
              retry_count: retryCount + 1
            })
            .eq('id', email.id);

          logger.info('[EmailQueue] Email envoyé avec succès', {
            id: email.id,
            to: decryptedEmail,
            subject: email.subject,
            attempts: retryCount + 1
          });

          totalSent++;
        } catch (err: any) {
          // Déchiffrer l'adresse email pour les logs d'erreur
          const decryptedEmail2 = await decryptDataAsync(email.to_email);

          // Calculer le délai avant la prochaine tentative (diminue avec le nombre de tentatives)
          // Plus un email a été tenté, plus vite on réessaie
          const nextRetryDelay = Math.max(RETRY_DELAY_HOURS / (retryCount + 1), 0.1); // Minimum 6 minutes
          
          const nextRetryDate = new Date();
          nextRetryDate.setHours(nextRetryDate.getHours() + nextRetryDelay);
          
          // Mettre à jour le compteur de tentatives et la date de dernière tentative
          await supabase
            .from('email_queue')
            .update({
              status: 'pending', // Garder en pending pour réessayer plus tard
              error_message: err.message,
              retry_count: retryCount + 1,
              last_retry: now.toISOString()
            })
            .eq('id', email.id);

          logger.info('[EmailQueue] Erreur envoi email (sera réessayé)', {
            id: email.id,
            to: decryptedEmail2,
            subject: email.subject,
            error: err.message,
            retryCount: retryCount + 1
          });

          totalError++;
        }

        // Throttling SMTP réduit : 100ms entre chaque email pour plus de rapidité
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Nettoyage : suppression des emails sent/error vieux de plus de 12 mois
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
    const isoDate = twelveMonthsAgo.toISOString();

    const { error: delError } = await supabase
      .from('email_queue')
      .delete()
      .or(`status.eq.sent,status.eq.error`)
      .lt('created_at', isoDate);

    if (delError) {
      logger.error('[EmailQueue] Erreur lors du nettoyage des anciens emails', delError);
    } else {
      logger.info('[EmailQueue] Nettoyage des emails sent/error vieux de plus de 12 mois effectué');
    }

    logger.info(`[EmailQueue] Fin du traitement : ${totalSent} envoyés, ${totalError} en erreur.`);
  } catch (error) {
    logger.info('[EmailQueue] Erreur inattendue lors du traitement de la file d\'attente:', error);
  } finally {
    isProcessing = false;
  }
}

// Exécuter le script si appelé directement (pour les tests ou l'exécution manuelle)
if (require.main === module) {
  // Boucle continue intelligente : 5s si emails, 10s si file vide
  (async function loop() {
    while (true) {
      const hadEmails = await processQueueWithReturn();
      if (hadEmails) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5s si emails envoyés
      } else {
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10s si file vide
      }
    }
  })();
}

// Nouvelle fonction qui retourne true si des emails ont été traités
async function processQueueWithReturn() {
  if (isProcessing) {
    logger.info('[EmailQueue] Traitement déjà en cours, ignoré');
    return false;
  }
  isProcessing = true;
  let hadEmails = false;
  try {
    let totalSent = 0;
    let totalError = 0;
    
    // Vérifier le nombre total d'emails en attente dans la file
    const { count: pendingCount, error: countError } = await supabase
      .from('email_queue')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'pending');
      
    if (!countError) {
      logger.info(`[EmailQueue] Nombre total d'emails en attente dans la file: ${pendingCount}`);
    }
    try {
      await testSmtpConnection();
      logger.info('[EmailQueue] Connexion SMTP vérifiée avec succès');
    } catch (smtpError) {
      logger.info('[EmailQueue] Erreur de connexion SMTP, traitement interrompu:', smtpError);
      return false;
    }
    const now = new Date();
    const { data: futureEmails, error: futureError } = await supabase
      .from('email_queue')
      .select('id, created_at')
      .gt('created_at', now.toISOString());
      
    // Détecter et corriger les emails bloqués depuis trop longtemps
    const stuckThreshold = new Date();
    stuckThreshold.setHours(stuckThreshold.getHours() - STUCK_EMAIL_THRESHOLD_HOURS);
    
    const { data: stuckEmails, error: stuckError } = await supabase
      .from('email_queue')
      .select('id, created_at, retry_count')
      .eq('status', 'pending')
      .lt('created_at', stuckThreshold.toISOString())
      .gt('retry_count', 0) // Emails qui ont déjà été tentés au moins une fois
      .limit(10);
      
    if (stuckError) {
      logger.info('[EmailQueue] Erreur lors de la vérification des emails bloqués:', stuckError);
    } else if (stuckEmails && stuckEmails.length > 0) {
      logger.info(`[EmailQueue] ${stuckEmails.length} emails bloqués détectés, réinitialisation en cours...`);
      
      for (const email of stuckEmails) {
        await supabase
          .from('email_queue')
          .update({ last_retry: null }) // Réinitialiser la dernière tentative pour forcer le traitement
          .eq('id', email.id);
      }
      
      logger.info(`[EmailQueue] ${stuckEmails.length} emails bloqués réinitialisés pour traitement prioritaire`);
    }
    if (futureError) {
      logger.info('[EmailQueue] Erreur lors de la vérification des emails avec dates futures:', futureError);
    } else if (futureEmails && futureEmails.length > 0) {
      logger.info(`[EmailQueue] ${futureEmails.length} emails avec dates futures détectés, correction en cours...`);
      for (const email of futureEmails) {
        await supabase
          .from('email_queue')
          .update({ created_at: now.toISOString() })
          .eq('id', email.id);
      }
      logger.info(`[EmailQueue] ${futureEmails.length} emails avec dates futures corrigés`);
    }
    while (true) {
      // Calculer la date limite pour les emails prêts à être traités
      const retryThreshold = new Date();
      retryThreshold.setHours(retryThreshold.getHours() - RETRY_DELAY_HOURS);
      
      // Calculer la date limite pour les emails en attente depuis trop longtemps
      const maxWaitThreshold = new Date();
      maxWaitThreshold.setHours(maxWaitThreshold.getHours() - MAX_WAIT_HOURS);

      // Récupérer les emails en attente qui sont prêts à être traités
      // Priorité 1: Emails en attente depuis plus de MAX_WAIT_HOURS
      // Priorité 2: Emails qui n'ont jamais été tentés ou dont la dernière tentative est antérieure au seuil
      const { data: emails, error } = await supabase
        .from('email_queue')
        .select('*')
        .eq('status', 'pending')
        .or(`created_at.lt.${maxWaitThreshold.toISOString()},last_retry.is.null,last_retry.lt.${retryThreshold.toISOString()}`)
        .order('created_at', { ascending: true })
        .limit(20); // Augmentation de la limite pour traiter plus d'emails à chaque exécution
      if (error) {
        logger.info('[EmailQueue] Erreur récupération file email:', error);
        break;
      }
      if (!emails || emails.length === 0) break;
      hadEmails = true;
      for (const email of emails) {
        const retryCount = email.retry_count || 0;
        if (retryCount >= MAX_RETRY_COUNT) {
          await supabase
            .from('email_queue')
            .update({
              status: 'error',
              error_message: `Nombre maximum de tentatives (${MAX_RETRY_COUNT}) atteint`
            })
            .eq('id', email.id);
          // Déchiffrer l'adresse email pour les logs
          const decryptedEmailAbandoned2 = await decryptDataAsync(email.to_email);

          logger.info('[EmailQueue] Email abandonné après trop de tentatives', {
            id: email.id,
            to: decryptedEmailAbandoned2,
            subject: email.subject,
            retryCount
          });
          totalError++;
          continue;
        }
        try {
          // Déchiffrer l'adresse email avant l'envoi
          const decryptedEmail2 = await decryptDataAsync(email.to_email);

          // Bloquer les emails anonymisés pour le RGPD
          if (decryptedEmail2.includes('@supprime.local')) {
            await supabase
              .from('email_queue')
              .update({
                status: 'error',
                error_message: 'Email anonymisé bloqué (RGPD)'
              })
              .eq('id', email.id);

            logger.warn('[EmailQueue] Email anonymisé bloqué (RGPD)', {
              id: email.id,
              email: decryptedEmail2.substring(0, 20) + '...',
              subject: email.subject
            });

            totalError++;
            continue;
          }

          await sendEmailWithRetry({
            to: decryptedEmail2,
            subject: email.subject,
            html: email.html,
            from: `"Job Partiel" <<EMAIL>>`
          });
          await supabase
            .from('email_queue')
            .update({
              status: 'sent',
              sent_at: new Date().toISOString(),
              retry_count: retryCount + 1
            })
            .eq('id', email.id);
          logger.info('[EmailQueue] Email envoyé avec succès', {
            id: email.id,
            to: decryptedEmail2,
            subject: email.subject,
            attempts: retryCount + 1
          });
          totalSent++;
        } catch (err: any) {
          // Déchiffrer l'adresse email pour les logs d'erreur
          const decryptedEmail2Error = await decryptDataAsync(email.to_email);

          // Calculer le délai avant la prochaine tentative (diminue avec le nombre de tentatives)
          // Plus un email a été tenté, plus vite on réessaie
          const nextRetryDelay = Math.max(RETRY_DELAY_HOURS / (retryCount + 1), 0.1); // Minimum 6 minutes
          
          const nextRetryDate = new Date();
          nextRetryDate.setHours(nextRetryDate.getHours() + nextRetryDelay);
          
          await supabase
            .from('email_queue')
            .update({
              status: 'pending',
              error_message: err.message,
              retry_count: retryCount + 1,
              last_retry: now.toISOString()
            })
            .eq('id', email.id);
          logger.info('[EmailQueue] Erreur envoi email (sera réessayé)', {
            id: email.id,
            to: decryptedEmail2Error,
            subject: email.subject,
            error: err.message,
            retryCount: retryCount + 1
          });
          totalError++;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    // Nettoyage : suppression des emails sent/error vieux de plus de 12 mois
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);
    const isoDate = twelveMonthsAgo.toISOString();
    const { error: delError } = await supabase
      .from('email_queue')
      .delete()
      .or(`status.eq.sent,status.eq.error`)
      .lt('created_at', isoDate);
    if (delError) {
      logger.error('[EmailQueue] Erreur lors du nettoyage des anciens emails', delError);
    } else {
      logger.info('[EmailQueue] Nettoyage des emails sent/error vieux de plus de 12 mois effectué');
    }
    logger.info(`[EmailQueue] Fin du traitement : ${totalSent} envoyés, ${totalError} en erreur.`);
  } catch (error) {
    logger.info('[EmailQueue] Erreur inattendue lors du traitement de la file d\'attente:', error);
  } finally {
    isProcessing = false;
  }
  return hadEmails;
}