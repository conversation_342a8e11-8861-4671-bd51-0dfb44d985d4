import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { useAuth } from '../contexts/AuthContext';
import logger from '../utils/logger';

// Cache simple pour éviter les requêtes répétées
const profileCache = new Map<string, { data: UserProfileData; timestamp: number }>();
const CACHE_DURATION = 30000; // 30 secondes

// Interface pour le type de retour
export interface UserProfileData {
  id: string;
  slug?: string;
  nom?: string;
  prenom?: string;
  nom_entreprise?: string;
  telephone?: string;
  adresse?: string;
  code_postal?: string;
  ville?: string;
  pays?: string;
  photo_url?: string;
  email?: string;
  note_moyenne?: number;
  nombre_avis?: number;
  role?: string;
  type_de_profil?: string;
}

interface UseUserProfileProps {
  userId?: string;
  slug?: string;
  enabled?: boolean;
  includePrivateData?: boolean;
}

interface UseUserProfileReturn {
  profileData: UserProfileData | null;
  isLoading: boolean;
  error: any;
  refetch: () => Promise<void>;
}

const useUserProfile = ({
  userId,
  slug,
  enabled = true,
  includePrivateData = false
}: UseUserProfileProps = {}): UseUserProfileReturn => {
  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);
  const { user } = useAuth();

  const fetchProfile = async () => {
    if (!enabled) return;

    // Créer une clé de cache unique
    const cacheKey = `${userId || 'current'}-${slug || 'current'}-${user?.id || 'none'}`;

    // Vérifier le cache
    const cached = profileCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      setProfileData(cached.data);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      let endpoint = '/api/users/profil';

      // Si un ID ou slug est spécifié, utiliser l'endpoint approprié
      if (slug) {
        endpoint = `/api/users/profil/${slug}`;
      } else if (userId) {
        // Récupérer d'abord le slug de l'utilisateur si on a son ID
        const slugResponse = await axios.get(`${API_CONFIG.baseURL}/api/users/get-slug/${userId}`, {
          headers: await getCommonHeaders(),
          withCredentials: true
        });

        if (slugResponse.data.success && slugResponse.data.slug) {
          endpoint = `/api/users/profil/${slugResponse.data.slug}`;
        } else {
          throw new Error("Impossible de récupérer le slug de l'utilisateur");
        }
      } else if (user && user.profil && user.profil.data.slug) {
        // Si aucun paramètre n'est fourni mais l'utilisateur est connecté, récupérer son propre profil
        endpoint = `/api/users/profil/${user.profil.data.slug}`;
      }

      logger.info('🔍 Endpoint utilisé pour récupérer le profil:', endpoint);
      const response = await axios.get(endpoint, API_CONFIG);
      const userData = response.data;

      if (!userData) {
        throw new Error("Aucune donnée de profil trouvée");
      }

      const formattedData: UserProfileData = {
        id: userData.id,
        slug: userData.profil?.data?.slug,
        nom: userData.profil?.data?.nom,
        prenom: userData.profil?.data?.prenom,
        nom_entreprise: userData.profil?.data?.nom_entreprise,
        ville: userData.profil?.data?.ville,
        pays: userData.profil?.data?.pays,
        photo_url: userData.profil?.data?.photo_url,
        email: userData.email,
        note_moyenne: userData.rating,
        nombre_avis: userData.total_reviews,
        role: userData.role,
        type_de_profil: userData.profil?.data?.type_de_profil,
      };

      // Ajouter les données privées si demandées et disponibles
      if (includePrivateData || (userId === user?.id) || (!userId && !slug)) {
        formattedData.telephone = userData.profil?.data?.telephone;
        formattedData.adresse = userData.profil?.data?.adresse;
        formattedData.code_postal = userData.profil?.data?.code_postal;
      }

      logger.info('🔍 Données de profil récupérées:', formattedData);
      setProfileData(formattedData);

      // Mettre en cache les données
      profileCache.set(cacheKey, {
        data: formattedData,
        timestamp: Date.now()
      });
    } catch (err) {
      logger.error('Erreur lors de la récupération du profil:', err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enabled) {
      fetchProfile();
    }
  }, [enabled, userId, slug, user?.id]);

  // Éviter les appels multiples si les données sont déjà en cours de chargement
  useEffect(() => {
    if (!enabled || isLoading) return;

    // Debounce pour éviter les appels multiples rapides
    const timeoutId = setTimeout(() => {
      if (enabled && !profileData) {
        fetchProfile();
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [enabled, profileData, isLoading]);

  return {
    profileData,
    isLoading,
    error,
    refetch: fetchProfile
  };
};

export default useUserProfile;