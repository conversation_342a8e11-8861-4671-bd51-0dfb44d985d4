import logger from "@/utils/logger";
import api from "./api";
import { fetchCsrfToken } from "./csrf";

// Récupérer tous les documents (devis, factures, avoirs)
export const getDocuments = async () => {
    try {
      const response = await api.get('/api/invoices');
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la récupération des documents:', error);
      throw error;
    }
  };
  
  // Récupérer un document par son ID
  export const getDocumentById = async (id: string) => {
    try {
      const response = await api.get(`/api/invoices/${id}`);
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la récupération du document:', error);
      throw error;
    }
  };
  
  // Récupérer l'historique d'un document par son ID
  export const getDocumentHistory = async (id: string) => {
    try {
      const response = await api.get(`/api/invoices/${id}/history`);
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la récupération de l\'historique du document:', error);
      throw error;
    }
  };
  
  // Récupérer tous les clients
  export const getClients = async () => {
    try {
      const response = await api.get('/api/users/clients');
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la récupération des clients:', error);
      throw error;
    }
  };
  
  // Sauvegarder un document (création ou mise à jour)
  export const saveDocument = async (document: any) => {
    try {
      if (document.id) {
        // Mise à jour
        const response = await api.put(`/api/invoices/${document.id}`, document);
        return response.data;
      } else {
        // Création
        await fetchCsrfToken();
        const csrfToken = await fetchCsrfToken();
        const response = await api.post('/api/invoices', document, {
          headers: { 'X-CSRF-Token': csrfToken },
        });
        return response.data;
      }
    } catch (error) {
      logger.error('[API] Erreur lors de la sauvegarde du document:', error);
      throw error;
    }
  };
  
  // Supprimer un document
  export const deleteDocument = async (id: string) => {
    try {
      const response = await api.delete(`/api/invoices/${id}`);
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la suppression du document:', error);
      throw error;
    }
  };
  
  // Télécharger un document au format PDF
  export const downloadDocument = async (id: string) => {
    try {
      const response = await api.get(`/api/invoices/${id}/pdf`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors du téléchargement du document:', error);
      throw error;
    }
  };
  
  // Envoyer un document par email
  export const sendDocumentByEmail = async (id: string, emailData: { email: string, message: string }) => {
    try {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/send`, emailData, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de l\'envoi du document par email:', error);
      throw error;
    }
  };
  
  // Convertir un devis en facture
  export const convertToInvoice = async (id: string) => {
    try {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/convert-to-invoice`, {}, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la conversion du devis en facture:', error);
      throw error;
    }
  };
  
  // Créer un avoir à partir d'une facture
  export const createCreditNote = async (id: string) => {
    try {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/credit-note`, {}, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la création de l\'avoir:', error);
      throw error;
    }
  };  