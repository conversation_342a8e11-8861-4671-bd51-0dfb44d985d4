import React from 'react';
import { Line, Group } from 'react-konva';

interface GuidesProps {
  width: number;
  height: number;
  selectedElement: any;
  elements: any[];
  visible?: boolean;
}

const Guides: React.FC<GuidesProps> = ({
  width,
  height,
  selectedElement,
  elements,
  visible = true
}) => {
  if (!visible || !selectedElement) return null;

  // Tolérance pour l'affichage des guides
  const TOLERANCE = 0.7;

  // Coordonnées du centre et des bords de l'élément sélectionné
  const selX = selectedElement.x;
  const selY = selectedElement.y;
  const selWidth = selectedElement.width || 0;
  const selHeight = selectedElement.height || 0;
  const selCenterX = selX + selWidth / 2;
  const selCenterY = selY + selHeight / 2;
  const selRight = selX + selWidth;
  const selBottom = selY + selHeight;

  // Guides pour l'alignement avec les autres éléments
  const guides: { points: number[]; key: string }[] = [];

  // Ajouter des guides pour le centre du canvas
  guides.push({
    points: [width / 2, 0, width / 2, height],
    key: 'center-vertical'
  });
  guides.push({
    points: [0, height / 2, width, height / 2],
    key: 'center-horizontal'
  });

  // Vérifier l'alignement avec les autres éléments
  elements.forEach((el, index) => {
    if (el.id === selectedElement.id) return;

    const elX = el.x;
    const elY = el.y;
    const elWidth = el.width || 0;
    const elHeight = el.height || 0;
    const elCenterX = elX + elWidth / 2;
    const elCenterY = elY + elHeight / 2;
    const elRight = elX + elWidth;
    const elBottom = elY + elHeight;

    // Alignement vertical (gauche, centre, droite)
    if (Math.abs(selX - elX) < TOLERANCE) {
      guides.push({
        points: [elX, 0, elX, height],
        key: `v-left-${index}`
      });
    }
    if (Math.abs(selCenterX - elCenterX) < TOLERANCE) {
      guides.push({
        points: [elCenterX, 0, elCenterX, height],
        key: `v-center-${index}`
      });
    }
    if (Math.abs(selRight - elRight) < TOLERANCE) {
      guides.push({
        points: [elRight, 0, elRight, height],
        key: `v-right-${index}`
      });
    }
    // Alignement droite du sélectionné avec gauche d'un autre
    if (Math.abs(selRight - elX) < TOLERANCE) {
      guides.push({
        points: [elX, 0, elX, height],
        key: `v-right-left-${index}`
      });
    }
    // Alignement gauche du sélectionné avec droite d'un autre
    if (Math.abs(selX - elRight) < TOLERANCE) {
      guides.push({
        points: [elRight, 0, elRight, height],
        key: `v-left-right-${index}`
      });
    }

    // Alignement horizontal (haut, centre, bas)
    if (Math.abs(selY - elY) < TOLERANCE) {
      guides.push({
        points: [0, elY, width, elY],
        key: `h-top-${index}`
      });
    }
    if (Math.abs(selCenterY - elCenterY) < TOLERANCE) {
      guides.push({
        points: [0, elCenterY, width, elCenterY],
        key: `h-center-${index}`
      });
    }
    if (Math.abs(selBottom - elBottom) < TOLERANCE) {
      guides.push({
        points: [0, elBottom, width, elBottom],
        key: `h-bottom-${index}`
      });
    }
    // Alignement bas du sélectionné avec haut d'un autre
    if (Math.abs(selBottom - elY) < TOLERANCE) {
      guides.push({
        points: [0, elY, width, elY],
        key: `h-bottom-top-${index}`
      });
    }
    // Alignement haut du sélectionné avec bas d'un autre
    if (Math.abs(selY - elBottom) < TOLERANCE) {
      guides.push({
        points: [0, elBottom, width, elBottom],
        key: `h-top-bottom-${index}`
      });
    }
  });

  return (
    <Group>
      {guides.map((guide) => (
        <Line
          key={guide.key}
          points={guide.points}
          stroke="#0096FF"
          strokeWidth={1}
          dash={[5, 5]}
          perfectDrawEnabled={false}
        />
      ))}
    </Group>
  );
};

export default Guides;
