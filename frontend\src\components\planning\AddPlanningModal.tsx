import React, { useState } from 'react';
import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, IconButton, Box, FormControl, RadioGroup, FormControlLabel, Radio, InputAdornment } from '@mui/material';
import { X, Clock, CalendarDays, PenSquare, Calendar, Coins } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import DOMPurify from 'dompurify';
import { PlanningFormData, planningFormSchema } from '../../types/planning';
import ModalPortal from '../../components/ModalPortal';
import TiptapEditor from '../TiptapEditor';

interface AddPlanningModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: PlanningFormData) => void;
  selectedMissionId?: string;
  selectedDate?: string;
}

const AddPlanningModal: React.FC<AddPlanningModalProps> = ({
  open,
  onClose,
  onSubmit,
  selectedMissionId,
  selectedDate
}) => {
  // Obtenir la date d'aujourd'hui au format YYYY-MM-DD
  const today = new Date().toISOString().split('T')[0];
  
  // États pour suivre les compteurs de caractères
  const [titleLength, setTitleLength] = useState(0);
  const [descriptionLength, setDescriptionLength] = useState(0);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    control,
    watch,
    setValue
  } = useForm<PlanningFormData>({
    resolver: zodResolver(planningFormSchema),
    defaultValues: {
      mission_id: selectedMissionId,
      date: selectedDate || today,
      start_time: '08:00',
      end_time: '18:00',
      payment_method: 'jobi_only',
      montant_propose: 0,
      description: '' // Initialiser à vide pour TiptapEditor
    }
  });

  // Surveillez le mode de paiement choisi
  const selectedPaymentMethod = watch('payment_method');
  // Surveillez la description pour activer/désactiver le bouton de prévisualisation

  // Fonction pour convertir une heure HH:MM en minutes
  const getTimeAsMinutes = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const handleClose = () => {
    reset();
    setTitleLength(0);
    setDescriptionLength(0);
    onClose();
  };

  const onSubmitForm = (data: PlanningFormData) => {
    // Sanitizer les données avant de les soumettre
    const sanitizedData = {
      ...data,
      title: DOMPurify.sanitize(data.title),
      description: data.description ? DOMPurify.sanitize(data.description) : undefined,
      mission_id: selectedMissionId,
      // S'assurer que montant_propose est un nombre
      montant_propose: typeof data.montant_propose === 'string' 
        ? parseFloat(data.montant_propose) || 0 
        : data.montant_propose || 0
    };
    
    // Forcer le montant à un nombre si indéfini ou NaN
    if (sanitizedData.montant_propose === undefined || isNaN(sanitizedData.montant_propose)) {
      sanitizedData.montant_propose = 0;
    }
    
    onSubmit(sanitizedData);
  };

  // Gestionnaire pour le champ titre
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Sanitize l'entrée
    const sanitizedValue = DOMPurify.sanitize(e.target.value);
    
    // Limiter à 120 caractères
    const truncatedValue = sanitizedValue.slice(0, 120);
    
    // Mettre à jour le champ et le compteur
    if (sanitizedValue !== e.target.value) {
      e.target.value = truncatedValue;
    }
    
    setTitleLength(truncatedValue.length);
    
    // Mettre à jour le champ dans le formulaire
    setValue('title', truncatedValue);
  };

  // Gestionnaire pour le contenu de l'éditeur TipTap
  const handleEditorChange = (content: string) => {
    // Le contenu HTML est déjà nettoyé par TiptapEditor
    setValue('description', content);
    
    // Extraire le texte pour le comptage des caractères
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    setDescriptionLength(textContent.length);
  };
  
  return (
    <ModalPortal isOpen={open} onBackdropClick={handleClose}>
      <Dialog 
        open={true}
        maxWidth="sm" 
        fullWidth
        hideBackdrop={true}
        onClose={(event, reason) => {
          if (reason === 'backdropClick') {
            handleClose();
          }
        }}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            backgroundColor: '#fff',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
            overflow: 'auto',
            overflowY: 'scroll',
            m: {
              xs: '8px',
              sm: '16px',
              md: '32px'
            },
            minWidth: '600px',
            display: 'flex',
            flexDirection: 'column',
            width: {
              xs: 'calc(100% - 16px)',
              sm: 'auto'
            },
            height: {
              xs: 'auto',
              sm: 'auto',
              md: 'auto'
            },
            maxHeight: {
              xs: '90dvh',
              sm: 'calc(100vh - 100px)'
            },
            '@media (max-width: 650px)': {
              minWidth: 'calc(100% - 16px)',
              margin: '8px'
            }
          }
        }}
      >
        <form onSubmit={handleSubmit(onSubmitForm)} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <DialogTitle 
            sx={{ 
              backgroundColor: '#FF6B2C', 
              color: 'white', 
              fontWeight: 700,
              fontSize: { xs: '1rem', sm: '1.1rem' },
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: { xs: '14px 16px', sm: '18px 24px' },
              flexShrink: 0
            }}
          >
            <Typography variant="inherit" sx={{ fontSize: 'inherit', fontWeight: 'inherit', pr: 1 }}>
              Ajouter une mission au planning{selectedDate ? ` du ${new Date(selectedDate).toLocaleDateString('fr-FR')}` : ''}
            </Typography>
            <IconButton 
              onClick={handleClose} 
              sx={{ 
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
              size="small"
            >
              <X size={18} />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ 
            padding: { xs: '20px 16px', sm: '28px 24px' }, 
            background: '#fff',
            overflow: 'auto',
            overflowY: 'auto',
            flexGrow: 1
          }}>
            <Box className="space-y-5 mt-1">
              {selectedMissionId ? (
                <Box 
                  sx={{
                    backgroundColor: 'rgba(255, 107, 44, 0.06)',
                    padding: '12px 16px',
                    borderRadius: '10px',
                    borderLeft: '3px solid #FF6B2C',
                    color: '#555',
                    marginBottom: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5
                  }}
                >
                  <Calendar size={18} style={{ color: '#FF6B2C' }} />
                  <Typography variant="body2">
                    Cette mission sera liée à la mission sélectionnée.
                  </Typography>
                </Box>
              ) : (
                <Box 
                  sx={{
                    backgroundColor: 'rgba(255, 107, 44, 0.06)',
                    padding: '12px 16px',
                    borderRadius: '10px',
                    borderLeft: '3px solid #FF6B2C',
                    color: '#555',
                    marginBottom: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5
                  }}
                >
                  <Calendar size={18} style={{ color: '#FF6B2C' }} />
                  <Typography variant="body2">
                    Vous ajoutez une mission manuelle{selectedDate ? ` pour le ${new Date(selectedDate).toLocaleDateString('fr-FR')}` : ''}.
                  </Typography>
                </Box>
              )}

              <Box className="mb-5">
                <Box className="flex items-center gap-2 mb-2.5">
                  <PenSquare size={18} style={{ color: '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Titre de la mission
                  </Typography>
                </Box>
                <TextField
                  {...register('title')}
                  placeholder="Ex: Tondre la pelouse"
                  fullWidth
                  error={!!errors.title}
                  helperText={errors.title?.message || `${titleLength}/120 caractères`}
                  onChange={handleTitleChange}
                  inputProps={{ maxLength: 120 }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '10px',
                      '&:hover fieldset': {
                        borderColor: '#FF965E',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& .MuiFormHelperText-root': {
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginRight: 0,
                    },
                  }}
                />
              </Box>

              <Box className="mb-5">
                <Box className="flex flex-col space-y-1.5 mb-1">
                  <Typography variant="subtitle2" color="text.primary" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                    Description <span className="text-gray-400 font-normal">(optionnel)</span>
                  </Typography>
                  <Box className="flex items-center justify-between">
                    <Typography variant="caption" color="text.secondary">
                      Donnez tous les détails nécessaires à la réalisation de la mission
                    </Typography>
                    <Typography variant="caption" color={descriptionLength > 1200 ? "error" : "text.secondary"}>
                      {descriptionLength}/1200
                    </Typography>
                  </Box>
                </Box>
                
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TiptapEditor
                      content={field.value || ''}
                      onChange={handleEditorChange}
                      placeholder="Décrivez la mission en détail..."
                      className="rounded-md border border-gray-300 focus-within:border-primary-500"
                      maxLength={1200}
                    />
                  )}
                />
                
                {errors.description && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                    {errors.description.message}
                  </Typography>
                )}
              </Box>

              <Box className="mb-5">
                <Box className="flex items-center gap-2 mb-2.5">
                  <CalendarDays size={18} style={{ color: '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Date de la mission
                  </Typography>
                </Box>
                <TextField
                  {...register('date')}
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  error={!!errors.date}
                  helperText={errors.date?.message}
                  defaultValue={selectedDate || today}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '10px',
                      '&:hover fieldset': {
                        borderColor: '#FF965E',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                  }}
                />
              </Box>

              <Box className="mb-5">
                <Box className="flex items-center gap-2 mb-2.5">
                  <Clock size={18} style={{ color: '#FF6B2C' }} />
                  <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                    Horaires
                  </Typography>
                </Box>
                <Box className="grid grid-cols-2 gap-4">
                  <TextField
                    {...register('start_time', {
                      onChange: (e) => {
                        // Si la valeur est vide, utiliser la valeur par défaut
                        if (!e.target.value) {
                          e.target.value = '08:00';
                        }
                      }
                    })}
                    label="Heure de début"
                    type="time"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    error={!!errors.start_time}
                    helperText={errors.start_time?.message}
                    defaultValue="08:00"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '10px',
                        '&:hover fieldset': {
                          borderColor: '#FF965E',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF6B2C',
                        },
                      },
                      '& .MuiInputLabel-root': {
                        color: '#666',
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF6B2C',
                      },
                    }}
                  />
                  <TextField
                    {...register('end_time', {
                      onChange: (e) => {
                        // Si la valeur est vide, utiliser la valeur par défaut
                        if (!e.target.value) {
                          e.target.value = '18:00';
                        }
                      }
                    })}
                    label="Heure de fin"
                    type="time"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    error={!!errors.end_time || (!!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || '18:00') <= getTimeAsMinutes(watch('start_time') || '08:00'))}
                    helperText={errors.end_time?.message || 
                      (!!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || '18:00') <= getTimeAsMinutes(watch('start_time') || '08:00')
                        ? "L'heure de fin doit être après l'heure de début" 
                        : undefined)}
                    defaultValue="18:00"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '10px',
                        '&:hover fieldset': {
                          borderColor: '#FF965E',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF6B2C',
                        },
                      },
                      '& .MuiInputLabel-root': {
                        color: '#666',
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF6B2C',
                      },
                    }}
                  />
                </Box>
                {/* Message d'erreur conditionnel - affiché seulement si les deux champs ont des valeurs valides */}
                {(!!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || '18:00') <= getTimeAsMinutes(watch('start_time') || '08:00')) && (
                  <Box sx={{ mt: 1, color: '#d32f2f', fontSize: '0.75rem', fontWeight: 400 }}>
                    L'heure de fin doit être postérieure à l'heure de début
                  </Box>
                )}
              </Box>

              {/* Section de tarif - visible uniquement pour les missions manuelles */}
              {!selectedMissionId && (
                <Box className="mb-5">
                  <Box className="flex items-center gap-2 mb-2.5">
                    <Coins size={18} style={{ color: '#FF6B2C' }} />
                    <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '0.95rem' }}>
                      Mode de paiement et tarif
                    </Typography>
                  </Box>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      Sélectionnez le mode de paiement pour cette mission
                    </Typography>
                    
                    <Controller
                      name="payment_method"
                      control={control}
                      defaultValue="jobi_only"
                      render={({ field }) => (
                        <FormControl component="fieldset">
                          <RadioGroup
                            {...field}
                            sx={{
                              '& .MuiFormControlLabel-root': {
                                marginBottom: '8px'
                              }
                            }}
                          >
                            <FormControlLabel
                              value="jobi_only"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Jobi uniquement
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Échange de services via système Jobi
                                  </Typography>
                                </Box>
                              }
                            />
                            <FormControlLabel
                              value="direct_only"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Paiement direct
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Paiement en euros directement au prestataire
                                  </Typography>
                                </Box>
                              }
                            />
                            <FormControlLabel
                              value="both"
                              control={
                                <Radio 
                                  sx={{
                                    '&.Mui-checked': {
                                      color: '#FF6B2C',
                                    },
                                  }}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    Mode hybride
                                  </Typography>
                                  <Typography variant="caption" sx={{ color: '#777' }}>
                                    Combinaison de Jobi et paiement direct
                                  </Typography>
                                </Box>
                              }
                            />
                          </RadioGroup>
                        </FormControl>
                      )}
                    />
                  </Box>
                  
                  <Box sx={{ mt: 2 }}>
                    <Controller
                      name="montant_propose"
                      control={control}
                      defaultValue={0}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          type="number"
                          label="Montant proposé"
                          fullWidth
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                {selectedPaymentMethod === 'jobi_only' ? 'J' : 
                                 selectedPaymentMethod === 'direct_only' ? '€' : 'J/€'}
                              </InputAdornment>
                            ),
                          }}
                          inputProps={{ 
                            min: 0,
                            step: 1 
                          }}
                          // Convertir la valeur en nombre
                          onChange={(e) => {
                            // Gérer le cas où le champ est vide
                            const value = e.target.value === '' ? 0 : Number(e.target.value);
                            
                            // S'assurer que la valeur est un nombre
                            const numericValue = isNaN(value) ? 0 : value;
                            
                            // Mettre à jour le champ
                            field.onChange(numericValue);
                          }}
                          error={!!errors.montant_propose}
                          helperText={errors.montant_propose?.message || (
                            selectedPaymentMethod === 'jobi_only' 
                              ? 'Montant en Jobi' 
                              : selectedPaymentMethod === 'direct_only' 
                                ? 'Montant en euros' 
                                : 'Montant combiné Jobi/euros'
                          )}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '10px',
                              '&:hover fieldset': {
                                borderColor: '#FF965E',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#FF6B2C',
                              },
                            },
                          }}
                        />
                      )}
                    />
                  </Box>
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions sx={{ 
            padding: { xs: '12px 16px', sm: '16px 24px' }, 
            backgroundColor: '#fbfbfb', 
            borderTop: '1px solid #f0f0f0',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: 'stretch',
            gap: { xs: 1, sm: 0 },
            flexShrink: 0
          }}>
            <Button 
              onClick={handleClose}
              sx={{
                color: '#777',
                '&:hover': {
                  backgroundColor: '#f5f5f5',
                  color: '#555'
                },
                textTransform: 'none',
                fontSize: '0.9rem',
                fontWeight: 500,
                borderRadius: '10px',
                padding: '8px 16px',
                width: { xs: '100%', sm: 'auto' },
                order: { xs: 2, sm: 1 }
              }}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={!!watch('start_time') && !!watch('end_time') && getTimeAsMinutes(watch('end_time') || '18:00') <= getTimeAsMinutes(watch('start_time') || '08:00')}
              sx={{
                backgroundColor: '#FF6B2C',
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
                '&.Mui-disabled': {
                  backgroundColor: '#ccc',
                  color: '#666'
                },
                textTransform: 'none',
                fontWeight: 600,
                borderRadius: '10px',
                boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)',
                padding: '8px 24px',
                fontSize: '0.9rem',
                width: { xs: '100%', sm: 'auto' },
                order: { xs: 1, sm: 2 }
              }}
            >
              Ajouter
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </ModalPortal>
  );
};

export default AddPlanningModal; 