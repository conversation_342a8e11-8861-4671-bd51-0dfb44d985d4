import { useState, useCallback } from 'react';

type ValidationRule = {
  validate: (value: string) => boolean;
  message: string;
} | ((value: string) => { isValid: boolean; message: string; });

type ValidationRules = {
  [key: string]: ValidationRule[];
};

type Errors = {
  [key: string]: string[];
};

export const useFormValidation = (rules: ValidationRules) => {
  const [errors, setErrors] = useState<Errors>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});

  const validateField = useCallback((name: string, value: string, isSubmitting: boolean = false) => {
    if (!rules[name] || !isSubmitting) return [];

    const fieldErrors = rules[name]
      .filter(rule => {
        if (typeof rule === 'function') {
          return !rule(value).isValid;
        }
        return !rule.validate(value);
      })
      .map(rule => {
        if (typeof rule === 'function') {
          return rule(value).message;
        }
        return rule.message;
      });

    return fieldErrors;
  }, [rules]);

  const validateForm = useCallback((values: { [key: string]: string }) => {
    const newErrors: Errors = {};
    let isValid = true;

    Object.keys(rules).forEach(fieldName => {
      const value = values[fieldName] || '';
      const fieldErrors = validateField(fieldName, value, true);
      if (fieldErrors.length > 0) {
        newErrors[fieldName] = fieldErrors;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched(Object.keys(rules).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    return isValid;
  }, [rules, validateField]);

  const handleBlur = useCallback((name: string, _value: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  }, []);

  const handleChange = useCallback((name: string, _value: string) => {
    if (touched[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [touched]);

  return {
    errors,
    validateForm,
    handleBlur,
    handleChange,
    touched,
  };
};

// Règles de validation prédéfinies
export const validationRules = {
  required: (message = 'Ce champ est requis'): ValidationRule => ({
    validate: (value: string) => value.trim().length > 0,
    message,
  }),
  email: (message = 'Email invalide'): ValidationRule => ({
    validate: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message,
  }),
  minLength: (length: number, message = `Minimum ${length} caractères`): ValidationRule => ({
    validate: (value: string) => value.length >= length,
    message,
  }),
  maxLength: (length: number, message = `Maximum ${length} caractères`): ValidationRule => ({
    validate: (value: string) => value.length <= length,
    message,
  }),
  password: (message = 'Le mot de passe doit contenir au moins 8 caractères et inclure 2 des 4 types suivants : minuscules, majuscules, chiffres, caractères spéciaux. Exemple : Gerard66!'): ValidationRule => ({
    validate: (value: string) => {
      const hasLowercase = /[a-z]/.test(value);
      const hasUppercase = /[A-Z]/.test(value);
      const hasDigit = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
      
      const typesPresent = 
        [hasLowercase, hasUppercase, hasDigit, hasSpecialChar]
        .filter(Boolean).length >= 2;
      
      return value.length >= 8 && typesPresent;
    },
    message,
  }),
  phone: (message = 'Numéro de téléphone invalide'): ValidationRule => ({
    validate: (value: string) => /^(\+33|0)[1-9](\d{2}){4}$/.test(value),
    message,
  }),
};
