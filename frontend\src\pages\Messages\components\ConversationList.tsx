import React, { useState } from 'react';
import {
  Box,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  Avatar,
  Typography,
  Badge,
  TextField,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Switch,
  styled,
  Tooltip,
  Zoom,
  Button as <PERSON>i<PERSON>utton,
  SxProps
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Chat as ChatIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Conversation } from '../types';
import NewConversationDialog from './NewConversationDialog';
import { useAuth } from '../../../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import OnlineStatusDot from '../../../components/OnlineStatusDot';
import { useQuery } from '@tanstack/react-query';
import api from '../../../services/api';
import { useMessageSound } from '../../../hooks/useMessageSound';
import { logger } from '@/utils/logger';

// Composants stylisés
const StyledListItemButton = styled(ListItemButton)(() => ({
  borderRadius: '12px',
  marginBottom: '8px',
  padding: '12px 16px',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 248, 243, 0.8)',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 6px rgba(255, 107, 44, 0.08)',
  },
  '&.Mui-selected': {
    backgroundColor: 'rgba(255, 107, 44, 0.1)',
    borderLeft: '4px solid #FF6B2C',
    paddingLeft: '12px',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.15)',
    }
  },
}));

const StyledSearchField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '24px',
    backgroundColor: '#FFFFFF',
    transition: 'all 0.2s ease',
    '& fieldset': {
      borderColor: 'rgba(255, 107, 44, 0.2)',
    },
    '&:hover fieldset': {
      borderColor: 'rgba(255, 107, 44, 0.4)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  }
}));

const StyledSwitch = styled(Switch)(() => ({
  '& .MuiSwitch-switchBase.Mui-checked': {
    color: '#FF6B2C',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.08)',
    },
  },
  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
    backgroundColor: '#FF965E',
  },
}));

const StyledBadge = styled(Badge)(() => ({
  '& .MuiBadge-badge': {
    backgroundColor: '#FF6B2C',
    color: 'white',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(255, 107, 44, 0.2)',
  },
}));

const FilterContainer = styled(Box)(() => ({
  padding: '16px',
  backgroundColor: 'rgba(255, 248, 243, 0.5)',
  borderRadius: '0 0 12px 12px',
  borderTop: '1px solid rgba(255, 107, 44, 0.05)',
  transition: 'all 0.3s ease',
}));

const StyledAvatar = styled(Avatar)(() => ({
  width: 48,
  height: 48,
  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.15)',
  border: '2px solid #FFFFFF',
  backgroundColor: '#FF7A35',
}));

const EmptyStateContainer = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '32px 16px',
  textAlign: 'center',
  height: '100%',
}));

const AddButton = styled(IconButton)(() => ({
  backgroundColor: '#FF6B2C',
  color: 'white',
  '&:hover': {
    backgroundColor: '#FF7A35',
    transform: 'rotate(90deg)',
  },
  transition: 'all 0.3s ease',
}));

const HeaderContainer = styled(Box)(() => ({
  padding: '16px',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  background: 'linear-gradient(to right, rgba(255, 248, 243, 0.6), rgba(255, 248, 243, 0.9))',
  backdropFilter: 'blur(10px)',
  borderRadius: '12px 12px 0 0',
  position: 'sticky',
  top: 0,
  zIndex: 10,
}));

const StyledButton = styled(MuiButton)(() => ({
  backgroundColor: '#FF6B2C',
  color: 'white',
  borderRadius: '20px',
  padding: '8px 16px',
  fontWeight: 'bold',
  '&:hover': {
    backgroundColor: '#FF7A35',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 8px rgba(255, 107, 44, 0.2)',
  },
  '& .MuiButton-startIcon': {
    marginRight: '8px',
  },
  transition: 'all 0.2s ease',
}));

interface SkeletonProps {
  variant: 'circular' | 'text' | 'rectangular';
  width: number | string;
  height: number | string;
  sx?: SxProps;
}

interface ConversationListProps {
  conversations: Conversation[];
  selectedId?: string;
  isLoading: boolean;
  onSearchChange: (query: string) => void;
  onUnreadFilterChange: (showUnreadOnly: boolean) => void;
  unreadOnly: boolean;
  onShowDeletedChange: (showDeleted: boolean) => void;
  showDeleted: boolean;
  isNewConversationOpen: boolean;
  onNewConversationClose: () => void;
  setIsNewConversationOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  selectedId,
  isLoading,
  onSearchChange,
  onUnreadFilterChange,
  unreadOnly,
  onShowDeletedChange,
  showDeleted,
  isNewConversationOpen,
  onNewConversationClose,
  setIsNewConversationOpen
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showDeletedLocal, setShowDeletedLocal] = useState(showDeleted);
  const { isSoundEnabled, toggleSound } = useMessageSound();

  const { user } = useAuth();

  // Filtrer les conversations basées sur la recherche
  const filteredConversations = conversations.filter(conversation => {
    const otherUser = conversation.user1_id === user?.id
      ? conversation.user2 || conversation.otherUser
      : conversation.user1 || conversation.otherUser;

    const searchLower = searchQuery.toLowerCase();
    
    return (
      otherUser?.prenom?.toLowerCase().includes(searchLower) ||
      otherUser?.nom?.toLowerCase().includes(searchLower) ||
      otherUser?.first_name?.toLowerCase().includes(searchLower) ||
      otherUser?.last_name?.toLowerCase().includes(searchLower) ||
      otherUser?.email?.toLowerCase().includes(searchLower) ||
      otherUser?.usertag?.toLowerCase().includes(searchLower) ||
      conversation.last_message_content?.toLowerCase().includes(searchLower)
    );
  });

  // Trier les conversations : non lus d'abord, puis par date
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    // Si l'une des conversations a des messages non lus et l'autre non
    if ((a.unread_count || 0) > 0 && (b.unread_count || 0) === 0) return -1;
    if ((a.unread_count || 0) === 0 && (b.unread_count || 0) > 0) return 1;
    
    // Si même statut de lecture, trier par date
    const dateA = new Date(a.last_message_date || a.updated_at || a.created_at).getTime();
    const dateB = new Date(b.last_message_date || b.updated_at || b.created_at).getTime();
    return dateB - dateA;
  });

  // Gérer la recherche
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearchChange(query);
  };

  // Effacer la recherche
  const handleClearSearch = () => {
    setSearchQuery('');
    onSearchChange('');
  };

  // Filtrer par messages non lus
  const handleUnreadFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUnreadFilterChange(e.target.checked);
  };

  // Toggle affichage des filtres
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Récupérer le nombre de conversations
  const { data: conversationCount } = useQuery({
    queryKey: ['conversationCount'],
    queryFn: async () => {
      const response = await api.get('/api/messages/count-conversations');
      return response.data.data;
    }
  });

  // Gérer le clic sur le compteur
  const handleCounterClick = () => {
    const newShowDeleted = !showDeletedLocal;
    setShowDeletedLocal(newShowDeleted);
    onShowDeletedChange(newShowDeleted);
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        height: '100%',
        overflow: 'hidden'
      }}
    >
      {/* En-tête avec titre et bouton nouvelle conversation */}
      <HeaderContainer>
        <Typography variant="h6" fontWeight="bold" color="#2D3748">
          {conversationCount?.count === 1 ? 'Conversation' : 'Conversations'}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={isSoundEnabled ? "Désactiver le son de notification" : "Activer le son de notification"} arrow TransitionComponent={Zoom}>
            <IconButton
              onClick={toggleSound}
              sx={{
                color: isSoundEnabled ? '#FF6B2C' : 'text.secondary',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                },
                transition: 'all 0.2s'
              }}
            >
              {isSoundEnabled ? <VolumeUpIcon /> : <VolumeOffIcon className="text-gray-400" />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Filtres" arrow TransitionComponent={Zoom}>
            <IconButton
              onClick={toggleFilters}
              color="primary"
              sx={{
                color: '#FF6B2C',
                backgroundColor: showFilters ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                },
              }}
            >
              <FilterIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Nouvelle conversation" arrow TransitionComponent={Zoom}>
            <AddButton 
              onClick={() => setIsNewConversationOpen(true)}
              size="small"
            >
              <AddIcon />
            </AddButton>
          </Tooltip>
        </Box>
      </HeaderContainer>

      {/* Barre de recherche */}
      <Box sx={{ p: 2, pb: 1 }}>
        <StyledSearchField
          fullWidth
          placeholder="Rechercher..."
          variant="outlined"
          size="small"
          value={searchQuery}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: 'rgba(255, 107, 44, 0.6)' }} />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {searchQuery ? (
                  <IconButton size="small" onClick={handleClearSearch}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                ) : (
                  <Tooltip title="Afficher les filtres" arrow>
                    <IconButton 
                      size="small" 
                      onClick={toggleFilters}
                      sx={{ 
                        color: showFilters ? '#FF6B2C' : 'rgba(0, 0, 0, 0.54)',
                        transform: showFilters ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'all 0.3s ease',
                      }}
                    >
                      <FilterIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </InputAdornment>
            )
          }}
        />
      </Box>

      {/* Filtres avancés */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            style={{ overflow: 'hidden' }}
          >
            <FilterContainer>
              <FormControlLabel
                control={
                  <StyledSwitch 
                    checked={unreadOnly} 
                    onChange={handleUnreadFilterChange}
                    size="small"
                  />
                }
                label={
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    Messages non lus uniquement
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <StyledSwitch 
                    checked={showDeletedLocal} 
                    onChange={(e) => {
                      const isChecked = e.target.checked;
                      setShowDeletedLocal(isChecked);
                      onShowDeletedChange(isChecked);
                    }}
                    size="small"
                  />
                }
                label={
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    Afficher les conversations masquées
                  </Typography>
                }
              />
            </FilterContainer>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Liste des conversations */}
      <Box 
        sx={{ 
          flexGrow: 1, 
          overflow: 'auto', 
          p: 1.5,
          height: '100%',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(255, 107, 44, 0.05)',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(255, 107, 44, 0.2)',
            borderRadius: '3px',
            '&:hover': {
              background: 'rgba(255, 107, 44, 0.3)',
            },
          },
        }}
      >
        {isLoading ? (
          // Affichage de chargement avec skeleton
          <Box sx={{ p: 1 }}>
            {[...Array(5)].map((_, index) => (
              <Box 
                key={index} 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 2, 
                  p: 2, 
                  mb: 1,
                  borderRadius: 2,
                  bgcolor: 'rgba(255, 248, 243, 0.5)',
                }}
              >
                <Skeleton 
                  variant="circular" 
                  width={48} 
                  height={48} 
                  sx={{ bgcolor: 'rgba(255, 107, 44, 0.1)' }} 
                />
                <Box sx={{ width: '100%' }}>
                  <Skeleton 
                    variant="text" 
                    width="60%" 
                    height={24} 
                    sx={{ bgcolor: 'rgba(255, 107, 44, 0.1)' }} 
                  />
                  <Skeleton 
                    variant="text" 
                    width="80%" 
                    height={20} 
                    sx={{ bgcolor: 'rgba(255, 107, 44, 0.05)', mt: 1 }} 
                  />
                </Box>
              </Box>
            ))}
          </Box>
        ) : sortedConversations.length === 0 ? (
          // Aucune conversation
          <EmptyStateContainer>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                type: "spring", 
                stiffness: 260, 
                damping: 20,
                delay: 0.1
              }}
            >
              <ChatIcon sx={{ fontSize: 60, color: 'rgba(255, 107, 44, 0.3)', mb: 2 }} />
            </motion.div>
            <Typography 
              color="text.secondary" 
              sx={{ mb: 1, fontWeight: 500 }}
            >
              {searchQuery
                ? "Aucune conversation ne correspond à votre recherche."
                : "Vous n'avez pas encore de conversations."}
            </Typography>
            <Typography 
              color="text.secondary" 
              variant="body2" 
              sx={{ mb: 3, maxWidth: '80%', mx: 'auto' }}
            >
              Commencez une nouvelle conversation en cliquant sur le bouton "+" ci-dessus.
            </Typography>
            <StyledButton 
              variant="contained" 
              onClick={() => setIsNewConversationOpen(true)}
              startIcon={<AddIcon />}
            >
              Nouvelle conversation
            </StyledButton>
          </EmptyStateContainer>
        ) : (
          // Liste des conversations
          <AnimatePresence mode="popLayout">
            {sortedConversations.map((conversation) => {
              // Déterminer l'autre utilisateur dans la conversation
              const otherUser = conversation.user1_id === user?.id
                ? conversation.user2 || conversation.otherUser
                : conversation.user1 || conversation.otherUser;

              const hasUnread = conversation.unread_count > 0;
              
              // Vérifier si la conversation est masquée/supprimée
              const isDeleted = conversation.user1_id === user?.id 
                ? conversation.user1_has_deleted 
                : conversation.user2_has_deleted;
              
              // Fonction pour vérifier si une date est valide
              const isValidDate = (date: any): boolean => {
                if (!date) return false;
                const d = new Date(date);
                return !isNaN(d.getTime());
              };
              
              // Créer une date valide avec fallback
              let lastMessageDate: Date = new Date(); 
              
              try {
                if (conversation.last_message_date && isValidDate(conversation.last_message_date)) {
                  lastMessageDate = new Date(conversation.last_message_date as string | number | Date);
                } else if (conversation.updated_at && isValidDate(conversation.updated_at)) {
                  lastMessageDate = new Date(conversation.updated_at as string | number | Date);
                } else if (conversation.created_at && isValidDate(conversation.created_at)) {
                  lastMessageDate = new Date(conversation.created_at as string | number | Date);
                }
              } catch (error) {
                logger.info('Erreur de format de date pour la conversation', conversation.id, error);
                // Date actuelle déjà initialisée comme fallback
              }

              // Générer l'affichage du nom d'utilisateur avec les données disponibles
              const getUserDisplayName = () => {
                if (otherUser?.prenom && otherUser?.nom) {
                  return `${otherUser.prenom} ${otherUser.nom.charAt(0).toUpperCase()}.`;
                } else if (otherUser?.first_name && otherUser?.last_name) {
                  return `${otherUser.first_name} ${otherUser.last_name.charAt(0).toUpperCase()}.`;
                } else if (otherUser?.prenom) {
                  return otherUser.prenom;
                } else if (otherUser?.first_name) {
                  return otherUser.first_name;
                } else if (otherUser?.nom) {
                  return `${otherUser.nom.charAt(0).toUpperCase()}.`;
                } else if (otherUser?.last_name) {
                  return `${otherUser.last_name.charAt(0).toUpperCase()}.`;
                } else if (otherUser?.email) {
                  return otherUser.email.split('@')[0];
                } else if (otherUser?.usertag) {
                  return `@${otherUser.usertag}`;
                } else {
                  logger.info('Pas d\'informations utilisateur pour:', otherUser?.id);
                  return 'Utilisateur';
                }
              };

              // Obtenir l'initiale à afficher dans l'avatar (si pas d'image)
              const getAvatarInitial = () => {
                if (otherUser?.prenom?.[0]) {
                  return otherUser.prenom[0].toUpperCase();
                } else if (otherUser?.first_name?.[0]) {
                  return otherUser.first_name[0].toUpperCase();
                } else if (otherUser?.nom?.[0]) {
                  return otherUser.nom[0].toUpperCase();
                } else if (otherUser?.last_name?.[0]) {
                  return otherUser.last_name[0].toUpperCase();
                } else if (otherUser?.email?.[0]) {
                  return otherUser.email[0].toUpperCase();
                } else if (otherUser?.usertag?.[0]) {
                  return otherUser.usertag[0].toUpperCase();
                } else {
                  return 'U';
                }
              };

              return (
                <motion.div
                  key={conversation.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  layout
                >
                  <Link to={`/dashboard/messages/${conversation.id}`} style={{ textDecoration: 'none' }}>
                    <StyledListItemButton
                      selected={selectedId === conversation.id}
                      sx={{
                        ...(isDeleted && {
                          backgroundColor: 'rgba(0, 0, 0, 0.03)',
                          borderLeft: '4px solid #CCCCCC',
                          opacity: 0.7,
                          '&:hover': {
                            backgroundColor: 'rgba(0, 0, 0, 0.05)',
                            opacity: 0.9,
                          },
                          '&.Mui-selected': {
                            backgroundColor: 'rgba(0, 0, 0, 0.07)',
                            borderLeft: '4px solid #AAAAAA',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.09)',
                            }
                          }
                        })
                      }}
                    >
                      <ListItemAvatar>
                        <Zoom in={true} style={{ transitionDelay: '100ms' }}>
                          <Box sx={{ position: 'relative' }}>
                            <StyledBadge
                              color="error"
                              badgeContent={hasUnread ? Math.max(1, Math.round(conversation.unread_count / 2)) : 0}
                              invisible={!hasUnread}
                              overlap="circular"
                            >
                              <StyledAvatar
                                src={otherUser?.avatar_url || undefined}
                                alt={getUserDisplayName()}
                              >
                                {getAvatarInitial()}
                              </StyledAvatar>
                            </StyledBadge>
                            {otherUser?.id && (
                              <Box sx={{ 
                                position: 'absolute',
                                bottom: 0,
                                right: 10,
                                zIndex: 1
                              }}>
                                <OnlineStatusDot userId={otherUser.id} />
                              </Box>
                            )}
                          </Box>
                        </Zoom>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography
                            variant="subtitle1"
                            fontWeight={hasUnread ? 'bold' : 'medium'}
                            noWrap
                            component="div"
                            sx={{ color: hasUnread ? '#FF6B2C' : 'inherit' }}
                          >
                            {getUserDisplayName()}
                            {isDeleted && (
                              <Typography
                                component="span"
                                variant="caption"
                                sx={{
                                  ml: 1,
                                  color: '#888888',
                                  border: '1px solid #CCCCCC',
                                  borderRadius: '4px',
                                  padding: '0px 4px',
                                  fontSize: '0.6rem',
                                  backgroundColor: 'rgba(0, 0, 0, 0.03)'
                                }}
                              >
                                Masquée
                              </Typography>
                            )}
                          </Typography>
                        }
                        secondary={
                          <Typography component="div">
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography
                                variant="body2"
                                color={hasUnread ? '#FF6B2C' : 'text.secondary'}
                                noWrap
                                component="span"
                                sx={{ 
                                  maxWidth: '75%', 
                                  fontWeight: hasUnread ? 'medium' : 'normal',
                                  fontSize: '0.8rem',
                                }}
                              >
                                <span dangerouslySetInnerHTML={{ __html: conversation.last_message_content ? conversation.last_message_content.slice(0, 17) + '...' : '' }} />
                              </Typography>
                              <Typography
                                variant="caption"
                                color={hasUnread ? '#FF6B2C' : 'text.secondary'}
                                sx={{ 
                                  fontSize: '0.7rem',
                                  fontWeight: hasUnread ? 'medium' : 'normal'
                                }}
                              >
                                {`Il y a ${formatDistanceToNow(lastMessageDate, { locale: fr })}`}
                              </Typography>
                            </Box>
                          </Typography>
                        }
                      />
                    </StyledListItemButton>
                  </Link>
                </motion.div>
              );
            })}
          </AnimatePresence>
        )}
      </Box>

      {/* Dialog pour créer une nouvelle conversation */}
      <NewConversationDialog
        open={isNewConversationOpen}
        onClose={onNewConversationClose}
      />

      {/* Compteur de conversations en bas */}
      <Box 
        sx={{ 
          mt: 'auto',
          pt: 2,
          pb: 1,
          borderTop: '1px solid rgba(255, 107, 44, 0.1)',
          textAlign: 'center',
          backgroundColor: 'rgba(255, 248, 243, 0.3)',
          borderRadius: '0 0 12px 12px',
          backdropFilter: 'blur(4px)',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: 'rgba(255, 248, 243, 0.5)',
            transform: 'translateY(-1px)',
            boxShadow: '0 2px 4px rgba(255, 107, 44, 0.1)'
          }
        }}
        onClick={handleCounterClick}
      >
        <Typography 
          variant="body2" 
          sx={{
            color: '#FF6B2C',
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1
          }}
        >
          <ChatIcon fontSize="small" />
          <span>
            {conversationCount?.count || 0} conversation{(conversationCount?.count || 0) > 1 ? 's' : ''}
            {/* <span style={{ color: '#FF6B2C', fontWeight: 400, opacity: 0.8 }}> sur {conversationCount?.limit || 50}</span> */}
          </span>
        </Typography>
        <Typography 
          variant="caption" 
          sx={{
            color: '#FF6B2C',
            opacity: 0.7,
            display: 'block',
            mt: 0.5
          }}
        >
          {showDeletedLocal ? 'Masquer les conversations archivées' : 'Afficher les conversations archivées'}
        </Typography>
      </Box>
    </motion.div>
  );
};

// Helper components
const Skeleton: React.FC<SkeletonProps> = ({ variant, width, height, sx }) => (
  <Box
    sx={{
      width,
      height: variant === 'circular' ? width : height,
      borderRadius: variant === 'circular' ? '50%' : '4px',
      animation: 'pulse 1.5s infinite ease-in-out',
      ...sx,
      '@keyframes pulse': {
        '0%': { opacity: 0.6 },
        '50%': { opacity: 0.3 },
        '100%': { opacity: 0.6 },
      },
    }}
  />
);

export default ConversationList;
