import { useState, useEffect } from 'react';
import { notify } from '../../components/Notification';
import logger from '../../utils/logger';
import { 
  Card, 
  CardContent, 
  Typography, 
  FormControlLabel, 
  Switch, 
  Divider, 
  CircularProgress, 
  Box, 
  FormGroup,
  Button,
  Tooltip,
  Stack,
  Alert
} from '@mui/material';
import { Mail, Info, Loader2 } from 'lucide-react';
import { api } from '../../services/api';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';

interface EmailPreferences {
  nouvelles_offres: boolean;
  connexion: boolean;
  messages: boolean;
  evaluations: boolean;
  missions: boolean;
  paiements: boolean;
  systeme: boolean;
  badges: boolean;
}

interface EmailPreferencesSectionProps {
  onPreferencesUpdate?: (allEnabled: boolean) => void;
  onClose?: () => void;
}

export default function EmailPreferencesSection({ onPreferencesUpdate, onClose }: EmailPreferencesSectionProps) {
  const [preferences, setPreferences] = useState<EmailPreferences>({
    nouvelles_offres: true,
    connexion: true,
    messages: true,
    evaluations: true,
    missions: true,
    paiements: true,
    systeme: true,
    badges: true
  });
  
  const [masterSwitch, setMasterSwitch] = useState<boolean>(true);
  const [previousState, setPreviousState] = useState<EmailPreferences | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  
  // Récupérer les préférences au chargement du composant
  useEffect(() => {
    const fetchPreferences = async () => {
      try {
        setIsLoading(true);
        
        // Obtenez le token CSRF
        await fetchCsrfToken();
        // Obtenez les en-têtes communs
        const headers = await getCommonHeaders();
        
        const response = await api.get('/api/users/email-preferences', { headers });
        
        if (response.data.success && response.data.preferences) {
          let mailEnabled = true;
          
          // Vérifier si email_enabled est défini et mettre à jour le master switch
          if (response.data.preferences.email_enabled !== undefined) {
            mailEnabled = response.data.preferences.email_enabled;
            setMasterSwitch(mailEnabled);
            
            // Mettre à jour le bouton dans la page principale
            if (onPreferencesUpdate) {
              onPreferencesUpdate(mailEnabled);
            }
          }
          
          // Vérifier si email_notifications existe et le charger
          if (response.data.preferences.email_notifications) {
            const emailPrefs = response.data.preferences.email_notifications;
            
            // Si le master switch est désactivé, désactiver toutes les préférences
            if (mailEnabled === false) {
              // Créer un objet avec toutes les préférences désactivées
              const disabledPrefs = Object.keys(emailPrefs).reduce((acc, key) => {
                acc[key as keyof EmailPreferences] = false;
                return acc;
              }, {} as EmailPreferences);
              setPreferences(disabledPrefs);
            } else {
              // Sinon, utiliser les préférences telles quelles
              setPreferences(emailPrefs);
              
              // Vérifier s'il y a au moins une préférence activée
              const anyEnabled = Object.values(emailPrefs).some(value => value === true);
              setMasterSwitch(anyEnabled);
              
              // Mettre à jour le bouton dans la page principale
              if (onPreferencesUpdate) {
                onPreferencesUpdate(anyEnabled);
              }
            }
          }
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération des préférences:', error);
        notify('Erreur lors de la récupération des préférences d\'email', 'error');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPreferences();
  }, []);
  
  // Gérer le changement du master switch
  const handleMasterSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
    setMasterSwitch(newValue);
    
    // Mettre à jour toutes les préférences en fonction du master switch
    const updatedPreferences = { ...preferences };
    Object.keys(updatedPreferences).forEach(key => {
      updatedPreferences[key as keyof EmailPreferences] = newValue;
    });
    
    setPreferences(updatedPreferences);
  };
  
  // Gérer le changement d'une préférence individuelle
  const handlePreferenceChange = (key: keyof EmailPreferences) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
    const newPreferences = { ...preferences, [key]: newValue };
    setPreferences(newPreferences);
    
    // Mettre à jour le master switch s'il y a au moins une préférence activée
    const anyEnabled = Object.values(newPreferences).some(value => value === true);
    setMasterSwitch(anyEnabled);
  };
  
  // Sauvegarder les préférences
  const savePreferences = async () => {
    try {
      setIsSaving(true);

      // Obtenez le token CSRF
      const csrfToken = await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = csrfToken;
      headers['Content-Type'] = 'application/json';

      const response = await api.put('/api/users/update-email-preferences', {
        preferences: {
          email_enabled: masterSwitch,
          email_notifications: preferences
        }
      }, { headers });
      
      if (response.data.success) {
        notify('Préférences d\'email mises à jour avec succès', 'success');
        
        // Appeler la fonction de callback si elle existe
        if (onPreferencesUpdate) {
          onPreferencesUpdate(masterSwitch);
        }
        
        // Fermer la modale après la mise à jour réussie
        if (onClose) {
          onClose();
        }
      } else {
        notify('Erreur lors de la mise à jour des préférences', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde des préférences:', error);
      notify('Erreur lors de la mise à jour des préférences', 'error');
    } finally {
      setIsSaving(false);
    }
  };
  
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Card sx={{ mt: 0,  boxShadow: 'none' }}>
      <CardContent>
        <Typography variant="body2" sx={{ mb: 3, color: '#718096' }}>
          Choisissez les types d'emails que vous souhaitez recevoir. Vous pouvez désactiver ou activer tous les emails avec l'interrupteur principal.
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="500">
            Activer/désactiver tous les emails ci-dessous
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={masterSwitch}
                onChange={handleMasterSwitchChange}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            }
            label=""
          />
        </Box>
        
        {!masterSwitch && (
          <Alert severity="info" sx={{ mb: 2, bgcolor: '#FFF8F3', color: '#718096', border: '1px solid #FFE4BA' }}>
            Certains emails importants comme la réinitialisation du mot de passe, les messages de sécurité ou les confirmations d'inscription seront toujours envoyés.
          </Alert>
        )}
        
        <Divider sx={{ my: 2 }} />
        
        <FormGroup>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Nouvelles offres</Typography>
                <Tooltip title="Emails concernant les nouvelles offres sur la plateforme" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.nouvelles_offres}
                onChange={handlePreferenceChange('nouvelles_offres')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Connexion à votre compte</Typography>
                <Tooltip title="Emails de sécurité concernant les connexions à votre compte" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.connexion}
                onChange={handlePreferenceChange('connexion')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Messages</Typography>
                <Tooltip title="Notifications concernant les nouveaux messages reçus" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.messages}
                onChange={handlePreferenceChange('messages')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box> */}
            
            {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Évaluations</Typography>
                <Tooltip title="Notifications concernant les évaluations reçues ou publiées" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.evaluations}
                onChange={handlePreferenceChange('evaluations')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Missions</Typography>
                <Tooltip title="Emails concernant vos missions en cours, propositions, etc." arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.missions}
                onChange={handlePreferenceChange('missions')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Paiements</Typography>
                <Tooltip title="Notifications relatives aux paiements et aux transactions Jobi" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.paiements}
                onChange={handlePreferenceChange('paiements')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Système</Typography>
                <Tooltip title="Emails importants concernant votre compte et la plateforme" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.systeme}
                onChange={handlePreferenceChange('systeme')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body1">Badges</Typography>
                <Tooltip title="Notifications concernant les badges obtenus ou perdus" arrow>
                  <Info size={16} color="#718096" />
                </Tooltip>
              </Stack>
              <Switch
                checked={preferences.badges}
                onChange={handlePreferenceChange('badges')}
                color="primary"
                sx={{ 
                  '& .MuiSwitch-switchBase.Mui-checked': { color: '#FF6B2C' },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFB380' }
                }}
              />
            </Box> */}
          </Box>
        </FormGroup>
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
          <Button
            variant="contained"
            onClick={savePreferences}
            disabled={isSaving}
            startIcon={isSaving ? <Loader2 size={18} className="animate-spin" /> : null}
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': {
                bgcolor: '#E55A1B',
              },
              color: 'white',
              fontWeight: 500,
              textTransform: 'none',
              borderRadius: '8px',
              px: 3
            }}
          >
            {isSaving ? 'Enregistrement...' : 'Enregistrer les préférences'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
} 