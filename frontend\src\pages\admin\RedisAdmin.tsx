import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  IconButton,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Pagination,
  Stack,
  useMediaQuery
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';

const PAGE_SIZE = 200;

const RedisAdmin: React.FC = () => {
  const [keys, setKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pattern, setPattern] = useState('*');
  const [filter, setFilter] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const isMobile = useMediaQuery('(max-width:900px)');

  const fetchKeys = async (newPage = page, newPattern = pattern) => {
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/admin/redis/keys`, {
        headers,
        withCredentials: true,
        params: { pattern: newPattern, limit: PAGE_SIZE, offset: (newPage - 1) * PAGE_SIZE }
      });
      setKeys(res.data.keys || []);
      setTotal(res.data.total || 0);
    } catch (e: any) {
      setError("Erreur lors de la récupération des clés Redis");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchKeys(1, pattern);
    // eslint-disable-next-line
  }, [pattern]);

  useEffect(() => {
    fetchKeys(page, pattern);
    // eslint-disable-next-line
  }, [page]);

  const handleDelete = async (key: string) => {
    if (key === 'coolify_horizon') return;
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      await axios.delete(`${API_CONFIG.baseURL}/api/admin/redis/keys`, {
        headers,
        withCredentials: true,
        data: { keys: [key] }
      });
      setSuccess(`Clé supprimée : ${key}`);
      fetchKeys(page, pattern);
    } catch (e: any) {
      setError("Erreur lors de la suppression de la clé");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAll = async () => {
    const filtered = keys.filter(key => key !== 'coolify_horizon');
    if (filtered.length === 0) return;
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      await axios.delete(`${API_CONFIG.baseURL}/api/admin/redis/keys`, {
        headers,
        withCredentials: true,
        data: { keys: filtered }
      });
      setSuccess('Toutes les clés affichées ont été supprimées');
      fetchKeys(page, pattern);
    } catch (e: any) {
      setError("Erreur lors de la suppression des clés");
    } finally {
      setLoading(false);
    }
  };

  const handleFlushAll = async () => {
    if (!window.confirm('⚠️ Cette action va supprimer TOUTES les clés Redis, y compris les sessions, le cache, etc. Cette action est irréversible. Continuer ?')) return;
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      await axios.post(`${API_CONFIG.baseURL}/api/admin/redis/flush-all`, {}, {
        headers,
        withCredentials: true
      });
      setSuccess('TOUTES les clés Redis ont été supprimées (flushdb)');
      setPage(1);
      fetchKeys(1, '*');
    } catch (e: any) {
      setError("Erreur lors du flush complet Redis");
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value);
  };

  const handleFilterApply = () => {
    let value = filter.trim();
    // Si le filtre ne contient pas déjà * ou ?, on l'entoure de *
    if (value && !value.includes('*') && !value.includes('?')) {
      value = `*${value}*`;
    }
    setPattern(value === '' ? '*' : value);
    setPage(1);
  };

  const handlePageChange = (_: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  // Trie les clés selon l'ordre choisi
  const getSortedKeys = () => {
    return [...keys].sort((a, b) => sortOrder === 'asc' ? a.localeCompare(b) : b.localeCompare(a));
  };

  // Filtre les clés pour masquer coolify_horizon
  const getFilteredKeys = () => {
    return keys.filter(key => key !== 'coolify_horizon');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h5" sx={{ color: '#FF6B2C' }}>Gestion Redis</Typography>
          <Button startIcon={<RefreshIcon />} onClick={() => fetchKeys(page, pattern)} disabled={loading}>
            Actualiser
          </Button>
        </Box>
        <Stack
          direction={isMobile ? 'column' : 'row'}
          spacing={isMobile ? 1 : 2}
          mb={2}
          alignItems={isMobile ? 'stretch' : 'center'}
        >
          <TextField
            label="Filtre avancé (pattern Redis)"
            value={filter}
            onChange={handleFilterChange}
            size="small"
            sx={{ minWidth: isMobile ? undefined : 200, width: isMobile ? '100%' : undefined }}
            placeholder="*, user:*, session:* ..."
            onKeyDown={e => { if (e.key === 'Enter') handleFilterApply(); }}
          />
          <Button variant="contained" onClick={handleFilterApply} disabled={loading} sx={{ bgcolor: '#FF6B2C', '&:hover': { bgcolor: '#e65c1f' }, width: isMobile ? '100%' : undefined }}>
            Filtrer
          </Button>
          <Button variant="outlined" color="error" onClick={handleDeleteAll} disabled={loading || keys.length === 0} sx={{ width: isMobile ? '100%' : undefined }}>
            Supprimer tout (affiché)
          </Button>
          <Button variant="contained" color="error" onClick={handleFlushAll} disabled={loading} sx={{ fontWeight: 'bold', width: isMobile ? '100%' : undefined }}>
            Supprimer TOUT Redis (DANGER)
          </Button>
          <Button 
            variant={sortOrder === 'asc' ? 'contained' : 'outlined'} 
            onClick={() => setSortOrder('asc')} 
            disabled={loading} 
            sx={sortOrder === 'asc' ? { bgcolor: '#FF6B2C', color: '#fff', '&:hover': { bgcolor: '#e65c1f' }, minWidth: 40, width: isMobile ? '100%' : undefined } : { borderColor: '#FF6B2C', color: '#FF6B2C', minWidth: 40, width: isMobile ? '100%' : undefined }}
          >
            A→Z
          </Button>
          <Button 
            variant={sortOrder === 'desc' ? 'contained' : 'outlined'} 
            onClick={() => setSortOrder('desc')} 
            disabled={loading} 
            sx={sortOrder === 'desc' ? { bgcolor: '#FF6B2C', color: '#fff', '&:hover': { bgcolor: '#e65c1f' }, minWidth: 40, width: isMobile ? '100%' : undefined } : { borderColor: '#FF6B2C', color: '#FF6B2C', minWidth: 40, width: isMobile ? '100%' : undefined }}
          >
            Z→A
          </Button>
        </Stack>
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            {total} clé(s) trouvée(s) | Page {page} / {Math.max(1, Math.ceil(total / PAGE_SIZE))}
          </Typography>
        </Box>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="100px">
            <CircularProgress />
          </Box>
        ) : (
          <>
            <List>
              {getFilteredKeys().length === 0 ? (
                <Typography>Aucune clé trouvée.</Typography>
              ) : (
                getSortedKeys()
                  .filter(key => key !== 'coolify_horizon')
                  .map((key) => (
                    <React.Fragment key={key}>
                      <ListItem>
                        <ListItemText primary={key} />
                        <ListItemSecondaryAction>
                          <IconButton edge="end" aria-label="delete" onClick={() => handleDelete(key)} color="error" disabled={key === 'coolify_horizon'}>
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))
              )}
            </List>
            {total > PAGE_SIZE && (
              <Box display="flex" justifyContent="center" mt={2}>
                <Pagination
                  count={Math.ceil(total / PAGE_SIZE)}
                  page={page}
                  onChange={handlePageChange}
                  color="primary"
                  shape="rounded"
                />
              </Box>
            )}
          </>
        )}
      </Paper>
    </Box>
  );
};

export default RedisAdmin; 