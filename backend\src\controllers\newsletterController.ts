import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import crypto from 'crypto';
import { sendNewsletterVerificationEmail, sendNewsletterWelcomeEmail, sendNewsletterUnsubscribeEmail } from '../services/emailService';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { z } from 'zod';
import { asyncHandler } from '../utils/inputValidation';

// Schéma de validation pour l'email
const emailSchema = z.object({
  email: z.string().email('Adresse email invalide')
});

/**
 * Contrôleur pour gérer les abonnements à la newsletter
 */
export const newsletterController = {
  /**
   * S'abonner à la newsletter
   */
  subscribe: asyncHandler(async (req: Request, res: Response) => {
    try {
      // Valider l'email
      const { email } = emailSchema.parse(req.body);

      // Vérifier si l'email existe déjà
      const { data: existingSubscriber, error: checkError } = await supabase
        .from('newsletter_subscribers')
        .select('id, is_verified')
        .eq('email', email)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        logger.error('Erreur lors de la vérification de l\'email:', checkError);
        return res.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors de la vérification de l\'email'
        });
      }

      // Si l'abonné existe déjà et est vérifié
      if (existingSubscriber && existingSubscriber.is_verified) {
        return res.status(200).json({
          success: true,
          message: 'Vous êtes déjà abonné à notre newsletter'
        });
      }

      // Si l'abonné existe mais n'est pas vérifié, renvoyer un email de vérification
      if (existingSubscriber && !existingSubscriber.is_verified) {
        // Générer un nouveau token
        const verificationToken = crypto.randomBytes(16).toString('hex');
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24); // Expire dans 24h

        // Mettre à jour le token
        const { error: updateError } = await supabase
          .from('newsletter_subscribers')
          .update({
            verification_token: verificationToken,
            verification_token_expires_at: expiresAt.toISOString()
          })
          .eq('id', existingSubscriber.id);

        if (updateError) {
          logger.error('Erreur lors de la mise à jour du token:', updateError);
          return res.status(500).json({
            success: false,
            message: 'Une erreur est survenue lors de la mise à jour du token'
          });
        }

        // Envoyer l'email de vérification
        await sendNewsletterVerificationEmail(email, { token: verificationToken });

        return res.status(200).json({
          success: true,
          message: 'Un email de vérification a été envoyé à votre adresse email'
        });
      }

      // Nouvel abonné
      const verificationToken = crypto.randomBytes(16).toString('hex');
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Expire dans 24h

      // Insérer le nouvel abonné
      const { error: insertError } = await supabase
        .from('newsletter_subscribers')
        .insert({
          email,
          verification_token: verificationToken,
          verification_token_expires_at: expiresAt.toISOString()
        });

      if (insertError) {
        logger.error('Erreur lors de l\'insertion de l\'abonné:', insertError);
        return res.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors de l\'abonnement'
        });
      }

      // Envoyer l'email de vérification
      await sendNewsletterVerificationEmail(email, { token: verificationToken });

      return res.status(200).json({
        success: true,
        message: 'Un email de vérification a été envoyé à votre adresse email'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          message: 'Adresse email invalide',
          errors: error.errors
        });
      }

      logger.error('Erreur lors de l\'abonnement à la newsletter:', error);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de l\'abonnement à la newsletter'
      });
    }
  }),

  /**
   * Vérifier l'email d'un abonné à la newsletter
   */
  verifyEmail: asyncHandler(async (req: Request, res: Response) => {
    try {
      const { token } = req.query;

      if (!token || typeof token !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'Token de vérification manquant ou invalide'
        });
      }

      // Rechercher l'abonné avec ce token
      const { data: subscriber, error: findError } = await supabase
        .from('newsletter_subscribers')
        .select('*')
        .eq('verification_token', token)
        .single();

      if (findError || !subscriber) {
        logger.error('Erreur lors de la recherche de l\'abonné:', findError);
        return res.status(404).json({
          success: false,
          message: 'Token de vérification invalide ou expiré'
        });
      }

      // Vérifier si le token a expiré
      if (subscriber.verification_token_expires_at && new Date(subscriber.verification_token_expires_at) < new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Le token de vérification a expiré'
        });
      }

      // Si déjà vérifié
      if (subscriber.is_verified) {
        return res.status(200).json({
          success: true,
          message: 'Votre email a déjà été vérifié'
        });
      }

      // Mettre à jour l'abonné
      const { error: updateError } = await supabase
        .from('newsletter_subscribers')
        .update({
          is_verified: true,
          verified_at: new Date().toISOString(),
          verification_token: null,
          verification_token_expires_at: null
        })
        .eq('id', subscriber.id);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de l\'abonné:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors de la vérification de l\'email'
        });
      }

      // Envoyer l'email de bienvenue
      try {
        await sendNewsletterWelcomeEmail(subscriber.email);
        logger.info('Email de bienvenue newsletter envoyé avec succès', { email: subscriber.email });
      } catch (emailError) {
        // Ne pas bloquer le processus si l'envoi de l'email échoue
        logger.error('Erreur lors de l\'envoi de l\'email de bienvenue newsletter:', emailError);
      }

      return res.status(200).json({
        success: true,
        message: 'Votre email a été vérifié avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'email:', error);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la vérification de l\'email'
      });
    }
  }),

  /**
   * Se désabonner de la newsletter
   */
  unsubscribe: asyncHandler(async (req: Request, res: Response) => {
    try {
      const { email } = req.query;

      if (!email || typeof email !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'Email manquant ou invalide'
        });
      }

      // Rechercher l'abonné
      const { data: subscriber, error: findError } = await supabase
        .from('newsletter_subscribers')
        .select('*')
        .eq('email', email)
        .single();

      if (findError || !subscriber) {
        return res.status(404).json({
          success: false,
          message: 'Abonné non trouvé'
        });
      }

      // Supprimer l'abonné
      const { error: deleteError } = await supabase
        .from('newsletter_subscribers')
        .delete()
        .eq('id', subscriber.id);

      if (deleteError) {
        logger.error('Erreur lors de la suppression de l\'abonné:', deleteError);
        return res.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors du désabonnement'
        });
      }

      // Envoyer un email de confirmation de désabonnement
      try {
        await sendNewsletterUnsubscribeEmail(email);
        logger.info('Email de confirmation de désabonnement envoyé avec succès', { email });
      } catch (emailError) {
        // Ne pas bloquer le processus si l'envoi de l'email échoue
        logger.error('Erreur lors de l\'envoi de l\'email de confirmation de désabonnement:', emailError);
      }

      return res.status(200).json({
        success: true,
        message: 'Vous avez été désabonné avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors du désabonnement:', error);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors du désabonnement'
      });
    }
  }),

  /**
   * Liste des abonnés à la newsletter (admin seulement)
   */
  getSubscribers: asyncHandler(async (req: Request, res: Response) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;
      const search = req.query.search as string;
      const verified = req.query.verified as string;

      // Vérifier le cache
      const cacheKey = `newsletter:subscribers:${page}:${limit}:${search || ''}:${verified || ''}`;
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        return res.json(JSON.parse(cachedData));
      }

      // Construire la requête
      let query = supabase
        .from('newsletter_subscribers')
        .select('*', { count: 'exact' });

      // Filtrer par statut de vérification
      if (verified === 'true') {
        query = query.eq('is_verified', true);
      } else if (verified === 'false') {
        query = query.eq('is_verified', false);
      }

      // Recherche par email
      if (search) {
        query = query.ilike('email', `%${search}%`);
      }

      // Pagination
      query = query.range(offset, offset + limit - 1).order('created_at', { ascending: false });

      // Exécuter la requête
      const { data: subscribers, error, count } = await query;

      if (error) {
        logger.error('Erreur lors de la récupération des abonnés:', error);
        return res.status(500).json({
          success: false,
          message: 'Une erreur est survenue lors de la récupération des abonnés'
        });
      }

      const result = {
        success: true,
        subscribers,
        pagination: {
          total: count || 0,
          page,
          limit,
          pages: count ? Math.ceil(count / limit) : 0
        }
      };

      // Mettre en cache pour 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(result));

      return res.json(result);
    } catch (error) {
      logger.error('Erreur lors de la récupération des abonnés:', error);
      return res.status(500).json({
        success: false,
        message: 'Une erreur est survenue lors de la récupération des abonnés'
      });
    }
  })
};

export default newsletterController;
