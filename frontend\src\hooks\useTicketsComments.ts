import { useState, useEffect, useCallback, useRef } from 'react';
import supportTicketService, { Comment, CreateCommentDto } from '../services/supportTicketService';
import { logger } from '../utils/logger';

interface UseCommentsResult {
  comments: Comment[];
  loading: boolean;
  error: Error | null;
  fetchComments: (ticketId: string) => Promise<void>;
  addComment: (comment: CreateCommentDto) => Promise<Comment | null>;
  deleteComment: (commentId: string) => Promise<boolean>;
}

/**
 * Hook pour gérer les commentaires d'un ticket
 * @param ticketId ID du ticket pour lequel récupérer les commentaires
 * @returns Un objet avec les commentaires, l'état de chargement, les erreurs et les méthodes pour manipuler les commentaires
 */
export const useTicketsComments = (ticketId?: string): UseCommentsResult => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const fetchInProgress = useRef(false);

  /**
   * Récupère les commentaires d'un ticket
   */
  const fetchComments = useCallback(async (id: string) => {
    if (!id) return;
    
    // Éviter les appels multiples rapprochés
    if (fetchInProgress.current ) {
      logger.info(`Appel à fetchComments ignoré pour éviter les requêtes multiples (ticket: ${id})`);
      return;
    }
    
    try {
      fetchInProgress.current = true;
      setLoading(true);
      setError(null);
      
      logger.info(`Récupération des commentaires pour le ticket ${id}`);
      const fetchedComments = await supportTicketService.getTicketComments(id);
      setComments(fetchedComments);
    } catch (error) {
      logger.error(`Erreur lors de la récupération des commentaires du ticket ${id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
    } finally {
      setLoading(false);
      fetchInProgress.current = false;
    }
  }, []);

  /**
   * Ajoute un commentaire à un ticket
   */
  const addComment = useCallback(async (comment: CreateCommentDto): Promise<Comment | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const newComment = await supportTicketService.addTicketComment(comment);
      
      // Ajouter le nouveau commentaire à la liste existante
      setComments(prevComments => [...prevComments, newComment]);
      
      return newComment;
    } catch (error) {
      logger.error(`Erreur lors de l'ajout d'un commentaire au ticket ${comment.ticket_id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Supprime un commentaire d'un ticket
   */
  const deleteComment = useCallback(async (commentId: string): Promise<boolean> => {
    if (!ticketId) {
      logger.error('Tentative de suppression d\'un commentaire sans ID de ticket');
      setError(new Error('ID de ticket manquant'));
      return false;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      await supportTicketService.deleteTicketComment(ticketId, commentId);
      
      // Supprimer le commentaire de la liste locale
      setComments(prevComments => prevComments.filter(comment => comment.id !== commentId));
      
      return true;
    } catch (error) {
      logger.error(`Erreur lors de la suppression du commentaire ${commentId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, [ticketId]);

  // Charger les commentaires au montage du composant si ticketId est fourni
  useEffect(() => {
    if (ticketId) {
      fetchComments(ticketId);
    }
  }, [ticketId, fetchComments]);

  return {
    comments,
    loading,
    error,
    fetchComments,
    addComment,
    deleteComment
  };
};

export default useTicketsComments; 