import { redis } from '../config/redis';
import { SessionData, SecurityEvent as SessionSecurityEvent } from '../types';

interface Session {
  id: string;
  userId: string;
  lastActivity: number;
  expiresAt: number;
}

export class SessionManager {
  private static instance: SessionManager;
  private static readonly SESSION_PREFIX = 'session:';
  private static readonly SESSION_EXPIRY = 24 * 60 * 60; // 24 heures en secondes
  private readonly USER_SESSIONS_PREFIX = 'user_sessions:';
  private readonly SESSION_TIMEOUT = 3600; // Durée de vie de la session en secondes (1 heure)
  private readonly MAX_SESSIONS_PER_USER = 5;

  private constructor() {}

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Crée une nouvelle session
   */
  async createSession(userId: string): Promise<string> {
    const sessionId = Math.random().toString(36).substring(2);
    const now = Date.now();
    const session: Session = {
      id: sessionId,
      userId,
      lastActivity: now,
      expiresAt: now + (SessionManager.SESSION_EXPIRY * 1000)
    };

    try {
      // Enregistrer dans Redis pour les vérifications rapides
      const key = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      await redis.setex(key, SessionManager.SESSION_EXPIRY, JSON.stringify(session));

      return sessionId;
    } catch (error) {
      console.error('Erreur lors de la création de la session:', error);
      throw error;
    }
  }

  /**
   * Vérifie si une session est valide
   */
  async isValidSession(sessionId: string): Promise<boolean> {
    try {
      const key = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      const sessionData = await redis.get(key);
      if (!sessionData) return false;

      const session: Session = JSON.parse(sessionData);
      return session.expiresAt > Date.now();
    } catch (error) {
      console.error('Erreur lors de la vérification de la session:', error);
      return false;
    }
  }

  /**
   * Met à jour l'activité d'une session
   */
  async refreshSession(sessionId: string): Promise<void> {
    try {
      const key = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      const sessionData = await redis.get(key);
      if (!sessionData) return;

      const session: Session = JSON.parse(sessionData);
      session.lastActivity = Date.now();
      session.expiresAt = Date.now() + (SessionManager.SESSION_EXPIRY * 1000);

      await redis.setex(key, SessionManager.SESSION_EXPIRY, JSON.stringify(session));

    } catch (error) {
      console.error('Erreur lors du rafraîchissement de la session:', error);
    }
  }

  /**
   * Invalide une session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    try {
      const key = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      const sessionData = await redis.get(key);
      if (!sessionData) return;

      const session: Session = JSON.parse(sessionData);
      await redis.del(key);

    } catch (error) {
      console.error('Erreur lors de l\'invalidation de la session:', error);
    }
  }

  /**
   * Nettoie les sessions expirées
   */
  static async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      const keys = await redis.keys(`${SessionManager.SESSION_PREFIX}*`);

      for (const key of keys) {
        const sessionData = await redis.get(key);
        if (sessionData) {
          const session: Session = JSON.parse(sessionData);
          if (session.expiresAt < now) {
            await redis.del(key);
          }
        }
      }
    } catch (error) {
      console.error('Erreur lors du nettoyage des sessions:', error);
    }
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    const sessionKey = `${SessionManager.SESSION_PREFIX}${sessionId}`;
    const sessionData = await redis.get(sessionKey);
    if (!sessionData) {
      return null;
    }
    return JSON.parse(sessionData);
  }

  async getUserSessions(userId: string): Promise<SessionData[]> {
    const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
    const sessionIds = await redis.smembers(userSessionsKey);
    const sessions: SessionData[] = [];

    for (const sessionId of sessionIds) {
      const sessionKey = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      const sessionDataString = await redis.get(sessionKey);
      if (sessionDataString) {
        sessions.push(JSON.parse(sessionDataString));
      }
    }
    return sessions;
  }

  async revokeSession(sessionId: string): Promise<void> {
    const sessionKey = `${SessionManager.SESSION_PREFIX}${sessionId}`;
    const sessionDataString = await redis.get(sessionKey);
    if (sessionDataString) {
      const sessionData = JSON.parse(sessionDataString);
      const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${sessionData.userId}`;
      await redis.del(sessionKey);
      await redis.srem(userSessionsKey, sessionId);
    }
  }

  async revokeAllUserSessions(userId: string): Promise<void> {
    const userSessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
    const sessionIds = await redis.smembers(userSessionsKey);

    for (const sessionId of sessionIds) {
      const sessionKey = `${SessionManager.SESSION_PREFIX}${sessionId}`;
      await redis.del(sessionKey);
    }
    await redis.del(userSessionsKey);
  }
}
