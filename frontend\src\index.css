@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles pour les tableaux de l'editeur de texte dans le profil etc */
.tiptap-table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  overflow: hidden;
  margin: 1rem 0;
  min-width: 100%;
}

.tiptap-table td,
.tiptap-table th {
  border: 2px solid #ddd;
  padding: 0.5rem;
  position: relative;
  min-width: 150px;
}

.tiptap-table th {
  background-color: #FFF8F3;
  font-weight: bold;
  color: #FF6B2C;
}

.tiptap-table .selectedCell {
  background-color: #FFE4BA;
}

.tiptap-table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #FF6B2C;
  cursor: col-resize;
  user-select: none;
  touch-action: none;
}

.tiptap-table .column-resize-handle::after {
  content: "";
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #FF6B2C;
  opacity: 0;
  transition: opacity 0.2s;
}

.tiptap-table .column-resize-handle:hover::after {
  opacity: 1;
}

.tiptap-table p {
  margin: 0;
}

.tableWrapper {
  margin: 1rem 0;
  overflow-x: auto;
}

.tableWrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.tableWrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background: #FF6B2C;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb:hover {
  background: #FF7A35;
}

@layer base {
  * {
    scrollbar-width: auto;
    scrollbar-color: #ff7a35 #f1f1f1;
  }

  html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    width: 100%;
  }
  
  body {
    overflow-x: hidden;
    width: 100%;
    position: relative;
  }
  
  .text-xs {
    font-size: 0.75rem!important;   /* 12px */
    line-height: 1rem!important;    /* 16px */
  }
  
  /* Forcer la taille de la police à 1rem et la hauteur de la ligne à 1.5rem */
  .text-sm {
    font-size: 0.935rem !important;  /* 14px */
    line-height: 1.35rem !important; /* 20px */
  }
  
  .text-lg {
    font-size: 1.125rem !important;  /* 18px */
    line-height: 1.75rem !important; /* 28px */
  }
  
  .text-xl {
    font-size: 1.25rem !important;   /* 20px */
    line-height: 1.75rem !important; /* 28px */
  }
  
  .text-2xl {
    font-size: 1.5rem !important;    /* 24px */
    line-height: 2rem !important;    /* 32px */
  }  
}

@layer components {
  .animate-vibrate {
    animation: vibrate 0.3s infinite;
  }

  .animate-shake {
    animation: shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }
  
  .animate-fadeIn {
    animation: fadeIn 1.5s ease-in-out;
  }

  .backdrop-blur-lg {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  /* Styles pour les boutons avec dégradé */
  .btn-gradient {
    background-size: 200% auto;
    transition: all 0.3s ease;
    background-image: linear-gradient(45deg, #ff7a35 0%, #ff965e 50%, #ff7a35 100%);
  }

  .btn-gradient:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 122, 53, 0.2);
  }

  /* Style alternatif pour certains boutons */
  .btn-gradient-alt {
    background-size: 200% auto;
    transition: all 0.3s ease;
    background-image: linear-gradient(45deg, #ff6b2c 0%, #ff8f4d 50%, #ff6b2c 100%);
  }

  .btn-gradient-alt:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 107, 44, 0.2);
  }
}

@keyframes vibrate {
  0% { transform: translate(0); }
  25% { transform: translate(-2px, 2px); }
  50% { transform: translate(2px, -2px); }
  75% { transform: translate(-2px, -2px); }
  100% { transform: translate(2px, 2px); }
}

@keyframes shake {
  0%, 100% { transform: translate(0); }
  10%, 30%, 50%, 70%, 90% { transform: translate(-5px); }
  20%, 40%, 60%, 80% { transform: translate(5px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out forwards;
}

@keyframes softBreathe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.animate-softBreathe {
  animation: softBreathe 15s ease-in-out infinite;
}

/* Scrollbar styles - only apply to webkit browsers */
@supports (-webkit-appearance: none) {
  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  *::-webkit-scrollbar-thumb {
    background: #ff7a35;
    border-radius: 10px;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #ff965e;
  }
}

/* Override des couleurs de focus par défaut pour utiliser la couleur du thème */
:root {
  --primary-color: #FF6B2C;
  --secondary-color: #FF7A35;
  --tertiary-color: #FF965E;
}

/* Cibler tous les éléments avec focus */
*:focus {
  outline-color: var(--primary-color) !important;
}

/* Cibler spécifiquement les éléments Material UI */
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: var(--primary-color) !important;
}

.MuiInputLabel-root.Mui-focused {
  color: var(--primary-color) !important;
}

/* Cibler les boutons, checkbox, radio buttons, etc. */
.MuiButtonBase-root.Mui-focused {
  color: var(--primary-color) !important;
}

.MuiRadio-root.Mui-checked {
  color: var(--primary-color) !important;
}

.MuiCheckbox-root.Mui-checked {
  color: var(--primary-color) !important;
}

.MuiSwitch-root .Mui-checked {
  color: var(--primary-color) !important;
}

.MuiSwitch-root .Mui-checked + .MuiSwitch-track {
  background-color: var(--tertiary-color) !important;
}

/* Cibler les sélecteurs de Material UI */
.MuiSelect-root.Mui-focused {
  color: var(--primary-color) !important;
}

/* Cibler les ripple effects */
.MuiTouchRipple-rippleVisible {
  color: var(--tertiary-color) !important;
}

@media (max-width: 380px) {
  .hide-below-380 {
    display: none;
  }
}

/* Suppression du padding pour MuiCardContent */
.MuiCardContent-root {
  padding: 0 !important;
}

/* Styles pour les tableaux dans le mode lecture */
.prose table,
.tiptap-table,
[class*="prose"] table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  overflow: hidden;
  margin: 1rem 0;
}

.prose table td,
.prose table th,
.tiptap-table td,
.tiptap-table th,
[class*="prose"] table td,
[class*="prose"] table th {
  border: 2px solid #ddd;
  padding: 0.5rem;
  position: relative;
  min-width: 150px;
  width: 150px;
}

.prose table th,
.tiptap-table th,
[class*="prose"] table th {
  background-color: #FFF8F3;
  font-weight: bold;
  color: #FF6B2C;
}

.prose table p,
.tiptap-table p,
[class*="prose"] table p {
  margin: 0;
}

/* Wrapper pour le défilement horizontal */
.tableWrapper {
  margin: 1rem 0;
  overflow-x: auto;
}

.tableWrapper::-webkit-scrollbar {
  height: 8px;
  background-color: #f1f1f1;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background-color: #FF6B2C;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb:hover {
  background-color: #FF7A35;
}

/* Styles pour le calendrier */
.bg-primary-100 {
  background-color: #FFF8F3;
}

.bg-primary-200 {
  background-color: #FFE4BA;
}

.border-primary-500 {
  border-color: #FF6B2C;
}

.hover\:bg-primary-200:hover {
  background-color: #FFE4BA;
}

/* Styles pour afficher correctement le contenu HTML de Tiptap */
.tiptap-content {
  font-family: inherit;
  line-height: 1.6;
  color: #333;
}

.tiptap-content p {
  margin-bottom: 0.75rem;
}

.tiptap-content ul, 
.tiptap-content ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.tiptap-content ul {
  list-style-type: disc;
}

.tiptap-content ol {
  list-style-type: decimal;
}

.tiptap-content li {
  margin-bottom: 0.25rem;
}

.tiptap-content a {
  color: #FF6B2C;
  text-decoration: underline;
}

.tiptap-content a:hover {
  color: #FF7A35;
}

.tiptap-content blockquote {
  border-left: 3px solid #FF6B2C;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #666;
  font-style: italic;
}

.tiptap-content code {
  background-color: #f1f1f1;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9em;
}

.tiptap-content pre {
  background-color: #f1f1f1;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.tiptap-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

.tiptap-content h1,
.tiptap-content h2,
.tiptap-content h3,
.tiptap-content h4,
.tiptap-content h5,
.tiptap-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #333;
}

.tiptap-content h1 {
  font-size: 1.5rem;
}

.tiptap-content h2 {
  font-size: 1.3rem;
}

.tiptap-content h3 {
  font-size: 1.1rem;
}