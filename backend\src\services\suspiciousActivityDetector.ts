import { Request, Response } from 'express';
import { ConnectionLoggerService } from './connectionLogger';
import { Redis } from 'ioredis';
import logger, { SecurityEvent } from '../utils/logger';
import FailedAttemptsService from './failedAttempts';

interface SuspiciousPattern {
  pattern: RegExp;
  score: number;
  description: string;
}

interface SuspiciousActivity {
  type: string;
  severity: 'low' | 'medium' | 'high';
  details: any;
  timestamp: number;
}

export class SuspiciousActivityDetector {
  private static redis: Redis;
  private static readonly THRESHOLD = 100;
  private static readonly WINDOW_SIZE = 60; // 1 minute en secondes
  private static readonly MAX_ATTEMPTS = 10; // Maximum du compteur d'erreurs pour une IP dans le temps donné par WINDOW_SIZE (si dépassé, l'IP est bloquée pendant RESET_AFTER)
  private static readonly RESET_AFTER = 12 * 60 * 60 * 1000; // 12 heures
  private static readonly PREFIX = 'suspicious_activity:';
  private static readonly ALERT_EXPIRY = 7 * 24 * 60 * 60; // 7 jours

  // Seuils de détection
  private static readonly THRESHOLDS = {
    MAX_FAILED_ATTEMPTS: 5,
    MAX_IPS_PER_USER: 5,
    MAX_USERS_PER_IP: 5,
    TIME_WINDOW: 60 * 60 * 1000, // 1 heure en millisecondes
    SUSPICIOUS_COUNTRIES: ['', 'unknown'], // Liste des pays suspects
    LOCATION_CHANGE_THRESHOLD: 500, // Distance en km
  };
  
  private static patterns: SuspiciousPattern[] = [
    {
      pattern: /union\s+select|select.+from|insert\s+into|delete\s+from|drop\s+table/i,
      score: 50,
      description: 'SQL Injection attempt'
    },
    {
      pattern: /<script>|javascript:|data:text\/html|base64/i,
      score: 40,
      description: 'XSS attempt'
    },
    {
      pattern: /\.\.\/|\.\.%2f|%252f/i,
      score: 30,
      description: 'Path traversal attempt'
    },
    {
      pattern: /%00|null\x00|undefined\x00/i,
      score: 25,
      description: 'Null byte injection'
    },
    {
      pattern: /\$\{.+\}|\$\{.+?:-/i,
      score: 35,
      description: 'Template injection attempt'
    },
    {
      pattern: /eval\(|setTimeout\(|setInterval\(|new\s+Function\(/i,
      score: 45,
      description: 'Code injection attempt'
    }
  ];

  public static async initialize(redisClient: Redis) {
    this.redis = redisClient;
    // Démarrer le nettoyage périodique
    setInterval(() => this.cleanup(), 60 * 60 * 1000); // Toutes les heures
  }

  /**
   * Analyse une tentative de connexion pour détecter des comportements suspects
   */
  public static async analyzeConnection(data: {
    userId: string;
    ip: string;
    userAgent: string;
    location?: string;
    status: 'success' | 'failure';
  }): Promise<void> {
    const now = Date.now();
    const suspiciousActivities: SuspiciousActivity[] = [];

    // Vérification des tentatives échouées
    const failedAttempts = await FailedAttemptsService.getAttempts(data.ip);
    if (failedAttempts && failedAttempts.count >= this.THRESHOLDS.MAX_FAILED_ATTEMPTS) {
      suspiciousActivities.push({
        type: 'multiple_failed_attempts',
        severity: 'high',
        details: { 
          count: failedAttempts.count, 
          ip: data.ip,
          userAgent: data.userAgent,
          lastAttempt: new Date(failedAttempts.lastAttempt)
        },
        timestamp: now
      });
    }

    // Vérification des connexions multiples depuis différentes IPs
    const userHistory = await ConnectionLoggerService.getUserConnectionHistory(data.userId);
    const uniqueIPs = new Set(userHistory.map(log => log.ip));
    if (uniqueIPs.size > this.THRESHOLDS.MAX_IPS_PER_USER) {
      suspiciousActivities.push({
        type: 'multiple_ips',
        severity: 'medium',
        details: { 
          count: uniqueIPs.size, 
          userId: data.userId,
          ips: Array.from(uniqueIPs),
          timeWindow: this.THRESHOLDS.TIME_WINDOW
        },
        timestamp: now
      });
    }

    // Vérification des connexions multiples depuis une même IP
    const ipHistory = await ConnectionLoggerService.getIPConnectionHistory(data.ip);
    const uniqueUsers = new Set(ipHistory.map(log => log.userId));
    if (uniqueUsers.size > this.THRESHOLDS.MAX_USERS_PER_IP) {
      suspiciousActivities.push({
        type: 'multiple_users_same_ip',
        severity: 'medium',
        details: { 
          count: uniqueUsers.size, 
          ip: data.ip,
          users: Array.from(uniqueUsers),
          timeWindow: this.THRESHOLDS.TIME_WINDOW
        },
        timestamp: now
      });
    }

    // Vérification des changements de localisation rapides
    if (userHistory.length > 0 && data.location) {
      const lastLocation = userHistory[0].location;
      if (lastLocation && this.isLocationChangeSuspicious(lastLocation, data.location)) {
        suspiciousActivities.push({
          type: 'suspicious_location_change',
          severity: 'high',
          details: { 
            previousLocation: lastLocation,
            newLocation: data.location,
            userId: data.userId,
            timeDiff: now - userHistory[0].timestamp,
            threshold: this.THRESHOLDS.LOCATION_CHANGE_THRESHOLD
          },
          timestamp: now
        });

        // Vérifier si le pays est dans la liste des pays suspects
        if (this.THRESHOLDS.SUSPICIOUS_COUNTRIES.includes(data.location)) {
          suspiciousActivities.push({
            type: 'suspicious_country_access',
            severity: 'high',
            details: {
              country: data.location,
              userId: data.userId,
              ip: data.ip
            },
            timestamp: now
          });
        }
      }
    }

    // Enregistrement des activités suspectes
    for (const activity of suspiciousActivities) {
      await this.logSuspiciousActivity(data.userId, activity);
    }

    // Si le statut est un échec, incrémenter le compteur
    if (data.status === 'failure') {
      await FailedAttemptsService.incrementAttempts(data.ip);
    } else {
      // Réinitialiser en cas de succès
      await FailedAttemptsService.resetAttempts(data.ip);
    }

    const securityEvent: SecurityEvent = {
      action: `Suspicious activity detected for user ${data.userId}`,
      ip: data.ip,
      status: data.status,
      message: `User agent: ${data.userAgent}, Location: ${data.location}`,
      userId: data.userId,
    };
    logger.log('security', securityEvent);
  }

  /**
   * Vérifie les activités suspectes pour les tentatives de force brute 
   */
  public static async check(req: Request, res: Response): Promise<string | null> {
    try {
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const now = Date.now();
      const userId = (req as any).user?.id; // Assuming user is attached to request

      // Vérification des tentatives échouées et délai exponentiel
      const attempt = await FailedAttemptsService.getAttempts(ip);
      
      // Réinitialiser le compteur après 24h
      if (attempt && (now - attempt.lastAttempt) > this.RESET_AFTER) {
        await FailedAttemptsService.resetAttempts(ip);
      } else if (attempt && attempt.count >= this.MAX_ATTEMPTS) {
        // Enregistrer l'activité suspecte pour tentatives multiples
        await this.logSuspiciousActivity(userId || null, {
          type: 'multiple_failed_attempts',
          severity: 'high',
          details: { 
            count: attempt.count, 
            ip,
            lastAttempt: new Date(attempt.lastAttempt)
          },
          timestamp: now
        });

        // Délai exponentiel : 2^count minutes (max 24h)
        const delay = Math.min(Math.pow(2, attempt.count) * 60 * 1000, this.RESET_AFTER);
        const timeLeft = attempt.lastAttempt + delay - now;

        if (timeLeft > 0) {
          const minutes = Math.ceil(timeLeft / (60 * 1000));
          if (process.env.NODE_ENV !== 'development') {
            res.status(429).json({
              message: `Trop de tentatives échouées. Veuillez réessayer dans ${minutes} minute${minutes > 1 ? 's' : ''}.`
            });
            return 'Trop de tentatives échouées. Veuillez réessayer dans ' + minutes + ' minute' + (minutes > 1 ? 's' : '');
          } else {
            logger.warn('FailedAttemptsService déclenché, mais pas en developpement', {
              type: 'rate_limit_exceeded',
              severity: 'medium',
              details: { 
                count: attempt.count, 
                ip,
                lastAttempt: new Date(attempt.lastAttempt)
              },
              timestamp: now
            })
          }
        }
        
        await FailedAttemptsService.resetAttempts(ip);
      }

      // Vérifications supplémentaires si l'utilisateur est connecté
      if (userId) {
        const suspiciousActivities: SuspiciousActivity[] = [];

        // Vérification des connexions multiples depuis différentes IPs
        const userHistory = await ConnectionLoggerService.getUserConnectionHistory(userId);
        const uniqueIPs = new Set(userHistory.map(log => log.ip));
        if (uniqueIPs.size > this.THRESHOLDS.MAX_IPS_PER_USER) {
          suspiciousActivities.push({
            type: 'multiple_ips',
            severity: 'medium',
            details: { 
              count: uniqueIPs.size, 
              userId,
              ips: Array.from(uniqueIPs),
              timeWindow: this.THRESHOLDS.TIME_WINDOW
            },
            timestamp: now
          });
        }

        // Vérification des connexions multiples depuis une même IP
        const ipHistory = await ConnectionLoggerService.getIPConnectionHistory(ip);
        const uniqueUsers = new Set(ipHistory.map(log => log.userId));
        if (uniqueUsers.size > this.THRESHOLDS.MAX_USERS_PER_IP) {
          suspiciousActivities.push({
            type: 'multiple_users_same_ip',
            severity: 'medium',
            details: { 
              count: uniqueUsers.size, 
              ip,
              users: Array.from(uniqueUsers),
              timeWindow: this.THRESHOLDS.TIME_WINDOW
            },
            timestamp: now
          });
        }

        // Vérification des changements de localisation
        if (userHistory.length > 0) {
          const lastLocation = userHistory[0].location;
          const currentLocation = req.headers['cf-ipcountry'] as string || req.headers['x-real-country'] as string;
          
          if (lastLocation && currentLocation && 
              this.isLocationChangeSuspicious(lastLocation, currentLocation)) {
            suspiciousActivities.push({
              type: 'suspicious_location_change',
              severity: 'high',
              details: {
                previousLocation: lastLocation,
                newLocation: currentLocation,
                userId,
                timeDiff: now - userHistory[0].timestamp,
                threshold: this.THRESHOLDS.LOCATION_CHANGE_THRESHOLD
              },
              timestamp: now
            });

            // Vérifier si le pays est dans la liste des pays suspects
            if (this.THRESHOLDS.SUSPICIOUS_COUNTRIES.includes(currentLocation)) {
              suspiciousActivities.push({
                type: 'suspicious_country_access',
                severity: 'high',
                details: {
                  country: currentLocation,
                  userId,
                  ip
                },
                timestamp: now
              });
            }
          }
        }

        // Enregistrer et notifier pour chaque activité suspecte
        for (const activity of suspiciousActivities) {
          await this.logSuspiciousActivity(userId, activity);
          if (activity.severity === 'high') {
            await this.notifyAdministrators(activity);
          }
        }
      }

      // Calcul du score de risque
      const score = await this.calculateRiskScore(req);
      
      if (score >= this.THRESHOLD) {
        const highRiskActivity: SuspiciousActivity = {
          type: 'high_risk_score',
          severity: 'high' as const,
          details: { 
            score, 
            ip,
            threshold: this.THRESHOLD,
            patterns: this.getMatchedPatterns(req),
            headers: this.getSuspiciousHeaders(req)
          },
          timestamp: now
        };
        
        await this.logSuspiciousActivity(userId || null, highRiskActivity);
        await this.notifyAdministrators(highRiskActivity);
        return 'High risk activity detected';
      }

      // Middleware pour enregistrer les échecs
      res.on('finish', async () => {
        if (res.statusCode === 401) {
          await FailedAttemptsService.incrementAttempts(ip);
          // Enregistrer la tentative échouée
          await this.logSuspiciousActivity(userId || null, {
            type: 'authentication_failure',
            severity: 'low' as const,
            details: {
              ip,
              attempt: attempt ? attempt.count + 1 : 1,
              userAgent: req.headers['user-agent']
            },
            timestamp: now
          });
        } else if (res.statusCode === 200) {
          // Réinitialiser en cas de succès
          await FailedAttemptsService.resetAttempts(ip);
        }
      });

      return null;
    } catch (error) {
      logger.error('Error in suspicious activity detection:', error);
      return null;
    }
  }

  private static async calculateRiskScore(req: Request): Promise<number> {
    let score = 0;
    const ip = req.ip || req.socket.remoteAddress || 'unknown';

    // Check request patterns
    score += this.checkPatterns(req);

    // Check request frequency
    const requestCount = await this.getRequestCount(ip);
    if (requestCount > 100) score += 20;
    if (requestCount > 500) score += 40;

    // Check for suspicious headers
    score += this.checkHeaders(req);

    // Check for suspicious query parameters
    score += this.checkQueryParams(req);

    // Check for suspicious payload
    score += this.checkPayload(req);

    return score;
  }

  private static checkPatterns(req: Request): number {
    let score = 0;
    const testString = JSON.stringify({
      url: req.url,
      body: req.body,
      query: req.query,
      params: req.params
    });

    for (const pattern of this.patterns) {
      if (pattern.pattern.test(testString)) {
        score += pattern.score;
        this.logPattern(req, pattern);
      }
    }

    return score;
  }

  private static checkHeaders(req: Request): number {
    let score = 0;
    const suspiciousHeaders = [
      'x-originating-ip',
      'x-forwarded-for',
      'x-remote-addr',
      'x-remote-ip'
    ];

    for (const header of suspiciousHeaders) {
      if (req.headers[header]) score += 10;
    }

    // Check for missing or suspicious User-Agent
    const userAgent = req.headers['user-agent'] || '';
    if (!userAgent || userAgent.length < 10) score += 15;
    if (/curl|wget|postman|insomnia/i.test(userAgent)) score += 10;

    return score;
  }

  private static checkQueryParams(req: Request): number {
    let score = 0;
    const params = Object.keys(req.query).length;
    
    if (params > 10) score += 10;
    if (params > 20) score += 20;

    return score;
  }

  private static checkPayload(req: Request): number {
    let score = 0;
    
    if (req.body) {
      const size = JSON.stringify(req.body).length;
      if (size > 1000) score += 10;
      if (size > 10000) score += 20;
    }

    return score;
  }

  private static async getRequestCount(ip: string): Promise<number> {
    const key = `request_count:${ip}`;
    const count = await this.redis.incr(key);
    await this.redis.expire(key, this.WINDOW_SIZE);
    return count;
  }

  private static async logSuspiciousActivity(userId: string | null, activity: SuspiciousActivity) {
    const key = `${this.PREFIX}${userId || 'anonymous'}:${Date.now()}`;
    try {
      await this.redis.setex(key, this.ALERT_EXPIRY, JSON.stringify(activity));

      // Log de sécurité
      const securityEvent: SecurityEvent = {
        action: 'suspicious_activity_detected',
        ip: activity.details.ip || 'unknown',
        status: 'failure',
        message: `User ID: ${userId}, Activity type: ${activity.type}`,
        userId: userId || undefined,
      };
      logger.log('security', securityEvent);

      // Notification des administrateurs pour les activités à haute sévérité
      if (activity.severity === 'high') {
        await this.notifyAdministrators(activity);
      }
    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement de l\'activité suspecte:', error);
    }
  }

  private static logPattern(req: Request, pattern: SuspiciousPattern) {
    const securityEvent: SecurityEvent = {
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      action: 'pattern_detected',
      status: 'failure', // Changé de 'warning' à 'failure' car c'est un pattern suspect
      message: `Pattern: ${pattern.description}, URL: ${req.url}, Method: ${req.method}`,
      userId: req.user?.id || req.user?.userId || undefined,
    };
    logger.log('security', securityEvent);
  }

  private static isLocationChangeSuspicious(location1: string, location2: string): boolean {
    // Simple check for country change
    // Pour une version plus sophistiquée, on pourrait utiliser une API de géolocalisation
    // pour calculer la distance réelle entre les deux points
    return location1 !== location2;
  }

  private static async notifyAdministrators(activity: SuspiciousActivity): Promise<void> {
    try {
      // Ici, implémentez la logique de notification (email, SMS, webhook, etc.)
      logger.info('Admin notification for suspicious activity:', activity);
      
      // Exemple : Envoyer à un webhook Discord ou Slack
      // await axios.post(process.env.ADMIN_WEBHOOK_URL, {
      //   text: `🚨 High severity suspicious activity detected!\nType: ${activity.type}\nDetails: ${JSON.stringify(activity.details)}`
      // });
    } catch (error) {
      logger.error('Error notifying administrators:', error);
    }
  }

  public static async getUserSuspiciousActivities(userId: string): Promise<SuspiciousActivity[]> {
    try {
      const keys = await this.redis.keys(`${this.PREFIX}${userId}:*`);
      const activities: SuspiciousActivity[] = [];

      for (const key of keys) {
        const activity = await this.redis.get(key);
        if (activity) {
          activities.push(JSON.parse(activity));
        }
      }

      return activities.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      logger.error('Error retrieving suspicious activities:', error);
      return [];
    }
  }

  /**
   * Nettoie les anciennes activités suspectes de la base de données
   */
  public static async cleanup(): Promise<void> {
    try {
      if (!this.redis) {
        logger.warn('Redis client n\'est pas initialisé pour le nettoyage au démarrage du serveur');
        return;
      }
      const keys = await this.redis.keys(`${this.PREFIX}*`);
      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl <= 0) {
          await this.redis.del(key);
        }
      }
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }

  private static getMatchedPatterns(req: Request): Array<{ pattern: string; score: number }> {
    const matchedPatterns = [];
    const testString = JSON.stringify({
      url: req.url,
      body: req.body,
      query: req.query,
      params: req.params
    });

    for (const pattern of this.patterns) {
      if (pattern.pattern.test(testString)) {
        matchedPatterns.push({
          pattern: pattern.description,
          score: pattern.score
        });
      }
    }

    return matchedPatterns;
  }

  private static getSuspiciousHeaders(req: Request): Array<string> {
    const suspiciousHeaders = [];
    const headersToCheck = [
      'x-originating-ip',
      'x-forwarded-for',
      'x-remote-addr',
      'x-remote-ip',
      'x-client-ip',
      'x-real-ip'
    ];

    for (const header of headersToCheck) {
      if (req.headers[header]) {
        suspiciousHeaders.push(header);
      }
    }

    return suspiciousHeaders;
  }
}
