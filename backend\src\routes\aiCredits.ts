import express, { Router } from 'express';
import { getAiCredits, useAiCredit, buyAiCredits, createStripeCheckoutSession, handleStripeWebhook, getAiCreditsHistory } from '../controllers/aiCreditsController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';

const router = Router();

// Rate limiter pour les requêtes de crédits IA
const aiCreditsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes concernant les crédits IA. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour récupérer le nombre de crédits IA
router.get('/', aiCreditsLimiter, asyncHandler(getAiCredits));

// Route pour utiliser un crédit IA
router.post('/use', aiCreditsLimiter, asyncHandler(useAiCredit));

// Route pour acheter des crédits IA avec des Jobi
router.post('/buy', aiCreditsLimiter, asyncHandler(buyAiCredits));

// Route pour créer une session de paiement Stripe pour l'achat de crédits IA
router.post('/buy-stripe', aiCreditsLimiter, asyncHandler(createStripeCheckoutSession));

// Route pour le webhook Stripe (pas de rate limiter ni d'authentification)
router.post('/webhook', express.raw({ type: 'application/json' }), asyncHandler(handleStripeWebhook));

// Route pour récupérer l'historique des crédits IA
router.get('/history', aiCreditsLimiter, asyncHandler(getAiCreditsHistory));

export default router;
