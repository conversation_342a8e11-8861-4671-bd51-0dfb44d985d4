import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Button, CircularProgress, Paper, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { missionsApi, Mission } from './missionsApi';
import { notify } from '../../../components/Notification';
import MissionsLayout from './MissionsLayout';
import { FilterBar } from './FilterBar/FilterBar';
import { useLocation, useNavigate } from 'react-router-dom';
import { Lightbulb } from 'lucide-react';
import { debounce } from 'lodash';
import ProposalModal from './ProposalModal';
import { MissionCardRef } from './MissionCard';


const EmptyStateInfo = styled(Paper)(({  }) => ({
  padding: '24px',
  marginTop: '24px',
  backgroundColor: '#FFF8F3',
  border: '2px solid #FFE4BA',
  borderRadius: '12px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: '16px',
}));

const ServiceExample = styled(Box)({
  backgroundColor: 'white',
  padding: '16px',
  borderRadius: '12px',
  border: '1px solid #FFE4BA',
  marginTop: '8px',
  width: '100%',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateX(4px)',
    borderColor: '#FF965E',
    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
  },
});

const ServiceIcon = styled(Box)({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '32px',
  height: '32px',
  borderRadius: '8px',
  backgroundColor: '#FFF8F3',
  marginRight: '12px',
  '& svg': {
    color: '#FF6B2C',
  },
});

const EmptyState = () => (
  <EmptyStateInfo>
    <Lightbulb size={32} color="#FF6B2C" />
    <Typography variant="h6" color="#FF6B2C" textAlign="center" fontWeight="bold">
      Aucune mission ne correspond à votre profil pour le moment
    </Typography>
    <Typography variant="body1" component="div" color="text.secondary" textAlign="center">
      Les missions sont filtrées en fonction des services que vous proposez dans votre profil.
      Ajoutez des services pour voir plus de missions !
    </Typography>
    <Box sx={{ width: '100%', maxWidth: '500px' }}>
      <Typography variant="subtitle1" color="#FF6B2C" sx={{ mt: 2, mb: 1, fontWeight: '300' }}>
        Exemples de services que vous pouvez ajouter :
      </Typography>
      <ServiceExample>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ServiceIcon>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 19C12 19 19 15 19 10C19 6.13401 15.866 3 12 3C8.13401 3 5 6.13401 5 10C5 15 12 19 12 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10C10 11.1046 10.8954 12 12 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </ServiceIcon>
          <Box>
            <Typography variant="body1" component="div" color="text.primary" fontWeight="bold">Jardinage</Typography>
            <Typography variant="body2" color="text.secondary">
              Tonte de pelouse, taille de haies, entretien du jardin...
            </Typography>
          </Box>
        </Box>
      </ServiceExample>
      <ServiceExample>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ServiceIcon>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </ServiceIcon>
          <Box>
            <Typography variant="body1" component="div" color="text.primary" fontWeight="bold">Bricolage</Typography>
            <Typography variant="body2" color="text.secondary">
              Montage de meubles, petites réparations, peinture...
            </Typography>
          </Box>
        </Box>
      </ServiceExample>
      <ServiceExample>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ServiceIcon>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 5.172C10 3.172 12 2 14 2s4 1.172 4 3.172c0 1.828-1.828 3.172-4 3.172s-4-1.344-4-3.172z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M18 10c0 3-2 4-4 4s-4-1-4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 20c0-1.657-1.343-3-3-3s-3 1.343-3 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </ServiceIcon>
          <Box>
            <Typography variant="body1" component="div" color="text.primary" fontWeight="bold">Garde d'animaux</Typography>
            <Typography variant="body2" color="text.secondary">
              Promenade de chiens, visites à domicile, garde temporaire...
            </Typography>
          </Box>
        </Box>
      </ServiceExample>
    </Box>
    <Button
      variant="contained"
      sx={{
        backgroundColor: '#FF6B2C',
        '&:hover': { backgroundColor: '#FF965E' },
        mt: 2,
        px: 4,
        py: 1.5,
        borderRadius: '12px',
        textTransform: 'none',
        fontWeight: 'bold',
        boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
      }}
      href="/dashboard/profil"
    >
      Compléter mon profil
    </Button>
  </EmptyStateInfo>
);

const LoadingIndicator = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  padding: '20px',
});

const MatchingMissions: React.FC<{
  showRejected?: boolean;
  onToggleRejected?: () => void;
}> = ({
  showRejected = false,
  onToggleRejected
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [missions, setMissions] = useState<Mission[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [rejectedMissions, setRejectedMissions] = useState<string[]>([]);
  const [highlightedMissionId, setHighlightedMissionId] = useState<string | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement | null>(null);

  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [budgetFilters, setBudgetFilters] = useState<string[]>([]);
  const [paymentFilters, setPaymentFilters] = useState<string[]>([]);
  const [categoryFilters, setCategoryFilters] = useState<string[]>([]);
  const [subcategoryFilters, setSubcategoryFilters] = useState<string[]>([]);
  const [likedFilter, setLikedFilter] = useState<string[]>([]);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [showProposalModal, setShowProposalModal] = useState(false);
  const [isFilterBarExpanded, setIsFilterBarExpanded] = useState(false);
  const [currentMissionCardRef, setCurrentMissionCardRef] = useState<React.RefObject<MissionCardRef> | null>(null);
  const [offresEnvoyeesFilter, setOffresEnvoyeesFilter] = useState<string[]>([]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const missionId = params.get('mission');
    if (missionId) {
      setHighlightedMissionId(missionId);
      params.delete('mission');
      navigate({ search: params.toString() }, { replace: true });
    }
  }, [location.search, navigate]);

  // Ajouter un écouteur d'événement pour ouvrir la modale de proposition depuis la modale de commentaires
  useEffect(() => {
    const handleOpenProposalModal = (event: CustomEvent) => {
      const { missionId } = event.detail;
      const mission = missions.find(m => m.id === missionId);
      if (mission) {
        setSelectedMission(mission);
        setShowProposalModal(true);
      }
    };

    window.addEventListener('open-proposal-modal', handleOpenProposalModal as EventListener);

    return () => {
      window.removeEventListener('open-proposal-modal', handleOpenProposalModal as EventListener);
    };
  }, [missions]);

  const toggleFilter = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees') => {
    const setters: Record<string, React.Dispatch<React.SetStateAction<string[]>>> = {
      status: setStatusFilters,
      budget: setBudgetFilters,
      payment: setPaymentFilters,
      category: setCategoryFilters,
      subcategory: setSubcategoryFilters,
      liked: setLikedFilter,
      offres_envoyees: setOffresEnvoyeesFilter
    };

    if (category === 'payment' || category === 'budget') {
      const setter = category === 'payment' ? setPaymentFilters : setBudgetFilters;
      setter(prev => {
        if (prev.includes(filter)) {
          return [];
        }
        return [filter];
      });
      return;
    }

    if (setters[category]) {
      setters[category](prev => {
        if (prev.includes(filter)) {
          return prev.filter(f => f !== filter);
        }
        return [...prev, filter];
      });
    }
  };

  const isFilterActive = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees'): boolean => {
    const filters: Record<string, string[]> = {
      status: statusFilters,
      budget: budgetFilters,
      payment: paymentFilters,
      category: categoryFilters,
      subcategory: subcategoryFilters,
      liked: likedFilter,
      offres_envoyees: offresEnvoyeesFilter
    };
    return filters[category]?.includes(filter) || false;
  };

  const handleCategoryFilterChange = (categories: string[], subcategories: string[]) => {
    setCategoryFilters(categories);
    setSubcategoryFilters(subcategories);
  };

  const fetchMissions = async (pageNumber: number, isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const limit = 6; // Limite constante par page

      // Séparer les statuts normaux et le statut urgent
      const normalStatuses = statusFilters.filter(status => ['en_cours', 'terminee', 'annulee', 'en_moderation'].includes(status));
      const isUrgentSelected = statusFilters.includes('urgent');

      // Construire les paramètres de filtrage
      const filterParams: Record<string, any> = {
        status: normalStatuses.length > 0 ? normalStatuses : undefined,
        search: searchTerm || undefined,
        categories: categoryFilters.length > 0 ? categoryFilters : undefined,
        subcategories: subcategoryFilters.length > 0 ? subcategoryFilters : undefined,
        budget_types: budgetFilters.length > 0 ? budgetFilters : undefined,
        payment_methods: paymentFilters.length > 0 ? paymentFilters : undefined,
        is_urgent: isUrgentSelected || undefined,
        liked: likedFilter.includes('liked') || undefined,
        offres_envoyees: offresEnvoyeesFilter.includes('offres_envoyees') || undefined,
        sort_by: 'date_creation' // Ajouter un tri explicite par date de création
      };

      // Supprimer les paramètres undefined
      Object.keys(filterParams).forEach(key => {
        if (filterParams[key] === undefined) {
          delete filterParams[key];
        }
      });

      const response = await missionsApi.getMatchingMissions(pageNumber, showRejected, limit, filterParams);

      const newMissions = response.data || [];

      if (isLoadMore) {
        setMissions(prev => [...prev, ...newMissions]);
      } else {
        setMissions(newMissions);
      }

      // Mettre à jour hasMore en fonction du nombre total de missions
      setHasMore(response.total > (pageNumber * limit));
    } catch (error) {
      notify('Erreur lors de la récupération des missions', 'error');
      if (!isLoadMore) {
        setMissions([]);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleFetchMissions = useCallback(async (pageNum: number) => {
    await fetchMissions(pageNum);
  }, [showRejected]);

  useEffect(() => {
    handleFetchMissions(1);
  }, [handleFetchMissions]);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '900px',
      threshold: 0.1, // Réduire le seuil pour déclencher plus tôt
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loading && !loadingMore) {
        requestAnimationFrame(() => {
          setPage(prevPage => {
            const nextPage = prevPage + 1;
            fetchMissions(nextPage, true);
            return nextPage;
          });
        });
      }
    };

    const observer = new IntersectionObserver(handleIntersection, options);
    observerRef.current = observer;

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, loadingMore]);

  const handleReject = async (missionId: string) => {
    try {
      await missionsApi.rejectMission(missionId);
      setRejectedMissions(prev => [...prev, missionId]);

      // Recharger les missions immédiatement avec le même état de showRejected
      await fetchMissions(1);

      // Si on est en mode "missions rejetées", recharger à nouveau pour afficher la nouvelle mission rejetée
      if (showRejected) {
        setTimeout(() => fetchMissions(1), 100);
      }
    } catch (error) {
      notify('Erreur lors du rejet de la mission', 'error');
    }
  };

  const handleMissionUpdate = useCallback((updatedMission: Mission) => {
    setMissions(prev => prev.map(mission =>
      mission.id === updatedMission.id ? updatedMission : mission
    ));
  }, []);

  const handleSearch = useCallback(debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1);
    fetchMissions(1);
  }, 300), []);

  useEffect(() => {
    // On recharge toujours les données quand les filtres changent
    requestAnimationFrame(() => {
      setPage(1);
      fetchMissions(1);
    });
  }, [statusFilters, budgetFilters, paymentFilters, categoryFilters, subcategoryFilters, likedFilter, searchTerm, offresEnvoyeesFilter]);

  const resetAllFilters = () => {
    setStatusFilters([]);
    setBudgetFilters([]);
    setPaymentFilters([]);
    setCategoryFilters([]);
    setSubcategoryFilters([]);
    setLikedFilter([]);
    setSearchTerm('');
  };

  const filteredMissions = missions
    .filter(mission => {
      // Si on affiche les missions rejetées, on garde uniquement les missions rejetées
      if (showRejected) {
        return mission.is_rejected === true;
      }
      // Sinon, on exclut les missions rejetées
      return mission.is_rejected !== true;
    })
    .filter(mission => {
      // Filtrage offres envoyées :
      if (offresEnvoyeesFilter.length === 0) {
        // Afficher uniquement les missions où aucune offre n'a été envoyée par l'utilisateur
        return !mission.applications || mission.applications.length === 0;
      } else if (offresEnvoyeesFilter.includes('offres_envoyees')) {
        // Afficher uniquement les missions où une offre a été envoyée par l'utilisateur
        return mission.applications && mission.applications.length > 0;
      }
      return true;
    })
    .filter(mission => {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        mission.titre.toLowerCase().includes(searchLower) ||
        mission.description.toLowerCase().includes(searchLower) ||
        mission.ville.toLowerCase().includes(searchLower) ||
        mission.code_postal.includes(searchLower);

      const matchesStatus = statusFilters.length === 0 || (
        (statusFilters.includes('urgent') && mission.is_urgent)
      );

      const matchesBudget = budgetFilters.length === 0 || (
        (budgetFilters.includes('budget_defini') && mission.budget_defini) ||
        (budgetFilters.includes('budget_non_defini') && !mission.budget_defini)
      );

      const matchesPayment = paymentFilters.length === 0 || (
        (paymentFilters.includes('jobi_only') && mission.payment_method === 'jobi_only') ||
        (paymentFilters.includes('direct_only') && mission.payment_method === 'direct_only') ||
        (paymentFilters.includes('both') && mission.payment_method === 'both')
      );

      const matchesCategories = categoryFilters.length === 0 ||
        (mission.category_id && categoryFilters.includes(mission.category_id));

      const matchesSubcategories = subcategoryFilters.length === 0 ||
        (mission.subcategory_id && subcategoryFilters.includes(mission.subcategory_id));

      const matchesLiked = likedFilter.length === 0 ||
        (likedFilter.includes('liked') && mission.user_has_liked === true);

      return matchesSearch && matchesStatus && matchesBudget &&
             matchesPayment && matchesCategories && matchesSubcategories && matchesLiked;
    });

  const sortedMissions = [...filteredMissions].sort((a, b) => {
    // Priorité 1 : Mettre en avant la mission mise en évidence
    if (a.id === highlightedMissionId) return -1;
    if (b.id === highlightedMissionId) return 1;

    // Priorité 2 : Trier par date de création (de la plus récente à la plus ancienne)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });

  const footerContent = hasMore && !loading && (
    <LoadingIndicator ref={loadingRef}>
      {loadingMore && <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />}
    </LoadingIndicator>
  );

  const getEmptyMessage = () => {
    const hasActiveFilters = statusFilters.length > 0 ||
                           budgetFilters.length > 0 ||
                           paymentFilters.length > 0 ||
                           categoryFilters.length > 0 ||
                           subcategoryFilters.length > 0 ||
                           likedFilter.length > 0 ||
                           searchTerm.length > 0;

    if (showRejected) {
      return "Aucune mission rejetée pour le moment";
    }

    if (hasActiveFilters) {
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '20px'
        }}>
          <div>Aucune mission ne correspond à vos critères de recherche</div>
          <button
            onClick={resetAllFilters}
            style={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold',
              transition: 'background-color 0.3s',
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#FF965E'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#FF6B2C'}
          >
            Réinitialiser les filtres
          </button>
        </Box>
      );
    }
    return "Aucune mission ne correspond à vos services pour le moment";
  };

  return (
    <>
      <MissionsLayout
        missions={sortedMissions}
        loading={loading}
        emptyMessage={getEmptyMessage()}
        onUpdate={handleMissionUpdate}
        onReject={handleReject}
        headerContent={
          <>
            <FilterBar
              statusFilters={statusFilters}
              budgetFilters={budgetFilters}
              paymentFilters={paymentFilters}
              categoryFilters={categoryFilters}
              subcategoryFilters={subcategoryFilters}
              toggleFilter={toggleFilter}
              isFilterActive={isFilterActive}
              showRejectedToggle={true}
              showRejected={showRejected}
              onToggleRejected={onToggleRejected}
              onCategoryFilterChange={handleCategoryFilterChange}
              isExpanded={isFilterBarExpanded}
              onToggleExpand={(expanded: boolean) => setIsFilterBarExpanded(expanded)}
              searchTerm={searchTerm}
              onSearch={handleSearch}
              showSearchField={true}
              offresEnvoyeesFilter={offresEnvoyeesFilter}
              showOffresEnvoyeesFilter={true}
            />

            {(statusFilters.length > 0 ||
              budgetFilters.length > 0 ||
              paymentFilters.length > 0 ||
              categoryFilters.length > 0 ||
              subcategoryFilters.length > 0 ||
              likedFilter.length > 0 ||
              searchTerm.length > 0) && (
              <Box sx={{
                padding: {
                  xs: '0 0 16px 0',
                  sm: '0 0 16px 0'
                },
                display: 'flex',
                justifyContent: 'flex-start'
              }}>
                <Button
                  onClick={resetAllFilters}
                  startIcon={<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                  </svg>}
                  sx={{
                    color: '#FF6B2C',
                    borderColor: '#FFE4BA',
                    '&:hover': {
                      backgroundColor: '#FFF8F3',
                      borderColor: '#FF6B2C'
                    }
                  }}
                  variant="outlined"
                  size="small"
                >
                  Réinitialiser les filtres
                </Button>
              </Box>
            )}
          </>
        }
        footerContent={footerContent}
        emptyStateComponent={!showRejected ? <EmptyState /> : null}
        showRejectedOverlay={showRejected}
        highlightedMissionId={highlightedMissionId}
        showStatus={false}
        onMakeProposal={(mission, missionCardRef) => {
          setSelectedMission(mission);
          setCurrentMissionCardRef(missionCardRef as React.RefObject<MissionCardRef> || null);
          setShowProposalModal(true);
        }}
      />

      {selectedMission && (
        <ProposalModal
          open={showProposalModal}
          onClose={() => setShowProposalModal(false)}
          mission={selectedMission}
          onProposalSubmitted={(proposalData) => {
            setShowProposalModal(false);
            // Mettre à jour l'état userProposal dans MissionCard sans faire de requête API
            if (currentMissionCardRef && currentMissionCardRef.current && proposalData) {
              currentMissionCardRef.current.updateUserProposal(proposalData);
            }
          }}
        />
      )}
    </>
  );
};

export default MatchingMissions;