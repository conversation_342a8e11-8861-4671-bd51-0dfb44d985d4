import React, { useState, useRef, useEffect } from 'react';
import { Text<PERSON>ield, Box, Typography, CircularProgress, Button, IconButton, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Mission } from './missionsApi';
import { notify } from '../../../components/Notification';
import { missionsApi } from './missionsApi';
import DOMPurify from 'dompurify';
import ModalPortal from '../../../components/ModalPortal';
import { X, AlertCircle, CheckCircle2, Clock, MapPin, Euro, Info, ThumbsUp, MessageCircle, User, Coins, ArrowRightCircle, ChevronDown, ArrowRightLeft, Sparkles, HelpCircle } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion, AnimatePresence } from 'framer-motion';
import PredefinedResponsesList from '../../../components/PredefinedResponsesList';
import MissionResponsesManager from '../../../components/MissionResponsesManager';
import { missionResponsesApi, PreMessageInfo } from '../../../services/missionResponsesApi';
import { getCookie, setCookie } from '../../../utils/cookieUtils';
import logger from '@/utils/logger';
import AiGenerationSystem from '../../../components/ai/AiGenerationSystem';
import { useSeoPromotionContext } from '../../../components/SEOBanniere/SeoPromotionProvider';

interface ProposalModalProps {
  open: boolean;
  onClose: () => void;
  mission: Mission | null;
  onProposalSubmitted: (proposalData?: any) => void;
}

// Composant ScrollIndicator
const ScrollIndicator = styled(motion.div)({
  position: 'absolute',
  bottom: '20%',
  right: '20px',
  padding: '12px',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  background: '#FF6B2C',
  color: 'white',
  borderRadius: '12px',
  cursor: 'pointer',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.25)',
  zIndex: 50,
  fontSize: '0.875rem',
  fontWeight: 500,
  '&:hover': {
    background: '#FF7A35',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.3s ease',
});

const ProposalModal: React.FC<ProposalModalProps> = ({
  open,
  onClose,
  mission,
  onProposalSubmitted
}) => {
  const [amount, setAmount] = useState<string>('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMaxAmount, setIsMaxAmount] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showTimeSlotsModal, setShowTimeSlotsModal] = useState(false);
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const [showResponsesManager, setShowResponsesManager] = useState(false);
  const [isLoadingPreMessage, setIsLoadingPreMessage] = useState(false);
  const [showAutoFillInfo, setShowAutoFillInfo] = useState(false);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Hook pour déclencher la promotion SEO
  const { forceShowPromotion, refreshStats } = useSeoPromotionContext();

  // Vérifier si on doit afficher l'info sur le pré-remplissage automatique
  useEffect(() => {
    if (open) {
      const lastInfoDate = getCookie('lastAutoFillInfoDate');
      const today = new Date().toDateString();

      if (!lastInfoDate || lastInfoDate !== today) {
        setShowAutoFillInfo(true);
        // Définir le cookie avec une durée de 24 heures (en secondes)
        setCookie('lastAutoFillInfoDate', today, 24 * 60 * 60);
      }
    }
  }, [open]);

  // Initialiser le montant avec le budget du client s'il est défini
  useEffect(() => {
    if (open && mission && mission.budget_defini && mission.budget > 0 && amount === '') {
      setAmount(mission.budget.toString());
      setIsMaxAmount(mission.budget >= 99999);
    }
  }, [mission, open, amount]);

  // Réinitialiser les champs lorsque le modal est fermé
  useEffect(() => {
    if (!open) {
      setAmount('');
      setMessage('');
      setIsMaxAmount(false);
      setShowConfirmation(false);
    }
  }, [open]);

  // Détecter si le scroll est possible
  useEffect(() => {
    const checkScroll = () => {
      if (modalRef.current) {
        const { scrollHeight, clientHeight } = modalRef.current;
        setShowScrollIndicator(scrollHeight > clientHeight);
      }
    };

    // Vérifier immédiatement
    checkScroll();

    // Vérifier après un court délai pour s'assurer que le contenu est chargé
    const timer = setTimeout(checkScroll, 100);

    // Vérifier lors du redimensionnement
    window.addEventListener('resize', checkScroll);

    // Observer les changements de taille du contenu
    const resizeObserver = new ResizeObserver(checkScroll);
    if (modalRef.current) {
      resizeObserver.observe(modalRef.current);
    }

    return () => {
      window.removeEventListener('resize', checkScroll);
      clearTimeout(timer);
      resizeObserver.disconnect();
    };
  }, [open, mission]); // Ajouter mission comme dépendance

  // Gérer le scroll automatique
  const handleScrollClick = () => {
    if (modalRef.current) {
      modalRef.current.scrollBy({
        top: 350,
        behavior: 'smooth'
      });
    }
  };

  // Cacher l'indicateur au scroll
  const handleScroll = () => {
    setShowScrollIndicator(false);
  };

  // Charger le pré-message lorsque la modal s'ouvre, afin de générer un pré-message quand on fait une proposition de mission (offre de mission)
  useEffect(() => {
    if (open && mission && message === '') {
      loadPreMessage();
    }
  }, [open, mission]);

  // Fonction pour charger le pré-message
  const loadPreMessage = async () => {
    try {
      setIsLoadingPreMessage(true);
      const preMessageInfo = await missionResponsesApi.getPreMessageInfo();

      if (preMessageInfo) {
        const generatedMessage = generatePreMessage(preMessageInfo, mission);
        setMessage(generatedMessage);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement du pré-message:', error);
    } finally {
      setIsLoadingPreMessage(false);
    }
  };

  // Fonction pour générer le pré-message à partir des informations du profil
  const generatePreMessage = (info: PreMessageInfo, mission: Mission | null): string => {
    if (!mission) return '';

    // Fonction pour formater le nom (prénom + initiale du nom)
    const formatName = (prenom: string, nom: string) => {
      return `${prenom} ${nom.charAt(0).toUpperCase()}`;
    };

    // Tableaux de variations pour chaque partie du message
    const salutations = [
      'Bonjour',
      'Bonjour à vous',
      'Bonjour Madame, Monsieur',
      'Bonjour cher Jobbeur',
      'Bonjour, je vous contacte',
      'Bonjour, je me permets de vous écrire',
      'Bonjour, j\'espère que vous allez bien',
      'Bonjour, je vous remercie de votre attention'
    ];

    const presentations = [
      `Je suis ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? ` de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? ` de l'entreprise ${info.profil.nom_entreprise}` : ''} à votre service`,
      `Je m'appelle ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? ` et je représente l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `Je suis ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, fondateur(trice) de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, dirigeant de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `Je me présente, ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? ` de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `Je suis ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, gérant de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, responsable de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `Je suis ${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, propriétaire de l'entreprise ${info.profil.nom_entreprise}` : ''}`,
      `${formatName(info.profil.prenom, info.profil.nom)}${info.profil.type_de_profil === 'entreprise' ? `, à la tête de l'entreprise ${info.profil.nom_entreprise}` : ''}`
    ];

    const expertises = [
      info.services && info.services.length > 0
        ? `Je suis spécialisé dans ${info.services.map(service => service.titre).slice(0, 3).join(', ')}`
        : 'Je suis spécialisé dans les services que vous recherchez',
      info.services && info.services.length > 0
        ? `Mon expertise couvre ${info.services.map(service => service.titre).slice(0, 3).join(', ')}`
        : 'Mon expertise couvre les services que vous recherchez',
      info.services && info.services.length > 0
        ? `Je propose des services de ${info.services.map(service => service.titre).slice(0, 3).join(', ')}`
        : 'Je propose des services adaptés à vos besoins',
      info.services && info.services.length > 0
        ? `Je maîtrise ${info.services.map(service => service.titre).slice(0, 3).join(', ')}`
        : 'Je maîtrise les compétences nécessaires pour cette mission',
      info.services && info.services.length > 0
        ? `Je suis expert en ${info.services.map(service => service.titre).slice(0, 3).join(', ')}`
        : 'Je suis expert dans ce domaine'
    ];

    const experiences = [
      'Avec plusieurs années d\'expérience',
      'Fort de mon expérience',
      'Grâce à mon parcours',
      'En tant que professionnel(le) expérimenté',
      'Avec mon expertise',
      'Fort de mes années de pratique',
      'Grâce à mon expérience professionnelle',
      'En tant que spécialiste confirmé',
      'Avec mon savoir-faire',
      'Grâce à mes compétences professionnelles',
      'Avec mon expérience significative',
      'Fort de mon parcours professionnel',
      'Grâce à mes années d\'expertise',
      'En tant que professionnel(le) qualifié',
      'Avec ma solide expérience',
      'Fort de ma formation et de mon expérience',
      'Grâce à mon expertise approfondie',
      'En tant que spécialiste reconnu',
      'Avec mon expérience terrain',
      'Fort de mes compétences avérées',
      'Grâce à mon expérience diversifiée',
      'En tant que professionnel(le) chevronné',
      'Avec mon expertise technique',
      'Fort de mon expérience pratique',
      'Grâce à mes compétences techniques',
      'En tant que spécialiste expérimenté',
      'Avec mon expérience professionnelle solide',
      'Fort de mon expertise métier',
      'Grâce à mon expérience terrain',
      'En tant que professionnel(le) aguerri'
    ];

    const bioVariations = info.profil.bio ? [
      `\n\n${info.profil.bio}`,
      `\n\nPour plus d'informations : ${info.profil.bio}`,
      `\n\nEn quelques mots : ${info.profil.bio}`,
      `\n\nPour vous donner un aperçu : ${info.profil.bio}`,
      `\n\nPour vous présenter mon profil : ${info.profil.bio}`,
      `\n\nPour vous faire une idée : ${info.profil.bio}`,
      `\n\nPour mieux me connaître : ${info.profil.bio}`,
      `\n\nPour vous donner plus de détails : ${info.profil.bio}`,
      `\n\nPour vous en dire plus : ${info.profil.bio}`,
      `\n\nPour vous donner plus d'informations : ${info.profil.bio}`,
      `\n\nPour vous faire part de mon expérience : ${info.profil.bio}`,
      `\n\nPour vous présenter mon parcours : ${info.profil.bio}`,
      `\n\nPour vous donner un aperçu de mon profil : ${info.profil.bio}`,
      `\n\nPour vous faire découvrir mon profil : ${info.profil.bio}`,
      `\n\nPour vous donner plus de contexte : ${info.profil.bio}`,
      `\n\nPour vous faire une présentation complète : ${info.profil.bio}`,
      `\n\nPour vous donner une vue d'ensemble : ${info.profil.bio}`,
      `\n\nPour vous faire part de mon background : ${info.profil.bio}`,
      `\n\nPour vous présenter mon expérience : ${info.profil.bio}`,
      `\n\nPour vous donner un résumé de mon profil : ${info.profil.bio}`,
      `\n\nPour vous faire découvrir qui je suis : ${info.profil.bio}`,
      `\n\nPour vous donner plus de détails sur mon profil : ${info.profil.bio}`,
      `\n\nPour vous faire une présentation détaillée : ${info.profil.bio}`,
      `\n\nPour vous donner un aperçu de mon parcours : ${info.profil.bio}`,
      `\n\nPour vous faire part de mon expérience professionnelle : ${info.profil.bio}`,
      `\n\nPour vous présenter mon background professionnel : ${info.profil.bio}`,
      `\n\nPour vous donner une idée de mon profil : ${info.profil.bio}`,
      `\n\nPour vous faire découvrir mon expérience : ${info.profil.bio}`,
      `\n\nPour vous donner plus d'informations sur moi : ${info.profil.bio}`,
      `\n\nPour vous faire une présentation de mon profil : ${info.profil.bio}`
    ] : [''];

    const interets = [
      `Je suis intéressé par votre mission "${mission.titre}"`,
      `Votre mission "${mission.titre}" correspond à mes compétences`,
      `Je peux vous aider avec votre mission "${mission.titre}"`,
      `Votre projet "${mission.titre}" correspond à mes compétences`,
      `Je suis disponible pour votre mission "${mission.titre}"`,
      `Je peux réaliser votre mission "${mission.titre}"`,
      `Je suis disponible pour "${mission.titre}"`,
      `Je peux prendre en charge votre mission "${mission.titre}"`,
      `Je suis intéressé par "${mission.titre}"`,
      `Je peux vous aider pour "${mission.titre}"`,
      `Votre mission "${mission.titre}" m\'intéresse particulièrement`,
      `Je serais ravi de vous aider avec "${mission.titre}"`,
      `Je suis très intéressé par votre projet "${mission.titre}"`,
      `Je peux vous accompagner dans votre mission "${mission.titre}"`,
      `Je suis disponible et motivé pour "${mission.titre}"`,
      `Je suis disponible pour réaliser votre mission "${mission.titre}"`,
      `Je souhaite vous proposer mes services pour votre mission "${mission.titre}"`,
      `Votre mission "${mission.titre}" m'intéresse particulièrement`,
      `Je serais ravi de réaliser votre mission "${mission.titre}"`,
      `Votre projet "${mission.titre}" correspond parfaitement à mon profil`,
      `Je suis particulièrement intéressé par votre mission "${mission.titre}"`,
      `Je peux vous apporter mon expertise pour "${mission.titre}"`,
      `Je suis disponible et enthousiaste pour "${mission.titre}"`,
      `Votre mission "${mission.titre}" m\'inspire beaucoup`,
      `Je serais ravi de collaborer sur votre projet "${mission.titre}"`,
      `Je suis très motivé pour votre mission "${mission.titre}"`,
      `Je peux mettre mes compétences au service de "${mission.titre}"`,
      `Je suis disponible et passionné pour "${mission.titre}"`,
      `Votre projet "${mission.titre}" m\'intéresse vivement`,
      `Je serais ravi de contribuer à votre mission "${mission.titre}"`,
      `Je suis particulièrement enthousiaste pour "${mission.titre}"`,
      `Je peux vous apporter une solution adaptée pour "${mission.titre}"`,
      `Je suis disponible et déterminé pour "${mission.titre}"`,
      `Votre mission "${mission.titre}" correspond à mes aspirations`,
      `Je serais ravi de participer à votre projet "${mission.titre}"`,
      `Je suis très intéressé par les défis de "${mission.titre}"`,
      `Je peux mettre mon expérience au service de "${mission.titre}"`,
      `Je suis disponible et engagé pour "${mission.titre}"`,
      `Votre projet "${mission.titre}" m\'attire particulièrement`,
      `Je serais ravi de mettre mes compétences au service de "${mission.titre}"`,
      `Je suis particulièrement motivé pour "${mission.titre}"`,
      `Je peux vous apporter une expertise précieuse pour "${mission.titre}"`,
      `Je suis disponible et passionné par "${mission.titre}"`,
      `Je peux mettre mes compétences au service de votre mission "${mission.titre}"`,
      `Votre mission "${mission.titre}" correspond parfaitement à mon profil`,
      `Je suis disponible et motivé pour réaliser votre mission "${mission.titre}"`,
      `Je suis disponible et passionné pour "${mission.titre}"`,
      `Votre projet "${mission.titre}" m\'intéresse vivement`,
      `Je serais ravi de contribuer à votre mission "${mission.titre}"`,
      `Je suis particulièrement enthousiaste pour "${mission.titre}"`,
      `Je peux vous apporter une solution adaptée pour "${mission.titre}"`,
      `Je suis disponible et déterminé pour "${mission.titre}"`
    ];

    const questions = [
      'Avez-vous des exigences particulières pour cette mission ?',
      'Quelles sont vos attentes précises pour cette mission ?',
      'Avez-vous des contraintes spécifiques à prendre en compte ?',
      'Quels sont les points importants pour vous concernant cette mission ?',
      'Avez-vous des préférences pour la réalisation de cette mission ?',
      'Quelles sont vos priorités pour cette mission ?',
      'Souhaitez-vous me communiquer des informations supplémentaires ?',
      'Y a-t-il des détails importants que je devrais connaître ?',
      'Avez-vous des attentes particulières concernant cette mission ?',
      'Souhaitez-vous préciser certains aspects de la mission ?',
      'Quel est le budget que vous envisagez pour cette mission ?',
      'Quel est le délai que vous souhaitez pour la réalisation de cette mission ?',
      'Quel est le type de mission que vous recherchez ?',
      'Quel est le secteur d\'activité de votre entreprise ?',
      'Quel est le nombre de personnes que vous souhaitez pour cette mission ?',
      'Quel est le type de contrat que vous envisagez ?',
      'Quel est le mode de paiement que vous souhaitez ?'
    ];

    const disponibilites = [
      'Je suis disponible pour en discuter',
      'Je reste à votre disposition',
      'Je suis à votre écoute',
      'Je suis ouvert à la discussion',
      'Je suis disponible pour échanger',
      'Je reste à votre disposition pour échanger',
      'Je suis à votre écoute pour discuter',
      'Je suis disponible pour en parler',
      'Je reste à votre disposition pour discuter',
      'Je suis à votre écoute pour échanger',
      'Je suis disponible pour répondre à vos questions',
      'Je reste à votre disposition pour plus de détails',
      'Je suis à votre écoute pour clarifier les points',
      'Je suis disponible pour approfondir le sujet',
      'Je reste à votre disposition pour échanger plus en détail',
      'Je suis à votre écoute pour toute précision',
      'Je suis disponible pour échanger sur cette mission',
      'Je reste à votre disposition pour répondre à vos questions',
      'Je suis à votre écoute pour toutes les informations',
      'Je suis disponible pour échanger sur cette mission',
      'Je reste à votre disposition pour répondre à vos questions',
      'Je suis à votre écoute pour toutes les informations',
      'Je suis disponible pour échanger sur cette mission',
      'Je reste à votre disposition pour répondre à vos questions'
    ];

    const conclusions = [
      'Cordialement',
      'À bientôt',
      'À votre disposition',
      'Je reste à votre écoute',
      'En vous remerciant',
      'Bien cordialement',
      'Je vous remercie de votre attention',
      'Dans l\'attente de votre retour',
      'Je reste à votre disposition pour échanger',
      'Sincères salutations',
      'Meilleures salutations',
      'À bientôt',
      'À votre disposition',
      'Je reste à votre écoute',
      'En vous remerciant',
      'Bien cordialement',
      'Je vous remercie de votre attention',
      'À bientôt'
    ];

    // Ajout des variations pour le montant de l'offre
    const montantVariations = [
      `\n\nJe vous propose ${amount || 'X'}€ pour cette mission.`,
      `\n\nPour cette mission, je vous propose un tarif de ${amount || 'X'}€.`,
      `\n\nMon tarif pour cette mission est de ${amount || 'X'}€.`,
      `\n\nJe peux réaliser cette mission pour ${amount || 'X'}€.`,
      `\n\nPour cette prestation, je vous propose ${amount || 'X'}€.`,
      `\n\nJe vous propose un tarif de ${amount || 'X'}€ pour cette mission.`,
      `\n\nPour cette mission, je peux intervenir pour ${amount || 'X'}€.`,
      `\n\nJe vous propose de réaliser cette mission pour ${amount || 'X'}€.`,
      `\n\nPour cette prestation, mon tarif est de ${amount || 'X'}€.`,
      `\n\nJe peux vous aider avec cette mission pour ${amount || 'X'}€.`
    ];

    // Fonction pour générer un message aléatoire
    const generateRandomMessage = (): string => {
      // Fonction pour choisir un élément aléatoire d'un tableau
      const randomFromArray = (arr: string[]) => arr[Math.floor(Math.random() * arr.length)];

      // Fonction pour décider aléatoirement si on inclut une partie du message
      const shouldInclude = () => Math.random() > 0.5;

      // Générer le message en combinant les parties aléatoirement
      let message = `${randomFromArray(salutations)},\n\n`;

      message += `${randomFromArray(presentations)}. `;

      if (shouldInclude()) {
        message += `${randomFromArray(experiences)}, `;
      }

      message += `${randomFromArray(expertises)}.`;

      if (shouldInclude()) {
        message += `${randomFromArray(bioVariations)}`;
      }

      message += `\n\n${randomFromArray(interets)}. `;

      if (shouldInclude()) {
        message += `${randomFromArray(disponibilites)}. `;
      }

      message += `${randomFromArray(questions)}`;

      // Ajouter le montant de l'offre
      message += `${randomFromArray(montantVariations)}`;

      message += `\n\n${randomFromArray(conclusions)},\n${formatName(info.profil.prenom, info.profil.nom)}`;

      // Nettoyer le message final
      return cleanGeneratedMessage(message);
    };

    // Générer un message et vérifier sa longueur
    let finalMessage = '';
    let attempts = 0;
    const MAX_ATTEMPTS = 10;
    const MAX_LENGTH = 1000;

    while (attempts < MAX_ATTEMPTS) {
      finalMessage = generateRandomMessage();

      // Si le message est dans la limite de caractères, on le retourne
      if (finalMessage.length <= MAX_LENGTH) {
        return finalMessage;
      }

      attempts++;
    }

    // Si après plusieurs tentatives, on n'a pas réussi à générer un message court,
    // on génère un message minimal
    return `Bonjour,\n\nJe suis ${formatName(info.profil.prenom, info.profil.nom)}. Je suis intéressé par votre mission "${mission.titre}".\n\nJe vous propose ${amount || 'X'}€ pour cette mission.\n\nCordialement,\n${formatName(info.profil.prenom, info.profil.nom)}`;
  };

  const handleSubmit = async () => {
    try {
      if (!mission) return;

      const amountNumber = parseFloat(amount);
      if (isNaN(amountNumber) || amountNumber <= 0) {
        notify('Le montant doit être un nombre positif', 'error');
        return;
      }

      if (message.trim().length < 10) {
        notify('Le message doit contenir au moins 10 caractères', 'error');
        return;
      }

      if (message.length > 1000) {
        notify(`Le message ne doit pas dépasser 1000 caractères (actuellement ${message.length} caractères)`, 'error');
        return;
      }

      // Nettoyer le message avec DOMPurify
      const cleanMessage = DOMPurify.sanitize(message);

      setIsSubmitting(true);
      const response = await missionsApi.makeProposal(mission.id, amountNumber, cleanMessage);
      notify('Votre proposition a été envoyée avec succès', 'success');

      // Déclencher la promotion SEO après la première offre (simple et léger)
      setTimeout(async () => {
        const newStats = await refreshStats();

        // Si c'est la première offre ET SEO pas activé ET pas déjà montré
        if (newStats?.missionsCompleted === 1 &&
            !newStats.seoIndexable &&
            !localStorage.getItem('seoPrompt_first_mission')) {
          forceShowPromotion('first_mission');
        }
      }, 2000);

      // Mettre à jour le compteur d'applications
      if (mission.applications_count !== undefined) {
        mission.applications_count += 1;
      } else {
        mission.applications_count = 1;
      }

      // Créer les données de l'offre pour mettre à jour l'état userProposal
      const proposalData = {
        id: response.proposal.id,
        mission_id: mission.id,
        montant_propose: amountNumber,
        message: cleanMessage,
        statut: 'en_attente',
        created_at: new Date().toISOString()
      };

      // Appeler onProposalSubmitted pour informer le composant parent
      onProposalSubmitted(proposalData);
      onClose();

      // Réinitialiser les champs
      setAmount('');
      setMessage('');
      setShowConfirmation(false);
    } catch (error: any) {
      notify(error.response?.data?.error || 'Erreur lors de l\'envoi de la proposition', 'error', 20000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setShowConfirmation(false);
    setAmount('');
    setMessage('');
    setIsMaxAmount(false);
    onClose();
  };

  const handleProceedToConfirmation = () => {
    if (!amount || message.trim().length < 10) {
      if (!amount) {
        notify('Veuillez entrer un montant', 'error');
      } else if (message.trim().length < 10) {
        notify('Le message doit contenir au moins 10 caractères', 'error');
      }
      return;
    }

    if (message.length > 1000) {
      notify(`Vous ne pouvez pas faire "continuer", le message ne doit pas dépasser 1000 caractères (actuellement ${message.length} caractères)`, 'error', 15000);
      return;
    }

    setShowConfirmation(true);
  };

  const handleBackFromConfirmation = () => {
    setShowConfirmation(false);
  };

  // Limiter le message à 1000 caractères
  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
  };

  const handleSelectResponse = (content: string) => {
    setMessage(content);
  };

  // Limiter le montant à 99999
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numValue = parseFloat(value);

    if (value === '') {
      setAmount('');
      setIsMaxAmount(false);
    } else if (!isNaN(numValue)) {
      if (numValue <= 99999) {
        setAmount(value);
        setIsMaxAmount(numValue === 99999);
      } else {
        setAmount('99999');
        setIsMaxAmount(true);
      }
    }
  };

  // Incrémenter le montant
  const incrementAmount = () => {
    const currentAmount = amount === '' ? 0 : parseFloat(amount);
    if (currentAmount < 99999) {
      const newAmount = Math.min(currentAmount + 1, 99999);
      setAmount(newAmount.toString());
      setIsMaxAmount(newAmount === 99999);
    }
  };

  // Décrémenter le montant
  const decrementAmount = () => {
    const currentAmount = amount === '' ? 0 : parseFloat(amount);
    if (currentAmount > 0) {
      const newAmount = Math.max(currentAmount - 1, 0);
      setAmount(newAmount.toString());
      setIsMaxAmount(false);
    }
  };

  if (!mission || !open) return null;

  // Nettoyer la description HTML
  const cleanDescription = (htmlContent: string) => {
    // Supprimer les balises HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = DOMPurify.sanitize(htmlContent);
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Limiter à 150 caractères
    return textContent.length > 150 ? `${textContent.substring(0, 150)}...` : textContent;
    };

  // Nouvelle fonction pour nettoyer le message généré
  const cleanGeneratedMessage = (message: string): string => {
    // Supprimer les balises HTML et les classes
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = DOMPurify.sanitize(message);

    // Supprimer les classes et attributs data
    const elements = tempDiv.getElementsByTagName('*');
    for (let element of elements) {
      element.removeAttribute('class');
      element.removeAttribute('data-name');
    }

    // Supprimer les balises spécifiques à Tiptap
    const tiptapElements = tempDiv.querySelectorAll('.tiptap-align-center, .tiptap-emoji, strong');
    tiptapElements.forEach(element => {
      const parent = element.parentNode;
      if (parent) {
        parent.replaceChild(document.createTextNode(element.textContent || ''), element);
      }
    });

    // Récupérer le texte en préservant les retours à la ligne
    let textContent = tempDiv.innerHTML
      .replace(/<br\s*\/?>/g, '\n') // Remplacer les <br> par des retours à la ligne
      .replace(/<\/p>/g, '\n') // Remplacer les </p> par des retours à la ligne
      .replace(/<[^>]+>/g, '') // Supprimer toutes les autres balises HTML
      .replace(/&nbsp;/g, ' ') // Remplacer les &nbsp; par des espaces
      .replace(/\n\s*\n/g, '\n\n') // Nettoyer les doubles retours à la ligne
      .trim();

    return textContent;
  };

  // Fonction pour générer du contenu avec l'IA
  const generateWithAI = () => {
    if (!mission) return;

    setIsAiConfirmModalOpen(true);
  };

  // Fonction pour extraire le texte brut du HTML
  const stripHtml = (html: string) => {
    if (!html) return '';
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Fonction pour formater la date et les créneaux
  const formatTimeSlots = (timeSlots: Mission['time_slots'], dateMission: string) => {
    if (!timeSlots || timeSlots.length === 0) {
      return 'Pas de créneaux définis';
    }

    const formattedDate = format(new Date(dateMission), 'dd MMMM yyyy', { locale: fr });

    if (timeSlots.length === 1) {
      return `${formattedDate} - ${timeSlots[0].start} à ${timeSlots[0].end}`;
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <span>{formattedDate}</span>
        <Button
          variant="outlined"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            setShowTimeSlotsModal(true);
          }}
          sx={{
            borderColor: '#FFE4BA',
            color: '#FF6B2C',
            '&:hover': {
              borderColor: '#FF6B2C',
              backgroundColor: 'rgba(255, 107, 44, 0.04)',
            },
          }}
        >
          +{timeSlots.length - 1} créneaux
        </Button>
      </Box>
    );
  };

  // Contenu du formulaire de proposition
  const proposalFormContent = (
    <div
      className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl w-full max-w-[900px] max-h-[90vh] overflow-y-auto mx-auto"
      onClick={(e) => e.stopPropagation()}
      ref={modalRef}
      onScroll={handleScroll}
    >
      {/* Indicateur de scroll */}
      <AnimatePresence>
        {showScrollIndicator && (
          <ScrollIndicator
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            onClick={handleScrollClick}
          >
            <span>Faire défiler</span>
            <ChevronDown size={18} />
          </ScrollIndicator>
        )}
      </AnimatePresence>

      {/* En-tête */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200 sticky top-0 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Faire une proposition</h2>
        <button
          onClick={handleClose}
          className="text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Contenu */}
      <div className="p-6">
        {/* Résumé de la mission */}
        <div className="mb-6 bg-[#FFF8F3] p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-[#FF6B2C] mb-2">Résumé de la mission</h3>
          <h4 className="text-lg font-medium mb-2">{mission.titre}</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="flex items-center">
              <MapPin className="h-5 w-5 text-[#FF6B2C] mr-2" />
              <span>{mission.ville}, {mission.code_postal}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-[#FF6B2C] mr-2" />
              <span>{formatTimeSlots(mission.time_slots, mission.date_mission)}</span>
            </div>
            <div className="flex items-center">
              {mission.payment_method === 'jobi_only' && <Coins className="h-5 w-5 text-[#FF6B2C] mr-2" />}
              {mission.payment_method === 'direct_only' && <Euro className="h-5 w-5 text-[#FF6B2C] mr-2" />}
              {mission.payment_method === 'both' && <ArrowRightLeft className="h-5 w-5 text-[#FF6B2C] mr-2" />}
              <span>
                {mission.budget_defini ? (
                  `${mission.budget} € ${
                    mission.payment_method === 'jobi_only' ? '(Paiement Jobi uniquement)' :
                    mission.payment_method === 'direct_only' ? '(Paiement Direct uniquement)' :
                    '(Paiement Hybride - Jobi ou Direct)'
                  }`
                ) : 'Budget à définir'}
              </span>
            </div>
            {mission.is_urgent && (
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <span className="text-red-500 font-medium">Mission urgente</span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-600 line-clamp-3 mb-2">
            {cleanDescription(mission.description)}
          </div>
        </div>

        {/* Consignes de bienveillance */}
        <div className="mb-6 bg-[#FFE4BA] p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-[#FF6B2C] mb-2">Consignes importantes</h3>
          <ul className="space-y-2 text-sm">
            <li className="flex items-start">
              <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
              <span>Proposez un tarif juste et adapté à la mission, ni trop élevé ni trop bas.</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
              <span>Soyez bienveillant et respectueux dans votre message.</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
              <span>Détaillez vos compétences et expériences pertinentes pour cette mission.</span>
            </li>
            <li className="flex items-start">
              <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
              <span>N'hésitez pas à poser des questions pour clarifier les détails de la mission.</span>
            </li>
          </ul>
        </div>

        {/* Formulaire */}
        <div className="space-y-4">
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Montant proposé (€)
            </label>
            <div className="relative">
              <TextField
                id="amount"
                type="number"
                fullWidth
                value={amount}
                onChange={handleAmountChange}
                inputProps={{
                  min: 0,
                  max: 99999,
                  step: "1"
                }}
                placeholder="Entrez votre tarif"
                size="small"
                className="bg-white"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF6B2C',
                    },
                    '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
                      '-webkit-appearance': 'none',
                      margin: 0,
                    },
                    '& input[type=number]': {
                      '-moz-appearance': 'textfield',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF6B2C',
                  },
                }}
              />
              <div className="absolute right-0 top-0 h-full flex flex-col border-l">
                <button
                  type="button"
                  onClick={incrementAmount}
                  disabled={isMaxAmount}
                  className="flex-1 px-2 flex items-center justify-center hover:bg-gray-100 transition-colors border-b"
                  aria-label="Augmenter le montant"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${isMaxAmount ? 'text-gray-300' : 'text-gray-600'}`}>
                    <path d="m18 15-6-6-6 6"/>
                  </svg>
                </button>
                <button
                  type="button"
                  onClick={decrementAmount}
                  disabled={amount === '' || parseFloat(amount) <= 0}
                  className="flex-1 px-2 flex items-center justify-center hover:bg-gray-100 transition-colors"
                  aria-label="Diminuer le montant"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${amount === '' || parseFloat(amount) <= 0 ? 'text-gray-300' : 'text-gray-600'}`}>
                    <path d="m6 9 6 6 6-6"/>
                  </svg>
                </button>
              </div>
            </div>
            {isMaxAmount && (
              <p className="mt-1 text-xs text-amber-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <path d="M10.29 3.86 1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                  <line x1="12" y1="9" x2="12" y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                Montant maximum atteint (99 999 €)
              </p>
            )}
          </div>

          <div>
            <div className="flex justify-between items-center mb-1">
              <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                Votre message
              </label>
              <div className="flex gap-2">
                <Button
                  variant="text"
                  size="small"
                  onClick={generateWithAI}
                  disabled={isLoadingPreMessage}
                  startIcon={<Sparkles size={16} />}
                  sx={{
                    color: '#FF6B2C',
                    '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.04)' },
                    textTransform: 'none',
                    fontSize: '0.8rem',
                    padding: '4px 8px',
                    minWidth: 'auto'
                  }}
                >
                  {message.length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
                  <Tooltip
                    title={message.length > 0
                      ? "Améliorer le contenu existant avec l'IA"
                      : "Générer du nouveau contenu avec l'IA"}
                    sx={{ ml: 1 }}
                  >
                    <HelpCircle className="h-4 w-4 ml-1" />
                  </Tooltip>
                </Button>
                <Button
                  variant="text"
                  size="small"
                  onClick={loadPreMessage}
                  disabled={isLoadingPreMessage}
                  startIcon={isLoadingPreMessage ? <CircularProgress size={16} sx={{ color: '#FF6B2C' }} /> : <ArrowRightCircle size={16} />}
                  sx={{
                    color: '#FF6B2C',
                    '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.04)' },
                    textTransform: 'none',
                    fontSize: '0.8rem',
                    padding: '4px 8px',
                    minWidth: 'auto'
                  }}
                >
                  {isLoadingPreMessage ? 'Génération en cours...' : 'Régénérer sans IA'}
                </Button>
              </div>
            </div>
            <TextField
              id="message"
              multiline
              minRows={2}
              maxRows={20}
              fullWidth
              value={message}
              onChange={handleMessageChange}
              placeholder="Présentez-vous et expliquez pourquoi vous êtes la personne idéale pour cette mission..."
              size="small"
              className="bg-white"
              error={message.length > 0 && message.length < 10}
              helperText={
                <span style={{
                  color: message.length >= 1000 ? '#FF0000' :
                         message.length >= 700 ? '#FF6B2C' :
                         '#000000',
                  fontWeight: message.length >= 1000 ? 700 : 400
                }}>
                  {message.length}/1000 caractères - Minimum 10 caractères
                </span>
              }
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#FF6B2C',
                },
                '& .MuiFormHelperText-root': {
                  marginLeft: 0,
                },
              }}
              disabled={isLoadingPreMessage}
              InputProps={{
                startAdornment: message && (
                  <IconButton
                    size="small"
                    onClick={() => setMessage('')}
                    sx={{
                      color: '#FF6B2C',
                      position: 'absolute',
                      right: 8,
                      top: 8,
                      zIndex: 1
                    }}
                  >
                    <X size={18} />
                  </IconButton>
                ),
                endAdornment: isLoadingPreMessage && (
                  <CircularProgress size={20} sx={{ color: '#FF6B2C' }} />
                ),
              }}
            />

            {/* Liste des réponses prédéfinies */}
            <PredefinedResponsesList
              onSelectResponse={handleSelectResponse}
              onManageResponses={() => setShowResponsesManager(true)}
            />
          </div>
        </div>
      </div>

      {/* Pied de page */}
      <div className="flex justify-end gap-3 p-4 border-t border-gray-200 sticky bottom-0 bg-white">
        <button
          onClick={handleClose}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          disabled={isSubmitting}
        >
          Annuler
        </button>
        <button
          onClick={handleProceedToConfirmation}
          className="px-4 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center min-w-[120px] gap-2"
          disabled={isSubmitting}
        >
          <span>Continuer</span>
          <ArrowRightCircle size={18} />
        </button>
      </div>
    </div>
  );

  // Contenu de la modale de confirmation
  const confirmationContent = (
    <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-y-auto">
      {/* En-tête */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200 sticky top-0 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Confirmation de votre proposition</h2>
        <button
          onClick={handleClose}
          className="text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Contenu */}
      <div className="p-6">
        {/* Message de confirmation */}
        <div className="mb-6 bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="flex items-start">
            <CheckCircle2 className="h-6 w-6 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-green-700 mb-2">Votre proposition est prête à être envoyée</h3>
              <p className="text-sm text-green-700">
                Veuillez vérifier les informations ci-dessous avant de confirmer l'envoi de votre proposition.
                Une fois envoyée, vous ne pourrez plus la modifier.
              </p>
            </div>
          </div>
        </div>

        {/* Récapitulatif de la proposition */}
        <div className="mb-6 bg-[#FFF8F3] p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-[#FF6B2C] mb-4">Récapitulatif de votre proposition</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <div className="bg-white p-4 rounded-lg border border-[#FFE4BA]">
              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                <User className="h-5 w-5 text-[#FF6B2C] mr-2" />
                Mission
              </h4>
              <p className="text-lg font-semibold mb-2">{mission.titre}</p>
              <div className="flex items-center text-sm text-gray-600 mb-1">
                <MapPin className="h-4 w-4 text-[#FF6B2C] mr-2" />
                <span>{mission.ville}, {mission.code_postal}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 text-[#FF6B2C] mr-2" />
                <span>{formatTimeSlots(mission.time_slots, mission.date_mission)}</span>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-[#FFE4BA]">
              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                <Coins className="h-5 w-5 text-[#FF6B2C] mr-2" />
                Votre offre
              </h4>
              <p className="text-2xl font-bold text-[#FF6B2C] mb-3">{amount} €</p>
              {mission.budget_defini && (
                <div className="text-sm text-gray-600 mb-1">
                  {parseFloat(amount) < mission.budget ? (
                    <span className="text-green-600 font-medium">Votre offre est inférieure au budget indiqué ({mission.budget} €)</span>
                  ) : parseFloat(amount) > mission.budget ? (
                    <span className="text-amber-600 font-medium">Votre offre est supérieure au budget indiqué ({mission.budget} €)</span>
                  ) : (
                    <span className="text-blue-600 font-medium">Votre offre correspond exactement au budget indiqué</span>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-[#FFE4BA] mb-4">
            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
              <MessageCircle className="h-5 w-5 text-[#FF6B2C] mr-2" />
              Votre message
            </h4>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">{message}</p>
          </div>
        </div>

        {/* Conseils */}
        <div className="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <Info className="h-6 w-6 text-blue-500 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-blue-700 mb-2">Conseils pour maximiser vos chances</h3>
              <ul className="space-y-2 text-sm text-blue-700">
                <li className="flex items-start">
                  <ThumbsUp className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>Répondez rapidement aux messages du client après l'envoi de votre proposition.</span>
                </li>
                <li className="flex items-start">
                  <ThumbsUp className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>Soyez flexible sur les horaires et adaptez-vous aux besoins du client.</span>
                </li>
                <li className="flex items-start">
                  <ThumbsUp className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
                  <span>Préparez-vous à répondre à des questions supplémentaires sur votre expérience.</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Pied de page */}
      <div className="flex justify-end gap-3 p-4 border-t border-gray-200 sticky bottom-0 bg-white">
        <button
          onClick={handleBackFromConfirmation}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          disabled={isSubmitting}
        >
          Revenir en arrière
        </button>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center min-w-[120px] gap-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <CircularProgress size={24} sx={{ color: 'white' }} />
          ) : (
            <>
              <span>Envoyer ma proposition</span>
              <ArrowRightCircle size={18} />
            </>
          )}
        </button>
      </div>
    </div>
  );

  // Modale des créneaux horaires
  if (showTimeSlotsModal) {
    return (
      <ModalPortal>
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1400,
          }}
          onClick={() => setShowTimeSlotsModal(false)}
        >
          <Box
            sx={{
              backgroundColor: 'white',
              borderRadius: '24px',
              padding: '24px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '90vh',
              overflowY: 'auto',
              position: 'relative',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <IconButton
              onClick={() => setShowTimeSlotsModal(false)}
              sx={{
                position: 'absolute',
                right: '16px',
                top: '16px',
                color: '#FF6B2C',
              }}
            >
              <X />
            </IconButton>
            <Typography variant="h6" gutterBottom sx={{ color: '#2D3748', fontWeight: 'bold', mb: 3 }}>
              Créneaux disponibles pour {mission.titre}
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {mission.time_slots.map((slot, index) => (
                <Box
                  key={index}
                  sx={{
                    backgroundColor: '#FFF8F3',
                    borderRadius: '12px',
                    padding: '16px',
                    border: '1px solid #FFE4BA',
                  }}
                >
                  <Typography variant="subtitle1" sx={{ color: '#FF6B2C', fontWeight: 600, mb: 1 }}>
                    {format(new Date(slot.date), 'dd MMMM yyyy', { locale: fr })}
                  </Typography>
                  <Typography variant="body1" sx={{ color: '#666' }}>
                    {slot.start} à {slot.end}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </ModalPortal>
    );
  }

  // Contenu de la modale d'information sur le pré-remplissage automatique
  const autoFillInfoContent = (
    <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-y-auto">
      {/* En-tête */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200 sticky top-0 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
          <Sparkles className="h-6 w-6 text-[#FF6B2C]" />
          Pré-remplissage automatique
        </h2>
        <button
          onClick={() => setShowAutoFillInfo(false)}
          className="text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Contenu */}
      <div className="p-6">
        <div className="mb-6 bg-[#FFF8F3] p-4 rounded-lg">
          <div className="flex items-start">
            <Info className="h-6 w-6 text-[#FF6B2C] mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-[#FF6B2C] mb-2">Comment ça marche ?</h3>
              <p className="text-sm text-gray-700">
                Pour vous faciliter la vie, nous pré-remplissons automatiquement votre proposition avec :
              </p>
              <ul className="mt-3 space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                  <span>Un message personnalisé basé sur votre profil et vos compétences</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                  <span>Le montant proposé (si un budget est défini)</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                  <span>Des questions pertinentes pour la mission</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mb-6 bg-[#FFE4BA] p-4 rounded-lg">
          <div className="flex items-start">
            <ThumbsUp className="h-6 w-6 text-[#FF6B2C] mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-[#FF6B2C] mb-2">Vous pouvez toujours modifier</h3>
              <p className="text-sm text-gray-700 mb-2">
                N'hésitez pas à personnaliser le message et le montant selon vos besoins.
                Vous pouvez également utiliser le bouton "Régénérer le message automatique" pour obtenir une nouvelle version.
              </p>
              <p className="text-sm text-[#FF6B2C] font-medium">
                💡 Conseil : Un message personnalisé sera toujours mieux perçu par le client et augmentera vos chances d'être sélectionné !
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Pied de page */}
      <div className="flex justify-end p-4 border-t border-gray-200 sticky bottom-0 bg-white">
        <button
          onClick={() => setShowAutoFillInfo(false)}
          className="px-4 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors"
        >
          J'ai compris
        </button>
      </div>
    </div>
  );

  // Utiliser le ModalPortal avec l'effet de flou intégré
  return (
    <>
      <ModalPortal isOpen={open} onBackdropClick={onClose}>
        {showConfirmation ? confirmationContent : proposalFormContent}
      </ModalPortal>

      {/* Modal de gestion des réponses prédéfinies */}
      <MissionResponsesManager
        isOpen={showResponsesManager}
        onClose={() => setShowResponsesManager(false)}
        onSelectResponse={handleSelectResponse}
      />

      {/* Modal d'information sur le pré-remplissage automatique */}
      <ModalPortal isOpen={showAutoFillInfo} onBackdropClick={() => setShowAutoFillInfo(false)}>
        {autoFillInfoContent}
      </ModalPortal>

      {/* Système de génération IA pour la proposition de mission */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="mission_offer"
          prompt={`
            Informations de la mission:
            - Titre: ${mission?.titre || ''}
            - Description: ${stripHtml(mission?.description || '')}
            - Budget: ${mission?.budget_defini ? `${mission?.budget} €` : 'Non défini'}
            - Lieu: ${mission?.ville || ''}, ${mission?.code_postal || ''}
            - Date: ${mission?.date_mission ? format(new Date(mission.date_mission), 'dd MMMM yyyy', { locale: fr }) : 'Non définie'}
            - Méthode de paiement: ${
              mission?.payment_method === 'jobi_only' ? 'Jobi uniquement' :
              mission?.payment_method === 'direct_only' ? 'Paiement direct uniquement' :
              'Hybride (Jobi ou direct)'
            }
            - Montant proposé: ${amount || 'À définir'} €

            ${message.length > 0 ?
              `- Action: Améliorer le texte existant
            - Texte à améliorer: ${stripHtml(message)}
            - Instructions: Conserve le sens et les informations du texte original, mais améliore le style, la clarté et le professionnalisme. Ne réinvente pas complètement le contenu. Garde le montant proposé tel quel. Mets en valeur tes compétences pertinentes pour cette mission spécifique.`
              :
              `- Action: Générer une nouvelle proposition de mission
            - Instructions: Crée une proposition professionnelle et persuasive pour cette mission spécifique. Mentionne le montant que je propose pour cette mission (${amount || 'X'} €). Mets en avant tes compétences et ton expérience pertinentes pour cette mission. Inclus une brève présentation, pourquoi tu es qualifié, et termine par une question ou une invitation à discuter.`
            }
          `}
          originalPrompt={message.length > 0 ? stripHtml(message) : undefined}
          onComplete={(content) => {
            setIsAiConfirmModalOpen(false);

            if (content) {
              // Mettre à jour le message avec le contenu généré
              setMessage(content);
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}
    </>
  );
};

export default ProposalModal;