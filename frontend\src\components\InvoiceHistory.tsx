import React, { useState, useEffect } from 'react';
import { getDocumentHistory } from '../services/invoice';
import { History, Clock, AlertCircle } from 'lucide-react';

interface HistoryItem {
  id: string;
  invoice_id: string;
  user_id: string;
  action: string;
  details: any;
  created_at: string;
  users: {
    id: string;
    email: string;
  };
}

interface InvoiceHistoryProps {
  documentId: string;
}

const InvoiceHistory: React.FC<InvoiceHistoryProps> = ({ documentId }) => {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      setLoading(true);
      try {
        const response = await getDocumentHistory(documentId);
        setHistory(response.data || []);
        setError(null);
      } catch (err) {
        console.error('Erreur lors de la récupération de l\'historique:', err);
        setError('Impossible de charger l\'historique du document');
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchHistory();
    }
  }, [documentId]);

  // Fonctions pour afficher les détails en français
  const getActionLabel = (action: string): string => {
    switch (action) {
      case 'creation':
        return 'Création';
      case 'changement_statut':
        return 'Changement de statut';
      case 'modification':
        return 'Modification';
      case 'envoi_email':
        return 'Envoi par email';
      case 'telecharger':
        return 'Téléchargement';
      case 'conversion':
        return 'Conversion en facture';
      case 'duplication':
        return 'Duplication';
      case 'annulation':
        return 'Annulation';
      default:
        return action;
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDetailsText = (action: string, details: any): string => {
    if (!details) return '';

    switch (action) {
      case 'changement_statut':
        return `De ${details.ancien_statut || 'N/A'} à ${details.nouveau_statut || 'N/A'}`;
      case 'creation':
        return `Type: ${details.type || 'document'}`;
      case 'conversion':
        return `Facture créée: ${details.facture_id || 'N/A'}`;
      case 'duplication':
        return `Source: ${details.source_id || 'N/A'}`;
      case 'envoi_email':
        return `Destinataire: ${details.email || 'N/A'}`;
      default:
        return JSON.stringify(details);
    }
  };

  if (loading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin h-8 w-8 border-4 border-[#FF7A35] border-t-transparent rounded-full mx-auto mb-2"></div>
        <p>Chargement de l'historique...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center text-red-600 flex flex-col items-center justify-center">
        <AlertCircle size={24} className="mb-2" />
        <p>{error}</p>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500 flex flex-col items-center justify-center">
        <History size={24} className="mb-2" />
        <p>Aucun historique disponible pour ce document</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="px-6 py-4 bg-gradient-to-r from-[#FFF8F3] to-white border-b">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <History size={20} className="mr-2 text-[#FF7A35]" />
          Historique du document
        </h3>
      </div>
      <div className="divide-y divide-gray-100">
        {history.map((item) => (
          <div key={item.id} className="p-4 hover:bg-gray-50">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3 mt-1">
                <Clock size={18} className="text-gray-400" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <span className="font-medium text-gray-700">{getActionLabel(item.action)}</span>
                  <span className="text-sm text-gray-500">{formatDate(item.created_at)}</span>
                </div>
                {item.details && (
                  <p className="text-sm text-gray-600 mt-1">{getDetailsText(item.action, item.details)}</p>
                )}
                <p className="text-xs text-gray-500 mt-2">
                  Par: {item.users?.email || 'Utilisateur inconnu'}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InvoiceHistory; 