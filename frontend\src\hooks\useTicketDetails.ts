import { useState, useEffect, useCallback } from 'react';
import supportTicketService, { 
  Ticket, 
  Comment, 
  Attachment, 
  CreateCommentDto 
} from '../services/supportTicketService';
import { logger } from '../utils/logger';

interface UseTicketDetailsProps {
  ticketId: string | undefined;
}

interface UseTicketDetailsResult {
  ticket: Ticket | null;
  comments: Comment[];
  attachments: Attachment[];
  loading: boolean;
  error: Error | null;
  updateTicketStatus: (status: string) => Promise<boolean>;
  addComment: (message: string, isInternal: boolean) => Promise<boolean>;
  uploadAttachment: (file: File) => Promise<boolean>;
  uploadMultipleAttachments: (files: File[]) => Promise<{success: boolean, progress: number}>;
  downloadAttachment: (attachmentId: string, fileName: string) => Promise<boolean>;
  refreshTicket: () => Promise<void>;
  refreshComments: () => Promise<void>;
  refreshAttachments: () => Promise<void>;
}

export const useTicketDetails = ({ ticketId }: UseTicketDetailsProps): UseTicketDetailsResult => {
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Charger les détails du ticket
  const fetchTicketDetails = useCallback(async () => {
    if (!ticketId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const ticketData = await supportTicketService.getTicket(ticketId);
      setTicket(ticketData);
    } catch (error) {
      logger.error(`Erreur lors de la récupération du ticket ${ticketId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
    }
  }, [ticketId]);

  // Charger les commentaires
  const fetchComments = useCallback(async () => {
    if (!ticketId) return;
    
    try {
      const commentsData = await supportTicketService.getTicketComments(ticketId);
      setComments(commentsData);
    } catch (error) {
      logger.error(`Erreur lors de la récupération des commentaires du ticket ${ticketId}:`, error);
    }
  }, [ticketId]);

  // Charger les pièces jointes
  const fetchAttachments = useCallback(async () => {
    if (!ticketId) return;
    
    try {
      const attachmentsData = await supportTicketService.getTicketAttachments(ticketId);
      setAttachments(attachmentsData);
    } catch (error) {
      logger.error(`Erreur lors de la récupération des pièces jointes du ticket ${ticketId}:`, error);
    } finally {
      setLoading(false);
    }
  }, [ticketId]);

  // Mettre à jour le statut d'un ticket
  const updateTicketStatus = useCallback(async (status: string): Promise<boolean> => {
    if (!ticketId || !ticket) return false;
    
    try {
      setLoading(true);
      setError(null);
      
      const updatedTicket = await supportTicketService.updateTicket(ticketId, { status });
      setTicket(updatedTicket);
      return true;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du statut du ticket ${ticketId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, [ticketId, ticket]);

  // Ajouter un commentaire
  const addComment = useCallback(async (message: string, isInternal: boolean): Promise<boolean> => {
    if (!ticketId) return false;
    
    try {
      setLoading(true);
      setError(null);
      
      const commentData: CreateCommentDto = {
        ticket_id: ticketId,
        message,
        is_internal: isInternal
      };
      
      await supportTicketService.addTicketComment(commentData);
      await fetchComments();
      return true;
    } catch (error) {
      logger.error(`Erreur lors de l'ajout d'un commentaire au ticket ${ticketId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, [ticketId, fetchComments]);

  // Uploader une pièce jointe
  const uploadAttachment = useCallback(async (file: File): Promise<boolean> => {
    if (!ticketId) return false;
    
    try {
      setLoading(true);
      setError(null);
      
      await supportTicketService.uploadAttachment(ticketId, file);
      await fetchAttachments();
      return true;
    } catch (error) {
      logger.error(`Erreur lors de l'upload d'une pièce jointe pour le ticket ${ticketId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, [ticketId, fetchAttachments]);

  // Uploader plusieurs pièces jointes avec suivi de progression
  const uploadMultipleAttachments = useCallback(async (files: File[]): Promise<{success: boolean, progress: number}> => {
    if (!ticketId || files.length === 0) return { success: false, progress: 0 };
    
    try {
      setLoading(true);
      setError(null);
      
      let uploadedCount = 0;
      const totalFiles = files.length;
      
      for (const file of files) {
        try {
          await supportTicketService.uploadAttachment(ticketId, file);
          uploadedCount++;
        } catch (error) {
          logger.error(`Erreur lors de l'upload du fichier ${file.name}:`, error);
          // On continue malgré l'erreur pour traiter les autres fichiers
        }
      }
      
      await fetchAttachments();
      return { 
        success: uploadedCount > 0, 
        progress: (uploadedCount / totalFiles) * 100 
      };
    } catch (error) {
      logger.error(`Erreur lors de l'upload des pièces jointes pour le ticket ${ticketId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return { success: false, progress: 0 };
    } finally {
      setLoading(false);
    }
  }, [ticketId, fetchAttachments]);

  // Télécharger une pièce jointe
  const downloadAttachment = useCallback(async (attachmentId: string, fileName: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const blob = await supportTicketService.downloadAttachment(attachmentId);
      
      // Créer un lien de téléchargement
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      return true;
    } catch (error) {
      logger.error(`Erreur lors du téléchargement de la pièce jointe ${attachmentId}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Charger les données au montage du composant
  useEffect(() => {
    if (ticketId) {
      Promise.all([
        fetchTicketDetails(),
        fetchComments(),
        fetchAttachments()
      ]);
    }
  }, [ticketId, fetchTicketDetails, fetchComments, fetchAttachments]);

  return {
    ticket,
    comments,
    attachments,
    loading,
    error,
    updateTicketStatus,
    addComment,
    uploadAttachment,
    uploadMultipleAttachments,
    downloadAttachment,
    refreshTicket: fetchTicketDetails,
    refreshComments: fetchComments,
    refreshAttachments: fetchAttachments
  };
};

export default useTicketDetails; 