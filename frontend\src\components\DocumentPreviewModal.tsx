import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Document, CompanySettings } from '../services/invoiceService';
import ModalPortal from './ModalPortal';
import { Printer, Download, FileText, X } from 'lucide-react';
import logger from '@/utils/logger';
import useCompanySettings from '../hooks/invoices/useCompanyInvoiceSettings';

interface DocumentPreviewModalProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (document: Document) => void;
  isReceivedQuote?: boolean;
  isReceivedInvoice?: boolean;
  isReceivedCreditNote?: boolean;
}

const DocumentPreviewModal: React.FC<DocumentPreviewModalProps> = ({
  document,
  isOpen,
  onClose,
  onDownload,
  isReceivedQuote,
  isReceivedInvoice,
  isReceivedCreditNote
}) => {
  const [companySettings, setCompanySettings] = useState<CompanySettings | null>(null);
  const { settings: globalCompanySettings } = useCompanySettings();
  const printRef = useRef<HTMLDivElement>(null);

  // CORRECTION : Forcer le re-render quand le document change pour une UX en temps réel
  const [documentKey, setDocumentKey] = useState(0);

  // Mémoriser le contenu du document pour forcer la mise à jour
  const memoizedDocument = useMemo(() => {
    return {
      ...document,
      // Forcer la recalculation en incluant un timestamp
      _lastUpdate: Date.now()
    };
  }, [
    document.id, 
    document.number,
    (document as any).draft_number,
    document.type,
    document.statut, 
    document.client_name,
    (document as any).client_id,
    (document as any).client_address,
    (document as any).client_email,
    (document as any).client_phone,
    (document as any).client_siret,
    (document as any).client_tva,
    document.description,
    document.date_creation,
    (document as any).date_validite,
    (document as any).date_paiement,
    document.total_ht,
    document.total_tva,
    document.total_ttc,
    (document as any).conditions_paiement,
    (document as any).mode_paiement,
    (document as any).mentions_legales,
    (document as any).mentions_tva,
    (document as any).penalite_retard,
    (document as any).indemnite_recouvrement,
    (document as any).notes,
    (document as any).forme_juridique,
    (document as any).code_ape,
    (document as any).user_id,
    (document as any).devis_origine_id,
    (document as any).facture_origine_id,
    (document as any).converti,
    JSON.stringify(document.invoice_items), // Surveiller les changements dans les items
    JSON.stringify((document as any).sender_info) // Surveiller les informations de l'émetteur pour les documents reçus
  ]);

  useEffect(() => {
    // Incrémenter la clé pour forcer le re-render complet du contenu
    setDocumentKey(prev => prev + 1);
    logger.info('Document preview mis à jour en temps réel:', document.id, document.statut);
  }, [memoizedDocument]);

  // Récupérer les paramètres de l'entreprise
  useEffect(() => {
    if (isOpen) {
      // Réinitialiser les paramètres à chaque changement de document pour forcer le rechargement
      setCompanySettings(null);
      
      // Si c'est un document reçu, récupérer les informations de l'émetteur
      if (isReceivedQuote || isReceivedInvoice || isReceivedCreditNote) {
        // Pour les documents reçus, on utilise les informations de l'émetteur
        // qui sont déjà dans le document via sender_info
        const emitterInfo = (memoizedDocument as any).sender_info;
        logger.info('Document reçu - sender_info:', emitterInfo);
        logger.info('Document complet:', memoizedDocument);

        if (emitterInfo) {
          logger.info('Utilisation des informations sender_info:', emitterInfo);
          setCompanySettings({
            nom: emitterInfo.entreprise || `${emitterInfo.prenom} ${emitterInfo.nom}`,
            adresse: emitterInfo.adresse || '',
            code_postal: emitterInfo.code_postal || '',
            ville: emitterInfo.ville || '',
            telephone: emitterInfo.telephone || '',
            email: emitterInfo.email || '',
            site_web: emitterInfo.site_web || '',
            siret: emitterInfo.siret || '',
            tva: emitterInfo.tva || '',
            rcs: emitterInfo.rcs || '',
            capital: emitterInfo.capital || '',
            iban: emitterInfo.iban || '',
            bic: emitterInfo.bic || '',
            banque: emitterInfo.banque || ''
          });
        } else {
          logger.info('Aucune information sender_info trouvée dans le document');
          // Fallback : utiliser les paramètres d'entreprise de l'utilisateur connecté
          setCompanySettings(globalCompanySettings);
        }
      } else {
        // Pour les documents émis, utiliser les paramètres globaux
        setCompanySettings(globalCompanySettings);
      }
    }
  }, [isOpen, isReceivedQuote, isReceivedInvoice, isReceivedCreditNote, memoizedDocument.id, globalCompanySettings]);

  // Format date to localized string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get document type in French
  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case 'devis':
        return 'Devis';
      case 'facture':
        return 'Facture';
      case 'avoir':
        return 'Avoir';
      default:
        return 'Document';
    }
  };

  // Fonctions utilitaires pour la gestion des numéros de documents
  const isDraft = (document: any): boolean => {
    return document.statut === 'brouillon' || document.draft_number !== null;
  };

  const getDisplayNumber = (document: any): string => {
    if (isDraft(document)) {
      const draftNumber = document.draft_number || 'BROUILLON-EN-COURS';
      // Extraire seulement la partie BROUILLON-XXXX pour simplifier l'affichage
      const match = draftNumber.match(/BROUILLON-.*?-.*?-(\d+)$/);
      if (match) {
        return `BROUILLON-${match[1]}`;
      }
      return draftNumber;
    }
    return document.number || 'NUMERO-EN-COURS';
  };

  // Empêche les appels d'impression dupliqués
  const handlePrint = () => {
    const printContent = window.document.getElementById('document-to-print') as HTMLElement;
    
    if (printContent) {
      const printStyles = `
        @page {
          size: A4 portrait;
          margin: 1cm;
        }
        body {
          margin: 0;
          padding: 0;
          background: white;
          font-family: Arial, sans-serif;
          color: black;
        }
        .document-container {
          width: 100%;
          max-width: 210mm;
          padding: 15mm;
          margin: 0 auto;
          background: white;
          box-sizing: border-box;
          position: relative;
          min-height: 100%;
          padding-bottom: 50pt; /* Espace pour le pied de page */
        }
        .print-footer {
          position: fixed;
          bottom: 10pt;
          left: 0;
          right: 0;
          text-align: center;
          font-size: 9pt;
          color: #777;
          border-top: 1pt solid #eee;
          padding-top: 5pt;
        }
        .print-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20pt;
          padding-bottom: 10pt;
          border-bottom: 1pt solid #eee;
        }
        .print-header-date {
          font-size: 9pt;
          color: #777;
        }
        .print-header-title {
          font-size: 9pt;
          color: #777;
          font-weight: bold;
        }
        /* En-tête */
        h1, h2 {
          margin-top: 0;
          color: #000000;
        }
        h1 {
          font-size: 18pt;
          margin-bottom: 10pt;
        }
        h2 {
          font-size: 16pt;
          margin-bottom: 8pt;
          color: #FF7A35;
        }
        h3 {
          font-size: 14pt;
          margin-top: 14pt;
          margin-bottom: 6pt;
          color: #333333;
          font-weight: 600;
        }
        p {
          margin: 0 0 6pt 0;
          line-height: 1.5;
        }
        /* Table */
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 20pt 0;
          font-size: 11pt;
        }
        th {
          background-color: #f9fafb;
          color: #4B5563;
          font-weight: 600;
          text-align: left;
          padding: 8pt 6pt;
          border-bottom: 1pt solid #e5e7eb;
        }
        td {
          padding: 8pt 6pt;
          border-bottom: 0.5pt solid #e5e7eb;
          text-align: left;
        }
        th:last-child,
        td:last-child {
          text-align: right;
        }
        /* Mise en page générale */
        .flex {
          display: flex;
        }
        .justify-between {
          justify-content: space-between;
        }
        .items-start {
          align-items: flex-start;
        }
        .mb-8 {
          margin-bottom: 24pt;
        }
        .mb-6 {
          margin-bottom: 18pt;
        }
        .mb-3 {
          margin-bottom: 9pt;
        }
        .mb-2 {
          margin-bottom: 6pt;
        }
        .mb-1 {
          margin-bottom: 3pt;
        }
        .mt-1 {
          margin-top: 3pt;
        }
        .mt-2 {
          margin-top: 6pt;
        }
        .mt-3 {
          margin-top: 9pt;
        }
        .mt-5 {
          margin-top: 15pt;
        }
        .pt-5 {
          padding-top: 15pt;
        }
        .grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10pt;
        }
        .grid-cols-2 {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10pt;
        }
        .border-t, .border-b {
          border-color: #e5e7eb;
          border-style: solid;
        }
        .border-t {
          border-width: 1pt 0 0 0;
          padding-top: 10pt;
        }
        .border-b {
          border-width: 0 0 1pt 0;
          padding-bottom: 10pt;
        }
        .py-5 {
          padding-top: 15pt;
          padding-bottom: 15pt;
        }
        .text-gray-800 {
          color: #1F2937;
        }
        .text-gray-600 {
          color: #4B5563;
        }
        .font-medium {
          font-weight: 500;
        }
        .font-semibold {
          font-weight: 600;
        }
        .font-bold {
          font-weight: 700;
        }
        .text-right {
          text-align: right;
        }
        .text-sm {
          font-size: 10pt;
        }
        .text-lg {
          font-size: 13pt;
        }
        .text-xl {
          font-size: 15pt;
        }
        .text-2xl {
          font-size: 18pt;
        }
        /* Sections spécifiques */
        .w-64 {
          width: 200pt;
          margin-left: auto;
        }
        .rounded-lg {
          border-radius: 8pt;
        }
        .rounded-full {
          border-radius: 100pt;
        }
        .overflow-hidden {
          overflow: hidden;
        }
        .border {
          border: 1pt solid #e5e7eb;
        }
        .bg-gray-50 {
          background-color: #f9fafb;
        }
        .bg-gray-100 {
          background-color: #f3f4f6;
        }
        .bg-\\[\\#fff8f3\\] {
          background-color: #fff8f3;
        }
        .text-\\[\\#FF7A35\\] {
          color: #FF7A35;
        }
        .px-3 {
          padding-left: 9pt;
          padding-right: 9pt;
        }
        .px-4 {
          padding-left: 12pt;
          padding-right: 12pt;
        }
        .py-1 {
          padding-top: 3pt;
          padding-bottom: 3pt;
        }
        .py-2\\.5, .py-2 {
          padding-top: 7.5pt;
          padding-bottom: 7.5pt;
        }
        .py-3 {
          padding-top: 9pt;
          padding-bottom: 9pt;
        }
        .p-5 {
          padding: 15pt;
        }
        .whitespace-pre-line {
          white-space: pre-line;
        }
        .space-y-1 > * + * {
          margin-top: 3pt;
        }
        .space-y-3 > * + * {
          margin-top: 9pt;
        }
        .gap-4 {
          gap: 12pt;
        }
        .inline-block {
          display: inline-block;
        }
        .max-w-xs {
          max-width: 150pt;
        }
        /* Styles spécifiques pour l'impression des totaux */
        .print-totals {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 24pt;
          page-break-inside: avoid;
        }
        .print-total-box {
          width: 200pt;
          margin-left: auto;
          border: 1pt solid #e5e7eb;
          border-radius: 8pt;
          overflow: hidden;
          page-break-inside: avoid;
        }
        /* Contrôler la taille de l'icône de paiement à l'impression */
        .payment-status-icon {
          height: 16pt !important;
          width: 16pt !important;
          flex-shrink: 0;
          padding-right: 10pt;
        }
        /* Styles pour s'assurer que l'encadré de paiement ne soit pas disproportionné */
        .flex.items-center {
          display: flex;
          align-items: center;
        }
        
        /* Style spécifique pour le bandeau de paiement */
        .payment-status-banner {
          background-color: #f0fdf4 !important;
          border-left: 4pt solid #22c55e !important;
          padding: 10pt !important;
          margin-bottom: 18pt !important;
          border-radius: 6pt !important;
          page-break-inside: avoid;
        }
        
        /* Style pour le texte dans le bandeau de paiement */
        .payment-status-banner h3 {
          font-size: 12pt !important;
          margin-bottom: 3pt !important;
          color: #166534 !important;
          font-weight: 600 !important;
        }
        
        .payment-status-banner p {
          font-size: 10pt !important;
          color: #166534 !important;
          margin-bottom: 0 !important;
        }
        
        /* Contenu du bandeau de paiement */
        .payment-status-banner .flex.items-center {
          display: flex;
          align-items: center;
        }
        
        .payment-status-banner .flex.items-center > div {
          display: block;
        }
        
        /* Styles pour la section informations de paiement */
        .payment-info-section {
          margin-bottom: 18pt !important;
          background-color: #f9fafb !important;
          padding: 12pt 15pt !important;
          border-radius: 6pt !important;
          border: 1pt solid #f3f4f6 !important;
        }
        
        .payment-info-title {
          font-weight: 600 !important;
          color: #1f2937 !important;
          margin-bottom: 9pt !important;
          padding-bottom: 6pt !important;
          border-bottom: 1pt solid #e5e7eb !important;
        }
        
        .payment-info-grid {
          display: grid !important;
          grid-template-columns: 1fr 1fr !important;
          gap: 12pt !important;
          padding-left: 12pt !important;
        }
        
        .payment-info-item {
          margin-bottom: 0 !important;
        }
        
        .payment-info-label {
          color: #4b5563 !important;
          font-weight: 500 !important;
          margin-bottom: 3pt !important;
        }
        
        .payment-info-value {
          color: #1f2937 !important;
          padding-left: 0pt !important;
        }
      `;
      
      // Créer un iframe invisible que nous utiliserons pour l'impression
      const iframe = window.document.createElement('iframe');
      iframe.style.position = 'fixed';
      iframe.style.right = '0';
      iframe.style.bottom = '0';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.border = '0';
      
      window.document.body.appendChild(iframe);
      
      // Récupérer la date du jour formatée
      const today = new Date().toLocaleDateString();
      const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
      
      // Accéder au document de l'iframe une fois qu'il est chargé
      iframe.onload = () => {
        if (iframe.contentWindow) {
          // Créer le contenu du document
          const iframeDoc = iframe.contentWindow.document;
          
          // Contenu HTML complet
          iframeDoc.open();
          iframeDoc.write('<html><head><title>JobPartiel - Impression</title>');
          iframeDoc.write('<style type="text/css">' + printStyles + '</style>');
          iframeDoc.write('</head><body><div class="document-container">');
          iframeDoc.write('<div class="print-header"><span class="print-header-date">' + today + ' ' + time + '</span><span class="print-header-title">JobPartiel - Impression</span></div>');
          iframeDoc.write(printContent.innerHTML);
          iframeDoc.write('<div class="print-footer">Document généré par JobPartiel - www.jobpartiel.fr</div>');
          iframeDoc.write('</div></body></html>');
          iframeDoc.close();
          
          // Imprimer et supprimer l'iframe
          setTimeout(() => {
            iframe.contentWindow?.print();
            
            // Supprimer l'iframe après l'impression
            iframe.onload = null;
            setTimeout(() => window.document.body.removeChild(iframe), 100);
          }, 500);
        }
      };
      
      // Déclencher l'événement onload
      const iframeDoc = iframe.contentWindow?.document;
      if (iframeDoc) {
        iframe.src = 'about:blank';
      }
    }
  };
  
  return (
    <ModalPortal
      isOpen={isOpen}
      onBackdropClick={onClose}
      closeOnBackdropClick={true}
    >
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[92vh] overflow-hidden flex flex-col">
        <div className="flex justify-between items-center p-4 sm:p-5 border-b print:hidden bg-gradient-to-r from-[#FFF8F3] to-white">
          <h3 className="text-base sm:text-xl font-semibold text-gray-800 flex items-center truncate">
            <Printer size={20} className="mr-2 sm:mr-3 text-[#FF7A35] flex-shrink-0" />
            <span className="truncate">
            {memoizedDocument.type === 'facture'
              ? `Aperçu de la ${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
              : memoizedDocument.type === 'avoir'
                ? `Aperçu de l'${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
                : `Aperçu du ${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
            }
            </span>
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full p-2 transition-colors"
          >
            <X size={22} />
          </button>
        </div>
        
        <div className="flex-1 overflow-auto bg-gray-50 p-3 sm:p-6 print-container">
          {/* Document à prévisualiser avec clé pour forcer le re-render */}
          <div
            key={documentKey}
            ref={printRef}
            className="print-document mx-auto bg-white p-4 sm:p-8 border border-gray-200 rounded-lg shadow-sm max-w-4xl"
            id="document-to-print"
          >
            {/* En-tête du document */}
            <div className="flex flex-col sm:flex-row justify-between items-start mb-8 gap-4">
              <div>
                {(() => {
                  // Pour les documents reçus, utiliser les informations de l'émetteur
                  const isReceivedDocument = isReceivedQuote || isReceivedInvoice || isReceivedCreditNote;
                  const senderInfo = (memoizedDocument as any)?.sender_info;

                  if (isReceivedDocument && senderInfo) {
                    logger.info('Affichage des informations de l\'émetteur:', senderInfo);
                    return (
                      <>
                        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
                          {senderInfo.entreprise || `${senderInfo.prenom} ${senderInfo.nom}` || 'Émetteur'}
                        </h1>
                        {senderInfo.adresse && (
                          <p className="text-gray-600 mt-1">{senderInfo.adresse}</p>
                        )}
                        {(senderInfo.code_postal || senderInfo.ville) && (
                          <p className="text-gray-600">
                            {senderInfo.code_postal} {senderInfo.ville}
                          </p>
                        )}
                        {senderInfo.email && (
                          <p className="text-gray-600 mt-1">{senderInfo.email}</p>
                        )}
                        {senderInfo.telephone && (
                          <p className="text-gray-600">{senderInfo.telephone}</p>
                        )}
                        {senderInfo.siret && (
                          <p className="text-gray-600 mt-1">SIRET: {senderInfo.siret}</p>
                        )}
                        {senderInfo.tva && (
                          <p className="text-gray-600">TVA: {senderInfo.tva}</p>
                        )}
                        {senderInfo.forme_juridique && (
                          <p className="text-gray-600">{senderInfo.forme_juridique}</p>
                        )}
                        {senderInfo.code_ape && (
                          <p className="text-gray-600">APE: {senderInfo.code_ape}</p>
                        )}
                        {senderInfo.rcs && (
                          <p className="text-gray-600 mt-1">{senderInfo.rcs}</p>
                        )}
                        {senderInfo.capital && (
                          <p className="text-gray-600">{senderInfo.capital}</p>
                        )}
                        {senderInfo.mention_pied_page && (
                          <p className="text-gray-600 whitespace-pre-line mt-2">{senderInfo.mention_pied_page}</p>
                        )}
                      </>
                    );
                  } else {
                    // Pour les documents émis, utiliser les paramètres de l'entreprise
                    return (
                      <>
                        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
                          {companySettings?.nom || 'Votre Entreprise'}
                        </h1>
                        {companySettings?.adresse && (
                          <p className="text-gray-600 mt-1">{companySettings.adresse}</p>
                        )}
                        {(companySettings?.code_postal || companySettings?.ville) && (
                          <p className="text-gray-600">
                            {companySettings.code_postal} {companySettings.ville}
                          </p>
                        )}
                        {companySettings?.email && (
                          <p className="text-gray-600 mt-1">{companySettings.email}</p>
                        )}
                        {companySettings?.telephone && (
                          <p className="text-gray-600">{companySettings.telephone}</p>
                        )}
                        {companySettings?.siret && (
                          <p className="text-gray-600 mt-1">SIRET: {companySettings.siret}</p>
                        )}
                        {companySettings?.tva && (
                          <p className="text-gray-600">TVA: {companySettings.tva}</p>
                        )}
                        {companySettings?.forme_juridique && (
                          <p className="text-gray-600">{companySettings.forme_juridique}</p>
                        )}
                        {companySettings?.code_ape && (
                          <p className="text-gray-600">APE: {companySettings.code_ape}</p>
                        )}
                        {companySettings?.rcs && (
                          <p className="text-gray-600 mt-1">{companySettings.rcs}</p>
                        )}
                        {companySettings?.capital && (
                          <p className="text-gray-600">{companySettings.capital}</p>
                        )}
                        {companySettings?.mention_pied_page && (
                          <p className="text-gray-600 whitespace-pre-line mt-2">{companySettings.mention_pied_page}</p>
                        )}
                      </>
                    );
                  }
                })()}
              </div>
              
              <div className="text-left sm:text-right mt-4 sm:mt-0">
                <h2 className="text-lg sm:text-xl font-bold text-[#FF7A35]">
                  {memoizedDocument.type === 'facture'
                    ? `${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
                    : memoizedDocument.type === 'avoir'
                      ? `${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
                      : `${getDocumentTypeLabel(memoizedDocument.type)} ${getDisplayNumber(memoizedDocument)}`
                  }
                  {isDraft(memoizedDocument) && (
                    <span className="ml-2 text-base text-gray-500 italic">(Brouillon)</span>
                  )}
                </h2>
                <p className="text-gray-600 mt-1">
                  Date : {formatDate(memoizedDocument.date_creation)}
                </p>
                {memoizedDocument.date_validite && (
                  <p className="text-gray-600">
                    Valable jusqu'au : {formatDate(memoizedDocument.date_validite)}
                  </p>
                )}
                <div className="mt-3">
                  <span className="inline-block px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full font-medium">
                    {(() => {
                      // Formatage des statuts avec les bonnes majuscules et accords
                      const status = memoizedDocument.statut;
                      if (memoizedDocument.type === 'facture') {
                        // Accord féminin pour les factures
                        switch(status) {
                          case 'brouillon': return 'Brouillon';
                          case 'envoye': return 'Envoyée';
                          case 'paye': return 'Payée';
                          case 'en_retard': return 'En retard';
                          case 'annule': return 'Annulée';
                          default: return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Statut inconnu';
                        }
                      } else if (memoizedDocument.type === 'avoir') {
                        // Accord masculin pour les avoirs
                        switch(status) {
                          case 'brouillon': return 'Brouillon';
                          case 'envoye': return 'Envoyé';
                          case 'annule': return 'Annulé';
                          default: return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Statut inconnu';
                        }
                      } else {
                        // Accord masculin pour les devis
                        switch(status) {
                          case 'brouillon': return 'Brouillon';
                          case 'envoye': return 'Envoyé';
                          case 'accepte': return 'Accepté';
                          case 'refuse': return 'Refusé';
                          case 'facture': return 'Facturé';
                          case 'expire': return 'Expiré';
                          case 'annule': return 'Annulé';
                          default: return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Statut inconnu';
                        }
                      }
                    })()}
                  </span>
                </div>
              </div>
            </div>

            {/* Informations du client */}
            <div className="border-t border-b border-gray-200 py-4 sm:py-5 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start">
                <div>
                  <h3 className="text-gray-600 mb-2 font-medium">Facturé à :</h3>
                  <p className="font-semibold text-gray-800 text-base sm:text-lg">{memoizedDocument.client_name}</p>
                  {memoizedDocument.client_address && (
                    <p className="text-gray-600 mt-1">{memoizedDocument.client_address}</p>
                  )}
                  {memoizedDocument.client_email && (
                    <p className="text-gray-600 mt-2">{memoizedDocument.client_email}</p>
                  )}
                  {memoizedDocument.client_phone && (
                    <p className="text-gray-600">{memoizedDocument.client_phone}</p>
                  )}
                  {memoizedDocument.forme_juridique && (
                    <p className="text-gray-600 mt-2">Forme juridique : {memoizedDocument.forme_juridique}</p>
                  )}
                  {memoizedDocument.code_ape && (
                    <p className="text-gray-600">Code APE : {memoizedDocument.code_ape}</p>
                  )}
                  {memoizedDocument.client_siret && (
                    <p className="text-gray-600 mt-2">SIRET : {memoizedDocument.client_siret}</p>
                  )}
                  {memoizedDocument.client_tva && (
                    <p className="text-gray-600">TVA : {memoizedDocument.client_tva}</p>
                  )}
                </div>
                
                {memoizedDocument.description && (
                  <div className="max-w-xs w-full sm:text-right mt-4 sm:mt-0">
                    <h3 className="text-gray-600 mb-2 font-medium">Description :</h3>
                    <p className="text-gray-800">{memoizedDocument.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Tableau des articles */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse mb-8">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-3 border-b border-gray-200 text-left text-sm font-semibold text-gray-700 min-w-[150px]">Description</th>
                    <th className="px-4 py-3 border-b border-gray-200 text-right text-sm font-semibold text-gray-700 min-w-[70px]">Qté</th>
                    <th className="px-4 py-3 border-b border-gray-200 text-right text-sm font-semibold text-gray-700 min-w-[100px]">Prix unitaire</th>
                    <th className="px-4 py-3 border-b border-gray-200 text-right text-sm font-semibold text-gray-700 min-w-[70px]">TVA</th>
                    <th className="px-4 py-3 border-b border-gray-200 text-right text-sm font-semibold text-gray-700 min-w-[100px]">Total HT</th>
                  </tr>
                </thead>
                <tbody>
                {memoizedDocument.invoice_items && memoizedDocument.invoice_items.map((item, index) => (
                  <tr key={item.id || index} className="border-b border-gray-200">
                    <td className="px-4 py-3 text-gray-800 whitespace-pre-line">{item.description}</td>
                    <td className="px-4 py-3 text-right text-gray-800">
                      {item.quantite} {item.unite}
                    </td>
                    <td className="px-4 py-3 text-right text-gray-800">
                      {item.prix_unitaire.toFixed(2)} €
                    </td>
                    <td className="px-4 py-3 text-right text-gray-800">
                      {item.taux_tva}%
                    </td>
                    <td className="px-4 py-3 text-right text-gray-800">
                      {item.montant_ht.toFixed(2)} €
                    </td>
                  </tr>
                ))}
              </tbody>
              </table>
            </div>

            {/* Totaux */}
            <div className="flex justify-end mb-8 print-totals">
              <div className="w-full sm:w-64 border border-gray-200 rounded-lg overflow-hidden print-total-box">
                <div className="flex justify-between py-2.5 px-4 bg-gray-50 border-b border-gray-200">
                  <span className="text-gray-600 font-medium">Total HT :</span>
                  <span className="font-medium">{memoizedDocument.total_ht !== undefined ? memoizedDocument.total_ht.toFixed(2) : '0.00'} €</span>
                </div>
                <div className="flex justify-between py-2.5 px-4 border-b border-gray-200">
                  <span className="text-gray-600 font-medium">Total TVA :</span>
                  <span className="font-medium">{memoizedDocument.total_tva !== undefined ? memoizedDocument.total_tva.toFixed(2) : '0.00'} €</span>
                </div>
                <div className="flex justify-between py-3 px-4 bg-[#FFF8F3]">
                  <span className="font-bold text-gray-800">Total TTC :</span>
                  <span className="font-bold text-[#FF7A35]">{memoizedDocument.total_ttc !== undefined ? memoizedDocument.total_ttc.toFixed(2) : '0.00'} €</span>
                </div>
              </div>
            </div>

            {/* Bandeau de paiement pour les factures payées */}
            {memoizedDocument.type === 'facture' && ['paye', 'partiellement_paye'].includes(memoizedDocument.statut) && (
              <div className="mb-6 bg-green-50 border-l-4 border-green-500 p-4 sm:p-5 rounded-lg payment-status-banner">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500 mr-3 payment-status-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  
                  <div>
                    <h3 className="font-semibold text-green-800 mb-1">
                      {memoizedDocument.statut === 'paye' ? 'Facture payée' : 'Facture partiellement payée'}
                    </h3>
                    
                    <p className="text-green-700">
                      {memoizedDocument.date_paiement ? (
                        `Paiement effectué le ${new Date(memoizedDocument.date_paiement).toLocaleDateString('fr-FR')} à ${new Date(memoizedDocument.date_paiement).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`
                      ) : (
                        `Paiement enregistré le ${new Date().toLocaleDateString('fr-FR')}`
                      )}
                    </p>
                    
                    {memoizedDocument.statut === 'partiellement_paye' && (
                      <p className="text-green-700 mt-1 font-medium">
                        Un règlement complémentaire est attendu pour cette facture.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Informations de paiement */}
            {(memoizedDocument.mode_paiement || memoizedDocument.conditions_paiement) && (
              <div className="mb-6 bg-gray-50 p-4 sm:p-5 rounded-lg border border-gray-100 payment-info-section">
                <h3 className="font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200 payment-info-title">Informations de paiement</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pl-4 payment-info-grid">
                  {memoizedDocument.mode_paiement && (
                    <div className="payment-info-item">
                      <p className="text-gray-600 font-medium mb-1 payment-info-label">Mode de paiement :</p>
                      <p className="text-gray-800 pl-2 payment-info-value">
                        {memoizedDocument.mode_paiement === 'virement' && 'Virement bancaire'}
                        {memoizedDocument.mode_paiement === 'carte' && 'Carte bancaire'}
                        {memoizedDocument.mode_paiement === 'cheque' && 'Chèque'}
                        {memoizedDocument.mode_paiement === 'jobi' && 'Jobi'}
                        {memoizedDocument.mode_paiement === 'especes' && 'Espèces'}
                      </p>
                    </div>
                  )}
                  {memoizedDocument.conditions_paiement && (
                    <div className="payment-info-item">
                      <p className="text-gray-600 font-medium mb-1 payment-info-label">Conditions de paiement :</p>
                      <p className="text-gray-800 pl-2 payment-info-value">{memoizedDocument.conditions_paiement}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Mentions légales */}
            {(memoizedDocument.mentions_legales || memoizedDocument.mentions_tva || memoizedDocument.penalite_retard || memoizedDocument.indemnite_recouvrement) && (
              <div className="border-t border-gray-200 pt-5 text-sm text-gray-600 mt-5">
                <h3 className="font-semibold text-gray-800 mb-3">Mentions légales</h3>
                {memoizedDocument.mentions_legales && <p className="mb-2">{memoizedDocument.mentions_legales}</p>}
                {memoizedDocument.mentions_tva && <p className="mb-2">{memoizedDocument.mentions_tva}</p>}
                {memoizedDocument.penalite_retard && <p className="mb-2">Pénalités de retard : {memoizedDocument.penalite_retard}</p>}
                {memoizedDocument.indemnite_recouvrement && <p className="mb-2">Indemnité forfaitaire de recouvrement : {memoizedDocument.indemnite_recouvrement}</p>}
              </div>
            )}

            {/* Notes */}
            {memoizedDocument.notes && (
              <div className="border-t border-gray-200 pt-5 mt-5">
                <h3 className="font-semibold text-gray-800 mb-3">Notes</h3>
                <p className="text-gray-800 whitespace-pre-line">{memoizedDocument.notes}</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap justify-end gap-3 p-4 border-t bg-white print:hidden">
          <button
            onClick={handlePrint}
            className="px-3 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] transition-colors duration-200 inline-flex items-center print-hidden text-sm w-full sm:w-auto"
          >
            <Printer size={16} className="mr-2" />
            Imprimer
          </button>
          <button
            onClick={() => onDownload(memoizedDocument)}
            className="px-3 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200 inline-flex items-center print-hidden text-sm w-full sm:w-auto"
          >
            <Download size={16} className="mr-2" />
            Télécharger PDF
          </button>
          {isReceivedQuote && memoizedDocument.statut !== 'accepte' && memoizedDocument.statut !== 'facture' && (
            <>
              <button
                onClick={() => {
                  onClose();
                  window.location.href = `/quote-acceptance/${memoizedDocument.id}`;
                }}
                className="px-3 py-2 border border-gray-300 bg-white text-green-600 rounded-md hover:bg-green-50 transition-colors duration-200 inline-flex items-center print-hidden text-sm w-full sm:w-auto"
              >
                <FileText size={16} className="mr-2" />
                Accepter ce devis
              </button>
              <button
                onClick={() => {
                  onClose();
                  window.location.href = `/quote-refusal/${memoizedDocument.id}`;
                }}
                className="px-3 py-2 border border-gray-300 bg-white text-red-600 rounded-md hover:bg-red-50 transition-colors duration-200 inline-flex items-center print-hidden text-sm w-full sm:w-auto"
              >
                <X size={16} className="mr-2" />
                Refuser ce devis
              </button>
            </>
          )}
        </div>
      </div>
    </ModalPortal>
  );
};

export default DocumentPreviewModal; 