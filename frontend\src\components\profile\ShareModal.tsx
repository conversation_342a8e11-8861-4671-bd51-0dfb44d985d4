import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Share2, Mail, Copy, Send, Smartphone, MessageSquare, Link } from 'lucide-react';
import { <PERSON>ton, TextField, Box, Typography, Divider } from '@mui/material';
import ModalPortal from '../ModalPortal';
import { notify } from '../Notification';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import logger from '../../utils/logger';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  profil: {
    firstName: string;
    lastName: string;
    slug: string;
    bio?: string;
  } | null;
  onNativeShare: () => void;
}

const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  profil,
  onNativeShare
}) => {
  const [email, setEmail] = useState('');
  const [personalMessage, setPersonalMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  if (!profil) return null;

  const profileUrl = `${window.location.origin}/profil/${profil.slug}`;

  // Message pré-rédigé non-modifiable
  const predefinedMessage = `Bonjour,

Je souhaite vous recommander le profil de ${profil.firstName} ${profil.lastName} sur JobPartiel.fr.

JobPartiel.fr est une plateforme de confiance qui met en relation des particuliers avec des professionnels vérifiés pour des services de jardinage, bricolage et garde d'animaux.

Vous pouvez consulter le profil complet ici : ${profileUrl}

${profil.bio ? `À propos de ${profil.firstName} :\n${profil.bio.replace(/<[^>]*>/g, '').substring(0, 200)}${profil.bio.length > 200 ? '...' : ''}\n\n` : ''}`;

  const handleEmailShare = async () => {
    if (!email.trim()) {
      notify('Veuillez saisir une adresse email', 'error');
      return;
    }

    // Validation email simple
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      notify('Veuillez saisir une adresse email valide', 'error');
      return;
    }

    setIsLoading(true);

    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      const response = await fetch(`${API_CONFIG.baseURL}/api/email/share-profile`, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          recipientEmail: email,
          profileData: {
            firstName: profil.firstName,
            lastName: profil.lastName,
            slug: profil.slug,
            bio: profil.bio
          },
          personalMessage: personalMessage.trim()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de l\'envoi de l\'email');
      }

      notify('Email envoyé avec succès !', 'success');
      setEmail('');
      setPersonalMessage('');
      onClose();
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de l\'email de partage:', error);
      notify(error instanceof Error ? error.message : 'Erreur lors de l\'envoi de l\'email', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      notify('Lien copié dans le presse-papier', 'success');
    } catch (error) {
      notify('Erreur lors de la copie du lien', 'error');
    }
  };

  const handleNativeShareClick = () => {
    onNativeShare();
    onClose();
  };

  const handleWhatsAppShare = () => {
    const text = `Découvrez le profil de ${profil.firstName} ${profil.lastName} sur JobPartiel.fr : ${profileUrl}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(whatsappUrl, '_blank');
    notify('Partage WhatsApp ouvert', 'success');
  };

  const handleSMSShare = () => {
    const text = `Découvrez le profil de ${profil.firstName} ${profil.lastName} sur JobPartiel.fr : ${profileUrl}`;
    const smsUrl = `sms:?body=${encodeURIComponent(text)}`;
    window.open(smsUrl, '_blank');
    notify('Application SMS ouverte', 'success');
  };

  const handleFacebookShare = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(profileUrl)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
    notify('Partage Facebook ouvert', 'success');
  };

  const handleTwitterShare = () => {
    const text = `Découvrez le profil de ${profil.firstName} ${profil.lastName} sur JobPartiel.fr`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(profileUrl)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
    notify('Partage Twitter ouvert', 'success');
  };

  const handleLinkedInShare = () => {
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(profileUrl)}`;
    window.open(linkedinUrl, '_blank', 'width=600,height=400');
    notify('Partage LinkedIn ouvert', 'success');
  };

  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={onClose}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ duration: 0.2 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white rounded-t-2xl border-b border-gray-100 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#FFF8F3] rounded-lg">
                <Share2 className="h-5 w-5 text-[#FF6B2C]" />
              </div>
              <Typography variant="h6" className="font-bold text-gray-800">
                Partager le profil
              </Typography>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Profil info */}
          <div className="text-center">
            <Typography variant="h6" className="font-semibold text-gray-800">
              {profil.firstName} {profil.lastName}
            </Typography>
            <Typography variant="body2" className="text-gray-500 mt-1">
              jobpartiel.fr
            </Typography>
          </div>

          {/* Options de partage */}
          <div className="space-y-4">
            {/* Partage rapide */}
            <div>
              <Typography variant="subtitle2" className="font-medium text-gray-700 mb-3">
                Partage rapide
              </Typography>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outlined"
                  startIcon={<Share2 className="h-4 w-4" />}
                  onClick={handleNativeShareClick}
                  className="flex-1"
                  sx={{
                    borderColor: '#FF6B2C',
                    color: '#FF6B2C',
                    '&:hover': {
                      borderColor: '#FF6B2C',
                      backgroundColor: '#FFF8F3'
                    }
                  }}
                >
                  Partager
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Copy className="h-4 w-4" />}
                  onClick={handleCopyLink}
                  className="flex-1"
                  sx={{
                    borderColor: '#FF6B2C',
                    color: '#FF6B2C',
                    '&:hover': {
                      borderColor: '#FF6B2C',
                      backgroundColor: '#FFF8F3'
                    }
                  }}
                >
                  Copier
                </Button>
              </div>
            </div>

            {/* Réseaux sociaux et applications */}
            <div>
              <Typography variant="subtitle2" className="font-medium text-gray-700 mb-3">
                Réseaux sociaux et applications
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outlined"
                  startIcon={<MessageSquare className="h-4 w-4" />}
                  onClick={handleWhatsAppShare}
                  size="small"
                  sx={{
                    borderColor: '#25D366',
                    color: '#25D366',
                    '&:hover': {
                      borderColor: '#25D366',
                      backgroundColor: '#f0f9ff'
                    }
                  }}
                >
                  WhatsApp
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Smartphone className="h-4 w-4" />}
                  onClick={handleSMSShare}
                  size="small"
                  sx={{
                    borderColor: '#6B7280',
                    color: '#6B7280',
                    '&:hover': {
                      borderColor: '#6B7280',
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  SMS
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  }
                  onClick={handleFacebookShare}
                  size="small"
                  sx={{
                    borderColor: '#1877F2',
                    color: '#1877F2',
                    '&:hover': {
                      borderColor: '#1877F2',
                      backgroundColor: '#f0f8ff'
                    }
                  }}
                >
                  Facebook
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                  }
                  onClick={handleTwitterShare}
                  size="small"
                  sx={{
                    borderColor: '#1DA1F2',
                    color: '#1DA1F2',
                    '&:hover': {
                      borderColor: '#1DA1F2',
                      backgroundColor: '#f0f8ff'
                    }
                  }}
                >
                  Twitter
                </Button>
                <Button
                  variant="outlined"
                  startIcon={
                    <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  }
                  onClick={handleLinkedInShare}
                  size="small"
                  sx={{
                    borderColor: '#0A66C2',
                    color: '#0A66C2',
                    '&:hover': {
                      borderColor: '#0A66C2',
                      backgroundColor: '#f0f8ff'
                    }
                  }}
                >
                  LinkedIn
                </Button>
              </div>
            </div>

            <Divider />

            {/* Partage par email */}
            <div>
              <Typography variant="subtitle2" className="font-medium text-gray-700 mb-3 flex items-center">
                <Mail className="h-4 w-4 mr-2 text-[#FF6B2C]" />
                Partager par email
              </Typography>

              <div className="space-y-4">
                <TextField
                  fullWidth
                  label="Adresse email du destinataire"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  variant="outlined"
                  size="small"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF6B2C',
                    },
                  }}
                />

                <TextField
                  fullWidth
                  label="Message personnel (optionnel)"
                  multiline
                  rows={3}
                  value={personalMessage}
                  onChange={(e) => setPersonalMessage(e.target.value)}
                  variant="outlined"
                  size="small"
                  placeholder="Ajoutez votre message personnel..."
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF6B2C',
                    },
                  }}
                />

                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<Send className="h-4 w-4" />}
                  onClick={handleEmailShare}
                  disabled={isLoading}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: '#FF7A35',
                    },
                    '&:disabled': {
                      backgroundColor: '#FFE4BA',
                    },
                    py: 1.5,
                    fontWeight: 600
                  }}
                >
                  {isLoading ? 'Envoi en cours...' : 'Envoyer l\'email'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </ModalPortal>
  );
};

export default ShareModal;
