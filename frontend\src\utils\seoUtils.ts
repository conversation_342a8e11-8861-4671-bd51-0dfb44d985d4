// Utilitaires pour le SEO des pages dynamiques

export interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  ogLocale?: string;
  twitterCard?: string;
  twitterImage?: string;
  structuredData?: any;
}

// Fonction pour générer les métadonnées SEO pour la page principale des services
export const generateServicesPageSEO = (): SEOMetadata => {
  return {
    title: `JobPartiel : Jobbing & Services à Domicile | Jobbeurs Vérifiés & Système Jobi`,
    description: `Plateforme française de jobbing. Trouvez jobbeurs vérifiés pour jardinage, bricolage, ménage... Système Jobi unique, tarifs transparents, sans commission !`,
    keywords: [
      'jobbeur',
      'jobpartiel',
      'services à domicile',
      'plateforme jobbing france',
      'système jobi',
      'échange services',
      'jardinage à domicile',
      'bricolage professionnel',
      'garde animaux',
      'ménage à domicile',
      'jobbeurs vérifiés',
      'tarifs transparents',
      'sans commission',
      'économie collaborative',
      'troc services',
      'plateforme française',
      'professionnels qualifiés',
      'services de proximité',
      'jobbing france',
      'travaux à domicile',
      'aide à domicile',
      'services entre particuliers',
      'marketplace services',
      'petits boulots',
      'missions ponctuelles'
    ],
    canonical: `/services`,
    ogImage: generateOpenGraphImage('services', 'france'),
    ogType: 'website',
    ogLocale: 'fr_FR',
    twitterCard: 'summary_large_image',
    twitterImage: generateOpenGraphImage('services', 'france')
  };
};

// Fonction pour générer les métadonnées SEO pour les pages de résultats de recherche
export const generateSearchResultsSEO = (searchParams: URLSearchParams): SEOMetadata => {
  const query = searchParams.get('q') || '';
  const city = searchParams.get('city') || '';
  const category = searchParams.get('category') || '';
  const subcategory = searchParams.get('subcategory') || '';

  let title = '';
  let description = '';
  let keywords: string[] = [];

  if (subcategory && city) {
    const serviceFormatted = formatServiceName(subcategory);
    const cityFormatted = formatCityName(city);
    title = `${serviceFormatted} ${cityFormatted} - Jobbeurs Professionnels Vérifiés | JobPartiel`;
    description = `🎯 Trouvez votre jobbeur ${serviceFormatted} à ${cityFormatted} ! Profils vérifiés, tarifs transparents, avis clients authentiques. Système Jobi unique pour échanger vos services.`;
    keywords = [
      `${serviceFormatted.toLowerCase()} ${cityFormatted.toLowerCase()}`,
      `jobbeur ${serviceFormatted.toLowerCase()}`,
      `professionnel ${serviceFormatted.toLowerCase()}`,
      `${serviceFormatted.toLowerCase()} à domicile`,
      'jobpartiel',
      'jobi',
      'services vérifiés'
    ];
  } else if (category && city) {
    const categoryFormatted = formatServiceName(category);
    const cityFormatted = formatCityName(city);
    title = `${categoryFormatted} ${cityFormatted} - Tous les Jobbeurs Spécialisés | JobPartiel`;
    description = `🔍 Découvrez tous nos jobbeurs spécialisés en ${categoryFormatted} à ${cityFormatted}. Comparez les profils, tarifs et avis. Système Jobi révolutionnaire pour échanger vos services. Plateforme 100% française.`;
    keywords = [
      `${categoryFormatted.toLowerCase()} ${cityFormatted.toLowerCase()}`,
      `jobbeurs ${categoryFormatted.toLowerCase()}`,
      `services ${categoryFormatted.toLowerCase()}`,
      'jobpartiel',
      'professionnels vérifiés'
    ];
  } else if (query && city) {
    const queryFormatted = formatServiceName(query);
    const cityFormatted = formatCityName(city);
    title = `${queryFormatted} ${cityFormatted} - Résultats de Recherche | JobPartiel`;
    description = `🔎 Résultats pour "${queryFormatted}" à ${cityFormatted}. Jobbeurs professionnels vérifiés, tarifs compétitifs, système Jobi unique. Trouvez le professionnel parfait pour vos besoins !`;
    keywords = [
      `${queryFormatted.toLowerCase()} ${cityFormatted.toLowerCase()}`,
      'recherche jobbeur',
      'services à domicile',
      'jobpartiel'
    ];
  } else if (city) {
    const cityFormatted = formatCityName(city);
    title = `Services à Domicile ${cityFormatted} - Jobbeurs Locaux Vérifiés | JobPartiel`;
    description = `🏠 Tous les services à domicile à ${cityFormatted} ! Jardinage, bricolage, ménage... Jobbeurs locaux vérifiés, tarifs transparents, système Jobi innovant.`;
    keywords = [
      `services à domicile ${cityFormatted.toLowerCase()}`,
      `jobbeurs ${cityFormatted.toLowerCase()}`,
      `professionnels ${cityFormatted.toLowerCase()}`,
      'services locaux',
      'jobpartiel'
    ];
  } else {
    title = `Recherche de Services - Trouvez votre Jobbeur Idéal | JobPartiel`;
    description = `🎯 Recherchez parmi des milliers de jobbeurs vérifiés ! Jardinage, bricolage, ménage... Système Jobi unique, tarifs transparents, satisfaction garantie.`;
    keywords = [
      'recherche jobbeur',
      'services à domicile',
      'jobpartiel',
      'professionnels vérifiés',
      'système jobi'
    ];
  }

  return {
    title,
    description,
    keywords: [
      ...keywords,
      'jobbeur',
      'jobpartiel',
      'services à domicile',
      'professionnels vérifiés',
      'système jobi',
      'plateforme française',
      'tarifs transparents',
      'avis clients',
      'économie collaborative'
    ],
    canonical: `/services/search?${searchParams.toString()}`,
    ogImage: generateOpenGraphImage(query || category || subcategory || 'recherche', city || 'france'),
    ogType: 'website',
    ogLocale: 'fr_FR',
    twitterCard: 'summary_large_image',
    twitterImage: generateOpenGraphImage(query || category || subcategory || 'recherche', city || 'france')
  };
};

// Fonction pour générer les métadonnées SEO pour une page service/ville (optimisée)
export const generateServiceCitySEO = (service: string, ville: string): SEOMetadata => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  return {
    title: `${serviceFormatted} ${villeFormatted} 🏆 Jobbeurs Professionnels Vérifiés | JobPartiel`,
    description: `⭐ N°1 du ${serviceFormatted} à ${villeFormatted} ! Jobbeurs 100% vérifiés, tarifs transparents, avis clients authentiques. Système Jobi révolutionnaire de troc de services.`,
    keywords: [
      `${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `jobbeur ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `professionnel ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `${serviceFormatted.toLowerCase()} à domicile ${villeFormatted.toLowerCase()}`,
      `meilleur ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `tarif ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `prix ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      `devis ${serviceFormatted.toLowerCase()} ${villeFormatted.toLowerCase()}`,
      'jobbeur',
      'jobpartiel',
      'système jobi',
      'échange services',
      'plateforme française',
      'jobbeurs vérifiés',
      'service à domicile',
      'professionnel vérifié',
      'tarif transparent',
      'avis clients',
      'réservation sécurisée',
      'économie collaborative',
      'sans commission cachée',
      'satisfaction garantie',
      'professionnels qualifiés',
      'services de proximité',
      'intervention rapide',
      'devis gratuit',
      'paiement sécurisé'
    ],
    canonical: `/services/${service}/${ville}`,
    ogImage: generateOpenGraphImage(service, ville),
    ogType: 'website',
    ogLocale: 'fr_FR',
    twitterCard: 'summary_large_image',
    twitterImage: generateOpenGraphImage(service, ville)
  };
};

// Fonction pour formater le nom d'un service
export const formatServiceName = (serviceName: string): string => {
  return serviceName.charAt(0).toUpperCase() + serviceName.slice(1).replace(/-/g, ' ');
};

// Fonction pour formater le nom d'une ville
export const formatCityName = (cityName: string): string => {
  return cityName.charAt(0).toUpperCase() + cityName.slice(1).replace(/-/g, ' ');
};

// Fonction pour créer un slug à partir d'un texte
export const createSlug = (text: string): string => {
  return text.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[àáâãäå]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ç]/g, 'c')
    .replace(/[^a-z0-9-]/g, '');
};

// Fonction pour générer du contenu SEO adaptatif
export const generateSEOContent = (service: string, ville: string) => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  return {
    introText: `Vous recherchez un professionnel en ${serviceFormatted} à ${villeFormatted} ? JobPartiel vous met en relation avec les meilleurs jobbeurs de votre région. Consultez leurs profils, comparez les tarifs et réservez en quelques clics.`,

    howToFind: {
      title: `Comment trouver le bon jobbeur en ${serviceFormatted} ?`,
      content: `Pour trouver le meilleur jobbeur en ${serviceFormatted} à ${villeFormatted}, consultez les profils vérifiés, lisez les avis clients et comparez les tarifs. Tous nos jobbeurs sont évalués par la communauté JobPartiel.`
    },

    howToChoose: {
      title: `Comment bien choisir votre professionnel ?`,
      content: `Vérifiez les certifications, l'expérience et les spécialisations du jobbeur. Consultez sa galerie photo pour voir ses réalisations précédentes. N'hésitez pas à demander un devis gratuit.`
    },

    howItWorks: {
      title: `Comment fonctionne JobPartiel ?`,
      content: `JobPartiel utilise un système d'échange innovant avec les Jobis, notre monnaie virtuelle. Vous pouvez payer en euros ou échanger des services contre des Jobis. Créez votre mission, recevez des propositions et choisissez votre jobbeur.`
    },

    recentRequestsTitle: `Dernières demandes pour ${serviceFormatted} à ${villeFormatted}`,

    nearbyTitle: `${serviceFormatted} dans les villes proches`,

    similarTitle: `Services similaires à ${villeFormatted}`,

    ctaTitle: `Besoin d'un service en ${serviceFormatted} ?`,
    ctaSubtitle: `Postez votre mission gratuitement et recevez des propositions de jobbeurs qualifiés`
  };
};

// Fonction pour mettre à jour les métadonnées de la page
export const updatePageMetadata = (metadata: SEOMetadata) => {
  // Mettre à jour le titre
  document.title = metadata.title;

  // Mettre à jour la description
  let descriptionMeta = document.querySelector('meta[name="description"]');
  if (!descriptionMeta) {
    descriptionMeta = document.createElement('meta');
    descriptionMeta.setAttribute('name', 'description');
    document.head.appendChild(descriptionMeta);
  }
  descriptionMeta.setAttribute('content', metadata.description);

  // Mettre à jour les keywords
  let keywordsMeta = document.querySelector('meta[name="keywords"]');
  if (!keywordsMeta) {
    keywordsMeta = document.createElement('meta');
    keywordsMeta.setAttribute('name', 'keywords');
    document.head.appendChild(keywordsMeta);
  }
  keywordsMeta.setAttribute('content', metadata.keywords.join(', '));

  // Mettre à jour l'URL canonique
  if (metadata.canonical) {
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', `${window.location.origin}${metadata.canonical}`);
  }

  // Mettre à jour les métadonnées Open Graph
  updateOpenGraphMetadata(metadata);
};

// Fonction pour mettre à jour les métadonnées Open Graph
const updateOpenGraphMetadata = (metadata: SEOMetadata) => {
  const ogTags = [
    { property: 'og:title', content: metadata.title },
    { property: 'og:description', content: metadata.description },
    { property: 'og:type', content: metadata.ogType || 'website' },
    { property: 'og:url', content: window.location.href },
    { property: 'og:site_name', content: 'JobPartiel' },
    { property: 'og:locale', content: metadata.ogLocale || 'fr_FR' },
    { property: 'og:image', content: metadata.ogImage || `${window.location.origin}/logo-jobpartiel.png` },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: metadata.title }
  ];

  ogTags.forEach(tag => {
    let ogMeta = document.querySelector(`meta[property="${tag.property}"]`);
    if (!ogMeta) {
      ogMeta = document.createElement('meta');
      ogMeta.setAttribute('property', tag.property);
      document.head.appendChild(ogMeta);
    }
    ogMeta.setAttribute('content', tag.content);
  });

  // Twitter Card
  const twitterTags = [
    { name: 'twitter:card', content: metadata.twitterCard || 'summary_large_image' },
    { name: 'twitter:title', content: metadata.title },
    { name: 'twitter:description', content: metadata.description },
    { name: 'twitter:image', content: metadata.twitterImage || metadata.ogImage || `${window.location.origin}/logo-jobpartiel.png` },
    { name: 'twitter:image:alt', content: metadata.title },
    { name: 'twitter:site', content: '@jobpartiel' },
    { name: 'twitter:creator', content: '@jobpartiel' }
  ];

  twitterTags.forEach(tag => {
    let twitterMeta = document.querySelector(`meta[name="${tag.name}"]`);
    if (!twitterMeta) {
      twitterMeta = document.createElement('meta');
      twitterMeta.setAttribute('name', tag.name);
      document.head.appendChild(twitterMeta);
    }
    twitterMeta.setAttribute('content', tag.content);
  });

  // Meta robots et autres balises SEO importantes
  const additionalTags = [
    { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' },
    { name: 'author', content: 'JobPartiel' },
    { name: 'theme-color', content: '#FF6B2C' },
    { name: 'application-name', content: 'JobPartiel' },
    { name: 'apple-mobile-web-app-title', content: 'JobPartiel' },
    { name: 'msapplication-TileColor', content: '#FF6B2C' }
  ];

  additionalTags.forEach(tag => {
    let meta = document.querySelector(`meta[name="${tag.name}"]`);
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('name', tag.name);
      document.head.appendChild(meta);
    }
    meta.setAttribute('content', tag.content);
  });
};

// Fonction pour générer un JSON-LD structuré pour les services locaux
export const generateLocalBusinessSchema = (service: string, ville: string, providers: any[]) => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  const schema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": `${serviceFormatted} à ${villeFormatted} - JobPartiel`,
    "description": `Service de mise en relation avec des professionnels en ${serviceFormatted} à ${villeFormatted}`,
    "url": `${window.location.origin}/services/${service}/${ville}`,
    "areaServed": {
      "@type": "City",
      "name": villeFormatted,
      "addressCountry": "FR"
    },
    "serviceType": serviceFormatted,
    "provider": {
      "@type": "Organization",
      "name": "JobPartiel",
      "url": window.location.origin,
      "description": "Première plateforme française de jobbing avec système d'échange Jobi unique",
      "foundingLocation": {
        "@type": "Place",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Pia",
          "addressCountry": "FR"
        }
      },
      "serviceArea": {
        "@type": "Country",
        "name": "France"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Services JobPartiel",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Jardinage",
              "description": "Services de jardinage par des professionnels vérifiés"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Bricolage",
              "description": "Services de bricolage par des professionnels vérifiés"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Garde d'animaux",
              "description": "Services de garde d'animaux par des professionnels vérifiés"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Ménage",
              "description": "Services de ménage par des professionnels vérifiés"
            }
          }
        ]
      },
      "paymentAccepted": ["Cash", "Credit Card", "Jobi"],
      "priceRange": "€€",
      "currenciesAccepted": ["EUR", "Jobi"]
    },
    "aggregateRating": providers.length > 0 ? {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": providers.length,
      "bestRating": "5",
      "worstRating": "1"
    } : undefined,
    "offers": {
      "@type": "Offer",
      "description": `Services de ${serviceFormatted} à ${villeFormatted}`,
      "priceCurrency": "EUR",
      "availability": "https://schema.org/InStock",
      "validFrom": new Date().toISOString(),
      "seller": {
        "@type": "Organization",
        "name": "JobPartiel"
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": `${window.location.origin}/services/search?q=${serviceFormatted}&city=${villeFormatted}`,
      "query-input": "required name=search_term_string"
    }
  };

  return JSON.stringify(schema);
};

// Fonction pour injecter les données structurées JSON-LD dans la page
export const injectStructuredData = (structuredData: string | object) => {
  // Supprimer les anciens scripts JSON-LD de JobPartiel
  const existingScripts = document.querySelectorAll('script[type="application/ld+json"][data-jobpartiel]');
  existingScripts.forEach(script => script.remove());

  // Créer et injecter le nouveau script
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.setAttribute('data-jobpartiel', 'true');
  script.textContent = typeof structuredData === 'string' ? structuredData : JSON.stringify(structuredData);
  document.head.appendChild(script);
};

// Fonction pour générer un schéma FAQ optimisé pour le SEO
export const generateFAQSchema = (service: string, ville: string) => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": `Comment trouver un bon jobbeur en ${serviceFormatted} à ${villeFormatted} ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `Pour trouver le meilleur jobbeur en ${serviceFormatted} à ${villeFormatted}, utilisez JobPartiel ! Consultez les profils vérifiés, lisez les avis clients authentiques, comparez les tarifs transparents. Tous nos jobbeurs sont évalués par la communauté et 100% vérifiés.`
        }
      },
      {
        "@type": "Question",
        "name": `Quels sont les tarifs pour un service de ${serviceFormatted} à ${villeFormatted} ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `Les tarifs pour ${serviceFormatted} à ${villeFormatted} varient selon l'expérience du jobbeur et la complexité de la mission. Sur JobPartiel, tous les prix sont transparents et affichés clairement. Vous pouvez aussi utiliser notre système Jobi pour échanger des services !`
        }
      },
      {
        "@type": "Question",
        "name": `Qu'est-ce que le système Jobi de JobPartiel ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Le système Jobi est notre innovation révolutionnaire ! C'est une monnaie virtuelle qui vous permet d'échanger des services sans utiliser d'euros. Proposez vos compétences, gagnez des Jobis, et utilisez-les pour obtenir les services dont vous avez besoin. C'est l'économie collaborative réinventée !"
        }
      },
      {
        "@type": "Question",
        "name": `Comment sont vérifiés les jobbeurs sur JobPartiel ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Tous nos jobbeurs passent par un processus de vérification rigoureux : vérification d'identité, contrôle des compétences, validation des assurances si nécessaire. De plus, notre système d'avis clients permet une évaluation continue de la qualité des services."
        }
      },
      {
        "@type": "Question",
        "name": `JobPartiel prend-il des commissions sur les services ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "JobPartiel fonctionne avec un système d'abonnement transparent, sans commission cachée sur les transactions ! Contrairement aux autres plateformes, nous ne prélevons pas de pourcentage sur vos paiements. C'est plus équitable pour tous."
        }
      }
    ]
  };

  return JSON.stringify(faqSchema);
};

// Fonction pour générer un schéma BreadcrumbList
export const generateBreadcrumbSchema = (service: string, ville: string) => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);
  const baseUrl = window.location.origin;

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Accueil",
        "item": baseUrl
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Services",
        "item": `${baseUrl}/services`
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": serviceFormatted,
        "item": `${baseUrl}/services?category=${serviceFormatted}`
      },
      {
        "@type": "ListItem",
        "position": 4,
        "name": `${serviceFormatted} ${villeFormatted}`,
        "item": `${baseUrl}/services/${service}/${ville}`
      }
    ]
  };

  return JSON.stringify(breadcrumbSchema);
};

// Fonction pour générer automatiquement une URL d'image Open Graph optimisée
export const generateOpenGraphImage = (service: string, ville: string): string => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  // Utiliser un service de génération d'images dynamiques optimisé pour le SEO
  const baseUrl = window.location.origin;
  const params = new URLSearchParams({
    service: serviceFormatted,
    ville: villeFormatted,
    title: `${serviceFormatted} ${villeFormatted}`,
    subtitle: '🏆 JobPartiel - N°1 du Jobbing en France',
    description: 'Jobbeurs vérifiés • Système Jobi • Tarifs transparents',
    logo: 'jobpartiel',
    theme: 'orange-gradient',
    badge: 'verified',
    rating: '4.9/5',
    users: '50k+',
    format: 'og' // Format optimisé pour Open Graph (1200x630)
  });

  return `${baseUrl}/api/og-image?${params.toString()}`;
};

// Fonction pour générer une image Twitter Card optimisée
export const generateTwitterCardImage = (service: string, ville: string): string => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);

  const baseUrl = window.location.origin;
  const params = new URLSearchParams({
    service: serviceFormatted,
    ville: villeFormatted,
    title: `${serviceFormatted} ${villeFormatted}`,
    subtitle: 'JobPartiel - Plateforme Française',
    logo: 'jobpartiel',
    theme: 'orange-compact',
    format: 'twitter' // Format optimisé pour Twitter (1200x600)
  });

  return `${baseUrl}/api/twitter-image?${params.toString()}`;
};

// Fonction pour générer des schémas JSON-LD plus détaillés
export const generateDetailedSchemas = (service: string, ville: string, providers: any[]) => {
  const serviceFormatted = formatServiceName(service);
  const villeFormatted = formatCityName(ville);
  const baseUrl = window.location.origin;

  // 1. Schema Organization (JobPartiel)
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": `${baseUrl}/#organization`,
    "name": "JobPartiel",
    "alternateName": "JobPartiel.fr",
    "description": "Première plateforme française de jobbing avec système d'échange Jobi révolutionnaire. Jardinage, bricolage, garde d'animaux, ménage ... par des professionnels vérifiés.",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/logo-jobpartiel.png`,
      "width": 512,
      "height": 512
    },
    "image": {
      "@type": "ImageObject",
      "url": `${baseUrl}/og-image-default.png`,
      "width": 1200,
      "height": 630
    },
    "foundingDate": "2024",
    "foundingLocation": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Pia",
        "addressRegion": "Occitanie",
        "addressCountry": "FR"
      }
    },
    "areaServed": {
      "@type": "Country",
      "name": "France"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "French",
      "areaServed": "FR"
    },
    "sameAs": [
      "https://www.facebook.com/jobpartiel",
      "https://www.linkedin.com/company/jobpartiel",
      "https://twitter.com/jobpartiel"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Services JobPartiel",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Jardinage",
            "description": "Services de jardinage par des professionnels vérifiés : tonte, taille, plantation, entretien espaces verts",
            "category": "Jardinage",
            "areaServed": "France"
          },
          "priceCurrency": ["EUR", "Jobi"],
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Bricolage",
            "description": "Services de bricolage par des professionnels vérifiés : montage, réparation, installation, petits travaux",
            "category": "Bricolage",
            "areaServed": "France"
          },
          "priceCurrency": ["EUR", "Jobi"],
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Garde d'animaux",
            "description": "Services de garde d'animaux par des professionnels vérifiés : promenade, garde à domicile, soins",
            "category": "Garde d'animaux",
            "areaServed": "France"
          },
          "priceCurrency": ["EUR", "Jobi"],
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Ménage",
            "description": "Services de ménage par des professionnels vérifiés : nettoyage, repassage, entretien domicile",
            "category": "Ménage",
            "areaServed": "France"
          },
          "priceCurrency": ["EUR", "Jobi"],
          "availability": "https://schema.org/InStock"
        }
      ]
    },
    "paymentAccepted": ["Cash", "Credit Card", "Jobi"],
    "currenciesAccepted": ["EUR", "Jobi"]
  };

  // 2. Schema WebSite avec SearchAction
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${baseUrl}/#website`,
    "name": "JobPartiel",
    "description": "Plateforme française de jobbing avec système d'échange Jobi",
    "url": baseUrl,
    "publisher": {
      "@id": `${baseUrl}/#organization`
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/services/search?q={search_term_string}&city={city_string}`
      },
      "query-input": [
        {
          "@type": "PropertyValueSpecification",
          "valueRequired": true,
          "valueName": "search_term_string"
        },
        {
          "@type": "PropertyValueSpecification",
          "valueRequired": false,
          "valueName": "city_string"
        }
      ]
    }
  };

  // 3. Schema Service spécifique
  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "@id": `${baseUrl}/services/${service}/${ville}/#service`,
    "name": `${serviceFormatted} à ${villeFormatted}`,
    "description": `Service de ${serviceFormatted} à ${villeFormatted} par des professionnels vérifiés sur JobPartiel. Système d'échange Jobi unique, tarifs transparents, jobbeurs 100% vérifiés.`,
    "provider": {
      "@id": `${baseUrl}/#organization`
    },
    "areaServed": {
      "@type": "City",
      "name": villeFormatted,
      "addressCountry": "FR"
    },
    "serviceType": serviceFormatted,
    "category": serviceFormatted,
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": `${serviceFormatted} à ${villeFormatted}`,
      "numberOfItems": providers.length,
      "itemListElement": providers.slice(0, 5).map((provider, index) => ({
        "@type": "Offer",
        "position": index + 1,
        "itemOffered": {
          "@type": "Service",
          "name": `${serviceFormatted} par ${provider.prenom} ${provider.nom?.charAt(0)}.`,
          "description": provider.bio || `Service de ${serviceFormatted} professionnel`,
          "provider": {
            "@type": "Person",
            "name": `${provider.prenom} ${provider.nom?.charAt(0)}.`,
            "address": {
              "@type": "PostalAddress",
              "addressLocality": provider.ville,
              "postalCode": provider.code_postal,
              "addressCountry": "FR"
            }
          }
        },
        "priceCurrency": "EUR",
        "price": provider.user_services?.[0]?.tarif_horaire || 25,
        "priceSpecification": {
          "@type": "PriceSpecification",
          "price": provider.user_services?.[0]?.tarif_horaire || 25,
          "priceCurrency": "EUR",
          "unitText": "HOUR"
        },
        "availability": "https://schema.org/InStock",
        "validFrom": new Date().toISOString()
      }))
    },
    "aggregateRating": providers.length > 0 ? {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": providers.length,
      "bestRating": "5",
      "worstRating": "1"
    } : undefined
  };

  // 4. Schema FAQ
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "@id": `${baseUrl}/services/${service}/${ville}/#faq`,
    "mainEntity": [
      {
        "@type": "Question",
        "name": `Combien coûte un service de ${serviceFormatted} à ${villeFormatted} ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `Les tarifs pour ${serviceFormatted} à ${villeFormatted} varient selon la complexité et la durée. Sur JobPartiel, vous pouvez comparer les prix transparents de nos jobbeurs vérifiés. Avec notre système Jobi unique, vous pouvez aussi échanger des services au lieu de payer en euros, ce qui rend nos prestations encore plus accessibles.`
        }
      },
      {
        "@type": "Question",
        "name": `Comment trouver un jobbeur fiable en ${serviceFormatted} à ${villeFormatted} ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": `Sur JobPartiel, tous nos jobbeurs sont 100% vérifiés avec le badge "vérifié". Consultez leurs profils détaillés, avis clients authentiques, galeries photos de réalisations et certifications. Notre système de notation communautaire garantit la qualité.`
        }
      },
      {
        "@type": "Question",
        "name": "Qu'est-ce qui différencie JobPartiel des autres plateformes ?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "JobPartiel révolutionne le jobbing avec son système d'échange Jobi unique en France. Plateforme 100% française basée dans le sud de la France, nous proposons des abonnements transparents sans commissions cachées. Vous pouvez payer en euros ou échanger des services avec les Jobi."
        }
      },
      {
        "@type": "Question",
        "name": `Comment fonctionne le système Jobi pour ${serviceFormatted} ?`,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Les Jobi sont notre système d'échange / troc révolutionnaire. Gagnez des Jobi en publiant des missions, en laissant des avis, ou par parrainage. Utilisez-les pour échanger des services ou contre d'autres prestations. C'est l'économie du troc moderne !"
        }
      }
    ]
  };

  return {
    organization: JSON.stringify(organizationSchema),
    website: JSON.stringify(websiteSchema),
    service: JSON.stringify(serviceSchema),
    faq: JSON.stringify(faqSchema)
  };
};


