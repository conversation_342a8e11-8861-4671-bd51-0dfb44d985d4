import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  InputAdornment, 
  IconButton, 
  Popover, 
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Typography,
  Chip,
  Button,
  Tooltip,
  Badge,
  SelectChangeEvent
} from '@mui/material';
import { Search, Filter, X } from 'lucide-react';
import { format, startOfMonth, endOfMonth, getYear, getMonth } from 'date-fns';

export interface MissionFilters {
  searchTerm: string;
  status: string[];
  jobber: string[];
  minPrice: number | null;
  maxPrice: number | null;
  dateRange: {
    start: string | null;
    end: string | null;
  };
  selectedMonth?: number;
  selectedYear?: number;
}

interface MissionFilterBarProps {
  onFilterChange: (filters: MissionFilters) => void;
  totalCount: number;
  filteredCount: number;
  jobbers: { id: string; name: string; count: number }[];
  maxPossiblePrice: number;
  initialMonth?: number;
  initialYear?: number;
}

const STATUSES = [
  { value: 'en_attente', label: 'En attente', color: '#FFC107' },
  { value: 'acceptée', label: 'Acceptée', color: '#4CAF50' },
  { value: 'refusée', label: 'Refusée', color: '#F44336' },
  { value: 'contre_offre', label: 'Contre-offre', color: '#9C27B0' },
  { value: 'contre_offre_jobbeur', label: 'Contre-offre jobbeur', color: '#2196F3' },
  { value: 'pending', label: 'Paiement en attente', color: '#FF9800' },
  { value: 'completed', label: 'Payée', color: '#8BC34A' },
  { value: 'manual', label: 'Paiement manuel', color: '#795548' },
  { value: 'no_proposition', label: 'Sans proposition', color: '#9E9E9E' },
];

const MissionFilterBar: React.FC<MissionFilterBarProps> = ({ 
  onFilterChange, 
  totalCount, 
  filteredCount, 
  jobbers, 
  maxPossiblePrice,
  initialMonth,
  initialYear
}) => {
  // États locaux
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [status, setStatus] = useState<string[]>([]);
  const [jobber, setJobber] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, maxPossiblePrice || 500]);
  
  // Remplacer les dates par mois/année
  const currentDate = new Date();
  const [selectedMonth, setSelectedMonth] = useState<number>(
    initialMonth !== undefined ? initialMonth : getMonth(currentDate)
  );
  const [selectedYear, setSelectedYear] = useState<number>(
    initialYear !== undefined ? initialYear : getYear(currentDate)
  );
  
  // useEffect pour mettre à jour les états quand les props changent
  useEffect(() => {
    if (initialMonth !== undefined && initialMonth !== selectedMonth) {
      setSelectedMonth(initialMonth);
    }
    if (initialYear !== undefined && initialYear !== selectedYear) {
      setSelectedYear(initialYear);
    }
  }, [initialMonth, initialYear]);
  
  // Calculer les dates de début et fin pour le mois sélectionné
  const getStartDate = (year: number, month: number) => format(startOfMonth(new Date(year, month)), 'yyyy-MM-dd');
  const getEndDate = (year: number, month: number) => format(endOfMonth(new Date(year, month)), 'yyyy-MM-dd');

  // Génerer les dates actuelles de début et fin de mois
  const selectedStartDate = getStartDate(selectedYear, selectedMonth);
  const selectedEndDate = getEndDate(selectedYear, selectedMonth);
  
  // Compteur de filtres actifs (hors recherche textuelle)
  const currentMonthAndYear = getMonth(currentDate) === selectedMonth && getYear(currentDate) === selectedYear;

  const activeFiltersCount = 
    status.length + 
    jobber.length + 
    (priceRange[0] > 0 ? 1 : 0) + 
    (priceRange[1] < maxPossiblePrice ? 1 : 0) +
    (currentMonthAndYear ? 0 : 1); // Le mois est compté comme filtre actif seulement s'il est différent du mois courant

  const open = Boolean(anchorEl);
  const id = open ? 'mission-filter-popover' : undefined;

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchTerm(value);
    applyFilters(value, status, jobber, priceRange, selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
  };

  const handleStatusChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    setStatus(value);
    applyFilters(searchTerm, value, jobber, priceRange, selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
  };

  const handleJobberChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    setJobber(value);
    applyFilters(searchTerm, status, value, priceRange, selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
  };

  const handlePriceChange = (event: Event, newValue: number | number[]) => {
    const newRange = newValue as [number, number];
    setPriceRange(newRange);
    applyFilters(searchTerm, status, jobber, newRange, selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
  };

  const handleMonthChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setSelectedMonth(value);
    
    // Calculer les dates de début et fin du mois
    const startDate = getStartDate(selectedYear, value);
    const endDate = getEndDate(selectedYear, value);
    
    applyFilters(searchTerm, status, jobber, priceRange, startDate, endDate, value, selectedYear);
  };

  const handleYearChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    setSelectedYear(value);
    
    // Recalculer les dates de début et fin avec la nouvelle année
    const startDate = getStartDate(value, selectedMonth);
    const endDate = getEndDate(value, selectedMonth);
    
    applyFilters(searchTerm, status, jobber, priceRange, startDate, endDate, selectedMonth, value);
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setStatus([]);
    setJobber([]);
    setPriceRange([0, maxPossiblePrice || 500]);
    
    // Réinitialiser le mois et l'année à la date actuelle
    const now = new Date();
    setSelectedMonth(getMonth(now));
    setSelectedYear(getYear(now));
    
    const startDate = getStartDate(getYear(now), getMonth(now));
    const endDate = getEndDate(getYear(now), getMonth(now));
    
    applyFilters('', [], [], [0, maxPossiblePrice || 500], startDate, endDate, getMonth(now), getYear(now));
  };

  const applyFilters = (
    search: string,
    selectedStatus: string[],
    selectedJobber: string[],
    selectedPriceRange: [number, number],
    selectedStartDate: string,
    selectedEndDate: string,
    month: number = selectedMonth,
    year: number = selectedYear
  ) => {
    onFilterChange({
      searchTerm: search,
      status: selectedStatus,
      jobber: selectedJobber,
      minPrice: selectedPriceRange[0] > 0 ? selectedPriceRange[0] : null,
      maxPrice: selectedPriceRange[1] < maxPossiblePrice ? selectedPriceRange[1] : null,
      dateRange: {
        start: selectedStartDate || null,
        end: selectedEndDate || null
      },
      selectedMonth: month,
      selectedYear: year
    });
  };

  // Génération des mois pour le select
  const months = [
    { value: 0, label: 'Janvier' },
    { value: 1, label: 'Février' },
    { value: 2, label: 'Mars' },
    { value: 3, label: 'Avril' },
    { value: 4, label: 'Mai' },
    { value: 5, label: 'Juin' },
    { value: 6, label: 'Juillet' },
    { value: 7, label: 'Août' },
    { value: 8, label: 'Septembre' },
    { value: 9, label: 'Octobre' },
    { value: 10, label: 'Novembre' },
    { value: 11, label: 'Décembre' }
  ];

  // Génération des années (de l'année actuelle - 2 à l'année actuelle + 2)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  return (
    <Box sx={{ p: 2, pb: 2, width: '100%' }}>
      <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
        <TextField
          fullWidth
          placeholder="Rechercher par titre, description ou personne..."
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={16} color="#999" />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton 
                  edge="end" 
                  size="small" 
                  onClick={() => {
                    setSearchTerm('');
                    applyFilters('', status, jobber, priceRange, selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
                  }}
                >
                  <X size={14} />
                </IconButton>
              </InputAdornment>
            ),
            sx: {
              borderRadius: '10px',
              backgroundColor: 'rgba(0, 0, 0, 0.02)'
            }
          }}
        />
        
        <Badge 
          badgeContent={activeFiltersCount} 
          color="primary"
          sx={{
            '& .MuiBadge-badge': {
              backgroundColor: '#FF6B2C',
              fontSize: '0.7rem',
            }
          }}
        >
          <Tooltip title="Filtres avancés">
            <Button
              aria-describedby={id}
              variant="outlined"
              onClick={handleClick}
              startIcon={<Filter size={16} />}
              sx={{
                borderColor: open || activeFiltersCount > 0 ? '#FF6B2C' : '#ddd',
                color: open || activeFiltersCount > 0 ? '#FF6B2C' : '#666',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                },
                minWidth: '120px',
                height: '40px',
                borderRadius: '10px',
                textTransform: 'none',
                fontWeight: 600
              }}
            >
              Filtres
            </Button>
          </Tooltip>
        </Badge>
      </Box>

      {/* Résultats de filtrage */}
      {filteredCount !== totalCount && (
        <Box sx={{ mt: 1, mb: 1 }}>
          <Typography variant="body2" sx={{ color: '#666' }}>
            Affichage de <strong>{filteredCount}</strong> mission{filteredCount !== 1 ? 's' : ''} sur {totalCount}
            {activeFiltersCount > 0 && (
              <Button 
                size="small" 
                onClick={handleResetFilters}
                sx={{ 
                  ml: 1, 
                  color: '#FF6B2C', 
                  fontSize: '0.75rem',
                  textTransform: 'none',
                  padding: '0px 8px',
                  minWidth: 'auto'
                }}
              >
                Réinitialiser les filtres
              </Button>
            )}
          </Typography>
        </Box>
      )}

      {/* Popover avec les filtres avancés */}
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            p: 3,
            width: 350,
            borderRadius: '12px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
            mt: 1
          }
        }}
      >
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Filtres avancés
        </Typography>

        {/* Filtre par statut */}
        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel 
            id="status-filter-label" 
            shrink={true}
            sx={{ 
              backgroundColor: 'white', 
              px: 0.5, 
              borderRadius: '4px',
              transform: 'translate(14px, -9px) scale(0.75)' 
            }}
          >
            Statut
          </InputLabel>
          <Select
            labelId="status-filter-label"
            id="status-filter"
            multiple
            value={status}
            onChange={handleStatusChange}
            displayEmpty
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.length === 0 ? (
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Sélectionner un ou plusieurs statuts
                  </Typography>
                ) : (
                  (selected as string[]).map((value) => {
                    const statusObj = STATUSES.find(s => s.value === value);
                    return (
                      <Chip 
                        key={value} 
                        label={statusObj?.label} 
                        size="small"
                        sx={{ 
                          backgroundColor: `${statusObj?.color}20`,
                          color: statusObj?.color,
                          fontWeight: 600
                        }}
                      />
                    );
                  })
                )}
              </Box>
            )}
          >
            {STATUSES.map((statusOption) => (
              <MenuItem key={statusOption.value} value={statusOption.value}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box 
                    sx={{ 
                      width: 10, 
                      height: 10, 
                      borderRadius: '50%', 
                      backgroundColor: statusOption.color 
                    }} 
                  />
                  {statusOption.label}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Filtre par jobbeur */}
        {jobbers.length > 0 && (
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel 
              id="jobber-filter-label"
              shrink={true}
              sx={{ 
                backgroundColor: 'white', 
                px: 0.5, 
                borderRadius: '4px',
                transform: 'translate(14px, -9px) scale(0.75)' 
              }}
            >
              Jobbeur
            </InputLabel>
            <Select
              labelId="jobber-filter-label"
              id="jobber-filter"
              multiple
              value={jobber}
              onChange={handleJobberChange}
              displayEmpty
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.length === 0 ? (
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Sélectionner un ou plusieurs jobbeurs
                    </Typography>
                  ) : (
                    (selected as string[]).map((value) => {
                      const jobberObj = jobbers.find(j => j.id === value);
                      return <Chip key={value} label={jobberObj?.name} size="small" />;
                    })
                  )}
                </Box>
              )}
            >
              {jobbers.map((jobberOption) => (
                <MenuItem key={jobberOption.id} value={jobberOption.id}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                    <span>{jobberOption.name}</span>
                    <Chip 
                      label={jobberOption.count} 
                      size="small"
                      sx={{ 
                        height: 20, 
                        minWidth: 28,
                        fontSize: '0.7rem',
                        backgroundColor: 'rgba(0, 0, 0, 0.08)'
                      }} 
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {/* Filtre par fourchette de prix */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
            Fourchette de prix (Jobi)
          </Typography>
          <Box sx={{ px: 1 }}>
            <Slider
              value={priceRange}
              onChange={handlePriceChange}
              min={0}
              max={maxPossiblePrice || 500}
              step={5}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value} J`}
              sx={{
                color: '#FF6B2C',
                '& .MuiSlider-thumb': {
                  '&:hover, &.Mui-focusVisible': {
                    boxShadow: '0px 0px 0px 8px rgba(255, 107, 44, 0.16)'
                  }
                }
              }}
            />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <TextField
              label="Min"
              type="number"
              size="small"
              value={priceRange[0]}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 0;
                setPriceRange([value, priceRange[1]]);
                applyFilters(searchTerm, status, jobber, [value, priceRange[1]], selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
              }}
              InputProps={{ endAdornment: <InputAdornment position="end">J</InputAdornment> }}
              sx={{ width: '45%' }}
            />
            <TextField
              label="Max"
              type="number"
              size="small"
              value={priceRange[1]}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 0;
                setPriceRange([priceRange[0], value]);
                applyFilters(searchTerm, status, jobber, [priceRange[0], value], selectedStartDate, selectedEndDate, selectedMonth, selectedYear);
              }}
              InputProps={{ endAdornment: <InputAdornment position="end">J</InputAdornment> }}
              sx={{ width: '45%' }}
            />
          </Box>
        </Box>

        {/* Filtre par période */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, color: '#666' }}>
            Période
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>
            <FormControl sx={{ width: '45%' }}>
              <InputLabel id="month-select-label">Mois</InputLabel>
              <Select
                labelId="month-select-label"
                id="month-select"
                value={selectedMonth}
                label="Mois"
                onChange={handleMonthChange}
                size="small"
              >
                {months.map((month) => (
                  <MenuItem key={month.value} value={month.value}>
                    {month.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl sx={{ width: '45%' }}>
              <InputLabel id="year-select-label">Année</InputLabel>
              <Select
                labelId="year-select-label"
                id="year-select"
                value={selectedYear}
                label="Année"
                onChange={handleYearChange}
                size="small"
              >
                {years.map((year) => (
                  <MenuItem key={year} value={year}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            onClick={handleResetFilters}
            sx={{ 
              color: '#666',
              textTransform: 'none'
            }}
          >
            Réinitialiser
          </Button>
          <Button 
            variant="contained" 
            onClick={handleClose}
            sx={{
              backgroundColor: '#FF6B2C',
              '&:hover': {
                backgroundColor: '#FF7A35',
              },
              textTransform: 'none',
              borderRadius: '8px',
            }}
          >
            Appliquer
          </Button>
        </Box>
      </Popover>
    </Box>
  );
};

export default MissionFilterBar; 