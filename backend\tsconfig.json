{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["express", "node"]}, "ts-node": {"transpileOnly": true, "files": true, "compilerOptions": {"module": "CommonJS"}}, "include": ["src/**/*", "src/types/*.d.ts"], "exclude": ["node_modules", "dist"]}