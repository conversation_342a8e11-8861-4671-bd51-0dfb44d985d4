import React, { useState, useEffect } from 'react';
import { 
  PlusCircle, 
  MinusCircle, 
  Save,
  X,
  AlertCircle,
  Plus
} from 'lucide-react';
import ClientForm from './ClientForm';
import { notify } from '@/components/Notification';
import DOMPurify from 'dompurify';

interface InvoiceItem {
  id?: string;
  description: string;
  quantite: number;
  unite: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

interface Client {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse?: string;
  siret?: string;
  tva?: string;
  forme_juridique?: string;
  code_ape?: string;
  notes?: string;
}

interface Document {
  id?: string;
  number?: string;
  type: 'devis' | 'facture' | 'avoir';
  client_name: string;
  client_id?: string;
  date_creation: string;
  date_validite?: string;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
  statut: string;
  description: string;
  invoice_items: InvoiceItem[];
  client_address?: string;
  client_email?: string;
  client_phone?: string;
  client_siret?: string;
  client_tva?: string;
  conditions_paiement?: string;
  mode_paiement?: string;
  mentions_legales?: string;
  mentions_tva?: string;
  penalite_retard?: string;
  indemnite_recouvrement?: string;
  notes?: string;
  date_paiement?: string | null;
  forme_juridique?: string;
  code_ape?: string;
}

interface DocumentFormProps {
  document?: Document;
  documentType: 'devis' | 'facture' | 'avoir';
  clients: Client[];
  onSave: (document: Partial<Document>) => void | Promise<void>;
  onCancel: () => void;
  onRefreshClients?: () => void;
  scrollToForm?: boolean;
}

const emptyItem = (): InvoiceItem => ({
  id: crypto.randomUUID(),
  description: '',
  quantite: 1,
  unite: 'unité',
  prix_unitaire: 0,
  taux_tva: 20,
  montant_ht: 0,
  montant_tva: 0,
  montant_ttc: 0
});

const DocumentForm: React.FC<DocumentFormProps> = ({
  document,
  documentType,
  clients,
  onSave,
  onCancel,
  onRefreshClients,
  scrollToForm = true
}) => {
  const formRef = React.useRef<HTMLDivElement>(null);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [scrollTrigger, setScrollTrigger] = useState(Date.now());
  const [formData, setFormData] = useState<Document>({
    type: documentType,
    client_name: '',
    date_creation: new Date().toISOString().split('T')[0],
    date_validite: documentType === 'devis' ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
    total_ht: 0,
    total_tva: 0,
    total_ttc: 0,
    statut: 'brouillon',
    description: '',
    invoice_items: [emptyItem()],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showClientForm, setShowClientForm] = useState(false);

  // Initialize form with document data when editing
  useEffect(() => {
    if (document) {
      setFormData({
        ...document,
        date_creation: new Date(document.date_creation).toISOString().split('T')[0],
        date_validite: document.date_validite 
          ? new Date(document.date_validite).toISOString().split('T')[0] 
          : undefined,
        invoice_items: document.invoice_items && document.invoice_items.length > 0 
          ? document.invoice_items 
          : [emptyItem()]
      });
      
      // Find and set selected client
      const client = clients.find(c => c.nom === document.client_name);
      if (client) {
        setSelectedClient(client.id);
      }
    }
  }, [document, clients]);

  // Scroll to form when requested
  useEffect(() => {
    if (scrollToForm && formRef.current) {
      setTimeout(() => {
        formRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 100);
    }
  }, [scrollToForm, document]);

  // Force scroll on mount
  useEffect(() => {
    setScrollTrigger(Date.now());
  }, []);

  // Calculate item totals when quantity or price changes
  const calculateItemTotals = (item: InvoiceItem): InvoiceItem => {
    const montantHT = Number(item.quantite) * Number(item.prix_unitaire);
    const montantTVA = montantHT * (Number(item.taux_tva) / 100);
    const montantTTC = montantHT + montantTVA;
    
    return {
      ...item,
      montant_ht: Number(montantHT.toFixed(2)),
      montant_tva: Number(montantTVA.toFixed(2)),
      montant_ttc: Number(montantTTC.toFixed(2))
    };
  };

  // Calculate document totals based on items
  const calculateDocumentTotals = (items: InvoiceItem[]): { totalHT: number, totalTVA: number, totalTTC: number } => {
    const totalHT = items.reduce((sum, item) => sum + item.montant_ht, 0);
    const totalTVA = items.reduce((sum, item) => sum + item.montant_tva, 0);
    const totalTTC = items.reduce((sum, item) => sum + item.montant_ttc, 0);
    
    return {
      totalHT: parseFloat(totalHT.toFixed(2)),
      totalTVA: parseFloat(totalTVA.toFixed(2)),
      totalTTC: parseFloat(totalTTC.toFixed(2))
    };
  };

  // Handle changes to item fields
  const handleItemChange = (index: number, field: keyof InvoiceItem, value: any) => {
    const newItems = [...(formData.invoice_items || [])];
    
    // Convertir et valider la valeur
    let parsedValue = value;
    if (field === 'quantite' || field === 'prix_unitaire') {
      parsedValue = Math.max(0, parseFloat(value) || 0);
    } else if (field === 'taux_tva') {
      parsedValue = parseFloat(value) || 0;
    } else if (field === 'description' || field === 'unite') {
      parsedValue = DOMPurify.sanitize(value);
    }
    
    newItems[index] = {
      ...newItems[index],
      [field]: parsedValue
    };
    
    // Recalculer les totaux pour l'item modifié
    newItems[index] = calculateItemTotals(newItems[index]);
    
    // Mettre à jour tous les totaux du document
    const { totalHT, totalTVA, totalTTC } = calculateDocumentTotals(newItems);
    
    setFormData({
      ...formData,
      invoice_items: newItems,
      total_ht: totalHT,
      total_tva: totalTVA,
      total_ttc: totalTTC
    });
  };

  // Add a new item
  const addItem = () => {
    setFormData({
      ...formData,
      invoice_items: [...(formData.invoice_items || []), emptyItem()]
    });
  };

  // Remove an item
  const removeItem = (index: number) => {
    if ((formData.invoice_items || []).length === 1) {
      notify("Vous devez avoir au moins un article", "error");
      return;
    }
    
    const newItems = (formData.invoice_items || []).filter((_, i) => i !== index);
    const { totalHT, totalTVA, totalTTC } = calculateDocumentTotals(newItems);
    
    setFormData({
      ...formData,
      invoice_items: newItems,
      total_ht: totalHT,
      total_tva: totalTVA,
      total_ttc: totalTTC
    });
  };

  // Handle client selection
  const handleClientSelect = (clientId: string) => {
    setSelectedClient(clientId);
    const selected = clients.find(c => c.id === clientId);
    
    if (selected) {
      setFormData({
        ...formData,
        client_id: selected.id,
        client_name: selected.nom,
        client_email: selected.email,
        client_phone: selected.telephone,
        client_address: selected.adresse,
        client_siret: selected.siret,
        client_tva: selected.tva,
        forme_juridique: selected.forme_juridique,
        code_ape: selected.code_ape
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.client_name) newErrors.client_name = "Le nom du client est requis";
    if (!formData.date_creation) newErrors.date_creation = "La date est requise";
    if (documentType === 'devis' && !formData.date_validite) newErrors.date_validite = "La date de validité est requise";
    if ((formData.invoice_items || []).some(item => !item.description)) newErrors.items = "Description requise pour tous les articles";
    if ((formData.invoice_items || []).some(item => item.quantite <= 0)) newErrors.items = "La quantité doit être positive";
    if ((formData.invoice_items || []).some(item => item.prix_unitaire < 0)) newErrors.items = "Le prix unitaire doit être positif ou zéro";
    if ((formData.invoice_items || []).some(item => ![0, 5.5, 10, 20].includes(item.taux_tva))) newErrors.items = "Le taux de TVA doit être 0%, 5.5%, 10% ou 20%";
    
    // Validation du mode de paiement
    if (formData.mode_paiement && !['virement', 'carte', 'cheque', 'jobi', 'especes'].includes(formData.mode_paiement)) {
      newErrors.mode_paiement = "Mode de paiement invalide";
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return; // Empêcher la soumission si il y a des erreurs
    }
    
    // S'assurer que toutes les valeurs sont correctement initialisées
    const formDataToSend = {
      ...formData,
      // Si c'est une modification, conserver l'ID du document
      id: document?.id,
      // Initialiser tous les champs optionnels avec des valeurs par défaut
      client_address: formData.client_address || "",
      client_email: formData.client_email || "",
      client_phone: formData.client_phone || "",
      client_siret: formData.client_siret || "",
      client_tva: formData.client_tva || "",
      conditions_paiement: formData.conditions_paiement || "",
      mode_paiement: formData.mode_paiement || "virement",
      mentions_legales: formData.mentions_legales || "",
      mentions_tva: formData.mentions_tva || "",
      penalite_retard: formData.penalite_retard || "",
      indemnite_recouvrement: formData.indemnite_recouvrement || "",
      notes: formData.notes || "",
      // Omettre date_paiement s'il n'est pas défini
      ...(formData.date_paiement ? { date_paiement: formData.date_paiement } : {}),
      items: (formData.invoice_items || []).map(item => ({
        ...item,
        // Si c'est une modification, conserver l'ID de l'item s'il existe
        id: item.id || crypto.randomUUID(),
        quantite: Number(item.quantite),
        prix_unitaire: Number(item.prix_unitaire),
        taux_tva: Number(item.taux_tva),
        montant_ht: Number(item.montant_ht),
        montant_tva: Number(item.montant_tva),
        montant_ttc: Number(item.montant_ttc),
        unite: item.unite || "unité"
      })),
      forme_juridique: formData.forme_juridique || "",
      code_ape: formData.code_ape || ""
    };
    
    // Faire défiler la page vers le haut avant de fermer le formulaire
      window.scrollTo({ top: 0, behavior: 'smooth' });
    
    onSave(formDataToSend);
  };

  const handleClientFormSave = () => {
    setShowClientForm(false);
    // Refresh clients list
    onRefreshClients?.();
  };

  // Handle form field changes
  const handleFieldChange = (field: string, value: any) => {
    if (typeof value === 'string' && field !== 'date_creation' && field !== 'date_validite') {
      value = DOMPurify.sanitize(value);
    }
    setFormData({
      ...formData,
      [field]: value
    });
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div ref={formRef} className="bg-white rounded-lg shadow-sm p-6 border" onClick={() => setScrollTrigger(Date.now())}>
          <h2 className="text-xl font-bold text-gray-800 mb-4">
            {document ? 'Modifier le' : 'Créer un nouveau'} {documentType === 'devis' ? 'devis' : documentType === 'facture' ? 'facture' : 'avoir'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client *
              </label>
              <div className="flex gap-2">
                <select
                  value={selectedClient}
                  onChange={(e) => handleClientSelect(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                  required
                >
                  <option value="">Sélectionner un client</option>
                  {clients.map(client => (
                    <option key={client.id} value={client.id}>
                      {client.nom}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => setShowClientForm(true)}
                  className="px-3 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] flex items-center"
                  title="Ajouter un nouveau client"
                >
                  <Plus size={18} />
                </button>
              </div>
              {selectedClient && document?.id && formData.statut === 'brouillon' && (
                <div className="mt-2">
                  <button
                    type="button"
                    onClick={() => {
                      const selected = clients.find(c => c.id === selectedClient);
                      if (selected) {
                        setFormData({
                          ...formData,
                          client_email: selected.email,
                          client_phone: selected.telephone,
                          client_address: selected.adresse,
                          client_siret: selected.siret,
                          client_tva: selected.tva,
                          forme_juridique: selected.forme_juridique,
                          code_ape: selected.code_ape
                        });
                        notify('Informations client mises à jour', 'success');
                      }
                    }}
                    className="text-sm text-[#FF7A35] hover:text-[#ff6b2c] flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg>
                    Mettre à jour les informations client
                  </button>
                  <p className="text-xs text-gray-500 mt-1">
                    Permet de synchroniser les informations du client avec sa fiche actuelle.
                  </p>
                  <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <p className="text-xs text-[#FF7A35]">
                      <strong>Pourquoi ce bouton ?</strong> Lorsqu'un document est créé, les informations du client sont copiées à ce moment précis. 
                      Si vous modifiez la fiche client ultérieurement, les documents existants ne sont pas mis à jour automatiquement. 
                      Ce bouton vous permet de mettre à jour manuellement les coordonnées du client sur ce document avec 
                      les informations les plus récentes de sa fiche.
                    </p>
                  </div>
                </div>
              )}
              {errors.client_name && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <AlertCircle size={12} className="mr-1" />
                  {errors.client_name}
                </p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input 
                type="date"
                value={formData.date_creation}
                onChange={(e) => handleFieldChange('date_creation', e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                required
              />
              {errors.date_creation && (
                <p className="mt-1 text-red-600 text-xs flex items-center">
                  <AlertCircle size={12} className="mr-1" />
                  {errors.date_creation}
                </p>
              )}
            </div>
            
            {documentType === 'devis' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date de validité *
                </label>
                <input 
                  type="date"
                  value={formData.date_validite || ''}
                  onChange={(e) => handleFieldChange('date_validite', e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                  required={documentType === 'devis'}
                />
                {errors.date_validite && (
                  <p className="mt-1 text-red-600 text-xs flex items-center">
                    <AlertCircle size={12} className="mr-1" />
                    {errors.date_validite}
                  </p>
                )}
              </div>
            )}
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea 
              value={formData.description || ''}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              rows={2}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              placeholder="Description ou objet du document"
            ></textarea>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 border">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-gray-800">Articles</h3>
            <button 
              type="button"
              onClick={addItem}
              className="inline-flex items-center px-3 py-1.5 text-sm text-[#FF7A35] bg-orange-50 rounded-md border border-[#FF7A35] hover:bg-orange-100"
            >
              <PlusCircle size={16} className="mr-1" />
              Ajouter un article
            </button>
          </div>
          
          {errors.items && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-xs flex items-center">
                <AlertCircle size={12} className="mr-1" />
                {errors.items}
              </p>
            </div>
          )}
          
          {/* Table view for desktop */}
          <div className="hidden md:block overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                    Qté
                  </th>
                  <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                    Unité
                  </th>
                  <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                    Prix unit.
                  </th>
                  <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                    TVA %
                  </th>
                  <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                    Total HT
                  </th>
                  <th scope="col" className="w-10"></th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {formData.invoice_items?.map((item, index) => (
                  <tr key={item.id}>
                    <td className="px-3 py-2">
                      <textarea 
                        value={item.description}
                        onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                        placeholder="Description de l'article"
                        rows={2}
                        required
                      />
                    </td>
                    <td className="px-3 py-2">
                      <input 
                        type="number"
                        min="0.01"
                        step="0.01"
                        value={item.quantite}
                        onChange={(e) => handleItemChange(index, 'quantite', e.target.value)}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm text-right focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                        required
                      />
                    </td>
                    <td className="px-3 py-2">
                      <input 
                        type="text"
                        value={item.unite}
                        onChange={(e) => handleItemChange(index, 'unite', e.target.value)}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                        placeholder="unité"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <input 
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.prix_unitaire}
                        onChange={(e) => handleItemChange(index, 'prix_unitaire', e.target.value)}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm text-right focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                        required
                      />
                    </td>
                    <td className="px-3 py-2">
                      <select
                        value={item.taux_tva}
                        onChange={(e) => handleItemChange(index, 'taux_tva', e.target.value)}
                        className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm text-right focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                      >
                        <option value="0">0%</option>
                        <option value="5.5">5.5%</option>
                        <option value="10">10%</option>
                        <option value="20">20%</option>
                      </select>
                    </td>
                    <td className="px-3 py-2 text-right">
                      <span className="text-gray-700">{item.montant_ht.toFixed(2)} €</span>
                    </td>
                    <td className="px-3 py-2">
                      <button 
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <MinusCircle size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Card view for mobile */}
          <div className="md:hidden space-y-4">
            {(formData.invoice_items || []).map((item, index) => (
              <div key={item.id} className="bg-white border rounded-lg p-4 shadow-sm relative">
                <div className="absolute top-2 right-2">
                  <button 
                    type="button"
                    onClick={() => removeItem(index)}
                    className="text-red-500 hover:text-red-700 p-1"
                  >
                    <MinusCircle size={18} />
                  </button>
                </div>
                
                <div className="mb-3">
                  <label className="block text-xs font-medium text-gray-500 mb-1">
                    Description
                  </label>
                  <textarea 
                    value={item.description}
                    onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                    placeholder="Description de l'article"
                    rows={2}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">
                      Quantité
                    </label>
                    <input 
                      type="number"
                      min="0.01"
                      step="0.01"
                      value={item.quantite}
                      onChange={(e) => handleItemChange(index, 'quantite', e.target.value)}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-right focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">
                      Unité
                    </label>
                    <input 
                      type="text"
                      value={item.unite}
                      onChange={(e) => handleItemChange(index, 'unite', e.target.value)}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                      placeholder="unité"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">
                      Prix unitaire
                    </label>
                    <input 
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.prix_unitaire}
                      onChange={(e) => handleItemChange(index, 'prix_unitaire', e.target.value)}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-right focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-500 mb-1">
                      TVA %
                    </label>
                    <select
                      value={item.taux_tva}
                      onChange={(e) => handleItemChange(index, 'taux_tva', e.target.value)}
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                    >
                      <option value="0">0%</option>
                      <option value="5.5">5.5%</option>
                      <option value="10">10%</option>
                      <option value="20">20%</option>
                    </select>
                  </div>
                </div>
                
                <div className="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
                  <span className="text-xs font-medium text-gray-500">Total HT:</span>
                  <span className="text-sm font-medium text-[#FF7A35]">{item.montant_ht.toFixed(2)} €</span>
                </div>
              </div>
            ))}
            
            <button 
              type="button"
              onClick={addItem}
              className="w-full py-3 flex justify-center items-center text-[#FF7A35] bg-orange-50 rounded-md border border-[#FF7A35] hover:bg-orange-100"
            >
              <PlusCircle size={18} className="mr-2" />
              Ajouter un article
            </button>
          </div>
          
          <div className="mt-4 flex justify-end">
            <div className="w-64">
              <div className="grid grid-cols-2 gap-1 text-sm">
                <div className="text-right text-gray-600">Sous-total:</div>
                <div className="text-right font-medium">{formData.total_ht.toFixed(2)} €</div>
                
                <div className="text-right text-gray-600">TVA:</div>
                <div className="text-right font-medium">{formData.total_tva.toFixed(2)} €</div>
                
                <div className="text-right text-gray-600 font-semibold pt-1 border-t">Total:</div>
                <div className="text-right font-semibold text-lg text-[#FF7A35] pt-1 border-t">{formData.total_ttc.toFixed(2)} €</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 border">
          <h3 className="font-semibold text-gray-800 mb-4">Informations complémentaires</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Conditions de paiement
              </label>
              <input 
                type="text"
                value={formData.conditions_paiement || ''}
                onChange={(e) => handleFieldChange('conditions_paiement', e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder="Ex: Paiement à réception, à 30 jours, etc."
              />
              <p className="mt-1 text-xs text-gray-500">Délais, acompte éventuel, etc.</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mode de paiement
              </label>
              <select
                value={formData.mode_paiement || ''}
                onChange={(e) => handleFieldChange('mode_paiement', e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              >
                <option value="">Sélectionner un mode de paiement</option>
                <option value="virement">Virement</option>
                <option value="carte">Carte</option>
                <option value="cheque">Chèque</option>
                <option value="jobi">Jobi</option>
                <option value="especes">Espèces</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pénalités de retard
              </label>
              <input 
                type="text"
                value={formData.penalite_retard || (documentType === 'facture' ? '3 fois le taux d\'intérêt légal' : '')}
                onChange={(e) => handleFieldChange('penalite_retard', e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder="Ex: 3 fois le taux d'intérêt légal"
              />
              <p className="mt-1 text-xs text-gray-500">Obligatoire pour les factures (en % annuel)</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Indemnité forfaitaire de recouvrement
              </label>
              <input 
                type="text"
                value={formData.indemnite_recouvrement || (documentType === 'facture' ? 'Indemnité forfaitaire pour frais de recouvrement : 40 € (art. D441-5 du Code de commerce)' : '')}
                onChange={(e) => handleFieldChange('indemnite_recouvrement', e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder="Ex: Indemnité forfaitaire de 40€"
              />
              <p className="mt-1 text-xs text-gray-500">Obligatoire pour les factures professionnelles</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mentions légales
              </label>
              <textarea 
                value={formData.mentions_legales || ''}
                onChange={(e) => handleFieldChange('mentions_legales', e.target.value)}
                rows={2}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder="Mentions légales obligatoires"
              ></textarea>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mentions TVA
              </label>
              <textarea 
                value={formData.mentions_tva || ''}
                onChange={(e) => handleFieldChange('mentions_tva', e.target.value)}
                rows={2}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder={documentType === 'facture' ? 'Ex: TVA non applicable, art. 293 B du CGI' : 'Mentions concernant la TVA'}
              ></textarea>
              {documentType === 'facture' && (
                <p className="mt-1 text-xs text-gray-500">Ex: "TVA non applicable, art. 293 B du CGI" pour auto-entrepreneurs</p>
              )}
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes additionnelles
              </label>
              <textarea 
                value={formData.notes || ''}
                onChange={(e) => handleFieldChange('notes', e.target.value)}
                rows={3}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
                placeholder="Notes ou informations supplémentaires"
              ></textarea>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-3 pt-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none inline-flex items-center"
          >
            <X size={18} className="mr-1" />
            Annuler
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] focus:outline-none inline-flex items-center"
          >
            <Save size={18} className="mr-1" />
            Enregistrer
          </button>
        </div>
      </form>

      {showClientForm && (
        <ClientForm
          onSave={handleClientFormSave}
          onCancel={() => setShowClientForm(false)}
        />
      )}
    </>
  );
};

export default DocumentForm;