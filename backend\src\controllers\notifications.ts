import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { messagingService } from '../services/messagingService'; // Importer messagingService

export class NotificationController {
  // Récupérer toutes les notifications d'un utilisateur
  async getNotifications(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { archived, page = 1, type, search, unread } = req.query;
      const isArchived = archived === 'true';
      const pageSize = 20;
      const offset = (Number(page) - 1) * pageSize;

      logger.info('Fetching notifications with params:', { 
        userId, 
        archived,
        page,
        type,
        search,
        unread,
        offset
      });

      // Générer une clé de cache unique
      const cacheKey = `notifications:${userId}:${isArchived}:${page}:${type || ''}:${search || ''}:${unread || ''}`;
      
      // Vérifier si les données sont en cache
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        // Retourner les données du cache
        res.json(JSON.parse(cachedData));
        return;
      }

      let query = supabase
        .from('user_notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_archived', isArchived)
        .order('created_at', { ascending: false })
        .range(offset, offset + pageSize - 1);

      // Appliquer les filtres si présents
      if (type) {
        query = query.eq('type', type);
      }
      if (search) {
        query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`);
      }
      if (unread === 'true') {
        query = query.eq('is_read', false);
      }

      const { data: notifications, error, count } = await query;

      if (error) {
        logger.error('Erreur lors de la récupération des notifications:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des notifications'
        });
        return;
      }

      // Vérifier et maintenir la limite de 75 notifications
      const totalCount = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_archived', isArchived);

      if (totalCount.count && totalCount.count > 75) {
        // Supprimer les notifications les plus anciennes par lots de 3 (limite Supabase)
        let toDelete = totalCount.count - 75;
        while (toDelete > 0) {
          // Récupérer les IDs des plus anciennes notifications à supprimer (max 3)
          const { data: oldNotifications, error: fetchOldError } = await supabase
            .from('user_notifications')
            .select('id')
            .eq('user_id', userId)
            .eq('is_archived', isArchived)
            .order('created_at', { ascending: true })
            .limit(Math.min(3, toDelete));

          if (fetchOldError || !oldNotifications || oldNotifications.length === 0) break;

          const idsToDelete = oldNotifications.map(n => n.id);
          const { error: deleteError } = await supabase
            .from('user_notifications')
            .delete()
            .in('id', idsToDelete);

          if (deleteError) {
            logger.error('Erreur lors de la suppression des anciennes notifications:', deleteError);
            break;
          }
          toDelete -= idsToDelete.length;
        }
      }

      const response = {
        success: true,
        data: notifications,
        pagination: {
          page: Number(page),
          pageSize,
          totalPages: count ? Math.ceil(count / pageSize) : 0,
          totalCount: count || 0
        }
      };
      
      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(response));

      res.json(response);
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération des notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
      return;
    }
  }

  // Marquer une notification comme lue ou non lue
  async markAsRead(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { notificationId } = req.params;

      // Récupérer d'abord la notification pour connaître son état actuel
      const { data: notification, error: fetchError } = await supabase
        .from('user_notifications')
        .select('is_read')
        .eq('id', notificationId)
        .eq('user_id', userId)
        .single();

      if (fetchError) {
        logger.error('Erreur lors de la récupération de la notification:', fetchError);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de la notification'
        });
        return;
      }

      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notification non trouvée'
        });
        return;
      }

      // Basculer le statut is_read
      const newReadStatus = !notification.is_read;

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_read: newReadStatus })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors du changement de statut de lecture:', error);
        res.status(500).json({
          success: false,
          message: `Erreur lors du marquage de la notification comme ${newReadStatus ? 'lue' : 'non lue'}`
        });
        return;
      }

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      // Invalider le cache du compteur de notifications non lues
      await redis.del(`notifications_count:${userId}`);

      res.json({
        success: true,
        message: `Notification marquée comme ${newReadStatus ? 'lue' : 'non lue'}`
      });
    } catch (error) {
      logger.error('Erreur lors du changement de statut de lecture:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }

  // Marquer une notification comme archivée ou non
  async archiveNotification(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { notificationId } = req.params;
      const { is_archived = true } = req.body;

      logger.info('Modification du statut d\'archive - Paramètres:', {
        userId,
        notificationId,
        is_archived
      });

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_archived })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors de la modification du statut d\'archive:', error);
        res.status(500).json({
          success: false,
          message: `Erreur lors du changement de statut d'archive de la notification`
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        message: `Notification ${is_archived ? 'archivée' : 'désarchivée'}`
      });
    } catch (error) {
      logger.error('Erreur lors de la modification du statut d\'archive:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }

  // Supprimer une notification
  async deleteNotification(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { notificationId } = req.params;

      const { error } = await supabase
        .from('user_notifications')
        .delete()
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors de la suppression de la notification:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la suppression de la notification'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        message: 'Notification supprimée'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la notification:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }

  // Récupérer le nombre de notifications non lues
  async getUnreadCount(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      
      // Vérifier d'abord dans le cache Redis
      const cacheKey = `notifications_count:${userId}`;
      const cachedCount = await redis.get(cacheKey);
      
      if (cachedCount !== null) {
        res.json({
          success: true,
          count: parseInt(cachedCount)
        });
        return;
      }

      const { data, error } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_read', false)
        .eq('is_archived', false);

      if (error) {
        logger.error('Erreur lors du comptage des notifications:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors du comptage des notifications'
        });
        return;
      }

      const count = data?.length || 0;
      
      // Mettre en cache pour 5 minutes
      await redis.setex(cacheKey, 300, count.toString());

      res.json({
        success: true,
        count
      });
    } catch (error) {
      logger.error('Erreur lors du comptage des notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }

  // Créer une nouvelle notification
  async createNotification(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { type, title, content, link } = req.body;

      // Validation des données requises
      if (!type || !title || !content) {
        res.status(400).json({
          success: false,
          message: 'Type, titre et contenu sont requis'
        });
        return;
      }

      // Vérification du type de notification valide
      const validTypes = ['jobi', 'message', 'mission', 'system', 'mission_comment', 'profile', 'bug_report', 'invoice', 'subscription'];
      if (!validTypes.includes(type)) {
        res.status(400).json({
          success: false,
          message: 'Type de notification invalide'
        });
        return;
      }

      const { data: notification, error } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          type,
          title,
          content,
          link,
          is_read: false,
          is_archived: false
        })
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la création de la notification:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la création de la notification'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        data: notification
      });
    } catch (error) {
      logger.error('Erreur lors de la création de la notification:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  }

  // Créer plusieurs notifications en même temps
  async createMultipleNotifications(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { notifications } = req.body;

      if (!Array.isArray(notifications) || notifications.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Le tableau de notifications est requis'
        });
        return;
      }

      const validTypes = ['jobi', 'message', 'mission', 'system', 'mission_comment', 'profile', 'bug_report', 'invoice', 'subscription'];
      const notificationsToInsert = notifications.map((notif: any) => ({
        user_id: userId,
        type: notif.type,
        title: notif.title,
        content: notif.content,
        link: notif.link,
        is_read: false,
        is_archived: false
      }));

      // Validation des données
      const isValid = notificationsToInsert.every((notif: any) => 
        notif.type && 
        validTypes.includes(notif.type) && 
        notif.title && 
        notif.content
      );

      if (!isValid) {
        res.status(400).json({
          success: false,
          message: 'Données de notification invalides'
        });
        return;
      }

      const { data: createdNotifications, error } = await supabase
        .from('user_notifications')
        .insert(notificationsToInsert)
        .select();

      if (error) {
        logger.error('Erreur lors de la création des notifications:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la création des notifications'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        data: createdNotifications
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la création des notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
      return;
    }
  }

  // Marquer toutes les notifications comme lues
  async markAllAsRead(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false)
        .eq('is_archived', false);

      if (error) {
        logger.error('Erreur lors du marquage de toutes les notifications comme lues:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors du marquage des notifications comme lues'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        message: 'Toutes les notifications ont été marquées comme lues'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors du marquage de toutes les notifications comme lues:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
      return;
    }
  }

  // Archiver toutes les notifications
  async archiveAllNotifications(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_archived: true })
        .eq('user_id', userId)
        .eq('is_archived', false);

      if (error) {
        logger.error('Erreur lors de l\'archivage de toutes les notifications:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de l\'archivage des notifications'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        message: 'Toutes les notifications ont été archivées'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de l\'archivage de toutes les notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
      return;
    }
  }

  // Supprimer toutes les notifications
  async deleteAllNotifications(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const { archived = false } = req.query;
      const isArchived = archived === 'true';

      const { error } = await supabase
        .from('user_notifications')
        .delete()
        .eq('user_id', userId)
        .eq('is_archived', isArchived);

      if (error) {
        logger.error('Erreur lors de la suppression de toutes les notifications:', error);
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la suppression des notifications'
        });
        return;
      }

      // Invalider le cache Redis pour le compteur de notifications
      const cacheKey = `notifications_count:${userId}`;
      await redis.del(cacheKey);

      // Invalider le cache des notifications
      const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
      if (notificationsCacheKeys.length > 0) {
        await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
      }

      res.json({
        success: true,
        message: 'Toutes les notifications ont été supprimées'
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la suppression de toutes les notifications:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
      return;
    }
  }

  // Récupérer le nombre combiné de notifications et messages non lus
  async getCombinedUnreadCounts(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
        return;
      }

      // Clé de cache combinée
      const cacheKey = `combined_unread_count:${userId}`;
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        res.json({ success: true, count: parseInt(cachedData) });
        return;
      }

      // Obtenir le nombre de notifications non lues (non archivées)
      const { count: notificationCount, error: notificationError } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false)
        .eq('is_archived', false);

      if (notificationError) {
        logger.error('Erreur lors du comptage des notifications non lues:', { userId, error: notificationError });
        // Continuer même en cas d'erreur pour obtenir le compte des messages
      }

      // Obtenir le nombre de messages non lus
      const messageResult = await messagingService.getUnreadMessageCount(userId);
      if (!messageResult.success) {
        logger.error('Erreur lors de la récupération du nombre de messages non lus:', { userId, error: messageResult.error });
        // Continuer même en cas d'erreur
      }

      const totalUnreadCount = (notificationCount || 0) + (messageResult.data || 0);

      // Mettre en cache pour 1 minute
      await redis.setex(cacheKey, 60, totalUnreadCount.toString());

      res.json({
        success: true,
        count: totalUnreadCount
      });
      return;
    } catch (error) {
      logger.error('Erreur lors de la récupération des comptes combinés non lus:', { userId: req.user?.userId, error });
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des comptes non lus'
      });
      return;
    }
  }
}
