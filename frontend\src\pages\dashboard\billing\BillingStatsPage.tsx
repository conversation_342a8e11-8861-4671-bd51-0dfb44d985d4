import React, { useState } from 'react';
import { 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  Typography, 
  LinearProgress, 
  Paper,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Stack,
  IconButton,
  Divider,
  Tooltip as MuiTooltip
} from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fr } from 'date-fns/locale';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Equal,
  AlertCircle,
  FileText,
  Wallet,
  Clock,
  FileCheck,
  Calendar,
  FileX,
  FilePlus2,
  FileEdit,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  User,
  Receipt,
  CreditCard,
  Filter,
  FileText as FileSpreadsheet,
  File as FilePdf,
  RefreshCw
} from 'lucide-react';
import { useBillingStats, BillingStatsFilters } from '../../../hooks/invoices/useBillingStats';
import LoadingSpinner from '../../../components/LoadingSpinner';

// Formatage des montants
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Formatage des mois
const formatMonth = (monthStr: string): string => {
  const [year, month] = monthStr.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1, 1);
  return date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
};

// Composant pour afficher un KPI
const StatCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  subtitle?: string;
  color?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
}> = ({ title, value, icon, subtitle, color = '#FF7A35', trend, trendValue }) => {
  
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} color="#36B37E" />;
      case 'down':
        return <TrendingDown size={16} color="#FF5630" />;
      case 'stable':
        return <Equal size={16} color="#6B778C" />;
      default:
        return null;
    }
  };

  return (
    <Card 
      elevation={0} 
      sx={{ 
        height: '100%', 
        borderRadius: '12px', 
        border: '1px solid rgba(0,0,0,0.08)',
        transition: 'all 0.2s ease-in-out',
        padding: '14px',
        '&:hover': {
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          transform: 'translateY(-2px)'
        }
      }}
    >
      <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Typography 
            variant="body2" 
            color="text.secondary" 
            fontWeight={500}
            sx={{ lineHeight: 1.4 }}
          >
            {title}
          </Typography>
          <Box 
            sx={{ 
              p: 1.5,
              borderRadius: '8px',
              backgroundColor: `${color}15`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: color
            }}
          >
            {icon}
          </Box>
        </Box>
        
        <Typography 
          variant="h4" 
          fontWeight={700} 
          sx={{ 
            color: color,
            mb: subtitle || trend ? 1.5 : 0,
            lineHeight: 1.2
          }}
        >
          {value}
        </Typography>
        
        {subtitle && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ mb: trend ? 1 : 0, lineHeight: 1.3 }}
          >
            {subtitle}
          </Typography>
        )}
        
        {trend && trendValue && (
          <Box display="flex" alignItems="center" gap={0.5} mt={1}>
            {getTrendIcon()}
            <Typography 
              variant="body2" 
              color={trend === 'up' ? '#36B37E' : trend === 'down' ? '#FF5630' : '#6B778C'}
              fontWeight={500}
            >
              {trendValue}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Couleurs pour les graphiques
const COLORS = ['#FF7A35', '#FF9C5E', '#FFBD86', '#36B37E', '#4670FD', '#A463F2', '#FFC043'];

// Composant principal
const BillingStatsPage: React.FC = () => {
  const { 
    stats, 
    loading, 
    error, 
    filters, 
    updateFilters, 
    refreshStats,
    exportToExcel,
    exportToPDF
  } = useBillingStats();

  const [startDate, setStartDate] = useState<Date | null>(filters.startDate || null);
  const [endDate, setEndDate] = useState<Date | null>(filters.endDate || null);
  const [type, setType] = useState<'devis' | 'facture' | 'avoir' | 'all'>(filters.type || 'all');
  const [isExporting, setIsExporting] = useState<boolean>(false);

  const handleFilterApply = () => {
    const newFilters: BillingStatsFilters = {
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      type: type
    };
    updateFilters(newFilters);
  };

  const handleExportExcel = async () => {
    try {
      setIsExporting(true);
      await exportToExcel();
    } catch (error) {
      console.error('Erreur lors de l\'export Excel:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportPDF = async () => {
    try {
      setIsExporting(true);
      await exportToPDF();
    } catch (error) {
      console.error('Erreur lors de l\'export PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h5" sx={{ mb: 4 }}>Statistiques de Facturation</Typography>
        <LoadingSpinner />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Statistiques de Facturation</Typography>
        <Paper sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <AlertCircle size={48} color="#f44336" />
          <Typography variant="h6" color="error">
            Erreur lors du chargement des statistiques
          </Typography>
          <Typography color="text.secondary">
            {error}
          </Typography>
        </Paper>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Statistiques de Facturation</Typography>
        <Paper sx={{ p: 3 }}>
          <Typography>Aucune donnée disponible.</Typography>
        </Paper>
      </Box>
    );
  }

  // Filtres et contrôles
  const renderFilters = () => (
    <Card elevation={0} sx={{ 
      mb: 3, 
      borderRadius: '12px', 
      border: '1px solid rgba(0,0,0,0.08)', 
      padding: '14px',
      width: '100%',
      minWidth: 0,
      position: 'relative' // Empêche le décalage des éléments enfants
    }}>
      <CardContent sx={{ p: 0 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight={600}>
            Filtres et exports
          </Typography>
          <MuiTooltip title="Rafraîchir les données">
            <IconButton onClick={refreshStats} size="small" sx={{ color: '#4670FD' }}>
              <RefreshCw size={20} />
            </IconButton>
          </MuiTooltip>
        </Box>
        
        <Grid container spacing={2} alignItems="flex-start" sx={{ width: '100%' }}>
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Box sx={{ minWidth: 0, width: '100%' }}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
                <DatePicker
                  label="Date de début"
                  value={startDate}
                  onChange={(newValue: Date | null) => setStartDate(newValue)}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      sx: { 
                        minWidth: 0,
                        width: '100%',
                        '& .MuiInputBase-root': {
                          width: '100%'
                        }
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 1300 // S'assurer que le popper est au-dessus
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Box>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
            <Box sx={{ minWidth: 0, width: '100%' }}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={fr}>
                <DatePicker
                  label="Date de fin"
                  value={endDate}
                  onChange={(newValue: Date | null) => setEndDate(newValue)}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      sx: { 
                        minWidth: 0,
                        width: '100%',
                        '& .MuiInputBase-root': {
                          width: '100%'
                        }
                      }
                    },
                    popper: {
                      sx: {
                        zIndex: 1300
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Box>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, lg: 2 }}>
            <Box sx={{ minWidth: 0, width: '100%' }}>
              <FormControl fullWidth size="small">
                <InputLabel id="document-type-label">Type de document</InputLabel>
                <Select
                  labelId="document-type-label"
                  value={type}
                  label="Type de document"
                  onChange={(e) => setType(e.target.value as 'devis' | 'facture' | 'avoir' | 'all')}
                  sx={{ 
                    minWidth: 0,
                    width: '100%',
                    '& .MuiSelect-select': {
                      width: '100%'
                    }
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        maxHeight: 200,
                        zIndex: 1300
                      }
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left'
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left'
                    }
                  }}
                >
                  <MenuItem value="all">Tous</MenuItem>
                  <MenuItem value="devis">Devis</MenuItem>
                  <MenuItem value="facture">Factures</MenuItem>
                  <MenuItem value="avoir">Avoirs</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, lg: 4 }}>
            <Stack direction="row" spacing={1} sx={{ 
              justifyContent: 'flex-start', 
              flexWrap: 'wrap',
              alignItems: 'center',
              minHeight: '40px' // Hauteur minimale pour éviter le décalage
            }}>
              <Button
                variant="contained"
                startIcon={<Filter />}
                onClick={handleFilterApply}
                sx={{ 
                  bgcolor: '#FF7A35',
                  '&:hover': { bgcolor: '#FF6B2C' },
                  minWidth: 'auto',
                  height: '40px'
                }}
              >
                Appliquer
              </Button>
              
              <Divider orientation="vertical" flexItem sx={{ height: '24px', alignSelf: 'center' }} />
              
              <MuiTooltip title="Exporter en Excel">
                <IconButton 
                  onClick={handleExportExcel}
                  disabled={isExporting}
                  sx={{ color: '#36B37E', width: '40px', height: '40px' }}
                >
                  <FileSpreadsheet size={20} />
                </IconButton>
              </MuiTooltip>
              
              <MuiTooltip title="Exporter en PDF">
                <IconButton 
                  onClick={handleExportPDF}
                  disabled={isExporting}
                  sx={{ color: '#FF5630', width: '40px', height: '40px' }}
                >
                  <FilePdf size={20} />
                </IconButton>
              </MuiTooltip>
            </Stack>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  // Préparation des données pour les graphiques
  const monthlyData = stats.monthly.map(item => ({
    ...item,
    name: formatMonth(item.month),
    Devis: item.quotes,
    Factures: item.invoices,
    Avoirs: item.creditNotes,
    Montant: item.amount,
  }));

  // Traduction des statuts
  const translateStatus = (status: string): string => {
    const translations: Record<string, string> = {
      'brouillon': 'Brouillon',
      'émis': 'Émis',
      'envoye': 'Envoyé',
      'payé': 'Payé',
      'paye': 'Payé',
      'à payer': 'À payer',
      'en retard': 'En retard',
      'en_retard': 'En retard',
      'annulé': 'Annulé',
      'annule': 'Annulé',
      'refusé': 'Refusé',
      'refuse': 'Refusé',
      'accepté': 'Accepté',
      'accepte': 'Accepté',
      'partiellement_payé': 'Partiellement payé'
    };
    return translations[status] || status;
  };

  // Données pour le graphique de répartition par type
  const typeData = Object.entries(stats.byType).map(([type, data]) => ({
    name: type === 'devis' ? 'Devis' : type === 'facture' ? 'Factures' : 'Avoirs',
    value: data.count,
    amount: data.amount,
    percentage: data.percentage
  }));

  // Données pour le graphique de répartition par statut
  const statusData = Object.entries(stats.byStatus).map(([status, data]) => ({
    name: translateStatus(status),
    value: data.count,
    amount: data.amount,
    percentage: data.percentage
  }));

  // Données pour le tableau des clients
  const clientData = stats.byClient || [];

  // Données pour le délai de paiement
  const paymentDelayData = stats.paymentStats.paymentDelayByMonth.map(item => ({
    name: formatMonth(item.month),
    delay: item.delay,
    count: item.count
  }));

  // Personnalisation du tooltip des graphiques
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            bgcolor: 'background.paper',
            p: 1.5,
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: '1px solid rgba(0,0,0,0.05)',
            borderRadius: 1,
          }}
        >
          <Typography variant="body2" fontWeight={600}>{label}</Typography>
          {payload.map((entry: any, index: number) => (
            <Typography 
              key={`item-${index}`} 
              variant="body2" 
              sx={{ color: entry.color, display: 'flex', alignItems: 'center', gap: 1 }}
            >
              <span style={{
                display: 'inline-block',
                width: 10,
                height: 10,
                backgroundColor: entry.color,
                borderRadius: '50%'
              }}></span>
              {entry.name}: {entry.name.includes('Montant') ? formatCurrency(entry.value) : entry.value}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>
      {/* Filtres et exports */}
      {renderFilters()}

      {/* KPI principaux */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Total des documents"
            value={stats.summary.totalDocuments}
            icon={<FileText />}
            subtitle="Devis, factures et avoirs"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Montant total"
            value={formatCurrency(stats.summary.totalAmount)}
            icon={<Wallet />}
            subtitle="Tous documents confondus"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Montant en attente"
            value={formatCurrency(stats.summary.pendingAmount)}
            icon={<Clock />}
            subtitle="Factures non payées"
            color="#4670FD"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Montant encaissé"
            value={formatCurrency(stats.summary.paidAmount)}
            icon={<FileCheck />}
            subtitle="Factures payées"
            color="#36B37E"
          />
        </Grid>
      </Grid>

      {/* Graphiques d'évolution */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" fontWeight={600}>
                  Évolution mensuelle
                </Typography>
                <Box display="flex" gap={1}>
                  <Chip 
                    label="Montant" 
                    size="small" 
                    sx={{ backgroundColor: '#FF7A35', color: 'white' }}
                  />
                  <Chip 
                    label="Nombre" 
                    size="small" 
                    variant="outlined" 
                    sx={{ borderColor: '#FF7A35', color: '#FF7A35' }}
                  />
                </Box>
              </Box>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyData}
                    margin={{ top: 5, right: 0, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="left" tick={{ fontSize: 12 }} />
                    <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="Montant"
                      stroke="#FF7A35"
                      strokeWidth={2}
                      dot={{ r: 3 }}
                      activeDot={{ r: 6 }}
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="count"
                      name="Nombre"
                      stroke="#4670FD"
                      strokeWidth={2}
                      dot={{ r: 3 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>  
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Typography variant="h6" fontWeight={600} mb={2}>
                Répartition par type
              </Typography>
              <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <ResponsiveContainer width="100%" height="75%">
                  <PieChart>
                    <Pie
                      data={typeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }: { name: string, percent: number }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {typeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <Box mt={2}>
                  {typeData.map((entry, index) => (
                    <Box key={index} display="flex" alignItems="center" gap={1} mb={1}>
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '2px',
                          backgroundColor: COLORS[index % COLORS.length]
                        }}
                      />
                      <Typography variant="body2" fontWeight={500}>
                        {entry.name}: {entry.value} ({formatCurrency(entry.amount)})
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Détail des types de documents */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>  
          <StatCard
            title="Devis"
            value={stats.byType.devis.count}
            icon={<FilePlus2 />}
            subtitle={`${formatCurrency(stats.byType.devis.amount)}`}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Factures"
            value={stats.byType.facture.count}
            icon={<FileEdit />}
            subtitle={`${formatCurrency(stats.byType.facture.amount)}`}
            color="#36B37E"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Avoirs"
            value={stats.byType.avoir.count}
            icon={<FileX />}
            subtitle={`${formatCurrency(stats.byType.avoir.amount)}`}
            color="#A463F2"
          />
        </Grid>
      </Grid>

      {/* Répartition par statut et clients */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Typography variant="h6" fontWeight={600} mb={3}>
                Répartition par statut
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={statusData}
                    layout="vertical"
                    margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={false} />
                    <XAxis type="number" tick={{ fontSize: 12 }} />
                    <YAxis 
                      dataKey="name" 
                      type="category" 
                      tick={{ fontSize: 12 }} 
                      width={50}
                    />
                    <Tooltip 
                      content={<CustomTooltip />}
                    />
                    <Legend />
                    <Bar dataKey="value" name="Nombre" fill="#FF7A35" barSize={20} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>  
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Typography variant="h6" fontWeight={600} mb={2}>
                Principaux clients
              </Typography>
              <TableContainer sx={{ maxHeight: 300 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 600 }}>Client</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Documents</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>Montant</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 600 }}>%</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {clientData.length > 0 ? (
                      clientData.map((client, index) => (
                        <TableRow key={index} hover>
                          <TableCell>{client.clientName}</TableCell>
                          <TableCell align="right">{client.count}</TableCell>
                          <TableCell align="right">{formatCurrency(client.amount)}</TableCell>
                          <TableCell align="right">{client.percentage}%</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} align="center">Aucun client disponible</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Délai de paiement */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>  
          <StatCard
            title="Délai de paiement moyen"
            value={`${stats.paymentStats.averagePaymentDelay} jours`}
            icon={<Calendar />}
            subtitle="Factures payées uniquement"
            color="#4670FD"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Typography variant="h6" fontWeight={600} mb={2}>
                Évolution du délai de paiement
              </Typography>
              <Box sx={{ height: 200 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={paymentDelayData}
                    margin={{ top: 5, right: 30, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} width={30} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar 
                      dataKey="delay" 
                      name="Délai (jours)" 
                      fill="#4670FD" 
                      barSize={30}
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Documents reçus */}
      <Typography variant="h6" fontWeight={600} sx={{ mb: 3, mt: 4 }}>
        Documents reçus
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Documents reçus"
            value={stats.received.totalDocuments}
            icon={<Receipt />}
            subtitle="En tant que client"
            color="#A463F2"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <StatCard
            title="Montant total reçu"
            value={formatCurrency(stats.received.totalAmount)}
            icon={<CreditCard />}
            subtitle="Tous documents confondus"
            color="#FFC043"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Card elevation={0} sx={{ height: '100%', borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
            <CardContent sx={{ p: '14px !important' }}>
              <Typography variant="h6" fontWeight={600} mb={2}>
                Répartition par type
              </Typography>
              <Box>
                {Object.entries(stats.received.byType).map(([type, data], index) => (
                  <Box key={index} mb={1}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                      <Typography variant="body2">
                        {type === 'devis' ? 'Devis' : type === 'facture' ? 'Factures' : 'Avoirs'}
                      </Typography>
                      <Typography variant="body2" fontWeight={600}>
                        {data.count} ({formatCurrency(data.amount)})
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={stats.received.totalDocuments > 0 ? (data.count / stats.received.totalDocuments) * 100 : 0} 
                      sx={{ 
                        height: 8, 
                        borderRadius: 4,
                        backgroundColor: 'rgba(0,0,0,0.05)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: COLORS[index % COLORS.length],
                          borderRadius: 4
                        }
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Expéditeurs principaux */}
      {stats.received.bySender.length > 0 && (
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6 }}>   
            <Card elevation={0} sx={{ borderRadius: '12px', border: '1px solid rgba(0,0,0,0.08)' }}>
              <CardContent sx={{ p: '14px !important' }}>
                <Typography variant="h6" fontWeight={600} mb={2}>
                  Principaux expéditeurs
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600 }}>Expéditeur</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 600 }}>Documents</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 600 }}>Montant</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {stats.received.bySender.map((sender, index) => (
                        <TableRow key={index} hover>
                          <TableCell sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <User size={16} />
                            {sender.senderName}
                          </TableCell>
                          <TableCell align="right">{sender.count}</TableCell>
                          <TableCell align="right">{formatCurrency(sender.amount)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default BillingStatsPage; 