import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import crypto from 'crypto';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import {
  encryptUserDataAsync,
  decryptUserDataAsync,
  encryptProfilDataAsync,
  decryptProfilDataAsync,
  hashEmail,
  encryptDataAsync
} from '../utils/encryption';

// Charger les variables d'environnement depuis le bon chemin
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

interface User {
  id: string;
  email: string;
  password_hash: string | null;
  role: string | null;

  // Authentification Google
  google_id?: string | null;
  google_data?: any;

  // États de vérification et activation
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  email_verifier: boolean;
  profil_actif: boolean;
  // Gestion des tentatives de connexion
  login_attempts: number;
  last_login_attempt: string | null;
  account_locked_until: string | null;

  // Champs spécifiques pour les jobbeurs
  user_type: 'jobbeur' | 'non-jobbeur';

  last_login: string | null;
  date_inscription?: string;
  password_expires_at?: string;
  referred_by?: string | null;
  referral_completed: boolean;
}

interface EmailVerification {
  id: string;
  user_id: string;
  token: string;
  expires_at: string;
  created_at: string;
}

interface PasswordReset {
  id: string;
  user_id: string;
  token: string;
  expires_at: string;
  created_at: string;
}

interface PasswordResetAttempt {
  id: string;
  user_id: string;
  attempt_count: number;
  last_attempt: string;
  cooldown_until: string | null;
}

interface Transaction {
  id: string;
  amount: number;
  status: string;
}

interface ContentFilterResult {
  keywordMatches: Array<{ severity: number }>;
  ruleMatches: Array<{ severity: number }>;
  severity: number;
}

interface ContentFilter {
  type: string;
  keywords: Array<{ text: string; severity: number }>;
  rules: Array<{ pattern: string; severity: number }>;
}

interface UpdateData {
  [key: string]: any; // Vous pouvez remplacer 'any' par un type plus spécifique si vous le savez
}

interface LoginHistoryData {
  user_id: string;
  ip_address: string;
  city: string;
  country: string;
  region: string;
  postal_code: string;
}

interface LoginHistoryEntry {
  id: string;
  login_date: string;
}

class DatabaseService {
  private static instance: DatabaseService;
  private supabaseClient: any;

  private constructor() {
    try {
      logger.info('Initialisation du service de base de données');

      // Vérification détaillée des variables d'environnement
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      logger.info('Vérification des credentials Supabase:', {
        hasUrl: !!supabaseUrl,
        hasKey: !!supabaseKey,
        urlLength: supabaseUrl?.length,
        keyLength: supabaseKey?.length
      });

      if (!supabaseUrl || !supabaseKey) {
        const error = new Error('Variables d\'environnement Supabase manquantes');
        logger.error('Erreur de configuration:', {
          error: error.message,
          supabaseUrl: !!supabaseUrl,
          supabaseKey: !!supabaseKey
        });
        throw error;
      }

      logger.info('Tentative de connexion à Supabase avec les credentials');

      try {
        this.supabaseClient = createClient(supabaseUrl, supabaseKey, {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        });
        logger.info('Client Supabase initialisé avec succès');
      } catch (error) {
        logger.error('Erreur lors de l\'initialisation du client Supabase:', {
          error: error instanceof Error ? error.message : 'Erreur inconnue',
          stack: error instanceof Error ? error.stack : undefined
        });
        throw error;
      }

      // Vérifier la connexion
      this.testConnection().catch(error => {
        logger.error('Échec du test de connexion initial:', {
          error: error instanceof Error ? error.message : 'Erreur inconnue',
          stack: error instanceof Error ? error.stack : undefined
        });
        throw error;
      });
    } catch (error) {
      logger.error('Erreur fatale lors de l\'initialisation de la base de données:', {
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  private async testConnection() {
    try {
      logger.info('Test de la connexion à Supabase');

      if (!this.supabaseClient) {
        throw new Error('Client Supabase non initialisé');
      }

      const { data, error, status, statusText } = await this.supabaseClient
        .from('users')
        .select('count')
        .limit(1);

      logger.info('Résultat du test de connexion:', {
        hasData: !!data,
        status,
        statusText
      });

      if (error) {
        logger.error('Erreur de connexion à Supabase:', {
          error: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      logger.info('Connexion à Supabase établie avec succès');
      return true;
    } catch (error) {
      logger.error('Échec du test de connexion à Supabase:', {
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public get supabase() {
    return this.supabaseClient;
  }

  private async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await redis.get(key);
      // logger.info('Lecture du cache demandé pour la clé :', { key, cached });
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.error('Erreur lors de la lecture du cache:', error);
      return null;
    }
  }

  private async setInCache(key: string, data: any, ttlSeconds: number = 120): Promise<void> {
    try {
      await redis.setex(key, ttlSeconds, JSON.stringify(data));

      // logger.info('Données mises en cache', { key, data }); // Données mises en caches dans le cache Redis avec TTL de 5 minutes (300 secondes) par défaut si aucun TTL n'est fourni au niveau de la fonction setInCache
    } catch (error) {
      logger.error('Erreur lors de l\'écriture dans le cache:', error);
    }
  }

  private async invalidateCache(key: string): Promise<void> {
    try {
      await redis.del(key);
      logger.info('Cache invalidé', { key });
    } catch (error) {
      logger.error('Erreur lors de l\'invalidation du cache:', error);
    }
  }

  // Fonction utilitaire pour générer des slugs
  private generateSlug(str: string): string {
    return str
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Enlever les accents
      .replace(/[^a-z0-9]+/g, '-') // Remplacer les caractères spéciaux par des tirets
      .replace(/^-+|-+$/g, '') // Enlever les tirets au début et à la fin
      .replace(/-+/g, '-'); // Remplacer les multiples tirets par un seul
  }

  async generateUniqueSlug(nom: string, prenom: string): Promise<string> {
    // Si le nom est "utilisateur", on le garde en entier
    // Sinon on ne prend que la première lettre du nom
    const nomFormatted = nom === 'utilisateur' ? nom : nom.charAt(0);

    // Générer le slug de base avec le prénom complet et le nom formaté selon la règle
    let baseSlug = this.generateSlug(`${prenom}-${nomFormatted}`);

    let slug = baseSlug;
    let counter = 1;

    // Vérifier si le slug existe déjà
    while (true) {
      const { data, error } = await this.supabase
        .from('user_profil')
        .select('slug')
        .eq('slug', slug)
        .maybeSingle();

      if (error || !data) {
        break;
      }

      // Si le slug existe, ajouter un numéro
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  async createUser(userData: Partial<User>) {
    try {
      logger.info('Début de la création de l\'utilisateur:', { email: userData.email, user_type: userData.user_type });

      // S'assurer que les champs requis sont présents
      // Pour l'authentification Google, le password_hash peut être null si google_id est présent
      if (!userData.email || (!userData.password_hash && !userData.google_id) || !userData.user_type) {
        logger.error('Champs requis manquants:', {
          email: !!userData.email,
          password_hash: !!userData.password_hash,
          google_id: !!userData.google_id,
          user_type: !!userData.user_type
        });
        if (!userData.password_hash && !userData.google_id) {
          throw new Error('Champs requis manquants: password_hash ou google_id est nécessaire');
        } else {
          throw new Error('Champs requis manquants: email, password_hash ou google_id, user_type');
        }
      }

      logger.info('Tentative d\'insertion dans la base de données');

      // Chiffrer les données sensibles et créer le hash de l'email (version asynchrone)
      const encryptedUserData = await encryptUserDataAsync({
        email: userData.email
      });
      const emailHash = hashEmail(userData.email);

      // 1. Insérer l'utilisateur
      const insertResult = await this.supabase
        .from('users')
        .insert({
          email: encryptedUserData.email,
          email_hash: emailHash,
          password_hash: userData.password_hash,
          google_id: userData.google_id || null,
          google_data: userData.google_data || null,
          user_type: userData.user_type,
          email_verifier: userData.email_verifier || false,
          profil_verifier: userData.profil_verifier || false,
          profil_actif: userData.profil_actif || false,
          role: userData.role || null,
          date_inscription: userData.date_inscription || new Date().toISOString(),
          password_expires_at: userData.password_hash ? new Date(Date.now() + 360 * 24 * 60 * 60 * 1000).toISOString() : null,
          referred_by: userData.referred_by || null,
          referral_completed: false
        });

      if (insertResult.error) {
        logger.error('Erreur lors de l\'insertion:', insertResult.error);
        throw insertResult.error;
      }

      logger.info('Utilisateur inséré avec succès, récupération des données');

      // 2. Récupérer l'utilisateur inséré
      const { data: user, error: selectError } = await this.supabase
        .from('users')
        .select('*')
        .eq('email_hash', emailHash)
        .maybeSingle();

      if (selectError) {
        logger.error('Erreur lors de la récupération de l\'utilisateur:', selectError);
        throw selectError;
      }

      if (!user) {
        logger.error('Utilisateur non trouvé après insertion');
        throw new Error('Utilisateur non trouvé après insertion');
      }

      // Générer un slug unique temporaire basé sur l'ID
      const tempSlug = await this.generateUniqueSlug('utilisateur', 'nouveau');

      // 3. Insérer le profil de l'utilisateur dans la table user_profil
      const { error: profilError_deux } = await this.supabase
        .from('user_profil')
        .insert({
          user_id: user.id,
          slug: tempSlug,
          nom: null,
          prenom: null,
          photo_url: "https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg",
        });

      // Creer l'utilisateur dans la table user_jobi pour l'initialiser avec 0 Jobis et profil_complet à false a l'inscription
        const { error: insertError } = await this.supabase
          .from('user_jobi')
          .insert({
            user_id: user.id,
            montant: 0,
            profil_complet: false
        });

      if (profilError_deux) {
        logger.error('Erreur lors de l\'insertion du profil:', profilError_deux);
        throw profilError_deux;
      }

      logger.info('Utilisateur créé avec succès:', { id: user.id, email: user.email });
      return user;
    } catch (error) {
      logger.error('Erreur lors de la création de l\'utilisateur:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string) {
    try {
      // Créer le hash de l'email pour la recherche efficace
      const emailHashToSearch = hashEmail(email);

      logger.info('Recherche d\'utilisateur par email:', { email, emailHashToSearch });

      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          email,
          password_hash,
          user_type,
          profil_verifier,
          email_verifier,
          profil_actif,
          role,
          login_attempts,
          last_login_attempt,
          account_locked_until,
          last_login,
          suspended_until,
          suspension_reason,
          two_factor_enabled,
          two_factor_verified
        `)
        .eq('email_hash', emailHashToSearch)
        .maybeSingle();

      if (error) {
        if (error.code === 'PGRST116') {
          logger.info('Utilisateur non trouvé par email:', { email });
          return null;
        }
        throw error;
      }

      // Déchiffrer les données utilisateur si elles existent (version asynchrone)
      if (data) {
        return await decryptUserDataAsync(data);
      }

      return data || null;

    } catch (error) {
      logger.error('Erreur lors de la recherche par email:', error);
      throw error;
    }
  }

  async getUserById(id: string) {
    // Validation de l'UUID
    if (!id || id === 'null' || id === 'undefined' || typeof id !== 'string') {
      logger.error('ID utilisateur invalide fourni à getUserById:', { id });
      return null;
    }

    // Validation du format UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      logger.error('Format UUID invalide fourni à getUserById:', { id });
      return null;
    }

    const cacheKey = `user_deux:${id}`;

    const cachedUser = await this.getFromCache<User>(cacheKey);
    if (cachedUser) {
      // logger.info('Utilisateur récupéré depuis le cache getUserById :', { id });
      // logger.info('Cache getUserById :', { cachedUser });
      return cachedUser;
    }

    const { data, error } = await this.supabase
      .from('users')
      .select(`
        id,
        profil_actif,
        profil_verifier,
        email_verifier,
        identite_verifier,
        entreprise_verifier,
        assurance_verifier,
        is_online,
        email,
        role,
        user_type,
        last_login,
        password_expires_at,
        login_attempts,
        last_login_attempt,
        account_locked_until,
        date_inscription,
        two_factor_enabled,
        two_factor_verified
      `)
      .eq('id', id)
      .maybeSingle();

    if (error) {
      logger.error('Erreur lors de la récupération de l\'utilisateur par ID:', { id, error });
      throw error;
    }

    if (data) {
      // Déchiffrer les données utilisateur (version asynchrone)
      const decryptedUserData = await decryptUserDataAsync(data);

      //Ajout de la récupération des informations de user_profil (nouvelle version avec gestion erreur)
      const userProfil: { data: any; error: any; } = await this.supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', id)
      .maybeSingle();

      if (userProfil.error) {
        logger.error('Erreur lors de la récupération des informations de user_profil:', userProfil.error);
        throw userProfil.error;
      }

      // Déchiffrer les données du profil (version asynchrone)
      const decryptedProfilData = userProfil.data ? await decryptProfilDataAsync(userProfil.data) : null;

      await redis.setex(cacheKey, 600, JSON.stringify({
        ...decryptedUserData,
        telephone: decryptedProfilData?.telephone,
        profil: {
          data: {
            nom: decryptedProfilData?.nom,
            prenom: decryptedProfilData?.prenom,
            telephone: decryptedProfilData?.telephone,
            telephone_prive: decryptedProfilData?.telephone_prive,
            adresse: decryptedProfilData?.adresse,
            ville: decryptedProfilData?.ville,
            code_postal: decryptedProfilData?.code_postal,
            pays: decryptedProfilData?.pays,
            bio: decryptedProfilData?.bio,
            slogan: decryptedProfilData?.slogan,
            email: decryptedProfilData?.email,
            photo_url: decryptedProfilData?.photo_url,
            banner_url: decryptedProfilData?.banner_url,
            banner_position: decryptedProfilData?.banner_position,
            banner_position_offset: decryptedProfilData?.banner_position_offset,
            numero: decryptedProfilData?.numero,
            intervention_zone: decryptedProfilData?.intervention_zone,
            mode_vacance: decryptedProfilData?.mode_vacance,
            type_de_profil: decryptedProfilData?.type_de_profil,
            nom_entreprise: decryptedProfilData?.nom_entreprise,
            prenom_entreprise: decryptedProfilData?.prenom_entreprise,
            statut_entreprise: decryptedProfilData?.statut_entreprise,
            siren_entreprise: decryptedProfilData?.siren_entreprise,
            code_ape_entreprise: decryptedProfilData?.code_ape_entreprise,
            categorie_entreprise: decryptedProfilData?.categorie_entreprise,
            effectif_entreprise: decryptedProfilData?.effectif_entreprise,
            date_insee_creation_entreprise: decryptedProfilData?.date_insee_creation_entreprise,
            date_categorie_entreprise: decryptedProfilData?.date_categorie_entreprise,
            date_derniere_mise_a_jour_entreprise_insee: decryptedProfilData?.date_derniere_mise_a_jour_entreprise_insee,
            date_derniere_mise_a_jour_du_client_entreprise: decryptedProfilData?.date_derniere_mise_a_jour_du_client_entreprise,
            slug: decryptedProfilData?.slug,
            profil_visible: decryptedProfilData?.profil_visible,
            date_validation_document_identite: decryptedProfilData?.date_validation_document_identite,
            date_validation_document_entreprise: decryptedProfilData?.date_validation_document_entreprise,
            date_validation_document_assurance: decryptedProfilData?.date_validation_document_assurance,
            seo_indexable: decryptedProfilData?.seo_indexable || false
          }
        }
      }));

      return { ...decryptedUserData, profil: { data: decryptedProfilData } };
    }

    return null;
  }

  async getUserByToken(token: string) {
    try {
      // Récupérer le token de la table auth_tokens
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('auth_tokens')
        .select('user_id')
        .eq('token', token)
        .maybeSingle();

      if (tokenError || !tokenData) {
        return null;
      }

      // Récupérer l'utilisateur
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', tokenData.user_id)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération de l\'utilisateur par token:', userError);
        return null;
      }

      // Déchiffrer les données utilisateur (version asynchrone)
      return userData ? await decryptUserDataAsync(userData) : userData;
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'utilisateur par token:', error);
      return null;
    }
  }

  async createEmailVerification(userId: string) {
    try {
      // Générer un token unique
      const token = crypto.randomBytes(32).toString('hex');

      // Définir l'expiration du token (par exemple, 24 heures)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Récupérer l'URL frontend depuis l'environnement
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      // Créer le token dans la table auth_tokens
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('auth_tokens')
        .insert({
          user_id: userId,
          token: token,
          type: 'email_verification',
          expires_at: expiresAt.toISOString()
        })
        .select()
        .maybeSingle();

      if (tokenError) {
        throw tokenError;
      }

      // Retourner un objet avec le token et l'URL complète de vérification
      return {
        ...tokenData,
        verificationLink: `${frontendUrl}/verify-email?token=${token}`
      };
    } catch (error) {
      logger.error('Erreur lors de la création du token de vérification:', error);
      throw error;
    }
  }

  async verifyEmail(token: string): Promise<{ user_id: string } | null> {
    try {
      logger.info('Token reçu pour vérification:', { token });
      // Récupérer le token de vérification
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', token)
        .eq('type', 'email_verification')
        .maybeSingle();

      logger.info('Recherche du token dans la base de données:', { token });

      if (tokenError || !tokenData) {
        logger.warn('❌ Token non trouvé ou invalide', {
          token,
          error: tokenError
        });
        return null;
      }

      // Log pour vérifier l'ID de l'utilisateur dans le token
      logger.info('ID utilisateur trouvé dans le token:', {
        user_id: tokenData.user_id,
        token_data: tokenData
      });

      // Vérifier si l'utilisateur existe
      const { data: userData, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', tokenData.user_id)
        .maybeSingle();

      if (userError || !userData) {
        logger.error('❌ Utilisateur non trouvé avec l\'ID du token:', {
          user_id: tokenData.user_id,
          error: userError
        });
        return null;
      }

      logger.info('Utilisateur trouvé:', {
        user_id: userData.id,
        email: userData.email
      });

      // Vérifier l'expiration du token
      const now = new Date();
      const expiresAt = new Date(tokenData.expires_at);
      logger.info('Vérification de l\'expiration du token:', {
        token,
        now,
        expiresAt
      });
      if (now > expiresAt) {
        // Supprimer le token expiré
        await this.supabase
          .from('auth_tokens')
          .delete()
          .eq('token', token);
        return null;  // Token expiré
      }

      // Mettre à jour l'utilisateur
      await this.supabase
        .from('users')
        .update({
          email_verifier: true,
          profil_actif: true
        })
        .eq('id', tokenData.user_id);

      // Supprimer le token après utilisation
      await this.supabase
        .from('auth_tokens')
        .delete()
        .eq('token', token);

      return { user_id: tokenData.user_id };
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'email:', error);
      return null;
    }
  }

  async createPasswordReset(userId: string) {
    const token = crypto.randomUUID();
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 20); // Expire dans 20 minutes

    const { data, error } = await this.supabase
      .from('auth_tokens')
      .insert({
        user_id: userId,
        token,
        type: 'password_reset',
        expires_at: expiresAt.toISOString()
      })
      .select()
      .maybeSingle();

    if (error) throw error;

    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const resetLink = `${frontendUrl}/reset-password?token=${token}`;

    return {
      ...data,
      resetLink
    };
  }

  async verifyPasswordReset(token: string, deleteToken: boolean = false) {
    try {
      logger.info('Token reçu pour vérification:', { token });

      // Vérifier d'abord si le token existe
      const { data: tokenExists, error: checkError } = await this.supabase
        .from('auth_tokens')
        .select('count')
        .eq('token', token)
        .maybeSingle();

      logger.info('Vérification de l\'existence du token:', {
        tokenExists,
        error: checkError
      });

      // Requête principale
      const { data, error } = await this.supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', token)
        .eq('type', 'password_reset')
        .maybeSingle();

      if (!data || error?.code === 'PGRST116') {
        logger.error('Aucun token trouvé', {
          token,
          type: 'password_reset',
          error: error?.message
        });
        throw new Error('TOKEN_NOT_FOUND');
      }

      const now = new Date();
      const expiresAt = new Date(data.expires_at);

      if (now > expiresAt) {
        await this.supabase
          .from('auth_tokens')
          .delete()
          .eq('token', token);

        throw new Error('TOKEN_EXPIRED');
      }

      // Ne supprimer le token que si deleteToken est true
      if (deleteToken) {
        await this.supabase
          .from('auth_tokens')
          .delete()
          .eq('token', token);
      }

      return data;
    } catch (error) {
      logger.error('Échec de la vérification du token de réinitialisation', {
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async updatePassword(userId: string, passwordHash: string, expirationDate?: Date) {
    try {
      const { error } = await this.supabase
        .from('users')
        .update({
          password_hash: passwordHash,
          password_expires_at: expirationDate || new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) // Expiration du mot de passe dans 6 mois
        })
        .eq('id', userId);

      if (error) throw error;

      return true;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du mot de passe:', error);
      throw error;
    }
  }

  async checkPasswordExpiration(userId: string): Promise<{ expired: boolean; daysUntilExpiration?: number }> {
    const { data, error } = await this.supabase
      .from('users')
      .select('password_expires_at')
      .eq('id', userId)
      .maybeSingle();

    if (error || !data?.password_expires_at) {
      return { expired: false };
    }

    const expirationDate = new Date(data.password_expires_at);
    const now = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return {
      expired: daysUntilExpiration <= 0,
      daysUntilExpiration: Math.max(0, daysUntilExpiration)
    };
  }

  async getUserPasswordHash(userId: string): Promise<string | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select('password_hash')
      .eq('id', userId)
      .maybeSingle();

    if (error || !data) {
      return null;
    }

    return data.password_hash;
  }

  async incrementLoginAttempts(email: string): Promise<{ locked: boolean; remainingAttempts: number; lockDuration?: number; remainingSeconds?: number }> {
    const MAX_ATTEMPTS = 5;
    const LOCK_DURATION = 5; // minutes

    const emailHashToSearch = hashEmail(email);
    const { data: user } = await this.supabase
      .from('users')
      .select('*')
      .eq('email_hash', emailHashToSearch)
      .maybeSingle();

    if (!user) {
      return { locked: false, remainingAttempts: MAX_ATTEMPTS, remainingSeconds: 0 };
    }

    // Déchiffrer les données utilisateur (version asynchrone)
    const decryptedUser = await decryptUserDataAsync(user);

    // Vérifier si le compte est verrouillé
    if (decryptedUser.account_locked_until && new Date(decryptedUser.account_locked_until) > new Date()) {
      const remainingLockTime = Math.ceil((new Date(decryptedUser.account_locked_until).getTime() - new Date().getTime()) / 1000);
      return {
        locked: true,
        remainingAttempts: 0,
        lockDuration: LOCK_DURATION,
        remainingSeconds: remainingLockTime
      };
    }

    // Réinitialiser les tentatives si la dernière tentative date de plus de 30 minutes
    const resetAttempts = !decryptedUser.last_login_attempt ||
      (new Date().getTime() - new Date(decryptedUser.last_login_attempt).getTime()) > (30 * 60 * 1000);

    const attempts = resetAttempts ? 1 : decryptedUser.login_attempts + 1;
    const updates: Partial<User> = {
      login_attempts: attempts,
      last_login_attempt: new Date().toISOString()
    };

    // Verrouiller le compte si trop de tentatives
    if (attempts >= MAX_ATTEMPTS) {
      updates.account_locked_until = new Date(Date.now() + LOCK_DURATION * 60 * 1000).toISOString();
      updates.login_attempts = 0;
    }

    const emailHashToUpdate = hashEmail(email);
    await this.supabase
      .from('users')
      .update(updates)
      .eq('email_hash', emailHashToUpdate);

    if (attempts >= MAX_ATTEMPTS) {
      return {
        locked: true,
        remainingAttempts: 0,
        lockDuration: LOCK_DURATION,
        remainingSeconds: LOCK_DURATION * 60
      };
    }

    return {
      locked: false,
      remainingAttempts: MAX_ATTEMPTS - attempts,
      remainingSeconds: 0
    };
  }

  async resetLoginAttempts(email: string): Promise<void> {
    const emailHashToReset = hashEmail(email);
    await this.supabase
      .from('users')
      .update({
        login_attempts: 0,
        last_login_attempt: null,
        account_locked_until: null
      })
      .eq('email_hash', emailHashToReset);
  }

  async checkPasswordResetAttempts(userId: string): Promise<{ allowed: boolean; cooldownMinutes?: number }> {
    const MAX_ATTEMPTS = 3;
    const COOLDOWN_MINUTES = 30;
    const MIN_INTERVAL_MINUTES = 5;

    // Vérifier les tentatives existantes
    const { data: attempt } = await this.supabase
      .from('password_reset_attempts')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    // Vérifier s'il y a un cooldown actif
    if (attempt?.cooldown_until && new Date(attempt.cooldown_until) > new Date()) {
      const remainingMinutes = Math.ceil(
        (new Date(attempt.cooldown_until).getTime() - new Date().getTime()) / (1000 * 60)
      );
      return { allowed: false, cooldownMinutes: remainingMinutes };
    }

    // Vérifier la dernière demande de réinitialisation
    const { data: lastReset } = await this.supabase
      .from('password_resets')
      .select('created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (lastReset) {
      const minutesSinceLastReset = (new Date().getTime() - new Date(lastReset.created_at).getTime()) / (1000 * 60);
      if (minutesSinceLastReset < MIN_INTERVAL_MINUTES) {
        return { allowed: false, cooldownMinutes: Math.ceil(MIN_INTERVAL_MINUTES - minutesSinceLastReset) };
      }
    }

    // Réinitialiser le compteur si le dernier essai date de plus de 30 minutes
    const resetAttempts = !attempt?.last_attempt ||
      (new Date().getTime() - new Date(attempt.last_attempt).getTime()) > (COOLDOWN_MINUTES * 60 * 1000);

    if (resetAttempts) {
      await this.supabase
        .from('password_reset_attempts')
        .upsert({
          user_id: userId,
          attempt_count: 1,
          last_attempt: new Date().toISOString(),
          cooldown_until: null
        });
      return { allowed: true };
    }

    // Incrémenter le compteur
    const newAttemptCount = (attempt?.attempt_count || 0) + 1;
    const updates: Partial<PasswordResetAttempt> = {
      attempt_count: newAttemptCount,
      last_attempt: new Date().toISOString()
    };

    // Appliquer le cooldown si trop de tentatives
    if (newAttemptCount >= MAX_ATTEMPTS) {
      updates.cooldown_until = new Date(Date.now() + COOLDOWN_MINUTES * 60 * 1000).toISOString();
      updates.attempt_count = 0;
    }

    await this.supabase
      .from('password_reset_attempts')
      .upsert({
        user_id: userId,
        ...updates
      });

    if (newAttemptCount >= MAX_ATTEMPTS) {
      return { allowed: false, cooldownMinutes: COOLDOWN_MINUTES };
    }

    return { allowed: true };
  }

  async resetPasswordAttempts(userId: string): Promise<void> {
    await this.supabase
      .from('password_reset_attempts')
      .upsert({
        user_id: userId,
        attempt_count: 0,
        last_attempt: null,
        cooldown_until: null
      });
  }

  async cleanExpiredTokens(): Promise<void> {
    try {
      const now = new Date().toISOString();

      // Supprimer tous les tokens expirés de tous types
      const { error } = await this.supabase
        .from('auth_tokens')
        .delete()
        .lt('expires_at', now);

      if (error) {
        logger.error('Erreur lors du nettoyage des tokens expirés:', error);
      } else {
        logger.info('Nettoyage des tokens expirés terminé');
      }
    } catch (error) {
      logger.error('Erreur lors du nettoyage des tokens expirés:', error);
    }
  }

  async updateUser(userId: string, updateData: UpdateData) {
    try {
      logger.info('Début de updateUser', { userId, updateData });

      // Vérifier si l'utilisateur existe avant la mise à jour
      const existingUser = await this.getUserById(userId);
      if (!existingUser) {
        logger.error('Utilisateur non trouvé pour la mise à jour', { userId });
        throw new Error('Utilisateur non trouvé');
      }

      // Préparer les données de mise à jour avec chiffrement si nécessaire
      const finalUpdateData = { ...updateData };
      
      // Si l'email ou le téléphone sont mis à jour, les chiffrer
      if (updateData.email) {
        const dataToEncrypt: any = {};
        if (updateData.email) {
          dataToEncrypt.email = updateData.email;
          // Ajouter le hash de l'email pour la recherche efficace
          finalUpdateData.email_hash = hashEmail(updateData.email);
        }
        
        const encryptedData = await encryptUserDataAsync(dataToEncrypt);
        if (encryptedData.email) finalUpdateData.email = encryptedData.email;
      }

      const { data, error } = await this.supabase
        .from('users')
        .update(finalUpdateData)
        .eq('id', userId)
        .select(`
          id,
          email,
          user_type,
          profil_verifier,
          email_verifier,
          profil_actif,
          role,
          last_login
        `)
        .maybeSingle();

      if (error) {
        logger.error('Erreur Supabase lors de la mise à jour de l\'utilisateur:', { userId, error });
        throw error;
      }

      if (!data) {
        logger.error('Aucune donnée retournée après la mise à jour', { userId });
        throw new Error('Échec de la mise à jour');
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis 1 supprimé, updateUser :', { cacheKey });
      logger.info('Cache Redis 2 supprimé, updateUser :', { cacheKey_deux });

      // Déchiffrer les données avant de les retourner (version asynchrone)
      return await decryptUserDataAsync(data);
    } catch (error) {
      logger.error('Erreur critique lors de la mise à jour de l\'utilisateur:', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  async updateUserProfil(userId: string, profilData: {
    nom?: string;
    prenom?: string;
    telephone?: string;
    telephone_prive?: boolean;
    numero?: string;
    adresse?: string;
    ville?: string;
    code_postal?: string;
    pays?: string;
    bio?: string;
    photo_url?: string;
    mode_vacance?: boolean;
    intervention_zone?: {
      center: [number, number];
      radius: number;
    };
    type_de_profil?: string;
    nom_entreprise?: string;
    prenom_entreprise?: string;
    statut_entreprise?: string;
    siren_entreprise?: string;
    code_ape_entreprise?: string;
    categorie_entreprise?: string;
    effectif_entreprise?: string;
    date_insee_creation_entreprise?: string;
    date_categorie_entreprise?: string;
    date_derniere_mise_a_jour_entreprise_insee?: string;
    date_derniere_mise_a_jour_du_client_entreprise?: string;
    slug?: string;
  }): Promise<any> {
    try {
      logger.info('Début de la mise à jour du profil utilisateur', { userId });

      // Si le nom ou le prénom est modifié, mettre à jour le slug
      if (profilData.nom || profilData.prenom) {
        const { data: currentProfil } = await this.supabase
          .from('user_profil')
          .select('nom, prenom')
          .eq('user_id', userId)
          .maybeSingle();

        // Déchiffrer les données actuelles pour générer le slug (version asynchrone)
        const decryptedCurrentProfil = currentProfil ? await decryptProfilDataAsync(currentProfil) : null;

        const nom = profilData.nom || decryptedCurrentProfil?.nom;
        const prenom = profilData.prenom || decryptedCurrentProfil?.prenom;

        if (nom && prenom) {
          profilData.slug = await this.generateUniqueSlug(nom, prenom);
        }
      }

      // Vérifier si un profil existe déjà
      const { data: existingProfil, error: existingProfilError } = await this.supabaseClient
        .from('user_profil')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      if (existingProfilError && existingProfilError.code !== 'PGRST116') {
        logger.error('Erreur lors de la vérification du profil existant', {
          error: existingProfilError,
          userId
        });
        throw existingProfilError;
      }

      let result;

      if (existingProfil) {
        // Ne mettre à jour que les champs fournis
        const updateData: UpdateData = {};
        Object.entries(profilData).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            updateData[key] = value;
          }
        });

        // Chiffrer les données sensibles avant la mise à jour (version asynchrone)
        const encryptedUpdateData = await encryptProfilDataAsync(updateData);

        // Mise à jour du profil existant
        const { data, error } = await this.supabaseClient
          .from('user_profil')
          .update(encryptedUpdateData)
          .eq('user_id', userId)
          .select()
          .single();

        if (error) {
          logger.error('Erreur lors de la mise à jour du profil', {
            error,
            userId,
            profilData: updateData
          });
          throw error;
        }

        result = data;
        logger.info('Profil utilisateur mis à jour avec succès', { userId, updatedFields: Object.keys(updateData) });
      } else {
        // Création d'un nouveau profil avec les champs fournis
        const insertData = {
          ...profilData,
          user_id: userId
        };

        // Chiffrer les données sensibles avant l'insertion (version asynchrone)
        const encryptedInsertData = await encryptProfilDataAsync(insertData);

        const { data, error } = await this.supabaseClient
          .from('user_profil')
          .insert(encryptedInsertData)
          .select()
          .maybeSingle();

        if (error) {
          logger.error('Erreur lors de la création du profil', {
            error,
            userId,
            profilData: insertData
          });
          throw error;
        }

        result = data;
        logger.info('Nouveau profil utilisateur créé avec succès', { userId });
      }

      // Déchiffrer les données avant de les retourner (version asynchrone)
      return result ? await decryptProfilDataAsync(result) : result;
    } catch (error) {
      logger.error('Erreur lors de la mise à jour/création du profil utilisateur', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      throw error;
    }
  }

  // Vérification si on peut envoyer un nouvel email de vérification, réinitialisation ou notification de connexion
  async checkLastEmailSent(userId: string, emailType: 'email_verification' | 'password_reset' | 'newLogin'): Promise<{ canSendEmail: boolean; remainingSeconds?: number }> {
    try {
      // Récupérer l'email de l'utilisateur
      const user = await this.getUserById(userId);
      if (!user?.email) {
        logger.error(`Impossible de récupérer l'email pour l'utilisateur lors de la vérification du délai d'envoi.`, { userId });
        // Autoriser l'envoi si l'email n'est pas trouvé pour ne pas bloquer
        return { canSendEmail: true };
      }
      const userEmail = user.email;

      const cooldownMinutes = Number(process.env.CONFIG_ENVOI_EMAIL_VERIFICATION_PASSWORD_ET_MAIL || 3);
      const cooldownMilliseconds = cooldownMinutes * 60 * 1000;
      const cooldownAgo = new Date(Date.now() - cooldownMilliseconds);

      // Déterminer le sujet exact en fonction du type d'email
      let exactEmailSubject: string;
      if (emailType === 'email_verification') {
        exactEmailSubject = '🚀 Votre compte vous attend : vérifiez votre adresse email.';
      } else if (emailType === 'password_reset') {
        exactEmailSubject = '🔐 Réinitialisation de votre mot de passe';
      } else if (emailType === 'newLogin') {
        exactEmailSubject = '🔔 Une nouvelle connexion a été détectée sur votre compte.';
      } else {
        // Gérer les types d'email inconnus si nécessaire
        logger.warn(`Type d'email inconnu pour la vérification du délai d'envoi: ${emailType}`, { userId, userEmail });
        return { canSendEmail: true }; // Par défaut, autoriser si le type est inconnu
      }

      // Créer le hash de l'email pour la recherche (cohérent avec le système de recherche d'utilisateur)
      const emailHashToSearch = hashEmail(userEmail);

      // Rechercher dans email_queue en utilisant le hash de l'email
      const { data, error } = await this.supabase
        .from('email_queue')
        .select('created_at')
        .eq('email_hash', emailHashToSearch) // Utiliser le hash de l'email pour la recherche
        .eq('subject', exactEmailSubject) // Utiliser la correspondance exacte du sujet
        .eq('status', 'sent') // Uniquement les emails envoyés avec succès
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        logger.error(`Erreur lors de la vérification du dernier email (${emailType}) dans email_queue:`, { error, userId, userEmail, exactEmailSubject, emailHashToSearch });
        // En cas d'erreur de requête, autoriser l'envoi pour ne pas bloquer
        return { canSendEmail: true };
      }

      if (!data || data.length === 0) {
        // Aucun email de ce type trouvé pour cette adresse email
        logger.info(`Aucun email précédent (${emailType}) trouvé pour l'adresse email. Envoi autorisé.`, { userId, userEmail, exactEmailSubject, emailHashToSearch });
        return { canSendEmail: true };
      }

      const lastEmailDate = new Date(data[0].created_at);
      const canSendEmail = lastEmailDate < cooldownAgo;

      if (!canSendEmail) {
        const remainingMilliseconds = lastEmailDate.getTime() + cooldownMilliseconds - Date.now();
        const remainingSeconds = Math.ceil(remainingMilliseconds / 1000);

        logger.info(`Impossible d'envoyer un nouvel email (${emailType}) car le délai de ${cooldownMinutes} minutes n'est pas écoulé. Temps restant: ${remainingSeconds}s.`, { userId, userEmail, lastEmailDate: lastEmailDate.toISOString(), exactEmailSubject, emailHashToSearch });

        return { canSendEmail: false, remainingSeconds };
      } else {
        logger.info(`Envoi d'un nouvel email (${emailType}) autorisé. Dernier envoi: ${lastEmailDate.toISOString()}`, { userId, userEmail, exactEmailSubject, emailHashToSearch });
        return { canSendEmail: true };
      }
    } catch (error) {
      logger.error(`Exception lors de la vérification du dernier email (${emailType}) via email_queue:`, { error: error instanceof Error ? error.message : error, userId });
      // En cas d'erreur inattendue, autoriser l'envoi pour ne pas bloquer l'utilisateur
      return { canSendEmail: true };
    }
  }

  // Ajout de jobi a la base de données
  async createJobiEntrySupplement(userId: string, jobiData: { montant: number, titre: string, description: string }) {
    // Récupérer la valeur actuelle des Jobi
    const { data: currentJobiData, error: fetchError } = await this.supabaseClient
        .from('user_jobi')
        .select('montant')
        .eq('user_id', userId)
        .maybeSingle(); // Récupérer une seule entrée

    if (fetchError && fetchError.code !== 'PGRST116') { // Vérifier si l'erreur est due à l'absence de l'entrée
        logger.error('Erreur lors de la récupération des Jobi:', fetchError);
        throw new Error('Échec de la récupération des Jobi.');
    }

    // Si aucune entrée n'existe, créer une nouvelle entrée avec un montant initial
    const currentJobiAmount = currentJobiData?.montant || 0; // Si aucune valeur, utiliser 0
    const newJobiAmount = currentJobiAmount + jobiData.montant; // Ajouter le montant (positif ou négatif)

    // Vérifier que le solde ne devient pas négatif, pas activé pour le moment
    // if (newJobiAmount < 0) {
    //   throw new Error('Solde insuffisant');
    // }

    // Mettre à jour le solde
    const { data, error } = await this.supabaseClient
      .from('user_jobi')
      .update({ montant: newJobiAmount })
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
        logger.error('Erreur lors de l\'ajout de Jobi:', { error });
        throw new Error('Échec de l\'ajout de Jobi. Veuillez réessayer.');
    }

    // Ajouter l'entrée à l'historique des Jobi
    await this.supabaseClient
      .from('user_jobi_historique')
      .insert({
        user_id: userId,
        montant: jobiData.montant,
        titre: jobiData.titre,
        description: jobiData.description,
        date_creation: new Date() // Utiliser la date actuelle
      });

    return data;
  }

  async createLoginHistory(data: LoginHistoryData): Promise<void> {
    const { user_id, ip_address, city, country, region, postal_code } = data;
    await this.supabase
      .from('user_login_history')
      .insert([
        {
          user_id,
          ip_address,
          city,
          country,
          region,
          postal_code
        }
      ]);
  }

  async cleanLoginHistory(userId: string, keepLatest: number): Promise<void> {
    const { data: oldEntries } = await this.supabase
      .from('user_login_history')
      .select('id')
      .eq('user_id', userId)
      .order('login_date', { ascending: false })
      .range(keepLatest, 1000000);

    if (oldEntries && oldEntries.length > 0) {
      const idsToDelete = oldEntries.map((entry: LoginHistoryEntry) => entry.id);
      await this.supabase
        .from('user_login_history')
        .delete()
        .in('id', idsToDelete);
    }
  }

  // Fonctions pour l'authentification à deux facteurs
  async enableTwoFactorAuth(userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('users')
        .update({
          two_factor_enabled: true,
          two_factor_verified: false
        })
        .eq('id', userId);

      if (error) {
        logger.error('Erreur lors de l\'activation de l\'authentification à deux facteurs:', error);
        return false;
      }

      // Invalider le cache
      const cacheKey = `user_deux:${userId}`;
      await this.invalidateCache(cacheKey);

      return true;
    } catch (error) {
      logger.error('Erreur lors de l\'activation de l\'authentification à deux facteurs:', error);
      return false;
    }
  }

  async disableTwoFactorAuth(userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('users')
        .update({
          two_factor_enabled: false,
          two_factor_verified: false
        })
        .eq('id', userId);

      if (error) {
        logger.error('Erreur lors de la désactivation de l\'authentification à deux facteurs:', error);
        return false;
      }

      // Invalider le cache
      const cacheKey = `user_deux:${userId}`;
      await this.invalidateCache(cacheKey);

      return true;
    } catch (error) {
      logger.error('Erreur lors de la désactivation de l\'authentification à deux facteurs:', error);
      return false;
    }
  }

  async getUserTwoFactorStatus(userId: string): Promise<{ enabled: boolean; verified: boolean } | null> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('two_factor_enabled, two_factor_verified')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        logger.error('Erreur lors de la récupération du statut de l\'authentification à deux facteurs:', error);
        return null;
      }

      if (!data) {
        logger.warn('Utilisateur non trouvé lors de la récupération du statut 2FA:', { userId });
        return null;
      }

      // Déchiffrer les données utilisateur
      const decryptedData = await decryptUserDataAsync(data);

      return {
        enabled: decryptedData.two_factor_enabled || false,
        verified: decryptedData.two_factor_verified || false
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération du statut de l\'authentification à deux facteurs:', error);
      return null;
    }
  }

  async verifyTwoFactorAuth(userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('users')
        .update({
          two_factor_verified: true
        })
        .eq('id', userId);

      if (error) {
        logger.error('Erreur lors de la vérification de l\'authentification à deux facteurs:', error);
        return false;
      }

      // Invalider le cache
      const cacheKey = `user_deux:${userId}`;
      await this.invalidateCache(cacheKey);

      return true;
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'authentification à deux facteurs:', error);
      return false;
    }
  }

  async createTwoFactorToken(userId: string): Promise<{ token: string; twoFactorLink: string } | null> {
    try {
      // Générer un token unique à 6 chiffres
      const twoFactorCode = Math.floor(100000 + Math.random() * 900000).toString();

      // Définir l'expiration du token (10 minutes)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 10);

      // Récupérer l'URL frontend depuis l'environnement
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      // Créer le token dans la table auth_tokens
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('auth_tokens')
        .insert({
          user_id: userId,
          token: twoFactorCode,
          type: 'two_factor_auth',
          expires_at: expiresAt.toISOString()
        })
        .select()
        .maybeSingle();

      if (tokenError) {
        logger.error('Erreur lors de la création du token d\'authentification à deux facteurs:', tokenError);
        return null;
      }

      // Construire le lien de vérification
      const twoFactorLink = `${frontendUrl}/verify-two-factor?token=${twoFactorCode}`;

      return {
        token: twoFactorCode,
        twoFactorLink
      };
    } catch (error) {
      logger.error('Erreur lors de la création du token d\'authentification à deux facteurs:', error);
      return null;
    }
  }

  async verifyTwoFactorToken(token: string): Promise<{ user_id: string } | null> {
    try {
      logger.info('Token reçu pour vérification 2FA:', { token });

      // Récupérer le token de vérification
      const { data: tokenData, error: tokenError } = await this.supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', token)
        .eq('type', 'two_factor_auth')
        .maybeSingle();

      if (tokenError || !tokenData) {
        logger.warn('❌ Token 2FA non trouvé ou invalide', {
          token,
          error: tokenError
        });
        return null;
      }

      // Vérifier l'expiration du token
      const now = new Date();
      const expiresAt = new Date(tokenData.expires_at);

      if (now > expiresAt) {
        // Supprimer le token expiré
        await this.supabase
          .from('auth_tokens')
          .delete()
          .eq('token', token);

        logger.warn('❌ Token 2FA expiré', { token });
        return null;
      }

      // Supprimer le token après utilisation
      await this.supabase
        .from('auth_tokens')
        .delete()
        .eq('token', token);

      return { user_id: tokenData.user_id };
    } catch (error) {
      logger.error('Erreur lors de la vérification du token 2FA:', error);
      return null;
    }
  }

  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('two_factor_enabled')
        .eq('id', userId)
        .maybeSingle();

      if (error || !data) {
        logger.error('Erreur lors de la vérification de l\'état de l\'authentification à deux facteurs:', error);
        return false;
      }

      // Déchiffrer les données utilisateur
      const decryptedData = await decryptUserDataAsync(data);

      return decryptedData.two_factor_enabled || false;
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'état de l\'authentification à deux facteurs:', error);
      return false;
    }
  }

  async getUserByGoogleId(googleId: string) {
    const cacheKey = `google_user:${googleId}`;
    const cachedUser = await this.getFromCache<User>(cacheKey);

    if (cachedUser) {
      logger.info('Utilisateur Google récupéré depuis le cache', { googleId });
      return cachedUser;
    }

    try {
      logger.info('Récupération de l\'utilisateur par Google ID', { googleId });

      const { data, error } = await this.supabaseClient
        .from('users')
        .select('*')
        .eq('google_id', googleId)
        .maybeSingle();

      if (error) {
        if (error.code !== 'PGRST116') { // Code pour "aucun résultat"
          logger.error('Erreur lors de la récupération de l\'utilisateur par Google ID', {
            error: error.message,
            details: error.details,
            googleId
          });
        } else {
          logger.info('Aucun utilisateur trouvé avec ce Google ID', { googleId });
        }
        return null;
      }

      if (data) {
        logger.info('Utilisateur trouvé par Google ID', { userId: data.id, googleId });
        // Déchiffrer les données utilisateur avant de les mettre en cache et les retourner
        const decryptedData = await decryptUserDataAsync(data);
        await this.setInCache(cacheKey, decryptedData);
        return decryptedData as User;
      }

      return null;
    } catch (error) {
      logger.error('Exception lors de la récupération de l\'utilisateur par Google ID', {
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        stack: error instanceof Error ? error.stack : undefined,
        googleId
      });
      throw error;
    }
  }
}

export { DatabaseService };

export const dbService = DatabaseService.getInstance();