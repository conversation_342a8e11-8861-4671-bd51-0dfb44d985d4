<?php
session_start();
require_once 'config.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION[SESSION_KEY]) || $_SESSION[SESSION_KEY] !== true) {
    // Afficher une belle page de connexion au lieu d'un message d'erreur
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Connexion requise - JobPartiel</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .login-container {
                background: white;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 400px;
                text-align: center;
            }
            
            .logo {
                width: 120px;
                height: auto;
                margin-bottom: 30px;
            }
            
            h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }
            
            .subtitle {
                color: #666;
                margin-bottom: 30px;
                font-size: 14px;
            }
            
            .form-group {
                margin-bottom: 20px;
                text-align: left;
            }
            
            label {
                display: block;
                margin-bottom: 5px;
                color: #333;
                font-weight: 500;
            }
            
            input[type="text"],
            input[type="password"] {
                width: 100%;
                padding: 12px 15px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 16px;
                transition: border-color 0.3s ease;
            }
            
            input[type="text"]:focus,
            input[type="password"]:focus {
                outline: none;
                border-color: #667eea;
            }
            
            .btn-login {
                width: 100%;
                padding: 12px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.2s ease;
            }
            
            .btn-login:hover {
                transform: translateY(-2px);
            }
            
            .error {
                background: #fee;
                color: #c33;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 20px;
                border: 1px solid #fcc;
            }
            
            .warning {
                background: #fff3cd;
                color: #856404;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 1px solid #ffeaa7;
            }
            
            .footer {
                margin-top: 30px;
                color: #999;
                font-size: 12px;
            }
            
            .access-request {
                margin-top: 25px;
                padding-top: 20px;
                border-top: 1px solid #e1e5e9;
            }
            
            .request-text {
                color: #666;
                margin-bottom: 15px;
                font-size: 14px;
            }
            
            .btn-request {
                background: transparent;
                color: #667eea;
                border: 2px solid #667eea;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .btn-request:hover {
                background: #667eea;
                color: white;
                transform: translateY(-1px);
            }
            
            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 10px;
                margin-left: 10px;
                transition: all 0.3s ease;
            }
            
            .btn-secondary:hover {
                background: #5a6268;
                transform: translateY(-1px);
            }
            
            select, textarea {
                width: 100%;
                padding: 12px 15px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 16px;
                background: white;
                transition: border-color 0.3s ease;
            }
            
            select:focus, textarea:focus {
                border-color: #667eea;
                outline: none;
            }
            
            .checkbox-group {
                display: flex;
                align-items: flex-start;
                gap: 10px;
            }
            
            .checkbox-label {
                display: flex;
                align-items: flex-start;
                gap: 8px;
                font-size: 14px;
                line-height: 1.4;
                cursor: pointer;
            }
            
            input[type="checkbox"] {
                width: auto;
                margin: 0;
            }
            
            .success-icon {
                font-size: 48px;
                color: #28a745;
                margin-bottom: 20px;
            }
            
            .contact-info {
                margin-top: 15px;
                font-size: 14px;
            }
            
            .contact-info a {
                color: #667eea;
                text-decoration: none;
            }
            
            .contact-info a:hover {
                text-decoration: underline;
            }
        </style>
        <script>
            function showRequestForm() {
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('requestForm').style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            }
            
            function showLoginForm() {
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('requestForm').style.display = 'none';
                document.getElementById('successMessage').style.display = 'none';
            }
            
            function showSuccessMessage() {
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('requestForm').style.display = 'none';
                document.getElementById('successMessage').style.display = 'block';
            }
            
            document.addEventListener('DOMContentLoaded', function() {
                const accessRequestForm = document.getElementById('accessRequestForm');
                
                if (accessRequestForm) {
                    accessRequestForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // Récupération des données du formulaire
                        const formData = new FormData(accessRequestForm);
                        const data = Object.fromEntries(formData.entries());
                        
                        // Validation basique
                        if (!data.gdpr) {
                            alert('Vous devez accepter l\'utilisation de vos données pour continuer.');
                            return;
                        }
                        
                        // Envoi de la demande via AJAX
                        const button = e.target.querySelector('button[type="submit"]');
                        const originalText = button.textContent;
                        button.textContent = 'Envoi en cours...';
                        button.disabled = true;
                        
                        // Envoi des données au serveur
                        fetch('process_access_request.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(result => {
                            if (result.success) {
                                // Affichage du message de succès
                                showSuccessMessage();
                                // Reset du formulaire
                                accessRequestForm.reset();
                            } else {
                                // Affichage de l'erreur
                                alert('Erreur: ' + result.message);
                            }
                        })
                        .catch(error => {
                            alert('Une erreur technique est survenue. Veuillez réessayer ou nous contacter directement à <EMAIL>');
                        })
                        .finally(() => {
                            // Restauration du bouton
                            button.textContent = originalText;
                            button.disabled = false;
                        });
                    });
                }
            });
        </script>
    </head>
    <body>
        <div class="login-container">
            <h1>🔒 Accès Restreint</h1>
            <p class="subtitle">Connexion requise pour télécharger ce fichier</p>
            
            <div class="warning">
                <strong>⚠️ Attention :</strong> Vous devez être connecté pour accéder à ce contenu réservé aux investisseurs.
            </div>
            
            <div id="loginForm">
                <form method="POST" action="login.php">
                    <div class="form-group">
                        <label for="username">Nom d'utilisateur :</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Mot de passe :</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
                    
                    <button type="submit" class="btn-login">Se connecter</button>
                </form>
                
                <div class="access-request">
                    <p class="request-text">Vous n'avez pas d'accès ?</p>
                    <button type="button" class="btn-request" onclick="showRequestForm()">Demander un accès</button>
                </div>
            </div>
            
            <div id="requestForm" style="display: none;">
                <h2>Demande d'accès investisseur</h2>
                <p class="subtitle">Remplissez ce formulaire pour obtenir vos identifiants</p>
                
                <form id="accessRequestForm">
                    <div class="form-group">
                        <label for="fullName">Nom complet *</label>
                        <input type="text" id="fullName" name="fullName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email professionnel *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="company">Société/Fonds d'investissement *</label>
                        <input type="text" id="company" name="company" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="position">Fonction *</label>
                        <input type="text" id="position" name="position" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Téléphone</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="investmentType">Type d'investissement recherché *</label>
                        <select id="investmentType" name="investmentType" required>
                            <option value="">Sélectionnez...</option>
                            <option value="seed">Amorçage (Seed)</option>
                            <option value="series-a">Série A</option>
                            <option value="series-b">Série B+</option>
                            <option value="private-equity">Private Equity</option>
                            <option value="venture-capital">Venture Capital</option>
                            <option value="business-angel">Business Angel</option>
                            <option value="other">Autre</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="investmentRange">Montant d'investissement typique *</label>
                        <select id="investmentRange" name="investmentRange" required>
                            <option value="">Sélectionnez...</option>
                            <option value="0-50k">0 - 50k €</option>
                            <option value="50k-250k">50k - 250k €</option>
                            <option value="250k-1m">250k - 1M €</option>
                            <option value="1m-5m">1M - 5M €</option>
                            <option value="5m+">5M+ €</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Message (motivations, questions...) *</label>
                        <textarea id="message" name="message" rows="4" required placeholder="Décrivez brièvement votre intérêt pour JobPartiel et vos motivations d'investissement..."></textarea>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="gdpr" name="gdpr" required>
                            <span class="checkmark"></span>
                            J'accepte que mes données soient utilisées pour traiter ma demande d'accès *
                        </label>
                    </div>
                    
                    <button type="submit" class="btn-login">Envoyer la demande</button>
                    <button type="button" class="btn-secondary" onclick="showLoginForm()">Retour à la connexion</button>
                </form>
            </div>
            
            <div id="successMessage" style="display: none;">
                <div class="success-icon">✓</div>
                <h2>Demande envoyée avec succès !</h2>
                <p>Votre demande d'accès a été transmise à notre équipe. Vous recevrez une réponse sous 24-48h ouvrées.</p>
                <p class="contact-info">Pour toute urgence : <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <button type="button" class="btn-secondary" onclick="showLoginForm()">Retour à la connexion</button>
            </div>
            
            <div class="footer">
                <p>JobPartiel - Espace Investisseurs</p>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Vérifier si un fichier est demandé
if (!isset($_GET['file'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('Fichier non spécifié');
}

$filename = $_GET['file'];

// Liste des fichiers autorisés au téléchargement
$allowed_files = [
    'Pitch Deck.pdf',
    'Pitch Deck.pptx',
    'JobPartiel_Executive_Summary.html',
    'JobPartiel_Business_Plan.html',
    'JobPartiel_Plan_Financier.html',
    'JobPartiel_Equipe_Fondatrice.html',
    'JobPartiel_Statuts_Juridiques.html',
    'README.md',
    'JobPartiel_Dossier_Complet.zip'
];

// Vérifier si le fichier est autorisé
if (!in_array($filename, $allowed_files)) {
    header('HTTP/1.0 403 Forbidden');
    exit('Fichier non autorisé');
}

// Chemin complet du fichier
$filepath = __DIR__ . '/' . $filename;

// Vérifier si le fichier existe
if (!file_exists($filepath)) {
    header('HTTP/1.0 404 Not Found');
    exit('Fichier non trouvé');
}

// Déterminer le type MIME
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mime_type = finfo_file($finfo, $filepath);
finfo_close($finfo);

// Headers pour le téléchargement
header('Content-Type: ' . $mime_type);
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Envoyer le fichier
readfile($filepath);
exit;
?>