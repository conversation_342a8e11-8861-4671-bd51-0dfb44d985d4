import React from 'react';
import { Chip } from '@mui/material';

interface TicketStatusBadgeProps {
  status: string;
}

const statusConfig: Record<string, { label: string; color: string; bgColor: string; borderColor: string }> = {
  nouveau: {
    label: 'Nouveau',
    color: '#1976D2',
    bgColor: 'rgba(33, 150, 243, 0.15)',
    borderColor: 'rgba(33, 150, 243, 0.3)',
  },
  en_attente: {
    label: 'En attente',
    color: '#ED6C02',
    bgColor: 'rgba(255, 152, 0, 0.15)',
    borderColor: 'rgba(255, 152, 0, 0.3)',
  },
  en_cours: {
    label: 'En cours',
    color: '#2E7D32',
    bgColor: 'rgba(76, 175, 80, 0.15)',
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  resolu: {
    label: 'Résolu',
    color: '#689F38',
    bgColor: 'rgba(139, 195, 74, 0.15)',
    borderColor: 'rgba(139, 195, 74, 0.3)',
  },
  ferme: {
    label: 'Fermé',
    color: '#757575',
    bgColor: 'rgba(158, 158, 158, 0.15)',
    borderColor: 'rgba(158, 158, 158, 0.3)',
  },
  reouvert: {
    label: 'Réouvert',
    color: '#C62828',
    bgColor: 'rgba(244, 67, 54, 0.15)',
    borderColor: 'rgba(244, 67, 54, 0.3)',
  },
};

const TicketStatusBadge: React.FC<TicketStatusBadgeProps> = ({ status }) => {
  const config = statusConfig[status] || {
    label: 'Inconnu',
    color: '#757575',
    bgColor: 'rgba(158, 158, 158, 0.15)',
    borderColor: 'rgba(158, 158, 158, 0.3)',
  };

  return (
    <Chip
      label={config.label}
      size="small"
      sx={{
        backgroundColor: config.bgColor,
        color: config.color,
        fontWeight: 600,
        height: '26px',
        fontSize: '0.75rem',
        border: `1px solid ${config.borderColor}`,
        borderLeft: `3px solid ${config.color}`,
        borderRadius: '6px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        '& .MuiChip-label': {
          px: 1.2,
        },
        transition: 'all 0.2s ease',
        '&:hover': {
          boxShadow: `0 2px 4px ${config.borderColor}`,
        }
      }}
    />
  );
};

export default TicketStatusBadge; 