import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import DOMPurify from 'dompurify';
import {
  Box,
  Typography,
  Avatar,
  IconButton,
  Button,
  CircularProgress,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Paper,
  styled,
  useMediaQuery,
  TextField
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Block as BlockIcon,
  Delete as DeleteIcon,
  GetApp as DownloadIcon,
  MarkEmailUnread as MarkUnreadIcon,
  PictureAsPdf as PictureAsPdfIcon,
  InsertPhoto as InsertPhotoIcon,
  Description as DescriptionIcon,
  ZoomIn as ZoomInIcon,
  Close as CloseIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Collections as CollectionsIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Flag as FlagIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient, useInfiniteQuery, InfiniteData, QueryFunctionContext } from '@tanstack/react-query';
import { api } from '../../../services/api';
import { Message, Conversation, MessageAttachment } from '../types';
import { formatDistanceToNow, format } from 'date-fns';
import { useDropzone } from 'react-dropzone';
import { notify } from '../../../components/Notification';
import { useSocket } from '../../../contexts/SocketContext';
import { motion, AnimatePresence } from 'framer-motion';
import { fr } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import UserProfileModal from '../../../components/UserProfileModal';
import OnlineStatusDot from '../../../components/OnlineStatusDot';
import ModalPortal from '../../../components/ModalPortal';
import { useMessageSound } from '../../../hooks/useMessageSound';
import TiptapEditor, { TiptapInstance } from '../../../components/TiptapEditor'
import { logger } from '@/utils/logger';
import { fetchCsrfToken } from '@/services/csrf';
import { useImageCompression } from '../../../utils/imageCompressor';
import { API_URL } from '../../../config/api';


// Fonction pour convertir le HTML en texte brut (nécessaire pour le compteur)
const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};


// Types pour le composant Skeleton
interface SkeletonProps {
  variant: 'circular' | 'text' | 'rectangular';
  width: number | string;
  height: number | string;
  sx?: any;
}

// Helpers
const Skeleton: React.FC<SkeletonProps> = ({ variant, width, height, sx }) => (
  <Box
    sx={{
      width,
      height: variant === 'circular' ? width : height,
      borderRadius: variant === 'circular' ? '50%' : '4px',
      animation: 'pulse 1.5s infinite ease-in-out',
      ...sx,
      '@keyframes pulse': {
        '0%': { opacity: 0.6 },
        '50%': { opacity: 0.3 },
        '100%': { opacity: 0.6 },
      },
    }}
  />
);

// Fonction pour supprimer les accents et normaliser les noms de fichiers
const normalizeFileName = (fileName: string): string => {
  // Supprimer les accents (normalisation Unicode + remplacement des caractères décomposés)
  const normalized = fileName
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Remplacer les espaces et caractères spéciaux par des underscores
    .replace(/[^a-zA-Z0-9.]/g, '_')
    // Éviter les underscores multiples consécutifs
    .replace(/_+/g, '_');

  // Extraire l'extension pour la préserver
  const lastDotIndex = normalized.lastIndexOf('.');
  if (lastDotIndex === -1) return normalized;

  const nameWithoutExt = normalized.substring(0, lastDotIndex);
  const extension = normalized.substring(lastDotIndex);

  // Tronquer le nom si nécessaire (longueur max totale de 100 caractères)
  const maxFileNameLength = 100;
  if (nameWithoutExt.length + extension.length > maxFileNameLength) {
    return nameWithoutExt.substring(0, maxFileNameLength - extension.length) + extension;
  }

  return normalized;
};

interface HeaderBoxProps {
  isExpanded?: boolean;
}

// Composants stylisés
const HeaderBox = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isExpanded'
})<HeaderBoxProps>(() => ({
  padding: '16px',
  borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  background: 'linear-gradient(to right, rgba(255, 248, 243, 0.6), rgba(255, 248, 243, 0.9))',
  backdropFilter: 'blur(10px)',
  borderTopLeftRadius: '12px',
  borderTopRightRadius: '12px',
  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.05)',
  flexWrap: 'nowrap',
  gap: '8px',
  '@media (max-width: 600px)': {
    padding: '12px',
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  '@media (max-width: 400px)': {
    padding: '10px',
  },
}));

const HeaderMain = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '8px',
}));

const HeaderActions = styled(motion.div)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  '@media (max-width: 600px)': {
    display: 'none',
    padding: '8px 0 0 0',
    justifyContent: 'center',
    borderTop: '1px solid rgba(255, 107, 44, 0.1)',
    marginTop: '8px',
    '&.expanded': {
      display: 'flex'
    }
  }
}));

const MessageInputBox = styled(Box)(() => ({
  padding: '16px',
  borderTop: '1px solid rgba(255, 107, 44, 0.1)',
  background: 'linear-gradient(to right, #FFF8F3, rgba(255, 248, 243, 0.9))',
  backdropFilter: 'blur(10px)',
  display: 'flex',
  alignItems: 'flex-start', // Changer pour aligner avec le haut quand l'éditeur est grand
  minHeight: 'auto', // Hauteur minimale auto
}));


const MessagesContainer = styled(Box)(() => ({
  flexGrow: 1,
  overflowY: 'auto',
  overflowX: 'hidden',
  // padding: '16px',
  gap: '0px',
  backgroundColor: '#FFFFFF',
  scrollBehavior: 'smooth',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#FF7A35',
    borderRadius: '10px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '#FF965E',
  },
  '.message-item': {
    transition: 'background-color 2s ease-in-out',
    paddingTop: '16px',
    padding: '16px 10px 0px 10px',
  },
  '.new-message-highlight': {
    backgroundColor: '#FFE4BA',
    padding: '16px 10px 0px 10px'
    // borderRadius: '4px',
  },

}));

const AttachmentPreview = styled(Paper)(() => ({
  padding: '8px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  backgroundColor: 'rgba(255, 228, 186, 0.5)',
  borderRadius: '8px',
  marginBottom: '8px',
  borderLeft: '3px solid #FF6B2C',
}));

const MessageTimeInfo = styled(Box)(() => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-end',
  gap: '4px',
  fontSize: '0.7rem',
  color: 'rgba(0, 0, 0, 0.6)',
  marginLeft: '4px',
  width: '100%', // Prend toute la largeur disponible
  marginTop: '2px' // Petit espace entre la bulle et les informations de temps
}));

const ReadIndicator = styled('span')(() => ({
  fontSize: '0.65rem',
  display: 'inline-flex',
  alignItems: 'center',
  marginLeft: '4px',
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
}));

interface ConversationViewProps {
  conversationId: string;
  userId: string;
}

interface MessagesResponse {
  messages: Message[];
  nextPage?: number;
}

interface InfiniteMessagesData {
  pages: MessagesResponse[];
  pageParams: number[];
}

interface AttachmentFile {
  name: string;
  size: number;
  type: string;
  path?: string;
  relativePath?: string;
  lastModified?: number;
  webkitRelativePath?: string;
  originalFile?: File;
}

// Interface pour la prévisualisation
interface PreviewState {
  open: boolean;
  url: string;
  type: string;
  name: string;
  currentIndex?: number;
}

// Interface pour le composant PreviewCarousel
interface PreviewCarouselProps {
  attachments: MessageAttachment[];
  initialIndex: number;
  onClose: () => void;
}

// Composants stylisés pour la prévisualisation
const PreviewContainer = styled(motion.div)({
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'white',
  borderRadius: '12px',
  overflow: 'hidden',
  maxWidth: '1080px',
  maxHeight: 'calc(100vh - 48px)',
  width: '90vw',
  height: '90vh',
  position: 'relative',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
  margin: 'auto'
});

const PreviewHeader = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '16px 20px',
  backgroundColor: '#fff8f3',
  borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.04)'
});

const PreviewContent = styled('div')({
  display: 'flex',
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative',
  backgroundColor: '#f5f5f5',
  overflow: 'hidden',
  '& img': {
    maxWidth: '100%',
    maxHeight: '100%',
    objectFit: 'contain',
    transition: 'transform 0.3s ease'
  },
  '& iframe': {
    width: '100%',
    height: '100%',
    border: 'none'
  }
});

const PreviewFooter = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  padding: '16px 20px',
  backgroundColor: '#fff8f3',
  borderTop: '1px solid rgba(0, 0, 0, 0.08)',
  boxShadow: '0 -2px 4px rgba(0, 0, 0, 0.04)',
  flexDirection: 'column',
  gap: '12px',
  '@media (min-width: 600px)': {
    flexDirection: 'row',
    alignItems: 'center'
  }
});

const NavButton = styled(IconButton)({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  backgroundColor: 'rgba(255, 255, 255, 0.8) !important',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
  zIndex: 10,
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.95) !important',
    transform: 'translateY(-50%) scale(1.05)'
  },
  '&.left': {
    left: '16px'
  },
  '&.right': {
    right: '16px'
  },
  '& svg': {
    color: '#FF6B2C',
    fontSize: '28px'
  }
});

const CarouselIndicator = styled('div')({
  position: 'absolute',
  bottom: '16px',
  left: '50%',
  transform: 'translateX(-50%)',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  color: 'white',
  padding: '4px 12px',
  borderRadius: '20px',
  fontSize: '14px',
  fontWeight: 500,
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  zIndex: 10
});

// Fonction pour déterminer l'icône en fonction du type de fichier
const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) {
    return <InsertPhotoIcon fontSize="large" />;
  } else if (mimeType === 'application/pdf') {
    return <PictureAsPdfIcon fontSize="large" color="error" />;
  } else {
    return <DescriptionIcon fontSize="large" />;
  }
};

const PreviewCarousel: React.FC<PreviewCarouselProps> = ({ attachments, initialIndex, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const currentAttachment = attachments[currentIndex];
  const isMobile = useMediaQuery('(max-width:600px)');
  const [isImageLoading, setIsImageLoading] = useState(true);

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setIsImageLoading(true);
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < attachments.length - 1) {
      setIsImageLoading(true);
      setCurrentIndex(currentIndex + 1);
    }
  };

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentIndex < attachments.length - 1) {
      handleNext();
    }

    if (isRightSwipe && currentIndex > 0) {
      handlePrevious();
    }
  };

  // useEffect(() => {
  //   const handleKeyDown = (e: KeyboardEvent) => {
  //     if (e.key === 'ArrowLeft') {
  //       handlePrevious();
  //     } else if (e.key === 'ArrowRight') {
  //       handleNext();
  //     } else if (e.key === 'Escape') {
  //       onClose();
  //     }
  //   };

  //   window.addEventListener('keydown', handleKeyDown);
  //   return () => {
  //     window.removeEventListener('keydown', handleKeyDown);
  //   };
  // }, [currentIndex, attachments.length]);

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(2) + ' MB';
    else return (bytes / 1073741824).toFixed(2) + ' GB';
  };

  const renderContent = () => {
    if (!currentAttachment) return null;

    if (currentAttachment.mime_type?.startsWith('image/')) {
      return (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isImageLoading ? 0 : 1 }}
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5'
          }}
        >
          <img
            src={`https://api.jobpartiel.fr/api/storage-proxy/message_attachments/${currentAttachment.storage_path}`}
            alt={currentAttachment.file_name}
            onLoad={() => setIsImageLoading(false)}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              transition: 'transform 0.3s ease',
            }}
          />
          {isImageLoading && (
            <CircularProgress
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#FF6B2C'
              }}
            />
          )}
        </motion.div>
      );
    } else if (currentAttachment.mime_type === 'application/pdf') {
      return (
        <iframe
          src={`https://api.jobpartiel.fr/api/storage-proxy/message_attachments/${currentAttachment.storage_path}#toolbar=0`}
          title={currentAttachment.file_name}
          style={{ width: '100%', height: '100%', border: 'none' }}
        />
      );
    } else {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            p: 4,
            backgroundColor: '#fff8f3',
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}
        >
          <Box sx={{ fontSize: 100, color: '#FF6B2C', mb: 2 }}>
            {getFileIcon(currentAttachment.mime_type || '')}
          </Box>
          <Typography variant="h6" gutterBottom>
            {currentAttachment.file_name}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {formatFileSize(currentAttachment.file_size || 0)}
          </Typography>
          <Button
            variant="contained"
            component="a"
            href={`https://api.jobpartiel.fr/api/storage-proxy/message_attachments/${currentAttachment.storage_path}`}
            download
            target="_blank"
            sx={{
              mt: 2,
              backgroundColor: '#FF6B2C',
              '&:hover': { backgroundColor: '#FF7A35' }
            }}
            startIcon={<DownloadIcon />}
          >
            Télécharger
          </Button>
        </Box>
      );
    }
  };

  // Vérification de sécurité pour éviter l'erreur currentAttachment undefined
  if (!currentAttachment) {
    return null;
  }

  return (
    <PreviewContainer
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      <PreviewHeader>
        <Typography variant="subtitle1" sx={{
          fontWeight: 500,
          color: '#333',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          {currentAttachment.mime_type?.startsWith('image/') ? (
            <InsertPhotoIcon sx={{ color: '#FF6B2C' }} />
          ) : currentAttachment.mime_type === 'application/pdf' ? (
            <PictureAsPdfIcon sx={{ color: 'error.main' }} />
          ) : (
            <DescriptionIcon />
          )}
          {currentAttachment.file_name}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: '#555' }}>
          <CloseIcon />
        </IconButton>
      </PreviewHeader>

      <PreviewContent
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {renderContent()}

        {attachments.length > 1 && (
          <>
            <NavButton
              className="left"
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              sx={{ opacity: currentIndex === 0 ? 0.4 : 1 }}
            >
              <ChevronLeftIcon />
            </NavButton>

            <NavButton
              className="right"
              onClick={handleNext}
              disabled={currentIndex === attachments.length - 1}
              sx={{ opacity: currentIndex === attachments.length - 1 ? 0.4 : 1 }}
            >
              <ChevronRightIcon />
            </NavButton>

            <CarouselIndicator>
              <CollectionsIcon sx={{ fontSize: 16 }} />
              {currentIndex + 1}/{attachments.length}
            </CarouselIndicator>
          </>
        )}
      </PreviewContent>

      <PreviewFooter>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          width: '100%',
          overflow: 'hidden'
        }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              color: '#555',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: { xs: '180px', sm: '300px' }
            }}
          >
            {currentAttachment.file_name}
          </Typography>
          <Typography variant="caption" sx={{ color: '#777', flexShrink: 0 }}>
            ({formatFileSize(currentAttachment.file_size || 0)})
          </Typography>
        </Box>
        <Box sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: 2,
          width: '100%'
        }}>
          <Button
            component="a"
            href={currentAttachment.storage_path ? `https://api.jobpartiel.fr/api/storage-proxy/message_attachments/${currentAttachment.storage_path}` : '#'}
            download={currentAttachment.file_name}
            fullWidth
            disabled={!currentAttachment.storage_path}
            sx={{
              bgcolor: '#FF6B2C',
              color: 'white',
              '&:hover': { bgcolor: '#FF7A35' },
              '&:disabled': {
                bgcolor: '#ccc',
                color: '#666'
              },
              whiteSpace: 'nowrap',
              transition: 'transform 0.2s ease',
              '&:active': {
                transform: 'scale(0.96)'
              },
              flex: 1
            }}
          >
            Télécharger
          </Button>
          <Button
            onClick={onClose}
            variant="outlined"
            size={isMobile ? "small" : "medium"}
            fullWidth
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                bgcolor: 'rgba(255, 107, 44, 0.05)',
                borderColor: '#FF7A35'
              },
              whiteSpace: 'nowrap',
              transition: 'transform 0.2s ease',
              '&:active': {
                transform: 'scale(0.96)'
              },
              flex: 1
            }}
          >
            Fermer
          </Button>
        </Box>
      </PreviewFooter>
    </PreviewContainer>
  );
};

const ConversationView: React.FC<ConversationViewProps> = ({ conversationId, userId }) => {
  // TOUS les hooks doivent être ici, AVANT tout return ou if
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [reportMessageId, setReportMessageId] = useState<string | null>(null);
  const [reportReason, setReportReason] = useState('');
  const [reportLoading, setReportLoading] = useState(false);
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [newMessage, setNewMessage] = useState('');
  const [attachments, setAttachments] = useState<AttachmentFile[]>([]);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [newMessageIds, setNewMessageIds] = useState<Set<string>>(new Set());
  const [messageOrderMap, setMessageOrderMap] = useState(() => new Map<string, number>());
  const { socket } = useSocket();
  const navigate = useNavigate();
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);
  const [selectedUserProfile, setSelectedUserProfile] = useState<any>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isUnhideDialogOpen, setIsUnhideDialogOpen] = useState(false);
  const { isSoundEnabled, toggleSound } = useMessageSound();
  const [highlightedMessageIds, setHighlightedMessageIds] = useState<Set<string>>(new Set());
  const editorRef = useRef<TiptapInstance>(null);
  const [isHeaderActionsExpanded, setIsHeaderActionsExpanded] = useState(false);
  const isMobile = useMediaQuery('(max-width:600px)');
  const messageTextLength = useMemo(() => stripHtml(newMessage).length, [newMessage]);
  const socketHandlersRef = useRef<{
    messageDeleted: (payload: any) => void;
    messageRead: (payload: any) => void;
    conversationBlocked: (payload: any) => void;
  } | null>(null);
  const [preview, setPreview] = useState<PreviewState>({ open: false, url: '', type: '', name: '' });
  const { compressGalleryPhoto } = useImageCompression();
  
  // Fonction utilitaire de scroll
  const scrollToBottom = (behavior: ScrollBehavior = 'auto') => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      const targetScrollTop = container.scrollHeight;
      
      // Utiliser scrollTo sans condition
      container.scrollTo({
        top: targetScrollTop,
        behavior
      });
    }
  };

  // Récupération de la conversation
  const { data: conversation, isLoading: isLoadingConversation, error: conversationError } = useQuery<Conversation>({
    queryKey: ['conversation', conversationId],
    queryFn: async () => {
      try {
        const response = await api.get(`/api/messages/${conversationId}`);
        if (response.data.success && response.data.data) {
          return response.data.data;
        }
        throw new Error('Format de réponse inattendu pour la conversation');
      } catch (error) {
        logger.info('Erreur lors de la récupération de la conversation:', error);
        throw error;
      }
    },
    enabled: !!conversationId && !!userId,
    retry: 1,
    staleTime: 60000, // 1 minute
    gcTime: 3600000, // 1 heure // Remplacer cacheTime par gcTime
    refetchOnWindowFocus: false
  });

  // Récupération des messages avec pagination
  const { data: messagesData, isLoading: isLoadingMessages, error: messagesError, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery<MessagesResponse, Error, InfiniteData<MessagesResponse>, readonly [string, string], number>({ // Ajout des types génériques corrects
    queryKey: ['messages', conversationId],
    queryFn: async ({ pageParam }: QueryFunctionContext<readonly [string, string], number>) => { // Utiliser QueryFunctionContext et typer pageParam
      try {
        const response = await api.get(`/api/messages/${conversationId}/messages`, {
          params: {
            page: pageParam,
            limit: 20
          }
        });
        if (response.data.success && response.data.data) {
          return {
            messages: response.data.data.messages,
            nextPage: response.data.data.messages.length === 20 ? pageParam + 1 : undefined
          };
        }
        throw new Error('Format de réponse invalide');
      } catch (error) {
        logger.info('Erreur lors de la récupération des messages:', error);
        throw error;
      }
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1, // Ajouter initialPageParam
    enabled: !!conversationId,
    staleTime: 0, // Réduire à 0 pour forcer la vérification des nouveaux messages
    gcTime: 3600000, // 1 heure // Remplacer cacheTime par gcTime
    refetchOnMount: true, // Changer à true pour recharger quand ConversationView se monte
    refetchOnWindowFocus: false,
    refetchOnReconnect: false
  });

  // Mutation pour marquer les messages comme lus
  const markAsReadMutation = useMutation({
    mutationFn: async () => {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post(`/api/messages/${conversationId}/read`, {}, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      return response.data;
    },
    onSuccess: (data) => {
      // Mise à jour optimiste du cache au lieu d'invalider
      if (data && data.messages) {
        queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
          if (!oldData) return oldData;

          const newData = JSON.parse(JSON.stringify(oldData));
          if (newData.pages) {
            newData.pages.forEach((page: any) => {
              if (page.messages) {
                page.messages.forEach((msg: any) => {
                  const matchingMessage = data.messages.find((m: any) => m.id === msg.id);
                  if (matchingMessage) {
                    msg.read_at = matchingMessage.read_at;
                    msg.is_read = !!matchingMessage.read_at; // Mettre à jour is_read en fonction de read_at
                  }
                });
              }

            });

          }
          return newData;
        });

        // Mettre à jour le compteur de non lus dans la liste des conversations
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData?.conversations) return oldData;

          const newData = JSON.parse(JSON.stringify(oldData));
          const conversation = newData.conversations.find(
            (conv: any) => conv.id === conversationId
          );

          if (conversation) {
            conversation.unread_count = 0;
          }

          return newData;
        });
      }
    }
  });

  // Ajouter un message à l'ordre de référence avec un nombre croissant
  const addMessageToOrder = useCallback((messageId: string) => {
    setMessageOrderMap(prev => {
      const newMap = new Map(prev);
      // Utiliser la taille actuelle + 1 comme prochain index
      newMap.set(messageId, newMap.size + 1);
      return newMap;
    });
  }, []);

  // Aplatir les messages de toutes les pages
  const messages = useMemo(() => {
    if (!messagesData) return [];

    // Extraction et dédoublonnage des messages
    const allMessages = (messagesData.pages || []) // Ajouter un fallback au cas où pages est undefined
      .flatMap((page: MessagesResponse) => page.messages || []) // Typer page et ajouter un fallback
      .filter((message, index, self) =>
        // Supprimer les doublons basés sur l'ID du message
        index === self.findIndex((m) => m.id === message.id)
      );

    // S'assurer que tous les messages ont une date valide
    allMessages.forEach(message => {
      if (!message.created_at || !(message.created_at instanceof Date)) {
        if (typeof message.created_at === 'string') {
          // Convertir la chaîne en Date
          message.created_at = new Date(message.created_at);
        } else {
          // Créer une nouvelle date
          message.created_at = new Date();
        }
      }
    });

    // Créer une copie pour le tri sans modifier l'original
    return [...allMessages].sort((a, b) => {
      // Récupérer l'ordre des messages s'il existe (0 sinon)
      const orderA = messageOrderMap.get(a.id) || 0;
      const orderB = messageOrderMap.get(b.id) || 0;

      // Si les deux messages sont récents (ont un ordre enregistré)
      if (orderA > 0 && orderB > 0) {
        // Tri selon leur ordre d'envoi
        return orderA - orderB;
      }

      // Sinon, tri par date comme avant
      const timeA = a.created_at.getTime();
      const timeB = b.created_at.getTime();

      if (timeA === timeB) {
        return a.id.localeCompare(b.id);
      }

      return timeA - timeB;
    });
  }, [messagesData, messageOrderMap]);

  // Initialiser les handlers une seule fois
  useEffect(() => {
    // Définir les handlers dans la référence
    socketHandlersRef.current = {
      // Supprimer la gestion de newMessage car elle est déjà gérée dans MessagesPage
      // newMessage: handleNewMessage,

      messageDeleted: (payload: {
        conversation_id: string;
        message_id: string;
        deleted_content: string;
        is_deleted_attachment: boolean;
      }) => {
        logger.info("✅ Message supprimé reçu via socket:", payload);
        if (payload.conversation_id === conversationId) {
          // Mettre à jour le cache pour refléter la suppression
          queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
            if (!oldData) return oldData;

            const newData = JSON.parse(JSON.stringify(oldData));
            if (newData.pages) {
              newData.pages.forEach((page: any) => {
                if (page.messages) {
                  page.messages = page.messages.map((msg: any) => {
                    if (msg.id === payload.message_id) {
                      return {
                        ...msg,
                        content: payload.deleted_content,
                        is_deleted: true,
                        is_deleted_attachment: payload.is_deleted_attachment
                      };
                    }
                    return msg;
                  });
                }
              });
            }
            return newData;
          });
        }
      },

      messageRead: (payload: {
        conversation_id: string,
        messages: Array<{ id: string, read_at: string }>
      }) => {
        logger.info('✅ Message lu reçu via socket:', payload);
        if (payload.conversation_id === conversationId) {
          // Au lieu d'invalider le cache, mettre à jour les données directement
          queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
            if (!oldData) return oldData;

            const newData = JSON.parse(JSON.stringify(oldData));
            if (newData.pages) {
              newData.pages.forEach((page: any) => {
                if (page.messages) {
                  page.messages.forEach((msg: any) => {
                    const matchingMessage = payload.messages.find(m => m.id === msg.id);
                    if (matchingMessage) {
                      msg.read_at = matchingMessage.read_at;
                      msg.is_read = !!matchingMessage.read_at; // Mettre à jour is_read en fonction de read_at
                    }
                  });
                }
              });
            }
            return newData;
          });

          // Mettre à jour le cache des conversations plus subtilement
          queryClient.setQueryData(['conversations'], (oldData: any) => {
            if (!oldData?.conversations) return oldData;

            const newData = JSON.parse(JSON.stringify(oldData));
            const conversation = newData.conversations.find(
              (conv: any) => conv.id === conversationId
            );

            if (conversation) {
              conversation.unread_count = 0;
            }

            return newData;
          });
        }
      },

      conversationBlocked: (payload: {
        conversation_id: string;
        is_blocked: boolean;
        blocker_id: string;
      }) => {
        logger.info('✅ Événement de blocage de conversation reçu via socket:', payload);
        if (payload.conversation_id === conversationId) {
          // Forcer un rechargement de la conversation pour refléter le nouveau statut de blocage
          queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] }); // Utiliser la syntaxe objet

          // Afficher une notification à l'utilisateur qui n'a pas bloqué la conversation
          if (payload.blocker_id !== userId) {
            if (payload.is_blocked) {
              notify('Cette conversation a été bloquée par l\'autre utilisateur', 'warning');
            } else {
              notify('Cette conversation a été débloquée', 'success');
            }
          }
        }
      }
    };
  }, [conversationId, userId, queryClient]); // Supprimer handleNewMessage des dépendances

  // Écouter les événements socket spécifiques à la conversation - supprimer new_message
  useEffect(() => {
    if (!socket || !conversationId || !socketHandlersRef.current) {
      logger.info("🔴 Socket ou conversationId manquant:", { socket: !!socket, conversationId });
      return;
    }

    // Log une seule fois pour réduire le bruit dans la console
    logger.info("✅ Configuration de l'écouteur socket pour les événements de conversation");

    // Utiliser les handlers depuis la référence
    const handlers = socketHandlersRef.current;

    // S'abonner aux événements (supprimer new_message car géré dans MessagesPage)
    socket.on('message_deleted', handlers.messageDeleted);
    socket.on('message_read', handlers.messageRead);
    socket.on('conversation_blocked', handlers.conversationBlocked);

    // Nettoyage
    return () => {
      // Log une seule fois pour réduire le bruit dans la console
      if (process.env.NODE_ENV === 'development') {
        logger.info("Suppression des écouteurs socket de conversation");
      }

      socket.off('message_deleted', handlers.messageDeleted);
      socket.off('message_read', handlers.messageRead);
      socket.off('conversation_blocked', handlers.conversationBlocked);
    };
  }, [socket, conversationId]);

  // Mutation pour envoyer un message
  const sendMessageMutation = useMutation({
    mutationFn: async (data: FormData | { content: string, files?: File[] }) => {
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      if (data instanceof FormData) {
        const response = await api.post(`/api/messages/${conversationId}/messages`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-CSRF-Token': csrfToken
          }
        });
        return response.data;
      } else {
        const formData = new FormData();
        formData.append('content', data.content);
        formData.append('conversation_id', conversationId);

        if (data.files) {
          data.files.forEach(file => {
            formData.append('files', file);
          });
        }

        const response = await api.post(`/api/messages/${conversationId}/messages`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-CSRF-Token': csrfToken
          }
        });
        return response.data;
      }
    },
    onSuccess: (data) => {
      // Garder le bouton désactivé pendant 1.5 secondes après l'envoi
      setTimeout(() => {
        setIsSending(false);
      }, 150);

      // S'assurer d'avoir un ID valide pour le message
      const messageId = data.message?.id || data.id || `temp-${Date.now()}`;

      // Ajouter l'ID du nouveau message à l'ensemble des nouveaux messages
      setNewMessageIds(prev => new Set(prev).add(messageId));

      // Enregistrer l'ordre du message
      addMessageToOrder(messageId);

      // Mise à jour optimiste du cache des messages
      queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
        if (!oldData) return oldData;

        // Récupérer les informations de l'utilisateur actuel (l'expéditeur)
        let senderInfo = null;

        // Chercher d'abord dans la conversation
        const conversationData = queryClient.getQueryData(['conversation', conversationId]) as any;
        if (conversationData) {
          const sender = userId === conversationData.user1_id
            ? conversationData.user1
            : conversationData.user2;

          if (sender) {
            senderInfo = {
              id: sender.id,
              avatar_url: sender.avatar_url,
              prenom: sender.prenom || sender.first_name,
              nom: sender.nom || sender.last_name
            };
          }
        }

        // Si pas trouvé, chercher dans les messages existants
        if (!senderInfo && oldData.pages) {
          for (const page of oldData.pages) {
            if (page.messages) {
              const existingSenderMessage = page.messages.find((msg: any) =>
                msg.sender_id === userId && msg.sender
              );

              if (existingSenderMessage?.sender) {
                senderInfo = existingSenderMessage.sender;
                break;
              }
            }
          }
        }

        const newData = JSON.parse(JSON.stringify(oldData));
        if (newData.pages && newData.pages.length > 0) {
          const lastPage = newData.pages[newData.pages.length - 1];
          if (lastPage.messages) {
            // S'assurer que le nouveau message a une date valide et plus récente que tous les autres
            const now = new Date();
            // Ajouter une milliseconde pour garantir qu'il soit le dernier après tri
            now.setMilliseconds(now.getMilliseconds() + 1);

            lastPage.messages.push({
              id: messageId,
              conversation_id: conversationId,
              content: data.content,
              sender_id: userId,
              created_at: now, // Utiliser un objet Date
              is_read: false,
              attachments: data.attachments || [],
              sender: senderInfo // Ajouter les informations d'expéditeur
            });
          }
        }
        return newData;
      });

      // Mise à jour optimiste du cache des conversations
      queryClient.setQueryData(['conversations'], (oldData: any) => {
        if (!oldData?.conversations) return oldData;

        const newData = JSON.parse(JSON.stringify(oldData));
        const conversation = newData.conversations.find(
          (conv: any) => conv.id === conversationId
        );

        if (conversation) {
          conversation.last_message = {
            content: data.content,
            created_at: new Date().toISOString()
          };
          // Déplacer la conversation en haut de la liste
          const [updatedConv] = newData.conversations.splice(
            newData.conversations.indexOf(conversation),
            1
          );
          newData.conversations.unshift(updatedConv);
        }

        return newData;
      });

      // Réinitialiser le formulaire d'abord
      setNewMessage('');
      setAttachments([]);

      // Garder le bouton désactivé pendant 1.5 secondes après l'envoi
      setTimeout(() => {
        setIsSending(false);
      }, 150);
    },
    onError: (error: any) => {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.info("Erreur lors de l'envoi du message:", error);
      setIsSending(false);
    }
  });

  // Mutation pour bloquer/débloquer
  const toggleBlockMutation = useMutation({
    mutationFn: async (isBlocked: boolean) => {
      const response = await api.patch(`/api/messages/${conversationId}`, {
        is_blocked: isBlocked
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] }); // Utiliser la syntaxe objet
      notify(
        conversation?.user1_has_blocked || conversation?.user2_has_blocked // Utiliser optional chaining
          ? 'Utilisateur débloqué'
          : 'Utilisateur bloqué',
        'success'
      );
    }
  });

  // Mutation pour supprimer la conversation
  const deleteConversationMutation = useMutation({
    mutationFn: async () => {
      const response = await api.patch(`/api/messages/${conversationId}`, {
        is_deleted: true
      });
      return response.data;
    },
    onSuccess: () => {
      // Invalider à la fois les listes de conversations et la conversation actuelle
      queryClient.invalidateQueries({ queryKey: ['conversations'] }); // Utiliser la syntaxe objet
      queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] }); // Utiliser la syntaxe objet

      // Mettre à jour de façon optimiste le cache de la conversation actuelle
      queryClient.setQueryData(['conversation', conversationId], (oldData: any) => {
        if (!oldData) return oldData;

        const newData = {...oldData};
        if (newData.data) {
          // Mettre à jour les flags de suppression selon l'utilisateur
          if (newData.data.user1_id === userId) {
            newData.data.user1_has_deleted = true;
          } else if (newData.data.user2_id === userId) {
            newData.data.user2_has_deleted = true;
          }
        }
        return newData;
      });

      notify('Conversation masquée de votre liste', 'success');
    },
    onError: (error) => {
      logger.info('Erreur lors de la suppression de la conversation:', error);
      notify('Erreur lors de la suppression de la conversation', 'error');
    }
  });

  // Mutation pour démasquer une conversation
  const unhideConversationMutation = useMutation({
    mutationFn: async () => {
      const response = await api.patch(`/api/messages/${conversationId}`, {
        is_deleted: false
      });
      return response.data;
    },
    onSuccess: () => {
      // Invalider à la fois les listes de conversations et la conversation actuelle
      queryClient.invalidateQueries({ queryKey: ['conversations'] }); // Utiliser la syntaxe objet
      queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] }); // Utiliser la syntaxe objet

      notify('Conversation restaurée dans votre liste', 'success');
    },
    onError: (error) => {
      logger.info('Erreur lors du démasquage de la conversation:', error);
      notify('Erreur lors du démasquage de la conversation', 'error');
    }
  });

  // Mutation pour marquer comme non lu
  const markAsUnreadMutation = useMutation({
    mutationFn: async () => {
      try {
        await fetchCsrfToken();
        const csrfToken = await fetchCsrfToken();
        const response = await api.post(`/api/messages/${conversationId}/unread`, {}, {
          headers: { 'X-CSRF-Token': csrfToken },
        });
        return response.data;
      } catch (error) {
        logger.info('Erreur lors du marquage comme non lu:', error);
        return { success: false, error: 'Impossible de marquer comme non lu' };
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        queryClient.invalidateQueries({ queryKey: ['conversations'] }); // Utiliser la syntaxe objet
        notify('Conversation marquée comme non lue', 'success');
        handleMenuClose();
        // Naviguer vers la page des messages
        navigate('/dashboard/messages');
      }
    }
  });

  // Mutation pour supprimer un message
  const deleteMessageMutation = useMutation({
    mutationFn: async (messageId: string) => {
      const response = await api.delete(`/api/messages/${conversationId}/messages/${messageId}`);
      return response.data;
    },
    onSuccess: (_, messageId) => {
      setMessageToDelete(null);
      // Mise à jour optimiste du cache des messages
      queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
        if (!oldData) return oldData;
        const newData = JSON.parse(JSON.stringify(oldData));
        if (newData.pages) {
          newData.pages.forEach((page: any) => {
            if (page.messages) {
              page.messages = page.messages.map((msg: any) => {
                if (msg.id === messageId) {
                  // Vérifier si le message contient des pièces jointes
                  const hasAttachments = msg.attachments && msg.attachments.length > 0;
                  const deletedContent = hasAttachments
                    ? `Pièce${msg.attachments.length > 1 ? 's' : ''} jointe${msg.attachments.length > 1 ? 's' : ''} supprimée${msg.attachments.length > 1 ? 's' : ''} ou expirée${msg.attachments.length > 1 ? 's' : ''}`
                    : 'Ce message a été supprimé.';
                  return {
                    ...msg,
                    content: deletedContent,
                    is_deleted: true,
                    is_deleted_attachment: hasAttachments
                  };
                }
                return msg;
              });
            }
          });
        }
        return newData;
      });
      notify('Message supprimé', 'success');
    },
    onError: (error: any) => {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.info('Erreur lors de la suppression du message:', error);
      notify('Erreur lors de la suppression du message', 'error');
    }
  });

  // Fonction pour vérifier si un message a moins de 2 minutes
  const isMessageDeletable = (message: Message) => {
    const messageDate = new Date(message.created_at);
    const now = new Date();
    const diffInMinutes = (now.getTime() - messageDate.getTime()) / (1000 * 60);
    return diffInMinutes <= 2;
  };

  // Fonction pour gérer la suppression d'un message
  const handleDeleteMessage = async (messageId: string) => {
    try {
      await deleteMessageMutation.mutateAsync(messageId);
      setMessageToDelete(null);
    } catch (error) {
      logger.info('Erreur dans handleDeleteMessage:', error);
    }
  };

  // Fonction pour charger plus de messages
  const handleLoadMore = () => {
    if (messagesContainerRef.current && !isFetchingNextPage) {
      setIsLoadingMore(true);
      const container = messagesContainerRef.current;
      const oldScrollHeight = container.scrollHeight;
      const oldScrollTop = container.scrollTop;

      // Stocker les IDs des messages actuellement affichés AVANT le fetch
      const currentMessageIds = new Set(messages.map(msg => msg.id));

      fetchNextPage().then(() => {
        // Attendre que le DOM soit complètement mis à jour
        requestAnimationFrame(() => {
          const newScrollHeight = container.scrollHeight;
          const heightDifference = newScrollHeight - oldScrollHeight;

          container.scrollTo({
            top: oldScrollTop + heightDifference,
            behavior: 'auto'
          });

          // Identifier les nouveaux IDs après le fetch et la mise à jour du cache
          // On doit attendre que `messages` soit mis à jour par React Query
          // Utiliser un léger délai pour s'assurer que l'état `messages` est à jour
          setTimeout(() => {
            const updatedMessages = queryClient.getQueryData<InfiniteMessagesData>(['messages', conversationId]);
            const allCurrentMessages = updatedMessages?.pages.flatMap(page => page.messages) || [];
            const newMessages = allCurrentMessages.filter(msg => !currentMessageIds.has(msg.id));
            const newIds = new Set(newMessages.map(msg => msg.id));

            if (newIds.size > 0) {
              // Ajouter les nouveaux IDs à l'état de surbrillance
              setHighlightedMessageIds(prev => {
                const updatedSet = new Set(prev);
                newIds.forEach(id => updatedSet.add(id));
                return updatedSet;
              });

              // Programmer la suppression des IDs de l'état après 5 secondes
              newIds.forEach(id => {
                setTimeout(() => {
                  setHighlightedMessageIds(prev => {
                    const updatedSet = new Set(prev);
                    updatedSet.delete(id);
                    return updatedSet;
                  });
                }, 5000);
              });
            }
            setIsLoadingMore(false);
          }, 100); // Léger délai pour la mise à jour de l'état

        });
      }).catch(() => {
        setIsLoadingMore(false);
      });
    } else if (!isFetchingNextPage) {
      setIsLoadingMore(true);
      fetchNextPage().finally(() => setIsLoadingMore(false));
    }
  };

  // Scroll vers le bas UNIQUEMENT lors du chargement initial ou à la réception/envoi de NOUVEAUX messages
  useEffect(() => {
    // Ne pas scroller si on charge des messages plus anciens ou si on fetch déjà
    if (isLoadingMore || isFetchingNextPage) {
      return;
    }

    if (messages.length > 0) {
      const container = messagesContainerRef.current;
      if (container) {
        const lastMessage = messages[messages.length - 1];
        
        // Améliorer la détection des nouveaux messages
        const isLastMessageNew = 
          newMessageIds.has(lastMessage.id) || // Message marqué comme nouveau localement
          (Date.now() - new Date(lastMessage.created_at).getTime()) < 15000; // Message récent (15 sec au lieu de 10)

        // Scroller seulement si c'est le chargement initial ou si le dernier message est nouveau
        if (isInitialLoad || isLastMessageNew) {
          // Utiliser un petit délai pour laisser le temps aux animations/rendus de se terminer
          setTimeout(() => {
            const scrollBehavior = isInitialLoad ? 'auto' : 'smooth';
            scrollToBottom(scrollBehavior);

            if (isInitialLoad) {
              setIsInitialLoad(false);
              // Marquer comme lu seulement au chargement initial
              markAsReadMutation.mutate();
            }
          }, 200); // Délai augmenté pour laisser plus de temps
        }
      }
    }
  }, [messages.length, messages, isInitialLoad, isLoadingMore, isFetchingNextPage, conversationId]); // Ajouter messages dans les dépendances

  // Effet supplémentaire pour détecter les changements dans les messages et forcer le scroll si nécessaire
  useEffect(() => {
    if (messages.length > 0 && !isInitialLoad && !isLoadingMore && !isFetchingNextPage) {
      const container = messagesContainerRef.current;
      if (container) {
        const lastMessage = messages[messages.length - 1];
        
        // Si le dernier message est très récent (moins de 5 secondes), forcer le scroll
        const isVeryRecentMessage = (Date.now() - new Date(lastMessage.created_at).getTime()) < 5000;
        
        if (isVeryRecentMessage) {
          // Petit délai pour s'assurer que le DOM est mis à jour
          setTimeout(() => {
            scrollToBottom('smooth');
            logger.info("🔄 Scroll forcé pour nouveau message détecté:", lastMessage.id);
          }, 300); // Délai augmenté
        }
      }
    }
  }, [messages]); // Surveiller tous les changements dans les messages

  // Effet spécifique pour détecter les nouveaux messages ajoutés par MessagesPage
  useEffect(() => {
    if (messages.length > 0 && !isInitialLoad) {
      const lastMessage = messages[messages.length - 1];
      
      // Si le message n'est pas dans newMessageIds (donc ajouté par MessagesPage)
      // et qu'il est récent, forcer le scroll
      if (!newMessageIds.has(lastMessage.id)) {
        const messageAge = Date.now() - new Date(lastMessage.created_at).getTime();
        
        if (messageAge < 3000) { // Message de moins de 3 secondes
          logger.info("🔄 Nouveau message détecté via MessagesPage, scroll forcé:", lastMessage.id);
          
          setTimeout(() => {
            scrollToBottom('smooth');
          }, 100);
        }
      }
    }
  }, [messages.length, messages]); // Surveiller les changements de messages

  // Reset isInitialLoad et marquer les messages comme lus au changement de conversation
  useEffect(() => {
    setIsInitialLoad(true);
    setNewMessage('');
    setAttachments([]);

    if (conversationId) {
      logger.info("🔄 Conversation changée pour:", conversationId);
    }
  }, [conversationId]); // Simplifier les dépendances

  // Gestionnaire pour le blocage de la conversation
  const handleToggleBlock = () => {
    if (!conversation) return;
    toggleBlockMutation.mutate(!conversation.is_blocked); // Utiliser optional chaining si TQueryFnData n'est pas Conversation
    setIsBlockDialogOpen(false);
  };

  // Gestionnaire pour la suppression de la conversation
  const handleDeleteConversation = () => {
    deleteConversationMutation.mutate();
    setIsDeleteDialogOpen(false);
  };

  // Gestionnaire pour démasquer la conversation
  const handleUnhideConversation = () => {
    unhideConversationMutation.mutate();
    setIsUnhideDialogOpen(false);
  };

  // Récupérer les informations de l'autre utilisateur à partir des messages
  const otherUser = useMemo(() => {
    if (!conversation) return null;

    // Déterminer quel est l'autre utilisateur dans la conversation
    const otherUserData = userId === conversation.user1_id ? conversation.user2 : conversation.user1; // Utiliser optional chaining si TQueryFnData n'est pas Conversation

    if (otherUserData) {
      return {
        id: otherUserData.id,
        avatar_url: otherUserData.avatar_url,
        prenom: otherUserData.prenom,
        nom: otherUserData.nom
      };
    }

    // Si on ne peut pas obtenir les infos de l'utilisateur depuis la conversation,
    // on essaie de les trouver dans les messages (comme avant)
    const data = messagesData as InfiniteMessagesData | undefined;
    if (!data?.pages || data.pages.length === 0) return null;

    // Parcourir toutes les pages de messages
    for (const page of data.pages) {
      if (!page.messages || page.messages.length === 0) continue;

      // Trouver le premier message qui n'est pas de l'utilisateur courant
      const otherUserMessage = page.messages.find((message: Message) => message.sender_id !== userId);
      if (otherUserMessage?.sender) {
        return {
          id: otherUserMessage.sender_id,
          avatar_url: otherUserMessage.sender.avatar_url,
          prenom: otherUserMessage.sender.prenom,
          nom: otherUserMessage.sender.nom
        };
      }
    }
    return null;
  }, [messagesData, userId, conversation]);

  // Modifier handleSendMessage pour utiliser la nouvelle structure
  const handleSendMessage = async (e?: React.FormEvent) => {
    e?.preventDefault();

    if (isSending || (!newMessage.trim() && attachments.length === 0)) return;

    setIsSending(true);

    let messageContent = newMessage;
    const textContent = stripHtml(messageContent).trim();

    if (!textContent && attachments.length === 0) return; // Ne rien envoyer si tout est vide

    // Si le texte est vide mais qu'il y a des pièces jointes, utiliser le texte par défaut
    if (!textContent && attachments.length > 0) {
      messageContent = "<p>Pièce(s) jointe(s)</p>"; // Utiliser du HTML simple
    } else {
      // Nettoyer le HTML avant l'envoi (important pour la sécurité et la cohérence)
      messageContent = DOMPurify.sanitize(messageContent);
    }

    // Créer un ID temporaire pour le message optimiste
    const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    try {
      const files: File[] = [];

      // Convertir les attachments en Files
      for (const attachment of attachments) {
        try {
          let fileToAdd: File | null = null;
          if (attachment.originalFile instanceof File) {
            // Si on a déjà le fichier original, créer une copie avec le nom normalisé
            const file = new File(
              [attachment.originalFile],
              normalizeFileName(attachment.originalFile.name),
              { type: attachment.originalFile.type }
            );
            fileToAdd = file;
          } else if (attachment instanceof File) {
            // Pour les fichiers qui seraient directement des instances de File
            const normalizedName = normalizeFileName(attachment.name);
            const file = new File([attachment], normalizedName, { type: attachment.type });
            fileToAdd = file;
          } else if (attachment.path) {
            // Pour les fichiers qu'on doit recréer à partir de leur URL
            const response = await fetch(attachment.path);
            const blob = await response.blob();
            fileToAdd = new File([blob], normalizeFileName(attachment.name), { type: attachment.type });
          }
          // Si c'est une image, on compresse
          if (fileToAdd && fileToAdd.type.startsWith('image/')) {
            try {
              fileToAdd = await compressGalleryPhoto(fileToAdd);
            } catch (err) {
              // Si la compression échoue, on garde le fichier original
            }
          }
          if (fileToAdd) {
            files.push(fileToAdd);
          }
        } catch (error) {
          logger.info('Erreur lors de la conversion du fichier:', error);
          // Afficher une notification d'erreur
          notify('Erreur lors de la préparation du fichier', 'error');
        }
      }

      // Créer et envoyer le FormData
      const formData = new FormData();
      formData.append('conversation_id', conversationId);
      formData.append('content', messageContent);

      files.forEach((file) => {
        formData.append('files', file);
      });

      // Mise à jour optimiste du cache avant l'envoi
      queryClient.setQueryData(['messages', conversationId], (old: any) => {
        if (!old || !old.pages || !Array.isArray(old.pages)) return old;

        const newData = JSON.parse(JSON.stringify(old));
        const optimisticAttachments = attachments.map((attachment, index) => ({
          id: `temp-attachment-${index}`,
          file_name: attachment.name,
          file_size: attachment.size,
          mime_type: attachment.type,
          storage_path: attachment.path,
          file_path: attachment.path,
          created_at: new Date().toISOString()
        }));

        const optimisticMessage = {
          id: tempId,
          content: messageContent,
          sender_id: userId,
          created_at: new Date().toISOString(),
          attachments: optimisticAttachments,
          is_read: false,
          sender: conversation?.user1_id === userId ? conversation?.user1 : conversation?.user2 // Utiliser optional chaining
        };

        if (newData.pages.length > 0) {
          newData.pages[newData.pages.length - 1].messages.push(optimisticMessage);
        }
        return newData;
      });

      // Assurer que le scroll se fait bien après la mise à jour optimiste
      scrollToBottom('smooth');

      // Confirmer avec un second appel après un délai pour s'assurer que le DOM a été mis à jour
      setTimeout(() => {
        scrollToBottom('smooth');
      }, 300);

      const response = await sendMessageMutation.mutateAsync(formData);

      // Remplacer le message optimiste par le vrai message en préservant les données de l'avatar
      queryClient.setQueryData(['messages', conversationId], (old: any) => {
        if (!old || !old.pages || !Array.isArray(old.pages)) return old;
        const newData = JSON.parse(JSON.stringify(old));
        newData.pages = newData.pages.map((page: any) => ({
          ...page,
          messages: page.messages.map((msg: any) => {
            if (msg.id === tempId) {
              return {
                ...response.data,
                sender: msg.sender,
                attachments: response.data.attachments
              };
            }
            return msg;
          })
        }));
        return newData;
      });

      // Réinitialiser le contenu de l'éditeur Tiptap
      const editor = editorRef.current?.getEditor();
      if (editor) {
        editor.commands.setContent('');
      }

      // Réinitialiser le formulaire d'abord
      setNewMessage('');
      setAttachments([]);

      // Garder le bouton désactivé pendant 1.5 secondes après l'envoi
      setTimeout(() => {
        setIsSending(false);

        // Remettre le focus sur l'éditeur après l'envoi du message
        const editor = editorRef.current?.getEditor();
        if (editor) {
          editor.commands.focus();
        }
      }, 150);
    } catch (error: any) {
      // En cas d'erreur, supprimer le message optimiste
      queryClient.setQueryData(['messages', conversationId], (old: any) => {
        if (!old || !old.pages || !Array.isArray(old.pages)) return old;
        const newData = JSON.parse(JSON.stringify(old));
        newData.pages = newData.pages.map((page: any) => ({
          ...page,
          messages: page.messages.filter((msg: any) => msg.id !== tempId)
        }));
        return newData;
      });

      // Afficher le message du rate limiter si c'est une erreur 429
      if (error.response && error.response.status === 429) {
        const errorMessage = error.response.data.message || error.response.data.error || "Trop de requêtes effectuées sur la messagerie (8 requêtes par minute), veuillez réessayer dans quelques minutes";
        notify(errorMessage, 'error');
      } else {
        setNewMessage(messageContent);
        notify('Erreur lors de l\'envoi du message', 'error');
      }

      // En cas d'erreur, réactiver immédiatement le bouton
      setIsSending(false);
    }
  };

  // Configuration pour uploader des images uniquement
  const { getRootProps: getImageRootProps, getInputProps: getImageInputProps, inputRef: imageInputRef } = useDropzone({
    onDrop: (acceptedFiles) => {
      // Vérifier la taille totale
      const totalSize = acceptedFiles.reduce((acc, file) => acc + file.size, 0);
      if (totalSize > 5 * 1024 * 1024) { // 5MB
        notify('La taille totale des fichiers ne doit pas dépasser 5MB', 'error');
        return;
      }

      // Filtrer uniquement les images
      const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'));

      if (imageFiles.length !== acceptedFiles.length) {
        notify('Certains fichiers ont été ignorés car ils ne sont pas des images', 'warning');
      }

      // Normaliser les noms de fichiers et créer les objets AttachmentFile
      const normalizedFiles = imageFiles.map((file) => {
        const normalizedName = normalizeFileName(file.name);

        return {
          name: normalizedName,
        size: file.size,
        type: file.type,
        path: URL.createObjectURL(file),
        lastModified: file.lastModified,
          webkitRelativePath: file.webkitRelativePath,
          originalFile: file
        };
      });

      setAttachments(prev => [...prev, ...normalizedFiles]);
    },
    maxFiles: 5,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp']
    },
    noClick: true,
    noKeyboard: true
  });

  // Configuration pour uploader des documents (non-images)
  const { getRootProps: getDocRootProps, getInputProps: getDocInputProps, inputRef: docInputRef } = useDropzone({
    onDrop: (acceptedFiles) => {
      // Vérifier la taille totale
      const totalSize = acceptedFiles.reduce((acc, file) => acc + file.size, 0);
      if (totalSize > 5 * 1024 * 1024) { // 5MB
        notify('La taille totale des fichiers ne doit pas dépasser 5MB', 'error');
        return;
      }

      // Vérifier les types de fichiers acceptés
      const acceptedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv'
      ];

      const validFiles = acceptedFiles.filter(file =>
        acceptedTypes.includes(file.type) || file.name.endsWith('.pdf') || file.name.endsWith('.doc') ||
        file.name.endsWith('.docx') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx') ||
        file.name.endsWith('.ppt') || file.name.endsWith('.pptx') || file.name.endsWith('.txt') ||
        file.name.endsWith('.csv')
      );

      if (validFiles.length !== acceptedFiles.length) {
        notify('Certains fichiers ont été ignorés car leur format n\'est pas pris en charge', 'warning');
      }

      // Normaliser les noms de fichiers
      const normalizedFiles = validFiles.map((file) => {
        const normalizedName = normalizeFileName(file.name);

        return {
          name: normalizedName,
          size: file.size,
          type: file.type,
          path: URL.createObjectURL(file),
          lastModified: file.lastModified,
          webkitRelativePath: file.webkitRelativePath,
          originalFile: file
        };
      });

      setAttachments(prev => [...prev, ...normalizedFiles]);
    },
    maxFiles: 5,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'text/plain': ['.txt'],
      'text/csv': ['.csv']
    },
    noClick: true,
    noKeyboard: true
  });

  const handleRemoveAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Fonction pour formater le temps de manière personnalisée
  const formatMessageTime = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 30) {
      return 'À l\'instant';
    }

    return formatDistanceToNow(date, {
      locale: fr,
      addSuffix: true
    });
  };

  // Fonction pour formater la date de lecture
  const formatReadTime = (date: string | Date) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'dd/MM/yy à HH:mm', { locale: fr });
  };

  // Fonction pour ouvrir le profil utilisateur
  const handleOpenUserProfile = async (userId: string) => {
    try {
      // D'abord récupérer le slug de l'utilisateur
      const slugResponse = await api.get(`/api/users/get-slug/${userId}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true
      });

      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      // Ensuite récupérer le profil complet avec le slug
      const response = await api.get(`/api/users/profil/${slugResponse.data.slug}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true
      });

      if (response.data) {
        setSelectedUserProfile(response.data);
        setIsProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.info('Erreur lors de la récupération du profil:', error);
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  // Fonction pour ouvrir la prévisualisation
  const handleOpenPreview = (url: string, type: string, name: string) => {
    setPreview({
      open: true,
      url,
      type,
      name
    });
  };

  // Fonction pour fermer la prévisualisation
  const handleClosePreview = () => {
    setPreview({
      open: false,
      url: '',
      type: '',
      name: ''
    });
  };

  // Afficher un état de chargement global
  if (isLoadingConversation || isLoadingMessages) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress sx={{ color: '#FF6B2C' }} />
      </Box>
    );
  }

  // Afficher les erreurs
  if (conversationError || isLoadingMessages) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          overflow: 'hidden' // Ajouter overflow hidden au conteneur principal
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            p: 2
          }}
        >
          <Typography color="error">
            Une erreur est survenue lors du chargement de la conversation.
            Veuillez réessayer plus tard.
          </Typography>
        </Box>
      </Box>
    );
  }

  // Fonction pour ouvrir la modale de signalement
  const handleOpenReportDialog = (messageId: string) => {
    setReportMessageId(messageId);
    setReportReason('');
    setReportDialogOpen(true);
  };

  // Fonction pour envoyer le signalement
  const handleSendReport = async () => {
    if (!reportMessageId || !reportReason.trim()) return;
    setReportLoading(true);
    try {
      const headers: any = { 'Content-Type': 'application/json' };
      const { fetchCsrfToken } = await import('@/services/csrf');
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await api.post('/api/reported-content', {
        content_type: 'message',
        content_id: reportMessageId,
        reason: reportReason.trim(),
      }, { headers, withCredentials: true });
      notify('Message signalé. Merci pour votre vigilance.', 'success');
      setReportDialogOpen(false);
      setReportMessageId(null);
      setReportReason('');
    } catch (e: any) {
      let errorMsg = e?.response?.data?.error || e?.response?.data?.message || e?.message || "Erreur lors du signalement";
      notify(errorMsg, 'error');
    } finally {
      setReportLoading(false);
    }
  };

  const getAttachmentUrl = (path: string) => `${API_URL}/api/storage-proxy/message_attachments/${path}`;

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflow: 'hidden' // Ajouter overflow hidden au conteneur principal
      }}
    >
      {/* En-tête de la conversation */}
      <HeaderBox isExpanded={isHeaderActionsExpanded}>
        {isLoadingConversation ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Skeleton
              variant="circular"
              width={40}
              height={40}
              sx={{ bgcolor: 'rgba(255, 107, 44, 0.1)' }}
            />
            <Box sx={{ ml: 2 }}>
              <Skeleton
                variant="text"
                width={120}
                height={24}
                sx={{ bgcolor: 'rgba(255, 107, 44, 0.1)' }}
              />
            </Box>
          </Box>
        ) : conversationError ? (
          <Typography color="error">Erreur de chargement</Typography>
        ) : conversation ? (
          <>
            <HeaderMain>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
                <Box sx={{ position: 'relative' }}>
                  <Avatar
                    src={otherUser?.avatar_url || undefined}
                    sx={{
                      width: 40,
                      height: 40,
                      bgcolor: '#FF6B2C',
                      boxShadow: '0 2px 8px rgba(255, 107, 44, 0.2)',
                      cursor: 'pointer',
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'scale(1.05)'
                      },
                      '@media (max-width: 400px)': {
                        width: 36,
                        height: 36,
                      }
                    }}
                    onClick={() => otherUser?.id && handleOpenUserProfile(otherUser.id)}
                  >
                    {otherUser?.prenom?.[0] || otherUser?.nom?.[0] || 'U'}
                  </Avatar>
                  {otherUser?.id && (
                    <Box sx={{
                      position: 'absolute',
                      bottom: -2,
                      right: -2,
                      zIndex: 1
                    }}>
                      <OnlineStatusDot userId={otherUser.id} />
                    </Box>
                  )}
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography
                    variant="subtitle1"
                    fontWeight="medium"
                    sx={{
                      cursor: 'pointer',
                      '&:hover': {
                        color: '#FF6B2C'
                      },
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      '@media (max-width: 400px)': {
                        fontSize: '0.95rem',
                      }
                    }}
                    onClick={() => otherUser?.id && handleOpenUserProfile(otherUser.id)}
                  >
                    {conversation?.user1_id === userId
                      ? `${conversation?.user2?.prenom || ''} ${conversation?.user2?.nom?.charAt(0) + '.' || ''}`
                      : `${conversation?.user1?.prenom || ''} ${conversation?.user1?.nom?.charAt(0) + '.' || ''}`}
                  </Typography>

                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '100%',
                      '@media (max-width: 400px)': {
                        fontSize: '0.7rem',
                      }
                    }}
                  >
                    {conversation?.user1_id === userId
                      ? `Inscrit ${formatDistanceToNow(new Date(conversation?.user2?.date_inscription || ''), { addSuffix: true, locale: fr })} • ${(conversation?.user2?.profil?.type === 'professional' || conversation?.user2?.profil?.type_de_profil === 'professional') ? 'Professionnel' : 'Particulier'}`
                      : `Inscrit ${formatDistanceToNow(new Date(conversation?.user1?.date_inscription || ''), { addSuffix: true, locale: fr })} • ${(conversation?.user1?.profil?.type === 'professional' || conversation?.user1?.profil?.type_de_profil === 'professional') ? 'Professionnel' : 'Particulier'}`}
                  </Typography>

                  {conversation?.is_blocked || conversation?.user1_has_blocked || conversation?.user2_has_blocked ? (
                    <Typography
                      variant="caption"
                      color="error"
                      sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%',
                        '@media (max-width: 400px)': {
                          fontSize: '0.7rem',
                        }
                      }}
                    >
                      Conversation bloquée
                    </Typography>
                  ) : null}
                </Box>
              </Box>

              {isMobile && (
                <IconButton
                  onClick={() => setIsHeaderActionsExpanded(!isHeaderActionsExpanded)}
                  size="small"
                  sx={{
                    color: '#FF6B2C',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      bgcolor: 'rgba(255, 107, 44, 0.08)',
                      transform: 'scale(1.05)'
                    },
                    '& .MuiSvgIcon-root': {
                      fontSize: '1.5rem',
                      transition: 'transform 0.2s ease-in-out'
                    },
                    '&:hover .MuiSvgIcon-root': {
                      transform: 'translateY(2px)'
                    }
                  }}
                >
                  {isHeaderActionsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
            </HeaderMain>

            <HeaderActions
              initial={{ y: -20 }}
              animate={{
                y: isHeaderActionsExpanded ? 0 : -20,
                transition: {
                  type: "spring",
                  stiffness: 200,
                  damping: 15,
                  mass: 1.2,
                  velocity: 0.5
                }
              }}
              className={isHeaderActionsExpanded ? 'expanded' : ''}
            >
              <Button
                variant="text"
                onClick={() => otherUser?.id && handleOpenUserProfile(otherUser.id)}
                sx={{
                  color: '#FF6B2C',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
                }}
              >
                Voir le profil
              </Button>

              <IconButton
                onClick={toggleSound}
                size="small"
                sx={{
                  color: '#FF6B2C',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
                }}
              >
                {isSoundEnabled ? <VolumeUpIcon /> : <VolumeOffIcon className='text-gray-400'/>}
              </IconButton>

              <IconButton
                onClick={handleMenuOpen}
                size="small"
                sx={{
                  color: '#FF6B2C',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </HeaderActions>
          </>
        ) : (
          <Typography color="error">Erreur de chargement</Typography>
        )}
      </HeaderBox>

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255, 107, 44, 0.1)',
            maxWidth: 'calc(100vw - 32px)',
            '@media (max-width: 400px)': {
              '& .MuiMenuItem-root': {
                minHeight: '36px',
                fontSize: '0.85rem',
                padding: '4px 8px',
              },
            }
          }
        }}
      >
        <MenuItem
          onClick={() => markAsUnreadMutation.mutate()}
          sx={{
            color: '#FF6B2C',
            '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
          }}
        >
          <MarkUnreadIcon fontSize="small" sx={{ mr: 1 }} />
          Marquer comme non lu
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuClose();
            setIsBlockDialogOpen(true);
          }}
          sx={{
            color: '#FF6B2C',
            '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.08)' }
          }}
        >
          <BlockIcon fontSize="small" sx={{ mr: 1 }} />
          {(conversation?.is_blocked ||
            (conversation?.user1_id === userId && conversation?.user1_has_blocked) ||
            (conversation?.user2_id === userId && conversation?.user2_has_blocked)) ?
            'Débloquer' : 'Bloquer'} la conversation
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuClose();
            // Si la conversation est déjà masquée, ouvrir le dialogue de démasquage
            if ((conversation?.user1_id === userId && conversation?.user1_has_deleted) ||
                (conversation?.user2_id === userId && conversation?.user2_has_deleted)) {
              setIsUnhideDialogOpen(true);
            } else {
              setIsDeleteDialogOpen(true);
            }
          }}
          sx={{
            color: 'error.main',
            '&:hover': { bgcolor: 'rgba(211, 47, 47, 0.08)' }
          }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          {(conversation?.user1_id === userId && conversation?.user1_has_deleted) ||
          (conversation?.user2_id === userId && conversation?.user2_has_deleted) ?
            'Démasquer la conversation' : 'Masquer la conversation'}
        </MenuItem>
      </Menu>

      {/* Zone de messages */}
      <MessagesContainer ref={messagesContainerRef}>
        {/* Bannière de conversation bloquée */}
        {conversation && (conversation.is_blocked || conversation.user1_has_blocked || conversation.user2_has_blocked) && (
          <Box
            sx={{
              p: 2.5,
              mb: 2,
              borderRadius: 2,
              backgroundColor: 'rgba(255, 107, 44, 0.15)',
              borderLeft: '4px solid #FF6B2C',
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
              position: 'sticky',
              top: 0,
              zIndex: 10,
              boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)'
            }}
          >
            <BlockIcon color="error" fontSize="small" />
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" fontWeight="medium" color="error.main">
                {conversation.user1_id === userId && conversation.user1_has_blocked ?
                  "Vous avez bloqué cette conversation" :
                  conversation.user2_id === userId && conversation.user2_has_blocked ?
                  "Vous avez bloqué cette conversation" :
                  "Conversation bloquée par l'autre utilisateur"}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {conversation.user1_id === userId && conversation.user1_has_blocked ||
                conversation.user2_id === userId && conversation.user2_has_blocked ?
                  "Vous pouvez débloquer cette conversation en utilisant le bouton ci-contre." :
                  "Vous ne pouvez pas envoyer de messages tant que la conversation est bloquée."}
              </Typography>
            </Box>
            {/* Afficher le bouton de déblocage seulement si c'est l'utilisateur actuel qui a bloqué */}
            {((conversation.user1_id === userId && conversation.user1_has_blocked) ||
              (conversation.user2_id === userId && conversation.user2_has_blocked)) && (
              <Button
                variant="contained"
                size="small"
                onClick={handleToggleBlock}
                sx={{
                  bgcolor: '#FF6B2C',
                  '&:hover': { bgcolor: '#FF7A35' }
                }}
              >
                Débloquer
              </Button>
            )}
          </Box>
        )}

        {/* Bannière de conversation masquée */}
        {conversation && ((conversation.user1_id === userId && conversation.user1_has_deleted) ||
                          (conversation.user2_id === userId && conversation.user2_has_deleted)) && (
          <Box
            sx={{
              p: 2.5,
              mb: 2,
              borderRadius: 2,
              backgroundColor: 'rgba(255, 107, 44, 0.15)',
              borderLeft: '4px solid #FF6B2C',
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
              position: 'sticky',
              top: 0,
              zIndex: 10,
              boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)'
            }}
          >
            <DeleteIcon color="error" fontSize="small" />
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" fontWeight="medium" color="error.main">
                Cette conversation est masquée de votre liste
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Vous pouvez la restaurer pour qu'elle apparaisse à nouveau dans votre liste de conversations.
              </Typography>
            </Box>
            <Button
              variant="contained"
              size="small"
              onClick={() => setIsUnhideDialogOpen(true)}
              sx={{
                bgcolor: '#FF6B2C',
                '&:hover': { bgcolor: '#FF7A35' }
              }}
            >
              Démasquer
            </Button>
          </Box>
        )}

        {isLoadingMessages && !isFetchingNextPage ? (
          // Affichage des squelettes de chargement pour les messages
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {[...Array(3)].map((_, index) => (
              <Box
                key={`skeleton-${index}`}
                sx={{
                  display: 'flex',
                  flexDirection: index % 2 === 0 ? 'row' : 'row-reverse',
                  gap: 1,
                  alignItems: 'flex-start'
                }}
              >
                <Skeleton
                  variant="circular"
                  width={32}
                  height={32}
                  sx={{ bgcolor: 'rgba(255, 107, 44, 0.1)' }}
                />
                <Skeleton
                  variant="rectangular"
                  width={index % 2 === 0 ? '60%' : '40%'}
                  height={60}
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    borderRadius: 2
                  }}
                />
              </Box>
            ))}
          </Box>
        ) : messagesError ? (
          <Box sx={{ textAlign: 'center', p: 4 }}>
            <Typography color="error">
              Erreur lors du chargement des messages. Veuillez réessayer.
            </Typography>
          </Box>
        ) : !messages || messages.length === 0 ? (
          <Box
            sx={{
              textAlign: 'center',
              p: 4,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              opacity: 0.7
            }}
          >
            <Typography component="div" color="text.secondary" sx={{ fontSize: '0.9rem' }}>
              Aucun message dans cette conversation
            </Typography>
            <Typography component="div" color="text.secondary" sx={{ fontSize: '0.8rem', mt: 1 }}>
              Commencez à échanger des messages maintenant !
            </Typography>
          </Box>
        ) : (
          <>
            {hasNextPage && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Button
                  onClick={handleLoadMore}
                  disabled={isFetchingNextPage}
                  variant="text"
                  color="primary"
                  sx={{
                    color: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.08)'
                    }
                  }}
                >
                  {isFetchingNextPage ? (
                    <CircularProgress size={20} sx={{ color: '#FF6B2C' }} />
                  ) : (
                    'Charger plus de messages'
                  )}
                </Button>
              </Box>
            )}
            <AnimatePresence mode="popLayout">
              {messages.map((message: Message) => {
                // Vérifier si c'est un message système en vérifiant son contenu
                if (message.content === "⚠️ Attention : Cette conversation est limitée à 150 messages. Les messages les plus anciens seront automatiquement supprimés.") {
                  return (
                    <Box
                      key={message.id}
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '16px 0',
                        padding: '8px 16px',
                        backgroundColor: 'rgba(255, 107, 44, 0.1)',
                        borderRadius: '8px',
                        border: '1px solid rgba(255, 107, 44, 0.2)',
                        color: '#FF6B2C',
                        fontSize: '0.9rem',
                        textAlign: 'center',
                        width: 'fit-content',
                        marginLeft: 'auto',
                        marginRight: 'auto'
                      }}
                    >
                      {message.content}
                    </Box>
                  );
                }

                // Vérifier si c'est un nouveau message à animer
                const isNewMessage = newMessageIds.has(message.id);
                const messageKey = `message-${message.id}`;
                const isHighlighted = highlightedMessageIds.has(message.id); // Vérifier si l'ID est dans l'état

                return (
                <motion.div
                  key={messageKey}
                  className={`message-item ${isHighlighted ? 'new-message-highlight' : ''}`} // Ajouter la classe conditionnellement
                  initial={isNewMessage ? {
                    opacity: 0,
                    y: 20, // Changé de x à y pour une animation plus naturelle
                    height: 'auto',
                    scale: 0.95
                  } : false}
                  animate={{
                    opacity: 1,
                    y: 0,
                    height: 'auto',
                    scale: 1
                  }}
                  exit={{
                    opacity: 0,
                    y: -20,
                    scale: 0.9
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30,
                    mass: 0.8,
                    duration: 0.2
                  }}
                  style={{
                    position: 'relative',
                    marginBottom: '8px' // Ajout d'une marge fixe entre les messages
                  }}
                >
                  {/* Ne pas afficher les messages vides ou non définis */}
                  {message.id && (message.content || (message.attachments && message.attachments.length > 0)) && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: message.sender_id === userId ? 'row-reverse' : 'row',
                        gap: 1,
                        alignItems: 'flex-start',
                        mb: 2
                      }}
                    >
                      <Box sx={{ position: 'relative' }}>
                        <Avatar
                          src={message.sender?.avatar_url || undefined}
                          sx={{
                            width: 32,
                            height: 32,
                            bgcolor: message.sender_id === userId ? '#FF6B2C' : '#FF965E',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                          }}
                          imgProps={{
                            loading: "eager", // Force le chargement prioritaire de l'image
                            crossOrigin: "anonymous"
                          }}
                        >
                          {message.sender?.prenom?.[0]}
                        </Avatar>
                        {message.sender_id !== userId && (
                          <Box sx={{
                            position: 'absolute',
                            bottom: -2,
                            right: -2,
                            zIndex: 1
                          }}>
                            <OnlineStatusDot userId={message.sender_id} />
                          </Box>
                        )}
                      </Box>

                      <Box
                        sx={{
                          maxWidth: '70%',
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 0.5,
                          position: 'relative',
                          width: '100%', // Prend toute la largeur disponible
                          alignItems: message.sender_id === userId ? 'flex-end' : 'flex-start' // Aligne les éléments à gauche ou à droite
                        }}
                      >
                        {/* Bouton de suppression */}
                        <Box
                          sx={{
                            position: 'absolute',
                            top: -16,
                            left: message.sender_id !== userId ? -8 : 'auto',
                            right: message.sender_id === userId ? -8 : 'auto',
                            zIndex: 2,
                            display: 'flex',
                            flexDirection: 'row',
                            gap: 0.5,
                          }}
                        >
                          {/* Bouton supprimer (déjà existant) */}
                          {message.sender_id === userId && isMessageDeletable(message) && (
                            <IconButton
                              size="small"
                              onClick={() => setMessageToDelete(message.id)}
                              sx={{
                                opacity: 0.7,
                                backgroundColor: 'rgba(255, 107, 44, 0.9)',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                width: '24px',
                                height: '24px',
                                minWidth: '24px',
                                minHeight: '24px',
                                padding: '4px',
                                '&:hover': {
                                  backgroundColor: '#FF6B2C',
                                  transform: 'scale(1.1)',
                                },
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                          {/* Bouton signaler (toujours visible) */}
                          {message.sender_id !== userId && (
                            <IconButton
                              size="small"
                              onClick={() => handleOpenReportDialog(message.id)}
                              sx={{
                                opacity: 0.7,
                                backgroundColor: 'rgba(255, 107, 44, 0.7)',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                width: '24px',
                                height: '24px',
                                minWidth: '24px',
                                minHeight: '24px',
                                padding: '4px',
                                marginLeft: 0.5,
                                '&:hover': {
                                  backgroundColor: '#FF6B2C',
                                  transform: 'scale(1.1)',
                                },
                              }}
                              title="Signaler ce message"
                            >
                              <FlagIcon sx={{ fontSize: 14, color: 'white' }} />
                            </IconButton>
                          )}
                        </Box>

                        <Box
                          sx={{
                            // Styles de base qui changent selon l'émetteur et si c'est un message avec pièce jointe
                            ...(message.content && (message.content.startsWith('Pièce(s) jointe(s)') || message.attachments?.length > 0) && !message.content.startsWith('Pièce jointe supprimée ou expirée') && message.sender_id === userId
                              ? {
                                  // Style spécial pour les messages avec pièces jointes envoyés par l'utilisateur
                                  bgcolor: 'rgba(255, 248, 243, 0.8)',
                                  color: 'rgba(0, 0, 0, 0.87)',
                                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.08)'
                                }
                              : {
                                  // Style normal selon l'émetteur
                                  bgcolor: message.sender_id === userId ? '#FF6B2C' : 'rgba(255, 248, 243, 0.8)',
                                  color: message.sender_id === userId ? 'white' : 'rgba(0, 0, 0, 0.87)',
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.08)'
                                }
                            ),
                            p: 1.5,
                            borderRadius: 2,
                            wordBreak: 'break-word',
                            borderTopLeftRadius: message.sender_id !== userId ? 0 : 12,
                            borderTopRightRadius: message.sender_id === userId ? 0 : 12,
                            position: 'relative',
                            alignSelf: message.sender_id === userId ? 'flex-end' : 'flex-start', // Alignement selon l'expéditeur

                            // Style pour les messages reçus avec pièces jointes
                            ...(message.content && (message.content.startsWith('Pièce(s) jointe(s)') || message.attachments?.length > 0) && !message.content.startsWith('Pièce jointe supprimée ou expirée') && message.sender_id !== userId
                              ? {
                                  borderBottom: '1px solid rgba(255, 107, 44, 0.3)',
                                  paddingLeft: 2.5,
                                  '&::before': {
                                    content: '"📎"',
                                    position: 'absolute',
                                    fontSize: '0.75rem',
                                    bottom: 4,
                                    left: 6,
                                    opacity: 0.6
                                  }
                                }
                              : {}
                            ),
                            '&::before': message.sender_id !== userId ? {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: -8,
                              width: 0,
                              height: 0,
                              borderTop: '8px solid rgba(255, 248, 243, 0.8)',
                              borderLeft: '8px solid transparent'
                            } : message.sender_id === userId ? {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              right: -8,
                              width: 0,
                              height: 0,
                              borderTop: '8px solid #FF6B2C',
                              borderRight: '8px solid transparent'
                            } : {},
                            opacity: message.is_deleted ? 0.7 : 1
                          }}
                        >
                          {(message.is_deleted_attachment || (message.content && message.content.startsWith('Pièce jointe supprimée ou expirée'))) ? (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1
                              }}
                            >
                              <Box component="span" sx={{ mr: 0.5 }}>⚠️</Box>
                              <Box component="span">{message.content}</Box>
                            </Box>
                          ) : (
                            // Utiliser dangerouslySetInnerHTML pour afficher le HTML
                            <Box component="span" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(message.content) }} />
                          )}

                          {/* Indication que la pièce jointe a été supprimée */}
                          {message.content === "Pièce(s) jointe(s)" && (!message.attachments || message.attachments.length === 0) && (
                            <Box
                              sx={{
                                mt: 1,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                color: message.sender_id === userId ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.6)',
                                fontStyle: 'italic',
                                fontSize: '0.85rem',
                                border: '1px dashed',
                                borderColor: message.sender_id === userId ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.2)',
                                borderRadius: 1,
                                p: 0.5
                              }}
                            >
                              <Box component="span" sx={{ display: 'flex', alignItems: 'center', mr: 0.5 }}>⚠️</Box>
                              Pièce jointe supprimée ou expirée
                            </Box>
                          )}

                          {message.attachments && message.attachments.length > 0 && (
                            <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 1 }}>
                              {message.attachments.map((attachment: MessageAttachment, index) => (
                                <Box
                                  key={`${attachment.id || message.id}-${index}-${attachment.file_name}`}
                                  sx={{
                                    border: '1px solid rgba(0, 0, 0, 0.1)',
                                    borderRadius: 1,
                                    p: 0.5,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'stretch',
                                    gap: 1,
                                    bgcolor: message.sender_id === userId
                                    ? 'rgba(255, 107, 44, 0.03)'
                                    : 'rgba(255, 255, 255, 0.1)'
                              }}
                                  >
                                    {/* Aperçu d'image si c'est une image */}
                                    {attachment.mime_type?.startsWith('image/') && (
                                      <Box
                                        sx={{
                                          width: '100%',
                                          maxHeight: '200px',
                                          overflow: 'hidden',
                                          display: 'flex',
                                          justifyContent: 'center',
                                          cursor: 'pointer',
                                          borderRadius: 1
                                        }}
                                        onClick={() => attachment.file_path && handleOpenPreview( // Garder la vérification
                                          getAttachmentUrl(attachment.file_path),
                                          attachment.mime_type || '',
                                          attachment.file_name
                                        )}
                                      >
                                        <img
                                          src={attachment.file_path ? getAttachmentUrl(attachment.file_path) : ''} // Garder la vérification et fallback
                                          alt={attachment.file_name}
                                          style={{
                                            maxWidth: '100%',
                                            maxHeight: '200px',
                                            objectFit: 'contain'
                                          }}
                                        />
                                      </Box>
                                    )}

                                    {/* Informations du fichier */}
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      {getFileIcon(attachment.mime_type || '')}
                                      <Typography variant="caption" sx={{ flex: 1 }}>
                                        {attachment.file_name}
                                      </Typography>

                                      {/* Bouton de prévisualisation pour les PDFs et images */}
                                      {(attachment.mime_type?.startsWith('image/') || attachment.mime_type === 'application/pdf') && (
                                        <IconButton
                                          size="small"
                                          onClick={() => attachment.file_path && handleOpenPreview( // Garder la vérification
                                            getAttachmentUrl(attachment.file_path),
                                            attachment.mime_type || '',
                                            attachment.file_name
                                          )}
                                          sx={{
                                            padding: '4px',
                                            color: '#FFFFFF',
                                            backgroundColor: 'rgba(0,0,0,0.15)',
                                            '&:hover': {
                                              backgroundColor: 'rgba(0,0,0,0.3)'
                                            },
                                            minWidth: '24px',
                                            minHeight: '24px',
                                            width: '24px',
                                            height: '24px',
                                            marginRight: '4px'
                                          }}
                                        >
                                          <ZoomInIcon sx={{ fontSize: 14 }} />
                                        </IconButton>
                                      )}

                                      {/* Bouton de téléchargement */}
                                      <IconButton
                                        size="small"
                                        component="a"
                                        href={attachment.file_path ? getAttachmentUrl(attachment.file_path) : '#'} // Garder la vérification et fallback
                                        download={attachment.file_name}
                                        target="_blank"
                                        disabled={!attachment.file_path} // Désactiver si le chemin n'existe pas
                                        sx={{
                                          padding: '4px',
                                          color: '#FFFFFF',
                                          backgroundColor: 'rgba(0,0,0,0.15)',
                                          '&:hover': {
                                            backgroundColor: 'rgba(0,0,0,0.3)'
                                          },
                                          minWidth: '24px',
                                          minHeight: '24px',
                                          width: '24px',
                                          height: '24px'
                                        }}
                                      >
                                        <DownloadIcon sx={{ fontSize: 14 }} />
                                      </IconButton>
                                    </Box>
                                  </Box>
                              ))}
                            </Box>
                          )}
                        </Box>

                        <MessageTimeInfo
                          sx={{
                            alignItems: message.sender_id === userId ? 'flex-end' : 'flex-start' // Aligne à droite pour les messages envoyés, à gauche pour les messages reçus
                          }}
                        >
                          <Typography
                            variant="caption"
                            component="span"
                            sx={{
                              maxWidth: message.sender_id === userId ? '100%' : '70%',
                              textAlign: message.sender_id === userId ? 'right' : 'left'
                            }}
                          >
                            {formatMessageTime(new Date(message.created_at))}
                          </Typography>
                          {message.sender_id === userId && (
                            <>
                              <ReadIndicator>
                                {message.is_read ? (
                                  <>
                                    <span style={{ color: '#FF6B2C' }}>✓</span>
                                    {message.read_at && (
                                      <Typography
                                        variant="caption"
                                        component="span"
                                        sx={{
                                          fontSize: '0.6rem',
                                          color: 'rgba(0, 0, 0, 0.5)',
                                          marginLeft: '4px'
                                        }}
                                      >
                                        (Lu le {formatReadTime(message.read_at)})
                                      </Typography>
                                    )}
                                  </>
                                ) : (
                                  <>
                                    <span style={{ color: 'rgba(0, 0, 0, 0.3)' }}>✓</span>
                                    <Typography
                                      variant="caption"
                                      component="span"
                                      sx={{
                                        fontSize: '0.6rem',
                                        color: 'rgba(0, 0, 0, 0.5)',
                                        marginLeft: '4px'
                                      }}
                                    >
                                      (Non lu)
                                    </Typography>
                                  </>
                                )}
                              </ReadIndicator>
                            </>
                          )}
                        </MessageTimeInfo>
                      </Box>
                    </Box>
                  )}
                </motion.div>
                );
              })}
            </AnimatePresence>
            <div ref={messagesEndRef} style={{ height: 1, visibility: 'hidden' }} />
          </>
        )}
      </MessagesContainer>

      {/* Zone de saisie */}
      <MessageInputBox>
        {/* Prévisualisation des pièces jointes */}
        {attachments.length > 0 && (
          <Box sx={{ position: 'absolute', bottom: '100%', left: 0, right: 0, p: 1, bgcolor: 'background.paper' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {attachments.map((file, index) => (
              <AttachmentPreview key={`attachment-preview-${index}-${file.name}`}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%', overflow: 'hidden' }}>
                    {file.type.startsWith('image/') ? (
                      <Box sx={{
                        width: 50,
                        height: 50,
                        borderRadius: 1,
                        overflow: 'hidden',
                        border: '1px solid rgba(0, 0, 0, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <img
                          src={file.path as string}
                          alt={file.name}
                          style={{
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectFit: 'cover'
                          }}
                        />
                      </Box>
                    ) : (
                      <Box sx={{
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#FF6B2C'
                      }}>
                        {file.type === 'application/pdf' ? (
                          <PictureAsPdfIcon color="error" />
                        ) : (
                          <DescriptionIcon />
                        )}
                      </Box>
                    )}
                    <Box sx={{ overflow: 'hidden', flex: 1 }}>
                      <Typography variant="body2" noWrap>
                  {file.name}
                </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {(file.size / 1024).toFixed(2)} KB
                      </Typography>
                    </Box>
                <IconButton size="small" onClick={() => handleRemoveAttachment(index)}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
                  </Box>
              </AttachmentPreview>
            ))}
            </Box>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              p: 1,
              mt: 1,
              bgcolor: 'rgba(255, 228, 186, 0.3)',
              borderRadius: '8px',
              borderLeft: '3px solid #5cb85c'
            }}>
              <Box
                component="span"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: '#5cb85c',
                  mr: 1,
                  fontSize: '1rem'
                }}
              >
                🌱
              </Box>
              <Typography variant="caption" color="text.secondary">
                Pour des raisons écologiques, les pièces jointes seront automatiquement supprimées après 90 jours.
              </Typography>
            </Box>
          </Box>
        )}

        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, width: '100%' }}>
          <TiptapEditor
            ref={editorRef}
            content={newMessage}
            onChange={(content) => {
              const textLength = stripHtml(content).length;
              if (textLength <= 350) {
                setNewMessage(content);
              } else {
                notify('Limite de 350 caractères atteinte', 'warning');
                const editor = editorRef.current?.getEditor();
                if (editor) {
                  editor.commands.setContent(content.slice(0, 350));
                  setNewMessage(editor.getHTML());
                }
              }
            }}
            placeholder={
              conversation?.is_blocked || conversation?.user1_has_blocked || conversation?.user2_has_blocked
                ? "Conversation bloquée"
                : "Écrivez votre message..."
            }
            readOnly={isSending || !!conversation?.is_blocked || !!conversation?.user1_has_blocked || !!conversation?.user2_has_blocked}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            className="tiptap-editor"
            onImageUpload={() => imageInputRef.current?.click()}
            onFileUpload={() => docInputRef.current?.click()}
            isUploadDisabled={isSending || !!conversation?.is_blocked || !!conversation?.user1_has_blocked || !!conversation?.user2_has_blocked}
            isPrivateMessage={true}
            onSend={handleSendMessage}
            isSendDisabled={!stripHtml(newMessage).trim() && attachments.length === 0 || isSending || !!conversation?.is_blocked || !!conversation?.user1_has_blocked || !!conversation?.user2_has_blocked || messageTextLength > 350}
            isSending={isSending}
            maxLength={350}
            conversation={conversation}
            userId={userId}
          />
        </Box>

        {/* Zones de drop invisibles pour useDropzone */}
        <div {...getImageRootProps()} style={{ display: 'none' }}>
          <input {...getImageInputProps()} />
        </div>
        <div {...getDocRootProps()} style={{ display: 'none' }}>
          <input {...getDocInputProps()} />
        </div>
      </MessageInputBox>

      {/* Dialogue de confirmation pour le blocage */}
      <Dialog
        open={isBlockDialogOpen}
        onClose={() => setIsBlockDialogOpen(false)}
        PaperProps={{
          sx: { borderRadius: 3, maxWidth: '90vw', width: 400 }
        }}
      >
        <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#FF6B2C', fontWeight: 'bold' }}>
          {(conversation?.is_blocked || conversation?.user1_has_blocked || (conversation?.user1_id === userId && conversation?.user1_has_blocked) || (conversation?.user2_id === userId && conversation?.user2_has_blocked)) ? 'Débloquer' : 'Bloquer'} la conversation
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {(conversation?.is_blocked || conversation?.user1_has_blocked || (conversation?.user1_id === userId && conversation?.user1_has_blocked) || (conversation?.user2_id === userId && conversation?.user2_has_blocked))
              ? 'Êtes-vous sûr de vouloir débloquer cette conversation ? Vous pourrez à nouveau échanger des messages.'
              : 'Êtes-vous sûr de vouloir bloquer cette conversation ? Vous ne pourrez plus échanger de messages.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={() => setIsBlockDialogOpen(false)}
            variant="outlined"
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                borderColor: '#FF7A35',
                bgcolor: 'rgba(255, 107, 44, 0.05)'
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleToggleBlock}
            variant="contained"
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': { bgcolor: '#FF7A35' }
            }}
          >
            {(conversation?.is_blocked || conversation?.user1_has_blocked || (conversation?.user1_id === userId && conversation?.user1_has_blocked) || (conversation?.user2_id === userId && conversation?.user2_has_blocked)) ? 'Débloquer' : 'Bloquer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation pour la suppression */}
      <Dialog
        open={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        PaperProps={{
          sx: { borderRadius: 3, maxWidth: '90vw', width: 400 }
        }}
      >
        <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#FF6B2C', fontWeight: 'bold' }}>
          Masquer la conversation
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir masquer cette conversation de votre liste ?
            Elle ne sera plus visible dans votre liste principale, mais l'autre participant pourra toujours y accéder.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={() => setIsDeleteDialogOpen(false)}
            variant="outlined"
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                borderColor: '#FF7A35',
                bgcolor: 'rgba(255, 107, 44, 0.05)'
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleDeleteConversation}
            variant="contained"
            sx={{
              bgcolor: 'error.main',
              '&:hover': { bgcolor: 'error.dark' }
            }}
          >
            Masquer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation pour la suppression de message */}
      <Dialog
        open={!!messageToDelete}
        onClose={() => setMessageToDelete(null)}
        PaperProps={{
          sx: { borderRadius: 3, maxWidth: '90vw', width: 600 }
        }}
      >
        <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#FF6B2C', fontWeight: 'bold' }}>
          Supprimer le message
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ paddingTop: 2 }}>
            Êtes-vous sûr de vouloir supprimer ce message ? Cette action est irréversible.
            <br />
            <br />
            <Typography variant="caption" color="text.secondary">
              Note : Vous ne pouvez supprimer que vos propres messages uniquement si le message a été envoyé il y a moins de 2 minutes.
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={() => setMessageToDelete(null)}
            variant="outlined"
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                borderColor: '#FF7A35',
                bgcolor: 'rgba(255, 107, 44, 0.05)'
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={() => messageToDelete && handleDeleteMessage(messageToDelete)}
            variant="contained"
            sx={{
              bgcolor: 'error.main',
              '&:hover': { bgcolor: 'error.dark' }
            }}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal du profil utilisateur */}
      {selectedUserProfile && (
        <UserProfileModal
          isOpen={isProfileModalOpen}
          onClose={() => setIsProfileModalOpen(false)}
          userData={selectedUserProfile}
        />
      )}

      {/* Remplacer la Dialog de prévisualisation par ModalPortal */}
      {preview.open && (
        <ModalPortal
          isOpen={preview.open}
          onBackdropClick={handleClosePreview}
          withBlur={true}
        >
          <PreviewCarousel
            attachments={messages
              .flatMap(msg => msg.attachments || [])
              .filter(att => (att.mime_type?.startsWith('image/') || att.mime_type === 'application/pdf') && att.storage_path) // Garder la vérification pour storage_path
              .map(att => ({
                ...att,
                public_url: att.storage_path ? getAttachmentUrl(att.storage_path) : '' // Vérifier explicitement ici
              }))}
            initialIndex={messages
              .flatMap(msg => msg.attachments || [])
              .filter(att => (att.mime_type?.startsWith('image/') || att.mime_type === 'application/pdf') && att.storage_path) // Garder la vérification pour storage_path
              .findIndex(att => att.storage_path ? getAttachmentUrl(att.storage_path) === preview.url : false)} // Vérifier explicitement ici
            onClose={handleClosePreview}
          />
        </ModalPortal>
      )}

      {/* Dialogue de confirmation pour démasquer la conversation */}
      <Dialog
        open={isUnhideDialogOpen}
        onClose={() => setIsUnhideDialogOpen(false)}
        PaperProps={{
          sx: { borderRadius: 3, maxWidth: '90vw', width: 400 }
        }}
      >
        <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#FF6B2C', fontWeight: 'bold' }}>
          Démasquer la conversation
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir démasquer cette conversation ?
            Elle apparaîtra à nouveau dans votre liste de conversations.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={() => setIsUnhideDialogOpen(false)}
            variant="outlined"
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                borderColor: '#FF7A35',
                bgcolor: 'rgba(255, 107, 44, 0.05)'
              }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleUnhideConversation}
            variant="contained"
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': { bgcolor: '#FF7A35' }
            }}
          >
            Démasquer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation pour signalement */}
      <Dialog
        open={reportDialogOpen}
        onClose={() => setReportDialogOpen(false)}
        PaperProps={{
          sx: { borderRadius: 3, maxWidth: '90vw', width: 400 }
        }}
      >
        <DialogTitle>Signaler ce message</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Merci d'indiquer la raison de votre signalement. L'équipe de modération va examiner ce message.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            label="Raison du signalement"
            type="text"
            fullWidth
            variant="outlined"
            value={reportReason}
            onChange={e => setReportReason(e.target.value)}
            multiline
            minRows={2}
            maxRows={5}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportDialogOpen(false)} color="inherit">
            Annuler
          </Button>
          <Button onClick={handleSendReport} disabled={reportLoading || !reportReason.trim()} sx={{ bgcolor: '#FF6B2C', color: 'white', '&:hover': { bgcolor: '#FF7A35' } }}>
            {reportLoading ? 'Envoi...' : 'Signaler'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConversationView;
