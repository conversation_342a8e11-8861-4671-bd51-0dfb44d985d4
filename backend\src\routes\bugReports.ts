import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import { 
  getBugReports, 
  getBugReportById, 
  createBugReport, 
  updateBugReport, 
  deleteBugReport, 
  voteBugReport, 
  deleteVote, 
  getBugReportHistory,
  getBugReportStats,
  getBugReportComments,
  addBugReportComment,
  updateBugReportComment,
  deleteBugReportComment,
  deleteMultipleBugReportComments,
  closeBugReport,
  reopenBugReport,
  closeMultipleBugReports,
  approveReport,
  rejectReport,
  toggleReportVisibility,
  deleteMultipleBugReports
} from '../controllers/bugReports';
import { authMiddleware } from '../middleware/authMiddleware';
import { handleValidationErrors } from '../middleware/validation';
import { body } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

// --- Ajout d'un asyncHandler simple pour Express 5 ---
function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any> | void
): (req: Request, res: Response, next: NextFunction) => void {
  return function (req: Request, res: Response, next: NextFunction): void {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
// ---------------------------------------------------

const router = Router();

// Rate limiter global - 120 requêtes par minute
const globalLimiter = rateLimit({
  windowMs: 30 * 1000, // 1 minute
  max: 60, // 60 requêtes max
  message: 'Trop de requêtes effectuées. Veuillez réessayer dans une minute.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter pour les créations - 5 requêtes par minute
const creationLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requêtes max
  message: 'Trop de rapports/commentaires créés. Veuillez réessayer dans une minute.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation pour la création d'un rapport de bug
const createBugReportValidation = [
  body('title').isString().trim().notEmpty().withMessage('Le titre est requis'),
  body('description').isString().trim().notEmpty().withMessage('La description est requise'),
  body('report_type').isIn(['bug', 'improvement']).withMessage('Le type de rapport doit être "bug" ou "improvement"'),
  body('category').isIn(['interface', 'fonctionnalite', 'paiement', 'securite', 'autre']).withMessage('Catégorie invalide'),
  body('priority').isIn(['faible', 'moyenne', 'elevee', 'critique']).withMessage('Priorité invalide'),
  body('is_private').optional().isBoolean().withMessage('Le champ is_private doit être un booléen'),
  body('reproduction_steps').optional().isString().withMessage('Les étapes de reproduction doivent être une chaîne de caractères'),
  body('browser_info').optional().isObject().withMessage('Les informations du navigateur doivent être un objet'),
  body('os_info').optional().isString().withMessage('Les informations du système d\'exploitation doivent être une chaîne de caractères')
];

// Validation pour la mise à jour d'un rapport de bug
const updateBugReportValidation = [
  body('title').optional().isString().trim().notEmpty().withMessage('Le titre ne peut pas être vide'),
  body('description').optional().isString().trim().notEmpty().withMessage('La description ne peut pas être vide'),
  body('report_type').optional().isIn(['bug', 'improvement']).withMessage('Le type de rapport doit être "bug" ou "improvement"'),
  body('category').optional().isIn(['interface', 'fonctionnalite', 'paiement', 'securite', 'autre']).withMessage('Catégorie invalide'),
  body('priority').optional().isIn(['faible', 'moyenne', 'elevee', 'critique']).withMessage('Priorité invalide'),
  body('status').optional().isIn(['nouveau', 'en_cours', 'resolu', 'rejete', 'ferme', 'reouvert', 'attente_moderation']).withMessage('Statut invalide'),
  body('is_private').optional().isBoolean().withMessage('Le champ is_private doit être un booléen'),
  body('admin_comment').optional().isString().withMessage('Le commentaire doit être une chaîne de caractères'),
  body('reproduction_steps').optional().isString().withMessage('Les étapes de reproduction doivent être une chaîne de caractères'),
  body('browser_info').optional().isObject().withMessage('Les informations du navigateur doivent être un objet'),
  body('os_info').optional().isString().withMessage('Les informations du système d\'exploitation doivent être une chaîne de caractères'),
  body('assigned_to').optional().isUUID().withMessage('L\'ID de l\'assigné doit être un UUID valide')
];

// Validation pour les commentaires de modération
const moderationCommentValidation = [
  body('admin_comment').optional().isString().withMessage('Le commentaire doit être une chaîne de caractères')
];

// Validation pour les votes
const voteValidation = [
  body('vote_type').isIn(['pour', 'contre']).withMessage('Le type de vote doit être "pour" ou "contre"'),
  body('comment').optional().isString().withMessage('Le commentaire doit être une chaîne de caractères')
];

// Validation de la création de commentaire
const createCommentValidation = [
  body('message')
    .notEmpty()
    .withMessage('Le message est requis')
    .isString()
    .withMessage('Le message doit être une chaîne de caractères')
    .isLength({ min: 1, max: 900 })
    .withMessage('Le message doit faire entre 1 et 900 caractères'),
  body('parent_comment_id')
    .optional()
    .isUUID()
    .withMessage('L\'identifiant du commentaire parent doit être un UUID valide')
];

// Validation pour la modification de la visibilité
const toggleVisibilityValidation = [
  body('is_private').isBoolean().withMessage('Le paramètre is_private doit être un booléen')
];

// Routes accessibles à tous les utilisateurs authentifiés
router.get('/', authMiddleware.authenticateToken, globalLimiter, asyncHandler(getBugReports));
router.get('/stats', authMiddleware.authenticateToken, globalLimiter, asyncHandler(getBugReportStats));
router.get('/:id', authMiddleware.authenticateToken, globalLimiter, asyncHandler(getBugReportById));
router.get('/:id/history', authMiddleware.authenticateToken, globalLimiter, asyncHandler(getBugReportHistory));
router.post('/', authMiddleware.authenticateToken, creationLimiter, createBugReportValidation, handleValidationErrors, asyncHandler(createBugReport));
router.put('/:id', authMiddleware.authenticateToken, globalLimiter, updateBugReportValidation, handleValidationErrors, asyncHandler(updateBugReport));
router.delete('/:id', authMiddleware.authenticateToken, globalLimiter, asyncHandler(deleteBugReport));

// Route pour modifier la visibilité d'un rapport
router.put('/:id/visibility', authMiddleware.authenticateToken, globalLimiter, toggleVisibilityValidation, handleValidationErrors, asyncHandler(toggleReportVisibility));

// Routes pour les votes
router.post('/:id/vote', authMiddleware.authenticateToken, globalLimiter, voteValidation, handleValidationErrors, asyncHandler(voteBugReport));
router.delete('/:id/vote', authMiddleware.authenticateToken, globalLimiter, asyncHandler(deleteVote));

// Routes pour les commentaires
router.get('/:id/comments', authMiddleware.authenticateToken, globalLimiter, asyncHandler(getBugReportComments));
router.post('/:id/comments', authMiddleware.authenticateToken, creationLimiter, createCommentValidation, handleValidationErrors, asyncHandler(addBugReportComment));
router.put('/:id/comments/:commentId', authMiddleware.authenticateToken, globalLimiter, createCommentValidation, handleValidationErrors, asyncHandler(updateBugReportComment));
router.delete('/:id/comments/:commentId', authMiddleware.authenticateToken, globalLimiter, asyncHandler(deleteBugReportComment));
router.post('/:id/comments/delete-multiple', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm']), globalLimiter, asyncHandler(deleteMultipleBugReportComments));

// Routes pour les opérations de fermeture et réouverture (admin/modérateur uniquement)
router.post('/:id/close', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, asyncHandler(closeBugReport));
router.post('/:id/reopen', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, asyncHandler(reopenBugReport));
router.post('/close-multiple', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, asyncHandler(closeMultipleBugReports));
router.post('/delete-multiple', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, asyncHandler(deleteMultipleBugReports));

// Routes pour la modération des rapports (admin/modérateur uniquement)
router.post('/:id/approve', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, moderationCommentValidation, handleValidationErrors, asyncHandler(approveReport));
router.post('/:id/reject', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), globalLimiter, moderationCommentValidation, handleValidationErrors, asyncHandler(rejectReport));

export default router; 