import React, { useState } from 'react';
import VerifierForceMotDePasse from '../pages/auth/VerifierForceMotDePasse';
import DOMPurify from 'dompurify';

interface FormFieldProps {
  id?: string;
  label?: string;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (e?: React.FocusEvent<HTMLInputElement>) => void;
  error?: string | string[];
  errors?: string[];
  touched?: boolean;
  helpText?: string;
  required?: boolean;
  placeholder?: string;
  autoComplete?: string;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  id,
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  errors = [],
  touched = false,
  helpText,
  required = false,
  placeholder,
  autoComplete,
  className,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  // Convertir error en tableau si c'est une chaîne
  const errorArray = error 
    ? (Array.isArray(error) ? error : [error]) 
    : errors;
  
  const hasError = touched && errorArray.length > 0;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    onBlur(e);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e);
  };

  return (
    <div className={`mb-6 ${className}`}>
      {label && (
        <label
          htmlFor={id || name}
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <input
        id={id || name}
        name={name}
        type={type}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={`w-full px-4 py-2 border rounded-md shadow-sm text-gray-900 ${
          hasError && touched
            ? 'border-red-500 focus:ring-red-500'
            : 'border-gray-300 focus:ring-blue-500'
        } focus:outline-none focus:ring-2 focus:border-transparent h-10 flex items-center`}
        placeholder={placeholder}
        autoComplete={autoComplete}
        required={required}
      />
      {type === 'password' && value && (
        <div className="mt-1">
          <VerifierForceMotDePasse motDePasse={value.toString()} />
        </div>
      )}
      {hasError && touched && errorArray.length > 0 && (
        <div className="mt-1">
          {errorArray.map((err, index) => (
            <p key={index} className="text-sm text-red-500">
              {err}
            </p>
          ))}
        </div>
      )}
      {helpText && !hasError && isFocused && (
        <p className="mt-1 text-sm text-gray-500">{helpText}</p>
      )}
    </div>
  );
};