import React from 'react';
import { Box, Tooltip } from '@mui/material';
import { PriorityHigh as PriorityHighIcon } from '@mui/icons-material';

interface TicketPriorityIndicatorProps {
  priority: string;
}

const priorityConfig: Record<string, { label: string; color: string; level: number }> = {
  faible: {
    label: 'Priorité faible',
    color: '#8BC34A', // Vert clair
    level: 1,
  },
  normale: {
    label: 'Priorité normale',
    color: '#2196F3', // Bleu
    level: 2,
  },
  elevee: {
    label: 'Priorité élevée',
    color: '#FF9800', // Orange
    level: 3,
  },
  urgente: {
    label: 'Priorité urgente',
    color: '#F44336', // Rouge
    level: 4,
  },
};

const TicketPriorityIndicator: React.FC<TicketPriorityIndicatorProps> = ({ priority }) => {
  const config = priorityConfig[priority] || priorityConfig.normale;

  return (
    <Tooltip title={config.label} arrow>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 0.5,
        }}
      >
        {[...Array(config.level)].map((_, index) => (
          <PriorityHighIcon
            key={index}
            sx={{
              color: config.color,
              fontSize: '1rem',
              transform: 'rotate(180deg)',
            }}
          />
        ))}
      </Box>
    </Tooltip>
  );
};

export default TicketPriorityIndicator; 