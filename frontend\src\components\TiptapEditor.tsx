import { use<PERSON><PERSON><PERSON>, EditorContent, Editor, Extension } from '@tiptap/react'
import { Command } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Placeholder from '@tiptap/extension-placeholder'
import HardBreak from '@tiptap/extension-hard-break'
import { Box, Paper, IconButton, Tooltip, Divider, Popover, Menu, MenuItem, Typography, Slider, Button, CircularProgress } from '@mui/material'
import { styled } from '@mui/material/styles'
import { forwardRef, useImperativeHandle, useState, useRef, useCallback, useEffect } from 'react'
import FormatBoldIcon from '@mui/icons-material/FormatBold'
import FormatItalicIcon from '@mui/icons-material/FormatItalic'
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined'
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted'
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered'
import FormatQuoteIcon from '@mui/icons-material/FormatQuote'
import CodeIcon from '@mui/icons-material/Code'
import EmojiEmotionsIcon from '@mui/icons-material/EmojiEmotions'
import FormatColorTextIcon from '@mui/icons-material/FormatColorText'
import FormatSizeIcon from '@mui/icons-material/FormatSize'
import HorizontalRuleIcon from '@mui/icons-material/HorizontalRule'
import TableChartIcon from '@mui/icons-material/TableChart'
import TableViewIcon from '@mui/icons-material/TableView'
import DeleteIcon from '@mui/icons-material/Delete'
import AddIcon from '@mui/icons-material/Add'
import EmojiPicker, { Categories, Theme, EmojiStyle } from 'emoji-picker-react'
import type { EmojiClickData } from 'emoji-picker-react'
import ImageIcon from '@mui/icons-material/Image'
import AttachFileIcon from '@mui/icons-material/AttachFile'
import SendIcon from '@mui/icons-material/Send'
import { AnyExtension } from '@tiptap/core'

const StyledPaper = styled(Paper)(() => ({
  padding: 0,
  '&.with-icon .ProseMirror': {
    paddingLeft: '40px',
  },
  '& .ProseMirror, & .tiptap-table': {
    '& table': {
      borderCollapse: 'collapse',
      width: '100%',
      tableLayout: 'fixed',
      overflow: 'hidden',
      margin: '1rem 0',
      '& td, & th': {
        border: '2px solid #ddd',
        padding: '0.5rem',
        position: 'relative',
        minWidth: '150px',
        width: '150px',
        '& p': {
          margin: 0,
        },
      },
      '& th': {
        backgroundColor: '#FFF8F3',
        fontWeight: 'bold',
        color: '#FF6B2C',
      },
      '& .selectedCell': {
        backgroundColor: '#FFE4BA',
      },
      '& .column-resize-handle': {
        position: 'absolute',
        right: '-2px',
        top: 0,
        bottom: 0,
        width: '4px',
        backgroundColor: '#FF6B2C',
        cursor: 'col-resize',
        userSelect: 'none',
        touchAction: 'none',
        '&::after': {
          content: '""',
          position: 'absolute',
          right: '-2px',
          top: 0,
          bottom: 0,
          width: '4px',
          backgroundColor: '#FF6B2C',
          opacity: 0,
          transition: 'opacity 0.2s',
        },
        '&:hover::after': {
          opacity: 1,
        },
      },
    },
  },
  '& .ProseMirror': {
    minHeight: '40px',
    maxHeight: '400px',
    outline: 'none',
    padding: '1rem',
    transition: 'all 0.2s ease-in-out',
    overflowY: 'auto',
    fontSize: '16px',
    lineHeight: '1.6',
    '&::-webkit-scrollbar': {
      width: '8px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f1f1f1',
      borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb': {
      background: '#FF6B2C',
      borderRadius: '4px',
      '&:hover': {
        background: '#FF7A35',
      },
    },
    '&:focus': {
      outline: 'none',
    },
    '& p': {
      margin: '0.5em 0',
    },
    '& ul, & ol': {
      padding: '0 1rem',
    },
    '& code': {
      backgroundColor: '#f1f1f1',
      padding: '0.2em 0.4em',
      borderRadius: '3px',
    },
    '& pre': {
      backgroundColor: '#f1f1f1',
      padding: '0.75rem 1rem',
      borderRadius: '0.5rem',
    },
    '& blockquote': {
      borderLeft: '3px solid #FF6B2C',
      paddingLeft: '1rem',
      color: '#666',
      margin: '1rem 0',
    },
    '& hr': {
      border: 'none',
      borderTop: '1px solid #ddd',
      margin: '1.5rem 0',
    },
    '& p.is-editor-empty:first-of-type::before': {
      content: 'attr(data-placeholder)',
      float: 'left',
      color: '#adb5bd',
      pointerEvents: 'none',
      height: 0,
    }
  },
}))

const ColorPicker = styled('div')(() => ({
  width: '250px',
  padding: '1rem',
  '& .color-grid': {
    display: 'grid',
    gridTemplateColumns: 'repeat(5, 1fr)',
    gap: '0.5rem',
  },
  '& .color-item': {
    width: '30px',
    height: '30px',
    borderRadius: '4px',
    cursor: 'pointer',
    border: '2px solid transparent',
    transition: 'all 0.2s',
    '&:hover': {
      transform: 'scale(1.1)',
    },
    '&.active': {
      border: '2px solid #FF6B2C',
    },
  },
  '& .custom-color': {
    marginTop: '1rem',
    '& input[type="color"]': {
      width: '100%',
      height: '40px',
      padding: '0.25rem',
      border: '1px solid #ddd',
      borderRadius: '4px',
      cursor: 'pointer',
    },
  },
}))

const fontSizes = [
  { label: 'Très petit', value: '12px' },
  { label: 'Petit', value: '14px' },
  { label: 'Normal', value: '16px' },
  { label: 'Grand', value: '18px' },
  { label: 'Très grand', value: '20px' },
  { label: 'Énorme', value: '24px' },
]

const colors = [
  '#FF6B2C', '#FF7A35', '#FF965E', '#FFE4BA', '#FFF8F3',
  '#000000', '#333333', '#666666', '#999999', '#CCCCCC',
  '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF',
  '#00FFFF', '#FFA500', '#800080', '#008000', '#800000',
]

const FontSize = Extension.create({
  name: 'fontSize',

  addOptions() {
    return {
      types: ['textStyle'],
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML: element => element.style.fontSize,
            renderHTML: attributes => {
              if (!attributes.fontSize) {
                return {}
              }
              return {
                style: `font-size: ${attributes.fontSize}`,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      setFontSize: (fontSize: string): Command => ({ chain }) => {
        return chain()
          .setMark('textStyle', { fontSize })
          .run()
      },
    }
  },
})

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    fontSize: {
      setFontSize: (fontSize: string) => ReturnType
    }
  }
}

export interface TiptapInstance {
  getEditor: () => Editor | null;
  focus: () => void;
  blur: () => void;
}

interface TiptapEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  readOnly?: boolean;
  onKeyDown?: (event: KeyboardEvent) => void;
  onImageUpload?: () => void;
  onFileUpload?: () => void;
  isUploadDisabled?: boolean;
  isPrivateMessage?: boolean;
  onSend?: () => void;
  isSendDisabled?: boolean;
  isSending?: boolean;
  maxLength?: number;
  conversation?: any;
  userId?: string;
  withIcon?: boolean;
}

const ToolbarStyled = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: '0.5rem',
  padding: '0.5rem',
  borderBottom: `1px solid ${theme.palette.divider}`,
  backgroundColor: '#FFF8F3',
  borderTopLeftRadius: theme.shape.borderRadius,
  borderTopRightRadius: theme.shape.borderRadius,
  overflowX: 'auto',
  '&::-webkit-scrollbar': {
    height: '8px',
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#FF6B2C',
    borderRadius: '4px',
    '&:hover': {
      background: '#FF7A35',
    },
  },
  '& .MuiIconButton-root': {
    color: '#666',
    flexShrink: 0,
    '&:hover': {
      backgroundColor: '#FFE4BA',
    },
    '&.active': {
      color: '#FF6B2C',
      backgroundColor: '#FFE4BA',
    },
  },
  '& .MuiDivider-root': {
    flexShrink: 0,
  },
  '&.hidden': {
    display: 'none',
  }
}))

const TiptapEditor = forwardRef<TiptapInstance, TiptapEditorProps>((props, ref) => {
  const { 
    content, 
    onChange, 
    placeholder, 
    className, 
    onFocus, 
    onBlur, 
    readOnly, 
    onKeyDown,
    onImageUpload,
    onFileUpload,
    isUploadDisabled,
    isPrivateMessage = false,
    onSend,
    isSendDisabled,
    isSending = false,
    maxLength,
    conversation,
    userId,
    withIcon = false
  } = props;

  const [isEditorFocused, setIsEditorFocused] = useState(false);
  const [emojiAnchorEl, setEmojiAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [sizeAnchorEl, setSizeAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [colorAnchorEl, setColorAnchorEl] = useState<HTMLButtonElement | null>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const [tableAnchorEl, setTableAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);
  const [tableMenuAnchorEl, setTableMenuAnchorEl] = useState<HTMLButtonElement | null>(null);
  const colorTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const getTextLength = () => {
    if (!editor) return 0;
    
    // On récupère uniquement le texte brut, sans le HTML
    const text = editor.getText();
    
    // On compte les emojis comme un seul caractère
    const emojiRegex = /\p{Emoji}/gu;
    const textWithoutEmojis = text.replace(emojiRegex, '1');
    
    return textWithoutEmojis.length;
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        hardBreak: false
      }) as AnyExtension,
      HardBreak.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => this.editor.commands.setHardBreak()
          }
        }
      }) as AnyExtension,
      Underline as AnyExtension,
      TextStyle as AnyExtension,
      Color as AnyExtension,
      FontSize as AnyExtension,
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'tiptap-table',
        },
      }) as AnyExtension,
      TableRow as AnyExtension,
      TableHeader as AnyExtension,
      TableCell as AnyExtension,
      Placeholder.configure({
        placeholder: placeholder || '',
      }) as AnyExtension,
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor: editorInstance }) => {
      const textLength = getTextLength();
      if (!maxLength || textLength <= maxLength) {
        onChange(editorInstance.getHTML());
      } else {
        editorInstance.commands.undo();
      }
    },
    onFocus: () => {
      setIsEditorFocused(true);
      onFocus?.();
    },
    onBlur: () => {
      setIsEditorFocused(false);
      onBlur?.();
    },
    editorProps: {
      handleKeyDown: (view, event) => {
        const textLength = getTextLength();
        if (maxLength && textLength >= maxLength && !event.metaKey && !event.ctrlKey && event.key.length === 1) {
          event.preventDefault();
          return true;
        }

        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault();
          if (editor) {
            editor.commands.setHardBreak();
          }
          return true;
        }
        onKeyDown?.(event);
        return false;
      },
    },
  })

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    if (maxLength && getTextLength() >= maxLength) {
      return; // On n'ajoute pas l'emoji si on dépasse la limite
    }

    if (emojiData.isCustom) {
      // Mapping des emojis personnalisés vers leurs équivalents Unicode
      const customToUnicode: { [key: string]: string } = {
        "jardinier": "🌿",
        "plombier": "🔧",
        "menuisier": "🔨",
        "electricien": "⚡",
        "peintre": "🎨",
        "maçon": "🏗️",
        "vitrier": "🪟",
        "serrurier": "🔑",
        "chauffagiste": "🏠",
        "carreleur": "🏛️"
      };
      const emojiId = emojiData.names[0];
      const unicodeEmoji = customToUnicode[emojiId] || emojiData.emoji;
      editor?.chain().focus().insertContent(unicodeEmoji).run();
    } else {
      editor?.chain().focus().insertContent(emojiData.emoji).run();
    }
  }

  const handleSizeSelect = (size: string) => {
    editor?.chain().focus().setFontSize(size).run()
    setSizeAnchorEl(null)
  }

  const handleColorSelect = useCallback((color: string, isCustom: boolean = false) => {
    if (isCustom) {
      if (colorTimeoutRef.current) {
        clearTimeout(colorTimeoutRef.current);
      }
      colorTimeoutRef.current = setTimeout(() => {
        editor?.chain().focus().setColor(color).run();
      }, 100);
    } else {
      editor?.chain().focus().setColor(color).run();
      setColorAnchorEl(null);
    }
  }, [editor]);

  useEffect(() => {
    return () => {
      if (colorTimeoutRef.current) {
        clearTimeout(colorTimeoutRef.current);
      }
    };
  }, []);

  const handleTableInsert = () => {
    editor?.chain().focus().insertTable({ rows: tableRows, cols: tableCols, withHeaderRow: true }).run()
    setTableAnchorEl(null)
  }

  const handleTableMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (editor?.isActive('table')) {
      setTableMenuAnchorEl(event.currentTarget)
    }
  }

  const handleTableMenuClose = () => {
    setTableMenuAnchorEl(null)
  }

  const handleDeleteRow = () => {
    editor?.chain().focus().deleteRow().run()
    handleTableMenuClose()
  }

  const handleDeleteColumn = () => {
    editor?.chain().focus().deleteColumn().run()
    handleTableMenuClose()
  }

  const handleDeleteTable = () => {
    editor?.chain().focus().deleteTable().run()
    handleTableMenuClose()
  }

  const handleAddRowBefore = () => {
    editor?.chain().focus().addRowBefore().run()
    handleTableMenuClose()
  }

  const handleAddRowAfter = () => {
    editor?.chain().focus().addRowAfter().run()
    handleTableMenuClose()
  }

  const handleAddColumnBefore = () => {
    editor?.chain().focus().addColumnBefore().run()
    handleTableMenuClose()
  }

  const handleAddColumnAfter = () => {
    editor?.chain().focus().addColumnAfter().run()
    handleTableMenuClose()
  }

  useImperativeHandle(ref, () => ({
    getEditor: () => editor,
    focus: () => editor?.commands.focus(),
    blur: () => editor?.commands.blur(),
  }))

  return (
    <Box sx={{ width: '100%' }} className={className}>
      <StyledPaper elevation={0} className={withIcon ? 'with-icon' : ''}>
        <ToolbarStyled>
          <Tooltip title="Taille du texte">
            <IconButton
              onClick={(e) => setSizeAnchorEl(e.currentTarget)}
            >
              <FormatSizeIcon />
            </IconButton>
          </Tooltip>

          <Menu
            anchorEl={sizeAnchorEl}
            open={Boolean(sizeAnchorEl)}
            onClose={() => setSizeAnchorEl(null)}
          >
            {fontSizes.map((size) => (
              <MenuItem 
                key={size.value} 
                onClick={() => handleSizeSelect(size.value)}
                style={{ fontSize: size.value }}
              >
                {size.label}
              </MenuItem>
            ))}
          </Menu>

          <Tooltip title="Gras (Ctrl+B)">
            <IconButton
              onClick={() => editor?.chain().focus().toggleBold().run()}
              className={editor?.isActive('bold') ? 'active' : ''}
            >
              <FormatBoldIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Italique (Ctrl+I)">
            <IconButton
              onClick={() => editor?.chain().focus().toggleItalic().run()}
              className={editor?.isActive('italic') ? 'active' : ''}
            >
              <FormatItalicIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Souligné (Ctrl+U)">
            <IconButton
              onClick={() => editor?.chain().focus().toggleUnderline().run()}
              className={editor?.isActive('underline') ? 'active' : ''}
            >
              <FormatUnderlinedIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Couleur">
            <IconButton
              onClick={(e) => setColorAnchorEl(e.currentTarget)}
            >
              <FormatColorTextIcon />
            </IconButton>
          </Tooltip>

          <Popover
            open={Boolean(colorAnchorEl)}
            anchorEl={colorAnchorEl}
            onClose={() => setColorAnchorEl(null)}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <ColorPicker>
              <div className="color-grid">
                {colors.map((color) => (
                  <div
                    key={color}
                    className={`color-item ${editor?.isActive('textStyle', { color }) ? 'active' : ''}`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorSelect(color)}
                    title={color}
                  />
                ))}
              </div>
              <div className="custom-color">
                <input
                  type="color"
                  onChange={(e) => handleColorSelect(e.target.value, true)}
                  title="Choisir une couleur personnalisée"
                />
              </div>
            </ColorPicker>
          </Popover>

          <Divider orientation="vertical" flexItem />

          <Tooltip title="Emoji">
            <IconButton
              ref={emojiButtonRef}
              onClick={(e) => setEmojiAnchorEl(e.currentTarget)}
            >
              <EmojiEmotionsIcon />
            </IconButton>
          </Tooltip>

          <Popover
            open={Boolean(emojiAnchorEl)}
            anchorEl={emojiAnchorEl}
            onClose={() => setEmojiAnchorEl(null)}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <EmojiPicker
              onEmojiClick={handleEmojiClick}
              width={300}
              height={350}
              emojiVersion="5.0"
              theme={Theme.LIGHT}
              searchPlaceholder="Rechercher un emoji..."
              previewConfig={{
                defaultCaption: '',
                defaultEmoji: '',
                showPreview: false
              }}
              categories={[
                {
                  name: "Sourires et émotions",
                  category: Categories.SMILEYS_PEOPLE
                },
                {
                  name: "Animaux et nature",
                  category: Categories.ANIMALS_NATURE
                },
                {
                  name: "Nourriture et boissons",
                  category: Categories.FOOD_DRINK
                },
                {
                  name: "Activités",
                  category: Categories.ACTIVITIES
                },
                {
                  name: "Voyage et lieux",
                  category: Categories.TRAVEL_PLACES
                },
                {
                  name: "Objets",
                  category: Categories.OBJECTS
                },
                {
                  name: "Symboles",
                  category: Categories.SYMBOLS
                },
                {
                  name: "Drapeaux",
                  category: Categories.FLAGS
                }
              ]}
              searchDisabled={false}
              skinTonesDisabled={true}
              emojiStyle={EmojiStyle.APPLE}
              lazyLoadEmojis={true}
              reactionsDefaultOpen={false}
              allowExpandReactions={true}
              hiddenEmojis={[]}
            />
          </Popover>

          <Divider orientation="vertical" flexItem />

          <Tooltip title="Liste à puces">
            <IconButton
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              className={editor?.isActive('bulletList') ? 'active' : ''}
            >
              <FormatListBulletedIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Liste numérotée">
            <IconButton
              onClick={() => editor?.chain().focus().toggleOrderedList().run()}
              className={editor?.isActive('orderedList') ? 'active' : ''}
            >
              <FormatListNumberedIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem />

          <Tooltip title="Citation">
            <IconButton
              onClick={() => editor?.chain().focus().toggleBlockquote().run()}
              className={editor?.isActive('blockquote') ? 'active' : ''}
            >
              <FormatQuoteIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Code">
            <IconButton
              onClick={() => editor?.chain().focus().toggleCode().run()}
              className={editor?.isActive('code') ? 'active' : ''}
            >
              <CodeIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem />

          <Tooltip title="Ligne horizontale">
            <IconButton
              onClick={() => editor?.chain().focus().setHorizontalRule().run()}
            >
              <HorizontalRuleIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem />

          <Tooltip title="Insérer un tableau">
            <IconButton
              onClick={(e) => setTableAnchorEl(e.currentTarget)}
            >
              <TableChartIcon />
            </IconButton>
          </Tooltip>

          {editor?.isActive('table') && (
            <Tooltip title="Gérer le tableau">
              <IconButton
                onClick={handleTableMenuOpen}
                sx={{
                  opacity: 1,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    backgroundColor: '#FFE4BA',
                    transform: 'scale(1.1)',
                    boxShadow: '0 0 8px rgba(255, 107, 44, 0.3)',
                  },
                  '&:active': {
                    transform: 'scale(0.95)',
                  },
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': {
                      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0.4)',
                    },
                    '70%': {
                      boxShadow: '0 0 0 6px rgba(255, 107, 44, 0)',
                    },
                    '100%': {
                      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0)',
                    },
                  },
                }}
              >
                <TableViewIcon />
              </IconButton>
            </Tooltip>
          )}

          <Menu
            anchorEl={tableAnchorEl}
            open={Boolean(tableAnchorEl)}
            onClose={() => setTableAnchorEl(null)}
            PaperProps={{
              sx: {
                p: 2,
                minWidth: 100,
              }
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Nombre de lignes</Typography>
              <Slider
                value={tableRows}
                onChange={(_, value) => setTableRows(value as number)}
                min={1}
                max={10}
                marks
                valueLabelDisplay="auto"
                sx={{
                  color: '#FF6B2C',
                  '& .MuiSlider-thumb': {
                    '&:hover, &.Mui-focusVisible': {
                      boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)',
                    },
                    '&.Mui-active': {
                      boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)',
                    },
                  },
                }}
              />
              <Typography variant="body2" color="text.secondary" align="center">
                {tableRows}
              </Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Nombre de colonnes</Typography>
              <Slider
                value={tableCols}
                onChange={(_, value) => setTableCols(value as number)}
                min={1}
                max={10}
                marks
                valueLabelDisplay="auto"
                sx={{
                  color: '#FF6B2C',
                  '& .MuiSlider-thumb': {
                    '&:hover, &.Mui-focusVisible': {
                      boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)',
                    },
                    '&.Mui-active': {
                      boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)',
                    },
                  },
                }}
              />
              <Typography variant="body2" color="text.secondary" align="center">
                {tableCols}
              </Typography>
            </Box>
            <Button
              variant="contained"
              fullWidth
              onClick={handleTableInsert}
              sx={{
                backgroundColor: '#FF6B2C',
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
              }}
            >
              Insérer le tableau
            </Button>
          </Menu>

          <Menu
            anchorEl={tableMenuAnchorEl}
            open={Boolean(tableMenuAnchorEl)}
            onClose={handleTableMenuClose}
            PaperProps={{
              sx: {
                minWidth: 100,
              }
            }}
          >
            <MenuItem onClick={handleAddRowBefore}>
              <AddIcon sx={{ mr: 1 }} />
              Ajouter une ligne avant
            </MenuItem>
            <MenuItem onClick={handleAddRowAfter}>
              <AddIcon sx={{ mr: 1 }} />
              Ajouter une ligne après
            </MenuItem>
            <MenuItem onClick={handleAddColumnBefore}>
              <AddIcon sx={{ mr: 1 }} />
              Ajouter une colonne avant
            </MenuItem>
            <MenuItem onClick={handleAddColumnAfter}>
              <AddIcon sx={{ mr: 1 }} />
              Ajouter une colonne après
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleDeleteRow} sx={{ color: 'error.main' }}>
              <DeleteIcon sx={{ mr: 1 }} />
              Supprimer la ligne
            </MenuItem>
            <MenuItem onClick={handleDeleteColumn} sx={{ color: 'error.main' }}>
              <DeleteIcon sx={{ mr: 1 }} />
              Supprimer la colonne
            </MenuItem>
            <MenuItem onClick={handleDeleteTable} sx={{ color: 'error.main' }}>
              <DeleteIcon sx={{ mr: 1 }} />
              Supprimer le tableau
            </MenuItem>
          </Menu>

          <Divider orientation="vertical" flexItem />

          {isPrivateMessage && (
            <>
              <Tooltip title="Ajouter une image">
                <IconButton
                  onClick={onImageUpload}
                  disabled={isUploadDisabled}
                >
                  <ImageIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Ajouter un fichier">
                <IconButton
                  onClick={onFileUpload}
                  disabled={isUploadDisabled}
                >
                  <AttachFileIcon />
                </IconButton>
              </Tooltip>

              <Divider orientation="vertical" flexItem />
            </>
          )}
        </ToolbarStyled>
        <Box sx={{ position: 'relative' }}>
          <EditorContent editor={editor} />
          {readOnly && (
            <>
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.7)',
                  backdropFilter: 'blur(4px)',
                  zIndex: 1,
                  cursor: 'not-allowed'
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                  pointerEvents: 'none',
                  zIndex: 2
                }}
              >
                <Typography
                  variant="body2"
                  color={placeholder === "Conversation bloquée" ? "error" : "success"}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    backdropFilter: 'blur(4px)',
                    '&::before': {
                      content: '""',
                      display: 'block',
                      width: '16px',
                      height: '16px',
                      backgroundImage: placeholder === "Conversation bloquée" 
                        ? 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23d32f2f\'%3E%3Cpath d=\'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z\'/%3E%3C/svg%3E")'
                        : 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%232e7d32\'%3E%3Cpath d=\'M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z\'/%3E%3C/svg%3E")',
                      backgroundSize: 'contain',
                      backgroundRepeat: 'no-repeat',
                      opacity: 0.8
                    }
                  }}
                >
                  {placeholder === "Conversation bloquée" ? 
                    (conversation?.user1_id === userId && conversation?.user1_has_blocked ? 
                      "Vous avez bloqué cette conversation" : 
                      conversation?.user2_id === userId && conversation?.user2_has_blocked ?
                      "Vous avez bloqué cette conversation" :
                      "Conversation bloquée par l'autre utilisateur") 
                    : "Envoi en cours"}
                </Typography>
              </Box>
            </>
          )}
          {onSend && maxLength && (
            <Box
              sx={{
                position: 'absolute',
                bottom: '2px',
                right: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 1.5,
                backgroundColor: 'rgba(255, 255, 255, 0.98)',
                padding: '6px 12px',
                borderRadius: '20px',
                border: '1px solid rgba(255, 107, 44, 0.15)',
                boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
                backdropFilter: 'blur(8px)',
                zIndex: 2,
                transform: 'translateY(-50%)',
                height: '36px',
                '& > *': {
                  display: 'flex',
                  alignItems: 'center',
                },
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                  borderColor: 'rgba(255, 107, 44, 0.25)',
                }
              }}
            >
              <Typography 
                variant="caption" 
                color={getTextLength() > maxLength ? 'error' : 'textSecondary'}
                sx={{ 
                  fontSize: '0.85rem',
                  fontWeight: 500,
                  letterSpacing: '0.3px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '0 4px',
                  '&::before': {
                    content: '""',
                    width: '5px',
                    height: '5px',
                    borderRadius: '50%',
                    backgroundColor: getTextLength() > maxLength ? 'error.main' : '#FF6B2C',
                    opacity: getTextLength() > maxLength ? 0.8 : 0.5,
                  }
                }}
              >
                {getTextLength()}/{maxLength}
              </Typography>
              <Divider 
                orientation="vertical" 
                flexItem 
                sx={{ 
                  height: '20px',
                  borderColor: 'rgba(255, 107, 44, 0.15)',
                  margin: '0 4px'
                }} 
              />
              <IconButton
                onClick={onSend}
                disabled={isSendDisabled}
                size="small"
                sx={{
                  color: '#FF6B2C',
                  padding: '6px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF7A35',
                  },
                  '&:active': {
                    transform: 'scale(0.95)'
                  },
                  '&.Mui-disabled': {
                    color: 'rgba(255, 107, 44, 0.3)',
                  }
                }}
              >
                {isSending ? (
                  <CircularProgress 
                    size={18} 
                    sx={{ 
                      color: '#FF6B2C',
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' }
                      }
                    }} 
                  />
                ) : (
                  <SendIcon 
                    fontSize="small"
                    sx={{
                      transition: 'transform 0.2s ease-in-out',
                      '&:hover': {
                        transform: 'translateX(2px)'
                      }
                    }}
                  />
                )}
              </IconButton>
            </Box>
          )}
        </Box>
      </StyledPaper>
    </Box>
  )
})

TiptapEditor.displayName = 'TiptapEditor'

export default TiptapEditor 