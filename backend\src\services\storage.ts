import { supabase } from '../config/supabase';
import { randomBytes } from 'crypto';
import logger from '../utils/logger';
import { processImage } from '../utils/imageProcessor';
import { emailService } from '../services/emailService';
import { MessageAttachment } from '../types/messaging';
import config from '../config';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

const BUCKET_NAME = 'photo_profil';
const GALLERY_BUCKET = 'galerie_realisation_client';
const MISSION_BUCKET = 'mission_photos';
const SUPPORT_TICKET_BUCKET = 'support_ticket_attachments';
const MESSAGE_BUCKET = 'message_attachments';
const ENTREPRISE_VERIF_BUCKET = 'entreprise_verification';
const TEMP_MODERATION_BUCKET = 'temp_moderation'; // Bucket pour les images temporaires en attente de modération
const CARD_EDITOR_BUCKET = 'carte_visite_et_flyer'; // Bucket pour les images des cartes de visite et flyers
const REVIEW_PHOTOS_BUCKET = 'avis_photos_client'; // Bucket pour les photos des avis clients

export const DEFAULT_AVATAR_URL = `${config.apiUrl}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

/**
 * Fonction utilitaire pour extraire le chemin du fichier depuis une URL Supabase
 * Gère différents formats d'URL (direct, via proxy, etc.)
 */
export const extractFilePathFromUrl = (url: string, bucketName: string): string | null => {
  if (!url || !bucketName) return null;

  // Essayer différents patterns d'URL
  const patterns = [
    new RegExp(`${bucketName}\\/(.+)$`),  // Pattern principal
    new RegExp(`\\/${bucketName}\\/(.+)$`), // Avec slash initial
    new RegExp(`api\\/storage-proxy\\/${bucketName}\\/(.+)$`), // Via proxy
    new RegExp(`storage\\/v1\\/object\\/public\\/${bucketName}\\/(.+)$`), // URL Supabase directe
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  // Si aucun pattern ne correspond, essayer de prendre la partie après le dernier slash
  const lastSlashIndex = url.lastIndexOf('/');
  if (lastSlashIndex !== -1 && lastSlashIndex < url.length - 1) {
    const fileName = url.substring(lastSlashIndex + 1);
    // Vérifier que le nom de fichier semble valide (contient une extension)
    if (fileName.includes('.')) {
      return fileName;
    }
  }

  logger.warn('Impossible d\'extraire le chemin du fichier', { url, bucketName });
  return null;
};

// Fonction pour générer un storage_id unique
export const generateStorageId = (): string => {
  return randomBytes(16).toString('hex'); // Génère un ID de 32 caractères hexadécimaux
};

// Fonction pour obtenir ou créer le storage_id d'un utilisateur
export const getOrCreateStorageId = async (userId: string): Promise<string> => {
  try {
    // Vérifier si l'utilisateur a déjà un storage_id
    const { data, error } = await supabase
      .from('user_profil')
      .select('storage_id')
      .eq('user_id', userId)
      .single();

    if (error) throw error;

    if (data?.storage_id) {
      return data.storage_id;
    }

    // Si pas de storage_id, en créer un nouveau
    const newStorageId = generateStorageId();
    const { error: updateError } = await supabase
      .from('user_profil')
      .update({ storage_id: newStorageId })
      .eq('user_id', userId);

    if (updateError) throw updateError;

    return newStorageId;
  } catch (error) {
    logger.error('Erreur lors de la récupération/création du storage_id', { error, userId });
    throw error;
  }
};

// Fonction utilitaire pour générer l'URL publique via le proxy backend
export const getPublicUrl = (bucket: string, filePath: string): string => {
  return `${config.apiUrl}/api/storage-proxy/${bucket}/${filePath}`;
};

export const uploadProfilPhoto = async (
  userId: string,
  file: Buffer,
  fileType: string,
  options: { addIaTag?: boolean } = {}
): Promise<string> => {
  try {
    // Compression et réduction de la taille de l'image avant l'upload pour la photo de profil
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1200,
      maxHeight: 1200,
      quality: 73,
      addIaTag: options.addIaTag === true // Ajout du tag IA seulement si demandé
    });

    // Obtenir le storage_id de l'utilisateur
    await getOrCreateStorageId(userId);

    // Obtenir le storage_id et les informations de l'utilisateur en une seule requête
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('storage_id, nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError || !userData?.storage_id) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError || new Error('storage_id non trouvé');
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Avant de créer un nouveau fichier, supprimer tous les fichiers existants dans le dossier de l'utilisateur
    const userFolder = `${userData.storage_id}/profil`;
    const { data: existingFiles } = await supabase.storage
      .from(BUCKET_NAME)
      .list(userFolder);

    if (existingFiles && existingFiles.length > 0) {
      const filesToDelete = existingFiles.map(file => `${userFolder}/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(BUCKET_NAME)
        .remove(filesToDelete);

      if (deleteError) {
        logger.error('Erreur lors de la suppression des anciennes photos', { error: deleteError, userId, userFolder });
      } else {
        logger.info('Anciennes photos supprimées avec succès', { userId, userFolder, filesCount: filesToDelete.length });
      }
    }

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = processedImage.format;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const timestamp = Date.now();
    // Utiliser seulement la première lettre du nom de famille
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_photo_de_profil_job_partiel_fr_${userData.storage_id}${timestamp}.${fileExt}`;
    const filePath = `${userData.storage_id}/profil/${fileName}`;

    // Upload du fichier vers Supabase Storage avec le bon type de contenu
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(filePath, processedImage.buffer, {
        contentType: `image/${processedImage.format}`,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la photo de profil', { error, userId });
      throw new Error('Erreur lors de l\'upload de la photo');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(BUCKET_NAME, filePath);

    logger.info('Photo de profil uploadée avec succès', {
      userId,
      storageId: userData.storage_id,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });

    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la photo de profil', { error, userId });
    throw error;
  }
};

export const uploadBannerPhoto = async (
  userId: string,
  file: Buffer,
  fileType: string,
  options: { addIaTag?: boolean } = {}
): Promise<string> => {
  try {
    // Compression et réduction de la taille de l'image avant l'upload pour la bannière
    // Dimensions plus larges pour la bannière
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 73,
      addIaTag: options.addIaTag === true // Ajout du tag IA seulement si demandé
    });

    // Obtenir le storage_id de l'utilisateur
    await getOrCreateStorageId(userId);

    // Obtenir le storage_id et les informations de l'utilisateur en une seule requête
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('storage_id, nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError || !userData?.storage_id) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError || new Error('storage_id non trouvé');
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Avant de créer un nouveau fichier, supprimer tous les fichiers existants dans le dossier de bannière de l'utilisateur
    const userFolder = `${userData.storage_id}/banniere`;
    const { data: existingFiles } = await supabase.storage
      .from(BUCKET_NAME)
      .list(userFolder);

    if (existingFiles && existingFiles.length > 0) {
      const filesToDelete = existingFiles.map(file => `${userFolder}/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(BUCKET_NAME)
        .remove(filesToDelete);

      if (deleteError) {
        logger.error('Erreur lors de la suppression des anciennes bannières', { error: deleteError, userId, userFolder });
      } else {
        logger.info('Anciennes bannières supprimées avec succès', { userId, userFolder, filesCount: filesToDelete.length });
      }
    }

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = processedImage.format;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const timestamp = Date.now();
    // Utiliser seulement la première lettre du nom de famille
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_banniere_job_partiel_fr_${userData.storage_id}${timestamp}.${fileExt}`;
    const filePath = `${userData.storage_id}/banniere/${fileName}`;

    // Upload du fichier vers Supabase Storage avec le bon type de contenu
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(filePath, processedImage.buffer, {
        contentType: `image/${processedImage.format}`,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la bannière', { error, userId });
      throw new Error('Erreur lors de l\'upload de la bannière');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(BUCKET_NAME, filePath);

    logger.info('Bannière uploadée avec succès', {
      userId,
      storageId: userData.storage_id,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });

    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la bannière', { error, userId });
    throw error;
  }
};

export const uploadGalleryPhoto = async (
  userId: string,
  file: Buffer,
  fileType: string,
  galleryId: string,
  galleryName?: string,
  options: { addIaTag?: boolean } = {}
): Promise<string> => {
  try {
    // Compression et réduction de la taille de l'image avant l'upload pour la galerie
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1200,
      maxHeight: 1200,
      quality: 73,
      addIaTag: options.addIaTag === true // Ajout du tag IA seulement si demandé
    });

    // Obtenir le storage_id de l'utilisateur
    const storageId = await getOrCreateStorageId(userId);

    // Récupérer le nom et prénom de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError;
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Récupérer le nom de la galerie si non fourni
    let sanitizedGalleryName = 'galerie';
    if (!galleryName) {
      const { data: galleryData, error: galleryError } = await supabase
        .from('user_gallery')
        .select('name')
        .eq('id', galleryId)
        .single();

      if (!galleryError && galleryData) {
        sanitizedGalleryName = galleryData.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
      }
    } else {
      sanitizedGalleryName = galleryName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = processedImage.format;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    // Utiliser seulement la première lettre du nom de famille
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_${sanitizedGalleryName}_realisation_job_partiel_fr_${uniqueId}_${timestamp}.${fileExt}`;
    const filePath = `${storageId}/gallery/${galleryId}/${fileName}`;

    const { data, error } = await supabase.storage
      .from(GALLERY_BUCKET)
      .upload(filePath, processedImage.buffer, {
        contentType: `image/${processedImage.format}`,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la photo de galerie', { error, userId, galleryId });
      throw new Error('Erreur lors de l\'upload de la photo');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(GALLERY_BUCKET, filePath);

    logger.info('Photo de galerie uploadée avec succès', {
      userId,
      storageId,
      galleryId,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });

    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la photo de galerie', { error, userId });
    throw error;
  }
};

export const deleteProfilPhoto = async (userId: string, photoUrl: string): Promise<void> => {
  try {
    // Extraire le chemin du fichier de l'URL avec la fonction utilitaire
    const filePath = extractFilePathFromUrl(photoUrl, BUCKET_NAME);

    if (!filePath) {
      logger.warn('Impossible d\'extraire le chemin du fichier pour la suppression', { userId, photoUrl });
      return;
    }

    // Ne pas supprimer les avatars par défaut
    if (filePath.startsWith('avatar/')) {
      logger.info('Tentative de suppression d\'un avatar par défaut ignorée', { userId, filePath });
      return;
    }

    const { error } = await supabase.storage
      .from(BUCKET_NAME)
      .remove([filePath]);

    if (error) {
      logger.error('Erreur lors de la suppression de la photo de profil', { error, userId, photoUrl, filePath });
      throw new Error('Erreur lors de la suppression de la photo');
    } else {
      logger.info('Photo de profil supprimée avec succès', { userId, filePath });
    }
  } catch (error) {
    logger.error('Erreur lors de la suppression de la photo de profil', { error, userId });
    throw error;
  }
};

export const deleteBannerPhoto = async (userId: string): Promise<void> => {
  try {
    // Obtenir le storage_id de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('storage_id')
      .eq('user_id', userId)
      .single();

    if (userError || !userData?.storage_id) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError || new Error('storage_id non trouvé');
    }

    // Récupérer tous les fichiers dans le dossier bannière de l'utilisateur
    const userFolder = `${userData.storage_id}/banniere`;
    const { data: existingFiles, error: listError } = await supabase.storage
      .from(BUCKET_NAME)
      .list(userFolder);

    if (listError) {
      logger.error('Erreur lors de la récupération des fichiers de bannière', { error: listError, userId, userFolder });
      throw new Error('Erreur lors de la récupération des fichiers de bannière');
    }

    // S'il y a des fichiers à supprimer
    if (existingFiles && existingFiles.length > 0) {
      const filesToDelete = existingFiles.map(file => `${userFolder}/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(BUCKET_NAME)
        .remove(filesToDelete);

      if (deleteError) {
        logger.error('Erreur lors de la suppression des fichiers de bannière', { error: deleteError, userId, userFolder });
        throw new Error('Erreur lors de la suppression des fichiers de bannière');
      }

      logger.info('Fichiers de bannière supprimés avec succès', { userId, userFolder, filesCount: filesToDelete.length });
    } else {
      logger.info('Aucun fichier de bannière à supprimer', { userId, userFolder });
    }
  } catch (error) {
    logger.error('Erreur lors de la suppression de la bannière', { error, userId });
    throw error;
  }
};

export const uploadReviewPhotos = async (
  userId: string,
  files: Array<{ buffer: Buffer; fileType: string }>,
  reviewId: string,
  options: { addIaTag?: boolean } = {}
): Promise<string[]> => {
  try {
    // Vérifier le nombre de fichiers (max 4)
    if (files.length > 4) {
      throw new Error('Maximum 4 photos autorisées par avis');
    }

    const uploadedUrls: string[] = [];
    const storageId = await getOrCreateStorageId(userId);

    // Récupérer le nom et prénom de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError;
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const firstLetterOfLastName = sanitizedNom.charAt(0);

    // Traiter chaque fichier
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      logger.info('Traitement du fichier d\'avis', {
        index: i,
        bufferLength: file.buffer ? file.buffer.length : 0,
        fileType: file.fileType,
        hasBuffer: !!file.buffer,
        bufferType: typeof file.buffer
      });

      // Vérifier que le buffer n'est pas vide
      if (!file.buffer || file.buffer.length === 0) {
        throw new Error(`Le buffer du fichier ${i + 1} est vide ou invalide`);
      }

      // Compression et réduction de la taille de l'image
      const processedImage = await processImage(file.buffer, file.fileType, {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 73,
        addIaTag: options.addIaTag === true
      });

      logger.info('Image traitée avec succès', {
        index: i,
        originalSize: file.buffer.length,
        processedSize: processedImage.buffer.length,
        format: processedImage.format
      });

      // Note: La modération des images doit être faite côté frontend avant l'upload
      // via le hook useImageModeration et l'API /api/content-moderation/analyze

      // Créer un nom de fichier unique pour chaque photo
      const fileExt = processedImage.format;
      const uniqueId = randomBytes(4).toString('hex');
      const timestamp = Date.now();
      const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_avis_photo_${i + 1}_job_partiel_fr_${uniqueId}_${timestamp}.${fileExt}`;
      const filePath = `${storageId}/avis/${reviewId}/${fileName}`;

      // Upload du fichier vers Supabase Storage
      const { data, error } = await supabase.storage
        .from(REVIEW_PHOTOS_BUCKET)
        .upload(filePath, processedImage.buffer, {
          contentType: `image/${processedImage.format}`,
          duplex: 'half',
          upsert: true
        });

      if (error) {
        logger.error('Erreur lors de l\'upload de la photo d\'avis', { error, userId, reviewId, fileIndex: i });
        throw new Error(`Erreur lors de l\'upload de la photo ${i + 1}`);
      }

      // Récupérer l'URL publique via le proxy
      const publicUrl = getPublicUrl(REVIEW_PHOTOS_BUCKET, filePath);
      uploadedUrls.push(publicUrl);

      logger.info('Photo d\'avis uploadée avec succès', {
        userId,
        storageId,
        reviewId,
        filePath,
        fileIndex: i + 1,
        fileSize: processedImage.buffer.length,
        fileType: processedImage.format,
        publicUrl
      });
    }

    return uploadedUrls;
  } catch (error) {
    logger.error('Erreur lors de l\'upload des photos d\'avis', { error, userId, reviewId });
    throw error;
  }
};

export const uploadMissionPhoto = async (
  userId: string,
  file: Buffer,
  fileType: string,
  missionId: string,
  options: { addIaTag?: boolean } = {}
): Promise<string> => {
  try {
    // Compression et réduction de la taille de l'image avant l'upload
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1200,
      maxHeight: 1200,
      quality: 73,
      addIaTag: options.addIaTag === true // Ajout du tag IA seulement si demandé
    });

    // Obtenir le storage_id de l'utilisateur
    const storageId = await getOrCreateStorageId(userId);

    // Récupérer le nom et prénom de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError;
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = processedImage.format;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    // Utiliser seulement la première lettre du nom de famille
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_mission_${missionId}_job_partiel_fr_${uniqueId}_${timestamp}.${fileExt}`;
    const filePath = `${storageId}/missions/${missionId}/${fileName}`;

    const { data, error } = await supabase.storage
      .from(MISSION_BUCKET)
      .upload(filePath, processedImage.buffer, {
        contentType: `image/${processedImage.format}`,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la photo de mission', { error, userId, missionId });
      throw new Error('Erreur lors de l\'upload de la photo');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(MISSION_BUCKET, filePath);
    logger.info('Photo de mission uploadée avec succès', {
      userId,
      storageId,
      missionId,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });
    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la photo de mission', { error, userId });
    throw error;
  }
};

// Fonction pour supprimer les photos de mission anciennes automatiquement (90 jours)
export const cleanupOldMissionPhotos = async () => {
  try {
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    // Récupérer toutes les photos à supprimer
    const { data: photosToDelete, error: selectError } = await supabase
      .from('user_mission_photos')
      .select('*, user_missions(titre, description, budget, date_mission, adresse, ville), users(email)')
      .lt('created_at', ninetyDaysAgo.toISOString())
      .neq('photo_url', DEFAULT_AVATAR_URL);

    if (selectError) {
      logger.error('Erreur lors de la récupération des photos à supprimer', { error: selectError });
      return;
    }

    if (!photosToDelete || photosToDelete.length === 0) {
      logger.info('Aucune photo à supprimer');
      return;
    }

    // Préparer les chemins de stockage pour la suppression avec la fonction utilitaire
    const storagePaths = photosToDelete
      .filter(photo => photo.photo_url !== DEFAULT_AVATAR_URL)
      .map(photo => extractFilePathFromUrl(photo.photo_url, MISSION_BUCKET))
      .filter(Boolean) as string[];

    if (storagePaths.length > 0) {
      // Supprimer toutes les photos du storage en une seule opération
      const { error: deleteError } = await supabase.storage
        .from(MISSION_BUCKET)
        .remove(storagePaths);

      if (deleteError) {
        logger.error('Erreur lors de la suppression des photos du storage', { error: deleteError });
        return;
      }
    }

    // Mettre à jour toutes les entrées dans la base de données en une seule opération
    const { error: updateError } = await supabase
      .from('user_mission_photos')
      .update({ photo_url: DEFAULT_AVATAR_URL })
      .in('id', photosToDelete.map(photo => photo.id));

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des URLs dans la base de données', { error: updateError });
      return;
    }

    // Grouper les photos par utilisateur et préparer les données pour l'email
    const userPhotos: { [userId: string]: { email: string | undefined, photos: any[] } } = {};
    
    for (const photo of photosToDelete) {
      const userId = photo.user_id;
      if (!userPhotos[userId]) {
        // Décrypter les données utilisateur pour obtenir l'email
        const decryptedUser = photo.users ? await decryptUserDataAsync(photo.users) : null;
        userPhotos[userId] = {
          email: decryptedUser?.email,
          photos: []
        };
      }
      userPhotos[userId].photos.push(photo);
    }

    // Envoyer les emails de notification
    const emailPromises = Object.entries(userPhotos).map(async ([userId, userData]: [string, any]) => {
      if (!userData.email) {
        logger.error('Email manquant pour l\'utilisateur', { userId });
        return;
      }

      try {
        await emailService.sendPhotosDeletionEmail(userData.email, {
          photoCount: userData.photos.length,
          missions: userData.photos.map((m: any) => ({
            title: m.user_missions.titre,
            description: m.user_missions.description,
            budget: m.user_missions.budget,
            date: m.user_missions.date_mission,
            location: `${m.user_missions.adresse}, ${m.user_missions.ville}`,
            photoCount: 1
          }))
        });

        logger.info('Email de notification envoyé avec succès', {
          userId,
          email: userData.email,
          missionsCount: userData.photos.length
        });
      } catch (error) {
        logger.error('Erreur lors de l\'envoi de l\'email de notification', { error, userId, email: userData.email });
      }
    });

    // Attendre l'envoi de tous les emails
    await Promise.all(emailPromises);

    logger.info(`${photosToDelete.length} photos anciennes ont été nettoyées avec succès`);
  } catch (error) {
    logger.error('Erreur lors du nettoyage des photos anciennes', { error });
  }
};

export const uploadTicketAttachment = async (
  userId: string,
  file: Buffer,
  fileType: string,
  ticketId: string,
  fileName: string
): Promise<string> => {
  try {
    // Compression et réduction de la taille du fichier si c'est une image
    let processedFile = file;
    let processedFileType = fileType;

    if (fileType.startsWith('image/')) {
      const processedImage = await processImage(file, fileType, {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 73
      });
      processedFile = processedImage.buffer;
      processedFileType = `image/${processedImage.format}`;
    }

    // Obtenir le storage_id de l'utilisateur
    const storageId = await getOrCreateStorageId(userId);

    // Récupérer le nom et prénom de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError;
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = fileName.split('.').pop() || 'file';
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const safeFileName = `${sanitizedPrenom}_${firstLetterOfLastName}_ticket_${ticketId}_${uniqueId}_${timestamp}.${fileExt}`;
    const filePath = `${storageId}/tickets/${ticketId}/${safeFileName}`;

    const { data, error } = await supabase.storage
      .from(SUPPORT_TICKET_BUCKET)
      .upload(filePath, processedFile, {
        contentType: processedFileType,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la pièce jointe', { error, userId, ticketId });
      throw new Error('Erreur lors de l\'upload de la pièce jointe');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(SUPPORT_TICKET_BUCKET, filePath);
    logger.info('Pièce jointe uploadée avec succès', {
      userId,
      storageId,
      ticketId,
      filePath,
      fileSize: processedFile.length,
      fileType: processedFileType,
      publicUrl
    });
    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la pièce jointe', { error, userId });
    throw error;
  }
};

export const uploadMessageAttachment = async (
  file: Express.Multer.File | any,
  userId: string = '00000000-0000-0000-0000-000000000000', // UUID par défaut réservé système
  conversationId: string = 'unknown' // Ajout du paramètre conversation_id
): Promise<MessageAttachment | null> => {
  // Déclarer la variable tempFilePath au début de la fonction pour qu'elle soit accessible partout
  let tempFilePath: string | null = null;

  try {
    // Handle both Multer and express-fileupload files
    let fileBuffer: Buffer;
    let fileName: string;
    let fileType: string;
    let fileSize: number;

    if (file.tempFilePath) {
      // express-fileupload avec fichier temporaire
      const fs = require('fs');
      tempFilePath = file.tempFilePath;
      fileBuffer = fs.readFileSync(tempFilePath);
      fileName = file.name;
      fileType = file.mimetype;
      fileSize = file.size;
    } else if (file.data) {
      // express-fileupload avec données en mémoire
      fileBuffer = file.data;
      fileName = file.name;
      fileType = file.mimetype;
      fileSize = file.size;
    } else {
      // Multer
      fileBuffer = file.buffer;
      fileName = file.originalname;
      fileType = file.mimetype;
      fileSize = file.size;
    }

    // Generate a unique message ID
    const messageId = randomBytes(16).toString('hex');

    // Compression et réduction de la taille du fichier si c'est une image
    let processedFile = fileBuffer;
    let processedFileType = fileType;

    if (fileType.startsWith('image/')) {
      const processedImage = await processImage(fileBuffer, fileType, {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 73
      });
      processedFile = processedImage.buffer;
      processedFileType = `image/${processedImage.format}`;
    }

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = fileName.split('.').pop() || 'file';
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    const safeFileName = `message_${uniqueId}_${timestamp}.${fileExt}`;

    // Nouvelle structure simplifiée: conversation_id/nom_fichier
    const filePath = `conversations/${conversationId}/${safeFileName}`;

    // Date d'expiration (90 jours à partir de maintenant)
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 90);

    // Upload du fichier
    const { data, error } = await supabase.storage
      .from(MESSAGE_BUCKET)
      .upload(filePath, processedFile, {
        contentType: processedFileType,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de la pièce jointe du message', { error, messageId, conversationId });
      throw error;
    }

    // Construire l'URL publique via le proxy
    const publicUrl = getPublicUrl(MESSAGE_BUCKET, filePath);

    // Création d'un ID unique pour la pièce jointe
    const attachmentId = randomBytes(16).toString('hex');

    // Retourner les informations sur le fichier uploadé
    return {
      id: attachmentId,
      message_id: messageId,
      file_name: fileName,
      file_size: fileSize,
      mime_type: fileType,
      file_path: filePath,
      public_url: publicUrl,
      expires_at: expiryDate,
      created_at: new Date()
    };
  } catch (error) {
    logger.error('Erreur lors de l\'upload de la pièce jointe du message', { error });
    throw error;
  } finally {
    // Nettoyer le fichier temporaire si nécessaire
    if (tempFilePath) {
      try {
        const fs = require('fs');
        fs.unlinkSync(tempFilePath);
      } catch (cleanupError) {
        logger.error('Erreur lors du nettoyage du fichier temporaire', { cleanupError });
      }
    }
  }
};

// Fonction pour nettoyer les pièces jointes de messages expirées (plus de 90 jours)
export const cleanupOldMessageAttachments = async () => {
  try {
    const BATCH_SIZE = 100; // Taille du lot
    let totalProcessed = 0;
    let totalDeleted = 0;
    let hasMore = true;

    // Date d'expiration (90 jours dans le passé)
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() - 90);

    logger.info(`Début du nettoyage des pièces jointes de messages expirées (avant ${expirationDate.toISOString()})`);

    while (hasMore) {
      // Récupérer un lot de pièces jointes expirées
      const { data: oldAttachments, error: selectError } = await supabase
        .from('user_message_attachments')
        .select('id, file_path, file_name, expires_at, message_id')
        .lt('expires_at', expirationDate.toISOString())
        .range(totalProcessed, totalProcessed + BATCH_SIZE - 1)
        .order('expires_at', { ascending: true });

      if (selectError) {
        logger.error('Erreur lors de la recherche des pièces jointes de messages expirées:', selectError);
        return;
      }

      if (!oldAttachments || oldAttachments.length === 0) {
        if (totalProcessed === 0) {
          logger.info('Aucune pièce jointe de message expirée à supprimer');
        } else {
          logger.info(`Nettoyage terminé. Total traité: ${totalProcessed} pièces jointes, ${totalDeleted} fichiers supprimés`);
        }
        hasMore = false;
        continue;
      }

      logger.info(`Traitement du lot de ${oldAttachments.length} pièces jointes (total traité: ${totalProcessed})`);

      // Log détaillé des pièces jointes à supprimer pour le débogage
      logger.info('Détail des pièces jointes à supprimer:',
        oldAttachments.map(att => ({
          id: att.id,
          file_path: att.file_path,
          expires_at: att.expires_at
        }))
      );

      // Préparer les chemins de fichiers à supprimer
      const filePaths = oldAttachments
        .filter(att => att.file_path)
        .map(att => att.file_path);

      // Supprimer les fichiers du stockage en lot
      if (filePaths.length > 0) {
        logger.info(`Tentative de suppression de ${filePaths.length} fichiers du bucket ${MESSAGE_BUCKET}`);

        // Diviser en lots plus petits pour éviter les timeouts
        const MAX_DELETE_BATCH = 20;
        for (let i = 0; i < filePaths.length; i += MAX_DELETE_BATCH) {
          const pathsBatch = filePaths.slice(i, i + MAX_DELETE_BATCH);

          try {
            const { data: removeData, error: removeError } = await supabase.storage
              .from(MESSAGE_BUCKET)
              .remove(pathsBatch);

            if (removeError) {
              logger.error(`Erreur lors de la suppression du lot ${i / MAX_DELETE_BATCH + 1}:`,
                { error: removeError, paths: pathsBatch });
            } else {
              const deletedCount = removeData?.length || 0;
              totalDeleted += deletedCount;
              logger.info(`Lot ${i / MAX_DELETE_BATCH + 1}: ${deletedCount} fichiers supprimés du bucket ${MESSAGE_BUCKET}`);
            }
          } catch (batchError) {
            logger.error(`Exception lors de la suppression du lot ${i / MAX_DELETE_BATCH + 1}:`,
              { error: batchError, paths: pathsBatch });
          }

          // Petite pause entre les lots de suppression
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Marquer les pièces jointes comme expirées en lot
      const { error: updateError } = await supabase
        .from('user_message_attachments')
        .update({ is_expired: true })
        .in('id', oldAttachments.map(att => att.id));

      if (updateError) {
        logger.error('Erreur lors de la mise à jour des statuts:', updateError);
      }

      // Supprimer les entrées de la base de données en lot
      const { error: deleteError } = await supabase
        .from('user_message_attachments')
        .delete()
        .in('id', oldAttachments.map(att => att.id));

      if (deleteError) {
        logger.error('Erreur lors de la suppression des entrées:', deleteError);
      } else {
        logger.info(`${oldAttachments.length} entrées supprimées de la base de données`);
      }

      totalProcessed += oldAttachments.length;

      // Si on a reçu moins que la taille du lot, c'est qu'il n'y en a plus
      if (oldAttachments.length < BATCH_SIZE) {
        hasMore = false;
        logger.info(`Nettoyage terminé. Total traité: ${totalProcessed} pièces jointes, ${totalDeleted} fichiers supprimés`);
      }

      // Petite pause entre les lots pour ne pas surcharger la base de données
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    logger.error('Erreur lors du nettoyage des pièces jointes de messages expirées:', error);
  }
};

/**
 * Upload d'un document de vérification entreprise (Kbis, Assurance, Identité, etc.)
 * @param userId string
 * @param file Buffer
 * @param fileType string (ex: 'application/pdf', 'image/jpeg', ...)
 * @param fileName string (nom original)
 * @returns URL publique du fichier
 */
export const uploadEntrepriseVerificationDocument = async (
  userId: string,
  file: Buffer,
  fileType: string,
  fileName: string
): Promise<string> => {
  try {
    // Vérification du type MIME autorisé
    const allowedMimes = [
      'image/jpeg', 'image/png', 'image/webp', 'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    if (!allowedMimes.includes(fileType)) {
      logger.warn('Type de fichier non autorisé pour vérification entreprise', { fileType, userId });
      throw new Error('Type de fichier non autorisé');
    }

    // Compression si image
    let processedFile = file;
    let processedFileType = fileType;
    if (fileType.startsWith('image/')) {
      const { processImage } = await import('../utils/imageProcessor');
      const processedImage = await processImage(file, fileType, {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 73
      });
      processedFile = processedImage.buffer;
      processedFileType = `image/${processedImage.format}`;
    }

    // Obtenir le storage_id et les infos utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('storage_id, nom, prenom')
      .eq('user_id', userId)
      .single();
    if (userError || !userData?.storage_id) {
      logger.error('Erreur lors de la récupération des infos utilisateur', { error: userError, userId });
      throw userError || new Error('storage_id non trouvé');
    }
    
    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);
    
    const storageId = userData.storage_id;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileExt = fileName.split('.').pop() || 'file';
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    // Déduire le type de document à partir du nom ou du mimetype
    let typeDoc = 'doc';
    if (fileType.includes('pdf')) typeDoc = 'pdf';
    else if (fileType.includes('word')) typeDoc = 'word';
    else if (fileType.includes('image')) typeDoc = 'img';
    // Nom de fichier descriptif
    const safeFileName = `${sanitizedPrenom}_${firstLetterOfLastName}_${typeDoc}_verif_job_partiel_fr_${uniqueId}_${timestamp}.${fileExt}`;
    const filePath = `${storageId}/entreprise_verif/${safeFileName}`;

    // Upload du fichier
    const { error } = await supabase.storage
      .from(ENTREPRISE_VERIF_BUCKET)
      .upload(filePath, processedFile, {
        contentType: processedFileType,
        upsert: true
      });
    if (error) throw error;

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(ENTREPRISE_VERIF_BUCKET, filePath);
    logger.info('Document de vérification entreprise uploadé', { userId, storageId, filePath, fileType: processedFileType, publicUrl });
    return publicUrl;
  } catch (error) {
    logger.error('Erreur upload doc vérification entreprise', { error, userId });
    throw error;
  }
};

/**
 * Suppression d'un document de vérification entreprise
 */
export const deleteEntrepriseVerificationDocument = async (
  userId: string,
  filePath: string
): Promise<void> => {
  try {
    // filePath = chemin relatif dans le bucket (ex: storageId/nomfichier.pdf)
    const { error } = await supabase.storage
      .from(ENTREPRISE_VERIF_BUCKET)
      .remove([filePath]);
    if (error) throw error;
  } catch (error) {
    logger.error('Erreur suppression doc vérification entreprise', { error, userId, filePath });
    throw error;
  }
};

/**
 * Suppression de tous les documents de vérification entreprise d'un utilisateur
 */
export const deleteAllEntrepriseVerificationDocuments = async (userId: string): Promise<number> => {
  // 1. Récupérer tous les documents de l'utilisateur
  const { data: docs, error } = await supabase
    .from('entreprise_verification_documents')
    .select('id, file_url')
    .eq('user_id', userId);
  if (error) throw error;
  if (!docs || docs.length === 0) return 0;

  // 2. Supprimer les fichiers du storage
  for (const doc of docs) {
    if (doc.file_url) {
      const match = doc.file_url.match(/entreprise_verification\/(.+)/);
      if (match && match[1]) {
        try {
          await deleteEntrepriseVerificationDocument(userId, match[1]);
        } catch (e) {
          // On continue même si une suppression échoue
        }
      }
    }
  }

  // 3. Supprimer les entrées SQL
  const { error: deleteError } = await supabase
    .from('entreprise_verification_documents')
    .delete()
    .eq('user_id', userId);
  if (deleteError) throw deleteError;
  return docs.length;
};

/**
 * Upload temporaire d'une image pour la modération
 * Cette fonction permet d'uploader une image temporairement pour la modération
 * Si l'image passe la modération, elle peut être déplacée vers son emplacement final
 * Sinon, elle sera automatiquement supprimée
 *
 * @param userId ID de l'utilisateur qui upload l'image
 * @param file Buffer de l'image
 * @param fileType Type MIME de l'image
 * @param addIaTag Ajout du paramètre pour watermark IA
 * @returns URL publique de l'image temporaire
 */
export const uploadTemporaryImageForModeration = async (
  userId: string,
  file: Buffer,
  fileType: string,
  addIaTag: boolean = false // Ajout du paramètre pour watermark IA
): Promise<{ publicUrl: string, filePath: string }> => {
  try {
    // Compression et optimisation de l'image
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1200,
      maxHeight: 1200,
      quality: 73,
      addIaTag // On transmet le paramètre
    });

    // Créer un nom de fichier unique
    const fileExt = processedImage.format;
    const uniqueId = randomBytes(8).toString('hex'); // ID plus long pour éviter les collisions
    const timestamp = Date.now();
    const fileName = `temp_moderation_${userId}_${uniqueId}_${timestamp}.${fileExt}`;

    // Chemin dans le bucket temporaire
    // Structure: /temp_moderation/userId/timestamp_uniqueId.ext
    const filePath = `${userId}/${fileName}`;

    // Upload du fichier vers Supabase Storage avec le type MIME correct
    const contentType = `image/${processedImage.format}`;

    const { data, error } = await supabase.storage
      .from(TEMP_MODERATION_BUCKET)
      .upload(filePath, processedImage.buffer, {
        contentType: contentType,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload temporaire pour modération', { error, userId });
      throw new Error('Erreur lors de l\'upload temporaire de l\'image');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(TEMP_MODERATION_BUCKET, filePath);

    logger.info('Image temporaire uploadée avec succès pour modération', {
      userId,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });

    return { publicUrl, filePath };
  } catch (error) {
    logger.error('Erreur lors de l\'upload temporaire pour modération', { error, userId });
    throw error;
  }
};

/**
 * Supprime une image temporaire du bucket de modération
 * Si le dossier utilisateur devient vide après la suppression, tente de supprimer le dossier également
 *
 * @param userId ID de l'utilisateur
 * @param filePath Chemin du fichier dans le bucket
 */
export const deleteTemporaryModerationImage = async (
  userId: string,
  filePath: string
): Promise<void> => {
  try {
    // Extraire le dossier utilisateur du chemin du fichier
    const folderPath = filePath.split('/')[0];

    // Supprimer le fichier
    const { error } = await supabase.storage
      .from(TEMP_MODERATION_BUCKET)
      .remove([filePath]);

    if (error) {
      logger.error('Erreur lors de la suppression de l\'image temporaire', { error, userId, filePath });
      throw error;
    }

    logger.info('Image temporaire supprimée avec succès', { userId, filePath });

    // Vérifier si le dossier est maintenant vide
    const { data: remainingFiles, error: listError } = await supabase.storage
      .from(TEMP_MODERATION_BUCKET)
      .list(folderPath);

    if (listError) {
      logger.error('Erreur lors de la vérification du dossier après suppression', { error: listError, userId, folderPath });
      return; // Ne pas bloquer le processus en cas d'erreur
    }

    // Si le dossier est vide, on le considère comme supprimé
    if (!remainingFiles || remainingFiles.length === 0) {
      logger.info(`Dossier ${folderPath} vidé et supprimé avec succès`, { userId });
    }
  } catch (error) {
    logger.error('Erreur lors de la suppression de l\'image temporaire', { error, userId, filePath });
    throw error;
  }
};

/**
 * Nettoie les images temporaires de modération qui sont plus anciennes qu'une certaine durée
 * Cette fonction est destinée à être exécutée périodiquement par une tâche cron
 *
 * @param maxAgeHours Âge maximum des images en heures (par défaut: 24 heures)
 * @returns Nombre d'images supprimées
 */
/**
 * Upload une image pour une carte de visite ou un flyer
 * @param userId ID de l'utilisateur
 * @param file Buffer de l'image
 * @param fileType Type MIME de l'image
 * @param templateId ID du template (carte de visite ou flyer)
 * @param options Options supplémentaires (tag IA, etc.)
 * @returns URL publique de l'image
 */
export const uploadCardEditorImage = async (
  userId: string,
  file: Buffer,
  fileType: string,
  templateId: string,
  options: { addIaTag?: boolean } = {}
): Promise<string> => {
  try {
    // Compression et réduction de la taille de l'image
    // IMPORTANT : Ne jamais ajouter le tag IA pour les images de cartes de visite/flyers (conformément au cahier des charges)
    const processedImage = await processImage(file, fileType, {
      maxWidth: 1200,
      maxHeight: 1200,
      quality: 73,
      addIaTag: options.addIaTag === true // Ajout du tag IA seulement si explicitement demandé (jamais pour card_editor)
    });

    // Obtenir le storage_id de l'utilisateur
    const storageId = await getOrCreateStorageId(userId);

    // Vérification stricte du storageId
    if (!storageId || typeof storageId !== 'string' || storageId.trim() === '') {
      logger.error('storageId manquant ou invalide lors de l\'upload d\'image card editor', { userId, templateId });
      throw new Error('Impossible de récupérer vos paramètres de profil. Veuillez réessayer.');
    }

    // Récupérer le nom et prénom de l'utilisateur
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('nom, prenom')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur', { error: userError, userId });
      throw userError;
    }

    // Déchiffrer les données de profil
    const decryptedUserData = await decryptProfilDataAsync(userData);

    // Créer un nom de fichier sécurisé et descriptif
    const fileExt = processedImage.format;
    const sanitizedNom = (decryptedUserData?.nom || 'utilisateur').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const sanitizedPrenom = (decryptedUserData?.prenom || 'jobpartiel').toLowerCase().replace(/[^a-z0-9]/g, '_');
    const uniqueId = randomBytes(4).toString('hex');
    const timestamp = Date.now();
    // Utiliser seulement la première lettre du nom de famille
    const firstLetterOfLastName = sanitizedNom.charAt(0);
    const fileName = `${sanitizedPrenom}_${firstLetterOfLastName}_card_editor_${templateId || 'default'}_${uniqueId}_${timestamp}.${fileExt}`;
    const filePath = templateId
      ? `${storageId}/card_editor/${templateId}/${fileName}`
      : `${storageId}/card_editor/${fileName}`;

    // Upload du fichier vers Supabase Storage
    const { data, error } = await supabase.storage
      .from(CARD_EDITOR_BUCKET)
      .upload(filePath, processedImage.buffer, {
        contentType: `image/${processedImage.format}`,
        duplex: 'half',
        upsert: true
      });

    if (error) {
      logger.error('Erreur lors de l\'upload de l\'image pour carte de visite/flyer', { error, userId, templateId });
      throw new Error('Erreur lors de l\'upload de l\'image');
    }

    // Récupérer l'URL publique via le proxy
    const publicUrl = getPublicUrl(CARD_EDITOR_BUCKET, filePath);

    logger.info('Image pour carte de visite/flyer uploadée avec succès', {
      userId,
      storageId,
      templateId,
      filePath,
      fileSize: processedImage.buffer.length,
      fileType: processedImage.format,
      publicUrl
    });

    return publicUrl;
  } catch (error) {
    logger.error('Erreur lors de l\'upload de l\'image pour carte de visite/flyer', { error, userId });
    throw error;
  }
};

// Nettoyage des images temporaires de modération du bucket temporaire "temp_moderation" (tâche cron) toutes les 24h
export const cleanupTemporaryModerationImages = async (maxAgeHours: number = 24): Promise<number> => {
  try {
    logger.info('Début du nettoyage des images temporaires de modération', { maxAgeHours });

    // Récupérer tous les dossiers utilisateurs dans le bucket temporaire
    const { data: folders, error: foldersError } = await supabase.storage
      .from(TEMP_MODERATION_BUCKET)
      .list();

    if (foldersError) {
      logger.error('Erreur lors de la récupération des dossiers utilisateurs', { error: foldersError });
      throw foldersError;
    }

    if (!folders || folders.length === 0) {
      logger.info('Aucun dossier utilisateur trouvé dans le bucket temporaire');
      return 0;
    }

    logger.info(`${folders.length} dossiers utilisateurs trouvés dans le bucket temporaire`);

    let totalDeleted = 0;

    // Pour chaque dossier utilisateur
    for (const folder of folders) {
      if (!folder.name) continue;

      // Récupérer tous les fichiers dans le dossier
      const { data: files, error: filesError } = await supabase.storage
        .from(TEMP_MODERATION_BUCKET)
        .list(folder.name);

      if (filesError) {
        logger.error('Erreur lors de la récupération des fichiers', { error: filesError, folder: folder.name });
        continue; // Passer au dossier suivant en cas d'erreur
      }

      logger.info(`${files.length} fichiers trouvés dans le dossier ${folder.name}`);

      // Calculer la date limite
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - maxAgeHours);

      // Filtrer les fichiers plus anciens que la date limite
      const oldFiles = files.filter(file => {
        if (!file.name) return false;

        // Extraire le timestamp du nom du fichier (format: temp_moderation_userId_uniqueId_timestamp.ext)
        const timestampMatch = file.name.match(/_(\d+)\.[^.]+$/);
        let fileTimestamp = undefined;
        let fileDate = undefined;
        let isOld = false;
        if (timestampMatch && timestampMatch[1]) {
          fileTimestamp = parseInt(timestampMatch[1]);
          if (!isNaN(fileTimestamp)) {
            fileDate = new Date(fileTimestamp);
            isOld = fileDate < cutoffDate;
          }
        }
        return isOld;
      });

      if (oldFiles.length === 0) {
        logger.info(`Aucun fichier ancien trouvé dans le dossier ${folder.name}`);
        continue;
      }

      logger.info(`${oldFiles.length} fichiers anciens trouvés dans le dossier ${folder.name}`);

      // Supprimer les fichiers anciens
      const filesToDelete = oldFiles.map(file => `${folder.name}/${file.name}`);

      // Supprimer par lots de 100 pour éviter les erreurs
      const batchSize = 100;
      for (let i = 0; i < filesToDelete.length; i += batchSize) {
        const batch = filesToDelete.slice(i, i + batchSize);

        const { error: deleteError } = await supabase.storage
          .from(TEMP_MODERATION_BUCKET)
          .remove(batch);

        if (deleteError) {
          logger.error('Erreur lors de la suppression des fichiers anciens', {
            error: deleteError,
            folder: folder.name,
            batchSize: batch.length,
            batchStart: i
          });
        } else {
          totalDeleted += batch.length;
          logger.info(`${batch.length} fichiers anciens supprimés du dossier ${folder.name}`);
        }
      }

      // Vérifier si tous les fichiers ont été supprimés
      if (oldFiles.length === files.length) {
        // Si tous les fichiers ont été supprimés, vérifier si le dossier est maintenant vide
        const { data: remainingFiles, error: checkError } = await supabase.storage
          .from(TEMP_MODERATION_BUCKET)
          .list(folder.name);

        if (!checkError && (!remainingFiles || remainingFiles.length === 0)) {
          // Le dossier est vide, on le considère comme supprimé
          logger.info(`Dossier ${folder.name} vidé et supprimé avec succès`);
        }
      }
    }

    logger.info(`Nettoyage des images temporaires terminé, ${totalDeleted} fichiers supprimés au total`);
    return totalDeleted;
  } catch (error) {
    logger.error('Erreur lors du nettoyage des images temporaires', { error });
    throw error;
  }
};

// Fonction pour supprimer toutes les images d'un template de carte de visite/flyer
export const deleteReviewPhotos = async (userId: string, photoUrls: string[]): Promise<void> => {
  try {
    if (!photoUrls || photoUrls.length === 0) {
      return;
    }

    const filePaths: string[] = [];

    // Extraire tous les chemins de fichiers
    for (const photoUrl of photoUrls) {
      const filePath = extractFilePathFromUrl(photoUrl, REVIEW_PHOTOS_BUCKET);
      if (filePath) {
        filePaths.push(filePath);
      } else {
        logger.warn('Impossible d\'extraire le chemin du fichier pour la suppression de la photo d\'avis', { userId, photoUrl });
      }
    }

    if (filePaths.length === 0) {
      logger.warn('Aucun chemin de fichier valide trouvé pour la suppression', { userId, photoUrls });
      return;
    }

    // Supprimer tous les fichiers en une seule opération
    const { error } = await supabase.storage
      .from(REVIEW_PHOTOS_BUCKET)
      .remove(filePaths);

    if (error) {
      logger.error('Erreur lors de la suppression des photos d\'avis', { error, userId, filePaths });
      throw new Error('Erreur lors de la suppression des photos');
    } else {
      logger.info('Photos d\'avis supprimées avec succès', { userId, filePaths, count: filePaths.length });
    }
  } catch (error) {
    logger.error('Erreur lors de la suppression des photos d\'avis', { error, userId });
    throw error;
  }
};



export const deleteCardEditorImages = async (
  storageId: string,
  templateId: string
): Promise<void> => {
  try {
    logger.info('Début de la suppression des images du template (card editor) ', { storageId, templateId });

    // Lister tous les fichiers du dossier du template
    const folderPath = `${storageId}/card_editor/${templateId}`;
    const { data: files, error: listError } = await supabase.storage
      .from(CARD_EDITOR_BUCKET)
      .list(folderPath);

    if (listError) {
      logger.error('Erreur lors de la récupération des fichiers à supprimer (card editor)', { error: listError, storageId, templateId });
      return;
    }

    if (!files || files.length === 0) {
      logger.info('Aucun fichier à supprimer pour ce template (card editor)', { storageId, templateId });
      return;
    }

    // Construire la liste des chemins à supprimer, en excluant l'avatar par défaut
    const filesToDelete = files
      .filter(file => !file.name.includes('avatar-defaut-jobpartiel.jpg'))
      .map(file => `${folderPath}/${file.name}`);
    if (filesToDelete.length === 0) {
      logger.info('Aucun fichier à supprimer (hors avatar par défaut) pour ce template (card editor)', { storageId, templateId });
      return;
    }
    const { error: deleteError } = await supabase.storage
      .from(CARD_EDITOR_BUCKET)
      .remove(filesToDelete);

    if (deleteError) {
      logger.error('Erreur lors de la suppression des images du card editor', { error: deleteError, storageId, templateId });
    } else {
      logger.info('Images du card editor supprimées avec succès', { storageId, templateId, count: filesToDelete.length });
    }
  } catch (error) {
    logger.error('Erreur lors de la suppression des images du card editor', { error, storageId, templateId });
  }
};

// Fonction pour supprimer les images NON UTILISÉES d'un template de carte de visite/flyer
export const deleteUnusedCardEditorImages = async (
  storageId: string,
  templateId: string,
  usedImagePublicUrls: string[] // Liste des URLs publiques complètes des images utilisées dans le template_data
): Promise<void> => {
  try {
    logger.info('Début de la suppression des images NON UTILISÉES du template (card editor)', { storageId, templateId, usedImageCount: usedImagePublicUrls.length });

    const folderPath = `${storageId}/card_editor/${templateId}`;
    const { data: filesInBucket, error: listError } = await supabase.storage
      .from(CARD_EDITOR_BUCKET)
      .list(folderPath);

    if (listError) {
      logger.error('Erreur lors de la récupération des fichiers du bucket (deleteUnused)', { error: listError, storageId, templateId });
      return; // Ne rien faire si on ne peut pas lister
    }

    if (!filesInBucket || filesInBucket.length === 0) {
      logger.info('Aucun fichier trouvé dans le bucket pour ce template (deleteUnused)', { storageId, templateId });
      return;
    }

    // Convertir les URLs publiques utilisées en chemins de fichiers relatifs au bucket
    // ex: https://<project>.supabase.co/storage/v1/object/public/carte_visite_et_flyer/storageId/card_editor/templateId/image.jpg
    //  -> storageId/card_editor/templateId/image.jpg
    const usedFilePathsInBucket = usedImagePublicUrls.map(url => {
      const pathPrefix = `${CARD_EDITOR_BUCKET}/`;
      const pathWithBucket = url.substring(url.indexOf(pathPrefix));
      return pathWithBucket.substring(pathPrefix.length);
    }).filter(path => path.startsWith(folderPath)); // S'assurer qu'ils appartiennent bien au dossier du template

    const filesToDelete = filesInBucket
      .map(fileInBucket => `${folderPath}/${fileInBucket.name}`)
      .filter(fullPathInBucket =>
        !usedFilePathsInBucket.includes(fullPathInBucket) &&
        !fullPathInBucket.includes('avatar-defaut-jobpartiel.jpg')
      );

    if (filesToDelete.length === 0) {
      logger.info('Aucune image non utilisée à supprimer pour ce template', { storageId, templateId, usedFilePathsInBucket });
      return;
    }

    const { error: deleteError } = await supabase.storage
      .from(CARD_EDITOR_BUCKET)
      .remove(filesToDelete);

    if (deleteError) {
      logger.error('Erreur lors de la suppression des images non utilisées', { error: deleteError, storageId, templateId, count: filesToDelete.length });
    } else {
      logger.info('Images non utilisées supprimées avec succès', { storageId, templateId, count: filesToDelete.length, deletedFiles: filesToDelete });
    }
  } catch (error) {
    logger.error('Erreur majeure lors de la suppression des images non utilisées', { error, storageId, templateId });
  }
};