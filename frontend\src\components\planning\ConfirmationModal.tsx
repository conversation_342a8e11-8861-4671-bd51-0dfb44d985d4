import React from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogContentText, 
  DialogActions, 
  Button, 
  IconButton,
  Typography,
  Box
} from '@mui/material';
import { X, AlertTriangle } from 'lucide-react';
import DOMPurify from 'dompurify';
import ModalPortal from '../../components/ModalPortal';

interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  isManualMission?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = 'Confirmer',
  cancelLabel = 'Annuler',
  isManualMission = false
}) => {
  // Sanitize les données affichées
  const sanitizedTitle = DOMPurify.sanitize(title);
  const sanitizedMessage = DOMPurify.sanitize(message);
  const sanitizedConfirmLabel = DOMPurify.sanitize(confirmLabel);
  const sanitizedCancelLabel = DOMPurify.sanitize(cancelLabel);

  const handleClose = () => {
    onClose();
  };

  return (
    <ModalPortal isOpen={open} onBackdropClick={handleClose}>
      <Dialog
        open={true}
        maxWidth="xs"
        fullWidth
        hideBackdrop={true}
        onClose={(event, reason) => {
          if (reason === 'backdropClick') {
            handleClose();
          }
        }}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
            overflow: 'auto',
            overflowY: 'scroll',
            m: {
              xs: '8px',
              sm: '16px',
              md: '32px'
            },
            minWidth: '400px',
            width: {
              xs: 'calc(100% - 16px)',
              sm: 'auto'
            },
            maxHeight: {
              xs: 'calc(100% - 16px)',
              sm: 'auto'
            },
            '@media (max-width: 450px)': {
              minWidth: 'calc(100% - 16px)',
              margin: '8px'
            }
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          pt: { xs: 1.5, sm: 2 }, 
          pb: { xs: 0.5, sm: 1 },
          px: { xs: 2, sm: 3 }
        }}>
          <Typography component="div" sx={{ 
            fontWeight: 600,
            fontSize: { xs: '1.15rem', sm: '1.25rem' },
            color: 'inherit'
          }}>
            {sanitizedTitle}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            size="small"
            sx={{
              color: '#777',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
            }}
          >
            <X size={20} />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ 
          pt: { xs: 1.5, sm: 2 }, 
          px: { xs: 2, sm: 3 },
          pb: { xs: 1.5, sm: 2 }
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'flex-start', 
            gap: { xs: 1.5, sm: 2 },
            mb: 2
          }}>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center',
              width: { xs: 36, sm: 40 }, 
              height: { xs: 36, sm: 40 }, 
              borderRadius: '50%', 
              backgroundColor: isManualMission ? 'rgba(211, 47, 47, 0.1)' : 'rgba(255, 107, 44, 0.1)'
            }}>
              <AlertTriangle size={isManualMission ? 20 : 22} color={isManualMission ? '#d32f2f' : '#FF6B2C'} />
            </Box>
            <DialogContentText sx={{ 
              color: '#555', 
              flex: 1,
              fontSize: { xs: '0.9rem', sm: '1rem' }
            }}>
              {sanitizedMessage}
            </DialogContentText>
          </Box>
          
          {isManualMission && (
            <Box sx={{ 
              backgroundColor: 'rgba(211, 47, 47, 0.05)', 
              p: { xs: 1.5, sm: 2 }, 
              borderRadius: '8px',
              mb: 1
            }}>
              <Typography variant="body2" sx={{ 
                color: '#d32f2f', 
                fontWeight: 500,
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}>
                Attention : Cette action est définitive et ne peut pas être annulée.
              </Typography>
            </Box>
          )}
          
          {!isManualMission && (
            <Box sx={{ 
              backgroundColor: 'rgba(255, 107, 44, 0.05)', 
              p: { xs: 1.5, sm: 2 }, 
              borderRadius: '8px',
              mb: 1
            }}>
              <Typography variant="body2" sx={{ 
                color: '#666',
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}>
                Vous pourrez toujours restaurer cette mission plus tard en utilisant le bouton "Missions masquées".
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ 
          px: { xs: 2, sm: 3 }, 
          pb: { xs: 2, sm: 3 }, 
          pt: { xs: 0.5, sm: 1 },
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: 'stretch',
          gap: { xs: 1, sm: 1 }
        }}>
          <Button 
            onClick={handleClose}
            variant="outlined"
            sx={{
              borderColor: '#ddd',
              color: '#666',
              '&:hover': {
                borderColor: '#ccc',
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
              textTransform: 'none',
              fontWeight: 500,
              borderRadius: '8px',
              width: { xs: '100%', sm: 'auto' },
              order: { xs: 2, sm: 1 }
            }}
          >
            {sanitizedCancelLabel}
          </Button>
          <Button 
            onClick={onConfirm}
            variant="contained"
            sx={{
              backgroundColor: isManualMission ? '#d32f2f' : '#FF6B2C',
              '&:hover': {
                backgroundColor: isManualMission ? '#b71c1c' : '#FF7A35',
              },
              textTransform: 'none',
              fontWeight: 500,
              borderRadius: '8px',
              boxShadow: isManualMission 
                ? '0 4px 12px rgba(211, 47, 47, 0.15)' 
                : '0 4px 12px rgba(255, 107, 44, 0.15)',
              width: { xs: '100%', sm: 'auto' },
              order: { xs: 1, sm: 2 }
            }}
          >
            {sanitizedConfirmLabel}
          </Button>
        </DialogActions>
      </Dialog>
    </ModalPortal>
  );
};

export default ConfirmationModal; 