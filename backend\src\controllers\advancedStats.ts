import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { decryptProfilDataAsync } from '../utils/encryption';

// Cache pour les statistiques avancées
const ADVANCED_STATS_CACHE_PREFIX = 'advanced_stats:';
const CACHE_TTL_STATS = 1800; // 30 minutes pour les statistiques
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';

// Interface pour les statistiques de facturation
interface InvoiceStats {
  total: number;
  montantTotal: number;
  parStatut: {
    [key: string]: number;
  };
  parType: {
    [key: string]: number;
  };
  evolution: Array<{
    mois: string;
    montant: number;
    nombre: number;
  }>;
}

export const getAdvancedStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium } = await getUserSubscriptionLimits(userId);
    
    // Si l'utilisateur n'a pas d'abonnement premium, retourner des données par défaut
    if (!isPremium) {
      logger.info(`Utilisateur ${userId} sans abonnement premium tente d'accéder aux statistiques avancées - Retour des données par défaut`);
      res.json(generateDefaultStats());
      return;
    }

    // Récupérer la période depuis la query (7, 30, 90 jours)
    const timeRange = req.query.timeRange ? String(req.query.timeRange) : '30';
    const cacheKey = `${ADVANCED_STATS_CACHE_PREFIX}${userId}:${timeRange}`;

    // Vérifier si les stats sont en cache
    const cachedStats = await redis.get(cacheKey);
    if (cachedStats) {
      res.json(JSON.parse(cachedStats));
      return;
    }

    // Calculer la date de début en fonction de la période
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeRange));
    const startDateISO = startDate.toISOString();

    // Définir la date de fin (aujourd'hui)
    const endDate = new Date();
    const endDateISO = endDate.toISOString();

    // Récupérer d'abord les jobbeurs pour les profils
    const { data: clientIds } = await supabase
      .from('user_mission_candidature')
      .select('jobbeur_id, mission:user_missions!inner(user_id)')
      .eq('statut', 'acceptée')
      .eq('mission.user_id', userId);
      
    // Liste des IDs de jobbeurs pour la requête de profils
    const jobbeurIds = clientIds?.map(item => item.jobbeur_id) || [];

    // Requêtes parallèles pour obtenir toutes les statistiques
    const [
      totalClientsQuery,
      newClientsQuery,
      clientSatisfactionQuery,
      servicesPopulairesQuery,
      missionsPopulairesQuery,
      heuresPopulairesQuery,
      missionsQuery,
      invoicesQuery // Ajout de la requête pour les factures
    ] = await Promise.all([
      // Total des clients uniques (uniquement missions acceptées)
      supabase
        .from('user_mission_candidature')
        .select('jobbeur_id')
        .eq('statut', 'acceptée')
        .eq('jobbeur_id', userId),

      // Nouveaux clients uniques sur la période
      supabase
        .from('user_mission_candidature')
        .select('jobbeur_id')
        .eq('statut', 'acceptée')
        .eq('jobbeur_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Satisfaction client (moyenne des notes)
      supabase
        .from('user_reviews')
        .select('note')
        .eq('target_user_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Services populaires (candidatures)
      supabase
        .from('user_mission_candidature')
        .select(`
          mission:user_missions!inner(
            category_id,
            reviews:user_reviews!fk_mission(note)
          ),
          montant_paiement,
          created_at
        `)
        .eq('statut', 'acceptée')
        .eq('mission.user_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Services populaires (mes missions)
      supabase
        .from('user_missions')
        .select(`
          category_id,
          created_at,
          budget,
          reviews:user_reviews!fk_mission(note)
        `)
        .eq('user_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Heures création des missions
      supabase
        .from('user_missions')
        .select('created_at')
        .eq('user_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),

      // Statistiques des missions
      supabase
        .from('user_missions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),
        
      // Statistiques des factures
      supabase
        .from('invoices')
        .select('*')
        .eq('user_id', userId)
    ]);

    // Récupérer des infos sur les profils clients (code postal)
    const profilsQuery = jobbeurIds.length > 0
      ? await supabase
          .from('user_profil')
          .select('ville, code_postal')
          .in('user_id', jobbeurIds)
      : { data: [], error: null };

    // Déchiffrer les données de profil
    const decryptedProfils = profilsQuery.data ?
      await Promise.all(profilsQuery.data.map(profil => decryptProfilDataAsync(profil))) :
      [];

    // Récupérer le taux de complétion des missions
    const missionsCompleteesQuery = await supabase
      .from('user_mission_candidature')
      .select('statut, created_at, mission:user_missions!inner(user_id)')
      .eq('mission.user_id', userId)
      .gte('created_at', startDateISO.slice(0, 7) + '-01');

    // Temps de réponse moyen aux candidatures
    const tempsReponseQuery = await supabase
      .from('user_mission_candidature')
      .select(`
        created_at,
        first_response_date
      `)
      .eq('jobbeur_id', userId)
      .not('first_response_date', 'is', null)
      .gte('created_at', startDateISO)
      .lte('created_at', endDateISO);

    // Vérifier les erreurs
    const errors = [
      totalClientsQuery.error,
      newClientsQuery.error,
      clientSatisfactionQuery.error,
      servicesPopulairesQuery.error,
      missionsPopulairesQuery.error,
      heuresPopulairesQuery.error,
      missionsQuery.error,
      profilsQuery.error,
      missionsCompleteesQuery.error,
      tempsReponseQuery.error,
      invoicesQuery.error // Ajout de la vérification d'erreur pour les factures
    ].filter(error => error !== null);

    if (errors.length > 0) {
      logger.error('Erreur lors de la récupération des statistiques:', errors);
      res.status(500).json({ error: 'Erreur lors de la récupération des statistiques' });
      return;
    }

    // Traitement des données
    // console.log('Raw total clients data:', totalClientsQuery.data);
    const uniqueClients = totalClientsQuery.data ? new Set(totalClientsQuery.data.map(item => item.jobbeur_id)) : new Set();
    // console.log('Unique clients set:', uniqueClients);
    const totalClients = uniqueClients.size;
    // console.log('Total clients calculated:', totalClients);

    // console.log('Raw new clients data:', newClientsQuery.data);
    const nouveauxClients = new Set(newClientsQuery.data?.map(item => item.jobbeur_id)).size || 0;
    // console.log('New clients calculated:', nouveauxClients);

    // Calcul de la satisfaction client
    const reviews = clientSatisfactionQuery.data || [];
    const tauxSatisfaction = reviews.length > 0
      ? Math.round((reviews.reduce((acc, rev) => acc + rev.note, 0) / reviews.length) * 20) // Conversion en pourcentage
      : 0;

    // Traitement des services populaires (candidatures)
    const servicesData = (servicesPopulairesQuery.data as unknown as any[]) || [];
    const servicesCount: Record<string, { 
      reservations: number, 
      satisfaction: number,
      tarifAverage: number,
      totalNotes: number
    }> = {};
    
    servicesData.forEach(service => {
      const categoryId = service.mission?.category_id;
      if (categoryId) {
        if (!servicesCount[categoryId]) {
          servicesCount[categoryId] = { 
            reservations: 0, 
            satisfaction: 0,
            tarifAverage: 0,
            totalNotes: 0
          };
        }
        servicesCount[categoryId].reservations++;
        servicesCount[categoryId].tarifAverage += service.montant_paiement || 0;
        
        // Ajouter les notes si disponibles
        if (service.mission?.reviews && service.mission.reviews.length > 0) {
          service.mission.reviews.forEach((review: { note: number }) => {
            servicesCount[categoryId].satisfaction += review.note;
            servicesCount[categoryId].totalNotes++;
          });
        }
      }
    });

    // Traitement des services populaires (mes missions)
    const mesMissionsData = (missionsPopulairesQuery.data as any[]) || [];
    const mesMissionsCount: Record<string, { 
      reservations: number, 
      satisfaction: number,
      tarifAverage: number,
      totalNotes: number
    }> = {};
    
    mesMissionsData.forEach(mission => {
      const categoryId = mission.category_id;
      if (categoryId) {
        if (!mesMissionsCount[categoryId]) {
          mesMissionsCount[categoryId] = { 
            reservations: 0, 
            satisfaction: 0,
            tarifAverage: 0,
            totalNotes: 0
          };
        }
        mesMissionsCount[categoryId].reservations++;
        mesMissionsCount[categoryId].tarifAverage += mission.budget || 0;
        
        // Ajouter les notes si disponibles
        if (mission.reviews && mission.reviews.length > 0) {
          mission.reviews.forEach((review: { note: number }) => {
            mesMissionsCount[categoryId].satisfaction += review.note;
            mesMissionsCount[categoryId].totalNotes++;
          });
        }
      }
    });

    // Calculer les moyennes pour chaque service
    Object.keys(servicesCount).forEach(key => {
      if (servicesCount[key].reservations > 0) {
        servicesCount[key].tarifAverage = Math.round(servicesCount[key].tarifAverage / servicesCount[key].reservations);
      }
    });

    Object.keys(mesMissionsCount).forEach(key => {
      if (mesMissionsCount[key].reservations > 0) {
        mesMissionsCount[key].tarifAverage = Math.round(mesMissionsCount[key].tarifAverage / mesMissionsCount[key].reservations);
      }
    });

    // Traitement des heures populaires
    const heuresData = heuresPopulairesQuery.data || [];
    const heuresCount: Record<string, number> = {};
    const joursSemaineCount: Record<string, number> = {};
    
    const joursMapping = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];
    
    heuresData.forEach(mission => {
      if (mission.created_at) {
        const date = new Date(mission.created_at);
        
        // Heures
        const heure = date.getHours();
        const creneau = `${heure.toString().padStart(2, '0')}h-${(heure + 1).toString().padStart(2, '0')}h`;
        heuresCount[creneau] = (heuresCount[creneau] || 0) + 1;
        
        // Jours de la semaine
        const jourSemaine = joursMapping[date.getDay()];
        joursSemaineCount[jourSemaine] = (joursSemaineCount[jourSemaine] || 0) + 1;
      }
    });

    // OPTIMISATION: Calcul du taux de conversion en parallèle
    const [totalCandidaturesResult, candidaturesAccepteesResult] = await Promise.all([
      supabase
        .from('user_mission_candidature')
        .select('id, created_at')
        .eq('jobbeur_id', userId)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),
      supabase
        .from('user_mission_candidature')
        .select('id, created_at')
        .eq('jobbeur_id', userId)
        .eq('statut', 'acceptée')
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO)
    ]);

    const tauxConversion = (totalCandidaturesResult.data?.length ?? 0) > 0
      ? Math.round(((candidaturesAccepteesResult.data?.length ?? 0) / (totalCandidaturesResult.data?.length ?? 1)) * 100)
      : 0;

    // Calcul du taux de rétention
    interface MissionAvecUser {
      mission: {
        user_id: string;
      } | null;
      created_at: string;
    }

    // OPTIMISATION: Requêtes parallèles pour taux de rétention et délai de réponse
    const [historiqueMissionsResult, reponsesCandidaturesResult, totalMissionsResult] = await Promise.all([
      supabase
        .from('user_mission_candidature')
        .select('mission:user_missions(user_id), created_at')
        .eq('jobbeur_id', userId)
        .eq('statut', 'acceptée')
        .order('created_at'),
      supabase
        .from('user_mission_candidature')
        .select('created_at, first_response_date')
        .eq('jobbeur_id', userId)
        .not('first_response_date', 'is', null)
        .gte('created_at', startDateISO)
        .lte('created_at', endDateISO),
      supabase
        .from('user_mission_candidature')
        .select('id')
        .eq('jobbeur_id', userId)
    ]);

    const clientsUniques = new Set();
    const clientsRecurrents = new Set();

    (historiqueMissionsResult.data as MissionAvecUser[] | null)?.forEach(item => {
      const userId = item.mission?.user_id;
      if (userId) {
        if (clientsUniques.has(userId)) {
          clientsRecurrents.add(userId);
        }
        clientsUniques.add(userId);
      }
    });

    const tauxRetention = clientsUniques.size > 0
      ? Math.round((clientsRecurrents.size / clientsUniques.size) * 100)
      : 0;

    // Calcul du délai moyen de réponse (utilise les données déjà récupérées)
    const reponsesCandidatures = reponsesCandidaturesResult.data;

    let delaiTotal = 0;
    let nbDelais = 0;

    reponsesCandidatures?.forEach(item => {
      if (item.created_at && item.first_response_date) {
        const created = new Date(item.created_at);
        const firstResponse = new Date(item.first_response_date);
        const delai = (firstResponse.getTime() - created.getTime()) / (1000 * 60); // en minutes
        
        // Ne compter que les délais inférieurs à 48h pour éviter les valeurs aberrantes
        if (delai <= 2880) { // 48 heures en minutes
          delaiTotal += delai;
          nbDelais++;
        }
      }
    });

    const delaiMoyenMinutes = nbDelais > 0 ? Math.round(delaiTotal / nbDelais) : 0;
    
    // Formatage du délai moyen
    let delaiMoyenFormate = '';
    if (delaiMoyenMinutes < 60) {
      // Moins d'une heure
      delaiMoyenFormate = `${delaiMoyenMinutes} min`;
    } else if (delaiMoyenMinutes < 1440) {
      // Moins d'un jour
      const heures = Math.floor(delaiMoyenMinutes / 60);
      const minutes = delaiMoyenMinutes % 60;
      delaiMoyenFormate = minutes > 0 ? `${heures}h ${minutes}min` : `${heures}h`;
    } else if (delaiMoyenMinutes < 10080) {
      // Moins d'une semaine
      const jours = Math.floor(delaiMoyenMinutes / 1440);
      const heures = Math.floor((delaiMoyenMinutes % 1440) / 60);
      delaiMoyenFormate = heures > 0 ? `${jours}j ${heures}h` : `${jours}j`;
    } else if (delaiMoyenMinutes < 43200) {
      // Moins d'un mois
      const semaines = Math.floor(delaiMoyenMinutes / 10080);
      const jours = Math.floor((delaiMoyenMinutes % 10080) / 1440);
      delaiMoyenFormate = jours > 0 ? `${semaines}sem ${jours}j` : `${semaines}sem`;
    } else {
      // Plus d'un mois
      const mois = Math.floor(delaiMoyenMinutes / 43200);
      const semaines = Math.floor((delaiMoyenMinutes % 43200) / 10080);
      delaiMoyenFormate = semaines > 0 ? `${mois}m ${semaines}sem` : `${mois}m`;
    }

    // Calcul du taux de complétion des missions (utilise les données déjà récupérées)
    const totalMissions = totalMissionsResult.data?.length || 0;
    const totalMissionsTerminees = missionsCompleteesQuery.data?.filter(m => 
      m.statut === 'acceptée' || m.statut === 'terminée').length || 0;
    const tauxCompletion = totalMissions > 0 ? Math.round((totalMissionsTerminees / totalMissions) * 100) : 0;

    // Évolution du taux de complétion (sur 6 mois)
    const evolutionCompletion = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() - i);
      
      const startOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const endOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);
      
      const { data: missionsMois } = await supabase
        .from('user_mission_candidature')
        .select('statut, mission:user_missions(titre)')
        .eq('jobbeur_id', userId)
        .gte('created_at', startOfMonth.toISOString())
        .lte('created_at', endOfMonth.toISOString());
        
      if (missionsMois && missionsMois.length > 0) {
        const terminees = missionsMois.filter(m => 
          m.statut === 'acceptée' || m.statut === 'terminée').length;
        const taux = Math.round((terminees / missionsMois.length) * 100);
        
        evolutionCompletion.push({
          mois: monthDate.toLocaleString('fr-FR', { month: 'short' }),
          taux
        });
      } else {
        evolutionCompletion.push({
          mois: monthDate.toLocaleString('fr-FR', { month: 'short' }),
          taux: 0
        });
      }
    }

    // Analyse des régions principales
    const regionsCount: Record<string, number> = {};
    decryptedProfils?.forEach(profile => {
      const codePostal = profile.code_postal;
      if (codePostal) {
        // Utiliser les 2 premiers chiffres du code postal pour identifier le département
        const departement = codePostal.substring(0, 2);
        regionsCount[departement] = (regionsCount[departement] || 0) + 1;
      }
    });

    // Calculer les pourcentages par région
    const totalProfiles = decryptedProfils?.length || 0;
    let regionsPrincipales = [];
    
    if (totalProfiles > 0) {
      regionsPrincipales = Object.entries(regionsCount)
        .map(([code, count]) => ({
          nom: `Dépt. ${code}`,
          pourcentage: Math.round((count / totalProfiles) * 100)
        }))
        .sort((a, b) => b.pourcentage - a.pourcentage)
        .slice(0, 5);
    } else {
      // Fournir des régions par défaut lorsqu'aucune donnée n'est disponible
      regionsPrincipales = [
        { nom: 'Dépt. 75', pourcentage: 0 },
        { nom: 'Dépt. 69', pourcentage: 0 },
        { nom: 'Dépt. 13', pourcentage: 0 },
        { nom: 'Dépt. 66', pourcentage: 0 },
        { nom: 'Dépt. 59', pourcentage: 0 }
      ];
    }

    // Performance détaillée par service
    const performanceServices = Object.entries(servicesCount)
      .map(([nom, data]) => {
        // Trouver les données des 3 derniers mois pour ce service
        const derniersMois = mesMissionsData
          .filter(m => m.category_id === nom && m.created_at)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, Math.min(10, mesMissionsData.length));
          
        const revenuRecent = derniersMois.reduce((sum, m) => sum + (m.budget || 0), 0);
        const revenuMoyen = derniersMois.length > 0 ? revenuRecent / derniersMois.length : 0;
        
        // Calculer la croissance en comparant avec les 3 mois précédents
        let croissance = 0;
        if (data.reservations > 0 && derniersMois.length > 0) {
          croissance = Math.round(((revenuMoyen / data.tarifAverage) - 1) * 100);
        }
        
        return {
          nomService: nom,
          revenuTotal: data.reservations * data.tarifAverage,
          missionsMoyennes: data.reservations / (parseInt(timeRange) / 30), // Missions par mois
          croissance,
          satisfaction: data.totalNotes > 0 ? Math.round((data.satisfaction / data.totalNotes) * 20) : null
        };
      })
      .sort((a, b) => b.revenuTotal - a.revenuTotal);

    // Évolution des clients
    const evolution = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() - i);
      
      // Début du mois
      const startOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      // Fin du mois
      const endOfMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);
      
      const monthStr = monthDate.toLocaleString('fr-FR', { month: 'short' });
      
      // Missions payées pour ce mois
      const { data: missionsData } = await supabase
        .from('user_mission_candidature')
        .select(`
          jobbeur_id,
          montant_paiement,
          payment_status,
          payment_date,
          mission:user_missions!inner(
            user_id,
            titre
          )
        `)
        .eq('mission.user_id', userId)
        .eq('statut', 'acceptée')
        .not('payment_status', 'is', null)
        .gte('payment_date', startOfMonth.toISOString())
        .lte('payment_date', endOfMonth.toISOString());

      // Montant total des missions payées
      const montantTotal = missionsData?.reduce((sum, mission) => 
        sum + (mission.montant_paiement || 0), 0) || 0;

      // Nombre de missions payées
      const nombreMissions = missionsData?.length || 0;

      // Clients uniques pour ce mois
      const clientsUniques = new Set(missionsData?.map(item => item.jobbeur_id)).size || 0;

      // Score global pour ce mois (pondération ajustée)
      const score = (montantTotal * 0.4) + (nombreMissions * 0.3 * 1000) + (clientsUniques * 0.3 * 1000);

      evolution.push({
        mois: monthStr,
        score: score,
        details: {
          montantTotal,
          nombreMissions,
          clientsUniques
        }
      });
    }

    // Calculer la croissance entre les deux derniers mois
    let croissanceMensuelle = 0;
    let moyenneMissionActuelle = 0;
    let moyenneMissionPrecedente = 0;

    if (evolution.length >= 2) {
      const moisActuel = evolution[evolution.length - 1];
      const moisPrecedent = evolution[evolution.length - 2];
      
      // Calculer les moyennes par mission
      moyenneMissionActuelle = moisActuel.details.nombreMissions > 0 
        ? Math.round(moisActuel.details.montantTotal / moisActuel.details.nombreMissions) 
        : 0;
      
      moyenneMissionPrecedente = moisPrecedent.details.nombreMissions > 0 
        ? Math.round(moisPrecedent.details.montantTotal / moisPrecedent.details.nombreMissions) 
        : 0;
      
      // Calculer la croissance sur chaque métrique
      const croissanceMontant = moisPrecedent.details.montantTotal > 0 
        ? ((moisActuel.details.montantTotal - moisPrecedent.details.montantTotal) / moisPrecedent.details.montantTotal) * 100 
        : (moisActuel.details.montantTotal > 0 ? 100 : 0);

      const croissanceMissions = moisPrecedent.details.nombreMissions > 0 
        ? ((moisActuel.details.nombreMissions - moisPrecedent.details.nombreMissions) / moisPrecedent.details.nombreMissions) * 100 
        : (moisActuel.details.nombreMissions > 0 ? 100 : 0);

      const croissanceClients = moisPrecedent.details.clientsUniques > 0 
        ? ((moisActuel.details.clientsUniques - moisPrecedent.details.clientsUniques) / moisPrecedent.details.clientsUniques) * 100 
        : (moisActuel.details.clientsUniques > 0 ? 100 : 0);

      // Moyenne pondérée des croissances
      croissanceMensuelle = Math.round((croissanceMontant * 0.4) + (croissanceMissions * 0.3) + (croissanceClients * 0.3));
    }

    // Prévoir les prochains mois
    const donneesEvolution = evolution.map(e => e.details.montantTotal);
    const moyenne3DerniersMois = donneesEvolution.slice(-3).reduce((a, b) => a + b, 0) / Math.min(3, donneesEvolution.length);
    const moyenne6DerniersMois = donneesEvolution.reduce((a, b) => a + b, 0) / donneesEvolution.length;
    
    // Calculer la tendance
    let tendance: 'hausse' | 'stable' | 'baisse' = 'stable';
    
    if (moyenne3DerniersMois > moyenne6DerniersMois * 1.1) {
      tendance = 'hausse';
    } else if (moyenne3DerniersMois < moyenne6DerniersMois * 0.9) {
      tendance = 'baisse';
    }
    
    // Projections avec facteur saisonnier (simplifiée)
    const facteurCroissance = moyenne3DerniersMois / (moyenne6DerniersMois || 1);
    const prochainMois = Math.round(moyenne3DerniersMois * facteurCroissance);
    const troisMois = Math.round(prochainMois * 3);
    const sixMois = Math.round(troisMois * 2);

    // Traitement des données de facturation
    const invoices = invoicesQuery.data || [];
    
    // Statistiques générales des factures
    const invoiceStats: InvoiceStats = {
      total: invoices.length,
      montantTotal: invoices.reduce((acc, inv) => acc + (inv.total_ttc || 0), 0),
      parStatut: {},
      parType: {},
      evolution: []
    };
    
    // Comptage par statut et par type
    invoices.forEach(inv => {
      // Comptage par statut
      if (!invoiceStats.parStatut[inv.statut]) {
        invoiceStats.parStatut[inv.statut] = 0;
      }
      invoiceStats.parStatut[inv.statut]++;
      
      // Comptage par type
      if (!invoiceStats.parType[inv.type]) {
        invoiceStats.parType[inv.type] = 0;
      }
      invoiceStats.parType[inv.type]++;
    });
    
    // Évolution mensuelle des factures
    const invoicesByMonth: {[key: string]: {montant: number, nombre: number}} = {};
    
    // Créer les entrées pour les 6 derniers mois
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      invoicesByMonth[monthKey] = { montant: 0, nombre: 0 };
    }
    
    // Remplir avec les données réelles
    invoices.forEach(inv => {
      if (inv.created_at) {
        const date = new Date(inv.created_at);
        const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        
        if (invoicesByMonth[monthKey]) {
          invoicesByMonth[monthKey].montant += (inv.total_ttc || 0);
          invoicesByMonth[monthKey].nombre += 1;
        }
      }
    });
    
    // Convertir en tableau pour l'évolution
    invoiceStats.evolution = Object.entries(invoicesByMonth)
      .map(([mois, data]) => ({
        mois,
        montant: Math.round(data.montant * 100) / 100,
        nombre: data.nombre
      }))
      .sort((a, b) => a.mois.localeCompare(b.mois));

    // Formatage des statistiques
    const stats = {
      clientStats: {
        total: totalClients,
        nouveaux: nouveauxClients,
        tauxSatisfaction,
        croissanceMensuelle: Math.round(croissanceMensuelle),
        tauxRetention,
        tauxConversion,
        regionsPrincipales,
        evolution: evolution.map(e => ({
          mois: e.mois,
          clients: e.details.clientsUniques,
          missions: e.details.nombreMissions,
          montant: e.details.montantTotal
        }))
      },
      servicesPopulaires: Object.entries(servicesCount)
        .map(([nom, data]) => ({
          nom,
          reservations: data.reservations,
          satisfaction: data.totalNotes > 0 ? Math.round((data.satisfaction / data.totalNotes) * 20) : null, // Conversion en pourcentage
          tarifAverage: data.tarifAverage || 50,
          type: 'candidature'
        }))
        .sort((a, b) => b.reservations - a.reservations)
        .slice(0, 4),
      mesMissionsPopulaires: Object.entries(mesMissionsCount)
        .map(([nom, data]) => ({
          nom,
          reservations: data.reservations,
          satisfaction: data.totalNotes > 0 ? Math.round((data.satisfaction / data.totalNotes) * 20) : null, // Conversion en pourcentage
          tarifAverage: data.tarifAverage || 50,
          type: 'mission'
        }))
        .sort((a, b) => b.reservations - a.reservations)
        .slice(0, 4),
      heuresPopulaires: Object.entries(heuresCount)
        .map(([heure, reservations]) => ({
          heure,
          reservations
        }))
        .sort((a, b) => {
          // Extraire l'heure de début du créneau (ex: "14h-15h" -> 14)
          const heureA = parseInt(a.heure.split('h-')[0]);
          const heureB = parseInt(b.heure.split('h-')[0]);
          return heureA - heureB;
        })
        .slice(0, 24), // Afficher toutes les heures de la journée
      
      // Nouvelles statistiques
      joursSemaine: Object.entries(joursSemaineCount)
        .map(([jour, reservations]) => ({
          jour,
          reservations,
          pourcentage: Math.round((reservations / heuresData.length) * 100) || 0
        }))
        .sort((a, b) => {
          // Trier par jour de la semaine
          const jourOrder = { 'Lun': 0, 'Mar': 1, 'Mer': 2, 'Jeu': 3, 'Ven': 4, 'Sam': 5, 'Dim': 6 };
          return jourOrder[a.jour as keyof typeof jourOrder] - jourOrder[b.jour as keyof typeof jourOrder];
        }),
      
      delaiMoyen: {
        valeur: delaiMoyenMinutes,
        formatte: delaiMoyenFormate
      },
      
      tauxCompletion: {
        pourcentage: tauxCompletion,
        evolution: evolutionCompletion
      },
      
      performanceServices,
      
      previsions: {
        prochainMois,
        troisMois,
        sixMois,
        tendance
      },
      // Ajouter les statistiques de facturation
      invoiceStats
    };

    // Mise en cache optimisée pour 30 minutes
    await redis.setex(cacheKey, CACHE_TTL_STATS, JSON.stringify(stats));

    res.json(stats);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques avancées:', error);
    res.status(500).json({ error: 'Erreur serveur' });
    return;
  }
};

// Fonction pour générer des données par défaut pour les utilisateurs sans abonnement premium
function generateDefaultStats() {
  // Créer des données statiques pour montrer à quoi ressemblent les statistiques
  const currentDate = new Date();
  const currentMonth = currentDate.toLocaleString('fr-FR', { month: 'short' });
  
  // Générer des mois précédents pour l'évolution
  const getLastMonths = (count: number) => {
    const months = [];
    for (let i = count - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date.toLocaleString('fr-FR', { month: 'short' }));
    }
    return months;
  };
  
  const lastMonths = getLastMonths(6);
  
  return {
    clientStats: {
      total: 0,
      nouveaux: 0,
      tauxSatisfaction: 0,
      croissanceMensuelle: 0,
      evolution: lastMonths.map(mois => ({
        mois,
        clients: 0,
        missions: 0,
        montant: 0
      })),
      tauxRetention: 0,
      tauxConversion: 0,
      regionsPrincipales: [
        { nom: "Île-de-France", pourcentage: 0 },
        { nom: "Auvergne-Rhône-Alpes", pourcentage: 0 },
        { nom: "Nouvelle-Aquitaine", pourcentage: 0 },
        { nom: "Occitanie", pourcentage: 0 },
        { nom: "Hauts-de-France", pourcentage: 0 }
      ]
    },
    servicesPopulaires: [],
    mesMissionsPopulaires: [],
    heuresPopulaires: [
      { heure: "08h-10h", reservations: 0 },
      { heure: "10h-12h", reservations: 0 },
      { heure: "12h-14h", reservations: 0 },
      { heure: "14h-16h", reservations: 0 },
      { heure: "16h-18h", reservations: 0 },
      { heure: "18h-20h", reservations: 0 }
    ],
    joursSemaine: [
      { jour: "Lun", reservations: 0, pourcentage: 0 },
      { jour: "Mar", reservations: 0, pourcentage: 0 },
      { jour: "Mer", reservations: 0, pourcentage: 0 },
      { jour: "Jeu", reservations: 0, pourcentage: 0 },
      { jour: "Ven", reservations: 0, pourcentage: 0 },
      { jour: "Sam", reservations: 0, pourcentage: 0 },
      { jour: "Dim", reservations: 0, pourcentage: 0 }
    ],
    tauxCompletion: {
      pourcentage: 0,
      evolution: lastMonths.map(mois => ({
        mois,
        taux: 0
      }))
    },
    performanceServices: [],
    previsions: {
      prochainMois: 0,
      troisMois: 0,
      sixMois: 0,
      tendance: 'stable'
    },
    delaiMoyen: {
      valeur: 0,
      formatte: "0min"
    },
    // Ajouter les statistiques de facturation par défaut
    invoiceStats: {
      total: 0,
      montantTotal: 0,
      parStatut: {},
      parType: {},
      evolution: generateDefaultMonthlyEvolution()
    }
  };
}

// Fonction utilitaire pour générer des données d'évolution par défaut
function generateDefaultMonthlyEvolution() {
  const evolution = [];
  for (let i = 0; i < 6; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
    evolution.push({
      mois: monthKey,
      montant: 0,
      nombre: 0
    });
  }
  return evolution.sort((a, b) => a.mois.localeCompare(b.mois));
} 