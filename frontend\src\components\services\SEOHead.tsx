import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  ogLocale?: string;
  twitterCard?: string;
  twitterImage?: string;
  structuredData?: string | object;
  noindex?: boolean;
  nofollow?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  canonical,
  ogImage,
  ogType = 'website',
  ogLocale = 'fr_FR',
  twitterCard = 'summary_large_image',
  twitterImage,
  structuredData,
  noindex = false,
  nofollow = false
}) => {
  const baseUrl = window.location.origin;
  const currentUrl = window.location.href;
  
  // Construire les robots meta
  const robotsContent = [];
  if (noindex) robotsContent.push('noindex');
  if (nofollow) robotsContent.push('nofollow');
  if (robotsContent.length === 0) robotsContent.push('index', 'follow');

  useEffect(() => {
    // Injecter les données structurées si fournies
    if (structuredData) {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.setAttribute('data-seo-head', 'true');
      script.textContent = typeof structuredData === 'string' ? structuredData : JSON.stringify(structuredData);
      document.head.appendChild(script);

      // Nettoyer au démontage
      return () => {
        const existingScript = document.querySelector('script[data-seo-head="true"]');
        if (existingScript) {
          existingScript.remove();
        }
      };
    }
  }, [structuredData]);

  return (
    <Helmet>
      {/* Balises de base */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords.length > 0 && <meta name="keywords" content={keywords.join(', ')} />}
      <meta name="robots" content={robotsContent.join(', ')} />
      
      {/* URL canonique */}
      {canonical && <link rel="canonical" href={`${baseUrl}${canonical}`} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:locale" content={ogLocale} />
      <meta property="og:site_name" content="JobPartiel" />
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogImage && <meta property="og:image:alt" content={title} />}
      {ogImage && <meta property="og:image:width" content="1200" />}
      {ogImage && <meta property="og:image:height" content="630" />}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:site" content="@JobPartiel" />
      <meta name="twitter:creator" content="@JobPartiel" />
      {(twitterImage || ogImage) && <meta name="twitter:image" content={twitterImage || ogImage} />}
      {(twitterImage || ogImage) && <meta name="twitter:image:alt" content={title} />}
      
      {/* Balises supplémentaires pour le SEO */}
      <meta name="author" content="JobPartiel" />
      <meta name="publisher" content="JobPartiel" />
      <meta name="application-name" content="JobPartiel" />
      <meta name="theme-color" content="#FF6B2C" />
      
      {/* Balises pour les moteurs de recherche */}
      <meta name="googlebot" content="index,follow,snippet,archive" />
      <meta name="bingbot" content="index,follow,snippet,archive" />
      
      {/* Balises de géolocalisation pour les services locaux */}
      <meta name="geo.region" content="FR" />
      <meta name="geo.country" content="France" />
      
      {/* Balises pour les réseaux sociaux */}
      <meta property="fb:app_id" content="JobPartiel" />
      
      {/* Balises pour l'indexation mobile */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="JobPartiel" />
      
      {/* Balises pour la performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="//api.jobpartiel.fr" />
      
      {/* Balises pour l'accessibilité */}
      <meta name="color-scheme" content="light" />
    </Helmet>
  );
};

export default SEOHead;
