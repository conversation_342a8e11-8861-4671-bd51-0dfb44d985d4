import { UserService } from '../services/types';
import { ProfilData } from './profileUtils';

// Types
export interface DisponibilitesJour {
  disponible: boolean;
  creneaux: Array<{
    debut: string;
    fin: string;
  }>;
}

export interface DisponibilitesConsolidees {
  [key: string]: DisponibilitesJour;
}

// Constante pour les jours de la semaine pour les disponibilités
export const JOURS = [
  'lundi',
  'mardi',
  'mercredi',
  'jeudi',
  'vendredi',
  'samedi',
  'dimanche'
] as const;

// Fonction pour formater la date
export const formatDate = (dateString: string) => {
  if (!dateString) return 'Date non définie';

  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    month: 'long' as const,
    year: 'numeric' as const
  };
  const formattedDate = date.toLocaleString('fr-FR', options);

  // Mettre la première lettre en majuscule et le reste en minuscules
  return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1).toLowerCase();
};

// Fonction pour formater l'heure
export const formatHeure = (heure: string) => {
  return heure.substring(0, 5);
};

// Fonction pour vérifier si on est dans les 48h après la création du profil
export const isWithin48Hours = (dateInscription?: string) => {
  if (!dateInscription) return false;

  const creationDate = new Date(dateInscription);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - creationDate.getTime());
  const diffHours = diffTime / (1000 * 60 * 60);

  return diffHours <= 48;
};

// Fonction pour convertir le HTML en texte brut
export const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

// Fonction pour consolider les horaires de tous les services
export const consolidateDisponibilites = (services: UserService[]) => {
  const disponibilites: DisponibilitesConsolidees = {};

  // Initialiser les jours
  JOURS.forEach((jour) => {
    disponibilites[jour] = {
      disponible: false,
      creneaux: []
    };
  });

  // Parcourir tous les services actifs
  services.filter(service => service.statut === 'actif').forEach(service => {
    Object.entries(service.horaires || {}).forEach(([jour, horaire]) => {
      if (horaire && typeof horaire === 'object') {
        // Gérer les créneaux spécifiques d'abord
        if (Array.isArray(horaire.creneaux)) {
          horaire.creneaux.forEach(creneau => {
            if (creneau.disponible !== false) {
              disponibilites[jour].disponible = true;
              const nouveauCreneau = {
                debut: creneau.debut,
                fin: creneau.fin
              };

              const creneauExiste = disponibilites[jour].creneaux.some(
                c => c.debut === nouveauCreneau.debut && c.fin === nouveauCreneau.fin
              );

              if (!creneauExiste) {
                disponibilites[jour].creneaux.push(nouveauCreneau);
              }
            }
          });
        }

        // Ensuite gérer l'horaire général s'il est disponible
        if (horaire.disponible && horaire.debut && horaire.fin) {
          disponibilites[jour].disponible = true;
          const nouveauCreneau = {
            debut: horaire.debut,
            fin: horaire.fin
          };

          const creneauExiste = disponibilites[jour].creneaux.some(
            c => c.debut === nouveauCreneau.debut && c.fin === nouveauCreneau.fin
          );

          if (!creneauExiste) {
            disponibilites[jour].creneaux.push(nouveauCreneau);
          }
        }
      }
    });
  });

  // Trier les créneaux par heure de début pour chaque jour
  Object.values(disponibilites).forEach(jour => {
    jour.creneaux.sort((a, b) => {
      // Convertir les heures en minutes pour une comparaison correcte
      const [aHours, aMinutes] = a.debut.split(':').map(Number);
      const [bHours, bMinutes] = b.debut.split(':').map(Number);
      const aTotal = aHours * 60 + aMinutes;
      const bTotal = bHours * 60 + bMinutes;
      return aTotal - bTotal;
    });
  });

  return disponibilites;
};

// Fonction pour gérer le mode vacances
// Fonction utilitaire pour savoir si une date de validation expire dans moins de 2 mois
export const isDocumentExpiringSoon = (dateValidation?: string) => {
  if (!dateValidation) return false;
  const now = new Date();
  const expiration = new Date(dateValidation);
  // la validité est d'un an
  expiration.setFullYear(expiration.getFullYear() + 1);
  const diff = expiration.getTime() - now.getTime();
  const days = diff / (1000 * 60 * 60 * 24);
  return days <= 60;
};

export const handleVacationMode = async (
  isVacationMode: boolean,
  setIsVacationMode: React.Dispatch<React.SetStateAction<boolean>>,
  setCooldown: React.Dispatch<React.SetStateAction<boolean>>,
  setCooldownTime: React.Dispatch<React.SetStateAction<number>>,
  profil: ProfilData | null,
  setProfil: React.Dispatch<React.SetStateAction<ProfilData | null>>,
  notify: (message: string, type: 'success' | 'error' | 'info' | 'warning', duration?: number) => void,
  API_CONFIG: any,
  getCommonHeaders: () => Promise<any>,
  fetchCsrfToken: () => Promise<string>,
  axios: any
) => {
  // Activer le cooldown pour éviter les clics multiples
  setCooldown(true);
  let countdown = 5;
  setCooldownTime(countdown);

  const cooldownInterval = setInterval(() => {
    countdown -= 1;
    setCooldownTime(countdown);
    if (countdown <= 0) {
      clearInterval(cooldownInterval);
      setCooldown(false);
    }
  }, 1000);

  try {
    const headers = await getCommonHeaders();
    await fetchCsrfToken();
    headers['X-CSRF-Token'] = await fetchCsrfToken();

    const response = await axios.put(
      `${API_CONFIG.baseURL}/api/users/updateProfil`,
      { mode_vacance: !isVacationMode },
      {
        headers,
        withCredentials: true
      }
    );

    if (response.data.success) {
      setIsVacationMode(!isVacationMode);
      setProfil(prev => prev ? { ...prev, mode_vacance: !isVacationMode } : null);
      notify(
        !isVacationMode
          ? 'Mode vacances activé. Les utilisateurs seront informés que vous êtes en vacances.'
          : 'Mode vacances désactivé.',
        'success'
      );
    }
  } catch (error) {
    notify('Erreur lors de la modification du mode vacances', 'error');
    console.error('Erreur lors de la modification du mode vacances:', error);
  }
};
