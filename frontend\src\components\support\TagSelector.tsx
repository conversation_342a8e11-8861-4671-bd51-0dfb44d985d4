import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Autocomplete,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Typography,
  useTheme,
  Grid,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  InfoOutlined as InfoIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import supportTicketService, { Tag, CreateTagDto } from '../../services/supportTicketService';
import { logger } from '../../utils/logger';

// Couleurs prédéfinies pour notre sélecteur
const PREDEFINED_COLORS = [
  '#FF6B2C', // Couleur primaire du thème
  '#FF7A35', // Couleur secondaire du thème
  '#FF965E', // Couleur tertiaire du thème
  '#FFE4BA', // Couleur accent du thème
  '#E91E63', // Pink
  '#9C27B0', // Purple
  '#673AB7', // Deep Purple
  '#3F51B5', // Indigo
  '#2196F3', // Blue
  '#03A9F4', // Light Blue
  '#00BCD4', // Cyan
  '#009688', // Teal
  '#4CAF50', // Green
  '#8BC34A', // Light Green
  '#CDDC39', // Lime
  '#FFEB3B', // Yellow
  '#FFC107', // Amber
  '#FF9800', // Orange
  '#FF5722', // Deep Orange
  '#795548', // Brown
  '#9E9E9E', // Grey
  '#607D8B', // Blue Grey
];

interface TagSelectorProps {
  selectedTags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  ticketId?: string;
}

const TagSelector: React.FC<TagSelectorProps> = ({
  selectedTags,
  onTagsChange,
  ticketId,
}) => {
  const theme = useTheme();
  const auth = useAuth();
  const isStaff = auth.user?.role === 'jobpadm' || auth.user?.role === 'jobmodo';
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [newTag, setNewTag] = useState<CreateTagDto>({
    name: '',
    color: '#FF6B2C',
  });

  const fetchTags = async () => {
    try {
      setLoading(true);
      // Utiliser le service pour récupérer les tags
      const tagsData = await supportTicketService.getTags();
      setTags(tagsData || []);
    } catch (error) {
      logger.error('Erreur lors de la récupération des tags:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  const handleOpenDialog = (tag?: Tag) => {
    if (tag) {
      setEditingTag(tag);
      setNewTag({ name: tag.name, color: tag.color });
    } else {
      setEditingTag(null);
      setNewTag({ name: '', color: '#FF6B2C' });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingTag(null);
    setNewTag({ name: '', color: '#FF6B2C' });
  };

  const handleSaveTag = async () => {
    try {
      setLoading(true);
      if (editingTag) {
        // Mise à jour du tag existant
        await supportTicketService.updateTag(editingTag.id, newTag);
      } else {
        // Création d'un nouveau tag
        await supportTicketService.createTag(newTag);
      }
      
      await fetchTags();
      handleCloseDialog();
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde du tag:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTag = async (tagId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce tag ?')) return;
    
    try {
      setLoading(true);
      await supportTicketService.deleteTag(tagId);
      
      // Mettre à jour la liste des tags sélectionnés
      onTagsChange(selectedTags.filter(tag => tag.id !== tagId));
      
      await fetchTags();
      handleCloseDialog();
    } catch (error) {
      logger.error(`Erreur lors de la suppression du tag ${tagId}:`, error);
    } finally {
      setLoading(false);
    }
  };

  const handleTagsChange = (newTags: Tag[]) => {
    // Limiter à 5 tags maximum
    if (newTags.length <= 5) {
      onTagsChange(newTags);
      
      // Si on est en train de modifier un ticket et qu'on a les permissions
      if (ticketId && isStaff) {
        // L'API et le hook se chargeront d'enregistrer le changement
        // lors de la sauvegarde du ticket
      }
    }
  };

  const handleColorChange = (color: string) => {
    setNewTag({ ...newTag, color });
  };

  return (
    <>
      <Box>
        <Typography 
          variant="subtitle1" 
          gutterBottom
          sx={{
            fontWeight: 500,
            color: '#333',
            mb: 1,
            display: 'flex',
            alignItems: 'center'
          }}
        >
          Tags
          <Typography 
            variant="caption" 
            component="span" 
            sx={{ 
              ml: 1, 
              color: 'text.secondary',
              display: 'flex',
              alignItems: 'center',
              fontStyle: 'italic'
            }}
          >
            <InfoIcon fontSize="small" sx={{ fontSize: '1rem', mr: 0.5 }} />
            (Optionnel - Maximum 5 tags - Utilisé à des fins statistiques)
          </Typography>
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Autocomplete
            multiple
            options={selectedTags.length >= 5 ? selectedTags : tags}
            value={selectedTags}
            getOptionLabel={(option) => option.name}
            onChange={(_, newValue) => handleTagsChange(newValue)}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={selectedTags.length > 0 ? '' : 'Sélectionnez des tags (facultatif, max 5)'}
                variant="outlined"
                helperText={selectedTags.length >= 5 ? "Limite de 5 tags atteinte" : ""}
              />
            )}
            limitTags={5}
            renderTags={(tagValue, getTagProps) =>
              tagValue.map((option, index) => (
                <Chip
                  {...getTagProps({ index })}
                  key={option.id}
                  label={option.name}
                  style={{
                    backgroundColor: option.color,
                    color: theme.palette.getContrastText(option.color),
                  }}
                />
              ))
            }
            sx={{ flex: 1 }}
            loading={loading}
          />
          {isStaff && (
            <IconButton
              size="small"
              onClick={() => handleOpenDialog()}
              color="primary"
              disabled={selectedTags.length >= 5}
            >
              <AddIcon />
            </IconButton>
          )}
        </Box>
      </Box>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          {editingTag ? 'Modifier le tag' : 'Nouveau tag'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={2}>
            <TextField
              fullWidth
              label="Nom du tag"
              value={newTag.name}
              onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Couleur
              </Typography>
              
              {/* Sélecteur de couleurs personnalisé */}
              <Box mt={1}>
                <TextField 
                  label="Code couleur"
                  value={newTag.color}
                  onChange={(e) => handleColorChange(e.target.value)}
                  fullWidth
                  size="small"
                  InputProps={{
                    startAdornment: (
                      <Box
                        component="span"
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: 1,
                          mr: 1,
                          backgroundColor: newTag.color,
                          display: 'inline-block',
                          border: '1px solid rgba(0,0,0,0.1)'
                        }}
                      />
                    ),
                  }}
                />

                <Typography variant="caption" sx={{ display: 'block', mt: 2, mb: 1 }}>
                  Couleurs prédéfinies
                </Typography>

                <Grid container spacing={1}>
                  {PREDEFINED_COLORS.map((color) => (
                    <Grid size={{ xs: 'auto' }} key={color}>
                      <Tooltip title={color}>
                        <Paper
                          onClick={() => handleColorChange(color)}
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: 1,
                            backgroundColor: color,
                            cursor: 'pointer',
                            border: newTag.color === color ? `2px solid ${theme.palette.primary.main}` : '1px solid rgba(0,0,0,0.1)',
                            '&:hover': {
                              boxShadow: '0 0 0 2px rgba(0,0,0,0.2)',
                            },
                          }}
                        />
                      </Tooltip>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          {editingTag && (
            <Button
              onClick={() => handleDeleteTag(editingTag.id)}
              color="error"
              startIcon={<DeleteIcon />}
            >
              Supprimer
            </Button>
          )}
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button
            onClick={handleSaveTag}
            variant="contained"
            disabled={!newTag.name || loading}
          >
            {editingTag ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TagSelector; 