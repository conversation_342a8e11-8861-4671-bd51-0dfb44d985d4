import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  useTheme,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  SupportAgent as SupportAgentIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { CreateTicketDto } from '../../services/supportTicketService';
import useTickets from '../../hooks/useTickets';
import { logger } from '../../utils/logger';

const CreateTicketButton: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [ticket, setTicket] = useState<CreateTicketDto>({
    title: '',
    description: '',
    priority: 'normale',
    category: 'autre',
  });

  // Utiliser notre hook personnalisé
  const { loading, error, createTicket } = useTickets();

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setTicket({
      title: '',
      description: '',
      priority: 'normale',
      category: 'autre',
    });
  };

  const handleChange = (field: keyof CreateTicketDto) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setTicket(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSelectChange = (field: keyof CreateTicketDto) => (
    event: SelectChangeEvent
  ) => {
    setTicket(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      const newTicket = await createTicket(ticket);
      
      if (newTicket) {
        handleClose();
        navigate(`/dashboard/support/ticket/${newTicket.id}`);
        setSnackbar({
          open: true,
          message: 'Votre ticket a été créé avec succès !',
          severity: 'success',
        });
      } else {
        setSnackbar({
          open: true,
          message: 'Erreur lors de la création du ticket',
          severity: 'error',
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la création du ticket:', error);
      setSnackbar({
        open: true,
        message: 'Erreur lors de la création du ticket',
        severity: 'error',
      });
    }
  };

  return (
    <>
      <Fab
        color="primary"
        aria-label="support"
        onClick={handleOpen}
        sx={{
          position: 'fixed',
          bottom: theme.spacing(4),
          right: theme.spacing(4),
          zIndex: 1000,
        }}
      >
        <SupportAgentIcon />
      </Fab>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Nouveau ticket de support</Typography>
            <IconButton onClick={handleClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <form onSubmit={handleSubmit}>
          <DialogContent>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                Une erreur est survenue. Veuillez réessayer.
              </Alert>
            )}

            <Box display="flex" flexDirection="column" gap={2}>
              <TextField
                fullWidth
                label="Titre"
                value={ticket.title}
                onChange={handleChange('title')}
                required
                placeholder="Décrivez brièvement votre problème"
              />

              <TextField
                fullWidth
                label="Description"
                value={ticket.description}
                onChange={handleChange('description')}
                multiline
                rows={4}
                required
                placeholder="Donnez plus de détails sur votre problème..."
              />

              <FormControl fullWidth>
                <InputLabel>Priorité</InputLabel>
                <Select
                  value={ticket.priority}
                  label="Priorité"
                  onChange={handleSelectChange('priority')}
                  required
                >
                  <MenuItem value="faible">Faible</MenuItem>
                  <MenuItem value="normale">Normale</MenuItem>
                  <MenuItem value="elevee">Élevée</MenuItem>
                  <MenuItem value="urgente">Urgente</MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={ticket.category}
                  label="Catégorie"
                  onChange={handleSelectChange('category')}
                  required
                >
                  <MenuItem value="technique">Technique</MenuItem>
                  <MenuItem value="facturation">Facturation</MenuItem>
                  <MenuItem value="compte">Compte</MenuItem>
                  <MenuItem value="mission">Mission</MenuItem>
                  <MenuItem value="autre">Autre</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </DialogContent>

          <DialogActions sx={{ 
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 2, sm: 1 },
            pt: { xs: 2, sm: 1 },
            pb: { xs: 2, sm: 2 },
            px: { xs: 2, sm: 3 }
          }}>
            <Button 
              onClick={handleClose} 
              disabled={loading}
              fullWidth={true}
              sx={{ 
                order: { xs: 2, sm: 1 },
                mb: { xs: 0, sm: 0 }
              }}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading || !ticket.title || !ticket.description}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
              fullWidth={true}
              sx={{ 
                order: { xs: 1, sm: 2 }
              }}
            >
              {loading ? 'Création en cours...' : 'Créer le ticket'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default CreateTicketButton; 