import axios, { 
  InternalAxiosRequestConfig, 
  AxiosResponse, 
  AxiosError 
} from 'axios';
import { API_CONFIG } from '../config/api';
import { logger } from '../utils/logger';
import { fetchCsrfToken } from './csrf';

/**
 * Instance Axios centralisée pour toutes les requêtes API
 * Utiliser cette instance au lieu de créer des instances séparées
 * dans chaque service/contexte
 */
export const api = axios.create({
  baseURL: API_CONFIG.baseURL,
  headers: API_CONFIG.headers,
  withCredentials: API_CONFIG.withCredentials
});

// Intercepteur pour ajouter le token CSRF et logger les requêtes
api.interceptors.request.use(async (request: InternalAxiosRequestConfig) => {
  // Logger la requête
  logger.info('[API] Request:', {
    url: request.url,
    method: request.method,
    data: request.data
  });

  // Ajouter automatiquement le token CSRF pour les méthodes modifiantes
  if (['post', 'put', 'delete', 'patch'].includes(request.method?.toLowerCase() || '')) {
    try {
      // Éviter de récupérer le token CSRF si la requête contient déjà l'en-tête
      if (!request.headers['X-CSRF-Token'] && !request.headers['x-csrf-token']) {
        const csrfToken = await fetchCsrfToken();
        // Définir l'en-tête CSRF
        request.headers['X-CSRF-Token'] = csrfToken;
        logger.info('[API] CSRF Token ajouté automatiquement');
      }
    } catch (error) {
      logger.error('[API] Erreur lors de la récupération du token CSRF:', error);
    }
  }

  return request;
});

// Intercepteur pour gérer les erreurs et les réponses
api.interceptors.response.use(
  (response: AxiosResponse) => {
    logger.info('[API] Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error: AxiosError) => {
    // Gestion spécifique des erreurs HTTP/2 Protocol Error
    if (error.code === 'ERR_HTTP2_PROTOCOL_ERROR' || error.message?.includes('HTTP2_PROTOCOL_ERROR')) {
      logger.warn('[API] Erreur de protocole HTTP/2 détectée, tentative de retry', {
        url: error.config?.url,
        method: error.config?.method
      });

      // Retry automatique avec fallback HTTP/1.1 (une seule fois)
      if (error.config && !error.config.headers?.['X-HTTP2-Retry']) {
        const retryConfig = {
          ...error.config,
          headers: {
            ...error.config.headers,
            'Connection': 'close', // Force HTTP/1.1
            'X-HTTP2-Retry': 'true' // Marquer pour éviter les retries infinis
          }
        };

        return api.request(retryConfig);
      }
    }

    logger.error('[API] Error:', error.response || error.message);

    // Gestion des erreurs CSRF spécifiques
    if (error.response?.status === 403) {
      const responseData = error.response.data as any;

      // Vérifier si c'est spécifiquement une erreur CSRF
      if (responseData?.code === 'CSRF_TOKEN_INVALID') {
        logger.error('[API] Erreur CSRF détectée:', responseData.error);

        // Ajouter une notification pour informer l'utilisateur
        if (typeof window !== 'undefined' && window.location) {
          // Optionnel : rafraîchir la page pour obtenir un nouveau token CSRF
          logger.info('[API] Rafraîchissement de la page pour obtenir un nouveau token CSRF...');
        }
      } else if (responseData?.message?.includes('CSRF')) {
        logger.error('[API] Erreur CSRF générique détectée');
      }
    }
    
    // Gestion des erreurs spécifiques si nécessaire
    if (error.response && error.response.data) {
      // Si le backend renvoie une erreur avec un message personnalisé
      const responseData = error.response.data as Record<string, any>;
      if (responseData.message) {
        logger.error('[API] Error message:', responseData.message);
      }
      if (responseData.error) {
        logger.error('[API] Error details:', responseData.error);
      }
    }
    
    return Promise.reject(error);
  }
);

export default api; 