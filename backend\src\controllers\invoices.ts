import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { z } from 'zod';
import logger from '../utils/logger';
import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { redis } from '../config/redis';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { sendInvoiceEmail } from '../services/emailService';
import {
  decryptProfilDataAsync,
  decryptUserDataAsync,
  hashEmail,
  encryptInvoiceDataAsync,
  decryptInvoiceDataAsync,
  decryptCompanyDataAsync,
  encryptDataAsync,
  decryptDataAsync
} from '../utils/encryption';

// Constantes pour le cache - TTL optimisés pour de meilleures performances
const INVOICE_CACHE_PREFIX = 'invoice_controller:';
const INVOICE_LIST_CACHE_PREFIX = 'invoice_list_controller:';
const CACHE_TTL = 1800; // 30 minutes pour les factures (données peu changeantes)
const CACHE_TTL_LIST = 900; // 15 minutes pour les listes

// Ajouter la fonction formatDate directement dans ce fichier
const formatDate = (date: string | Date): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Fonction utilitaire pour obtenir le numéro d'affichage selon le statut
const getDisplayNumber = (invoice: any): string => {
  if (invoice.statut === 'brouillon' && invoice.draft_number) {
    // Extraire seulement la partie BROUILLON-XXXX pour simplifier l'affichage
    const match = invoice.draft_number.match(/BROUILLON-.*?-.*?-(\d+)$/);
    if (match) {
      return `BROUILLON-${match[1]}`;
    }
    return invoice.draft_number;
  }
  return invoice.number || 'N/A';
};

// Fonction utilitaire pour vérifier si un document est un brouillon
const isDraft = (invoice: any): boolean => {
  return invoice.statut === 'brouillon';
};

// Interface pour les éléments de facture
interface InvoiceItem {
  id?: string;
  description: string;
  quantite: number;
  unite?: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
  ordre?: number;
}

// Type pour les éléments de facture en cours de création/calcul
interface InvoiceItemInput {
  id?: string;
  description: string;
  quantite: number;
  unite?: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht?: number;
  montant_tva?: number;
  montant_ttc?: number;
  ordre?: number;
}

// Schémas de validation pour les factures et devis
const invoiceItemSchema = z.object({
  id: z.string().uuid().optional(),
  description: z.string().min(1, 'La description est requise'),
  quantite: z.number().positive('La quantité doit être positive'),
  unite: z.string().optional(),
  prix_unitaire: z.number().nonnegative('Le prix unitaire doit être positif ou zéro'),
  taux_tva: z.number().refine(val => [0, 5.5, 10, 20].includes(val), {
    message: 'Le taux de TVA doit être 0%, 5.5%, 10% ou 20%'
  }),
  montant_ht: z.number().optional(),
  montant_tva: z.number().optional(),
  montant_ttc: z.number().optional(),
  ordre: z.number().int().nonnegative().optional()
});

const createInvoiceSchema = z.object({
  type: z.enum(['devis', 'facture', 'avoir']),
  client_name: z.string().min(1, 'Le nom du client est requis'),
  client_address: z.string().optional(),
  client_email: z.string().email('Email invalide').optional(),
  client_phone: z.string().optional(),
  client_siret: z.string().optional(),
  client_tva: z.string().optional(),
  forme_juridique: z.string().optional(),
  code_ape: z.string().optional(),

  date_validite: z.string().optional(), // Sera convertie en date
  conditions_paiement: z.string().optional(),
  mode_paiement: z.enum(['virement', 'carte', 'cheque', 'jobi', 'especes']).optional(),

  statut: z.enum([
    'brouillon', 'envoye', 'accepte', 'refuse', 'expire',
    'paye', 'partiellement_paye', 'en_retard', 'annule'
  ]).optional().default('brouillon'),

  mentions_legales: z.string().optional(),
  mentions_tva: z.string().optional(),
  penalite_retard: z.string().optional(),
  indemnite_recouvrement: z.string().optional(),

  description: z.string().optional(),
  notes: z.string().optional(),

  items: z.array(invoiceItemSchema).min(1, 'Au moins un élément est requis'),

  // Pour les avoirs et les conversions
  facture_origine_id: z.string().uuid().optional(),
  devis_origine_id: z.string().uuid().optional(),
});

const updateInvoiceSchema = z.object({
  client_name: z.string().min(1, 'Le nom du client est requis').optional(),
  client_address: z.string().optional(),
  client_email: z.string().email('Email invalide').optional(),
  client_phone: z.string().optional(),
  client_siret: z.string().optional(),
  client_tva: z.string().optional(),
  forme_juridique: z.string().optional(),
  code_ape: z.string().optional(),

  date_validite: z.string().optional(),
  conditions_paiement: z.string().optional(),
  mode_paiement: z.enum(['virement', 'carte', 'cheque', 'jobi', 'especes']).optional(),

  statut: z.enum([
    'brouillon', 'envoye', 'accepte', 'refuse', 'expire',
    'paye', 'partiellement_paye', 'en_retard', 'annule'
  ]).optional(),

  // Champs pour la gestion des numéros
  number: z.string().nullable().optional(),
  draft_number: z.string().nullable().optional(),

  mentions_legales: z.string().optional(),
  mentions_tva: z.string().optional(),
  penalite_retard: z.string().optional(),
  indemnite_recouvrement: z.string().optional(),

  description: z.string().optional(),
  notes: z.string().optional(),

  date_paiement: z.string().nullable().optional(),

  items: z.array(invoiceItemSchema).optional(),
});

const sendInvoiceSchema = z.object({
  email: z.string().email('Email invalide'),
  message: z.string().optional(),
});

// Fonction pour calculer les montants
const calculateAmounts = (items: InvoiceItemInput[]) => {
  let totalHT = 0;
  let totalTVA = 0;
  let totalTTC = 0;

  items.forEach(item => {
    const montantHT = item.quantite * item.prix_unitaire;
    const montantTVA = montantHT * (item.taux_tva / 100);
    const montantTTC = montantHT + montantTVA;

    totalHT += montantHT;
    totalTVA += montantTVA;
    totalTTC += montantTTC;

    // Ajouter ces valeurs calculées à l'item
    item.montant_ht = parseFloat(montantHT.toFixed(2));
    item.montant_tva = parseFloat(montantTVA.toFixed(2));
    item.montant_ttc = parseFloat(montantTTC.toFixed(2));
  });

  return {
    totalHT: parseFloat(totalHT.toFixed(2)),
    totalTVA: parseFloat(totalTVA.toFixed(2)),
    totalTTC: parseFloat(totalTTC.toFixed(2))
  };
};

// Obtenir toutes les factures et devis de l'utilisateur
export const getInvoices = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({  
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const type = req.query.type as string;
    const statut = req.query.statut as string;
    const search = req.query.search as string;
    const client = req.query.client as string;
    
    // Créer une clé de cache unique basée sur les paramètres de la requête
    const cacheKey = `${INVOICE_LIST_CACHE_PREFIX}${userId}:${type || 'all'}:${statut || ''}:${search || ''}:${client || ''}`;
    
    // Vérifier si les données sont en cache
    const cachedInvoices = await redis.get(cacheKey);
    if (cachedInvoices) {
      res.status(200).json({ 
        success: true, 
        data: JSON.parse(cachedInvoices)
      });
      return;
    }
    
    let query = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('user_id', userId);
    
    if (type && type !== 'all') {
      query = query.eq('type', type);
    }
    
    if (statut) {
      query = query.eq('statut', statut);
    }
    
    if (client) {
      query = query.ilike('client_name', `%${client}%`);
    }
    
    if (search) {
      query = query.or(`number.ilike.%${search}%,client_name.ilike.%${search}%,description.ilike.%${search}%`);
    }
    
    const { data, error } = await query.order('date_creation', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des documents:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des documents',
        error: error.message
      });
    }

    // Déchiffrer les données des factures en parallèle pour optimiser les performances
    const decryptedData = await Promise.all(data?.map(async invoice => await decryptInvoiceDataAsync(invoice)) || []);

    // Mettre en cache les données déchiffrées pour 15 minutes (listes)
    await redis.setex(cacheKey, CACHE_TTL_LIST, JSON.stringify(decryptedData));

    res.status(200).json({
      success: true,
      data: decryptedData
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération des documents:', err);
    return res.status(500).json({ 
      success: false, 
      message: 'Erreur serveur lors de la récupération des documents',
      error: err.message
    });
  }
};

// Obtenir une facture/devis par son ID
export const getInvoiceById = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const invoiceId = req.params.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const cacheKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
    const cachedInvoice = await redis.get(cacheKey);

    if (cachedInvoice) {
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedInvoice)
      });
    }

    // Récupérer le document d'abord sans restriction pour vérifier les permissions
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération du document:', error);
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé',
        error: error.message
      });
    }

    // Vérifier les permissions d'accès
    let hasAccess = false;

    // 1. L'utilisateur est le créateur du document
    if (data.user_id === userId) {
      hasAccess = true;
    }

    // 2. Pour les devis, vérifier par email du client
    if (!hasAccess && data.type === 'devis' && data.client_email) {
      try {
        // Récupérer l'email de l'utilisateur connecté
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();

        if (!userError && userData) {
          const decryptedUserEmail = await decryptDataAsync(userData.email);
          const decryptedClientEmail = await decryptDataAsync(data.client_email);

          if (decryptedUserEmail === decryptedClientEmail) {
            hasAccess = true;
          }
        }
      } catch (emailError) {
        logger.error('Erreur lors de la vérification de l\'email:', emailError);
      }
    }

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'êtes pas autorisé à accéder à ce document'
      });
    }
    
    // Déchiffrer les données de la facture
    const decryptedInvoice = await decryptInvoiceDataAsync(data);

    // Récupérer les informations de l'entreprise
    const { data: companySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('*')
      .eq('user_id', data.user_id)
      .single();

    if (!companyError && companySettings) {
      // Déchiffrer les données de l'entreprise
      const decryptedCompanySettings = await decryptCompanyDataAsync(companySettings);

      // Ajouter les informations de l'entreprise au document
      decryptedInvoice.emitter_name = decryptedCompanySettings.nom;
      decryptedInvoice.company_settings = decryptedCompanySettings;
    }

    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(decryptedInvoice));

    return res.status(200).json({
      success: true,
      data: decryptedInvoice
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération du document:', err);
    return res.status(500).json({ 
      success: false, 
      message: 'Erreur serveur lors de la récupération du document',
      error: err.message
    });
  }
};

// Créer une facture/devis
export const createInvoice = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // Vérifier si l'utilisateur a rempli ses informations d'entreprise
    const { data: companySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (companyError || !companySettings || !companySettings.nom) {
      return res.status(403).json({
        success: false,
        message: 'Vous devez d\'abord compléter les informations de votre entreprise avant de créer un document. Vous pouvez le faire via le bouton "Paramètres" en haut de cette page.',
        toastType: 'error'
      });
    }
    
    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium, quoteLimit, invoiceLimit } = await getUserSubscriptionLimits(userId);
    
    // Validation des données
    const validationResult = createInvoiceSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors
      });
    }
    
    const invoiceData = validationResult.data;
    if (!invoiceData) {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation des données.'
      });
    }
    const items = [...invoiceData.items];
    
    // Vérifier les limites selon l'abonnement pour les devis et factures
    if (!isPremium) {
      // Requête pour compter le nombre de documents par type
      const { data: documentCount, error: countError } = await supabase
        .from('invoices')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('type', invoiceData.type);
        
      if (countError) {
        logger.error('Erreur lors du comptage des documents:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }
      
      const documentLimit = invoiceData.type === 'devis' 
        ? quoteLimit
        : invoiceData.type === 'facture'
          ? invoiceLimit
          : null;
      
      if (documentLimit !== null && (documentCount?.length || 0) >= documentLimit) {
        return res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${documentLimit} ${invoiceData.type === 'devis' ? 'devis' : 'factures'} pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          toastType: 'error',
          limitReached: true
        });
      }
    }
    
    // Si c'est un avoir, vérifier que la facture d'origine existe
    if (invoiceData.type === 'avoir' && invoiceData.facture_origine_id) {
      const { data: originalInvoice, error: originalError } = await supabase
        .from('invoices')
        .select('id, type, statut')
        .eq('id', invoiceData.facture_origine_id)
        .eq('user_id', userId)
        .single();
      
      if (originalError || !originalInvoice) {
        return res.status(400).json({
          success: false,
          message: 'Facture d\'origine inexistante'
        });
      }
      
      if (originalInvoice.type !== 'facture') {
        return res.status(400).json({
          success: false,
          message: 'Un avoir ne peut être créé que pour une facture'
        });
      }
      
      // Vérifier le statut de la facture d'origine
      if (originalInvoice.statut === 'brouillon' || originalInvoice.statut === 'annule') {
        return res.status(400).json({
          success: false,
          message: 'Impossible de créer un avoir pour une facture en brouillon ou annulée'
        });
      }
    }
    
    // Si c'est une facture et qu'il y a un devis d'origine, vérifier qu'il existe
    if (invoiceData.type === 'facture' && invoiceData.devis_origine_id) {
      const { data: originalQuote, error: originalError } = await supabase
        .from('invoices')
        .select('id, type, statut')
        .eq('id', invoiceData.devis_origine_id)
        .eq('user_id', userId)
        .single();
      
      if (originalError || !originalQuote) {
        return res.status(400).json({
          success: false,
          message: 'Devis d\'origine inexistant'
        });
      }
      
      // Vérifier le statut du devis d'origine
      if (originalQuote.statut !== 'accepte') {
        return res.status(400).json({
          success: false,
          message: 'Impossible de créer une facture depuis un devis non accepté'
        });
      }
    }
    
    // Calculer les montants
    const { totalHT, totalTVA, totalTTC } = calculateAmounts(items);

    // Générer le numéro de document selon le statut
    const isDraft = invoiceData.statut === 'brouillon';
    let numberResult = null;
    let draftNumber = null;

    if (isDraft) {
      // Pour les brouillons, générer un numéro temporaire
      const { data: draftNumberResult, error: draftNumberError } = await supabase.rpc(
        'generate_invoice_number',
        { doc_type: invoiceData.type, is_draft: true }
      );

      if (draftNumberError) {
        logger.error('Erreur lors de la génération du numéro de brouillon:', draftNumberError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération du numéro de brouillon',
          error: draftNumberError.message
        });
      }
      draftNumber = draftNumberResult;
    } else {
      // Pour les documents validés, générer un numéro officiel
      const { data: officialNumberResult, error: numberError } = await supabase.rpc(
        'generate_invoice_number',
        { doc_type: invoiceData.type, is_draft: false }
      );

      if (numberError) {
        logger.error('Erreur lors de la génération du numéro:', numberError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération du numéro de document',
          error: numberError.message
        });
      }
      numberResult = officialNumberResult;
    }
    
    // Préparer les données de la facture
    const invoice = {
      user_id: userId,
      number: numberResult,
      draft_number: draftNumber,
      type: invoiceData.type,
      client_name: invoiceData.client_name,
      client_address: invoiceData.client_address,
      client_email: invoiceData.client_email,
      client_phone: invoiceData.client_phone,
      client_siret: invoiceData.client_siret,
      client_tva: invoiceData.client_tva,
      date_validite: invoiceData.date_validite ? new Date(invoiceData.date_validite) : null,
      conditions_paiement: invoiceData.conditions_paiement,
      mode_paiement: invoiceData.mode_paiement,
      statut: invoiceData.statut || 'brouillon',
      mentions_legales: invoiceData.mentions_legales,
      mentions_tva: invoiceData.mentions_tva,
      penalite_retard: invoiceData.penalite_retard,
      indemnite_recouvrement: invoiceData.indemnite_recouvrement,
      description: invoiceData.description,
      notes: invoiceData.notes,
      facture_origine_id: invoiceData.facture_origine_id,
      devis_origine_id: invoiceData.devis_origine_id,
      total_ht: totalHT,
      total_tva: totalTVA,
      total_ttc: totalTTC
    };

    // Chiffrer les données sensibles de la facture avant insertion
    const encryptedInvoice = await encryptInvoiceDataAsync(invoice);

    // Créer la facture
    const { data: createdInvoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert(encryptedInvoice)
      .select()
      .single();
    
    if (invoiceError) {
      logger.error('Erreur lors de la création du document:', invoiceError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du document',
        error: invoiceError.message
      });
    }
    
    // Préparer les items pour insertion
    const itemsToInsert = items.map((item, index) => ({
      invoice_id: createdInvoice.id,
      description: item.description,
      quantite: item.quantite,
      unite: item.unite,
      prix_unitaire: item.prix_unitaire,
      taux_tva: item.taux_tva,
      montant_ht: item.montant_ht,
      montant_tva: item.montant_tva,
      montant_ttc: item.montant_ttc,
      ordre: item.ordre ?? index
    }));
    
    // Insérer les items
    const { data: createdItems, error: itemsError } = await supabase
      .from('invoice_items')
      .insert(itemsToInsert)
      .select();
    
    if (itemsError) {
      logger.error('Erreur lors de la création des items:', itemsError);
      // Supprimer la facture en cas d'erreur sur les items
      await supabase.from('invoices').delete().eq('id', createdInvoice.id);
      
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création des items du document',
        error: itemsError.message
      });
    }
    
    // Enregistrer l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: createdInvoice.id,
      user_id: userId,
      action: 'Création du document',
      details: invoiceData.type
    });
    
    // Enregistrer l'activité dans l'historique d'activités de l'utilisateur
    const actionType = invoiceData.type === 'facture' 
      ? 'creation_facture' 
      : invoiceData.type === 'devis' 
        ? 'creation_devis' 
        : 'creation_avoir';
        
    await logUserActivity(
      userId,
      actionType,
      createdInvoice.id,
      'invoices',
      {
        number: createdInvoice.number,
        client: invoiceData.client_name,
        total: totalTTC
      },
      req ? getIpFromRequest(req) : undefined
    );
    
    // Si c'est un avoir, mettre à jour le statut de la facture d'origine
    if (invoiceData.type === 'avoir' && invoiceData.facture_origine_id) {
      await supabase
        .from('invoices')
        .update({ statut: 'annule' })
        .eq('id', invoiceData.facture_origine_id);
      
      await supabase.from('invoice_history').insert({
        invoice_id: invoiceData.facture_origine_id,
        user_id: userId,
        action: 'Annulation du document',
        details: createdInvoice.id
      });
    }
    
    // Si c'est une facture créée depuis un devis, mettre à jour la référence dans le devis
    if (invoiceData.type === 'facture' && invoiceData.devis_origine_id) {
      await supabase
        .from('invoices')
        .update({ 
          facture_origine_id: createdInvoice.id  // Ajouter seulement l'ID de la facture créée
        })
        .eq('id', invoiceData.devis_origine_id);
      
      await supabase.from('invoice_history').insert({
        invoice_id: invoiceData.devis_origine_id,
        user_id: userId,
        action: 'Conversion du devis en facture',
        details: createdInvoice.id
      });
    }
    
    // Invalider les caches pertinents
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    // Déchiffrer les données avant de les retourner
    const decryptedInvoice = await decryptInvoiceDataAsync(createdInvoice);

    return res.status(201).json({
      success: true,
      message: 'Document créé avec succès',
      data: {
        ...decryptedInvoice,
        invoice_items: createdItems
      }
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la création du document:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du document',
      error: err.message
    });
  }
};

// Mettre à jour une facture/devis
export const updateInvoice = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    const invoiceId = req.params.id;

    logger.info(`Tentative de mise à jour du document ${invoiceId} pour l'utilisateur ${userId}`);

    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: existingInvoice, error: existingError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();

    if (existingError || !existingInvoice) {
      logger.error(`Document ${invoiceId} non trouvé pour l'utilisateur ${userId}:`, existingError);
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    logger.info(`Document ${invoiceId} trouvé, type: ${existingInvoice.type}, statut: ${existingInvoice.statut}`);
    
    // Vérifier si la facture peut être modifiée (un document envoyé ne peut être modifié)
    const nonEditableStatuses = ['envoye', 'accepte', 'paye', 'partiellement_paye', 'annule'];
    
    if (existingInvoice.type !== 'devis' && nonEditableStatuses.includes(existingInvoice.statut)) {
      return res.status(400).json({
        success: false,
        message: 'Ce document ne peut plus être modifié'
      });
    }
    
    // Validation des données
    const validationResult = updateInvoiceSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      logger.error('Erreur de validation:', validationResult.error);
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      });
    }
    
    const updateData = validationResult.data;
    if (!updateData) {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation des données.'
      });
    }
    
    // Si le statut change à "envoye", vérifier que toutes les informations requises sont présentes
    if (updateData.statut === 'envoye') {
      const requiredFields = ['client_name', 'client_address'];
      const missingFields = [];
      
      for (const field of requiredFields) {
        if (!existingInvoice[field as keyof typeof existingInvoice] && !updateData[field as keyof typeof updateData]) {
          missingFields.push(field);
        }
      }
      
      if (missingFields.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Les champs suivants sont requis pour envoyer le document: ${missingFields.join(', ')}`
        }); 
      }
    }
    
    // Gérer la transition brouillon → validé
    if (existingInvoice.statut === 'brouillon' && updateData.statut && updateData.statut !== 'brouillon') {
      // Générer un numéro officiel pour le document qui sort du statut brouillon
      const { data: officialNumberResult, error: numberError } = await supabase.rpc(
        'generate_invoice_number',
        { doc_type: existingInvoice.type, is_draft: false }
      );

      if (numberError) {
        logger.error('Erreur lors de la génération du numéro officiel:', numberError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération du numéro officiel',
          error: numberError.message
        });
      }

      // Ajouter le numéro officiel aux champs à mettre à jour
      updateData.number = officialNumberResult;
      updateData.draft_number = null; // Supprimer le numéro de brouillon
    }

    // Si les items sont modifiés, recalculer les montants
    let updateFields: any = { ...updateData };

    if (updateData.items && updateData.items.length > 0) {
      const items = [...updateData.items];
      const { totalHT, totalTVA, totalTTC } = calculateAmounts(items);

      updateFields = {
        ...updateFields,
        total_ht: totalHT,
        total_tva: totalTVA,
        total_ttc: totalTTC
      };

      // Supprimer les items existants et insérer les nouveaux
      await supabase
        .from('invoice_items')
        .delete()
        .eq('invoice_id', invoiceId);

      const itemsToInsert = items.map((item, index) => ({
        invoice_id: invoiceId,
        description: item.description,
        quantite: item.quantite,
        unite: item.unite,
        prix_unitaire: item.prix_unitaire,
        taux_tva: item.taux_tva,
        montant_ht: item.montant_ht,
        montant_tva: item.montant_tva,
        montant_ttc: item.montant_ttc,
        ordre: item.ordre ?? index
      }));

      const { error: itemsError } = await supabase
        .from('invoice_items')
        .insert(itemsToInsert);

      if (itemsError) {
        logger.error('Erreur lors de la mise à jour des items:', itemsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour des items',
          error: itemsError.message
        });
      }

      // Supprimer items du updateFields pour éviter la duplication
      delete updateFields.items;
    }
    
    // Chiffrer les données sensibles avant la mise à jour
    const encryptedUpdateFields = await encryptInvoiceDataAsync(updateFields);

    logger.info(`Mise à jour du document ${invoiceId} avec les champs:`, Object.keys(encryptedUpdateFields));

    // Mettre à jour la facture
    const { data: updatedInvoice, error: updateError } = await supabase
      .from('invoices')
      .update(encryptedUpdateFields)
      .eq('id', invoiceId)
      .eq('user_id', userId) // Ajouter cette condition pour plus de sécurité
      .select()
      .single();

    if (updateError) {
      logger.error(`Erreur lors de la mise à jour du document ${invoiceId}:`, updateError);
      logger.error('Champs de mise à jour:', encryptedUpdateFields);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du document',
        error: updateError.message
      });
    }

    if (!updatedInvoice) {
      logger.error(`Aucune ligne mise à jour pour le document ${invoiceId}`);
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé ou non modifiable'
      });
    }

    logger.info(`Document ${invoiceId} mis à jour avec succès`);
    
    // Enregistrer l'historique si le statut a changé
    if (updateData.statut && updateData.statut !== existingInvoice.statut) {
      await supabase.from('invoice_history').insert({
        invoice_id: invoiceId,
        user_id: userId,
        action: 'Changement de statut',
        details: `${existingInvoice.statut} -> ${updateData.statut}`
      });
      
      // Enregistrer l'activité dans l'historique d'activités pour le changement de statut
      const actionType = `modification_statut_${existingInvoice.type}`;
      await logUserActivity(
        userId,
        actionType,
        invoiceId,
        'invoices',
        {
          number: existingInvoice.number,
          client: existingInvoice.client_name,
          old_status: existingInvoice.statut,
          new_status: updateData.statut
        },
        req ? getIpFromRequest(req) : undefined
      );
    } else {
      // Enregistrer l'activité de mise à jour générale
      const actionType = existingInvoice.type === 'facture' 
        ? 'modification_facture' 
        : existingInvoice.type === 'devis' 
          ? 'modification_devis' 
          : 'modification_avoir';
          
      await logUserActivity(
        userId,
        actionType,
        invoiceId,
        'invoices',
        {
          number: existingInvoice.number,
          client: existingInvoice.client_name
        },
        req ? getIpFromRequest(req) : undefined
      );
    }
    
    // Récupérer le document mis à jour avec ses items pour le retourner déchiffré
    const { data: completeUpdatedInvoice, error: fetchError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .single();

    if (fetchError) {
      logger.error('Erreur lors de la récupération du document mis à jour:', fetchError);
      // Retourner quand même le document mis à jour même si on ne peut pas récupérer les items
      const decryptedUpdatedInvoice = decryptInvoiceDataAsync(updatedInvoice);

      return res.status(200).json({
        success: true,
        message: 'Document mis à jour avec succès',
        data: decryptedUpdatedInvoice
      });
    }

    // Déchiffrer les données du document complet
    const decryptedCompleteInvoice = decryptInvoiceDataAsync(completeUpdatedInvoice);

    // Invalider les caches pertinents
    const invoiceCacheKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;

    await redis.del(invoiceCacheKey);
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    return res.status(200).json({
      success: true,
      message: 'Document mis à jour avec succès',
      data: decryptedCompleteInvoice
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la mise à jour du document:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du document',
      error: err.message
    });
  }
};

// Fonction auxiliaire pour générer le contenu du PDF
const generatePDFContent = async (req: Request, doc: PDFKit.PDFDocument, invoice: any, userData: any, useUserDataAsCompany: boolean = false) => {
  try {
    const userId = req.user?.userId;

    let decryptedCompanySettings = null;

    if (useUserDataAsCompany) {
      // Utiliser les données userData comme paramètres d'entreprise (pour les documents reçus)
      decryptedCompanySettings = {
        nom: userData.entreprise || `${userData.prenom} ${userData.nom}`,
        adresse: userData.adresse || '',
        code_postal: userData.code_postal || '',
        ville: userData.ville || '',
        telephone: userData.telephone || '',
        email: userData.email || '',
        site_web: userData.site_web || '',
        siret: userData.siret || '',
        tva: userData.tva || '',
        rcs: userData.rcs || '',
        capital: userData.capital || '',
        iban: userData.iban || '',
        bic: userData.bic || '',
        banque: userData.banque || '',
        forme_juridique: userData.forme_juridique || '',
        code_ape: userData.code_ape || '',
        mention_pied_page: userData.mention_pied_page || ''
      };
    } else {
      // Récupérer les paramètres de l'entreprise de l'utilisateur connecté (pour les documents émis)
      const { data: companySettings, error: companySettingsError } = await supabase
        .from('invoices_company_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (companySettingsError && companySettingsError.code !== 'PGRST116') {
        logger.error('Erreur lors de la récupération des paramètres d\'entreprise:', companySettingsError);
      }

      // Déchiffrer les données de l'entreprise si elles existent
      decryptedCompanySettings = companySettings ? await decryptCompanyDataAsync(companySettings) : null;
    }

    // Définir quelques couleurs et styles
    const primaryColor = '#FF7A35';
    const secondaryColor = '#4B5563';
    const lightGrayColor = '#F9FAFB';
    const borderColor = '#E5E7EB';
    
    // En-tête avec la date et le nom du site
    const today = new Date().toLocaleDateString('fr-FR');
    const time = new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute:'2-digit'});
    
    doc.fontSize(9).fillColor('#777777');
    doc.text(`Date de génération du document : ${today} ${time}`, 50, 30, { align: 'left' });
    doc.text('JobPartiel.fr – Le jobbing malin pour arrondir vos fins de mois', 50, 30, { align: 'right' });
    
    doc.moveTo(50, 45).lineTo(545, 45).strokeColor(borderColor).lineWidth(0.5).stroke();
    doc.moveDown(2);
    
    // En-tête: Informations de l'entreprise et du document
    const companyInfoY = 60;
    
    // Information de l'entreprise à gauche
    doc.fontSize(12).fillColor('#000000');
    doc.font('Helvetica-Bold').text(decryptedCompanySettings?.nom || `${userData.prenom} ${userData.nom}`, 50, companyInfoY);
    doc.font('Helvetica').fontSize(10).fillColor('#4B5563');

    let currentY = doc.y + 5;
    if (decryptedCompanySettings?.adresse || userData.adresse) {
      doc.text(decryptedCompanySettings?.adresse || userData.adresse, 50, currentY);
      currentY = doc.y + 5;
    }

    if ((decryptedCompanySettings?.code_postal && decryptedCompanySettings?.ville) || userData.adresse) {
      doc.text(`${decryptedCompanySettings?.code_postal || ''} ${decryptedCompanySettings?.ville || ''}`, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.email || userData.email) {
      doc.text(decryptedCompanySettings?.email || userData.email, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.telephone || userData.telephone) {
      doc.text(decryptedCompanySettings?.telephone || userData.telephone, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.siret || userData.siret) {
      doc.text(`SIRET: ${decryptedCompanySettings?.siret || userData.siret}`, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.tva) {
      doc.text(`TVA: ${decryptedCompanySettings.tva}`, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.forme_juridique) {
      doc.text(decryptedCompanySettings.forme_juridique, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.code_ape) {
      doc.text(`APE: ${decryptedCompanySettings.code_ape}`, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.rcs) {
      doc.text(`RCS ${decryptedCompanySettings.rcs}`, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.capital) {
      doc.text(decryptedCompanySettings.capital, 50, currentY);
      currentY = doc.y + 5;
    }

    if (decryptedCompanySettings?.mention_pied_page) {
      doc.text(decryptedCompanySettings.mention_pied_page, 50, currentY);
      currentY = doc.y + 5;
    }
    
    // Information du document à droite
    const documentX = 350;
    doc.font('Helvetica-Bold').fontSize(14).fillColor(primaryColor);
    const displayNumber = getDisplayNumber(invoice);
    doc.text(`${invoice.type === 'devis' ? 'Devis' : invoice.type === 'facture' ? 'Facture' : 'Avoir'} ${displayNumber}`, documentX, companyInfoY, { align: 'right' });
    
    doc.font('Helvetica').fontSize(10).fillColor('#4B5563');
    doc.text(`Date : ${formatDate(invoice.date_creation)}`, documentX, doc.y + 5, { align: 'right' });
    
    if (invoice.date_validite) {
      doc.text(`Valable jusqu'au : ${formatDate(invoice.date_validite)}`, documentX, doc.y + 5, { align: 'right' });
    }
    
    // Afficher le statut
    doc.font('Helvetica-Bold').fontSize(9);
    
    let statusBackground;
    let statusColor;
    
    // Définir les couleurs du statut
    switch (invoice.statut) {
      case 'brouillon':
        statusBackground = '#F3F4F6';
        statusColor = '#4B5563';
        break;
      case 'émis':
      case 'envoyé':
        statusBackground = '#DBEAFE';
        statusColor = '#1E40AF';
        break;
      case 'accepte':
        statusBackground = '#DCFCE7';
        statusColor = '#15803D';
        break;
      case 'refusé':
        statusBackground = '#FEE2E2';
        statusColor = '#B91C1C';
        break;
      case 'à payer':
        statusBackground = '#FEF3C7';
        statusColor = '#92400E';
        break;
      default:
        statusBackground = '#F3F4F6';
        statusColor = '#4B5563';
    }
    
    // Rectangle pour le statut
    const statusY = doc.y + 10;
    // Formatage des statuts avec les bonnes majuscules et accords selon le type de document
    let statusText;
    if (invoice.type === 'facture') {
      // Accord féminin pour les factures
      switch(invoice.statut) {
        case 'brouillon': statusText = 'Brouillon'; break;
        case 'envoye': statusText = 'Envoyée'; break;
        case 'paye': statusText = 'Payée'; break;
        case 'en_retard': statusText = 'En retard'; break;
        case 'annule': statusText = 'Annulée'; break;
        default: statusText = invoice.statut.charAt(0).toUpperCase() + invoice.statut.slice(1);
      }
    } else if (invoice.type === 'avoir') {
      // Accord masculin pour les avoirs
      switch(invoice.statut) {
        case 'brouillon': statusText = 'Brouillon'; break;
        case 'envoye': statusText = 'Envoyé'; break;
        case 'annule': statusText = 'Annulé'; break;
        default: statusText = invoice.statut.charAt(0).toUpperCase() + invoice.statut.slice(1);
      }
    } else {
      // Accord masculin pour les devis
      switch(invoice.statut) {
        case 'brouillon': statusText = 'Brouillon'; break;
        case 'envoye': statusText = 'Envoyé'; break;
        case 'accepte': statusText = 'Accepté'; break;
        case 'refuse': statusText = 'Refusé'; break;
        case 'facture': statusText = 'Facturé'; break;
        case 'expire': statusText = 'Expiré'; break;
        case 'annule': statusText = 'Annulé'; break;
        default: statusText = invoice.statut ? invoice.statut.charAt(0).toUpperCase() + invoice.statut.slice(1) : 'Brouillon';
      }
    }
    
    // Assurer une largeur minimale pour éviter que le statut ne déborde
    const textWidth = doc.widthOfString(statusText);
    const statusWidth = Math.max(textWidth + 40, 100); // Largeur minimale de 100px
    
    // Calculer la position x pour que le rectangle reste dans la page
    const statusX = Math.min(545 - statusWidth, 480);
    
    doc.roundedRect(statusX, statusY, statusWidth, 20, 10)
       .fillColor(statusBackground)
       .fill();
       
    // Augmenter la taille du texte légèrement et ajuster la position verticale 
    // pour qu'il soit bien centré dans le rectangle
    doc.fontSize(9)
       .fillColor(statusColor)
       .text(statusText, statusX, statusY + 6, { 
         width: statusWidth, 
         align: 'center' 
       });
    
    // Séparation
    const separatorY = Math.max(doc.y, currentY) + 20;
    doc.moveTo(50, separatorY).lineTo(545, separatorY).strokeColor(borderColor).lineWidth(0.5).stroke();
    
    // Informations du client
    const clientY = separatorY + 20;
    
    doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
    doc.text("Facturé à :", 50, clientY);
    
    doc.font('Helvetica-Bold').fontSize(12).fillColor('#000000');
    doc.text(invoice.client_name, 50, doc.y + 5);
    
    doc.font('Helvetica').fontSize(10).fillColor('#4B5563');
    currentY = doc.y + 5;
    
    if (invoice.client_address) {
      doc.text(invoice.client_address, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.client_email) {
      doc.text(invoice.client_email, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.client_phone) {
      doc.text(invoice.client_phone, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.client_siret) {
      doc.text(`SIRET : ${invoice.client_siret}`, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.forme_juridique) {
      doc.text(`Forme juridique : ${invoice.forme_juridique}`, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.code_ape) {
      doc.text(`Code APE : ${invoice.code_ape}`, 50, currentY);
      currentY = doc.y + 5;
    }
    
    if (invoice.client_tva) {
      doc.text(`TVA : ${invoice.client_tva}`, 50, currentY);
      currentY = doc.y + 5;
    }
    
    // Description du document (sur la droite)
    if (invoice.description) {
      doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
      doc.text("Description :", 350, clientY, { align: 'left' });
      
      doc.font('Helvetica').fontSize(10).fillColor('#1F2937');
      doc.text(invoice.description, 350, doc.y + 5, { align: 'left', width: 195 });
    }
    
    // Séparation après les infos client
    const clientSeparatorY = Math.max(doc.y, currentY) + 20;
    doc.moveTo(50, clientSeparatorY).lineTo(545, clientSeparatorY).strokeColor(borderColor).lineWidth(0.5).stroke();
    
    // Tableau des prestations avec un meilleur style
    const tableTop = clientSeparatorY + 20;
    const tableHeaders = ['Description', 'Qté', 'Prix unitaire', 'TVA', 'Total HT'];
    const colWidths = [240, 50, 85, 50, 70];
    
    // Dessiner l'en-tête du tableau avec fond gris clair
    doc.rect(50, tableTop, 495, 25).fillColor(lightGrayColor).fill();
    doc.rect(50, tableTop, 495, 25).strokeColor(borderColor).lineWidth(0.5).stroke();
    
    // Texte des en-têtes
    doc.font('Helvetica-Bold').fontSize(9).fillColor(secondaryColor);
    
    // Positionner correctement les en-têtes (valeurs fixes pour un meilleur alignement)
    doc.text('Description', 60, tableTop + 8, { width: 220, align: 'left' });
    doc.text('Qté', 295, tableTop + 8, { width: 50, align: 'center' });
    doc.text('Prix unitaire', 355, tableTop + 8, { width: 60, align: 'center' });
    doc.text('TVA', 430, tableTop + 8, { width: 30, align: 'center' });
    doc.text('Total HT', 485, tableTop + 8, { width: 50, align: 'right' });
    
    // Dessiner les lignes du tableau
    let rowY = tableTop + 25;
    let lastBottomY = rowY;
    
    // Lignes du tableau
    invoice.invoice_items.forEach((item: any, index: number) => {
      // Vérifier si nous avons besoin d'une nouvelle page
      if (rowY > 700) {
        doc.addPage();
        rowY = 50;
        
        // Redessiner l'en-tête du tableau sur la nouvelle page
        doc.rect(50, rowY, 495, 25).fillColor(lightGrayColor).fill();
        doc.rect(50, rowY, 495, 25).strokeColor(borderColor).lineWidth(0.5).stroke();
        
        // Texte des en-têtes avec positionnement fixe
        doc.font('Helvetica-Bold').fontSize(9).fillColor(secondaryColor);
        doc.text('Description', 60, rowY + 8, { width: 220, align: 'left' });
        doc.text('Qté', 295, rowY + 8, { width: 50, align: 'center' });
        doc.text('Prix unitaire', 355, rowY + 8, { width: 60, align: 'center' });
        doc.text('TVA', 430, rowY + 8, { width: 30, align: 'center' });
        doc.text('Total HT', 485, rowY + 8, { width: 50, align: 'right' });
            
        rowY += 25;
        lastBottomY = rowY;
      }
      
      // Mesurer la hauteur du texte de la description
      const descriptionText = item.description;
      const descriptionWidth = 220; // Largeur disponible pour la description
      const textHeight = doc.heightOfString(descriptionText, {
        width: descriptionWidth,
        lineGap: 2
      });
      
      // Calculer la hauteur de ligne en fonction du contenu
      const rowHeight = Math.max(30, textHeight + 16); // Minimum 30px ou hauteur du texte + marge
      
      // Dessiner le fond de la ligne
      if (index % 2 === 1) {
        doc.rect(50, rowY, 495, rowHeight).fillColor('#F9FAFB').fill();
      }
      
      // Dessiner les bordures de la ligne
      doc.rect(50, rowY, 495, rowHeight).strokeColor(borderColor).lineWidth(0.5).stroke();
      
      // Dessiner les séparations verticales entre les colonnes (positions fixes)
      doc.moveTo(290, rowY).lineTo(290, rowY + rowHeight).strokeColor(borderColor).lineWidth(0.5).stroke();
      doc.moveTo(350, rowY).lineTo(350, rowY + rowHeight).strokeColor(borderColor).lineWidth(0.5).stroke();
      doc.moveTo(420, rowY).lineTo(420, rowY + rowHeight).strokeColor(borderColor).lineWidth(0.5).stroke();
      doc.moveTo(470, rowY).lineTo(470, rowY + rowHeight).strokeColor(borderColor).lineWidth(0.5).stroke();
      
      // Contenu des cellules
      doc.font('Helvetica').fontSize(9).fillColor('#1F2937');
      
      // Description (aligné à gauche) avec support des retours à la ligne
      doc.text(descriptionText, 60, rowY + 8, { 
        width: descriptionWidth,
        lineGap: 2,
        height: rowHeight - 16,
        ellipsis: false
      });
      
      // Quantité (aligné au centre)
      doc.text(`${item.quantite} ${item.unite || ''}`, 290, rowY + 8, { 
        width: 60,
        align: 'center'
      });
      
      // Prix unitaire (aligné au centre)
      doc.text(`${item.prix_unitaire.toFixed(2)} €`, 350, rowY + 8, { 
        width: 70,
        align: 'center'
      });
      
      // TVA (aligné au centre)
      doc.text(`${item.taux_tva}%`, 420, rowY + 8, { 
        width: 50,
        align: 'center'
      });
      
      // Total HT (aligné à droite)
      doc.text(`${item.montant_ht.toFixed(2)} €`, 470, rowY + 8, { 
        width: 65, 
        align: 'right'
      });
      
      // Mettre à jour la position Y pour la prochaine ligne
      rowY += rowHeight;
      lastBottomY = rowY;
    });
    
    // Bloc des totaux avec style amélioré
    const totalsX = 380;
    const totalsWidth = 165;
    
    // Séparation
    doc.moveTo(50, lastBottomY).lineTo(545, lastBottomY).strokeColor(borderColor).lineWidth(1).stroke();
    rowY = lastBottomY + 20;
    
    // Bordure du bloc totaux avec un style moderne
    doc.roundedRect(totalsX, rowY, totalsWidth, 90, 5)
       .strokeColor(borderColor)
       .lineWidth(0.5)
       .stroke();
    
    // Fond subtil pour le bloc des totaux
    doc.roundedRect(totalsX, rowY, totalsWidth, 90, 5)
       .fillColor('#FCFCFD')
       .fill();
    
    // Ligne Total HT
    doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
    doc.text('Total HT :', totalsX + 15, rowY + 15, { width: 70, align: 'left' });
    
    doc.font('Helvetica-Bold').fontSize(10).fillColor('#1F2937');
    doc.text(`${invoice.total_ht.toFixed(2)} €`, totalsX + 85, rowY + 15, { width: 70, align: 'right' });
    
    // Séparation
    doc.moveTo(totalsX, rowY + 35).lineTo(totalsX + totalsWidth, rowY + 35)
       .strokeColor(borderColor)
       .lineWidth(0.5)
       .stroke();
    
    // Ligne Total TVA
    doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
    doc.text('Total TVA :', totalsX + 15, rowY + 45, { width: 70, align: 'left' });
    
    doc.font('Helvetica-Bold').fontSize(10).fillColor('#1F2937');
    doc.text(`${invoice.total_tva.toFixed(2)} €`, totalsX + 85, rowY + 45, { width: 70, align: 'right' });
    
    // Séparation
    doc.moveTo(totalsX, rowY + 65).lineTo(totalsX + totalsWidth, rowY + 65)
       .strokeColor(borderColor)
       .lineWidth(0.5)
       .stroke();
    
    // Bloc Total TTC avec fond coloré
    doc.roundedRect(totalsX, rowY + 65, totalsWidth, 25, 5)
       .fillColor('#FFF8F3')
       .fill();
    
    // Ligne Total TTC
    doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
    doc.text('Total TTC :', totalsX + 15, rowY + 72, { width: 70, align: 'left' });
    
    doc.font('Helvetica-Bold').fontSize(11).fillColor(primaryColor);
    doc.text(`${invoice.total_ttc.toFixed(2)} €`, totalsX + 85, rowY + 72, { width: 70, align: 'right' });
    
    // Bandeau de paiement pour les factures payées
    if (invoice.type === 'facture' && ['paye', 'partiellement_paye'].includes(invoice.statut)) {
      // Ajuster la position y pour le bandeau
      rowY += 120;
      
      // Vérifier si nous avons besoin d'une nouvelle page
      if (rowY > 700) {
        doc.addPage();
        rowY = 50;
      }
      
      // Créer un bandeau vert avec bordure et arrondis
      doc.roundedRect(50, rowY, 495, 80, 5)
         .fillColor('#ECFDF5') // vert clair
         .fill();
      
      doc.roundedRect(50, rowY, 495, 80, 5)
         .strokeColor('#34D399') // bordure verte
         .lineWidth(0.5)
         .stroke();
      
      // Ajouter une icône de cercle avec coche (simulée avec des primitives PDFKit)
      doc.circle(70, rowY + 25, 10)
         .fillColor('#059669') // vert foncé
         .fill();
      
      // Dessiner la coche
      doc.moveTo(65, rowY + 25)
         .lineTo(70, rowY + 30)
         .lineTo(75, rowY + 20)
         .strokeColor('#FFFFFF')
         .lineWidth(2)
         .stroke();
         
      // Texte du bandeau
      doc.font('Helvetica-Bold').fontSize(11).fillColor('#065F46'); // vert foncé
      doc.text(
        invoice.statut === 'paye' ? 'Facture payée' : 'Facture partiellement payée',
        90, rowY + 15
      );
      
      // Date de paiement
      doc.font('Helvetica').fontSize(10).fillColor('#065F46');
      if (invoice.date_paiement) {
        const paiementDate = new Date(invoice.date_paiement);
        const dateFormatee = paiementDate.toLocaleDateString('fr-FR');
        const heureFormatee = paiementDate.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        doc.text(
          `Paiement effectué le ${dateFormatee} à ${heureFormatee}`,
          90, rowY + 35
        );
      } else {
        doc.text(
          `Paiement enregistré le ${new Date().toLocaleDateString('fr-FR')}`,
          90, rowY + 35
        );
      }
      
      // Message supplémentaire pour paiement partiel
      if (invoice.statut === 'partiellement_paye') {
        doc.font('Helvetica-Bold').fontSize(10).fillColor('#065F46');
        doc.text(
          'Un règlement complémentaire est attendu pour cette facture.',
          90, rowY + 55
        );
      }
      
      rowY += 100;
    } else {
      rowY += 120;
    }
    
    // Informations de paiement
    if (invoice.mode_paiement || invoice.conditions_paiement || (invoice.mode_paiement === 'virement' && (decryptedCompanySettings?.iban || decryptedCompanySettings?.bic || decryptedCompanySettings?.banque))) {
      // Calculer la hauteur nécessaire
      let blockHeight = 80;
      if (invoice.mode_paiement === 'virement' && (decryptedCompanySettings?.iban || decryptedCompanySettings?.bic || decryptedCompanySettings?.banque)) {
        blockHeight = 140; // Plus de place pour les infos bancaires
      }

      // Vérifier si nous avons besoin d'une nouvelle page
      if (rowY > 700) {
        doc.addPage();
        rowY = 50;
      }

      // Fond pour le bloc d'infos de paiement avec bordure arrondie
      doc.roundedRect(50, rowY, 495, blockHeight, 5)
         .fillColor(lightGrayColor)
         .fill();

      doc.roundedRect(50, rowY, 495, blockHeight, 5)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();

      doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
      doc.text('Informations de paiement', 70, rowY + 15);

      // Séparation
      doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();

      let currentPaymentY = rowY + 40;

      // Afficher les informations sur deux colonnes
      if (invoice.mode_paiement) {
        doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
        doc.text('Mode de paiement :', 70, currentPaymentY);

        doc.font('Helvetica').fontSize(10).fillColor('#1F2937');
        let modeText = '';
        switch (invoice.mode_paiement) {
          case 'virement': modeText = 'Virement bancaire'; break;
          case 'carte': modeText = 'Carte bancaire'; break;
          case 'cheque': modeText = 'Chèque'; break;
          case 'jobi': modeText = 'Jobi'; break;
          case 'especes': modeText = 'Espèces'; break;
          default: modeText = invoice.mode_paiement;
        }
        doc.text(modeText, 70, currentPaymentY + 15);
      }

      if (invoice.conditions_paiement) {
        doc.font('Helvetica').fontSize(10).fillColor('#6B7280');
        doc.text('Conditions de paiement :', 300, currentPaymentY);

        doc.font('Helvetica').fontSize(10).fillColor('#1F2937');
        doc.text(invoice.conditions_paiement, 300, currentPaymentY + 15);
      }

      // Afficher les informations bancaires si le mode de paiement est virement
      if (invoice.mode_paiement === 'virement' && (decryptedCompanySettings?.iban || decryptedCompanySettings?.bic || decryptedCompanySettings?.banque)) {
        currentPaymentY += 40;

        doc.font('Helvetica-Bold').fontSize(10).fillColor('#6B7280');
        doc.text('Coordonnées bancaires :', 70, currentPaymentY);
        currentPaymentY += 15;

        doc.font('Helvetica').fontSize(9).fillColor('#1F2937');

        if (decryptedCompanySettings.banque) {
          doc.text(`Banque : ${decryptedCompanySettings.banque}`, 70, currentPaymentY);
          currentPaymentY += 12;
        }

        if (decryptedCompanySettings.iban) {
          doc.text(`IBAN : ${decryptedCompanySettings.iban}`, 70, currentPaymentY);
          currentPaymentY += 12;
        }

        if (decryptedCompanySettings.bic) {
          doc.text(`BIC : ${decryptedCompanySettings.bic}`, 70, currentPaymentY);
        }
      }

      rowY += blockHeight + 20;
    }
    
    // Mentions légales
    if (invoice.mentions_legales || invoice.mentions_tva || invoice.penalite_retard || invoice.indemnite_recouvrement) {
      // Vérifier si nous avons besoin d'une nouvelle page
      if (rowY > 700) {
        doc.addPage();
        rowY = 50;
      }
      
      // Fond pour le bloc des mentions légales
      doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
         .fillColor('#F9FAFB')
         .fill();
      
      doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();
      
      doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
      doc.text('Mentions légales', 70, rowY + 15);
      
      // Séparation
      doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();
      
      doc.font('Helvetica').fontSize(9).fillColor('#4B5563');
      const mentionsY = rowY + 40;
      rowY = mentionsY;
      
      let currentY = mentionsY;
      let maxWidth = 455; // Largeur disponible dans le bloc
      
      if (invoice.mentions_legales) {
        doc.text(invoice.mentions_legales, 70, currentY, { width: maxWidth });
        currentY = doc.y + 10;
        
        // Vérifier si nous avons besoin d'une nouvelle page après chaque section
        if (currentY > 750 && (invoice.mentions_tva || invoice.penalite_retard || invoice.indemnite_recouvrement)) {
          doc.addPage();
          
          // Recréer l'en-tête des mentions légales sur la nouvelle page
          rowY = 50;
          currentY = 90;
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .fillColor('#F9FAFB')
             .fill();
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
          doc.text('Mentions légales (suite)', 70, rowY + 15);
          
          doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica').fontSize(9).fillColor('#4B5563');
        }
      }
      
      if (invoice.mentions_tva) {
        doc.text(invoice.mentions_tva, 70, currentY, { width: maxWidth });
        currentY = doc.y + 10;
        
        // Vérifier si nous avons besoin d'une nouvelle page
        if (currentY > 750 && (invoice.penalite_retard || invoice.indemnite_recouvrement)) {
          doc.addPage();
          
          // Recréer l'en-tête sur la nouvelle page
          rowY = 50;
          currentY = 90;
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .fillColor('#F9FAFB')
             .fill();
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
          doc.text('Mentions légales (suite)', 70, rowY + 15);
          
          doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica').fontSize(9).fillColor('#4B5563');
        }
      }
      
      if (invoice.penalite_retard) {
        doc.text(`Pénalités de retard : ${invoice.penalite_retard}`, 70, currentY, { width: maxWidth });
        currentY = doc.y + 10;
        
        // Vérifier si nous avons besoin d'une nouvelle page
        if (currentY > 750 && invoice.indemnite_recouvrement) {
          doc.addPage();
          
          // Recréer l'en-tête sur la nouvelle page
          rowY = 50;
          currentY = 90;
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .fillColor('#F9FAFB')
             .fill();
          
          doc.roundedRect(50, rowY, 495, Math.min(200, 780 - rowY - 30), 5)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
          doc.text('Mentions légales (suite)', 70, rowY + 15);
          
          doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
             .strokeColor(borderColor)
             .lineWidth(0.5)
             .stroke();
          
          doc.font('Helvetica').fontSize(9).fillColor('#4B5563');
        }
      }
      
      if (invoice.indemnite_recouvrement) {
        doc.text(`Indemnité forfaitaire de recouvrement : ${invoice.indemnite_recouvrement}`, 70, currentY, { width: maxWidth });
        currentY = doc.y + 10;
      }
      
      rowY = Math.max(rowY + 200, currentY + 20);
    }
    
    // Notes
    if (invoice.notes) {
      // Ajouter un peu d'espace avant les notes
      rowY += 20;
      
      // Vérifier si nous avons besoin d'une nouvelle page
      if (rowY > 700) {
        doc.addPage();
        rowY = 50;
      } else if (rowY + 100 > 700) {
        // Si le contenu des notes risque de dépasser, mieux vaut commencer une nouvelle page
        doc.addPage();
        rowY = 50;
      }
      
      // Fond pour le bloc des notes
      doc.roundedRect(50, rowY, 495, Math.min(150, 780 - rowY - 30), 5)
         .fillColor('#FFF8F3')
         .fillOpacity(0.3)
         .fill();
      
      doc.roundedRect(50, rowY, 495, Math.min(150, 780 - rowY - 30), 5)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();
      
      doc.font('Helvetica-Bold').fontSize(11).fillColor('#1F2937');
      doc.text('Notes', 70, rowY + 15);
      
      // Séparation
      doc.moveTo(70, rowY + 30).lineTo(525, rowY + 30)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();
      
      doc.font('Helvetica').fontSize(9).fillColor('#4B5563');
      doc.text(invoice.notes, 70, rowY + 40, { width: 455 });
    }
    
    // Pied de page simple sans numéro de page
    const footerY = 780;
    doc.moveTo(50, footerY).lineTo(545, footerY)
       .strokeColor(borderColor)
       .lineWidth(0.5)
       .stroke();
    
    doc.fontSize(8).fillColor('#777777');
    doc.text('JobPartiel.fr – Le jobbing malin pour arrondir vos fins de mois', 50, footerY + 1, { align: 'center' });
    
    // Ajouter le même pied de page sur les nouvelles pages
    doc.on('pageAdded', () => {
      const footerY = 780;
      doc.moveTo(50, footerY).lineTo(545, footerY)
         .strokeColor(borderColor)
         .lineWidth(0.5)
         .stroke();
      
      doc.fontSize(8).fillColor('#777777');
      doc.text('JobPartiel.fr – Le jobbing malin pour arrondir vos fins de mois', 50, footerY + 1, { align: 'center' });
    });
  } catch (err) {
    logger.error('Erreur dans generatePDFContent:', err);
    throw err;
  }
};

// Générer un PDF
export const generatePDF = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const invoiceId = req.params.id;
    
    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();
    
    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }
    
    // Récupérer les informations de l'utilisateur (jobbeur)
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur',
        error: userError.message
      });
    }

    // Déchiffrer les données du profil utilisateur
    const decryptedUserData = userData ? await decryptProfilDataAsync(userData) : null;

    // Récupérer l'email de l'utilisateur depuis la table users
    const { data: userEmailData, error: userEmailError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();
      
    if (userEmailError) {
      logger.error('Erreur lors de la récupération de l\'email utilisateur:', userEmailError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'email utilisateur',
        error: userEmailError.message
      });
    }

    // Créer un dossier temporaire si nécessaire
    const tempDir = path.join(__dirname, '../../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Chemin du fichier PDF
    const displayNumber = getDisplayNumber(invoice);
    const fileName = `${displayNumber.replace(/\//g, '-')}.pdf`;
    const filePath = path.join(tempDir, fileName);
    
    // Créer le PDF avec un meilleur style
    const doc = new PDFDocument({ 
      size: 'A4', 
      margin: 50,
      info: {
        Title: `${invoice.type.toUpperCase()} ${displayNumber}`,
        Author: userData.prenom + ' ' + userData.nom,
        Subject: `${invoice.type.toUpperCase()} pour ${invoice.client_name}`,
        Producer: 'JobPartiel',
      }
    });
    
    // Créer un flux de sortie pour le fichier temporaire (pour le traiter ensuite)
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);
    
    // Génération du contenu PDF avec les données déchiffrées
    const decryptedInvoice = await decryptInvoiceDataAsync(invoice);
    await generatePDFContent(req, doc, decryptedInvoice, decryptedUserData);
    
    // Finaliser le document
    doc.end();
    
    // Attendre que le fichier soit écrit
    await new Promise<void>((resolve, reject) => {
      stream.on('finish', () => {
        resolve();
      });
      stream.on('error', (err) => {
        reject(err);
      });
    });
    
    // Supprimer les entrées d'historique précédentes de génération PDF pour ce document
    await supabase
      .from('invoice_history')
      .delete()
      .eq('invoice_id', invoiceId)
      .eq('user_id', userId)
      .eq('action', 'Génération du PDF');
    
    // Enregistrer l'action dans l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: invoiceId,
      user_id: userId,
      action: 'Génération du PDF',
      details: fileName
    });
    
    // Envoyer le fichier
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    // Supprimer le fichier après l'envoi
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });
  } catch (err: any) {
    logger.error('Erreur lors de la génération du PDF:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du PDF',
      error: err.message
    });
  }
};

// Générer un PDF pour un document reçu
export const generateReceivedPDF = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const invoiceId = req.params.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Récupérer l'email de l'utilisateur connecté et créer son hash
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur'
      });
    }

    const decryptedUserEmail = (await decryptUserDataAsync(userData)).email;
    const userEmailHash = hashEmail(decryptedUserEmail);

    // Récupérer le document par ID
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .single();

    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Vérifier si l'utilisateur est le destinataire en comparant les emails
    if (!invoice.client_email) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'êtes pas autorisé à accéder à ce document'
      });
    }

    try {
      const decryptedClientEmail = await decryptDataAsync(invoice.client_email);
      const clientEmailHash = hashEmail(decryptedClientEmail);

      if (clientEmailHash !== userEmailHash) {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'êtes pas autorisé à accéder à ce document'
        });
      }
    } catch (error) {
      return res.status(403).json({
        success: false,
        message: 'Erreur lors de la vérification des permissions'
      });
    }

    // Récupérer les informations de l'émetteur du document (profil + entreprise)
    const { data: emitterData, error: emitterError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', invoice.user_id)
      .single();

    if (emitterError) {
      logger.error('Erreur lors de la récupération des informations de l\'émetteur:', emitterError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations de l\'émetteur'
      });
    }

    // Récupérer les paramètres d'entreprise de l'émetteur
    const { data: emitterCompanySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('*')
      .eq('user_id', invoice.user_id)
      .single();

    if (companyError) {
      logger.error('Erreur lors de la récupération des paramètres d\'entreprise de l\'émetteur:', companyError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des paramètres d\'entreprise de l\'émetteur'
      });
    }

    // Déchiffrer les données du profil et de l'entreprise de l'émetteur
    const decryptedEmitterData = emitterData ? await decryptProfilDataAsync(emitterData) : null;
    const decryptedEmitterCompanySettings = emitterCompanySettings ? await decryptCompanyDataAsync(emitterCompanySettings) : null;

    // Créer un dossier temporaire si nécessaire
    const tempDir = path.join(__dirname, '../../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Chemin du fichier PDF
    const displayNumber = getDisplayNumber(invoice);
    const fileName = `${displayNumber.replace(/\//g, '-')}.pdf`;
    const filePath = path.join(tempDir, fileName);

    // Créer le PDF avec un meilleur style
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `${invoice.type.toUpperCase()} ${displayNumber}`,
        Author: decryptedEmitterData.prenom + ' ' + decryptedEmitterData.nom,
        Subject: `${invoice.type.toUpperCase()} pour ${invoice.client_name}`,
        Producer: 'JobPartiel',
      }
    });

    // Créer un flux de sortie pour le fichier temporaire
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // Génération du contenu PDF avec les données déchiffrées
    const decryptedInvoice = await decryptInvoiceDataAsync(invoice);

    // Créer un objet userData compatible avec generatePDFContent en utilisant les paramètres d'entreprise
    const emitterUserData = {
      prenom: decryptedEmitterData?.prenom || '',
      nom: decryptedEmitterData?.nom || '',
      // Utiliser les paramètres d'entreprise pour les autres champs
      entreprise: decryptedEmitterCompanySettings?.nom || '',
      adresse: decryptedEmitterCompanySettings?.adresse || '',
      code_postal: decryptedEmitterCompanySettings?.code_postal || '',
      ville: decryptedEmitterCompanySettings?.ville || '',
      telephone: decryptedEmitterCompanySettings?.telephone || '',
      email: decryptedEmitterCompanySettings?.email || '',
      site_web: decryptedEmitterCompanySettings?.site_web || '',
      siret: decryptedEmitterCompanySettings?.siret || '',
      tva: decryptedEmitterCompanySettings?.tva || '',
      rcs: decryptedEmitterCompanySettings?.rcs || '',
      capital: decryptedEmitterCompanySettings?.capital || '',
      iban: decryptedEmitterCompanySettings?.iban || '',
      bic: decryptedEmitterCompanySettings?.bic || '',
      banque: decryptedEmitterCompanySettings?.banque || '',
      forme_juridique: decryptedEmitterCompanySettings?.forme_juridique || '',
      code_ape: decryptedEmitterCompanySettings?.code_ape || '',
      mention_pied_page: decryptedEmitterCompanySettings?.mention_pied_page || ''
    };

    await generatePDFContent(req, doc, decryptedInvoice, emitterUserData, true);

    // Finaliser le document
    doc.end();

    // Attendre que le fichier soit écrit
    await new Promise<void>((resolve, reject) => {
      stream.on('finish', () => {
        resolve();
      });
      stream.on('error', (err) => {
        reject(err);
      });
    });

    // Envoyer le fichier
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Supprimer le fichier après l'envoi
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });
  } catch (err: any) {
    logger.error('Erreur lors de la génération du PDF reçu:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du PDF',
      error: err.message
    });
  }
};

// Envoyer le document par email
export const sendInvoiceByEmail = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const invoiceId = req.params.id;
    
    // Validation de la requête
    const validationResult = sendInvoiceSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({  
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors
      });
      return;
    }
    
    const { email, message } = validationResult.data;
    
    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();

    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Si le document est un brouillon, le valider automatiquement lors de l'envoi
    let updatedInvoice = invoice;
    if (invoice.statut === 'brouillon') {
      // Générer un numéro officiel si c'est un brouillon
      const { data: officialNumber, error: numberError } = await supabase.rpc(
        'generate_invoice_number',
        { doc_type: invoice.type, is_draft: false }
      );

      if (numberError) {
        logger.error('Erreur lors de la génération du numéro officiel:', numberError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération du numéro officiel'
        });
      }

      const { data: validatedInvoice, error: validationError } = await supabase
        .from('invoices')
        .update({
          statut: 'envoye',
          number: officialNumber,
          draft_number: null // Supprimer le numéro de brouillon
        })
        .eq('id', invoiceId)
        .select(`
          *,
          invoice_items:invoice_items(*)
        `)
        .single();

      if (validationError) {
        logger.error('Erreur lors de la validation du brouillon:', validationError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la validation du document'
        });
      }

      updatedInvoice = validatedInvoice;
      logger.info(`Document ${invoiceId} validé automatiquement lors de l'envoi avec le numéro ${officialNumber}`);
    }
    
    // Protection anti-spam : vérifier si un email a déjà été envoyé dans les 30 minutes précédentes
    const thirtyMinutesAgo = new Date();
    thirtyMinutesAgo.setMinutes(thirtyMinutesAgo.getMinutes() - 30);
    
    const { data: recentSendings, error: sendingsError } = await supabase
      .from('invoice_sendings')
      .select('sent_at')
      .eq('invoice_id', invoiceId)
      .eq('status', 'success')
      .gte('sent_at', thirtyMinutesAgo.toISOString())
      .order('sent_at', { ascending: false });
    
    if (sendingsError) {
      logger.error('Erreur lors de la vérification des envois récents:', sendingsError);
    } else if (recentSendings && recentSendings.length > 0) {
      // Un email a déjà été envoyé dans les 30 minutes précédentes
      const lastSending = new Date(recentSendings[0].sent_at);
      const minutesAgo = Math.floor((Date.now() - lastSending.getTime()) / (1000 * 60));
      const minutesToWait = 30 - minutesAgo;
      
      return res.status(429).json({
        success: false,
        message: `Un email a déjà été envoyé il y a ${minutesAgo} minute(s). Veuillez attendre ${minutesToWait} minute(s) avant de réessayer.`
      });
    }
    
    // Récupérer les informations de l'utilisateur (jobbeur)
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur',
        error: userError.message
      });
    }

    // Déchiffrer les données du profil utilisateur
    const decryptedUserData = userData ? await decryptProfilDataAsync(userData) : null;
    
    // Récupérer l'email de l'utilisateur depuis la table users
    const { data: userEmailData, error: userEmailError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userEmailError) {
      logger.error('Erreur lors de la récupération de l\'email utilisateur:', userEmailError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'email utilisateur',
        error: userEmailError.message
      });
    }

    // Décrypter l'email de l'utilisateur
    const decryptedUserEmail = userEmailData ? await decryptUserDataAsync(userEmailData) : null;

    // S'assurer que l'email existe
    if (!decryptedUserEmail || !decryptedUserEmail.email) {
      logger.error('Email de l\'utilisateur non trouvé:', { userId });
      return res.status(500).json({
        success: false,
        message: 'Email de l\'utilisateur non trouvé'
      });
    }

    // Rechercher si l'utilisateur destinataire existe déjà dans la base en utilisant le hash d'email
    const emailHashToSearch = hashEmail(email);
    const { data: destinataire, error: destinataireError } = await supabase
      .from('users')
      .select('id')
      .eq('email_hash', emailHashToSearch)
      .maybeSingle();

    if (destinataire) {
        // Chercher si l'email correspond à un utilisateur de la plateforme
        const emailHash = hashEmail(email);
        const { data: recipientUser, error: recipientError } = await supabase
          .from('users')
          .select('id')
          .eq('email_hash', emailHash)
          .maybeSingle();

        let invoiceClient = null;

        if (recipientUser && !recipientError) {
          // L'email correspond à un utilisateur de la plateforme
          // Chercher ou créer un enregistrement dans invoices_client avec l'UID de l'utilisateur
          const { data: existingClient, error: existingClientError } = await supabase
            .from('invoices_client')
            .select('id')
            .eq('user_id', userId)
            .eq('client_user_id', recipientUser.id)
            .maybeSingle();

          if (existingClient && !existingClientError) {
            invoiceClient = existingClient;
          }
        } else {
          // L'email ne correspond pas à un utilisateur, chercher par email chiffré
          const { data: allClients, error: clientError } = await supabase
            .from('invoices_client')
            .select('id, email')
            .eq('user_id', userId)
            .is('client_user_id', null); // Seulement les clients non-utilisateurs

          if (allClients && !clientError) {
            // Déchiffrer et comparer les emails
            for (const client of allClients) {
              try {
                const decryptedEmail = await decryptDataAsync(client.email);
                if (decryptedEmail === email) {
                  invoiceClient = { id: client.id };
                  break;
                }
              } catch (decryptError) {
                // Ignorer les erreurs de déchiffrement pour les anciens emails non chiffrés
                if (client.email === email) {
                  invoiceClient = { id: client.id };
                  break;
                }
              }
            }
          }
        }

        // Si le client n'existe pas, le créer
        if (!invoiceClient) {
          const clientData: any = {
            user_id: userId,
            nom: await encryptDataAsync(invoice.client_name || 'Client'),
            email: await encryptDataAsync(email),
            telephone: invoice.client_telephone ? await encryptDataAsync(invoice.client_telephone) : null,
            adresse: invoice.client_adresse ? await encryptDataAsync(invoice.client_adresse) : null,
            siret: invoice.client_siret ? await encryptDataAsync(invoice.client_siret) : null,
            tva: invoice.client_numero_tva ? await encryptDataAsync(invoice.client_numero_tva) : null
          };

          // Si c'est un utilisateur de la plateforme, ajouter son UID
          if (recipientUser && !recipientError) {
            clientData.client_user_id = recipientUser.id;
          }

          const { data: newClient, error: createError } = await supabase
            .from('invoices_client')
            .insert(clientData)
            .select('id')
            .single();

          if (createError) {
            logger.error('Erreur lors de la création du client:', createError);
          } else {
            invoiceClient = newClient;
            logger.info(`Nouveau client créé avec l'ID: ${newClient.id}`);
          }
        }

        // Mettre à jour le client_id dans la facture si on a un client
        if (invoiceClient) {
          const { error: updateError } = await supabase
            .from('invoices')
            .update({ client_id: invoiceClient.id })
            .eq('id', invoiceId);

          if (updateError) {
            logger.error('Erreur lors de la mise à jour du client_id:', updateError);
            // On continue malgré l'erreur, car ce n'est pas bloquant pour l'envoi
          } else {
            logger.info(`client_id mis à jour pour le document ${invoiceId}`);
          }
        }
    }

    // Créer un dossier temporaire si nécessaire
    const tempDir = path.join(__dirname, '../../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Chemin du fichier PDF
    const displayNumber = getDisplayNumber(updatedInvoice);
    const fileName = `${displayNumber.replace(/\//g, '-')}.pdf`;
    const filePath = path.join(tempDir, fileName);

    // Créer le document PDF avec les mêmes métadonnées
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `${updatedInvoice.type.toUpperCase()} ${displayNumber}`,
        Author: userData.prenom + ' ' + userData.nom,
        Subject: `${updatedInvoice.type.toUpperCase()} pour ${updatedInvoice.client_name}`,
        Producer: 'JobPartiel',
      }
    });

    // Flux de sortie vers le fichier
    const stream = fs.createWriteStream(filePath);
    doc.pipe(stream);

    // Déchiffrer les données pour le PDF et les emails
    const decryptedInvoice = await decryptInvoiceDataAsync(updatedInvoice);

    // Utiliser la même fonction que generatePDF pour créer le contenu avec les données déchiffrées
    await generatePDFContent(req, doc, decryptedInvoice, decryptedUserData);

    // Finaliser le document
    doc.end();

    // Attendre que le fichier soit écrit
    await new Promise<void>((resolve, reject) => {
      stream.on('finish', () => {
        resolve();
      });
      stream.on('error', (err) => {
        reject(err);
      });
    });

    // Type du document pour l'email
    const documentType = decryptedInvoice.type === 'devis' ? 'Devis' : decryptedInvoice.type === 'facture' ? 'Facture' : 'Avoir';

    try {
      await sendInvoiceEmail(email, {
        subject: `${documentType} ${displayNumber} - ${decryptedUserData.prenom} ${decryptedUserData.nom}`,
        html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <style>
                body {
                  font-family: Arial, sans-serif;
                  color: #333;
                  line-height: 1.6;
                  margin: 0;
                  padding: 0;
                }
                .container {
                  max-width: 600px;
                  margin: 0 auto;
                  padding: 20px;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                }
                .header {
                  background-color: #FF7A35;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px 8px 0 0;
                  margin: -20px -20px 20px;
                }
                .footer {
                  background-color: #f9fafb;
                  padding: 15px 20px;
                  border-top: 1px solid #e5e7eb;
                  margin: 20px -20px -20px;
                  border-radius: 0 0 8px 8px;
                  font-size: 12px;
                  color: #6b7280;
                }
                h1 {
                  margin: 0;
                  font-size: 24px;
                }
                .info-section {
                  margin-bottom: 20px;
                  border-bottom: 1px solid #f3f4f6;
                  padding-bottom: 15px;
                }
                .info-label {
                  font-weight: bold;
                  color: #6b7280;
                }
                .info-value {
                  color: #1f2937;
                }
                .message-box {
                  background-color: #fff8f3;
                  border-left: 4px solid #FF7A35;
                  padding: 15px;
                  margin: 20px 0;
                }
                .amount-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 8px;
                }
                .total-row {
                  display: flex;
                  justify-content: space-between;
                  font-weight: bold;
                  font-size: 18px;
                  color: #FF7A35;
                  border-top: 2px solid #FF7A35;
                  padding-top: 8px;
                  margin-top: 8px;
                }
                .button {
                  display: inline-block;
                  background-color: #FF7A35;
                  color: white;
                  padding: 12px 24px;
                  text-decoration: none;
                  border-radius: 4px;
                  font-weight: bold;
                  margin-top: 20px;
                }
                .button-accept {
                  display: inline-block;
                  background-color: #15803D;
                  color: white;
                  padding: 12px 24px;
                  text-decoration: none;
                  border-radius: 4px;
                  font-weight: bold;
                  margin-top: 20px;
                }
                .acceptance-section {
                  background-color: #ECFDF5;
                  padding: 20px;
                  margin: 20px 0;
                  border-radius: 4px;
                  border: 1px solid #D1FAE5;
                }
                @media (max-width: 600px) {
                  .container {
                    width: 100%;
                    padding: 10px;
                  }
                  .header {
                    padding: 10px;
                    margin: -10px -10px 15px;
                  }
                  .footer {
                    margin: 15px -10px -10px;
                    padding: 10px;
                  }
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>${documentType} ${displayNumber}</h1>
                </div>
                
                <p>Bonjour,</p>
                
                <p>Veuillez trouver ci-joint votre ${decryptedInvoice.type === 'devis' ? 'devis' : decryptedInvoice.type === 'facture' ? 'facture' : "avoir"} <strong>${displayNumber}</strong>.</p>

                <div class="info-section">
                  <p><span class="info-label">Date du document:</span> <span class="info-value">${formatDate(decryptedInvoice.date_creation)}</span></p>
                  ${decryptedInvoice.date_validite ? `<p><span class="info-label">Valable jusqu'au:</span> <span class="info-value">${formatDate(decryptedInvoice.date_validite)}</span></p>` : ''}
                  ${decryptedInvoice.description ? `<p><span class="info-label">Description:</span> <span class="info-value">${decryptedInvoice.description}</span></p>` : ''}
                </div>

                <div class="info-section">
                  <p><span class="info-label">Montants:</span></p>
                  <div class="amount-row">
                    <span>Total HT:</span>
                    <span>${decryptedInvoice.total_ht.toFixed(2)} €</span>
                  </div>
                  <div class="amount-row">
                    <span>TVA:</span>
                    <span>${decryptedInvoice.total_tva.toFixed(2)} €</span>
                  </div>
                  <div class="total-row">
                    <span>Total TTC:</span>
                    <span>${decryptedInvoice.total_ttc.toFixed(2)} €</span>
                  </div>
                </div>

                ${decryptedInvoice.type === 'devis' ? `
                <div class="acceptance-section">
                  <p><strong>Accepter ce devis :</strong></p>
                  <p>Pour accepter ce devis et permettre à ${decryptedUserData.prenom} ${decryptedUserData.nom} de démarrer les travaux, veuillez cliquer sur le bouton ci-dessous :</p>
                  <a href="https://jobpartiel.fr/quote-acceptance/${decryptedInvoice.id}" class="button-accept">Accepter et signer le devis</a>
                  <p style="font-size: 12px; margin-top: 10px;">En cliquant sur ce bouton, vous allez accéder à un formulaire sécurisé pour finaliser l'acceptation du devis.</p>
                </div>
                ` : ''}

                <div class="info-section">
                  <p><span class="info-label">Mode de paiement:</span>
                  <span class="info-value">${
                    decryptedInvoice.mode_paiement === 'virement' ? 'Virement bancaire' :
                    decryptedInvoice.mode_paiement === 'carte' ? 'Carte bancaire' :
                    decryptedInvoice.mode_paiement === 'cheque' ? 'Chèque' :
                    decryptedInvoice.mode_paiement === 'jobi' ? 'Jobi' :
                    decryptedInvoice.mode_paiement === 'especes' ? 'Espèces' : decryptedInvoice.mode_paiement
                  }</span></p>
                  ${decryptedInvoice.conditions_paiement ? `<p><span class="info-label">Conditions de paiement:</span> <span class="info-value">${decryptedInvoice.conditions_paiement}</span></p>` : ''}
                </div>
                
                ${message ? `
                <div class="message-box">
                  <p><span class="info-label">Message:</span></p>
                  <p>${message}</p>
                </div>
                ` : ''}
                
                <p>Pour plus de détails, veuillez consulter le document en pièce jointe.</p>
                
                <p>Cordialement,</p>
                <p><strong>${decryptedUserData.prenom} ${decryptedUserData.nom}</strong></p>

                <div class="footer">
                  <p>Ce document a été généré automatiquement via <strong>JobPartiel.fr</strong></p>
                  <p>Le jobbing malin pour arrondir vos fins de mois</p>
                </div>
              </div>
            </body>
            </html>
          `,
          attachments: [
            {
              filename: fileName,
              path: filePath
            }
          ]
        });

      // Envoyer également une copie au créateur (jobbeur)
      await sendInvoiceEmail(decryptedUserEmail.email, {
        subject: `Copie - ${documentType} ${displayNumber} envoyé à ${email}`,
        html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <style>
                body {
                  font-family: Arial, sans-serif;
                  color: #333;
                  line-height: 1.6;
                  margin: 0;
                  padding: 0;
                }
                .container {
                  max-width: 600px;
                  margin: 0 auto;
                  padding: 20px;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                }
                .header {
                  background-color: #FF7A35;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px 8px 0 0;
                  margin: -20px -20px 20px;
                }
                .copy-notice {
                  background-color: #f3f4f6;
                  padding: 10px 15px;
                  border-radius: 4px;
                  margin-bottom: 20px;
                  font-style: italic;
                }
                .footer {
                  background-color: #f9fafb;
                  padding: 15px 20px;
                  border-top: 1px solid #e5e7eb;
                  margin: 20px -20px -20px;
                  border-radius: 0 0 8px 8px;
                  font-size: 12px;
                  color: #6b7280;
                }
                h1 {
                  margin: 0;
                  font-size: 24px;
                }
                .info-section {
                  margin-bottom: 20px;
                  border-bottom: 1px solid #f3f4f6;
                  padding-bottom: 15px;
                }
                .info-label {
                  font-weight: bold;
                  color: #6b7280;
                }
                .info-value {
                  color: #1f2937;
                }
                .message-box {
                  background-color: #fff8f3;
                  border-left: 4px solid #FF7A35;
                  padding: 15px;
                  margin: 20px 0;
                }
                .amount-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 8px;
                }
                .total-row {
                  display: flex;
                  justify-content: space-between;
                  font-weight: bold;
                  font-size: 18px;
                  color: #FF7A35;
                  border-top: 2px solid #FF7A35;
                  padding-top: 8px;
                  margin-top: 8px;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>Copie - ${decryptedInvoice.type === 'devis' ? 'Devis' : decryptedInvoice.type === 'facture' ? 'Facture' : 'Avoir'} ${displayNumber}</h1>
                </div>

                <div class="copy-notice">
                  <p>Ceci est une copie du document envoyé à <strong>${email}</strong> le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}.</p>
                </div>

                <div class="info-section">
                  <p><span class="info-label">Client:</span> <span class="info-value">${decryptedInvoice.client_name}</span></p>
                  <p><span class="info-label">Date du document:</span> <span class="info-value">${formatDate(decryptedInvoice.date_creation)}</span></p>
                  ${decryptedInvoice.date_validite ? `<p><span class="info-label">Valable jusqu'au:</span> <span class="info-value">${formatDate(decryptedInvoice.date_validite)}</span></p>` : ''}
                  ${decryptedInvoice.description ? `<p><span class="info-label">Description:</span> <span class="info-value">${decryptedInvoice.description}</span></p>` : ''}
                </div>

                <div class="info-section">
                  <p><span class="info-label">Montants:</span></p>
                  <div class="amount-row">
                    <span>Total HT:</span>
                    <span>${decryptedInvoice.total_ht.toFixed(2)} €</span>
                  </div>
                  <div class="amount-row">
                    <span>TVA:</span>
                    <span>${decryptedInvoice.total_tva.toFixed(2)} €</span>
                  </div>
                  <div class="total-row">
                    <span>Total TTC:</span>
                    <span>${decryptedInvoice.total_ttc.toFixed(2)} €</span>
                  </div>
                </div>
                
                ${message ? `
                <div class="message-box">
                  <p><span class="info-label">Message envoyé au client:</span></p>
                  <p>${message}</p>
                </div>
                ` : ''}
                
                <div class="footer">
                  <p>Ce document a été généré automatiquement via <strong>JobPartiel.fr</strong></p>
                  <p>Le jobbing malin pour arrondir vos fins de mois</p>
                </div>
              </div>
            </body>
            </html>
          `,
          attachments: [
            {
              filename: fileName,
              path: filePath
            }
          ]
        });
      
      // Le statut a déjà été mis à jour plus haut si c'était un brouillon
      
      // Purger les enregistrements d'envoi de plus de 2 heures
      const twoHoursAgo = new Date();
      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);
      
      await supabase
        .from('invoice_sendings')
        .delete()
        .lt('sent_at', twoHoursAgo.toISOString());
        
      // Enregistrer l'envoi avec email chiffré
      await supabase.from('invoice_sendings').insert({
        invoice_id: invoiceId,
        recipient_email: await encryptDataAsync(email),
        status: 'success',
        message: message || null
      });
      
      // Enregistrer l'action dans l'historique
      await supabase.from('invoice_history').insert({
        invoice_id: invoiceId,
        user_id: userId,
        action: 'Envoi de l\'email',
        details: email
      });
      
      // Enregistrer l'activité d'envoi de document
      const actionType = decryptedInvoice.type === 'facture'
        ? 'envoi_facture'
        : decryptedInvoice.type === 'devis'
          ? 'envoi_devis'
          : 'envoi_avoir';

      // Vérifier que userId est défini avant d'appeler logUserActivity
      if (userId) {
        await logUserActivity(
          userId,
          actionType,
          invoiceId,
          'invoices',
          {
            number: displayNumber,
            client: decryptedInvoice.client_name,
            email: email, // L'email sera masqué par le système d'activité
            was_draft: invoice.statut === 'brouillon' // Indiquer si c'était un brouillon
          },
          req ? getIpFromRequest(req) : undefined
        );
      }

      // Invalider le cache si nécessaire
        const invoiceKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
      const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
      
      await redis.del(invoiceKey);
      const keys = await redis.keys(listCachePattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
      
      
      res.status(200).json({
        success: true,
        message: 'Document envoyé avec succès'
      });
    } catch (emailErr: any) {
      logger.error('Erreur lors de l\'envoi de l\'email:', emailErr);
      
      // Enregistrer l'échec avec email chiffré
      await supabase.from('invoice_sendings').insert({
        invoice_id: invoiceId,
        recipient_email: await encryptDataAsync(email),
        status: 'failed',
        message: emailErr.message
      });
      
      // Supprimer le fichier
      fs.unlinkSync(filePath);
      
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'envoi de l\'email',
        error: emailErr.message
      });
    }
  } catch (err: any) {
    logger.error('Erreur lors de l\'envoi du document:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi du document',
      error: err.message
    });
  }
};

// Supprimer un document
export const deleteInvoice = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    const invoiceId = req.params.id;
    
    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();
    
    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }
    
    // Vérifier si le document peut être supprimé (seulement les brouillons)
    if (invoice.statut !== 'brouillon') {
      res.status(400).json({
        success: false,
        message: 'Seuls les documents en brouillon peuvent être supprimés'
      });
      return;
    }
    
    // Vérifier s'il y a des documents qui font référence à ce document via facture_origine_id ou devis_origine_id
    const { data: referencingDocs, error: referencingError } = await supabase
      .from('invoices')
      .select('id, type')
      .or(`facture_origine_id.eq.${invoiceId},devis_origine_id.eq.${invoiceId}`);
    
    if (referencingError) {
      logger.error('Erreur lors de la vérification des documents liés:', referencingError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification des documents liés',
        error: referencingError.message
      });
    }
    
    // S'il y a des documents qui font référence à celui-ci, on doit d'abord mettre à jour ces références
    if (referencingDocs && referencingDocs.length > 0) {
      // Mettre à jour les références pour supprimer la contrainte de clé étrangère
      for (const doc of referencingDocs) {
        const updateData: { [key: string]: any } = {};
        
        if (invoice.type === 'devis') {
          updateData['devis_origine_id'] = null;
        } else if (invoice.type === 'facture') {
          updateData['facture_origine_id'] = null;
        }
        
        const { error: updateError } = await supabase
          .from('invoices')
          .update(updateData)
          .eq('id', doc.id);
        
        if (updateError) {
          logger.error(`Erreur lors de la mise à jour des références pour le document ${doc.id}:`, updateError);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la suppression des références liées',
            error: updateError.message
          });
        }
      }
    }
    
    // Supprimer les items associés
    const { error: itemsDeleteError } = await supabase
      .from('invoice_items')
      .delete()
      .eq('invoice_id', invoiceId);
    
    if (itemsDeleteError) {
      logger.error('Erreur lors de la suppression des items:', itemsDeleteError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression des items associés',
        error: itemsDeleteError.message
      });
    }
    
    // Supprimer le document
    const { error: invoiceDeleteError } = await supabase
      .from('invoices')
      .delete()
      .eq('id', invoiceId);
    
    if (invoiceDeleteError) {
      logger.error('Erreur lors de la suppression du document:', invoiceDeleteError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du document',
        error: invoiceDeleteError.message
      });
    }
    
    // Enregistrer l'activité de suppression dans l'historique
    const actionType = invoice.type === 'facture' 
      ? 'suppression_facture' 
      : invoice.type === 'devis' 
        ? 'suppression_devis' 
        : 'suppression_avoir';
        
    await logUserActivity(
      userId,
      actionType,
      invoiceId,
      'invoices',
      {
        number: invoice.number,
        client: invoice.client_name
      },
      req ? getIpFromRequest(req) : undefined
    );
    
    // Invalider les caches pertinents
    const invoiceCacheKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    
    await redis.del(invoiceCacheKey);
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    res.status(200).json({
      success: true,
      message: 'Document supprimé avec succès'
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la suppression du document:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression du document',
      error: err.message
    });
  }
};

// Dupliquer un document
export const duplicateInvoice = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    // Vérifier si l'utilisateur a rempli ses informations d'entreprise
    const { data: companySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (companyError || !companySettings || !companySettings.nom) {
      res.status(403).json({
        success: false,
        message: 'Vous devez d\'abord compléter les informations de votre entreprise avant de dupliquer un document',
        toastType: 'error'
      });
      return;
    }
    
    const invoiceId = req.params.id;
    
    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();
    
    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }
    
    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium, quoteLimit, invoiceLimit } = await getUserSubscriptionLimits(userId);
    
    // Vérifier les limites selon l'abonnement pour les devis et factures
    if (!isPremium) {
      // Requête pour compter le nombre de documents par type
      let { data: documentCount, error: countError } = await supabase
        .from('invoices')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('type', invoice.type);
        
      // Si c'est un avoir, compter aussi les factures car ils partagent la même limite
      if (invoice.type === 'avoir') {
        const { data: invoiceAndCreditCount, error: countError2 } = await supabase
          .from('invoices')
          .select('id', { count: 'exact' })
          .eq('user_id', userId)
          .in('type', ['facture', 'avoir']);
          
        if (countError2) {
          logger.error('Erreur lors du comptage des factures/avoirs:', countError2);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la vérification des limitations'
          });
        }
        
        documentCount = invoiceAndCreditCount;
      }
        
      if (countError) {
        logger.error('Erreur lors du comptage des documents:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }
      
      const documentLimit = invoice.type === 'devis' 
        ? quoteLimit
        : invoice.type === 'facture' || invoice.type === 'avoir'
          ? invoiceLimit
          : null;
      
      if (documentLimit !== null && (documentCount?.length || 0) >= documentLimit) {
        res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${documentLimit} ${invoice.type === 'devis' ? 'devis' : invoice.type === 'facture' ? 'factures' : 'avoirs'} pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          toastType: 'error',
          limitReached: true
        });
        return;
      }
    }
    
    // Générer un nouveau numéro de brouillon (les duplications sont toujours des brouillons)
    const { data: draftNumberResult, error: draftNumberError } = await supabase.rpc(
      'generate_invoice_number',
      { doc_type: invoice.type, is_draft: true }
    );

    if (draftNumberError) {
      logger.error('Erreur lors de la génération du numéro de brouillon:', draftNumberError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du numéro de brouillon',
        error: draftNumberError.message
      });
    }
    
    // Déchiffrer les données de l'original pour les re-crypter
    const decryptedOriginal = await decryptInvoiceDataAsync(invoice);

    // Créer un nouveau document basé sur l'original avec cryptage des données client
    const newInvoice = {
      user_id: userId,
      number: null, // Pas de numéro officiel pour les brouillons
      draft_number: draftNumberResult,
      type: invoice.type,
      client_name: decryptedOriginal.client_name ? await encryptDataAsync(decryptedOriginal.client_name) : null,
      client_address: decryptedOriginal.client_address ? await encryptDataAsync(decryptedOriginal.client_address) : null,
      client_email: decryptedOriginal.client_email ? await encryptDataAsync(decryptedOriginal.client_email) : null,
      client_phone: decryptedOriginal.client_phone ? await encryptDataAsync(decryptedOriginal.client_phone) : null,
      client_siret: decryptedOriginal.client_siret ? await encryptDataAsync(decryptedOriginal.client_siret) : null,
      client_tva: decryptedOriginal.client_tva ? await encryptDataAsync(decryptedOriginal.client_tva) : null,
      date_validite: invoice.date_validite,
      conditions_paiement: invoice.conditions_paiement,
      mode_paiement: invoice.mode_paiement,
      statut: 'brouillon',
      mentions_legales: invoice.mentions_legales,
      mentions_tva: invoice.mentions_tva,
      penalite_retard: invoice.penalite_retard,
      indemnite_recouvrement: invoice.indemnite_recouvrement,
      description: invoice.description,
      notes: invoice.notes,
      devis_origine_id: invoice.devis_origine_id,
      total_ht: invoice.total_ht,
      total_tva: invoice.total_tva,
      total_ttc: invoice.total_ttc
    };
    
    // Insérer le nouveau document
    const { data: createdInvoice, error: createError } = await supabase
      .from('invoices')
      .insert(newInvoice)
      .select()
      .single();
    
    if (createError) {
      logger.error('Erreur lors de la duplication du document:', createError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la duplication du document',
        error: createError.message
      });
    }
    
    // Dupliquer les items
    const newItems = invoice.invoice_items.map((item: InvoiceItem) => ({
      invoice_id: createdInvoice.id,
      description: item.description,
      quantite: item.quantite,
      unite: item.unite,
      prix_unitaire: item.prix_unitaire,
      taux_tva: item.taux_tva,
      montant_ht: item.montant_ht,
      montant_tva: item.montant_tva,
      montant_ttc: item.montant_ttc,
      ordre: item.ordre
    }));
    
    const { data: createdItems, error: itemsError } = await supabase
      .from('invoice_items')
      .insert(newItems)
      .select();
    
    if (itemsError) {
      logger.error('Erreur lors de la duplication des items:', itemsError);
      // Supprimer le document en cas d'erreur
      await supabase.from('invoices').delete().eq('id', createdInvoice.id);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la duplication des items',
        error: itemsError.message
      });
    }
    
    // Enregistrer l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: createdInvoice.id,
      user_id: userId,
      action: 'Duplication du document',
      details: invoiceId
    });
    
    // Invalider les caches pertinents (après la duplication)
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    // Déchiffrer les données avant de les retourner
    const decryptedInvoice = await decryptInvoiceDataAsync(createdInvoice);

    logger.info(`Document dupliqué avec succès, nouvel ID: ${createdInvoice.id}`);

    return res.status(201).json({
      success: true,
      message: 'Document dupliqué avec succès',
      data: {
        ...decryptedInvoice,
        invoice_items: createdItems
      }
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la duplication du document:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la duplication du document',
      error: err.message
    });
  }
};

// Convertir un devis en facture
export const convertToInvoice = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    const quoteId = req.params.id;
    
    // Vérifier si le devis existe et appartient à l'utilisateur
    const { data: quote, error: quoteError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', quoteId)
      .eq('user_id', userId)
      .eq('type', 'devis')
      .single();
    
    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        message: 'Devis non trouvé'
      });
    }
    
    // Vérifier que le devis est dans un état qui permet la conversion (accepté)
    if (quote.statut !== 'accepte') {
      res.status(400).json({
        success: false,
        message: 'Seuls les devis acceptés peuvent être convertis en facture'
      });
      return;
    }
    
    // Vérifier si le devis a déjà été converti en facture
    if (quote.statut === 'facture') {
      res.status(400).json({
        success: false,
        message: 'Ce devis a déjà été converti en facture'
      });
      return;
    }

    // Vérifier s'il existe déjà une facture basée sur ce devis
    const { data: existingInvoice, error: existingInvoiceError } = await supabase
      .from('invoices')
      .select('id')
      .eq('devis_origine_id', quoteId)
      .eq('type', 'facture')
      .maybeSingle();

    if (!existingInvoiceError && existingInvoice) {
      res.status(400).json({
        success: false,
        message: 'Ce devis a déjà été converti en facture'
      });
      return;
    }
    
    // Vérifier si l'utilisateur a un abonnement premium
    const { isPremium, invoiceLimit } = await getUserSubscriptionLimits(userId);
    
    // Vérifier les limites selon l'abonnement pour les factures
    if (!isPremium) {
      // Requête pour compter le nombre de factures
      const { data: invoiceCount, error: countError } = await supabase
        .from('invoices')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('type', 'facture');
        
      if (countError) {
        logger.error('Erreur lors du comptage des factures:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }
      
      if ((invoiceCount?.length || 0) >= invoiceLimit) {
        res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${invoiceLimit} factures pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          toastType: 'error',
          limitReached: true
        });
        return;
      }
    }
    
    // Générer un nouveau numéro pour la facture
    const { data: numberResult, error: numberError } = await supabase.rpc(
      'generate_invoice_number',
      { doc_type: 'facture', is_draft: true }
    );
    
    if (numberError) {
      logger.error('Erreur lors de la génération du numéro:', numberError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du numéro de facture',
        error: numberError.message
      });
    }
    
    // Déchiffrer les données du devis pour les re-crypter
    const decryptedQuote = await decryptInvoiceDataAsync(quote);

    // Créer une nouvelle facture basée sur le devis avec cryptage des données client
    const newInvoice = {
      user_id: userId,
      number: numberResult,
      type: 'facture',
      client_name: decryptedQuote.client_name ? await encryptDataAsync(decryptedQuote.client_name) : null,
      client_address: decryptedQuote.client_address ? await encryptDataAsync(decryptedQuote.client_address) : null,
      client_email: decryptedQuote.client_email ? await encryptDataAsync(decryptedQuote.client_email) : null,
      client_phone: decryptedQuote.client_phone ? await encryptDataAsync(decryptedQuote.client_phone) : null,
      client_siret: quote.client_siret,
      client_tva: quote.client_tva,
      conditions_paiement: quote.conditions_paiement,
      mode_paiement: quote.mode_paiement,
      statut: 'brouillon',
      mentions_legales: quote.mentions_legales,
      mentions_tva: quote.mentions_tva,
      penalite_retard: quote.penalite_retard,
      indemnite_recouvrement: quote.indemnite_recouvrement,
      description: quote.description,
      notes: quote.notes,
      devis_origine_id: quoteId,
      total_ht: quote.total_ht,
      total_tva: quote.total_tva,
      total_ttc: quote.total_ttc
    };
    
    // Insérer la nouvelle facture
    const { data: createdInvoice, error: createError } = await supabase
      .from('invoices')
      .insert(newInvoice)
      .select()
      .single();
    
    if (createError) {
      logger.error('Erreur lors de la création de la facture:', createError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la facture',
        error: createError.message
      });
    }
    
    // Dupliquer les items du devis pour la facture
    const newItems = quote.invoice_items.map((item: InvoiceItem) => ({
      invoice_id: createdInvoice.id,
      description: item.description,
      quantite: item.quantite,
      unite: item.unite,
      prix_unitaire: item.prix_unitaire,
      taux_tva: item.taux_tva,
      montant_ht: item.montant_ht,
      montant_tva: item.montant_tva,
      montant_ttc: item.montant_ttc,
      ordre: item.ordre
    }));
    
    const { error: itemsError } = await supabase
      .from('invoice_items')
      .insert(newItems);
    
    if (itemsError) {
      logger.error('Erreur lors de la création des items de facture:', itemsError);
      // Supprimer la facture en cas d'erreur
      await supabase.from('invoices').delete().eq('id', createdInvoice.id);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création des items de facture',
        error: itemsError.message
      });
    }
    
    // Mettre à jour le devis avec l'ID de la facture créée, sans changer le statut
    await supabase
      .from('invoices')
      .update({ 
        facture_origine_id: createdInvoice.id  // Ajouter seulement l'ID de la facture créée
      })
      .eq('id', quoteId);
    
    // Enregistrer l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: quoteId,
      user_id: userId,
      action: 'Conversion du devis en facture',
      details: createdInvoice.id
    });
    
    await supabase.from('invoice_history').insert({
      invoice_id: createdInvoice.id,
      user_id: userId,
      action: 'Création du document',
      details: `${createdInvoice.type} -> ${quoteId}`
    });
    
    // Envoyer une notification au jobbeur
    const { error: notifError } = await supabase
      .from('user_notifications')
      .insert([{
        user_id: userId,
        type: 'invoice',
        title: 'Devis converti en facture',
        content: `Votre devis ${quote.number} a été converti en facture ${createdInvoice.number}.`,
        link: `/dashboard/facturation`,
        is_read: false,
        is_archived: false
      }]);
    
    if (notifError) {
      logger.error('Erreur lors de l\'envoi de la notification:', notifError);
    }
    
    // Si le devis a un client associé et que ce client existe en tant qu'utilisateur
    if (quote.client_email) {
      // Chercher si le client existe en tant qu'utilisateur en utilisant le hash d'email
      const clientEmailHash = hashEmail(quote.client_email);
      const { data: clientUser, error: clientUserError } = await supabase
        .from('users')
        .select('id')
        .eq('email_hash', clientEmailHash)
        .maybeSingle();

      // Si le client existe, lui envoyer également une notification
      if (clientUser && !clientUserError) {
        const { error: notifClientError } = await supabase
          .from('user_notifications')
          .insert([{
            user_id: clientUser.id,
            type: 'invoice',
            title: 'Devis converti en facture',
            content: `Le devis ${quote.number} a été converti en facture ${createdInvoice.number}.`,
            link: `/dashboard/facturation`,
            is_read: false,
            is_archived: false
          }]);
          
        if (notifClientError) {
          logger.error('Erreur lors de l\'envoi de la notification au client:', notifClientError);
        }
      }
    }
    
    // Invalider les caches pertinents
    const quoteKey = `${INVOICE_CACHE_PREFIX}${userId}:${quoteId}`;
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    
    await redis.del(quoteKey);
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    // Déchiffrer les données avant de les retourner
    const decryptedInvoice = await decryptInvoiceDataAsync(createdInvoice);

    return res.status(201).json({
      success: true,
      message: 'Devis converti en facture avec succès',
      data: decryptedInvoice
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la conversion du devis:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la conversion du devis',
      error: err.message
    });
  }
};

// Créer un avoir à partir d'une facture
export const createCreditNote = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    const invoiceId = req.params.id;
 
    // Vérifier si l'utilisateur a rempli ses informations d'entreprise
      const { data: companySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (companyError || !companySettings || !companySettings.nom) {
      res.status(403).json({
        success: false,
        message: 'Vous devez d\'abord compléter les informations de votre entreprise avant de créer un document. Vous pouvez le faire via le bouton "Paramètres" en haut de cette page.',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si la facture existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .eq('type', 'facture')
      .single();
    
    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }
    
    // Vérifier que la facture n'est pas en brouillon ou déjà annulée
    if (invoice.statut === 'brouillon' || invoice.statut === 'annule') {
      res.status(400).json({
        success: false,
        message: 'Impossible de créer un avoir pour une facture en brouillon ou déjà annulée'
      });
      return;
    }
    
    // Vérifier si l'utilisateur a un abonnement premium et ses limites
    const { isPremium, invoiceLimit } = await getUserSubscriptionLimits(userId);
    
    // Vérifier les limites selon l'abonnement pour les avoirs (considérés comme des factures)
    if (!isPremium) {
      // Requête pour compter le nombre de factures/avoirs
      const { data: invoiceCount, error: countError } = await supabase
        .from('invoices')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .in('type', ['facture', 'avoir']);
        
      if (countError) {
        logger.error('Erreur lors du comptage des factures/avoirs:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }
      
      if ((invoiceCount?.length || 0) >= invoiceLimit) {
        res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${invoiceLimit} factures/avoirs pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          toastType: 'error',
          limitReached: true
        });
        return;
      }
    }
    
    // Générer un nouveau numéro pour l'avoir
    const { data: numberResult, error: numberError } = await supabase.rpc(
      'generate_invoice_number',
      { doc_type: 'avoir', is_draft: true }
    );
    
    if (numberError) {
      logger.error('Erreur lors de la génération du numéro:', numberError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du numéro d\'avoir',
        error: numberError.message
      });
    }
    
    // Créer un nouvel avoir basé sur la facture
    const newCreditNote = {
      user_id: userId,
      number: numberResult,
      type: 'avoir',
      client_name: invoice.client_name,
      client_address: invoice.client_address,
      client_email: invoice.client_email,
      client_phone: invoice.client_phone,
      client_siret: invoice.client_siret,
      client_tva: invoice.client_tva,
      statut: 'brouillon',
      mentions_legales: invoice.mentions_legales,
      mentions_tva: invoice.mentions_tva,
      description: `Avoir de la facture ${invoice.number}`,
      notes: req.body.notes || `Annulation de la facture ${invoice.number}`,
      facture_origine_id: invoiceId,
      total_ht: invoice.total_ht,
      total_tva: invoice.total_tva,
      total_ttc: invoice.total_ttc
    };
    
    // Insérer le nouvel avoir
    const { data: createdCreditNote, error: createError } = await supabase
      .from('invoices')
      .insert(newCreditNote)
      .select()
      .single();
    
    if (createError) {
      logger.error('Erreur lors de la création de l\'avoir:', createError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de l\'avoir',
        error: createError.message
      });
    }
    
    // Dupliquer les items de la facture pour l'avoir
    const newItems = invoice.invoice_items.map((item: InvoiceItem) => ({
      invoice_id: createdCreditNote.id,
      description: item.description,
      quantite: item.quantite,
      unite: item.unite,
      prix_unitaire: item.prix_unitaire,
      taux_tva: item.taux_tva,
      montant_ht: item.montant_ht,
      montant_tva: item.montant_tva,
      montant_ttc: item.montant_ttc,
      ordre: item.ordre
    }));
    
    const { error: itemsError } = await supabase
      .from('invoice_items')
      .insert(newItems);
    
    if (itemsError) {
      logger.error('Erreur lors de la création des items d\'avoir:', itemsError);
      // Supprimer l'avoir en cas d'erreur
      await supabase.from('invoices').delete().eq('id', createdCreditNote.id);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création des items d\'avoir',
        error: itemsError.message
      });
    }
    
    // Mettre à jour le statut de la facture
    await supabase
      .from('invoices')
      .update({ statut: 'annule' })
      .eq('id', invoiceId);
    
    // Enregistrer l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: invoiceId,
      user_id: userId,
      action: 'Annulation de la facture',
      details: createdCreditNote.id
    });
    
    await supabase.from('invoice_history').insert({
      invoice_id: createdCreditNote.id,
      user_id: userId,
      action: 'Création du document',
      details: `${createdCreditNote.type} -> ${invoiceId}`
    });
    
    // Invalider les caches pertinents
    const invoiceKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    
    await redis.del(invoiceKey);
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    // Déchiffrer les données avant de les retourner
    const decryptedCreditNote = await decryptInvoiceDataAsync(createdCreditNote);

    return res.status(201).json({
      success: true,
      message: 'Avoir créé avec succès',
      data: decryptedCreditNote
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la création de l\'avoir:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création de l\'avoir',
      error: err.message
    });
  }
};

// Obtenir l'historique d'un document
export const getInvoiceHistory = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    const invoiceId = req.params.id;
    
    // Vérifier si la facture/devis existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('id')
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();
    
    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }
    
    // Récupérer l'historique
    const { data: history, error: historyError } = await supabase
      .from('invoice_history')
      .select(`
        *,
        users:user_id (id, email)
      `)
      .eq('invoice_id', invoiceId)
      .order('created_at', { ascending: false });
    
    if (historyError) {
      logger.error('Erreur lors de la récupération de l\'historique:', historyError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique',
        error: historyError.message
      });
    }
    
    res.status(200).json({
      success: true,
      data: history
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération de l\'historique:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'historique',
      error: err.message
    });
  }
};

export const acceptQuote = async (req: Request, res: Response) => {
  try {
    const quoteId = req.params.id;
    const { clientName, clientAddress, clientEmail, clientPhone, signature, dataConsent } = req.body;
    const userId = req.user?.userId; // Utilisateur connecté

    if (!clientName || !clientEmail || !signature) {
      res.status(400).json({
        success: false,
        message: 'Informations manquantes pour l\'acceptation du devis'
      });
      return;
    }

    // Vérifier le consentement au partage des données
    if (!dataConsent) {
      res.status(400).json({
        success: false,
        message: 'Le consentement au partage des données est requis pour accepter le devis'
      });
      return;
    }

    // Vérifier si le devis existe
    const { data: quote, error: quoteError } = await supabase
      .from('invoices')
      .select(`
        *,
        user_id
      `)
      .eq('id', quoteId)
      .eq('type', 'devis')
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        message: 'Devis non trouvé'
      });
    }

    // Vérifier que l'utilisateur connecté est bien le destinataire du devis (par email)
    if (userId && quote.client_email) {
      try {
        // Récupérer l'email de l'utilisateur connecté
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();

        if (userError || !userData) {
          return res.status(403).json({
            success: false,
            message: 'Erreur lors de la vérification des permissions'
          });
        }

        const decryptedUserEmail = await decryptDataAsync(userData.email);
        const decryptedClientEmail = await decryptDataAsync(quote.client_email);

        if (decryptedUserEmail !== decryptedClientEmail) {
          return res.status(403).json({
            success: false,
            message: 'Vous n\'êtes pas autorisé à accepter ce devis'
          });
        }
      } catch (emailError) {
        logger.error('Erreur lors de la vérification de l\'email:', emailError);
        return res.status(403).json({
          success: false,
          message: 'Erreur lors de la vérification des permissions'
        });
      }
    }

    // Vérifier que le devis n'est pas déjà accepté
    if (quote.statut === 'accepte' || quote.statut === 'facture') {
      res.status(400).json({
        success: false,
        message: 'Ce devis a déjà été accepté'
      });
      return;
    }

    // Préparer les données client à crypter
    const clientDataToUpdate = {
      statut: 'accepte',
      client_name: clientName,
      client_address: clientAddress || quote.client_address,
      client_email: clientEmail,
      client_phone: clientPhone || quote.client_phone
    };

    // Crypter les données client avant la mise à jour
    const encryptedClientData = await encryptInvoiceDataAsync(clientDataToUpdate);

    // Mettre à jour le devis avec les informations du client cryptées et le statut
    const { error: updateError } = await supabase
      .from('invoices')
      .update(encryptedClientData)
      .eq('id', quoteId);
    
    if (updateError) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du devis',
        error: updateError.message
      });
    }

    // Enregistrer la signature dans l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: quoteId,
      user_id: quote.user_id,
      action: 'Devis accepté par le client',
      details: `Signé par: ${clientName} (Consentement au partage des données: Accepté)`
    });

    // Envoyer une notification au jobbeur (créateur du devis)
    const { error: notifJobbeurError } = await supabase
      .from('user_notifications')
      .insert([{
        user_id: quote.user_id,
        type: 'invoice',
        title: 'Devis accepté',
        content: `Votre devis ${quote.number} a été accepté par ${clientName}.`,
        link: `/dashboard/facturation`,
        is_read: false,
        is_archived: false
      }]);

    if (notifJobbeurError) {
      logger.error('Erreur lors de l\'envoi de la notification au jobbeur:', notifJobbeurError);
    }

    // Récupérer l'ID de l'utilisateur (créateur du devis)
    const creatorUserId = quote.user_id;

    // Chercher si le client existe déjà en tant qu'utilisateur en utilisant le hash d'email
    const clientEmailHashToSearch = hashEmail(clientEmail);
    const { data: clientUser, error: clientUserError } = await supabase
      .from('users')
      .select('id')
      .eq('email_hash', clientEmailHashToSearch)
      .maybeSingle();

    // Si le client existe en tant qu'utilisateur, lui envoyer une notification également
    if (clientUser && !clientUserError) {
      const { error: notifClientError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: clientUser.id,
          type: 'invoice',
          title: 'Devis signé',
          content: `Vous avez accepté le devis ${quote.number}.`,
          link: `/dashboard/facturation`,
          is_read: false,
          is_archived: false
        }]);

      if (notifClientError) {
        logger.error('Erreur lors de l\'envoi de la notification au client:', notifClientError);
      }
    }

    // Récupérer les informations de l'utilisateur pour l'email
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', quote.user_id)
      .single();

    // Récupérer séparément les informations du profil de l'utilisateur
    const { data: userProfil, error: profilError } = await supabase
      .from('user_profil')
      .select('prenom, nom')
      .eq('user_id', quote.user_id)
      .maybeSingle();

    // Déchiffrer les données de l'utilisateur et du profil
    const decryptedUserData = userData ? await decryptUserDataAsync(userData) : null;
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération des données utilisateur:', userError);
      // Continuer quand même, l'échec de l'envoi d'email ne devrait pas bloquer l'acceptation
    } else {
      // Obtenir le numéro et la description du devis pour les emails
      const quoteNumber = quote.number || 'N/A';
      const quoteDescription = quote.description || 'Prestation de services';
      const userPrenom = decryptedUserProfil?.prenom || 'Utilisateur';
      const userNom = decryptedUserProfil?.nom || '';
      
      // Envoyer un email de confirmation au client
      await sendInvoiceEmail(clientEmail, {
        subject: `Confirmation - Devis ${quoteNumber} accepté`,
        html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <style>
                body {
                  font-family: Arial, sans-serif;
                  color: #333;
                  line-height: 1.6;
                  margin: 0;
                  padding: 0;
                }
                .container {
                  max-width: 600px;
                  margin: 0 auto;
                  padding: 20px;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                }
                .header {
                  background-color: #FF7A35;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px 8px 0 0;
                  margin: -20px -20px 20px;
                }
                .footer {
                  background-color: #f9fafb;
                  padding: 15px 20px;
                  border-top: 1px solid #e5e7eb;
                  margin: 20px -20px -20px;
                  border-radius: 0 0 8px 8px;
                  font-size: 12px;
                  color: #6b7280;
                }
                h1 {
                  margin: 0;
                  font-size: 24px;
                }
                .success-box {
                  background-color: #DCFCE7;
                  border-left: 4px solid #15803D;
                  padding: 15px;
                  margin: 20px 0;
                }
                .next-steps {
                  background-color: #FEF3C7;
                  border-left: 4px solid #92400E;
                  padding: 15px;
                  margin: 20px 0;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>Confirmation de signature</h1>
                </div>
                
                <p>Bonjour ${clientName},</p>
                
                <div class="success-box">
                  <p><strong>Félicitations !</strong> Vous avez accepté et signé le devis <strong>électroniquement</strong> ${quoteNumber} de ${userPrenom} ${userNom}.</p>
                </div>
                
                <p>Détails du devis :</p>
                <ul>
                  <li><strong>Numéro :</strong> ${quoteNumber}</li>
                  <li><strong>Description :</strong> ${quoteDescription}</li>
                  <li><strong>Montant total :</strong> ${quote.total_ttc.toFixed(2)} €</li>
                  <li><strong>Date d'acceptation :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                </ul>
                
                <div style="background-color: #FEE2E2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0;">
                  <p><strong>Information importante :</strong></p>
                  <p>Veuillez noter que cette confirmation électronique n'a pas de valeur juridique devant un tribunal en cas de litige. Pour une validité juridique complète, il est recommandé de signer le devis manuscritement avec la mention "bon pour accord", votre prénom et nom.</p>
                  <p>Vous pouvez demander à ${userPrenom} ${userNom} de vous faire signer une version papier du devis lors de votre première rencontre.</p>
                </div>
                
                <div class="next-steps">
                  <p><strong>Prochaines étapes :</strong></p>
                  <p>${userPrenom} ${userNom} va maintenant pouvoir commencer les travaux et vous contactera prochainement pour organiser la prestation.</p>
                </div>
                
                <p>Nous vous remercions pour votre confiance et votre collaboration.</p>
                
                <p>Cordialement,</p>
                <p><strong>L'équipe JobPartiel</strong></p>
                
                <div class="footer">
                  <p>Ce message est généré automatiquement par <strong>JobPartiel.fr</strong></p>
                  <p>Le jobbing malin pour arrondir vos fins de mois</p>
                </div>
              </div>
            </body>
            </html>
          `,
          attachments: []
        });
      
      // Envoyer une notification à l'utilisateur (jobbeur)
      if (decryptedUserData && decryptedUserData.email) {
        await sendInvoiceEmail(decryptedUserData.email, {
          subject: `Bonne nouvelle ! Votre devis ${quoteNumber} a été accepté`,
          html: `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    color: #333;
                    line-height: 1.6;
                    margin: 0;
                    padding: 0;
                  }
                  .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                  }
                  .header {
                    background-color: #FF7A35;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px 8px 0 0;
                    margin: -20px -20px 20px;
                  }
                  .footer {
                    background-color: #f9fafb;
                    padding: 15px 20px;
                    border-top: 1px solid #e5e7eb;
                    margin: 20px -20px -20px;
                    border-radius: 0 0 8px 8px;
                    font-size: 12px;
                    color: #6b7280;
                  }
                  h1 {
                    margin: 0;
                    font-size: 24px;
                  }
                  .success-box {
                    background-color: #DCFCE7;
                    border-left: 4px solid #15803D;
                    padding: 15px;
                    margin: 20px 0;
                  }
                  .tips-box {
                    background-color: #DBEAFE;
                    border-left: 4px solid #1E40AF;
                    padding: 15px;
                    margin: 20px 0;
                  }
                  .next-steps-list {
                    margin-left: 20px;
                  }
                  .next-steps-list li {
                    margin-bottom: 10px;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="header">
                    <h1>Devis accepté !</h1>
                  </div>
                  
                  <p>Bonjour ${userPrenom},</p>
                  
                  <div class="success-box">
                    <p><strong>Félicitations !</strong> Votre client ${clientName} vient d'accepter et de signer votre devis ${quoteNumber}.</p>
                  </div>
                  
                  <p>Détails du devis :</p>
                  <ul>
                    <li><strong>Numéro :</strong> ${quoteNumber}</li>
                    <li><strong>Client :</strong> ${clientName}</li>
                    <li><strong>Email du client :</strong> ${clientEmail}</li>
                    <li><strong>Téléphone du client :</strong> ${clientPhone || 'Non renseigné'}</li>
                    <li><strong>Montant total :</strong> ${quote.total_ttc.toFixed(2)} €</li>
                    <li><strong>Date d'acceptation :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                  </ul>

                  <div style="background-color: #FEE2E2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0;">
                    <p><strong>Information importante :</strong></p>
                    <p>Veuillez noter que cette confirmation électronique n'a pas de valeur juridique devant un tribunal en cas de litige. Pour une validité juridique complète, il est recommandé de faire signer le devis manuscritement avec la mention "bon pour accord", incluant le prénom et nom du client.</p>
                    <p>Vous pouvez demander à ${clientName} de vous faire signer une version papier du devis lors de votre première rencontre.</p>
                  </div>

                  
                  <div class="tips-box">
                    <p><strong>Conseils pour réussir votre mission :</strong></p>
                    <ol class="next-steps-list">
                      <li>Contactez rapidement votre client pour confirmer la mission et organiser les détails pratiques</li>
                      <li>Préparez bien votre intervention en amont</li>
                      <li>Soyez ponctuel et professionnel</li>
                      <li>Communiquez clairement pendant toute la durée de la prestation</li>
                      <li>Une fois le travail terminé, n'hésitez pas à convertir ce devis en facture depuis votre espace JobPartiel</li>
                    </ol>
                  </div>
                  
                  <p>Vous pouvez dès maintenant <a href="https://jobpartiel.fr/dashboard/facturation">convertir ce devis en facture</a> et démarrer la prestation.</p>
                  
                  <p>Nous vous souhaitons une excellente collaboration avec votre client !</p>
                  
                  <p>Cordialement,</p>
                  <p><strong>L'équipe JobPartiel</strong></p>
                  
                  <div class="footer">
                    <p>Ce message est généré automatiquement par <strong>JobPartiel.fr</strong></p>
                    <p>Le jobbing malin pour arrondir vos fins de mois</p>
                  </div>
                </div>
              </body>
              </html>
            `,
            attachments: []
          });

          // Invalider le cache pour le créateur du devis
          const quoteKey = `${INVOICE_CACHE_PREFIX}${creatorUserId}:${quoteId}`;
          const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${creatorUserId}:*`;

          await redis.del(quoteKey);
          const keys = await redis.keys(listCachePattern);
          if (keys.length > 0) {
            await redis.del(...keys);
          }

          // Invalider le cache pour le destinataire du devis (utilisateur connecté)
          if (userId && userId !== creatorUserId) {
            const clientQuoteKey = `${INVOICE_CACHE_PREFIX}${userId}:${quoteId}`;
            const clientListCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;

            await redis.del(clientQuoteKey);
            const clientKeys = await redis.keys(clientListCachePattern);
            if (clientKeys.length > 0) {
              await redis.del(...clientKeys);
            }
          }
        }
    }

    res.status(200).json({
      success: true,
      message: 'Devis accepté avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de l\'acceptation du devis:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'acceptation du devis'
    });
  }
};

// Refuser un devis
export const rejectQuote = async (req: Request, res: Response) => {
  try {
    const { quoteId } = req.params;
    const { reason, message } = req.body;
    const userId = req.user?.userId; // Utilisateur connecté

    if (!quoteId || !reason) {
      res.status(400).json({
        success: false,
        message: 'Informations manquantes pour le refus du devis'
      });
      return;
    }

    // Vérifier si le devis existe
    const { data: quote, error: quoteError } = await supabase
      .from('invoices')
      .select(`
        *,
        user_id
      `)
      .eq('id', quoteId)
      .eq('type', 'devis')
      .single();

    if (quoteError || !quote) {
      return res.status(404).json({
        success: false,
        message: 'Devis non trouvé'
      });
    }

    // Vérifier que l'utilisateur connecté est bien le destinataire du devis (par email)
    if (userId && quote.client_email) {
      try {
        // Récupérer l'email de l'utilisateur connecté
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email')
          .eq('id', userId)
          .single();

        if (userError || !userData) {
          return res.status(403).json({
            success: false,
            message: 'Erreur lors de la vérification des permissions'
          });
        }

        const decryptedUserEmail = await decryptDataAsync(userData.email);
        const decryptedClientEmail = await decryptDataAsync(quote.client_email);

        if (decryptedUserEmail !== decryptedClientEmail) {
          return res.status(403).json({
            success: false,
            message: 'Vous n\'êtes pas autorisé à refuser ce devis'
          });
        }
      } catch (emailError) {
        logger.error('Erreur lors de la vérification de l\'email:', emailError);
        return res.status(403).json({
          success: false,
          message: 'Erreur lors de la vérification des permissions'
        });
      }
    }

    // Vérifier que le devis n'est pas déjà accepté ou refusé
    if (quote.statut === 'accepte' || quote.statut === 'refuse' || quote.statut === 'facture') {
      res.status(400).json({
        success: false,
        message: 'Ce devis a déjà été traité'
      });
      return;
    }

    // Récupérer l'ID de l'utilisateur (créateur du devis)
    const creatorUserId = quote.user_id;

    // Mettre à jour le devis avec le statut refusé
    const { error: updateError } = await supabase
      .from('invoices')
      .update({ 
        statut: 'refuse'
      })
      .eq('id', quoteId);
    
    if (updateError) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du devis',
        error: updateError.message
      });
    }

    // Récupérer et décrypter les données du client
    const decryptedQuoteData = await decryptInvoiceDataAsync(quote);
    const clientName = decryptedQuoteData.client_name || 'Client';
    const clientEmail = decryptedQuoteData.client_email || '';

    // Enregistrer la raison du refus dans l'historique
    await supabase.from('invoice_history').insert({
      invoice_id: quoteId,
      user_id: quote.user_id,
      action: 'Devis refusé par le client',
      details: `Motif: ${reason}${message ? ` - Message: ${message}` : ''}`
    });

    // Envoyer une notification au jobbeur (créateur du devis)
    const { error: notifJobbeurError } = await supabase
      .from('user_notifications')
      .insert([{
        user_id: quote.user_id,
        type: 'invoice',
        title: 'Devis refusé',
        content: `Votre devis ${quote.number} a été refusé. Motif: ${reason}.`,
        link: `/dashboard/facturation`,
        is_read: false,
        is_archived: false
      }]);

    if (notifJobbeurError) {
      logger.error('Erreur lors de l\'envoi de la notification au jobbeur:', notifJobbeurError);
    }

    // Chercher si le client existe déjà en tant qu'utilisateur en utilisant le hash d'email
    const clientEmailHashForRefusal = hashEmail(clientEmail);
    const { data: clientUser, error: clientUserError } = await supabase
      .from('users')
      .select('id')
      .eq('email_hash', clientEmailHashForRefusal)
      .maybeSingle();

    // Si le client existe en tant qu'utilisateur, lui envoyer une notification également
    if (clientUser && !clientUserError) {
      const { error: notifClientError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: clientUser.id,
          type: 'invoice',
          title: 'Devis refusé',
          content: `Vous avez refusé le devis ${quote.number}.`,
          link: `/dashboard/facturation`,
          is_read: false,
          is_archived: false
        }]);

      if (notifClientError) {
        logger.error('Erreur lors de l\'envoi de la notification au client:', notifClientError);
      }
    }

    // Récupérer les informations de l'utilisateur pour l'email
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', quote.user_id)
      .single();

    // Récupérer séparément les informations du profil de l'utilisateur
    const { data: userProfil, error: profilError } = await supabase
      .from('user_profil')
      .select('prenom, nom')
      .eq('user_id', quote.user_id)
      .maybeSingle();

    // Déchiffrer les données de l'utilisateur et du profil
    const decryptedUserDataReject = userData ? await decryptUserDataAsync(userData) : null;
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération des données utilisateur:', userError);
      // Continuer quand même, l'échec de l'envoi d'email ne devrait pas bloquer le refus
    } else {
      // Obtenir le numéro et la description du devis pour les emails
      const quoteNumber = quote.number || 'N/A';
      const quoteDescription = quote.description || 'Prestation de services';
      const userPrenom = decryptedUserProfil?.prenom || 'Utilisateur';
      const userNom = decryptedUserProfil?.nom || '';
      
      // Envoyer un email de confirmation au client
      if (clientEmail) {
        await sendInvoiceEmail(clientEmail, {
          subject: `Confirmation - Devis ${quoteNumber} refusé`,
          html: `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    color: #333;
                    line-height: 1.6;
                    margin: 0;
                    padding: 0;
                  }
                  .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                  }
                  .header {
                    background-color: #FF7A35;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px 8px 0 0;
                    margin: -20px -20px 20px;
                  }
                  .footer {
                    background-color: #f9fafb;
                    padding: 15px 20px;
                    border-top: 1px solid #e5e7eb;
                    margin: 20px -20px -20px;
                    border-radius: 0 0 8px 8px;
                    font-size: 12px;
                    color: #6b7280;
                  }
                  h1 {
                    margin: 0;
                    font-size: 24px;
                  }
                  .info-box {
                    background-color: #FEE2E2;
                    border-left: 4px solid #B91C1C;
                    padding: 15px;
                    margin: 20px 0;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="header">
                    <h1>Confirmation de refus</h1>
                  </div>
                  
                  <p>Bonjour,</p>
                  
                  <div class="info-box">
                    <p>Vous avez refusé le devis ${quoteNumber} de ${userPrenom} ${userNom}.</p>
                    <p><strong>Motif du refus :</strong> ${reason}</p>
                    ${message ? `<p><strong>Message :</strong> ${message}</p>` : ''}
                  </div>
                  
                  <p>Détails du devis :</p>
                  <ul>
                    <li><strong>Numéro :</strong> ${quoteNumber}</li>
                    <li><strong>Description :</strong> ${quoteDescription}</li>
                    <li><strong>Montant total :</strong> ${quote.total_ttc.toFixed(2)} €</li>
                    <li><strong>Date de refus :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                  </ul>
                  
                  <p>Si vous changez d'avis ou souhaitez discuter des conditions, n'hésitez pas à contacter directement le prestataire.</p>
                  
                  <p>Cordialement,</p>
                  <p><strong>L'équipe JobPartiel</strong></p>
                  
                  <div class="footer">
                    <p>Ce message est généré automatiquement par <strong>JobPartiel.fr</strong></p>
                    <p>Le jobbing malin pour arrondir vos fins de mois</p>
                  </div>
                </div>
              </body>
              </html>
            `,
            attachments: []
          });
      }
      
      // Envoyer une notification à l'utilisateur (jobbeur)
      if (decryptedUserDataReject && decryptedUserDataReject.email) {
        await sendInvoiceEmail(decryptedUserDataReject.email, {
          subject: `Information - Votre devis ${quoteNumber} a été refusé`,
          html: `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    color: #333;
                    line-height: 1.6;
                    margin: 0;
                    padding: 0;
                  }
                  .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                  }
                  .header {
                    background-color: #FF7A35;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px 8px 0 0;
                    margin: -20px -20px 20px;
                  }
                  .footer {
                    background-color: #f9fafb;
                    padding: 15px 20px;
                    border-top: 1px solid #e5e7eb;
                    margin: 20px -20px -20px;
                    border-radius: 0 0 8px 8px;
                    font-size: 12px;
                    color: #6b7280;
                  }
                  h1 {
                    margin: 0;
                    font-size: 24px;
                  }
                  .info-box {
                    background-color: #FEE2E2;
                    border-left: 4px solid #B91C1C;
                    padding: 15px;
                    margin: 20px 0;
                  }
                  .tips-box {
                    background-color: #DBEAFE;
                    border-left: 4px solid #1E40AF;
                    padding: 15px;
                    margin: 20px 0;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="header">
                    <h1>Devis refusé</h1>
                  </div>
                  
                  <p>Bonjour ${userPrenom},</p>
                  
                  <div class="info-box">
                    <p>Votre devis ${quoteNumber} a été refusé par le client.</p>
                    <p><strong>Motif du refus :</strong> ${reason}</p>
                    ${message ? `<p><strong>Message du client :</strong> ${message}</p>` : ''}
                  </div>
                  
                  <p>Détails du devis :</p>
                  <ul>
                    <li><strong>Numéro :</strong> ${quoteNumber}</li>
                    <li><strong>Client :</strong> ${clientName}</li>
                    <li><strong>Montant total :</strong> ${quote.total_ttc.toFixed(2)} €</li>
                    <li><strong>Date de refus :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                  </ul>
                  
                  <div class="tips-box">
                    <p><strong>Conseils :</strong></p>
                    <ul>
                      <li>Ne vous découragez pas ! C'est une opportunité d'améliorer votre offre</li>
                      <li>Si le motif est lié au prix, vous pouvez proposer un devis révisé</li>
                      <li>Contactez le client pour comprendre plus en détail ses besoins</li>
                      <li>Utilisez ce retour pour ajuster vos futures propositions</li>
                    </ul>
                  </div>
                  
                  <p>Nous restons à votre disposition pour vous aider à développer votre activité sur JobPartiel.</p>
                  
                  <p>Cordialement,</p>
                  <p><strong>L'équipe JobPartiel</strong></p>
                  
                  <div class="footer">
                    <p>Ce message est généré automatiquement par <strong>JobPartiel.fr</strong></p>
                    <p>Le jobbing malin pour arrondir vos fins de mois</p>
                  </div>
                </div>
              </body>
              </html>
            `,
            attachments: []
          });

          // Invalider le cache pour le créateur du devis
          const quoteKey = `${INVOICE_CACHE_PREFIX}${creatorUserId}:${quoteId}`;
          const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${creatorUserId}:*`;

          await redis.del(quoteKey);
          const keys = await redis.keys(listCachePattern);
          if (keys.length > 0) {
            await redis.del(...keys);
          }

          // Invalider le cache pour le destinataire du devis (utilisateur connecté)
          if (userId && userId !== creatorUserId) {
            const clientQuoteKey = `${INVOICE_CACHE_PREFIX}${userId}:${quoteId}`;
            const clientListCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;

            await redis.del(clientQuoteKey);
            const clientKeys = await redis.keys(clientListCachePattern);
            if (clientKeys.length > 0) {
              await redis.del(...clientKeys);
            }
          }
        }
    }

    res.status(200).json({
      success: true,
      message: 'Devis refusé avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors du refus du devis:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors du refus du devis'
    });
  }
};

// Récupérer les devis reçus (par client_id)
export const getReceivedQuotes = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }

    // Créer une clé de cache unique
    const cacheKey = `${INVOICE_LIST_CACHE_PREFIX}${userId}:getReceivedQuotes`;
    const cachedQuotes = await redis.get(cacheKey);
    if (cachedQuotes) {
      res.status(200).json({
        success: true,
        data: JSON.parse(cachedQuotes)
      });
      return;
    }
    
    // Récupérer l'email de l'utilisateur connecté et créer son hash
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération de l\'utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur'
      });
    }

    // Déchiffrer l'email de l'utilisateur et créer son hash
    const decryptedUserEmail = await decryptDataAsync(userData.email);
    const userEmailHash = hashEmail(decryptedUserEmail);

    // Récupérer tous les devis où l'email client correspond à l'email de l'utilisateur connecté
    // et où le statut n'est pas 'brouillon'
    const { data: allQuotes, error: quotesError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('type', 'devis')
      .neq('statut', 'brouillon');

    if (quotesError) {
      logger.error('Erreur lors de la récupération des devis:', quotesError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des devis reçus'
      });
    }

    // Filtrer les devis où l'email client correspond à l'email de l'utilisateur
    const receivedQuotes = [];
    for (const quote of allQuotes || []) {
      if (!quote.client_email) continue;
      try {
        const decryptedClientEmail = await decryptDataAsync(quote.client_email);
        const clientEmailHash = hashEmail(decryptedClientEmail);
        if (clientEmailHash === userEmailHash) {
          receivedQuotes.push(quote);
        }
      } catch (error) {
        // Ignorer les erreurs de déchiffrement
        continue;
      }
    }

    // Déchiffrer les données des devis reçus et enrichir avec les informations de l'émetteur
    const enrichedQuotes = [];
    for (const quote of receivedQuotes) {
      const decryptedQuote = await decryptInvoiceDataAsync(quote);

      // Récupérer les informations de l'émetteur
      const { data: senderProfil, error: senderProfilError } = await supabase
          .from('user_profil')
          .select('prenom, nom')
          .eq('user_id', quote.user_id)
        .maybeSingle();

      const { data: senderCompany, error: senderCompanyError } = await supabase
          .from('invoices_company_settings')
          .select('*')
          .eq('user_id', quote.user_id)
        .maybeSingle();

      // Déchiffrer les données de l'émetteur
      const decryptedSenderProfil = senderProfil ? await decryptProfilDataAsync(senderProfil) : null;
      const decryptedSenderCompany = senderCompany ? await decryptCompanyDataAsync(senderCompany) : null;

      // Ajouter les informations de l'émetteur au devis
      decryptedQuote.sender_info = {
        prenom: decryptedSenderProfil?.prenom || '',
        nom: decryptedSenderProfil?.nom || '',
        entreprise: decryptedSenderCompany?.nom || '',
        adresse: decryptedSenderCompany?.adresse || '',
        code_postal: decryptedSenderCompany?.code_postal || '',
        ville: decryptedSenderCompany?.ville || '',
        telephone: decryptedSenderCompany?.telephone || '',
        email: decryptedSenderCompany?.email || '',
        site_web: decryptedSenderCompany?.site_web || '',
        siret: decryptedSenderCompany?.siret || '',
        tva: decryptedSenderCompany?.tva || '',
        forme_juridique: decryptedSenderCompany?.forme_juridique || '',
        code_ape: decryptedSenderCompany?.code_ape || '',
        rcs: decryptedSenderCompany?.rcs || '',
        capital: decryptedSenderCompany?.capital || '',
        mention_pied_page: decryptedSenderCompany?.mention_pied_page || '',
        iban: decryptedSenderCompany?.iban || '',
        bic: decryptedSenderCompany?.bic || '',
        banque: decryptedSenderCompany?.banque || ''
      };

      enrichedQuotes.push(decryptedQuote);
    }

    // Mettre les devis enrichis en cache
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(enrichedQuotes));

    return res.json({
      success: true,
      data: enrichedQuotes
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération des devis reçus:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des devis reçus'
    });
  }
};

// Récupérer les factures reçues (par client_id)
export const getReceivedInvoices = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }

    // Créer une clé de cache unique
    const cacheKey = `${INVOICE_LIST_CACHE_PREFIX}${userId}:getReceivedInvoices`;
    const cachedInvoices = await redis.get(cacheKey);
    if (cachedInvoices) {
      res.status(200).json({
        success: true,
        data: JSON.parse(cachedInvoices)
      });
      return;
    }

    // Récupérer l'email de l'utilisateur connecté et créer son hash
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération de l\'utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur'
      });
    }

    // Déchiffrer l'email de l'utilisateur et créer son hash
    const decryptedUserEmail = await decryptDataAsync(userData.email);
    const userEmailHash = hashEmail(decryptedUserEmail);

    // Récupérer toutes les factures où l'email client correspond à l'email de l'utilisateur connecté
    // et où le statut n'est pas 'brouillon'
    const { data: allInvoices, error: invoicesError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('type', 'facture')
      .neq('statut', 'brouillon');

    if (invoicesError) {
      logger.error('Erreur lors de la récupération des factures:', invoicesError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des factures reçues'
      });
    }

    // Filtrer les factures où l'email client correspond à l'email de l'utilisateur
    const receivedInvoices = [];
    for (const invoice of allInvoices || []) {
      if (!invoice.client_email) continue;
      try {
        const decryptedClientEmail = await decryptDataAsync(invoice.client_email);
        const clientEmailHash = hashEmail(decryptedClientEmail);
        if (clientEmailHash === userEmailHash) {
          receivedInvoices.push(invoice);
        }
      } catch (error) {
        // Ignorer les erreurs de déchiffrement
        continue;
      }
    }

    // Déchiffrer les données des factures reçues et enrichir avec les informations de l'émetteur
    const enrichedInvoices = [];
    for (const invoice of receivedInvoices) {
      const decryptedInvoice = await decryptInvoiceDataAsync(invoice);

      // Récupérer les informations de l'émetteur
      const { data: senderProfil, error: senderProfilError } = await supabase
        .from('user_profil')
        .select('prenom, nom')
        .eq('user_id', invoice.user_id)
        .maybeSingle();

      const { data: senderCompany, error: senderCompanyError } = await supabase
        .from('invoices_company_settings')
        .select('*')
        .eq('user_id', invoice.user_id)
        .maybeSingle();

      // Déchiffrer les données de l'émetteur
      const decryptedSenderProfil = senderProfil ? await decryptProfilDataAsync(senderProfil) : null;
      const decryptedSenderCompany = senderCompany ? await decryptCompanyDataAsync(senderCompany) : null;

      // Ajouter les informations de l'émetteur à la facture
      decryptedInvoice.sender_info = {
        prenom: decryptedSenderProfil?.prenom || '',
        nom: decryptedSenderProfil?.nom || '',
        entreprise: decryptedSenderCompany?.nom || '',
        adresse: decryptedSenderCompany?.adresse || '',
        code_postal: decryptedSenderCompany?.code_postal || '',
        ville: decryptedSenderCompany?.ville || '',
        telephone: decryptedSenderCompany?.telephone || '',
        email: decryptedSenderCompany?.email || '',
        site_web: decryptedSenderCompany?.site_web || '',
        siret: decryptedSenderCompany?.siret || '',
        tva: decryptedSenderCompany?.tva || '',
        forme_juridique: decryptedSenderCompany?.forme_juridique || '',
        code_ape: decryptedSenderCompany?.code_ape || '',
        rcs: decryptedSenderCompany?.rcs || '',
        capital: decryptedSenderCompany?.capital || '',
        mention_pied_page: decryptedSenderCompany?.mention_pied_page || '',
        iban: decryptedSenderCompany?.iban || '',
        bic: decryptedSenderCompany?.bic || '',
        banque: decryptedSenderCompany?.banque || ''
      };

      enrichedInvoices.push(decryptedInvoice);
    }

    // Mettre les factures enrichies en cache
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(enrichedInvoices));

    return res.json({
      success: true,
      data: enrichedInvoices
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération des factures reçues:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des factures reçues'
    });
  }
};

// Récupérer les avoirs reçus (par client_id)
export const getReceivedCreditNotes = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }

    // Créer une clé de cache unique
    const cacheKey = `${INVOICE_LIST_CACHE_PREFIX}${userId}:getReceivedCreditNotes`;
    const cachedCreditNotes = await redis.get(cacheKey);
    if (cachedCreditNotes) {
      res.status(200).json({ 
        success: true,
        data: JSON.parse(cachedCreditNotes)
      });
      return;
    }

    // Récupérer l'email de l'utilisateur connecté et créer son hash
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      logger.error('Erreur lors de la récupération de l\'utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations utilisateur'
      });
    }

    // Déchiffrer l'email de l'utilisateur et créer son hash
    const decryptedUserEmail = await decryptDataAsync(userData.email);
    const userEmailHash = hashEmail(decryptedUserEmail);

    // Récupérer tous les avoirs où l'email client correspond à l'email de l'utilisateur connecté
    // et où le statut n'est pas 'brouillon'
    const { data: allCreditNotes, error: creditNotesError } = await supabase
      .from('invoices')
      .select(`
        *,
        invoice_items:invoice_items(*)
      `)
      .eq('type', 'avoir')
      .neq('statut', 'brouillon');

    if (creditNotesError) {
      logger.error('Erreur lors de la récupération des avoirs:', creditNotesError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des avoirs reçus'
      });
    }

    // Filtrer les avoirs où l'email client correspond à l'email de l'utilisateur
    const receivedCreditNotes = [];
    for (const creditNote of allCreditNotes || []) {
      if (!creditNote.client_email) continue;
      try {
        const decryptedClientEmail = await decryptDataAsync(creditNote.client_email);
        const clientEmailHash = hashEmail(decryptedClientEmail);
        if (clientEmailHash === userEmailHash) {
          receivedCreditNotes.push(creditNote);
        }
      } catch (error) {
        // Ignorer les erreurs de déchiffrement
        continue;
      }
    }

    // Déchiffrer les données des avoirs reçus et enrichir avec les informations de l'émetteur
    const enrichedCreditNotes = [];
    for (const creditNote of receivedCreditNotes) {
      const decryptedCreditNote = await decryptInvoiceDataAsync(creditNote);

      // Récupérer les informations de l'émetteur
      const { data: senderProfil, error: senderProfilError } = await supabase
        .from('user_profil')
        .select('prenom, nom')
        .eq('user_id', creditNote.user_id)
        .maybeSingle();

      const { data: senderCompany, error: senderCompanyError } = await supabase
        .from('invoices_company_settings')
        .select('*')
        .eq('user_id', creditNote.user_id)
        .maybeSingle();

      // Déchiffrer les données de l'émetteur
      const decryptedSenderProfil = senderProfil ? await decryptProfilDataAsync(senderProfil) : null;
      const decryptedSenderCompany = senderCompany ? await decryptCompanyDataAsync(senderCompany) : null;

      // Ajouter les informations de l'émetteur à l'avoir
      decryptedCreditNote.sender_info = {
        prenom: decryptedSenderProfil?.prenom || '',
        nom: decryptedSenderProfil?.nom || '',
        entreprise: decryptedSenderCompany?.nom || '',
        adresse: decryptedSenderCompany?.adresse || '',
        code_postal: decryptedSenderCompany?.code_postal || '',
        ville: decryptedSenderCompany?.ville || '',
        telephone: decryptedSenderCompany?.telephone || '',
        email: decryptedSenderCompany?.email || '',
        site_web: decryptedSenderCompany?.site_web || '',
        siret: decryptedSenderCompany?.siret || '',
        tva: decryptedSenderCompany?.tva || '',
        forme_juridique: decryptedSenderCompany?.forme_juridique || '',
        code_ape: decryptedSenderCompany?.code_ape || '',
        rcs: decryptedSenderCompany?.rcs || '',
        capital: decryptedSenderCompany?.capital || '',
        mention_pied_page: decryptedSenderCompany?.mention_pied_page || '',
        iban: decryptedSenderCompany?.iban || '',
        bic: decryptedSenderCompany?.bic || '',
        banque: decryptedSenderCompany?.banque || ''
      };

      enrichedCreditNotes.push(decryptedCreditNote);
    }

    // Mettre les avoirs enrichis en cache
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(enrichedCreditNotes));

    return res.json({
      success: true,
      data: enrichedCreditNotes
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération des avoirs reçus:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des avoirs reçus'
    });
  }
};

// Récupérer les informations de l'entreprise d'un utilisateur
export const getUserCompanyInfo = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'ID utilisateur manquant'
      });
      return;
    }

    // Créer une clé de cache unique
    const cacheKey = `${INVOICE_CACHE_PREFIX}${userId}:getUserCompanyInfo`;
    const cachedUserInfo = await redis.get(cacheKey);
    if (cachedUserInfo) {
      res.status(200).json({
        success: true,
        data: JSON.parse(cachedUserInfo)
      });
      return;
    }
    
    // Récupérer les informations du profil de l'utilisateur
    const { data: userProfil, error: profilError } = await supabase
      .from('user_profil')
      .select('prenom, nom')
      .eq('user_id', userId)
      .maybeSingle();

    if (profilError) {
      logger.error('Erreur lors de la récupération du profil de l\'utilisateur:', profilError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations de l\'utilisateur'
      });
    }

    // Déchiffrer les données du profil
    const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

    // Récupérer les paramètres d'entreprise de l'utilisateur
    const { data: companySettings, error: companyError } = await supabase
      .from('invoices_company_settings')
      .select('nom')
      .eq('user_id', userId)
      .maybeSingle();

    if (companyError) {
      logger.error('Erreur lors de la récupération des paramètres d\'entreprise:', companyError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations d\'entreprise'
      });
    }

    // Déchiffrer les données de l'entreprise
    const decryptedCompanySettings = companySettings ? await decryptCompanyDataAsync(companySettings) : null;

    // Mettre les informations de l'entreprise en cache
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify({
      prenom: decryptedUserProfil?.prenom || '',
      nom: decryptedUserProfil?.nom || '',
      entreprise: decryptedCompanySettings?.nom || ''
    }));

    return res.json({
      success: true,
      data: {
        prenom: decryptedUserProfil?.prenom || '',
        nom: decryptedUserProfil?.nom || '',
        entreprise: decryptedCompanySettings?.nom || ''
      }
    });
  } catch (err: any) {
    logger.error('Erreur serveur lors de la récupération des informations utilisateur:', err);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des informations utilisateur'
    });
  }
};

// Mettre à jour le statut d'une facture
export const updateInvoiceStatus = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const invoiceId = req.params.id;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
      return;
    }
    
    // Vérifier si la facture existe et appartient à l'utilisateur
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*, users:user_id (email)')
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();

    if (invoiceError || !invoice) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    // Décrypter les données sensibles de la facture
    const decryptedInvoice = await decryptInvoiceDataAsync(invoice);
    // Décrypter les données de l'utilisateur si elles existent
    if (invoice.users?.email) {
      const decryptedUserData = await decryptUserDataAsync(invoice.users);
      invoice.users.email = decryptedUserData.email;
    }

    // Invalider les caches pertinents
    const invoiceKey = `${INVOICE_CACHE_PREFIX}${userId}:${invoiceId}`;
    const listCachePattern = `${INVOICE_LIST_CACHE_PREFIX}${userId}:*`;
    
    // Vérifier si c'est une facture (pas un devis ou un avoir)
    if (invoice.type !== 'facture') {
      res.status(400).json({
        success: false,
        message: 'Cette action n\'est disponible que pour les factures'
      });
      return;
    }

    // Vérifier si le statut n'est pas déjà payé ou partiellement payé
    if (invoice.statut === 'paye' || invoice.statut === 'partiellement_paye') {
      res.status(400).json({
        success: false,
        message: 'La facture est déjà payée ou partiellement payée'
      });
      return;
    }
    
    // Vérifier que le statut est valide
    const statusSchema = z.object({
      status: z.enum([
        'envoye', 'paye', 'partiellement_paye', 'en_retard', 'annule'
      ]),
      date_paiement: z.string().optional()
    });
    
    const validationResult = statusSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        message: 'Statut invalide',
        errors: validationResult.error.errors
      });
      return;
    }
    
    const { status, date_paiement } = validationResult.data;
    const previousStatus = invoice.statut;
    
    // Créer l'objet de mise à jour
    const updateData: { statut: string, date_paiement?: string } = { statut: status };
    
    // Ajouter la date de paiement si le statut est payé ou partiellement payé
    if ((status === 'paye' || status === 'partiellement_paye') && (date_paiement || previousStatus !== 'paye' && previousStatus !== 'partiellement_paye')) {
      updateData.date_paiement = date_paiement || new Date().toISOString();
    }
    
    // Mettre à jour la facture avec le nouveau statut et éventuellement la date de paiement
    const { data: updatedInvoice, error: updateError } = await supabase
      .from('invoices')
      .update(updateData)
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .select()
      .single();
    
    if (updateError) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du statut',
        error: updateError.message
      });
    }
    
    // Ajouter une entrée dans l'historique du document
    await supabase
      .from('document_history')
      .insert({
        document_id: invoiceId,
        action: 'update_status',
        details: {
          previous_status: invoice.statut,
          new_status: status,
          date_paiement: updateData.date_paiement
        },
        user_id: userId
      });
    
    // Si la facture passe à l'état payé ou partiellement payé, envoi de notifications et emails
    if ((status === 'paye' || status === 'partiellement_paye') && previousStatus !== 'paye' && previousStatus !== 'partiellement_paye') {
      // Récupérer les informations du profil de l'utilisateur
      const { data: userProfil, error: profilError } = await supabase
        .from('user_profil')
        .select('prenom, nom')
        .eq('user_id', userId)
        .maybeSingle();

      // Déchiffrer les données du profil
      const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;
      
      // Envoyer une notification au jobbeur (créateur de la facture)
      const { error: notifJobbeurError } = await supabase
        .from('user_notifications')
        .insert([{
          user_id: userId,
          type: 'invoice',
          title: 'Facture mise à jour',
          content: `Votre facture ${invoice.number} a été marquée comme ${status === 'paye' ? 'payée' : 'partiellement payée'}.`,
          link: `/dashboard/facturation`,
          is_read: false,
          is_archived: false
        }]);

      if (notifJobbeurError) {
        logger.error('Erreur lors de l\'envoi de la notification au jobbeur:', notifJobbeurError);
      }

      // Chercher si le client existe déjà en tant qu'utilisateur en utilisant le hash d'email
      if (decryptedInvoice.client_email) {
        const clientEmailHashForInvoice = hashEmail(decryptedInvoice.client_email);
        const { data: clientUser, error: clientUserError } = await supabase
          .from('users')
          .select('id')
          .eq('email_hash', clientEmailHashForInvoice)
          .maybeSingle();

        // Si le client existe en tant qu'utilisateur, lui envoyer une notification également
        if (clientUser && !clientUserError) {
          const { error: notifClientError } = await supabase
            .from('user_notifications')
            .insert([{
              user_id: clientUser.id,
              type: 'invoice',
              title: 'Facture mise à jour',
              content: `La facture ${invoice.number} a été marquée comme ${status === 'paye' ? 'payée' : 'partiellement payée'}.`,
              link: `/dashboard/facturation`,
              is_read: false,
              is_archived: false
            }]);

          if (notifClientError) {
            logger.error('Erreur lors de l\'envoi de la notification au client:', notifClientError);
          }
        }

        // Envoyer un email au client pour l'informer du paiement
        const emailData = {
          subject: `Facture ${invoice.number} - Paiement ${status === 'paye' ? 'reçu' : 'partiel reçu'}`,
          html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <style>
                body {
                  font-family: Arial, sans-serif;
                  color: #333;
                  line-height: 1.6;
                  margin: 0;
                  padding: 0;
                }
                .container {
                  max-width: 600px;
                  margin: 0 auto;
                  padding: 20px;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                }
                .header {
                  background-color: #FF7A35;
                  color: white;
                  padding: 15px 20px;
                  border-radius: 8px 8px 0 0;
                  margin: -20px -20px 20px;
                }
                .footer {
                  background-color: #f9fafb;
                  padding: 15px 20px;
                  border-top: 1px solid #e5e7eb;
                  margin: 20px -20px -20px;
                  border-radius: 0 0 8px 8px;
                  font-size: 12px;
                  color: #6b7280;
                }
                h1 {
                  margin: 0;
                  font-size: 24px;
                }
                .success-box {
                  background-color: #DCFCE7;
                  border-left: 4px solid #15803D;
                  padding: 15px;
                  margin: 20px 0;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>Facture ${status === 'paye' ? 'payée' : 'partiellement payée'}</h1>
                </div>
                
                <p>Bonjour,</p>
                
                <div class="success-box">
                  <p><strong>Information :</strong> Votre paiement pour la facture ${invoice.number} a été ${status === 'paye' ? 'reçu' : 'partiellement reçu'}.</p>
                </div>
                
                <p>Détails de la facture :</p>
                <ul>
                  <li><strong>Numéro :</strong> ${invoice.number}</li>
                  <li><strong>Montant total :</strong> ${invoice.total_ttc.toFixed(2)} €</li>
                  <li><strong>Date de mise à jour :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                </ul>
                
                <p>Nous vous remercions pour votre confiance.</p>
                
                <p>Cordialement,</p>
                <p><strong>${decryptedUserProfil?.prenom || ''} ${decryptedUserProfil?.nom || ''}</strong></p>
                
                <div class="footer">
                  <p>Ce message est généré automatiquement via <strong>JobPartiel.fr</strong></p>
                  <p>Le jobbing malin pour arrondir vos fins de mois</p>
                </div>
              </div>
            </body>
            </html>
          `,
          attachments: []
        };

        try {
          await sendInvoiceEmail(decryptedInvoice.client_email, emailData);
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email au client:', emailError);
        }
      }

      // Envoi d'une copie d'email au jobbeur
      if (invoice.users?.email) {
        try {
          await sendInvoiceEmail(invoice.users.email, {
            subject: `Confirmation - Facture ${invoice.number} ${status === 'paye' ? 'payée' : 'partiellement payée'}`,
            html: `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    color: #333;
                    line-height: 1.6;
                    margin: 0;
                    padding: 0;
                  }
                  .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                  }
                  .header {
                    background-color: #FF7A35;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px 8px 0 0;
                    margin: -20px -20px 20px;
                  }
                  .footer {
                    background-color: #f9fafb;
                    padding: 15px 20px;
                    border-top: 1px solid #e5e7eb;
                    margin: 20px -20px -20px;
                    border-radius: 0 0 8px 8px;
                    font-size: 12px;
                    color: #6b7280;
                  }
                  h1 {
                    margin: 0;
                    font-size: 24px;
                  }
                  .success-box {
                    background-color: #DCFCE7;
                    border-left: 4px solid #15803D;
                    padding: 15px;
                    margin: 20px 0;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <div class="header">
                    <h1>Facture ${status === 'paye' ? 'payée' : 'partiellement payée'}</h1>
                  </div>
                  
                  <p>Bonjour,</p>
                  
                  <div class="success-box">
                    <p><strong>Confirmation :</strong> Vous avez marqué la facture ${invoice.number} comme ${status === 'paye' ? 'payée' : 'partiellement payée'}.</p>
                  </div>
                  
                  <p>Détails de la facture :</p>
                  <ul>
                    <li><strong>Numéro :</strong> ${invoice.number}</li>
                    <li><strong>Client :</strong> ${decryptedInvoice.client_name}</li>
                    <li><strong>Montant total :</strong> ${invoice.total_ttc.toFixed(2)} €</li>
                    <li><strong>Date de mise à jour :</strong> ${new Date().toLocaleDateString('fr-FR')}</li>
                  </ul>
                  
                  <p>Un email de confirmation a ${decryptedInvoice.client_email ? 'été envoyé au client.' : 'n\'a pas pu être envoyé car aucune adresse email n\'est associée à ce client.'}</p>
                  
                  <p>Cordialement,</p>
                  <p><strong>L'équipe JobPartiel</strong></p>
                  
                  <div class="footer">
                    <p>Ce message est généré automatiquement via <strong>JobPartiel.fr</strong></p>
                    <p>Le jobbing malin pour arrondir vos fins de mois</p>
                  </div>
                </div>
              </body>
              </html>
            `,
            attachments: []
          });
        } catch (emailError) {
          logger.error('Erreur lors de l\'envoi de l\'email au jobbeur:', emailError);
        }
      }
    }
    
    await redis.del(invoiceKey);
    const keys = await redis.keys(listCachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    res.status(200).json({
      success: true,
      message: 'Statut de la facture mis à jour avec succès',
      data: updatedInvoice
    });
    
  } catch (error: any) {
    logger.error('Erreur lors de la mise à jour du statut:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut',
      error: error.message
    });
  }
};