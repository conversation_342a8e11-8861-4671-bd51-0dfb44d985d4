import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { uploadGalleryPhoto } from '../services/storage';
import { FileRequest } from '../middleware/fileValidation';
import contentModerationService from '../services/contentModerationService';

export enum LogEventType {
  SERVER_ERROR = 'SERVER_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  USER_ERROR = 'USER_ERROR',
  USER_UPDATE = 'USER_UPDATE',
  AUTH_FAILURE = 'AUTH_FAILURE',
  DB_ERROR = 'DB_ERROR'
}

export class FeaturedPhotosController {
  async getFeaturedPhotos(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de récupération des photos mises en avant sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { data: photos, error } = await supabase
        .from('user_featured_photos')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des photos mises en avant', { error, userId });
        return res.status(500).json({
          message: 'Erreur lors de la récupération des photos mises en avant',
          success: false,
          toastType: 'error'
        });
      }

      return res.json({
        success: true,
        photos: photos.map(photo => ({
          id: photo.id,
          photo_url: photo.photo_url,
          caption: photo.caption
        }))
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la récupération des photos mises en avant', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: req.user?.userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la récupération des photos mises en avant',
        success: false,
        toastType: 'error'
      });
    }
  }

  async uploadFeaturedPhoto(req: FileRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      const file = req.files?.photo;

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'upload de photo mise en avant sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      if (!file || Array.isArray(file)) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Tentative d\'upload sans fichier valide');
        return res.status(400).json({
          message: 'Aucun fichier n\'a été fourni',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier le nombre de photos existantes
      const { count: existingPhotosCount, error: countError } = await supabase
        .from('user_featured_photos')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (countError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la vérification du nombre de photos', {
          error: countError,
          errorMessage: countError.message,
          errorDetails: countError.details,
          errorHint: countError.hint,
          errorCode: countError.code,
          userId
        });
        throw countError;
      }

      if (existingPhotosCount && existingPhotosCount >= 3) {
        return res.status(400).json({
          message: 'Vous avez atteint la limite de 3 photos mises en avant',
          success: false,
          toastType: 'error'
        });
      }

      // Lire le fichier temporaire avec la même méthode que les autres uploads
      const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
        require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      // Utiliser le service de stockage existant
      const photoUrl = await uploadGalleryPhoto(
        userId,
        fileBuffer,
        file.mimetype,
        'featured', // On utilise 'featured' comme ID de galerie spécial
        'photos_mises_en_avant'
      );

      // Enregistrer dans la base de données
      logger.info('Tentative d\'insertion dans la base de données', {
        userId,
        photo_url: photoUrl,
        storage_path: photoUrl.split('galerie_realisation_client/')[1]
      });

      // Insertion simple sans select
      const { data: photo, error: dbError } = await supabase
        .from('user_featured_photos')
        .insert([{
          user_id: userId,
          photo_url: photoUrl,
          storage_path: photoUrl.split('galerie_realisation_client/')[1]
        }])
        .select()
        .single();

      if (dbError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur détaillée lors de l\'insertion', {
          error: dbError,
          errorMessage: dbError.message,
          errorDetails: dbError.details,
          errorHint: dbError.hint,
          errorCode: dbError.code,
          userId,
          data: {
            user_id: userId,
            photo_url: photoUrl,
            storage_path: photoUrl.split('galerie_realisation_client/')[1]
          }
        });

        // Vérifier si l'erreur est liée à la contrainte de clé étrangère
        if (dbError.code === '23503') {
          logger.error('Erreur de clé étrangère - L\'utilisateur n\'existe pas dans auth.users', {
            userId,
            error: dbError
          });
          return res.status(400).json({
            message: 'Erreur : utilisateur non trouvé',
            success: false,
            toastType: 'error'
          });
        }

        throw new Error(`Erreur lors de l'insertion dans la base de données: ${dbError.message}`);
      }

      // Récupérer la photo insérée
      const { data: insertedPhoto, error: fetchError } = await supabase
        .from('user_featured_photos')
        .select('*')
        .eq('photo_url', photoUrl)
        .single();

      if (fetchError || !insertedPhoto) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération de la photo insérée', {
          error: fetchError,
          userId
        });
        throw fetchError || new Error('Photo non trouvée après insertion');
      }

      logger.info('Photo enregistrée avec succès dans la base de données', {
        photoId: insertedPhoto.id,
        userId
      });

      // Créer un signalement différé seulement si l'image a été modérée avec un ID temporaire réel
      if (req.body?.tempImageId && typeof req.body.tempImageId === 'string' && req.body.tempImageId.trim() !== '') {
        try {
          await contentModerationService.createDeferredImageReport(
            req.body.tempImageId,
            insertedPhoto.id,
            'featured',
            userId
          );

          logger.info('Signalement différé créé avec succès', {
            tempImageId: req.body.tempImageId,
            permanentImageId: insertedPhoto.id,
            contentType: 'featured',
            userId
          });
        } catch (reportError) {
          // Ne pas bloquer le processus si la création du signalement échoue
          logger.error('Erreur lors de la création du signalement différé', {
            error: reportError instanceof Error ? reportError.message : 'Unknown error',
            userId,
            photoId: insertedPhoto.id
          });
        }
      }

      return res.json({
        success: true,
        photo: {
          id: insertedPhoto.id,
          photo_url: insertedPhoto.photo_url,
          caption: insertedPhoto.caption
        }
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de l\'upload de la photo mise en avant', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: req.user?.userId
      });
      return res.status(500).json({
        message: 'Erreur lors de l\'upload de la photo',
        success: false,
        toastType: 'error'
      });
    }
  }

  async deleteFeaturedPhoto(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const photoId = req.params.photoId;

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de suppression de photo mise en avant sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer le chemin de stockage
      const { data: photo, error: fetchError } = await supabase
        .from('user_featured_photos')
        .select('storage_path')
        .eq('id', photoId)
        .eq('user_id', userId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      if (!photo) {
        return res.status(404).json({
          message: 'Photo non trouvée',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer de Supabase Storage
      const { error: storageError } = await supabase.storage
        .from('galerie_realisation_client')
        .remove([photo.storage_path]);

      if (storageError) {
        throw storageError;
      }

      // Supprimer de la base de données
      const { error: dbError } = await supabase
        .from('user_featured_photos')
        .delete()
        .eq('id', photoId)
        .eq('user_id', userId);

      if (dbError) {
        throw dbError;
      }

      return res.json({
        success: true,
        message: 'Photo supprimée avec succès'
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la suppression de la photo mise en avant', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: req.user?.userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la suppression de la photo',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updateFeaturedPhotoCaption(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      const photoId = req.params.photoId;
      const caption = req.body.caption;

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de mise à jour de légende sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // if (!caption) {
      //   logger.warn(LogEventType.VALIDATION_ERROR, 'Tentative de mise à jour de légende sans contenu');
      //   res.status(400).json({
      //     message: 'La légende ne peut pas être vide.',
      //     success: false,
      //     toastType: 'error'
      //   });
      // }

      const { data: photo, error } = await supabase
        .from('user_featured_photos')
        .update({ caption })
        .eq('id', photoId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de la légende dans la base de données', {
          error,
          userId,
          photoId
        });
        throw error;
      }

      return res.json({
        success: true,
        photo: {
          id: photo.id,
          photo_url: photo.photo_url,
          caption: photo.caption
        }
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la mise à jour de la légende', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: req.user?.userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la mise à jour de la légende',
        success: false,
        toastType: 'error'
      });
    }
  }
}

export const featuredPhotosController = new FeaturedPhotosController();