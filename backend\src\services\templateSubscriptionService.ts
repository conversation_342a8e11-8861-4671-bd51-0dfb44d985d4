import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { redis } from '../config/redis';
import { logUserActivity } from '../utils/activityLogger';

const CACHE_PREFIX = 'card_editor:';

/**
 * Service pour gérer l'activation/désactivation automatique des templates en fonction de l'abonnement
 */
const templateSubscriptionService = {
  /**
   * Vérifie et met à jour les templates d'un utilisateur en fonction de son abonnement
   * @param userId ID de l'utilisateur
   * @returns Résultat de l'opération avec les templates activés/désactivés
   */
  async updateTemplatesBasedOnSubscription(userId: string) {
    try {
      // Récupérer les limites d'abonnement de l'utilisateur
      const subscriptionLimits = await getUserSubscriptionLimits(userId);
      
      // Résultat à retourner
      const result = {
        businessCards: {
          activated: [] as any[],
          deactivated: [] as any[]
        },
        flyers: {
          activated: [] as any[],
          deactivated: [] as any[]
        }
      };

      // Traiter les cartes de visite
      await this.processTemplateType(
        userId, 
        ['business_card', 'business_card_landscape'], 
        subscriptionLimits.businessCardsLimit,
        result.businessCards
      );

      // Traiter les flyers
      await this.processTemplateType(
        userId, 
        ['flyer', 'flyer_landscape'], 
        subscriptionLimits.flyersLimit,
        result.flyers
      );

      // Invalider le cache des templates de l'utilisateur
      await redis.del(`${CACHE_PREFIX}templates:${userId}`);

      return {
        success: true,
        result
      };
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour des templates pour l'utilisateur ${userId}:`, error);
      return {
        success: false,
        error: 'Erreur lors de la mise à jour des templates'
      };
    }
  },

  /**
   * Traite un type spécifique de template (cartes de visite ou flyers)
   * @param userId ID de l'utilisateur
   * @param templateTypes Types de templates à traiter
   * @param limit Limite d'abonnement
   * @param resultContainer Conteneur pour stocker les résultats
   */
  async processTemplateType(
    userId: string, 
    templateTypes: string[], 
    limit: number,
    resultContainer: { activated: any[], deactivated: any[] }
  ) {
    try {
      // 1. Récupérer tous les templates de ce type pour l'utilisateur
      const { data: templates, error } = await supabase
        .from('card_templates')
        .select('*')
        .eq('user_id', userId)
        .in('type', templateTypes)
        .order('order_index', { ascending: true });

      if (error) {
        throw error;
      }

      if (!templates || templates.length === 0) {
        return;
      }

      // 2. Séparer les templates actifs et inactifs
      const activeTemplates = templates.filter(t => !t.name.startsWith('[DÉSACTIVÉ]'));
      const inactiveTemplates = templates.filter(t => t.name.startsWith('[DÉSACTIVÉ]'));

      // 3. Si le nombre de templates actifs est supérieur à la limite
      if (activeTemplates.length > limit) {
        // Désactiver les templates excédentaires (en commençant par les derniers)
        const templatesToDeactivate = activeTemplates.slice(limit);
        
        for (const template of templatesToDeactivate) {
          const newName = `[DÉSACTIVÉ] ${template.name}`;
          
          const { data, error: updateError } = await supabase
            .from('card_templates')
            .update({
              name: newName,
              is_public: false,
              updated_at: new Date().toISOString()
            })
            .eq('id', template.id)
            .select()
            .single();

          if (updateError) {
            logger.error(`Erreur lors de la désactivation du template ${template.id}:`, updateError);
            continue;
          }

          // Invalider le cache du template
          await redis.del(`${CACHE_PREFIX}template:${template.id}`);
          
          // Ajouter au résultat
          resultContainer.deactivated.push(data);
          
          // Log de l'activité
          logUserActivity(
            userId, 
            'auto_deactivate_card_template', 
            template.id, 
            'card_template', 
            {
              template_name: template.name,
              template_type: template.type,
              reason: 'subscription_limit'
            }
          );
        }
      } 
      // 4. Si le nombre de templates actifs est inférieur à la limite et qu'il y a des templates inactifs
      else if (activeTemplates.length < limit && inactiveTemplates.length > 0) {
        // Calculer combien de templates peuvent être activés
        const canActivateCount = limit - activeTemplates.length;
        // Activer les premiers templates inactifs jusqu'à la limite
        const templatesToActivate = inactiveTemplates.slice(0, canActivateCount);
        
        for (const template of templatesToActivate) {
          const newName = template.name.replace(/^\[DÉSACTIVÉ\]\s*/, '');
          
          const { data, error: updateError } = await supabase
            .from('card_templates')
            .update({
              name: newName,
              updated_at: new Date().toISOString()
            })
            .eq('id', template.id)
            .select()
            .single();

          if (updateError) {
            logger.error(`Erreur lors de l'activation du template ${template.id}:`, updateError);
            continue;
          }

          // Invalider le cache du template
          await redis.del(`${CACHE_PREFIX}template:${template.id}`);
          
          // Ajouter au résultat
          resultContainer.activated.push(data);
          
          // Log de l'activité
          logUserActivity(
            userId, 
            'auto_activate_card_template', 
            template.id, 
            'card_template', 
            {
              template_name: template.name,
              template_type: template.type,
              reason: 'subscription_upgrade'
            }
          );
        }
      }
    } catch (error) {
      logger.error(`Erreur lors du traitement des templates de type ${templateTypes.join(', ')} pour l'utilisateur ${userId}:`, error);
      throw error;
    }
  }
};

export default templateSubscriptionService;
