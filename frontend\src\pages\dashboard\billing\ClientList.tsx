import { useState, useEffect } from 'react';
import { notify } from '../../../components/Notification';
import { Plus, Edit, Trash2, Search, X, AlertTriangle, ArrowLeft, Download } from 'lucide-react';
import LoadingSpinner from '../../../components/LoadingSpinner';
import ClientForm from './ClientForm';
import { invoiceService } from '../../../services/invoiceService';
import ModalPortal from '../../../components/ModalPortal';
import { motion, AnimatePresence } from 'framer-motion';
import { styled } from '@mui/material/styles';
import { Typography } from '@mui/material';
import { Link } from 'react-router-dom';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

export default function ClientList() {
  const [clients, setClients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingClient, setEditingClient] = useState<any | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<any | null>(null);
  const [searchText, setSearchText] = useState('');
  const [exportLoading, setExportLoading] = useState(false);

  const fetchClients = async () => {
    try {
      const data = await invoiceService.getClients();
      setClients(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des clients:', error);
      notify('Erreur lors de la récupération des clients', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  const handleEditClient = (client: any) => {
    setEditingClient(client);
    setShowForm(true);
  };

  const handleDeleteClient = (client: any) => {
    setClientToDelete(client);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!clientToDelete) return;

    try {
      await invoiceService.deleteClient(clientToDelete.id);
      notify('Client supprimé avec succès', 'success');
      fetchClients();
      setClientToDelete(null);
      setShowDeleteModal(false);
    } catch (error: any) {
      if (error.response?.status === 409) {
        notify('Ce client ne peut pas être supprimé car il est lié à des documents', 'error');
      } else {
        notify('Erreur lors de la suppression du client', 'error');
      }
    }
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingClient(null);
  };

  const handleFormSave = () => {
    setShowForm(false);
    setEditingClient(null);
    fetchClients();
  };

  const handleExportExcel = async () => {
    try {
      setExportLoading(true);
      await invoiceService.exportClientsToExcel();
      notify('Export Excel réussi', 'success');
    } catch (error) {
      console.error('Erreur lors de l\'export Excel:', error);
      notify('Erreur lors de l\'export Excel', 'error');
    } finally {
      setExportLoading(false);
    }
  };

  const filteredClients = clients.filter(client =>
    !searchText ||
    client.nom.toLowerCase().includes(searchText.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchText.toLowerCase()) ||
    client.telephone?.toLowerCase().includes(searchText.toLowerCase()) ||
    client.siret?.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div className="space-y-6 px-2 md:px-0">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 bg-white p-6 rounded-xl shadow-sm">
        <PageTitle variant="h1">
          Gestion des clients
        </PageTitle>
        
        <div className="flex flex-col md:flex-row gap-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleExportExcel}
            disabled={exportLoading || clients.length === 0}
            className="bg-green-600 text-white px-5 py-2.5 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2 font-medium shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exportLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Download size={20} />
            )}
            {exportLoading ? 'Export...' : 'Exporter Excel'}
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowForm(true)}
            className="bg-[#FF7A35] text-white px-5 py-2.5 rounded-lg hover:bg-[#ff6b2c] transition-colors flex items-center justify-center gap-2 font-medium shadow-md hover:shadow-lg"
          >
            <Plus size={20} />
            Nouveau client
          </motion.button>
          
          <div className="relative">
            <input
              type="text"
              placeholder="Rechercher un client..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="pl-10 pr-10 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent w-full min-w-[250px] transition-all duration-200"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            {searchText && (
              <motion.button 
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                onClick={() => setSearchText('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X size={16} />
              </motion.button>
            )}
          </div>
        </div>
      </div>

      <AnimatePresence mode="wait">
        {loading ? (
          <div className="flex justify-center py-20">
            <LoadingSpinner />
          </div>
        ) : filteredClients.length === 0 ? (
          <div className="text-center py-16 bg-white rounded-xl shadow-sm">
            <div className="flex flex-col items-center gap-4">
              <div className="text-gray-400">
                <Search size={48} />
              </div>
              <div>
                <p className="text-gray-600 text-lg">Aucun client trouvé</p>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowForm(true)}
                  className="mt-4 text-[#FF7A35] hover:text-[#ff6b2c] font-medium flex items-center gap-2 mx-auto"
                >
                  <Plus size={20} />
                  Créer un client
                </motion.button>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredClients.map((client, index) => (
              <div
                key={client.id}
                    className="bg-white rounded-xl shadow-sm border p-5 hover:border-[#FF7A35]/20 transition-all duration-200 hover:shadow-md group"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-800 text-lg group-hover:text-[#FF7A35] transition-colors">
                      {client.nom}
                    </h3>
                    {client.email && (
                      <p className="text-sm text-gray-600 mt-1">{client.email}</p>
                    )}
                  </div>
                  <div className="flex gap-1 sm:gap-2 shrink-0">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handleEditClient(client)}
                      className="p-2 sm:p-2.5 text-gray-500 hover:text-[#FF7A35] rounded-lg hover:bg-[#FF7A35]/10 transition-all"
                      title="Modifier"
                    >
                      <Edit className="w-[18px] h-[18px] sm:w-5 sm:h-5" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handleDeleteClient(client)}
                      className="p-2 sm:p-2.5 text-gray-500 hover:text-red-600 rounded-lg hover:bg-red-50 transition-all"
                      title="Supprimer"
                    >
                      <Trash2 className="w-[18px] h-[18px] sm:w-5 sm:h-5" />
                    </motion.button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {client.telephone && (
                    <p className="text-sm text-gray-600 flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center">📞</span>
                      {client.telephone}
                    </p>
                  )}
                  
                  {client.adresse && (
                    <p className="text-sm text-gray-600 flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center">📍</span>
                      {client.adresse}
                    </p>
                  )}
                  
                  {client.siret && (
                    <p className="text-sm text-gray-600 flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center">🏢</span>
                      SIRET : {client.siret}
                    </p>
                  )}
                  
                  {client.tva && (
                    <p className="text-sm text-gray-600 flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center">📄</span>
                      N° TVA : {client.tva}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </AnimatePresence>

      <div className="mt-6">
        <Link 
          to="/dashboard/facturation"
          className="inline-flex items-center gap-2 p-2 text-gray-500 hover:text-[#FF7A35] rounded-lg hover:bg-[#FF7A35]/10 transition-all"
        >
          <ArrowLeft className="w-5 h-5" />
          <span className="text-sm font-medium">Retour aux factures</span>
        </Link>
      </div>

      {/* Formulaire de création/modification */}
      {showForm && (
        <ClientForm
          client={editingClient}
          onSave={handleFormSave}
          onCancel={handleFormClose}
        />
      )}

      {/* Modal de confirmation de suppression */}
      {showDeleteModal && clientToDelete && (
        <ModalPortal isOpen={showDeleteModal} onBackdropClick={() => setShowDeleteModal(false)}>
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-xl shadow-2xl overflow-hidden max-w-md w-full"
          >
            <div className="bg-red-600 px-6 py-4 flex justify-between items-center">
              <h2 className="text-white font-semibold text-lg flex items-center gap-2">
                <AlertTriangle size={20} />
                Confirmer la suppression
              </h2>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setShowDeleteModal(false)}
                className="text-white hover:bg-white/20 p-1.5 rounded-lg transition-colors"
              >
                <X size={20} />
              </motion.button>
            </div>

            <div className="p-6">
              <p className="text-gray-600">
                Êtes-vous sûr de vouloir supprimer le client <span className="font-semibold">{clientToDelete.nom}</span> ?
                Cette action est irréversible.
              </p>

              <div className="flex justify-end gap-3 mt-6">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowDeleteModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
                >
                  Annuler
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleConfirmDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
                >
                  Supprimer
                </motion.button>
              </div>
            </div>
          </motion.div>
        </ModalPortal>
      )}
    </div>
  );
} 