import { Router } from 'express';
import { getUserQualityMetrics, generateQualityReport } from '../controllers/qualityMetrics';
import { authMiddleware } from '../middleware/authMiddleware';
import { limiter } from '../middleware/security';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiter spécifique pour les métriques de qualité
const qualityLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 secondes
  max: 20, // 20 requêtes maximum
  message: {
    message: 'Trop de requêtes pour les métriques de qualité. Veuillez réessayer dans 30 secondes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Rate limiter spécifique pour les rapports PDF (plus restrictif)
const pdfLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // 5 requêtes maximum
  message: {
    message: 'Trop de requêtes pour les rapports PDF. Veuillez réessayer dans une minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Route pour obtenir les métriques de qualité d'un utilisateur
router.get('/', authMiddleware.authenticateToken, limiter, qualityLimiter, getUserQualityMetrics);

// Route pour générer un rapport PDF de qualité
router.get('/report', authMiddleware.authenticateToken, limiter, pdfLimiter, generateQualityReport);

export default router; 