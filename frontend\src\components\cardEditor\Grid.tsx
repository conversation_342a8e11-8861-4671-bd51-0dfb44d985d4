import React from 'react';
import { Line, Group } from 'react-konva';

interface GridProps {
  width: number;
  height: number;
  cellSize?: number;
  blockSnapSize?: number;
  strokeWidth?: number;
  stroke?: string;
  visible?: boolean;
}

const Grid: React.FC<GridProps> = ({
  width,
  height,
  cellSize = 20,
  blockSnapSize,
  strokeWidth = 0.5,
  stroke = 'rgba(0, 0, 0, 0.1)',
  visible = true
}) => {
  if (!visible) return null;

  // Utiliser blockSnapSize si fourni, sinon cellSize
  const size = blockSnapSize || cellSize;
  const numHorizontalLines = Math.floor(height / size);
  const numVerticalLines = Math.floor(width / size);

  // Créer les lignes horizontales
  const horizontalLines = Array.from({ length: numHorizontalLines + 1 }).map((_, i) => (
    <Line
      key={`h-${i}`}
      points={[0, i * size, width, i * size]}
      stroke={stroke}
      strokeWidth={strokeWidth}
      perfectDrawEnabled={false}
    />
  ));

  // Créer les lignes verticales
  const verticalLines = Array.from({ length: numVerticalLines + 1 }).map((_, i) => (
    <Line
      key={`v-${i}`}
      points={[i * size, 0, i * size, height]}
      stroke={stroke}
      strokeWidth={strokeWidth}
      perfectDrawEnabled={false}
    />
  ));

  return (
    <Group>
      {horizontalLines}
      {verticalLines}
    </Group>
  );
};

export default Grid;
