import { Router } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import favoritesController from '../controllers/favorites';

const router = Router();

// Rate limiter pour les favoris
const favoritesLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 30, // 30 requêtes maximum
  message: {
    message: 'Trop de requêtes pour les favoris. Veuillez réessayer dans 30 secondes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);
router.use(favoritesLimiter);

// Récupérer tous les favoris de l'utilisateur
router.get('/', favoritesController.getFavorites.bind(favoritesController));

// Ajouter un favori
router.post('/:userId', favoritesController.addFavorite.bind(favoritesController));

// Supprimer un favori
router.delete('/:userId', favoritesController.removeFavorite.bind(favoritesController));

export default router; 