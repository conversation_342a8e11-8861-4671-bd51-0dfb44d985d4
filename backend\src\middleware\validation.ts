import { Request, Response, NextFunction } from 'express';
import { body, validationResult, ValidationError as ExpressValidationError } from 'express-validator';
import { AnyZodObject, ZodError } from 'zod';
import logger from '../utils/logger';
import { z } from 'zod';

// Validation pour l'inscription
export const validateRegister = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Email invalide')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Le mot de passe doit contenir au moins 8 caractères')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/)
    .withMessage('Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial'),
  body('userType')
    .isIn(['jobbeur', 'non-jobbeur'])
    .withMessage('Type d\'utilisateur invalide'),
  body('phone')
    .optional()
    .matches(/^(\+33|0)[1-9](\d{2}){4}$/)
    .withMessage('Numéro de téléphone invalide'),
];

// Validation pour la connexion
export const validateLogin = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Veuillez entrer une adresse email valide')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('Le mot de passe est requis')
    .isLength({ min: 8 })
    .withMessage('Le mot de passe doit contenir au moins 8 caractères'),
];

// Validation pour la réinitialisation du mot de passe
export const validatePasswordReset = [
  body('token')
    .notEmpty()
    .withMessage('Token requis')
    .isUUID()
    .withMessage('Format de token invalide'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Le mot de passe doit contenir au moins 8 caractères')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/)
    .withMessage('Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial'),
];

// Validation pour la demande de réinitialisation du mot de passe
export const validateForgotPassword = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Veuillez entrer une adresse email valide')
    .normalizeEmail(),
];

// Validation pour la vérification d'email
export const validateEmailVerification = [
  body('token')
    .notEmpty()
    .withMessage('Token requis')
    .isLength({ min: 32, max: 128 })
    .withMessage('Token invalide'),
];

// Validation pour la mise à jour du profil
export const validateProfilUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Le prénom doit contenir au moins 2 caractères')
    .matches(/^[a-zA-ZÀ-ÿ\s'-]+$/)
    .withMessage('Le prénom contient des caractères non autorisés')
    .escape(),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Le nom doit contenir au moins 2 caractères')
    .matches(/^[a-zA-ZÀ-ÿ\s'-]+$/)
    .withMessage('Le nom contient des caractères non autorisés')
    .escape(),
  body('phone')
    .optional()
    .matches(/^(\+33|0)[1-9](\d{2}){4}$/)
    .withMessage('Numéro de téléphone invalide'),
];

interface ValidationError {
  type: string;
  value?: any;
  msg: string;
  path: string;
  location?: string;
}

// Middleware de validation
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map((error: ExpressValidationError): ValidationError => {
      if ('path' in error) {
        return {
          type: 'field',
          value: error.value,
          msg: error.msg,
          path: error.path,
          location: error.location
        };
      }
      return {
        type: 'unknown',
        msg: error.msg,
        path: ''
      };
    });

    logger.error('Validation errors:', { errors: formattedErrors });
    res.status(400).json({ errors: formattedErrors });
  }
  next();
};

// Nouvelle validation Zod pour les services
export const validateRequest = (schema: z.ZodTypeAny) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.info('Données reçues pour validation:', {
        body: req.body,
        path: req.path,
        method: req.method
      });

      const validatedData = await schema.parseAsync(req.body);
      
      logger.info('Données validées avec succès:', {
        validatedData,
        path: req.path,
        method: req.method
      });

      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        logger.error('Erreur de validation Zod:', {
          errors: error.errors,
          receivedData: req.body,
          path: req.path,
          method: req.method
        });
        res.status(400).json({
          error: 'Données invalides',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};
