import axios from 'axios';
import logger from '@/utils/logger';
import { getCommonHeaders } from '@/utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import { API_CONFIG } from '../config/api';

interface VPSMetrics {
  cpu_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  ram_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  disk_space: {
    unit: string;
    usage: Record<string, number>;
  };
  outgoing_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  incoming_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  uptime: {
    unit: string;
    usage: Record<string, number>;
  };
}

const vpsService = {
  getVPSInfo: async () => {
    const headers = await getCommonHeaders();
    const response = await axios.get(`${API_CONFIG.baseURL}/api/vps/info`, {
      headers,
      withCredentials: true,
    });
    logger.info('getVPSInfo', response.data);
    return response.data;
  },

  getVPSMetrics: async (dateFrom: Date, dateTo: Date) => {
    const headers = await getCommonHeaders();
    const response = await axios.get<VPSMetrics>(`${API_CONFIG.baseURL}/api/vps/metrics`, { 
      headers,
      withCredentials: true,
      params: {
        dateFrom: dateFrom.toISOString(),
        dateTo: dateTo.toISOString(),
      },
    });
    return response.data;
  },

  getVPSHistory: async () => {
    const headers = await getCommonHeaders();
    const response = await axios.get(`${API_CONFIG.baseURL}/api/vps/history`, {
      headers,
      withCredentials: true,
    });
    return response.data;
  },

  restartVPS: async () => {
    await fetchCsrfToken();
    const headers = await getCommonHeaders();
    headers['X-CSRF-Token'] = await fetchCsrfToken();
    const response = await axios.post(`${API_CONFIG.baseURL}/api/vps/restart`, {}, {
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      withCredentials: true,
    });
    return response.data;
  },
};

export default vpsService; 