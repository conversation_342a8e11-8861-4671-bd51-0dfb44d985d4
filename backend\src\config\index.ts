import dotenv from 'dotenv';
import path from 'path';
import { AppConfig } from './types';

// Détermine l'environnement
const NODE_ENV = process.env.NODE_ENV || 'development';

// Charge le fichier .env approprié
dotenv.config({
  path: path.resolve(__dirname, `../../.env.${NODE_ENV}`)
});

// Configuration de l'application
export const config: AppConfig = {
  env: NODE_ENV,
  isProduction: NODE_ENV === 'production',
  isDevelopment: NODE_ENV === 'development',

  // Server
  port: parseInt(process.env.PORT || '3001', 10),
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:5173',
  apiUrl: process.env.API_URL || (NODE_ENV === 'production' ? 'https://api.jobpartiel.fr' : 'https://dev-api.jobpartiel.fr'),

  // Database
  supabase: {
    url: process.env.SUPABASE_URL,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
  },

  // Email
  smtp: {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '465', 10),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    from: process.env.SMTP_FROM
  },

  // Security
  jwt: {
    secret: process.env.NODE_ENV === 'production'
      ? process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET must be defined in production') })()
      : process.env.JWT_SECRET || 'dev-jwt-secret',
    expiresIn: '1h',
    refreshSecret: process.env.NODE_ENV === 'production'
      ? process.env.JWT_REFRESH_SECRET || (() => { throw new Error('JWT_REFRESH_SECRET must be defined in production') })()
      : process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret',
    refreshExpiresIn: '30d',
    socketExpiresIn: '24h' // Ajout d'une durée d'expiration plus longue pour les sockets
  },
  password: {
    salt: process.env.NODE_ENV === 'production'
      ? process.env.PASSWORD_SALT || (() => { throw new Error('PASSWORD_SALT must be defined in production') })()
      : process.env.PASSWORD_SALT || 'dev-password-salt',
    expiryDays: parseInt(process.env.PASSWORD_EXPIRY_DAYS || '360', 10),
    warningDays: parseInt(process.env.PASSWORD_WARNING_DAYS || '20', 10)
  },

  // Rate Limiting - Configuration sécurisée
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: NODE_ENV === 'production' ? 50 : 100, // SÉCURITÉ: Limite réduite en production
    loginWindowMs: 60 * 60 * 1000, // 1 heure pour les tentatives de connexion
    loginMax: NODE_ENV === 'production' ? 5 : 10, // SÉCURITÉ: Limite réduite en production
    adminWindowMs: 60 * 60 * 1000, // 1 heure pour les routes admin
    adminMax: NODE_ENV === 'production' ? 10 : 50 // SÉCURITÉ: Très restrictif pour les routes admin
  },

  // Security Monitoring
  security: {
    maxFailedAttempts: 5,
    maxIPsPerUser: 5,
    maxUsersPerIP: 5,
    suspiciousCountries: process.env.SUSPICIOUS_COUNTRIES ?
      process.env.SUSPICIOUS_COUNTRIES.split(',') : [],
    locationChangeThreshold: parseInt(process.env.LOCATION_CHANGE_THRESHOLD || '500', 10),
    logRetentionDays: parseInt(process.env.LOG_RETENTION_DAYS || '30', 10),
    alertRetentionDays: parseInt(process.env.ALERT_RETENTION_DAYS || '7', 10)
  },

  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    tls: process.env.REDIS_TLS === 'true'
  },

  // Session
  session: {
    secret: process.env.NODE_ENV === 'production'
      ? process.env.SESSION_SECRET || (() => { throw new Error('SESSION_SECRET must be defined in production') })()
      : process.env.SESSION_SECRET || 'dev-session-secret'
  }
};

// Validation de la configuration
const validateConfig = () => {
  console.log('Validation de la configuration...');

  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'SMTP_HOST',
    'SMTP_USER',
    'SMTP_PASS',
    'JWT_SECRET',
    'PASSWORD_SALT',
    'INSEE_API_KEY'
  ];

  console.log('Variables d\'environnement chargées:', {
    NODE_ENV: process.env.NODE_ENV,
    hasSupabaseUrl: !!process.env.SUPABASE_URL,
    hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    hasJwtSecret: !!process.env.JWT_SECRET,
    frontendUrl: process.env.FRONTEND_URL,
    apiUrl: process.env.API_URL,
    hasInseeApiKey: !!process.env.INSEE_API_KEY,
    SUPABASE_URL: process.env.SUPABASE_URL,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET
  });

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('Variables d\'environnement manquantes:', { missingVars });
    throw new Error(
      `Variables d'environnement manquantes : ${missingVars.join(', ')}`
    );
  }

  console.log('Configuration validée avec succès');
};

// Valide la configuration au démarrage
validateConfig();

export default config;
