import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Box, Typography, CircularProgress, Grid, Tabs, Tab, Button, Avatar, IconButton, Chip, Collapse, AvatarGroup, Tooltip, Divider, Paper, Dialog, DialogTitle, DialogContent, DialogActions, TextField, InputAdornment, Alert } from '@mui/material';
import { styled } from '@mui/material/styles';
import { missionsApi, Mission, FilterParams as BaseFilterParams } from '../ToutesLesMissions/missionsApi';
import { notify } from '@/components/Notification';
import MissionCard from '../ToutesLesMissions/MissionCard';
import { FilterBar } from '../ToutesLesMissions/FilterBar/FilterBar';
import { CounterOfferModal, JobbeurCounterOfferModal } from '@/components/CounterOfferModals';
import { useSearchParams } from 'react-router-dom';
import logger from '@/utils/logger';
import ModalPortal from '@/components/ModalPortal';
import { motion } from 'framer-motion';
import UserProfileModal from '@/components/UserProfileModal';
import axios from 'axios';
import { API_CONFIG, API_URL } from '@/config/api';
import OnlineStatusDot from '@/components/OnlineStatusDot';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CommentsDialog from '../ToutesLesMissions/CommentsDialog';
import { AccessTime, ChatBubbleOutline } from '@mui/icons-material';
import FavoriteRoundedIcon from '@mui/icons-material/FavoriteRounded';
import ChatRoundedIcon from '@mui/icons-material/ChatRounded';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import EuroIcon from '@mui/icons-material/Euro';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import PendingIcon from '@mui/icons-material/Pending';
import FilterIcon from '@mui/icons-material/Filter';
import SearchOffIcon from '@mui/icons-material/SearchOff';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PetsIcon from '@mui/icons-material/Pets';
import GrassIcon from '@mui/icons-material/Grass';
import HandymanIcon from '@mui/icons-material/Handyman';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import RateReviewIcon from '@mui/icons-material/RateReview'; // Ajout de l'icône pour l'avis
import ContactInfoModal from '@/components/ContactInfoModal';
import ReviewModal from '@/components/ReviewModalPopupDepotAvis'; // Importer la modale d'avis
import { useNavigate } from 'react-router-dom';
import useReviews from '@/hooks/useReviews';
import { useAuth } from '@/contexts/AuthContext';
import { getCommonHeaders } from '@/utils/headers';

// Fonction pour nettoyer le HTML
const stripHtml = (html: string | undefined): string => {
  if (!html) return '';

  // Créer un élément div temporaire
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Récupérer le texte sans les balises HTML
  return tempDiv.textContent || tempDiv.innerText || '';
};

interface JobbeurProfile {
  nom?: string;
  prenom?: string;
  photo_url?: string;
  type_de_profil?: string;
  assurance_verifier?: boolean;
  date_inscription?: string;
  entreprise_verifier?: boolean;
  identite_verifier?: boolean;
  is_online?: boolean;
  mode_vacance?: boolean;
  profil_verifier?: boolean;
  fullProfile?: any;
  slug?: string; // Ajouter le slug pour identifier l'utilisateur
}

// Interface pour les éléments de l'historique des négociations
interface NegotiationHistoryItem {
  type: 'initial' | 'counter_client' | 'counter_jobbeur';
  date: string;
  montant: number;
  message: string;
  sender: 'client' | 'jobbeur';
  profile?: JobbeurProfile;
  time_slots?: { date: string; start: string; end: string }[];
  previousAmount?: number;
}

interface Proposal {
  id: string;
  mission: Mission;
  statut: 'en_attente' | 'acceptée' | 'refusée' | 'contre_offre' | 'contre_offre_jobbeur';
  montant_propose: number;
  message: string;
  created_at: string;
  updated_at: string;  // Maintenant obligatoire car toujours présent
  montant_contre_offre?: number;
  message_contre_offre?: string;
  date_contre_offre?: string;
  montant_contre_offre_jobbeur?: number;
  message_contre_offre_jobbeur?: string;
  date_contre_offre_jobbeur?: string;
  jobbeur_profile?: JobbeurProfile;
  jobbeur_id?: string;
  mission_id?: string;
  time_slots?: { date: string; start: string; end: string }[];
  payment_status?: 'pending' | 'completed' | 'manual'; // Statut du paiement: en attente, complété, ou effectué manuellement
  payment_date?: string; // Date du paiement
  montant_paiement?: number; // Montant payé
}

// Extension du type FilterParams pour inclure missionId
interface FilterParams extends BaseFilterParams {
  missionId?: string;
}

const Container = styled(Box)(() => ({
  padding: 0,
  backgroundColor: 'white'
}));

const LoadingContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '32px',
});

const NoOffresBox = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '32px',
  backgroundColor: 'white',
  borderRadius: '16px',
  textAlign: 'center',
});

const StyledTabs = styled(Tabs)({
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF6B2C',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    '@media (max-width: 600px)': {
      flexDirection: 'row',
      justifyContent: 'space-between',
    }
  },
  '@media (max-width: 600px)': {
    minHeight: '48px',
  }
});

const StyledTab = styled(Tab)({
  textTransform: 'none',
  fontWeight: 'bold',
  fontSize: '1rem',
  color: '#666',
  '&.Mui-selected': {
    color: '#FF6B2C',
  },
  '&:hover': {
    color: '#FF965E',
    transition: 'color 0.3s ease',
  },
  padding: '16px 24px',
  '@media (max-width: 600px)': {
    padding: '8px 12px',
    minHeight: '48px',
    fontSize: '0.875rem',
    flex: 1,
    minWidth: 'auto',
    '& .MuiTab-wrapper': {
      flexDirection: 'row'
    }
  }
});

// Composant pour le point d'interrogation avec modale
interface InfoTooltipProps {
  title: string;
  content: React.ReactNode;
}

// Composant pour la ligne de connexion entre les offres
const OfferConnector: React.FC<{ isLastItem: boolean }> = ({ isLastItem }) => {
  if (isLastItem) return null;

  return (
    <Box sx={{
      position: 'relative',
      height: '70px',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      my: 0,
      '@keyframes pulse': {
        '0%': {
          opacity: 0.7,
        },
        '50%': {
          opacity: 1,
        },
        '100%': {
          opacity: 0.7,
        },
      },
    }}>
      {/* Ligne pointillée verticale */}
      <Box sx={{
        position: 'absolute',
        top: '0',
        height: '100%',
        width: '2px',
        background: 'linear-gradient(to bottom, #FF6B2C 50%, transparent 50%)',
        backgroundSize: '4px 4px',
        opacity: 0.7,
        animation: 'pulse 2s infinite'
      }} />

      {/* Cercle central */}
      <Box sx={{
        position: 'absolute',
        top: '50%',
        transform: 'translateY(-50%)',
        width: '28px',
        height: '28px',
        borderRadius: '50%',
        backgroundColor: '#FFF8F3',
        border: '2px solid #FF6B2C',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        boxShadow: '0 0 0 4px rgba(255, 107, 44, 0.1)',
        zIndex: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-50%) scale(1.1)',
          boxShadow: '0 0 0 6px rgba(255, 107, 44, 0.15)'
        }
      }}>
        <Box sx={{
          width: '10px',
          height: '10px',
          borderRadius: '50%',
          backgroundColor: '#FF6B2C',
          animation: 'pulse 2s infinite'
        }} />
      </Box>

      {/* Petits cercles décoratifs */}
      <Box sx={{
        position: 'absolute',
        top: '25%',
        left: 'calc(50% + 20px)',
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: '#FFE4BA',
        border: '1px solid #FF6B2C',
        opacity: 0.5
      }} />

      <Box sx={{
        position: 'absolute',
        top: '70%',
        left: 'calc(50% - 25px)',
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        backgroundColor: '#FFE4BA',
        border: '1px solid #FF6B2C',
        opacity: 0.3
      }} />
    </Box>
  );
};

const InfoTooltip: React.FC<InfoTooltipProps> = ({ title, content }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const ModalContent = styled(motion.div)(() => ({
    backgroundColor: '#FFF8F3',
    borderRadius: 8,
    padding: 24,
    maxWidth: '700px',
    width: '90%',
    position: 'relative',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
    border: `1px solid #FF6B2C`,
    maxHeight: '80vh',
    display: 'flex',
    flexDirection: 'column'
  }));

  const ModalHeader = styled(Box)({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  });

  const ModalTitle = styled(Typography)({
    fontWeight: 600,
    color: '#FF6B2C',
  });

  const ModalBody = styled(Box)({
    overflowY: 'auto',
    paddingRight: 4,
    '&::-webkit-scrollbar': {
      width: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f1f1f1',
      borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
      background: '#FF965E',
      borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: '#FF6B2C',
    },
  });

  return (
    <>
      <Box
        component="span"
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          cursor: 'pointer',
          ml: 0.5,
          color: '#FF6B2C',
          '&:hover': { opacity: 0.8 }
        }}
        onClick={handleOpenModal}
      >
        <HelpOutlineIcon sx={{ fontSize: 18 }} />
      </Box>

      <ModalPortal isOpen={isModalOpen} onBackdropClick={handleCloseModal}>
        <ModalContent
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2 }}
        >
          <ModalHeader>
            <ModalTitle variant="h6">{title}</ModalTitle>
            <IconButton
              onClick={handleCloseModal}
              size="small"
              sx={{ color: '#FF6B2C' }}
            >
              <CloseIcon />
            </IconButton>
          </ModalHeader>

          <ModalBody>
            {typeof content === 'string' ? (
              <Typography variant="body1">{content}</Typography>
            ) : (
              content
            )}
          </ModalBody>
        </ModalContent>
      </ModalPortal>
    </>
  );
};

const StyledTooltip = styled(Tooltip)(() => ({
  '& .MuiTooltip-tooltip': {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: 'white',
    padding: '8px 12px',
    borderRadius: '8px',
    fontSize: '0.875rem',
    fontWeight: 500,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: '100%',
      left: '50%',
      transform: 'translateX(-50%)',
      borderWidth: '6px',
      borderStyle: 'solid',
      borderColor: 'rgba(0, 0, 0, 0.8) transparent transparent transparent'
    }
  }
}));

// Nouveaux composants styled pour le header amélioré
interface MissionHeaderProps {
  missionId: string;
}

// Fonction pour générer une couleur cohérente basée sur l'ID de la mission
const getMissionColor = (missionId: string): string => {
  // Palette de couleurs orange fidèle à l'identité du site
  const colors = [
    '#FF6B2C', // Orange principal du site
    '#FF7D45', // Orange principal légèrement plus clair
    '#FF8B5A', // Orange doux
    '#FF7A35', // Orange secondaire du site
    '#FF8F4F', // Orange-corail
    '#FF7947', // Orange mandarine
    '#FF6F36', // Orange vif
    '#FF8352', // Orange pastel
  ];

  // Utiliser les caractères de l'ID pour générer un index stable
  let sum = 0;
  for (let i = 0; i < missionId.length; i++) {
    sum += missionId.charCodeAt(i);
  }

  // Récupérer un index basé sur la somme des codes de caractères
  const colorIndex = sum % colors.length;
  return colors[colorIndex];
};

// Modifié pour empêcher la prop missionId d'être transmise au DOM
const MissionHeaderContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'missionId',
})<MissionHeaderProps>(({ missionId }) => ({
  backgroundColor: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 24px rgba(255, 107, 44, 0.08)',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  position: 'relative',
  border: '1px solid rgba(255, 228, 186, 0.5)',
  '&:hover': {
    boxShadow: '0 12px 28px rgba(255, 107, 44, 0.12)',
    transform: 'translateY(-2px)',
  },
  paddingTop: '44px',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '44px',
    zIndex: 10,
    backgroundColor: getMissionColor(missionId)
  }
}));

// Composant pour le titre dans le bandeau sur mobile
const MissionMobileHeader = styled(Box)(() => ({
  display: 'flex', // Toujours visible
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  height: '44px',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '0 12px',
  zIndex: 20,
  color: 'white',
  fontWeight: 600,
  fontSize: '14px',
  textShadow: '0 1px 2px rgba(0, 0, 0, 0.15)',
  overflow: 'hidden',
  '& .mission-title': {
    fontWeight: 700,
    fontSize: '16px',
    letterSpacing: '0.2px',
    textShadow: '0 1px 3px rgba(0, 0, 0, 0.3)',
    flex: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    paddingRight: '10px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  },
  '& .mission-location': {
    fontSize: '13px',
    fontWeight: 600,
    background: 'rgba(0, 0, 0, 0.2)',
    padding: '3px 10px',
    borderRadius: '12px',
    whiteSpace: 'nowrap',
    boxShadow: '0 1px 2px rgba(0, 0, 0, 0.15)'
  },
  '& .category-icon': {
    fontSize: '20px',
    filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))'
  }
}));

const MissionHeaderTop = styled(Box)(() => ({
  padding: '20px 24px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  alignItems: 'stretch',
  position: 'relative',
  '& .avatar-group': {
    display: 'flex',
    padding: '0',
    marginTop: '0',
    justifyContent: 'flex-end',
  },
  '@media (min-width: 600px)': {
    alignItems: 'stretch'
  }
}));

const MissionTitleContainer = styled(Box)(() => ({
  flex: 1,
  marginBottom: 0,
  paddingTop: '8px',
}));

const MissionTitle = styled(Typography)(() => ({
  fontSize: '1.25rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '8px',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  borderBottom: '2px solid transparent',
  paddingBottom: '4px',
  transition: 'border-color 0.3s ease',
  '&:hover': {
    borderBottom: '2px solid #FF6B2C',
  },
  // Ajout du padding pour mobile
  '@media (max-width: 600px)': {
    paddingRight: '70px' // Espace pour la photo + marge
  },
  '@media (min-width: 601px) and (max-width: 1200px)': {
    paddingRight: '90px' // Espace pour la photo + marge
  }
}));

const LocationContainer = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  background: 'rgba(255, 107, 44, 0.05)',
  padding: '6px 12px',
  borderRadius: '20px',
  width: 'fit-content',
}));

const LocationText = styled(Typography)(() => ({
  color: '#718096',
  fontSize: '0.9rem',
  display: 'flex',
  alignItems: 'center',
  '&::before': {
    content: '""',
    display: 'inline-block',
    width: '16px',
    height: '16px',
    marginRight: '6px',
    backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23FF6B2C\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\'%3E%3C/path%3E%3Ccircle cx=\'12\' cy=\'10\' r=\'3\'%3E%3C/circle%3E%3C/svg%3E")',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
  }
}));

// Conteneur pour l'image de mission avec overlay
const MissionImageContainer = styled(Box)(() => ({
  position: 'relative',
  float: 'right',
  width: '180px',
  height: '130px',
  margin: '0 0 15px 20px',
  borderRadius: '8px',
  overflow: 'hidden',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  zIndex: 2, // Priorité plus élevée que le texte
  '&:hover .mission-image-overlay': {
    opacity: 1
  },
  // Taille et marges adaptées pour les écrans moyens
  '@media (min-width: 601px) and (max-width: 1024px)': {
    width: '150px',
    height: '110px',
    margin: '0 0 12px 15px',
  },
  // Taille et marges pour mobile
  '@media (max-width: 600px)': {
    width: '120px',
    height: '120px',
    margin: '0 0 10px 15px',
    borderRadius: '6px',
  },
  // Taille et marges pour très petits écrans
  '@media (max-width: 350px)': {
    width: '100px',
    height: '100px',
    margin: '0 0 8px 10px',
    borderRadius: '5px',
  }
}));

const MissionImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  cursor: 'pointer',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)'
  }
}));

const MissionImageOverlay = styled(Box)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.25)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 1,
  transition: 'opacity 0.3s ease',
  cursor: 'pointer',
  color: 'white',
  fontWeight: 600,
  fontSize: '0.9rem',
  zIndex: 3,
  '&:hover': {
    opacity: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    fontSize: '0.95rem',
    transition: 'all 1s ease',
  },
  '@media (max-width: 350px)': {
    padding: '6px',
    // fontSize: '0.8rem'
  }
}));

// Conteneur pour la description sur mobile et desktop
const MobileDescriptionContainer = styled(Box)(() => ({
  // Styles communs pour tous les appareils
  position: 'relative',
  padding: '18px 22px', // Légèrement réduit pour plus d'espace au texte
  fontSize: '14px',
  lineHeight: '1.6',
  color: '#333',
  backgroundColor: '#FFF8F3',
  borderRadius: '12px',
  border: '1px solid #FFE4BA',
  marginTop: '16px',
  marginBottom: '16px',
  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.06)',
  display: 'flow-root', // Assure le clearfix pour tous les écrans
  '& p': {
    margin: '0 0 12px 0',
    textAlign: 'justify',
    '&:last-child': {
      marginBottom: 0
    }
  },
  // Style pour les écrans très grands
  '@media (min-width: 1025px)': {
    minHeight: '240px',
  },
  // Style pour les écrans moyens
  '@media (min-width: 601px) and (max-width: 1024px)': {
    padding: '16px 18px',
    fontSize: '13.5px',
    lineHeight: '1.5',
  },
  // Style pour les très petits écrans
  '@media (max-width: 350px)': {
    padding: '14px 15px',
    fontSize: '13px',
    lineHeight: '1.4',
    marginTop: '12px',
    marginBottom: '12px',
  },
  // Style pour les paragraphes de Typography Material UI
  '& .MuiTypography-root': {
    position: 'relative',
    zIndex: 1, // Z-index inférieur à l'image
    wordWrap: 'break-word', // Permet aux mots longs de se casser si nécessaire
    // Ajustement de la taille du texte pour les écrans moyens
    '@media (min-width: 601px) and (max-width: 1024px)': {
      fontSize: '13.5px',
    },
    // Ajustement de la taille du texte pour les très petits écrans
    '@media (max-width: 350px)': {
      fontSize: '13px',
    },
  },
  // Style spécifique pour mobile
  '@media (max-width: 100px)': {
    padding: '10px 15px',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#333',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: 0,
    boxShadow: 'none',
    marginTop: 0,
    marginBottom: 0,
    minHeight: 'auto', // Pas de hauteur minimale sur mobile
    '& p': {
      margin: '0 0 12px 0',
      textAlign: 'justify' // Améliore la mise en page du texte
    }
  }
}));

const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

const OffresPage = () => {
  const { user } = useAuth();
  const [hasReview, setHasReview] = useState<{ [key: string]: boolean }>({});
  const { checkReviewExists } = useReviews({ userId: user?.id || '' });
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // Récupérer les paramètres de l'URL
  const missionParam = searchParams.get('mission');
  const tabParam = searchParams.get('tab');

  const [activeTab, setActiveTab] = useState(tabParam ? parseInt(tabParam, 10) : 0);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterBarExpanded, setIsFilterBarExpanded] = useState(false);
  const loadingRef = useRef<HTMLDivElement | null>(null);

  // État pour stocker les profils jobbeurs récupérés
  const [jobbeurProfiles, setJobbeurProfiles] = useState<Record<string, JobbeurProfile>>({});
  const [loadingProfiles, setLoadingProfiles] = useState(false);

  // États pour les filtres
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [budgetFilters, setBudgetFilters] = useState<string[]>([]);
  const [paymentFilters, setPaymentFilters] = useState<string[]>([]);
  const [categoryFilters, setCategoryFilters] = useState<string[]>([]);
  const [subcategoryFilters, setSubcategoryFilters] = useState<string[]>([]);
  const [likedFilter, setLikedFilter] = useState<string[]>([]);

  // Nouveaux états pour les filtres de profil et de statut d'offre
  const [profileFilters, setProfileFilters] = useState<string[]>([]);
  const [offerStatusFilters, setOfferStatusFilters] = useState<string[]>([]);

  // État pour le tri
  const [sortBy, setSortBy] = useState('');

  // États pour les dialogues et modales
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'accept' | 'reject' | null>(null);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // État pour la modal de contre-offre
  const [counterOfferModalOpen, setCounterOfferModalOpen] = useState(false);

  // État pour la modal de contre-offre du jobbeur
  const [jobbeurCounterOfferModalOpen, setJobbeurCounterOfferModalOpen] = useState(false);

  const [selectedUserProfile, setSelectedUserProfile] = useState<any>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  // État pour gérer l'expansion des cartes
  const [expandedCards, setExpandedCards] = useState<string[]>([]);

  // Statistiques
  interface Stats {
    total: number;
    enAttente: number;
    acceptees: number;
    refusees: number;
    contreOffres: number;
    montantMoyen: number;
    montantTotal: number;
    sentProposals: number;
    receivedProposals: number;
  }

  const [stats, setStats] = useState<Stats>({
    total: 0,
    enAttente: 0,
    acceptees: 0,
    refusees: 0,
    contreOffres: 0,
    montantMoyen: 0,
    montantTotal: 0,
    sentProposals: 0,
    receivedProposals: 0
  });
  const [loadingStats, setLoadingStats] = useState(false);
  const [statsExpanded, setStatsExpanded] = useState(false);

  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [currentMissionId, setCurrentMissionId] = useState<string | null>(null);
  const [currentMission, setCurrentMission] = useState<Mission | null>(null);

  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isMissionModalOpen, setIsMissionModalOpen] = useState(false);

  // Nouvel état pour la modal des propositions de mission
  const [selectedMissionProposals, setSelectedMissionProposals] = useState<Proposal[]>([]);
  const [isMissionProposalsModalOpen, setIsMissionProposalsModalOpen] = useState(false);

  const [expandedNegotiations, setExpandedNegotiations] = useState<Record<string, boolean>>({});
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [proposalStatusFilter, setProposalStatusFilter] = useState<'all' | 'en_attente' | 'acceptée' | 'refusée' | 'contre_offre'>('all');

  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [selectedSender, setSelectedSender] = useState<{nom?: string, prenom?: string} | null>(null);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);

  // État pour la modale d'envoi d'informations de contact
  const [isContactInfoModalOpen, setIsContactInfoModalOpen] = useState(false);
  const [selectedJobbeurForContact, setSelectedJobbeurForContact] = useState<string | null>(null);

  // Nouveaux états pour le paiement manuel
  const [manualPaymentAmount, setManualPaymentAmount] = useState<string>('');

  // États pour la modale d'avis
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [reviewData, setReviewData] = useState<{ missionId: string; jobbeurId: string } | null>(null);
  const [selectedReview, setSelectedReview] = useState<any>(null);

  // Ajout d'un nouvel état pour stocker les informations des propriétaires de mission
  const [missionOwnerProfiles, setMissionOwnerProfiles] = useState<Record<string, any>>({});

  useEffect(() => {
    const checkReviews = async () => {
      if (!proposals) return;

      const reviewChecks: { [key: string]: boolean } = {};
      for (const proposal of proposals) {
        if (proposal.mission_id) {
          try {
            const response = await axios.get(
              `${API_CONFIG.baseURL}/api/reviews/check/${proposal.mission_id}`,
              {
                headers: await getCommonHeaders(),
                withCredentials: true
              }
            );
            reviewChecks[proposal.mission_id] = response.data.exists;
          } catch (error) {
            reviewChecks[proposal.mission_id] = false;
          }
        }
      }
      setHasReview(reviewChecks);
    };

    checkReviews();
  }, [proposals]);

  // Fonction pour basculer l'expansion d'une carte
  const toggleCardExpansion = (proposalId: string) => {
    setExpandedCards(prev => {
      const newExpandedCards = [...prev];
      if (newExpandedCards.includes(proposalId)) {
        return newExpandedCards.filter(id => id !== proposalId);
      } else {
        newExpandedCards.push(proposalId);
        return newExpandedCards;
      }
    });
  };

  // Fonction pour basculer l'expansion d'une carte de mission
  const toggleMissionCardExpansion = (missionId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    // Récupérer les propositions pour cette mission
    const missionProposals = Object.entries(groupProposalsByMission(proposals))
      .find(([id]) => id === missionId)?.[1] || [];

    // Ouvrir la modal avec les propositions de la mission
    setSelectedMissionProposals(missionProposals);
    setIsMissionProposalsModalOpen(true);
  };

  // Fonction pour fermer la modal des propositions de mission
  const handleCloseMissionProposalsModal = () => {
    setIsMissionProposalsModalOpen(false);
    setSelectedMissionProposals([]);
  };

  // Fonction pour récupérer les informations du jobbeur si elles sont manquantes
  const fetchMissingJobbeurProfiles = useCallback(async (proposals: Proposal[]) => {
    try {
      setLoadingProfiles(true);

      // Identifier les jobbeurs dont les profils sont manquants
      const missingProfileIds = proposals
        .filter(p => p.jobbeur_id && (!p.jobbeur_profile || !p.jobbeur_profile.prenom || !p.jobbeur_profile.nom))
        .map(p => p.jobbeur_id)
        .filter((id): id is string => id !== undefined);

      // Éliminer les doublons d'IDs
      const uniqueMissingProfileIds = [...new Set(missingProfileIds)];

      // Si aucun profil manquant, ne rien faire
      if (uniqueMissingProfileIds.length === 0) {
        setLoadingProfiles(false);
        return;
      }

      // logger.info('Récupération des profils jobbeurs manquants sur le backend :', {
      //   nombre: uniqueMissingProfileIds.length,
      //   ids: uniqueMissingProfileIds
      // });

      // Récupérer les profils manquants
      const profiles: Record<string, JobbeurProfile> = {};

      // Récupérer les profils un par un pour éviter les erreurs
      for (const jobbeurId of uniqueMissingProfileIds) {
        try {
          // Vérifier si le profil est déjà dans le cache
          if (jobbeurProfiles[jobbeurId]) {
            profiles[jobbeurId] = jobbeurProfiles[jobbeurId];
            // logger.info('Profil récupéré depuis le cache:', { profiles });
            continue;
          }

          // D'abord récupérer le slug de l'utilisateur
          const slugResponse = await axios.get(`/api/users/get-slug/${jobbeurId}`, API_CONFIG);

          if (slugResponse.data.success && slugResponse.data.slug) {
            // Ensuite récupérer le profil complet avec le slug
            const profileResponse = await axios.get(`/api/users/profil/${slugResponse.data.slug}`, API_CONFIG);

            if (profileResponse.data) {
              profiles[jobbeurId] = {
                nom: profileResponse.data.profil?.data?.nom || '',
                prenom: profileResponse.data.profil?.data?.prenom || '',
                photo_url: profileResponse.data.profil?.data?.photo_url || null,
                type_de_profil: profileResponse.data.profil?.data?.type_de_profil || 'particulier',
                assurance_verifier: profileResponse.data.assurance_verifier || false,
                date_inscription: profileResponse.data.date_inscription || '',
                entreprise_verifier: profileResponse.data.entreprise_verifier || false,
                identite_verifier: profileResponse.data.identite_verifier || false,
                is_online: profileResponse.data.is_online || false,
                mode_vacance: profileResponse.data.profil?.data?.mode_vacance || false,
                profil_verifier: profileResponse.data.profil_verifier || false,
                fullProfile: profileResponse.data,
                slug: slugResponse.data.slug
              };
            }
          }
        } catch (error) {
          logger.error(`Erreur lors de la récupération du profil pour l'ID ${jobbeurId}:`, error);
        }
      }

      // Mettre à jour l'état avec les profils récupérés
      setJobbeurProfiles(prev => ({ ...prev, ...profiles }));

      // Mettre à jour les propositions avec les profils récupérés
      setProposals(prevProposals =>
        prevProposals.map(proposal => {
          if (proposal.jobbeur_id && profiles[proposal.jobbeur_id]) {
            return {
              ...proposal,
              jobbeur_profile: {
                ...proposal.jobbeur_profile,
                ...profiles[proposal.jobbeur_id]
              }
            };
          }
          return proposal;
        })
      );

    } catch (error) {
      logger.error('Erreur lors de la récupération des profils jobbeurs:', error);
    } finally {
      setLoadingProfiles(false);
    }
  }, [jobbeurProfiles]);

  // Fonction pour récupérer les propositions avec tous les filtres
  const fetchProposals = useCallback(async (pageNumber: number, isLoadMore: boolean = false) => {
    try {
      const currentActiveTab = activeTab;
      const limit = 10;

      if (isLoadMore) {
        setLoadingMore(true);
      } else if (!loading) { // Ne pas définir loading si c'est déjà true
        setLoading(true);
      }

      // Construire l'objet des filtres
      const filters: FilterParams = {
        status: statusFilters.filter(s => s !== 'urgent').length > 0 ? statusFilters.filter(s => s !== 'urgent') : undefined,
        search: searchTerm || undefined,
        categories: categoryFilters.length > 0 ? categoryFilters : undefined,
        subcategories: subcategoryFilters.length > 0 ? subcategoryFilters : undefined,
        budget_types: budgetFilters.length > 0 ? budgetFilters : undefined,
        payment_methods: paymentFilters.length > 0 ? paymentFilters : undefined,
        profile_types: profileFilters.length > 0 ? profileFilters : undefined,
        offer_status: offerStatusFilters.length > 0 ? offerStatusFilters : undefined,
        sort_by: sortBy || undefined,
        is_urgent: statusFilters.includes('urgent') ? true : undefined
      };

      // Nettoyer les filtres undefined
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof FilterParams] === undefined) {
          delete filters[key as keyof FilterParams];
        }
      });

      // Ajouter le filtre de mission si présent
      if (missionParam && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(missionParam)) {
        filters.missionId = missionParam;
      }

      // Log des filtres
      // logger.info('Filtres envoyés à l\'API:', filters);

      // Appeler l'API appropriée
      const response = await (currentActiveTab === 0
        ? missionsApi.getMyProposals(pageNumber, limit, filters)
        : missionsApi.getReceivedProposals(pageNumber, limit, filters));

      let newProposals = response.data || [];

      // Récupérer les détails complets de chaque mission pour avoir les likes et commentaires à jour
      const updatedProposals = await Promise.all(
        newProposals.map(async (proposal) => {
          try {
            if (proposal.mission && proposal.mission.id) {
              const missionDetails = await missionsApi.getMissionDetails(proposal.mission.id);
              return {
                ...proposal,
                mission: missionDetails
              };
            }
          } catch (error) {
            logger.error(`Erreur lors de la récupération des détails de la mission ${proposal.mission?.id}:`, error);
          }
          return proposal;
        })
      );

      newProposals = updatedProposals.filter(Boolean);

      // S'assurer que les propriétés likes_count, comments_count et user_has_liked sont définies
      newProposals = newProposals.map(proposal => {
        if (proposal.mission) {
          return {
            ...proposal,
            mission: {
              ...proposal.mission,
              likes_count: proposal.mission.likes_count || 0,
              comments_count: proposal.mission.comments_count || 0,
              user_has_liked: proposal.mission.user_has_liked || false
            }
          };
        }
        return proposal;
      });

      // Log pour déboguer les données de profil
      // if (newProposals.length > 0) {
      //   logger.info('Détails du profil du propriétaire de la mission:', {
      //     user_profile: newProposals[0].mission?.user_profile,
      //     profil_verifier: newProposals[0].mission?.user_profile?.profil_verifier,
      //     mission_data: JSON.stringify(newProposals[0].mission)
      //   });
      // }

      // Pour les offres envoyées, récupérer les informations de vérification des propriétaires de mission
      if (currentActiveTab === 0) {
        // Récupérer les IDs des propriétaires de mission uniques
        const missionOwnerIds = [...new Set(newProposals
          .filter(p => p.mission?.user_id)
          .map(p => p.mission.user_id))];


        // Filtrer pour ne récupérer que les profils qui ne sont pas déjà en cache
        const missingOwnerIds = missionOwnerIds.filter(ownerId => !missionOwnerProfiles[ownerId]);

        if (missingOwnerIds.length > 0) {

          // Récupérer les informations de vérification pour chaque propriétaire
          const newOwnerProfiles: Record<string, any> = {};

          for (const ownerId of missingOwnerIds) {
            try {
              // Récupérer le slug de l'utilisateur
              const slugResponse = await axios.get(`/api/users/get-slug/${ownerId}`, API_CONFIG);

              if (slugResponse.data.success && slugResponse.data.slug) {
                // Récupérer le profil complet avec le slug
                const profileResponse = await axios.get(`/api/users/profil/${slugResponse.data.slug}`, API_CONFIG);

                // Stocker les informations dans le nouvel objet
                newOwnerProfiles[ownerId] = {
                  profil_verifier: profileResponse.data.profil_verifier,
                  identite_verifier: profileResponse.data.identite_verifier,
                  assurance_verifier: profileResponse.data.assurance_verifier,
                  entreprise_verifier: profileResponse.data.entreprise_verifier
                };
              }
            } catch (error) {
              logger.error(`Erreur lors de la récupération des informations de vérification pour l'ID ${ownerId}:`, error);
            }
          }

          // Mettre à jour l'état avec les nouveaux profils
          setMissionOwnerProfiles(prev => ({ ...prev, ...newOwnerProfiles }));

          // Ajouter les nouveaux profils à l'objet pour la mise à jour
          Object.assign(missionOwnerProfiles, newOwnerProfiles);
        }

        // Mettre à jour les propositions avec les informations de vérification (en utilisant le cache)
        for (let i = 0; i < newProposals.length; i++) {
          const ownerId = newProposals[i].mission?.user_id;
          if (ownerId && missionOwnerProfiles[ownerId] && newProposals[i].mission.user_profile) {
            // @ts-ignore - Nous savons que ces propriétés existent dans le type mais sont undefined
            newProposals[i].mission.user_profile.profil_verifier = missionOwnerProfiles[ownerId].profil_verifier;
            // @ts-ignore
            newProposals[i].mission.user_profile.identite_verifier = missionOwnerProfiles[ownerId].identite_verifier;
            // @ts-ignore
            newProposals[i].mission.user_profile.assurance_verifier = missionOwnerProfiles[ownerId].assurance_verifier;
            // @ts-ignore
            newProposals[i].mission.user_profile.entreprise_verifier = missionOwnerProfiles[ownerId].entreprise_verifier;
          }
        }
      }

      // Filtrer les propositions côté frontend
      newProposals = newProposals.filter(proposal => {
        // Vérifier si la mission existe (a plus que juste user_profile)
        const missionExists = proposal.mission && Object.keys(proposal.mission).length > 1;
        if (!missionExists) {
          // logger.info('Mission inexistante ou supprimée:', {
          //   proposalId: proposal.id
          // });
          return false;
        }

        // Filtre de budget
        if (budgetFilters.length > 0) {
          const budgetDefini = proposal.mission.budget_defini;

          // Logique différente selon l'onglet
          if (currentActiveTab === 1) { // Onglet "reçues"
            if (budgetFilters.includes('budget_defini') && !budgetDefini) {
              // logger.info('Proposition filtrée par budget (reçues):', {
              //   proposalId: proposal.id,
              //   budgetDefini,
              //   expectedBudget: 'budget_defini'
              // });
              return false;
            }
            if (budgetFilters.includes('budget_non_defini') && budgetDefini) {
              // logger.info('Proposition filtrée par budget (reçues):', {
              //   proposalId: proposal.id,
              //   budgetDefini,
              //   expectedBudget: 'budget_non_defini'
              // });
              return false;
            }
          } else { // Onglet "envoyées"
            const montantPropose = proposal.montant_propose;
            if (budgetFilters.includes('budget_defini') && (!budgetDefini || !montantPropose)) {
              // logger.info('Proposition filtrée par budget (envoyées):', {
              //   proposalId: proposal.id,
              //   budgetDefini,
              //   montantPropose,
              //   expectedBudget: 'budget_defini'
              // });
              return false;
            }
            if (budgetFilters.includes('budget_non_defini') && (budgetDefini || montantPropose)) {
              // logger.info('Proposition filtrée par budget (envoyées):', {
              //   proposalId: proposal.id,
              //   budgetDefini,
              //   montantPropose,
              //   expectedBudget: 'budget_non_defini'
              // });
              return false;
            }
          }
        }

        // Filtre de catégories
        if (categoryFilters.length > 0 || subcategoryFilters.length > 0) {
          const missionCategoryId = proposal.mission?.category_id;
          const missionSubcategoryId = proposal.mission?.subcategory_id;

          const matchesCategory = categoryFilters.length === 0 ||
            (missionCategoryId && categoryFilters.includes(missionCategoryId));

          const matchesSubcategory = subcategoryFilters.length === 0 ||
            (missionSubcategoryId && subcategoryFilters.includes(missionSubcategoryId));

          if (!matchesCategory && !matchesSubcategory) return false;
        }

        // Filtre de paiement
        if (paymentFilters.length > 0) {
          if (activeTab === 1) { // Onglet "envoyées"
            const paymentMethod = proposal.mission.payment_method;
            if (!paymentMethod) {
              // logger.info('Mission sans payment_method:', {
              //   proposalId: proposal.id
              // });
              return false;
            }

            const mappedFilter = paymentFilters.map(filter => {
              switch (filter) {
                case 'jobi': return 'jobi_only';
                case 'euro': return 'direct_only';
                case 'hybride': return 'both';
                default: return filter;
              }
            });

            // Ne garder que les propositions dont le payment_method correspond au filtre
            if (!mappedFilter.includes(paymentMethod)) {
              // logger.info('Proposition filtrée par payment_method:', {
              //   proposalId: proposal.id,
              //   paymentMethod,
              //   expectedMethods: mappedFilter
              // });
              return false;
            }
          }
        }

        return true;
      });

      // Trier les propositions
      const sortedProposals = [...newProposals].sort((a, b) => {
        if (sortBy) {
          switch (sortBy) {
            case 'montant_propose':
              return b.montant_propose - a.montant_propose;
            case 'date_contre_offre':
              return new Date(b.date_contre_offre || 0).getTime() - new Date(a.date_contre_offre || 0).getTime();
            case 'montant_contre_offre':
              return (b.montant_contre_offre || 0) - (a.montant_contre_offre || 0);
            case 'date_creation':
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            default:
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
        }
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

      // Déboguer les informations du jobbeur
      // logger.info('Propositions récupérées:', sortedProposals);

      if (isLoadMore) {
        setProposals(prev => [...prev, ...sortedProposals]);
      } else {
        setProposals(sortedProposals);
      }

      // Récupérer les profils jobbeurs manquants
      await fetchMissingJobbeurProfiles(sortedProposals);

      setHasMore(response.total > (pageNumber * limit));
      // logger.info('Offres chargées avec succès:', {
      //   onglet: currentActiveTab === 0 ? 'Offres Envoyées' : 'Offres Reçues',
      //   nombreOffres: newProposals.length,
      //   missionId: filters.missionId || 'aucun',
      //   total: response.total || 0
      // });

    } catch (error) {
      logger.error('Erreur lors du chargement des offres:', error);
      notify('Erreur lors de la récupération des offres', 'error');
      if (!isLoadMore) {
        setProposals([]);
      }
      setHasMore(false);
    } finally {
      if (isLoadMore) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  }, [activeTab, statusFilters, budgetFilters, paymentFilters, categoryFilters, subcategoryFilters, likedFilter, profileFilters, offerStatusFilters, sortBy, searchTerm, missionParam, missionOwnerProfiles]); // Retirer activeTab des dépendances

  // Gestion des filtres
  const toggleFilter = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees') => {
    setPage(1);
    switch (category) {
      case 'budget':
        if (budgetFilters.includes(filter)) {
          setBudgetFilters([]);
        } else {
          setBudgetFilters([filter]); // On ne met que le nouveau filtre
        }
        break;
      case 'status':
        setStatusFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'payment':
        setPaymentFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'category':
        setCategoryFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'subcategory':
        setSubcategoryFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'liked':
        setLikedFilter(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'profile':
        setProfileFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'offer_status':
        setOfferStatusFilters(prev => {
          const newFilters = prev.includes(filter)
            ? prev.filter(f => f !== filter)
            : [...prev, filter];
          return newFilters;
        });
        break;
      case 'sort':
        setSortBy(filter);
        break;
    }
  };

  const isFilterActive = (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees'): boolean => {
    switch (category) {
      case 'status':
        return statusFilters.includes(filter);
      case 'budget':
        return budgetFilters.includes(filter);
      case 'payment':
        return paymentFilters.includes(filter);
      case 'liked':
        return likedFilter.includes('liked');
      case 'profile':
        return profileFilters.includes(filter);
      case 'offer_status':
        return offerStatusFilters.includes(filter);
      case 'sort':
        return sortBy === filter;
      default:
        return false;
    }
  };

  // Effet pour charger les données initiales et lors des changements de filtres
  useEffect(() => {
    // Ne pas recharger si on est dans le chargement initial d'un changement d'onglet
    if (loading && !loadingMore) return;

    // Réinitialiser la pagination
    setPage(1);
    setHasMore(true);

    // Charger les données
    fetchProposals(1, false);
  }, [
    statusFilters,
    budgetFilters,
    paymentFilters,
    categoryFilters,
    subcategoryFilters,
    likedFilter,
    profileFilters,
    offerStatusFilters,
    sortBy,
    searchTerm,
    missionParam
  ]); // Retirer activeTab des dépendances

  // Effet pour gérer le changement d'onglet
  useEffect(() => {
    // Réinitialiser les filtres si on a un ID de mission dans l'URL
    if (missionParam) {
      resetAllFilters();
      return; // Sortir car resetAllFilters déclenchera l'autre effet
    }

    // Réinitialiser l'état pour le chargement initial
    setPage(1);
    setHasMore(true);
    setProposals([]);
    setLoading(true);

    // Charger les données
    fetchProposals(1, false);
  }, [activeTab]);

  // Effet pour ouvrir automatiquement la modal des propositions quand on arrive via une notification
  useEffect(() => {
    if (missionParam && !loading && proposals.length > 0) {
      // Récupérer les propositions pour cette mission
      const missionProposals = proposals.filter(p => p.mission_id === missionParam || p.mission.id === missionParam);

      if (missionProposals.length > 0) {
        // Ouvrir la modal avec les propositions de la mission
        setSelectedMissionProposals(missionProposals);
        setIsMissionProposalsModalOpen(true);
      }
    }
  }, [missionParam, loading, proposals]);

  // Effet pour l'intersection observer (chargement infini)
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '100px',
      threshold: 0.1,
    };

    const observer = new IntersectionObserver((entries) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loading && !loadingMore) {
        setPage(prevPage => {
          const nextPage = prevPage + 1;
          fetchProposals(nextPage, true);
          return nextPage;
        });
      }
    }, options);

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore]); // eslint-disable-line react-hooks/exhaustive-deps

  // Gestionnaires d'événements
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    // Ne pas appeler fetchProposals directement ici
    // L'effet qui surveille les changements de filtres se chargera de recharger les données
  };

  const handleCategoryFilterChange = (categories: string[], subcategories: string[]) => {
    setCategoryFilters(categories);
    setSubcategoryFilters(subcategories);
    // Ne pas appeler fetchProposals directement ici
    // L'effet qui surveille les changements de filtres se chargera de recharger les données
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('tab', newValue.toString());
      // Supprimer le paramètre mission lorsque l'utilisateur change d'onglet manuellement
      if (newParams.has('mission')) {
        newParams.delete('mission');
      }
      return newParams;
    });

    // Réinitialiser l'état pour le nouvel onglet
    setPage(1);
    setProposals([]);
    setHasMore(true);
    setLoading(true);

    // Les données seront chargées par l'effet qui surveille activeTab
  };

  const resetAllFilters = () => {
    // Réinitialiser tous les états de filtres
    setStatusFilters([]);
    setBudgetFilters([]);
    setPaymentFilters([]);
    setCategoryFilters([]);
    setSubcategoryFilters([]);
    setProfileFilters([]);
    setOfferStatusFilters([]);
    setSortBy('');
    setSearchTerm('');
    setPage(1);
    setHasMore(true);

    // Ne pas faire d'appel API directement ici
    // L'effet qui surveille les changements de filtres se chargera de recharger les données
  };

  // Nouvelle fonction pour gérer le clic sur un onglet, même si c'est l'onglet actif
  const handleTabClick = (tabIndex: number) => {
    // Si on clique sur l'onglet déjà actif
    if (tabIndex === activeTab) {
      // Vérifier si l'URL contient des paramètres supplémentaires autres que "tab"
      const currentParams = new URLSearchParams(window.location.search);
      const hasExtraParams = Array.from(currentParams.keys()).some(key => key !== 'tab');

      // Ne faire l'action que s'il y a des paramètres supplémentaires
      if (hasExtraParams) {
        // Réinitialiser l'URL en supprimant tous les paramètres sauf tab
        setSearchParams(prev => {
          const newParams = new URLSearchParams();
          newParams.set('tab', tabIndex.toString());
          return newParams;
        });

        // Réinitialiser tous les filtres
        resetAllFilters();

        // Forcer le rechargement des données
        setProposals([]);
        setLoading(true);
        fetchProposals(1, false);
      }
    }
  };

  const hasActiveFilters = statusFilters.length > 0 ||
                          budgetFilters.length > 0 ||
                          paymentFilters.length > 0 ||
                          categoryFilters.length > 0 ||
                          subcategoryFilters.length > 0 ||
                          likedFilter.length > 0 ||
                          searchTerm.length > 0 ||
                          profileFilters.length > 0 ||
                          offerStatusFilters.length > 0 ||
                          sortBy !== '';

  const getEmptyMessage = () => {
    // Si on a des filtres actifs - vérifier que les tableaux ne sont pas vides
    if (
      (statusFilters.length > 0) ||
      (budgetFilters.length > 0) ||
      (paymentFilters.length > 0) ||
      (categoryFilters.length > 0) ||
      (subcategoryFilters.length > 0) ||
      (likedFilter && likedFilter.length > 0) ||  // Vérifier que likedFilter est un tableau non vide
      (profileFilters.length > 0) ||
      (offerStatusFilters.length > 0) ||
      (searchTerm && searchTerm.trim() !== '')  // Vérifier que searchTerm n'est pas vide après suppression des espaces
    ) {
      // logger.info('Affichage du message d\'absence d\'offres avec filtres actifs : ' +
      //   JSON.stringify({
      //     statusFilters,
      //     budgetFilters,
      //     paymentFilters,
      //     categoryFilters,
      //     subcategoryFilters,
      //     likedFilter,
      //     profileFilters,
      //     offerStatusFilters,
      //     searchTerm
      //   })
      // );
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '20px',
          backgroundColor: '#FFF8F3',
          borderRadius: '12px',
          border: '1px solid #FFE4BA',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          margin: '20px 0'
        }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 'bold', mb: 1 }}>
            Aucune offre ne correspond à vos critères de recherche
          </Typography>
          <Typography variant="body1" sx={{ color: '#666', mb: 2 }}>
            Essayez d'ajuster vos filtres pour voir plus de résultats
          </Typography>
          <Button
            onClick={resetAllFilters}
            variant="contained"
            sx={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              fontWeight: 'bold',
              padding: '10px 20px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FF965E',
              }
            }}
          >
            Réinitialiser les filtres
          </Button>
        </Box>
      );
    }

    // Si aucun filtre n'est actif, afficher un message différent selon l'onglet
    // logger.info('Affichage du message d\'absence d\'offres sans filtres actifs');

    if (activeTab === 0) {
      // Onglet "Offres envoyées"
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '30px',
          backgroundColor: '#FFF8F3',
          borderRadius: '12px',
          border: '1px solid #FFE4BA',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          margin: '20px 0',
          mx: 'auto'
        }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 'bold', mb: 1 }}>
            Vous n'avez pas encore fait d'offres
          </Typography>
          <Typography variant="body1" sx={{ color: '#666', mb: 1 }}>
            Parcourez les missions disponibles et proposez vos services pour commencer à recevoir des réponses.
          </Typography>
          <Button
            component="a"
            href="/dashboard/missions?tab=all"
            variant="contained"
            sx={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              fontWeight: 'bold',
              padding: '10px 20px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FF965E',
              }
            }}
          >
            Voir les missions disponibles
          </Button>
        </Box>
      );
    } else {
      // Onglet "Offres reçues"
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '30px',
          backgroundColor: '#FFF8F3',
          borderRadius: '12px',
          border: '1px solid #FFE4BA',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          margin: '20px 0',
          mx: 'auto'
        }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 'bold', mb: 1 }}>
            Vous n'avez pas encore reçu d'offres
          </Typography>
          <Typography variant="body1" sx={{ color: '#666', mb: 1 }}>
            Pour recevoir des offres, publiez des missions et attendez que des jobbeurs vous contactent.
          </Typography>
          <Button
            component="a"
            href="/dashboard/missions/poster-une-mission"
            variant="contained"
            sx={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              fontWeight: 'bold',
              padding: '10px 20px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FF965E',
              }
            }}
          >
            Créer une mission
          </Button>
        </Box>
      );
    }
  };

  // Gestion des actions sur les propositions
  const handleProposalUpdate = async (updatedMissionFromCard?: Mission) => {
    try {
      // Si nous recevons une mission mise à jour directement du MissionCard (likes, commentaires)
      if (updatedMissionFromCard && selectedMission && updatedMissionFromCard.id === selectedMission.id) {
        // Mettre à jour la mission sélectionnée avec les nouvelles valeurs
        setSelectedMission({
          ...selectedMission,
          likes_count: updatedMissionFromCard.likes_count,
          comments_count: updatedMissionFromCard.comments_count,
          user_has_liked: updatedMissionFromCard.user_has_liked,
          user_has_recommended: updatedMissionFromCard.user_has_recommended
        });

        // Mettre également à jour la mission dans les propositions
        const updatedProposals = proposals.map(prop => {
          if (prop.mission_id === updatedMissionFromCard.id || prop.mission.id === updatedMissionFromCard.id) {
            return {
              ...prop,
              mission: {
                ...prop.mission,
                likes_count: updatedMissionFromCard.likes_count,
                comments_count: updatedMissionFromCard.comments_count,
                user_has_liked: updatedMissionFromCard.user_has_liked,
                user_has_recommended: updatedMissionFromCard.user_has_recommended
              }
            };
          }
          return prop;
        });

        setProposals(updatedProposals);

        // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
        if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0) {
          const missionId = selectedMissionProposals[0].mission.id;
          if (missionId === updatedMissionFromCard.id) {
            const updatedMissionProposals = selectedMissionProposals.map(prop => {
              return {
                ...prop,
                mission: {
                  ...prop.mission,
                  likes_count: updatedMissionFromCard.likes_count,
                  comments_count: updatedMissionFromCard.comments_count,
                  user_has_liked: updatedMissionFromCard.user_has_liked,
                  user_has_recommended: updatedMissionFromCard.user_has_recommended
                }
              };
            });
            setSelectedMissionProposals(updatedMissionProposals);
          }
        }

        return;
      }

      // Sinon, continuer avec la logique existante pour les autres mises à jour
      await fetchProposals(page);

      // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
      if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0) {
        const missionId = selectedMissionProposals[0].mission.id;

        try {
          // Récupérer les propositions à jour pour cette mission
          const response = await (activeTab === 0
            ? missionsApi.getMyProposals(1, 100, { missionId })
            : missionsApi.getReceivedProposals(1, 100, { missionId }));

          if (response.data && response.data.length > 0) {
            // Récupérer les détails complets de chaque mission pour avoir les likes et commentaires à jour
            const updatedProposals = await Promise.all(
              response.data.map(async (prop) => {
                try {
                  if (prop.mission && prop.mission.id) {
                    const missionDetails = await missionsApi.getMissionDetails(prop.mission.id);
                    return {
                      ...prop,
                      mission: missionDetails
                    };
                  }
                } catch (error) {
                  logger.error(`Erreur lors de la récupération des détails de la mission ${prop.mission?.id}:`, error);
                }
                return prop;
              })
            );

            const filteredProposals = updatedProposals.filter(Boolean);

            // Récupérer les profils jobbeurs manquants AVANT de mettre à jour les propositions
            await fetchMissingJobbeurProfiles(filteredProposals);

            // Maintenant, récupérer à nouveau les propositions pour s'assurer qu'elles ont les profils à jour
            const proposalsWithProfiles = filteredProposals.map(prop => {
              if (prop.jobbeur_id && jobbeurProfiles[prop.jobbeur_id]) {
                return {
                  ...prop,
                  jobbeur_profile: {
                    ...prop.jobbeur_profile,
                    ...jobbeurProfiles[prop.jobbeur_id]
                  }
                };
              }
              return prop;
            });

            // Mettre à jour les propositions dans la modal
            setSelectedMissionProposals(proposalsWithProfiles);
          }
        } catch (error) {
          logger.error('Erreur lors de la mise à jour des propositions dans la modal:', error);
        }
      }
    } catch (error) {
      notify('Erreur lors de la mise à jour des offres', 'error');
    }
  };

  const handleConfirmAction = (proposal: Proposal, action: 'accept' | 'reject') => {
    setSelectedProposal(proposal);
    setConfirmAction(action);
    setConfirmDialogOpen(true);
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
    setConfirmAction(null);
  };

  const handleConfirm = async () => {
    if (!selectedProposal || !confirmAction) return;

    setIsProcessing(true);
    try {
      if (confirmAction === 'accept') {
        // 1. Accepter l'offre sélectionnée
        await missionsApi.acceptProposal(selectedProposal.mission.id, selectedProposal.id);

        // 2. Récupérer toutes les autres offres en attente pour cette mission afin de les refuser
        const otherProposals = proposals.filter(
          p => p.mission_id === selectedProposal.mission_id &&
               p.id !== selectedProposal.id &&
               (p.statut === 'en_attente' || p.statut === 'contre_offre' || p.statut === 'contre_offre_jobbeur')
        );

        // 3. Refuser automatiquement toutes les autres offres
        for (const proposal of otherProposals) {
          await missionsApi.rejectProposal(proposal.mission.id, proposal.id);
        }

        notify('Proposition acceptée avec succès.', 'success');

        // 4. Ouvrir automatiquement la modale de partage de contact uniquement si l'utilisateur est le propriétaire de la mission
        if (selectedProposal.jobbeur_id && selectedProposal.mission.user_id === user?.id) {
          // Ouvrir automatiquement la modale de partage de contact
          setSelectedJobbeurForContact(selectedProposal.jobbeur_id);
          setIsContactInfoModalOpen(true);
          // Notification supplémentaire indiquant que le partage des informations est facultatif
          notify('Vous pouvez partager vos informations de contact avec le jobbeur sélectionné (facultatif)', 'info');
        }
      } else {
        await missionsApi.rejectProposal(selectedProposal.mission.id, selectedProposal.id);
        notify('Proposition refusée avec succès', 'success');
      }

      // Rafraîchir les propositions après l'action
      await fetchProposals(page);

      // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
      if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0) {
        const missionId = selectedProposal.mission_id || selectedProposal.mission.id;

        try {
          // Récupérer les propositions à jour pour cette mission
          const response = await (activeTab === 0
            ? missionsApi.getMyProposals(1, 100, { missionId })
            : missionsApi.getReceivedProposals(1, 100, { missionId }));

          if (response.data && response.data.length > 0) {
            // Récupérer les détails complets de chaque mission pour avoir les likes et commentaires à jour
            const updatedProposals = await Promise.all(
              response.data.map(async (prop) => {
                try {
                  if (prop.mission && prop.mission.id) {
                    const missionDetails = await missionsApi.getMissionDetails(prop.mission.id);
                    return {
                      ...prop,
                      mission: missionDetails
                    };
                  }
                } catch (error) {
                  logger.error(`Erreur lors de la récupération des détails de la mission ${prop.mission?.id}:`, error);
                }
                return prop;
              })
            );

            const filteredProposals = updatedProposals.filter(Boolean);

            // Récupérer les profils jobbeurs manquants AVANT de mettre à jour les propositions
            await fetchMissingJobbeurProfiles(filteredProposals);

            // Maintenant, récupérer à nouveau les propositions pour s'assurer qu'elles ont les profils à jour
            const proposalsWithProfiles = filteredProposals.map(prop => {
              if (prop.jobbeur_id && jobbeurProfiles[prop.jobbeur_id]) {
                return {
                  ...prop,
                  jobbeur_profile: {
                    ...prop.jobbeur_profile,
                    ...jobbeurProfiles[prop.jobbeur_id]
                  }
                };
              }
              return prop;
            });

            // Mettre à jour les propositions dans la modal
            setSelectedMissionProposals(proposalsWithProfiles);
          }
        } catch (error) {
          logger.error('Erreur lors de la mise à jour des propositions dans la modal:', error);
        }
      }
    } catch (error: any) {
      // Afficher un message d'erreur plus précis
      if (error.response && error.response.data && error.response.data.error) {
        notify(`Erreur: ${error.response.data.error}`, 'error');
      } else {
        notify(`Erreur lors de l'action sur la proposition: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, 'error');
      }
    } finally {
      setIsProcessing(false);
      setConfirmDialogOpen(false);
      setConfirmAction(null);
    }
  };

  const handleCounterOffer = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setCounterOfferModalOpen(true);
  };

  const handleCloseCounterOfferModal = () => {
    setCounterOfferModalOpen(false);
    setSelectedProposal(null);
  };

  const handleCounterOfferSubmit = () => {
    // Rafraîchir les propositions après l'envoi d'une contre-offre
    fetchProposals(page).then(async () => {
      // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
      if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0 && selectedProposal) {
        const missionId = selectedProposal.mission_id || selectedProposal.mission.id;

        try {
          // Récupérer les propositions à jour pour cette mission
          const response = await (activeTab === 0
            ? missionsApi.getMyProposals(1, 100, { missionId })
            : missionsApi.getReceivedProposals(1, 100, { missionId }));

          if (response.data && response.data.length > 0) {
            // Récupérer les détails complets de chaque mission pour avoir les likes et commentaires à jour
            const updatedProposals = await Promise.all(
              response.data.map(async (prop) => {
                try {
                  if (prop.mission && prop.mission.id) {
                    const missionDetails = await missionsApi.getMissionDetails(prop.mission.id);
                    return {
                      ...prop,
                      mission: missionDetails
                    };
                  }
                } catch (error) {
                  logger.error(`Erreur lors de la récupération des détails de la mission ${prop.mission?.id}:`, error);
                }
                return prop;
              })
            );

            const filteredProposals = updatedProposals.filter(Boolean);

            // Récupérer les profils jobbeurs manquants AVANT de mettre à jour les propositions
            await fetchMissingJobbeurProfiles(filteredProposals);

            // Maintenant, récupérer à nouveau les propositions pour s'assurer qu'elles ont les profils à jour
            const proposalsWithProfiles = filteredProposals.map(prop => {
              if (prop.jobbeur_id && jobbeurProfiles[prop.jobbeur_id]) {
                return {
                  ...prop,
                  jobbeur_profile: {
                    ...prop.jobbeur_profile,
                    ...jobbeurProfiles[prop.jobbeur_id]
                  }
                };
              }
              return prop;
            });

            // Mettre à jour les propositions dans la modal
            setSelectedMissionProposals(proposalsWithProfiles);
          }
        } catch (error) {
          logger.error('Erreur lors de la mise à jour des propositions dans la modal:', error);
        }
      }
    });
  };

  const handleJobbeurCounterOffer = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setJobbeurCounterOfferModalOpen(true);
  };

  const handleCloseJobbeurCounterOfferModal = () => {
    setJobbeurCounterOfferModalOpen(false);
    setSelectedProposal(null);
  };

  const handleOpenUserProfile = async (jobbeurId: string) => {
    try {
      // Vérifier si le profil est déjà dans le cache
      if (jobbeurProfiles[jobbeurId] && jobbeurProfiles[jobbeurId].fullProfile) {
        setSelectedUserProfile(jobbeurProfiles[jobbeurId].fullProfile);
        setIsProfileModalOpen(true);
        return;
      }

      // D'abord récupérer le slug de l'utilisateur
      const slugResponse = await axios.get(`/api/users/get-slug/${jobbeurId}`, API_CONFIG);
      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      // Ensuite récupérer le profil complet avec le slug
      const response = await axios.get(`/api/users/profil/${slugResponse.data.slug}`, API_CONFIG);
      if (response.data) {
        // Mettre à jour le cache avec le profil complet
        setJobbeurProfiles(prev => ({
          ...prev,
          [jobbeurId]: {
            ...prev[jobbeurId],
            fullProfile: response.data
          }
        }));

        setSelectedUserProfile(response.data);
        setIsProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du profil:', error);
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  // Fonction pour récupérer les statistiques
  const fetchStats = useCallback(async () => {
    try {
      setLoadingStats(true);

      // Construire l'objet des filtres
      const filters: FilterParams = {
        status: statusFilters.filter(s => s !== 'urgent').length > 0 ? statusFilters.filter(s => s !== 'urgent') : undefined,
        search: searchTerm || undefined,
        categories: categoryFilters.length > 0 ? categoryFilters : undefined,
        subcategories: subcategoryFilters.length > 0 ? subcategoryFilters : undefined,
        budget_types: budgetFilters.length > 0 ? budgetFilters : undefined,
        payment_methods: paymentFilters.length > 0 ? paymentFilters : undefined,
        profile_types: profileFilters.length > 0 ? profileFilters : undefined,
        offer_status: offerStatusFilters.length > 0 ? offerStatusFilters : undefined,
        sort_by: sortBy || undefined,
        is_urgent: statusFilters.includes('urgent') ? true : undefined
      };

      // Nettoyer les filtres undefined
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof FilterParams] === undefined) {
          delete filters[key as keyof FilterParams];
        }
      });

      // Ajouter le filtre de mission si présent
      if (missionParam && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(missionParam)) {
        filters.missionId = missionParam;
      }

      const statsData = await missionsApi.getProposalStats(activeTab === 0, filters);
      setStats(statsData);

      // logger.info('Statistiques récupérées avec succès:', statsData);
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques:', error);
    } finally {
      setLoadingStats(false);
    }
  }, [activeTab, statusFilters, budgetFilters, paymentFilters, categoryFilters, subcategoryFilters, profileFilters, offerStatusFilters, sortBy, searchTerm, missionParam]);

  // Effet pour charger les propositions au chargement de la page
  useEffect(() => {
    // Réinitialiser la page à 1 lors du changement d'onglet
    setPage(1);

    // Charger les propositions
    fetchProposals(1);

    // Charger les statistiques
    fetchStats();
  }, [activeTab, fetchProposals, fetchStats]);

  // Effet pour mettre à jour les statistiques lorsque les filtres changent
  useEffect(() => {
    fetchStats();
  }, [statusFilters, budgetFilters, paymentFilters, categoryFilters, subcategoryFilters, profileFilters, offerStatusFilters, sortBy, searchTerm, fetchStats]);

  // Calcul des statistiques (fonction de secours si l'API échoue)
  const getStats = useCallback(() => {
    // Toujours utiliser notre calcul local pour les statistiques
    // Commenté pour ignorer les statistiques de l'API
    // if (stats.total > 0) {
    //   return stats;
    // }

    // Calculer à partir des données locales
    const localStats = {
      total: proposals.length,
      // Calcul des offres en attente en fonction de l'onglet actif
      enAttente: activeTab === 0
        // Onglet "Offres envoyées" : en_attente + contre_offre (du client)
        ? proposals.filter(p => p.statut === 'en_attente' || p.statut === 'contre_offre').length
        // Onglet "Offres reçues" : en_attente + contre_offre_jobbeur (uniquement)
        : proposals.filter(p => p.statut === 'en_attente' || p.statut === 'contre_offre_jobbeur').length,
      acceptees: proposals.filter(p => p.statut === 'acceptée').length,
      refusees: proposals.filter(p => p.statut === 'refusée').length,
      // Calcul des contre-offres en fonction de l'onglet actif
      contreOffres: activeTab === 0
        // Onglet "Offres envoyées" : contre_offre_jobbeur (du jobbeur)
        ? proposals.filter(p => p.statut === 'contre_offre_jobbeur').length
        // Onglet "Offres reçues" : contre_offre + contre_offre_jobbeur
        : proposals.filter(p => p.statut === 'contre_offre' || p.statut === 'contre_offre_jobbeur').length,
      montantMoyen: proposals.length > 0
        ? Math.round(proposals.reduce((acc, p) => acc + p.montant_propose, 0) / proposals.length)
        : 0,
      montantTotal: proposals.length > 0
        ? proposals.reduce((acc, p) => acc + p.montant_propose, 0)
        : 0,
      sentProposals: activeTab === 0 ? proposals.length : 0,
      receivedProposals: activeTab === 1 ? proposals.length : 0
    };
    return localStats;
  }, [proposals, stats, activeTab]);

  // Fonction pour basculer l'expansion des statistiques
  const toggleStatsExpansion = () => {
    setStatsExpanded(prev => !prev);
  };

  const handleLike = async (mission: Mission) => {
    try {
      await missionsApi.likeMission(mission.id);

      // Mettre à jour la mission dans la liste des propositions
      const updatedMission = await missionsApi.getMissionDetails(mission.id);

      // Mettre à jour la proposition qui contient cette mission
      const updatedProposals = proposals.map(prop => {
        if (prop.mission_id === mission.id || prop.mission.id === mission.id) {
          return {
            ...prop,
            mission: {
              ...prop.mission,
              likes_count: updatedMission.likes_count || 0,
              comments_count: updatedMission.comments_count || 0,
              user_has_liked: updatedMission.user_has_liked || false
            }
          };
        }
        return prop;
      });

      setProposals(updatedProposals);

      // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
      if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0) {
        const missionId = selectedMissionProposals[0].mission.id;
        if (missionId === mission.id) {
          const updatedMissionProposals = selectedMissionProposals.map(prop => {
            return {
              ...prop,
              mission: {
                ...prop.mission,
                likes_count: updatedMission.likes_count || 0,
                comments_count: updatedMission.comments_count || 0,
                user_has_liked: updatedMission.user_has_liked || false
              }
            };
          });
          setSelectedMissionProposals(updatedMissionProposals);
        }
      }

      notify(mission.user_has_liked ? 'Like retiré' : 'Mission likée', 'success');
    } catch (error) {
      notify('Erreur lors du like de la mission', 'error');
    }
  };

  const handleOpenComments = (mission: Mission) => {
    setCurrentMissionId(mission.id);
    setCurrentMission(mission);
    setShowCommentDialog(true);
  };

  const handleCloseComments = () => {
    setShowCommentDialog(false);
    setCurrentMissionId(null);
  };

  const handleCommentUpdate = async () => {
    if (currentMissionId) {
      const updatedMission = await missionsApi.getMissionDetails(currentMissionId);

      // Mettre à jour la proposition qui contient cette mission
      const updatedProposals = proposals.map(prop => {
        if (prop.mission_id === currentMissionId || prop.mission.id === currentMissionId) {
          return {
            ...prop,
            mission: {
              ...prop.mission,
              likes_count: updatedMission.likes_count || 0,
              comments_count: updatedMission.comments_count || 0,
              user_has_liked: updatedMission.user_has_liked || false
            }
          };
        }
        return prop;
      });

      setProposals(updatedProposals);

      // Mettre à jour les propositions dans la modal ouverte si elle est ouverte
      if (isMissionProposalsModalOpen && selectedMissionProposals.length > 0) {
        const missionId = selectedMissionProposals[0].mission.id;
        if (missionId === currentMissionId) {
          const updatedMissionProposals = selectedMissionProposals.map(prop => {
            return {
              ...prop,
              mission: {
                ...prop.mission,
                likes_count: updatedMission.likes_count || 0,
                comments_count: updatedMission.comments_count || 0,
                user_has_liked: updatedMission.user_has_liked || false
              }
            };
          });
          setSelectedMissionProposals(updatedMissionProposals);
        }
      }

      setCurrentMission(updatedMission);
    }
  };

  // Fonction utilitaire pour calculer le dernier montant négocié
  const getLastNegotiatedAmount = (proposal: Proposal): number => {
    const history = getNegotiationHistory(proposal);
    if (history.length === 0) {
      return proposal.montant_propose;
    }

    // Trier l'historique par date (du plus récent au plus ancien)
    const sortedHistory = [...history].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    return sortedHistory[0].montant;
  };

  // Fonction pour obtenir l'historique des négociations trié par date
  const getNegotiationHistory = (proposal: Proposal) => {
    const history: NegotiationHistoryItem[] = [
      {
        type: 'initial',
        date: proposal.created_at,
        montant: proposal.montant_propose,
        message: proposal.message,
        sender: 'jobbeur',
        profile: proposal.jobbeur_profile,
        time_slots: proposal.time_slots
      }
    ];

    if (proposal.montant_contre_offre && proposal.date_contre_offre) {
      history.push({
        type: 'counter_client',
        date: proposal.date_contre_offre,
        montant: proposal.montant_contre_offre,
        message: proposal.message_contre_offre || '',
        sender: 'client',
        profile: proposal.mission.user_profile,
        previousAmount: proposal.montant_propose
      });
    }

    if (proposal.montant_contre_offre_jobbeur && proposal.date_contre_offre_jobbeur) {
      history.push({
        type: 'counter_jobbeur',
        date: proposal.date_contre_offre_jobbeur,
        montant: proposal.montant_contre_offre_jobbeur,
        message: proposal.message_contre_offre_jobbeur || '',
        sender: 'jobbeur',
        profile: proposal.jobbeur_profile,
        previousAmount: proposal.montant_contre_offre || proposal.montant_propose
      });
    }

    // Trier par date (du plus ancien au plus récent)
    return history.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const groupProposalsByMission = (proposals: Proposal[]): { [key: string]: Proposal[] } => {
    const groupedProposals: { [key: string]: Proposal[] } = {};

    proposals.forEach(proposal => {
      const missionId = proposal.mission.id;
      if (!groupedProposals[missionId]) {
        groupedProposals[missionId] = [];
      }
      groupedProposals[missionId].push(proposal);
    });

    return groupedProposals;
  };

  const handleOpenMissionModal = (mission: Mission) => {
    setSelectedMission(mission);
    setIsMissionModalOpen(true);
  };

  const handleCloseMissionModal = () => {
    setIsMissionModalOpen(false);
    setSelectedMission(null);
  };

  // Fonction pour basculer l'état d'expansion de l'historique des négociations
  const toggleNegotiationHistory = (proposalId: string) => {
    setExpandedNegotiations(prev => ({
      ...prev,
      [proposalId]: !prev[proposalId]
    }));
  };

  // Fonction pour compter les différents états des propositions
  const countProposalStatuses = (proposals: Proposal[]) => {
    const counts = {
      en_attente: 0,
      acceptée: 0,
      refusée: 0,
      contre_offre: 0,
      contre_offre_jobbeur: 0
    };

    proposals.forEach(proposal => {
      counts[proposal.statut as keyof typeof counts]++;
    });

    return counts;
  };

  // Fonction pour obtenir le dernier message d'une proposition
  const getLastMessage = (proposal: Proposal): { message: string, isInitial: boolean, date: string } => {
    const history = getNegotiationHistory(proposal);
    if (history.length === 0) {
      return { message: '', isInitial: true, date: proposal.created_at };
    }

    // Trier l'historique par date (du plus récent au plus ancien)
    const sortedHistory = [...history].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    const lastItem = sortedHistory[0];
    return {
      message: lastItem.message,
      isInitial: lastItem.type === 'initial',
      date: lastItem.date
    };
  };

  // Fonction pour ouvrir la modale d'envoi d'informations de contact
  const handleOpenContactInfoModal = (proposal: Proposal) => {
    if (proposal.jobbeur_id && proposal.mission?.user_id === user?.id) {
      setSelectedJobbeurForContact(proposal.jobbeur_id);
      setSelectedProposal(proposal);
      setIsContactInfoModalOpen(true);
    } else if (!proposal.jobbeur_id) {
      notify('Impossible de récupérer les informations du jobbeur', 'error');
    } else {
      notify('Seul le propriétaire de la mission peut partager ses informations de contact', 'warning');
    }
  };

  // Fonction pour fermer la modale d'envoi d'informations de contact
  const handleCloseContactInfoModal = () => {
    setIsContactInfoModalOpen(false);
    setSelectedJobbeurForContact(null);
    setSelectedProposal(null);
  };

  // Ajouter cette fonction pour filtrer les propositions en fonction du statut sélectionné
  const filterProposalsByStatus = (proposals: Proposal[], statusFilter: string): Proposal[] => {
    if (statusFilter === 'all') {
      // Pour le filtre "En cours", inclure uniquement les propositions qui ne sont ni acceptées ni refusées
      return proposals.filter(proposal => proposal.statut !== 'acceptée' && proposal.statut !== 'refusée');
    }

    if (statusFilter === 'en_attente') {
      // Pour le filtre "Attention requise", inclure uniquement les offres qui nécessitent une réponse
      return proposals.filter(proposal => {
        if (activeTab === 0) {
          // Dans l'onglet "Offres Envoyées", inclure uniquement les offres initiales en attente
          // et les contre-offres du client qui nécessitent une réponse du jobbeur
          return proposal.statut === 'en_attente' || proposal.statut === 'contre_offre';
        } else {
          // Dans l'onglet "Offres Reçues", inclure uniquement les contre-offres du jobbeur
          // qui nécessitent une réponse du client (l'utilisateur actuel)
          return proposal.statut === 'contre_offre_jobbeur';
        }
      });
    }

    // Pour les autres filtres (acceptée, refusée), filtrer normalement
    return proposals.filter(proposal => proposal.statut === statusFilter);
  };

  // Nouveaux composants styled pour le design amélioré
  const MissionActions = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
    padding: '12px 24px',
    borderTop: '1px solid rgba(255, 228, 186, 0.5)',
    backgroundColor: 'rgba(255, 248, 243, 0.5)',
    borderBottomLeftRadius: '16px',
    borderBottomRightRadius: '16px',
  }));

  const ChipsContainer = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    gap: '8px',
    marginTop: '12px',
  }));

  const StyledChip = styled(Chip)(({ color }) => ({
    borderRadius: '20px',
    fontWeight: 600,
    fontSize: '0.75rem',
    height: '24px',
    backgroundColor: color === 'primary'
      ? 'rgba(255, 107, 44, 0.1)'
      : color === 'secondary'
        ? 'rgba(25, 118, 210, 0.1)'
        : color === 'success'
          ? 'rgba(46, 125, 50, 0.1)'
          : 'rgba(255, 228, 186, 0.3)',
    color: color === 'primary'
      ? '#FF6B2C'
      : color === 'secondary'
        ? '#1976D2'
        : color === 'success'
          ? '#2E7D32'
          : '#FF7A35',
    border: `1px solid ${color === 'primary'
      ? 'rgba(255, 107, 44, 0.3)'
      : color === 'secondary'
        ? 'rgba(25, 118, 210, 0.3)'
        : color === 'success'
          ? 'rgba(46, 125, 50, 0.3)'
          : 'rgba(255, 228, 186, 0.5)'}`,
    '& .MuiChip-label': {
      padding: '0 10px',
    }
  }));

  // Interface pour le StatusIndicator
  interface StatusIndicatorProps {
    status: 'en_attente' | 'acceptée' | 'refusée' | 'contre_offre' | 'contre_offre_jobbeur';
  }

  const StatusIndicator = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'status',
  })<StatusIndicatorProps>(({ status }) => ({
    display: 'flex',
    alignItems: 'center',
    padding: '8px 16px',
    borderRadius: '8px',
    marginTop: 0,
    fontWeight: 700,
    fontSize: '0.875rem',
    justifyContent: 'center',
    '& .MuiSvgIcon-root': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    ...(status === 'en_attente' && {
      backgroundColor: activeTab === 1 ? '#1976D2' : 'rgba(255, 107, 44, 0.1)',
      color: activeTab === 1 ? 'white' : '#FF6B2C',
      border: activeTab === 1 ? '1px solid #1976D2' : '1px solid #FF6B2C',
      '& .MuiSvgIcon-root': {
        color: activeTab === 1 ? 'white' : '#FF6B2C'
      }
    }),
    ...(status === 'acceptée' && {
      backgroundColor: '#E8F5E9',
      color: '#10B981',
      border: '1px solid #10B981',
    }),
    ...(status === 'refusée' && {
      backgroundColor: 'red',
      color: 'white',
      border: '1px solid red',
    }),
    ...((status === 'contre_offre') && {
      backgroundColor: activeTab === 1 ? 'rgba(255, 107, 44, 0.1)' : '#1976D2',
      color: activeTab === 1 ? '#FF6B2C' : 'white',
      border: activeTab === 1 ? '1px solid #FF6B2C' : '1px solid #1976D2',
      '& .MuiSvgIcon-root': {
        color: activeTab === 1 ? '#FF6B2C' : 'white'
      }
    }),
    ...((status === 'contre_offre_jobbeur') && {
      backgroundColor: activeTab === 1 ? '#1976D2' : 'rgba(255, 107, 44, 0.1)',
      color: activeTab === 1 ? 'white' : '#FF6B2C',
      border: activeTab === 1 ? '1px solid #1976D2' : '1px solid #FF6B2C',
      '& .MuiSvgIcon-root': {
        color: activeTab === 1 ? 'white' : '#FF6B2C'
      }
    })
  }));

  const ActionButtonsContainer = styled(Box)(() => ({
    display: 'flex',
    gap: '12px',
    justifyContent: 'flex-end',
    alignItems: 'center',
  }));

  const EnhancedActionButton = styled(motion.button)({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '6px',
    padding: '6px 12px',
    borderRadius: '20px',
    border: 'none',
    background: 'rgba(255, 107, 44, 0.1)',
    color: '#FF6B2C',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    position: 'relative',
    fontWeight: 600,
    fontSize: '0.75rem',
    '&:hover': {
      background: 'rgba(255, 107, 44, 0.15)',
      transform: 'translateY(-1px)'
    }
  });

  const AvatarContainer = styled(Box)(() => ({
    position: 'relative',
    top: 'auto',
    right: 'auto',
    zIndex: 2,
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: '12px',
    '& .MuiAvatarGroup-root': {
      justifyContent: 'flex-end'
    }
  }));

  // Fonction pour obtenir l'icône de catégorie
  const getCategoryIcon = (categoryName?: string) => {
    if (!categoryName) return <MoreHorizIcon />;

    const lowerCaseCat = categoryName.toLowerCase();

    if (lowerCaseCat.includes('animal') || lowerCaseCat.includes('animaux') || lowerCaseCat.includes('pet')) {
      return <PetsIcon />;
    } else if (lowerCaseCat.includes('jardin') || lowerCaseCat.includes('garden') || lowerCaseCat.includes('extérieur') || lowerCaseCat.includes('ménage')) {
      return <GrassIcon />;
    } else if (lowerCaseCat.includes('bricol') || lowerCaseCat.includes('réparat') || lowerCaseCat.includes('travaux')) {
      return <HandymanIcon />;
    }

    return <MoreHorizIcon />;
  };

  // Fonction pour gérer la redirection vers la page Jobi pour effectuer un paiement
  const handlePaymentRedirect = (proposal: Proposal) => {
    const destinataire = proposal.mission.user_profile;

    if (!destinataire?.slug) {
      notify('Impossible de trouver le destinataire du paiement', 'error');
      return;
    }

    // Stocker les informations de proposition pour les passer à la page de paiement
    sessionStorage.setItem('payment_proposal', JSON.stringify({
      id: proposal.id,
      destinataire_usertag: destinataire.slug,
      destinataire_nom: destinataire.nom,
      destinataire_prenom: destinataire.prenom,
      montant: proposal.montant_propose,
      mission_id: proposal.mission_id || proposal.mission.id,
      mission_title: proposal.mission.titre,
      // Ajouter un callback pour pouvoir rafraîchir la page après le paiement
      return_url: window.location.href,
      current_tab: activeTab
    }));

    // Rediriger vers la page de paiement Jobi
    window.location.href = '/dashboard/jobi?tab=transfer&payment=true';
  };

  // Ajouter un effect pour vérifier si on revient d'un paiement et rafraîchir les données
  useEffect(() => {
    // Vérifier si l'utilisateur revient d'un paiement
    const paymentCompleted = sessionStorage.getItem('payment_completed');
    if (paymentCompleted) {
      try {
        const paymentData = JSON.parse(paymentCompleted);
        // Rafraîchir la proposition spécifique
        if (paymentData.proposalId && paymentData.missionId) {
          // Récupérer la proposition mise à jour
          missionsApi.getProposalById(paymentData.missionId, paymentData.proposalId)
            .then(updatedProposal => {
              if (updatedProposal) {
                // Mettre à jour la proposition dans l'état local
                setProposals(prevProposals => {
                  return prevProposals.map(prop => {
                    if (prop.id === paymentData.proposalId) {
                      return updatedProposal;
                    }
                    return prop;
                  });
                });

                // Mettre à jour les propositions de la mission sélectionnée
                if (selectedMission && selectedMissionProposals.some(p => p.id === paymentData.proposalId)) {
                  setSelectedMissionProposals(prevProposals => {
                    return prevProposals.map(prop => {
                      if (prop.id === paymentData.proposalId) {
                        return updatedProposal;
                      }
                      return prop;
                    });
                  });
                }

                notify('Paiement effectué avec succès', 'success');
              }
            })
            .catch(error => {
              logger.error('Erreur lors de la récupération de la proposition mise à jour:', error);
            })
            .finally(() => {
              // Rafraîchir toutes les propositions pour être sûr
              fetchProposals(page);
            });
        }
      } catch (error) {
        logger.error('Erreur lors du traitement des données de paiement terminé:', error);
      }

      // Supprimer l'indicateur de paiement terminé
      sessionStorage.removeItem('payment_completed');
    }
  }, []);

  // Fonction pour ouvrir un modal de confirmation de paiement manuel
  const [isManualPaymentModalOpen, setIsManualPaymentModalOpen] = useState<boolean>(false);
  const [proposalForManualPayment, setProposalForManualPayment] = useState<Proposal | null>(null);

  const openManualPaymentConfirmation = (proposal: Proposal) => {
    setProposalForManualPayment(proposal);
    setManualPaymentAmount(proposal.montant_propose.toString());
    setIsManualPaymentModalOpen(true);
  };

  const closeManualPaymentConfirmation = () => {
    setIsManualPaymentModalOpen(false);
    setProposalForManualPayment(null);
    setManualPaymentAmount('');
  };

  const confirmManualPayment = async () => {
    if (!proposalForManualPayment) return;

    try {
      // Convertir le montant en nombre
      const montantPaiement = parseFloat(manualPaymentAmount);
      if (isNaN(montantPaiement) || montantPaiement <= 0) {
        notify('Veuillez entrer un montant valide', 'error');
        return;
      }

      // Mise à jour du statut de paiement dans l'API
      const response = await missionsApi.updateProposalPaymentStatus(
        proposalForManualPayment.mission_id || proposalForManualPayment.mission.id,
        proposalForManualPayment.id,
        'manual',
        montantPaiement
      );

      if (response.success) {
        // Créer une version mise à jour de la proposition avec les nouvelles données
        const updatedProposal: Proposal = {
          ...proposalForManualPayment,
          payment_status: 'manual' as const,
          payment_date: new Date().toISOString(),
          montant_paiement: montantPaiement
        };

        // Mettre à jour la proposition dans l'état local
        setProposals(prevProposals => {
          return prevProposals.map(prop => {
            if (prop.id === proposalForManualPayment.id) {
              return updatedProposal;
            }
            return prop;
          });
        });

        // Utiliser handleProposalUpdate pour mettre à jour à la fois l'état local et la modal
        await handleProposalUpdate(proposalForManualPayment.mission);

        notify('Paiement marqué comme effectué manuellement', 'success');
        closeManualPaymentConfirmation();
      } else {
        notify('Erreur lors de la mise à jour du statut de paiement', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du statut de paiement:', error);
      notify('Erreur lors de la mise à jour du statut de paiement', 'error');
    }
  };

  const handleReviewClick = async (missionId: string) => {
    try {
      const proposal = proposals?.find(p => p.mission_id === missionId);

      if (!proposal?.jobbeur_id) {
        notify("Impossible de déterminer la mission ou le jobbeur pour l'avis.", "error");
        return;
      }

      // D'abord vérifier si un avis existe
      const checkResponse = await axios.get(
        `${API_CONFIG.baseURL}/api/reviews/check/${missionId}`,
        {
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (checkResponse.data.exists && checkResponse.data.reviewId) {
        // Si un avis existe, récupérer ses détails
        const reviewResponse = await axios.get(
          `${API_CONFIG.baseURL}/api/reviews/${checkResponse.data.reviewId}`,
          {
            headers: await getCommonHeaders(),
            withCredentials: true
          }
        );

        if (reviewResponse.data.success) {
          // Ouvrir le modal avec l'avis existant
          setSelectedReview(reviewResponse.data.review);
          setReviewData({
            missionId,
            jobbeurId: proposal.jobbeur_id
          });
          setIsReviewModalOpen(true);
        }
      } else {
        // Si aucun avis n'existe, ouvrir le modal pour en créer un nouveau
        setSelectedReview(null);
        setReviewData({
          missionId,
          jobbeurId: proposal.jobbeur_id
        });
        setIsReviewModalOpen(true);
      }
    } catch (error) {
      logger.info('Erreur lors de la vérification/récupération de l\'avis:', error);
      notify('Erreur lors de la récupération de l\'avis', 'error');
    }
  };

  // Rendu du composant
  return (
      <Container>
      <Box sx={{
        width: '100%',
        bgcolor: 'white',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden',
        mb: 3
      }}>
        <Box sx={{
          borderBottom: 1,
          borderColor: 'divider',
          '@media (max-width: 600px)': {
            px: 1
          }
        }}>
          <StyledTabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="offres tabs"
            variant="fullWidth"
            scrollButtons={false}
          >
            <StyledTab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Offres envoyées
                  {activeTab === 0 && (
                    <InfoTooltip
                      title="Offres de missions envoyées"
                      content="Ce sont les offres de prix que vous avez envoyées aux jobbeurs pour répondre à leurs demandes de services. Vous pouvez suivre leur statut, accepter ou refuser les contre-offres, et communiquer avec les jobbeurs pour finaliser les détails de la mission."
                    />
                  )}
                </Box>
              }
              onClick={() => handleTabClick(0)}
            />
            <StyledTab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Offres reçues
                  {activeTab === 1 && (
                    <InfoTooltip
                      title="Offres de missions reçues"
                      content="Ce sont les propositions de travail que les jobbeurs vous ont envoyées en réponse à vos missions publiées. Vous pouvez les accepter, les refuser ou faire une contre-offre pour négocier les conditions de la mission."
                    />
                  )}
                </Box>
              }
              onClick={() => handleTabClick(1)}
            />
          </StyledTabs>
        </Box>
      </Box>

      <Box sx={{
        padding: {
          xs: '0 0 24px 0',
          sm: '0 24px 24px 24px'
        }
      }}>
        <Box sx={{
            display: 'flex',
            gap: 2,
            alignItems: 'center',
            flexWrap: 'wrap'
          }}>
          {/* Bouton de réinitialisation des filtres */}
          {hasActiveFilters && (
            <Button
              onClick={resetAllFilters}
              startIcon={<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 6L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
              </svg>}
              sx={{
                color: '#FF6B2C',
                borderColor: '#FFE4BA',
                '&:hover': {
                  backgroundColor: '#FFF8F3',
                  borderColor: '#FF6B2C'
                }
              }}
              variant="outlined"
              size="small"
            >
              Réinitialiser les filtres
            </Button>
          )}
        </Box>

        <FilterBar
          statusFilters={statusFilters}
          budgetFilters={budgetFilters}
          paymentFilters={paymentFilters}
          categoryFilters={categoryFilters}
          subcategoryFilters={subcategoryFilters}
          toggleFilter={toggleFilter}
          isFilterActive={isFilterActive}
          showStatusFilters={false}
          showLikedFilter={false}
          onCategoryFilterChange={handleCategoryFilterChange}
          showProfileFilters={true}
          showOfferStatusFilters={true}
          showSortOptions={true}
          profileFilters={profileFilters}
          offerStatusFilters={offerStatusFilters}
          sortBy={sortBy}
          isExpanded={isFilterBarExpanded}
          onToggleExpand={(expanded: boolean) => setIsFilterBarExpanded(expanded)}
          searchTerm={searchTerm}
          onSearch={handleSearch}
          showSearchField={true}
        />

        {/* Statistiques rapides */}
        {!loading && (proposals.length > 0 || stats.total > 0) && (
          <Box sx={{
            mb: 3,
            overflow: 'hidden',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
            border: '1px solid #FFE4BA'
          }}>
            <Box
              sx={{
                padding: '12px 16px',
                borderBottom: { xs: 'none', md: '1px solid #FFE4BA' },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 1,
                backgroundColor: '#FFF8F3',
                cursor: 'pointer'
              }}
              onClick={toggleStatsExpansion}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 8V20C21 21.1046 20.1046 22 19 22H5C3.89543 22 3 21.1046 3 20V4C3 2.89543 3.89543 2 5 2H15L21 8Z" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M7 16H17" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M7 12H17" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M8 8H9" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <Typography variant="subtitle1" sx={{
                  fontWeight: 600,
                  color: '#FF6B2C'
                }}>
                  Statistiques des offres
                </Typography>
                {loadingStats && (
                  <CircularProgress size={16} sx={{ color: '#FF6B2C', ml: 1 }} />
                )}
              </Box>

              {/* Icône d'expansion pour tous les écrans */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer'
                }}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{
                    transform: statsExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s ease'
                  }}
                >
                  <path d="M6 9L12 15L18 9" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Box>
            </Box>

            <Box sx={{
              display: statsExpanded ? 'flex' : 'none',
              flexWrap: 'wrap',
              padding: '16px',
              gap: { xs: 2, md: 3 },
              justifyContent: { xs: 'space-between', md: 'flex-start' },
              borderTop: { xs: '1px solid #FFE4BA', md: 'none' },
              transition: 'all 0.3s ease'
            }}>
              {/* Nouvelle section de statistiques avec un design moderne */}
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: 'repeat(2, 1fr)',
                  sm: 'repeat(3, 1fr)',
                  md: 'repeat(4, 1fr)',
                  lg: 'repeat(7, 1fr)'
                },
                gap: 2,
                width: '100%'
              }}>
                {/* Total */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 107, 44, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 107, 44, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Total
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 8V12L15 15" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="9" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#2D3748',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().total}
                  </Typography>
                </Box>

                {/* En attente */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 122, 53, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 122, 53, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: '#FF7A35',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      En attente
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 122, 53, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 8V12L15 15" stroke="#FF7A35" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="12" r="9" stroke="#FF7A35" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#FF7A35',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().enAttente}
                  </Typography>
                </Box>

                {/* Acceptées */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(76, 175, 80, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: '#4CAF50',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Acceptées
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(76, 175, 80, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 13L9 17L19 7" stroke="#4CAF50" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#4CAF50',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().acceptees}
                  </Typography>
                </Box>

                {/* Refusées */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 59, 48, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 59, 48, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: '#FF3B30',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Refusées
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 59, 48, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18" stroke="#FF3B30" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 6L18 18" stroke="#FF3B30" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#FF3B30',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().refusees}
                  </Typography>
                </Box>

                {/* Contre-offres */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 150, 94, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 150, 94, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: '#FF965E',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Contre-offres
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 150, 94, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 16L3 12M3 12L7 8M3 12H16M16 3V5C16 6.65685 17.3431 8 19 8H21M16 21V19C16 17.3431 17.3431 16 19 16H21" stroke="#FF965E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#FF965E',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().contreOffres}
                  </Typography>
                </Box>

                {/* Montant moyen */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 107, 44, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 107, 44, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Montant moyen
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6H10M18 6C19.1046 6 20 6.89543 20 8C20 9.10457 19.1046 10 18 10H8M18 6C16.8954 6 16 5.10457 16 4C16 2.89543 16.8954 2 18 2H22M8 10H18M8 10C6.89543 10 6 10.8954 6 12C6 13.1046 6.89543 14 8 14H18M18 14H10M18 14C19.1046 14 20 14.8954 20 16C20 17.1046 19.1046 18 18 18H14" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#2D3748',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().montantMoyen}€
                  </Typography>
                </Box>

                {/* Montant total */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  backgroundColor: 'white',
                  padding: '16px',
                  borderRadius: '16px',
                  boxShadow: '0 4px 20px rgba(255, 107, 44, 0.08)',
                  border: '1px solid rgba(255, 228, 186, 0.5)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 30px rgba(255, 107, 44, 0.12)',
                    '& .icon-container': {
                      transform: 'scale(1.1)',
                    }
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '4px',
                    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                    borderRadius: '4px'
                  }
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Typography variant="caption" sx={{
                      fontWeight: 600,
                      color: '#666',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}>
                      Montant total
                    </Typography>
                    <Box className="icon-container" sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 8C16.4183 8 20 6.88071 20 5.5C20 4.11929 16.4183 3 12 3C7.58172 3 4 4.11929 4 5.5C4 6.88071 7.58172 8 12 8Z" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M20 12C20 13.3807 16.4183 14.5 12 14.5C7.58172 14.5 4 13.3807 4 12" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M4 5.5V18.5C4 19.8807 7.58172 21 12 21C16.4183 21 20 19.8807 20 18.5V5.5" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    color: '#2D3748',
                    fontSize: { xs: '1.5rem', md: '1.75rem' }
                  }}>
                    {getStats().montantTotal}€
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        )}

        {loading ? (
          <LoadingContainer>
            <CircularProgress sx={{ color: '#FF6B2C' }} />
          </LoadingContainer>
        ) : proposals.length === 0 ? (
          <NoOffresBox>
            <Typography variant="h6" gutterBottom>
              {getEmptyMessage()}
            </Typography>
          </NoOffresBox>
        ) : (
          <Grid container spacing={{ xs: 0, sm: 0, md: 3, lg: 3 }}> {/* Espacement 0 sur mobile, 3 sur tablette et desktop */}
            {Object.entries(groupProposalsByMission(proposals)).map(([missionId, missionProposals], index, array) => {
              const mission = missionProposals[0].mission; // On prend la première proposition pour avoir les infos de la mission
              const isLastItem = index === array.length - 1;
              return (
                <React.Fragment key={missionId}>
                  <Grid size={{ xs: 12, sm: 12, md: 6, lg: 6 }}>
                    <Box sx={{
                      borderRadius: '16px',
                      overflow: 'hidden',
                      backgroundColor: 'white',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                      height: '100%', // Pour que toutes les cartes aient la même hauteur
                    }}>
                      {/* En-tête de la mission avec le nouveau design */}
                      <MissionHeaderContainer missionId={mission.id}>
                        {/* Titre mobile pour la mission */}
                        <MissionMobileHeader>
                          <div className="mission-title">
                            <span className="category-icon">
                              {mission.categories && mission.categories.length > 0
                                ? getCategoryIcon(mission.categories[0])
                                : getCategoryIcon()}
                            </span>
                            {mission.titre}
                          </div>
                          <div className="mission-location">{mission.ville}, {mission.code_postal}</div>
                        </MissionMobileHeader>
                        <MissionHeaderTop
                          onClick={(event) => toggleMissionCardExpansion(missionId, event)}
                          sx={{ cursor: 'pointer', position: 'relative' }}
                        >
                          {/* Photo principale de la mission */}
                          <Box
                            sx={{
                              position: 'absolute',
                              top: '20px',
                              right: '40px',
                              width: '80px',
                              height: '80px',
                              borderRadius: '8px',
                              overflow: 'hidden',
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                              border: '2px solid #FFF8F3',
                              zIndex: 9,
                              cursor: 'pointer',
                              '&:hover .overlay': {
                                opacity: 1
                              },
                              // Masquer sur desktop et tablettes, mais garder sur mobile
                              display: { xs: 'none', sm: 'none', md: 'none', lg: 'none' },
                              // Tablettes
                              '@media (min-width: 601px) and (max-width: 1200px)': {
                                display: 'none'
                              }
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              mission && handleOpenMissionModal(mission);
                            }}
                          >
                            <Box
                              className="overlay"
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: 'rgba(0, 0, 0, 0.4)',
                                opacity: 0,
                                transition: 'opacity 0.2s ease-in-out',
                                zIndex: 10
                              }}
                            >
                              <Tooltip title="Voir les détails de la mission" arrow placement="top">
                                <VisibilityIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                              </Tooltip>
                            </Box>
                            <MissionImageContainer>
                              <MissionImage
                                src={mission.photos && mission.photos.length > 0
                                  ? mission.photos[0].photo_url
                                  : DEFAULT_AVATAR}
                                alt={`Photo principale de ${mission.titre}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenMissionModal(mission);
                                }}
                              />
                              <MissionImageOverlay
                                className="mission-image-overlay"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenMissionModal(mission);
                                }}
                              >
                                Revoir la mission
                              </MissionImageOverlay>
                            </MissionImageContainer>
                          </Box>

                          <MissionTitleContainer>
                            <MissionTitle sx={{ display: 'none' }}>
                              {mission.titre}
                            </MissionTitle>
                            <LocationContainer sx={{ display: 'none' }}>
                              <LocationText variant="body2">
                                <Box component="span" sx={{ fontWeight: 600 }}>
                                  {mission.ville}
                                </Box>
                                <Box component="span" sx={{ mx: 0.5, color: '#FF6B2C', fontWeight: 'bold' }}>•</Box>
                                <Box component="span">
                                  {mission.code_postal}
                                </Box>
                              </LocationText>
                            </LocationContainer>
                          </MissionTitleContainer>

                          {/* Extrait de la description de la mission */}
                          <MobileDescriptionContainer>
                            {/* Image de la mission flottante pour mobile avec overlay */}
                            <MissionImageContainer>
                              <MissionImage
                                src={mission.photos && mission.photos.length > 0
                                  ? mission.photos[0].photo_url
                                  : DEFAULT_AVATAR}
                                alt={`Photo principale de ${mission.titre}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenMissionModal(mission);
                                }}
                              />
                              <MissionImageOverlay
                                className="mission-image-overlay"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenMissionModal(mission);
                                }}
                              >
                                Revoir la mission
                              </MissionImageOverlay>
                            </MissionImageContainer>

                            {/* Titre de la description uniquement visible sur desktop */}
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: 700,
                                color: '#FF6B2C',
                                marginBottom: '12px',
                                marginRight: '0', // Supprimer la marge à droite qui bloque l'écoulement
                                display: 'flex', // Afficher sur tous les appareils
                                fontSize: '16px',
                                alignItems: 'center',
                                gap: '8px',
                                backgroundColor: 'rgba(255, 107, 44, 0.08)', // Fond orange clair
                                borderRadius: '8px', // Coins arrondis
                                border: '1px solid rgba(255, 107, 44, 0.15)', // Bordure légère
                                padding: '6px 12px', // Espacement interne
                                '@media (max-width: 600px)': {
                                  marginBottom: '8px', // Marge plus petite sur mobile
                                  fontSize: '15px',
                                  padding: '4px 10px', // Padding plus petit sur mobile
                                },
                                '&::before': {
                                  content: '""',
                                  display: 'inline-block',
                                  width: { xs: '16px', sm: '18px' }, // Icône plus petite sur mobile
                                  height: { xs: '16px', sm: '18px' },
                                  backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'18\' height=\'18\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23FF6B2C\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\'%3E%3C/path%3E%3Cpolyline points=\'14 2 14 8 20 8\'%3E%3C/polyline%3E%3Cline x1=\'16\' y1=\'13\' x2=\'8\' y2=\'13\'%3E%3C/line%3E%3Cline x1=\'16\' y1=\'17\' x2=\'8\' y2=\'17\'%3E%3C/line%3E%3Cpolyline points=\'10 9 9 9 8 9\'%3E%3C/polyline%3E%3C/svg%3E")',
                                  backgroundSize: 'contain',
                                  backgroundRepeat: 'no-repeat'
                                }
                              }}
                            >
                              Description de la mission
                            </Typography>

                            <Typography variant="body2" sx={{
                              color: '#4A5568',
                              position: 'relative',
                              textAlign: 'justify',
                              // Styles spécifiques pour desktop
                              '@media (min-width: 601px) and (max-width: 1024px)': {
                                fontSize: '13.5px',
                                lineHeight: '1.5',
                              },
                              '@media (min-width: 601px)': {
                                fontSize: '15px',
                                lineHeight: '1.7',
                                letterSpacing: '0.1px'
                              }
                            }}>
                              {mission.description && mission.description.length > 300 && window.innerWidth < 600
                                ? stripHtml(mission.description).substring(0, 300) + '...'
                                : stripHtml(mission.description)}
                            </Typography>
                          </MobileDescriptionContainer>

                          {/* Indicateur de statut pour les offres repliées - Offres envoyées */}
                          {missionProposals.length > 0 && activeTab === 0 && (
                            <Box sx={{ mt: 1, mb: 1 }}>
                              {(() => {
                                // Compter les différents statuts des offres pour cette mission
                                const statusCounts = countProposalStatuses(missionProposals);

                                // Prioriser l'affichage des statuts actifs (en attente, contre-offre) plutôt que refusée
                                if (statusCounts.en_attente > 0 && statusCounts.contre_offre_jobbeur > 0) {
                                  // Cas spécial: à la fois des offres en attente et des contre-offres
                                  return (
                                    <StatusIndicator status="en_attente">
                                      <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                      <span style={{ position: "relative", top: "-1px" }}>
                                        {`${statusCounts.en_attente} offre(s) en attente et ${statusCounts.contre_offre_jobbeur} contre-offre(s) en cours`}
                                      </span>
                                    </StatusIndicator>
                                  );
                                } else if (statusCounts.contre_offre > 0 && statusCounts.contre_offre_jobbeur > 0) {
                                  // Cas spécial: à la fois des contre-offres reçues et envoyées
                                  return (
                                    <StatusIndicator status="contre_offre">
                                      <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                      <span style={{ position: "relative", top: "-1px" }}>
                                        {`${statusCounts.contre_offre} contre-offre(s) reçue(s) et ${statusCounts.contre_offre_jobbeur} envoyée(s)`}
                                      </span>
                                    </StatusIndicator>
                                  );
                                } else if (statusCounts.en_attente > 0) {
                                  return (
                                    <StatusIndicator status="en_attente">
                                      <AccessTime fontSize="small" sx={{ mr: 1 }} />
                                      <span style={{ position: "relative", top: "-1px" }}>
                                        {statusCounts.en_attente > 1 ? `${statusCounts.en_attente} offres en attente de réponse` : 'Offre envoyée, en attente de réponse'}
                                      </span>
                                    </StatusIndicator>
                                  );
                                } else if (statusCounts.contre_offre > 0) {
                                  return (
                                    <StatusIndicator status="contre_offre">
                                      <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                      <span style={{ position: "relative", top: "-1px" }}>
                                        {statusCounts.contre_offre > 1 ? `${statusCounts.contre_offre} contre-offres reçues` : 'Contre-offre reçue, vous devez répondre'}
                                      </span>
                                    </StatusIndicator>
                                  );
                                } else if (statusCounts.contre_offre_jobbeur > 0) {
                                  return (
                                    <StatusIndicator status="contre_offre_jobbeur">
                                      <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                      <span style={{ position: "relative", top: "-1px" }}>
                                        {statusCounts.contre_offre_jobbeur > 1 ? `${statusCounts.contre_offre_jobbeur} contre-offres en attente` : 'Votre contre-offre à été envoyée'}
                                      </span>
                                    </StatusIndicator>
                                  );
                                }

                                // Fallback au comportement par défaut
                                return (
                                  <StatusIndicator status={missionProposals[0].statut}>
                                    {missionProposals[0].statut === 'en_attente' && (
                                      <>
                                        <AccessTime fontSize="small" sx={{ mr: 1 }} />
                                        Offre envoyée, en attente de réponse
                                      </>
                                    )}
                                    {missionProposals[0].statut === 'acceptée' && (
                                      <>
                                        <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 1 }} />
                                        Offre acceptée par le jobbeur
                                      </>
                                    )}
                                    {missionProposals[0].statut === 'refusée' && (
                                      <>
                                        <CancelOutlinedIcon fontSize="small" sx={{ mr: 1 }} />
                                        Offre refusée par le jobbeur
                                      </>
                                    )}
                                    {missionProposals[0].statut === 'contre_offre' && (
                                      <>
                                        <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                        Contre-offre reçue, en attente de réponse
                                      </>
                                    )}
                                    {missionProposals[0].statut === 'contre_offre_jobbeur' && (
                                      <>
                                        <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                        Votre contre-offre à été envoyée
                                      </>
                                    )}
                                  </StatusIndicator>
                                );
                              })()}
                            </Box>
                          )}

                          {/* Statut de l'offre */}
                          <Box sx={{
                            mt: { xs: 0, md: 1 },
                            mb: { xs: 0, md: 1 },
                            minHeight: { md: '48px' },
                            display: { xs: missionProposals[0].statut === 'acceptée' ? 'block' : 'none', md: 'block' }
                          }}>
                            {missionProposals[0].statut === 'acceptée' ? (
                              /* Ajout du statut de paiement si l'offre est acceptée */
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                px: { xs: 1, md: 2 },
                                py: { xs: 1, md: 1.5 },
                                borderRadius: '4px',
                                width: '100%',
                                fontSize: '0.875rem',
                                fontWeight: 500,
                                ...(
                                  missionProposals[0].payment_status === 'completed'
                                    ? {
                                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                        border: '1px solid rgba(76, 175, 80, 0.3)',
                                        color: '#4CAF50'
                                      }
                                    : missionProposals[0].payment_status === 'manual'
                                    ? {
                                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                        border: '1px solid rgba(76, 175, 80, 0.3)',
                                        color: '#4CAF50'
                                      }
                                    : {
                                        backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                        border: '1px solid rgba(255, 107, 44, 0.3)',
                                        color: '#FF6B2C'
                                      }
                                )
                              }}>
                                {missionProposals[0].payment_status === 'completed' ? (
                                  <>
                                    <MonetizationOnIcon fontSize="small" sx={{ mr: 1 }} />
                                    Echange de Jobi effectué le {new Date(missionProposals[0].payment_date!).toLocaleDateString()} - {missionProposals[0].montant_paiement === getLastNegotiatedAmount(missionProposals[0]) ? 'Paiement total' : 'Paiement partiel'} ({missionProposals[0].montant_paiement} / {getLastNegotiatedAmount(missionProposals[0])} Jobi)
                                  </>
                                ) : missionProposals[0].payment_status === 'manual' ? (
                                  <>
                                    <EuroIcon fontSize="small" sx={{ mr: 1 }} />
                                    Payé le {new Date(missionProposals[0].payment_date!).toLocaleDateString()} - {missionProposals[0].montant_paiement === getLastNegotiatedAmount(missionProposals[0]) ? 'Paiement total' : 'Paiement partiel'} ({missionProposals[0].montant_paiement} / {getLastNegotiatedAmount(missionProposals[0])}€)
                                  </>
                                ) : (
                                  <Box sx={{ display: 'flex', alignItems: 'center', color: '#FF6B2C', fontWeight: 'bold' }}>
                                    <PendingIcon fontSize="small" sx={{ mr: 1 }} />
                                    En attente de paiement
                                  </Box>
                                )}
                              </Box>
                            ) : (
                              /* Espace réservé invisible pour maintenir la hauteur uniquement sur desktop */
                              <Box sx={{
                                display: { xs: 'none', md: 'block' },
                                height: '48px',
                                visibility: 'hidden'
                              }} />
                            )}
                          </Box>

                          {/* Indicateur de statut pour les offres repliées - Offres reçues */}
                          {missionProposals.length > 0 && activeTab === 1 && (
                            <Box sx={{ mt: 1, mb: 1 }}>
                              {(() => {
                                // Compter les différents statuts des offres pour cette mission
                                const statusCounts = countProposalStatuses(missionProposals);

                                // Pour les offres reçues (activeTab === 1), prioriser l'affichage des statuts
                                return (
                                  <StatusIndicator status={
                                    statusCounts.acceptée > 0
                                      ? 'acceptée'
                                      : (statusCounts.refusée > 0 && statusCounts.refusée === missionProposals.length
                                          ? 'refusée'
                                          : (statusCounts.contre_offre_jobbeur > 0
                                              ? 'contre_offre_jobbeur'
                                              : (statusCounts.en_attente > 0
                                                  ? 'en_attente'
                                                  : missionProposals[0].statut)))
                                  }>
                                    {/* Pour les offres reçues avec une acceptée et d'autres refusées */}
                                    {statusCounts.acceptée > 0 && statusCounts.refusée > 0 && (
                                      <>
                                        <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 1 }} />
                                        <span style={{ position: "relative", top: "-1px" }}>
                                          {`Mission fermée : 1 offre acceptée et ${statusCounts.refusée} refusée${statusCounts.refusée > 1 ? 's' : ''}`}
                                        </span>
                                      </>
                                    )}
                                    {/* Pour les offres reçues avec une seule acceptée */}
                                    {statusCounts.acceptée > 0 && statusCounts.refusée === 0 && (
                                      <>
                                        <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 1 }} />
                                        <span style={{ position: "relative", top: "-1px" }}>
                                          Mission fermée, une offre a été acceptée
                                        </span>
                                      </>
                                    )}
                                    {/* Pour les offres reçues toutes refusées */}
                                    {statusCounts.refusée > 0 && statusCounts.acceptée === 0 && statusCounts.refusée === missionProposals.length && (
                                      <>
                                        <CancelOutlinedIcon fontSize="small" sx={{ mr: 1 }} />
                                        {statusCounts.refusée > 1 ? `${statusCounts.refusée} offres refusées` : 'Offre refusée'}
                                      </>
                                    )}
                                    {/* Cas par défaut pour les offres reçues */}
                                    {!(
                                      (statusCounts.acceptée > 0 && statusCounts.refusée > 0) ||
                                      (statusCounts.acceptée > 0 && statusCounts.refusée === 0) ||
                                      (statusCounts.refusée > 0 && statusCounts.acceptée === 0 && statusCounts.refusée === missionProposals.length)
                                    ) && (
                                      <>
                                        {statusCounts.contre_offre_jobbeur > 0 && (
                                          <>
                                            <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                            {statusCounts.contre_offre_jobbeur > 1
                                              ? `${statusCounts.contre_offre_jobbeur} contre-offres reçues, en attente de votre réponse`
                                              : `${statusCounts.contre_offre_jobbeur} contre-offre reçue, en attente de votre réponse`}
                                          </>
                                        )}
                                        {statusCounts.contre_offre_jobbeur === 0 && statusCounts.en_attente > 0 && (
                                          <>
                                            <AccessTime fontSize="small" sx={{ mr: 1 }} />
                                            {statusCounts.en_attente > 1
                                              ? `${statusCounts.en_attente} offres en attente de votre réponse`
                                              : 'Offre reçue, en attente de votre réponse'}
                                          </>
                                        )}
                                        {statusCounts.contre_offre_jobbeur === 0 && statusCounts.en_attente === 0 && missionProposals[0].statut === 'contre_offre' && (
                                          <>
                                            <SwapHorizIcon fontSize="small" sx={{ mr: 1 }} />
                                            {statusCounts.contre_offre > 1
                                              ? `${statusCounts.contre_offre} contre-offres envoyées`
                                              : 'Votre contre-offre a été envoyée'}
                                          </>
                                        )}
                                        {statusCounts.contre_offre_jobbeur === 0 && statusCounts.en_attente === 0 && missionProposals[0].statut === 'acceptée' && (
                                          <>
                                            <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 1 }} />
                                            Mission fermée, une offre a été acceptée
                                          </>
                                        )}
                                        {statusCounts.contre_offre_jobbeur === 0 && statusCounts.en_attente === 0 && missionProposals[0].statut === 'refusée' && (
                                          <>
                                            <CancelOutlinedIcon fontSize="small" sx={{ mr: 1 }} />
                                            Offre refusée
                                          </>
                                        )}
                                      </>
                                    )}
                                  </StatusIndicator>
                                );
                              })()}
                            </Box>
                          )}

                          {/* Bouton pour partager les informations de contact - affiché sous le statut de mission fermée */}
                          {missionProposals.length > 0 && activeTab === 1 && (() => {
                            const statusCounts = countProposalStatuses(missionProposals);
                            if (statusCounts.acceptée > 0) {
                              return (
                                <Box sx={{
                                  mt: 2,
                                  mb: 2,
                                  display: 'flex',
                                  flexDirection: { xs: 'column', sm: 'row' },
                                  justifyContent: 'center',
                                  gap: 2,
                                  width: '100%',
                                  '& > button': {
                                    width: { xs: '100%', sm: 'auto' }
                                  }
                                }}>
                                  <Button
                                    variant="contained"
                                    size="small"
                                    startIcon={<ContactPhoneIcon />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Trouver la proposition acceptée
                                      const acceptedProposal = missionProposals.find(p => p.statut === 'acceptée');
                                      if (acceptedProposal) {
                                        handleOpenContactInfoModal(acceptedProposal);
                                      }
                                    }}
                                    sx={{
                                      borderRadius: '8px',
                                      textTransform: 'none',
                                      fontWeight: 600,
                                      backgroundColor: '#FF6B2C',
                                      '&:hover': {
                                        backgroundColor: '#E55A1F'
                                      }
                                    }}
                                  >
                                    Partager vos informations de contact
                                  </Button>
                                  <Button
                                    variant="contained"
                                    size="small"
                                    startIcon={<ChatRoundedIcon />}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Trouver la proposition acceptée
                                      const acceptedProposal = missionProposals.find(p => p.statut === 'acceptée');
                                      if (acceptedProposal && acceptedProposal.jobbeur_id) {
                                        // Stocker les informations dans le localStorage pour la modal de nouvelle conversation
                                        localStorage.setItem('newMessageInfo', JSON.stringify({
                                          recipientId: acceptedProposal.jobbeur_id,
                                          recipientName: acceptedProposal.jobbeur_profile?.prenom ? `${acceptedProposal.jobbeur_profile.prenom} ${acceptedProposal.jobbeur_profile.nom || ''}` : 'Jobbeur',
                                          initialMessage: `Bonjour, je vous contacte concernant la mission "${acceptedProposal.mission.titre}".`
                                        }));

                                        // Rediriger vers la page des messages
                                        navigate('/dashboard/messages');
                                      }
                                    }}
                                    sx={{
                                      borderRadius: '8px',
                                      textTransform: 'none',
                                      fontWeight: 600,
                                      backgroundColor: '#FF6B2C',
                                      '&:hover': {
                                        backgroundColor: '#E55A1F'
                                      }
                                    }}
                                  >
                                    Envoyer un message
                                  </Button>
                                </Box>
                              );
                            }
                            return null;
                          })()}

                          {/* AvatarContainer pour les clients et les jobbeurs concernés */}
                          <AvatarContainer
                            sx={{
                              display: { xs: 'block', sm: 'none' },
                              mt: 2,
                              mb: 1,
                              border: '1px dashed rgba(255, 107, 44, 0.2)',
                              borderRadius: '8px',
                              p: 1,
                              backgroundColor: 'rgba(255, 248, 243, 0.5)'
                            }}
                          >
                            <Typography variant="caption" sx={{ display: 'block', mb: 1, color: '#666', fontWeight: 500 }}>
                              {activeTab === 0 ? 'Clients concernés :' : 'Jobbeurs concernés :'}
                            </Typography>
                            <AvatarGroup
                              max={7}
                              className="avatar-group"
                              sx={{
                                '& .MuiAvatar-root': {
                                  width: 32,
                                  height: 32,
                                  fontSize: '0.875rem',
                                  border: '2px solid #FFF8F3',
                                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                }
                              }}
                            >
                              {missionProposals.map(proposal => (
                                <Tooltip
                                  key={proposal.id}
                                  title={activeTab === 0
                                    ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom?.charAt(0).toUpperCase() || ''}.`
                                    : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom?.charAt(0).toUpperCase() || ''}.`
                                  }
                                  arrow
                                  placement="top"
                                >
                                  <Avatar
                                    src={activeTab === 0
                                      ? proposal.mission?.user_profile?.photo_url
                                      : proposal.jobbeur_profile?.photo_url
                                    }
                                    alt={activeTab === 0
                                      ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom ? proposal.mission?.user_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                      : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom ? proposal.jobbeur_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                    }
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const userId = activeTab === 0
                                        ? proposal.mission?.user_id
                                        : proposal.jobbeur_id;
                                      if (userId) {
                                        handleOpenUserProfile(userId);
                                      }
                                    }}
                                    sx={{
                                      cursor: 'pointer',
                                      transition: 'transform 0.2s ease',
                                      '&:hover': {
                                        transform: 'scale(1.1)',
                                        zIndex: 10
                                      }
                                    }}
                                  />
                                </Tooltip>
                              ))}
                            </AvatarGroup>
                          </AvatarContainer>

                          <AvatarContainer
                            sx={{
                              display: { xs: 'none', sm: 'block' },
                              mt: 2,
                              mb: 1,
                              border: '1px dashed rgba(255, 107, 44, 0.2)',
                              borderRadius: '8px',
                              p: 1,
                              backgroundColor: 'rgba(255, 248, 243, 0.5)'
                            }}
                          >
                            <Typography variant="caption" sx={{ display: 'block', mb: 1, color: '#666', fontWeight: 500 }}>
                              {activeTab === 0 ? 'Clients concernés :' : 'Jobbeurs concernés :'}
                            </Typography>
                            <AvatarGroup
                              max={4}
                              className="avatar-group"
                              sx={{
                                '& .MuiAvatar-root': {
                                  width: 36,
                                  height: 36,
                                  fontSize: '1rem',
                                  border: '2px solid #FFF8F3',
                                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                }
                              }}
                            >
                              {missionProposals.map(proposal => (
                                <Tooltip
                                  key={proposal.id}
                                  title={activeTab === 0
                                    ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom?.charAt(0).toUpperCase() || ''}.`
                                    : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom?.charAt(0).toUpperCase() || ''}.`
                                  }
                                  arrow
                                  placement="top"
                                >
                                  <Avatar
                                    src={activeTab === 0
                                      ? proposal.mission?.user_profile?.photo_url
                                      : proposal.jobbeur_profile?.photo_url
                                    }
                                    alt={activeTab === 0
                                      ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom ? proposal.mission?.user_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                      : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom ? proposal.jobbeur_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                    }
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const userId = activeTab === 0
                                        ? proposal.mission?.user_id
                                        : proposal.jobbeur_id;
                                      if (userId) {
                                        handleOpenUserProfile(userId);
                                      }
                                    }}
                                    sx={{
                                      cursor: 'pointer',
                                      transition: 'transform 0.2s ease',
                                      '&:hover': {
                                        transform: 'scale(1.1)',
                                        zIndex: 10
                                      }
                                    }}
                                  />
                                </Tooltip>
                              ))}
                            </AvatarGroup>
                          </AvatarContainer>
                          {/* Affichage des états des propositions avec les nouveaux composants */}
                          <ChipsContainer sx={{ mb: 2 }}>
                            {/* Compteur total d'offres */}
                            {activeTab === 1 && (
                              <StyledChip
                                size="small"
                                label={`${missionProposals.length} offre${missionProposals.length > 1 ? 's' : ''} :`}
                                color="primary"
                              />
                            )}

                            {activeTab === 1 && (() => {
                              const statusCounts = countProposalStatuses(missionProposals);
                              return (
                                <>
                                  {statusCounts.en_attente > 0 && (
                                    <StyledChip
                                      size="small"
                                      label={`${statusCounts.en_attente} offre${statusCounts.en_attente > 1 ? 's' : ''} reçue${statusCounts.en_attente > 1 ? 's' : ''}`}
                                    />
                                  )}
                                  {statusCounts.acceptée > 0 && (
                                    <StyledChip
                                      size="small"
                                      label={`${statusCounts.acceptée} acceptée${statusCounts.acceptée > 1 ? 's' : ''}`}
                                      color="success"
                                    />
                                  )}
                                  {statusCounts.refusée > 0 && (
                                    <StyledChip
                                      size="small"
                                      label={`${statusCounts.refusée} refusée${statusCounts.refusée > 1 ? 's' : ''}`}
                                    />
                                  )}
                                  {statusCounts.contre_offre > 0 && (
                                    <StyledChip
                                      size="small"
                                      label={`${statusCounts.contre_offre} contre-offre${statusCounts.contre_offre > 1 ? 's' : ''} envoyée${statusCounts.contre_offre > 1 ? 's' : ''}`}
                                      color="secondary"
                                    />
                                  )}
                                  {statusCounts.contre_offre_jobbeur > 0 && (
                                    <StyledChip
                                      size="small"
                                      label={`${statusCounts.contre_offre_jobbeur} contre-offre${statusCounts.contre_offre_jobbeur > 1 ? 's' : ''} reçue${statusCounts.contre_offre_jobbeur > 1 ? 's' : ''}`}
                                      color="secondary"
                                    />
                                  )}
                                </>
                              );
                            })()}
                          </ChipsContainer>
                        </MissionHeaderTop>

                        {/* Ajout d'une section d'actions en bas de la carte */}
                        <MissionActions onClick={() => toggleMissionCardExpansion(missionId)}>
                          <ActionButtonsContainer onClick={(e) => e.stopPropagation()}>
                            <StyledTooltip
                              title="J'aime cette mission"
                              placement="top"
                              arrow
                            >
                              <EnhancedActionButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleLike(missionProposals[0].mission);
                                }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ scale: 1 }}
                                transition={{
                                  type: "spring",
                                  stiffness: 400,
                                  damping: 17
                                }}
                                style={{
                                  backgroundColor: missionProposals[0].mission.user_has_liked ? 'rgba(255, 107, 44, 0.15)' : 'rgba(255, 107, 44, 0.05)',
                                }}
                              >
                                <FavoriteRoundedIcon sx={{
                                  fontSize: '1rem',
                                  color: missionProposals[0].mission.user_has_liked ? '#FF6B2C' : 'rgba(255, 107, 44, 0.7)',
                                }} />
                                <span>{missionProposals[0].mission.likes_count || 0}</span>
                              </EnhancedActionButton>
                            </StyledTooltip>

                            <StyledTooltip
                              title="Voir les commentaires"
                              placement="top"
                              arrow
                            >
                              <EnhancedActionButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenComments(missionProposals[0].mission);
                                }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ scale: 1 }}
                                transition={{
                                  type: "spring",
                                  stiffness: 400,
                                  damping: 17
                                }}
                              >
                                <ChatRoundedIcon sx={{
                                  fontSize: '1rem',
                                  color: '#FF6B2C'
                                }} />
                                <span>{missionProposals[0].mission.comments_count || 0}</span>
                              </EnhancedActionButton>
                            </StyledTooltip>
                          </ActionButtonsContainer>

                          <Box sx={{ display: 'flex', alignItems: 'center' }} onClick={(e) => e.stopPropagation()}>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px',
                                backgroundColor: 'rgba(255, 107, 44, 0.05)',
                                padding: '6px 12px',
                                borderRadius: '20px',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                },
                                color: '#FF6B2C',
                                fontWeight: 600,
                                fontSize: '0.75rem',
                              }}
                              onClick={(event) => {
                                event.stopPropagation();
                                toggleMissionCardExpansion(missionId, event);
                              }}
                            >
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {expandedCards.includes(missionId) ? 'Masquer offres' : 'Voir offres'}
                              </Typography>
                              {expandedCards.includes(missionId) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            </Box>
                          </Box>
                        </MissionActions>

                        {/* Indicateur de fin de mission */}
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: '4px',
                            background: 'linear-gradient(to right, #FFE4BA, #FF6B2C, #FFE4BA)',
                            opacity: 0.7,
                            borderRadius: '0 0 16px 16px'
                          }}
                        />
                      </MissionHeaderContainer>
                    </Box>
                  </Grid>
                  {/* Séparateur vertical entre les missions (visible uniquement sur mobile) */}
                  {!isLastItem && (
                    <Box
                      sx={{
                        display: { xs: 'flex', sm: 'flex', md: 'none', lg: 'none' },
                        width: '100%',
                        height: '50px',
                        position: 'relative',
                        justifyContent: 'center',
                        alignItems: 'center',
                        my: 1,
                        px: 2,
                      }}
                    >
                      {/* Ligne verticale pointillée */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          bottom: 0,
                          width: '2px',
                          background: 'linear-gradient(to bottom, #FF6B2C 50%, transparent 50%)',
                          backgroundSize: '4px 4px',
                          opacity: 0.7,
                        }}
                      />
                      {/* Cercle décoratif */}
                      <Box
                        sx={{
                          width: '24px',
                          height: '24px',
                          borderRadius: '50%',
                          border: '2px solid #FF6B2C',
                          backgroundColor: '#FFF8F3',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          zIndex: 1,
                        }}
                      >
                        <Box
                          sx={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            backgroundColor: '#FF6B2C',
                          }}
                        />
                      </Box>
                    </Box>
                  )}
                </React.Fragment>
              );
            })}
          </Grid>
        )}

        {hasMore && !loading && (
          <LoadingContainer ref={loadingRef}>
            {loadingMore && <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />}
          </LoadingContainer>
        )}

        {proposals.length > 0 && (
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 2,
            mb: 1,
            px: 1
          }}>
            {activeTab === 1 && (
              <Typography variant="body2" color="text.secondary">
                {proposals.length} {proposals.length === 1 ? 'offre trouvée' : 'offres trouvées'}
              </Typography>
            )}
            {hasMore && (
              <Typography variant="body2" color="text.secondary">
                Faites défiler pour voir plus d'offres
              </Typography>
            )}
          </Box>
        )}
      </Box>

      {/* Boîte de dialogue de confirmation */}
      <ModalPortal
        isOpen={confirmDialogOpen}
        onBackdropClick={handleCloseConfirmDialog}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: "spring", duration: 0.5 }}
          style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            padding: '0',
            maxWidth: '700px',
            width: '90%',
            position: 'relative',
            border: '2px solid',
            borderColor: confirmAction === 'accept' ? '#4CAF50' : '#FF3B30',
            margin: '0 auto',
            display: 'flex',
            flexDirection: 'column',
            maxHeight: '90vh'
          }}
        >
          {/* En-tête */}
          <Box
            sx={{
              borderBottom: '2px solid',
              borderColor: confirmAction === 'accept' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 59, 48, 0.2)',
              paddingBottom: '16px',
              padding: '24px 24px 16px 24px',
              flexShrink: 0
            }}
          >
            <Typography
              variant="h5"
              sx={{
                color: confirmAction === 'accept' ? '#4CAF50' : '#FF3B30',
                fontWeight: 'bold',
                fontSize: '1.5rem'
              }}
            >
              {confirmAction === 'accept' ? '✅ Accepter cette proposition ?' : '❌ Refuser cette proposition ?'}
            </Typography>
          </Box>

          {/* Contenu scrollable */}
          <Box
            sx={{
              overflowY: 'auto',
              padding: '0 24px',
              flexGrow: 1
            }}
          >
            {/* Résumé de la mission */}
            {selectedProposal && (
              <Box
                sx={{
                  backgroundColor: '#FFF8F3',
                  border: '1px solid #FFE4BA',
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '24px',
                  marginTop: '24px'
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    marginBottom: '12px'
                  }}
                >
                  Résumé de la mission
                </Typography>

                <Box sx={{ display: 'flex', gap: 2, marginBottom: '12px' }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Mission
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {selectedProposal.mission.titre}
                    </Typography>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Localisation
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {selectedProposal.mission.ville}, {selectedProposal.mission.code_postal}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 2, marginBottom: '12px' }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Offre initiale
                    </Typography>
                    <Typography variant="body1" fontWeight="500" color="#FF6B2C">
                      {selectedProposal.montant_propose}€
                    </Typography>
                  </Box>
                  {(selectedProposal.montant_contre_offre || selectedProposal.montant_contre_offre_jobbeur) && (
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Dernier montant négocié
                      </Typography>
                      <Typography variant="body1" fontWeight="500" color="#FF6B2C">
                        {getLastNegotiatedAmount(selectedProposal)}€
                      </Typography>
                    </Box>
                  )}
                </Box>

                {selectedProposal.mission.has_time_preference && selectedProposal.mission.time_slots?.length > 0 && (
                  <Box sx={{ marginBottom: '12px' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Créneaux souhaités
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {selectedProposal.mission.time_slots.map(slot =>
                        `${slot.date} de ${slot.start} à ${slot.end}`
                      ).join(', ')}
                    </Typography>
                  </Box>
                )}

                {selectedProposal.message && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Message du jobbeur
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        backgroundColor: 'white',
                        padding: '12px',
                        borderRadius: '6px',
                        border: '1px solid #FFE4BA',
                        fontStyle: 'italic'
                      }}
                    >
                      {selectedProposal.message}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            {/* Avertissement pour les offres multiples */}
            {confirmAction === 'accept' && (
              <Box sx={{
                mb: 3,
                p: 2,
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
                borderRadius: '8px',
                border: '1px solid rgba(255, 107, 44, 0.2)',
                display: 'flex',
                flexDirection: 'column',
                gap: 2
              }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <WarningAmberIcon sx={{ fontSize: 16, color: '#FF6B2C', mt: 0.5 }} />
                  <Typography variant="body2" sx={{ color: '#FF6B2C' }}>
                    En acceptant cette proposition, vous vous engagez à travailler avec ce jobbeur. Cette action est définitive et ne pourra pas être annulée. Toutes les autres offres pour cette mission seront automatiquement refusées.
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Contenu pour le refus uniquement */}
            {confirmAction === 'reject' && (
              <Box sx={{
                mb: 3,
                p: 2,
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
                borderRadius: '8px',
                border: '1px solid rgba(255, 107, 44, 0.2)',
                display: 'flex',
                flexDirection: 'column',
                gap: 2
              }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <WarningAmberIcon sx={{ fontSize: 16, color: '#FF6B2C', mt: 0.5 }} />
                  <Typography variant="body2" sx={{ color: '#FF6B2C' }}>
                    En refusant cette proposition, vous indiquez que vous ne souhaitez pas travailler avec ce jobbeur pour cette mission. Cette action est définitive et ne pourra pas être annulée.
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Contenu */}

          </Box>

          {/* Actions - toujours visibles en bas */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '12px',
            padding: '24px',
            borderTop: '1px solid #FFE4BA',
            flexShrink: 0
          }}>
            <Button
              onClick={handleCloseConfirmDialog}
              disabled={isProcessing}
              sx={{
                backgroundColor: '#FFF8F3',
                color: '#666',
                border: '2px solid #FFE4BA',
                borderRadius: '8px',
                padding: '8px 24px',
                fontWeight: 'bold',
                '&:hover': {
                  backgroundColor: '#FFE4BA',
                  border: '2px solid #FF6B2C',
                }
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={handleConfirm}
              variant="contained"
              disabled={isProcessing}
              sx={{
                borderRadius: '8px',
                padding: '8px 24px',
                fontWeight: 'bold',
                backgroundColor: confirmAction === 'accept' ? '#4CAF50' : '#FF3B30',
                '&:hover': {
                  backgroundColor: confirmAction === 'accept' ? '#45a049' : '#ff1f1f',
                }
              }}
            >
              {isProcessing ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={20} sx={{ color: 'white' }} />
                  <span>Traitement...</span>
                </Box>
              ) : (
                confirmAction === 'accept' ? 'Accepter' : 'Refuser'
              )}
            </Button>
          </Box>
        </motion.div>
      </ModalPortal>

      {/* Modal de contre-offre */}
      {selectedProposal && (
        <CounterOfferModal
          open={counterOfferModalOpen}
          onClose={handleCloseCounterOfferModal}
          missionId={selectedProposal.mission.id}
          proposalId={selectedProposal.id}
          currentAmount={selectedProposal.montant_contre_offre_jobbeur || selectedProposal.montant_propose}
          currentMessage={selectedProposal.message_contre_offre_jobbeur || selectedProposal.message}
          onCounterOfferSubmit={handleCounterOfferSubmit}
          jobbeurName={selectedProposal.jobbeur_profile ?
            `${selectedProposal.jobbeur_profile.prenom || ''} ${selectedProposal.jobbeur_profile.nom ? selectedProposal.jobbeur_profile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
            : "Jobbeur"}
          clientName="Vous"
          initialAmount={selectedProposal.montant_propose}
          offerHistory={
            selectedProposal.montant_contre_offre_jobbeur || selectedProposal.montant_contre_offre ?
            {
              initialOffer: {
                amount: selectedProposal.montant_propose,
                date: new Date(selectedProposal.created_at).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.created_at).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }),
                message: selectedProposal.message
              },
              jobbeurCounterOffers: selectedProposal.montant_contre_offre_jobbeur ? [{
                amount: selectedProposal.montant_contre_offre_jobbeur,
                date: selectedProposal.date_contre_offre_jobbeur ? new Date(selectedProposal.date_contre_offre_jobbeur).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.date_contre_offre_jobbeur).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }) : undefined,
                message: selectedProposal.message_contre_offre_jobbeur
              }] : [],
              clientCounterOffers: selectedProposal.montant_contre_offre ? [{
                amount: selectedProposal.montant_contre_offre,
                date: selectedProposal.date_contre_offre ? new Date(selectedProposal.date_contre_offre).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.date_contre_offre).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }) : undefined,
                message: selectedProposal.message_contre_offre
              }] : []
            } : undefined
          }
          missionInfo={{
            titre: selectedProposal.mission.titre,
            categorie: selectedProposal.mission.categories?.[0] || '',
            dureeEstimee: selectedProposal.mission.has_time_preference ?
              selectedProposal.mission.time_slots?.map(slot =>
                `${slot.date} ${slot.start}-${slot.end}`).join(', ') : '',
            localisation: `${selectedProposal.mission.ville}, ${selectedProposal.mission.code_postal}`,
            niveauRequis: '',
            description: selectedProposal.mission.description
          }}
        />
      )}

      {/* Modal de contre-offre du jobbeur */}
      {selectedProposal && (
        <JobbeurCounterOfferModal
          open={jobbeurCounterOfferModalOpen}
          onClose={handleCloseJobbeurCounterOfferModal}
          missionId={selectedProposal.mission.id}
          proposalId={selectedProposal.id}
          currentAmount={selectedProposal.montant_contre_offre || selectedProposal.montant_propose}
          currentMessage={selectedProposal.message_contre_offre || selectedProposal.message}
          onCounterOfferSubmit={handleCounterOfferSubmit}
          jobbeurName="Vous"
          clientName={selectedProposal.mission.user_profile ?
            `${selectedProposal.mission.user_profile.prenom || ''} ${selectedProposal.mission.user_profile.nom ? selectedProposal.mission.user_profile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
            : "Client"}
          initialAmount={selectedProposal.montant_propose}
          offerHistory={
            selectedProposal.montant_contre_offre_jobbeur || selectedProposal.montant_contre_offre ?
            {
              initialOffer: {
                amount: selectedProposal.montant_propose,
                date: new Date(selectedProposal.created_at).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.created_at).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }),
                message: selectedProposal.message
              },
              jobbeurCounterOffers: selectedProposal.montant_contre_offre_jobbeur ? [{
                amount: selectedProposal.montant_contre_offre_jobbeur,
                date: selectedProposal.date_contre_offre_jobbeur ? new Date(selectedProposal.date_contre_offre_jobbeur).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.date_contre_offre_jobbeur).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }) : undefined,
                message: selectedProposal.message_contre_offre_jobbeur
              }] : [],
              clientCounterOffers: selectedProposal.montant_contre_offre ? [{
                amount: selectedProposal.montant_contre_offre,
                date: selectedProposal.date_contre_offre ? new Date(selectedProposal.date_contre_offre).toLocaleDateString('fr-FR', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                }) + ' à ' + new Date(selectedProposal.date_contre_offre).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                }) : undefined,
                message: selectedProposal.message_contre_offre
              }] : []
            } : undefined
          }
          missionInfo={{
            titre: selectedProposal.mission.titre,
            categorie: selectedProposal.mission.categories?.[0] || '',
            dureeEstimee: selectedProposal.mission.has_time_preference ?
              selectedProposal.mission.time_slots?.map(slot =>
                `${slot.date} ${slot.start}-${slot.end}`).join(', ') : '',
            localisation: `${selectedProposal.mission.ville}, ${selectedProposal.mission.code_postal}`,
            niveauRequis: '',
            description: selectedProposal.mission.description
          }}
        />
      )}

      {/* Modal du profil utilisateur */}
      {selectedUserProfile && (
        <UserProfileModal
          isOpen={isProfileModalOpen}
          onClose={() => setIsProfileModalOpen(false)}
          userData={selectedUserProfile}
        />
      )}

      {/* Dialog pour les commentaires */}
      {showCommentDialog && currentMission && (
        <CommentsDialog
          open={showCommentDialog}
          onClose={handleCloseComments}
          mission={currentMission}
          missionId={currentMission.id}
          isOwner={false}
          onUpdate={handleCommentUpdate}
        />
      )}

      {/* Modal d'aperçu de la mission */}
      {isMissionModalOpen && selectedMission && (
        <ModalPortal>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
            onClick={handleCloseMissionModal}
          >
            <Box
              sx={{
                backgroundColor: 'white',
                borderRadius: '16px',
                width: '90%',
                maxWidth: '1200px',
                maxHeight: '90vh',
                overflow: 'auto',
                position: 'relative',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* En-tête de la modal */}
              <Box
                sx={{
                  p: 2,
                  borderBottom: '1px solid #FFE4BA',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: '#FFF8F3',
                  borderRadius: '16px 16px 0 0',
                }}
              >
                <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold' }}>
                  Aperçu de la mission
                </Typography>
                <IconButton
                  onClick={handleCloseMissionModal}
                  sx={{
                    color: '#718096',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      color: '#FF6B2C',
                    },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Contenu de la modal */}
              <Box sx={{ p: 3 }}>
                <MissionCard
                  mission={selectedMission}
                  isOwner={false}
                  onUpdate={handleProposalUpdate}
                  showStatus={true}
                  isHighlighted={false}
                  onMakeProposal={() => {}}
                />
              </Box>
            </Box>
          </Box>
        </ModalPortal>
      )}

      {/* Modal pour afficher le message complet */}
      {isMessageModalOpen && selectedMessage && (
        <ModalPortal>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
            onClick={() => setIsMessageModalOpen(false)}
          >
            <Box
              sx={{
                backgroundColor: 'white',
                borderRadius: '16px',
                width: '90%',
                maxWidth: '900px',
                maxHeight: '80vh',
                overflow: 'auto',
                position: 'relative',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* En-tête de la modal */}
              <Box
                sx={{
                  p: 2,
                  borderBottom: '1px solid #FFE4BA',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: '#FFF8F3',
                  borderRadius: '16px 16px 0 0',
                }}
              >
                <Box>
                  <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold' }}>
                    Message complet
                  </Typography>
                  {selectedSender && (
                    <Typography variant="caption" sx={{ color: '#718096', display: 'block' }}>
                      {selectedSender.prenom} {selectedSender.nom?.charAt(0)}.
                    </Typography>
                  )}
                </Box>
                <IconButton
                  onClick={() => setIsMessageModalOpen(false)}
                  sx={{
                    color: '#718096',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      color: '#FF6B2C',
                    },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Contenu de la modal */}
              <Box sx={{ p: 3 }}>
                <Typography sx={{
                  color: '#4A5568',
                  whiteSpace: 'pre-wrap',
                  lineHeight: 1.6
                }}>
                  {selectedMessage}
                </Typography>
              </Box>
            </Box>
          </Box>
        </ModalPortal>
      )}

      {/* Modale d'envoi d'informations de contact */}
      {isContactInfoModalOpen && selectedJobbeurForContact && selectedProposal?.id && selectedProposal?.mission_id && selectedProposal?.mission?.user_id === user?.id && (
        <ContactInfoModal
          open={isContactInfoModalOpen}
          onClose={handleCloseContactInfoModal}
          jobbeurId={selectedJobbeurForContact}
          proposalId={selectedProposal.id}
          missionId={selectedProposal.mission_id}
          isAccepted={true}
          jobbeurName={`${selectedProposal.jobbeur_profile?.prenom || ''} ${selectedProposal.jobbeur_profile?.nom ? selectedProposal.jobbeur_profile.nom.charAt(0) + '.' : ''}`.trim()}
          jobbeurPhotoUrl={selectedProposal.jobbeur_profile?.photo_url}
        />
      )}

      {/* Modal pour afficher les informations de contact */}
      {isContactInfoModalOpen && selectedJobbeurForContact && selectedProposal && selectedProposal?.mission?.user_id === user?.id && (
        <ContactInfoModal
          open={isContactInfoModalOpen}
          onClose={handleCloseContactInfoModal}
          jobbeurId={selectedJobbeurForContact}
          missionId={selectedProposal.mission_id || ''}
          proposalId={selectedProposal.id}
          isAccepted={selectedProposal.statut === 'acceptée'}
          jobbeurName={`${selectedProposal.jobbeur_profile?.prenom || ''} ${selectedProposal.jobbeur_profile?.nom ? selectedProposal.jobbeur_profile.nom.charAt(0) + '.' : ''}`.trim()}
          jobbeurPhotoUrl={selectedProposal.jobbeur_profile?.photo_url}
        />
      )}

      {/* Modal pour afficher les propositions d'une mission */}
      {isMissionProposalsModalOpen && selectedMissionProposals.length > 0 && (
        <ModalPortal>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
            onClick={handleCloseMissionProposalsModal}
          >
            <Box
              sx={{
                backgroundColor: 'white',
                borderRadius: '16px',
                width: '95%',
                maxWidth: '1200px',
                maxHeight: '90vh',
                overflow: 'auto',
                position: 'relative',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                backgroundImage: `
                  radial-gradient(circle at 10% 20%, rgba(255, 228, 186, 0.03) 0%, transparent 40%),
                  radial-gradient(circle at 90% 30%, rgba(255, 150, 94, 0.05) 0%, transparent 25%),
                  radial-gradient(circle at 30% 70%, rgba(255, 107, 44, 0.04) 0%, transparent 25%),
                  radial-gradient(circle at 80% 80%, rgba(255, 228, 186, 0.25) 0%, transparent 30%)
                `,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                '@media (max-width: 600px)': {
                  width: '98%'
                }
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* En-tête de la modal */}
              <Box
                sx={{
                  p: 2,
                  borderBottom: '1px solid #FFE4BA',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: '#FFF8F3',
                  borderRadius: '16px 16px 0 0',
                }}
              >
                <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold' }}>
                  {selectedMissionProposals[0]?.mission?.titre || 'Détails de la mission'}
                </Typography>
                <IconButton
                  onClick={handleCloseMissionProposalsModal}
                  sx={{
                    color: '#718096',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      color: '#FF6B2C',
                    },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Contenu de la modal des offres */}
              <Box sx={{
                p: { xs: 2, sm: 3 }
              }}>
                {/* En-tête avec le nombre d'offres et filtres */}
                <Box sx={{
                  mb: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  gap: 1
                }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '100%',
                      backgroundColor: '#FFF8F3',
                      borderRadius: '12px',
                      padding: '8px 12px',
                      border: '1px solid #FFE4BA',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: '#FFF0E0',
                        boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)'
                      }
                    }}
                    onClick={() => setShowFilterPanel(prev => !prev)}
                  >
                    <Typography variant="h6" sx={{
                      color: '#1E293B',
                      fontWeight: 'bold',
                      fontSize: { xs: '1rem', sm: '1.15rem' },
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box
                        component="span"
                        sx={{
                          backgroundColor: '#FF6B2C',
                          color: 'white',
                          borderRadius: '50%',
                          width: '24px',
                          height: '24px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontWeight: 'bold',
                          fontSize: '0.9rem'
                        }}
                      >
                        {selectedMissionProposals.length}
                      </Box>
                      {selectedMissionProposals.length > 1 ? 'offres disponibles' : 'offre disponible'}
                    </Typography>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      color: '#FF6B2C'
                    }}>
                      {showFilterPanel ? 'Masquer les filtres' : 'Afficher les filtres'}
                      <IconButton
                        size="small"
                        sx={{
                          color: '#FF6B2C',
                          backgroundColor: 'rgba(255, 107, 44, 0.1)',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.2)',
                          },
                          padding: '4px'
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowFilterPanel(prev => !prev);
                        }}
                      >
                        {showFilterPanel ? <CloseIcon fontSize="small" /> : <FilterIcon fontSize="small" />}
                      </IconButton>
                    </Box>
                  </Box>

                  {/* Panneau de filtres */}
                  {showFilterPanel && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: { xs: 2, sm: 3 },
                        mb: 2,
                        width: '100%',
                        borderRadius: '12px',
                        border: '1px solid #FFE4BA',
                        backgroundColor: '#FFF8F3',
                        mt: 2,
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
                      }}
                    >
                      <Typography variant="subtitle1" sx={{
                        fontWeight: 'bold',
                        color: '#333',
                        mb: 2,
                        borderBottom: '1px solid #FFE4BA',
                        paddingBottom: 1
                      }}>
                        Filtrer par statut
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1.5,
                        mt: 1,
                        justifyContent: { xs: 'center', sm: 'flex-start' }
                      }}>
                        <Chip
                          label={`En cours (${selectedMissionProposals.filter(p => p.statut !== 'acceptée' && p.statut !== 'refusée').length})`}
                          onClick={() => setProposalStatusFilter('all')}
                          sx={{
                            backgroundColor: proposalStatusFilter === 'all' ? '#FF6B2C' : '#FFF8F3',
                            color: proposalStatusFilter === 'all' ? 'white' : '#333',
                            border: '1px solid #FFE4BA',
                            fontWeight: 'medium',
                            padding: '4px',
                            '&:hover': {
                              backgroundColor: proposalStatusFilter === 'all' ? '#FF6B2C' : 'rgba(255, 107, 44, 0.1)',
                            },
                          }}
                        />
                        <Chip
                          label={`Attention requise (${filterProposalsByStatus(selectedMissionProposals, 'en_attente').length})`}
                          onClick={() => setProposalStatusFilter('en_attente')}
                          sx={{
                            backgroundColor: proposalStatusFilter === 'en_attente' ? '#FF6B2C' : '#FFF8F3',
                            color: proposalStatusFilter === 'en_attente' ? 'white' : '#333',
                            border: '1px solid #FFE4BA',
                            fontWeight: 'medium',
                            padding: '4px',
                            '&:hover': {
                              backgroundColor: proposalStatusFilter === 'en_attente' ? '#FF6B2C' : 'rgba(255, 107, 44, 0.1)',
                            },
                          }}
                        />
                        <Chip
                          label={`Acceptées (${filterProposalsByStatus(selectedMissionProposals, 'acceptée').length})`}
                          onClick={() => setProposalStatusFilter('acceptée')}
                          sx={{
                            backgroundColor: proposalStatusFilter === 'acceptée' ? '#FF6B2C' : '#FFF8F3',
                            color: proposalStatusFilter === 'acceptée' ? 'white' : '#333',
                            border: '1px solid #FFE4BA',
                            fontWeight: 'medium',
                            padding: '4px',
                            '&:hover': {
                              backgroundColor: proposalStatusFilter === 'acceptée' ? '#FF6B2C' : 'rgba(255, 107, 44, 0.1)',
                            },
                          }}
                        />
                        <Chip
                          label={`Refusées (${filterProposalsByStatus(selectedMissionProposals, 'refusée').length})`}
                          onClick={() => setProposalStatusFilter('refusée')}
                          sx={{
                            backgroundColor: proposalStatusFilter === 'refusée' ? '#FF6B2C' : '#FFF8F3',
                            color: proposalStatusFilter === 'refusée' ? 'white' : '#333',
                            border: '1px solid #FFE4BA',
                            fontWeight: 'medium',
                            padding: '4px',
                            '&:hover': {
                              backgroundColor: proposalStatusFilter === 'refusée' ? '#FF6B2C' : 'rgba(255, 107, 44, 0.1)',
                            },
                          }}
                        />
                      </Box>
                    </Paper>
                  )}

                  <Divider sx={{ width: '100%', mt: 1, mb: 1, borderColor: 'rgba(255, 228, 186, 0.5)' }} />
                </Box>

                {/* Conteneur pour les propositions en style conversation */}
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 0,
                  maxWidth: '1100px',
                  margin: '0 auto'
                }}>
                  {filterProposalsByStatus(selectedMissionProposals, proposalStatusFilter).length > 0 ? (
                    filterProposalsByStatus(selectedMissionProposals, proposalStatusFilter)
                      .map((proposal, index) => {
                      // Déterminer si c'est une offre paire ou impaire pour le décalage
                      const isEven = index % 2 === 0;

                      return (
                        <React.Fragment key={proposal.id}>
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: isEven ? 'flex-start' : 'flex-end',
                              width: '100%',
                              position: 'relative',
                              py: 0 // Suppression du padding vertical des offres
                            }}
                          >
                            {/* Carte de l'offre avec badge intégré */}
                            <Box
                              onClick={() => !expandedCards.includes(proposal.id) && toggleCardExpansion(proposal.id)}
                              sx={{
                                padding: { xs: '14px', sm: '24px' },
                                borderRadius: '16px',
                                backgroundColor: '#FFF8F3',
                                border: '1px solid #FFE4BA',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                                maxWidth: selectedMissionProposals.length === 1 || index === 0 ? '100%' : '94%',
                                width: { xs: '100%', sm: '100%' },
                                alignSelf: isEven ? 'flex-start' : 'flex-end',
                                position: 'relative',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  transform: 'translateY(-4px)',
                                  boxShadow: '0 8px 16px rgba(255, 107, 44, 0.15)'
                                },
                                '@media (max-width: 600px)': {
                                  maxWidth: '100%'
                                }
                              }}
                            >

                              {/* Profil utilisateur */}
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1.5,
                                  mb: 2,
                                  cursor: 'pointer',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const userId = activeTab === 0
                                    ? proposal.mission?.user_id
                                    : proposal.jobbeur_id;
                                  if (userId) {
                                    handleOpenUserProfile(userId);
                                  }
                                }}
                              >
                                <Box sx={{ position: 'relative' }}>
                                  <Avatar
                                    src={activeTab === 0
                                      ? proposal.mission?.user_profile?.photo_url
                                      : proposal.jobbeur_profile?.photo_url
                                    }
                                    alt={activeTab === 0
                                      ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom ? proposal.mission?.user_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                      : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom ? proposal.jobbeur_profile.nom.charAt(0).toUpperCase() + '.' : ''}`
                                    }
                                    sx={{
                                      width: 48,
                                      height: 48,
                                      border: '2px solid #FFE4BA',
                                      cursor: 'pointer',
                                      '&:hover': {
                                        boxShadow: '0 0 0 2px #FF6B2C'
                                      }
                                    }}
                                  />
                                  {/* Indicateur de statut en ligne sur l'avatar */}
                                  <Box sx={{
                                    position: 'absolute',
                                    bottom: '9px',
                                    right: 0,
                                    zIndex: 1,
                                    transform: 'translate(20%, 20%)'
                                  }}>
                                    {activeTab === 0 && proposal.mission?.user_id && (
                                      <OnlineStatusDot userId={proposal.mission.user_id} />
                                    )}
                                    {activeTab === 1 && proposal.jobbeur_id && (
                                      <OnlineStatusDot userId={proposal.jobbeur_id} />
                                    )}
                                  </Box>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, width: '100%' }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, width: '100%' }}>
                                    <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
                                      <Box sx={{ flexGrow: 1 }}>
                                        <Typography variant="body1" sx={{
                                          fontWeight: 600,
                                          color: '#2D3748'
                                        }}>
                                          {activeTab === 0
                                            ? `${proposal.mission?.user_profile?.prenom || ''} ${proposal.mission?.user_profile?.nom ? proposal.mission?.user_profile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
                                            : `${proposal.jobbeur_profile?.prenom || ''} ${proposal.jobbeur_profile?.nom ? proposal.jobbeur_profile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
                                          }
                                        </Typography>

                                        {/* Afficher la ville et le code postal du jobbeur pour les offres reçues */}
                                        {proposal.jobbeur_id && jobbeurProfiles[proposal.jobbeur_id]?.fullProfile?.profil?.data?.ville && (
                                          <Typography variant="caption" sx={{
                                            color: '#718096',
                                            display: 'block',
                                            mt: -0.5
                                          }}>
                                            {jobbeurProfiles[proposal.jobbeur_id].fullProfile.profil.data.ville}
                                            {jobbeurProfiles[proposal.jobbeur_id].fullProfile.profil.data.code_postal ?
                                              `, ${jobbeurProfiles[proposal.jobbeur_id].fullProfile.profil.data.code_postal}` : ''}
                                          </Typography>
                                        )}
                                      </Box>

                                      {/* Badge numéro d'offre - affiché uniquement pour les offres reçues (activeTab === 1) */}
                                      {activeTab === 1 && (
                                        <Box sx={{
                                          backgroundColor: '#FF6B2C',
                                          color: 'white',
                                          borderRadius: '8px',
                                          padding: '8px 14px',
                                          fontSize: '0.85rem',
                                          fontWeight: 'bold',
                                          display: 'flex',
                                          alignItems: 'center',
                                          height: 'fit-content',
                                          boxShadow: '0 2px 4px rgba(255, 107, 44, 0.3)',
                                          border: '1px solid rgba(255, 255, 255, 0.3)'
                                        }}>
                                          Offre {index + 1}
                                        </Box>
                                      )}
                                    </Box>
                                  </Box>
                                </Box>
                              </Box>

                              {/* En-tête avec statut */}
                              <Box sx={{
                                display: 'flex',
                                justifyContent: 'center', // Centrer au lieu de space-between
                                alignItems: 'center',
                                mb: 3,
                                width: '100%'
                              }}>
                                {/* Affichage du montant de l'offre ou de la contre-offre */}
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    textAlign: 'center',
                                    minWidth: '140px',
                                    padding: '6px 12px',
                                    borderRadius: '8px',
                                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    ...(proposal.statut === 'acceptée' && {
                                      backgroundColor: '#E8F5E9',
                                      color: '#10B981',
                                      border: '1px solid #10B981'
                                    }),
                                    ...(proposal.statut === 'refusée' && {
                                      backgroundColor: 'red',
                                      color: 'white',
                                      border: '1px solid red'
                                    }),
                                    ...(!['acceptée', 'refusée'].includes(proposal.statut) && {
                                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                      color: '#FF6B2C',
                                      border: '1px solid #FF6B2C'
                                    })
                                  }}
                                >
                                  <Box sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    fontWeight: 'bold',
                                    fontSize: '0.85rem',
                                    mb: 0.5,
                                    justifyContent: 'center',
                                  }}>
                                    <EuroIcon sx={{ fontSize: '1rem' }} />
                                    {proposal.statut === 'en_attente'
                                      ? (activeTab === 0
                                          ? `Offre envoyée : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`
                                          : `Offre reçue : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`)
                                      : proposal.statut === 'acceptée'
                                        ? (activeTab === 0
                                            ? `Offre acceptée : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`
                                            : `Offre acceptée : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`)
                                        : proposal.statut === 'refusée'
                                          ? (activeTab === 0
                                              ? `Offre refusée : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`
                                              : `Offre refusée : ${getLastNegotiatedAmount(proposal)} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`)
                                          : proposal.statut === 'contre_offre'
                                            ? (activeTab === 1
                                                ? `Contre-offre envoyée : ${proposal.montant_contre_offre} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`
                                                : `Contre-offre reçue : ${proposal.montant_contre_offre} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`)
                                            : proposal.statut === 'contre_offre_jobbeur'
                                              ? (activeTab === 0
                                                  ? `Contre-offre envoyée : ${proposal.montant_contre_offre_jobbeur} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`
                                                  : `Contre-offre reçue : ${proposal.montant_contre_offre_jobbeur} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`)
                                              : `${proposal.montant_propose} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`}
                                  </Box>
                                </Box>
                              </Box>

                              {/* Message */}
                              <Box sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: 2,
                                mt: 2,
                                backgroundColor: '#FFFFFF',
                                borderRadius: '12px',
                                padding: { xs: '12px', sm: '16px' },
                                border: '1px solid #F0F0F0'
                              }}>
                                {/* En-tête du message */}
                                <Typography variant="subtitle2" sx={{
                                  color: '#FF6B2C',
                                  fontWeight: 600,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                  mb: 1
                                }}>
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  </svg>
                                  {activeTab === 0 ? 'Votre dernier message' : 'Dernier message du jobbeur'}
                                </Typography>

                                {/* Message */}
                                {(() => {
                                  const lastMessageInfo = getLastMessage(proposal);
                                  const message = lastMessageInfo.message;

                                  // Fonction pour formater le message avec des sauts de ligne
                                  const formatMessage = (text: string) => {
                                    return text.split('\n').map((line, i) => (
                                      <React.Fragment key={i}>
                                        {line}
                                        {i < text.split('\n').length - 1 && <br />}
                                      </React.Fragment>
                                    ));
                                  };

                                  return (
                                    <Box
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedMessage(message);
                                        setSelectedSender({
                                          nom: proposal.jobbeur_profile?.nom,
                                          prenom: proposal.jobbeur_profile?.prenom
                                        });
                                        setIsMessageModalOpen(true);
                                      }}
                                      sx={{
                                        cursor: 'pointer',
                                        '&:hover': {
                                          opacity: 0.8
                                        }
                                      }}
                                    >
                                      <Typography variant="body2" sx={{
                                        color: '#2D3748',
                                        fontWeight: 500,
                                        lineHeight: 1.8,
                                        fontSize: '0.9rem',
                                        whiteSpace: 'pre-line'
                                      }}>
                                        {message.length > 450
                                          ? formatMessage(message.substring(0, 450) + '...')
                                          : formatMessage(message)}
                                      </Typography>
                                      {message.length > 450 && (
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            display: 'inline-block',
                                            color: '#FF6B2C',
                                            mt: 1,
                                            fontStyle: 'italic',
                                            fontWeight: 600,
                                            padding: '4px 8px',
                                            borderRadius: '4px',
                                            backgroundColor: 'rgba(255, 107, 44, 0.08)',
                                            '&:hover': {
                                              textDecoration: 'underline',
                                              backgroundColor: 'rgba(255, 107, 44, 0.12)'
                                            }
                                          }}
                                        >
                                          Voir le message complet →
                                        </Typography>
                                      )}
                                    </Box>
                                  );
                                })()}
                              </Box>

                              {/* Historique des négociations */}
                              {getNegotiationHistory(proposal).length > 0 && (
                                <Box sx={{
                                  mt: 3,
                                  mb: 2,
                                  backgroundColor: '#FFFFFF',
                                  borderRadius: '12px',
                                  overflow: 'hidden'
                                }}>
                                  <Box
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleNegotiationHistory(proposal.id);
                                    }}
                                    sx={{
                                      cursor: 'pointer',
                                      display: 'flex',
                                      justifyContent: 'space-between',
                                      alignItems: 'center',
                                      padding: '12px 16px',
                                      backgroundColor: '#FFF0E0',
                                      borderBottom: expandedNegotiations[proposal.id] ? '1px solid #FFE4BA' : 'none',
                                      transition: 'background-color 0.2s ease',
                                      '&:hover': {
                                        backgroundColor: '#FFEAD3'
                                      }
                                    }}
                                  >
                                    <Typography
                                      variant="subtitle1"
                                      sx={{
                                        color: '#1E293B',
                                        fontWeight: 600,
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 1
                                      }}
                                    >
                                      <ChatBubbleOutline sx={{ color: '#FF6B2C', fontSize: '1.2rem' }} />
                                      Historique des négociations
                                    </Typography>
                                    {expandedNegotiations[proposal.id] ?
                                      <ExpandLessIcon sx={{ color: '#FF6B2C' }} /> :
                                      <ExpandMoreIcon sx={{ color: '#FF6B2C' }} />
                                    }
                                  </Box>

                                  <Collapse in={expandedNegotiations[proposal.id] ?? false}>
                                    {/* Conteneur des messages */}
                                    <Box sx={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      gap: 3,
                                      padding: '16px'
                                    }}>
                                      {/* Messages triés par date */}
                                      {getNegotiationHistory(proposal).map((item, index) => (
                                        <Box
                                          key={`${item.type}-${index}`}
                                          sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: 1,
                                            ml: item.sender === 'client' ? 'auto' : 0,
                                            mr: item.sender === 'jobbeur' ? 'auto' : 0,
                                            maxWidth: '85%'
                                          }}
                                        >
                                          <Box sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 1,
                                            mb: 0.5,
                                            justifyContent: item.sender === 'client' ? 'flex-end' : 'flex-start'
                                          }}>
                                            {item.sender === 'jobbeur' && (
                                              <Box sx={{ position: 'relative' }}>
                                                <Avatar
                                                  src={item.profile?.photo_url}
                                                  alt={`${item.profile?.prenom || 'Aucun prénom'} ${item.profile?.nom ? item.profile?.nom.charAt(0).toUpperCase() + '.' : 'Aucun nom'}`}
                                                  sx={{ width: 32, height: 32, cursor: 'pointer' }}
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (item.profile && proposal.jobbeur_id) {
                                                      handleOpenUserProfile(proposal.jobbeur_id);
                                                    }
                                                  }}
                                                />
                                                {proposal.jobbeur_id && (
                                                  <Box sx={{
                                                    position: 'absolute',
                                                    bottom: '0px',
                                                    right: '0px',
                                                    zIndex: 1,
                                                    transform: 'scale(0.8)'
                                                  }}>
                                                    <OnlineStatusDot userId={proposal.jobbeur_id} />
                                                  </Box>
                                                )}
                                              </Box>
                                            )}
                                            <Box>
                                              <Typography
                                                variant="subtitle2"
                                                sx={{
                                                  color: '#1E293B',
                                                  fontWeight: 600,
                                                  textAlign: item.sender === 'client' ? 'right' : 'left',
                                                  cursor: 'pointer',
                                                  '&:hover': {
                                                    color: '#FF6B2C'
                                                  },
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                  gap: 0.5
                                                }}
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  const userId = item.sender === 'client'
                                                    ? proposal.mission?.user_id
                                                    : proposal.jobbeur_id;
                                                  if (userId) {
                                                    handleOpenUserProfile(userId);
                                                  }
                                                }}
                                              >
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                  {item.profile?.prenom || 'Aucun prénom'} {item.profile?.nom ? item.profile?.nom.charAt(0).toUpperCase() + '.' : 'Aucun nom'}
                                                </Box>
                                                <Box sx={{ display: 'flex', alignItems: 'center', ml: 0.5, fontSize: '0.75rem', color: '#718096' }}>
                                                  {item.sender === 'client' && (
                                                    proposal.mission?.ville || 'Ville inconnue'
                                                  )}
                                                  {item.sender === 'jobbeur' && (
                                                    item.profile?.fullProfile?.profil?.data?.ville || 'Ville inconnue'
                                                  )}
                                                </Box>
                                              </Typography>
                                              <Typography variant="caption" sx={{
                                                color: '#64748B',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 0.5,
                                                justifyContent: item.sender === 'client' ? 'flex-end' : 'flex-start'
                                              }}>
                                                <AccessTime sx={{ fontSize: 12 }} />
                                                {new Date(item.date).toLocaleDateString('fr-FR', {
                                                  day: 'numeric',
                                                  month: 'long',
                                                  year: 'numeric'
                                                })} à {new Date(item.date).toLocaleTimeString('fr-FR', {
                                                  hour: '2-digit',
                                                  minute: '2-digit'
                                                })}
                                              </Typography>
                                            </Box>
                                            {item.sender === 'client' && (
                                              <Box sx={{ position: 'relative' }}>
                                                <Avatar
                                                  src={item.profile?.photo_url}
                                                  alt={`${item.profile?.prenom || 'Aucun prénom'} ${item.profile?.nom ? item.profile?.nom.charAt(0).toUpperCase() + '.' : 'Aucun nom'}`}
                                                  sx={{ width: 32, height: 32, cursor: 'pointer' }}
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    if (proposal.mission?.user_id) {
                                                      handleOpenUserProfile(proposal.mission.user_id);
                                                    }
                                                  }}
                                                />
                                                {proposal.mission?.user_id && (
                                                  <Box sx={{
                                                    position: 'absolute',
                                                    bottom: '0px',
                                                    right: '0px',
                                                    zIndex: 1,
                                                    transform: 'scale(0.8)'
                                                  }}>
                                                    <OnlineStatusDot userId={proposal.mission.user_id} />
                                                  </Box>
                                                )}
                                              </Box>
                                            )}
                                          </Box>

                                          <Box sx={{
                                            backgroundColor: item.sender === 'client'
                                              ? 'rgba(255, 107, 44, 0.08)'
                                              : 'rgba(255, 228, 186, 0.15)',
                                            borderRadius: '12px',
                                            p: 2,
                                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                                            border: `1px solid ${item.sender === 'client'
                                              ? 'rgba(255, 107, 44, 0.3)'
                                              : 'rgba(255, 228, 186, 0.4)'}`
                                          }}>
                                            {/* Montant en haut du message */}
                                            <Box sx={{
                                              display: 'flex',
                                              justifyContent: 'flex-start',
                                              mb: 2
                                            }}>
                                              <Chip
                                                size="small"
                                                icon={<EuroIcon style={{ fontSize: 16 }} />}
                                                label={`Proposition : ${item.montant} ${proposal.mission.payment_method === 'direct_only' ? '€' : 'Jobis'}`}
                                                sx={{
                                                  backgroundColor: item.sender === 'client' ? '#FF6B2C' : '#FF965E',
                                                  color: 'white',
                                                  fontWeight: 600,
                                                  '& .MuiChip-icon': {
                                                    color: 'white'
                                                  },
                                                  '& .MuiChip-label': {
                                                    fontSize: '0.75rem',
                                                    paddingLeft: '4px',
                                                    paddingTop: '1px'
                                                  }
                                                }}
                                              />
                                            </Box>

                                            <Typography variant="body2" sx={{
                                              color: '#1E293B',
                                              whiteSpace: 'pre-wrap'
                                            }}>
                                              {item.message}
                                            </Typography>

                                            {/* Informations supplémentaires */}
                                            {item.time_slots && item.time_slots.length > 0 && (
                                              <Box sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                mt: 2,
                                                pt: 2,
                                                borderTop: '1px dashed #FFE4BA'
                                              }}>
                                                <Typography variant="body2" sx={{
                                                  color: '#64748B',
                                                  display: 'flex',
                                                  alignItems: 'center',
                                                  gap: 0.5
                                                }}>
                                                  <AccessTime sx={{ fontSize: 14 }} />
                                                  {item.time_slots.map(slot =>
                                                    `${slot.date} ${slot.start}-${slot.end}`).join(', ')
                                                  }
                                                </Typography>
                                              </Box>
                                            )}
                                          </Box>
                                        </Box>
                                      ))}
                                    </Box>
                                  </Collapse>
                                </Box>
                              )}

                              {/* Boutons d'action */}
                              <Box sx={{
                                display: 'flex',
                                flexDirection: { xs: 'column', md: 'row' }, // Changement à md pour tablette
                                justifyContent: { xs: 'center', md: 'space-between' },
                                alignItems: { xs: 'center', md: 'center' },
                                mt: 3,
                                gap: { xs: 0, md: 2 },
                                width: '100%'
                              }}>
                                {/* Sur desktop: date à gauche, boutons à droite sur la même ligne */}
                                {/* Sur mobile et tablette: boutons centrés en haut, date en bas à gauche */}

                                {/* Date positionnée en bas à gauche sur mobile/tablette, à gauche sur desktop */}
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: '#718096',
                                    fontSize: '0.7rem',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    width: { xs: '100%', md: 'auto' },
                                    textAlign: { xs: 'left', md: 'left' },
                                    order: { xs: 2, md: 1 },
                                    mt: { xs: 2, md: 0 },
                                    justifyContent: { xs: 'flex-start', md: 'flex-start' }
                                  }}
                                >
                                  {new Date(proposal.updated_at).toLocaleDateString('fr-FR', {
                                    day: 'numeric',
                                    month: 'long',
                                    year: 'numeric'
                                  })} à {new Date(proposal.updated_at).toLocaleTimeString('fr-FR', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </Typography>

                                {/* Conteneur pour les boutons */}
                                <Box sx={{
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: 1,
                                  justifyContent: { xs: 'center', md: 'flex-end' },
                                  width: { xs: '100%', md: 'auto' },
                                  order: { xs: 1, md: 2 }
                                }}>
                                  {/* Boutons d'action pour les offres en attente */}
                                  {((activeTab === 1 && (proposal.statut === 'en_attente' || proposal.statut === 'contre_offre_jobbeur')) ||
                                  (activeTab === 0 && proposal.statut === 'contre_offre')) && (
                                    <>
                                      <Button
                                        variant="outlined"
                                        color="error"
                                        startIcon={<CancelOutlinedIcon />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleConfirmAction(proposal, 'reject');
                                        }}
                                        sx={{
                                          borderRadius: '8px',
                                          textTransform: 'none',
                                          fontWeight: 600,
                                          backgroundColor: 'red',
                                          borderColor: 'red',
                                          color: 'white',
                                          '&:hover': {
                                            backgroundColor: 'red',
                                            borderColor: 'red'
                                          }
                                        }}
                                      >
                                        Refuser
                                      </Button>
                                      <Button
                                        variant="contained"
                                        color="success"
                                        startIcon={<CheckCircleOutlineIcon />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleConfirmAction(proposal, 'accept');
                                        }}
                                        sx={{
                                          borderRadius: '8px',
                                          textTransform: 'none',
                                          fontWeight: 600,
                                          backgroundColor: '#10B981',
                                          '&:hover': {
                                            backgroundColor: '#059669'
                                          }
                                        }}
                                      >
                                        Accepter
                                      </Button>
                                    </>
                                  )}

                                  {/* Bouton de contre-offre pour les offres reçues */}
                                  {((activeTab === 1 && (proposal.statut === 'en_attente' || proposal.statut === 'contre_offre_jobbeur')) ||
                                  (activeTab === 0 && proposal.statut === 'contre_offre')) && (
                                    <Button
                                      variant="contained"
                                      startIcon={<SwapHorizIcon />}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        if (activeTab === 1) {
                                          handleCounterOffer(proposal);
                                        } else {
                                          handleJobbeurCounterOffer(proposal);
                                        }
                                      }}
                                      sx={{
                                        borderRadius: '8px',
                                        textTransform: 'none',
                                        fontWeight: 600,
                                        backgroundColor: '#FF6B2C',
                                        '&:hover': {
                                          backgroundColor: '#E55A1F'
                                        }
                                      }}
                                    >
                                      Faire une contre-offre
                                    </Button>
                                  )}

                                  {/* Bouton pour partager les informations de contact */}
                                  {proposal.statut === 'acceptée' && activeTab === 1 && (
                                    <Box sx={{
                                      display: 'flex',
                                      flexDirection: { xs: 'column', sm: 'row' },
                                      gap: 2,
                                      width: '100%',
                                      '& > button': {
                                        width: { xs: '100%', sm: 'auto' }
                                      }
                                    }}>
                                      <Button
                                        variant="contained"
                                        startIcon={<ContactPhoneIcon />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleOpenContactInfoModal(proposal);
                                        }}
                                        sx={{
                                          borderRadius: '8px',
                                          textTransform: 'none',
                                          fontWeight: 600,
                                          backgroundColor: '#FF6B2C',
                                          '&:hover': {
                                            backgroundColor: '#E55A1F'
                                          }
                                        }}
                                      >
                                        Partager vos informations de contact
                                      </Button>
                                      <Button
                                        variant="contained"
                                        startIcon={<ChatRoundedIcon />}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          if (proposal.jobbeur_id) {
                                            // Stocker les informations dans le localStorage pour la modal de nouvelle conversation
                                            localStorage.setItem('newMessageInfo', JSON.stringify({
                                              recipientId: proposal.jobbeur_id,
                                              recipientName: proposal.jobbeur_profile?.prenom ? `${proposal.jobbeur_profile.prenom} ${proposal.jobbeur_profile.nom || ''}` : 'Jobbeur',
                                              initialMessage: `Bonjour, je vous contacte concernant la mission "${proposal.mission.titre}".`
                                            }));

                                            // Rediriger vers la page des messages
                                            navigate('/dashboard/messages');
                                          }
                                        }}
                                        sx={{
                                          borderRadius: '8px',
                                          textTransform: 'none',
                                          fontWeight: 600,
                                          backgroundColor: '#FF6B2C',
                                          '&:hover': {
                                            backgroundColor: '#E55A1F'
                                          }
                                        }}
                                      >
                                        Envoyer un message
                                      </Button>
                                    </Box>
                                  )}

                                  {/* Boutons de paiement pour les offres acceptées envoyées */}
                                  {proposal.statut === 'acceptée' && activeTab === 0 && (
                                    <>
                                      {proposal.payment_status === 'completed' ? (
                                        <>
                                          <Button
                                            variant="contained"
                                            startIcon={<CheckCircleOutlineIcon />}
                                          disabled
                                          sx={{
                                            borderRadius: '8px',
                                            textTransform: 'none',
                                            fontWeight: 600,
                                            backgroundColor: '#4CAF50',
                                            opacity: 0.7,
                                          }}
                                        >
                                            Echange de Jobi effectué le {new Date(proposal.payment_date!).toLocaleDateString()} - {proposal.montant_paiement === getLastNegotiatedAmount(proposal) ? 'Paiement total' : 'Paiement partiel'} ({proposal.montant_paiement} / {getLastNegotiatedAmount(proposal)} Jobi)
                                          </Button>
                                          {/* Bouton Déposer un avis - Placé ici */}
                                          <Button
                                            variant="contained"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleReviewClick(proposal.mission_id || '');
                                            }}
                                            className="bg-primary hover:bg-primary-dark text-white ml-1"
                                            startIcon={<RateReviewIcon />}
                                            sx={{
                                              backgroundColor: '#FF6B2C',
                                              textTransform: 'none',
                                              fontWeight: 600,
                                              '&:hover': {
                                                backgroundColor: '#FF965E'
                                              }
                                            }}
                                          >
                                            {hasReview[proposal.mission_id || ''] ? "Modifier l'avis" : "Déposer un avis"}
                                          </Button>
                                        </>
                                      ) : proposal.payment_status === 'manual' ? (
                                        <>
                                          <Button
                                            variant="contained"
                                            startIcon={<EuroIcon />}
                                          disabled
                                          sx={{
                                            borderRadius: '8px',
                                            textTransform: 'none',
                                            fontWeight: 600,
                                            backgroundColor: '#4CAF50',
                                            opacity: 0.7,
                                          }}
                                        >
                                            Paiement Manuel effectué le {new Date(proposal.payment_date!).toLocaleDateString()} - {proposal.montant_paiement === getLastNegotiatedAmount(proposal) ? 'Paiement total' : 'Paiement partiel'} ({proposal.montant_paiement} / {getLastNegotiatedAmount(proposal)}€)
                                          </Button>
                                          {/* Bouton Déposer un avis - Placé ici aussi */}
                                          <Button
                                            variant="outlined"
                                            startIcon={<RateReviewIcon />}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleReviewClick(proposal.mission_id || proposal.mission.id || '');
                                            }}
                                            sx={{
                                              borderRadius: '8px',
                                              textTransform: 'none',
                                              fontWeight: 600,
                                              color: '#FF6B2C',
                                              borderColor: '#FF6B2C',
                                              '&:hover': {
                                                backgroundColor: 'rgba(255, 107, 44, 0.08)',
                                                borderColor: '#E55A1F',
                                              },
                                              ml: 1 // Marge à gauche pour séparer du bouton de statut
                                            }}
                                          >
                                            {hasReview[proposal.mission_id || proposal.mission.id || ''] ? "Modifier l'avis" : "Déposer un avis"}
                                          </Button>
                                        </>
                                      ) : (
                                        <>
                                          <Button
                                            variant="contained"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handlePaymentRedirect(proposal);
                                            }}
                                            sx={{
                                              borderRadius: '8px',
                                              textTransform: 'none',
                                              fontWeight: 600,
                                              backgroundColor: '#4CAF50',
                                              '&:hover': {
                                                backgroundColor: '#3d8b40'
                                              },
                                              mr: 1
                                            }}
                                          >
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-5 w-5 mr-2">
                                              <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                                              <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                                              <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                                              <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                                            </svg>
                                            Echanger en Jobi ({proposal.montant_propose} Jobi)
                                          </Button>
                                          <Button
                                            variant="outlined"
                                            startIcon={<EuroIcon />}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              openManualPaymentConfirmation(proposal);
                                            }}
                                            sx={{
                                                borderRadius: '8px',
                                                textTransform: 'none',
                                                fontWeight: 600,
                                                backgroundColor: '#4CAF50',
                                                color: 'white',
                                                '&:hover': {
                                                  backgroundColor: '#3d8b40'
                                                },
                                                mr: 1
                                            }}
                                          >
                                            Paiement direct au jobbeur ({proposal.montant_propose}€)
                                          </Button>
                                        </>
                                      )}
                                    </>
                                  )}
                                </Box>
                              </Box>
                            </Box>

                            {/* Connecteur entre les offres */}
                            {index < selectedMissionProposals.length - 1 && (
                              <OfferConnector isLastItem={false} />
                            )}
                          </Box>
                        </React.Fragment>
                      );
                    })
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 4,
                        backgroundColor: '#FFF8F3',
                        borderRadius: '16px',
                        border: '1px dashed #FFE4BA',
                        margin: '20px auto',
                        width: '100%',
                        maxWidth: '600px'
                      }}
                    >
                      <SearchOffIcon
                        sx={{
                          fontSize: 80,
                          color: '#FF6B2C',
                          opacity: 0.7,
                          mb: 2
                        }}
                      />
                      <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'medium', mb: 1 }}>
                        Aucune offre trouvée
                      </Typography>
                      <Typography variant="body2" color="text.secondary" align="center">
                        {proposalStatusFilter === 'all'
                          ? "Vous n'avez pas encore d'offres pour cette mission."
                          : proposalStatusFilter === 'en_attente'
                            ? "Vous n'avez pas d'offres nécessitant votre attention."
                            : proposalStatusFilter === 'acceptée'
                              ? "Vous n'avez pas encore accepté d'offres pour cette mission."
                              : "Vous n'avez pas encore refusé d'offres pour cette mission."
                        }
                      </Typography>
                      {proposalStatusFilter !== 'all' && (
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => setProposalStatusFilter('all')}
                          sx={{
                            mt: 2,
                            color: '#FF6B2C',
                            borderColor: '#FF6B2C',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              borderColor: '#FF6B2C'
                            }
                          }}
                        >
                          Voir toutes les offres
                        </Button>
                      )}
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </Box>
        </ModalPortal>
      )}

      {/* Modal de confirmation de paiement manuel */}
      <Dialog
        open={isManualPaymentModalOpen}
        onClose={closeManualPaymentConfirmation}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          style: {
            borderRadius: '12px',
            padding: '24px'
          }
        }}
      >
        <DialogTitle sx={{
          fontSize: '1.25rem',
          fontWeight: 'bold',
          color: '#4CAF50',
          p: 0,
          mb: 2
        }}>
          Confirmation de paiement manuel
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Veuillez confirmer que vous avez effectué un paiement hors jobi au jobbeur et indiquer le montant payé.
          </Typography>

          <TextField
            autoFocus
            margin="dense"
            label="Montant payé"
            type="number"
            fullWidth
            variant="outlined"
            value={manualPaymentAmount}
            onChange={(e) => setManualPaymentAmount(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">€</InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <Alert severity="info" sx={{ mb: 2 }}>
            Cette action est irréversible. Le montant indiqué sera enregistré comme le montant réel du paiement.
          </Alert>
        </DialogContent>
        <DialogActions sx={{ p: 0, mt: 2 }}>
          <Button
            onClick={closeManualPaymentConfirmation}
            sx={{
              color: 'gray',
              '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={confirmManualPayment}
            variant="contained"
            sx={{
              backgroundColor: '#4CAF50',
              '&:hover': { backgroundColor: '#3d8b40' }
            }}
          >
            Confirmer le paiement
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale pour déposer un avis */}
      {reviewData && (
        <ReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => {
            setIsReviewModalOpen(false);
            setReviewData(null);
          }}
          mission_id={reviewData.missionId}
          userId={reviewData.jobbeurId}
          onReviewAdded={() => {
            // Mettre à jour le state hasReview pour cette mission
            setHasReview(prev => ({...prev, [reviewData.missionId]: true}));
          }}
          reviewToEdit={selectedReview ? {
            id: selectedReview.id,
            note: selectedReview.note,
            commentaire: selectedReview.commentaire,
            qualites: selectedReview.qualites || []
          } : undefined}
        />
      )}
    </Container>
  );
};

export default OffresPage;