import React from 'react';
import {
  But<PERSON>,
  Typo<PERSON>,
  Box,
  IconButton,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import ModalPortal from '@/components/ModalPortal';
import { setCookie } from '@/utils/cookieUtils';

const COOKIE_NAME_PREFIX = 'stepGuideShown_';

const StyledModal = styled('div')(({ theme }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  borderRadius: '16px',
  padding: '4px',
  backgroundColor: '#FFF8F3',
  maxWidth: '1280px',
  width: '94%',
  margin: '0px',
  maxHeight: '90vh',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  zIndex: 1100,
}));

const StyledModalTitle = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  fontSize: '1.3rem',
  fontWeight: 'bold',
  color: '#FF6B2C',
  padding: '16px',
  '& .MuiIconButton-root': {
    color: '#FF6B2C',
  },
});

const ContentBox = styled(Box)({
  backgroundColor: '#FFFFFF',
  borderRadius: '8px',
  padding: '16px',
  marginTop: '16px',
  border: '1px solid #FFE4BA',
});

interface StepContent {
  title: string;
  description: string;
  details: string[];
  tips: string[];
}

const stepContents: { [key: number]: StepContent } = {
  1: {
    title: 'Description détaillée de votre mission',
    description: 'Cette étape est cruciale pour attirer les meilleurs jobbeurs. Une description détaillée permet aux jobbeurs de comprendre exactement vos attentes.',
    details: [
      'Donnez un titre clair et accrocheur qui résume bien votre besoin',
      'Décrivez précisément les tâches à effectuer, leur ordre et leur importance',
      'Mentionnez le matériel nécessaire, ce que vous fournissez et ce que le jobbeur doit apporter',
      'Indiquez si des compétences ou qualifications particulières sont requises',
      'Précisez l\'état actuel (pour le jardinage par exemple : hauteur de la pelouse, surface à tondre)',
      'Ajoutez des photos si possible pour illustrer la situation'
    ],
    tips: [
      'Soyez le plus précis possible dans votre description pour éviter les malentendus',
      'N\'oubliez pas de mentionner les contraintes particulières (accès difficile, animaux présents...)',
      'Plus votre description est détaillée, plus les devis seront précis et adaptés',
      'Évitez les descriptions trop vagues comme "petit travail de jardinage"'
    ],
  },
  2: {
    title: 'Localisation de votre mission',
    description: 'La localisation est importante pour trouver des jobbeurs disponibles dans votre secteur et optimiser les déplacements.',
    details: [
      'Utilisez la géolocalisation pour plus de précision et gagner du temps',
      'Vous pouvez rechercher une adresse manuellement si vous préférez',
      'Précisez s\'il y a des particularités d\'accès (parking, ascenseur...)'
    ],
    tips: [
      'Vérifiez que l\'adresse est bien exacte pour éviter tout problème le jour J',
      'Pensez à ajouter des indications d\'accès précises si nécessaire',
      'Les jobbeurs ne verront que votre ville pour protéger votre vie privée'
    ],
  },
  3: {
    title: 'Budget de votre mission',
    description: 'Le budget est un élément clé qui détermine la qualité des propositions que vous recevrez. Un budget juste et bien calculé attire les meilleurs jobbeurs.',
    details: [
      'Fixez un budget réaliste en fonction de la complexité de la mission',
      'Tenez compte de la durée estimée et des compétences requises',
      'Incluez les frais éventuels (matériel, déplacement, produits)',
      'Précisez si le budget est négociable ou fixe',
      'Considérez les tarifs moyens du marché pour ce type de service'
    ],
    tips: [
      'Consultez les prix moyens des missions similaires sur la plateforme',
      'Un budget trop bas risque de ne pas attirer des jobbeurs qualifiés',
      'Vous pouvez ajuster le budget selon les devis reçus et négocier',
      'Pensez à prévoir une marge pour les imprévus'
    ],
  },
  4: {
    title: 'Planification des horaires',
    description: 'Une bonne planification temporelle est essentielle pour le succès de votre mission. Plus vous êtes flexible, plus vous aurez de propositions.',
    details: [
      'Ajoutez plusieurs créneaux possibles pour maximiser vos chances',
      'Précisez la durée estimée de chaque tâche de la mission',
      'Indiquez si les horaires sont flexibles ou stricts',
      'Mentionnez si la mission est urgente ou peut être reportée',
      'Précisez si la mission peut être réalisée en plusieurs fois',
      'Option "Mission urgente" : Cochez cette case si votre mission nécessite une intervention rapide. Elle sera mise en avant sur la plateforme'
    ],
    tips: [
      'Proposer plusieurs créneaux augmente significativement vos chances de trouver un jobbeur',
      'Pensez à inclure des créneaux en soirée ou le week-end pour plus de flexibilité',
      'Estimez la durée avec une marge raisonnable pour éviter les dépassements',
      'Tenez compte des conditions météo pour les missions en extérieur'
    ],
  },
  5: {
    title: 'Ajout de photos',
    description: 'Les photos permettent aux jobbeurs de mieux comprendre votre besoin et de faire des propositions plus précises.',
    details: [
      'Ajoutez des photos claires et bien éclairées',
      'Montrez différents angles si nécessaire',
      'Mettez en évidence les points importants ou problématiques',
      'Ajoutez des photos de l\'accès si pertinent'
    ],
    tips: [
      'Des photos de qualité augmentent vos chances d\'avoir des réponses',
      'Évitez les photos floues ou mal cadrées',
      'N\'hésitez pas à ajouter des annotations sur les photos',
      'Respectez la vie privée (pas de visages, de plaques d\'immatriculation...)'
    ],
  }
};

interface StepGuideModalProps {
  open: boolean;
  onClose: () => void;
  step: number;
}

const StepGuideModal: React.FC<StepGuideModalProps> = ({
  open,
  onClose,
  step,
}) => {
  const content = stepContents[step];
  const [dontShowAgain, setDontShowAgain] = React.useState(false);

  if (!open || !content) return null;

  const handleClose = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    if (dontShowAgain) {
      setCookie(`${COOKIE_NAME_PREFIX}${step}`, 'true', 365 * 24 * 60 * 60);
    }
    onClose();
  };

  return (
    <ModalPortal onBackdropClick={handleClose}>
      <StyledModal onClick={(e) => e.stopPropagation()}>
        <StyledModalTitle>
          <Typography variant="h6" component="h2">
            {content.title}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </StyledModalTitle>

        <Box sx={{ 
          flex: 1, 
          overflowY: 'auto', 
          padding: '16px' 
        }}>
          <Typography variant="body1" component="div" paragraph>
            {content.description}
          </Typography>

          <ContentBox>
            <Typography variant="subtitle2" color="#FF6B2C" gutterBottom>
              Points clés :
            </Typography>
            <Box component="ul" sx={{ mb: 2 }}>
              {content.details.map((detail, i) => (
                <Typography component="li" key={i} sx={{ mb: 1 }}>
                  {detail}
                </Typography>
              ))}
            </Box>

            <Typography variant="subtitle2" color="#FF6B2C" gutterBottom>
              Conseils :
            </Typography>
            <Box component="ul" sx={{ mb: 0 }}>
              {content.tips.map((tip, i) => (
                <Typography component="li" key={i} sx={{ mb: 1 }}>
                  {tip}
                </Typography>
              ))}
            </Box>
          </ContentBox>
        </Box>

        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px',
          borderTop: '1px solid #FFE4BA',
          backgroundColor: '#FFF8F3'
        }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={dontShowAgain}
                onChange={(e) => setDontShowAgain(e.target.checked)}
                sx={{
                  color: '#FF6B2C',
                  '&.Mui-checked': {
                    color: '#FF6B2C',
                  },
                }}
              />
            }
            label="Ne plus afficher ce guide"
          />
          <Button
            onClick={handleClose}
            variant="contained"
            sx={{
              backgroundColor: '#FF6B2C',
              '&:hover': {
                backgroundColor: '#FF965E',
              },
            }}
          >
            J'ai compris
          </Button>
        </Box>
      </StyledModal>
    </ModalPortal>
  );
};

export default StepGuideModal;
