import React from 'react';
import { Box, Typography, Button, Paper, useTheme, useMediaQuery } from '@mui/material';
import BugReportList from '../components/BugReport/BugReportList';
import { Link } from 'react-router-dom';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import BugReportIcon from '@mui/icons-material/BugReport';
import ListAltIcon from '@mui/icons-material/ListAlt';

const BugReportListPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box sx={{ p: { xs: 1.5, sm: 3 } }}>
      {/* Titre et boutons d'action */}
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between', 
          alignItems: { xs: 'flex-start', md: 'center' }, 
          gap: { xs: 2, md: 0 },
          mb: 3
        }}
      >
        <Box sx={{ display: 'flex', alignItems: { xs: 'flex-start', sm: 'center' }, flexDirection: { xs: 'column', sm: 'row' } }}>
          <ListAltIcon 
            sx={{ 
              color: '#FF6B2C', 
              fontSize: { xs: '1.8rem', sm: '2.2rem' }, 
              mr: { xs: 0, sm: 2 },
              mb: { xs: 1, sm: 0 },
              filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
            }} 
          />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 700, 
              color: '#333',
              fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: { xs: '40px', sm: '60px' },
                height: '3px',
                background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                borderRadius: '2px'
              }
            }}
          >
            {isMobile ? 'Rapports' : 'Rapports de bugs et suggestions'}
          </Typography>
        </Box>

        <Box 
          sx={{ 
            display: 'flex', 
            gap: 1.5,
            flexWrap: 'wrap',
            width: { xs: '100%', md: 'auto' },
            justifyContent: { xs: 'stretch', sm: 'flex-start', md: 'flex-end' }
          }}
        >
          <Button
            component={Link}
            to="/dashboard/bug-reports/nouveau"
            variant="contained"
            fullWidth={isMobile}
            startIcon={<AddCircleOutlineIcon />}
            sx={{ 
              bgcolor: '#FF6B2C',
              fontWeight: 600,
              px: 2,
              py: 1,
              borderRadius: '8px',
              boxShadow: '0 4px 10px rgba(255, 107, 44, 0.25)',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: '#FF7A35',
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 12px rgba(255, 107, 44, 0.3)',
              },
              '&:active': {
                transform: 'translateY(0)',
                boxShadow: '0 2px 6px rgba(255, 107, 44, 0.3)',
              }
            }}
          >
            Créer un nouveau rapport
          </Button>
        </Box>
      </Box>

      {/* Carte pour le contenu principal */}
      <Paper
        elevation={3}
        sx={{
          borderRadius: { xs: '8px', sm: '12px' },
          overflow: 'hidden',
          boxShadow: '0 5px 20px rgba(0, 0, 0, 0.05)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
          }
        }}
      >
        <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
          <BugReportList isAdmin={false} />
        </Box>
      </Paper>
    </Box>
  );
};

export default BugReportListPage; 