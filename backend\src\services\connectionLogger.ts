import { redis } from '../config/redis';
import { supabase } from '../config/supabase';

interface ConnectionLog {
  userId: string;
  ip: string;
  userAgent: string;
  timestamp: number;
  status: 'success' | 'failure';
  location?: string;
  device?: string;
}

export class ConnectionLoggerService {
  private static readonly PREFIX = 'connection_log:';
  private static readonly LOG_EXPIRY = 30 * 24 * 60 * 60; // 30 jours en secondes

  /**
   * Enregistre une tentative de connexion
   */
  static async logConnection(log: ConnectionLog): Promise<void> {
    try {
      // Enregistrer dans Redis pour les vérifications rapides
      const key = `${this.PREFIX}${log.userId}:${log.ip}:${Date.now()}`;
      await redis.setex(key, this.LOG_EXPIRY, JSON.stringify(log));

      console.info('Enregistrement du log de connexion:', log);

      // Enregistrer dans Supabase pour la persistance
      await supabase.from('security_logs').insert({
        type: 'CONNECTION_ATTEMPT',
        severity: log.status === 'failure' ? 'high' : 'low',
        user_id: log.userId || null,
        ip_address: log.ip,
        user_agent: log.userAgent,
        message: `Tentative de connexion ${log.status === 'success' ? 'réussie' : 'échouée'}`,
        details: {
          status: log.status,
          location: log.location,
          device: log.device,
          timestamp: log.timestamp
        }
      });
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du log de connexion:', error);
    }
  }

  /**
   * Récupère l'historique des connexions d'un utilisateur
   */
  static async getUserConnectionHistory(userId: string): Promise<ConnectionLog[]> {
    try {
      const { data, error } = await supabase
        .from('security_logs')
        .select('*')
        .eq('type', 'CONNECTION_ATTEMPT')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      return data.map(log => ({
        userId: log.user_id,
        ip: log.ip_address,
        userAgent: log.user_agent,
        timestamp: new Date(log.created_at).getTime(),
        status: log.details.status,
        location: log.details.location,
        device: log.details.device
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des connexions:', error);
      return [];
    }
  }

  /**
   * Récupère l'historique des connexions pour une IP
   */
  static async getIPConnectionHistory(ip: string): Promise<ConnectionLog[]> {
    try {
      const { data, error } = await supabase
        .from('security_logs')
        .select('*')
        .eq('type', 'CONNECTION_ATTEMPT')
        .eq('ip_address', ip)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      return data.map(log => ({
        userId: log.user_id,
        ip: log.ip_address,
        userAgent: log.user_agent,
        timestamp: new Date(log.created_at).getTime(),
        status: log.details.status,
        location: log.details.location,
        device: log.details.device
      }));
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des connexions IP:', error);
      return [];
    }
  }

  /**
   * Nettoie les anciens logs
   */
  static async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      const expiryTime = now - (this.LOG_EXPIRY * 1000);

      // Supprimer les logs Redis expirés
      const keys = await redis.keys(`${this.PREFIX}*`);
      for (const key of keys) {
        const log = await redis.get(key);
        if (log) {
          const { timestamp } = JSON.parse(log);
          if (timestamp < expiryTime) {
            await redis.del(key);
          }
        }
      }

      // Supprimer les logs Supabase expirés
      const { error } = await supabase
        .from('security_logs')
        .delete()
        .eq('type', 'CONNECTION_ATTEMPT')
        .lt('created_at', new Date(expiryTime).toISOString());

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Erreur lors du nettoyage des logs:', error);
    }
  }
}

export default ConnectionLoggerService;