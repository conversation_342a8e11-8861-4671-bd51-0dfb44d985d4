import React from 'react';
import { DynamicIcon } from './Animations';

interface LoadingBarProps {
  title: string;
  subtitle?: string;
  icon?: string;
  iconColor?: string;
}

const LoadingBar: React.FC<LoadingBarProps> = ({ 
  title, 
  subtitle,
  icon = "Loader2",
  iconColor = "#FF7A35"
}) => {
  return (
    <div className="text-center space-y-6">
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes loading {
          0% { width: 0; left: 0; }
          50% { width: 100%; left: 0; }
          100% { width: 0; left: 100%; }
        }
        .animate-loading-bar {
          animation: loading 2s infinite ease-in-out;
        }
      `}} />
      <div className="relative mb-8">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-24 h-24 rounded-full bg-orange-100 animate-pulse"></div>
        </div>
        <DynamicIcon name={icon} size={24} className="mx-auto h-16 w-16 text-[#FF7A35] relative z-10 animate-spin" />
      </div>
      <h2 className="text-3xl font-extrabold bg-gradient-to-r from-[#FF7A35] to-[#ff965e] bg-clip-text text-transparent">
        Vérification en cours...
      </h2>
      <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
      {subtitle && <p className="text-gray-500">{subtitle}</p>}
      <div className="flex justify-center mt-4">
        <div className="relative w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div className="absolute top-0 left-0 h-full bg-[#FF7A35] animate-loading-bar" />
        </div>
      </div>
    </div>
  );
};

export default LoadingBar;
