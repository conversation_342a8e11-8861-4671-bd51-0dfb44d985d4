import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Pagination,
  Alert,
  CircularProgress,
  Collapse,
  styled
} from '@mui/material';
import {
  History,
  RefreshCw,
  Filter,
  Search,
  Info,
  User,
  Activity,
  Globe,
  ChevronDown,
  ChevronRight,
  Eye,
  Clock
} from 'lucide-react';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C',
  secondary: '#FF7A35',
  tertiary: '#FF965E',
  background: '#FFF8F3',
  accent: '#FFE4BA',
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
  info: '#2196F3',
  neutral: '#64748B',
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  overflow: 'hidden'
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.2rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  position: 'relative',
  paddingLeft: theme.spacing(1),
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '4px',
    height: '20px',
    backgroundColor: COLORS.primary,
    borderRadius: '2px',
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  border: `1px solid ${COLORS.borderColor}`,
  '& .MuiTableHead-root': {
    backgroundColor: `${COLORS.primary}08`,
  },
  '& .MuiTableCell-head': {
    fontWeight: 600,
    color: COLORS.primary,
    borderBottom: `2px solid ${COLORS.borderColor}`,
  },
  '& .MuiTableRow-root:hover': {
    backgroundColor: `${COLORS.background}80`,
  },
}));

const ActionTypeChip = styled(Chip)<{ actiontype: string }>(({ theme, actiontype }) => {
  const getActionColor = (type: string) => {
    if (type.includes('connexion') || type.includes('login')) return COLORS.success;
    if (type.includes('deconnexion') || type.includes('logout')) return COLORS.neutral;
    if (type.includes('mission')) return COLORS.info;
    if (type.includes('jobi') || type.includes('payment')) return COLORS.warning;
    if (type.includes('profile') || type.includes('profil')) return COLORS.secondary;
    if (type.includes('review') || type.includes('avis')) return COLORS.tertiary;
    if (type.includes('admin')) return COLORS.error;
    return COLORS.primary;
  };

  const color = getActionColor(actiontype);
  
  return {
    backgroundColor: `${color}15`,
    color: color,
    fontWeight: 500,
    fontSize: '0.75rem',
    borderRadius: '6px',
    border: `1px solid ${color}30`,
  };
});

interface ActivityHistoryItem {
  id: string;
  action_type: string;
  action_date: string;
  resource_id?: string;
  resource_type?: string;
  details?: any;
  ip_address?: string;
  formatted_date: string;
  parsed_details?: any;
}

interface UserActivityHistoryProps {
  userId: string;
}

const UserActivityHistory: React.FC<UserActivityHistoryProps> = ({ userId }) => {
  const [activities, setActivities] = useState<ActivityHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [limit] = useState(20);
  const [selectedType, setSelectedType] = useState('all');
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [userInfo, setUserInfo] = useState<{ id: string; email: string } | null>(null);

  // Fonction pour récupérer l'historique des activités
  const fetchActivityHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        type: selectedType
      });

      const response = await fetch(
        `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/actions-history?${params}`,
        {
          method: 'GET',
          headers: headers,
          credentials: 'include'
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setActivities(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
        setTotal(data.pagination?.total || 0);
        setAvailableTypes(data.filters?.availableTypes || []);
        setUserInfo(data.user || null);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des données');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchActivityHistory();
    }
  }, [userId, page, selectedType]);

  // Fonction pour formater le type d'action
  const formatActionType = (actionType: string) => {
    const actionLabels: Record<string, string> = {
      'connexion_utilisateur': 'Connexion',
      'deconnexion_utilisateur': 'Déconnexion',
      'creation_mission': 'Création de mission',
      'modification_mission': 'Modification de mission',
      'candidature_mission': 'Candidature à une mission',
      'jobi_transaction': 'Transaction Jobi',
      'profile_update': 'Mise à jour du profil',
      'admin_view_user_history': 'Affichage de l\'historique de l\'utilisateur',
      'review_response': 'Réponse à un avis',
      'favorite_add': 'Ajout aux favoris',
      'favorite_remove': 'Suppression des favoris',
      'admin_action': 'Action administrative',
      'password_change': 'Changement de mot de passe',
      'email_verification': 'Vérification email',
      'subscription_change': 'Changement d\'abonnement',
      'ai_credits_usage': 'Utilisation crédits IA',
      'photo_upload': 'Upload de photo',
      'message_sent': 'Message envoyé',
      'notification_read': 'Notification lue',
      'admin_photo_moderation': 'Modération de la photo de profil',
      'demande_suppression_compte': 'Demande de suppression de compte',
      'profile_banner_update': 'Mise à jour de la bannière de profil',
      'generate_random_card_template': 'Génération d\'un modèle de carte aléatoire',
      'auto_deactivate_card_template': 'Désactivation automatique du modèle de carte',
      'update_card_template': 'Mise à jour du modèle de carte'
    };

    return actionLabels[actionType] || actionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Fonction pour obtenir l'icône selon le type d'action
  const getActionIcon = (actionType: string) => {
    if (actionType.includes('connexion') || actionType.includes('login')) return <User size={16} />;
    if (actionType.includes('mission')) return <Activity size={16} />;
    if (actionType.includes('jobi') || actionType.includes('payment')) return <Globe size={16} />;
    if (actionType.includes('profile') || actionType.includes('profil')) return <User size={16} />;
    if (actionType.includes('admin')) return <Eye size={16} />;
    return <History size={16} />;
  };

  // Fonction pour basculer l'expansion d'une ligne
  const toggleRowExpansion = (id: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedRows(newExpanded);
  };

  // Filtrer les activités selon le terme de recherche
  const filteredActivities = activities.filter(activity =>
    activity.action_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formatActionType(activity.action_type).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (activity.resource_type && activity.resource_type.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading && activities.length === 0) {
    return (
      <StyledCard>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <Box textAlign="center">
              <CircularProgress sx={{ color: COLORS.primary, mb: 2 }} />
              <Typography variant="body2" color="text.secondary">
                Chargement de l'historique des activités...
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </StyledCard>
    );
  }

  if (error) {
    return (
      <StyledCard>
        <CardContent>
          <Alert 
            severity="error" 
            sx={{ 
              borderRadius: '12px',
              border: `1px solid ${COLORS.error}20`
            }}
            action={
              <Button 
                color="inherit" 
                size="small" 
                onClick={fetchActivityHistory}
                startIcon={<RefreshCw size={16} />}
              >
                Réessayer
              </Button>
            }
          >
            {error}
          </Alert>
        </CardContent>
      </StyledCard>
    );
  }

  return (
    <StyledCard>
      <CardContent sx={{ p: 0 }}>
        {/* En-tête avec informations utilisateur */}
        <Box sx={{ p: 3, borderBottom: `1px solid ${COLORS.borderColor}`, backgroundColor: COLORS.background }}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <SectionTitle>Historique des activités</SectionTitle>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                icon={<History size={16} />}
                label={`${total} activité${total > 1 ? 's' : ''}`}
                sx={{
                  backgroundColor: `${COLORS.primary}15`,
                  color: COLORS.primary,
                  fontWeight: 600
                }}
              />
              <IconButton 
                onClick={fetchActivityHistory} 
                disabled={loading}
                sx={{ color: COLORS.primary }}
              >
                <RefreshCw size={20} />
              </IconButton>
            </Box>
          </Box>

          {userInfo && (
            <Box sx={{ 
              p: 2, 
              backgroundColor: COLORS.white, 
              borderRadius: '8px',
              border: `1px solid ${COLORS.borderColor}`,
              mb: 2
            }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Utilisateur concerné:
              </Typography>
              <Typography variant="body1" fontWeight={600}>
                {userInfo.email} (ID: {userInfo.id})
              </Typography>
            </Box>
          )}

          {/* Filtres et recherche */}
          <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Box sx={{ flex: '0 0 300px' }}>
              <FormControl fullWidth size="small">
                <InputLabel>Type d'activité</InputLabel>
                <Select
                  value={selectedType}
                  label="Type d'activité"
                  onChange={(e) => {
                    setSelectedType(e.target.value);
                    setPage(1);
                  }}
                  startAdornment={<Filter size={16} style={{ marginRight: 8, color: COLORS.neutral }} />}
                >
                  <MenuItem value="all">Toutes les activités</MenuItem>
                  {availableTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {formatActionType(type)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            <Box sx={{ flex: 1 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Rechercher dans les activités..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search size={16} style={{ marginRight: 8, color: COLORS.neutral }} />
                }}
              />
            </Box>
          </Box>
        </Box>

        {/* Tableau des activités */}
        <Box sx={{ p: 3 }}>
          {filteredActivities.length === 0 ? (
            <Box textAlign="center" py={6}>
              <History size={48} color={COLORS.neutral} style={{ marginBottom: 16 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Aucune activité trouvée
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm ? 'Aucun résultat pour votre recherche.' : 'Cet utilisateur n\'a pas encore d\'activité enregistrée.'}
              </Typography>
            </Box>
          ) : (
            <>
              <StyledTableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell width="40px"></TableCell>
                      <TableCell>Type d'activité</TableCell>
                      <TableCell>Date et heure</TableCell>
                      <TableCell>Ressource</TableCell>
                      <TableCell>Adresse IP</TableCell>
                      <TableCell width="60px">Détails</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredActivities.map((activity) => (
                      <React.Fragment key={activity.id}>
                        <TableRow>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => toggleRowExpansion(activity.id)}
                              sx={{ color: COLORS.primary }}
                            >
                              {expandedRows.has(activity.id) ? 
                                <ChevronDown size={16} /> : 
                                <ChevronRight size={16} />
                              }
                            </IconButton>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              {getActionIcon(activity.action_type)}
                              <ActionTypeChip
                                actiontype={activity.action_type}
                                label={formatActionType(activity.action_type)}
                                size="small"
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Clock size={14} color={COLORS.neutral} />
                              <Typography variant="body2">
                                {activity.formatted_date}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            {activity.resource_type && (
                              <Chip
                                label={activity.resource_type}
                                size="small"
                                variant="outlined"
                                sx={{
                                  borderColor: COLORS.borderColor,
                                  color: COLORS.neutral,
                                  fontSize: '0.7rem'
                                }}
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary" fontFamily="monospace">
                              {activity.ip_address || 'Inconnue'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {activity.parsed_details && (
                              <Tooltip title="Voir les détails">
                                <IconButton
                                  size="small"
                                  onClick={() => toggleRowExpansion(activity.id)}
                                  sx={{ color: COLORS.info }}
                                >
                                  <Info size={16} />
                                </IconButton>
                              </Tooltip>
                            )}
                          </TableCell>
                        </TableRow>
                        
                        {/* Ligne de détails expansible */}
                        <TableRow>
                          <TableCell colSpan={6} sx={{ p: 0, border: 'none' }}>
                            <Collapse in={expandedRows.has(activity.id)}>
                              <Box sx={{ 
                                p: 2, 
                                backgroundColor: COLORS.background,
                                borderTop: `1px solid ${COLORS.borderColor}`
                              }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ color: COLORS.primary }}>
                                  Détails de l'activité
                                </Typography>
                                
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                  <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
                                    <Box>
                                      <Typography variant="caption" color="text.secondary">
                                        ID de l'activité:
                                      </Typography>
                                      <Typography variant="body2" fontFamily="monospace">
                                        {activity.id}
                                      </Typography>
                                    </Box>
                                    
                                    {activity.resource_id && (
                                      <Box>
                                        <Typography variant="caption" color="text.secondary">
                                          ID de la ressource:
                                        </Typography>
                                        <Typography variant="body2" fontFamily="monospace">
                                          {activity.resource_id}
                                        </Typography>
                                      </Box>
                                    )}
                                  </Box>
                                  
                                  {activity.parsed_details && (
                                    <Box>
                                      <Typography variant="caption" color="text.secondary">
                                        Informations supplémentaires:
                                      </Typography>
                                      <Box sx={{ 
                                        mt: 1, 
                                        p: 2, 
                                        backgroundColor: COLORS.white,
                                        borderRadius: '8px',
                                        border: `1px solid ${COLORS.borderColor}`,
                                        fontFamily: 'monospace',
                                        fontSize: '0.8rem',
                                        overflow: 'auto'
                                      }}>
                                        <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                                          {JSON.stringify(activity.parsed_details, null, 2)}
                                        </pre>
                                      </Box>
                                    </Box>
                                  )}
                                </Box>
                              </Box>
                            </Collapse>
                          </TableCell>
                        </TableRow>
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                    sx={{
                      '& .MuiPaginationItem-root': {
                        color: COLORS.primary,
                        '&.Mui-selected': {
                          backgroundColor: COLORS.primary,
                          color: COLORS.white,
                        },
                      },
                    }}
                  />
                </Box>
              )}
            </>
          )}
        </Box>
      </CardContent>
    </StyledCard>
  );
};

export default UserActivityHistory; 