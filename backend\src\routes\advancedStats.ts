import { Router } from 'express';
import { getAdvancedStats } from '../controllers/advancedStats';
import { authMiddleware } from '../middleware/authMiddleware';
import { limiter } from '../middleware/security';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiter spécifique pour les statistiques avancées
const statsLimiter = rateLimit({
  windowMs: 30 * 1000, // 30 secondes
  max: 50, // 50 requêtes maximum
  message: {
    message: 'Trop de requêtes pour les statistiques. Veuillez réessayer dans 30 secondes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Route pour obtenir les statistiques avancées
router.get('/', authMiddleware.authenticateToken, limiter, statsLimiter, getAdvancedStats);

export default router; 