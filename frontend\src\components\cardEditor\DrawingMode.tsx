import React, { useState, useRef, useEffect } from 'react';
import { Line, Layer, Image } from 'react-konva';
import { Stage } from 'konva/lib/Stage';
import { KonvaEventObject } from 'konva/lib/Node';
import { DrawingElement } from '../../types/cardEditor';

interface DrawingModeProps {
  isActive: boolean;
  stageRef: React.RefObject<any>;
  templateWidth: number;
  templateHeight: number;
  onDrawingComplete: (element: DrawingElement) => void;
  tool: 'brush' | 'eraser' | 'polygon';
  strokeColor: string;
  strokeWidth: number;
  onPolygonComplete?: (element: DrawingElement) => void;
}

const DrawingMode: React.FC<DrawingModeProps> = ({
  isActive,
  stageRef,
  templateWidth,
  templateHeight,
  onDrawingComplete,
  tool,
  strokeColor,
  strokeWidth,
  onPolygonComplete
}) => {
  const [currentLine, setCurrentLine] = useState<number[] | null>(null);
  const [polygonPoints, setPolygonPoints] = useState<number[]>([]);
  const [isPolygonDrawing, setIsPolygonDrawing] = useState(false);
  const isDrawing = useRef(false);
  const lastTapRef = useRef(0);

  useEffect(() => {
    if (!isActive || !stageRef.current) return;
    const stage = stageRef.current;

    // --- Mode POLYGON ---
    if (tool === 'polygon') {
      const handlePolygonClick = (e: KonvaEventObject<MouseEvent | TouchEvent>) => {
        e.cancelBubble = true;
        const pos = stage.getPointerPosition();
        if (pos) {
          const scale = stage.scaleX ? stage.scaleX() : 1;
          const x = pos.x / scale;
          const y = pos.y / scale;
          setPolygonPoints((prev) => [...prev, x, y]);
          setIsPolygonDrawing(true);
        }
      };
      const handlePolygonDblClick = (e: KonvaEventObject<MouseEvent | TouchEvent>) => {
        e.cancelBubble = true;
        if (polygonPoints.length >= 6) { // au moins 3 points
          // Calculer minX/minY pour normaliser
          const xs = polygonPoints.filter((_, i) => i % 2 === 0);
          const ys = polygonPoints.filter((_, i) => i % 2 === 1);
          const minX = Math.min(...xs);
          const minY = Math.min(...ys);
          const normalized = polygonPoints.map((v, i) => (i % 2 === 0 ? v - minX : v - minY));
          const newPolygon: DrawingElement = {
            id: crypto.randomUUID(),
            type: 'drawing',
            x: minX,
            y: minY,
            properties: {
              points: normalized,
              stroke: strokeColor,
              strokeWidth: strokeWidth,
              tension: 0,
              tool: 'brush',
              opacity: 1
            }
          };
          if (onPolygonComplete) onPolygonComplete(newPolygon);
          setPolygonPoints([]);
          setIsPolygonDrawing(false);
        }
      };
      const handlePolygonTouch = (e: KonvaEventObject<TouchEvent>) => {
        e.cancelBubble = true;
        const now = Date.now();
        if (now - lastTapRef.current < 250) {
          // Double tap : fermer le polygone uniquement si assez de points
          if (polygonPoints.length >= 6) {
            handlePolygonDblClick(e);
          }
          // Ne pas ajouter de point !
        } else {
          // Simple tap : ajouter un point
          handlePolygonClick(e);
        }
        lastTapRef.current = now;
      };
      stage.on('mousedown', handlePolygonClick);
      stage.on('dblclick', handlePolygonDblClick);
      stage.on('touchstart', handlePolygonTouch);
      return () => {
        stage.off('mousedown', handlePolygonClick);
        stage.off('dblclick', handlePolygonDblClick);
        stage.off('touchstart', handlePolygonTouch);
      };
    }
    // --- FIN MODE POLYGON ---

    // --- Mode ERASER (Gomme) ---
    if (tool === 'eraser') {
      // Fonction pour gérer le début de l'effacement
      const handleEraseStart = (e: KonvaEventObject<MouseEvent>) => {
        e.cancelBubble = true;

        isDrawing.current = true;
        const pos = stage.getPointerPosition();
        if (pos) {
          const scale = stage.scaleX ? stage.scaleX() : 1;
          setCurrentLine([pos.x / scale, pos.y / scale]);
        }
      };

      // Fonction pour gérer le mouvement pendant l'effacement
      const handleEraseMove = (e: KonvaEventObject<MouseEvent>) => {
        if (!isActive || !isDrawing.current || !currentLine) return;

        e.cancelBubble = true;

        const pos = stage.getPointerPosition();
        if (pos) {
          const scale = stage.scaleX ? stage.scaleX() : 1;
          const newPoints = [...currentLine, pos.x / scale, pos.y / scale];
          setCurrentLine(newPoints);
        }
      };

      // Fonction pour gérer la fin de l'effacement
      const handleEraseEnd = (e: KonvaEventObject<MouseEvent>) => {
        if (!isActive || !isDrawing.current || !currentLine) return;

        e.cancelBubble = true;
        isDrawing.current = false;

        // Créer un élément de type "eraser" qui sera utilisé pour effacer les pixels
        if (currentLine.length >= 4) { // Au moins 2 points (x1,y1,x2,y2)
          const minX = Math.min(...currentLine.filter((_, i) => i % 2 === 0));
          const minY = Math.min(...currentLine.filter((_, i) => i % 2 === 1));

          // Normaliser les points par rapport à la position (minX, minY)
          const normalizedPoints = [...currentLine];
          for (let i = 0; i < normalizedPoints.length; i += 2) {
            normalizedPoints[i] -= minX;
            normalizedPoints[i + 1] -= minY;
          }

          const eraserElement: DrawingElement = {
            id: crypto.randomUUID(),
            type: 'drawing',
            x: minX,
            y: minY,
            properties: {
              points: normalizedPoints,
              stroke: 'rgba(0,0,0,1)', // Noir pour l'effacement
              strokeWidth: strokeWidth,
              tension: 0.5,
              tool: 'eraser'
            }
          };

          // Ajouter l'élément au template
          onDrawingComplete(eraserElement);
        }

        setCurrentLine(null);
      };

      // Attacher les gestionnaires d'événements pour l'effacement
      stage.on('mousedown touchstart', handleEraseStart);
      stage.on('mousemove touchmove', handleEraseMove);
      stage.on('mouseup touchend', handleEraseEnd);

      return () => {
        stage.off('mousedown touchstart', handleEraseStart);
        stage.off('mousemove touchmove', handleEraseMove);
        stage.off('mouseup touchend', handleEraseEnd);
      };
    }
    // --- FIN MODE ERASER ---

    // --- Mode BRUSH (Pinceau) ---
    // Fonction pour gérer le début du dessin
    const handleMouseDown = (e: KonvaEventObject<MouseEvent>) => {
      if (!isActive) return;

      // Empêcher la propagation pour éviter la sélection d'autres éléments
      e.cancelBubble = true;

      isDrawing.current = true;
      const pos = stage.getPointerPosition();
      if (pos) {
        // Correction du zoom : on divise par le scale du stage
        const scale = stage.scaleX ? stage.scaleX() : 1;
        setCurrentLine([pos.x / scale, pos.y / scale]);
      }
    };

    // Fonction pour gérer le mouvement pendant le dessin
    const handleMouseMove = (e: KonvaEventObject<MouseEvent>) => {
      if (!isActive || !isDrawing.current || !currentLine) return;

      // Empêcher la propagation
      e.cancelBubble = true;

      const pos = stage.getPointerPosition();
      if (pos) {
        const scale = stage.scaleX ? stage.scaleX() : 1;
        setCurrentLine(prevLine => prevLine ? [...prevLine, pos.x / scale, pos.y / scale] : [pos.x / scale, pos.y / scale]);
      }
    };

    // Fonction pour gérer la fin du dessin
    const handleMouseUp = (e: KonvaEventObject<MouseEvent>) => {
      if (!isActive || !isDrawing.current || !currentLine) return;

      // Empêcher la propagation
      e.cancelBubble = true;

      isDrawing.current = false;

      // Créer un nouvel élément de dessin
      if (currentLine.length >= 4) { // Au moins 2 points (x1,y1,x2,y2)
        const minX = Math.min(...currentLine.filter((_, i) => i % 2 === 0));
        const minY = Math.min(...currentLine.filter((_, i) => i % 2 === 1));

        // Normaliser les points par rapport à la position (minX, minY)
        const normalizedPoints = [...currentLine];
        for (let i = 0; i < normalizedPoints.length; i += 2) {
          normalizedPoints[i] -= minX;
          normalizedPoints[i + 1] -= minY;
        }

        const newDrawing: DrawingElement = {
          id: crypto.randomUUID(),
          type: 'drawing',
          x: minX,
          y: minY,
          properties: {
            points: normalizedPoints,
            stroke: strokeColor,
            strokeWidth: strokeWidth,
            tension: 0.5,
            tool: 'brush'
          }
        };

        onDrawingComplete(newDrawing);
      }

      setCurrentLine(null);
    };

    // Attacher les gestionnaires d'événements
    stage.on('mousedown touchstart', handleMouseDown);
    stage.on('mousemove touchmove', handleMouseMove);
    stage.on('mouseup touchend', handleMouseUp);

    // Nettoyer les gestionnaires d'événements lors du démontage
    return () => {
      stage.off('mousedown touchstart', handleMouseDown);
      stage.off('mousemove touchmove', handleMouseMove);
      stage.off('mouseup touchend', handleMouseUp);
    };
    // --- FIN MODE BRUSH ---
  }, [isActive, stageRef, currentLine, strokeColor, strokeWidth, tool, onDrawingComplete, polygonPoints, onPolygonComplete, templateWidth, templateHeight]);

  // Si le mode dessin n'est pas actif, ne rien afficher
  if (!isActive) return null;

  // Affichage du polygone en cours
  if (tool === 'polygon') {
    return (
      <>{polygonPoints.length > 2 && (
        <Line
          points={polygonPoints}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          closed={false}
          lineCap="round"
          lineJoin="round"
        />
      )}</>
    );
  }

  // Affichage pour le mode gomme
  if (tool === 'eraser') {
    return (
      <>
        {currentLine && (
          <Line
            points={currentLine}
            stroke="rgba(255, 0, 0, 0.5)"  // Indicateur visuel rouge semi-transparent
            strokeWidth={strokeWidth}
            tension={0.5}
            lineCap="round"
            lineJoin="round"
          />
        )}
      </>
    );
  }

  // Affichage pour le mode pinceau
  return (
    <>
      {currentLine && (
        <Line
          points={currentLine}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          tension={0.5}
          lineCap="round"
          lineJoin="round"
        />
      )}
    </>
  );
};

export default DrawingMode;
