import React, { useMemo } from 'react';
import { validatePassword } from '../../utils/passwordValidator';
import DOMPurify from 'dompurify';

interface VerifierForceMotDePasseProps {
  motDePasse: string;
}

const VerifierForceMotDePasse: React.FC<VerifierForceMotDePasseProps> = ({ motDePasse }) => {
  const sanitizedMotDePasse = DOMPurify.sanitize(motDePasse);
  const resultat = useMemo(() => validatePassword(sanitizedMotDePasse), [sanitizedMotDePasse]);

  const obtenirEtiquetteForce = (score: number): string => {
    switch (score) {
      case 0:
        return 'Très faible';
      case 1:
        return 'Faible';
      case 2:
        return 'Moyen';
      case 3:
        return 'Fort';
      case 4:
        return 'Très fort';
      default:
        return 'Très faible';
    }
  };

  const obtenirCouleurForce = (score: number): string => {
    switch (score) {
      case 0:
        return 'bg-red-500';
      case 1:
        return 'bg-orange-500';
      case 2:
        return 'bg-yellow-500';
      case 3:
        return 'bg-green-500';
      case 4:
        return 'bg-emerald-500';
      default:
        return 'bg-red-500';
    }
  };

  const obtenirLargeurBarre = (score: number): string => {
    const pourcentage = ((score + 1) * 20);
    return `${pourcentage}%`;
  };

  return (
    <div className="mt-2">
      <div className="flex items-center mb-1">
        <div className="flex-1 h-2 bg-gray-200 rounded">
          <div
            className={`h-2 rounded transition-all duration-300 ${obtenirCouleurForce(resultat.score)}`}
            style={{ width: obtenirLargeurBarre(resultat.score) }}
          ></div>
        </div>
        <span className="ml-2 text-sm text-gray-600">
          {obtenirEtiquetteForce(resultat.score)}
        </span>
      </div>
      {resultat.feedback.warning && (
        <p className="text-sm text-red-500 mt-1">{resultat.feedback.warning}</p>
      )}
      {resultat.feedback.suggestions.length > 0 && (
        <ul className="text-sm text-gray-600 mt-1 list-disc list-inside">
          {resultat.feedback.suggestions.map((suggestion, index) => (
            <li key={index}>{suggestion}</li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default VerifierForceMotDePasse;
