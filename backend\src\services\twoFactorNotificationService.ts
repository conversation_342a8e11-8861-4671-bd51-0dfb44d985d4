import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

/**
 * Crée une notification pour l'activation de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const createTwoFactorEnabledNotification = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: 'system',
        title: 'Authentification à deux facteurs activée',
        content: 'L\'authentification à deux facteurs a été activée sur votre compte. Votre compte est maintenant mieux protégé.',
        link: '/dashboard/profil/securite',
        is_read: false,
        is_archived: false
      })
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la création de la notification d\'activation 2FA:', error);
      return false;
    }

    // Invalider le cache Redis pour le compteur de notifications
    const cacheKey = `notifications_count:${userId}`;
    await redis.del(cacheKey);

    // Invalider le cache des notifications
    const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
    if (notificationsCacheKeys.length > 0) {
      await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
    }

    logger.info('Notification d\'activation 2FA créée avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de la création de la notification d\'activation 2FA:', error);
    return false;
  }
};

/**
 * Crée une notification pour la désactivation de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const createTwoFactorDisabledNotification = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: 'system',
        title: 'Authentification à deux facteurs désactivée',
        content: 'L\'authentification à deux facteurs a été désactivée sur votre compte. Votre compte est maintenant moins sécurisé.',
        link: '/dashboard/profil/securite',
        is_read: false,
        is_archived: false
      })
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la création de la notification de désactivation 2FA:', error);
      return false;
    }

    // Invalider le cache Redis pour le compteur de notifications
    const cacheKey = `notifications_count:${userId}`;
    await redis.del(cacheKey);

    // Invalider le cache des notifications
    const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
    if (notificationsCacheKeys.length > 0) {
      await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
    }

    logger.info('Notification de désactivation 2FA créée avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de la création de la notification de désactivation 2FA:', error);
    return false;
  }
};

/**
 * Ajoute une entrée dans l'historique de l'utilisateur pour l'activation de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const addTwoFactorEnabledToHistory = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_actions_history')
      .insert({
        user_id: userId,
        action_type: 'two_factor_enabled',
        details: {
          timestamp: new Date().toISOString(),
          action: 'Activation de l\'authentification à deux facteurs'
        },
        action_date: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'ajout de l\'activité d\'activation 2FA à l\'historique:', error);
      return false;
    }

    logger.info('Activité d\'activation 2FA ajoutée à l\'historique avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout de l\'activité d\'activation 2FA à l\'historique:', error);
    return false;
  }
};

/**
 * Ajoute une entrée dans l'historique de l'utilisateur pour la désactivation de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const addTwoFactorDisabledToHistory = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_actions_history')
      .insert({
        user_id: userId,
        action_type: 'two_factor_disabled',
        details: {
          timestamp: new Date().toISOString(),
          action: 'Désactivation de l\'authentification à deux facteurs'
        },
        action_date: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'ajout de l\'activité de désactivation 2FA à l\'historique:', error);
      return false;
    }

    logger.info('Activité de désactivation 2FA ajoutée à l\'historique avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout de l\'activité de désactivation 2FA à l\'historique:', error);
    return false;
  }
};

/**
 * Crée une notification pour la vérification de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const createTwoFactorVerifiedNotification = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type: 'system',
        title: 'Authentification à deux facteurs vérifiée',
        content: 'L\'authentification à deux facteurs a été vérifiée avec succès sur votre compte. Votre compte est maintenant protégé par une couche de sécurité supplémentaire.',
        link: '/dashboard/profil/securite',
        is_read: false,
        is_archived: false
      })
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la création de la notification de vérification 2FA:', error);
      return false;
    }

    // Invalider le cache Redis pour le compteur de notifications
    const cacheKey = `notifications_count:${userId}`;
    await redis.del(cacheKey);

    // Invalider le cache des notifications
    const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
    if (notificationsCacheKeys.length > 0) {
      await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
    }

    logger.info('Notification de vérification 2FA créée avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de la création de la notification de vérification 2FA:', error);
    return false;
  }
};

/**
 * Ajoute une entrée dans l'historique de l'utilisateur pour la vérification de l'authentification à deux facteurs
 * @param userId ID de l'utilisateur
 */
export const addTwoFactorVerifiedToHistory = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_actions_history')
      .insert({
        user_id: userId,
        action_type: 'two_factor_verified',
        details: {
          timestamp: new Date().toISOString(),
          action: 'Vérification de l\'authentification à deux facteurs'
        },
        action_date: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'ajout de l\'activité de vérification 2FA à l\'historique:', error);
      return false;
    }

    logger.info('Activité de vérification 2FA ajoutée à l\'historique avec succès', { userId });
    return true;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout de l\'activité de vérification 2FA à l\'historique:', error);
    return false;
  }
};
