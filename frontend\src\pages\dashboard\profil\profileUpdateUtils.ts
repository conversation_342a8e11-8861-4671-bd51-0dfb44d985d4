import axios from 'axios';
import DOMPurify from 'dompurify';
import logger from '../../../utils/logger';
import { notify } from '../../../components/Notification';
import { API_CONFIG } from '../../../config/api';
import { getCommonHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';
import { ProfilData } from './profileUtils';
import { debounce } from 'lodash';

// Fonction pour rechercher une adresse
export const searchAddress = debounce(async (
  query: string,
  setNumero: React.Dispatch<React.SetStateAction<string>>,
  setRue: React.Dispatch<React.SetStateAction<string>>,
  setVille: React.Dispatch<React.SetStateAction<string>>,
  setCodePostal: React.Dispatch<React.SetStateAction<string>>,
  setPays: React.Dispatch<React.SetStateAction<string>>
) => {
  if (!query || query.length < 3) return;

  try {
    const response = await axios.get(
      `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(query)}&limit=1`
    );

    if (response.data.features && response.data.features.length > 0) {
      const address = response.data.features[0].properties;
      const [numero, ...rueParts] = address.name.split(' ');
      const rue = rueParts.join(' ');

      // Mise à jour des champs d'adresse
      let extractedNumero = '';
      let extractedRue = rue;

      // Extraire le numéro de la rue
      const numeroMatch = address.name.match(/^(\d+[a-z]?)/i);
      if (numeroMatch) {
        extractedNumero = numeroMatch[1];
        extractedRue = address.name.substring(numeroMatch[0].length).trim();
      }

      setNumero(extractedNumero);
      setRue(extractedRue);
      setVille(address.city);
      setCodePostal(address.postcode);
      setPays('France'); // Vous pouvez ajuster cela si nécessaire
    } else {
      notify('Aucune adresse trouvée, veuillez saisir une adresse plus complète ou la saisir manuellement.', 'warning');
    }
  } catch (error) {
    logger.error('Erreur lors de la recherche d\'adresse:', error);
    notify('Erreur lors de la recherche d\'adresse', 'error');
  }
}, 500);

// Fonction pour confirmer l'adresse
export const handleAddressConfirm = (
  numero: string,
  rue: string,
  ville: string,
  codePostal: string,
  pays: string,
  validComplements: string[],
  setTempAddress: React.Dispatch<React.SetStateAction<string>>,
  setPreviousAddress: React.Dispatch<React.SetStateAction<string>>,
  setIsEditingAddress: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingAddress: React.Dispatch<React.SetStateAction<boolean>>,
  profil: ProfilData | null
) => {
  // Remplacer toutes les occurrences d'un ou plusieurs espaces par un seul espace
  const newNumero = DOMPurify.sanitize(numero.trim()).replace(/\s+/g, ' ');
  const newRue = DOMPurify.sanitize(rue.trim());
  const newVille = DOMPurify.sanitize(ville.trim());
  const newCodePostal = DOMPurify.sanitize(codePostal.trim());
  const newPays = DOMPurify.sanitize(pays.trim());

  // Validation du numéro avec insensibilité à la casse
  const isNumberValid = new RegExp(`^\\d+(\\s+(${validComplements.join('|')}))?$`, 'i').test(newNumero);
  if (!isNumberValid) {
    notify('Le numéro doit contenir uniquement des chiffres ou des compléments d\'adresse valides (bis, ter, rond-point, etc.).', 'error');
    return;
  }

  // Nouvelle validation pour le code postal
  if (!/^\d{5}$/.test(newCodePostal)) {
    notify('Le code postal doit contenir exactement 5 chiffres.', 'error');
    return;
  }

  // Nouvelle validation pour la ville
  if (newVille.length > 30) {
    notify(`Le nom de la ville ne peut pas dépasser 30 caractères. Il en contient actuellement ${newVille.length}.`, 'error');
    return;
  }

  // Nouvelle validation pour le pays
  if (newPays.length > 20) {
    notify(`Le nom du pays ne peut pas dépasser 20 caractères. Il en contient actuellement ${newPays.length}.`, 'error');
    return;
  }

  // Si des changements ont été détectés, préparer l'adresse complète pour la confirmation
  const fullAddress = `${newNumero} ${newRue}, ${newVille}, ${newCodePostal}, ${newPays}`;
  setTempAddress(fullAddress);
  setPreviousAddress(profil?.location || '');
  setIsEditingAddress(false);
  setIsConfirmingAddress(true);
};

// Fonction pour annuler la modification de l'adresse
export const handleAddressCancel = (
  profil: ProfilData | null,
  setNumero: React.Dispatch<React.SetStateAction<string>>,
  setRue: React.Dispatch<React.SetStateAction<string>>,
  setVille: React.Dispatch<React.SetStateAction<string>>,
  setCodePostal: React.Dispatch<React.SetStateAction<string>>,
  setPays: React.Dispatch<React.SetStateAction<string>>,
  setTempAddress: React.Dispatch<React.SetStateAction<string>>,
  setIsEditingAddress: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingAddress: React.Dispatch<React.SetStateAction<boolean>>
) => {
  if (profil?.location) {
    const [streetPart, ville_, codePostal_, pays_] = profil.location.split(',').map((part: string) => part.trim());
    const [numero_, ...rueParts] = streetPart.split(' ');

    setNumero(numero_ || '');
    setRue(rueParts.join(' ') || '');
    setVille(ville_ || '');
    setCodePostal(codePostal_ || '');
    setPays(pays_ || '');
  }

  setTempAddress(profil?.location || '');
  setIsEditingAddress(false);
  setIsConfirmingAddress(false);
};

// Fonction pour confirmer le téléphone
export const handlePhoneConfirm = (
  tempPhone: string,
  setIsConfirmingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setPreviousPhone: React.Dispatch<React.SetStateAction<string>>,
  profil: ProfilData | null
) => {
  const newPhone = DOMPurify.sanitize(tempPhone.trim());

  // Vérification du format du numéro (chiffres et +)
  if (!/^[+\d]+$/.test(newPhone)) {
    notify('Le numéro de téléphone ne doit contenir que des chiffres et éventuellement un +', 'error');
    return;
  }

  if (newPhone.length <= 15) { // Limite standard pour les numéros internationaux
    setPreviousPhone(profil?.telephone || '');
    setIsConfirmingPhone(true);
    setIsEditingPhone(false);
  } else {
    notify('Le numéro de téléphone doit être inférieur à 15 caractères', 'error');
  }
};

// Fonction pour annuler la modification du téléphone
export const handlePhoneCancel = (
  profil: ProfilData | null,
  setTempPhone: React.Dispatch<React.SetStateAction<string>>,
  setTempPhonePrive: React.Dispatch<React.SetStateAction<boolean>>,
  setPreviousPhonePrive: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingPhonePrive: React.Dispatch<React.SetStateAction<boolean>>
) => {
  // Restaurer l'état initial
  setTempPhone(profil?.telephone || '');
  setTempPhonePrive(profil?.telephone_prive || false);
  setPreviousPhonePrive(profil?.telephone_prive || false);
  setIsEditingPhone(false);
  setIsConfirmingPhone(false);
  setIsConfirmingPhonePrive(false);
};

// Fonction pour mettre à jour le slogan
export const updateUserSlogan = async (slogan: string, profil: ProfilData | null): Promise<boolean> => {
  try {
    // Nettoyer et valider le slogan
    const cleanSlogan = DOMPurify.sanitize(slogan.trim());

    // Vérifier si le slogan a changé
    if (cleanSlogan === profil?.slogan) {
      notify('Aucune modification détectée', 'info');
      return true;
    }

    // Vérifier la longueur du slogan
    if (cleanSlogan.length > 150) {
      notify('Le slogan ne doit pas dépasser 150 caractères', 'error');
      return false;
    }

    // S'assurer d'avoir un token CSRF valide avant la requête
    await fetchCsrfToken();
    const headers = await getCommonHeaders();

    // Mettre à jour le profil avec le nouveau slogan
    const response = await axios.put(
      `${API_CONFIG.baseURL}/api/users/updateProfil`,
      { slogan: cleanSlogan },
      {
        headers,
        withCredentials: true
      }
    );

    if (response.data.success) {
      return true;
    } else {
      notify(response.data.message || 'Erreur lors de la mise à jour du slogan', 'error');
      return false;
    }
  } catch (error: any) {
    logger.error('Erreur lors de la mise à jour du slogan:', error);
    notify(error.response?.data?.message || 'Erreur lors de la mise à jour du slogan', 'error');
    return false;
  }
};

// Fonction pour mettre à jour le profil
export const handleProfilUpdate = async (
  isEditingFirstName: boolean,
  isConfirmingFirstName: boolean,
  tempFirstName: string,
  isEditingLastName: boolean,
  isConfirmingLastName: boolean,
  tempLastName: string,
  isConfirmingPhone: boolean,
  tempPhone: string,
  isConfirmingPhonePrive: boolean,
  tempPhonePrive: boolean,
  isConfirmingBio: boolean,
  tempBio: string,
  isEditingAddress: boolean,
  isConfirmingAddress: boolean,
  numero: string,
  rue: string,
  ville: string,
  codePostal: string,
  pays: string,
  profil: ProfilData | null,
  setProfil: React.Dispatch<React.SetStateAction<ProfilData | null>>,
  setFirstName: React.Dispatch<React.SetStateAction<string>>,
  setLastName: React.Dispatch<React.SetStateAction<string>>,
  setVille: React.Dispatch<React.SetStateAction<string>>,
  setCodePostal: React.Dispatch<React.SetStateAction<string>>,
  setPays: React.Dispatch<React.SetStateAction<string>>,
  setNumero: React.Dispatch<React.SetStateAction<string>>,
  setRue: React.Dispatch<React.SetStateAction<string>>,
  setIsEditingFirstName: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingLastName: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingAddress: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setIsEditingBio: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingFirstName: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingLastName: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingAddress: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingPhone: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingPhonePrive: React.Dispatch<React.SetStateAction<boolean>>,
  setIsConfirmingBio: React.Dispatch<React.SetStateAction<boolean>>,
  setPreviousPhonePrive: React.Dispatch<React.SetStateAction<boolean>>
) => {
  try {
    // Ne créer l'objet qu'avec les champs modifiés
    const profilData: any = {};
    let hasChanges = false;

    // Fonction pour mettre en majuscule la première lettre et le reste en minuscule
    const capitalizeFirstLetter = (string: string) => {
      if (string === 'Prénom' || string === 'Nom') return '';
      return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    };

    // Vérifier si le prénom a été modifié
    if (isEditingFirstName || isConfirmingFirstName) {
      const newFirstName = DOMPurify.sanitize(capitalizeFirstLetter(tempFirstName));
      if (newFirstName !== profil?.firstName) {
        profilData.prenom = newFirstName;
        hasChanges = true;
        setFirstName(newFirstName); // Mettre à jour l'état du prénom
      }
    }

    // Vérifier si le nom a été modifié
    if (isEditingLastName || isConfirmingLastName) {
      const newLastName = DOMPurify.sanitize(capitalizeFirstLetter(tempLastName));
      if (newLastName !== profil?.lastName) {
        profilData.nom = newLastName;
        hasChanges = true;
        setLastName(newLastName); // Mettre à jour l'état du nom
      }
    }

    // Vérifier si le téléphone ou sa confidentialité a été modifié
    if (isConfirmingPhone) {
      const newPhone = DOMPurify.sanitize(tempPhone.trim());
      if (newPhone !== profil?.telephone) {
        profilData.telephone = newPhone;
        hasChanges = true;
      }
    }

    // Vérifier si la confidentialité du téléphone a été modifiée
    if (isConfirmingPhonePrive) {
      profilData.telephone_prive = tempPhonePrive;
      setPreviousPhonePrive(tempPhonePrive);
      hasChanges = true;
    }

    // Vérifier si la bio a été modifiée
    if (isConfirmingBio) {
      const newBio = DOMPurify.sanitize(tempBio.trim());
      if (newBio !== profil?.bio) {
        profilData.bio = newBio;
        hasChanges = true;
      }
    }

    // Sauvegarder chaque partie de l'adresse séparément si modifiée
    if (isEditingAddress || isConfirmingAddress) {
      const newNumero = DOMPurify.sanitize(capitalizeFirstLetter(numero));
      const newRue = DOMPurify.sanitize(capitalizeFirstLetter(rue));
      const newVille = DOMPurify.sanitize(capitalizeFirstLetter(ville));
      const newCodePostal = DOMPurify.sanitize(capitalizeFirstLetter(codePostal));
      const newPays = DOMPurify.sanitize(capitalizeFirstLetter(pays));

      // Vérifiez que tous les champs sont définis avant de construire l'adresse
      if (newNumero && newRue && newVille && newCodePostal && newPays &&
          newNumero !== 'undefined' && newRue !== 'undefined' &&
          newVille !== 'undefined' && newCodePostal !== 'undefined' &&
          newPays !== 'undefined') {
          profilData.location = `${newNumero} ${newRue}, ${newVille}, ${newCodePostal}, ${newPays}`;
      } else {
          // Si des champs sont manquants, ne pas définir de location
          profilData.location = '';
      }

      // Vérifiez si des changements ont été détectés
      if (newNumero !== profil?.location?.split(' ')[0]) {
          profilData.numero = newNumero;
          hasChanges = true;
      }
      if (newRue !== profil?.location?.split(' ')[1]) {
          profilData.adresse = newRue;
          hasChanges = true;
      }
      if (newVille !== profil?.ville) {
          profilData.ville = newVille;
          hasChanges = true;
      }
      if (newCodePostal !== profil?.code_postal) {
          profilData.code_postal = newCodePostal;
          hasChanges = true;
      }
      if (newPays !== profil?.pays) {
          profilData.pays = newPays;
          hasChanges = true;
      }
    }

    // Ne faire la requête que s'il y a des changements à mettre à jour
    if (!hasChanges) {
      notify('Aucune modification à enregistrer n\'a été détectée', 'info');
      return;
    }

    // S'assurer d'avoir un token CSRF valide avant la requête
    await fetchCsrfToken();
    const headers = await getCommonHeaders();

    // Si tout est ok, mettre à jour le profil
    const response = await axios.put(
      `${API_CONFIG.baseURL}/api/users/updateProfil`,
      profilData,
      {
        headers,
        withCredentials: true
      }
    );

    if (response.data.success) {
      setProfil(prev => {
        if (!prev) return null;
        return {
          ...prev,
          firstName: profilData.prenom || prev.firstName,
          lastName: profilData.nom || prev.lastName,
          location: profilData.location || prev.location,
          telephone: profilData.telephone || prev.telephone,
          telephone_prive: profilData.telephone_prive || prev.telephone_prive,
          bio: profilData.bio || prev.bio,
          photo_url: profilData.photo_url || prev.photo_url,
          ville: profilData.ville || prev.ville,
          code_postal: profilData.code_postal || prev.code_postal,
          pays: profilData.pays || prev.pays
        };
      });

      // Mettre à jour les états individuels de l'adresse si elle a été modifiée
      if (profilData.ville) setVille(profilData.ville);
      if (profilData.code_postal) setCodePostal(profilData.code_postal);
      if (profilData.pays) setPays(profilData.pays);
      if (profilData.numero) setNumero(profilData.numero);
      if (profilData.adresse) setRue(profilData.adresse);

      // Réinitialisation des états d'édition
      setIsEditingFirstName(false);
      setIsEditingLastName(false);
      setIsEditingAddress(false);
      setIsEditingPhone(false);
      setIsEditingBio(false);
      setIsConfirmingFirstName(false);
      setIsConfirmingLastName(false);
      setIsConfirmingAddress(false);
      setIsConfirmingPhone(false);
      setIsConfirmingPhonePrive(false);
      setIsConfirmingBio(false);
      notify('Profil mis à jour avec succès.', 'success');
    }
  } catch (error: any) {
    logger.error('Erreur lors de la mise à jour du profil:', error);
    const errorMessage = error.response?.data?.message || 'Erreur lors de la mise à jour du profil';
    notify(errorMessage, 'error');
  }
};
