import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Chip, 
  Avatar,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Badge,
  IconButton,
  Paper,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Clock, 
  Euro, 
  AlertCircle, 
  Heart, 
  MessageCircle, 
  Share2,
  Users,
  Building2,
  User,
  Image as ImageIcon,
  Lock,
  X as CloseIcon,
  Eye,
  Calendar,
  Wallet,
  Coins,
  ArrowRightLeft,
  ChevronLeft,
  ChevronRight,
  Info,
  Hand,
  ArrowRightCircle,
  CheckCircle2,
  Mail,
  XCircle,
  CheckCircle
} from 'lucide-react';
import {
  AccountBalance as AccountBalanceIcon,
  Brush as BrushIcon,
  Business as BusinessIcon,
  Campaign as CampaignIcon,
  Celebration as CelebrationIcon,
  Computer as ComputerIcon,
  Construction as ConstructionIcon,
  DirectionsCar as DirectionsCarIcon,
  Event as EventIcon,
  Face as FaceIcon,
  Flight as FlightIcon,
  Handyman as HandymanIcon,
  Home as HomeIcon,
  LocalShipping as LocalShippingIcon,
  Nature as NatureIcon,
  Palette as PaletteIcon,
  PersonOutline as PersonOutlineIcon,
  Pets as PetsIcon,
  Pool as PoolIcon,
  School as SchoolIcon,
  Security as SecurityIcon,
  Spa as SpaIcon,
  SportsBasketball as SportsBasketballIcon,
  Yard as YardIcon
} from '@mui/icons-material';
import { Mission, missionsApi, reportMission } from './missionsApi';
import { notify } from '../../../components/Notification';
import CommentsDialog from './CommentsDialog';
import ModalPortal from '../../../components/ModalPortal';
import InterventionZoneMap from '../components/maps/InterventionZoneMap';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../services/types';
import DOMPurify from 'dompurify';
import { useCreateNotification } from '../../../hooks/useCreateNotification';
import OnlineStatusDot from '../../../components/OnlineStatusDot';
import VerificationBadge from '../../../components/VerificationBadge';
import { useNavigate } from 'react-router-dom';
import InfoIcon from '@mui/icons-material/Info';
import useMediaQuery from '@mui/material/useMediaQuery';
import logger from '@/utils/logger';
import { API_URL } from '../../../config/api';

const CardContainer = styled(motion.div)<{ isHighlighted?: boolean }>(({ isHighlighted }) => ({
  backgroundColor: 'white',
  borderRadius: '24px',
  overflow: 'hidden',
  boxShadow: isHighlighted 
    ? '0 8px 32px rgba(255, 107, 44, 0.2)' 
    : '0 8px 24px rgba(255, 107, 44, 0.08)',
  border: isHighlighted 
    ? '3px solid #FF6B2C'
    : '2px solid transparent',
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(255, 107, 44, 0.12)',
    border: '2px solid #FF6B2C',
    '& .mission-image': {
      transform: 'scale(1.05)',
    }
  },
}));

const ImagesContainer = styled(Box)({
  position: 'relative',
  height: '200px',
  overflow: 'hidden',
  borderRadius: '24px 24px 0 0',
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: '2px',
  backgroundColor: '#FFF8F3',
  '& .mission-image': {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transition: 'transform 0.5s ease',
  },
  '& .map-container': {
    position: 'relative',
    height: '100%',
    overflow: 'hidden',
    '& .leaflet-container': {
      height: '100%',
      width: '100%',
      zIndex: 1,
      borderRadius: '0 24px 0 0',
      '& .leaflet-control-container': {
        display: 'none',
      },
    },
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: '50%',
      pointerEvents: 'none',
      zIndex: 2,
    }
  },
  '& .image-container': {
    position: 'relative',
    height: '100%',
    overflow: 'hidden',
    '& img': {
      borderRadius: '24px 0 0 0',
    },
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: '50%',
      background: 'linear-gradient(to top, rgba(0,0,0,0.5), transparent)',
      pointerEvents: 'none',
    }
  },
  '@media (max-width: 600px)': {
    gridTemplateColumns: '1fr',
    height: '180px',
    '& .map-container': {
      display: 'none',
    },
    '& .image-container img': {
      borderRadius: '24px 24px 0 0',
    }
  }
});

const ContentContainer = styled(Box)({
  padding: '24px',
  backgroundColor: '#FFF8F3',
  borderRadius: '0',
  '@media (max-width: 1280px)': {
    padding: '34px 8px 0px 8px',
  }
});

const MissionContentBlock = styled(Box)({
  backgroundColor: 'white',
  borderRadius: '16px',
  padding: '20px',
  marginBottom: '24px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.05)',
  border: '1px solid #FFE4BA',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.1)',
    transform: 'translateY(-2px)',
  }
});

const InfoBlock = styled(Box)({
  backgroundColor: 'white',
  borderRadius: '16px',
  padding: '20px',
  marginBottom: '24px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.05)',
  border: '1px solid #FFE4BA',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.1)',
    transform: 'translateY(-2px)',
  }
});

const InfoBlockTitle = styled(Typography)({
  fontSize: '1.1rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  '& svg': {
    color: '#FF6B2C',
  }
});

const UserInfo = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  marginBottom: '20px',
  position: 'relative',
  zIndex: 1,
  marginTop: '-56px',
  backgroundColor: 'rgba(255, 248, 243, 0.85)',
  backdropFilter: 'blur(8px)',
  padding: '8px 16px 8px 8px',
  borderRadius: '16px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.6)',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 248, 243, 0.95)',
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.15)',
  }
});

const UserAvatarContainer = styled(Box)({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
});

const StyledOnlineStatusDot = styled(OnlineStatusDot)({
  position: 'absolute',
  bottom: 0,
  right: 0,
  transform: 'translate(-20%, -80%)',
  boxShadow: '0 0 0 2px rgba(255, 248, 243, 0.9)',
});

const UserAvatar = styled(Avatar)({
  width: 64,
  height: 64,
  border: '4px solid #FFF8F3',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.25)',
  }
});

const Title = styled(Typography)({
  fontSize: '1.75rem',
  fontWeight: 800,
  color: '#2D3748',
  marginBottom: '12px',
  lineHeight: 1.2,
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  position: 'relative',
  paddingLeft: '12px',
  transition: 'all 0.2s ease',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '8%',
    height: '84%',
    width: '4px',
    background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)',
    borderRadius: '4px',
  },
  '&:hover': {
    color: '#FF6B2C',
    transform: 'translateX(2px)',
  },
  '@media (max-width: 980px)': {
    fontSize: '1.45rem',
  },
  '@media (max-width: 600px)': {
    fontSize: '1.35rem',
  },
});

const Description = styled(Typography)({
  color: '#4A5568',
  marginBottom: '0',
  lineHeight: 1.6,
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  padding: '16px 0 0 0',
  borderTop: '1px dashed rgba(255, 107, 44, 0.2)',
  '&:hover': {
    color: '#2D3748',
  },
  '& .description-content': {
    display: '-webkit-box',
    WebkitLineClamp: 3,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    marginBottom: '16px',
    '& p': {
      margin: '0 0 8px 0',
    }
  },
  '& .read-more': {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.9rem',
    color: '#FF6B2C',
    fontWeight: '500',
    marginTop: '8px',
    padding: '8px 16px',
    borderRadius: '8px',
    backgroundColor: 'rgba(255, 107, 44, 0.05)',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.1)',
    },
    '&::before': {
      content: '"→ "',
      marginRight: '4px',
    }
  }
}) as typeof Typography;

const UrgentChip = styled(Chip)({
  position: 'absolute',
  top: '50px',
  left: '16px',
  backgroundColor: 'rgba(255, 59, 48, 0.95)',
  color: 'white',
  fontWeight: 'bold',
  borderRadius: '8px',
  height: '32px',
  padding: '0 16px',
  backdropFilter: 'blur(4px)',
  zIndex: 2,
  boxShadow: '0 4px 12px rgba(255, 59, 48, 0.2)',
  '& .MuiChip-icon': {
    color: 'white',
    marginRight: '4px',
  },
  '&:hover': {
    transform: 'translateY(-1px) scale(1.02)',
    boxShadow: '0 6px 16px rgba(255, 59, 48, 0.25)',
  },
});

const InfoGrid = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '12px',
  marginBottom: '0',
  padding: '0',
  justifyContent: 'flex-start',
  '@media (max-width: 600px)': {
    gap: '8px',
  },
});

const InfoRow = styled(Box)(() => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  gap: '8px',
  color: '#4A5568',
  padding: '8px 14px',
  backgroundColor: '#FFF8F3',
  borderRadius: '14px',
  border: '1px solid #FFE4BA',
  transition: 'all 0.3s ease',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  minWidth: '140px',
  '& svg': {
    width: '18px',
    height: '18px',
    strokeWidth: 2,
    color: '#FF6B2C',
    flexShrink: 0,
  },
  '& .MuiTypography-root': {
    fontSize: '0.9rem',
    fontWeight: 600,
    color: '#4A5568',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'flex',
    alignItems: 'center',
  },
  '& .info-label': {
    maxWidth: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  '& .info-sublabel': {
    color: '#718096',
    fontSize: '0.75rem',
    marginLeft: '4px',
    fontWeight: 500,
    display: 'inline-flex',
    alignItems: 'center',
  },
  '&.category-row': {
    '& .MuiTypography-root': {
      '@media (max-width: 600px)': {
        flexDirection: 'column',
        alignItems: 'flex-start',
      }
    },
    '& .info-sublabel': {
      '@media (max-width: 600px)': {
        marginLeft: '0',
        marginTop: '2px',
      }
    }
  },
  '&[data-has-time-slots="true"]': {
    cursor: 'pointer',
    '&:hover': {
      transform: 'translateY(-3px)',
      boxShadow: '0 4px 8px rgba(255, 107, 44, 0.15)',
      '& svg': {
        transform: 'scale(1.1)',
      },
    },
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 8px rgba(255, 107, 44, 0.1)',
  },
  '&.highlight': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FF6B2C',
    '& svg': {
      color: '#FF6B2C',
    },
  },
  '&.budget': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FFE4BA',
    '& svg': {
      color: '#FF6B2C',
    },
    '&:hover': {
      backgroundColor: '#FFF8F3',
      '& svg': {
        color: '#FF965E',
      },
    },
  },
  '&.applications': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FFE4BA',
    '& svg': {
      color: '#FF6B2C',
    },
    '&:hover': {
      backgroundColor: '#FFF8F3',
      '& svg': {
        color: '#FF965E',
      },
    },
  },
  '&.urgent': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FEB2B2',
    '& svg': {
      color: '#E53E3E',
    },
    '&:hover': {
      backgroundColor: '#FFF8F3',
      '& svg': {
        color: '#C53030',
      },
    },
  },
}));

const ActionBar = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  padding: '10px 24px',
  backgroundColor: 'white',
  borderRadius: '0 0 24px 24px',
  borderTop: '2px solid #FFE4BA',
  position: 'relative',
  gap: '16px',
  '@media (max-width: 600px)': {
    padding: '12px 16px', // Augmenter légèrement le padding vertical
    gap: '12px', // Augmenter légèrement l'espace entre les éléments
    paddingBottom: '16px' // Ajouter plus d'espace en bas
  }
});

const ActionButtonsContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  width: '100%',
  '& .left-buttons': {
    display: 'flex',
    gap: '12px',
  },
  '& .right-buttons': {
    display: 'flex',
    alignItems: 'center',
    gap: '16px',
  },
  '@media (max-width: 700px)': {
    flexDirection: 'column', // Revenir à column pour empiler les éléments
    alignItems: 'stretch', // Étirer les éléments sur toute la largeur
    gap: '12px', // Espace entre les groupes de boutons
    '& .left-buttons': {
      width: '100%',
      justifyContent: 'flex-start', // Aligné à gauche
      gap: '8px', // Réduire l'espace entre les boutons sur mobile
    },
    '& .right-buttons': {
      width: '100%', // Prendre toute la largeur
      justifyContent: 'center', // Centrer les boutons
      marginTop: '8px', // Ajouter un espace au-dessus
      '& .MuiBox-root': {
        width: '100%', // Prendre toute la largeur
        display: 'flex',
        justifyContent: 'center',
      },
      '& .MuiButton-root': {
        flex: '1', // Faire en sorte que les boutons prennent l'espace disponible
        maxWidth: '48%', // Limiter la largeur pour avoir deux boutons côte à côte
        padding: '8px 12px', // Réduire le padding sur mobile
        fontSize: '0.85rem', // Réduire la taille de la police sur mobile
      }
    }
  },
  '@media (max-width: 525px)': {
    '& .right-buttons': {
      '& .MuiBox-root': {
        flexDirection: 'column',
        gap: '8px',
      },
      '& .MuiButton-root': {
        maxWidth: '100%', // Prendre toute la largeur
        width: '100%',
      }
    }
  }
});

const ActionButton = styled(motion.button)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '8px',
  background: 'white',
  border: '2px solid #FFE4BA',
  cursor: 'pointer',
  color: '#4A5568',
  padding: '12px',
  width: '48px',
  height: '48px',
  borderRadius: '16px',
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 107, 44, 0.05)',
    borderRadius: '50%',
    transform: 'translate(-50%, -50%) scale(0)',
    transition: 'transform 0.4s ease-out',
  },
  '&:hover': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FF6B2C',
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 16px rgba(255, 107, 44, 0.15)',
    '&::before': {
      transform: 'translate(-50%, -50%) scale(2.5)',
    }
  },
  '&:active': {
    transform: 'translateY(0)',
    boxShadow: '0 4px 8px rgba(255, 107, 44, 0.1)',
  },
  '& .MuiBadge-badge': {
    backgroundColor: '#FF6B2C',
    color: 'white',
    fontWeight: 'bold',
    minWidth: '22px',
    height: '22px',
    padding: '0 6px',
    borderRadius: '11px',
    border: '2px solid white',
    fontSize: '0.75rem',
    transform: 'translate(30%, -30%)',
    transition: 'all 0.3s ease',
  },
  '& svg': {
    width: '22px',
    height: '22px',
    transition: 'all 0.3s ease',
    strokeWidth: 1.5,
    position: 'relative',
    zIndex: 1,
  },
  '&:hover svg': {
    transform: 'scale(1.2)',
    strokeWidth: 2,
    color: '#FF6B2C',
  },
  '&[data-active="true"]': {
    color: '#FF6B2C',
    backgroundColor: '#FFF8F3',
    borderColor: '#FF6B2C',
    boxShadow: '0 8px 16px rgba(255, 107, 44, 0.15)',
    '& svg': {
      fill: '#FF6B2C',
      stroke: '#FF6B2C',
    },
    '&::before': {
      backgroundColor: 'rgba(255, 107, 44, 0.1)',
      transform: 'translate(-50%, -50%) scale(2.5)',
    },
    '& .MuiBadge-badge': {
      backgroundColor: '#FF4B0C',
      transform: 'translate(30%, -30%) scale(1.1)',
    }
  }
});

const MissionStatusChip = styled(Chip)({
  position: 'absolute',
  top: '-16px',
  left: '50%',
  transform: 'translateX(-50%)',
  backgroundColor: 'white',
  color: '#FF6B2C',
  fontWeight: 'bold',
  borderRadius: '8px',
  height: '32px',
  padding: '0 16px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
  '& .MuiChip-icon': {
    color: '#FF6B2C',
    marginRight: '6px',
  }
});

const RejectedOverlay = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(255, 248, 243, 0.9)',
  backdropFilter: 'blur(8px)',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 3,
  borderRadius: '24px',
  border: '2px solid rgba(255, 107, 44, 0.2)',
  padding: '24px',
  transition: 'all 0.3s ease-in-out',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '120px',
    height: '120px',
    background: 'radial-gradient(circle, rgba(255, 107, 44, 0.1) 0%, rgba(255, 248, 243, 0) 70%)',
    borderRadius: '50%',
    zIndex: -1
  },
  '& .overlay-content': {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    width: '100%',
    maxWidth: '640px',
    gap: '20px'
  },
  '& .mission-info': {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    backgroundColor: 'white',
    padding: '24px',
    borderRadius: '16px',
    width: '100%',
    maxWidth: '480px',
    border: '1px solid rgba(255, 107, 44, 0.2)',
    marginTop: '8px',
    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.05)',
    '& .mission-title': {
      color: '#2D3748',
      fontSize: '1.25rem',
      fontWeight: 700,
      marginBottom: '4px',
      textAlign: 'left'
    },
    '& .mission-description': {
      color: '#666',
      fontSize: '0.95rem',
      lineHeight: 1.6,
      textAlign: 'left',
      marginBottom: '16px',
      padding: '12px',
      backgroundColor: '#FFF8F3',
      borderRadius: '8px',
      border: '1px solid #FFE4BA'
    },
    '& .info-row': {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      color: '#666',
      padding: '8px 0',
      borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
      '&:last-child': {
        borderBottom: 'none'
      },
      '& svg': {
        color: '#FF6B2C',
        strokeWidth: 2,
      },
      '& .MuiTypography-root': {
        color: '#2D3748',
        fontWeight: 500,
      }
    }
  },
  '& .MuiTypography-h6': {
    color: '#FF6B2C',
    fontWeight: 800,
    fontSize: '1.5rem',
    marginBottom: '4px',
    position: 'relative',
    textAlign: 'center',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: '-8px',
      left: '50%',
      transform: 'translateX(-50%)',
      width: '40px',
      height: '3px',
      background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
      borderRadius: '3px'
    }
  },
  '& .MuiTypography-body2': {
    color: '#666',
    textAlign: 'center',
    width: '100%',
    lineHeight: 1.6,
    fontSize: '1rem',
    marginTop: '15px',
    marginBottom: '8px'
  }
});

const ViewButton = styled(Button)({
  minWidth: 'auto',
  padding: '8px 16px',
  marginTop: '16px',
  fontSize: '0.9rem',
  color: 'white',
  backgroundColor: '#FF6B2C',
  border: 'none',
  borderRadius: '16px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
  '&:hover': {
    backgroundColor: '#FF965E',
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.3)',
  },
  '&:active': {
    transform: 'translateY(0) scale(0.98)',
    boxShadow: '0 2px 8px rgba(255, 107, 44, 0.3)',
  },
  '& .MuiButton-startIcon': {
    color: 'white',
  },
});

const StatusButton = styled(Button)(({ selected }: { selected?: boolean }) => ({
  backgroundColor: selected ? '#FF6B2C' : '#FFF8F3',
  color: selected ? 'white' : '#FF6B2C',
  border: `2px solid ${selected ? '#FF6B2C' : '#FFE4BA'}`,
  borderRadius: '12px',
  padding: '16px',
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: '8px',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: selected ? '#FF965E' : '#FFE4BA',
    borderColor: '#FF6B2C',
  },
}));

const CreationDate = styled(Typography)({
  color: '#666',
  fontSize: '0.85rem',
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  backgroundColor: 'rgba(255, 248, 243, 0.85)',
  backdropFilter: 'blur(4px)',
  padding: '6px 10px',
  marginBottom: '16px',
  borderRadius: '8px',
  border: '1px solid rgba(255, 107, 44, 0.2)',
  boxShadow: '0 2px 6px rgba(255, 107, 44, 0.08)',
  width: 'fit-content',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 248, 243, 0.95)',
    boxShadow: '0 3px 8px rgba(255, 107, 44, 0.12)',
    transform: 'translateY(-2px)',
  },
  '& svg': {
    width: '16px',
    height: '16px',
    strokeWidth: 2,
    color: '#FF6B2C',
  }
}) as typeof Typography;

const OffersButton = styled(Button)({
  backgroundColor: '#FF6B2C',
  color: 'white',
  borderRadius: '8px',
  padding: '8px 16px',
  fontWeight: 'bold',
  maxWidth: '300px',
  textTransform: 'none',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
  '&:hover': {
    backgroundColor: '#FF965E',
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.3)',
  },
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  '@media (max-width: 600px)': {
    padding: '8px 12px',
    fontSize: '0.85rem',
    maxWidth: 'none',
    width: '100%', // Prendre toute la largeur disponible
    justifyContent: 'center', // Centrer le contenu
    '& .MuiButton-startIcon': {
      marginRight: '4px',
    },
    '& .MuiButton-endIcon': {
      marginLeft: '4px',
    }
  }
});

// --- MODALE DE SIGNALEMENT DE MISSION ---
const ReportMissionModal = ({ open, onClose, onSubmit, loading }: { open: boolean; onClose: () => void; onSubmit: (reason: string) => void; loading: boolean }) => {
  const [reason, setReason] = React.useState('');
  const [selectedReason, setSelectedReason] = React.useState('');
  const PREDEFINED_REASONS = [
    'Mission frauduleuse',
    'Contenu inapproprié',
    'Spam ou publicité',
    'Mission dangereuse',
    'Informations personnelles',
    'Autre',
  ];
  const PREDEFINED_PHRASES: { [key: string]: string } = {
    'Mission frauduleuse': "Je signale cette mission car elle semble frauduleuse (arnaque, fausse offre, etc.).",
    'Contenu inapproprié': "Je signale cette mission car son contenu est inapproprié ou offensant.",
    'Spam ou publicité': "Je signale cette mission car elle ressemble à du spam ou de la publicité non sollicitée.",
    'Mission dangereuse': "Je signale cette mission car elle présente un danger potentiel pour les utilisateurs.",
    'Informations personnelles': "Je signale cette mission car elle contient des informations personnelles sensibles.",
    'Autre': "Je souhaite signaler cette mission pour une autre raison : (merci de préciser)"
  };
  const handleSelectChange = (e: any) => {
    const value = e.target.value;
    setSelectedReason(value);
    if (PREDEFINED_PHRASES[value]) {
      setReason(PREDEFINED_PHRASES[value]);
    } else {
      setReason('');
    }
  };
  const handleSubmit = () => {
    if (reason.trim()) onSubmit(reason.trim());
  };
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <Box sx={{ background: 'white', borderRadius: 2, p: 0, maxWidth: 400, mx: 'auto', boxShadow: 3, overflowY: 'auto', maxHeight: '80vh' }}>
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2 }}>Signaler cette mission</Typography>
          <Typography variant="body2" sx={{ mb: 2, color: '#FF6B2C', fontWeight: 500 }}>
            Merci de décrire précisément le problème rencontré avec cette mission.
          </Typography>
          <FormControl fullWidth size="small" sx={{ mb: 1 }}>
            <InputLabel>Raison prédéfinie</InputLabel>
            <Select
              value={selectedReason}
              label="Raison prédéfinie"
              onChange={handleSelectChange}
            >
              <MenuItem value="">Aucune (raison personnalisée)</MenuItem>
              {PREDEFINED_REASONS.map(r => (
                <MenuItem key={r} value={r}>{r}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <textarea
            value={reason}
            onChange={e => setReason(e.target.value.slice(0, 750))}
            rows={5}
            maxLength={750}
            style={{ width: '100%', borderRadius: 8, border: '1px solid #FFE4BA', padding: 8, marginBottom: 8 }}
            placeholder="Décrivez le problème rencontré... (750 caractères max)"
          />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="caption" sx={{ color: reason.length >= 750 ? '#FF6B2C' : '#888' }}>
              {reason.length}/750 caractères
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button onClick={onClose} sx={{ color: '#666' }}>Annuler</Button>
            <Button
              onClick={handleSubmit}
              sx={{ background: '#FF6B2C', color: 'white', '&:hover': { background: '#FF7A35' } }}
              disabled={!reason.trim() || loading}
            >
              Signaler
            </Button>
          </Box>
        </Box>
      </Box>
    </ModalPortal>
  );
};

interface MissionCardProps {
  mission: Mission;
  isOwner?: boolean;
  onUpdate: (mission: Mission) => void;
  onReject?: (missionId: string) => Promise<void>;
  showRejectedOverlay?: boolean;
  isHighlighted?: boolean;
  showStatus?: boolean;
  onMakeProposal?: (mission: Mission) => void;
}

// Composant pour rendre l'icône de catégorie dynamiquement
const DynamicCategoryIcon = ({ iconName }: { iconName: string }) => {
  const iconMap: { [key: string]: React.ComponentType<any> } = {
    'AccountBalance': AccountBalanceIcon,
    'Brush': BrushIcon,
    'Business': BusinessIcon,
    'Campaign': CampaignIcon,
    'Celebration': CelebrationIcon,
    'Computer': ComputerIcon,
    'Construction': ConstructionIcon,
    'DirectionsCar': DirectionsCarIcon,
    'Event': EventIcon,
    'Face': FaceIcon,
    'Flight': FlightIcon,
    'Handyman': HandymanIcon,
    'Home': HomeIcon,
    'LocalShipping': LocalShippingIcon,
    'Nature': NatureIcon,
    'Palette': PaletteIcon,
    'PersonOutline': PersonOutlineIcon,
    'Pets': PetsIcon,
    'Pool': PoolIcon,
    'School': SchoolIcon,
    'Security': SecurityIcon,
    'Spa': SpaIcon,
    'SportsBasketball': SportsBasketballIcon,
    'Yard': YardIcon
  };

  const IconComponent = iconMap[iconName];
  return IconComponent ? <IconComponent sx={{ color: '#FF6B2C', width: 24, height: 24 }} /> : null;
};

// Définir l'interface pour la ref
export interface MissionCardRef {
  updateUserProposal: (proposalData?: any) => void;
  showProposalDetails: () => void;
}

const MissionCard = forwardRef<MissionCardRef, MissionCardProps>(({ 
  mission, 
  isOwner = false, 
  onUpdate, 
  onReject, 
  showRejectedOverlay = false,
  isHighlighted = false,
  showStatus = true,
  onMakeProposal
}, ref) => {
  const [showPhotos, setShowPhotos] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showCloseDialog, setShowCloseDialog] = useState(false);
  const [showTimeSlotsModal, setShowTimeSlotsModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<'terminee' | 'annulee' | null>(null);
  const [showOverlay, setShowOverlay] = useState(true);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const { createJobiNotification } = useCreateNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [userProposal, setUserProposal] = useState<any>(null);
  const [checkingProposal, setCheckingProposal] = useState(false);
  const [showProposalDetailsModal, setShowProposalDetailsModal] = useState(false);
  const navigate = useNavigate();
  const [pendingProposalsWarning, setPendingProposalsWarning] = useState(false);
  const [pendingProposalsCount, setPendingProposalsCount] = useState(0);
  const [cooldownTime, setCooldownTime] = useState(0);
  const [cooldownActive, setCooldownActive] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  
  // Fonction pour mettre à jour l'état userProposal après l'envoi d'une offre
  const updateUserProposal = async (proposalData?: any) => {
    if (proposalData) {
      // Si les données de l'offre sont fournies, les utiliser directement
      setUserProposal(proposalData);
    } else if (!isOwner && mission.id) {
      // Sinon, faire une requête API (cas de fallback)
      try {
        const proposal = await missionsApi.getUserProposalForMission(mission.id);
        setUserProposal(proposal);
      } catch (error) {
        logger.error("Erreur lors de la mise à jour de l'offre:", error);
      }
    }
  };

  // Vérifier si l'utilisateur a déjà fait une offre pour cette mission
  useEffect(() => {
    const checkUserProposal = async () => {
      if (!isOwner && mission.id) {
        setCheckingProposal(true);
        try {
          const proposal = await missionsApi.getUserProposalForMission(mission.id);
          setUserProposal(proposal);
        } catch (error) {
          logger.error("Erreur lors de la vérification de l'offre:", error);
          setUserProposal(null);
        } finally {
          setCheckingProposal(false);
        }
      }
    };
    
    checkUserProposal();
  }, [mission, isOwner]); // Dépendre de la mission entière, pas seulement de son ID

  const formatStatusLabel = (statut: string) => {
    switch (statut) {
      case 'terminee':
        return 'Mission terminée';
      case 'annulee':
        return 'Mission annulée';
      case 'en_cours':
        return 'Mission en cours';
      case 'en_moderation':
        return 'Mission en cours de modération';
      default:
        return 'Erreur';
    }
  };

  const formatDate = (date: string) => {
    return format(new Date(date), 'dd MMMM yyyy', { locale: fr });
  };

  const formatTime = (time: string | null) => {
    if (!time) return null;
    return format(new Date(`2000-01-01T${time}`), 'HH:mm');
  };

  const formatTimeSlots = (timeSlots: Mission['time_slots'], dateMission: string) => {
    if (!timeSlots || timeSlots.length === 0) {
      return 'Pas de créneaux définis';
    }

    const formattedDate = formatDate(dateMission);

    if (timeSlots.length === 1) {
      return `${formattedDate} - ${formatTime(timeSlots[0].start)} à ${formatTime(timeSlots[0].end)}`;
    }

    return `${formattedDate} (+ ${timeSlots.length} créneaux disponibles)`;
  };

  const handleLike = async () => {
    try {
      await missionsApi.likeMission(mission.id);
      if (onUpdate) {
        const updatedMission = await missionsApi.getMissionDetails(mission.id);
        onUpdate(updatedMission);
      }
      notify(mission.user_has_liked ? 'Like retiré' : 'Mission likée', 'success');
    } catch (error) {
      notify('Erreur lors du like de la mission', 'error');
    }
  };

  const handleRecommend = async () => {
    try {
      await missionsApi.recommendMission(mission.id);
      if (onUpdate) {
        const updatedMission = await missionsApi.getMissionDetails(mission.id);
        onUpdate(updatedMission);
      }
      notify(mission.user_has_recommended ? 'Recommandation retirée' : 'Mission recommandée', 'success');
    } catch (error) {
      notify('Erreur lors de la recommandation', 'error');
    }
  };

  const handleReject = async () => {
    if (onReject) {
      await onReject(mission.id);
    }
  };

  const handleCloseMission = async () => {
    if (!selectedStatus || isLoading || cooldownActive) return;
    
    // Si l'utilisateur veut annuler et qu'il n'a pas encore confirmé
    if (selectedStatus === 'annulee' && !showCancelConfirmation) {
      // Vérifier s'il y a des offres pour cette mission avant d'afficher la confirmation
      try {
        const response = await missionsApi.getReceivedProposals(1, 100, { missionId: mission.id });
        setPendingProposalsCount(response.total);
      } catch (error) {
        logger.error("Erreur lors de la vérification des candidatures:", error);
        setPendingProposalsCount(0);
      }
      
      setShowCancelConfirmation(true);
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Si l'utilisateur veut terminer la mission, vérifier d'abord s'il y a des offres acceptées
      if (selectedStatus === 'terminee') {
        // Vérifier s'il y a des offres pour cette mission
        const response = await missionsApi.getReceivedProposals(1, 1, { missionId: mission.id });
        
        if (response.total === 0) {
          // Aucune offre pour cette mission
          notify("Vous ne pouvez pas terminer cette mission car elle n'a reçu aucune offre. Veuillez l'annuler.", 'warning');
          setIsLoading(false);
          return;
        }
        
        // Vérifier s'il y a une offre acceptée
        const acceptedResponse = await missionsApi.getReceivedProposals(1, 1, { 
          missionId: mission.id,
          offer_status: ['acceptée']
        });
        
        if (acceptedResponse.total === 0) {
          // Aucune offre acceptée, mais des offres existent
          const pendingResponse = await missionsApi.getReceivedProposals(1, 1, { 
            missionId: mission.id,
            offer_status: ['en_attente', 'contre_offre', 'contre_offre_jobbeur']
          });
          
          if (pendingResponse.total > 0) {
            // Il y a des offres en attente
            setPendingProposalsCount(pendingResponse.total);
            setPendingProposalsWarning(true);
            setIsLoading(false);
            return;
          } else {
            // Aucune offre en attente, toutes ont été refusées
            notify("Vous ne pouvez pas terminer cette mission car aucune offre n'a été acceptée. Veuillez l'annuler.", 'warning');
            setIsLoading(false);
            return;
          }
        }
      }
      
      // Continuer avec l'annulation ou la terminaison (si une offre a été acceptée)
      const response = await missionsApi.closeMission(mission.id, selectedStatus, createJobiNotification);
      
      if (onUpdate) {
        const updatedMission = await missionsApi.getMissionDetails(mission.id);
        onUpdate(updatedMission);
      }
      
      // Réinitialiser les états
      setShowCloseDialog(false);
      setShowCancelConfirmation(false);
      
      // Activer le cooldown
      setCooldownActive(true);
      setCooldownTime(10);
      
      // Démarrer le compte à rebours
      const interval = setInterval(() => {
        setCooldownTime(prevTime => {
          if (prevTime <= 1) {
            clearInterval(interval);
            setCooldownActive(false);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
      
    } catch (error: any) {
      if (error.response?.data?.pendingProposals > 0 && selectedStatus === 'terminee') {
        setPendingProposalsCount(error.response.data.pendingProposals);
        setPendingProposalsWarning(true);
      } else {
        const errorMessage = error.response?.data?.error || 'Erreur lors de la fermeture de la mission';
        notify(errorMessage, 'error');
      }
      setShowCancelConfirmation(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmWithPendingProposals = async () => {
    // Rediriger vers la page des offres pour que l'utilisateur puisse choisir une candidature
    navigate(`/dashboard/missions/offres?tab=1&mission=${mission.id}`);
    setShowCloseDialog(false);
    setPendingProposalsWarning(false);
  };

  // Formater le montant avec le symbole €
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(amount);
  };
  
  // Formater la date au format français
  const formatProposalDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "d MMMM yyyy 'à' HH'h'mm", { locale: fr });
  };
  
  // Afficher le statut de la proposition
  const getProposalStatusLabel = (status: string) => {
    switch (status) {
      case 'en_attente':
        return 'En attente';
      case 'acceptée':
        return 'Acceptée';
      case 'refusée':
        return 'Refusée';
      case 'contre_offre':
        return 'Contre-offre reçue';
      case 'contre_offre_jobbeur':
        return 'Contre-offre envoyée';
      default:
        return status;
    }
  };
  
  // Obtenir la couleur du statut
  const getProposalStatusColor = (status: string) => {
    switch (status) {
      case 'en_attente':
        return '#FFA500'; // Orange
      case 'acceptée':
        return '#4CAF50'; // Vert
      case 'refusée':
        return '#F44336'; // Rouge
      case 'contre_offre':
      case 'contre_offre_jobbeur':
        return '#2196F3'; // Bleu
      default:
        return '#757575'; // Gris
    }
  };
  
  // Remplacer le bouton "Faire une offre" par "Voir l'offre faite" si l'utilisateur a déjà fait une offre
  const renderProposalButton = () => {
    // Check if we're in the offres page by looking at the URL
    const isOffresPage = window.location.pathname.includes('/dashboard/missions/offres');
    
    // Don't render the button if we're in the offres page
    if (isOffresPage) {
      return null;
    }
    
    if (!isOwner && typeof onMakeProposal === 'function') {
      return (
        <Button
          variant="contained"
          onClick={() => {
            if (userProposal) {
              // Ouvrir la modal des détails de l'offre
              setShowProposalDetailsModal(true);
            } else {
              onMakeProposal(mission);
            }
          }}
          sx={{
            backgroundColor: userProposal ? '#FF965E' : '#FF6B2C', // Couleur différente pour "Voir l'offre faite"
            color: 'white',
            borderRadius: '8px',
            padding: '8px 16px',
            fontWeight: 'bold',
            textTransform: 'none',
            fontSize: '1rem',
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.3s ease',
            animation: userProposal ? 'none' : 'pulse 2s infinite', // Pas d'animation pour "Voir l'offre faite"
            '@keyframes pulse': {
              '0%': {
                transform: 'scale(1)'
              },
              '70%': {
                transform: 'scale(1.02)'
              },
              '100%': {
                transform: 'scale(1)'
              }
            },
            '@media (min-width: 700px) and (max-width: 850px)': {
              marginLeft: '25px',
            },
            '@media (max-width: 600px)': {
              padding: '8px 12px',
              fontSize: '0.85rem',
              flex: '1',
              maxWidth: '48%',
              whiteSpace: 'nowrap',
              '& .MuiButton-startIcon': {
                marginRight: '4px',
              },
              '& .MuiButton-endIcon': {
                marginLeft: '4px',
              }
            },
            '@media (max-width: 525px)': {
              maxWidth: '100%',
              width: '100%',
              marginBottom: '8px'
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1,
            },
            '&:hover': {
              backgroundColor: userProposal ? '#FFA87A' : '#FF7A35', // Couleur hover différente pour "Voir l'offre faite"
              transform: 'translateY(-3px) scale(1.05)',
              boxShadow: userProposal 
                ? '0 8px 20px rgba(255, 150, 94, 0.4)' 
                : '0 8px 20px rgba(255, 107, 44, 0.4)',
            },
            '&:active': {
              transform: 'translateY(0) scale(0.98)',
              boxShadow: userProposal 
                ? '0 2px 8px rgba(255, 150, 94, 0.3)' 
                : '0 2px 8px rgba(255, 107, 44, 0.3)',
            },
            // Ajouter une bordure pour "Voir l'offre faite" pour plus de distinction
            border: userProposal ? '1px solid #FFE4BA' : 'none',
          }}
          startIcon={userProposal ? <Eye size={20} color="white" /> : <Hand size={20} color="white" />}
        >
          {userProposal 
            ? "Voir l'offre faite" 
            : `Faire une offre ${typeof mission.applications_count === 'number' && mission.applications_count > 0 ? `(${mission.applications_count} reçue${mission.applications_count > 1 ? 's' : ''})` : ''}`
          }
        </Button>
      );
    }
    return null;
  };

  // Section pour voir les offres reçues dans la page des missions directement
  const handleViewOffers = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/dashboard/missions/offres?tab=1&mission=${mission.id}`);
  };

  const renderOffersSection = () => {
    if (!isOwner) return null;
    
    const offersCount = mission.applications_count || 0;
    
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 1,
        '@media (max-width: 600px)': {
          width: 'auto',
          marginTop: 0,
          marginBottom: 0
        }
      }}>
        {offersCount > 0 ? (
            <OffersButton
              onClick={handleViewOffers}
              startIcon={<Mail size={16} />}
              endIcon={<Badge badgeContent={offersCount} color="error" sx={{ ml: 1 }} />}
            >
              {`Voir ${offersCount} offre${offersCount > 1 ? 's' : ''} reçue${offersCount > 1 ? 's' : ''}`}
            </OffersButton>
        ) : (
          <Typography variant="body2" sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1, 
            color: '#666',
            '@media (max-width: 600px)': {
              fontSize: '0.8rem',
              whiteSpace: 'nowrap'
            }
          }}>
            <Mail size={16} color="#FF6B2C" />
            Aucune offre reçue
          </Typography>
        )}
      </Box>
    );
  };

  const handleOpenCloseDialog = () => {
    if (cooldownActive) {
      notify(`Veuillez patienter ${cooldownTime} secondes avant de pouvoir fermer une mission`, 'warning');
      return;
    }
    setShowCloseDialog(true);
    setSelectedStatus(null);
    setShowCancelConfirmation(false);
  };

  const handleCloseDialog = () => {
    setShowCloseDialog(false);
  };

  // Exposer les méthodes via la ref
  useImperativeHandle(ref, () => ({
    updateUserProposal,
    showProposalDetails: () => {
      setShowProposalDetailsModal(true);
    }
  }));

  const handleReportMission = async (reason: string) => {
    setReportLoading(true);
    try {
      await reportMission(mission.id, reason);
      notify('Signalement envoyé, merci pour votre vigilance.', 'success');
      setReportModalOpen(false);
    } catch (error: any) {
      notify(error?.response?.data?.message || 'Erreur lors du signalement', 'error');
    } finally {
      setReportLoading(false);
    }
  };

  const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

  return (
    <CardContainer
      isHighlighted={isHighlighted}
      initial={isHighlighted ? { y: 20, opacity: 0 } : false}
      animate={isHighlighted ? { y: 0, opacity: 1 } : false}
      transition={{ duration: 0.5, type: "spring" }}
    >
      <Card sx={{ position: 'relative' }}>
        {showRejectedOverlay && showOverlay && (
          <RejectedOverlay>
            <Box className="overlay-content">
              <Box>
                <Typography variant="h6" gutterBottom>
                  Mission rejetée
                </Typography>
                <Typography variant="body2">
                  Cette mission ne correspond pas à vos critères ou préférences. 
                  Elle n'apparaîtra plus dans vos suggestions.
                </Typography>
              </Box>
              
              <Box className="mission-info">
                <Typography className="mission-title">
                  {mission.titre}
                </Typography>
                <Typography 
                  className="mission-description" 
                  component="div" 
                  dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(mission.description) }} 
                />

                <Box className="info-row">
                  <User size={20} />
                  <Typography>
                    {mission.user_profile?.prenom} {mission.user_profile?.nom?.charAt(0)}.
                    {mission.user_profile?.type_de_profil === 'professionnel' && 
                      ` (${mission.user_profile?.statut_entreprise})`
                    }
                  </Typography>
                </Box>
                <Box className="info-row">
                  <MapPin size={20} />
                  <Typography>
                    {mission.ville} ({mission.code_postal})
                  </Typography>
                </Box>
                <Box className="info-row">
                  <Clock size={20} />
                  <Typography>
                    {mission.has_time_preference 
                      ? formatTimeSlots(mission.time_slots, mission.date_mission)
                      : (mission.date_mission 
                          ? formatDate(mission.date_mission)
                          : 'Date à définir'
                        )
                  }
                  </Typography>
                </Box>
                <Box className="info-row">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    {mission.budget_defini ? (
                      <>
                        {mission.payment_method === 'jobi_only' && <Coins size={16} />}
                        {mission.payment_method === 'direct_only' && <Euro size={16} />}
                        {mission.payment_method === 'both' && <ArrowRightLeft size={16} />}
                        <Typography>
                          {`${mission.budget} € ${
                            mission.payment_method === 'jobi_only' ? '(Paiement Jobi)' :
                            mission.payment_method === 'direct_only' ? '(Paiement Direct)' :
                            '(Paiement Hybride)'
                          }`}
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Wallet size={16} />
                        <Typography>Budget à définir</Typography>
                      </>
                    )}
                  </Box>
                </Box>
              </Box>

              <ViewButton
                onClick={() => setShowOverlay(false)}
                startIcon={<Eye size={20} />}
              >
                Voir la mission
              </ViewButton>
            </Box>
          </RejectedOverlay>
        )}
        {mission.statut === "en_moderation" && showOverlay && (
          <RejectedOverlay>
            <Box className="overlay-content">
              <Box>
                <Typography variant="h6" gutterBottom>
                  Mission en cours de modération
                </Typography>
                <Typography variant="body2">
                  Cette mission est temporairement indisponible car elle est en cours de vérification par notre équipe.
                </Typography>
              </Box>
              <ViewButton
                onClick={() => setShowOverlay(false)}
                startIcon={<Eye size={20} />}
              >
                Voir la mission
              </ViewButton>
            </Box>
          </RejectedOverlay>
        )}

        <CardContent>
          <ImagesContainer>
            <Box className="image-container">
              {mission.photos && mission.photos.length > 0 ? (
                mission.photos.slice(0, 1).map((photo, index) => (
                  <img
                    key={photo.id}
                    src={photo.photo_url}
                    alt={`${mission.titre} - Photo ${index + 1}`}
                    loading="lazy"
                    className="mission-image"
                    onClick={() => setShowPhotos(true)}
                  />
                ))
              ) : (
                <img
                  src={DEFAULT_AVATAR}
                  alt="Image par défaut de la mission"
                  loading="lazy"
                  className="mission-image"
                />
              )}
              {mission.photos && mission.photos.length > 1 && (
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: '16px',
                    right: '16px',
                    zIndex: 2,
                    '@media (max-width: 600px)': {
                      bottom: '100px',
                    }
                  }}
                >
                  <Chip
                    icon={<ImageIcon size={16} />}
                    label={`+${mission.photos.length - 1} photos`}
                    onClick={() => setShowPhotos(true)}
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.9)',
                      backdropFilter: 'blur(4px)',
                      color: '#FF6B2C',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      borderRadius: '6px',
                      '&:hover': {
                        backgroundColor: 'white',
                      },
                    }}
                  />
                </Box>
              )}
            </Box>
            <Box className="map-container">
              <InterventionZoneMap
                center={mission.intervention_zone?.center || [48.8566, 2.3522]}
                radius={30}
                avatarUrl={mission.user_profile?.photo_url}
                isOwnProfil={false}
              />
            </Box>

            {(mission.is_closed || showStatus) && (
              <MissionStatusChip
                icon={mission.is_closed ? <Lock size={16} /> : <Clock size={16} />}
                label={
                  mission.statut === "en_moderation"
                    ? formatStatusLabel(mission.statut)
                    : mission.is_closed
                      ? formatStatusLabel(mission.statut)
                      : "Mission en cours"
                }
                sx={{
                  position: 'absolute',
                  top: '16px',
                  left: '16px',
                  backgroundColor: mission.statut === 'en_moderation'
                    ? '#FF3B30' // Rouge pour la modération
                    : mission.is_closed ? (
                        mission.statut === 'annulee' ? '#FF3B30' :
                        mission.statut === 'terminee' ? '#4CAF50' : '#FF6B2C'
                      ) : '#FF6B2C',
                  color: 'white',
                  fontWeight: 'bold',
                  '& .MuiChip-icon': {
                    color: 'white'
                  },
                  zIndex: 3,
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)',
                  transform: 'none'
                }}
              />
            )}

            {mission.is_urgent && (
              <UrgentChip
                icon={<AlertCircle size={16} />}
                label="Urgent"
              />
            )}
          </ImagesContainer>

          <ContentContainer>
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              position: 'relative',
              mb: 1
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                width: '100%',
                mb: 2
              }}>
                <UserInfo>
                  <UserAvatarContainer>
                    <UserAvatar
                      src={mission.user_profile?.photo_url || DEFAULT_AVATAR}
                      alt={mission.user_profile?.prenom}
                      onClick={() => {
                        if (mission.user_profile?.slug) {
                          window.open(`/dashboard/profil/${mission.user_profile.slug}`, '_blank');
                        } else {
                          window.open(`/dashboard/profil/`, '_blank');
                        }
                      }}
                      sx={{ 
                        cursor: mission.user_profile?.slug ? 'pointer' : 'default',
                        '&:hover': {
                          transform: mission.user_profile?.slug ? 'scale(1.05)' : 'none',
                          transition: 'transform 0.2s ease-in-out'
                        }
                      }}
                    />
                    <StyledOnlineStatusDot userId={mission.user_id} />
                    <VerificationBadge 
                      isVerified={mission.user_profile?.profil_verifier || false}
                      type={mission.user_profile?.type_de_profil || 'particulier'}
                    />
                  </UserAvatarContainer>
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    position: 'relative',
                  }}>
                    <Typography
                      variant="body1"
                      onClick={() => {
                        if (mission.user_profile?.slug) {
                          window.open(`/dashboard/profil/${mission.user_profile.slug}`, '_blank');
                        } else {
                          window.open(`/dashboard/profil/`, '_blank');
                        }
                      }}
                      sx={{ 
                        cursor: mission.user_profile?.slug ? 'pointer' : 'default',
                        fontWeight: 600,
                        color: '#2D3748',
                        fontSize: '1.1rem',
                        '&:hover': {
                          color: mission.user_profile?.slug ? '#FF6B2C' : '#2D3748',
                          transition: 'color 0.2s ease-in-out'
                        }
                      }}
                    >
                      {mission.user_profile?.prenom} {mission.user_profile?.nom?.charAt(0)}.
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ 
                        fontSize: '0.85rem',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        backgroundColor: mission.user_profile?.type_de_profil === 'particulier' 
                          ? 'rgba(255, 107, 44, 0.1)' 
                          : 'rgba(255, 150, 94, 0.1)',
                        color: mission.user_profile?.type_de_profil === 'particulier'
                          ? '#FF6B2C'
                          : '#FF7A35',
                        fontWeight: 600,
                        padding: '4px 8px',
                        borderRadius: '6px',
                        border: '1px solid',
                        borderColor: mission.user_profile?.type_de_profil === 'particulier'
                          ? 'rgba(255, 107, 44, 0.3)'
                          : 'rgba(255, 150, 94, 0.3)',
                        boxShadow: '0 2px 4px rgba(255, 107, 44, 0.05)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: mission.user_profile?.type_de_profil === 'particulier'
                            ? 'rgba(255, 107, 44, 0.15)'
                            : 'rgba(255, 150, 94, 0.15)',
                          transform: 'translateY(-1px)',
                        }
                      }}
                    >
                      {mission.user_profile?.type_de_profil === 'particulier' ? (
                        <>
                          <User size={12} color="#FF6B2C" />
                          Particulier
                        </>
                      ) : (
                        <>
                          <Building2 size={12} color="#FF7A35" />
                          Pro {mission.user_profile?.statut_entreprise && `- ${mission.user_profile.statut_entreprise}`}
                        </>
                      )}
                    </Typography>
                  </Box>
                </UserInfo>

              </Box>

              <MissionContentBlock>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                  <Title variant="h5">{mission.titre}</Title>

                  {isOwner && !mission.is_closed && (
                    <Button
                      onClick={handleOpenCloseDialog}
                      startIcon={<Lock size={20} />}
                      sx={{
                        color: '#FF6B2C',
                        borderColor: '#FFE4BA',
                        border: '2px solid',
                        borderRadius: '8px',
                        padding: '6px 12px',
                        fontWeight: 'bold',
                        whiteSpace: 'nowrap',
                        minWidth: 'auto',
                        flexShrink: 0,
                        marginLeft: '12px',
                        '@media (max-width: 600px)': {
                          padding: '4px 8px',
                          '& .MuiButton-startIcon': {
                            margin: 0,
                          },
                          '& .MuiButton-endIcon': {
                            margin: 0,
                          },
                          '& .MuiButton-label': {
                            display: 'none',
                          },
                        },
                        '&:hover': {
                          backgroundColor: '#FFF8F3',
                          borderColor: '#FF6B2C',
                        }
                      }}
                    >
                      <Box sx={{ 
                        display: 'flex', 
                        alignItems: 'center',
                        '@media (max-width: 600px)': {
                          '& > span:last-child': {
                            display: 'none'
                          }
                        }
                      }}>
                        <span>Fermer la mission</span>
                      </Box>
                    </Button>
                  )}
                </Box>
                
                <CreationDate>
                  <Calendar strokeWidth={2.5} />
                  <span style={{ 
                    paddingTop: '2px', 
                    fontWeight: 600,
                    color: '#4A5568'
                  }}>
                    {mission.created_at ? formatDate(mission.created_at) : 'Non spécifiée'}
                  </span>
                </CreationDate>

                <Description 
                  onClick={() => setShowCommentDialog(true)}
                  role="button"
                  aria-label="Voir la description complète et les commentaires"
                  tabIndex={0}
                  component="div"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      setShowCommentDialog(true);
                    }
                  }}
                >
                  <div 
                    className="description-content"
                    dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(mission.description) }}
                  />
                  <div className="read-more">
                    Cliquez pour voir la suite et les commentaires
                  </div>
                </Description>
              </MissionContentBlock>

              <InfoBlock>
                <InfoBlockTitle>
                  <Info size={18} />
                  Détails de la mission
                </InfoBlockTitle>
                <InfoGrid>
                  {mission.is_urgent && (
                    <InfoRow className="urgent">
                      <AlertCircle size={18} />
                      <Typography>Urgent</Typography>
                    </InfoRow>
                  )}

                  {/* 1. Catégorie */}
                  {mission.category_id && SERVICE_CATEGORIES.find(cat => cat.id === mission.category_id) && (
                    <InfoRow className="category-row">
                      <DynamicCategoryIcon 
                        iconName={SERVICE_CATEGORIES.find(cat => cat.id === mission.category_id)?.icon || ''} 
                      />
                      <Typography>
                        <span className="info-label">
                          {SERVICE_CATEGORIES.find(cat => cat.id === mission.category_id)?.nom}
                        </span>
                        {mission.subcategory_id && (
                          <span className="info-sublabel">
                            • {SERVICE_SUBCATEGORIES.find(sub => sub.id === mission.subcategory_id)?.nom}
                          </span>
                        )}
                      </Typography>
                    </InfoRow>
                  )}

                  {/* 2. Créneau */}
                  <InfoRow 
                    onClick={() => mission.has_time_preference && setShowTimeSlotsModal(true)} 
                    data-has-time-slots={mission.has_time_preference}
                    className={mission.has_time_preference ? "highlight" : ""}
                  >
                    <Clock size={18} />
                    <Typography>
                      {mission.has_time_preference 
                        ? formatTimeSlots(mission.time_slots, mission.date_mission)
                        : (mission.date_mission 
                            ? formatDate(mission.date_mission)
                            : 'Date à définir'
                          )
                      }
                    </Typography>
                  </InfoRow>

                  {/* 3. Lieu */}
                  <InfoRow>
                    <MapPin size={18} />
                    <Typography>
                      <span className="info-label">{mission.ville}</span>
                      <span className="info-sublabel">({mission.code_postal})</span>
                    </Typography>
                  </InfoRow>
                  
                  {/* 4. Prix */}
                  <InfoRow className="budget">
                    {mission.budget_defini ? (
                      <>
                        {mission.payment_method === 'jobi_only' && <Coins size={20} />}
                        {mission.payment_method === 'direct_only' && <Euro size={20} />}
                        {mission.payment_method === 'both' && <ArrowRightLeft size={20} />}
                        <Typography>
                          <span className="info-label">
                            {mission.budget}{mission.payment_method !== 'jobi_only' && ' €'}
                          </span>
                          <span className="info-sublabel">
                            {mission.payment_method === 'jobi_only' ? '(Jobi)' :
                             mission.payment_method === 'direct_only' ? '(Direct)' :
                             '(Hybride)'}
                          </span>
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Wallet size={20} />
                        <Typography>Budget à définir</Typography>
                      </>
                    )}
                  </InfoRow>                  
                </InfoGrid>
              </InfoBlock>
            </Box>
          </ContentContainer>

            <ActionBar>
            {/* Action buttons */}
            <ActionButtonsContainer>
              <Box className="left-buttons">
                <Tooltip title={mission.user_has_liked ? "Retirer le like" : "Liker la mission"}>
                  <ActionButton 
                    onClick={handleLike} 
                    whileHover={{ scale: 1.05 }} 
                    whileTap={{ scale: 0.95, rotate: mission.user_has_liked ? 0 : -10 }}
                    data-active={mission.user_has_liked}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Badge badgeContent={mission.likes_count || 0} color="primary">
                      <Heart size={20} fill={mission.user_has_liked ? 'currentColor' : 'none'} />
                    </Badge>
                  </ActionButton>
                </Tooltip>
                <Tooltip title="Commenter">
                  <ActionButton 
                    onClick={() => setShowCommentDialog(true)} 
                    whileHover={{ scale: 1.05 }} 
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Badge badgeContent={mission.comments_count || 0} color="primary">
                      <MessageCircle size={20} />
                    </Badge>
                  </ActionButton>
                </Tooltip>
                <Tooltip title={mission.user_has_recommended ? "Retirer la recommandation" : "Recommander"}>
                  <ActionButton 
                    onClick={handleRecommend} 
                    whileHover={{ scale: 1.05 }} 
                    whileTap={{ scale: 0.95, rotate: mission.user_has_recommended ? 0 : 15 }}
                    data-active={mission.user_has_recommended}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Share2 size={20} fill={mission.user_has_recommended ? 'currentColor' : 'none'} />
                  </ActionButton>
                </Tooltip>
                {/* Ajout du bouton Signaler */}
                {!isOwner && (
                  <Tooltip title="Signaler cette mission">
                    <ActionButton
                      onClick={() => setReportModalOpen(true)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.4 }}
                      data-testid="report-mission-btn"
                    >
                      <AlertCircle size={20} color="#FF6B2C" />
                    </ActionButton>
                  </Tooltip>
                )}
              </Box>

              <Box className="right-buttons">
                {isOwner ? renderOffersSection() : (
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    gap: 2,
                    width: '100%',
                    '@media (max-width: 600px)': {
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      gap: 1
                    }
                  }}>
                    {renderProposalButton()}
                    {!isOwner && onReject && !showRejectedOverlay && (
                      <Button
                        onClick={() => setShowRejectDialog(true)}
                        startIcon={<AlertCircle size={20} />}
                        sx={{
                          backgroundColor: 'red',
                          color: 'white',
                          borderColor: '#FFE4BA',
                          border: '2px solid',
                          borderRadius: '8px',
                          padding: '8px 16px',
                          fontWeight: 'bold',
                          whiteSpace: 'nowrap',
                          '@media (max-width: 600px)': {
                            padding: '8px 12px',
                            fontSize: '0.8rem',
                            '& .MuiButton-startIcon': {
                              marginRight: '4px',
                            }
                          },
                          '&:hover': {
                            backgroundColor: '#FFF8F3',
                            borderColor: '#FF6B2C',
                            color: '#FF6B2C'
                          }
                        }}
                      >
                        Ne m'intéresse pas
                      </Button>
                    )}
                  </Box>
                )}
              </Box>
            </ActionButtonsContainer>
          </ActionBar>
        </CardContent>
      </Card>

      {/* Dialog pour les photos */}
      {showPhotos && (
        <ModalPortal>
          <div
            className="fixed inset-0 flex items-center justify-center p-4 z-[60]"
            onClick={() => setShowPhotos(false)}
          >
            <div
              className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] relative max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Bouton de fermeture amélioré (en haut à droite) */}
              <button 
                onClick={() => setShowPhotos(false)} 
                className="absolute top-4 right-4 text-white bg-[#FF6B2C] hover:bg-[#FF965E] rounded-full p-2 transition-colors duration-300 hover:scale-110 transform shadow-md z-50"
                aria-label="Fermer la galerie de photos"
              >
                <CloseIcon className="h-6 w-6" />
              </button>

              <div className="bg-white p-6 rounded-xl shadow-sm mb-8">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <ImageIcon className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-[#2D3748]">Photos de la mission</h2>
                </div>
              </div>

              <div className="relative group mb-8">
                <div className="relative overflow-hidden rounded-xl">
                  <img
                    src={mission.photos[currentPhotoIndex].photo_url}
                    alt={`Photo ${currentPhotoIndex + 1} de la mission`}
                    className="w-full h-[300px] object-contain rounded-xl shadow-md"
                  />
                  {mission.photos.length > 1 && (
                    <>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (currentPhotoIndex > 0) {
                            setCurrentPhotoIndex(currentPhotoIndex - 1);
                          }
                        }}
                        className={`absolute left-0 top-0 bottom-0 px-6 flex items-center justify-center bg-gradient-to-r from-[#FF6B2C]/20 to-transparent hover:from-[#FF6B2C]/50 transition-all duration-300 group ${
                          currentPhotoIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'opacity-0 group-hover:opacity-100'
                        }`}
                        disabled={currentPhotoIndex === 0}
                      >
                        <ChevronLeft className="h-8 w-8 text-white" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (currentPhotoIndex < mission.photos.length - 1) {
                            setCurrentPhotoIndex(currentPhotoIndex + 1);
                          }
                        }}
                        className={`absolute right-0 top-0 bottom-0 px-6 flex items-center justify-center bg-gradient-to-l from-[#FF6B2C]/20 to-transparent hover:from-[#FF6B2C]/50 transition-all duration-300 group ${
                          currentPhotoIndex === mission.photos.length - 1 ? 'opacity-50 cursor-not-allowed' : 'opacity-0 group-hover:opacity-100'
                        }`}
                        disabled={currentPhotoIndex === mission.photos.length - 1}
                      >
                        <ChevronRight className="h-8 w-8 text-white" />
                      </motion.button>
                    </>
                  )}
                </div>
                {mission.photos.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {mission.photos.map((_, index) => (
                      <button
                        key={index}
                        onClick={(e) => {
                          e.stopPropagation();
                          setCurrentPhotoIndex(index);
                        }}
                        className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                          index === currentPhotoIndex
                            ? 'bg-[#FF6B2C]'
                            : 'bg-white/50 hover:bg-white/75'
                        }`}
                        aria-label={`Aller à la photo ${index + 1}`}
                      />
                    ))}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <div className="bg-white p-6 rounded-xl shadow-md">
                  <div className="flex items-center gap-3 mb-4">
                    <MapPin className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Informations</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 pb-2 border-b border-[#FFE4BA]">
                      <ImageIcon className="h-5 w-5 text-[#FF6B2C]" />
                      <p className="text-gray-600">Photo {currentPhotoIndex + 1} sur {mission.photos.length}</p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-[#FF6B2C]" />
                      <p className="text-gray-600">
                        {mission.ville} ({mission.code_postal})
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-[#FF6B2C]" />
                      <p className="text-gray-600">
                        {mission.has_time_preference 
                          ? formatTimeSlots(mission.time_slots, mission.date_mission)
                          : (mission.date_mission 
                              ? formatDate(mission.date_mission)
                              : 'Date à définir'
                            )
                        }
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      {mission.budget_defini ? (
                        <>
                          {mission.payment_method === 'jobi_only' && <Coins className="h-5 w-5 text-[#FF6B2C]" />}
                          {mission.payment_method === 'direct_only' && <Euro className="h-5 w-5 text-[#FF6B2C]" />}
                          {mission.payment_method === 'both' && <ArrowRightLeft className="h-5 w-5 text-[#FF6B2C]" />}
                          <p className="text-gray-600">
                            {`${mission.budget} € ${
                              mission.payment_method === 'jobi_only' ? '(Paiement Jobi)' :
                              mission.payment_method === 'direct_only' ? '(Paiement Direct)' :
                              '(Paiement Hybride)'
                            }`}
                          </p>
                        </>
                      ) : (
                        <>
                          <Wallet className="h-5 w-5 text-[#FF6B2C]" />
                          <p className="text-gray-600">Budget à définir</p>
                        </>
                      )}
                    </div>

                    {mission.applications_count !== undefined && (
                      <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-[#FF6B2C]" />
                        <p className="text-gray-600">
                          {mission.applications_count} candidature{mission.applications_count !== 1 ? 's' : ''}
                        </p>
                      </div>
                    )}

                    {mission.is_urgent && (
                      <div className="flex items-center gap-2 mt-2 p-2 bg-red-50 rounded-lg border border-red-200">
                        <AlertCircle className="h-5 w-5 text-red-500" />
                        <p className="text-red-600 font-medium">Mission urgente</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-md">
                  <div className="flex items-center gap-3 mb-4">
                    <Info className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Résumé de la mission</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-[#FF6B2C]" />
                      <p className="text-gray-800 font-medium">
                        {mission.user_profile?.prenom} {mission.user_profile?.nom?.charAt(0)}.
                        {mission.user_profile?.type_de_profil === 'professionnel' && 
                          ` (${mission.user_profile?.statut_entreprise})`
                        }
                      </p>
                    </div>
                    <div className="mt-2">
                      <div className="p-3 bg-[#FFF8F3] rounded-lg border border-[#FFE4BA]">
                        <p className="text-gray-700 text-sm line-clamp-3" dangerouslySetInnerHTML={{ 
                          __html: DOMPurify.sanitize(mission.description) 
                        }} />
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowFullDescription(true);
                        }}
                        className="mt-2 w-full py-2 px-4 bg-white border-2 border-[#FFE4BA] text-[#FF6B2C] rounded-lg hover:bg-[#FFF8F3] hover:border-[#FF6B2C] transition-colors duration-300 flex items-center justify-center gap-2 font-medium"
                      >
                        <Eye className="h-4 w-4" />
                        Voir la description complète
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bouton de fermeture en bas */}
              <div className="flex justify-center mt-4 border-t border-[#FFE4BA] pt-6">
                <button
                  onClick={() => setShowPhotos(false)}
                  className="bg-[#FF6B2C] hover:bg-[#FF965E] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300 flex items-center gap-2 shadow-md"
                >
                  <CloseIcon className="h-5 w-5" />
                  <span className="px-2 -mt-0.5">Fermer</span>
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Dialog pour les commentaires */}
      {showCommentDialog && (
        <CommentsDialog
          open={showCommentDialog}
          onClose={() => setShowCommentDialog(false)}
          mission={mission}
          missionId={mission.id}
          isOwner={isOwner}
          onUpdate={async () => {
            if (onUpdate) {
              const updatedMission = await missionsApi.getMissionDetails(mission.id);
              onUpdate(updatedMission);
            }
          }}
        />
      )}

      {/* Dialog de confirmation de rejet */}
      <Dialog
        open={showRejectDialog}
        onClose={() => setShowRejectDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            backgroundColor: '#FFF8F3',
            boxShadow: '0 8px 32px rgba(255, 107, 44, 0.1)',
            '&:before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
              borderRadius: '16px 16px 0 0',
            },
          }
        }}
      >
        <DialogTitle sx={{ 
          color: '#2D3748',
          fontSize: '1.5rem',
          fontWeight: 'bold',
          borderBottom: '1px solid #FFE4BA',
          padding: '20px 24px',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <AlertCircle size={24} color="#FF6B2C" />
          Rejeter cette mission ?
        </DialogTitle>
        <DialogContent sx={{ padding: '24px' }}>
          <Typography variant="body1" component="div" gutterBottom sx={{ paddingTop: '15px' }}>
            En rejetant cette mission :
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body2" color="#666" gutterBottom>
              Elle n'apparaîtra plus dans vos suggestions de missions
            </Typography>
            <Typography component="li" variant="body2" color="#666" gutterBottom>
              Vous ne pourrez plus postuler à cette mission
            </Typography>
            <Typography component="li" variant="body2" color="#666">
              Cette action est définitive et ne peut pas être annulée
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ 
          padding: '16px 24px',
          borderTop: '1px solid #FFE4BA',
          gap: '12px'
        }}>
          <Button 
            onClick={() => setShowRejectDialog(false)}
            sx={{
              color: '#666',
              fontWeight: 'bold',
              textTransform: 'none',
              fontSize: '1rem',
              padding: '8px 24px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: 'rgba(102, 102, 102, 0.08)',
              },
            }}
          >
            Annuler
          </Button>
          <Button 
            onClick={() => {
              handleReject();
              setShowRejectDialog(false);
            }}
            variant="contained"
            sx={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              fontWeight: 'bold',
              textTransform: 'none',
              fontSize: '1rem',
              padding: '8px 24px',
              borderRadius: '8px',
              '&:hover': {
                backgroundColor: '#FF965E',
              },
            }}
          >
            Confirmer le rejet
          </Button>
        </DialogActions>
      </Dialog>

      <ModalPortal isOpen={showCloseDialog} onBackdropClick={handleCloseDialog}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            width: '95%',
            maxWidth: 600,
            maxHeight: '90vh',
            overflow: 'auto',
            position: 'relative',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            margin: 'auto'
          }}
        >
          <Box sx={{ 
            color: '#2D3748',
            fontSize: '1.5rem',
            fontWeight: 'bold',
            borderBottom: '1px solid #FFE4BA',
            padding: '20px 24px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}>
            <Lock size={24} color="#FF6B2C" />
            {showCancelConfirmation ? 'Confirmer l\'annulation' : 'Fermer la mission'}
          </Box>
          <Box sx={{ padding: '24px' }}>
            {showCancelConfirmation ? (
              <>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 2, 
                  mb: 3, 
                  p: 2, 
                  bgcolor: '#FFF8F3', 
                  borderRadius: 2,
                  border: '1px solid #FF6B2C'
                }}>
                  <AlertCircle size={24} color="#FF6B2C" />
                  <Typography variant="body1" color="#FF6B2C" fontWeight="bold">
                    Êtes-vous sûr de vouloir annuler cette mission ?
                  </Typography>
                </Box>
                <Typography variant="body1" gutterBottom>
                  Cette action est irréversible et entraînera :
                </Typography>
                <Box component="ul" sx={{ pl: 3, mt: 1 }}>
                  <Typography component="li" variant="body2" gutterBottom>
                    Le retrait de 1 Jobi de votre solde
                  </Typography>
                  {pendingProposalsCount > 0 ? (
                    <Typography component="li" variant="body2" gutterBottom>
                      Le refus automatique de <strong>{pendingProposalsCount} candidature{pendingProposalsCount > 1 ? 's' : ''}</strong> en cours
                    </Typography>
                  ) : null}
                  {pendingProposalsCount > 0 && (
                    <Typography component="li" variant="body2" gutterBottom>
                      L'envoi d'un email à tous les jobbeurs ayant fait une offre
                    </Typography>
                  )}
                  <Typography component="li" variant="body2" gutterBottom>
                    La fermeture définitive de la mission
                  </Typography>
                </Box>
                {pendingProposalsCount > 0 && (
                  <Box sx={{ mt: 3, p: 2, bgcolor: '#FFF8F3', borderRadius: 2, border: '1px solid #FFE4BA' }}>
                    <Typography variant="body2" color="#666">
                      <InfoIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'text-bottom', color: '#FF6B2C' }} />
                      Attention : {pendingProposalsCount} jobbeur{pendingProposalsCount > 1 ? 's ont' : ' a'} pris le temps de faire une offre pour cette mission. Êtes-vous certain de vouloir l'annuler ?
                    </Typography>
                  </Box>
                )}
              </>
            ) : pendingProposalsWarning ? (
              <>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 2, 
                  mb: 3, 
                  p: 2, 
                  bgcolor: '#FFF8F3', 
                  borderRadius: 2,
                  border: '1px solid #FF6B2C'
                }}>
                  <AlertCircle size={24} color="#FF6B2C" />
                  <Typography variant="body1" color="#FF6B2C" fontWeight="bold">
                    Attention : Candidatures en attente
                  </Typography>
                </Box>
                <Typography variant="body1" gutterBottom>
                  Vous avez {pendingProposalsCount} candidature{pendingProposalsCount > 1 ? 's' : ''} en attente pour cette mission.
                </Typography>
                <Typography variant="body1" gutterBottom>
                  Pour terminer la mission, vous devez d'abord accepter une candidature.
                </Typography>
                <Typography variant="body1" gutterBottom sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
                  Si vous ne souhaitez accepter aucune candidature, veuillez annuler la mission.
                </Typography>
                <Typography variant="body2" color="#666" sx={{ mt: 2 }}>
                  Cliquez sur "Voir les candidatures" pour choisir un jobbeur et accepter son offre.
                </Typography>
              </>
            ) : (
              <>
                <Typography 
                  variant="body1" 
                  component="div"
                  gutterBottom
                  sx={{ pt: '15px' }}
                >
                  Vous êtes sur le point de fermer cette mission. Cette action est irréversible.
                </Typography>
                <Typography variant="body2" color="#666" sx={{ mb: 3 }}>
                  Veuillez sélectionner le statut final de la mission :
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <StatusButton
                      onClick={() => setSelectedStatus('terminee')}
                      selected={selectedStatus === 'terminee'}
                    >
                      <CheckCircle size={32} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        Terminée
                      </Typography>
                      <Typography variant="body2" align="center">
                        La mission a été réalisée avec succès
                      </Typography>
                    </StatusButton>
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <StatusButton
                      onClick={() => setSelectedStatus('annulee')}
                      selected={selectedStatus === 'annulee'}
                    >
                      <XCircle size={32} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        Annulée
                      </Typography>
                      <Typography variant="body2" align="center">
                        La mission est annulée définitivement
                      </Typography>
                    </StatusButton>
                  </Grid>
                </Grid>
                
                {selectedStatus === 'terminee' && (
                  <Box sx={{ mt: 3, p: 2, bgcolor: '#FFF8F3', borderRadius: 2, border: '1px solid #FFE4BA' }}>
                    <Typography variant="body2" color="#666">
                      <InfoIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'text-bottom', color: '#FF6B2C' }} />
                      Pour terminer une mission, vous devez avoir accepté une offre au préalable. Si aucune offre n'a été acceptée, vous devrez annuler la mission.
                    </Typography>
                  </Box>
                )}
                
                {selectedStatus === 'annulee' && (
                  <Box sx={{ mt: 3, p: 2, bgcolor: '#FFF8F3', borderRadius: 2, border: '1px solid #FFE4BA' }}>
                    <Typography variant="body2" color="#666">
                      <InfoIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'text-bottom', color: '#FF6B2C' }} />
                      En annulant cette mission, 1 Jobi sera retiré de votre solde conformément à nos conditions d'utilisation. Toutes les candidatures en cours seront automatiquement refusées et les jobbeurs ayant fait une offre seront avertis par email.
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
          <Box sx={{ 
            padding: '16px 24px',
            borderTop: '1px solid #FFE4BA',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '12px'
          }}>
            <Button 
              onClick={handleCloseDialog}
              sx={{
                color: '#666',
                fontWeight: 'bold',
                textTransform: 'none',
                fontSize: '1rem',
                padding: '8px 24px',
                borderRadius: '8px',
                '&:hover': {
                  backgroundColor: 'rgba(102, 102, 102, 0.08)',
                },
              }}
            >
              {showCancelConfirmation ? 'Retour' : 'Annuler'}
            </Button>
            {pendingProposalsWarning ? (
              <Button 
                onClick={handleConfirmWithPendingProposals}
                variant="contained"
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  fontWeight: 'bold',
                  textTransform: 'none',
                  fontSize: '1rem',
                  padding: '8px 24px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                }}
              >
                Voir les candidatures
              </Button>
            ) : (
              <Button 
                onClick={handleCloseMission}
                disabled={(!selectedStatus && !showCancelConfirmation) || isLoading}
                variant="contained"
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  fontWeight: 'bold',
                  textTransform: 'none',
                  fontSize: '1rem',
                  padding: '8px 24px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: '#FFE4BA',
                    color: '#FF6B2C',
                    opacity: 0.7,
                    cursor: 'not-allowed',
                    border: '2px dashed #FF6B2C',
                    '&::after': {
                      content: showCancelConfirmation ? '""' : '"⚠️ Sélectionnez un statut"',
                      position: 'absolute',
                      top: '-30px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      backgroundColor: '#FFF8F3',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      whiteSpace: 'nowrap',
                      color: '#FF6B2C',
                      border: '1px solid #FFE4BA'
                    }
                  },
                }}
              >
                {isLoading ? 'Chargement...' : (showCancelConfirmation ? 'Confirmer l\'annulation' : 'Confirmer')}
              </Button>
            )}
          </Box>
        </motion.div>
      </ModalPortal>

      {/* Modale des créneaux horaires lors du click sur le bouton "Créneaux disponibles" d'une mission */}
      {showTimeSlotsModal && (
        <ModalPortal>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
            onClick={() => setShowTimeSlotsModal(false)}
          >
            <Box
              sx={{
                backgroundColor: 'white',
                borderRadius: '24px',
                padding: '24px',
                maxWidth: '600px',
                width: '90%',
                maxHeight: '90vh',
                overflowY: 'auto',
                position: 'relative',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <IconButton
                onClick={() => setShowTimeSlotsModal(false)}
                sx={{
                  position: 'absolute',
                  right: '16px',
                  top: '16px',
                  color: '#FF6B2C',
                }}
              >
                <CloseIcon />
              </IconButton>
              <Typography variant="h6" gutterBottom sx={{ color: '#2D3748', fontWeight: 'bold', mb: 3 }}>
                Créneaux disponibles pour {mission.titre}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {mission.time_slots?.map((slot, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      padding: '16px',
                      backgroundColor: '#FFF8F3',
                      borderRadius: '12px',
                      border: '1px solid #FFE4BA',
                    }}
                  >
                    <Calendar size={24} color="#FF6B2C" />
                    <Box>
                      <Typography variant="subtitle1" sx={{ color: '#2D3748', fontWeight: 'bold' }}>
                        {formatDate(slot.date)}
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#4A5568' }}>
                        {formatTime(slot.start)} - {formatTime(slot.end)}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        </ModalPortal>
      )}

      {/* Dialog pour la description complète */}
      {showFullDescription && (
        <ModalPortal>
          <div
          className="fixed inset-0 flex items-center justify-center p-4 z-[70]"
          onClick={() => setShowFullDescription(false)}
        >
          <div
            className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[700px] relative max-h-[80vh]"
            onClick={(e) => e.stopPropagation()}
          >
            <button 
              onClick={() => setShowFullDescription(false)} 
              className="absolute top-4 right-4 text-[#FF6B2C] hover:text-[#FF965E] transition-colors duration-300 hover:scale-110 transform"
            >
              <CloseIcon className="h-7 w-7" />
            </button>

            <div className="flex items-center gap-3 mb-6">
              <Info className="h-6 w-6 text-[#FF6B2C]" />
              <h3 className="text-2xl font-bold text-gray-800">Description de la mission</h3>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md overflow-y-auto max-h-[50vh]">
              <div className="prose prose-sm max-w-none">
                <div dangerouslySetInnerHTML={{ 
                  __html: DOMPurify.sanitize(mission.description) 
                }} className="text-gray-700 leading-relaxed" />
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowFullDescription(false)}
                className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center gap-2"
              >
                <CloseIcon className="h-4 w-4" />
                Fermer
              </button>
            </div>
          </div>
        </div>
      </ModalPortal>
    )}

    {/* Modal pour afficher les détails de l'offre */}
    <Dialog 
      open={showProposalDetailsModal} 
      onClose={() => setShowProposalDetailsModal(false)}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          padding: '0px',
          margin: '8px'
        }
      }}
    >
      <DialogTitle sx={{ 
        fontSize: '1.5rem', 
        fontWeight: 'bold',
        color: '#333',
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <Hand size={24} color="#FF6B2C" />
        Détails de votre offre
      </DialogTitle>
      
      <DialogContent>
        {userProposal && (
          <Box sx={{ 
            mt: 2, 
            width: { xs: '100%', sm: '540px' } 
          }}>
            {/* Statut de l'offre mis en évidence en haut */}
            <Paper 
              elevation={3}
              sx={{ 
                mb: 3, 
                p: 1, 
                borderRadius: '12px',
                backgroundColor: getProposalStatusColor(userProposal.statut),
                color: 'white',
                textAlign: 'center',
                boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 2
              }}
            >
              {userProposal.statut === 'en_attente' && <Clock size={28} strokeWidth={2.5} />}
              {userProposal.statut === 'acceptée' && <CheckCircle2 size={28} strokeWidth={2.5} />}
              {userProposal.statut === 'refusée' && <AlertCircle size={28} strokeWidth={2.5} />}
              {(userProposal.statut === 'contre_offre' || userProposal.statut === 'contre_offre_jobbeur') && <ArrowRightLeft size={28} strokeWidth={2.5} />}
              
              <Typography variant="h6" sx={{ fontWeight: 'bold', letterSpacing: '0.5px', fontSize: '1.1rem' }}>
                Statut : {getProposalStatusLabel(userProposal.statut)}
              </Typography>
            </Paper>
            
            <Paper elevation={0} sx={{ 
              p: 3, 
              mb: 3, 
              borderRadius: '12px',
              border: '1px solid #FFE4BA',
              backgroundColor: '#FFF8F3'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                  Mission : {mission.titre}
                </Typography>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1" sx={{ color: '#666' }}>
                    Montant proposé :
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
                    {formatAmount(userProposal.montant_propose || 0)}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1" sx={{ color: '#666' }}>
                    Date de l'offre :
                  </Typography>
                  <Typography variant="body1">
                    {userProposal.created_at ? formatProposalDate(userProposal.created_at) : '-'}
                  </Typography>
                </Box>
                
                {userProposal.statut === 'contre_offre' && (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                      Contre-offre reçue
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1" sx={{ color: '#666' }}>
                        Montant contre-offre :
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                        {formatAmount(userProposal.montant_contre_offre || 0)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1" sx={{ color: '#666' }}>
                        Date de la contre-offre :
                      </Typography>
                      <Typography variant="body1">
                        {userProposal.date_contre_offre ? formatProposalDate(userProposal.date_contre_offre) : '-'}
                      </Typography>
                    </Box>
                  </>
                )}
                
                {userProposal.statut === 'contre_offre_jobbeur' && (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                      Votre contre-offre
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1" sx={{ color: '#666' }}>
                        Montant contre-offre :
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                        {formatAmount(userProposal.montant_contre_offre_jobbeur || 0)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1" sx={{ color: '#666' }}>
                        Date de la contre-offre :
                      </Typography>
                      <Typography variant="body1">
                        {userProposal.date_contre_offre_jobbeur ? formatProposalDate(userProposal.date_contre_offre_jobbeur) : '-'}
                      </Typography>
                    </Box>
                  </>
                )}
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                Votre message :
              </Typography>
              <Paper elevation={0} sx={{ p: 2, backgroundColor: 'white', borderRadius: '8px' }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {userProposal.message || ''}
                </Typography>
              </Paper>
              
              {(userProposal.statut === 'contre_offre' && userProposal.message_contre_offre) && (
                <>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mt: 2, mb: 1, color: '#2196F3' }}>
                    Message de la contre-offre :
                  </Typography>
                  <Paper elevation={0} sx={{ p: 2, backgroundColor: 'white', borderRadius: '8px', border: '1px solid #2196F3' }}>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {userProposal.message_contre_offre}
                    </Typography>
                  </Paper>
                </>
              )}
              
              {(userProposal.statut === 'contre_offre_jobbeur' && userProposal.message_contre_offre_jobbeur) && (
                <>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mt: 2, mb: 1, color: '#2196F3' }}>
                    Message de votre contre-offre :
                  </Typography>
                  <Paper elevation={0} sx={{ p: 2, backgroundColor: 'white', borderRadius: '8px', border: '1px solid #2196F3' }}>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {userProposal.message_contre_offre_jobbeur}
                    </Typography>
                  </Paper>
                </>
              )}
            </Paper>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions sx={{ 
        p: 3, 
        justifyContent: { xs: 'center', sm: 'space-between' },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 }
      }}>
        <Button 
          onClick={() => setShowProposalDetailsModal(false)}
          variant="outlined"
          fullWidth={useMediaQuery('(max-width:600px)')}
          sx={{ 
            borderColor: '#FF6B2C',
            color: '#FF6B2C',
            '&:hover': {
              borderColor: '#FF7A35',
              backgroundColor: 'rgba(255, 107, 44, 0.04)'
            }
          }}
        >
          Fermer
        </Button>
        
        <Button 
          onClick={() => {
            setShowProposalDetailsModal(false);
            navigate('/dashboard/missions/offres?tab=0');
          }}
          variant="contained"
          fullWidth={useMediaQuery('(max-width:600px)')}
          sx={{ 
            backgroundColor: '#FF6B2C',
            color: 'white',
            '&:hover': {
              backgroundColor: '#FF7A35'
            }
          }}
          startIcon={<ArrowRightCircle size={20} />}
        >
          Voir toutes mes offres
        </Button>
      </DialogActions>
    </Dialog>

    <ReportMissionModal open={reportModalOpen} onClose={() => setReportModalOpen(false)} onSubmit={handleReportMission} loading={reportLoading} />
  </CardContainer>
);
});

export default MissionCard; 
