import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  ArrowLeft,
  Download,
  Mail,
  Copy,
  Edit,
  Trash,
  FileText,
  CreditCard,
  X,
  History,
  MoreHorizontal,
  FileCheck
} from 'lucide-react';
import { getDisplayNumber, isDraft } from '../../../services/billingService';
import ModalPortal from '../../../components/ModalPortal';
import InvoiceHistory from '../../../components/InvoiceHistory';
import DocumentPreviewModal from '../../../components/DocumentPreviewModal';
import useCompanySettings from '../../../hooks/invoices/useCompanyInvoiceSettings';
import { useNavigate } from 'react-router-dom';

interface InvoiceItem {
  id: string;
  description: string;
  quantite: number;
  unite: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

interface Document {
  id: string;
  number: string;
  draft_number?: string;
  type: 'devis' | 'facture' | 'avoir';
  client_name: string;
  client_id?: string;
  date_creation: string;
  date_validite?: string;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
  statut: string;
  description: string;
  invoice_items: InvoiceItem[];
  client_address?: string;
  client_email?: string;
  client_phone?: string;
  client_siret?: string;
  client_tva?: string;
  conditions_paiement?: string;
  mode_paiement?: string;
  mentions_legales?: string;
  mentions_tva?: string;
  penalite_retard?: string;
  indemnite_recouvrement?: string;
  notes?: string;
  forme_juridique?: string;
  code_ape?: string;
  facture_origine_id?: string;
  sender_info?: {
    prenom?: string;
    nom?: string;
    entreprise?: string;
  };
}

interface DocumentDetailsProps {
  document: Document;
  onBack?: () => void;
  onEdit: (document: Document) => void;
  onDelete: (document: Document) => void;
  onDuplicate: (document: Document) => void;
  onDownload: (document: Document, isReceived?: boolean) => void;
  onSendEmail: () => void;
  onConvertToInvoice?: (document: Document) => void;
  onCreateCreditNote?: (document: Document) => void;
  onReject?: (document: Document) => void;
  onUpdateInvoiceStatus?: (document: Document, newStatus: string) => void;
  getStatusText?: (statut: string, type?: string) => string;
  getStatusColor?: (statut: string) => string;
  isReceivedQuote?: boolean;
  isReceivedInvoice?: boolean;
  isReceivedCreditNote?: boolean;
}

const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  document,
  onBack,
  onEdit,
  onDelete,
  onDuplicate,
  onDownload,
  onSendEmail,
  onConvertToInvoice,
  onCreateCreditNote,
  onReject,
  onUpdateInvoiceStatus,
  getStatusText,
  getStatusColor,
  isReceivedQuote,
  isReceivedInvoice,
  isReceivedCreditNote
}) => {
  // State pour le modal de prévisualisation
  const [previewOpen, setPreviewOpen] = useState(false);
  const { settings: companySettings } = useCompanySettings();
  const [historyModalOpen, setHistoryModalOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);
  const [showPaymentConfirmation, setShowPaymentConfirmation] = useState(false);
  const navigate = useNavigate();

  // Mémoriser le document pour forcer la mise à jour en temps réel
  const memoizedDocument = useMemo(() => {
    return {
      ...document,
      _lastUpdate: Date.now()
    };
  }, [
    document.id, 
    document.number,
    document.draft_number,
    document.type,
    document.statut, 
    document.client_name,
    document.client_id,
    document.client_address,
    document.client_email,
    document.client_phone,
    document.client_siret,
    document.client_tva,
    document.description,
    document.date_creation,
    document.date_validite,
    document.total_ht,
    document.total_tva,
    document.total_ttc,
    document.conditions_paiement,
    document.mode_paiement,
    document.mentions_legales,
    document.mentions_tva,
    document.penalite_retard,
    document.indemnite_recouvrement,
    document.notes,
    document.forme_juridique,
    document.code_ape,
    document.facture_origine_id,
    JSON.stringify(document.invoice_items),
    JSON.stringify(document.sender_info)
  ]);

  // CORRECTION : Forcer le re-render quand le document change pour une UX en temps réel
  useEffect(() => {
    // Fermer les dropdowns quand le document change pour éviter les états incohérents
    setDropdownOpen(false);
    setStatusDropdownOpen(false);
    }, [memoizedDocument]);
  const [paymentConfirmed, setPaymentConfirmed] = useState(false);
  const [statusToConfirm, setStatusToConfirm] = useState<string | null>(null);
  const [showMobileStatusModal, setShowMobileStatusModal] = useState(false);

  // Référence pour les menus dropdown
  const dropdownRef = useRef<HTMLDivElement>(null);
  const statusDropdownRef = useRef<HTMLDivElement>(null);

  // Fermer les dropdowns quand on clique ailleurs
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
        setStatusDropdownOpen(false);
      }
    }
    
    // Ajouter l'écouteur d'événement
    if (typeof window !== 'undefined') {
      window.addEventListener('mousedown', handleClickOutside);
    }
    
    // Nettoyer l'écouteur d'événement
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mousedown', handleClickOutside);
      }
    };
  }, []);

  // Les paramètres d'entreprise sont maintenant gérés par le hook useCompanySettings

  // Format date to localized string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get document type in French
  const getDocumentTypeLabel = () => {
    switch (memoizedDocument.type) {
      case 'devis':
        return 'Devis';
      case 'facture':
        return 'Facture';
      case 'avoir':
        return 'Avoir';
      default:
        return 'Document';
    }
  };

  // Get status color class
  const getStatusColorClass = () => {
    if (getStatusColor) {
      return getStatusColor(memoizedDocument.statut);
    }

    switch (memoizedDocument.statut) {
      case 'brouillon':
        return 'bg-gray-100 text-gray-800';
      case 'émis':
      case 'envoyé':
        return 'bg-blue-100 text-blue-800';
      case 'accepté':
      case 'payé':
        return 'bg-green-100 text-green-800';
      case 'refusé':
        return 'bg-red-100 text-red-800';
      case 'à payer':
        return 'bg-yellow-100 text-yellow-800';
      case 'facture':
        return 'bg-purple-100 text-purple-800 border border-purple-400';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplay = () => {
    return getStatusText ? getStatusText(memoizedDocument.statut, memoizedDocument.type) : memoizedDocument.statut;
  };

  // Liste des statuts disponibles pour les factures
  const availableInvoiceStatuses = [
    'envoye',
    'paye',
    'partiellement_paye',
    'en_retard',
    'annule'
  ];

  // Empêche les appels d'impression dupliqués
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header avec les actions - Version améliorée */}
      <div className="bg-white border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Première ligne: Titre et statut */}
          <div className="py-3 justify-end flex items-center">
            <button
              onClick={onBack}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors mr-3"
              aria-label="Retour"
            >
              <ArrowLeft size={20} />
            </button>
            <h1 className="text-lg font-semibold text-gray-800">
              {getDocumentTypeLabel()} {getDisplayNumber(memoizedDocument)}
              {isDraft(memoizedDocument) && (
                <span className="ml-2 text-sm text-gray-500 italic">(Brouillon)</span>
              )}
            </h1>
            <span className={`ml-3 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColorClass()}`}>
              {getStatusDisplay()}
            </span>
            {memoizedDocument.type === 'devis' && memoizedDocument.facture_origine_id && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-400">
                <FileCheck size={12} className="mr-1" />
                Facture créée
              </span>
            )}
          </div>
          
          {/* Seconde ligne: Barre d'actions */}
          <div className="pb-3 flex justify-end items-center">
            <div className="flex items-center space-x-2">
              {/* Version mobile: Menu déroulant avec toutes les actions */}
              <div className="md:hidden relative">
                <button
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                  className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                >
                  <span>Actions</span>
                  <MoreHorizontal size={16} className="ml-1" />
                </button>

                {dropdownOpen && (
                  <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20">
                    <div className="py-1" role="menu" aria-orientation="vertical">
                      <button
                        onClick={() => {
                          setPreviewOpen(true);
                          setDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                      >
                        <FileText size={16} className="inline-block mr-2" />
                        Aperçu
                      </button>

                      {/* Bouton de statut pour mobile */}
                      {memoizedDocument.type === 'facture' && onUpdateInvoiceStatus && !isReceivedInvoice && memoizedDocument.statut !== 'brouillon' && !['paye', 'partiellement_paye'].includes(memoizedDocument.statut) && (
                        <div>
                          <button
                            onClick={() => {
                              setDropdownOpen(false);
                              setShowMobileStatusModal(true);
                            }}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 whitespace-nowrap"
                            role="menuitem"
                          >
                            <CreditCard size={16} className="inline-block mr-2 flex-shrink-0" />
                            <span>Changer le statut</span>
                          </button>
                        </div>
                      )}

                      {!isReceivedQuote && !isReceivedInvoice && memoizedDocument.statut === 'brouillon' && (
                        <button
                          onClick={() => {
                            onEdit(memoizedDocument);
                            setDropdownOpen(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          role="menuitem"
                        >
                          <Edit size={16} className="inline-block mr-2" />
                          Modifier
                        </button>
                      )}

                      <button
                        onClick={() => {
                          onDownload(memoizedDocument, isReceivedQuote || isReceivedInvoice || isReceivedCreditNote);
                          setDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                      >
                        <Download size={16} className="inline-block mr-2" />
                        Télécharger PDF
                      </button>

                      <button
                        onClick={() => {
                          onSendEmail();
                          setDropdownOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                      >
                        <Mail size={16} className="inline-block mr-2" />
                        Envoyer par email
                      </button>

                      {/* Bouton de suppression pour mobile */}
                      {!isReceivedQuote && !isReceivedInvoice && memoizedDocument.statut === 'brouillon' && (
                        <button
                          onClick={() => {
                            onDelete(memoizedDocument);
                            setDropdownOpen(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                          role="menuitem"
                        >
                          <Trash size={16} className="inline-block mr-2" />
                          Supprimer
                        </button>
                      )}

                      {/* Autres boutons pour mobile */}
                      {/* ... */}
                    </div>
                  </div>
                )}
              </div>

              {/* Version desktop: Boutons séparés */}
              <div className="hidden md:flex md:flex-wrap gap-2 space-x-0">
                <button
                  onClick={() => setPreviewOpen(true)}
                  className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                >
                  <FileText size={16} className="mr-1.5" />
                  <span>Aperçu</span>
                </button>

                {/* Bouton de changement de statut pour les factures */}
                {memoizedDocument.type === 'facture' && onUpdateInvoiceStatus && !isReceivedInvoice && memoizedDocument.statut !== 'brouillon' && !['paye', 'partiellement_paye'].includes(memoizedDocument.statut) && (
                  <div className="relative inline-block text-left" ref={statusDropdownRef}>
                    <button
                      onClick={() => setStatusDropdownOpen(!statusDropdownOpen)}
                      className="flex items-center justify-center whitespace-nowrap px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                    >
                      <CreditCard size={16} className="mr-1.5 flex-shrink-0" />
                      <span>Statut</span>
                      <svg className="ml-2 h-5 w-5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                    
                    {statusDropdownOpen && (
                      <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20">
                        <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                          {availableInvoiceStatuses.map((status) => (
                            <button
                              key={status}
                              onClick={() => {
                                if (status === 'paye' || status === 'partiellement_paye') {
                                  setStatusToConfirm(status);
                                  setShowPaymentConfirmation(true);
                                  setStatusDropdownOpen(false);
                                } else {
                                  onUpdateInvoiceStatus(memoizedDocument, status);
                                  setStatusDropdownOpen(false);
                                }
                              }}
                              className={`block px-4 py-2 text-sm text-left w-full hover:bg-gray-100 ${memoizedDocument.statut === status ? 'bg-gray-100 font-medium' : ''}`}
                              role="menuitem"
                            >
                              <span className={`inline-block w-3 h-3 rounded-full mr-2 ${getStatusColor ? getStatusColor(status) : ''}`}></span>
                              {getStatusText ? getStatusText(status, 'facture') : status}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {!isReceivedQuote && !isReceivedInvoice && (
                  <button
                    onClick={() => onEdit(memoizedDocument)}
                    className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                  >
                    <Edit size={16} className="mr-1.5" />
                    <span>Modifier</span>
                  </button>
                )}
                
                <button
                  onClick={() => onDownload(memoizedDocument, isReceivedQuote || isReceivedInvoice || isReceivedCreditNote)}
                  className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                >
                  <Download size={16} className="mr-1.5" />
                  <span>PDF</span>
                </button>
                
                {!isReceivedQuote && !isReceivedInvoice && (
                  <button
                    onClick={onSendEmail}
                    className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                  >
                    <Mail size={16} className="mr-1.5" />
                    <span>Envoyer</span>
                  </button>
                )}

                {/* Bouton de duplication sur desktop */}
                {!isReceivedQuote && !isReceivedInvoice && (
                  <button
                    onClick={() => onDuplicate(memoizedDocument)}
                    className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                  >
                    <Copy size={16} className="mr-1.5" />
                    <span>Dupliquer</span>
                  </button>
                )}

                {/* Bouton de suppression sur desktop */}
                {!isReceivedQuote && !isReceivedInvoice && memoizedDocument.statut === 'brouillon' && (
                  <button
                    onClick={() => onDelete(memoizedDocument)}
                    className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <Trash size={16} className="mr-1.5" />
                    <span>Supprimer</span>
                  </button>
                )}

                {memoizedDocument.type === 'devis' && onConvertToInvoice && !isReceivedQuote && memoizedDocument.statut === 'accepte' && (
                  <button
                    onClick={() => onConvertToInvoice(memoizedDocument)}
                    className="flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#FF7A35] hover:bg-[#FF6A25] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                  >
                    <FileText size={16} className="mr-1.5" />
                    <span>Convertir</span>
                  </button>
                )}
                
                {isReceivedQuote && memoizedDocument.statut !== 'accepte' && memoizedDocument.statut !== 'facture' && memoizedDocument.statut !== 'refuse' && (
                  <>
                    <button
                      onClick={() => {
                        navigate(`/quote-acceptance/${memoizedDocument.id}`);
                      }}
                      className="flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#FF7A35] hover:bg-[#FF6A25] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                    >
                      <FileCheck size={16} className="mr-1.5" />
                      <span>Accepter</span>
                    </button>
                    {onReject && (
                      <button
                        onClick={() => onReject(memoizedDocument)}
                        className="flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ml-2"
                      >
                        <X size={16} className="mr-1.5" />
                        <span>Refuser</span>
                      </button>
                    )}
                  </>
                )}
                
                {memoizedDocument.type === 'facture' && onCreateCreditNote && !isReceivedQuote && (
                  <button
                    onClick={() => onCreateCreditNote(memoizedDocument)}
                    className="flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#FF7A35] hover:bg-[#FF6A25] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
                  >
                    <CreditCard size={16} className="mr-1.5" />
                    <span>Créer avoir</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Document Info */}
      <div className="p-6 md:p-8">
        <div className="flex flex-col md:flex-row justify-between">
          <div className="mb-6 md:mb-0">
            <div className="flex items-center mb-2">
              <h1 className="text-2xl font-bold text-gray-800 mr-3">
                {getDocumentTypeLabel()} {getDisplayNumber(memoizedDocument)}
                {isDraft(memoizedDocument) && (
                  <span className="ml-2 text-lg text-gray-500 italic">(Brouillon)</span>
                )}
              </h1>
              <span className={`px-2.5 py-1 text-xs rounded-full ${getStatusColorClass()}`}>
                {getStatusDisplay()}
              </span>
            </div>
            <p className="text-gray-600 mb-4">
              {formatDate(memoizedDocument.date_creation)}
              {memoizedDocument.date_validite && (
                <> - Valable jusqu'au {formatDate(memoizedDocument.date_validite)}</>
              )}
            </p>
            <div className="mb-4">
              <h3 className="font-medium text-gray-700 mb-1">Client</h3>
              <p className="text-gray-800 font-medium">{memoizedDocument.client_name}</p>
              {memoizedDocument.client_address && (
                <p className="text-gray-600">{memoizedDocument.client_address}</p>
              )}
              <div className="mt-2 space-y-1">
                {memoizedDocument.client_email && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">Email:</span> {memoizedDocument.client_email}
                  </p>
                )}
                {memoizedDocument.client_phone && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">Téléphone:</span> {memoizedDocument.client_phone}
                  </p>
                )}
                {memoizedDocument.client_siret && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">SIRET:</span> {memoizedDocument.client_siret}
                  </p>
                )}
                {memoizedDocument.client_tva && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">N° TVA:</span> {memoizedDocument.client_tva}
                  </p>
                )}
                {memoizedDocument.forme_juridique && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">Forme juridique:</span> {memoizedDocument.forme_juridique}
                  </p>
                )}
                {memoizedDocument.code_ape && (
                  <p className="text-gray-600 text-sm">
                    <span className="font-medium">Code APE:</span> {memoizedDocument.code_ape}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Afficher seulement sur mobile */}
          <div className="flex flex-col gap-3 md:hidden">
            <button
              onClick={() => setPreviewOpen(true)}
              className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
            >
              <FileText size={16} className="mr-1.5" />
              <span>Aperçu</span>
            </button>

            <button
              onClick={() => onEdit(memoizedDocument)}
              className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] inline-flex items-center"
            >
              <Edit size={16} className="mr-2" />
              Modifier
            </button>
            <button
              onClick={() => onDuplicate(memoizedDocument)}
              className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md inline-flex items-center hover:bg-gray-50"
            >
              <Copy size={16} className="mr-2" />
              Dupliquer
            </button>
            
            {/* Conditional buttons based on document type */}
            {memoizedDocument.type === 'devis' && onConvertToInvoice && !isReceivedQuote && memoizedDocument.statut === 'accepte' && (
              <button
                onClick={() => onConvertToInvoice(memoizedDocument)}
                className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md inline-flex items-center hover:bg-gray-50"
              >
                <FileText size={16} className="mr-2" />
                Convertir en facture
              </button>
            )}
            
            {memoizedDocument.type === 'facture' && onCreateCreditNote && !isReceivedQuote && (
              <button
                onClick={() => onCreateCreditNote(memoizedDocument)}
                className="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md inline-flex items-center hover:bg-gray-50"
              >
                <CreditCard size={16} className="mr-2" />
                Créer un avoir
              </button>
            )}
            
            {memoizedDocument.statut === 'brouillon' && (
              <button
                onClick={() => onDelete(memoizedDocument)}
                className="px-4 py-2 border border-gray-300 bg-white text-red-600 rounded-md inline-flex items-center hover:bg-red-50"
              >
                <Trash size={16} className="mr-2" />
                Supprimer
              </button>
            )}

            <button
              onClick={() => onSendEmail()}
              className="flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35]"
            >
              <Mail size={16} className="mr-1.5" />
              Envoyer par email
            </button>
          </div>
        </div>

        {/* Description */}
        {memoizedDocument.description && (
          <div className="mt-8">
            <h3 className="font-medium text-gray-700 mb-2">Description</h3>
            <p className="text-gray-600 whitespace-pre-line">{memoizedDocument.description}</p>
          </div>
        )}

        {/* Items Table */}
        <div className="mt-8">
          <h3 className="font-medium text-gray-700 mb-4">Détail des articles</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantité
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prix unitaire
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TVA
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total HT
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {memoizedDocument.invoice_items && memoizedDocument.invoice_items.map((item, index) => (
                  <tr key={item.id || index}>
                    <td className="px-6 py-4 text-sm text-gray-700 max-w-md whitespace-pre-line">
                      <div>{item.description}</div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-right">
                      {item.quantite} {item.unite}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-right whitespace-nowrap">
                      {item.prix_unitaire.toFixed(2)} €
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-right whitespace-nowrap">
                      {item.taux_tva}%
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-700 text-right whitespace-nowrap">
                      {item.montant_ht.toFixed(2)} €
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Totals */}
        <div className="mt-6 md:flex md:justify-end">
          <div className="md:w-1/3">
            <div className="border-t border-gray-200 pt-4 pb-2">
              <div className="flex justify-between py-1.5">
                <div className="text-sm text-gray-600">Total HT</div>
                <div className="text-sm font-medium text-gray-800">
                  {memoizedDocument.total_ht !== undefined ? memoizedDocument.total_ht.toFixed(2) : '0.00'} €
                </div>
              </div>
              <div className="flex justify-between py-1.5">
                <div className="text-sm text-gray-600">Total TVA</div>
                <div className="text-sm font-medium text-gray-800">
                  {memoizedDocument.total_tva !== undefined ? memoizedDocument.total_tva.toFixed(2) : '0.00'} €
                </div>
              </div>
              <div className="flex justify-between py-1.5 border-t border-gray-200 mt-2">
                <div className="text-base font-medium text-gray-800">Total TTC</div>
                <div className="text-base font-bold text-gray-800">
                  {memoizedDocument.total_ttc !== undefined ? memoizedDocument.total_ttc.toFixed(2) : '0.00'} €
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Legal mentions */}
        {(memoizedDocument.mentions_legales || memoizedDocument.mentions_tva || memoizedDocument.penalite_retard || memoizedDocument.indemnite_recouvrement) && (
          <div className="mt-10 border-t border-gray-200 pt-6">
            <h3 className="font-medium text-gray-700 mb-4">Mentions légales</h3>
            <div className="text-sm text-gray-600 space-y-3">
              {memoizedDocument.mentions_legales && <p>{memoizedDocument.mentions_legales}</p>}
              {memoizedDocument.mentions_tva && <p>{memoizedDocument.mentions_tva}</p>}
              {memoizedDocument.penalite_retard && <p>Pénalités de retard : {memoizedDocument.penalite_retard}</p>}
              {memoizedDocument.indemnite_recouvrement && <p>Indemnité forfaitaire de recouvrement : {memoizedDocument.indemnite_recouvrement}</p>}
            </div>
          </div>
        )}

        {/* Notes */}
        {memoizedDocument.notes && (
          <div className="mt-10 border-t border-gray-200 pt-6">
            <h3 className="font-medium text-gray-700 mb-4">Notes</h3>
            <div className="text-gray-600 whitespace-pre-line">
              {memoizedDocument.notes}
            </div>
          </div>
        )}

        {/* Conditions de paiement et mentions légales */}
        <div className="mt-6 border-t border-gray-200 pt-4 space-y-4">
          {memoizedDocument.conditions_paiement && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Conditions de paiement</h4>
              <p className="text-gray-600">{memoizedDocument.conditions_paiement}</p>
            </div>
          )}
          
          {memoizedDocument.mode_paiement && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Mode de paiement</h4>
              <p className="text-gray-600">
                {memoizedDocument.mode_paiement === 'virement' && 'Virement bancaire'}
                {memoizedDocument.mode_paiement === 'carte' && 'Carte bancaire'}
                {memoizedDocument.mode_paiement === 'cheque' && 'Chèque'}
                {memoizedDocument.mode_paiement === 'jobi' && 'Jobi'}
                {memoizedDocument.mode_paiement === 'especes' && 'Espèces'}
              </p>
            </div>
          )}
          
          {memoizedDocument.type === 'facture' && memoizedDocument.penalite_retard && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Pénalités de retard</h4>
              <p className="text-gray-600">{memoizedDocument.penalite_retard}</p>
            </div>
          )}
          
          {memoizedDocument.type === 'facture' && memoizedDocument.indemnite_recouvrement && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Indemnité forfaitaire</h4>
              <p className="text-gray-600">{memoizedDocument.indemnite_recouvrement}</p>
            </div>
          )}
          
          {memoizedDocument.mentions_tva && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Mentions TVA</h4>
              <p className="text-gray-600">{memoizedDocument.mentions_tva}</p>
            </div>
          )}
          
          {memoizedDocument.notes && (
            <div>
              <h4 className="font-medium text-gray-700 mb-1">Notes</h4>
              <p className="text-gray-600">{memoizedDocument.notes}</p>
            </div>
          )}
        </div>

        {/* Pied de page avec mentions de l'entreprise */}
        <div className="border-t border-gray-200 pt-4 mt-8 text-sm text-gray-500">
          {!companySettings?.mention_pied_page && (
            <>
              <p>{companySettings?.nom || 'Votre Entreprise'}{companySettings?.forme_juridique ? ` - ${companySettings.forme_juridique}` : ''}</p>
              {companySettings?.adresse && <p>{companySettings.adresse}, {companySettings.code_postal} {companySettings.ville}</p>}
              {companySettings?.siret && <p>SIRET: {companySettings.siret}{companySettings?.code_ape ? ` - APE: ${companySettings.code_ape}` : ''}</p>}
              {companySettings?.email && <p>Email: {companySettings.email} - Tél: {companySettings?.telephone || 'Non renseigné'}</p>}
              {companySettings?.capital && <p>Capital: {companySettings.capital}</p>}
            </>
          )}
        </div>
      </div>

      {/* Modal d'historique */}
      <ModalPortal isOpen={historyModalOpen} onBackdropClick={() => setHistoryModalOpen(false)}>
        <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[92vh] overflow-hidden flex flex-col">
          <div className="flex justify-between items-center p-5 border-b bg-gradient-to-r from-[#FFF8F3] to-white">
            <h3 className="text-xl font-semibold text-gray-800 flex items-center">
              <History size={22} className="mr-3 text-[#FF7A35]" />
              Historique du {getDocumentTypeLabel()} {getDisplayNumber(memoizedDocument)}
            </h3>
            <button 
              onClick={() => setHistoryModalOpen(false)}
              className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full p-2 transition-colors"
            >
              <X size={22} />
            </button>
          </div>
          
          <div className="flex-1 overflow-auto bg-gray-50 p-6">
            <InvoiceHistory documentId={memoizedDocument.id} />
          </div>
        </div>
      </ModalPortal>

      {/* Modal de prévisualisation */}
      <DocumentPreviewModal
        document={memoizedDocument}
        isOpen={previewOpen}
        onClose={() => setPreviewOpen(false)}
        onDownload={(doc) => onDownload(doc, isReceivedQuote || isReceivedInvoice || isReceivedCreditNote)}
        isReceivedQuote={isReceivedQuote}
        isReceivedInvoice={isReceivedInvoice}
        isReceivedCreditNote={isReceivedCreditNote}
      />

      {/* Modal de statut pour mobile */}
      {showMobileStatusModal && (
        <ModalPortal isOpen={showMobileStatusModal} onBackdropClick={() => setShowMobileStatusModal(false)} closeOnBackdropClick={true}>
          <div className="bg-white rounded-lg shadow-xl w-4/5 max-w-sm overflow-hidden">
            <div className="bg-[#FF7A35] text-white px-4 py-3 font-medium">
              Changer le statut
            </div>
            <div>
              {availableInvoiceStatuses && availableInvoiceStatuses.map(status => (
                <button
                  key={status}
                  className="block w-full text-left px-4 py-3 border-b border-gray-100 hover:bg-gray-50"
                  onClick={() => {
                    if (status === 'paye' || status === 'partiellement_paye') {
                      setStatusToConfirm(status);
                      setShowPaymentConfirmation(true);
                      setShowMobileStatusModal(false);
                    } else if (onUpdateInvoiceStatus) {
                      onUpdateInvoiceStatus(memoizedDocument, status);
                      setShowMobileStatusModal(false);
                    }
                  }}
                >
                  <span className={`inline-block w-3 h-3 rounded-full mr-2 ${typeof getStatusColor === 'function' ? getStatusColor(status) : ''}`}></span>
                  {typeof getStatusText === 'function' ? getStatusText(status, 'facture') : status}
                </button>
              ))}
            </div>
            <button
              className="block w-full text-center px-4 py-3 text-[#FF7A35] font-medium"
              onClick={() => setShowMobileStatusModal(false)}
            >
              Annuler
            </button>
          </div>
        </ModalPortal>
      )}

      {/* Boîte de dialogue de confirmation pour le statut payé */}
      {showPaymentConfirmation && (
        <ModalPortal isOpen={showPaymentConfirmation} onBackdropClick={() => setShowPaymentConfirmation(false)} closeOnBackdropClick={true}>
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Confirmation de changement de statut
              </h3>
              <button
                type="button"
                className="text-gray-400 hover:text-gray-500"
                onClick={() => setShowPaymentConfirmation(false)}
              >
                <span className="sr-only">Fermer</span>
                <X size={20} />
              </button>
            </div>
            <div>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Lorsque vous marquez cette facture comme {statusToConfirm === 'paye' ? "payée" : "partiellement payée"}, 
                  vous ne pourrez plus modifier son statut par la suite.
                </p>

                <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        Cette action est irréversible. Un email sera envoyé au client pour l'informer du paiement.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
                      checked={paymentConfirmed}
                      onChange={(e) => setPaymentConfirmed(e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-700">Je confirme que je ne pourrai plus modifier le statut de cette facture une fois cette action effectuée.</span>
                  </label>
                </div>
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                onClick={() => setShowPaymentConfirmation(false)}
              >
                Annuler
              </button>
              <button
                type="button"
                className={`inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-[#FF7A35] text-sm font-medium text-white hover:bg-[#ff6b2c] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ${!paymentConfirmed ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => {
                  if (paymentConfirmed && statusToConfirm && onUpdateInvoiceStatus) {
                    onUpdateInvoiceStatus(memoizedDocument, statusToConfirm);
                    setShowPaymentConfirmation(false);
                    setPaymentConfirmed(false);
                    setStatusToConfirm(null);
                  }
                }}
                disabled={!paymentConfirmed}
              >
                Confirmer
              </button>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
};

export default DocumentDetails; 