import { notify } from '@/components/Notification';

export const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Fonction utilitaire pour obtenir l'URL de base du storage
export const getStorageBaseUrl = () => {
    return API_URL;
};

export const API_CONFIG = {
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    withCredentials: true,  // Pour envoyer les cookies
};

// Endpoints
export const ENDPOINTS = {
    AUTH: {
        LOGIN: '/api/auth/login',
        REGISTER: '/api/auth/inscription',
        LOGOUT: '/api/auth/logout',
        VERIFY_EMAIL: '/api/auth/verify-email',
        RESEND_VERIFICATION: '/api/auth/resend-verification',
        FORGOT_PASSWORD: '/api/auth/forgot-password',
        RESET_PASSWORD: '/api/auth/reset-password',
        UPDATE_PASSWORD: '/api/auth/update-password',
        UPDATE_PROFILE: '/api/auth/profil',
    },
    USER: {
        PROFILE: '/api/users/profil',
        UPDATE: '/api/user/update',
    },
    // Ajoutez d'autres endpoints selon besoins
};

// Intercepteur pour les erreurs 401
export const setupAxiosInterceptors = (logout: () => Promise<void>) => {
    const handleUnauthorizedError = async (error: any) => {
        if (error.response?.status === 401) {
            if (window.location.pathname !== '/login') {
                window.location.href = '/login';
                await logout();
                notify('Votre session a expiré. Veuillez vous reconnecter.', 'warning');
            }
        }
        return Promise.reject(error);
    };

    // Intercepteur de réponse global
    const responseInterceptor = (response: Response) => {
        if (response.status === 401) {
            handleUnauthorizedError({ response });
        }
        return response;
    };

    // Ajouter l'intercepteur à fetch
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
        try {
            const response = await originalFetch(...args);
            return responseInterceptor(response);
        } catch (error: any) {
            return handleUnauthorizedError(error);
        }
    };
};
