import { useState, useEffect, useCallback, useRef } from 'react';
import supportTicketService, { 
  ResponseTemplate, 
  TemplateFilters, 
  CreateTemplateDto 
} from '../services/supportTicketService';
import { logger } from '../utils/logger';

// Cache global pour stocker les templates entre les montages de composants
// Cela évite de refaire des requêtes API en mode strict React
const globalTemplatesCache: {
  templates: ResponseTemplate[];
  timestamp: number;
  filters: string;
} = {
  templates: [],
  timestamp: 0,
  filters: ''
};

// Durée de validité du cache en millisecondes (5 minutes)
const CACHE_VALIDITY_DURATION = 5 * 60 * 1000;

/**
 * Fonction pour invalider le cache global des templates
 * À appeler après chaque modification (création, mise à jour, suppression)
 */
export const invalidateTemplatesCache = (): void => {
  logger.info('🔄 Invalidation du cache global des templates');
  globalTemplatesCache.templates = [];
  globalTemplatesCache.timestamp = 0;
  globalTemplatesCache.filters = '';
  // Demander également au service d'invalider son cache
  supportTicketService.invalidateTemplatesCache?.();
};

interface UseResponseTemplatesResult {
  templates: ResponseTemplate[];
  loading: boolean;
  error: Error | null;
  fetchTemplates: (filters?: TemplateFilters) => Promise<void>;
  getTemplate: (id: string) => Promise<ResponseTemplate | null>;
  createTemplate: (templateData: CreateTemplateDto) => Promise<ResponseTemplate | null>;
  updateTemplate: (id: string, templateData: Partial<ResponseTemplate>) => Promise<ResponseTemplate | null>;
  deleteTemplate: (id: string) => Promise<boolean>;
  invalidateCache: () => void;
}

/**
 * Hook pour gérer les modèles de réponse de support
 * @param initialFilters Filtres initiaux à appliquer
 * @returns Un objet avec les modèles, l'état de chargement, les erreurs et les méthodes pour manipuler les modèles
 */
export const useResponseTemplates = (initialFilters: TemplateFilters = {}): UseResponseTemplatesResult => {
  const [templates, setTemplates] = useState<ResponseTemplate[]>(globalTemplatesCache.templates);
  const [loading, setLoading] = useState<boolean>(globalTemplatesCache.templates.length === 0);
  const [error, setError] = useState<Error | null>(null);
  // Compteur de tentatives pour forcer le chargement
  const retryCountRef = useRef<number>(0);
  
  // Utiliser une référence pour stocker les filtres actuels
  // afin d'éviter les re-rendus en cascade
  const filtersRef = useRef<TemplateFilters>(initialFilters);
  
  // Flag pour suivre si le composant est monté
  const isMounted = useRef<boolean>(true);
  
  // Flag pour savoir si on a déjà fait une requête initiale
  const initialRequestMadeRef = useRef<boolean>(false);
  
  // Flag pour bloquer les rechargements multiples
  const isLoadingRef = useRef<boolean>(false);
  
  // Compteur pour s'assurer que nous ne traitons que la dernière requête
  const requestIdRef = useRef<number>(0);

  /**
   * Vérifier si le cache est valide
   */
  const isCacheValid = useCallback((filters: TemplateFilters): boolean => {
    // Convertir les filtres en string pour pouvoir les comparer
    const filtersString = JSON.stringify(filters);
    
    // Le cache est valide si:
    // 1. Il contient des templates
    // 2. Les filtres sont les mêmes
    // 3. Le cache n'est pas trop vieux
    return (
      globalTemplatesCache.templates.length > 0 &&
      globalTemplatesCache.filters === filtersString &&
      Date.now() - globalTemplatesCache.timestamp < CACHE_VALIDITY_DURATION
    );
  }, []);

  /**
   * Récupère les modèles avec les filtres spécifiés
   * Cette fonction ne déclenche PAS directement de mise à jour d'état
   * qui pourrait créer une boucle
   */
  const fetchTemplatesFromAPI = useCallback(async (filters: TemplateFilters): Promise<ResponseTemplate[]> => {
    // Si une requête est déjà en cours, retourner les templates existants
    if (isLoadingRef.current) {
      logger.info('🔄 Requête déjà en cours, utilisation des templates existants');
      return templates;
    }
    
    const requestId = ++requestIdRef.current;
    isLoadingRef.current = true;
    
    // Vérifier si on peut utiliser le cache
    if (isCacheValid(filters)) {
      logger.info('✅ Utilisation du cache de templates:', {
        cacheSize: globalTemplatesCache.templates.length,
        cacheAge: Math.round((Date.now() - globalTemplatesCache.timestamp) / 1000) + 's'
      });
      isLoadingRef.current = false;
      return globalTemplatesCache.templates;
    }
    
    try {
      // Uniquement logger la première requête pour éviter les doublons en mode strict
      if (!initialRequestMadeRef.current) {
        logger.info('Appel API pour les modèles avec filtres:', filters);
        initialRequestMadeRef.current = true;
      }
      
      const response = await supportTicketService.getResponseTemplates(filters);
      
      // Vérifie si la requête est toujours la plus récente
      if (requestId !== requestIdRef.current && retryCountRef.current < 3) {
        logger.info('Requête obsolète ignorée:', requestId, 'actuelle:', requestIdRef.current);
        isLoadingRef.current = false;
        return templates;
      }
      
      // Filtrer les templates invalides
      const validTemplates = response.filter(template => {
        return template && template.id && template.title;
      });
      
      // Mettre à jour le cache global
      if (validTemplates.length > 0) {
        globalTemplatesCache.templates = validTemplates;
        globalTemplatesCache.timestamp = Date.now();
        globalTemplatesCache.filters = JSON.stringify(filters);
        
        logger.info(`🔄 Cache de templates mis à jour avec ${validTemplates.length} templates`);
      } else if (response.length === 0) {
        // Si l'API renvoie une liste vide, c'est valide aussi
        globalTemplatesCache.templates = [];
        globalTemplatesCache.timestamp = Date.now();
        globalTemplatesCache.filters = JSON.stringify(filters);
        
        logger.info('🔄 Cache de templates mis à jour avec une liste vide');
      }
      
      isLoadingRef.current = false;
      return validTemplates;
    } catch (error) {
      logger.error('Erreur lors de la récupération des modèles de réponse:', error);
      isLoadingRef.current = false;
      throw error;
    }
  }, [isCacheValid, templates]);

  /**
   * Fonction publique pour récupérer les modèles
   * Cette fonction peut être appelée par les composants utilisateurs
   */
  const fetchTemplates = useCallback(async (newFilters?: TemplateFilters): Promise<void> => {
    if (!isMounted.current) return;
    
    // Éviter les appels multiples en parallèle
    if (isLoadingRef.current) {
      logger.info('⏳ Chargement déjà en cours, requête ignorée');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Mettre à jour la référence des filtres si de nouveaux filtres sont fournis
      if (newFilters) {
        filtersRef.current = newFilters;
      }
      
      // Utiliser les filtres actuels (de la référence)
      const filtersToUse = filtersRef.current;
      
      // Récupérer les modèles avec les filtres actuels
      const response = await fetchTemplatesFromAPI(filtersToUse);
      
      // Mettre à jour l'état seulement si le composant est toujours monté
      if (isMounted.current) {
        setTemplates(response);
      }
    } catch (error) {
      if (isMounted.current) {
        logger.error('Erreur lors de la récupération des modèles de réponse:', error);
        setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [fetchTemplatesFromAPI]);

  /**
   * Récupère un modèle par son ID
   */
  const getTemplate = useCallback(async (id: string): Promise<ResponseTemplate | null> => {
    if (!isMounted.current) return null;
    
    try {
      setLoading(true);
      setError(null);
      const template = await supportTicketService.getResponseTemplate(id);
      return template;
    } catch (error) {
      logger.error(`Erreur lors de la récupération du modèle ${id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return null;
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, []);

  /**
   * Fonction pour invalider le cache local du hook
   */
  const invalidateCache = useCallback(() => {
    logger.info('🧹 Invalidation du cache local du hook');
    
    // Invalider le cache global
    invalidateTemplatesCache();
    
    // Réinitialiser les flags locaux
    initialRequestMadeRef.current = false;
    
    // Forcer un rechargement seulement si nécessaire et si aucun chargement n'est en cours
    if (isMounted.current && !isLoadingRef.current) {
      setLoading(true);
      fetchTemplatesFromAPI(filtersRef.current)
        .then(response => {
          if (isMounted.current) {
            setTemplates(response);
            setLoading(false);
          }
        })
        .catch(error => {
          if (isMounted.current) {
            setError(error instanceof Error ? error : new Error('Erreur inconnue'));
            setLoading(false);
          }
        });
    }
  }, [fetchTemplatesFromAPI]);

  /**
   * Crée un nouveau modèle
   */
  const createTemplate = useCallback(async (templateData: CreateTemplateDto): Promise<ResponseTemplate | null> => {
    if (!isMounted.current) return null;
    
    try {
      setLoading(true);
      setError(null);
      
      // Ajout de logs plus détaillés
      logger.info('Début de création de template avec les données suivantes:', JSON.stringify(templateData));
      
      // Vérification des données avant envoi
      if (!templateData.title || templateData.title.trim() === '') {
        const err = new Error('Le titre du modèle est requis');
        logger.error('Validation échouée:', err.message);
        throw err;
      }
      
      if (!templateData.content || templateData.content.trim() === '') {
        const err = new Error('Le contenu du modèle est requis');
        logger.error('Validation échouée:', err.message);
        throw err;
      }
      
      // Appel au service en loggant tous les détails
      logger.info('Appel au service createResponseTemplate avec les données nettoyées:', {
        title: templateData.title,
        content: templateData.content ? `${templateData.content.substring(0, 30)}...` : null,
        category: templateData.category
      });
      
      // S'assurer que le service a les bons tokens avant de faire la requête
      // (Le service doit déjà gérer cela, mais ça ne fait pas de mal de le confirmer)
      const newTemplate = await supportTicketService.createResponseTemplate(templateData);
      
      // Vérification de la réponse
      if (!newTemplate) {
        logger.error('Erreur: createResponseTemplate a retourné une valeur nulle ou undefined');
        throw new Error('Le service a retourné une réponse vide');
      }
      
      logger.info('Template créé avec succès:', {
        id: newTemplate.id,
        title: newTemplate.title,
        category: newTemplate.category
      });
      
      // Invalider le cache après création
      invalidateCache();
      
      // Mettre à jour la liste locale des modèles
      if (isMounted.current) {
        setTemplates(prev => [...prev, newTemplate]);
      }
      return newTemplate;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      logger.error('Erreur détaillée lors de la création du modèle:', {
        message: errorMessage,
        error: JSON.stringify(error)
      });
      
      if (isMounted.current) {
        setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      }
      return null;
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [invalidateCache]);

  /**
   * Met à jour un modèle existant
   */
  const updateTemplate = useCallback(async (id: string, templateData: Partial<ResponseTemplate>): Promise<ResponseTemplate | null> => {
    if (!isMounted.current) return null;
    
    try {
      setLoading(true);
      setError(null);
      const updatedTemplate = await supportTicketService.updateResponseTemplate(id, templateData);
      
      // Invalider le cache après mise à jour
      invalidateCache();
      
      // Mettre à jour la liste locale des modèles
      if (isMounted.current && updatedTemplate) {
        setTemplates(prevTemplates => 
          prevTemplates.map(template => 
            template.id === id ? updatedTemplate : template
          )
        );
      }
      return updatedTemplate;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du modèle ${id}:`, error);
      if (isMounted.current) {
        setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      }
      return null;
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [invalidateCache]);

  /**
   * Supprime un modèle
   */
  const deleteTemplate = useCallback(async (id: string): Promise<boolean> => {
    if (!isMounted.current) return false;
    
    try {
      setLoading(true);
      setError(null);
      await supportTicketService.deleteResponseTemplate(id);
      
      // Invalider le cache après suppression
      invalidateCache();
      
      // Mettre à jour la liste locale des modèles
      if (isMounted.current) {
        setTemplates(prevTemplates => {
          const updatedTemplates = prevTemplates.filter(template => template.id !== id);
          logger.info(`Templates après suppression: ${updatedTemplates.length} templates`);
          return updatedTemplates;
        });
      }
      return true;
    } catch (error) {
      logger.error(`Erreur lors de la suppression du modèle ${id}:`, error);
      if (isMounted.current) {
        setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      }
      return false;
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [invalidateCache]);

  // Effet pour charger les modèles au montage du composant
  useEffect(() => {
    logger.info('🔄 Initialisation du hook useResponseTemplates');
    isMounted.current = true;
    
    // Charger les modèles initiaux seulement si nécessaire (cache invalide ou vide)
    const loadInitialTemplates = async () => {
      // Protection contre les doubles chargements
      if (isLoadingRef.current) {
        logger.info('⚠️ Chargement déjà en cours, initialisation ignorée');
        return;
      }
      
      // Si nous avons déjà des templates en cache, vérifions s'ils sont valides
      if (isCacheValid(initialFilters)) {
        logger.info('✅ Cache valide, utilisation des templates en cache');
        setTemplates(globalTemplatesCache.templates);
        setLoading(false);
        return;
      }
      
      logger.info('🚀 Chargement initial des templates dans le hook');
      try {
        // Charger une seule fois
        if (!initialRequestMadeRef.current) {
          await fetchTemplates(initialFilters);
        }
      } catch (err) {
        logger.error('❌ Erreur lors du chargement initial des templates:', err);
        setLoading(false);
      }
    };
    
    // Lancer le chargement immédiatement
    loadInitialTemplates();
    
    // Nettoyage lors du démontage
    return () => {
      logger.info('🔚 Démontage du hook useResponseTemplates');
      isMounted.current = false;
    };
  }, [fetchTemplates, isCacheValid, initialFilters]); // Dépendances nécessaires

  return {
    templates,
    loading,
    error,
    fetchTemplates,
    getTemplate,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    invalidateCache
  };
};

export default useResponseTemplates; 