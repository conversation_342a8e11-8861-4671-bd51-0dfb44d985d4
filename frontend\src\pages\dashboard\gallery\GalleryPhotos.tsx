import React, { useState, useEffect, useCallback } from 'react';
import { motion, PanInfo} from 'framer-motion';
import { DndContext, closestCenter, DragEndEvent, DragOverlay } from '@dnd-kit/core';
import { SortableContext, arrayMove, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Upload, Trash2, X, Edit2, MapPin, Tag, Info, ChevronLeft, ChevronRight, Camera } from 'lucide-react';
import { notify } from '../../../components/Notification';
import logger from '../../../utils/logger';
import { GalleryImage } from './types';
import { fetchGalleryPhotos, uploadPhoto, uploadPhotos, updatePhotoOrder, deletePhoto, updatePhotoCaption } from './api';
import { SortablePhoto } from './SortablePhoto';
import ModalPortal from '../../../components/ModalPortal';
import DOMPurify from 'dompurify';
import { CaptionInput } from './CaptionInput';
import imageCompression from 'browser-image-compression';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import useImageModeration from '../../../hooks/useImageModeration';
import ImageModerationStatus from '../../../components/ImageModerationStatus';
import RejectedImageMessage from '../../../components/RejectedImageMessage';
import AiGalleryImageGenerator from '../../../components/ai/AiGalleryImageGenerator';

const MAX_PHOTOS = 8;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

interface GalleryPhotosProps {
  galleryId: string;
  galleryName?: string;
  galleryDescription?: string;
  onClose?: () => void;
  onPhotosChange?: () => void;
  onUpdateProfil?: (isDeletion: boolean, galleryId: string) => void;
  handleAddPhotoToGallery?: (galleryId: string, photo: any) => void;
  handleRemovePhotoFromGallery?: (galleryId: string, photoId: string) => void;
}

const GalleryPhotos: React.FC<GalleryPhotosProps> = ({ galleryId, galleryName = '', galleryDescription = '', onPhotosChange, onUpdateProfil, handleAddPhotoToGallery, handleRemovePhotoFromGallery }) => {
  const { slug } = useParams();
  const { user } = useAuth();
  const isOwnProfil = !slug || slug === user?.profil?.data?.slug;
  const [photos, setPhotos] = useState<GalleryImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<GalleryImage | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [draggedPhoto, setDraggedPhoto] = useState<GalleryImage | null>(null);
  const [editingCaption, setEditingCaption] = useState('');
  const [visiblePhotosCount, setVisiblePhotosCount] = useState(8);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState<number>(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [compressionProgress, setCompressionProgress] = useState<number>(0);
  // États pour l'upload multiple avec modération
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [moderationImages, setModerationImages] = useState<File[]>([]);
  const [currentModerationIndex, setCurrentModerationIndex] = useState(0);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();
  const { moderateImage, isLoading: isModerationLoading } = useImageModeration();

  const loadPhotos = useCallback(async () => {
    try {
      const response = await fetchGalleryPhotos(galleryId);
      setPhotos(response.photos);
    } catch (error) {
      logger.error('Erreur lors du chargement des photos:', error);
      notify('Erreur lors du chargement des photos', 'error');
    }
  }, [galleryId]);

  useEffect(() => {
    loadPhotos();
  }, [loadPhotos]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);

    if (photos.length + fileArray.length > MAX_PHOTOS) {
      notify(`Vous ne pouvez pas ajouter plus de ${MAX_PHOTOS} photos par galerie.`, 'error');
      event.target.value = '';
      return;
    }

    // Vérifier le type et la taille de chaque fichier
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const validFiles: File[] = [];

    for (const file of fileArray) {
      if (!allowedTypes.includes(file.type)) {
        notify(`Le fichier ${file.name} n'est pas un format d'image valide. Formats acceptés : JPG, PNG, WEBP`, 'error');
        continue;
      }

      if (file.size > MAX_FILE_SIZE) {
        notify(`Le fichier ${file.name} dépasse la taille maximale de 5MB`, 'error');
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      event.target.value = '';
      return;
    }

    try {
      setIsUploading(true);
      setCompressionProgress(1);

      // Compresser toutes les images
      const compressedFiles: File[] = [];
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        const compressed = await imageCompression(file, {
          maxSizeMB: 0.4,
          maxWidthOrHeight: 400,
          useWebWorker: true,
          onProgress: (progress) => {
            const totalProgress = ((i / validFiles.length) + (progress / 100 / validFiles.length)) * 100;
            setCompressionProgress(Math.round(totalProgress));
          }
        });
        compressedFiles.push(compressed);
      }

      setCompressionProgress(100);
      setSelectedFiles(compressedFiles);

      // Si un seul fichier, utiliser l'ancienne méthode avec modération
      if (compressedFiles.length === 1) {
        const file = compressedFiles[0];
        const objectUrl = URL.createObjectURL(file);
        setModerationPreviewUrl(objectUrl);
        setIsModerationModalOpen(true);
        handleModerationWithFile(file);
      } else {
        // Pour plusieurs fichiers, démarrer la modération en lot
        setSelectedFiles(compressedFiles); // IMPORTANT: Mettre à jour selectedFiles AVANT d'appeler moderateImages
        setModerationImages([...compressedFiles]);
        setCurrentModerationIndex(0);
        setModerationPreviewUrl(URL.createObjectURL(compressedFiles[0]));
        setIsModerationModalOpen(true);

        // Attendre que les états soient mis à jour avant de lancer la modération
        setTimeout(() => {
          moderateImagesWithFiles(compressedFiles);
        }, 100);
      }

      // Réinitialiser le champ de fichier
      event.target.value = '';

    } catch (error) {
      logger.error('Erreur lors de la compression des fichiers:', error);
      notify('Erreur lors de la compression des fichiers', 'error');
      event.target.value = '';
      setCompressionProgress(0);
      setIsUploading(false);
    }
  };

  // Fonction pour modérer plusieurs images avec les fichiers passés en paramètre
  const moderateImagesWithFiles = async (files: File[]) => {
    if (files.length === 0) {
      return true; // Pas d'images à modérer
    }

    logger.info('Début de la modération de plusieurs images', { count: files.length });

    // Réinitialiser les états de rejet
    setIsImageRejected(false);
    setRejectionDescription(undefined);
    setRejectionImprovementSuggestions(undefined);

    try {
      // Modérer chaque image
      const moderatedResults = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        setCurrentModerationIndex(i);
        setModerationPreviewUrl(URL.createObjectURL(file));

        logger.info(`Modération de l'image ${i + 1}/${files.length}`, { fileName: file.name });

        const tempImageId = `image-temp-${Date.now()}-${i}`;
        const result = await moderateImage(file, 'gallery', tempImageId);

        logger.info(`Résultat modération image ${i + 1}`, { isSafe: result.isSafe });

        if (result.isSafe) {
          moderatedResults.push(file);
        } else {
          // Image refusée, afficher le message et arrêter
          setIsImageRejected(true);
          setRejectionDescription(result.description);
          setRejectionImprovementSuggestions(result.improvementSuggestions);
          notify(`Image ${i + 1} refusée : ne respecte pas nos règles de modération`, 'error');
          logger.info(`Image ${i + 1} refusée`, { description: result.description });
          return false;
        }
      }

      // Toutes les images sont validées, les uploader
      logger.info('Toutes les images validées, début upload', { count: moderatedResults.length });
      setIsModerationModalOpen(false);

      if (moderatedResults.length > 0) {
        const response = await uploadPhotos(galleryId, moderatedResults);
        logger.info('Réponse upload multiple', { response });

        // Recharger les photos
        await loadPhotos();
        notify(`${moderatedResults.length} photo(s) ajoutée(s) avec succès`, 'success');
        onPhotosChange?.();
        onUpdateProfil?.(false, galleryId);
      }

      return true;
    } catch (error) {
      logger.error('Erreur lors de la modération des images:', error);
      setIsModerationModalOpen(false);
      notify('Erreur lors de la vérification des images. Veuillez réessayer.', 'error');
      return false;
    } finally {
      setIsUploading(false);
      setCompressionProgress(0);
      setSelectedFiles([]);
      setModerationImages([]);
      setCurrentModerationIndex(0);
    }
  };

  const handleModerationWithFile = async (file: File) => {
    setIsUploading(true);
    try {
      // Modérer l'image
      const tempImageId = `image-temp-${Date.now()}`;
      const result = await moderateImage(file, 'gallery', tempImageId);

      // Le chargement s'arrête automatiquement après la modération

      if (result.isSafe) {
        // L'image est sûre, on peut l'envoyer
        notify('Image validée par la modération', 'success');

        // Réinitialiser les états de rejet
        setIsImageRejected(false);
        setRejectionDescription(undefined);
        setRejectionImprovementSuggestions(undefined);

        // Uploader l'image
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('tempImageId', tempImageId); // Ajouter l'ID temporaire
        const response = await uploadPhoto(galleryId, formData);

        if (response.photo) {
          // Assurons-nous que response.photo est bien défini avant de l'ajouter
          const newPhoto: GalleryImage = response.photo;
          setPhotos(prev => [...prev, newPhoto]);
          notify('Photo ajoutée avec succès', 'success');
          onPhotosChange?.();
          onUpdateProfil?.(false, galleryId);
          handleAddPhotoToGallery?.(galleryId, newPhoto);
        }

        // Fermer la modale de modération
        setIsModerationModalOpen(false);
      } else {
        // L'image est inappropriée
        notify('Image refusée : ne respecte pas nos règles de modération', 'error');

        // Afficher un message plus détaillé dans la console pour le débogage
        logger.info('Image refusée par la modération', {
          description: result.description,
          contentType: 'gallery'
        });

        // Mettre à jour les états pour afficher le message de rejet
        setIsImageRejected(true);
        setRejectionDescription(result.description);
        setRejectionImprovementSuggestions(result.improvementSuggestions);

        // S'assurer que la modale reste ouverte pour afficher le message détaillé
        setIsModerationModalOpen(true);

        // Ajouter un log pour le débogage
        logger.info('Modale de rejet ouverte', {
          isImageRejected: true,
          isModerationModalOpen: true,
          description: result.description
        });
      }
    } catch (err) {
      logger.error('Erreur lors de la modération ou de l\'upload de l\'image', err);
      notify('Erreur lors de la vérification ou de l\'upload de l\'image. Veuillez réessayer.', 'error');

      // En cas d'erreur, fermer la modale et réinitialiser les états
      setIsModerationModalOpen(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      setRejectionImprovementSuggestions(undefined);
    } finally {
      // Ne pas fermer la modale ici, car nous voulons afficher le message de rejet si l'image est refusée
      // La modale sera fermée dans le cas où l'image est acceptée ou en cas d'erreur

      // Réinitialiser la progression de compression et l'état d'upload
      setIsUploading(false);
      setCompressionProgress(0);

      // Ne pas nettoyer les ressources ici, car nous voulons les garder pour afficher le message de rejet
      // Elles seront nettoyées lorsque l'utilisateur fermera la modale
    }
  };

  const handleDragStart = (event: DragEndEvent) => {
    const { active } = event;
    const draggedPhoto = photos.find(p => p.id === active.id);
    if (draggedPhoto) {
      setDraggedPhoto(draggedPhoto);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedPhoto(null);

    if (!over || active.id === over.id) return;

    const oldIndex = photos.findIndex(photo => photo.id === active.id);
    const newIndex = photos.findIndex(photo => photo.id === over.id);

    if (oldIndex !== newIndex) {
      const newPhotos = arrayMove(photos, oldIndex, newIndex);
      setPhotos(newPhotos);

      try {
        updatePhotoOrder(galleryId, active.id as string, newIndex);
        notify('Position de la photo mise à jour', 'success');
      } catch (error) {
        logger.error('Erreur lors de la réorganisation des photos:', error);
        notify('Erreur lors de la réorganisation des photos', 'error');
        // Recharger les photos en cas d'erreur pour revenir à l'état initial
        loadPhotos();
      }
    }
  };

  const handleModalDragEnd = (_: any, info: PanInfo) => {
    const swipeThreshold = 50;
    if (Math.abs(info.offset.x) > swipeThreshold) {
      if (info.offset.x > 0 && currentPhotoIndex > 0) {
        // Swipe vers la droite - photo précédente
        setCurrentPhotoIndex(currentPhotoIndex - 1);
        setSelectedPhoto(photos[currentPhotoIndex - 1]);
        setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
      } else if (info.offset.x < 0 && currentPhotoIndex < photos.length - 1) {
        // Swipe vers la gauche - photo suivante
        setCurrentPhotoIndex(currentPhotoIndex + 1);
        setSelectedPhoto(photos[currentPhotoIndex + 1]);
        setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
      }
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const swipeThreshold = 50;
    const distance = touchStart - touchEnd;

    if (Math.abs(distance) > swipeThreshold) {
      if (distance > 0 && currentPhotoIndex < photos.length - 1) {
        // Swipe vers la gauche - photo suivante
        setCurrentPhotoIndex(currentPhotoIndex + 1);
        setSelectedPhoto(photos[currentPhotoIndex + 1]);
        setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
      } else if (distance < 0 && currentPhotoIndex > 0) {
        // Swipe vers la droite - photo précédente
        setCurrentPhotoIndex(currentPhotoIndex - 1);
        setSelectedPhoto(photos[currentPhotoIndex - 1]);
        setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
      }
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  const handleDeletePhoto = async () => {
    if (!selectedPhoto) return;

    try {
      await deletePhoto(galleryId, selectedPhoto.id);
      setPhotos(prev => prev.filter(p => p.id !== selectedPhoto.id));
      notify('Photo supprimée avec succès', 'success');
      onPhotosChange?.();
      onUpdateProfil?.(true, galleryId);
      handleRemovePhotoFromGallery?.(galleryId, selectedPhoto.id);
      setIsDeleteModalOpen(false);
      setSelectedPhoto(null);
    } catch (error: any) {
      // Si le statut est 204, c'est une réussite malgré l'erreur
      if (error.response?.status === 204) {
        setPhotos(prev => prev.filter(p => p.id !== selectedPhoto.id));
        notify('Photo supprimée avec succès', 'success');
        onPhotosChange?.();
        onUpdateProfil?.(true, galleryId);
        handleRemovePhotoFromGallery?.(galleryId, selectedPhoto.id);
        setIsDeleteModalOpen(false);
        setSelectedPhoto(null);
      } else {
        logger.error('Erreur lors de la suppression de la photo:', error);
        notify('Erreur lors de la suppression de la photo', 'error');
      }
    }
  };

  const handleUpdateCaption = async () => {
    if (!selectedPhoto) return;

    // Vérifier si la légende a été modifiée
    const sanitizedCaption = DOMPurify.sanitize(editingCaption);
    if (sanitizedCaption === selectedPhoto.caption) {
      setIsEditModalOpen(false);
      setSelectedPhoto(null);
      setEditingCaption('');
      notify('Aucune modification de légende détectée', 'info');
      return;
    }

    try {
      await updatePhotoCaption(galleryId, selectedPhoto.id, sanitizedCaption);
      setPhotos(prev => prev.map(p =>
        p.id === selectedPhoto.id ? { ...p, caption: sanitizedCaption } : p
      ));
      notify('Légende mise à jour avec succès', 'success');
      setIsEditModalOpen(false);
      setSelectedPhoto(null);
      setEditingCaption('');
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de la légende:', error);
      notify('Erreur lors de la mise à jour de la légende', 'error');
    }
  };

  return (
    <div className="max-w-[1920px] mx-auto p-6">
      <div className="bg-white rounded-2xl shadow-lg">
        {/* Contenu de la galerie */}
        <DndContext
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={photos.map(p => p.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-6">
              {photos.slice(0, visiblePhotosCount).map((photo, index) => (
                <SortablePhoto
                  key={photo.id}
                  photo={photo}
                  onEdit={() => {
                    setSelectedPhoto(photo);
                    setEditingCaption(photo.caption || '');
                    setCurrentPhotoIndex(photos.findIndex(p => p.id === photo.id));
                    setIsEditModalOpen(true);
                  }}
                  onDelete={() => {
                    setSelectedPhoto(photo);
                    setIsDeleteModalOpen(true);
                  }}
                  onClick={() => {
                    setSelectedPhoto(photo);
                    setEditingCaption(photo.caption || '');
                    setCurrentPhotoIndex(photos.findIndex(p => p.id === photo.id));
                    setIsEditModalOpen(true);
                  }}
                  currentIndex={index}
                  isOwnProfil={isOwnProfil}
                />
              ))}
            </div>
          </SortableContext>

          {photos.length > visiblePhotosCount && (
            <div className="flex justify-center mt-6 pb-6">
              <button
                onClick={() => setVisiblePhotosCount(prev => prev + 4)}
                className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors shadow-md hover:shadow-lg flex items-center space-x-2"
              >
                <span>Voir plus de photos</span>
              </button>
            </div>
          )}

          {/* Section upload */}
          {isOwnProfil && (
            <div className="p-8 bg-[#FFF8F3] border-t border-gray-100">
              <div className="flex flex-col items-center text-center space-y-4">
                <Upload className="h-8 w-8 text-[#FF6B2C]" />
                <h3 className="text-xl font-semibold text-gray-800">
                  Ajoutez plus de photos à votre galerie
                </h3>
                <p className="text-gray-600 max-w-lg">
                  Mettez en valeur vos réalisations en ajoutant des photos de qualité.
                </p>

                <div className="flex flex-col md:flex-row gap-4 w-full max-w-lg">
                  <label className="cursor-pointer flex-1">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl flex items-center justify-center space-x-3 h-full"
                    >
                      <Upload className="h-5 w-5" />
                      <span className="font-medium">Ajouter des photos</span>
                    </motion.div>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                      disabled={isUploading}
                    />
                  </label>

                  <div className="flex-1">
                    <AiGalleryImageGenerator
                      onImageGenerated={async (imageUrl, imageBase64, photoObj) => {
                        // Pour les images IA de galerie, l'image est déjà uploadée et confirmée par l'API IA
                        // On ajoute directement la photo à la liste sans passer par la modération locale
                        if (photoObj) {
                          const newPhoto: GalleryImage = {
                            id: photoObj.id || `temp-${Date.now()}`,
                            gallery_id: galleryId,
                            photo_url: photoObj.photo_url,
                            caption: photoObj.caption || undefined,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                            order: photos.length
                          };
                          setPhotos(prev => [...prev, newPhoto]);
                          notify('Photo IA ajoutée avec succès', 'success');
                          onPhotosChange?.();
                          onUpdateProfil?.(false, galleryId);
                          handleAddPhotoToGallery?.(galleryId, newPhoto);
                        }
                      }}
                      galleryId={galleryId}
                      galleryName={galleryName}
                      galleryDescription={galleryDescription}
                      isCover={false}
                      className="h-full"
                    />
                  </div>
                </div>

                <div className="text-sm text-gray-500">
                  Formats acceptés : JPG, PNG ou WEBP • Maximum {MAX_PHOTOS} photos • 5MB max par photo
                </div>
                {/* Barre de progression compression image */}
                {compressionProgress > 0 && compressionProgress < 100 && (
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${compressionProgress}%` }} />
                    <div className="text-xs text-center mt-1 text-gray-500">Compression en cours… {compressionProgress}%</div>
                  </div>
                )}
              </div>
            </div>
          )}

          <DragOverlay>
            {draggedPhoto && (
              <div className="relative bg-white rounded-xl shadow-xl transform scale-105">
                <img
                  src={draggedPhoto.photo_url}
                  alt={draggedPhoto.caption || 'Photo en cours de déplacement'}
                  className="w-full h-[250px] object-cover rounded-xl"
                />
                <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-100" />
                <div className="absolute bottom-4 left-4 right-4">
                  <p className="text-white text-sm line-clamp-2">
                    {draggedPhoto.caption || 'Aucune légende'}
                  </p>
                </div>
              </div>
            )}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Modal de visualisation/édition */}
      {isEditModalOpen && selectedPhoto && (
        <ModalPortal>
          <div
            className="fixed inset-0 flex items-center justify-center p-4 z-[60]"
            onClick={() => setIsEditModalOpen(false)}
          >
            <div
              className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] relative max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-8 bg-[#FFF8F3] pb-4 border-b border-[#FFE4BA]">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-white rounded-lg shadow-sm">
                    <Camera className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">
                    {selectedPhoto.caption || 'Photo de la galerie'}
                  </h2>
                </div>
                <button
                  onClick={() => setIsEditModalOpen(false)}
                  className="p-2 hover:bg-white rounded-lg text-[#FF6B2C] hover:text-[#FF965E] transition-all duration-300 hover:scale-105 transform"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="relative group mb-8">
                {/* Navigation buttons */}
                {currentPhotoIndex > 0 && (
                  <button
                    onClick={() => {
                      setCurrentPhotoIndex(currentPhotoIndex - 1);
                      setSelectedPhoto(photos[currentPhotoIndex - 1]);
                      setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
                    }}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-r-2xl shadow-lg transition-all duration-300 z-50 group-hover:translate-x-0 -translate-x-2 opacity-90 hover:opacity-100"
                  >
                    <ChevronLeft className="h-6 w-6 text-[#FF6B2C]" />
                  </button>
                )}
                {currentPhotoIndex < photos.length - 1 && (
                  <button
                    onClick={() => {
                      setCurrentPhotoIndex(currentPhotoIndex + 1);
                      setSelectedPhoto(photos[currentPhotoIndex + 1]);
                      setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
                    }}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-l-2xl shadow-lg transition-all duration-300 z-50 group-hover:translate-x-0 translate-x-2 opacity-90 hover:opacity-100"
                  >
                    <ChevronRight className="h-6 w-6 text-[#FF6B2C]" />
                  </button>
                )}

                <motion.div
                  className="relative overflow-hidden rounded-xl"
                  drag="x"
                  dragConstraints={{ left: 0, right: 0 }}
                  onDragEnd={handleModalDragEnd}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <img
                    src={selectedPhoto.photo_url}
                    alt={selectedPhoto.caption || 'Photo de galerie'}
                    className="w-full h-[400px] object-contain rounded-xl shadow-md"
                    draggable="false"
                  />
                </motion.div>
              </div>

              <div className={isOwnProfil ? "grid grid-cols-1 md:grid-cols-2 gap-6 mt-8" : "mt-8"}>
                <div className={`bg-white p-6 rounded-xl shadow-md ${!isOwnProfil ? "w-full" : ""}`}>
                  <div className="flex items-center gap-3 mb-4">
                    <MapPin className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Informations</h3>
                  </div>
                  <div className="space-y-3">
                    <p className="text-gray-600">
                      Ajoutée le {new Date(selectedPhoto.created_at).toLocaleDateString()}
                    </p>
                    <div className="pt-2 border-t border-gray-100">
                      <p className="text-gray-600">
                        <span className="font-medium text-gray-700">Légende :</span>{' '}
                        {selectedPhoto.caption || 'Aucune légende'}
                      </p>
                    </div>
                  </div>
                </div>

                {isOwnProfil && (
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <Tag className="h-6 w-6 text-[#FF6B2C]" />
                      <h3 className="text-xl font-semibold text-gray-800">Légende</h3>
                    </div>
                    <CaptionInput
                      value={editingCaption}
                      onChange={setEditingCaption}
                    />
                  </div>
                )}
              </div>

              {isOwnProfil && (
                <div className="mt-8 bg-white p-6 rounded-xl shadow-md">
                  <div className="flex items-center gap-3 mb-4">
                    <Info className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Actions</h3>
                  </div>
                  <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3">
                    <button
                      onClick={() => setIsDeleteModalOpen(true)}
                      className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors flex items-center justify-center gap-2 w-full sm:w-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span>Supprimer la photo</span>
                    </button>
                    <button
                      onClick={handleUpdateCaption}
                      className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center gap-2 w-full sm:w-auto"
                    >
                      <Edit2 className="h-4 w-4" />
                      <span>Enregistrer les modifications</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModalPortal>
      )}

      {isDeleteModalOpen && (
        <ModalPortal containerId="delete-confirmation-modal">
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Confirmer la suppression</h3>
              <p className="text-gray-600 mb-6">
                Êtes-vous sûr de vouloir supprimer cette photo ? Cette action est irréversible.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={handleDeletePhoto}
                  className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modal de modération d'image */}
      {isModerationModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay avec gestion du clic */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => {
                // Toujours permettre l'annulation, même pendant le chargement
                setIsModerationModalOpen(false);
                if (moderationPreviewUrl) {
                  URL.revokeObjectURL(moderationPreviewUrl);
                  setModerationPreviewUrl(null);
                }

                setIsImageRejected(false);
                setRejectionDescription(undefined);
              }}
              aria-label="Fermer la modale"
            />
            {/* Contenu de la modale */}
            <div
              className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-4 pt-5 flex justify-between items-center border-b">
                <h3 className="text-lg font-semibold">
                  {isModerationLoading
                    ? moderationImages.length > 1
                      ? `Analyse de sécurité (${currentModerationIndex + 1}/${moderationImages.length})`
                      : "Analyse de sécurité en cours"
                    : isImageRejected
                      ? "Image refusée"
                      : "Modération de l'image"}
                </h3>
                <button
                  onClick={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }

                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-full"
                  aria-label="Fermer"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              {/* Contenu scrollable */}
              <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
                {isModerationLoading || (!isModerationLoading && !isImageRejected) ? (
                  <>
                    <ImageModerationStatus
                      isLoading={isModerationLoading}
                      imageUrl={moderationPreviewUrl || undefined}
                      title={isModerationLoading ? "Analyse de sécurité en cours" : "Vérification de l'image"}
                      onCancel={() => {
                        // Toujours permettre l'annulation, même pendant le chargement
                        setIsModerationModalOpen(false);
                        if (moderationPreviewUrl) {
                          URL.revokeObjectURL(moderationPreviewUrl);
                          setModerationPreviewUrl(null);
                        }

                        setIsImageRejected(false);
                        setRejectionDescription(undefined);
                      }}
                    />
                    {!isModerationLoading && !isImageRejected && (
                      <div className="flex justify-center mt-6">
                        <p className="text-gray-600 text-sm">
                          Vérification automatique en cours...
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="p-6">
                    {/* Afficher l'image refusée */}
                    {moderationPreviewUrl && (
                      <div className="mb-6 flex justify-center">
                        <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                          <img
                            src={moderationPreviewUrl}
                            alt="Image refusée"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-red-900/20"></div>
                        </div>
                      </div>
                    )}

                    {/* Message de rejet détaillé */}
                    <RejectedImageMessage
                      contentType="gallery"
                      description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                      improvementSuggestions={rejectionImprovementSuggestions}
                      variant="detailed"
                    />

                    {/* Bouton pour réessayer */}
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => {
                          setIsModerationModalOpen(false);
                          if (moderationPreviewUrl) {
                            URL.revokeObjectURL(moderationPreviewUrl);
                            setModerationPreviewUrl(null);
                          }

                          setIsImageRejected(false);
                          setRejectionDescription(undefined);
                          setRejectionImprovementSuggestions(undefined);

                          // Ouvrir automatiquement le sélecteur de fichier
                          setTimeout(() => {
                            const input = document.querySelector('input[type="file"]') as HTMLInputElement;
                            if (input) {
                              input.click();
                            }
                          }, 300);
                        }}
                        className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                      >
                        Choisir une autre image
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
};

export default GalleryPhotos;