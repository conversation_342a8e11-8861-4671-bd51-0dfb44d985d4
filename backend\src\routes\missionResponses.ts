import { Router } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateRequest } from '../middleware/validation';
import { z } from 'zod';
import rateLimit from 'express-rate-limit';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

const router = Router();

// Création du rate limiter pour les réponses prédéfinies
const responsesLimiter = rateLimit({
  windowMs: 60 * 1000, // fenêtre de 1 minute
  max: 30, // 30 requêtes par minute
  message: {
    message: "Trop de requêtes pour les réponses prédéfinies. Veuillez réessayer dans 1 minute.",
    success: false,
    toastType: "error"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);
router.use(responsesLimiter);

// Schema de validation pour la création/mise à jour d'une réponse prédéfinie
const missionResponseSchema = z.object({
  title: z.string().min(3, "Le titre doit contenir au moins 3 caractères").max(50, "Le titre ne doit pas dépasser 50 caractères"),
  content: z.string().min(10, "Le contenu doit contenir au moins 10 caractères").max(1000, "Le contenu ne doit pas dépasser 1000 caractères"),
  order_index: z.number().optional()
});

// Récupérer toutes les réponses prédéfinies de l'utilisateur
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: "Utilisateur non authentifié",
        toastType: "error"
      });
      return;
    }

    // Vérifier si les données sont en cache
    const cacheKey = `mission_responses:${userId}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      // Retourner les données du cache
      res.status(200).json(JSON.parse(cachedData));
      return;
    }

    const { data, error } = await supabase
      .from('user_mission_responses')
      .select('*')
      .eq('user_id', userId)
      .order('order_index', { ascending: true });

    if (error) {
      logger.error('Erreur lors de la récupération des réponses prédéfinies:', error);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la récupération des réponses prédéfinies",
        toastType: "error"
      });
      return;
    }

    const response = { 
      success: true, 
      data 
    };
    
    // Mettre en cache pour 2 minutes
    await redis.setex(cacheKey, 120, JSON.stringify(response));

    res.status(200).json(response);
  } catch (error) {
    logger.error('Erreur lors de la récupération des réponses prédéfinies:', error);
    res.status(500).json({ 
      success: false, 
      message: "Erreur serveur lors de la récupération des réponses prédéfinies",
      toastType: "error"
    });
  }
});

// Créer une nouvelle réponse prédéfinie
router.post('/', validateRequest(missionResponseSchema), async (req, res) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: "Utilisateur non authentifié",
        toastType: "error"
      });
      return;
    }

    // Vérifier si l'utilisateur a déjà 10 réponses prédéfinies
    const { count, error: countError } = await supabase
      .from('user_mission_responses')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      logger.error('Erreur lors du comptage des réponses prédéfinies:', countError);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la vérification du nombre de réponses prédéfinies",
        toastType: "error"
      });
      return;
    }

    if (count && count >= 10) {
      res.status(400).json({ 
        success: false, 
        message: "Vous avez atteint le nombre maximum de réponses prédéfinies (10)",
        toastType: "warning"
      });
      return;
    }

    // Déterminer l'ordre de la nouvelle réponse
    let order_index = 0;
    if (count && count > 0) {
      const { data: maxOrderData, error: maxOrderError } = await supabase
        .from('user_mission_responses')
        .select('order_index')
        .eq('user_id', userId)
        .order('order_index', { ascending: false })
        .limit(1);

      if (!maxOrderError && maxOrderData && maxOrderData.length > 0) {
        order_index = (maxOrderData[0].order_index || 0) + 1;
      }
    }

    const { title, content } = req.body;
    const { data, error } = await supabase
      .from('user_mission_responses')
      .insert([
        { 
          user_id: userId, 
          title, 
          content,
          order_index: req.body.order_index !== undefined ? req.body.order_index : order_index
        }
      ])
      .select();

    if (error) {
      logger.error('Erreur lors de la création de la réponse prédéfinie:', error);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la création de la réponse prédéfinie",
        toastType: "error"
      });
      return;
    }

    // Invalider le cache des réponses prédéfinies
    const cacheKey = `mission_responses:${userId}`;
    await redis.del(cacheKey);

    res.status(201).json({ 
      success: true, 
      message: "Réponse prédéfinie créée avec succès",
      data: data[0],
      toastType: "success"
    });
  } catch (error) {
    logger.error('Erreur lors de la création de la réponse prédéfinie:', error);
    res.status(500).json({ 
      success: false, 
      message: "Erreur serveur lors de la création de la réponse prédéfinie",
      toastType: "error"
    });
  }
});

// Mettre à jour une réponse prédéfinie
router.put('/:id', validateRequest(missionResponseSchema), async (req, res) => {
  try {
    const userId = req.user?.userId;
    const responseId = req.params.id;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: "Utilisateur non authentifié",
        toastType: "error"
      });
      return;
    }

    // Vérifier que la réponse appartient à l'utilisateur
    const { data: existingResponse, error: checkError } = await supabase
      .from('user_mission_responses')
      .select('*')
      .eq('id', responseId)
      .eq('user_id', userId)
      .single();

    if (checkError || !existingResponse) {
      res.status(404).json({ 
        success: false, 
        message: "Réponse prédéfinie non trouvée ou non autorisée",
        toastType: "error"
      });
      return;
    }

    const { title, content, order_index } = req.body;
    const { data, error } = await supabase
      .from('user_mission_responses')
      .update({ 
        title, 
        content,
        order_index: order_index !== undefined ? order_index : existingResponse.order_index,
        updated_at: new Date()
      })
      .eq('id', responseId)
      .eq('user_id', userId)
      .select();

    if (error) {
      logger.error('Erreur lors de la mise à jour de la réponse prédéfinie:', error);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la mise à jour de la réponse prédéfinie",
        toastType: "error"
      });
      return;
    }

    // Invalider le cache des réponses prédéfinies
    const cacheKey = `mission_responses:${userId}`;
    await redis.del(cacheKey);

    res.status(200).json({ 
      success: true, 
      message: "Réponse prédéfinie mise à jour avec succès",
      data: data[0],
      toastType: "success"
    });
  } catch (error) {
    logger.error('Erreur lors de la mise à jour de la réponse prédéfinie:', error);
    res.status(500).json({ 
      success: false, 
      message: "Erreur serveur lors de la mise à jour de la réponse prédéfinie",
      toastType: "error"
    });
  }
});

// Supprimer une réponse prédéfinie
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user?.userId;
    const responseId = req.params.id;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: "Utilisateur non authentifié",
        toastType: "error"
      });
      return;
    }

    // Vérifier que la réponse appartient à l'utilisateur
    const { data: existingResponse, error: checkError } = await supabase
      .from('user_mission_responses')
      .select('*')
      .eq('id', responseId)
      .eq('user_id', userId)
      .single();

    if (checkError || !existingResponse) {
      res.status(404).json({ 
        success: false, 
        message: "Réponse prédéfinie non trouvée ou non autorisée",
        toastType: "error"
      });
      return;
    }

    const { error } = await supabase
      .from('user_mission_responses')
      .delete()
      .eq('id', responseId)
      .eq('user_id', userId);

    if (error) {
      logger.error('Erreur lors de la suppression de la réponse prédéfinie:', error);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la suppression de la réponse prédéfinie",
        toastType: "error"
      });
      return;
    }

    // Invalider le cache des réponses prédéfinies
    const cacheKey = `mission_responses:${userId}`;
    await redis.del(cacheKey);

    res.status(200).json({ 
      success: true, 
      message: "Réponse prédéfinie supprimée avec succès",
      toastType: "success"
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression de la réponse prédéfinie:', error);
    res.status(500).json({ 
      success: false, 
      message: "Erreur serveur lors de la suppression de la réponse prédéfinie",
      toastType: "error"
    });
  }
});

// Réorganiser les réponses prédéfinies
router.post('/reorder', async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { items } = req.body;
    
    if (!userId) {
      res.status(401).json({ 
        success: false, 
        message: "Utilisateur non authentifié",
        toastType: "error"
      });
      return;
    }

    if (!Array.isArray(items) || items.length === 0) {
      res.status(400).json({ 
        success: false, 
        message: "Format de données invalide pour la réorganisation",
        toastType: "error"
      });
      return;
    }

    // Vérifier que toutes les réponses appartiennent à l'utilisateur
    const responseIds = items.map(item => item.id);
    const { data: existingResponses, error: checkError } = await supabase
      .from('user_mission_responses')
      .select('id')
      .eq('user_id', userId)
      .in('id', responseIds);

    if (checkError) {
      logger.error('Erreur lors de la vérification des réponses prédéfinies:', checkError);
      res.status(500).json({ 
        success: false, 
        message: "Erreur lors de la vérification des réponses prédéfinies",
        toastType: "error"
      });
      return;
    }

    if (!existingResponses || existingResponses.length !== responseIds.length) {
      res.status(400).json({ 
        success: false, 
        message: "Certaines réponses prédéfinies n'appartiennent pas à l'utilisateur",
        toastType: "error"
      });
      return;
    }

    // Mettre à jour l'ordre de chaque réponse
    for (const [index, item] of items.entries()) {
      const { error } = await supabase
        .from('user_mission_responses')
        .update({ order_index: index, updated_at: new Date() })
        .eq('id', item.id)
        .eq('user_id', userId);

      if (error) {
        logger.error(`Erreur lors de la mise à jour de l'ordre de la réponse ${item.id}:`, error);
        res.status(500).json({ 
          success: false, 
          message: "Erreur lors de la réorganisation des réponses prédéfinies",
          toastType: "error"
        });
        return;
      }
    }

    // Invalider le cache des réponses prédéfinies
    const cacheKey = `mission_responses:${userId}`;
    await redis.del(cacheKey);

    res.status(200).json({ 
      success: true, 
      message: "Réponses prédéfinies réorganisées avec succès",
      toastType: "success"
    });
  } catch (error) {
    logger.error('Erreur lors de la réorganisation des réponses prédéfinies:', error);
    res.status(500).json({ 
      success: false, 
      message: "Erreur serveur lors de la réorganisation des réponses prédéfinies",
      toastType: "error"
    });
  }
});

export default router; 