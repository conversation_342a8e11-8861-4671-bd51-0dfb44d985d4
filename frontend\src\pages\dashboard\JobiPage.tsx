import React, { useEffect, useState, useRef, useCallback } from 'react';
import axios from 'axios';
import { API_CONFIG, API_URL } from '../../config/api';
import { notify } from '../../components/Notification';
import DOMPurify from 'dompurify';
import { motion } from 'framer-motion';
import StarIcon from '@mui/icons-material/Star';
import DiscountIcon from '@mui/icons-material/AttachMoney';
import TrophyIcon from '@mui/icons-material/EmojiEvents';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import HistoryIcon from '@mui/icons-material/History';
import { styled } from '@mui/material/styles';  
import { Typography } from '@mui/material';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import { getCommonHeaders } from '../../utils/headers';
import logger from '../../utils/logger';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { fetchCsrfToken } from '../../services/csrf';

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  color: '#2D3748',
  marginBottom: '24px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '40px',
    height: '2px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.25rem',
  },
}));

interface JobiHistorique {
  id: string;
  titre: string;
  description: string;
  montant: number;
  message?: string;
  date_creation: string;
}

interface MonthlyStats {
  transactionCount: number;
  totalIn: number;
  totalOut: number;
}

interface User {
  id: string;
  usertag: string;
  avatar_url: string | null;
  type?: 'favori' | 'offre_recue' | 'offre_envoyee' | 'jobi_echange' | 'no_result';
  nom?: string;
  prenom?: string;
  dernierEchange?: {
    montant: number;
    date: string;
    type: 'envoi' | 'reception';
  };
}

const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

export default function JobiPage() {
  const [solde, setSolde] = useState<number>(0);
  const [historique, setHistorique] = useState<JobiHistorique[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [offset, setOffset] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [newTransactionsCount, setNewTransactionsCount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<'account' | 'history' | 'credit' | 'transfer'>('account');
  const [monthlyStats, setMonthlyStats] = useState<MonthlyStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState<boolean>(false);
  
  // États pour l'onglet transfert
  const [searchInputValue, setSearchInputValue] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [transferAmount, setTransferAmount] = useState<string>('');
  const [isTransferring, setIsTransferring] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const transactionsEndRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [showScrollArrow, setShowScrollArrow] = useState<boolean>(false);

  // État pour le message optionnel
  const [transferMessage, setTransferMessage] = useState<string>('');

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Récupérer le solde
      const soldeResponse = await axios.get('/api/jobi/solde', API_CONFIG);
      if (soldeResponse.data.success) {
        setSolde(soldeResponse.data.montant);
      }

      // Récupérer l'historique
      const historiqueResponse = await axios.get('/api/jobi/historique', API_CONFIG);
      if (historiqueResponse.data.success) {
        setHistorique(historiqueResponse.data.historique);
        if (historiqueResponse.data.historique.length < 10) {
          setHasMore(false);
        }
      }

      // Si nous sommes sur l'onglet compte, récupérer les statistiques mensuelles
      if (activeTab === 'account') {
        fetchMonthlyStats();
      }

    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Une erreur est survenue';
      setError(errorMessage);
      notify(errorMessage, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  useEffect(() => {
    // Charger les utilisateurs connus uniquement si on entre dans l'onglet transfert
    // et qu'aucun utilisateur n'est déjà sélectionné (cas d'un paiement en cours)
    if (activeTab === 'transfer' && !selectedUser) {
      loadKnownUsers();
    }
  }, [activeTab, selectedUser]);

  // Effet pour gérer les paramètres d'URL et le paiement
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tab = params.get('tab');
    const isPayment = params.get('payment') === 'true';

    if (tab) {
      setActiveTab(tab as 'account' | 'history' | 'credit' | 'transfer');
    }

    if (isPayment) {
      const paymentData = sessionStorage.getItem('payment_proposal');
      if (paymentData) {
        try {
          const proposal = JSON.parse(paymentData);
          
          // Rechercher les informations de l'utilisateur
          const fetchUserDetails = async () => {
            try {
              const headers = await getCommonHeaders();
              const response = await axios.get(
                `${API_CONFIG.baseURL}/api/users/profile-by-slug/${proposal.destinataire_usertag}`,
                { headers, withCredentials: true }
              );
              
              if (response.data.success) {
                const userData = response.data.user;
                // Définir l'utilisateur sélectionné et empêcher l'affichage des résultats de recherche
                setSelectedUser({
                  id: userData.id || '',
                  usertag: proposal.destinataire_usertag,
                  avatar_url: userData.photo_url || null,
                  nom: proposal.destinataire_nom || userData.nom,
                  prenom: proposal.destinataire_prenom || userData.prenom,
                  type: 'jobi_echange'
                });
                setSearchResults([]); // Vider les résultats de recherche
              } else {
                // Si l'utilisateur n'est pas trouvé, utiliser les informations de base
                setSelectedUser({
                  id: '',
                  usertag: proposal.destinataire_usertag,
                  avatar_url: null,
                  nom: proposal.destinataire_nom,
                  prenom: proposal.destinataire_prenom,
                  type: 'jobi_echange'
                });
                setSearchResults([]); // Vider les résultats de recherche
              }
            } catch (error) {
              logger.error('Erreur lors de la récupération des détails de l\'utilisateur:', error);
              // En cas d'erreur, utiliser quand même les informations de base
              setSelectedUser({
                id: '',
                usertag: proposal.destinataire_usertag,
                avatar_url: null,
                nom: proposal.destinataire_nom,
                prenom: proposal.destinataire_prenom,
                type: 'jobi_echange'
              });
              setSearchResults([]); // Vider les résultats de recherche
            } finally {
              setTransferAmount(proposal.montant.toString());
              setTransferMessage(`Paiement pour la mission: ${proposal.mission_title}`);
              setActiveTab('transfer');
            }
          };
          
          fetchUserDetails();
        } catch (error) {
          logger.error('Erreur lors du traitement des données de paiement:', error);
        }
      }
    }
  }, []);

  const loadKnownUsers = async () => {
    try {
      setIsSearching(true);
      const headers = await getCommonHeaders();
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/users/recup_users_connus_jobi`,
        { headers, withCredentials: true }
      );

      if (response.data.success) {
        logger.info('Users data received:', response.data.users);
        
        // Récupérer l'ID de l'utilisateur connecté pour l'exclure des résultats
        const currentUserId = await getCurrentUserId();
        
        // Chargement des données d'historique Jobi pour identifier les échanges
        const transactions = await fetchHistoriqueForAnalysis();
        logger.info('Historique récupéré pour analyse:', transactions);
        
        // Identifier les utilisateurs avec qui on a échangé des Jobi
        const jobiUsers = identifyJobiExchangeUsers(transactions);
        
        // Filtrer l'utilisateur actuel et enrichir les données
        const filteredUsers = response.data.users
          .filter((user: User) => user && user.id && user.id !== currentUserId)
          .map((user: User) => {
            // Vérifier si cet utilisateur est dans la liste des utilisateurs avec qui on a échangé des Jobi
            const jobiExchangeInfo = jobiUsers.find(u => u.usertag === user.usertag);
            
            return {
              ...user,
              usertag: user.usertag || 'utilisateur',
              avatar_url: user.avatar_url || null,
              type: jobiExchangeInfo ? 'jobi_echange' : (user.type || 'favori'),
              dernierEchange: jobiExchangeInfo?.dernierEchange
            };
          });
        
        setSearchResults(filteredUsers || []);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement des utilisateurs connus:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Fetches historique Jobi without updating the state, just for analysis
  const fetchHistoriqueForAnalysis = async (): Promise<JobiHistorique[]> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/jobi/historique`,
        { headers, withCredentials: true }
      );
      
      if (response.data.success) {
        return response.data.historique || [];
      }
      return [];
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'historique pour analyse:', error);
      return [];
    }
  };

  // Fonction pour identifier les utilisateurs avec qui on a échangé des Jobi
  const identifyJobiExchangeUsers = (historique: JobiHistorique[]): User[] => {
    const jobiUsers: User[] = [];
    
    // Parcourir l'historique pour identifier les transferts et réceptions
    historique.forEach(transaction => {
      // Analyse différents formats possibles de description
      let userTag: string | null = null;
      let montant = Math.abs(transaction.montant);
      let type: 'envoi' | 'reception' = transaction.montant < 0 ? 'envoi' : 'reception';
      
      // Regex pour différents formats de description
      const transferRegex = /Transfert de (\d+) Jobi à @([^\s]+)/;
      const receptionRegex = /Réception de (\d+) Jobi de @([^\s]+)/;
      const altTransferRegex = /Envoi de (\d+) Jobi à @([^\s]+)/;
      const altReceptionRegex = /Reçu (\d+) Jobi de @([^\s]+)/;
      
      if (transaction.description.match(transferRegex)) {
        const match = transaction.description.match(transferRegex);
        if (match && match[2]) {
          userTag = match[2];
          type = 'envoi';
          logger.info(`Transfert détecté: ${montant} Jobi à @${userTag}`);
        }
      } else if (transaction.description.match(receptionRegex)) {
        const match = transaction.description.match(receptionRegex);
        if (match && match[2]) {
          userTag = match[2];
          type = 'reception';
          logger.info(`Réception détectée: ${montant} Jobi de @${userTag}`);
        }
      } else if (transaction.description.match(altTransferRegex)) {
        const match = transaction.description.match(altTransferRegex);
        if (match && match[2]) {
          userTag = match[2];
          type = 'envoi';
          logger.info(`Transfert alternatif détecté: ${montant} Jobi à @${userTag}`);
        }
      } else if (transaction.description.match(altReceptionRegex)) {
        const match = transaction.description.match(altReceptionRegex);
        if (match && match[2]) {
          userTag = match[2];
          type = 'reception';
          logger.info(`Réception alternative détectée: ${montant} Jobi de @${userTag}`);
        }
      } else {
        // Recherche plus générique dans le titre et la description
        if (transaction.titre.includes('Transfert') || transaction.description.includes('Transfert')) {
          // Essayer d'extraire le tag utilisateur si présent
          const tagMatch = transaction.description.match(/@([^\s)]+)/);
          if (tagMatch && tagMatch[1]) {
            userTag = tagMatch[1];
            type = 'envoi';
            logger.info(`Transfert générique détecté: ${montant} Jobi à @${userTag}`);
          }
        } else if (transaction.titre.includes('Réception') || transaction.description.includes('Réception')) {
          const tagMatch = transaction.description.match(/@([^\s)]+)/);
          if (tagMatch && tagMatch[1]) {
            userTag = tagMatch[1];
            type = 'reception';
            logger.info(`Réception générique détectée: ${montant} Jobi de @${userTag}`);
          }
        }
      }
      
      if (userTag) {
        // Vérifier si l'utilisateur existe déjà dans notre liste
        const existingUserIndex = jobiUsers.findIndex(u => u.usertag === userTag);
        
        if (existingUserIndex >= 0) {
          // Si la transaction est plus récente, mettre à jour
          const existingDate = new Date(jobiUsers[existingUserIndex].dernierEchange?.date || '');
          const newDate = new Date(transaction.date_creation);
          
          if (newDate > existingDate) {
            jobiUsers[existingUserIndex].dernierEchange = {
              montant,
              date: transaction.date_creation,
              type
            };
          }
        } else {
          // Ajouter un nouvel utilisateur
          jobiUsers.push({
            id: '', // Sera mis à jour lors de la fusion avec les données utilisateur
            usertag: userTag,
            avatar_url: null,
            type: 'jobi_echange',
            dernierEchange: {
              montant,
              date: transaction.date_creation,
              type
            }
          });
        }
      }
    });
    
    logger.info('Utilisateurs avec échanges Jobi identifiés:', jobiUsers);
    return jobiUsers;
  };

  // Fonction pour récupérer l'ID de l'utilisateur connecté
  const getCurrentUserId = async (): Promise<string> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/users/profil`,
        { headers, withCredentials: true }
      );
      
      if (response.data.success && response.data.id) {
        return response.data.id;
      }
      return '';
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'ID utilisateur:', error);
      return '';
    }
  };

  const fetchMonthlyStats = async () => {
    try {
      setIsLoadingStats(true);
      const response = await axios.get('/api/jobi/monthly-stats', API_CONFIG);
      if (response.data.success) {
        setMonthlyStats(response.data.stats);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement des statistiques mensuelles:', error);
      notify('Erreur lors du chargement des statistiques', 'error');
    } finally {
      setIsLoadingStats(false);
    }
  };

  const loadMoreTransactions = async () => {
    if (isLoading || isLoadingMore || !hasMore) return;
    
    try {
      setIsLoadingMore(true);
      const newOffset = offset + 10;
      const historiqueResponse = await axios.get(`/api/jobi/historique?offset=${newOffset}`, API_CONFIG);
      
      if (historiqueResponse.data.success) {
        const newTransactions = historiqueResponse.data.historique;
        if (newTransactions.length < 10) {
          setHasMore(false);
        }
        
        // Marquer les nouvelles transactions pour l'animation
        setNewTransactionsCount(newTransactions.length);
        
        // Ajouter les nouvelles transactions à l'historique
        setHistorique(prev => [...prev, ...newTransactions]);
        setOffset(newOffset);

        // Réinitialiser le compteur après 3 secondes
        setTimeout(() => {
          setNewTransactionsCount(0);
        }, 3000);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Une erreur est survenue';
      notify(errorMessage, 'error');
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Observer setup pour l'infinite scroll - simplification et amélioration
  const lastTransactionElementRef = useCallback((node: HTMLDivElement | null) => {
    if (isLoadingMore || !hasMore) return;
    
    if (observerRef.current) observerRef.current.disconnect();
    
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
        logger.info('Element intersection detected, loading more transactions...');
        loadMoreTransactions();
      }
    }, { rootMargin: '200px', threshold: 0.1 });
    
    if (node) observerRef.current.observe(node);
  }, [isLoadingMore, hasMore, loadMoreTransactions]);

  // Nettoyer l'observateur lors du démontage du composant
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const clearSearch = () => {
    setSearchInputValue('');
    setSearchResults([]);
    setSelectedUser(null);
    loadKnownUsers();
  };

  const handleSearchInputChange = useCallback((value: string) => {
    setSearchInputValue(value);
    
    // Si le champ est vide, on charge les utilisateurs connus
    if (!value.trim()) {
      setSearchResults([]);
      loadKnownUsers();
      return;
    }

    // Ne lancer la recherche qu'à partir de 2 caractères
    if (value.trim().length >= 2) {
      // Utiliser un debounce de 750ms pour éviter le spam
      setIsSearching(true);
      const timeoutId = setTimeout(() => {
        handleSearch(value);
      }, 750);
      
      return () => {
        clearTimeout(timeoutId);
        setIsSearching(false);
      };
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, []);

  const handleSearch = async (searchTerm: string) => {
    try {
      setIsSearching(true);
      const headers = await getCommonHeaders();
      
      // Récupérer l'ID de l'utilisateur connecté
      const currentUserId = await getCurrentUserId();
      
      // Chargement des données d'historique Jobi pour identifier les échanges
      const transactions = await fetchHistoriqueForAnalysis();
      const jobiUsers = identifyJobiExchangeUsers(transactions);
      
      // Vérifier si le terme de recherche est une date (format JJ/MM/AAAA ou JJ-MM-AAAA)
      const dateRegex = /^(\d{2})[/-](\d{2})[/-](\d{4})$/;
      const dateMatch = searchTerm.match(dateRegex);
      
      if (dateMatch) {
        const [_, jour, mois, annee] = dateMatch;
        const searchDate = `${annee}-${mois}-${jour}`;
        
        // Filtrer les utilisateurs par date d'échange
        const usersParDate = jobiUsers.filter(user => 
          user.dernierEchange && user.dernierEchange.date.startsWith(searchDate)
        );
        
        if (usersParDate.length > 0) {
          // Enrichir les données des utilisateurs
          const enrichedUsers = await Promise.all(usersParDate.map(async (user) => {
            try {
              const response = await axios.get(
                `${API_CONFIG.baseURL}/api/users/profile-by-slug/${user.usertag}`,
                { headers, withCredentials: true }
              );
              
              if (response.data.success) {
                const userData = response.data.user;
                return {
                  ...user,
                  nom: userData.nom || '',
                  prenom: userData.prenom || '',
                  avatar_url: userData.photo_url || null
                };
              }
              return user;
            } catch (error) {
              return user;
            }
          }));
          
          setSearchResults(enrichedUsers);
          setIsSearching(false);
          return;
        }
      }
      
      // Vérifier si le terme de recherche est un montant
      const montantRecherche = parseFloat(searchTerm);
      if (!isNaN(montantRecherche)) {
        // Filtrer les utilisateurs par montant d'échange
        const usersParMontant = jobiUsers.filter(user => 
          user.dernierEchange && Math.abs(user.dernierEchange.montant) === montantRecherche
        );
        
        if (usersParMontant.length > 0) {
          // Enrichir les données des utilisateurs
          const enrichedUsers = await Promise.all(usersParMontant.map(async (user) => {
            try {
              const response = await axios.get(
                `${API_CONFIG.baseURL}/api/users/profile-by-slug/${user.usertag}`,
                { headers, withCredentials: true }
              );
              
              if (response.data.success) {
                const userData = response.data.user;
                return {
                  ...user,
                  nom: userData.nom || '',
                  prenom: userData.prenom || '',
                  avatar_url: userData.photo_url || null
                };
              }
              return user;
            } catch (error) {
              return user;
            }
          }));
          
          setSearchResults(enrichedUsers);
          setIsSearching(false);
          return;
        }
      }

      // Recherche par usertag et par nom/prénom
      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/users/search?query=${encodeURIComponent(searchTerm)}`,
        { headers, withCredentials: true }
      );

      if (response.data.success) {
        logger.info('Search results from API 2:', response.data.users);
        
        // Filtrer l'utilisateur actuel et enrichir les données avec l'historique
        const filteredUsers = response.data.users
          .filter((user: User) => user && user.id && user.id !== currentUserId)
          .map((user: User) => {
            // Vérifier si cet utilisateur a eu des échanges de Jobi
            const jobiExchangeInfo = jobiUsers.find(u => u.usertag === user.usertag);
            
            return {
              ...user,
              usertag: user.usertag || 'utilisateur',
              avatar_url: user.avatar_url || null,
              type: jobiExchangeInfo ? 'jobi_echange' : (user.type || 'favori'),
              dernierEchange: jobiExchangeInfo?.dernierEchange
            };
          });
        
        // Si aucun résultat trouvé, afficher un message
        if (filteredUsers.length === 0) {
          setSearchResults([{
            id: 'no-results',
            usertag: 'Aucun résultat',
            avatar_url: null,
            type: 'no_result',
            nom: `Aucun utilisateur trouvé pour "${searchTerm}"`,
            prenom: '',
          }]);
        } else {
          setSearchResults(filteredUsers);
        }
      } else {
        setSearchResults([{
          id: 'no-results',
          usertag: 'Aucun résultat',
          avatar_url: null,
          type: 'no_result',
          nom: `Aucun utilisateur trouvé pour "${searchTerm}"`,
          prenom: '',
        }]);
      }
    } catch (error) {
      logger.error('Erreur lors de la recherche d\'utilisateurs:', error);
      setSearchResults([{
        id: 'error',
        usertag: 'Erreur',
        avatar_url: null,
        type: 'no_result',
        nom: 'Une erreur est survenue lors de la recherche',
        prenom: '',
      }]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleUserSelect = (user: User) => {
    logger.info('Utilisateur sélectionné:', user);
    setSelectedUser(user);
    setSearchInputValue(''); // Vider le champ de recherche
    setSearchResults([]); // Effacer les résultats
  };

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser || !transferAmount || isTransferring) return;
    
    try {
      setIsTransferring(true);
      
      const amount = parseInt(transferAmount);
      if (isNaN(amount) || amount <= 0) {
        notify('Veuillez entrer un montant valide', 'error');
        setIsTransferring(false);
        return;
      }
      
      if (amount > solde) {
        notify('Solde insuffisant', 'error');
        setIsTransferring(false);
        return;
      }
      
      logger.info('Envoi de transfert avec:', {
        destinataire_usertag: selectedUser.usertag,
        montant: amount,
        message: transferMessage
      });
      
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      
      // Effectuer le transfert Jobi
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/jobi/echange_jobi`,
        {
          destinataire_usertag: selectedUser.usertag,
          montant: amount,
          message: transferMessage.trim() || undefined
        },
        { 
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true 
        }
      );
      
      if (response.data.success) {
        notify('Transfert réussi', 'success');
        
        // Vérifier si ce transfert était pour un paiement de mission
        const paymentData = sessionStorage.getItem('payment_proposal');
        if (paymentData) {
          try {
            const proposal = JSON.parse(paymentData);
            
            // Utiliser le montant saisi par l'utilisateur plutôt que le montant initial
            const paymentAmount = amount;
            
            // Mettre à jour le statut de paiement de la proposition
            const updateResponse = await axios.post(
              `${API_CONFIG.baseURL}/api/missions/propositions/${proposal.mission_id}/${proposal.id}/payment-status`,
              {
                status: 'completed',
                montant_paiement: paymentAmount
              },
              { 
                headers: {
                  ...headers,
                  'Content-Type': 'application/json',
                  'X-CSRF-Token': await fetchCsrfToken()
                },
                withCredentials: true 
              }
            );
            
            if (updateResponse.data.success) {
              notify('Statut de paiement mis à jour avec succès', 'success');
              
              // Stocker les informations de paiement terminé pour rafraîchir la page après redirection
              sessionStorage.setItem('payment_completed', JSON.stringify({
                proposalId: proposal.id,
                missionId: proposal.mission_id,
                amount: paymentAmount,
                status: 'completed',
                timestamp: new Date().toISOString()
              }));
              
              // Rediriger vers la page de retour si elle existe
              if (proposal.return_url) {
                window.location.href = proposal.return_url;
                return; // Arrêter l'exécution ici car on redirige
              }
            }
            
            // Supprimer les données de paiement du sessionStorage
            sessionStorage.removeItem('payment_proposal');
          } catch (error) {
            logger.error('Erreur lors de la mise à jour du statut de paiement:', error);
          }
        }
        
        setTransferAmount('');
        setSelectedUser(null);
        setTransferMessage('');
        fetchData(); // Rafraîchir les données
        setActiveTab('history'); // Rediriger vers l'onglet Historique
      } else {
        notify(response.data.message || 'Erreur lors du transfert', 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors du transfert:', error);
      notify(
        error.response?.data?.message || 'Erreur lors du transfert',
        'error'
      );
    } finally {
      setIsTransferring(false);
    }
  };

  // Fonction pour obtenir l'icône selon le type d'utilisateur
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'favori':
        return <StarIcon className="text-yellow-500" />;
      case 'offre_recue':
        return <PersonIcon className="text-green-500" />;
      case 'offre_envoyee':
        return <SwapHorizIcon className="text-blue-500" />;
      case 'jobi_echange':
        return <AccountBalanceIcon className="text-orange-500" />;
      default:
        return <PersonIcon className="text-gray-500" />;
    }
  };

  // Fonction pour obtenir le libellé selon le type d'utilisateur
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'favori':
        return 'Favoris';
      case 'offre_recue':
        return 'Offres reçues';
      case 'offre_envoyee':
        return 'Offres envoyées';
      case 'jobi_echange':
        return 'Échanges de Jobi';
      case 'no_result':
        return 'Résultats de recherche';
      default:
        return type;
    }
  };

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollLeft, scrollWidth, clientWidth } = event.currentTarget;
    // Afficher la flèche si l'utilisateur n'est pas complètement à droite
    setShowScrollArrow(scrollLeft + clientWidth < scrollWidth);
    
    // Masquer la flèche dès que l'utilisateur commence à faire défiler
    if (scrollLeft > 0) {
      setShowScrollArrow(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF7A35]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <p className="text-red-600">{DOMPurify.sanitize(error)}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 px-2 md:px-0">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <PageTitle variant="h1">
          Mes Jobi
        </PageTitle>
        <div className="text-sm text-gray-500 mb-6">
          Dernière mise à jour: {new Date().toLocaleString()}
        </div>
      </div>

      {/* Onglets */}
      <div className="relative">
        <div className="flex border-b border-gray-200 mb-6 overflow-x-auto whitespace-nowrap bg-white shadow-md rounded-lg p-2" onScroll={handleScroll}>
          <button
            className={`py-3 px-6 font-medium relative ${activeTab === 'account' ? 'text-[#FF6B2C]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('account')}
          >
            <div className="flex items-center gap-2">
              <ReceiptLongIcon className="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall mr-1" />
              <span>Compte</span>
            </div>
            {activeTab === 'account' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF6B2C]"></div>
            )}
          </button>

          <button
            className={`py-3 px-6 font-medium relative ${activeTab === 'history' ? 'text-[#FF6B2C]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('history')}
          >
            <div className="flex items-center gap-2">
              <HistoryIcon className="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall mr-1" />
              <span>Historique</span>
            </div>
            {activeTab === 'history' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF6B2C]"></div>
            )}
          </button>

          <button
            className={`py-3 px-6 font-medium relative ${activeTab === 'transfer' ? 'text-[#FF6B2C]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('transfer')}
          >
            <div className="flex items-center gap-2">
              <SwapHorizIcon className="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall mr-1" />
              <span>Transfert</span>
            </div>
            {activeTab === 'transfer' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF6B2C]"></div>
            )}
          </button>

          <button
            className={`py-3 px-6 font-medium relative ${activeTab === 'credit' ? 'text-[#FF6B2C]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('credit')}
          >
            <div className="flex items-center gap-2">
              <ShoppingCartIcon className="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall mr-1" />
              <span>Convertir en bon d'achat</span>
            </div>
            {activeTab === 'credit' && (
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF6B2C]"></div>
            )}
          </button>
        </div>

        {/* Flèche de défilement */}
        {showScrollArrow && (
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#FF6B2C] rounded-full shadow-md p-1 cursor-pointer">
            <svg
              onClick={() => {
                const scrollContainer = document.querySelector('.overflow-x-auto');
                if (scrollContainer) {
                  scrollContainer.scrollBy({ left: 100, behavior: 'smooth' });
                }
              }}
              className="h-6 w-6 text-white animate-pulse"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5-5 5M6 7l5 5-5 5" />
            </svg>
          </div>
        )}
      </div>

      {/* Contenu de l'onglet */}
      {activeTab === 'account' && (
      <motion.section
          className="space-y-6"
        >
          {/* Carte principale du solde */}
          <div className="bg-white rounded-lg shadow-md border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] p-6 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-64 h-64 -translate-y-24 translate-x-20 rounded-full bg-white/10 blur-xl"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 translate-y-10 -translate-x-10 rounded-full bg-white/10 blur-lg"></div>
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 relative z-10">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-white/20 rounded-full shadow-lg backdrop-blur-sm">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-8 w-8 text-white">
                      <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                      <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                      <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                      <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white">Solde actuel</h3>
                    <div className="text-sm text-white/80">
                      Dernière mise à jour : {new Date().toLocaleString()}
                    </div>
                  </div>
                </div>
                <div className="text-4xl font-bold text-white flex items-baseline">
                  {solde} <span className="text-2xl ml-1 opacity-90">Jobi</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-white">
              <div className="bg-white rounded-xl p-5 shadow-md border border-gray-50 transition-all duration-300 hover:shadow-lg hover:border-green-100 flex flex-col items-center">
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-full p-4 mb-3 flex items-center justify-center shadow-sm">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-8 w-8 text-green-600">
                    <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                    <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                  </svg>
                </div>
                <div className="text-center">
                  <div className="text-gray-600 text-sm mb-2 font-medium">Reçus ce mois</div>
                  <div className="text-3xl font-bold text-green-600 flex justify-center">
                    <span className="mr-1">+</span>{monthlyStats?.totalIn ? monthlyStats.totalIn.toFixed(1) : '0.0'}
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-5 shadow-md border border-gray-50 transition-all duration-300 hover:shadow-lg hover:border-red-100 flex flex-col items-center">
                <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-full p-4 mb-3 flex items-center justify-center shadow-sm">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-8 w-8 text-red-600">
                    <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                    <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                  </svg>
                </div>
                <div className="text-center">
                  <div className="text-gray-600 text-sm mb-2 font-medium">Dépensés ce mois</div>
                  <div className="text-3xl font-bold text-red-600 flex justify-center">
                    <span className="mr-1">-</span>{monthlyStats?.totalOut ? monthlyStats.totalOut.toFixed(1) : '0.0'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Activité récente */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-6">
              <h4 className="text-lg font-medium text-gray-800">Activité récente</h4>
              <div className="text-sm text-gray-500">
                {monthlyStats?.transactionCount || 0} transactions ce mois
              </div>
            </div>
            
            {historique && historique.length > 0 ? (
              <div className="space-y-4">
                {historique.slice(0, 3).map((transaction, index) => (
                  <div key={index} className="flex justify-between items-center p-4 rounded-lg bg-gray-50">
                    <div>
                      <div className="font-medium text-gray-800">{transaction.titre}</div>
                      <div className="text-sm text-gray-500">{new Date(transaction.date_creation).toLocaleDateString()}</div>
                    </div>
                    <div className={`font-medium ${transaction.montant >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {transaction.montant >= 0 ? '+' : ''}{transaction.montant} Jobi
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                Aucune activité récente
              </div>
            )}
            
          <button
              onClick={() => {
                setActiveTab('history');
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
              className="mt-6 w-full py-2.5 px-4 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-2 font-medium"
          >
              <HistoryIcon fontSize="small" />
              <span>Voir l'historique complet</span>
          </button>
        </div>

          {/* Utilisation des Jobi */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">Utilisation des Jobi</h2>
            <p className="text-gray-600 mb-4">
              Les Jobi constituent notre système d'échange, conçu pour faciliter les échanges de services et l'accès à des avantages exclusifs sur notre plateforme. 
              Gagnez des Jobi en participant activement à la communauté : en laissant des avis, en publiant des missions, ou en répondant à des questions.
              Vous pouvez également convertir vos Jobi en bons d'achat chez de futures enseignes partenaires (magasins de bricolage, décoration et jardinage).
            </p>
            <p className="text-gray-600 mb-6">
              Que vous soyez un prestataire de services ou un client, les Jobi vous offrent une flexibilité unique pour optimiser votre expérience sur JobPartiel et accéder à des réductions chez nos futurs partenaires !
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 flex items-start gap-3">
                <StarIcon className="text-[#FF6B2C] mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">Services Premium</h3>
                  <p className="text-sm text-gray-600">Accédez à des fonctionnalités exclusives et des boosts d'annonces.</p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 flex items-start gap-3">
                <DiscountIcon className="text-[#FF7A35] mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">Bons d'achat partenaires</h3>
                  <p className="text-sm text-gray-600">Convertissez vos Jobi en bons d'achat chez nos futures enseignes partenaires (magasins de bricolage, décoration et jardinage).</p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 flex items-start gap-3">
                <TrophyIcon className="text-[#FF965E] mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">Programme de récompenses</h3>
                  <p className="text-sm text-gray-600">Gagnez des Jobi en participant activement à la communauté.</p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 flex items-start gap-3">
                <ShoppingCartIcon className="text-[#FF7A35] mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">Échange de services</h3>
                  <p className="text-sm text-gray-600">Utilisez vos Jobi pour échanger des services entre membres.</p>
                </div>
              </div>
            </div>
          </div>
      </motion.section>
      )}

      {activeTab === 'history' && (
      <motion.section
          className="bg-white rounded-lg shadow-sm border border-gray-100"
        >
          <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] p-6">
            <div className="flex justify-between items-center text-white">
              <h2 className="text-xl font-semibold">Historique des transactions</h2>
              <span className="text-sm bg-white/20 px-3 py-1 rounded-full">
                {historique.length} transaction{historique.length > 1 ? 's' : ''}
              </span>
            </div>
          </div>
          
          {/* Légende pour nouvelles transactions */}
          {newTransactionsCount > 0 && (
            <div className="mx-6 mt-6 bg-[#FFF8F3] p-3 rounded-xl border border-[#FFE4BA] flex items-center">
              <div className="w-2 h-2 rounded-full bg-[#FF6B2C] mr-2"></div>
              <span className="text-sm text-gray-700">
                {newTransactionsCount} nouvelle{newTransactionsCount > 1 ? 's' : ''} transaction{newTransactionsCount > 1 ? 's' : ''} chargée{newTransactionsCount > 1 ? 's' : ''}
          </span>
        </div>
          )}
          
          <div className="p-6 space-y-4 max-h-[60vh] overflow-y-auto transactions-container">
          {historique.map((transaction, index) => (
            <motion.div
              key={transaction.id}
              id={`transaction-${index}`}
              ref={index === historique.length - 3 ? lastTransactionElementRef : null}
              initial={index >= historique.length - newTransactionsCount ? { opacity: 0, y: 10 } : { opacity: 1 }}
              animate={{ 
                opacity: 1, 
                y: 0,
                backgroundColor: index >= historique.length - newTransactionsCount ? ['#FFF8F3', '#ffffff'] : '#ffffff'
              }}
              transition={{ 
                duration: 0.5,
                backgroundColor: { duration: 2 }
              }}
              className={`p-4 rounded-xl hover:shadow-md transition-all duration-200 ${
                index >= historique.length - newTransactionsCount 
                  ? 'bg-[#FFF8F3] border border-[#FFE4BA]' 
                  : 'bg-white border border-gray-100'
              }`}
            >
              <div className="flex justify-between items-start">
                <div className="flex-grow">
                  <h3 className="font-medium text-gray-800">
                    {DOMPurify.sanitize(transaction.titre)}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {DOMPurify.sanitize(transaction.description)}
                  </p>
                  {transaction.message && (
                    <div className="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-100">
                      <p className="text-sm italic text-gray-700">
                        "{DOMPurify.sanitize(transaction.message)}"
                      </p>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-2 flex items-center">
                    <HistoryIcon sx={{ fontSize: 14 }} className="mr-1" />
                    {new Date(transaction.date_creation).toLocaleString()}
                  </p>
                </div>
                <div className={`flex items-center gap-2 ${
                  transaction.montant >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-6 w-6">
                    <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                    <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                  </svg>
                  <span className="font-semibold text-lg">
                    {transaction.montant >= 0 ? '+' : ''}{transaction.montant}
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
            
            {/* Point de référence en fin de liste pour détecter quand charger plus */}
            <div className="h-10 flex items-center justify-center mt-2">
              {isLoadingMore && (
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#FF6B2C]"></div>
              )}
              {!hasMore && historique.length > 0 && (
                <p className="text-sm text-gray-500">Fin de l'historique</p>
              )}
            </div>
            
            {/* Élément de secours pour détecter la fin même si les transactions ne remplissent pas l'écran */}
            <div ref={transactionsEndRef} className="h-1" />
          </div>
        </motion.section>
      )}

      {activeTab === 'transfer' && (
        <motion.section
          className="space-y-6"
        >
          {/* En-tête avec solde */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
            <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] p-6">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-white/20 rounded-full">
                    <MonetizationOnIcon className="text-white" />
                  </div>
            <div>
                    <h3 className="text-xl font-semibold text-white">Solde disponible</h3>
                    <div className="text-sm text-white/80">
                      Dernière mise à jour : {new Date().toLocaleString()}
                    </div>
                  </div>
                </div>
                <div className="text-4xl font-bold text-white">
                  {solde} <span className="text-2xl">Jobi</span>
                </div>
              </div>
            </div>
          </div>

          {/* Formulaire de transfert */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
            <div className="p-6">
              <form onSubmit={handleTransfer} className="space-y-6">
                {/* Recherche de destinataire */}
                {!selectedUser && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Rechercher un destinataire
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={searchInputValue}
                        onChange={(e) => handleSearchInputChange(e.target.value)}
                        className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent bg-white"
                        placeholder="@usertag, nom, prénom, montant ou date (JJ/MM/AAAA)"
                      />
                      <SearchIcon className="absolute left-3 top-3.5 text-gray-400" />
                      
                      <div className="absolute right-3 top-3 flex items-center gap-2">
                        {searchInputValue && (
                          <button
                            type="button"
                            onClick={clearSearch}
                            className="text-gray-400 hover:text-gray-600 focus:outline-none"
                            title="Effacer la recherche"
                          >
                            <CloseIcon fontSize="small" />
                          </button>
                        )}
                        {isSearching && (
                          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#FF6B2C]"></div>
                        )}
                      </div>
                    </div>

                    {/* Liste des résultats de recherche */}
                    {searchResults.length > 0 && (
                      <div className="absolute z-10 w-full max-w-lg mt-1 bg-white rounded-xl border border-gray-200 shadow-lg max-h-96 overflow-auto">
                        {Object.entries(
                          searchResults.reduce((acc, user) => {
                            if (!acc[user.type || 'favori']) {
                              acc[user.type || 'favori'] = [];
                            }
                            acc[user.type || 'favori'].push(user);
                            return acc;
                          }, {} as Record<string, User[]>)
                        ).map(([type, users]) => (
                          <div key={type} className="py-2">
                            <div className="px-4 py-2 bg-[#FFF8F3] flex items-center">
                              {getTypeIcon(type)}
                              <span className="ml-2 font-medium text-gray-700">{getTypeLabel(type)} ({users.length})</span>
                            </div>
                            <ul className="divide-y divide-gray-100">
                              {users.map((user) => (
                                <li
                                  key={user.id}
                                  className="px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                                  onClick={() => handleUserSelect(user)}
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                      <div className="flex-shrink-0">
                                        {user.avatar_url ? (
                                          <img
                                            src={user.avatar_url}
                                            alt={user.usertag}
                                            className="h-10 w-10 rounded-full object-cover"
                                          />
                                        ) : (
                                          <div className="h-10 w-10 rounded-full bg-[#FFF8F3] flex items-center justify-center">
                                            <PersonIcon className="text-[#FF6B2C]" />
                                          </div>
                                        )}
                                      </div>
                                      <div className="ml-3">
                                        <div className="flex-1">
                                          <div className="font-medium text-gray-800">
                                            {user.prenom && user.nom 
                                              ? `${user.prenom} ${user.nom.charAt(0).toUpperCase()}.`
                                              : `@${user.usertag}`}
                                          </div>
                                          <div className="text-sm text-gray-500">
                                            @{user.usertag}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    {user.dernierEchange && (
                                      <div className="text-right">
                                        <div className={`text-sm font-medium ${
                                          user.dernierEchange.type === 'envoi' 
                                            ? 'text-red-500' 
                                            : 'text-green-500'
                                        }`}>
                                          {user.dernierEchange.type === 'envoi' ? '-' : '+'}{user.dernierEchange.montant} Jobi
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          {new Date(user.dernierEchange.date).toLocaleDateString()}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </li>
                              ))}
                            </ul>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Destinataire sélectionné */}
                <div className="bg-[#FFF8F3] rounded-xl p-4 border border-[#FFE4BA]">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Destinataire
                  </label>
                  <div className="flex items-center gap-3">
                    {selectedUser ? (
                      <>
                        <div className="flex-shrink-0">
                          {selectedUser.avatar_url ? (
                            <img
                              src={selectedUser.avatar_url}
                              alt={selectedUser.usertag}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-white flex items-center justify-center">
                              <PersonIcon className="text-[#FF6B2C]" />
                            </div>
                          )}
                        </div>
            <div>
                          <div className="font-medium text-gray-900">
                            {selectedUser.nom && selectedUser.prenom
                              ? `${selectedUser.prenom} ${selectedUser.nom.charAt(0).toUpperCase()}.`
                              : `@${selectedUser.usertag}`}
                          </div>
                          <div className="text-sm text-gray-500">
                            @{selectedUser.usertag}
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedUser(null);
                            clearSearch(); // Relancer la recherche
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          Désélectionner
                        </button>
                      </>
                    ) : (
                      <div className="text-gray-500 flex items-center gap-2">
                        <PersonIcon />
                        <span>Aucun destinataire sélectionné</span>
                      </div>
                    )}
            </div>
          </div>

                {/* Montant du transfert */}
            <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Montant à transférer
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={transferAmount}
                      onChange={(e) => setTransferAmount(e.target.value)}
                      className="w-full pr-16 pl-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent"
                      placeholder="Montant"
                      min="1"
                      max={solde}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <span className="text-gray-500 font-medium">Jobi</span>
            </div>
          </div>
                  <div className="flex justify-between mt-2 text-sm">
                    <span className="text-gray-500">Min: 1 Jobi</span>
                    <span className="text-gray-500">Max: {solde} Jobi</span>
            </div>
          </div>

                {/* Message optionnel */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (optionnel)
                  </label>
                  <textarea
                    value={transferMessage}
                    onChange={(e) => setTransferMessage(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:border-transparent"
                    placeholder="Entrez un message (optionnel)"
                  />
                </div>

                {/* Boutons d'action */}
                <div className="flex gap-3 pt-4">
                  <button
                    type="button"
                    onClick={clearSearch}
                    className="flex-1 px-5 py-2.5 text-[#FF6B2C] bg-white border-2 border-[#FF6B2C] rounded-xl hover:bg-[#FFF8F3] transition-colors duration-200 font-medium"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    disabled={isTransferring || !selectedUser || !transferAmount}
                    className={`flex-1 px-5 py-2.5 text-white bg-[#FF6B2C] rounded-xl transition-colors duration-200 font-medium flex items-center justify-center gap-2
                      ${(isTransferring || !selectedUser || !transferAmount) 
                        ? 'opacity-50 cursor-not-allowed' 
                        : 'hover:bg-[#FF7A35]'}`}
                  >
                    {isTransferring ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                        Transfert en cours...
                      </div>
                    ) : (
                      <>
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-5 w-5">
                          <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                          <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                          <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                          <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                        </svg>
                        Échanger en Jobi {transferAmount && `(${transferAmount} Jobi)`}
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </motion.section>
      )}

      {activeTab === 'credit' && (
        <motion.section
          className="space-y-6"
        >
          {/* En-tête */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] p-6">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-white/20 rounded-full">
                    <ShoppingCartIcon className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white">Convertir vos Jobi en bons d'achat</h3>
                    <div className="text-sm text-white/80">
                      Profitez de réductions chez nos futures enseignes partenaires (magasins de bricolage et jardinage)
                    </div>
                  </div>
                </div>
                <div className="text-4xl font-bold text-white">
                  {solde} <span className="text-2xl">Jobi</span>
                </div>
              </div>
            </div>
          </div>

          {/* Options de conversion */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Pack magasins de décoration */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="text-center">
                <div className="bg-[#FFF8F3] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <img src={DEFAULT_AVATAR} alt="Magasins de décoration" className="w-24 h-24 object-contain" />
                </div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">Bon Magasins de décoration</h4>
                <div className="text-3xl font-bold text-[#FF6B2C] mb-4">50 Jobi</div>
                <div className="text-gray-600 mb-6">Bon d'achat de 50€</div>
                <button
                  disabled
                  className="w-full py-2 px-4 bg-gray-100 text-gray-500 rounded-lg cursor-not-allowed"
                >
                  Bientôt disponible
                </button>
              </div>
            </div>

            {/* Pack Magasins de bricolage */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="text-center">
                <div className="bg-[#FFF8F3] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <img src={DEFAULT_AVATAR} alt="Magasins de bricolage" className="w-24 h-24 object-contain" />
                </div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">Bon Magasins de bricolage</h4>
                <div className="text-3xl font-bold text-[#FF6B2C] mb-4">100 Jobi</div>
                <div className="text-gray-600 mb-6">Bon d'achat de 100€</div>
                <button
                  disabled
                  className="w-full py-2 px-4 bg-gray-100 text-gray-500 rounded-lg cursor-not-allowed"
                >
                  Bientôt disponible
                </button>
              </div>
            </div>

            {/* Pack Magasins de jardinage */}
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div className="text-center">
                <div className="bg-[#FFF8F3] rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <img src={DEFAULT_AVATAR} alt="Magasins de jardinage" className="w-24 h-24 object-contain" />
                </div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">Bon Magasins de jardinage</h4>
                <div className="text-3xl font-bold text-[#FF6B2C] mb-4">75 Jobi</div>
                <div className="text-gray-600 mb-6">Bon d'achat de 75€</div>
                <button
                  disabled
                  className="w-full py-2 px-4 bg-gray-100 text-gray-500 rounded-lg cursor-not-allowed"
                >
                  Bientôt disponible
                </button>
              </div>
            </div>
          </div>

          {/* Information supplémentaire */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div className="flex items-start gap-4 text-gray-600">
              <div className="bg-[#FFF8F3] rounded-full p-3">
                <InfoIcon className="text-[#FF6B2C]" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Comment ça marche ?</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <CheckCircleIcon className="text-[#FF6B2C] text-sm" />
                    <span>Choisissez le bon d'achat qui vous intéresse</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircleIcon className="text-[#FF6B2C] text-sm" />
                    <span>Convertissez vos Jobi en bon d'achat</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircleIcon className="text-[#FF6B2C] text-sm" />
                    <span>Recevez votre code par email sous 24h</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircleIcon className="text-[#FF6B2C] text-sm" />
                    <span>Utilisez votre bon d'achat en magasin ou en ligne</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </motion.section>
      )}
    </div>
  );
}