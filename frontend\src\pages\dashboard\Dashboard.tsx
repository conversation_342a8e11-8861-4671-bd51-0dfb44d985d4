import { useState, useEffect, useContext } from 'react';
import DOMPurify from 'dompurify';
import { useNavigate } from 'react-router-dom';
import { useJobiBalance } from '../../hooks/useJobiBalance';
import { useStatistics } from '../../hooks/useStatistics';
import { useReviews } from '../../hooks/useReviews';
import { CoinIcon } from './icons';
import { Bar<PERSON>hart, CheckCircle, PlusCircle, Wrench, ClipboardList, X, Briefcase, Users, Clock, Star, ArrowRight, Calendar, Tag, Euro, HelpCircle } from 'lucide-react';
import { getCookie, setCookie } from '../../utils/cookieUtils';
import { UserContext } from './DashboardLayout';
import { styled } from '@mui/material/styles';
import { Typography } from '@mui/material';
import RecentNotifications from './components/RecentNotifications';
import { Tooltip } from '@mui/material';
import { motion } from 'framer-motion';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const Dashboard = () => {
  const userContext = useContext(UserContext);
  const [userType, setUserType] = useState<'jobbeur' | 'non-jobbeur'>('jobbeur');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [firstName, setFirstName] = useState('');
  const [showWelcomeBlock, setShowWelcomeBlock] = useState(true);
  const navigate = useNavigate();
  const { balance, loading } = useJobiBalance();
  const { statistics, myMissions, availableMissions, loading: statsLoading, goToMatchingMissions, goToMyMissions } = useStatistics();
  const { stats: reviewStats } = useReviews({ userId: userContext?.userData?.id || '' });

  useEffect(() => {
    if (userContext?.userData?.profil?.data?.prenom) {
      setFirstName(userContext.userData.profil.data.prenom);
    }
  }, [userContext]);

  useEffect(() => {
    // Vérifier si le cookie existe
    const hideWelcomeBlock = getCookie('hideWelcomeBlock');
    if (hideWelcomeBlock === 'true') {
      setShowWelcomeBlock(false);
    }
  }, []);

  const handleHideWelcomeBlock = () => {
    // Définir le cookie pour 180 jours
    setCookie('hideWelcomeBlock', 'true', 180 * 24 * 60 * 60);
    setShowWelcomeBlock(false);
    // Remonter en haut de la fenêtre
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderPrice = (mission: any) => {
    if (!mission.budget_defini) {
      return (
        <div className="flex items-center text-gray-600">
          <Tag className="h-4 w-4 mr-1 text-gray-400" />
          <span className="text-sm">Prix à définir</span>
        </div>
      );
    }

    const price = mission.budget;

    switch (mission.payment_method) {
      case 'jobi_only':
        return (
          <div className="flex items-center text-[#FF7A35]">
            <CoinIcon className="h-4 w-4 mr-1" />
            <span className="text-sm font-medium">{price} Jobi</span>
          </div>
        );
      case 'direct_only':
        return (
          <div className="flex items-center text-green-600">
            <Euro className="h-4 w-4 mr-1" />
            <span className="text-sm font-medium">{price}€</span>
          </div>
        );
      case 'both':
        return (
          <div className="flex items-center space-x-2">
            <div className="flex items-center text-[#FF7A35]">
              <CoinIcon className="h-4 w-4 mr-1" />
              <span className="text-sm font-medium">{price} Jobi</span>
            </div>
            <span className="text-gray-400">ou</span>
            <div className="flex items-center text-green-600">
              <Euro className="h-4 w-4 mr-1" />
              <span className="text-sm font-medium">{price}€</span>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const renderActionButton = (mission: any) => {
    if (mission.has_user_proposal) {
      return (
        <button
          onClick={() => navigate(`/dashboard/missions/${mission.id}?openOffer=true`)}
          className="bg-[#FF7A35] text-white px-4 py-2 rounded-lg hover:bg-[#FF6B2C] transition-colors flex items-center justify-between w-full"
        >
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Offre déjà envoyée</span>
          </div>
          <span className="text-sm opacity-80 hover:opacity-100 ml-4">Voir les détails →</span>
        </button>
      );
    }

    return (
      <button
        onClick={() => navigate(`/dashboard/missions/${mission.id}`)}
        className="bg-[#FF7A35] text-white px-4 py-2 rounded-lg hover:bg-[#FF6B2C] transition-colors flex items-center justify-center w-full"
      >
        Postuler maintenant
      </button>
    );
  };

  return (
    <div className="space-y-6 px-4 mt-4 sm:px-6 md:px-0 pb-6 sm:pb-8">
      {isLoading || statsLoading ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF7A35]"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-lg">
          <p className="text-red-600">{DOMPurify.sanitize(error)}</p>
          {error === 'Veuillez compléter votre profil avant de poursuivre sur le site.' && (
            <button onClick={() => window.location.href = '/dashboard/profil'} className="bg-orange-500 text-white font-bold py-2 px-4 rounded hover:bg-orange-600 transition duration-300 mt-4">
              Accéder à votre profil en un clic ici
            </button>
          )}
        </div>
      ) : (
        <>
          {showWelcomeBlock && (
            <div className="relative">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 30 }}
                transition={{ duration: 0.5 }}
                className="bg-white p-6 rounded-2xl shadow-lg border"
                style={{ position: 'relative' }}
              >
                {/* Illustration vectorielle en haut */}
                <div className="flex justify-center mb-4">
                  <svg width="90" height="60" viewBox="0 0 90 60" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <ellipse cx="45" cy="50" rx="40" ry="8" fill="#FFE4BA" />
                    <circle cx="25" cy="30" r="14" fill="#FF965E" />
                    <circle cx="65" cy="24" r="10" fill="#FF7A35" />
                    <circle cx="50" cy="16" r="6" fill="#FF6B2C" />
                  </svg>
                </div>
                {/* Croix flottante hors du bloc pour ne jamais être masquée */}
                <button
                  onClick={handleHideWelcomeBlock}
                  className="absolute -top-4 -right-4 z-50 pointer-events-auto"
                  title="Cacher ce bloc"
                  aria-label="Ne plus voir ce bloc"
                >
                  <span
                    className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#FF7A35] shadow-xl hover:bg-[#FF6B2C] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF965E] pointer-events-auto z-50"
                  >
                    <X className="h-7 w-7 text-white transition-transform duration-200 group-hover:rotate-90" />
                  </span>
                </button>
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-4 gap-4">
                  <h2 className="text-xl sm:text-2xl lg:text-3xl font-extrabold text-[#FF7A35] pr-2 sm:pr-6 lg:pr-8 flex flex-col items-center sm:flex-row sm:items-start flex-wrap gap-1 sm:gap-2">
                    {firstName ? DOMPurify.sanitize(`Bienvenue ${firstName} !`) : DOMPurify.sanitize('Bienvenue sur JobPartiel !')}
                  </h2>
                </div>
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                  <p className="text-gray-700 mb-4 text-lg leading-relaxed">
                    <strong>JobPartiel</strong> est une plateforme française dédiée à la mise en relation entre prestataires de services (jobbeurs) et particuliers ou professionnels. Avec plus de 200 catégories de services couvrant tous les domaines du quotidien et de la vie professionnelle, JobPartiel révolutionne l'accès aux services de proximité grâce à sa double approche : paiement classique en euros ou échange de services via notre système de troc innovant basé sur les Jobi.
                  </p>
                  <p className="text-gray-700 mb-4 text-lg leading-relaxed">
                    En tant que client, vous accédez à un vaste réseau de professionnels qualifiés et vérifiés, prêts à répondre à vos besoins spécifiques. Notre plateforme vous permet de comparer les profils, consulter les évaluations authentiques et choisir le prestataire idéal selon vos critères. Que vous recherchiez une intervention ponctuelle ou régulière, JobPartiel vous connecte aux meilleures compétences locales dans un environnement sécurisé et transparent.
                  </p>
                  <p className="text-gray-700 mb-4 text-lg leading-relaxed">
                    En tant que jobbeur, vous bénéficiez d'une vitrine professionnelle pour valoriser vos compétences, d'un système de mise en relation intelligent et d'une flexibilité totale dans la gestion de vos missions. Notre écosystème favorise les échanges locaux, l'économie collaborative et la valorisation des savoir-faire, tout en vous offrant des outils professionnels pour développer votre activité.
                  </p>
                </motion.div>
                <div className="my-6 h-1 w-full bg-gradient-to-r from-[#FF965E]/30 via-[#FF7A35]/10 to-[#FFE4BA]/0 rounded-full" />
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
                  <h2 className="text-xl font-bold mt-4 text-[#FF6B2C] flex items-center gap-2">
                    <CoinIcon className="h-6 w-6 text-[#FF7A35]" />
                    Le système Jobi : notre système de troc/échange
                  </h2>
                  <p className="text-gray-700 mt-2 text-base leading-relaxed">
                    Les Jobi constituent notre système d'échange et de troc, conçu pour faciliter les échanges de services entre utilisateurs. Contrairement à une monnaie classique, les Jobi valorisent l'échange direct de compétences et permettent de créer un écosystème d'entraide sans transaction financière. Gagnez des Jobi en participant activement à la communauté (avis, missions accomplies, parrainage) et échangez-les contre des services variés comme du jardinage, du bricolage, de l'aide au déménagement, du soutien informatique, des cours particuliers, des gardes d'animaux, etc.
                  </p>
                  <p className="text-gray-700 mt-2 text-base leading-relaxed">
                    Ce système présente de nombreux avantages : économies financières, valorisation de vos savoir-faire, création de liens sociaux, et participation à une économie plus collaborative et durable. Avec les Jobi, chaque compétence a de la valeur et peut être échangée contre d'autres services utiles, créant ainsi un cercle vertueux d'entraide locale.
                  </p>
                </motion.div>
                <div className="my-6 h-1 w-full bg-gradient-to-r from-[#FF965E]/30 via-[#FF7A35]/10 to-[#FFE4BA]/0 rounded-full" />
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
                  <h2 className="text-xl font-bold mt-4 text-[#FF6B2C] flex items-center gap-2">
                    <Users className="h-6 w-6 text-[#FF7A35]" />
                    Qui sont les jobbeurs ?
                  </h2>
                  <p className="text-gray-700 mt-2 text-base leading-relaxed">
                    Les jobbeurs sont des professionnels ou amateurs proposant leurs services sur notre plateforme. Ils créent un profil détaillé, mettent en avant leurs compétences et réalisations, et peuvent faire vérifier leur identité ou leur entreprise pour gagner en crédibilité.
                  </p>
                  <div className="text-gray-700 mt-2">
                    <strong>Exemples de jobbeurs :</strong>
                    <ul className="list-none mt-2 space-y-2">
                      <li className="flex items-center gap-2"><Wrench className="h-5 w-5 text-[#FF6B2C]" />Un jardinier proposant entretien de jardins, tonte de pelouse et taille de haies</li>
                      <li className="flex items-center gap-2"><ClipboardList className="h-5 w-5 text-[#FF7A35]" />Un bricoleur qualifié pour montage de meubles, petites réparations ou installations</li>
                      <li className="flex items-center gap-2"><Users className="h-5 w-5 text-[#FF965E]" />Un pet sitter gardant des animaux pendant l'absence de leurs propriétaires</li>
                      <li className="flex items-center gap-2"><Star className="h-5 w-5 text-[#FFE4BA]" />Un aide-ménager offrant des services de nettoyage et d'entretien domestique</li>
                      <li className="flex items-center gap-2"><Tag className="h-5 w-5 text-[#FF6B2C]" />Un électricien certifié réalisant des installations et dépannages électriques</li>
                      <li className="flex items-center gap-2"><Briefcase className="h-5 w-5 text-[#FF7A35]" />Un graphiste réalisant logos, flyers et supports de communication</li>
                    </ul>
                  </div>
                </motion.div>
                <div className="my-6 h-1 w-full bg-gradient-to-r from-[#FF965E]/30 via-[#FF7A35]/10 to-[#FFE4BA]/0 rounded-full" />
                <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5 }}>
                  <h2 className="text-xl font-bold mt-4 text-[#FF6B2C] flex items-center gap-2">
                    <Users className="h-6 w-6 text-[#FF965E]" />
                    Qui sont les clients ?
                  </h2>
                  <p className="text-gray-700 mt-2 text-base leading-relaxed">
                    Les clients (ou non-jobbeurs) sont des utilisateurs recherchant des services. Ils publient des missions détaillées, proposent un échange en Jobi ou en direct (en euros), consultent les profils des jobbeurs et choisissent le prestataire idéal en fonction des compétences, évaluations et modalités d'échange.
                  </p>
                  <div className="text-gray-700 mt-2">
                    <strong>Exemples de clients :</strong>
                    <ul className="list-none mt-2 space-y-2">
                      <li className="flex items-center gap-2"><Wrench className="h-5 w-5 text-[#FF6B2C]" />Un particulier cherchant quelqu'un pour entretenir son jardin régulièrement</li>
                      <li className="flex items-center gap-2"><Users className="h-5 w-5 text-[#FF7A35]" />Une famille ayant besoin de garde pour leurs animaux pendant les vacances</li>
                      <li className="flex items-center gap-2"><ClipboardList className="h-5 w-5 text-[#FF965E]" />Un propriétaire nécessitant des petites réparations dans son logement</li>
                      <li className="flex items-center gap-2"><Star className="h-5 w-5 text-[#FFE4BA]" />Une personne âgée recherchant de l'aide pour ses courses hebdomadaires</li>
                      <li className="flex items-center gap-2"><Briefcase className="h-5 w-5 text-[#FF6B2C]" />Un couple souhaitant faire monter des meubles après un déménagement</li>
                      <li className="flex items-center gap-2"><Tag className="h-5 w-5 text-[#FF7A35]" />Une entreprise recherchant un rédacteur web pour son site internet</li>
                      <li className="flex items-center gap-2"><CoinIcon className="h-5 w-5 text-[#FF965E]" />Un étudiant souhaitant des cours particuliers pour préparer ses examens</li>
                    </ul>
                  </div>
                </motion.div>
                <div className="my-6 h-1 w-full bg-gradient-to-r from-[#FF965E]/30 via-[#FF7A35]/10 to-[#FFE4BA]/0 rounded-full" />
                {/* Section Services et Commencer : inchangée, mais on peut ajouter un effet hover plus marqué */}
                <div className="space-y-8 mt-8">
                  {/* Section Services */}
                  <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-[#FF6B2C] hover:shadow-lg transition-all duration-300">
                    <h2 className="text-xl font-bold text-[#FF6B2C] flex items-center mb-4">
                      <Wrench className="mr-2 size-6" />
                      Besoin d'un service ?
                    </h2>
                    <ul className="grid gap-3 text-gray-700">
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <Wrench className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Publiez une mission détaillée montage de meubles, tonte de pelouse, garde d'animaux ... Parmi plus de 200 catégories de services</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <ClipboardList className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Consultez les candidatures et choisissez le jobbeur qui vous convient</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <Clock className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Définissez des créneaux horaires spécifiques ou optez pour une flexibilité</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <CoinIcon className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Payez en euros ou échangez des services via notre système de troc avec les Jobi</span>
                      </li>
                    </ul>
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => navigate('/dashboard/missions/poster-une-mission')}
                        className="bg-[#FF6B2C] hover:bg-[#FF7A35] text-white font-semibold py-2 px-6 rounded-md shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
                      >
                        <PlusCircle className="mr-2 h-5 w-5" />
                        Poster une mission
                      </button>
                    </div>
                  </div>

                  {/* Section Commencer */}
                  <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-[#FFE4BA] hover:shadow-lg transition-all duration-300">
                    <h2 className="text-xl font-bold text-[#FF6B2C] flex items-center mb-4">
                      <PlusCircle className="mr-2 size-6" />
                      Commencez dès maintenant :
                    </h2>
                    <ul className="grid gap-3 text-gray-700">
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <PlusCircle className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Complétez votre profil professionnel</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <PlusCircle className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Ajoutez des photos de vos réalisations dans votre galerie</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <PlusCircle className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Définissez votre zone d'intervention et vos tarifs</span>
                      </li>
                      <li className="flex items-start p-2 rounded-md hover:bg-[#FFF8F3]">
                        <PlusCircle className="text-[#FF6B2C] mr-3 size-6 mt-0.5 flex-shrink-0" />
                        <span>Postulez aux missions correspondant à vos compétences</span>
                      </li>
                    </ul>
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => navigate('/dashboard/profil')}
                        className="bg-[#FF6B2C] hover:bg-[#FF7A35] text-white font-semibold py-2 px-6 rounded-md shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
                      >
                        <PlusCircle className="mr-2 h-5 w-5" />
                        Compléter mon profil
                      </button>
                    </div>
                  </div>
                </div>

                <div className="bg-[#FFF8F3] rounded-lg p-4 mt-6 border-l-4 border-[#FF6B2C]">
                  <h3 className="font-semibold text-[#FF6B2C] mb-2">Conseil de sécurité</h3>
                  <p className="text-gray-700 text-sm">Pour une expérience optimale, nous recommandons de toujours effectuer les échanges via la plateforme, d'utiliser notre système de messagerie sécurisé, et de laisser des évaluations après chaque mission. En cas de question ou problème, notre support est disponible pour vous accompagner.</p>
                </div>

                {/* Bouton de fermeture en bas */}
                <div className="flex justify-end mt-6">
                  <button
                    onClick={handleHideWelcomeBlock}
                    className="bg-[#FF7A35] text-white px-4 py-2 rounded-md shadow-md hover:shadow-lg hover:bg-[#FF6B2C] transition-all duration-300 flex items-center justify-center"
                    title="Cacher ce bloc"
                    aria-label="Ne plus voir ce bloc"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Fermer ce message
                  </button>
                </div>
              </motion.div>
            </div>
          )}

          {/* En-tête */}
          <div className="flex justify-between items-center">
            <PageTitle variant="h1">
              Aperçu Général
            </PageTitle>
            <div className="text-sm text-gray-500">
              Dernière mise à jour: {new Date().toLocaleString()}
            </div>
          </div>

          {/* Statistiques */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <div
              className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Mes Missions</h3>
                  <Tooltip
                    title="Nombre de missions en cours d'exécution actuellement"
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur Mes Missions"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <div className="cursor-pointer" onClick={goToMyMissions}>
                  <Briefcase className="h-5 w-5 sm:h-6 sm:w-6 text-[#FF7A35]" />
                </div>
              </div>
              <div className="cursor-pointer" onClick={goToMyMissions}>
                <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-[#FF7A35]">
                  {myMissions.filter(mission => mission.statut === 'en_cours').length}
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mt-1">Missions en cours</p>
              </div>
            </div>

            <div
              className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">À Postuler</h3>
                  <Tooltip
                    title="Nombre de missions disponibles auxquelles vous n'avez pas encore postulé ou qui vous intéressent potentiellement"
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur les missions à postuler"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={() => navigate('/dashboard/missions', { state: { activeTab: 2 } })}
                >
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" />
                </div>
              </div>
              <div className="cursor-pointer" onClick={goToMatchingMissions}>
                <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-blue-500">{statistics.disponibles}</p>
                <p className="text-xs sm:text-sm text-gray-500 mt-1">Missions disponibles</p>
              </div>
            </div>

            <div
              className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Offres en Attente</h3>
                  <Tooltip
                    title="Nombre d'offres reçues et envoyées en attente de réponse"
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur les offres en attente"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={() => navigate('/dashboard/missions/offres')}
                >
                  <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-orange-500" />
                </div>
              </div>
              <div
                className="cursor-pointer"
                onClick={() => navigate('/dashboard/missions/offres')}
              >
                <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-orange-500">
                  {statistics.enAttente || 0}
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mt-1">
                  Offre{(statistics.enAttente || 0) > 1 ? 's' : ''} en attente
                </p>
              </div>
            </div>

            <div
              className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Taux de Réponse</h3>
                  <Tooltip
                    title="Pourcentage de vos candidatures qui ont été acceptées"
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur le taux de réponse"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={() => navigate('/dashboard/missions/offres')}
                >
                  <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-green-500" />
                </div>
              </div>
              <div
                className="cursor-pointer"
                onClick={() => navigate('/dashboard/missions/offres')}
              >
                <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-green-500">
                  {statistics.total > 0 ? Math.round((statistics.acceptees / statistics.total) * 100) : 0}%
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mt-1">Missions acceptées</p>
              </div>
            </div>

          </div>

          {/* Missions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Mes Missions */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <Briefcase className="h-6 w-6 text-[#FF7A35] mr-2" />
                  Mes Missions
                  <Tooltip
                    title="Retrouvez ici toutes vos missions en cours d'exécution. Vous pouvez suivre leur progression et interagir avec vos clients."
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur Mes Missions"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </h2>
                <button
                  onClick={goToMyMissions}
                  className="text-[#FF7A35] hover:text-[#FF6B2C] font-medium transition-colors flex items-center group"
                >
                  Tout voir
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
              <div className="space-y-4">
                {myMissions
                  .filter(mission => mission.statut === 'en_cours')
                  .slice(0, 2)
                  .map((mission) => (
                  <div key={mission.id} className="group relative bg-white rounded-xl p-4 sm:p-6 hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#FF7A35]/20">
                    <div className="flex flex-col space-y-3">
                      <div className="flex flex-col w-full">
                        <h4 className="font-semibold text-gray-900 text-lg sm:text-xl leading-tight">
                          {mission.titre}
                          <div className="w-12 h-1 bg-[#FF7A35] mt-2 rounded-full"></div>
                        </h4>

                        <div className="flex justify-between items-center mt-2">
                          <div className="flex items-center text-gray-500 text-sm">
                            <Calendar className="h-4 w-4 mr-1.5 text-gray-500" />
                            <span>
                              {new Date(mission.date_creation.split('+')[0]).toLocaleDateString('fr-FR', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </span>
                          </div>
                          <div className="flex-shrink-0">
                            {renderPrice(mission)}
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-600 text-sm sm:text-base leading-relaxed line-clamp-2 sm:line-clamp-3">
                        {mission.description ? mission.description.replace(/<[^>]*>/g, '') : ''}
                      </p>

                      <div className="flex flex-wrap gap-2 items-center">
                        {mission.evaluation && (
                          <div className="flex items-center bg-gray-50 px-2 py-1 sm:px-3 sm:py-1.5 rounded-lg">
                            <Star className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-yellow-500 mr-1" />
                            <span className="text-xs sm:text-sm text-gray-700">{mission.evaluation}</span>
                          </div>
                        )}
                        {mission.statut && (
                          <div className="flex items-center bg-gray-50 px-2 py-1 sm:px-3 sm:py-1.5 rounded-lg">
                            <Tag className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#FF7A35] mr-1" />
                            <span className="text-xs sm:text-sm text-gray-600">
                              Statut: <span className="font-medium capitalize">{mission.statut.replace(/_/g, ' ')}</span>
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end mt-2 sm:mt-3">
                        <div className="w-full">
                          <button
                            onClick={() => navigate(`/dashboard/missions/${mission.id}`)}
                            className="bg-[#FF7A35] text-white px-3 py-2 text-sm sm:text-base rounded-lg hover:bg-[#FF6B2C] transition-colors flex items-center justify-center w-full"
                          >
                            Voir les détails
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {myMissions.filter(mission => mission.statut === 'en_cours').length === 0 && (
                  <div className="text-center py-8">
                    <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">Aucune mission en cours</p>
                    <button
                      onClick={() => navigate('/dashboard/missions/poster-une-mission')}
                      className="bg-[#FF7A35] text-white font-medium py-2 px-4 rounded-lg hover:bg-[#FF6B2C] transition-colors flex items-center mx-auto"
                    >
                      <PlusCircle className="mr-2 h-5 w-5" />
                      Ajouter une mission
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Missions à Postuler */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <Users className="h-6 w-6 text-[#FF7A35] mr-2" />
                  Missions à Postuler
                  <Tooltip
                    title="Découvrez les missions qui correspondent à votre profil et vos compétences. Vous pouvez postuler directement depuis cette section."
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur les Missions à Postuler"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </h2>
                <button
                  onClick={goToMatchingMissions}
                  className="text-[#FF7A35] hover:text-[#FF6B2C] font-medium transition-colors flex items-center group"
                >
                  Tout voir
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
              <div className="space-y-4">
                {availableMissions.slice(0, 2).map((mission) => (
                  <div key={mission.id} className="group relative bg-white rounded-xl p-4 sm:p-6 hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#FF7A35]/20">
                    <div className="flex flex-col space-y-3">
                      <div className="flex flex-col w-full">
                        <h4 className="font-semibold text-gray-900 text-lg sm:text-xl leading-tight">
                          {mission.titre}
                          <div className="w-12 h-1 bg-[#FF7A35] mt-2 rounded-full"></div>
                        </h4>

                        <div className="flex justify-between items-center mt-2">
                          <div className="flex items-center text-gray-500 text-sm">
                            <Calendar className="h-4 w-4 mr-1.5 text-gray-500" />
                            <span>
                              {new Date(mission.date_creation.split('+')[0]).toLocaleDateString('fr-FR', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </span>
                          </div>
                          <div className="flex-shrink-0">
                            {renderPrice(mission)}
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-600 text-sm sm:text-base leading-relaxed line-clamp-2 sm:line-clamp-3">
                        {mission.description ? mission.description.replace(/<[^>]*>/g, '') : ''}
                      </p>

                      <div className="flex flex-wrap gap-2 items-center">
                        {mission.evaluation && (
                          <div className="flex items-center bg-gray-50 px-2 py-1 sm:px-3 sm:py-1.5 rounded-lg">
                            <Star className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-yellow-500 mr-1" />
                            <span className="text-xs sm:text-sm text-gray-700">{mission.evaluation}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end mt-2 sm:mt-3">
                        <div className="w-full">
                          {renderActionButton(mission)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {availableMissions.length === 0 && (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">Aucune mission disponible pour le moment</p>
                    <button
                      onClick={() => navigate('/dashboard/profil')}
                      className="bg-[#FF7A35] text-white font-medium py-2 px-4 rounded-lg hover:bg-[#FF6B2C] transition-colors flex items-center mx-auto"
                    >
                      <PlusCircle className="mr-2 h-5 w-5" />
                      Ajouter des services à mon profil
                    </button>
                    <p className="text-sm text-gray-500 mt-2">ou</p>
                    <button
                      onClick={goToMatchingMissions}
                      className="mt-2 text-[#FF7A35] hover:text-[#FF6B2C] font-medium flex items-center mx-auto"
                    >
                      Voir toutes les missions disponibles
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Notifications récentes */}
          <RecentNotifications />

          {/* Section Performance Globale */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mt-6">
            <div
              className="bg-white rounded-lg p-4 sm:p-6 shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
              onClick={() => navigate('/dashboard/jobi')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-800">Solde Jobi</h3>
                  <Tooltip
                    title="Système de récompense et de fidélité. Gagnez des Jobis en effectuant des missions, échangez-les contre des services sur la plateforme ou profitez de réductions exclusives chez nos partenaires."
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur le Solde Jobi"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <CoinIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#FF6B2C]" />
              </div>

              {!loading ? (
                <div className="flex items-center">
                  <span className="text-2xl sm:text-3xl font-bold text-[#FF6B2C]">{balance}</span>
                  <span className="text-sm text-gray-600 ml-2">Jobi disponibles</span>
                </div>
              ) : (
                <div className="animate-pulse h-7 bg-gray-200 rounded w-24 sm:w-32"></div>
              )}
            </div>

            <div className="bg-white rounded-lg p-4 sm:p-6 shadow-md hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-800">Performance Globale</h3>
                  <Tooltip
                    title="Visualisez vos performances sur la plateforme : votre note moyenne attribuée par les clients et votre taux d'acceptation des missions."
                    placement="top"
                    enterTouchDelay={0}
                    leaveTouchDelay={5000}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Plus d'informations sur la Performance Globale"
                    >
                      <HelpCircle className="h-4 w-4 text-gray-400" />
                    </button>
                  </Tooltip>
                </div>
                <BarChart className="h-5 w-5 sm:h-6 sm:w-6 text-[#FF6B2C]" />
              </div>
              <div className="flex items-start justify-start space-x-8">
                <div
                  className="text-left cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => navigate('/dashboard/avis')}
                >
                  <p className="text-sm text-gray-500">Note Moyenne</p>
                  <p className="text-3xl font-bold text-[#FF7A35]">
                    {reviewStats?.rating ? reviewStats.rating.toFixed(1) : '0.0'} / 5
                  </p>
                </div>
                <div
                  className="text-left cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => navigate('/dashboard/missions/offres')}
                >
                  <p className="text-sm text-gray-500">Taux d'Acceptation</p>
                  <p className="text-3xl font-bold text-[#FF7A35]">
                    {statistics.total > 0 ? Math.round((statistics.acceptees / statistics.total) * 100) : 0}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;

