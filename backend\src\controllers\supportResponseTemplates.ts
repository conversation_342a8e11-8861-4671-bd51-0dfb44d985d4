import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

/**
 * Fonction simplifiée pour sanitiser le HTML
 * Cette fonction retire les balises script et les attributs dangereux
 * @param html Le HTML à sanitiser
 * @returns Le HTML sanitisé
 */
function sanitizeHtml(html: string): string {
  if (!html) return '';
  
  try {
    // Enregistrer la taille de l'entrée
    const originalLength = html.length;
    
    // Supprimer les balises script et leur contenu
    let sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    
    // Supprimer les attributs dangereux (onclick, onerror, etc.)
    sanitized = sanitized.replace(/\s(on\w+)="[^"]*"/gi, '');
    sanitized = sanitized.replace(/\s(on\w+)='[^']*'/gi, '');
    
    // Supprimer les balises iframe
    sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
    
    // Supprimer javascript: dans les liens
    sanitized = sanitized.replace(/javascript:/gi, 'removed:');
    
    // Supprimer data: dans les attributs src (sauf pour les images)
    sanitized = sanitized.replace(/(<[^>]+src=["'])data:(?!image\/)/gi, '$1removed:');
    
    logger.info('HTML sanitisé', {
      originalLength,
      sanitizedLength: sanitized.length,
      difference: originalLength - sanitized.length
    });
    
    return sanitized;
  } catch (error) {
    logger.error('Erreur lors de la sanitisation HTML:', error);
    // En cas d'erreur, retourner une version encore plus stricte (texte brut)
    return html.replace(/<[^>]*>/g, '');
  }
}

const CACHE_PREFIX = 'response_template:';
const CACHE_TTL = 3600; // 1 heure

/**
 * Nettoie le cache Redis des templates de réponse
 * Cette fonction supprime toutes les clés liées aux templates
 */
export const clearTemplatesCache = async (req: Request, res: Response) => {
  try {
    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';

    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      logger.warn('Tentative non autorisée - userId manquant', { userId, role });
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      logger.warn('Tentative non autorisée - rôle insuffisant', { role, isStaff });
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    logger.info('Nettoyage du cache des templates demandé par', { userId, role });

    // Récupérer toutes les clés qui commencent par le préfixe
    const keys = await redis.keys(`${CACHE_PREFIX}*`);
    
    logger.info(`Trouvé ${keys.length} clés de cache de templates à supprimer`);
    
    let result = 0;
    if (keys.length > 0) {
      // Supprimer toutes les clés trouvées
      result = await redis.del(...keys);
      logger.info(`Cache des templates nettoyé: ${result} clés supprimées`);
    } else {
      logger.info('Aucune clé de cache de templates trouvée');
    }

    res.status(200).json({ 
      success: true, 
      message: `Cache des templates nettoyé: ${result} clés supprimées`,
      keysDeleted: result
    });
    return;
  } catch (error) {
    logger.error('Erreur lors du nettoyage du cache Redis:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Erreur lors du nettoyage du cache', 
      message: error instanceof Error ? error.message : 'Erreur inconnue' 
    });
    return;
  }
};

export const createTemplate = async (req: Request, res: Response) => {
  try {
    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';


    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      logger.warn('Tentative non autorisée - userId manquant', { userId, role });
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      logger.warn('Tentative non autorisée - rôle insuffisant', { role, isStaff });
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    const { title, content, category } = req.body;

    if (!title || !content) {
      res.status(400).json({ error: 'Titre et contenu requis' });
      return;
    }

    // Sanitize le contenu HTML
    const sanitizedContent = sanitizeHtml(content);

    const { data: template, error } = await supabase
      .from('support_response_templates')
      .insert({
        title,
        content: sanitizedContent,
        category,
        created_by: userId
      })
      .select()
      .single();

    if (error) {
      res.status(500).json({ error: 'Erreur lors de la création du modèle', details: error.message });
      return;
    }

    if (!template) {
      res.status(500).json({ error: 'Erreur inattendue: template non créé' });
      return;
    }

    // Invalider le cache des modèles
    await redis.del(`${CACHE_PREFIX}list`);
    logger.info('Cache des templates invalidé');

    res.status(201).json(template);
    return;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
    res.status(500).json({ error: 'Erreur serveur', message: errorMessage });
    return;
  }
};

export const updateTemplate = async (req: Request, res: Response) => {
  try {
    const { templateId } = req.params;
    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';

    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    const { title, content, category } = req.body;

    // Vérifier l'existence du modèle
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('support_response_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (fetchError || !existingTemplate) {
      res.status(404).json({ error: 'Modèle non trouvé' });
      return;
    }

    const updates: any = {};
    if (title) updates.title = title;
    if (content) updates.content = sanitizeHtml(content);
    if (category) updates.category = category;
    updates.updated_at = new Date().toISOString();

    const { data: template, error } = await supabase
      .from('support_response_templates')
      .update(updates)
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      res.status(500).json({ error: 'Erreur lors de la mise à jour du modèle', details: error.message });
      return;
    }

    // Invalider les caches
    await Promise.all([
      redis.del(`${CACHE_PREFIX}${templateId}`),
      redis.del(`${CACHE_PREFIX}list`)
    ]);

    res.json(template);
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
    return;
  }
};

export const deleteTemplate = async (req: Request, res: Response) => {
  try {
    const { templateId } = req.params;
    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';


    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    // Vérifier l'existence du modèle
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('support_response_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (fetchError || !existingTemplate) {
      res.status(404).json({ error: 'Modèle non trouvé' });
      return;
    }

    logger.info('Suppression du template', { templateId });

    const { error } = await supabase
      .from('support_response_templates')
      .delete()
      .eq('id', templateId);

    if (error) {
      logger.error('Erreur lors de la suppression du modèle:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du modèle' });
      return;
    }

    logger.info('Template supprimé avec succès', { templateId });

    // Invalider les caches
    await Promise.all([
      redis.del(`${CACHE_PREFIX}${templateId}`),
      redis.del(`${CACHE_PREFIX}list`)
    ]);

    res.json({ success: true });
    return;
  } catch (error) {
    logger.error('Erreur lors de la suppression du modèle:', error);
    res.status(500).json({ error: 'Erreur serveur' });
    return;
  }
};

export const getTemplate = async (req: Request, res: Response) => {
  try {
    // Ajouter des logs pour déboguer
    logger.info('Requête GET template/:id reçue', { 
      templateId: req.params.templateId,
      user: req.user,
      userId: req.user?.id || req.user?.userId,
      role: req.user?.role
    });

    const { templateId } = req.params;
    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';
    

    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      logger.warn('Tentative non autorisée de consultation - userId manquant', { userId, role });
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      logger.warn('Tentative non autorisée de consultation - rôle insuffisant', { role, isStaff });
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    // Vérifier le cache
    const cachedTemplate = await redis.get(`${CACHE_PREFIX}${templateId}`);
    if (cachedTemplate) {
      logger.info('Modèle récupéré depuis le cache', { templateId });
      res.json(JSON.parse(cachedTemplate));
      return;
    }

    const { data: template, error } = await supabase
      .from('support_response_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération du modèle:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération du modèle' });
      return;
    }

    if (!template) {
      logger.warn('Modèle non trouvé', { templateId });
      res.status(404).json({ error: 'Modèle non trouvé' });
      return;
    }

    // Mettre en cache
    await redis.set(
      `${CACHE_PREFIX}${templateId}`,
      JSON.stringify(template),
      'EX',
      CACHE_TTL
    );

    logger.info('Modèle récupéré avec succès', { templateId });
    res.json(template);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération du modèle:', error);
    res.status(500).json({ error: 'Erreur serveur' });
    return;
  }
};

export const getTemplates = async (req: Request, res: Response) => {
  try {
    logger.info('Requête GET /templates reçue', {
      query: req.query,
      user: req.user,
      userId: req.user?.id || req.user?.userId,
      role: req.user?.role,
      ip: req.ip,
      method: req.method,
      path: req.path
    });

    // Récupération et vérification des informations de l'utilisateur
    const userId = req.user?.id || req.user?.userId;
    const role = req.user?.role;
    const isStaff = role === 'jobpadm' || role === 'jobmodo';
    
    logger.info('Vérification des permissions utilisateur pour liste templates', {
      userId,
      role,
      isStaff
    });

    // Vérifier que l'utilisateur est authentifié et a les droits
    if (!userId) {
      logger.warn('Tentative non autorisée de liste templates - userId manquant', { userId, role });
      res.status(403).json({ error: 'Non autorisé - identifiant utilisateur manquant' });
      return;
    }

    if (!isStaff) {
      logger.warn('Tentative non autorisée de liste templates - rôle insuffisant', { role, isStaff });
      res.status(403).json({ error: 'Non autorisé - accès réservé aux administrateurs et modérateurs' });
      return;
    }

    const { category, search } = req.query;

    // Vérifier le cache pour la liste complète
    const cacheKey = category 
      ? `${CACHE_PREFIX}list:${category}` 
      : `${CACHE_PREFIX}list`;
    
    const cachedTemplates = await redis.get(cacheKey);
    if (cachedTemplates) {
      logger.info('Templates récupérés depuis le cache', { cacheKey });
      res.json(JSON.parse(cachedTemplates));
      return;
    }

    let query = supabase
      .from('support_response_templates')
      .select('*')
      .order('title', { ascending: true });

    if (category) {
      query = query.eq('category', String(category));
    }

    // Ajouter une recherche si demandée
    if (search) {
      query = query.ilike('title', `%${String(search)}%`);
    }

    logger.info('Exécution requête SQL pour templates', { 
      category: category ? String(category) : 'toutes',
      search: search ? String(search) : 'aucune' 
    });

    const { data: templates, error } = await query;

    if (error) {
      logger.error('Erreur lors de la récupération des modèles:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des modèles', details: error.message });
      return;
    }

    // S'assurer que templates n'est pas null
    const safeTemplates = templates || [];

    // Mettre en cache
    await redis.set(
      cacheKey,
      JSON.stringify(templates),
      'EX',
      CACHE_TTL
    );

    res.json(safeTemplates);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des modèles:', error);
    res.status(500).json({ error: 'Erreur serveur' });
    return;
  }
}; 