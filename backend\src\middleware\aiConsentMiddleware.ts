import { Request, Response, NextFunction } from 'express';
import { hasUserConsented } from '../controllers/aiConsent';
import logger from '../utils/logger';

/**
 * Middleware pour vérifier si l'utilisateur a donné son consentement pour l'utilisation de l'IA
 * Si l'utilisateur n'a pas donné son consentement, renvoie une erreur 403
 */
export const checkAiConsent = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user || !req.user.userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        requiresConsent: false,
        toastType: 'error'
      });
      return;
    }

    const userId = req.user.userId;
    const hasConsent = await hasUserConsented(userId);

    if (!hasConsent) {
      res.status(403).json({
        success: false,
        message: 'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu',
        requiresConsent: true,
        toastType: 'error'
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('Erreur lors de la vérification du consentement IA:', error);
    res.status(500).json({
      success: false,
      message: 'Une erreur est survenue lors de la vérification du consentement',
      requiresConsent: false,
      toastType: 'error'
    });
  }
};

export default {
  checkAiConsent
};
