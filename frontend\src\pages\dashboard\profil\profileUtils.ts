import { GalleryFolder } from '../gallery/types';
import { CompanyInfo } from '@/types/company';
import { UserService } from '../services/types';

// Types
export interface ProfilData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  photo_url: string;
  banner_url?: string;
  bannerPosition?: 'top' | 'center' | 'bottom';
  bannerPositionOffset?: number;
  location: string;
  bio: string;
  hourly_rate: number;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  profil_complet: boolean;
  rating: number;
  total_reviews: number;
  completion_rate: number;
  telephone: string;
  telephone_prive: boolean;
  ville: string;
  code_postal: string;
  pays: string;
  responseTime: {
    average: number;
    lastWeek: number;
  };
  connectionsCount: number;
  companyInfo: CompanyInfo;
  verificationStatus: {
    identity: {
      isVerified: boolean;
      verificationDate: string;
    };
    company: {
      isVerified: boolean;
      verificationDate: string;
    };
    documents: {
      isVerified: boolean;
      verificationDate: string;
    };
  };
  galleryFolders: GalleryFolder[];
  intervention_zone?: {
    center: [number, number];
    radius: number;
    adresse?: string;
    france_entiere?: boolean;
  };
  mode_vacance?: boolean;
  slug: string;
  is_online?: boolean;
  services?: UserService[];
  isPremium: boolean;
  options?: {
    franceEntiere?: boolean;
    bannerPosition?: 'top' | 'center' | 'bottom';
  };
  profil_visible?: boolean;
  profil_id?: string;
  slogan?: string;
}

export interface ProfilCompletionField {
  name: string;
  isComplete: boolean;
  importance: 'high' | 'medium' | 'low';
}