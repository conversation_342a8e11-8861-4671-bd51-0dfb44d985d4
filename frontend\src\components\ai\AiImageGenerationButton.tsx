import React, { useState } from 'react';
import { Sparkles } from 'lucide-react';
import { Button, Tooltip, IconButton } from '@mui/material';
import AiImageGenerationModal from './AiImageGenerationModal';
import { ImageGenerationPurpose } from '../../hooks/useAiImageGeneration';
import { useAiCredits } from '../../hooks/useAiCredits';
import useAiConsent from '../../hooks/useAiConsent';

interface AiImageGenerationButtonProps {
  purpose: ImageGenerationPurpose;
  onImageGenerated: (imageUrl: string, imageBase64: string) => void;
  defaultPrompt?: string;
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  size?: 'small' | 'medium' | 'large';
  iconOnly?: boolean;
  disabled?: boolean;
  className?: string;
  tooltip?: string;
  width?: number;
  height?: number;
}

/**
 * Bouton pour ouvrir le modal de génération d'images IA
 */
const AiImageGenerationButton: React.FC<AiImageGenerationButtonProps> = ({
  purpose,
  onImageGenerated,
  defaultPrompt = '',
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  iconOnly = false,
  disabled = false,
  className = '',
  tooltip = 'Générer une image avec l\'IA',
  width,
  height
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { credits } = useAiCredits();
  const { hasConsent } = useAiConsent();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleImageGenerated = (imageUrl: string, imageBase64: string) => {
    onImageGenerated(imageUrl, imageBase64);
    setIsModalOpen(false);
  };

  const buttonContent = (
    <>
      <Sparkles className={`h-4 w-4 ${!iconOnly ? 'mr-2' : ''}`} />
      {!iconOnly && 'Générer avec IA'}
    </>
  );

  // Déterminer si le bouton doit être visuellement désactivé (grisé)
  // Note: On ne désactive pas complètement le bouton pour permettre d'ouvrir la modale de consentement

  // Déterminer le tooltip à afficher
  const finalTooltip = !hasConsent
    ? 'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu'
    : tooltip;

  // Gérer le clic sur le bouton
  const handleClick = () => {
    if (!hasConsent) {
      // Ouvrir la modale de consentement IA
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
    } else {
      // Ouvrir la modale de génération d'image
      handleOpenModal();
    }
  };

  return (
    <>
      {iconOnly ? (
        <Tooltip title={finalTooltip}>
          <span>
            <IconButton
              color={color}
              size={size}
              onClick={handleClick}
              disabled={disabled}
              className={className}
              sx={{
                backgroundColor: '#FF6B2C',
                color: '#fff',
                '&:hover': { backgroundColor: '#FF7A35' },
                '&.Mui-disabled': { backgroundColor: '#FFE4BA', color: '#FF965E' },
              }}
            >
              <Sparkles className="h-4 w-4" />
            </IconButton>
          </span>
        </Tooltip>
      ) : (
        <Button
          variant={variant}
          color={undefined}
          size={size}
          onClick={handleClick}
          disabled={disabled}
          className={className}
          startIcon={<Sparkles className="h-4 w-4" />}
          sx={{
            backgroundColor: '#FF6B2C',
            color: '#fff',
            '&:hover': { backgroundColor: '#FF7A35' },
            '&.Mui-disabled': { backgroundColor: '#FFE4BA', color: '#FF965E' },
          }}
        >
          {!hasConsent ? 'Accepter les CGU' : 'Générer avec IA'}
        </Button>
      )}

      <AiImageGenerationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onImageGenerated={handleImageGenerated}
        purpose={purpose}
        defaultPrompt={defaultPrompt}
        width={width}
        height={height}
      />
    </>
  );
};

export default AiImageGenerationButton;
