import React, { useRef, useState, useEffect } from 'react';
import { Stage, Layer, Rect, Image, Text, Group } from 'react-konva';
import { KonvaEventObject } from 'konva/lib/Node';
import {
  CardElement,
  CardTemplateData,
  TextElement,
  ImageElement,
  ShapeElement,
  QRCodeElement,
  DrawingElement
} from '../../types/cardEditor';
import TextElementComponent from './elements/TextElement';
import ImageElementComponent from './elements/ImageElement';
import ShapeElementComponent from './elements/ShapeElement';
import QRCodeElementComponent from './elements/QRCodeElement';
import DrawingElementComponent from './elements/DrawingElement';
import DrawingMode from './DrawingMode';
import Grid from './Grid';
import Guides from './Guides';
import SnapGrid from './SnapGrid';
import ShadowRectangle from './ShadowRectangle';
import { Box, Paper, IconButton, Tooltip } from '@mui/material';
import PanToolAltIcon from '@mui/icons-material/PanToolAlt';
import AlignHorizontalCenterIcon from '@mui/icons-material/AlignHorizontalCenter';
import AlignVerticalCenterIcon from '@mui/icons-material/AlignVerticalCenter';
import DeleteIcon from '@mui/icons-material/Delete';
import useUserProfile, { UserProfileData } from '../../hooks/useUserProfileCarteVisite';
import { useAuth } from '../../contexts/AuthContext';

// Définir l'interface pour les données utilisateur
interface CardEditorCanvasProps {
  templateData: CardTemplateData;
  onElementSelect: (element: CardElement | null) => void;
  onElementUpdate: (element: CardElement) => void;
  onElementAdd: (element: CardElement) => void;
  selectedElement: CardElement | null;
  isEditable?: boolean;
  zoom?: number;
  showGrid?: boolean;
  showGuides?: boolean;
  snapEnabled?: boolean;
  isDrawingMode?: boolean;
  setIsDrawingMode?: (active: boolean) => void;
  blockSnapSize?: number;
  onElementContextMenu?: (event: React.MouseEvent, el: CardElement) => void;
  onMobileElementSelect?: (element: CardElement) => void;
  onElementDelete: (elementId: string) => void;
  userProfile?: UserProfileData; // Prop optionnelle pour les données utilisateur
  showProfessionalFooter?: boolean; // Option pour afficher/masquer le footer professionnel
  userProfileId?: string; // ID de l'utilisateur pour récupérer son profil
  userProfileSlug?: string; // Slug de l'utilisateur pour récupérer son profil
  autoLoadProfile?: boolean; // Charger automatiquement le profil de l'utilisateur connecté
  showWatermark?: boolean; // Option pour afficher/masquer le filigrane standard
  // AMÉLIORATION: Props pour les outils de dessin
  drawingTool?: 'brush' | 'eraser' | 'polygon';
  strokeColor?: string;
  strokeWidth?: number;
}

const CardEditorCanvas = React.forwardRef<any, CardEditorCanvasProps>(({
  templateData,
  onElementSelect,
  onElementUpdate,
  onElementAdd,
  selectedElement,
  isEditable = true,
  zoom = 1,
  showGrid = true,
  showGuides = true,
  snapEnabled = true,
  isDrawingMode = false,
  setIsDrawingMode,
  blockSnapSize = 10,
  onElementContextMenu,
  onMobileElementSelect,
  onElementDelete,
  userProfile: userProfileProp, // Données de profil fournies en prop
  showProfessionalFooter = true, // Valeur par défaut
  userProfileId,
  userProfileSlug,
  autoLoadProfile = true,
  showWatermark = true, // Valeur par défaut: afficher le filigrane
  // AMÉLIORATION: Props pour les outils de dessin
  drawingTool: drawingToolProp = 'brush',
  strokeColor: strokeColorProp = '#FF6B2C',
  strokeWidth: strokeWidthProp = 5,
}, ref) => {
  const { user } = useAuth();

  // Utiliser le hook useUserProfile pour récupérer les données du profil
  // Optimisation : ne charger le profil que si nécessaire et pas en mode prévisualisation
  const shouldLoadProfile = autoLoadProfile && !userProfileProp && (!!userProfileId || !!userProfileSlug || !!user) && isEditable;

  const {
    profileData: fetchedProfileData,
    isLoading: isProfileLoading
  } = useUserProfile({
    userId: userProfileId,
    slug: userProfileSlug,
    enabled: shouldLoadProfile,
    includePrivateData: true
  });

  // Utiliser les données fournies ou celles récupérées
  const userProfile = userProfileProp || fetchedProfileData;

  const stageRef = useRef<any>(null);
  const [stageSize, setStageSize] = useState({
    width: templateData.width,
    height: templateData.height
  });
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null);

  // AMÉLIORATION: Utiliser les props pour les outils de dessin au lieu des états locaux
  const drawingTool = drawingToolProp;
  const strokeColor = strokeColorProp;
  const strokeWidth = strokeWidthProp;

  // États pour le snap
  const [draggingElement, setDraggingElement] = useState<CardElement | null>(null);

  // États pour le mode main
  const [isPanMode, setIsPanMode] = useState(false);
  const [hasScroll, setHasScroll] = useState(false);
  const panState = useRef({ isPanning: false, lastX: 0, lastY: 0, scrollLeft: 0, scrollTop: 0 });

  // États pour le Ctrl
  const [isCtrlPressed, setIsCtrlPressed] = useState(false);

  // Vérifier que le template est valide et contient des éléments
  useEffect(() => {
    if (!templateData || !templateData.elements || !Array.isArray(templateData.elements)) {
      console.error('Template invalide:', templateData);
    }
  }, [templateData]);

  // Gérer le centrage horizontal de l'élément sélectionné
  const handleCenterHorizontal = () => {
    if (selectedElement) {
      // Calculer le centre horizontal du stage
      const stageCenterX = templateData.width / 2;

      let newX = selectedElement.x;

      // Si c'est un cercle, une ellipse, une étoile, un polygone, un anneau, un arc ou un secteur, le point x est déjà le centre
      if (selectedElement.type === 'shape' && (
        (selectedElement as ShapeElement).properties.shape === 'circle' ||
        (selectedElement as ShapeElement).properties.shape === 'ellipse' ||
        (selectedElement as ShapeElement).properties.shape === 'star' ||
        (selectedElement as ShapeElement).properties.shape === 'polygon' ||
        (selectedElement as ShapeElement).properties.shape === 'ring' ||
        (selectedElement as ShapeElement).properties.shape === 'arc' ||
        (selectedElement as ShapeElement).properties.shape === 'wedge'
      )) {
        newX = stageCenterX;
      } else if (selectedElement.type === 'shape' && ((selectedElement as ShapeElement).properties.shape === 'line' || (selectedElement as ShapeElement).properties.shape === 'arrow')) {
        // Pour les lignes et flèches, calculer la boîte englobante des points
        const points = (selectedElement as ShapeElement).properties.points || [0, 0];
        let minX = Infinity;
        let maxX = -Infinity;

        for (let i = 0; i < points.length; i += 2) {
          // Calculer la position absolue de chaque point
          const currentX = selectedElement.x + points[i];
          minX = Math.min(minX, currentX);
          maxX = Math.max(maxX, currentX);
        }

        const lineCenterX = (minX + maxX) / 2;
        // La nouvelle position x est la position du stageCenter moins le décalage du centre de la ligne par rapport à son point d'origine (element.x)
        newX = stageCenterX - (lineCenterX - selectedElement.x);

      } else if (selectedElement.type === 'shape' && (selectedElement as ShapeElement).properties.shape === 'path') {
        // Pour les paths, utiliser la boîte englobante du nœud Konva
        const pathNode = stageRef.current?.findOne(`#${selectedElement.id}`);
        if (pathNode) {
          const clientRect = pathNode.getClientRect();
          const pathCenterX = clientRect.x + clientRect.width / 2;
           // La nouvelle position x est la position du stageCenter moins le décalage du centre du path par rapport à son point d'origine (element.x)
          newX = stageCenterX - (pathCenterX - selectedElement.x);
        }

      } else {
        // Pour les autres éléments (rectangle, image, texte, qrcode, drawing), soustraire la moitié de la largeur
        const elementWidth = selectedElement.width || 0;
        newX = stageCenterX - elementWidth / 2;
      }

      // Mettre à jour l'élément avec la nouvelle position horizontale
      const updatedElement = {
        ...selectedElement,
        x: newX,
      };
      onElementUpdate(updatedElement);
    }
  };

  // Gérer le centrage vertical de l'élément sélectionné
  const handleCenterVertical = () => {
    if (selectedElement) {
      // Calculer le centre vertical du stage
      const stageCenterY = templateData.height / 2;

      let newY = selectedElement.y;

      // Si c'est un cercle, une ellipse, une étoile, un polygone, un anneau, un arc ou un secteur, le point y est déjà le centre
      if (selectedElement.type === 'shape' && (
        (selectedElement as ShapeElement).properties.shape === 'circle' ||
        (selectedElement as ShapeElement).properties.shape === 'ellipse' ||
        (selectedElement as ShapeElement).properties.shape === 'star' ||
        (selectedElement as ShapeElement).properties.shape === 'polygon' ||
        (selectedElement as ShapeElement).properties.shape === 'ring' ||
        (selectedElement as ShapeElement).properties.shape === 'arc' ||
        (selectedElement as ShapeElement).properties.shape === 'wedge'
      )) {
        newY = stageCenterY;
      } else if (selectedElement.type === 'shape' && ((selectedElement as ShapeElement).properties.shape === 'line' || (selectedElement as ShapeElement).properties.shape === 'arrow')) {
        // Pour les lignes et flèches, calculer la boîte englobante des points
        const points = (selectedElement as ShapeElement).properties.points || [0, 0];
        let minY = Infinity;
        let maxY = -Infinity;

        for (let i = 1; i < points.length; i += 2) {
          // Calculer la position absolue de chaque point
          const currentY = selectedElement.y + points[i];
          minY = Math.min(minY, currentY);
          maxY = Math.max(maxY, currentY);
        }

        const lineCenterY = (minY + maxY) / 2;
         // La nouvelle position y est la position du stageCenter moins le décalage du centre de la ligne par rapport à son point d'origine (element.y)
        newY = stageCenterY - (lineCenterY - selectedElement.y);

      } else if (selectedElement.type === 'shape' && (selectedElement as ShapeElement).properties.shape === 'path') {
         // Pour les paths, utiliser la boîte englobante du nœud Konva
         const pathNode = stageRef.current?.findOne(`#${selectedElement.id}`);
         if (pathNode) {
           const clientRect = pathNode.getClientRect();
           const pathCenterY = clientRect.y + clientRect.height / 2;
            // La nouvelle position y est la position du stageCenter moins le décalage du centre du path par rapport à son point d'origine (element.y)
           newY = stageCenterY - (pathCenterY - selectedElement.y);
         }
      } else {
         // Calculer la nouvelle position y de l'élément pour le centrer
         // Il faut soustraire la moitié de la hauteur de l'élément pour l'aligner par son centre
        const elementHeight = selectedElement.height || 0;
        newY = stageCenterY - elementHeight / 2;
      }

      // Mettre à jour l'élément avec la nouvelle position verticale
      const updatedElement = {
        ...selectedElement,
        y: newY,
      };
      onElementUpdate(updatedElement);
    }
  };

  // Charger l'image de fond si elle existe
  useEffect(() => {
    if (templateData.background_image) {
      const image = new window.Image();
      image.src = templateData.background_image;
      image.onload = () => {
        setBackgroundImage(image);
      };
    } else {
      setBackgroundImage(null);
    }
  }, [templateData.background_image]);

  // Mettre à jour la taille du stage si les dimensions du template changent
  useEffect(() => {
    setStageSize({
      width: templateData.width * zoom,
      height: templateData.height * zoom
    });
  }, [templateData.width, templateData.height, zoom]);

  // Détecter le scroll horizontal ET vertical possible
  useEffect(() => {
    const checkScroll = () => {
      const box = document.getElementById('card-editor-scroll-box');
      if (box) {
        const hasHorizontalScroll = box.scrollWidth > box.clientWidth;
        const hasVerticalScroll = box.scrollHeight > box.clientHeight;
        setHasScroll(hasHorizontalScroll || hasVerticalScroll);
      }
    };
    checkScroll();
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, [stageSize.width, stageSize.height]);

  // Gérer l'état global de la touche Ctrl
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey) setIsCtrlPressed(true);
    };
    const handleKeyUp = (e: KeyboardEvent) => {
      if (!e.ctrlKey) setIsCtrlPressed(false);
    };
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Activer automatiquement le mode pan si le zoom est supérieur à 1.5
  useEffect(() => {
    if (zoom > 1.5) {
      setIsPanMode(true);
    } else if (zoom === 1) {
      setIsPanMode(false);
    }
  }, [zoom]);

  // Activer automatiquement le mode pan si le scroll est nécessaire
  useEffect(() => {
    if (hasScroll && zoom > 1) {
      setIsPanMode(true);
    }
  }, [hasScroll, zoom]);

  // Gérer l'élection d'un élément
  const handleElementSelect = (element: CardElement | null) => {
    onElementSelect(element);
    // Si on est sur mobile et qu'on sélectionne un élément, appeler le callback mobile
    if (element && typeof window !== 'undefined' && window.innerWidth <= 900 && onMobileElementSelect) {
      onMobileElementSelect(element);
    }
  };

  // Gérer le début du déplacement
  const handleElementDragStart = (element: CardElement, _e: KonvaEventObject<DragEvent>) => {
    if (!isEditable) return;

    // Ne rien faire si le snap est désactivé
    if (!snapEnabled) return;

    // Marquer l'élément comme étant en cours de déplacement
    setDraggingElement(element);

    // Mettre à jour l'élément avec l'état de déplacement
    const updatedElement = {
      ...element,
      isDragging: true
    };

    onElementUpdate(updatedElement);
  };

  // Gérer le déplacement en cours
  const handleElementDragMove = (element: CardElement, e: KonvaEventObject<DragEvent>) => {
    if (!isEditable) return;

    // Obtenir la position actuelle
    const currentPos = { x: e.target.x(), y: e.target.y() };

    // Mettre à jour l'élément avec les nouvelles coordonnées
    const updatedElement = {
      ...element,
      x: currentPos.x,
      y: currentPos.y,
      isDragging: true
    };

    onElementUpdate(updatedElement);
  };

  // Gérer la fin du déplacement
  const handleElementDragEnd = (element: CardElement, e: KonvaEventObject<DragEvent> | any) => {
    if (!isEditable) return;

    // Si le snap est activé, aligner l'élément sur la grille
    if (snapEnabled && e.target.x && e.target.y) {
      // Calculer la position de snap
      const snapX = Math.round(e.target.x() / blockSnapSize) * blockSnapSize;
      const snapY = Math.round(e.target.y() / blockSnapSize) * blockSnapSize;

      // Appliquer la position de snap
      if (e.target.position) {
        e.target.position({ x: snapX, y: snapY });
      }
    }

    // Mettre à jour l'élément avec les nouvelles coordonnées et rotation/size si présentes
    const updatedElement = {
      ...element,
      x: e.target.x ? e.target.x() : element.x,
      y: e.target.y ? e.target.y() : element.y,
      rotation: e.target.rotation ? e.target.rotation() : element.rotation,
      width: e.target.width ? e.target.width() : element.width,
      height: e.target.height ? e.target.height() : element.height,
      isDragging: false
    };

    onElementUpdate(updatedElement);

    // Réinitialiser l'élément en cours de déplacement (seulement si le snap est activé)
    if (snapEnabled) {
      setDraggingElement(null);
    }
  };

  // Gérer le clic sur le fond pour désélectionner
  const handleBackgroundClick = (e: any) => {
    // Désélectionner si on clique sur le fond (Stage) ou sur le rectangle de fond
    if (e.target === e.target.getStage() || e.target.name() === 'background-rect') {
      onElementSelect(null);
    }
  };

  // Exporter le stage en tant qu'image
  const exportAsImage = (format: 'png' | 'jpg' = 'png'): string => {
    if (!stageRef.current) return '';

    // Assurez-vous que le filigrane est visible lors de l'exportation
    // Le filigrane est déjà dans une couche séparée, donc il sera inclus dans l'exportation
    return stageRef.current.toDataURL({
      pixelRatio: 2,
      mimeType: format === 'png' ? 'image/png' : 'image/jpeg',
      quality: 0.9
    });
  };

  // Exposer la méthode d'export via la ref
  React.useImperativeHandle(
    ref,
    () => ({
      exportAsImage,
      getStageRef: () => stageRef.current
    })
  );

  // Gérer l'ajout d'un dessin
  const handleDrawingComplete = (drawingElement: DrawingElement) => {
    onElementAdd(drawingElement);
  };

  // Gérer l'ajout d'un polygone
  const handlePolygonComplete = (polygonElement: DrawingElement) => {
    onElementAdd(polygonElement);
  };

  // Déterminer si les événements de souris doivent être désactivés
  const disableMouseEvents = isDrawingMode && !selectedElement;

  // Fonction pour gérer le pan (drag pour scroll)
  const handleStageMouseDown = (e: any) => {
    if (!isPanMode) return;
    panState.current.isPanning = true;
    // Support souris et tactile
    if (e.evt.touches && e.evt.touches.length > 0) {
      panState.current.lastX = e.evt.touches[0].clientX;
      panState.current.lastY = e.evt.touches[0].clientY;
    } else {
      panState.current.lastX = e.evt.clientX;
      panState.current.lastY = e.evt.clientY;
    }
    // Trouver le conteneur scrollable
    const box = document.getElementById('card-editor-scroll-box');
    if (box) {
      panState.current.scrollLeft = box.scrollLeft;
      panState.current.scrollTop = box.scrollTop;
    }
  };

  const handleStageMouseMove = (e: any) => {
    if (!isPanMode || !panState.current.isPanning) return;
    let clientX, clientY;
    if (e.evt.touches && e.evt.touches.length > 0) {
      clientX = e.evt.touches[0].clientX;
      clientY = e.evt.touches[0].clientY;
    } else {
      clientX = e.evt.clientX;
      clientY = e.evt.clientY;
    }
    const dx = clientX - panState.current.lastX;
    const dy = clientY - panState.current.lastY;
    const box = document.getElementById('card-editor-scroll-box');
    if (box) {
      box.scrollLeft = panState.current.scrollLeft - dx;
      box.scrollTop = panState.current.scrollTop - dy;
    }
  };

  const handleStageMouseUp = () => {
    if (!isPanMode) return;
    panState.current.isPanning = false;
  };

  // Handle deleting the selected element
  const handleDeleteElement = () => {
    if (selectedElement) {
      onElementDelete(selectedElement.id);
      onElementSelect(null); // Deselect the element after deletion
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
      }}
    >
      {/* Contrôles de navigation flottants sur le côté droit supérieur */}
      {isEditable && (
        <Box
          sx={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            zIndex: 10,
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            backgroundColor: 'rgba(255, 255, 255, 0.85)',
            borderRadius: '8px',
            p: 1,
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            // Styles responsifs pour mobile
            "@media (max-width: 780px)": {
              position: 'relative', // Changer en relatif sur mobile
              top: 'auto', // Supprimer la position top
              right: 'auto', // Supprimer la position right
              flexDirection: 'row', // Changer la direction en ligne sur mobile
              justifyContent: 'center', // Centrer les boutons
              width: '100%', // Prendre toute la largeur
              borderRadius: '0', // Enlever les coins arrondis
              boxShadow: 'none', // Enlever l'ombre
              p: 0.5, // Réduire le padding
              gap: '4px', // Réduire l'espacement entre les boutons
              backgroundColor: 'rgba(255, 255, 255, 0.95)', // Rendre le fond un peu plus opaque
            },
          }}
        >
          {/* Bouton Mode Main */}
          {/* Masquer le bouton de mode main en mode non éditable */}
          {isEditable && (
            <Tooltip title="Activer le mode déplacement">
              <IconButton
                size="small"
                onClick={() => setIsPanMode(!isPanMode)}
                color={isPanMode ? 'primary' : 'default'}
              >
                <PanToolAltIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {/* Bouton Centrer Horizontalement */}
          {selectedElement && !isDrawingMode && (
            <Tooltip title="Centrer horizontalement">
              <IconButton
                size="small"
                onClick={handleCenterHorizontal}
                sx={{
                  backgroundColor: '#FFF8F3',
                  color: '#FF6B2C',
                  border: '1px solid #FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                    color: '#fff',
                  }
                }}
              >
                <AlignHorizontalCenterIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {/* Bouton Centrer Verticalement */}
          {selectedElement && !isDrawingMode && (
            <Tooltip title="Centrer verticalement">
              <IconButton
                size="small"
                onClick={handleCenterVertical}
                sx={{
                  backgroundColor: '#FFF8F3',
                  color: '#FF6B2C',
                  border: '1px solid #FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                    color: '#fff',
                  }
                }}
              >
                <AlignVerticalCenterIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {/* Add Delete Button */}
          {selectedElement && !isDrawingMode && (
            <Tooltip title="Supprimer l'élément">
              <IconButton
                size="small"
                onClick={handleDeleteElement}
                sx={{
                  backgroundColor: '#FFF8F3',
                  color: '#FF6B2C',
                  border: '1px solid #FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                    color: '#fff',
                  }
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}

      <Box
        id="card-editor-scroll-box"
        sx={{
          width: '100%',
          height: '100%',
          overflow: 'auto',
          position: 'relative',
        }}
      >
        <Paper
          elevation={2}
          sx={{
            width: stageSize.width,
            overflow: 'hidden',
            backgroundColor: '#f5f5f5',
            border: '4px solid #EEE',
            borderRadius: 3,
            boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
            position: 'relative',
            cursor: isPanMode
              ? panState.current.isPanning
                ? 'grabbing'
                : 'grab'
              : isDrawingMode
              ? (drawingTool === 'brush' ? 'crosshair' : 'cell')
              : 'default',
            transition: 'box-shadow 0.3s ease',
            '&:hover': {
              boxShadow: '0 4px 20px rgba(0,0,0,0.12)'
            },
            display: 'flex',
            flexDirection: 'column',
          }}
        >


          <Stage
            ref={stageRef}
            width={stageSize.width}
            height={stageSize.height}
            scale={{ x: zoom, y: zoom }}
            style={{ display: 'block', position: 'relative', flex: 1 }}
            onClick={isPanMode ? undefined : (disableMouseEvents ? undefined : handleBackgroundClick)}
            onMouseDown={(e) => {
              if (isPanMode) {
                handleStageMouseDown(e);
                return;
              }
              // Désélectionner si on clique sur le Stage ou le Rect de fond, uniquement si on n'est PAS en mode dessin
              if (!isDrawingMode && (e.target === e.target.getStage() || e.target.name() === 'background-rect')) {
                onElementSelect(null);
              }
            }}
            onMouseMove={(e) => {
              if (isPanMode) {
                handleStageMouseMove(e);
              }
            }}
            onMouseUp={() => {
              if (isPanMode) {
                handleStageMouseUp();
              }
            }}
            onTouchStart={(e) => {
              if (isPanMode) {
                handleStageMouseDown(e);
              }
            }}
            onTouchMove={(e) => {
              if (isPanMode) {
                handleStageMouseMove(e);
              }
            }}
            onTouchEnd={() => {
              if (isPanMode) {
                handleStageMouseUp();
              }
            }}
          >
          {/* Couche de fond */}
          <Layer>
            {/* Fond du template */}
            <Rect
              x={0}
              y={0}
              width={templateData.width}
              height={templateData.height}
              fill={templateData.background_color || '#ffffff'}
              name="background-rect"
            />

            {/* Image de fond si elle existe */}
            {backgroundImage && (
              <Image
                x={0}
                y={0}
                width={templateData.width}
                height={templateData.height}
                image={backgroundImage}
              />
            )}

            {/* Grille d'alignement */}
            {showGrid && (
              <Grid
                width={templateData.width}
                height={templateData.height}
                blockSnapSize={blockSnapSize}
                stroke="rgba(0, 0, 0, 0.05)"
              />
            )}
          </Layer>

          {/* Couche pour les éléments de dessin (brush, polygone, gomme) */}
          <Layer>
            {/* Éléments du template (tous les drawings, y compris gomme) */}
            {templateData.elements && templateData.elements.filter(element => {
              // Si la propriété visible existe et est false, on ne rend pas l'élément
              if (
                (element.type === 'text' && (element as TextElement).properties.visible === false) ||
                (element.type === 'image' && (element as ImageElement).properties.visible === false) ||
                (element.type === 'shape' && (element as ShapeElement).properties.visible === false) ||
                (element.type === 'drawing' && (element as DrawingElement).properties.visible === false) ||
                (element.type === 'qrcode' && (element as QRCodeElement).properties.visible === false)
              ) {
                return false;
              }
              return true;
            })
            .slice() // copie défensive
            .reverse() // inverser l'ordre pour que le premier soit au-dessus
            .map((element) => {
              const isSelected = selectedElement?.id === element.id;

              switch (element.type) {
                case 'text':
                  return (
                    <TextElementComponent
                      key={element.id}
                      element={element as TextElement}
                      isSelected={isSelected}
                      onSelect={() => handleElementSelect(element)}
                      onDragStart={(e) => handleElementDragStart(element, e)}
                      onDragMove={(e) => handleElementDragMove(element, e)}
                      onDragEnd={(e) => handleElementDragEnd(element, e)}
                      isEditable={isEditable && !isDrawingMode}
                      onContextMenu={onElementContextMenu ? (e) => onElementContextMenu(e.evt, element) : undefined}
                    />
                  );
                case 'image':
                  return (
                    <ImageElementComponent
                      key={element.id}
                      element={element as ImageElement}
                      isSelected={isSelected}
                      onSelect={() => handleElementSelect(element)}
                      onDragStart={(e) => handleElementDragStart(element, e)}
                      onDragMove={(e) => handleElementDragMove(element, e)}
                      onDragEnd={(e) => handleElementDragEnd(element, e)}
                      isEditable={isEditable && !isDrawingMode}
                      onContextMenu={onElementContextMenu ? (e) => onElementContextMenu(e.evt, element) : undefined}
                      isCtrlPressed={isCtrlPressed}
                    />
                  );
                case 'shape':
                  return (
                    <ShapeElementComponent
                      key={element.id}
                      element={element as ShapeElement}
                      isSelected={isSelected}
                      onSelect={() => handleElementSelect(element)}
                      onDragStart={(e) => handleElementDragStart(element, e)}
                      onDragMove={(e) => handleElementDragMove(element, e)}
                      onDragEnd={(e) => handleElementDragEnd(element, e)}
                      isEditable={isEditable && !isDrawingMode}
                      onContextMenu={onElementContextMenu ? (e) => onElementContextMenu(e.evt, element) : undefined}
                    />
                  );
                case 'qrcode':
                  return (
                    <QRCodeElementComponent
                      key={`qrcode-${element.id}`}
                      element={{
                        id: element.id,
                        type: "qrcode",
                        x: element.x,
                        y: element.y,
                        width: element.width || 100,
                        height: element.height || 100,
                        rotation: element.rotation,
                        properties: {
                          data: (element as QRCodeElement).properties.data || `https://jobpartiel.fr`,
                          fill: (element as QRCodeElement).properties.fill || "#000000",
                          background: (element as QRCodeElement).properties.background || "#ffffff",
                          visible: (element as QRCodeElement).properties.visible !== false
                        }
                      }}
                      isSelected={isSelected}
                      onSelect={() => handleElementSelect(element)}
                      onDragStart={(e) => handleElementDragStart(element, e)}
                      onDragMove={(e) => handleElementDragMove(element, e)}
                      onDragEnd={(e) => handleElementDragEnd(element, e)}
                      isEditable={isEditable && !isDrawingMode}
                      onContextMenu={onElementContextMenu ? (e) => onElementContextMenu(e.evt, element) : undefined}
                    />
                  );
                case 'drawing':
                  return (
                    <DrawingElementComponent
                      key={element.id}
                      element={element as DrawingElement}
                      isSelected={isSelected}
                      onSelect={() => handleElementSelect(element)}
                      onDragStart={(e) => handleElementDragStart(element, e)}
                      onDragMove={(e) => handleElementDragMove(element, e)}
                      onDragEnd={(e) => handleElementDragEnd(element, e)}
                      isEditable={isEditable && !isDrawingMode}
                      onContextMenu={onElementContextMenu ? (e) => onElementContextMenu(e.evt, element) : undefined}
                    />
                  );
                default:
                  return null;
              }
            })}

            {/* Mode dessin */}
            {isDrawingMode && (
              <DrawingMode
                isActive={isDrawingMode}
                stageRef={stageRef}
                templateWidth={templateData.width}
                templateHeight={templateData.height}
                onDrawingComplete={handleDrawingComplete}
                tool={drawingTool}
                strokeColor={strokeColor}
                strokeWidth={strokeWidth}
                onPolygonComplete={handlePolygonComplete}
              />
            )}
          </Layer>

          {/* Couche pour les guides et éléments d'interface */}
          <Layer>
            {/* Guides d'alignement */}
            {showGuides && !isDrawingMode && (
              <Guides
                width={templateData.width}
                height={templateData.height}
                selectedElement={selectedElement}
                elements={templateData.elements}
              />
            )}

            {/* Grille de snap */}
            {snapEnabled && !isDrawingMode && (
              <SnapGrid
                width={templateData.width}
                height={templateData.height}
                blockSnapSize={blockSnapSize}
                visible={snapEnabled}
              />
            )}

            {/* Ombre de snap */}
            {snapEnabled && draggingElement && !isDrawingMode && (
              <ShadowRectangle
                element={draggingElement}
                blockSnapSize={blockSnapSize}
                visible={snapEnabled}
              />
            )}
          </Layer>

          {/* Couche pour le filigrane JobPartiel (toujours visible et non modifiable) */}
          <Layer>
            {(showProfessionalFooter && showWatermark) ? (
              (() => {
                // Responsive : stacker si largeur < 350px
                const isMobileFooter = templateData.width < 350;
                return isMobileFooter ? (
                  <Group
                    x={0}
                    y={templateData.height - 100}
                    width={templateData.width}
                    listening={false}
                  >
                    {/* Fond du footer */}
                    <Rect
                      width={templateData.width}
                      height={100}
                      fill="#FFF9F5"
                      stroke="#FFE6D9"
                      strokeWidth={0.5}
                    />
                    {/* Texte au-dessus du QR code, centré */}
                    <Text
                      text="Scannez le QR Code pour"
                      fontSize={11}
                      fontFamily="'Inter', sans-serif"
                      fill="#FF6B2C"
                      x={0}
                      y={7}
                      width={templateData.width}
                      align="center"
                    />
                    {/* QR code centré */}
                    <Group x={templateData.width / 2 - 20} y={30}>
                      {isProfileLoading ? (
                        <Rect width={40} height={40} fill="#FFFFFF" stroke="#FFE6D9" strokeWidth={0.5} />
                      ) : userProfile && (userProfile.slug || userProfile.id) ? (
                        <QRCodeElementComponent
                          key="footer-qr-code"
                          element={{
                            id: "footer-qr-code",
                            type: "qrcode",
                            x: 0,
                            y: -10,
                            width: 40,
                            height: 40,
                            properties: {
                              data: `https://jobpartiel.fr/profil/${userProfile?.slug || userProfile?.id || 'profil'}`,
                              fill: "#000000",
                              background: "#ffffff",
                              visible: true
                            }
                          }}
                          isSelected={false}
                          onSelect={() => {}}
                          onDragStart={() => {}}
                          onDragMove={() => {}}
                          onDragEnd={() => {}}
                          isEditable={false}
                        />
                      ) : (
                        <Rect width={40} height={40} fill="#FFFFFF" stroke="#FFE6D9" strokeWidth={0.5} />
                      )}
                    </Group>
                    {/* Liste des avantages, centrée */}
                    <Text
                      text={
                        `• Voir les avis laissés par mes clients\n` +
                        `• Voir mes réalisations\n` +
                        `• Me contacter`
                      }
                      fontSize={6}
                      fontFamily="'Inter', sans-serif"
                      fill="#666666"
                      x={0}
                      y={63}
                      width={templateData.width}
                      align="center"
                      lineHeight={1.2}
                    />
                    {/* URL du profil, centrée en bas, jamais superposée */}
                    <Text
                      text={userProfile && (userProfile.slug || userProfile.id)
                        ? `jobpartiel.fr/profil/${userProfile?.slug || userProfile?.id || ''}`
                        : 'jobpartiel.fr/'}
                      fontSize={6}
                      fontFamily="'Inter', sans-serif"
                      fontStyle="italic"
                      fill="#FF6B2C"
                      x={8}
                      y={88}
                      width={templateData.width - 16}
                      align="center"
                      opacity={userProfile ? 1 : 0.7}
                      ellipsis={true}
                    />
                  </Group>
                ) : (
                  // Version desktop (inchangée)
                  <Group
                    x={0}
                    y={templateData.height - 32}
                    listening={false}
                  >
                    {/* Fond du footer - épuré, blanc légèrement coloré */}
                    <Rect
                      width={templateData.width}
                      height={32}
                      fill="#FFF9F5"
                      stroke="#FFE6D9"
                      strokeWidth={0.5}
                    />
                    {/* Zone pour le QR code - carrée et propre */}
                    <Group x={15} y={-8}>
                      {/* Emplacement pour le QR code */}
                      {isProfileLoading ? (
                        <Rect
                          width={40}
                          height={40}
                          fill="#FFFFFF"
                          stroke="#FFE6D9"
                          strokeWidth={0.5}
                        />
                      ) : userProfile && (userProfile.slug || userProfile.id) ? (
                        <QRCodeElementComponent
                          key="footer-qr-code"
                          element={{
                            id: "footer-qr-code",
                            type: "qrcode",
                            x: 0,
                            y: -2,
                            width: 40,
                            height: 40,
                            properties: {
                              data: `https://jobpartiel.fr/profil/${userProfile?.slug || userProfile?.id || 'profil'}`,
                              fill: "#000000",
                              background: "#ffffff",
                              visible: true
                            }
                          }}
                          isSelected={false}
                          onSelect={() => {}}
                          onDragStart={() => {}}
                          onDragMove={() => {}}
                          onDragEnd={() => {}}
                          isEditable={false}
                        />
                      ) : (
                        <Rect
                          width={40}
                          height={40}
                          fill="#FFFFFF"
                          stroke="#FFE6D9"
                          strokeWidth={0.5}
                        />
                      )}
                    </Group>
                    {/* Texte au-dessus du QR code */}
                    <Text
                      text="Scannez le QR Code pour"
                      fontSize={7}
                      fontFamily="'Inter', sans-serif"
                      fill="#FF6B2C"
                      x={60}
                      y={-10}
                    />
                    {/* Liste des avantages */}
                    <Group x={60} y={3}>
                      <Text
                        text="• Voir les avis laissés par mes clients"
                        fontSize={7}
                        fontFamily="'Inter', sans-serif"
                        fill="#666666"
                        y={0}
                      />
                      <Text
                        text="• Voir mes réalisations"
                        fontSize={7}
                        fontFamily="'Inter', sans-serif"
                        fill="#666666"
                        y={10}
                      />
                      <Text
                        text="• Me contacter"
                        fontSize={7}
                        fontFamily="'Inter', sans-serif"
                        fill="#666666"
                        y={20}
                      />
                    </Group>
                    {/* Information de contact - nom (supprimé) */}
                    <Group x={templateData.width / 2 + 10} y={4}></Group>
                    {/* URL du profil */}
                    {userProfile && (userProfile.slug || userProfile.id) ? (
                      <Text
                        text={`jobpartiel.fr/profil/${userProfile?.slug || userProfile?.id || ''}`}
                        fontSize={7}
                        fontFamily="'Inter', sans-serif"
                        fontStyle="italic"
                        fill="#FF6B2C"
                        x={templateData.width - 120}
                        y={16}
                      />
                    ) : (
                      <Text
                        text="jobpartiel.fr/"
                        fontSize={7}
                        fontFamily="'Inter', sans-serif"
                        fontStyle="italic"
                        fill="#FF6B2C"
                        opacity={0.7}
                        x={templateData.width - 150}
                        y={16}
                      />
                    )}
                  </Group>
                );
              })()
            ) : (
              // Logo JobPartiel
              <Text
                text="Créé avec JobPartiel.fr"
                fontSize={4.5}
                fontFamily="'Inter', sans-serif"
                fontStyle="bold"
                fill="#FF6B2C"
                opacity={0.7}
                x={templateData.width - 65}
                y={templateData.height - 10}
              />
            )}
          </Layer>
        </Stage>
        </Paper>
      </Box>
    </Box>
  );
});

export default CardEditorCanvas;
