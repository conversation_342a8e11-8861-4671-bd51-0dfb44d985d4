import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Card,
  CardContent,
  IconButton,
  CircularProgress,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import {
  Download,
  FileText,
  Image as ImageIcon,
  Camera,
  CheckCircle,
  Info,
  X
} from 'lucide-react';
import { motion } from 'framer-motion';

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  onExport: (format: 'pdf' | 'png' | 'jpg') => Promise<void>;
  templateName: string;
  templateId?: string;
  isExporting?: boolean;
}

const ExportModal: React.FC<ExportModalProps> = ({
  open,
  onClose,
  onExport,
  templateName,
  templateId,
  isExporting = false
}) => {
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'png' | 'jpg' | null>(null);
  const [exportingFormat, setExportingFormat] = useState<string | null>(null);

  const handleExport = async (format: 'pdf' | 'png' | 'jpg') => {
    setExportingFormat(format);
    try {
      await onExport(format);
    } finally {
      setExportingFormat(null);
      setSelectedFormat(null);
    }
  };

  const exportOptions = [
    {
      format: 'pdf' as const,
      title: 'PDF Haute Qualité',
      description: 'Parfait pour l\'impression professionnelle',
      icon: <FileText size={32} />,
      color: '#FF6B2C',
      bgColor: '#FFF8F3',
      features: ['Qualité vectorielle', 'Prêt pour impression', '~50-200 KB'],
      disabled: !templateId,
      disabledReason: 'Sauvegardez d\'abord votre template'
    },
    {
      format: 'png' as const,
      title: 'PNG Transparent',
      description: 'Idéal pour le web et les présentations',
      icon: <ImageIcon size={32} />,
      color: '#4CAF50',
      bgColor: '#F1F8E9',
      features: ['Fond transparent', 'Haute résolution', '~200-500 KB'],
      disabled: false
    },
    {
      format: 'jpg' as const,
      title: 'JPG Optimisé',
      description: 'Format universel, taille réduite',
      icon: <Camera size={32} />,
      color: '#2196F3',
      bgColor: '#E3F2FD',
      features: ['Taille réduite', 'Compatible partout', '~100-300 KB'],
      disabled: false
    }
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          overflow: 'hidden',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle
        sx={{
          background: 'linear-gradient(135deg, #FF6B2C 0%, #FF965E 100%)',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          py: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Download size={24} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Exporter votre création
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{ color: 'white', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
        >
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 1, fontWeight: 600 }}>
            {templateName}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Choisissez le format d'export qui correspond le mieux à vos besoins
          </Typography>
        </Box>

        <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' } }}>
          {exportOptions.map((option) => (
            <motion.div
              key={option.format}
              whileHover={{ scale: option.disabled ? 1 : 1.02 }}
              whileTap={{ scale: option.disabled ? 1 : 0.98 }}
            >
              <Card
                sx={{
                  cursor: option.disabled ? 'not-allowed' : 'pointer',
                  border: selectedFormat === option.format ? `2px solid ${option.color}` : '2px solid transparent',
                  opacity: option.disabled ? 0.6 : 1,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    boxShadow: option.disabled ? 'none' : `0 4px 20px ${option.color}20`,
                    transform: option.disabled ? 'none' : 'translateY(-2px)'
                  }
                }}
                onClick={() => !option.disabled && setSelectedFormat(option.format)}
              >
                <CardContent sx={{ p: 2.5, textAlign: 'center' }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      bgcolor: option.bgColor,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2,
                      color: option.color
                    }}
                  >
                    {option.icon}
                  </Box>

                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1, color: option.color }}>
                    {option.title}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {option.description}
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center', mb: 2 }}>
                    {option.features.map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        sx={{
                          bgcolor: option.bgColor,
                          color: option.color,
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                    ))}
                  </Box>

                  {option.disabled && (
                    <Alert severity="warning" sx={{ mt: 1, fontSize: '0.75rem' }}>
                      {option.disabledReason}
                    </Alert>
                  )}

                  <Button
                    variant={selectedFormat === option.format ? 'contained' : 'outlined'}
                    fullWidth
                    disabled={option.disabled || exportingFormat === option.format}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleExport(option.format);
                    }}
                    startIcon={
                      exportingFormat === option.format ? (
                        <CircularProgress size={16} />
                      ) : selectedFormat === option.format ? (
                        <CheckCircle size={16} />
                      ) : (
                        <Download size={16} />
                      )
                    }
                    sx={{
                      mt: 1,
                      bgcolor: selectedFormat === option.format ? option.color : 'transparent',
                      borderColor: option.color,
                      color: selectedFormat === option.format ? 'white' : option.color,
                      '&:hover': {
                        bgcolor: selectedFormat === option.format ? option.color : `${option.color}10`,
                        borderColor: option.color
                      }
                    }}
                  >
                    {exportingFormat === option.format ? 'Export...' : 'Télécharger'}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, p: 2, bgcolor: '#F5F5F5', borderRadius: 2 }}>
          <Info size={20} color="#FF6B2C" style={{ marginTop: 2, flexShrink: 0 }} />
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              <strong>Conseils d'utilisation :</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary" component="ul" sx={{ pl: 2, m: 0 }}>
              <li>📄 <strong>PDF</strong> : Idéal pour l'impression professionnelle chez un imprimeur</li>
              <li>🖼️ <strong>PNG</strong> : Parfait pour le web, réseaux sociaux, avec fond transparent</li>
              <li>📸 <strong>JPG</strong> : Format universel, taille réduite, pour email et partage</li>
            </Typography>
            <Typography variant="body2" color="warning.main" sx={{ mt: 1, fontStyle: 'italic' }}>
              ⚠️ <strong>Note :</strong> Les exports PNG/JPG peuvent échouer si votre design contient des images externes. Dans ce cas, utilisez le format PDF.
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: '#FFE4BA',
            color: '#FF6B2C',
            '&:hover': {
              borderColor: '#FF965E',
              bgcolor: '#FFF8F3'
            }
          }}
        >
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExportModal;
