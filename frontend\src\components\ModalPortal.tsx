import logger from '@/utils/logger';
import React, { useEffect, useState, ReactNode } from 'react';
import { createPortal } from 'react-dom';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';

// Compteur global de modals actives
let activeModalsCount = 0;
let initialScrollPosition = 0;

// Composant stylisé pour l'overlay avec effet de flou
const BlurOverlay = styled(motion.div)({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  backdropFilter: 'blur(6px)',
  zIndex: 1000,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '8px', // Réduire les marges sur mobile
  '@media (min-width: 600px)': { // Équivalent à sm
    padding: '16px' // Marges standards sur tablette
  },
  '@media (min-width: 960px)': { // Équivalent à md
    padding: '32px' // Marges standards sur desktop
  }
});

// Variantes d'animation pour l'overlay
const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1, 
    transition: { 
      duration: 0.15,
      ease: 'easeOut'
    } 
  },
  exit: { 
    opacity: 0, 
    transition: { 
      duration: 0.15,
      ease: 'easeIn'
    } 
  }
};

interface ModalPortalProps {
  children: ReactNode;
  containerId?: string;
  withBlur?: boolean; // Option pour activer/désactiver l'effet de flou
  overlayStyle?: React.CSSProperties; // Styles personnalisés pour l'overlay
  onBackdropClick?: () => void; // Fonction appelée lors du clic sur l'arrière-plan
  closeOnBackdropClick?: boolean; // Option pour activer/désactiver la fermeture au clic sur l'arrière-plan (activé par défaut)
  isOpen?: boolean; // Nouvel état pour contrôler l'animation
  zIndex?: number; // Nouvel attribut pour contrôler la priorité d'affichage
}

// Composant ModalPortal - Crée un portail React pour afficher des modales
// Caractéristiques :
// - Utilise createPortal pour rendre le contenu en dehors de la hiérarchie DOM normale
// - Bloque automatiquement le scroll de la page lorsqu'une modale est ouverte
// - Applique un effet de flou sur l'arrière-plan (désactivable)
// - Ferme automatiquement la modale au clic sur l'arrière-plan (désactivable)
// - Gère correctement l'empilement de plusieurs modales
// - Animations fluides à l'ouverture et à la fermeture avec Framer Motion

const ModalPortal: React.FC<ModalPortalProps> = ({ 
  children, 
  containerId = 'modal-root',
  withBlur = true, // Activé par défaut
  overlayStyle = {},
  onBackdropClick,
  closeOnBackdropClick = true, // Activé par défaut
  isOpen = true, // Par défaut, considéré comme ouvert
  zIndex = 1000 // Valeur par défaut pour le z-index
}) => {
  const [container, setContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    let modalContainer = document.getElementById(containerId);
    if (!modalContainer) {
      modalContainer = document.createElement('div');
      modalContainer.setAttribute('id', containerId);
      document.body.appendChild(modalContainer);
    }
    setContainer(modalContainer);

    return () => {
      if (modalContainer && !document.body.contains(modalContainer)) {
        document.body.removeChild(modalContainer);
      }
    };
  }, [containerId]);

  // Bloque le scroll du body lorsque le modal est ouvert
  useEffect(() => {
    if (container && isOpen) {

      // Incrémente le compteur de modals actives
      activeModalsCount++;

      // Sauvegarde la position initiale seulement pour la première modal
      if (activeModalsCount === 1) {
        logger.info('Lancement du modal et blocage du scroll');

        initialScrollPosition = window.scrollY;
        
        // Calculer la largeur de la scrollbar pour éviter le saut
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        
        // Appliquer les styles pour bloquer le scroll
        document.body.style.position = 'fixed';
        document.body.style.top = `-${initialScrollPosition}px`;
        document.body.style.left = '0';
        document.body.style.right = '0';
        document.body.style.width = '100%';
        document.body.style.paddingRight = `${scrollbarWidth}px`;
        document.body.style.overflow = 'hidden';
      }

      return () => {
        // Décrémente le compteur de modals actives
        activeModalsCount--;
        
        // Restaure le scroll seulement quand la dernière modal est fermée
        if (activeModalsCount === 0) {
          // Restaurer les styles
          document.body.style.position = '';
          document.body.style.top = '';
          document.body.style.left = '';
          document.body.style.right = '';
          document.body.style.width = '';
          document.body.style.paddingRight = '';
          document.body.style.overflow = '';
          
          // Restaurer la position de scroll de manière synchrone
          requestAnimationFrame(() => {
            document.documentElement.style.scrollBehavior = 'auto';
            window.scrollTo(0, initialScrollPosition);
            document.documentElement.style.scrollBehavior = '';
            logger.info('Restauration de la position de scroll');
          });
        }
      };
    }
  }, [container, isOpen]);

  if (!container) {
    return null;
  }

  // Gestionnaire de clic sur l'overlay
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // S'assurer que le clic est bien sur l'overlay et pas sur un élément enfant
    if (e.target === e.currentTarget) {
      if (onBackdropClick) {
        onBackdropClick();
      } else if (closeOnBackdropClick) {
        // Si aucun gestionnaire personnalisé n'est fourni mais que closeOnBackdropClick est activé,
        // nous fermons la modale en déclenchant un événement d'échappement
        const escEvent = new KeyboardEvent('keydown', {
          key: 'Escape',
          code: 'Escape',
          keyCode: 27,
          which: 27,
          bubbles: true
        });
        document.dispatchEvent(escEvent);
      }
    }
  };

  // Contenu avec animation
  const animatedContent = (
    <AnimatePresence mode="wait">
      {isOpen && withBlur && (
        <BlurOverlay 
          style={{ ...overlayStyle, zIndex }}
          onClick={handleBackdropClick}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={overlayVariants}
        >
            {children}
        </BlurOverlay>
      )}
        
      {isOpen && !withBlur && (
        <div style={{ zIndex }}>
          {children}
        </div>
      )}
    </AnimatePresence>
  );

  return createPortal(animatedContent, container);
};

export default ModalPortal; 