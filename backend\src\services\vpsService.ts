import axios from 'axios';
import logger from '../utils/logger';

const VPS_API_BASE_URL = process.env.VPS_API_BASE_URL;
const VPS_ID = process.env.VPS_ID;
const VPS_API_KEY = process.env.VPS_API_KEY;

interface VPSMetrics {
  cpu_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  ram_usage: {
    unit: string;
    usage: Record<string, number>;
  };
  disk_space: {
    unit: string;
    usage: Record<string, number>;
  };
  outgoing_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  incoming_traffic: {
    unit: string;
    usage: Record<string, number>;
  };
  uptime: {
    unit: string;
    usage: Record<string, number>;
  };
}

class VPSService {
  private static instance: VPSService;

  private constructor() {}

  public static getInstance(): VPSService {
    if (!VPSService.instance) {
      VPSService.instance = new VPSService();
    }
    return VPSService.instance;
  }

  async getVPSInfo() {
    try {
      const response = await axios.get(`${VPS_API_BASE_URL}/virtual-machines`, {
        headers: {
          Authorization: `Bearer ${VPS_API_KEY}`,
        },
      });
      logger.info('VPS info retrieved successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting VPS info:', error);
      throw error;
    }
  }

  async getVPSMetrics(dateFrom: Date, dateTo: Date) {
    try {
      const response = await axios.get<VPSMetrics>(
        `${VPS_API_BASE_URL}/virtual-machines/${VPS_ID}/metrics`,
        {
          headers: {
            Authorization: `Bearer ${VPS_API_KEY}`,
            'Content-Type': 'application/json',
          },
          params: {
            date_from: dateFrom.toISOString(),
            date_to: dateTo.toISOString(),
          },
        }
      );
      logger.info('VPS metrics retrieved successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting VPS metrics:', error);
      throw error;
    }
  }

  async getVPSHistory() {
    try {
      const response = await axios.get(
        `${VPS_API_BASE_URL}/virtual-machines/${VPS_ID}/actions`,
        {
          headers: {
            Authorization: `Bearer ${VPS_API_KEY}`,
          },
        }
      );
      logger.info('VPS history retrieved successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting VPS history:', error);
      throw error;
    }
  }

  async restartVPS() {
    try {
      const response = await axios.post(
        `${VPS_API_BASE_URL}/virtual-machines/${VPS_ID}/restart`,
        {},
        {
          headers: {
            Authorization: `Bearer ${VPS_API_KEY}`,
          },
        }
      );
      logger.info('VPS restart initiated successfully');
      return response.data;
    } catch (error) {
      logger.error('Error restarting VPS:', error);
      throw error;
    }
  }
}

export default VPSService.getInstance(); 