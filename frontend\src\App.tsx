import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { NotificationProvider } from './notification';
import { RefreshProvider } from './pages/dashboard/components/RefreshDashboard';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { JobiProvider } from './contexts/JobiContext';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
// import ReportBugButton from './components/BugReport/ReportBugButton';
import AiConsentProvider from './components/ai/AiConsentProvider';
import BottomNavigationBar from './components/BottomNavigationBar';
import { Container } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import logger from './utils/logger';
import { SeoPromotionProvider } from './components/SEOBanniere/SeoPromotionProvider';
import SeoPromotionBanner from './components/SEOBanniere/SeoPromotionBanner';

// Création d'une instance de QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 30000,
      refetchOnWindowFocus: false,
    },
  },
});

// Composant principal de l'application
function App() {
  const location = useLocation();

  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  // Effet pour gérer les erreurs de chargement des ressources
  React.useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      // Vérifier si l'erreur concerne le chargement d'une ressource
      if (event.target &&
          ((event.target as HTMLElement).tagName === 'SCRIPT' ||
           (event.target as HTMLElement).tagName === 'LINK')) {
        logger.info('Erreur de chargement de ressource:', event);

        // Si c'est une ressource critique, recharger la page après un délai
        const src = (event.target as HTMLScriptElement).src || (event.target as HTMLLinkElement).href;
        if (src && (
            src.includes('main-') ||
            src.includes('vendor-') ||
            src.includes('react-') ||
            src.includes('socket.io')
        )) {
          logger.warn('Ressource critique non chargée, rechargement de la page dans 2 secondes...');
          setTimeout(() => window.location.reload(), 2000);
        }
      }
    };

    // Ajouter l'écouteur d'événements
    window.addEventListener('error', handleError, true);

    // Nettoyer l'écouteur lors du démontage
    return () => {
      window.removeEventListener('error', handleError, true);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ErrorBoundary>
          <SocketProvider>
            <AppContent />
          </SocketProvider>
        </ErrorBoundary>
      </AuthProvider>
    </QueryClientProvider>
  );
}

// Composant pour capturer les erreurs React
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.info('Erreur capturée par ErrorBoundary:', error, errorInfo);
  }

  componentDidUpdate(prevProps: any, prevState: {hasError: boolean}) {
    if (this.state.hasError && !prevState.hasError) {
      // Recharger la page après 2 secondes en cas d'erreur
      setTimeout(() => window.location.reload(), 2000);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <div className="p-8 bg-white rounded-lg shadow-md text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Une erreur est survenue</h2>
            <p className="mb-4">L'application va redémarrer automatiquement...</p>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF7A35] mx-auto"></div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Contenu de l'application après l'initialisation du contexte d'authentification
const AppContent: React.FC = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');
  const isDashboardRoute = location.pathname.startsWith('/dashboard');

  return (
    <JobiProvider>
      <RefreshProvider>
        <AiConsentProvider>
          <SeoPromotionProvider>
            <div>
              <Navbar />
              <NotificationProvider />
              <main className="flex-grow w-full">
              {/* Limite la taille d'écran à 1480px pour toutes les pages sauf dashboard */}
              {!isDashboardRoute && (
                <Container
                  maxWidth={isAdminRoute ? false : 'lg'}
                  disableGutters={true}
                  sx={{
                    '@media (min-width: 980px)': {
                      maxWidth: isAdminRoute ? '100%' : '100%',
                      margin: '0 auto',
                      width: '100%'
                    }
                  }}
                >
                  <Outlet />
                </Container>
              )}
              {isDashboardRoute && <Outlet />}
            </main>
            {!isAdminRoute && <Footer />}
            <ScrollToTop />
            <ConditionalBugReportButton />
            <BottomNavigationBar />
            <SeoPromotionBanner />
          </div>
          </SeoPromotionProvider>
        </AiConsentProvider>
      </RefreshProvider>
    </JobiProvider>
  );
};

// Composant qui affiche le bouton de signalement de bug conditionnellement
const ConditionalBugReportButton: React.FC = () => {
  const { user } = useAuth();

  // N'afficher le bouton que si l'utilisateur est connecté
  if (!user) return null;

  // return <ReportBugButton position="bottom-right" color="primary" />;
};

export default App;
