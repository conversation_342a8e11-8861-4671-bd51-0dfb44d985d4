import React, { useState, useEffect } from 'react';
import { ArrowUp } from 'lucide-react';
import DOMPurify from 'dompurify';

const ScrollToTop: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Calcul de la position de scroll en pourcentage
      const scrollPercentage = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      setIsVisible(scrollPercentage > 30);
    };

    // Sécurisation des événements de scroll avec un debounce
    let timeoutId: NodeJS.Timeout;
    const debouncedScroll = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleScroll, 100);
    };

    window.addEventListener('scroll', debouncedScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', debouncedScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  const scrollToTop = () => {
    // Sécurisation de l'URL avant le scroll
    const sanitizedUrl = DOMPurify.sanitize(window.location.href);
    if (sanitizedUrl) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  return (
    <button
      onClick={scrollToTop}
      className={`fixed md:bottom-10 md:right-10 bottom-4 right-4 md:translate-x-0 -translate-x-8 z-50 p-3 rounded-full bg-[#FF7A35] text-white shadow-lg transition-all duration-300 hover:bg-[#e55a20] hover:scale-110 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:ring-offset-2 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-16 pointer-events-none'
      }`}
      aria-label="Retourner en haut de la page"
    >
      <ArrowUp className="w-6 h-6" />
    </button>
  );
};

export default ScrollToTop; 