import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Paper, 
  Badge, 
  CircularProgress, 
  Button,
  IconButton
} from '@mui/material';
import { 
  format, 
  getYear, 
  getMonth, 
  eachMonthOfInterval, 
  startOfYear, 
  endOfYear, 
  parseISO,
  addYears,
  subYears
} from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { Mission } from '../../types/planning';

interface YearViewProps {
  missions: Mission[];
  isLoading: boolean;
  onDateChange: (date: Date) => void;
  currentDate: Date;
  filters: any;
  onFilterChange: (filters: any) => void;
  onViewChange: (view: 'year' | 'calendar' | 'list') => void;
  onAddMission?: () => void;
}

const YearView: React.FC<YearViewProps> = ({
  missions,
  isLoading,
  onDateChange,
  currentDate,
  onViewChange
}) => {
  const [selectedYear, setSelectedYear] = useState<number>(getYear(currentDate));
  
  // Recalculer les mois de l'année à chaque changement d'année
  const months = useMemo(() => {
    const yearStart = startOfYear(new Date(selectedYear, 0, 1));
    const yearEnd = endOfYear(yearStart);
    return eachMonthOfInterval({ start: yearStart, end: yearEnd });
  }, [selectedYear]);

  // Mettre à jour l'année quand currentDate change
  useEffect(() => {
    setSelectedYear(getYear(currentDate));
  }, [currentDate]);

  // Gérer la navigation entre les années
  const handlePreviousYear = () => {
    const newDate = subYears(new Date(selectedYear, 0, 1), 1);
    setSelectedYear(getYear(newDate));
    onDateChange(newDate);
  };

  const handleNextYear = () => {
    const newDate = addYears(new Date(selectedYear, 0, 1), 1);
    setSelectedYear(getYear(newDate));
    onDateChange(newDate);
  };

  // Sélectionner un mois pour passer à la vue mensuelle
  const handleSelectMonth = (month: number) => {
    const newDate = new Date(selectedYear, month, 1);
    onDateChange(newDate);
    onViewChange('calendar');
  };

  // Calculer les statistiques des missions pour chaque mois
  const monthlyStats = useMemo(() => {
    if (!missions.length) return {};

    const stats: Record<string, { 
      total: number; 
      completed: number; 
      pending: number; 
      accepted: number; 
      other: number;
      jobis: number;
      euros: number;
      // Nouvelles catégories
      posted: number; // Missions postées sur JobPartiel
      manual: number; // Missions entrées manuellement
      acceptedOffers: number; // Missions où j'ai fait une offre acceptée
    }> = {};

    // Initialiser les statistiques pour chaque mois
    months.forEach(month => {
      const monthKey = format(month, 'yyyy-MM');
      stats[monthKey] = { 
        total: 0, 
        completed: 0, 
        pending: 0,
        accepted: 0,
        other: 0,
        jobis: 0,
        euros: 0,
        // Initialiser les nouvelles catégories
        posted: 0,
        manual: 0,
        acceptedOffers: 0
      };
    });

    // Calculer les statistiques pour chaque mission
    missions.forEach(mission => {
      const missionDate = parseISO(mission.date);
      const monthKey = format(missionDate, 'yyyy-MM');
      
      if (stats[monthKey]) {
        // Incrémenter le total
        stats[monthKey].total += 1;
        
        // Classifier le type de mission
        if (mission.mission && mission.mission.id) {
          // Pour l'instant, on considère que toutes les missions avec mission_id sont des offres acceptées,
          // car nous n'avons pas d'accès direct à l'identifiant de l'utilisateur courant
          stats[monthKey].acceptedOffers += 1;
        } else {
          // Mission entrée manuellement (sans mission_id)
          stats[monthKey].manual += 1;
        }
        
        // Déterminer le mode de paiement (priorité au mode de paiement de la mission)
        const paymentMethod = mission.payment_method || 
                              (mission.proposition ? mission.proposition.payment_method : 'jobi_only');
        
        // Déterminer le montant - priorité au montant défini dans la mission
        let montantFinal = mission.montant_propose !== undefined ? mission.montant_propose : 0;
        
        // Si aucun montant n'est défini dans la mission, utiliser celui de la proposition
        if (montantFinal === 0 && mission.proposition) {
          if (mission.proposition.statut === 'contre_offre_jobbeur' && mission.proposition.montant_contre_offre_jobbeur !== undefined) {
            montantFinal = mission.proposition.montant_contre_offre_jobbeur;
          } else if (mission.proposition.statut === 'contre_offre' && mission.proposition.montant_contre_offre !== undefined) {
            montantFinal = mission.proposition.montant_contre_offre;
          } else {
            montantFinal = mission.proposition.montant_propose || 0;
          }
        }
        
        // Compter selon le mode de paiement
        if (paymentMethod === 'jobi_only' || !paymentMethod) {
          stats[monthKey].jobis += montantFinal;
        } else if (paymentMethod === 'direct_only') {
          stats[monthKey].euros += montantFinal;
        } else if (paymentMethod === 'both') {
          // Mode hybride - on considère que c'est des Jobis par défaut
          stats[monthKey].jobis += montantFinal;
        }
        
        // Conserver les anciens compteurs pour compatibilité
        if (mission.proposition) {
          // Statut de paiement
          if (mission.proposition.payment_status === 'completed' || 
              mission.proposition.payment_status === 'manual') {
            stats[monthKey].completed += 1;
          } else if (mission.proposition.payment_status === 'pending') {
            stats[monthKey].pending += 1;
          }
          
          // Statut de proposition
          if (mission.proposition.statut === 'acceptée') {
            stats[monthKey].accepted += 1;
          } else {
            stats[monthKey].other += 1;
          }
        } else {
          // Missions sans proposition sont considérées comme "other"
          stats[monthKey].other += 1;
        }
      }
    });

    return stats;
  }, [missions, months]);

  // Afficher les couleurs selon la densité des missions
  const getMonthColor = (monthKey: string) => {
    const stats = monthlyStats[monthKey];
    if (!stats || stats.total === 0) return '#f5f5f5';
    
    // Calculer une couleur sur un dégradé en fonction du nombre de missions
    // Plus il y a de missions, plus l'intensité de la couleur est forte
    const maxMissions = Math.max(...Object.values(monthlyStats).map(s => s.total));
    const intensity = stats.total / maxMissions;
    
    // Dégradé de couleur orange #FF6B2C
    return `rgba(255, 107, 44, ${Math.max(0.1, intensity * 0.3)})`;
  };

  // Formater le nom du mois en français
  const formatMonth = (date: Date) => {
    return format(date, 'MMMM', { locale: fr });
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* En-tête avec la navigation annuelle */}
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between', 
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: 2, sm: 0 },
          mb: 3
        }}
      >
        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' }, 
          gap: { xs: 1, sm: 2 },
          width: { xs: '100%', sm: 'auto' }
        }}>
          <Typography 
            variant="h5" 
            sx={{ 
              fontWeight: 700,
              textTransform: 'capitalize',
              color: '#333',
              fontSize: { xs: '1.2rem', sm: '1.5rem' }
            }}
          >
            Vue annuelle {selectedYear}
          </Typography>
          
          <Button
            variant="text"
            startIcon={<Calendar size={16} />}
            onClick={() => onViewChange('calendar')}
            sx={{
              color: '#FF6B2C',
              fontSize: '0.8rem',
              textTransform: 'none',
              fontWeight: 600,
              padding: { xs: '4px 8px', sm: '6px 16px' },
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
              }
            }}
          >
            Retour au calendrier
          </Button>
        </Box>
        
        <Box 
          sx={{ 
            display: 'flex', 
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.03)',
            borderRadius: '20px',
            padding: '6px 8px',
            height: '42px',
            alignSelf: { xs: 'flex-start', sm: 'auto' }
          }}
        >
          <IconButton 
            onClick={handlePreviousYear} 
            size="small" 
            sx={{ 
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.1)',
                color: '#FF6B2C',
              },
              width: '36px',
              height: '36px',
              margin: '0 2px'
            }}
          >
            <ChevronLeft size={20} />
          </IconButton>
          <Typography sx={{ mx: 2, fontWeight: 600, minWidth: '60px', textAlign: 'center' }}>
            {selectedYear}
          </Typography>
          <IconButton 
            onClick={handleNextYear} 
            size="small"
            sx={{ 
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.1)',
                color: '#FF6B2C',
              },
              width: '36px',
              height: '36px',
              margin: '0 2px'
            }}
          >
            <ChevronRight size={20} />
          </IconButton>
        </Box>
      </Box>

      {/* Indicateur de chargement */}
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
        </Box>
      ) : (
        <>
          {/* Grille des mois */}
          <Grid container spacing={2}>
            {months.map((month, index) => {
              const monthKey = format(month, 'yyyy-MM');
              const stats = monthlyStats[monthKey] || { total: 0, completed: 0, pending: 0, accepted: 0, other: 0, jobis: 0, euros: 0 };
              
              return (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={monthKey}>
                  <Paper 
                    elevation={0}
                    onClick={() => handleSelectMonth(getMonth(month))}
                    sx={{ 
                      p: { xs: 1.5, sm: 1.8 },
                      borderRadius: '12px',
                      height: { xs: '185px', sm: '200px', md: '210px' },
                      display: 'flex',
                      flexDirection: 'column',
                      backgroundColor: getMonthColor(monthKey),
                      border: '1px solid',
                      borderColor: stats.total > 0 ? 'rgba(255, 107, 44, 0.2)' : '#eee',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 5px 15px rgba(0,0,0,0.08)',
                        borderColor: 'rgba(255, 107, 44, 0.3)'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography sx={{ 
                        fontWeight: 600, 
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        textTransform: 'capitalize', 
                        color: '#333',
                        position: 'relative',
                        '&::after': stats.total > 0 ? {
                          content: '""',
                          position: 'absolute',
                          width: '30%',
                          height: '2px',
                          background: 'linear-gradient(90deg, #FF6B2C 0%, rgba(255, 107, 44, 0.2) 100%)',
                          bottom: '-4px',
                          left: '0',
                          borderRadius: '4px'
                        } : {}
                      }}>
                        {formatMonth(month)}
                      </Typography>
                      
                      {stats.total > 0 && (
                        <Badge 
                          badgeContent={stats.total} 
                          color="error" 
                          sx={{ 
                            '& .MuiBadge-badge': { 
                              backgroundColor: '#FF6B2C',
                              fontWeight: 600,
                              fontSize: '0.7rem',
                              minWidth: '18px',
                              height: '18px',
                              padding: '0 4px'
                            } 
                          }}
                        />
                      )}
                    </Box>
                    
                    {stats.total > 0 ? (
                      <Box sx={{ mt: 0.5, flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                        <Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.3 }}>
                            <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              Missions ajoutées manuellement :
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#FF6B2C', fontSize: { xs: '0.7rem', sm: '0.75rem' }, ml: 0.5 }}>
                              {stats.manual}
                            </Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.3 }}>
                            <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              Missions publiées sur JobPartiel :
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#4CAF50', fontSize: { xs: '0.7rem', sm: '0.75rem' }, ml: 0.5 }}>
                              {stats.posted}
                            </Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.3 }}>
                            <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              Missions avec offres acceptées :
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#1976d2', fontSize: { xs: '0.7rem', sm: '0.75rem' }, ml: 0.5 }}>
                              {stats.acceptedOffers}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <Box>
                          <Box 
                            sx={{ 
                              mt: 0.5, 
                              py: 0.5,
                              px: 0.8,
                              borderRadius: '6px', 
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              display: 'flex',
                              justifyContent: 'space-between',
                              minHeight: '24px',
                              alignItems: 'center'
                            }}
                          >
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#FF6B2C', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              Total Jobis:
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 700, color: '#FF6B2C', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              {stats.jobis > 0 ? `${stats.jobis} J` : '0 J'}
                            </Typography>
                          </Box>
                          
                          <Box 
                            sx={{ 
                              mt: 0.5, 
                              py: 0.5,
                              px: 0.8,
                              borderRadius: '6px', 
                              backgroundColor: 'rgba(25, 118, 210, 0.08)',
                              display: 'flex',
                              justifyContent: 'space-between',
                              minHeight: '24px',
                              alignItems: 'center'
                            }}
                          >
                            <Typography variant="body2" sx={{ fontWeight: 600, color: '#1976d2', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              Total Euros:
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 700, color: '#1976d2', fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                              {stats.euros > 0 ? `${stats.euros} €` : '0 €'}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    ) : (
                      <Box sx={{ 
                        flex: 1, 
                        display: 'flex', 
                        flexDirection: 'column', 
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}>
                        <Box sx={{ 
                          display: 'flex', 
                          justifyContent: 'center', 
                          alignItems: 'center',
                          flexDirection: 'column',
                          width: '100%',
                          height: '100%'
                        }}>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              color: '#888',
                              textAlign: 'center',
                              fontStyle: 'italic',
                              fontSize: { xs: '0.75rem', sm: '0.8rem' }
                            }}
                          >
                            Aucune mission ce mois-ci
                          </Typography>
                          <Box 
                            sx={{ 
                              mt: 0.5,
                              width: '30%', 
                              height: '1px', 
                              backgroundColor: 'rgba(0, 0, 0, 0.1)',
                              borderRadius: '2px'
                            }}
                          />
                        </Box>
                      </Box>
                    )}
                  </Paper>
                </Grid>
              );
            })}
          </Grid>

          {/* Récapitulatif annuel */}
          <Paper
            elevation={0}
            sx={{
              mt: 4,
              p: 3,
              borderRadius: '16px',
              backgroundColor: 'rgba(255, 107, 44, 0.05)',
              border: '1px solid rgba(255, 107, 44, 0.2)',
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#333' }}>
              Récapitulatif annuel {selectedYear}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Box sx={{ 
                  p: { xs: 1.5, sm: 2 }, 
                  borderRadius: '12px', 
                  backgroundColor: 'white',
                  border: '1px solid #eee',
                  height: { xs: '90px', sm: '100px', md: '110px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                    Missions ajoutées manuellement
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#FF6B2C', fontSize: { xs: '1.4rem', sm: '1.7rem', md: '1.9rem' } }}>
                    {Object.values(monthlyStats).reduce((sum, stat) => sum + stat.manual, 0)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Box sx={{ 
                  p: { xs: 1.5, sm: 2 }, 
                  borderRadius: '12px', 
                  backgroundColor: 'white',
                  border: '1px solid #eee',
                  height: { xs: '90px', sm: '100px', md: '110px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                    Missions publiées sur JobPartiel
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#4CAF50', fontSize: { xs: '1.4rem', sm: '1.7rem', md: '1.9rem' } }}>
                    {Object.values(monthlyStats).reduce((sum, stat) => sum + stat.posted, 0)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Box sx={{ 
                  p: { xs: 1.5, sm: 2 }, 
                  borderRadius: '12px', 
                  backgroundColor: 'white',
                  border: '1px solid #eee',
                  height: { xs: '90px', sm: '100px', md: '110px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <Typography variant="body2" sx={{ color: '#666', fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                    Missions avec offres acceptées
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#1976d2', fontSize: { xs: '1.4rem', sm: '1.7rem', md: '1.9rem' } }}>
                    {Object.values(monthlyStats).reduce((sum, stat) => sum + stat.acceptedOffers, 0)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Box sx={{ 
                  p: { xs: 1.5, sm: 2 }, 
                  borderRadius: '12px', 
                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  height: { xs: '90px', sm: '100px', md: '110px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <Typography variant="body2" sx={{ color: '#FF6B2C', fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                    Total Jobis
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#FF6B2C', fontSize: { xs: '1.4rem', sm: '1.7rem', md: '1.9rem' } }}>
                    {Object.values(monthlyStats).reduce((sum, stat) => sum + stat.jobis, 0)} J
                  </Typography>
                </Box>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Box sx={{ 
                  p: { xs: 1.5, sm: 2 }, 
                  borderRadius: '12px', 
                  backgroundColor: 'rgba(25, 118, 210, 0.08)',
                  border: '1px solid rgba(25, 118, 210, 0.2)',
                  height: { xs: '90px', sm: '100px', md: '110px' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <Typography variant="body2" sx={{ color: '#1976d2', fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                    Total Euros
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: '#1976d2', fontSize: { xs: '1.4rem', sm: '1.7rem', md: '1.9rem' } }}>
                    {Object.values(monthlyStats).reduce((sum, stat) => sum + stat.euros, 0)} €
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default YearView; 