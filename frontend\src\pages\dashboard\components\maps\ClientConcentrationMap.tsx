import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

interface Region {
  nom: string;
  pourcentage: number;
}

interface ClientConcentrationMapProps {
  regions: Region[];
}

const ClientConcentrationMap: React.FC<ClientConcentrationMapProps> = ({ regions }) => {
  const mapRef = useRef<L.Map | null>(null);
  const [isLocked, setIsLocked] = useState(true);

  useEffect(() => {
    if (!mapRef.current) {
      // Initialiser la carte centrée sur la France
      mapRef.current = L.map('client-concentration-map', {
        center: [46.603354, 1.888334], // Centre de la France
        zoom: 5,
        zoomControl: !isLocked,
        dragging: !isLocked,
        scrollWheelZoom: !isLocked,
        doubleClickZoom: !isLocked,
        boxZoom: !isLocked,
        keyboard: !isLocked,
      });

      // Ajouter le fond de carte OpenStreetMap
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(mapRef.current);

      // Ajouter les cercles pour chaque région
      regions.forEach(region => {
        // Extraire le numéro de département
        const deptNumber = region.nom.replace('Dépt. ', '');
        
        // Coordonnées approximatives des départements français
        const deptCoords: { [key: string]: [number, number] } = {
          '01': [46.0500, 5.3500], // Ain
          '02': [49.5500, 3.5500], // Aisne
          '03': [46.3833, 3.0833], // Allier
          '04': [44.0833, 6.2333], // Alpes-de-Haute-Provence
          '05': [44.6500, 6.0833], // Hautes-Alpes
          '06': [43.7000, 7.2500], // Alpes-Maritimes
          '07': [44.7500, 4.4000], // Ardèche
          '08': [49.5000, 4.7167], // Ardennes
          '09': [42.9333, 1.4333], // Ariège
          '10': [48.3000, 4.1667], // Aube
          '11': [43.2167, 2.3500], // Aude
          '12': [44.3500, 2.5750], // Aveyron
          '13': [43.2965, 5.3698], // Bouches-du-Rhône
          '14': [49.1833, -0.3500], // Calvados
          '15': [45.0333, 2.6667], // Cantal
          '16': [45.6500, 0.1500], // Charente
          '17': [45.7500, -0.6333], // Charente-Maritime
          '18': [47.0833, 2.3833], // Cher
          '19': [45.2667, 1.7667], // Corrèze
          '21': [47.3167, 5.0167], // Côte-d'Or
          '22': [48.5167, -2.7833], // Côtes-d'Armor
          '23': [46.1667, 2.0667], // Creuse
          '24': [45.1833, 0.7167], // Dordogne
          '25': [47.2500, 6.0167], // Doubs
          '26': [44.7333, 5.0000], // Drôme
          '27': [49.0833, 1.1500], // Eure
          '28': [48.4500, 1.5000], // Eure-et-Loir
          '29': [48.0000, -4.1000], // Finistère
          '30': [43.8333, 4.3500], // Gard
          '31': [43.6000, 1.4333], // Haute-Garonne
          '32': [43.6500, 0.5833], // Gers
          '33': [44.8378, -0.5792], // Gironde
          '34': [43.6167, 3.8767], // Hérault
          '35': [48.1167, -1.6767], // Ille-et-Vilaine
          '36': [46.8167, 1.7167], // Indre
          '37': [47.3833, 0.6833], // Indre-et-Loire
          '38': [45.1667, 5.7167], // Isère
          '39': [46.6667, 5.5500], // Jura
          '40': [43.8900, -0.5000], // Landes
          '41': [47.5833, 1.3333], // Loir-et-Cher
          '42': [45.4333, 4.4000], // Loire
          '43': [45.0333, 3.8833], // Haute-Loire
          '44': [47.2167, -1.5500], // Loire-Atlantique
          '45': [47.9000, 2.2000], // Loiret
          '46': [44.6333, 1.6833], // Lot
          '47': [44.2000, 0.6333], // Lot-et-Garonne
          '48': [44.5167, 3.5000], // Lozère
          '49': [47.4667, -0.5500], // Maine-et-Loire
          '50': [49.0500, -1.2500], // Manche
          '51': [48.9500, 4.3667], // Marne
          '52': [48.1167, 5.1333], // Haute-Marne
          '53': [48.0667, -0.7667], // Mayenne
          '54': [48.6833, 6.2000], // Meurthe-et-Moselle
          '55': [49.0000, 5.1667], // Meuse
          '56': [47.7500, -2.7500], // Morbihan
          '57': [49.1000, 6.6500], // Moselle
          '58': [47.0833, 3.5000], // Nièvre
          '59': [50.6292, 3.0573], // Nord
          '60': [49.4167, 2.4167], // Oise
          '61': [48.5833, 0.0333], // Orne
          '62': [50.4500, 2.7333], // Pas-de-Calais
          '63': [45.7833, 3.0833], // Puy-de-Dôme
          '64': [43.3000, -0.3667], // Pyrénées-Atlantiques
          '65': [43.0333, 0.1500], // Hautes-Pyrénées
          '66': [42.7000, 2.9000], // Pyrénées-Orientales
          '67': [48.5833, 7.7500], // Bas-Rhin
          '68': [47.9500, 7.3167], // Haut-Rhin
          '69': [45.7578, 4.8320], // Rhône
          '70': [47.6333, 6.0833], // Haute-Saône
          '71': [46.5833, 4.5500], // Saône-et-Loire
          '72': [48.0000, 0.2000], // Sarthe
          '73': [45.5667, 6.3000], // Savoie
          '74': [46.0000, 6.3333], // Haute-Savoie
          '75': [48.8566, 2.3522], // Paris
          '76': [49.6500, 0.9667], // Seine-Maritime
          '77': [48.6000, 3.0000], // Seine-et-Marne
          '78': [48.8000, 1.9500], // Yvelines
          '79': [46.5000, -0.3333], // Deux-Sèvres
          '80': [49.9000, 2.3000], // Somme
          '81': [43.9333, 2.1500], // Tarn
          '82': [44.0167, 1.3500], // Tarn-et-Garonne
          '83': [43.4167, 6.2667], // Var
          '84': [44.0500, 5.0500], // Vaucluse
          '85': [46.6667, -1.4333], // Vendée
          '86': [46.5833, 0.3333], // Vienne
          '87': [45.8333, 1.2500], // Haute-Vienne
          '88': [48.1667, 6.4500], // Vosges
          '89': [47.8000, 3.5667], // Yonne
          '90': [47.6333, 6.8667], // Territoire de Belfort
          '91': [48.6333, 2.4333], // Essonne
          '92': [48.8833, 2.2000], // Hauts-de-Seine
          '93': [48.9167, 2.4833], // Seine-Saint-Denis
          '94': [48.7833, 2.4667], // Val-de-Marne
          '95': [49.0833, 2.1333], // Val-d'Oise
          // DOM-TOM
          '971': [16.2500, -61.5833], // Guadeloupe
          '972': [14.6667, -61.0000], // Martinique
          '973': [4.9333, -52.3333], // Guyane
          '974': [-21.1144, 55.5325], // La Réunion
          '976': [-12.7806, 45.2278], // Mayotte
        };

        // Si on a les coordonnées pour ce département
        if (deptCoords[deptNumber]) {
          const radius = Math.sqrt(region.pourcentage) * 10000; // Rayon proportionnel au pourcentage
          
          L.circle(deptCoords[deptNumber], {
            color: '#FF6B2C',
            fillColor: '#FF6B2C',
            fillOpacity: 0.2,
            radius: radius,
            weight: 1
          })
          .bindTooltip(`${region.nom}: ${region.pourcentage}%`, {
            permanent: false,
            direction: 'top'
          })
          .addTo(mapRef.current!);
        }
      });
    }

    // Mettre à jour les contrôles de la carte quand isLocked change
    if (mapRef.current) {
      if (isLocked) {
        mapRef.current.dragging.disable();
        mapRef.current.scrollWheelZoom.disable();
        mapRef.current.doubleClickZoom.disable();
        mapRef.current.boxZoom.disable();
        mapRef.current.keyboard.disable();
        mapRef.current.zoomControl?.remove();
      } else {
        mapRef.current.dragging.enable();
        mapRef.current.scrollWheelZoom.enable();
        mapRef.current.doubleClickZoom.enable();
        mapRef.current.boxZoom.enable();
        mapRef.current.keyboard.enable();
        if (!mapRef.current.zoomControl) {
          mapRef.current.addControl(L.control.zoom());
        }
      }
    }

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [regions, isLocked]);

  return (
    <div className="relative h-full w-full">
      <div id="client-concentration-map" className="h-full w-full rounded-lg" />
      <button
        onClick={() => setIsLocked(!isLocked)}
        className="absolute top-2 right-2 z-[1000] bg-white p-2 rounded-lg shadow-md hover:bg-gray-100 transition-colors"
        title={isLocked ? "Débloquer la carte" : "Bloquer la carte"}
      >
        {isLocked ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FF6B2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FF6B2C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
          </svg>
        )}
      </button>
    </div>
  );
};

export default ClientConcentrationMap; 