import React, {useState, useRef, useEffect } from 'react';
import ModalPortal from '@/components/ModalPortal';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { notify } from '@/components/Notification';
import { fetchCsrfToken } from '@/services/csrf';
import { getMultipartHeaders } from '@/utils/headers';
import imageCompression from 'browser-image-compression';

const documentTypes = [
  { key: 'kbis', label: 'Kbis', description: 'Extrait Kbis de moins de 3 mois', accept: '.pdf,.jpg,.jpeg,.png' },
  { key: 'assurance', label: "Attestation d'assurance", description: "Attestation d'assurance professionnelle", accept: '.pdf,.jpg,.jpeg,.png' },
  { key: 'identity', label: "Carte d'identité", description: "Carte d'identité du dirigeant", accept: '.pdf,.jpg,.jpeg,.png' },
  { key: 'autre', label: 'Autre document', description: 'Tout autre justificatif utile', accept: '.pdf,.jpg,.jpeg,.png' },
];

export interface EntrepriseVerificationDocument {
  id: string;
  type: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadDate: string;
  documentUrl: string;
  comments?: string;
  fileName?: string;
}

interface EntrepriseVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  typeDeProfil?: string;
  profilEntreprise?: any;
  initialType?: string;
}

export const EntrepriseVerificationModal: React.FC<EntrepriseVerificationModalProps> = ({ isOpen, onClose, typeDeProfil, profilEntreprise, initialType }) => {
  const [selectedType, setSelectedType] = useState(documentTypes[0].key);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [documents, setDocuments] = useState<EntrepriseVerificationDocument[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<{ file: File, type: string }[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewFile, setPreviewFile] = useState<File|null>(null);
  const [compressionProgress, setCompressionProgress] = useState<number>(0);

  // Charger la liste des documents déjà envoyés
  useEffect(() => {
    if (!isOpen) return;
    const fetchDocs = async () => {
      try {
        const headers = await getCommonHeaders();
        const res = await axios.get(`${API_CONFIG.baseURL}/api/users/verification/entreprise/my-docs`, { headers, withCredentials: true });
        const docs = (res.data.docs || []).map((doc: any) => ({
          ...doc,
          fileName: doc.fileName || doc.file_name,
          uploadDate: doc.uploadDate || doc.upload_date,
          documentUrl: doc.documentUrl || doc.file_url,
        }));
        setDocuments(docs);
      } catch (e) {
        setDocuments([]);
      }
    };
    fetchDocs();
  }, [isOpen]);

  // Correction : sélectionne automatiquement le bon type selon le profil ou la prop initialType
  useEffect(() => {
    if (initialType && documentTypes.some(dt => dt.key === initialType)) {
      setSelectedType(initialType);
    } else if (typeDeProfil === 'particulier') {
      setSelectedType('identity');
    } else if (typeDeProfil === 'entreprise') {
      setSelectedType('kbis');
    }
  }, [typeDeProfil, isOpen, initialType]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    let duplicateFound = false;
    let duplicateName = '';
    let tooBigFound = false;
    let tooBigName = '';
    let tooMany = false;
    const maxFiles = 10;
    const maxSize = 10 * 1024 * 1024; // 10 Mo
    let newFiles: { file: File, type: string }[] = [];
    let totalImages = Array.from(files).filter(f => f.type.startsWith('image/')).length;
    let compressedCount = 0;
    if (totalImages > 0) setCompressionProgress(1);
    for (const file of Array.from(files)) {
      if (file.size > maxSize) {
        tooBigFound = true;
        tooBigName = file.name;
        continue;
      }
      const isDuplicate = selectedFiles.some(sel =>
        sel.file.name === file.name &&
        sel.file.size === file.size &&
        sel.file.lastModified === file.lastModified
      );
      if (isDuplicate) {
        duplicateFound = true;
        duplicateName = file.name;
        continue;
      }
      if (file.type.startsWith('image/')) {
        try {
          const compressed = await imageCompression(file, {
            maxSizeMB: 1.5,
            maxWidthOrHeight: 1920,
            useWebWorker: true,
            onProgress: (progress) => {
              setCompressionProgress(Math.round(((compressedCount + progress / 100) / totalImages) * 100));
            }
          });
          compressedCount++;
          setCompressionProgress(Math.round((compressedCount / totalImages) * 100));
          const compressedFile = new File([compressed], file.name, { type: compressed.type });
          newFiles.push({ file: compressedFile, type: selectedType });
        } catch (err) {
          notify(`Erreur lors de la compression de l'image ${file.name}`, 'error');
        }
      } else {
        newFiles.push({ file, type: selectedType });
      }
    }
    setCompressionProgress(0);
    // Limite totale de fichiers
    if (selectedFiles.length + newFiles.length > maxFiles) {
      tooMany = true;
      newFiles = newFiles.slice(0, maxFiles - selectedFiles.length);
    }
    setSelectedFiles(prev => [...prev, ...newFiles]);
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (tooBigFound) {
      notify(`Le fichier "${tooBigName}" dépasse la taille maximale de 10 Mo.`, 'warning');
    }
    if (duplicateFound) {
      notify(`Le fichier "${duplicateName}" a déjà été ajouté pour ce type de document.`, 'warning');
    }
    if (tooMany) {
      notify('Vous ne pouvez pas ajouter plus de 10 documents.', 'warning');
    }
  };

  const handleRemoveFile = (fileToRemove: File, typeToRemove: string) => {
    setSelectedFiles(prev => prev.filter(sel =>
      !(
        sel.file.name === fileToRemove.name &&
        sel.file.size === fileToRemove.size &&
        sel.file.lastModified === fileToRemove.lastModified &&
        sel.type === typeToRemove
      )
    ));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;
    setUploading(true);
    setUploadProgress(0);
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const formData = new FormData();
      selectedFiles.forEach(({ file, type }) => {
        formData.append('files[]', file);
        formData.append('types[]', type);
      });
      const res = await axios.post(`${API_CONFIG.baseURL}/api/users/verification/entreprise/upload`, formData, {
        headers: { ...headers, 'Content-Type': 'multipart/form-data' },
        withCredentials: true,
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            setUploadProgress(Math.round((progressEvent.loaded * 100) / progressEvent.total));
          }
        },
      });
      if (res.data.success) {
        notify('Document(s) envoyé(s) avec succès', 'success');
        setSelectedFiles([]);
        // Recharge la liste des documents avec la nouvelle route
        const headers = await getCommonHeaders();
        const resList = await axios.get(`${API_CONFIG.baseURL}/api/users/verification/entreprise/my-docs`, { headers, withCredentials: true });
        const docs = (resList.data.docs || []).map((doc: any) => ({
          ...doc,
          fileName: doc.fileName || doc.file_name,
          uploadDate: doc.uploadDate || doc.upload_date,
          documentUrl: doc.documentUrl || doc.file_url,
        }));
        setDocuments(docs);
      } else {
        notify('Erreur lors de l\'envoi du document', 'error');
      }
    } catch (e: any) {
      notify(e?.response?.data?.error || 'Erreur lors de l\'upload', 'error');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'Validé';
      case 'rejected': return 'Refusé';
      default: return 'En attente';
    }
  };

  // Fonction utilitaire pour vérifier si les infos entreprise sont vides
  const isEntrepriseInfoEmpty = (profil: any) => {
    if (!profil || profil.type_de_profil !== 'entreprise') return false;
    return !profil.siren_entreprise && !profil.nom_entreprise && !profil.statut_entreprise && !profil.code_ape_entreprise && !profil.categorie_entreprise && !profil.effectif_entreprise && !profil.date_insee_creation_entreprise && !profil.date_derniere_mise_a_jour_entreprise_insee;
  };

  const labelByType: Record<string, string> = {
    identity: "Pièces d'identité envoyées",
    kbis: "Kbis envoyés",
    assurance: "Attestations d'assurance envoyées",
    autre: "Autres documents envoyés"
  };

  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={onClose} closeOnBackdropClick>
      <div className="bg-white rounded-xl p-8 max-w-2xl w-full mx-auto shadow-lg max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold mb-4 text-gray-800">
          {typeDeProfil === 'entreprise' ? "Vérification de l'entreprise" : typeDeProfil === 'particulier' ? "Vérification de l'identité" : "Vérification du profil"}
        </h2>
        {typeDeProfil && (
          <>
            <p className="mb-2 text-[#FF6B2C] font-semibold">
              {typeDeProfil === 'entreprise'
                ? 'Les documents ne sont stockés que le temps de la validation ou du refus.'
                : 'Votre document est stocké uniquement le temps de la validation ou du refus.'}
            </p>
            <p className="mb-6 text-gray-600">
              {typeDeProfil === 'entreprise'
                ? 'Joignez ici tous les documents nécessaires à la validation de votre entreprise (Kbis, Assurance, Identité, etc). Vous pouvez ajouter des documents supplémentaires à tout moment.'
                : "Joignez ici votre pièce d'identité (CNI, passeport, etc) pour valider votre profil. Vous pouvez ajouter un document supplémentaire à tout moment."}
            </p>
            {/* AVERTISSEMENT SI INFOS ENTREPRISE VIDES */}
            {typeDeProfil === 'entreprise' && isEntrepriseInfoEmpty(profilEntreprise) && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-900 text-center mb-4 flex flex-col items-center gap-2">
                <div className="flex items-center gap-2 mb-1">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 11 3 12a9 9 0 0118 0z" /></svg>
                  <span className="font-bold text-lg">Informations entreprise incomplètes</span>
                </div>
                <div className="mb-1 text-base font-medium">Vous devez remplir toutes les informations de votre entreprise pour que vos documents puissent être validés.</div>
                <div className="mb-1 text-sm">Tant que ces informations ne sont pas complétées, <b>aucun document ne pourra être validé</b> et ils pourront être supprimés automatiquement après un certain délai.</div>
                <div className="text-sm text-gray-600">Pour compléter vos informations, rendez-vous dans l'onglet <b>Informations profil</b> puis remplissez tous les champs obligatoires de la section entreprise.</div>
              </div>
            )}
          </>
        )}
        <div className="mb-4">
          {typeDeProfil === undefined || typeDeProfil === '' ? (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-900 text-center mb-4 flex flex-col items-center gap-2">
              <div className="flex items-center gap-2 mb-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 11 3 12a9 9 0 0118 0z" /></svg>
                <span className="font-bold text-lg">Sélection du type de profil requise</span>
              </div>
              <div className="mb-1 text-base font-medium">Vous devez choisir votre type de profil pour continuer.</div>
              <div className="mb-1 text-sm">Ce choix détermine les documents à fournir pour la vérification de votre compte.</div>
              <div className="text-sm text-gray-600">Pour sélectionner votre type de profil, rendez-vous dans l'onglet <b>Informations profil</b> de votre profil puis choisissez <span className='font-semibold'>Particulier</span> ou <span className='font-semibold'>Entreprise</span>.</div>
            </div>
          ) : (
            <div className="flex flex-wrap gap-2 mb-2">
              {(typeDeProfil === 'particulier'
                ? documentTypes.filter(t => t.key === 'identity')
                : [
                    ...documentTypes.filter(t => t.key !== 'autre').sort((a, b) => a.label.localeCompare(b.label)),
                    documentTypes.find(t => t.key === 'autre')!
                  ]
              ).map((type) => {
                const count = selectedFiles.filter(f => f.type === type.key).length + documents.filter(d => d.type === type.key).length;
                return (
                  <button
                    key={type.key}
                    onClick={() => setSelectedType(type.key)}
                    className={`relative px-4 py-2 rounded-lg border-2 text-sm font-medium transition-colors ${selectedType === type.key ? 'border-[#FF6B2C] bg-[#FFF8F3]' : 'border-gray-200 hover:border-gray-300'}`}
                    disabled={uploading}
                  >
                    {type.label}
                    {count > 0 && (
                      <span className="absolute -top-2 -right-2 bg-[#FF6B2C] text-white text-xs rounded-full px-1.5 py-0.5 min-w-[20px] text-center">{count}</span>
                    )}
                  </button>
                );
              })}
            </div>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept={documentTypes.find(t => t.key === selectedType)?.accept}
            className="hidden"
            onChange={handleFileChange}
            multiple
            disabled={uploading || !typeDeProfil}
          />
          {selectedFiles.filter(sel => sel.type === selectedType).length > 0 && (
            <div className="mt-4 space-y-2">
              {selectedFiles.filter(sel => sel.type === selectedType).map(({ file, type }) => (
                <div key={file.name + file.size + file.lastModified} className="flex items-center gap-3 bg-gray-50 rounded p-2">
                  {file.type.startsWith('image/') ? (
                    <img
                      src={URL.createObjectURL(file)}
                      alt={file.name}
                      className="w-12 h-12 object-cover rounded cursor-pointer"
                      onClick={() => setPreviewFile(file)}
                    />
                  ) : (
                    <span
                      className="w-12 h-12 flex items-center justify-center bg-gray-200 rounded text-gray-500 font-bold cursor-pointer"
                      onClick={() => setPreviewFile(file)}
                    >PDF</span>
                  )}
                  <div className="flex-1">
                    <div className="font-medium text-gray-800 text-sm">{file.name}</div>
                    <div className="text-xs text-gray-500">{documentTypes.find(t => t.key === type)?.label || type}</div>
                  </div>
                  <button onClick={() => handleRemoveFile(file, type)} className="text-red-500 hover:text-red-700 text-xs">Supprimer</button>
                </div>
              ))}
            </div>
          )}
          {/* Barre de progression compression image */}
          {compressionProgress > 0 && compressionProgress < 100 && (
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${compressionProgress}%` }} />
              <div className="text-xs text-center mt-1 text-gray-500">Compression en cours… {compressionProgress}%</div>
            </div>
          )}
          {uploading && (
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${uploadProgress}%` }} />
            </div>
          )}
        </div>
        <div className="bg-white rounded-lg shadow-sm mt-6">
          {typeDeProfil && documents.length > 0 && (
            <div className="px-4 py-3 border-b border-gray-100">
              <h3 className="text-lg font-medium">{labelByType[selectedType] || "Documents envoyés"}</h3>
            </div>
          )}
          <div className="divide-y divide-gray-100">
            {typeDeProfil && documents.filter(doc => doc.type === selectedType).length === 0 && <div className="px-4 py-6 text-gray-400 text-center">Aucun document envoyé pour le moment.</div>}
            {typeDeProfil && documents.filter(doc => doc.type === selectedType).map((doc) => (
              <div key={doc.id} className="px-4 py-4 flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-800">{documentTypes.find(t => t.key === doc.type)?.label || doc.type}</div>
                  <div className="text-sm text-gray-500">{doc.fileName || (doc.documentUrl ? doc.documentUrl.split('/').pop() : 'Nom inconnu')}</div>
                  <div className="text-xs text-gray-400">Envoyé le {new Date(doc.uploadDate).toLocaleDateString('fr-FR')}</div>
                  {doc.comments && <div className="text-xs text-red-500 mt-1">{doc.comments}</div>}
                </div>
                <div className="flex flex-col items-end gap-2">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(doc.status)}`}>{getStatusText(doc.status)}</span>
                  <a href={doc.documentUrl} target="_blank" rel="noopener noreferrer" className="text-[#FF6B2C] hover:text-[#FF7A35] text-xs">Voir</a>
                </div>
              </div>
            ))}
          </div>
        </div>
        {typeDeProfil && (
          <div className="mt-6 flex justify-center">
            <button
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
            >
              Joindre un ou plusieurs documents
            </button>
          </div>
        )}
        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-8">
          <button onClick={onClose} className="px-6 py-2 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">Fermer</button>
          {typeDeProfil && selectedFiles.length > 0 && (
            <button
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
              onClick={handleUpload}
              disabled={uploading}
            >
              {uploading ? 'Envoi en cours...' : 'Envoyer les documents'}
            </button>
          )}
        </div>
      </div>
      {/* Modal de prévisualisation */}
      {previewFile && (
        <ModalPortal isOpen={true} onBackdropClick={() => setPreviewFile(null)} closeOnBackdropClick>
          <div className="bg-white rounded-xl shadow-lg flex flex-col items-center justify-center p-6 max-w-lg w-full mx-auto">
            <button onClick={() => setPreviewFile(null)} className="mb-4 self-end px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">Fermer</button>
            {previewFile.type.startsWith('image/') ? (
              <img src={URL.createObjectURL(previewFile)} alt={previewFile.name} className="max-w-full max-h-[70vh] rounded shadow" />
            ) : (
              <div className="flex flex-col items-center">
                <span className="text-4xl mb-2">📄</span>
                <div className="mb-2 font-medium">{previewFile.name}</div>
                <div className="text-gray-500 mb-4">Aperçu non disponible pour ce type de fichier.</div>
                <a
                  href={URL.createObjectURL(previewFile)}
                  download={previewFile.name}
                  className="px-4 py-2 bg-[#FF6B2C] text-white rounded hover:bg-[#FF7A35]"
                >Télécharger</a>
              </div>
            )}
          </div>
        </ModalPortal>
      )}
    </ModalPortal>
  );
};

export default EntrepriseVerificationModal; 