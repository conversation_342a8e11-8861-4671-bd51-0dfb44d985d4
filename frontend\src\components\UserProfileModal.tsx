import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Grid, Chip, Avatar, IconButton, Divider, Paper, Button } from '@mui/material';
import { 
  MapPin, Award, Shield, CheckCircle, X, Calendar, Briefcase, MapIcon, 
  Phone, Clock, AlertTriangle, Building, FileText, User, 
  ChevronLeft, ChevronRight, Info, Star, Lock, History
} from 'lucide-react';
import ModalPortal from './ModalPortal';
import { useAuth } from '../contexts/AuthContext';
import { useReviews, Review } from '@/hooks/useReviews';
import logger from '@/utils/logger';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { notify } from './Notification';
import { useNavigate } from 'react-router-dom';
import ModalReview from '@/components/ReviewModalSelecteurMission';
import ReviewModalListeDesAvis from '@/components/ReviewModalListeDesAvis';
import ReviewModalPopupDepotAvis from '@/components/ReviewModalPopupDepotAvis';
import { getCommonHeaders } from '../utils/headers';
import { BadgesDisplay } from '@/pages/dashboard/profil/badges';
import { usePublicUserActions } from '@/hooks/usePublicUserActions';
import RecentUserActions from '@/components/common/RecentUserActions';
import ReportProfileModal from './profile/ReportProfileModal';

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    id: string;
    email: string;
    profil_verifier: boolean;
    identite_verifier: boolean;
    entreprise_verifier: boolean;
    assurance_verifier: boolean;
    email_verifier?: boolean;
    is_online: boolean;
    profil_actif: boolean;
    date_inscription: string;
    role: string;
    mode_vacance?: boolean;
    total_reviews?: number;
    profil: {
      data: {
        nom?: string;
        prenom?: string;
        photo_url?: string;
        bio?: string;
        ville?: string;
        pays?: string;
        slug?: string;
        telephone?: string;
        telephone_prive?: boolean;
        type_de_profil?: string;
        intervention_zone?: {
          center: number[];
          radius: number;
          adresse: string;
          france_entiere: boolean;
        };
        nom_entreprise?: string;
        siren_entreprise?: string;
        statut_entreprise?: string;
        code_ape_entreprise?: string;
        effectif_entreprise?: string;
        date_insee_creation_entreprise?: string;
        categorie_entreprise?: string;
        created_at?: string;
        updated_at?: string;
      }
    };
    services?: Array<{
      id: string;
      titre: string;
      description: string;
      tarif_horaire: number;
      category_id: string;
      subcategory_id: string;
    }>;
    galleryFolders?: Array<{
      id: string;
      name: string;
      description?: string;
      cover_image?: string;
      photos: Array<{
        id: string;
        photo_url: string;
        caption?: string;
        order_index?: number;
      }>;
    }>;
    featuredPhotos?: Array<{
      id: string;
      photo_url: string;
      caption?: string;
      created_at: string;
    }>;
    success?: boolean;
  };
  zIndex?: number; // Ajout du paramètre zIndex optionnel
}

const UserProfileModal: React.FC<UserProfileModalProps> = ({ isOpen, onClose, userData, zIndex = 1000 }) => {
  const { user } = useAuth();
  const [selectedPhoto, setSelectedPhoto] = useState<{
    id: string;
    photo_url: string;
    caption?: string;
    folder?: string;
  } | null>(null);
  const { fetchReviews, getReview, deleteReview } = useReviews({ userId: userData.id });
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isMissionSelectModalOpen, setIsMissionSelectModalOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [selectedMissionId, setSelectedMissionId] = useState<string | null>(null);
  const [selectedReviewerProfile, setSelectedReviewerProfile] = useState<any>(null);
  const [isReviewerProfileModalOpen, setIsReviewerProfileModalOpen] = useState(false);
  const [isDeleteConfirmationModalOpen, setIsDeleteConfirmationModalOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);
  const navigate = useNavigate();
  const [refreshKey, setRefreshKey] = useState(0);
  const [stats, setStats] = useState<{ total_reviews: number; rating: number; completion_rate: number } | null>(null);
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  
  // Récupérer les actions récentes de l'utilisateur
  const { actions, loading: actionsLoading, error: actionsError } = usePublicUserActions({ 
    userId: userData.id || '', 
    enabled: isOpen && !!userData.id 
  });

  // useEffect(() => {
  //   if (isOpen) {
  //     logger.info('🔍 Chargement modal profil utilisateur : ', userData);
  //     fetchReviews(1);
  //   }
  // }, [isOpen]);

  if (!isOpen) return null;

  // Formatage du nom pour n'afficher que la première lettre du nom suivie d'un point
  const formatName = () => {
    const prenom = userData.profil?.data?.prenom || '';
    const nom = userData.profil?.data?.nom || '';
    
    if (nom && nom.length > 0) {
      return `${prenom} ${nom.charAt(0)}.`;
    }
    
    return prenom;
  };

  // Formatage de la date d'inscription
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' });
  };

  // Vérifier si des sections ont du contenu
  const hasBio = !!userData.profil?.data?.bio;
  const hasServices = userData.services && userData.services.length > 0;
  const hasEnterpriseInfo = userData.profil?.data?.type_de_profil === 'entreprise' && 
    (userData.profil?.data?.siren_entreprise || 
     userData.profil?.data?.code_ape_entreprise || 
     userData.profil?.data?.date_insee_creation_entreprise || 
     userData.profil?.data?.categorie_entreprise);
  const hasInterventionInfo = userData.profil?.data?.intervention_zone || 
    (userData.profil?.data?.telephone && !userData.profil?.data?.telephone_prive);
  const hasGallery = (userData.featuredPhotos && userData.featuredPhotos.length > 0) || 
    (userData.galleryFolders && userData.galleryFolders.length > 0);
  const hasActions = actions && actions.length > 0;

  // Créer les sections pour le Masonry layout
  const renderSections = () => {
    const sections = [];

    // Section Statut du profil - Toujours affichée
    sections.push(
      <Paper key="statut" elevation={0} sx={{ 
        p: 1.5, // 12px
        borderRadius: '16px', 
        border: '1px solid #FFE4BA',
        backgroundColor: 'white',
        transition: 'all 0.3s ease',
        '&:hover': {
          backgroundColor: '#FFF8F3'
        }
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1.5, 
          mb: 3,
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: '-8px',
            left: 0,
            width: '40px',
            height: '3px',
            backgroundColor: '#FF6B2C',
            borderRadius: '2px'
          }
        }}>
          <Clock size={20} color="#FF6B2C" />
          <Typography variant="h6" sx={{ 
            color: '#2D3748', 
            fontWeight: 'bold', 
            fontSize: '1.1rem',
            letterSpacing: '-0.5px'
          }}>
            Profil
          </Typography>
        </Box>
        <Grid container spacing={2}>
          {/* Statut d'activité */}
          <Grid size={12}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'flex-start', 
              gap: 1.5,
              backgroundColor: userData.profil_actif ? 'rgba(76, 175, 80, 0.08)' : 'rgba(244, 67, 54, 0.08)',
              padding: '12px 16px',
              borderRadius: '12px',
              border: `1px solid ${userData.profil_actif ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)'}`,
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: userData.profil_actif 
                  ? '0 4px 12px rgba(76, 175, 80, 0.15)' 
                  : '0 4px 12px rgba(244, 67, 54, 0.15)'
              }
            }}>
              <Clock size={18} color={userData.profil_actif ? "#4CAF50" : "#F44336"} style={{ marginTop: '2px' }} />
              <Box>
                <Typography variant="body2" sx={{ 
                  fontWeight: 'bold', 
                  color: '#4A5568',
                  fontSize: '0.875rem'
                }}>
                  Etat du profil
                </Typography>
                <Typography variant="body2" color={userData.profil_actif ? "success.main" : "error.main"} sx={{ 
                  fontWeight: 'medium',
                  fontSize: '0.875rem'
                }}>
                  {userData.profil_actif ? 'Actif' : 'Inactif'}
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          {/* Mode vacances */}
          {userData.mode_vacance !== undefined && (
            <Grid size={12}>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'flex-start', 
                gap: 1.5,
                backgroundColor: userData.mode_vacance ? 'rgba(255, 193, 7, 0.08)' : 'rgba(76, 175, 80, 0.08)',
                padding: '12px 16px',
                borderRadius: '12px',
                border: `1px solid ${userData.mode_vacance ? 'rgba(255, 193, 7, 0.2)' : 'rgba(76, 175, 80, 0.2)'}`,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: userData.mode_vacance 
                    ? '0 4px 12px rgba(255, 193, 7, 0.15)' 
                    : '0 4px 12px rgba(76, 175, 80, 0.15)'
                }
              }}>
                <AlertTriangle size={18} color={userData.mode_vacance ? "#FFC107" : "#4CAF50"} style={{ marginTop: '2px' }} />
                <Box>
                  <Typography variant="body2" sx={{ 
                    fontWeight: 'bold', 
                    color: '#4A5568',
                    fontSize: '0.875rem'
                  }}>
                    Mode vacances
                  </Typography>
                  <Typography variant="body2" color={userData.mode_vacance ? "warning.main" : "success.main"} sx={{ 
                    fontWeight: 'medium',
                    fontSize: '0.875rem'
                  }}>
                    {userData.mode_vacance ? 'Activé' : 'Désactivé'}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>
      </Paper>
    );

    // Bio - Conditionnelle
    if (hasBio) {
      sections.push(
        <Paper key="bio" elevation={0} sx={{ 
          p: 1.5, // 12px
          borderRadius: '16px', 
          border: '1px solid #FFE4BA',
          backgroundColor: 'white',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: '#FFF8F3'
          }
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1.5, 
            mb: 3,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-8px',
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: '#FF6B2C',
              borderRadius: '2px'
            }
          }}>
            <User size={20} color="#FF6B2C" />
            <Typography variant="h6" sx={{ 
              color: '#2D3748', 
              fontWeight: 'bold', 
              fontSize: '1.1rem',
              letterSpacing: '-0.5px'
            }}>
              À propos
            </Typography>
          </Box>
          <Box 
            sx={{ 
              color: '#4A5568',
              lineHeight: 1.7,
              fontSize: '0.95rem',
              position: 'relative',
              pl: 3,
              '&::before': {
                content: '""',
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: '2px',
                backgroundColor: '#FFE4BA',
                borderRadius: '2px'
              }
            }}
            dangerouslySetInnerHTML={{ __html: userData.profil?.data?.bio || '' }}
          />
        </Paper>
      );
    }

    // Informations d'intervention - Conditionnelle
    if (hasInterventionInfo) {
      sections.push(
        <Paper key="intervention" elevation={0} sx={{ 
          p: 1.5, // 12px
          borderRadius: '16px', 
          border: '1px solid #FFE4BA',
          backgroundColor: 'white',
          transition: 'all 0.3s ease',
          width: '100%',
          '&:hover': {
            backgroundColor: '#FFF8F3'
          }
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1.5, 
            mb: 3,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-8px',
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: '#FF6B2C',
              borderRadius: '2px'
            }
          }}>
            <MapIcon size={20} color="#FF6B2C" />
            <Typography variant="h6" sx={{ 
              color: '#2D3748', 
              fontWeight: 'bold', 
              fontSize: '1.1rem',
              letterSpacing: '-0.5px'
            }}>
              Informations d'intervention
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {/* Zone d'intervention */}
            {userData.profil?.data?.intervention_zone && (
              <Grid size={12}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <MapIcon size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      Zone d'intervention
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem'
                    }}>
                      {userData.profil?.data?.intervention_zone.france_entiere 
                        ? 'France entière' 
                        : `Rayon de ${userData.profil?.data?.intervention_zone.radius} km autour de ${userData.profil?.data?.ville}`}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}

            {/* Téléphone */}
            {userData.profil?.data?.telephone && (
              <Grid size={12}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <Phone size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      Téléphone
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}>
                      {userData.profil?.data?.telephone_prive ? (
                        <>
                          <span>Numéro masqué</span>
                          <Lock size={14} color="#FF6B2C" />
                        </>
                      ) : (
                        userData.profil?.data?.telephone
                      )}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
          </Grid>
        </Paper>
      );
    }

    // Informations entreprise - Conditionnelle
    if (hasEnterpriseInfo) {
      sections.push(
        <Paper key="entreprise" elevation={0} sx={{ 
          p: 1.5, // 12px
          borderRadius: '16px', 
          border: '1px solid #FFE4BA',
          backgroundColor: 'white',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: '#FFF8F3'
          }
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1.5, 
            mb: 3,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-8px',
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: '#FF6B2C',
              borderRadius: '2px'
            }
          }}>
            <Building size={20} color="#FF6B2C" />
            <Typography variant="h6" sx={{ 
              color: '#2D3748', 
              fontWeight: 'bold', 
              fontSize: '1.1rem',
              letterSpacing: '-0.5px'
            }}>
              Informations entreprise
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {/* SIREN */}
            {userData.profil?.data?.siren_entreprise && (
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <Building size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      SIREN
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem'
                    }}>
                      {userData.profil?.data?.siren_entreprise}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
            
            {/* Code APE */}
            {userData.profil?.data?.code_ape_entreprise && (
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <FileText size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      Code APE
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem'
                    }}>
                      {userData.profil?.data?.code_ape_entreprise}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
            
            {/* Date de création INSEE */}
            {userData.profil?.data?.date_insee_creation_entreprise && (
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <Calendar size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      Date de création
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem'
                    }}>
                      {formatDate(userData.profil?.data?.date_insee_creation_entreprise)}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
            
            {/* Catégorie d'entreprise */}
            {userData.profil?.data?.categorie_entreprise && (
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'flex-start', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 107, 44, 0.2)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                  }
                }}>
                  <Briefcase size={18} color="#FF6B2C" style={{ marginTop: '2px' }} />
                  <Box>
                    <Typography variant="body2" sx={{ 
                      fontWeight: 'bold', 
                      color: '#4A5568',
                      fontSize: '0.875rem'
                    }}>
                      Catégorie
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontSize: '0.875rem'
                    }}>
                      {userData.profil?.data?.categorie_entreprise}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            )}
          </Grid>
        </Paper>
      );
    }

    // Services - Conditionnelle
    if (hasServices) {
      sections.push(
        <Paper key="services" elevation={0} sx={{ 
          p: 1.5, // 12px
          borderRadius: '16px', 
          border: '1px solid #FFE4BA',
          backgroundColor: 'white',
          transition: 'all 0.3s ease',
          '&:hover': {
            backgroundColor: '#FFF8F3'
          }
        }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1.5, 
            mb: 3,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-8px',
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: '#FF6B2C',
              borderRadius: '2px'
            }
          }}>
            <Briefcase size={20} color="#FF6B2C" />
            <Typography variant="h6" sx={{ 
              color: '#2D3748', 
              fontWeight: 'bold', 
              fontSize: '1.1rem',
              letterSpacing: '-0.5px'
            }}>
              Services proposés
            </Typography>
          </Box>
          <Box sx={{ maxHeight: '300px', overflowY: 'auto', pr: 1 }}>
            {userData.services?.map((service, index) => (
              <React.Fragment key={service.id}>
                <Box sx={{ 
                  mb: 2,
                  mt: index === 0 ? 1 : 0,
                  p: 2,
                  backgroundColor: '#FFF8F3',
                  borderRadius: '12px',
                  border: '1px solid #FFE4BA',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                    borderColor: '#FF6B2C'
                  }
                }}>
                  <Typography variant="subtitle2" sx={{ 
                    color: '#FF6B2C', 
                    fontWeight: 'bold',
                    fontSize: '0.9rem',
                    mb: 1
                  }}>
                    {service.titre}
                  </Typography>
                  <Typography variant="body2" sx={{ 
                    color: '#4A5568', 
                    mb: 1.5,
                    lineHeight: 1.6,
                    fontSize: '0.875rem'
                  }}>
                    {service.description.replace(/<[^>]*>/g, '')}
                  </Typography>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    gap: 0.5,
                    backgroundColor: 'white',
                    padding: '4px 8px',
                    borderRadius: '8px',
                    width: 'fit-content',
                    border: '1px solid #FFE4BA'
                  }}>
                    <Typography variant="subtitle2" sx={{ 
                      color: '#FF6B2C', 
                      fontWeight: 'bold',
                      fontSize: '0.875rem'
                    }}>
                      {service.tarif_horaire} €/heure
                    </Typography>
                  </Box>
                </Box>
                {userData.services && index < userData.services.length - 1 && (
                  <Divider sx={{ my: 2, borderColor: '#FFE4BA' }} />
                )}
              </React.Fragment>
            ))}
          </Box>
        </Paper>
      );
    }

    // Nouvelle section pour les badges
    sections.push(
      <Paper key="badges" elevation={0} sx={{ 
        p: 1.5,
        borderRadius: '16px', 
        border: '1px solid #FFE4BA',
        backgroundColor: 'white',
        transition: 'all 0.3s ease',
        '&:hover': {
          backgroundColor: '#FFF8F3'
        }
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1.5, 
          mb: 3,
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: '-8px',
            left: 0,
            width: '40px',
            height: '3px',
            backgroundColor: '#FF6B2C',
            borderRadius: '2px'
          }
        }}>
          <Award size={20} color="#FF6B2C" />
          <Typography variant="h6" sx={{ 
            color: '#2D3748', 
            fontWeight: 'bold', 
            fontSize: '1.1rem',
            letterSpacing: '-0.5px'
          }}>
            Badges
          </Typography>
        </Box>
        
        {/* Intégration du composant BadgesDisplay */}
        <Box>
          <BadgesDisplay profil={userData} />
        </Box>
      </Paper>
    );

    // Section Avis
    sections.push(
      <Paper key="avis" elevation={0} sx={{ 
        p: 1.5, // 12px
        borderRadius: '16px', 
        border: '1px solid #FFE4BA',
        backgroundColor: 'white',
        transition: 'all 0.3s ease',
        '&:hover': {
          backgroundColor: '#FFF8F3'
        }
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1.5, 
          mb: 3,
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: '-8px',
            left: 0,
            width: '40px',
            height: '3px',
            backgroundColor: '#FF6B2C',
            borderRadius: '2px'
          }
        }}>
          <Star size={20} color="#FF6B2C" />
          <Typography variant="h6" sx={{ 
            color: '#2D3748', 
            fontWeight: 'bold', 
            fontSize: '1.1rem',
            letterSpacing: '-0.5px'
          }}>
            Avis ({stats?.total_reviews || 0})
          </Typography>
          {user && user.id !== userData.id && (
            <Button
              variant="contained"
              startIcon={<Star size={18} />}
              onClick={() => setIsMissionSelectModalOpen(true)}
              sx={{
                ml: 'auto',
                backgroundColor: '#FF6B2C',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#FF965E'
                }
              }}
            >
              Déposer un avis
            </Button>
          )}
        </Box>
        <ReviewModalListeDesAvis
          userId={userData.id}
          userData={userData}
          currentUser={user}
          onEditReview={handleEditReview}
          onDeleteReview={handleDeleteReview}
          onOpenReviewerProfile={handleOpenReviewerProfile}
          onOpenMissionSelector={() => setIsMissionSelectModalOpen(true)}
          onStatsUpdate={handleStatsUpdate}
          refreshKey={refreshKey}
        />
      </Paper>
    );

    // Section Activités récentes
    if (hasActions) {
      sections.push(
        <Grid size={12} key="recent-actions">
          <Paper elevation={0} sx={{ 
            p: 1.5, // 12px
            borderRadius: '16px', 
            border: '1px solid #FFE4BA',
            backgroundColor: 'white',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: '#FFF8F3'
            }
          }}>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1.5, 
              mb: 3,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-8px',
                left: 0,
                width: '40px',
                height: '3px',
                backgroundColor: '#FF6B2C',
                borderRadius: '2px'
              }
            }}>
              <History size={20} color="#FF6B2C" />
              <Typography variant="h6" sx={{ 
                color: '#2D3748', 
                fontWeight: 'bold', 
                fontSize: '1.1rem',
                letterSpacing: '-0.5px'
              }}>
                Activités récentes
              </Typography>
            </Box>
            
            <Box className="px-2 mt-4">
              <RecentUserActions
                actions={actions}
                loading={actionsLoading}
                error={actionsError}
                variant="content-only"
                maxHeight={300}
              />
            </Box>
          </Paper>
        </Grid>
      );
    }

    return sections;
  };

  // Fonction pour obtenir toutes les photos (featured et gallery)
  const getAllPhotos = () => {
    const photos: {
      id: string;
      photo_url: string;
      caption?: string;
      folder?: string;
    }[] = [];

    // Ajouter les photos mises en avant
    if (userData.featuredPhotos && userData.featuredPhotos.length > 0) {
      photos.push(...userData.featuredPhotos.map(photo => ({
        id: photo.id,
        photo_url: photo.photo_url,
        caption: photo.caption,
        folder: 'Photos mises en avant'
      })));
    }

    // Ajouter les photos des galeries
    if (userData.galleryFolders && userData.galleryFolders.length > 0) {
      userData.galleryFolders.forEach(folder => {
        if (folder.photos && folder.photos.length > 0) {
          photos.push(...folder.photos.map(photo => ({
            id: photo.id,
            photo_url: photo.photo_url,
            caption: photo.caption,
            folder: 'Galerie ' + folder.name
          })));
        }
      });
    }

    return photos;
  };

  // Obtenir toutes les photos
  const allPhotos = getAllPhotos();

  // Fonction pour naviguer vers la photo précédente
  const goToPreviousPhoto = () => {
    if (!selectedPhoto || allPhotos.length <= 1) return;
    
    const currentIndex = allPhotos.findIndex(p => p.id === selectedPhoto.id);
    if (currentIndex > 0) {
      setSelectedPhoto(allPhotos[currentIndex - 1]);
    }
  };

  // Fonction pour naviguer vers la photo suivante
  const goToNextPhoto = () => {
    if (!selectedPhoto || allPhotos.length <= 1) return;
    
    const currentIndex = allPhotos.findIndex(p => p.id === selectedPhoto.id);
    if (currentIndex < allPhotos.length - 1) {
      setSelectedPhoto(allPhotos[currentIndex + 1]);
    }
  };

  const handleEditReview = async (reviewId: string): Promise<void> => {
    const review = await getReview(reviewId);
    if (review) {
      setSelectedReview(review);
      setSelectedMissionId(review.mission_id);
      setIsReviewModalOpen(true);
    }
  };

  const handleDeleteReview = async (reviewId: string): Promise<void> => {
    setReviewToDelete(reviewId);
    setIsDeleteConfirmationModalOpen(true);
  };

  const handleOpenReviewerProfile = async (reviewerId: string): Promise<void> => {
    try {
      const slugResponse = await axios.get(`${API_CONFIG.baseURL}/api/users/get-slug/${reviewerId}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil/${slugResponse.data.slug}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      if (response.data) {
        setSelectedReviewerProfile(response.data);
        setIsReviewerProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du profil:', error);
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  const handleContactUser = () => {
    // Stocker les informations de l'utilisateur dans le localStorage
    localStorage.setItem('newMessageInfo', JSON.stringify({
      recipientId: userData.id,
      recipientName: `${userData.profil?.data?.prenom} ${userData.profil?.data?.nom}`,
      initialMessage: `Bonjour ${userData.profil?.data?.prenom},`
    }));

    // Rediriger vers la page des messages
    navigate('/dashboard/messages');
  };

  const handleReviewSubmitted = (): void => {
    setRefreshKey(prev => prev + 1); // Force le rafraîchissement
    setIsReviewModalOpen(false);
    setSelectedMissionId(null);
    setSelectedReview(null);
  };

  const confirmDeleteReview = async () => {
    if (reviewToDelete) {
      const success = await deleteReview(reviewToDelete);
      if (success) {
        setRefreshKey(prev => prev + 1); // Force le rafraîchissement
        fetchReviews(1);
      }
      setIsDeleteConfirmationModalOpen(false);
      setReviewToDelete(null);
    }
  };

  const handleStatsUpdate = (newStats: { total_reviews: number; rating: number; completion_rate: number }) => {
    setStats(newStats);
  };

  const handleReportProfile = async (reason: string) => {
    setReportLoading(true);
    try {
      let profilId = (userData as any).profil_id;
      // Si on n'a pas l'id du profil, on le récupère via l'API (comme dans Profile.tsx)
      if (!profilId) {
        let endpoint = '/api/users/profil';
        if (userData.profil?.data?.slug) {
          endpoint = `/api/users/profil/${userData.profil.data.slug}`;
        }
        const response = await axios.get(endpoint, API_CONFIG);
        profilId = response.data?.profil?.data?.id;
      }
      if (!profilId) {
        notify("Impossible de récupérer l'identifiant du profil à signaler.", 'error');
        setReportLoading(false);
        return;
      }
      const headers = await getCommonHeaders();
      await axios.post(
        `${API_CONFIG.baseURL}/api/reported-content`,
        {
          content_type: 'profile',
          content_id: profilId,
          reason
        },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      notify('Signalement envoyé, merci pour votre vigilance.', 'success');
      setReportModalOpen(false);
    } catch (error: any) {
      notify(error?.response?.data?.message || 'Erreur lors du signalement', 'error');
    } finally {
      setReportLoading(false);
    }
  };

  // GESTION MASQUÉ : tout en haut, AVANT TOUT !
  if ((userData as any).masqué) {
    return (
      <ModalPortal isOpen={isOpen} onBackdropClick={onClose}>
        <Box sx={{ p: 4, bgcolor: '#FFF8F3', border: '2px solid #FF6B2C', borderRadius: 3, textAlign: 'center', maxWidth: 480, m: 'auto' }}>
          <Typography variant="h5" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 2 }}>
            Profil en attente de modération
          </Typography>
          <Typography variant="body1" sx={{ color: '#222', mb: 2 }}>
            {(userData as any).message || "Ce profil est en cours de modération et n'est pas visible actuellement."}
          </Typography>
          <Typography variant="body2" sx={{ color: '#BDBDBD' }}>
            Merci de votre compréhension.
          </Typography>
          <Button onClick={onClose} sx={{ mt: 3, bgcolor: '#FF6B2C', color: 'white', '&:hover': { bgcolor: '#FF7A35' } }}>
            Fermer
          </Button>
        </Box>
      </ModalPortal>
    );
  }

  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={onClose} zIndex={zIndex}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        style={{
          position: 'fixed',
          backgroundColor: 'white',
          borderRadius: '20px',
          padding: '0',
          maxWidth: '900px',
          width: '95%',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
          zIndex: 1100,
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 107, 44, 0.1)'
        }}
      >
        {/* Contenu du modal avec défilement */}
        <Box sx={{ 
          flex: 1,
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(255, 107, 44, 0.04)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(255, 107, 44, 0.2)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: 'rgba(255, 107, 44, 0.3)',
            },
          },
        }}>
          {/* Bouton de fermeture (croix) */}
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              top: '20px',
              right: '20px',
              color: '#FF6B2C',
              padding: '10px',
              zIndex: 1200,
              backgroundColor: 'rgba(255, 107, 44, 0.05)',
              transition: 'all 0.3s ease',
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.1)',
                transform: 'rotate(90deg)',
                color: '#FF965E',
              }
            }}
            aria-label="Fermer"
          >
            <X size={24} />
          </IconButton>

          {/* En-tête du profil avec fond coloré */}
          <Box sx={{ 
            backgroundColor: '#FFF8F3', 
            borderTopLeftRadius: '20px', 
            borderTopRightRadius: '20px',
            p: 3,
            pb: 4,
            position: 'relative',
            background: 'linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%)'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
              <Avatar
                src={userData.profil?.data?.photo_url}
                alt={`${userData.profil?.data?.prenom} ${userData.profil?.data?.nom}`}
                sx={{ 
                  width: 90, 
                  height: 90, 
                  border: '3px solid white',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.15)'
                  }
                }}
              />
              <Box>
                <Typography variant="h5" sx={{ 
                  color: '#2D3748', 
                  fontWeight: 'bold', 
                  mb: 1,
                  letterSpacing: '-0.5px',
                  fontSize: '1.5rem'
                }}>
                  {formatName()}
                </Typography>
                
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1.5, 
                  mb: 1,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '6px 12px',
                  borderRadius: '10px',
                  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 3px 8px rgba(0, 0, 0, 0.1)'
                  }
                }}>
                  <MapPin size={16} color="#FF6B2C" />
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                    {userData.profil?.data?.ville}, {userData.profil?.data?.pays}
                  </Typography>
                </Box>
                
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1.5, 
                  mb: 1,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '6px 12px',
                  borderRadius: '10px',
                  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 3px 8px rgba(0, 0, 0, 0.1)'
                  }
                }}>
                  <Briefcase size={16} color="#FF6B2C" />
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                    {userData.profil?.data?.type_de_profil === 'entreprise' ? 'Entreprise' : 
                     userData.profil?.data?.type_de_profil === 'particulier' ? 'Particulier' : 
                     'Type de profil non spécifié'}
                    {userData.profil?.data?.nom_entreprise && ` - ${userData.profil?.data?.nom_entreprise}`}
                  </Typography>
                </Box>
                
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1.5,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  padding: '6px 12px',
                  borderRadius: '10px',
                  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 3px 8px rgba(0, 0, 0, 0.1)'
                  }
                }}>
                  <Calendar size={16} color="#FF6B2C" />
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                    Membre depuis {formatDate(userData.date_inscription)}
                  </Typography>
                </Box>
              </Box>
            </Box>
            
            {/* Statut en ligne et mode vacances */}
            <Box sx={{ 
              display: 'flex', 
              gap: 1.5, 
              position: 'absolute', 
              top: '20px', 
              right: '80px' 
            }}>
              {userData.is_online && !userData.mode_vacance && (
                <Chip
                  size="small"
                  label="En ligne"
                  sx={{
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    fontWeight: 'bold',
                    height: '32px',
                    padding: '0 12px',
                    boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(76, 175, 80, 0.4)'
                    }
                  }}
                />
              )}
              {userData.mode_vacance && (
                <Chip
                  size="small"
                  label="En vacances"
                  sx={{
                    backgroundColor: '#FFC107',
                    color: 'white',
                    fontWeight: 'bold',
                    height: '32px',
                    padding: '0 12px',
                    boxShadow: '0 2px 8px rgba(255, 193, 7, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 193, 7, 0.4)'
                    }
                  }}
                />
              )}
            </Box>

            {/* Lien vers le profil public et actions */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                justifyContent: 'center',
                alignItems: 'center',
                mt: 3,
                mb: 2,
                width: '100%',
                '& > a, & > button': {
                  flex: 1,
                  minWidth: { xs: '100%', sm: 'auto' },
                  textAlign: 'center',
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '0.85rem',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
                  textTransform: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  display: 'inline-block',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.4)'
                  }
                },
                '& > button.outlined': {
                  backgroundColor: 'white',
                  color: '#FF6B2C',
                  border: '1.5px solid #FF6B2C',
                  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.08)',
                  '&:hover': {
                    backgroundColor: '#FFF8F3',
                    color: '#FF965E',
                    borderColor: '#FF965E',
                  }
                }
              }}
            >
              <a
                href={`/dashboard/profil/${userData.profil?.data?.slug}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                Voir le profil complet
              </a>
              <Button
                variant="contained"
                onClick={handleContactUser}
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  fontWeight: 'bold',
                  boxShadow: 'none',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  width: { xs: '100%', sm: 'auto' }
                }}
              >
                Contacter
              </Button>
              <Button
                variant="outlined"
                className="outlined"
                sx={{
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  backgroundColor: 'white',
                  fontWeight: 'bold',
                  '&:hover': {
                    backgroundColor: '#FFF8F3',
                    borderColor: '#FF965E',
                    color: '#FF965E',
                  },
                  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.08)',
                  width: { xs: '100%', sm: 'auto' }
                }}
                onClick={() => setReportModalOpen(true)}
              >
                Signaler le profil
              </Button>
            </Box>
          </Box>

          {/* Contenu principal */}
          <Box sx={{ 
            p: { 
              xs: 1, // 4px sur mobile
              sm: 1, // 4px sur tablette
              md: 4  // 32px sur desktop
            } 
          }}>
            {/* Badges de vérification */}
            <Box sx={{ 
              mb: 4, 
              display: 'flex', 
              justifyContent: 'center', 
              flexWrap: 'wrap', 
              gap: 2,
              px: 2
            }}>
              {userData.email_verifier && (
                <Chip
                  icon={<CheckCircle size={18} />}
                  label="Email vérifié"
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    height: '36px',
                    padding: '0 16px',
                    '& .MuiChip-icon': {
                      color: '#FF6B2C',
                      marginLeft: '8px'
                    },
                    '& .MuiChip-label': {
                      padding: '0 8px',
                      fontSize: '0.9rem'
                    },
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 107, 44, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.12)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
              )}
              {userData.profil_verifier && (
                <Chip
                  icon={<CheckCircle size={18} />}
                  label="Profil vérifié"
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    height: '36px',
                    padding: '0 16px',
                    '& .MuiChip-icon': {
                      color: '#FF6B2C',
                      marginLeft: '8px'
                    },
                    '& .MuiChip-label': {
                      padding: '0 8px',
                      fontSize: '0.9rem'
                    },
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 107, 44, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.12)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
              )}
              {userData.identite_verifier && (
                <Chip
                  icon={<Shield size={18} />}
                  label="Identité vérifiée"
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    height: '36px',
                    padding: '0 16px',
                    '& .MuiChip-icon': {
                      color: '#FF6B2C',
                      marginLeft: '8px'
                    },
                    '& .MuiChip-label': {
                      padding: '0 8px',
                      fontSize: '0.9rem'
                    },
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 107, 44, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.12)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
              )}
              {userData.entreprise_verifier && (
                <Chip
                  icon={<Award size={18} />}
                  label="Entreprise vérifiée"
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    height: '36px',
                    padding: '0 16px',
                    '& .MuiChip-icon': {
                      color: '#FF6B2C',
                      marginLeft: '8px'
                    },
                    '& .MuiChip-label': {
                      padding: '0 8px',
                      fontSize: '0.9rem'
                    },
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 107, 44, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.12)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
              )}
              {userData.assurance_verifier && (
                <Chip
                  icon={<Shield size={18} />}
                  label="Assurance vérifiée"
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    height: '36px',
                    padding: '0 16px',
                    '& .MuiChip-icon': {
                      color: '#FF6B2C',
                      marginLeft: '8px'
                    },
                    '& .MuiChip-label': {
                      padding: '0 8px',
                      fontSize: '0.9rem'
                    },
                    transition: 'all 0.3s ease',
                    border: '1px solid rgba(255, 107, 44, 0.2)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.12)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
              )}
            </Box>

            {/* Layout Grid pour les sections d'informations */}
            <Box sx={{ 
              width: '100%', 
              mb: 4,
              px: { xs: 0, sm: 1 }
            }}>
              {renderSections().length > 0 && (
                <Grid container spacing={2}>
                  {renderSections().map((section, index) => (
                    <Grid size={12} key={index}>
                      {section}
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>

            {/* Galerie photos - Conditionnelle, toujours en pleine largeur */}
            {hasGallery && (
              <Paper elevation={0} sx={{ p: 2.5, mb: 3, borderRadius: '12px', border: '1px solid #FFE4BA' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2 }}>
                  <FileText size={20} color="#FF6B2C" />
                  <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold', fontSize: '1.1rem' }}>
                    Galerie photos
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  {userData.featuredPhotos?.map((photo) => (
                    <Grid size={{ xs: 6, sm: 4, md: 3 }} key={photo.id}>
                      <Box 
                        sx={{ 
                          position: 'relative', 
                          borderRadius: '10px', 
                          overflow: 'hidden', 
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          cursor: 'pointer',
                          '&:hover img': {
                            transform: 'scale(1.05)'
                          },
                          '&:hover .caption': {
                            opacity: 1
                          }
                        }}
                        onClick={() => setSelectedPhoto({
                          id: photo.id,
                          photo_url: photo.photo_url,
                          caption: photo.caption,
                          folder: 'Photos mises en avant'
                        })}
                      >
                        <Box
                          component="img"
                          src={photo.photo_url}
                          alt={photo.caption || 'Photo'}
                          sx={{
                            width: '100%',
                            height: '140px',
                            objectFit: 'cover',
                            transition: 'transform 0.3s ease'
                          }}
                        />
                        <Box 
                          className="caption"
                          sx={{ 
                            position: 'absolute', 
                            bottom: 0, 
                            left: 0, 
                            right: 0, 
                            backgroundColor: 'rgba(0,0,0,0.6)', 
                            padding: '8px',
                            textAlign: 'center',
                            opacity: 0,
                            transition: 'opacity 0.3s ease'
                          }}
                        >
                          <Typography variant="caption" sx={{ color: 'white' }}>
                            {photo.caption || 'Cliquez pour agrandir'}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                  {userData.galleryFolders?.map((folder) =>
                    folder.photos.map((photo) => (
                      <Grid size={{ xs: 6, sm: 4, md: 3 }} key={photo.id}>
                        <Box 
                          sx={{ 
                            position: 'relative', 
                            borderRadius: '10px', 
                            overflow: 'hidden', 
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                            cursor: 'pointer',
                            '&:hover img': {
                              transform: 'scale(1.05)'
                            },
                            '&:hover .caption': {
                              opacity: 1
                            }
                          }}
                          onClick={() => setSelectedPhoto({
                            id: photo.id,
                            photo_url: photo.photo_url,
                            caption: photo.caption,
                            folder: folder.name
                          })}
                        >
                          <Box
                            component="img"
                            src={photo.photo_url}
                            alt={photo.caption || folder.name}
                            sx={{
                              width: '100%',
                              height: '140px',
                              objectFit: 'cover',
                              transition: 'transform 0.3s ease'
                            }}
                          />
                          <Box 
                            className="caption"
                            sx={{ 
                              position: 'absolute', 
                              bottom: 0, 
                              left: 0, 
                              right: 0, 
                              backgroundColor: 'rgba(0,0,0,0.6)', 
                              padding: '8px',
                              textAlign: 'center',
                              opacity: 0,
                              transition: 'opacity 0.3s ease'
                            }}
                          >
                            <Typography variant="caption" sx={{ color: 'white' }}>
                              {photo.caption || 'Cliquez pour agrandir'}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    ))
                  )}
                </Grid>
              </Paper>
            )}
          </Box>
        </Box>

        {/* Bouton Fermer fixe en bas */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'flex-end', 
          p: 2,
          borderTop: '1px solid rgba(255, 107, 44, 0.1)',
          backgroundColor: 'white'
        }}>
          <button
            onClick={onClose}
            style={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              border: 'none',
              padding: '12px 28px',
              borderRadius: '12px',
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: '0.95rem',
              transition: 'all 0.3s ease',
              boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
            onMouseOver={(e) => {
              e.currentTarget.style.backgroundColor = '#FF965E';
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 6px 16px rgba(255, 107, 44, 0.4)';
            }}
            onMouseOut={(e) => {
              e.currentTarget.style.backgroundColor = '#FF6B2C';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 107, 44, 0.3)';
            }}
          >
            <X size={18} />
            Fermer
          </button>
        </Box>
      </motion.div>

      {/* Modal pour afficher une photo en grand */}
      {selectedPhoto && (
        <ModalPortal isOpen={!!selectedPhoto} onBackdropClick={() => setSelectedPhoto(null)}>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-[9999] p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] relative max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <IconButton
                onClick={() => setSelectedPhoto(null)}
                sx={{
                  position: 'absolute',
                  top: '16px',
                  right: '16px',
                  color: '#FF6B2C',
                  zIndex: 10,
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    transform: 'scale(1.1)'
                  },
                  transition: 'all 0.2s'
                }}
              >
                <X size={24} />
              </IconButton>

              <Typography variant="h4" sx={{ 
                fontWeight: 'bold', 
                mb: 3, 
                textAlign: 'center', 
                color: '#FF6B2C',
                mt: 1
              }}>
                {selectedPhoto.caption || 'Photo'}
              </Typography>

              <Box sx={{ position: 'relative', mb: 4 }}>
                <Box sx={{ 
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: '12px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  height: { xs: '300px', sm: '400px' },
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#000'
                }}>
                  <Box
                    component="img"
                    src={selectedPhoto.photo_url}
                    alt={selectedPhoto.caption || 'Photo'}
                    sx={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain'
                    }}
                  />

                  {allPhotos.length > 1 && (
                    <>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          goToPreviousPhoto();
                        }}
                        disabled={allPhotos.findIndex(p => p.id === selectedPhoto.id) === 0}
                        sx={{
                          position: 'absolute',
                          left: '8px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          backgroundColor: 'rgba(255, 255, 255, 0.3)',
                          color: 'white',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.5)',
                          },
                          opacity: allPhotos.findIndex(p => p.id === selectedPhoto.id) === 0 ? 0.5 : 1,
                          cursor: allPhotos.findIndex(p => p.id === selectedPhoto.id) === 0 ? 'not-allowed' : 'pointer'
                        }}
                      >
                        <ChevronLeft size={24} />
                      </IconButton>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          goToNextPhoto();
                        }}
                        disabled={allPhotos.findIndex(p => p.id === selectedPhoto.id) === allPhotos.length - 1}
                        sx={{
                          position: 'absolute',
                          right: '8px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          backgroundColor: 'rgba(255, 255, 255, 0.3)',
                          color: 'white',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 0.5)',
                          },
                          opacity: allPhotos.findIndex(p => p.id === selectedPhoto.id) === allPhotos.length - 1 ? 0.5 : 1,
                          cursor: allPhotos.findIndex(p => p.id === selectedPhoto.id) === allPhotos.length - 1 ? 'not-allowed' : 'pointer'
                        }}
                      >
                        <ChevronRight size={24} />
                      </IconButton>
                    </>
                  )}
                </Box>

                {allPhotos.length > 1 && (
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    mt: 2,
                    gap: 0.5
                  }}>
                    {allPhotos.map((photo) => (
                      <Box
                        key={photo.id}
                        onClick={() => setSelectedPhoto(photo)}
                        sx={{
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          backgroundColor: photo.id === selectedPhoto.id ? '#FF6B2C' : 'rgba(0, 0, 0, 0.2)',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            backgroundColor: photo.id === selectedPhoto.id ? '#FF6B2C' : 'rgba(0, 0, 0, 0.4)',
                          }
                        }}
                      />
                    ))}
                  </Box>
                )}
              </Box>

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper elevation={0} sx={{ p: 3, borderRadius: '12px', backgroundColor: 'white', height: '100%' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2, position: 'relative' }}>
                      <Info size={20} color="#FF6B2C" />
                      <Typography variant="h6" sx={{ 
                        color: '#2D3748', 
                        fontWeight: 'bold', 
                        fontSize: '1.1rem',
                        position: 'relative',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: '-8px',
                          left: 0,
                          width: '40px',
                          height: '3px',
                          backgroundColor: '#FF6B2C',
                          borderRadius: '2px'
                        }
                      }}>
                        Informations
                      </Typography>
                    </Box>
                    <Box sx={{ color: '#4A5568' }}>
                      <Typography variant="body1" sx={{ mb: 1 }}>
                        {selectedPhoto.folder || 'Galerie photos'}
                      </Typography>
                      <Divider sx={{ my: 1.5 }} />
                      <Typography variant="body2" sx={{ 
                        color: '#4A5568',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '250px'
                      }}>
                        {selectedPhoto.caption || 'Aucune légende'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Paper elevation={0} sx={{ p: 3, borderRadius: '12px', backgroundColor: 'white', height: '100%' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2, position: 'relative' }}>
                      <User size={20} color="#FF6B2C" />
                      <Typography variant="h6" sx={{ 
                        color: '#2D3748', 
                        fontWeight: 'bold', 
                        fontSize: '1.1rem',
                        position: 'relative',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: '-8px',
                          left: 0,
                          width: '40px',
                          height: '3px',
                          backgroundColor: '#FF6B2C',
                          borderRadius: '2px'
                        }
                      }}>
                        Profil
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        src={userData.profil?.data?.photo_url}
                        alt={`${userData.profil?.data?.prenom} ${userData.profil?.data?.nom}`}
                        sx={{ width: 50, height: 50, border: '2px solid white', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}
                      />
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {formatName()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {userData.profil?.data?.type_de_profil === 'entreprise' ? 'Entreprise' : 
                           userData.profil?.data?.type_de_profil === 'particulier' ? 'Particulier' : 
                           'Non spécifié'}
                          {userData.profil?.data?.nom_entreprise && ` - ${userData.profil?.data?.nom_entreprise}`}
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'flex-end', 
                mt: 4,
                width: '100%'
              }}>
                <button
                    onClick={onClose}
                    style={{
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    border: 'none',
                    padding: '12px 28px',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    fontSize: '0.95rem',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = '#FF965E';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 6px 16px rgba(255, 107, 44, 0.4)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = '#FF6B2C';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 107, 44, 0.3)';
                  }}
                >
                  <X size={18} />
                  Fermer
                </button>
              </Box>
            </motion.div>
          </motion.div>
        </ModalPortal>
      )}

      {/* Modal du profil de l'utilisateur qui a déposé l'avis */}
      {selectedReviewerProfile && (
        <UserProfileModal
          isOpen={isReviewerProfileModalOpen}
          onClose={() => setIsReviewerProfileModalOpen(false)}
          userData={selectedReviewerProfile}
        />
      )}

      {/* Modal de confirmation de suppression */}
      <ModalPortal isOpen={isDeleteConfirmationModalOpen} onBackdropClick={() => setIsDeleteConfirmationModalOpen(false)}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          style={{
            position: 'fixed',
            backgroundColor: 'white',
            borderRadius: '16px',
            padding: '0',
            maxWidth: '500px',
            width: '95%',
            maxHeight: '90vh',
            overflowY: 'auto',
            boxShadow: '0px 11px 15px -7px rgba(0,0,0,0.2), 0px 24px 38px 3px rgba(0,0,0,0.14), 0px 9px 46px 8px rgba(0,0,0,0.12)',
            zIndex: 1100
          }}
        >
          <Box sx={{ p: 2.5, borderBottom: '1px solid #FFE4BA', position: 'relative' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <AlertTriangle size={24} color="#FF3B30" />
              <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold', fontSize: '1.1rem' }}>
                Confirmer la suppression
              </Typography>
            </Box>
            <IconButton
              onClick={() => setIsDeleteConfirmationModalOpen(false)}
              sx={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                color: '#FF6B2C',
                padding: '8px',
                '&:hover': {
                  backgroundColor: 'transparent',
                  color: '#FF965E',
                }
              }}
              aria-label="Fermer"
            >
              <X size={24} />
            </IconButton>
          </Box>

          <Box sx={{ p: 3 }}>
            <Typography variant="body1" sx={{ color: '#4A5568', mb: 2 }}>
              Êtes-vous sûr de vouloir supprimer cet avis ? Cette action est irréversible et vous perdrez 1 Jobi.
            </Typography>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center',
              gap: 1.5,
              backgroundColor: '#FFF8F3',
              padding: '12px 16px',
              borderRadius: '12px',
              border: '1px solid #FFE4BA',
              mb: 3
            }}>
              <Info size={20} color="#FF6B2C" />
              <Typography variant="body2" sx={{ 
                color: '#2D3748',
                fontWeight: 'medium',
                fontSize: '0.875rem'
              }}>
                La suppression d'un avis coûte 1 Jobi pour éviter les abus
              </Typography>
            </Box>
          </Box>

          <Box sx={{ 
            p: 2.5, 
            borderTop: '1px solid #FFE4BA',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2
          }}>
            <Button 
              onClick={() => setIsDeleteConfirmationModalOpen(false)}
              variant="outlined"
              sx={{ 
                color: '#FF6B2C',
                borderColor: '#FF6B2C',
                padding: '8px 16px',
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 'bold',
                fontSize: '0.875rem',
                '&:hover': {
                  borderColor: '#FF965E',
                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                }
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={confirmDeleteReview}
              variant="contained"
              startIcon={<X size={18} />}
              sx={{
                backgroundColor: '#FF3B30',
                color: 'white',
                padding: '8px 16px',
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 'bold',
                fontSize: '0.875rem',
                boxShadow: '0 2px 8px rgba(255, 59, 48, 0.3)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: '#FF6B6B',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 12px rgba(255, 59, 48, 0.4)'
                },
                '& .MuiButton-startIcon': {
                  marginRight: '8px'
                }
              }}
            >
              Supprimer l'avis
            </Button>
          </Box>
        </motion.div>
      </ModalPortal>

      {/* Ajouter le ModalReview */}
      <ModalReview
        isOpen={isMissionSelectModalOpen}
        onClose={() => setIsMissionSelectModalOpen(false)}
        profilId={userData.id}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Ajouter le ReviewModalPopupDepotAvis */}
      <ReviewModalPopupDepotAvis
        isOpen={isReviewModalOpen}
        onClose={() => {
          setIsReviewModalOpen(false);
          setSelectedReview(null);
        }}
        userId={userData.id}
        mission_id={selectedMissionId || ''}
        onReviewAdded={handleReviewSubmitted}
        reviewToEdit={selectedReview ? {
          id: selectedReview.id,
          note: selectedReview.note,
          commentaire: selectedReview.commentaire,
          qualites: selectedReview.qualites,
          defauts: selectedReview.defauts || []
        } : undefined}
      />

      <ReportProfileModal open={reportModalOpen} onClose={() => setReportModalOpen(false)} onSubmit={handleReportProfile} loading={reportLoading} photos={userData.featuredPhotos?.map(p => ({ id: p.id, url: p.photo_url, label: p.caption || '' })) || []} />
    </ModalPortal>
  );
};

export default UserProfileModal; 