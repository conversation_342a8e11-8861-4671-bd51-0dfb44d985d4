import { Plugin } from 'vite';
import { ImageOptimizer } from './src/utils/imageOptimizer/index.js';
import path from 'path';
import { logger } from 'vite';

export function imageOptimizePlugin(): Plugin {
    return {
        name: 'vite-plugin-image-optimize',
        async buildStart() {
            logger.info('🖼️ Optimisation des images pendant le build...');
            try {
                const optimizer = new ImageOptimizer();
                // Utiliser le chemin relatif au frontend
                const publicImagesDir = path.join(process.cwd(), 'public/images');
                await optimizer.optimizeAll();
            } catch (error) {
                logger.error('❌ Erreur lors de l\'optimisation des images:', error);
            }
        }
    };
}
