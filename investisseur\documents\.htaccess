# Protection du fichier de configuration
<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Redirection vers la page de connexion pour tous les fichiers sauf login.php, index.php, process_access_request.php, download.php et les documents PDF/PowerPoint
RewriteEngine On
RewriteCond %{REQUEST_URI} !login\.php$
RewriteCond %{REQUEST_URI} !index\.php$
RewriteCond %{REQUEST_URI} !process_access_request\.php$
RewriteCond %{REQUEST_URI} !download\.php$
RewriteCond %{REQUEST_URI} !README\.md$
RewriteCond %{REQUEST_URI} !Pitch\ Deck\.pdf$
RewriteCond %{REQUEST_URI} !Pitch\ Deck\.pptx$
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ login.php [L,R=302]