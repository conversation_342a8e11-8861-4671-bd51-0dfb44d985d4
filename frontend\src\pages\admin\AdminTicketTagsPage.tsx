import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Alert,
  useTheme,
  Tooltip,
  Chip,
  Skeleton,
  Fade,
  Zoom,
  CircularProgress,
  InputAdornment,
  Badge,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ViewColumn as TagIcon,
  Refresh as RefreshIcon,
  Label as LabelIcon,
  ColorLens as ColorLensIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import supportTicketService, { Tag, CreateTagDto } from '../../services/supportTicketService';
import { logger } from '../../utils/logger';

// Couleurs prédéfinies pour notre sélecteur (les mêmes que dans TagSelector)
const PREDEFINED_COLORS = [
  '#FF6B2C', // Couleur primaire du thème
  '#FF7A35', // Couleur secondaire du thème
  '#FF965E', // Couleur tertiaire du thème
  '#FFE4BA', // Couleur accent du thème
  '#E91E63', // Pink
  '#9C27B0', // Purple
  '#673AB7', // Deep Purple
  '#3F51B5', // Indigo
  '#2196F3', // Blue
  '#03A9F4', // Light Blue
  '#00BCD4', // Cyan
  '#009688', // Teal
  '#4CAF50', // Green
  '#8BC34A', // Light Green
  '#CDDC39', // Lime
  '#FFEB3B', // Yellow
  '#FFC107', // Amber
  '#FF9800', // Orange
  '#FF5722', // Deep Orange
  '#795548', // Brown
  '#9E9E9E', // Grey
  '#607D8B', // Blue Grey
];

const AdminTicketTagsPage: React.FC = () => {
  const theme = useTheme();
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newTag, setNewTag] = useState<CreateTagDto>({
    name: '',
    color: '#FF6B2C',
  });

  // Récupérer tous les tags
  const fetchTags = async () => {
    try {
      setLoading(true);
      setError(null);
      // Utiliser le service pour récupérer les tags
      const tagsData = await supportTicketService.getTags();
      setTags(tagsData || []);
    } catch (error) {
      logger.error('Erreur lors de la récupération des tags:', error);
      setError('Impossible de récupérer les tags. Veuillez réessayer plus tard.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  const handleOpenDialog = (tag?: Tag) => {
    if (tag) {
      setEditingTag(tag);
      setNewTag({ name: tag.name, color: tag.color });
    } else {
      setEditingTag(null);
      setNewTag({ name: '', color: '#FF6B2C' });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingTag(null);
    setNewTag({ name: '', color: '#FF6B2C' });
  };

  const handleSaveTag = async () => {
    try {
      if (!newTag.name.trim()) {
        setError('Le nom du tag ne peut pas être vide');
        return;
      }

      setLoading(true);
      setError(null);
      
      if (editingTag) {
        // Mise à jour du tag existant
        await supportTicketService.updateTag(editingTag.id, newTag);
        setSuccess(`Le tag "${newTag.name}" a été mis à jour avec succès.`);
      } else {
        // Création d'un nouveau tag
        await supportTicketService.createTag(newTag);
        setSuccess(`Le tag "${newTag.name}" a été créé avec succès.`);
      }
      
      await fetchTags();
      handleCloseDialog();
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde du tag:', error);
      setError('Une erreur est survenue lors de la sauvegarde du tag. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTag = async (tagId: string, tagName: string) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer le tag "${tagName}" ?`)) return;
    
    try {
      setLoading(true);
      setError(null);
      await supportTicketService.deleteTag(tagId);
      setSuccess(`Le tag "${tagName}" a été supprimé avec succès.`);
      await fetchTags();
    } catch (error) {
      logger.error(`Erreur lors de la suppression du tag ${tagId}:`, error);
      setError('Une erreur est survenue lors de la suppression du tag. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const handleColorChange = (color: string) => {
    setNewTag({ ...newTag, color });
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: '#FFF8F3', minHeight: '100vh' }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 4,
            gap: 2,
            position: 'relative',
            pl: 1
          }}
        >
          <LabelIcon
            sx={{
              fontSize: 36,
              color: '#FF6B2C',
              mr: 1
            }}
          />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              color: '#FF6B2C',
              fontWeight: 700,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: '40px',
                height: '3px',
                backgroundColor: '#FF965E',
                borderRadius: '8px'
              }
            }}
          >
            Gestion des Tags
          </Typography>
          {loading && (
            <Fade in={loading}>
              <CircularProgress 
                size={24} 
                sx={{ ml: 2, color: '#FF6B2C' }} 
              />
            </Fade>
          )}
        </Box>

        {/* Messages d'alerte */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mb: 3, 
              border: '1px solid #f44336',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
            }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}

        {success && (
          <Alert 
            severity="success" 
            sx={{ 
              mb: 3, 
              border: '1px solid #4caf50',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
            }}
            onClose={() => setSuccess(null)}
          >
            {success}
          </Alert>
        )}

        {/* Barre d'outils */}
        <Paper 
          elevation={2} 
          sx={{ 
            p: 3, 
            mb: 4, 
            borderRadius: '12px',
            background: 'linear-gradient(135deg, #ffffff 0%, #fff8f3 100%)',
            boxShadow: '0 4px 20px rgba(255, 107, 44, 0.05)'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 6, md: 6 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f1b18', display: 'flex', alignItems: 'center' }}>
                <TagIcon sx={{ mr: 1, color: '#FF965E' }} /> Liste des tags disponibles
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Utilisez ces tags pour organiser et catégoriser les tickets de support.
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 6 }} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchTags}
                disabled={loading}
                sx={{
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF965E',
                    backgroundColor: 'rgba(255, 107, 44, 0.05)',
                  },
                  borderRadius: '8px',
                  py: 1,
                }}
              >
                Actualiser
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
                disabled={loading}
                sx={{
                  backgroundColor: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  borderRadius: '8px',
                  color: 'white',
                  fontWeight: 600,
                  py: 1,
                  boxShadow: '0 4px 8px rgba(255, 107, 44, 0.25)',
                }}
              >
                Nouveau tag
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Badge avec le nombre de tags */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Badge 
            badgeContent={tags.length} 
            color="primary"
            sx={{ 
              '& .MuiBadge-badge': { 
                backgroundColor: '#FF7A35',
                color: 'white',
                fontWeight: 'bold',
              } 
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f1b18', display: 'flex', alignItems: 'center' }}>
              <LabelIcon sx={{ mr: 1, color: '#FF965E' }} /> Tags disponibles
            </Typography>
          </Badge>
        </Box>

        {/* Liste des tags */}
        <Paper 
          elevation={3} 
          sx={{ 
            width: '100%', 
            overflow: 'hidden',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
          }}
        >
          <TableContainer sx={{ maxHeight: '70vh' }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell 
                    width="15%" 
                    sx={{ 
                      backgroundColor: '#FFE4BA', 
                      fontWeight: 'bold',
                      color: '#1f1b18' 
                    }}
                  >
                    Aperçu
                  </TableCell>
                  <TableCell 
                    width="35%" 
                    sx={{ 
                      backgroundColor: '#FFE4BA', 
                      fontWeight: 'bold',
                      color: '#1f1b18' 
                    }}
                  >
                    Nom
                  </TableCell>
                  <TableCell 
                    width="30%" 
                    sx={{ 
                      backgroundColor: '#FFE4BA', 
                      fontWeight: 'bold',
                      color: '#1f1b18' 
                    }}
                  >
                    Couleur
                  </TableCell>
                  <TableCell 
                    width="20%" 
                    align="right" 
                    sx={{ 
                      backgroundColor: '#FFE4BA', 
                      fontWeight: 'bold',
                      color: '#1f1b18' 
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading && tags.length === 0 && (
                  // Skeletons de chargement
                  Array.from(new Array(5)).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell>
                        <Skeleton animation="wave" height={40} width={80} variant="rounded" />
                      </TableCell>
                      <TableCell>
                        <Skeleton animation="wave" height={40} />
                      </TableCell>
                      <TableCell>
                        <Skeleton animation="wave" height={40} />
                      </TableCell>
                      <TableCell align="right">
                        <Skeleton animation="wave" height={40} width={120} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
                
                {!loading && tags.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Box sx={{ py: 6, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <LabelIcon sx={{ fontSize: 60, color: '#FF965E', opacity: 0.7 }} />
                        </motion.div>
                        <Typography variant="h6" sx={{ color: '#666', fontWeight: 500 }}>
                          Aucun tag disponible pour le moment.
                        </Typography>
                        <Button 
                          variant="outlined" 
                          startIcon={<AddIcon />} 
                          onClick={() => handleOpenDialog()}
                          sx={{
                            mt: 2,
                            borderColor: '#FF6B2C',
                            color: '#FF6B2C',
                            '&:hover': {
                              borderColor: '#FF965E',
                              backgroundColor: 'rgba(255, 107, 44, 0.05)',
                            },
                          }}
                        >
                          Créer un tag
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
                
                {/* Affichage des tags */}
                {!loading && tags.map((tag, index) => (
                  <motion.tr
                    key={tag.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.03 }}
                    style={{ display: 'table-row' }}
                  >
                    <TableCell sx={{ 
                      borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                    }}>
                      <Chip
                        label={tag.name}
                        sx={{
                          backgroundColor: tag.color,
                          color: theme.palette.getContrastText(tag.color),
                          fontWeight: '500',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        }}
                      />
                    </TableCell>
                    <TableCell sx={{ 
                      borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                    }}>
                      <Typography 
                        variant="subtitle1" 
                        sx={{ 
                          fontWeight: 500,
                          color: '#1f1b18'
                        }}
                      >
                        {tag.name}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ 
                      borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '4px',
                            backgroundColor: tag.color,
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            mr: 2,
                          }}
                        />
                        <Typography variant="body2" color="text.secondary">
                          {tag.color.toUpperCase()}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right" sx={{ 
                      borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                    }}>
                      <Tooltip title="Modifier" arrow>
                        <IconButton
                          onClick={() => handleOpenDialog(tag)}
                          size="small"
                          sx={{ 
                            color: '#FF6B2C',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 107, 44, 0.05)',
                            },
                            mr: 0.5
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Supprimer" arrow>
                        <IconButton
                          onClick={() => handleDeleteTag(tag.id, tag.name)}
                          size="small"
                          sx={{ 
                            color: '#f44336',
                            '&:hover': {
                              backgroundColor: 'rgba(244, 67, 54, 0.05)',
                            }
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </motion.div>

      {/* Dialogue de création/édition */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog} 
        maxWidth="sm" 
        fullWidth
        TransitionComponent={Zoom}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: '#FF6B2C', 
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          px: 3,
          py: 2
        }}>
          <LabelIcon sx={{ mr: 1 }} />
          {editingTag ? 'Modifier le tag' : 'Créer un nouveau tag'}
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mt: 1 }}>
            <TextField
              autoFocus
              margin="dense"
              id="name"
              label="Nom du tag"
              type="text"
              fullWidth
              variant="outlined"
              value={newTag.name}
              onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LabelIcon sx={{ color: newTag.color }} />
                  </InputAdornment>
                ),
              }}
              sx={{ 
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: '#FF965E',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& label.Mui-focused': {
                  color: '#FF6B2C',
                },
              }}
            />

            {/* Sélecteur de couleur */}
            <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
              <ColorLensIcon sx={{ mr: 1, color: '#FF6B2C' }} /> Couleur du tag
            </Typography>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(36px, 1fr))',
                gap: 1,
                mb: 1,
              }}
            >
              {PREDEFINED_COLORS.map((color) => (
                <Box
                  key={color}
                  onClick={() => handleColorChange(color)}
                  sx={{
                    width: 36,
                    height: 36,
                    borderRadius: '8px',
                    backgroundColor: color,
                    cursor: 'pointer',
                    border: color === newTag.color ? '3px solid white' : '2px solid transparent',
                    outline: color === newTag.color ? `2px solid ${color}` : 'none',
                    boxShadow: color === newTag.color ? '0 2px 8px rgba(0,0,0,0.2)' : '0 2px 4px rgba(0,0,0,0.1)',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      boxShadow: '0 4px 10px rgba(0,0,0,0.2)',
                    },
                  }}
                />
              ))}
            </Box>

            {/* Aperçu du tag */}
            <Box sx={{ mt: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Typography variant="subtitle1" gutterBottom>
                Aperçu du tag
              </Typography>
              <Chip
                label={newTag.name || 'Exemple de tag'}
                sx={{
                  backgroundColor: newTag.color,
                  color: theme.palette.getContrastText(newTag.color),
                  fontWeight: '500',
                  fontSize: '0.9rem',
                  height: '32px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9f9f9' }}>
          <Button 
            onClick={handleCloseDialog} 
            sx={{ 
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              borderRadius: '8px',
              fontWeight: 500,
            }}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleSaveTag} 
            disabled={loading || !newTag.name}
            sx={{ 
              backgroundColor: '#FF6B2C',
              color: 'white',
              '&:hover': {
                backgroundColor: '#FF7A35',
              },
              borderRadius: '8px',
              px: 3,
              fontWeight: 600,
              boxShadow: '0 4px 8px rgba(255, 107, 44, 0.25)',
            }}
          >
            {editingTag ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminTicketTagsPage; 