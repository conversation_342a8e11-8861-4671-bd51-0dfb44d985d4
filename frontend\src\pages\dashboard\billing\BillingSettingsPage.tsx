import React from 'react';
import { Typography, styled } from '@mui/material';
import { Settings, ChevronLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import CompanySettingsForm from './CompanySettingsForm';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const BillingSettingsPage: React.FC = () => {
  return (
    <div className="space-y-6 px-2 md:px-0">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <PageTitle variant="h1">
          Paramètres de facturation
        </PageTitle>
        
        <Link 
          to="/dashboard/facturation"
          className="flex items-center gap-2 text-gray-600 hover:text-[#FF7A35]"
        >
          <ChevronLeft size={16} />
          Retour à la facturation
        </Link>
      </div>

      <CompanySettingsForm />
    </div>
  );
};

export default BillingSettingsPage; 