import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import axios from 'axios';
import DOMPurify from 'dompurify';
import logger from '../../../utils/logger';
import { <PERSON><PERSON>et, He<PERSON>etProvider } from 'react-helmet-async';
import { motion } from 'framer-motion';
import { notify } from '../../../components/Notification';
import { API_CONFIG } from '../../../config/api';
import { User as UserIcon, MapPin, BadgeCheck, Clock, Star, MessageCircle, Phone, AlertCircle, User, InfoIcon, HelpCircle, Calendar, CheckCircle, X, ChevronLeft, ChevronRight, Image, Heart } from 'lucide-react';
import SloganDisplay from '../../../components/profile/SloganDisplay';
import { Tooltip } from '@mui/material';
import { Link } from 'react-router-dom';
import ModalPortal from '../../../components/ModalPortal';
import InterventionZoneSection from '../components/maps/InterventionZoneSection';
// Suppression des imports inutilisés

// Types
interface ProfilData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  photo_url: string;
  banner_url?: string;
  location: string;
  bio: string;
  hourly_rate: number;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  profil_complet: boolean;
  rating: number;
  total_reviews: number;
  completion_rate: number;
  telephone: string;
  telephone_prive: boolean;
  ville: string;
  code_postal: string;
  pays: string;
  responseTime: { average: number; lastWeek: number };
  connectionsCount: number;
  companyInfo: {
    type_de_profil: string;
    nom_entreprise: string;
    prenom_entreprise: string;
    statut_entreprise: string;
    siren_entreprise: string;
    code_ape_entreprise: string;
    categorie_entreprise: string;
    effectif_entreprise: string;
    date_insee_creation_entreprise: string;
  };
  isPremium: boolean;
  intervention_zone?: {
    center: [number, number];
    radius: number;
    france_entiere?: boolean;
  };
  disponibilites?: any;
  mode_vacance?: boolean;
  slug?: string;
  slogan?: string;
  masqué?: boolean;
  services?: any[];
  galleries?: any[];
  featuredPhotos?: any[];
  date_inscription?: string;
  reviews?: ReviewData[];
}

// Type pour un avis individuel (basé sur les champs récupérés du backend)
interface ReviewData {
  id: string;
  author_id: string;
  note: number;
  commentaire?: string;
  qualites?: string[];
  defauts?: string[];
  reponse?: string;
  reponse_date?: string;
  created_at: string;
  author?: {
    id: string;
    profil?: Array<{
      prenom?: string;
      nom?: string;
      photo_url?: string;
    }>;
  };
}

// Pas besoin d'interface ProfileStats car nous n'utilisons pas de statistiques séparées

const PublicProfile: React.FC = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [profil, setProfil] = useState<ProfilData | null>(null);
  // Pas besoin de stocker les statistiques séparément car elles sont incluses dans le profil
  const [date_inscription, setDateInscription] = useState<string>('');
  const [isReviewsModalOpen, setIsReviewsModalOpen] = useState(false);
  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [selectedGallery, setSelectedGallery] = useState<any>(null);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);

  // États pour la modale des photos mises en avant
  const [isFeaturedPhotoModalOpen, setIsFeaturedPhotoModalOpen] = useState(false);
  const [selectedFeaturedPhoto, setSelectedFeaturedPhoto] = useState<any>(null);

  // Ajouter les états pour les avis
  const [userReviews, setUserReviews] = useState<ReviewData[]>([]);
  const [isReviewsLoading] = useState(false);

  // Hook pour vérifier si l'utilisateur est connecté et gérer la redirection vers le profil privé si c'est le cas
  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        // Appel à l'API pour vérifier si l'utilisateur est connecté
        const response = await axios.get(`${API_CONFIG.baseURL}/api/auth/me`, { withCredentials: true });
        const currentUser = response.data;

        // Vérifier si l'utilisateur est connecté ET si le profil affiché n'est pas le sien (en comparant les slugs)
        // Attendre que les données du profil soient chargées (profil !== null)
        if (currentUser && currentUser.user && profil && currentUser.user.slug !== profil.slug) {
          navigate(`/dashboard/profil/${slug}`);
        }
      } catch (error) {
        logger.info('Erreur lors de la vérification d\'authentification:', error);
      }
    };

    // Exécuter la vérification uniquement si un slug est présent et que les données du profil sont chargées
    if (slug && profil) {
      checkAuthAndRedirect();
    }
  }, [slug, navigate, profil]); // Ajouter 'profil' aux dépendances

  // Fonction pour formater la date
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  // Fonction pour formater les dates (utilisée dans plusieurs endroits)

  // Fonction pour nettoyer le HTML
  const stripHtml = (html: string): string => {
    if (!html) return '';
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [] }).trim();
  };

  // Récupérer les données du profil
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsLoading(true);

        // Récupérer les données du profil public
        const endpoint = `/api/users/public-profile/${slug}`;

        const response = await axios.get(endpoint, API_CONFIG);
        const userData = response.data;

        if (!userData) {
          throw new Error("Aucune donnée de profil trouvée");
        }

        // Mettre à jour l'état avec les données du profil
        setProfil(userData);

        // Récupérer la date d'inscription
        if (userData.date_inscription) {
          setDateInscription(userData.date_inscription);
        }

        // Stocker les avis dans le nouvel état si disponibles
        if (userData.reviews) {
          setUserReviews(userData.reviews);
        }

      } catch (error) {
        logger.error('Erreur lors de la récupération du profil public:', error);
        notify('Impossible de charger ce profil', 'error');
        navigate('/');
      } finally {
        setIsLoading(false);
      }
    };

    if (slug) {
      fetchUserProfile();
    }
  }, [slug, navigate]);

  // Bannière d'inscription
  const SignupBanner = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 }}
      className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] p-4 md:p-6 shadow-lg text-white mb-6"
    >
      <div className="flex flex-col md:flex-row items-center justify-between">
        <div className="mb-4 md:mb-0 md:mr-6">
          <h3 className="text-xl md:text-2xl font-bold mb-2">Vous aimez ce profil ?</h3>
          <p className="text-sm md:text-base">Inscrivez-vous sur JobPartiel pour contacter ce jobbeur et découvrir d'autres professionnels qualifiés.</p>
        </div>
        <div className="flex space-x-3">
          <Link to="/inscription" className="bg-white text-[#FF6B2C] px-4 py-2 rounded-lg font-medium hover:bg-[#FFF8F3] transition-all">
            S'inscrire
          </Link>
          <Link to="/login" className="bg-[#FFF8F3] text-[#FF6B2C] px-4 py-2 rounded-lg font-medium hover:bg-white transition-all">
            Se connecter
          </Link>
        </div>
      </div>
    </motion.div>
  );

  // Composant pour les actions bloquées (nécessitant une inscription)
  const BlockedAction = ({ actionName }: { actionName: string }) => (
    <div className="bg-white rounded-xl p-6 shadow-lg text-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-12 w-12 text-[#FF6B2C] mx-auto mb-4"><circle cx="12" cy="12" r="10"></circle><path d="M8 12h8"></path><path d="M12 8v8"></path></svg>

      <h3 className="text-xl font-bold text-gray-800 mb-2">Accédez à toutes les fonctionnalités</h3>
      <p className="text-gray-600 mb-4">
        Connectez-vous ou créez un compte gratuitement pour pouvoir {actionName} et interagir avec {actionName === 'contacter ce jobbeur' ? 'ce jobbeur' : 'les profils'}.
      </p>

      <div className="flex justify-center space-x-4">
        <Link
          to="/inscription"
          className="bg-[#FF6B2C] text-white py-2 px-4 rounded-lg font-medium hover:bg-[#FF7A35] transition-all"
        >
          S'inscrire
        </Link>
        <Link
          to="/login"
          className="border border-[#FF6B2C] text-[#FF6B2C] py-2 px-4 rounded-lg font-medium hover:bg-[#FFF8F3] transition-all"
        >
          Se connecter
        </Link>
      </div>
    </div>
  );

  // Icône de cadenas pour les actions bloquées
  const Lock = ({ className }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
  );

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#FF6B2C]"></div>
      </div>
    );
  }

  if (!profil) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen p-4">
        <AlertCircle className="h-16 w-16 text-[#FF6B2C] mb-4" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Profil introuvable</h1>
        <p className="text-gray-600 mb-6 text-center">Ce profil n'existe pas ou n'est plus disponible.</p>
        <Link to="/" className="bg-[#FF6B2C] text-white px-6 py-2 rounded-lg hover:bg-[#FF7A35] transition-all">
          Retour à l'accueil
        </Link>
      </div>
    );
  }

  return (
    <HelmetProvider>
      <div className="bg-gray-50 min-h-screen pb-12" style={{ maxWidth: '1980px', margin: '0 auto' }}>
        <Helmet>
          <title>{`${profil.firstName} ${profil.lastName} - Profil JobPartiel`}</title>
          <meta name="description" content={`Découvrez le profil de ${profil.firstName} ${profil.lastName} sur JobPartiel.fr - ${stripHtml(profil.bio || '')}`} />
        </Helmet>

        {/* Bannière d'inscription */}
        <SignupBanner />

        {/* Bannière du profil avec slogan */}
        {profil.banner_url && (
          <div className="relative w-full h-48 md:h-64 rounded-xl overflow-hidden mb-6 shadow-lg">
            <img
              src={profil.banner_url}
              alt="Bannière du profil"
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
            {profil.slogan && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                <p className="text-xl md:text-3xl font-bold text-white text-center px-4 py-2 italic shadow-text">
                  "{profil.slogan}"
                </p>
              </div>
            )}
          </div>
        )}

        {/* Header du profil */}
        <header className="bg-white rounded-xl p-6 shadow-lg mb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            {/* Photo de profil */}
            <div className="relative">
              <div className="h-24 w-24 md:h-32 md:w-32 rounded-full overflow-hidden border-4 border-white shadow-md">
                {/* Utiliser une référence pour éviter les boucles d'erreurs */}
                <img
                  src={profil.photo_url ? profil.photo_url : `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`}
                  alt={`${profil.firstName || ''} ${profil.lastName || ''}`}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    // Éviter la boucle d'erreurs en vérifiant si l'image est déjà l'image par défaut
                    const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;
                    if (e.currentTarget.src !== defaultImage) {
                      e.currentTarget.src = defaultImage;
                    }
                  }}
                />
              </div>
              {profil.isPremium && (
                <div className="absolute -top-1 -right-1 bg-[#FF6B2C] text-white text-xs font-bold px-2 py-1 rounded-full">
                  PREMIUM
                </div>
              )}
            </div>

            {/* Informations principales */}
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
                  {profil.firstName ? profil.firstName : '...'} {profil.lastName ? profil.lastName : '...'}
                </h1>
                {/* Log pour déboguer */}
                {!profil.firstName && !profil.lastName && (
                  <div className="text-xs text-red-500">
                    Données manquantes: firstName={JSON.stringify(profil.firstName)}, lastName={JSON.stringify(profil.lastName)}
                  </div>
                )}


                <div className="flex items-center gap-2">
                  {profil.identite_verifier && (
                    <Tooltip title="Identité vérifiée">
                      <div className="bg-[#FFF8F3] p-1 rounded-full">
                        <BadgeCheck className="h-5 w-5 text-[#FF6B2C]" />
                      </div>
                    </Tooltip>
                  )}
                  {profil.profil_verifier && (
                    <Tooltip title="Profil vérifié">
                      <div className="bg-[#FFF8F3] p-1 rounded-full">
                        <BadgeCheck className="h-5 w-5 text-[#FF6B2C]" />
                      </div>
                    </Tooltip>
                  )}
                  {profil.entreprise_verifier && (
                    <Tooltip title="Entreprise vérifiée">
                      <div className="bg-[#FFF8F3] p-1 rounded-full">
                        <BadgeCheck className="h-5 w-5 text-[#FF6B2C]" />
                      </div>
                    </Tooltip>
                  )}
                  {profil.assurance_verifier && (
                    <Tooltip title="Assurance vérifiée">
                      <div className="bg-[#FFF8F3] p-1 rounded-full">
                        <BadgeCheck className="h-5 w-5 text-[#FF6B2C]" />
                      </div>
                    </Tooltip>
                  )}
                </div>
              </div>

              <div className="mt-2 flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-600">
                {profil.ville && (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-[#FF6B2C]" />
                    <span>{profil.ville}{profil.code_postal ? `, ${profil.code_postal}` : ''}</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Star className="h-4 w-4 mr-1 text-[#FF6B2C]" />
                  <span>{profil.rating ? profil.rating.toFixed(1) : '0'}/5 ({profil.total_reviews || 0} avis)</span>
                </div>
                {profil?.telephone_prive === false && profil?.telephone && (
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-1 text-[#FF6B2C]" />
                    <span>{profil.telephone}</span>
                  </div>
                )}
                {profil?.telephone_prive === true && (
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-1 text-gray-400" />
                    <span>Numéro de téléphone privé</span>
                  </div>
                )}
                {profil?.connectionsCount !== undefined && (
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 mr-1 text-[#FF6B2C]" />
                    <span>{profil.connectionsCount} connexion{profil.connectionsCount !== 1 ? 's' : ''}</span>
                  </div>
                )}
              </div>

              {/* Badges - Version simplifiée pour le profil public */}
              <div className="mt-4 flex flex-wrap gap-2">
                {profil.isPremium && (
                  <div className="bg-[#FFF8F3] text-[#FF6B2C] px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    Premium
                  </div>
                )}
                {profil.profil_verifier && (
                  <div className="bg-[#FFF8F3] text-[#FF6B2C] px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <BadgeCheck className="h-4 w-4 mr-1" />
                    Profil vérifié
                  </div>
                )}
                {profil.identite_verifier && (
                  <div className="bg-[#FFF8F3] text-[#FF6B2C] px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <BadgeCheck className="h-4 w-4 mr-1" />
                    Identité vérifiée
                  </div>
                )}
                {profil.entreprise_verifier && (
                  <div className="bg-[#FFF8F3] text-[#FF6B2C] px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <BadgeCheck className="h-4 w-4 mr-1" />
                    Entreprise vérifiée
                  </div>
                )}
                {profil.assurance_verifier && (
                  <div className="bg-[#FFF8F3] text-[#FF6B2C] px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <BadgeCheck className="h-4 w-4 mr-1" />
                    Assurance vérifiée
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Contenu principal */}
        <main className="space-y-6 w-full">
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Colonne principale */}
            <div className="xl:col-span-3 space-y-6">
              {/* Slogan - affiché uniquement si pas de bannière */}
              {profil.slogan && !profil.banner_url && <SloganDisplay slogan={profil.slogan} />}

              {/* À propos, bio */}
              {/* Section À propos simplifiée */}
              <motion.section
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
                    <UserIcon className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">À propos</h2>
                </div>

                <div className="prose prose-lg max-w-none">
                  {profil?.bio ? (
                    <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(profil.bio) }} />
                  ) : (
                    <p className="text-gray-500 italic">Aucune biographie disponible</p>
                  )}
                </div>
              </motion.section>

              {/* Informations professionnelles */}
              {/* Version simplifiée des informations professionnelles */}
              <motion.section
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
                    <Calendar className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Informations professionnelles</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Date d'inscription */}
                  <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
                    <Calendar className="h-5 w-5 text-[#FF6B2C]" />
                    <div>
                      <div className="text-sm text-gray-600 font-medium">Membre depuis</div>
                      <div className="mt-1 text-gray-800 font-semibold">
                        {formatDate(date_inscription)}
                      </div>
                    </div>
                  </div>

                  {/* Taux de réponse */}
                  <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
                    <Clock className="h-5 w-5 text-[#FF6B2C]" />
                    <div>
                      <div className="text-sm text-gray-600 font-medium">Temps de réponse</div>
                      <div className="mt-1 text-gray-800 font-semibold">
                        {profil?.responseTime?.average ? profil.responseTime.average.toFixed(1) : '0'} minutes
                      </div>
                    </div>
                  </div>

                  {/* Vérifications */}
                  {profil?.identite_verifier && (
                    <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
                      <BadgeCheck className="h-5 w-5 text-[#FF6B2C]" />
                      <div>
                        <div className="text-sm text-gray-600 font-medium">Identité vérifiée</div>
                        <div className="mt-1 text-green-600 font-semibold flex items-center">
                          <CheckCircle className="h-4 w-4 mr-1" /> Vérifié
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Type de profil */}
                  {profil?.companyInfo?.type_de_profil && (
                    <div className="flex items-center gap-3 p-4 bg-[#FFF8F3] rounded-lg">
                      <UserIcon className="h-5 w-5 text-[#FF6B2C]" />
                      <div>
                        <div className="text-sm text-gray-600 font-medium">Type de profil</div>
                        <div className="mt-1 text-gray-800 font-semibold">
                          {profil.companyInfo.type_de_profil.charAt(0).toUpperCase() + profil.companyInfo.type_de_profil.slice(1)}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Informations de l'entreprise (pour profils non-particuliers vérifiés) */}
                  {profil?.companyInfo?.type_de_profil !== 'particulier' && (
                    <div className="md:col-span-2 space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h3 className="text-lg font-semibold text-blue-800">Informations de l'entreprise</h3>
                      {profil?.companyInfo?.nom_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Nom : {profil.companyInfo.nom_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.siren_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                           <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>SIREN : {profil.companyInfo.siren_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.statut_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Statut : {profil.companyInfo.statut_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.code_ape_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Code APE : {profil.companyInfo.code_ape_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.categorie_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Catégorie : {profil.companyInfo.categorie_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.effectif_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Effectif : {profil.companyInfo.effectif_entreprise}</span>
                        </div>
                      )}
                      {profil?.companyInfo?.date_insee_creation_entreprise && (
                        <div className="flex items-center gap-2 text-blue-700">
                          <InfoIcon className="h-5 w-5 flex-shrink-0" />
                          <span>Date de création (INSEE) : {formatDate(profil.companyInfo.date_insee_creation_entreprise)}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </motion.section>

              {/* Services */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-[#FF6B2C]"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Services proposés</h2>
                </div>
                <div id="services-container" className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {isLoading ? (
                    <>
                      <div className="animate-pulse">
                        <div className="h-32 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-32 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </>
                  ) : profil?.services && profil.services.length > 0 ? (
                    profil.services.map((service: any) => (
                      <div key={service.id} className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                        <div className="p-4">
                          <h3 className="text-lg font-semibold text-gray-800 mb-2">{service.titre}</h3>
                          <div className="flex items-center text-sm text-gray-600 mb-3">
                            <Clock className="h-4 w-4 mr-1 text-[#FF6B2C]" />
                            <span>{service.tarif_horaire}€/heure</span>
                          </div>
                          <p className="text-gray-600 text-sm line-clamp-3">{stripHtml(service.description)}</p>
                          <div className="mt-4 flex justify-between items-center">
                            <span className={`px-2 py-1 text-xs rounded-full ${service.statut === 'actif' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {service.statut === 'actif' ? 'Disponible' : 'Non disponible'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(service.created_at).toLocaleDateString('fr-FR')}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-2 text-center py-8">
                      <p className="text-gray-600">Aucun service proposé pour le moment.</p>
                    </div>
                  )}
                </div>
              </motion.section>

              {/* Photos mises en avant */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-[#FF6B2C]"><path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path></svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Photos mises en avant</h2>
                </div>
                <div id="featured-photos-container" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {isLoading ? (
                    <>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      </div>
                    </>
                  ) : profil?.featuredPhotos && profil.featuredPhotos.length > 0 ? (
                    profil.featuredPhotos.map((photo: any) => {
                      return (
                        <div
                          key={photo.id}
                          className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                          onClick={() => {
                            setSelectedFeaturedPhoto(photo);
                            setIsFeaturedPhotoModalOpen(true);
                          }}
                        >
                          <div className="relative aspect-video">
                            <img
                              src={photo.photo_url ? photo.photo_url : `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/featured-photo-default.jpg`}
                              alt={photo.caption || "Photo mise en avant"}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // Éviter la boucle d'erreurs en vérifiant si l'image est déjà l'image par défaut
                                const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/featured-photo-default.jpg`;
                                if (e.currentTarget.src !== defaultImage) {
                                  // Fallback vers l'image par défaut en cas d'erreur
                                  e.currentTarget.src = defaultImage;
                                }
                              }}
                            />
                            <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full">
                              Cliquer pour agrandir
                            </div>
                          </div>
                          {photo.caption && (
                            <div className="p-4">
                              <p className="text-gray-600 text-sm">{photo.caption}</p>
                            </div>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <div className="col-span-3 text-center py-8">
                      <p className="text-gray-600">Aucune photo mise en avant pour le moment.</p>
                      {/* Afficher des informations de débogage */}
                      <p className="text-xs text-gray-400 mt-2">
                        {profil?.featuredPhotos === undefined
                          ? "featuredPhotos est undefined"
                          : profil.featuredPhotos === null
                            ? "featuredPhotos est null"
                            : `featuredPhotos est un tableau vide (${profil.featuredPhotos.length})`}
                      </p>
                    </div>
                  )}
                </div>
              </motion.section>

              {/* Galerie de réalisations */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-[#FF6B2C]"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Galerie de réalisations</h2>
                </div>
                <div id="galleries-container" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {isLoading ? (
                    <>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="animate-pulse">
                        <div className="h-48 bg-gray-200 rounded-lg mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </>
                  ) : profil?.galleries && profil.galleries.length > 0 ? (
                    profil.galleries.map((gallery: any) => (
                      <div
                        key={gallery.id}
                        className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                        onClick={() => {
                          setSelectedGallery(gallery);
                          setCurrentPhotoIndex(0);
                          setIsGalleryModalOpen(true);
                        }}
                      >
                        <div className="relative aspect-video">
                          <img
                            src={gallery.cover_image ? gallery.cover_image : `${API_CONFIG.baseURL}/api/storage-proxy/galerie_realisation_client/galerie-defaut-jobpartiel.jpg`}
                            alt={gallery.name || "Galerie de réalisations"}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // Éviter la boucle d'erreurs en vérifiant si l'image est déjà l'image par défaut
                              const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/galerie_realisation_client/galerie-defaut-jobpartiel.jpg`;
                              if (e.currentTarget.src !== defaultImage) {
                                logger.error(`❌ Erreur de chargement de l'image de galerie: ${gallery.cover_image}`);
                                // Fallback vers l'image par défaut en cas d'erreur
                                e.currentTarget.src = defaultImage;
                              }
                            }}
                          />
                          <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full">
                            {gallery.imagesCount || 0} photos
                          </div>
                        </div>
                        <div className="p-4">
                          <h3 className="text-lg font-semibold text-gray-800 mb-2">{gallery.name}</h3>
                          <p className="text-gray-600 text-sm line-clamp-2">{gallery.description}</p>
                          <div className="mt-4 flex justify-between items-center">
                            <span className={`px-2 py-1 text-xs rounded-full ${gallery.status === 'actif' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {gallery.status === 'actif' ? 'Visible' : 'Non visible'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(gallery.createdAt).toLocaleDateString('fr-FR')}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 text-center py-8">
                      <p className="text-gray-600">Aucune galerie disponible pour le moment.</p>
                    </div>
                  )}
                </div>
              </motion.section>

              {/* Zone d'intervention */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <MapPin className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Zone d'intervention</h2>
                </div>
                <div className="relative h-96 rounded-lg overflow-hidden border border-gray-200">
                  {profil.intervention_zone ? (
                    <InterventionZoneSection
                      avatarUrl={profil?.photo_url}
                      initialCenter={profil?.intervention_zone?.center || [48.8566, 2.3522]}
                      initialRadius={profil?.intervention_zone?.radius || 15}
                      profil={profil}
                      maxRadius={profil?.intervention_zone?.radius || 15}
                      isOwnProfil={false}
                      franceEntiereIncluse={profil?.intervention_zone?.france_entiere}
                    />
                  ) : (
                    <div className="absolute inset-0 bg-gray-50 flex items-center justify-center">
                       <div className="text-center p-6">
                         <MapPin className="h-12 w-12 text-[#FF6B2C] mx-auto mb-4" />
                         <h3 className="text-lg font-semibold text-gray-800 mb-2">Zone d'intervention</h3>
                       <p className="text-gray-600">
                         Information non disponible
                       </p>
                     </div>
                   </div>
                 )}
               </div>

                <div className="mt-4 text-center">
                  <Link
                    to="/inscription"
                    className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm inline-flex items-center"
                  >
                    <HelpCircle className="h-4 w-4 mr-1" />
                    Inscrivez-vous pour voir la carte détaillée de la zone d'intervention
                  </Link>
                </div>
              </motion.section>
            </div>

            {/* Barre latérale */}
            <div className="xl:col-span-1 space-y-6">
              {/* Actions */}
              <motion.section
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <h2 className="text-xl font-bold text-gray-800 mb-4">Actions</h2>
                <div className="space-y-3">
                  <BlockedAction actionName="contacter ce jobbeur" />

                  <BlockedAction actionName="ajouter aux favoris" />

                  <button
                    onClick={() => setIsReviewsModalOpen(true)}
                    className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg flex items-center justify-center"
                  >
                    <Star className="h-5 w-5 mr-2" />
                    Voir les avis
                  </button>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <Link
                    to="/inscription"
                    className="text-[#FF6B2C] hover:text-[#FF7A35] flex items-center justify-center"
                  >
                    <User className="h-4 w-4 mr-1" />
                    Créer votre profil
                  </Link>
                </div>
              </motion.section>

              {/* Disponibilité */}
              <motion.section
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <Clock className="h-5 w-5 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-800">Disponibilité</h2>
                </div>

                {profil.mode_vacance ? (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 text-amber-700 flex items-start">
                    <InfoIcon className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                    <p className="text-sm">
                      Ce jobbeur est actuellement en mode vacances et pourrait mettre plus de temps à répondre.
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-600">
                    Ce jobbeur est disponible pour de nouvelles missions.
                  </p>
                )}

                <div className="mt-4 text-center">
                  <Link
                    to="/inscription"
                    className="text-[#FF6B2C] hover:text-[#FF7A35] text-sm inline-flex items-center"
                  >
                    <HelpCircle className="h-4 w-4 mr-1" />
                    Inscrivez-vous pour voir plus de détails
                  </Link>
                </div>
              </motion.section>

              {/* Informations de contact */}
              <motion.section
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <Phone className="h-5 w-5 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-800">Contact</h2>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
                  {profil?.telephone_prive === false && profil?.telephone ? (
                    <a href={`tel:${profil.telephone}`} className="text-gray-800 font-semibold flex items-center">
                      <Phone className="h-5 w-5 mr-2 text-[#FF6B2C]" />
                      {profil.telephone}
                    </a>
                  ) : profil?.telephone_prive === true ? (
                    <p className="text-gray-500 flex items-center">
                      <Phone className="h-5 w-5 mr-2 text-gray-400" />
                      Numéro de téléphone privé
                    </p>
                  ) : (
                    <>
                      <Lock className="h-6 w-6 text-gray-400 mr-2" />
                      <p className="text-gray-500">
                        Informations visibles après inscription
                      </p>
                    </>
                  )}
                </div>

                <div className="mt-4 text-center">
                  <Link
                    to="/inscription"
                    className="bg-[#FF6B2C] text-white py-2 px-4 rounded-lg inline-flex items-center"
                  >
                    S'inscrire pour contacter
                  </Link>
                </div>
              </motion.section>
            </div>
          </div>
        </main>

        {/* Modal des avis - Version simplifiée */}
        {isReviewsModalOpen && (
          <ModalPortal>
            <div
              className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50"
              onClick={() => setIsReviewsModalOpen(false)}
            >
              <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-gray-800">Avis sur {profil.firstName} {profil.lastName}</h2>
                  <button
                    onClick={() => setIsReviewsModalOpen(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {/* Afficher les avis ou le message par défaut */}
                {isReviewsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B2C] mx-auto mb-4"></div>
                    <p className="text-gray-600">Chargement des avis...</p>
                  </div>
                ) : userReviews && userReviews.length > 0 ? (
                  <div className="space-y-6">
                    {userReviews.map(review => (
                      <div key={review.id} className="border border-gray-200 rounded-lg p-4 shadow-sm">
                        <div className="flex items-center mb-2">
                          {/* Avatar et nom de l'auteur */}
                          <div className="flex items-center mr-3 min-w-[44px]">
                            <img
                              src={
                                review.author?.profil?.[0]?.photo_url
                                  ? review.author.profil[0].photo_url
                                  : `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`
                              }
                              alt={
                                review.author?.profil?.[0]?.prenom
                                  ? `${review.author.profil[0].prenom} ${review.author.profil[0].nom ?? ''}`
                                  : 'Avatar utilisateur'
                              }
                              className="h-11 w-11 rounded-full object-cover border-2 border-[#FF965E] bg-white"
                              onError={e => {
                                const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;
                                if ((e.target as HTMLImageElement).src !== defaultImage) {
                                  (e.target as HTMLImageElement).src = defaultImage;
                                }
                              }}
                            />
                            <div className="ml-2">
                              <div className="font-semibold text-gray-800 text-sm">
                                {review.author?.profil?.[0]?.prenom || 'Utilisateur'}{' '}
                                {review.author?.profil?.[0]?.nom || ''}
                              </div>
                            </div>
                          </div>
                          {/* Étoiles */}
                          <div className="flex items-center text-[#FF6B2C] mr-2">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${i < review.note ? 'fill-[#FF6B2C]' : 'fill-transparent'}`}
                                strokeWidth={2}
                              />
                            ))}
                          </div>
                          {/* Note */}
                          <span className="text-sm font-semibold text-gray-700">{review.note.toFixed(1)}/5</span>
                        </div>
                        {/* Commentaire */}
                        {review.commentaire && (
                          <p className="text-gray-600 text-sm mb-2">{stripHtml(review.commentaire)}</p>
                        )}
                        {/* Qualités */}
                        {review.qualites && review.qualites.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-2">
                            {review.qualites.map((qualite, idx) => (
                              <span key={idx} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                {qualite}
                              </span>
                            ))}
                          </div>
                        )}
                        {/* Défauts */}
                        {review.defauts && review.defauts.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-2">
                            {review.defauts.map((defaut, idx) => (
                              <span key={idx} className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                                {defaut}
                              </span>
                            ))}
                          </div>
                        )}
                        {/* Réponse de l'utilisateur */}
                        {review.reponse && (
                          <div className="mt-2 p-3 bg-gray-100 rounded-lg">
                            <p className="font-semibold text-gray-800 text-sm mb-1">Réponse du jobbeur :</p>
                            <p className="text-gray-700 text-sm">{stripHtml(review.reponse)}</p>
                          </div>
                        )}
                        {/* Date de l'avis */}
                        <div className="text-xs text-gray-500 mt-2 text-right">
                          Posté le {formatDate(review.created_at)}
                          {review.reponse_date && ` | Répondu le ${formatDate(review.reponse_date)}`}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : ( profil?.total_reviews || 0) > 0 ? (
                  <div className="mb-6 p-4 bg-[#FFF8F3] rounded-lg">
                    <div className="flex items-center mb-2">
                      <Star className="h-5 w-5 text-[#FF6B2C] mr-2" />
                      <span className="text-lg font-semibold">{profil.rating.toFixed(1)}/5</span>
                      <span className="text-gray-500 ml-2">({profil.total_reviews} avis)</span>
                    </div>
                    <p className="text-gray-600">
                      Pour voir tous les avis et laisser le vôtre, inscrivez-vous sur JobPartiel.
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 text-[#FF6B2C] mx-auto mb-4" />
                    <p className="text-gray-600">Aucun avis n'est encore disponible pour ce jobbeur.</p>
                    {/* Afficher des informations de débogage si nécessaire */}
                     <p className="text-xs text-gray-400 mt-2">
                      {profil?.reviews === undefined
                          ? "reviews est undefined"
                          : profil?.reviews === null
                            ? "reviews est null"
                            : `reviews est un tableau vide (${profil?.reviews?.length || 0})`}
                     </p>
                  </div>
                )}

                <div className="mt-6 pt-4 border-t border-gray-200 text-center">
                  <p className="text-gray-600 mb-4">Connectez-vous ou créez un compte pour laisser un avis !</p>
                  <Link
                    to="/inscription"
                    className="bg-[#FF6B2C] text-white py-2 px-6 rounded-lg inline-flex items-center hover:bg-[#FF7A35] transition-all"
                  >
                    S'inscrire
                  </Link>
                </div>
              </div>
            </div>
          </ModalPortal>
        )}

        {/* Modal de galerie photos */}
        {isGalleryModalOpen && selectedGallery && (
          <ModalPortal>
            <div
              className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-80"
              onClick={() => setIsGalleryModalOpen(false)}
            >
              <div className="bg-white rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-gray-800">{selectedGallery.name}</h2>
                  <button
                    onClick={() => setIsGalleryModalOpen(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {selectedGallery.photos && selectedGallery.photos.length > 0 ? (
                  <div className="relative">
                    <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-4">
                      <img
                        src={selectedGallery.photos[currentPhotoIndex]?.photo_url || selectedGallery.cover_image || `${API_CONFIG.baseURL}/api/storage-proxy/galerie_realisation_client/galerie-defaut-jobpartiel.jpg`}
                        alt={selectedGallery.photos[currentPhotoIndex]?.caption || selectedGallery.name || "Photo de galerie"}
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          // Éviter la boucle d'erreurs en vérifiant si l'image est déjà l'image par défaut
                          const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/galerie_realisation_client/galerie-defaut-jobpartiel.jpg`;
                          if (e.currentTarget.src !== defaultImage) {
                            logger.error(`❌ Erreur de chargement de l'image de galerie dans la modale`);
                            // Fallback vers l'image par défaut en cas d'erreur
                            e.currentTarget.src = defaultImage;
                          }
                        }}
                      />
                    </div>

                    {selectedGallery.photos.length > 1 && (
                      <div className="flex justify-between items-center">
                        <button
                          onClick={() => setCurrentPhotoIndex(prev => (prev > 0 ? prev - 1 : selectedGallery.photos.length - 1))}
                          className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                        >
                          <ChevronLeft className="h-6 w-6 text-[#FF6B2C]" />
                        </button>

                        <div className="text-center text-gray-600">
                          {currentPhotoIndex + 1} / {selectedGallery.photos.length}
                        </div>

                        <button
                          onClick={() => setCurrentPhotoIndex(prev => (prev < selectedGallery.photos.length - 1 ? prev + 1 : 0))}
                          className="bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                        >
                          <ChevronRight className="h-6 w-6 text-[#FF6B2C]" />
                        </button>
                      </div>
                    )}

                    {selectedGallery.photos[currentPhotoIndex]?.caption && (
                      <div className="mt-4 p-4 bg-[#FFF8F3] rounded-lg">
                        <p className="text-gray-700">{selectedGallery.photos[currentPhotoIndex].caption}</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="bg-gray-100 p-6 rounded-lg inline-block mb-4">
                      <Image className="h-12 w-12 text-gray-400" />
                    </div>
                    <p className="text-gray-600">Aucune photo disponible dans cette galerie</p>
                  </div>
                )}

                <div className="mt-6 text-center">
                  <Link
                    to="/inscription"
                    className="bg-[#FF6B2C] text-white py-2 px-6 rounded-lg inline-flex items-center hover:bg-[#FF7A35] transition-all"
                  >
                    S'inscrire pour voir plus de détails
                  </Link>
                </div>
              </div>
            </div>
          </ModalPortal>
        )}

        {/* Modal pour les photos mises en avant */}
        {isFeaturedPhotoModalOpen && selectedFeaturedPhoto && (
          <ModalPortal>
            <div
              className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-80"
              onClick={() => setIsFeaturedPhotoModalOpen(false)}
            >
              <div className="bg-white rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-gray-800">Photo mise en avant</h2>
                  <button
                    onClick={() => setIsFeaturedPhotoModalOpen(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="relative">
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-4">
                    <img
                      src={selectedFeaturedPhoto.photo_url ? selectedFeaturedPhoto.photo_url : `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/featured-photo-default.jpg`}
                      alt={selectedFeaturedPhoto.caption || "Photo mise en avant"}
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        // Éviter la boucle d'erreurs en vérifiant si l'image est déjà l'image par défaut
                        const defaultImage = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/featured-photo-default.jpg`;
                        if (e.currentTarget.src !== defaultImage) {
                          logger.error(`❌ Erreur de chargement de l'image dans la modale: ${selectedFeaturedPhoto.photo_url}`);
                          // Fallback vers l'image par défaut en cas d'erreur
                          e.currentTarget.src = defaultImage;
                        }
                      }}
                    />
                  </div>

                  {selectedFeaturedPhoto.caption && (
                    <div className="mt-4 p-4 bg-[#FFF8F3] rounded-lg">
                      <p className="text-gray-700">{selectedFeaturedPhoto.caption}</p>
                    </div>
                  )}
                </div>

                <div className="mt-6 text-center">
                  <Link
                    to="/inscription"
                    className="bg-[#FF6B2C] text-white py-2 px-6 rounded-lg inline-flex items-center hover:bg-[#FF7A35] transition-all"
                  >
                    S'inscrire pour voir plus de détails
                  </Link>
                </div>
              </div>
            </div>
          </ModalPortal>
        )}

        {/* Bannière d'inscription en bas de page */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-40"
        >
          <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between">
            <p className="text-gray-700 mb-3 sm:mb-0">
              <span className="font-bold text-[#FF6B2C]">JobPartiel</span> - La plateforme qui connecte les jobbeurs et les clients
            </p>
            <div className="flex space-x-3">
              <Link to="/inscription" className="bg-[#FF6B2C] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#FF7A35] transition-all">
                S'inscrire gratuitement
              </Link>
              <Link to="/login" className="border border-[#FF6B2C] text-[#FF6B2C] px-4 py-2 rounded-lg font-medium hover:bg-[#FFF8F3] transition-all">
                Se connecter
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </HelmetProvider>
  );
};

export default PublicProfile;
