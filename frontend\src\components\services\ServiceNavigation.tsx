import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Paper,
  Grid
} from '@mui/material';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../../pages/dashboard/services/types';

interface ServiceNavigationProps {
  currentService?: string;
  currentCity?: string;
  showTitle?: boolean;
  maxItems?: number;
}

const ServiceNavigation: React.FC<ServiceNavigationProps> = ({
  currentService,
  currentCity,
  showTitle = true,
  maxItems = 20
}) => {
  const navigate = useNavigate();

  // Fonction pour convertir un nom en slug
  const createSlug = (name: string) => {
    return name.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/[^a-z0-9-]/g, '');
  };

  // Obtenir tous les services populaires
  const getPopularServices = () => {
    const popularServices: Array<{ name: string; slug: string; category: string }> = [];
    
    SERVICE_SUBCATEGORIES.forEach(subcategory => {
      const category = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId);
      if (category) {
        popularServices.push({
          name: subcategory.nom,
          slug: createSlug(subcategory.nom),
          category: category.nom
        });
      }
    });

    // Trier par popularité (services les plus demandés en premier)
    const popularOrder = [
      'plomberie', 'électricité', 'jardinage', 'bricolage', 'ménage', 
      'garde d\'animaux', 'baby-sitting', 'aide aux personnes âgées',
      'peinture', 'serrurerie', 'chauffage', 'carrelage', 'menuiserie',
      'paysagiste', 'élagage', 'déménagement', 'coursier', 'repassage'
    ];

    return popularServices
      .sort((a, b) => {
        const aIndex = popularOrder.findIndex(p => a.slug.includes(p));
        const bIndex = popularOrder.findIndex(p => b.slug.includes(p));
        if (aIndex === -1 && bIndex === -1) return a.name.localeCompare(b.name);
        if (aIndex === -1) return 1;
        if (bIndex === -1) return -1;
        return aIndex - bIndex;
      })
      .slice(0, maxItems);
  };

  // Obtenir les villes populaires
  const getPopularCities = () => {
    return [
      'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes',
      'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille', 'Rennes',
      'Reims', 'Le Havre', 'Saint-Étienne', 'Toulon', 'Grenoble',
      'Dijon', 'Angers', 'Nîmes', 'Villeurbanne'
    ].slice(0, maxItems);
  };

  const handleServiceClick = (serviceSlug: string) => {
    if (currentCity) {
      const citySlug = createSlug(currentCity);
      navigate(`/services/${serviceSlug}/${citySlug}`);
    } else {
      navigate(`/services/search?q=${serviceSlug}`);
    }
  };

  const handleCityClick = (city: string) => {
    if (currentService) {
      const citySlug = createSlug(city);
      navigate(`/services/${currentService}/${citySlug}`);
    } else {
      navigate(`/services/search?city=${city}`);
    }
  };

  const handleServiceCityClick = (serviceSlug: string, city: string) => {
    const citySlug = createSlug(city);
    navigate(`/services/${serviceSlug}/${citySlug}`);
  };

  const popularServices = getPopularServices();
  const popularCities = getPopularCities();

  return (
    <Box>
      {/* Services populaires */}
      <Paper sx={{ p: 3, mb: 3 }}>
        {showTitle && (
          <Typography variant="h5" gutterBottom color="primary">
            Services populaires
          </Typography>
        )}
        <Box display="flex" flexWrap="wrap" gap={1}>
          {popularServices.map((service) => (
            <Chip
              key={service.slug}
              label={service.name}
              variant={currentService === service.slug ? "filled" : "outlined"}
              color={currentService === service.slug ? "primary" : "default"}
              clickable
              onClick={() => handleServiceClick(service.slug)}
              sx={{ mb: 1 }}
            />
          ))}
        </Box>
      </Paper>

      {/* Villes populaires */}
      <Paper sx={{ p: 3, mb: 3 }}>
        {showTitle && (
          <Typography variant="h5" gutterBottom color="primary">
            Villes populaires
          </Typography>
        )}
        <Box display="flex" flexWrap="wrap" gap={1}>
          {popularCities.map((city) => (
            <Chip
              key={city}
              label={city}
              variant={currentCity?.toLowerCase() === city.toLowerCase() ? "filled" : "outlined"}
              color={currentCity?.toLowerCase() === city.toLowerCase() ? "primary" : "default"}
              clickable
              onClick={() => handleCityClick(city)}
              sx={{ mb: 1 }}
            />
          ))}
        </Box>
      </Paper>

      {/* Combinaisons populaires service + ville */}
      {!currentService && !currentCity && (
        <Paper sx={{ p: 3 }}>
          {showTitle && (
            <Typography variant="h5" gutterBottom color="primary">
              Recherches populaires
            </Typography>
          )}
          <Grid container spacing={1}>
            {popularServices.slice(0, 6).map((service) => (
              popularCities.slice(0, 3).map((city) => (
                <Grid size={{ xs: 6, sm: 4, md: 3 }} key={`${service.slug}-${city}`}>
                  <Chip
                    label={`${service.name} ${city}`}
                    variant="outlined"
                    clickable
                    onClick={() => handleServiceCityClick(service.slug, city)}
                    sx={{ 
                      mb: 1, 
                      width: '100%',
                      '& .MuiChip-label': {
                        fontSize: '0.75rem'
                      }
                    }}
                  />
                </Grid>
              ))
            ))}
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default ServiceNavigation;
