import express from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import {
  getUsers,
  getUserDetails,
  getUserStats,
  manageJobi,
  manageAiCredits
} from '../controllers/userManagement';
import {
  manageSubscription,
  performUserAction,
  manageUserPhotos,
  manageUserBadges,
  manageUserMessages,
  getUserMissions,
  generateUserReport,
  getUserActionsHistory
} from '../controllers/userManagementActions';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiter pour les actions administratives
const adminActionsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes par minute
  message: {
    success: false,
    message: 'Trop de requêtes administratives. Veuillez réessayer dans une minute.',
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification et vérification des rôles admin/modo
router.use(authMiddleware.authenticateToken);
router.use(authMiddleware.checkRole(['jobpadm', 'jobmodo']));
router.use(adminActionsLimiter);

// Routes pour la gestion des utilisateurs

// Récupérer la liste des utilisateurs avec pagination et filtres
router.get('/users', async (req, res) => {
  try {
    await getUsers(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Récupérer les détails complets d'un utilisateur
router.get('/users/:userId', async (req, res) => {
  try {
    await getUserDetails(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Récupérer les statistiques d'un utilisateur
router.get('/users/:userId/stats', async (req, res) => {
  try {
    await getUserStats(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des Jobi
router.post('/users/:userId/jobi', async (req, res) => {
  try {
    await manageJobi(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des crédits IA
router.post('/users/:userId/ai-credits', async (req, res) => {
  try {
    await manageAiCredits(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des abonnements
router.post('/users/:userId/subscription', async (req, res) => {
  try {
    await manageSubscription(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Actions administratives sur l'utilisateur
router.post('/users/:userId/actions', async (req, res) => {
  try {
    await performUserAction(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des photos et galeries
router.post('/users/:userId/photos', async (req, res) => {
  try {
    await manageUserPhotos(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des badges
router.post('/users/:userId/badges', async (req, res) => {
  try {
    await manageUserBadges(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Gestion des messages et conversations
router.post('/users/:userId/messages', async (req, res) => {
  try {
    await manageUserMessages(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Récupération des missions d'un utilisateur
router.get('/users/:userId/missions', async (req, res) => {
  try {
    await getUserMissions(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Récupération de l'historique des activités d'un utilisateur
router.get('/users/:userId/actions-history', async (req, res) => {
  try {
    await getUserActionsHistory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Génération de rapports utilisateur
router.get('/users/:userId/report', async (req, res) => {
  try {
    await generateUserReport(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

// Mettre à jour le profil d'un utilisateur (admin/modo)
router.put('/users/:userId/profile', async (req, res) => {
  try {
    const { updateUserProfile } = await import('../controllers/userManagementActions');
    await updateUserProfile(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur serveur', toastType: 'error' });
  }
});

export default router;
