import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { notify } from '../components/Notification';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { useAiCredits } from './useAiCredits';
import useAiConsent from './useAiConsent';
import logger from '../utils/logger';

// Types de génération d'images supportés
export type ImageGenerationPurpose = 'profile_picture' | 'banner_picture' | 'mission_image' | 'gallery_photo' | 'featured_photo' | 'card_editor';

// Coût en crédits pour générer une image
export const IMAGE_GENERATION_COST = 5;

// Interface pour les options de génération d'images
export interface ImageGenerationOptions {
  prompt?: string;
  width?: number;
  height?: number;
  purpose: ImageGenerationPurpose;
  useContextualPrompt?: boolean;
  galleryId?: string;
  galleryName?: string;
  galleryDescription?: string;
  missionTitle?: string;
  missionDescription?: string;
}

// Interface pour les données de retour
export interface AiImageGenerationData {
  generating: boolean;
  error: string | null;
  imageUrl: string | null;
  imageBase64: string | null;
  prompt: string | null;
  generateImage: (options: ImageGenerationOptions) => Promise<{ imageUrl: string; imageBase64: string } | null>;
  resetState: () => void;
  credits: number;
  isRateLimited: boolean;
}

/**
 * Hook pour gérer la génération d'images IA
 */
export const useAiImageGeneration = (): AiImageGenerationData => {
  const { isAuthenticated } = useAuth();
  const { credits, isRateLimited, error: creditError, refetch: refetchCredits } = useAiCredits();
  const { hasConsent } = useAiConsent();
  const [generating, setGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageBase64, setImageBase64] = useState<string | null>(null);
  const [prompt, setPrompt] = useState<string | null>(null);

  /**
   * Réinitialise l'état du hook
   */
  const resetState = useCallback(() => {
    setGenerating(false);
    setError(null);
    setImageUrl(null);
    setImageBase64(null);
    setPrompt(null);
  }, []);

  /**
   * Vérifie si l'utilisateur a assez de crédits pour générer une image
   */
  const checkCredits = useCallback(() => {
    if (isRateLimited) {
      notify('Vous avez atteint la limite de requêtes. Veuillez patienter quelques minutes.', 'error');
      return false;
    }

    if (credits < IMAGE_GENERATION_COST) {
      notify(`Vous n'avez pas assez de crédits IA. La génération d'image nécessite ${IMAGE_GENERATION_COST} crédits. Veuillez en acheter dans le menu "Intelligence Artificielle"`, 'error');
      return false;
    }

    if (creditError) {
      notify('Une erreur est survenue lors de la vérification des crédits.', 'error');
      return false;
    }

    return true;
  }, [credits, isRateLimited, creditError]);

  /**
   * Génère une image avec l'IA
   * @param options Options de génération d'image
   * @param useTemporaryStorage Si true, l'image sera stockée temporairement et non définitivement (par défaut: false)
   */
  const generateImage = useCallback(async (options: ImageGenerationOptions): Promise<{ imageUrl: string; imageBase64: string } | null> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour utiliser cette fonctionnalité.', 'error');
      return null;
    }

    if (!hasConsent) {
      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu.', 'error');
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      return null;
    }

    if (!checkCredits()) {
      return null;
    }

    try {
      setGenerating(true);
      setError(null);

      // Appeler l'API de génération d'images
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/ai-image-generation/generate`,
        {
          prompt: options.prompt || '',
          purpose: options.purpose,
          width: options.width || 1024,
          height: options.height || 1024,
          useContextualPrompt: options.useContextualPrompt || !options.prompt,
          storeTemporarily: true, // Toujours stocker temporairement d'abord
          galleryId: options.galleryId,
          galleryName: options.galleryName,
          galleryDescription: options.galleryDescription,
          missionTitle: options.missionTitle,
          missionDescription: options.missionDescription
        },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data && response.data.success) {
        // Rafraîchir le solde de crédits après la génération
        await refetchCredits();

        // Mettre à jour l'état avec l'URL de l'image générée
        setImageUrl(response.data.imageUrl);
        setImageBase64(response.data.imageBase64);
        setPrompt(response.data.prompt);

        return {
          imageUrl: response.data.imageUrl,
          imageBase64: response.data.imageBase64
        };
      } else {
        throw new Error(response.data.message || 'Erreur lors de la génération de l\'image');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la génération de l\'image';
      setError(errorMessage);
      notify(errorMessage, 'error');
      logger.error('Erreur lors de la génération d\'image IA:', err);
      return null;
    } finally {
      setGenerating(false);
    }
  }, [isAuthenticated, hasConsent, checkCredits, refetchCredits]);

  return {
    generating,
    error,
    imageUrl,
    imageBase64,
    prompt,
    generateImage,
    resetState,
    credits,
    isRateLimited
  };
};
