import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
  Divider,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  InputAdornment,
  Collapse
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  Star as StarIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import { notify } from '../../components/Notification';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

// Types pour les logs de modération
interface UserInfo {
  // Informations de base
  id: string;
  email: string;
  role: string | null;
  user_type: string | null;

  // États de vérification
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  email_verifier: boolean;
  profil_actif: boolean;

  // Dates
  date_inscription: string | null;
  last_login: string | null;

  // Informations de profil
  prenom: string | null;
  nom: string | null;
  telephone: string | null;
  telephone_prive: boolean;
  ville: string | null;
  code_postal: string | null;
  pays: string | null;
  photo_url: string | null;
  type_de_profil: string | null;
  slug: string | null;
  profil_visible: boolean;
}

interface ModerationLog {
  id: string;
  content_type: 'mission' | 'comment' | 'profile' | 'titre_service' | 'description_service' |
                'gallery_name' | 'gallery_description' | 'mission_title' | 'mission_description' |
                'review' | 'gallery' | 'gallery_cover' | 'featured' | 'mission_assistant' |
                'avatar' | 'profile_picture' | 'banner_picture';
  content_id: string;
  user_id: string;
  is_safe: boolean;
  score: number;
  categories: {
    harassment: boolean;
    hateSpeech: boolean;
    sexualContent: boolean;
    violence: boolean;
    selfHarm: boolean;
    illegalActivity: boolean;
    spam: boolean;
    phoneSpam: boolean;
    addressSpam: boolean;
    adult: boolean;
    dangerousContent: boolean;
    poorQuality: boolean;
    irrelevantContent: boolean;
    misleadingContent: boolean;
    unknownRisk: boolean;
    clean?: boolean; // Ajouté : true si l'image est parfaitement clean
  };
  flagged_text: string[];
  moderation_id: string;
  created_at: string;
  user_email?: string;
  user_name?: string;
  user_info?: UserInfo;
  // Nouveaux champs pour la modération d'images
  description?: string;
  serviceType?: string;
  relevantToUserServices?: boolean;
  qualityAssessment?: {
    overall: number;
    clarity: number;
    relevance: number;
    professionalAppearance: number;
  };
  improvementSuggestions?: string;
  is_image_moderation?: boolean;
  service_type?: string;
  image_url?: string;
  temp_image_path?: string;
  image_path?: string;
}

// Types pour les filtres
interface Filters {
  contentType: string;
  isSafe: string;
  userId: string;
  userEmail: string;
  dateFrom: string;
  dateTo: string;
  searchTerm: string;
  scoreMin: string;
  scoreMax: string;
  moderationMethod: string;
  category: string;
}

const ContentModerationLogsPage: React.FC = () => {
  // États pour les données et la pagination
  const [logs, setLogs] = useState<ModerationLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [isTyping, setIsTyping] = useState(false);

  // État pour les filtres
  const [filters, setFilters] = useState<Filters>({
    contentType: '',
    isSafe: '',
    userId: '',
    userEmail: '',
    dateFrom: '',
    dateTo: '',
    searchTerm: '',
    scoreMin: '',
    scoreMax: '',
    moderationMethod: '',
    category: ''
  });

  // État temporaire pour les champs avec debounce
  const [debouncedFilters, setDebouncedFilters] = useState<{
    userEmail: string;
    searchTerm: string;
  }>({
    userEmail: '',
    searchTerm: ''
  });

  // État pour le tri
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // État pour le détail d'un log
  const [selectedLog, setSelectedLog] = useState<ModerationLog | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  // État pour suivre quelles catégories ont leur description affichée
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // État pour le filtre avancé
  const [showFilters, setShowFilters] = useState(false);

  // Fonction pour récupérer les logs
  const fetchLogs = useCallback(async (pageNum = page, rowsPerPageNum = rowsPerPage) => {
    setLoading(true);
    setError(null);
    setInfoMessage(null);

    try {
      const headers = await getCommonHeaders();

      const params: any = {
        page: pageNum + 1, // API commence à 1, React à 0
        limit: rowsPerPageNum,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      // Ajouter les filtres aux paramètres
      if (filters.contentType) params.content_type = filters.contentType;

      // Gestion spéciale pour le filtre "potentiellement problématique"
      if (filters.isSafe !== '') {
        if (filters.isSafe === 'potential') {
          // Pour les contenus potentiellement problématiques, on filtre côté client
          params.is_safe = true; // On récupère d'abord tous les contenus sûrs
          params.min_score = 0.3; // Avec un score minimum de 0.3
        } else {
          params.is_safe = filters.isSafe === 'true';
        }
      }

      if (filters.userId) params.user_id = filters.userId;
      if (filters.userEmail) params.user_email = filters.userEmail;
      if (filters.dateFrom) params.date_from = filters.dateFrom;
      if (filters.dateTo) params.date_to = filters.dateTo;
      if (filters.searchTerm) params.search = filters.searchTerm;

      // Nouveaux filtres
      if (filters.scoreMin) params.score_min = filters.scoreMin;
      if (filters.scoreMax) params.score_max = filters.scoreMax;
      if (filters.moderationMethod) params.moderation_method = filters.moderationMethod;
      if (filters.category) params.category = filters.category;

      const response = await axios.get(`${API_CONFIG.baseURL}/api/content-moderation/history`, {
        headers,
        withCredentials: true,
        params
      });

      if (response.data.success) {
        const transformedLogs = response.data.data.map((log: any) => {
          const userName = log.user_name ||
                           (log.user_info?.prenom && log.user_info?.nom ? `${log.user_info.prenom} ${log.user_info.nom}` :
                           (log.user_info?.email ? log.user_info.email.split('@')[0] : 'Utilisateur inconnu'));
          const userEmail = log.user_email || log.user_info?.email || 'Email non disponible';

          return {
            ...log,
            user_name: userName,
            user_email: userEmail,
          };
        });

        setLogs(transformedLogs);
        setTotal(response.data.pagination.total);

        // Afficher un message d'information si aucun résultat n'est trouvé
        if (transformedLogs.length === 0) {
          if (filters.userEmail && filters.userEmail.trim() !== '') {
            setInfoMessage(`Aucun log de modération trouvé pour l'email contenant "${filters.userEmail}"`);
          } else if (filters.userId && filters.userId.trim() !== '') {
            setInfoMessage(`Aucun log de modération trouvé pour l'ID utilisateur "${filters.userId}"`);
          } else if (Object.values(filters).some(val => val && val.trim && val.trim() !== '')) {
            setInfoMessage('Aucun log de modération ne correspond aux critères de recherche');
          }
        }
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération des logs');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la récupération des logs';
      setError(errorMessage);
      notify(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, sortBy, sortOrder, filters]);

  // Initialiser les valeurs de debouncedFilters avec les valeurs de filters
  useEffect(() => {
    setDebouncedFilters({
      userEmail: filters.userEmail,
      searchTerm: filters.searchTerm
    });
  }, []);

  // Charger les logs au chargement de la page et lors des changements de filtres/tri
  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  // Gestionnaires d'événements pour la pagination
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Gestionnaire pour le tri
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Fonction pour appliquer les filtres avec debounce
  const applyDebouncedFilters = useCallback((name: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  // Version avec debounce de la fonction d'application des filtres
  const debouncedApplyFilters = useCallback(
    (name: string, value: string) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      setIsTyping(true);

      timeoutRef.current = setTimeout(() => {
        applyDebouncedFilters(name, value);
        setIsTyping(false);
        timeoutRef.current = null;
      }, 500); // 500ms de délai
    },
    [applyDebouncedFilters]
  );

  // Référence pour le timeout de debounce
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Gestionnaire pour les filtres
  const handleFilterChange = (
    event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>
  ) => {
    const { name, value } = event.target;
    if (!name) return;

    // Appliquer le debounce uniquement pour les champs de recherche
    if (name === 'userEmail' || name === 'searchTerm') {
      // Mettre à jour l'état temporaire immédiatement pour l'affichage
      setDebouncedFilters(prev => ({
        ...prev,
        [name]: value as string
      }));

      // Appliquer le debounce pour la recherche
      debouncedApplyFilters(name, value as string);
    } else {
      // Pour les autres champs, appliquer immédiatement
      setFilters(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Gestionnaire pour réinitialiser les filtres
  const handleResetFilters = () => {
    // Réinitialiser les filtres principaux
    setFilters({
      contentType: '',
      isSafe: '',
      userId: '',
      userEmail: '',
      dateFrom: '',
      dateTo: '',
      searchTerm: '',
      scoreMin: '',
      scoreMax: '',
      moderationMethod: '',
      category: ''
    });

    // Réinitialiser également les filtres avec debounce
    setDebouncedFilters({
      userEmail: '',
      searchTerm: ''
    });

    // Annuler tout timeout en cours
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    setIsTyping(false);
    setPage(0);
  };

  // Gestionnaire pour appliquer les filtres
  const handleApplyFilters = () => {
    setPage(0);
    fetchLogs(0, rowsPerPage);
  };

  // Gestionnaire pour ouvrir le dialogue de détail
  const handleOpenDetail = (log: ModerationLog) => {
    setSelectedLog(log);
    setDetailDialogOpen(true);
    // Réinitialiser l'état des catégories développées à chaque ouverture
    setExpandedCategories({});
  };

  // Fonction pour basculer l'affichage de la description d'une catégorie
  const toggleCategoryDescription = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Fonction pour obtenir la couleur du score
  const getScoreColor = (score: number) => {
    if (score < 0.3) return 'success';
    if (score < 0.7) return 'warning';
    return 'error';
  };

  // Fonction pour obtenir le style du type de contenu
  const getTypeStyle = (type: string): { bgcolor: string; color: string } => {
    switch (type) {
      case 'profile':
      case 'profile_picture':
      case 'banner_picture':
      case 'avatar':
        return { bgcolor: '#E8F5E9', color: '#2E7D32' }; // Vert
      case 'gallery':
        return { bgcolor: '#F3E5F5', color: '#7B1FA2' }; // Violet
      case 'gallery_cover':
        return { bgcolor: '#E1F5FE', color: '#0288D1' }; // Bleu clair
      case 'featured':
        return { bgcolor: '#FFF3E0', color: '#E65100' }; // Orange
      case 'mission_assistant':
        return { bgcolor: '#FFECB3', color: '#FF8F00' }; // Jaune foncé
      case 'mission':
        return { bgcolor: '#E3F2FD', color: '#1565C0' }; // Bleu
      case 'mission_title':
        return { bgcolor: '#FFF8E1', color: '#FFA000' }; // Jaune
      case 'mission_description':
        return { bgcolor: '#F0F4C3', color: '#388E3C' }; // Vert clair
      case 'comment':
        return { bgcolor: '#FFF3E0', color: '#E65100' }; // Orange
      case 'titre_service':
        return { bgcolor: '#F3E5F5', color: '#7B1FA2' }; // Violet
      case 'description_service':
        return { bgcolor: '#E1F5FE', color: '#0288D1' }; // Bleu clair
      case 'gallery_name':
        return { bgcolor: '#FFECB3', color: '#FF8F00' }; // Jaune foncé
      case 'gallery_description':
        return { bgcolor: '#FFE0B2', color: '#EF6C00' }; // Orange clair
      case 'review':
        return { bgcolor: '#FFF8F3', color: '#FF6B2C' }; // Orange JobPartiel
      default:
        return { bgcolor: '#F5F5F5', color: '#616161' }; // Gris par défaut
    }
  };

  // Fonction pour obtenir le libellé du type de contenu
  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'mission': return 'Mission';
      case 'comment': return 'Commentaire';
      case 'profile': return 'Biographie';
      case 'titre_service': return 'Titre de service';
      case 'description_service': return 'Description de service';
      case 'gallery_name': return 'Nom de galerie';
      case 'gallery_description': return 'Description de galerie';
      case 'mission_title': return 'Titre de mission';
      case 'mission_description': return 'Description de mission';
      case 'review': return 'Avis';
      case 'gallery': return 'Image de galerie';
      case 'gallery_cover': return 'Image de couverture';
      case 'featured': return 'Image featured profil';
      case 'mission_assistant': return 'Image assistant mission';
      case 'avatar': return 'Photo de profil';
      case 'profile_picture': return 'Photo de profil'; 
      case 'banner_picture': return 'Bannière de profil';
      default: return type;
    }
  };

  // Fonction pour déterminer le type de modération à partir de l'ID
  const getModerationType = (moderationId: string, _isImageModeration?: boolean | string, contentType?: string) => {
    if (moderationId.startsWith('error-image-')) return 'Erreur Image';
    if (moderationId.startsWith('image-')) return 'Image (IA)';

    // Gestion du préfixe 'gen-'
    if (moderationId.startsWith('gen-')) {
      // Déterminer uniquement à partir du contentType
      const imageTypes = [
        'gallery',
        'gallery_cover',
        'featured',
        'mission_assistant',
        'avatar',
        'profile_picture',
        'banner_picture'
      ];
      if (contentType && imageTypes.includes(contentType)) {
        return 'Image (IA)';
      } else {
        return 'Texte (IA)';
      }
    }

    if (moderationId.startsWith('api-')) return 'Texte (IA)';
    if (moderationId.startsWith('basic-')) return 'Texte (SANS IA)'; // Label préféré par l'utilisateur
    if (moderationId.startsWith('local-')) return 'Texte (Local)';

    // Erreur texte générique (s'assurer que ce n'est pas une erreur image déjà gérée)
    if (moderationId.startsWith('error-') && !moderationId.startsWith('error-image-')) {
      return 'Erreur Texte';
    }

    return 'Modération Inconnue'; // Fallback si aucun préfixe connu
  };

  // Fonction pour obtenir la description détaillée du type de modération
  const getModerationTypeDescription = (moderationId: string, contentType?: string, isImageModeration?: boolean): string => {
    const type = getModerationType(moderationId, isImageModeration, contentType);

    switch (type) {
      case 'Image (IA)':
        return 'Analyse d\'image par intelligence artificielle qui évalue le contenu visuel pour détecter des éléments inappropriés, la qualité de l\'image et sa pertinence par rapport aux services proposés.';
      case 'Erreur Image':
        return 'Une erreur s\'est produite lors de la modération de l\'image. L\'image a été considérée comme sûre par défaut.';
      case 'Texte (IA)':
        return 'Analyse avancée par intelligence artificielle qui évalue le contexte et le contenu pour détecter des propos inappropriés.';
      case 'Texte (SANS IA)':
        return 'Détection automatique basée sur une liste de mots interdits, expressions inappropriées, numéros de téléphone et adresses postales.';
      case 'Texte (Local)':
        return 'Vérification effectuée localement par le système sans appel à une API externe.';
      case 'Erreur Texte':
        return 'Une erreur s\'est produite lors de la modération du texte. Le contenu a été considéré comme sûr par défaut.';
      default:
        return 'Méthode de modération non identifiée.';
    }
  };

  // Fonction pour obtenir la couleur selon le type de modération
  const getModerationTypeColor = (moderationId: string, contentType?: string, isImageModeration?: boolean): 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' => {
    const type = getModerationType(moderationId, isImageModeration, contentType);

    switch (type) {
      case 'Image (IA)': return 'success';
      case 'Erreur Image': return 'error';
      case 'Texte (IA)': return 'primary';
      case 'Texte (SANS IA)': return 'secondary';
      case 'Texte (Local)': return 'info';
      case 'Erreur Texte': return 'error';
      default: return 'warning';
    }
  };

  // Fonction pour traduire et expliquer les catégories de modération
  const getCategoryLabel = (category: string): { label: string; description: string } => {
    switch (category) {
      case 'harassment':
        return {
          label: 'Harcèlement',
          description: 'Contenu qui cible, intimide, insulte ou menace des individus. Inclut des insultes, des attaques personnelles ou du contenu dégradant.'
        };
      case 'hateSpeech':
        return {
          label: 'Discours haineux',
          description: 'Contenu qui promeut la haine, la discrimination ou des préjugés basés sur l\'identité, l\'origine, la religion ou d\'autres caractéristiques.'
        };
      case 'sexualContent':
      case 'sexual':
        return {
          label: 'Contenu sexuel',
          description: 'Contenu à caractère sexuel explicite ou suggestif, incluant des références inappropriées ou des termes à connotation sexuelle.'
        };
      case 'violence':
        return {
          label: 'Violence',
          description: 'Contenu qui décrit, glorifie ou encourage la violence, les menaces physiques ou les actes dangereux envers autrui.'
        };
      case 'selfHarm':
        return {
          label: 'Automutilation',
          description: 'Contenu qui encourage, décrit ou fait référence à des actes d\'automutilation, de suicide ou de comportements autodestructeurs.'
        };
      case 'illegalActivity':
        return {
          label: 'Activité illégale',
          description: 'Contenu qui promeut, facilite ou fait référence à des activités illégales comme la vente de drogues, la fraude ou d\'autres crimes.'
        };
      case 'spam':
        return {
          label: 'Spam',
          description: 'Contenu non sollicité, répétitif, trompeur ou promotionnel. Inclut les tentatives de redirection vers d\'autres services ou plateformes.'
        };
      case 'phoneSpam':
        return {
          label: 'Numéro de téléphone',
          description: 'Contient un numéro de téléphone personnel. Les coordonnées personnelles doivent être échangées uniquement via la messagerie privée.'
        };
      case 'addressSpam':
        return {
          label: 'Adresse postale',
          description: 'Contient une adresse d\'habitation ou postale. Les coordonnées personnelles doivent être échangées uniquement via la messagerie privée.'
        };
      case 'adult':
        return {
          label: 'Contenu adulte',
          description: 'Contenu destiné aux adultes ou inapproprié pour un public général, incluant la nudité ou des thèmes matures.'
        };
      case 'dangerousContent':
        return {
          label: 'Contenu dangereux',
          description: 'Contenu présentant des situations dangereuses, des armes, des substances dangereuses ou des activités à risque.'
        };
      case 'poorQuality':
        return {
          label: 'Qualité insuffisante',
          description: 'Image de mauvaise qualité, floue, pixelisée ou ne respectant pas les standards professionnels requis.'
        };
      case 'irrelevantContent':
        return {
          label: 'Contenu non pertinent',
          description: 'Contenu qui ne correspond pas au contexte ou aux services proposés sur la plateforme.'
        };
      case 'misleadingContent':
        return {
          label: 'Contenu trompeur',
          description: 'Contenu qui induit en erreur, présente de fausses informations ou ne correspond pas à la description donnée.'
        };
      case 'unknownRisk':
        return {
          label: 'Risque inconnu',
          description: 'Contenu qui n\'a pas été classé dans une catégorie spécifique, mais qui présente un risque potentiel.'
        };
      default:
        return {
          label: category,
          description: 'Catégorie non reconnue'
        };
    }
  };

  // Fonction pour obtenir la couleur selon la catégorie
  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'harassment':
        return '#F44336'; // Rouge
      case 'hateSpeech':
        return '#9C27B0'; // Violet
      case 'sexualContent':
      case 'sexual':
        return '#E91E63'; // Rose
      case 'violence':
        return '#FF5722'; // Orange foncé
      case 'selfHarm':
        return '#795548'; // Marron
      case 'illegalActivity':
        return '#607D8B'; // Bleu-gris
      case 'spam':
        return '#FFC107'; // Jaune
      case 'phoneSpam':
        return '#00BCD4'; // Cyan
      case 'addressSpam':
        return '#009688'; // Teal
      case 'adult':
        return '#D32F2F'; // Rouge foncé
      case 'dangerousContent':
        return '#F57C00'; // Orange
      case 'poorQuality':
        return '#757575'; // Gris
      case 'irrelevantContent':
        return '#8D6E63'; // Marron clair
      case 'misleadingContent':
        return '#F9A825'; // Jaune foncé
      case 'unknownRisk':
        return '#D32A2F'; // Rouge
      default:
        return '#9E9E9E'; // Gris
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ color: '#1f1b18', fontWeight: 'bold', mb: 3 }}>
        Logs de modération de contenu automatique par intelligence artificielle
      </Typography>

      {/* Barre d'outils avec recherche et filtres */}
      <Paper sx={{ p: 2, mb: 3, borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <Box sx={{ flex: { md: 2 } }}>
            <TextField
              fullWidth
              label="Recherche"
              name="searchTerm"
              value={debouncedFilters.searchTerm}
              onChange={handleFilterChange}
              placeholder="Rechercher par ID, contenu..."
              variant="outlined"
              size="small"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {isTyping ? (
                      <HourglassEmptyIcon fontSize="small" color="action" />
                    ) : (
                      <SearchIcon fontSize="small" color="action" />
                    )}
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box sx={{ flex: { md: 2 } }}>
            <TextField
              fullWidth
              label="Email Utilisateur"
              name="userEmail"
              value={debouncedFilters.userEmail}
              onChange={handleFilterChange}
              placeholder="Rechercher par email..."
              variant="outlined"
              size="small"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {isTyping ? (
                      <HourglassEmptyIcon fontSize="small" color="action" />
                    ) : (
                      <SearchIcon fontSize="small" color="action" />
                    )}
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box sx={{ flex: 1, display: 'flex', gap: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              color="primary"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{ height: '40px' }}
            >
              Filtres
            </Button>
          </Box>
          <Box sx={{ flex: 1, display: 'flex', gap: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={() => fetchLogs()}
              sx={{ height: '40px' }}
            >
              Actualiser
            </Button>
          </Box>
          <Box sx={{ flex: 1, display: 'flex', gap: 2 }}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleApplyFilters}
              sx={{
                height: '40px',
                bgcolor: '#FF6B2C',
                '&:hover': { bgcolor: '#FF7A35' }
              }}
            >
              Appliquer
            </Button>
          </Box>
        </Box>

        {/* Filtres avancés */}
        {showFilters && (
          <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #eee' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Type de contenu</InputLabel>
                    <Select
                      name="contentType"
                      value={filters.contentType}
                      onChange={handleFilterChange}
                      label="Type de contenu"
                    >
                      <MenuItem value="">Tous</MenuItem>
                      <MenuItem value="mission">Mission</MenuItem>
                      <MenuItem value="comment">Commentaire</MenuItem>
                      <MenuItem value="profile_picture">Photo de profil</MenuItem> 
                      <MenuItem value="banner_picture">Bannière de profil</MenuItem>
                      <MenuItem value="profile">Photo de profil (ancien)</MenuItem>
                      <MenuItem value="avatar">Photo de profil (avatar)</MenuItem>
                      <MenuItem value="service_title">Titre de service</MenuItem>
                      <MenuItem value="service_description">Description de service</MenuItem>
                      <MenuItem value="review">Avis</MenuItem>
                      <MenuItem value="gallery">Image de galerie</MenuItem>
                      <MenuItem value="gallery_cover">Image de couverture</MenuItem>
                      <MenuItem value="featured">Image featured profil</MenuItem>
                      <MenuItem value="mission_assistant">Image assistant mission</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Statut</InputLabel>
                    <Select
                      name="isSafe"
                      value={filters.isSafe}
                      onChange={handleFilterChange}
                      label="Statut"
                    >
                      <MenuItem value="">Tous</MenuItem>
                      <MenuItem value="true">Sûr</MenuItem>
                      <MenuItem value="false">Inapproprié</MenuItem>
                      <MenuItem value="potential">Potentiellement problématique</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Méthode</InputLabel>
                    <Select
                      name="moderationMethod"
                      value={filters.moderationMethod}
                      onChange={handleFilterChange}
                      label="Méthode"
                    >
                      <MenuItem value="">Toutes</MenuItem>
                      <MenuItem value="basic">Liste de mots</MenuItem>
                      <MenuItem value="api">API générative (IA)</MenuItem>
                      <MenuItem value="gen">API générative (IA)</MenuItem>
                      <MenuItem value="image">Modération d'image</MenuItem>
                      <MenuItem value="temp">Modération temporaire</MenuItem>
                      <MenuItem value="local">Simulation locale</MenuItem>
                      <MenuItem value="error">Erreur</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Catégorie</InputLabel>
                    <Select
                      name="category"
                      value={filters.category}
                      onChange={handleFilterChange}
                      label="Catégorie"
                    >
                      <MenuItem value="">Toutes</MenuItem>
                      <MenuItem value="harassment">Harcèlement</MenuItem>
                      <MenuItem value="hateSpeech">Discours haineux</MenuItem>
                      <MenuItem value="sexualContent">Contenu sexuel</MenuItem>
                      <MenuItem value="violence">Violence</MenuItem>
                      <MenuItem value="selfHarm">Automutilation</MenuItem>
                      <MenuItem value="illegalActivity">Activité illégale</MenuItem>
                      <MenuItem value="spam">Spam</MenuItem>
                      <MenuItem value="phoneSpam">Numéro de téléphone</MenuItem>
                      <MenuItem value="addressSpam">Adresse postale</MenuItem>
                      <MenuItem value="unknownRisk">Risque inconnu</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, mt: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label="Date de début"
                    name="dateFrom"
                    type="date"
                    value={filters.dateFrom}
                    onChange={handleFilterChange}
                    sx={{ '& .MuiInputLabel-root': { transform: 'translate(14px, -9px) scale(0.75)' } }}
                    size="small"
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label="Date de fin"
                    name="dateTo"
                    type="date"
                    value={filters.dateTo}
                    onChange={handleFilterChange}
                    sx={{ '& .MuiInputLabel-root': { transform: 'translate(14px, -9px) scale(0.75)' } }}
                    size="small"
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label="Score minimum"
                    name="scoreMin"
                    type="number"
                    InputProps={{
                      inputProps: { min: 0, max: 1, step: 0.1 }
                    }}
                    value={filters.scoreMin}
                    onChange={handleFilterChange}
                    placeholder="0.0 - 1.0"
                    size="small"
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <TextField
                    fullWidth
                    label="Score maximum"
                    name="scoreMax"
                    type="number"
                    InputProps={{
                      inputProps: { min: 0, max: 1, step: 0.1 }
                    }}
                    value={filters.scoreMax}
                    onChange={handleFilterChange}
                    placeholder="0.0 - 1.0"
                    size="small"
                  />
                </Box>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <TextField
                      fullWidth
                      label="ID Utilisateur"
                      name="userId"
                      value={filters.userId}
                      onChange={handleFilterChange}
                      placeholder="UUID de l'utilisateur"
                      size="small"
                    />
                  </Box>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="secondary"
                    onClick={handleResetFilters}
                    sx={{ height: '40px' }}
                  >
                    Réinitialiser les filtres
                  </Button>
                </Box>
                <Box sx={{ flex: 1 }}></Box>
                <Box sx={{ flex: 1 }}></Box>
              </Box>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Tableau des logs */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {infoMessage && (
          <Alert
            severity="info"
            sx={{
              mb: 2,
              '& .MuiAlert-message': {
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }
            }}
            icon={<FilterListIcon />}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 0.5 }}>
              <Typography component="span" fontWeight="medium">Filtrage :</Typography>
              {infoMessage}
              {filters.userEmail && (
                <Button
                  size="small"
                  variant="outlined"
                  color="info"
                  onClick={handleResetFilters}
                  sx={{ ml: 1, minHeight: '24px', py: 0 }}
                >
                  Réinitialiser les filtres
                </Button>
              )}
            </Box>
          </Alert>
        )}

        <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell
                  onClick={() => handleSort('created_at')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Date
                  {sortBy === 'created_at' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  onClick={() => handleSort('content_type')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Type
                  {sortBy === 'content_type' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  onClick={() => handleSort('user_name')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Utilisateur
                  {sortBy === 'user_name' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  onClick={() => handleSort('is_safe')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Statut
                  {sortBy === 'is_safe' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  onClick={() => handleSort('score')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Score
                  {sortBy === 'score' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  onClick={() => handleSort('moderation_id')}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18',
                    '&:hover': { backgroundColor: '#FFD4A0' }
                  }}
                >
                  Méthode
                  {sortBy === 'moderation_id' && (
                    <span>{sortOrder === 'asc' ? ' ▲' : ' ▼'}</span>
                  )}
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18'
                  }}
                >
                  Catégories
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: '#FFE4BA',
                    fontWeight: 'bold',
                    color: '#1f1b18'
                  }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
                  </TableCell>
                </TableRow>
              ) : logs.filter(log => !log.categories.clean).length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    Aucun log de modération trouvé
                  </TableCell>
                </TableRow>
              ) : (
                logs.filter(log => !log.categories.clean).map((log) => (
                  <TableRow
                    key={log.id}
                    hover
                    onClick={() => handleOpenDetail(log)}
                    sx={{
                      cursor: 'pointer',
                      // Mettre en évidence les contenus potentiellement problématiques mais marqués comme sûrs
                      ...(log.is_safe && log.score > 0.3 ? {
                        bgcolor: 'rgba(255, 152, 0, 0.08)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 152, 0, 0.15)',
                        }
                      } : {})
                    }}
                  >
                    <TableCell>{formatDate(log.created_at)}</TableCell>
                    <TableCell>
                      <Chip
                        label={getContentTypeLabel(log.content_type)}
                        size="small"
                        sx={{
                          bgcolor: getTypeStyle(log.content_type).bgcolor,
                          color: getTypeStyle(log.content_type).color,
                          fontWeight: 'medium'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar
                          sx={{
                            bgcolor: '#FF6B2C',
                            width: 24,
                            height: 24,
                            fontSize: '0.8rem'
                          }}
                        >
                          {log.user_email ? log.user_email.charAt(0).toUpperCase() : '?'}
                        </Avatar>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium', lineHeight: 1.2 }}>
                            {log.user_name && log.user_name !== 'Utilisateur inconnu' && !log.user_name.includes('undefined')
                              ? log.user_name
                              : log.user_email
                                ? log.user_email.split('@')[0]
                                : 'Utilisateur inconnu'}
                          </Typography>
                          <Typography variant="caption" color="textSecondary" sx={{ lineHeight: 1.2 }}>
                            {log.user_email || 'Email non disponible'}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={log.is_safe ? 'Sûr' : 'Inapproprié'}
                          color={log.is_safe ? 'success' : 'error'}
                          size="small"
                        />
                        {/* Icône d'avertissement pour les contenus potentiellement problématiques */}
                        {log.is_safe && log.score > 0.3 && (
                          <Tooltip title="Contenu potentiellement problématique qui a passé la modération">
                            <InfoOutlinedIcon color="warning" fontSize="small" />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${Math.round(log.score * 100)}%`}
                        color={getScoreColor(log.score)}
                        size="small"
                        sx={{ minWidth: '60px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip
                        title={getModerationTypeDescription(log.moderation_id, log.content_type, log.is_image_moderation)}
                        arrow
                      >
                        <Chip
                          label={getModerationType(log.moderation_id, log.is_image_moderation, log.content_type)}
                          color={getModerationTypeColor(log.moderation_id, log.content_type, log.is_image_moderation)}
                          size="small"
                          sx={{ fontSize: '0.7rem', height: '20px' }}
                        />
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Object.entries(log.categories).map(([category, isDetected]) =>
                          isDetected && (
                            <Tooltip
                              key={category}
                              title={getCategoryLabel(category).description}
                              arrow
                            >
                              <Chip
                                label={getCategoryLabel(category).label}
                                size="small"
                                sx={{
                                  fontSize: '0.7rem',
                                  height: '20px',
                                  bgcolor: getCategoryColor(category),
                                  color: 'white',
                                  fontWeight: 'bold'
                                }}
                              />
                            </Tooltip>
                          )
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Voir les détails">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDetail(log)}
                          sx={{ color: '#FF6B2C' }}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count}`}
        />
      </Paper>

      {/* Dialogue de détail */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#1f1b18', fontWeight: 'bold' }}>
          Détails de la modération
          <IconButton
            aria-label="close"
            onClick={() => setDetailDialogOpen(false)}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedLog && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="textSecondary">ID utilisateur</Typography>
                  <Typography variant="body1" gutterBottom>{selectedLog.id}</Typography>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Type de contenu</Typography>
                  <Typography variant="body1" gutterBottom>{getContentTypeLabel(selectedLog.content_type)}</Typography>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>ID du contenu</Typography>
                  <Typography variant="body1" gutterBottom>{selectedLog.content_id}</Typography>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Informations utilisateur</Typography>
                  <Box sx={{
                    mt: 0.5,
                    p: 2,
                    bgcolor: '#f5f5f5',
                    borderRadius: 1,
                    border: '1px solid #e0e0e0',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1
                  }}>
                    {/* En-tête avec avatar et nom */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar
                        src={selectedLog.user_info?.photo_url || undefined}
                        sx={{
                          bgcolor: '#FF6B2C',
                          width: 40,
                          height: 40,
                          fontSize: '1rem'
                        }}
                      >
                        {selectedLog.user_email ? selectedLog.user_email.charAt(0).toUpperCase() : '?'}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {selectedLog.user_name && selectedLog.user_name !== 'Utilisateur inconnu' && !selectedLog.user_name.includes('undefined')
                            ? selectedLog.user_name
                            : selectedLog.user_email
                              ? selectedLog.user_email.split('@')[0]
                              : 'Utilisateur inconnu'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {selectedLog.user_email || 'Email non disponible'}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Informations de base */}
                    <Accordion
                      disableGutters
                      elevation={0}
                      sx={{
                        bgcolor: 'transparent',
                        '&:before': { display: 'none' },
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        mt: 1
                      }}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        sx={{ bgcolor: '#f9f9f9' }}
                      >
                        <Typography variant="subtitle2">Informations de base</Typography>
                      </AccordionSummary>
                      <AccordionDetails sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              ID Utilisateur:
                            </Typography>
                            <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                              {selectedLog.user_id}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Type d'utilisateur:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.user_type === 'jobbeur' ? 'Jobbeur' : 'Client'}
                              </Typography>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Rôle:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.role === 'jobpadm' ? 'Administrateur' :
                                 selectedLog.user_info?.role === 'jobmodo' ? 'Modérateur' : 'Utilisateur'}
                              </Typography>
                            </Box>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Date d'inscription:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.date_inscription
                                  ? new Date(selectedLog.user_info.date_inscription).toLocaleDateString('fr-FR')
                                  : 'Non disponible'}
                              </Typography>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Dernière connexion:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.last_login
                                  ? new Date(selectedLog.user_info.last_login).toLocaleDateString('fr-FR')
                                  : 'Non disponible'}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </AccordionDetails>
                    </Accordion>

                    {/* Coordonnées */}
                    <Accordion
                      disableGutters
                      elevation={0}
                      sx={{
                        bgcolor: 'transparent',
                        '&:before': { display: 'none' },
                        border: '1px solid #e0e0e0',
                        borderRadius: 1
                      }}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        sx={{ bgcolor: '#f9f9f9' }}
                      >
                        <Typography variant="subtitle2">Coordonnées</Typography>
                      </AccordionSummary>
                      <AccordionDetails sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Téléphone:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.telephone || 'Non renseigné'}
                                {selectedLog.user_info?.telephone_prive && ' (privé)'}
                              </Typography>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Ville:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.ville || 'Non renseigné'}
                              </Typography>
                            </Box>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Code postal:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.code_postal || 'Non renseigné'}
                              </Typography>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                Pays:
                              </Typography>
                              <Typography variant="body2">
                                {selectedLog.user_info?.pays || 'Non renseigné'}
                              </Typography>
                            </Box>
                          </Box>

                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              Slug du profil:
                            </Typography>
                            <Typography variant="body2">
                              {selectedLog.user_info?.slug || 'Non renseigné'}
                            </Typography>
                          </Box>
                        </Box>
                      </AccordionDetails>
                    </Accordion>

                    {/* Vérifications */}
                    <Accordion
                      disableGutters
                      elevation={0}
                      sx={{
                        bgcolor: 'transparent',
                        '&:before': { display: 'none' },
                        border: '1px solid #e0e0e0',
                        borderRadius: 1
                      }}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        sx={{ bgcolor: '#f9f9f9' }}
                      >
                        <Typography variant="subtitle2">Statut du compte</Typography>
                      </AccordionSummary>
                      <AccordionDetails sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.profil_actif
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Compte {selectedLog.user_info?.profil_actif ? 'actif' : 'inactif'}
                                </Typography>
                              </Box>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.email_verifier
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Email {selectedLog.user_info?.email_verifier ? 'vérifié' : 'non vérifié'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.profil_verifier
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Profil {selectedLog.user_info?.profil_verifier ? 'vérifié' : 'non vérifié'}
                                </Typography>
                              </Box>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.identite_verifier
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Identité {selectedLog.user_info?.identite_verifier ? 'vérifiée' : 'non vérifiée'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>

                          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.entreprise_verifier
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Entreprise {selectedLog.user_info?.entreprise_verifier ? 'vérifiée' : 'non vérifiée'}
                                </Typography>
                              </Box>
                            </Box>

                            <Box sx={{ flex: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {selectedLog.user_info?.assurance_verifier
                                  ? <CheckCircleIcon color="success" fontSize="small" />
                                  : <CancelIcon color="error" fontSize="small" />}
                                <Typography variant="body2">
                                  Assurance {selectedLog.user_info?.assurance_verifier ? 'vérifiée' : 'non vérifiée'}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  </Box>
                </Box>

                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" color="textSecondary">Date de modération</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(selectedLog.created_at)}</Typography>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Statut</Typography>
                  <Chip
                    label={selectedLog.is_safe ? 'Contenu sûr' : 'Contenu inapproprié'}
                    color={selectedLog.is_safe ? 'success' : 'error'}
                    sx={{ mt: 0.5 }}
                  />
                  {/* Afficher un avertissement si le statut et le score sont incohérents */}
                  {(selectedLog.is_safe === false && selectedLog.score < 0.1 &&
                    !Object.values(selectedLog.categories).some(val => val === true)) && (
                    <Alert severity="warning" sx={{ mt: 1, fontSize: '0.8rem' }}>
                      Incohérence détectée : contenu marqué comme inapproprié par l'API mais avec un score de risque très faible et aucune catégorie détectée ...
                    </Alert>
                  )}

                  {/* Afficher un avertissement pour les contenus qui passent sans modération mais avec un score élevé */}
                  {(selectedLog.is_safe === true && selectedLog.score > 0.3) && (
                    <Alert severity="warning" sx={{ mt: 1, fontSize: '0.8rem' }}>
                      Attention : contenu marqué comme sûr mais avec un score de risque élevé ({Math.round(selectedLog.score * 100)}%). Ce contenu pourrait nécessiter une vérification manuelle.
                    </Alert>
                  )}

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Score de risque</Typography>
                  <Chip
                    label={`${Math.round(selectedLog.score * 100)}%`}
                    color={getScoreColor(selectedLog.score)}
                    sx={{ mt: 0.5 }}
                  />

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Catégories détectées</Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 0.5 }}>
                    {Object.entries(selectedLog.categories).some(([_, isDetected]) => isDetected) ? (
                      Object.entries(selectedLog.categories).map(([category, isDetected]) =>
                        isDetected && (
                          <Box key={category} sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                            <Chip
                              icon={<InfoOutlinedIcon style={{ color: 'white', fontSize: '16px' }} />}
                              label={getCategoryLabel(category).label}
                              size="small"
                              sx={{
                                bgcolor: getCategoryColor(category),
                                color: 'white',
                                fontWeight: 'bold',
                                alignSelf: 'flex-start',
                                cursor: 'pointer',
                                '&:hover': {
                                  opacity: 0.9,
                                  boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                                }
                              }}
                              onClick={() => toggleCategoryDescription(category)}
                            />
                            <Collapse in={expandedCategories[category]} timeout="auto" unmountOnExit>
                              <Typography variant="caption" color="textSecondary" sx={{ ml: 1, mt: 0.5, display: 'block' }}>
                                {getCategoryLabel(category).description}
                              </Typography>
                            </Collapse>
                          </Box>
                        )
                      )
                    ) : (
                      <Typography variant="body2" color="textSecondary">Aucune catégorie détectée</Typography>
                    )}
                  </Box>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>Méthode de modération</Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                    <Chip
                      icon={<InfoOutlinedIcon style={{ fontSize: '16px' }} />}
                      label={getModerationType(selectedLog.moderation_id, selectedLog.is_image_moderation, selectedLog.content_type)}
                      color={getModerationTypeColor(selectedLog.moderation_id, selectedLog.content_type, selectedLog.is_image_moderation)}
                      size="small"
                      sx={{
                        mt: 0.5,
                        alignSelf: 'flex-start',
                        cursor: 'pointer',
                        '&:hover': {
                          opacity: 0.9,
                          boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                        }
                      }}
                      onClick={() => toggleCategoryDescription('moderation_method')}
                    />
                    <Collapse in={expandedCategories['moderation_method']} timeout="auto" unmountOnExit>
                      <Typography variant="caption" color="textSecondary" sx={{ ml: 1, mt: 0.5, display: 'block' }}>
                        {getModerationTypeDescription(selectedLog.moderation_id)}
                      </Typography>
                    </Collapse>
                  </Box>

                  <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>ID de modération</Typography>
                  <Typography variant="body1" gutterBottom>{selectedLog.moderation_id}</Typography>
                </Box>
              </Box>

              <Box sx={{ width: '100%' }}>
                <Divider sx={{ my: 2 }} />
                {(selectedLog.moderation_id.startsWith('image-') ||
                  (selectedLog.moderation_id.startsWith('gen-') &&
                    selectedLog.is_image_moderation &&
                    [
                      'gallery',
                      'gallery_cover',
                      'featured',
                      'mission_assistant',
                      'avatar',
                      'profile_picture',
                      'banner_picture'
                    ].includes(selectedLog.content_type)
                  )
                ) ? (
                  // Bloc image
                  <>
                    {/* Affichage de l'image modérée si disponible */}
                    {selectedLog.image_url ? (
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                          <img
                            src={selectedLog.image_url}
                            alt="Image modérée"
                            style={{
                              maxWidth: 220,
                              maxHeight: 220,
                              borderRadius: 8,
                              border: '2px solid #FFE4BA',
                              background: '#FFF8F3',
                              objectFit: 'cover',
                            }}
                            onError={e => {
                              (e.target as HTMLImageElement).style.display = 'none';
                              const fallback = document.getElementById('image-moderation-fallback');
                              if (fallback) fallback.style.display = 'flex';
                            }}
                          />
                          <Box id="image-moderation-fallback" style={{ display: 'none', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: 220, width: 220, background: '#FFF8F3', border: '2px dashed #FFE4BA', borderRadius: 8 }}>
                            <ImageIcon style={{ fontSize: 64, color: '#FF965E', marginBottom: 8 }} />
                            <Typography variant="body2" color="textSecondary">Image supprimée du stockage</Typography>
                          </Box>
                          {/* Bouton corbeille pour supprimer l'image */}
                          <IconButton
                            aria-label="Supprimer l'image"
                            sx={{ ml: 1, color: '#FF6B2C', bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', '&:hover': { bgcolor: '#FFE4BA' } }}
                            onClick={async () => {
                              if (window.confirm('Supprimer définitivement cette image du stockage ?')) {
                                try {
                                  // const headers = await getCommonHeaders();
                                  // await axios.post(
                                  //   `${API_CONFIG.baseURL}/api/content-moderation/delete-image`,
                                  //   {
                                  //     userId: selectedLog.user_id,
                                  //     filePath: selectedLog.image_path
                                  //   },
                                  //   {
                                  //     headers,
                                  //     withCredentials: true
                                  //   }
                                  // );
                                  // notify('Image supprimée du stockage', 'success');
                                  notify('Désactiver pour le moment car ca supprime dans le bucket au lieu de celui du profil ... ', 'success');
                                  // Rafraîchir la modale (recharger les logs et fermer/réouvrir la modale)
                                  fetchLogs();
                                  setDetailDialogOpen(false);
                                } catch (err) {
                                  notify("Erreur lors de la suppression de l'image", 'error');
                                }
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                        {/* Description si présente */}
                        {selectedLog.description && (
                          <>
                            <Typography variant="subtitle2" color="textSecondary">Description de l'image</Typography>
                            <Box sx={{ mt: 1, p: 2, bgcolor: '#FFF8F3', borderRadius: 1, border: '1px solid #FFE4BA' }}>
                              <Typography variant="body2" gutterBottom>
                                {selectedLog.description}
                              </Typography>
                            </Box>
                          </>
                        )}
                        {/* Bloc d'évaluation TOUJOURS affiché si qualityAssessment existe */}
                        {selectedLog.qualityAssessment && (
                          <>
                            <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2, mb: 1 }}>
                              Évaluation de la qualité de l'image
                            </Typography>
                            <Box sx={{
                              p: { xs: 2, md: 2.5 },
                              bgcolor: '#FFF8F3',
                              borderRadius: 2,
                              border: '1px solid #FFE4BA',
                              boxShadow: '0 1px 4px rgba(255, 123, 53, 0.04)',
                              maxWidth: 480,
                              mx: 'auto',
                            }}>
                              {/* Titre explicatif */}
                              <Typography variant="body2" color="textSecondary" sx={{ mb: 2, textAlign: 'center' }}>
                                Évaluation sur une échelle de 1 à 10 (10 étant excellent)
                              </Typography>

                              {/* Conteneur des métriques */}
                              <Box sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 2,
                                justifyContent: 'center',
                                flexWrap: { xs: 'wrap', sm: 'nowrap' }
                              }}>
                                {/* Clarté */}
                                <Box sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  minWidth: 80,
                                  p: 1,
                                  borderRadius: 1,
                                  bgcolor: 'rgba(255, 150, 94, 0.08)',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: 'rgba(255, 150, 94, 0.15)',
                                    transform: 'translateY(-2px)'
                                  }
                                }}>
                                  <Box sx={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '50%',
                                    bgcolor: '#FF965E',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mb: 1,
                                    boxShadow: '0 2px 4px rgba(255, 150, 94, 0.2)'
                                  }}>
                                    <ImageIcon sx={{ color: 'white', fontSize: 22 }} />
                                  </Box>
                                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: 20, color: '#FF965E' }}>
                                    {selectedLog.qualityAssessment.clarity}/10
                                  </Typography>
                                  <Typography variant="subtitle2" sx={{ fontWeight: 500, fontSize: 13 }}>
                                    Clarté
                                  </Typography>
                                  <Tooltip title="Netteté et qualité visuelle de l'image" arrow placement="top">
                                    <InfoOutlinedIcon sx={{ fontSize: 14, color: '#FF965E', mt: 0.5, cursor: 'help' }} />
                                  </Tooltip>
                                </Box>

                                {/* Pertinence */}
                                <Box sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  minWidth: 80,
                                  p: 1,
                                  borderRadius: 1,
                                  bgcolor: 'rgba(255, 122, 53, 0.08)',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: 'rgba(255, 122, 53, 0.15)',
                                    transform: 'translateY(-2px)'
                                  }
                                }}>
                                  <Box sx={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '50%',
                                    bgcolor: '#FF7A35',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mb: 1,
                                    boxShadow: '0 2px 4px rgba(255, 122, 53, 0.2)'
                                  }}>
                                    <InfoOutlinedIcon sx={{ color: 'white', fontSize: 22 }} />
                                  </Box>
                                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: 20, color: '#FF7A35' }}>
                                    {selectedLog.qualityAssessment.relevance}/10
                                  </Typography>
                                  <Typography variant="subtitle2" sx={{ fontWeight: 500, fontSize: 13 }}>
                                    Pertinence
                                  </Typography>
                                  <Tooltip title="Adéquation avec le service proposé" arrow placement="top">
                                    <InfoOutlinedIcon sx={{ fontSize: 14, color: '#FF7A35', mt: 0.5, cursor: 'help' }} />
                                  </Tooltip>
                                </Box>

                                {/* Apparence professionnelle */}
                                <Box sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  minWidth: 80,
                                  p: 1,
                                  borderRadius: 1,
                                  bgcolor: 'rgba(255, 107, 44, 0.08)',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: 'rgba(255, 107, 44, 0.15)',
                                    transform: 'translateY(-2px)'
                                  }
                                }}>
                                  <Box sx={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '50%',
                                    bgcolor: '#FF6B2C',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mb: 1,
                                    boxShadow: '0 2px 4px rgba(255, 107, 44, 0.2)'
                                  }}>
                                    <CheckCircleIcon sx={{ color: 'white', fontSize: 22 }} />
                                  </Box>
                                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: 20, color: '#FF6B2C' }}>
                                    {selectedLog.qualityAssessment.professionalAppearance}/10
                                  </Typography>
                                  <Typography variant="subtitle2" sx={{ fontWeight: 500, fontSize: 13 }}>
                                    Apparence
                                  </Typography>
                                  <Tooltip title="Aspect professionnel et soigné de l'image" arrow placement="top">
                                    <InfoOutlinedIcon sx={{ fontSize: 14, color: '#FF6B2C', mt: 0.5, cursor: 'help' }} />
                                  </Tooltip>
                                </Box>

                                {/* Note globale */}
                                <Box sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  minWidth: 80,
                                  p: 1,
                                  borderRadius: 1,
                                  bgcolor: 'rgba(255, 228, 186, 0.3)',
                                  border: '1px solid rgba(255, 228, 186, 0.8)',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: 'rgba(255, 228, 186, 0.5)',
                                    transform: 'translateY(-2px)'
                                  }
                                }}>
                                  <Box sx={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '50%',
                                    bgcolor: '#FF965E',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mb: 1,
                                    boxShadow: '0 2px 4px rgba(255, 150, 94, 0.2)'
                                  }}>
                                    <StarIcon sx={{ color: 'white', fontSize: 22 }} />
                                  </Box>
                                  <Typography variant="h6" sx={{ fontWeight: 600, fontSize: 20, color: '#FF965E' }}>
                                    {selectedLog.qualityAssessment.overall}/10
                                  </Typography>
                                  <Typography variant="subtitle2" sx={{ fontWeight: 500, fontSize: 13 }}>
                                    Note globale
                                  </Typography>
                                  <Tooltip title="Évaluation générale de la qualité de l'image" arrow placement="top">
                                    <InfoOutlinedIcon sx={{ fontSize: 14, color: '#FF965E', mt: 0.5, cursor: 'help' }} />
                                  </Tooltip>
                                </Box>
                              </Box>
                            </Box>
                          </>
                        )}
                        {/* Suggestions et pertinence */}
                        {(selectedLog.serviceType || selectedLog.relevantToUserServices !== undefined) && (
                          <Box sx={{ mt: 2, p: 1.5, bgcolor: 'rgba(255, 107, 44, 0.1)', borderRadius: 1, border: '1px dashed #FF6B2C' }}>
                            {selectedLog.serviceType && (
                              <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 'medium' }}>
                                Type de service identifié : {selectedLog.serviceType}
                              </Typography>
                            )}
                            {selectedLog.relevantToUserServices !== undefined && (
                              <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 'medium', mt: 1 }}>
                                {selectedLog.relevantToUserServices
                                  ? "✓ L'image est pertinente par rapport aux services proposés"
                                  : "✗ L'image n'est pas pertinente par rapport aux services proposés"}
                              </Typography>
                            )}
                          </Box>
                        )}
                        {selectedLog.improvementSuggestions && (
                          <Box sx={{ mt: 2, p: 1.5, bgcolor: 'rgba(255, 152, 0, 0.1)', borderRadius: 1, border: '1px dashed #FFA000' }}>
                            <Typography variant="body2" sx={{ color: '#FFA000', fontWeight: 'medium' }}>
                              <strong>Suggestions d'amélioration :</strong> {selectedLog.improvementSuggestions}
                            </Typography>
                          </Box>
                        )}
                      </>
                    ) : (
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: 220, width: 220, background: '#FFF8F3', border: '2px dashed #FFE4BA', borderRadius: 2, mb: 2 }}>
                        <ImageIcon style={{ fontSize: 64, color: '#FF965E', marginBottom: 8 }} />
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                          L'image a été supprimée du stockage.
                        </Typography>
                      </Box>
                    )}
                  </>
                ) : (
                  // Bloc texte signalé
                  <>
                    <Typography variant="subtitle2" color="textSecondary">Texte signalé/trouvé</Typography>
                    {selectedLog.flagged_text && selectedLog.flagged_text.length > 0 ? (
                      // Afficher les textes signalés s'ils existent
                      selectedLog.flagged_text.map((text, index) => (
                        <Typography key={index} variant="body2" gutterBottom>
                          • "{text}"
                        </Typography>
                      ))
                    ) : selectedLog.moderation_id && (selectedLog.moderation_id.startsWith('gen-') || selectedLog.moderation_id.startsWith('api-')) ? (
                      // Message par défaut pour les modérations par IA sans texte signalé spécifique
                      <Typography variant="body2" gutterBottom sx={{ fontStyle: 'italic' }}>
                        • L'IA a détecté un contenu potentiellement inapproprié dans ce texte, mais n'a pas spécifié de passage précis.
                      </Typography>
                    ) : null}

                    {/* Explication supplémentaire pour la modération par liste de mots */}
                    {selectedLog.moderation_id.startsWith('basic-') && (
                      <Box sx={{ mt: 2, p: 1.5, bgcolor: 'rgba(255, 107, 44, 0.1)', borderRadius: 1, border: '1px dashed #FF6B2C' }}>
                        <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#FF6B2C', fontWeight: 'medium' }}>
                          <strong>Note :</strong> Ce contenu a été détecté par notre système de filtrage automatique basé sur une liste de mots interdits,
                          d'expressions inappropriées, ou de coordonnées personnelles (numéros de téléphone, adresses).
                          {selectedLog.categories.phoneSpam && " Un numéro de téléphone a été détecté dans le texte."}
                          {selectedLog.categories.addressSpam && " Une adresse postale a été détectée dans le texte."}
                          {selectedLog.categories.unknownRisk && " Un contenu potentiellement inapproprié a été détecté dans le texte."}
                          {(selectedLog.categories.harassment || selectedLog.categories.hateSpeech) &&
                            " Des termes potentiellement offensants ou inappropriés ont été détectés."}
                        </Typography>
                      </Box>
                    )}

                    {/* Explication supplémentaire pour la modération par IA */}
                    {(selectedLog.moderation_id.startsWith('gen-') || selectedLog.moderation_id.startsWith('api-')) && (
                      <Box sx={{ mt: 2, p: 1.5, bgcolor: 'rgba(25, 118, 210, 0.1)', borderRadius: 1, border: '1px dashed #1976D2' }}>
                        <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#1976D2', fontWeight: 'medium' }}>
                          <strong>Note :</strong> Ce contenu a été analysé par notre système d'intelligence artificielle qui évalue le contexte et le contenu pour détecter des propos inappropriés.
                          {selectedLog.categories.phoneSpam && " Un numéro de téléphone a été détecté dans le texte."}
                          {selectedLog.categories.addressSpam && " Une adresse postale a été détectée dans le texte."}
                          {selectedLog.categories.unknownRisk && " Un contenu potentiellement inapproprié a été détecté dans le texte."}
                          {(selectedLog.categories.harassment || selectedLog.categories.hateSpeech) &&
                            " Des propos potentiellement offensants ou inappropriés ont été détectés."}
                          {selectedLog.categories.spam && " Du contenu promotionnel ou non pertinent a été détecté."}
                        </Typography>
                      </Box>
                    )}
                  </>
                )}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setDetailDialogOpen(false)}
            variant="contained"
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': { bgcolor: '#FF7A35' }
            }}
          >
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContentModerationLogsPage;
