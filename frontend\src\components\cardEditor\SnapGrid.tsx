import React from 'react';
import { Line, Group } from 'react-konva';

interface SnapGridProps {
  width: number;
  height: number;
  blockSnapSize: number;
  visible: boolean;
}

/**
 * Composant qui affiche une grille pour faciliter l'alignement des éléments
 */
const SnapGrid: React.FC<SnapGridProps> = ({ width, height, blockSnapSize, visible }) => {
  if (!visible) return null;

  const horizontalLines = [];
  const verticalLines = [];

  // Créer les lignes horizontales
  for (let i = 0; i <= Math.ceil(height / blockSnapSize); i++) {
    horizontalLines.push(
      <Line
        key={`h-${i}`}
        points={[0, i * blockSnapSize, width, i * blockSnapSize]}
        stroke="#ddd"
        strokeWidth={0.5}
        perfectDrawEnabled={false}
        listening={false}
      />
    );
  }

  // Créer les lignes verticales
  for (let i = 0; i <= Math.ceil(width / blockSnapSize); i++) {
    verticalLines.push(
      <Line
        key={`v-${i}`}
        points={[i * blockSnapSize, 0, i * blockSnapSize, height]}
        stroke="#ddd"
        strokeWidth={0.5}
        perfectDrawEnabled={false}
        listening={false}
      />
    );
  }

  return (
    <Group>
      {horizontalLines}
      {verticalLines}
    </Group>
  );
};

export default SnapGrid;
