import React, { useState } from 'react';
import { TextField, InputAdornment, styled, Button, Box, IconButton, useMediaQuery, useTheme } from '@mui/material';
import { Search, X } from 'lucide-react';

const StyledSearchField = styled(TextField)({
  flex: 1,
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    backgroundColor: 'white',
    transition: 'all 0.3s ease',
    '& fieldset': {
      borderColor: '#FFE4BA',
      borderWidth: '2px',
    },
    '&:hover fieldset': {
      borderColor: '#FF965E',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiInputBase-input': {
    padding: '8px 12px',
    fontSize: '0.9rem',
    '@media (max-width: 768px)': {
      padding: '8px',
      fontSize: '0.85rem',
    }
  },
  '& .MuiInputAdornment-root': {
    color: '#FF6B2C',
  },
});

const SearchButton = styled(Button)({
  marginLeft: '8px',
  backgroundColor: '#FF6B2C',
  color: 'white',
  borderRadius: '8px',
  padding: '5px 12px',
  minWidth: 'auto',
  whiteSpace: 'nowrap',
  fontSize: '0.85rem',
  '&:hover': {
    backgroundColor: '#FF965E',
  },
  '@media (max-width: 768px)': {
    marginLeft: '4px',
    padding: '8px 10px',
    minWidth: 'auto',
  }
});

const SearchContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  width: '100%',
  maxWidth: '100%',
  '@media (max-width: 900px)': {
    width: '100%',
    maxWidth: '100%',
  }
});

interface SearchFieldProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  onClear?: () => void;
}

export const SearchField: React.FC<SearchFieldProps> = ({
  value,
  onChange,
  placeholder = "Rechercher une mission ...",
  onClear
}) => {
  const [localValue, setLocalValue] = useState(value);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleLocalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const event = {
      target: {
        value: localValue
      }
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(event);
  };

  const handleClear = () => {
    setLocalValue('');
    if (onClear) {
      onClear();
    } else {
      // Si onClear n'est pas fourni, simuler un changement avec une valeur vide
      const event = {
        target: {
          value: ''
        }
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(event);
    }
  };

  return (
    <form onSubmit={handleSubmit} style={{ width: '100%', maxWidth: '100%' }}>
      <SearchContainer>
        <StyledSearchField
          fullWidth
          placeholder={placeholder}
          value={localValue}
          onChange={handleLocalChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={16} />
              </InputAdornment>
            ),
            endAdornment: localValue ? (
              <InputAdornment position="end">
                <IconButton 
                  onClick={handleClear}
                  edge="end"
                  sx={{ color: '#FF6B2C', padding: '4px' }}
                >
                  <X size={14} />
                </IconButton>
              </InputAdornment>
            ) : null,
          }}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSubmit(e);
            }
          }}
        />
        <SearchButton type="submit" size="small">
          {isMobile ? <Search size={16} /> : "Rechercher"}
        </SearchButton>
      </SearchContainer>
    </form>
  );
};

export default SearchField; 