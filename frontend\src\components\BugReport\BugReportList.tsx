import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Chip,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  TableSortLabel,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Tooltip,
  TextField,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  Card,
  CardContent,
  Divider,
  useMediaQuery
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import SearchIcon from '@mui/icons-material/Search';
import SwipeIcon from '@mui/icons-material/SwipeOutlined';
import SwipeRightIcon from '@mui/icons-material/SwipeRight';
import { useNavigate } from 'react-router-dom';
import { bugReportService } from '../../services/bugReportService';
import {
  BugReport,
  BugReportFilters,
  ReportType,
  ReportCategory,
  ReportStatus,
  ReportPriority
} from '../../types/bugReports';
import { useTheme } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import CloseTwoToneIcon from '@mui/icons-material/CloseTwoTone';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import BugReportDetail from './BugReportDetail';
import ModalPortal from '../ModalPortal';
import LockIcon from '@mui/icons-material/Lock';
import ProfileCompleteCheck from './ProfileCompleteCheck';
import logger from '@/utils/logger';
import { notify } from '../Notification';

// Formatage du status pour l'affichage avec style personnalisé
const getStatusChipProps = (status: ReportStatus) => {
  switch (status) {
    case 'nouveau':
      return { 
        label: 'Nouveau', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 107, 44, 0.1)', color: '#FF6B2C', fontWeight: 500 }
      };
    case 'en_cours':
      return { 
        label: 'En cours', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 122, 53, 0.1)', color: '#FF7A35', fontWeight: 500 }
      };
    case 'resolu':
      return { 
        label: 'Résolu', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(46, 125, 50, 0.1)', color: '#2E7D32', fontWeight: 500 }
      };
    case 'rejete':
      return { 
        label: 'Rejeté', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    case 'reouvert':
      return { 
        label: 'Réouvert', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 152, 0, 0.1)', color: '#FF9800', fontWeight: 500 }
      };
    case 'attente_moderation':
      return { 
        label: 'En attente de modération', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(63, 81, 181, 0.1)', color: '#3F51B5', fontWeight: 500 }
      };
    default:
      return { 
        label: status, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage du type pour l'affichage avec style personnalisé
const getTypeChipProps = (type: ReportType) => {
  switch (type) {
    case 'bug':
      return { 
        label: 'Bug', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    case 'improvement':
      return { 
        label: 'Amélioration', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 150, 94, 0.1)', color: '#FF965E', fontWeight: 500 }
      };
    default:
      return { 
        label: type, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage de la priorité pour l'affichage avec style personnalisé
const getPriorityChipProps = (priority: ReportPriority) => {
  switch (priority) {
    case 'faible':
      return { 
        label: 'Faible', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(46, 125, 50, 0.1)', color: '#2E7D32', fontWeight: 500 }
      };
    case 'moyenne':
      return { 
        label: 'Moyenne', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 150, 94, 0.1)', color: '#FF965E', fontWeight: 500 }
      };
    case 'elevee':
      return { 
        label: 'Haute', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 107, 44, 0.1)', color: '#FF6B2C', fontWeight: 500 }
      };
    case 'critique':
      return { 
        label: 'Critique', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    default:
      return { 
        label: priority, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage de la catégorie pour l'affichage
const getCategoryLabel = (category: ReportCategory): string => {
  switch (category) {
    case 'interface':
      return 'Interface';
    case 'fonctionnalite':
      return 'Fonctionnalité';
    case 'securite':
      return 'Sécurité';
    case 'paiement':
      return 'Paiement';
    case 'autre':
      return 'Autre';
    default:
      return category;
  }
};

interface BugReportListProps {
  isAdmin?: boolean;
}

// Composant indicateur de défilement
const TableWithScrollIndicator: React.FC<{ children: React.ReactNode; visible?: boolean }> = ({ 
  children, 
  visible = true 
}) => {
  const isMobile = useMediaQuery('(max-width: 1200px)');

  if (!visible || !isMobile) return <>{children}</>;

  return (
    <>
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        padding: '10px 16px',
        backgroundColor: 'rgba(255, 107, 44, 0.04)',
        borderBottom: '1px solid #E2E8F0'
      }}>
        <SwipeIcon fontSize="small" sx={{ color: '#FF6B2C', mr: 1 }} />
        <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
          Faites défiler horizontalement pour voir tout le tableau
        </Typography>
      </Box>
      {children}
    </>
  );
};

const BugReportList: React.FC<BugReportListProps> = ({ isAdmin = false }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const { user } = useAuth();
  const [reports, setReports] = useState<BugReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<BugReportFilters>({
    page: 1,
    limit: 10,
    order: 'created_at',
    direction: 'desc',
    show_pending: true
  });
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observer = useRef<IntersectionObserver | null>(null);
  const lastReportRef = useCallback((node: HTMLTableRowElement) => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }));
      }
    });
    if (node) observer.current.observe(node);
  }, [loading, hasMore]);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [isClosingSelected, setIsClosingSelected] = useState(false);
  const [isDeletingSelected, setIsDeletingSelected] = useState(false);
  const [adminComment, setAdminComment] = useState('');
  const [deleteReason, setDeleteReason] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('resolu');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const [detailModalOpen, setDetailModalOpen] = useState<boolean>(false);

  useEffect(() => {
    fetchReports();
  }, [filters]);

  const fetchReports = async () => {
    try {
      if (filters.page === 1) {
        setLoading(true);
      }
      setError(null);
      const response = await bugReportService.getAll(filters);
      
      const currentData = response.data || [];
      
      if (filters.page === 1) {
        setReports(currentData);
      } else {
        setReports(prev => [...prev, ...currentData]);
      }
      
      // Mise à jour de la pagination
      const limit = filters.limit || 10;
      const currentPage = filters.page || 1;
      
      // Si nous sommes sur la première page ou si nous avons moins d'éléments que la limite,
      // cela signifie que nous avons le total réel
      if (currentPage === 1 || currentData.length < limit) {
        const total = currentPage === 1 ? currentData.length : (currentPage - 1) * limit + currentData.length;
        setTotalItems(total);
        setTotalPages(Math.ceil(total / limit));
      }
      
      setHasMore(currentData.length === limit);
    } catch (err: any) {
      logger.error('Erreur lors de la récupération des rapports:', err);
      setError(err.response?.data?.error || 'Erreur lors de la récupération des rapports');
      setReports([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof BugReportFilters, value: any) => {
    setFilters({ ...filters, [key]: value, page: 1 });
    setReports([]);
  };

  const handleSortChange = (column: string) => {
    const newDirection = filters.order === column && filters.direction === 'asc' ? 'desc' : 'asc';
    setFilters({
      ...filters,
      order: column,
      direction: newDirection,
      page: 1
    });
    setReports([]);
  };

  const handleViewDetails = (id: string) => {
    setSelectedReportId(id);
    setDetailModalOpen(true);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchTerm(value);
    
    // Annuler tout timeout précédent
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Définir un nouveau timeout pour le debounce
    searchTimeoutRef.current = setTimeout(() => {
      handleFilterChange('search', value);
    }, 1000); // 1 seconde de délai avant de déclencher la recherche
  };

  const resetFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      order: 'created_at',
      direction: 'desc',
      type: undefined,
      category: undefined,
      status: undefined,
      priority: undefined,
      search: undefined,
      user_id: undefined,
      show_pending: true
    });
    setSearchTerm('');
    setReports([]);
  };

  // Sélection d'un rapport
  const handleSelectReport = (reportId: string) => {
    if (selectedReports.includes(reportId)) {
      setSelectedReports(prev => prev.filter(id => id !== reportId));
    } else {
      setSelectedReports(prev => [...prev, reportId]);
    }
  };

  // Sélectionner/désélectionner tous les rapports
  const handleSelectAll = () => {
    if (selectedReports.length === reports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(reports.map(report => report.id));
    }
  };

  // Ouvrir la modal pour clôturer les rapports sélectionnés
  const handleCloseSelected = () => {
    if (selectedReports.length > 0) {
      setIsModalOpen(true);
    }
  };

  // Ouvrir la modal pour supprimer les rapports sélectionnés
  const handleDeleteSelected = () => {
    if (selectedReports.length > 0) {
      setIsDeleteModalOpen(true);
    }
  };

  // Nouvelle fonction pour gérer le rafraîchissement
  const handleRefresh = () => {
    setFilters(prev => ({ ...prev, page: 1 }));
    setReports([]);
    setLoading(true);
  };

  // Confirmer le changement de statut des rapports sélectionnés
  const confirmCloseSelected = async () => {
    try {
      setIsClosingSelected(true);
      await bugReportService.closeMultiple(selectedReports, selectedStatus, adminComment);
      notify(`${selectedReports.length} rapport(s) ont été mis à jour avec succès`, 'success');
      setSelectedReports([]);
      setAdminComment('');
      setSelectedStatus('resolu');
      setIsModalOpen(false);
      // Rafraîchir la liste des rapports
      setFilters(prev => ({ ...prev, page: 1 }));
    } catch (error) {
      logger.error('Erreur lors de la mise à jour des rapports:', error);
      notify('Une erreur est survenue lors de la mise à jour des rapports', 'error');
    } finally {
      setIsClosingSelected(false);
    }
  };

  // Confirmer la suppression des rapports sélectionnés
  const confirmDeleteSelected = async () => {
    try {
      setIsDeletingSelected(true);
      const result = await bugReportService.deleteMultiple(selectedReports, deleteReason);
      if (result.success) {
        notify(`${result.count} rapport(s) ont été supprimés avec succès`, 'success');
        setSelectedReports([]);
        setDeleteReason('');
        setIsDeleteModalOpen(false);
        // Rafraîchir la liste des rapports
        setFilters(prev => ({ ...prev, page: 1 }));
      } else {
        notify(result.message || 'Une erreur est survenue lors de la suppression des rapports', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des rapports:', error);
      notify('Une erreur est survenue lors de la suppression des rapports', 'error'); 
    } finally {
      setIsDeletingSelected(false);
    }
  };

  const handleCloseDetailModal = () => {
    setDetailModalOpen(false);
    setSelectedReportId(null);
  };

  const handleReportUpdated = (updatedReport: BugReport) => {
    // Vérifier si le rapport a été supprimé via une demande de confirmation
    // Dans ce cas, simplement rafraîchir la liste depuis le serveur
    if (selectedReportId === updatedReport.id) {
      // Fermer la modale si le rapport affiché actuellement a été mis à jour
      setDetailModalOpen(false);
      setSelectedReportId(null);
    }

    // Mettre à jour le rapport dans la liste locale
    const updatedReports = reports.map(report => 
      report.id === updatedReport.id ? updatedReport : report
    );
    setReports(updatedReports);
    
    // Rafraîchir depuis le serveur pour s'assurer d'avoir les données les plus récentes
    handleRefresh();
  };

  // Fonction pour fermer tous les modaux et dialogues
  const closeAllModals = () => {
    setDetailModalOpen(false);
    setSelectedReportId(null);
    setFiltersOpen(false);
    setIsModalOpen(false);
    setIsDeleteModalOpen(false);
  };

  return (
    <ProfileCompleteCheck onClose={closeAllModals}>
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* En-tête avec titre et boutons d'actions */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', sm: 'center' }, 
            p: { xs: 1.5, sm: 2, md: 2.5 },
            gap: { xs: 1.5, sm: 0 },
            backgroundColor: 'white',
            borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
          }}
        >
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1, 
            flexGrow: 1, 
            width: { xs: '100%', sm: 'auto' },
            maxWidth: { sm: '300px', md: '400px' }, 
            mb: { xs: 1, sm: 0 },
            mx: { xs: 0, sm: 2 } 
          }}>
            <TextField
              fullWidth
              variant="outlined"
              size="small"
              placeholder={isMobile ? "Rechercher..." : "Rechercher dans les rapports..."}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ 
                      color: '#FF6B2C',
                      fontSize: { xs: '1.2rem', sm: '1.5rem' } 
                    }} />
                  </InputAdornment>
                ),
                endAdornment: searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton 
                      size="small"
                      onClick={() => {
                        setSearchTerm('');
                        handleFilterChange('search', '');
                      }}
                      sx={{ 
                        color: '#757575',
                        padding: { xs: '2px', sm: '4px' }
                      }}
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null,
                sx: {
                  borderRadius: '8px',
                  height: { xs: '36px', sm: 'auto' },
                  '& .MuiOutlinedInput-input': {
                    padding: { xs: '6px 8px', sm: '8.5px 14px' },
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  },
                }
              }}
              sx={{ backgroundColor: 'white' }}
            />
          </Box>
          
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            alignSelf: { xs: 'flex-end', sm: 'auto' }
          }}>
            <Tooltip title="Rafraîchir la liste">
              <IconButton 
                onClick={handleRefresh} 
                sx={{
                  color: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                  },
                  mr: { xs: 0.5, sm: 1 },
                  padding: { xs: '6px', sm: '8px' }
                }}
              >
                <RefreshIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
              </IconButton>
            </Tooltip>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }} />}
              onClick={() => setFiltersOpen(true)}
              size={isMobile ? "small" : "medium"}
              sx={{
                borderColor: '#FF6B2C',
                color: '#FF6B2C',
                '&:hover': {
                  borderColor: '#FF7A35',
                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                },
                borderRadius: '8px',
                textTransform: 'none',
                padding: { xs: '3px 10px', sm: '5px 15px' },
                fontSize: { xs: '0.8125rem', sm: '0.875rem' }
              }}
            >
              Filtres
              {Object.keys(filters).filter(key => 
                key !== 'page' && key !== 'limit' && key !== 'order' && key !== 'direction' && 
                filters[key as keyof BugReportFilters]
              ).length > 0 && (
                <Chip
                  label={Object.keys(filters).filter(key => 
                    key !== 'page' && key !== 'limit' && key !== 'order' && key !== 'direction' && 
                    filters[key as keyof BugReportFilters]
                  ).length}
                  size="small"
                  sx={{
                    ml: 0.5,
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    height: { xs: '16px', sm: '20px' },
                    minWidth: { xs: '16px', sm: '20px' },
                    fontSize: { xs: '0.65rem', sm: '0.75rem' },
                    '& .MuiChip-label': {
                      padding: { xs: '0 3px', sm: '0 6px' }
                    }
                  }}
                />
              )}
            </Button>
          </Box>
        </Box>

        {/* Actions en lot pour admin/modo */}
        {isAdmin && (
          <Paper 
            elevation={0} 
            sx={{ 
              p: { xs: 1.5, sm: 2 }, 
              mb: { xs: 2, sm: 3 }, 
              backgroundColor: '#FFF8F3',
              borderRadius: '8px',
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'flex-start', sm: 'center' },
              justifyContent: 'space-between',
              gap: { xs: 1, sm: 2 }
            }}
          >
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              width: { xs: '100%', sm: 'auto' },
              mb: { xs: 1, sm: 0 }
            }}>
              <Typography 
                variant="body1" 
                sx={{ 
                  fontWeight: 'medium',
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                }}
              >
                {selectedReports.length} rapport(s) sélectionné(s)
              </Typography>
            </Box>
            <Box sx={{ 
              display: 'flex', 
              gap: { xs: 1, sm: 2 },
              width: { xs: '100%', sm: 'auto' },
              flexWrap: { xs: 'wrap', sm: 'nowrap' }
            }}>
              <Button
                variant="outlined"
                sx={{
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF7A35',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                  },
                  fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                  padding: { xs: '2px 8px', sm: '3px 10px' },
                  minWidth: { xs: 0, sm: 'auto' },
                  flex: { xs: 1, sm: 'auto' }
                }}
                startIcon={
                  <CheckBoxOutlineBlankIcon sx={{ 
                    fontSize: { xs: '1rem', sm: '1.25rem' } 
                  }} />
                }
                onClick={handleSelectAll}
                size="small"
              >
                {selectedReports.length === reports.length ? 'Désélectionner' : 'Sélectionner'}
              </Button>
              
              {selectedReports.length > 0 && (
                <>
                  <Button
                    variant="contained"
                    sx={{
                      backgroundColor: '#FF6B2C',
                      '&:hover': {
                        backgroundColor: '#FF7A35',
                      },
                      fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                      padding: { xs: '2px 8px', sm: '3px 10px' },
                      minWidth: { xs: 0, sm: 'auto' },
                      flex: { xs: 1, sm: 'auto' }
                    }}
                    startIcon={
                      <CloseTwoToneIcon sx={{ 
                        fontSize: { xs: '1rem', sm: '1.25rem' } 
                      }} />
                    }
                    onClick={handleCloseSelected}
                    size="small"
                  >
                    {isMobile ? 'Changer' : 'Changer le statut'}
                  </Button>
                  
                  <Button
                    variant="contained"
                    sx={{
                      backgroundColor: '#f44336',
                      '&:hover': {
                        backgroundColor: '#d32f2f',
                      },
                      fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                      padding: { xs: '2px 8px', sm: '3px 10px' },
                      minWidth: { xs: 0, sm: 'auto' },
                      flex: { xs: 1, sm: 'auto' }
                    }}
                    startIcon={
                      <DeleteForeverIcon sx={{ 
                        fontSize: { xs: '1rem', sm: '1.25rem' } 
                      }} />
                    }
                    onClick={handleDeleteSelected}
                    size="small"
                  >
                    Supprimer
                  </Button>
                </>
              )}
            </Box>
          </Paper>
        )}

        {/* États de chargement, erreur et liste vide */}
        {loading && filters.page === 1 ? (
          <Box 
            sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              p: 5,
              backgroundColor: 'white'
            }}
          >
            <CircularProgress sx={{ color: '#FF6B2C' }} />
          </Box>
        ) : error ? (
          <Box 
            sx={{ 
              p: 4, 
              textAlign: 'center',
              backgroundColor: 'white' 
            }}
          >
            <ErrorOutlineIcon 
              sx={{ 
                fontSize: 48, 
                color: '#D32F2F', 
                mb: 2 
              }} 
            />
            <Typography color="error" variant="h6">
              Erreur lors du chargement des rapports
            </Typography>
            <Typography color="textSecondary" sx={{ mt: 1 }}>
              {error}
            </Typography>
            <Button
              variant="contained"
              onClick={fetchReports}
              sx={{
                mt: 2,
                backgroundColor: '#FF6B2C',
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
                borderRadius: '8px',
                textTransform: 'none'
              }}
            >
              Réessayer
            </Button>
          </Box>
        ) : reports.length === 0 ? (
          <Box 
            sx={{ 
              p: 4, 
              textAlign: 'center',
              backgroundColor: 'white'
            }}
          >
            <SentimentDissatisfiedIcon 
              sx={{ 
                fontSize: 48, 
                color: '#9E9E9E', 
                mb: 2 
              }} 
            />
            <Typography variant="h6" color="textSecondary">
              Aucun rapport trouvé
            </Typography>
            <Typography color="textSecondary" sx={{ mt: 1 }}>
              {isAdmin 
                ? "Aucun rapport n'a été soumis pour le moment ou vos filtres sont trop restrictifs." 
                : "Vous n'avez pas encore soumis de rapport ou vos filtres sont trop restrictifs."}
            </Typography>
          </Box>
        ) : (
          <>
            {/* Vue mobile (cartes) */}
            {isMobile ? (
              <Box sx={{ 
                p: 2, 
                // bgcolor: '#f9f9f9',
                borderRadius: '0 0 12px 12px' 
              }}>
                <Grid container spacing={4}>
                  {reports.map((report, index) => (
                    <Grid 
                      size={{ xs: 12 }} 
                      key={report.id} 
                      ref={index === reports.length - 1 ? lastReportRef : undefined}
                      sx={{
                        position: 'relative',
                        '&:not(:last-child)::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: -12,
                          left: '5%',
                          width: '90%',
                          height: '1px',
                          background: 'linear-gradient(90deg, transparent, rgba(255, 107, 44, 0.1), transparent)',
                          zIndex: 1
                        }
                      }}
                    >
                      <Card 
                        sx={{ 
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.06), 0 2px 4px rgba(255, 107, 44, 0.03)',
                          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: '0 8px 20px rgba(255, 107, 44, 0.15), 0 4px 8px rgba(255, 107, 44, 0.05)',
                          },
                          position: 'relative',
                          overflow: 'visible',
                          padding: '24px',
                          border: '1px solid rgba(0, 0, 0, 0.05)',
                          background: index % 2 === 0 
                            ? 'linear-gradient(145deg, #ffffff, #FFF8F3)'
                            : 'linear-gradient(145deg, #ffffff, #f7f1ec)',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: -8,
                            left: '10%',
                            width: '80%',
                            height: '10px',
                            background: 'radial-gradient(ellipse at center, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0) 70%)',
                            borderRadius: '50%',
                            zIndex: -1
                          },
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '5px',
                            height: '100%',
                            backgroundColor: report.report_type === 'bug' ? '#FF6B2C' : '#3F51B5',
                            borderRadius: '12px 0 0 12px',
                          }
                        }}
                        onClick={() => handleViewDetails(report.id)}
                      >
                        {isAdmin && (
                          <Box 
                            sx={{
                              position: 'absolute',
                              top: 5,
                              right: 5,
                              zIndex: 10,
                            }}
                            onClick={(e: React.MouseEvent) => e.stopPropagation()}
                          >
                            <Checkbox
                              checked={selectedReports.includes(report.id)}
                              onChange={() => handleSelectReport(report.id)}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                              sx={{
                                color: 'rgba(255, 107, 44, 0.5)',
                                backgroundColor: 'rgba(255, 255, 255, 0.85)',
                                borderRadius: '4px',
                                padding: '4px',
                                '&.Mui-checked': {
                                  color: '#FF6B2C',
                                }
                              }}
                              size="small"
                            />
                          </Box>
                        )}
                        <CardContent sx={{ p: 1.5 }}>
                          <Typography 
                            variant="subtitle1" 
                            component="div" 
                            sx={{ 
                              fontWeight: 600, 
                              mb: 1.5,
                              color: '#333',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              lineHeight: 1.4,
                              fontSize: '1rem',
                              letterSpacing: '0.01em'
                            }}
                          >
                            {report.title}
                            {report.is_private && (
                              <Box 
                                component="span" 
                                sx={{ 
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  ml: 1,
                                  background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.2) 0%, rgba(255, 150, 94, 0.2) 100%)',
                                  color: '#FF6B2C',
                                  borderRadius: '6px',
                                  px: 0.8,
                                  py: 0.3,
                                  fontSize: '0.7rem',
                                  fontWeight: 600,
                                  verticalAlign: 'middle',
                                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                                  border: '1px solid rgba(255, 107, 44, 0.2)'
                                }}
                              >
                                <LockIcon sx={{ fontSize: '0.7rem', mr: 0.4 }} />
                                Privé
                              </Box>
                            )}
                          </Typography>

                          <Typography 
                            variant="body2" 
                            component="div" 
                            sx={{ 
                              mb: 2,
                              color: '#555',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              lineHeight: 1.5,
                              fontSize: '0.875rem',
                              opacity: 0.9
                            }}
                          >
                            {report.description ? (report.description.length > 100 ? report.description.substring(0, 100) + '...' : report.description) : 'Aucune description'}
                          </Typography>

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8, mb: 2 }}>
                            <Chip
                              size="small"
                              label={getTypeChipProps(report.report_type).label}
                              sx={{
                                ...getTypeChipProps(report.report_type).sx,
                                borderRadius: '6px',
                                height: '26px',
                                fontSize: '0.75rem',
                                fontWeight: 600,
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                                '& .MuiChip-label': {
                                  px: 1.2,
                                }
                              }}
                            />
                            <Chip
                              size="small"
                              label={getStatusChipProps(report.status).label}
                              sx={{
                                ...getStatusChipProps(report.status).sx,
                                borderRadius: '6px',
                                height: '26px',
                                fontSize: '0.75rem',
                                fontWeight: 600,
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                                '& .MuiChip-label': {
                                  px: 1.2,
                                }
                              }}
                            />
                            <Chip
                              size="small"
                              label={getPriorityChipProps(report.priority).label}
                              sx={{
                                ...getPriorityChipProps(report.priority).sx,
                                borderRadius: '6px',
                                height: '26px',
                                fontSize: '0.75rem',
                                fontWeight: 600,
                                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                                '& .MuiChip-label': {
                                  px: 1.2,
                                }
                              }}
                            />
                          </Box>

                          <Divider sx={{ 
                            my: 1.5, 
                            opacity: 0.6,
                            background: 'linear-gradient(90deg, rgba(255, 107, 44, 0.15), rgba(255, 150, 94, 0.08), transparent)'
                          }} />

                          <Box sx={{ 
                            display: 'flex', 
                            justifyContent: 'space-between', 
                            alignItems: 'center', 
                            mt: 1.5 
                          }}>
                            <Typography 
                              variant="caption" 
                              sx={{
                                color: '#666',
                                fontWeight: 500,
                                fontSize: '0.75rem',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                            >
                              {new Date(report.created_at).toLocaleDateString('fr-FR', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric',
                              })}
                            </Typography>
                            
                            <Box>
                              <Tooltip title="Voir les détails">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleViewDetails(report.id);
                                  }}
                                  aria-label="voir détails"
                                  sx={{
                                    color: '#FF6B2C',
                                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                                    mr: 1,
                                    border: '1px solid rgba(255, 107, 44, 0.2)',
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                      backgroundColor: 'rgba(255, 107, 44, 0.15)',
                                      transform: 'translateY(-2px)',
                                      boxShadow: '0 3px 8px rgba(255, 107, 44, 0.15)'
                                    }
                                  }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              {isAdmin && (
                                <Tooltip title="Modifier">
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigate(`/admin/bug-reports/${report.id}/edit`);
                                    }}
                                    aria-label="modifier"
                                    sx={{
                                      color: '#FF7A35',
                                      backgroundColor: 'rgba(255, 122, 53, 0.08)',
                                      border: '1px solid rgba(255, 122, 53, 0.2)',
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        backgroundColor: 'rgba(255, 122, 53, 0.15)',
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 3px 8px rgba(255, 122, 53, 0.15)'
                                      }
                                    }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
                {loading && filters.page && filters.page > 1 && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                    <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />
                  </Box>
                )}
              </Box>
            ) : (
              /* Vue desktop (tableau) */
              <Paper 
                elevation={0} 
                sx={{ 
                  width: '100%', 
                  overflow: 'hidden',
                  backgroundColor: 'white',
                  borderRadius: 0
                }}
              >
                {isTablet && (
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    padding: '10px 16px',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                    borderBottom: '1px solid #E2E8F0'
                  }}>
                    <SwipeRightIcon fontSize="small" sx={{ color: '#FF6B2C', mr: 1 }} />
                    <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
                      Faites défiler horizontalement pour voir tout le tableau
                    </Typography>
                  </Box>
                )}
                <TableWithScrollIndicator>
                  <TableContainer>
                    <Table sx={{ minWidth: 650 }} aria-label="table des rapports de bugs">
                      <TableHead>
                        <TableRow
                          sx={{
                            '& th': {
                              backgroundColor: '#FFF8F3',
                              borderBottom: '2px solid #FF965E',
                              color: '#333',
                              fontWeight: 600,
                              py: 1.5
                            }
                          }}
                        >
                          {isAdmin && (
                            <TableCell padding="checkbox">
                              <Checkbox
                                indeterminate={selectedReports.length > 0 && selectedReports.length < reports.length}
                                checked={reports.length > 0 && selectedReports.length === reports.length}
                                onChange={handleSelectAll}
                                sx={{
                                  color: 'rgba(255, 107, 44, 0.5)',
                                  '&.Mui-checked': {
                                    color: '#FF6B2C',
                                  },
                                  '&.MuiCheckbox-indeterminate': {
                                    color: '#FF6B2C',
                                  }
                                }}
                              />
                            </TableCell>
                          )}
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('title')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Titre
                              <TableSortLabel
                                active={filters.order === 'title'}
                                direction={filters.order === 'title' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('report_type')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Type
                              <TableSortLabel
                                active={filters.order === 'report_type'}
                                direction={filters.order === 'report_type' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('category')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Catégorie
                              <TableSortLabel
                                active={filters.order === 'category'}
                                direction={filters.order === 'category' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('status')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Statut
                              <TableSortLabel
                                active={filters.order === 'status'}
                                direction={filters.order === 'status' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('priority')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Priorité
                              <TableSortLabel
                                active={filters.order === 'priority'}
                                direction={filters.order === 'priority' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ cursor: 'pointer' }}
                            onClick={() => handleSortChange('created_at')}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              Date
                              <TableSortLabel
                                active={filters.order === 'created_at'}
                                direction={filters.order === 'created_at' ? filters.direction : 'asc'}
                                sx={{
                                  '& .MuiTableSortLabel-icon': {
                                    color: '#FF6B2C !important',
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell align="right">Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {loading && filters.page === 1 ? (
                          <TableRow>
                            <TableCell colSpan={isAdmin ? 8 : 7} align="center">
                              <CircularProgress size={32} sx={{ m: 2 }} />
                            </TableCell>
                          </TableRow>
                        ) : reports.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={isAdmin ? 8 : 7} align="center" sx={{ py: 4 }}>
                              <Typography variant="body1">Aucun rapport trouvé</Typography>
                            </TableCell>
                          </TableRow>
                        ) : (
                          reports.map((report, index) => (
                            <TableRow 
                              key={`${report.id}-${index}`} 
                              ref={index === reports.length - 1 ? lastReportRef : null}
                              hover
                              onClick={() => handleViewDetails(report.id)}
                              sx={{
                                cursor: 'pointer',
                                '&:hover': { backgroundColor: '#FFF8F3' }
                              }}
                            >
                              {isAdmin && (
                                <TableCell padding="checkbox">
                                  <Checkbox
                                    checked={selectedReports.includes(report.id)}
                                    onClick={(e) => e.stopPropagation()}
                                    onChange={() => handleSelectReport(report.id)}
                                    sx={{
                                      color: 'rgba(255, 107, 44, 0.5)',
                                      '&.Mui-checked': {
                                        color: '#FF6B2C',
                                      }
                                    }}
                                  />
                                </TableCell>
                              )}
                              <TableCell 
                                sx={{ 
                                  maxWidth: 300,
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  fontWeight: 500
                                }}
                              >
                                {report.title}
                                {report.is_private && (
                                  <Box 
                                    component="span" 
                                    sx={{ 
                                      display: 'inline-flex',
                                      alignItems: 'center',
                                      ml: 1,
                                      background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.2) 0%, rgba(255, 150, 94, 0.2) 100%)',
                                      color: '#FF6B2C',
                                      borderRadius: '6px',
                                      px: 0.8,
                                      py: 0.3,
                                      fontSize: '0.7rem',
                                      fontWeight: 600,
                                      verticalAlign: 'middle',
                                      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                                      border: '1px solid rgba(255, 107, 44, 0.2)'
                                    }}
                                  >
                                    <LockIcon sx={{ fontSize: '0.7rem', mr: 0.4 }} />
                                    Privé
                                  </Box>
                                )}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={getTypeChipProps(report.report_type).label}
                                  sx={{
                                    ...getTypeChipProps(report.report_type).sx,
                                    borderRadius: '6px',
                                    height: '28px'
                                  }}
                                />
                              </TableCell>
                              <TableCell>{getCategoryLabel(report.category)}</TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={getStatusChipProps(report.status).label}
                                  sx={{
                                    ...getStatusChipProps(report.status).sx,
                                    borderRadius: '6px',
                                    height: '28px'
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={getPriorityChipProps(report.priority).label}
                                  sx={{
                                    ...getPriorityChipProps(report.priority).sx,
                                    borderRadius: '6px',
                                    height: '28px'
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                {new Date(report.created_at).toLocaleDateString('fr-FR', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric',
                                })}
                              </TableCell>
                              <TableCell align="right">
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewDetails(report.id)}
                                  aria-label="voir détails"
                                  sx={{
                                    color: '#FF6B2C',
                                    backgroundColor: 'rgba(255, 107, 44, 0.08)',
                                    mr: 1,
                                    border: '1px solid rgba(255, 107, 44, 0.2)',
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                      backgroundColor: 'rgba(255, 107, 44, 0.15)',
                                      transform: 'translateY(-2px)',
                                      boxShadow: '0 3px 8px rgba(255, 107, 44, 0.15)'
                                    }
                                  }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                                {isAdmin && (
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigate(`/admin/bug-reports/${report.id}/edit`);
                                    }}
                                    aria-label="modifier"
                                    sx={{
                                      color: '#FF7A35',
                                      backgroundColor: 'rgba(255, 122, 53, 0.08)',
                                      border: '1px solid rgba(255, 122, 53, 0.2)',
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        backgroundColor: 'rgba(255, 122, 53, 0.15)',
                                        transform: 'translateY(-2px)',
                                        boxShadow: '0 3px 8px rgba(255, 122, 53, 0.15)'
                                      }
                                    }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                )}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                        {loading && (filters.page ?? 1) > 1 && (
                          <TableRow>
                            <TableCell colSpan={isAdmin ? 8 : 7} align="center">
                              <CircularProgress size={24} sx={{ m: 1 }} />
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  
                  {/* Pagination Info */}
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      p: 2,
                      backgroundColor: 'white',
                      borderTop: '1px solid rgba(0, 0, 0, 0.08)',
                    }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {totalItems > 0 ? (
                        `${totalItems} rapport${totalItems > 1 ? 's' : ''} au total`
                      ) : (
                        'Aucun rapport trouvé'
                      )}
                    </Typography>
                  </Box>
                </TableWithScrollIndicator>
              </Paper>
            )}
          </>
        )}

        {/* Dialogue pour les filtres */}
        <Dialog 
          open={filtersOpen} 
          onClose={() => setFiltersOpen(false)} 
          maxWidth="sm" 
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: '12px',
              overflow: 'hidden',
            }
          }}
        >
          <DialogTitle
            sx={{
              backgroundColor: '#FFF8F3',
              color: '#333',
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <FilterListIcon sx={{ mr: 1, color: '#FF6B2C' }} />
              Filtrer les rapports
            </Box>
            <IconButton
              edge="end"
              color="inherit"
              onClick={() => setFiltersOpen(false)}
              aria-label="fermer"
              sx={{
                color: '#757575',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent 
            sx={{ 
              p: '24px !important',
              backgroundColor: 'white',
              '& .MuiInputLabel-root': {
                color: 'rgba(0, 0, 0, 0.6)',
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#FF6B2C !important',
              },
              '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#FF6B2C !important',
              },
              '& .Mui-focused .MuiSvgIcon-root': {
                color: '#FF6B2C !important',
              },
              '& .MuiFormLabel-root': {
                color: 'rgba(0, 0, 0, 0.6)',
              },
              '& .MuiFormLabel-root.Mui-focused': {
                color: '#FF6B2C !important',
              }
            }}
          >
            <Grid container spacing={3}>
              <Grid size={{ xs: 12 }}>
                <TextField
                  fullWidth
                  label="Rechercher"
                  placeholder="Rechercher dans les titres et descriptions..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  variant="outlined"
                  size="small"
                  InputLabelProps={{
                    sx: { 
                      '&.Mui-focused': { color: '#FF6B2C' } 
                    }
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: '#FF6B2C' }} />
                      </InputAdornment>
                    ),
                    sx: {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                    }
                  }}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 2 }}>
                  <InputLabel id="status-label">Statut</InputLabel>
                  <Select
                    labelId="status-label"
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    label="Statut"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 300,
                          overflow: 'auto',
                          '& .MuiMenuItem-root': {
                            padding: '8px 16px',
                          },
                        },
                      },
                    }}
                    sx={{
                      backgroundColor: 'white',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                    }}
                  >
                    <MenuItem value="">Tous</MenuItem>
                    <MenuItem value="attente_moderation">En attente de modération</MenuItem>
                    <MenuItem value="nouveau">Nouveau</MenuItem>
                    <MenuItem value="en_cours">En cours</MenuItem>
                    <MenuItem value="reouvert">Réouvert</MenuItem>
                    <MenuItem value="resolu">Résolu</MenuItem>
                    <MenuItem value="rejete">Rejeté</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 2 }}>
                  <InputLabel id="type-label">Type</InputLabel>
                  <Select
                    labelId="type-label"
                    value={filters.type || ''}
                    onChange={(e) => handleFilterChange('type', e.target.value)}
                    label="Type"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 300,
                          overflow: 'auto',
                          '& .MuiMenuItem-root': {
                            padding: '8px 16px',
                          },
                        },
                      },
                    }}
                    sx={{
                      backgroundColor: 'white',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                    }}
                  >
                    <MenuItem value="">Tous</MenuItem>
                    <MenuItem value="bug">Bug</MenuItem>
                    <MenuItem value="improvement">Amélioration</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 2 }}>
                  <InputLabel id="priority-label">Priorité</InputLabel>
                  <Select
                    labelId="priority-label"
                    value={filters.priority || ''}
                    onChange={(e) => handleFilterChange('priority', e.target.value)}
                    label="Priorité"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 300,
                          overflow: 'auto',
                          '& .MuiMenuItem-root': {
                            padding: '8px 16px',
                          },
                        },
                      },
                    }}
                    sx={{
                      backgroundColor: 'white',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                    }}
                  >
                    <MenuItem value="">Toutes</MenuItem>
                    <MenuItem value="faible">Faible</MenuItem>
                    <MenuItem value="moyenne">Moyenne</MenuItem>
                    <MenuItem value="elevee">Haute</MenuItem>
                    <MenuItem value="critique">Critique</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 2 }}>
                  <InputLabel id="category-label">Catégorie</InputLabel>
                  <Select
                    labelId="category-label"
                    value={filters.category || ''}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    label="Catégorie"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 300,
                          overflow: 'auto',
                          '& .MuiMenuItem-root': {
                            padding: '8px 16px',
                          },
                        },
                      },
                    }}
                    sx={{
                      backgroundColor: 'white',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                    }}
                  >
                    <MenuItem value="">Toutes</MenuItem>
                    <MenuItem value="interface">Interface</MenuItem>
                    <MenuItem value="fonctionnalite">Fonctionnalité</MenuItem>
                    <MenuItem value="securite">Sécurité</MenuItem>
                    <MenuItem value="paiement">Paiement</MenuItem>
                    <MenuItem value="autre">Autre</MenuItem>
                  </Select>
                </FormControl>

                {user && (
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={filters.user_id === user.id}
                        onChange={(e) => {
                          handleFilterChange('user_id', e.target.checked ? user.id : undefined);
                        }}
                        sx={{
                          color: '#FF6B2C',
                          '&.Mui-checked': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                    }
                    label="Mes rapports uniquement"
                    sx={{
                      '.MuiFormControlLabel-label': {
                        fontSize: '0.9rem',
                      },
                    }}
                  />
                )}
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.08)', backgroundColor: 'white' }}>
            <Button 
              onClick={() => {
                resetFilters();
                setFiltersOpen(false);
              }}
              sx={{
                color: '#757575',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              Réinitialiser les filtres
            </Button>
            <Button
              onClick={() => setFiltersOpen(false)}
              sx={{
                backgroundColor: '#FF6B2C',
                color: 'white',
                textTransform: 'none',
                borderRadius: '8px',
                px: 3,
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
              }}
            >
              Appliquer
            </Button>
          </DialogActions>
        </Dialog>

        {/* Modal de confirmation pour la mise à jour en lot */}
        <Dialog
          open={isModalOpen}
          onClose={() => !isClosingSelected && setIsModalOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#FF6B2C', fontWeight: 'bold' }}>
            Modifier le statut de {selectedReports.length} rapport(s)
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Typography variant="body1" gutterBottom>
              Vous êtes sur le point de modifier le statut de {selectedReports.length} rapport(s) de bug.
            </Typography>
            
            <FormControl fullWidth margin="normal">
              <InputLabel 
                id="status-select-label"
                sx={{ 
                  '&.Mui-focused': { 
                    color: '#FF6B2C' 
                  } 
                }}
              >
                Nouveau statut
              </InputLabel>
              <Select
                labelId="status-select-label"
                id="status-select"
                value={selectedStatus}
                label="Nouveau statut"
                onChange={(e) => setSelectedStatus(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  },
                  '& .MuiSvgIcon-root': {
                    color: '#FF6B2C',
                  }
                }}
              >
                <MenuItem value="nouveau">Nouveau</MenuItem>
                <MenuItem value="en_cours">En cours</MenuItem>
                <MenuItem value="resolu">Résolu</MenuItem>
                <MenuItem value="rejete">Rejeté</MenuItem>
                <MenuItem value="reouvert">Réouvert</MenuItem>
                <MenuItem value="attente_moderation">En attente de modération</MenuItem>
              </Select>
            </FormControl>
            
            <Typography variant="body1" gutterBottom sx={{ mt: 2 }}>
              Veuillez saisir un commentaire facultatif qui sera ajouté à tous les rapports:
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Commentaire (facultatif)"
              value={adminComment}
              onChange={(e) => setAdminComment(e.target.value)}
              variant="outlined"
              margin="normal"
            />
          </DialogContent>
          <DialogActions sx={{ p: 2 }}>
            <Button 
              variant="outlined" 
              onClick={() => setIsModalOpen(false)}
              disabled={isClosingSelected}
              sx={{
                borderColor: '#757575',
                color: '#757575',
                '&:hover': {
                  borderColor: '#5c5c5c',
                }
              }}
            >
              Annuler
            </Button>
            <Button 
              variant="contained" 
              onClick={confirmCloseSelected}
              disabled={isClosingSelected}
              startIcon={isClosingSelected ? <CircularProgress size={20} color="inherit" /> : <CloseTwoToneIcon />}
              sx={{
                backgroundColor: '#FF6B2C',
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(255, 107, 44, 0.3)',
                }
              }}
            >
              {isClosingSelected ? 'En cours...' : 'Confirmer la modification'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Modal de confirmation pour la suppression en lot */}
        <Dialog
          open={isDeleteModalOpen}
          onClose={() => !isDeletingSelected && setIsDeleteModalOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ bgcolor: '#FFF8F3', color: '#f44336', fontWeight: 'bold' }}>
            Supprimer {selectedReports.length} rapport(s)
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Typography variant="body1" gutterBottom>
              Vous êtes sur le point de supprimer définitivement {selectedReports.length} rapport(s) de bug.
            </Typography>
            <Typography variant="body2" color="error" sx={{ mt: 1, mb: 2, fontWeight: 500 }}>
              Cette action est irréversible et les données ne pourront pas être récupérées.
            </Typography>
            
            <Typography variant="body1" gutterBottom sx={{ mt: 2 }}>
              Veuillez saisir une raison de suppression (facultatif) :
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Raison de la suppression (facultatif)"
              value={deleteReason}
              onChange={(e) => setDeleteReason(e.target.value)}
              variant="outlined"
              margin="normal"
            />
          </DialogContent>
          <DialogActions sx={{ p: 2 }}>
            <Button 
              variant="outlined" 
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={isDeletingSelected}
              sx={{
                borderColor: '#757575',
                color: '#757575',
                '&:hover': {
                  borderColor: '#5c5c5c',
                }
              }}
            >
              Annuler
            </Button>
            <Button 
              variant="contained" 
              onClick={confirmDeleteSelected}
              disabled={isDeletingSelected}
              startIcon={isDeletingSelected ? <CircularProgress size={20} color="inherit" /> : <DeleteForeverIcon />}
              sx={{
                backgroundColor: '#f44336',
                '&:hover': {
                  backgroundColor: '#d32f2f',
                },
                '&.Mui-disabled': {
                  backgroundColor: 'rgba(244, 67, 54, 0.3)',
                }
              }}
            >
              {isDeletingSelected ? 'Suppression...' : 'Confirmer la suppression'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Modal pour afficher les détails du rapport */}
        {detailModalOpen && selectedReportId && (
          <ModalPortal
            isOpen={detailModalOpen}
            onBackdropClick={handleCloseDetailModal}
          >
            <Box 
              sx={{ 
                position: 'relative', 
                width: '90vw', 
                maxWidth: '1200px', 
                height: '80vh', 
                maxHeight: '90vh',
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 10px 40px rgba(0, 0, 0, 0.2)',
                overflow: 'hidden'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 2,
                  borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
                  backgroundColor: '#FFF8F3'
                }}
              >
                <Typography variant="h6" fontWeight="bold">Détails du rapport</Typography>
                <IconButton onClick={handleCloseDetailModal}>
                  <CloseIcon />
                </IconButton>
              </Box>
              <Box 
                sx={{ 
                  position: 'absolute', 
                  top: '64px', 
                  left: 0, 
                  right: 0, 
                  bottom: 0, 
                  overflow: 'auto', 
                  p: { xs: 2, sm: 3 },
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                  <BugReportDetail isAdmin={isAdmin} reportId={selectedReportId} onReportUpdated={handleReportUpdated} />
                </Box>
                
                <Box 
                  sx={{ 
                    mt: 3, 
                    pt: 2, 
                    borderTop: '1px solid rgba(0, 0, 0, 0.1)',
                    display: 'flex',
                    justifyContent: 'center'
                  }}
                >
                  <Button
                    variant="contained"
                    onClick={handleCloseDetailModal}
                    startIcon={<CloseIcon />}
                    sx={{
                      backgroundColor: '#FF6B2C',
                      color: 'white',
                      '&:hover': {
                        backgroundColor: '#FF7A35',
                      },
                      borderRadius: '8px',
                      px: 4,
                      py: 1
                    }}
                  >
                    Fermer
                  </Button>
                </Box>
              </Box>
            </Box>
          </ModalPortal>
        )}
      </Box>
    </ProfileCompleteCheck>
  );
};

export default BugReportList; 