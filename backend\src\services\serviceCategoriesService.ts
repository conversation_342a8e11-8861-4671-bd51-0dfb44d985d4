import logger from '../utils/logger';
import { redis } from '../config/redis';
import {
  SERVICE_CATEGORIES as CATEGORIES_DATA,
  SERVICE_SUBCATEGORIES as SUBCATEGORIES_DATA,
  ServiceCategory,
  ServiceSubcategory
} from '../data/serviceTypes';

// Constantes pour le cache Redis
const CACHE_PREFIX = 'service_categories:';
const CACHE_TTL = 60 * 60; // 1 heure




/**
 * Récupère les catégories de services depuis les données locales
 */
export async function getServiceCategories(): Promise<ServiceCategory[]> {
  try {
    // Vérifier le cache
    const cachedCategories = await redis.get(`${CACHE_PREFIX}categories`);
    if (cachedCategories) {
      return JSON.parse(cachedCategories);
    }

    // Utiliser les données importées directement
    const categories = CATEGORIES_DATA;
    logger.info(`Catégories extraites avec succès: ${categories.length} catégories trouvées`);

    // Mettre en cache
    await redis.setex(`${CACHE_PREFIX}categories`, CACHE_TTL, JSON.stringify(categories));

    return categories;
  } catch (error) {
    logger.error('Erreur lors de la récupération des catégories de service:', error);
    throw new Error('Impossible de récupérer les catégories de services');
  }
}

/**
 * Récupère les sous-catégories de services depuis les données locales
 */
export async function getServiceSubcategories(): Promise<ServiceSubcategory[]> {
  try {
    // Vérifier le cache
    const cachedSubcategories = await redis.get(`${CACHE_PREFIX}subcategories`);
    if (cachedSubcategories) {
      return JSON.parse(cachedSubcategories);
    }

    // Utiliser les données importées directement
    const subcategories = SUBCATEGORIES_DATA;
    logger.info(`Sous-catégories extraites avec succès: ${subcategories.length} sous-catégories trouvées`);

    // Ajouter category_id pour compatibilité backend
    const subcategoriesWithCategoryId = subcategories.map(subcat => ({
      ...subcat,
      category_id: subcat.categoryId
    }));

    // Mettre en cache
    await redis.setex(`${CACHE_PREFIX}subcategories`, CACHE_TTL, JSON.stringify(subcategoriesWithCategoryId));

    return subcategoriesWithCategoryId;
  } catch (error) {
    logger.error('Erreur lors de la récupération des sous-catégories de service:', error);
    throw new Error('Impossible de récupérer les sous-catégories de services');
  }
}

/**
 * Invalide le cache des catégories et sous-catégories
 */
export async function invalidateServiceCategoriesCache(): Promise<void> {
  await redis.del(`${CACHE_PREFIX}categories`);
  await redis.del(`${CACHE_PREFIX}subcategories`);
  logger.info('Cache des catégories et sous-catégories invalidé');
}
