import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import { ServiceCategory, ServiceSubcategory, UserService, ServiceFormDataBackend } from './types';
import logger from '../../../utils/logger';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from './types';
import { getCommonHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';

// Type étendu pour inclure la catégorie
type ServiceDataWithCategorie = ServiceFormDataBackend & {
  categorie: string[];
};

export const serviceApi = {
  // Récupérer les catégories
  getCategories: async (): Promise<ServiceCategory[]> => {
    return SERVICE_CATEGORIES;
  },

  // Récupérer les sous-catégories d'une catégorie
  getSubcategories: async (categoryId: string): Promise<ServiceSubcategory[]> => {
    return SERVICE_SUBCATEGORIES.filter(sub => sub.categoryId === categoryId);
  },

  // Créer un nouveau service
  createService: async (serviceData: ServiceDataWithCategorie): Promise<UserService> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/services`, serviceData, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        // Afficher le toast et stopper le process
        const { message, toastType } = error.response.data;
        // @ts-ignore
        import('../../../components/Notification').then(({ notify }) => notify(message, toastType || 'error'));
        throw new Error('profile_hidden');
      }
      if (error.response?.status === 400) {
        // Si c'est une erreur 400, on vérifie si c'est lié au CSRF
        if (error.response?.data?.error?.includes('CSRF')) {
          logger.error('Erreur de token CSRF lors de la création du service. Essayez de vider le cache pour tester.');
        } else {
          logger.error('Erreur de validation des données pour la création d\'un service :', error.response?.data);
        }
      }
      throw error;
    }
  },

  // Mettre à jour un service
  updateService: async (serviceId: string, serviceData: any) => {
    logger.info('Demande de mise à jour du service :', serviceData);
    try {
      const headers = await getCommonHeaders();
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/services/${serviceId}`,
        serviceData,
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      return response.data.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        const { message, toastType } = error.response.data;
        // @ts-ignore
        import('../../../components/Notification').then(({ notify }) => notify(message, toastType || 'error'));
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la mise à jour du service:', error);
      throw error;
    }
  },

  // Supprimer un service
  deleteService: async (serviceId: string): Promise<void> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      await axios.delete(`${API_CONFIG.baseURL}/api/services/${serviceId}`, {
        headers,
        withCredentials: true
      });
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        const { message, toastType } = error.response.data;
        // @ts-ignore
        import('../../../components/Notification').then(({ notify }) => notify(message, toastType || 'error'));
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la suppression du service:', error);
      throw error;
    }
  },

  // Changer le statut d'un service (actif/inactif)
  toggleServiceStatus: async (serviceId: string, statut: 'actif' | 'inactif'): Promise<UserService> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.patch(`${API_CONFIG.baseURL}/api/services/${serviceId}/status`, { statut }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        const { message, toastType } = error.response.data;
        // @ts-ignore
        import('../../../components/Notification').then(({ notify }) => notify(message, toastType || 'error'));
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors du changement de statut du service:', error);
      throw error;
    }
  },

  getServicesCount: async (): Promise<number> => {
    const response = await fetch('/api/services/count');
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération du nombre de services');
    }
    const data = await response.json();
    return data.count;
  },
}; 