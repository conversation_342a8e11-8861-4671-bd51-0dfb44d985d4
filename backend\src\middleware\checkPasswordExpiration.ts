import { Request, Response, NextFunction } from 'express';
import { DatabaseService } from '../services/db';
import { logSecurity } from '../services/logger';
import { LogEventType } from '../types/logger';

export const checkPasswordExpiration = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Vérifier si l'utilisateur est authentifié
    if (!req.user?.userId) {
      return next();
    }

    // Vérifier l'expiration du mot de passe
    const passwordStatus = await DatabaseService.getInstance().checkPasswordExpiration(req.user.id);
    
    if (passwordStatus.expired) {
      res.status(401).json({
        message: 'Votre mot de passe a expiré. Veuillez vous reconnecter et le réinitialiser.',
        toastType: 'error',
        passwordExpired: true
      });
    }

    // Ajouter le statut d'expiration à la réponse pour le front-end
    res.locals.passwordStatus = passwordStatus;
    next();
  } catch (error) {
    logSecurity.error(LogEventType.SECURITY_ERROR, 'Erreur lors de la vérification de l\'expiration du mot de passe', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.userId 
    });
    next();
  }
};
