import { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { notify } from '@/components/Notification';
import logger from '@/utils/logger'; // Importer le logger
import { setCookie, getCookie, removeCookie } from '../../utils/cookieUtils'; // Importer les fonctions de gestion des cookies
import { User, Eye, EyeOff } from 'lucide-react';
import DOMPurify from 'dompurify';
import GoogleSignInButton from '@/components/common/GoogleSignInButton';

const COOLDOWN_DURATION = 3; // 3 secondes de cooldown

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const [searchParams] = useSearchParams();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [lastAttemptTime, setLastAttemptTime] = useState<number | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Vérifier si on a un token dans l'URL (redirection depuis Google)
  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      // Ne plus stocker le token dans un cookie JS, simplement rediriger
      logger.info('Token reçu depuis la redirection Google');

      // Rediriger vers le dashboard
      navigate('/dashboard');
    }
  }, [searchParams, navigate]);

  useEffect(() => {
    const updateCountdown = () => {
      if (lastAttemptTime) {
        const now = Date.now();
        const elapsed = Math.floor((now - lastAttemptTime) / 1000);
        const remaining = Math.max(0, COOLDOWN_DURATION - elapsed);

        if (remaining > 0) {
          setCountdown(remaining);
        } else {
          setCountdown(0);
          setLastAttemptTime(null);
          removeCookie('verificationEmailCooldown');
        }
      } else {
        // Vérifier le cookie au cas où il y aurait un cooldown existant
        const storedCooldown = getCookie('verificationEmailCooldown');
        if (storedCooldown) {
          const cooldownTime = parseInt(storedCooldown);
          const now = Date.now();
          if (cooldownTime > now) {
            setLastAttemptTime(cooldownTime - (COOLDOWN_DURATION * 1000));
          } else {
            removeCookie('verificationEmailCooldown');
          }
        }
      }
    };

    // Mettre à jour immédiatement
    updateCountdown();

    // Mettre à jour toutes les secondes
    const intervalId = setInterval(updateCountdown, 1000);

    return () => clearInterval(intervalId);
  }, [lastAttemptTime]);

  const setCooldownTimer = () => {
    const now = Date.now();
    setLastAttemptTime(now);
    const expiryTime = now + (COOLDOWN_DURATION * 1000); // Calculer l'heure d'expiration
    setCookie('verificationEmailCooldown', expiryTime.toString(), COOLDOWN_DURATION); // Passer la durée du cooldown en secondes pour maxAge
    setCountdown(COOLDOWN_DURATION); // Mettre à jour le compte à rebours en secondes
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Éviter les soumissions multiples
    if (isSubmitting) {
      return;
    }

    // Vérifier si on est en cooldown
    if (countdown > 0) {
      notify('Veuillez attendre 3 secondes entre chaque tentative', 'warning');
      return;
    }

    const sanitizedEmail = DOMPurify.sanitize(formData.email);
    setIsSubmitting(true);
    setFormError('');

    try {
      logger.info('📝 Tentative de connexion depuis frontend/src/pages/auth/Login.tsx', { email: sanitizedEmail });

      // Activer le cooldown après la tentative
      setCooldownTimer();

      const result = await login(sanitizedEmail, formData.password);

      logger.info('🔐 Résultat de la connexion', {
        success: result.success,
        message: result.message,
        requiresTwoFactor: result.requiresTwoFactor
      });

      // Vérifier si l'authentification à deux facteurs est requise
      if (result.success && result.requiresTwoFactor) {
        logger.info('🔒 Authentification à deux facteurs requise');

        // Rediriger vers la page dédiée de vérification à deux facteurs
        notify('Un code de vérification a été envoyé à votre adresse email', 'info');
        navigate(`/verify-two-factor?maskedEmail=${encodeURIComponent(result.maskedEmail || '')}`);
        return;
      }

      if (result.success) {
        logger.info('✅ Connexion réussie');
        notify(
          result.user?.last_login
            ? `Connexion réussie, votre dernière connexion a eu lieu le ${result.user.last_login}`
            : 'Connexion réussie, c\'est votre première connexion !',
          'success',
          10000
        );

        // Réinitialiser les erreurs en cas de connexion réussie
        setFormError('');
        const from = (location.state as any)?.from?.pathname || '/dashboard';
        logger.info('🚀 Redirection vers la page d\'origine', { from: from, to: from });
        navigate(from);
        return;
      }

      // Gérer les différents cas d'erreur
      if (!result.email_verifier && result.user?.profil_actif === true) {
        logger.warn('⚠️ Email non vérifié');
        notify('Veuillez vérifier votre email avant de continuer, un email de vérification a été envoyé.', 'warning');
        setFormError('Veuillez vérifier votre email avant de continuer, un email de vérification a été envoyé.');
        if (result.redirectTo) {
          navigate(result.redirectTo);
          return;
        }
        setCooldownTimer();
        return;
      }

      // Vérifier si c'est une erreur CSRF spécifique
      if (result.message && (result.message.includes('CSRF') || result.message.includes('Token CSRF invalide'))) {
        logger.error('🔒 Erreur CSRF détectée dans le composant Login');
        notify(result.message, 'error');
        setFormError(result.message);
        return;
      }

      if (result.user?.profil_actif === false) {
        logger.warn('🔒 Compte bloqué, profil_actif sur false !');
        notify('Votre compte a été désactivé. Veuillez contacter le support.', 'error');
        setFormError(`Votre compte a été désactivé. Veuillez contacter le support.`);
        return;
      }

      // Vérifier si le mot de passe a expiré et rediriger vers la page de réinitialisation
      logger.info('🔒 Résultat de la connexion', { result });
      if (result.message === 'Votre mot de passe a expiré. Veuillez le réinitialiser.') {
        navigate('/forgot-password');
      }

      logger.error('❌ Échec de la connexion', { message: result.message });
      notify(result.message || 'Une erreur est survenue pour cette connexion.', 'error');
      setFormError(result.message || 'Une erreur est survenue pour cette connexion.');
      return;

    } catch (err: any) {
      logger.error('🚨 Erreur inattendue lors de la connexion', err);
      // Utiliser directement l'objet d'erreur pour une meilleure traduction
      notify(err|| 'Une erreur est survenue pour cette connexion.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-24 px-4 sm:px-6 lg:px-8">
      <title>Connexion - JobPartiel.fr</title>
      <meta name="description" content="Connectez-vous à votre compte JobPartiel.fr pour accéder à vos services locaux."/>
      <meta name="keywords" content="connexion, compte, jobpartiel, services locaux"/>
      <meta name="robots" content="index, follow"/>

      <div className="max-w-md w-full space-y-8 p-8 bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl">
          <div>
            <h2 className="mt-2 text-center text-3xl font-extrabold text-gray-900 transition-all duration-300">
              Connectez-vous à votre compte
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Ou{' '}
              <Link to="/inscription" className="font-medium text-[#FF7A35] hover:text-[#ff965e] transition-colors duration-300">
                créez un compte
              </Link>
            </p>
          </div>

          {/* Bouton de connexion Google */}
          <div className="mt-4">
            <GoogleSignInButton text="Se connecter avec Google" />
          </div>

          <div className="relative mt-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Ou continuez avec votre email</span>
            </div>
          </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {/* Affichage des erreurs */}
          {formError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{formError}</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={(e) => {
                    setFormData({ ...formData, email: DOMPurify.sanitize(e.target.value) });
                    // Réinitialiser l'erreur quand l'utilisateur tape
                    if (formError) setFormError('');
                  }}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#FF7A35] focus:border-[#FF7A35] sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Mot de passe
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={(e) => {
                    setFormData({ ...formData, password: e.target.value });
                    // Réinitialiser l'erreur quand l'utilisateur tape
                    if (formError) setFormError('');
                  }}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#FF7A35] focus:border-[#FF7A35] sm:text-sm"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700 focus:outline-none"
                  aria-label={showPassword ? "Masquer le mot de passe" : "Afficher le mot de passe"}
                >
                  {showPassword ? (
                    <EyeOff size={20} className="text-gray-500" />
                  ) : (
                    <Eye size={20} className="text-gray-500" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={(e) => setFormData({ ...formData, rememberMe: e.target.checked })}
                className="h-4 w-4 text-[#FF7A35] focus:ring-[#FF7A35] border-gray-300 rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                Se souvenir de moi
              </label>
            </div>

            <div className="text-sm">
              <Link to="/forgot-password" className="font-medium text-[#FF7A35] hover:text-[#ff965e]">
                Mot de passe oublié ?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting || countdown > 0}
              className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] transition-all duration-300 ${
                isSubmitting || countdown > 0
                  ? 'bg-[#FF7A35]/50 cursor-not-allowed'
                  : 'bg-[#FF7A35] hover:bg-[#ff965e] active:bg-[#ff7a35]/80'
              }`}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connexion en cours...
                </span>
              ) : countdown > 0 ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Veuillez patienter {countdown} secondes
                </span>
              ) : (
                'Se connecter'
              )}
            </button>
          </div>
        </form>
        <div className="border-t border-gray-600 my-1 w-1/2 mx-auto" style={{ height: '2px', marginTop: '20px', marginBottom: '1px' }}></div>
        <div className="flex flex-row items-center justify-center mt-6 space-x-4">
          <button className="flex items-center justify-center text-gray-200 hover:text-gray-300 transition duration-300 w-10 h-10 relative group">
            <User size={24} />
            <span className="absolute left-1/2 transform -translate-x-1/2 bg-white text-gray-600 text-xs rounded px-2 py-1 opacity-0 transition-opacity duration-300 group-hover:opacity-100">Bientôt disponible</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
