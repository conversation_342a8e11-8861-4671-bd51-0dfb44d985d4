import { api } from './api';
import { logger } from '../utils/logger';
import { fetchCsrfToken } from './csrf';

const emailService = {
  sendVerificationEmail: async (email: string, token: string) => {
    try {
      logger.info('Envoi email de vérification :', { 
        email, 
        tokenType: typeof token, 
        tokenLength: token.length
      });

      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post('/api/email/send-verification', {
        email,
        token
      }, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      
      logger.info('Réponse email de vérification :', response.data);
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de l\'email de vérification :', error);
      throw error;
    }
  },

  sendPasswordResetEmail: async (email: string, token: string) => {
    try {
      logger.info('Sending password reset email to:', email);
      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post('/api/email/send-reset', {
        email,
        token
      }, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      
      logger.info('Password reset email response:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Error sending password reset email:', error);
      throw error;
    }
  },

  sendWelcomeEmail: async (email: string) => {
    try {
      logger.info('Envoi email de bienvenue :', { 
        email
      });

      await fetchCsrfToken();
      const csrfToken = await fetchCsrfToken();
      const response = await api.post('/api/email/send-welcome', {
        email
      }, {
        headers: { 'X-CSRF-Token': csrfToken },
      });
      
      logger.info('Réponse email de bienvenue :', response.data);
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de l\'email de bienvenue :', error);
      throw error;
    }
  }
};

export default emailService;
