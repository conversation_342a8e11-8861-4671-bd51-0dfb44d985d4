import React, { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { styled } from '@mui/material/styles';

// Composant d'animation pour indiquer le scroll
const ScrollIndicator = styled(motion.div)({
  position: 'absolute',
  bottom: '10px',
  right: '20px',
  padding: '10px 12px',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  background: '#FF6B2C',
  color: 'white',
  borderRadius: '14px',
  cursor: 'pointer',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.25)',
  zIndex: 50,
  fontSize: '0.875rem',
  fontWeight: 500,
  '&:hover': {
    background: '#FF7A35',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.3s ease',
});

interface ReglementationModalProps {
  open: boolean;
  onClose: () => void;
  onAccept: () => void;
  accepted: boolean;
  onAcceptedChange: (accepted: boolean) => void;
}

const ReglementationModal: React.FC<ReglementationModalProps> = ({
  open,
  onClose,
  onAccept,
  accepted,
  onAcceptedChange,
}) => {
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Vérifier si le défilement est nécessaire
    if (open && contentRef.current) {
      const needsScroll = contentRef.current.scrollHeight > contentRef.current.clientHeight;
      setShowScrollIndicator(needsScroll);

      // Ajouter un écouteur d'événement de défilement pour masquer l'indicateur une fois que l'utilisateur a défilé
      const handleScroll = () => {
        if (contentRef.current) {
          const scrollPosition = contentRef.current.scrollTop;
          const scrollHeight = contentRef.current.scrollHeight;
          const clientHeight = contentRef.current.clientHeight;
          
          // Masquer l'indicateur si on a défilé de plus de 100px ou si on est proche de la fin
          setShowScrollIndicator(scrollPosition < 50 && scrollPosition < scrollHeight - clientHeight - 100);
        }
      };

      contentRef.current.addEventListener('scroll', handleScroll);
      
      return () => {
        if (contentRef.current) {
          contentRef.current.removeEventListener('scroll', handleScroll);
        }
      };
    }
  }, [open]);

  const scrollToContent = () => {
    if (contentRef.current) {
      contentRef.current.scrollTo({
        top: contentRef.current.scrollTop + 200,
        behavior: 'smooth'
      });
    }
  };

  const handleAccept = () => {
    onAcceptedChange(true);
    onClose();
    onAccept();
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        style: {
          borderRadius: '16px',
          padding: '14px',
          backgroundColor: '#FFF8F3',
        }
      }}
    >
      <DialogTitle sx={{ 
        color: '#FF6B2C',
        fontWeight: 600,
        fontSize: '1.25rem'
      }}>
        Information importante sur la réglementation
      </DialogTitle>
      <DialogContent ref={contentRef} sx={{ position: 'relative' }}>
        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          Certaines activités sont soumises à des réglementations spécifiques. Avant de les exercer, il est essentiel de vérifier que vous respectez les conditions requises, telles que la possession d'une qualification particulière, un statut professionnel, une autorisation administrative ou une inscription obligatoire à un registre.
        </Typography>

        <Typography variant="h6" sx={{ color: '#FF6B2C', mt: 3, mb: 2 }}>
          Exemples d'activités réglementées en France :
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • <strong>Services à la personne</strong> : Garde d'animaux (ACACED requise), Assistance aux personnes âgées (diplôme requis)
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • <strong>Métiers du bâtiment</strong> : Électricien, Plombier, Peintre, Maçon (qualification professionnelle et inscription au répertoire des métiers)
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • <strong>Services esthétiques</strong> : Coiffeur, Esthéticienne (diplôme professionnel et inscription au répertoire des métiers)
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • <strong>Entretien et réparation</strong> : Réparateur automobile, Carrossier (qualification spécifique et inscription au répertoire des métiers)
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • <strong>Espaces verts</strong> : Paysagiste, Jardinier (diplôme spécifique et inscription au répertoire des métiers selon les cas)
        </Typography>

        <Typography variant="body1" component="div" sx={{ 
          mb: 3,
          backgroundColor: 'rgba(255, 107, 44, 0.1)',
          padding: '16px',
          borderRadius: '8px',
          border: '1px solid rgba(255, 107, 44, 0.2)'
        }}>
          Pour plus d'informations sur les conditions d'exercice et les qualifications requises, consultez le site officiel de BPIFrance Création : 
          <a 
            href="https://bpifrance-creation.fr/entrepreneur/activites-reglementees" 
            target="_blank" 
            rel="noopener noreferrer"
            style={{
              color: '#FF6B2C',
              textDecoration: 'underline',
              marginLeft: '4px'
            }}
          >
            Activités réglementées
          </a>
        </Typography>

        <Typography variant="body1" component="div" sx={{ 
          mb: 2,
          fontWeight: 500,
          color: '#FF6B2C'
        }}>
          En poursuivant, vous vous engagez à :
        </Typography>

        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
          • Vérifier et respecter les conditions légales et réglementaires applicables à votre activité<br/>
          • Obtenir les qualifications et autorisations nécessaires avant de débuter votre activité<br/>
          • Maintenir à jour vos certifications et inscriptions professionnelles<br/>
          • Assumer votre responsabilité en cas de non-respect des réglementations
        </Typography>

        <FormControlLabel
          control={
            <Checkbox 
              checked={accepted}
              onChange={(e) => onAcceptedChange(e.target.checked)}
              sx={{
                color: '#FF6B2C',
                '&.Mui-checked': {
                  color: '#FF6B2C',
                },
              }}
            />
          }
          label="J'ai lu et j'accepte les conditions réglementaires"
        />

        <AnimatePresence>
          {showScrollIndicator && (
            <ScrollIndicator
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ 
                opacity: 1, 
                scale: 1,
                y: [0, -10, 0],
                transition: {
                  y: {
                    repeat: Infinity,
                    duration: 1.5,
                    ease: "easeInOut"
                  }
                }
              }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={scrollToContent}
              whileHover={{ 
                scale: 1.05,
                y: -5
              }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Défiler</span>
              <ChevronDown size={18} />
            </ScrollIndicator>
          )}
        </AnimatePresence>
      </DialogContent>
      <DialogActions>
        <Button 
          onClick={onClose}
          variant="outlined"
          sx={{
            color: '#FF6B2C',
            borderColor: '#FF6B2C',
            '&:hover': {
              borderColor: '#FF7A35',
              backgroundColor: 'rgba(255, 107, 44, 0.04)',
            },
          }}
        >
          Annuler
        </Button>
        <Button
          onClick={handleAccept}
          variant="contained"
          disabled={!accepted}
          sx={{
            backgroundColor: '#FF6B2C',
            '&:hover': {
              backgroundColor: '#FF7A35',
            },
            '&.Mui-disabled': {
              backgroundColor: 'rgba(255, 107, 44, 0.3)',
            },
          }}
        >
          Continuer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReglementationModal; 