import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import axios from 'axios';
import DOMPurify from 'dompurify';
import logger from '../../../utils/logger';
import { <PERSON><PERSON><PERSON>, HelmetProvider } from 'react-helmet-async';
import { motion } from 'framer-motion';
import InterventionZoneSection from '../components/maps/InterventionZoneSection';
import { notify } from '../../../components/Notification';
import { API_CONFIG, API_URL } from '../../../config/api';
import { User as UserIcon, MapPin, BadgeCheck, Clock, Star, MessageCircle, Settings, Trash2, Camera, Calendar, Users, Building2, CheckCircle, Heart, Phone, CircleSlash, CheckCircle2, AlertCircle, Search, User, Palmtree, LockIcon, Lock, InfoIcon, XIcon, CheckIcon, History } from 'lucide-react';
import AvatarModal from '../../../components/AvatarModal';
import ServiceManagement from '../services/ServiceManagement';
import GalleryRealizations from '../gallery/GalleryRealizations';
import ModalPortal from '../../../components/ModalPortal';
import { Tooltip, Checkbox } from '@mui/material';
import { getCommonHeaders, getMultipartHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';
import { GalleryFolder, GalleryModalData } from '../gallery/types';
import { useJobi } from '../../../contexts/JobiContext';
import { useImageCompression } from '../../../utils/imageCompressor';
import { UserService } from '../services/types';
import { useParams, useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { useCreateNotification } from '@/hooks/useCreateNotification';
import { BadgesDisplay } from './badges';
import { TiptapInstance } from '../../../components/TiptapEditor';
import useContentModeration from '../../../hooks/useContentModeration';
import ReviewModal from '../../../components/ReviewModalPopupDepotAvis';
import { useReviews } from '../../../hooks/useReviews';
import ModalReview from '@/components/ReviewModalSelecteurMission';
import ReviewModalListeDesAvis from '../../../components/ReviewModalListeDesAvis';
import UserProfileModal from '../../../components/UserProfileModal';
import { profileService, ProfileStats } from '../../../services/profile';
import { usePublicUserActions } from '../../../hooks/usePublicUserActions';
import RecentUserActions from '../../../components/common/RecentUserActions';
import { useSubscription } from '@/hooks/useSubscription'; // Permet de récupérer les options d'abonnement utilisateur abonnement actif et des options d'abonnement par défaut
import { Box, Typography } from '@mui/material';
import { EntrepriseVerificationModal } from '../components/DocumentVerification';
import AiGenerationSystem from '../../../components/ai/AiGenerationSystem';
import ReportProfileModal from '../../../components/profile/ReportProfileModal';
import ConfirmAddressModal from '../../../components/profile/ConfirmAddressModal';
import ConfirmPhoneModal from '../../../components/profile/ConfirmPhoneModal';
import ConfirmNameModal from '../../../components/profile/ConfirmNameModal';
import ConfirmBioModal from '../../../components/profile/ConfirmBioModal';
import ConfirmDeleteReviewModal from '../../../components/profile/ConfirmDeleteReviewModal';
import GalleryModal from '../../../components/profile/GalleryModal';
import AboutSection from '../../../components/profile/AboutSection';
import StatsAndProfessionalInfoSection from '../../../components/profile/StatsAndProfessionalInfoSection';
import SubscriptionLimitsSection from '../../../components/profile/SubscriptionLimitsSection';
import SloganSection from '../../../components/profile/SloganSection';
import ShareModal from '../../../components/profile/ShareModal';

// Types et fonctions importés des fichiers utilitaires
import { ProfilData, ProfilCompletionField } from './profileUtils';
import { Review, handleReportProfile, getAllProfilePhotos } from './reviewReportUtils';
import { DisponibilitesConsolidees, JOURS, formatDate, formatHeure, stripHtml, handleVacationMode as handleVacationModeUtil, consolidateDisponibilites, isDocumentExpiringSoon } from './miscUtils';
import { handleAddPhotoToGallery, handleRemovePhotoFromGallery as handleRemovePhotoFromGalleryUtil, handleDeleteGallery as handleDeleteGalleryUtil } from './photoGalleryUtils';
import { searchAddress, handleAddressConfirm, handleAddressCancel as handleAddressCancelUtil, handlePhoneConfirm, handlePhoneCancel as handlePhoneCancelUtil, handleProfilUpdate } from './profileUpdateUtils';

// Interfaces
interface SubscriptionConfig {
  gratuit?: {
    interventionAreas?: {
      included: number;
    };
    galleries?: {
      included: number;
    };
    services?: {
      included: number;
    };
    conversations_messages_prives?: {
      included: number;
    };
  };
  premium?: {
    interventionAreas?: {
      included: number;
    };
    galleries?: {
      included: number;
    };
    services?: {
      included: number;
    };
    conversations_messages_prives?: {
      included: number;
    };
  };
}

interface UserData {
  id: string;
  profil?: {
    data?: {
      prenom?: string;
      nom?: string;
      photo_url?: string;
    };
  };
}

const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

const DashboardProfil: React.FC = () => {
  const { user } = useAuth();
  const { slug } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isOwnProfil, setIsOwnProfil] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isVacationMode, setIsVacationMode] = useState(false);
  const [cooldown, setCooldown] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const { updateBalance, profilComplet } = useJobi();
  const [profil, setProfil] = useState<ProfilData | null>(null);
  const editorRef = useRef<TiptapInstance>(null);
  const [editorHtml, setEditorHtml] = useState<string>('');
  const reviewsSectionRef = useRef<HTMLElement>(null);
  const { validateContentSafety } = useContentModeration();
  const [isModerationLoading, setIsModerationLoading] = useState(false);

  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [selectedGallery, setSelectedGallery] = useState<GalleryFolder | null>(null);
  const [galleryModalData, setGalleryModalData] = useState<GalleryModalData>({
    name: '',
    description: ''
  });
  const [isEditingFirstName, setIsEditingFirstName] = useState(false);
  const [isEditingLastName, setIsEditingLastName] = useState(false);
  const [tempFirstName, setTempFirstName] = useState('');
  const [tempLastName, setTempLastName] = useState('');
  const [previousFirstName, setPreviousFirstName] = useState<string>('');
  const [previousLastName, setPreviousLastName] = useState<string>('');
  const [firstName, setFirstName] = useState<string>('Prénom');
  const [lastName, setLastName] = useState<string>('Nom');
  const [numero, setNumero] = useState<string>('');
  const [rue, setRue] = useState<string>('');
  const [codePostal, setCodePostal] = useState<string>('');
  const [ville, setVille] = useState<string>('');
  const [pays, setPays] = useState<string>('');
  const [tempAddress, setTempAddress] = useState<string>('');
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [isConfirmingAddress, setIsConfirmingAddress] = useState(false);
  const [isConfirmingFirstName, setIsConfirmingFirstName] = useState(false);
  const [isConfirmingLastName, setIsConfirmingLastName] = useState(false);
  const [previousAddress, setPreviousAddress] = useState<string>('');
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [tempPhone, setTempPhone] = useState('');
  const [previousPhone, setPreviousPhone] = useState<string>('');
  const [isConfirmingPhone, setIsConfirmingPhone] = useState(false);
  const [isEditingBio, setIsEditingBio] = useState(false);
  const [tempBio, setTempBio] = useState<string>('');
  const [previousBio, setPreviousBio] = useState('');
  const [isConfirmingBio, setIsConfirmingBio] = useState(false);
  const [date_inscription, setDate_inscription] = useState<string>('');
  const [photo_url, setPhoto_url] = useState<string>('');
  const [banner_url, setBanner_url] = useState<string>('');
  const [bannerPosition, setBannerPosition] = useState<'top' | 'center' | 'bottom'>('center'); // New state for banner position
  const [telephone, setTelephone] = useState<string>('');
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [showBannerModal, setShowBannerModal] = useState(false); // Modale toujours utilisée pour l'upload
  const [showBannerPositionControls, setShowBannerPositionControls] = useState(false); // Nouvel état pour afficher les contrôles de position sur la page
  const [isDeleteBannerModalOpen, setIsDeleteBannerModalOpen] = useState(false);

  const [profilCompletion, setProfilCompletion] = useState({
    percentage: 0,
    fields: [] as ProfilCompletionField[]
  });
  const [isConfirmationChecked, setIsConfirmationChecked] = useState(false);
  const [isFirstNameFocused, setIsFirstNameFocused] = useState(false);
  const [isLastNameFocused, setIsLastNameFocused] = useState(false);
  const firstNameInputRef = useRef<HTMLInputElement>(null);
  const lastNameInputRef = useRef<HTMLInputElement>(null);

  const [subscriptionConfig, setSubscriptionConfig] = useState<SubscriptionConfig | null>(null);

  const { compressProfilPhoto, compressBannerPhoto } = useImageCompression();

  // Ajout de l'état pour les disponibilités dans la colonne de droite disponibilitesConsolidees
  const [disponibilitesConsolidees, setDisponibilitesConsolidees] = useState<DisponibilitesConsolidees>({});

  // Ajout d'un état pour les services dans la colonne de droite disponibilitesConsolidees
  const [services, setServices] = useState<UserService[]>([]);

  // Ajout de l'état pour gérer l'affichage des créneaux dans disponibilitesConsolidees
  const [expandedDays, setExpandedDays] = useState<{ [key: string]: boolean }>({});

  // Ajouter les états pour la gestion de la confidentialité
  const [tempPhonePrive, setTempPhonePrive] = useState(false); // Numéro de téléphone privé
  const [isConfirmingPhonePrive, setIsConfirmingPhonePrive] = useState(false); // Confirmer le numéro de téléphone privé
  const [previousPhonePrive, setPreviousPhonePrive] = useState(false); // Numéro de téléphone privé précédent

  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [selectedMissionId, setSelectedMissionId] = useState<string | null>(null);

  const {
    reviews: userReviews,
    loading: reviewsLoading,
    error: reviewsError,
    fetchReviews,
    stats: reviewStats,
    getReview,
    deleteReview
  } = useReviews({
    userId: profil?.id || ''
  });

  const { createJobiNotification } = useCreateNotification();

  // Ajout d'un cache pour stocker le dernier résultat de calcul de complétion afin d'éviter les mises à jour en cascade
  const profilCompletionCache = useRef<{
    lastProfilData: string | null;
    result: { percentage: number; fields: ProfilCompletionField[] } | null;
  }>({
    lastProfilData: null,
    result: null
  });

  // Ajout d'un état pour suivre si un calcul est en cours
  const [isCalculatingCompletion, setIsCalculatingCompletion] = useState(false);

  // Fonction debounce pour limiter les appels
  const debouncedCalculation = useRef<NodeJS.Timeout | null>(null);

  const [selectedReviewerProfile, setSelectedReviewerProfile] = useState<any>(null);
  const [isReviewerProfileModalOpen, setIsReviewerProfileModalOpen] = useState(false);

  const [reviewsRefreshKey, setReviewsRefreshKey] = useState(0);

  const [isDeleteReviewModalOpen, setIsDeleteReviewModalOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);

  const [profileStats, setProfileStats] = useState<ProfileStats | null>(null);

  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);

  // Pour la modale de profil masqué sur notre propre profil (modération)
  const [showModerationModal, setShowModerationModal] = useState(true);

  const [favoriteLimit, setFavoriteLimit] = useState<number | null>(null);
  const [favoriteCount, setFavoriteCount] = useState<number>(0);

  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [verificationInitialType, setVerificationInitialType] = useState<string | undefined>(undefined);

  // État pour la génération IA de la bio
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);

  const [bannerPositionOffset, setBannerPositionOffset] = useState<number>(0);

  // États pour la modal de partage
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  // Mise à jour des stats du profil quand les stats des avis changent
  useEffect(() => {
    if (reviewStats && profil) {
      setProfil(prev => ({
        ...prev!,
        rating: reviewStats.rating,
        total_reviews: reviewStats.total_reviews,
        completion_rate: reviewStats.completion_rate
      }));
    }
  }, [reviewStats]);

  // Rafraîchir les avis quand reviewsRefreshKey change
  useEffect(() => {
    if (profil?.id) {
      fetchReviews(1);
    }
  }, [reviewsRefreshKey, profil?.id]);

  useEffect(() => {
    if (profil?.id && !isOwnProfil) {
      // Récupérer les statistiques du profil
      const fetchProfileStats = async () => {
        try {
          const stats = await profileService.getProfileStats(profil.id);
          setProfileStats(stats);
        } catch (error) {
          logger.info('Erreur lors de la récupération des statistiques du profil:', error);
        }
      };

      fetchProfileStats();
    }
  }, [profil?.id, isOwnProfil]);

  useEffect(() => {
    // ...
    const fetchFavoriteLimit = async () => {
      try {
        const response = await axios.get(`${API_CONFIG.baseURL}/api/favorites`, { withCredentials: true });
        if (typeof response.data.favoriteLimit === 'number') {
          setFavoriteLimit(response.data.favoriteLimit);
        }
        if (Array.isArray(response.data.data)) {
          setFavoriteCount(response.data.data.length);
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération de la limite de favoris:', error);
      }
    };
    fetchFavoriteLimit();
  }, [isOwnProfil, profil?.id]);

  useEffect(() => {
    if (profil) {
      setTempFirstName(profil.firstName || '');
      setTempLastName(profil.lastName || '');
      setTempPhone(profil.telephone || '');
    }
  }, [profil]);

  const calculateProfilCompletion = useCallback(async (
    profil: ProfilData,
    wasProfilComplete: boolean
  ) => {
    // On vérifie que c'est notre profil
    if (!isOwnProfil) return { percentage: 0, fields: [] };

    // Si un calcul est déjà en cours, on ne lance pas un nouveau
    if (isCalculatingCompletion) {
      return profilCompletionCache.current?.result || { percentage: 0, fields: [] };
    }

    // Création d'une empreinte des données du profil pour comparaison
    const profilDataString = JSON.stringify({
      firstName: profil.firstName,
      lastName: profil.lastName,
      email: profil.email,
      photo_url: profil.photo_url,
      bio: profil.bio,
      location: profil.location,
      telephone: profil.telephone,
      ville: profil.ville,
      code_postal: profil.code_postal,
      pays: profil.pays,
      intervention_zone: profil.intervention_zone
    });

    // Vérifier si les données du profil ont changé depuis le dernier calcul
    if (profilCompletionCache.current.lastProfilData === profilDataString &&
        profilCompletionCache.current.result) {
      logger.info('Utilisation du cache pour le calcul de complétion du profil');
      return profilCompletionCache.current.result;
    }

    // Annuler tout calcul précédent en attente
    if (debouncedCalculation.current) {
      clearTimeout(debouncedCalculation.current);
    }

    // Créer une promesse qui sera résolue après le debounce
    return new Promise<{ percentage: number; fields: ProfilCompletionField[] }>((resolve) => {
      debouncedCalculation.current = setTimeout(async () => {
        setIsCalculatingCompletion(true);

        try {
          logger.info('🚀 Calcul de la complétion du profil');

          // Vérifier si l'utilisateur a des services
          let hasServices = false;
          try {
            const { data: services } = await axios.get(`${API_CONFIG.baseURL}/api/services/user`, {
              headers: await getCommonHeaders(),
              withCredentials: true
            });
            hasServices = services && services.length > 0;
          } catch (error) {
            logger.error('Erreur lors de la vérification des services:', error);
          }

          const fields: ProfilCompletionField[] = [
            {
              name: 'Photo de profil',
              isComplete: !!profil.photo_url && !profil.photo_url.includes('avatar/avatar-defaut-jobpartiel.jpg'),
              importance: 'high'
            },
            {
              name: 'Nom complet',
              isComplete: !!profil.firstName && !!profil.lastName &&
                         profil.firstName !== 'Prénom' &&
                         profil.lastName !== 'Nom',
              importance: 'high'
            },
            {
              name: 'Numéro de téléphone',
              isComplete: !!profil.telephone,
              importance: 'high'
            },
            {
              name: 'Adresse complète',
              isComplete: (() => {
                if (!profil.location) return false;
                const locationParts: string[] = profil.location.split(',').map((part: string) => part.trim());
                if (locationParts.length < 4) return false;

                const [streetPart, ville, codePostal, pays] = locationParts;
                const [numero, ...rueParts] = streetPart.split(' ');
                const rue = rueParts.join(' ');

                // Vérifier que les valeurs sont significatives (pas vides ou valeurs par défaut)
                return !!(
                  numero &&
                  rue && rue.length > 1 &&
                  ville && ville.length > 1 &&
                  codePostal && codePostal.length >= 4 &&
                  pays && pays.length > 1 &&
                  // S'assurer que ce ne sont pas des valeurs par défaut
                  ville !== 'undefined' &&
                  codePostal !== 'undefined' &&
                  pays !== 'undefined' &&
                  rue !== 'undefined'
                );
              })(),
              importance: 'high'
            },
            {
              name: 'A propos',
              isComplete: !!profil.bio && profil.bio.length >= 10 && profil.bio !== '<p><br></p>',
              importance: 'high'
            },
            {
              name: 'Services proposés',
              isComplete: hasServices,
              importance: 'high'
            },
            {
              name: 'Zone d\'intervention',
              isComplete: !!profil.intervention_zone?.center &&
                !(profil.intervention_zone.center[0] === 48.8566 && profil.intervention_zone.center[1] === 2.3522),
              importance: 'high'
            },
            {
              name: 'Informations du profil',
              isComplete: !!profil.companyInfo.type_de_profil && profil.companyInfo.type_de_profil !== '',
              importance: 'high'
            },
            {
              name: 'Galerie avec photos',
              isComplete: (profil.galleryFolders?.some((gallery: GalleryFolder) => (gallery.imagesCount || 0) > 0)) || false,
              importance: 'high'
            }
          ];

          const weights = {
            high: 3,
            medium: 2,
            low: 1
          };

          const totalWeight = fields.reduce((sum, field) => sum + weights[field.importance], 0);
          const completedWeight = fields.reduce((sum, field) =>
            sum + (field.isComplete ? weights[field.importance] : 0), 0
          );

          const completionPercentage = Math.round((completedWeight / totalWeight) * 100);

          // Récupérer l'état actuel du profil
          try {
            const { data: userData } = await axios.get(`${API_CONFIG.baseURL}/api/jobi/solde`, {
              headers: await getCommonHeaders(),
              withCredentials: true
            });

            logger.info('🚀 Vérification userData :', userData);
            logger.info('🚀 Vérification wasProfilComplete :', wasProfilComplete);

            // Si le profil est complet (100%) et n'était pas complet avant
            if (completionPercentage === 100 && !wasProfilComplete) {
              try {
                const success = await updateBalance({
                  montant: 10,
                  operation: 'plus',
                  titre: 'Profil Complet',
                  description: '10 Jobi offerts pour avoir complété votre profil'
                });
                if (success) {
                  notify('Vous avez reçu 10 Jobi pour avoir complété votre profil !', 'success');

                  createJobiNotification(
                    'Nouveau Jobi',
                    `Vous avez reçu 10 Jobi gratuitement pour avoir complété votre profil.`
                  );
                }
              } catch (error) {
                logger.error('Erreur lors de l\'ajout des Jobis:', error);
                notify('Erreur lors de l\'ajout des Jobis', 'error');
              }
            }
            // Si le profil n'est plus complet (< 100%) et était complet avant
            else if (completionPercentage < 100 && wasProfilComplete && completionPercentage >= 50) {
              try {
                const success = await updateBalance({
                  montant: 10,
                  operation: 'moins',
                  titre: 'Profil Incomplet',
                  description: '10 Jobi retirés car le profil n\'est plus complet'
                });
                if (success) {
                  notify('10 Jobis ont été retirés de votre compte car le profil n\'est plus complet.', 'warning');

                  createJobiNotification(
                    'Profil incomplet',
                    `Vous avez perdu 10 Jobi car votre profil n'est plus complet, complétez votre profil pour gagner des Jobi.`
                  );
                }
              } catch (error) {
                logger.error('Erreur lors du retrait des jobis:', error);
                notify('Erreur lors du retrait des jobis', 'error');
              }
            }
          } catch (error) {
            logger.error('Erreur lors de la vérification du solde:', error);
          }

          // À la fin de la fonction, mettre à jour le cache
          const result = { percentage: completionPercentage, fields };
          profilCompletionCache.current = {
            lastProfilData: profilDataString,
            result
          };
          setIsCalculatingCompletion(false);
          resolve(result);
        } catch (error) {
          logger.error('Erreur lors du calcul de la complétion du profil:', error);
          setIsCalculatingCompletion(false);
          resolve({ percentage: 0, fields: [] });
        }
      }, 500); // Debounce de 500ms
    });
  }, [isOwnProfil]);

  useEffect(() => {
    const updateProfilCompletion = async () => {
      if (!profil || !isOwnProfil) return;
      try {
        const completion = await calculateProfilCompletion(profil, profilComplet);
        setProfilCompletion(completion);
      } catch (error) {
        logger.error('Erreur lors de la mise à jour de la complétion du profil:', error);
      }
    };
    updateProfilCompletion();
  }, [profil, calculateProfilCompletion, profilComplet]);

  // Récupérer la configuration de base des abonnements gratuits et premium
  useEffect(() => {
    const loadSubscriptionConfig = async () => {
      try {
        const headers = await getCommonHeaders();
        const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions`, {
          headers,
          withCredentials: true
        });
        setSubscriptionConfig(response.data.data);
        logger.info('🚀 Récupération de la configuration des abonnements gratuits et premium :', response.data.data);
      } catch (error) {
        logger.error('Erreur lors du chargement de la configuration des abonnements:', error);
      }
    };
    loadSubscriptionConfig();
  }, []);

  // Récupérer les donnees utilisateur via l'API du profil sauvegardé au chargement de la page
  useEffect(() => {
    const fetchUserType = async () => {
      try {
        setIsLoading(true);
        const isOwn = !slug || slug === user?.profil?.data?.slug;
        setIsOwnProfil(isOwn);
        logger.info('🔍 Type de profil:', { isOwn, slug, currentUserId: user?.id });

        const endpoint = isOwn ? '/api/users/profil' : `/api/users/profil/${slug}`;
        logger.info('🔍 Endpoint utilisé:', endpoint);

        const response = await axios.get(endpoint, API_CONFIG);
        const userData = response.data;

        // Si profil est masqué, on ne récupère pas les données
        if (userData && userData.masqué) {
          setProfil({
            id: userData.id,
            firstName: '',
            lastName: '',
            email: '',
            photo_url: '',
            location: '',
            bio: '',
            slogan: '',
            hourly_rate: 0,
            profil_verifier: false,
            identite_verifier: false,
            entreprise_verifier: false,
            assurance_verifier: false,
            profil_complet: false,
            rating: 0,
            total_reviews: 0,
            completion_rate: 0,
            telephone: '',
            telephone_prive: false,
            ville: '',
            code_postal: '',
            pays: '',
            responseTime: { average: 0, lastWeek: 0 },
            connectionsCount: 0,
            companyInfo: {
              type_de_profil: '',
              nom_entreprise: '',
              prenom_entreprise: '',
              statut_entreprise: '',
              siren_entreprise: '',
              code_ape_entreprise: '',
              categorie_entreprise: '',
              effectif_entreprise: '',
              date_insee_creation_entreprise: '',
              date_categorie_entreprise: '',
              date_derniere_mise_a_jour_entreprise_insee: '',
              date_derniere_mise_a_jour_du_client_entreprise: ''
            },
            verificationStatus: {
              identity: { isVerified: false, verificationDate: '' },
              company: { isVerified: false, verificationDate: '' },
              documents: { isVerified: false, verificationDate: '' }
            },
            galleryFolders: [],
            // intervention_zone: { center: [48.8566, 2.3522], radius: 15 },
            mode_vacance: false,
            slug: '',
            is_online: false,
            services: [],
            isPremium: false,
            options: {},
            profil_visible: false,
            profil_id: userData.profil?.data?.id,
            banner_url: userData.profil.data.banner_url || '',
            bannerPosition: userData.profil.data.banner_position || 'center',
            bannerPositionOffset: userData.profil.data.banner_position_offset || 0
          });
          setIsLoading(false);
          return;
        }

        if (userData) {
          logger.info('Récupération totale des données de la sauvegarde de la page profil', userData);

          // Mise à jour synchrone des états
          const updates = {
            firstName: userData.profil.data.prenom,
            lastName: userData.profil.data.nom,
            numero: userData.profil.data.numero,
            rue: userData.profil.data.adresse,
            ville: userData.profil.data.ville,
            codePostal: userData.profil.data.code_postal,
            pays: userData.profil.data.pays,
            telephone: userData.profil.data.telephone,
            telephone_prive: userData.profil.data.telephone_prive,
            tempBio: userData.profil.data.bio,
            date_inscription: userData.profil.data.date_inscription,
            photo_url: userData.profil.data.photo_url,
            banner_url: userData.profil.data.banner_url || '',
            banner_position: userData.profil.data.banner_position || 'center',
            banner_position_offset: userData.profil.data.banner_position_offset || 0,
            type_de_profil: userData.profil.data.type_de_profil,
            nom_entreprise: userData.profil.data.nom_entreprise,
            prenom_entreprise: userData.profil.data.prenom_entreprise,
            statut_entreprise: userData.profil.data.statut_entreprise,
            siren_entreprise: userData.profil.data.siren_entreprise,
            code_ape_entreprise: userData.profil.data.code_ape_entreprise,
            categorie_entreprise: userData.profil.data.categorie_entreprise,
            effectif_entreprise: userData.profil.data.effectif_entreprise,
            mode_vacance: userData.profil.data.mode_vacance,
            slug: userData.profil.data.slug,
            profil_visible: userData.profil.data.profil_visible,
            slogan: userData.profil.data.slogan || ''
          };

          logger.info('🔍 Valeurs du profil mis à jour:', updates);

          // Mettre à jour tous les états en une fois
          setFirstName(updates.firstName || 'Prénom');
          setLastName(updates.lastName || 'Nom');
          setNumero(updates.numero);
          setRue(updates.rue);
          setVille(updates.ville);
          setCodePostal(updates.codePostal);
          setPays(updates.pays);
          setTelephone(updates.telephone);
          setTempPhonePrive(updates.telephone_prive);
          setPreviousPhonePrive(updates.telephone_prive);
          setTempBio(updates.tempBio);
          setDate_inscription(userData.date_inscription);
          setPhoto_url(updates.photo_url);
          setBanner_url(userData.profil.data.banner_url || '');
          setBannerPosition(updates.banner_position || 'center');
          setBannerPositionOffset(updates.banner_position_offset || 0);
          setIsVacationMode(updates.mode_vacance);

          // Mettre à jour le profil immédiatement avec les nouvelles données
          const profilId = userData.id;
          if (profilId) {  // Toujours utiliser l'ID de la table users
            setProfil({
              id: profilId,
              firstName: updates.firstName || 'Prénom',
              lastName: updates.lastName || 'Nom',
              email: userData.email || '',
              photo_url: updates.photo_url || '',
              banner_url: userData.profil.data.banner_url || '',
              bannerPosition: userData.profil.data.banner_position || 'center',
              bannerPositionOffset: userData.profil.data.banner_position_offset || 0,
              location: (() => {
                // Vérifier que les valeurs sont définies et non vides
                const numero = updates.numero && updates.numero !== 'undefined' ? updates.numero : '';
                const rue = updates.rue && updates.rue !== 'undefined' ? updates.rue : '';
                const ville = updates.ville && updates.ville !== 'undefined' ? updates.ville : '';
                const codePostal = updates.codePostal && updates.codePostal !== 'undefined' ? updates.codePostal : '';
                const pays = updates.pays && updates.pays !== 'undefined' ? updates.pays : '';

                // Ne construire la chaîne que si toutes les valeurs sont présentes
                if (numero && rue && ville && codePostal && pays) {
                  return `${numero} ${rue}, ${ville}, ${codePostal}, ${pays}`.trim();
                }
                return ''; // Retourner une chaîne vide si des valeurs sont manquantes
              })(),
              bio: updates.tempBio || '',
              slogan: userData.profil.data.slogan || '',
              telephone: updates.telephone || '',
              telephone_prive: updates.telephone_prive,
              ville: updates.ville || '',
              code_postal: updates.codePostal || '',
              pays: updates.pays || '',
              hourly_rate: 35,
              profil_verifier: userData.profil_verifier || false,
              identite_verifier: userData.identite_verifier || false,
              entreprise_verifier: userData.entreprise_verifier || false,
              assurance_verifier: userData.assurance_verifier || false,
              is_online: userData.is_online || false,
              profil_complet: userData.profil_complet || false,
              intervention_zone: userData.profil.data.intervention_zone || {
                center: [48.8566, 2.3522], // Paris par défaut si pas de données
                radius: 15
              },
              rating: userData.profil.data.rating || 0,
              total_reviews: userData.profil.data.total_reviews || 0,
              completion_rate: userData.profil.data.completion_rate || 0,
              responseTime: userData.profil.data.responseTime || { average: 0, lastWeek: 0 },
              connectionsCount: userData.profil.data.connectionsCount || 0,
              companyInfo: {
                type_de_profil: userData.profil.data.type_de_profil || '',
                nom_entreprise: userData.profil.data.nom_entreprise || '',
                prenom_entreprise: userData.profil.data.prenom_entreprise || '',
                statut_entreprise: userData.profil.data.statut_entreprise || '',
                siren_entreprise: userData.profil.data.siren_entreprise || '',
                code_ape_entreprise: userData.profil.data.code_ape_entreprise || '',
                categorie_entreprise: userData.profil.data.categorie_entreprise || '',
                effectif_entreprise: userData.profil.data.effectif_entreprise || '',
                date_insee_creation_entreprise: userData.profil.data.date_insee_creation_entreprise || '',
                date_categorie_entreprise: userData.profil.data.date_categorie_entreprise || '',
                date_derniere_mise_a_jour_entreprise_insee: userData.profil.data.date_derniere_mise_a_jour_entreprise_insee || '',
                date_derniere_mise_a_jour_du_client_entreprise: userData.profil.data.date_derniere_mise_a_jour_du_client_entreprise || '',
                date_validation_document_identite: userData.profil.data.date_validation_document_identite || '',
                date_validation_document_entreprise: userData.profil.data.date_validation_document_entreprise || '',
                date_validation_document_assurance: userData.profil.data.date_validation_document_assurance || ''
              },
              verificationStatus: {
                identity: {
                  isVerified: userData.profil.data.identity_verified || false,
                  verificationDate: userData.profil.data.date_validation_document_identite || ''
                },
                company: {
                  isVerified: userData.profil.data.company_verified || false,
                  verificationDate: userData.profil.data.date_validation_document_entreprise || ''
                },
                documents: {
                  isVerified: userData.profil.data.documents_verified || false,
                  verificationDate: userData.profil.data.date_validation_document_assurance || ''
                }
              },
              galleryFolders: userData.galleryFolders || [],
              mode_vacance: userData.profil.data.mode_vacance || false,
              slug: userData.profil.data.slug || '',
              isPremium: userData.isPremium ?? userData.profil.data.isPremium,
              options: {
                franceEntiere: userData.profil.data.france_entiere || false
              },
              profil_visible: userData.profil.data.profil_visible || false,
              profil_id: userData.profil?.data?.id
            });
          }
        } else {
          if (slug === '') {
            notify('Aucune donnée utilisateur trouvée, veuillez vérifier le profil.', 'error');
          }
          logger.warn('⚠️ Aucune donnée utilisateur reçue');
        }
      } catch (err: any) {
        logger.error('❌ Erreur lors de la récupération des données de la sauvegarde du profil, Erreur complète:', err);
        logger.error('❌ Message d\'erreur:', err.message);
        if (err.response) {
          logger.error('❌ Données de réponse:', err.response.data);
          logger.error('❌ Statut:', err.response.status);

          // Si le profil n'existe pas (404), rediriger vers la page 404
          if (err.response.status === 404) {
            navigate('/404');
            notify('Ce profil n\'existe pas.', 'error');
            return;
          }
        }

        // N'afficher le message d'erreur que si c'est le profil de l'utilisateur connecté
        if (slug === '') {
          notify('Aucune donnée trouvée. Veuillez compléter votre profil en cliquant sur les crayons à côté des champs.', 'error', 10000);
        }else{
          notify('Aucune donnée trouvée sur le profil de l\'utilisateur.', 'error', 10000);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserType();
  }, [user, slug, navigate]);

  useEffect(() => {
    const checkIfFavorite = async () => {
      if (!isOwnProfil && profil?.id) {
        try {
          const response = await axios.get(`${API_CONFIG.baseURL}/api/favorites`, API_CONFIG);
          const favorites = response.data.data;

          logger.info('🔍 Vérification des favoris :', favorites);
          setIsFavorite(favorites.some((fav: any) => fav.favorite_user_id === profil.id));
        } catch (error) {
          logger.error('Erreur lors de la vérification des favoris:', error);
        }
      }
    };

    checkIfFavorite();
  }, [isOwnProfil, profil?.id]);

  const toggleFavorite = async () => {
    if (!profil?.id) return;
    if (!isFavorite && favoriteLimit !== null && favoriteCount >= favoriteLimit) {
      notify(`Vous avez atteint la limite de ${favoriteLimit} favoris. Supprimez un favori ou passez Premium pour en ajouter davantage.`, 'warning');
      return;
    }
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      if (isFavorite) {
        await axios.delete(`${API_CONFIG.baseURL}/api/favorites/${profil.id}`, {
          headers,
          withCredentials: true
        });
        notify('Profil retiré des favoris', 'success');
        setFavoriteCount((c) => Math.max(0, c - 1));
      } else {
        await axios.post(`${API_CONFIG.baseURL}/api/favorites/${profil.id}`, {}, {
          headers,
          withCredentials: true
        });
        notify('Profil ajouté aux favoris, vous pouvez le retrouver dans votre liste de favoris', 'success');
        setFavoriteCount((c) => c + 1);
      }
      setIsFavorite(!isFavorite);
    } catch (error: any) {
      notify(error.response?.data?.message || 'Erreur lors de la modification des favoris', 'error');
      logger.error('Erreur lors de la modification des favoris:', error);
    }
  };

   const handleAvatarUpload = async (file: File) => {
    try {
      // Vérification du type et de la taille du fichier
      const validImageTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validImageTypes.includes(file.type)) {
        notify('Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.', 'error');
        return;
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      const fileSizeInMB = (file.size / 1024 / 1024).toFixed(2);
      if (file.size > maxSize) {
        notify('La taille du fichier ne doit pas dépasser 5 MB. Votre fichier fait ' + fileSizeInMB + ' MB.', 'error');
        return;
      }

      // Compression de l'image avant l'upload
      notify('Image en cours de traitement, veuillez patienter.', 'info');
      const compressedFile = await compressProfilPhoto(file);

      // Récupérer l'ID temporaire de l'image s'il existe
      const tempImageId = (file as any).tempImageId || `image-temp-${Date.now()}`;

      // Création du FormData avec l'image compressée
      const formData = new FormData();
      formData.append('avatar', compressedFile);
      formData.append('tempImageId', tempImageId);

      // S'assurer d'avoir un token CSRF valide avant la requête
      await fetchCsrfToken();

      const headers = await getMultipartHeaders();
      logger.info('📤 Headers de la requête:', headers);

      const response = await fetch(`${API_CONFIG.baseURL}/api/users/profil/photo`, {
        method: 'POST',
        headers,
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de l\'upload de la photo');
      }

      const data = await response.json();

      // Mise à jour immédiate de l'URL de la photo
      const newPhotoUrl = data.photo_url;

      // Mise à jour synchronisée des deux states
      setPhoto_url(newPhotoUrl);
      setProfil(prev => {
        if (!prev) return null;
        const updated = {
          ...prev,
          photo_url: newPhotoUrl
        };
        // Recalculer la complétion du profil après la mise à jour de la photo
        calculateProfilCompletion(updated, profilComplet).then(setProfilCompletion);
        return updated;
      });

      // Forcer un rafraîchissement du composant
      setTimeout(() => {
        setPhoto_url(prevUrl => {
          if (prevUrl === newPhotoUrl) {
            // Re-trigger le rendu même si l'URL est la même
            return newPhotoUrl + '?t=' + new Date().getTime();
          }
          return prevUrl;
        });
      }, 0);

      setShowAvatarModal(false);
      notify('Photo de profil mise à jour avec succès.', 'success');
    } catch (err) {
      logger.warn('Erreur lors de l\'upload de la photo:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la mise à jour de la photo de profil';
      notify(errorMessage, 'error');
    }
  };

  const handleBannerUpload = async (file: File, position: 'top' | 'center' | 'bottom' = 'center') => {
    try {
      // Vérification du type et de la taille du fichier
      const validImageTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validImageTypes.includes(file.type)) {
        notify('Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.', 'error');
        return;
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      const fileSizeInMB = (file.size / 1024 / 1024).toFixed(2);
      if (file.size > maxSize) {
        notify('La taille du fichier ne doit pas dépasser 5 MB. Votre fichier fait ' + fileSizeInMB + ' MB.', 'error');
        return;
      }

      // Compression de l'image avant l'upload
      notify('Image en cours de traitement, veuillez patienter.', 'info');
      const compressedFile = await compressBannerPhoto(file);

      // Récupérer l'ID temporaire de l'image s'il existe
      const tempImageId = (file as any).tempImageId || `image-temp-${Date.now()}`;

      // Création du FormData avec l'image compressée
      const formData = new FormData();
      formData.append('banner', compressedFile);
      formData.append('tempImageId', tempImageId);
      formData.append('position', position);
      formData.append('position_offset', bannerPositionOffset.toString());

      // S'assurer d'avoir un token CSRF valide avant la requête
      await fetchCsrfToken();

      const headers = await getMultipartHeaders();
      logger.info('📤 Headers de la requête:', headers);

      const response = await fetch(`${API_CONFIG.baseURL}/api/users/profil/banner`, {
        method: 'POST',
        headers,
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de l\'upload de la bannière');
      }

      const data = await response.json();

      // Mise à jour immédiate de l'URL de la bannière
      const newBannerUrl = data.banner_url;

      // Mise à jour synchronisée des deux states
      setBanner_url(newBannerUrl);
      setProfil(prev => {
        if (!prev) return null;
        const updated = {
          ...prev,
          banner_url: newBannerUrl,
          bannerPosition: position
        };
        // Recalculer la complétion du profil après la mise à jour de la bannière
        calculateProfilCompletion(updated, profilComplet).then(setProfilCompletion);
        return updated;
      });

      // Forcer un rafraîchissement du composant
      setTimeout(() => {
        setBanner_url(prevUrl => {
          if (prevUrl === newBannerUrl) {
            // Re-trigger le rendu même si l'URL est la même
            return newBannerUrl + '?t=' + new Date().getTime();
          }
          return prevUrl;
        });
      }, 0);

      setShowBannerModal(false);
      notify('Bannière de profil mise à jour avec succès.', 'success');
    } catch (err) {
      logger.warn('Erreur lors de l\'upload de la bannière:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la mise à jour de la bannière de profil';
      notify(errorMessage, 'error');
    }
  };

  const handleDeleteBanner = async () => {
    if (!banner_url) return;
    setIsDeleteBannerModalOpen(true);
  };

  // Nouvelle fonction pour confirmer la suppression de la bannière
  const confirmDeleteBanner = async () => {
    try {
      // S'assurer d'avoir un token CSRF valide avant la requête
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/users/profil/banner`, {
        method: 'DELETE',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la suppression de la bannière');
      }

      // Mise à jour des états
      setBanner_url('');
      setProfil(prev => {
        if (!prev) return null;
        const updated = {
          ...prev,
          banner_url: '',
          bannerPosition: 'center' as 'top' | 'center' | 'bottom'
        };
        return updated;
      });

      notify('Bannière de profil supprimée avec succès.', 'success');
    } catch (err) {
      logger.warn('Erreur lors de la suppression de la bannière:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la suppression de la bannière de profil';
      notify(errorMessage, 'error');
    } finally {
      setIsDeleteBannerModalOpen(false);
    }
  };

  // Gestionnaires pour les galeries
  const handleCreateGallery = async () => {
    try {
      const formData = new FormData();
      formData.append('name', galleryModalData.name);
      if (galleryModalData.description) {
        formData.append('description', galleryModalData.description);
      }
      if (galleryModalData.cover_image) {
        formData.append('cover_image', galleryModalData.cover_image);
      }

      notify('Galerie créée avec succès', 'success');
      setIsGalleryModalOpen(false);
      setGalleryModalData({ name: '', description: '' });

      // Mettre à jour le profil avec la nouvelle galerie (simulation, à adapter selon la réponse API)
      setProfil(prev => {
        if (!prev) return null;
        const updated = {
          ...prev,
          galleryFolders: [
            ...(prev.galleryFolders || []),
            {
              id: `temp-${Date.now()}`,
              name: galleryModalData.name,
              description: galleryModalData.description,
              imagesCount: 0,
              createdAt: new Date().toISOString(),
              photos: []
            }
          ]
        };
        calculateProfilCompletion(updated, profilComplet).then(setProfilCompletion);
        return updated;
      });
    } catch (error) {
      notify('Erreur lors de la création de la galerie', 'error');
      logger.error('Erreur lors de la création de la galerie:', error);
    }
  };

  const handleEditGallery = async (galleryId: string) => {
    try {
      const formData = new FormData();
      formData.append('name', galleryModalData.name);
      if (galleryModalData.description) {
        formData.append('description', galleryModalData.description);
      }
      if (galleryModalData.cover_image) {
        formData.append('cover_image', galleryModalData.cover_image);
      }

      notify('Galerie mise à jour avec succès', 'success');
      setIsGalleryModalOpen(false);
      setGalleryModalData({ name: '', description: '' });
      setSelectedGallery(null);

      // Mettre à jour le profil avec la galerie modifiée (simulation, à adapter selon la réponse API)
      setProfil(prev => {
        if (!prev) return null;
        const updated = {
          ...prev,
          galleryFolders: (prev.galleryFolders || []).map(gallery =>
            gallery.id === galleryId
              ? { ...gallery, name: galleryModalData.name, description: galleryModalData.description }
              : gallery
          )
        };
        calculateProfilCompletion(updated, profilComplet).then(setProfilCompletion);
        return updated;
      });
    } catch (error) {
      notify('Erreur lors de la mise à jour de la galerie', 'error');
      logger.error('Erreur lors de la mise à jour de la galerie:', error);
    }
  };

  // Gestionnaire pour le partage - ouvre la modal de partage
  const handleShare = () => {
    setIsShareModalOpen(true);
  };

  // Gestionnaire pour le partage classique (menu natif)
  const handleNativeShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `${profil?.firstName} ${profil?.lastName} - JobPartiel.fr`,
          text: `Découvrez le profil de ${profil?.firstName} ${profil?.lastName} sur JobPartiel.fr`,
          url: `${window.location.origin}/profil/${profil?.slug}`,
        });
      } else {
        // Fallback : copier le lien dans le presse-papier
        await navigator.clipboard.writeText(`${window.location.origin}/profil/${profil?.slug}`);
        notify('Lien copié dans le presse-papier', 'success');
      }
    } catch (error) {
      notify('Erreur lors du partage', 'error');
      logger.error('Erreur lors du partage:', error);
    }
  };

  // Liste complète des compléments d'adresse autorisés
  const validComplements = [
    'bis', 'ter', 'quater', 'quint', 'sex', 'sept', 'huit', 'neuf', 'dix',
    'rond point', 'rond-point', 'rdp', 'rdpt', 'allée', 'chemin', 'voie',
    'impasse', 'place', 'square', 'quai', 'avenue', 'boulevard', 'route',
    'route de', 'avenue de', 'chemin de', 'rue de', 'place de', 'impasse de',
    'lotissement', 'hameau', 'village', 'secteur', 'côte', 'chemin rural',
    'voie privée', 'sentier', 'passage', 'cour',
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
    'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
  ].map(comp => comp.toLowerCase());

  // Fonction de recherche d'adresse - utilise la fonction extraite
  const searchAddressCallback = useCallback(
    (query: string) => searchAddress(
      query,
      setNumero,
      setRue,
      setVille,
      setCodePostal,
      setPays
    ),
    [setNumero, setRue, setVille, setCodePostal, setPays]
  );

  // Fonction de confirmation de l'adresse - utilise la fonction extraite
  const handleAddressConfirmCallback = () => {
    handleAddressConfirm(
      numero,
      rue,
      ville,
      codePostal,
      pays,
      validComplements,
      setTempAddress,
      setPreviousAddress,
      setIsEditingAddress,
      setIsConfirmingAddress,
      profil
    );
  };

    // Restaurer les anciennes valeurs - utilise la fonction extraite
  const handleAddressCancelCallback = () => {
    handleAddressCancelUtil(
      profil,
      setNumero,
      setRue,
      setVille,
      setCodePostal,
      setPays,
      setTempAddress,
      setIsEditingAddress,
      setIsConfirmingAddress
    );
  };

  // Mise à jour du profil via l'API - utilise la fonction extraite
  const handleProfilUpdateCallback = async () => {
    await handleProfilUpdate(
      isEditingFirstName,
      isConfirmingFirstName,
      tempFirstName,
      isEditingLastName,
      isConfirmingLastName,
      tempLastName,
      isConfirmingPhone,
      tempPhone,
      isConfirmingPhonePrive,
      tempPhonePrive,
      isConfirmingBio,
      tempBio,
      isEditingAddress,
      isConfirmingAddress,
      numero,
      rue,
      ville,
      codePostal,
      pays,
      profil,
      setProfil,
      setFirstName,
      setLastName,
      setVille,
      setCodePostal,
      setPays,
      setNumero,
      setRue,
      setIsEditingFirstName,
      setIsEditingLastName,
      setIsEditingAddress,
      setIsEditingPhone,
      setIsEditingBio,
      setIsConfirmingFirstName,
      setIsConfirmingLastName,
      setIsConfirmingAddress,
      setIsConfirmingPhone,
      setIsConfirmingPhonePrive,
      setIsConfirmingBio,
      setPreviousPhonePrive
    );
  };

  // Fonction de confirmation du téléphone - utilise la fonction extraite
  const handlePhoneConfirmCallback = () => {
    handlePhoneConfirm(
      tempPhone,
      setIsConfirmingPhone,
      setIsEditingPhone,
      setPreviousPhone,
      profil
    );
  };

  // Fonction d'annulation du téléphone - utilise la fonction extraite
  const handlePhoneCancelCallback = () => {
    handlePhoneCancelUtil(
      profil,
      setTempPhone,
      setTempPhonePrive,
      setPreviousPhonePrive,
      setIsEditingPhone,
      setIsConfirmingPhone,
      setIsConfirmingPhonePrive
    );
  };

  const handlePhoneSaveUpdate = () => {
    const newPhone = DOMPurify.sanitize(tempPhone.trim());

    // Vérifier si le numéro ou la confidentialité a changé
    if (newPhone === profil?.telephone && tempPhonePrive === profil?.telephone_prive && isConfirmingPhone === false && isConfirmingPhonePrive === false) {
      notify('Aucune modification à enregistrer', 'info');
      setIsConfirmingPhone(false);
      setIsConfirmingPhonePrive(false);
      setIsEditingPhone(false);
      return;
    }

    // Mettre à jour l'état du profil avant la sauvegarde
    setProfil(prev => prev ? {
      ...prev,
      telephone: newPhone,
      telephone_prive: tempPhonePrive
    } : null);

    // Mettre à jour le profil
    handleProfilUpdateCallback();

    // Réinitialiser les états
    setIsConfirmingPhone(false);
    setIsConfirmingPhonePrive(false);
    setIsEditingPhone(false);
  };

  // Fonction pour formater la date - utilise la fonction extraite de miscUtils

  const handleAvatarSelect = async (avatarUrl: string) => {
    try {
      // Si l'URL sélectionnée est une URL Supabase directe, on la convertit en URL proxy
      let finalAvatarUrl = avatarUrl;
      const supabasePrefix = 'https://dev-stockage.jobpartiel.fr/storage/v1/object/public/photo_profil/avatar/';
      if (avatarUrl.startsWith(supabasePrefix)) {
        const fileName = avatarUrl.replace(supabasePrefix, '');
        finalAvatarUrl = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/${fileName}`;
      }
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await fetch(`${API_CONFIG.baseURL}/api/users/profil/avatar`, {
        method: 'PUT',
        headers,
        credentials: 'include',
        body: JSON.stringify({ avatarUrl: finalAvatarUrl, userId: profil?.id })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erreur lors de la mise à jour de l\'avatar');
      }

      setProfil(prev => prev ? { ...prev, photo_url: finalAvatarUrl } : null);
      setPhoto_url(finalAvatarUrl); // Mise à jour du state local pour photo en live
      notify('Avatar mis à jour avec succès', 'success');
      setShowAvatarModal(false);
    } catch (error) {
      logger.warn('Erreur lors de la mise à jour de l\'avatar:', error);
      notify(error instanceof Error ? error.message : 'Erreur lors de la mise à jour de l\'avatar', 'error');
    }
  };

  const handleEditorChange = useCallback((content: string) => {
    // Vérifier si le contenu est vide ou ne contient que des espaces/retours à la ligne
    const isEmpty = !content || content.replace(/<[^>]*>/g, '').trim() === '';

    if (isEmpty) {
      setEditorHtml('');
      setTempBio('');
    } else {
      setEditorHtml(content);
      setTempBio(content);
    }
  }, []);



  // Fonction pour convertir le HTML en texte brut - utilise la fonction extraite de miscUtils

  // Fonction pour consolider les horaires - utilise la fonction extraite de miscUtils

  // Fonction pour mettre à jour les disponibilités à partir des services
  const updateDisponibilites = useCallback((services: UserService[]) => {
    // Utilise la fonction extraite de miscUtils
    const disponibilites = consolidateDisponibilites(services);
    setDisponibilitesConsolidees(disponibilites);
  }, []);

  useEffect(() => {
    if (services.length > 0) {
      updateDisponibilites(services);
    }
  }, [services, updateDisponibilites]);

  // Fonction de callback pour ServiceManagement
  const handleServicesUpdate = useCallback(async (updatedServices: UserService[]) => {
    setServices(updatedServices);
    // Vider le cache de complétion du profil
    profilCompletionCache.current = { lastProfilData: null, result: null };
    setProfil(prev => {
      if (!prev) return null;
      const updated = {
        ...prev,
        services: updatedServices
      };
      calculateProfilCompletion(updated, profilComplet).then(setProfilCompletion);
      return updated;
    });
  }, [profilComplet, calculateProfilCompletion]);

  // Fonction pour formater l'heure - utilise la fonction extraite de miscUtils

  const handleOpenMessage = () => {
    if (!profil) return;

    // Stocker les informations dans le localStorage pour la modal de nouvelle conversation
    localStorage.setItem('newMessageInfo', JSON.stringify({
      recipientId: profil.id,
      recipientName: `${profil.firstName} ${profil.lastName}`,
      initialMessage: `Bonjour ${profil.firstName}, Je souhaite vous contacter pour ...`
    }));

    // Rediriger vers la page des messages
    navigate('/dashboard/messages');
  };


  // Wrapper pour handleEditReview
  const handleEditReview = async (reviewId: string) => {
    try {
    const review = await getReview(reviewId);
    if (review) {
        setSelectedReview(review);
        setSelectedMissionId(review.mission_id || null);
      setIsReviewModalOpen(true);
      }
      setReviewsRefreshKey(prev => prev + 1);
    } catch (error) {
      logger.info('Erreur lors de la modification de l\'avis:', error);
    }
  };

  // Wrapper pour handleDeleteReview
  const handleDeleteReview = async (reviewId: string) => {
    setReviewToDelete(reviewId);
    setIsDeleteReviewModalOpen(true);
  };

  const confirmDeleteReview = async () => {
    try {
      if (reviewToDelete) {
        await deleteReview(reviewToDelete);
        setReviewsRefreshKey(prev => prev + 1);
      }
    } catch (error) {
      logger.info('Erreur lors de la suppression de l\'avis:', error);
      notify('Erreur lors de la suppression de l\'avis', 'error');
    } finally {
      setIsDeleteReviewModalOpen(false);
      setReviewToDelete(null);
    }
  };

  const cancelDeleteReview = () => {
    setIsDeleteReviewModalOpen(false);
    setReviewToDelete(null);
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setSelectedReview(null);
    setSelectedMissionId(null);
  };

  const handleOpenReviewerProfile = async (reviewerId: string) => {
    try {
      // D'abord récupérer le slug de l'utilisateur
      const slugResponse = await axios.get(`${API_CONFIG.baseURL}/api/users/get-slug/${reviewerId}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      // Ensuite récupérer le profil complet avec le slug
      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/profil/${slugResponse.data.slug}`, {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      if (response.data) {
        setSelectedReviewerProfile(response.data);
        setIsReviewerProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du profil:', error);
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  const handleReviewSubmitted = () => {
    setReviewsRefreshKey(prev => prev + 1);
  };

  const transformProfilToUserData = (profil: ProfilData | null): UserData | undefined => {
    if (!profil) return undefined;

    return {
      id: profil.id,
      profil: {
        data: {
          prenom: profil.firstName,
          nom: profil.lastName,
          photo_url: profil.photo_url
        }
      }
    };
  };

  const { actions, loading: actionsLoading, error: actionsError } = usePublicUserActions({
    userId: profil?.id || '',
    enabled: !isOwnProfil && !!profil?.id
  });

  // Fonction pour vérifier si le profil a moins de 48h depuis la création
  const isWithin48Hours = useCallback(() => {
    if (!date_inscription) return false;

    const createdAt = new Date(date_inscription);
    const now = new Date();
    const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    return hoursDiff <= 48;
  }, [date_inscription]);

  // Ajout du hook d'abonnement
  const { isLoading: subLoading, error: subError, status: aboStatus, getOptionPremiumUtilisateur } = useSubscription();

  // --- LOGIQUE D'ABONNEMENT CENTRALISÉE ---
  // France entière
  const franceEntiere = aboStatus?.options?.franceEntiere === true || getOptionPremiumUtilisateur('franceEntiere') === true;
  // Rayon d'intervention
  let interventionRadius;
  if (franceEntiere) {
    interventionRadius = 9999; // valeur numérique pour "illimité"
  } else {
    interventionRadius = getOptionPremiumUtilisateur('interventionAreas') || profil?.intervention_zone?.radius || 15;
  }
  // Nombre de services
  const servicesLimit = getOptionPremiumUtilisateur('services') || 2;
  // Nombre de galeries
  const galleriesLimit = getOptionPremiumUtilisateur('galleries') || 1;

  // Ajout récupération de l'option France entière via le hook d'abonnement
  const franceEntiereIncluse = getOptionPremiumUtilisateur('franceEntiere') === true;

  // La fonction de signalement du profil est importée directement de reviewReportUtils

  // Récupération de toutes les photos du profil pour le signalement - utilise la fonction extraite
  const getAllProfilePhotosFormatted = () => {
    return getAllProfilePhotos(profil);
  };

  // Fonction utilitaire pour savoir si une date de validation expire dans moins de 2 mois - utilise la fonction extraite

  // Fonction pour gérer le changement de la bio - utilise stripHtml
  const handleBioChange = (content: string) => {
    // Utilise stripHtml pour extraire le texte brut
    stripHtml(content);
    // La longueur n'est plus utilisée
  };

  // Les fonctions de gestion des photos et galeries sont importées directement de photoGalleryUtils

  const handleBannerPositionSelect = (position: 'top' | 'center' | 'bottom') => {
    setBannerPosition(position);
    setProfil(prev => {
      if (!prev) return null;
      return {
        ...prev,
        bannerPosition: position // Correction : propriété à la racine
      };
    });
    saveBannerPosition(position, bannerPositionOffset); // Ajout de la sauvegarde immédiate
  };

  // Fonction pour sauvegarder la position de la bannière côté backend
  const saveBannerPosition = async (position: 'top' | 'center' | 'bottom', offset: number) => {
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await fetch(`${API_CONFIG.baseURL}/api/users/updateProfil`, {
        method: 'PUT',
        headers,
        credentials: 'include',
        body: JSON.stringify({ banner_position: position, banner_position_offset: offset })
      });
    } catch (error) {
      logger.warn('Erreur lors de la sauvegarde de la position de la bannière:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center space-y-4"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-[#FF6B2C] border-t-transparent rounded-full"
          />
          <p className="text-[#FF6B2C] font-medium">Chargement du profil...</p>
        </motion.div>
      </div>
    );
  }

  // Fenetre profil en attente de modération version publique
  if (!isOwnProfil && profil && profil.profil_visible === false) {
    return (
      <div style={{ minHeight: '60vh', display: 'flex', alignItems: 'center', justifyContent: 'center', background: 'linear-gradient(to bottom, #FFF8F3, white)' }}>
        <Box sx={{ p: 4, bgcolor: '#FFF8F3', border: '2px solid #FF6B2C', borderRadius: 3, textAlign: 'center', maxWidth: 480 }}>
          <Typography variant="h5" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 2 }}>
            Profil en attente de modération
          </Typography>
          <Typography variant="body1" sx={{ color: '#222', mb: 2 }}>
            {"Ce profil est en cours de modération et n'est pas visible actuellement."}
          </Typography>
          <Typography variant="body2" sx={{ color: '#BDBDBD' }}>
            Merci de votre compréhension.
          </Typography>
        </Box>
      </div>
    );
  }

  // Fenetre profil en attente de modération version privée sur notre propre profil (modale fermable)
  if (isOwnProfil && profil && profil.profil_visible === false && showModerationModal) {
    return (
      <div style={{
        position: 'fixed',
        zIndex: 9999,
        top: 0, left: 0, right: 0, bottom: 0,
        background: 'rgba(0,0,0,0.35)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: '#fff',
          border: '2.5px solid #D32F2F',
          color: '#D32F2F',
          borderRadius: 18,
          padding: '36px 38px 28px 38px',
          minWidth: 380,
          maxWidth: 480,
          boxShadow: '0 8px 32px 0 rgba(211,47,47,0.18)',
          position: 'relative',
          textAlign: 'center'
        }}>
          <div style={{ marginBottom: 18 }}>
            <div style={{ fontSize: 24, fontWeight: 800, marginBottom: 8 }}>
              ⚠️ Profil masqué par la modération
            </div>
            <div style={{ fontWeight: 500, fontSize: 16, color: '#B71C1C', marginBottom: 10 }}>
              Votre profil n'est plus visible par les autres utilisateurs.<br />
              Merci de le mettre à jour pour qu'il soit conforme à la charte.<br />
              <span style={{ color: '#D32F2F', fontWeight: 700 }}>
                Une fois les modifications faites, ouvrez un ticket support ici&nbsp;:
                <a
                  href="/dashboard/support/new"
                  style={{
                    color: '#D32F2F',
                    textDecoration: 'underline',
                    fontWeight: 800,
                    marginLeft: 8
                  }}
                >
                  Ouvrir un ticket support
                </a>
              </span>
            </div>
          </div>
          <button
            style={{
              background: '#D32F2F',
              color: '#fff',
              border: 'none',
              borderRadius: 8,
              padding: '12px 32px',
              fontWeight: 700,
              fontSize: 17,
              cursor: 'pointer',
              boxShadow: '0 2px 8px 0 rgba(211,47,47,0.10)',
              marginTop: 8
            }}
            onClick={() => setShowModerationModal(false)}
          >
            J'ai bien pris connaissance
          </button>
        </div>
      </div>
    );
  }

  // Fonction pour vérifier si le profil est prêt pour l'aperçu public
  const isProfileReadyForPublicView = () => {
    if (!profil?.slug) return false;

    // Vérifier que le nom et prénom sont définis et ne sont pas les valeurs par défaut
    const hasValidName = profil?.firstName &&
                        profil?.lastName &&
                        profil.firstName !== 'Prénom' &&
                        profil.lastName !== 'Nom';

    return hasValidName;
  };

  // Rendu du composant
  return (
    <>
      <HelmetProvider>
        <Helmet>
          <title>Mon Profil - JobPartiel.fr</title>
          <meta name="description" content="Profil professionnel sur JobPartiel.fr" />
        </Helmet>
      </HelmetProvider>

      <div className={`min-h-screen space-y-6 relative px-4 md:px-0 transition-opacity duration-300`}>
        {/* En-tête du profil */}
        <header className="relative bg-white rounded-2xl shadow-lg w-full overflow-hidden">
          {isVacationMode && (
            <div className="absolute top-0 left-0 w-full bg-gradient-to-r from-red-500 to-orange-500 p-3 lg:p-4 flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 z-10 shadow-md text-center">
              <div className="flex items-center gap-2 animate-pulse">
                <Clock className="h-5 w-5 text-white" />
                <span className="text-white font-semibold text-sm sm:text-base">Mode vacances activé</span>
              </div>
              <div className="h-6 w-px bg-white/30" />
              <span className="text-white/90">
                {isOwnProfil
                  ? "Les utilisateurs seront informés que vous êtes en vacances et que vos délais de réponse peuvent être plus longs"
                  : "Cet utilisateur est actuellement en vacances, ses délais de réponse peuvent être plus longs"
                }
              </span>
            </div>
          )}

          <div className={`relative w-full ${isVacationMode ? 'mt-16 sm:mt-14' : ''} bg-gradient-to-b from-[#FFF8F3] to-white`}>
            {/* Bannière et photo de profil */}
            <div className="relative pb-0 sm:pb-4 md:pb-0">
              {/* Bannière */}
              <div
                className={`relative ${banner_url ? 'h-40 sm:h-40 md:h-40 lg:h-80' : 'h-32 sm:h-20 md:h-28 lg:h-26'} w-full overflow-hidden rounded-xl ${isOwnProfil ? "cursor-pointer group" : ""}`}
                onClick={() => isOwnProfil && setShowBannerModal(true)}
              >
                {banner_url ? (
                  <>
                    <img
                      src={banner_url}
                      alt="Bannière de profil"
                      className="w-full h-full object-cover"
                      style={{ objectPosition: bannerPosition === 'center' ? `center ${bannerPositionOffset}px` : bannerPosition === 'top' ? `center calc(0% + ${bannerPositionOffset}px)` : `center calc(100% + ${bannerPositionOffset}px)` }}
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    {/* Slogan sur la bannière */}
                    {profil?.slogan && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                        <p className="text-xl md:text-3xl font-bold text-white text-center px-4 py-2 italic shadow-text">
                          "{profil.slogan}"
                        </p>
                      </div>
                    )}
                    {isOwnProfil && (
                      <div className="absolute top-2 right-2 z-10 flex space-x-2">
                        {/* Bouton pour modifier la position */}
                        <button
                          className="bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowBannerPositionControls(true); // Affiche les contrôles de position
                          }}
                          title="Modifier la position de la bannière"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-grid-3x3"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M9 3v18"></path><path d="M15 3v18"></path><path d="M21 9H3"></path><path d="M21 15H3"></path></svg>
                        </button>
                        {/* Bouton pour supprimer la bannière */}
                        <button
                          className="bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteBanner();
                          }}
                          title="Supprimer la bannière"
                        >
                          <Trash2 className="h-4 w-4" strokeWidth={2} />
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="h-full w-full bg-gradient-to-r from-[#FF965E]/20 to-[#FFE4BA]/30"></div>
                )}
                {/* Overlay au survol pour indiquer qu'on peut modifier la bannière */}
                {isOwnProfil && (
                  <div className="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out">
                    <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300 ease-out text-white text-center">
                      <Camera className="h-6 w-6 text-white mx-auto mb-1" strokeWidth={1.5} />
                      <span className="text-xs font-medium tracking-wide block">Modifier la bannière</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Photo de profil */}
              <div className="absolute -bottom-20 left-1/2 transform -translate-x-1/2 md:left-8 md:-bottom-20 md:translate-x-0">
                <div className="relative mx-auto">
                  <div
                    className={`relative w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden shadow-[0_5px_15px_rgba(255,107,44,0.3)] border-4 border-white bg-white ${isOwnProfil ? "cursor-pointer group" : ""} transition-all duration-300 hover:shadow-[0_8px_25px_rgba(255,107,44,0.4)]`}
                    onClick={() => isOwnProfil && setShowAvatarModal(true)}
                  >
                    <img
                      src={photo_url || DEFAULT_AVATAR}
                      alt={`${profil?.firstName} ${profil?.lastName}`}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                    />
                    {/* Statut en ligne (pour les autres utilisateurs) */}
                    {!isOwnProfil && (
                      <Tooltip
                        title={profil?.is_online ? 'En ligne actuellement' : 'L\'utilisateur est actuellement hors ligne'}
                        placement="top"
                        arrow
                        slotProps={{
                          tooltip: {
                            sx: {
                              bgcolor: 'rgba(255, 255, 255, 0.95)',
                              color: profil?.is_online ? '#10B981' : '#6B7280',
                              fontSize: '0.875rem',
                              padding: '0.5rem 0.75rem',
                              borderRadius: '0.5rem',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                              '& .MuiTooltip-arrow': {
                                color: 'rgba(255, 255, 255, 0.95)'
                              }
                            }
                          }
                        }}
                      >
                        <div
                          className={`absolute bottom-3 right-3 bg-white rounded-full p-2 shadow-lg cursor-help backdrop-blur-sm border border-gray-100 transition-transform duration-300 hover:scale-110`}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={`${profil?.is_online ? 'text-emerald-500' : 'text-gray-400'}`}
                          >
                            {profil?.is_online ? (
                              <>
                                <circle cx="12" cy="12" r="10" />
                                <circle cx="12" cy="12" r="4" fill="currentColor" />
                              </>
                            ) : (
                              <>
                                <circle cx="12" cy="12" r="10" />
                                <line x1="4.93" y1="4.93" x2="19.07" y2="19.07" />
                              </>
                            )}
                          </svg>
                        </div>
                      </Tooltip>
                    )}

                    {/* Overlay au survol pour indiquer qu'on peut modifier la photo */}
                    {isOwnProfil && (
                      <div className="absolute inset-0 bg-gradient-to-b from-black/10 to-black/70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out">
                        <div className="transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300 ease-out text-white text-center">
                          <Camera className="h-6 w-6 text-white mx-auto mb-1" strokeWidth={1.5} />
                          <span className="text-xs font-medium tracking-wide block">Modifier</span>
                        </div>
                      </div>
                    )}
                  </div>

                    {/* Badge de vérification */}
                    {profil?.profil_verifier && (
                      <Tooltip
                        title={profil?.companyInfo?.type_de_profil === 'entreprise'
                          ? "Profil vérifié par JobPartiel : identité, documents et informations validés"
                          : "Profil vérifié par JobPartiel : identité validée"
                        }
                        placement="top"
                        arrow
                        slotProps={{
                          tooltip: {
                            sx: {
                              bgcolor: 'rgba(255, 255, 255, 0.95)',
                              color: '#10B981',
                              fontSize: '0.875rem',
                              padding: '0.5rem 0.75rem',
                              borderRadius: '0.5rem',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                              '& .MuiTooltip-arrow': {
                                color: 'rgba(255, 255, 255, 0.95)'
                              }
                            }
                          }
                        }}
                      >
                        <div className="absolute -top-2 -right-2 bg-white rounded-full p-2 shadow-lg cursor-help border border-emerald-100 transition-transform duration-300 hover:scale-110">
                          <BadgeCheck className="h-6 w-6 text-emerald-500" />
                        </div>
                      </Tooltip>
                    )}

                    {/* Type de profil */}
                    {!isOwnProfil && (
                      <div className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1.5 flex items-center gap-2 shadow-md border border-[#FFE4BA] transition-transform duration-300 hover:scale-105">
                        {profil?.companyInfo?.type_de_profil === 'entreprise' ? (
                          <>
                            <Building2 className="h-4 w-4 text-[#FF6B2C]" />
                            <span className="text-xs font-semibold text-[#FF6B2C]">Pro</span>
                          </>
                        ) : (
                          <>
                            <User className="h-4 w-4 text-[#FF6B2C]" />
                            <span className="text-xs font-semibold text-[#FF6B2C]">Particulier</span>
                          </>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Modal de sélection d'avatar */}
                  <AvatarModal
                    isOpen={showAvatarModal}
                    onClose={() => setShowAvatarModal(false)}
                    onUploadPhoto={(file) => handleAvatarUpload(file)}
                    onSelectAvatar={(avatarUrl) => handleAvatarSelect(avatarUrl)}
                    firstName={profil?.firstName}
                    lastName={profil?.lastName}
                  />

                  {/* Modal de sélection de bannière */}
                  <AvatarModal
                    isOpen={showBannerModal}
                    onClose={() => setShowBannerModal(false)}
                    onUploadPhoto={(file) => handleBannerUpload(file, bannerPosition)}
                    title="Choisir une bannière de profil"
                    uploadLabel="Télécharger une bannière"
                    firstName={profil?.firstName}
                    lastName={profil?.lastName}
                    isBannerMode={true}
                    onSelectBannerPosition={handleBannerPositionSelect}
                  />
                </div>
              </div>

              {/* Informations principales */}
            <div className="px-4 sm:px-6 lg:px-8 pb-6 pt-24 md:pt-6 lg:pt-4 md:pl-[220px] lg:pl-[220px]">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                {/* Informations principales */}
                <div className="text-center md:text-left max-w-full w-full md:max-w-md space-y-2">
                  {isEditingName ? (
                    <div className="space-y-4 bg-white/80 p-4 rounded-xl shadow-sm border border-gray-100 max-w-lg mx-auto md:mx-0">
                      <h3 className="text-lg font-semibold text-[#FF6B2C] mb-2">Modifier vos informations</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                          <input
                            ref={firstNameInputRef}
                            type="text"
                            value={tempFirstName}
                            onChange={(e) => setTempFirstName(e.target.value)}
                            placeholder="Prénom"
                            className={`w-full px-3 py-2 bg-white border-2 rounded-lg transition-all duration-300
                              ${isFirstNameFocused ? 'border-red-500 bg-red-50' : 'border-gray-200'}
                              focus:outline-none focus:border-[#FF965E] focus:ring-2 focus:ring-[#FF965E] focus:ring-opacity-50`}
                            onBlur={() => setIsFirstNameFocused(false)}
                          />
                          {isFirstNameFocused && (
                            <div className="absolute -bottom-5 left-0 text-xs text-red-500">
                              Le prénom doit contenir entre 2 et 20 caractères
                            </div>
                          )}
                        </div>
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                          <input
                            ref={lastNameInputRef}
                            type="text"
                            value={tempLastName}
                            onChange={(e) => setTempLastName(e.target.value)}
                            placeholder="Nom"
                            className={`w-full px-3 py-2 bg-white border-2 rounded-lg transition-all duration-300
                              ${isLastNameFocused ? 'border-red-500 bg-red-50' : 'border-gray-200'}
                              focus:outline-none focus:border-[#FF965E] focus:ring-2 focus:ring-[#FF965E] focus:ring-opacity-50`}
                            onBlur={() => setIsLastNameFocused(false)}
                          />
                          {isLastNameFocused && (
                            <div className="absolute -bottom-5 left-0 text-xs text-red-500">
                              Le nom doit contenir entre 2 et 20 caractères
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-3 mt-6">
                        <button
                          onClick={() => {
                            const firstNameLength = (tempFirstName || '').length;
                            const lastNameLength = (tempLastName || '').length;

                            if (firstNameLength > 20) {
                              notify("Le prénom ne peut pas dépasser 20 caractères, il en contient actuellement " + firstNameLength + ".", 'error');
                              return;
                            }
                            if (firstNameLength < 2) {
                              notify("Le prénom doit être d'au moins 2 caractères, il en contient actuellement " + firstNameLength + ".", 'error');
                              return;
                            }
                            if (lastNameLength > 20) {
                              notify("Le nom ne peut pas dépasser 20 caractères, il en contient actuellement " + lastNameLength + ".", 'error');
                              return;
                            }
                            if (lastNameLength < 2) {
                              notify("Le nom doit être d'au moins 2 caractères, il en contient actuellement " + lastNameLength + ".", 'error');
                              return;
                            }

                            if (tempLastName === 'Nom' || tempFirstName === 'Prénom') {
                              notify('Vous devez remplir votre prénom et votre nom avant de les modifier.', 'error');
                              return;
                            }

                            const cleanedFirstName = tempFirstName.replace(/\s+/g, ' ').trim();
                            const cleanedLastName = tempLastName.replace(/\s+/g, ' ').trim();

                            setPreviousFirstName(profil?.firstName || '');
                            setPreviousLastName(profil?.lastName || '');
                            setIsEditingName(false);
                            setIsConfirmingFirstName(true);
                            setIsConfirmingLastName(true);
                            setTempFirstName(cleanedFirstName);
                            setTempLastName(cleanedLastName);
                          }}
                          className="flex-1 bg-[#FF6B2C] text-white px-4 py-2 rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-lg font-medium"
                        >
                          Enregistrer
                        </button>
                        <button
                          onClick={() => {
                            setIsEditingName(false);
                            setTempFirstName(profil?.firstName || '');
                            setTempLastName(profil?.lastName || '');
                          }}
                          className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium"
                        >
                          Annuler
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col gap-1">
                      <div className="flex flex-wrap items-center justify-center md:justify-start gap-2">
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-800 tracking-tight">
                          {profil?.firstName || 'Prénom'} {profil?.lastName || 'Nom'}
                        </h1>
                        {/* Badge Premium - Nouvelle Position */}
                        {!isOwnProfil && profil?.isPremium && (
                          <Tooltip
                            title="Cet utilisateur bénéficie de l'abonnement Premium JobPartiel, lui donnant accès à des fonctionnalités exclusives"
                            placement="top"
                            arrow
                            slotProps={{
                              tooltip: {
                                sx: {
                                  bgcolor: 'rgba(255, 255, 255, 0.95)',
                                  color: '#FF6B2C',
                                  fontSize: '0.875rem',
                                  padding: '0.5rem 0.75rem',
                                  borderRadius: '0.5rem',
                                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                                  '& .MuiTooltip-arrow': {
                                    color: 'rgba(255, 255, 255, 0.95)'
                                  }
                                }
                              }
                            }}
                          >
                            <div className="flex items-center bg-[#FF6B2C] text-white px-2 py-1 rounded-full shadow-lg border-2 border-white transition-transform duration-300 hover:scale-105">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M12 17.75l-6.172 3.247 1.179-6.873L2 9.753l6.914-1.004L12 2.25l3.086 6.499L22 9.753l-5.007 4.371 1.179 6.873z" fill="#FF965E"/>
                              </svg>
                              <span className="text-xs font-bold tracking-wide">Premium</span>
                            </div>
                          </Tooltip>
                        )}
                        {isOwnProfil && ((firstName === 'Prénom' || lastName === 'Nom' || isWithin48Hours())) && (
                          <Tooltip title={isWithin48Hours() && !(firstName === 'Prénom' || lastName === 'Nom') ? "Vous pouvez modifier votre nom et prénom pendant 48h après la création du profil" : "Modifier le nom et prénom"}>
                            <button
                              onClick={() => {
                                setIsEditingName(true);
                                setTempFirstName('');
                                setTempLastName('');
                              }}
                              className="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-all duration-300 hover:shadow-md"
                              aria-label={profil?.firstName === 'Prénom' || profil?.lastName === 'Nom' ? "Saisir le nom et prénom" : "Modifier le nom et prénom"}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600">
                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                <path d="m15 5 4 4"></path>
                              </svg>
                            </button>
                          </Tooltip>
                        )}
                      </div>

                      {/* Informations clés */}
                      <div className="flex flex-wrap items-center justify-center md:justify-start gap-3 text-sm text-gray-600 mt-1">
                        {/* Localisation */}
                        <div className="flex items-center gap-1.5">
                          <MapPin className="h-3.5 w-3.5 text-[#FF6B2C]" />
                          <span>
                            {profil?.ville && profil?.pays ?
                              `${profil?.ville}, ${profil?.pays}` :
                              <span className="italic text-gray-400">Non renseignée</span>
                            }
                          </span>
                          {isOwnProfil && (
                            <button
                              onClick={() => {
                                if (profil?.firstName === 'Prénom' || profil?.lastName === 'Nom') {
                                  notify('Vous devez définir votre prénom et nom avant de modifier votre adresse.', 'error');
                                  return;
                                }
                                setIsEditingAddress(true);
                              }}
                              className="p-1 rounded-full hover:bg-gray-100"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                              </svg>
                            </button>
                          )}
                        </div>

                        {/* Séparateur */}
                        <span className="hidden md:block h-4 w-0.5 bg-gray-200 rounded-full mx-1"></span>

                        {/* Date d'inscription */}
                        <div className="flex items-center gap-1.5">
                          <Calendar className="h-3.5 w-3.5 text-[#FF6B2C]" />
                          <span>Membre depuis {formatDate(date_inscription)}</span>
                        </div>

                        {/* Séparateur */}
                        <span className="hidden md:block h-4 w-0.5 bg-gray-200 rounded-full mx-1"></span>

                        {/* Note */}
                        <div className="flex items-center gap-1.5">
                          <Star className="h-3.5 w-3.5 text-[#FF6B2C]" />
                          <span className="font-medium text-[#FF6B2C]">{profil?.rating ? profil.rating.toFixed(1) : '0.0'}</span>
                          <span className="text-gray-400">({profil?.total_reviews || 0} avis)</span>
                        </div>
                      </div>

                      {/* Modal d'édition d'adresse */}
                      {isEditingAddress && (
                        <div className="mt-3 bg-white/80 p-4 rounded-xl shadow border border-gray-100 max-w-lg mx-auto md:mx-0">
                          <h4 className="text-sm font-semibold text-[#FF6B2C] mb-3">Modifier votre adresse</h4>
                          {/* Recherche d'adresse automatique */}
                          <div className="relative w-full mb-3">
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Recherche d'adresse
                            </label>
                            <div className="relative">
                              <input
                                type="text"
                                placeholder="Rechercher une adresse ..."
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                                value={tempAddress}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  setTempAddress(value);
                                  searchAddressCallback(value);
                                }}
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <Search className="h-4 w-4 text-gray-400" />
                              </div>
                            </div>
                          </div>

                          {/* Champs d'adresse détaillés */}
                          <div className="grid grid-cols-4 gap-2 mb-3">
                            <div className="col-span-1">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Numéro
                              </label>
                              <input
                                type="text"
                                placeholder="Numéro"
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                                value={numero}
                                onChange={(e) => setNumero(e.target.value)}
                              />
                            </div>
                            <div className="col-span-3">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Rue
                              </label>
                              <input
                                type="text"
                                placeholder="Rue"
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                                value={rue}
                                onChange={(e) => setRue(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-3 gap-2 mb-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Code Postal
                              </label>
                              <input
                                type="text"
                                placeholder="Code Postal"
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                                value={codePostal}
                                onChange={(e) => {
                                  const value = e.target.value.replace(/\D/g, '');
                                  if (value.length <= 5) {
                                    setCodePostal(value);
                                  }
                                }}
                                maxLength={5}
                              />
                            </div>
                            <div className="col-span-2">
                              <label className="block text-xs font-medium text-gray-700 mb-1">
                                Ville
                              </label>
                              <input
                                type="text"
                                placeholder="Ville"
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                                value={ville}
                                onChange={(e) => setVille(e.target.value)}
                                maxLength={20}
                              />
                            </div>
                          </div>

                          <div className="mb-4">
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Pays
                            </label>
                            <select
                              value={pays}
                              onChange={(e) => setPays(e.target.value)}
                              className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E] bg-white"
                            >
                              <option value="">Sélectionnez un pays</option>
                              <option value="France">France</option>
                              <option value="Belgique">Belgique</option>
                              <option value="Suisse">Suisse</option>
                              <option value="Luxembourg">Luxembourg</option>
                              <option value="Monaco">Monaco</option>
                            </select>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={handleAddressCancelCallback}
                              className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
                            >
                              Annuler
                            </button>
                            <button
                              onClick={handleAddressConfirmCallback}
                              className="px-3 py-1.5 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors text-sm font-medium shadow-sm hover:shadow"
                            >
                              Enregistrer
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Téléphone */}
                      {(isOwnProfil || (!isOwnProfil && aboStatus?.isPremium && (!profil?.telephone_prive || (profil?.telephone_prive && aboStatus?.isPremium)))) && (
                        <div className="mt-3 flex justify-center md:justify-start">
                          <div className="inline-flex items-center gap-2 bg-white/80 px-3 py-1.5 rounded-lg shadow-sm border border-gray-100">
                            <Phone className="h-4 w-4 text-[#FF6B2C]" />
                            <span className="text-gray-700 font-medium">{profil?.telephone || <span className="italic text-gray-400">Non renseigné</span>}</span>
                            {profil?.telephone_prive && isOwnProfil && (
                              <Lock className="h-3.5 w-3.5 text-[#FF6B2C]" />
                            )}
                            {isOwnProfil && (
                              <button
                                onClick={() => {
                                  if (profil?.firstName === 'Prénom' || profil?.lastName === 'Nom') {
                                    notify('Vous devez définir votre prénom et nom avant de modifier votre numéro.', 'error');
                                    return;
                                  }
                                  setIsEditingPhone(true);
                                  setTempPhone(profil?.telephone || '');
                                }}
                                className="p-1 rounded-full hover:bg-gray-100"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
                                  <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                </svg>
                              </button>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Modal d'édition du téléphone */}
                      {isEditingPhone && (
                        <div className="mt-3 bg-white/80 p-4 rounded-xl shadow border border-gray-100 max-w-lg mx-auto md:mx-0">
                          <h4 className="text-sm font-semibold text-[#FF6B2C] mb-3">Modifier votre téléphone</h4>
                          <div className="mb-3">
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Numéro de téléphone
                            </label>
                            <div className="relative">
                              <input
                                type="tel"
                                value={tempPhone}
                                onChange={(e) => {
                                  const value = e.target.value.replace(/[^\d+]/g, '');
                                  if (value.length <= 15) {
                                    setTempPhone(value);
                                  }
                                }}
                                placeholder="0612345678"
                                className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-[#FF965E] focus:ring-1 focus:ring-[#FF965E]"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <Phone className="h-4 w-4 text-gray-400" />
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 bg-[#FFF8F3] p-2 rounded-lg mb-3">
                            <Checkbox
                              checked={tempPhonePrive}
                              onChange={(e) => {
                                setTempPhonePrive(e.target.checked);
                                setIsConfirmingPhonePrive(true);
                              }}
                              sx={{
                                color: '#FF6B2C',
                                '&.Mui-checked': {
                                  color: '#FF6B2C',
                                },
                                padding: '4px',
                              }}
                              size="small"
                            />
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-gray-700">Numéro privé</span>
                              <Tooltip title="Votre numéro de téléphone ne sera visible que par vous.">
                                <InfoIcon className="h-3.5 w-3.5 text-[#FF6B2C] cursor-help" />
                              </Tooltip>
                            </div>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={handlePhoneCancelCallback}
                              className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium flex items-center gap-1"
                            >
                              <XIcon className="h-3.5 w-3.5" />
                              Annuler
                            </button>
                            <button
                              onClick={handlePhoneConfirmCallback}
                              className="px-3 py-1.5 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors text-sm font-medium flex items-center gap-1 shadow-sm hover:shadow"
                            >
                              <CheckIcon className="h-3.5 w-3.5" />
                              Enregistrer
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Message restriction premium */}
                      {!isOwnProfil && !aboStatus?.isPremium && (
                        <div className="mt-3 flex justify-center md:justify-start">
                          <div className="inline-flex items-center gap-1 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-lg">
                            <Lock className="h-3.5 w-3.5 text-[#FF6B2C]" />
                            <span>Numéro de téléphone visible avec l'abonnement Premium</span>
                          </div>
                        </div>
                      )}

                      {/* Si l'utilisateur a masqué son numéro */}
                      {!isOwnProfil && aboStatus?.isPremium && profil?.telephone_prive && (
                        <div className="mt-3 flex justify-center md:justify-start">
                          <div className="inline-flex items-center gap-1 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-lg">
                            <Lock className="h-3.5 w-3.5 text-orange-500" />
                            <span>L'utilisateur a masqué son numéro</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Boutons d'action */}
                <div className="flex flex-row flex-wrap justify-center md:justify-end gap-2 mt-3 sm:mt-2 md:mt-0">
                  {isOwnProfil ? (
                    <Link
                      to={isProfileReadyForPublicView() ? `/dashboard/profil/${profil?.slug}` : '#'}
                      target="_blank"
                      onClick={(e) => {
                        if (!isProfileReadyForPublicView()) {
                          e.preventDefault();
                          if (!profil?.slug) {
                            notify('Votre profil doit être complété avant de pouvoir accéder à l\'aperçu public.', 'warning');
                          } else if (!profil?.firstName || !profil?.lastName || profil.firstName === 'Prénom' || profil.lastName === 'Nom') {
                            notify('Vous devez définir votre nom et prénom avant d\'accéder à l\'aperçu public.', 'warning');
                          }
                        }
                      }}
                      className={`flex items-center justify-center gap-1.5 px-3 py-2 text-sm bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-300 shadow-sm ${
                        isProfileReadyForPublicView() ? 'cursor-pointer' : 'cursor-not-allowed opacity-70'
                      }`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15 3 21 3 21 9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                      <span className="font-medium">Aperçu public</span>
                    </Link>
                  ) : (
                    <>
                      <button
                        onClick={handleOpenMessage}
                        className="flex-1 lg:flex-none items-center justify-center gap-1.5 px-3 py-2 text-sm bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-lg font-medium flex"
                      >
                        <MessageCircle className="h-4 w-4" />
                        <span>Contacter</span>
                      </button>

                      <button
                        onClick={toggleFavorite}
                        className={`flex-1 lg:flex-none items-center justify-center gap-1.5 px-3 py-2 text-sm rounded-lg transition-all duration-300 shadow-md font-medium flex ${
                          isFavorite
                            ? 'bg-red-50 text-red-600 hover:bg-red-100'
                            : 'bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current text-red-500' : ''}`} />
                        <span>{isFavorite ? 'Favori' : 'Ajouter'}</span>
                      </button>

                      <button
                        onClick={handleShare}
                        className="flex-1 lg:flex-none items-center justify-center gap-1.5 px-3 py-2 text-sm bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-300 shadow-md font-medium flex"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="18" cy="5" r="3"></circle>
                          <circle cx="6" cy="12" r="3"></circle>
                          <circle cx="18" cy="19" r="3"></circle>
                          <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
                          <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
                        </svg>
                        <span>Partager</span>
                      </button>

                      <button
                        onClick={() => setReportModalOpen(true)}
                        className="flex-1 lg:flex-none items-center justify-center gap-1.5 px-3 py-2 text-sm bg-white text-[#FF6B2C] border border-[#FF6B2C] rounded-lg hover:bg-[#FFF8F3] transition-all duration-300 shadow-md font-medium flex"
                        title="Signaler ce profil"
                      >
                        <AlertCircle className="h-4 w-4" />
                        <span>Signaler</span>
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Statistiques et Avis (uniquement pour les profils publics) */}
          {!isOwnProfil && (
            <div className="px-4 sm:px-6 lg:px-8 py-4 border-t border-gray-100">
              {/* Statistiques */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                {/* Taux de réponse */}
                <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div className="p-4 flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
                        <MessageCircle className="h-5 w-5 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {profileStats ?
                          (profileStats.reviews_count > 0 ?
                            `${Math.round(profileStats.response_rate * 100)}%` :
                            '—')
                          : '—'}
                      </div>
                      <div className="text-xs font-medium text-gray-500">Taux de réponse</div>
                    </div>
                  </div>
                </div>

                {/* Missions réalisées */}
                <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div className="p-4 flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-600">
                        {profileStats ? profileStats.missions_count : '0'}
                      </div>
                      <div className="text-xs font-medium text-gray-500">Missions réalisées</div>
                    </div>
                  </div>
                </div>

                {/* Connexions */}
                <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div className="p-4 flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center">
                        <Users className="h-5 w-5 text-purple-600" />
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {profileStats ? profileStats.connections_count : '0'}
                      </div>
                      <div className="text-xs font-medium text-gray-500">Clients connectés</div>
                    </div>
                  </div>
                </div>

                {/* Délai de réponse */}
                <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div className="p-4 flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 flex items-center justify-center">
                        <Clock className="h-5 w-5 text-orange-600" />
                      </div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-orange-600">
                        {profileStats && profileStats.avg_response_time > 0 ?
                          (profileStats.avg_response_time >= 3600 ?
                            `${Math.round(profileStats.avg_response_time / 3600)}h` :
                            `${Math.round(profileStats.avg_response_time / 60)}min`)
                          : '—'}
                      </div>
                      <div className="text-xs font-medium text-gray-500">Délai de réponse</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Avis récents et Activités récentes */}
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-gray-100">
                {/* Dernier avis */}
                <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-5">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 flex items-center justify-center">
                          <MessageCircle className="h-5 w-5 text-amber-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Dernier avis</h3>
                      </div>
                      <div className="flex items-center gap-2 bg-amber-50 px-3 py-1.5 rounded-full">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className="h-4 w-4 text-amber-500"
                              fill={star <= (profil?.rating || 0) ? 'currentColor' : 'none'}
                            />
                          ))}
                        </div>
                        <span className="text-xs font-medium text-amber-700">
                          ({profil?.total_reviews || 0})
                        </span>
                      </div>
                    </div>

                    {/* Afficher le dernier avis */}
                    <ReviewModalListeDesAvis
                      userId={profil?.id || ''}
                      userData={transformProfilToUserData(profil)}
                      currentUser={user}
                      onEditReview={handleEditReview}
                      onDeleteReview={handleDeleteReview}
                      onOpenReviewerProfile={handleOpenReviewerProfile}
                      onOpenMissionSelector={() => setIsReviewModalOpen(true)}
                      onStatsUpdate={(stats) => {
                        if (profil) {
                          profil.rating = stats.rating;
                          profil.total_reviews = stats.total_reviews;
                          profil.completion_rate = stats.completion_rate;
                        }
                      }}
                      refreshKey={reviewsRefreshKey}
                      limit={1}
                      showHeader={false}
                      whiteBackground
                      maxCharacters={70}
                      emptyStateBackgroundColor="#FFFFFF"
                      viewType="profil"
                    />

                    {/* Actions pour les avis */}
                    <div className="flex flex-wrap gap-2 mt-5">
                      {/* Bouton Voir tous les avis */}
                      {profil && typeof profil.total_reviews === 'number' && profil.total_reviews > 0 && (
                        <button
                          onClick={() => {
                            if (reviewsSectionRef.current) {
                              reviewsSectionRef.current.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                              });
                            }
                          }}
                          className="flex-1 py-2.5 px-4 text-[#FF6B2C] bg-white rounded-lg border border-[#FF6B2C]/20 transition-all duration-300 hover:bg-[#FFF8F3] flex items-center justify-center gap-2 text-sm font-medium"
                        >
                          <MessageCircle className="h-4 w-4" />
                          Voir tous les avis
                        </button>
                      )}
                      {profil && (
                        <button
                          onClick={() => setIsReviewModalOpen(true)}
                          className="flex-1 py-2.5 px-4 text-white bg-gradient-to-r from-[#FF6B2C] to-[#FF7A35] rounded-lg transition-all duration-300 hover:shadow-md flex items-center justify-center gap-2 text-sm font-medium"
                        >
                          <MessageCircle className="h-4 w-4" />
                          Déposer un avis
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Activités Récentes */}
                {!isOwnProfil && profil?.id && (
                  <div className="bg-white rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg">
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-5">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center">
                          <History className="h-5 w-5 text-red-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800">Activités récentes</h3>
                      </div>
                      <div className="border-t border-gray-50 pt-3">
                        <RecentUserActions
                          actions={actions}
                          loading={actionsLoading}
                          error={actionsError}
                          className="w-full"
                          variant="content-only"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Badges */}
              <div className="mt-4">
                <BadgesDisplay profil={profil} />
              </div>
            </div>
          )}
        </header>

        {/* Progression du profil : complétion des champs */}
        {isOwnProfil && profilCompletion.percentage < 100 && profilCompletion.fields.length > 0 && (
          <motion.section
            className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Progression du profil</h3>
              <div className="flex items-center space-x-2">
                <div className={`text-2xl font-bold ${
                  profilCompletion.percentage >= 80 ? 'text-green-500' :
                  profilCompletion.percentage >= 50 ? 'text-[#FF6B2C]' :
                  'text-red-500'
                }`}>
                  {profilCompletion.percentage}%
                </div>
              </div>
            </div>

            <div className="h-2 bg-gray-200 rounded-full overflow-hidden mb-4">
              <div
                className={`left-0 top-0 h-full transition-all duration-500 ${
                  profilCompletion.percentage >= 80 ? 'bg-green-500' :
                  profilCompletion.percentage >= 50 ? 'bg-[#FF6B2C]' :
                  'bg-red-500'
                }`}
                style={{ width: `${profilCompletion.percentage}%` }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {profilCompletion.fields.map((field, index) => (
                <div key={typeof field.name === 'string' ? field.name : index} className="flex items-center space-x-2">
                  {field.isComplete ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  ) : field.importance === 'high' ? (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  ) : (
                    <CircleSlash className="h-5 w-5 text-gray-400" />
                  )}
                  <span className={`text-sm ${field.isComplete ? 'text-gray-600' : 'text-gray-400'}`}>
                    {typeof field.name === 'string' ? field.name : ''}
                  </span>
                </div>
              ))}
            </div>
            {profilCompletion.percentage !== 100 && (
              <>
                <div className="mt-4 text-green-600 font-semibold">Recevez 10 jobi lorsque vous aurez complété votre profil à 100% !</div>
              </>
            )}
          </motion.section>
        )}

        {/* Contenu principal */}
        <main className="space-y-6 w-full">
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Colonne principale */}
            <div className="xl:col-span-3 space-y-6">
              {/* Slogan - affiché uniquement si bannière */}
              {banner_url && isOwnProfil && (
                <SloganSection
                  isOwnProfil={isOwnProfil}
                  profil={profil}
                  setProfil={setProfil}
                />
              )}

              {/* À propos, bio */}
              <AboutSection
                isOwnProfil={isOwnProfil}
                isEditingBio={isEditingBio}
                setIsEditingBio={setIsEditingBio}
                tempBio={tempBio}
                setTempBio={setTempBio}
                profil={profil}
                notify={notify}
                setIsFirstNameFocused={setIsFirstNameFocused}
                setIsLastNameFocused={setIsLastNameFocused}
                validateContentSafety={validateContentSafety as (content: string, type: string) => Promise<boolean>}
                setPreviousBio={setPreviousBio}
                setIsConfirmingBio={setIsConfirmingBio}
                setIsModerationLoading={setIsModerationLoading}
                isModerationLoading={isModerationLoading}
                stripHtml={stripHtml}
                editorRef={editorRef as React.RefObject<TiptapInstance | null>}
                handleEditorChange={handleEditorChange}
                handleBioChange={handleBioChange}
                previousBio={previousBio}
                setIsAiConfirmModalOpen={setIsAiConfirmModalOpen}
              />

              {/* Services */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <ServiceManagement onServicesUpdate={handleServicesUpdate} />
              </motion.section>


              {/* Galerie */}
              <motion.section
                className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {/* Liste des galeries */}
                <GalleryRealizations
                  onUpdateProfil={(isDeleting = false, galleryId?: string) => {
                    if (profil && galleryId) {
                      // On récupère la galerie mise à jour
                      const updatedGalleries = profil.galleryFolders.map(gallery => {
                        if (gallery.id === galleryId) {
                          return {
                            ...gallery,
                            // On met à jour le nombre d'images en fonction des photos actuelles
                            imagesCount: isDeleting ? Math.max(0, gallery.imagesCount - 1) : gallery.imagesCount + 1
                          };
                        }
                        return gallery;
                      }) as GalleryFolder[]; // On force le type ici

                      // On met à jour le profil avec les données à jour
                      const updatedProfil = {
                        ...profil,
                        galleryFolders: updatedGalleries
                      };
                      setProfil(updatedProfil);
                      calculateProfilCompletion(updatedProfil, profilComplet).then(setProfilCompletion);
                    }
                  }}
                  handleAddPhotoToGallery={(galleryId, photo) => handleAddPhotoToGallery(galleryId, photo, setProfil, profilComplet, calculateProfilCompletion, profilCompletionCache)}
                  handleRemovePhotoFromGallery={(galleryId, photoId) => handleRemovePhotoFromGalleryUtil(galleryId, photoId, setProfil, profilComplet, calculateProfilCompletion, profilCompletionCache)}
                  handleDeleteGallery={(galleryId) => handleDeleteGalleryUtil(galleryId, setProfil, profilComplet, calculateProfilCompletion, profilCompletionCache)}
                />

              </motion.section>

              {/* Zone d'intervention */}
              <InterventionZoneSection
                avatarUrl={profil?.photo_url}
                initialCenter={profil?.intervention_zone?.center || [48.8566, 2.3522]}
                initialRadius={profil?.intervention_zone?.radius || 15}
                profil={profil}
                maxRadius={interventionRadius}
                isOwnProfil={isOwnProfil}
                onProfilUpdate={(updatedProfil) => {
                  setProfil(updatedProfil);
                }}
                franceEntiereIncluse={franceEntiereIncluse}
              />

              {/* Informations professionnelles */}
              <StatsAndProfessionalInfoSection
                      isOwnProfil={isOwnProfil}
                profil={profil}
                profileStats={profileStats}
                date_inscription={date_inscription}
                formatDate={formatDate}
                isDocumentExpiringSoon={isDocumentExpiringSoon}
                setVerificationInitialType={setVerificationInitialType}
                setIsVerificationModalOpen={setIsVerificationModalOpen}
                getCommonHeaders={getCommonHeaders}
                axios={axios}
                setProfil={setProfil}
                notify={notify}
                logger={logger}
                apiBaseUrl={API_CONFIG.baseURL}
                invalidateProfilCompletionCache={() => {
                  profilCompletionCache.current = { lastProfilData: null, result: null };
                }}
                recalculateProfilCompletion={(profilToUpdate) => {
                  calculateProfilCompletion(profilToUpdate, profilComplet).then(setProfilCompletion);
                }}
              />

              {/* Avis */}
              {!isOwnProfil && (
              <motion.section
                ref={reviewsSectionRef}
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-[#FFF8F3] rounded-lg">
                      <MessageCircle className="h-6 w-6 text-[#FF6B2C]" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800">Avis clients</h2>
                  </div>
                  {profil && profil.total_reviews > 0 && (
                    <div className="flex justify-start sm:justify-end">
                      <button
                        onClick={() => setIsReviewModalOpen(true)}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors text-sm font-medium w-auto"
                      >
                        <MessageCircle className="h-4 w-4" />
                        Déposer un avis
                      </button>
                    </div>
                  )}
                </div>

                <ReviewModalListeDesAvis
                  userId={profil?.id || ''}
                  userData={transformProfilToUserData(profil)}
                  currentUser={user}
                  onEditReview={handleEditReview}
                  onDeleteReview={handleDeleteReview}
                  onOpenReviewerProfile={handleOpenReviewerProfile}
                  onOpenMissionSelector={() => setIsReviewModalOpen(true)}
                  onStatsUpdate={(stats) => {
                    if (profil) {
                      profil.rating = stats.rating;
                      profil.total_reviews = stats.total_reviews;
                      profil.completion_rate = stats.completion_rate;
                    }
                  }}
                  refreshKey={reviewsRefreshKey}
                  limit={1}
                  showHeader={false}
                  whiteBackground
                  maxCharacters={70}
                  emptyStateBackgroundColor="#FFFFFF"
                  viewType="profil"
                />
              </motion.section>
              )}

              {/* Abonnement & limites */}
              {isOwnProfil && profil && subscriptionConfig && (
              <SubscriptionLimitsSection
                profil={profil}
                interventionRadius={interventionRadius}
                servicesLimit={servicesLimit}
                galleriesLimit={galleriesLimit}
                franceEntiere={franceEntiere}
              />
            )}

            </div>

            {/* Barre latérale */}
            <div className="xl:col-span-1 space-y-6">

              {/* Disponibilité */}
              {!isOwnProfil && (
              <motion.section
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <Clock className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Disponibilité</h2>
                </div>

                <div className="space-y-4">
                  <div className="bg-[#FFF8F3] rounded-xl p-4">
                    <div className="space-y-3">
                      {JOURS.map((jour) => {
                        const jourData = disponibilitesConsolidees[jour];
                        const estDisponible = jourData?.disponible;
                        const creneaux = jourData?.creneaux || [];
                        const isExpanded = expandedDays[jour] || false;
                        const hasMoreThanTwo = creneaux.length > 1;
                        const visibleCreneaux = isExpanded ? creneaux : creneaux.slice(0, 1);

                        return (
                          <div key={jour} className="flex items-center justify-between group">
                            <div className="flex items-center space-x-3">
                              <div className={`w-2 h-2 rounded-full ${estDisponible ? 'bg-green-500' : 'bg-red-500'}`}></div>
                              <span className="text-sm text-gray-700 font-medium capitalize">{jour}</span>
                            </div>
                            {estDisponible ? (
                              <div className="flex flex-col items-end gap-1">
                                {visibleCreneaux.map((creneau, index) => (
                                  <div
                                    key={index}
                                    className="text-sm text-[#FF6B2C] font-semibold whitespace-nowrap bg-white/50 px-2 py-0.5 rounded"
                                  >
                                    {formatHeure(creneau.debut)} - {formatHeure(creneau.fin)}
                                  </div>
                                ))}
                                {hasMoreThanTwo && (
                                  <button
                                    onClick={() => setExpandedDays(prev => ({ ...prev, [jour]: !isExpanded }))}
                                    className="text-xs text-[#FF6B2C] hover:text-[#FF7A35] transition-colors mt-1 opacity-1 group-hover:opacity-100"
                                  >
                                    {isExpanded ? 'Voir moins' : `+${creneaux.length - 1} créneau${creneaux.length - 1 > 1 ? 'x' : ''}`}
                                  </button>
                                )}
                              </div>
                            ) : (
                              <span className="text-sm text-red-500 font-semibold">Non dispo</span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {isOwnProfil && (
                    <div className="text-sm text-gray-500 mt-2">
                      Ces horaires sont basés sur vos services actifs et correspondent aux services que vous proposez. Modifiez vos services pour mettre à jour vos disponibilités.
                    </div>
                  )}
                </div>
              </motion.section>
              )}

              {/* Paramètres du profil */}
              {isOwnProfil && (
                <motion.section
                  className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="p-2 bg-[#FFF8F3] rounded-lg">
                      <Settings className="h-6 w-6 text-[#FF6B2C]" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800">Paramètres</h2>
                  </div>

                  <div className="space-y-4">
                    <div className="flex flex-col p-4 bg-[#FFF8F3] rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                      {/* En-tête avec icône et titre */}
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="p-1.5 bg-white rounded-lg">
                          <Palmtree className="h-5 w-5 text-[#FF6B2C]" />
                        </div>
                        <h3 className="text-gray-800 font-semibold">En vacances</h3>
                      </div>

                      {/* Description */}
                      <p className="text-gray-500 text-sm mb-3 pl-1">
                        Avertir les utilisateurs que vous êtes en vacances
                      </p>

                      {/* Contrôles */}
                      <div className="flex items-center justify-between pl-1">
                        <span className={`text-sm font-medium ${isVacationMode ? 'text-[#FF6B2C]' : 'text-gray-500'}`}>
                          {cooldown ? `${cooldownTime}s` : isVacationMode ? 'Activé' : 'Désactivé'}
                        </span>
                        <button
                          onClick={() => handleVacationModeUtil(
                            isVacationMode,
                            setIsVacationMode,
                            setCooldown,
                            setCooldownTime,
                            profil,
                            setProfil,
                            notify,
                            API_CONFIG,
                            getCommonHeaders,
                            fetchCsrfToken,
                            axios
                          )}
                          disabled={cooldown}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-1 ${
                            isVacationMode ? 'bg-[#FF6B2C]' : 'bg-gray-200'
                          } ${cooldown ? 'cursor-not-allowed opacity-50' : 'hover:shadow-md'}`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-sm transition-transform duration-300 ${
                              isVacationMode ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        className="w-full p-4 bg-[#FFF8F3] hover:bg-[#FFE4BA]/20 rounded-xl transition-colors flex items-center justify-center space-x-2 text-gray-700 hover:text-[#FF6B2C]"
                        onClick={() => navigate('/dashboard/parametres')}
                      >
                        <Settings className="h-5 w-5" />
                        <span>Paramètres avancés</span>
                      </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      className="w-full p-4 bg-red-50 hover:bg-red-100 rounded-xl transition-colors flex items-center justify-center space-x-2 text-red-500 hover:text-red-600"
                      onClick={() => navigate('/dashboard/parametres')}
                    >
                      <Trash2 className="h-5 w-5" />
                      <span>Supprimer le compte</span>
                    </motion.button>
                  </div>
                </motion.section>
              )}
            </div>
          </div>
        </main>

        {/* Modale de confirmation de modification d'adresse */}
        {isConfirmingAddress && (
          <ConfirmAddressModal
            open={isConfirmingAddress}
            onClose={() => {
                      setIsConfirmingAddress(false);
                      setTempAddress(previousAddress);
                      setIsConfirmationChecked(false);
                    }}
            onConfirm={() => {
                      if (isConfirmationChecked) {
                        handleProfilUpdateCallback();
                        setIsConfirmingAddress(false);
                        setIsConfirmationChecked(false);
                      } else {
                        notify('Veuillez cocher la case de confirmation', 'error');
                      }
                    }}
            previousAddress={previousAddress}
            tempAddress={tempAddress}
            isConfirmationChecked={isConfirmationChecked}
            setIsConfirmationChecked={setIsConfirmationChecked}
          />
        )}

        {/* Modale de confirmation de modification du téléphone */}
        {isConfirmingPhone && (
          <ConfirmPhoneModal
            open={isConfirmingPhone}
            onClose={() => {
                      setIsConfirmingPhone(false);
                      setTempPhone(previousPhone);
                      setTempPhonePrive(profil?.telephone_prive || false);
                      setIsEditingPhone(false);
              setIsConfirmationChecked(false);
            }}
            onConfirm={() => {
              if (isConfirmationChecked) {
                handlePhoneSaveUpdate();
              } else {
                notify('Veuillez cocher la case de confirmation', 'error');
              }
            }}
            previousPhone={previousPhone}
            previousPhonePrive={previousPhonePrive}
            tempPhone={tempPhone}
            tempPhonePrive={tempPhonePrive}
          />
        )}

        {(isConfirmingFirstName || isConfirmingLastName) && (
          <ConfirmNameModal
            open={isConfirmingFirstName || isConfirmingLastName}
            onClose={() => {
                      setIsConfirmingFirstName(false);
                      setIsConfirmingLastName(false);
                      setTempFirstName(previousFirstName);
                      setTempLastName(previousLastName);
                      setIsConfirmationChecked(false);
                    }}
            onConfirm={() => {
                      if (isConfirmationChecked) {
                        handleProfilUpdateCallback();
                        setIsConfirmingFirstName(false);
                        setIsConfirmingLastName(false);
                        setIsConfirmationChecked(false);
                      } else {
                        notify('Veuillez cocher la case de confirmation', 'error');
                      }
                    }}
            previousFirstName={previousFirstName}
            previousLastName={previousLastName}
            tempFirstName={tempFirstName}
            tempLastName={tempLastName}
            isConfirmingFirstName={isConfirmingFirstName}
            isConfirmingLastName={isConfirmingLastName}
            isConfirmationChecked={isConfirmationChecked}
            setIsConfirmationChecked={setIsConfirmationChecked}
            firstName={firstName}
            lastName={lastName}
            isWithin48Hours={isWithin48Hours()}
          />
        )}

        {/* Modale de confirmation de la bio */}
        {isConfirmingBio && (
          <ConfirmBioModal
            open={isConfirmingBio}
            onClose={() => {
                      setIsConfirmingBio(false);
                      setTempBio(previousBio);
              setIsModerationLoading(false);
            }}
            onConfirm={() => {
                      handleProfilUpdateCallback();
                      setIsConfirmingBio(false);
              setIsModerationLoading(false);
            }}
            previousBio={previousBio}
            tempBio={tempBio}
          />
        )}

        {/* Modale de confirmation du téléphone */}
        {isConfirmingPhone && (
          <ModalPortal>
            <div className="fixed inset-0 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
                <h3 className="text-lg font-semibold mb-4">Confirmer la modification</h3>
                <div className="space-y-4 overflow-y-auto flex-grow pr-2">
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Vos données actuelles :</p>
                    <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through">
                      {telephone || 'Aucun numéro'}
                      <div className="text-sm text-gray-500 mt-1 flex items-center gap-1">
                        <span>État :</span>
                        {previousPhonePrive ? (
                          <span className="flex items-center gap-1">
                            Privé <LockIcon className="h-4 w-4 text-gray-400" />
                          </span>
                        ) : (
                          <span>Public</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Vos nouvelles données :</p>
                    <div className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg">
                      {tempPhone}
                      <div className="text-sm text-gray-500 mt-1 flex items-center gap-1">
                        <span>État :</span>
                        {tempPhonePrive ? (
                          <span className="flex items-center gap-1 text-orange-500">
                            Privé <LockIcon className="h-4 w-4" />
                          </span>
                        ) : (
                          <span>Public</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex items-start bg-red-100 p-2 rounded">
                    <input
                      type="checkbox"
                      id="confirmation-checkbox-phone"
                      className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md"
                      checked={isConfirmationChecked}
                      onChange={() => setIsConfirmationChecked(!isConfirmationChecked)}
                    />
                    <label htmlFor="confirmation-checkbox-phone" className="text-sm">
                      Vous devez cocher cette case pour confirmer la modification.
                    </label>
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
                  <button
                    onClick={() => {
                      setIsConfirmingPhone(false);
                      setIsConfirmationChecked(false);
                      handlePhoneCancelCallback();
                    }}
                    className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={() => {
                      if (isConfirmationChecked) {
                        handlePhoneSaveUpdate();
                      } else {
                        notify('Veuillez cocher la case de confirmation', 'error');
                      }
                    }}
                    className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                  >
                    Confirmer
                  </button>
                </div>
              </div>
            </div>
          </ModalPortal>
        )}

        {/* Modale de création/édition de galerie */}
        <GalleryModal
          open={isGalleryModalOpen}
          onClose={() => {
                        setIsGalleryModalOpen(false);
                        setSelectedGallery(null);
                        setGalleryModalData({ name: '', description: '' });
                      }}
          onSave={() => selectedGallery ? handleEditGallery(selectedGallery.id) : handleCreateGallery()}
          galleryModalData={galleryModalData}
          setGalleryModalData={setGalleryModalData}
          selectedGallery={selectedGallery}
          handleEditGallery={handleEditGallery}
          handleCreateGallery={handleCreateGallery}
        />

        {/* Modal d'avis */}
        {isReviewModalOpen && selectedMissionId && (
          <ReviewModal
            isOpen={isReviewModalOpen}
            onClose={handleCloseReviewModal}
            userId={profil?.id || ''}
            mission_id={selectedMissionId}
            onReviewAdded={() => {
              fetchReviews(1);
              setReviewsRefreshKey(prev => prev + 1);
            }}
            reviewToEdit={selectedReview ? {
              id: selectedReview.id,
              note: selectedReview.rating,
              commentaire: selectedReview.comment,
              qualites: selectedReview.qualites || []
            } : undefined}
          />
        )}
      </div>

      {/* Ajouter le ModalReview */}
      <ModalReview
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        profilId={profil?.id || ''}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Modale de confirmation de suppression d'avis */}
      {isDeleteReviewModalOpen && (
        <ConfirmDeleteReviewModal
          open={isDeleteReviewModalOpen}
          onClose={cancelDeleteReview}
          onConfirm={confirmDeleteReview}
        />
      )}
      {selectedReviewerProfile && (
        <UserProfileModal
          isOpen={isReviewerProfileModalOpen}
          onClose={() => setIsReviewerProfileModalOpen(false)}
          userData={selectedReviewerProfile}
        />
      )}
      {/* MODALE SIGNALEMENT PROFIL */}
      <ReportProfileModal
        open={reportModalOpen}
        onClose={() => setReportModalOpen(false)}
        onSubmit={(reason) => handleReportProfile(reason, profil, setReportLoading, setReportModalOpen, isOwnProfil)}
        loading={reportLoading}
        photos={getAllProfilePhotosFormatted()}
      />
      <EntrepriseVerificationModal
        isOpen={isVerificationModalOpen}
        onClose={() => { setIsVerificationModalOpen(false); setVerificationInitialType(undefined); }}
        typeDeProfil={profil?.companyInfo?.type_de_profil}
        profilEntreprise={profil?.companyInfo}
        initialType={verificationInitialType}
      />

      {/* Système de génération IA pour la bio */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="biography"
          prompt={`
            Informations de mon profil:
            - Nom: ${profil?.lastName || ''}
            - Prénom: ${profil?.firstName || ''}
            - Métier/Profession: ${profil?.companyInfo?.type_de_profil || ''}
            - Localisation: ${profil?.ville || ''}, ${profil?.code_postal || ''}
            ${stripHtml(tempBio || editorHtml || '').length > 0 ?
              `- Action: Améliorer le texte existant
            - Texte à améliorer: ${stripHtml(tempBio || editorHtml || '')}
            - Instructions: Conserve le sens et les informations du texte original, mais améliore le style, la clarté et le professionnalisme. Ne réinvente pas complètement le contenu. Format HTML simple : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, utilise plutôt le HTML approprié.`
              :
              `- Action: Générer une nouvelle bio professionnelle
            - Instructions: Crée une bio professionnelle et engageante qui présente mes compétences et mon expérience.`
            }
          `}
          originalPrompt={stripHtml(tempBio || editorHtml || '').length > 0 ? stripHtml(tempBio || editorHtml || '') : undefined}
          onComplete={(content) => {
            setIsAiConfirmModalOpen(false);
            if (content) {
              setTempBio(content);
              if (editorRef.current) {
                const editor = editorRef.current.getEditor();
                if (editor) {
                  editor.commands.setContent(content);
                }
              }

              // Mettre à jour le compteur de caractères
              handleEditorChange(content);

              // Activer l'édition si ce n'est pas déjà le cas
              if (!isEditingBio) {
                setIsEditingBio(true);
              }
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}
      {isDeleteBannerModalOpen && (
        <ModalPortal isOpen={isDeleteBannerModalOpen} onBackdropClick={() => setIsDeleteBannerModalOpen(false)} closeOnBackdropClick={true}>
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden flex flex-col max-h-[90vh] overflow-y-auto">
            <div className="bg-gradient-to-r from-red-100 to-white border-b border-red-200 px-8 py-6">
              <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                <span className="h-8 w-1 bg-gradient-to-b from-red-600 to-red-400 rounded-full"></span>
                Confirmation
              </h2>
              <p className="mt-2 text-gray-600 text-sm">
                Êtes-vous sûr de vouloir supprimer votre bannière de profil ?
              </p>
            </div>
            <div className="p-8">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5-4h4a2 2 0 012 2v2H7V5a2 2 0 012-2zm0 0V3m0 2v2" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Cette action est irréversible
                </h3>
                <p className="text-gray-600">
                  La bannière sera définitivement supprimée de votre profil.
                </p>
              </div>
            </div>
            <div className="bg-gray-50 border-t border-gray-100 px-8 py-4 flex justify-end gap-4">
              <button
                onClick={() => setIsDeleteBannerModalOpen(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium"
              >
                Annuler
              </button>
              <button
                onClick={confirmDeleteBanner}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
              >
                Supprimer
              </button>
            </div>
          </div>
        </ModalPortal>
      )}
      {isOwnProfil && showBannerPositionControls && (
        <ModalPortal isOpen={showBannerPositionControls} onBackdropClick={() => setShowBannerPositionControls(false)} closeOnBackdropClick={true}>
          <div className="bg-white rounded-lg p-6 shadow-xl flex flex-col items-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Choisir la position de l'image</h3>
            <div className="flex space-x-4 mb-6">
              <button
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${bannerPosition === 'top' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                onClick={() => {
                  handleBannerPositionSelect('top');
                  // setShowBannerPositionControls(false);
                }}
              >
                Haut
              </button>
              <button
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${bannerPosition === 'center' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                onClick={() => {
                  handleBannerPositionSelect('center');
                  // setShowBannerPositionControls(false);
                }}
              >
                Centre
              </button>
              <button
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${bannerPosition === 'bottom' ? 'bg-[#FF6B2C] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                onClick={() => {
                  handleBannerPositionSelect('bottom');
                  // setShowBannerPositionControls(false);
                }}
              >
                Bas
              </button>
            </div>
            <div className="w-full flex flex-col items-center mb-4">
              <label htmlFor="banner-position-offset" className="block text-sm font-medium text-gray-700 mb-2">Ajuster la position verticale :</label>
              <input
                id="banner-position-offset"
                type="range"
                min={-200}
                max={200}
                value={bannerPositionOffset}
                onChange={e => setBannerPositionOffset(Number(e.target.value))}
                className="w-64 accent-[#FF6B2C]"
              />
              <span className="mt-1 text-xs text-gray-500">{bannerPositionOffset > 0 ? `+${bannerPositionOffset}` : bannerPositionOffset} px</span>
            </div>
            <button
              className="mt-2 px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors font-medium"
              onClick={() => {
                saveBannerPosition(bannerPosition, bannerPositionOffset);
                setShowBannerPositionControls(false);
              }}
            >
              Enregistrer
            </button>
            <button
              className="mt-4 text-gray-600 hover:text-gray-800 text-sm font-medium"
              onClick={() => setShowBannerPositionControls(false)}
            >
              Fermer
            </button>
          </div>
        </ModalPortal>
      )}

      {/* Modal de partage */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        profil={profil}
        onNativeShare={handleNativeShare}
      />
    </>
  );
};

export default DashboardProfil;