import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import logger from '../../../utils/logger';
import { getCommonHeaders, getMultipartHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';
import { useImageCompression } from '../../../utils/imageCompressor';
import { notify } from '../../../components/Notification';

export interface Mission {
  id: string;
  user_id: string;
  category_id: string;
  subcategory_id: string;
  titre: string;
  description: string;
  budget: number;
  budget_defini: boolean;
  date_mission: string;
  time_slots: Array<{
    date: string;
    start: string;
    end: string;
  }>;
  adresse: string;
  code_postal: string;
  ville: string;
  pays: string;
  intervention_zone?: {
    center: [number, number];
    radius: number;
    adresse?: string;
  };
  photos?: Array<{
    id: string;
    photo_url: string;
    order_index: number;
  }>;
  statut: 'en_attente' | 'en_cours' | 'terminee' | 'annulee' | 'en_moderation';
  created_at: string;
  updated_at: string;
}

export const missionApi = {
  // Créer une nouvelle mission
  createMission: async (missionData: Omit<Mission, 'id' | 'user_id' | 'statut' | 'created_at' | 'updated_at'>) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/missions`, missionData, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      // Gestion spécifique du profil masqué
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        // On lève une erreur générique pour stopper le process côté page
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la création de la mission:', error);
      throw error;
    }
  },

  // Nouvelle méthode pour uploader les photos
  uploadMissionPhotos: async (missionId: string, files: File[]) => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const { compressGalleryPhoto } = useImageCompression();
      
      // Log des types MIME avant validation
      files.forEach((file, index) => {
        logger.info(`Type MIME du fichier ${index + 1} avant validation:`, {
          fileName: file.name,
          type: file.type,
          size: file.size
        });
      });

      // Vérifier les types de fichiers avant la compression
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      const validFiles = files.filter(file => {
        if (!allowedTypes.includes(file.type)) {
          logger.error(`Type MIME non autorisé pour ${file.name}:`, { type: file.type });
          notify(`Le fichier ${file.name} n'est pas un format d'image valide. Formats acceptés : JPG, PNG, WEBP`, 'error');
          return false;
        }
        if (file.size > 5 * 1024 * 1024) { // 5MB
          logger.error(`Fichier trop volumineux: ${file.name}`, { size: file.size });
          notify(`Le fichier ${file.name} dépasse la taille maximale de 5MB`, 'error');
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) {
        throw new Error('Aucun fichier valide à uploader');
      }

      // Log des fichiers valides avant compression
      logger.info('Fichiers valides avant compression:', validFiles.map(f => ({
        name: f.name,
        type: f.type,
        size: f.size
      })));

      const compressedFiles = await Promise.all(
        validFiles.map(async (file) => {
          const compressed = await compressGalleryPhoto(file);
          logger.info(`Fichier compressé: ${file.name}`, {
            originalType: file.type,
            compressedType: compressed.type,
            originalSize: file.size,
            compressedSize: compressed.size
          });
          return compressed;
        })
      );

      const formData = new FormData();
      compressedFiles.forEach((file, index) => {
        formData.append('photo', file);
        formData.append('order_index', index.toString());
        logger.info(`Ajout au FormData: fichier ${index + 1}`, {
          name: file.name,
          type: file.type,
          size: file.size
        });
      });

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/photos`,
        formData,
        {
          headers,
          withCredentials: true,
        }
      );

      if (response.data.success) {
        notify(response.data.message, 'success');
        return response.data.photos || [response.data.photo];
      } else {
        throw new Error(response.data.message || 'Erreur lors de l\'upload des photos');
      }
    } catch (error) {
      logger.error('Erreur lors de l\'upload des photos:', error);
      throw error;
    }
  },

  // Supprimer une photo
  deleteMissionPhoto: async (missionId: string, photoId: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.delete(`${API_CONFIG.baseURL}/api/missions/${missionId}/photos/${photoId}`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la suppression de la photo:', error);
      throw error;
    }
  },

  // Réorganiser les photos
  reorderMissionPhotos: async (missionId: string, photoIds: string[]) => {
    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/missions/${missionId}/photos/reorder`,
        { photoIds },
        {
          headers,
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la réorganisation des photos:', error);
      throw error;
    }
  },

  // Récupérer les missions d'un utilisateur
  getMissions: async (): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      throw error;
    }
  },

  // Récupérer les missions disponibles pour un jobbeur
  getAvailableMissions: async (): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/missions/available`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions disponibles:', error);
      throw error;
    }
  },

  // Postuler à une mission
  applyToMission: async (missionId: string, message: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/missions/apply`, {
        missionId,
        message
      }, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 403 && error?.response?.data?.profileHidden) {
        notify(error.response.data.message, error.response.data.toastType || 'error');
        throw new Error('profile_hidden');
      }
      logger.error('Erreur lors de la candidature à la mission:', error);
      throw error;
    }
  },

  // Modérer une mission (admin)
  moderateMission: async (missionId: string, statut: string, reason?: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.put(`${API_CONFIG.baseURL}/api/missions/${missionId}/moderate`, {
        statut,
        reason
      }, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la modération de la mission:', error);
      throw error;
    }
  },

  // Supprimer une mission (admin)
  adminDeleteMission: async (missionId: string, reason?: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.delete(`${API_CONFIG.baseURL}/api/missions/${missionId}/admin-delete`, {
        data: { reason },
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la suppression de la mission:', error);
      throw error;
    }
  }
}; 