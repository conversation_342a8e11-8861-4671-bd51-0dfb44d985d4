import { redis } from '../config/redis';
import logger from '../utils/logger';

export class FailedAttemptsService {
  private static readonly PREFIX = 'failed_attempts:';
  private static readonly EXPIRY = 24 * 60 * 60; // 24 heures en secondes

  /**
   * Incrémente le compteur de tentatives échouées pour une IP
   */
  static async incrementAttempts(ip: string): Promise<number> {
    const key = this.getKey(ip);
    const multi = redis.multi();
    
    // Incrémente le compteur et met à jour le timestamp
    multi.hincrby(key, 'count', 1);
    multi.hset(key, 'lastAttempt', Date.now().toString());
    multi.expire(key, this.EXPIRY);

    try {
      await multi.exec();
      const count = await redis.hget(key, 'count');
      return parseInt(count || '0', 10);
    } catch (error) {
      logger.error('Erreur lors de l\'incrémentation des tentatives:', error);
      return 0;
    }
  }

  /**
   * Récupère les informations sur les tentatives échouées
   */
  static async getAttempts(ip: string): Promise<{ count: number; lastAttempt: number } | null> {
    const key = this.getKey(ip);
    try {
      const [count, lastAttempt] = await redis.hmget(key, 'count', 'lastAttempt');
      
      if (!count || !lastAttempt) {
        return null;
      }

      return {
        count: parseInt(count, 10),
        lastAttempt: parseInt(lastAttempt, 10)
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération des tentatives:', error);
      return null;
    }
  }

  /**
   * Réinitialise le compteur pour une IP
   */
  static async resetAttempts(ip: string): Promise<void> {
    const key = this.getKey(ip);
    try {
      await redis.del(key);
    } catch (error) {
      logger.error('Erreur lors de la réinitialisation des tentatives:', error);
    }
  }

  /**
   * Génère la clé Redis pour une IP
   */
  private static getKey(ip: string): string {
    return `${this.PREFIX}${ip}`;
  }
}

export default FailedAttemptsService;
