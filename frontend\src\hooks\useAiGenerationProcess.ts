import { useState, useRef, useCallback, useEffect } from 'react';
import { useAiGeneration, GenerationType } from './useAiGeneration';
import logger from '../utils/logger';

export type AiGenerationStatus = 'idle' | 'confirming' | 'generating' | 'completed' | 'error';

export interface AiGenerationProcessOptions {
  onSuccess?: (content: string) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  autoStart?: boolean;
}

/**
 * Hook pour gérer le processus complet de génération IA
 * Gère les états de confirmation, génération, et complétion
 */
export const useAiGenerationProcess = (options: AiGenerationProcessOptions = {}) => {
  // États
  const [status, setStatus] = useState<AiGenerationStatus>('idle');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showLoadingModal, setShowLoadingModal] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [generatedContent, setGeneratedContent] = useState<string | null>(null);

  // Références
  const promptRef = useRef<string | null>(null);
  const typeRef = useRef<GenerationType | null>(null);
  const originalPromptRef = useRef<string | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const generatedContentRef = useRef<string>('');

  // Étapes de génération pour l'animation
  const GENERATION_STEPS = [
    { threshold: 10, label: "Initialisation de l'IA..." },
    { threshold: 25, label: "Analyse de vos données..." },
    { threshold: 40, label: "Génération des idées..." },
    { threshold: 60, label: "Rédaction du contenu..." },
    { threshold: 80, label: "Optimisation du texte..." },
    { threshold: 95, label: "Finalisation..." },
    { threshold: 100, label: "Terminé !" }
  ];

  // Hook de génération IA
  const {
    generateContent,
    generating,
    credits,
    isRateLimited
  } = useAiGeneration({
    onGenerationComplete: (content) => {
      logger.info("useAiGenerationProcess: API a répondu avec contenu:", content ? content.substring(0, 50) + "..." : "aucun contenu");

      // Stocker le contenu généré
      if (content) {
        // Stocker le contenu dans une variable d'état
        setGeneratedContent(content);

        // Stocker également dans une ref pour éviter les problèmes de fermeture (closure)
        generatedContentRef.current = content;

        logger.info("Contenu stocké dans state et ref:", content.substring(0, 50) + "...");
      } else {
        logger.info("Contenu reçu est vide ou null");
      }

      // Quand l'API répond, accélérer l'animation vers 100%
      // Utiliser setTimeout pour s'assurer que le state est mis à jour
      setTimeout(() => {
        accelerateToCompletion();
      }, 0);
    },
    onGenerationError: (error) => {
      stopProgressSimulation();
      setStatus('error');
      options.onError?.(error);
    }
  });

  // Démarrer la simulation de progression
  const startProgressSimulation = useCallback((maxDuration = 15000) => {
    stopProgressSimulation(); // Arrêter toute simulation en cours

    setProgress(0);
    setCurrentStep(0);

    // Simuler une progression jusqu'à 90% sur la durée maximale
    const interval = 100; // Mise à jour toutes les 100ms
    const steps = maxDuration / interval;
    const incrementPerStep = 90 / steps;

    progressIntervalRef.current = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + incrementPerStep, 90);

        // Mettre à jour l'étape actuelle
        const newStep = GENERATION_STEPS.findIndex(step =>
          step.threshold > newProgress
        ) - 1;

        if (newStep >= 0 && newStep !== currentStep) {
          setCurrentStep(newStep);
        }

        return newProgress;
      });
    }, interval);

    // Sécurité : forcer à 100% après le délai maximum + 2 secondes
    progressTimeoutRef.current = setTimeout(() => {
      accelerateToCompletion();
    }, maxDuration + 2000);
  }, [currentStep]);

  // Accélérer la progression vers 100%
  const accelerateToCompletion = useCallback(() => {
    // Capturer le contenu généré actuel depuis la référence
    // pour éviter les problèmes de fermeture (closure)
    const currentGeneratedContent = generatedContentRef.current || generatedContent;
    logger.info("accelerateToCompletion: contenu capturé:", currentGeneratedContent ? currentGeneratedContent.substring(0, 50) + "..." : "aucun contenu");

    stopProgressSimulation();

    // Accélérer vers 100% en 800ms
    const startValue = progress;
    const targetValue = 100;
    const duration = 800;
    const startTime = Date.now();

    const animateToCompletion = () => {
      const elapsed = Date.now() - startTime;
      const ratio = Math.min(elapsed / duration, 1);
      const newProgress = startValue + (targetValue - startValue) * ratio;

      setProgress(newProgress);

      // Mettre à jour l'étape actuelle
      const newStep = GENERATION_STEPS.findIndex(step =>
        step.threshold > newProgress
      ) - 1;

      if (newStep >= 0 && newStep !== currentStep) {
        setCurrentStep(newStep);
      }

      if (ratio < 1) {
        requestAnimationFrame(animateToCompletion);
      } else {
        setProgress(100);
        setCurrentStep(GENERATION_STEPS.length - 1);
        setStatus('completed');

        // Fermer la modale après un court délai
        setTimeout(() => {
          // Utiliser la référence pour obtenir le contenu final
          // Fallback sur la variable locale puis sur une chaîne vide
          const finalContent = generatedContentRef.current || currentGeneratedContent || '';
          logger.info("Animation terminée, appel de onSuccess avec:", finalContent ? finalContent.substring(0, 50) + "..." : "aucun contenu");

          setShowLoadingModal(false);

          if (options.onSuccess) {
            logger.info("Appel du callback onSuccess avec le contenu final");
            options.onSuccess(finalContent);
          } else {
            logger.info("Aucun callback onSuccess fourni");
          }
        }, 500);
      }
    };

    requestAnimationFrame(animateToCompletion);
  }, [progress, currentStep, generatedContent, options]);

  // Arrêter la simulation de progression
  const stopProgressSimulation = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }

    if (progressTimeoutRef.current) {
      clearTimeout(progressTimeoutRef.current);
      progressTimeoutRef.current = null;
    }
  }, []);

  // Démarrer le processus de génération
  const startGeneration = useCallback((type: GenerationType, prompt: string, originalPrompt?: string) => {
    typeRef.current = type;
    promptRef.current = prompt;
    originalPromptRef.current = originalPrompt || null;

    if (options.autoStart) {
      // Démarrer directement la génération sans confirmation
      handleConfirm();
    } else {
      // Afficher la modale de confirmation
      setStatus('confirming');
      setShowConfirmModal(true);
    }
  }, [options.autoStart]);

  // Gérer la confirmation
  const handleConfirm = useCallback(async () => {
    if (!typeRef.current || !promptRef.current) {
      return;
    }

    setShowConfirmModal(false);
    setStatus('generating');
    setShowLoadingModal(true);

    // Démarrer la simulation de progression
    startProgressSimulation();

    // Générer le contenu
    await generateContent(
      typeRef.current,
      promptRef.current,
      originalPromptRef.current || undefined
    );
  }, [generateContent, startProgressSimulation]);

  // Gérer l'annulation
  const handleCancel = useCallback(() => {
    setShowConfirmModal(false);
    setShowLoadingModal(false);
    setStatus('idle');
    stopProgressSimulation();
    options.onCancel?.();
  }, [options.onCancel, stopProgressSimulation]);

  // Nettoyer les intervalles et timeouts à la destruction
  useEffect(() => {
    return () => {
      stopProgressSimulation();
    };
  }, [stopProgressSimulation]);

  // Obtenir l'étape actuelle
  const getCurrentStep = useCallback(() => {
    return GENERATION_STEPS[currentStep] || GENERATION_STEPS[0];
  }, [currentStep]);

  return {
    status,
    progress,
    currentStep: getCurrentStep(),
    showConfirmModal,
    showLoadingModal,
    generatedContent,
    credits,
    isRateLimited,
    generating,
    startGeneration,
    handleConfirm,
    handleCancel
  };
};
