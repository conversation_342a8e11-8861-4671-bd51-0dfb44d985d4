const Hero = () => {
  return (
    <div className="bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA]">
      {/* Hero Content */}
      <div className="relative pt-20 sm:pt-20 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 h-full">
        <div className="max-w-7xl mx-auto h-full">
          <div className="grid grid-cols-1 desktop:grid-cols-3 gap-8 h-full">
            {/* Left Column */}
            <div className="space-y-8 pb-12 desktop:bg-transparent h-full">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Arrondissez Vos Fins de Mois avec{' '}
                  <span className="text-[#ff7a35]">JobPartiel.fr</span>
                  {' '}<span className="text-2xl md:text-3xl font-semibold inline-block">
                    <span className="bg-gradient-to-r from-[#ff7a35] to-[#ff965e] text-transparent bg-clip-text">Propulsé par l'IA</span>
                  </span>
                </h1>
              </div>

              <div>
                <p className="text-gray-600 text-lg">
                  Gagnez un revenu complémentaire en proposant vos services de
                  <span className="font-semibold"> bricolage</span>,
                  <span className="font-semibold"> jardinage</span>,
                  <span className="font-semibold"> plomberie</span> et
                  <span className="font-semibold text-[#ff7a35]"> +200 autres activités</span>.
                  <span className="block mt-1 font-bold">Gagnez <span className="text-[#ff7a35]">300€ à 1500€</span> chaque mois !</span>
                  <span className="block mt-2 text-[#ff7a35] font-medium">Notre IA vous aide à optimiser vos missions et maximiser vos revenus</span>
                </p>
              </div>

              <div className="flex items-center gap-4 bg-[#ff7a35] hover:bg-[#e55a20] text-white px-6 py-3 rounded-lg transition-colors cursor-pointer w-fit" onClick={() => {
                const discoverSection = document.getElementById('discover');
                if (discoverSection) {
                  const offset = 50; // Espace supplémentaire en pixels
                  const elementPosition = discoverSection.getBoundingClientRect().top + window.pageYOffset;
                  window.scrollTo({
                    top: elementPosition - offset,
                    behavior: 'smooth'
                  });
                }
              }}>
                <picture>
                  <source type="image/webp" srcSet="/images/handyman-7-res-11-1.webp" />
                  <source type="image/png" srcSet="/images/handyman-7-res-11-1.png" />
                  <img
                    src="/images/handyman-7-res-11-1.png"
                    alt="En savoir plus"
                    className="w-8 h-8"
                    loading="lazy"
                  />
                </picture>
                <span className="font-medium whitespace-nowrap">EN SAVOIR PLUS …</span>
              </div>

              <div className="grid mobile:grid-cols-2 tablet:grid-cols-2 desktop:grid-cols-2 gap-4 mobile:gap-6 tablet:gap-8 mt-8">
                {[
                  {
                    src: '/images/jobbeur-image-menuisier.jpg',
                    webp: '/images/jobbeur-image-menuisier.webp',
                    alt: 'Menuisier professionnel',
                    title: 'Bricolage',
                    category: '+150€/jour'
                  },
                  {
                    src: '/images/jobbeur-image-charpentier.jpg',
                    webp: '/images/jobbeur-image-charpentier.webp',
                    alt: 'Charpentier au travail',
                    title: 'Rénovation',
                    category: 'Forte demande'
                  },
                  {
                    src: '/images/jobbeur-image-montage-cuisine.jpg',
                    webp: '/images/jobbeur-image-montage-cuisine.webp',
                    alt: 'Montage de cuisine',
                    title: 'Plomberie',
                    category: 'Urgent'
                  },
                  {
                    src: '/images/jobbeur-image-montage-de-meuble.jpg',
                    webp: '/images/jobbeur-image-montage-de-meuble.webp',
                    alt: 'Montage de meuble',
                    title: 'Assemblage',
                    category: 'Très recherché'
                  }
                ].map((img, index) => (
                  <div key={index} className="group relative overflow-hidden rounded-xl shadow-2xl hover:shadow-lg transition-shadow duration-300">
                    {/* Image avec effet de zoom */}
                    <div className="aspect-square overflow-hidden">
                      <picture>
                        <source type="image/webp" srcSet={img.webp} />
                        <source type="image/jpeg" srcSet={img.src} />
                        <img
                          src={img.src}
                          alt={img.alt}
                          className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"
                          loading="lazy"
                        />
                      </picture>
                    </div>

                    {/* Overlay avec dégradé */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-100 transition-opacity duration-300" />

                    {/* Texte */}
                    <div className="absolute inset-0 flex flex-col justify-end p-4 transition-transform duration-300">
                      <div className="text-white font-semibold text-xl opacity-100 transition-opacity duration-300 delay-100">
                        {img.title}
                      </div>
                      <p className="text-white/80 text-lg opacity-100 transition-opacity duration-300 delay-200">
                        {img.category}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Middle Column - Main Image */}
            <div className="relative hidden desktop:block h-full">
              <div className="absolute -inset-x-24 -top-0 bottom-0">
                <div className="relative h-full overflow-hidden">
                  <picture>
                    <source type="image/webp" srcSet="/images/image-accueil-bricoleur.webp" />
                    <source type="image/png" srcSet="/images/image-accueil-bricoleur.png" />
                    <img
                      src="/images/image-accueil-bricoleur.png"
                      alt="Bricoleur professionnel"
                      className="w-full h-full object-cover object-top"
                      loading="lazy"
                    />
                  </picture>
                  <div className="absolute inset-0 bg-gradient-to-b from-white/0 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>

            {/* Right Column - Call To Action */}
            <div className="relative pb-12 desktop:bg-transparent h-full">
              <div className="absolute -top-10 mobile:-top-14 tablet:-top-14 right-4">
                <img
                  src="/images/icone-signalisation-formulaire.png"
                  alt="Inscription"
                  className="w-32 h-auto animate-vibrate"
                />
              </div>

              <div className="bg-white p-8 rounded-lg shadow-xl desktop:mt-16 relative overflow-visible">
                {/* Background pattern */}
                <div className="absolute top-0 left-0 right-0 h-3 bg-gradient-to-r from-[#ff7a35] via-[#ff965e] to-[#ff7a35]"></div>

                <h2 className="text-2xl font-bold mb-2 mt-2">Commencez dès maintenant</h2>
                <p className="text-gray-600 mb-2">
                  Inscrivez-vous et gagnez <span className="font-bold text-[#ff7a35]">300€ à 1500€</span> chaque mois
                </p>

                {/* Badge IA */}
                <div className="bg-[#E6F7FF] border border-[#B3E0FF] rounded-md p-2 mb-4 mt-3 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-[#0091FF]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
                    <path d="M12 10.5C13.1046 10.5 14 9.60457 14 8.5C14 7.39543 13.1046 6.5 12 6.5C10.8954 6.5 10 7.39543 10 8.5C10 9.60457 10.8954 10.5 12 10.5Z" fill="currentColor"/>
                    <path d="M12 11.5C9.33 11.5 7 13.43 7 16H17C17 13.43 14.67 11.5 12 11.5Z" fill="currentColor"/>
                  </svg>
                  <span className="text-xs text-[#0070CC] font-medium">Technologie IA incluse dans tous les abonnements !</span>
                </div>

                {/* Garanties avec mention de gratuité */}
                <div className="flex flex-col space-y-2 mt-6 text-sm text-gray-500">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Inscription <b>100% GRATUITE</b></span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Premiers revenus dès cette semaine</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Paiement sécurisé, aucunes commissions & annulation sans frais</span>
                  </div>
                </div>

                {/* Nouvelle offre premium */}
                <div className="bg-[#FFF8F3] border border-[#FFE4BA] rounded-md p-3 mb-4 mt-6">
                  <p className="text-sm font-bold text-[#ff7a35] flex items-center">
                    <span className="inline-block w-4 h-4 bg-[#ff7a35] text-white rounded-full text-xs flex items-center justify-center mr-2">%</span>
                    <span>PREMIUM À MOITIÉ PRIX, À VIE !</span>
                  </p>
                  <p className="text-xs text-gray-600">Limité aux <span className="font-bold">500</span> premiers inscrits !</p>
                </div>

                {/* Inscription en 1 clic */}
                <div className="space-y-4">
                  <button
                    className="w-full bg-[#ff7a35] text-white py-4 px-6 rounded-md hover:bg-[#e55a20] transition-colors font-bold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all relative overflow-hidden group"
                    onClick={() => {
                      // Redirection vers la page d'inscription
                      window.location.href = '/inscription';
                    }}
                  >
                    <span className="relative z-10">DÉMARRER MAINTENANT ➔</span>
                    <span className="absolute inset-0 bg-gradient-to-r from-[#ff7a35] to-[#e55a20] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  </button>

                  <div className="flex items-center justify-center space-x-4 py-2">
                    <hr className="w-full border-gray-300" />
                    <span className="text-gray-500 whitespace-nowrap text-sm">ou inscrivez-vous avec</span>
                    <hr className="w-full border-gray-300" />
                  </div>

                  {/* Google uniquement - avec redirection vers l'API d'authentification */}
                  <button
                    className="flex items-center justify-center gap-2 bg-white text-gray-700 border border-gray-300 py-3 px-4 rounded-md hover:bg-gray-100 transition-colors w-full"
                    onClick={() => {
                      // Même logique que dans GoogleSignInButton.tsx
                      const timestamp = Date.now();
                      window.location.href = `${import.meta.env.VITE_API_URL}/api/auth/google?t=${timestamp}`;
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24">
                      <path fill="#EA4335" d="M5.26620003,9.76452941 C6.19878754,6.93863203 8.85444915,4.90909091 12,4.90909091 C13.6909091,4.90909091 15.2181818,5.50909091 16.4181818,6.49090909 L19.9090909,3 C17.7818182,1.14545455 15.0545455,0 12,0 C7.27006974,0 3.1977497,2.69829785 1.23999023,6.65002441 L5.26620003,9.76452941 Z" />
                      <path fill="#34A853" d="M16.0407269,18.0125889 C14.9509167,18.7163016 13.5660892,19.0909091 12,19.0909091 C8.86648613,19.0909091 6.21911939,17.076871 5.27698177,14.2678769 L1.23746264,17.3349879 C3.19279051,21.2936293 7.26500293,24 12,24 C14.9328362,24 17.7353462,22.9573905 19.834192,20.9995801 L16.0407269,18.0125889 Z" />
                      <path fill="#4A90E2" d="M19.834192,20.9995801 C22.0291676,18.9520994 23.4545455,15.903663 23.4545455,12 C23.4545455,11.2909091 23.3454545,10.5272727 23.1818182,9.81818182 L12,9.81818182 L12,14.4545455 L18.4363636,14.4545455 C18.1187732,16.013626 17.2662994,17.2212117 16.0407269,18.0125889 L19.834192,20.9995801 Z" />
                      <path fill="#FBBC05" d="M5.27698177,14.2678769 C5.03832634,13.556323 4.90909091,12.7937589 4.90909091,12 C4.90909091,11.2182781 5.03443647,10.4668121 5.26620003,9.76452941 L1.23999023,6.65002441 C0.43658717,8.26043162 0,10.0753848 0,12 C0,13.9195484 0.444780743,15.7301709 1.23746264,17.3349879 L5.27698177,14.2678769 Z" />
                    </svg>
                    <span>Google en 1 clic</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;