import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const imagesDir = path.join(__dirname, '../public/images');

function analyzeImages() {
  fs.readdir(imagesDir, (err, files) => {
    if (err) {
      console.warn('Erreur lors de la lecture du dossier images:', err);
      return;
    }

    const imageExtensions = ['.jpg', '.jpeg', '.png'];
    const webpFiles = new Set(
      files
        .filter(file => path.extname(file) === '.webp')
        .map(file => path.parse(file).name)
    );

    const stats = {
      totalImages: 0,
      convertedToWebP: 0,
      unconvertedImages: []
    };

    files.forEach(file => {
      const fileExt = path.extname(file);
      const fileName = path.parse(file).name;

      // Compter et analyser les images
      if (imageExtensions.includes(fileExt)) {
        stats.totalImages++;
        
        if (webpFiles.has(fileName)) {
          stats.convertedToWebP++;
        } else {
          stats.unconvertedImages.push(file);
        }
      }
    });

    // Rapport détaillé
    console.info('\n--- Rapport de conversion WebP ---');
    console.info(`Images totales : ${stats.totalImages}`);
    console.info(`Images converties : ${stats.convertedToWebP}`);
    console.info(`Images non converties : ${stats.unconvertedImages.length}`);
    
    if (stats.unconvertedImages.length > 0) {
      console.warn('\nImages non converties :');
      stats.unconvertedImages.forEach(img => console.warn(`- ${img}`));
    }
  });
}

analyzeImages();
