import { Router } from 'express';
import * as contentModerationController from '../controllers/contentModerationController';
import * as moderationStatsController from '../controllers/moderationStatsController';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateImageForModeration } from '../middleware/imageValidation';
import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Rate limiter pour les requêtes de modération
const moderationLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // 20 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes de modération. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour modérer un contenu (texte et/ou image)
router.post('/analyze', moderationLimiter, validateImageForModeration, async (req: Request, res: Response, next: NextFunction) => {
  try {
    await contentModerationController.moderateContent(req, res);
  } catch (error) {
    next(error);
  }
});

// Route pour récupérer l'historique des modérations (utilisateur connecté ou admin)
router.get('/history', moderationLimiter, async (req: Request, res: Response, next: NextFunction) => {
  try {
    await contentModerationController.getModerationHistory(req, res);
  } catch (error) {
    next(error);
  }
});

// Routes pour les statistiques de modération (admin/modo uniquement)
router.get('/stats',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  moderationLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await moderationStatsController.getModerationStats(req, res);
    } catch (error) {
      next(error);
    }
  }
);

// Statistiques de modération pour un utilisateur spécifique
router.get('/stats/user/:userId',
  authMiddleware.authenticateToken,
  moderationLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await moderationStatsController.getUserModerationStats(req, res);
    } catch (error) {
      next(error);
    }
  }
);

// Suppression d'une image temporaire de modération (admin/modo uniquement)
router.post('/delete-image',
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await contentModerationController.deleteModerationImage(req, res);
    } catch (error) {
      next(error);
    }
  }
);

// Création d'un signalement différé pour une image (après enregistrement avec UUID permanent)
router.post('/deferred-report',
  authMiddleware.authenticateToken,
  moderationLimiter,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await contentModerationController.createDeferredImageReport(req, res);
    } catch (error) {
      next(error);
    }
  }
);

// Suppression d'une image temporaire de modération
router.post('/delete-image',
  authMiddleware.authenticateToken,
  authMiddleware.checkRole(['jobpadm', 'jobmodo']),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      await contentModerationController.deleteModerationImage(req, res);
    } catch (error) {
      next(error);
    }
  }
);

export default router;
