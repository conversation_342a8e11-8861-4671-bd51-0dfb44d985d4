import React, { useState, useMemo } from 'react';
import { 
  Box, 
  Typography, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  TableSortLabel, 
  Paper, 
  IconButton, 
  Avatar,
  Chip,
  Tooltip,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { format, isBefore, isAfter, parseISO, isToday, isWithinInterval } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Edit, Clock, Calendar, ChevronRight, MoveHorizontal } from 'lucide-react';
import DOMPurify from 'dompurify';
import { Mission } from '../../types/planning';
import MissionFilterBar, { MissionFilters } from './MissionFilterBar';

type OrderDirection = 'asc' | 'desc';

interface MissionListViewProps {
  missions: Mission[];
  onEditMission: (mission: Mission) => void;
  onClose: () => void;
  onFilterChange?: (filters: MissionFilters) => void;
  filters?: MissionFilters;
}

const MissionListView: React.FC<MissionListViewProps> = ({ 
  missions, 
  onEditMission, 
  onClose,
  onFilterChange,
  filters: externalFilters
}) => {
  const [orderBy, setOrderBy] = useState<keyof Mission>('date');
  const [order, setOrder] = useState<OrderDirection>('asc');
  const [filters, setFilters] = useState<MissionFilters>(externalFilters || {
    searchTerm: '',
    status: [],
    jobber: [],
    minPrice: null,
    maxPrice: null,
    dateRange: {
      start: null,
      end: null
    },
    selectedMonth: undefined,
    selectedYear: undefined
  });

  const handleRequestSort = (property: keyof Mission) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const createSortHandler = (property: keyof Mission) => () => {
    handleRequestSort(property);
  };

  // Liste des jobbeurs pour le filtre (extraite des missions)
  const jobbers = useMemo(() => {
    const jobberMap = new Map<string, { name: string; count: number }>();
    
    missions.forEach(mission => {
      if (mission.proposition?.jobbeur_id) {
        const id = mission.proposition.jobbeur_id;
        const name = mission.user?.prenom && mission.user.nom 
          ? `${mission.user.prenom} ${mission.user.nom}` 
          : mission.user?.prenom || 'Jobbeur inconnu';
        
        if (jobberMap.has(id)) {
          const current = jobberMap.get(id)!;
          jobberMap.set(id, { ...current, count: current.count + 1 });
        } else {
          jobberMap.set(id, { name, count: 1 });
        }
      }
    });
    
    return Array.from(jobberMap.entries()).map(([id, { name, count }]) => ({
      id,
      name,
      count
    }));
  }, [missions]);

  // Prix maximum possible pour le filtre de prix
  const maxPossiblePrice = useMemo(() => {
    const prices = missions
      .map(m => m.proposition?.montant_propose || 0)
      .concat(missions.map(m => m.proposition?.montant_paiement || 0))
      .filter(price => price > 0);
    
    return prices.length > 0 ? Math.max(...prices) + 50 : 500;
  }, [missions]);

  // Fonction de tri
  const sortedMissions = useMemo(() => {
    const comparator = (a: Mission, b: Mission, orderBy: keyof Mission) => {
      // Tri par date
      if (orderBy === 'date') {
        if (order === 'asc') {
          return a.date.localeCompare(b.date) || a.start_time.localeCompare(b.start_time);
        } else {
          return b.date.localeCompare(a.date) || b.start_time.localeCompare(a.start_time);
        }
      }
      // Tri par titre
      else if (orderBy === 'title') {
        return order === 'asc' 
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      }
      // Tri par heure de début
      else if (orderBy === 'start_time') {
        if (a.date === b.date) {
          return order === 'asc'
            ? a.start_time.localeCompare(b.start_time)
            : b.start_time.localeCompare(a.start_time);
        } else {
          return order === 'asc'
            ? a.date.localeCompare(b.date) 
            : b.date.localeCompare(a.date);
        }
      }
      return 0;
    };

    return [...missions].sort((a, b) => comparator(a, b, orderBy));
  }, [missions, order, orderBy]);

  // Fonction de filtrage complète avec tous les filtres
  const filteredMissions = useMemo(() => {
    // Filtrer d'abord par mois/année si sélectionnés
    let filteredByDate = sortedMissions;
    
    if (filters.selectedMonth !== undefined && filters.selectedYear !== undefined) {
      const month = filters.selectedMonth;
      const year = filters.selectedYear;
      
      // Construire les dates de début et fin du mois pour le filtrage
      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0); // Dernier jour du mois
      
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');
      
      filteredByDate = sortedMissions.filter(mission => {
        const missionDate = mission.date;
        return missionDate >= startDateStr && missionDate <= endDateStr;
      });
    }

    return filteredByDate.filter(mission => {
      // 1. Filtre par texte (recherche générale)
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const matchesSearch = 
          mission.title.toLowerCase().includes(searchTerm) ||
          (mission.description || '').toLowerCase().includes(searchTerm) ||
          mission.date.includes(searchTerm) ||
          mission.start_time.includes(searchTerm) ||
          (mission.user?.prenom || '').toLowerCase().includes(searchTerm) ||
          (mission.user?.nom || '').toLowerCase().includes(searchTerm);
        
        if (!matchesSearch) return false;
      }

      // 2. Filtre par statut
      if (filters.status.length > 0) {
        const hasProposition = !!mission.proposition;
        const paymentStatus = mission.proposition?.payment_status;
        const propositionStatus = mission.proposition?.statut;

        // Gérer le cas spécial "Sans proposition"
        if (filters.status.includes('no_proposition') && hasProposition) {
          if (!filters.status.some(s => s === propositionStatus || s === paymentStatus)) {
            return false;
          }
        } 
        // Gérer tous les autres cas de statut
        else if (!filters.status.includes('no_proposition') && !hasProposition) {
          return false;
        }
        else if (hasProposition && !filters.status.some(s => s === propositionStatus || s === paymentStatus)) {
          return false;
        }
      }

      // 3. Filtre par jobbeur
      if (filters.jobber.length > 0) {
        if (!mission.proposition?.jobbeur_id || !filters.jobber.includes(mission.proposition.jobbeur_id)) {
          return false;
        }
      }

      // 4. Filtre par prix
      if (filters.minPrice !== null && mission.proposition?.montant_propose) {
        if (mission.proposition.montant_propose < filters.minPrice) {
          return false;
        }
      }
      if (filters.maxPrice !== null && mission.proposition?.montant_propose) {
        if (mission.proposition.montant_propose > filters.maxPrice) {
          return false;
        }
      }

      // 5. Filtre par plage de dates (si défini et différent du filtre mois/année)
      if (filters.dateRange.start || filters.dateRange.end) {
        const missionDate = parseISO(mission.date);
        
        if (filters.dateRange.start && filters.dateRange.end) {
          // Filtrer dans l'intervalle
          const startDate = parseISO(filters.dateRange.start);
          const endDate = parseISO(filters.dateRange.end);
          if (!isWithinInterval(missionDate, { start: startDate, end: endDate })) {
            return false;
          }
        } else if (filters.dateRange.start) {
          // Filtrer après une date
          const startDate = parseISO(filters.dateRange.start);
          if (isBefore(missionDate, startDate)) {
            return false;
          }
        } else if (filters.dateRange.end) {
          // Filtrer avant une date
          const endDate = parseISO(filters.dateRange.end);
          if (isAfter(missionDate, endDate)) {
            return false;
          }
        }
      }

      return true;
    });
  }, [sortedMissions, filters]);

  // Calcul des statistiques
  const stats = useMemo(() => {
    const now = new Date();
    const today = format(now, 'yyyy-MM-dd');
    
    return {
      total: missions.length,
      past: missions.filter(m => 
        m.date < today || (m.date === today && m.end_time < format(now, 'HH:mm'))
      ).length,
      today: missions.filter(m => m.date === today).length,
      upcoming: missions.filter(m => m.date > today).length
    };
  }, [missions]);

  // Fonction pour déterminer la couleur de la rangée selon la date
  const getRowColor = (mission: Mission) => {
    const now = new Date();
    const missionDate = parseISO(mission.date);
    
    if (isToday(missionDate)) {
      return 'rgba(255, 107, 44, 0.05)';
    } else if (isBefore(missionDate, now) && !isToday(missionDate)) {
      return 'rgba(0, 0, 0, 0.02)';
    }
    return 'transparent';
  };

  // Format de l'heure
  const formatTime = (time: string) => {
    return time.substring(0, 5); // Garde seulement HH:MM
  };

  // Gestionnaire pour les changements de filtres
  const handleFilterChange = (newFilters: MissionFilters) => {
    setFilters(newFilters);
    if (onFilterChange) {
      onFilterChange(newFilters);
    }
  };

  // Formatage d'une valeur pour l'affichage dans le tableau
  const formatStatus = (mission: Mission) => {
    if (!mission.proposition) {
      return (
        <Chip 
          label="Sans proposition" 
          size="small"
          sx={{ 
            backgroundColor: 'rgba(158, 158, 158, 0.1)', 
            color: '#9E9E9E',
            fontWeight: 600,
            fontSize: '0.7rem'
          }}
        />
      );
    }

    // Statut de paiement prioritaire si présent
    if (mission.proposition.payment_status) {
      const paymentStatus = mission.proposition.payment_status;
      let color = '';
      let label = '';

      switch (paymentStatus) {
        case 'pending':
          color = '#FF9800';
          label = 'Paiement en attente';
          break;
        case 'completed':
          color = '#8BC34A';
          label = 'Payée';
          break;
        case 'manual':
          color = '#795548';
          label = 'Paiement manuel';
          break;
      }

      return (
        <Chip 
          label={label}
          size="small"
          sx={{ 
            backgroundColor: `${color}20`,
            color: color,
            fontWeight: 600,
            fontSize: '0.7rem'
          }}
        />
      );
    }

    // Sinon on affiche le statut de la proposition
    const propositionStatus = mission.proposition.statut;
    let color = '';
    let label = '';

    switch (propositionStatus) {
      case 'en_attente':
        color = '#FFC107';
        label = 'En attente';
        break;
      case 'acceptée':
        color = '#4CAF50';
        label = 'Acceptée';
        break;
      case 'refusée':
        color = '#F44336';
        label = 'Refusée';
        break;
      case 'contre_offre':
        color = '#9C27B0';
        label = 'Contre-offre';
        break;
      case 'contre_offre_jobbeur':
        color = '#2196F3';
        label = 'Contre-offre jobbeur';
        break;
    }

    return (
      <Chip 
        label={label}
        size="small"
        sx={{ 
          backgroundColor: `${color}20`,
          color: color,
          fontWeight: 600,
          fontSize: '0.7rem'
        }}
      />
    );
  };

  // Modifier la fonction pour déterminer le style de la bordure selon le type de mission
  const getMissionStyles = (mission: Mission) => {
    // Le style de base pour l'arrière-plan de la ligne reste le même
    return {
      backgroundColor: getRowColor(mission),
      '&:last-child td, &:last-child th': { border: 0 }
    };
  };

  // Fonction pour déterminer la classe CSS de la bordure de la cellule
  const getCellBorderClass = (mission: Mission) => {
    // Si la mission a une proposition acceptée
    if (mission.proposition?.statut === 'acceptée') {
      return 'mission-accepted-border';
    }
    
    // Si c'est une mission manuelle
    if (!mission.mission || !mission.mission.id || mission.mission.id === '') {
      return 'mission-manual-border';
    }
    
    // Si c'est une mission JobPartiel standard
    return 'mission-jobpartiel-border';
  };

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Styles CSS intégrés pour les bordures */}
      <style>{`
        .mission-accepted-border {
          border-left: 5px solid #4CAF50 !important;
        }
        .mission-manual-border {
          border-left: 5px solid #FF6B2C !important;
        }
        .mission-jobpartiel-border {
          border-left: 5px solid #1976d2 !important;
        }
      `}</style>
      
      {/* En-tête */}
      <Box 
        sx={{ 
          p: 2, 
          display: 'flex', 
          flexDirection: {xs: 'column', sm: 'row'}, 
          justifyContent: 'space-between',
          alignItems: {xs: 'flex-start', sm: 'center'},
          gap: 2,
          borderBottom: '1px solid #eee'
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 700, color: '#333' }}>
          Liste complète des missions ({missions.length})
        </Typography>

        <Box 
          sx={{
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          <Chip 
            icon={<Calendar size={14} />} 
            label={`Aujourd'hui: ${stats.today}`} 
            size="small"
            sx={{ 
              backgroundColor: 'rgba(255, 107, 44, 0.08)',
              color: '#FF6B2C',
              fontWeight: 600,
              '& .MuiChip-icon': { color: '#FF6B2C' }
            }}
          />
          <Chip 
            label={`À venir: ${stats.upcoming}`} 
            size="small"
            sx={{ 
              backgroundColor: 'rgba(25, 118, 210, 0.08)',
              color: '#1976d2',
              fontWeight: 600
            }}
          />
          <Chip 
            label={`Passées: ${stats.past}`} 
            size="small"
            sx={{ 
              backgroundColor: 'rgba(0, 0, 0, 0.06)',
              color: '#666',
              fontWeight: 600
            }}
          />
        </Box>
      </Box>

      {/* Barre de filtres avancés (remplace la recherche simple) */}
      <MissionFilterBar
        onFilterChange={handleFilterChange}
        totalCount={missions.length}
        filteredCount={filteredMissions.length}
        jobbers={jobbers}
        maxPossiblePrice={maxPossiblePrice}
      />

      {/* Tableau */}
      <TableContainer 
        component={Paper} 
        sx={{ 
          mt: 2, 
          flex: 1, 
          boxShadow: 'none',
          '.MuiTableRow-root:hover': {
            backgroundColor: 'rgba(255, 107, 44, 0.03)'
          },
          '@media (max-width: 1200px)': {
            overflowX: 'auto',
            '& .MuiTable-root': {
              minWidth: '1000px',
            },
          },
          '&::-webkit-scrollbar': {
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#f1f1f1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#FF6B2C',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            backgroundColor: '#FF7A35',
          },
        }}
      >
        {useMediaQuery('(max-width: 1200px)') && (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            padding: '10px 16px',
            backgroundColor: 'rgba(255, 107, 44, 0.04)',
            borderBottom: '1px solid #E2E8F0'
          }}>
            <MoveHorizontal size={16} style={{ color: '#FF6B2C', marginRight: '8px' }} />
            <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
              Faites défiler horizontalement pour voir tout le tableau
            </Typography>
          </Box>
        )}
        
        <Table size="small" stickyHeader aria-label="Liste des missions">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 600, color: '#666' }}>
                <TableSortLabel
                  active={orderBy === 'date'}
                  direction={orderBy === 'date' ? order : 'asc'}
                  onClick={createSortHandler('date')}
                >
                  Date
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666' }}>
                <TableSortLabel
                  active={orderBy === 'start_time'}
                  direction={orderBy === 'start_time' ? order : 'asc'}
                  onClick={createSortHandler('start_time')}
                >
                  Horaire
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666' }}>
                <TableSortLabel
                  active={orderBy === 'title'}
                  direction={orderBy === 'title' ? order : 'asc'}
                  onClick={createSortHandler('title')}
                >
                  Mission
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666' }}>
                Client
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666' }}>
                Statut
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666', textAlign: 'right' }}>
                Prix
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: '#666', textAlign: 'center' }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredMissions.length > 0 ? (
              filteredMissions.map((mission) => (
                <TableRow 
                  key={mission.id}
                  hover
                  sx={getMissionStyles(mission)}
                >
                  <TableCell 
                    component="th" 
                    scope="row"
                    className={getCellBorderClass(mission)}
                    sx={{ paddingLeft: '12px', position: 'relative' }}
                  >
                    {format(parseISO(mission.date), 'dd/MM/yyyy', { locale: fr })}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Clock size={12} style={{ marginRight: 4, color: '#999' }} />
                      {formatTime(mission.start_time)} - {formatTime(mission.end_time)}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography 
                      sx={{ 
                        fontWeight: 600, 
                        color: '#444',
                        maxWidth: 250,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {mission.title}
                    </Typography>
                    {mission.description && (
                      <Typography 
                        variant="caption"
                        sx={{ 
                          color: '#666',
                          display: 'block',
                          maxWidth: 250,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                        dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(mission.description) }}
                      />
                    )}
                  </TableCell>
                  <TableCell>
                    {mission.user ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar
                          src={mission.user.photo_url}
                          alt={`${mission.user.prenom || ''}`}
                          sx={{ width: 24, height: 24, fontSize: '0.7rem' }}
                        />
                        <Typography sx={{ color: '#555', fontSize: '0.875rem' }}>
                          {mission.user.prenom} {mission.user.nom?.charAt(0).toUpperCase() || ''}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="caption" sx={{ color: '#999' }}>
                        Non assigné
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {formatStatus(mission)}
                  </TableCell>
                  <TableCell align="right">
                    {(mission.montant_propose && mission.montant_propose > 0) || mission.proposition?.montant_propose ? (
                      <Typography 
                        sx={{ 
                          fontWeight: 700, 
                          color: '#FF6B2C',
                          fontSize: '0.875rem'
                        }}
                      >
                        {(mission.montant_propose && mission.montant_propose > 0) 
                          ? mission.montant_propose 
                          : mission.proposition?.montant_propose} {mission.payment_method === 'direct_only' || mission.proposition?.payment_method === 'direct_only' ? '€' : 'J'}
                      </Typography>
                    ) : (
                      <Typography variant="caption" sx={{ color: '#999' }}>
                        -
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="Éditer la mission" arrow>
                      <IconButton 
                        size="small"
                        onClick={() => onEditMission(mission)}
                        sx={{ 
                          color: '#666',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.08)',
                            color: '#FF6B2C',
                          }
                        }}
                      >
                        <Edit size={16} />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                  <Typography sx={{ color: '#666', fontWeight: 500 }}>
                    Aucune mission ne correspond aux critères de recherche
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#999', mt: 1 }}>
                    Essayez de modifier vos filtres ou de réinitialiser la recherche
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Légende des codes couleur - En bas du tableau */}
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: 1.5, sm: 2 },
          p: 2,
          mt: 1,
          mb: 1,
          backgroundColor: 'rgba(255, 107, 44, 0.04)',
          borderRadius: '8px',
          width: '100%'
        }}
      >
        <Typography variant="body2" sx={{ fontWeight: 600, color: '#555', mr: 'auto', mb: { xs: 1, sm: 0 } }}>
          Légende des couleurs :
        </Typography>
        
        <Box 
          sx={{ 
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 2 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box 
              sx={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: 'rgba(255, 107, 44, 0.08)', 
                borderLeft: '3px solid #FF6B2C',
                borderRadius: '3px'
              }} 
            />
            <Typography variant="body2" sx={{ color: '#555' }}>
              Mission manuelle
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box 
              sx={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                borderLeft: '3px solid #1976d2',
                borderRadius: '3px'
              }} 
            />
            <Typography variant="body2" sx={{ color: '#555' }}>
              Mission JobPartiel
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box 
              sx={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: 'rgba(76, 175, 80, 0.08)', 
                borderLeft: '3px solid #4CAF50',
                borderRadius: '3px'
              }} 
            />
            <Typography variant="body2" sx={{ color: '#555' }}>
              Mission avec offre acceptée
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Footer avec bouton de retour (sur mobile) */}
      <Box 
        sx={{ 
          display: { xs: 'flex', sm: 'none' },
          justifyContent: 'center',
          p: 2,
          borderTop: '1px solid #eee'
        }}
      >
        <Box
          onClick={onClose}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            color: '#FF6B2C',
            cursor: 'pointer',
            fontSize: '0.875rem',
            fontWeight: 600
          }}
        >
          <ChevronRight size={16} style={{ transform: 'rotate(180deg)' }} />
          Retour au calendrier
        </Box>
      </Box>
    </Box>
  );
};

export default MissionListView; 