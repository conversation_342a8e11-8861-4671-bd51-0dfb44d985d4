import { useState, useEffect } from 'react';

export function useDynamicImport<T>(
  importFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [module, setModule] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    setLoading(true);
    importFn()
      .then((mod) => {
        setModule(mod);
        setLoading(false);
      })
      .catch((err) => {
        setError(err);
        setLoading(false);
      });
  }, dependencies);

  return { module, loading, error };
}

// Hook spécifique pour les icônes Lucide
export function useDynamicIcon(iconName: string) {
  return useDynamicImport(
    () => import('lucide-react').then(module => module[iconName]),
    [iconName]
  );
}

// Hook pour React Hot Toast
export function useToast() {
  return useDynamicImport(() => import('react-hot-toast'));
}
