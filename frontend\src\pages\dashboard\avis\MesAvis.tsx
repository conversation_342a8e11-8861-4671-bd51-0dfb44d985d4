import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Star, Filter, Search, ChevronDown, Clock, MessageCircle, SquarePen, Trash2, X, Briefcase, ChevronRight, Plus, HelpCircle, User, ChevronLeft } from 'lucide-react';
import { Button, TextField, Menu, MenuItem, Chip, Tabs, Tab as MuiTab, styled, Typography, Badge, Avatar, Box, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { useAuth } from '@/contexts/AuthContext';
import { notify } from '@/components/Notification';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders } from '@/utils/headers';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import DOMPurify from 'dompurify';
import UserProfileModal from '@/components/UserProfileModal';
import ReviewReplyModal from '@/components/ReviewReplyModal';
import ReviewModalPopupDepotAvis from '@/components/ReviewModalPopupDepotAvis';
import ModalPortal from '@/components/ModalPortal';
import ReviewModalSelecteurMission from '@/components/ReviewModalSelecteurMission';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '@/pages/dashboard/services/types';
import MissionRecapModal from '@/components/planning/MissionRecapModal';
import { Mission } from '@/types/planning';
import { logger } from '@/utils/logger';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import { fetchCsrfToken } from '@/services/csrf';

// Constante pour l'avatar par défaut
const DEFAULT_AVATAR = `${API_CONFIG.baseURL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

interface Review {
  id: string;
  note: number;
  commentaire: string;
  qualites: string[];
  defauts: string[];
  created_at: string;
  reponse?: string;
  mission_id: string;
  is_modified?: boolean;
  modified_at?: string;
  target_user_id?: string;
  author_id?: string;
  photos?: string[];
  mission: {
    id: string;
    titre: string;
    description: string;
    statut: string;
    category_id?: string;
    subcategory_id?: string;
  };
  author?: {
    id: string;
    profil?: Array<{
      prenom?: string;
      nom?: string;
      photo_url?: string;
    }>;
  };
  target_user?: {
    id: string;
    profil?: Array<{
      prenom?: string;
      nom?: string;
      photo_url?: string;
    }>;
  };
}

interface ReviewsStats {
  rating: number;
  total_reviews: number;
  completion_rate: number;
  receivedAnswered: number;
  sentModified: number;
}

const PageTitle = styled(Typography)(() => ({
    fontSize: '1.5rem',
    fontWeight: 700,
    color: '#2D3748',
    marginBottom: '16px',
    position: 'relative',
    '&::after':
    {
      content: '""',
      position: 'absolute',
      bottom: '-8px',
      left: 0,
      width: '60px',
      height: '3px',
      background: 'linear-gradient(to right, #FF6B2C, #FFE4BA)',
      borderRadius: '2px',
    },
}));

const StyledTab = styled(MuiTab)({
  textTransform: 'none',
  minWidth: 0,
  padding: '1rem 1.5rem',
  fontWeight: 600,
  color: '#64748B',
  transition: 'all 0.2s ease',
  position: 'relative',
  overflow: 'hidden',
  '&.Mui-selected': {
    color: '#FF7A35',
    fontWeight: 700,
    '&::after': {
      transform: 'scaleX(1)',
      transformOrigin: 'left center',
    }
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    left: '1rem',
    right: '1rem',
    bottom: 0,
    height: '3px',
    backgroundColor: '#FF965E',
    borderRadius: '3px 3px 0 0',
    transform: 'scaleX(0)',
    transformOrigin: 'center center',
    transition: 'transform 0.3s ease',
  },
  '&:hover': {
    backgroundColor: '#FFF8F3',
    '&::after': {
      transform: 'scaleX(0.5)',
    }
  },
});

const StyledTabs = styled(Tabs)({
  borderBottom: '1px solid #E2E8F0',
  '& .MuiTabs-indicator': {
    backgroundColor: 'transparent',
  },
});

const SearchTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#FFFFFF',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    border: '1px solid #E2E8F0',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
    '&:hover fieldset': {
      borderColor: '#FFE4BA',
      boxShadow: '0 0 0 4px rgba(255, 228, 186, 0.15)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF7A35',
      boxShadow: '0 0 0 4px rgba(255, 122, 53, 0.1)',
    },
    '& fieldset': {
      borderColor: 'transparent',
    },
  },
  '& .MuiInputBase-input': {
    padding: '12px 16px',
  },
});

const FilterButton = styled(Button)({
  borderColor: '#E2E8F0',
  color: '#64748B',
  backgroundColor: '#FFFFFF',
  textTransform: 'none',
  borderRadius: '12px',
  padding: '10px 16px',
  fontWeight: 600,
  boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.2s ease',
  '&:hover': {
    borderColor: '#FFE4BA',
    backgroundColor: '#FFF8F3',
    boxShadow: '0 4px 10px rgba(255, 107, 44, 0.1)',
    transform: 'translateY(-1px)',
  },
  '& .MuiButton-startIcon, & .MuiButton-endIcon': {
    color: '#94A3B8',
  },
});

const MesAvis = () => {
  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [isUserProfileModalOpen, setIsUserProfileModalOpen] = useState(false);
  const [selectedUserData, setSelectedUserData] = useState<any>(null);
  const [receivedReviews, setReceivedReviews] = useState<Review[]>([]);
  const [sentReviews, setSentReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedReviewToEdit, setSelectedReviewToEdit] = useState<Review | null>(null);
  const [isEditReviewModalOpen, setIsEditReviewModalOpen] = useState(false);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [updatedReviewId, setUpdatedReviewId] = useState<string | null>(null);
  const [currentReceivedPage, setCurrentReceivedPage] = useState(1);
  const [currentSentPage, setCurrentSentPage] = useState(1);
  const [hasMoreReceived, setHasMoreReceived] = useState(true);
  const [hasMoreSent, setHasMoreSent] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<Review | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isMissionSelectModalOpen, setIsMissionSelectModalOpen] = useState(false);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isMissionModalOpen, setIsMissionModalOpen] = useState(false);
  const reviewsPerPage = 10;
  const [stats, setStats] = useState<ReviewsStats>({
    rating: 0,
    total_reviews: 0,
    completion_rate: 0,
    receivedAnswered: 0,
    sentModified: 0
  });
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [reportingReview, setReportingReview] = useState<Review | null>(null);
  const [reportReason, setReportReason] = useState('');
  const [reportLoading, setReportLoading] = useState(false);

  // États pour la modal de visualisation des photos
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [currentPhotos, setCurrentPhotos] = useState<string[]>([]);

  const searchParams = new URLSearchParams(window.location.search);
  const reviewIdFromUrl = searchParams.get('review');
  const reviewRef = useRef<HTMLDivElement>(null);

  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastReviewElementRef = useCallback((node: HTMLDivElement | null) => {
    if (isLoading || isFetchingMore) return;

    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting) {
        if (selectedTab === 0 && hasMoreReceived) {
          loadMoreReviews('received');
        } else if (selectedTab === 1 && hasMoreSent) {
          loadMoreReviews('sent');
        }
      }
    });

    if (node) observerRef.current.observe(node);
  }, [isLoading, isFetchingMore, selectedTab, hasMoreReceived, hasMoreSent]);

  const fetchReviews = async (type: 'received' | 'sent', page: number = 1, append: boolean = false) => {
    if (!user) return;

    if (page === 1) {
      setIsLoading(true);
    } else {
      setIsFetchingMore(true);
    }

    try {
      const endpoint = type === 'received'
        ? `${API_CONFIG.baseURL}/api/reviews/user/${user.id}/reviews`
        : `${API_CONFIG.baseURL}/api/reviews/user/${user.id}/sent`;

      const response = await axios.get(endpoint, {
        headers: await getCommonHeaders(),
        withCredentials: true,
        params: {
          page,
          limit: reviewsPerPage
        }
      });

      if (response.data.success) {
        const newReviews = response.data.reviews.map((review: any) => ({
          ...review,
          target_user: type === 'sent' ? {
            ...review.target_user,
            id: review.target_user_id,
            profil: review.target_user?.profil || []
          } : review.target_user
        }));
        const hasMore = response.data.pagination?.has_more ?? false;

        if (type === 'received') {
          if (append) {
            setReceivedReviews(prev => [...prev, ...newReviews]);
          } else {
            setReceivedReviews(newReviews);
          }
          setHasMoreReceived(hasMore);
          setCurrentReceivedPage(page);

          // Mise à jour des statistiques pour les avis reçus
          const allReviews = append ? [...receivedReviews, ...newReviews] : newReviews;
          const totalReviews = allReviews.length;
          const answeredReviews = allReviews.filter((review: Review) => review.reponse).length;
          const completionRate = totalReviews > 0 ? Math.round((answeredReviews / totalReviews) * 100) : 0;
          const averageRating = totalReviews > 0
            ? allReviews.reduce((acc: number, review: Review) => acc + review.note, 0) / totalReviews
            : 0;

          setStats(prevStats => ({
            ...prevStats,
            rating: Number(averageRating || 0),
            total_reviews: totalReviews,
            completion_rate: completionRate,
            receivedAnswered: answeredReviews
          }));
        } else {
          if (append) {
            setSentReviews(prev => [...prev, ...newReviews]);
          } else {
            setSentReviews(newReviews);
          }
          setHasMoreSent(hasMore);
          setCurrentSentPage(page);

          // Mise à jour des statistiques pour les avis envoyés
          if (response.data.stats) {
            setStats(prevStats => ({
              ...prevStats,
              sentModified: response.data.stats.modified_reviews || 0
            }));
          }
        }
      }
    } catch (error) {
      logger.info(`Erreur lors de la récupération des avis ${type}:`, error);
      notify(`Erreur lors de la récupération des avis`, 'error');
      // Réinitialiser les états en cas d'erreur
      if (type === 'received') {
        setHasMoreReceived(false);
      } else {
        setHasMoreSent(false);
      }
    } finally {
      setIsLoading(false);
      setIsFetchingMore(false);
    }
  };

  const loadMoreReviews = async (type: 'received' | 'sent') => {
    const currentPage = type === 'received' ? currentReceivedPage : currentSentPage;
    await fetchReviews(type, currentPage + 1, true);
  };

  useEffect(() => {
    if (user) {
      void fetchReviews('received', 1, false);
      void fetchReviews('sent', 1, false);
    }
  }, [user]);

  useEffect(() => {
    if (selectedTab === 0) {
      setCurrentReceivedPage(1);
      void fetchReviews('received', 1, false);
    } else {
      setCurrentSentPage(1);
      void fetchReviews('sent', 1, false);
    }
  }, [selectedTab]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (selectedTab === 0) {
        setCurrentReceivedPage(1);
        void fetchReviews('received', 1, false);
      } else {
        setCurrentSentPage(1);
        void fetchReviews('sent', 1, false);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, selectedFilter]);

  useEffect(() => {
    if (reviewIdFromUrl) {
      const findAndHighlightReview = () => {
        const allReviews = [...receivedReviews, ...sentReviews];
        const targetReview = allReviews.find(r => r.id === reviewIdFromUrl);

        if (targetReview) {
          setSelectedTab(targetReview.author_id === user?.id ? 1 : 0);
          setUpdatedReviewId(reviewIdFromUrl);
          setTimeout(() => {
            const reviewElement = document.getElementById(`review-${reviewIdFromUrl}`);
            if (reviewElement) {
              reviewElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }, 500);
        }
      };

      findAndHighlightReview();
    }
  }, [reviewIdFromUrl, receivedReviews, sentReviews, user]);

  const filterOptions = [
    { value: 'all', label: 'Tous les avis' },
    { value: 'recent', label: 'Plus récents' },
    { value: 'highest', label: 'Notes les plus hautes' },
    { value: 'lowest', label: 'Notes les plus basses' },
    { value: 'unanswered', label: 'Sans réponse' },
  ];

  const filterReviews = (reviews: Review[]) => {
    let filtered: Review[] = [...reviews];

    if (searchTerm) {
      filtered = filtered.filter((review: Review) =>
        review.commentaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.author?.profil?.[0]?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.qualites.some((q: string) => q.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    switch (selectedFilter) {
      case 'recent':
        filtered.sort((a: Review, b: Review) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'highest':
        filtered.sort((a: Review, b: Review) => b.note - a.note);
        break;
      case 'lowest':
        filtered.sort((a: Review, b: Review) => a.note - b.note);
        break;
      case 'unanswered':
        filtered = filtered.filter((review: Review) => !review.reponse);
        break;
      default:
        break;
    }

    return filtered;
  };

  const handleReviewClick = (review: Review) => {
    if (selectedTab === 0) {
      setSelectedReview(review);
      setIsReplyModalOpen(true);
    }
  };

  const handleUserProfileClick = async (userId: string) => {
    if (!userId) return;

    try {
      const slugResponse = await axios.get(
        `${API_CONFIG.baseURL}/api/users/get-slug/${userId}`,
        {
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil utilisateur', 'error');
        return;
      }

      const response = await axios.get(
        `${API_CONFIG.baseURL}/api/users/profil/${slugResponse.data.slug}`,
        {
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (response.data) {
        setSelectedUserData(response.data);
        setIsUserProfileModalOpen(true);
      }
    } catch (error) {
      notify('Erreur lors du chargement du profil utilisateur', 'error');
    }
  };

  const handleEditReview = (review: Review, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedReviewToEdit(review);
    setIsEditReviewModalOpen(true);
  };

  const handleReviewEdited = async () => {
    setIsActionLoading(true);
    try {
      // Rafraîchir les avis depuis le serveur
      await fetchReviews('sent', currentSentPage, false);
      setIsEditReviewModalOpen(false);
      setSelectedReviewToEdit(null);

      // Déclencher l'animation de mise à jour
      if (selectedReviewToEdit) {
        setUpdatedReviewId(selectedReviewToEdit.id);
        setTimeout(() => setUpdatedReviewId(null), 2000);
      }
    } catch (error) {
      logger.info('Erreur lors de la mise à jour de l\'avis:', error);
      notify('Erreur lors de la mise à jour de l\'avis', 'error');
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleReplySubmitted = async () => {
    setIsActionLoading(true);
    await fetchReviews('received', 1, false);
    setIsActionLoading(false);
    // Déclencher l'animation de mise à jour
    if (selectedReview) {
      setUpdatedReviewId(selectedReview.id);
      setTimeout(() => setUpdatedReviewId(null), 2000);
    }
  };

  const handleDeleteReview = async (review: Review, e: React.MouseEvent) => {
    e.stopPropagation();
    setReviewToDelete(review);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDeleteReview = async () => {
    if (!reviewToDelete) return;

    setIsActionLoading(true);
    try {
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/reviews/${reviewToDelete.id}`,
        {
          headers: await getCommonHeaders(),
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Avis supprimé avec succès', 'success');
        await fetchReviews(selectedTab === 0 ? 'received' : 'sent', 1, false);
      }
    } catch (error) {
      notify('Erreur lors de la suppression de l\'avis', 'error');
    } finally {
      setIsActionLoading(false);
      setIsDeleteConfirmOpen(false);
      setReviewToDelete(null);
    }
  };

  const handleReviewSubmitted = async () => {
    setIsMissionSelectModalOpen(false);
    await fetchReviews('sent', 1, false);
  };

  // Fonction pour récupérer les détails d'une mission et ouvrir le modal
  const handleOpenMissionModal = (review: Review, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!review.mission || !review.mission.id) {
      notify("Impossible d'afficher les détails de cette mission", "error");
      return;
    }

    // Créer un objet Mission avec les données disponibles dans l'objet review
    // et des valeurs par défaut pour les champs obligatoires
    const missionData: Mission = {
      id: review.mission.id,
      title: review.mission.titre,
      description: review.mission.description,
      // Utiliser la date actuelle formatée en YYYY-MM-DD comme valeur par défaut
      date: new Date().toISOString().split('T')[0],
      // Valeurs par défaut pour les heures de début et de fin
      start_time: "08:00",
      end_time: "18:00",
      // Copier les informations de base de la mission
      mission: {
        id: review.mission.id,
        title: review.mission.titre,
        description: review.mission.description
      },
      // Informations sur l'utilisateur - pour les avis envoyés, nous utilisons target_user (propriétaire)
      // au lieu de author_id (qui est l'utilisateur connecté qui a laissé l'avis)
      user: selectedTab === 1
        ? {
            id: review.target_user?.id || '',
            prenom: review.target_user?.profil?.[0]?.prenom || 'Utilisateur',
            nom: review.target_user?.profil?.[0]?.nom || ''
          }
        : {
            id: review.author_id || '',
            prenom: review.author?.profil?.[0]?.prenom || 'Utilisateur',
            nom: review.author?.profil?.[0]?.nom || ''
          }
    };

    setSelectedMission(missionData);
    setIsMissionModalOpen(true);
  };

  const handleOpenReportModal = (review: Review) => {
    setReportingReview(review);
    setReportReason('');
    setReportModalOpen(true);
  };

  const handleSendReport = async () => {
    if (!reportingReview || !reportReason.trim()) return;
    setReportLoading(true);
    try {
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.post(
        `${API_CONFIG.baseURL}/api/reported-content`,
        {
          content_type: 'review',
          content_id: reportingReview.id,
          reason: reportReason
        },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      notify('Signalement envoyé, merci pour votre vigilance.', 'success');
      setReportModalOpen(false);
      setReportingReview(null);
      setReportReason('');
    } catch (error: any) {
      notify(error?.response?.data?.message || 'Erreur lors du signalement', 'error');
    } finally {
      setReportLoading(false);
    }
  };

  // Fonctions pour la modal de visualisation des photos
  const openPhotoModal = (photos: string[], startIndex: number = 0) => {
    setCurrentPhotos(photos);
    setCurrentPhotoIndex(startIndex);
    setPhotoModalOpen(true);
  };

  const closePhotoModal = () => {
    setPhotoModalOpen(false);
    setCurrentPhotos([]);
    setCurrentPhotoIndex(0);
  };

  const goToPreviousPhoto = () => {
    setCurrentPhotoIndex((prev) =>
      prev === 0 ? currentPhotos.length - 1 : prev - 1
    );
  };

  const goToNextPhoto = () => {
    setCurrentPhotoIndex((prev) =>
      prev === currentPhotos.length - 1 ? 0 : prev + 1
    );
  };

  // Gestion des touches clavier pour la navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!photoModalOpen) return;

    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        goToPreviousPhoto();
        break;
      case 'ArrowRight':
        e.preventDefault();
        goToNextPhoto();
        break;
      case 'Escape':
        e.preventDefault();
        closePhotoModal();
        break;
    }
  };

  // Ajouter l'écouteur d'événements clavier
  useEffect(() => {
    if (photoModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [photoModalOpen, currentPhotos.length]);

  return (
    <motion.div
      className="space-y-6 px-4 sm:px-6 md:px-0"
    >
      <div className="flex items-center justify-between mb-6">
        <PageTitle variant="h1">
          Mes Avis
        </PageTitle>
        <Button
          variant="contained"
          startIcon={<Plus size={18} />}
          onClick={() => setIsMissionSelectModalOpen(true)}
          sx={{
            backgroundColor: '#FF7A35',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '12px',
            textTransform: 'none',
            fontSize: '0.95rem',
            fontWeight: 600,
            boxShadow: '0 6px 16px rgba(255, 107, 44, 0.12)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: '#FF965E',
              transform: 'translateY(-2px)',
              boxShadow: '0 8px 20px rgba(255, 107, 44, 0.15)',
            }
          }}
        >
          Déposer un avis
        </Button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Note Moyenne</h3>
              <Tooltip
                title="Moyenne des notes reçues sur l'ensemble de vos missions terminées"
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={5000}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur la note moyenne"
                >
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <Star className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500" />
          </div>
          <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-yellow-500">
            {stats.rating ? stats.rating.toFixed(1) : '0.0'} / 5
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1">Sur l'ensemble des avis</p>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Total des Avis</h3>
              <Tooltip
                title="Nombre total d'avis reçus sur vos missions"
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={5000}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le total des avis"
                >
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <MessageCircle className="h-5 w-5 sm:h-6 sm:w-6 text-[#FF965E]" />
          </div>
          <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-[#FF965E]">
            {stats.total_reviews || 0}
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1">Avis reçus</p>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Taux de Réponse</h3>
              <Tooltip
                title="Pourcentage d'avis auxquels vous avez répondu"
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={5000}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le taux de réponse"
                >
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <SquarePen className="h-5 w-5 sm:h-6 sm:w-6 text-green-500" />
          </div>
          <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-green-500">
            {stats.total_reviews > 0 ? Math.round((stats.receivedAnswered / stats.total_reviews) * 100) : 0}%
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1">Avis avec réponse</p>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-700">Missions Évaluées</h3>
              <Tooltip
                title="Nombre de missions ayant reçu au moins un avis"
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={5000}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur les missions évaluées"
                >
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <Briefcase className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" />
          </div>
          <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-blue-500">
            {stats.total_reviews || 0}
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1">Sur l'ensemble des missions</p>
        </div>
      </div>

      <div className="mb-8">
        <div className="relative">
          <div className="flex justify-between items-center mb-6">
            <div className="text-sm text-gray-500 flex items-center bg-white py-1.5 px-3 rounded-full border border-gray-100 shadow-sm">
              <Clock size={14} className="mr-1 text-[#FF965E]" />
              Dernière mise à jour: {format(new Date(), 'dd/MM/yyyy HH:mm:ss', { locale: fr })}
            </div>
          </div>
          <motion.div
            className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-white to-[#FFF8F3]">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full">
                  <div className="w-full sm:w-64">
                    <SearchTextField
                      fullWidth
                      size="small"
                      placeholder="Rechercher..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      InputProps={{
                        startAdornment: <Search className="text-[#FF965E] mr-2" size={18} />,
                      }}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <FilterButton
                      variant="outlined"
                      onClick={(e) => setFilterAnchorEl(e.currentTarget)}
                      startIcon={<Filter size={16} className="text-[#FF965E]" />}
                      endIcon={<ChevronDown size={12} />}
                    >
                      <span className="hidden xs:inline">Filtrer</span>
                      <span className="xs:hidden">Filtre</span>
                    </FilterButton>
                    {selectedFilter !== 'all' && (
                      <Chip
                        label={filterOptions.find(o => o.value === selectedFilter)?.label}
                        onDelete={() => setSelectedFilter('all')}
                        color="primary"
                        sx={{
                          backgroundColor: '#FFF8F3',
                          color: '#FF7A35',
                          borderColor: '#FFE4BA',
                          border: '1px solid',
                          fontWeight: 600,
                          boxShadow: '0 2px 6px rgba(255, 107, 44, 0.1)',
                          '& .MuiChip-deleteIcon': {
                            color: '#FF7A35'
                          }
                        }}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            <Menu
              anchorEl={filterAnchorEl}
              open={Boolean(filterAnchorEl)}
              onClose={() => setFilterAnchorEl(null)}
              PaperProps={{
                elevation: 2,
                sx: {
                  mt: 1,
                  borderRadius: '12px',
                  border: '1px solid #E2E8F0',
                  boxShadow: '0 8px 20px rgba(0, 0, 0, 0.08)',
                  overflow: 'hidden'
                }
              }}
            >
              {filterOptions.map((option) => (
                <MenuItem
                  key={option.value}
                  onClick={() => {
                    setSelectedFilter(option.value);
                    setFilterAnchorEl(null);
                  }}
                  selected={selectedFilter === option.value}
                  sx={{
                    py: 1.5,
                    px: 2,
                    '&.Mui-selected': {
                      backgroundColor: '#FFF8F3',
                      '&:hover': {
                        backgroundColor: '#FFE4BA',
                      }
                    },
                    '&:hover': {
                      backgroundColor: '#F8FAFC',
                    }
                  }}
                >
                  {option.label}
                </MenuItem>
              ))}
            </Menu>

            {/* Section des qualités les plus fréquentes */}
            {receivedReviews.length > 0 && receivedReviews.some(review => review.qualites && review.qualites.length > 0) && (
              <Box sx={{
                m: 3,
                p: 2,
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid rgba(34, 197, 94, 0.1)',
                boxShadow: '0 2px 4px rgba(34, 197, 94, 0.05)'
              }}>
                <Typography variant="subtitle1" sx={{
                  color: '#2D3748',
                  fontWeight: 'bold',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Star size={16} color="#16A34A" />
                  Points forts les plus appréciés
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  {Array.from(
                    receivedReviews.reduce((acc, review) => {
                      if (review.qualites) {
                        review.qualites.forEach(qualite => {
                          acc.set(qualite, (acc.get(qualite) || 0) + 1);
                        });
                      }
                      return acc;
                    }, new Map())
                  )
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 8)
                    .map(([qualite, count], index) => (
                      <Box
                        key={index}
                        sx={{
                          backgroundColor: 'rgba(34, 197, 94, 0.08)',
                          color: '#16A34A',
                          fontWeight: 'medium',
                          fontSize: '0.75rem',
                          height: '28px',
                          padding: '0 12px',
                          borderRadius: '14px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          border: '1px solid rgba(34, 197, 94, 0.2)',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            backgroundColor: 'rgba(34, 197, 94, 0.12)',
                            transform: 'translateY(-1px)',
                            boxShadow: '0 2px 4px rgba(34, 197, 94, 0.1)'
                          }
                        }}
                      >
                        <span>{qualite}</span>
                        <Typography
                          component="span"
                          sx={{
                            backgroundColor: 'white',
                            color: '#16A34A',
                            fontSize: '0.7rem',
                            fontWeight: 'bold',
                            padding: '2px 6px',
                            borderRadius: '10px',
                            border: '1px solid rgba(34, 197, 94, 0.2)'
                          }}
                        >
                          {count}
                        </Typography>
                      </Box>
                    ))}
                </Box>
              </Box>
            )}

            {/* Section des défauts les plus fréquents */}
            {receivedReviews.length > 0 && receivedReviews.some(review => review.defauts && review.defauts.length > 0) && (
              <Box sx={{
                m: 3,
                p: 2,
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid rgba(239, 68, 68, 0.1)',
                boxShadow: '0 2px 4px rgba(239, 68, 68, 0.05)'
              }}>
                <Typography variant="subtitle1" sx={{
                  color: '#2D3748',
                  fontWeight: 'bold',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <User size={16} color="#DC2626" />
                  Points à améliorer les plus mentionnés
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  {Array.from(
                    receivedReviews.reduce((acc: Map<string, number>, review) => {
                      if (review.defauts && review.defauts.length > 0) {
                        review.defauts.forEach(defaut => {
                          acc.set(defaut, (acc.get(defaut) || 0) + 1);
                        });
                      }
                      return acc;
                    }, new Map<string, number>())
                  )
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 8)
                    .map(([defaut, count], index) => (
                      <Box
                        key={index}
                        sx={{
                          backgroundColor: 'rgba(239, 68, 68, 0.08)',
                          color: '#DC2626',
                          fontWeight: 'medium',
                          fontSize: '0.75rem',
                          height: '28px',
                          padding: '0 12px',
                          borderRadius: '14px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          border: '1px solid rgba(239, 68, 68, 0.2)',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            backgroundColor: 'rgba(239, 68, 68, 0.12)',
                            transform: 'translateY(-1px)',
                            boxShadow: '0 2px 4px rgba(239, 68, 68, 0.1)'
                          }
                        }}
                      >
                        <span>{defaut}</span>
                        <Typography
                          component="span"
                          sx={{
                            backgroundColor: 'white',
                            color: '#DC2626',
                            fontSize: '0.7rem',
                            fontWeight: 'bold',
                            padding: '2px 6px',
                            borderRadius: '10px',
                            border: '1px solid rgba(239, 68, 68, 0.2)'
                          }}
                        >
                          {count}
                        </Typography>
                      </Box>
                    ))}
                </Box>
              </Box>
            )}

            <StyledTabs
              value={selectedTab}
              onChange={(_, newValue) => setSelectedTab(newValue)}
              variant="fullWidth"
            >
              <StyledTab label={
                <Badge
                  badgeContent={receivedReviews.length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#FF965E',
                      fontWeight: 'bold'
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    Avis reçus
                    {stats.receivedAnswered > 0 && (
                      <Chip
                        size="small"
                        label={`${stats.receivedAnswered} répondu${stats.receivedAnswered > 1 ? 's' : ''}`}
                        sx={{
                          height: '20px',
                          backgroundColor: '#FFF8F3',
                          color: '#FF7A35',
                          border: '1px solid #FFE4BA',
                          '& .MuiChip-label': { px: 1, fontSize: '0.65rem' }
                        }}
                      />
                    )}
                  </div>
                </Badge>
              } />
              <StyledTab label={
                <Badge
                  badgeContent={sentReviews.length}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#FF965E',
                      fontWeight: 'bold'
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    Avis envoyés
                    {stats.sentModified > 0 && (
                      <Chip
                        size="small"
                        label={`${stats.sentModified} modifié${stats.sentModified > 1 ? 's' : ''}`}
                        sx={{
                          height: '20px',
                          backgroundColor: '#FFF8F3',
                          color: '#FF7A35',
                          border: '1px solid #FFE4BA',
                          '& .MuiChip-label': { px: 1, fontSize: '0.65rem' }
                        }}
                      />
                    )}
                  </div>
                </Badge>
              } />
            </StyledTabs>

            <div role="tabpanel" hidden={selectedTab !== 0} className="p-4">
              {selectedTab === 0 && (
                <div className="space-y-6">
                  {isLoading ? (
                    <div className="p-12 text-center text-gray-500 animate-pulse">
                      <div className="flex justify-center items-center space-x-2">
                        <div className="w-4 h-4 bg-[#FFE4BA] rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                        <div className="w-4 h-4 bg-[#FF965E] rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                        <div className="w-4 h-4 bg-[#FF7A35] rounded-full animate-bounce"></div>
                      </div>
                      <p className="mt-4 font-medium">Chargement des avis...</p>
                    </div>
                  ) : filterReviews(receivedReviews).length === 0 ? (
                    <div className="p-12 text-center">
                      <div className="text-[#FF965E] opacity-60 mb-3">
                        <Star size={48} className="mx-auto mb-3" />
                      </div>
                      <p className="text-gray-700 font-medium text-lg">Aucun avis reçu pour le moment</p>
                      <p className="text-gray-500 mt-2">Les avis que vous recevrez apparaîtront ici</p>
                    </div>
                  ) : (
                    filterReviews(receivedReviews).map((review, index, array) => (
                      <motion.div
                        key={review.id}
                        ref={index === array.length - 1 ? lastReviewElementRef : null}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          scale: updatedReviewId === review.id ? [1, 1.02, 1] : 1,
                        }}
                        transition={{
                          duration: 0.3,
                          delay: index * 0.05,
                          scale: { duration: 0.5 },
                        }}
                      >
                        <ReviewItem
                          review={review}
                          onClick={handleReviewClick}
                          onUserClick={handleUserProfileClick}
                          onEditReview={handleEditReview}
                          onDeleteReview={handleDeleteReview}
                          onMissionClick={handleOpenMissionModal}
                          isSent={!!selectedTab}
                          onReport={handleOpenReportModal}
                          onOpenPhotoModal={openPhotoModal}
                        />
                      </motion.div>
                    ))
                  )}
                </div>
              )}
            </div>

            <div role="tabpanel" hidden={selectedTab !== 1} className="p-4">
              {selectedTab === 1 && (
                <div className="space-y-6">
                  {isLoading ? (
                    <div className="p-12 text-center text-gray-500 animate-pulse">
                      <div className="flex justify-center items-center space-x-2">
                        <div className="w-4 h-4 bg-[#FFE4BA] rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                        <div className="w-4 h-4 bg-[#FF965E] rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                        <div className="w-4 h-4 bg-[#FF7A35] rounded-full animate-bounce"></div>
                      </div>
                      <p className="mt-4 font-medium">Chargement des avis...</p>
                    </div>
                  ) : filterReviews(sentReviews).length === 0 ? (
                    <div className="p-12 text-center">
                      <div className="text-[#FF965E] opacity-60 mb-3">
                        <Star size={48} className="mx-auto mb-3" />
                      </div>
                      <p className="text-gray-700 font-medium text-lg">Aucun avis envoyé pour le moment</p>
                      <p className="text-gray-500 mt-2">Les avis que vous enverrez apparaîtront ici</p>
                    </div>
                  ) : (
                    filterReviews(sentReviews).map((review, index, array) => (
                      <motion.div
                        key={review.id}
                        ref={index === array.length - 1 ? lastReviewElementRef : null}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          scale: updatedReviewId === review.id ? [1, 1.02, 1] : 1,
                        }}
                        transition={{
                          duration: 0.3,
                          delay: index * 0.05,
                          scale: { duration: 0.5 },
                        }}
                      >
                        <ReviewItem
                          review={review}
                          onClick={handleReviewClick}
                          onUserClick={handleUserProfileClick}
                          onEditReview={handleEditReview}
                          onDeleteReview={handleDeleteReview}
                          onMissionClick={handleOpenMissionModal}
                          isSent={!!selectedTab}
                          onReport={handleOpenReportModal}
                          onOpenPhotoModal={openPhotoModal}
                        />
                      </motion.div>
                    ))
                  )}
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {selectedReview && (
        <ReviewReplyModal
          isOpen={isReplyModalOpen}
          onClose={() => {
            setIsReplyModalOpen(false);
            setSelectedReview(null);
          }}
          reviewId={selectedReview.id}
          existingReply={selectedReview.reponse}
          onReplySubmitted={handleReplySubmitted}
          reviewData={{
            note: selectedReview.note,
            commentaire: selectedReview.commentaire,
            qualites: selectedReview.qualites,
            defauts: selectedReview.defauts,
            mission_titre: selectedReview.mission?.titre
          }}
        />
      )}

      {selectedUserData && (
        <UserProfileModal
          isOpen={isUserProfileModalOpen}
          onClose={() => setIsUserProfileModalOpen(false)}
          userData={selectedUserData}
        />
      )}

      {selectedReviewToEdit && (
        <ReviewModalPopupDepotAvis
          isOpen={isEditReviewModalOpen}
          onClose={() => {
            setIsEditReviewModalOpen(false);
            setSelectedReviewToEdit(null);
          }}
          userId={selectedReviewToEdit.target_user?.id || ''}
          mission_id={selectedReviewToEdit.mission_id}
          onReviewAdded={handleReviewEdited}
          reviewToEdit={{
            id: selectedReviewToEdit.id,
            note: selectedReviewToEdit.note,
            commentaire: selectedReviewToEdit.commentaire,
            qualites: selectedReviewToEdit.qualites,
            defauts: selectedReviewToEdit.defauts || []
          }}
        />
      )}

      {/* Modal de confirmation de suppression */}
      <ModalPortal
        isOpen={isDeleteConfirmOpen}
        onBackdropClick={() => setIsDeleteConfirmOpen(false)}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-full max-w-[600px] relative max-h-[90vh] overflow-y-auto"
        >
          <div className="flex items-center justify-between p-8 pb-4 border-b border-gray-100">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] rounded-xl shadow-sm">
                <Trash2 className="h-7 w-7 text-[#FF6B2C]" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Confirmer la suppression</h2>
                <p className="text-sm text-gray-500 mt-1">Cette action est irréversible</p>
              </div>
            </div>
            <IconButton
              onClick={() => setIsDeleteConfirmOpen(false)}
              className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 rounded-full"
            >
              <X size={24} />
            </IconButton>
          </div>

          <div className="p-8">
            {reviewToDelete?.note && (
              <Box className="p-4 bg-gray-50 rounded-lg mb-6">
                <Box className="flex items-center gap-2 text-gray-600 mb-2">
                  <Star className="text-[#FF6B2C]" size={18} />
                  <Typography variant="body1" component="span" sx={{ fontWeight: 600 }}>
                    {reviewToDelete.note}/5
                  </Typography>
                </Box>
                {reviewToDelete.commentaire && reviewToDelete.commentaire.trim() !== "" && (
                  <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                    "{reviewToDelete.commentaire}"
                  </Typography>
                )}
              </Box>
            )}

            <div className="flex justify-end gap-3">
              <Button
                onClick={() => setIsDeleteConfirmOpen(false)}
                variant="outlined"
                sx={{
                  color: '#64748B',
                  borderColor: '#E2E8F0',
                  '&:hover': {
                    backgroundColor: '#F1F5F9',
                    borderColor: '#CBD5E1'
                  }
                }}
              >
                Annuler
              </Button>
              <Button
                onClick={confirmDeleteReview}
                variant="contained"
                color="error"
                sx={{
                  backgroundColor: '#EF4444',
                  '&:hover': {
                    backgroundColor: '#DC2626'
                  }
                }}
              >
                Supprimer
              </Button>
            </div>
          </div>
        </motion.div>
      </ModalPortal>

      {/* Indicateur de chargement global pour les actions */}
      {isActionLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 shadow-xl">
            <div className="flex items-center space-x-3">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-[#FFE4BA] rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-3 h-3 bg-[#FF965E] rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-3 h-3 bg-[#FF7A35] rounded-full animate-bounce"></div>
              </div>
              <span className="text-gray-600 font-medium">Mise à jour en cours...</span>
            </div>
          </div>
        </div>
      )}

      {(isFetchingMore || (selectedTab === 0 ? hasMoreReceived : hasMoreSent)) && (
        <div className="p-6 text-center text-gray-500">
          <div className="flex justify-center items-center space-x-2">
            <div className="w-3 h-3 bg-[#FFE4BA] rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-3 h-3 bg-[#FF965E] rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-3 h-3 bg-[#FF7A35] rounded-full animate-bounce"></div>
          </div>
          <p className="mt-4 font-medium">Chargement des avis...</p>
        </div>
      )}

      {/* Modal de sélection de mission pour déposer un avis */}
      <ReviewModalSelecteurMission
        isOpen={isMissionSelectModalOpen}
        onClose={() => setIsMissionSelectModalOpen(false)}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Modal de détails de mission */}
      {selectedMission && (
        <MissionRecapModal
          open={isMissionModalOpen}
          mission={selectedMission}
          onClose={() => setIsMissionModalOpen(false)}
        />
      )}

      <Dialog open={reportModalOpen} onClose={() => setReportModalOpen(false)} maxWidth="xs" fullWidth>
        <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Signaler cet avis</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, color: '#222' }}>
            Merci d'indiquer la raison de votre signalement. L'équipe de modération analysera votre demande.
          </Typography>
          <TextField
            label="Raison du signalement"
            fullWidth
            multiline
            minRows={2}
            value={reportReason}
            onChange={e => setReportReason(e.target.value)}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportModalOpen(false)} color="inherit" sx={{ fontWeight: 600 }}>Annuler</Button>
          <Button
            onClick={handleSendReport}
            color="error"
            variant="contained"
            sx={{ fontWeight: 700, bgcolor: '#FF6B2C' }}
            disabled={reportLoading || !reportReason.trim()}
          >
            {reportLoading ? 'Envoi...' : 'Signaler'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* MODAL DE VISUALISATION DES PHOTOS */}
      {photoModalOpen && currentPhotos.length > 0 && (
        <ModalPortal
          isOpen={photoModalOpen}
          onBackdropClick={closePhotoModal}
          zIndex={2000}
        >
          <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-2 sm:p-4">
            <div className="relative w-full h-full max-w-7xl bg-white rounded-none sm:rounded-2xl shadow-2xl overflow-hidden flex flex-col">

              {/* Header de la modal */}
              <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between flex-shrink-0">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <Star size={16} className="sm:w-5 sm:h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-base sm:text-lg">Photos de l'avis</h3>
                    <p className="text-white text-opacity-90 text-xs sm:text-sm">
                      {currentPhotos.length} photo{currentPhotos.length > 1 ? 's' : ''} disponible{currentPhotos.length > 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                <button
                  onClick={closePhotoModal}
                  className="w-8 h-8 sm:w-10 sm:h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-200 group"
                >
                  <X size={16} className="sm:w-5 sm:h-5 text-white group-hover:scale-110 transition-transform duration-200" />
                </button>
              </div>

              {/* Contenu principal - Zone d'affichage de l'image */}
              <div className="relative flex-1 bg-gradient-to-br from-[#FFF8F3] to-white flex items-center justify-center p-2 sm:p-4 md:p-6 min-h-0">
                <div className="relative w-full h-full flex items-center justify-center">
                  <img
                    src={currentPhotos[currentPhotoIndex]}
                    alt={`Photo ${currentPhotoIndex + 1} de l'avis`}
                    className="max-w-full max-h-full object-contain rounded-lg sm:rounded-xl shadow-lg"
                  />

                  {/* Overlay avec informations */}
                  <div className="absolute top-2 sm:top-4 left-2 sm:left-4 bg-black bg-opacity-60 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">
                    {currentPhotoIndex + 1} / {currentPhotos.length}
                  </div>
                </div>

                {/* Boutons de navigation */}
                {currentPhotos.length > 1 && (
                  <>
                    <button
                      onClick={goToPreviousPhoto}
                      className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 z-10"
                    >
                      <ChevronLeft size={20} className="sm:w-6 sm:h-6" />
                    </button>

                    <button
                      onClick={goToNextPhoto}
                      className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 z-10"
                    >
                      <ChevronRight size={20} className="sm:w-6 sm:h-6" />
                    </button>
                  </>
                )}
              </div>

              {/* Footer avec miniatures - Seulement si plusieurs photos */}
              {currentPhotos.length > 1 && (
                <div className="bg-white border-t border-gray-100 px-3 sm:px-6 py-3 sm:py-4 flex-shrink-0">
                  <div className="flex items-center justify-center gap-2 sm:gap-3 overflow-x-auto pb-2">
                    {currentPhotos.map((photo, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentPhotoIndex(index)}
                        className={`flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 hover:scale-105 ${
                          index === currentPhotoIndex
                            ? 'border-[#FF6B2C] shadow-lg'
                            : 'border-gray-200 hover:border-[#FFE4BA]'
                        }`}
                      >
                        <img
                          src={photo}
                          alt={`Miniature ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>

                  {/* Instructions - Masquées sur mobile */}
                  <div className="text-center mt-2 hidden sm:block">
                    <p className="text-gray-500 text-sm">
                      Utilisez les flèches du clavier ou cliquez sur les miniatures pour naviguer
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModalPortal>
      )}
    </motion.div>
  );
};

interface ReviewItemProps {
  review: Review;
  onClick: (review: Review) => void;
  onUserClick: (userId: string) => void;
  onEditReview: (review: Review, e: React.MouseEvent) => void;
  onDeleteReview: (review: Review, e: React.MouseEvent) => void;
  onMissionClick: (review: Review, e: React.MouseEvent) => void;
  isSent?: boolean;
  onReport: (review: Review) => void;
  onOpenPhotoModal: (photos: string[], startIndex: number) => void;
}

const ReviewItem: React.FC<ReviewItemProps> = ({
  review,
  onClick,
  onUserClick,
  onEditReview,
  onDeleteReview,
  onMissionClick,
  isSent = false,
  onReport,
  onOpenPhotoModal
}) => {
  const handleUserClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const userId = isSent ? review.target_user?.id : review.author?.id;
    if (userId) {
      onUserClick(userId);
    }
  };

  const userProfile = isSent ? review.target_user?.profil?.[0] : review.author?.profil?.[0];

  return (
    <div
      id={`review-${review.id}`}
      className="relative mb-6 cursor-pointer group"
      onClick={() => onClick(review)}
    >
      <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-[#FFE4BA]">
        {/* En-tête avec photo et note */}
        <div className="bg-gradient-to-r from-[#FFF8F3] to-white border-b border-gray-100 p-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
            <div className="flex items-center gap-3">
              <div
                onClick={handleUserClick}
                className="cursor-pointer relative"
              >
                <Avatar
                  src={userProfile?.photo_url || DEFAULT_AVATAR}
                  alt={`${userProfile?.prenom} ${userProfile?.nom?.charAt(0)}.`}
                  sx={{
                    width: { xs: 50, sm: 60 },
                    height: { xs: 50, sm: 60 },
                    border: '3px solid #FFE4BA',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                    '&:hover': {
                      borderColor: '#FF965E',
                      transform: 'scale(1.05)',
                      boxShadow: '0 8px 24px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-[#FF7A35] rounded-full flex items-center justify-center text-white text-xs font-bold shadow-md">
                  {review.note}
                </div>
              </div>
              <div>
                <h3
                  className="font-semibold text-gray-900 text-lg group-hover:text-[#FF7A35] transition-colors duration-200 cursor-pointer hover:underline"
                  onClick={handleUserClick}
                >
                  {isSent ? (
                    <>Pour : {userProfile?.prenom} {userProfile?.nom?.charAt(0)}.</>
                  ) : (
                    <>{userProfile?.prenom} {userProfile?.nom?.charAt(0)}.</>
                  )}
                </h3>
                <div className="flex items-center mt-1">
                  <div className="flex mr-2">
                    {[...Array(5)].map((_, index) => (
                      <Star
                        key={index}
                        size={14}
                        className={`${
                          index < review.note
                            ? 'text-[#FF965E] fill-current'
                            : 'text-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500">
                    {format(new Date(review.created_at), 'dd MMMM yyyy', { locale: fr })}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center mt-3 sm:mt-0 justify-between sm:justify-end sm:gap-2">
              {review.reponse && (
                <Chip
                  icon={<MessageCircle size={14} />}
                  label={isSent ? "Réponse reçue" : "Répondu"}
                  size="small"
                  sx={{
                    backgroundColor: '#FFF8F3',
                    color: '#FF7A35',
                    borderColor: '#FFE4BA',
                    border: '1px solid',
                    fontWeight: 500,
                    borderRadius: '8px',
                    height: '28px',
                    '& .MuiChip-label': {
                      fontSize: '0.75rem'
                    }
                  }}
                />
              )}
              <Tooltip title="Signaler cet avis">
                <IconButton size="small" onClick={e => { e.stopPropagation(); onReport(review); }} sx={{ color: '#FF6B2C' }}>
                  <ReportProblemIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              {isSent && (
                <div className="flex gap-2">
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<SquarePen size={16} className="sm:block hidden" />}
                    onClick={(e) => onEditReview(review, e)}
                    sx={{
                      backgroundColor: '#FF7A35',
                      color: 'white',
                      padding: '6px 12px',
                      borderRadius: '8px',
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      minWidth: '0',
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        backgroundColor: '#FF965E',
                        boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                      }
                    }}
                  >
                    <span className="hidden sm:inline">Modifier</span>
                    <span className="sm:hidden">
                      <SquarePen size={16} />
                    </span>
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Trash2 size={16} className="sm:block hidden" />}
                    onClick={(e) => onDeleteReview(review, e)}
                    sx={{
                      borderColor: '#EF4444',
                      color: '#EF4444',
                      padding: '6px 12px',
                      borderRadius: '8px',
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      minWidth: '0',
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        backgroundColor: '#FEE2E2',
                        borderColor: '#DC2626'
                      }
                    }}
                  >
                    <span className="hidden sm:inline">Supprimer</span>
                    <span className="sm:hidden">
                      <Trash2 size={16} />
                    </span>
                  </Button>
                </div>
              )}

              {(!isSent && !review.reponse) && (
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<MessageCircle size={16} className="sm:block hidden" />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onClick(review);
                  }}
                  sx={{
                    backgroundColor: '#FF7A35',
                    color: 'white',
                    padding: '6px 12px',
                    borderRadius: '8px',
                    fontSize: '0.75rem',
                    fontWeight: 'bold',
                    whiteSpace: 'nowrap',
                    '&:hover': {
                      backgroundColor: '#FF965E',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                    }
                  }}
                >
                  <span className="hidden sm:inline">Répondre</span>
                  <span className="sm:hidden">
                    <MessageCircle size={16} />
                  </span>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Corps de l'avis */}
        <div className="p-4">
          {/* Mission liée */}
          {review.mission && review.mission.id && review.mission.titre && (
            <div
              className="mb-4 p-3 bg-gradient-to-r from-gray-50 to-white rounded-lg border border-gray-100 cursor-pointer group/mission hover:shadow-md transition-all duration-300"
              onClick={(e) => onMissionClick(review, e)}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[#FFF8F3] group-hover/mission:bg-[#FFE4BA] transition-colors duration-300">
                  <Briefcase size={16} className="text-[#FF965E]" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm text-gray-900 truncate group-hover/mission:text-[#FF7A35] transition-colors duration-300">
                    {review.mission.titre}
                  </h4>
                  <div className="flex items-center gap-1">
                    {review.mission.category_id && (
                      <Chip
                        size="small"
                        label={SERVICE_CATEGORIES.find(cat => cat.id === review.mission.category_id)?.nom || review.mission.category_id}
                        sx={{
                          height: '20px',
                          backgroundColor: '#F3F4F6',
                          color: '#6B7280',
                          border: '1px solid #E5E7EB',
                          '& .MuiChip-label': {
                            px: 1,
                            fontSize: '0.65rem',
                            fontWeight: 500
                          }
                        }}
                      />
                    )}
                    {review.mission.subcategory_id && (
                      <Chip
                        size="small"
                        label={SERVICE_SUBCATEGORIES.find(subcat => subcat.id === review.mission.subcategory_id)?.nom || review.mission.subcategory_id}
                        sx={{
                          height: '20px',
                          backgroundColor: '#F3F4F6',
                          color: '#6B7280',
                          border: '1px solid #E5E7EB',
                          '& .MuiChip-label': {
                            px: 1,
                            fontSize: '0.65rem',
                            fontWeight: 500
                          }
                        }}
                      />
                    )}
                  </div>
                </div>
                <ChevronRight size={16} className="text-gray-400 group-hover/mission:text-[#FF7A35] transition-colors duration-300" />
              </div>
            </div>
          )}

          {/* Commentaire */}
          {review.commentaire ? (
            <div className="relative">
              <div className="bg-gradient-to-r from-white to-[#FFF8F3] p-4 rounded-lg border-l-4 border-[#FFE4BA] mb-4">
                <p className="text-gray-700 text-sm leading-relaxed"
                   dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(review.commentaire) }}
                />
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg text-center mb-4">
              <p className="text-gray-400 italic text-sm">
                Aucun commentaire ajouté
              </p>
            </div>
          )}

          {/* Photos de l'avis */}
          {review.photos && review.photos.length > 0 && (
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2 max-w-md mx-auto">
                {review.photos.map((photoUrl, index) => (
                  <div key={index} className="relative">
                    <img
                      src={photoUrl}
                      alt={`Photo ${index + 1} de l'avis`}
                      className="w-full h-32 object-cover rounded-lg border-2 border-[#FFE4BA] shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Ouvrir la modal de visualisation des photos
                        if (review.photos && review.photos.length > 0) {
                          onOpenPhotoModal(review.photos, index);
                        }
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.02)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </div>
                ))}
              </div>
              {review.photos.length > 0 && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  {review.photos.length} photo{review.photos.length > 1 ? 's' : ''} - Cliquez pour agrandir
                </p>
              )}
            </div>
          )}

          {/* Réponse */}
          {review.reponse && (
            <div className="relative pl-4 ml-6">
              <div className="relative bg-gradient-to-r from-white to-[#FFF8F3] rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-[#FFE4BA]/30">
                <div className="absolute -left-5 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gradient-to-br from-[#FFF8F3] to-white shadow-md border border-[#FFE4BA]">
                  <MessageCircle size={14} className="text-[#FF965E]" />
                </div>
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-sm font-semibold text-gray-700 bg-gradient-to-r from-[#FF7A35] to-[#FF965E] bg-clip-text text-transparent">
                      Réponse
                    </span>
                  </div>
                  {!isSent && (
                    <Button
                      variant="text"
                      size="small"
                      startIcon={<SquarePen size={14} />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onClick(review);
                      }}
                      sx={{
                        color: '#FF7A35',
                        fontSize: '0.75rem',
                        padding: '4px 12px',
                        borderRadius: '8px',
                        minWidth: 0,
                        '&:hover': {
                          backgroundColor: '#FFF8F3',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)'
                        }
                      }}
                    >
                      Modifier
                    </Button>
                  )}
                </div>
                <div className="relative">
                  <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-[#FFE4BA] to-transparent rounded-full opacity-30"></div>
                  <p className="text-gray-600 text-sm leading-relaxed pl-3"
                     dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(review.reponse) }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Qualités */}
          {review.qualites.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500, mb: 1, display: 'block' }}>
                Points forts :
              </Typography>
              <div className="flex flex-wrap gap-2">
                {review.qualites.map((qualite, index) => (
                  <Chip
                    key={index}
                    label={qualite}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(34, 197, 94, 0.08)',
                      color: '#16A34A',
                      border: '1px solid rgba(34, 197, 94, 0.2)',
                      borderRadius: '6px',
                      height: '22px',
                      '& .MuiChip-label': {
                        px: 1.5,
                        fontSize: '0.7rem',
                        fontWeight: 500
                      },
                      '&:hover': {
                        backgroundColor: 'rgba(34, 197, 94, 0.12)'
                      }
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Défauts */}
          {review.defauts && review.defauts.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500, mb: 1, display: 'block' }}>
                Points à améliorer :
              </Typography>
              <div className="flex flex-wrap gap-2 mt-1">
                {review.defauts.map((defaut, index) => (
                  <Chip
                    key={index}
                    label={defaut}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(239, 68, 68, 0.08)',
                      color: '#DC2626',
                      border: '1px solid rgba(239, 68, 68, 0.2)',
                      borderRadius: '6px',
                      height: '22px',
                      '& .MuiChip-label': {
                        px: 1.5,
                        fontSize: '0.7rem',
                        fontWeight: 500
                      },
                      '&:hover': {
                        backgroundColor: 'rgba(239, 68, 68, 0.12)'
                      }
                    }}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Pied avec informations supplémentaires */}
        <div className="bg-gray-50 px-4 py-2 border-t border-gray-100 flex justify-between items-center">
          <div className="text-xs text-gray-500">
            {review.is_modified && review.modified_at && (
              <span className="italic">
                Modifié le {format(new Date(review.modified_at), 'dd/MM/yyyy', { locale: fr })}
              </span>
            )}
          </div>

          {/* Horodatage */}
          <div className="text-xs text-gray-500 flex items-center">
            <Clock size={12} className="mr-1 text-gray-400" />
            {format(new Date(review.created_at), 'HH:mm', { locale: fr })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MesAvis;