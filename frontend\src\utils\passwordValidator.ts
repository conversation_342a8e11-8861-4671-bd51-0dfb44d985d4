interface PasswordStrength {
  score: number;
  feedback: {
    warning: string;
    suggestions: string[];
  };
}

export function validatePassword(password: string): PasswordStrength {
  let score = 0;
  const feedback = {
    warning: '',
    suggestions: [] as string[],
  };

  // Longueur minimale
  if (password.length < 8) {
    feedback.warning = 'Le mot de passe est trop court';
    feedback.suggestions.push('Utilisez au moins 8 caractères');
    return { score, feedback };
  }

  // Vérification des types de caractères
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSpecialChars = /[^a-zA-Z0-9]/.test(password);

  // Compte le nombre de types de caractères présents
  const typesCount = [hasLowercase, hasUppercase, hasNumbers, hasSpecialChars].filter(Boolean).length;

  // Détection d'un mot de passe généré
  const isGenerated = password.length >= 12 && 
                     typesCount >= 3 && 
                     /[^a-zA-Z0-9].*[^a-zA-Z0-9]/.test(password); // Au moins 2 caractères spéciaux

  if (isGenerated) {
    score = 4; // Très fort pour les mots de passe générés
    return { score, feedback };
  }

  // Score basé sur la longueur pour les mots de passe non générés
  if (password.length >= 16) {
    score += 2;
  } else if (password.length >= 12) {
    score += 1;
  }

  // Score basé sur la diversité des caractères
  if (typesCount >= 4) score += 2;
  else if (typesCount >= 3) score += 1;

  // Pénalités pour les motifs communs
  if (password.match(/^[A-Z][a-z]+[0-9]+$/)) { // Motif type "Gerard66"
    score = Math.max(0, score - 2);
    feedback.warning = 'Attention ! Ce format de mot de passe est facilement piraté. Utilisez un mot de passe plus complexe.';
  }

  // Pénalités pour les motifs simples
  if (password.match(/^[a-zA-Z]+[0-9]+[^a-zA-Z0-9]?$/)) { // Lettres suivies de chiffres et peut-être un caractère spécial
    score = Math.max(0, score - 1);
    feedback.warning = 'Attention ! Ce format de mot de passe est trop simple. Mélangez mieux les différents types de caractères.';
  }

  // Suggestions basées sur les types manquants
  if (!hasLowercase) feedback.suggestions.push('Ajoutez des lettres minuscules');
  if (!hasUppercase) feedback.suggestions.push('Ajoutez des lettres majuscules');
  if (!hasNumbers) feedback.suggestions.push('Ajoutez des chiffres');
  if (!hasSpecialChars) feedback.suggestions.push('Ajoutez des caractères spéciaux');

  // Normalisation du score
  score = Math.min(4, score);

  // Si le mot de passe est court et n'a que 2 types de caractères, force un score faible
  if (password.length < 12 && typesCount <= 2) {
    score = Math.min(score, 1);
  }

  return { score, feedback };
}