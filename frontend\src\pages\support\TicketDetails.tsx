import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  Breadcrumbs,
  useMediaQuery,
  Avatar,
  IconButton,
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  EventNote as EventNoteIcon,
  Home as HomeIcon,
  Support as SupportIcon,
  Assignment as AssignmentIcon,
  ListAlt as ListAltIcon,
  PriorityHigh as PriorityHighIcon,
  Category as CategoryIcon,
  LocalOffer as LocalOfferIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  EditCalendar as EditCalendarIcon,
} from '@mui/icons-material';
import { format, differenceInMinutes } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import CommentsList from '../../components/support/TicketCommentsList';
import TicketAttachmentsList from '../../components/support/TicketAttachmentsList';
import useTickets from '../../hooks/useTickets';
import supportTicketService from '../../services/supportTicketService';
import { notify } from '../../components/Notification';
import ModalPortal from '../../components/ModalPortal';
import { motion } from 'framer-motion';
import { formatTicketStatus, formatTicketPriority } from '../../utils/formatters';
import logger from '@/utils/logger';

const TicketDetails: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { ticketId } = useParams<{ ticketId: string }>();
  const { user } = useAuth();
  const [ticket, setTicket] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isAdminRoute = window.location.pathname.includes('/admin/');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  
  // Utiliser notre hook pour récupérer un ticket
  const { getTicket } = useTickets();
  
  useEffect(() => {
    const fetchTicketDetails = async () => {
      if (!ticketId) return;
      
      try {
        setLoading(true);
        const fetchedTicket = await getTicket(ticketId);
        
        if (fetchedTicket) {
          setTicket(fetchedTicket);
        } else {
          setError(new Error('Ticket non trouvé'));
        }
      } catch (err) {
        logger.error('Erreur lors de la récupération du ticket:', err);
        setError(err instanceof Error ? err : new Error('Une erreur est survenue'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchTicketDetails();
  }, [ticketId, getTicket]);
  
  // Déterminer si l'utilisateur est membre du staff (admin ou modérateur)
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';
  
  // Formater la date au format français
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return dateString;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'nouveau':
        return '#3498db'; // Bleu
      case 'en_cours':
        return '#f39c12'; // Orange
      case 'resolu':
        return '#2ecc71'; // Vert
      case 'ferme':
        return '#95a5a6'; // Gris
      default:
        return '#95a5a6'; // Gris par défaut
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgente':
        return '#e74c3c'; // Rouge
      case 'elevee':
        return '#f39c12'; // Orange
      case 'normale':
        return '#3498db'; // Bleu
      case 'faible':
        return '#2ecc71'; // Vert
      default:
        return theme.palette.grey[500];
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technique':
        return '🔧';
      case 'facturation':
        return '💰';
      case 'compte':
        return '👤';
      case 'mission':
        return '📋';
      case 'autre':
        return '📌';
      default:
        return '📄';
    }
  };
  
  // Vérifier si le ticket a moins de 30 minutes (pour l'édition et la suppression)
  const canUserModify = useCallback(() => {
    if (!ticket || !user) return false;
    
    // Les admins/modos peuvent toujours modifier
    if (user.role === 'jobpadm' || user.role === 'jobmodo') return true;
    
    // L'utilisateur ne peut modifier que son propre ticket
    if (user.id !== ticket.user_id) return false;
    
    // Vérifier si le ticket a moins de 30 minutes
    const creationDate = new Date(ticket.created_at);
    const now = new Date();
    const minutesSinceCreation = differenceInMinutes(now, creationDate);
    
    return minutesSinceCreation <= 30;
  }, [ticket, user]);

  // Obtenir le temps restant en minutes pour la modification
  const getRemainingTime = useCallback(() => {
    if (!ticket) return 0;
    
    const creationDate = new Date(ticket.created_at);
    const now = new Date();
    const minutesSinceCreation = differenceInMinutes(now, creationDate);
    
    return Math.max(0, 30 - minutesSinceCreation);
  }, [ticket]);

  // Gérer la suppression du ticket
  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };
  
  const confirmDelete = async () => {
    if (!ticket || !ticketId) return;
    
    try {
      await supportTicketService.deleteTicket(ticketId);
      notify('Ticket supprimé avec succès', 'success');
      setShowDeleteConfirmation(false);
      // Rediriger vers la liste des tickets
      navigate(isAdminRoute ? '/admin/support/tickets' : '/dashboard/support/tickets');
    } catch (error) {
      logger.error('Erreur lors de la suppression du ticket:', error);
      notify('Erreur lors de la suppression du ticket', 'error');
    }
  };
  
  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
  };
  
  if (loading) {
    return (
      <Box 
        sx={{ 
          minHeight: '100vh',
          background: 'linear-gradient(160deg, #FFF8F3 0%, rgba(255, 255, 255, 0.95) 100%)',
          pt: { xs: 2, sm: 3 },
          pb: { xs: 4, sm: 6 },
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress sx={{ color: '#FF6B2C' }} />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box 
        sx={{ 
          minHeight: '100vh',
          background: 'linear-gradient(160deg, #FFF8F3 0%, rgba(255, 255, 255, 0.95) 100%)',
          pt: { xs: 2, sm: 3 },
          pb: { xs: 4, sm: 6 }
        }}
      >
        <Box 
          sx={{ 
            px: { xs: '8px', md: 0 },
            margin: 0,
            width: '100%'
          }}
        >
          <Breadcrumbs 
            aria-label="breadcrumb"
            sx={{ 
              mb: 3, 
              background: 'rgba(255, 255, 255, 0.7)',
              py: 1.5, 
              px: 2, 
              borderRadius: 2,
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}
          >
            <Button
              component={Link}
              to={isAdminRoute ? "/admin" : "/dashboard"}
              size="small"
              startIcon={<HomeIcon />}
              sx={{ 
                color: 'rgba(0, 0, 0, 0.6)',
                '&:hover': { color: '#FF6B2C' },
                textTransform: 'none',
                fontWeight: 'normal'
              }}
            >
              {isAdminRoute ? "Administration" : "Tableau de bord"}
            </Button>
            <Button
              component={Link}
              to={isAdminRoute ? "/admin/support/tickets" : "/dashboard/support/tickets"}
              size="small"
              startIcon={<ListAltIcon />}
              sx={{ 
                color: 'rgba(0, 0, 0, 0.6)',
                '&:hover': { color: '#FF6B2C' },
                textTransform: 'none',
                fontWeight: 'normal'
              }}
            >
              Tickets
            </Button>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
              <SupportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
              Erreur
            </Typography>
          </Breadcrumbs>

          <Paper 
            elevation={3}
            sx={{
              borderRadius: { xs: '12px', sm: '16px' },
              overflow: 'hidden',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
              position: 'relative',
              p: { xs: 2, sm: 3 },
              mb: 4,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '6px',
                background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
              }
            }}
          >
            <Alert severity="error" sx={{ mb: 3 }}>
              {error.message || 'Une erreur est survenue lors de la récupération du ticket.'}
            </Alert>
            <Button
              startIcon={<ArrowBackIcon />}
              variant="outlined"
              onClick={() => navigate(isAdminRoute ? '/admin/support/tickets' : '/dashboard/support/tickets')}
              sx={{ 
                color: '#FF6B2C',
                borderColor: '#FF6B2C',
                fontWeight: 500,
                px: 2,
                py: 1.2,
                borderRadius: '8px',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  bgcolor: 'rgba(255, 107, 44, 0.05)',
                },
              }}
            >
              Retour aux tickets
            </Button>
          </Paper>
        </Box>
      </Box>
    );
  }
  
  if (!ticket) {
    return (
      <Box 
        sx={{ 
          minHeight: '100vh',
          background: 'linear-gradient(160deg, #FFF8F3 0%, rgba(255, 255, 255, 0.95) 100%)',
          pt: { xs: 2, sm: 3 },
          pb: { xs: 4, sm: 6 }
        }}
      >
        <Box 
          sx={{ 
            px: { xs: '8px', md: 0 },
            margin: 0,
            width: '100%'
          }}
        >
          <Breadcrumbs 
            aria-label="breadcrumb"
            sx={{ 
              mb: 3, 
              background: 'rgba(255, 255, 255, 0.7)',
              py: 1.5, 
              px: 2, 
              borderRadius: 2,
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}
          >
            <Button
              component={Link}
              to={isAdminRoute ? "/admin" : "/dashboard"}
              size="small"
              startIcon={<HomeIcon />}
              sx={{ 
                color: 'rgba(0, 0, 0, 0.6)',
                '&:hover': { color: '#FF6B2C' },
                textTransform: 'none',
                fontWeight: 'normal'
              }}
            >
              {isAdminRoute ? "Administration" : "Tableau de bord"}
            </Button>
            <Button
              component={Link}
              to={isAdminRoute ? "/admin/support/tickets" : "/dashboard/support/tickets"}
              size="small"
              startIcon={<ListAltIcon />}
              sx={{ 
                color: 'rgba(0, 0, 0, 0.6)',
                '&:hover': { color: '#FF6B2C' },
                textTransform: 'none',
                fontWeight: 'normal'
              }}
            >
              Tickets
            </Button>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
              <SupportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
              Introuvable
            </Typography>
          </Breadcrumbs>

          <Paper 
            elevation={3}
            sx={{
              borderRadius: { xs: '12px', sm: '16px' },
              overflow: 'hidden',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
              position: 'relative',
              p: { xs: 2, sm: 3 },
              mb: 4,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '6px',
                background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
              }
            }}
          >
            <Alert severity="warning" sx={{ mb: 3 }}>
              Ce ticket n'existe pas ou a été supprimé.
            </Alert>
            <Button
              startIcon={<ArrowBackIcon />}
              variant="outlined"
              onClick={() => navigate(isAdminRoute ? '/admin/support/tickets' : '/dashboard/support/tickets')}
              sx={{ 
                color: '#FF6B2C',
                borderColor: '#FF6B2C',
                fontWeight: 500,
                px: 2,
                py: 1.2,
                borderRadius: '8px',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  bgcolor: 'rgba(255, 107, 44, 0.05)',
                },
              }}
            >
              Retour aux tickets
            </Button>
          </Paper>
        </Box>
      </Box>
    );
  }
  
  return (
    <Box 
      sx={{ 
        minHeight: '100vh',
        background: 'linear-gradient(160deg, #FFF8F3 0%, rgba(255, 255, 255, 0.95) 100%)',
        pt: { xs: 2, sm: 3 },
        pb: { xs: 4, sm: 6 }
      }}
    >
      <Box 
        sx={{ 
          px: { xs: '8px', md: 0 },
          margin: 0,
          width: '100%'
        }}
      >
        <Breadcrumbs 
          aria-label="breadcrumb"
          sx={{ 
            mb: 3, 
            background: 'rgba(255, 255, 255, 0.7)',
            py: 1.5, 
            px: 2, 
            borderRadius: 2,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Button
            component={Link}
            to={isAdminRoute ? "/admin" : "/dashboard"}
            size="small"
            startIcon={<HomeIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            {isAdminRoute ? "Administration" : "Tableau de bord"}
          </Button>
          <Button
            component={Link}
            to={isAdminRoute ? "/admin/support/tickets" : "/dashboard/support/tickets"}
            size="small"
            startIcon={<ListAltIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            Tickets
          </Button>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
            <SupportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
            Ticket #{ticket.id?.substring(0, 8)}
          </Typography>
        </Breadcrumbs>

        {/* Titre et boutons d'action */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', md: 'center' }, 
            gap: { xs: 2, md: 0 },
            mb: 4
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box 
              sx={{ 
                backgroundColor: 'rgba(255, 107, 44, 0.1)', 
                borderRadius: '50%',
                p: 1.5,
                display: 'flex',
                mr: 2
              }}
            >
              <SupportIcon 
                sx={{ 
                  color: '#FF6B2C', 
                  fontSize: { xs: '2rem', sm: '2.5rem' },
                  filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
                }} 
              />
            </Box>
            <Box>
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 700, 
                  color: '#333',
                  fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
                  lineHeight: 1.2
                }}
              >
                Détails du ticket
              </Typography>
              <Typography
                variant="subtitle1"
                color="text.secondary"
                sx={{ mt: 0.5, fontWeight: 400 }}
              >
                #{ticket.id?.substring(0, 8)} • Créé le {formatDate(ticket.created_at)}
              </Typography>
            </Box>
          </Box>

          {isStaff && (
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => navigate(isAdminRoute ? `/admin/support/ticket/${ticket.id}/edit` : `/dashboard/support/ticket/${ticket.id}/edit`)}
              sx={{ 
                color: '#FF6B2C',
                borderColor: '#FF6B2C',
                fontWeight: 500,
                px: 2,
                py: 1.2,
                borderRadius: '8px',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  bgcolor: 'rgba(255, 107, 44, 0.05)',
                },
              }}
            >
              Modifier
            </Button>
          )}
        </Box>
        
        {/* Informations du ticket */}
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 8 }}>
            <Paper 
              elevation={3}
              sx={{
                borderRadius: { xs: '12px', sm: '16px' },
                overflow: 'hidden',
                boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
                position: 'relative',
                mb: 3,
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                },
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
                }
              }}
            >
              <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
                <Box sx={{ mb: 3 }}>
                  <Typography 
                    variant="h5" 
                    component="h2" 
                    gutterBottom
                    sx={{ 
                      fontWeight: 600,
                      color: '#333',
                      mb: 2
                    }}
                  >
                    {ticket.title}
                  </Typography>
                  
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      flexWrap: 'wrap',
                      gap: 2, 
                      mb: 3,
                      alignItems: 'center'
                    }}
                  >
                    <Chip
                      icon={<AssignmentIcon style={{ color: 'white' }} />}
                      label={formatTicketStatus(ticket.status)}
                      sx={{
                        bgcolor: getStatusColor(ticket.status),
                        color: 'white',
                        fontWeight: 500,
                        borderRadius: '8px',
                        py: 0.5,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                      }}
                    />
                    <Chip
                      icon={<PriorityHighIcon style={{ color: 'white' }} />}
                      label={formatTicketPriority(ticket.priority)}
                      sx={{
                        bgcolor: getPriorityColor(ticket.priority),
                        color: 'white',
                        fontWeight: 500,
                        borderRadius: '8px',
                        py: 0.5,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                      }}
                    />
                    <Chip
                      avatar={<Avatar>{getCategoryIcon(ticket.category)}</Avatar>}
                      label={ticket.category}
                      variant="outlined"
                      sx={{
                        borderRadius: '8px',
                        py: 0.5,
                        fontWeight: 500,
                        borderColor: 'rgba(0, 0, 0, 0.12)',
                      }}
                    />
                  </Box>
                </Box>
                
                <Divider sx={{ 
                  my: 3, 
                  borderColor: 'rgba(0, 0, 0, 0.08)',
                  '&::before, &::after': {
                    borderColor: 'rgba(0, 0, 0, 0.04)',
                  }
                }} />
                
                <Box>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      fontWeight: 600, 
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      color: '#333'
                    }}
                  >
                    <AssignmentIcon sx={{ mr: 1, color: '#FF6B2C', fontSize: '1.2rem' }} />
                    Description
                  </Typography>
                  <Typography
                    variant="body1"
                    component="div"
                    sx={{
                      whiteSpace: 'pre-wrap',
                      p: 2.5,
                      bgcolor: 'rgba(255, 255, 255, 0.7)',
                      border: '1px solid rgba(0, 0, 0, 0.06)',
                      borderRadius: '12px',
                      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                      color: '#333',
                      lineHeight: 1.6,
                    }}
                  >
                    {ticket.description || "Aucune description fournie."}
                  </Typography>
                </Box>
                
                {ticket.tags && ticket.tags.length > 0 && (
                  <Box mt={4}>
                    <Typography 
                      variant="subtitle1" 
                      sx={{ 
                        fontWeight: 600, 
                        mb: 2,
                        display: 'flex',
                        alignItems: 'center',
                        color: '#333'
                      }}
                    >
                      <LocalOfferIcon sx={{ mr: 1, color: '#FF6B2C', fontSize: '1.2rem' }} />
                      Tags
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      {ticket.tags.map((tag: any) => (
                        <Chip
                          key={tag.id}
                          label={tag.name}
                          sx={{
                            bgcolor: tag.color || '#F0F0F0',
                            color: tag.color ? theme.palette.getContrastText(tag.color) : '#333',
                            fontWeight: 500,
                            borderRadius: '8px',
                            py: 0.5,
                          }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
            </Paper>
            
            {/* Pièces jointes */}
            <TicketAttachmentsList ticketId={ticketId || ''} />
            
            {/* Commentaires */}
            <Paper 
              elevation={3}
              sx={{
                borderRadius: { xs: '12px', sm: '16px' },
                overflow: 'hidden',
                boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                },
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
                }
              }}
            >
              <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
                <CommentsList 
                  ticketId={ticket.id} 
                  readOnly={!isStaff && ticket.status === 'ferme'} 
                  showInternal={isStaff}
                  ticketStatus={ticket.status}
                  ticketCategory={ticket.category}
                />
              </Box>
            </Paper>
          </Grid>
          
          {/* Panneau latéral d'informations */}
          <Grid size={{ xs: 12, md: 4 }}>
            <Paper 
              elevation={3}
              sx={{
                borderRadius: { xs: '12px', sm: '16px' },
                overflow: 'hidden',
                boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
                position: 'relative',
                height: 'fit-content',
                mb: { xs: 3, md: 0 },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: 'linear-gradient(90deg, #FF965E, #FFE4BA)',
                },
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
                }
              }}
            >
              <Box sx={{ p: { xs: 2, sm: 3 } }}>
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 3,
                    pb: 2,
                    borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                  }}
                >
                  <EventNoteIcon sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.5rem' }} />
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontWeight: 600,
                      color: '#333'
                    }}
                  >
                    Informations
                  </Typography>
                </Box>
                
                <Box 
                  sx={{ 
                    mb: 3,
                    pb: 2,
                    borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                  }}
                >
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ mb: 0.5 }}
                  >
                    Créé le
                  </Typography>
                  <Typography 
                    variant="body1"
                    sx={{ 
                      fontSize: '0.95rem',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <EventNoteIcon sx={{ fontSize: '1.1rem', mr: 1, color: '#FF6B2C' }} />
                    {formatDate(ticket.created_at)}
                  </Typography>
                </Box>
                
                {ticket.updated_at && (
                  <Box 
                    sx={{ 
                      mb: 3,
                      pb: 2,
                      borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                    }}
                  >
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ mb: 0.5 }}
                    >
                      Dernière mise à jour
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{ 
                        fontSize: '0.95rem',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <EventNoteIcon sx={{ fontSize: '1.1rem', mr: 1, color: '#FF6B2C' }} />
                      {formatDate(ticket.updated_at)}
                    </Typography>
                  </Box>
                )}
                
                {/* Date d'inscription utilisateur */}
                {ticket.user?.date_inscription && (
                  <Box 
                    sx={{ 
                      mb: 3,
                      pb: 2,
                      borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                    }}
                  >
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ mb: 0.5 }}
                    >
                      Date d'inscription utilisateur
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{ 
                        fontSize: '0.95rem',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <EventNoteIcon sx={{ fontSize: '1.1rem', mr: 1, color: '#FF6B2C' }} />
                      {formatDate(ticket.user.date_inscription)}
                    </Typography>
                  </Box>
                )}
                
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 3,
                    pb: 2,
                    borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                  }}
                >
                  <PersonIcon sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.5rem' }} />
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontWeight: 600,
                      color: '#333'
                    }}
                  >
                    Contacts
                  </Typography>
                </Box>
                
                <Box 
                  sx={{ 
                    mb: 3,
                    pb: 2,
                    borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                  }}
                >
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ mb: 0.5 }}
                  >
                    Demandeur
                  </Typography>
                  <Typography 
                    variant="body1"
                    sx={{ 
                      fontSize: '0.95rem',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <EmailIcon sx={{ fontSize: '1.1rem', mr: 1, color: '#FF6B2C' }} />
                    {ticket.user?.email || 'N/A'}
                  </Typography>
                </Box>
                
                {isStaff && (
                  <Box sx={{ mb: 3 }}>
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ mb: 0.5 }}
                    >
                      Assigné à
                    </Typography>
                    <Typography 
                      variant="body1"
                      sx={{ 
                        fontSize: '0.95rem',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <EmailIcon sx={{ fontSize: '1.1rem', mr: 1, color: '#FF6B2C' }} />
                      {ticket.assigned_user?.email || 'Non assigné'}
                    </Typography>
                  </Box>
                )}
                
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate(isAdminRoute ? '/admin/support/tickets' : '/dashboard/support/tickets')}
                  sx={{ 
                    color: '#FF6B2C',
                    borderColor: '#FF6B2C',
                    fontWeight: 500,
                    mt: 1,
                    py: 1.2,
                    borderRadius: '8px',
                    '&:hover': {
                      borderColor: '#FF6B2C',
                      color: '#FF6B2C',
                      bgcolor: 'rgba(255, 107, 44, 0.05)',
                    },
                  }}
                >
                  Retour aux tickets
                </Button>
                
                {/* Indicateur de temps restant pour modifier le ticket - visible uniquement pour les utilisateurs réguliers */}
                {user?.id === ticket?.user_id && !isStaff && canUserModify() && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mt: 2,
                      bgcolor: getRemainingTime() < 5 ? 'rgba(229, 57, 53, 0.1)' : 'rgba(255, 107, 44, 0.1)',
                      borderRadius: '8px',
                      p: 1.5,
                      border: getRemainingTime() < 5 ? '1px solid rgba(229, 57, 53, 0.3)' : '1px solid rgba(255, 107, 44, 0.2)',
                    }}
                  >
                    <EditCalendarIcon sx={{ 
                      fontSize: '1.25rem', 
                      color: getRemainingTime() < 5 ? '#e53935' : '#FF6B2C', 
                      mr: 1.5 
                    }} />
                    <Box>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: 'medium',
                          color: getRemainingTime() < 5 ? '#e53935' : '#FF6B2C',
                        }}
                      >
                        Temps restant pour modifier votre ticket : {getRemainingTime()} minutes
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block', mt: 0.5 }}>
                        Les utilisateurs peuvent modifier ou supprimer leurs tickets uniquement dans les 30 minutes suivant leur création.
                      </Typography>
                    </Box>
                  </Box>
                )}
                
                {/* Bouton de modification */}
                {((user?.id === ticket?.user_id && canUserModify()) || isStaff) && (
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => navigate(isAdminRoute ? `/admin/support/ticket/${ticketId}/edit` : `/dashboard/support/ticket/${ticketId}/edit`)}
                    sx={{
                      bgcolor: '#FF6B2C',
                      color: 'white',
                      fontWeight: 500,
                      mt: 2,
                      py: 1.2,
                      borderRadius: '8px',
                      '&:hover': {
                        bgcolor: '#FF7A35',
                        boxShadow: '0 6px 15px rgba(255, 107, 44, 0.3)',
                      },
                      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.2)',
                    }}
                  >
                    Modifier le ticket
                  </Button>
                )}

                {/* Bouton de suppression - visible uniquement pour les propriétaires (durant 30min) ou le staff */}
                {((user?.id === ticket?.user_id && canUserModify() && !isStaff) || isStaff) && (
                  <Button
                    fullWidth
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleDeleteClick}
                    sx={{
                      borderColor: '#e74c3c',
                      color: '#e74c3c',
                      fontWeight: 500,
                      mt: 2,
                      py: 1.2,
                      borderRadius: '8px',
                      '&:hover': {
                        bgcolor: 'rgba(231, 76, 60, 0.05)',
                        borderColor: '#c0392b',
                      },
                    }}
                  >
                    Supprimer le ticket
                  </Button>
                )}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Modale de confirmation de suppression */}
      <ModalPortal isOpen={showDeleteConfirmation} onBackdropClick={cancelDelete}>
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          style={{ 
            width: '100%', 
            maxWidth: '450px', 
            margin: '0 20px',
            position: 'relative',
            zIndex: 1500 
          }}
        >
          <Paper
            elevation={8}
            sx={{
              borderRadius: '16px',
              overflow: 'hidden',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.15)'
            }}
          >
            <Box
              sx={{
                bgcolor: '#e74c3c',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DeleteIcon sx={{ mr: 1.5 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Confirmation de suppression
                </Typography>
              </Box>
              <IconButton onClick={cancelDelete} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </Box>
            
            <Box sx={{ p: 3 }}>
              <Typography variant="body1" sx={{ mb: 3, color: '#333' }}>
                Êtes-vous sûr de vouloir supprimer ce ticket ? 
                <Typography component="span" sx={{ display: 'block', mt: 1, fontWeight: 'bold', color: '#e74c3c' }}>
                  Cette action est irréversible.
                </Typography>
              </Typography>
              
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={cancelDelete}
                  sx={{
                    borderColor: 'rgba(0, 0, 0, 0.23)',
                    color: 'text.primary',
                    '&:hover': {
                      borderColor: 'rgba(0, 0, 0, 0.5)',
                      bgcolor: 'rgba(0, 0, 0, 0.03)'
                    },
                    borderRadius: '8px',
                    px: 3
                  }}
                >
                  Annuler
                </Button>
                <Button
                  variant="contained"
                  onClick={confirmDelete}
                  sx={{
                    bgcolor: '#e74c3c',
                    '&:hover': {
                      bgcolor: '#c0392b'
                    },
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(231, 76, 60, 0.2)',
                    px: 3
                  }}
                >
                  Supprimer
                </Button>
              </Box>
            </Box>
          </Paper>
        </motion.div>
      </ModalPortal>
    </Box>
  );
};

export default TicketDetails; 