import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { z } from 'zod';
import { redis } from '../config/redis';
import { encryptCompanyDataAsync, decryptCompanyDataAsync } from '../utils/encryption';

// Constantes pour le cache
const COMPANY_SETTINGS_CACHE_PREFIX = 'company_settings_controller:';
const CACHE_TTL = 180; // 3 minutes en secondes

// Schéma de validation pour les paramètres de l'entreprise
const companySettingsSchema = z.object({
  nom: z.string().min(1, 'Le nom de l\'entreprise est requis'),
  adresse: z.string().optional(),
  code_postal: z.string().optional(),
  ville: z.string().optional(),
  pays: z.string().optional(),
  telephone: z.string().optional(),
  email: z.string().email('Format email invalide').optional(),
  site_web: z.string().optional(),
  siret: z.string().optional(),
  tva: z.string().optional(),
  forme_juridique: z.string().optional(),
  code_ape: z.string().optional(),
  rcs: z.string().optional(),
  capital: z.string().optional(),
  mention_pied_page: z.string().optional(),
});

export const companySettingsController = {
  // Récupérer les paramètres de l'entreprise
  getCompanySettings: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ success: false, message: 'Non autorisé' });
      }

      // Vérifier si les données sont en cache
      const cacheKey = `${COMPANY_SETTINGS_CACHE_PREFIX}${userId}`;
      const cachedSettings = await redis.get(cacheKey);
      
      if (cachedSettings) {
        return res.json({ success: true, data: JSON.parse(cachedSettings) || null });
      }

      const { data: settings, error } = await supabase
        .from('invoices_company_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = No rows found
        logger.error('Erreur lors de la récupération des paramètres d\'entreprise:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des paramètres d\'entreprise'
        });
      }

      // Déchiffrer les données si elles existent
      const decryptedSettings = settings ? await decryptCompanyDataAsync(settings) : null;

      // Mettre en cache les données déchiffrées pour 3 minutes
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(decryptedSettings));

      return res.json({
        success: true,
        data: decryptedSettings
      });
    } catch (error) {
      logger.error('Erreur serveur lors de la récupération des paramètres d\'entreprise:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur serveur'
      });
    }
  },

  // Mettre à jour ou créer les paramètres de l'entreprise
  updateCompanySettings: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({ success: false, message: 'Non autorisé' });
      }

      // Valider les données
      const validationResult = companySettingsSchema.safeParse(req.body);
      
      if (!validationResult.success) {
        const errorMessage = validationResult.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
        return res.status(400).json({ 
          success: false, 
          message: 'Données invalides', 
          errors: errorMessage 
        });
      }

      const settingsData = validationResult.data;
      
      // Vérifier si les paramètres existent déjà
      const { data: existingSettings, error: fetchError } = await supabase
        .from('invoices_company_settings')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        logger.error('Erreur lors de la récupération des paramètres existants:', fetchError);
        return res.status(500).json({ 
          success: false, 
          message: 'Erreur lors de la récupération des paramètres existants' 
        });
      }

      let result;
      if (existingSettings) {
        // Chiffrer les données avant mise à jour
        const encryptedUpdateData = await encryptCompanyDataAsync({
          ...settingsData,
          updated_at: new Date()
        });

        // Mettre à jour les paramètres existants
        result = await supabase
          .from('invoices_company_settings')
          .update(encryptedUpdateData)
          .eq('id', existingSettings.id)
          .select()
          .single();
      } else {
        // Chiffrer les données avant insertion
        const encryptedInsertData = await encryptCompanyDataAsync({
          user_id: userId,
          ...settingsData
        });

        // Créer de nouveaux paramètres
        result = await supabase
          .from('invoices_company_settings')
          .insert([encryptedInsertData])
          .select()
          .single();
      }

      if (result.error) {
        logger.error('Erreur lors de la mise à jour des paramètres d\'entreprise:', result.error);
        return res.status(500).json({ 
          success: false, 
          message: 'Erreur lors de la mise à jour des paramètres d\'entreprise' 
        });
      }

      // Déchiffrer les données avant de les retourner
      const decryptedResult = result.data ? await decryptCompanyDataAsync(result.data) : null;

      // Invalider le cache pour ce user
      const cacheKey = `${COMPANY_SETTINGS_CACHE_PREFIX}${userId}`;
      await redis.del(cacheKey);

      return res.json({
        success: true,
        data: decryptedResult
      });
    } catch (error) {
      logger.error('Erreur serveur lors de la mise à jour des paramètres d\'entreprise:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur serveur' 
      });
    }
  }
}; 