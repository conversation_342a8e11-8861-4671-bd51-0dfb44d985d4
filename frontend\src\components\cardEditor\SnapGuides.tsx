import React from 'react';
import { Line, Group } from 'react-konva';
import { CardElement } from '../../types/cardEditor';

// Définition des interfaces
interface SnapGuidesProps {
  width: number;
  height: number;
  guides: GuideInfo[];
}

export interface GuideInfo {
  lineGuide: number;
  offset: number;
  orientation: 'H' | 'V';
}

// Fonction utilitaire pour obtenir les points d'arrêt des lignes guides
export const getLineGuideStops = (
  elements: CardElement[],
  canvasWidth: number,
  canvasHeight: number
): { vertical: number[]; horizontal: number[] } => {
  // Points d'arrêt pour les bords et le centre du canvas
  // Nous incluons seulement les bords et le centre du canvas
  const vertical: number[] = [0, canvasWidth / 2, canvasWidth];
  const horizontal: number[] = [0, canvasHeight / 2, canvasHeight];

  // Ajouter les bords et centres de chaque élément
  elements.forEach((element) => {
    const width = element.width || 0;
    const height = element.height || 0;

    // Points verticaux (x) - seulement les bords et le centre
    vertical.push(element.x);
    vertical.push(element.x + width);
    vertical.push(element.x + width / 2);

    // Points horizontaux (y) - seulement les bords et le centre
    horizontal.push(element.y);
    horizontal.push(element.y + height);
    horizontal.push(element.y + height / 2);
  });

  // Filtrer les doublons et trier
  const filteredVertical = vertical.filter((v, i, a) => a.indexOf(v) === i).sort((a, b) => a - b);
  const filteredHorizontal = horizontal.filter((v, i, a) => a.indexOf(v) === i).sort((a, b) => a - b);

  return {
    vertical: filteredVertical,
    horizontal: filteredHorizontal
  };
};

// Fonction pour obtenir les bords d'un élément pour le snap
export const getObjectSnappingEdges = (element: CardElement): {
  vertical: number[];
  horizontal: number[];
} => {
  const width = element.width || 0;
  const height = element.height || 0;

  return {
    vertical: [
      element.x,
      element.x + width / 2,
      element.x + width
    ],
    horizontal: [
      element.y,
      element.y + height / 2,
      element.y + height
    ]
  };
};

// Fonction pour trouver les guides d'alignement
export const getGuides = (
  lineGuideStops: { vertical: number[]; horizontal: number[] },
  itemBounds: { vertical: number[]; horizontal: number[] },
  threshold: number = 5
): GuideInfo[] => {
  const guides: GuideInfo[] = [];

  // Trouver le meilleur guide vertical (celui avec la plus petite différence)
  let minVerticalDiff = threshold;
  let bestVerticalGuide: GuideInfo | null = null;

  lineGuideStops.vertical.forEach((lineGuide) => {
    itemBounds.vertical.forEach((itemBound) => {
      const diff = Math.abs(lineGuide - itemBound);
      if (diff < minVerticalDiff) {
        minVerticalDiff = diff;
        bestVerticalGuide = {
          lineGuide,
          offset: lineGuide - itemBound,
          orientation: 'V'
        };
      }
    });
  });

  if (bestVerticalGuide) {
    guides.push(bestVerticalGuide);
  }

  // Trouver le meilleur guide horizontal (celui avec la plus petite différence)
  let minHorizontalDiff = threshold;
  let bestHorizontalGuide: GuideInfo | null = null;

  lineGuideStops.horizontal.forEach((lineGuide) => {
    itemBounds.horizontal.forEach((itemBound) => {
      const diff = Math.abs(lineGuide - itemBound);
      if (diff < minHorizontalDiff) {
        minHorizontalDiff = diff;
        bestHorizontalGuide = {
          lineGuide,
          offset: lineGuide - itemBound,
          orientation: 'H'
        };
      }
    });
  });

  if (bestHorizontalGuide) {
    guides.push(bestHorizontalGuide);
  }

  return guides;
};

// Fonction pour dessiner les guides
export const drawGuides = (
  guides: GuideInfo[],
  width: number,
  height: number
): React.ReactNode[] => {
  return guides.map((guide, i) => {
    if (guide.orientation === 'H') {
      return (
        <Line
          key={`h-${i}`}
          points={[0, guide.lineGuide, width, guide.lineGuide]}
          stroke="#FF6B2C"
          strokeWidth={1}
          dash={[4, 4]}
          perfectDrawEnabled={false}
          listening={false}
          name="guid-line"
        />
      );
    } else {
      return (
        <Line
          key={`v-${i}`}
          points={[guide.lineGuide, 0, guide.lineGuide, height]}
          stroke="#FF6B2C"
          strokeWidth={1}
          dash={[4, 4]}
          perfectDrawEnabled={false}
          listening={false}
          name="guid-line"
        />
      );
    }
  });
};

// Composant pour afficher les guides de snap
const SnapGuides: React.FC<SnapGuidesProps> = ({ width, height, guides }) => {
  return (
    <Group>
      {drawGuides(guides, width, height)}
    </Group>
  );
};

export default SnapGuides;
