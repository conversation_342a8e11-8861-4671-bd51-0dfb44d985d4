import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getCommonHeaders } from '../../utils/headers';
import { API_CONFIG } from '../../config/api';
import { 
  Box, 
  Typography, 
  Button, 
  TextField, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Switch,
  FormControlLabel,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon, 
  Edit as EditIcon
} from '@mui/icons-material';
import { notify } from '../../components/Notification';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { SelectChangeEvent } from '@mui/material/Select';
import { useAuth } from '../../contexts/AuthContext';
import { fetchCsrfToken } from '../../services/csrf';

const PromoCodesPage: React.FC = () => {
  const { user } = useAuth();
  const [promoCodes, setPromoCodes] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [editingCode, setEditingCode] = useState<any>(null);
  
  // État du formulaire
  const [formData, setFormData] = useState({
    code: '',
    discount_type: 'percentage',
    discount_value: 10,
    max_uses: 100,
    expires_at: '',
    plan_type: '',
    description: '',
    duration_type: 'one_time',
    limit_first_users: 0,
    is_active: true
  });

  // Charger les codes promo
  const fetchPromoCodes = async () => {
    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/promocodes/admin`, {
        headers,
        withCredentials: true
      });
      
      if (response.data.success) {
        setPromoCodes(response.data.data || []);
      } else {
        notify(response.data.message || "Erreur lors du chargement des codes promo", "error");
      }
    } catch (error) {
      console.error("Erreur lors du chargement des codes promo:", error);
      notify("Impossible de charger les codes promo", "error");
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au démarrage
  useEffect(() => {
    fetchPromoCodes();
  }, []);

  // Gérer les changements de champ
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (!name) return;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'code' ? value.toUpperCase() : value
    }));
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    if (!name) return;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Gérer les toggles pour les champs booléens
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Ouvrir le dialogue pour créer un nouveau code
  const handleOpenCreate = () => {
    setEditingCode(null);
    setFormData({
      code: '',
      discount_type: 'percentage',
      discount_value: 10,
      max_uses: 100,
      expires_at: '',
      plan_type: '',
      description: '',
      duration_type: 'one_time',
      limit_first_users: 0,
      is_active: true
    });
    setOpenDialog(true);
  };

  // Ouvrir le dialogue pour éditer un code existant
  const handleOpenEdit = (code: any) => {
    setEditingCode(code);
    setFormData({
      code: code.code,
      discount_type: code.discount_type,
      discount_value: code.discount_value,
      max_uses: code.max_uses || 100,
      expires_at: code.expires_at ? new Date(code.expires_at).toISOString().split('T')[0] : '',
      plan_type: code.plan_type || '',
      description: code.description || '',
      duration_type: code.duration_type,
      limit_first_users: code.limit_first_users || 0,
      is_active: code.is_active
    });
    setOpenDialog(true);
  };

  // Fermer le dialogue
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Formater la date pour affichage
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: fr });
    } catch (error) {
      return 'Date invalide';
    }
  };

  // Créer ou mettre à jour un code promo
  const handleSavePromoCode = async () => {
    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      let response;
      
      if (editingCode) {
        // Mise à jour d'un code existant
        response = await axios.put(
          `${API_CONFIG.baseURL}/api/promocodes/admin/${editingCode.id}`, 
          formData,
          {
            headers: {
              ...headers,
              'Content-Type': 'application/json'
            },
            withCredentials: true
          }
        );
      } else {
        // Création d'un nouveau code
        response = await axios.post(
          `${API_CONFIG.baseURL}/api/promocodes/admin`, 
          formData,
          {
            headers: {
              ...headers,
              'Content-Type': 'application/json',
              'X-CSRF-Token': await fetchCsrfToken()
            },
            withCredentials: true
          }
        );
      }
      
      if (response.data.success) {
        notify(editingCode ? "Code promo mis à jour" : "Code promo créé", "success");
        setOpenDialog(false);
        fetchPromoCodes();
      } else {
        notify(response.data.message || "Erreur lors de l'opération", "error");
      }
    } catch (error: any) {
      console.error("Erreur lors de l'enregistrement du code promo:", error);
      notify(error.response?.data?.message || "Erreur lors de l'enregistrement", "error");
    } finally {
      setLoading(false);
    }
  };

  // Supprimer un code promo
  const handleDeletePromoCode = async (id: string) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce code promo?")) {
      return;
    }
    
    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/promocodes/admin/${id}`,
        {
          headers,
          withCredentials: true
        }
      );
      
      if (response.data.success) {
        notify("Code promo supprimé", "success");
        fetchPromoCodes();
      } else {
        notify(response.data.message || "Erreur lors de la suppression", "error");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression du code promo:", error);
      notify("Impossible de supprimer le code promo", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: { xs: 1, md: 4 }, maxWidth: 1200, mx: 'auto' }}>
      {/* Header modernisé, orange uniquement sur le titre */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" fontWeight="bold" color="#FF7A35" sx={{ mb: 0.5 }}>
            Gestion des codes promo
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Créez, modifiez et suivez l'utilisation de vos codes promotionnels.
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          sx={{
            bgcolor: '#FF7A35',
            color: 'white',
            fontWeight: 'bold',
            borderRadius: 2,
            px: 3,
            py: 1.2,
            boxShadow: '0 4px 12px rgba(255, 122, 53, 0.10)',
            '&:hover': { bgcolor: '#E16B28' }
          }}
          startIcon={<AddIcon />}
          onClick={handleOpenCreate}
        >
          Nouveau code
        </Button>
      </Box>
      {/* Tableau modernisé, fond blanc, header gris clair, orange uniquement sur accents */}
      <TableContainer component={Paper} sx={{ borderRadius: 3, boxShadow: '0 4px 16px rgba(0,0,0,0.06)', background: 'white' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ background: '#F7F7F7' }}>
              <TableCell sx={{ color: '#FF7A35', fontWeight: 'bold' }}>Code</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Type</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Valeur</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Utilisations</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Expiration</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Plan</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Statut</TableCell>
              <TableCell sx={{ color: 'text.primary', fontWeight: 'bold' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {promoCodes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  Aucun code promo trouvé
                </TableCell>
              </TableRow>
            ) : (
              promoCodes.map((code) => (
                <TableRow key={code.id} sx={{ '&:hover': { background: '#FAFAFA' } }}>
                  <TableCell sx={{ fontWeight: 700, color: '#FF7A35' }}>{code.code}</TableCell>
                  <TableCell>{code.discount_type === 'percentage' ? 'Pourcentage' : 'Montant fixe'}</TableCell>
                  <TableCell>
                    <Box sx={{ fontWeight: 'bold', color: '#FF7A35' }}>
                      {code.discount_value}{code.discount_type === 'percentage' ? '%' : '€'}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ fontWeight: 'bold', color: (code.max_uses - code.remaining_uses) > 0 ? '#FF7A35' : 'text.secondary' }}>
                      {code.remaining_uses !== null && code.max_uses !== null
                        ? `${code.max_uses - code.remaining_uses}/${code.max_uses}`
                        : 'Illimité'
                      }
                    </Box>
                  </TableCell>
                  <TableCell>{code.expires_at ? formatDate(code.expires_at) : <span style={{ color: '#9e9e9e' }}>Pas de date</span>}</TableCell>
                  <TableCell>{code.plan_type || <span style={{ color: '#9e9e9e' }}>Tous</span>}</TableCell>
                  <TableCell>
                    {code.is_active ? (
                      <Typography sx={{ color: '#43a047', fontWeight: 'bold' }}>Actif</Typography>
                    ) : (
                      <Typography sx={{ color: '#e53935', fontWeight: 'bold' }}>Inactif</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Modifier">
                        <IconButton onClick={() => handleOpenEdit(code)} sx={{ color: '#FF7A35' }}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      {user?.role === 'jobpadm' && (
                        <Tooltip title="Supprimer">
                          <IconButton onClick={() => handleDeletePromoCode(code.id)} color="error">
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {/* Dialogue pour créer/éditer un code promo modernisé, fond blanc, champs gris clair */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ fontWeight: 'bold', color: '#FF7A35', fontSize: '1.5rem', pb: 0 }}>
          {editingCode ? 'Modifier le code promo' : 'Créer un nouveau code promo'}
        </DialogTitle>
        <DialogContent sx={{ pt: 2, background: 'white' }}>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                name="code"
                label="Code promo"
                fullWidth
                value={formData.code}
                onChange={handleInputChange}
                helperText="Laissez vide pour générer automatiquement"
                inputProps={{ style: { textTransform: 'uppercase' } }}
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <FormControl fullWidth sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}>
                <InputLabel id="discount-type-label">Type de réduction</InputLabel>
                <Select
                  labelId="discount-type-label"
                  name="discount_type"
                  value={formData.discount_type}
                  onChange={handleSelectChange}
                  label="Type de réduction"
                >
                  <MenuItem value="percentage">Pourcentage (%)</MenuItem>
                  <MenuItem value="fixed">Montant fixe (€)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                name="discount_value"
                label={`Valeur (${formData.discount_type === 'percentage' ? '%' : '€'})`}
                fullWidth
                type="number"
                value={formData.discount_value}
                onChange={handleInputChange}
                inputProps={{ min: 0, max: formData.discount_type === 'percentage' ? 100 : 10000 }}
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <FormControl fullWidth sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}>
                <InputLabel id="duration-type-label">Durée</InputLabel>
                <Select
                  labelId="duration-type-label"
                  name="duration_type"
                  value={formData.duration_type}
                  onChange={handleSelectChange}
                  label="Durée"
                >
                  <MenuItem value="one_time">Usage unique</MenuItem>
                  <MenuItem value="lifetime">À vie (récurrent)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                name="max_uses"
                label="Nombre maximal d'utilisations"
                fullWidth
                type="number"
                value={formData.max_uses}
                onChange={handleInputChange}
                inputProps={{ min: 0 }}
                helperText="0 pour illimité"
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
                disabled={!!editingCode}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                name="limit_first_users"
                label="Limiter aux premiers utilisateurs"
                fullWidth
                type="number"
                value={formData.limit_first_users}
                onChange={handleInputChange}
                inputProps={{ min: 0 }}
                helperText="0 pour désactiver cette limite"
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                name="expires_at"
                label="Date d'expiration"
                type="date"
                fullWidth
                value={formData.expires_at}
                onChange={handleInputChange}
                InputLabelProps={{ shrink: true }}
                helperText="Laissez vide pour ne pas définir de date d'expiration"
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <FormControl fullWidth sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}>
                <InputLabel id="plan-type-label">Plan applicable</InputLabel>
                <Select
                  labelId="plan-type-label"
                  name="plan_type"
                  value={formData.plan_type}
                  onChange={handleSelectChange}
                  label="Plan applicable"
                >
                  <MenuItem value="">Tous les plans</MenuItem>
                  <MenuItem value="premium">Premium uniquement</MenuItem>
                  <MenuItem value="gratuit">Gratuit uniquement</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={12}>
              <TextField
                name="description"
                label="Description"
                fullWidth
                multiline
                rows={2}
                value={formData.description}
                onChange={handleInputChange}
                helperText="Description du code promo (visible par les utilisateurs)"
                sx={{ bgcolor: '#F7F7F7', borderRadius: 2 }}
              />
            </Grid>
            <Grid size={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={handleSwitchChange}
                    name="is_active"
                    color="primary"
                  />
                }
                label="Code actif"
                sx={{ ml: 1 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2, pb: 3, background: 'white' }}>
          <Button onClick={handleCloseDialog} color="inherit" sx={{ borderRadius: 2, px: 3 }}>
            Annuler
          </Button>
          <Button 
            onClick={handleSavePromoCode} 
            sx={{
              bgcolor: '#FF7A35',
              color: 'white',
              fontWeight: 'bold',
              borderRadius: 2,
              px: 3,
              boxShadow: '0 2px 8px rgba(255, 122, 53, 0.10)',
              '&:hover': { bgcolor: '#E16B28' }
            }}
            variant="contained"
            disabled={loading}
          >
            {editingCode ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PromoCodesPage; 