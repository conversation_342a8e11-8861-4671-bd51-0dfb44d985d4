import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Slider,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Drawer,
  IconButton,
  Fab,
  useMediaQuery,
  Tooltip,
  Tabs,
  Tab,
  Checkbox,
  InputBase,
  Menu,
  GlobalStyles
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Save,
  Tune,
  ZoomIn,
  ZoomOut,
  GridOn,
  GridOff,
  Straighten,
  GridViewRounded,
  Delete,
  FileCopy,
  ArrowUpward,
  ArrowDownward,
  Visibility,
  VisibilityOff,
  Close
} from '@mui/icons-material';
import EditorThemeProvider from '../../components/cardEditor/EditorTheme';
import CardEditorCanvas from '../../components/cardEditor/CardEditorCanvas';
import PropertiesPanel from '../../components/cardEditor/PropertiesPanel';
import Toolbar from '../../components/cardEditor/Toolbar';
import ExportModal from '../../components/cardEditor/ExportModal';
import DrawingControls from '../../components/cardEditor/DrawingControls';
import {
  CardElement,
  CardTemplateData,
  CardTemplateType,
  TextElement,
  ImageElement,
  ShapeElement,
  DrawingElement,
  QRCodeElement
} from '../../types/cardEditor';
import cardEditorService from '../../services/cardEditorService';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { notify } from '../../components/Notification';
import AiConsentModal from '../../components/ai/AiConsentModal';
import logger from '../../utils/logger';
import ModalPortal from '../../components/ModalPortal';
import useUserProfile from '../../hooks/useUserProfileCarteVisite';

const CardEditorPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const canvasRef = useRef<any>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Utiliser le hook pour obtenir les données du profil, y compris l'URL de l'avatar
  const { profileData } = useUserProfile();

  // Détecter si on est en mode génération IA
  const isGenerateMode = new URLSearchParams(location.search).get('generate') === 'true';

  // États pour le template
  const [templateName, setTemplateName] = useState<string>('Nouveau template');
  const [templateType, setTemplateType] = useState<CardTemplateType>('business_card');
  const [templateData, setTemplateData] = useState<CardTemplateData>({
    width: 350,
    height: 200,
    background_color: '#FFFFFF',
    elements: []
  });
  const [selectedElement, setSelectedElement] = useState<CardElement | null>(null);
  const [zoom, setZoom] = useState<number>(1.4);
  const [showGrid, setShowGrid] = useState<boolean>(true);
  const [showGuides, setShowGuides] = useState<boolean>(true);
  const [snapEnabled, setSnapEnabled] = useState<boolean>(false);
  const [isDrawingMode, setIsDrawingMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // États pour l'interface utilisateur responsive
  const [propertiesPanelOpen, setPropertiesPanelOpen] = useState<boolean>(!isMobile);

  // AMÉLIORATION: États pour les panneaux mobiles
  const [mobilePropertiesDrawerOpen, setMobilePropertiesDrawerOpen] = useState<boolean>(false);
  const [mobileDrawingControlsOpen, setMobileDrawingControlsOpen] = useState<boolean>(false);

  // AMÉLIORATION: États pour les outils de dessin
  const [drawingTool, setDrawingTool] = useState<'brush' | 'eraser' | 'polygon'>('brush');
  const [strokeColor, setStrokeColor] = useState<string>('#FF6B2C');
  const [strokeWidth, setStrokeWidth] = useState<number>(5);

  // États pour l'historique (undo/redo)
  const [history, setHistory] = useState<CardTemplateData[]>([]);
  const [historyIndex, setHistoryIndex] = useState<number>(-1);
  const MAX_HISTORY_LENGTH = 50; // Limite de l'historique pour éviter les problèmes de mémoire

  // États pour les modales
  const [saveDialogOpen, setSaveDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [aiConsentModalOpen, setAiConsentModalOpen] = useState<boolean>(false);
  const [showElementsListModal, setShowElementsListModal] = useState<boolean>(false);

  // État pour la modale BETA (doit être ici avec les autres hooks)
  const [showBetaModal, setShowBetaModal] = useState<boolean>(true);

  // --- Sauvegarde automatique ---
  const [autoSaveEnabled, setAutoSaveEnabled] = useState<boolean>(false); // Initialisé à false
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
  const autoSaveTimeout = useRef<NodeJS.Timeout | null>(null);

  // Détecter les modifications pour activer la sauvegarde automatique
  useEffect(() => {
    if (historyIndex !== -1) {
      setHasUnsavedChanges(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateData, templateName, templateType]);

  // Nouvelle logique de sauvegarde automatique avec debounce
  useEffect(() => {
    // Ajoute cette condition pour vérifier si des éléments existent avant de sauvegarder
    if (!autoSaveEnabled || !hasUnsavedChanges || templateData.elements.length === 0) return;

    if (autoSaveTimeout.current) {
      clearTimeout(autoSaveTimeout.current);
    }
    autoSaveTimeout.current = setTimeout(() => {
      handleSave(false);
      setHasUnsavedChanges(false);
    }, 5000); // 5 secondes après la dernière modif
    return () => {
      if (autoSaveTimeout.current) {
        clearTimeout(autoSaveTimeout.current);
      }
    };
  }, [templateData, templateName, templateType, autoSaveEnabled, hasUnsavedChanges]);

  // Charger un template existant si un ID est fourni
  useEffect(() => {
    if (id) {
      loadTemplate(id);
    } else {
      // Lire le type depuis la query string
      const params = new URLSearchParams(location.search);
      const typeParam = params.get('type');
      // const validType = typeParam === 'flyer' ? 'flyer' : 'business_card'; // Ancienne logique
      let initialType: CardTemplateType = 'business_card'; // Type par défaut

      if (typeParam === 'business_card') {
        initialType = 'business_card';
      } else if (typeParam === 'business_card_landscape') {
        initialType = 'business_card_landscape';
      } else if (typeParam === 'flyer') {
        initialType = 'flyer';
      } else if (typeParam === 'flyer_landscape') {
        initialType = 'flyer_landscape';
      }

      initializeTemplate(initialType);
    }
  }, [id, location.search]);

  // Ajouter l'état actuel à l'historique lorsque templateData change
  useEffect(() => {
    if (templateData && Object.keys(templateData).length > 0) {
      // Ne pas ajouter à l'historique lors du chargement initial
      if (historyIndex === -1) {
        setHistory([templateData]);
        setHistoryIndex(0);
      }
    }
  }, []);

  // Fonction utilitaire pour ajouter un état à l'historique
  const addToHistory = (newState: CardTemplateData) => {
    // Supprimer les états futurs si on a fait un undo puis une modification
    const newHistory = history.slice(0, historyIndex + 1);

    // Ajouter le nouvel état
    newHistory.push(newState);

    // Limiter la taille de l'historique
    if (newHistory.length > MAX_HISTORY_LENGTH) {
      newHistory.shift(); // Supprimer l'état le plus ancien
    }

    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Charger un template existant
  const loadTemplate = async (templateId: string) => {
    setIsLoading(true);
    try {
      const template = await cardEditorService.getTemplateById(templateId);
      if (template) {
        setTemplateName(template.name);
        setTemplateType(template.type);
        setTemplateData(template.template_data);

        // Masquer la grille et les guides si le template contient des éléments
        if (template.template_data.elements && template.template_data.elements.length > 0) {
          setShowGrid(false);
          setShowGuides(false);
        }

        // Réinitialiser l'historique
        setHistory([template.template_data]);
        setHistoryIndex(0);
      } else {
        notify('Template non trouvé', 'error');
        navigate('/dashboard/card-editor');
      }
    } catch (error) {
      logger.info('Erreur lors du chargement du template:', error);
      notify('Erreur lors du chargement du template', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialiser un nouveau template
  const initializeTemplate = (type: CardTemplateType) => {
    let newTemplateData: CardTemplateData;

    if (type === 'business_card') {
      newTemplateData = {
        width: 200, // Correction : dimensions portrait
        height: 350, // Correction : dimensions portrait
        background_color: '#FFFFFF',
        elements: []
      };
      setTemplateName('Nouvelle carte de visite');
    } else if (type === 'business_card_landscape') {
      newTemplateData = {
        width: 350, // Correction : dimensions paysage
        height: 200, // Correction : dimensions paysage
        background_color: '#FFFFFF',
        elements: []
      };
      setTemplateName('Nouvelle carte de visite (paysage)');
    } else if (type === 'flyer') {
      newTemplateData = {
        width: 595,
        height: 842,
        background_color: '#FFFFFF',
        elements: []
      };
      setTemplateName('Nouveau flyer');
    } else { // flyer_landscape
      newTemplateData = {
        width: 842,
        height: 595,
        background_color: '#FFFFFF',
        elements: []
      };
      setTemplateName('Nouveau flyer (paysage)');
    }

    setTemplateType(type);
    setTemplateData(newTemplateData);

    // Réinitialiser l'historique
    setHistory([newTemplateData]);
    setHistoryIndex(0);
  };

  // Ajouter un élément au template
  const handleAddElement = (element: CardElement, userAvatarUrl?: string | null) => {
    // Compter le nombre d'éléments de type 'image'
    const imageElementsCount = templateData.elements.filter(el => el.type === 'image').length;

    // Vérifier si l'élément ajouté est une image et si la limite est atteinte
    if (element.type === 'image' && imageElementsCount >= 15) {
      notify('Vous avez atteint la limite de 15 images par carte/flyer.', 'warning');
      return; // Ne pas ajouter l'élément si la limite est atteinte
    }

    let elementToAdd = element;
    if (element.type === 'image' && userAvatarUrl) {
      elementToAdd = {
        ...element,
        properties: {
          ...(element as ImageElement).properties,
          src: userAvatarUrl
        }
      };
    }

    const updatedTemplateData = {
      ...templateData,
      elements: [elementToAdd, ...templateData.elements] // Ajout en tête de liste
    };

    setTemplateData(updatedTemplateData);
    setSelectedElement(elementToAdd); // Sélectionner l'élément potentiellement modifié
    setSidePanelTab('properties'); // Aller directement sur l'onglet propriétés

    // Ajouter à l'historique
    addToHistory(updatedTemplateData);

    // Activer la sauvegarde automatique si c'est le premier élément ajouté
    if (templateData.elements.length === 0) {
      setAutoSaveEnabled(true);
    }
  };

  // Mettre à jour un élément
  const handleElementUpdate = (updatedElement: CardElement) => {
    const updatedElements = templateData.elements.map(element =>
      element.id === updatedElement.id ? updatedElement : element
    );

    const updatedTemplateData = {
      ...templateData,
      elements: updatedElements
    };

    setTemplateData(updatedTemplateData);

    // Mettre à jour l'élément sélectionné avec la nouvelle référence
    setSelectedElement(updatedElements.find(e => e.id === updatedElement.id) || updatedElement);

    // Ajouter à l'historique seulement si c'est une action significative
    if (!updatedElement.isDragging) {
      addToHistory(updatedTemplateData);
    }
  };

  // Supprimer un élément
  const handleElementDelete = (elementId: string) => {
    const updatedElements = templateData.elements.filter(element =>
      element.id !== elementId
    );

    const updatedTemplateData = {
      ...templateData,
      elements: updatedElements
    };

    setTemplateData(updatedTemplateData);
    setSelectedElement(null);

    // Ajouter à l'historique
    addToHistory(updatedTemplateData);
  };

  // Annuler (undo)
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setTemplateData(history[historyIndex - 1]);
      setSelectedElement(null);
    }
  };

  // Rétablir (redo)
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setTemplateData(history[historyIndex + 1]);
      setSelectedElement(null);
    }
  };

  // Ajouter des raccourcis clavier pour Undo/Redo
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ignorer les événements si un champ de texte est actif
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ctrl+Z ou Cmd+Z pour Undo
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        if (historyIndex > 0) {
          handleUndo();
        }
      }

      // Ctrl+Y ou Cmd+Shift+Z pour Redo
      if (((e.ctrlKey || e.metaKey) && e.key === 'y') ||
          ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z')) {
        e.preventDefault();
        if (historyIndex < history.length - 1) {
          handleRedo();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [historyIndex, history.length]);

  // Effacer tout
  const handleClear = () => {
    const clearedTemplateData = {
      ...templateData,
      elements: []
    };

    setTemplateData(clearedTemplateData);
    setSelectedElement(null);

    // Ajouter à l'historique
    addToHistory(clearedTemplateData);
  };

 // Sauvegarder le template
  const handleSave = async (manual = false) => {
    setIsSaving(true);
    try {
      const templateToSave = {
        name: templateName,
        type: templateType,
        template_data: templateData,
        is_public: false
      };

      let savedTemplate;
      if (id) {
        savedTemplate = await cardEditorService.updateTemplate(id, templateToSave);
      } else {
        savedTemplate = await cardEditorService.createTemplate(templateToSave);
        if (savedTemplate) {
          navigate(`/dashboard/card-editor/${savedTemplate.id}`);
        }
      }

      if (savedTemplate && manual) {
        notify('Template sauvegardé avec succès', 'success');
      } else {
        logger.info('Template sauvegardé avec succès', savedTemplate);
      }
    } catch (error) {
      logger.info('Erreur lors de la sauvegarde du template:', error);
      notify('Erreur lors de la sauvegarde du template', 'error');
    } finally {
      setIsSaving(false);
      setSaveDialogOpen(false);
    }
  };

  // Ouvrir la boîte de dialogue d'exportation
  const handleExportClick = () => {
    setExportDialogOpen(true);
  };

  // Exporter le template
  const handleExport = async (format: 'pdf' | 'png' | 'jpg') => {
    try {
      if (format === 'pdf' && id) {
        // Exporter en PDF via le backend
        const pdfBlob = await cardEditorService.exportTemplatePDF(id);
        if (pdfBlob) {
          // Créer un lien de téléchargement
          const url = URL.createObjectURL(pdfBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${templateName}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }
      } else if (format === 'png' || format === 'jpg') {
        // Exporter en image côté client via la ref du canvas
        if (canvasRef.current && canvasRef.current.exportAsImage) {
          try {
            const dataURL = canvasRef.current.exportAsImage(format);

            if (dataURL) {
              // Créer un lien de téléchargement
              const link = document.createElement('a');
              link.href = dataURL;
              link.download = `${templateName}.${format}`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              notify(`Template exporté en ${format.toUpperCase()} avec succès`, 'success');
            } else {
              notify('Erreur: Impossible de générer l\'image', 'error');
            }
          } catch (canvasError: any) {
            console.error('Erreur canvas:', canvasError);
            if (canvasError.message && canvasError.message.includes('Tainted')) {
              // Fallback: export côté serveur pour contourner le problème CORS
              notify('Export côté serveur en cours...', 'info');
              try {
                const response = await fetch(`/api/card-editor/export-image/${id}?format=${format}`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ templateData })
                });

                if (response.ok) {
                  const blob = await response.blob();
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `${templateName}.${format}`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  URL.revokeObjectURL(url);
                  notify(`Template exporté en ${format.toUpperCase()} avec succès`, 'success');
                } else {
                  notify('Erreur lors de l\'export côté serveur', 'error');
                }
              } catch (serverError) {
                notify('Erreur: Les images externes empêchent l\'export. Utilisez le format PDF.', 'warning');
              }
            } else {
              notify('Erreur lors de l\'export de l\'image', 'error');
            }
          }
        } else {
          notify('Erreur: Canvas non disponible pour l\'export', 'error');
        }
      }
    } catch (error) {
      logger.info('Erreur lors de l\'exportation du template:', error);
      notify('Erreur lors de l\'exportation du template', 'error');
    } finally {
      setExportDialogOpen(false);
    }
  };

  // Générer un template aléatoire avec l'IA
  const handleGenerateRandom = async () => {
    try {
      const result = await cardEditorService.generateRandomTemplate(templateType);

      if (result.requiresConsent) {
        setAiConsentModalOpen(true);
        return;
      }

      if (result.creditsRequired) {
        notify(`Vous n'avez pas assez de crédits IA. Cette action nécessite ${result.creditsRequired} crédits. Veuillez en acheter dans le menu Intelligence Artificielle.`, 'error');
        navigate('/dashboard/ai-credits');
        return;
      }

      if (result.template) {
        setTemplateName(result.template.name);
        setTemplateType(result.template.type);
        setTemplateData(result.template.template_data);

        // Réinitialiser l'historique
        setHistory([result.template.template_data]);
        setHistoryIndex(0);

        navigate(`/dashboard/card-editor/${result.template.id}`);
      }
    } catch (error) {
      logger.info('Erreur lors de la génération du template:', error);
      notify('Erreur lors de la génération du template', 'error');
    }
  };

  // Mettre à jour la couleur de fond
  const handleBackgroundColorChange = (color: string) => {
    const updatedTemplateData = {
      ...templateData,
      background_color: color
    };

    setTemplateData(updatedTemplateData);

    // Ajouter à l'historique
    addToHistory(updatedTemplateData);
  };

  // Basculer le mode dessin
  const toggleDrawingMode = () => {
    setIsDrawingMode(!isDrawingMode);

    // AMÉLIORATION: Gérer l'ouverture du panneau de dessin sur mobile
    if (isMobile && !isDrawingMode) {
      setMobileDrawingControlsOpen(true);
    } else if (isMobile && isDrawingMode) {
      setMobileDrawingControlsOpen(false);
    }

    // Désélectionner l'élément actuel lorsqu'on entre en mode dessin
    if (!isDrawingMode) {
      setSelectedElement(null);
    }
  };

  // Changer le type de template
  const handleTypeChange = (newType: CardTemplateType) => {
    if (newType !== templateType) {
      // Demander confirmation si le template contient des éléments
      if (templateData.elements.length > 0) {
        if (window.confirm('Changer le type de template effacera tous les éléments. Continuer ?')) {
          initializeTemplate(newType);
        }
      } else {
        initializeTemplate(newType);
      }
    }
  };

  // Mettre l'élément sélectionné au premier plan
  const handleElementBringToFront = (elementId: string) => {
    const idx = templateData.elements.findIndex(el => el.id === elementId);
    if (idx === -1) return;
    const newElements = [...templateData.elements];
    const [el] = newElements.splice(idx, 1);
    newElements.push(el);
    const updatedTemplateData = { ...templateData, elements: newElements };
    setTemplateData(updatedTemplateData);
    setSelectedElement(el);
    addToHistory(updatedTemplateData);
  };

  // Mettre l'élément sélectionné à l'arrière-plan
  const handleElementSendToBack = (elementId: string) => {
    const idx = templateData.elements.findIndex(el => el.id === elementId);
    if (idx === -1) return;
    const newElements = [...templateData.elements];
    const [el] = newElements.splice(idx, 1);
    newElements.unshift(el);
    const updatedTemplateData = { ...templateData, elements: newElements };
    setTemplateData(updatedTemplateData);
    setSelectedElement(el);
    addToHistory(updatedTemplateData);
  };

  // Fonction pour gérer le zoom avec des boutons
  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.1, 3));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.1, 0.5));
  };

  // Fonction pour basculer le panneau de propriétés sur mobile
  const togglePropertiesPanel = () => {
    setPropertiesPanelOpen(!propertiesPanelOpen);
  };

  // AMÉLIORATION: Fonctions de gestion des panneaux mobiles
  const handleMobileElementSelect = (element: CardElement) => {
    setSelectedElement(element);
    if (isMobile) {
      setMobilePropertiesDrawerOpen(true);
      setSidePanelTab('properties'); // Basculer automatiquement sur l'onglet Propriétés
    }
  };

  const handleMobilePropertiesClose = () => {
    setMobilePropertiesDrawerOpen(false);
  };

  const handleMobileDrawingControlsClose = () => {
    setMobileDrawingControlsOpen(false);
    setIsDrawingMode(false);
  };

  // AMÉLIORATION: Ouvrir automatiquement le panneau de dessin sur mobile quand le mode dessin est activé
  useEffect(() => {
    if (isDrawingMode && isMobile) {
      setMobileDrawingControlsOpen(true);
    }
  }, [isDrawingMode, isMobile]);

  // Ajout de l'état pour la taille de la grille
  const [blockSnapSize, setBlockSnapSize] = useState<number>(10);

  // Onglet actif du panneau latéral : 'elements' ou 'properties'
  const [sidePanelTab, setSidePanelTab] = useState<'elements' | 'properties'>('elements');

  // Ajout pour la gestion de la sélection multiple, du renommage inline et du drag & drop
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [renamingId, setRenamingId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState<string>('');
  const [draggedId, setDraggedId] = useState<string | null>(null);

  // Gestion sélection multiple
  const toggleSelect = (id: string) => {
    setSelectedIds(ids => ids.includes(id) ? ids.filter(i => i !== id) : [...ids, id]);
  };
  const selectAll = () => setSelectedIds(templateData.elements.map(e => e.id));
  const deselectAll = () => setSelectedIds([]);

  // Actions groupées
  const handleDeleteSelected = () => {
    const updatedElements = templateData.elements.filter(e => !selectedIds.includes(e.id));
    setTemplateData({ ...templateData, elements: updatedElements });
    setSelectedIds([]);
    setSelectedElement(null);
    addToHistory({ ...templateData, elements: updatedElements });
  };
  const handleDuplicateSelected = () => {
    const toDuplicate = templateData.elements.filter(e => selectedIds.includes(e.id));
    const duplicated = toDuplicate.map(e => ({ ...e, id: crypto.randomUUID(), x: (e.x || 0) + 20, y: (e.y || 0) + 20 }));
    const updatedElements = [...templateData.elements, ...duplicated];
    setTemplateData({ ...templateData, elements: updatedElements });
    addToHistory({ ...templateData, elements: updatedElements });
  };
  const handleToggleVisibilitySelected = () => {
    const updatedElements = templateData.elements.map(e => {
      if (selectedIds.includes(e.id) && e.type === 'text') {
        const props = { ...(e as TextElement).properties };
        const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
        return { ...(e as TextElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
      }
      return e;
    });
    setTemplateData({ ...templateData, elements: updatedElements });
    addToHistory({ ...templateData, elements: updatedElements });
    setHasUnsavedChanges(true);
  };

  // Drag & drop (simple, sans lib externe)
  const handleDragStart = (id: string) => setDraggedId(id);
  const handleDragOver = (e: React.DragEvent, overId: string) => {
    e.preventDefault();
    if (draggedId && draggedId !== overId) {
      const fromIdx = templateData.elements.findIndex(e => e.id === draggedId);
      const toIdx = templateData.elements.findIndex(e => e.id === overId);
      if (fromIdx !== -1 && toIdx !== -1) {
        const newElements = [...templateData.elements];
        const [moved] = newElements.splice(fromIdx, 1);
        newElements.splice(toIdx, 0, moved);
        setTemplateData({ ...templateData, elements: newElements });
      }
    }
  };
  const handleDragEnd = () => {
    setDraggedId(null);
    addToHistory(templateData);
  };

  // Renommage inline
  const startRenaming = (el: any) => {
    setRenamingId(el.id);
    setRenameValue(el.type === 'text' ? el.properties.text : el.name || '');
  };
  const handleRenameChange = (e: React.ChangeEvent<HTMLInputElement>) => setRenameValue(e.target.value);
  const handleRenameSubmit = (el: any) => {
    if (el.type === 'text') {
      const updatedElements = templateData.elements.map(e => {
        if (e.id === el.id && e.type === 'text') {
          return { ...e, properties: { ...e.properties, text: renameValue } } as TextElement;
        }
        return e;
      });
      setTemplateData({ ...templateData, elements: updatedElements });
      addToHistory({ ...templateData, elements: updatedElements });
    } else {
      const updatedElements = templateData.elements.map(e => {
        if (e.id === el.id) {
          return { ...e, name: renameValue };
        }
        return e;
      });
      setTemplateData({ ...templateData, elements: updatedElements });
      addToHistory({ ...templateData, elements: updatedElements });
    }
    setRenamingId(null);
    setHasUnsavedChanges(true);
  };

  // Actions rapides par élément
  const handleDelete = (id: string) => {
    const updatedElements = templateData.elements.filter(e => e.id !== id);
    setTemplateData({ ...templateData, elements: updatedElements });
    setSelectedElement(null);
    addToHistory({ ...templateData, elements: updatedElements });
  };
  const handleDuplicate = (el: any) => {
    const duplicated = { ...el, id: crypto.randomUUID(), x: (el.x || 0) + 20, y: (el.y || 0) + 20 };
    const updatedElements = [...templateData.elements, duplicated];
    setTemplateData({ ...templateData, elements: updatedElements });
    addToHistory({ ...templateData, elements: updatedElements });
  };
  const handleMoveUp = (id: string) => {
    const idx = templateData.elements.findIndex(e => e.id === id);
    if (idx > 0) {
      const newElements = [...templateData.elements];
      const [moved] = newElements.splice(idx, 1);
      newElements.splice(idx - 1, 0, moved);
      setTemplateData({ ...templateData, elements: newElements });
      addToHistory({ ...templateData, elements: newElements });
    }
  };
  const handleMoveDown = (id: string) => {
    const idx = templateData.elements.findIndex(e => e.id === id);
    if (idx < templateData.elements.length - 1) {
      const newElements = [...templateData.elements];
      const [moved] = newElements.splice(idx, 1);
      newElements.splice(idx + 1, 0, moved);
      setTemplateData({ ...templateData, elements: newElements });
      addToHistory({ ...templateData, elements: newElements });
    }
  };
  const handleToggleVisibility = (el: any) => {
    const updatedElements = templateData.elements.map(e => {
      if (e.id === el.id && e.type === el.type) {
        if (e.type === 'text') {
          const props = { ...(e as TextElement).properties };
          const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
          return { ...(e as TextElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
        }
        if (e.type === 'image') {
          const props = { ...(e as ImageElement).properties };
          const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
          return { ...(e as ImageElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
        }
        if (e.type === 'shape') {
          const props = { ...(e as ShapeElement).properties };
          const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
          return { ...(e as ShapeElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
        }
        if (e.type === 'drawing') {
          const props = { ...(e as DrawingElement).properties };
          const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
          return { ...(e as DrawingElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
        }
        if (e.type === 'qrcode') {
          const props = { ...(e as QRCodeElement).properties };
          const hasVisible = Object.prototype.hasOwnProperty.call(props, 'visible');
          return { ...(e as QRCodeElement), properties: { ...props, visible: hasVisible && props.visible === false ? true : false } };
        }
      }
      return e;
    });
    setTemplateData({ ...templateData, elements: updatedElements });
    addToHistory({ ...templateData, elements: updatedElements });
    setHasUnsavedChanges(true);
  };

  const [contextMenu, setContextMenu] = useState<{ mouseX: number; mouseY: number; el: any | null } | null>(null);

  const handleContextMenu = (event: React.MouseEvent, el: any) => {
    event.preventDefault();
    setContextMenu(
      contextMenu === null
        ? {
            mouseX: event.clientX - 2,
            mouseY: event.clientY - 4,
            el,
          }
        : null,
    );
  };
  const handleCloseContextMenu = () => {
    setContextMenu(null);
  };

  const [showWatermark, setShowWatermark] = useState<boolean>(!isGenerateMode);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Fonction pour ouvrir l'onglet "Éléments" (desktop/tablette) ou la modale (mobile)
  const handleShowElementsList = () => {
    if (isMobile) {
      setShowElementsListModal(true);
    } else {
      setSidePanelTab('elements');
      setPropertiesPanelOpen(true); // s'assurer que le panneau latéral est ouvert
    }
  };

  // Fonction pour importer un template IA complet
  const handleImportTemplate = (importedTemplate: CardTemplateData) => {
    // Désactiver la grille et les guides lors de l'importation
    setShowGrid(false);
    setShowGuides(false);

    setTemplateData(importedTemplate);
    setTemplateName('Template IA importé');
    // Déduire le type selon les dimensions
    let detectedType: CardTemplateType = 'business_card';
    if (importedTemplate.width === 350 && importedTemplate.height === 200) detectedType = 'business_card_landscape';
    else if (importedTemplate.width === 200 && importedTemplate.height === 350) detectedType = 'business_card';
    else if (importedTemplate.width === 595 && importedTemplate.height === 842) detectedType = 'flyer';
    else if (importedTemplate.width === 842 && importedTemplate.height === 595) detectedType = 'flyer_landscape';
    setTemplateType(detectedType);
    // Réinitialiser l'historique
    setHistory([importedTemplate]);
    setHistoryIndex(0);
    setSelectedElement(null);
  };

  return (
    <EditorThemeProvider>
      <GlobalStyles styles={{ body: { paddingRight: '0 !important' } }} />
      <Box sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header avec titre et contrôles principaux */}
        <Paper
          elevation={0}
          sx={{
            p: { xs: 1, sm: 1.5 },
            borderRadius: 2,
            border: '0px solid rgba(0,0,0,0.08)',
            mb: 1,
            mt: 2,
            mx: { xs: 1, sm: 2 },
            position: 'sticky',
            top: 0,
            zIndex: 10,
            backgroundColor: 'background.paper'
          }}
        >
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 1.5 },
            alignItems: { xs: 'stretch', sm: 'center' },
            width: '100%'
          }}>
            {/* Titre avec adaptation mobile */}
            <Typography
              variant="h4"
              sx={{
                fontSize: { xs: '1.3rem', sm: '1.5rem', md: '1.75rem' },
                fontWeight: 600,
                color: 'primary.main',
                mb: { xs: 0.5, sm: 0 },
                textAlign: { xs: 'center', sm: 'left' }
              }}
            >
              {templateType.includes('business_card') ? 'Carte de Visite' : 'Flyer'}
              {templateType.includes('landscape') && ' (paysage)'}
            </Typography>

            {/* Zone du milieu avec Type et Nom */}
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 1.5 },
              alignItems: { xs: 'stretch', sm: 'center' },
              flexGrow: 1,
              width: { xs: '100%', sm: 'auto' }
            }}>
              <FormControl
                size="small"
                sx={{
                  minWidth: { xs: '100%', sm: 180 },
                  maxWidth: { xs: '100%', sm: 220 }
                }}
              >
                <InputLabel>Type</InputLabel>
                <Select
                  value={templateType}
                  onChange={(e) => handleTypeChange(e.target.value as CardTemplateType)}
                  label="Type"
                  sx={{ width: '100%' }}
                >
                  <MenuItem value="business_card">Carte de visite (portrait)</MenuItem>
                  <MenuItem value="business_card_landscape">Carte de visite (paysage)</MenuItem>
                  <MenuItem value="flyer">Flyer (portrait)</MenuItem>
                  <MenuItem value="flyer_landscape">Flyer (paysage)</MenuItem>
                </Select>
              </FormControl>

              <TextField
                label="Nom"
                size="small"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                sx={{
                  width: '100%'
                }}
              />
            </Box>

            {/* Zone des boutons */}
            <Box sx={{
              display: 'flex',
              gap: 1,
              justifyContent: { xs: 'center', sm: 'flex-end' },
              mt: { xs: 1, sm: 0 },
              width: { xs: '100%', sm: 'auto' }
            }}>
              <Tooltip title="Enregistrer">
                <Button
                  onClick={() => handleSave(true)}
                  variant="contained"
                  disabled={isSaving}
                  startIcon={<Save />}
                  size={isMobile ? "small" : "medium"}
                  sx={{
                    minWidth: { xs: '45%', sm: isMobile ? 40 : 110 },
                    whiteSpace: 'nowrap'
                  }}
                >
                  {isMobile ? '' : 'Enregistrer'}
                </Button>
              </Tooltip>

              <Tooltip title="Exporter">
                <Button
                  variant="outlined"
                  onClick={handleExportClick}
                  size={isMobile ? "small" : "medium"}
                  sx={{
                    minWidth: { xs: '45%', sm: isMobile ? 40 : 110 },
                    whiteSpace: 'nowrap'
                  }}
                >
                  {isMobile ? 'Export' : 'Exporter'}
                </Button>
              </Tooltip>
            </Box>
          </Box>
        </Paper>

        {/* Barre d'outils principale */}
        <Box sx={{
          position: 'sticky',
          top: isMobile ? 56 : 64,
          zIndex: 9,
          backgroundColor: 'transparent'
        }}>
          <Toolbar
            onAddElement={handleAddElement}
            onGenerateRandom={handleGenerateRandom}
            onUndo={handleUndo}
            onRedo={handleRedo}
            onClear={handleClear}
            onDrawingModeToggle={toggleDrawingMode}
            isDrawingMode={isDrawingMode}
            canUndo={historyIndex > 0}
            canRedo={historyIndex < history.length - 1}
            autoSaveEnabled={autoSaveEnabled}
            setAutoSaveEnabled={setAutoSaveEnabled}
            onShowElementsList={handleShowElementsList}
            isSaving={isSaving}
            userAvatarUrl={profileData?.photo_url || null}
            templateType={templateType}
            onImportTemplate={handleImportTemplate}
            onSave={() => handleSave(false)}
          />
          {/* Contrôle taille de grille, affiché seulement si le snap est activé */}
          {snapEnabled && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1, ml: 2 }}>
              <Typography variant="body2" color="text.secondary">Taille de la grille :</Typography>
              <Slider
                value={blockSnapSize}
                min={5}
                max={100}
                step={1}
                onChange={(_, value) => setBlockSnapSize(value as number)}
                valueLabelDisplay="auto"
                size="small"
                sx={{ width: 120 }}
              />
              <Typography variant="body2" color="text.secondary">{blockSnapSize}px</Typography>
            </Box>
          )}
        </Box>

        {/* Contenu principal avec zone de travail et panneau de propriétés */}
        <Box sx={{
          display: 'flex',
          flexGrow: 1,
          overflow: 'hidden',
          flexDirection: { xs: 'column', md: 'row' }
        }}>
          {/* Zone de travail principale */}
          <Box sx={{
            flexGrow: 1,
            overflow: 'auto',
            p: { xs: 1, sm: 2 },
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Contrôles de zoom et d'affichage */}
            <Paper sx={{
              p: 1.5,
              mb: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: 1,
              borderRadius: 2,
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                width: { xs: '100%', sm: 'auto' }
              }}>
                <Tooltip title="Zoom arrière">
                  <IconButton
                    onClick={handleZoomOut}
                    disabled={zoom <= 0.5}
                    size="small"
                  >
                    <ZoomOut />
                  </IconButton>
                </Tooltip>

                <Box sx={{ width: { xs: '100%', sm: 150 } }}>
                  <Slider
                    value={zoom}
                    min={0.5}
                    max={3}
                    step={0.1}
                    onChange={(_, value) => setZoom(value as number)}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    size="small"
                    sx={{
                      '& .MuiSlider-thumb': {
                        transition: 'none',
                      },
                      '& .MuiSlider-track': {
                        transition: 'none',
                      }
                    }}
                  />
                </Box>

                <Tooltip title="Zoom avant">
                  <IconButton
                    onClick={handleZoomIn}
                    disabled={zoom >= 3}
                    size="small"
                  >
                    <ZoomIn />
                  </IconButton>
                </Tooltip>

                <Typography variant="body2" sx={{
                  minWidth: 45,
                  textAlign: 'center'
                }}>
                  {Math.round(zoom * 100)}%
                </Typography>
              </Box>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                ml: { xs: 0, sm: 'auto' }
              }}>
                <Tooltip title={showGrid ? "Masquer la grille" : "Afficher la grille"}>
                  <IconButton
                    onClick={() => setShowGrid(!showGrid)}
                    color={showGrid ? "primary" : "default"}
                    size="small"
                  >
                    {showGrid ? <GridOn /> : <GridOff />}
                  </IconButton>
                </Tooltip>

                <Tooltip title={showGuides ? "Masquer les guides" : "Afficher les guides"}>
                  <IconButton
                    onClick={() => setShowGuides(!showGuides)}
                    color={showGuides ? "primary" : "default"}
                    size="small"
                  >
                    <Straighten />
                  </IconButton>
                </Tooltip>

                <Tooltip title={snapEnabled ? "Désactiver l'alignement automatique" : "Activer l'alignement automatique"}>
                  <IconButton
                    onClick={() => setSnapEnabled(!snapEnabled)}
                    color={snapEnabled ? "primary" : "default"}
                    size="small"
                  >
                    <GridViewRounded />
                  </IconButton>
                </Tooltip>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5
                }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mr: 0.5 }}>
                    Fond:
                  </Typography>
                  <Tooltip title="Couleur de fond">
                    <Box sx={{ position: 'relative' }}>
                      <input
                        type="color"
                        value={templateData.background_color}
                        onChange={(e) => handleBackgroundColorChange(e.target.value)}
                        style={{
                          width: '28px',
                          height: '28px',
                          cursor: 'pointer',
                          border: 'none',
                          borderRadius: '4px',
                          padding: 0
                        }}
                      />
                    </Box>
                  </Tooltip>
                </Box>
                <Tooltip title={showWatermark ? "Masquer le filigrane" : "Afficher le filigrane"}>
                  <IconButton
                    onClick={() => setShowWatermark(v => !v)}
                    color={showWatermark ? "primary" : "default"}
                    size="small"
                  >
                    {showWatermark ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </Tooltip>
              </Box>
            </Paper>

            {/* Alerte mode dessin */}
            {isDrawingMode && (
              <Paper sx={{
                mb: 2,
                p: 1.5,
                backgroundColor: '#FFF8F3',
                borderRadius: 2,
                border: '1px solid #FFE4BA',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Mode dessin activé</strong> - Dessinez directement sur le canvas.
                  Cliquez sur l'icône pinceau pour terminer.
                </Typography>
              </Paper>
            )}

            {/* Dimensions du template */}
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 1, textAlign: 'center' }}
            >
              {templateType === 'business_card'
                ? 'Dimensions: 350 x 200 px (format standard carte de visite)'
                : 'Dimensions: 595 x 842 px (format A4)'}
            </Typography>

            {/* Canvas d'édition */}
            <Box sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexGrow: 1,
              overflow: 'auto',
              p: 2,
              backgroundColor: '#f5f5f5',
              borderRadius: 2
            }}>
              <CardEditorCanvas
                ref={canvasRef}
                templateData={templateData}
                onElementSelect={setSelectedElement}
                onElementUpdate={handleElementUpdate}
                onElementAdd={handleAddElement}
                selectedElement={selectedElement}
                zoom={zoom}
                showGrid={showGrid}
                showGuides={showGuides}
                snapEnabled={snapEnabled}
                isDrawingMode={isDrawingMode}
                setIsDrawingMode={setIsDrawingMode}
                blockSnapSize={blockSnapSize}
                onElementContextMenu={handleContextMenu}
                onMobileElementSelect={handleMobileElementSelect}
                onElementDelete={handleElementDelete}
                showWatermark={showWatermark}
                drawingTool={drawingTool}
                strokeColor={strokeColor}
                strokeWidth={strokeWidth}
              />
            </Box>

            {/* AMÉLIORATION: Contrôles de dessin mobiles */}
            <DrawingControls
              isActive={mobileDrawingControlsOpen}
              tool={drawingTool}
              strokeColor={strokeColor}
              strokeWidth={strokeWidth}
              onToolChange={setDrawingTool}
              onColorChange={setStrokeColor}
              onWidthChange={setStrokeWidth}
              onClose={handleMobileDrawingControlsClose}
            />
          </Box>

          {/* Panneau de propriétés */}
          {isMobile ? (
            <Drawer
              variant="temporary"
              anchor="bottom"
              open={mobilePropertiesDrawerOpen}
              onClose={handleMobilePropertiesClose}
              sx={{
                width: '100%',
                '& .MuiDrawer-paper': {
                  width: '100%',
                  height: 'auto',
                  maxHeight: '60vh', // AMÉLIORATION: Réduction de 80vh à 60vh pour laisser plus d'espace au canvas
                  boxSizing: 'border-box',
                  borderTop: '1px solid rgba(0,0,0,0.12)',
                  boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'
                },
              }}
            >
              <Tabs
                value={sidePanelTab}
                onChange={(_, v) => setSidePanelTab(v)}
                variant="fullWidth"
                sx={{ borderBottom: '1px solid rgba(0,0,0,0.08)', position: 'relative' }}
              >
                <Tab
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                      <span>Éléments</span>
                      {isMobile && sidePanelTab === 'elements' && mobilePropertiesDrawerOpen && (
                        <IconButton
                          size="small"
                          aria-label="Fermer le panneau"
                          onClick={e => { e.stopPropagation(); setMobilePropertiesDrawerOpen(false); }}
                          sx={{ ml: 1, p: 0.5 }}
                        >
                          <Close fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  }
                  value="elements"
                  sx={{ minWidth: 0, flex: 1 }}
                />
                <Tab
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                      <span>Propriétés</span>
                      {isMobile && sidePanelTab === 'properties' && mobilePropertiesDrawerOpen && (
                        <IconButton
                          size="small"
                          aria-label="Fermer le panneau"
                          onClick={e => { e.stopPropagation(); setMobilePropertiesDrawerOpen(false); }}
                          sx={{ ml: 1, p: 0.5 }}
                        >
                          <Close fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  }
                  value="properties"
                  disabled={!selectedElement}
                  sx={{ minWidth: 0, flex: 1 }}
                />
              </Tabs>
              {sidePanelTab === 'elements' && (
                <Box sx={{ overflow: 'auto', height: isMobile ? 'calc(60vh - 96px)' : 'calc(100% - 48px)', p: 1 }}>
                  {/* Nom du canva sur une ligne */}
                  <Typography
                    variant="h6"
                    align="center"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 0,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '100%',
                      display: 'block',
                    }}
                  >
                    {templateName}
                  </Typography>
                  {/* Barre d'actions groupées sur une autre ligne, espacée */}
                  {selectedIds.length > 0 && (
                    <Box sx={{ display: 'flex', gap: 1, mb: 1, mt: 1, alignItems: 'center', justifyContent: 'center' }}>
                      <Tooltip title="Tout sélectionner">
                        <Checkbox checked={selectedIds.length === templateData.elements.length} indeterminate={selectedIds.length > 0 && selectedIds.length < templateData.elements.length} onChange={e => e.target.checked ? selectAll() : deselectAll()} />
                      </Tooltip>
                      <Tooltip title="Supprimer">
                        <IconButton color="error" onClick={handleDeleteSelected}><Delete /></IconButton>
                      </Tooltip>
                      <Tooltip title="Dupliquer">
                        <IconButton color="primary" onClick={handleDuplicateSelected}><FileCopy /></IconButton>
                      </Tooltip>
                      <Tooltip title="Afficher/Masquer">
                        <IconButton color="primary" onClick={handleToggleVisibilitySelected}><VisibilityOff /></IconButton>
                      </Tooltip>
                      <Typography variant="caption" color="text.secondary">{selectedIds.length} sélectionné(s)</Typography>
                    </Box>
                  )}
                  <Box sx={{ borderBottom: '1px solid #FFE4BA', mb: 1 }} />
                  {/* Liste des éléments */}
                  <Box
                    component="ul"
                    sx={{ pl: 0, pr: 0, m: 0, listStyle: 'none', maxHeight: isMobile ? '40vh' : '70vh', overflowY: 'auto' }}
                  >
                    {templateData.elements.length === 0 ? (
                      <Typography color="text.secondary" sx={{ p: 2 }}>Aucun élément sur le canvas.</Typography>
                    ) : templateData.elements.map((el, idx) => {
                      const isSelected = selectedElement && selectedElement.id === el.id;
                      const isChecked = selectedIds.includes(el.id);
                      // Correction : détecter la visibilité pour tous les types
                      let isVisible = true;
                      if (el.type === 'text') isVisible = (el as TextElement).properties.visible !== false;
                      if (el.type === 'image') isVisible = (el as ImageElement).properties.visible !== false;
                      if (el.type === 'shape') isVisible = (el as ShapeElement).properties.visible !== false;
                      if (el.type === 'drawing') isVisible = (el as DrawingElement).properties.visible !== false;
                      if (el.type === 'qrcode') isVisible = (el as QRCodeElement).properties.visible !== false;
                      return (
                        <Box
                          component="li"
                          key={el.id}
                          draggable
                          onDragStart={() => handleDragStart(el.id)}
                          onDragOver={e => handleDragOver(e, el.id)}
                          onDragEnd={handleDragEnd}
                          onContextMenu={e => handleContextMenu(e, el)}
                          sx={{
                            mb: 0.5,
                            px: 1,
                            py: 1,
                            borderRadius: 1,
                            cursor: 'pointer',
                            bgcolor: isSelected ? 'primary.light' : !isVisible ? '#FFE4BA' : 'transparent',
                            border: isSelected ? `2px solid ${theme.palette.primary.main}` : '1px solid transparent',
                            display: 'block',
                            transition: 'background 0.2s, border 0.2s',
                            opacity: isVisible ? 1 : 0.7,
                            textDecoration: isVisible ? 'none' : 'line-through',
                            '&:hover': {
                              bgcolor: isSelected ? 'primary.light' : !isVisible ? '#FFE4BA' : 'grey.100',
                            },
                          }}
                          onClick={() => setSelectedElement(el)}
                        >
                          {/* Première ligne : checkbox + label/mini-preview */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Checkbox checked={isChecked} onClick={e => { e.stopPropagation(); toggleSelect(el.id); }} size="small" />
                            {/* Mini-preview ou label principal */}
                            {el.type === 'image' && (
                              <Tooltip title={`Image (id: ${el.id})`}>
                                <img src={el.properties.src} alt="" style={{ width: 28, height: 28, objectFit: 'cover', borderRadius: 4, filter: !isVisible ? 'grayscale(1) brightness(0.8)' : undefined }} />
                              </Tooltip>
                            )}
                            {el.type === 'text' && (
                              renamingId === el.id ? (
                                <InputBase
                                  value={renameValue}
                                  onChange={handleRenameChange}
                                  onBlur={() => handleRenameSubmit(el)}
                                  onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                                  autoFocus
                                  sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: 120 }}
                                />
                              ) : (
                                <Tooltip title={`Texte (id: ${el.id})`}>
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', textTransform: 'capitalize', textDecoration: isVisible ? 'none' : 'line-through' }}
                                    onDoubleClick={() => startRenaming(el)}
                                  >
                                    {el.properties.text}
                                  </Typography>
                                </Tooltip>
                              )
                            )}
                            {el.type !== 'text' && (
                              renamingId === el.id ? (
                                <InputBase
                                  value={renameValue}
                                  onChange={handleRenameChange}
                                  onBlur={() => handleRenameSubmit(el)}
                                  onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                                  autoFocus
                                  sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: 120 }}
                                />
                              ) : (
                                <Tooltip title={`${el.type.charAt(0).toUpperCase() + el.type.slice(1)} (id: ${el.id})`}>
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', textTransform: 'capitalize', textDecoration: isVisible ? 'none' : 'line-through' }}
                                    onDoubleClick={() => startRenaming(el)}
                                  >
                                    {el.name || (el.type === 'shape' ? (el as ShapeElement).properties.shape : undefined) || el.type}
                                  </Typography>
                                </Tooltip>
                              )
                            )}
                            {/* Icône d'œil barré si masqué */}
                            {!isVisible && (
                              <VisibilityOff sx={{ color: '#FF6B2C', ml: 1 }} />
                            )}
                          </Box>
                          {/* Deuxième ligne : barre d'actions */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5, ml: 4 }}>
                            {/* Actions rapides */}
                            {['text', 'image', 'shape', 'drawing', 'qrcode'].includes(el.type) && (
                              <Tooltip title={isVisible ? 'Masquer' : 'Afficher'}>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleToggleVisibility(el); }}>
                                  {isVisible ? <Visibility /> : <VisibilityOff sx={{ color: '#FF6B2C' }} />}
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Monter">
                              <span>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveUp(el.id); }} disabled={idx === 0}><ArrowUpward /></IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip title="Descendre">
                              <span>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveDown(el.id); }} disabled={idx === templateData.elements.length - 1}><ArrowDownward /></IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip title="Dupliquer">
                              <IconButton size="small" onClick={e => { e.stopPropagation(); handleDuplicate(el); }}><FileCopy /></IconButton>
                            </Tooltip>
                            <Tooltip title="Supprimer">
                              <IconButton size="small" color="error" onClick={e => { e.stopPropagation(); handleDelete(el.id); }}><Delete /></IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      );
                    })}
                  </Box>
                </Box>
              )}
              {sidePanelTab === 'properties' && selectedElement && (
                <Box sx={{ overflow: 'auto', maxHeight: 'calc(60vh - 96px)' }}>
                  <PropertiesPanel
                    selectedElement={selectedElement}
                    onElementUpdate={handleElementUpdate}
                    onElementDelete={handleElementDelete}
                    onElementBringToFront={handleElementBringToFront}
                    onElementSendToBack={handleElementSendToBack}
                    isMobileDrawerOpen={mobilePropertiesDrawerOpen}
                    onMobileDrawerClose={handleMobilePropertiesClose}
                  />
                  {/* Bouton Fermer pour mobile uniquement */}
                  {isMobile && (
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      fullWidth
                      sx={{ mt: 2, mb: 1, fontWeight: 600 }}
                      onClick={() => setMobilePropertiesDrawerOpen(false)}
                    >
                      Fermer
                    </Button>
                  )}
                </Box>
              )}
            </Drawer>
          ) : (
            <Box
              sx={{
                width: 320,
                flexShrink: 0,
                height: '100%',
                overflow: 'hidden',
                borderLeft: '1px solid rgba(0,0,0,0.12)',
                position: 'relative'
              }}
            >
              <Tabs
                value={sidePanelTab}
                onChange={(_, v) => setSidePanelTab(v)}
                variant="fullWidth"
                sx={{ borderBottom: '1px solid rgba(0,0,0,0.08)' }}
              >
                <Tab
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                      <span>Éléments</span>
                      {isMobile && sidePanelTab === 'elements' && mobilePropertiesDrawerOpen && (
                        <IconButton
                          size="small"
                          aria-label="Fermer le panneau"
                          onClick={e => { e.stopPropagation(); setMobilePropertiesDrawerOpen(false); }}
                          sx={{ ml: 1, p: 0.5 }}
                        >
                          <Close fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  }
                  value="elements"
                  sx={{ minWidth: 0, flex: 1 }}
                />
                <Tab
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
                      <span>Propriétés</span>
                      {isMobile && sidePanelTab === 'properties' && mobilePropertiesDrawerOpen && (
                        <IconButton
                          size="small"
                          aria-label="Fermer le panneau"
                          onClick={e => { e.stopPropagation(); setMobilePropertiesDrawerOpen(false); }}
                          sx={{ ml: 1, p: 0.5 }}
                        >
                          <Close fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  }
                  value="properties"
                  disabled={!selectedElement}
                  sx={{ minWidth: 0, flex: 1 }}
                />
              </Tabs>
              {sidePanelTab === 'elements' && (
                <Box sx={{ overflow: 'auto', height: isMobile ? 'calc(80vh - 96px)' : 'calc(100% - 48px)', p: 1 }}>
                  {/* Nom du canva sur une ligne */}
                  <Typography
                    variant="h6"
                    align="center"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 0,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '100%',
                      display: 'block',
                    }}
                  >
                    {templateName}
                  </Typography>
                  {/* Barre d'actions groupées sur une autre ligne, espacée */}
                  {selectedIds.length > 0 && (
                    <Box sx={{ display: 'flex', gap: 1, mb: 1, mt: 1, alignItems: 'center', justifyContent: 'center' }}>
                      <Tooltip title="Tout sélectionner">
                        <Checkbox checked={selectedIds.length === templateData.elements.length} indeterminate={selectedIds.length > 0 && selectedIds.length < templateData.elements.length} onChange={e => e.target.checked ? selectAll() : deselectAll()} />
                      </Tooltip>
                      <Tooltip title="Supprimer">
                        <IconButton color="error" onClick={handleDeleteSelected}><Delete /></IconButton>
                      </Tooltip>
                      <Tooltip title="Dupliquer">
                        <IconButton color="primary" onClick={handleDuplicateSelected}><FileCopy /></IconButton>
                      </Tooltip>
                      <Tooltip title="Afficher/Masquer">
                        <IconButton color="primary" onClick={handleToggleVisibilitySelected}><VisibilityOff /></IconButton>
                      </Tooltip>
                      <Typography variant="caption" color="text.secondary">{selectedIds.length} sélectionné(s)</Typography>
                    </Box>
                  )}
                  <Box sx={{ borderBottom: '1px solid #FFE4BA', mb: 1 }} />
                  {/* Liste des éléments */}
                  <Box
                    component="ul"
                    sx={{ pl: 0, pr: 0, m: 0, listStyle: 'none', maxHeight: '70vh', overflowY: 'auto' }}
                  >
                    {templateData.elements.length === 0 ? (
                      <Typography color="text.secondary" sx={{ p: 2 }}>Aucun élément sur le canvas.</Typography>
                    ) : templateData.elements.map((el, idx) => {
                      const isSelected = selectedElement && selectedElement.id === el.id;
                      const isChecked = selectedIds.includes(el.id);
                      // Correction : détecter la visibilité pour tous les types
                      let isVisible = true;
                      if (el.type === 'text') isVisible = (el as TextElement).properties.visible !== false;
                      if (el.type === 'image') isVisible = (el as ImageElement).properties.visible !== false;
                      if (el.type === 'shape') isVisible = (el as ShapeElement).properties.visible !== false;
                      if (el.type === 'drawing') isVisible = (el as DrawingElement).properties.visible !== false;
                      if (el.type === 'qrcode') isVisible = (el as QRCodeElement).properties.visible !== false;
                      return (
                        <Box
                          component="li"
                          key={el.id}
                          draggable
                          onDragStart={() => handleDragStart(el.id)}
                          onDragOver={e => handleDragOver(e, el.id)}
                          onDragEnd={handleDragEnd}
                          onContextMenu={e => handleContextMenu(e, el)}
                          sx={{
                            mb: 0.5,
                            px: 1,
                            py: 1,
                            borderRadius: 1,
                            cursor: 'pointer',
                            bgcolor: isSelected ? 'primary.light' : !isVisible ? '#FFE4BA' : 'transparent',
                            border: isSelected ? `2px solid ${theme.palette.primary.main}` : '1px solid transparent',
                            display: 'block',
                            transition: 'background 0.2s, border 0.2s',
                            opacity: isVisible ? 1 : 0.7,
                            textDecoration: isVisible ? 'none' : 'line-through',
                            '&:hover': {
                              bgcolor: isSelected ? 'primary.light' : !isVisible ? '#FFE4BA' : 'grey.100',
                            },
                          }}
                          onClick={() => setSelectedElement(el)}
                        >
                          {/* Première ligne : checkbox + label/mini-preview */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Checkbox checked={isChecked} onClick={e => { e.stopPropagation(); toggleSelect(el.id); }} size="small" />
                            {/* Mini-preview ou label principal */}
                            {el.type === 'image' && (
                              <Tooltip title={`Image (id: ${el.id})`}>
                                <img src={el.properties.src} alt="" style={{ width: 28, height: 28, objectFit: 'cover', borderRadius: 4, filter: !isVisible ? 'grayscale(1) brightness(0.8)' : undefined }} />
                              </Tooltip>
                            )}
                            {el.type === 'text' && (
                              renamingId === el.id ? (
                                <InputBase
                                  value={renameValue}
                                  onChange={handleRenameChange}
                                  onBlur={() => handleRenameSubmit(el)}
                                  onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                                  autoFocus
                                  sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: 120 }}
                                />
                              ) : (
                                <Tooltip title={`Texte (id: ${el.id})`}>
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', textTransform: 'capitalize', textDecoration: isVisible ? 'none' : 'line-through' }}
                                    onDoubleClick={() => startRenaming(el)}
                                  >
                                    {el.properties.text}
                                  </Typography>
                                </Tooltip>
                              )
                            )}
                            {el.type !== 'text' && (
                              renamingId === el.id ? (
                                <InputBase
                                  value={renameValue}
                                  onChange={handleRenameChange}
                                  onBlur={() => handleRenameSubmit(el)}
                                  onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                                  autoFocus
                                  sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: 120 }}
                                />
                              ) : (
                                <Tooltip title={`${el.type.charAt(0).toUpperCase() + el.type.slice(1)} (id: ${el.id})`}>
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', textTransform: 'capitalize', textDecoration: isVisible ? 'none' : 'line-through' }}
                                    onDoubleClick={() => startRenaming(el)}
                                  >
                                    {el.name || (el.type === 'shape' ? (el as ShapeElement).properties.shape : undefined) || el.type}
                                  </Typography>
                                </Tooltip>
                              )
                            )}
                            {/* Icône d'œil barré si masqué */}
                            {!isVisible && (
                              <VisibilityOff sx={{ color: '#FF6B2C', ml: 1 }} />
                            )}
                          </Box>
                          {/* Deuxième ligne : barre d'actions */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5, ml: 4 }}>
                            {/* Actions rapides */}
                            {['text', 'image', 'shape', 'drawing', 'qrcode'].includes(el.type) && (
                              <Tooltip title={isVisible ? 'Masquer' : 'Afficher'}>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleToggleVisibility(el); }}>
                                  {isVisible ? <Visibility /> : <VisibilityOff sx={{ color: '#FF6B2C' }} />}
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Monter">
                              <span>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveUp(el.id); }} disabled={idx === 0}><ArrowUpward /></IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip title="Descendre">
                              <span>
                                <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveDown(el.id); }} disabled={idx === templateData.elements.length - 1}><ArrowDownward /></IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip title="Dupliquer">
                              <IconButton size="small" onClick={e => { e.stopPropagation(); handleDuplicate(el); }}><FileCopy /></IconButton>
                            </Tooltip>
                            <Tooltip title="Supprimer">
                              <IconButton size="small" color="error" onClick={e => { e.stopPropagation(); handleDelete(el.id); }}><Delete /></IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      );
                    })}
                  </Box>
                </Box>
              )}
              {sidePanelTab === 'properties' && selectedElement && (
                <Box sx={{ overflow: 'auto', maxHeight: 'calc(80vh - 96px)' }}>
                  <PropertiesPanel
                    selectedElement={selectedElement}
                    onElementUpdate={handleElementUpdate}
                    onElementDelete={handleElementDelete}
                    onElementBringToFront={handleElementBringToFront}
                    onElementSendToBack={handleElementSendToBack}
                    isMobileDrawerOpen={false}
                    onMobileDrawerClose={undefined}
                  />
                  {/* Bouton Fermer pour mobile uniquement */}
                  {isMobile && (
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      fullWidth
                      sx={{ mt: 2, mb: 1, fontWeight: 600 }}
                      onClick={() => setPropertiesPanelOpen(false)}
                    >
                      Fermer
                    </Button>
                  )}
                </Box>
              )}
            </Box>
          )}
        </Box>

        {/* AMÉLIORATION: Bouton flottant pour ouvrir le panneau de propriétés sur mobile */}
        {isMobile && selectedElement && !mobilePropertiesDrawerOpen && (
          <Fab
            color="primary"
            size="medium"
            onClick={() => setMobilePropertiesDrawerOpen(true)}
            sx={{
              position: 'fixed',
              bottom: 16,
              right: 16,
              zIndex: 1000
            }}
          >
            <Tune />
          </Fab>
        )}

      {/* Boîte de dialogue de sauvegarde */}
      <Dialog
        open={saveDialogOpen}
        onClose={() => setSaveDialogOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Enregistrer le template</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Nom du template"
            fullWidth
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>Annuler</Button>
          <Button
            onClick={() => handleSave(true)}
            variant="contained"
            disabled={isSaving}
          >
            {isSaving ? <CircularProgress size={24} /> : 'Enregistrer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Nouvelle modale d'exportation moderne */}
      <ExportModal
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        onExport={handleExport}
        templateName={templateName}
        templateId={id}
      />

      {/* Modale de consentement IA */}
      <AiConsentModal
        isOpen={aiConsentModalOpen}
        onClose={() => setAiConsentModalOpen(false)}
        onAccept={() => {
          setAiConsentModalOpen(false);
          handleGenerateRandom();
        }}
      />

      {/* Modale liste des éléments du canvas sur mobile */}
      {isMobile && (
        <ModalPortal isOpen={showElementsListModal} onBackdropClick={() => setShowElementsListModal(false)}>
          <Box sx={{
            bgcolor: '#FFF8F3',
            borderRadius: 3,
            boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
            minWidth: 200,
            maxWidth: '100vw',
            width: '100%',
            p: 0,
            m: 0,
            pt: 0,
            overflowX: 'auto',
            overflowY: 'visible',
            display: 'flex',
            flexDirection: 'column',
            maxHeight: '80vh',
          }}>
            <Box sx={{
              bgcolor: '#FF965E',
              color: '#fff',
              fontWeight: 700,
              py: 1.2,
              px: 2,
              fontSize: 18,
              borderRadius: '12px 12px 0 0',
              m: 0,
              minHeight: 0,
              lineHeight: 1.2,
              width: '100%',
              textAlign: 'left',
            }}>
              Éléments du canvas
            </Box>
            <Box sx={{ p: 0, pt: 1, overflowX: 'auto', maxWidth: '100%', flex: 1 }}>
              {/* Nom du canva sur une ligne */}
              <Typography
                variant="h6"
                align="center"
                sx={{
                  fontWeight: 700,
                  color: 'primary.main',
                  mb: 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '100%',
                  minWidth: 0,
                  display: 'block',
                }}
              >
                {templateName}
              </Typography>
              {/* Barre d'actions groupées sur une autre ligne, espacée */}
              {selectedIds.length > 0 && (
                <Box sx={{ display: 'flex', gap: 1, mb: 1, mt: 1, alignItems: 'center', justifyContent: 'center', maxWidth: '100%', minWidth: 0, overflowX: 'auto' }}>
                  <Tooltip title="Tout sélectionner">
                    <Checkbox checked={selectedIds.length === templateData.elements.length} indeterminate={selectedIds.length > 0 && selectedIds.length < templateData.elements.length} onChange={e => e.target.checked ? selectAll() : deselectAll()} />
                  </Tooltip>
                  <Tooltip title="Supprimer">
                    <IconButton color="error" onClick={handleDeleteSelected}><Delete /></IconButton>
                  </Tooltip>
                  <Tooltip title="Dupliquer">
                    <IconButton color="primary" onClick={handleDuplicateSelected}><FileCopy /></IconButton>
                  </Tooltip>
                  <Tooltip title="Afficher/Masquer">
                    <IconButton color="primary" onClick={handleToggleVisibilitySelected}><VisibilityOff /></IconButton>
                  </Tooltip>
                  <Typography variant="caption" color="text.secondary" sx={{ maxWidth: '100%', minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{selectedIds.length} sélectionné(s)</Typography>
                </Box>
              )}
              <Box sx={{ borderBottom: '1px solid #FFE4BA', mb: 1 }} />
              {/* Liste des éléments */}
              <Box component="ul" sx={{ pl: 0, pr: 0, m: 0, listStyle: 'none', maxHeight: 350, overflowY: 'auto', width: '100%', maxWidth: '100%', minWidth: 200 }}>
                {templateData.elements.length === 0 ? (
                  <Typography color="text.secondary" sx={{ p: 2, maxWidth: '100%', minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>Aucun élément sur le canvas.</Typography>
                ) : templateData.elements.map((el, idx) => {
                  const isSelected = selectedElement && selectedElement.id === el.id;
                  const isChecked = selectedIds.includes(el.id);
                  // Correction : détecter la visibilité pour tous les types
                  let isVisible = true;
                  if (el.type === 'text') isVisible = (el as TextElement).properties.visible !== false;
                  if (el.type === 'image') isVisible = (el as ImageElement).properties.visible !== false;
                  if (el.type === 'shape') isVisible = (el as ShapeElement).properties.visible !== false;
                  if (el.type === 'drawing') isVisible = (el as DrawingElement).properties.visible !== false;
                  if (el.type === 'qrcode') isVisible = (el as QRCodeElement).properties.visible !== false;
                  return (
                    <Box
                      component="li"
                      key={el.id}
                      draggable
                      onDragStart={() => handleDragStart(el.id)}
                      onDragOver={e => handleDragOver(e, el.id)}
                      onDragEnd={handleDragEnd}
                      onContextMenu={e => handleContextMenu(e, el)}
                      sx={{
                        mb: 0.5,
                        px: 1.2,
                        py: 1,
                        borderRadius: 1,
                        cursor: 'pointer',
                        bgcolor: isSelected ? '#FF965E' : isVisible ? 'transparent' : '#FFE4BA',
                        border: isSelected ? '2px solid #FF6B2C' : '1px solid transparent',
                        display: 'block',
                        transition: 'background 0.2s, border 0.2s',
                        opacity: isVisible ? 1 : 0.5,
                        textDecoration: isVisible ? 'none' : 'line-through',
                        '&:hover': {
                          bgcolor: isSelected ? '#FF965E' : isVisible ? '#FFF8F3' : '#FFE4BA',
                        },
                        width: '100%',
                        maxWidth: '100%',
                        minWidth: 0,
                        overflowX: 'auto',
                      }}
                      onClick={() => setSelectedElement(el)}
                    >
                      {/* Première ligne : checkbox + label/mini-preview */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'nowrap', overflow: 'hidden', maxWidth: '100%', minWidth: 0, overflowX: 'auto' }}>
                        <Checkbox checked={isChecked} onClick={e => { e.stopPropagation(); toggleSelect(el.id); }} size="small" />
                        {/* Mini-preview ou label principal */}
                        {el.type === 'image' && (
                          <Tooltip title={`Image (id: ${el.id})`}>
                            <img src={el.properties.src} alt="" style={{ width: 28, height: 28, objectFit: 'cover', borderRadius: 4, filter: !isVisible ? 'grayscale(1) brightness(0.8)' : undefined }} />
                          </Tooltip>
                        )}
                        {el.type === 'text' && (
                          renamingId === el.id ? (
                            <InputBase
                              value={renameValue}
                              onChange={handleRenameChange}
                              onBlur={() => handleRenameSubmit(el)}
                              onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                              autoFocus
                              sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                            />
                          ) : (
                            <Tooltip title={`Texte (id: ${el.id})`}>
                              <Typography
                                variant="subtitle2"
                                sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: '100%', minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                                onDoubleClick={() => startRenaming(el)}
                              >
                                {el.properties.text}
                              </Typography>
                            </Tooltip>
                          )
                        )}
                        {el.type !== 'text' && (
                          renamingId === el.id ? (
                            <InputBase
                              value={renameValue}
                              onChange={handleRenameChange}
                              onBlur={() => handleRenameSubmit(el)}
                              onKeyDown={e => { if (e.key === 'Enter') handleRenameSubmit(el); }}
                              autoFocus
                              sx={{ ml: 1, flex: 1, fontSize: 14, bgcolor: 'background.paper', borderRadius: 1, px: 0.5, minWidth: 0, maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                            />
                          ) : (
                            <Tooltip title={`${el.type.charAt(0).toUpperCase() + el.type.slice(1)} (id: ${el.id})`}>
                              <Typography
                                variant="subtitle2"
                                sx={{ color: isSelected ? '#fff' : isVisible ? 'text.secondary' : '#FF6B2C', fontWeight: isSelected ? 700 : 400, ml: 1, flex: 1, fontSize: 15, cursor: 'pointer', maxWidth: '100%', minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                                onDoubleClick={() => startRenaming(el)}
                              >
                                {el.name || (el.type === 'shape' ? (el as ShapeElement).properties.shape : undefined) || el.type}
                              </Typography>
                            </Tooltip>
                          )
                        )}
                      </Box>
                      {/* Deuxième ligne : barre d'actions */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5, ml: 4, flexWrap: 'nowrap', overflow: 'hidden', maxWidth: '100%', minWidth: 0, overflowX: 'auto' }}>
                        {/* Actions rapides */}
                        {['text', 'image', 'shape', 'drawing', 'qrcode'].includes(el.type) && (
                          <Tooltip title={isVisible ? 'Masquer' : 'Afficher'}>
                            <IconButton size="small" onClick={e => { e.stopPropagation(); handleToggleVisibility(el); }}>
                              {isVisible ? <Visibility /> : <VisibilityOff sx={{ color: '#FF6B2C' }} />}
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Monter">
                          <span>
                            <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveUp(el.id); }} disabled={idx === 0}><ArrowUpward /></IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Descendre">
                          <span>
                            <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveDown(el.id); }} disabled={idx === templateData.elements.length - 1}><ArrowDownward /></IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Dupliquer">
                          <IconButton size="small" onClick={e => { e.stopPropagation(); handleDuplicate(el); }}><FileCopy /></IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer">
                          <IconButton size="small" color="error" onClick={e => { e.stopPropagation(); handleDelete(el.id); }}><Delete /></IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            </Box>
            <Box sx={{ justifyContent: 'center', pb: 1, pt: 1, display: 'flex', width: '100%' }}>
              <Button onClick={() => setShowElementsListModal(false)} variant="outlined" sx={{ color: '#FF6B2C', borderColor: '#FF965E', fontWeight: 600, px: 3, borderRadius: 2 }}>Fermer</Button>
            </Box>
          </Box>
        </ModalPortal>
      )}

      <Menu
        open={contextMenu !== null}
        onClose={handleCloseContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        disableScrollLock
      >
        <MenuItem onClick={() => { handleDelete(contextMenu?.el.id); handleCloseContextMenu(); }}>Effacer</MenuItem>
        <MenuItem onClick={() => { handleDuplicate(contextMenu?.el); handleCloseContextMenu(); }}>Dupliquer</MenuItem>
        <MenuItem onClick={() => { handleMoveUp(contextMenu?.el.id); handleCloseContextMenu(); }} disabled={templateData.elements.findIndex(e => e.id === contextMenu?.el.id) === 0}>Monter</MenuItem>
        <MenuItem onClick={() => { handleMoveDown(contextMenu?.el.id); handleCloseContextMenu(); }} disabled={templateData.elements.findIndex(e => e.id === contextMenu?.el.id) === templateData.elements.length - 1}>Descendre</MenuItem>
        <MenuItem onClick={() => { handleToggleVisibility(contextMenu?.el); handleCloseContextMenu(); }}>{(() => {
          let isVisible = true;
          if (contextMenu?.el?.type === 'text') isVisible = (contextMenu.el as TextElement).properties.visible !== false;
          if (contextMenu?.el?.type === 'image') isVisible = (contextMenu.el as ImageElement).properties.visible !== false;
          if (contextMenu?.el?.type === 'shape') isVisible = (contextMenu.el as ShapeElement).properties.visible !== false;
          if (contextMenu?.el?.type === 'drawing') isVisible = (contextMenu.el as DrawingElement).properties.visible !== false;
          if (contextMenu?.el?.type === 'qrcode') isVisible = (contextMenu.el as QRCodeElement).properties.visible !== false;
          return isVisible ? 'Masquer' : 'Afficher';
        })()}</MenuItem>
      </Menu>
      </Box>

      {/* Modale d'information BETA */}
      <ModalPortal isOpen={showBetaModal} onBackdropClick={() => setShowBetaModal(false)}>
        <Box
          sx={{
            bgcolor: '#FFF8F3',
            borderRadius: 4,
            boxShadow: '0 8px 32px rgba(255,107,44,0.18)',
            width: { xs: '95%', sm: 'auto' }, // Ajout du width responsif
            maxWidth: { xs: '95vw', sm: 400, md: 500 }, // Ajustement du maxWidth responsif
            p: { xs: 3, sm: 4 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            border: '2px solid #FF965E',
            position: 'relative',
            overflow: 'hidden',
            animation: 'fadeInBetaModal 0.5s cubic-bezier(.4,0,.2,1)',
            maxHeight: '90vh', // Ajout scroll interne
            overflowY: 'auto', // Ajout scroll interne
          }}
        >
          <Box sx={{
            bgcolor: '#FF965E',
            width: 64,
            height: 64,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 1,
            boxShadow: '0 2px 12px rgba(255,107,44,0.10)'
          }}>
            <span style={{ color: '#fff', fontSize: 38, display: 'flex', alignItems: 'center' }}>
              <svg width="38" height="38" viewBox="0 0 24 24" fill="none" style={{ display: 'block' }}><path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><circle cx="12" cy="16" r="1.2" fill="#fff"/></svg>
            </span>
          </Box>
          <Tooltip title="Cet éditeur est en développement. Certaines fonctionnalités peuvent évoluer ou être instables.">
            <Box sx={{
              bgcolor: '#FF6B2C',
              color: '#fff',
              px: 2.5,
              py: 0.7,
              borderRadius: 2,
              fontWeight: 800,
              fontSize: 15,
              textTransform: 'uppercase',
              boxShadow: '0 2px 8px rgba(255,107,44,0.10)',
              mb: 1,
              display: 'inline-block',
              cursor: 'pointer',
              letterSpacing: 2
            }}>
              BETA
            </Box>
          </Tooltip>
          <Typography variant="h6" sx={{ color: '#FF6B2C', textAlign: 'center', fontWeight: 700, mb: 0.5 }}>
            Éditeur de cartes en BETA
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary', textAlign: 'center', fontWeight: 400, maxWidth: 360, mb: 1, wordWrap: 'break-word' }}>
            Cet éditeur est en cours de développement. Certaines fonctionnalités peuvent évoluer ou être instables.<br />N'hésitez pas à faire vos retours pour l'améliorer !
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setShowBetaModal(false)}
            sx={{
              mt: 1,
              fontWeight: 700,
              borderRadius: 99,
              px: 5,
              py: 1.2,
              fontSize: 17,
              bgcolor: '#FF6B2C',
              boxShadow: '0 2px 8px rgba(255,107,44,0.10)',
              '&:hover': { bgcolor: '#FF7A35' }
            }}
            fullWidth
          >
            J'ai compris
          </Button>
          <style>{`
            @keyframes fadeInBetaModal {
              from { opacity: 0; transform: translateY(40px) scale(0.98); }
              to { opacity: 1; transform: translateY(0) scale(1); }
            }
          `}</style>
        </Box>
      </ModalPortal>
    </EditorThemeProvider>
  );
};

export default CardEditorPage;
