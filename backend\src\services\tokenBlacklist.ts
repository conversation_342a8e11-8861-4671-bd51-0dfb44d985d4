import { redis } from '../config/redis';
import { logSecurityEvent } from '../utils/logger';
import { SecurityEvent } from '../middleware/security';

export class TokenBlacklist {
  private static instance: TokenBlacklist;
  private readonly KEY_PREFIX = 'blacklist:';

  private constructor() {}

  static getInstance(): TokenBlacklist {
    if (!TokenBlacklist.instance) {
      TokenBlacklist.instance = new TokenBlacklist();
    }
    return TokenBlacklist.instance;
  }

  async add(jti: string, token: string, expiry: number): Promise<void> {
    const key = `${this.KEY_PREFIX}${jti}`;
    // L'expiration est calculée en secondes, Redis attend un timestamp en secondes ou une durée en secondes
    const ttlSeconds = Math.floor(expiry / 1000);
    await redis.set(key, token, 'EX', ttlSeconds);
  }

  async isBlacklisted(jti: string): Promise<boolean> {
    const key = `${this.KEY_PREFIX}${jti}`;
    const exists = await redis.exists(key);
    return exists === 1;
  }

  static async cleanup(): Promise<void> {
    // Le nettoyage des tokens blacklistés expirés est géré par la configuration d'expiration de Redis (EX)
    const securityEvent: SecurityEvent = {
      type: 'blacklist_cleanup',
      message: 'Blacklist cleanup initiated.',
      action: 'cleanup',
      status: 'success',
      ip: '127.0.0.1'
    };
    logSecurityEvent(securityEvent);
  }
}
