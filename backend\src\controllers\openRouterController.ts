import { Request, Response } from 'express';
import axios from 'axios';
import logger from '../utils/logger';
import { redis } from '../config/redis';

// Clé API OpenRouter
const MODERATION_API_KEY = process.env.MODERATION_API_KEY || '';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/auth/key';
const OPENROUTER_CREDITS_URL = 'https://openrouter.ai/api/v1/credits';
const MODERATION_API_MODEL_FREE = process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free';
const MODERATION_API_MODEL_PAYANT = process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-preview';
const MODERATION_API_MODEL_VISION_FREE = process.env.MODERATION_API_MODEL_VISION_FREE || 'google/gemini-2.0-flash-exp:free';
const MODERATION_API_MODEL_VISION_PAYANT = process.env.MODERATION_API_MODEL_VISION_PAYANT || 'google/gemini-2.5-flash-preview';
const DAILY_CALLS_LIMIT = 999;

// Préfixe pour le cache Redis
const OPENROUTER_CACHE_KEY = 'openrouter:api_info';
const OPENROUTER_CREDITS_CACHE_KEY = 'openrouter:credits_info';
const OPENROUTER_MODELS_CACHE_KEY = 'openrouter:models_info';
const CACHE_DURATION = 5 * 60; // 5 minutes

// Clé de cache pour les appels quotidiens
const OPENROUTER_DAILY_CALLS_CACHE_KEY = 'openrouter:daily_calls';

/**
 * Récupère les informations sur la clé API OpenRouter
 * (crédits restants, limites, etc.)
 */
/**
 * Récupère les crédits OpenRouter (total et utilisés)
 */
export async function getOpenRouterCredits(req: Request, res: Response) {
  try {
    // Vérifier si les informations sont en cache
    const cachedInfo = await redis.get(OPENROUTER_CREDITS_CACHE_KEY);

    if (cachedInfo) {
      logger.info('Récupération des crédits OpenRouter depuis le cache');
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedInfo),
        source: 'cache'
      });
    }

    // Si pas en cache, faire l'appel à l'API
    logger.info('Appel à l\'API OpenRouter pour récupérer les crédits');

    try {
      const response = await axios.get(OPENROUTER_CREDITS_URL, {
        headers: {
          'Authorization': `Bearer ${MODERATION_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Log de la réponse pour le débogage
      logger.info('Réponse de l\'API OpenRouter Credits:', {
        status: response.status,
        data: JSON.stringify(response.data)
      });

      // Extraire les données pertinentes
      let creditsInfo = response.data;

      // Vérifier si les données sont dans un sous-objet
      if (creditsInfo && typeof creditsInfo === 'object' && creditsInfo.data) {
        creditsInfo = creditsInfo.data;
      }

      // Mettre en cache les informations
      await redis.set(OPENROUTER_CREDITS_CACHE_KEY, JSON.stringify(creditsInfo), 'EX', CACHE_DURATION);

      // Retourner les informations
      return res.status(200).json({
        success: true,
        data: creditsInfo,
        source: 'api'
      });
    } catch (apiError: any) {
      logger.error('Erreur lors de l\'appel à l\'API OpenRouter Credits', {
        error: apiError.message,
        stack: apiError.stack
      });

      return res.status(200).json({
        success: true,
        data: {
          total_credits: 0,
          total_usage: 0
        },
        source: 'error',
        error: apiError.message
      });
    }
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des crédits OpenRouter', {
      error: error.message,
      stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des crédits OpenRouter',
      error: error.message
    });
  }
}

export async function getOpenRouterInfo(req: Request, res: Response) {
  try {
    // Vérifier si les informations sont en cache
    const cachedInfo = await redis.get(OPENROUTER_CACHE_KEY);

    if (cachedInfo) {
      logger.info('Récupération des informations OpenRouter depuis le cache');
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedInfo),
        source: 'cache'
      });
    }

    // Si pas en cache, faire l'appel à l'API
    logger.info('Appel à l\'API OpenRouter pour récupérer les informations de la clé');

    let apiInfo;
    try {
      logger.info(`Appel à l'API OpenRouter avec la clé: ${MODERATION_API_KEY.substring(0, 10)}...`);

      const response = await axios.get(OPENROUTER_API_URL, {
        headers: {
          'Authorization': `Bearer ${MODERATION_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Extraire les données pertinentes
      apiInfo = response.data;

      // Si la réponse a une structure différente de celle attendue, essayons de l'adapter
      if (apiInfo && typeof apiInfo === 'object') {
        // Vérifier si les données sont dans un sous-objet
        if (apiInfo.data && typeof apiInfo.data === 'object') {
          apiInfo = apiInfo.data;
        }

        // Créer une structure compatible si nécessaire
        const adaptedData = {
          label: apiInfo.label || apiInfo.name || 'Clé API OpenRouter',
          usage: apiInfo.usage || apiInfo.used || 0,
          limit: apiInfo.limit || apiInfo.max || null,
          is_free_tier: apiInfo.is_free_tier !== undefined ? apiInfo.is_free_tier : true,
          rate_limit: {
            requests: apiInfo.rate_limit?.requests ||
                     (apiInfo.rateLimit?.requests) ||
                     (apiInfo.rate?.requests) ||
                     10,
            interval: apiInfo.rate_limit?.interval ||
                     (apiInfo.rateLimit?.interval) ||
                     (apiInfo.rate?.interval) ||
                     '60s'
          }
        };

        // Utiliser les données adaptées
        apiInfo = adaptedData;

        logger.info('Données adaptées:', { adaptedData });
      }
    } catch (apiError: any) {
      logger.error('Erreur lors de l\'appel à l\'API OpenRouter', {
        error: apiError.message,
        stack: apiError.stack
      });

      // Fournir des données par défaut en cas d'erreur d'API
      return res.status(200).json({
        success: true,
        data: {
          label: 'Erreur de connexion à l\'API',
          usage: 0,
          limit: null,
          is_free_tier: undefined,
          rate_limit: {
            requests: 0,
            interval: '60s'
          }
        },
        source: 'error',
        error: apiError.message
      });
    }

    // Vérifier que la réponse contient les données attendues
    if (!apiInfo || typeof apiInfo !== 'object') {
      logger.warn('Réponse inattendue de l\'API OpenRouter', { apiInfo });
      return res.status(200).json({
        success: true,
        data: {
          label: 'Information non disponible',
          usage: 0,
          limit: null,
          is_free_tier: undefined,
          rate_limit: {
            requests: 0,
            interval: '60s'
          }
        },
        source: 'api',
        warning: 'Format de réponse inattendu'
      });
    }

    // Mettre en cache les informations
    await redis.set(OPENROUTER_CACHE_KEY, JSON.stringify(apiInfo), 'EX', CACHE_DURATION);

    // Retourner les informations
    return res.status(200).json({
      success: true,
      data: apiInfo,
      source: 'api'
    });
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des informations OpenRouter', {
      error: error.message,
      stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des informations OpenRouter',
      error: error.message
    });
  }
}

/**
 * Récupère les informations sur les modèles utilisés
 */
export async function getModelsInfo(req: Request, res: Response) {
  try {
    // Vérifier si les informations sont en cache
    const cachedInfo = await redis.get(OPENROUTER_MODELS_CACHE_KEY);

    if (cachedInfo) {
      logger.info('Récupération des informations sur les modèles depuis le cache');
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedInfo),
        source: 'cache'
      });
    }

    // Si pas en cache, préparer les données
    const modelsInfo = {
      free_model: MODERATION_API_MODEL_FREE,
      paid_model: MODERATION_API_MODEL_PAYANT,
      vision_free_model: MODERATION_API_MODEL_VISION_FREE,
      vision_paid_model: MODERATION_API_MODEL_VISION_PAYANT,
      daily_calls_limit: DAILY_CALLS_LIMIT
    };

    // Mettre en cache les informations
    await redis.set(OPENROUTER_MODELS_CACHE_KEY, JSON.stringify(modelsInfo), 'EX', CACHE_DURATION);

    return res.status(200).json({
      success: true,
      data: modelsInfo,
      source: 'api'
    });
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des informations sur les modèles:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des informations sur les modèles',
      error: error.message
    });
  }
}

/**
 * Vide le cache des appels quotidiens pour forcer une nouvelle requête
 * Cette fonction est utilisée pour synchroniser les compteurs entre les différentes pages
 */
export async function clearDailyCallsCache(_req: Request, res: Response) {
  try {
    // Vider le cache des appels quotidiens
    const exists = await redis.exists(OPENROUTER_DAILY_CALLS_CACHE_KEY);

    if (exists) {
      await redis.del(OPENROUTER_DAILY_CALLS_CACHE_KEY);
      logger.info('Cache des appels quotidiens vidé avec succès');

      return res.status(200).json({
        success: true,
        message: 'Cache des appels quotidiens vidé avec succès'
      });
    } else {
      return res.status(200).json({
        success: true,
        message: 'Aucun cache d\'appels quotidiens à vider'
      });
    }
  } catch (error: any) {
    logger.error('Erreur lors de la suppression du cache des appels quotidiens', {
      error: error.message,
      stack: error.stack
    });

    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du cache des appels quotidiens',
      error: error.message
    });
  }
}

/**
 * Sélectionne le modèle IA approprié pour la modération (texte ou image)
 * @param isImage true pour un modèle vision/image, false pour texte
 * @returns le nom du modèle à utiliser
 */
export async function selectAIModel(isVision: boolean = false): Promise<string> {
  const { getDailyRequestCount } = require('./openRouterStatsController');
  const count = await getDailyRequestCount();
  const now = Math.floor(Date.now() / 1000);
  const redisKey = `openrouter:model:ratelimit:${now}`;
  let currentSecondCount = 0;
  try {
    currentSecondCount = await redis.incr(redisKey);
    if (currentSecondCount === 1) {
      await redis.expire(redisKey, 2);
    }
  } catch (e) {
    if (isVision) {
      return process.env.MODERATION_API_MODEL_VISION_PAYANT || 'google/gemini-2.5-flash-preview';
    } else {
      return process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-preview';
    }
  }
  if (count >= (process.env.DAILY_CALLS_LIMIT || 999) || currentSecondCount > 10) {
    return isVision ? (process.env.MODERATION_API_MODEL_VISION_PAYANT || 'google/gemini-2.5-flash-preview') : (process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-preview');
  } else {
    return isVision ? (process.env.MODERATION_API_MODEL_VISION_FREE || 'google/gemini-2.0-flash-exp:free') : (process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free');
  }
}