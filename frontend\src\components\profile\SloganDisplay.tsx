import React from 'react';
import { motion } from 'framer-motion';

interface SloganDisplayProps {
  slogan?: string;
}

const SloganDisplay: React.FC<SloganDisplayProps> = ({ slogan }) => {
  if (!slogan) return null;

  return (
    <motion.div
      className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 mb-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-4 bg-[#FFF8F3] rounded-lg">
        <p className="text-lg font-medium text-gray-700 italic text-center">"{slogan}"</p>
      </div>
    </motion.div>
  );
};

export default SloganDisplay;
