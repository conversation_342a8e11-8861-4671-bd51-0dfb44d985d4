import React, { useEffect, useRef, useState, useCallback } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { styled } from '@mui/material/styles';
import { TextField, IconButton, Box, Paper } from '@mui/material';
import axios from 'axios';
import debounce from 'lodash/debounce';
import { notify } from '@/components/Notification';
import { MapPin, Search as SearchIcon, Plus as ZoomInIcon, Minus as ZoomOutIcon, Crosshair as LocationIcon } from 'lucide-react';
import logger from '@/utils/logger';
import { motion, AnimatePresence } from 'framer-motion';

export interface InterventionZone {
  center: [number, number];
  radius: number;
  adresse?: string;
}

interface InterventionZoneMapProps {
  center: [number, number];
  radius: number;
  onZoneChange: (zone: InterventionZone) => void;
  onAddressChange?: (address: {
    adresse: string;
    code_postal: string;
    ville: string;
    pays: string;
  }) => void;
}

const StyledPaper = styled(Paper)(({ theme }) => ({
  position: 'absolute',
  top: '10px',
  left: '10px',
  zIndex: 1000,
  padding: theme.spacing(1),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  borderRadius: '8px',
  width: 'calc(100% - 20px)',
  maxWidth: '400px',
}));

const ZoomControls = styled(Box)(({ theme }) => ({
  position: 'absolute',
  right: '10px',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 1000,
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(1),
}));

const StyledIconButton = styled(IconButton)(() => ({
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 1)',
  },
  '& svg': {
    width: '20px',
    height: '20px',
    color: '#FF6B2C',
  },
}));

const LocationIconStyled = styled(LocationIcon)(() => ({
  '@keyframes colorChange': {
    '0%': {
      color: '#FF6B2C',
    },
    '100%': {
      color: '#FFD1B8',
    },
  },
}));

// Ajout des composants d'animation
const PinDropEffect = styled(motion.div)(() => ({
  position: 'absolute',
  zIndex: 1001,
  pointerEvents: 'none',
  width: '40px',
  height: '40px',
  borderRadius: '50%',
  backgroundColor: 'rgba(255, 107, 44, 0.15)',
  boxShadow: '0 0 0 rgba(255, 107, 44, 0.6)',
}));

const ShockwaveEffect = styled(motion.div)(() => ({
  position: 'absolute',
  zIndex: 1000,
  pointerEvents: 'none',
  width: '40px',
  height: '40px',
  borderRadius: '50%',
  border: '2px solid #FF6B2C',
}));

// Ajout d'un composant stylisé pour le message en bas de la carte
const StatusMessage = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  bottom: '20px',
  left: '0',
  right: '0',
  marginLeft: 'auto',
  marginRight: 'auto',
  width: 'fit-content',
  zIndex: 1000,
  padding: theme.spacing(1.5, 2.5),
  backgroundColor: 'rgba(0, 0, 0, 0.75)',
  color: 'white',
  borderRadius: '12px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing(1.5),
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.25)',
  maxWidth: '90%',
  textAlign: 'center',
  fontWeight: 500,
  fontSize: '0.9rem',
  letterSpacing: '0.2px',
  border: '1px solid rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(4px)',
  WebkitBackdropFilter: 'blur(4px)',
  '& svg': {
    flexShrink: 0,
    color: '#FF6B2C',
  },
  '& span': {
    lineHeight: 1.4,
  }
}));

const InterventionZoneMap: React.FC<InterventionZoneMapProps> = ({ center, radius, onZoneChange, onAddressChange }) => {
  const mapRef = useRef<L.Map | null>(null);
  const circleRef = useRef<L.Circle | null>(null);
  const markerRef = useRef<L.Marker | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<Array<{ display_name: string; lat: string; lon: string }>>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isDraggingEnabled, setIsDraggingEnabled] = useState(false);
  const [locationCooldown, setLocationCooldown] = useState(false);

  // États pour les animations
  const [showPinDrop, setShowPinDrop] = useState(false);
  const [showShockwave, setShowShockwave] = useState(false);
  const [effectPosition, setEffectPosition] = useState({ x: 0, y: 0 });

  // Ajout d'un état pour le message de statut
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [showStatusMessage, setShowStatusMessage] = useState(false);

  // Fonction pour afficher un message temporaire
  const showTemporaryMessage = useCallback((message: string, duration: number = 3000) => {
    setStatusMessage(message);
    setShowStatusMessage(true);

    // Effacer le message après la durée spécifiée
    setTimeout(() => {
      setShowStatusMessage(false);
    }, duration);
  }, []);

  useEffect(() => {
    if (!mapRef.current) {
      mapRef.current = L.map('map', {
        zoomControl: false,
        dragging: isDraggingEnabled,
        scrollWheelZoom: isDraggingEnabled,
        doubleClickZoom: isDraggingEnabled,
        boxZoom: isDraggingEnabled,
        keyboard: isDraggingEnabled
      }).setView(center, 13);

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(mapRef.current);

      // Icône de punaise classique - optimisée pour les performances
      const customIcon = L.divIcon({
        className: 'map-pin-marker',
        html: `
          <div class="pin-container">
            <svg viewBox="0 0 24 36" width="24" height="36">
              <path d="M12 0C5.4 0 0 5.4 0 12c0 7.2 12 24 12 24s12-16.8 12-24c0-6.6-5.4-12-12-12z" fill="#FF6B2C" />
              <circle cx="12" cy="12" r="4" fill="white" />
            </svg>
            <div class="pin-shadow"></div>
          </div>
        `,
        iconSize: [24, 36],
        iconAnchor: [12, 36]
      });

      markerRef.current = L.marker(center, {
        draggable: isDraggingEnabled,
        icon: customIcon
      }).addTo(mapRef.current);

      circleRef.current = L.circle(center, {
        radius: radius,
        color: '#FF6B2C',
        fillColor: '#FF6B2C',
        fillOpacity: 0.2,
        interactive: isDraggingEnabled
      }).addTo(mapRef.current);

      // Optimisation: utiliser des variables pour stocker l'état du glissement
      let isDragging = false;
      let lastPos: L.LatLng | null = null;

      // Ajouter un événement pour le début du glissement
      markerRef.current.on('dragstart', () => {
        if (markerRef.current) {
          isDragging = true;
          const markerElement = markerRef.current.getElement();
          if (markerElement) {
            markerElement.classList.add('grabbing');
          }
        }
      });

      // Optimisation: mettre à jour le cercle pendant le glissement sans appels coûteux
      markerRef.current.on('drag', (e) => {
        if (isDragging && circleRef.current && mapRef.current) {
          const marker = e.target;
          const newPos = marker.getLatLng();

          // Mettre à jour le cercle
          circleRef.current.setLatLng(newPos);

          // Contourner complètement le système de rendu de Leaflet pour le marqueur
          // en positionnant directement l'élément DOM
          const markerElement = marker.getElement();
          if (markerElement) {
            // Calculer la position en pixels
            const pixelPoint = mapRef.current.latLngToLayerPoint(newPos);

            // Appliquer directement la transformation CSS pour un mouvement instantané
            markerElement.style.transform = `translate3d(${pixelPoint.x}px, ${pixelPoint.y}px, 0)`;
            markerElement.style.zIndex = '1000'; // S'assurer que le marqueur reste au-dessus
          }

          // Stocker la dernière position
          lastPos = newPos;
        }
      });

      // Ajouter un événement pour le mouvement de la carte
      // pour s'assurer que le marqueur reste synchronisé
      mapRef.current.on('move', () => {
        if (markerRef.current && !isDragging) {
          // Forcer la mise à jour de la position du marqueur quand la carte bouge
          const currentPos = markerRef.current.getLatLng();
          markerRef.current.setLatLng(currentPos);
        }
      });

      markerRef.current.on('dragend', (event) => {
        if (markerRef.current && circleRef.current) {
          isDragging = false;
          const newPos = markerRef.current.getLatLng();

          // Mettre à jour la zone et faire la géocodification inverse seulement à la fin
          updateZone(newPos);

          // Déclencher l'animation de chute d'épingle
          triggerPinDropAnimation(event);

          // Retirer la classe d'attrapage
          const markerElement = markerRef.current.getElement();
          if (markerElement) {
            markerElement.classList.remove('grabbing');
          }

          // Effectuer la géocodification inverse après un court délai
          // pour ne pas bloquer l'animation fluide
          setTimeout(() => {
            reverseGeocode(newPos.lat, newPos.lng);
          }, 100);
        }
      });

      mapRef.current.on('click', (e) => {
        if (!isDraggingEnabled) return;
        if (markerRef.current && circleRef.current) {
          const newPos = e.latlng;
          markerRef.current.setLatLng(newPos);
          circleRef.current.setLatLng(newPos);
          updateZone(newPos);
          reverseGeocode(newPos.lat, newPos.lng);

          // Attendre que la carte finisse son déplacement avant de déclencher l'effet
          setTimeout(() => {
            if (mapRef.current) {
              const pixelPoint = mapRef.current.latLngToContainerPoint(newPos);
              triggerPinDropEffects(pixelPoint.x, pixelPoint.y);
            }
          }, 500); // Délai augmenté à 1000ms (1 seconde) pour garantir que la carte est stabilisée
        }
      });
    }

    if (mapRef.current) {
      const map = mapRef.current;
      if (isDraggingEnabled) {
        map.dragging.enable();
        map.scrollWheelZoom.enable();
        map.doubleClickZoom.enable();
        map.boxZoom.enable();
        map.keyboard.enable();
      } else {
        map.dragging.disable();
        map.scrollWheelZoom.disable();
        map.doubleClickZoom.disable();
        map.boxZoom.disable();
        map.keyboard.disable();
      }
    }

    if (markerRef.current) {
      if (isDraggingEnabled) {
        markerRef.current.dragging?.enable();
      } else {
        markerRef.current.dragging?.disable();
      }
    }

    // Injecter les styles CSS pour le marqueur personnalisé
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .map-pin-marker {
        position: relative;
        will-change: transform; /* Optimisation des performances */
        transform: translate3d(0, 0, 0); /* Forcer l'accélération matérielle */
        backface-visibility: hidden; /* Réduire les problèmes de rendu */
        perspective: 1000; /* Améliorer le rendu 3D */
      }

      /* Désactiver les transitions par défaut de Leaflet */
      .leaflet-marker-icon,
      .leaflet-marker-shadow {
        transition: none !important;
      }

      .pin-container {
        position: relative;
        width: 24px;
        height: 36px;
        transform-origin: bottom center;
        will-change: transform; /* Optimisation des performances */
        transform: translate3d(0, 0, 0); /* Forcer l'accélération matérielle */
      }

      /* Supprimer les transitions pendant le glissement pour un suivi instantané */
      .leaflet-marker-dragging {
        transition: none !important;
        animation: none !important;
      }

      .leaflet-marker-dragging * {
        transition: none !important;
      }

      .pin-shadow {
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 4px;
        background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
        border-radius: 50%;
        opacity: 0.7;
        will-change: transform, opacity; /* Optimisation des performances */
      }

      /* Animation d'attrapage - optimisée */
      .leaflet-marker-icon.grabbing .pin-container {
        transform: translateY(-8px) scale(1.15);
        filter: drop-shadow(0 6px 3px rgba(0, 0, 0, 0.2));
      }

      .leaflet-marker-icon.grabbing .pin-shadow {
        opacity: 0.3;
        transform: translateX(-50%) scale(1.5);
      }

      /* Simplifier l'animation pendant le glissement pour plus de fluidité */
      .leaflet-marker-dragging .pin-container {
        transform: translateY(-5px);
      }

      .leaflet-marker-dragging .pin-shadow {
        opacity: 0.3;
      }

      .leaflet-marker-icon.dropped .pin-container {
        animation: drop 0.5s forwards cubic-bezier(0.175, 0.885, 0.32, 1.275);
      }

      .leaflet-marker-icon.dropped .pin-shadow {
        animation: shadow 0.5s forwards ease-out;
      }

      @keyframes drop {
        0% {
          transform: translateY(-20px);
        }
        50% {
          transform: translateY(3px);
        }
        75% {
          transform: translateY(-2px);
        }
        100% {
          transform: translateY(0);
        }
      }

      @keyframes shadow {
        0% {
          opacity: 0;
          transform: translateX(-50%) scale(0.5);
        }
        50% {
          opacity: 1;
          transform: translateX(-50%) scale(1.2);
        }
        100% {
          opacity: 0.7;
          transform: translateX(-50%) scale(1);
        }
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      document.head.removeChild(styleElement);
    };
  }, [isDraggingEnabled]);

  useEffect(() => {
    if (mapRef.current && markerRef.current && circleRef.current) {
      const newLatLng = L.latLng(center[0], center[1]);
      markerRef.current.setLatLng(newLatLng);
      circleRef.current.setLatLng(newLatLng);
      circleRef.current.setRadius(radius);
      mapRef.current.setView(newLatLng, mapRef.current.getZoom());
    }
  }, [center, radius]);

  const updateZone = (position: L.LatLng) => {
    if (circleRef.current) {
      onZoneChange({
        center: [position.lat, position.lng],
        radius: circleRef.current.getRadius()
      });
    }
  };

  const handleSearch = debounce(async (query: string) => {
    if (query.length < 3) return;
    setIsSearching(true);
    showTemporaryMessage('Recherche en cours...');

    try {
      const response = await axios.get(
        `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(query)}&limit=5`,
        { timeout: 5000 }
      );

      if (response.data && response.data.features) {
        const suggestions = response.data.features.map((feature: any) => ({
          lat: feature.geometry.coordinates[1],
          lon: feature.geometry.coordinates[0],
          display_name: feature.properties.label || feature.properties.name
        }));
        setSuggestions(suggestions);

        if (suggestions.length === 0) {
          showTemporaryMessage('Aucun résultat trouvé pour cette recherche', 4000);
        } else {
          showTemporaryMessage(`${suggestions.length} résultat(s) trouvé(s)`, 4000);
        }
      } else {
        setSuggestions([]);
        showTemporaryMessage('Aucun résultat trouvé pour cette recherche', 4000);
      }
    } catch (error) {
      logger.error('Erreur lors de la recherche d\'adresse:', error);
      notify('Erreur lors de la recherche d\'adresse', 'error');
      showTemporaryMessage('Erreur lors de la recherche d\'adresse', 4000);
    } finally {
      setIsSearching(false);
    }
  }, 1000);

  const handleSearchSubmit = () => {
    if (searchQuery.length >= 3) {
      handleSearch(searchQuery);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearchSubmit();
    }
  };

  const formatAddress = (road: string | undefined, houseNumber: string | undefined): string => {
    if (!road) return '';
    if (!houseNumber) return road;
    return `${houseNumber} ${road}`;
  };

  const reverseGeocode = async (lat: number, lon: number) => {
    showTemporaryMessage('Récupération de l\'adresse...');
    try {
      const response = await axios.get(
        `https://api-adresse.data.gouv.fr/reverse/?lon=${lon}&lat=${lat}`,
        { timeout: 5000 }
      );

      if (response.data && response.data.features && response.data.features.length > 0) {
        const feature = response.data.features[0];
        const properties = feature.properties;
        const displayName = properties.label || properties.name || 'Adresse trouvée';

        onZoneChange({
          center: [lat, lon],
          radius: circleRef.current?.getRadius() || radius,
          adresse: displayName
        });

        if (onAddressChange) {
          const address = {
            adresse: formatAddress(properties.street, properties.housenumber),
            code_postal: properties.postcode || '',
            ville: properties.city || '',
            pays: 'France'
          };
          onAddressChange(address);
          showTemporaryMessage(`Adresse trouvée : ${address.adresse}, ${address.code_postal} ${address.ville}`, 10000);
        } else {
          showTemporaryMessage('Adresse trouvée', 10000);
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la géocodification inverse:', error);
      showTemporaryMessage('Erreur lors de la récupération de l\'adresse', 4000);
    }
  };

  const handleLocationSelect = (lat: string, lon: string, displayName: string) => {
    if (mapRef.current && markerRef.current && circleRef.current) {
      const latNum = parseFloat(lat);
      const lonNum = parseFloat(lon);
      const newPos = L.latLng(latNum, lonNum);
      mapRef.current.setView(newPos, 15);
      markerRef.current.setLatLng(newPos);
      circleRef.current.setLatLng(newPos);
      onZoneChange({
        center: [latNum, lonNum],
        radius: circleRef.current.getRadius(),
        adresse: displayName
      });
      setSuggestions([]);
      setSearchQuery(displayName);
      showTemporaryMessage('Emplacement sélectionné', 10000);

      reverseGeocode(latNum, lonNum);
    }
  };

  const handleZoomIn = () => {
    if (mapRef.current && isDraggingEnabled) {
      mapRef.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapRef.current && isDraggingEnabled) {
      mapRef.current.zoomOut();
    }
  };

  const handleGeolocation = useCallback(() => {
    if (locationCooldown) {
      return;
    }

    setLocationCooldown(true);
    showTemporaryMessage('Récupération de votre position...');

    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          if (mapRef.current && markerRef.current && circleRef.current) {
            const newPos = L.latLng(latitude, longitude);
            mapRef.current.setView(newPos, 15);
            markerRef.current.setLatLng(newPos);
            circleRef.current.setLatLng(newPos);
            // Utiliser directement la fonction reverseGeocode qui utilise maintenant api-adresse.data.gouv.fr
            reverseGeocode(latitude, longitude);
            notify('Position récupérée avec succès', 'success');
            showTemporaryMessage('Position récupérée avec succès', 10000);
          }
        },
        () => {
          notify('Erreur lors de la récupération de votre position', 'error');
          showTemporaryMessage('Erreur lors de la récupération de votre position', 10000);
          setLocationCooldown(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0
        }
      );
    } else {
      notify('La géolocalisation n\'est pas supportée par votre navigateur', 'error');
      showTemporaryMessage('La géolocalisation n\'est pas supportée par votre navigateur', 10000);
      setLocationCooldown(false);
    }

    setTimeout(() => {
      setLocationCooldown(false);
    }, 10000); // Cooldown de 10 seconds sur la geolocalisation
  }, [locationCooldown, setLocationCooldown, mapRef, markerRef, circleRef, notify, showTemporaryMessage]);

  // Fonction pour déclencher l'animation de chute d'épingle
  const triggerPinDropAnimation = (event: L.DragEndEvent) => {
    if (!mapRef.current) return;

    const marker = event.target;
    const latLng = marker.getLatLng();

    // Ajouter temporairement une classe pour l'animation
    const markerElement = marker.getElement();
    if (markerElement) {
      markerElement.classList.add('dropped');
      setTimeout(() => {
        markerElement.classList.remove('dropped');
      }, 500);
    }

    // Attendre que la carte finisse son déplacement avant de déclencher l'effet
    setTimeout(() => {
      if (mapRef.current) {
        const pixelPoint = mapRef.current.latLngToContainerPoint(latLng);
        triggerPinDropEffects(pixelPoint.x, pixelPoint.y);
      }
    }, 500); // Délai pour laisser la carte se stabiliser
  };

  // Fonction pour déclencher les effets visuels
  const triggerPinDropEffects = (x: number, y: number) => {
    setEffectPosition({ x, y });

    // Effet de choc
    setShowPinDrop(true);
    setTimeout(() => setShowPinDrop(false), 600);

    // Effet d'onde de choc
    setShowShockwave(true);
    setTimeout(() => setShowShockwave(false), 800);
  };

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%' }}>
      {/* Animations d'effets */}
      <AnimatePresence>
        {showPinDrop && (
          <PinDropEffect
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 1.5],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 0.6,
              times: [0, 0.3, 1],
              ease: "easeOut"
            }}
            style={{
              left: effectPosition.x - 20,
              top: effectPosition.y - 20,
            }}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showShockwave && (
          <ShockwaveEffect
            initial={{ opacity: 0.8, scale: 0.5 }}
            animate={{
              opacity: 0,
              scale: 3,
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 0.8,
              ease: "easeOut"
            }}
            style={{
              left: effectPosition.x - 20,
              top: effectPosition.y - 20,
            }}
          />
        )}
      </AnimatePresence>

      <StyledPaper elevation={3}>
        <TextField
          fullWidth
          size="small"
          placeholder="Rechercher une adresse..."
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
          }}
          onKeyPress={handleKeyPress}
          InputProps={{
            startAdornment: <SearchIcon className="h-4 w-4 text-gray-400 mr-2" />,
            sx: { backgroundColor: 'white' }
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: '#FF6B2C',
              },
              '&:hover fieldset': {
                borderColor: '#FF6B2C',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#FF6B2C',
              },
            },
          }}
        />
        <StyledIconButton onClick={handleSearchSubmit} size="small" disabled={isSearching || searchQuery.length < 3}>
          <SearchIcon />
        </StyledIconButton>
        <StyledIconButton
          onClick={handleGeolocation}
          size="small"
          disabled={locationCooldown}
          sx={{
            opacity: locationCooldown ? 0.7 : 1,
            transition: 'opacity 0.3s',
            position: 'relative',
            '& svg': {
              animation: locationCooldown ? 'colorChange 10s linear' : 'none',
              color: locationCooldown ? '#FFD1B8' : '#FF6B2C',
              transition: 'color 0.3s',
            },
            '&::after': locationCooldown ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '50%',
              border: '2px solid #FF6B2C',
              animation: 'cooldown 10s linear',
            } : {},
            '@keyframes cooldown': {
              '0%': {
                clipPath: 'polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%)',
              },
              '100%': {
                clipPath: 'polygon(50% 50%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%)',
              },
            },
          }}
        >
          <LocationIconStyled />
        </StyledIconButton>
      </StyledPaper>

      {/* Bouton de verrouillage - version desktop */}
      <div className="absolute top-4 right-4 z-[1000] hidden sm:block">
        <button
          onClick={() => setIsDraggingEnabled(!isDraggingEnabled)}
          className={`px-4 py-2 rounded-lg shadow-lg transition-colors ${
            isDraggingEnabled ? 'bg-white text-gray-700' : 'bg-[#FF6B2C] text-white'
          }`}
        >
          {isDraggingEnabled ? 'Verrouiller la carte' : 'Déverrouiller la carte'}
        </button>
      </div>

      {!isDraggingEnabled && (
        <div className="absolute left-0 right-0 mx-auto top-1/2 -translate-y-1/2 z-[1000] flex flex-col items-center gap-4">
          {/* Message de verrouillage */}
          <div className="bg-black/60 text-white px-4 py-2 rounded-lg flex items-center gap-2 pointer-events-none shadow-lg w-fit max-w-[90%] sm:max-w-none justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginBottom: '5px' }}>
              <path d="M6 13c2 2 4 2 6 0 2-2 4-2 6 0"/>
              <path d="M6 17c2 2 4 2 6 0 2-2 4-2 6 0"/>
            </svg>
            <span className="text-sm whitespace-wrap font-medium">Déverrouillez la carte pour la déplacer</span>
          </div>

          {/* Bouton de verrouillage - version mobile */}
          <div className="block sm:hidden">
            <button
              onClick={() => setIsDraggingEnabled(!isDraggingEnabled)}
              className={`px-4 py-2 rounded-lg shadow-lg transition-colors ${
                isDraggingEnabled ? 'bg-white text-gray-700' : 'bg-[#FF6B2C] text-white'
              }`}
            >
              {isDraggingEnabled ? 'Verrouiller la carte' : 'Déverrouiller la carte'}
            </button>
          </div>
        </div>
      )}

      {isDraggingEnabled && (
        /* Bouton de verrouillage mobile quand la carte est déverrouillée */
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-[1000] block sm:hidden">
          <button
            onClick={() => setIsDraggingEnabled(!isDraggingEnabled)}
            className="px-4 py-2 rounded-lg shadow-lg transition-colors bg-white text-gray-700 hover:bg-gray-50"
          >
            Verrouiller la carte
          </button>
        </div>
      )}

      {suggestions.length > 0 && (
        <Paper
          sx={{
            position: 'absolute',
            top: '60px',
            left: '10px',
            zIndex: 1000,
            maxWidth: '400px',
            width: 'calc(100% - 20px)',
            maxHeight: '200px',
            overflowY: 'auto'
          }}
        >
          {suggestions.map((suggestion, index) => (
            <Box
              key={index}
              sx={{
                p: 1,
                cursor: 'pointer',
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
              }}
              onClick={() => handleLocationSelect(
                suggestion.lat,
                suggestion.lon,
                suggestion.display_name
              )}
            >
              <MapPin className="h-4 w-4 text-[#FF6B2C] inline-block mr-2" />
              {suggestion.display_name}
            </Box>
          ))}
        </Paper>
      )}

      <ZoomControls>
        <StyledIconButton
          onClick={handleZoomIn}
          size="small"
          disabled={!isDraggingEnabled}
          sx={{ opacity: isDraggingEnabled ? 1 : 0.5 }}
        >
          <ZoomInIcon />
        </StyledIconButton>
        <StyledIconButton
          onClick={handleZoomOut}
          size="small"
          disabled={!isDraggingEnabled}
          sx={{ opacity: isDraggingEnabled ? 1 : 0.5 }}
        >
          <ZoomOutIcon />
        </StyledIconButton>
      </ZoomControls>

      <div id="map" style={{ height: '100%', width: '100%', borderRadius: '8px' }} />

      {/* Message de statut en bas de la carte */}
      <AnimatePresence>
        {showStatusMessage && statusMessage && (
          <StatusMessage
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{
              duration: 0.3,
              ease: "easeOut"
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10" fill="rgba(255, 107, 44, 0.15)"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
            <span>{statusMessage}</span>
          </StatusMessage>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InterventionZoneMap;