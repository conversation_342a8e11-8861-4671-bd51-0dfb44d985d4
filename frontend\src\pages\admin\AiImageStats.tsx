import React, { useState, useEffect } from 'react';
import {
  Box, Container, Typography, Paper, Grid, FormControl, InputLabel,
  MenuItem, Select, Button,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  CircularProgress, Chip, Alert, TextField, useTheme, useMediaQuery, TableSortLabel, Dialog, DialogTitle, DialogContent, DialogActions
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, Pie<PERSON><PERSON>, Pie, Cell } from 'recharts';
import { ImagePlus, Sparkles, Calendar, Filter, Database, BarChart2, RefreshCcw } from 'lucide-react';
import { notify } from '../../components/Notification';
import { formatDate } from '../../utils/dateUtils';
import logger from '../../utils/logger';
import { fetchAiImageStats, AiImageStats as AiImageStatsType } from './api/aiImageStats';
import type { SelectChangeEvent } from '@mui/material/Select';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  cost: '#00C853', // Vert pour les coûts
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Traduction des types d'images
const purposeTranslations: Record<string, string> = {
  'profile_picture': 'Photo de profil', 
  'banner_picture': 'Bannière de profil',
  'mission_image': 'Image de mission',
  'gallery_photo': 'Photo de galerie',
  'featured_photo': 'Photo mise en avant',
  'card_editor': 'Carte de visite/flyer'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
  }
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

const ChipStyled = styled(Chip)(({ theme }) => ({
  borderRadius: '6px',
  fontWeight: 500,
  height: '24px',
  '&.MuiChip-colorPrimary': {
    backgroundColor: `${COLORS.primary}30`,
    color: COLORS.primary,
  },
  '&.MuiChip-colorSecondary': {
    backgroundColor: `${COLORS.secondary}30`,
    color: COLORS.secondary,
  },
  '&.MuiChip-colorDefault': {
    backgroundColor: `${COLORS.neutral}30`,
    color: COLORS.neutral,
  },
  '&.MuiChip-colorSuccess': {
    backgroundColor: `${COLORS.success}30`,
    color: COLORS.success,
  },
  '&.MuiChip-colorInfo': {
    backgroundColor: `${COLORS.info}30`,
    color: COLORS.info,
  },
}));

const AiImageStats: React.FC = () => {
  const [stats, setStats] = useState<AiImageStatsType | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [purposeFilter, setPurposeFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [openPrompt, setOpenPrompt] = useState<{ prompt: string; imageUrl?: string } | null>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Charger les statistiques
  const loadStats = async () => {
    try {
      setLoading(true);

      // Utiliser la fonction d'API
      const statsData = await fetchAiImageStats({
        purpose: purposeFilter !== 'all' ? purposeFilter : undefined,
        startDate: dateRange.start,
        endDate: dateRange.end
      });

      setStats(statsData);
    } catch (error) {
      logger.error('Erreur lors du chargement des statistiques d\'images IA:', error);
      setError('Erreur lors du chargement des statistiques');
      notify('Erreur lors du chargement des statistiques', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Charger les statistiques au chargement du composant
  useEffect(() => {
    loadStats();
  }, [purposeFilter, dateRange, refreshTrigger]);

  // Préparer les données pour le graphique en camembert
  const preparePieData = () => {
    if (!stats) return [];

    return Object.entries(stats.byPurpose).map(([purpose, count], index) => ({
      name: purposeTranslations[purpose] || purpose,
      value: count,
      color: COLORS[Object.keys(COLORS)[index % Object.keys(COLORS).length] as keyof typeof COLORS]
    }));
  };

  // Gérer le changement de filtre
  const handlePurposeFilterChange = (event: SelectChangeEvent<string>) => {
    setPurposeFilter(event.target.value);
  };

  // Gérer le changement de date
  const handleDateChange = (type: 'start' | 'end', value: string) => {
    setDateRange(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // Gérer le rafraîchissement des données
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Ajout fonction de reset des filtres
  const handleResetFilters = () => {
    setPurposeFilter('all');
    setDateRange({
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    });
  };

  // Obtenir le libellé de la période
  const getPeriodLabel = () => {
    return `${formatDate(dateRange.start)} - ${formatDate(dateRange.end)}`;
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortDirection('asc');
    }
  };

  const sortedGenerations = React.useMemo(() => {
    if (!stats?.recentGenerations) return [];
    const sorted = [...stats.recentGenerations].sort((a, b) => {
      const aObj = a as Record<string, any>;
      const bObj = b as Record<string, any>;
      let aValue = aObj[sortBy];
      let bValue = bObj[sortBy];
      // Pour la date, on convertit en timestamp
      if (sortBy === 'created_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      // Pour le coût, on convertit en nombre
      if (sortBy === 'cost') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      }
      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [stats?.recentGenerations, sortBy, sortDirection]);

  // Rendu des statistiques globales
  const renderGlobalStats = () => {
    return (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <MetricCard>
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <Box sx={{ position: 'relative', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Total des générations</Typography>
                <IconBox>
                  <ImagePlus size={20} />
                </IconBox>
              </Box>
              <Box sx={{ mt: 'auto' }}>
                <Typography variant="h3" fontWeight="700" color={COLORS.primary}>
                  {stats?.totalCount || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary" mt={1}>
                  Nombre total d'images générées
                </Typography>
              </Box>
            </Box>
          </MetricCard>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <MetricCard>
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <Box sx={{ position: 'relative', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Coût total</Typography>
                <IconBox color={COLORS.cost}>
                  <Sparkles size={20} />
                </IconBox>
              </Box>
              <Box sx={{ mt: 'auto' }}>
                <Typography variant="h3" fontWeight="700" color={COLORS.cost}>
                  {stats?.totalCost ? `${stats.totalCost.toFixed(4)} $` : '0 $'}
                </Typography>
                <Typography variant="body2" color="text.secondary" mt={1}>
                  Coût total de génération
                </Typography>
              </Box>
            </Box>
          </MetricCard>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <MetricCard>
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <Box sx={{ position: 'relative', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">Moyenne journalière</Typography>
                <IconBox color={COLORS.info}>
                  <Calendar size={20} />
                </IconBox>
              </Box>
              <Box sx={{ mt: 'auto' }}>
                <Typography variant="h3" fontWeight="700" color={COLORS.info}>
                  {stats?.byDate && stats.byDate.length > 0
                    ? (stats.totalCount / stats.byDate.length).toFixed(1)
                    : '0'}
                </Typography>
                <Typography variant="body2" color="text.secondary" mt={1}>
                  Images générées par jour
                </Typography>
              </Box>
            </Box>
          </MetricCard>
        </Grid>
      </Grid>
    );
  };

  // Rendu des filtres
  const renderFilters = () => {
    return (
      <StyledPaper sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Filter size={18} color={COLORS.primary} />
          <SectionTitle sx={{ ml: 1, mb: 0 }}>Filtres</SectionTitle>
          <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
            <StyledButton
              variant="outlined"
              startIcon={<RefreshCcw size={16} />}
              onClick={handleRefresh}
            >
              Actualiser
            </StyledButton>
            <StyledButton
              variant="outlined"
              onClick={handleResetFilters}
            >
              Réinitialiser
            </StyledButton>
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 4 }}>
            <StyledFormControl fullWidth variant="outlined">
              <InputLabel id="purpose-filter-label">Type d'image</InputLabel>
              <Select
                labelId="purpose-filter-label"
                value={purposeFilter}
                onChange={handlePurposeFilterChange}
                label="Type d'image"
              >
                <MenuItem value="all">Tous les types</MenuItem>
                <MenuItem value="profile_picture">Photos de profil</MenuItem>
                <MenuItem value="banner_picture">Bannières de profil</MenuItem>
                <MenuItem value="mission_image">Images de mission</MenuItem>
                <MenuItem value="gallery_photo">Photos de galerie</MenuItem>
                <MenuItem value="featured_photo">Photos mises en avant</MenuItem>
                <MenuItem value="card_editor">Carte de visite/flyer</MenuItem>
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              label="Date de début"
              type="date"
              fullWidth
              value={dateRange.start}
              onChange={(e) => handleDateChange('start', e.target.value)}
              InputLabelProps={{ shrink: true }}
              InputProps={{ sx: { borderRadius: '8px' } }}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              label="Date de fin"
              type="date"
              fullWidth
              value={dateRange.end}
              onChange={(e) => handleDateChange('end', e.target.value)}
              InputLabelProps={{ shrink: true }}
              InputProps={{ sx: { borderRadius: '8px' } }}
            />
          </Grid>
        </Grid>
      </StyledPaper>
    );
  };

  // Rendu des graphiques
  const renderCharts = () => {
    return (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 8 }}>
          <StyledPaper>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <SectionTitle>Évolution des générations ({getPeriodLabel()})</SectionTitle>
              <Box display="flex" alignItems="center">
                <BarChart2 size={18} color={COLORS.primary} />
              </Box>
            </Box>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={stats?.byDate || []}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke={COLORS.borderColor} />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip
                  contentStyle={{
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                    border: 'none'
                  }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke={COLORS.primary}
                  name="Nombre de générations"
                  strokeWidth={2}
                  dot={{ r: 4, fill: COLORS.primary }}
                  activeDot={{ r: 6, fill: COLORS.primary }}
                />
              </LineChart>
            </ResponsiveContainer>
          </StyledPaper>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <StyledPaper>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <SectionTitle>Répartition par type ({getPeriodLabel()})</SectionTitle>
            </Box>
            <Box sx={{ width: '100%', height: isMobile ? 290 : 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={preparePieData()}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={isMobile ? 70 : 100}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={isMobile ? undefined : ({ name, percent }) => {
                      const value = (percent * 100).toFixed(0);
                      // if (Number(value) < 5) return null; // Ne pas afficher les petits pourcentages
                      return `${name}: ${value}%`;
                    }}
                  >
                    {preparePieData().map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip
                    formatter={(value: number) => [`${value} générations`, 'Quantité']}
                    contentStyle={{
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      border: 'none'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </StyledPaper>
        </Grid>
      </Grid>
    );
  };

  // Rendu du tableau des générations récentes
  const renderRecentGenerationsTable = () => {
    return (
      <StyledPaper>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box display="flex" alignItems="center">
            <Database size={18} color={COLORS.primary} />
            <SectionTitle sx={{ ml: 1, mb: 0 }}>Générations récentes</SectionTitle>
          </Box>
        </Box>

        <StyledTableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'created_at'}
                    direction={sortBy === 'created_at' ? sortDirection : 'asc'}
                    onClick={() => handleSort('created_at')}
                  >
                    Date
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'user_id'}
                    direction={sortBy === 'user_id' ? sortDirection : 'asc'}
                    onClick={() => handleSort('user_id')}
                  >
                    Utilisateur
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'nom'}
                    direction={sortBy === 'nom' ? sortDirection : 'asc'}
                    onClick={() => handleSort('nom')}
                  >
                    Nom
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'prenom'}
                    direction={sortBy === 'prenom' ? sortDirection : 'asc'}
                    onClick={() => handleSort('prenom')}
                  >
                    Prénom
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'email'}
                    direction={sortBy === 'email' ? sortDirection : 'asc'}
                    onClick={() => handleSort('email')}
                  >
                    Email
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'purpose'}
                    direction={sortBy === 'purpose' ? sortDirection : 'asc'}
                    onClick={() => handleSort('purpose')}
                  >
                    Type
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'prompt'}
                    direction={sortBy === 'prompt' ? sortDirection : 'asc'}
                    onClick={() => handleSort('prompt')}
                  >
                    Prompt
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={sortBy === 'cost'}
                    direction={sortBy === 'cost' ? sortDirection : 'asc'}
                    onClick={() => handleSort('cost')}
                  >
                    Coût
                  </TableSortLabel>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedGenerations.length > 0 ? (
                sortedGenerations.map((generation) => (
                  <TableRow key={generation.id}>
                    <TableCell>{formatDate(generation.created_at)}</TableCell>
                    <TableCell>{generation.user_id || 'Inconnu'}</TableCell>
                    <TableCell>{generation.nom || '-'}</TableCell>
                    <TableCell>{generation.prenom || '-'}</TableCell>
                    <TableCell>{generation.email || '-'}</TableCell>
                    <TableCell>
                      <ChipStyled
                        label={purposeTranslations[generation.purpose] || generation.purpose}
                        color="primary"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', cursor: 'pointer' }}
                        onClick={() => setOpenPrompt({ prompt: generation.prompt, imageUrl: generation.image_url })}
                        title="Afficher le prompt complet"
                      >
                        {generation.prompt}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        fontWeight="600"
                        color={COLORS.cost}
                      >
                        {generation.cost.toFixed(4)} $
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} align="center">Aucune génération trouvée</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </StyledTableContainer>
      </StyledPaper>
    );
  };

  // Modale pour afficher le prompt complet et l'image générée
  const renderPromptModal = () => {
    return (
      <Dialog open={!!openPrompt} onClose={() => setOpenPrompt(null)} maxWidth="md" fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            border: '1px solid #f3f3f3',
            background: '#fff',
            boxShadow: '0 6px 32px 0 rgba(255,107,44,0.10)',
          }
        }}
      >
        <DialogTitle
          sx={{
            fontWeight: 600,
            fontSize: '1.1rem',
            color: '#222',
            pb: 1,
            pl: 2,
            borderLeft: `4px solid ${COLORS.primary}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          Détails de la génération d'image
          <Sparkles size={18} color={COLORS.primary} style={{ marginLeft: 8, opacity: 0.7 }} />
        </DialogTitle>
        <DialogContent sx={{ p: 2, pt: 0 }}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: openPrompt?.imageUrl ? 6 : 12 }}>
              <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>Prompt utilisé</Typography>
              <Box
                sx={{
                  background: COLORS.accent,
                  borderRadius: 1.5,
                  p: 2,
                  fontFamily: 'Menlo, monospace, sans-serif',
                  fontSize: '1rem',
                  color: '#222',
                  maxHeight: 220,
                  overflowY: 'auto',
                  border: `1px solid ${COLORS.primary}22`,
                  mb: 1
                }}
              >
                {openPrompt?.prompt}
              </Box>
            </Grid>

            {openPrompt?.imageUrl && (
              <Grid size={{ xs: 12, md: 6 }}>
                <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>Image générée</Typography>
                <Box
                  sx={{
                    borderRadius: 1.5,
                    border: `1px solid ${COLORS.primary}22`,
                    overflow: 'hidden',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: 220,
                    position: 'relative',
                    backgroundColor: '#f5f5f5'
                  }}
                >
                  {openPrompt.imageUrl ? (
                    <img
                      src={openPrompt.imageUrl}
                      alt="Image générée par IA"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectFit: 'contain'
                      }}
                      onError={(e) => {
                        // En cas d'erreur de chargement, afficher un message
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          const errorMsg = document.createElement('div');
                          errorMsg.textContent = "Image non disponible";
                          errorMsg.style.color = '#666';
                          errorMsg.style.textAlign = 'center';
                          errorMsg.style.width = '100%';
                          parent.appendChild(errorMsg);
                        }
                      }}
                    />
                  ) : (
                    <Typography color="text.secondary" align="center">
                      Image non disponible
                    </Typography>
                  )}
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', p: 2 }}>
          <Button onClick={() => setOpenPrompt(null)} variant="contained" sx={{ borderRadius: 2, fontWeight: 500, px: 3, background: COLORS.primary, color: '#fff', '&:hover': { background: COLORS.secondary } }} autoFocus>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress color="primary" />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mt: 4, mb: 4 }}>
          <PageTitle variant="h4">
            Statistiques de génération d'images IA
          </PageTitle>
          <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 4, mb: 4 }}>
        <PageTitle variant="h4">
          Statistiques de génération d'images IA
        </PageTitle>
        <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 4 }}>
          Suivi détaillé de la génération d'images IA sur la plateforme, coûts et utilisations.
        </Typography>

        {/* Statistiques globales */}
        {renderGlobalStats()}

        {/* Filtres */}
        {renderFilters()}

        {/* Graphiques */}
        {renderCharts()}

        {/* Tableau des générations récentes */}
        {renderRecentGenerationsTable()}

        {/* Modale pour afficher le prompt complet */}
        {openPrompt && renderPromptModal()}
      </Box>
    </Container>
  );
};

export default AiImageStats;
