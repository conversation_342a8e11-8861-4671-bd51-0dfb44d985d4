import axios from 'axios';
import { logger } from './logger';

// Interface pour les résultats de validation d'adresse
export interface AddressValidationResult {
  isValid: boolean;
  adresse: string;
  code_postal: string;
  ville: string;
  pays: string;
  coordinates?: [number, number];
  score?: number;
}

// Interface pour les suggestions de ville
export interface CitySuggestion {
  nom: string;
  code_postal: string;
  coordinates: [number, number];
  score: number;
}

// Fonction pour normaliser les chaînes de caractères (insensible à la casse)
export const normalizeString = (str: string): string => {
  return str.toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
    .replace(/[^a-z0-9\s]/g, '') // Garder seulement lettres, chiffres et espaces
    .replace(/\s+/g, ' ') // Normaliser les espaces multiples
    .trim();
};

// Service centralisé pour la validation d'adresse avec api-adresse.data.gouv.fr
export const validateAddressWithGouv = async (address: string): Promise<AddressValidationResult> => {
  try {
    if (!address || address.length < 3) {
      return {
        isValid: false,
        adresse: '',
        code_postal: '',
        ville: '',
        pays: ''
      };
    }

    const response = await axios.get(
      `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(address)}&limit=1`,
      { timeout: 5000 }
    );

    if (response.data && response.data.features && response.data.features.length > 0) {
      const feature = response.data.features[0];
      const properties = feature.properties;
      const coordinates = feature.geometry.coordinates;

      // Extraire les informations d'adresse
      const numero = properties.housenumber || '';
      const rue = properties.street || properties.name || '';
      const ville = properties.city || '';
      const codePostal = properties.postcode || '';

      return {
        isValid: true,
        adresse: `${numero} ${rue}`.trim(),
        code_postal: codePostal,
        ville: ville,
        pays: 'France',
        coordinates: [coordinates[1], coordinates[0]], // Inverser lat/lng
        score: properties.score || 0
      };
    }

    return {
      isValid: false,
      adresse: '',
      code_postal: '',
      ville: '',
      pays: ''
    };
  } catch (error) {
    logger.error('Erreur lors de la validation d\'adresse avec api-adresse.data.gouv.fr:', error);
    return {
      isValid: false,
      adresse: '',
      code_postal: '',
      ville: '',
      pays: ''
    };
  }
};

// Fonction pour détecter si la query est un code postal
export const isPostalCode = (query: string): boolean => {
  // Code postal français : 5 chiffres
  const postalCodeRegex = /^\d{5}$/;
  return postalCodeRegex.test(query.trim());
};

// Fonction pour rechercher des villes avec suggestions (insensible à la casse + codes postaux)
export const searchCitiesWithSuggestions = async (query: string, limit: number = 5): Promise<CitySuggestion[]> => {
  try {
    if (!query || query.length < 2) {
      return [];
    }

    const trimmedQuery = query.trim();

    // Détecter si c'est un code postal complet (5 chiffres exactement)
    if (isPostalCode(trimmedQuery)) {
      // Pour un code postal, essayer plusieurs méthodes
      const urls = [
        // Méthode 1: Sans filtre type
        `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(trimmedQuery)}&limit=${limit}`,
        // Méthode 2: Avec filtre municipality
        `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(trimmedQuery)}&type=municipality&limit=${limit}`,
        // Méthode 3: Avec filtre postcode (si supporté)
        `https://api-adresse.data.gouv.fr/search/?q=*&postcode=${encodeURIComponent(trimmedQuery)}&type=municipality&limit=${limit}`
      ];

      for (const searchUrl of urls) {
        try {
          const response = await axios.get(searchUrl, { timeout: 5000 });

          if (response.data && response.data.features && response.data.features.length > 0) {
            // Filtrer pour ne garder que les communes
            const features = response.data.features.filter((feature: any) =>
              feature.properties.type === 'municipality' ||
              feature.properties.city ||
              feature.properties.name
            );

            if (features.length > 0) {
              return features.map((feature: any) => ({
                nom: feature.properties.city || feature.properties.name,
                code_postal: feature.properties.postcode || '',
                coordinates: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]] as [number, number],
                score: feature.properties.score || 0
              })).filter((result: any) => result.nom);
            }
          }
        } catch (urlError) {
          // Continuer avec l'URL suivante
          logger.warn(`Erreur avec URL ${searchUrl}:`, urlError);
        }
      }

      return []; // Aucune méthode n'a fonctionné
    } else {
      // Recherche normale par nom de ville
      const searchUrl = /^\d{2,4}$/.test(trimmedQuery)
        ? `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(trimmedQuery)}&limit=${limit}`
        : `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(trimmedQuery)}&type=municipality&limit=${limit}`;

      const response = await axios.get(searchUrl, { timeout: 5000 });

      if (response.data && response.data.features) {
        return response.data.features.map((feature: any) => ({
          nom: feature.properties.city || feature.properties.name,
          code_postal: feature.properties.postcode || '',
          coordinates: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]] as [number, number],
          score: feature.properties.score || 0
        })).filter((result: any) => result.nom);
      }
    }

    return [];
  } catch (error) {
    logger.error('Erreur lors de la recherche de villes:', error);
    return [];
  }
};

// Fonction pour rechercher par ville ET code postal combinés
export const searchCityWithPostalCode = async (cityName: string, postalCode: string, limit: number = 5): Promise<CitySuggestion[]> => {
  try {
    if (!cityName || !postalCode) {
      return [];
    }

    // Rechercher avec le nom de ville et filtrer par code postal
    const response = await axios.get(
      `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(cityName)}&postcode=${encodeURIComponent(postalCode)}&type=municipality&limit=${limit}`,
      { timeout: 5000 }
    );

    if (response.data && response.data.features) {
      return response.data.features.map((feature: any) => ({
        nom: feature.properties.city || feature.properties.name,
        code_postal: feature.properties.postcode || '',
        coordinates: [feature.geometry.coordinates[1], feature.geometry.coordinates[0]] as [number, number],
        score: feature.properties.score || 0
      }));
    }

    return [];
  } catch (error) {
    logger.error('Erreur lors de la recherche ville + code postal:', error);
    return [];
  }
};

// Fonction pour valider spécifiquement une ville
export const validateCity = async (cityName: string): Promise<CitySuggestion | null> => {
  try {
    const suggestions = await searchCitiesWithSuggestions(cityName, 1);

    if (suggestions.length > 0) {
      const suggestion = suggestions[0];
      // Vérifier si la correspondance est suffisamment bonne
      const normalizedQuery = normalizeString(cityName);
      const normalizedResult = normalizeString(suggestion.nom);

      if (normalizedResult.includes(normalizedQuery) || normalizedQuery.includes(normalizedResult)) {
        return suggestion;
      }
    }

    return null;
  } catch (error) {
    logger.error('Erreur lors de la validation de ville:', error);
    return null;
  }
};

export const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
  try {
    const response = await axios.get(
      `https://api-adresse.data.gouv.fr/reverse/?lon=${lng}&lat=${lat}`,
      { timeout: 5000 }
    );

    if (response.data && response.data.features && response.data.features.length > 0) {
      const feature = response.data.features[0];
      const properties = feature.properties;

      // Construire l'adresse complète
      const numero = properties.housenumber || '';
      const rue = properties.street || properties.name || '';
      const ville = properties.city || '';
      const codePostal = properties.postcode || '';

      const adresseComplete = [numero, rue, codePostal, ville].filter(Boolean).join(' ');
      return adresseComplete || properties.label || 'Adresse non trouvée';
    }

    return 'Adresse non trouvée';
  } catch (error) {
    logger.error('Erreur lors du géocodage inverse:', error);
    return 'Erreur lors de la récupération de l\'adresse';
  }
};

export const getCityFromPostalCode = async (postalCode: string): Promise<string> => {
  try {
    const response = await axios.get(
      `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(postalCode)}&type=municipality&limit=1`,
      { timeout: 5000 }
    );

    if (response.data && response.data.features && response.data.features.length > 0) {
      const feature = response.data.features[0];
      return feature.properties.city || feature.properties.name || 'Ville inconnue';
    }

    return 'Ville inconnue';
  } catch (error) {
    logger.error('Erreur lors de la récupération de la ville:', error);
    return 'Ville inconnue';
  }
};