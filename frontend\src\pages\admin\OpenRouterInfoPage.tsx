import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Card,
  Chip,
  Divider,
  Button,
  LinearProgress,
  Grid,
  Container,
  useTheme,
  Paper,
  Stack
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  getOpenRouterInfo,
  OpenRouterInfo,
  getOpenRouterCredits,
  OpenRouterCredits,
  getDailyRequestCount,
  getModelsInfo,
  ModelsInfo,
  clearDailyCallsCache
} from '../../services/openRouterApi';
import { notify } from '../../components/Notification';
import {
  RefreshCw,
  Key,
  AlertCircle,
  Clock,
  Database,
  Gauge,
  Shield,
  CheckCircle2,
  Info,
  BarChart,
  Cpu,
  Zap,
  DollarSign,
  Activity
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion } from 'framer-motion';
import logger from '@/utils/logger';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  position: 'absolute',
  top: '-15px',
  right: '20px',
  color: color,
}));

const OpenRouterInfoPage: React.FC = () => {
  const [apiInfo, setApiInfo] = useState<OpenRouterInfo | null>(null);
  const [creditsInfo, setCreditsInfo] = useState<OpenRouterCredits | null>(null);
  const [modelsInfo, setModelsInfo] = useState<ModelsInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [dailyRequestCount, setDailyRequestCount] = useState<number>(0);
  const [dailyRequestDate, setDailyRequestDate] = useState<string>('');
  const theme = useTheme();

  // Fonction pour vider le cache des appels quotidiens
  const handleClearDailyCallsCache = async () => {
    try {
      const success = await clearDailyCallsCache();
      if (success) {
        // Recharger les informations après avoir vidé le cache
        loadApiInfo();
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression du cache des appels quotidiens', error);
    }
  };

  // Fonction pour charger les informations de l'API
  const loadApiInfo = async () => {
    setLoading(true);
    setError(null);

    try {
      // Récupérer les informations de la clé API
      const info = await getOpenRouterInfo();
      logger.info('Informations OpenRouter reçues:', info);

      // Récupérer les crédits
      const credits = await getOpenRouterCredits();
      logger.info('Crédits OpenRouter reçus:', credits);

      // Récupérer les informations sur les modèles
      const models = await getModelsInfo();
      logger.info('Informations sur les modèles reçues:', models);

      if (info) {
        setApiInfo(info);
        setLastRefresh(new Date());
      } else {
        setError('Impossible de récupérer les informations de l\'API OpenRouter');
      }

      if (credits) {
        setCreditsInfo(credits);
      }

      if (models) {
        setModelsInfo(models);
      }

      const dailyCallsCount = await getDailyRequestCount();
      setDailyRequestCount(dailyCallsCount);
      setDailyRequestDate(new Date().toISOString().split('T')[0]);
    } catch (err: any) {
      console.error('Erreur lors de la récupération des informations OpenRouter:', err);
      setError(err.message || 'Une erreur est survenue lors de la récupération des informations');
      notify('Erreur lors de la récupération des informations OpenRouter', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Charger les informations au chargement de la page
  useEffect(() => {
    loadApiInfo();
  }, []);

  // Fonction pour formater l'intervalle de rate limit
  const formatRateLimitInterval = (interval: string): string => {
    if (!interval) return 'intervalle inconnu';
    if (interval.endsWith('s')) return `${interval.slice(0, -1)} secondes`;
    if (interval.endsWith('m')) return `${interval.slice(0, -1)} minutes`;
    if (interval.endsWith('h')) return `${interval.slice(0, -1)} heures`;
    if (interval.endsWith('d')) return `${interval.slice(0, -1)} jours`;
    return interval;
  };

  // Calculer le pourcentage d'utilisation des crédits
  const calculateUsagePercentage = (): number => {
    if (!apiInfo || apiInfo.data.limit === undefined || apiInfo.data.limit === null || apiInfo.data.usage === undefined) return 0;
    return (apiInfo.data.usage / apiInfo.data.limit) * 100;
  };

  // Déterminer la couleur de la barre de progression
  const getProgressColor = (): string => {
    const percentage = calculateUsagePercentage();
    if (percentage < 50) return COLORS.success;
    if (percentage < 80) return COLORS.warning;
    return COLORS.error;
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <PageTitle variant="h4">
            Informations API OpenRouter
          </PageTitle>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Statistiques d'utilisation et configuration de l'API OpenRouter utilisée pour la modération
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<RefreshCw size={18} />}
          onClick={loadApiInfo}
          disabled={loading}
          sx={{
            bgcolor: COLORS.primary,
            '&:hover': {
              bgcolor: COLORS.secondary
            },
            borderRadius: '8px',
            px: 3
          }}
        >
          Actualiser
        </Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', my: 8, height: '200px' }}>
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      ) : error ? (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: '12px',
            border: `1px solid ${COLORS.error}20`
          }}
        >
          {error}
        </Alert>
      ) : !apiInfo ? (
        <Alert
          severity="info"
          sx={{
            mb: 3,
            borderRadius: '12px',
            border: `1px solid ${COLORS.info}20`
          }}
        >
          Aucune information disponible
        </Alert>
      ) : (
        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
        >
          <Grid container spacing={3}>
            {/* Carte d'utilisation des crédits */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <motion.div variants={item} style={{ height: '100%' }}>
                <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                  <IconBox color={getProgressColor()}>
                    <Database size={24} />
                  </IconBox>
                  <SectionTitle>Utilisation des crédits</SectionTitle>

                  <Typography variant="h4" sx={{ fontWeight: 700, color: getProgressColor(), mb: 1 }}>
                    {apiInfo.data.usage !== undefined && apiInfo.data.limit
                      ? apiInfo.data.limit - apiInfo.data.usage
                      : apiInfo.data.limit === null
                        ? 'Illimité'
                        : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Crédits restants
                  </Typography>

                  {apiInfo.data.usage !== undefined && apiInfo.data.limit && (
                    <>
                      <Box sx={{ mb: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" fontWeight="medium">
                          {apiInfo.data.usage} / {apiInfo.data.limit}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 'bold',
                            color: getProgressColor()
                          }}
                        >
                          {calculateUsagePercentage().toFixed(1)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={Math.min(calculateUsagePercentage(), 100)}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: `${getProgressColor()}20`,
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getProgressColor(),
                            borderRadius: 4
                          }
                        }}
                      />
                    </>
                  )}

                  <Box sx={{ mt: 3 }}>
                    <Chip
                      size="small"
                      label={apiInfo.data.is_free_tier !== undefined ? (apiInfo.data.is_free_tier ? 'Compte gratuit' : 'Compte premium') : 'Type inconnu'}
                      icon={apiInfo.data.is_free_tier !== undefined ? (apiInfo.data.is_free_tier ? <Info size={14} /> : <CheckCircle2 size={14} />) : <AlertCircle size={14} />}
                      sx={{
                        backgroundColor: apiInfo.data.is_free_tier !== undefined ? (apiInfo.data.is_free_tier ? 'rgba(100, 116, 139, 0.1)' : `${COLORS.primary}15`) : 'rgba(100, 116, 139, 0.1)',
                        color: apiInfo.data.is_free_tier !== undefined ? (apiInfo.data.is_free_tier ? COLORS.neutral : COLORS.primary) : COLORS.neutral,
                        borderRadius: '16px',
                        fontWeight: 500,
                        border: apiInfo.data.is_free_tier !== undefined ? (apiInfo.data.is_free_tier ? 'none' : `1px solid ${COLORS.primary}40`) : 'none'
                      }}
                    />
                  </Box>
                </StyledPaper>
              </motion.div>
            </Grid>

            {/* Carte d'informations générales */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <motion.div variants={item} style={{ height: '100%' }}>
                <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                  <IconBox>
                    <Key size={24} />
                  </IconBox>
                  <SectionTitle>Informations de la clé</SectionTitle>

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Nom de la clé
                    </Typography>
                    <Typography variant="h6" fontWeight="medium">
                      {apiInfo.data.label || 'Non défini'}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2, borderColor: 'rgba(0,0,0,0.08)' }} />

                  <Box sx={{ mb: 1 }}>
                    <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 0.5 }}>
                      <Clock size={16} color={theme.palette.text.secondary} />
                      <Typography variant="body2" color="text.secondary">
                        Dernière actualisation
                      </Typography>
                    </Stack>
                    <Typography variant="body1" fontWeight="medium">
                      {formatDistanceToNow(lastRefresh, { addSuffix: true, locale: fr })}
                    </Typography>
                  </Box>

                  <Box sx={{ mt: 3 }}>
                    <Chip
                      size="small"
                      icon={<BarChart size={14} />}
                      label={`Source: ${apiInfo.source === 'cache' ? 'Cache (Redis)' : 'API OpenRouter'}`}
                      sx={{
                        backgroundColor: 'rgba(100, 116, 139, 0.1)',
                        color: COLORS.neutral,
                        borderRadius: '16px'
                      }}
                    />
                  </Box>
                </StyledPaper>
              </motion.div>
            </Grid>

            {/* Carte de limite de taux */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <motion.div variants={item} style={{ height: '100%' }}>
                <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                  <IconBox color={COLORS.info}>
                    <Gauge size={24} />
                  </IconBox>
                  <SectionTitle>Limites de taux</SectionTitle>

                  {apiInfo.data.rate_limit && apiInfo.data.rate_limit.requests ? (
                    <>
                      <Typography variant="h4" sx={{ fontWeight: 700, color: COLORS.info, mb: 1 }}>
                        {apiInfo.data.rate_limit.requests}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Requêtes par {formatRateLimitInterval(apiInfo.data.rate_limit.interval || '60s')}
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="body1" sx={{ mb: 3 }}>
                      Information non disponible
                    </Typography>
                  )}

                  <Alert
                    severity="info"
                    icon={<Shield size={18} />}
                    sx={{
                      mt: 2,
                      borderRadius: '12px',
                      border: `1px solid ${COLORS.info}20`,
                      '& .MuiAlert-icon': {
                        color: COLORS.info
                      }
                    }}
                  >
                    <Typography variant="body2">
                      Ces limites s'appliquent à la modération de contenu et à la validation automatique
                    </Typography>
                  </Alert>
                </StyledPaper>
              </motion.div>
            </Grid>

            {/* Carte des crédits totaux */}
            <Grid size={{ xs: 12, md: 6 }}>
              <motion.div variants={item} style={{ height: '100%' }}>
                <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                  <IconBox color={COLORS.success}>
                    <DollarSign size={24} />
                  </IconBox>
                  <SectionTitle>Crédits OpenRouter (solde)</SectionTitle>

                  {creditsInfo && creditsInfo.data ? (
                    <>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Total des crédits achetés
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color={COLORS.success}>
                          {creditsInfo.data.total_credits !== undefined ? creditsInfo.data.total_credits.toFixed(5) : 'N/A'}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Total des crédits utilisés
                        </Typography>
                        <Typography variant="h4" fontWeight="bold" color={COLORS.primary}>
                          {creditsInfo.data.total_usage !== undefined ? creditsInfo.data.total_usage.toFixed(5) : 'N/A'}
                        </Typography>
                      </Box>

                      {creditsInfo.data.total_credits !== undefined && creditsInfo.data.total_usage !== undefined && (
                        <Box sx={{ mb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                            <Typography variant="body2" fontWeight="medium">
                              Solde restant
                            </Typography>
                            <Typography variant="body2" fontWeight="bold" color={COLORS.success}>
                              {(creditsInfo.data.total_credits - creditsInfo.data.total_usage).toFixed(5)}
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min((creditsInfo.data.total_usage / creditsInfo.data.total_credits) * 100, 100)}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              backgroundColor: `${COLORS.success}20`,
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: COLORS.success,
                                borderRadius: 4
                              }
                            }}
                          />
                        </Box>
                      )}
                    </>
                  ) : (
                    <Typography variant="body1">
                      Information non disponible
                    </Typography>
                  )}
                </StyledPaper>
              </motion.div>
            </Grid>

            {/* Carte des appels quotidiens */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <motion.div variants={item} style={{ height: '100%' }}>
                <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                  <IconBox color={COLORS.info}>
                    <Activity size={24} />
                  </IconBox>
                  <SectionTitle>Appels quotidiens</SectionTitle>

                  <Typography variant="h4" sx={{ fontWeight: 700, color: COLORS.info, mb: 1 }}>
                    {dailyRequestCount}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    sur {modelsInfo?.data.daily_calls_limit || 999} appels gratuits
                  </Typography>

                  <Box sx={{ mb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" fontWeight="medium">
                        Utilisation gratuite
                      </Typography>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        color={dailyRequestCount > (modelsInfo?.data.daily_calls_limit || 999) * 0.9 ? COLORS.error : COLORS.info}
                      >
                        {dailyRequestCount ? ((dailyRequestCount / (modelsInfo?.data.daily_calls_limit || 999)) * 100).toFixed(1) : 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(dailyRequestCount || 0) / (modelsInfo?.data.daily_calls_limit || 999) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: `${COLORS.info}20`,
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: dailyRequestCount > (modelsInfo?.data.daily_calls_limit || 999) * 0.9 ? COLORS.error : COLORS.info,
                          borderRadius: 4
                        },
                      }}
                    />
                  </Box>

                  <Alert
                    severity="info"
                    icon={<Info size={18} />}
                    sx={{
                      mt: 3,
                      borderRadius: '12px',
                      border: `1px solid ${COLORS.info}20`,
                      '& .MuiAlert-icon': {
                        color: COLORS.info
                      }
                    }}
                  >
                    <Typography variant="body2">
                      Le modèle <strong>{modelsInfo?.data.free_model || 'meta-llama/llama-3.3-70b-instruct:free'}</strong> est utilisé pour les {modelsInfo?.data.daily_calls_limit || 999} premiers appels de modération de texte.<br />
                      Le modèle <strong>{modelsInfo?.data.vision_free_model || 'google/gemini-2.0-flash-exp:free'}</strong> est utilisé pour les {modelsInfo?.data.daily_calls_limit || 999} premiers appels de modération d'images.<br />
                      Au-delà, les modèles payants seront automatiquement utilisés.
                    </Typography>
                  </Alert>

                  <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip
                      size="small"
                      icon={<Clock size={14} />}
                      label={`Aujourd'hui (${dailyRequestDate || 'N/A'})`}
                      sx={{
                        backgroundColor: 'rgba(100, 116, 139, 0.1)',
                        color: COLORS.neutral,
                        borderRadius: '16px'
                      }}
                    />
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={handleClearDailyCallsCache}
                      sx={{
                        fontSize: '0.7rem',
                        borderRadius: '8px',
                        borderColor: `${COLORS.info}50`,
                        color: COLORS.info,
                        '&:hover': {
                          borderColor: COLORS.info,
                          backgroundColor: `${COLORS.info}10`
                        }
                      }}
                    >
                      Synchroniser
                    </Button>
                  </Box>
                </StyledPaper>
              </motion.div>
            </Grid>

            {/* Carte des fonctionnalités API */}
            <Grid size={{ xs: 12 }}>
              <motion.div variants={item}>
                <StyledPaper>
                  <SectionTitle>Fonctionnalités disponibles</SectionTitle>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <Card
                        elevation={0}
                        sx={{
                          p: 2,
                          border: `1px solid ${COLORS.borderColor}`,
                          borderRadius: '12px',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                          }
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: '12px',
                              bgcolor: `${COLORS.primary}15`,
                              display: 'flex'
                            }}
                          >
                            <Shield size={24} color={COLORS.primary} />
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" fontWeight={600}>
                              Modération
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Contenu utilisateur
                            </Typography>
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <Card
                        elevation={0}
                        sx={{
                          p: 2,
                          border: `1px solid ${COLORS.borderColor}`,
                          borderRadius: '12px',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                          }
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: '12px',
                              bgcolor: `${COLORS.info}15`,
                              display: 'flex'
                            }}
                          >
                            <Cpu size={24} color={COLORS.info} />
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" fontWeight={600}>
                              Validation
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Auto-vérification
                            </Typography>
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <Card
                        elevation={0}
                        sx={{
                          p: 2,
                          border: `1px solid ${COLORS.borderColor}`,
                          borderRadius: '12px',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                          }
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: '12px',
                              bgcolor: `${COLORS.success}15`,
                              display: 'flex'
                            }}
                          >
                            <Zap size={24} color={COLORS.success} />
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" fontWeight={600}>
                              Optimisation
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Cache intégré
                            </Typography>
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                      <Card
                        elevation={0}
                        sx={{
                          p: 2,
                          border: `1px solid ${COLORS.borderColor}`,
                          borderRadius: '12px',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                          }
                        }}
                      >
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Box
                            sx={{
                              p: 1.5,
                              borderRadius: '12px',
                              bgcolor: `${COLORS.accent}50`,
                              display: 'flex'
                            }}
                          >
                            <AlertCircle size={24} color={COLORS.warning} />
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" fontWeight={600}>
                              Supervision
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Monitoring avancé
                            </Typography>
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>
                  </Grid>
                </StyledPaper>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}
    </Container>
  );
};

export default OpenRouterInfoPage;
