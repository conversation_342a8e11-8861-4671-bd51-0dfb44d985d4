import React, { useEffect, useState } from 'react';
import { Image, Transformer } from 'react-konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { QRCodeElement } from '../../../types/cardEditor';
import QRCode from 'qrcode';

interface QRCodeElementProps {
  element: QRCodeElement;
  isSelected: boolean;
  onSelect: () => void;
  onDragStart?: (e: KonvaEventObject<DragEvent>) => void;
  onDragMove?: (e: KonvaEventObject<DragEvent>) => void;
  onDragEnd: (e: KonvaEventObject<DragEvent>) => void;
  isEditable?: boolean;
  onContextMenu?: (e: any) => void;
}

const QRCodeElementComponent: React.FC<QRCodeElementProps> = ({
  element,
  isSelected,
  onSelect,
  onDragStart,
  onDragMove = () => {},
  onDragEnd = () => {},
  isEditable = true,
  onContextMenu
}) => {
  const qrCodeRef = React.useRef<any>(null);
  const transformerRef = React.useRef<any>(null);
  const [qrImage, setQrImage] = useState<HTMLImageElement | null>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Générer l'image du QR code
  useEffect(() => {
    if (element.properties.data) {
      try {
        // S'assurer que les données ne sont pas vides
        const data = element.properties.data.trim() || 'https://jobpartiel.fr';
        let fill = element.properties.fill || '#000000';
        let background = element.properties.background || '#FFFFFF';

        // Vérifier et corriger les couleurs transparentes ou invalides
        if (fill === 'transparent' || fill === '' || !fill) {
          fill = '#000000';
        }
        if (background === 'transparent' || background === '' || !background) {
          background = '#FFFFFF';
        }

        // Valider que les couleurs sont des hex valides
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (!hexColorRegex.test(fill)) {
          fill = '#000000';
        }
        if (!hexColorRegex.test(background)) {
          background = '#FFFFFF';
        }

        // Options pour la génération du QR code
        const options = {
          errorCorrectionLevel: 'H' as const,
          margin: 1,
          width: 200,
          color: {
            dark: fill,
            light: background
          }
        };

        // Générer le QR code en tant que Data URL
        QRCode.toDataURL(data, options)
          .then((url: string) => {
            const img = new window.Image();
            img.src = url;
            img.onload = () => {
              setQrImage(img);
            };
            img.onerror = () => {
              console.error('Erreur lors du chargement du QR code');

              // Essayer avec des options de secours si la première tentative échoue
              const fallbackOptions = {
                errorCorrectionLevel: 'M' as const,
                margin: 1,
                width: 200,
                color: {
                  dark: '#000000',
                  light: '#FFFFFF'
                }
              };

              QRCode.toDataURL('https://jobpartiel.fr', fallbackOptions)
                .then((fallbackUrl: string) => {
                  const fallbackImg = new window.Image();
                  fallbackImg.src = fallbackUrl;
                  fallbackImg.onload = () => {
                    setQrImage(fallbackImg);
                  };
                })
                .catch((fallbackError: Error) => {
                  console.error('Erreur lors de la génération du QR code de secours:', fallbackError);
                });
            };
          })
          .catch((error: Error) => {
            console.error('Erreur lors de la génération du QR code:', error);
          });
      } catch (error) {
        console.error('Erreur lors de la création du QR code:', error);
      }
    }
  }, [element.properties.data, element.properties.fill, element.properties.background]);

  // Attacher le transformer au QR code s'il est sélectionné
  useEffect(() => {
    if (isSelected && transformerRef.current && qrCodeRef.current) {
      transformerRef.current.nodes([qrCodeRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const handleTransformEnd = (e: any) => {
    if (!qrCodeRef.current) return;
    const node = qrCodeRef.current;
    let newRotation = node.rotation();
    newRotation = ((newRotation % 360) + 360) % 360;
    if (onDragEnd) {
      onDragEnd({
        ...e,
        target: {
          ...node,
          rotation: () => newRotation,
          width: () => node.width(),
          height: () => node.height()
        }
      });
    }
  };

  // Gérer le survol
  const handleMouseEnter = () => {
    if (isEditable && !isSelected) {
      setIsHovered(true);
      document.body.style.cursor = 'pointer';
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    document.body.style.cursor = 'default';
  };

  // Effet de survol
  const hoverEffect = isHovered ? {
    stroke: '#0096FF', // Couleur du contour
    strokeWidth: 1, // Épaisseur du contour
    // Désactiver les propriétés d'ombre si elles étaient définies ici
    shadowColor: undefined,
    shadowBlur: undefined,
    shadowOffset: undefined,
    shadowOpacity: undefined,
  } : {};

  if (!qrImage) {
    return null;
  }

  return (
    <>
      <Image
        ref={qrCodeRef}
        x={element.x}
        y={element.y}
        width={element.width || 100}
        height={element.height || 100}
        image={qrImage}
        draggable={isEditable}
        onClick={onSelect}
        onTap={onSelect}
        onDragStart={onDragStart}
        onDragMove={onDragMove}
        onDragEnd={onDragEnd}
        perfectDrawEnabled={false}
        rotation={element.rotation || 0}
        onContextMenu={onContextMenu}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...(isHovered ? hoverEffect : {})}
      />
      {isSelected && isEditable && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox: any, newBox: any) => {
            // Limiter la taille minimale
            if (newBox.width < 50 || newBox.height < 50) {
              return oldBox;
            }
            return newBox;
          }}
          rotateEnabled={true}
          enabledAnchors={[
            'top-left', 'top-right', 'bottom-left', 'bottom-right'
          ]}
          onTransformEnd={handleTransformEnd}
        />
      )}
    </>
  );
};

export default QRCodeElementComponent;
