import React, { useEffect, useState } from 'react';
import { Image as ImageIcon, Shield, CheckCircle, Eye } from 'lucide-react';

interface ImageModerationStatusProps {
  isLoading: boolean;
  imageUrl?: string;
  title?: string;
  showPreview?: boolean;
  onCancel?: () => void;
}

// Messages amusants et rassurants pour l'attente
const loadingMessages = [
  "Nous scannons votre magnifique photo...",
  "Nos petits robots examinent votre image...",
  "Vérification en cours, encore un instant...",
  "Analyse des pixels avec attention...",
  "Notre IA fait connaissance avec votre photo...",
  "Presque terminé, merci de votre patience !",
  "Nous vérifions que tout est parfait...",
  "Vos futures missions vous attendent...",
  "Votre photo va impressionner les clients !",
  "L'image parfaite pour trouver des jobis 💰",
  "Un beau profil attire plus de missions !",
  "Nos jardiniers virtuels arrosent votre photo...",
  "Votre image sera bientôt prête pour briller !",
  "Nos bricoleurs algorithmiques s'activent...",
  "Chaque pixel compte pour votre réputation !",
  "Préparation de votre profil de super jobbeur...",
  "En train de polir votre image professionnelle...",
  "On fait briller votre photo de mille feux...",
  "Les jobis vont pleuvoir avec cette photo !",
  "Le succès commence par une belle image de profil...",
  "Nos chats de garde examinent l'image...",
  "Nos toutous analysent votre photo avec soin...",
  "Un peu de patience, la réussite est au bout...",
  "Bientôt prêt à conquérir le monde des services !",
  "Jardinage, bricolage ou garde d'animaux ? Vous serez au top !",
];

const ImageModerationStatus: React.FC<ImageModerationStatusProps> = ({
  isLoading,
  imageUrl,
  title = "Modération de l'image",
  showPreview = true,
  onCancel
}) => {
  const [currentMessage, setCurrentMessage] = useState(loadingMessages[0]);
  const [progress, setProgress] = useState(0);
  const [animationStep, setAnimationStep] = useState(0);

  // Gestion de l'animation des messages et de la barre de progression
  useEffect(() => {
    if (!isLoading) return;

    // Animation des messages
    const messageInterval = setInterval(() => {
      setCurrentMessage(prev => {
        const currentIndex = loadingMessages.indexOf(prev);
        const nextIndex = (currentIndex + 1) % loadingMessages.length;
        return loadingMessages[nextIndex];
      });
    }, 3000);

    // Animation de la barre de progression
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        // Augmente progressivement jusqu'à 95% pour donner l'impression que ça avance
        if (prev < 95) {
          return prev + Math.random() * 10;
        }
        return prev;
      });
    }, 800);

    // Animation des icônes
    const stepInterval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 4);
    }, 1000);

    return () => {
      clearInterval(messageInterval);
      clearInterval(progressInterval);
      clearInterval(stepInterval);
    };
  }, [isLoading]);

  // Réinitialise la progression quand isLoading devient false
  useEffect(() => {
    if (!isLoading) {
      setProgress(100);
    } else {
      setProgress(10); // Commence à 10% pour montrer que le processus a démarré
    }
  }, [isLoading]);

  return (
    <div className="flex flex-col items-center justify-center p-4 overflow-y-auto">
      <div className="w-full max-w-md">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">{title}</h3>

          {/* Prévisualisation de l'image avec effet de scintillement pendant le chargement */}
          {showPreview && imageUrl && (
            <div className="relative w-32 h-32 sm:w-40 sm:h-40 mx-auto mb-6 rounded-full overflow-hidden border-4 border-white shadow-lg">
              <img
                src={imageUrl}
                alt="Prévisualisation"
                className={`w-full h-full object-cover ${isLoading ? 'animate-pulse' : ''}`}
              />
              {isLoading && (
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" style={{ backgroundSize: '200% 100%' }}></div>
              )}
            </div>
          )}

          {/* État de la modération avec animation améliorée */}
          <div className="mb-6">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center">
                {/* Animation des icônes */}
                <div className="relative h-12 w-12 sm:h-16 sm:w-16 mb-4">
                  <Shield className={`absolute inset-0 h-full w-full text-[#FF6B2C] transition-opacity duration-500 ${animationStep === 0 ? 'opacity-100' : 'opacity-0'}`} />
                  <Eye className={`absolute inset-0 h-full w-full text-[#FF6B2C] transition-opacity duration-500 ${animationStep === 1 ? 'opacity-100' : 'opacity-0'}`} />
                  <CheckCircle className={`absolute inset-0 h-full w-full text-[#FF6B2C] transition-opacity duration-500 ${animationStep === 2 ? 'opacity-100' : 'opacity-0'}`} />
                  <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-500 ${animationStep === 3 ? 'opacity-100' : 'opacity-0'}`}>
                    <div className="animate-spin rounded-full h-8 w-8 sm:h-10 sm:w-10 border-4 border-[#FF6B2C]/20 border-l-[#FF6B2C]"></div>
                  </div>
                </div>

                {/* Message dynamique */}
                <div className="h-12 sm:h-16 flex items-center justify-center">
                  <p className="text-gray-600 text-sm sm:text-base transition-all duration-700 animate-fadeIn">{currentMessage}</p>
                </div>

                {/* Barre de progression */}
                <div className="w-full h-2 sm:h-3 bg-gray-100 rounded-full mt-4 overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-[#FF965E] to-[#FF6B2C] rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>

                <p className="text-xs sm:text-sm text-gray-500 mt-4">
                  Nous analysons votre image pour nous assurer qu'elle respecte nos règles de modération
                </p>
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-2 sm:p-3 text-xs sm:text-sm text-blue-700">
                  <p className="flex items-center">
                    <ImageIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    Votre image est temporairement stockée pour analyse
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center">
                <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-green-500 mb-4" />
                <p className="text-gray-600">Analyse terminée</p>
              </div>
            )}
          </div>

          {/* Bouton d'annulation optionnel */}
          {onCancel && (
            <div className="flex justify-center gap-4">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (onCancel) onCancel();
                }}
                className="px-4 sm:px-6 py-2 sm:py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-all duration-300"
              >
                Annuler
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageModerationStatus;