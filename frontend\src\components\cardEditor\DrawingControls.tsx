import React from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON>lider,
  ToggleButton,
  ToggleButtonGroup,
  Divider,
  Button,
  Tooltip,
  useMediaQuery,

} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Brush, Close, Timeline, Info } from '@mui/icons-material';
import BackspaceIcon from '@mui/icons-material/Backspace';

interface DrawingControlsProps {
  isActive: boolean;
  tool: 'brush' | 'eraser' | 'polygon';
  strokeColor: string;
  strokeWidth: number;
  onToolChange: (tool: 'brush' | 'eraser' | 'polygon') => void;
  onColorChange: (color: string) => void;
  onWidthChange: (width: number) => void;
  onClose: () => void;
}

const DrawingControls: React.FC<DrawingControlsProps> = ({
  isActive,
  tool,
  strokeColor,
  strokeWidth,
  onToolChange,
  onColorChange,
  onWidthChange,
  onClose
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (!isActive) return null;

  // Contenu des contrôles de dessin
  const controlsContent = (
    <Box sx={{ p: { xs: 1.5, sm: 2 } }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 1.5
      }}>
        <Typography
          variant="subtitle1"
          color="primary.main"
          sx={{
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5
          }}
        >
          <Brush fontSize="small" /> Outils de dessin
        </Typography>
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={onClose}
          startIcon={<Close />}
          sx={{
            py: 0.5,
            px: 1.5,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
            }
          }}
        >
          Terminer
        </Button>
      </Box>

      <Divider sx={{ mb: 1.5 }} />

      {/* Sélection d'outil */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Outil:
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 1 }}>
          <ToggleButtonGroup
            value={tool}
            exclusive
            onChange={(_, newTool) => newTool && onToolChange(newTool)}
            orientation={isMobile ? "vertical" : "horizontal"}
            fullWidth
            color="primary"
            size="small"
            sx={{ mb: { xs: 1.5, sm: 0 } }}
          >
            <ToggleButton value="brush" sx={{ py: 0.5 }}>
              <Brush sx={{ mr: 0.5 }} fontSize="small" />
              Pinceau
            </ToggleButton>
            <Tooltip title="Efface réellement les pixels (pas seulement un trait de couleur)" arrow>
              <ToggleButton value="eraser" sx={{ py: 0.5 }}>
                <BackspaceIcon sx={{ mr: 0.5 }} fontSize="small" />
                Gomme
              </ToggleButton>
            </Tooltip>
            <ToggleButton value="polygon" sx={{ py: 0.5 }}>
              <Timeline sx={{ mr: 0.5 }} fontSize="small" />
              Polygone
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </Box>

      {/* Couleur */}
      {tool !== 'eraser' && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Couleur:
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
            <input
              type="color"
              value={strokeColor}
              onChange={(e) => onColorChange(e.target.value)}
              style={{
                width: 40,
                height: 40,
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}
            />
            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
              {['#000000', '#FF6B2C', '#FF7A35', '#2196F3', '#4CAF50', '#F44336', '#9C27B0', '#FF9800'].map((color) => (
                <Box
                  key={color}
                  onClick={() => onColorChange(color)}
                  sx={{
                    width: 24,
                    height: 24,
                    backgroundColor: color,
                    borderRadius: '50%',
                    cursor: 'pointer',
                    border: strokeColor === color ? '2px solid #FF6B2C' : '1px solid rgba(0,0,0,0.1)',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      transition: 'transform 0.2s'
                    }
                  }}
                />
              ))}
            </Box>
          </Box>
        </Box>
      )}

      {/* Épaisseur */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Épaisseur: {strokeWidth}px
        </Typography>
        <Slider
          value={strokeWidth}
          onChange={(_, value) => onWidthChange(value as number)}
          min={1}
          max={tool === 'eraser' ? 50 : 20}
          step={1}
          color="primary"
          sx={{
            '& .MuiSlider-thumb': {
              backgroundColor: '#FF6B2C',
            },
            '& .MuiSlider-track': {
              backgroundColor: '#FF6B2C',
            }
          }}
        />
      </Box>

      {/* Instructions */}
      <Box sx={{
        mt: 1.5,
        pt: 1.5,
        borderTop: '1px dashed rgba(0,0,0,0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Typography
          variant="caption"
          color="text.secondary"
          align="center"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            fontStyle: 'italic'
          }}
        >
          <Box
            component="span"
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              display: 'inline-block'
            }}
          />
          {tool === 'brush' && 'Cliquez et faites glisser sur le canvas pour dessiner'}
          {tool === 'eraser' && (
            <>
              Cliquez et faites glisser pour effacer les pixels
              <Tooltip title="La gomme efface réellement les pixels, pas seulement un trait de couleur" arrow>
                <Info fontSize="inherit" sx={{ ml: 0.5, cursor: 'help', opacity: 0.7 }} />
              </Tooltip>
            </>
          )}
          {tool === 'polygon' && 'Cliquez pour ajouter des points, double-cliquez pour terminer'}
        </Typography>
      </Box>
    </Box>
  );

  // Version mobile : Paper sticky en haut (pas de Drawer qui recouvre)
  if (isMobile) {
    return (
      <Paper
        elevation={3}
        sx={{
          position: 'fixed',
          top: 80,
          left: 8,
          right: 8,
          zIndex: 1000,
          borderRadius: 2,
          backgroundColor: '#FFF8F3',
          border: '1px solid #FFE4BA',
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          maxHeight: '40vh',
          overflow: 'auto'
        }}
      >
        {controlsContent}
      </Paper>
    );
  }

  // Version desktop : Paper sticky
  return (
    <Paper
      elevation={2}
      sx={{
        borderRadius: 2,
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
        backgroundColor: '#FFF8F3',
        border: '1px solid #FFE4BA',
        position: 'sticky',
        top: 0,
        zIndex: 5,
        mb: 2
      }}
    >
      {controlsContent}
    </Paper>
  );
};

export default DrawingControls;
