import { Router } from 'express';
import { missionController } from '../controllers/missionController';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateRequest } from '../middleware/validation';
import { validateFileUpload } from '../middleware/fileValidation';
import { z } from 'zod';
import rateLimit from 'express-rate-limit';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { invalidateMissionCache, invalidatePropositionCache } from '../controllers/missionController';
import { redis } from '../config/redis';
import { sendPaymentReceivedEmail, sendPaymentConfirmedEmail } from '../services/emailService';
import { asyncHandler } from '../utils/inputValidation';
import { checkProfileVisibility } from '../middleware/checkProfileVisibility';
import { decryptProfilDataAsync, decryptDataAsync } from '../utils/encryption';

interface ProposalWithMission {
  id: string;
  jobbeur_id: string;
  montant_propose: number;
  mission: {
    user_id: string;
  };
}

interface UserWithProfile {
  id: string;
  email: string;
  profil: {
    prenom: string;
    nom: string;
  };
}

const router = Router();

// Création du rate limiter pour les missions
const missionsLimiter = rateLimit({
  windowMs: 60 * 1000, // fenêtre de 1 minute
  max: process.env.NODE_ENV === 'development' ? 1000 : 100, // Limite augmentée
  message: {
    message: "Trop de requêtes pour les missions. Veuillez réessayer dans 1 minute.",
    success: false,
    toastType: "error"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Création du rate limiter spécifique pour les commentaires
const commentsLimiter = rateLimit({
  windowMs: 60 * 1000, // fenêtre de 1 minute
  max: 1, // 1 commentaire par minute
  message: {
    message: "Veuillez attendre 1 minute entre chaque commentaire sur la même mission.",
    success: false,
    toastType: "warning"
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => `${req.user?.userId}-${req.params.id}` // Clé unique par utilisateur et par mission
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Application du rate limiter sur toutes les routes missions sauf les commentaires
router.use((req, res, next) => {
  if (!req.path.includes('/comment')) {
    return missionsLimiter(req, res, next);
  }
  next();
});

// Schema de validation pour la création d'une mission
const createMissionSchema = z.object({
  category_id: z.string(),
  subcategory_id: z.string(),
  titre: z.string().min(3).max(70),
  description: z.string()
    .min(10)
    .transform(val => {
      // Nettoyer les balises HTML pour le comptage
      const cleanText = val.replace(/<[^>]*>/g, '');
      return val;
    })
    .refine(
      (val) => {
        const cleanText = val.replace(/<[^>]*>/g, '');
        return cleanText.length <= 1200;
      },
      {
        message: "Le texte (sans HTML) doit contenir au maximum 1200 caractères"
      }
    ),
  budget: z.number().optional(),
  budget_defini: z.boolean(),
  date_mission: z.string().transform(val => val === '' ? undefined : val).optional(),
  has_time_preference: z.boolean().default(false),
  time_slots: z.array(z.object({
    start: z.string(),
    end: z.string(),
    date: z.string()
  })).default([]),
  adresse: z.string(),
  code_postal: z.string(),
  ville: z.string(),
  pays: z.string(),
  payment_method: z.enum(['jobi_only', 'both', 'direct_only']),
  intervention_zone: z.object({
    center: z.tuple([z.number(), z.number()]),
    radius: z.number(),
    adresse: z.string().optional()
  }).optional(),
  is_urgent: z.boolean()
}).superRefine((data, ctx) => {
  // Validation conditionnelle pour le budget
  if (data.budget_defini && (!data.budget || data.budget <= 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Le budget est requis et doit être supérieur à 0 quand budget_defini est true",
      path: ['budget']
    });
  }

  // Validation conditionnelle pour la date et les horaires
  if (data.has_time_preference) {
    // Vérifier que la date est présente et au bon format
    if (!data.date_mission) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "La date est requise lorsque des préférences horaires sont définies",
        path: ['date_mission']
      });
    } else {
      // Valider le format YYYY-MM-DD
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(data.date_mission)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "La date doit être au format YYYY-MM-DD",
          path: ['date_mission']
        });
      }
    }

    // Vérifier que les créneaux sont présents
    if (data.time_slots.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Au moins un créneau horaire est requis lorsque des préférences horaires sont définies",
        path: ['time_slots']
      });
    }
  }
});

// Schema de validation pour la candidature
const applyToMissionSchema = z.object({
  missionId: z.string(),
  message: z.string().min(10).max(1000)
});

// Schema de validation pour la proposition
const proposalSchema = z.object({
  amount: z.number().positive(),
  message: z.string().min(10).max(1000)
});

// Schema de validation pour les contre-offres
const counterOfferSchema = z.object({
  proposalId: z.string().uuid(),
  amount: z.number().min(1, 'Le montant doit être supérieur à 0'),
  message: z.string().min(10, 'Le message doit contenir au moins 10 caractères').max(1000, 'Le message ne doit pas dépasser 1000 caractères')
});

// Schema de validation pour les contre-offres du jobbeur
const jobbeurCounterOfferSchema = z.object({
  proposalId: z.string().uuid(),
  amount: z.number().min(1, 'Le montant doit être supérieur à 0'),
  message: z.string().min(10, 'Le message doit contenir au moins 10 caractères').max(1000, 'Le message ne doit pas dépasser 1000 caractères')
});

// Routes
router.post('/', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(createMissionSchema), asyncHandler(missionController.createMission)); // Route pour créer une mission
router.get('/', authMiddleware.authenticateToken, asyncHandler(missionController.getMissions)); // Route pour les missions d'un utilisateur
router.get('/all', authMiddleware.authenticateToken, asyncHandler(missionController.getAllMissions)); // Route pour toutes les missions disponibles
router.get('/matching', authMiddleware.authenticateToken, asyncHandler(missionController.getMatchingMissions)); // Route pour les missions correspondant aux services de l'utilisateur
router.get('/available', authMiddleware.authenticateToken, asyncHandler(missionController.getAvailableMissions)); // Route pour les missions disponibles
router.post('/apply', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(applyToMissionSchema), asyncHandler(missionController.applyToMission)); // Route pour la candidature à une mission

// Routes pour les photos des missions
router.post('/:id/photos', authMiddleware.authenticateToken, validateFileUpload, asyncHandler(missionController.addPhotoToMission)); // Route pour ajouter une photo à une mission
router.delete('/:missionId/photos/:photoId', authMiddleware.authenticateToken, asyncHandler(missionController.deletePhotoFromMission)); // Route pour supprimer une photo d'une mission
router.put('/:missionId/photos/reorder', authMiddleware.authenticateToken, validateRequest(z.object({
  photoIds: z.array(z.string())
})), asyncHandler(missionController.reorderMissionPhotos)); // Route pour réorganiser les photos d'une mission

// Routes pour les likes, commentaires et recommandations
router.post('/:id/like', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.likeMission)); // Route pour liker une mission
router.get('/:missionId/comments', authMiddleware.authenticateToken, asyncHandler(missionController.getMissionComments)); // Route pour récupérer les commentaires d'une mission
router.post('/:id/comment', authMiddleware.authenticateToken, checkProfileVisibility, commentsLimiter, validateRequest(z.object({
  comment: z.string().min(1).max(500),
  isPrivate: z.boolean().optional().default(false),
  parentId: z.string().optional()
})), asyncHandler(missionController.commentMission)); // Route pour commenter une mission
router.put('/:id/comments/:commentId', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(z.object({
  comment: z.string().min(1).max(500)
})), asyncHandler(missionController.updateComment)); // Route pour modifier un commentaire
router.delete('/:id/comments/:commentId', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.deleteComment)); // Route pour supprimer un commentaire
router.post('/:id/recommend', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.recommendMission)); // Route pour recommander une mission
router.get('/:id/details', authMiddleware.authenticateToken, asyncHandler(missionController.getMissionDetails)); // Route pour récupérer les détails d'une mission

// Routes pour les actions sur les missions
router.post('/:id/reject', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.rejectMission)); // Route pour refuser une mission
router.post('/:id/propose', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(proposalSchema), asyncHandler(missionController.makeProposal)); // Route pour faire une proposition
router.post('/:id/counter-offer', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(counterOfferSchema), asyncHandler(missionController.makeCounterOffer)); // Route pour faire une contre-proposition
router.post('/:id/close', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(z.object({
  statut: z.enum(['terminee', 'annulee'])
})), asyncHandler(missionController.closeMission)); // Route pour fermer une mission

// Route pour récupérer les propositions de l'utilisateur
router.get('/propositions', authMiddleware.authenticateToken, asyncHandler(missionController.getPropositions)); // Route pour récupérer les propositions d'un utilisateur

// Routes pour les propositions
router.get('/propositions/sent', authMiddleware.authenticateToken, asyncHandler(missionController.getSentProposals)); // Route pour récupérer les propositions envoyées par l'utilisateur
router.get('/propositions/received', authMiddleware.authenticateToken, asyncHandler(missionController.getReceivedProposals)); // Route pour récupérer les propositions reçues par l'utilisateur

// Routes pour les statistiques des propositions
router.get('/propositions/sent/stats', authMiddleware.authenticateToken, asyncHandler(missionController.getSentProposalStats)); // Route pour récupérer les statistiques des propositions envoyées par l'utilisateur
router.get('/propositions/received/stats', authMiddleware.authenticateToken, asyncHandler(missionController.getReceivedProposalStats)); // Route pour récupérer les statistiques des propositions reçues par l'utilisateur

// Routes pour accepter et refuser des propositions
router.post('/:id/proposals/:proposalId/accept', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.acceptProposal)); // Route pour accepter une proposition
router.post('/:id/proposals/:proposalId/reject', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.rejectProposal)); // Route pour refuser une proposition
router.post('/:id/proposals/:proposalId/contact-info', authMiddleware.authenticateToken, checkProfileVisibility, asyncHandler(missionController.sendContactInfo)); // Route pour envoyer les informations de contact d'un utilisateur

// Route pour vérifier si l'utilisateur a déjà fait une offre pour une mission spécifique
router.get('/:id/user-proposal', authMiddleware.authenticateToken, asyncHandler(missionController.getUserProposalForMission)); // Route pour vérifier si l'utilisateur a déjà fait une offre pour une mission spécifique

// Route pour la contre-offre du jobbeur
router.post('/:id/jobbeur-counter-offer', authMiddleware.authenticateToken, checkProfileVisibility, validateRequest(jobbeurCounterOfferSchema), asyncHandler(missionController.makeJobbeurCounterOffer)); // Route pour faire une contre-proposition du jobbeur

// Route pour récupérer une proposition spécifique par son ID
router.get('/propositions/:missionId/:proposalId', async (req, res): Promise<void> => {
  try {
    const { missionId, proposalId } = req.params;
    const cacheKey = `proposition:${missionId}:${proposalId}`;

    // Vérifier si la proposition est en cache
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.info(`Proposition récupérée depuis le cache: ${cacheKey}`);
      res.json({
        success: true,
        fromCache: true,
        proposal: JSON.parse(cachedData)
      });
      return;
    }

    logger.info(`Récupération de la proposition: Mission=${missionId}, Proposal=${proposalId}`);

    // Récupérer la proposition dans la base de données
    const { data, error } = await supabase
      .from('user_mission_candidature')
      .select(`
        *,
        mission:mission_id (
          *,
          user_profile:user_id (
            nom,
            prenom,
            photo_url,
            type_de_profil,
            assurance_verifier,
            date_inscription,
            entreprise_verifier,
            identite_verifier,
            is_online,
            mode_vacance,
            profil_verifier,
            slug
          )
        ),
        jobbeur_profile:jobbeur_id (
          nom,
          prenom,
          photo_url,
          type_de_profil,
          assurance_verifier,
          date_inscription,
          entreprise_verifier,
          identite_verifier,
          is_online,
          mode_vacance,
          profil_verifier,
          slug
        )
      `)
      .match({ id: proposalId, mission_id: missionId })
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération de la proposition:', error);
      res.status(404).json({
        success: false,
        message: 'Proposition introuvable',
        error: error
      });
      return;
    }

    if (!data) {
      logger.error(`Proposition non trouvée: Mission=${missionId}, Proposal=${proposalId}`);
      res.status(404).json({
        success: false,
        message: 'Proposition introuvable'
      });
      return;
    }

    // Déchiffrer les données sensibles des profils
    if (data.mission?.user_profile) {
      data.mission.user_profile = await decryptProfilDataAsync(data.mission.user_profile);
    }
    if (data.jobbeur_profile) {
      data.jobbeur_profile = await decryptProfilDataAsync(data.jobbeur_profile);
    }

    // Mettre en cache la proposition pour 10 minutes
    await redis.set(cacheKey, JSON.stringify(data), 'EX', 600);
    logger.info(`Proposition mise en cache: ${cacheKey}`);

    logger.info('Proposition récupérée avec succès');

    res.json({
      success: true,
      fromCache: false,
      proposal: data
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération de la proposition:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la proposition'
    });
  }
});

// Route pour mettre à jour le statut de paiement d'une proposition
router.post('/propositions/:missionId/:proposalId/payment-status', async (req, res): Promise<void> => {
  try {
    const { missionId, proposalId } = req.params;
    const { status, montant_paiement } = req.body;

    logger.info(`Mise à jour du statut de paiement: Mission=${missionId}, Proposal=${proposalId}, Status=${status}, Montant=${montant_paiement}`);

    // Vérifier que le statut est valide
    if (!['pending', 'completed', 'manual'].includes(status)) {
      res.status(400).json({
        success: false,
        message: 'Statut de paiement invalide'
      });
      return;
    }

    // Vérifier d'abord si la proposition existe
    const { data: checkData, error: checkError } = await supabase
      .from('user_mission_candidature')
      .select('id, jobbeur_id, montant_propose, mission:user_missions!inner(user_id)')
      .match({ id: proposalId, mission_id: missionId })
      .single() as { data: ProposalWithMission | null, error: any };

    if (checkError) {
      logger.error('Erreur lors de la vérification de l\'existence de la proposition:', checkError);
      res.status(404).json({
        success: false,
        message: 'Proposition introuvable',
        error: checkError
      });
      return;
    }

    if (!checkData) {
      logger.error(`Proposition non trouvée: Mission=${missionId}, Proposal=${proposalId}`);
      res.status(404).json({
        success: false,
        message: 'Proposition introuvable'
      });
      return;
    }

    // Déterminer si c'est un paiement complet ou partiel
    const isFullPayment = montant_paiement === checkData.montant_propose;
    const paymentType = isFullPayment ? 'Complet' : 'Partiel';

    // Mettre à jour le statut de paiement dans la base de données
    const paymentDate = status !== 'pending' ? new Date().toISOString() : null;
    logger.info(`Mise à jour de payment_status=${status}, payment_date=${paymentDate}, montant_paiement=${montant_paiement}, type=${paymentType}`);

    const updateData: any = {
      payment_status: status,
      payment_date: paymentDate
    };

    // Ajouter le montant du paiement si fourni
    if (montant_paiement !== undefined) {
      updateData.montant_paiement = montant_paiement;
    }

    const { data, error } = await supabase
      .from('user_mission_candidature')
      .update(updateData)
      .match({ id: proposalId, mission_id: missionId });

    if (error) {
      logger.error('Erreur SQL lors de la mise à jour du statut de paiement:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du statut de paiement'
      });
      return;
    }

    // Invalider le cache pour le jobbeur et le propriétaire de la mission
    await invalidateMissionCache(missionId, checkData.jobbeur_id);
    await invalidateMissionCache(missionId, checkData.mission.user_id);
    logger.info('Cache invalidé après mise à jour du statut de paiement:', { missionId, jobbeurId: checkData.jobbeur_id, missionOwnerId: checkData.mission.user_id });

    // Invalider le cache pour la proposition
    await invalidatePropositionCache(missionId, proposalId);
    logger.info('Cache de la proposition invalidé après mise à jour du statut de paiement');

    logger.info('Statut de paiement mis à jour avec succès', data);

    // Récupérer les informations des utilisateurs pour les notifications
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        profil:user_profil(
          prenom,
          nom
        )
      `)
      .in('id', [checkData.jobbeur_id, checkData.mission.user_id]);

    if (!usersError && users) {
      const jobbeur = users.find(u => u.id === checkData.jobbeur_id);
      const client = users.find(u => u.id === checkData.mission.user_id);

      if (!jobbeur?.profil?.[0] || !client?.profil?.[0]) {
        logger.error('Profils utilisateurs non trouvés pour les notifications:', {
          jobbeurId: checkData.jobbeur_id,
          clientId: checkData.mission.user_id,
          jobbeurProfil: !!jobbeur?.profil?.[0],
          clientProfil: !!client?.profil?.[0]
        });
        res.status(500).json({
          success: false,
          message: 'Profils utilisateurs non trouvés pour les notifications'
        });
        return;
      }

      // Déchiffrer les données sensibles des profils pour les notifications
      const decryptedJobbeurProfil = await decryptProfilDataAsync(jobbeur.profil[0]);
      const decryptedClientProfil = await decryptProfilDataAsync(client.profil[0]);

      // Déchiffrer les emails des utilisateurs
      const decryptedJobbeurEmail = await decryptDataAsync(jobbeur.email);
      const decryptedClientEmail = await decryptDataAsync(client.email);

      // Récupérer les informations de la mission pour les notifications
      const { data: mission } = await supabase
        .from('user_missions')
        .select('titre')
        .eq('id', missionId)
        .single();

      if (mission) {
        // Envoyer une notification au jobbeur (celui qui envoie le paiement)
        await supabase
          .from('user_notifications')
          .insert({
            user_id: jobbeur.id,
            type: 'jobi',
            title: 'Paiement envoyé',
            content: `Vous avez confirmé avoir effectué un paiement manuel ${paymentType.toLowerCase()} de ${montant_paiement}€${!isFullPayment ? ` (sur un total de ${checkData.montant_propose}€)` : ''} pour la mission "${mission.titre}" de ${decryptedClientProfil.prenom} ${decryptedClientProfil.nom.charAt(0)}.`,
            link: `/dashboard/missions/${missionId}`,
            is_read: false,
            is_archived: false
          });

        // Email si les préférences le permettent
        await sendPaymentReceivedEmail(decryptedJobbeurEmail, {
          amount: montant_paiement,
          totalAmount: checkData.montant_propose,
          isFullPayment,
          missionTitle: mission.titre,
          missionUrl: `${process.env.FRONTEND_URL}/dashboard/missions/${missionId}`,
          paymentMethod: status === 'manual' ? 'Paiement Manuel' : 'Jobi',
          clientName: `${decryptedClientProfil.prenom} ${decryptedClientProfil.nom.charAt(0)}.`,
          jobbeurName: `${decryptedJobbeurProfil.prenom} ${decryptedJobbeurProfil.nom.charAt(0)}.`
        });

        // Envoyer une notification au client (celui qui reçoit le paiement)
        await supabase
          .from('user_notifications')
          .insert({
            user_id: client.id,
            type: 'jobi',
            title: 'Paiement reçu',
            content: `${decryptedJobbeurProfil.prenom} ${decryptedJobbeurProfil.nom.charAt(0)}. vient de confirmer vous avoir envoyé un paiement ${paymentType.toLowerCase()} de ${montant_paiement}€${!isFullPayment ? ` (sur un total de ${checkData.montant_propose}€)` : ''} pour la mission "${mission.titre}"`,
            link: `/dashboard/missions/${missionId}`,
            is_read: false,
            is_archived: false
          });

        // Email si les préférences le permettent
        await sendPaymentConfirmedEmail(decryptedClientEmail, {
          amount: montant_paiement,
          totalAmount: checkData.montant_propose,
          isFullPayment,
          missionTitle: mission.titre,
          missionUrl: `${process.env.FRONTEND_URL}/dashboard/missions/${missionId}`,
          paymentMethod: status === 'manual' ? 'Paiement Manuel' : 'Jobi',
          jobbeurName: `${decryptedJobbeurProfil.prenom} ${decryptedJobbeurProfil.nom.charAt(0)}.`,
          clientName: `${decryptedClientProfil.prenom} ${decryptedClientProfil.nom.charAt(0)}.`
        });
      }
    } else {
      logger.error('Erreur lors de la récupération des informations des utilisateurs pour les notifications de paiement:', usersError);
    }

    res.json({
      success: true,
      message: 'Statut de paiement mis à jour avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du statut de paiement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut de paiement'
    });
  }
});

// Routes d'administration pour la modération des missions
router.put('/:id/moderate', authMiddleware.authenticateToken, validateRequest(z.object({
  statut: z.enum(['en_attente', 'en_cours', 'terminee', 'annulee', 'en_moderation']),
  reason: z.string().optional()
})), asyncHandler(missionController.moderateMission)); // Route pour modérer une mission

router.delete('/:id/admin-delete', authMiddleware.authenticateToken, validateRequest(z.object({
  reason: z.string().optional()
})), asyncHandler(missionController.adminDeleteMission)); // Route pour supprimer une mission par un admin

export default router;