import api from '../services/api';
import logger from '../utils/logger';
import { getMultipartHeaders } from '../utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import { API_CONFIG } from '../config/api';
import axios from 'axios';

// Cache local pour les templates
let templateCache: ResponseTemplate[] = [];
let templateCacheTimestamp = 0;
let templateCacheKey = '';
// Durée de validité du cache en ms (2 minutes)
const TEMPLATE_CACHE_TTL = 2 * 60 * 1000;

// Dernière requête templates en cours
let pendingTemplatesRequest: Promise<ResponseTemplate[]> | null = null;

// Types
export interface Tag {
  id: string;
  name: string;
  color: string;
}

export interface User {
  id: string;
  email: string;
  avatar_url?: string;
  first_name?: string;
  last_name?: string;
  role?: 'jobpadm' | 'jobmodo' | 'jobutil';
}

export interface Ticket {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  assigned_to?: string | null;
  tags?: Tag[];
  user?: User;
  assigned_user?: User;
  repondu: boolean;
}

export interface Comment {
  id: string;
  ticket_id: string;
  user_id: string;
  message: string;
  created_at: string;
  is_internal: boolean;
  user: User;
}

export interface Attachment {
  id: string;
  ticket_id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  created_at: string;
}

export interface ResponseTemplate {
  id: string;
  title: string;
  content: string;
  category: string;
  created_at: string;
  updated_at?: string;
  created_by?: string;
}

export interface TicketFilters {
  status?: string;
  priority?: string;
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
  user_id?: string;
  repondu?: boolean;
  assigned_to?: string;
  status_not_in?: string[];
}

export interface TemplateFilters {
  category?: string;
  search?: string;
}

export interface TicketListResponse {
  tickets: Ticket[];
  total: number;
  page: number;
  limit: number;
}

export interface TicketStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  ticketsByStatus: { status: string; count: number }[];
  ticketsByPriority: { priority: string; count: number }[];
  ticketsByCategory: { category: string; count: number }[];
  averageFirstResponseTime: number;
  averageSatisfaction: number;
  slaComplianceRate: number;
  ticketsByAgent: { agent: string; count: number }[];
  resolutionByCategory: { category: string; time: number }[];
  peakHours: { hour: string; count: number }[];
  tagsDistribution: { tag: string; count: number }[];
  weeklyTrends: { week: string; count: number }[];
  monthlyComparison: { month: string; thisYear: number; lastYear: number }[];
}

export interface CreateTicketDto {
  title: string;
  description: string;
  priority: string;
  category: string;
}

export interface CreateCommentDto {
  ticket_id: string;
  message: string;
  is_internal: boolean;
}

export interface CreateTagDto {
  name: string;
  color: string;
}

export interface CreateTemplateDto {
  title: string;
  content: string;
  category: string;
}

const supportTicketService = {
  // Récupérer la liste des tickets avec pagination et filtres
  getTickets: async (filters: TicketFilters = {}): Promise<TicketListResponse> => {
    try {
      const { page = 1, limit = 10, ...otherFilters } = filters;
      
      // Nettoyer les filtres pour éviter d'envoyer des valeurs vides
      const cleanParams: Record<string, any> = { page, limit };
      
      // N'ajouter que les filtres qui ont une valeur
      Object.entries(otherFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          // Traitement spécial pour les tableaux
          if (Array.isArray(value)) {
            // Pour status_not_in, nous envoyons la valeur sous forme de chaîne séparée par des virgules
            cleanParams[key] = value.join(',');
          } else {
            cleanParams[key] = value;
          }
        }
      });
      
      // Log des filtres pour le débogage
      logger.info("Filtres nettoyés envoyés à l'API:", cleanParams);
      
      const response = await api.get('/api/support', {
        params: cleanParams
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des tickets:', error);
      throw error;
    }
  },

  // Récupérer un ticket par son id
  getTicket: async (ticketId: string): Promise<Ticket> => {
    try {
      const response = await api.get(`/api/support/${ticketId}`);
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la récupération du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Créer un nouveau ticket
  createTicket: async (ticket: CreateTicketDto): Promise<Ticket> => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post('/api/support', ticket, { headers });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la création du ticket:', error);
      throw error;
    }
  },

  // Mettre à jour un ticket
  updateTicket: async (ticketId: string, data: Partial<Ticket>): Promise<Ticket> => {
    try {
      const response = await api.patch(`/api/support/${ticketId}`, data);
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Supprimer un ticket
  deleteTicket: async (ticketId: string): Promise<void> => {
    try {
      await api.delete(`/api/support/${ticketId}`);
    } catch (error) {
      logger.error(`Erreur lors de la suppression du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Récupérer les statistiques des tickets
  getTicketStats: async (timeRange: string = '7'): Promise<TicketStats> => {
    try {
      logger.info('Appel API getTicketStats avec timeRange:', timeRange);
      
      const response = await api.get('/api/support/stats', {
        params: {
          timeRange
        }
      });

      logger.info('Réponse API stats brute:', response.data);

      // Récupérer les données ou utiliser des valeurs par défaut
      const data = response.data;
      
      // Générer des dates pour les derniers jours si by_date est manquant ou vide
      const hasValidDates = data.by_date && Object.values(data.by_date).some(value => {
        // Type guard pour s'assurer que count est un nombre
        const count = typeof value === 'number' ? value : 0;
        return count > 0;
      });
      if (!hasValidDates) {
        logger.warn('Données de date manquantes ou vides, génération de données par défaut');
        data.by_date = {};
        // Répartir les tickets sur les 3 derniers jours
        const ticketsPerDay = Math.ceil(data.total / 3);
        for (let i = 0; i < Number(timeRange); i++) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          const dateStr = date.toISOString().split('T')[0];
          // Mettre plus de tickets sur les jours récents
          data.by_date[dateStr] = i < 3 ? ticketsPerDay : 0;
        }
      }

      // Assurer que by_category a toutes les catégories
      if (!data.by_category) {
        data.by_category = {};
      }
      const categories = ['technique', 'facturation', 'compte', 'mission', 'autre'];
      categories.forEach(category => {
        if (!(category in data.by_category)) {
          data.by_category[category] = 0;
        }
      });

      // Générer tickets_by_agent si manquant
      if (!data.tickets_by_agent) {
        logger.warn('Données d\'agent manquantes, génération de données par défaut');
        data.tickets_by_agent = {
          'Non assigné': data.total || 0
        };
      }

      // Assurer que toutes les propriétés requises existent
      const defaultData = {
        total: 0,
        by_status: { 
          nouveau: 0, 
          en_cours: 0, 
          en_attente: 0,
          resolu: 0 
        },
        by_priority: { 
          basse: 0, 
          normale: 0, 
          haute: 0, 
          urgente: 0 
        },
        average_resolution_time: 0,
        average_first_response_time: 0,
        average_satisfaction: 0,
        sla_compliance_rate: 0,
        resolution_by_category: {},
        peak_hours: {},
        tags_distribution: {}
      };

      // Fusionner les données par défaut avec les données reçues
      const mergedData = {
        ...defaultData,
        ...data,
        by_status: { ...defaultData.by_status, ...data.by_status },
        by_priority: { ...defaultData.by_priority, ...data.by_priority }
      };

      // Calculer le nombre de tickets ouverts en incluant tous les statuts non résolus
      const openStatuses = ['nouveau', 'en_cours', 'en_attente'];
      mergedData.open_tickets = openStatuses.reduce((sum, status) => 
        sum + (mergedData.by_status[status] || 0), 0
      );

      // Générer les heures de pointe si manquantes
      if (!mergedData.peak_hours || Object.keys(mergedData.peak_hours).length === 0) {
        mergedData.peak_hours = {};
        for (let hour = 0; hour < 24; hour++) {
          mergedData.peak_hours[hour] = hour >= 9 && hour <= 17 ? Math.round(data.total / 8) : 0;
        }
      }

      return mergedData;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques des tickets:', error);
      throw error;
    }
  },

  // Récupérer les statistiques des tickets avec une plage de dates personnalisée
  getTicketStatsCustom: async (startDate: string, endDate: string): Promise<TicketStats> => {
    try {
      logger.info(`Appel API getTicketStatsCustom avec dates: ${startDate} - ${endDate}`);
      
      const response = await api.get('/api/support/stats/custom', {
        params: {
          startDate,
          endDate
        }
      });

      logger.info('Réponse API stats personnalisées brute:', response.data);

      // Récupérer les données ou utiliser des valeurs par défaut
      const data = response.data;
      
      // Générer des dates pour les jours si by_date est manquant ou vide
      const hasValidDates = data.by_date && Object.values(data.by_date).some(value => {
        // Type guard pour s'assurer que count est un nombre
        const count = typeof value === 'number' ? value : 0;
        return count > 0;
      });
      
      if (!hasValidDates) {
        logger.warn('Données de date manquantes ou vides, génération de données par défaut');
        data.by_date = {};
        
        // Calculer la différence de jours entre les dates
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end.getTime() - start.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 pour inclure la date de fin
        
        // Répartir les tickets sur la période
        const ticketsPerDay = Math.ceil(data.total / diffDays);
        
        // Générer les données pour chaque jour de la période
        const currentDate = new Date(start);
        while (currentDate <= end) {
          const dateStr = currentDate.toISOString().split('T')[0];
          data.by_date[dateStr] = ticketsPerDay;
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Assurer que by_category a toutes les catégories
      if (!data.by_category) {
        data.by_category = {};
      }
      const categories = ['technique', 'facturation', 'compte', 'mission', 'autre'];
      categories.forEach(category => {
        if (!(category in data.by_category)) {
          data.by_category[category] = 0;
        }
      });

      // Générer tickets_by_agent si manquant
      if (!data.tickets_by_agent) {
        logger.warn('Données d\'agent manquantes, génération de données par défaut');
        data.tickets_by_agent = {
          'Non assigné': data.total || 0
        };
      }

      // Assurer que toutes les propriétés requises existent
      const defaultData = {
        total: 0,
        by_status: { 
          nouveau: 0, 
          en_cours: 0, 
          en_attente: 0,
          resolu: 0 
        },
        by_priority: { 
          basse: 0, 
          normale: 0, 
          haute: 0, 
          urgente: 0 
        },
        average_resolution_time: 0,
        average_first_response_time: 0,
        average_satisfaction: 0,
        sla_compliance_rate: 0,
        resolution_by_category: {},
        peak_hours: {},
        tags_distribution: {}
      };

      // Fusionner les données par défaut avec les données reçues
      const stats = { ...defaultData, ...data };

      return stats;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques personnalisées:', error);
      throw error;
    }
  },

  // Récupérer les informations des utilisateurs par leurs IDs
  getUsersInfo: async (userIds: string[]): Promise<Record<string, User>> => {
    try {
      // Version alternative qui ne fait pas d'appel API
      // Comme la route /api/users/batch n'existe pas, nous retournons simplement un objet vide
      // Dans une vraie implémentation, on pourrait faire des requêtes individuelles pour chaque utilisateur
      // ou utiliser une autre route existante
      
      logger.info("getUsersInfo appelé avec les IDs:", userIds);
      
      // On peut accéder aux informations des tickets si elles sont déjà disponibles
      // ou retourner un objet vide qui sera rempli plus tard si nécessaire
      
      // Si les tickets contiennent déjà les infos utilisateur, elles seront utilisées directement
      return {};
    } catch (error) {
      logger.error('Erreur lors de la récupération des informations utilisateur:', error);
      // Retourner un objet vide en cas d'erreur
      return {};
    }
  },

  // Récupérer les commentaires d'un ticket
  getTicketComments: async (ticketId: string): Promise<Comment[]> => {
    try {
      const response = await api.get(`/api/support/${ticketId}/comments`);
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la récupération des commentaires du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Ajouter un commentaire à un ticket
  addTicketComment: async (comment: CreateCommentDto): Promise<Comment> => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/support/${comment.ticket_id}/comments`, comment, { headers });
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de l'ajout d'un commentaire au ticket ${comment.ticket_id}:`, error);
      throw error;
    }
  },

  // Supprimer un commentaire d'un ticket
  deleteTicketComment: async (ticketId: string, commentId: string): Promise<void> => {
    try {
      await api.delete(`/api/support/${ticketId}/comments/${commentId}`);
      logger.info(`Commentaire ${commentId} supprimé avec succès du ticket ${ticketId}`);
    } catch (error) {
      logger.error(`Erreur lors de la suppression du commentaire ${commentId} du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Récupérer les pièces jointes d'un ticket
  getTicketAttachments: async (ticketId: string): Promise<Attachment[]> => {
    try {
      const response = await api.get(`/api/support/${ticketId}/attachments`);
      // Log détaillé de la réponse pour le débogage
      logger.info(`Réponse de l'API attachments pour ticket ${ticketId}:`, {
        responseType: typeof response.data,
        isArray: Array.isArray(response.data),
        dataStructure: response.data && typeof response.data === 'object' ? Object.keys(response.data) : 'not an object'
      });
      
      // Normaliser la réponse pour s'assurer qu'on retourne toujours un tableau
      let attachments: Attachment[] = [];
      
      if (Array.isArray(response.data)) {
        attachments = response.data;
      } else if (response.data && typeof response.data === 'object') {
        // Certaines API retournent { data: [] } au lieu de juste []
        if (Array.isArray(response.data.data)) {
          attachments = response.data.data;
        } else if (response.data.attachments && Array.isArray(response.data.attachments)) {
          // Une autre structure possible
          attachments = response.data.attachments;
        }
      }
      
      return attachments;
    } catch (error) {
      logger.error(`Erreur lors de la récupération des pièces jointes du ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Uploader une pièce jointe
  uploadAttachment: async (ticketId: string, file: File): Promise<Attachment> => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      
      // Utiliser un nouveau FormData pour chaque upload
      const formData = new FormData();
      
      // S'assurer d'utiliser le bon nom de champ - le backend attend 'files'
      formData.append('files', file, file.name);
      
      // Logs détaillés pour le débogage
      logger.info(`Préparation de l'upload pour le ticket ${ticketId}`, {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });
      
      // Utiliser l'URL complète avec le préfixe API pour éviter les erreurs de routing
      const url = `${API_CONFIG.baseURL}/api/support/${ticketId}/attachments`;
      logger.info(`URL complète pour l'upload: ${url}`);
      
      // Utiliser axios directement avec une configuration optimisée pour FormData
      const response = await axios.post(url, formData, {
        headers,
        withCredentials: true
      });

      logger.info('Réponse de l\'upload:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      return response.data.data || response.data;
    } catch (error) {
      logger.error(`Erreur lors de l'upload d'une pièce jointe pour le ticket ${ticketId}:`, error);
      throw error;
    }
  },

  // Télécharger une pièce jointe
  downloadAttachment: async (attachmentId: string): Promise<Blob> => {
    try {
      const response = await api.get(`/api/support/attachments/${attachmentId}/download`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors du téléchargement de la pièce jointe ${attachmentId}:`, error);
      throw error;
    }
  },

  // Supprimer une pièce jointe
  deleteAttachment: async (ticketId: string, attachmentId: string): Promise<void> => {
    try {
      await api.delete(`/api/support/${ticketId}/attachments/${attachmentId}`);
      logger.info(`Pièce jointe ${attachmentId} supprimée avec succès`);
    } catch (error) {
      logger.error(`Erreur lors de la suppression de la pièce jointe ${attachmentId}:`, error);
      throw error;
    }
  },

  // TAGS
  
  // Récupérer tous les tags
  getTags: async (): Promise<Tag[]> => {
    try {
      const response = await api.get('/api/support/ticket-tags');
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des tags:', error);
      throw error;
    }
  },

  // Créer un nouveau tag
  createTag: async (tagData: CreateTagDto): Promise<Tag> => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post('/api/support/ticket-tags', tagData, { headers });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la création du tag:', error);
      throw error;
    }
  },

  // Mettre à jour un tag
  updateTag: async (tagId: string, tagData: CreateTagDto): Promise<Tag> => {
    try {
      const response = await api.patch(`/api/support/ticket-tags/${tagId}`, tagData);
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du tag ${tagId}:`, error);
      throw error;
    }
  },

  // Supprimer un tag
  deleteTag: async (tagId: string): Promise<void> => {
    try {
      await api.delete(`/api/support/ticket-tags/${tagId}`);
    } catch (error) {
      logger.error(`Erreur lors de la suppression du tag ${tagId}:`, error);
      throw error;
    }
  },

  // TEMPLATES DE RÉPONSE
  
  /**
   * Invalide le cache interne des templates
   */
  invalidateTemplatesCache: (): void => {
    logger.info('🧹 Invalidation du cache service des templates');
    templateCache = [];
    templateCacheTimestamp = 0;
    templateCacheKey = '';
    pendingTemplatesRequest = null;
  },
  
  // Nettoyer le cache des templates côté serveur (Redis)
  clearTemplatesCache: async (): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post('/api/support/templates/clear-cache', {}, { headers });
      logger.info('Réponse du nettoyage du cache:', response.data);
      
      // Vider aussi le cache local
      templateCache = [];
      templateCacheTimestamp = 0;
      templateCacheKey = '';
      pendingTemplatesRequest = null;
      
      return true;
    } catch (error) {
      logger.error('Erreur lors du nettoyage du cache côté serveur:', error);
      return false;
    }
  },

  // Récupérer tous les modèles de réponse
  getResponseTemplates: async (filters: TemplateFilters = {}): Promise<ResponseTemplate[]> => {
    try {
      // Créer une clé de cache basée sur les filtres
      const cacheKey = JSON.stringify(filters);
      
      // Vérifier si une requête identique est déjà en cours
      if (pendingTemplatesRequest && cacheKey === templateCacheKey) {
        logger.info('🔄 Réutilisation d\'une requête templates en cours');
        return pendingTemplatesRequest;
      }
      
      // Vérifier si le cache est valide
      const cacheIsValid = 
        templateCache.length > 0 && 
        cacheKey === templateCacheKey && 
        Date.now() - templateCacheTimestamp < TEMPLATE_CACHE_TTL;
      
      if (cacheIsValid) {
        logger.info(`📦 Utilisation du cache service (${templateCache.length} templates)`);
        return templateCache;
      }
      
      // Construire l'URL avec les paramètres de requête
      const query = new URLSearchParams();
      if (filters.category) query.append('category', filters.category);
      if (filters.search) query.append('search', filters.search);
      
      const url = `/api/support/templates${query.toString() ? `?${query.toString()}` : ''}`;
      logger.info(`⚡ CHARGEMENT DES TEMPLATES - API: ${url}`);
      
      // Créer une fonction qui exécute la requête
      const fetchFunction = async () => {
        try {
          // Ici nous utilisons une seule requête pour tous les appels simultanés
          const response = await api.get(url, {
            timeout: 10000 // Timeout augmenté
          });
          
          // S'assurer que nous retournons toujours un tableau
          let templates: ResponseTemplate[] = [];
          
          if (Array.isArray(response.data)) {
            templates = response.data;
          } else if (response.data && typeof response.data === 'object') {
            if (Array.isArray(response.data.data)) {
              templates = response.data.data;
            } else if (response.data.templates && Array.isArray(response.data.templates)) {
              templates = response.data.templates;
            }
          }
          
          // Filtrer les templates invalides
          templates = templates.filter(template => {
            if (!template || !template.id || !template.title) {
              logger.warn("Template invalide ignoré:", template);
              return false;
            }
            return true;
          });
          
          // Mettre à jour le cache
          templateCache = templates;
          templateCacheTimestamp = Date.now();
          templateCacheKey = cacheKey;
          
          logger.info(`✅ Récupération réussie de ${templates.length} templates valides`);
          
          // La requête est terminée
          pendingTemplatesRequest = null;
          
          return templates;
        } catch (error) {
          // En cas d'erreur, supprimer la référence à la requête en cours
          pendingTemplatesRequest = null;
          throw error;
        }
      };
      
      // Stocker et retourner la promesse pour partager la même requête
      pendingTemplatesRequest = fetchFunction();
      templateCacheKey = cacheKey;
      
      return pendingTemplatesRequest;
    } catch (error) {
      logger.error('⛔ Erreur lors de la récupération des templates:', error);
      // En cas d'erreur, retourner le cache si disponible
      if (templateCache.length > 0) {
        logger.info('🔄 Utilisation du cache après erreur');
        return templateCache;
      }
      throw error;
    }
  },

  // Récupérer un modèle de réponse par son id
  getResponseTemplate: async (templateId: string): Promise<ResponseTemplate> => {
    try {
      const response = await api.get(`/api/support/templates/${templateId}`);
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la récupération du modèle de réponse ${templateId}:`, error);
      throw error;
    }
  },

  // Créer un nouveau modèle de réponse
  createResponseTemplate: async (template: CreateTemplateDto): Promise<ResponseTemplate> => {
    try {
      logger.info('Début création template avec données:', template);
      
      // Vérification des données avant envoi
      if (!template.title || template.title.trim() === '') {
        throw new Error('Le titre du modèle est requis');
      }
      
      if (!template.content || template.content.trim() === '') {
        throw new Error('Le contenu du modèle est requis');
      }
      
      if (!template.category) {
        // Définir une catégorie par défaut au lieu d'échouer
        logger.warn('Catégorie non définie, utilisation de "autre" par défaut');
        template.category = 'autre';
      }
      
      // Récupérer explicitement un nouveau token CSRF
      const csrfToken = await fetchCsrfToken();
      
      // Utilisation directe d'axios au lieu de l'instance api pour débuguer
      try {
        const response = await api.post('/api/support/templates', template, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken // Définir explicitement le token CSRF
          },
          timeout: 10000 // Augmenter le timeout pour éviter les erreurs de délai
        });
        
      
        if (!response.data) {
          throw new Error('Réponse vide du serveur');
        }
        
        // Invalider le cache après création réussie
        supportTicketService.invalidateTemplatesCache();
        
        return response.data;
      } catch (axiosError) {
        throw axiosError;
      }
    } catch (error) {
      logger.error('Erreur détaillée lors de la création du modèle de réponse:', error);
      
      // Extraire le message d'erreur spécifique de la réponse si disponible
      if (axios.isAxiosError(error) && error.response) {
        const serverError = error.response.data?.error || error.response.data?.message;
        if (serverError) {
          logger.error('Message d\'erreur du serveur:', serverError);
          throw new Error(`Erreur serveur: ${serverError}`);
        }
      }
      
      throw error;
    }
  },

  // Mettre à jour un modèle de réponse
  updateResponseTemplate: async (templateId: string, template: Partial<CreateTemplateDto>): Promise<ResponseTemplate> => {
    try {
      const response = await api.patch(`/api/support/templates/${templateId}`, template);
      
      // Invalider le cache après modification
      supportTicketService.invalidateTemplatesCache();
      
      return response.data;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du modèle de réponse ${templateId}:`, error);
      throw error;
    }
  },

  // Supprimer un modèle de réponse
  deleteResponseTemplate: async (templateId: string): Promise<void> => {
    try {
      await api.delete(`/api/support/templates/${templateId}`);
      
      // Invalider le cache après suppression
      supportTicketService.invalidateTemplatesCache();
      
    } catch (error) {
      logger.error(`Erreur lors de la suppression du modèle de réponse ${templateId}:`, error);
      throw error;
    }
  },

  // Récupérer la liste des utilisateurs du staff (admin et modérateurs)
  getStaffUsers: async (): Promise<User[]> => {
    try {
      const response = await api.get('/api/users/staff');
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des utilisateurs du staff:', error);
      // En cas d'erreur, on peut retourner un tableau vide pour éviter les erreurs côté client
      return [];
    }
  },
};

export default supportTicketService;