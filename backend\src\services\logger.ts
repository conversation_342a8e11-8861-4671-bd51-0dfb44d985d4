import winston from 'winston';
import { LogEventType } from '../types/logger';
import logger from '../utils/logger';

// Masquage des données sensibles
const sensitiveFields = ['password', 'token', 'authorization', 'credit_card', 'ssn'];

const maskSensitiveData = (data: any): any => {
    if (!data) return data;
    
    if (typeof data === 'object') {
        const maskedData = { ...data };
        for (const key in maskedData) {
            if (sensitiveFields.includes(key.toLowerCase())) {
                maskedData[key] = '[MASQUÉ]';
            } else if (typeof maskedData[key] === 'object') {
                maskedData[key] = maskSensitiveData(maskedData[key]);
            }
        }
        return maskedData;
    }
    return data;
};

// Format personnalisé pour les logs
const logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const maskedMeta = maskSensitiveData(meta);
        return `${timestamp} [${level.toUpperCase()}]: ${message} ${
            Object.keys(maskedMeta).length ? JSON.stringify(maskedMeta) : ''
        }`;
    })
);

// Interface pour les métadonnées de log
interface LogMetadata {
    userId?: string;
    ip?: string;
    userAgent?: string;
    path?: string;
    method?: string;
    [key: string]: any;
}

// Fonctions de logging sécurisées
export const logSecurity = {
    info: (event: LogEventType, message: string, metadata?: LogMetadata) => {
        logger.info(`[${event}] ${message}`, maskSensitiveData(metadata));
    },
    
    error: (event: LogEventType, message: string, metadata?: LogMetadata) => {
        logger.error(`[${event}] ${message}`, maskSensitiveData(metadata));
    },
    
    warn: (event: LogEventType, message: string, metadata?: LogMetadata) => {
        logger.warn(`[${event}] ${message}`, maskSensitiveData(metadata));
    },
    
    debug: (event: LogEventType, message: string, metadata?: LogMetadata) => {
        if (process.env.NODE_ENV !== 'production') {
            logger.info(`[${event}] ${message}`, maskSensitiveData(metadata));
        }
    },
    
    audit: (event: LogEventType, metadata: LogMetadata) => {
        logger.info(`[AUDIT][${event}]`, {
            timestamp: new Date().toISOString(),
            ...maskSensitiveData(metadata),
        });
    }
};

export default logSecurity;
