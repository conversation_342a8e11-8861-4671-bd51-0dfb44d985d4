import { useAuth } from '../contexts/AuthContext';
import Erreur404Etc from './Erreur404Etc';

interface PrivateRouteProps {
  children: React.ReactNode;
  roleRequis?: 'jobpadm' | 'jobmodo' | 'jobutil';
}

export const RoutePrivee: React.FC<PrivateRouteProps> = ({ children, roleRequis }) => {
  const { user, loading } = useAuth();

  // Afficher le loader pendant le chargement
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF7A35]"></div>
      </div>
    );
  }

  // Vérifier si l'utilisateur est connecté
  if (!user) {
    return <Erreur404Etc code={403} />;
  }

  // Vérifier les droits d'accès basés sur le rôle
  if (roleRequis) {
    const aAcces = () => {
      // L'administrateur a accès à tout
      if (user.role === 'jobpadm') return true;

      // Le modérateur a accès aux pages modérateur et utilisateur
      if (user.role === 'jobmodo') {
        return ['jobmodo', 'jobutil'].includes(roleRequis);
      }

      // L'utilisateur n'a accès qu'aux pages utilisateur
      if (user.role === 'jobutil') {
        return roleRequis === 'jobutil';
      }

      return false;
    };

    if (!aAcces()) {
      return <Erreur404Etc code={403} />;
    }
  }

  return <>{children}</>;
};
