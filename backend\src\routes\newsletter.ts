import { Router } from 'express';
import { newsletterController } from '../controllers/newsletterController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiter pour les routes de newsletter
const newsletterLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 requêtes par IP
  message: { 
    success: false, 
    message: 'Trop de requêtes, veuillez réessayer dans 15 minutes' 
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Route pour s'abonner à la newsletter
router.post('/subscribe', newsletterLimiter, newsletterController.subscribe);

// Route pour vérifier l'email d'un abonné
router.get('/verify', newsletterLimiter, newsletterController.verifyEmail);

// Route pour se désabonner de la newsletter
router.get('/unsubscribe', newsletterLimiter, newsletterController.unsubscribe);

// Routes admin (protégées)
router.get(
  '/subscribers', 
  authMiddleware.authenticateToken, 
  authMiddleware.checkRole(['jobpadm', 'jobmodo']), 
  newsletterController.getSubscribers
);

export default router;
