import React, { useEffect, useState } from 'react';
import { Typography, Box, Paper, Grid, Divider, styled, Tabs, Tab } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  SmartToy as RobotIcon,
  Lightbulb as LightbulbIcon,
  HelpOutline as QuestionIcon,
  CreditCard as CreditCardIcon,
  History as HistoryIcon,
  Info as InfoIcon,
  PostAdd as PostAddIcon,
  Comment as CommentIcon,
  LocalOffer as LocalOfferIcon,
  Settings as SettingsIcon,
  Gavel as GavelIcon
} from '@mui/icons-material';
import AiCreditsCard from '../../components/AiCredits/AiCreditsCard';
import AiCreditsHistory from '../../components/AiCredits/AiCreditsHistory';
import AiConsentStatus from '../../components/AiCredits/AiConsentStatus';
import AiPromptsSection from '../../components/settings/AiPromptsSection';
import { notify } from '../../components/Notification';
import { useSubscription } from '../../hooks/useSubscription';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '0.9rem',
  textTransform: 'none',
  minHeight: '48px',
  borderRadius: '8px 8px 0 0',
  marginRight: '4px',
  transition: 'all 0.2s ease',
  [theme.breakpoints.up('sm')]: {
    minWidth: '160px',
  },
  '&.Mui-selected': {
    color: '#FF6B2C',
    backgroundColor: 'rgba(255, 107, 44, 0.04)',
  },
  '&:hover:not(.Mui-selected)': {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    color: '#FF6B2C',
  }
}));

const StyledTabs = styled(Tabs)({
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF6B2C',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    borderBottom: '1px solid #E2E8F0',
  }
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-credits-tabpanel-${index}`}
      aria-labelledby={`ai-credits-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AiCreditsPage: React.FC = () => {
  const location = useLocation();
  const { config, isLoading } = useSubscription();
  const [currentTab, setCurrentTab] = useState(0);

  useEffect(() => {
    // Vérifier si l'utilisateur revient d'une session de paiement Stripe
    const queryParams = new URLSearchParams(location.search);
    const success = queryParams.get('success');
    const canceled = queryParams.get('canceled');

    if (success === 'true') {
      notify('Paiement réussi ! Vos crédits IA ont été ajoutés à votre compte.', 'success');
    } else if (canceled === 'true') {
      notify('Paiement annulé. Aucun crédit IA n\'a été ajouté à votre compte.', 'info');
    }
  }, [location.search]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const useCases = [
    {
      icon: <LightbulbIcon sx={{ color: '#FF6B2C' }} />,
      title: "Génération de biographie",
      description: "Créez une biographie professionnelle et attrayante pour votre profil en quelques secondes."
    },
    {
      icon: <RobotIcon sx={{ color: '#FF6B2C' }} />,
      title: "Assistance rédactionnelle",
      description: "Obtenez de l'aide pour rédiger des descriptions de services claires et persuasives."
    },
    {
      icon: <PostAddIcon sx={{ color: '#FF6B2C' }} />,
      title: "Poster des missions",
      description: "Utilisez l'IA pour créer des descriptions de missions complètes et attractives."
    },
    {
      icon: <CommentIcon sx={{ color: '#FF6B2C' }} />,
      title: "Répondre aux avis",
      description: "Générez des réponses professionnelles et personnalisées aux avis de vos clients."
    },
    {
      icon: <LocalOfferIcon sx={{ color: '#FF6B2C' }} />,
      title: "Proposer une offre à une mission",
      description: "Créez des offres convaincantes pour augmenter vos chances de décrocher des missions."
    },
    {
      icon: <QuestionIcon sx={{ color: '#FF6B2C' }} />,
      title: "Et bientôt plus...",
      description: "D'autres fonctionnalités IA seront bientôt disponibles pour vous aider dans vos activités."
    }
  ];

  return (
    <div className="space-y-6 px-2 md:px-0">
      <motion.div>
        <div className="flex items-center justify-between mb-4">
          <PageTitle variant="h1">
            Crédits IA
          </PageTitle>
        </div>

        <Box sx={{ width: '100%' }}>
          <StyledTabs
            value={currentTab}
            onChange={handleTabChange}
            aria-label="ai-credits-tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <StyledTab
              icon={<CreditCardIcon className="w-4 h-4" />}
              iconPosition="start"
              label="Achat de crédits"
            />
            <StyledTab
              icon={<HistoryIcon className="w-4 h-4" />}
              iconPosition="start"
              label="Historique des crédits"
            />
            <StyledTab
              icon={<GavelIcon className="w-4 h-4" />}
              iconPosition="start"
              label="Consentement IA"
            />
            <StyledTab
              icon={<RobotIcon className="w-4 h-4" />}
              iconPosition="start"
              label="Personnalisation des prompts"
            />
          </StyledTabs>

          {/* Onglet Mes crédits */}
          <TabPanel value={currentTab} index={0}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid size={{ xs: 12 }}>
                <AiCreditsCard />
              </Grid>
              <Grid size={{ xs: 12 }} sx={{ mt: { xs: 2, sm: 3 } }}>
                 <Paper
                  component={motion.div}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  elevation={0}
                  sx={{
                    borderRadius: 2,
                    overflow: 'hidden',
                    position: 'relative',
                    border: '1px solid #e0e0e0',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.03)'
                  }}
                >
                  {/* Éléments décoratifs */}
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: -100,
                      right: -100,
                      width: 250,
                      height: 250,
                      borderRadius: '50%',
                      background: 'rgba(255, 150, 94, 0.04)',
                      zIndex: 0
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -80,
                      left: -80,
                      width: 200,
                      height: 200,
                      borderRadius: '50%',
                      background: 'rgba(255, 107, 44, 0.02)',
                      zIndex: 0
                    }}
                  />

                  <Box sx={{ p: { xs: 2.5, sm: 3.5 }, position: 'relative', zIndex: 1 }}>
                    {/* Titre avec ligne décorative */}
                    <Typography
                      variant="h6"
                      fontWeight="bold"
                      sx={{
                        mb: 3,
                        position: 'relative',
                        display: 'inline-block',
                        color: '#2D3748'
                      }}
                    >
                      À propos des crédits IA
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: -8,
                          left: 0,
                          width: '60%',
                          height: 3,
                          backgroundColor: '#FF6B2C',
                          borderRadius: 1
                        }}
                      />
                    </Typography>

                    {/* Description avec style */}
                    <Typography
                      variant="body1"
                      sx={{
                        mb: 3,
                        color: '#4B5563',
                        lineHeight: 1.6
                      }}
                    >
                      Les crédits IA vous permettent d'utiliser l'intelligence artificielle pour améliorer votre expérience sur JobPartiel.
                    </Typography>

                    {/* Alerte stylisée - nouveau design */}
                    <Box
                      component={motion.div}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.1 }}
                      sx={{
                        mb: 4,
                        borderRadius: 3,
                        position: 'relative',
                        overflow: 'hidden',
                        boxShadow: '0 8px 25px rgba(255, 107, 44, 0.08)'
                      }}
                    >
                      {/* En-tête avec dégradé */}
                      <Box
                        sx={{
                          p: 2.5,
                          background: 'linear-gradient(135deg, #FF6B2C 0%, #FF965E 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                          }}
                        >
                          <InfoIcon sx={{ color: 'white', fontSize: 24 }} />
                        </Box>

                        <Typography variant="subtitle1" fontWeight="bold" sx={{ color: 'white' }}>
                          Crédits inclus avec votre abonnement
                        </Typography>
                      </Box>

                      {/* Contenu */}
                      <Box
                        sx={{
                          p: 3,
                          background: 'white',
                          position: 'relative',
                          zIndex: 1,
                          display: 'flex',
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: 3
                        }}
                      >
                        {/* Élément décoratif */}
                        <Box
                          sx={{
                            position: 'absolute',
                            top: -60,
                            right: -60,
                            width: 120,
                            height: 120,
                            borderRadius: '50%',
                            background: 'rgba(255, 228, 186, 0.3)',
                            zIndex: 0
                          }}
                        />

                        {/* Abonnement gratuit */}
                        <Box
                          component={motion.div}
                          whileHover={{ y: -5 }}
                          transition={{ type: "spring", stiffness: 300 }}
                          sx={{
                            flex: 1,
                            p: 3,
                            borderRadius: 3,
                            background: 'linear-gradient(145deg, #ffffff, #fafafa)',
                            border: '1px solid #f0f0f0',
                            boxShadow: '0 6px 15px rgba(0, 0, 0, 0.03)',
                            position: 'relative',
                            overflow: 'hidden',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Box
                            sx={{
                              mb: 1.5,
                              p: 1.5,
                              background: 'linear-gradient(145deg, #FFF8F3, rgba(255, 228, 186, 0.3))',
                              borderRadius: '50%',
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                background: 'white',
                                boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)'
                              }}
                            >
                              <Typography variant="subtitle1" fontWeight="bold" color="#FF6B2C">
                                G
                              </Typography>
                            </Box>
                          </Box>

                          <Typography variant="subtitle2" fontWeight="medium" color="#4B5563" gutterBottom>
                            Abonnement Gratuit
                          </Typography>

                          <motion.div
                            key={isLoading ? 'loading' : config?.gratuit?.aiCredits?.included}
                            initial={{ scale: 0.8 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Typography
                              variant="h3"
                              fontWeight="bold"
                              sx={{
                                color: '#FF6B2C',
                                mt: 1
                              }}
                            >
                              {isLoading ? '...' : config?.gratuit?.aiCredits?.included || 0}
                            </Typography>
                          </motion.div>

                          <Typography
                            variant="body2"
                            sx={{
                              mt: 0.5,
                              color: '#6B7280',
                              backgroundColor: 'rgba(255, 107, 44, 0.05)',
                              borderRadius: 5,
                              px: 1.5,
                              py: 0.5,
                              display: 'inline-block'
                            }}
                          >
                            Crédits par mois
                          </Typography>
                        </Box>

                        {/* Abonnement premium */}
                        <Box
                          component={motion.div}
                          whileHover={{ y: -5 }}
                          transition={{ type: "spring", stiffness: 300 }}
                          sx={{
                            flex: 1,
                            p: 3,
                            borderRadius: 3,
                            background: 'linear-gradient(145deg, #FFF8F3, #FFEFDF)',
                            border: '1px solid #FFE4BA',
                            boxShadow: '0 6px 20px rgba(255, 107, 44, 0.1)',
                            position: 'relative',
                            overflow: 'hidden',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Box
                            sx={{
                              mb: 1.5,
                              p: 1.5,
                              background: 'linear-gradient(145deg, rgba(255, 107, 44, 0.1), rgba(255, 150, 94, 0.2))',
                              borderRadius: '50%',
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                background: 'white',
                                boxShadow: '0 2px 8px rgba(255, 107, 44, 0.2)'
                              }}
                            >
                              <Typography variant="subtitle1" fontWeight="bold" color="#FF6B2C">
                                P
                              </Typography>
                            </Box>
                          </Box>

                          <Typography variant="subtitle2" fontWeight="medium" color="#4B5563" gutterBottom>
                            Abonnement Premium
                          </Typography>

                          <motion.div
                            key={isLoading ? 'loading-premium' : config?.premium?.aiCredits?.included}
                            initial={{ scale: 0.8 }}
                            animate={{ scale: 1 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Typography
                              variant="h3"
                              fontWeight="bold"
                              sx={{
                                color: '#FF6B2C',
                                mt: 1
                              }}
                            >
                              {isLoading ? '...' : config?.premium?.aiCredits?.included || 0}
                            </Typography>
                          </motion.div>

                          <Typography
                            variant="body2"
                            sx={{
                              mt: 0.5,
                              color: '#6B7280',
                              backgroundColor: 'rgba(255, 107, 44, 0.15)',
                              borderRadius: 5,
                              px: 1.5,
                              py: 0.5,
                              display: 'inline-block'
                            }}
                          >
                            Crédits par mois
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <Divider sx={{ my: 3.5, borderColor: '#F0F0F0' }} />

                    {/* Titre utilisations possibles */}
                    <Typography
                      variant="h6"
                      fontWeight="bold"
                      sx={{
                        mb: 3,
                        position: 'relative',
                        display: 'inline-block',
                        color: '#2D3748'
                      }}
                    >
                      Utilisations possibles
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: -8,
                          left: 0,
                          width: '60%',
                          height: 3,
                          backgroundColor: '#FF6B2C',
                          borderRadius: 1
                        }}
                      />
                    </Typography>

                    {/* Grille des cas d'utilisation */}
                    <Grid container spacing={{ xs: 2, sm: 2.5 }}>
                      {useCases.map((useCase, index) => (
                        <Grid
                          size={{
                            xs: 12,
                            sm: 6,
                            md: 4
                          }}
                          key={index}
                        >
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 * index }}
                          >
                            <Box
                              sx={{
                                height: '100%',
                                borderRadius: 2,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                textAlign: 'center',
                                transition: 'all 0.3s ease',
                                p: 3,
                                bgcolor: 'white',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.04)',
                                border: '1px solid #F0F0F0',
                                '&:hover': {
                                  transform: 'translateY(-4px)',
                                  boxShadow: '0 8px 24px rgba(255, 107, 44, 0.12)',
                                  borderColor: '#FFE4BA'
                                }
                              }}
                            >
                              <Box
                                sx={{
                                  mb: 2,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  width: 56,
                                  height: 56,
                                  borderRadius: '50%',
                                  background: 'linear-gradient(135deg, #FFF8F3 0%, rgba(255, 228, 186, 0.4) 100%)',
                                }}
                              >
                                <Box sx={{ fontSize: 28, color: '#FF6B2C' }}>
                                  {useCase.icon}
                                </Box>
                              </Box>

                              <Typography
                                variant="subtitle1"
                                fontWeight="bold"
                                sx={{
                                  mb: 1.5,
                                  color: '#2D3748'
                                }}
                              >
                                {useCase.title}
                              </Typography>

                              <Typography
                                variant="body2"
                                sx={{
                                  color: '#4B5563',
                                  lineHeight: 1.6
                                }}
                              >
                                {useCase.description}
                              </Typography>
                            </Box>
                          </motion.div>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Onglet Historique */}
          <TabPanel value={currentTab} index={1}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid size={{ xs: 12 }}>
                <AiCreditsHistory limit={10} />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Onglet Consentement IA */}
          <TabPanel value={currentTab} index={2}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid size={{ xs: 12 }}>
                <AiConsentStatus />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Onglet Personnalisation des prompts IA */}
          <TabPanel value={currentTab} index={3}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid size={{ xs: 12 }}>
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <h2 className="text-xl font-semibold mb-4 flex items-center">
                      <RobotIcon className="mr-2 text-[#FF6B2C]" sx={{ fontSize: 24 }} />
                      Personnalisation des prompts IA
                    </h2>
                    <AiPromptsSection />
                  </div>
                </div>
              </Grid>
            </Grid>
          </TabPanel>
        </Box>
      </motion.div>
    </div>
  );
};

export default AiCreditsPage;
