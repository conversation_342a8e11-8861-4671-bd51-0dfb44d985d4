export interface DashboardStats {
  totalViews?: number;
  totalBookings?: number;
  totalRevenue?: number;
  conversionRate?: number;
  averageRating?: number;
}

export interface DailyStats {
  date?: string;
  views?: number;
  bookings?: number;
  revenue?: number;
}

export interface MonthlyStats {
  month?: string;
  views?: number;
  bookings?: number;
  revenue?: number;
}

export interface Subscription {
  id?: string;
  plan?: 'gratuit' | 'premium';
  startDate?: string;
  endDate?: string;
  autoRenew?: boolean;
  price?: number;
}

export interface Message {
  id?: string;
  senderId?: string;
  receiverId?: string;
  content?: string;
  timestamp?: string;
  read?: boolean;
  serviceId?: string;
}

export interface Notification {
  id?: string;
  userId?: string;
  type?: 'message' | 'referral' | 'subscription' | 'document' | 'system' | 'mission_comment' | 'profile' | 'mission' | 'jobi' | 'bug_report' | 'invoice' | 'subscription';
  title?: string;
  message?: string;
  priority?: 'normal' | 'high';
  readAt?: string;
  action?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
}

export interface VerificationDocument {
  id?: string;
  type?: 'identity' | 'address' | 'criminal' | 'professional';
  status?: 'pending' | 'approved' | 'rejected';
  uploadDate?: string;
  verificationDate?: string;
  documentUrl?: string;
  comments?: string;
}

export interface Coupon {
  id?: string;
  code?: string;
  discount?: number;
  type?: 'percentage' | 'fixed';
  startDate?: string;
  endDate?: string;
  usageLimit?: number;
  usageCount?: number;
  minimumAmount?: number;
  services?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface Revenue {
  period?: string;
  amount?: number;
  bookings?: number;
  platformFees?: number;
  netAmount?: number;
  taxAmount?: number;
  paymentMethod?: string;
  status?: 'pending' | 'paid' | 'failed';
}

export interface ReferralProgram {
  code?: string;
  invitedUsers?: number;
  successfulReferrals?: number;
  pendingReferrals?: number;
  earnedCredits?: number;
  availableCredits?: number;
}

export interface Referral {
  id?: string;
  referrerId?: string;
  referredEmail?: string;
  status?: 'pending' | 'completed' | 'expired';
  reward?: number;
  createdAt?: string;
  completedAt?: string;
}

export interface ReferralCode {
  code?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface DashboardData {
  stats?: {
    totalViews?: number;
    totalBookings?: number;
    totalRevenue?: number;
  };
  notifications?: any[];
  services?: any[];
  revenues?: any[];
  messages?: any[];
  verificationDocuments?: any[];
  subscription?: any;
  coupons?: any[];
  referralProgram?: any;
}