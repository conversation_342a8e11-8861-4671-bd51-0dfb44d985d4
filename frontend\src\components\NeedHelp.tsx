import { useState, useEffect } from 'react';

interface FAQItem {
  question: string;
  answer: string;
  category?: string;
}

const faqItems: FAQItem[] = [
  {
    question: "Quelles sont mes responsabilités concernant le contenu que je publie ?",
    answer: "Vous êtes entièrement responsable de tout contenu que vous publiez sur la plateforme. Ce contenu doit être conforme aux lois en vigueur, ne pas porter atteinte aux droits des tiers, et ne pas contenir d'éléments diffamatoires, injurieux ou discriminatoires. JobPartiel se réserve le droit de supprimer tout contenu inapproprié et d'appliquer des sanctions en cas de violation répétée.",
    category: 'general'
  },
  {
    question: "Comment JobPartiel protège-t-il mes données personnelles ?",
    answer: "Nous utilisons des protocoles de sécurité avancés conformes au RGPD. Vos données sont stockées sur des serveurs sécurisés en France, avec un chiffrement des communications et des transactions. Vous disposez d'un droit d'accès, de modification et de suppression de vos données. Pour plus de détails, consultez notre Politique de Confidentialité.",
    category: 'technical'
  },
  {
    question: "Quelles sont les limites de responsabilité de JobPartiel ?",
    answer: "JobPartiel est une plateforme de mise en relation et n'est pas responsable de l'exécution des prestations entre jobbeurs et clients. Nous ne garantissons pas la qualité des services, ne sommes pas partie aux contrats entre utilisateurs, et ne pouvons être tenus responsables des dommages résultant des transactions ou services réalisés via la plateforme.",
    category: 'general'
  },
  {
    question: "Comment puis-je proposer mes services ?",
    answer: "Après vous être inscrit, connectez-vous à votre compte et accédez à votre profil. Vous pouvez y ajouter des informations sur vos compétences, les services que vous proposez, vos tarifs, et télécharger des photos de vos travaux précédents.",
    category: 'general'
  },
  {
    question: "Comment les clients me contactent-ils ?",
    answer: "Les clients peuvent vous contacter directement via la messagerie intégrée de la plateforme, par e-mail, téléphone ou en répondant à vos annonces. Vous recevrez une notification lorsque vous recevez un message ou une demande de service.",
    category: 'general'
  },
  {
    question: "Comment puis-je annuler mon abonnement ?",
    answer: "Pour annuler votre abonnement, accédez à la section \"Mon Compte\" et cliquez sur \"Annuler l'abonnement\". Vous recevrez une confirmation de l'annulation par e-mail. Si vous avez des questions supplémentaires, notre support est disponible pour vous aider.",
    category: 'account'
  },
  {
    question: "Quels types de services puis-je proposer sur JobPartiel ?",
    answer: "JobPartiel accepte une large gamme de services : bricolage, jardinage, aide à domicile, soutien scolaire, services administratifs, etc. Vous pouvez proposer tout service légal qui correspond à vos compétences et qualifications.",
    category: 'general'
  },
  {
    question: "Comment fonctionne le paiement des prestations ?",
    answer: "Le paiement se fait directement entre le client et le jobbeur, sans intermédiaire. Vous convenez ensemble du montant et du mode de paiement qui vous convient le mieux (espèces, virement, chèque, etc.). JobPartiel facilite uniquement la mise en relation et ne prend aucune commission sur vos transactions.",
    category: 'pricing'
  },
  {
    question: "Suis-je assuré pendant mes prestations ?",
    answer: "Non, JobPartiel ne fournit aucune assurance pour vos prestations. Il est de votre responsabilité de souscrire une assurance responsabilité civile professionnelle adaptée à votre activité. Cette assurance est obligatoire pour certaines activités réglementées et fortement recommandée pour toutes les autres. JobPartiel ne peut être tenu responsable des dommages causés pendant l'exécution de vos prestations.",
    category: 'technical'
  },
  {
    question: "Quels sont les frais d'abonnement ?",
    answer: "JobPartiel fonctionne sur la base d'un abonnement mensuel. Cela vous donne un accès complet à la plateforme et à tous ses services, sans frais de commission supplémentaires sur vos prestations. Vous gardez 100% de vos revenus.",
    category: 'pricing'
  },
  {
    question: "Comment puis-je augmenter ma visibilité sur la plateforme ?",
    answer: "Pour augmenter votre visibilité, complétez intégralement votre profil, ajoutez des photos de qualité de vos réalisations, collectez des avis positifs et répondez rapidement aux demandes. Vous pouvez aussi opter pour nos options de mise en avant premium.",
    category: 'general'
  },
  {
    question: "Puis-je proposer plusieurs types de services différents ?",
    answer: "Oui, vous pouvez proposer autant de services que vous le souhaitez, tant que vous avez les compétences nécessaires. Nous vous encourageons à diversifier vos offres pour toucher un plus large public, tout en maintenant une qualité de service optimale.",
    category: 'general'
  },
  {
    question: "Comment sont gérés les litiges entre clients et jobbeurs ?",
    answer: "JobPartiel dispose d'un service de médiation dédié pour gérer les litiges. En cas de désaccord, contactez notre support qui analysera la situation et proposera une solution équitable pour les deux parties.",
    category: 'technical'
  },
  {
    question: "Puis-je mettre mon compte en pause temporairement ?",
    answer: "Oui, vous pouvez mettre votre compte en pause à tout moment depuis votre tableau de bord. Vos annonces seront temporairement masquées et vous ne recevrez plus de nouvelles demandes jusqu'à la réactivation de votre compte.",
    category: 'account'
  },
  {
    question: "Comment sont calculées les notes et avis des jobbeurs ?",
    answer: "Les notes sont basées sur les évaluations laissées par les clients après chaque prestation. La note globale prend en compte plusieurs critères : qualité du service, ponctualité, communication, et rapport qualité-prix.",
    category: 'general'
  },
  {
    question: "Y a-t-il une limite géographique pour proposer mes services ?",
    answer: "Vous pouvez définir votre zone d'intervention selon vos préférences. Vous pouvez choisir de travailler dans votre quartier, votre ville ou même proposer vos services à l'échelle nationale pour certaines prestations à distance.",
    category: 'general'
  },
  {
    question: "Comment puis-je modifier mes tarifs ?",
    answer: "Vous pouvez modifier vos tarifs à tout moment depuis votre espace jobbeur. Les changements seront immédiatement répercutés sur vos annonces, mais n'affecteront pas les prestations déjà confirmées.",
    category: 'pricing'
  },
  {
    question: "Que faire si je dois annuler une prestation ?",
    answer: "En cas d'annulation nécessaire, prévenez le client le plus tôt possible via la messagerie de la plateforme. Proposez si possible une date de report ou recommandez un autre jobbeur. Des annulations répétées peuvent affecter votre visibilité sur la plateforme.",
    category: 'general'
  },
  {
    question: "Comment puis-je obtenir le badge 'jobbeur Vérifié' ?",
    answer: "Le badge 'jobbeur Vérifié' est attribué après vérification de votre identité (pièce d'identité valide), de vos qualifications professionnelles (diplômes, certifications) et l'obtention d'un minimum de 5 avis positifs. Ce badge n'est pas une garantie absolue de qualité mais un indicateur de confiance. JobPartiel ne garantit pas la qualité des prestations, même pour les jobbeurs vérifiés.",
    category: 'account'
  },
  {
    question: "Puis-je proposer des forfaits ou des abonnements ?",
    answer: "Oui, vous pouvez créer des forfaits personnalisés ou des formules d'abonnement pour vos services réguliers. Cela permet de fidéliser vos clients et de leur offrir des tarifs préférentiels.",
    category: 'pricing'
  },
  {
    question: "Comment gérer mon planning et mes disponibilités ?",
    answer: "Un calendrier intégré vous permet de gérer vos disponibilités en temps réel. Vous pouvez bloquer des créneaux, définir vos horaires de travail et synchroniser votre agenda avec d'autres calendriers (Google Calendar, iCal, etc.).",
    category: 'general'
  },
  {
    question: "Quelles sont les options de personnalisation de mon profil ?",
    answer: "Votre profil peut être entièrement personnalisé avec une photo de profil, une description détaillée, vos certifications, vos réalisations en photos, vos zones d'intervention et vos disponibilités. Plus votre profil est complet, plus vous avez de chances d'être contacté.",
    category: 'account'
  },
  {
    question: "Comment puis-je recevoir mes paiements ?",
    answer: "Vous pouvez recevoir vos paiements directement des clients selon le mode de votre choix : espèces, virement bancaire, ou chèque. Pour plus de sécurité, nous recommandons les virements bancaires qui laissent une trace de la transaction.",
    category: 'pricing'
  },
  {
    question: "Y a-t-il un système de parrainage ?",
    answer: "Oui, JobPartiel propose un programme de parrainage. Vous recevez des avantages (mois d'abonnement gratuit, mise en avant premium) pour chaque nouveau jobbeur que vous parrainez et qui devient actif sur la plateforme.",
    category: 'general'
  },
  {
    question: "Comment puis-je gérer mes documents administratifs ?",
    answer: "Un espace dédié vous permet de stocker et gérer vos documents administratifs (assurance, diplômes, certifications). Vous pouvez les mettre à jour à tout moment et les partager facilement avec les clients qui le demandent.",
    category: 'account'
  },
  {
    question: "Existe-t-il une application mobile ?",
    answer: "Oui, JobPartiel dispose d'une application mobile disponible sur iOS et Android. Elle vous permet de gérer vos prestations, communiquer avec vos clients et recevoir des notifications en temps réel, même en déplacement.",
    category: 'technical'
  },
  {
    question: "Comment puis-je suivre mes performances sur la plateforme ?",
    answer: "Vous pouvez suivre vos performances via votre tableau de bord personnel. Celui-ci affiche des statistiques sur vos missions, vos revenus, et vos évaluations, vous permettant d'optimiser votre activité sur JobPartiel.",
    category: 'general'
  },
  {
    question: "Puis-je proposer des services à distance ?",
    answer: "Oui, JobPartiel permet de proposer des services à distance. Assurez-vous de préciser dans votre profil que vous offrez des prestations en ligne et de bien définir votre zone d'intervention pour les services physiques.",
    category: 'general'
  },
  {
    question: "Comment puis-je mettre à jour mes informations de profil ?",
    answer: "Vous pouvez mettre à jour vos informations de profil à tout moment en accédant à la section 'Mon Profil' dans votre compte. Assurez-vous que vos informations sont toujours à jour pour maximiser vos chances d'être contacté par des clients.",
    category: 'account'
  },
  {
    question: "Quels sont les critères pour obtenir des avis positifs ?",
    answer: "Pour obtenir des avis positifs, assurez-vous de fournir un service de qualité, d'être ponctuel, et de maintenir une bonne communication avec vos clients. Demandez à vos clients de laisser un avis après chaque prestation pour améliorer votre profil.",
    category: 'general'
  },
  {
    question: "Comment fonctionne le système de badges et de récompenses ?",
    answer: "Le système de badges et de récompenses de JobPartiel reconnaît les jobbeurs qui se distinguent par la qualité de leurs services. Vous pouvez gagner des badges pour des avis positifs, un nombre élevé de missions complétées, et d'autres critères de performance.",
    category: 'general'
  },
  {
    question: "Puis-je offrir des promotions ou des réductions ?",
    answer: "Oui, vous pouvez proposer des promotions ou des réductions à vos clients. Utilisez la section 'Offres Spéciales' de votre profil pour créer et gérer vos promotions, et attirer ainsi plus de clients.",
    category: 'pricing'
  },
  {
    question: "Comment puis-je signaler un comportement inapproprié ?",
    answer: "Si vous rencontrez un comportement inapproprié, vous pouvez le signaler via la plateforme en utilisant le bouton 'Signaler' sur le profil du client ou du jobbeur concerné. Notre équipe de modération examinera le signalement et prendra les mesures appropriées selon notre échelle de sanctions (avertissement, suspension temporaire ou définitive).",
    category: 'technical'
  },
  {
    question: "Comment fonctionne le système de modération sur JobPartiel ?",
    answer: "JobPartiel utilise un système de modération a posteriori basé sur les signalements des utilisateurs, des contrôles aléatoires et des outils automatisés de détection. En cas de violation des CGV, nous appliquons une échelle de sanctions proportionnée : avertissement, suspension temporaire (24h à 30 jours), ou suspension définitive du compte selon la gravité et la récurrence des infractions.",
    category: 'technical'
  },
  {
    question: "Comment puis-je vérifier l'authenticité d'un jobbeur ?",
    answer: "JobPartiel vérifie l'identité des jobbeurs via des documents officiels. Recherchez le badge 'jobbeur Vérifié' sur leur profil pour vous assurer de leur authenticité.",
    category: 'technical'
  },
  {
    question: "Puis-je réserver un service pour une date future ?",
    answer: "Oui, vous pouvez réserver un service pour une date future en contactant directement le jobbeur via la messagerie intégrée et en convenant d'une date qui vous convient.",
    category: 'general'
  },
  {
    question: "Comment puis-je annuler une réservation ?",
    answer: "Pour annuler une réservation, contactez le jobbeur via la messagerie intégrée. Assurez-vous de le faire le plus tôt possible pour éviter des désagréments.",
    category: 'account'
  },
  {
    question: "Quels types de paiement sont acceptés ?",
    answer: "Les jobbeurs acceptent divers modes de paiement, notamment les espèces, les virements bancaires, les jobbis et les chèques. Vérifiez les préférences de paiement du jobbeur avant de confirmer la prestation.",
    category: 'pricing'
  },
  {
    question: "Comment puis-je modifier ma zone d'intervention ?",
    answer: "Vous pouvez modifier votre zone d'intervention à tout moment en accédant à votre profil et en ajustant les paramètres de localisation.",
    category: 'account'
  },
  {
    question: "Existe-t-il un support client disponible 24/7 ?",
    answer: "Notre support client est disponible pour vous aider pendant les heures ouvrables (du lundi au vendredi, de 9h à 18h), et non 24/7. Vous pouvez nous contacter via la messagerie intégrée ou par e-mail pour toute assistance. Les demandes reçues en dehors des heures ouvrables seront traitées lors du prochain jour ouvré.",
    category: 'technical'
  },
  {
    question: "Comment puis-je signaler un problème technique sur la plateforme ?",
    answer: "Si vous rencontrez un problème technique, veuillez le signaler via le formulaire de contact ou la messagerie intégrée. Notre équipe technique s'efforcera de résoudre le problème rapidement.",
    category: 'technical'
  },
  {
    question: "Puis-je partager mon profil sur les réseaux sociaux ?",
    answer: "Oui, vous pouvez partager votre profil et vos services sur les réseaux sociaux pour attirer plus de clients. Utilisez les options de partage disponibles sur votre profil.",
    category: 'general'
  },
  {
    question: "Comment puis-je améliorer ma visibilité sur la plateforme ?",
    answer: "Pour améliorer votre visibilité, complétez intégralement votre profil, obtenez des avis positifs, et répondez rapidement aux demandes des clients. Vous pouvez également opter pour des options de mise en avant premium.",
    category: 'general'
  },
  {
    question: "Quels sont les avantages de la messagerie intégrée ?",
    answer: "La messagerie intégrée vous permet de communiquer directement avec les clients et les jobbeurs, de suivre l'historique des échanges, et de recevoir des notifications en temps réel.",
    category: 'technical'
  },
  {
    question: "Comment puis-je accéder à mes factures et reçus ?",
    answer: "Vous pouvez accéder à vos factures et reçus depuis votre tableau de bord. Toutes vos transactions sont enregistrées et peuvent être téléchargées au format PDF à tout moment. Ces documents sont générés conformément aux exigences légales françaises et sont conservés selon les obligations légales de conservation.",
    category: 'account'
  },
  {
    question: "Comment fonctionne le module de facturation de JobPartiel ?",
    answer: "Le module de facturation vous permet de créer des devis, factures et avoirs professionnels. Vous pouvez personnaliser ces documents avec votre identité visuelle, les envoyer directement par email à vos clients, et suivre leur statut (brouillon, émis, accepté, payé, etc.). Les documents sont numérotés automatiquement selon un système séquentiel conforme aux exigences légales.",
    category: 'account'
  },
  {
    question: "Puis-je proposer des services en dehors de ma ville ?",
    answer: "Oui, vous pouvez proposer des services en dehors de votre ville. Assurez-vous de définir clairement votre zone d'intervention dans votre profil.",
    category: 'general'
  },
  {
    question: "Comment puis-je recevoir des notifications sur mon téléphone ?",
    answer: "Activez les notifications push dans les paramètres de l'application mobile pour recevoir des alertes en temps réel sur votre téléphone.",
    category: 'technical'
  },
  {
    question: "Comment fonctionne le système de notation ?",
    answer: "Le système de notation permet aux clients d'évaluer les prestations des jobbeurs. Les notes sont basées sur la qualité du service, la ponctualité, et la communication.",
    category: 'general'
  },
  {
    question: "Puis-je ajouter des photos de mes réalisations ?",
    answer: "Oui, vous pouvez ajouter des photos de vos réalisations à votre profil pour montrer votre travail et attirer plus de clients.",
    category: 'general'
  },
  {
    question: "Comment puis-je contacter le support technique ?",
    answer: "Vous pouvez contacter le support technique via la messagerie intégrée ou par e-mail. Notre équipe est là pour vous aider avec tout problème technique.",
    category: 'technical'
  },
  {
    question: "Quels sont les critères pour devenir un jobbeur vérifié ?",
    answer: "Pour devenir un jobbeur vérifié, vous devez fournir des documents officiels prouvant votre identité et vos qualifications. Obtenez également un minimum de 5 avis positifs.",
    category: 'account'
  },
  {
    question: "Comment puis-je changer mon mot de passe ?",
    answer: "Vous pouvez changer votre mot de passe en accédant à la section 'Mon Compte' et en sélectionnant l'option 'Changer le mot de passe'. Pour des raisons de sécurité, nous vous recommandons de choisir un mot de passe fort et unique, et de le changer régulièrement.",
    category: 'account'
  },
  {
    question: "Quelles mesures de sécurité sont mises en place pour protéger mon compte ?",
    answer: "JobPartiel utilise plusieurs mesures de sécurité : chiffrement des communications, protection contre les tentatives de connexion multiples (maximum 5 tentatives avant verrouillage temporaire), notifications de connexion suspecte, tokens d'authentification renouvelés périodiquement, et vérification d'identité pour les fonctionnalités sensibles.",
    category: 'technical'
  },
  {
    question: "Puis-je désactiver temporairement mon compte ?",
    answer: "Oui, vous pouvez désactiver temporairement votre compte depuis votre tableau de bord. Vos annonces seront masquées jusqu'à la réactivation de votre compte.",
    category: 'account'
  },
  {
    question: "Comment puis-je obtenir de l'aide pour remplir mon profil ?",
    answer: "Consultez notre guide d'aide en ligne ou contactez notre support client pour obtenir des conseils sur la façon de remplir votre profil de manière efficace.",
    category: 'account'
  },
  {
    question: "Puis-je proposer des services saisonniers ou ponctuels ?",
    answer: "Absolument ! JobPartiel est idéal pour les services saisonniers comme le jardinage au printemps, le déneigement en hiver, ou les petits travaux ponctuels. Vous pouvez indiquer vos disponibilités et types de services saisonniers dans votre profil.",
    category: 'general'
  },
  {
    question: "Comment sont protégées mes informations personnelles ?",
    answer: "Nous utilisons des protocoles de sécurité avancés pour protéger vos données personnelles. Seules les informations nécessaires à la prestation de service sont partagées. Vous pouvez gérer vos paramètres de confidentialité dans votre espace personnel.",
    category: 'technical'
  },
  {
    question: "Puis-je spécialiser mes services pour certains publics ?",
    answer: "Oui, vous pouvez vous spécialiser dans des services destinés à des publics spécifiques comme les personnes âgées, les familles, ou les entreprises. Précisez ces spécialisations dans votre profil pour attirer des clients ciblés.",
    category: 'general'
  },
  {
    question: "Comment gérer mes indemnités kilométriques ?",
    answer: "Vous pouvez inclure vos frais de déplacement dans vos tarifs. Il est recommandé de les détailler clairement avec le client avant la prestation et de les mentionner sur votre devis ou facture.",
    category: 'pricing'
  },
  {
    question: "Existe-t-il des formations proposées sur la plateforme ?",
    answer: "JobPartiel propose des webinaires et des ressources de formation pour vous aider à développer vos compétences et à améliorer votre activité. Consultez la section 'Formations' dans votre espace jobbeur.",
    category: 'account'
  },
  {
    question: "Comment puis-je me différencier des autres jobbeurs ?",
    answer: "Mettez en avant vos compétences uniques, vos certifications, votre expérience, et vos réalisations. Des photos de qualité, des descriptions détaillées et des avis positifs peuvent vous démarquer.",
    category: 'general'
  },
  {
    question: "Puis-je refuser une mission ?",
    answer: "Oui, vous avez la liberté de refuser une mission qui ne correspond pas à vos compétences, vos disponibilités ou vos conditions. Nous vous recommandons de communiquer clairement et professionnellement.",
    category: 'general'
  },
  {
    question: "Comment sont calculés mes revenus sur la plateforme ?",
    answer: "Vos revenus sont intégralement basés sur vos prestations. JobPartiel ne prélève aucune commission sur vos services. Vous recevez 100% du montant convenu avec le client, sous réserve du paiement de votre abonnement mensuel à la plateforme.",
    category: 'pricing'
  },
  {
    question: "Puis-je proposer des services en équipe ?",
    answer: "Oui, vous pouvez collaborer avec d'autres jobbeurs pour des missions nécessitant plusieurs compétences. Vous pouvez créer un profil de groupe ou mentionner vos collaborations.",
    category: 'general'
  },
  {
    question: "Comment gérer mes impôts en tant que jobbeur ?",
    answer: "JobPartiel fournit des outils de suivi de revenus qui peuvent vous aider à organiser vos données financières. Cependant, vous êtes seul responsable de vos obligations fiscales et sociales. Nous vous recommandons de consulter un expert-comptable ou l'administration fiscale pour vous assurer de respecter toutes vos obligations légales.",
    category: 'pricing'
  },
  {
    question: "Quels sont les délais de paiement recommandés ?",
    answer: "Nous recommandons de convenir des délais de paiement avant la prestation. Un délai standard est de 30 jours après la réalisation du service, mais cela peut varier selon votre accord avec le client.",
    category: 'pricing'
  },
  {
    question: "Puis-je proposer des services de consultation en ligne ?",
    answer: "Oui, JobPartiel permet les services à distance comme le conseil, le coaching, les cours en ligne, le support administratif ou technique. Précisez bien les modalités de votre service en ligne.",
    category: 'general'
  },
  {
    question: "Comment sont gérés les acomptes et les arrhes ?",
    answer: "Vous pouvez demander un acompte pour les missions importantes. Assurez-vous de le mentionner clairement dans votre devis et d'obtenir un accord écrit du client avec la mention 'bon pour accord' et une signature. Pour une sécurité juridique maximale, nous recommandons de faire signer physiquement vos devis importants, particulièrement ceux impliquant des acomptes significatifs.",
    category: 'pricing'
  },
  {
    question: "Quelle est la valeur juridique des documents générés sur JobPartiel ?",
    answer: "Les documents générés sur JobPartiel (devis, factures, avoirs) sont conformes aux exigences légales françaises. Cependant, pour qu'un devis ait une valeur juridique complète, il doit être signé par le client avec la mention manuscrite 'bon pour accord'. L'acceptation électronique simple ne remplace pas cette exigence légale. Nous vous recommandons de toujours obtenir une signature formelle pour vos devis importants.",
    category: 'technical'
  },
  {
    question: "Existe-t-il une assurance pour les jobbeurs ?",
    answer: "JobPartiel ne fournit aucune assurance pour les jobbeurs. Vous devez obligatoirement souscrire votre propre assurance responsabilité civile professionnelle adaptée à vos services, particulièrement pour les activités réglementées. Cette assurance est indispensable pour vous protéger en cas de dommages causés pendant vos prestations. JobPartiel ne peut en aucun cas être tenu responsable des incidents survenant lors de vos missions.",
    category: 'technical'
  },
  {
    question: "Comment gérer un client difficile ?",
    answer: "En cas de difficulté, restez professionnel. Communiquez clairement, documentez les échanges, et si nécessaire, contactez notre service de médiation pour une résolution amiable.",
    category: 'general'
  },
  {
    question: "Comment développer ma clientèle sur JobPartiel ?",
    answer: "Complétez votre profil, obtenez des avis positifs, répondez rapidement, proposez une communication claire, et utilisez les options de mise en avant premium pour augmenter votre visibilité.",
    category: 'general'
  },
  {
    question: "Quels sont les avantages fiscaux pour les jobbeurs ?",
    answer: "En tant qu'auto-entrepreneur ou indépendant, vous pouvez bénéficier de certains régimes fiscaux adaptés à votre situation. JobPartiel ne fournit pas de conseils fiscaux personnalisés. Nous vous recommandons vivement de consulter un expert-comptable ou l'administration fiscale pour des conseils adaptés à votre situation particulière.",
    category: 'pricing'
  },
  {
    question: "Comment puis-je gérer mes données personnelles et mon droit à la déconnexion ?",
    answer: "Vous avez un contrôle total sur vos données personnelles. Vous pouvez à tout moment consulter, modifier ou supprimer vos informations depuis votre espace personnel. Le droit à la déconnexion vous permet de suspendre temporairement votre activité sans perdre vos informations.",
    category: 'account'
  },
  {
    question: "Existe-t-il des outils de gestion de projet pour les jobbeurs ?",
    answer: "Oui, JobPartiel propose des outils intégrés de gestion de projet. Vous pouvez suivre vos missions, gérer vos plannings, créer des devis, et suivre vos revenus depuis un tableau de bord complet et intuitif.",
    category: 'technical'
  },
  {
    question: "Comment sont protégées mes transactions financières ?",
    answer: "Nous utilisons des protocoles de sécurité bancaire de pointe (chiffrement SSL, authentification à deux facteurs) pour garantir la sécurité de toutes vos transactions financières sur la plateforme.",
    category: 'technical'
  },
  {
    question: "Puis-je exporter mes données et mes factures ?",
    answer: "Oui, vous pouvez exporter l'ensemble de vos données, historique de missions, et factures dans différents formats (PDF, CSV) depuis votre espace personnel. Cela facilite votre comptabilité et vos déclarations fiscales.",
    category: 'account'
  },
  {
    question: "Comment sont gérés les conflits entre jobbeurs et clients ?",
    answer: "Notre service de médiation intervient en cas de litige. Nous analysons objectivement la situation, écoutons les deux parties, et proposons des solutions équitables. En cas de désaccord grave, nous pouvons suspendre temporairement un compte. Conformément au Code de la consommation, les utilisateurs consommateurs peuvent également recourir gratuitement à notre service de médiation externe. Notez que JobPartiel n'a pas le pouvoir d'imposer une solution et ne peut pas garantir le remboursement des sommes versées entre utilisateurs.",
    category: 'technical'
  },
  {
    question: "Quelle loi s'applique en cas de litige sur JobPartiel ?",
    answer: "Les Conditions Générales de Vente de JobPartiel sont soumises au droit français. En cas de litige non résolu à l'amiable, compétence exclusive est attribuée aux tribunaux du ressort de la Cour d'appel de Paris, sauf pour les utilisateurs consommateurs pour lesquels les règles légales de compétence territoriale s'appliquent.",
    category: 'technical'
  },
  {
    question: "Quelles sont les limites de responsabilité de JobPartiel ?",
    answer: "JobPartiel est une plateforme de mise en relation. Nous ne sommes pas responsables de l'exécution des prestations. Nous recommandons aux jobbeurs et aux clients de bien définir les termes de leur accord et de souscrire les assurances appropriées.",
    category: 'technical'
  },
  {
    question: "Comment puis-je me protéger des arnaques ?",
    answer: "Restez vigilant : ne partagez pas d'informations personnelles sensibles, utilisez uniquement la messagerie de la plateforme, demandez un acompte pour les grandes missions avec un devis signé comportant la mention 'bon pour accord', et signalez immédiatement tout comportement suspect à notre équipe. Ne transférez jamais d'argent à un utilisateur qui vous le demande en dehors du cadre d'une prestation clairement définie.",
    category: 'technical'
  },
  {
    question: "Existe-t-il des certifications ou des formations proposées ?",
    answer: "Nous proposons des parcours de certification pour les jobbeurs souhaitant valider et améliorer leurs compétences. Ces certifications internes à JobPartiel ne remplacent pas les diplômes ou certifications professionnelles officielles. Elles constituent un indicateur de compétence sur notre plateforme mais n'ont pas de valeur légale ou académique reconnue par l'État.",
    category: 'account'
  },
  {
    question: "Comment sont utilisées mes données personnelles ?",
    answer: "Vos données sont utilisées uniquement pour faciliter les mises en relation et améliorer nos services. Nous ne vendons jamais vos informations à des tiers. Vos données sont conservées pendant la durée nécessaire aux finalités pour lesquelles elles ont été collectées, puis anonymisées ou supprimées. Consultez notre politique de confidentialité pour plus de détails.",
    category: 'technical'
  },
  {
    question: "Comment puis-je exercer mes droits RGPD sur mes données personnelles ?",
    answer: "Vous pouvez exercer vos droits d'accès, de rectification, d'effacement, de limitation, de portabilité et d'opposition concernant vos données personnelles en contactant notre Délégué à la Protection des Données via le formulaire dédié dans votre espace personnel ou par email. Nous traiterons votre demande dans un délai maximum d'un mois.",
    category: 'technical'
  },
  {
    question: "Quels sont les prérequis techniques pour utiliser JobPartiel ?",
    answer: "Vous avez besoin d'un appareil avec connexion internet (ordinateur, tablette ou smartphone) et d'un navigateur web récent. Notre application mobile est compatible avec iOS (10.0+) et Android (5.0+). Une connexion internet stable est recommandée.",
    category: 'technical'
  },
  {
    question: "Comment fonctionne l'algorithme de recommandation de jobbeurs ?",
    answer: "Notre algorithme prend en compte plusieurs critères : proximité géographique, compétences, tarifs, disponibilités, avis clients, et taux de réponse. L'objectif est de proposer les jobbeurs les plus pertinents pour chaque demande. Ces recommandations sont fournies à titre indicatif et ne constituent pas un engagement de résultat.",
    category: 'technical'
  },
  {
    question: "Comment puis-je signaler un bug ou suggérer une amélioration ?",
    answer: "Vous pouvez signaler un bug ou suggérer une amélioration via notre système de signalement accessible depuis toutes les pages de la plateforme. Votre rapport sera examiné par notre équipe technique qui le traitera selon sa priorité. Vous serez informé des principales évolutions du traitement de votre signalement par email.",
    category: 'technical'
  },
  {
    question: "Puis-je intégrer JobPartiel avec d'autres outils de gestion ?",
    answer: "Nous proposons des API et des intégrations avec des outils de gestion courants comme Google Agenda. Ces intégrations sont fournies en l'état, sans garantie de fonctionnement parfait avec tous les logiciels tiers. Consultez notre section 'Intégrations' pour plus de détails et les limitations connues.",
    category: 'technical'
  },
  {
    question: "Quelles sont les mesures de performance de la plateforme ?",
    answer: "Nous suivons plusieurs indicateurs clés : temps de réponse des jobbeurs, taux de satisfaction clients, nombre de missions réussies, et temps moyen de mise en relation. Ces KPIs nous permettent d'améliorer continuellement notre service.",
    category: 'technical'
  },
  {
    question: "Comment fonctionne la vérification d'identité ?",
    answer: "La vérification d'identité implique la fourniture de documents officiels (carte d'identité, justificatif de domicile, extrait de casier judiciaire). Nos équipes vérifient manuellement ces documents pour renforcer la sécurité de la plateforme. Cette vérification ne constitue pas une garantie absolue et JobPartiel ne peut être tenu responsable en cas de fraude d'identité sophistiquée. Les utilisateurs doivent toujours faire preuve de vigilance.",
    category: 'account'
  },
  {
    question: "Quels sont les processus de modération des contenus ?",
    answer: "Tous les profils, messages et avis sont soumis à une modération automatique et manuelle. Nous utilisons des algorithmes de détection de contenu inapproprié et une équipe dédiée examine les signalements. Cependant, notre modération est principalement réactive et basée sur les signalements. Nous ne pouvons garantir la détection immédiate de tous les contenus problématiques et comptons sur la vigilance de notre communauté.",
    category: 'technical'
  },
  {
    question: "Comment sont gérées les mises à jour de la plateforme ?",
    answer: "Nous effectuons des mises à jour régulières (tous les 15 jours en moyenne) avec de nouvelles fonctionnalités, des améliorations de sécurité et des corrections de bugs. Les utilisateurs sont informés via des notifications in-app et par email. Les mises à jour importantes peuvent nécessiter une acceptation des nouvelles CGV.",
    category: 'technical'
  },
  {
    question: "Que se passe-t-il en cas de modification des CGV ?",
    answer: "En cas de modification substantielle des CGV, vous serez informé par email et/ou notification sur la plateforme au moins 15 jours avant leur entrée en vigueur. Vous devrez accepter les nouvelles conditions pour continuer à utiliser la plateforme. Si vous refusez, vous pourrez clôturer votre compte et demander le remboursement prorata temporis de votre abonnement en cours.",
    category: 'account'
  },
  {
    question: "Existe-t-il un programme de beta-test ou de suggestion de fonctionnalités ?",
    answer: "Oui, nous avons un programme de beta-test où les utilisateurs peuvent tester de nouvelles fonctionnalités avant leur déploiement général. Vous pouvez également soumettre vos suggestions via notre formulaire dédié dans l'espace utilisateur.",
    category: 'account'
  },
  {
    question: "Comment sont sécurisées les communications sur la plateforme ?",
    answer: "Toutes les communications passent par notre messagerie sécurisée avec chiffrement de bout en bout. Les échanges de coordonnées personnelles sont filtrés pour protéger la vie privée des utilisateurs. Cependant, nous vous recommandons de ne jamais partager d'informations sensibles (coordonnées bancaires, mots de passe) via la messagerie, même si elle est sécurisée.",
    category: 'technical'
  },
  {
    question: "Quelles sont les politiques de confidentialité et de protection des données ?",
    answer: "Nous sommes conformes au RGPD. Vos données sont stockées sur des serveurs sécurisés en France, vous avez un droit d'accès, de modification et de suppression. Nous ne conservons que les informations nécessaires à la fourniture de nos services. Notez que malgré nos mesures de sécurité, aucun système n'est infaillible et nous ne pouvons garantir une sécurité absolue des données.",
    category: 'technical'
  },
  {
    question: "Comment puis-je optimiser ma visibilité sur JobPartiel ?",
    answer: "Pour maximiser votre visibilité, assurez-vous d'avoir un profil complet avec des photos de qualité, des descriptions détaillées de vos services, et des tarifs clairs. Répondez rapidement aux demandes, collectez des avis positifs, et considérez les options de mise en avant premium.",
    category: 'general'
  },
  {
    question: "Quelles sont les obligations légales des jobbeurs proposant des services réglementés ?",
    answer: "Les jobbeurs proposant des services dans des secteurs réglementés (bâtiment, services à la personne, etc.) doivent disposer des qualifications, autorisations et assurances requises par la loi. JobPartiel informe les utilisateurs des secteurs avec réglementation mais ne garantit pas l'exhaustivité des informations. L'utilisateur reste seul responsable de vérifier et respecter les conditions légales applicables à son activité.",
    category: 'general'
  },
  {
    question: "Comment fonctionne le système de support de JobPartiel ?",
    answer: "Notre système de support est accessible via un formulaire dédié dans votre espace personnel. Chaque demande génère un ticket avec un identifiant unique, permettant de suivre son traitement. Vous pouvez joindre des fichiers à votre demande (images, documents) pour faciliter la résolution de votre problème. Notre équipe s'efforce de répondre dans les meilleurs délais selon la priorité de votre demande.",
    category: 'technical'
  }
];

function NeedHelp() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAllResults, setShowAllResults] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const questionsPerPage = 6;

  // Effet pour réinitialiser l'état lors du changement de catégorie ou de recherche
  useEffect(() => {
    setCurrentPage(0);
    setOpenItems([]);
    setShowAllResults(false);
  }, [selectedCategory, searchQuery]);

  // Les catégories sont maintenant définies directement dans les boutons

  // Fonction de recherche améliorée qui ignore les espaces superflus
  const filteredQuestions = faqItems.filter(item =>
    (selectedCategory === 'all' || item.category === selectedCategory) &&
    (searchQuery.trim() === '' ||
    item.question.toLowerCase().includes(searchQuery.trim().toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.trim().toLowerCase()))
  );

  const startIndex = currentPage * questionsPerPage;
  const visibleItems = showAllResults
    ? filteredQuestions
    : filteredQuestions.slice(startIndex, startIndex + questionsPerPage);
  const hasMoreItems = !showAllResults && startIndex + questionsPerPage < filteredQuestions.length;
  const hasPreviousItems = !showAllResults && currentPage > 0;

  // Fonction pour gérer l'ouverture et la fermeture des questions
  const toggleItem = (index: number) => {
    const isEven = index % 2 === 0;
    const pairIndex = isEven ? index + 1 : index - 1;
    const pairExists = pairIndex < visibleItems.length;

    setOpenItems(prev => {
      // Sur les écrans < md (768px), on gère chaque élément individuellement
      if (window.matchMedia('(max-width: 767px)').matches) {
        const newOpenItems = prev.includes(index)
          ? prev.filter(item => item !== index)
          : [...prev, index];

        // Ajout du défilement automatique après la mise à jour de l'état
        setTimeout(() => {
          const element = document.querySelector(`[data-needhelp-index="${index}"]`);
          if (element) {
            const elementRect = element.getBoundingClientRect();
            const offset = 100; // Augmenté à 100px pour tenir compte du menu
            const targetScroll = window.pageYOffset + elementRect.top - offset;
            window.scrollTo({
              top: targetScroll,
              behavior: 'smooth'
            });
          }
        }, 0);

        return newOpenItems;
      } else {
        // Sur les écrans ≥ md, on garde la synchronisation par paire
        const bothClosed = !prev.includes(index) && !prev.includes(pairIndex);
        if (bothClosed) {
          return pairExists ? [...prev, index, pairIndex] : [...prev, index];
        } else {
          return prev.filter(item => item !== index && item !== pairIndex);
        }
      }
    });
  };

  // Fonction pour gérer le changement de page avec défilement
  const changePage = (direction: 'next' | 'prev') => {
    setIsAnimating(true);

    // Mettre à jour la page
    const newPage = direction === 'next' ? currentPage + 1 : currentPage - 1;
    setCurrentPage(newPage);

    // Réinitialiser les éléments ouverts
    setOpenItems([]);

    // Attendre que l'animation de fondu soit terminée
    setTimeout(() => {
      setIsAnimating(false);

      // Attendre que le nouveau contenu soit rendu avant de défiler
      requestAnimationFrame(() => {
        const needhelpAccordion = document.getElementById('needhelp-accordion');
        if (needhelpAccordion && window.innerWidth <= 768) {
          const accordionRect = needhelpAccordion.getBoundingClientRect();
          const scrollTop = window.pageYOffset + accordionRect.top - 40; // 40px de marge
          window.scrollTo({
            top: scrollTop,
            behavior: 'smooth'
          });
        }
      });
    }, 300);
  };



  return (
      <section className="pt-0 bg-white to-white relative overflow-hidden">
      {/* Cercles décoratifs */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-[#FF7A35]/5 rounded-full -translate-x-1/2 -translate-y-1/2" />
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full translate-x-1/2 translate-y-1/2" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-start">
          {/* Left Column - Image - Hidden on mobile */}
          <div className="relative order-2 lg:order-1 hidden lg:block">
            <div className="relative overflow-hidden h-full p-8">
              <img
                src="/images/image-accueil-besoin-aide.png"
                alt="Besoin d'aide"
                className="w-full h-full object-cover"
              />
              {/* Griffe icon overlay */}
              <div className="absolute -right-12 top-1/2 -translate-y-1/2 w-24 h-auto transform hover:scale-110 transition-transform duration-300">
                <img
                  src="/images/griffe-icon-image-accueil.png"
                  alt="Griffe décorative"
                  className="w-full h-auto"
                />
              </div>
              {/* Counter overlay */}
              <div className="absolute top-8 left-8 bg-[#FFD700] rounded-lg p-6 shadow-lg">
                <div className="text-center">
                  <div className="text-4xl font-bold">250+</div>
                  <div className="text-sm">Clients<br />Satisfaits</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="mt-24 pb-24">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4">
                Questions <span className="text-[#FF7A35]">Fréquentes</span>
              </h2>
              <div className="flex justify-center items-center gap-2 mb-6">
                <div className="h-1 w-20 bg-gradient-to-r from-transparent to-[#FF7A35] opacity-50"></div>
                <div className="w-2 h-2 rounded-full bg-[#FF7A35]"></div>
                <div className="h-1 w-20 bg-gradient-to-l from-transparent to-[#FF7A35] opacity-50"></div>
              </div>

              {/* Nouvelle barre de recherche et filtres */}
              <div id="main-search-section" className="sticky top-0 bg-white pt-4 pb-6 -mx-4 px-4 z-10">
                <div className="max-w-4xl mx-auto px-4">
                  {/* Barre de recherche */}
                  <div className="relative mb-6">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#FF7A35] to-orange-400 rounded-xl blur opacity-10"></div>
                    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden">
                      <div className="flex items-center px-6 py-4">
                        <svg className="w-5 h-5 text-[#FF7A35]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input
                          type="text"
                          placeholder="Rechercher une question..."
                          value={searchQuery}
                          onChange={(e) => {
                            setSearchQuery(e.target.value);
                            if (e.target.value.trim() === '') {
                              setShowAllResults(false);
                            }
                          }}
                          className="flex-1 ml-4 text-gray-700 placeholder-gray-400 bg-transparent outline-none"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Filtres de catégorie */}
                  <div className="flex flex-col sm:flex-row sm:flex-wrap sm:justify-center gap-2 mb-6">
                    <button
                      onClick={() => {
                        setSelectedCategory('all');
                        setSearchQuery('');
                        setShowAllResults(false);
                      }}
                      className={`w-full sm:w-auto px-4 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                        selectedCategory === 'all'
                          ? 'bg-[#FF7A35] text-white shadow-lg hover:bg-[#ff8d4f]'
                          : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <span>🔍</span>
                        <span>Toutes les questions</span>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        setSelectedCategory('general');
                        setSearchQuery('');
                        setShowAllResults(false);
                      }}
                      className={`w-full sm:w-auto px-4 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                        selectedCategory === 'general'
                          ? 'bg-[#FF7A35] text-white shadow-lg hover:bg-[#ff8d4f]'
                          : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <span>❓</span>
                        <span>Questions générales</span>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        setSelectedCategory('account');
                        setSearchQuery('');
                        setShowAllResults(false);
                      }}
                      className={`w-full sm:w-auto px-4 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                        selectedCategory === 'account'
                          ? 'bg-[#FF7A35] text-white shadow-lg hover:bg-[#ff8d4f]'
                          : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <span>👤</span>
                        <span>Compte et profil</span>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        setSelectedCategory('pricing');
                        setSearchQuery('');
                        setShowAllResults(false);
                      }}
                      className={`w-full sm:w-auto px-4 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                        selectedCategory === 'pricing'
                          ? 'bg-[#FF7A35] text-white shadow-lg hover:bg-[#ff8d4f]'
                          : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <span>💰</span>
                        <span>Tarification</span>
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        setSelectedCategory('technical');
                        setSearchQuery('');
                        setShowAllResults(false);
                      }}
                      className={`w-full sm:w-auto px-4 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                        selectedCategory === 'technical'
                          ? 'bg-[#FF7A35] text-white shadow-lg hover:bg-[#ff8d4f]'
                          : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-100'
                      }`}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <span>🔧</span>
                        <span>Support technique</span>
                      </div>
                    </button>
                  </div>

                  {/* Nombre de résultats */}
                  <div className="text-center text-sm text-gray-500">
                    {filteredQuestions.length} résultat{filteredQuestions.length !== 1 ? 's' : ''} trouvé{filteredQuestions.length !== 1 ? 's' : ''}
                  </div>

                  {/* Bouton pour basculer l'affichage de tous les résultats - affiché uniquement s'il y a plus de résultats que la limite par page */}
                  {filteredQuestions.length > 0 && filteredQuestions.length > questionsPerPage && (
                    <button
                      onClick={() => setShowAllResults(!showAllResults)}
                      className="text-[#FF7A35] underline text-sm mt-2"
                    >
                      {showAllResults ? 'Afficher moins' : 'Afficher tout'}
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Conteneur des résultats avec hauteur fixe */}
            <div id="needhelp-accordion" className="max-w-6xl mx-auto px-4 mt-8">
              {filteredQuestions.length === 0 ? (
                <div className="text-center text-gray-500 py-12 bg-white rounded-xl shadow-sm">
                  <p className="text-lg">Aucun résultat trouvé pour votre recherche</p>
                  <p className="text-sm mt-2">Essayez avec d'autres mots-clés ou une autre catégorie</p>
                </div>
              ) : (
                <>
                  <div className={`grid md:grid-cols-2 gap-4 relative transition-opacity duration-200 ${
                    isAnimating ? 'opacity-0' : 'opacity-100'
                  }`}>
                    {visibleItems.map((item, index) => {
                      const isOpen = openItems.includes(index);
                      return (
                        <div
                          key={index}
                          className={`bg-white rounded-xl transition-all duration-300 ${
                            isOpen
                              ? 'shadow-lg ring-2 ring-[#FF7A35]/20'
                              : 'shadow-sm hover:shadow ring-1 ring-gray-100 hover:ring-[#FF7A35]/20'
                          }`}
                        >
                          <button
                            onClick={() => toggleItem(index)}
                            data-needhelp-index={index}
                            className={`w-full px-6 py-4 text-left flex items-center justify-between group transition-all duration-300 ${
                              isOpen ? 'bg-[#FF7A35]/5' : ''
                            }`}
                          >
                            <span className={`font-medium transition-all duration-300 ${
                              isOpen
                                ? 'text-[#FF7A35] translate-x-2'
                                : 'text-gray-700 group-hover:text-[#FF7A35] group-hover:translate-x-1'
                            }`}>
                              {item.question}
                            </span>
                            <div className={`flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300 ${
                              isOpen
                                ? 'bg-[#FF7A35]'
                                : 'bg-gray-100 group-hover:bg-[#FF7A35]/10'
                            }`}>
                              <svg
                                className={`w-5 h-5 transition-all duration-300 ${
                                  isOpen ? 'text-white rotate-180' : 'text-gray-400 group-hover:text-[#FF7A35]'
                                }`}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                              </svg>
                            </div>
                          </button>
                          <div
                            className={`transition-all duration-300 ease-out overflow-hidden ${
                              isOpen ? 'max-h-[500px]' : 'max-h-0'
                            }`}
                          >
                            <div className="px-8 pb-6 pt-2">
                              <p className="text-gray-600 leading-relaxed">
                                {item.answer}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  {!showAllResults && (
                    <div className="flex justify-center gap-4 mt-8">
                      <button
                        onClick={() => hasPreviousItems && changePage('prev')}
                        className={`px-6 py-3 rounded-xl transition-all duration-200 ${
                          hasPreviousItems
                            ? 'bg-white text-[#FF7A35] border-2 border-[#FF7A35] hover:bg-[#FF7A35] hover:text-white'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                        disabled={!hasPreviousItems}
                      >
                        Questions précédentes
                      </button>
                      <button
                        onClick={() => hasMoreItems && changePage('next')}
                        className={`px-6 py-3 rounded-xl transition-all duration-200 ${
                          hasMoreItems
                            ? 'bg-[#FF7A35] text-white hover:bg-[#ff8d4f]'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                        disabled={!hasMoreItems}
                      >
                        Questions suivantes
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>


      </div>
    </section>
  );
}

export default NeedHelp;
