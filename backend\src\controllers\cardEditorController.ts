import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { logUserActivity } from '../utils/activityLogger';
import { hasUserConsented } from './aiConsent';
import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import os from 'os';
import axios from 'axios';
import { uploadCardEditorImage, deleteCardEditorImages, DEFAULT_AVATAR_URL, getOrCreateStorageId } from '../services/storage';
import { z } from 'zod';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import templateSubscriptionService from '../services/templateSubscriptionService';
import { selectAIModel } from './openRouterController';
import { logOpenRouterUsage } from '../services/contentModerationService';
import { generateImage, generateMidjourneyPromptViaOpenRouter, ImageGenerationPurpose } from '../services/aiImageGenerationService';
import { generateColorPalette, generateSvgPath, generateDrawingPoints, getSectorConfig, generateSmartPosition, generateRandomTextLayout } from '../utils/designHelpers';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

// Constantes pour le cache Redis
const CACHE_PREFIX = 'card_editor:';
const USER_CREDITS_CACHE_PREFIX = 'ai_credits:';
const CACHE_TTL = 60 * 5; // 5 minutes

// Coût en crédits pour générer un template aléatoire
const TEMPLATE_GENERATION_COST = 5;

// URL et clé API OpenRouter
const AI_API_URL = process.env.MODERATION_API_URL || 'https://api.openrouter.ai/api/v1/chat/completions';
const AI_API_KEY = process.env.MODERATION_API_KEY || '';
const AI_API_MODEL_FREE = process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free';
const AI_API_MODEL_PAYANT = process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-preview';

// Schéma de validation pour la création/mise à jour de template
const templateSchema = z.object({
  name: z.string().min(1, "Le nom est requis").max(100, "Le nom est trop long"),
  type: z.enum(["business_card", "business_card_landscape", "flyer", "flyer_landscape"], {
    errorMap: () => ({ message: "Le type doit être 'business_card', 'business_card_landscape', 'flyer' ou 'flyer_landscape'" })
  }),
  template_data: z.object({
    width: z.number().min(1),
    height: z.number().min(1),
    background_color: z.string().optional(),
    background_image: z.string().optional(),
    elements: z.array(z.object({
      id: z.string(),
      type: z.enum(["text", "image", "shape", "qrcode", "drawing"]),
      x: z.number(),
      y: z.number(),
      width: z.number().optional(),
      height: z.number().optional(),
      rotation: z.number().optional(),
      name: z.string().optional(),
      properties: z.record(z.any()).optional()
    }))
  }),
  is_public: z.boolean().optional()
});

// Ajouter un stockage temporaire pour suivre l'état de génération des templates pour chaque utilisateur
interface GenerationStatus {
  status: string; // 'in_progress', 'completed', 'error'
  progress: number[];
  templates?: any[];
  timestamp: number;
}

// Map pour stocker l'état de génération par utilisateur
const userGenerationStatus = new Map<string, GenerationStatus>();

// Fonction pour mettre à jour le statut de génération
export const updateGenerationStatus = (userId: string, status: string, progress: number[], templates?: any[]) => {
  userGenerationStatus.set(userId, {
    status,
    progress,
    templates,
    timestamp: Date.now()
  });

  // Nettoyer les entrées anciennes (plus de 5 minutes)
  const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
  for (const [key, value] of userGenerationStatus.entries()) {
    if (value.timestamp < fiveMinutesAgo) {
      userGenerationStatus.delete(key);
    }
  }
  logger.info(`[updateGenerationStatus] Statut mis à jour avec succès pour l'utilisateur ${userId}`);
};

// Récupérer l'état de génération pour un utilisateur
export const getGenerationStatus = async (req: Request, res: Response) => {
  logger.info(`[getGenerationStatus] Reçu une requête pour le statut de génération pour l'utilisateur : ${req.user?.id}`);
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const status = userGenerationStatus.get(userId);

    if (!status) {
      return res.json({
        status: 'unknown',
        progress: []
      });
    }

    // Nettoyer l'état si terminé ou en erreur et qu'on a déjà renvoyé l'info
    if (status.status === 'completed' || status.status === 'error') {
      // On laisse les templates dans l'état pour un appel, puis on les nettoie
      const result = { ...status };

      // Réinitialiser après un succès pour éviter de renvoyer ces templates encore
      if (status.templates) {
        setTimeout(() => {
          const currentStatus = userGenerationStatus.get(userId);
          if (currentStatus && currentStatus.timestamp === status.timestamp) {
            userGenerationStatus.delete(userId);
          }
        }, 5000);
      }

      logger.info(`[getGenerationStatus] Statut récupéré pour l'utilisateur ${req.user?.id}:`, status);
      return res.json(result);
    }

    logger.info(`[getGenerationStatus] Statut récupéré pour l'utilisateur ${req.user?.id}:`, status);
    return res.json(status);
  } catch (error) {
    logger.error('Erreur lors de la récupération du statut de génération:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du statut de génération'
    });
  }
};

// Obtenir les templates de l'utilisateur
export const getTemplates = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;

    // Mettre à jour les templates de l'utilisateur en fonction de son abonnement
    if (userId) {
      await templateSubscriptionService.updateTemplatesBasedOnSubscription(userId);
    }

    // Vérifier si les données sont en cache
    const cacheKey = `${CACHE_PREFIX}templates:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    // Récupérer les templates de l'utilisateur
    const { data, error } = await supabase
      .from('card_templates')
      .select('*')
      .eq('user_id', userId)
      .order('order_index', { ascending: true });

    if (error) {
      logger.error('Erreur lors de la récupération des templates:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des templates'
      });
    }

    // Mettre en cache les résultats
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify({
      success: true,
      data
    }));

    return res.json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des templates:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des templates'
    });
  }
};

// Obtenir un template par son ID
export const getTemplateById = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;

    // Vérifier si les données sont en cache
    const cacheKey = `${CACHE_PREFIX}template:${templateId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      // Vérifier si le template est désactivé (pour le propriétaire uniquement)
      if (parsedData.data.user_id === userId && parsedData.data.name.startsWith('[DÉSACTIVÉ]')) {
        return res.status(403).json({
          success: false,
          message: 'Ce template est désactivé en raison des limitations de votre abonnement. Passez à l\'abonnement premium pour l\'activer.'
        });
      }
      return res.json(parsedData);
    }

    // Récupérer le template
    const { data, error } = await supabase
      .from('card_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération du template:', error);
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }

    // Vérifier que l'utilisateur est le propriétaire ou que le template est public
    if (data.user_id !== userId && !data.is_public) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas accès à ce template'
      });
    }

    // Vérifier si le template est désactivé (pour le propriétaire uniquement)
    if (data.user_id === userId && data.name.startsWith('[DÉSACTIVÉ]')) {
      return res.status(403).json({
        success: false,
        message: 'Ce template est désactivé en raison des limitations de votre abonnement. Passez à l\'abonnement premium pour l\'activer.'
      });
    }

    // Mettre en cache les résultats
    await redis.setex(cacheKey, CACHE_TTL, JSON.stringify({
      success: true,
      data
    }));

    return res.json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération du template:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du template'
    });
  }
};

// Créer un nouveau template
export const createTemplate = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;

    // Valider les données
    const validationResult = templateSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors
      });
    }

    const templateData = validationResult.data;

    // Vérifier les limites d'abonnement
    const subscriptionLimits = await getUserSubscriptionLimits(String(userId));

    // Compter le nombre de templates existants par type
    const isBusinessCard = templateData.type === 'business_card' || templateData.type === 'business_card_landscape';
    const isFlyer = templateData.type === 'flyer' || templateData.type === 'flyer_landscape';

    if (isBusinessCard) {
      // Compter les cartes de visite existantes
      const { count: businessCardCount, error: countError } = await supabase
        .from('card_templates')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .in('type', ['business_card', 'business_card_landscape'])
        .not('name', 'ilike', '%[DÉSACTIVÉ]%');

      if (countError) {
        logger.error('Erreur lors du comptage des cartes de visite:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }

      const currentCount = businessCardCount || 0;
      if (currentCount >= subscriptionLimits.businessCardsLimit) {
        return res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${subscriptionLimits.businessCardsLimit} carte(s) de visite pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          limitReached: true,
          currentCount,
          limit: subscriptionLimits.businessCardsLimit
        });
      }
    } else if (isFlyer) {
      // Compter les flyers existants
      const { count: flyerCount, error: countError } = await supabase
        .from('card_templates')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .in('type', ['flyer', 'flyer_landscape'])
        .not('name', 'ilike', '%[DÉSACTIVÉ]%');

      if (countError) {
        logger.error('Erreur lors du comptage des flyers:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }

      const currentCount = flyerCount || 0;
      if (currentCount >= subscriptionLimits.flyersLimit) {
        return res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${subscriptionLimits.flyersLimit} flyer(s) pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          limitReached: true,
          currentCount,
          limit: subscriptionLimits.flyersLimit
        });
      }
    }

    // Récupérer le plus grand order_index existant pour l'utilisateur
    const { data: maxOrderData } = await supabase
      .from('card_templates')
      .select('order_index')
      .eq('user_id', userId)
      .order('order_index', { ascending: false })
      .limit(1);
    const nextOrderIndex = maxOrderData && maxOrderData.length > 0 ? (maxOrderData[0].order_index || 0) + 1 : 0;

    // Insérer le template dans la base de données
    const { data, error } = await supabase
      .from('card_templates')
      .insert({
        user_id: userId,
        name: templateData.name,
        type: templateData.type,
        template_data: templateData.template_data,
        is_public: templateData.is_public || false,
        order_index: nextOrderIndex
      })
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la création du template:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du template'
      });
    }

    // Invalider le cache des templates de l'utilisateur
    await redis.del(`${CACHE_PREFIX}templates:${String(userId)}`);

    // Log de l'activité
    logUserActivity(String(userId), 'create_card_template', data.id, 'card_template', {
      template_name: data.name,
      template_type: data.type
    });

    // Après la création et le traitement des images, nettoyer les images non utilisées
    try {
      const userStorageId = await getOrCreateStorageId(String(userId)); // Assurez-vous que getOrCreateStorageId est importé ou accessible
      if (data && data.template_data && userStorageId) {
        const finalTemplateData = data.template_data;
        let usedImageUrls = [];
        if (finalTemplateData.background_image) {
          usedImageUrls.push(finalTemplateData.background_image);
        }
        if (Array.isArray(finalTemplateData.elements)) {
          finalTemplateData.elements.forEach((el: any) => {
            if (el.type === 'image' && el.properties && el.properties.src) {
              usedImageUrls.push(el.properties.src);
            }
          });
        }
        // Importer la fonction de suppression des images non utilisées
        const { deleteUnusedCardEditorImages } = require('../services/storage');
        await deleteUnusedCardEditorImages(userStorageId, data.id, usedImageUrls);
      }
    } catch (cleanupError) {
      logger.error('Erreur lors du nettoyage des images non utilisées après création:', cleanupError);
      // Ne pas bloquer la réponse pour une erreur de nettoyage
    }

    return res.status(201).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la création du template:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du template'
    });
  }
};

// Mettre à jour un template existant
export const updateTemplate = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;

    // Valider les données
    const validationResult = templateSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors
      });
    }

    const templateData = validationResult.data;

    // Vérifier que l'utilisateur est le propriétaire du template
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('card_templates')
      .select('user_id')
      .eq('id', templateId)
      .single();

    if (fetchError || !existingTemplate) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }

    if (existingTemplate.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas le droit de modifier ce template'
      });
    }

    // Mettre à jour le template
    const { data, error } = await supabase
      .from('card_templates')
      .update({
        name: templateData.name,
        type: templateData.type,
        template_data: templateData.template_data,
        is_public: templateData.is_public,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la mise à jour du template:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du template'
      });
    }

    // Invalider le cache des templates de l'utilisateur et du template spécifique
    await redis.del(`${CACHE_PREFIX}templates:${userId}`);
    await redis.del(`${CACHE_PREFIX}template:${templateId}`);

    // Log de l'activité
    logUserActivity(String(userId), 'update_card_template', data.id, 'card_template', {
      template_name: data.name,
      template_type: data.type
    });

    // Après la mise à jour, nettoyer les images non utilisées
    try {
      const userStorageId = await getOrCreateStorageId(String(userId));
      if (data && data.template_data && userStorageId) {
        const finalTemplateData = data.template_data;
        let usedImageUrls = [];
        if (finalTemplateData.background_image) {
          usedImageUrls.push(finalTemplateData.background_image);
        }
        if (Array.isArray(finalTemplateData.elements)) {
          finalTemplateData.elements.forEach((el: any) => {
            if (el.type === 'image' && el.properties && el.properties.src) {
              usedImageUrls.push(el.properties.src);
            }
          });
        }
        const { deleteUnusedCardEditorImages } = require('../services/storage');
        await deleteUnusedCardEditorImages(userStorageId, templateId, usedImageUrls);
      }
    } catch (cleanupError) {
      logger.error('Erreur lors du nettoyage des images non utilisées après mise à jour:', cleanupError);
      // Ne pas bloquer la réponse pour une erreur de nettoyage
    }

    return res.json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du template:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du template'
    });
  }
};

// Supprimer un template
export const deleteTemplate = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;

    // Vérifier que l'utilisateur est le propriétaire du template
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('card_templates')
      .select('user_id')
      .eq('id', templateId)
      .single();

    if (fetchError || !existingTemplate) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }

    if (existingTemplate.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas le droit de supprimer ce template'
      });
    }

    // Récupérer le storage_id de l'utilisateur
    const { data: userProfil, error: userProfilError } = await supabase
      .from('user_profil')
      .select('storage_id')
      .eq('user_id', userId)
      .single();
    if (!userProfil || !userProfil.storage_id) {
      logger.error('Impossible de récupérer le storage_id pour suppression des images du card editor', { userId, templateId });
    } else {
      // Supprimer les images du bucket carte_visite_et_flyer
      await deleteCardEditorImages(userProfil.storage_id, templateId);
    }

    // Supprimer le template
    const { error } = await supabase
      .from('card_templates')
      .delete()
      .eq('id', templateId);

    if (error) {
      logger.error('Erreur lors de la suppression du template:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du template'
      });
    }

    // Invalider les caches
    await redis.del(`${CACHE_PREFIX}templates:${String(userId)}`);
    await redis.del(`${CACHE_PREFIX}template:${String(templateId)}`);

    // Log de l'activité
    logUserActivity(String(userId), 'delete_card_template', templateId, 'card_template');

    return res.json({
      success: true,
      message: 'Template supprimé avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression du template:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du template'
    });
  }
};

// Générer un template aléatoire avec l'IA
export const generateRandomTemplate = async (req: Request, res: Response) => {
  try {
    let templateData: any = null;
    const userId = req.user?.userId;
    const { type, userData, _imageUrl, _backgroundImageUrl } = req.body;

    // Vérifier le type
    if (!type || !['business_card', 'business_card_landscape', 'flyer', 'flyer_landscape'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Type invalide. Doit être "business_card", "business_card_landscape", "flyer" ou "flyer_landscape"'
      });
    }

    // AMÉLIORATION: Randomisation intelligente de l'inclusion des images et des fonds
    const includeImage = _imageUrl ? true : Math.random() < 0.7; // 70% de chance d'inclure une image
    // OBJECTIF: 9.9 fois sur 10 (99%) de génération d'images de fond pour les cartes de visite
    const includeBackgroundImage = _backgroundImageUrl ? true : Math.random() < 0.99; // 99% de chance d'avoir une image de fond

    logger.info(`Génération de templates : Génération d'un type ${type} pour l'utilisateur ${userId}`);
    logger.info(`Options de génération: includeImage=${includeImage}, includeBackgroundImage=${includeBackgroundImage}`);

    // Vérifier les limites d'abonnement
    const subscriptionLimits = await getUserSubscriptionLimits(String(userId));

    // Compter le nombre de templates existants par type
    const isBusinessCard = type === 'business_card' || type === 'business_card_landscape';
    const isFlyer = type === 'flyer' || type === 'flyer_landscape';

    if (isBusinessCard) {
      // Compter les cartes de visite existantes
      const { count: businessCardCount, error: countError } = await supabase
        .from('card_templates')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .in('type', ['business_card', 'business_card_landscape'])
        .not('name', 'ilike', '%[DÉSACTIVÉ]%');

      if (countError) {
        logger.error('Erreur lors du comptage des cartes de visite:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }

      const currentCount = businessCardCount || 0;
      if (currentCount >= subscriptionLimits.businessCardsLimit) {
        return res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${subscriptionLimits.businessCardsLimit} carte(s) de visite pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          limitReached: true,
          currentCount,
          limit: subscriptionLimits.businessCardsLimit
        });
      }
    } else if (isFlyer) {
      // Compter les flyers existants
      const { count: flyerCount, error: countError } = await supabase
        .from('card_templates')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .in('type', ['flyer', 'flyer_landscape'])
        .not('name', 'ilike', '%[DÉSACTIVÉ]%');

      if (countError) {
        logger.error('Erreur lors du comptage des flyers:', countError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la vérification des limitations'
        });
      }

      const currentCount = flyerCount || 0;
      if (currentCount >= subscriptionLimits.flyersLimit) {
        return res.status(403).json({
          success: false,
          message: `Vous avez atteint la limite de ${subscriptionLimits.flyersLimit} flyer(s) pour votre abonnement. Passez à l'abonnement premium pour en créer davantage.`,
          limitReached: true,
          currentCount,
          limit: subscriptionLimits.flyersLimit
        });
      }
    }

    // Vérifier le consentement de l'utilisateur pour l'IA
    const userConsent = await hasUserConsented(String(userId));
    if (!userConsent) {
      return res.status(403).json({
        success: false,
        message: 'Vous devez accepter les conditions d\'utilisation de l\'IA pour utiliser cette fonctionnalité',
        requiresConsent: true
      });
    }

    // Vérifier les crédits IA de l'utilisateur
    const userCreditsKey = `${USER_CREDITS_CACHE_PREFIX}${userId}`;
    let userCredits = await redis.get(userCreditsKey);
    let credits = 0;

    // Si les crédits ne sont pas dans Redis, les récupérer depuis la base de données
    if (userCredits === null) {
      // Récupérer les crédits depuis la base de données
      const { data: creditsData, error: creditsError } = await supabase
        .from('user_ai_credits')
        .select('credits')
        .eq('user_id', userId)
        .single();

      if (creditsError && creditsError.code !== 'PGRST116') {
        logger.error('Erreur lors de la récupération des crédits IA:', creditsError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des crédits IA'
        });
      }

      credits = creditsData?.credits || 0;

      // Mettre en cache pour les prochaines requêtes
      await redis.setex(userCreditsKey, 3600, credits.toString());
      logger.info(`Crédits IA récupérés depuis la base de données et mis en cache: ${credits}`);
    } else {
      credits = parseInt(userCredits);
    }

    // Le coût est fixe, quelle que soit l'inclusion d'images ou de fond d'image
    const totalCost = TEMPLATE_GENERATION_COST;

    logger.info(`Vérification des crédits IA pour l'utilisateur ${userId}: ${credits} crédits disponibles, ${totalCost} crédits requis (images incluses dans le prix du template)`);

    if (credits < totalCost) {
      return res.status(402).json({
        success: false,
        message: 'Crédits IA insuffisants pour générer un template',
        creditsRequired: totalCost,
        currentCredits: credits
      });
    }

    // Récupérer les informations de l'utilisateur pour enrichir le prompt
    // On fait un join entre user_profil et users pour avoir toutes les infos pertinentes
    const { data: userProfile, error: userError } = await supabase
      .from('user_profil')
      .select(`*, users:users!inner(email)`) // join explicite
      .eq('user_id', userId)
      .single();

    if (userError || !userProfile) {
      logger.error('Erreur lors de la récupération du profil utilisateur:', userError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du profil utilisateur'
      });
    }

    // Déchiffrer les données du profil
    const decryptedUserProfile = await decryptProfilDataAsync(userProfile);

    // Déchiffrer les données utilisateur
    const decryptedUserData = userProfile.users ? await decryptUserDataAsync(userProfile.users) : null;

    // Récupérer les informations de l'utilisateur pour enrichir le prompt avec les services
    const { data: userServicesData, error: userServicesError } = await supabase
      .from('user_services')
      .select('*')
      .eq('user_id', userId);

    if (userServicesError) {
      logger.error('Erreur lors de la récupération des services utilisateur:', userServicesError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des services utilisateur'
      });
    }

    // --- CONSTRUCTION DES DONNÉES PERSONNELLES POUR LE PROMPT ---
    // On récupère les champs pertinents depuis userProfile et userProfile.users
    const infosPerso = {
      nom: decryptedUserProfile.nom || '',
      prenom: decryptedUserProfile.prenom || '',
      email: decryptedUserData?.email || '',
      telephone: decryptedUserProfile.telephone || '',
      type_de_profil: decryptedUserProfile.type_de_profil || '',
      nom_entreprise: decryptedUserProfile.nom_entreprise || '',
      titre: decryptedUserProfile.titre || '',
      bio: decryptedUserProfile.bio || '',
      slogan: decryptedUserProfile.slogan || '',
      site_web: decryptedUserProfile.site_web || '',
      adresse: decryptedUserProfile.adresse || '',
      ville: decryptedUserProfile.ville || '',
      code_postal: decryptedUserProfile.code_postal || '',
      pays: decryptedUserProfile.pays || '',
      services: userServicesData && Array.isArray(userServicesData) ? userServicesData.map(s => s.titre).filter(Boolean) : [],
      secteur: decryptedUserProfile.categorie_entreprise || '',
      slug: decryptedUserProfile.slug || '',
      photo_url: decryptedUserProfile.photo_url || '',
    };

    // Extraire les secteurs d'activité de l'utilisateur
    let userSectors = [];
    if (userProfile.services && Array.isArray(userProfile.services)) {
      userSectors = userProfile.services.map((s: any) => s.nom || '').filter(Boolean);
    }
    const primarySector = userSectors.length > 0 ? userSectors[0] : '';

    // Obtenir les configurations adaptées au secteur
    const sectorConfig = getSectorConfig(primarySector);
    logger.info(`Configuration adaptée au secteur "${primarySector}":`, sectorConfig);

    // Générer une palette de couleurs harmonieuse
    const colorPalette = generateColorPalette(sectorConfig.colorScheme);
    logger.info(`Palette de couleurs générée:`, colorPalette);

    // Nombres randomisés d'éléments pour le design
    const numText = 3 + Math.floor(Math.random() * 3); // 3-5 éléments texte
    const numShapes = sectorConfig.shapeCount || (4 + Math.floor(Math.random() * 5)); // 4-8 formes
    const includeDrawing = sectorConfig.includeDrawing !== undefined ? sectorConfig.includeDrawing : Math.random() < 0.4;

    const includeProfilePhoto = Math.random() < 0.85; // 85% de chance d'inclure la photo de profil

    const profileUrl = infosPerso.slug
      ? `https://jobpartiel.fr/profil/${infosPerso.slug}`
      : 'https://jobpartiel.fr';

    // Générer ou utiliser des images si nécessaire
    let imageUrl = _imageUrl || '';
    let backgroundImageUrl = _backgroundImageUrl || '';

    try {
      // Générer une image pour le template si nécessaire et si elle n'est pas déjà fournie
      if (includeImage && !imageUrl) {
        logger.info(`Génération d'une image pour le template de type ${type}`);

          // Générer un prompt optimisé pour Midjourney selon le métier de l'utilisateur

          let imagePrompt = "";

        // Utiliser directement la fonction existante generateMidjourneyPromptViaOpenRouter
        try {
          const purpose: ImageGenerationPurpose = 'card_editor';

          // Préparation d'un prompt de base enrichi avec les informations de l'utilisateur
          let basePromptInfo = "";

          // Récupérer les services spécifiques de l'utilisateur pour enrichir le prompt
          if (userServicesData && Array.isArray(userServicesData) && userServicesData.length > 0) {
            const serviceNames = userServicesData.map(service => service.titre || '').filter(Boolean);
            const serviceDescriptions = userServicesData.map(service => service.description || '').filter(Boolean);
            const serviceCategories = userServicesData.map(service => service.categorie || '').filter(Boolean);

            if (serviceNames.length > 0) {
              basePromptInfo += ` Services: ${serviceNames.join(', ')}. `;
            }

            if (serviceDescriptions.length > 0) {
              // Ajouter une description condensée
              const combinedDesc = serviceDescriptions.join(' ').substring(0, 100);
              basePromptInfo += ` Description: ${combinedDesc}. `;
            }

            if (serviceCategories.length > 0 && serviceCategories[0]) {
              basePromptInfo += ` Catégorie: ${serviceCategories[0]}. `;
            }
          }

          // Ajouter des informations du profil utilisateur
          if (decryptedUserProfile) {
            if (decryptedUserProfile.nom_entreprise) {
              basePromptInfo += ` Entreprise: ${decryptedUserProfile.nom_entreprise}. `;
            }
            if (decryptedUserProfile.type_de_profil) {
              basePromptInfo += ` Type de profil: ${decryptedUserProfile.type_de_profil}. `;
            }
            if (decryptedUserProfile.bio) {
              basePromptInfo += ` Bio: ${decryptedUserProfile.bio.substring(0, 50)}... `;
            }
            if (decryptedUserProfile.slogan) {
              basePromptInfo += ` Slogan: ${decryptedUserProfile.slogan}. `;
            }
          }

          // Créer le prompt de base pour l'image
          const promptBase = `Image professionnelle pour ${type.includes('flyer') ? 'flyer' : 'carte de visite'} sans texte. ${basePromptInfo} Logo moderne et élégant qui représente parfaitement ce domaine d'activité.`;

          // Génération du prompt optimisé pour Midjourney via OpenRouter
          imagePrompt = await generateMidjourneyPromptViaOpenRouter(
            String(userId),
            purpose,
            undefined,
            promptBase
          );

          logger.info(`Prompt Midjourney généré via OpenRouter pour l'image: ${imagePrompt}`);
        } catch (err) {
          // Fallback: Prompt générique si erreur
          logger.warn('Erreur lors de la génération du prompt Midjourney via OpenRouter:', err);

          // Récupérer les informations des services pour enrichir le prompt de secours
          let serviceMention = '';
          if (userServicesData && Array.isArray(userServicesData) && userServicesData.length > 0) {
            const serviceNames = userServicesData.map(service => service.titre || '').filter(Boolean);
            if (serviceNames.length > 0) {
              serviceMention = ` services de ${serviceNames.join(', ')}`;
            }
          }

          // Utiliser le nom d'entreprise ou le nom/prénom si disponible
          let identificationMention = '';
          if (decryptedUserProfile.nom_entreprise) {
            identificationMention = decryptedUserProfile.nom_entreprise;
          } else if (decryptedUserProfile.nom && decryptedUserProfile.prenom) {
            identificationMention = `${decryptedUserProfile.prenom} ${decryptedUserProfile.nom}`;
          } else {
            identificationMention = 'un professionnel';
          }

          imagePrompt = `Image professionnelle pour ${type.includes('flyer') ? 'flyer' : 'carte de visite'} représentant ${identificationMention}${serviceMention}. Logo moderne ou symbole élégant et polyvalent. Style minimaliste, couleurs harmonieuses et professionnelles. SANS TEXTE.`;
          imagePrompt += ` IMPORTANT: Image adaptée pour impression professionnelle, SANS TEXTE, SANS LETTRAGE. Uniquement visuel graphique, symbole, logo ou photographie de haute qualité qui représente bien le métier.`;
          logger.info(`Prompt générique utilisé pour l'image suite à une erreur: ${imagePrompt}`);
        }

        const { imageUrl: generatedImageUrl } = await generateImage({
          prompt: imagePrompt,
          userId: String(userId),
          purpose: 'card_editor',
          width: 512,
          height: 512
        });

        imageUrl = generatedImageUrl;
        logger.info(`Image générée avec succès pour le template: ${imageUrl}`);
      } else if (imageUrl) {
        logger.info(`Utilisation de l'image fournie pour le template: ${imageUrl}`);
      }

      // Générer une image de fond pour le template si nécessaire et si elle n'est pas déjà fournie
      if (includeBackgroundImage && !backgroundImageUrl) {
        logger.info(`Génération d'une image de fond pour le template de type ${type}`);

        // Utiliser notre fonction avancée pour générer un prompt spécifique à l'arrière-plan
        let backgroundPrompt = "";

        // Utiliser les données de services déjà récupérées ou les récupérer si nécessaire
        let userServicesData = null;
        if (!userServicesData) {
          const { data: servicesData, error: servicesError } = await supabase
            .from('user_services')
            .select('*')
            .eq('user_id', userId);

          if (!servicesError) {
            userServicesData = servicesData;
          }
        }

        // Utiliser directement la fonction existante generateMidjourneyPromptViaOpenRouter
        try {
          const purpose: ImageGenerationPurpose = 'card_editor';

          // Préparation d'un prompt de base enrichi avec les informations de l'utilisateur
          let backgroundInfo = "";

          // Récupérer les services spécifiques pour personnaliser l'arrière-plan
          let serviceType = "";
          if (userServicesData && Array.isArray(userServicesData) && userServicesData.length > 0) {
            const serviceNames = userServicesData.map(service => service.titre || '').filter(Boolean);
            const serviceCategories = userServicesData.map(service => service.categorie || '').filter(Boolean);

            if (serviceNames.length > 0) {
              serviceType = serviceNames[0]; // Utiliser le premier service comme référence principale
              backgroundInfo += ` Service principal: ${serviceType}. `;
            }

            if (serviceCategories.length > 0) {
              const uniqueCategories = [...new Set(serviceCategories)];
              backgroundInfo += ` Catégorie(s): ${uniqueCategories.join(', ')}. `;
            }
          }

          // Ajouter des informations du profil utilisateur pour l'ambiance
          if (userProfile) {
            if (userProfile.type_de_profil) {
              backgroundInfo += ` Type de profil: ${userProfile.type_de_profil}. `;
            }

            // Sélectionner un style visuel approprié à la catégorie du service
            if (userServicesData && Array.isArray(userServicesData) && userServicesData.length > 0) {
              const categorie = (userServicesData[0].categorie || '').toLowerCase();

              if (categorie.includes('jardin') || categorie.includes('paysag')) {
                backgroundInfo += " Style: naturel, organique, tons verts et terreux. ";
              } else if (categorie.includes('beauté') || categorie.includes('coiff')) {
                backgroundInfo += " Style: élégant, raffiné, tons pastel ou dorés. ";
              } else if (categorie.includes('btp') || categorie.includes('construct')) {
                backgroundInfo += " Style: robuste, technique, tons bleus ou gris. ";
              } else if (categorie.includes('santé') || categorie.includes('médic')) {
                backgroundInfo += " Style: apaisant, professionnel, tons bleus ou verts clairs. ";
              } else if (userProfile.slogan) {
                // Utiliser le slogan comme élément de style si disponible
                backgroundInfo += ` Ambiance: ${userProfile.slogan}. `;
              }
            }
          }

          // Créer le prompt de base pour l'arrière-plan selon le type de document
          const basePrompt = type === 'business_card' || type === 'business_card_landscape'
            ? `Arrière-plan professionnel pour carte de visite format ${type.includes('landscape') ? 'paysage' : 'portrait'} pour ${serviceType || "services professionnels"}. ${backgroundInfo} Sans texte.`
            : `Arrière-plan professionnel pour flyer format ${type.includes('landscape') ? 'paysage' : 'portrait'} pour ${serviceType || "services professionnels"}. ${backgroundInfo} Sans texte.`;

          // Génération du prompt optimisé pour Midjourney via OpenRouter
          backgroundPrompt = await generateMidjourneyPromptViaOpenRouter(
            String(userId),
            purpose,
            undefined,
            basePrompt
          );

          logger.info(`Prompt Midjourney généré via OpenRouter pour l'arrière-plan: ${backgroundPrompt}`);
        } catch (err) {
          // Fallback: Prompt générique si erreur
          logger.warn('Erreur lors de la génération du prompt Midjourney via OpenRouter pour l\'arrière-plan:', err);

          // Récupérer les services pour le prompt de secours
          let serviceInfo = '';
          let styleInfo = '';

          if (userServicesData && Array.isArray(userServicesData) && userServicesData.length > 0) {
            const serviceNames = userServicesData.map(service => service.titre || '').filter(Boolean);
            const serviceCategories = userServicesData.map(service => service.categorie || '').filter(Boolean);

            if (serviceNames.length > 0) {
              serviceInfo = serviceNames[0]; // Utiliser le premier service
            }

            // Déterminer un style visuel approprié en fonction de la catégorie de service
            if (serviceCategories.length > 0 && serviceCategories[0]) {
              const categorie = serviceCategories[0].toLowerCase();
              if (categorie.includes('jardin') || categorie.includes('paysag')) {
                styleInfo = "motifs naturels, tons verts et terreux, textures organiques";
              } else if (categorie.includes('beauté') || categorie.includes('coiff')) {
                styleInfo = "motifs élégants, tons pastel ou dorés, textures douces";
              } else if (categorie.includes('btp') || categorie.includes('construct')) {
                styleInfo = "motifs géométriques, tons bleus ou gris, textures techniques";
              } else if (categorie.includes('animal')) {
                styleInfo = "tons chaleureux, textures douces, motifs subtils";
              } else if (categorie.includes('santé') || categorie.includes('médic')) {
                styleInfo = "tons bleus apaisants, textures douces, design minimaliste";
              }
            }
          }

          // Si pas de style spécifique et qu'on a un nom d'entreprise, créer un style élégant par défaut
          if (!styleInfo && infosPerso && infosPerso.nom_entreprise) {
            styleInfo = "design professionnel, élégant, avec texture subtile et dégradé doux";
          }

          // Créer un prompt de secours adapté au type de document et au service
          if (type === 'business_card' || type === 'business_card_landscape') {
            backgroundPrompt = `Arrière-plan haute qualité pour carte de visite professionnelle ${serviceInfo ? 'de ' + serviceInfo : ''}. Texture élégante avec ${styleInfo || "motif abstrait très subtil, dégradé doux et sophistiqué"}. Style minimaliste moderne avec profondeur visuelle. JAMAIS DE TEXTE ni d'éléments proéminents qui distraient.`;
          } else {
            backgroundPrompt = `Arrière-plan haute qualité pour flyer promotionnel ${serviceInfo ? 'de ' + serviceInfo : ''}. Texture sophistiquée avec ${styleInfo || "dégradé dynamique, effets de profondeur et motifs abstraits très légers"}. Design contemporain avec zones claires pour le texte. JAMAIS DE TEXTE ni d'éléments proéminents.`;
          }
          backgroundPrompt += ` IMPORTANT: Uniquement un fond visuel abstrait ou texturé, AUCUN TEXTE, AUCUN LOGO, AUCUN PERSONNAGE. Seulement un arrière-plan pur, élégant et professionnel avec texture/motif/dégradé.`;

          logger.info(`Prompt générique utilisé pour l'arrière-plan suite à une erreur: ${backgroundPrompt}`);
        }

        // Ajuster les dimensions en fonction du type tout en respectant les contraintes
        let width = 512;  // Valeur par défaut sûre
        let height = 512; // Valeur par défaut sûre

        if (type.includes('landscape')) {
          width = 768;  // 768 est un multiple de 64 (768 = 64 * 12)
          height = 512; // 512 est un multiple de 64 (512 = 64 * 8)
        } else {
          width = 512;  // 512 est un multiple de 64 (512 = 64 * 8)
          height = 768; // 768 est un multiple de 64 (768 = 64 * 12)
        }

        const { imageUrl: generatedBackgroundUrl } = await generateImage({
          prompt: backgroundPrompt, // backgroundPrompt est déjà optimisé par generateMidjourneyPromptViaOpenRouter
          userId: String(userId),
          purpose: 'card_editor',
          width: width,
          height: height
        });

        backgroundImageUrl = generatedBackgroundUrl;
        logger.info(`Image de fond générée avec succès pour le template: ${backgroundImageUrl}`);
      } else if (backgroundImageUrl) {
        logger.info(`Utilisation de l'image de fond fournie pour le template: ${backgroundImageUrl}`);
      }
    } catch (imageError) {
      logger.error('Erreur lors de la génération des images:', imageError);
      // On continue même si la génération d'image échoue
    }

    // Construire le prompt pour l'IA
    let prompt = '';
    if (type === 'business_card') {
      // AMÉLIORATION: Générer des layouts aléatoires pour plus de diversité
      const textLayouts = generateRandomTextLayout(200, 350, numText);
      const alignments = ['left', 'center', 'right'];
      const randomAlignment1 = alignments[Math.floor(Math.random() * alignments.length)];
      const randomAlignment2 = alignments[Math.floor(Math.random() * alignments.length)];
      const randomAlignment3 = alignments[Math.floor(Math.random() * alignments.length)];

      prompt = `Génère UNIQUEMENT un objet JSON valide pour une carte de visite professionnelle format portrait (largeur < hauteur).

Données personnelles :
${userData ? JSON.stringify(userData) : `
- Nom: ${infosPerso.prenom} ${infosPerso.nom}
- Titre: ${infosPerso.titre || 'Professionnel indépendant'}
- Email: ${infosPerso.email || '<EMAIL>'}
- Téléphone: ${infosPerso.telephone || '+33 6 00 00 00 00'}
- Site web: ${infosPerso.site_web || 'www.jobpartiel.fr'}
- Services: ${infosPerso.services.length > 0 ? infosPerso.services.join(', ') : 'Services professionnels'}
- Secteur d'activité: ${infosPerso.secteur || 'Services professionnels'}
- Description: ${infosPerso.bio || 'Professionnel expérimenté proposant des services de qualité'}
`}

DIMENSIONS OBLIGATOIRES : width: 200, height: 350 (format portrait)

Structure JSON exacte à respecter :
{
  "width": 200,
  "height": 350,
  "background_color": "${colorPalette.backgroundLight}",
  ${backgroundImageUrl ? `"background_image": "${backgroundImageUrl}",` : ''}
  "elements": [
    // TEXTES (premier plan) - ORGANISATION PROFESSIONNELLE AVEC POSITIONNEMENT ALÉATOIRE
    {
      "id": "text_name",
      "type": "text",
      "x": ${Math.max(10, Math.min(textLayouts[0]?.x || 20, 140))},
      "y": ${Math.max(20, Math.min(textLayouts[0]?.y || 30, 100))},
      "width": ${textLayouts[0]?.width || 160},
      "height": 30,
      "rotation": 0,
      "properties": {
        "text": "NOM PRÉNOM",
        "fontSize": 18,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.mainColor}",
        "align": "${randomAlignment1}",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "text_title",
      "type": "text",
      "x": ${Math.max(10, Math.min(textLayouts[1]?.x || 20, 140))},
      "y": ${Math.max(60, Math.min(textLayouts[1]?.y || 70, 150))},
      "width": ${textLayouts[1]?.width || 160},
      "height": 25,
      "rotation": 0,
      "properties": {
        "text": "TITRE PROFESSIONNEL",
        "fontSize": 12,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.secondaryColor}",
        "align": "${randomAlignment2}",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "text_contact",
      "type": "text",
      "x": ${Math.max(10, Math.min(textLayouts[2]?.x || 20, 140))},
      "y": ${Math.max(200, Math.min(textLayouts[2]?.y || 250, 270))},
      "width": ${textLayouts[2]?.width || 160},
      "height": 80,
      "rotation": 0,
      "properties": {
        "text": "TÉLÉPHONE\\nEMAIL\\nSITE WEB",
        "fontSize": 10,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.tertiaryColor}",
        "align": "${randomAlignment3}",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // IMAGES ET QR CODE
    ${imageUrl ? `{
      "id": "logo_image",
      "type": "image",
      "x": 75,
      "y": 120,
      "width": 50,
      "height": 50,
      "rotation": 0,
      "properties": {
        "src": "${imageUrl}",
        "cornerRadius": 25,
        "opacity": 1
      }
    },` : ''}
    {
      "id": "qrcode",
      "type": "qrcode",
      "x": 140,
      "y": 280,
      "width": 40,
      "height": 40,
      "rotation": 0,
      "properties": {
        "data": "${profileUrl}",
        "fill": "#000000",
        "background": "#ffffff",
        "opacity": 0.8
      }
    },
    // ÉLÉMENTS DÉCORATIFS PROFESSIONNELS (arrière-plan)
    {
      "id": "accent_line_top",
      "type": "shape",
      "x": 30,
      "y": 110,
      "width": 140,
      "height": 2,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.accentColor1}",
        "opacity": 0.6
      }
    },
    {
      "id": "accent_circle",
      "type": "shape",
      "x": 10,
      "y": 180,
      "width": 30,
      "height": 30,
      "rotation": 0,
      "properties": {
        "shape": "circle",
        "fill": "${colorPalette.accentColor2}",
        "opacity": 0.3
      }
    },
    {
      "id": "corner_accent",
      "type": "shape",
      "x": 160,
      "y": 10,
      "width": 30,
      "height": 30,
      "rotation": 45,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.4
      }
    }
  ]
}

DESIGN PROFESSIONNEL (CRUCIAL) :
1. ESTHÉTIQUE PREMIUM : Design moderne et premium avec une hiérarchie visuelle claire
2. STRUCTURE CLAIRE : Zone supérieure pour l'identité, zone centrale pour les informations, zone inférieure pour les contacts
3. CONTRASTE : Maintenir un contraste élevé entre texte et arrière-plan
4. TYPOGRAPHIE : Tailles de police proportionnelles et cohérentes
5. COULEURS : Utiliser la palette fournie de façon harmonieuse
6. ESPACE NÉGATIF : Prévoir suffisamment d'espace vide autour des éléments
7. ORDRE DES ÉLÉMENTS : Textes et QR code au début du tableau (premier plan), formes décoratives à la fin (arrière-plan)

RETOURNE UNIQUEMENT L'OBJET JSON COMPLET, PAS DE COMMENTAIRES NI MARKDOWN.`;
    } else if (type === 'business_card_landscape') {
      // Générer des variations aléatoires pour plus de diversité
      prompt = `Génère UNIQUEMENT un objet JSON valide pour une carte de visite professionnelle format paysage (largeur > hauteur).

Données personnelles :
${userData ? JSON.stringify(userData) : `
- Nom: ${infosPerso.prenom} ${infosPerso.nom}
- Titre: ${infosPerso.titre || 'Professionnel indépendant'}
- Email: ${infosPerso.email || '<EMAIL>'}
- Téléphone: ${infosPerso.telephone || '+33 6 00 00 00 00'}
- Site web: ${infosPerso.site_web || 'www.jobpartiel.fr'}
- Services: ${infosPerso.services.length > 0 ? infosPerso.services.join(', ') : 'Services professionnels'}
- Secteur d'activité: ${infosPerso.secteur || 'Services professionnels'}
- Description: ${infosPerso.bio || 'Professionnel expérimenté proposant des services de qualité'}
`}

DIMENSIONS OBLIGATOIRES : width: 350, height: 200 (format paysage)

Structure JSON exacte à respecter :
{
  "width": 350,
  "height": 200,
  "background_color": "${colorPalette.backgroundLight}",
  ${backgroundImageUrl ? `"background_image": "${backgroundImageUrl}",` : ''}
  "elements": [
    // TEXTES (premier plan) - ORGANISATION PROFESSIONNELLE PAYSAGE
    {
      "id": "text_name",
      "type": "text",
      "x": 30,
      "y": 30,
      "width": 150,
      "height": 30,
      "rotation": 0,
      "properties": {
        "text": "NOM PRÉNOM",
        "fontSize": 18,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.mainColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "text_title",
      "type": "text",
      "x": 30,
      "y": 70,
      "width": 150,
      "height": 25,
      "rotation": 0,
      "properties": {
        "text": "TITRE PROFESSIONNEL",
        "fontSize": 12,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.secondaryColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "text_contact",
      "type": "text",
      "x": 200,
      "y": 50,
      "width": 130,
      "height": 80,
      "rotation": 0,
      "properties": {
        "text": "TÉLÉPHONE\\nEMAIL\\nSITE WEB",
        "fontSize": 10,
        "fontFamily": "Montserrat",
        "fill": "${colorPalette.tertiaryColor}",
        "align": "right",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // IMAGES ET QR CODE
    ${imageUrl ? `{
      "id": "logo_image",
      "type": "image",
      "x": 30,
      "y": 110,
      "width": 50,
      "height": 50,
      "rotation": 0,
      "properties": {
        "src": "${imageUrl}",
        "cornerRadius": 25,
        "opacity": 1
      }
    },` : ''}
    {
      "id": "qrcode",
      "type": "qrcode",
      "x": 280,
      "y": 140,
      "width": 40,
      "height": 40,
      "rotation": 0,
      "properties": {
        "data": "${profileUrl}",
        "fill": "#000000",
        "background": "#ffffff",
        "opacity": 0.8
      }
    },
    // ÉLÉMENTS DÉCORATIFS PROFESSIONNELS PAYSAGE (arrière-plan)
    {
      "id": "divider_vertical",
      "type": "shape",
      "x": 175,
      "y": 30,
      "width": 2,
      "height": 140,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.accentColor1}",
        "opacity": 0.5
      }
    },
    {
      "id": "accent_line_bottom",
      "type": "shape",
      "x": 30,
      "y": 100,
      "width": 120,
      "height": 2,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.6
      }
    },
    {
      "id": "corner_decoration",
      "type": "shape",
      "x": 300,
      "y": 20,
      "width": 30,
      "height": 30,
      "rotation": 0,
      "properties": {
        "shape": "circle",
        "fill": "${colorPalette.accentColor2}",
        "opacity": 0.4
      }
    }
  ]
}

INSTRUCTIONS SPÉCIFIQUES POUR PROFESSIONNEL INDÉPENDANT:
1. DESIGN ÉPURÉ ET PROFESSIONNEL: Créer un look sophistiqué avec éléments minimalistes
2. STYLE ADAPTÉ AU SECTEUR: ${infosPerso.services.length > 0 ? 'Adapter le design pour mettre en valeur ses compétences de ' + infosPerso.services.join(', ') : 'Mettre en valeur son statut de professionnel indépendant avec un design élégant et confiant'}
3. EXPÉRIENCE PROFESSIONNELLE: Suggérer l'expertise et la qualité à travers le design
4. MISE EN PAGE ÉQUILIBRÉE: Créer une hiérarchie claire entre le nom, le titre et les coordonnées
5. ZONE DE CONTACT BIEN VISIBLE: S'assurer que les coordonnées sont faciles à lire et à mémoriser

DESIGN PROFESSIONNEL (CRUCIAL) :
1. ESTHÉTIQUE PREMIUM : Design moderne et premium avec une hiérarchie visuelle claire
2. LAYOUT HORIZONTAL : Structure claire avec informations d'identité à gauche et contacts à droite
3. CONTRASTE : Maintenir un contraste élevé entre texte et arrière-plan
4. TYPOGRAPHIE : Tailles de police proportionnelles et cohérentes
5. COULEURS : Utiliser la palette fournie de façon harmonieuse
6. ESPACE NÉGATIF : Prévoir suffisamment d'espace vide autour des éléments
7. ORDRE DES ÉLÉMENTS : Textes et QR code au début du tableau (premier plan), formes décoratives à la fin (arrière-plan)

RETOURNE UNIQUEMENT L'OBJET JSON COMPLET, PAS DE COMMENTAIRES NI MARKDOWN.`;
    } else if (type === 'flyer') {
      // AMÉLIORATION: Prompt avancé pour flyer portrait
      // Pour les flyers, on a besoin de plus d'éléments
      const flyerNumText = 5 + Math.floor(Math.random() * 4); // 5-8 textes
      const flyerNumShapes = 8 + Math.floor(Math.random() * 5); // 8-12 formes

      prompt = `Génère UNIQUEMENT un objet JSON valide pour un flyer promotionnel format portrait (hauteur > largeur).

Données personnelles :
${userData ? JSON.stringify(userData) : `
- Nom: ${infosPerso.prenom} ${infosPerso.nom}
- Titre: ${infosPerso.titre || 'Professionnel indépendant'}
- Email: ${infosPerso.email || '<EMAIL>'}
- Téléphone: ${infosPerso.telephone || '+33 6 00 00 00 00'}
- Site web: ${infosPerso.site_web || 'www.jobpartiel.fr'}
- Services: ${infosPerso.services.length > 0 ? infosPerso.services.join(', ') : 'Services professionnels'}
- Secteur d'activité: ${infosPerso.secteur || 'Services professionnels'}
- Description: ${infosPerso.bio || 'Professionnel expérimenté proposant des services de qualité'}
`}

DIMENSIONS OBLIGATOIRES : width: 595, height: 842 (format A4 portrait)

Structure JSON exacte à respecter :
{
  "width": 595,
  "height": 842,
  "background_color": "${colorPalette.backgroundLight}",
  ${backgroundImageUrl ? `"background_image": "${backgroundImageUrl}",` : ''}
  "elements": [
    // TITRE ET SOUS-TITRE (premier plan)
    {
      "id": "headline",
      "type": "text",
      "x": 50,
      "y": 50,
      "width": 495,
      "height": 80,
      "rotation": 0,
      "properties": {
        "text": "TITRE PRINCIPAL ACCROCHEUR",
        "fontSize": number_between_36_and_48,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "center",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "subheadline",
      "type": "text",
      "x": 100,
      "y": 140,
      "width": 395,
      "height": 50,
      "rotation": 0,
      "properties": {
        "text": "SOUS-TITRE EXPLICATIF",
        "fontSize": number_between_20_and_28,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.secondaryColor}",
        "align": "center",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // SECTION PRINCIPALE - SERVICES
    {
      "id": "services_title",
      "type": "text",
      "x": 50,
      "y": 230,
      "width": 495,
      "height": 40,
      "rotation": 0,
      "properties": {
        "text": "NOS SERVICES",
        "fontSize": number_between_24_and_30,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "center",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "services_list",
      "type": "text",
      "x": 100,
      "y": 280,
      "width": 395,
      "height": 200,
      "rotation": 0,
      "properties": {
        "text": "• Premier service offert\\n• Deuxième service proposé\\n• Troisième prestation\\n• Quatrième option disponible",
        "fontSize": number_between_16_and_22,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.tertiaryColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // INFORMATION DE CONTACT
    {
      "id": "contact_info",
      "type": "text",
      "x": 50,
      "y": 700,
      "width": 495,
      "height": 80,
      "rotation": 0,
      "properties": {
        "text": "CONTACTEZ-NOUS\\nTél: TÉLÉPHONE | Email: EMAIL\\nSite web: SITE_WEB",
        "fontSize": number_between_16_and_20,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "center",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // IMAGES ET QR CODE
    ${imageUrl ? `{
      "id": "main_image",
      "type": "image",
      "x": 150,
      "y": 500,
      "width": 300,
      "height": 180,
      "rotation": 0,
      "properties": {
        "src": "${imageUrl}",
        "cornerRadius": number_between_0_and_15,
        "opacity": 1
      }
    },` : ''}
    {
      "id": "qrcode",
      "type": "qrcode",
      "x": 270,
      "y": 760,
      "width": 55,
      "height": 55,
      "rotation": 0,
      "properties": {
        "data": "${profileUrl}",
        "fill": "#000000",
        "background": "#ffffff",
        "opacity": 0.9
      }
    },
    // FORMES DÉCORATIVES (arrière-plan)
    {
      "id": "shape_header",
      "type": "shape",
      "x": 0,
      "y": 0,
      "width": 595,
      "height": 200,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.1
      }
    },
    {
      "id": "shape_accent_1",
      "type": "shape",
      "x": -100,
      "y": -100,
      "width": 400,
      "height": 400,
      "rotation": 45,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(4, sectorConfig.svgType)}",
        "fill": "${colorPalette.accentColor1}",
        "opacity": 0.15
      }
    },
    {
      "id": "shape_accent_2",
      "type": "shape",
      "x": 400,
      "y": 500,
      "width": 300,
      "height": 300,
      "rotation": -15,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(3, sectorConfig.svgType)}",
        "fill": "${colorPalette.accentColor2}",
        "opacity": 0.2
      }
    },
    ${includeDrawing ? `{
      "id": "drawing_flourish",
      "type": "drawing",
      "x": 150,
      "y": 400,
      "width": 300,
      "height": 250,
      "rotation": number_between_0_and_45,
      "properties": {
        "points": ${JSON.stringify(generateDrawingPoints(300, 250, 20, "fluid"))},
        "tension": number_between_0.2_and_0.5,
        "strokeWidth": number_between_1_and_3,
        "stroke": "${colorPalette.mainColor}",
        "opacity": number_between_0.2_and_0.5
      }
    },` : ''}
    {
      "id": "shape_brand",
      "type": "shape",
      "x": 50,
      "y": 350,
      "width": 80,
      "height": 80,
      "rotation": 0,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(5, 'custom')}",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.8
      }
    }
  ]
}

DESIGN PROFESSIONNEL (CRUCIAL) :
1. HIÉRARCHIE VISUELLE : Structure claire avec titre, services, et coordonnées
2. COMPOSITION ÉQUILIBRÉE : Bonne répartition des éléments avec points focaux clairs
3. CONTRASTE : Maintenir un contraste élevé entre texte et arrière-plan
4. TYPOGRAPHIE : Utiliser des tailles de police hiérarchisées (grand pour titres, plus petit pour détails)
5. COULEURS : Cohérence de la palette de couleurs avec accents visuels
6. FORMES : Utilisation stratégique des formes décoratives pour guider le regard
7. ORDRE DES ÉLÉMENTS : Textes et QR code au début du tableau (premier plan), formes décoratives à la fin (arrière-plan)

RETOURNE UNIQUEMENT L'OBJET JSON COMPLET, PAS DE COMMENTAIRES NI MARKDOWN.`;
    } else { // flyer_landscape
      // AMÉLIORATION: Prompt avancé pour flyer paysage
      // Pour les flyers, on a besoin de plus d'éléments
      const flyerNumText = 5 + Math.floor(Math.random() * 4); // 5-8 textes
      const flyerNumShapes = 8 + Math.floor(Math.random() * 5); // 8-12 formes

      prompt = `Génère UNIQUEMENT un objet JSON valide pour un flyer promotionnel format paysage (largeur > hauteur).

Données personnelles :
${userData ? JSON.stringify(userData) : `
- Nom: ${infosPerso.prenom} ${infosPerso.nom}
- Titre: ${infosPerso.titre || 'Professionnel indépendant'}
- Email: ${infosPerso.email || '<EMAIL>'}
- Téléphone: ${infosPerso.telephone || '+33 6 00 00 00 00'}
- Site web: ${infosPerso.site_web || 'www.jobpartiel.fr'}
- Services: ${infosPerso.services.length > 0 ? infosPerso.services.join(', ') : 'Services professionnels'}
- Secteur d'activité: ${infosPerso.secteur || 'Services professionnels'}
- Description: ${infosPerso.bio || 'Professionnel expérimenté proposant des services de qualité'}
`}

DIMENSIONS OBLIGATOIRES : width: 842, height: 595 (format A4 paysage)

Structure JSON exacte à respecter :
{
  "width": 842,
  "height": 595,
  "background_color": "${colorPalette.backgroundLight}",
  ${backgroundImageUrl ? `"background_image": "${backgroundImageUrl}",` : ''}
  "elements": [
    // ZONE DE TITRE (premier plan)
    {
      "id": "headline",
      "type": "text",
      "x": 50,
      "y": 50,
      "width": 400,
      "height": 80,
      "rotation": 0,
      "properties": {
        "text": "TITRE PRINCIPAL ACCROCHEUR",
        "fontSize": number_between_36_and_48,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "subheadline",
      "type": "text",
      "x": 50,
      "y": 130,
      "width": 400,
      "height": 50,
      "rotation": 0,
      "properties": {
        "text": "SOUS-TITRE EXPLICATIF",
        "fontSize": number_between_20_and_28,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.secondaryColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // ZONE DE SERVICES (colonne gauche)
    {
      "id": "services_title",
      "type": "text",
      "x": 50,
      "y": 200,
      "width": 300,
      "height": 40,
      "rotation": 0,
      "properties": {
        "text": "NOS SERVICES",
        "fontSize": number_between_20_and_24,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "services_list",
      "type": "text",
      "x": 50,
      "y": 250,
      "width": 300,
      "height": 200,
      "rotation": 0,
      "properties": {
        "text": "• Premier service offert\\n• Deuxième service proposé\\n• Troisième prestation\\n• Quatrième option disponible",
        "fontSize": number_between_16_and_20,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.tertiaryColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // ZONE DE CONTACT (colonne droite)
    {
      "id": "contact_title",
      "type": "text",
      "x": 550,
      "y": 200,
      "width": 250,
      "height": 40,
      "rotation": 0,
      "properties": {
        "text": "CONTACTEZ-NOUS",
        "fontSize": number_between_20_and_24,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.mainColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    {
      "id": "contact_info",
      "type": "text",
      "x": 550,
      "y": 250,
      "width": 250,
      "height": 100,
      "rotation": 0,
      "properties": {
        "text": "Tél: TÉLÉPHONE\\nEmail: EMAIL\\nSite web: SITE_WEB",
        "fontSize": number_between_16_and_20,
        "fontFamily": "Montserrat|Helvetica|Arial",
        "fill": "${colorPalette.tertiaryColor}",
        "align": "left",
        "fontStyle": "bold",
        "opacity": 1
      }
    },
    // IMAGES ET QR CODE
    ${imageUrl ? `{
      "id": "main_image",
      "type": "image",
      "x": 470,
      "y": 50,
      "width": 350,
      "height": 200,
      "rotation": 0,
      "properties": {
        "src": "${imageUrl}",
        "cornerRadius": number_between_0_and_15,
        "opacity": 1
      }
    },` : ''}
    {
      "id": "qrcode",
      "type": "qrcode",
      "x": 650,
      "y": 350,
      "width": 70,
      "height": 70,
      "rotation": 0,
      "properties": {
        "data": "${profileUrl}",
        "fill": "#000000",
        "background": "#ffffff",
        "opacity": 0.9
      }
    },
    // FORMES DÉCORATIVES (arrière-plan)
    {
      "id": "shape_header",
      "type": "shape",
      "x": 0,
      "y": 0,
      "width": 842,
      "height": 150,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.1
      }
    },
    ${Math.random() < 0.5 ? `{
      "id": "shape_vertical_divider",
      "type": "shape",
      "x": 420,
      "y": 180,
      "width": 2,
      "height": 350,
      "rotation": 0,
      "properties": {
        "shape": "rect",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.3
      }
    },` : `{
      "id": "design_element_${Math.floor(Math.random() * 1000)}",
      "type": "shape",
      "x": 400,
      "y": 200,
      "width": 40,
      "height": 300,
      "rotation": ${Math.random() < 0.5 ? '0' : 'number_between_0_and_30'},
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(4, Math.random() < 0.5 ? 'spiral' : 'blob')}",
        "fill": "${colorPalette.mainColor}",
        "opacity": number_between_0.1_and_0.4
      }
    },`}
    {
      "id": "shape_accent_1",
      "type": "shape",
      "x": -50,
      "y": 350,
      "width": 300,
      "height": 300,
      "rotation": 15,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(4, sectorConfig.svgType)}",
        "fill": "${colorPalette.accentColor1}",
        "opacity": 0.15
      }
    },
    {
      "id": "shape_accent_2",
      "type": "shape",
      "x": 550,
      "y": 400,
      "width": 400,
      "height": 400,
      "rotation": -15,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(3, sectorConfig.svgType)}",
        "fill": "${colorPalette.accentColor2}",
        "opacity": 0.2
      }
    },
    ${includeDrawing ? `{
      "id": "drawing_curve",
      "type": "drawing",
      "x": 350,
      "y": 150,
      "width": 400,
      "height": 300,
      "rotation": number_between_0_and_30,
      "properties": {
        "points": ${JSON.stringify(generateDrawingPoints(400, 300, 20, "fluid"))},
        "tension": number_between_0.2_and_0.5,
        "strokeWidth": number_between_1_and_3,
        "stroke": "${colorPalette.accentColor1}",
        "opacity": number_between_0.2_and_0.5
      }
    },` : ''}
    {
      "id": "shape_brand",
      "type": "shape",
      "x": 50,
      "y": 480,
      "width": 100,
      "height": 100,
      "rotation": 0,
      "properties": {
        "shape": "path",
        "data": "${generateSvgPath(5, 'custom')}",
        "fill": "${colorPalette.mainColor}",
        "opacity": 0.8
      }
    }
  ]
}

DESIGN PROFESSIONNEL (CRUCIAL) :
1. STRUCTURE EN COLONNES : Organisation claire avec zone de titre, zone de services (gauche) et zone de contact (droite)
2. COMPOSITION DYNAMIQUE : Utilisation efficace de l'espace horizontal avec équilibre entre texte et visuel
3. CONTRASTE : Maintenir un contraste élevé entre texte et arrière-plan
4. TYPOGRAPHIE : Utiliser des tailles de police hiérarchisées pour guider la lecture
5. COULEURS : Cohérence de la palette de couleurs avec accents visuels
6. SÉPARATION VISUELLE : Utilisation d'un séparateur vertical pour distinguer les zones d'information
7. ORDRE DES ÉLÉMENTS : Textes et QR code au début du tableau (premier plan), formes décoratives à la fin (arrière-plan)

RETOURNE UNIQUEMENT L'OBJET JSON COMPLET, PAS DE COMMENTAIRES NI MARKDOWN.`;
    }

    // Appel à l'API OpenRouter pour générer les templates
    let AI_API_MODEL = await selectAIModel(false);
    let usedFallback = false;

    logger.info('Génération de templates : Début de la génération des templates avec le modèle :' + AI_API_MODEL);
    logger.info(`Génération de templates : Prompt utilisé : ${prompt}`);

    const getAIPayload = (model: string) => ({
      model: model,
      messages: [
        {
          role: "system",
          content: `Tu es un générateur de templates JSON spécialisé pour Konva.js. Tu dois UNIQUEMENT retourner un objet JSON valide, sans aucun texte explicatif avant ou après, sans bloc de code markdown. Le JSON doit contenir exactement les propriétés demandées (width, height, background_color, elements).

IMPORTANT:
1. CONTRASTE: Assure un contraste suffisant entre le texte et l'arrière-plan. JAMAIS de texte blanc/clair sur fond blanc/clair ou texte foncé sur fond foncé.

2. ORDRE DES ÉLÉMENTS: L'ordre des éléments dans le tableau "elements" détermine l'ordre d'empilement visuel:
   - Les premiers éléments du tableau sont affichés AU-DESSUS (premier plan)
   - Les derniers éléments du tableau sont affichés EN-DESSOUS (arrière-plan)
   - Ordre correct: textes et QR codes → formes décoratives → formes d'arrière-plan

3. ADAPTATION AU MÉTIER: Crée un design parfaitement adapté au secteur d'activité de l'utilisateur:
   - Analyse le titre, les services et la description pour identifier précisément le métier
   - Pour les métiers de beauté (coiffeur, esthéticien): formes fluides, courbes élégantes, motifs délicats
   - Pour les métiers du jardinage/paysagisme: formes organiques, motifs naturels, éléments évoquant des feuilles
   - Pour les métiers du bâtiment/bricolage: formes géométriques, angles nets, structures techniques
   - Pour les services aux animaux: formes évoquant subtiles (empreintes, silhouettes)
   - Pour le secteur médical/santé: formes apaisantes, lignes douces et professionnelles
   - Pour les métiers créatifs: design plus audacieux avec superpositions et formes originales
   - Pour les métiers de la restauration: formes évoquant des ingrédients, ustensiles ou saveurs
   - Pour les métiers du transport: éléments suggérant mouvement, itinéraires ou véhicules
   - Pour les métiers de l'enseignement: symboles de connaissance, apprentissage et croissance
   - Pour les métiers du nettoyage: motifs épurés, lignes claires et espaces aérés
   - Pour les métiers de l'artisanat: textures authentiques, outils stylisés et savoir-faire
   - Pour les métiers de l'automobile: formes techniques, courbes dynamiques et éléments mécaniques
   - Pour les services juridiques: design équilibré, formes structurées et éléments symboliques

4. DESIGN PROFESSIONNEL:
   - Équilibre visuel avec espacement généreux entre les éléments
   - Hiérarchie claire des informations (nom/titre mis en évidence)
   - Utilisation intelligente des couleurs fournies
   - Formes décoratives qui ajoutent de la personnalité sans surcharger

5. POSITIONNEMENT INTELLIGENT:
   - Logo/image principale dans une position stratégique (généralement haut ou côté)
   - Éléments textuels bien alignés pour une lecture facile
   - QR code placé de façon accessible mais discrète

6. CRÉATIVITÉ ET DIVERSITÉ (CRUCIAL):
   - NE PAS suivre systématiquement le même modèle pour tous les designs
   - Varier les types de formes et leur disposition (éviter de toujours utiliser les mêmes formes)
   - Créer des combinaisons uniques d'éléments pour chaque design
   - Alterner entre designs épurés et designs plus élaborés
   - Modifier l'alignement des textes (gauche, droite, centré) en fonction du design global
   - Utiliser intelligemment l'espace négatif pour créer des designs plus variés
   - ÉVITER les éléments trop génériques comme "shape_vertical_divider" dans chaque design
   - Créer des mises en page originales tout en restant professionnelles

7. MISE EN FORME DU TEXTE (IMPORTANT):
   - Appliquer la propriété "fontStyle": "bold" à tous les éléments de type "text" pour assurer une bonne visibilité.

Respecte TOUJOURS cet ordre: textes et QR code au début du tableau (premier plan) → formes décoratives → formes d'arrière-plan à la fin du tableau (arrière-plan).`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.85, // Température élevée pour maximiser la diversité et la créativité
      max_tokens: 20000
    });

    const axiosConfig = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AI_API_KEY}`,
        'HTTP-Referer': 'https://jobpartiel.fr',
        'X-Title': 'JobPartiel Card Editor'
      },
      timeout: 30000 // Augmenter le timeout pour la génération de plusieurs templates
    };

    let response;
    try {
      response = await axios.post(
        AI_API_URL,
        getAIPayload(AI_API_MODEL),
        axiosConfig
      );

      // Fallback si le modèle gratuit retourne une erreur
      if (!usedFallback && response?.data?.error && response.data.error.code === 503) {
        logger.warn('Erreur 503 provider détectée, tentative avec le modèle payant.');
        AI_API_MODEL = AI_API_MODEL_PAYANT;
        usedFallback = true;
        response = await axios.post(
          AI_API_URL,
          getAIPayload(AI_API_MODEL),
          axiosConfig
        );
      }
    } catch (err: any) {
      // Si erreur 429 ou 400, on tente une fois avec le modèle payant
      if (err.response && (err.response.status === 429 || err.response.status === 400) && !usedFallback) {
        logger.warn(`Erreur ${err.response.status} détectée, tentative avec le modèle payant.`);
        AI_API_MODEL = AI_API_MODEL_PAYANT;
        usedFallback = true;
        try {
          response = await axios.post(
            AI_API_URL,
            getAIPayload(AI_API_MODEL),
            axiosConfig
          );
        } catch (err2) {
          logger.error('Erreur lors du fallback payant OpenRouter:', err2);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la génération des templates'
          });
        }
      } else {
        logger.error('Erreur lors de la génération des templates via OpenRouter:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération des templates'
        });
      }
    }

    logger.info('Génération de templates : Réponse de l\'IA:', response.data);

    // Extraire le contenu généré
    const generatedContent = response.data.choices[0].message.content;

    logger.info('Génération de templates : Contenu généré:', generatedContent);

    // Nettoyer les backticks, markdown et balises code
    let cleanedContent = generatedContent
      .replace(/```json\n/g, '')  // Supprimer les balises markdown json
      .replace(/```\n/g, '')      // Supprimer les balises markdown avec retour à la ligne
      .replace(/```json/g, '')    // Supprimer les balises markdown json sans retour à la ligne
      .replace(/```/g, '')        // Supprimer les backticks restants
      .trim();                    // Supprimer les espaces en début/fin

    logger.info('Génération de templates : Contenu nettoyé:', cleanedContent);

    // Chercher d'abord un objet JSON complet
    let jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);

    try {
      // On extrait le JSON trouvé dans la réponse de l'IA
      if (!jsonMatch) {
        logger.error("Erreur: Aucun JSON valide trouvé dans la réponse de l'IA");
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la génération des templates: format invalide'
        });
      }

      const jsonString = jsonMatch[0];
      logger.info(`JSON extrait avant parsing: ${jsonString}`);

      // Ajouter cette fonction après la fonction normalizeTemplateOrder

      /**
       * Répare un JSON potentiellement tronqué ou incomplet
       * @param jsonString JSON possiblement tronqué
       * @param expectedProperties Liste des propriétés obligatoires à vérifier
       * @returns JSON réparé ou null si impossible à réparer
       */
      function repairTruncatedJson(jsonString: string, type: string): any {
        try {
          // Essayons d'abord de parser directement
          try {
            const parsed = JSON.parse(jsonString);
            return parsed;
          } catch (initialError) {
            // Si l'erreur n'est pas une erreur de parsing (troncature), relancer
            if (!(initialError instanceof SyntaxError)) {
              throw initialError;
            }

            logger.warn(`JSON tronqué détecté, tentative de réparation...`);

            // Tenter de fermer correctement les accolades
            let fixedJson = jsonString;

            // Si le JSON est tronqué au milieu d'un élément
            if (!fixedJson.includes('"background_image"') && type.includes('background_image')) {
              fixedJson += '"background_image": null,';
            }

            // Vérifier si le JSON a une structure de base correcte
            if (!fixedJson.endsWith('}}')) {
              // Si le dernier élément est incomplet, fermer toutes les accolades et crochets nécessaires
              const openBraces = (fixedJson.match(/{/g) || []).length;
              const closeBraces = (fixedJson.match(/}/g) || []).length;
              const openBrackets = (fixedJson.match(/\[/g) || []).length;
              const closeBrackets = (fixedJson.match(/\]/g) || []).length;

              // Fermer tous les crochets d'abord, puis les accolades
              if (closeBrackets < openBrackets) {
                fixedJson += ']'.repeat(openBrackets - closeBrackets);
              }

              if (closeBraces < openBraces) {
                fixedJson += '}'.repeat(openBraces - closeBraces);
              }
            }

            // Vérifier si le dernier élément est incomplet
            const lastBraceIndex = fixedJson.lastIndexOf('}');
            const lastCommaIndex = fixedJson.lastIndexOf(',');

            if (lastCommaIndex > lastBraceIndex) {
              // Supprimer la virgule et tout ce qui la suit jusqu'à l'accolade fermante
              fixedJson = fixedJson.substring(0, lastCommaIndex) + '}';
            }

            // Essayer de parser le JSON réparé
            try {
              const repaired = JSON.parse(fixedJson);

              // Vérifier et corriger les propriétés minimales requises
              if (!repaired.width || !repaired.height) {
                if (type.includes('business_card_landscape')) {
                  repaired.width = 350;
                  repaired.height = 200;
                } else if (type.includes('business_card')) {
                  repaired.width = 200;
                  repaired.height = 350;
                } else if (type.includes('flyer_landscape')) {
                  repaired.width = 842;
                  repaired.height = 595;
                } else {
                  repaired.width = 595;
                  repaired.height = 842;
                }
              }

              if (!repaired.background_color) {
                repaired.background_color = "#ffffff";
              }

              if (!repaired.elements || !Array.isArray(repaired.elements) || repaired.elements.length < 3) {
                // Si les éléments sont absents ou insuffisants, impossible à réparer correctement
                logger.error('JSON réparé mais éléments insuffisants, impossible de récupérer');
                return null;
              }

              // Vérifier que les éléments ont des IDs uniques
              const seenIds = new Set();
              repaired.elements = repaired.elements.filter((el: any) => {
                if (!el || !el.id || seenIds.has(el.id)) {
                  return false;
                }
                seenIds.add(el.id);
                return true;
              });

              logger.info(`JSON réparé avec succès, ${repaired.elements.length} éléments récupérés`);
              return repaired;
            } catch (repairError) {
              logger.error('Échec de la réparation du JSON:', repairError);
              return null;
            }
          }
        } catch (error) {
          logger.error('Erreur lors de la tentative de réparation du JSON:', error);
          return null;
        }
      }

      try {
      templateData = JSON.parse(jsonString);
      } catch (parseError: any) {
        // NOUVELLE LOGIQUE: Essayer de réparer le JSON s'il est tronqué
        logger.warn(`Erreur lors du parsing initial du JSON: ${parseError.message}. Tentative de réparation...`);
        templateData = repairTruncatedJson(jsonString, type);

        if (!templateData) {
          logger.error(`Impossible de réparer le JSON tronqué.`);
          return res.status(500).json({
            success: false,
            message: 'Le template généré est incomplet ou corrompu. Veuillez réessayer.'
          });
        }
      }

      logger.info(`JSON parsé: ${JSON.stringify(templateData)}`);

      // Vérifier que nous avons un objet avec les propriétés requises
      if (!templateData || typeof templateData !== 'object' || !templateData.elements || !Array.isArray(templateData.elements)) {
        logger.error('JSON parsé invalide ou sans propriété elements:', templateData);
        return res.status(500).json({
          success: false,
          message: 'Le template généré est incomplet ou invalide. Veuillez réessayer.'
        });
      }

      // Si une image d'arrière-plan a été générée, s'assurer qu'elle est dans le JSON
      if (backgroundImageUrl && !templateData.background_image) {
        templateData.background_image = backgroundImageUrl;
        logger.info(`Ajout de l'image d'arrière-plan dans le JSON: ${backgroundImageUrl}`);
      }

      // Vérification des dimensions
      if (!templateData.width || !templateData.height || !templateData.background_color) {
        logger.error('Template généré sans propriétés obligatoires (width, height, background_color):', templateData);
        return res.status(500).json({
          success: false,
          message: 'Le template généré est incomplet (dimensions ou couleur de fond manquantes). Veuillez réessayer.'
        });
      }

      // Vérification du nombre minimum d'éléments
      if (templateData.elements.length < 3) {
        logger.error('Template généré avec trop peu d\'éléments:', templateData);
        return res.status(500).json({
          success: false,
          message: 'Le template généré ne contient pas assez d\'éléments. Veuillez réessayer.'
        });
      }

      // --- AJOUT: Logging et normalisation des éléments ---
      logger.info(`Ordre des éléments avant normalisation: ${JSON.stringify(templateData.elements.map((el: any) => ({ id: el.id, type: el.type })))}`);
      templateData = normalizeTemplateElements(templateData);
      logger.info(`Ordre des éléments après normalisation: ${JSON.stringify(templateData.elements.map((el: any) => ({ id: el.id, type: el.type })))}`);

      // --- AMÉLIORATION: Ajout automatique de la photo de profil lors de la génération IA ---
      if (includeProfilePhoto && userProfile.photo_url) {
        logger.info('Ajout automatique de la photo de profil lors de la génération IA');

        // Calculer les zones occupées par les éléments existants
        const occupiedZones = templateData.elements.map((el: any) => ({
          x: el.x || 0,
          y: el.y || 0,
          width: el.width || 50,
          height: el.height || 50
        }));

        // Dimensions de la photo de profil selon le type de template
        let photoWidth = 60;
        let photoHeight = 60;
        if (type.includes('flyer')) {
          photoWidth = 80;
          photoHeight = 80;
        }

        // Générer une position intelligente pour la photo de profil
        const photoPosition = generateSmartPosition(
          templateData.width,
          templateData.height,
          photoWidth,
          photoHeight,
          occupiedZones,
          15 // marge
        );

        // Créer l'élément photo de profil
        const profilePhotoElement = {
          id: `profile_photo_${Date.now()}`,
          type: 'image',
          x: photoPosition.x,
          y: photoPosition.y,
          width: photoWidth,
          height: photoHeight,
          rotation: 0,
          properties: {
            src: userProfile.photo_url,
            cornerRadius: Math.random() < 0.7 ? photoWidth / 2 : Math.random() * 15, // 70% de chance d'être circulaire
            opacity: 1
          }
        };

        // Ajouter la photo de profil au début du tableau (premier plan)
        templateData.elements.unshift(profilePhotoElement);
        logger.info(`Photo de profil ajoutée automatiquement à la position (${photoPosition.x}, ${photoPosition.y})`);
      }
    } catch (err: any) {
      logger.error('Erreur lors du parsing du JSON généré:', { error: err.message, content: cleanedContent });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du template: JSON invalide ou format non attendu.'
      });
    }

    // Déduire les crédits IA
    credits -= totalCost;

    // Mettre à jour les crédits dans la base de données
    const { error: updateError } = await supabase
      .from('user_ai_credits')
      .update({
        credits: credits,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour des crédits IA dans la base de données:', updateError);
      // Continuer malgré l'erreur, les templates ont été générés
    }

    // Mettre à jour le cache Redis
    await redis.setex(userCreditsKey, 3600, credits.toString());
    logger.info(`Crédits IA mis à jour dans Redis: ${credits}`);

    // Enregistrer les statistiques d'utilisation
    await supabase.from('card_ai_generation_stats').insert({
      user_id: userId,
      prompt: prompt,
      cost: totalCost,
      includes_image: includeImage,
      includes_background: includeBackgroundImage
    });

    // --- AJOUT: On insère d'abord le template pour obtenir le vrai templateId ---
    let savedTemplate;
    let templateId;
    let initialTemplateDataForDb = { ...templateData }; // Copie pour l'insertion initiale

    {
      let templateName = '';
      if (type === 'business_card') {
        templateName = 'Carte de visite générée';
      } else if (type === 'business_card_landscape') {
        templateName = 'Carte de visite générée (paysage)';
      } else if (type === 'flyer') {
        templateName = 'Flyer généré';
      } else { // flyer_landscape
        templateName = 'Flyer généré (paysage)';
      }
      const { data, error } = await supabase
        .from('card_templates')
        .insert({
          user_id: userId,
          name: templateName,
          type: type,
          template_data: initialTemplateDataForDb, // Utiliser la copie pour l'insertion initiale
          is_public: false,
          is_ai_generated: true
        })
        .select()
        .single();
      if (error) {
        logger.error('Erreur lors de la sauvegarde du template généré:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la sauvegarde du template généré'
        });
      }
      savedTemplate = data; // savedTemplate contient maintenant le template_data initial
      templateId = data.id;
    }

    let templateDataWasModified = false;

    // --- Traitement de imageUrl (image d'élément) et backgroundImageUrl (image de fond) après avoir le vrai templateId ---
    const imageElementResult = await processTemporaryImage(imageUrl, String(userId), templateId, templateData, 'element');
    if (imageElementResult.modified) {
      templateDataWasModified = true;
      imageUrl = imageElementResult.finalUrl; // Mettre à jour la variable locale pour les logs/stats
    }

    const backgroundImageResult = await processTemporaryImage(backgroundImageUrl, String(userId), templateId, templateData, 'background');
    if (backgroundImageResult.modified) {
      templateDataWasModified = true;
      backgroundImageUrl = backgroundImageResult.finalUrl; // Mettre à jour la variable locale pour les logs/stats
    }

    // --- Mise à jour finale en base de données si templateData a été modifié ---
    if (templateDataWasModified) {
      const { error: updateDbError } = await supabase
        .from('card_templates')
        .update({ template_data: templateData })
        .eq('id', templateId);

      if (updateDbError) {
        logger.error('Erreur lors de la mise à jour du template_data avec les URLs finales:', updateDbError);
        // Potentiellement retourner une erreur ou gérer, mais pour l'instant on loggue
      } else {
        // Mettre à jour savedTemplate avec le templateData final si l'update DB a réussi
        if (savedTemplate) {
          savedTemplate.template_data = templateData;
        }
        logger.info('Template_data mis à jour avec les URLs finales des images IA.', { templateId });
      }
    }

    // --- Correction IA : Déplacement de l'image IA dans le bon dossier après avoir le vrai templateId ---
    if (imageUrl && imageUrl.includes('/api/storage-proxy/temp_moderation/')) {
      try {
        const { uploadCardEditorImage } = require('../services/storage');
        const tempBucketUrl = '/api/storage-proxy/temp_moderation/';
        const filePath = imageUrl.split(tempBucketUrl)[1];
        if (filePath) {
          const { data: fileData, error: downloadError } = await supabase.storage
            .from('temp_moderation')
            .download(filePath);
          if (!downloadError && fileData) {
            const buffer = Buffer.from(await fileData.arrayBuffer());
            const mimeType = fileData.type || 'image/jpeg';
            // Uploader dans le bucket définitif AVEC LE VRAI templateId
            const finalImageUrl = await uploadCardEditorImage(String(userId), buffer, mimeType, templateId, { addIaTag: false });
            await supabase.storage.from('temp_moderation').remove([filePath]);
            // Mettre à jour le template en base avec la bonne URL
            if (templateData && Array.isArray(templateData.elements)) {
              templateData.elements = templateData.elements.map((el: any) => {
                if (el.type === 'image' && el.properties && el.properties.src === imageUrl) {
                  return { ...el, properties: { ...el.properties, src: finalImageUrl } };
                }
                return el;
              });
            }
            // Update en base
            await supabase
              .from('card_templates')
              .update({ template_data: templateData })
              .eq('id', templateId);
            imageUrl = finalImageUrl;
          }
        }
      } catch (err) {
        logger.error('Erreur lors du déplacement de l\'image IA du temporaire vers le bucket définitif:', err);
        // On continue même si l'upload échoue
      }
    }

    // Mettre à jour les statistiques d'utilisation avec l'ID du template
    await supabase.from('card_ai_generation_stats').insert({
      template_id: savedTemplate.id,
      user_id: userId,
      prompt: prompt,
      cost: totalCost,
      includes_image: includeImage,
      includes_background: includeBackgroundImage
    });

    // Invalider le cache des templates de l'utilisateur
    await redis.del(`${CACHE_PREFIX}templates:${String(userId)}`);

    // Log de l'activité
    logUserActivity(String(userId), 'generate_random_card_template', savedTemplate.id, 'card_template', {
      template_type: type,
      credits_used: totalCost,
      includes_image: includeImage,
      includes_background: includeBackgroundImage
    });

    // Enregistrer dans l'historique des crédits IA
    try {
      const { logAiCreditsOperation } = require('./aiCreditsController');

      // Calculer le solde avant l'opération
      const soldeAvant = credits + totalCost;

      // Construire une description détaillée
      let description = `Génération d'un template ${type} avec l'IA`;
      if (includeImage) description += ' (avec image)';
      if (includeBackgroundImage) description += ' (avec fond d\'image)';

      await logAiCreditsOperation(
        String(userId),
        'utilisation',
        totalCost,
        soldeAvant,
        credits,
        description,
        undefined,
        req?.ip || req?.headers?.['x-forwarded-for']?.toString() || null,
        'card_editor'
      );
      logger.info(`Opération de crédits IA enregistrée pour l'utilisateur ${userId}: utilisation de ${totalCost} crédits pour la génération d'un template`);
    } catch (historyError) {
      logger.error("Erreur lors de l'enregistrement dans l'historique des crédits IA:", historyError);
      // Continuer malgré l'erreur
    }

    // Log OpenRouter usage (statistiques admin)
    try {
      if (response && response.data && response.data.usage) {
        await logOpenRouterUsage(
          String(userId),
          'card_editor_prompt',
          AI_API_MODEL,
          response.data.usage.prompt_tokens || 0,
          response.data.usage.completion_tokens || 0,
          response.data.usage.total_tokens || 0,
          response.data.id || null
        );
      } else {
        // Estimation si usage non présent
        await logOpenRouterUsage(
          String(userId),
          'card_editor_prompt',
          AI_API_MODEL,
          prompt.length / 4,
          1000,
          (prompt.length / 4) + 1000,
          null
        );
      }
    } catch (logError) {
      logger.error('Erreur lors du log OpenRouter usage (card_editor_prompt):', logError);
    }

    return res.json({
      success: true,
      data: savedTemplate,
      creditsUsed: totalCost,
      creditsRemaining: credits,
      includesImage: includeImage,
      includesBackgroundImage: includeBackgroundImage
    });
  } catch (error) {
    logger.error('Erreur lors de la génération des templates:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération des templates'
    });
  }
};

// Exporter un template en PDF ou image
/**
 * Uploader une image pour un template de carte de visite ou flyer
 */
export const uploadImage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;

    // Vérifier que l'utilisateur est authentifié
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Vérifier que le templateId est bien présent et non vide
    if (!templateId || typeof templateId !== 'string' || templateId.trim() === '') {
      return res.status(400).json({
        success: false,
        message: "templateId manquant ou invalide dans l'URL. Impossible d'uploader l'image."
      });
    }

    // Vérifier que le template existe et appartient à l'utilisateur
    const { data: template, error: templateError } = await supabase
      .from('card_templates')
      .select('user_id')
      .eq('id', templateId)
      .single();

    if (templateError || !template) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }

    if (template.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas le droit de modifier ce template'
      });
    }

    // Vérifier que le fichier est présent (express-fileupload)
    if (!req.files || (!req.files.image && !req.files.file)) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier n\'a été fourni'
      });
    }
    const file = req.files.image
      ? (Array.isArray(req.files.image) ? req.files.image[0] : req.files.image)
      : (Array.isArray(req.files.file) ? req.files.file[0] : req.files.file);
    let fileBuffer: Buffer;
    if (file.tempFilePath) {
      fileBuffer = fs.readFileSync(file.tempFilePath);
    } else {
      fileBuffer = file.data;
    }
    const fileType = file.mimetype;

    // Vérifier que le fichier est une image
    if (!fileType.startsWith('image/')) {
      return res.status(400).json({
        success: false,
        message: 'Le fichier doit être une image'
      });
    }

    // Uploader l'image
    const imageUrl = await uploadCardEditorImage(
      String(userId),
      fileBuffer,
      fileType,
      templateId
    );

    // Invalider le cache du template
    await redis.del(`${CACHE_PREFIX}template:${templateId}`);

    // Log de l'activité
    logUserActivity(String(userId), 'upload_card_image', templateId, 'card_template', {
      image_url: imageUrl
    });

    return res.status(200).json({
      success: true,
      data: {
        url: imageUrl
      }
    });
  } catch (error) {
    logger.error('Erreur lors de l\'upload de l\'image:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'upload de l\'image'
    });
  }
};

export const exportTemplateImage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { format } = req.query;
    const { templateData } = req.body;

    if (!templateData) {
      return res.status(400).json({ error: 'Template data required' });
    }

    if (!['png', 'jpg', 'jpeg'].includes(format as string)) {
      return res.status(400).json({ error: 'Format non supporté' });
    }

    // Pour l'instant, retourner une erreur car l'implémentation côté serveur
    // nécessiterait un moteur de rendu comme Puppeteer ou Canvas
    return res.status(501).json({
      error: 'Export d\'images côté serveur non encore implémenté. Utilisez le format PDF.'
    });

  } catch (error) {
    logger.error('Erreur lors de l\'export d\'image:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const exportTemplate = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;
    const { format } = req.body;

    // Vérifier le format
    if (!format || !['pdf', 'png', 'jpg'].includes(format)) {
      return res.status(400).json({
        success: false,
        message: 'Format invalide. Doit être "pdf", "png" ou "jpg"'
      });
    }

    // Récupérer le template
    const { data: template, error: templateError } = await supabase
      .from('card_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (templateError || !template) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }

    // Vérifier que l'utilisateur est le propriétaire ou que le template est public
    if (template.user_id !== userId && !template.is_public) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas accès à ce template'
      });
    }

    // Créer un dossier temporaire si nécessaire
    const tempDir = os.tmpdir();
    const timestamp = new Date().getTime();
    const fileName = `${template.type}_${templateId}_${timestamp}.${format}`;
    const filePath = path.join(tempDir, fileName);

    // Exporter selon le format demandé
    if (format === 'pdf') {
      // Créer un PDF
      const doc = new PDFDocument({
        size: template.type === 'business_card' ? [template.template_data.width, template.template_data.height] : 'A4',
        margin: 0,
        info: {
          Title: template.name,
          Author: 'JobPartiel',
          Subject: template.type === 'business_card' ? 'Carte de visite' : 'Flyer',
          Producer: 'JobPartiel Card Editor'
        }
      });

      // Flux de sortie vers le fichier
      const stream = fs.createWriteStream(filePath);
      doc.pipe(stream);

      // Ajouter le fond de couleur
      if (template.template_data.background_color) {
        doc.rect(0, 0, template.template_data.width, template.template_data.height)
           .fill(template.template_data.background_color);
      }

      // Ajouter l'image de fond si elle existe
      if (template.template_data.background_image) {
        try {
          // Pour l'instant, ajouter un placeholder pour l'image de fond
          doc.rect(0, 0, template.template_data.width, template.template_data.height)
             .stroke('#DDDDDD')
             .lineWidth(1);

          doc.fontSize(8)
             .fillColor('#999999')
             .text('Image de fond', 10, 10);
        } catch (bgError) {
          logger.error('Erreur lors du traitement de l\'image de fond:', bgError);
        }
      }

      // Ajouter les éléments dans l'ordre correct
      const sortedElements = [...template.template_data.elements].sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0));

      for (const element of sortedElements) {
        try {
          // Vérifier la visibilité de l'élément
          if (element.properties?.visible === false) continue;

          if (element.type === 'text') {
            const props = element.properties || {};

            // Utiliser uniquement les polices disponibles dans PDFKit
            let fontFamily = 'Helvetica';
            if (props.fontFamily) {
              const fontMap: { [key: string]: string } = {
                'Arial': 'Helvetica',
                'Times': 'Times-Roman',
                'Courier': 'Courier',
                'Helvetica': 'Helvetica',
                'Times-Roman': 'Times-Roman',
                'Times-Bold': 'Times-Bold',
                'Times-Italic': 'Times-Italic',
                'Times-BoldItalic': 'Times-BoldItalic',
                'Helvetica-Bold': 'Helvetica-Bold',
                'Helvetica-Oblique': 'Helvetica-Oblique',
                'Helvetica-BoldOblique': 'Helvetica-BoldOblique',
                'Courier-Bold': 'Courier-Bold',
                'Courier-Oblique': 'Courier-Oblique',
                'Courier-BoldOblique': 'Courier-BoldOblique'
              };
              fontFamily = fontMap[props.fontFamily] || 'Helvetica';
            }

            doc.font(fontFamily)
               .fontSize(props.fontSize || 12)
               .fillColor(props.fill || '#000000');

            // Gérer l'alignement et la rotation
            if (element.rotation && element.rotation !== 0) {
              doc.save()
                 .translate(element.x + (element.width || 0) / 2, element.y + (element.height || 0) / 2)
                 .rotate(element.rotation * (180 / Math.PI)) // Convertir radians en degrés
                 .text(props.text || '', -(element.width || 0) / 2, -(element.height || 0) / 2, {
                   width: element.width,
                   height: element.height,
                   align: props.align || 'left'
                 })
                 .restore();
            } else {
              doc.text(props.text || '', element.x, element.y, {
                width: element.width,
                height: element.height,
                align: props.align || 'left'
              });
            }

          } else if (element.type === 'shape') {
            const props = element.properties || {};
            const fillColor = props.fill || '#000000';
            const strokeColor = props.stroke || null;
            const strokeWidth = props.strokeWidth || 0;
            const opacity = props.opacity !== undefined ? props.opacity : 1;

            // Appliquer l'opacité si différente de 1
            if (opacity < 1) {
              doc.fillOpacity(opacity);
              doc.strokeOpacity(opacity);
            }

            // Gérer la rotation des formes
            const hasRotation = element.rotation && element.rotation !== 0;
            if (hasRotation) {
              doc.save()
                 .translate(element.x + (element.width || 100) / 2, element.y + (element.height || 100) / 2)
                 .rotate(element.rotation * (180 / Math.PI)); // Convertir radians en degrés
            }

            if (props.shape === 'rect') {
              const rectX = hasRotation ? -(element.width || 100) / 2 : element.x;
              const rectY = hasRotation ? -(element.height || 100) / 2 : element.y;
              if (fillColor && fillColor !== 'transparent' && fillColor !== null) {
                doc.rect(rectX, rectY, element.width || 100, element.height || 100)
                   .fill(fillColor);
              }
              if (strokeColor && strokeWidth > 0) {
                doc.strokeColor(strokeColor)
                   .lineWidth(strokeWidth)
                   .rect(rectX, rectY, element.width || 100, element.height || 100)
                   .stroke();
              }
            } else if (props.shape === 'circle') {
              const radius = (element.width || 100) / 2;
              const centerX = hasRotation ? 0 : element.x + radius;
              const centerY = hasRotation ? 0 : element.y + radius;

              if (fillColor && fillColor !== 'transparent' && fillColor !== null) {
                doc.circle(centerX, centerY, radius).fill(fillColor);
              }
              if (strokeColor && strokeWidth > 0) {
                doc.strokeColor(strokeColor)
                   .lineWidth(strokeWidth)
                   .circle(centerX, centerY, radius)
                   .stroke();
              }
            } else if (props.shape === 'ellipse') {
              const radiusX = (element.width || 100) / 2;
              const radiusY = (element.height || 100) / 2;
              const centerX = hasRotation ? 0 : element.x + radiusX;
              const centerY = hasRotation ? 0 : element.y + radiusY;

              if (fillColor && fillColor !== 'transparent' && fillColor !== null) {
                doc.ellipse(centerX, centerY, radiusX, radiusY).fill(fillColor);
              }
              if (strokeColor && strokeWidth > 0) {
                doc.strokeColor(strokeColor)
                   .lineWidth(strokeWidth)
                   .ellipse(centerX, centerY, radiusX, radiusY)
                   .stroke();
              }
            } else if (props.shape === 'polygon') {
              // Gestion des polygones (hexagone, etc.)
              const sides = props.sides || 6;
              const radius = (element.width || 100) / 2;
              const centerX = hasRotation ? 0 : element.x + radius;
              const centerY = hasRotation ? 0 : element.y + radius;

              // Créer les points du polygone
              const points = [];
              for (let i = 0; i < sides; i++) {
                const angle = (i * 2 * Math.PI) / sides - Math.PI / 2;
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle);
                points.push(x, y);
              }

              if (points.length >= 6) { // Au moins 3 points (6 coordonnées)
                doc.moveTo(points[0], points[1]);
                for (let i = 2; i < points.length; i += 2) {
                  doc.lineTo(points[i], points[i + 1]);
                }
                doc.closePath();

                if (fillColor && fillColor !== 'transparent' && fillColor !== null) {
                  doc.fill(fillColor);
                }
                if (strokeColor && strokeWidth > 0) {
                  doc.strokeColor(strokeColor)
                     .lineWidth(strokeWidth)
                     .stroke();
                }
              }
            } else if (props.shape === 'path' && props.data) {
              // Gestion des paths SVG - simplifiée pour PDF
              // Pour l'instant, on dessine un rectangle avec un motif
              if (fillColor && fillColor !== 'transparent' && fillColor !== null) {
                // Créer un pattern simple pour représenter le path
                const pathWidth = element.width || 100;
                const pathHeight = element.height || 100;
                const startX = hasRotation ? -pathWidth / 2 : element.x;
                const startY = hasRotation ? -pathHeight / 2 : element.y;

                // Dessiner une forme ondulée simplifiée
                doc.moveTo(startX, startY + pathHeight / 2);
                doc.quadraticCurveTo(
                  startX + pathWidth / 4, startY,
                  startX + pathWidth / 2, startY + pathHeight / 2
                );
                doc.quadraticCurveTo(
                  startX + (3 * pathWidth) / 4, startY + pathHeight,
                  startX + pathWidth, startY + pathHeight / 2
                );
                doc.lineTo(startX + pathWidth, startY + pathHeight);
                doc.lineTo(startX, startY + pathHeight);
                doc.closePath();
                doc.fill(fillColor);
              }
            }

            // Fermer la rotation si elle était appliquée
            if (hasRotation) {
              doc.restore();
            }

            // Réinitialiser l'opacité
            if (opacity < 1) {
              doc.fillOpacity(1);
              doc.strokeOpacity(1);
            }

          } else if (element.type === 'image' && element.properties?.src) {
            try {
              // Essayer de charger et inclure l'image
              const imageUrl = element.properties.src;

              // Si c'est une URL locale (notre API), essayer de la charger
              if (imageUrl.includes('/api/storage-proxy/')) {
                // Pour l'instant, ajouter un placeholder avec les bonnes dimensions
                doc.rect(element.x, element.y, element.width || 100, element.height || 100)
                   .stroke('#CCCCCC')
                   .lineWidth(1);

                // Ajouter un texte centré
                doc.fontSize(10)
                   .fillColor('#666666')
                   .text('Image',
                     element.x + (element.width || 100) / 2 - 15,
                     element.y + (element.height || 100) / 2 - 5
                   );
              } else {
                // Pour les autres URLs, ajouter un placeholder
                doc.rect(element.x, element.y, element.width || 100, element.height || 100)
                   .stroke('#CCCCCC')
                   .lineWidth(1);

                doc.fontSize(8)
                   .fillColor('#666666')
                   .text('Image externe',
                     element.x + 5,
                     element.y + (element.height || 100) / 2
                   );
              }
            } catch (imageError) {
              logger.error('Erreur lors du traitement de l\'image:', imageError);
              // Fallback: rectangle avec texte d'erreur
              doc.rect(element.x, element.y, element.width || 100, element.height || 100)
                 .stroke('#FF0000')
                 .lineWidth(1);

              doc.fontSize(8)
                 .fillColor('#FF0000')
                 .text('Erreur image', element.x + 5, element.y + 5);
            }

          } else if (element.type === 'qrcode' && (element.properties?.value || element.properties?.data)) {
            try {
              // Créer un QR code placeholder avec les bonnes dimensions
              const qrSize = Math.min(element.width || 100, element.height || 100);

              // Dessiner le contour du QR code
              doc.rect(element.x, element.y, qrSize, qrSize)
                 .stroke('#000000')
                 .lineWidth(2);

              // Ajouter un pattern simple pour simuler un QR code
              const cellSize = qrSize / 10;
              for (let i = 0; i < 10; i++) {
                for (let j = 0; j < 10; j++) {
                  if ((i + j) % 2 === 0) {
                    doc.rect(
                      element.x + i * cellSize,
                      element.y + j * cellSize,
                      cellSize,
                      cellSize
                    ).fill('#000000');
                  }
                }
              }

              // Ajouter le texte de la valeur en dessous si il y a de la place
              const qrValue = element.properties.value || element.properties.data;
              if (qrValue && qrValue.length < 50) {
                doc.fontSize(6)
                   .fillColor('#000000')
                   .text(
                     qrValue.substring(0, 30) + (qrValue.length > 30 ? '...' : ''),
                     element.x,
                     element.y + qrSize + 2,
                     { width: qrSize, align: 'center' }
                   );
              }
            } catch (qrError) {
              logger.error('Erreur lors du traitement du QR code:', qrError);
              // Fallback: rectangle simple
              doc.rect(element.x, element.y, element.width || 100, element.height || 100)
                 .stroke('#000000')
                 .lineWidth(1);

              doc.fontSize(8)
                 .fillColor('#000000')
                 .text('QR Code', element.x + 5, element.y + 5);
            }
          }

        } catch (elementError) {
          logger.error('Erreur lors du rendu d\'un élément:', elementError);
          // Continuer avec les autres éléments
        }
      }

      // Finaliser le document
      doc.end();

      // Attendre que le fichier soit écrit
      await new Promise<void>((resolve, reject) => {
        stream.on('finish', () => {
          resolve();
        });
        stream.on('error', (err) => {
          reject(err);
        });
      });
    } else {
      // Pour les formats d'image, nous aurions besoin d'une bibliothèque de rendu canvas côté serveur
      // Cette partie est simplifiée et devrait être adaptée avec une solution comme node-canvas
      return res.status(501).json({
        success: false,
        message: 'L\'export en format image n\'est pas encore implémenté côté serveur. Utilisez l\'export côté client.'
      });
    }

    // Enregistrer l'export dans la base de données
    await supabase.from('card_exports').insert({
      user_id: userId,
      template_id: templateId,
      export_type: format,
      file_path: fileName
    });

    // Log de l'activité
    logUserActivity(String(userId), 'export_card_template', templateId, 'card_template', {
      export_type: format
    });

    // Envoyer le fichier
    res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : `image/${format}`);
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Supprimer le fichier après l'envoi
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    logger.error('Erreur lors de l\'export du template:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'export du template'
    });
  }
};

// Supprimer une image individuelle du card editor
export const deleteImage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;
    const { imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'URL de l\'image requise'
      });
    }

    // Empêcher la suppression de l'avatar par défaut (utiliser la constante)
    if (imageUrl === DEFAULT_AVATAR_URL || imageUrl.includes('avatar-defaut-jobpartiel.jpg')) {
      return res.status(400).json({
        success: false,
        message: 'Impossible de supprimer l\'avatar par défaut.'
      });
    }

    // Vérifier que le template appartient à l'utilisateur
    const { data: template, error: templateError } = await supabase
      .from('card_templates')
      .select('user_id')
      .eq('id', templateId)
      .single();
    if (templateError || !template) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }
    if (template.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas le droit de supprimer cette image'
      });
    }

    // Récupérer le storage_id de l'utilisateur
    const { data: userProfil, error: storageError } = await supabase
      .from('user_profil')
      .select('storage_id')
      .eq('user_id', userId)
      .single();

    if (storageError) {
      logger.error('Erreur lors de la récupération du storage_id:', storageError);
      // Continuer malgré l'erreur
    }
    if (!userProfil || !userProfil.storage_id) {
      logger.error('Impossible de récupérer le storage_id pour suppression image individuelle', { userId, templateId });
      return res.status(500).json({
        success: false,
        message: 'Impossible de récupérer le storage_id'
      });
    }

    // Extraire le chemin du fichier à partir de l'URL publique
    const bucketPrefix = `/api/storage-proxy/carte_visite_et_flyer/`;
    const idx = imageUrl.indexOf(bucketPrefix);
    if (idx === -1) {
      return res.status(400).json({
        success: false,
        message: 'URL d\'image invalide'
      });
    }
    const filePath = imageUrl.substring(idx + bucketPrefix.length);

    // Supprimer l'image du bucket
    const { error: deleteError } = await supabase.storage
      .from('carte_visite_et_flyer')
      .remove([filePath]);
    if (deleteError) {
      logger.error('Erreur lors de la suppression de l\'image individuelle du card editor', { deleteError, userId, templateId, filePath });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression de l\'image'
      });
    }
    logger.info('Image individuelle supprimée du card editor', { userId, templateId, filePath });
    return res.json({
      success: true,
      message: 'Image supprimée avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la suppression d\'une image individuelle du card editor', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'image'
    });
  }
};

// Supprimer toutes les images d'un template (card editor)
export const deleteAllImages = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const templateId = req.params.id;

    // Vérifier que le template appartient à l'utilisateur
    const { data: template, error: templateError } = await supabase
      .from('card_templates')
      .select('user_id')
      .eq('id', templateId)
      .single();
    if (templateError || !template) {
      return res.status(404).json({
        success: false,
        message: 'Template non trouvé'
      });
    }
    if (template.user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas le droit de supprimer les images de ce template'
      });
    }

    // Récupérer le storage_id de l'utilisateur
    const { data: userProfil, error: storageError } = await supabase
      .from('user_profil')
      .select('storage_id')
      .eq('user_id', userId)
      .single();

        if (storageError) {
      logger.error('Erreur lors de la récupération du storage_id:', storageError);
      // Continuer malgré l'erreur
    }
    if (!userProfil || !userProfil.storage_id) {
      return res.status(500).json({
        success: false,
        message: 'Impossible de récupérer le storage_id'
      });
    }

    // La logique d'exclusion de l'avatar par défaut est déjà gérée dans deleteCardEditorImages
    await deleteCardEditorImages(userProfil.storage_id, templateId);
    return res.json({ success: true, message: 'Toutes les images supprimées du bucket' });
  } catch (error) {
    logger.error('Erreur lors de la suppression de toutes les images du card editor', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression des images'
    });
  }
};

// Réordonner les templates de l'utilisateur
export const reorderTemplates = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    const { items } = req.body; // items: [{id, order_index}]

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Format de données invalide pour la réorganisation'
      });
    }
    // Vérifier que tous les templates appartiennent à l'utilisateur
    const templateIds = items.map(item => item.id);
    const { data: existingTemplates, error: checkError } = await supabase
      .from('card_templates')
      .select('id')
      .eq('user_id', userId)
      .in('id', templateIds);
    if (checkError) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification des templates'
      });
    }
    if (!existingTemplates || existingTemplates.length !== templateIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Certains templates n\'appartiennent pas à l\'utilisateur'
      });
    }
    // Mettre à jour l'ordre de chaque template
    for (const item of items) {
      const { error } = await supabase
        .from('card_templates')
        .update({ order_index: item.order_index, updated_at: new Date().toISOString() })
        .eq('id', item.id)
        .eq('user_id', userId);
      if (error) {
        return res.status(500).json({
          success: false,
          message: `Erreur lors de la mise à jour de l'ordre du template ${item.id}`
        });
      }
    }
    // Invalider le cache
    await redis.del(`${CACHE_PREFIX}templates:${String(userId)}`);
    return res.status(200).json({
      success: true,
      message: 'Templates réorganisés avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la réorganisation des templates:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la réorganisation des templates'
    });
  }
};

// Cette fonction a été remplacée par un processus automatique
// L'activation/désactivation des templates est maintenant gérée automatiquement
// par le système en fonction du type d'abonnement de l'utilisateur
export const toggleTemplateStatus = async (_req: Request, res: Response) => {
  return res.status(403).json({
    success: false,
    message: 'Cette fonctionnalité n\'est plus disponible. L\'activation/désactivation des templates est gérée automatiquement en fonction de votre abonnement.'
  });
};

// Modifier la fonction generateMultipleTemplates pour mettre à jour l'état d'avancement
export const generateMultipleTemplates = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    const { type, userData, count = 2 } = req.body;

    logger.info(`[generateMultipleTemplates] Début de la génération multiple pour l'utilisateur ${userId}. Type: ${type}, Count: ${count}`);

    // Vérifier le type
    if (!type || !['business_card', 'business_card_landscape', 'flyer', 'flyer_landscape'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Type invalide. Doit être "business_card", "business_card_landscape", "flyer" ou "flyer_landscape"'
      });
    }

    // Initialiser l'état de génération
    updateGenerationStatus(userId, 'in_progress', Array(count).fill(0));
    logger.info(`[generateMultipleTemplates] Statut initialisé pour l'utilisateur ${userId}: in_progress, progress: ${Array(count).fill(0)}`);

    // On va stocker les résultats ici
    const templates: any[] = [];
    let creditsUsed = 0;
    let errorResponse = null;

    for (let i = 0; i < count; i++) {
      logger.info(`[generateMultipleTemplates] Début de la génération pour le template ${i + 1}/${count} pour l'utilisateur ${userId}.`);

      // Mettre à jour l'état pour indiquer qu'on commence à générer ce template
      const currentProgressArray = [...userGenerationStatus.get(userId)?.progress || Array(count).fill(0)];
      currentProgressArray[i] = 5;
      updateGenerationStatus(userId, 'in_progress', currentProgressArray);

      // Simulation de progression fluide
      const steps = [15, 35, 55, 75, 90];
      for (const step of steps) {
        currentProgressArray[i] = step;
        updateGenerationStatus(userId, 'in_progress', currentProgressArray);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      currentProgressArray[i] = 95;
      updateGenerationStatus(userId, 'in_progress', currentProgressArray);

      // Appel direct à generateRandomTemplate
      const fakeReq: any = {
        ...req,
        body: {
          type,
          userData
        },
        user: req.user
      };
      let templateResult: any = {};
      const fakeRes = {
        status: (code: number) => { templateResult._statusCode = code; return fakeRes; },
        json: (data: any) => { templateResult = data; return data; }
      };

      // @ts-ignore
      await generateRandomTemplate(fakeReq, fakeRes);

      logger.info(`[generateMultipleTemplates] Appel à generateRandomTemplate terminé pour le template ${i + 1}. Résultat capturé:`, templateResult);

      if (templateResult.success && templateResult.data) {
        logger.info(`[generateMultipleTemplates] Génération du template ${i + 1} réussie.`);
        const templateWithId = {
          ...templateResult.data.template_data || templateResult.data,
          id: templateResult.data.id
        };
        templates.push(templateWithId);
        creditsUsed += templateResult.creditsUsed || 0;
        currentProgressArray[i] = 100;
        updateGenerationStatus(userId, 'in_progress', currentProgressArray, templates);
      } else {
        errorResponse = templateResult;
        logger.error(`[generateMultipleTemplates] Erreur lors de la génération du template ${i + 1}:`, errorResponse);
        updateGenerationStatus(userId, 'error', currentProgressArray);
        break;
      }
      logger.info(`[generateMultipleTemplates] Fin de l'itération pour le template ${i + 1}.`)
    }

    if (errorResponse) {
      logger.info(`[generateMultipleTemplates] Terminé avec erreur. errorResponse:`, errorResponse);
      return res.status(errorResponse._statusCode || 400).json(errorResponse);
    }

    updateGenerationStatus(userId, 'completed', Array(count).fill(100), templates);
    logger.info(`[generateMultipleTemplates] Génération multiple terminée avec succès pour l'utilisateur ${userId}.`);

    return res.json({
      success: true,
      templates,
      creditsUsed
    });
  } catch (error) {
    logger.error('Erreur lors de la génération multiple (boucle random):', error);
    const userId = req.user?.userId;
    if (userId) {
      const currentStatus = userGenerationStatus.get(userId);
      if (currentStatus) {
        updateGenerationStatus(userId, 'error', currentStatus.progress);
      }
    }
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération des templates (multiple)'
    });
  }
};

// --- AJOUT: Fonction utilitaire de normalisation des éléments IA ---
function normalizeTemplateElements(template: any) {
  const maxWidth = template.width;
  const maxHeight = template.height;

  template.elements = template.elements.map((el: any) => {
    // Clamp x/y >= 0
    if (typeof el.x === 'number' && el.x < 0) el.x = 0;
    if (typeof el.y === 'number' && el.y < 0) el.y = 0;

    // Clamp width/height <= max
    if (el.width && el.width > maxWidth) el.width = maxWidth;
    if (el.height && el.height > maxHeight) el.height = maxHeight;

    // Clamp width/height pour ne pas sortir du cadre
    if (el.width && typeof el.x === 'number' && el.x + el.width > maxWidth) {
      el.width = Math.max(10, maxWidth - el.x);
    }
    if (el.height && typeof el.y === 'number' && el.y + el.height > maxHeight) {
      el.height = Math.max(10, maxHeight - el.y);
    }

    // Clamp text width/height pour les textes
    if (el.type === 'text') {
      if (el.width && el.width > maxWidth) el.width = maxWidth;
      if (el.width && typeof el.x === 'number' && el.x + el.width > maxWidth) {
        el.width = Math.max(10, maxWidth - el.x);
      }
      if (el.height && el.height > maxHeight) el.height = maxHeight;
      if (el.height && typeof el.y === 'number' && el.y + el.height > maxHeight) {
        el.height = Math.max(10, maxHeight - el.y);
      }
    }

    // Largeur/hauteur minimum pour éviter des éléments invisibles
    if (el.width && el.width < 10) el.width = 10;
    if (el.height && el.height < 10) el.height = 10;

    // --- PATCH QR CODE ---
    if (el.type === 'qrcode' && el.properties) {
      if (el.properties.background === 'transparent') {
        el.properties.background = '#FFFFFF00'; // blanc transparent en hex RGBA
      }
      if (el.properties.fill === 'transparent') {
        el.properties.fill = '#000000'; // noir par défaut
      }
    }

    return el;
  });

  // Normaliser l'ordre des éléments
  template = normalizeTemplateOrder(template);

  return template;
}

// Fonction pour réorganiser les éléments dans le bon ordre d'empilement
function normalizeTemplateOrder(template: any) {
  if (!template.elements || !Array.isArray(template.elements)) {
    return template;
  }

  // Créer des catégories pour grouper les éléments
  const backgroundShapes: any[] = [];  // Arrière-plans (formes couvrant tout)
  const decorativeShapes: any[] = [];  // Formes décoratives (petites formes)
  const midLayerShapes: any[] = [];    // Formes qui ne doivent pas être au-dessus des textes
  const textsAndQRCodes: any[] = [];   // Textes et QR codes (premier plan)
  const images: any[] = [];            // Images (peuvent être placées avant ou après les textes)

  // Classifier chaque élément selon son type et ses caractéristiques
  template.elements.forEach((element: any) => {
    if (element.type === 'text') {
      textsAndQRCodes.push(element);
    } else if (element.type === 'qrcode') {
      textsAndQRCodes.push(element);
    } else if (element.type === 'image') {
      // Déterminer si c'est une image de fond ou une image normale
      // Une image de fond occupe généralement une grande partie de la carte
      const isBackgroundImage =
        element.width >= template.width * 0.8 &&
        element.height >= template.height * 0.8;

      if (isBackgroundImage) {
        // Les images de fond vont à l'arrière-plan
        backgroundShapes.push(element);
      } else {
        // Les images normales vont au premier plan
        images.push(element);
      }
    } else if (element.type === 'shape') {
      // Si c'est un fond ou une forme qui occupe presque tout l'espace
      const isBackground = element.width >= template.width * 0.8 && element.height >= template.height * 0.8;

      // Vérifier si l'élément est au milieu de la carte (donc susceptible de masquer du texte)
      const isInCenterArea =
        element.x < template.width * 0.6 &&
        element.y < template.height * 0.6 &&
        element.x + element.width > template.width * 0.4 &&
        element.y + element.height > template.height * 0.4;

      // Si la forme est grande et en transparence, c'est probablement une couche décorative
      const isLargeDecorativeOverlay =
        element.width > template.width * 0.5 &&
        element.height > template.height * 0.5 &&
        element.properties?.opacity &&
        element.properties.opacity < 0.6;

      if (isBackground) {
        backgroundShapes.push(element);
      } else if (isLargeDecorativeOverlay || isInCenterArea) {
        // Si c'est une grande forme décorative ou qu'elle est au centre, la mettre en arrière des textes
        midLayerShapes.push(element);
      } else {
        // Petites formes décoratives (peuvent être au-dessus des textes si elles ne sont pas au centre)
        decorativeShapes.push(element);
      }
    } else {
      // Autres types d'éléments
      decorativeShapes.push(element);
    }
  });

  // IMPORTANT: L'ordre des éléments dans le tableau est inversé lors de l'affichage dans l'éditeur.
  // Le premier élément du tableau est affiché au-dessus (premier plan), et le dernier élément est affiché en dessous (arrière-plan).
  // Par conséquent, nous devons inverser notre logique d'empilement ici.

  // Vérifier s'il y a des formes qui pourraient causer des problèmes de superposition avec le texte
  const hasOverlappingShapes = midLayerShapes.length > 0;

  // Reconstituer le tableau d'éléments dans le bon ordre (inversé pour l'affichage)
  // IMPORTANT: Dans Konva, le premier élément du tableau est affiché au-dessus (premier plan)
  // et le dernier élément est affiché en dessous (arrière-plan)

  // Séparer les images de fond des images normales pour un meilleur contrôle
  const backgroundImages = backgroundShapes.filter(el => el.type === 'image');
  const otherBackgroundShapes = backgroundShapes.filter(el => el.type !== 'image');

  // Placer les images de fond tout à la fin (arrière-plan)
  if (hasOverlappingShapes) {
    // Si des formes pourraient masquer le texte, on force l'ordre logique
    template.elements = [
      ...textsAndQRCodes,    // Textes et QR codes en premier plan (au-dessus)
      ...images,             // Images normales juste en-dessous des textes
      ...decorativeShapes.filter(shape => !shape.properties?.opacity || shape.properties.opacity >= 0.7), // Formes opaques
      ...decorativeShapes.filter(shape => shape.properties?.opacity && shape.properties.opacity < 0.7), // Formes transparentes
      ...midLayerShapes,     // Formes décoratives qui seraient au milieu
      ...otherBackgroundShapes, // Formes d'arrière-plan
      ...backgroundImages    // Images de fond tout à la fin (tout en bas)
    ];
  } else {
    // Sans problème potentiel de superposition, ordre standard
    template.elements = [
      ...textsAndQRCodes,    // Textes et QR codes en premier plan (au-dessus)
      ...images,             // Images normales juste en-dessous des textes
      ...decorativeShapes,   // Formes décoratives
      ...midLayerShapes,     // Formes moyennes
      ...otherBackgroundShapes, // Formes d'arrière-plan
      ...backgroundImages    // Images de fond tout à la fin (tout en bas)
    ];
  }

  // Assurer un contraste de couleur suffisant pour chaque texte
  ensureTextContrast(template);

  return template;
}

// Fonction pour assurer un contraste suffisant entre le texte et l'arrière-plan
function ensureTextContrast(template: any) {
  try {
    // Si pas d'éléments, retourner le template tel quel
    if (!template.elements || !Array.isArray(template.elements)) {
      return template;
    }

    // Déterminer la couleur de fond dominante
    let bgColor = template.background_color || '#FFFFFF';
    const hasBgImage = !!template.background_image;

    // Pour les éléments de texte
    for (const element of template.elements) {
      if (element.type === 'text' && element.properties && element.properties.fill) {
        const textColor = element.properties.fill;

        // Si image de fond, utiliser du blanc pour une visibilité maximale
        if (hasBgImage) {
          // Utiliser du blanc pour tous les textes sur image de fond
          element.properties.fill = '#FFFFFF';

          // Supprimer complètement les contours et ombres moches
          delete element.properties.stroke;
          delete element.properties.strokeWidth;
          delete element.properties.shadowColor;
          delete element.properties.shadowBlur;
          delete element.properties.shadowOffset;
        }
        // Sans image de fond, vérifier le contraste avec la couleur de fond
        else {
          const isBgLight = isLightColor(bgColor);
          const isTextLight = isLightColor(textColor);

          // Si les deux couleurs sont claires ou foncées (faible contraste)
          if ((isBgLight && isTextLight) || (!isBgLight && !isTextLight)) {
            // Inverser la luminosité du texte par rapport au fond
            element.properties.fill = isBgLight ? '#333333' : '#F5F5F5';
          }

          // Supprimer les contours et ombres pour un design propre
          delete element.properties.stroke;
          delete element.properties.strokeWidth;
          delete element.properties.shadowColor;
          delete element.properties.shadowBlur;
          delete element.properties.shadowOffset;
        }
      }
    }

    return template;
  } catch (error) {
    console.error('Erreur lors de l\'ajustement du contraste:', error);
    return template; // Retourner le template original en cas d'erreur
  }
}

// Fonction d'aide pour déterminer si une couleur est claire
function isLightColor(color: string): boolean {
  try {
    // Si ce n'est pas une couleur hexadécimale valide, considérer comme foncée
    if (!color || !color.startsWith('#')) {
      return false;
    }

    // Convertir en RGB
    let r, g, b;

    // Format court #RGB
    if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16);
      g = parseInt(color[2] + color[2], 16);
      b = parseInt(color[3] + color[3], 16);
    }
    // Format long #RRGGBB
    else if (color.length === 7) {
      r = parseInt(color.substring(1, 3), 16);
      g = parseInt(color.substring(3, 5), 16);
      b = parseInt(color.substring(5, 7), 16);
    } else {
      return false;
    }

    // Calculer la luminance (formule standard)
    // Les humains perçoivent différemment les composantes R, G et B, d'où les coefficients
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Seuil de 0.5 pour déterminer si la couleur est claire
    return luminance > 0.5;
  } catch (error) {
    console.error('Erreur lors de la vérification de la luminance:', error);
    return false;
  }
}

// Fonction pour traiter les images (temporaires)
async function processTemporaryImage(
  tempImageUrl: string | undefined,
  userId: string,
  templateId: string,
  templateData: any, // Type plus précis si possible, ex: CardTemplateData
  imageType: 'element' | 'background',
  originalImageUrlVariable?: string // Pour mettre à jour la variable locale imageUrl/backgroundImageUrl
): Promise<{ finalUrl?: string; modified: boolean }> {
  if (!tempImageUrl || !tempImageUrl.includes('/api/storage-proxy/temp_moderation/')) {
    return { finalUrl: tempImageUrl, modified: false };
  }

  let modified = false;
  let finalUrl = tempImageUrl;

  try {
    const { uploadCardEditorImage } = require('../services/storage');
    const tempBucketUrl = '/api/storage-proxy/temp_moderation/';
    const tempFilePath = tempImageUrl.substring(tempImageUrl.indexOf(tempBucketUrl) + tempBucketUrl.length);

    if (tempFilePath) {
      const { data: fileData, error: downloadError } = await supabase.storage
        .from('temp_moderation')
        .download(tempFilePath);

      if (downloadError) {
        logger.error(`Erreur lors du téléchargement de l'image temporaire (${imageType}) depuis ${tempFilePath}:`, downloadError);
        return { finalUrl: tempImageUrl, modified: false }; // Retourner l'URL originale si le téléchargement échoue
      }

      if (fileData) {
        const buffer = Buffer.from(await fileData.arrayBuffer());
        const mimeType = fileData.type || 'image/jpeg';
        finalUrl = await uploadCardEditorImage(userId, buffer, mimeType, templateId, { addIaTag: false });

        if (imageType === 'element') {
          if (templateData && Array.isArray(templateData.elements)) {
            templateData.elements = templateData.elements.map((el: any) => {
              if (el.type === 'image' && el.properties && el.properties.src === tempImageUrl) {
                modified = true;
                return { ...el, properties: { ...el.properties, src: finalUrl } };
              }
              return el;
            });
          }
        } else if (imageType === 'background') {
          if (templateData) {
            templateData.background_image = finalUrl;
            modified = true;
          }
        }

        await supabase.storage.from('temp_moderation').remove([tempFilePath]);
        logger.info(`Image temporaire (${imageType}) ${tempFilePath} déplacée vers ${finalUrl} et supprimée de temp_moderation.`);
      }
    }
  } catch (err) {
    logger.error(`Erreur lors du traitement de l'image temporaire (${imageType}) ${tempImageUrl}:`, err);
    // En cas d'erreur, on retourne l'URL originale et modified: false pour ne pas casser le flux
    return { finalUrl: tempImageUrl, modified: false };
  }

  return { finalUrl, modified };
}