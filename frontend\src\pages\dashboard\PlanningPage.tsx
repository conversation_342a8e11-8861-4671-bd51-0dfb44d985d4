import React, { useState, useEffect } from 'react';
import { Paper, Button, IconButton, Typography, Box, Tooltip, CircularProgress, Avatar, Badge } from '@mui/material';
import { ChevronLeft, ChevronRight, Clock, Plus, EyeOff, Eye, List, Calendar as CalendarIcon, CalendarDays, Share2 } from 'lucide-react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isWeekend, getMonth, getYear, parseISO, isWithinInterval, isBefore, isAfter } from 'date-fns';
import { fr } from 'date-fns/locale';
import DOMPurify from 'dompurify';
import AddPlanningModal from '../../components/planning/AddPlanningModal';
import EditPlanningModal from '../../components/planning/EditPlanningModal';
import ConfirmationModal from '../../components/planning/ConfirmationModal';
import MissionRecapModal from '../../components/planning/MissionRecapModal';
import MissionListView from '../../components/planning/MissionListView';
import MissionFilterBar from '../../components/planning/MissionFilterBar';
import YearView from '../../components/planning/YearView';
import { notify } from '../../components/Notification';
import { Mission, PlanningFormData } from '../../types/planning';
import planningService from '../../services/planningService';
import { MissionFilters } from '../../components/planning/MissionFilterBar';
import ExportOptionsModal from '../../components/planning/ExportOptionsModal';
import { logger } from '@/utils/logger';
import { useAuth } from '../../contexts/AuthContext';
import AvertissementModalPremiumEtJobbeur from '../../components/AvertissementModalPremiumEtJobbeur';
import { api } from '../../services/api';
import { useMediaQuery } from '@mui/material';
import { SwipeOutlined } from '@mui/icons-material';

// Type de vue pour le planning
type ViewType = 'calendar' | 'list' | 'year';

const PlanningCalendar: React.FC = () => {
  const { user } = useAuth();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [missions, setMissions] = useState<Mission[]>([]);
  const [filteredMissions, setFilteredMissions] = useState<Mission[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [showHiddenMissions, setShowHiddenMissions] = useState(false);
  const [hiddenMissionsCount, setHiddenMissionsCount] = useState(0);
  const [restoringMissions, setRestoringMissions] = useState<Record<string, boolean>>({});
  const [isRecapModalOpen, setIsRecapModalOpen] = useState(false);
  const [missionForRecap, setMissionForRecap] = useState<Mission | null>(null);
  const [viewType, setViewType] = useState<ViewType>('calendar');
  const [filters, setFilters] = useState<MissionFilters>({
    searchTerm: '',
    status: [],
    jobber: [],
    minPrice: null,
    maxPrice: null,
    dateRange: {
      start: null,
      end: null
    }
  });
  const [isSyncGoogleLoading, setIsSyncGoogleLoading] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  // État pour le modal premium
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  // Vérifier si l'utilisateur est premium
  const [isPremium, setIsPremium] = useState<boolean>(false);
  const isSmallScreen = useMediaQuery('(max-width: 1200px)');

  useEffect(() => {
    // Vérifier si l'utilisateur est premium
    const checkPremiumStatus = async () => {
      try {
        // Les administrateurs ont tous les droits
        // if (user?.role === 'jobpadm') {
        //   setIsPremium(true);
        //   return;
        // }
        
        // Vérification du statut premium
        const response = await api.get('/api/subscriptions/status');
        setIsPremium(response.data?.plan === 'premium');
      } catch (error) {
        logger.error('Erreur lors de la vérification du statut premium:', error);
        setIsPremium(false);
      }
    };
    
    if (user) {
      checkPremiumStatus();
    }
  }, [user]);

  useEffect(() => {
    fetchMissions();
  }, [currentDate, showHiddenMissions, viewType]);

  useEffect(() => {
    // Mettre à jour les filtres quand currentDate change (navigation via flèches)
    setFilters(prev => ({
      ...prev,
      selectedMonth: getMonth(currentDate),
      selectedYear: getYear(currentDate)
    }));
  }, [currentDate]);

  useEffect(() => {
    // Appliquer les filtres aux missions déjà chargées
    if (missions.length > 0) {
      applyFilters();
    }
  }, [filters, missions]);

  const fetchMissions = async () => {
    try {
      setIsLoading(true);
      
      // Extraire le mois (1-12) et l'année de la date actuelle
      const month = getMonth(currentDate) + 1; // getMonth renvoie 0-11, donc +1
      const year = getYear(currentDate);
      
      // 1. Importer les missions acceptées (pour tous les utilisateurs)
      try {
        const importResult = await planningService.importMissions();
        
        // Afficher un message d'information sur l'importation si disponible
        if (importResult && importResult.message) {
          notify(DOMPurify.sanitize(importResult.message), 'info');
        }
        
        // Si une limite a été atteinte pour un utilisateur non-premium, afficher une notification
        if (importResult && importResult.limitReached) {
          notify('Vous avez atteint la limite de 2 missions pour les comptes gratuits. Passez à la version premium pour importer plus de missions.', 'warning');
        }
      } catch (importError: any) {
        // Si c'est une erreur 403 (limite atteinte), afficher le modal premium
        if (importError.response && importError.response.status === 403) {
          notify(importError.response.data?.error || 'Limite de missions atteinte pour les comptes gratuits', 'warning');
          setShowPremiumModal(true);
        } else {
          logger.error('Erreur lors de l\'importation des missions:', importError);
        }
      }
      
      // 2. Récupérer soit les missions visibles soit les missions masquées selon l'état
      if (showHiddenMissions) {
        let hiddenMissions;
        
        // Si on est en vue annuelle, récupérer toutes les missions masquées de l'année
        if (viewType === 'year') {
          hiddenMissions = await planningService.getHiddenMissionsByYear(year);
        } else {
          hiddenMissions = await planningService.getHiddenMissions(month, year);
        }
        
        // Sanitize les données avant affichage
        const sanitizedHiddenMissions = hiddenMissions?.map((mission: Mission) => ({
          ...mission,
          title: DOMPurify.sanitize(mission.title),
          description: mission.description ? DOMPurify.sanitize(mission.description) : undefined,
          mission: mission.mission ? {
            ...mission.mission,
            title: DOMPurify.sanitize(mission.mission.title),
            description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
          } : mission.mission,
          user: mission.user ? {
            ...mission.user,
            prenom: DOMPurify.sanitize(mission.user.prenom),
            nom: DOMPurify.sanitize(mission.user.nom)
          } : undefined
        })) || [];
        
        setMissions(sanitizedHiddenMissions);
        setFilteredMissions(sanitizedHiddenMissions);
        
        // Compter les missions masquées pour ce mois uniquement
        setHiddenMissionsCount(hiddenMissions?.length || 0);
      } else {
        let visibleMissions;
        
        // Si on est en vue annuelle, récupérer toutes les missions visibles de l'année
        if (viewType === 'year') {
          visibleMissions = await planningService.getMissionsByYear(year);
        } else {
          visibleMissions = await planningService.getMissions(month, year);
        }
        
        // Sanitize les données avant affichage
        const sanitizedVisibleMissions = visibleMissions?.map((mission: Mission) => ({
          ...mission,
          title: DOMPurify.sanitize(mission.title),
          description: mission.description ? DOMPurify.sanitize(mission.description) : undefined,
          mission: mission.mission ? {
            ...mission.mission,
            title: DOMPurify.sanitize(mission.mission.title),
            description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
          } : mission.mission,
          user: mission.user ? {
            ...mission.user,
            prenom: DOMPurify.sanitize(mission.user.prenom),
            nom: DOMPurify.sanitize(mission.user.nom)
          } : undefined
        })) || [];
        
        setMissions(sanitizedVisibleMissions);
        setFilteredMissions(sanitizedVisibleMissions);
        
        // Récupérer également le nombre de missions masquées pour l'afficher
        let hiddenMissions;
        if (viewType === 'year') {
          hiddenMissions = await planningService.getHiddenMissionsByYear(year);
        } else {
          hiddenMissions = await planningService.getHiddenMissions(month, year);
        }
        setHiddenMissionsCount(hiddenMissions?.length || 0);
      }
    } catch (error: any) {
      logger.info('Erreur lors de la récupération des missions:', error);
      
      // Vérifier si c'est une erreur Axios avec un statut 429
      if (error.response && error.response.status === 429 && error.response.data && error.response.data.error) {
        // Afficher le message spécifique de l'erreur 429
        notify(`${error.response.data.error}`, 'error');
      } else {
        // Message générique pour les autres erreurs
        notify('Erreur lors de la récupération du planning', 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Méthode pour appliquer les filtres aux missions
  const applyFilters = () => {
    // Si aucun filtre actif, montrer toutes les missions
    if (!filters.searchTerm && 
        filters.status.length === 0 && 
        filters.jobber.length === 0 && 
        filters.minPrice === null && 
        filters.maxPrice === null && 
        !filters.dateRange.start && 
        !filters.dateRange.end) {
      setFilteredMissions(missions);
      return;
    }

    // Sinon filtrer selon les critères
    const filtered = missions.filter(mission => {
      // 1. Filtre par texte (recherche générale)
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const matchesSearch = 
          mission.title.toLowerCase().includes(searchTerm) ||
          (mission.description || '').toLowerCase().includes(searchTerm) ||
          mission.date.includes(searchTerm) ||
          mission.start_time.includes(searchTerm) ||
          (mission.user?.prenom || '').toLowerCase().includes(searchTerm) ||
          (mission.user?.nom || '').toLowerCase().includes(searchTerm);
        
        if (!matchesSearch) return false;
      }

      // 2. Filtre par statut
      if (filters.status.length > 0) {
        const hasProposition = !!mission.proposition;
        const paymentStatus = mission.proposition?.payment_status;
        const propositionStatus = mission.proposition?.statut;

        // Gérer le cas spécial "Sans proposition"
        if (filters.status.includes('no_proposition') && hasProposition) {
          if (!filters.status.some(s => s === propositionStatus || s === paymentStatus)) {
            return false;
          }
        } 
        // Gérer tous les autres cas de statut
        else if (!filters.status.includes('no_proposition') && !hasProposition) {
          return false;
        }
        else if (hasProposition && !filters.status.some(s => s === propositionStatus || s === paymentStatus)) {
          return false;
        }
      }

      // 3. Filtre par jobbeur
      if (filters.jobber.length > 0) {
        if (!mission.proposition?.jobbeur_id || !filters.jobber.includes(mission.proposition.jobbeur_id)) {
          return false;
        }
      }

      // 4. Filtre par prix
      if (filters.minPrice !== null && mission.proposition?.montant_propose) {
        if (mission.proposition.montant_propose < filters.minPrice) {
          return false;
        }
      }
      if (filters.maxPrice !== null && mission.proposition?.montant_propose) {
        if (mission.proposition.montant_propose > filters.maxPrice) {
          return false;
        }
      }

      // 5. Filtre par plage de dates
      if (filters.dateRange.start || filters.dateRange.end) {
        const missionDate = parseISO(mission.date);
        
        if (filters.dateRange.start && filters.dateRange.end) {
          // Filtrer dans l'intervalle
          const startDate = parseISO(filters.dateRange.start);
          const endDate = parseISO(filters.dateRange.end);
          if (!isWithinInterval(missionDate, { start: startDate, end: endDate })) {
            return false;
          }
        } else if (filters.dateRange.start) {
          // Filtrer après une date
          const startDate = parseISO(filters.dateRange.start);
          if (isBefore(missionDate, startDate)) {
            return false;
          }
        } else if (filters.dateRange.end) {
          // Filtrer avant une date
          const endDate = parseISO(filters.dateRange.end);
          if (isAfter(missionDate, endDate)) {
            return false;
          }
        }
      }

      return true;
    });

    setFilteredMissions(filtered);
  };

  const handleFilterChange = (newFilters: MissionFilters) => {
    setFilters(newFilters);
    
    // Si le mois ou l'année ont changé dans les filtres, mettre à jour currentDate
    if (newFilters.selectedMonth !== undefined && newFilters.selectedYear !== undefined) {
      // Créer une nouvelle date avec le mois et l'année sélectionnés
      const newDate = new Date(newFilters.selectedYear, newFilters.selectedMonth, 1);
      
      // Seulement si la date a changé pour éviter des rechargements inutiles
      if (getMonth(newDate) !== getMonth(currentDate) || getYear(newDate) !== getYear(currentDate)) {
        setIsLoading(true);
        setCurrentDate(newDate);
        // Pas besoin d'appeler fetchMissions ici car le useEffect va s'en charger
      }
    }
  };

  const handlePreviousMonth = () => {
    setIsLoading(true);
    setCurrentDate(subMonths(currentDate, 1));
  };

  const handleNextMonth = () => {
    setIsLoading(true);
    setCurrentDate(addMonths(currentDate, 1));
  };

  const handleExportIcal = async () => {
    try {
      // Vérifier si l'utilisateur est premium
      if (!isPremium) {
        setShowPremiumModal(true);
        return;
      }
      
      const data = await planningService.exportIcal();
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'planning.ics');
      document.body.appendChild(link);
      link.click();
      link.remove();
      setIsExportModalOpen(false);
      notify('Planning exporté avec succès', 'success');
    } catch (error) {
      notify('Erreur lors de l\'export du planning', 'error');
    }
  };

  const handleSyncWithGoogleCalendar = async () => {
    try {
      // Vérifier si l'utilisateur est premium
      if (!isPremium) {
        setShowPremiumModal(true);
        return;
      }
      
      setIsSyncGoogleLoading(true);
      const response = await planningService.getGoogleCalendarSyncUrl();
      
      // Ouvrir l'URL de synchronisation Google Calendar dans un nouvel onglet
      const newWindow = window.open(response.googleCalendarUrl, '_blank');
      
      // Vérifier si la fenêtre s'est bien ouverte (peut être bloquée par un bloqueur de popups)
      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        notify('Impossible d\'ouvrir Google Calendar. Veuillez vérifier vos paramètres de bloqueur de popups.', 'warning');
      } else {
        setIsExportModalOpen(false);
        notify('Synchronisation avec Google Calendar initiée. Votre calendrier JobPartiel sera ajouté automatiquement à votre Google Calendar.', 'success');
      }
    } catch (error: any) {
      logger.info('Erreur lors de la synchronisation avec Google Calendar:', error);
      
      // Message d'erreur personnalisé en fonction du type d'erreur
      if (error.response?.status === 401) {
        notify('Vous devez être connecté pour synchroniser votre calendrier. Veuillez vous reconnecter.', 'error');
      } else if (error.response?.status === 403) {
        notify('Vous n\'avez pas l\'autorisation de synchroniser ce calendrier.', 'error');
      } else if (error.response?.status === 404) {
        notify('Le service de synchronisation calendrier est temporairement indisponible.', 'error');
      } else {
        notify('Erreur lors de la synchronisation avec Google Calendar. Veuillez réessayer.', 'error');
      }
    } finally {
      setIsSyncGoogleLoading(false);
    }
  };

  const handleOpenAddModal = (date?: Date) => {
    // L'utilisateur non-premium peut aussi ajouter des missions (max 2, vérifié côté backend)
    // Pas besoin d'afficher le modal premium ici
    if (date) {
      setSelectedDate(format(date, 'yyyy-MM-dd'));
    } else {
      setSelectedDate('');
    }
    setIsAddModalOpen(true);
  };

  const handleAddMission = async (data: PlanningFormData) => {
    try {
      // Convertir les heures en minutes pour faciliter la comparaison
      const getTimeAsMinutes = (time: string) => {
        if (!time) return 0; // Gérer les valeurs undefined ou vides
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
      };

      // Vérifier que l'heure de fin est après l'heure de début
      if (getTimeAsMinutes(data.end_time || '18:00') <= getTimeAsMinutes(data.start_time || '08:00')) {
        notify("L'heure de fin doit être postérieure à l'heure de début", 'error');
        return; // Arrêter le traitement si les heures sont invalides
      }

      setIsLoading(true);
      // Sanitize les données avant envoi
      const sanitizedData = {
        ...data,
        title: DOMPurify.sanitize(data.title),
        description: data.description ? DOMPurify.sanitize(data.description) : undefined
      };
      
      await planningService.addMission(sanitizedData);
      await fetchMissions();
      setIsAddModalOpen(false);
      setSelectedDate('');
      notify('Mission ajoutée au planning', 'success');
    } catch (error: any) {
      // Si c'est une erreur 403 (limite de missions atteinte), afficher le modal premium
      if (error.response && error.response.status === 403) {
        setIsAddModalOpen(false);
        setShowPremiumModal(true);
        notify(error.response.data?.error || 'Vous avez atteint la limite de missions pour les comptes gratuits', 'warning');
      } else {
        notify('Erreur lors de l\'ajout de la mission', 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditMission = async (id: string, data: PlanningFormData) => {
    try {
      // Convertir les heures en minutes pour faciliter la comparaison
      const getTimeAsMinutes = (time: string) => {
        if (!time) return 0; // Gérer les valeurs undefined ou vides
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
      };

      // Vérifier que l'heure de fin est après l'heure de début
      if (getTimeAsMinutes(data.end_time) <= getTimeAsMinutes(data.start_time)) {
        notify("L'heure de fin doit être postérieure à l'heure de début", 'error');
        return; // Arrêter le traitement si les heures sont invalides
      }

      setIsLoading(true);
      // Sanitize les données avant envoi
      const sanitizedData = {
        ...data,
        title: DOMPurify.sanitize(data.title),
        description: data.description ? DOMPurify.sanitize(data.description) : undefined
      };
      
      await planningService.updateMission(id, sanitizedData);
      await fetchMissions();
      setIsEditModalOpen(false);
      setSelectedMission(null);
      notify('Mission modifiée', 'success');
    } catch (error) {
      notify('Erreur lors de la modification de la mission', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDeleteConfirmation = () => {
    if (selectedMission) {
      setIsConfirmationModalOpen(true);
    }
  };

  const handleDeleteMission = async () => {
    if (!selectedMission) return;
    
    try {
      const id = selectedMission.id;
      const result = await planningService.deleteMission(id);
      
      // Message personnalisé selon le type d'action (suppression ou masquage)
      const mission = missions.find(m => m.id === id);
      if (result.isSoftDelete) {
        notify(`Mission "${mission?.title}" masquée du planning`, 'success');
      } else {
        notify(`Mission "${mission?.title}" supprimée du planning`, 'success');
      }
      
      // Fermer les modales
      setIsConfirmationModalOpen(false);
      setIsEditModalOpen(false);
      setSelectedMission(null);
      
      // Rafraîchir les données
      await fetchMissions();
      
    } catch (error: any) {
      // Affichage du message d'erreur précis fourni par le service
      const errorMessage = error.message || 'Erreur lors de la suppression de la mission';
      notify(errorMessage, 'error');
      
      // Fermer la modale en cas d'erreur 404 (mission non trouvée)
      if (error.message && error.message.includes('non trouvée')) {
        setIsConfirmationModalOpen(false);
        setIsEditModalOpen(false);
        setSelectedMission(null);
        // Rafraîchir quand même la liste pour être sûr
        fetchMissions();
      }
    }
  };

  const handleRestoreMission = async () => {
    if (!selectedMission) return;
    
    try {
      const id = selectedMission.id;
      const missionTitle = selectedMission.title; // Sauvegarder le titre pour la notification
      
      setRestoringMissions(prev => ({ ...prev, [id]: true }));
      
      // Restaurer la mission
      await planningService.restoreMission(id);
      
      // Extraire le mois et l'année actuels pour les appels API
      const month = getMonth(currentDate) + 1; // getMonth renvoie 0-11, donc +1
      const year = getYear(currentDate);
      
      // Mise à jour de l'interface utilisateur immédiatement
      if (showHiddenMissions) {
        // Si on est en mode "missions masquées", supprimer la mission de la liste locale
        setMissions(prev => prev.filter(m => m.id !== id));
        setFilteredMissions(prev => prev.filter(m => m.id !== id));
        
        // Mettre à jour le compteur de missions masquées
        const hiddenMissions = await planningService.getHiddenMissions(month, year);
        setHiddenMissionsCount(hiddenMissions?.length || 0);
        
        // Si c'était la dernière mission masquée, revenir à la vue normale
        if (hiddenMissions?.length === 0) {
          setShowHiddenMissions(false);
          
          // Charger les missions visibles
          const visibleMissions = await planningService.getMissions(month, year);
          if (visibleMissions) {
            const sanitizedVisibleMissions = visibleMissions.map(mission => ({
              ...mission,
              title: DOMPurify.sanitize(mission.title),
              description: mission.description ? DOMPurify.sanitize(mission.description) : undefined,
              mission: mission.mission ? {
                ...mission.mission,
                title: DOMPurify.sanitize(mission.mission.title),
                description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
              } : mission.mission,
              user: mission.user ? {
                ...mission.user,
                prenom: DOMPurify.sanitize(mission.user.prenom),
                nom: DOMPurify.sanitize(mission.user.nom)
              } : undefined
            }));
            
            setMissions(sanitizedVisibleMissions);
            // Réappliquer les filtres avec les nouvelles missions
            applyFilters();
          }
          
          notify(`Mission "${missionTitle}" restaurée. Retour à la vue normale car il n'y a plus de missions masquées.`, 'success');
        } else {
          // Notification de succès standard
          notify(`Mission "${missionTitle}" restaurée dans le planning`, 'success');
        }
      } else {
        // Si on est en mode normal, rafraîchir complètement les missions
        const [visibleMissions, hiddenMissions] = await Promise.all([
          planningService.getMissions(month, year),
          planningService.getHiddenMissions(month, year)
        ]);
        
        // Mettre à jour les missions visibles
        if (visibleMissions) {
          const sanitizedVisibleMissions = visibleMissions.map(mission => ({
            ...mission,
            title: DOMPurify.sanitize(mission.title),
            description: mission.description ? DOMPurify.sanitize(mission.description) : undefined,
            mission: mission.mission ? {
              ...mission.mission,
              title: DOMPurify.sanitize(mission.mission.title),
              description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
            } : mission.mission,
            user: mission.user ? {
              ...mission.user,
              prenom: DOMPurify.sanitize(mission.user.prenom),
              nom: DOMPurify.sanitize(mission.user.nom)
            } : undefined
          }));
          
          setMissions(sanitizedVisibleMissions);
          // Réappliquer les filtres avec les nouvelles missions
          applyFilters(); 
        }
        
        // Mettre à jour le compteur de missions masquées
        setHiddenMissionsCount(hiddenMissions?.length || 0);
        
        // Notification de succès
        notify(`Mission "${missionTitle}" restaurée dans le planning`, 'success');
      }
      
      // Fermer la modale
      setIsEditModalOpen(false);
      setSelectedMission(null);
      
    } catch (error: any) {
      // Affichage du message d'erreur précis
      const errorMessage = error.message || 'Erreur lors de la restauration de la mission';
      notify(errorMessage, 'error');
    } finally {
      if (selectedMission) {
        setRestoringMissions(prev => ({ ...prev, [selectedMission.id]: false }));
      }
    }
  };

  const toggleHiddenMissions = () => {
    setShowHiddenMissions(!showHiddenMissions);
  };

  const days = eachDayOfInterval({
    start: startOfMonth(currentDate),
    end: endOfMonth(currentDate)
  });

  const getMissionsForDay = (date: Date) => {
    if (!Array.isArray(filteredMissions)) return [];
    return filteredMissions.filter(mission => mission.date === format(date, 'yyyy-MM-dd'));
  };

  // Formater l'heure pour un affichage plus propre (enlever les secondes)
  const formatTime = (time: string) => {
    return time.substring(0, 5); // Garde seulement HH:MM
  };

  return (
    <Paper 
      className="p-8 overflow-hidden" 
      elevation={0}
      sx={{ 
        backgroundColor: '#fff',
        borderRadius: '16px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.05)',
      }}
    >
      {/* En-tête du planning - Nouvelle structure simplifiée */}
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 2,
        mb: 3
      }}>
        {/* Ligne 1: Mois/Année + Navigation mois + Vue */}
        <Box sx={{ 
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: { xs: 'wrap', md: 'nowrap' },
          gap: { xs: 1, md: 0 }
        }}>
          {/* Mois + Année */}
          <Box sx={{ 
            display: 'flex',
            alignItems: 'baseline',
            gap: 1
          }}>
            <Typography variant="h4" component="h1" sx={{ 
              fontWeight: 700,
              color: '#333',
              textTransform: 'capitalize',
              position: 'relative'
            }}>
              {format(currentDate, 'MMMM', { locale: fr })}
            </Typography>
            <Box sx={{ 
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              padding: '2px 8px',
              borderRadius: '8px',
              backgroundColor: 'rgba(255,248,243,0.8)',
              border: '1px solid rgba(255,107,44,0.1)'
            }}>
              <Typography sx={{ 
                fontSize: '1rem',
                color: '#666',
                fontWeight: 600
              }}>
                {format(currentDate, 'yyyy')}
              </Typography>
            </Box>
          </Box>

          {/* Navigation mois + Calendrier */}
          <Box sx={{ 
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            {/* Navigation mois */}
            <Box sx={{ 
              display: 'flex',
              alignItems: 'center',
              backgroundColor: '#f5f5f5',
              borderRadius: '12px',
              overflow: 'hidden',
            }}>
              <IconButton 
                onClick={handlePreviousMonth} 
                size="small" 
                sx={{ 
                  color: '#666',
                  borderRadius: '0',
                  padding: '8px 16px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C'
                  }
                }}
              >
                <ChevronLeft size={20} />
              </IconButton>
              <IconButton 
                onClick={handleNextMonth} 
                size="small"
                sx={{ 
                  color: '#666',
                  borderRadius: '0',
                  padding: '8px 16px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C'
                  }
                }}
              >
                <ChevronRight size={20} />
              </IconButton>
            </Box>

            {/* Type de vue */}
            <Box sx={{ 
              display: 'flex',
              alignItems: 'center',
              backgroundColor: '#f5f5f5',
              borderRadius: '12px',
              overflow: 'hidden'
            }}>
              <IconButton 
                onClick={() => setViewType('calendar')} 
                sx={{ 
                  color: viewType === 'calendar' ? '#FF6B2C' : '#666',
                  backgroundColor: viewType === 'calendar' ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                  borderRadius: '0',
                  padding: '8px 12px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C'
                  }
                }}
              >
                <CalendarIcon size={20} />
              </IconButton>
              <IconButton 
                onClick={() => setViewType('list')} 
                sx={{ 
                  color: viewType === 'list' ? '#FF6B2C' : '#666',
                  backgroundColor: viewType === 'list' ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                  borderRadius: '0',
                  padding: '8px 12px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C'
                  }
                }}
              >
                <List size={20} />
              </IconButton>
              <IconButton 
                onClick={() => setViewType('year')} 
                sx={{ 
                  color: viewType === 'year' ? '#FF6B2C' : '#666',
                  backgroundColor: viewType === 'year' ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                  borderRadius: '0',
                  padding: '8px 12px',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C'
                  }
                }}
              >
                <CalendarDays size={20} />
              </IconButton>
            </Box>
          </Box>
        </Box>

        {/* Info navigation (Optionnel - peut être masqué) */}
        <Box 
          sx={{ 
            display: { xs: 'none', sm: 'flex' }, // Masqué sur mobile
            alignItems: 'center',
            padding: '6px 10px',
            borderRadius: '8px',
            backgroundColor: 'rgba(255,107,44,0.04)',
            maxWidth: '600px',
            border: '1px solid rgba(255,107,44,0.08)',
            mb: 0.5
          }}
        >
          <Typography 
            variant="body2" 
            sx={{ 
              color: '#666', 
              fontSize: '0.8rem',
              fontStyle: 'italic'
            }}
          >
            Navigation mois par mois pour optimiser le chargement des données. Utilisez les flèches pour changer de mois.
          </Typography>
        </Box>

        {/* Notification limite pour utilisateur non-premium */}
        {!isPremium && !showHiddenMissions && missions.length > 0 && (
          <Box 
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '10px 14px',
              borderRadius: '8px',
              backgroundColor: 'rgba(255,248,243,0.9)',
              border: '1px solid rgba(255,107,44,0.2)',
              mb: 1
            }}
          >
            <Typography
              variant="body2"
              sx={{
                display: 'flex',
                alignItems: 'center',
                color: '#FF6B2C',
                fontWeight: 500,
                fontSize: '0.9rem'
              }}
            >
              <span role="img" aria-label="info" style={{ marginRight: '8px', fontSize: '1.1rem' }}>ℹ️</span>
              Vous utilisez {missions.length} mission{missions.length > 1 ? 's' : ''} sur les 2 gratuites à afficher. 
              <Button
                size="small"
                variant="text"
                onClick={() => setShowPremiumModal(true)}
                sx={{
                  color: '#FF6B2C',
                  fontWeight: 600,
                  textTransform: 'none',
                  ml: 1,
                  fontSize: '0.9rem',
                  '&:hover': {
                    backgroundColor: 'rgba(255,107,44,0.1)',
                  }
                }}
              >
                Passez à Premium
              </Button>
            </Typography>
          </Box>
        )}

        {/* Badge missions masquées */}
        {(hiddenMissionsCount > 0 || showHiddenMissions) && (
          <Badge 
            badgeContent={!showHiddenMissions ? hiddenMissionsCount : 0} 
            color="error"
            max={99}
            sx={{
              '& .MuiBadge-badge': {
                backgroundColor: '#FF6B2C',
                fontSize: '0.7rem',
              },
              alignSelf: 'flex-start',
              mb: 0.5
            }}
          >
            <Button
              variant="outlined"
              startIcon={showHiddenMissions ? <Eye size={16} /> : <EyeOff size={16} />}
              onClick={toggleHiddenMissions}
              disabled={isLoading}
              sx={{
                borderColor: showHiddenMissions ? '#FF6B2C' : '#ddd',
                color: showHiddenMissions ? '#FF6B2C' : '#666',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                  color: '#FF6B2C',
                },
                fontSize: '0.8rem',
                textTransform: 'none',
                fontWeight: 600,
                borderRadius: '10px',
                padding: '6px 12px',
                height: '36px'
              }}
            >
              {showHiddenMissions ? 'Missions visibles' : 'Missions masquées'}
            </Button>
          </Badge>
        )}

        {/* Ligne 2: Actions */}
        <Box sx={{ 
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' }, // Empiler sur mobile, côte à côte sur tablette/desktop
          gap: 1,
          mt: 0.5,
          width: '100%',
          maxWidth: '700px' // Limite la largeur du conteneur des boutons
        }}>
          <Button
            variant="outlined"
            startIcon={<Share2 size={16} />}
            onClick={() => {
              if (!isPremium) {
                setShowPremiumModal(true);
              } else {
                setIsExportModalOpen(true);
              }
            }}
            disabled={isLoading}
            sx={{
              borderColor: '#ddd',
              color: '#666',
              backgroundColor: '#fff',
              '&:hover': {
                borderColor: '#FF6B2C',
                backgroundColor: 'rgba(255, 107, 44, 0.04)',
                color: '#FF6B2C',
              },
              fontSize: '0.9rem',
              textTransform: 'none',
              fontWeight: 500,
              borderRadius: '12px',
              padding: '8px 16px',
              height: '40px',
              width: { xs: '100%', sm: 'auto' }, // Pleine largeur sur mobile, auto sur desktop
              minWidth: { sm: '200px' }, // Largeur minimale sur tablette/desktop
              flex: { sm: '0 1 auto' } // Ne pas étirer
            }}
          >
            Exporter / Synchroniser
          </Button>
          <Button
            variant="contained"
            onClick={() => handleOpenAddModal()}
            disabled={isLoading || showHiddenMissions}
            startIcon={<Plus size={16} />}
            sx={{
              backgroundColor: '#FF6B2C',
              '&:hover': {
                backgroundColor: '#FF7A35',
              },
              textTransform: 'none',
              fontSize: '0.9rem',
              fontWeight: 500,
              borderRadius: '12px',
              padding: '8px 16px',
              height: '40px',
              width: { xs: '100%', sm: 'auto' }, // Pleine largeur sur mobile, auto sur desktop
              minWidth: { sm: '200px' }, // Largeur minimale sur tablette/desktop
              flex: { sm: '0 1 auto' } // Ne pas étirer
            }}
          >
            Nouvelle mission
          </Button>
        </Box>
      </Box>

      {isLoading ? (
        <Box className="flex flex-col justify-center items-center h-[500px]" sx={{ color: '#666' }}>
          <CircularProgress size={40} sx={{ color: '#FF6B2C', mb: 2 }} />
          <Typography sx={{ color: '#666' }}>Chargement du planning...</Typography>
        </Box>
      ) : (
        <>
          {viewType === 'calendar' ? (
            /* La vue du calendrier déjà existante */
            <Box 
              className="overflow-hidden" 
              sx={{ 
                borderRadius: '16px',
                overflow: 'auto', // Permettre le défilement horizontal sur les petits écrans
                maxWidth: '100%',
                '&::-webkit-scrollbar': {
                  height: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#FF6B2C',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  backgroundColor: '#FF7A35',
                }
              }}
            >
              {showHiddenMissions && (
                <Box 
                  sx={{ 
                    backgroundColor: 'rgba(255, 107, 44, 0.08)', 
                    p: 2, 
                    mb: 2, 
                    borderRadius: '8px',
                    borderLeft: '4px solid #FF6B2C'
                  }}
                >
                  <Typography sx={{ fontWeight: 600 }}>
                    Mode d'affichage : Missions masquées ({missions.length})
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Vous consultez les missions qui ont été retirées du planning principal. Pour restaurer une mission, cliquez dessus.
                  </Typography>
                </Box>
              )}
              
              {/* Indicateur de défilement horizontal pour les petits écrans */}
              {isSmallScreen && (
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  padding: '10px 16px',
                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                  borderBottom: '1px solid #E2E8F0'
                }}>
                  <Box component={SwipeOutlined} fontSize="small" sx={{ color: '#FF6B2C', mr: 1 }} />
                  <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
                    Faites défiler horizontalement pour voir tout le calendrier
                  </Typography>
                </Box>
              )}
              
              {/* Barre de filtrage pour la vue calendrier également */}
              {!showHiddenMissions && (
                <MissionFilterBar
                  onFilterChange={handleFilterChange}
                  totalCount={missions.length}
                  filteredCount={filteredMissions.length}
                  initialMonth={getMonth(currentDate)}
                  initialYear={getYear(currentDate)}
                  jobbers={missions.reduce((acc, mission) => {
                    if (mission.proposition?.jobbeur_id && mission.user) {
                      const jobbeurId = mission.proposition.jobbeur_id;
                      const existingJobber = acc.find(j => j.id === jobbeurId);
                      
                      if (existingJobber) {
                        existingJobber.count++;
                      } else {
                        acc.push({
                          id: jobbeurId,
                          name: mission.user?.prenom && mission.user.nom 
                            ? `${mission.user.prenom} ${mission.user.nom}` 
                            : mission.user?.prenom || 'Jobbeur inconnu',
                          count: 1
                        });
                      }
                    }
                    return acc;
                  }, [] as { id: string; name: string; count: number }[])}
                  maxPossiblePrice={missions.reduce((max, mission) => {
                    const price = mission.proposition?.montant_propose || 0;
                    return price > max ? price : max;
                  }, 500)}
                />
              )}
              
              <Box 
                className="grid grid-cols-7 gap-0" 
                sx={{ 
                  backgroundColor: '#f9f9f9',
                  borderRadius: '16px 16px 0 0',
                  overflow: 'hidden',
                  minWidth: { xs: '800px', md: 'auto' }, // Augmentation de la largeur minimale
                  '& > div': {
                    minWidth: { xs: '110px', sm: '120px', md: 'auto' } // Cellules plus larges
                  }
                }}
              >
                {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map(day => (
                  <Box 
                    key={day} 
                    className="py-3 text-center font-semibold"
                    sx={{ 
                      color: day === 'Sam' || day === 'Dim' ? '#FF965E' : '#666',
                      borderBottom: '1px solid #eee',
                      backdropFilter: 'blur(8px)',
                      fontSize: { xs: '0.8rem', sm: '0.85rem' }, // Taille de police légèrement augmentée
                      letterSpacing: '0.5px'
                    }}
                  >
                    {day}
                  </Box>
                ))}
              </Box>

              <Box 
                className="grid grid-cols-7 grid-auto-rows-fr gap-0"
                sx={{ 
                  minWidth: { xs: '800px', md: 'auto' }, // Cohérence avec l'en-tête
                  '& > div': {
                    minWidth: { xs: '110px', sm: '120px', md: 'auto' } // Cohérence avec l'en-tête
                  }
                }}
              >
                {days.map(day => {
                  const dayMissions = getMissionsForDay(day);
                  const isWeekendDay = isWeekend(day);
                  const isCurrentMonth = isSameMonth(day, currentDate);
                  
                  return (
                    <Box
                      key={day.toString()}
                      className="relative"
                      sx={{
                        backgroundColor: !isCurrentMonth 
                          ? '#fafafa' 
                          : isWeekendDay 
                            ? 'rgba(255, 228, 186, 0.05)' 
                            : '#fff',
                        borderRight: '1px solid #f0f0f0',
                        borderBottom: '1px solid #f0f0f0',
                        minHeight: { xs: '110px', sm: '130px' }, // Cellules plus hautes
                        transition: 'all 0.2s ease',
                        cursor: showHiddenMissions ? 'default' : 'pointer',
                        '&:hover': {
                          backgroundColor: showHiddenMissions ? undefined : 'rgba(255, 107, 44, 0.02)'
                        }
                      }}
                      onClick={showHiddenMissions ? undefined : (e) => {
                        e.preventDefault();
                        if (!isPremium) {
                          setShowPremiumModal(true);
                        } else {
                          handleOpenAddModal(day);
                        }
                      }}
                    >
                      <Box sx={{ 
                        position: 'relative', 
                        padding: { xs: '8px', sm: '10px' }, // Plus de padding
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                      }}>
                        <Box 
                          sx={{
                            position: 'absolute',
                            top: { xs: '6px', sm: '8px' },
                            right: { xs: '6px', sm: '8px' },
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: isToday(day) ? { xs: '24px', sm: '26px' } : 'auto', // Légèrement plus grand
                            height: isToday(day) ? { xs: '24px', sm: '26px' } : 'auto',
                            borderRadius: '50%',
                            backgroundColor: isToday(day) ? '#FF6B2C' : 'transparent',
                            zIndex: 1
                          }}
                        >
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontWeight: isToday(day) ? 700 : 500,
                              color: isToday(day) 
                                ? '#fff' 
                                : !isCurrentMonth 
                                  ? '#aaa' 
                                  : isWeekendDay 
                                    ? '#FF965E' 
                                    : '#555',
                              fontSize: { xs: '0.8rem', sm: '0.85rem' } // Plus grand
                            }}
                          >
                            {format(day, 'd')}
                          </Typography>
                        </Box>
                        
                        <Box className="mt-7 space-y-2"> {/* Augmenté l'espacement */}
                          {dayMissions.slice(0, 3).map(mission => {
                            // Déterminer si c'est une mission manuelle ou JobPartiel
                            const isManualMission = !mission.mission?.id;
                            
                            // Deux couleurs distinctes selon le type de mission
                            const getColorVariant = () => {
                              // Si on affiche les missions masquées, utiliser des tons grisés
                              if (showHiddenMissions) {
                                return { 
                                  bg: 'rgba(120, 120, 120, 0.1)', 
                                  border: '#999', 
                                  text: '#777' 
                                };
                              }
                              
                              // Vérifier si c'est une mission avec offre acceptée
                              if (mission.proposition?.statut === 'acceptée') {
                                return {
                                  // Mission avec offre acceptée - Vert
                                  bg: 'rgba(76, 175, 80, 0.08)',
                                  border: '#4CAF50',
                                  text: '#4CAF50'
                                };
                              }
                              
                              // Sinon, choisir selon le type de mission
                              return isManualMission
                                ? { 
                                    // Mission manuelle - Orange (couleur primaire de l'app)
                                    bg: 'rgba(255, 107, 44, 0.08)', 
                                    border: '#FF6B2C', 
                                    text: '#FF6B2C' 
                                  }
                                : { 
                                    // Mission JobPartiel - Bleu
                                    bg: 'rgba(25, 118, 210, 0.08)', 
                                    border: '#1976d2', 
                                    text: '#1976d2' 
                                  };
                            };
                            
                            const colors = getColorVariant();
                            
                            return (
                              <Tooltip 
                                key={mission.id} 
                                title={showHiddenMissions ? "Voir détails de la mission masquée" : "Cliquer pour éditer la mission"} 
                                arrow
                                placement="top"
                              >
                                <Box
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Ouvrir l'édition dans tous les cas (comme avant)
                                    setSelectedMission(mission);
                                    setIsEditModalOpen(true);
                                  }}
                                  onContextMenu={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setSelectedMission(mission);
                                    setIsEditModalOpen(true);
                                  }}
                                  sx={{
                                    padding: { xs: '6px 8px', sm: '6px 8px' }, // Augmenté le padding
                                    backgroundColor: colors.bg,
                                    borderRadius: '6px',
                                    marginBottom: '4px', // Plus d'espace entre les missions
                                    borderLeft: `3px solid ${colors.border}`,
                                    boxShadow: '0 1px 3px rgba(0,0,0,0.03)',
                                    cursor: 'pointer',
                                    transition: 'all 0.15s ease',
                                    opacity: showHiddenMissions ? 0.7 : 1,
                                    '&:hover': {
                                      transform: 'translateY(-1px) scale(1.01)',
                                      boxShadow: '0 3px 6px rgba(0,0,0,0.06)',
                                      opacity: 1
                                    },
                                    position: 'relative'
                                  }}
                                >
                                  {/* Avatar du propriétaire - Visible sur plus d'écrans maintenant */}
                                  {mission.user && (
                                    <Box 
                                      sx={{ 
                                        position: 'absolute',
                                        top: '-8px',
                                        right: '-5px',
                                        zIndex: 2,
                                        display: { xs: 'block', sm: 'block' } // Visible sur tous les écrans
                                      }}
                                    >
                                      <Avatar
                                        src={mission.user.photo_url}
                                        alt={`${mission.user.prenom || ''}`}
                                        sx={{
                                          width: { xs: 20, sm: 22 },
                                          height: { xs: 20, sm: 22 },
                                          border: '1px solid #fff',
                                          fontSize: '0.6rem',
                                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                        }}
                                      />
                                    </Box>
                                  )}
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Typography 
                                      noWrap
                                      sx={{ 
                                        color: colors.text, 
                                        fontWeight: 600, 
                                        fontSize: { xs: '0.7rem', sm: '0.75rem' }, // Taille augmentée
                                        maxWidth: '100%',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis'
                                      }}
                                    >
                                      {mission.title}
                                      {showHiddenMissions && <EyeOff size={10} style={{ display: 'inline', marginLeft: '4px' }} />}
                                    </Typography>
                                  </Box>
                                  
                                  <Box 
                                    sx={{ 
                                      display: 'flex', 
                                      flexDirection: 'column',
                                      justifyContent: 'space-between',
                                      mt: { xs: 0.3, sm: 0.5 },
                                      color: '#777',
                                      fontSize: { xs: '0.65rem', sm: '0.7rem' }
                                    }}
                                  >
                                    <Box sx={{ 
                                      display: 'flex', 
                                      justifyContent: 'space-between',
                                      alignItems: 'center',
                                      width: '100%'
                                    }}>
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <Clock size={10} style={{ marginRight: '4px', color: '#999' }} />
                                        {`${formatTime(mission.start_time)} - ${formatTime(mission.end_time)}`}
                                      </Box>
                                      
                                      {/* Nom du propriétaire - Masqué uniquement sur très petits écrans */}
                                      {mission.user && (
                                        <Typography 
                                          variant="caption" 
                                          sx={{ 
                                            fontSize: '0.65rem', 
                                            color: '#888',
                                            display: { xs: 'none', sm: 'flex' },
                                            alignItems: 'center'
                                          }}
                                        >
                                          {mission.user.prenom} {mission.user.nom ? mission.user.nom.charAt(0).toUpperCase() + '.' : ''}
                                        </Typography>
                                      )}
                                    </Box>

                                    {/* Affichage du montant négocié sur une ligne à part */}
                                    {((mission.montant_propose && mission.montant_propose > 0) || mission.proposition?.montant_propose) && (
                                      <Box 
                                        sx={{ 
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'flex-start',
                                          mt: 0.5
                                        }}
                                      >
                                        <Box 
                                          sx={{ 
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                            borderRadius: '4px',
                                            padding: '1px 4px',
                                            color: '#FF6B2C',
                                            fontWeight: 600,
                                            fontSize: { xs: '0.65rem', sm: '0.7rem' }
                                          }}
                                        >
                                          {/* Priorité au montant_propose SEULEMENT s'il existe et est > 0 */}
                                          {(mission.montant_propose && mission.montant_propose > 0) 
                                            ? mission.montant_propose 
                                            : mission.proposition?.montant_propose} {(mission.payment_method === 'direct_only' || mission.proposition?.payment_method === 'direct_only') ? '€' : 'J'}
                                        </Box>
                                      </Box>
                                    )}
                                  </Box>
                                </Box>
                              </Tooltip>
                            );
                          })}
                          
                          {/* Indicateur pour les missions supplémentaires - Style amélioré */}
                          {dayMissions.length > 3 && (
                            <Box
                              sx={{
                                padding: '3px 8px',
                                borderRadius: '6px',
                                backgroundColor: 'rgba(255, 107, 44, 0.06)', // Couleur orange très légère
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                cursor: 'pointer',
                                boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                                border: '1px solid rgba(255, 107, 44, 0.1)',
                                '&:hover': {
                                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                }
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                // Rediriger vers la vue liste au lieu d'ouvrir la modale d'édition
                                setViewType('list');
                                // Définir un filtre pour n'afficher que les missions de ce jour
                                const dateString = format(day, 'yyyy-MM-dd');
                                setFilters(prev => ({
                                  ...prev,
                                  dateRange: {
                                    start: dateString,
                                    end: dateString
                                  }
                                }));
                              }}
                            >
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  fontSize: '0.7rem', // Plus grande
                                  color: '#FF6B2C', // Couleur orange
                                  fontWeight: 600
                                }}
                              >
                                +{dayMissions.length - 3} autres
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </Box>
                  );
                })}
              </Box>

              {/* Légende des codes couleur - En bas du calendrier */}
              {!showHiddenMissions && viewType === 'calendar' && (
                <Box 
                  sx={{ 
                    display: 'flex', 
                    flexDirection: { xs: 'column', sm: 'row' },
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    gap: { xs: 1.5, sm: 2 },
                    p: 2,
                    mt: 2,
                    mb: 2,
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                    borderRadius: '8px',
                    width: '100%'
                  }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600, color: '#555', mr: 'auto', mb: { xs: 1, sm: 0 } }}>
                    Légende des couleurs :
                  </Typography>
                  
                  <Box 
                    sx={{ 
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      gap: { xs: 1, sm: 2 },
                      width: { xs: '100%', sm: 'auto' }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box 
                        sx={{ 
                          width: '16px', 
                          height: '16px', 
                          backgroundColor: 'rgba(255, 107, 44, 0.08)', 
                          borderLeft: '3px solid #FF6B2C',
                          borderRadius: '3px'
                        }} 
                      />
                      <Typography variant="body2" sx={{ color: '#555' }}>
                        Mission manuelle
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box 
                        sx={{ 
                          width: '16px', 
                          height: '16px', 
                          backgroundColor: 'rgba(25, 118, 210, 0.08)', 
                          borderLeft: '3px solid #1976d2',
                          borderRadius: '3px'
                        }} 
                      />
                      <Typography variant="body2" sx={{ color: '#555' }}>
                        Mission JobPartiel
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box 
                        sx={{ 
                          width: '16px', 
                          height: '16px', 
                          backgroundColor: 'rgba(76, 175, 80, 0.08)', 
                          borderLeft: '3px solid #4CAF50',
                          borderRadius: '3px'
                        }} 
                      />
                      <Typography variant="body2" sx={{ color: '#555' }}>
                        Mission avec offre acceptée
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              )}

              {/* Indication du filtre actif s'il y en a un */}
              {(filters.searchTerm || 
                filters.status.length > 0 || 
                filters.jobber.length > 0 || 
                filters.minPrice !== null || 
                filters.maxPrice !== null || 
                filters.dateRange.start || 
                filters.dateRange.end) && (
                <Box sx={{ mt: 2, p: 2, backgroundColor: 'rgba(255, 107, 44, 0.08)', borderRadius: '8px' }}>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    <strong>Filtres actifs :</strong> Affichage de {filteredMissions.length} mission{filteredMissions.length !== 1 ? 's' : ''} sur {missions.length}
                    <Button 
                      size="small" 
                      onClick={() => setFilters({
                        searchTerm: '',
                        status: [],
                        jobber: [],
                        minPrice: null,
                        maxPrice: null,
                        dateRange: {
                          start: null,
                          end: null
                        }
                      })}
                      sx={{ 
                        ml: 1, 
                        color: '#FF6B2C', 
                        fontSize: '0.75rem',
                        textTransform: 'none'
                      }}
                    >
                      Réinitialiser les filtres
                    </Button>
                  </Typography>
                </Box>
              )}
            </Box>
          ) : viewType === 'list' ? (
            /* La vue liste avec passage des filtres */
            <MissionListView 
              missions={missions} 
              onEditMission={(mission) => {
                setSelectedMission(mission);
                setIsEditModalOpen(true);
              }}
              onClose={() => setViewType('calendar')}
              onFilterChange={handleFilterChange}
              filters={filters}
            />
          ) : (
            /* La nouvelle vue annuelle */
            <YearView
              missions={missions}
              isLoading={isLoading}
              onDateChange={setCurrentDate}
              currentDate={currentDate}
              filters={filters}
              onFilterChange={handleFilterChange}
              onViewChange={setViewType}
            />
          )}
        </>
      )}

      {/* Modal d'ajout de mission */}
      {isAddModalOpen && (
        <AddPlanningModal
          open={isAddModalOpen}
          onClose={() => {
            setIsAddModalOpen(false);
            setSelectedDate('');
          }}
          onSubmit={handleAddMission}
          selectedMissionId={selectedMission?.mission?.id || undefined}
          selectedDate={selectedDate}
        />
      )}

      {/* Modal d'édition ou visualisation de mission (selon l'état masqué ou non) */}
      {isEditModalOpen && selectedMission && (
        <EditPlanningModal
          open={isEditModalOpen}
          mission={selectedMission}
          isHidden={showHiddenMissions}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedMission(null);
          }}
          onSubmit={(data) => handleEditMission(selectedMission.id, data)}
          onDelete={handleOpenDeleteConfirmation}
          onRestore={handleRestoreMission}
          onOpenRecap={() => {
            setMissionForRecap(selectedMission);
            setIsRecapModalOpen(true);
          }}
        />
      )}

      {/* Modal de confirmation de suppression/masquage */}
      {isConfirmationModalOpen && selectedMission && (
        <ConfirmationModal
          open={isConfirmationModalOpen}
          onClose={() => setIsConfirmationModalOpen(false)}
          onConfirm={handleDeleteMission}
          title={!selectedMission.mission?.id ? "Confirmer la suppression" : "Confirmer le masquage"}
          message={
            !selectedMission.mission?.id 
              ? `Êtes-vous sûr de vouloir supprimer définitivement la mission "${selectedMission.title}"?`
              : `Êtes-vous sûr de vouloir masquer la mission "${selectedMission.title}" du planning?`          }
          confirmLabel={!selectedMission.mission?.id ? "Supprimer" : "Masquer"}
          isManualMission={!selectedMission.mission?.id}
        />
      )}

      {/* Modal de récapitulatif de mission */}
      {isRecapModalOpen && missionForRecap && (
        <MissionRecapModal
          open={isRecapModalOpen}
          mission={missionForRecap}
          onClose={() => {
            setIsRecapModalOpen(false);
            setMissionForRecap(null);
          }}
        />
      )}

      {/* Export Options Modal */}
      <ExportOptionsModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        onExportIcal={handleExportIcal}
        onSyncWithGoogle={handleSyncWithGoogleCalendar}
        isSyncGoogleLoading={isSyncGoogleLoading}
      />

      {/* Modal pour avertissement premium */}
      <AvertissementModalPremiumEtJobbeur
        isOpen={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        type="premium"
      />
    </Paper>
  );
};

export default PlanningCalendar; 

