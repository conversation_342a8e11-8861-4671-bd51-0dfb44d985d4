import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { sendReviewPostedEmail, sendReviewReceivedEmail, sendReviewReplyEmail, sendReviewDeletedEmail } from '../services/emailService';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';
import contentModerationService from '../services/contentModerationService';
import { uploadReviewPhotos, deleteReviewPhotos } from '../services/storage';

// Constantes
const JOBI_REWARD_REVIEW = 1; // Récompense en Jobi pour un avis
const CACHE_TTL = 3600; // 1 heure en secondes
const CACHE_KEYS = {
  USER_REVIEWS: (userId: string, page: number = 0, limit: number = 0) => 
    page && limit ? `reviews:user:${userId}:page:${page}:limit:${limit}` : `reviews:user:${userId}`,
  REVIEW_DETAIL: (reviewId: string) => `reviews:detail:${reviewId}`,
  USER_REVIEWS_STATS: (userId: string) => `reviews:stats:${userId}`,
  USER_SENT_REVIEWS: (userId: string) => `reviews:sent:${userId}`,
  USER_QUALITES_STATS: (userId: string) => `reviews:qualites:${userId}`,
  REVIEW_RESPONSES: (reviewId: string) => `reviews:responses:${reviewId}`
};

// Fonction pour invalider le cache lié à un utilisateur
const invalidateUserCache = async (userId: string, targetUserId?: string) => {
  const pageSizes = [5, 10, 20];
  const keysToDelete = [];

  // Ajouter les clés de base
  keysToDelete.push(
    CACHE_KEYS.USER_REVIEWS(userId),
    CACHE_KEYS.USER_REVIEWS_STATS(userId),
    CACHE_KEYS.USER_SENT_REVIEWS(userId),
    CACHE_KEYS.USER_QUALITES_STATS(userId)
  );

  // Ajouter les clés de pagination
  for (const size of pageSizes) {
    for (let page = 1; page <= 10; page++) {
      keysToDelete.push(CACHE_KEYS.USER_REVIEWS(userId, page, size));
    }
  }

  if (targetUserId && targetUserId !== userId) {
    // Ajouter les clés de base pour la cible
    keysToDelete.push(
      CACHE_KEYS.USER_REVIEWS(targetUserId),
      CACHE_KEYS.USER_REVIEWS_STATS(targetUserId),
      CACHE_KEYS.USER_SENT_REVIEWS(targetUserId),
      CACHE_KEYS.USER_QUALITES_STATS(targetUserId)
    );

    // Ajouter les clés de pagination pour la cible
    for (const size of pageSizes) {
      for (let page = 1; page <= 10; page++) {
        keysToDelete.push(CACHE_KEYS.USER_REVIEWS(targetUserId, page, size));
      }
    }
  }

  // Supprimer toutes les clés en une seule opération
  if (keysToDelete.length > 0) {
    await redis.del(...keysToDelete);
    logger.info('Cache avis : Invalidation complète pour les utilisateurs', { 
      userId, 
      targetUserId,
      keysCount: keysToDelete.length 
    });
  }
};

// Fonction pour invalider le cache d'un avis spécifique
const invalidateReviewCache = async (reviewId: string) => {
  const keysToDelete = [
    CACHE_KEYS.REVIEW_DETAIL(reviewId),
    CACHE_KEYS.REVIEW_RESPONSES(reviewId)
  ];
  await redis.del(...keysToDelete);
  logger.info('Cache avis : Invalidation des détails et réponses', { reviewId });
};

export { invalidateUserCache };
export { invalidateReviewCache };

export const reviewsController = {
  // Créer un nouvel avis
  createReview: async (req: Request, res: Response, next: Function) => {
    try {
      const { note, commentaire, mission_id, qualites, defauts } = req.body;
      const authorId = req.user?.userId;

      if (!authorId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que la candidature existe et est acceptée
      let { data: candidature, error: candidatureError } = await supabase
        .from('user_mission_candidature')
        .select(`
          *,
          mission:user_missions!user_mission_candidature_mission_id_fkey (
            user_id
          )
        `)
        .eq('mission_id', mission_id)
        .eq('jobbeur_id', authorId)
        .single();

      if (candidatureError || !candidature) {
        // Si pas de candidature en tant que jobbeur, vérifier si c'est le client de la mission
        const { data: mission, error: missionError } = await supabase
          .from('user_missions')
          .select('user_id')
          .eq('id', mission_id)
          .eq('user_id', authorId)
          .single();

        if (missionError || !mission) {
          console.log("Erreur détaillée:", candidatureError || missionError);
          return res.status(400).json({
            success: false,
            message: 'Vous n\'êtes pas autorisé à donner un avis pour cette mission',
            error: candidatureError?.message || missionError?.message
          });
        }

        // L'utilisateur est le client de la mission, chercher une candidature acceptée
        const { data: acceptedCandidature, error: acceptedError } = await supabase
          .from('user_mission_candidature')
          .select(`
            *,
            mission:user_missions!user_mission_candidature_mission_id_fkey (
              user_id
            )
          `)
          .eq('mission_id', mission_id)
          .eq('statut', 'acceptée')
          .single();

        if (acceptedError || !acceptedCandidature) {
          console.log("Aucune candidature acceptée trouvée pour cette mission");
          return res.status(400).json({
            success: false,
            message: 'Aucune candidature acceptée trouvée pour cette mission'
          });
        }

        candidature = acceptedCandidature;
      } else {
        // Vérifier que la candidature du jobbeur est acceptée
        if (candidature.statut !== 'acceptée') {
          return res.status(400).json({
            success: false,
            message: 'Vous ne pouvez donner un avis que pour une candidature acceptée'
          });
        }
      }

      // Vérifier que l'utilisateur est bien le client de cette candidature ou le jobbeur
      if (!candidature.mission?.user_id) {
        console.log("Pas d'user_id dans la mission");
        return res.status(400).json({
          success: false,
          message: 'Impossible de vérifier le client de la mission'
        });
      }

      // Vérifier que l'utilisateur est soit le client de la mission, soit le jobbeur de la candidature
      if (candidature.mission.user_id !== authorId && candidature.jobbeur_id !== authorId) {
        console.log("IDs ne correspondent pas:", {
          missionUserId: candidature.mission.user_id,
          jobbeurId: candidature.jobbeur_id,
          authorId: authorId
        });
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez pas donner un avis pour cette candidature'
        });
      }

      // Déterminer le target_user_id en fonction de qui donne l'avis
      const target_user_id = authorId === candidature.mission.user_id 
        ? candidature.jobbeur_id 
        : candidature.mission.user_id;

      // Vérifier qu'un utilisateur ne peut pas s'auto-évaluer
      if (authorId === target_user_id) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez pas vous auto-évaluer'
        });
      }

      // Vérifier que l'utilisateur n'a pas déjà donné un avis pour cette candidature
      const { data: existingReview } = await supabase
        .from('user_reviews')
        .select('id')
        .eq('author_id', authorId)
        .eq('mission_id', mission_id)
        .single();

      if (existingReview) {
        return res.status(400).json({
          success: false,
          message: 'Vous avez déjà donné un avis pour cette candidature'
        });
      }

      // Modération automatique par IA du contenu de l'avis
      if (commentaire && commentaire.trim()) {
        try {
          // Préparer le contenu à modérer (commentaire + qualités + défauts)
          let contentToModerate = commentaire.trim();
          if (qualites && qualites.length > 0) {
            contentToModerate += ' Qualités: ' + qualites.join(', ');
          }
          if (defauts && defauts.length > 0) {
            contentToModerate += ' Points à améliorer: ' + defauts.join(', ');
          }

          const moderationResult = await contentModerationService.moderateContent({
            text: contentToModerate,
            type: 'review',
            contentId: `temp-review-${Date.now()}`,
            userId: authorId
          });

          if (!moderationResult.isSafe) {
            logger.warn('Avis détecté comme inapproprié par la modération automatique', {
              userId: authorId,
              missionId: mission_id,
              score: moderationResult.score,
              categories: moderationResult.categories
            });

            return res.status(400).json({
              error: 'Votre avis contient du contenu inapproprié et ne peut pas être publié.',
              message: 'Votre avis contient du contenu inapproprié et ne peut pas être publié.',
              success: false,
              toastType: 'error'
            });
          }
        } catch (moderationError) {
          // En cas d'erreur de modération, on log mais on continue
          logger.error('Erreur lors de la modération de l\'avis:', moderationError);
        }
      }

      // Créer l'avis
      const { data: review, error: reviewError } = await supabase
        .from('user_reviews')
        .insert([
          {
            author_id: authorId,
            target_user_id,
            note,
            commentaire,
            mission_id,
            qualites,
            defauts,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (reviewError) {
        logger.error('Erreur lors de la création de l\'avis:', reviewError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la création de l\'avis',
          error: reviewError.message
        });
      }

      if (!authorId || !target_user_id) {
        return res.status(400).json({
          success: false,
          message: 'Identifiant auteur ou cible manquant pour la création de l\'avis.'
        });
      }
      // Journaliser l'action de l'utilisateur
      logUserActivity(
        authorId || '',
        'review_create',
        review?.id,
        'review',
        { 
          mission_id,
          target_user_id,
          note
        },
        getIpFromRequest(req)
      );

      // Invalider le cache pour l'auteur et la cible
      await invalidateUserCache(authorId, target_user_id);

      // Récupérer les informations de l'auteur et de la cible
      const { data: author, error: authorError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', authorId)
        .single();

      const { data: targetUser, error: targetError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', target_user_id)
        .single();

      if (authorError || targetError) {
        logger.error('Erreur lors de la récupération des utilisateurs:', { authorError, targetError });
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations utilisateurs'
        });
      }

      // Déchiffrer les données des utilisateurs
      const decryptedAuthor = author ? await decryptUserDataAsync(author) : null;
      const decryptedTargetUser = targetUser ? await decryptUserDataAsync(targetUser) : null;

      // Déchiffrer les profils
      if (decryptedAuthor?.profil && Array.isArray(decryptedAuthor.profil)) {
        decryptedAuthor.profil = await Promise.all(decryptedAuthor.profil.map((p: any) => decryptProfilDataAsync(p)));
      }
      if (decryptedTargetUser?.profil && Array.isArray(decryptedTargetUser.profil)) {
        decryptedTargetUser.profil = await Promise.all(decryptedTargetUser.profil.map((p: any) => decryptProfilDataAsync(p)));
      }

      // Ajouter JOBI_REWARD_REVIEW jobi à l'auteur de l'avis
      const { data: userJobi } = await supabase
        .from('user_jobi')
        .select('montant')
        .eq('user_id', authorId)
        .single();

      const newAmount = (userJobi?.montant || 0) + JOBI_REWARD_REVIEW;

      const { error: updateJobiError } = await supabase
        .from('user_jobi')
        .update({ montant: newAmount })
        .eq('user_id', authorId)
        .single();

      if (!updateJobiError) {
        await supabase
          .from('user_jobi_historique')
          .insert({
            user_id: authorId,
            montant: JOBI_REWARD_REVIEW,
            titre: 'Récompense avis',
            description: 'Récompense pour avoir déposé un avis',
          });

        // Invalider tous les caches liés aux Jobi
        const cacheKeys = [
          `jobi_balance_${authorId}`,
          `historique-jobi:${authorId}:0`, // Premier offset
          `historique-jobi:${authorId}:10`, // Deuxième offset possible
          `jobi-monthly-stats:${authorId}`
        ];

        await Promise.all(cacheKeys.map(key => redis.del(key)));

        logger.info('Cache Redis invalidé après transaction Jobi:', { 
          authorId,
          cacheKeys,
          montant: JOBI_REWARD_REVIEW,
          newAmount
        });
      }

      // Récupérer les détails de la mission
      const { data: missionDetails, error: missionError } = await supabase
        .from('user_missions')
        .select(`
          id,
          titre,
          category_id,
          subcategory_id
        `)
        .eq('id', mission_id)
        .single();

      if (missionError) {
        logger.error('Erreur lors de la récupération des détails de la mission:', missionError);
        return next(missionError);
      }

      // Envoyer une notification à l'utilisateur qui reçoit l'avis
      const notificationData = {
        user_id: target_user_id,
        type: 'profile',
        title: '⭐ Nouvel avis reçu',
        content: `
          <div style="font-family: Arial, sans-serif; color: #333;">
            <p style="margin-bottom: 10px;">
              Pour la mission <strong style="color: #FF6B2C;">${missionDetails?.titre || 'Non spécifiée'}</strong>
            </p>
            <p style="margin-bottom: 10px;">
              <strong>${decryptedAuthor?.profil?.[0]?.prenom ? `${decryptedAuthor.profil[0].prenom} ${decryptedAuthor.profil[0].nom ? decryptedAuthor.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() : 'Un utilisateur'}</strong>
              vous a donné une note de <span style="color: #FF6B2C; font-weight: bold;">${note}/5</span>
            </p>
            ${commentaire ? `
              <p style="margin-bottom: 10px;">
                <strong>Commentaire :</strong>
                <p style="margin: 5px 0 10px 0; font-style: italic; color: #666; padding-left: 10px; border-left: 3px solid #FF6B2C;">
                  "${commentaire.substring(0, 100)}${commentaire.length > 100 ? '...' : ''}"
                </p>
              </p>
            ` : ''}
            ${qualites && qualites.length > 0 ? `
              <p style="margin-bottom: 10px;">
                <strong>Qualités soulignées :</strong><br/>
                <span style="color: #FF6B2C; display: inline-block; margin-top: 5px;">
                  ${qualites.map((q: string) => `<span style="background-color: #FFF8F3; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #FF6B2C;">${q}</span>`).join(' ')}
                </span>
              </p>
            ` : ''}
            ${defauts && defauts.length > 0 ? `
              <p style="margin-bottom: 10px;">
                <strong>Points à améliorer :</strong><br/>
                <span style="color: #F43F5E; display: inline-block; margin-top: 5px;">
                  ${defauts.map((d: string) => `<span style="background-color: #FEE2E2; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #F43F5E;">${d}</span>`).join(' ')}
                </span>
              </p>
            ` : ''}
            <p style="margin: 0; font-size: 0.9em; color: #666;">
              Cliquez pour voir les détails complets de l'avis
            </p>
          </div>
        `,
        link: `/dashboard/avis?review=${review.id}`,
        is_read: false,
        is_archived: false
      };

      const { data: notification, error: notificationError } = await supabase
        .from('user_notifications')
        .insert([notificationData])
        .select()
        .single();

      if (notificationError) {
        logger.error('Erreur lors de la création de la notification:', {
          error: notificationError,
          data: notificationData
        });
        return next(notificationError);
      }

      // Envoyer les emails via la file d'attente
      await sendReviewPostedEmail(decryptedAuthor.email, {
        note,
        commentaire,
        qualites,
        defauts,
        jobi_earned: JOBI_REWARD_REVIEW,
        targetUserName: decryptedTargetUser?.profil?.[0]?.prenom ?
          `${decryptedTargetUser.profil[0].prenom} ${decryptedTargetUser.profil[0].nom ? decryptedTargetUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
          'Utilisateur',
        is_modified: false,
        reviewLink: `/dashboard/avis?review=${review.id}`
    });
      await sendReviewReceivedEmail(decryptedTargetUser.email, {
        note,
        commentaire,
        qualites,
        defauts,
        reviewerName: decryptedAuthor?.profil?.[0]?.prenom ?
          `${decryptedAuthor.profil[0].prenom} ${decryptedAuthor.profil[0].nom ? decryptedAuthor.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
          'Utilisateur',
        is_modified: false,
        reviewLink: `/dashboard/avis?review=${review.id}`
    });

      // Envoyer une notification à l'auteur de l'avis
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: authorId,
          type: 'profile',
          title: '✅ Avis déposé avec succès',
          content: `
            <div style="font-family: Arial, sans-serif; color: #333;">
              <p style="margin-bottom: 10px;">
                Votre avis a été déposé avec succès pour la mission 
                <strong style="color: #FF6B2C;">${missionDetails?.titre || 'Non spécifiée'}</strong>
              </p>
              <p style="margin-bottom: 10px;">
                Destinataire : <strong>${decryptedTargetUser?.profil?.[0]?.prenom || ''} ${decryptedTargetUser?.profil?.[0]?.nom ? decryptedTargetUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}</strong>
              </p>
              <p style="margin-bottom: 10px;">
                Note donnée : <strong style="color: #FF6B2C;">${note}/5</strong>
              </p>
              ${qualites && qualites.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  Qualités soulignées : <span style="color: #FF6B2C;">${qualites.join(', ')}</span>
                </p>
              ` : ''}
              ${defauts && defauts.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  Points à améliorer : <span style="color: #F43F5E;">${defauts.join(', ')}</span>
                </p>
              ` : ''}
              <p style="margin: 0; color: #FF6B2C; font-weight: bold;">
                Vous avez gagné ${JOBI_REWARD_REVIEW} jobi !
              </p>
            </div>
          `,
          link: `/dashboard/avis?review=${review.id}`,
          is_read: false,
          is_archived: false
        }]);

      // Formater la réponse
      const formattedReview = {
        ...review,
        author: {
          ...decryptedAuthor,
          profil: decryptedAuthor?.profil ? [{
            ...decryptedAuthor.profil[0],
            nom: decryptedAuthor.profil[0].nom ? `${decryptedAuthor.profil[0].nom.charAt(0).toUpperCase()}.` : '',
            prenom: decryptedAuthor.profil[0].prenom || ''
          }] : []
        },
        mission: {
          id: review.mission?.id || '',
          titre: review.mission?.titre || 'Mission inconnue',
          description: review.mission?.description || '',
          statut: review.mission?.statut || '',
          category_id: review.mission?.category_id || '',
          subcategory_id: review.mission?.subcategory_id || ''
        },
        mission_titre: review.mission?.titre || 'Mission inconnue',
        mission_categorie: review.mission?.category_id || 'Catégorie inconnue',
        mission_sous_categorie: review.mission?.subcategory_id || 'Sous-catégorie inconnue'
      };

      return res.status(201).json({
        success: true,
        review: formattedReview
      });
    } catch (error) {
      logger.error('Erreur lors de la création de l\'avis:', error);
      return next(error);
    }
  },

  // Modifier un avis
  updateReview: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { note, commentaire, qualites, defauts } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'avis existe et appartient à l'utilisateur
      const { data: existingReview } = await supabase
        .from('user_reviews')
        .select('*')
        .eq('id', id)
        .eq('author_id', userId)
        .single();

      if (!existingReview) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé ou non autorisé'
        });
      }

      // Vérifier si moins d'une minute s'est écoulée depuis la dernière modification
      const lastUpdate = new Date(existingReview.updated_at);
      const now = new Date();
      const diffInSeconds = Math.ceil((now.getTime() - lastUpdate.getTime()) / 1000);

      if (diffInSeconds < 60 && process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          success: false,
          message: 'Veuillez patienter ' + (60 - diffInSeconds) + ' secondes avant de modifier à nouveau votre avis'
        });
      }

      // Modération automatique par IA du contenu modifié de l'avis
      if (commentaire && commentaire.trim()) {
        try {
          // Préparer le contenu à modérer (commentaire + qualités + défauts)
          let contentToModerate = commentaire.trim();
          if (qualites && qualites.length > 0) {
            contentToModerate += ' Qualités: ' + qualites.join(', ');
          }
          if (defauts && defauts.length > 0) {
            contentToModerate += ' Points à améliorer: ' + defauts.join(', ');
          }

          const moderationResult = await contentModerationService.moderateContent({
            text: contentToModerate,
            type: 'review',
            contentId: id,
            userId: userId
          });

          if (!moderationResult.isSafe) {
            logger.warn('Avis modifié détecté comme inapproprié par la modération automatique', {
              userId: userId,
              reviewId: id,
              score: moderationResult.score,
              categories: moderationResult.categories
            });

            return res.status(400).json({
              error: 'Votre avis contient du contenu inapproprié et ne peut pas être publié.',
              message: 'Votre avis contient du contenu inapproprié et ne peut pas être publié.',
              success: false,
              toastType: 'error'
            });
          }
        } catch (moderationError) {
          // En cas d'erreur de modération, on log mais on continue
          logger.error('Erreur lors de la modération de l\'avis modifié:', moderationError);
        }
      }

      // Mettre à jour l'avis
      const { data: review, error } = await supabase
        .from('user_reviews')
        .update({
          note,
          commentaire,
          qualites,
          defauts,
          is_modified: true,
          modified_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la modification de l\'avis:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la modification de l\'avis'
        });
      }

      // Invalider le cache pour l'auteur et la cible
      await invalidateUserCache(userId, existingReview.target_user_id);
      await invalidateReviewCache(id);

      // Récupérer les informations de l'auteur et de l'utilisateur cible
      const { data: author } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', userId)
        .single();

      const { data: targetUser } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', existingReview.target_user_id)
        .single();

      // Déchiffrer les données des utilisateurs
      const decryptedAuthor = author ? await decryptUserDataAsync(author) : null;
      const decryptedTargetUser = targetUser ? await decryptUserDataAsync(targetUser) : null;

      // Déchiffrer les profils
      if (decryptedAuthor?.profil && Array.isArray(decryptedAuthor.profil)) {
        decryptedAuthor.profil = await Promise.all(decryptedAuthor.profil.map((p: any) => decryptProfilDataAsync(p)));
      }
      if (decryptedTargetUser?.profil && Array.isArray(decryptedTargetUser.profil)) {
        decryptedTargetUser.profil = await Promise.all(decryptedTargetUser.profil.map((p: any) => decryptProfilDataAsync(p)));
      }

      if (author && targetUser) {
        // Notifier l'utilisateur qui reçoit l'avis
        await supabase
          .from('user_notifications')
          .insert([{
            user_id: existingReview.target_user_id,
            type: 'profile',
            title: '📝 Avis modifié',
            content: `
              <div style="font-family: Arial, sans-serif; color: #333;">
                <p style="margin-bottom: 10px;">
                  <strong>${decryptedAuthor?.profil?.[0]?.prenom ? `${decryptedAuthor.profil[0].prenom} ${decryptedAuthor.profil[0].nom ? decryptedAuthor.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() : 'Un utilisateur'}</strong> a modifié son avis avec une note de
                  <span style="color: #FF6B2C; font-weight: bold;">${note}/5</span>
                </p>
                ${commentaire ? `
                  <p style="margin-bottom: 10px; font-style: italic; color: #666;">
                    "${commentaire.substring(0, 30)}${commentaire.length > 30 ? '...' : ''}"
                  </p>
                ` : ''}
                ${qualites && qualites.length > 0 ? `
                  <p style="margin: 0;">
                    <strong>Qualités :</strong> 
                    <span style="color: #FF6B2C;">${qualites.join(', ')}</span>
                  </p>
                ` : ''}
                ${defauts && defauts.length > 0 ? `
                  <p style="margin: 0; margin-top: 5px;">
                    <strong>Points à améliorer :</strong> 
                    <span style="color: #F43F5E;">${defauts.join(', ')}</span>
                  </p>
                ` : ''}
              </div>
            `,
            link: `/dashboard/avis?review=${review.id}`,
            is_read: false,
            is_archived: false
          }]);

        // Envoyer les emails via la file d'attente
        await sendReviewPostedEmail(decryptedAuthor.email, {
          note,
          commentaire,
          qualites,
          defauts,
          jobi_earned: 0, // Pas de Jobi pour une modification
          targetUserName: decryptedTargetUser?.profil?.[0]?.prenom ?
            `${decryptedTargetUser.profil[0].prenom} ${decryptedTargetUser.profil[0].nom ? decryptedTargetUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur',
          is_modified: true,
          reviewLink: `/dashboard/avis?review=${review.id}`
      });
        await sendReviewReceivedEmail(decryptedTargetUser.email, {
          note,
          commentaire,
          qualites,
          defauts,
          reviewerName: decryptedAuthor?.profil?.[0]?.prenom ?
            `${decryptedAuthor.profil[0].prenom} ${decryptedAuthor.profil[0].nom ? decryptedAuthor.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur',
          is_modified: true,
          reviewLink: `/dashboard/avis?review=${review.id}`
      });
      }

      // Journaliser l'activité de l'utilisateur
      if (userId) {
        await logUserActivity(
          userId || '',
          'review_update',
          id,
          'review',
          {
            target_user_id: existingReview.target_user_id,
            new_note: note, 
            old_note: existingReview.note,
            has_comment: !!commentaire,
            qualites_count: qualites ? qualites.length : 0,
            defauts_count: defauts ? defauts.length : 0
          },
          getIpFromRequest(req)
        );
      }

      return res.status(200).json({
        success: true,
        data: review
      });
    } catch (error) {
      logger.error('Erreur lors de la modification de l\'avis:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  // Supprimer un avis
  deleteReview: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Récupérer l'avis avant de le supprimer
      const { data: existingReview, error: reviewError } = await supabase
        .from('user_reviews')
        .select('*')
        .eq('id', id)
        .single();

      if (reviewError || !existingReview) {
        logger.error('Erreur lors de la récupération de l\'avis:', reviewError);
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé'
        });
      }

      // Vérifier que l'utilisateur est bien l'auteur de l'avis
      if (existingReview.author_id !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'êtes pas autorisé à supprimer cet avis'
        });
      }

      // Supprimer les photos associées si elles existent
      if (existingReview.photos && existingReview.photos.length > 0) {
        try {
          await deleteReviewPhotos(userId, existingReview.photos);
          logger.info('Photos d\'avis supprimées lors de la suppression de l\'avis', {
            userId,
            reviewId: id,
            photosCount: existingReview.photos.length
          });
        } catch (error) {
          logger.warn('Erreur lors de la suppression des photos d\'avis', { error, userId, reviewId: id });
          // On continue même si la suppression des photos échoue
        }
      }

      // Récupérer les informations de l'auteur et de la cible
      const { data: authorUser, error: authorError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', existingReview.author_id)
        .single();

      const { data: targetUser, error: targetError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', existingReview.target_user_id)
        .single();

      if (authorError || targetError) {
        logger.error('Erreur lors de la récupération des utilisateurs:', { authorError, targetError });
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des informations utilisateurs'
        });
      }

      // Déchiffrer les données des utilisateurs
      const decryptedAuthorUser = authorUser ? await decryptUserDataAsync(authorUser) : null;
      const decryptedTargetUser = targetUser ? await decryptUserDataAsync(targetUser) : null;

      // Déchiffrer les profils
      if (decryptedAuthorUser?.profil && Array.isArray(decryptedAuthorUser.profil)) {
        decryptedAuthorUser.profil = await Promise.all(decryptedAuthorUser.profil.map((p: any) => decryptProfilDataAsync(p)));
      }
      if (decryptedTargetUser?.profil && Array.isArray(decryptedTargetUser.profil)) {
        decryptedTargetUser.profil = await Promise.all(decryptedTargetUser.profil.map((p: any) => decryptProfilDataAsync(p)));
      }

      // Retirer les Jobi à l'auteur de l'avis
      const { data: userJobi } = await supabase
        .from('user_jobi')
        .select('montant')
        .eq('user_id', userId)
        .single();

      if (userJobi) {
        const newAmount = Math.max(0, (userJobi.montant || 0) - JOBI_REWARD_REVIEW);

        const { error: updateJobiError } = await supabase
          .from('user_jobi')
          .update({ montant: newAmount })
          .eq('user_id', userId)
          .single();

        if (!updateJobiError) {
          await supabase
            .from('user_jobi_historique')
            .insert({
              user_id: userId,
              montant: -JOBI_REWARD_REVIEW,
              titre: 'Suppression avis',
              description: 'Retrait des Jobi suite à la suppression d\'un avis'
            });
        }
      }

      // Supprimer l'avis
      const { error: deleteError } = await supabase
        .from('user_reviews')
        .delete()
        .eq('id', id);

      if (deleteError) {
        logger.error('Erreur lors de la suppression de l\'avis:', deleteError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la suppression de l\'avis',
          error: deleteError.message
        });
      }

      // Invalider le cache pour l'auteur et la cible
      await invalidateUserCache(userId, existingReview.target_user_id);
      await invalidateReviewCache(id);

      // Envoyer les emails via la file d'attente
      if (decryptedTargetUser?.email) {
        await sendReviewDeletedEmail(decryptedTargetUser.email, {
          reviewId: existingReview.id,
          note: existingReview.note,
          commentaire: existingReview.commentaire,
          qualites: existingReview.qualites,
          defauts: existingReview.defauts,
          targetUserName: decryptedTargetUser?.profil?.[0]?.prenom ?
            `${decryptedTargetUser.profil[0].prenom} ${decryptedTargetUser.profil[0].nom ? decryptedTargetUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur',
          authorName: decryptedAuthorUser?.profil?.[0]?.prenom ?
            `${decryptedAuthorUser.profil[0].prenom} ${decryptedAuthorUser.profil[0].nom ? decryptedAuthorUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur'
      });
      }

      if (decryptedAuthorUser?.email) {
        await sendReviewDeletedEmail(decryptedAuthorUser.email, {
          reviewId: existingReview.id,
          note: existingReview.note,
          commentaire: existingReview.commentaire,
          qualites: existingReview.qualites,
          defauts: existingReview.defauts,
          targetUserName: decryptedTargetUser?.profil?.[0]?.prenom ?
            `${decryptedTargetUser.profil[0].prenom} ${decryptedTargetUser.profil[0].nom ? decryptedTargetUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur',
          authorName: decryptedAuthorUser?.profil?.[0]?.prenom ?
            `${decryptedAuthorUser.profil[0].prenom} ${decryptedAuthorUser.profil[0].nom ? decryptedAuthorUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
            'Utilisateur'
      });
      }

      // Envoyer une notification à l'utilisateur qui a reçu l'avis
      const notificationData = {
        user_id: existingReview.target_user_id,
        type: 'profile',
        title: '🗑 Avis supprimé',
        content: `
          <div style="font-family: Arial, sans-serif; color: #333;">
            <p style="margin-bottom: 10px;">
              <strong>${authorUser?.profil?.[0]?.prenom ? `${authorUser.profil[0].prenom} ${authorUser.profil[0].nom ? authorUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() : 'Un utilisateur'}</strong> a supprimé son avis avec la note 
              <span style="color: #FF6B2C; font-weight: bold;">${existingReview.note}/5</span>
            </p>
            ${existingReview.commentaire ? `
              <p style="margin-bottom: 10px; font-style: italic; color: #666;">
                "${existingReview.commentaire.substring(0, 30)}${existingReview.commentaire.length > 30 ? '...' : ''}"
              </p>
            ` : ''}
            ${existingReview.qualites && existingReview.qualites.length > 0 ? `
              <p style="margin: 0;">
                <strong>Qualités :</strong> 
                <span style="color: #FF6B2C;">${existingReview.qualites.join(', ')}</span>
              </p>
            ` : ''}
            ${existingReview.defauts && existingReview.defauts.length > 0 ? `
              <p style="margin: 5px 0 0 0;">
                <strong>Points à améliorer :</strong> 
                <span style="color: #F43F5E;">${existingReview.defauts.join(', ')}</span>
              </p>
            ` : ''}
          </div>
        `,
        link: `/dashboard/avis`,
        is_read: false,
        is_archived: false
      };

      const { error: notificationError } = await supabase
        .from('user_notifications')
        .insert([notificationData]);

      if (notificationError) {
        logger.error('Erreur lors de la création de la notification de suppression:', {
          error: notificationError,
          data: notificationData
        });
      }

      // Journaliser l'activité de l'utilisateur
      if (userId) {
        await logUserActivity(
          userId || '',
          'review_delete',
          id,
          'review',
          {
            target_user_id: existingReview.target_user_id,
            note: existingReview.note,
            had_comment: !!existingReview.commentaire,
            had_response: !!existingReview.reponse,
            qualites_count: existingReview.qualites ? existingReview.qualites.length : 0,
            defauts_count: existingReview.defauts ? existingReview.defauts.length : 0
          },
          getIpFromRequest(req)
        );
      }

      return res.status(200).json({
        success: true,
        message: 'Avis supprimé avec succès'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de l\'avis:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression de l\'avis',
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      });
    }
  },

  // Répondre à un avis
  replyToReview: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { reponse } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'avis existe et concerne l'utilisateur
      const { data: existingReview } = await supabase
        .from('user_reviews')
        .select('*')
        .eq('id', id)
        .eq('target_user_id', userId)
        .single();

      if (!existingReview) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé ou non autorisé'
        });
      }

      // Récupérer les informations de l'utilisateur répondant
      const { data: responderUser } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', userId)
        .single();

      if (!responderUser) {
        return res.status(404).json({
          success: false,
          message: 'Utilisateur répondant non trouvé'
        });
      }

      // Déchiffrer les données de l'utilisateur et du profil du répondant
      const decryptedResponderUser = responderUser ? await decryptUserDataAsync(responderUser) : null;
      
      // Déchiffrer également les données du profil du répondant
      if (decryptedResponderUser?.profil?.[0]) {
        decryptedResponderUser.profil[0] = await decryptProfilDataAsync(decryptedResponderUser.profil[0]);
      }

      // Modération automatique par IA de la réponse à l'avis
      if (reponse && reponse.trim()) {
        try {
          const moderationResult = await contentModerationService.moderateContent({
            text: reponse.trim(),
            type: 'review',
            contentId: `reply-${id}`,
            userId: userId
          });

          if (!moderationResult.isSafe) {
            logger.warn('Réponse à l\'avis détectée comme inappropriée par la modération automatique', {
              userId: userId,
              reviewId: id,
              score: moderationResult.score,
              categories: moderationResult.categories
            });

            return res.status(400).json({
              error: 'Votre réponse contient du contenu inapproprié et ne peut pas être publiée.',
              message: 'Votre réponse contient du contenu inapproprié et ne peut pas être publiée.',
              success: false,
              toastType: 'error'
            });
          }
        } catch (moderationError) {
          // En cas d'erreur de modération, on log mais on continue
          logger.error('Erreur lors de la modération de la réponse à l\'avis:', moderationError);
        }
      }

      // Ajouter la réponse
      const { data: review, error } = await supabase
        .from('user_reviews')
        .update({
          reponse,
          reponse_date: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de l\'ajout de la réponse:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de l\'ajout de la réponse'
        });
      }

      // Journaliser l'action de l'utilisateur
      if (!userId) return res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
      logUserActivity(
        userId || '',
        'review_response',
        id,
        'review',
        { 
          reponse: reponse.substring(0, 100),
          author_id: existingReview.author_id
        },
        getIpFromRequest(req)
      );

      // Invalider tous les caches concernés
      await Promise.all([
        // Invalider le cache de l'avis et ses réponses
        invalidateReviewCache(id),
        
        // Invalider tous les caches des deux utilisateurs
        invalidateUserCache(userId, existingReview.author_id)
      ]);

      logger.info('Cache avis : Invalidation complète après réponse', { 
        reviewId: id, 
        userId, 
        authorId: existingReview.author_id 
      });

      // Notifier l'auteur de l'avis
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: existingReview.author_id,
          type: 'profile',
          title: '💬 Réponse à votre avis',
          content: `
            <div style="font-family: Arial, sans-serif; color: #333;">
              <p style="margin-bottom: 10px;">
                <strong>${decryptedResponderUser?.profil?.[0]?.prenom ? `${decryptedResponderUser.profil[0].prenom} ${decryptedResponderUser.profil[0].nom ? decryptedResponderUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() : 'Le Jobbeur'}</strong>
                a répondu à votre avis avec la note <span style="color: #FF6B2C; font-weight: bold;">${existingReview.note}/5</span>
              </p>
              ${existingReview.commentaire ? `
                <p style="margin-bottom: 10px;">
                  <strong>Votre commentaire initial :</strong>
                  <p style="margin: 5px 0 10px 0; font-style: italic; color: #666; padding-left: 10px; border-left: 3px solid #FF6B2C;">
                    "${existingReview.commentaire.substring(0, 100)}${existingReview.commentaire.length > 100 ? '...' : ''}"
                  </p>
                </p>
              ` : ''}
              ${reponse ? `
                <p style="margin-bottom: 10px;">
                  <strong>Réponse du jobbeur :</strong>
                  <p style="margin: 5px 0 10px 0; font-style: italic; color: #666; padding-left: 10px; border-left: 3px solid #FF6B2C;">
                    "${reponse.substring(0, 100)}${reponse.length > 100 ? '...' : ''}"
                  </p>
                </p>
              ` : ''}
              ${existingReview.qualites && existingReview.qualites.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  <strong>Qualités soulignées :</strong><br/>
                  <span style="color: #FF6B2C; display: inline-block; margin-top: 5px;">
                    ${existingReview.qualites.map((q: string) => `<span style="background-color: #FFF8F3; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #FF6B2C;">${q}</span>`).join(' ')}
                  </span>
                </p>
              ` : ''}
              ${existingReview.defauts && existingReview.defauts.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  <strong>Points à améliorer :</strong><br/>
                  <span style="color: #F43F5E; display: inline-block; margin-top: 5px;">
                    ${existingReview.defauts.map((d: string) => `<span style="background-color: #FEE2E2; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #F43F5E;">${d}</span>`).join(' ')}
                  </span>
                </p>
              ` : ''}
            </div>
          `,
          link: `/dashboard/avis?review=${id}`,
          is_read: false,
          is_archived: false
        }]);

      // Envoyer un email à l'auteur de l'avis
      const { data: authorUser } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', existingReview.author_id)
        .single();

      if (!authorUser) {
        return res.status(404).json({
          success: false,
          message: 'Auteur de l\'avis non trouvé'
        });
      }

      // Décrypter l'email de l'auteur
      const decryptedAuthorUser = await decryptUserDataAsync(authorUser);

      await sendReviewReplyEmail(decryptedAuthorUser.email, {
        reponse,
        review: {
          note: existingReview.note,
          commentaire: existingReview.commentaire,
          qualites: existingReview.qualites,
          defauts: existingReview.defauts
        },
        responderName: decryptedResponderUser?.profil?.[0]?.prenom ?
          `${decryptedResponderUser.profil[0].prenom} ${decryptedResponderUser.profil[0].nom ? decryptedResponderUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
          'Le jobbeur',
        reviewLink: `/dashboard/avis?review=${id}`
      });

      return res.status(200).json({
        success: true,
        data: review
      });
    } catch (error) {
      logger.error('Erreur lors de l\'ajout de la réponse:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  // Modifier une réponse à un avis
  updateReplyToReview: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { reponse } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'avis existe et concerne l'utilisateur
      const { data: existingReview } = await supabase
        .from('user_reviews')
        .select('*')
        .eq('id', id)
        .eq('target_user_id', userId)
        .single();

      if (!existingReview) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé ou non autorisé'
        });
      }

      // Récupérer les informations de l'utilisateur répondant
      const { data: responderUser } = await supabase
        .from('users')
        .select(`
          id,
          email,
          profil:user_profil!user_profil_user_id_fkey (
            nom,
            prenom,
            photo_url
          )
        `)
        .eq('id', userId)
        .single();

      if (!responderUser) {
        return res.status(404).json({
          success: false,
          message: 'Utilisateur répondant non trouvé'
        });
      }

      // Déchiffrer les données de l'utilisateur et du profil du répondant
      const decryptedResponderUser = responderUser ? await decryptUserDataAsync(responderUser) : null;
      
      // Déchiffrer également les données du profil du répondant
      if (decryptedResponderUser?.profil?.[0]) {
        decryptedResponderUser.profil[0] = await decryptProfilDataAsync(decryptedResponderUser.profil[0]);
      }

      // Modération automatique par IA de la réponse modifiée à l'avis
      if (reponse && reponse.trim()) {
        try {
          const moderationResult = await contentModerationService.moderateContent({
            text: reponse.trim(),
            type: 'review',
            contentId: `reply-update-${id}`,
            userId: userId
          });

          if (!moderationResult.isSafe) {
            logger.warn('Réponse modifiée à l\'avis détectée comme inappropriée par la modération automatique', {
              userId: userId,
              reviewId: id,
              score: moderationResult.score,
              categories: moderationResult.categories
            });

            return res.status(400).json({
              error: 'Votre réponse contient du contenu inapproprié et ne peut pas être publiée.',
              message: 'Votre réponse contient du contenu inapproprié et ne peut pas être publiée.',
              success: false,
              toastType: 'error'
            });
          }
        } catch (moderationError) {
          // En cas d'erreur de modération, on log mais on continue
          logger.error('Erreur lors de la modération de la réponse modifiée à l\'avis:', moderationError);
        }
      }

      // Modifier la réponse
      const { data: review, error } = await supabase
        .from('user_reviews')
        .update({
          reponse,
          reponse_date: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logger.error('Erreur lors de la modification de la réponse:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la modification de la réponse'
        });
      }

      // Journaliser l'action de l'utilisateur
      if (!userId) return res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
      logUserActivity(
        userId || '',
        'review_response_update',
        id,
        'review',
        { 
          reponse: reponse.substring(0, 100),
          author_id: existingReview.author_id
        },
        getIpFromRequest(req)
      );

      // Invalider tous les caches concernés de manière atomique
      const keysToDelete = [
        // Clé de l'avis spécifique
        CACHE_KEYS.REVIEW_DETAIL(id),
        CACHE_KEYS.REVIEW_RESPONSES(id),
        
        // Clés de base pour les deux utilisateurs
        CACHE_KEYS.USER_REVIEWS(userId || ''),
        CACHE_KEYS.USER_REVIEWS_STATS(userId || ''),
        CACHE_KEYS.USER_SENT_REVIEWS(userId || ''),
        CACHE_KEYS.USER_QUALITES_STATS(userId || ''),
        CACHE_KEYS.USER_REVIEWS(existingReview.author_id),
        CACHE_KEYS.USER_REVIEWS_STATS(existingReview.author_id),
        CACHE_KEYS.USER_SENT_REVIEWS(existingReview.author_id),
        CACHE_KEYS.USER_QUALITES_STATS(existingReview.author_id)
      ];

      // Ajouter les clés de pagination pour les deux utilisateurs
      const pageSizes = [5, 10, 20];
      for (const size of pageSizes) {
        for (let page = 1; page <= 10; page++) {
          keysToDelete.push(
            CACHE_KEYS.USER_REVIEWS(userId || '', page, size),
            CACHE_KEYS.USER_REVIEWS(existingReview.author_id, page, size)
          );
        }
      }

      // Supprimer toutes les clés en une seule opération atomique
      await redis.del(...keysToDelete);

      logger.info('Cache avis : Invalidation complète après modification de réponse', { 
        reviewId: id, 
        userId, 
        authorId: existingReview.author_id,
        keysCount: keysToDelete.length
      });

      // Notifier l'auteur de l'avis
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: existingReview.author_id,
          type: 'profile',
          title: '📝 Réponse modifiée',
          content: `
            <div style="font-family: Arial, sans-serif; color: #333;">
              <p style="margin-bottom: 10px;">
                <strong>${decryptedResponderUser?.profil?.[0]?.prenom ? `${decryptedResponderUser.profil[0].prenom} ${decryptedResponderUser.profil[0].nom ? decryptedResponderUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() : 'Le Jobbeur'}</strong>
                a modifié sa réponse à votre avis.
              </p>
              ${reponse ? `
                <p style="margin-bottom: 10px; font-style: italic; color: #666;">
                  "${reponse.substring(0, 100)}${reponse.length > 100 ? '...' : ''}"
                </p>
              ` : ''}
              ${existingReview.qualites && existingReview.qualites.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  <strong>Qualités soulignées dans votre avis :</strong><br/>
                  <span style="color: #FF6B2C; display: inline-block; margin-top: 5px;">
                    ${existingReview.qualites.map((q: string) => `<span style="background-color: #FFF8F3; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #FF6B2C;">${q}</span>`).join(' ')}
                  </span>
                </p>
              ` : ''}
              ${existingReview.defauts && existingReview.defauts.length > 0 ? `
                <p style="margin-bottom: 10px;">
                  <strong>Points à améliorer mentionnés dans votre avis :</strong><br/>
                  <span style="color: #F43F5E; display: inline-block; margin-top: 5px;">
                    ${existingReview.defauts.map((d: string) => `<span style="background-color: #FEE2E2; padding: 2px 8px; margin: 0 4px; border-radius: 12px; border: 1px solid #F43F5E;">${d}</span>`).join(' ')}
                  </span>
                </p>
              ` : ''}
            </div>
          `,
          link: `/dashboard/avis?review=${id}`,
          is_read: false,
          is_archived: false
        }]);

      // Récupérer les informations de l'auteur de l'avis pour l'email
      const { data: authorUser } = await supabase
        .from('users')
        .select('email')
        .eq('id', existingReview.author_id)
        .single();

      if (!authorUser) {
        logger.error('Auteur de l\'avis non trouvé');
        return res.status(404).json({
          success: false,
          message: 'Auteur de l\'avis non trouvé'
        });
      }

      // Décrypter l'email de l'auteur
      const decryptedAuthorUserUpdate = await decryptUserDataAsync(authorUser);

      // Envoyer un email de notification
      await sendReviewReplyEmail(decryptedAuthorUserUpdate.email, {
        reponse,
        review: {
          note: existingReview.note,
          commentaire: existingReview.commentaire,
          qualites: existingReview.qualites,
          defauts: existingReview.defauts
        },
        responderName: decryptedResponderUser?.profil?.[0]?.prenom ?
          `${decryptedResponderUser.profil[0].prenom} ${decryptedResponderUser.profil[0].nom ? decryptedResponderUser.profil[0].nom.charAt(0).toUpperCase() + '.' : ''}`.trim() :
          'Le jobbeur',
        reviewLink: `/dashboard/avis?review=${id}`
      });

      return res.status(200).json({
        success: true,
        data: review
      });
    } catch (error) {
      logger.error('Erreur lors de la modification de la réponse:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  // Récupérer les avis d'un utilisateur
  getUserReviews: async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 5;
      const offset = (page - 1) * limit;

      // Vérifier le cache
      const cacheKey = `${CACHE_KEYS.USER_REVIEWS(userId, page, limit)}`;
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Cache avis : Données récupérées depuis le cache Redis:', { userId, page });
        return res.status(200).json(JSON.parse(cachedData));
      }

      // Récupérer le nombre total d'avis
      const { count } = await supabase
        .from('user_reviews')
        .select('*', { count: 'exact', head: true })
        .eq('target_user_id', userId)
        .eq('statut', 'visible');

      // Récupérer les avis paginés
      const { data: reviews, error } = await supabase
        .from('user_reviews')
        .select(`
          *,
          author:users!user_reviews_author_id_fkey (
            id,
            profil:user_profil!user_profil_user_id_fkey (
              nom,
              prenom,
              photo_url
            )
          ),
          mission:user_missions!user_reviews_mission_id_fkey (
            id,
            titre,
            description,
            statut,
            category_id,
            subcategory_id
          )
        `)
        .eq('target_user_id', userId)
        .eq('statut', 'visible')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error('Erreur lors de la récupération des avis:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des avis'
        });
      }

      // Calculer les statistiques
      const statsKey = CACHE_KEYS.USER_REVIEWS_STATS(userId);
      let stats = await redis.get(statsKey);
      let statsData;

      if (!stats) {
        const { data: allReviews, error: statsError } = await supabase
          .from('user_reviews')
          .select('note')
          .eq('target_user_id', userId)
          .eq('statut', 'visible');

        if (statsError) {
          logger.error('Erreur lors de la récupération des statistiques:', statsError);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la récupération des statistiques'
          });
        }

        // Calculer les statistiques
        const statsReviews = allReviews || [];
        const totalReviews = statsReviews.length;
        const averageRating = totalReviews > 0 
          ? Number((statsReviews.reduce((acc, review) => acc + review.note, 0) / totalReviews).toFixed(1))
          : 0;
        const satisfactionRate = totalReviews > 0
          ? Math.round((statsReviews.filter(review => review.note >= 4).length / totalReviews) * 100)
          : 0;

        statsData = {
          total_reviews: totalReviews,
          rating: averageRating,
          completion_rate: satisfactionRate
        };

        // Mettre en cache les statistiques
        await redis.setex(statsKey, CACHE_TTL, JSON.stringify(statsData));
      } else {
        statsData = JSON.parse(stats);
      }

      // Formater les avis pour le frontend avec décryptage asynchrone correct
      const formattedReviews = await Promise.all((reviews || []).map(async (review) => {
        // Déchiffrer les données de profil de l'auteur de manière asynchrone
        let decryptedAuthorProfil = [];
        if (review.author?.profil && review.author.profil.length > 0) {
          decryptedAuthorProfil = await Promise.all(
            review.author.profil.map(async (p: any) => await decryptProfilDataAsync(p))
          );
        }

        return {
          ...review,
          note: review.note,
          commentaire: review.commentaire,
          mission: {
            id: review.mission?.id || '',
            titre: review.mission?.titre || 'Mission inconnue',
            description: review.mission?.description || '',
            statut: review.mission?.statut || '',
            category_id: review.mission?.category_id || '',
            subcategory_id: review.mission?.subcategory_id || ''
          },
          mission_titre: review.mission?.titre || 'Mission inconnue',
          mission_categorie: review.mission?.category_id || 'Catégorie inconnue',
          mission_sous_categorie: review.mission?.subcategory_id || 'Sous-catégorie inconnue',
          author: {
            ...review.author,
            profil: decryptedAuthorProfil.length > 0 ? [{
              ...decryptedAuthorProfil[0],
              nom: decryptedAuthorProfil[0].nom ? `${decryptedAuthorProfil[0].nom.charAt(0).toUpperCase()}.` : '',
              prenom: decryptedAuthorProfil[0].prenom || '',
              photo_url: decryptedAuthorProfil[0].photo_url || ''
            }] : []
          }
        };
      }));

      const responseData = {
        success: true,
        reviews: formattedReviews,
        stats: statsData,
        pagination: {
          current_page: page,
          total_pages: Math.ceil((count || 0) / limit),
          has_more: offset + limit < (count || 0)
        }
      };

      // Mettre en cache les données
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(responseData));

      return res.status(200).json(responseData);
    } catch (error) {
      logger.error('Erreur lors de la récupération des avis:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  // Récupérer un avis spécifique
  getReviewById: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier le cache
      const cacheKey = CACHE_KEYS.REVIEW_DETAIL(id);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        const review = JSON.parse(cachedData);
        // Vérifier que l'utilisateur a accès à cet avis
        if (review.author_id === userId || review.target_user_id === userId) {
          logger.info('Cache avis : Avis récupéré depuis le cache Redis:', { id });
          return res.status(200).json({
            success: true,
            review
          });
        }
      }

      // Récupérer l'avis avec les informations de l'auteur
      const { data: review, error } = await supabase
        .from('user_reviews')
        .select(`
          *,
          author:users!user_reviews_author_id_fkey (
            id,
            email,
            profil:user_profil!user_profil_user_id_fkey (
              nom,
              prenom,
              photo_url
            )
          )
        `)
        .eq('id', id)
        .eq('statut', 'visible')
        .single();

      if (error) {
        logger.error('Erreur lors de la récupération de l\'avis:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de l\'avis'
        });
      }

      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé'
        });
      }

      // Vérifier que l'utilisateur est soit l'auteur de l'avis, soit la cible
      if (review.author_id !== userId && review.target_user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas accès à cet avis'
        });
      }

      // Déchiffrer les données de profil de l'auteur de manière asynchrone
      let decryptedAuthorProfil = [];
      if (review.author?.profil && review.author.profil.length > 0) {
        decryptedAuthorProfil = await Promise.all(
          review.author.profil.map(async (p: any) => await decryptProfilDataAsync(p))
        );
      }

      // Formater la réponse
      const formattedReview = {
        ...review,
        author: {
          ...review.author,
          profil: decryptedAuthorProfil.length > 0 ? [{
            ...decryptedAuthorProfil[0],
            nom: decryptedAuthorProfil[0].nom ? `${decryptedAuthorProfil[0].nom.charAt(0).toUpperCase()}.` : '',
            prenom: decryptedAuthorProfil[0].prenom || '',
            photo_url: decryptedAuthorProfil[0].photo_url || ''
          }] : []
        },
        mission: {
          id: review.mission?.id || '',
          titre: review.mission?.titre || 'Mission inconnue',
          description: review.mission?.description || '',
          statut: review.mission?.statut || '',
          category_id: review.mission?.category_id || '',
          subcategory_id: review.mission?.subcategory_id || ''
        },
        mission_titre: review.mission?.titre || 'Mission inconnue',
        mission_categorie: review.mission?.category_id || 'Catégorie inconnue',
        mission_sous_categorie: review.mission?.subcategory_id || 'Sous-catégorie inconnue'
      };

      // Mettre en cache
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(formattedReview));

      return res.status(200).json({
        success: true,
        review: formattedReview
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'avis:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  getQualitesStats: async (req: Request, res: Response) => {
    const { userId } = req.params;

    try {
      // Vérifier le cache
      const cacheKey = CACHE_KEYS.USER_QUALITES_STATS(userId);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Cache avis : Statistiques des qualités récupérées depuis le cache Redis:', { userId });
        return res.status(200).json({
          success: true,
          data: JSON.parse(cachedData)
        });
      }

      const { data, error } = await supabase.rpc('get_qualites_stats', { 
        user_id_param: userId 
      });

      if (error) {
        logger.info('Erreur lors de la récupération des stats des qualités:', error);
        return res.status(500).json({
          success: false,
          error: 'Erreur lors de la récupération des statistiques des qualités'
        });
      }

      // Mettre en cache
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(data || []));

      return res.status(200).json({
        success: true,
        data: data || []
      });
    } catch (error) {
      logger.info('Erreur lors de la récupération des stats des qualités:', error);
      return res.status(500).json({
        success: false,
        error: 'Erreur lors de la récupération des statistiques des qualités'
      });
    }
  },

  // Vérifier si un avis existe pour une mission
  checkReviewExists: async (req: Request, res: Response) => {
    try {
      const { missionId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier si un avis existe déjà pour cette mission
      const { data: existingReview } = await supabase
        .from('user_reviews')
        .select('id')
        .eq('author_id', userId)
        .eq('mission_id', missionId)
        .single();

      return res.status(200).json({
        success: true,
        exists: !!existingReview,
        reviewId: existingReview?.id
      });
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'avis:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }
  },

  // Récupérer les avis envoyés par un utilisateur
  getSentReviews: async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;

      // Récupérer les avis envoyés
      const { data: reviews, error } = await supabase
        .from('user_reviews')
        .select(`
          *,
          target_user:users!user_reviews_target_user_id_fkey (
            email,
            profil:user_profil!user_profil_user_id_fkey (
              nom,
              prenom,
              photo_url
            )
          ),
          mission:user_missions!user_reviews_mission_id_fkey (
            id,
            titre,
            description,
            statut,
            category_id,
            subcategory_id
          )
        `)
        .eq('author_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Erreur lors de la récupération des avis envoyés:', error);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des avis envoyés'
        });
      }

      // Formater les avis pour le frontend avec décryptage asynchrone correct
      const formattedReviews = await Promise.all((reviews || []).map(async (review) => {
        // Déchiffrer les données de profil de l'utilisateur cible de manière asynchrone
        let decryptedTargetProfil = [];
        if (review.target_user?.profil && review.target_user.profil.length > 0) {
          decryptedTargetProfil = await Promise.all(
            review.target_user.profil.map(async (p: any) => await decryptProfilDataAsync(p))
          );
        }

        return {
          ...review,
          mission: {
            id: review.mission?.id || '',
            titre: review.mission?.titre || 'Mission inconnue',
            description: review.mission?.description || '',
            statut: review.mission?.statut || '',
            category_id: review.mission?.category_id || '',
            subcategory_id: review.mission?.subcategory_id || ''
          },
          target_user: {
            ...review.target_user,
            profil: decryptedTargetProfil.length > 0 ? [{
              ...decryptedTargetProfil[0],
              nom: decryptedTargetProfil[0].nom ? `${decryptedTargetProfil[0].nom.charAt(0).toUpperCase()}.` : '',
              prenom: decryptedTargetProfil[0].prenom || '',
              photo_url: decryptedTargetProfil[0].photo_url || ''
            }] : []
          }
        };
      }));

      return res.status(200).json({
        success: true,
        reviews: formattedReviews
      });
    } catch (error) {
      logger.error('Erreur lors de la récupération des avis envoyés:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des avis envoyés'
      });
    }
  },

  // Uploader des photos pour un avis (max 4)
  uploadReviewPhoto: async (req: Request, res: Response) => {
    try {
      const { id: reviewId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'avis existe et appartient à l'utilisateur
      const { data: review, error: reviewError } = await supabase
        .from('user_reviews')
        .select('id, author_id, photos')
        .eq('id', reviewId)
        .eq('author_id', userId)
        .single();

      if (reviewError || !review) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé ou vous n\'êtes pas autorisé à le modifier'
        });
      }

      // Vérifier qu'au moins un fichier a été uploadé
      if (!req.files || !req.files.photos) {
        return res.status(400).json({
          success: false,
          message: 'Aucune photo fournie'
        });
      }

      // Gérer le cas où il y a une seule photo ou plusieurs
      const files = Array.isArray(req.files.photos) ? req.files.photos : [req.files.photos];

      // Vérifier le nombre de photos (max 4)
      const existingPhotos = review.photos || [];
      const totalPhotos = existingPhotos.length + files.length;

      if (totalPhotos > 4) {
        return res.status(400).json({
          success: false,
          message: `Maximum 4 photos autorisées par avis. Vous avez déjà ${existingPhotos.length} photo(s) et tentez d'en ajouter ${files.length}.`
        });
      }

      // Valider chaque fichier
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (!file.mimetype.startsWith('image/')) {
          return res.status(400).json({
            success: false,
            message: `Le fichier ${i + 1} n'est pas une image`
          });
        }

        if (file.size > 5 * 1024 * 1024) {
          return res.status(400).json({
            success: false,
            message: `Le fichier ${i + 1} dépasse la taille maximale de 5MB`
          });
        }
      }

      // Préparer les fichiers pour l'upload (comme les autres controllers)
      const filesToUpload = await Promise.all(files.map(async (file, index) => {
        logger.info('Fichier reçu pour upload', {
          name: file.name,
          size: file.size,
          mimetype: file.mimetype,
          hasTempFilePath: !!file.tempFilePath,
          hasData: !!file.data,
          dataLength: file.data ? file.data.length : 0,
          index: index
        });

        // Lire le fichier temporaire comme les autres controllers
        let fileBuffer: Buffer;
        if (file.tempFilePath) {
          // Lire depuis le fichier temporaire (configuration useTempFiles: true)
          fileBuffer = await new Promise<Buffer>((resolve, reject) => {
            require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
              if (err) {
                logger.error('Erreur lors de la lecture du fichier temporaire', { error: err, fileName: file.name });
                reject(new Error(`Erreur lors de la lecture du fichier: ${err.message}`));
              } else {
                resolve(data);
              }
            });
          });
        } else if (file.data && Buffer.isBuffer(file.data) && file.data.length > 0) {
          // Fallback sur file.data si pas de fichier temporaire
          fileBuffer = file.data;
        } else {
          throw new Error(`Le fichier ${index + 1} (${file.name}) est vide ou corrompu`);
        }

        logger.info('Fichier lu avec succès', {
          name: file.name,
          bufferLength: fileBuffer.length,
          index: index
        });

        return {
          buffer: fileBuffer,
          fileType: file.mimetype
        };
      }));

      // Uploader les nouvelles photos avec modération automatique
      const newPhotoUrls = await uploadReviewPhotos(
        userId,
        filesToUpload,
        reviewId
      );

      // Combiner avec les photos existantes
      const allPhotos = [...existingPhotos, ...newPhotoUrls];

      // Mettre à jour l'avis avec les nouvelles URLs de photos
      const { error: updateError } = await supabase
        .from('user_reviews')
        .update({
          photos: allPhotos,
          updated_at: new Date().toISOString()
        })
        .eq('id', reviewId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de l\'avis avec les photos:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour de l\'avis'
        });
      }

      // Invalider le cache
      await invalidateReviewCache(reviewId);

      logger.info('Photos d\'avis uploadées avec succès', {
        userId,
        reviewId,
        newPhotosCount: newPhotoUrls.length,
        totalPhotosCount: allPhotos.length
      });

      res.json({
        success: true,
        photos: allPhotos,
        new_photos: newPhotoUrls,
        message: `${newPhotoUrls.length} photo(s) uploadée(s) avec succès`
      });
    } catch (error: any) {
      logger.error('Erreur lors de l\'upload des photos d\'avis:', error);

      // Gestion spécifique des erreurs de modération
      if (error.message && error.message.includes('contenu inapproprié')) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'upload des photos'
      });
    }
  },

  // Supprimer des photos d'avis
  deleteReviewPhoto: async (req: Request, res: Response) => {
    try {
      const { id: reviewId } = req.params;
      const { photoUrls } = req.body; // Array des URLs à supprimer
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier que l'avis existe et appartient à l'utilisateur
      const { data: review, error: reviewError } = await supabase
        .from('user_reviews')
        .select('id, author_id, photos')
        .eq('id', reviewId)
        .eq('author_id', userId)
        .single();

      if (reviewError || !review) {
        return res.status(404).json({
          success: false,
          message: 'Avis non trouvé ou vous n\'êtes pas autorisé à le modifier'
        });
      }

      const existingPhotos = review.photos || [];

      if (existingPhotos.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Aucune photo à supprimer'
        });
      }

      // Si aucune URL spécifique n'est fournie, supprimer toutes les photos
      const urlsToDelete = photoUrls && Array.isArray(photoUrls) ? photoUrls : existingPhotos;

      // Vérifier que toutes les URLs à supprimer existent dans l'avis
      const invalidUrls = urlsToDelete.filter((url: string) => !existingPhotos.includes(url));
      if (invalidUrls.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Certaines photos spécifiées n\'existent pas dans cet avis'
        });
      }

      // Supprimer les photos du storage
      await deleteReviewPhotos(userId, urlsToDelete);

      // Mettre à jour l'avis en retirant les URLs supprimées
      const remainingPhotos = existingPhotos.filter((url: string) => !urlsToDelete.includes(url));

      const { error: updateError } = await supabase
        .from('user_reviews')
        .update({
          photos: remainingPhotos.length > 0 ? remainingPhotos : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', reviewId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de l\'avis après suppression des photos:', updateError);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour de l\'avis'
        });
      }

      // Invalider le cache
      await invalidateReviewCache(reviewId);

      logger.info('Photos d\'avis supprimées avec succès', {
        userId,
        reviewId,
        deletedCount: urlsToDelete.length,
        remainingCount: remainingPhotos.length
      });

      res.json({
        success: true,
        deleted_photos: urlsToDelete,
        remaining_photos: remainingPhotos,
        message: `${urlsToDelete.length} photo(s) supprimée(s) avec succès`
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression des photos d\'avis:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression des photos'
      });
    }
  },
};