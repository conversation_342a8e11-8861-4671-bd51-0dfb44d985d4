import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, X } from 'lucide-react';
import { <PERSON><PERSON>ield, Button, Tooltip, CircularProgress } from '@mui/material';
import { notify } from '../../components/Notification';
import AiGenerationSystem from '../../components/ai/AiGenerationSystem';
import { useAiCredits } from '../../hooks/useAiCredits';
import useContentModeration from '../../hooks/useContentModeration';
import { getCommonHeaders } from '../../utils/headers';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import logger from '../../utils/logger';
import IconButton from '@mui/material/IconButton';
import { CircleHelp } from 'lucide-react';

interface SloganSectionProps {
  isOwnProfil: boolean;
  profil: any;
  setProfil: React.Dispatch<React.SetStateAction<any>>;
}

const SloganSection: React.FC<SloganSectionProps> = ({ isOwnProfil, profil, setProfil }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [slogan, setSlogan] = useState(profil?.slogan || '');
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isModerating, setIsModerating] = useState(false);
  const { credits } = useAiCredits();
  const { validateContentSafety } = useContentModeration();

  const handleEditClick = () => {
    setIsEditing(true);
    setSlogan(profil?.slogan || '');
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    setSlogan(profil?.slogan || '');
  };

  const handleSaveClick = async () => {
    try {
      setIsSaving(true);

      // Vérifier si le slogan est vide ou ne contient que des espaces
      if (!slogan.trim()) {
        notify('Le slogan ne peut pas être vide', 'error');
        setIsSaving(false);
        return;
      }

      // Vérifier la sécurité du contenu avec le hook useContentModeration
      setIsModerating(true);
      const isSafe = await validateContentSafety(slogan, 'profile');
      setIsModerating(false);
      if (!isSafe) {
        setIsSaving(false);
        return;
      }

      // Mise à jour du slogan
      const headers = await getCommonHeaders();
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/users/updateProfil`,
        { slogan },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        setProfil((prev: any) => ({ ...prev, slogan }));
        notify('Slogan mis à jour avec succès', 'success');
        setIsEditing(false);
      } else {
        notify(response.data.message || 'Erreur lors de la mise à jour du slogan', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du slogan:', error);
      notify('Erreur lors de la mise à jour du slogan', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAiGeneration = () => {
    setIsAiConfirmModalOpen(true);
  };

  const handleAiComplete = (generatedContent: string | null) => {
    setIsAiConfirmModalOpen(false);
    if (generatedContent) {
      setSlogan(generatedContent);
      // Activer le mode édition pour permettre à l'utilisateur de vérifier et enregistrer
      setIsEditing(true);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 mb-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-gray-800">Slogan</h3>
        {isOwnProfil && !isEditing && (
          <div className="flex space-x-2">
            <button
              onClick={handleEditClick}
              className="flex-1 lg:flex-initial px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors whitespace-nowrap w-fit"
              aria-label="Modifier le slogan"
            >
              Modifier
            </button>
          </div>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <TextField
            fullWidth
            variant="outlined"
            value={slogan}
            onChange={(e) => setSlogan(e.target.value.slice(0, 80))}
            placeholder="Entrez votre slogan (80 caractères max)"
            InputProps={{
              inputProps: { maxLength: 80 },
              endAdornment: slogan ? (
                <IconButton
                  aria-label="clear slogan"
                  onClick={() => setSlogan('')}
                  edge="end"
                >
                  <X size={20} />
                </IconButton>
              ) : null,
            }}
            helperText={`${slogan.length}/80 caractères`}
          />
          <div className="flex flex-col sm:flex-row gap-3 justify-end mt-2 sm:mt-0">
            <Tooltip title={credits > 0 ? `${profil?.slogan ? 'Améliorer' : 'Générer'} avec l'IA (1 crédit)` : `Pas assez de crédits IA (${credits}/1)`}>
              <span>
                <Button
                  onClick={handleAiGeneration}
                  disabled={credits < 1 || isSaving}
                  sx={{
                    backgroundColor: '#FFF8F3',
                    color: '#FF6B2C',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    gap: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    whiteSpace: 'nowrap',
                    width: '100%',
                    textTransform: 'none',
                    '@media (min-width: 640px)': {
                      width: 'auto',
                    },
                    '&:hover': {
                      backgroundColor: '#FFE4BA',
                    },
                  }}
                >
                  <Sparkles size={16} />{slogan ? 'Améliorer avec IA' : 'Générer avec IA'}
                  <CircleHelp size={16} className="ml-1" aria-label="Améliorer le contenu existant avec l'IA" />
                </Button>
              </span>
            </Tooltip>
            <div className="flex space-x-2">
              <Button
                onClick={handleCancelClick}
                disabled={isSaving}
                sx={{
                  color: '#6B7280',
                  border: 'none',
                  backgroundColor: 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  whiteSpace: 'nowrap',
                  width: '100%',
                  textTransform: 'none',
                  '@media (min-width: 640px)': {
                    width: 'auto',
                  },
                  '&:hover': {
                    color: '#4B5563',
                    backgroundColor: '#F3F4F6',
                  },
                }}
              >
                <X size={16} className="mr-2" />Annuler
              </Button>
              <Button
                variant="contained"
                onClick={handleSaveClick}
                startIcon={isSaving ? <CircularProgress size={16} /> : <Save size={16} />}
                disabled={isSaving || isModerating}
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  borderRadius: '8px',
                  padding: '8px 24px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  whiteSpace: 'nowrap',
                  width: '100%',
                  textTransform: 'none',
                  '@media (min-width: 640px)': {
                    width: 'auto',
                  },
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                    boxShadow: '0 10px 15px rgba(0, 0, 0, 0.1)',
                  },
                }}
              >
                {isModerating ? 'En modération...' : 'Enregistrer'}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-[#FFF8F3] rounded-lg">
          {profil?.slogan ? (
            <p className="text-lg font-medium text-gray-700 italic">"{profil.slogan}"</p>
          ) : (
            <p className="text-gray-500 italic">
              {isOwnProfil
                ? "Vous n'avez pas encore défini de slogan. Ajoutez un slogan accrocheur pour mettre en valeur votre profil !"
                : "Cet utilisateur n'a pas encore défini de slogan."}
            </p>
          )}
        </div>
      )}

      {/* Système de génération IA pour le slogan */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="slogan"
          prompt={`
            ${profil?.slogan ?
              `⚠️ ALERTE MAXIMALE: Mon slogan actuel "${slogan}" manque d'impact commercial!
            
              💰 MISSION CRITIQUE: Transformer RADICALEMENT ce slogan en une version EXTRAORDINAIRE
              
              🎯 OBJECTIFS:
              1. RENFORCER LE POUVOIR de chaque mot pour qu'il frappe l'esprit
              2. CAPTURER MON EXPERTISE UNIQUE et mes points forts
              3. INCLURE UN VRAI BÉNÉFICE client ou une promesse forte
              4. CRÉER un effet "WOW" immédiat à la lecture
              
              ⭐ Sois AUDACIEUX! Ce slogan doit être ma signature professionnelle inoubliable.` :
                `🚨 MISSION CRITIQUE: Créer un slogan EXCEPTIONNEL qui me définit professionnellement
                
              💎 EXIGENCES ABSOLUES:
              - IMPACT IMMÉDIAT: Doit captiver dès la première lecture
              - BÉNÉFICE CLIENT CLAIR: Montrer ce que j'apporte à mes clients
              - MÉMORABLE: Utiliser une formulation créative ou un jeu de mots intelligent
              - PERSONNALITÉ UNIQUE: Doit refléter mon style professionnel distinct
              - PROMESSE PUISSANTE: Suggérer un résultat ou une valeur concrète
              
              🏆 Le slogan doit déclencher un "WOW!" et donner envie de faire appel à moi immédiatement.`}
          `}
          onComplete={handleAiComplete}
          onCancel={() => setIsAiConfirmModalOpen(false)}
        />
      )}
    </motion.div>
  );
};

export default SloganSection;
