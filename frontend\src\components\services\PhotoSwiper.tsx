// Composant Swiper pour les photos des jobbeurs
import React, { useState, useMemo } from 'react';
import {
  Box,
  CardMedia,
  IconButton
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight
} from '@mui/icons-material';

interface Provider {
  user_id: string;
  slug: string;
  nom: string;
  prenom: string;
  photo_url: string;
  featured_photos?: Array<{
    id: string;
    photo_url: string;
    caption: string;
    created_at: string;
  }>;
  galleries?: Array<{
    id: string;
    name: string;
    description: string;
    cover_image: string;
    photos: Array<{
      id: string;
      photo_url: string;
      caption: string;
      order_index: number;
    }>;
  }>;
}

interface PhotoSwiperProps {
  provider: Provider;
}

const PhotoSwiper: React.FC<PhotoSwiperProps> = ({ provider }) => {
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);

  // Collecter toutes les photos disponibles
  const allPhotos = useMemo(() => {
    const photos: Array<{ url: string; caption?: string; type: 'profile' | 'featured' | 'gallery' }> = [];

    // Photo de profil en premier
    if (provider.photo_url) {
      photos.push({
        url: provider.photo_url,
        caption: 'Photo de profil',
        type: 'profile'
      });
    }

    // Photos mises en avant
    if (provider.featured_photos) {
      provider.featured_photos.forEach(photo => {
        photos.push({
          url: photo.photo_url,
          caption: photo.caption || 'Photo mise en avant',
          type: 'featured'
        });
      });
    }

    // Photos des galeries
    if (provider.galleries) {
      provider.galleries.forEach(gallery => {
        if (gallery.photos) {
          gallery.photos
            .sort((a, b) => a.order_index - b.order_index)
            .forEach(photo => {
              photos.push({
                url: photo.photo_url,
                caption: photo.caption || `${gallery.name}`,
                type: 'gallery'
              });
            });
        }
      });
    }

    return photos;
  }, [provider]);

  const handlePrevPhoto = (e: React.MouseEvent) => {
    e.stopPropagation(); // Empêcher la propagation vers la carte
    setCurrentPhotoIndex(prev => prev === 0 ? allPhotos.length - 1 : prev - 1);
  };

  const handleNextPhoto = (e: React.MouseEvent) => {
    e.stopPropagation(); // Empêcher la propagation vers la carte
    setCurrentPhotoIndex(prev => prev === allPhotos.length - 1 ? 0 : prev + 1);
  };

  if (allPhotos.length === 0) {
    return (
      <CardMedia
        component="img"
        height="200"
        image="/images/default-avatar.jpg"
        alt={`${provider.prenom} ${provider.nom.charAt(0)}.`}
        sx={{
          objectFit: 'cover',
          borderRadius: '12px 12px 0 0'
        }}
      />
    );
  }

  return (
    <Box sx={{ position: 'relative', width: '100%', height: 200, overflow: 'hidden', borderRadius: '12px 12px 0 0' }}>
      <CardMedia
        component="img"
        height="200"
        image={allPhotos[currentPhotoIndex].url}
        alt={allPhotos[currentPhotoIndex].caption}
        sx={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          objectPosition: 'center',
          transition: 'transform 0.3s ease',
        }}
      />

      {/* Indicateurs de photos */}
      {allPhotos.length > 1 && (
        <>
          {/* Boutons de navigation */}
          <IconButton
            onClick={handlePrevPhoto}
            onMouseDown={(e) => e.stopPropagation()} // Empêcher la propagation
            sx={{
              position: 'absolute',
              left: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              width: 32,
              height: 32,
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)'
              }
            }}
          >
            <ChevronLeft />
          </IconButton>

          <IconButton
            onClick={handleNextPhoto}
            onMouseDown={(e) => e.stopPropagation()} // Empêcher la propagation
            sx={{
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              width: 32,
              height: 32,
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)'
              }
            }}
          >
            <ChevronRight />
          </IconButton>

          {/* Indicateurs de position */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 8,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 0.5,
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: 2,
              px: 1,
              py: 0.5
            }}
          >
            {allPhotos.map((_, index) => (
              <Box
                key={index}
                onClick={(e) => {
                  e.stopPropagation(); // Empêcher la propagation vers la carte
                  setCurrentPhotoIndex(index);
                }}
                sx={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  bgcolor: index === currentPhotoIndex ? 'white' : 'rgba(255, 255, 255, 0.5)',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              />
            ))}
          </Box>

          {/* Compteur de photos */}
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              fontSize: '0.75rem',
              fontWeight: 600
            }}
          >
            {currentPhotoIndex + 1}/{allPhotos.length}
          </Box>
        </>
      )}
    </Box>
  );
};

export default PhotoSwiper;
