import React, { useState, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  Slider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider,
  Paper,
  Grid,
  IconButton,
  Switch,
  FormControlLabel,
  CircularProgress,
  useMediaQuery,
  Drawer,
  AppBar,
  Toolbar,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  CardElement,
  TextElement,
  ImageElement,
  ShapeElement,
  QRCodeElement
} from '../../types/cardEditor';
import {
  Delete,
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  ExpandLess,
  ExpandMore,
  Crop,
  ArrowUpward,
  ArrowDownward,
  Upload,
  Image as ImageIcon,
  AutoAwesome as Sparkles
} from '@mui/icons-material';
import axios from 'axios';
import { notify } from '../Notification';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders, getMultipartHeaders } from '../../utils/headers';
import { useParams } from 'react-router-dom';
import AiImageGenerationModal from '../ai/AiImageGenerationModal';
import { logger } from '@/utils/logger';
import cardEditorService from '../../services/cardEditorService';
import { fetchCsrfToken } from '../../services/csrf';
import { debounce } from 'lodash';
import useUserProfile from '../../hooks/useUserProfileCarteVisite';

interface PropertiesPanelProps {
  selectedElement: CardElement | null;
  onElementUpdate: (element: CardElement) => void;
  onElementDelete: (elementId: string) => void;
  onElementBringToFront: (elementId: string) => void;
  onElementSendToBack: (elementId: string) => void;
  isMobileDrawerOpen?: boolean;
  onMobileDrawerClose?: () => void;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedElement,
  onElementUpdate,
  onElementDelete,
  onElementBringToFront,
  onElementSendToBack,
  isMobileDrawerOpen = false,
  onMobileDrawerClose
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [showAdvancedText, setShowAdvancedText] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [isAiModalOpen, setIsAiModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { id: templateId } = useParams();
  const { profileData } = useUserProfile();

  // Si aucun élément n'est sélectionné, afficher un message
  if (!selectedElement) {
    return (
      <Paper
        sx={{
          p: 3,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
          backgroundColor: '#f9f9f9',
          borderRadius: 0
        }}
      >
        <Box
          sx={{
            width: 80,
            height: 80,
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 107, 44, 0.1)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <Crop sx={{ fontSize: 40, color: 'primary.main', opacity: 0.7 }} />
        </Box>
        <Typography
          variant="subtitle1"
          align="center"
          color="text.secondary"
          sx={{ maxWidth: 220 }}
        >
          Sélectionnez un élément sur le canvas pour modifier ses propriétés
        </Typography>
      </Paper>
    );
  }

  // Fonction pour mettre à jour une propriété d'un élément
  const updateProperty = (key: string, value: any) => {
    const updatedElement = { ...selectedElement };

    if (key.includes('.')) {
      // Propriété imbriquée (ex: properties.text)
      const [parent, child] = key.split('.');
      if (parent === 'properties' && typeof updatedElement.properties === 'object') {
        updatedElement.properties = {
          ...updatedElement.properties,
          [child]: value
        };
      }
    } else {
      // Propriété directe (ex: x, y)
      (updatedElement as any)[key] = value;
    }

    onElementUpdate(updatedElement);
  };

  const debouncedUpdateProperty = React.useCallback(
    debounce((key: string, value: any) => {
      if (selectedElement) {
        const currentElementSnapshot = selectedElement;
        const updatedElement = { ...currentElementSnapshot };

        if (key.includes('.')) {
          const [parent, child] = key.split('.');
          if (parent === 'properties' && typeof updatedElement.properties === 'object') {
            updatedElement.properties = {
              ...updatedElement.properties,
              [child]: value
            };
          }
        } else {
          (updatedElement as any)[key] = value;
        }
        onElementUpdate(updatedElement);
      }
    }, 300),
    [onElementUpdate, selectedElement]
  );

  // Rendu conditionnel selon le type d'élément
  const renderProperties = () => {
    switch (selectedElement.type) {
      case 'text':
        return renderTextProperties(selectedElement as TextElement, showAdvancedText, setShowAdvancedText);
      case 'image':
        return renderImageProperties(
          selectedElement as ImageElement,
          showFilters,
          setShowFilters,
          uploading,
          setUploading,
          isAiModalOpen,
          setIsAiModalOpen,
          fileInputRef as React.RefObject<HTMLInputElement>,
          templateId
        );
      case 'shape':
        return renderShapeProperties(selectedElement as ShapeElement);
      case 'qrcode':
        return renderQRCodeProperties(selectedElement as QRCodeElement);
      default:
        return null;
    }
  };

  // Propriétés pour les éléments de texte
  const renderTextProperties = (element: TextElement, showAdvanced: boolean, setShowAdvanced: (v: boolean) => void) => {
    const { properties } = element;
    return (
      <>
        <Typography variant="h6" gutterBottom>
          Texte
        </Typography>

        <TextField
          label="Contenu"
          fullWidth
          multiline
          rows={3}
          value={properties.text}
          onChange={(e) => updateProperty('properties.text', e.target.value)}
          margin="normal"
        />

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Taille de police</Typography>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <Typography variant="caption" color="text.secondary">8</Typography>
            <Slider
              value={properties.fontSize}
              min={8}
              max={72}
              step={1}
              onChange={(_, value) => updateProperty('properties.fontSize', value)}
              valueLabelDisplay="auto"
              sx={{
                flexGrow: 1,
                '& .MuiSlider-thumb': {
                  transition: 'none',
                },
                '& .MuiSlider-track': {
                  transition: 'none',
                }
              }}
            />
            <Typography variant="caption" color="text.secondary">72</Typography>
            <Typography
              variant="body2"
              sx={{
                minWidth: 30,
                textAlign: 'right',
                fontWeight: 500
              }}
            >
              {properties.fontSize}
            </Typography>
          </Box>
        </Box>

        <FormControl fullWidth margin="normal">
          <InputLabel>Police</InputLabel>
          <Select
            value={properties.fontFamily}
            onChange={(e) => updateProperty('properties.fontFamily', e.target.value)}
            label="Police"
          >
            <MenuItem value="Arial">Arial</MenuItem>
            <MenuItem value="Helvetica">Helvetica</MenuItem>
            <MenuItem value="Times New Roman">Times New Roman</MenuItem>
            <MenuItem value="Courier New">Courier New</MenuItem>
            <MenuItem value="Georgia">Georgia</MenuItem>
            <MenuItem value="Verdana">Verdana</MenuItem>
            <MenuItem value="Impact">Impact</MenuItem>
            <MenuItem value="Tahoma">Tahoma</MenuItem>
            <MenuItem value="Trebuchet MS">Trebuchet MS</MenuItem>
            <MenuItem value="Comic Sans MS">Comic Sans MS</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth margin="normal">
          <InputLabel>Alignement</InputLabel>
          <Select
            value={properties.align}
            onChange={(e) => updateProperty('properties.align', e.target.value)}
            label="Alignement"
          >
            <MenuItem value="left">Gauche</MenuItem>
            <MenuItem value="center">Centre</MenuItem>
            <MenuItem value="right">Droite</MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ mr: 2 }}>Couleur:</Typography>
          <input
            type="color"
            value={properties.fill === 'transparent' ? '#000000' : properties.fill || '#000000'}
            onInput={(e: React.ChangeEvent<HTMLInputElement>) => debouncedUpdateProperty('properties.fill', e.target.value)}
            style={{
              width: 40,
              height: 40,
            }}
          />
        </Box>

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <IconButton
            color={properties.fontStyle === 'bold' ? 'primary' : 'default'}
            onClick={() => updateProperty('properties.fontStyle', properties.fontStyle === 'bold' ? 'normal' : 'bold')}
          >
            <FormatBold />
          </IconButton>
          <IconButton
            color={properties.fontStyle === 'italic' ? 'primary' : 'default'}
            onClick={() => updateProperty('properties.fontStyle', properties.fontStyle === 'italic' ? 'normal' : 'italic')}
          >
            <FormatItalic />
          </IconButton>
          <IconButton
            color={properties.textDecoration === 'underline' ? 'primary' : 'default'}
            onClick={() => updateProperty('properties.textDecoration', properties.textDecoration === 'underline' ? '' : 'underline')}
          >
            <FormatUnderlined />
          </IconButton>
        </Box>

        <Box sx={{ mt: 3 }}>
          <Button
            variant="outlined"
            fullWidth
            onClick={() => setShowAdvanced(!showAdvanced)}
            endIcon={showAdvanced ? <ExpandLess /> : <ExpandMore />}
          >
            {showAdvanced ? 'Masquer les options avancées' : 'Afficher les options avancées'}
          </Button>
        </Box>

        {showAdvanced && (
          <>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom>
              Options avancées
            </Typography>

            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Espacement des lettres</Typography>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Typography variant="caption" color="text.secondary">-5</Typography>
                <Slider
                  value={properties.letterSpacing || 0}
                  min={-5}
                  max={20}
                  step={0.5}
                  onChange={(_, value) => updateProperty('properties.letterSpacing', value)}
                  valueLabelDisplay="auto"
                  sx={{
                    flexGrow: 1,
                    '& .MuiSlider-thumb': {
                      transition: 'none',
                    },
                    '& .MuiSlider-track': {
                      transition: 'none',
                    }
                  }}
                />
                <Typography variant="caption" color="text.secondary">20</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    minWidth: 30,
                    textAlign: 'right',
                    fontWeight: 500
                  }}
                >
                  {properties.letterSpacing || 0}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Hauteur de ligne</Typography>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Typography variant="caption" color="text.secondary">0.5</Typography>
                <Slider
                  value={properties.lineHeight || 1}
                  min={0.5}
                  max={3}
                  step={0.1}
                  onChange={(_, value) => updateProperty('properties.lineHeight', value)}
                  valueLabelDisplay="auto"
                  sx={{
                    flexGrow: 1,
                    '& .MuiSlider-thumb': {
                      transition: 'none',
                    },
                    '& .MuiSlider-track': {
                      transition: 'none',
                    }
                  }}
                />
                <Typography variant="caption" color="text.secondary">3</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    minWidth: 30,
                    textAlign: 'right',
                    fontWeight: 500
                  }}
                >
                  {properties.lineHeight || 1}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Opacité</Typography>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Typography variant="caption" color="text.secondary">0</Typography>
                <Slider
                  value={properties.opacity || 1}
                  min={0}
                  max={1}
                  step={0.01}
                  onChange={(_, value) => updateProperty('properties.opacity', value)}
                  valueLabelDisplay="auto"
                  sx={{
                    flexGrow: 1,
                    '& .MuiSlider-thumb': {
                      transition: 'none',
                    },
                    '& .MuiSlider-track': {
                      transition: 'none',
                    }
                  }}
                />
                <Typography variant="caption" color="text.secondary">1</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    minWidth: 30,
                    textAlign: 'right',
                    fontWeight: 500
                  }}
                >
                  {properties.opacity || 1}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rembourrage</Typography>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Typography variant="caption" color="text.secondary">0</Typography>
                <Slider
                  value={properties.padding || 0}
                  min={0}
                  max={20}
                  step={1}
                  onChange={(_, value) => updateProperty('properties.padding', value)}
                  valueLabelDisplay="auto"
                  sx={{
                    flexGrow: 1,
                    '& .MuiSlider-thumb': {
                      transition: 'none',
                    },
                    '& .MuiSlider-track': {
                      transition: 'none',
                    }
                  }}
                />
                <Typography variant="caption" color="text.secondary">20</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    minWidth: 30,
                    textAlign: 'right',
                    fontWeight: 500
                  }}
                >
                  {properties.padding || 0}
                </Typography>
              </Box>
            </Box>

            <FormControl fullWidth margin="normal">
              <InputLabel>Retour à la ligne</InputLabel>
              <Select
                value={properties.wrap || 'word'}
                onChange={(e) => updateProperty('properties.wrap', e.target.value)}
                label="Retour à la ligne"
              >
                <MenuItem value="word">Par mot</MenuItem>
                <MenuItem value="char">Par caractère</MenuItem>
                <MenuItem value="none">Aucun</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={properties.ellipsis || false}
                  onChange={(e) => updateProperty('properties.ellipsis', e.target.checked)}
                  color="primary"
                />
              }
              label="Ajouter des points de suspension"
              sx={{ mt: 1 }}
            />

            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              Ombre du texte
            </Typography>

            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label="Couleur"
                  type="color"
                  fullWidth
                  value={(properties.textShadow?.color) === 'transparent' ? '#000000' : (properties.textShadow?.color) || '#000000'}
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const currentShadow = properties.textShadow || { color: '#000000', blur: 5, offset: { x: 2, y: 2 }, opacity: 0.5 };
                    debouncedUpdateProperty('properties.textShadow', { ...currentShadow, color: e.target.value });
                  }}
                  margin="normal"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Flou"
                  type="number"
                  fullWidth
                  value={(properties.textShadow?.blur) || 0}
                  onChange={(e) => {
                    const currentShadow = properties.textShadow || { color: '#000000', blur: 0, offset: { x: 0, y: 0 }, opacity: 0 };
                    updateProperty('properties.textShadow', { ...currentShadow, blur: Number(e.target.value) });
                  }}
                  margin="normal"
                  inputProps={{ min: 0, max: 30 }}
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Décalage X"
                  type="number"
                  fullWidth
                  value={(properties.textShadow?.offset?.x) || 0}
                  onChange={(e) => {
                    const currentShadow = properties.textShadow || { color: '#000000', blur: 0, offset: { x: 0, y: 0 }, opacity: 0 };
                    updateProperty('properties.textShadow', {
                      ...currentShadow,
                      offset: {
                        x: Number(e.target.value),
                        y: currentShadow.offset?.y || 0
                      }
                    });
                  }}
                  margin="normal"
                  inputProps={{ min: -20, max: 20 }}
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Décalage Y"
                  type="number"
                  fullWidth
                  value={(properties.textShadow?.offset?.y) || 0}
                  onChange={(e) => {
                    const currentShadow = properties.textShadow || { color: '#000000', blur: 0, offset: { x: 0, y: 0 }, opacity: 0 };
                    updateProperty('properties.textShadow', {
                      ...currentShadow,
                      offset: {
                        x: currentShadow.offset?.x || 0,
                        y: Number(e.target.value)
                      }
                    });
                  }}
                  margin="normal"
                  inputProps={{ min: -20, max: 20 }}
                />
              </Grid>
              <Grid size={12}>
                <Typography gutterBottom>Opacité de l'ombre</Typography>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Typography variant="caption" color="text.secondary">0</Typography>
                  <Slider
                    value={(properties.textShadow?.opacity) || 0}
                    min={0}
                    max={1}
                    step={0.01}
                    onChange={(_, value) => {
                      const currentShadow = properties.textShadow || { color: '#000000', blur: 5, offset: { x: 2, y: 2 }, opacity: 0 };
                      updateProperty('properties.textShadow', { ...currentShadow, opacity: value as number });
                    }}
                    valueLabelDisplay="auto"
                    sx={{
                      flexGrow: 1,
                      '& .MuiSlider-thumb': {
                        transition: 'none',
                      },
                      '& .MuiSlider-track': {
                        transition: 'none',
                      }
                    }}
                  />
                  <Typography variant="caption" color="text.secondary">1</Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      minWidth: 30,
                      textAlign: 'right',
                      fontWeight: 500
                    }}
                  >
                    {(properties.textShadow?.opacity) || 0}
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              Contour du texte
            </Typography>

            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label="Couleur du contour"
                  type="color"
                  fullWidth
                  value={properties.stroke === 'transparent' ? '#000000' : properties.stroke || '#000000'}
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    debouncedUpdateProperty('properties.stroke', e.target.value);
                  }}
                  margin="normal"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Épaisseur"
                  type="number"
                  fullWidth
                  value={properties.strokeWidth || 0}
                  onChange={(e) => {
                    updateProperty('properties.strokeWidth', Number(e.target.value));
                  }}
                  margin="normal"
                  inputProps={{ min: 0, max: 10 }}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              Ombre directe
            </Typography>

            <Grid container spacing={2}>
              <Grid size={6}>
                <TextField
                  label="Couleur de l'ombre"
                  type="color"
                  fullWidth
                  value={properties.shadowColor === 'transparent' ? '#000000' : properties.shadowColor || '#000000'}
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    debouncedUpdateProperty('properties.shadowColor', e.target.value);
                  }}
                  margin="normal"
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Flou de l'ombre"
                  type="number"
                  fullWidth
                  value={properties.shadowBlur || 0}
                  onChange={(e) => {
                    updateProperty('properties.shadowBlur', Number(e.target.value));
                  }}
                  margin="normal"
                  inputProps={{ min: 0, max: 30 }}
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Décalage X"
                  type="number"
                  fullWidth
                  value={properties.shadowOffset?.x || 0}
                  onChange={(e) => {
                    const currentOffset = properties.shadowOffset || { x: 0, y: 0 };
                    updateProperty('properties.shadowOffset', {
                      ...currentOffset,
                      x: Number(e.target.value)
                    });
                  }}
                  margin="normal"
                  inputProps={{ min: -20, max: 20 }}
                />
              </Grid>
              <Grid size={6}>
                <TextField
                  label="Décalage Y"
                  type="number"
                  fullWidth
                  value={properties.shadowOffset?.y || 0}
                  onChange={(e) => {
                    const currentOffset = properties.shadowOffset || { x: 0, y: 0 };
                    updateProperty('properties.shadowOffset', {
                      ...currentOffset,
                      y: Number(e.target.value)
                    });
                  }}
                  margin="normal"
                  inputProps={{ min: -20, max: 20 }}
                />
              </Grid>
            </Grid>
          </>
        )}
      </>
    );
  };

  // Propriétés pour les éléments d'image

  // Gérer l'upload d'image
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Empêcher l'upload si templateId non défini
    if (!templateId) {
      notify("Impossible d'uploader l'image : création du template en cours...", 'error');
      return;
    }

    const file = files[0];

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      notify('Image uploadée avec succès', 'success');
      return;
    }

    // Vérifier la taille du fichier (max 10 MB)
    if (file.size > 10 * 1024 * 1024) {
      notify('Erreur lors de l\'upload de l\'image', 'error');
      return;
    }

    try {
      setUploading(true);

      // Supprimer l'ancienne image du bucket si elle existe et n'est pas l'avatar par défaut
      const currentSrc = (selectedElement as ImageElement).properties.src;
      const isDefaultAvatar = currentSrc && currentSrc.includes('avatar-defaut-jobpartiel.jpg');
      const isUserProfilePhoto = currentSrc && profileData?.photo_url && currentSrc === profileData.photo_url;
      if (currentSrc && templateId && !isDefaultAvatar && !isUserProfilePhoto) {
        await cardEditorService.deleteImage(templateId, currentSrc);
      }

      const formData = new FormData();
      formData.append('file', file);

      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/card-editor/${templateId}/upload`,
        formData,
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        updateProperty('properties.src', response.data.data.url);
        notify('Image uploadée avec succès', 'success');
      } else {
        notify('Erreur lors de l\'upload de l\'image', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de l\'upload de l\'image:', error);
      notify('Erreur lors de l\'upload de l\'image', 'error');
    } finally {
      setUploading(false);
      // Réinitialiser l'input file
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Gérer l'ouverture du modal de génération d'image par IA
  const handleOpenAiModal = () => {
    setIsAiModalOpen(true);
  };

  // Gérer la fermeture du modal de génération d'image par IA
  const handleCloseAiModal = () => {
    setIsAiModalOpen(false);
  };

  // Gérer la génération d'image par IA
  const handleAiImageGenerated = async (imageUrl: string, imageBase64: string) => {
    if (imageUrl) {
      const currentSrc = (selectedElement as ImageElement).properties.src;
      const isDefaultAvatar = currentSrc && currentSrc.includes('avatar-defaut-jobpartiel.jpg');
      const isUserProfilePhoto = currentSrc && profileData?.photo_url && currentSrc === profileData.photo_url;
      if (currentSrc && templateId && !isDefaultAvatar && !isUserProfilePhoto) {
        await cardEditorService.deleteImage(templateId, currentSrc);
      }
      // Si l'image provient du bucket temporaire, on la confirme pour la transférer dans le bucket final
      if (imageUrl.includes('/api/storage-proxy/temp_moderation/')) {
        try {
          const headers = await getCommonHeaders();
          const response = await axios.post(
            `${API_CONFIG.baseURL}/api/ai-image-generation/confirm`,
            {
              imageUrl,
              purpose: 'card_editor',
              templateId
            },
            {
              headers,
              withCredentials: true
            }
          );
          if (response.data && response.data.finalImageUrl) {
            updateProperty('properties.src', response.data.finalImageUrl);
            logger.info('Image IA transférée dans le bucket final');
          } else {
            updateProperty('properties.src', imageUrl);
            logger.info('Image IA générée mais non transférée (pas d\'URL finale)');
          }
        } catch (err: any) {
          updateProperty('properties.src', imageUrl);
          logger.error('Erreur lors du transfert de l\'image IA dans le bucket final', err);
        }
      } else {
        updateProperty('properties.src', imageUrl);
        notify('Image générée avec succès', 'success');
      }
      setIsAiModalOpen(false);
    }
  };

  // Gérer l'ajout d'un filtre
  const handleAddFilter = (filterType: string) => {
    const newFilter = {
      type: filterType,
      params: getDefaultFilterParams(filterType)
    };

    const currentFilters: any[] = ((selectedElement as ImageElement).properties.filters ?? []) as any[];
    updateProperty('properties.filters', [...currentFilters, newFilter]);
  };

  // Gérer la suppression d'un filtre
  const handleRemoveFilter = (index: number) => {
    const currentFiltersRemove: any[] = ((selectedElement as ImageElement).properties.filters ?? []) as any[];
    currentFiltersRemove.splice(index, 1);
    updateProperty('properties.filters', currentFiltersRemove);
  };

  // Gérer la mise à jour des paramètres d'un filtre
  const handleUpdateFilterParams = (index: number, paramName: string, value: any) => {
    const currentFiltersUpdate: any[] = ((selectedElement as ImageElement).properties.filters ?? []) as any[];
    currentFiltersUpdate[index] = {
      ...currentFiltersUpdate[index],
      params: {
        ...currentFiltersUpdate[index].params,
        [paramName]: value
      }
    };
    updateProperty('properties.filters', currentFiltersUpdate);
  };

  // Obtenir les paramètres par défaut pour un type de filtre
  const getDefaultFilterParams = (filterType: string): any => {
    switch (filterType) {
      case 'blur':
        return { blurRadius: 5 };
      case 'brighten':
        return { brightness: 0.3 };
      case 'contrast':
        return { contrast: 10 };
      case 'emboss':
        return { embossStrength: 0.5, embossWhiteLevel: 0.5, embossDirection: 'top-left', embossBlend: true };
      case 'enhance':
        return { enhance: 0.5 };
      case 'grayscale':
        return {};
      case 'hsl':
        return { hue: 0, saturation: 0, luminance: 0 };
      case 'hsv':
        return { hue: 0, saturation: 0, value: 0 };
      case 'invert':
        return {};
      case 'kaleidoscope':
        return { kaleidoscopePower: 2, kaleidoscopeAngle: 0 };
      case 'noise':
        return { noise: 0.2 };
      case 'pixelate':
        return { pixelSize: 8 };
      case 'rgb':
        return { red: 1, green: 1, blue: 1 };
      case 'sepia':
        return {};
      case 'solarize':
        return {};
      case 'threshold':
        return { threshold: 0.5 };
      default:
        return {};
    }
  };

  // Rendu des contrôles pour un filtre spécifique
  const renderFilterControls = (filter: any, index: number) => {
    switch (filter.type) {
      case 'blur':
        return (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Rayon de flou</Typography>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <Typography variant="caption" color="text.secondary">0</Typography>
              <Slider
                value={filter.params.blurRadius}
                min={0}
                max={20}
                step={0.1}
                onChange={(_, value) => handleUpdateFilterParams(index, 'blurRadius', value as number)}
                valueLabelDisplay="auto"
                sx={{
                  flexGrow: 1,
                  '& .MuiSlider-thumb': {
                    transition: 'none',
                  },
                  '& .MuiSlider-track': {
                    transition: 'none',
                  }
                }}
              />
              <Typography variant="caption" color="text.secondary">20</Typography>
              <Typography
                variant="body2"
                sx={{
                  minWidth: 30,
                  textAlign: 'right',
                  fontWeight: 500
                }}
              >
                {filter.params.blurRadius}
              </Typography>
            </Box>
          </Box>
        );
      case 'brighten':
        return (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Luminosité</Typography>
            <Slider
              value={filter.params.brightness}
              min={-1}
              max={1}
              step={0.05}
              onChange={(_, value) => handleUpdateFilterParams(index, 'brightness', value as number)}
              valueLabelDisplay="auto"
            />
          </Box>
        );
      case 'contrast':
        return (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Contraste</Typography>
            <Slider
              value={filter.params.contrast}
              min={-100}
              max={100}
              step={1}
              onChange={(_, value) => handleUpdateFilterParams(index, 'contrast', value as number)}
              valueLabelDisplay="auto"
            />
          </Box>
        );
      case 'grayscale':
      case 'invert':
      case 'sepia':
      case 'solarize':
        return (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Ce filtre ne nécessite aucun paramètre.
          </Typography>
        );
      case 'pixelate':
        return (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Taille des pixels</Typography>
            <Slider
              value={filter.params.pixelSize}
              min={1}
              max={32}
              step={1}
              onChange={(_, value) => handleUpdateFilterParams(index, 'pixelSize', value as number)}
              valueLabelDisplay="auto"
            />
          </Box>
        );
      case 'noise':
        return (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Niveau de bruit</Typography>
            <Slider
              value={filter.params.noise}
              min={0}
              max={1}
              step={0.01}
              onChange={(_, value) => handleUpdateFilterParams(index, 'noise', value as number)}
              valueLabelDisplay="auto"
            />
          </Box>
        );
      default:
        return null;
    }
  };

  const renderImageProperties = (
    element: ImageElement,
    showFilters: boolean,
    setShowFilters: React.Dispatch<React.SetStateAction<boolean>>,
    uploading: boolean,
    setUploading: React.Dispatch<React.SetStateAction<boolean>>,
    isAiModalOpen: boolean,
    setIsAiModalOpen: React.Dispatch<React.SetStateAction<boolean>>,
    fileInputRef: React.RefObject<HTMLInputElement>,
    templateId: string | undefined
  ) => {
    const filters: any[] = ((element as ImageElement).properties.filters ?? []) as any[];

    // Fonction pour supprimer l'image du backend et du composant
    const handleDeleteImage = async () => {
      if (!templateId || !element.properties.src) return;
      const ok = await cardEditorService.deleteImage(templateId, element.properties.src);
      if (ok) {
        onElementDelete(element.id);
      }
    };

    return (
      <>
        <Typography variant="h6" gutterBottom>
          Image
        </Typography>

        {/* Logique d'affichage du champ URL de l'image selon la sécurité demandée */}
        {(/jobpartiel/.test(element.properties.src || '') && !/photo_profil\/avatar\/avatar-defaut-jobpartiel\.jpg$/.test(element.properties.src || '')) ? null :
          (/jobpartiel/.test(element.properties.src || '') && /photo_profil\/avatar\/avatar-defaut-jobpartiel\.jpg$/.test(element.properties.src || '')) ? (
            <TextField
              label="Ajouter une URL/Photos/IA"
              fullWidth
              value={''}
              onChange={(e) => updateProperty('properties.src', e.target.value)}
              margin="normal"
              placeholder="URL de l'image"
            />
          ) : (
            <TextField
              label="URL de l'image"
              fullWidth
              value={(element as ImageElement).properties.src}
              onChange={(e) => updateProperty('properties.src', e.target.value)}
              margin="normal"
            />
          )
        }

        {/* Boutons d'upload et de génération d'image */}
        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={uploading ? <CircularProgress size={20} /> : <Upload />}
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading || !templateId}
            sx={{ flex: 1 }}
          >
            {uploading ? 'Upload en cours...' : 'Uploader une image'}
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            accept="image/*"
            onChange={handleImageUpload}
          />

          <Button
            variant="outlined"
            color="primary"
            startIcon={<Sparkles />}
            onClick={() => {
              if (!templateId) {
                notify("Impossible d'utiliser l'IA tant que le template n'est pas créé.", 'error');
                return;
              }
              handleOpenAiModal();
            }}
            sx={{ minWidth: '60px' }}
            disabled={!templateId}
          >
            IA
          </Button>

          {/* Modal de génération d'image par IA */}
          <AiImageGenerationModal
            isOpen={isAiModalOpen}
            onClose={handleCloseAiModal}
            onImageGenerated={handleAiImageGenerated}
            purpose="card_editor"
            defaultPrompt="Image professionnelle pour carte de visite ou flyer"
          />
        </Box>

        {/* Prévisualisation de l'image si disponible */}
        {(element as ImageElement).properties.src && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <img
              src={(element as ImageElement).properties.src}
              alt="Prévisualisation"
              style={{
                maxWidth: '100%',
                maxHeight: '150px',
                objectFit: 'contain',
                borderRadius: `${(element as ImageElement).properties.cornerRadius || 0}px`
              }}
            />
            {/* Ne pas afficher le bouton de suppression si c'est l'image de profil */}
            {!profileData?.photo_url || element.properties.src !== profileData.photo_url ? (
              <Button
                variant="outlined"
                color="error"
                startIcon={<Delete />}
                sx={{ mt: 1 }}
                onClick={handleDeleteImage}
                fullWidth
              >
                Supprimer l'image
              </Button>
            ) : null}
          </Box>
        )}

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Rayon des coins</Typography>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <Typography variant="caption" color="text.secondary">0</Typography>
            <Slider
              value={(element as ImageElement).properties.cornerRadius || 0}
              min={0}
              max={50}
              step={1}
              onChange={(_, value) => updateProperty('properties.cornerRadius', value)}
              valueLabelDisplay="auto"
              sx={{
                flexGrow: 1,
                '& .MuiSlider-thumb': {
                  transition: 'none',
                },
                '& .MuiSlider-track': {
                  transition: 'none',
                }
              }}
            />
            <Typography variant="caption" color="text.secondary">50</Typography>
            <Typography
              variant="body2"
              sx={{
                minWidth: 30,
                textAlign: 'right',
                fontWeight: 500
              }}
            >
              {(element as ImageElement).properties.cornerRadius || 0}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 3 }}>
          <Button
            variant="outlined"
            fullWidth
            onClick={() => setShowFilters(!showFilters)}
            endIcon={showFilters ? <ExpandLess /> : <ExpandMore />}
          >
            {showFilters ? 'Masquer les filtres' : 'Afficher les filtres'}
          </Button>
        </Box>

        {showFilters && (
          <>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom>
              Filtres
            </Typography>

            <FormControl fullWidth margin="normal">
              <InputLabel>Ajouter un filtre</InputLabel>
              <Select
                value=""
                onChange={(e) => handleAddFilter(e.target.value)}
                label="Ajouter un filtre"
              >
                <MenuItem value="blur">Flou</MenuItem>
                <MenuItem value="brighten">Luminosité</MenuItem>
                <MenuItem value="contrast">Contraste</MenuItem>
                <MenuItem value="grayscale">Niveaux de gris</MenuItem>
                <MenuItem value="invert">Inverser</MenuItem>
                <MenuItem value="noise">Bruit</MenuItem>
                <MenuItem value="pixelate">Pixeliser</MenuItem>
                <MenuItem value="sepia">Sépia</MenuItem>
                <MenuItem value="solarize">Solariser</MenuItem>
              </Select>
            </FormControl>

            {filters.length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                Aucun filtre appliqué. Sélectionnez un filtre dans la liste ci-dessus.
              </Typography>
            )}

            {filters.map((filter, index) => (
              <Box key={index} sx={{ mt: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle2">
                    {filter.type.charAt(0).toUpperCase() + filter.type.slice(1)}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="error"
                    sx={{ cursor: 'pointer' }}
                    onClick={() => handleRemoveFilter(index)}
                  >
                    Supprimer
                  </Typography>
                </Box>
                {renderFilterControls(filter, index)}
              </Box>
            ))}
          </>
        )}

        {/* Section pour les ombres */}
        <Divider sx={{ my: 2 }} />
        <Typography variant="subtitle1" gutterBottom sx={{mt: 2}}>
          Ombre de l'image
        </Typography>
        <Grid container spacing={2}>
          <Grid size={6}>
            <TextField
              label="Couleur de l'ombre"
              type="color"
              fullWidth
              value={((element as ImageElement).properties.shadow?.color) === 'transparent' ? '#000000' : ((element as ImageElement).properties.shadow?.color) || '#000000'}
              onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                const currentShadow = (element as ImageElement).properties.shadow || { color: '#000000', blur: 0, offsetX: 0, offsetY: 0, opacity: 0 };
                debouncedUpdateProperty('properties.shadow', { ...currentShadow, color: e.target.value });
              }}
              margin="normal"
            />
          </Grid>
          <Grid size={6}>
            <TextField
              label="Flou"
              type="number"
              fullWidth
              value={((element as ImageElement).properties.shadow?.blur) || 0}
              onChange={(e) => {
                const currentShadow = (element as ImageElement).properties.shadow || { color: '#000000', blur: 0, offsetX: 0, offsetY: 0, opacity: 0 };
                updateProperty('properties.shadow', { ...currentShadow, blur: Math.min(Math.max(Number(e.target.value), 0), 50) });
              }}
              margin="normal"
              inputProps={{ min: 0, max: 50 }}
            />
          </Grid>
          <Grid size={6}>
            <TextField
              label="Décalage X"
              type="number"
              fullWidth
              value={((element as ImageElement).properties.shadow?.offsetX) || 0}
              onChange={(e) => {
                const currentShadow = (element as ImageElement).properties.shadow || { color: '#000000', blur: 0, offsetX: 0, offsetY: 0, opacity: 0 };
                updateProperty('properties.shadow', { ...currentShadow, offsetX: Number(e.target.value) });
              }}
              margin="normal"
              inputProps={{ min: -50, max: 50 }}
            />
          </Grid>
          <Grid size={6}>
            <TextField
              label="Décalage Y"
              type="number"
              fullWidth
              value={((element as ImageElement).properties.shadow?.offsetY) || 0}
              onChange={(e) => {
                const currentShadow = (element as ImageElement).properties.shadow || { color: '#000000', blur: 0, offsetX: 0, offsetY: 0, opacity: 0 };
                updateProperty('properties.shadow', { ...currentShadow, offsetY: Number(e.target.value) });
              }}
              margin="normal"
              inputProps={{ min: -50, max: 50 }}
            />
          </Grid>
          <Grid size={12}>
            <Typography gutterBottom>Opacité de l'ombre</Typography>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <Typography variant="caption" color="text.secondary">0</Typography>
              <Slider
                value={((element as ImageElement).properties.shadow?.opacity) || 0}
                min={0}
                max={1}
                step={0.01}
                onChange={(_, value) => {
                  const currentShadow = (element as ImageElement).properties.shadow || { color: '#000000', blur: 0, offsetX: 0, offsetY: 0, opacity: 0 };
                  updateProperty('properties.shadow', { ...currentShadow, opacity: value as number });
                }}
                valueLabelDisplay="auto"
                sx={{
                  flexGrow: 1,
                  '& .MuiSlider-thumb': {
                    transition: 'none',
                  },
                  '& .MuiSlider-track': {
                    transition: 'none',
                  }
                }}
              />
              <Typography variant="caption" color="text.secondary">1</Typography>
              <Typography
                variant="body2"
                sx={{
                  minWidth: 30,
                  textAlign: 'right',
                  fontWeight: 500
                }}
              >
                {(((element as ImageElement).properties.shadow?.opacity) || 0).toFixed(2)}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </>
    );
  };

  // Propriétés pour les éléments de forme
  const renderShapeProperties = (element: ShapeElement) => {
    const shapeType = element.properties.shape;

    return (
      <>
        <Typography variant="h6" gutterBottom>
          Forme
        </Typography>

        <FormControl fullWidth margin="normal">
          <InputLabel>Type de forme</InputLabel>
          <Select
            value={shapeType}
            onChange={(e) => updateProperty('properties.shape', e.target.value)}
            label="Type de forme"
          >
            <MenuItem value="rect">Rectangle</MenuItem>
            <MenuItem value="circle">Cercle</MenuItem>
            <MenuItem value="ellipse">Ellipse</MenuItem>
            <MenuItem value="line">Ligne</MenuItem>
            <MenuItem value="arrow">Flèche</MenuItem>
            <MenuItem value="star">Étoile</MenuItem>
            <MenuItem value="polygon">Polygone</MenuItem>
            <MenuItem value="ring">Anneau</MenuItem>
            <MenuItem value="arc">Arc</MenuItem>
            <MenuItem value="wedge">Secteur</MenuItem>
            <MenuItem value="path">Chemin</MenuItem>
          </Select>
        </FormControl>

        {/* Propriétés de couleur et bordure communes à toutes les formes */}
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ mr: 2 }}>Couleur de remplissage:</Typography>
          <input
            type="color"
            value={shapeType === 'path' && element.properties.fill === 'transparent' ? '#000000' : element.properties.fill}
            onInput={(e: React.ChangeEvent<HTMLInputElement>) => debouncedUpdateProperty('properties.fill', e.target.value)}
            style={{ width: '40px', height: '40px' }}
          />
        </Box>

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ mr: 2 }}>Couleur de bordure:</Typography>
          <input
            type="color"
            value={element.properties.stroke === 'transparent' ? '#000000' : element.properties.stroke || '#000000'}
            onInput={(e: React.ChangeEvent<HTMLInputElement>) => debouncedUpdateProperty('properties.stroke', e.target.value)}
            style={{ width: '40px', height: '40px' }}
          />
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Épaisseur de bordure</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Slider
              value={element.properties.strokeWidth || 0}
              min={0}
              max={20}
              step={1}
              onChange={(_, value) => updateProperty('properties.strokeWidth', value)}
              valueLabelDisplay="auto"
            />
            <Typography variant="body2" sx={{ minWidth: 30, textAlign: 'right', fontWeight: 500 }}>
              {element.properties.strokeWidth || 0}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Opacité</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Slider
              value={element.properties.opacity || 1}
              min={0}
              max={1}
              step={0.01}
              onChange={(_, value) => updateProperty('properties.opacity', value)}
              valueLabelDisplay="auto"
            />
            <Typography variant="body2" sx={{ minWidth: 30, textAlign: 'right', fontWeight: 500 }}>
              {element.properties.opacity !== undefined ? Number(element.properties.opacity).toFixed(2) : '1.00'}
            </Typography>
          </Box>
        </Box>

        {/* Propriétés spécifiques selon le type de forme */}
        {shapeType === 'rect' && (
          <Box sx={{ mt: 2 }}>
            <Typography gutterBottom>Rayon des coins</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Slider
                value={element.properties.cornerRadius || 0}
                min={0}
                max={50}
                step={1}
                onChange={(_, value) => updateProperty('properties.cornerRadius', value)}
                valueLabelDisplay="auto"
              />
              <Typography variant="body2" sx={{ minWidth: 30, textAlign: 'right', fontWeight: 500 }}>
                {element.properties.cornerRadius || 0}
              </Typography>
            </Box>
          </Box>
        )}

        {shapeType === 'star' && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Nombre de branches</Typography>
              <Slider
                value={element.properties.numPoints || 5}
                min={3}
                max={12}
                step={1}
                onChange={(_, value) => updateProperty('properties.numPoints', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon intérieur</Typography>
              <Slider
                value={element.properties.innerRadius || 20}
                min={5}
                max={100}
                step={1}
                onChange={(_, value) => updateProperty('properties.innerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon extérieur</Typography>
              <Slider
                value={element.properties.outerRadius || 40}
                min={10}
                max={200}
                step={1}
                onChange={(_, value) => updateProperty('properties.outerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        )}

        {shapeType === 'polygon' && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Nombre de côtés</Typography>
              <Slider
                value={element.properties.sides || 6}
                min={3}
                max={12}
                step={1}
                onChange={(_, value) => updateProperty('properties.sides', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon</Typography>
              <Slider
                value={element.properties.radius || 40}
                min={10}
                max={200}
                step={1}
                onChange={(_, value) => updateProperty('properties.radius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        )}

        {(shapeType === 'arrow' || shapeType === 'line') && (
          <>
            {shapeType === 'arrow' && (
              <>
                <Box sx={{ mt: 2 }}>
                  <Typography gutterBottom>Longueur de la pointe</Typography>
                  <Slider
                    value={element.properties.pointerLength || 10}
                    min={5}
                    max={30}
                    step={1}
                    onChange={(_, value) => updateProperty('properties.pointerLength', value)}
                    valueLabelDisplay="auto"
                  />
                </Box>
                <Box sx={{ mt: 2 }}>
                  <Typography gutterBottom>Largeur de la pointe</Typography>
                  <Slider
                    value={element.properties.pointerWidth || 10}
                    min={5}
                    max={30}
                    step={1}
                    onChange={(_, value) => updateProperty('properties.pointerWidth', value)}
                    valueLabelDisplay="auto"
                  />
                </Box>
              </>
            )}
          </>
        )}

        {(shapeType === 'ring') && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon intérieur</Typography>
              <Slider
                value={element.properties.innerRadius || 20}
                min={5}
                max={100}
                step={1}
                onChange={(_, value) => updateProperty('properties.innerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon extérieur</Typography>
              <Slider
                value={element.properties.outerRadius || 40}
                min={10}
                max={200}
                step={1}
                onChange={(_, value) => updateProperty('properties.outerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        )}

        {(shapeType === 'arc') && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Angle</Typography>
              <Slider
                value={element.properties.angle || 90}
                min={0}
                max={360}
                step={1}
                onChange={(_, value) => updateProperty('properties.angle', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon intérieur</Typography>
              <Slider
                value={element.properties.innerRadius || 0}
                min={0}
                max={100}
                step={1}
                onChange={(_, value) => updateProperty('properties.innerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon extérieur</Typography>
              <Slider
                value={element.properties.outerRadius || 40}
                min={10}
                max={200}
                step={1}
                onChange={(_, value) => updateProperty('properties.outerRadius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        )}

        {(shapeType === 'wedge') && (
          <>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Angle</Typography>
              <Slider
                value={element.properties.angle || 60}
                min={0}
                max={360}
                step={1}
                onChange={(_, value) => updateProperty('properties.angle', value)}
                valueLabelDisplay="auto"
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Rayon</Typography>
              <Slider
                value={element.properties.radius || 40}
                min={10}
                max={200}
                step={1}
                onChange={(_, value) => updateProperty('properties.radius', value)}
                valueLabelDisplay="auto"
              />
            </Box>
          </>
        )}

        {/* Propriétés avancées */}
        <Divider sx={{ my: 2 }} />
        <Typography variant="subtitle1" gutterBottom>
          Propriétés avancées
        </Typography>

        <FormControl fullWidth margin="normal">
          <InputLabel>Style de ligne</InputLabel>
          <Select
            value={element.properties.lineCap || 'butt'}
            onChange={(e) => updateProperty('properties.lineCap', e.target.value)}
            label="Style de ligne"
          >
            <MenuItem value="butt">Butt</MenuItem>
            <MenuItem value="round">Round</MenuItem>
            <MenuItem value="square">Square</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth margin="normal">
          <InputLabel>Style de jointure</InputLabel>
          <Select
            value={element.properties.lineJoin || 'miter'}
            onChange={(e) => updateProperty('properties.lineJoin', e.target.value)}
            label="Style de jointure"
          >
            <MenuItem value="miter">Miter</MenuItem>
            <MenuItem value="round">Round</MenuItem>
            <MenuItem value="bevel">Bevel</MenuItem>
          </Select>
        </FormControl>

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Ombre</Typography>
          <Grid container spacing={2}>
            <Grid size={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box sx={{ width: 20, height: 20, backgroundColor: element.properties.shadowColor || '#000000', borderRadius: 1 }} />
                <TextField
                  label="Couleur"
                  type="color"
                  fullWidth
                  value={element.properties.shadowColor === 'transparent' ? '#000000' : element.properties.shadowColor || '#000000'}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateProperty('properties.shadowColor', e.target.value)}
                  margin="normal"
                />
              </Box>
            </Grid>
            <Grid size={6}>
              <TextField
                label="Flou"
                type="number"
                fullWidth
                value={element.properties.shadowBlur || 0}
                onChange={(e) => {
                  const value = Math.min(Math.max(Number(e.target.value), 0), 30);
                  updateProperty('properties.shadowBlur', value);
                }}
                margin="normal"
              />
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  // Propriétés pour les éléments de QR code
  const renderQRCodeProperties = (element: QRCodeElement) => {
    return (
      <>
        <Typography variant="h6" gutterBottom>
          QR Code
        </Typography>

        <TextField
          label="Données"
          fullWidth
          value={element.properties.data}
          onChange={(e) => updateProperty('properties.data', e.target.value)}
          margin="normal"
          helperText="URL, texte ou coordonnées"
        />

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ mr: 2 }}>Couleur:</Typography>
          <input
            type="color"
            value={element.properties.fill}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateProperty('properties.fill', e.target.value)}
            style={{ width: '40px', height: '40px' }}
          />
        </Box>

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Typography sx={{ mr: 2 }}>Couleur de fond:</Typography>
          <input
            type="color"
            value={element.properties.background === 'transparent' ? '#FFFFFF' : element.properties.background || '#FFFFFF'}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateProperty('properties.background', e.target.value)}
            style={{ width: '40px', height: '40px' }}
          />
        </Box>
      </>
    );
  };

  // Propriétés communes à tous les éléments
  const renderCommonProperties = () => {
    return (
      <>
        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" gutterBottom>
          Position et taille
        </Typography>

        <Grid container spacing={2}>
          <Grid size={6}>
            <TextField
              label="X"
              type="number"
              fullWidth
              value={selectedElement.x}
              onChange={(e) => updateProperty('x', Number(e.target.value))}
              margin="normal"
            />
          </Grid>
          <Grid size={6}>
            <TextField
              label="Y"
              type="number"
              fullWidth
              value={selectedElement.y}
              onChange={(e) => updateProperty('y', Number(e.target.value))}
              margin="normal"
            />
          </Grid>
          {/* Largeur/Hauteur masquées pour arrow et line */}
          {selectedElement.type !== 'shape' || (selectedElement.type === 'shape' && selectedElement.properties.shape !== 'arrow' && selectedElement.properties.shape !== 'line') ? (
            <>
              {selectedElement.width !== undefined && (
                <Grid size={6}>
                  <TextField
                    label="Largeur"
                    type="number"
                    fullWidth
                    value={selectedElement.width}
                    onChange={(e) => updateProperty('width', Number(e.target.value))}
                    margin="normal"
                  />
                </Grid>
              )}
              {selectedElement.height !== undefined && (
                <Grid size={6}>
                  <TextField
                    label="Hauteur"
                    type="number"
                    fullWidth
                    value={selectedElement.height}
                    onChange={(e) => updateProperty('height', Number(e.target.value))}
                    margin="normal"
                  />
                </Grid>
              )}
            </>
          ) : null}
        </Grid>

        <Box sx={{ mt: 2 }}>
          <Typography gutterBottom>Rotation</Typography>
          <Slider
            value={Number.isFinite(selectedElement.rotation) ? selectedElement.rotation : 0}
            min={0}
            max={360}
            step={1}
            onChange={(_, value) => updateProperty('rotation', value)}
            valueLabelDisplay="auto"
          />
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 1,
          mb: 2
        }}>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => onElementBringToFront(selectedElement.id)}
            fullWidth
            size="small"
            startIcon={<ArrowUpward />}
            sx={{
              py: 1,
              borderWidth: 1,
              '&:hover': {
                borderWidth: 1,
                backgroundColor: 'rgba(255, 107, 44, 0.08)'
              }
            }}
          >
            Arrière-plan
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => onElementSendToBack(selectedElement.id)}
            fullWidth
            size="small"
            startIcon={<ArrowDownward />}
            sx={{
              py: 1,
              borderWidth: 1,
              '&:hover': {
                borderWidth: 1,
                backgroundColor: 'rgba(255, 107, 44, 0.08)'
              }
            }}
          >
            Premier plan
          </Button>
        </Box>

        <Button
          variant="contained"
          color="error"
          startIcon={<Delete />}
          onClick={() => onElementDelete(selectedElement.id)}
          fullWidth
          size="small"
          sx={{
            py: 1,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
            }
          }}
        >
          Supprimer l'élément
        </Button>
      </>
    );
  };

  // Contenu du panneau de propriétés
  const panelContent = (
    <Box sx={{ p: { xs: 1.5, sm: 2 } }}>
      <Box sx={{
        backgroundColor: 'white',
        zIndex: 5,
        pb: 1,
        mb: 1,
        borderBottom: '1px solid rgba(0,0,0,0.08)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Typography
          variant="subtitle1"
          color="primary.main"
          sx={{
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            mb: 1
          }}
        >
          Propriétés de l'élément
        </Typography>
        {isMobile && onMobileDrawerClose && (
          <IconButton
            onClick={onMobileDrawerClose}
            size="small"
            sx={{ color: 'text.secondary' }}
          >
            <ExpandMore />
          </IconButton>
        )}
      </Box>

      <Box sx={{ px: { xs: 0.5, sm: 1 } }}>
        {renderProperties()}
        {renderCommonProperties()}
      </Box>
    </Box>
  );

  // Version mobile : Drawer en bas
  if (isMobile) {
    return (
      <Drawer
        anchor="bottom"
        open={isMobileDrawerOpen}
        onClose={onMobileDrawerClose}
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            backgroundColor: '#FFFFFF',
            maxHeight: '40vh',
            overflow: 'auto'
          }
        }}
      >
        {panelContent}
      </Drawer>
    );
  }

  // Version desktop : Paper normal
  return (
    <Paper
      sx={{
        height: '100%',
        overflow: 'auto',
        borderRadius: 0,
        backgroundColor: '#FFFFFF'
      }}
    >
      {panelContent}
    </Paper>
  );
};

export default PropertiesPanel;
