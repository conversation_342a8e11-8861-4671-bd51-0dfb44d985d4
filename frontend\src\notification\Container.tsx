import { useEffect, useState, useCallback } from 'react';
import { toast, Toaster } from 'react-hot-toast';
import './toast.css';

// Gestion de la file d'attente des notifications
const MAX_TOASTS = 5;
const toastQueue: string[] = [];

interface ToastMessageProps {
  message: string;
  type?: string;
  visible?: boolean;
  t: any;
  duration?: number;
}

const icons = {
  success: (
    <svg className="notification-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M22 4 12 14.01l-3-3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  ),
  error: (
    <svg className="notification-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
      <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    </svg>
  ),
  warning: (
    <svg className="notification-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    </svg>
  ),
  info: (
    <svg className="notification-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
      <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      <line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
    </svg>
  )
};

const ToastMessage = ({ message, type = 'info', visible = true, t, duration = 5000 }: ToastMessageProps) => {
  const [progress, setProgress] = useState(100);
  const [isHovered, setIsHovered] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = useCallback(() => {
    setIsClosing(true);
    toast.dismiss(t.id);
  }, [t.id]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  useEffect(() => {
    if (isClosing) {
      return;
    }

    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, duration - elapsedTime);
      const currentProgress = (remainingTime / duration) * 100;
      
      setProgress(currentProgress);

      if (currentProgress <= 0) {
        clearInterval(interval);
        setIsClosing(true);
        toast.dismiss(t.id);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [duration, t.id, isClosing]);

  if (isClosing) {
    return null;
  }

  return (
    <div 
      className={`notification ${type} ${visible ? 'show' : 'hide'}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="notification-icon-wrapper">
        {icons[type as keyof typeof icons]}
      </div>
      <div className="notification-content">
        <div className="notification-message">{message}</div>
      </div>
      <button 
        className="notification-close"
        onClick={handleClose}
        aria-label="Fermer"
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
      <div className="notification-progress">
        <div 
          className="notification-progress-bar"
          style={{ 
            width: `${progress}%`,
            animationPlayState: isHovered ? 'paused' : 'running'
          }}
        />
      </div>
    </div>
  );
};

export const NotificationProvider = () => {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 5000, // Durée par défaut de 5 secondes
        style: {
          background: 'none',
          boxShadow: 'none',
          padding: 0,
          margin: 0,
        },
        // Fermer automatiquement toutes les notifications après un certain temps
        className: 'toast-container',
      }}
      containerStyle={{
        top: 16,
        right: 16,
      }}
    >
      {(t) => (
        <ToastMessage
          t={t}
          message={t.message as string}
          type={t.type as string}
          visible={t.visible}
          duration={t.duration}
        />
      )}
    </Toaster>
  );
};

export const showNotification = (message: string, type = 'info', duration = 5000) => {
  // Si on a déjà atteint le maximum, on supprime la plus ancienne notification
  if (toastQueue.length >= MAX_TOASTS) {
    const oldestToastId = toastQueue.shift();
    if (oldestToastId) {
      toast.dismiss(oldestToastId);
    }
  }

  // Créer la nouvelle notification
  const toastId = toast.custom(
    (t) => (
      <ToastMessage 
        message={message} 
        type={type} 
        t={t} 
        duration={duration}
      />
    ),
    {
      duration: duration,
    }
  );

  // Ajouter l'ID à la file d'attente
  toastQueue.push(toastId);
  
  return toastId;
};

export const NotificationService = {
  getErrorMessage: (error: any) => {
    // Si c'est une erreur Axios
    if (error?.response?.status) {
      switch (error.response.status) {
        case 429:
          return "Trop de tentatives de connexion échouées. Veuillez revenir plus tard.";
        case 401:
          return "Session expirée. Veuillez vous reconnecter.";
        case 403:
          // Vérifier si c'est un blocage temporaire
          if (error?.response?.data?.type === 'BRUTE_FORCE_BLOCKED' || 
              error?.config?.url?.includes('/csrf') ||
              error?.response?.data?.action === 'brute_force_blocked') {
            return "Accès temporairement bloqué pour des raisons de sécurité. Veuillez revenir plus tard.";
          }
          return "Accès refusé. Vous n'avez pas les permissions nécessaires.";
        case 404:
          return "La ressource demandée n'existe pas.";
        case 500:
          return "Une erreur est survenue sur le serveur. Veuillez réessayer plus tard.";
        default:
          return "Une erreur inattendue est survenue. Veuillez réessayer.";
      }
    }
    // Si c'est une erreur réseau
    if (error?.message?.includes('Network Error')) {
      return "Impossible de se connecter au serveur. Vérifiez votre connexion internet.";
    }
    // Message par défaut
    return error?.message || "Une erreur est survenue";
  },

  notify: (message: string, type: string = 'info', duration?: number) => {
    // Si c'est un objet d'erreur Axios
    if (typeof message === 'object' && message !== null) {
      const errorMessage = NotificationService.getErrorMessage(message);
      showNotification(errorMessage, 'error', duration);
      return;
    }
    
    // Si le message est une chaîne contenant une erreur Axios
    if (message?.includes('status code') || message?.includes('Error')) {
      const statusCode = message.match(/status code (\d+)/)?.[1];
      const errorMessage = NotificationService.getErrorMessage({
        response: {
          status: parseInt(statusCode || '0'),
          data: { type: 'BRUTE_FORCE_BLOCKED' },
          config: {
            url: '/api/auth/csrf'
          }
        }
      });
      showNotification(errorMessage, 'error', duration);
      return;
    }

    showNotification(message, type, duration);
  },
  handleBackendNotification: (response: any) => {
    // Vérifier si la réponse contient une notification
    if (response?.notification?.show) {
      showNotification(response.message, response.notification.type);
    }
  }
};


export const testNotifications = () => {
  showNotification("✨ Opération réussie avec succès !", "success");
  setTimeout(() => showNotification("⚠️ Attention : action requise", "warning"), 1000);
  setTimeout(() => showNotification("❌ Une erreur est survenue lors de l'opération", "error"), 2000);
  setTimeout(() => showNotification("ℹ️ Information importante à lire", "info"), 3000);
};
