# Dependencies
node_modules/
**/node_modules/
frontend/node_modules/
backend/node_modules/

# Testing
/coverage

# Production
/build
/dist
**/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
logs/
**/logs/

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.local.ts
*.local.js
*.local.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Uploads and user-generated content
/uploads
**/uploads/
*.tmp
*.temp
.cache/

# Backup files
*.bak
*.backup
*~

# SSL/TLS Certificates
*.pem
*.key
*.crt
*.cer
*.der
*.priv

# Debug files
debug.log
debug.json
*.debug.*

# Compiled source
*.com
*.class
*.dll
*.exe
*.o
*.so

# Sensitive configuration files
config.json
*.config.json
*.credentials
*.secrets

# Additional build files
**/.next/
**/.nuxt/
**/.output/

# TypeScript
*.tsbuildinfo
.tscache/
.typescript/

# Coverage reports
.nyc_output/
coverage/
**/coverage/

# Redis
dump.rdb
*.rdb

# Session files
sessions/
**/sessions/

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Build output
dist/

# Project specific
suivi_projet.md
update_github.bat

# Documentation personnelle
si bug méthode à faire.txt

# Fichiers personnels à ignorer
Texte pour correction etc.txt

# Additional exclusions
.history/
.history

# Ignorer les fichiers et dossiers spécifiques
Prompt image
Prompt debut chat
AVANCEMENT.md
.windsurfrules
SERVEUR PRODUCTION update_github.bat
.cursorignore
.groupedtimelineinclude
flush-redis.js
Prompt Mentions légales.ini
pousser modification de dev à prod sur github
Prompt image avatar
Prompt image pour les services
CGV_JobPartiel.md
CGV_JobPartiel - Copie.md
Renforcement_CGV_JobPartiel.md
.cursor/rules/regle-job-partiel.mdc
CGV_JobPartiel.md
CGV_JobPartiel.md
CGV_JobPartiel.md
Multiplication des ports pour le docker, tuto
CGV_JobPartiel.md
Test Securité RLS.ts
