import { useState, useEffect, useRef } from 'react';
import { CompanySettings, invoiceService } from '../../services/invoiceService';
import logger from '@/utils/logger';

// Cache global pour éviter les appels multiples
let globalCompanySettings: CompanySettings | null = null;
let globalLoading = false;
let globalError: string | null = null;
let fetchPromise: Promise<void> | null = null;
const subscribers = new Set<() => void>();

// Hook personnalisé pour gérer les paramètres d'entreprise avec cache
export const useCompanySettings = () => {
  const [settings, setSettings] = useState<CompanySettings | null>(globalCompanySettings);
  const [loading, setLoading] = useState(globalLoading);
  const [error, setError] = useState<string | null>(globalError);
  const subscriberRef = useRef<() => void>(() => {});

  useEffect(() => {
    // Créer une fonction de mise à jour pour ce composant
    const updateState = () => {
      setSettings(globalCompanySettings);
      setLoading(globalLoading);
      setError(globalError);
    };

    // S'abonner aux changements
    subscriberRef.current = updateState;
    subscribers.add(updateState);

    // Si les données ne sont pas encore chargées et qu'aucun chargement n'est en cours
    if (!globalCompanySettings && !globalLoading && !fetchPromise) {
      fetchCompanySettings();
    }

    // Cleanup
    return () => {
      if (subscriberRef.current) {
        subscribers.delete(subscriberRef.current);
      }
    };
  }, []);

  const notifySubscribers = () => {
    subscribers.forEach(callback => callback());
  };

  const fetchCompanySettings = async () => {
    // Si un fetch est déjà en cours, attendre qu'il se termine
    if (fetchPromise) {
      return fetchPromise;
    }

    // Si les données sont déjà chargées, ne pas refaire l'appel
    if (globalCompanySettings) {
      return Promise.resolve();
    }

    globalLoading = true;
    globalError = null;
    notifySubscribers();

    fetchPromise = (async () => {
      try {
        const data = await invoiceService.getCompanySettings();
        globalCompanySettings = data;
        globalError = null;
        logger.info('Paramètres d\'entreprise récupérés avec succès');
      } catch (err: any) {
        globalError = err.message || 'Erreur lors de la récupération des paramètres d\'entreprise';
        logger.error('Erreur lors de la récupération des paramètres d\'entreprise:', err);
      } finally {
        globalLoading = false;
        fetchPromise = null;
        notifySubscribers();
      }
    })();

    return fetchPromise;
  };

  const updateCompanySettings = async (newSettings: CompanySettings) => {
    try {
      const updatedSettings = await invoiceService.updateCompanySettings(newSettings);
      globalCompanySettings = updatedSettings;
      globalError = null;
      notifySubscribers();
      return updatedSettings;
    } catch (err: any) {
      globalError = err.message || 'Erreur lors de la mise à jour des paramètres d\'entreprise';
      notifySubscribers();
      throw err;
    }
  };

  const refreshSettings = () => {
    globalCompanySettings = null;
    fetchCompanySettings();
  };

  return {
    settings,
    loading,
    error,
    updateSettings: updateCompanySettings,
    refreshSettings
  };
};

export default useCompanySettings;
