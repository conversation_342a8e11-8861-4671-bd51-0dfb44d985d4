import React, { useCallback } from 'react';
import { Typography, IconButton, Box, Tooltip, Fade, Alert } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { Refresh, InfoOutlined } from '@mui/icons-material';
import HistoryTable from '../../components/common/HistoryTable';
import { useHistory } from '../../hooks/useHistory';

// Styled components
const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748', 
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.25rem',
  },
}));

const RefreshButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: 'rgba(255, 107, 44, 0.08)',
    color: '#FF6B2C',
    borderRadius: '8px',
  padding: '8px',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '6px',
  },
}));

const InfoBox = styled(Box)(({ theme }) => ({
  margin: theme.spacing(2, 0),
  padding: theme.spacing(1.5, 2),
  borderRadius: '8px',
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  display: 'flex',
  alignItems: 'center',
  '& .MuiSvgIcon-root': {
      color: '#FF6B2C',
    marginRight: theme.spacing(1),
  },
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
    '@media (max-width: 768px)': {
      fontSize: '0.9rem',
    },
  }
}));

const HistoriqueEvaluationsPage: React.FC = () => {
  const { fetchLoginHistory, fetchActionHistory, fetchActionTypes, isLoading, error, clearCache } = useHistory();

  const handleRefresh = useCallback(() => {
    clearCache();
    // La mise à jour des données sera déclenchée lors du prochain rendu du HistoryTable
  }, [clearCache]);

  return (
    <motion.div className="pb-6">
      <div className="space-y-6 px-2 md:px-0">
        <div className="flex items-center justify-between mb-4">
          <PageTitle variant="h1">
            Historique et journaux d'activité
          </PageTitle>
          
          <Tooltip title="Rafraîchir les données" arrow>
            <RefreshButton 
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <Refresh fontSize="small" />
            </RefreshButton>
          </Tooltip>
        </div>

        {error && (
          <Fade in={!!error}>
            <Alert 
              severity="error" 
              sx={{ mb: 3 }}
              onClose={() => {/* Fonction pour fermer l'alerte si nécessaire */}}
            >
              {error}
            </Alert>
          </Fade>
        )}
        
        <InfoBox>
          <InfoOutlined fontSize="small" />
          <Typography variant="body2" className="text-gray-700 ml-1">
            Cette page affiche l'historique de vos connexions et l'activité de votre compte JobPartiel. Les données sont conservées selon votre niveau d'abonnement.
          </Typography>
        </InfoBox>
        
        <motion.div 
          className="mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <HistoryTable
            onFetchLoginHistory={fetchLoginHistory}
            onFetchActionHistory={fetchActionHistory}
            onFetchActionTypes={fetchActionTypes}
            clearCache={clearCache}
            showSubscriptionInfo={true}
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

export default HistoriqueEvaluationsPage; 