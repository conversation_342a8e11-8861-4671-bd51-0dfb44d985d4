/* Permet de récupérer le solde Jobi et de le mettre à jour sur toutes les pages */

import React, { createContext, useContext, ReactNode } from 'react';
import { useJobiBalance } from '../hooks/useJobiBalance';

interface JobiTransaction {
  montant: number;
  operation: 'plus' | 'moins';
  titre: string;
  description: string;
}

interface JobiResponse {
  montant: number;
  profil_complet: boolean;
}

interface JobiContextType {
  balance: number;
  loading: boolean;
  profilComplet: boolean;
  refreshBalance: () => Promise<JobiResponse | undefined>;
  updateBalance: (transaction: JobiTransaction) => Promise<boolean>;
}

const JobiContext = createContext<JobiContextType | undefined>(undefined);

export const JobiProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const jobiHook = useJobiBalance();
  
  return (
    <JobiContext.Provider value={jobiHook}>
      {children}
    </JobiContext.Provider>
  );
};

export const useJobi = () => {
  const context = useContext(JobiContext);
  if (context === undefined) {
    throw new Error('useJobi doit être utilisé à l\'intérieur d\'un JobiProvider');
  }
  return context;
};
