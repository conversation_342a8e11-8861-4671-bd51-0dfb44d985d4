import React from 'react';
import ModalPortal from '../ModalPortal';
import { AnimatePresence } from 'framer-motion';
import { X, Upload } from 'lucide-react';

interface GalleryModalProps {
  open: boolean;
  onClose: () => void;
  onSave: () => void;
  galleryModalData: { name: string; description: string; cover_image?: File };
  setGalleryModalData: (data: any) => void;
  selectedGallery: any;
  handleEditGallery: (galleryId: string) => void;
  handleCreateGallery: () => void;
}

const GalleryModal: React.FC<GalleryModalProps> = ({
  open,
  onClose,
  onSave,
  galleryModalData,
  setGalleryModalData,
  selectedGallery,
  handleEditGallery,
  handleCreateGallery,
}) => {
  return (
    <AnimatePresence>
      {open && (
        <ModalPortal>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold">
                  {selectedGallery ? 'Modifier la galerie' : 'Nouvelle galerie'}
                </h3>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nom de la galerie
                  </label>
                  <input
                    type="text"
                    value={galleryModalData.name}
                    onChange={(e) => setGalleryModalData((prev: any) => ({ ...prev, name: e.target.value }))}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF965E] focus:border-transparent"
                    placeholder="Ex: Rénovation cuisine"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={galleryModalData.description}
                    onChange={(e) => setGalleryModalData((prev: any) => ({ ...prev, description: e.target.value }))}
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF965E] focus:border-transparent resize-none h-32"
                    placeholder="Décrivez cette galerie..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image de couverture
                  </label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-[#FF6B2C] transition-colors">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="cover-image"
                          className="cursor-pointer rounded-md font-medium text-[#FF6B2C] hover:text-[#FF7A35] focus-within:outline-none"
                        >
                          <span>Télécharger une image</span>
                          <input
                            id="cover-image"
                            type="file"
                            accept="image/*"
                            className="sr-only"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                setGalleryModalData((prev: any) => ({ ...prev, cover_image: file }));
                              }
                            }}
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, WEBP jusqu'à 5MB</p>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={() => selectedGallery ? handleEditGallery(selectedGallery.id) : handleCreateGallery()}
                    className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                  >
                    {selectedGallery ? 'Enregistrer' : 'Créer'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </AnimatePresence>
  );
};

export default GalleryModal; 