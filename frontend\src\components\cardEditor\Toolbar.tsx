import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Button,
  Divider,
  Typography,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  Badge,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  TextFields,
  Image,
  QrCode,
  Delete,
  Undo,
  Redo,
  Rectangle,
  Circle,
  LinearScale,
  AutoAwesome,
  Brush,
  Add,
  MoreVert,
  Timeline,
  List,
  AddBox
} from '@mui/icons-material';
import { CardElement, CardTemplateData, CardTemplateType, ShapeElement } from '../../types/cardEditor';
import PathGeneratorDialog from './PathGeneratorDialog';
import AiTemplateGenerationModal from './AiTemplateGenerationModal';
import { API_URL } from '@/config/api';
import '../../styles/aiAnimation.css';
import cardEditorService from '../../services/cardEditorService';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

interface ToolbarProps {
  onAddElement: (element: CardElement, userAvatarUrl?: string | null) => void;
  onGenerateRandom: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onClear: () => void;
  onDrawingModeToggle: () => void;
  isDrawingMode: boolean;
  canUndo: boolean;
  canRedo: boolean;
  autoSaveEnabled: boolean;
  setAutoSaveEnabled: (enabled: boolean) => void;
  onShowElementsList?: () => void;
  isSaving?: boolean;
  userAvatarUrl?: string | null;
  templateType: CardTemplateType;
  onImportTemplate?: (template: CardTemplateData) => void;
  onSave?: () => Promise<void>;
}

const Toolbar: React.FC<ToolbarProps> = ({
  onAddElement,
  onUndo,
  onRedo,
  onClear,
  onDrawingModeToggle,
  isDrawingMode,
  canUndo,
  canRedo,
  autoSaveEnabled,
  setAutoSaveEnabled,
  onShowElementsList,
  isSaving = false,
  userAvatarUrl = null,
  templateType,
  onImportTemplate,
  onSave
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  // États pour les menus
  const [shapeMenuAnchor, setShapeMenuAnchor] = useState<null | HTMLElement>(null);
  const [addMenuAnchor, setAddMenuAnchor] = useState<null | HTMLElement>(null);
  const [moreMenuAnchor, setMoreMenuAnchor] = useState<null | HTMLElement>(null);

  // État pour la modale de confirmation
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  // État pour le dialogue de génération de Path
  const [pathGeneratorOpen, setPathGeneratorOpen] = useState(false);

  // État pour la modale de génération IA
  const [aiGenerationModalOpen, setAiGenerationModalOpen] = useState(false);

  // Animation élégante sauvegarde auto
  const [showSaveAnim, setShowSaveAnim] = useState(false);
  const { id: templateId } = useParams<{ id: string }>();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isSaving) {
      setShowSaveAnim(false);
      setTimeout(() => {
        setShowSaveAnim(true);
        const timeout = setTimeout(() => setShowSaveAnim(false), 1200);
        return () => clearTimeout(timeout);
      }, 10);
    }
  }, [isSaving]);

  // Ajouter un élément texte
  const addTextElement = async () => {
    let defaultText = 'JobPartiel.fr';

    if (isAuthenticated && user?.id) {
      try {
        const response = await axios.get(`${API_URL}/api/users/get-slug/${user.id}`);
        if (response.data.success && response.data.slug) {
          defaultText = `https://jobpartiel.fr/profil/${response.data.slug}`;
        }
      } catch (error) {
        console.error('Erreur lors de la récupération du slug utilisateur:', error);
        // Revenir au texte par défaut en cas d'erreur
        defaultText = 'JobPartiel.fr';
      }
    }

    const element: CardElement = {
      id: crypto.randomUUID(),
      type: 'text',
      x: 50,
      y: 50,
      width: 200,
      properties: {
        text: defaultText,
        fontSize: 16,
        fontFamily: 'Arial',
        fill: '#000000',
        align: 'left'
      }
    };
    onAddElement(element);
  };

  const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

  // Ajouter un élément image
  const addImageElement = (userAvatarUrl?: string | null) => {
    const element: CardElement = {
      id: crypto.randomUUID(),
      type: 'image',
      x: 50,
      y: 50,
      width: 100,
      height: 100,
      properties: {
        src: userAvatarUrl || DEFAULT_AVATAR,
        cornerRadius: 0
      }
    };
    onAddElement(element, userAvatarUrl);
  };

  // Ajouter un élément QR code
  const addQRCodeElement = async () => {
    let defaultQRData = 'https://jobpartiel.fr';

    if (isAuthenticated && user?.id) {
      try {
        const response = await axios.get(`${API_URL}/api/users/get-slug/${user.id}`);
        if (response.data.success && response.data.slug) {
          defaultQRData = `https://jobpartiel.fr/profil/${response.data.slug}`;
        }
      } catch (error) {
        console.error('Erreur lors de la récupération du slug utilisateur pour QR code:', error);
        // Revenir à l'URL par défaut en cas d'erreur
        defaultQRData = 'https://jobpartiel.fr';
      }
    }

    const element: CardElement = {
      id: crypto.randomUUID(),
      type: 'qrcode',
      x: 50,
      y: 50,
      width: 100,
      height: 100,
      properties: {
        data: defaultQRData,
        fill: '#000000',
        background: '#FFFFFF'
      }
    };
    onAddElement(element);
  };

  // Ajouter un élément forme
  const addShapeElement = (shapeType: 'rect' | 'circle' | 'line' | 'ellipse' | 'star' | 'arrow' | 'polygon' | 'ring' | 'arc' | 'wedge' | 'path') => {
    let properties: any = {
      shape: shapeType,
      fill: '#FF6B2C',
      stroke: '#000000',
      strokeWidth: 0,
      opacity: 1
    };

    // Propriétés spécifiques selon le type de forme
    switch (shapeType) {
      case 'rect':
        properties = {
          ...properties,
          cornerRadius: 0
        };
        break;
      case 'ellipse':
        properties = {
          ...properties
        };
        break;
      case 'line':
      case 'arrow':
        properties = {
          ...properties,
          fill: '#000000',
          strokeWidth: 2,
          points: [0, 0, 100, 0]
        };
        break;
      case 'star':
        properties = {
          ...properties,
          numPoints: 5,
          innerRadius: 20,
          outerRadius: 40
        };
        break;
      case 'polygon':
        properties = {
          ...properties,
          sides: 6,
          radius: 40
        };
        break;
      case 'ring':
        properties = {
          ...properties,
          innerRadius: 20,
          outerRadius: 40
        };
        break;
      case 'arc':
        properties = {
          ...properties,
          angle: 90,
          innerRadius: 0,
          outerRadius: 40
        };
        break;
      case 'wedge':
        properties = {
          ...properties,
          angle: 60,
          radius: 40
        };
        break;
      case 'path':
        properties = {
          ...properties,
          data: 'M10 80 C 40 10, 65 10, 95 80',
          stroke: '#000000',
          strokeWidth: 3,
          fill: 'transparent'
        };
        break;
    }

    const element: CardElement = {
      id: crypto.randomUUID(),
      type: 'shape',
      x: 50,
      y: 50,
      width: shapeType === 'ellipse' ? 120 : ['line', 'arrow'].includes(shapeType) ? 100 : 80,
      height: shapeType === 'ellipse' ? 80 : 80,
      properties
    };

    onAddElement(element);
    setShapeMenuAnchor(null);
  };

  // Gestion des menus
  const handleShapeMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setShapeMenuAnchor(event.currentTarget);
  };

  const handleShapeMenuClose = () => {
    setShapeMenuAnchor(null);
  };

  const handleAddMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAddMenuAnchor(event.currentTarget);
  };

  const handleAddMenuClose = () => {
    setAddMenuAnchor(null);
  };

  const handleMoreMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMoreMenuAnchor(event.currentTarget);
  };

  const handleMoreMenuClose = () => {
    setMoreMenuAnchor(null);
  };

  // Ajouter un élément et fermer le menu
  const handleAddElementAndCloseMenu = (callback: () => void) => {
    callback();
    handleAddMenuClose();
  };

  // Remplace l'appel direct à onClear par une fonction avec confirmation
  const handleClearWithConfirm = () => {
    setOpenConfirmDialog(true);
  };

  const handleConfirmClear = async () => {
    setOpenConfirmDialog(false);
    // Suppression des images du bucket côté backend
    if (templateId) {
      try {
        await cardEditorService.deleteAllImages(templateId);
      } catch (e) {
        // Optionnel : notifier l'utilisateur en cas d'erreur
      }
    }
    onClear();
  };

  const handleCancelClear = () => {
    setOpenConfirmDialog(false);
  };

  // Ouvrir la modale de génération IA
  const handleOpenAiGenerationModal = async () => {
    setAiGenerationModalOpen(true);
  };

  // Gérer la sélection d'un template généré par l'IA
  const handleTemplateSelect = (templateData: CardTemplateData) => {
    if (onImportTemplate) {
      onImportTemplate(templateData);
    } else {
      // fallback : ancien comportement
      onClear();
      templateData.elements.forEach(element => {
        onAddElement(element);
      });
    }
  };

  // Rendu du menu des formes
  const renderShapesMenu = () => (
    <Menu
      anchorEl={shapeMenuAnchor}
      open={Boolean(shapeMenuAnchor)}
      onClose={handleShapeMenuClose}
      sx={{ maxHeight: '70vh', overflowY: 'auto' }}
    >
      <MenuItem onClick={() => { addShapeElement('rect'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <Rectangle fontSize="small" />
        </ListItemIcon>
        <ListItemText>Rectangle</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('circle'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <Circle fontSize="small" />
        </ListItemIcon>
        <ListItemText>Cercle</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('ellipse'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <ellipse cx="12" cy="12" rx="8" ry="5" />
          </svg>
        </ListItemIcon>
        <ListItemText>Ellipse</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('line'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <LinearScale fontSize="small" />
        </ListItemIcon>
        <ListItemText>Ligne</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('arrow'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <line x1="5" y1="12" x2="19" y2="12" strokeWidth="2" />
            <polyline points="15,8 19,12 15,16" strokeWidth="2" />
          </svg>
        </ListItemIcon>
        <ListItemText>Flèche</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('star'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" />
          </svg>
        </ListItemIcon>
        <ListItemText>Étoile</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('polygon'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <polygon points="12,2 19,7 19,17 12,22 5,17 5,7" />
          </svg>
        </ListItemIcon>
        <ListItemText>Polygone</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('ring'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="8" />
            <circle cx="12" cy="12" r="4" />
          </svg>
        </ListItemIcon>
        <ListItemText>Anneau</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('arc'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <path d="M12,12 L12,4 A8,8 0 0,1 20,12 Z" />
          </svg>
        </ListItemIcon>
        <ListItemText>Arc</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('wedge'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <path d="M12,12 L20,12 A8,8 0 0,1 12,20 Z" />
          </svg>
        </ListItemIcon>
        <ListItemText>Secteur</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { addShapeElement('path'); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor">
            <path d="M10 80 C 40 10, 65 10, 95 80" transform="scale(0.2)" strokeWidth="10" />
          </svg>
        </ListItemIcon>
        <ListItemText>Courbe SVG simple</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { setPathGeneratorOpen(true); handleShapeMenuClose(); }}>
        <ListItemIcon>
          <Timeline fontSize="small" />
        </ListItemIcon>
        <ListItemText>Générateur de courbes</ListItemText>
      </MenuItem>
    </Menu>
  );

  // Rendu du menu d'ajout d'éléments (pour mobile)
  const renderAddMenu = () => (
    <Menu
      anchorEl={addMenuAnchor}
      open={Boolean(addMenuAnchor)}
      onClose={handleAddMenuClose}
    >
      <MenuItem onClick={() => handleAddElementAndCloseMenu(addTextElement)}>
        <ListItemIcon>
          <TextFields fontSize="small" />
        </ListItemIcon>
        <ListItemText>Texte</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => handleAddElementAndCloseMenu(() => addImageElement(userAvatarUrl))}>
        <ListItemIcon>
          <Image fontSize="small" />
        </ListItemIcon>
        <ListItemText>Image</ListItemText>
      </MenuItem>
      <MenuItem component="button" onClick={handleShapeMenuOpen}>
        <ListItemIcon>
          <AddBox />
        </ListItemIcon>
        <ListItemText>Forme</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => handleAddElementAndCloseMenu(addQRCodeElement)}>
        <ListItemIcon>
          <QrCode fontSize="small" />
        </ListItemIcon>
        <ListItemText>QR Code</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={() => { onDrawingModeToggle(); handleAddMenuClose(); }}>
        <ListItemIcon>
          <Brush fontSize="small" color={isDrawingMode ? "secondary" : "inherit"} />
        </ListItemIcon>
        <ListItemText>{isDrawingMode ? "Quitter le mode dessin" : "Mode dessin libre"}</ListItemText>
      </MenuItem>
    </Menu>
  );

  // Rendu du menu "Plus" (pour mobile)
  const renderMoreMenu = () => (
    <Menu
      anchorEl={moreMenuAnchor}
      open={Boolean(moreMenuAnchor)}
      onClose={handleMoreMenuClose}
    >
      <MenuItem onClick={() => { handleOpenAiGenerationModal(); handleMoreMenuClose(); }}>
        <ListItemIcon>
          <AutoAwesome fontSize="small" />
        </ListItemIcon>
        <ListItemText>Générer avec l'IA</ListItemText>
      </MenuItem>
      <MenuItem onClick={() => { handleClearWithConfirm(); handleMoreMenuClose(); }}>
        <ListItemIcon>
          <Delete fontSize="small" color="error" />
        </ListItemIcon>
        <ListItemText>Effacer tout</ListItemText>
      </MenuItem>
    </Menu>
  );

  return (
    <Paper
      elevation={1}
      sx={{
        p: { xs: 0.5, sm: 1 },
        mb: 0,
        display: 'flex',
        flexWrap: 'wrap',
        alignItems: 'center',
        gap: 0.5,
        borderRadius: 2,
        borderTop: '1px solid rgba(0,0,0,0.08)',
        borderBottom: '1px solid rgba(0,0,0,0.08)',
        mx: { xs: 1, sm: 2 }
      }}
    >
      {/* Version mobile: boutons simplifiés */}
      {isMobile && (
        <>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Ajouter un élément">
                <IconButton onClick={handleAddMenuOpen} color="primary" size="small">
                  <Add />
                </IconButton>
              </Tooltip>

              <Tooltip title="Annuler">
                <span>
                  <IconButton
                    onClick={onUndo}
                    disabled={!canUndo}
                    color={canUndo ? "primary" : "default"}
                    size="small"
                  >
                    <Badge
                      variant="dot"
                      color="primary"
                      invisible={!canUndo}
                      sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                    >
                      <Undo fontSize="small" />
                    </Badge>
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title="Rétablir">
                <span>
                  <IconButton
                    onClick={onRedo}
                    disabled={!canRedo}
                    color={canRedo ? "primary" : "default"}
                    size="small"
                  >
                    <Badge
                      variant="dot"
                      color="primary"
                      invisible={!canRedo}
                      sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                    >
                      <Redo fontSize="small" />
                    </Badge>
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title={isDrawingMode ? "Quitter le mode dessin" : "Mode dessin libre"}>
                <IconButton
                  onClick={onDrawingModeToggle}
                  color={isDrawingMode ? "secondary" : "primary"}
                  size="small"
                >
                  <Badge
                    variant="dot"
                    color="secondary"
                    invisible={!isDrawingMode}
                    sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                  >
                    <Brush fontSize="small" />
                  </Badge>
                </IconButton>
              </Tooltip>

              <Tooltip title="Voir les éléments">
                <IconButton onClick={() => onShowElementsList && onShowElementsList()} color="primary" size="small">
                  <List />
                </IconButton>
              </Tooltip>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Plus d'options">
                <IconButton onClick={handleMoreMenuOpen} size="small">
                  <MoreVert fontSize="small" />
                </IconButton>
              </Tooltip>

              <Tooltip title="Générer avec l'IA">
                <span>
                  <IconButton
                    onClick={async () => await handleOpenAiGenerationModal()}
                    color="primary"
                    size="small"
                  >
                    <AutoAwesome fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Box>

          {renderAddMenu()}
          {renderMoreMenu()}
          {renderShapesMenu()}
        </>
      )}

      {/* Version tablette et desktop */}
      {!isMobile && (
        <>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mx: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{
                mr: 0.5,
                fontSize: isTablet ? '0.75rem' : '0.875rem',
                display: { xs: 'none', sm: 'block' }
              }}
            >
              Ajouter:
            </Typography>

            <Tooltip title="Ajouter du texte">
              <IconButton onClick={addTextElement} color="primary" size={isTablet ? "small" : "medium"}>
                <TextFields />
              </IconButton>
            </Tooltip>

            <Tooltip title="Ajouter une image">
              <IconButton onClick={() => addImageElement(userAvatarUrl)} color="primary" size={isTablet ? "small" : "medium"}>
                <Image />
              </IconButton>
            </Tooltip>

            <Tooltip title="Ajouter une forme">
              <IconButton onClick={handleShapeMenuOpen} color="primary" size={isTablet ? "small" : "medium"}>
                <AddBox />
              </IconButton>
            </Tooltip>

            <Tooltip title="Ajouter un QR code">
              <IconButton onClick={addQRCodeElement} color="primary" size={isTablet ? "small" : "medium"}>
                <QrCode />
              </IconButton>
            </Tooltip>

            <Tooltip title={isDrawingMode ? "Quitter le mode dessin" : "Mode dessin libre"}>
              <IconButton
                onClick={onDrawingModeToggle}
                color={isDrawingMode ? "secondary" : "primary"}
                size={isTablet ? "small" : "medium"}
              >
                <Badge
                  variant="dot"
                  color="secondary"
                  invisible={!isDrawingMode}
                  sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                >
                  <Brush />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Voir les éléments">
              <IconButton onClick={() => onShowElementsList && onShowElementsList()} color="primary" size={isTablet ? "small" : "medium"}>
                <List />
              </IconButton>
            </Tooltip>
          </Box>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Tooltip title="Annuler (Ctrl+Z)">
              <span>
                <IconButton
                  onClick={onUndo}
                  disabled={!canUndo}
                  color={canUndo ? "primary" : "default"}
                  size={isTablet ? "small" : "medium"}
                >
                  <Badge
                    variant="dot"
                    color="primary"
                    invisible={!canUndo}
                    sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                  >
                    <Undo />
                  </Badge>
                </IconButton>
              </span>
            </Tooltip>

            <Tooltip title="Rétablir (Ctrl+Y ou Ctrl+Shift+Z)">
              <span>
                <IconButton
                  onClick={onRedo}
                  disabled={!canRedo}
                  color={canRedo ? "primary" : "default"}
                  size={isTablet ? "small" : "medium"}
                >
                  <Badge
                    variant="dot"
                    color="primary"
                    invisible={!canRedo}
                    sx={{ '& .MuiBadge-dot': { top: 3, right: 3 } }}
                  >
                    <Redo />
                  </Badge>
                </IconButton>
              </span>
            </Tooltip>

            <Tooltip title="Effacer tout">
              <IconButton
                onClick={handleClearWithConfirm}
                color="error"
                size={isTablet ? "small" : "medium"}
              >
                <Delete />
              </IconButton>
            </Tooltip>
          </Box>

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            ml: 'auto',
            gap: { sm: 0.5, md: 1 }
          }}>
            <Tooltip title="Générer avec l'IA">
              <span>
                <Button
                  variant="outlined"
                  startIcon={<AutoAwesome />}
                  onClick={async () => await handleOpenAiGenerationModal()}
                  size={isTablet ? "small" : "medium"}
                  sx={{ minWidth: 0, px: isTablet ? 1 : 2 }}
                >
                  {isTablet ? <AutoAwesome fontSize="small" /> : "Générer"}
                </Button>
              </span>
            </Tooltip>

            {renderShapesMenu()}

            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2, position: 'relative' }}>
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ mr: 0.5, position: 'relative', display: 'inline-block' }}
                className={showSaveAnim ? 'auto-save-animate' : ''}
              >
                Sauvegarde auto
                {showSaveAnim && (
                  <span className="auto-save-check" aria-label="sauvegardé">
                    ✓
                  </span>
                )}
              </Typography>
              <Switch
                size="small"
                checked={autoSaveEnabled}
                onChange={e => setAutoSaveEnabled(e.target.checked)}
                color="primary"
                inputProps={{ 'aria-label': 'Activer la sauvegarde automatique' }}
              />
            </Box>
          </Box>
        </>
      )}

      {/* Modale de confirmation de suppression (toujours rendue, même sur mobile) */}
      <Dialog
        open={openConfirmDialog}
        onClose={handleCancelClear}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography id="confirm-dialog-description">
            Êtes-vous sûr de vouloir effacer tous les éléments de la carte ? Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelClear} color="primary">
            Annuler
          </Button>
          <Button onClick={handleConfirmClear} color="error" variant="contained" autoFocus>
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de génération de Path */}
      <PathGeneratorDialog
        open={pathGeneratorOpen}
        onClose={() => setPathGeneratorOpen(false)}
        onAdd={(element: ShapeElement) => onAddElement(element)}
      />

      {/* Modale de génération IA */}
      <AiTemplateGenerationModal
        open={aiGenerationModalOpen}
        onClose={() => setAiGenerationModalOpen(false)}
        templateType={templateType}
        onTemplateSelect={handleTemplateSelect}
      />
    </Paper>
  );
};

export default Toolbar;
