export interface User {
  id: string;
  email: string;
  password_hash: string;
  
  // États de vérification et activation
  profil_verifier: boolean;
  email_verifier: boolean;
  profil_actif: boolean;
  role: string | null;
  login_attempts: number;
  
  // Champs spécifiques pour les jobbeurs
  job_title?: string;
  user_type: 'jobbeur' | 'non-jobbeur';
  
  last_login: string | null;
  date_inscription?: string;
}

export interface AuthToken {
  id: string;
  user_id: string;
  token: string;
  type: 'email_verification' | 'password_reset';
  expires_at: string;
  created_at: string;
}

// Types pour les réponses API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SecurityEvent {
  type: string;
  ip: string;
  timestamp: Date;
  userId?: string;
  details?: any;
}

export interface SessionData {
  userId: string;
  sessionId: string;
  lastActivity: number;
}
