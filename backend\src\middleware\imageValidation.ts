import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

/**
 * Middleware pour valider les images envoyées pour modération
 * Vérifie que l'image est dans un format valide et ne dépasse pas la taille maximale
 */
export const validateImageForModeration = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { image, imageMimeType } = req.body;

    // Si aucune image n'est fournie, continuer sans validation
    if (!image || !imageMimeType) {
      next();
      return;
    }

    // Vérifier que le type MIME est valide
    const validMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
    if (!validMimeTypes.includes(imageMimeType)) {
      logger.warn('Type d\'image non valide pour la modération', {
        mimeType: imageMimeType,
        userId: req.user?.userId
      });
      res.status(400).json({
        success: false,
        message: 'Format d\'image non pris en charge. Formats acceptés : JPEG, PNG, GIF, WEBP, BMP'
      });
      return;
    }

    // Vérifier que l'image est au format base64 valide
    if (!image.startsWith('data:image/')) {
      logger.warn('Format base64 d\'image invalide', {
        userId: req.user?.userId
      });
      res.status(400).json({
        success: false,
        message: 'Format d\'image invalide. L\'image doit être au format base64 avec en-tête data:image/'
      });
      return;
    }

    // Extraire le contenu base64 (supprimer le préfixe data:image/xxx;base64,)
    const base64Data = image.replace(/^data:image\/\w+;base64,/, '');

    // Vérifier que le contenu base64 est valide
    try {
      const buffer = Buffer.from(base64Data, 'base64');

      // Vérifier la taille de l'image (max 5 Mo)
      const maxSize = 5 * 1024 * 1024; // 5 Mo
      if (buffer.length > maxSize) {
        logger.warn('Image trop volumineuse pour la modération', {
          size: buffer.length,
          maxSize,
          userId: req.user?.userId
        });
        res.status(400).json({
          success: false,
          message: 'Image trop volumineuse. Taille maximale : 5 Mo'
        });
        return;
      }
    } catch (error) {
      logger.error('Erreur lors de la conversion de l\'image base64', {
        error,
        userId: req.user?.userId
      });
      res.status(400).json({
        success: false,
        message: 'Données d\'image invalides'
      });
      return;
    }

    // Si toutes les validations sont passées, continuer
    next();
  } catch (error) {
    logger.error('Erreur lors de la validation de l\'image', {
      error,
      userId: req.user?.userId
    });
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de l\'image'
    });
  }
};
