import { Router } from 'express';
import { clientController } from '../controllers/clientController';
import { authMiddleware } from '../middleware/authMiddleware';
import { csrfProtection } from '../middleware/csrf';
import rateLimit from 'express-rate-limit';
import { RequestHandler } from 'express';

const router = Router();

// Configuration du rate limiter
const clientsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limite chaque IP à 100 requêtes par fenêtre
  message: 'Trop de requêtes, veuillez réessayer plus tard',
  standardHeaders: true,
  legacyHeaders: false,
});

// Protéger toutes les routes avec l'authentification
router.use(authMiddleware.authenticateToken as RequestHandler);

// Routes pour les clients
router.get('/', clientsLimiter, clientController.getClients as RequestHandler);
router.get('/export/excel', clientsLimiter, clientController.exportClientsToExcel as RequestHandler);
router.get('/:id', clientsLimiter, clientController.getClient as RequestHandler);
router.post('/', clientsLimiter, csrfProtection as RequestHandler, clientController.createClient as RequestHandler);
router.put('/:id', clientsLimiter, csrfProtection as RequestHandler, clientController.updateClient as RequestHandler);
router.delete('/:id', clientsLimiter, csrfProtection as RequestHandler, clientController.deleteClient as RequestHandler);

export default router; 