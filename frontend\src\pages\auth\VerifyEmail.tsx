import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useState, useCallback, useEffect, useRef } from 'react';
import { CheckCircle2, XCircle } from 'lucide-react';
import { AnimatedSuccessIcon } from '../../components/Animations';
import { notify } from '@/components/Notification';
import { getCookie, removeCookie } from '../../utils/cookieUtils'; // Import the cookie utility
import { Helmet } from 'react-helmet-async';
import LoadingBar from '../../components/LoadingBar';
import DOMPurify from 'dompurify';
import logger from '../../utils/logger';

const VerifyEmail = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { verifyEmail } = useAuth();
  const [loading, setLoading] = useState(true);
  const [showResult, setShowResult] = useState(false);
  const [verified, setVerified] = useState(false);
  const [alreadyVerified, setAlreadyVerified] = useState(false);
  const [redirectCountdown, setRedirectCountdown] = useState(10);
  const [error, setError] = useState<string | null>(null);

  const token = new URLSearchParams(location.search).get('token');
  const fromSignup = location.state?.fromSignup || false;

  // Utiliser useRef pour tracker si la notification a déjà été affichée
  const hasNotifiedRef = useRef(false);

  // Utiliser useRef pour éviter les multiples appels de vérification
  const verificationInProgress = useRef(false);

  const verifyEmailToken = useCallback(async () => {
    // Utiliser un flag pour éviter les multiples appels
    if (verificationInProgress.current) return;

    try {
      verificationInProgress.current = true;
      setLoading(true);
      setShowResult(false);
      setError(null);

      // // Si vient de l'inscription, ne pas vérifier le token
      if (fromSignup) {
        setLoading(false);
        return;
      }

      if (!token) {
        const pendingEmail = getCookie('pendingVerificationEmail');

        if (pendingEmail) {
          setLoading(false);
          setShowResult(true);
          return; // Afficher l'interface de renvoi
        }

        // N'afficher la notification qu'une seule fois avec useRef
        if (!hasNotifiedRef.current) {
          notify('Token de vérification manquant', 'error');
          hasNotifiedRef.current = true;
        }
        setLoading(false);
        setShowResult(true);
        return;
      }

      // Nettoyer le token pour éviter les injections XSS
      const sanitizedToken = DOMPurify.sanitize(token);

      try {
        logger.info('Tentative de vérification d\'email avec token sanitisé', {
          tokenLength: sanitizedToken.length
        });

        const result = await verifyEmail(sanitizedToken);

        // Attendre 1 seconde avant d'afficher le résultat
        setTimeout(() => {
          if (result.success) {
            if (result.alreadyVerified) {
              setAlreadyVerified(true);
            }
            setVerified(true);
          }
          setLoading(false);
          setShowResult(true);
        }, 1000);
      } catch (verifyError) {
        logger.error('Erreur lors de la vérification d\'email dans le composant', verifyError);
        throw verifyError; // Propager l'erreur pour qu'elle soit capturée par le bloc catch parent
      }

    } catch (err: any) {
      logger.error('Erreur lors de la vérification d\'email:', {
        message: err.message,
        name: err.name,
        stack: err.stack
      });

      // Afficher un message d'erreur plus convivial selon le type d'erreur
      let errorMessage = err.message;

      // Si c'est une erreur réseau ou de parsing JSON
      if (err.name === 'TypeError' || err.name === 'SyntaxError') {
        errorMessage = 'Problème de communication avec le serveur. Veuillez réessayer.';
      }

      setTimeout(() => {
        setError(errorMessage);
        setLoading(false);
        setShowResult(true);
      }, 1000);
    } finally {
      verificationInProgress.current = false;
    }
  }, [token, verifyEmail, fromSignup]);

  useEffect(() => {
    let isMounted = true;
    const performVerification = async () => {
      if (isMounted) {
        await verifyEmailToken();
      }
    };

    // N'exécuter qu'une seule fois et seulement si pas fromSignup
    if (!hasNotifiedRef.current && !fromSignup) {
      performVerification();
    }

    return () => {
      isMounted = false;
    };
  }, [verifyEmailToken, fromSignup]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (verified && !fromSignup) {
      timer = setInterval(() => {
        setRedirectCountdown((prev) => {
          const newCount = prev - 1;
          if (newCount <= 0) {
            clearInterval(timer);
            // Utiliser setTimeout pour éviter les mises à jour pendant le rendu
            setTimeout(() => {
              navigate('/login');
            }, 0);
            return 0;
          }
          return newCount;
        });
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [verified, navigate, fromSignup]);

  useEffect(() => {
    // Vérifier le cooldown
    const storedCooldown = getCookie('verificationEmailCooldown');
    if (storedCooldown) {
      const cooldownTime = parseInt(storedCooldown);
      const now = Date.now();
      if (cooldownTime > now) {
        removeCookie('verificationEmailCooldown');
      }
    }
  }, []);

  useEffect(() => {
    // Si une erreur est présente, on supprime le cooldown
    if (error) {
      removeCookie('verificationEmailCooldown');
    }
  }, [error]);

  // Si pas de token et vient de l'inscription, afficher la page d'attente de vérification
  if (!token || fromSignup) {
    return (
      <div>
        <style>
          {`
            .animate-gradient {
              background: linear-gradient(-45deg, #FF7A35, #ff965e, #e56826, #FF7A35);
              background-size: 400% 400%;
              animation: gradient 3s ease infinite;
            }

            @keyframes gradient {
              0% {
                background-position: 0% 50%;
              }
              50% {
                background-position: 100% 50%;
              }
              100% {
                background-position: 0% 50%;
              }
            }
          `}
        </style>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-24 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8 p-10 bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl transform transition-all duration-500">
            <div className="text-center space-y-6">
              <AnimatedSuccessIcon />
              <h2 className="text-3xl font-extrabold bg-gradient-to-r from-[#FF7A35] to-[#ff965e] bg-clip-text text-transparent">
                Vérifiez votre email
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed">
                Nous vous avons envoyé un email avec un lien de vérification.
                <br />
                <span className="text-sm text-gray-500 mt-2 block">
                  Cliquez sur le lien dans l'email pour activer votre compte.
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div>
      <Helmet>
        <title>Vérification de l'Email - JobPartiel.fr</title>
        <meta name="description" content="Vérifiez votre adresse email pour activer votre compte JobPartiel.fr." />
        <meta charSet="utf-8" />
        <meta name="keywords" content="vérification de l'email, JobPartiel.fr" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta property="og:title" content="Vérification de l'Email - JobPartiel.fr" />
        <meta property="og:description" content="Vérifiez votre adresse email pour activer votre compte JobPartiel.fr." />
        <meta property="og:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
        <meta property="og:url" content="https://jobpartiel.fr/verify-email" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="Vérification de l'Email - JobPartiel.fr" />
        <meta name="twitter:description" content="Vérifiez votre adresse email pour activer votre compte JobPartiel.fr." />
        <meta name="twitter:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
        <link rel="canonical" href="https://jobpartiel.fr/verify-email" />
      </Helmet>
      <style>
        {`
          .animate-gradient {
            background: linear-gradient(-45deg, #FF7A35, #ff965e, #e56826, #FF7A35);
            background-size: 400% 400%;
            animation: gradient 3s ease infinite;
          }

          @keyframes gradient {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }
        `}
      </style>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 p-10 bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl transform transition-all duration-500">
          {loading ? (
            <LoadingBar title="Vérification de votre email" />
          ) : showResult && (
            verified ? (
              <div className="text-center space-y-6">
                <div className="relative mb-8">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 rounded-full bg-green-100 animate-pulse"></div>
                  </div>
                  <CheckCircle2 className="mx-auto h-16 w-16 text-green-500 relative z-10 animate-bounce" />
                </div>
                <h2 className="text-3xl font-extrabold bg-gradient-to-r from-[#FF7A35] to-[#ff965e] bg-clip-text text-transparent">
                  {alreadyVerified ? "Email déjà vérifié" : "Email vérifié avec succès\u00A0!"}
                </h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  {alreadyVerified ? "Votre email a déjà été vérifié précédemment." : "Votre email a été vérifié."}
                  <br />
                  <span className="text-sm text-gray-500 mt-2 block">
                    Redirection vers la page de connexion dans <span className="font-medium text-[#FF7A35]">{redirectCountdown}</span> secondes...
                  </span>
                </p>
              </div>
            ) : (
              <div className="text-center space-y-6">
                <div className="relative mb-8">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 rounded-full bg-red-100 animate-pulse"></div>
                  </div>
                  <XCircle className="mx-auto h-16 w-16 text-red-500 relative z-10 animate-bounce" />
                </div>
                <h2 className="text-3xl font-extrabold bg-gradient-to-r from-[#FF7A35] to-[#ff965e] bg-clip-text text-transparent">
                  Lien invalide
                </h2>
                <p className="text-gray-600 text-lg leading-relaxed">
                  Le lien de vérification est invalide ou a expiré.
                  <br />
                  <span className="text-sm text-gray-500 mt-2 block">
                    Connectez-vous à nouveau, un nouvel email de vérification sera envoyé et le lien sera renouvellé.
                  </span>
                  {error && (
                    <span className="text-sm text-red-500 mt-2 block">
                      Erreur : {error}
                    </span>
                  )}
                </p>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default VerifyEmail;
