import React, { useState, useEffect, useRef } from 'react';
import {
  TextField,
  Box,
  Typography,
  Chip,
  InputAdornment,
  CircularProgress,
  Paper
} from '@mui/material';
import { LocationOn, TrendingUp, Star } from '@mui/icons-material';
import { searchCitiesWithSuggestions, validateCity, CitySuggestion, isPostalCode, normalizeString } from '../../utils/geocoding';

// Couleurs JobPartiel
const COLORS = {
  primary: '#FF6B2C',
  secondary: '#FF7A35',
  tertiary: '#FF965E',
  accent: '#FFE4BA',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  white: '#FFFFFF',
  neutral: '#6B7280',
  borderColor: '#E5E7EB',
  background: '#FFF8F3'
};

// Villes populaires avec tendances
const POPULAR_CITIES = [
  { name: 'Paris', region: 'Île-de-France', trending: true, popular: true },
  { name: 'Lyon', region: 'Auvergne-Rhône-Alpes', trending: true, popular: true },
  { name: 'Marseille', region: 'Provence-Alpes-Côte d\'Azur', trending: false, popular: true },
  { name: 'Toulouse', region: 'Occitanie', trending: true, popular: true },
  { name: 'Nice', region: 'Provence-Alpes-Côte d\'Azur', trending: false, popular: true },
  { name: 'Nantes', region: 'Pays de la Loire', trending: true, popular: true },
  { name: 'Montpellier', region: 'Occitanie', trending: true, popular: false },
  { name: 'Strasbourg', region: 'Grand Est', trending: false, popular: true },
  { name: 'Bordeaux', region: 'Nouvelle-Aquitaine', trending: true, popular: true },
  { name: 'Lille', region: 'Hauts-de-France', trending: false, popular: true }
];

interface CitySearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationChange?: (isValid: boolean) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  placeholder?: string;
  error?: string;
  helperText?: string;
  disabled?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium';
  variant?: 'outlined' | 'filled' | 'standard';
  showPopularCities?: boolean;
}

const CitySearchInput: React.FC<CitySearchInputProps> = ({
  value,
  onChange,
  onValidationChange,
  onFocus,
  onBlur,
  placeholder = "Ville ou code postal (ex: Paris, 75001, Lyon 69000...)",
  error,
  helperText,
  disabled = false,
  fullWidth = true,
  size = 'medium',
  variant = 'outlined',
  showPopularCities = true
}) => {
  const [suggestions, setSuggestions] = useState<CitySuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [isValid, setIsValid] = useState(true);
  const [validationError, setValidationError] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [open, setOpen] = useState(false);
  const debounceRef = useRef<NodeJS.Timeout>(null);

  // Mettre à jour inputValue quand value change
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Fonction pour rechercher des suggestions avec villes populaires et codes postaux
  const searchSuggestions = async (query: string) => {
    if (!query || query.length < 2) {
      // Si pas de query et showPopularCities, afficher les villes populaires
      if (showPopularCities && isFocused) {
        const popularSuggestions = POPULAR_CITIES.slice(0, 6).map(city => ({
          nom: city.name,
          code_postal: '',
          coordinates: [0, 0] as [number, number],
          score: 1,
          trending: city.trending,
          popular: city.popular,
          region: city.region
        }));
        setSuggestions(popularSuggestions);
        setOpen(popularSuggestions.length > 0);
      } else {
        setSuggestions([]);
        setOpen(false);
      }
      return;
    }

    setLoading(true);
    try {
      // Rechercher dans l'API avec support des codes postaux
      const apiResults = await searchCitiesWithSuggestions(query, 6);

      // Rechercher dans les villes populaires (nom de ville ou région) - insensible aux accents
      const normalizedQuery = normalizeString(query);
      const popularMatches = POPULAR_CITIES
        .filter(city =>
          normalizeString(city.name).includes(normalizedQuery) ||
          normalizeString(city.region).includes(normalizedQuery)
        )
        .slice(0, 3)
        .map(city => ({
          nom: city.name,
          code_postal: '',
          coordinates: [0, 0] as [number, number],
          score: 1,
          trending: city.trending,
          popular: city.popular,
          region: city.region
        }));

      // Combiner les résultats en évitant les doublons
      const combinedResults = [...popularMatches];
      apiResults.forEach(apiResult => {
        if (!combinedResults.some(existing => existing.nom.toLowerCase() === apiResult.nom.toLowerCase())) {
          combinedResults.push({
            ...apiResult,
            trending: false,
            popular: false,
            region: ''
          });
        }
      });

      const finalResults = combinedResults.slice(0, 8);
      setSuggestions(finalResults);
      setOpen(finalResults.length > 0);
    } catch (error) {
      console.error('Erreur lors de la recherche de villes:', error);
      setSuggestions([]);
      setOpen(false);
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour valider la ville ou code postal (plus permissive)
  const validateCityInput = async (cityName: string) => {
    if (!cityName.trim()) {
      setIsValid(true);
      setValidationError('');
      onValidationChange?.(true);
      return;
    }

    try {
      const trimmedInput = cityName.trim();

      // Vérifier d'abord si c'est un code postal
      if (isPostalCode(trimmedInput)) {
        // Si c'est un code postal, rechercher directement
        const postalResults = await searchCitiesWithSuggestions(trimmedInput, 1);
        if (postalResults.length > 0) {
          setIsValid(true);
          setValidationError('');
          onValidationChange?.(true);
          return;
        } else {
          setIsValid(false);
          setValidationError('Code postal non trouvé. Veuillez vérifier.');
          onValidationChange?.(false);
          return;
        }
      }

      // Pour les noms de ville, être plus permissif
      // Accepter si la ville fait au moins 2 caractères
      if (trimmedInput.length >= 2) {
        // Essayer de valider avec l'API
        const validCity = await validateCity(trimmedInput);

        if (validCity) {
          // Ville trouvée dans l'API
          setIsValid(true);
          setValidationError('');
          onValidationChange?.(true);
        } else {
          // Ville non trouvée dans l'API, mais on accepte quand même
          // L'utilisateur peut avoir tapé une ville valide qui n'est pas dans notre base
          setIsValid(true);
          setValidationError(''); // Pas d'erreur, on laisse passer
          onValidationChange?.(true);
        }
      } else {
        // Moins de 2 caractères
        setIsValid(false);
        setValidationError('Veuillez saisir au moins 2 caractères.');
        onValidationChange?.(false);
      }
    } catch (error) {
      console.error('Erreur lors de la validation de ville:', error);
      // En cas d'erreur, on accepte quand même si c'est au moins 2 caractères
      if (cityName.trim().length >= 2) {
        setIsValid(true);
        setValidationError('');
        onValidationChange?.(true);
      } else {
        setIsValid(false);
        setValidationError('Veuillez saisir au moins 2 caractères.');
        onValidationChange?.(false);
      }
    }
  };

  // Gérer les changements d'input avec debounce
  const handleInputChange = (_: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue);

    // IMPORTANT: Mettre à jour l'état parent immédiatement
    onChange(newInputValue);

    // Réinitialiser les erreurs pendant la saisie
    if (validationError) {
      setValidationError('');
      setIsValid(true);
      onValidationChange?.(true);
    }

    // Debounce pour les suggestions
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      searchSuggestions(newInputValue);
    }, 300);
  };

  // Gérer le focus
  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
    // Afficher les villes populaires si pas de texte
    if (!inputValue && showPopularCities) {
      searchSuggestions('');
    }
  };

  // Gérer la perte de focus pour validation
  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
    validateCityInput(inputValue);
    // Masquer les suggestions après un délai plus long pour permettre la sélection
    setTimeout(() => {
      setOpen(false);
    }, 300);
  };

  // Nettoyer le debounce
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <Box sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>
      <TextField
        placeholder={placeholder}
        variant={variant}
        size={size}
        value={inputValue}
        onChange={(e) => handleInputChange(e as any, e.target.value)}
        onFocus={handleFocus}
        onBlur={handleBlur}
        error={!!error || !!validationError || !isValid}
        helperText={error || validationError || helperText}
        disabled={disabled}
        fullWidth={fullWidth}
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment position="start">
                <LocationOn
                  sx={{
                    color: isFocused ? COLORS.primary : (isValid ? COLORS.neutral : COLORS.error),
                    transition: 'all 0.2s ease',
                    transform: isFocused ? 'scale(1.1)' : 'scale(1)'
                  }}
                />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {loading && <CircularProgress size={20} sx={{ color: COLORS.primary }} />}
              </InputAdornment>
            )
          }
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: 4,
            bgcolor: COLORS.white,
            border: `2px solid ${COLORS.borderColor}`,
            fontSize: '1.1rem',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              borderColor: COLORS.secondary,
              transform: 'translateY(-1px)',
              boxShadow: `0 4px 15px ${COLORS.secondary}20`
            },
            '&.Mui-focused': {
              borderColor: COLORS.primary,
              boxShadow: `0 0 0 3px ${COLORS.primary}20, 0 6px 20px ${COLORS.primary}15`,
              transform: 'translateY(-2px)'
            },
            '& fieldset': {
              border: 'none',
            },
          },
          '& .MuiInputBase-input': {
            fontWeight: 500,
            py: size === 'small' ? 1 : 2,
          }
        }}
      />

      {/* Liste de suggestions personnalisée */}
      {open && suggestions.length > 0 && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1300,
            maxHeight: '300px',
            overflowY: 'auto',
            mt: 1,
            boxShadow: `0 8px 30px ${COLORS.primary}15`,
            borderRadius: 3,
            border: `1px solid ${COLORS.borderColor}`,
            bgcolor: COLORS.white
          }}
        >
          {suggestions.map((suggestion, index) => {
            const extendedOption = suggestion as CitySuggestion & { trending?: boolean; popular?: boolean; region?: string };
            return (
              <Box
                key={`${suggestion.nom}-${suggestion.code_postal}-${index}`}
                onClick={() => {
                  // L'API attend juste le nom de la ville, pas le code postal
                  onChange(suggestion.nom);
                  setOpen(false);
                  setIsValid(true);
                  setValidationError('');
                  onValidationChange?.(true);
                }}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  py: 1.5,
                  px: 2,
                  cursor: 'pointer',
                  borderBottom: index < suggestions.length - 1 ? `1px solid ${COLORS.borderColor}` : 'none',
                  '&:hover': {
                    bgcolor: `${COLORS.primary}08`,
                    color: COLORS.primary
                  }
                }}
              >
                <LocationOn sx={{ fontSize: 18, color: COLORS.primary }} />
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {extendedOption.nom}
                    </Typography>
                    {extendedOption.trending && (
                      <Chip
                        icon={<TrendingUp sx={{ fontSize: 14 }} />}
                        label="Tendance"
                        size="small"
                        sx={{
                          bgcolor: `${COLORS.warning}15`,
                          color: COLORS.warning,
                          fontSize: '0.7rem',
                          height: 20,
                          '& .MuiChip-icon': {
                            fontSize: 12
                          }
                        }}
                      />
                    )}
                    {extendedOption.popular && (
                      <Chip
                        icon={<Star sx={{ fontSize: 14 }} />}
                        label="Populaire"
                        size="small"
                        sx={{
                          bgcolor: `${COLORS.success}15`,
                          color: COLORS.success,
                          fontSize: '0.7rem',
                          height: 20,
                          '& .MuiChip-icon': {
                            fontSize: 12
                          }
                        }}
                      />
                    )}
                  </Box>
                  {(extendedOption.code_postal || extendedOption.region) && (
                    <Typography variant="caption" color="text.secondary">
                      {extendedOption.code_postal && `${extendedOption.code_postal}`}
                      {extendedOption.region && extendedOption.code_postal && ` • `}
                      {extendedOption.region && `${extendedOption.region}`}
                    </Typography>
                  )}
                </Box>
                {extendedOption.score && extendedOption.score < 1 && (
                  <Chip
                    label={`${Math.round(extendedOption.score * 100)}%`}
                    size="small"
                    variant="outlined"
                    sx={{
                      fontSize: '0.7rem',
                      borderColor: COLORS.borderColor,
                      color: COLORS.neutral
                    }}
                  />
                )}
              </Box>
            );
          })}
        </Paper>
      )}

      {/* Message de chargement */}
      {loading && open && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1300,
            mt: 1,
            boxShadow: `0 8px 30px ${COLORS.primary}15`,
            borderRadius: 3,
            border: `1px solid ${COLORS.borderColor}`,
            bgcolor: COLORS.white,
            py: 2,
            textAlign: 'center'
          }}
        >
          <Typography color={COLORS.primary}>
            Recherche en cours...
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default CitySearchInput;
