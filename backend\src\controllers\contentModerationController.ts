import { Request, Response } from 'express';
import contentModerationService, { ContentToModerate } from '../services/contentModerationService';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import { z } from 'zod';
import { containsBadWords, commonSafePatterns } from '../utils/badwords';
import { redis } from '../config/redis';
import { getPublicUrl, uploadTemporaryImageForModeration } from '../services/storage';
import { decryptProfilDataAsync, decryptUserDataAsync, hashEmail } from '../utils/encryption';

// Préfixes pour les clés de cache
const CONTROLLER_MODERATION_PREFIX = 'controller_moderation_ia:';
const MODERATION_HISTORY_PREFIX = 'moderation_history_ia:';

// Constantes pour la configuration du cache
const CACHE_EXPIRY_MODERATION = 60 * 60; // 1 heure pour les résultats de modération
const CACHE_EXPIRY_HISTORY = 5 * 60; // 5 minutes pour l'historique de modération

/**
 * Invalide le cache de l'historique de modération pour un utilisateur spécifique
 * @param userId ID de l'utilisateur dont le cache doit être invalidé
 */
async function invalidateModerationHistoryCache(userId: string) {
  try {
    // Récupérer toutes les clés de cache liées à l'historique de modération de cet utilisateur
    const keys = await redis.keys(`${MODERATION_HISTORY_PREFIX}*"userId":"${userId}"*`);

    // Récupérer également les clés pour les admins (qui peuvent voir les modérations de tous les utilisateurs)
    const adminKeys = await redis.keys(`${MODERATION_HISTORY_PREFIX}*"userId":"admin"*`);

    // Combiner les deux ensembles de clés
    const allKeys = [...keys, ...adminKeys];

    if (allKeys.length > 0) {
      // Supprimer les clés par lots de 100 pour éviter les erreurs de mémoire
      const batchSize = 100;
      for (let i = 0; i < allKeys.length; i += batchSize) {
        const batch = allKeys.slice(i, i + batchSize);
        await redis.del(...batch);
      }
      logger.info(`Cache d'historique de modération invalidé pour l'utilisateur ${userId}`, {
        userId,
        keysCount: allKeys.length
      });
    }
  } catch (error) {
    logger.error('Erreur lors de l\'invalidation du cache d\'historique de modération', { error, userId });
  }
}

// Schéma de validation pour la requête de modération
const moderateContentSchema = z.object({
  text: z.string().min(1).max(5000),
  type: z.enum([
    // Types existants
    'mission', 'comment', 'profile', 'titre_service', 'description_service',
    'gallery_name', 'gallery_description', 'mission_title', 'mission_description', 'review',
    // Nouveaux types pour les galeries et images
    'gallery', 'gallery_cover', 'featured', 'mission_assistant',
    // Types pour les photos de profil
    'avatar', 'profile_picture', 'banner_picture',
    // Types spécifiques pour la génération d'images IA
    'featured_photo', 'mission_image', 'gallery_photo'
  ]),
  contentId: z.string().optional(),
  image: z.string().optional(), // Base64 de l'image (optionnel)
  imageMimeType: z.string().optional(), // Type MIME de l'image (optionnel)
  imageUrl: z.string().optional(), // URL de l'image à modérer (alternative à image+imageMimeType)
  tempImagePath: z.string().optional(), // Chemin temporaire de l'image dans le bucket (pour suppression si nécessaire)
});

/**
 * Modère un contenu (texte) pour détecter du contenu inapproprié
 */
export async function moderateContent(req: Request, res: Response) {
  try {
    // Récupérer l'ID de l'utilisateur depuis le token JWT
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Valider les données de la requête
    const validationResult = moderateContentSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors: validationResult.error.errors
      });
    }

    const { text, type, contentId = `temp-${Date.now()}`, image, imageMimeType, imageUrl, tempImagePath } = validationResult.data;

    // --- AJOUT : Invalidation du cache si contentId temporaire et texte modifié ---
    if (contentId.startsWith('temp-')) {
      // Supprimer toutes les clés de cache du contrôleur et du service pour ce contentId temporaire
      // (pour ce user/type), pour éviter de réutiliser un cache obsolète
      const controllerPattern = `${CONTROLLER_MODERATION_PREFIX}${userId}:${type}:*`;
      const servicePattern = `content_moderation:temp:*`;
      // On ne peut pas savoir le hash exact sans le texte, donc on supprime tout ce qui correspond à ce user/type
      const controllerKeys = await redis.keys(controllerPattern);
      const serviceKeys = await redis.keys(servicePattern);
      // On ne supprime que les clés qui contiennent le contentId temporaire (sécurité)
      const keysToDelete = [
        ...controllerKeys.filter(k => k.includes(contentId)),
        ...serviceKeys.filter(k => k.includes(contentId))
      ];
      if (keysToDelete.length > 0) {
        await redis.del(...keysToDelete);
        logger.info('Cache invalidé pour contentId temporaire corrigé', {
          userId,
          contentType: type,
          contentId,
          keysDeleted: keysToDelete.length
        });
      }
    }

    // Vérifier si le résultat est déjà en cache au niveau du contrôleur
    // Cela permet d'éviter même la vérification préliminaire pour les textes déjà traités
    // Si une image est fournie, inclure son hash dans la clé de cache
    let cacheKeyBase = `${CONTROLLER_MODERATION_PREFIX}${userId}:${type}:${Buffer.from(text).toString('base64')}`;

    // Ajouter l'image à la clé de cache si elle est fournie (base64 ou URL)
    if (image && imageMimeType) {
      const imageHash = require('crypto')
        .createHash('md5')
        .update(image)
        .digest('hex');
      cacheKeyBase += `:image:${imageHash}`;
    } else if (imageUrl) {
      const urlHash = require('crypto')
        .createHash('md5')
        .update(imageUrl)
        .digest('hex');
      cacheKeyBase += `:url:${urlHash}`;
    }

    const controllerCacheKey = cacheKeyBase;
    const cachedResult = await redis.get(controllerCacheKey);

    if (cachedResult) {
      logger.info('Modération : Résultat récupéré depuis le cache du contrôleur', {
        userId,
        contentType: type,
        contentId,
        hasImage: !!image,
        hasImageUrl: !!imageUrl
      });
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedResult)
      });
    }

    // Préparer le contenu à modérer
    const contentToModerate: ContentToModerate = {
      text,
      type,
      contentId,
      userId
    };

    // Ajouter l'URL de l'image si elle est fournie
    if (imageUrl) {
      contentToModerate.imageUrl = imageUrl;

      // Si un chemin temporaire est fourni, l'ajouter également
      if (tempImagePath) {
        contentToModerate.tempImagePath = tempImagePath;
      }

      logger.info('URL d\'image ajoutée pour modération', {
        userId,
        contentType: type,
        contentId,
        imageUrl,
        tempImagePath
      });
    }
    // Sinon, si une image base64 est fournie, la convertir en buffer
    else if (image && imageMimeType) {
      try {
        // Extraire le contenu base64 (supprimer le préfixe data:image/xxx;base64,)
        const base64Data = image.replace(/^data:image\/\w+;base64,/, '');
        const imageBuffer = Buffer.from(base64Data, 'base64');

        // Uploader l'image dans le bucket temporaire et récupérer le chemin
        const { filePath } = await uploadTemporaryImageForModeration(userId, imageBuffer, imageMimeType);
        contentToModerate.tempImagePath = filePath;

        // Ajouter l'image au contenu à modérer
        contentToModerate.imageBuffer = imageBuffer;
        contentToModerate.imageMimeType = imageMimeType;

        logger.info('Image base64 uploadée et ajoutée pour modération', {
          userId,
          contentType: type,
          contentId,
          imageSize: imageBuffer.length,
          imageMimeType,
          tempImagePath: filePath
        });
      } catch (error) {
        logger.error('Erreur lors de l\'upload ou de la conversion de l\'image base64', {
          error,
          userId,
          contentType: type,
          contentId
        });
        // Continuer sans l'image en cas d'erreur
      }
    }

    // Utilisation des patterns sûrs et de la fonction de vérification des mots interdits
    // importés depuis le module centralisé badwords.ts

    // Vérifier si le texte correspond à un pattern sûr ET ne contient pas de mots interdits
    // La longueur maximale est de 50 caractères (limite du système de commentaires)
    // IMPORTANT: On vérifie d'abord si le texte contient des mots interdits, car c'est la vérification la plus importante
    const hasBadWords = containsBadWords(text);

    // Si le texte contient des mots interdits, il n'est pas considéré comme sûr, quelle que soit sa longueur ou son début
    if (hasBadWords) {
      logger.info('Texte contenant des mots interdits détecté', {
        text: text.substring(0, 50),
        userId,
        contentId
      });
      // On ne retourne pas ici, on laisse le processus continuer vers l'API de modération
    }

    // Un texte est considéré comme sûr UNIQUEMENT s'il ne contient PAS de mots interdits
    // ET s'il commence par un pattern sûr ET s'il est dans la limite de caractères
    const isCommonSafeText = !hasBadWords &&
                            text.length <= 50 &&
                            commonSafePatterns.some(pattern => pattern.test(text.toLowerCase()));

    if (isCommonSafeText) {
      logger.info('Modération : Texte détecté comme sûr par vérification préliminaire (commence par une phrase courante, sans mots interdits)', {
        text: text.substring(0, 50),
        userId,
        contentId,
        length: text.length
      });

      // Créer le résultat pour un texte sûr
      const safeResult = {
        isSafe: true,
        score: 0.1,
        categories: {
          harassment: false,
          hateSpeech: false,
          sexualContent: false,
          violence: false,
          selfHarm: false,
          illegalActivity: false,
          spam: false,
          phoneSpam: false,
          addressSpam: false,
          unknownRisk: false
        },
        flaggedText: [],
        moderationId: `bypass-${Date.now()}`
      };

      // Mettre en cache le résultat
      await redis.set(controllerCacheKey, JSON.stringify(safeResult), 'EX', CACHE_EXPIRY_MODERATION);

      // Retourner directement un résultat positif sans appeler l'API
      return res.status(200).json({
        success: true,
        data: safeResult
      });
    }

    // Appeler le service de modération pour les autres cas
    const moderationResult = await contentModerationService.moderateContent(contentToModerate);

    // Mettre en cache le résultat au niveau du contrôleur
    await redis.set(controllerCacheKey, JSON.stringify(moderationResult), 'EX', CACHE_EXPIRY_MODERATION);

    // Si le contenu est inapproprié, invalider le cache de l'historique de modération
    // pour que les nouvelles entrées apparaissent immédiatement
    if (!moderationResult.isSafe) {
      await invalidateModerationHistoryCache(userId);
    }

    // Retourner le résultat
    return res.status(200).json({
      success: true,
      data: moderationResult
    });
  } catch (error) {
    logger.error('Erreur lors de la modération du contenu', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la modération du contenu'
    });
  }
}

/**
 * Nettoie le cache de modération en supprimant les entrées obsolètes
 * Cette fonction peut être appelée périodiquement par une tâche cron
 */
export async function cleanModerationCache() {
  try {
    // Récupérer toutes les clés de cache liées à la modération
    const moderationKeys = await redis.keys(`${CONTROLLER_MODERATION_PREFIX}*`);
    const historyKeys = await redis.keys(`${MODERATION_HISTORY_PREFIX}*`);
    const imageHashKeys = await redis.keys(`image_hash:*`);
    const imageModerationKeys = await redis.keys(`image_moderation:content:*`);

    logger.info('Nettoyage du cache de modération', {
      moderationKeysCount: moderationKeys.length,
      historyKeysCount: historyKeys.length,
      imageHashKeysCount: imageHashKeys.length,
      imageModerationKeysCount: imageModerationKeys.length
    });

    // Supprimer un pourcentage des clés les plus anciennes pour éviter une surcharge du cache
    // Pour cela, on utilise la commande SCAN pour parcourir les clés et TTL pour vérifier leur durée de vie restante

    // Pour les clés de modération de contenu
    if (moderationKeys.length > 1000) {
      // Trier les clés par TTL (du plus petit au plus grand)
      const keysWithTTL = await Promise.all(
        moderationKeys.map(async (key) => {
          const ttl = await redis.ttl(key);
          return { key, ttl };
        })
      );

      // Trier par TTL croissant (les plus proches de l'expiration d'abord)
      keysWithTTL.sort((a, b) => a.ttl - b.ttl);

      // Supprimer les 20% les plus anciens
      const keysToDelete = keysWithTTL.slice(0, Math.floor(keysWithTTL.length * 0.2)).map(item => item.key);

      if (keysToDelete.length > 0) {
        // Supprimer les clés par lots de 100 pour éviter les erreurs de mémoire
        const batchSize = 100;
        for (let i = 0; i < keysToDelete.length; i += batchSize) {
          const batch = keysToDelete.slice(i, i + batchSize);
          await redis.del(...batch);
        }
        logger.info(`${keysToDelete.length} clés de modération de contenu supprimées du cache`);
      }
    }

    // Pour les clés d'historique de modération
    if (historyKeys.length > 500) {
      // Même logique que pour les clés de modération
      const keysWithTTL = await Promise.all(
        historyKeys.map(async (key) => {
          const ttl = await redis.ttl(key);
          return { key, ttl };
        })
      );

      keysWithTTL.sort((a, b) => a.ttl - b.ttl);

      // Supprimer les 30% les plus anciens (l'historique est plus important à rafraîchir)
      const keysToDelete = keysWithTTL.slice(0, Math.floor(keysWithTTL.length * 0.3)).map(item => item.key);

      if (keysToDelete.length > 0) {
        // Supprimer les clés par lots de 100 pour éviter les erreurs de mémoire
        const batchSize = 100;
        for (let i = 0; i < keysToDelete.length; i += batchSize) {
          const batch = keysToDelete.slice(i, i + batchSize);
          await redis.del(...batch);
        }
        logger.info(`${keysToDelete.length} clés d'historique de modération supprimées du cache`);
      }
    }

    // Pour les clés de hash d'image
    if (imageHashKeys.length > 1000) {
      // Trier les clés par TTL (du plus petit au plus grand)
      const keysWithTTL = await Promise.all(
        imageHashKeys.map(async (key) => {
          const ttl = await redis.ttl(key);
          return { key, ttl };
        })
      );

      // Trier par TTL croissant (les plus proches de l'expiration d'abord)
      keysWithTTL.sort((a, b) => a.ttl - b.ttl);

      // Supprimer les 20% les plus anciens
      const keysToDelete = keysWithTTL.slice(0, Math.floor(keysWithTTL.length * 0.2)).map(item => item.key);

      if (keysToDelete.length > 0) {
        // Supprimer les clés par lots de 100 pour éviter les erreurs de mémoire
        const batchSize = 100;
        for (let i = 0; i < keysToDelete.length; i += batchSize) {
          const batch = keysToDelete.slice(i, i + batchSize);
          await redis.del(...batch);
        }
        logger.info(`${keysToDelete.length} clés de hash d'image supprimées du cache`);
      }
    }

    // Pour les clés de modération d'image
    if (imageModerationKeys.length > 1000) {
      // Trier les clés par TTL (du plus petit au plus grand)
      const keysWithTTL = await Promise.all(
        imageModerationKeys.map(async (key) => {
          const ttl = await redis.ttl(key);
          return { key, ttl };
        })
      );

      // Trier par TTL croissant (les plus proches de l'expiration d'abord)
      keysWithTTL.sort((a, b) => a.ttl - b.ttl);

      // Supprimer les 10% les plus anciens (moins que les autres car ces résultats sont plus importants)
      const keysToDelete = keysWithTTL.slice(0, Math.floor(keysWithTTL.length * 0.1)).map(item => item.key);

      if (keysToDelete.length > 0) {
        // Supprimer les clés par lots de 100 pour éviter les erreurs de mémoire
        const batchSize = 100;
        for (let i = 0; i < keysToDelete.length; i += batchSize) {
          const batch = keysToDelete.slice(i, i + batchSize);
          await redis.del(...batch);
        }
        logger.info(`${keysToDelete.length} clés de modération d'image supprimées du cache`);
      }
    }

    return {
      success: true,
      message: 'Cache de modération nettoyé avec succès',
      stats: {
        moderationKeysTotal: moderationKeys.length,
        historyKeysTotal: historyKeys.length,
        imageHashKeysTotal: imageHashKeys.length,
        imageModerationKeysTotal: imageModerationKeys.length
      }
    };
  } catch (error) {
    logger.error('Erreur lors du nettoyage du cache de modération', { error });
    return {
      success: false,
      message: 'Erreur lors du nettoyage du cache de modération',
      error: String(error)
    };
  }
}

export async function getModerationHistory(req: Request, res: Response) {
  try {
    // Récupérer l'ID de l'utilisateur depuis le token JWT
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Vérifier si l'utilisateur est un administrateur ou modérateur
    const isAdmin = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    // Paramètres de pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    // Paramètres de tri
    const sortBy = (req.query.sort_by as string) || 'created_at';
    const sortOrder = (req.query.sort_order as string) === 'asc' ? true : false;

    // Paramètres de filtrage
    const contentType = req.query.content_type as string;
    const isSafe = req.query.is_safe !== undefined ? req.query.is_safe === 'true' : undefined;
    const minScore = req.query.score_min ? parseFloat(req.query.score_min as string) : undefined;
    const maxScore = req.query.score_max ? parseFloat(req.query.score_max as string) : undefined;
    const filterUserId = req.query.user_id as string;
    const filterUserEmail = req.query.user_email as string;
    const dateFrom = req.query.date_from as string;
    const dateTo = req.query.date_to as string;
    const search = req.query.search as string;
    const moderationMethod = req.query.moderation_method as string;
    const category = req.query.category as string;

    // Construire la clé de cache en fonction des paramètres de requête
    // Inclure tous les paramètres de filtrage, tri et pagination dans la clé
    const cacheParams = {
      userId: isAdmin ? 'admin' : userId,
      page,
      limit,
      sortBy,
      sortOrder,
      contentType,
      isSafe,
      minScore,
      maxScore,
      filterUserId,
      filterUserEmail,
      dateFrom,
      dateTo,
      search,
      moderationMethod,
      category
    };

    const cacheKey = `${MODERATION_HISTORY_PREFIX}${JSON.stringify(cacheParams)}`;

    // Vérifier si les résultats sont en cache
    const cachedResults = await redis.get(cacheKey);

    if (cachedResults) {
      logger.info('Historique de modération récupéré depuis le cache', {
        userId,
        isAdmin,
        page,
        limit
      });
      return res.status(200).json(JSON.parse(cachedResults));
    }

    // Construire la requête en fonction du rôle
    let query = supabase
      .from('content_moderation_logs')
      .select(`
        *,
        quality_assessment,
        user:user_id (
          id,
          email,
          role,
          user_type,
          profil_verifier,
          identite_verifier,
          entreprise_verifier,
          assurance_verifier,
          email_verifier,
          profil_actif,
          date_inscription,
          last_login,
          profil:user_profil (
            id,
            user_id,
            slug,
            nom,
            prenom,
            telephone,
            numero,
            adresse,
            ville,
            code_postal,
            pays,
            photo_url,
            bio,
            storage_id,
            type_de_profil,
            nom_entreprise,
            prenom_entreprise,
            statut_entreprise,
            siren_entreprise,
            code_ape_entreprise,
            categorie_entreprise,
            effectif_entreprise,
            date_insee_creation_entreprise,
            date_categorie_entreprise,
            date_derniere_mise_a_jour_entreprise_insee,
            date_derniere_mise_a_jour_du_client_entreprise,
            date_validation_document_assurance,
            date_validation_document_entreprise,
            date_validation_document_identite,
            telephone_prive,
            mode_vacance,
            intervention_zone,
            created_at,
            updated_at,
            profil_visible
          )
        )
      `, { count: 'exact' });

    // Si l'utilisateur n'est pas admin, filtrer par son ID
    if (!isAdmin) {
      query = query.eq('user_id', userId);
    } else {
      // Appliquer les filtres (uniquement pour les admins)
      if (contentType) {
        query = query.eq('content_type', contentType);
      }

      if (isSafe !== undefined) {
        query = query.eq('is_safe', isSafe);
      }

      // Filtrer par score minimum si fourni
      if (minScore !== undefined) {
        query = query.gte('score', minScore);
      }

      // Filtrer par score maximum si fourni
      if (maxScore !== undefined) {
        query = query.lte('score', maxScore);
      }

      // Filtrer par méthode de modération
      if (moderationMethod) {
        if (moderationMethod === 'image') {
          // Pour les modérations d'images, utiliser plusieurs conditions
          query = query.or(
            `is_image_moderation.eq.true,
             content_type.eq.gallery,
             content_type.eq.gallery_cover,
             content_type.eq.featured,
             content_type.eq.mission_assistant,
             moderation_id.ilike.image-%,
             moderation_id.ilike.error-image-%`
          );
        } else {
          // Pour les autres méthodes, vérifier si la méthode de modération commence par le préfixe spécifié
          query = query.ilike('moderation_id', `${moderationMethod}-%`);
        }
      }

      // Filtrer par catégorie
      if (category) {
        // Utiliser une recherche dans l'objet JSON des catégories
        // Les catégories sont stockées comme un objet JSON où chaque clé est une catégorie
        // et la valeur est un booléen indiquant si la catégorie est détectée
        query = query.eq(`categories->>${category}`, true);
      }

      if (filterUserId) {
        query = query.eq('user_id', filterUserId);
      }

      // Filtrer par email utilisateur si fourni
      if (filterUserEmail && filterUserEmail.trim() !== '') {
        try {
          // Utiliser le hash d'email pour une recherche efficace et sécurisée
          const emailHashToSearch = hashEmail(filterUserEmail.trim());

          // Rechercher l'utilisateur par hash d'email
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id')
            .eq('email_hash', emailHashToSearch)
            .maybeSingle();

          if (userError) {
            logger.error('Erreur lors de la recherche de l\'utilisateur par hash d\'email', {
              error: userError,
              emailPrefix: filterUserEmail.substring(0, 3) + '***'
            });
            // En cas d'erreur, on retourne un résultat vide
            return res.status(200).json({
              success: true,
              data: [],
              pagination: {
                page,
                limit,
                total: 0,
                totalPages: 0
              }
            });
          }

          if (!userData) {
            // Si aucun utilisateur trouvé avec cet email, retourner un résultat vide
            logger.info(`Aucun utilisateur trouvé avec l'email: ${filterUserEmail.substring(0, 3)}***`);
            return res.status(200).json({
              success: true,
              data: [],
              pagination: {
                page,
                limit,
                total: 0,
                totalPages: 0
              }
            });
          }

          // Utilisateur trouvé, filtrer par son ID
          query = query.eq('user_id', userData.id);
          logger.info('Filtrage par utilisateur trouvé via hash d\'email', {
            userId: userData.id,
            emailPrefix: filterUserEmail.substring(0, 3) + '***'
          });
        } catch (error) {
          logger.error('Exception lors de la recherche par email', { error });
          // En cas d'erreur, on retourne un résultat vide
          return res.status(200).json({
            success: true,
            data: [],
            pagination: {
              page,
              limit,
              total: 0,
              totalPages: 0
            }
          });
        }
      }

      if (dateFrom) {
        query = query.gte('created_at', `${dateFrom}T00:00:00`);
      }

      if (dateTo) {
        query = query.lte('created_at', `${dateTo}T23:59:59`);
      }

      if (search) {
        query = query.or(`content_id.ilike.%${search}%,flagged_text.cs.{%${search}%}`);
      }
    }

    // Ajouter le tri et la pagination
    query = query
      .order(sortBy, { ascending: sortOrder })
      .range(offset, offset + limit - 1);

    // Exécuter la requête
    const { data, error, count } = await query;

    if (error) {
      logger.error('Erreur lors de la récupération de l\'historique de modération', { error });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique de modération'
      });
    }

    // Formater les données pour inclure les informations utilisateur
    const formattedData = await Promise.all(data.map(async (log) => {
      const user = log.user;
      const profil = user?.profil;

      let userName = 'Utilisateur inconnu';
      let userEmail = 'Email non disponible';

      // Accéder au premier élément si profil est un tableau
      const userProfile = Array.isArray(profil) ? profil[0] : profil;

      // Déchiffrer les données utilisateur et profil
      const decryptedUser = user ? await decryptUserDataAsync(user) : null;
      const decryptedProfile = userProfile ? await decryptProfilDataAsync(userProfile) : null;

      // Initialiser userInfo avec les données utilisateur et profil déchiffrées
      const userInfo: any = {
        id: log.user_id,
        email: decryptedUser?.email || 'Email non disponible',
        role: decryptedUser?.role || null,
        user_type: decryptedUser?.user_type || null,
        profil_verifier: decryptedUser?.profil_verifier || false,
        identite_verifier: decryptedUser?.identite_verifier || false,
        entreprise_verifier: decryptedUser?.entreprise_verifier || false,
        assurance_verifier: decryptedUser?.assurance_verifier || false,
        email_verifier: decryptedUser?.email_verifier || false,
        profil_actif: decryptedUser?.profil_actif || false,
        date_inscription: decryptedUser?.date_inscription || null,
        last_login: decryptedUser?.last_login || null,
        // Utiliser les données du profil déchiffrées si elles existent
        prenom: decryptedProfile?.prenom || null,
        nom: decryptedProfile?.nom || null,
        telephone: decryptedProfile?.telephone || null,
        numero: decryptedProfile?.numero || null,
        adresse: decryptedProfile?.adresse || null,
        ville: decryptedProfile?.ville || null,
        code_postal: decryptedProfile?.code_postal || null,
        pays: decryptedProfile?.pays || null,
        photo_url: decryptedProfile?.photo_url || null,
        bio: decryptedProfile?.bio || null,
        storage_id: decryptedProfile?.storage_id || null,
        type_de_profil: decryptedProfile?.type_de_profil || null,
        slug: decryptedProfile?.slug || null,
        profil_visible: decryptedProfile?.profil_visible ?? null,
        nom_entreprise: decryptedProfile?.nom_entreprise || null,
        prenom_entreprise: decryptedProfile?.prenom_entreprise || null,
        statut_entreprise: decryptedProfile?.statut_entreprise || null,
        siren_entreprise: decryptedProfile?.siren_entreprise || null,
        code_ape_entreprise: decryptedProfile?.code_ape_entreprise || null,
        categorie_entreprise: decryptedProfile?.categorie_entreprise || null,
        effectif_entreprise: decryptedProfile?.effectif_entreprise || null,
        date_insee_creation_entreprise: decryptedProfile?.date_insee_creation_entreprise || null,
        date_categorie_entreprise: decryptedProfile?.date_categorie_entreprise || null,
        date_derniere_mise_a_jour_entreprise_insee: decryptedProfile?.date_derniere_mise_a_jour_entreprise_insee || null,
        date_derniere_mise_a_jour_du_client_entreprise: decryptedProfile?.date_derniere_mise_a_jour_du_client_entreprise || null,
        date_validation_document_assurance: decryptedProfile?.date_validation_document_assurance || null,
        date_validation_document_entreprise: decryptedProfile?.date_validation_document_entreprise || null,
        date_validation_document_identite: decryptedProfile?.date_validation_document_identite || null,
        telephone_prive: decryptedProfile?.telephone_prive || false,
        mode_vacance: decryptedProfile?.mode_vacance || false,
        intervention_zone: decryptedProfile?.intervention_zone || null,
      };

      // Construire le nom d'utilisateur de manière plus fiable
      if (userInfo.prenom && userInfo.nom) {
        userName = `${userInfo.prenom} ${userInfo.nom}`;
      } else if (decryptedUser?.email) {
        userName = decryptedUser.email.split('@')[0]; // Fallback vers l'email si nom/prénom manquants
      }

      userEmail = decryptedUser?.email || 'Email non disponible';

      // Déterminer si c'est une modération d'image
      const isImageModeration =
        log.is_image_moderation || // Si le champ existe déjà
        log.content_type === 'gallery' ||
        log.content_type === 'gallery_cover' ||
        log.content_type === 'featured' ||
        log.content_type === 'mission_assistant' ||
        (log.content_type === 'profile' && log.moderation_id && (log.moderation_id.startsWith('image-') || log.moderation_id.startsWith('gen-')));

      // Générer l'URL publique de l'image modérée si image_path existe
      let image_url = undefined;
      if (isImageModeration && log.image_path) {
        image_url = getPublicUrl('temp_moderation', log.image_path);
      }

      return {
        ...log,
        user_name: userName,
        user_email: userEmail,
        user_info: userInfo,
        is_image_moderation: isImageModeration,
        image_url,
        qualityAssessment: log.quality_assessment || null
      };
    }));

    // Préparer la réponse
    const response = {
      success: true,
      data: formattedData,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: count ? Math.ceil(count / limit) : 0
      }
    };

    // Mettre en cache les résultats
    // Utiliser une durée de cache plus courte pour les admins (qui ont besoin de données plus fraîches)
    // et plus longue pour les utilisateurs normaux
    const cacheExpiry = isAdmin ? CACHE_EXPIRY_HISTORY : CACHE_EXPIRY_HISTORY * 2;
    await redis.set(cacheKey, JSON.stringify(response), 'EX', cacheExpiry);

    // Retourner les résultats
    return res.status(200).json(response);
  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique de modération', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'historique de modération'
    });
  }
}

/**
 * Supprime une image temporaire de modération (bucket temp_moderation)
 * Accessible uniquement aux admins/modos
 */
export async function deleteModerationImage(req: Request, res: Response) {
  try {
    const { userId, filePath } = req.body;
    if (!userId || !filePath) {
      return res.status(400).json({ success: false, message: 'userId et filePath requis' });
    }

    // Vérifier que l'utilisateur est un admin ou un modérateur
    const isAdmin = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';
    if (!isAdmin) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé' });
    }

    // Supprimer l'image du bucket temp_moderation
    const { error } = await supabase.storage
      .from('temp_moderation')
      .remove([filePath]);

    if (error) {
      logger.error('Erreur lors de la suppression de l\'image de modération', { error, userId, filePath });
      return res.status(500).json({ success: false, message: 'Erreur lors de la suppression de l\'image' });
    }

    // Supprimer également les clés Redis associées
    const imageHashKey = `image_hash:${filePath}`;
    await redis.del(imageHashKey);

    // Mettre à jour le signalement pour indiquer que l'image a été supprimée
    await supabase
      .from('reported_content')
      .update({ temp_image_path: null })
      .eq('temp_image_path', filePath);

    logger.info('Image de modération supprimée avec succès', { userId, filePath });
    return res.status(200).json({ success: true, message: 'Image supprimée avec succès' });
  } catch (error) {
    logger.error('Erreur lors de la suppression d\'une image de modération', { error });
    return res.status(500).json({ success: false, message: 'Erreur lors de la suppression de l\'image' });
  }
}

/**
 * Crée un signalement automatique pour une image qui a été modérée avec un ID temporaire
 * et qui est maintenant enregistrée avec un UUID permanent
 */
export async function createDeferredImageReport(req: Request, res: Response) {
  try {
    // Récupérer l'ID de l'utilisateur depuis le token JWT
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Valider les données de la requête
    const { tempImageId, permanentImageId, contentType } = req.body;

    if (!tempImageId || !permanentImageId || !contentType) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides: tempImageId, permanentImageId et contentType sont requis'
      });
    }

    // Vérifier que l'ID permanent est bien un UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(permanentImageId)) {
      return res.status(400).json({
        success: false,
        message: 'L\'ID permanent doit être un UUID valide'
      });
    }

    // Appeler le service pour créer le signalement différé
    await contentModerationService.createDeferredImageReport(tempImageId, permanentImageId, contentType, userId);

    // Retourner une réponse de succès
    return res.status(200).json({
      success: true,
      message: 'Signalement différé créé avec succès'
    });
  } catch (error) {
    logger.error('Erreur lors de la création du signalement différé', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la création du signalement différé'
    });
  }
}

