import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { notify } from '../components/Notification';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';

// Interface pour l'historique des crédits IA
export interface AiCreditHistoryItem {
  id: string;
  user_id: string;
  operation_type: 'achat_jobi' | 'achat_stripe' | 'utilisation' | 'offert_abonnement' | 'offert_admin' | 'autre';
  montant: number;
  solde_avant: number;
  solde_apres: number;
  description?: string;
  reference?: string;
  created_at: string;
}

// Interface pour la pagination
export interface Pagination {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
}

// Interface pour les données de crédits IA
interface AiCreditsData {
  credits: number;
  loading: boolean;
  error: string | null;
  isRateLimited: boolean;
  history: {
    items: AiCreditHistoryItem[];
    pagination: Pagination | null;
    loading: boolean;
    error: string | null;
  };
  buyCredits: (packQuantity: number) => Promise<void>;
  buyCreditsWithStripe: (packQuantity: number) => Promise<void>;
  useCredit: () => Promise<boolean>;
  refetch: () => Promise<void>;
  fetchHistory: (page?: number, limit?: number) => Promise<void>;
}

export const useAiCredits = (): AiCreditsData => {
  const { isAuthenticated } = useAuth();
  const [credits, setCredits] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isRateLimited, setIsRateLimited] = useState<boolean>(false);

  // État pour l'historique des crédits IA
  const [historyItems, setHistoryItems] = useState<AiCreditHistoryItem[]>([]);
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const [historyPagination, setHistoryPagination] = useState<Pagination | null>(null);

  const fetchCredits = useCallback(async () => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setIsRateLimited(false); // Réinitialiser l'état de rate limit à chaque tentative
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/ai-credits`, {
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        setCredits(response.data.credits);
        setError(null);
      } else {
        setError(response.data.message || 'Erreur lors de la récupération des crédits IA');
      }
    } catch (err: any) {
      if (axios.isAxiosError(err) && err.response?.status === 429) {
        setIsRateLimited(true);
        setError('Trop de requêtes. Veuillez réessayer dans quelques minutes.');
        logger.warn('Rate limit atteint pour la récupération des crédits IA');
      } else {
        setError(err.response?.data?.message || 'Erreur lors de la récupération des crédits IA');
        logger.error('Erreur lors de la récupération des crédits IA:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  const buyCredits = async (packQuantity: number = 1): Promise<void> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour acheter des crédits IA', 'error');
      return;
    }

    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/ai-credits/buy`,
        { packQuantity },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        setCredits(response.data.credits);
        notify(response.data.message || 'Crédits IA achetés avec succès', 'success');
      } else {
        notify(response.data.message || 'Erreur lors de l\'achat des crédits IA', 'error');
      }
    } catch (err: any) {
      notify(err.response?.data?.message || 'Erreur lors de l\'achat des crédits IA', 'error');
      console.error('Erreur lors de l\'achat des crédits IA:', err);
    } finally {
      setLoading(false);
    }
  };

  const buyCreditsWithStripe = async (packQuantity: number = 1): Promise<void> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour acheter des crédits IA', 'error');
      return;
    }

    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/ai-credits/buy-stripe`,
        { packQuantity },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success && response.data.url) {
        // Rediriger vers la page de paiement Stripe
        window.location.href = response.data.url;
      } else {
        notify(response.data.message || 'Erreur lors de la création de la session de paiement', 'error');
      }
    } catch (err: any) {
      notify(err.response?.data?.message || 'Erreur lors de la création de la session de paiement', 'error');
      console.error('Erreur lors de la création de la session de paiement:', err);
    } finally {
      setLoading(false);
    }
  };

  const useCredit = async (): Promise<boolean> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour utiliser des crédits IA', 'error');
      return false;
    }

    if (credits <= 0) {
      notify('Vous n\'avez pas assez de crédits IA, veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
      return false;
    }

    try {
      setLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/ai-credits/use`, {}, {
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        setCredits(response.data.credits);
        return true;
      } else {
        notify(response.data.message || 'Erreur lors de l\'utilisation du crédit IA', 'error');
        return false;
      }
    } catch (err: any) {
      notify(err.response?.data?.message || 'Erreur lors de l\'utilisation du crédit IA', 'error');
      console.error('Erreur lors de l\'utilisation du crédit IA:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour récupérer l'historique des crédits IA
  const fetchHistory = useCallback(async (page: number = 1, limit: number = 10) => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setHistoryLoading(true);
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/ai-credits/history`, {
        params: { page, limit },
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        setHistoryItems(response.data.history);
        setHistoryPagination(response.data.pagination);
        setHistoryError(null);
      } else {
        setHistoryError(response.data.message || 'Erreur lors de la récupération de l\'historique des crédits IA');
      }
    } catch (err: any) {
      setHistoryError(err.response?.data?.message || 'Erreur lors de la récupération de l\'historique des crédits IA');
      console.error('Erreur lors de la récupération de l\'historique des crédits IA:', err);
    } finally {
      setHistoryLoading(false);
    }
  }, [isAuthenticated]);

  return {
    credits,
    loading,
    error,
    isRateLimited,
    history: {
      items: historyItems,
      pagination: historyPagination,
      loading: historyLoading,
      error: historyError
    },
    buyCredits,
    buyCreditsWithStripe,
    useCredit,
    refetch: fetchCredits,
    fetchHistory
  };
};
