import React, { useState, useEffect } from 'react';
import { advancedStatsService, AdvancedStats } from '../../services/advancedStats';
import LoadingSpinner from '../../components/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import { qualityMetricsService, QualityMetric, Review } from '../../services/qualityMetrics';
import ReviewModalListeDesAvis from '../../components/ReviewModalListeDesAvis';
import logger from '../../utils/logger';
import { API_CONFIG } from '../../config/api';
import axios from 'axios';
import { HelpCircle } from 'lucide-react';
import { Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Typography } from '@mui/material';
import { notify } from '@/components/Notification';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

const QualityPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('30');
  const [stats, setStats] = useState<AdvancedStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [qualityMetrics, setQualityMetrics] = useState<QualityMetric[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reportLoading, setReportLoading] = useState<boolean>(false);
  const [isPremium, setIsPremium] = useState<boolean>(false);
  const [reviewStats, setReviewStats] = useState<{ rating: number; total_reviews: number; completion_rate: number } | null>(null);
  const { user } = useAuth();

  // Vérifier si l'utilisateur a un abonnement premium
  useEffect(() => {
    const checkSubscription = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions/status`, {
          withCredentials: true,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        logger.info('Abonnement premium:', response.data);
        setIsPremium(response.data.plan === 'premium');
      } catch (error) {
        console.error('Erreur lors de la vérification de l\'abonnement:', error);
        setIsPremium(false);
      } finally {
        setLoading(false);
      }
    };

    checkSubscription();
  }, []);

  // Récupérer les statistiques avancées
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        if (isPremium) {
          const data = await advancedStatsService.getStats(timeRange);
          setStats(data);
        } else {
          // Pour les utilisateurs sans abonnement premium, on initialise des stats vides
          setStats(null);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        notify('Erreur lors du chargement des statistiques', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange, isPremium]);

  // Récupérer les métriques de qualité et les avis
  useEffect(() => {
    const fetchQualityData = async () => {
      if (!user?.id) return;

      try {
        if (isPremium) {
          const response = await qualityMetricsService.getQualityMetrics();
          
          if (response.success) {
            setQualityMetrics(response.metrics);
            setReviews(response.reviews);
          }
        } else {
          // Pour les utilisateurs sans abonnement premium, on initialise des métriques et avis vides
          setQualityMetrics([]);
          setReviews([]);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des métriques de qualité:', error);
        notify('Impossible de charger vos métriques de qualité', 'error');
      }
    };

    fetchQualityData();
  }, [user, isPremium]);

  // Générer un rapport PDF détaillé
  const generateReport = async () => {
    if (!isPremium) {
      notify('Cette fonctionnalité requiert un abonnement Premium', 'error');
      return;
    }

    try {
      setReportLoading(true);
      await qualityMetricsService.generateQualityReport(timeRange);
      notify('Rapport généré et téléchargé avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      notify('Erreur lors de la génération du rapport', 'error');
    } finally {
      setReportLoading(false);
    }
  };

  // Mettre à jour les stats des avis
  const handleStatsUpdate = (stats: { rating: number; total_reviews: number; completion_rate: number }) => {
    setReviewStats(stats);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div>
      <div className="space-y-6 px-2 md:px-0">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <PageTitle variant="h1">
            Qualité & Performance
          </PageTitle>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 w-full md:w-auto">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="bg-white border border-gray-300 text-gray-700 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            >
              <option value="7">7 derniers jours</option>
              <option value="30">30 derniers jours</option>
              <option value="90">90 derniers jours</option>
            </select>
            <button 
              className="bg-[#FF7A35] text-white px-4 py-2 rounded-md hover:bg-[#ff6b2c] transition-colors flex items-center justify-center"
              onClick={generateReport}
              disabled={reportLoading || !isPremium}
            >
              {reportLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Génération...
                </>
              ) : 'Exporter en PDF'}
            </button>
          </div>
        </div>

        {/* Bannière d'abonnement premium */}
        {!isPremium && (
          <div className="bg-gradient-to-r from-[#ff9c65] to-[#FF7A35] text-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Débloquez les statistiques avancées</h3>
                <p className="text-sm">Les valeurs affichées sont des exemples. Passez à Premium pour accéder à vos données réelles.</p>
              </div>
              <div className="flex-shrink-0 ml-4">
                <a 
                  href="/dashboard/abonnement" 
                  className="inline-block bg-white text-[#FF7A35] px-4 py-2 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium"
                >
                  Passer à Premium
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Métriques clés */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Satisfaction</h3>
              <Tooltip 
                title="Moyenne des notes reçues sur vos missions, convertie en pourcentage (note/5 × 20). Calculée à partir des avis laissés par vos clients." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur la satisfaction"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.clientStats.tauxSatisfaction || reviewStats?.rating || 0}%</p>
            <p className="text-sm text-green-600">+{stats?.clientStats.croissanceMensuelle || 0}%</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Taux complétion</h3>
              <Tooltip 
                title="Pourcentage de missions que vous avez menées à bien jusqu'à leur terme. Calculé en divisant le nombre de missions terminées par le nombre total de missions acceptées." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le taux de complétion"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.tauxCompletion?.pourcentage || reviewStats?.completion_rate || 0}%</p>
            <p className="text-sm text-green-600">
              {stats && stats.tauxCompletion && stats.tauxCompletion.evolution && stats.tauxCompletion.evolution.length >= 2 ? 
                `${(stats.tauxCompletion.evolution[stats.tauxCompletion.evolution.length - 1].taux - 
                  stats.tauxCompletion.evolution[stats.tauxCompletion.evolution.length - 2].taux) > 0 ? '+' : ''}
                ${stats.tauxCompletion.evolution[stats.tauxCompletion.evolution.length - 1].taux - 
                  stats.tauxCompletion.evolution[stats.tauxCompletion.evolution.length - 2].taux}%` 
                : '+0%'}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Ponctualité</h3>
              <Tooltip 
                title="Pourcentage des missions réalisées dans les délais prévus. Estimé à partir du taux de complétion en tenant compte de la satisfaction globale des clients." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur la ponctualité"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.tauxCompletion?.pourcentage ? Math.min(98, stats.tauxCompletion.pourcentage + 3) : 0}%</p>
            <p className="text-sm text-green-600">+2%</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Conversion</h3>
              <Tooltip 
                title="Pourcentage de candidatures qui ont été acceptées par les clients. Calculé en divisant le nombre de missions acceptées par le nombre total de candidatures soumises." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le taux de conversion"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.clientStats.tauxConversion || 0}%</p>
            <p className="text-sm text-green-600">+3%</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Temps réponse</h3>
              <Tooltip 
                title="Délai moyen entre la réception d'une candidature et votre première réponse. Calculé uniquement sur les réponses données dans les 48 heures pour éviter les valeurs aberrantes." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le temps de réponse"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.delaiMoyen?.formatte || '0min'}</p>
            <p className="text-sm text-green-600">-2min</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-500">Rétention</h3>
              <Tooltip 
                title="Pourcentage de clients qui ont effectué plus d'une mission avec vous. Mesure votre capacité à fidéliser vos clients et à créer des relations durables." 
                placement="top"
                enterTouchDelay={0}
                leaveTouchDelay={3000}
              >
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="ml-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Plus d'informations sur le taux de rétention"
                >
                  <HelpCircle className="h-3 w-3 text-gray-400" />
                </button>
              </Tooltip>
            </div>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{stats?.clientStats.tauxRetention || 0}%</p>
            <p className="text-sm text-green-600">+5%</p>
          </div>
        </div>

        {/* Contrôles qualité basés sur les avis */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold text-gray-800">Contrôles qualité personnalisés</h2>
            <p className="text-sm text-gray-500 mt-1">
              Basés sur vos avis reçus - Vos points forts et axes d'amélioration
            </p>
          </div>
          {qualityMetrics.length > 0 ? (
            <>
              <div className="px-6 pt-4 pb-2">
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-md">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        Chaque barre représente le score obtenu pour une compétence, de 0% à 100%. La ligne verticale indique l'objectif à atteindre.
                        Les métriques en <span className="text-green-700 font-medium">vert</span> sont positives, tandis que celles en <span className="text-red-700 font-medium">rouge</span> nécessitent votre attention.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                {qualityMetrics.map((check, index) => (
                  <div key={index} className="border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow bg-white">
                    <div className="flex justify-between items-center mb-5">
                      <div>
                        <h3 className="font-medium text-gray-900 text-base">{check.name}</h3>
                        <p className="text-sm text-gray-500">
                          {check.status === 'above'
                            ? `Point fort (${check.count} mentions)`
                            : `À améliorer (${check.count} mentions)`
                          }
                        </p>
                      </div>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full shadow-sm ${
                        check.status === 'above'
                          ? 'bg-gradient-to-r from-green-50 to-green-100 text-green-800 border border-green-200'
                          : 'bg-gradient-to-r from-red-50 to-red-100 text-red-800 border border-red-200'
                      }`}>
                        {check.status === 'above' ? 'Positif' : 'À améliorer'}
                      </span>
                    </div>
                    <div className="relative pt-1">
                      <div className="flex mb-3 items-center justify-between">
                        <div>
                          <span className="text-sm font-semibold inline-block text-[#FF7A35]">
                            {check.score}%
                          </span>
                        </div>
                        <div>
                          <span className="text-sm font-medium inline-block text-gray-600">
                            Limite: {check.target}%
                          </span>
                        </div>
                      </div>
                      <div className="flex">
                        <div className="w-full bg-gray-100 rounded-lg h-4 relative overflow-hidden shadow-inner border border-gray-200">
                          <div
                            className={`h-full ${
                              check.status === 'above'
                                ? 'bg-gradient-to-r from-[#ffb88c] to-[#FF7A35]' 
                                : 'bg-gradient-to-r from-[#ff9a9e] to-[#ff5252]'
                            } rounded-lg transition-all duration-300 ease-in-out`}
                            style={{ 
                              width: `${check.score}%`,
                              position: 'absolute',
                              left: '0',
                              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                            }}
                          />
                          <div
                            className="absolute h-8 flex items-center justify-center z-10 pointer-events-none"
                            style={{ 
                              left: `${check.target}%`, 
                              top: '50%', 
                              transform: 'translateY(-50%)' 
                            }}
                          >
                            <div className="w-1 h-full bg-gray-700 shadow-md"></div>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between mt-2">
                        <div className="text-xs font-medium text-gray-500">0%</div>
                        <div className="text-xs font-medium text-gray-700 bg-gray-100 px-3 py-1 rounded-full shadow-sm border border-gray-200">
                          Objectif: {check.target}%
                        </div>
                        <div className="text-xs font-medium text-gray-500">100%</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="col-span-2 text-center py-8 text-gray-500">
              {isPremium 
                ? "Aucune donnée qualité disponible. Demandez à vos clients de vous évaluer pour obtenir des insights personnalisés."
                : "Les contrôles qualité personnalisés sont disponibles avec l'abonnement Premium."}
            </div>
          )}
        </div>

        {/* Recommandations */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold text-gray-800">Recommandations d'amélioration</h2>
            <p className="text-sm text-gray-500 mt-1">
              Basées sur vos points à améliorer et statistiques récentes
            </p>
          </div>
          <div className="p-6">
            {isPremium ? (
              <>
                {qualityMetrics.filter(metric => metric.status === 'below').length > 0 ? (
                  <div className="space-y-6">
                    {qualityMetrics
                      .filter(metric => metric.status === 'below')
                      .slice(0, 2)
                      .map((metric, index) => (
                        <div key={index} className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center space-x-4">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-orange-50 to-orange-100 rounded-full flex items-center justify-center text-[#FF7A35] border border-orange-200 shadow-sm">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900">Améliorer : {metric.name}</h3>
                              <p className="text-sm text-gray-500 mt-1">Actuellement {metric.score}% — Objectif : {metric.target}%</p>
                            </div>
                          </div>
                          <div className="mt-4 ml-14">
                            <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-3 rounded-r-md">
                              <p className="text-sm text-gray-700">
                                {metric.name.toLowerCase().includes('temp') || metric.name.toLowerCase().includes('délai') || metric.name.toLowerCase().includes('rapide') ? 
                                  "Essayez d'optimiser votre processus de travail pour réduire le temps de réponse et de traitement." :
                                  metric.name.toLowerCase().includes('communication') || metric.name.toLowerCase().includes('contact') ? 
                                  "Améliorez votre communication avec vos clients en établissant des points de contact réguliers." :
                                  "Concentrez-vous sur cette compétence lors de vos prochaines missions pour progresser."
                                }
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                    }
                    
                    {/* Recommandations génériques si nécessaire */}
                    {qualityMetrics.filter(metric => metric.status === 'below').length < 2 && (
                      <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-orange-50 to-orange-100 rounded-full flex items-center justify-center text-[#FF7A35] border border-orange-200 shadow-sm">
                            {qualityMetrics.filter(metric => metric.status === 'below').length + 1}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">Améliorer votre suivi client</h3>
                            <p className="text-sm text-gray-500 mt-1">Augmentez votre taux de satisfaction</p>
                          </div>
                        </div>
                        <div className="mt-4 ml-14">
                          <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-3 rounded-r-md">
                            <p className="text-sm text-gray-700">
                              Contactez systématiquement vos clients après chaque mission pour vous assurer que tout s'est bien passé et recueillir leurs commentaires.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-green-500 mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900">Excellentes performances !</h3>
                    <p className="text-sm text-gray-500 text-center mt-2 max-w-md">
                      Vous n'avez actuellement aucun point majeur à améliorer. Continuez à maintenir ce niveau de qualité dans vos missions.
                    </p>
                  </div>
                )}
              </>
            ) : (
              <div className="space-y-6">
                <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-orange-50 to-orange-100 rounded-full flex items-center justify-center text-[#FF7A35] border border-orange-200 shadow-sm">
                      1
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">Réduire le temps de réponse moyen</h3>
                      <p className="text-sm text-gray-500 mt-1">Objectif : moins de 10 minutes</p>
                    </div>
                  </div>
                  <div className="mt-4 ml-14">
                    <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-3 rounded-r-md">
                      <p className="text-sm text-gray-700">
                        Un temps de réponse rapide augmente considérablement vos chances d'être sélectionné pour une mission.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-orange-50 to-orange-100 rounded-full flex items-center justify-center text-[#FF7A35] border border-orange-200 shadow-sm">
                      2
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">Améliorer le suivi après-service</h3>
                      <p className="text-sm text-gray-500 mt-1">Contacter vos clients après chaque mission</p>
                    </div>
                  </div>
                  <div className="mt-4 ml-14">
                    <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-3 rounded-r-md">
                      <p className="text-sm text-gray-700">
                        Le suivi client est essentiel pour obtenir des avis positifs et fidéliser votre clientèle.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 text-center">
                  <a 
                    href="/dashboard/abonnement" 
                    className="inline-block bg-gradient-to-r from-[#ff9c65] to-[#FF7A35] text-white px-5 py-2 rounded-md hover:from-[#ff8a47] hover:to-[#ff6b2c] transition-colors text-sm font-medium shadow-sm"
                  >
                    Passer à Premium pour des recommandations personnalisées
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Graphique d'évolution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Évolution du taux de complétion</h2>
          
          {stats && stats.tauxCompletion && stats.tauxCompletion.evolution ? (
            <div style={{ height: "250px" }} className="relative">
              {/* Ligne de 100% */}
              <div className="absolute top-8 left-0 right-0 border-t border-dashed border-gray-300 z-10">
                <span className="absolute right-0 bg-gray-100 text-gray-600 text-xs px-1 -mt-5">100%</span>
              </div>
              
              <div className="grid grid-cols-6 gap-2 absolute top-8 bottom-24 left-0 right-0">
                {stats.tauxCompletion.evolution.map((item, index) => (
                  <div key={index} className="flex flex-col items-center h-full">
                    <div className="h-full w-full flex flex-col justify-end pb-1 relative">
                      {item.taux === 100 && (
                        <span className="absolute top-0 w-full text-center -mt-6">
                          <span className="bg-orange-100 text-[#FF7A35] text-xs font-medium px-2 py-0.5 rounded shadow-sm">
                            Objectif atteint
                          </span>
                        </span>
                      )}
                      <div 
                        className={`w-full bg-[#FF7A35] rounded-t-sm ${item.taux === 100 ? 'shadow-lg border border-[#FF7A35]' : ''}`}
                        style={{ height: item.taux === 100 ? '100%' : `${item.taux}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Zone de textes sous les barres - maintenant séparée */}
              <div className="grid grid-cols-6 gap-2 absolute bottom-0 left-0 right-0 h-20">
                {stats.tauxCompletion.evolution.map((item, index) => (
                  <div key={index} className="text-center">
                    <div className="text-xs font-medium text-gray-600">{item.mois}</div>
                    <div className={`text-xs ${item.taux === 100 ? 'font-bold text-[#FF7A35]' : 'text-gray-500'}`}>
                      {item.taux}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              {isPremium 
                ? "Aucune donnée d'évolution disponible"
                : "Les données d'évolution sont disponibles avec l'abonnement Premium"}
            </div>
          )}
        </div>

        {/* Avis récents */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-semibold text-gray-800">Avis récents</h2>
          </div>
          {user?.id ? (
            <div className="p-4">
              <ReviewModalListeDesAvis 
                userId={user.id}
                limit={5}
                showHeader={false}
                maxCharacters={150}
                whiteBackground={true}
                onStatsUpdate={handleStatsUpdate}
              />
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              Connectez-vous pour voir vos avis récents
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QualityPage;