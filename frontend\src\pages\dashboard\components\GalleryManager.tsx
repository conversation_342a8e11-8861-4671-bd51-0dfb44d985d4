import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Card,
  Typography,
  Grid,
  CircularProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PhotoCamera as PhotoCameraIcon,
} from '@mui/icons-material';
import { fetchCsrfToken } from '../../../services/csrf';
import { logger } from '../../../utils/logger';
interface GalleryItem {
  id: string;
  description: string;
  category: string;
  beforeImages: { path: string }[];
  afterImages: { path: string }[];
  service: {
    id: string;
    name: string;
    category: string;
  };
}

interface GalleryManagerProps {
  providerId: string;
}

export function GalleryManager({ providerId }: GalleryManagerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [gallery, setGallery] = useState<GalleryItem[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editItem, setEditItem] = useState<GalleryItem | null>(null);
  const [formData, setFormData] = useState({
    description: '',
    category: '',
    beforeImages: [] as File[],
    afterImages: [] as File[],
  });

  const fetchGallery = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get(`/api/gallery/${providerId}`);
      setGallery(data);
    } catch (err) {
      setError('Erreur lors du chargement de la galerie');
      logger.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGallery();
  }, [providerId]);

  const handleAddItem = () => {
    setEditItem(null);
    setFormData({
      description: '',
      category: '',
      beforeImages: [],
      afterImages: [],
    });
    setOpenDialog(true);
  };

  const handleEditItem = (item: GalleryItem) => {
    setEditItem(item);
    setFormData({
      description: item.description,
      category: item.category,
      beforeImages: [],
      afterImages: [],
    });
    setOpenDialog(true);
  };

  const handleDeleteItem = async (id: string) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      return;
    }

    try {
      await axios.delete(`/api/gallery/${id}`);
      await fetchGallery();
    } catch (err) {
      setError('Erreur lors de la suppression');
      logger.info(err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formDataToSend = new FormData();

    formDataToSend.append('description', formData.description);
    formDataToSend.append('category', formData.category);

    formData.beforeImages.forEach((file) => {
      formDataToSend.append('beforeImages', file);
    });

    formData.afterImages.forEach((file) => {
      formDataToSend.append('afterImages', file);
    });

    try {
      if (editItem) {
        await axios.put(`/api/gallery/${editItem.id}`, formDataToSend);
      } else {
        await fetchCsrfToken();
        const headers = { 'X-CSRF-Token': await fetchCsrfToken() };
        await axios.post('/api/gallery', formDataToSend, { headers });
      }
      setOpenDialog(false);
      await fetchGallery();
    } catch (err) {
      setError('Erreur lors de la sauvegarde');
      logger.info(err);
    }
  };

  const handleImageChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: 'before' | 'after'
  ) => {
    const files = event.target.files;
    if (files) {
      setFormData((prev) => ({
        ...prev,
        [type === 'before' ? 'beforeImages' : 'afterImages']: Array.from(files),
      }));
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Galerie des Réalisations</Typography>
        <Button
          startIcon={<AddIcon />}
          variant="contained"
          onClick={handleAddItem}
        >
          Ajouter une réalisation
        </Button>
      </Box>

      {error && (
        <Typography color="error" mb={2}>
          {error}
        </Typography>
      )}

      <Grid container spacing={3}>
        {Array.isArray(gallery) && gallery.map((item) => (
          <Grid size={{ xs: 12, sm: 6, md: 4 }} key={item.id}>
            <Card sx={{ p: 2 }}>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="h6">{item.service.name}</Typography>
                <Box>
                  <IconButton
                    size="small"
                    onClick={() => handleEditItem(item)}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteItem(item.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Box>

              <Grid container spacing={1}>
                <Grid size={6}>
                  <Typography variant="subtitle2">Avant</Typography>
                  {item.beforeImages[0] && (
                    <img
                      src={item.beforeImages[0].path}
                      alt="Avant"
                      style={{ width: '100%', height: 150, objectFit: 'cover' }}
                    />
                  )}
                </Grid>
                <Grid size={6}>
                  <Typography variant="subtitle2">Après</Typography>
                  {item.afterImages[0] && (
                    <img
                      src={item.afterImages[0].path}
                      alt="Après"
                      style={{ width: '100%', height: 150, objectFit: 'cover' }}
                    />
                  )}
                </Grid>
              </Grid>

              <Typography variant="body2" color="text.secondary" mt={1}>
                {item.description}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editItem ? 'Modifier la réalisation' : 'Ajouter une réalisation'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={4}
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, description: e.target.value }))
              }
              sx={{ mb: 2 }}
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Catégorie</InputLabel>
              <Select
                value={formData.category}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, category: e.target.value }))
                }
              >
                <MenuItem value="jardinage">Jardinage</MenuItem>
                <MenuItem value="bricolage">Bricolage</MenuItem>
                <MenuItem value="menage">Ménage</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2}>
              <Grid size={6}>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<PhotoCameraIcon />}
                  fullWidth
                >
                  Photos Avant
                  <input
                    type="file"
                    hidden
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageChange(e, 'before')}
                  />
                </Button>
              </Grid>
              <Grid size={6}>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<PhotoCameraIcon />}
                  fullWidth
                >
                  Photos Après
                  <input
                    type="file"
                    hidden
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageChange(e, 'after')}
                  />
                </Button>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editItem ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
