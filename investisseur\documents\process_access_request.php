<?php
// Désactiver l'affichage des erreurs pour éviter la pollution HTML
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Buffer de sortie pour capturer toute sortie non désirée
ob_start();

session_start();
header('Content-Type: application/json');

// Fonction pour nettoyer la sortie et retourner du JSON propre
function cleanJsonOutput($data) {
    // Nettoyer le buffer de sortie
    ob_clean();
    echo json_encode($data);
    exit;
}

// Configuration email
define('ADMIN_EMAIL', '<EMAIL>');
define('FROM_EMAIL', '<EMAIL>');

// Vérification de la méthode POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    cleanJsonOutput(['success' => false, 'message' => 'Méthode non autorisée']);
}

// Récupération et validation des données
$requiredFields = ['fullName', 'email', 'company', 'position', 'investmentType', 'investmentRange', 'message', 'gdpr'];
$data = [];

foreach ($requiredFields as $field) {
    if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
        if ($field === 'gdpr') {
            cleanJsonOutput(['success' => false, 'message' => 'Vous devez accepter l\'utilisation de vos données.']);
        }
        cleanJsonOutput(['success' => false, 'message' => 'Tous les champs obligatoires doivent être remplis.']);
    }
    $data[$field] = trim($_POST[$field]);
}

// Champs optionnels
$data['phone'] = isset($_POST['phone']) ? trim($_POST['phone']) : '';

// Validation email
if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
    cleanJsonOutput(['success' => false, 'message' => 'Adresse email invalide.']);
}

// Protection contre le spam (simple vérification)
if (strlen($data['message']) < 20) {
    cleanJsonOutput(['success' => false, 'message' => 'Le message doit contenir au moins 20 caractères.']);
}

// Préparation de l'email
$subject = '[JobPartiel] Nouvelle demande d\'accès investisseur - ' . $data['fullName'];

$emailBody = "
" .
"═══════════════════════════════════════════════════════════════\n" .
"INFORMATIONS PERSONNELLES\n" .
"═══════════════════════════════════════════════════════════════\n" .
"Nom complet: {$data['fullName']}\n" .
"Email: {$data['email']}\n" .
"Téléphone: " . ($data['phone'] ?: 'Non renseigné') . "\n" .
"Société: {$data['company']}\n" .
"Fonction: {$data['position']}\n\n" .

"═══════════════════════════════════════════════════════════════\n" .
"PROFIL D'INVESTISSEMENT\n" .
"═══════════════════════════════════════════════════════════════\n" .
"Type d'investissement: {$data['investmentType']}\n" .
"Montant typique: {$data['investmentRange']}\n\n" .

"═══════════════════════════════════════════════════════════════\n" .
"MESSAGE\n" .
"═══════════════════════════════════════════════════════════════\n" .
"{$data['message']}\n\n" .

"═══════════════════════════════════════════════════════════════\n" .
"INFORMATIONS TECHNIQUES\n" .
"═══════════════════════════════════════════════════════════════\n" .
"Date de la demande: " . date('d/m/Y H:i:s') . "\n" .
"Adresse IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Inconnue') . "\n" .
"User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Inconnu') . "\n\n" .

"Pour traiter cette demande:\n" .
"1. Vérifiez les informations du demandeur\n" .
"2. Créez les identifiants d'accès si validé\n" .
"3. Envoyez les identifiants par email sécurisé\n";

// Headers pour l'email
$headers = [
    'From: ' . FROM_EMAIL,
    'Reply-To: ' . $data['email'],
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

// Préparation des données complètes pour sauvegarde
$requestData = [
    'id' => uniqid('req_', true),
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Inconnue',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Inconnu',
    'data' => $data,
    'status' => 'pending'
];

// Sauvegarde en JSON (système principal)
try {
    $jsonFile = 'access_requests.json';
    $requests = [];
    
    // Lecture des demandes existantes
    if (file_exists($jsonFile)) {
        $existingData = file_get_contents($jsonFile);
        $requests = json_decode($existingData, true) ?: [];
    }
    
    // Ajout de la nouvelle demande
    $requests[] = $requestData;
    
    // Sauvegarde
    $saveResult = file_put_contents($jsonFile, json_encode($requests, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    if ($saveResult === false) {
        throw new Exception('Impossible de sauvegarder dans le fichier JSON');
    }
    
    // Tentative d'envoi email
    $emailSent = false;
    $emailError = null;
    
    try {
        $emailSent = mail(ADMIN_EMAIL, $subject, $emailBody, implode("\r\n", $headers));
        
        if ($emailSent) {
            // Log de succès email
            $logEntry = date('Y-m-d H:i:s') . " - Email envoyé pour {$data['fullName']} ({$data['email']})\n";
            file_put_contents('access_requests.log', $logEntry, FILE_APPEND | LOCK_EX);
        }
        
    } catch (Exception $e) {
        $emailError = $e->getMessage();
        error_log('Erreur envoi email: ' . $emailError);
    }
    
    // Réponse de succès avec info email
    $message = 'Votre demande a été enregistrée avec succès.';
    if ($emailSent) {
        $message .= ' Un email de notification a été envoyé à notre équipe.';
    } else {
        $message .= ' Notre équipe sera notifiée et vous recevrez une réponse sous 24-48h ouvrées.';
    }
    
    cleanJsonOutput([
        'success' => true, 
        'message' => $message
    ]);
    
} catch (Exception $e) {
    error_log('Erreur critique: ' . $e->getMessage());
    http_response_code(500);
    cleanJsonOutput([
        'success' => false, 
        'message' => 'Erreur technique lors de la sauvegarde'
    ]);
}
?>