import { useState } from 'react';
import DOMPurify from 'dompurify';
import ModalPortal from '../ModalPortal';
import { Box, Typography, FormControl, InputLabel, Select, MenuItem, Button } from '@mui/material';

export const ReportProfileModal = ({ open, onClose, onSubmit, loading, photos = [] }: { open: boolean; onClose: () => void; onSubmit: (reason: string) => void; loading: boolean; photos?: { id: string; url: string; label: string }[] }) => {
  const [selectedReason, setSelectedReason] = useState(''); // Pour le Select
  const [reason, setReason] = useState(''); // Pour le textarea
  const [selectedPhotoId, setSelectedPhotoId] = useState<string>('');
  const handleSubmit = () => {
    let motif = reason;
    if (selectedReason === 'Photo de profil ou galerie inappropriée' && selectedPhotoId) {
      const photo = photos.find(p => p.id === selectedPhotoId);
      motif = `[Signalement photo: ${photo?.label || photo?.url || selectedPhotoId}] ${reason}`;
    }
    const sanitizedReason = DOMPurify.sanitize(motif);
    onSubmit(sanitizedReason);
  };
  // Juste avant la déclaration de PREDEFINED_REASONS :
  const hasCompanyInfo = photos && Array.isArray(photos) && photos.length > 0 && photos.some(photo => photo.label?.toLowerCase().includes('entreprise'));

  const PREDEFINED_REASONS: Array<{ label: string; isGroup: true; value?: undefined } | { value: string; label: string; isGroup?: false }> = [
    { label: 'Raisons générales', isGroup: true },
    { value: 'Faux profil', label: 'Faux profil' },
    { value: "Usurpation d'identité", label: "Usurpation d'identité" },
    { value: 'Comportement inapproprié', label: 'Comportement inapproprié' },
    { value: 'Spam ou publicité', label: 'Spam ou publicité' },
    { value: 'Propos racistes ou discriminatoires', label: 'Propos racistes ou discriminatoires' },
    { value: 'Harcèlement', label: 'Harcèlement' },
    { label: 'Cibler un élément du profil', isGroup: true },
    { value: 'Photo de profil ou galerie inappropriée', label: 'Photo de profil ou galerie inappropriée' },
    { value: 'Bio inappropriée', label: 'Bio inappropriée' },
    { value: 'Informations personnelles', label: 'Informations personnelles' },
    // Groupe Entreprise (affiché seulement si infos entreprise)
    ...(
      hasCompanyInfo
        ? ([
            { label: 'Entreprise', isGroup: true } as const,
            { value: "Informations d'entreprise incorrectes ou frauduleuses", label: "Informations d'entreprise incorrectes ou frauduleuses" }
          ])
        : []
    ),
    { label: 'Autre', isGroup: true },
    { value: 'Autre', label: 'Autre' },
  ];
  // Map des raisons prédéfinies vers des textes explicatifs
  const REASON_EXPLANATIONS: Record<string, string> = {
    'Faux profil': "Ce profil semble être un faux profil. Merci de vérifier son authenticité.",
    "Usurpation d'identité": "Ce profil semble utiliser l'identité de quelqu'un d'autre sans autorisation.",
    'Comportement inapproprié': "Ce profil a eu un comportement inapproprié ou irrespectueux.",
    'Spam ou publicité': "Ce profil envoie des messages non sollicités ou fait de la publicité.",
    'Propos racistes ou discriminatoires': "Ce profil tient des propos racistes, discriminatoires ou haineux.",
    'Harcèlement': "Ce profil harcèle ou menace d'autres utilisateurs.",
    'Informations personnelles': "Ce profil partage des informations personnelles sensibles.",
    'Photo de profil ou galerie inappropriée': "La photo ou la galerie postée est inappropriée.",
    'Autre': "Merci de préciser la raison du signalement.",
    'Bio inappropriée': "La description (bio) du profil contient des propos inappropriés, offensants ou non conformes aux règles du site.",
    "Informations d'entreprise incorrectes ou frauduleuses": "Les informations d'entreprise (SIREN, nom, statut, etc.) semblent fausses, usurpées ou frauduleuses.",
  };
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <Box sx={{ background: 'white', borderRadius: 2, p: 0, maxWidth: 400, mx: 'auto', boxShadow: 3, overflowY: 'auto', maxHeight: '80vh' }}>
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2 }}>Signaler ce profil</Typography>
          <Typography variant="body2" sx={{ mb: 2, color: '#FF6B2C', fontWeight: 500 }}>
            Merci de décrire précisément le problème rencontré avec ce profil.<br />
            <span style={{ color: '#BDBDBD' }}>
              Plus votre description est détaillée, plus la modération pourra agir efficacement.
            </span>
          </Typography>
          <FormControl fullWidth size="small" sx={{ mb: 1 }}>
            <InputLabel>Raison prédéfinie</InputLabel>
            <Select
              value={selectedReason || ''}
              label="Raison prédéfinie"
              onChange={e => {
                const value = String(e.target.value);
                setSelectedReason(value);
                if (value === '') {
                  setReason('');
                } else {
                  setReason(REASON_EXPLANATIONS[value] || value);
                }
                if (value !== 'Photo de profil ou galerie inappropriée') {
                  setSelectedPhotoId('');
                }
              }}
            >
              <MenuItem value="" disabled>Choisissez une raison</MenuItem>
              {PREDEFINED_REASONS.map((item, idx) =>
                item.isGroup ? (
                  <MenuItem key={item.label + idx} value="" disabled style={{ fontWeight: 'bold', opacity: 0.7 }}>
                    {item.label}
                  </MenuItem>
                ) : (
                  <MenuItem key={item.value!} value={item.value!}>{item.label}</MenuItem>
                )
              )}
            </Select>
          </FormControl>
          {/* Affichage de la sélection de photo SEULEMENT si la raison correspond */}
          {selectedReason === 'Photo de profil ou galerie inappropriée' && photos.length > 0 && (
            <FormControl fullWidth size="small" sx={{ mb: 1 }}>
              <InputLabel>Photo concernée</InputLabel>
              <Select
                value={selectedPhotoId}
                label="Photo concernée"
                onChange={e => setSelectedPhotoId(e.target.value)}
              >
                <MenuItem value="">Aucune (signalement général du profil)</MenuItem>
                {photos.map(photo => (
                  <MenuItem key={photo.id} value={photo.id}>
                    <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <img src={photo.url} alt={photo.label} style={{ width: 32, height: 32, objectFit: 'cover', borderRadius: 4, marginRight: 8 }} />
                      {photo.label}
                    </span>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
          <textarea
            value={reason}
            onChange={e => {
              setReason(e.target.value.slice(0, 750));
              if (!PREDEFINED_REASONS.some(item => 'value' in item && item.value === e.target.value)) {
                setSelectedReason('');
              }
            }}
            rows={7}
            maxLength={750}
            style={{ width: '100%', borderRadius: 8, border: '1px solid #FFE4BA', padding: 8, marginBottom: 8 }}
            placeholder="Décrivez le problème rencontré... (750 caractères max)"
          />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="caption" sx={{ color: reason.length >= 750 ? '#FF6B2C' : '#888' }}>
              {reason.length}/750 caractères
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button onClick={onClose} sx={{ color: '#666' }}>Annuler</Button>
            <Button
              onClick={handleSubmit}
              sx={{ background: '#FF6B2C', color: 'white', '&:hover': { background: '#FF7A35' } }}
              disabled={!reason.trim() || loading}
            >
              Signaler
            </Button>
          </Box>
        </Box>
      </Box>
    </ModalPortal>
  );
};

export default ReportProfileModal; 