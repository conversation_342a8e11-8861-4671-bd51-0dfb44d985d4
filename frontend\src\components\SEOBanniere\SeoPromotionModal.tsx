import React, { useState } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';
import { notify } from '../Notification';
import logger from '../../utils/logger';
import ModalPortal from '../ModalPortal';

interface SeoPromotionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  trigger: 'onboarding' | 'profile_complete' | 'first_mission' | 'notification' | 'popup';
  refreshStats?: () => Promise<any>;
}

const SeoPromotionModal: React.FC<SeoPromotionModalProps> = ({
  isOpen,
  onClose,
  onAccept,
  trigger,
  refreshStats
}) => {
  const [loading, setLoading] = useState(false);

  const handleAccept = async () => {
    if (loading) return;

    try {
      setLoading(true);

      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await axios.put('/api/users/update-seo-preferences', {
        seo_indexable: true
      }, {
        ...API_CONFIG,
        headers: {
          ...API_CONFIG.headers,
          ...headers,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        notify('🎉 Parfait ! Votre profil sera maintenant référencé sur Google', 'success');

        // Rafraîchir les stats si la fonction est fournie
        if (refreshStats) {
          await refreshStats();
        }

        onAccept();
        onClose();
      } else {
        notify('Erreur lors de l\'activation du référencement', 'error');
      }
    } catch (error: any) {
      logger.error('Erreur lors de l\'activation du référencement SEO:', error);
      notify(error.response?.data?.message || 'Erreur lors de l\'activation du référencement', 'error');
    } finally {
      setLoading(false);
    }
  };

  const getTriggerContent = () => {
    switch (trigger) {
      case 'onboarding':
        return {
          title: '🚀 Boostez votre visibilité dès maintenant !',
          subtitle: 'Félicitations pour votre inscription !',
          description: 'Permettez à Google de référencer votre profil pour attirer plus de clients.'
        };
      case 'profile_complete':
        return {
          title: '✨ Votre profil est maintenant complet !',
          subtitle: 'Dernière étape pour maximiser vos opportunités',
          description: 'Activez le référencement Google pour que les clients vous trouvent facilement.'
        };
      case 'first_mission':
        return {
          title: '🎯 Prêt pour plus d\'offres ?',
          subtitle: 'Vous avez envoyé votre première offre !',
          description: 'Augmentez vos chances d\'obtenir plus de missions en activant le référencement Google.'
        };
      case 'notification':
        return {
          title: '📈 Développez votre activité',
          subtitle: 'Conseil personnalisé pour vous',
          description: 'Les jobbeurs avec un profil référencé reçoivent 3x plus de demandes.'
        };
      default:
        return {
          title: '🌟 Maximisez votre visibilité',
          subtitle: 'Opportunité d\'améliorer votre profil',
          description: 'Permettez à Google de référencer votre profil pour attirer plus de clients.'
        };
    }
  };

  const content = getTriggerContent();

  return (
    <ModalPortal
      isOpen={isOpen}
      onBackdropClick={onClose}
      closeOnBackdropClick={true}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="bg-white rounded-2xl shadow-2xl max-w-md w-[95%] max-h-[85vh] overflow-y-auto p-6"
        onClick={(e) => e.stopPropagation()}
      >
          {/* Header */}
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              {content.title}
            </h2>
            <p className="text-[#FF6B2C] font-semibold">
              {content.subtitle}
            </p>
          </div>

          {/* Content */}
          <div className="mb-6">
            <p className="text-gray-600 text-center mb-4">
              {content.description}
            </p>

            {/* Avantages */}
            <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mb-4">
              <h3 className="font-semibold text-[#FF6B2C] mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="mr-2" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M9 11l3 3l8-8"></path>
                  <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9s4.03-9 9-9c1.51 0 2.93.37 4.18 1.03"></path>
                </svg>
                Avantages du référencement :
              </h3>
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-start">
                  <span className="text-[#FF6B2C] mr-2">•</span>
                  <span>Votre profil apparaît dans les recherches Google</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#FF6B2C] mr-2">•</span>
                  <span>3x plus de demandes de missions</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#FF6B2C] mr-2">•</span>
                  <span>Nouveaux clients vous trouvent facilement</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#FF6B2C] mr-2">•</span>
                  <span>Amélioration de votre présence en ligne</span>
                </li>
              </ul>
            </div>

            {/* RGPD Info */}
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-700">
                <span className="font-semibold">🔒 Respect de votre vie privée :</span> Seules les informations publiques de votre profil seront référencées. Vous pouvez désactiver cette option à tout moment dans vos paramètres.
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Plus tard
            </button>
            <button
              onClick={handleAccept}
              disabled={loading}
              className={`flex-1 px-4 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors font-medium ${
                loading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {loading ? 'Activation...' : 'Activer maintenant'}
            </button>
          </div>

          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </motion.div>
    </ModalPortal>
  );
};

export default SeoPromotionModal;
