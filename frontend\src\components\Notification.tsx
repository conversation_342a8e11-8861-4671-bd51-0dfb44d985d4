import { useCallback } from 'react';
import { Toaster } from 'react-hot-toast';
import { NotificationService } from '../notification';

// Wrapper pour compatibilité
export const notify = NotificationService.notify;
export const success = (message: string) => NotificationService.notify(message, 'success');
export const error = (message: string) => NotificationService.notify(message, 'error');
export const warning = (message: string) => NotificationService.notify(message, 'warning');
export const info = (message: string) => NotificationService.notify(message, 'info');

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export function useNotification() {
  const notify = useCallback((message: string, type: NotificationType = 'info') => {
    NotificationService.notify(message, type);
  }, []);

  const notifySuccess = useCallback((message: string) => {
    NotificationService.notify(message, 'success');
  }, []);

  const notifyError = useCallback((message: string) => {
    NotificationService.notify(message, 'error');
  }, []);

  const notifyWarning = useCallback((message: string) => {
    NotificationService.notify(message, 'warning');
  }, []);

  const notifyInfo = useCallback((message: string) => {
    NotificationService.notify(message, 'info');
  }, []);

  return {
    notify,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo
  };
}

// Conteneur de notifications
export function NotificationContainer() {
  return (
    <Toaster 
      position="top-right"
      toastOptions={{
        style: {
          zIndex: 1000 // Z-index élevé pour les notifications
        },
      }}
    />
  );
}

export { NotificationService };
