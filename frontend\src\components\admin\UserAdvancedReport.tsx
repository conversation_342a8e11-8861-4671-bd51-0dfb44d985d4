import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Container,
  Paper,
  useTheme,
  Tabs,
  Tab
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Download,
  RefreshCw,
  Activity,
  Wallet,
  Image,
  MessageSquare,
  Star,
  Brain,
  Clock,
  User,
  FileText,
  DollarSign,
  Award
} from 'lucide-react';
import { useUserManagement } from '../../hooks/useUserManagement';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  position: 'absolute',
  top: '-15px',
  right: '20px',
  color: color,
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: `${COLORS.primary}10`,
      borderColor: COLORS.secondary,
    },
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '0.9rem',
  minHeight: '48px',
  padding: '12px 16px',
  color: '#64748B',
  '&.Mui-selected': {
    color: COLORS.primary,
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${COLORS.borderColor}`,
  '& .MuiTabs-indicator': {
    backgroundColor: COLORS.primary,
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
}));

const StatValue = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(0.5),
}));

const StatLabel = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: '#64748B',
  fontWeight: 500,
}));

interface UserAdvancedReportProps {
  userId: string;
}

const UserAdvancedReport: React.FC<UserAdvancedReportProps> = ({ userId }) => {
  const { generateUserReport } = useUserManagement();
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reportType, setReportType] = useState('complete');
  const [timeRange, setTimeRange] = useState('90');
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();

  const fetchReport = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await generateUserReport(userId, reportType, timeRange);

      if (result.success) {
        setReportData(result.data);
      } else {
        setError(result.message || 'Erreur lors de la génération du rapport');
      }
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      setError('Erreur lors de la génération du rapport');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReport();
  }, [userId, reportType, timeRange]);

  const exportReport = () => {
    if (!reportData) return;

    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `rapport_utilisateur_${userId}_${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('fr-FR').format(num);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Fonction pour obtenir la dernière date de connexion
  const getLastLoginDate = () => {
    if (reportData?.user?.last_login) {
      return format(new Date(reportData.user.last_login), 'dd/MM/yyyy à HH:mm', { locale: fr });
    }
    return 'N/A';
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress sx={{ color: COLORS.primary }} />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <PageTitle variant="h4">
            Rapport Avancé Utilisateur
          </PageTitle>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Analyse détaillée de l'activité et des performances de l'utilisateur
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <StyledFormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Période</InputLabel>
            <Select
              value={timeRange}
              label="Période"
              onChange={(e) => setTimeRange(e.target.value)}
              sx={{ borderRadius: '8px' }}
            >
              <MenuItem value="7">7 jours</MenuItem>
              <MenuItem value="30">30 jours</MenuItem>
              <MenuItem value="90">90 jours</MenuItem>
              <MenuItem value="365">1 an</MenuItem>
            </Select>
          </StyledFormControl>
          <StyledFormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={reportType}
              label="Type"
              onChange={(e) => setReportType(e.target.value)}
              sx={{ borderRadius: '8px' }}
            >
              <MenuItem value="complete">Complet</MenuItem>
              <MenuItem value="activity">Activité</MenuItem>
              <MenuItem value="financial">Financier</MenuItem>
              <MenuItem value="moderation">Modération</MenuItem>
            </Select>
          </StyledFormControl>
          <StyledButton
            variant="outlined"
            startIcon={<RefreshCw size={18} />}
            onClick={fetchReport}
            disabled={loading}
          >
            Actualiser
          </StyledButton>
          <StyledButton
            variant="contained"
            startIcon={<Download size={18} />}
            onClick={exportReport}
            disabled={!reportData}
          >
            Exporter
          </StyledButton>
        </Box>
      </Box>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 3, 
            borderRadius: '12px',
            border: `1px solid ${COLORS.error}20`
          }}
        >
          {error}
        </Alert>
      )}

      {reportData && (
        <>
          <StyledTabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="rapport utilisateur tabs"
            sx={{ mb: 3 }}
          >
            <StyledTab icon={<Activity size={16} />} iconPosition="start" label="Activité" />
            <StyledTab icon={<Wallet size={16} />} iconPosition="start" label="Finances" />
            <StyledTab icon={<Image size={16} />} iconPosition="start" label="Contenu" />
            <StyledTab icon={<Brain size={16} />} iconPosition="start" label="IA" />
          </StyledTabs>

          <motion.div
            variants={container}
            initial="hidden"
            animate="show"
          >
            {/* Onglet Activité */}
            {tabValue === 0 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <Clock size={24} />
                      </IconBox>
                      <SectionTitle>Connexions</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.login_count || 0)}
                      </StatValue>
                      <StatLabel>
                        Dernière connexion: {getLastLoginDate()}
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <FileText size={24} />
                      </IconBox>
                      <SectionTitle>Missions</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.missions_created || 0)}
                      </StatValue>
                      <StatLabel>
                        Créées sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <User size={24} />
                      </IconBox>
                      <SectionTitle>Candidatures</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.candidatures_sent || 0)}
                      </StatValue>
                      <StatLabel>
                        Envoyées sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <MessageSquare size={24} />
                      </IconBox>
                      <SectionTitle>Messages</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.messages_sent || 0)}
                      </StatValue>
                      <StatLabel>
                        Envoyés sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.success}>
                        <Star size={24} />
                      </IconBox>
                      <SectionTitle>Avis donnés</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.reviews_given || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.info}>
                        <Image size={24} />
                      </IconBox>
                      <SectionTitle>Photos uploadées</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.activity?.photos_uploaded || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>
              </Grid>
            )}

            {/* Onglet Finances */}
            {tabValue === 1 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.success}>
                        <DollarSign size={24} />
                      </IconBox>
                      <SectionTitle>Jobi gagnés</SectionTitle>
                      <StatValue sx={{ color: COLORS.success }}>
                        {formatCurrency(reportData.financial?.jobi_earned || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.error}>
                        <DollarSign size={24} />
                      </IconBox>
                      <SectionTitle>Jobi dépensés</SectionTitle>
                      <StatValue sx={{ color: COLORS.error }}>
                        {formatCurrency(reportData.financial?.jobi_spent || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <Activity size={24} />
                      </IconBox>
                      <SectionTitle>Transactions</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.financial?.transactions_count || 0)}
                      </StatValue>
                      <StatLabel>
                        Nombre total
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.info}>
                        <FileText size={24} />
                      </IconBox>
                      <SectionTitle>Factures</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.financial?.invoices_created || 0)}
                      </StatValue>
                      <StatLabel>
                        Montant: {formatCurrency(reportData.financial?.total_invoice_amount || 0)}
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.warning}>
                        <Award size={24} />
                      </IconBox>
                      <SectionTitle>Gains missions</SectionTitle>
                      <StatValue sx={{ color: COLORS.warning }}>
                        {formatCurrency(reportData.financial?.mission_earnings || 0)}
                      </StatValue>
                      <StatLabel>
                        Missions acceptées
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>
              </Grid>
            )}

            {/* Onglet Contenu */}
            {tabValue === 2 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <FileText size={24} />
                      </IconBox>
                      <SectionTitle>Services</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.content?.services_created || 0)}
                      </StatValue>
                      <StatLabel>
                        Créés sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <Image size={24} />
                      </IconBox>
                      <SectionTitle>Galeries</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.content?.galleries_created || 0)}
                      </StatValue>
                      <StatLabel>
                        Photos mises en avant: {formatNumber(reportData.content?.featured_photos_added || 0)}
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.warning}>
                        <Star size={24} />
                      </IconBox>
                      <SectionTitle>Avis reçus</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.content?.reviews_received || 0)}
                      </StatValue>
                      <StatLabel>
                        Note moyenne: {reportData.content?.average_rating || 0}/5
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.info}>
                        <Award size={24} />
                      </IconBox>
                      <SectionTitle>Badges</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.content?.badges_earned || 0)}
                      </StatValue>
                      <StatLabel>
                        Obtenus sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>
              </Grid>
            )}

            {/* Onglet IA */}
            {tabValue === 3 && (
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <Brain size={24} />
                      </IconBox>
                      <SectionTitle>Crédits IA</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.user?.user_ai_credits?.[0]?.credits || 0)}
                      </StatValue>
                      <StatLabel>
                        Solde actuel
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.success}>
                        <DollarSign size={24} />
                      </IconBox>
                      <SectionTitle>Crédits reçus</SectionTitle>
                      <StatValue sx={{ color: COLORS.success }}>
                        {formatNumber(reportData.aiUsage?.credits_received || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox color={COLORS.error}>
                        <DollarSign size={24} />
                      </IconBox>
                      <SectionTitle>Crédits utilisés</SectionTitle>
                      <StatValue sx={{ color: COLORS.error }}>
                        {formatNumber(reportData.aiUsage?.credits_used || 0)}
                      </StatValue>
                      <StatLabel>
                        Sur la période
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>

                <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                  <motion.div variants={item} style={{ height: '100%' }}>
                    <StyledPaper sx={{ position: 'relative', pt: 4 }}>
                      <IconBox>
                        <Activity size={24} />
                      </IconBox>
                      <SectionTitle>Générations</SectionTitle>
                      <StatValue>
                        {formatNumber(reportData.aiUsage?.generations_count || 0)}
                      </StatValue>
                      <StatLabel>
                        Tokens: {formatNumber(reportData.aiUsage?.tokens_used || 0)}
                      </StatLabel>
                    </StyledPaper>
                  </motion.div>
                </Grid>
              </Grid>
            )}
          </motion.div>
        </>
      )}
    </Container>
  );
};

export default UserAdvancedReport;
