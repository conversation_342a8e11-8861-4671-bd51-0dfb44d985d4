import React, { useEffect, useState } from 'react';
import {
  Box, Paper, Typography, Button, TextField, IconButton, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Dialog, DialogTitle, DialogContent, DialogActions
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ReplayIcon from '@mui/icons-material/Replay';
import Tooltip from '@mui/material/Tooltip';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Grid from '@mui/material/Grid';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import TableSortLabel from '@mui/material/TableSortLabel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

interface EmailQueueItem {
  id: number;
  to_email: string;
  subject: string;
  html: string;
  status?: string;
  created_at?: string;
}

const PAGE_SIZE = 100;

const EmailQueueAdmin: React.FC = () => {
  const [emails, setEmails] = useState<EmailQueueItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [page, setPage] = useState(0);
  const [total, setTotal] = useState(0);
  const [editEmail, setEditEmail] = useState<EmailQueueItem | null>(null);
  const [editLoading, setEditLoading] = useState(false);
  const [filterTo, setFilterTo] = useState('');
  const [filterSubject, setFilterSubject] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [pendingFilterTo, setPendingFilterTo] = useState('');
  const [pendingFilterSubject, setPendingFilterSubject] = useState('');
  const [pendingFilterStatus, setPendingFilterStatus] = useState('');
  const [sortBy, setSortBy] = useState<'to_email' | 'subject' | 'status' | 'created_at' | 'id'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewEmail, setViewEmail] = useState<EmailQueueItem | null>(null);
  const [viewTab, setViewTab] = useState<'text' | 'html'>('html');
  const [forceSendLoading, setForceSendLoading] = useState<number | null>(null);
  const [forceSendAllLoading, setForceSendAllLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState<number | null>(null);

  const fetchEmails = async (pageNum = page) => {
    setLoading(true);
    setError('');
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/admin/email-queue`, {
        headers,
        withCredentials: true,
        params: {
          limit: PAGE_SIZE,
          offset: pageNum * PAGE_SIZE,
          to: filterTo,
          subject: filterSubject,
          status: filterStatus,
          sort: sortBy,
          order: sortOrder
        }
      });
      setEmails(res.data.emails || []);
      setTotal(res.data.total || 0);
    } catch (e: any) {
      setError("Erreur lors de la récupération des emails");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmails(0);
    // eslint-disable-next-line
  }, [filterTo, filterSubject, filterStatus, sortBy, sortOrder]);

  useEffect(() => {
    fetchEmails(page);
    // eslint-disable-next-line
  }, [page]);

  const handleEdit = (email: EmailQueueItem) => {
    setEditEmail({ ...email });
  };

  const handleEditChange = (field: keyof EmailQueueItem, value: string) => {
    if (!editEmail) return;
    setEditEmail({ ...editEmail, [field]: value });
  };

  const handleEditSave = async () => {
    if (!editEmail) return;
    setEditLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      const csrfToken = (window as any).CSRF_TOKEN || document.cookie.split('; ').find(row => row.startsWith('csrfToken='))?.split('=')[1];
      if (csrfToken) headers['X-CSRF-Token'] = csrfToken;
      
      // Envoyer seulement les champs autorisés par le backend
      const allowedFields = {
        to_email: editEmail.to_email,
        subject: editEmail.subject,
        html: editEmail.html,
        // Si l'email était déjà envoyé et qu'on le modifie, on le remet automatiquement en pending
        status: editEmail.status === 'sent' ? 'pending' : editEmail.status
      };
      
      await axios.put(`${API_CONFIG.baseURL}/api/admin/email-queue/${editEmail.id}`, allowedFields, {
        headers,
        withCredentials: true
      });
      
      setSuccess('Email modifié avec succès');
      setEditEmail(null);
      fetchEmails(page);
    } catch (e: any) {
      setError(e.response?.data?.message || "Erreur lors de la modification de l'email");
    } finally {
      setEditLoading(false);
    }
  };

  // Fonction pour forcer l'envoi d'un email spécifique
  const handleForceSend = async (emailId: number) => {
    setForceSendLoading(emailId);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/admin/email-queue/force-send/${emailId}`, {}, {
        headers,
        withCredentials: true
      });
      setSuccess(response.data.message || 'Email envoyé avec succès');
      fetchEmails(page);
    } catch (e: any) {
      setError(e.response?.data?.message || "Erreur lors de l'envoi forcé de l'email");
    } finally {
      setForceSendLoading(null);
    }
  };

  // Fonction pour forcer l'envoi de tous les emails en attente
  const handleForceSendAll = async () => {
    setForceSendAllLoading(true);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/admin/email-queue/force-send-all`, {}, {
        headers,
        withCredentials: true
      });
      setSuccess(response.data.message || 'Traitement terminé');
      fetchEmails(page);
    } catch (e: any) {
      setError(e.response?.data?.message || "Erreur lors de l'envoi forcé des emails");
    } finally {
      setForceSendAllLoading(false);
    }
  };

  // Fonction pour renvoyer un email déjà envoyé
  const handleResend = async (emailId: number) => {
    setResendLoading(emailId);
    setError('');
    setSuccess('');
    try {
      const headers = await getCommonHeaders();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/admin/email-queue/resend/${emailId}`, {}, {
        headers,
        withCredentials: true
      });
      setSuccess(response.data.message || 'Email renvoyé avec succès');
      fetchEmails(page);
    } catch (e: any) {
      setError(e.response?.data?.message || "Erreur lors du renvoi de l'email");
    } finally {
      setResendLoading(null);
    }
  };

  // Fonction utilitaire simple pour convertir le HTML en texte brut
  function stripHtml(html: string) {
    const tmp = document.createElement('DIV');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  }

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5" sx={{ color: '#FF6B2C' }}>Gestion des emails en attente</Typography>
          <Button
            variant="contained"
            startIcon={forceSendAllLoading ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
            onClick={handleForceSendAll}
            disabled={forceSendAllLoading}
            sx={{
              bgcolor: '#2ECC40',
              color: 'white',
              fontWeight: 600,
              '&:hover': { bgcolor: '#27AE3C' },
              '&:disabled': { bgcolor: '#ccc' }
            }}
          >
            {forceSendAllLoading ? 'Envoi en cours...' : 'Forcer l\'envoi de tous les emails en attente'}
          </Button>
        </Box>
        {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
        {success && <Alert severity="success" sx={{ mt: 2 }}>{success}</Alert>}
        <Paper elevation={2} sx={{ p: 2, mb: 2, background: '#FFF7F3', borderRadius: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 3 }}>
              <TextField
                fullWidth
                size="small"
                label="Destinataire"
                value={pendingFilterTo}
                onChange={e => setPendingFilterTo(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  )
                }}
                onKeyDown={e => { if (e.key === 'Enter') { setFilterTo(pendingFilterTo); setPage(0); } }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 3 }}>
              <TextField
                fullWidth
                size="small"
                label="Sujet"
                value={pendingFilterSubject}
                onChange={e => setPendingFilterSubject(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  )
                }}
                onKeyDown={e => { if (e.key === 'Enter') { setFilterSubject(pendingFilterSubject); setPage(0); } }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Statut</InputLabel>
                <Select
                  value={pendingFilterStatus}
                  label="Statut"
                  onChange={e => setPendingFilterStatus(e.target.value)}
                  onKeyDown={e => { if (e.key === 'Enter') { setFilterStatus(pendingFilterStatus); setPage(0); } }}
                  sx={{ background: pendingFilterStatus ? (pendingFilterStatus === 'sent' ? '#E6F4EA' : pendingFilterStatus === 'pending' ? '#FFF9E6' : '#FDEAEA') : undefined }}
                >
                  <MenuItem value=""><em>Tous</em></MenuItem>
                  <MenuItem value="pending" sx={{ color: '#FF6B2C' }}>En attente</MenuItem>
                  <MenuItem value="sent" sx={{ color: '#2ECC40' }}>Envoyé</MenuItem>
                  <MenuItem value="error" sx={{ color: '#E53935' }}>Erreur</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 2 }}>
              <Button
                variant="contained"
                sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 600, mr: 1 }}
                fullWidth
                onClick={() => { setFilterTo(pendingFilterTo); setFilterSubject(pendingFilterSubject); setFilterStatus(pendingFilterStatus); setPage(0); }}
              >
                Rechercher
              </Button>
            </Grid>
            <Grid size={{ xs: 12, sm: 1 }}>
              {(filterTo || filterSubject || filterStatus) && (
                <Button variant="outlined" color="secondary" fullWidth onClick={() => {
                  setPendingFilterTo(''); setPendingFilterSubject(''); setPendingFilterStatus('');
                  setFilterTo(''); setFilterSubject(''); setFilterStatus(''); setPage(0);
                }}>
                  Réinitialiser
                </Button>
              )}
            </Grid>
          </Grid>
        </Paper>
        {loading ? <CircularProgress sx={{ mt: 3 }} /> : (
          <TableContainer sx={{ mt: 3 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>
                    <TableSortLabel
                      active={sortBy === 'id'}
                      direction={sortBy === 'id' ? sortOrder : 'asc'}
                      onClick={() => {
                        if (sortBy === 'id') setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        else { setSortBy('id'); setSortOrder('asc'); }
                      }}
                    >
                      ID
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortBy === 'to_email'}
                      direction={sortBy === 'to_email' ? sortOrder : 'asc'}
                      onClick={() => {
                        if (sortBy === 'to_email') setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        else { setSortBy('to_email'); setSortOrder('asc'); }
                      }}
                    >
                      Destinataire
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortBy === 'subject'}
                      direction={sortBy === 'subject' ? sortOrder : 'asc'}
                      onClick={() => {
                        if (sortBy === 'subject') setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        else { setSortBy('subject'); setSortOrder('asc'); }
                      }}
                    >
                      Sujet
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortBy === 'status'}
                      direction={sortBy === 'status' ? sortOrder : 'asc'}
                      onClick={() => {
                        if (sortBy === 'status') setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        else { setSortBy('status'); setSortOrder('asc'); }
                      }}
                    >
                      Status
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortBy === 'created_at'}
                      direction={sortBy === 'created_at' ? sortOrder : 'asc'}
                      onClick={() => {
                        if (sortBy === 'created_at') setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
                        else { setSortBy('created_at'); setSortOrder('asc'); }
                      }}
                    >
                      Date
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {emails.map((email, index) => (
                  <TableRow key={email.id || `email-${index}`}>
                    <TableCell>{email.id || ''}</TableCell>
                    <TableCell>{email.to_email || ''}</TableCell>
                    <TableCell>{email.subject || ''}</TableCell>
                    <TableCell>{email.status || ''}</TableCell>
                    <TableCell>{email.created_at ? new Date(email.created_at).toLocaleString() : ''}</TableCell>
                    <TableCell>
                      <Tooltip title="Voir le contenu de l'email">
                        <span>
                          <IconButton onClick={() => setViewEmail(email)} size="small" color="secondary">
                            <VisibilityIcon />
                          </IconButton>
                        </span>
                      </Tooltip>

                      {/* Bouton Modifier - disponible pour tous les emails sauf ceux en cours d'envoi */}
                      <Tooltip title={email.status === 'pending' || email.status === 'error' ? 'Modifier cet email' : 'Modifier cet email (sera remis en attente)'}>
                        <span>
                          <IconButton onClick={() => handleEdit(email)} size="small" color="primary">
                            <EditIcon />
                          </IconButton>
                        </span>
                      </Tooltip>

                      {/* Bouton Forcer l'envoi - uniquement pour les emails en attente */}
                      {email.status === 'pending' && (
                        <Tooltip title="Forcer l'envoi de cet email">
                          <span>
                            <IconButton
                              onClick={() => handleForceSend(email.id)}
                              size="small"
                              disabled={forceSendLoading === email.id}
                              sx={{ color: '#2ECC40' }}
                            >
                              {forceSendLoading === email.id ? <CircularProgress size={16} /> : <SendIcon />}
                            </IconButton>
                          </span>
                        </Tooltip>
                      )}

                      {/* Bouton Renvoyer - uniquement pour les emails déjà envoyés */}
                      {email.status === 'sent' && (
                        <Tooltip title="Renvoyer cet email">
                          <span>
                            <IconButton
                              onClick={() => handleResend(email.id)}
                              size="small"
                              disabled={resendLoading === email.id}
                              sx={{ color: '#FF6B2C' }}
                            >
                              {resendLoading === email.id ? <CircularProgress size={16} /> : <ReplayIcon />}
                            </IconButton>
                          </span>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <TablePagination
              component="div"
              count={total}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={PAGE_SIZE}
              rowsPerPageOptions={[PAGE_SIZE]}
            />
          </TableContainer>
        )}
      </Paper>
      <Dialog open={!!editEmail} onClose={() => setEditEmail(null)} maxWidth="md" fullWidth>
        <DialogTitle>Modifier l'email</DialogTitle>
        <DialogContent>
          {editEmail?.status === 'sent' && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <strong>Attention :</strong> Modifier un email déjà envoyé le remettra automatiquement en attente d'envoi.
              Utilisez plutôt la fonction "Renvoyer" si vous voulez simplement renvoyer l'email tel quel.
            </Alert>
          )}
          {editEmail?.status === 'error' && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Cet email est en erreur. Vous pouvez le modifier et il sera automatiquement remis en attente d'envoi.
            </Alert>
          )}
          <TextField
            label="Destinataire"
            value={editEmail?.to_email || ''}
            onChange={e => handleEditChange('to_email', e.target.value)}
            fullWidth
            margin="normal"
            helperText="L'adresse email du destinataire"
          />
          <TextField
            label="Sujet"
            value={editEmail?.subject || ''}
            onChange={e => handleEditChange('subject', e.target.value)}
            fullWidth
            margin="normal"
            helperText="Le sujet de l'email"
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Statut</InputLabel>
            <Select
              value={editEmail?.status || ''}
              label="Statut"
              onChange={e => handleEditChange('status', e.target.value)}
            >
              <MenuItem value="pending">En attente</MenuItem>
              <MenuItem value="sent">Envoyé</MenuItem>
              <MenuItem value="error">Erreur</MenuItem>
            </Select>
          </FormControl>
          <TextField
            label="Contenu HTML"
            value={editEmail?.html || ''}
            onChange={e => handleEditChange('html', e.target.value)}
            fullWidth
            margin="normal"
            multiline
            minRows={6}
            helperText="Le contenu HTML de l'email"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditEmail(null)} startIcon={<CloseIcon />}>Annuler</Button>
          <Button
            onClick={handleEditSave}
            startIcon={<SaveIcon />}
            disabled={editLoading}
            sx={{ bgcolor: '#FF6B2C', color: '#fff', '&:hover': { bgcolor: '#e65c1f' } }}
          >
            {editLoading ? 'Enregistrement...' : 'Enregistrer'}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog open={!!viewEmail} onClose={() => { setViewEmail(null); setViewTab('html'); }} maxWidth="md" fullWidth>
        <DialogTitle>Contenu de l'email</DialogTitle>
        <DialogContent>
          <Tabs value={viewTab} onChange={(_, v) => setViewTab(v)} sx={{ mb: 2 }}>
            <Tab value="html" label="HTML" sx={{ color: '#FF6B2C', fontWeight: 600 }} />
            <Tab value="text" label="Texte brut" sx={{ color: '#FF6B2C', fontWeight: 600 }} />
          </Tabs>
          <Typography variant="subtitle2" sx={{ color: '#FF6B2C', mb: 1 }}>Destinataire : {viewEmail?.to_email}</Typography>
          <Typography variant="subtitle2" sx={{ color: '#FF6B2C', mb: 1 }}>Sujet : {viewEmail?.subject}</Typography>
          {viewTab === 'text' && (
            <Box sx={{ background: '#FFF7F3', borderRadius: 2, p: 2, whiteSpace: 'pre-wrap', fontFamily: 'monospace', color: '#333', fontSize: 15 }}>
              {viewEmail ? stripHtml(viewEmail.html) : ''}
            </Box>
          )}
          {viewTab === 'html' && (
            <Box sx={{ background: '#FFF7F3', borderRadius: 2, p: 2, minHeight: 100, color: '#333', fontSize: 15, overflowX: 'auto' }}>
              {viewEmail && (
                <div style={{ fontFamily: 'inherit', color: 'inherit' }} dangerouslySetInnerHTML={{ __html: viewEmail.html }} />
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => { setViewEmail(null); setViewTab('html'); }} startIcon={<CloseIcon />}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EmailQueueAdmin; 