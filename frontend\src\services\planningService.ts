import { api } from './api';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';
import { Mission, PlanningFormData } from '../types/planning';
import { fetchCsrfToken } from '../services/csrf';

const planningService = {
  // Importer les missions acceptées dans le planning
  importMissions: async () => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post('/api/planning/import-missions', {}, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      logger.error('Erreur lors de l\'import des missions:', error);
      
      // Gestion spécifique de l'erreur 403 pour les limites de missions
      if (error.response && error.response.status === 403) {
        logger.info('Limite de missions atteinte pour utilisateur non-premium lors de l\'importation');
      }
      
      throw error;
    }
  },

  // Récupérer toutes les missions du planning
  getMissions: async (month?: number, year?: number): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      
      // Construire l'URL avec les paramètres de filtre
      let url = '/api/planning';
      const params = new URLSearchParams();
      
      if (month !== undefined && year !== undefined) {
        params.append('month', month.toString());
        params.append('year', year.toString());
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await api.get(url, {
        headers,
        withCredentials: true
      });
      
      // Mapper les champs de la BDD vers l'interface Mission
      return response.data.map((mission: any) => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        date: mission.date,
        start_time: mission.start_time,
        end_time: mission.end_time,
        // Pour les missions manuelles, inclure les champs montant_propose et payment_method
        montant_propose: mission.montant_propose,
        payment_method: mission.payment_method,
        // Pour les missions manuelles sans mission_id, créer un objet mission vide
        mission: mission.mission || {
          id: '',
          title: '',
          description: ''
        },
        // Ajouter les informations utilisateur si disponibles
        user: mission.user ? {
          id: mission.user.id,
          prenom: mission.user.prenom || '',
          nom: mission.user.nom || '',
          photo_url: mission.user.photo_url || ''
        } : undefined,
        // Ajouter les informations de proposition si disponibles
        proposition: mission.proposition ? {
          id: mission.proposition.id,
          statut: mission.proposition.statut,
          montant_propose: mission.proposition.montant_propose,
          message: mission.proposition.message,
          montant_contre_offre: mission.proposition.montant_contre_offre,
          message_contre_offre: mission.proposition.message_contre_offre,
          montant_contre_offre_jobbeur: mission.proposition.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: mission.proposition.message_contre_offre_jobbeur,
          payment_status: mission.proposition.payment_status,
          payment_date: mission.proposition.payment_date,
          montant_paiement: mission.proposition.montant_paiement,
          jobbeur_id: mission.proposition.jobbeur_id,
          payment_method: mission.proposition.payment_method || 'jobi_only'
        } : undefined
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions:', error);
      throw error;
    }
  },

  // Récupérer les missions masquées du planning
  getHiddenMissions: async (month?: number, year?: number): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      
      // Construire l'URL avec les paramètres de filtre
      let url = '/api/planning/hidden';
      const params = new URLSearchParams();
      
      if (month !== undefined && year !== undefined) {
        params.append('month', month.toString());
        params.append('year', year.toString());
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await api.get(url, {
        headers,
        withCredentials: true
      });
      
      // Mapper les champs de la BDD vers l'interface Mission
      return response.data.map((mission: any) => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        date: mission.date,
        start_time: mission.start_time,
        end_time: mission.end_time,
        // Pour les missions manuelles, inclure les champs montant_propose et payment_method
        montant_propose: mission.montant_propose,
        payment_method: mission.payment_method,
        // Pour les missions manuelles sans mission_id, créer un objet mission vide
        mission: mission.mission || {
          id: '',
          title: '',
          description: ''
        },
        // Ajouter les informations utilisateur si disponibles
        user: mission.user ? {
          id: mission.user.id,
          prenom: mission.user.prenom || '',
          nom: mission.user.nom || '',
          photo_url: mission.user.photo_url || ''
        } : undefined,
        // Ajouter les informations de proposition si disponibles
        proposition: mission.proposition ? {
          id: mission.proposition.id,
          statut: mission.proposition.statut,
          montant_propose: mission.proposition.montant_propose,
          message: mission.proposition.message,
          montant_contre_offre: mission.proposition.montant_contre_offre,
          message_contre_offre: mission.proposition.message_contre_offre,
          montant_contre_offre_jobbeur: mission.proposition.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: mission.proposition.message_contre_offre_jobbeur,
          payment_status: mission.proposition.payment_status,
          payment_date: mission.proposition.payment_date,
          montant_paiement: mission.proposition.montant_paiement,
          jobbeur_id: mission.proposition.jobbeur_id,
          payment_method: mission.proposition.payment_method || 'jobi_only'
        } : undefined
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions masquées:', error);
      throw error;
    }
  },

  // Récupérer toutes les missions visibles d'une année
  getMissionsByYear: async (year: number): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      
      // Construire l'URL avec le paramètre année
      let url = '/api/planning';
      const params = new URLSearchParams();
      
      params.append('year', year.toString());
      params.append('year_only', 'true');
      
      url += `?${params.toString()}`;
      
      const response = await api.get(url, {
        headers,
        withCredentials: true
      });
      
      // Mapper les champs de la BDD vers l'interface Mission
      return response.data.map((mission: any) => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        date: mission.date,
        start_time: mission.start_time,
        end_time: mission.end_time,
        // Pour les missions manuelles, inclure les champs montant_propose et payment_method
        montant_propose: mission.montant_propose,
        payment_method: mission.payment_method,
        // Pour les missions manuelles sans mission_id, créer un objet mission vide
        mission: mission.mission || {
          id: '',
          title: '',
          description: ''
        },
        // Ajouter les informations utilisateur si disponibles
        user: mission.user ? {
          id: mission.user.id,
          prenom: mission.user.prenom || '',
          nom: mission.user.nom || '',
          photo_url: mission.user.photo_url || ''
        } : undefined,
        // Ajouter les informations de proposition si disponibles
        proposition: mission.proposition ? {
          id: mission.proposition.id,
          statut: mission.proposition.statut,
          montant_propose: mission.proposition.montant_propose,
          message: mission.proposition.message,
          montant_contre_offre: mission.proposition.montant_contre_offre,
          message_contre_offre: mission.proposition.message_contre_offre,
          montant_contre_offre_jobbeur: mission.proposition.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: mission.proposition.message_contre_offre_jobbeur,
          payment_status: mission.proposition.payment_status,
          payment_date: mission.proposition.payment_date,
          montant_paiement: mission.proposition.montant_paiement,
          jobbeur_id: mission.proposition.jobbeur_id,
          payment_method: mission.proposition.payment_method || 'jobi_only'
        } : undefined
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions de l\'année:', error);
      
      // En cas d'échec (si le backend ne supporte pas encore le paramètre year_only),
      // on revient à la méthode précédente qui fait 12 requêtes
      logger.info('Tentative de récupération mois par mois suite à l\'échec de la requête annuelle');
      
      try {
        // Fallback: Appeler la méthode getMissions pour chaque mois de l'année
        const promises = [];
        for (let month = 1; month <= 12; month++) {
          promises.push(planningService.getMissions(month, year));
        }
        
        // Attendre que toutes les promesses soient résolues
        const results = await Promise.all(promises);
        
        // Combiner tous les résultats en un seul tableau
        return results.flatMap(missions => missions);
      } catch (fallbackError) {
        logger.error('Erreur lors du fallback de récupération des missions:', fallbackError);
        throw fallbackError;
      }
    }
  },

  // Récupérer toutes les missions masquées d'une année
  getHiddenMissionsByYear: async (year: number): Promise<Mission[]> => {
    try {
      const headers = await getCommonHeaders();
      
      // Construire l'URL avec le paramètre année
      let url = '/api/planning/hidden';
      const params = new URLSearchParams();
      
      params.append('year', year.toString());
      params.append('year_only', 'true');
      
      url += `?${params.toString()}`;
      
      const response = await api.get(url, {
        headers,
        withCredentials: true
      });
      
      // Mapper les champs de la BDD vers l'interface Mission
      return response.data.map((mission: any) => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        date: mission.date,
        start_time: mission.start_time,
        end_time: mission.end_time,
        // Pour les missions manuelles, inclure les champs montant_propose et payment_method
        montant_propose: mission.montant_propose,
        payment_method: mission.payment_method,
        // Pour les missions manuelles sans mission_id, créer un objet mission vide
        mission: mission.mission || {
          id: '',
          title: '',
          description: ''
        },
        // Ajouter les informations utilisateur si disponibles
        user: mission.user ? {
          id: mission.user.id,
          prenom: mission.user.prenom || '',
          nom: mission.user.nom || '',
          photo_url: mission.user.photo_url || ''
        } : undefined,
        // Ajouter les informations de proposition si disponibles
        proposition: mission.proposition ? {
          id: mission.proposition.id,
          statut: mission.proposition.statut,
          montant_propose: mission.proposition.montant_propose,
          message: mission.proposition.message,
          montant_contre_offre: mission.proposition.montant_contre_offre,
          message_contre_offre: mission.proposition.message_contre_offre,
          montant_contre_offre_jobbeur: mission.proposition.montant_contre_offre_jobbeur,
          message_contre_offre_jobbeur: mission.proposition.message_contre_offre_jobbeur,
          payment_status: mission.proposition.payment_status,
          payment_date: mission.proposition.payment_date,
          montant_paiement: mission.proposition.montant_paiement,
          jobbeur_id: mission.proposition.jobbeur_id,
          payment_method: mission.proposition.payment_method || 'jobi_only'
        } : undefined
      }));
    } catch (error) {
      logger.error('Erreur lors de la récupération des missions masquées de l\'année:', error);
      
      // En cas d'échec (si le backend ne supporte pas encore le paramètre year_only),
      // on revient à la méthode précédente qui fait 12 requêtes
      logger.info('Tentative de récupération mois par mois suite à l\'échec de la requête annuelle');
      
      try {
        // Fallback: Appeler la méthode getHiddenMissions pour chaque mois de l'année
        const promises = [];
        for (let month = 1; month <= 12; month++) {
          promises.push(planningService.getHiddenMissions(month, year));
        }
        
        // Attendre que toutes les promesses soient résolues
        const results = await Promise.all(promises);
        
        // Combiner tous les résultats en un seul tableau
        return results.flatMap(missions => missions);
      } catch (fallbackError) {
        logger.error('Erreur lors du fallback de récupération des missions masquées:', fallbackError);
        throw fallbackError;
      }
    }
  },

  // Ajouter une mission au planning
  addMission: async (data: PlanningFormData) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      
      // Formater les heures pour correspondre au format attendu par la validation (HH:MM)
      const formatTime = (time: string) => {
        if (time.includes(':')) {
          return time.substring(0, 5); // Garder uniquement HH:MM
        }
        return time;
      };
      
      // Convertir les champs pour correspondre à la BDD
      const dbData = {
        ...(data.mission_id && { mission_id: data.mission_id }), // Ajouter mission_id seulement s'il existe
        title: data.title,
        description: data.description,
        date: data.date,
        start_time: formatTime(data.start_time),
        end_time: formatTime(data.end_time),
        // Ajouter les champs de paiement uniquement pour les missions manuelles
        ...(!data.mission_id && {
          montant_propose: data.montant_propose,
          payment_method: data.payment_method
        })
      };
      const response = await api.post('/api/planning', dbData, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      logger.error('Erreur lors de l\'ajout de la mission:', error);
      
      // Gestion spécifique de l'erreur 403 pour les limites de missions
      if (error.response && error.response.status === 403) {
        logger.info('Limite de missions atteinte pour utilisateur non-premium');
      }
      
      throw error;
    }
  },

  // Modifier une mission du planning
  updateMission: async (id: string, data: PlanningFormData) => {
    try {
      const headers = await getCommonHeaders();
      
      // Formater les heures pour correspondre au format attendu par la validation (HH:MM)
      const formatTime = (time: string) => {
        if (time.includes(':')) {
          return time.substring(0, 5); // Garder uniquement HH:MM
        }
        return time;
      };
      
      // S'assurer que montant_propose est un nombre valide
      const montantPropose = typeof data.montant_propose === 'string'
        ? parseFloat(data.montant_propose)
        : data.montant_propose || 0;
      
      // Convertir les champs pour correspondre à la BDD
      const dbData = {
        title: data.title,
        description: data.description,
        date: data.date,
        start_time: formatTime(data.start_time),
        end_time: formatTime(data.end_time),
        // Inclure mission_id si présent
        ...(data.mission_id && { mission_id: data.mission_id }),
        // Ajouter les champs de paiement pour toutes les missions
        montant_propose: isNaN(montantPropose) ? 0 : montantPropose,
        payment_method: data.payment_method
      };
      const response = await api.put(`/api/planning/${id}`, dbData, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la modification de la mission:', error);
      throw error;
    }
  },

  // Supprimer une mission du planning
  deleteMission: async (id: string) => {
    try {
      const headers = await getCommonHeaders();
      const response = await api.delete(`/api/planning/${id}`, {
        headers,
        withCredentials: true
      });
      
      // Récupérer l'information de masquage ou suppression depuis les logs du backend
      const logInfo = response.headers['x-mission-action'] || '';
      const result = {
        success: true,
        data: response.data,
        isSoftDelete: logInfo.includes('masqué') || false
      };
      
      return result;
    } catch (error: any) {
      // Gestion améliorée des erreurs en fonction du statut HTTP
      if (error.response) {
        const status = error.response.status;
        
        switch (status) {
          case 403:
            logger.warn('Autorisation refusée pour la suppression de la mission:', { id, status });
            throw new Error('Vous n\'avez pas les droits pour supprimer cette mission');
          case 404:
            logger.warn('Mission non trouvée lors de la suppression:', { id, status });
            throw new Error('Mission non trouvée');
          case 401:
            logger.warn('Utilisateur non authentifié lors de la suppression:', { id, status });
            throw new Error('Veuillez vous reconnecter pour effectuer cette action');
          default:
            logger.error('Erreur lors de la suppression de la mission:', { id, status, error });
            throw new Error('Erreur lors de la suppression de la mission');
        }
      }
      
      logger.error('Erreur lors de la suppression de la mission:', error);
      throw error;
    }
  },

  // Restaurer une mission masquée
  restoreMission: async (id: string) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/planning/${id}/restore`, {}, {
        headers,
        withCredentials: true
      });
      
      return response.data;
    } catch (error: any) {
      // Gestion améliorée des erreurs en fonction du statut HTTP
      if (error.response) {
        const status = error.response.status;
        
        switch (status) {
          case 403:
            logger.warn('Autorisation refusée pour la restauration de la mission:', { id, status });
            throw new Error('Vous n\'avez pas les droits pour restaurer cette mission');
          case 404:
            logger.warn('Mission non trouvée ou déjà visible:', { id, status });
            throw new Error('Mission non trouvée ou déjà visible');
          case 401:
            logger.warn('Utilisateur non authentifié lors de la restauration:', { id, status });
            throw new Error('Veuillez vous reconnecter pour effectuer cette action');
          default:
            logger.error('Erreur lors de la restauration de la mission:', { id, status, error });
            throw new Error('Erreur lors de la restauration de la mission');
        }
      }
      
      logger.error('Erreur lors de la restauration de la mission:', error);
      throw error;
    }
  },

  // Exporter le planning au format iCal
  exportIcal: async () => {
    try {
      const headers = await getCommonHeaders();
      const response = await api.get('/api/planning/ical', {
        headers,
        withCredentials: true,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de l\'export du planning:', error);
      throw error;
    }
  },

  // Obtenir l'URL de synchronisation avec Google Calendar
  getGoogleCalendarSyncUrl: async () => {
    try {
      const headers = await getCommonHeaders();
      const response = await api.get('/api/planning/google-calendar-sync', {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération de l\'URL de synchronisation Google Calendar:', error);
      throw error;
    }
  }
};

export default planningService; 