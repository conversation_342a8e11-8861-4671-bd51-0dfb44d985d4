import React from 'react';

// MUI Icons - Location
import LocationOnIcon from '@mui/icons-material/LocationOn';
import MapIcon from '@mui/icons-material/Map';
import NavigationIcon from '@mui/icons-material/Navigation';

// MUI Icons - People
import PersonIcon from '@mui/icons-material/Person';
import PeopleIcon from '@mui/icons-material/People';
import GroupIcon from '@mui/icons-material/Group';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';

// MUI Icons - Commerce
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import StoreIcon from '@mui/icons-material/Store';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import PaymentIcon from '@mui/icons-material/Payment';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import BusinessIcon from '@mui/icons-material/Business';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';

// MUI Icons - Calendar
import EventIcon from '@mui/icons-material/Event';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ScheduleIcon from '@mui/icons-material/Schedule';
import DateRangeIcon from '@mui/icons-material/DateRange';

// MUI Icons - Ratings
import StarIcon from '@mui/icons-material/Star';
import GradeIcon from '@mui/icons-material/Grade';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';

// MUI Icons - Alerts
import WarningIcon from '@mui/icons-material/Warning';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import HelpIcon from '@mui/icons-material/Help';

// MUI Icons - Actions
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';

// MUI Icons - Files
import SaveIcon from '@mui/icons-material/Save';
import UploadIcon from '@mui/icons-material/Upload';
import DownloadIcon from '@mui/icons-material/Download';
import ShareIcon from '@mui/icons-material/Share';
import FolderIcon from '@mui/icons-material/Folder';

// MUI Icons - Filters
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';
import ViewListIcon from '@mui/icons-material/ViewList';

// MUI Icons - Communication
import MessageIcon from '@mui/icons-material/Message';
import ChatIcon from '@mui/icons-material/Chat';
import MailIcon from '@mui/icons-material/Mail';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CampaignIcon from '@mui/icons-material/Campaign';

// MUI Icons - Status
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

// MUI Icons - Categories
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import EuroIcon from '@mui/icons-material/Euro';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import YardIcon from '@mui/icons-material/Yard';
import HandymanIcon from '@mui/icons-material/Handyman';
import PetsIcon from '@mui/icons-material/Pets';
import ComputerIcon from '@mui/icons-material/Computer';
import CelebrationIcon from '@mui/icons-material/Celebration';
import SpaIcon from '@mui/icons-material/Spa';
import PaletteIcon from '@mui/icons-material/Palette';
import SportsBasketballIcon from '@mui/icons-material/SportsBasketball';
import HomeIcon from '@mui/icons-material/Home';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import BrushIcon from '@mui/icons-material/Brush';
import FlightIcon from '@mui/icons-material/Flight';
import ConstructionIcon from '@mui/icons-material/Construction';

// MUI Icons - Additional
import BriefcaseIcon from '@mui/icons-material/Work';
import ClockIcon from '@mui/icons-material/AccessTime';
import TagIcon from '@mui/icons-material/Label';
import SchoolIcon from '@mui/icons-material/School';

// Types d'icônes avec leurs propriétés
interface IconProps {
  className?: string;
  onClick?: () => void;
}

// Export direct des icônes MUI pour une utilisation flexible
export {
  // Location
  LocationOnIcon, MapIcon, NavigationIcon,
  // People
  PersonIcon, PeopleIcon, GroupIcon, AccountCircleIcon, PersonOutlineIcon,
  // Commerce
  ShoppingCartIcon, StoreIcon, LocalOfferIcon, PaymentIcon, LocalShippingIcon, BusinessIcon, AccountBalanceIcon,
  // Calendar
  EventIcon, CalendarTodayIcon, ScheduleIcon, DateRangeIcon,
  // Ratings
  StarIcon, GradeIcon, FavoriteIcon, ThumbUpIcon,
  // Alerts
  WarningIcon, ErrorIcon, InfoIcon, HelpIcon,
  // Actions
  EditIcon, DeleteIcon, AddIcon, RemoveIcon,
  // Files
  SaveIcon, UploadIcon, DownloadIcon, ShareIcon, FolderIcon,
  // Filters
  SearchIcon, FilterListIcon, SortIcon, ViewListIcon,
  // Communication
  MessageIcon, ChatIcon, MailIcon, NotificationsIcon, CampaignIcon,
  // Status
  VisibilityIcon, VisibilityOffIcon, CheckCircleIcon, CancelIcon,
  // Categories
  CategoryIcon, DescriptionIcon, EuroIcon, AccessTimeIcon,
  YardIcon, HandymanIcon, PetsIcon, ComputerIcon,
  CelebrationIcon, SpaIcon, PaletteIcon, SportsBasketballIcon,
  HomeIcon, DirectionsCarIcon, BrushIcon, FlightIcon,
  ConstructionIcon, SchoolIcon,
  // Additional
  BriefcaseIcon, ClockIcon, TagIcon
};

// Composants d'icônes réutilisables avec des styles par défaut
export const LocationIcon: React.FC<IconProps> = (props) => (
  <LocationOnIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const PersonUserIcon: React.FC<IconProps> = (props) => (
  <PersonIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const ShoppingIcon: React.FC<IconProps> = (props) => (
  <ShoppingCartIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const CalendarIcon: React.FC<IconProps> = (props) => (
  <CalendarTodayIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const RatingStarIcon: React.FC<IconProps> = (props) => (
  <StarIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const AlertWarningIcon: React.FC<IconProps> = (props) => (
  <WarningIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const EditActionIcon: React.FC<IconProps> = (props) => (
  <EditIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const SaveFileIcon: React.FC<IconProps> = (props) => (
  <SaveIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const SearchFilterIcon: React.FC<IconProps> = (props) => (
  <SearchIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
);

export const MessageCommIcon: React.FC<IconProps> = (props) => (
  <MessageIcon {...props} className={`h-5 w-5 ${props.className || ''}`} />
); 