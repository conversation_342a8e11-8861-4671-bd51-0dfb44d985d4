import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  useTheme,
  Paper,
  Button
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Briefcase,
  CreditCard,
  Users,
  Star,
  Zap,
  DollarSign,
  Activity,
  BarChart2,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  LineChart as LineChartIcon,
  RefreshCw
} from 'lucide-react';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Palette de couleurs pour les graphiques
const CHART_COLORS = [
  COLORS.primary,
  COLORS.info,
  COLORS.success,
  COLORS.warning,
  COLORS.error,
  COLORS.tertiary,
  '#9C27B0', // Violet
  '#009688', // Teal
  '#795548', // Marron
  '#607D8B', // Bleu gris
];

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '40px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

interface UserStatsChartsProps {
  userId: string;
  period?: string;
}

interface StatsData {
  general: {
    registrationDate: string;
    lastActivity: string;
    isOnline: boolean;
    accountAge: number;
  };
  missions: {
    total: number;
    byStatus: Record<string, number>;
    totalBudget: number;
  };
  candidatures: {
    total: number;
    byStatus: Record<string, number>;
    totalEarnings: number;
  };
  reviews: {
    total: number;
    averageRating: number;
  };
  activity: {
    loginCount: number;
    uniqueIPs: number;
    avgConnectionsPerDay: number;
    avgMissionsPerDay: number;
    activityRate: number;
  };
  financial: {
    totalMissionBudget: number;
    totalTransactionAmount: number;
    currentJobi: number;
    jobiTransactions: {
      in: number;
      out: number;
    };
    currentAiCredits: number;
    aiCreditUsage: number;
  };
  connections: {
    evolution: {
      date: string;
      count: number;
    }[];
    uniqueIPs: number;
    avgPerDay: number;
  };
}

const UserStatsCharts: React.FC<UserStatsChartsProps> = ({ userId, period = 'all' }) => {
  const theme = useTheme();
  
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(period);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        await fetchCsrfToken();
        const headers = await getCommonHeaders();

        const response = await fetch(
          `${API_CONFIG.baseURL}/api/admin/user-management/users/${userId}/stats?period=${selectedPeriod}`,
          {
            method: 'GET',
            headers: headers,
            credentials: 'include'
          }
        );

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des statistiques');
        }

        const data = await response.json();
        
        if (data.success) {
          setStats(data.data.stats);
        } else {
          throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        setError(error instanceof Error ? error.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [userId, selectedPeriod]);

  const handlePeriodChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedPeriod(event.target.value as string);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getPercentageColor = (percentage: number) => {
    if (percentage >= 80) return COLORS.error;
    if (percentage >= 60) return COLORS.warning;
    if (percentage >= 40) return COLORS.info;
    return COLORS.success;
  };

  const getTrendIcon = (value: number, positive: boolean = true) => {
    if (value === 0) return null;
    if ((value > 0 && positive) || (value < 0 && !positive)) {
      return <TrendingUp size={16} color={COLORS.success} style={{ marginLeft: '4px' }} />;
    }
    return <TrendingDown size={16} color={COLORS.error} style={{ marginLeft: '4px' }} />;
  };

  if (loading && !stats) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress sx={{ color: COLORS.primary }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert 
        severity="error" 
        sx={{ 
          borderRadius: '12px',
          border: `1px solid ${COLORS.error}20`
        }}
      >
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert 
        severity="info" 
        sx={{ 
          borderRadius: '12px',
          border: `1px solid ${COLORS.info}20`
        }}
      >
        Aucune statistique disponible pour cet utilisateur
      </Alert>
    );
  }

  // Fonction helper pour accéder aux données de manière sécurisée
  const getSafeValue = (obj: any, path: string, defaultValue: any = 0) => {
    const keys = path.split('.');
    let current = obj;
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    return current !== null && current !== undefined ? current : defaultValue;
  };

  // Utiliser directement les données de l'API sans adaptation
  const displayStats = {
    general: {
      missions: getSafeValue(stats, 'missions.total', 0),
      transactions: getSafeValue(stats, 'candidatures.total', 0),
      jobi: getSafeValue(stats, 'financial.currentJobi', 0),
      aiCredits: getSafeValue(stats, 'financial.currentAiCredits', 0),
      connections: getSafeValue(stats, 'activity.loginCount', 0),
      reviews: getSafeValue(stats, 'reviews.total', 0)
    },
    financial: {
      totalMissionBudget: getSafeValue(stats, 'financial.totalMissionBudget', 0),
      totalTransactionAmount: getSafeValue(stats, 'financial.totalTransactionAmount', 0),
      jobiTransactions: {
        in: getSafeValue(stats, 'financial.jobiTransactions.in', 0),
        out: getSafeValue(stats, 'financial.jobiTransactions.out', 0)
      },
      aiCreditUsage: getSafeValue(stats, 'financial.aiCreditUsage', 0)
    },
    missions: {
      byStatus: Object.entries(getSafeValue(stats, 'missions.byStatus', {})).map(([name, value]) => ({
        name: name === 'en_attente' ? 'En attente' : 
              name === 'en_cours' ? 'En cours' :
              name === 'terminee' ? 'Terminée' :
              name === 'annulee' ? 'Annulée' : name,
        value: value as number,
        color: name === 'en_attente' ? COLORS.warning :
               name === 'en_cours' ? COLORS.info :
               name === 'terminee' ? COLORS.success :
               name === 'annulee' ? COLORS.error : COLORS.primary
      }))
    },
    transactions: {
      byType: Object.entries(getSafeValue(stats, 'candidatures.byStatus', {})).map(([name, value]) => ({
        name: name === 'en_attente' ? 'En attente' :
              name === 'acceptee' ? 'Acceptée' :
              name === 'refusee' ? 'Refusée' :
              name === 'retiree' ? 'Retirée' : name,
        value: value as number
      }))
    },
    connections: {
      evolution: getSafeValue(stats, 'connections.evolution', []),
      uniqueIPs: getSafeValue(stats, 'connections.uniqueIPs', 0),
      avgPerDay: getSafeValue(stats, 'connections.avgPerDay', 0)
    },
    activity: {
      avgMissionsPerDay: getSafeValue(stats, 'activity.avgMissionsPerDay', 0),
      activityRate: getSafeValue(stats, 'activity.activityRate', 0) * 100 // Convertir en pourcentage
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <SectionTitle>Statistiques de l'utilisateur</SectionTitle>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <StyledFormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Période</InputLabel>
            <Select
              value={selectedPeriod}
              onChange={handlePeriodChange as any}
              label="Période"
            >
              <MenuItem value="all">Tout</MenuItem>
              <MenuItem value="today">Aujourd'hui</MenuItem>
              <MenuItem value="week">Cette semaine</MenuItem>
              <MenuItem value="month">Ce mois</MenuItem>
              <MenuItem value="year">Cette année</MenuItem>
            </Select>
          </StyledFormControl>
          <StyledButton
            variant="outlined"
            startIcon={<RefreshCw size={16} />}
            onClick={() => {
              // Forcer le rechargement des données
              setStats(null);
              setError(null);
              setLoading(true);
              // Le useEffect se déclenchera automatiquement
            }}
          >
            Actualiser
          </StyledButton>
        </Box>
      </Box>

      {/* Statistiques générales */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>    
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox>
                <Briefcase size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Missions
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="#2D3748">
                {displayStats.general.missions}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.info}>
                <CreditCard size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Transactions
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.info}>
                {displayStats.general.transactions}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.primary}>
                <DollarSign size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Jobi
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.primary}>
                {displayStats.general.jobi}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.warning}>
                <Zap size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Crédits AI
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.warning}>
                {displayStats.general.aiCredits}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.success}>
                <Users size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Connexions
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.success}>
                {displayStats.general.connections}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
          <MetricCard>
            <Box sx={{ position: 'relative', pt: 1 }}>
              <IconBox color={COLORS.error}>
                <Star size={20} />
              </IconBox>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Avis
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={COLORS.error}>
                {displayStats.general.reviews}
              </Typography>
            </Box>
          </MetricCard>
        </Grid>
      </Grid>

      {/* Statistiques financières */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 6 }}>
          <StyledPaper>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <DollarSign size={18} color={COLORS.primary} style={{ marginRight: '8px' }} />
              <Typography variant="h6" fontWeight="600">Finances</Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Budget total des missions
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {formatCurrency(displayStats.financial.totalMissionBudget)}
                  </Typography>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Montant total des transactions
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {formatCurrency(displayStats.financial.totalTransactionAmount)}
                  </Typography>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Jobi entrants
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="h5" fontWeight="bold" color={COLORS.success}>
                      +{displayStats.financial.jobiTransactions.in}
                    </Typography>
                    <TrendingUp size={16} color={COLORS.success} style={{ marginLeft: '4px' }} />
                  </Box>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Jobi sortants
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="h5" fontWeight="bold" color={COLORS.error}>
                      -{displayStats.financial.jobiTransactions.out}
                    </Typography>
                    <TrendingDown size={16} color={COLORS.error} style={{ marginLeft: '4px' }} />
                  </Box>
                </Box>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Utilisation des crédits AI
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {displayStats.financial.aiCreditUsage} crédits
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </StyledPaper>
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <StyledPaper>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Activity size={18} color={COLORS.primary} style={{ marginRight: '8px' }} />
              <Typography variant="h6" fontWeight="600">Activité</Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>  
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    IPs uniques
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {displayStats.connections.uniqueIPs}
                  </Typography>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Connexions moyennes / jour
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {displayStats.connections.avgPerDay.toFixed(1)}
                  </Typography>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Missions moyennes / jour
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color="#2D3748">
                    {displayStats.activity.avgMissionsPerDay.toFixed(1)}
                  </Typography>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Taux d'activité
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography 
                      variant="h5" 
                      fontWeight="bold" 
                      color={getPercentageColor(displayStats.activity.activityRate)}
                    >
                      {(displayStats.activity.activityRate).toFixed(0)}%
                    </Typography>
                    {getTrendIcon(displayStats.activity.activityRate - 0.5)}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </StyledPaper>
        </Grid>
      </Grid>

      {/* Graphiques */}
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 4 }}>
          <StyledPaper sx={{ height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PieChartIcon size={18} color={COLORS.primary} style={{ marginRight: '8px' }} />
              <Typography variant="h6" fontWeight="600">Missions par statut</Typography>
            </Box>
            {displayStats.missions.byStatus.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={displayStats.missions.byStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {displayStats.missions.byStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color || CHART_COLORS[index % CHART_COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip formatter={(value, name) => [`${value} missions`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <Typography variant="body1" color="text.secondary">
                  Aucune donnée disponible
                </Typography>
              </Box>
            )}
          </StyledPaper>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <StyledPaper sx={{ height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <BarChart2 size={18} color={COLORS.primary} style={{ marginRight: '8px' }} />
              <Typography variant="h6" fontWeight="600">Transactions par type</Typography>
            </Box>
            {displayStats.transactions.byType.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={displayStats.transactions.byType}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip formatter={(value) => [`${value} transactions`]} />
                  <Bar dataKey="value" fill={COLORS.primary}>
                    {displayStats.transactions.byType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <Typography variant="body1" color="text.secondary">
                  Aucune donnée disponible
                </Typography>
              </Box>
            )}
          </StyledPaper>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <StyledPaper sx={{ height: 400 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LineChartIcon size={18} color={COLORS.primary} style={{ marginRight: '8px' }} />
              <Typography variant="h6" fontWeight="600">Évolution des connexions</Typography>
            </Box>
            {displayStats.connections.evolution.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={displayStats.connections.evolution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip formatter={(value) => [`${value} connexions`]} />
                  <Line type="monotone" dataKey="count" stroke={COLORS.primary} activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <Typography variant="body1" color="text.secondary">
                  Aucune donnée disponible
                </Typography>
              </Box>
            )}
          </StyledPaper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserStatsCharts;
