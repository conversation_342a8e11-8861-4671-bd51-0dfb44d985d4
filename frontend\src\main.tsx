import React from 'react';
import { createRoot } from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { router } from './router';
import './tailwind.css';
import './index.css';
import { logger } from './utils/logger';
import { HelmetProvider } from 'react-helmet-async';

// Type pour les ressources
interface Resource {
  rel: string;
  href: string;
  as?: string;
  type?: string;
}

// Préchargement des ressources critiques
const preloadResources = () => {
  const resources: Resource[] = [
    { rel: 'preconnect', href: 'https://api-domoveillance.fr' }
  ];

  resources.forEach((resource) => {
    const { rel, href, as, type } = resource;
    const link = document.createElement('link');
    link.rel = rel;
    link.href = href;
    if (as) link.setAttribute('as', as);
    if (type) link.setAttribute('type', type);
    document.head.appendChild(link);
  });
};

// Fonction pour vérifier et nettoyer les ressources obsolètes
const cleanupStaleResources = () => {
  try {
    // Nettoyer les éventuels flags de socket actifs obsolètes
    const socketFlag = localStorage.getItem('socket_active');
    if (socketFlag) {
      try {
        const { timestamp } = JSON.parse(socketFlag);
        if (Date.now() - timestamp > 60000) { // 1 minute
          localStorage.removeItem('socket_active');
        }
      } catch {
        localStorage.removeItem('socket_active');
      }
    }

    // Nettoyer le cache du navigateur pour les ressources critiques
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          if (cacheName.startsWith('jobpartiel-')) {
            caches.delete(cacheName);
          }
        });
      });
    }
  } catch (error) {
    logger.error('Error cleaning up stale resources:', error);
  }
};

// Initialisation de l'application avec optimisations
const initApp = async () => {
  try {
    // Nettoyer les ressources obsolètes
    cleanupStaleResources();

    // Précharger les ressources
    preloadResources();

    const container = document.getElementById('root');
    if (!container) {
      throw new Error('Root element not found');
    }

    // Ajouter un gestionnaire d'erreurs global pour les ressources
    window.addEventListener('error', (event) => {
      if (event.target && (event.target as HTMLElement).tagName === 'SCRIPT' || (event.target as HTMLElement).tagName === 'LINK') {
        logger.error('Resource loading error:', event);
        // Tenter de recharger la page si une ressource critique échoue
        if ((event.target as HTMLScriptElement).src && (event.target as HTMLScriptElement).src.includes('main-')) {
          setTimeout(() => window.location.reload(), 2000);
        }
      }
    }, true);

    const root = createRoot(container);
    root.render(
      <React.StrictMode>
        <HelmetProvider>
          <RouterProvider router={router} />
        </HelmetProvider>
      </React.StrictMode>
    );
  } catch (error) {
    logger.error('Failed to initialize app:', error);
    // Tenter de recharger la page en cas d'erreur critique
    setTimeout(() => window.location.reload(), 2000);
  }
};

// Démarrage de l'application
initApp();

