import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

export class PromoCodesController {
  // Récupérer tous les codes de réduction
  async getAllPromoCodes(req: Request, res: Response) {
    try {
      const cacheKey = 'promocodes:all';
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        return res.json(JSON.parse(cachedData));
      }
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const response = {
        success: true,
        data
      };
      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(response));
      return res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des codes promo:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des codes promo',
        toastType: 'error'
      });
    }
  }

  // Créer un nouveau code promo
  async createPromoCode(req: Request, res: Response) {
    try {
      const {
        code = '',
        discount_type,
        discount_value,
        max_uses,
        expires_at,
        plan_type,
        description,
        duration_type,
        limit_first_users
      } = req.body;

      // Générer un code aléatoire si non fourni
      const promoCode = code.trim() || this.generatePromoCode();
      
      // Vérifier si le code existe déjà
      const { data: existingCode, error: checkError } = await supabase
        .from('promo_codes')
        .select('id')
        .eq('code', promoCode)
        .maybeSingle();

      if (checkError) throw checkError;

      if (existingCode) {
        res.status(400).json({
          success: false,
          message: 'Ce code promo existe déjà',
          toastType: 'error'
        });
      }

      // Insérer le nouveau code promo
      const { data, error } = await supabase
        .from('promo_codes')
        .insert({
          code: promoCode,
          discount_type, // 'percentage' ou 'fixed'
          discount_value, // Valeur de la réduction (pourcentage ou montant fixe)
          max_uses, // Nombre maximal d'utilisations
          remaining_uses: max_uses, // Nombre d'utilisations restantes
          expires_at: expires_at || null, // Date d'expiration (null si non spécifiée)
          is_active: true,
          plan_type, // Type de plan (premium, gratuit, null pour tous)
          description, // Description du code promo
          duration_type, // 'lifetime' ou 'one_time'
          limit_first_users // Limité aux X premiers utilisateurs
        })
        .select()
        .single();

      if (error) throw error;

      // Invalider le cache des codes promo
      await redis.del('promocodes:all');
      // Invalider le cache des codes promo utilisateur
      const userKeys = await redis.keys('promocodes:user:*');
      if (userKeys.length > 0) {
        await redis.del(...userKeys);
      }

      return res.json({
        success: true,
        message: 'Code promo créé avec succès',
        data,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la création du code promo:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création du code promo',
        toastType: 'error'
      });
    }
  }

  // Mettre à jour un code promo
  async updatePromoCode(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const {
        code,
        discount_type,
        discount_value,
        max_uses,
        remaining_uses,
        expires_at,
        is_active,
        plan_type,
        description,
        duration_type,
        limit_first_users
      } = req.body;

      // Vérifier si le code existe déjà (s'il a été modifié)
      if (code) {
        const { data: existingCode, error: checkError } = await supabase
          .from('promo_codes')
          .select('id')
          .eq('code', code)
          .neq('id', id)
          .maybeSingle();

        if (checkError) throw checkError;

        if (existingCode) {
          res.status(400).json({
            success: false,
            message: 'Ce code promo existe déjà',
            toastType: 'error'
          });
        }
      }

      // Mettre à jour le code promo
      const { data, error } = await supabase
        .from('promo_codes')
        .update({
          code,
          discount_type,
          discount_value,
          max_uses,
          remaining_uses,
          expires_at: expires_at || null,
          is_active,
          plan_type,
          description,
          duration_type,
          limit_first_users,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Invalider le cache des codes promo
      await redis.del('promocodes:all');
      // Invalider le cache des codes promo utilisateur
      const userKeys = await redis.keys('promocodes:user:*');
      if (userKeys.length > 0) {
        await redis.del(...userKeys);
      }

      return res.json({
        success: true,
        message: 'Code promo mis à jour avec succès',
        data,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la mise à jour du code promo:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du code promo',
        toastType: 'error'
      });
    }
  }

  // Supprimer un code promo
  async deletePromoCode(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const { error } = await supabase
        .from('promo_codes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Invalider le cache des codes promo
      await redis.del('promocodes:all');
      // Invalider le cache des codes promo utilisateur
      const userKeys = await redis.keys('promocodes:user:*');
      if (userKeys.length > 0) {
        await redis.del(...userKeys);
      }

      return res.json({
        success: true,
        message: 'Code promo supprimé avec succès',
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression du code promo:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression du code promo',
        toastType: 'error'
      });
    }
  }

  // Vérifier la validité d'un code promo
  async validatePromoCode(req: Request, res: Response) {
    try {
      const { code, plan } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      console.log('code', code);

      // Vérifier si le code existe et est actif
      const { data: promoCode, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code)
        .eq('is_active', true)
        .maybeSingle();

      if (error) throw error;

      console.log('promoCode', promoCode);

      if (!promoCode) {
        return res.status(404).json({
          success: false,
          message: 'Code promo invalide ou expiré',
          toastType: 'error'
        });
      }

      // Vérifier la date d'expiration
      if (promoCode.expires_at && new Date(promoCode.expires_at) < new Date()) {
        res.status(400).json({
          success: false,
          message: 'Ce code promo a expiré',
          toastType: 'error'
        });
      }

      // Vérifier le nombre d'utilisations restantes
      if (promoCode.remaining_uses !== null && promoCode.remaining_uses <= 0) {
        res.status(400).json({
          success: false,
          message: 'Ce code promo a atteint son nombre maximal d\'utilisations',
          toastType: 'error'
        });
      }

      // Vérifier si le code est applicable au plan spécifié
      if (promoCode.plan_type && promoCode.plan_type !== plan) {
        res.status(400).json({
          success: false,
          message: `Ce code promo est uniquement valable pour les abonnements ${promoCode.plan_type}`,
          toastType: 'error'
        });
      }

      // Vérifier si l'utilisateur a déjà utilisé ce code
      const { data: usageData, error: usageError } = await supabase
        .from('promo_code_usage')
        .select('id')
        .eq('user_id', userId)
        .eq('promo_code_id', promoCode.id)
        .maybeSingle();

      if (usageError) throw usageError;

      if (usageData && promoCode.duration_type === 'one_time') {
        res.status(400).json({
          success: false,
          message: 'Vous avez déjà utilisé ce code promo',
          toastType: 'error'
        });
      }

      // Vérifier la limitation aux premiers utilisateurs
      if (promoCode.limit_first_users) {
        const { count, error: countError } = await supabase
          .from('promo_code_usage')
          .select('id', { count: 'exact', head: true })
          .eq('promo_code_id', promoCode.id);
          
        if (countError) throw countError;
        
        if (count && count >= promoCode.limit_first_users) {
          res.status(400).json({
            success: false,
            message: `Ce code était limité aux ${promoCode.limit_first_users} premiers utilisateurs et n'est plus disponible`,
            toastType: 'error'
          });
        }
      }

      // Retourner les informations du code promo
      return res.json({
        success: true,
        message: 'Code promo valide',
        data: {
          id: promoCode.id,
          code: promoCode.code,
          discount_type: promoCode.discount_type,
          discount_value: promoCode.discount_value,
          duration_type: promoCode.duration_type,
          description: promoCode.description
        },
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la validation du code promo:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation du code promo',
        toastType: 'error'
      });
    }
  }

  // Appliquer un code promo à un abonnement
  async applyPromoCode(promoCodeId: string, userId: string, planType: string) {
    try {
      // Récupérer les informations du code promo
      // On vérifie d'abord si promoCodeId est un UUID (format d'ID) ou un code promo (texte)
      let query = supabase.from('promo_codes').select('*').eq('is_active', true);
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidPattern.test(promoCodeId)) {
        query = query.eq('id', promoCodeId);
      } else {
        query = query.eq('code', promoCodeId);
      }
      const { data: promoCode, error } = await query.single();
      if (error || !promoCode) {
        logger.error('Code promo non trouvé ou inactif:', error);
        throw new Error('Code promo non trouvé ou inactif');
      }

      // Vérifier si une utilisation existe déjà pour ce user/code
      const { data: existingUsage, error: usageCheckError } = await supabase
        .from('promo_code_usage')
        .select('id')
        .eq('user_id', userId)
        .eq('promo_code_id', promoCode.id)
        .maybeSingle();
      if (usageCheckError) {
        logger.error('Erreur lors de la vérification de l\'utilisation du code promo:', usageCheckError);
        throw usageCheckError;
      }

      if (promoCode.duration_type === 'lifetime') {
        // Si déjà utilisé, ne rien faire (ou mettre à jour la date si tu veux)
        if (existingUsage) {
          // Optionnel : tu pourrais mettre à jour la date d\'utilisation ici si tu veux garder une trace du dernier usage
          return promoCode;
        }
      } else if (promoCode.duration_type === 'one_time') {
        // Si déjà utilisé, lever une erreur (normalement déjà géré côté validation, mais sécurité)
        if (existingUsage) {
          throw new Error('Code promo déjà utilisé');
        }
      }

      // Enregistrer l'utilisation du code promo
      const { error: usageError } = await supabase
        .from('promo_code_usage')
        .insert({
          user_id: userId,
          promo_code_id: promoCode.id,
          plan_type: planType,
          applied_at: new Date().toISOString()
        });
      if (usageError) {
        logger.error('Erreur lors de l\'enregistrement de l\'utilisation du code promo:', usageError);
        throw usageError;
      }

      // Décrémenter le nombre d'utilisations restantes si applicable
      if (promoCode.remaining_uses !== null) {
        await supabase
          .from('promo_codes')
          .update({
            remaining_uses: Math.max(0, promoCode.remaining_uses - 1)
          })
          .eq('id', promoCode.id);
      }

      return promoCode;
    } catch (error) {
      logger.error('Erreur lors de l\'application du code promo:', error);
      throw error;
    }
  }

  // Générer un code promo aléatoire
  private generatePromoCode(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Nouvelle route : Récupérer les codes promo utilisés/attribués à l'utilisateur
  async getUserPromoCodes(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      const cacheKey = `promocodes:user:${userId}`;
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        return res.json(JSON.parse(cachedData));
      }

      // Jointure promo_code_usage + promo_codes
      const { data, error } = await supabase
        .from('promo_code_usage')
        .select('*, promo_codes(*)')
        .eq('user_id', userId)
        .order('applied_at', { ascending: false });

      if (error) throw error;

      const response = {
        success: true,
        data
      };
      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(response));

      // On retourne la liste des usages avec les infos du code
      return res.json(response);
    } catch (error) {
      logger.error('Erreur lors de la récupération des codes promo utilisateur:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des codes promo utilisateur',
        toastType: 'error'
      });
    }
  }
}

export default new PromoCodesController(); 