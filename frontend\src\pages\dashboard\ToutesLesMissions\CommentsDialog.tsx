import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import {
  DialogTitle,
  DialogContent,
  Button,
  Box,
  Typography,
  Avatar,
  IconButton,
  Tooltip,
  CircularProgress,
  Divider,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import useContentModeration from '../../../hooks/useContentModeration';
import { styled } from '@mui/material/styles';
import { AnimatePresence } from 'framer-motion';
import { MessageCircle, Edit2, Trash2, Send, X, Reply, Lock, HelpCircle, Building2, CircleAlert, MapPin, Clock, Euro, BadgeCheck, Hand, Eye, ArrowRightLeft, AlertCircle, CheckCircle2 } from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { missionsApi, Comment, Mission } from './missionsApi';
import { reportComment } from './missionsApi';
import { notify } from '../../../components/Notification';
import { setCookie, getCookie } from '../../../utils/cookieUtils';
import logger from '@/utils/logger';
import DOMPurify from 'dompurify';
import { useSearchParams } from 'react-router-dom';
import ModalPortal from '@/components/ModalPortal';
import OnlineStatusDot from '../../../components/OnlineStatusDot';
import { Paper } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TiptapEditor from '../../../components/TiptapEditor';
import UserProfileModal from '@/components/UserProfileModal';
import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import AiGenerationSystem from '../../../components/ai/AiGenerationSystem';
import { Sparkles } from 'lucide-react';

const StyledModal = styled('div')({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: 'white',
  borderRadius: '16px',
  maxWidth: '900px',
  width: '95%',
  maxHeight: '90vh',
  overflow: 'auto',
  margin: '0 auto',
  zIndex: 1100,
});

const CommentUserInfo = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  marginBottom: '8px',
  gap: '12px',
  '& a': {
    textDecoration: 'none',
    color: 'inherit',
    '&:hover': {
      textDecoration: 'underline',
      color: '#FF6B2C'
    }
  }
});

const CommentActions = styled(Box)({
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '8px',
  marginTop: '8px',
});

const PrivateToggle = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  padding: '6px 10px',
  borderRadius: '8px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  border: '1px solid #FFE4BA',
  backgroundColor: 'white',
  flex: 1,
  [theme.breakpoints.up('sm')]: {
    flex: 'none',
  },
  '&:hover': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FF965E',
  },
  '&.active': {
    backgroundColor: '#FFF8F3',
    borderColor: '#FF6B2C',
    boxShadow: '0 2px 4px rgba(255, 107, 44, 0.1)',
  }
}));

const PrivateIcon = styled(Lock)<{ active?: string }>(({ active }) => ({
  width: 14,
  height: 14,
  color: active === 'true' ? '#FF6B2C' : '#666',
  transition: 'color 0.2s ease',
  flexShrink: 0,
}));

const PrivateLabel = styled(Typography)<{ active?: string }>(({ active }) => ({
  fontSize: '0.75rem',
  color: active === 'true' ? '#FF6B2C' : '#666',
  fontWeight: active === 'true' ? 500 : 400,
  transition: 'all 0.2s ease',
  whiteSpace: 'nowrap',
  lineHeight: 1,
}));

const PrivateConfirmContent = styled('div')({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: '#FFF8F3',
  borderRadius: '16px',
  maxWidth: '500px',
  margin: '16px',
  padding: '24px',
  border: '1px solid #FFE4BA',
  zIndex: 1100,
});

const PrivateConfirmTitle = styled('div')({
  color: '#FF6B2C',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  marginBottom: '16px',
});

const PrivateConfirmActions = styled('div')({
  padding: '16px 0 0 0',
  borderTop: '1px solid #FFE4BA',
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '8px',
});


const CommentInputContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  '.editor-wrapper': {
    position: 'relative',
    backgroundColor: 'white',
    borderRadius: '8px',
    border: '1px solid #FFE4BA',
    overflow: 'hidden',
  },
  '.char-counter': {
    position: 'absolute',
    bottom: '20px',
    right: '12px',
    fontSize: '0.75rem',
    color: '#718096',
    padding: '4px 8px',
    borderRadius: '4px',
    backgroundColor: '#FFF8F3',
    display: 'flex',
    alignItems: 'center',
    gap: '2px',
    zIndex: 2,
    transition: 'all 0.2s ease',
    '&.warning': {
      color: '#FF965E',
    },
    '&.error': {
      color: '#FF6B2C',
      animation: 'pulse 1.5s infinite',
    }
  },
  '.editor-status-bar': {
    position: 'relative',
    backgroundColor: 'white',
    padding: '8px',
    display: 'flex',
    flexDirection: 'column',
    [theme.breakpoints.up('sm')]: {
      flexDirection: 'row',
    },
    alignItems: 'center',
    gap: '8px',
    borderTop: '1px solid #FFE4BA',
  },
  '.status-left': {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    flex: 1,
  },
  '.status-right': {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  '.private-controls': {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    flex: 1,
    [theme.breakpoints.up('sm')]: {
      flex: 'none',
    }
  },
  '.timer-display': {
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    padding: '6px 10px',
    borderRadius: '6px',
    color: '#FF6B2C',
    fontSize: '0.875rem',
    fontWeight: 500,
    transition: 'all 0.2s ease',
    border: '1px solid currentColor',
  },
  '@keyframes spin': {
    '0%': {
      transform: 'rotate(0deg)',
    },
    '100%': {
      transform: 'rotate(360deg)',
    }
  },
  '.send-button': {
    backgroundColor: '#FF6B2C',
    color: 'white',
    padding: '8px 16px',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    transition: 'all 0.2s ease',
    fontWeight: 500,
    textTransform: 'none',
    width: '100%',
    justifyContent: 'center',
    [theme.breakpoints.up('sm')]: {
      width: 'auto',
    },
    '&:hover': {
      backgroundColor: '#FF965E',
    },
    '&:disabled': {
      backgroundColor: '#FFE4BA',
      color: '#FFF8F3',
    },
    '& .send-icon': {
      transition: 'transform 0.2s ease',
    },
    '&:hover .send-icon': {
      transform: 'translateX(2px)',
    }
  }
}));

const UserAvatar = styled(Avatar)({
  width: 40,
  height: 40,
  border: '2px solid #FFE4BA',
  backgroundColor: '#FFF8F3',
  color: '#FF6B2C',
  '& .MuiSvgIcon-root': {
    color: '#FF6B2C',
  }
});

interface CommentsDialogProps {
  open: boolean;
  onClose: () => void;
  missionId: string;
  mission: Mission;
  onUpdate: (updatedMission?: any) => void;
  isOwner?: boolean;
}

// Fonction pour convertir le HTML en texte brut
const stripHtml = (html: string) => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

const SHORT_PLACEHOLDER = "Votre commentaire doit être respectueux et constructif, cliquez sur l'icône '?' pour plus d'informations.";
const FULL_PLACEHOLDER = "Votre commentaire doit être respectueux et constructif : Pas d'insultes, propos offensants, spam ou contenu hors-sujet, interdiction de publier liens, téléphones, emails, adresses personnelles. Soyez concis et précis.";

const EditorHelpIcon = styled(IconButton)({
  position: 'absolute',
  top: '8px',
  right: '8px',
  color: '#FF6B2C',
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  padding: '4px',
  zIndex: 2,
  '&:hover': {
    backgroundColor: '#FFE4BA'
  }
});

const EditorHelpDialog = styled(Box)({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: '#FFF8F3',
  borderRadius: '16px',
  padding: '24px',
  maxWidth: '90vw',
  width: '500px',
  border: '1px solid #FFE4BA',
  zIndex: 1100,
  boxShadow: '0 8px 32px rgba(255, 107, 44, 0.1)',
});

// Déplacer la fonction formatDate avant son utilisation
const formatDate = (date: string) => {
  const now = new Date();
  const commentDate = new Date(date);
  const diffInMinutes = Math.floor((now.getTime() - commentDate.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'À l\'instant';
  } else if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
  } else {
    return formatDistanceToNow(commentDate, { addSuffix: true, locale: fr });
  }
};

// Modifier le type de la prop setReplyingTo dans CommentContent
interface CommentContentProps {
  comment: Comment;
  handleEdit: (id: string) => void;
  handleDelete: (id: string) => void;
  setReplyingTo: (id: string | null) => void;
  isReply: boolean;
}

const CommentContent = React.memo(({
  comment,
  handleEdit,
  handleDelete,
  setReplyingTo
}: CommentContentProps) => {
  const [reportOpen, setReportOpen] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);

  const handleReport = async (reason: string) => {
    setReportLoading(true);
    try {
      await reportComment(comment.id, reason);
      notify('Le commentaire a été signalé. Merci pour votre vigilance.', 'success');
      setReportOpen(false);
    } catch (e: any) {
      // Afficher le message d'erreur précis du backend si disponible
      if (e?.response?.data?.message) {
        notify(e.response.data.message, 'error');
      } else if (e?.response?.data?.error) {
        notify(e.response.data.error, 'error');
      } else {
        notify("Erreur lors du signalement.", 'error');
      }
    } finally {
      setReportLoading(false);
    }
  };

  // Si le commentaire est masqué, afficher le message de modération
  if (comment.comment === 'Ce commentaire à été modéré') {
    return (
      <Box sx={{ mt: 1.5, background: '#FFF8F3', border: '1px solid #FF6B2C', borderRadius: 2, p: 2, mb: 2 }}>
        <Typography sx={{ color: '#FF6B2C', fontWeight: 600 }}>
        Ce commentaire à été modéré
        </Typography>
      </Box>
    );
  }

  const handleReplyClick = () => {
    // Force le reset puis réactive pour forcer le re-render
    setReplyingTo(null);
    setTimeout(() => {
      setReplyingTo(comment.id);
    }, 10);
  };

  // Vérifier si le commentaire peut encore être modifié (moins de 1 minute)
  const canEdit = useMemo(() => {
    const commentDate = new Date(comment.created_at);
    const now = new Date();
    const diffInMinutes = (now.getTime() - commentDate.getTime()) / (1000 * 60);
    return comment.canEdit && diffInMinutes <= 1;
  }, [comment.created_at, comment.canEdit]);

  // Vérifier si le commentaire peut encore être supprimé (moins de 1 minute)
  const canDelete = useMemo(() => {
    const commentDate = new Date(comment.created_at);
    const now = new Date();
    const diffInMinutes = (now.getTime() - commentDate.getTime()) / (1000 * 60);
    return comment.canEdit && diffInMinutes <= 1;
  }, [comment.created_at, comment.canEdit]);

  return (
    <>
      <Box
        sx={{
          mt: 1.5,
          backgroundColor: 'white',
          padding: '12px 16px',
          borderRadius: '8px',
          border: '1px solid #FFE4BA',
          position: 'relative',
          mb: 2,
          '& .comment-content': {
            margin: '8px 0',
            color: '#2D3748',
            fontSize: '0.95rem',
            lineHeight: '1.5',
            letterSpacing: '0.01em',
            wordBreak: 'break-word'
          },
          '& ul, & ol': { margin: '0.5em 0', paddingLeft: '1.5em' },
          '& li': { margin: '0.25em 0' },
          '& strong': { fontWeight: 600 },
          '& em': { fontStyle: 'italic' },
          '& u': { textDecoration: 'underline' },
          '& .tiptap-align-center': { textAlign: 'center' },
          '& .tiptap-align-right': { textAlign: 'right' },
          '& .tiptap-align-justify': { textAlign: 'justify' },
          '& img.emoji': {
            height: '1.2em',
            width: '1.2em',
            margin: '0 0.1em',
            verticalAlign: '-0.2em'
          }
        }}
      >
        <Box className="comment-content" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(comment.comment) }} />
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 1,
          mt: 2,
          pt: 1.5,
          borderTop: '1px solid #FFE4BA'
        }}>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Button
              size="small"
              startIcon={<Reply size={16} />}
              onClick={handleReplyClick}
              sx={{ color: '#666' }}
            >
              Répondre
            </Button>
            {/* Désactiver le report si le commentaire est déjà modéré ou masqué automatiquement */}
            <Tooltip title="Signaler ce commentaire">
              <span>
                <IconButton size="small" onClick={() => setReportOpen(true)} sx={{ color: '#FF6B2C' }} disabled={comment.comment === 'Ce commentaire à été modéré' || comment.comment === 'Ce commentaire a été automatiquement masqué après plusieurs signalements. Il est en attente de modération.'}>
                  <ReportProblemIcon fontSize="small" />
                </IconButton>
              </span>
            </Tooltip>
            {canEdit && (
              <Tooltip title="Modifier (disponible pendant 1 minute)">
                <IconButton
                  size="small"
                  onClick={() => handleEdit(comment.id)}
                  sx={{ color: '#666' }}
                >
                  <Edit2 size={16} />
                </IconButton>
              </Tooltip>
            )}
            {canDelete && (
              <Tooltip title="Supprimer (disponible pendant 1 minute)">
                <IconButton
                  size="small"
                  onClick={() => handleDelete(comment.id)}
                  sx={{ color: '#666' }}
                >
                  <Trash2 size={16} />
                </IconButton>
              </Tooltip>
            )}
          </Box>
          <Box component="div" sx={{ fontSize: '0.85rem', color: '#666', display: 'flex', alignItems: 'center' }}>
            {formatDate(comment.created_at)}
            {comment.is_private && (
              <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 1 }}>
                <Lock size={12} />
                <Box component="span" sx={{ fontSize: 'inherit', color: '#FF6B2C', fontWeight: 500 }}>
                  Privé
                </Box>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      {reportOpen && (
        <ReportCommentModal open={reportOpen} onClose={() => setReportOpen(false)} onSubmit={handleReport} loading={reportLoading} />
      )}
    </>
  );
});

// Extraire le composant CommentStatusBar
const CommentStatusBar = React.memo(({
  isPrivate,
  setIsPrivate,
  remainingTime,
  onSubmit,
  disabled,
  setInfoDialogOpen,
  loading
}: {
  isPrivate: boolean;
  setIsPrivate: (value: boolean) => void;
  remainingTime: number;
  onSubmit: () => void;
  disabled: boolean;
  setInfoDialogOpen: (value: boolean) => void;
  loading?: boolean;
}) => (
  <div className="editor-status-bar">
    <div className="status-left">
      <div className="private-controls">
        <PrivateToggle
          className={isPrivate ? 'active' : ''}
          onClick={() => setIsPrivate(!isPrivate)}
        >
          <PrivateIcon active={isPrivate.toString()} />
          <PrivateLabel active={isPrivate.toString()}>
            {window.innerWidth < 600 ? 'Commentaire privé ?' : 'Commentaire privé ?'}
          </PrivateLabel>
        </PrivateToggle>
        <IconButton
          size="small"
          onClick={() => setInfoDialogOpen(true)}
          sx={{
            color: '#FF6B2C',
            bgcolor: '#FFF8F3',
            border: '1px solid #FFE4BA',
            padding: '4px',
            flexShrink: 0,
            '&:hover': { bgcolor: '#FFE4BA' }
          }}
        >
          <HelpCircle size={14} />
        </IconButton>
      </div>
    </div>
    <div className="status-right">
      <Tooltip
        title={
          remainingTime > 0
            ? `Veuillez patienter encore ${remainingTime} seconde${remainingTime > 1 ? 's' : ''}`
            : loading
              ? 'Vérification du contenu en cours...'
              : !disabled
                ? 'Écrivez quelque chose pour pouvoir envoyer'
                : 'Envoyer le commentaire'
        }
        placement="top"
      >
        <span>
          <Button
            className="send-button"
            onClick={onSubmit}
            disabled={disabled || remainingTime > 0}
            endIcon={loading ? <CircularProgress size={16} sx={{ color: 'white' }} /> : <Send className="send-icon" size={16} />}
          >
            {loading ? 'Modération en cours ...' : 'Envoyer'}
          </Button>
        </span>
      </Tooltip>
    </div>
  </div>
));

interface InfoDialogProps {
  open: boolean;
  onClose: () => void;
}

const InfoDialogContent = styled('div')({
  borderRadius: '16px',
  maxWidth: '90vw',
  margin: '16px',
  padding: '16px',
  backgroundColor: '#FFF8F3',
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  zIndex: 1100,
});

const InfoDialogTitle = styled('div')({
  color: '#FF6B2C',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  padding: '16px',
  borderBottom: '1px solid #FFE4BA',
});

const InfoDialogActions = styled('div')({
  padding: '16px',
  display: 'flex',
  justifyContent: 'flex-end',
  borderTop: '1px solid #FFE4BA',
});

const InfoDialog: React.FC<InfoDialogProps> = ({ open, onClose }) => {
  if (!open) return null;

  return (
    <ModalPortal onBackdropClick={onClose}>
      <InfoDialogContent onClick={e => e.stopPropagation()}>
        <InfoDialogTitle>
          <Lock size={20} />
          Commentaire privé
        </InfoDialogTitle>
        <Box sx={{ padding: '16px' }}>
        <Typography variant="body1" component="div" sx={{ mb: 2 }}>
            Veuillez respecter les règles de commentaire pour maintenir une communauté bienveillante.
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Un commentaire privé ne sera visible que par :
          </Typography>
          <ul style={{ margin: '0 0 16px 20px', padding: 0 }}>
            <li>Vous</li>
            <li>L'auteur de la mission</li>
            <li>Les modérateurs</li>
          </ul>
          <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#666' }}>
            Idéal pour les questions sensibles ou personnelles.
          </Typography>
          <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#718096' }}>
            Utilisez les commentaires privés avec discernement. Si votre commentaire peut être utile à d'autres utilisateurs, privilégiez un commentaire public.
          </Typography>
        </Box>
        <InfoDialogActions>
          <Button
            onClick={onClose}
            sx={{ color: '#FF6B2C' }}
          >
            Fermer
          </Button>
        </InfoDialogActions>
      </InfoDialogContent>
    </ModalPortal>
  );
};

const MissionHeader = styled(Box)({
  backgroundColor: '#FFF8F3', // Fond clair du thème, cohérent avec les commentaires
  borderRadius: '16px',
  padding: '24px',
  marginTop: '24px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)', // Ombre colorée subtile
  border: '1px solid #FFE4BA',
  marginBottom: '24px',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(255, 107, 44, 0.12)', // Effet subtil au survol
  },
});

const UserSection = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  marginBottom: '20px',
});

const MissionUserInfo = styled(Box)({
  flex: 1,
});

const UserName = styled(Typography)({
  fontWeight: 600,
  color: '#2D3748',
});

const UserChip = styled(Chip)({
  backgroundColor: '#FFF8F3',
  color: '#FF6B2C',
  border: '1px solid #FFE4BA',
  borderRadius: '8px', // Réduction du border-radius à 4px
  '& .MuiChip-icon': {
    color: '#FF6B2C',
  },
  '& .MuiChip-label': {
    fontWeight: 500,
  },
});

const MissionTitle = styled(Typography)({
  fontSize: '1.5rem',
  fontWeight: 600,
  color: '#2D3748',
  marginBottom: '12px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
});

const UrgentChip = styled(Chip)({
  backgroundColor: '#FFE4BA',
  color: '#FF6B2C',
  fontWeight: 500,
  '& .MuiChip-icon': {
    color: '#FF6B2C',
  },
});

const MissionDescription = styled(Typography)({
  color: '#4A5568',
  marginBottom: '20px',
  lineHeight: 1.6,
});

const MissionDetails = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '16px',
  padding: '12px',
  backgroundColor: 'white', // Fond blanc pour faire ressortir les détails
  borderRadius: '12px',
  border: '1px solid #FFE4BA',
});

const DetailItem = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: '#718096',
  padding: '8px 12px',
  backgroundColor: '#FFFCF8', // Fond légèrement différent pour chaque item
  borderRadius: '8px',
  border: '1px solid #FFE4BA',
  transition: 'all 0.2s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 2px 6px rgba(255, 107, 44, 0.1)',
  },
  '& svg': {
    color: '#FF6B2C',
  },
  '& p': {
    fontSize: '0.9rem',
    fontWeight: 500,
  },
});

// Bouton animé pour "Faire une offre"
const AnimatedOfferButton = styled(Button)({
  backgroundColor: '#FF6B2C',
  color: 'white',
  fontWeight: 'bold',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: '#FF7A35',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 8px rgba(255, 107, 44, 0.3)',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
    animation: 'shine 3s infinite',
  },
  '@keyframes shine': {
    '0%': {
      left: '-100%',
    },
    '20%': {
      left: '100%',
    },
    '100%': {
      left: '100%',
    }
  },
  animation: 'softPulse 3s infinite',
  '@keyframes softPulse': {
    '0%': {
      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0.4)',
    },
    '50%': {
      boxShadow: '0 0 0 5px rgba(255, 107, 44, 0)',
    },
    '100%': {
      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0)',
    },
  },
});

// Texte animé pour "Faire une offre"
const AnimatedOfferText = styled('span')({
  display: 'inline-block',
  animation: 'textPulse 3s infinite',
  '@keyframes textPulse': {
    '0%': {
      transform: 'scale(1)',
    },
    '50%': {
      transform: 'scale(1.02)',
    },
    '100%': {
      transform: 'scale(1)',
    },
  },
});

// Mise à jour de l'interface MissionViewProps
interface MissionViewProps {
  mission: Mission;
  isOwner?: boolean;
  onClose?: () => void;
  handleUserClick: (userId: string) => Promise<void>;
}

const MissionView = ({ mission, isOwner, onClose, handleUserClick }: MissionViewProps) => {
  const [userProposal, setUserProposal] = useState<any>(null);
  const [checkingProposal, setCheckingProposal] = useState(false);
  const [showProposalDetailsModal, setShowProposalDetailsModal] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true); // État pour suivre si la section est repliée
  const isOffresPage = window.location.pathname.includes('/dashboard/missions/offres');

  // Vérifier si l'utilisateur a déjà fait une offre pour cette mission
  useEffect(() => {
    const checkUserProposal = async () => {
      if (!isOwner && mission.id) {
        setCheckingProposal(true);
        try {
          const proposal = await missionsApi.getUserProposalForMission(mission.id);
          setUserProposal(proposal);
        } catch (error) {
          logger.error("Erreur lors de la vérification de l'offre:", error);
          setUserProposal(null);
        } finally {
          setCheckingProposal(false);
        }
      }
    };

    checkUserProposal();
  }, [mission, isOwner]);

  // Fonction pour formater la date
  const formatDate = (date: string, timeSlots: Mission['time_slots']) => {
    if (!date || date === 'à définir') return 'Date à définir';

    if (timeSlots && timeSlots.length > 0) {
      const formattedDate = format(new Date(timeSlots[0].date), 'dd MMMM yyyy', { locale: fr });
      if (timeSlots.length === 1) {
        return `${formattedDate} - ${timeSlots[0].start} à ${timeSlots[0].end}`;
      }
      return `${formattedDate} (+ ${timeSlots.length} créneaux)`;
    }

    return format(new Date(date), 'dd MMMM yyyy', { locale: fr });
  };

  // Fonction pour formater le budget
  const formatBudget = (budget: number, budgetDefini: boolean) => {
    if (!budgetDefini) return 'Budget à définir';
    return `${budget} €`;
  };

  // Fonction pour formater le montant
  const formatAmount = (amount: number) => {
    return `${amount.toLocaleString('fr-FR')} €`;
  };

  // Fonction pour formater la date de proposition
  const formatProposalDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
  };

  // Fonction pour obtenir le libellé du statut de la proposition
  const getProposalStatusLabel = (status: string) => {
    switch (status) {
      case 'en_attente':
        return 'En attente';
      case 'acceptée':
        return 'Acceptée';
      case 'refusée':
        return 'Refusée';
      case 'contre_offre':
        return 'Contre-offre reçue';
      case 'contre_offre_jobbeur':
        return 'Contre-offre envoyée';
      default:
        return status;
    }
  };

  // Obtenir la couleur du statut
  const getProposalStatusColor = (status: string) => {
    switch (status) {
      case 'en_attente':
        return '#FFA500'; // Orange
      case 'acceptée':
        return '#4CAF50'; // Vert
      case 'refusée':
        return '#F44336'; // Rouge
      case 'contre_offre':
      case 'contre_offre_jobbeur':
        return '#2196F3'; // Bleu
      default:
        return '#757575'; // Gris
    }
  };

  // Styles pour l'animation de repli/dépli
  const collapsibleContentStyle = {
    overflow: 'hidden',
    maxHeight: isCollapsed ? '0px' : '2000px', // Valeur suffisamment grande pour contenir tout le contenu
    transition: 'max-height 0.3s ease-in-out',
    opacity: isCollapsed ? 0 : 1,
    visibility: isCollapsed ? 'hidden' : 'visible',
    marginTop: isCollapsed ? 0 : '16px',
  };

  // Style pour le bouton de repli/dépli
  const toggleButtonStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    padding: '8px 16px',
    backgroundColor: '#FFF8F3',
    border: '1px solid #FFE4BA',
    borderRadius: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    marginBottom: '8px',
    '&:hover': {
      backgroundColor: '#FFE4BA',
    }
  };

  return (
    <MissionHeader>
      {/* Bouton de repli/dépli */}
      <Box
        sx={toggleButtonStyle}
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <UserAvatar
            src={mission.user_profile?.photo_url || undefined}
            alt={`${mission.user_profile?.prenom || ''} ${mission.user_profile?.nom?.charAt(0) || ''}`}
            sx={{ width: 32, height: 32 }}
          >
            {!mission.user_profile?.photo_url && `${mission.user_profile?.prenom?.charAt(0) || ''}${mission.user_profile?.nom?.charAt(0) || ''}`}
          </UserAvatar>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
            {mission.titre}
          </Typography>
          {mission.is_urgent && (
            <UrgentChip
              icon={<CircleAlert size={14} />}
              label="Urgent"
              size="small"
              sx={{ height: 24 }}
            />
          )}
        </Box>
        <IconButton
          size="small"
          sx={{
            transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)',
            transition: 'transform 0.3s ease'
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </IconButton>
      </Box>

      {/* Contenu repliable */}
      <Box sx={collapsibleContentStyle}>
        <UserSection>
          <Box sx={{ position: 'relative' }}>
            <Box onClick={() => handleUserClick(mission.user_id)} sx={{ cursor: 'pointer' }}>
              <UserAvatar
                src={mission.user_profile?.photo_url || undefined}
                alt={`${mission.user_profile?.prenom || ''} ${mission.user_profile?.nom?.charAt(0) || ''}`}
                sx={{
                  '&:hover': {
                    opacity: 0.8,
                    transform: 'scale(1.05)',
                    transition: 'transform 0.2s ease'
                  }
                }}
              >
                {!mission.user_profile?.photo_url && `${mission.user_profile?.prenom?.charAt(0) || ''}${mission.user_profile?.nom?.charAt(0) || ''}`}
              </UserAvatar>
            </Box>
            <Box sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 1,
              transform: 'scale(0.8)'
            }}>
              <OnlineStatusDot userId={mission.user_id} />
            </Box>
          </Box>
          <MissionUserInfo>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UserName
                variant="subtitle1"
                onClick={() => handleUserClick(mission.user_id)}
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    color: '#FF6B2C',
                    textDecoration: 'underline'
                  }
                }}
              >
                {`${mission.user_profile?.prenom || ''} ${mission.user_profile?.nom?.charAt(0) || '.'}.`}
              </UserName>
              <UserChip
                icon={<Building2 size={12} />}
                label={mission.user_profile?.type_de_profil === 'professionnel' ? 'Pro' : 'Particulier'}
              />
            </Box>
          </MissionUserInfo>
        </UserSection>

        <Box>
          <MissionTitle>
            {mission.titre}
            {mission.is_urgent && (
              <UrgentChip
                icon={<CircleAlert size={16} />}
                label="Urgent"
                size="medium"
              />
            )}
          </MissionTitle>
          <MissionDescription dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(mission.description) }} />
        </Box>

        <MissionDetails>
          <DetailItem>
            <MapPin size={20} />
            <Typography>{`${mission.ville} (${mission.code_postal})`}</Typography>
          </DetailItem>
          <DetailItem>
            <Clock size={20} />
            <Typography>{formatDate(mission.date_mission, mission.time_slots)}</Typography>
          </DetailItem>
          <DetailItem>
            <Euro size={20} />
            <Typography>{formatBudget(mission.budget, mission.budget_defini)}</Typography>
          </DetailItem>
        </MissionDetails>

        {/* Bouton "Faire une offre" pour les non-propriétaires */}
        {!isOwner && !isOffresPage && (
          <Box sx={{ mt: 2 }}>
            <AnimatedOfferButton
              onClick={() => {
                if (userProposal) {
                  // Si l'utilisateur a déjà fait une offre, ouvrir la modal des détails
                  setShowProposalDetailsModal(true);
                } else if (onClose) {
                  // Sinon, fermer la modal des commentaires et ouvrir le modal de proposition
                  onClose();
                  setTimeout(() => {
                    const event = new CustomEvent('open-proposal-modal', {
                      detail: { missionId: mission.id }
                    });
                    window.dispatchEvent(event);
                  }, 100);
                }
              }}
            >
              {checkingProposal ? (
                <CircularProgress size={20} sx={{ color: 'white', marginRight: '8px' }} />
              ) : (
                userProposal ? (
                  <Eye size={20} color="white" style={{ marginRight: '8px' }}/>
                ) : (
                  <Hand size={20} color="white" style={{ marginRight: '8px' }}/>
                )
              )}
              <AnimatedOfferText>
                {userProposal
                  ? "Voir l'offre faite"
                  : `Faire une offre ${typeof mission.applications_count === 'number' && mission.applications_count > 0 ? `(${mission.applications_count} reçue${mission.applications_count > 1 ? 's' : ''})` : ''}`
                }
              </AnimatedOfferText>
            </AnimatedOfferButton>
          </Box>
        )}
      </Box>

      {/* Modal pour afficher les détails de l'offre */}
      {showProposalDetailsModal && (
        <ModalPortal>
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1300,
            }}
            onClick={() => setShowProposalDetailsModal(false)}
          >
            <Box
              sx={{
                backgroundColor: 'white',
                borderRadius: '16px',
                width: '90%',
                maxWidth: '800px',
                maxHeight: '90vh',
                overflow: 'auto',
                position: 'relative',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* En-tête de la modal */}
              <Box
                sx={{
                  p: 2,
                  borderBottom: '1px solid #FFE4BA',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  backgroundColor: '#FFF8F3',
                  borderRadius: '16px 16px 0 0',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Hand size={24} color="#FF6B2C" />
                  <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 'bold' }}>
                    Détails de votre offre
                  </Typography>
                </Box>
                <IconButton
                  onClick={() => setShowProposalDetailsModal(false)}
                  sx={{
                    color: '#718096',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                      color: '#FF6B2C',
                    },
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Contenu de la modal */}
              <Box sx={{ p: 3 }}>
                {userProposal && (
                  <Box sx={{
                    width: '100%'
                  }}>
                    {/* Statut de l'offre mis en évidence en haut */}
                    <Paper
                      elevation={3}
                      sx={{
                        mb: 3,
                        p: 1,
                        borderRadius: '12px',
                        backgroundColor: getProposalStatusColor(userProposal.statut),
                        color: 'white',
                        textAlign: 'center',
                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 2
                      }}
                    >
                      {userProposal.statut === 'en_attente' && <Clock size={28} strokeWidth={2.5} />}
                      {userProposal.statut === 'acceptée' && <CheckCircle2 size={28} strokeWidth={2.5} />}
                      {userProposal.statut === 'refusée' && <AlertCircle size={28} strokeWidth={2.5} />}
                      {(userProposal.statut === 'contre_offre' || userProposal.statut === 'contre_offre_jobbeur') && <ArrowRightLeft size={28} strokeWidth={2.5} />}

                      <Typography variant="h6" sx={{ fontWeight: 'bold', letterSpacing: '0.5px', fontSize: '1.1rem' }}>
                        Statut : {getProposalStatusLabel(userProposal.statut)}
                      </Typography>
                    </Paper>

                    <Paper elevation={0} sx={{
                      p: 3,
                      mb: 3,
                      borderRadius: '12px',
                      border: '1px solid #FFE4BA',
                      backgroundColor: '#FFF8F3'
                    }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                          Mission : {mission.titre}
                        </Typography>
                      </Box>

                      <Divider sx={{ my: 2 }} />

                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body1" sx={{ color: '#666' }}>
                            Montant proposé :
                          </Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
                            {formatAmount(userProposal.montant_propose || 0)}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body1" sx={{ color: '#666' }}>
                            Date de l'offre :
                          </Typography>
                          <Typography variant="body1">
                            {userProposal.created_at ? formatProposalDate(userProposal.created_at) : '-'}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>

                    <Paper elevation={0} sx={{
                      p: 3,
                      borderRadius: '12px',
                      border: '1px solid #FFE4BA',
                      backgroundColor: '#FFF8F3'
                    }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333', mb: 2 }}>
                        Votre message
                      </Typography>

                      <Typography variant="body1" sx={{
                        whiteSpace: 'pre-wrap',
                        backgroundColor: 'white',
                        p: 2,
                        borderRadius: '8px',
                        border: '1px solid #E2E8F0'
                      }}>
                        {userProposal.message || "Aucun message"}
                      </Typography>
                    </Paper>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
                      <Button
                        variant="contained"
                        onClick={() => setShowProposalDetailsModal(false)}
                        sx={{
                          backgroundColor: '#FF6B2C',
                          '&:hover': {
                            backgroundColor: '#FF7A35',
                          },
                          borderRadius: '8px',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          px: 3
                        }}
                      >
                        Fermer
                      </Button>
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
        </ModalPortal>
      )}
    </MissionHeader>
  );
};

const WaitingMessage = styled(Box)({
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  borderRadius: '12px',
  padding: '16px',
  marginTop: '16px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  animation: 'pulse 2s infinite',
  '@keyframes pulse': {
    '0%': {
      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0.4)',
    },
    '70%': {
      boxShadow: '0 0 0 10px rgba(255, 107, 44, 0)',
    },
    '100%': {
      boxShadow: '0 0 0 0 rgba(255, 107, 44, 0)',
    },
  },
});

const WaitingIcon = styled(Clock)({
  color: '#FF6B2C',
});

const WaitingText = styled(Typography)({
  color: '#4A5568',
  fontSize: '0.975rem',
  lineHeight: 1.5,
});

// Extraire le composant d'édition dans un composant séparé
const EditCommentEditor = React.memo(({
  value,
  onChange,
  onSubmit,
  loading,
  originalText,
  isPrivate,
  setIsPrivate,
  setInfoDialogOpen,
  isEditing = false,
  mission,
  parentComment // nouvelle prop optionnelle
}: {
  value: string;
  onChange: (content: string) => void;
  onSubmit: () => void;
  loading: boolean;
  originalText: string;
  isPrivate?: boolean;
  setIsPrivate?: (value: boolean) => void;
  setInfoDialogOpen?: (value: boolean) => void;
  isEditing?: boolean;
  mission: Mission;
  parentComment?: Comment;
}) => {
  const [localValue, setLocalValue] = useState(value || originalText);
  const editorRef = useRef<any>(null);
  const [showHelpDialog, setShowHelpDialog] = useState(false);
  const [aiLoading] = useState(false);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);

  useEffect(() => {
    setLocalValue(value || originalText);
  }, [value, originalText]);

  const commentLength = useMemo(() => stripHtml(localValue).length, [localValue]);
  const hasChanges = useMemo(() => localValue !== originalText, [localValue, originalText]);
  const isDisabled = useMemo(() =>
    !stripHtml(localValue).trim() ||
    commentLength > 150 ||
    (isEditing && !hasChanges) ||
    loading,
  [localValue, commentLength, hasChanges, isEditing, loading]);

  const handleChange = useCallback((content: string) => {
    const sanitizedContent = DOMPurify.sanitize(content);
    const textLength = stripHtml(sanitizedContent).length;

    if (textLength <= 150) {
      setLocalValue(sanitizedContent);
      onChange(sanitizedContent);
    } else {
      notify('Le commentaire ne peut pas dépasser 150 caractères', 'warning');
    }
  }, [onChange]);

  const handleSubmitClick = useCallback(() => {
    if (!isDisabled) {
      onSubmit();
    }
  }, [isDisabled, onSubmit]);

  // Fonction pour ouvrir la modale de confirmation d'utilisation de l'IA
  const handleAiImprovement = () => {
    setIsAiConfirmModalOpen(true);
  };

  return (
    <>
      <CommentInputContainer>
        <div className="editor-wrapper">
          <EditorHelpIcon
            size="small"
            onClick={() => setShowHelpDialog(true)}
            aria-label="Aide"
          >
            <HelpCircle size={16} />
          </EditorHelpIcon>

          {showHelpDialog && (
            <ModalPortal onBackdropClick={() => setShowHelpDialog(false)}>
              <EditorHelpDialog onClick={(e: React.MouseEvent) => e.stopPropagation()}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <HelpCircle size={20} color="#FF6B2C" />
                  <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 500 }}>
                    Règles de commentaire
                  </Typography>
                </Box>
                <Typography variant="body1" component="div" sx={{ whiteSpace: 'pre-line', mb: 3 }}>
                  {FULL_PLACEHOLDER}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    onClick={() => setShowHelpDialog(false)}
                    sx={{ color: '#FF6B2C' }}
                  >
                    Fermer
                  </Button>
                </Box>
              </EditorHelpDialog>
            </ModalPortal>
          )}

          <TiptapEditor
            ref={editorRef}
            content={localValue}
            onChange={handleChange}
            className="min-h-[150px]"
            placeholder={SHORT_PLACEHOLDER}
          />
          <div className={`char-counter ${
            commentLength >= 150 ? 'error' :
            commentLength >= 120 ? 'warning' : ''
          }`}>
            <span>{commentLength}</span>
            <span>/</span>
            <span>150</span>
          </div>
        </div>
        {!isEditing && setIsPrivate && setInfoDialogOpen ? (
          <div className="editor-status-bar">
            <div className="status-left">
              <Tooltip title={stripHtml(localValue).trim() ? "Améliorer avec l'IA" : "Générer avec l'IA"}>
                <span>
                  <Button
                    onClick={handleAiImprovement}
                    disabled={false}
                    sx={{
                      backgroundColor: '#FFE4BA',
                      color: '#FF6B2C',
                      padding: '6px 12px',
                      borderRadius: '8px',
                      fontSize: '0.85rem',
                      textTransform: 'none',
                      '&:hover': {
                        backgroundColor: '#FFF8F3',
                      },
                      '&:disabled': {
                        backgroundColor: '#f5f5f5',
                        color: '#bdbdbd',
                      }
                    }}
                    startIcon={
                      <Sparkles className="h-4 w-4 text-[#FF6B2C]" />
                    }
                  >
                    {stripHtml(localValue).trim()
                      ? "Améliorer avec l'IA"
                      : "Générer avec l'IA"
                    }
                  </Button>
                </span>
              </Tooltip>
            </div>
            <CommentStatusBar
              isPrivate={isPrivate || false}
              setIsPrivate={setIsPrivate}
              remainingTime={0}
              onSubmit={handleSubmitClick}
              disabled={isDisabled}
              setInfoDialogOpen={setInfoDialogOpen}
              loading={loading}
            />
          </div>
        ) : (
          <div className="editor-status-bar">
            <div className="status-left">
              <Tooltip title={stripHtml(localValue).trim() ? "Améliorer avec l'IA" : "Générer avec l'IA"}>
                <span>
                  <Button
                    onClick={handleAiImprovement}
                    disabled={false}
                    sx={{
                      backgroundColor: '#FFE4BA',
                      color: '#FF6B2C',
                      padding: '6px 12px',
                      borderRadius: '8px',
                      fontSize: '0.85rem',
                      textTransform: 'none',
                      '&:hover': {
                        backgroundColor: '#FFF8F3',
                      },
                      '&:disabled': {
                        backgroundColor: '#f5f5f5',
                        color: '#bdbdbd',
                      }
                    }}
                    startIcon={
                      aiLoading ? (
                        <CircularProgress size={16} sx={{ color: '#FF6B2C' }} />
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3Z" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 12L11 15L16 10" stroke="#FF6B2C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )
                    }
                  >
                    {aiLoading
                      ? "Génération en cours..."
                      : stripHtml(localValue).trim()
                        ? "Améliorer avec l'IA"
                        : "Générer avec l'IA"
                    }
                  </Button>
                </span>
              </Tooltip>
            </div>
            <div className="status-right">
              <Tooltip
                title={
                  !hasChanges
                    ? 'Aucune modification n\'a été effectuée'
                    : loading
                      ? 'Vérification du contenu en cours...'
                      : isDisabled
                        ? 'Le commentaire ne peut pas dépasser 150 caractères'
                        : 'Enregistrer les modifications'
                }
                placement="top"
              >
                <span>
                  <Button
                    className="send-button"
                    onClick={handleSubmitClick}
                    disabled={isDisabled}
                    endIcon={loading ? <CircularProgress size={16} sx={{ color: 'white' }} /> : <Send className="send-icon" size={16} />}
                  >
                    {loading ? 'Vérification...' : isEditing ? 'Enregistrer' : 'Envoyer'}
                  </Button>
                </span>
              </Tooltip>
            </div>
          </div>
        )}
      </CommentInputContainer>

      {/* Système de génération IA pour le commentaire */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="comment"
          prompt={parentComment
            ? `Contexte: Réponse à un commentaire sur une mission
                - Titre de la mission : ${mission.titre}
                - Description de la mission : ${stripHtml(mission.description)}
                - Commentaire auquel répondre : ${stripHtml(parentComment.comment)}
                - Tu es l'auteur de la réponse. Lis attentivement le commentaire ci-dessus et réponds-y de façon professionnelle, constructive et interrogative. Apporte une information nouvelle, utile ou une clarification. Ne répète pas la question du commentaire parent. Ta réponse doit faire entre 50 et 120 caractères, ne doit contenir ni guillemets ni balises HTML, et doit se terminer par une question.
                - Contraintes: Respecte strictement la longueur (50 à 120 caractères), pas de guillemets, pas de balises HTML, termine toujours par une question.`
            : stripHtml(localValue).trim().length > 0
              ? `Contexte: Commentaire sur une mission
                - Titre de la mission : ${mission.titre}
                - Description de la mission : ${stripHtml(mission.description)}
                - Action: Améliorer le texte existant
                - Texte à améliorer: ${stripHtml(localValue).trim()}
                - Consigne: Améliore ce commentaire pour le rendre plus professionnel, constructif et interrogatif, tout en gardant le même sens. Le texte final doit être entre 50 et 120 caractères, ne doit contenir ni guillemets ni balises HTML, et doit se terminer par une question.
                - Contraintes: Respecte strictement la longueur (50 à 120 caractères), pas de guillemets, pas de balises HTML, termine toujours par une question.`
              : `Contexte: Commentaire sur une mission
                - Titre de la mission : ${mission.titre}
                - Description de la mission : ${stripHtml(mission.description)}
                - Action: Générer un nouveau commentaire
                - Consigne: Génère un commentaire professionnel, constructif et interrogatif pour une mission. Le texte final doit être entre 50 et 120 caractères, ne doit contenir ni guillemets ni balises HTML, et doit se terminer par une question.
                - Contraintes: Respecte strictement la longueur (50 à 120 caractères), pas de guillemets, pas de balises HTML, termine toujours par une question.`}
          originalPrompt={stripHtml(localValue).trim().length > 0 ? stripHtml(localValue).trim() : undefined}
          onComplete={(content: string | null) => {
            setIsAiConfirmModalOpen(false);
            if (content) {
              // Mettre à jour le contenu de l'éditeur avec le contenu généré
              setLocalValue(content);
              onChange(content);
              // Forcer la mise à jour de l'éditeur
              if (editorRef.current) {
                const editor = editorRef.current.getEditor();
                if (editor) {
                  editor.commands.setContent(content);
                }
              }
              // Notification de succès
              notify(
                stripHtml(localValue).trim().length > 0
                  ? `${parentComment ? 'Réponse' : 'Commentaire'} amélioré avec succès par l'IA`
                  : `${parentComment ? 'Réponse' : 'Commentaire'} généré avec succès par l'IA`,
                'success'
              );
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}
    </>
  );
});

// Extraire la liste des commentaires dans un composant séparé
const CommentsList = React.memo(({
  comments,
  onEdit,
  onDelete,
  onReply,
  editingComment,
  handleEdit,
  replyingTo,
  newComment,
  handleEditorChange,
  handleSubmit,
  loading,
  remainingTime,
  isPrivate,
  setIsPrivate,
  setInfoDialogOpen,
  setEditingComment,
  setReplyingTo,
  handleUserClick,
  mission
}: {
  comments: Comment[];
  onEdit: (id: string, text: string) => void;
  onDelete: (id: string) => void;
  onReply: (id: string | null) => void;
  editingComment: string | null;
  editText: string;
  setEditText: (text: string) => void;
  handleEdit: (id: string, text: string) => void;
  replyingTo: string | null;
  newComment: string;
  handleEditorChange: (content: string) => void;
  handleSubmit: () => void;
  loading: boolean;
  remainingTime: number;
  isPrivate: boolean;
  setIsPrivate: (value: boolean) => void;
  setInfoDialogOpen: (value: boolean) => void;
  setEditingComment: (id: string | null) => void;
  setReplyingTo: (id: string | null) => void;
  handleUserClick: (userId: string) => Promise<void>;
  mission: Mission;
}) => {
  return (
    <AnimatePresence>
      {comments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          isReply={false}
          onEdit={onEdit}
          onDelete={onDelete}
          onReply={onReply}
          handleEdit={handleEdit}
          replyingTo={replyingTo}
          newComment={newComment}
          handleEditorChange={handleEditorChange}
          handleSubmit={handleSubmit}
          loading={loading}
          remainingTime={remainingTime}
          isPrivate={isPrivate}
          setIsPrivate={setIsPrivate}
          setInfoDialogOpen={setInfoDialogOpen}
          setEditingComment={setEditingComment}
          setReplyingTo={setReplyingTo}
          editingComment={editingComment}
          handleUserClick={handleUserClick}
          mission={mission}
        />
      ))}
    </AnimatePresence>
  );
}, (prevProps, nextProps) => {
  // Optimisation des re-rendus avec une fonction de comparaison personnalisée
  if (prevProps.comments.length !== nextProps.comments.length) return false;

  // Ne re-rendre que si les commentaires en cours d'édition ou de réponse changent
  return (
    prevProps.editingComment === nextProps.editingComment &&
    prevProps.replyingTo === nextProps.replyingTo &&
    (prevProps.editingComment ? prevProps.editText === nextProps.editText : true) &&
    (prevProps.replyingTo ? prevProps.newComment === nextProps.newComment : true)
  );
});

// Ajouter les interfaces et composants manquants
interface CommentItemProps {
  comment: Comment;
  isReply: boolean;
  onEdit: (id: string, text: string) => void;
  onDelete: (id: string) => void;
  onReply: (id: string | null) => void;
  handleEdit: (id: string, text: string) => void;
  replyingTo: string | null;
  newComment: string;
  handleEditorChange: (content: string) => void;
  handleSubmit: () => void;
  loading: boolean;
  remainingTime: number;
  isPrivate: boolean;
  setIsPrivate: (value: boolean) => void;
  setInfoDialogOpen: (value: boolean) => void;
  setEditingComment: (id: string | null) => void;
  setReplyingTo: (id: string | null) => void;
  editingComment: string | null;
  handleUserClick: (userId: string) => Promise<void>;
  mission: Mission;
}

// Composant pour le conteneur du commentaire directement dans le composant CommentItem (système de notification)
interface CommentContainerProps {
  transition?: {
    duration: number;
  };
}

// Composant pour le conteneur du commentaire directement dans le composant CommentItem (système de notification)
const CommentContainer = styled(Box)<CommentContainerProps>(() => ({
  position: 'relative',
  marginBottom: '16px',
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  borderRadius: '8px',
  padding: '16px',
  marginLeft: '0',
  transition: 'all 0.3s ease',
  boxShadow: '0 2px 4px rgba(255, 107, 44, 0.05)',

  '&[data-is-highlighted="true"]': {
    backgroundColor: '#FFF8F3',
    border: '2px solid #FF6B2C'
  },

  '&[data-is-reply="true"]': {
    marginLeft: '32px',
    marginBottom: '32px',
    marginTop: '32px',
    backgroundColor: '#FFFCF8',
    borderLeft: '3px solid #FF965E',
    padding: '24px 16px'
  },

  '& .ProseMirror': {
    minHeight: '150px',
    outline: 'none',
    padding: '1rem',
    '& p': {
      margin: '0.5em 0',
    },
    '& ul, & ol': {
      padding: '0 1rem',
    },
    '& code': {
      backgroundColor: '#f1f1f1',
      padding: '0.2em 0.4em',
      borderRadius: '3px',
    },
    '& pre': {
      backgroundColor: '#f1f1f1',
      padding: '0.75rem 1rem',
      borderRadius: '0.5rem',
    },
    '& blockquote': {
      borderLeft: '3px solid #FF6B2C',
      paddingLeft: '1rem',
      color: '#666',
      margin: '1rem 0',
    },
    '& p.is-editor-empty:first-of-type::before': {
      content: 'attr(data-placeholder)',
      float: 'left',
      color: '#adb5bd',
      pointerEvents: 'none',
      height: 0,
    }
  }
}));

const CommentItem = React.memo(({
  comment,
  isReply,
  onEdit,
  onDelete,
  onReply,
  handleEdit,
  replyingTo,
  newComment,
  handleEditorChange,
  handleSubmit,
  loading,
  remainingTime,
  isPrivate,
  setIsPrivate,
  setInfoDialogOpen,
  setEditingComment,
  setReplyingTo,
  editingComment,
  handleUserClick,
  mission
}: CommentItemProps): React.ReactElement => {
  const [editingText, setEditingText] = useState('');
  const [isDeleted, setIsDeleted] = useState(false);
  const [searchParams] = useSearchParams();
  const highlightedCommentId = searchParams.get('commentId');
  const commentRef = useRef<HTMLDivElement>(null);
  const replyEditorRef = useRef<HTMLDivElement>(null);
  const isHighlighted = comment.id === highlightedCommentId;

  useEffect(() => {
    if (isHighlighted && commentRef.current) {
      setTimeout(() => {
        commentRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
    }
  }, [isHighlighted]);

  useEffect(() => {
    if (replyingTo === comment.id && replyEditorRef.current) {
      replyEditorRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [replyingTo]);

  const handleDeleteWithAnimation = async (commentId: string) => {
    setIsDeleted(true);
    // Attendre la fin de l'animation avant de supprimer réellement
    setTimeout(() => {
      onDelete(commentId);
    }, 300); // Durée de l'animation
  };

  const handleProfileClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (comment.user) {
      handleUserClick(comment.user.id);
    }
  };

  if (isDeleted) {
    return (
      <CommentContainer
        key={comment.id}
        data-is-reply={isReply ? "true" : "false"}
        data-is-highlighted={isHighlighted ? "true" : "false"}
        transition={{ duration: 0.3 }}
      />
    );
  }

  return (
    <>
      <CommentContainer
        ref={commentRef}
        key={comment.id}
        data-is-reply={isReply ? "true" : "false"}
        data-is-highlighted={isHighlighted ? "true" : "false"}
        transition={{ duration: 0.3 }}
      >
        <CommentUserInfo>
          <Box sx={{ position: 'relative' }}>
            <Tooltip title="Voir le profil" arrow>
              <Box onClick={handleProfileClick} sx={{ cursor: 'pointer' }}>
                <Box sx={{ position: 'relative' }}>
                  <UserAvatar
                    src={comment.user?.user_profil?.[0].photo_url || undefined}
                    alt={`${comment.user?.user_profil?.[0].prenom || ''} ${comment.user?.user_profil?.[0].nom?.charAt(0) || ''}`}
                    sx={{
                      '&:hover': {
                        opacity: 0.8,
                        transform: 'scale(1.05)',
                        transition: 'transform 0.2s ease'
                      }
                    }}
                  >
                    {!comment.user?.user_profil?.[0].photo_url && `${comment.user?.user_profil?.[0].prenom?.charAt(0) || ''}${comment.user?.user_profil?.[0].nom?.charAt(0) || ''}`}
                  </UserAvatar>
                  <Box sx={{ position: 'absolute', top: 0, right: 0, transform: 'translate(0, 0) scale(0.8)' }}>
                    <OnlineStatusDot userId={comment.user.id} />
                  </Box>
                </Box>
              </Box>
            </Tooltip>
            {comment.user?.profil_verifier && (
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -2,
                  left: -2,
                  backgroundColor: 'white',
                  borderRadius: '50%',
                  padding: '2px',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                }}
              >
                <BadgeCheck size={14} className="text-emerald-500" />
              </Box>
            )}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              <Typography
                variant="subtitle2"
                onClick={() => handleUserClick(comment.user.id)}
                sx={{
                  fontWeight: 'bold',
                  color: '#2D3748',
                  cursor: 'pointer',
                  '&:hover': {
                    color: '#FF6B2C'
                  }
                }}
              >
                {`${comment.user?.user_profil?.[0].prenom || ''} ${comment.user?.user_profil?.[0].nom?.charAt(0) || ''}.`}
              </Typography>
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 1, sm: 2 },
                alignItems: { xs: 'flex-start', sm: 'center' }
              }}>
                {comment.user?.user_profil?.[0].type_de_profil && (
                  <Chip
                    size="small"
                    label={comment.user?.user_profil?.[0].type_de_profil === 'particulier' ? 'Particulier' : 'Professionnel'}
                    sx={{
                      backgroundColor: comment.user?.user_profil?.[0].type_de_profil === 'particulier' ? '#FFF8F3' : '#FFE4BA',
                      color: '#FF6B2C',
                      fontSize: '0.75rem',
                      height: '20px',
                      border: '1px solid #FFE4BA',
                      '& .MuiChip-label': {
                        padding: '0 8px',
                        fontWeight: 500,
                      },
                    }}
                  />
                )}
                {comment.user?.services?.some(service => service.statut === 'actif') && (
                  <Chip
                    size="small"
                    label="Propose des services"
                    sx={{
                      backgroundColor: '#FFF8F3',
                      color: '#FF6B2C',
                      fontSize: '0.75rem',
                      height: '20px',
                      border: '1px solid #FFE4BA',
                      '& .MuiChip-label': {
                        padding: '0 8px',
                        fontWeight: 500,
                      },
                    }}
                  />
                )}
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
              {comment.user?.user_profil?.[0].type_de_profil === 'professionnel' && comment.user?.user_profil?.[0].statut_entreprise && (
                <Typography
                  variant="caption"
                  component="div"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    color: '#718096',
                  }}
                >
                  <Building2 size={12} />
                  {comment.user?.user_profil?.[0].statut_entreprise}
                </Typography>
              )}
            </Box>
          </Box>
        </CommentUserInfo>

        {editingComment === comment.id ? (
          <Box sx={{ mt: 2 }}>
            <EditCommentEditor
              value={editingText}
              onChange={setEditingText}
              onSubmit={() => handleEdit(comment.id, editingText)}
              loading={loading}
              originalText={comment.comment}
              isPrivate={isPrivate}
              setIsPrivate={setIsPrivate}
              setInfoDialogOpen={setInfoDialogOpen}
              isEditing={true}
              mission={mission}
              parentComment={comment} // passage du commentaire parent
            />
            <CommentActions>
              <Button
                size="small"
                onClick={() => {
                  setEditingText('');
                  setEditingComment(null);
                }}
                sx={{ color: '#666' }}
              >
                Annuler
              </Button>
            </CommentActions>
          </Box>
        ) : (
          <CommentContent
            comment={comment}
            handleEdit={(id) => {
              setEditingText(comment.comment);
              setEditingComment(id);
            }}
            handleDelete={handleDeleteWithAnimation}
            setReplyingTo={onReply}
            isReply={isReply}
          />
        )}

        {/* Zone de réponse immédiatement après le commentaire */}
        {replyingTo === comment.id && (
          <Box ref={replyEditorRef} sx={{ mt: 2 }}>
            <ReplyEditor
              comment={comment}
              newComment={newComment}
              handleEditorChange={handleEditorChange}
              handleSubmit={handleSubmit}
              mission={mission}
              setInfoDialogOpen={setInfoDialogOpen}
              missionId={mission.id}
            />
          </Box>
        )}

        {/* Afficher les réponses après */}
        {!isReply && comment.replies && comment.replies.map(reply => (
          <CommentItem
            key={reply.id}
            comment={reply}
            isReply={true}
            onEdit={onEdit}
            onDelete={onDelete}
            onReply={onReply}
            handleEdit={handleEdit}
            replyingTo={replyingTo}
            newComment={newComment}
            handleEditorChange={handleEditorChange}
            handleSubmit={handleSubmit}
            loading={loading}
            remainingTime={remainingTime}
            isPrivate={isPrivate}
            setIsPrivate={setIsPrivate}
            setInfoDialogOpen={setInfoDialogOpen}
            setEditingComment={setEditingComment}
            setReplyingTo={setReplyingTo}
            editingComment={editingComment}
            handleUserClick={handleUserClick}
            mission={mission}
          />
        ))}
      </CommentContainer>
    </>
  );
});

// Composant ReplyEditor avec son propre état de loading
const ReplyEditor = React.memo(({
  comment,
  newComment,
  handleEditorChange,
  handleSubmit,
  mission,
  setInfoDialogOpen,
  missionId
}: {
  comment: Comment;
  newComment: string;
  handleEditorChange: (content: string) => void;
  handleSubmit: () => void;
  mission: Mission;
  setInfoDialogOpen: (value: boolean) => void;
  missionId: string;
}) => {
  const [replyLoading, setReplyLoading] = useState(false);
  const [replyPrivate, setReplyPrivate] = useState(false);
  const [showReplyPrivateConfirm, setShowReplyPrivateConfirm] = useState(false);
  const [replyRemainingTime, setReplyRemainingTime] = useState(0);
  const { validateContentSafety } = useContentModeration();

  // Version optimisée de handleEditorChange avec debounce
  const debouncedHandleEditorChange = useMemo(
    () => debounce((content: string) => {
      handleEditorChange(content);
    }, 100), // 100ms de debounce pour améliorer les performances
    [handleEditorChange]
  );

  // Fonctions de gestion du cooldown (copiées du composant principal)
  const cleanExpiredCooldowns = useCallback(() => {
    const cooldowns = JSON.parse(getCookie('commentCooldowns') || '[]');
    const now = new Date().getTime();
    const validCooldowns = cooldowns.filter((cd: any) => {
      return now - new Date(cd.timestamp).getTime() < 60000; // 60 secondes
    });

    if (validCooldowns.length !== cooldowns.length) {
      if (validCooldowns.length === 0) {
        setCookie('commentCooldowns', '', -1);
      } else {
        setCookie('commentCooldowns', JSON.stringify(validCooldowns), 60);
      }
    }
    return validCooldowns;
  }, []);



  // Fonction pour obtenir le temps restant
  const getRemainingTime = useCallback(() => {
    const validCooldowns = cleanExpiredCooldowns();
    const missionCooldown = validCooldowns.find((cd: any) => cd.missionId === missionId);

    if (!missionCooldown) return 0;

    const diffInSeconds = 60 - ((new Date().getTime() - new Date(missionCooldown.timestamp).getTime()) / 1000);
    return Math.max(0, Math.ceil(diffInSeconds));
  }, [missionId, cleanExpiredCooldowns]);

  // Timer pour mettre à jour le temps restant
  useEffect(() => {
    const checkRemainingTime = () => {
      setReplyRemainingTime(getRemainingTime());
    };

    const timer = setInterval(checkRemainingTime, 1000);
    checkRemainingTime();

    return () => clearInterval(timer);
  }, [getRemainingTime]);

  // Fonction pour ajouter un nouveau cooldown
  const setCommentCooldown = useCallback(() => {
    const validCooldowns = cleanExpiredCooldowns();
    const newCooldown = {
      missionId,
      timestamp: new Date().toISOString()
    };
    const updatedCooldowns = [...validCooldowns, newCooldown];
    setCookie('commentCooldowns', JSON.stringify(updatedCooldowns), 60);
  }, [missionId, cleanExpiredCooldowns]);

  const submitReplyComment = useCallback(async () => {
    try {
      const commentResponse = await missionsApi.commentMission(
        missionId,
        newComment,
        replyPrivate,
        comment.id
      );

      // S'assurer que les données utilisateur sont présentes et valides
      if (!commentResponse.user?.user_profil?.[0]) {
        throw new Error('Données utilisateur manquantes dans la réponse');
      }

      // Ajouter le cooldown après envoi réussi
      setCommentCooldown();

      // Appeler handleSubmit pour mettre à jour l'état parent
      handleSubmit();

      // Pas de notification - le parent gère déjà l'affichage
    } catch (error: any) {
      // Vérifier si l'erreur vient du rate limiter
      if (error.response?.data?.message) {
        notify(error.response.data.message, error.response.data.toastType || 'warning');
      } else {
        notify(error.message || 'Une erreur est survenue lors de l\'ajout de la réponse', 'error');
      }
      logger.error('Erreur lors de l\'ajout de la réponse:', error);
    }
  }, [missionId, newComment, replyPrivate, comment.id, handleSubmit, setCommentCooldown]);

  const handleReplySubmit = useCallback(async () => {
    if (!newComment.trim()) return;

    // Pas de vérification ici - le countdown est géré visuellement
    setReplyLoading(true);
    try {
      // Vérifier le contenu avec la modération
      const textContent = stripHtml(newComment);
      const isCommentSafe = await validateContentSafety(textContent, 'comment');

      if (!isCommentSafe) {
        return;
      }

      if (replyPrivate) {
        setShowReplyPrivateConfirm(true);
        return;
      }

      // Appeler directement l'API pour envoyer le commentaire
      await submitReplyComment();
    } finally {
      setReplyLoading(false);
    }
  }, [newComment, replyPrivate, validateContentSafety, submitReplyComment]);

  return (
    <>
      {replyRemainingTime > 0 ? (
        <WaitingMessage>
          <WaitingIcon size={24} />
          <WaitingText>
            {`Il vous reste ${replyRemainingTime} secondes avant de pouvoir commenter à nouveau. Pendant ce temps, vous pouvez consulter les autres commentaires, modifier ou supprimer votre commentaire.`}
          </WaitingText>
        </WaitingMessage>
      ) : (
        <EditCommentEditor
          value={newComment}
          onChange={debouncedHandleEditorChange}
          onSubmit={handleReplySubmit}
          loading={replyLoading}
          originalText=""
          isPrivate={replyPrivate}
          setIsPrivate={setReplyPrivate}
          setInfoDialogOpen={setInfoDialogOpen}
          mission={mission}
          parentComment={comment}
        />
      )}

      {/* Modal de confirmation pour commentaire privé de réponse */}
      <PrivateMessageConfirmation
        open={showReplyPrivateConfirm}
        onClose={() => setShowReplyPrivateConfirm(false)}
        onConfirm={async () => {
          setShowReplyPrivateConfirm(false);
          setReplyLoading(true);
          try {
            await submitReplyComment();
          } finally {
            setReplyLoading(false);
          }
        }}
      />
    </>
  );
});

// Ajouter le composant PrivateMessageConfirmation
const PrivateMessageConfirmation: React.FC<{
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}> = ({ open, onClose, onConfirm }) => {
  if (!open) return null;
  return (
    <ModalPortal onBackdropClick={onClose}>
      <PrivateConfirmContent onClick={e => e.stopPropagation()}>
        <PrivateConfirmTitle>
          <Lock size={24} />
          Confirmation
        </PrivateConfirmTitle>
        <Box sx={{ padding: '16px' }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Vous êtes sur le point d'envoyer un <strong>commentaire privé</strong>.
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Ce commentaire ne sera visible que par :
          </Typography>
          <ul style={{ margin: '0 0 16px 20px', padding: 0 }}>
            <li>Vous</li>
            <li>L'auteur de la mission</li>
            <li>Les modérateurs</li>
          </ul>
          <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#666' }}>
            Idéal pour les questions sensibles ou personnelles.
          </Typography>
        </Box>
        <PrivateConfirmActions>
          <Button
            onClick={onClose}
            sx={{ color: '#666' }}
          >
            Annuler
          </Button>
          <Button
            onClick={onConfirm}
            sx={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              '&:hover': {
                backgroundColor: '#FF7A35',
              }
            }}
          >
            Confirmer
          </Button>
        </PrivateConfirmActions>
      </PrivateConfirmContent>
    </ModalPortal>
  );
};

// Ajout du composant pour le modal de signalement
const ReportCommentModal = ({ open, onClose, onSubmit, loading }: { open: boolean; onClose: () => void; onSubmit: (reason: string) => void; loading: boolean }) => {
  const [reason, setReason] = useState('');
  const handleSubmit = () => {
    const sanitizedReason = DOMPurify.sanitize(reason);
    onSubmit(sanitizedReason);
  };
  const PREDEFINED_REASONS = [
    'Contient un numéro de téléphone',
    'Propos racistes ou discriminatoires',
    'Spam ou publicité',
    'Harcèlement ou intimidation',
    'Informations personnelles',
    'Langage inapproprié',
    'Autre',
  ];
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <Box sx={{ background: 'white', borderRadius: 2, p: 0, maxWidth: 400, mx: 'auto', boxShadow: 3, overflowY: 'auto', maxHeight: '80vh' }}>
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2 }}>Signaler ce commentaire</Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>Expliquez brièvement pourquoi ce commentaire est inapproprié :</Typography>
          <FormControl fullWidth size="small" sx={{ mb: 1 }}>
            <InputLabel>Raison prédéfinie</InputLabel>
            <Select
              value={PREDEFINED_REASONS.includes(reason) ? reason : ''}
              label="Raison prédéfinie"
              onChange={e => setReason(e.target.value)}
            >
              <MenuItem value="">Aucune (raison personnalisée)</MenuItem>
              {PREDEFINED_REASONS.map(r => (
                <MenuItem key={r} value={r}>{r}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <textarea
            value={reason}
            onChange={e => setReason(e.target.value.slice(0, 130))}
            rows={3}
            maxLength={130}
            style={{ width: '100%', borderRadius: 8, border: '1px solid #FFE4BA', padding: 8, marginBottom: 8 }}
            placeholder="Raison du signalement... (130 caractères max)"
          />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="caption" sx={{ color: reason.length >= 130 ? '#FF6B2C' : '#888' }}>
              {reason.length}/130 caractères
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button onClick={onClose} sx={{ color: '#666' }}>Annuler</Button>
            <Button
              onClick={handleSubmit}
              sx={{ background: '#FF6B2C', color: 'white', '&:hover': { background: '#FF7A35' } }}
              disabled={!reason.trim() || loading}
            >
              Signaler
            </Button>
          </Box>
        </Box>
      </Box>
    </ModalPortal>
  );
};

const CommentsDialog: React.FC<CommentsDialogProps> = ({
  open,
  onClose,
  missionId,
  mission,
  onUpdate,
  isOwner
}) => {
  const [searchParams] = useSearchParams();
  const highlightedCommentId = searchParams.get('commentId');
  const highlightedCommentRef = useRef<HTMLDivElement>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [showPrivateConfirm, setShowPrivateConfirm] = useState(false);
  const [isUserProfileModalOpen, setIsUserProfileModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const replyEditorRef = useRef<HTMLDivElement>(null);
  const [isVerifying, setIsVerifying] = useState(false);

  const handleUserClick = async (userId: string) => {
    try {
      // D'abord récupérer le slug de l'utilisateur
      const slugResponse = await axios.get(`/api/users/get-slug/${userId}`, API_CONFIG);
      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      // Ensuite récupérer le profil complet avec le slug
      const response = await axios.get(`/api/users/profil/${slugResponse.data.slug}`, API_CONFIG);
      if (response.data) {
        setSelectedUser(response.data);
        setIsUserProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du profil:', error);
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  const handleReplyClick = (commentId: string | null) => {
    if (!commentId) {
      setReplyingTo(null);
      return;
    }

    // Si on clique sur le même commentaire, on force le défilement
    if (replyingTo === commentId) {
      setTimeout(() => {
        replyEditorRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
      return;
    }

    // Trouver le commentaire principal si on répond à une réponse
    const findMainComment = (id: string) => {
      const mainComment = comments.find(c => c.id === id);
      if (mainComment) return mainComment.id;

      // Si c'est une réponse, chercher son commentaire parent
      for (const comment of comments) {
        if (comment.replies?.some(reply => reply.id === id)) {
          return comment.id;
        }
      }
      return id;
    };

    const mainCommentId = findMainComment(commentId);
    setReplyingTo(mainCommentId);
  };

  // Fonction utilitaire pour vérifier si un commentaire peut encore être édité
  const canStillEdit = (createdAt: string): boolean => {
    const commentDate = new Date(createdAt);
    const now = new Date();
    const diffInMinutes = (now.getTime() - commentDate.getTime()) / (1000 * 60);
    return diffInMinutes <= 1;
  };

  // Fonction pour vérifier si un commentaire peut encore être édité/supprimé
  const updateCommentsEditability = useCallback(() => {
    setComments(prevComments => {
      const updatedComments = prevComments.map(comment => {
        const isEditable = canStillEdit(comment.created_at);

        // Si le commentaire n'est plus éditable, on force la fermeture de l'édition
        if (!isEditable && editingComment === comment.id) {
          setEditingComment(null);
          setEditText('');
        }

        // Mise à jour des commentaires principaux
        const updatedComment = {
          ...comment,
          canEdit: comment.isOwnComment && isEditable
        };

        // Mise à jour des réponses si elles existent
        if (updatedComment.replies) {
          updatedComment.replies = updatedComment.replies.map(reply => {
            const isReplyEditable = canStillEdit(reply.created_at);

            // Si la réponse n'est plus éditable, on force la fermeture de l'édition
            if (!isReplyEditable && editingComment === reply.id) {
              setEditingComment(null);
              setEditText('');
            }

            return {
              ...reply,
              canEdit: reply.isOwnComment && isReplyEditable
            };
          });
        }

        return updatedComment;
      });

      return updatedComments;
    });
  }, [editingComment, replyingTo]);

  // Fonction pour nettoyer les cooldowns expirés
  const cleanExpiredCooldowns = useCallback(() => {
    const cooldowns = JSON.parse(getCookie('commentCooldowns') || '[]');
    const now = new Date().getTime();
    const validCooldowns = cooldowns.filter((cd: any) => {
      return now - new Date(cd.timestamp).getTime() < 60000; // 60 secondes
    });

    if (validCooldowns.length !== cooldowns.length) {
      if (validCooldowns.length === 0) {
        // Si plus aucun cooldown valide, supprimer le cookie
        setCookie('commentCooldowns', '', -1);
      } else {
        // Sinon mettre à jour avec les cooldowns valides
        setCookie('commentCooldowns', JSON.stringify(validCooldowns), 60);
      }
    }
    return validCooldowns;
  }, []);

  // Fonction pour vérifier si un commentaire est possible
  const canComment = useCallback(() => {
    const validCooldowns = cleanExpiredCooldowns();
    return !validCooldowns.some((cd: any) => cd.missionId === missionId);
  }, [missionId, cleanExpiredCooldowns]);

  // Fonction pour ajouter un nouveau cooldown
  const setCommentCooldown = useCallback(() => {
    const validCooldowns = cleanExpiredCooldowns();
    const newCooldown = {
      missionId,
      timestamp: new Date().toISOString()
    };
    const updatedCooldowns = [...validCooldowns, newCooldown];
    setCookie('commentCooldowns', JSON.stringify(updatedCooldowns), 60);
  }, [missionId, cleanExpiredCooldowns]);

  // Fonction pour obtenir le temps restant
  const getRemainingTime = useCallback(() => {
    const validCooldowns = cleanExpiredCooldowns();
    const missionCooldown = validCooldowns.find((cd: any) => cd.missionId === missionId);

    if (!missionCooldown) return 0;

    const diffInSeconds = 60 - ((new Date().getTime() - new Date(missionCooldown.timestamp).getTime()) / 1000);
    return Math.max(0, Math.ceil(diffInSeconds));
  }, [missionId, cleanExpiredCooldowns]);

  useEffect(() => {
    const checkRemainingTime = () => {
      setRemainingTime(getRemainingTime());
    };

    const timer = setInterval(() => {
      checkRemainingTime();
      updateCommentsEditability();
    }, 1000);

    checkRemainingTime();
    updateCommentsEditability();

    return () => clearInterval(timer);
  }, [getRemainingTime, updateCommentsEditability]);

  useEffect(() => {
    if (open) {
      setPage(1);
      fetchComments(1, true);
    }
  }, [open, missionId]);

  const fetchComments = async (pageNumber: number, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const data = await missionsApi.getMissionComments(missionId, pageNumber);

      if (reset) {
        setComments(data.comments);
      } else {
        setComments(prev => [...prev, ...data.comments]);
      }

      setHasMore(data.hasMore);
      setPage(pageNumber);
    } catch (error) {
      notify('Erreur lors de la récupération des commentaires', 'error');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchComments(page + 1);
    }
  }, [loadingMore, hasMore, page]);

  const handleEditorChange = useCallback((content: string) => {
    const sanitizedContent = DOMPurify.sanitize(content);
    const textContent = stripHtml(sanitizedContent);

    if (textContent.length > 150) {
      notify('Le commentaire ne peut pas dépasser 150 caractères', 'warning');
      return;
    }

    setNewComment(sanitizedContent);
  }, []);

  const { validateContentSafety } = useContentModeration();

  const handleSubmit = useCallback(async () => {
    if (!newComment.trim()) return;
    if (!canComment()) {
      notify('Veuillez attendre 1 minute entre chaque commentaire sur la même mission', 'warning');
      return;
    }

    setIsVerifying(true);
    try {
      // Vérifier le contenu avec la modération
      const textContent = stripHtml(newComment);
      const isCommentSafe = await validateContentSafety(textContent, 'comment');

      if (!isCommentSafe) {
        // La notification est déjà gérée par validateContentSafety
        return;
      }

      if (isPrivate) {
        setShowPrivateConfirm(true);
        return;
      }

      await submitComment();
    } finally {
      setIsVerifying(false);
    }
  }, [missionId, newComment, isPrivate, replyingTo, onUpdate, canComment]);

  const submitComment = async () => {
    try {
      logger.info('Envoi du commentaire avec les paramètres:', {
        missionId,
        comment: newComment,
        isPrivate,
        parentId: replyingTo
      });

      const comment = await missionsApi.commentMission(
        missionId,
        newComment,
        isPrivate,
        replyingTo || undefined
      );

      // S'assurer que les données utilisateur sont présentes et valides
      if (!comment.user?.user_profil?.[0]) {
        throw new Error('Données utilisateur manquantes dans la réponse');
      }

      const newCommentObj = {
        ...comment,
        canEdit: true,
        isOwnComment: true,
        user: {
          ...comment.user,
          user_profil: comment.user.user_profil
        }
      };

      if (replyingTo) {
        setComments(prev => prev.map(c =>
          c.id === replyingTo
            ? { ...c, replies: [newCommentObj, ...(c.replies || [])] }
            : c
        ));
      } else {
        setComments(prev => [newCommentObj, ...prev]);
      }

      setNewComment('');
      setIsPrivate(false);
      setReplyingTo(null);
      setCommentCooldown();

      // Forcer le rafraîchissement de l'interface
      if (onUpdate) onUpdate();

      // Notification de succès
      notify('Commentaire envoyé avec succès', 'success');
    } catch (error: any) {
      // Vérifier si l'erreur vient du rate limiter
      if (error.response?.data?.message) {
        notify(error.response.data.message, error.response.data.toastType || 'warning');
      } else {
        notify(error.message || 'Une erreur est survenue lors de l\'ajout du commentaire', 'error');
      }
      logger.error('Erreur lors de l\'ajout du commentaire:', error);
    }
  };

  const handleEdit = useCallback(async (commentId: string, newText: string) => {
    if (!newText.trim()) return;

    try {
      await missionsApi.updateComment(missionId, commentId, newText);

      setComments(prev => prev.map(c => {
        if (c.id === commentId) {
          return {
            ...c,
            comment: newText,
            updated_at: new Date().toISOString()
          };
        }
        if (c.replies) {
          const updatedReplies = c.replies.map(r => {
            if (r.id === commentId) {
              return {
                ...r,
                comment: newText,
                updated_at: new Date().toISOString()
              };
            }
            return r;
          });

          if (updatedReplies.some(r => r.id === commentId)) {
            return {
              ...c,
              replies: updatedReplies
            };
          }
        }
        return c;
      }));

      setEditingComment(null);
      if (onUpdate) onUpdate();
      notify('Commentaire modifié avec succès', 'success');
    } catch (error: any) {
      logger.error('Error updating comment:', error);
      if (error.response?.status === 403) {
        notify('Le délai de modification (1 minute) est dépassé', 'error');
      } else {
        notify('Erreur lors de la modification du commentaire', 'error');
      }
      setEditingComment(null);
    }
  }, [missionId, onUpdate]);

  const handleDelete = async (commentId: string) => {
    // Vérifier si le commentaire peut encore être supprimé
    const findComment = (comments: Comment[]) => {
      for (const comment of comments) {
        if (comment.id === commentId) return comment;
        if (comment.replies) {
          const found = comment.replies.find(r => r.id === commentId);
          if (found) return found;
        }
      }
      return null;
    };

    const comment = findComment(comments);

    if (!comment || !canStillEdit(comment.created_at)) {
      notify('Le délai de suppression (1 minute) est dépassé', 'error');
      return;
    }

    try {
      await missionsApi.deleteComment(missionId, commentId);

      // Mise à jour immédiate de l'état pour les commentaires imbriqués
      setComments(prev => {
        // Vérifier d'abord si c'est un commentaire principal
        const isMainComment = prev.some(c => c.id === commentId);

        if (isMainComment) {
          return prev.filter(c => c.id !== commentId);
        }

        // Si ce n'est pas un commentaire principal, c'est une réponse
        return prev.map(comment => {
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.filter(reply => reply.id !== commentId)
            };
          }
          return comment;
        });
      });

      if (onUpdate) onUpdate();
      notify('Commentaire supprimé avec succès', 'success');
    } catch (error: any) {
      if (error.response?.status === 403) {
        notify('Le délai de suppression (1 minute) est dépassé', 'error');
      } else {
        notify('Erreur lors de la suppression du commentaire', 'error');
      }
    }
  };

  useEffect(() => {
    if (open && highlightedCommentId && highlightedCommentRef.current) {
      highlightedCommentRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [open, highlightedCommentId]);

  return (
    <ModalPortal onBackdropClick={onClose}>
      <StyledModal onClick={e => e.stopPropagation()}>
        <InfoDialog open={infoDialogOpen} onClose={() => setInfoDialogOpen(false)} />
        <PrivateMessageConfirmation
          open={showPrivateConfirm}
          onClose={() => setShowPrivateConfirm(false)}
          onConfirm={() => {
            setShowPrivateConfirm(false);
            submitComment();
          }}
        />
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            borderBottom: '1px solid #FFE4BA',
            padding: '10px 24px',
            backgroundColor: '#FFF8F3'
          }}
        >
          <Box
            sx={{
              backgroundColor: '#FFE4BA',
              borderRadius: '50%',
              width: 40,
              height: 40,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <MessageCircle size={24} color="#FF6B2C" />
          </Box>
          <Typography
            variant="h6"
            component="span"
            sx={{ flex: 1, fontWeight: 'bold', color: '#FF6B2C' }}
          >
            Commentaires
          </Typography>
          <Tooltip title="Fermer">
            <IconButton
              onClick={onClose}
              size="small"
              aria-label="Fermer"
              sx={{
                color: '#FF6B2C',
                borderRadius: '50%',
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: '#FF965E',
                  color: '#FF6B2C'
                }
              }}
            >
              <X size={20} />
            </IconButton>
          </Tooltip>
        </DialogTitle>

        <DialogContent sx={{ padding: { xs: '8px', sm: '24px' }, overflowY: 'auto', flex: 1 }}>
          <MissionView
            mission={mission}
            isOwner={isOwner}
            onClose={onClose}
            handleUserClick={handleUserClick}
          />
          <Box sx={{ marginBottom: '24px', marginTop: '24px' }}>
            {remainingTime > 0 ? (
              <WaitingMessage>
                <WaitingIcon size={24} />
                <WaitingText>
                  {`Il vous reste ${remainingTime} secondes avant de pouvoir commenter à nouveau. Pendant ce temps, vous pouvez consulter les autres commentaires, modifier ou supprimer votre commentaire.`}
                </WaitingText>
              </WaitingMessage>
            ) : (
              <EditCommentEditor
                value={newComment}
                onChange={handleEditorChange}
                onSubmit={handleSubmit}
                loading={isVerifying}
                originalText=""
                isPrivate={isPrivate}
                setIsPrivate={setIsPrivate}
                setInfoDialogOpen={setInfoDialogOpen}
                mission={mission}
              />
            )}
          </Box>

          <Divider sx={{ margin: '16px 0' }} />

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', padding: '32px' }}>
              <CircularProgress sx={{ color: '#FF6B2C' }} />
            </Box>
          ) : (
            <>
              <CommentsList
                comments={comments}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onReply={handleReplyClick}
                editingComment={editingComment}
                editText={editText}
                setEditText={setEditText}
                handleEdit={handleEdit}
                replyingTo={replyingTo}
                newComment={newComment}
                handleEditorChange={handleEditorChange}
                handleSubmit={handleSubmit}
                loading={loading}
                remainingTime={remainingTime}
                isPrivate={isPrivate}
                setIsPrivate={setIsPrivate}
                setInfoDialogOpen={setInfoDialogOpen}
                setEditingComment={setEditingComment}
                setReplyingTo={setReplyingTo}
                handleUserClick={handleUserClick}
                mission={mission}
              />

              {hasMore && !loading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Button
                    onClick={handleLoadMore}
                    disabled={loadingMore}
                    startIcon={loadingMore ? <CircularProgress size={20} /> : null}
                    sx={{
                      color: '#FF6B2C',
                      borderColor: '#FFE4BA',
                      '&:hover': {
                        backgroundColor: '#FFF8F3',
                        borderColor: '#FF6B2C',
                      }
                    }}
                  >
                    {loadingMore ? 'Chargement...' : 'Charger plus de commentaires'}
                  </Button>
                </Box>
              )}
            </>
          )}
        </DialogContent>

        {/* Modal du profil utilisateur */}
        {selectedUser && (
          <UserProfileModal
            isOpen={isUserProfileModalOpen}
            onClose={() => setIsUserProfileModalOpen(false)}
            userData={selectedUser}
          />
        )}
      </StyledModal>
    </ModalPortal>
  );
};

export default React.memo(CommentsDialog);