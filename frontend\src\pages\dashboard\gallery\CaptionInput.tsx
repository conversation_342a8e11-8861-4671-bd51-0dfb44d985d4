import React from 'react';

interface CaptionInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  isTextArea?: boolean;
}

const MAX_CHARS = 30;

export const CaptionInput: React.FC<CaptionInputProps> = ({
  value,
  onChange,
  placeholder = "Entrez une légende...",
  isTextArea = false
}) => {
  const remainingChars = MAX_CHARS - value.length;
  const isAtLimit = remainingChars === 0;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= MAX_CHARS) {
      onChange(newValue);
    }
  };

  const InputComponent = isTextArea ? 'textarea' : 'input';

  return (
    <div className="space-y-2">
      <InputComponent
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] ${
          isAtLimit ? 'border-red-300' : 'border-gray-300'
        }`}
        {...(isTextArea ? { rows: 3 } : { type: 'text' })}
      />
      <div className={`text-sm flex justify-end ${
        isAtLimit ? 'text-red-500' : 'text-gray-500'
      }`}>
        {remainingChars} caractères restants
      </div>
    </div>
  );
}; 