import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNotification } from '../components/Notification';
import { logger } from '../utils/logger';
import { createAuthService } from '../services/auth';
import { getCookie, removeCookie } from '../utils/cookieUtils';
import { setCookie } from '../utils/cookieUtils';
import { fetchCsrfToken } from '../services/csrf';
import { setupAxiosInterceptors } from '../config/api';
import { getCommonHeaders } from '@/utils/headers';
import { api } from '../services/api'; // Import de l'instance API centralisée
import axios from 'axios'; // Importation d'axios pour vérifier les erreurs (isAxiosError)
import { InternalAxiosRequestConfig } from 'axios';

interface UserData {
  email: string;
  password: string;
  userType: 'jobbeur' | 'non-jobbeur';
}

interface User {
  id: string;
  email: string;
  role: 'jobpadm' | 'jobmodo' | 'jobutil';
  isAdmin?: boolean;
  isModerator?: boolean;
  profil_verifier: boolean;
  identite_verifier: boolean;
  entreprise_verifier: boolean;
  assurance_verifier: boolean;
  email_verifier: boolean;
  profil_actif: boolean;
  user_type: 'jobbeur' | 'non-jobbeur';
  last_login: string | null;
  date_inscription?: string;
  is_online?: boolean;
  profil?: {
    data: {
      slug: string;
      nom: string;
      prenom: string;
      telephone: string;
      telephone_prive: boolean;
      numero: string;
      adresse: string;
      ville: string;
      code_postal: string;
      pays: string;
      photo_url: string;
      bio: string;
      mode_vacance: boolean;
      intervention_zone: any;
      type_de_profil: string;
      nom_entreprise: string;
      prenom_entreprise: string;
      statut_entreprise: string;
      siren_entreprise: string;
      code_ape_entreprise: string;
      categorie_entreprise: string;
      effectif_entreprise: string;
      date_insee_creation_entreprise: string;
      date_categorie_entreprise: string;
      date_derniere_mise_a_jour_entreprise_insee: string;
      date_derniere_mise_a_jour_du_client_entreprise: string;
    };
  };
  suspension_reason?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isOnline: boolean;
  requestPasswordReset: (email: string) => Promise<{ success: boolean; cooldownMinutes: number }>;
  resetPassword: (token: string, newPassword: string) => Promise<{ success: boolean }>;
  validateResetToken: (token: string) => Promise<boolean>;
  logout: () => Promise<void>;
  login: (email: string, password: string) => Promise<{
    success: boolean;
    user: User | null;
    message?: string;
    email_verifier?: boolean;
    redirectTo?: string;
    requiresTwoFactor?: boolean;
    email?: string;
    maskedEmail?: string;
  }>;
  inscription: (userData: UserData) => Promise<{ success: boolean; userId: string; needsVerification: boolean; retour_message?: string }>;
  verifyEmail: (token: string) => Promise<{ success: boolean; alreadyVerified: boolean; error?: string }>;
  resendVerification: (email?: string) => Promise<{ success: boolean } | undefined>;
  updatePassword: (token: string, newPassword: string) => Promise<void>;
  updateProfil: (data: Partial<User>) => Promise<User>;
  refreshUser: () => Promise<void>;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isOnline: boolean;
}

const initialState: AuthState = {
  user: null,
  loading: true,
  error: null,
  isAuthenticated: false,
  isOnline: false,
};

// Déclaration de type pour import.meta.env
declare global {
  interface ImportMeta {
    readonly env: {
      readonly VITE_API_URL: string;
    }
  }

  // Étendre l'interface Error pour inclure alreadyShown
  interface Error {
    alreadyShown?: boolean;
  }
}

export {}; // Nécessaire pour que TypeScript reconnaisse la déclaration globale

// Étendre l'interface InternalAxiosRequestConfig pour inclure _retry
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Ajout d'un contexte par défaut
const defaultContext: AuthContextType = {
  user: null,
  loading: false,
  error: null,
  isAuthenticated: false,
  isOnline: false,
  requestPasswordReset: async () => ({ success: false, cooldownMinutes: 0 }),
  resetPassword: async () => ({ success: false }),
  validateResetToken: async () => false,
  logout: async () => {},
  login: async () => ({ success: false, user: null }),
  inscription: async () => ({ success: false, userId: '', needsVerification: false }),
  verifyEmail: async () => ({ success: false, alreadyVerified: false }),
  resendVerification: async () => undefined,
  updatePassword: async () => {},
  updateProfil: async () => Promise.resolve({} as User),
  refreshUser: async () => {},
};

// Contexte d'authentification
export const AuthContext = createContext<AuthContextType>(defaultContext);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Variable globale pour suivre si un intervalle de renouvellement est déjà configuré
let tokenRenewalIntervalId: number | null = null;

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<AuthState>(initialState);
  const { notify } = useNotification();
  const authService = createAuthService(notify);

  const handleError = (error: any) => {
    logger.info('Authentication Error - Error Details:', error);

    let message: string;

    if (typeof error === 'string') {
      message = error;
    } else if (error instanceof Error) {
      message = error.message;
    } else {
      message = error.response?.data?.message || error.response?.data?.msg || 'Une erreur est survenue';
    }

    logger.info('Error Message:', message);

    if (!error.alreadyShown) {
      notify(message, 'error');
      error.alreadyShown = true;
    }

    setState({ ...state, error: message });
    throw error;
  };

  const handleLogout = async () => {
    try {
      // Nettoyer l'intervalle de renouvellement du token
      if (tokenRenewalIntervalId) {
        window.clearInterval(tokenRenewalIntervalId);
        tokenRenewalIntervalId = null;
      }
      // Supprimer tous les cookies possibles
      removeCookie('token');
      removeCookie('_token');
      removeCookie('access_token');
      removeCookie('refresh_token');

      // Supprimer également les cookies avec le domaine spécifique
      if (window.location.hostname.includes('jobpartiel.fr')) {
        removeCookie('token', true, '.jobpartiel.fr');
        removeCookie('_token', true, '.jobpartiel.fr');
        removeCookie('access_token', true, '.jobpartiel.fr');
        removeCookie('refresh_token', true, '.jobpartiel.fr');
        // Suppression des cookies avec api.jobpartiel.fr
        removeCookie('token', true, 'api.jobpartiel.fr');
        removeCookie('_token', true, 'api.jobpartiel.fr');
        removeCookie('access_token', true, 'api.jobpartiel.fr');
        removeCookie('refresh_token', true, 'api.jobpartiel.fr');
      }

      delete api.defaults.headers.common['Authorization'];

      // Réinitialisation complète de l'état
      setState(initialState); // Utiliser l'état initial pour une réinitialisation propre
      // Forcer le loading à true pour indiquer la transition
      setState(prev => ({ ...prev, loading: true }));

      notify('Votre session a expiré. Veuillez vous reconnecter.', 'warning');
      // Supprimer tous les intervalles actifs pour éviter toute boucle
      for (let i = 1; i < 99999; i++) window.clearInterval(i);
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
        setTimeout(() => window.location.reload(), 100); // Ajout pour forcer le rechargement complet
      }
    } catch (error) {
      logger.error('Erreur lors de la déconnexion:', error);
      window.location.href = '/login';
    }
  };

  useEffect(() => {
    // Vérifier le token au chargement
    const checkAuth = async () => {
      try {
        // On ne lit plus le token JS, on compte sur le cookie httpOnly
        try {
          // Ajouter un timestamp pour éviter le cache du navigateur
          const timestamp = Date.now();
          const { data } = await api.get(`/api/auth/profil?_=${timestamp}`, {
            withCredentials: true
          });

          if (data && data.user && data.user.id) {
            setState((prevState: AuthState) => ({
              ...prevState,
              user: data.user,
              loading: false,
              isAuthenticated: true,
              isOnline: true
            }));

            // Renouveler le token immédiatement pour s'assurer qu'il est valide
            await authService.refreshToken();
          } else {
            setState((prevState: AuthState) => ({
              ...prevState,
              user: null,
              loading: false,
              isAuthenticated: false,
              isOnline: false
            }));
          }
        } catch (error) {
          logger.error('❌ Erreur lors de la vérification du profil:', error);

          setState((prevState: AuthState) => ({
            ...prevState,
            user: null,
            loading: false,
            isAuthenticated: false,
            isOnline: false
          }));
        }
      } catch (error) {
        logger.error('❌ Erreur inattendue lors de la vérification d\'authentification:', error);
        setState((prevState: AuthState) => ({
          ...prevState,
          loading: false,
          isAuthenticated: false
        }));
      }
    };

    checkAuth();
  }, []);

  // Effet séparé pour la vérification du profil
  useEffect(() => {
    if (!state.user?.id) return;

    const checkUserProfil = async () => {
      try {
        const userProfil = await authService.verify_profil_actif(state.user!.id);
        if (!userProfil) {
          logger.warn('🔒 Profil désactivé');
          await authService.sendSuspensionEmail(state.user!.email, state.user?.suspension_reason || 'Votre profil a été désactivé. Veuillez contacter le support.');
          await handleLogout();
          window.location.href = '/login';
          notify('Votre profil a été désactivé. Veuillez contacter le support.', 'error');
        }
      } catch (error) {
        logger.error('❌ Erreur de vérification du profil:', error);
      }
    };

    // Vérifier le profil toutes les 2 minutes pour s'assurer que le profil est actif et maintenir la session active (non banni, suspendu, supprimé etc pour la modération)
    checkUserProfil();
    const interval = setInterval(checkUserProfil, 2 * 60 * 1000);

    return () => clearInterval(interval);
  }, [state.user?.id]);

  useEffect(() => {
    // Initialiser l'intercepteur avec la fonction de déconnexion
    setupAxiosInterceptors(handleLogout);
    // Configuration des intercepteurs axios
    const interceptor = api.interceptors.response.use(
      (response) => {
        // Gérer les notifications de mot de passe qui expire
        if (response.data.passwordStatus && response.data.passwordStatus.daysUntilExpiration) {
          // logger.warn(`🕒 Mot de passe expire dans ${response.data.passwordStatus.daysUntilExpiration} jours`);
          notify(`Votre mot de passe expirera dans ${response.data.passwordStatus.daysUntilExpiration} jours`, 'warning');
        }
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Vérifier si l'erreur est due à un token expiré et que la requête n'a pas déjà été retentée
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Tenter de rafraîchir le token
            const refreshResult = await authService.refreshToken();

            if (refreshResult.success) {
              // Réessayer la requête originale avec le nouveau token
              return api(originalRequest);
            } else if (refreshResult.expired) {
              // Si le refresh échoue à cause d'un token expiré/révoqué, déconnecter immédiatement
              notify('Votre session a expiré. Veuillez vous reconnecter.', 'error');
              await handleLogout();
              return Promise.reject(error);
            } else {
              // Si le refresh échoue pour une autre raison, déconnecter aussi
              await handleLogout();
              return Promise.reject(error);
            }
          } catch (refreshError) {
            // En cas d'erreur de refresh, déconnecter l'utilisateur
            await handleLogout();
            return Promise.reject(refreshError);
          }
        }

        // Gérer l'expiration du mot de passe
        if (error.response?.data?.passwordExpired) {
          // logger.error('❌ Mot de passe expiré');
          handleLogout();
          notify('Votre mot de passe a expiré. Veuillez le réinitialiser.', 'error');
          window.location.href = '/forgot-password';
        }

        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.response.eject(interceptor);
    };
  }, []);

  // Fonction pour renouveler le token
  const renewToken = useCallback(async () => {
    if (!state.isAuthenticated) {
      logger.info('renewToken appelé alors que non authentifié, on stop.');
      return false;
    }
    try {
      logger.info('Renouvellement automatique du token');
      const refreshResult = await authService.refreshToken();

      if (refreshResult.success) {
        // logger.info('Token renouvelé avec succès');
        return true;
      } else {
        if (refreshResult.expired) {
          logger.warn('Session expirée, déconnexion automatique');
          await handleLogout();
        } else {
          logger.error('Échec du renouvellement du token');
        }
        return false;
      }
    } catch (error) {
      logger.error('Erreur lors du renouvellement du token:', error);
      return false;
    }
  }, [authService, handleLogout]);

  // Renouveler le token périodiquement
  useEffect(() => {
    if (state.isAuthenticated && state.user) {
      // Renouveler le token immédiatement au chargement si aucun renouvellement n'est en cours
      if (!tokenRenewalIntervalId) {
        renewToken();

        // Puis configurer un intervalle pour le renouvellement périodique
        tokenRenewalIntervalId = window.setInterval(() => {
          renewToken();
        }, 5 * 60 * 1000); // Toutes les 5 minutes

        // logger.info('Intervalle de renouvellement du token configuré');
      }

      return () => {
        // Ne pas effacer l'intervalle lors du démontage du composant
        // pour éviter de créer plusieurs intervalles lors des changements de page
      };
    } else if (!state.isAuthenticated && tokenRenewalIntervalId) {
      // Si l'utilisateur n'est plus authentifié, effacer l'intervalle
      window.clearInterval(tokenRenewalIntervalId);
      tokenRenewalIntervalId = null;
      logger.info('Intervalle de renouvellement du token effacé');
    }
  }, [state.isAuthenticated, state.user, renewToken]);

  // Intercepter les erreurs 401 et tenter de renouveler le token
  useEffect(() => {
    const interceptor = api.interceptors.response.use(
      response => response,
      async error => {
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          const config = error.config as ExtendedAxiosRequestConfig;
          // Ne pas tenter de renouveler le token si nous sommes sur une route d'authentification
          const isAuthRoute = config?.url?.includes('/api/auth/');
          if (!isAuthRoute && state.isAuthenticated && !config?._retry) {
            config._retry = true; // Marquer la requête comme ayant déjà été retentée
            logger.info('Erreur 401 interceptée, tentative de renouvellement du token');
            try {
              const success = await renewToken();
              if (success && config) {
                // Réessayer la requête originale avec le nouveau token
                return api(config);
              }
            } catch (refreshError) {
              // Si le renouvellement échoue, déconnecter l'utilisateur
              await handleLogout();
              return Promise.reject(error);
            }
          } else {
            // Si c'est une route d'auth ou si la requête a déjà été retentée, déconnecter
            await handleLogout();
          }
        }
        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.response.eject(interceptor);
    };
  }, [state.isAuthenticated, renewToken]);

  // Fonction pour mettre à jour l'activité
  const updateActivity = async () => {
    // Vérifier à la fois l'ID utilisateur et l'état d'authentification
    if (!state.user?.id || !state.isAuthenticated) {
      // Si l'utilisateur n'est pas authentifié mais a un ID, c'est probablement une incohérence d'état
      if (state.user?.id && !state.isAuthenticated) {
        logger.info('Incohérence détectée: ID utilisateur présent mais non authentifié');
      } else {
        logger.info('Impossible de mettre à jour l\'activité: utilisateur non connecté');
      }
      return;
    }

    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = csrfToken;
      headers['Content-Type'] = 'application/json';
      const response = await api.post('/api/users/activity', {}, { headers });

      logger.info('Réponse de la mise à jour de l\'activité:', response.data);

      if (response.data.success) {
        // logger.info('Mise à jour de l\'activité réussie');
        setState(prevState => ({ ...prevState, isOnline: true }));
      } else {
        logger.error('Erreur lors de la mise à jour de l\'activité:', response.data);
      }
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de l\'activité:', error);

      // Si l'erreur est 401, essayer de renouveler le token et réessayer
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        try {
          logger.info('Tentative de renouvellement du token après erreur 401...');
          const refreshResult = await authService.refreshToken();

          if (refreshResult.success) {
            logger.info('Token renouvelé avec succès après erreur 401, nouvelle tentative de mise à jour de l\'activité');
            // Réessayer la requête après renouvellement du token
            await fetchCsrfToken();
            const newCsrfToken = await fetchCsrfToken();
            const newHeaders = await getCommonHeaders();
            newHeaders['X-CSRF-Token'] = newCsrfToken;
            newHeaders['Content-Type'] = 'application/json';
            const newResponse = await api.post('/api/users/activity', {}, { headers: newHeaders });

            if (newResponse.data.success) {
              setState(prevState => ({ ...prevState, isOnline: true }));
            }
          } else {
            // Si le renouvellement échoue, déconnecter l'utilisateur
            logger.info('Échec du renouvellement du token après erreur 401, déconnexion...');
            await handleLogout();
          }
        } catch (refreshError) {
          logger.error('Erreur lors du renouvellement du token après erreur 401:', refreshError);
          await handleLogout();
        }
      }
    }
  };

  // Effet pour gérer le statut en ligne
  useEffect(() => {
    // Ne configurer l'intervalle que si l'utilisateur est authentifié
    if (!state.user?.id || !state.isAuthenticated) {
      return;
    }

    // logger.info('Configuration de l\'intervalle de mise à jour d\'activité');

    // Mettre à jour l'activité immédiatement
    updateActivity();

    // Configurer l'intervalle de mise à jour
    const activityInterval = setInterval(updateActivity, 30000); // 30 secondes

    // Gérer la déconnexion
    const handleOffline = () => {
      setState(prevState => ({ ...prevState, isOnline: false }));
    };

    // Écouter les événements de connexion
    window.addEventListener('offline', handleOffline);
    window.addEventListener('online', updateActivity);

    // Cleanup
    return () => {
      // logger.info('Nettoyage de l\'intervalle de mise à jour d\'activité');
      clearInterval(activityInterval);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', updateActivity);
    };
  }, [state.user?.id, state.isAuthenticated]); // Dépendre aussi de isAuthenticated

  const logout = async () => {
    try {
      setState(prevState => ({ ...prevState, loading: true }));

      // Nettoyer l'intervalle de renouvellement du token
      if (tokenRenewalIntervalId) {
        window.clearInterval(tokenRenewalIntervalId);
        tokenRenewalIntervalId = null;
      }

      await authService.signOut();

      // Supprimer tous les cookies possibles
      removeCookie('token');
      removeCookie('_token');
      removeCookie('access_token');
      removeCookie('refresh_token');

      // Supprimer également les cookies avec le domaine spécifique
      if (window.location.hostname.includes('jobpartiel.fr')) {
        removeCookie('token', true, '.jobpartiel.fr');
        removeCookie('_token', true, '.jobpartiel.fr');
        removeCookie('access_token', true, '.jobpartiel.fr');
        removeCookie('refresh_token', true, '.jobpartiel.fr');
        // Suppression des cookies avec api.jobpartiel.fr
        removeCookie('token', true, 'api.jobpartiel.fr');
        removeCookie('_token', true, 'api.jobpartiel.fr');
        removeCookie('access_token', true, 'api.jobpartiel.fr');
        removeCookie('refresh_token', true, 'api.jobpartiel.fr');
      }

      delete api.defaults.headers.common['Authorization'];

      setState({
        user: null,
        loading: false,
        error: null,
        isAuthenticated: false,
        isOnline: false
      });

      window.location.href = '/login';
    } catch (error) {
      logger.info('Erreur lors de la déconnexion :', error);

      // Supprimer tous les cookies même en cas d'erreur
      removeCookie('token');
      removeCookie('_token');
      removeCookie('access_token');
      removeCookie('refresh_token');

      setState({
        user: null,
        loading: false,
        error: null,
        isAuthenticated: false,
        isOnline: false
      });

      window.location.href = '/login';
    }
  };

  const inscription = async (userData: UserData): Promise<{
    success: boolean;
    userId: string;
    needsVerification: boolean;
    retour_message?: string;
  }> => {
    try {
      logger.info('🔑 Démarrage du processus d\'inscription...');

      await fetchCsrfToken();

      const result = await authService.signUp(userData);
      logger.info('📬 Inscription résultat:', result);

      if (!result.success) {
        setState({
          ...state,
          loading: false,
          error: result.message || 'Une erreur est survenue lors de l\'inscription'
        });

        // Notifier l'utilisateur de l'erreur
        notify(result.message || 'Une erreur est survenue lors de l\'inscription', 'error');

        // Retourner un objet avec les propriétés requises même en cas d'erreur
        return {
          success: false,
          userId: '', // Une chaîne vide pour respecter le type string
          needsVerification: false,
          retour_message: result.message || 'Une erreur est survenue lors de l\'inscription'
        };
      }

      return {
        success: true,
        userId: result.userId || '', // S'assurer d'avoir une chaîne même si undefined
        needsVerification: result.needsVerification || true, // Par défaut true pour la sécurité
        retour_message: result.message || 'Inscription réussie.'
      };
    } catch (error) {
      logger.error('❌ Inscription erreur:', error);

      setState({
        ...state,
        loading: false,
        error: error instanceof Error
          ? error.message
          : 'Une erreur est survenue lors de l\'inscription'
      });

      // Retourner un objet avec les propriétés requises même en cas d'erreur
      return {
        success: false,
        userId: '', // Une chaîne vide pour respecter le type string
        needsVerification: false,
        retour_message: error instanceof Error ? error.message : 'Une erreur est survenue lors de l\'inscription'
      };
    } finally {
      setState({ ...state, loading: false });
    }
  };

  const resendVerification = async (email?: string) => {
    try {
      setState({ ...state, loading: true });

      // Priorité 1 : Email passé en paramètre
      // Priorité 2 : Email de l'utilisateur connecté
      // Priorité 3 : Email stocké dans un cookie
      const emailToUse = email ||
                         state.user?.email ||
                         getCookie('pendingVerificationEmail');

      if (!emailToUse) {
        notify('Aucun email n\'a été trouvé. Veuillez vous réinscrire.', 'error');
        return;
      }

      notify('Un nouveau lien de vérification a été envoyé à votre adresse email.', 'info');
      return { success: true };
    } catch (error: any) {
      logger.error('Erreur lors du renvoi de vérification :', error);

      if (error.response?.data?.["afficher-notification"]) {
        notify(error.response.data.message, error.response.data.toastType);
      } else {
        notify(error.response?.data?.message || 'Une erreur est survenue lors du renvoi du lien de vérification', 'error');
      }

      throw error;
    } finally {
      setState({ ...state, loading: false });
    }
  };

  type ToastType = 'success' | 'error' | 'info' | 'warning';

  interface ApiResponse {
    success: boolean;
    message?: string;
    "afficher-notification"?: boolean;
    toastType?: ToastType;
  }

  interface EmailVerificationResponse extends ApiResponse {
    alreadyVerified: boolean;
  }

  interface PasswordResetResponse extends ApiResponse {
    cooldownMinutes: number;
  }

  interface TokenValidationResponse extends ApiResponse {
    valid: boolean;
  }

  const verifyEmail = async (token: string) => {
    try {
      const response = await authService.verifyEmail(token) as EmailVerificationResponse;

      if (response["afficher-notification"]) {
        notify(response.message || '', response.toastType || 'info');
      }

      return {
        success: true,
        alreadyVerified: false
      };
    } catch (error: any) {
      logger.error('Erreur lors de la vérification :', error);

      return {
        success: false,
        alreadyVerified: error.response?.status === 409,
        error: error.response?.data?.message || 'Une erreur est survenue'
      };
    }
  };

  const requestPasswordReset = async (email: string) => {
    try {
      const response = await authService.requestPasswordReset(email) as PasswordResetResponse;
      return { success: true, cooldownMinutes: response.cooldownMinutes };
    } catch (error: any) {
      handleError(error);
      throw error;
    }
  };

  const validateResetToken = async (token: string) => {
    try {
      const response = await authService.validateResetToken(token) as TokenValidationResponse;
      return response.valid;
    } catch (error: any) {
      handleError(error);
      return false;
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    try {
      const { success } = await authService.resetPassword(token, newPassword);
      return { success };
    } catch (error: any) {
      handleError(error);
      throw error;
    }
  };

  const updatePassword = async (token: string, newPassword: string) => {
    try {
      setState({ ...state, loading: true });
      await authService.updatePassword(token, newPassword);
      notify('Mot de passe mis à jour !', 'success');
    } catch (error: any) {
      handleError(error);
    } finally {
      setState({ ...state, loading: false });
    }
  };

  const updateProfil = async (data: Partial<User>) => {
    try {
      const response = await api.put(`/api/auth/profil`, data);
      setState(prevState => ({
        ...prevState,
        user: response.data.user
      }));
      return response.data.user;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };

  const login = async (email: string, password: string): Promise<{
    success: boolean;
    user: User | null;
    message?: string;
    email_verifier?: boolean;
    redirectTo?: string;
    requiresTwoFactor?: boolean;
    email?: string;
    maskedEmail?: string;
  }> => {
    try {
      logger.info('🔍 Reception tentative de connexion avec:', { email });

      setState({ ...state, loading: true });

      // Authenticate with API
      logger.info('📡 Envoi de la requête d\'authentification...');
      const response = await authService.signIn(email, password);
      logger.info('📥 Réponse complète:', response);

      // Vérifier la structure de la réponse
      if (!response || !response.data) {
        logger.info('Réponse d\'authentification invalide:', response);
        throw new Error('Réponse du serveur invalide');
      }

      const { success, user, email_verifier, message, requiresTwoFactor, maskedEmail } = response.data;

      // Ajouter un log pour déboguer
      logger.info('🔍 Données de réponse complètes:', response.data);

      // Vérifier si l'authentification à deux facteurs est requise
      if (success && requiresTwoFactor) {
        logger.info('🔒 Authentification à deux facteurs requise');
        setState({ ...state, loading: false });
        return {
          success: true,
          user: null,
          requiresTwoFactor: true,
          email: email,
          maskedEmail: maskedEmail || email
        };
      }

      if (!success) {
        // Vérifier d'abord si le compte est désactivé
        if (user?.profil_actif === false) {
          logger.warn('🔒 Compte désactivé');
          return {
            success: false,
            message: 'Votre compte a été désactivé. Veuillez contacter le support.',
            user: user as User // Cast explicite car on sait que user existe ici
          };
        }

        // Vérifier si c'est une erreur CSRF
        if (message && message.includes('CSRF')) {
          logger.error('🔒 Erreur CSRF détectée');
          setState({
            ...state,
            loading: false,
            error: message
          });
          return {
            success: false,
            message: message,
            user: null
          };
        }

        // Ensuite vérifier si l'email n'est pas vérifié
        if (email_verifier === false) {
          const pendingEmail = getCookie('pendingVerificationEmail') || email;
          setCookie('pendingVerificationEmail', pendingEmail);
          logger.info('📧 Email non vérifié, redirection vers vérification');
          setState({
            ...state,
            loading: false,
            error: message || 'Veuillez vérifier votre email avant de continuer.'
          });
          return {
            success: false,
            message: message || 'Veuillez vérifier votre email avant de continuer.',
            email_verifier: false,
            user: null
          };
        }

        // Autres types d'erreurs
        setState({
          ...state,
          loading: false,
          error: message || 'Une erreur est survenue lors de cette connexion.'
        });

        return {
          success: false,
          message: message || 'Une erreur est survenue',
          user: null
        };
      }

      // Vérification du token et de l'utilisateur
      if (!user) {
        logger.info('❌ Utilisateur non trouvé dans la réponse');
        throw new Error('Email ou mot de passe incorrect');
      }

      // logger.info('🔍 Données utilisateur reçues :', JSON.stringify(user, null, 2));

      // logger.info('🔍 Données utilisateur reçues :', JSON.stringify(user, null, 2));
      // logger.info('🟢 Statut du profil (profil_actif) :', user.profil_actif);

      setState({ ...state, user, loading: false });
      return {
        success: true,
        user: user as User // Cast explicite pour s'assurer du type
      };
    } catch (error: any) {
      logger.error('🚨 Erreur lors de la connexion :', error);

      setState({
        ...state,
        loading: false,
        error: error.message || 'Une erreur est survenue lors de cette connexion.'
      });

      return {
        success: false,
        message: error.message || 'Une erreur est survenue',
        user: null
      };
    }
  };

  const refreshUser = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const { data } = await api.get('/api/auth/profil', { withCredentials: true });
      if (data && data.user && data.user.id) {
        setState(prevState => ({
          ...prevState,
          user: data.user,
          loading: false,
          isAuthenticated: true,
          isOnline: true
        }));
      } else {
        setState(prevState => ({
          ...prevState,
          user: null,
          loading: false,
          isAuthenticated: false,
          isOnline: false
        }));
      }
    } catch (error) {
      setState(prevState => ({
        ...prevState,
        user: null,
        loading: false,
        isAuthenticated: false,
        isOnline: false
      }));
    }
  }, []);

  const value = {
    user: state.user,
    loading: state.loading,
    error: state.error,
    isAuthenticated: !!state.user?.id,
    isOnline: state.isOnline && !!state.user?.id,
    requestPasswordReset,
    resetPassword,
    validateResetToken,
    login,
    inscription,
    logout,
    verifyEmail,
    resendVerification,
    updatePassword,
    updateProfil,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
