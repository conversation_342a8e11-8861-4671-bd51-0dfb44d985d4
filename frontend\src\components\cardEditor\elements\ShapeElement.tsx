import React from 'react';
import {
  Rect,
  Circle,
  Line,
  Ellipse,
  Star,
  Arrow,
  RegularPolygon,
  Ring,
  Arc,
  Wedge,
  Path,
  Transformer
} from 'react-konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { ShapeElement } from '../../../types/cardEditor';
import type { LineJoin, LineCap } from 'konva/lib/Shape';

interface ShapeElementProps {
  element: ShapeElement;
  isSelected: boolean;
  onSelect: () => void;
  onDragStart?: (e: KonvaEventObject<DragEvent>) => void;
  onDragMove?: (e: KonvaEventObject<DragEvent>) => void;
  onDragEnd: (e: KonvaEventObject<DragEvent>) => void;
  isEditable?: boolean;
  onContextMenu?: (e: any) => void;
}

const ShapeElementComponent: React.FC<ShapeElementProps> = ({
  element,
  isSelected,
  onSelect,
  onDragStart,
  onDragMove = () => {},
  onDragEnd = () => {},
  isEditable = true,
  onContextMenu
}) => {
  const shapeRef = React.useRef<any>(null);
  const transformerRef = React.useRef<any>(null);

  // Ajout de l'état pour la position d'origine du drag
  const [dragOrigin, setDragOrigin] = React.useState<{ x: number; y: number } | null>(null);
  const [isDragging, setIsDragging] = React.useState(false);
  const [isHovered, setIsHovered] = React.useState(false);

  React.useEffect(() => {
    if (isSelected && transformerRef.current && shapeRef.current) {
      // Attacher le transformer à la forme
      transformerRef.current.nodes([shapeRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const { properties } = element;
  const shapeType = properties.shape || 'rect';

  // Propriétés communes à toutes les formes sauf Line/Arrow/Path
  const baseProps = {
    ref: shapeRef,
    fill: properties.fill,
    stroke: properties.stroke,
    strokeWidth: properties.strokeWidth,
    draggable: isEditable,
    onClick: onSelect,
    onTap: onSelect,
    rotation: element.rotation || 0,
    opacity: properties.opacity,
    shadowColor: properties.shadowColor,
    shadowBlur: properties.shadowBlur,
    shadowOffset: properties.shadowOffset,
    shadowOpacity: properties.shadowOpacity,
    onContextMenu: onContextMenu
  };

  // Pour Line, Arrow, Path : ajouter lineJoin, lineCap, dash si présents et cast en type Konva
  const lineProps = {
    ...baseProps,
    ...(properties.lineCap && ['butt', 'round', 'square'].includes(properties.lineCap)
      ? { lineCap: properties.lineCap as LineCap }
      : {}),
    ...(properties.lineJoin && ['miter', 'round', 'bevel'].includes(properties.lineJoin)
      ? { lineJoin: properties.lineJoin as LineJoin }
      : {}),
    ...(properties.dash ? { dash: properties.dash } : {}),
    onContextMenu: onContextMenu
  };

  // Gérer le survol
  const handleMouseEnter = () => {
    if (isEditable && !isSelected) {
      setIsHovered(true);
      document.body.style.cursor = 'pointer';
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    document.body.style.cursor = 'default';
  };

  // Effet de survol
  const hoverEffect = isHovered ? {
    stroke: '#0096FF', // Couleur du contour
    strokeWidth: 1, // Épaisseur du contour
    // Désactiver les propriétés d'ombre si elles étaient définies ici
    shadowColor: undefined,
    shadowBlur: undefined,
    shadowOffset: undefined,
    shadowOpacity: undefined,
  } : {};

  // Gestion de la fin de transformation (rotation, resize)
  const handleTransformEnd = (e: any) => {
    if (!shapeRef.current) return;
    const node = shapeRef.current;
    let newRotation = node.rotation();
    // Normaliser la rotation entre 0 et 360
    newRotation = ((newRotation % 360) + 360) % 360;
    const newWidth = node.width ? node.width() * node.scaleX() : element.width;
    const newHeight = node.height ? node.height() * node.scaleY() : element.height;
    // Remise à l'échelle normale
    if (node.width && node.height) {
      node.scaleX(1);
      node.scaleY(1);
    }
    onDragEnd({
      ...e,
      target: {
        ...node,
        rotation: () => newRotation,
        width: () => newWidth,
        height: () => newHeight
      }
    });
  };

  // Gestion du drag pour l'affichage du fantôme
  const handleDragStart = (e: KonvaEventObject<DragEvent>) => {
    // Utiliser la position réelle du node Konva pour éviter le décalage
    const node = e.target;
    setDragOrigin({ x: node.x(), y: node.y() });
    setIsDragging(true);
    if (onDragStart) onDragStart(e);
  };
  const handleDragEnd = (e: KonvaEventObject<DragEvent>) => {
    setIsDragging(false);
    setDragOrigin(null);
    if (onDragEnd) onDragEnd(e);
  };

  // Rendu conditionnel selon le type de forme
  const renderShape = (customProps = {}) => {
    switch (shapeType) {
      case 'rect':
        return (
          <Rect
            {...baseProps}
            {...customProps}
            width={element.width || 100}
            height={element.height || 100}
            cornerRadius={properties.cornerRadius || 0}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'circle':
        return (
          <Circle
            {...baseProps}
            {...customProps}
            radius={(element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'ellipse':
        return (
          <Ellipse
            {...baseProps}
            {...customProps}
            radiusX={(element.width || 100) / 2}
            radiusY={(element.height || 50) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'line':
        return (
          <Line
            {...lineProps}
            {...customProps}
            points={properties.points || [0, 0, element.width || 100, 0]}
            stroke={properties.fill}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'star':
        return (
          <Star
            {...baseProps}
            {...customProps}
            numPoints={properties.numPoints || 5}
            innerRadius={properties.innerRadius || (element.width || 100) / 4}
            outerRadius={properties.outerRadius || (element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'arrow':
        return (
          <Arrow
            {...lineProps}
            {...customProps}
            points={properties.points || [0, 0, element.width || 100, 0]}
            pointerLength={properties.pointerLength || 10}
            pointerWidth={properties.pointerWidth || 10}
            stroke={properties.fill}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'polygon':
        return (
          <RegularPolygon
            {...baseProps}
            {...customProps}
            sides={properties.sides || 6}
            radius={properties.radius || (element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'ring':
        return (
          <Ring
            {...baseProps}
            {...customProps}
            innerRadius={properties.innerRadius || (element.width || 100) / 4}
            outerRadius={properties.outerRadius || (element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'arc':
        return (
          <Arc
            {...baseProps}
            {...customProps}
            angle={properties.angle || 90}
            innerRadius={properties.innerRadius || 0}
            outerRadius={properties.outerRadius || (element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'wedge':
        return (
          <Wedge
            {...baseProps}
            {...customProps}
            angle={properties.angle || 60}
            radius={properties.radius || (element.width || 100) / 2}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      case 'path':
        return (
          <Path
            {...baseProps}
            {...customProps}
            data={properties.data || 'M 0 0 L 100 0 L 100 100 L 0 100 Z'}
            {...(isHovered ? hoverEffect : {})}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      {/* Affiche la forme fantôme à la position d'origine pendant le drag */}
      {isDragging && dragOrigin && renderShape({
        x: dragOrigin.x,
        y: dragOrigin.y,
        opacity: 0.3,
        shadowColor: properties.shadowColor,
        shadowBlur: properties.shadowBlur,
        shadowOffset: properties.shadowOffset,
        shadowOpacity: properties.shadowOpacity,
        listening: false, // non interactive
        ref: undefined,
        onClick: undefined,
        onTap: undefined,
        onContextMenu: undefined,
        draggable: false,
        onDragStart: undefined,
        onDragMove: undefined,
        onDragEnd: undefined
      })}
      {/* Forme principale */}
      {renderShape({
        x: element.x,
        y: element.y,
        draggable: isEditable,
        onDragStart: handleDragStart,
        onDragEnd: handleDragEnd,
        onDragMove: onDragMove || (() => {}),
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        ...(!isSelected && isHovered ? hoverEffect : {}),
      })}
      {isSelected && isEditable && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox: any, newBox: any) => {
            // Limiter la taille minimale
            if (newBox.width < 10 || newBox.height < 10) {
              return oldBox;
            }
            return newBox;
          }}
          rotateEnabled={true}
          enabledAnchors={[
            'top-left', 'top-center', 'top-right',
            'middle-left', 'middle-right',
            'bottom-left', 'bottom-center', 'bottom-right'
          ]}
          onTransformEnd={handleTransformEnd}
        />
      )}
    </>
  );
};

export default ShapeElementComponent;
