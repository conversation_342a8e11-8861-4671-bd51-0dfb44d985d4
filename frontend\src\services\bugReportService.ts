import { api } from './api';
import {
  BugReport,
  BugReportCreateRequest,
  BugReportUpdateRequest,
  BugReportVoteRequest,
  BugReportResponse,
  BugReportFilters,
  BugReportVoteCount,
  BugReportHistory,
  BugReportStats,
  BugReportComment,
  BugReportCommentRequest
} from '../types/bugReports';
import logger from '@/utils/logger';
import { fetchCsrfToken } from './csrf';
const BASE_URL = '/api/bug-reports';

interface BugReportCommentsResponse {
  data: BugReportComment[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
}

export const bugReportService = {
  /**
   * Récupère tous les rapports de bug avec filtrage et pagination
   */
  getAll: async (filters: BugReportFilters = {}): Promise<BugReportResponse> => {
    const {
      type,
      category,
      status,
      priority,
      search,
      page = 1,
      limit = 10,
      order = 'created_at',
      direction = 'desc',
      user_id,
      show_pending
    } = filters;

    const queryParams = new URLSearchParams();
    if (type) queryParams.append('type', type);
    if (category) queryParams.append('category', category);
    if (status) queryParams.append('status', status);
    if (priority) queryParams.append('priority', priority);
    if (search) queryParams.append('search', search);
    if (user_id) queryParams.append('user_id', user_id);
    if (show_pending !== undefined) queryParams.append('show_pending', show_pending.toString());
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('order', order);
    queryParams.append('direction', direction);

    const url = `${BASE_URL}?${queryParams.toString()}`;
    const response = await api.get<BugReportResponse>(url);
    return response.data;
  },

  /**
   * Récupère un rapport de bug par son ID
   */
  getById: async (id: string): Promise<BugReport> => {
    const response = await api.get<BugReport>(`${BASE_URL}/${id}`);
    return response.data;
  },

  /**
   * Crée un nouveau rapport de bug
   */
  create: async (data: BugReportCreateRequest): Promise<BugReport> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    const response = await api.post<BugReport>(BASE_URL, data, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
    return response.data;
  },

  /**
   * Met à jour un rapport de bug existant
   */
  update: async (id: string, data: BugReportUpdateRequest): Promise<BugReport> => {
    // Le token CSRF est automatiquement ajouté par l'intercepteur d'API
    const response = await api.put<BugReport>(`${BASE_URL}/${id}`, data);
    return response.data;
  },

  /**
   * Supprime un rapport de bug
   */
  delete: async (id: string): Promise<void> => {
    // Le token CSRF est automatiquement ajouté par l'intercepteur d'API
    await api.delete(`${BASE_URL}/${id}`);
  },

  /**
   * Vote pour un rapport de bug
   */
  vote: async (id: string, data: BugReportVoteRequest): Promise<{vote: any, vote_count: BugReportVoteCount}> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    const response = await api.post<{vote: any, vote_count: BugReportVoteCount}>(`${BASE_URL}/${id}/vote`, data, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
    return response.data;
  },

  /**
   * Retire un vote pour un rapport de bug
   */
  removeVote: async (id: string): Promise<{success: boolean, vote_count: BugReportVoteCount}> => {
    // Le token CSRF est automatiquement ajouté par l'intercepteur d'API
    const response = await api.delete<{success: boolean, vote_count: BugReportVoteCount}>(`${BASE_URL}/${id}/vote`);
    return response.data;
  },

  /**
   * Récupère l'historique d'un rapport de bug
   */
  getHistory: async (id: string): Promise<BugReportHistory[]> => {
    const response = await api.get<{ data: BugReportHistory[] } | BugReportHistory[]>(`${BASE_URL}/${id}/history`);
    
    // S'assurer de toujours retourner un tableau
    if (Array.isArray(response.data)) {
      return response.data;
    } else if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data.data || [];
    } else {
      logger.error('Format de réponse inattendu pour l\'historique:', response.data); 
      return [];
    }
  },

  /**
   * Récupère les statistiques des rapports de bug
   */
  getStats: async (): Promise<BugReportStats> => {
    const response = await api.get<BugReportStats>(`${BASE_URL}/stats`);
    return response.data;
  },

  /**
   * Récupère les commentaires d'un rapport de bug
   */
  getComments: async (id: string, page: number = 1, limit: number = 10): Promise<BugReportCommentsResponse> => {
    const response = await api.get<BugReportCommentsResponse>(`${BASE_URL}/${id}/comments?page=${page}&limit=${limit}`);
    return response.data;
  },

  /**
   * Ajoute un commentaire à un rapport de bug
   */
  addComment: async (id: string, data: BugReportCommentRequest): Promise<BugReportComment> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    const response = await api.post<BugReportComment>(`${BASE_URL}/${id}/comments`, data, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
    return response.data;
  },

  /**
   * Met à jour un commentaire d'un rapport de bug
   */
  updateComment: async (id: string, commentId: string, data: BugReportCommentRequest): Promise<BugReportComment> => {
    const response = await api.put<BugReportComment>(`${BASE_URL}/${id}/comments/${commentId}`, data);
    return response.data;
  },

  /**
   * Supprime un commentaire d'un rapport de bug
   */
  deleteComment: async (id: string, commentId: string): Promise<void> => {
    await api.delete(`${BASE_URL}/${id}/comments/${commentId}`);
  },

  /**
   * Supprime plusieurs commentaires d'un rapport de bug
   */
  deleteMultipleComments: async (id: string, commentIds: string[]): Promise<void> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    await api.post(`${BASE_URL}/${id}/comments/delete-multiple`, { commentIds }, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
  },

  /**
   * Clôture plusieurs rapports de bug en même temps (admin/modo uniquement)
   */
  closeMultiple: async (bugReportIds: string[], status: string, adminComment?: string): Promise<{success: boolean, message: string, data: BugReport[]}> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    const response = await api.post<{success: boolean, message: string, data: BugReport[]}>(`${BASE_URL}/close-multiple`, { 
      bugReportIds,
      status,
      adminComment
    }, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
    return response.data;
  },

  /**
   * Supprime plusieurs rapports de bug en même temps (admin/modo uniquement)
   */
  deleteMultiple: async (bugReportIds: string[], raison?: string): Promise<{success: boolean, message: string, count: number}> => {
    await fetchCsrfToken();
    const csrfToken = await fetchCsrfToken();
    const response = await api.post<{success: boolean, message: string, count: number}>(`${BASE_URL}/delete-multiple`, { 
      bugReportIds,
      raison
    }, {
      headers: { 'X-CSRF-Token': csrfToken },
    });
    return response.data;
  },

  /**
   * Récupère les informations du navigateur actuel
   */
  getBrowserInfo: (): { name: string; version: string; mobile: boolean; os: string } => {
    const userAgent = navigator.userAgent;
    
    // Détection du navigateur
    const browsers = [
      { name: 'Chrome', regex: /Chrome\/([0-9.]+)/ },
      { name: 'Firefox', regex: /Firefox\/([0-9.]+)/ },
      { name: 'Safari', regex: /Safari\/([0-9.]+)/ },
      { name: 'Edge', regex: /Edg(e|)\/([0-9.]+)/ },
      { name: 'Opera', regex: /OPR\/([0-9.]+)/ },
      { name: 'IE', regex: /Trident\/.*rv:([0-9.]+)/ }
    ];
    
    let browserName = 'Inconnu';
    let browserVersion = 'Inconnu';
    
    for (const browser of browsers) {
      const match = userAgent.match(browser.regex);
      if (match) {
        browserName = browser.name;
        browserVersion = match[1] || match[2] || 'Inconnu';
        break;
      }
    }
    
    // Détection de l'OS
    let os = 'Inconnu';
    if (userAgent.includes('Windows')) os = 'Windows';
    else if (userAgent.includes('Mac OS')) os = 'MacOS';
    else if (userAgent.includes('Linux')) os = 'Linux';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iOS') || userAgent.includes('iPhone') || userAgent.includes('iPad')) os = 'iOS';
    
    // Détection si mobile
    const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    
    return {
      name: browserName,
      version: browserVersion,
      mobile,
      os
    };
  }
}; 