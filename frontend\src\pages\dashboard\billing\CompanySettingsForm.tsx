import React, { useState, useEffect } from 'react';
import { notify } from '../../../components/Notification';
import { CompanySettings } from '../../../services/invoiceService';
import LoadingSpinner from '../../../components/LoadingSpinner';
import { Save, Info } from 'lucide-react';
import useCompanySettings from '../../../hooks/invoices/useCompanyInvoiceSettings';

const CompanySettingsForm: React.FC = () => {
  const { settings: globalSettings, loading, updateSettings } = useCompanySettings();
  const [settings, setSettings] = useState<CompanySettings>({
    nom: '',
    adresse: '',
    code_postal: '',
    ville: '',
    pays: 'France',
    telephone: '',
    email: '',
    site_web: '',
    siret: '',
    tva: '',
    forme_juridique: '',
    code_ape: '',
    rcs: '',
    capital: '',
    mention_pied_page: '',
  });
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (globalSettings) {
      setSettings(globalSettings);
    }
  }, [globalSettings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer les erreurs lors de la modification
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!settings.nom) {
      newErrors.nom = 'Le nom de l\'entreprise est requis';
    }
    
    if (settings.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.email)) {
      newErrors.email = 'Format d\'email invalide';
    }
    
    if (settings.siret && !/^\d{14}$/.test(settings.siret)) {
      newErrors.siret = 'Format SIRET invalide (14 chiffres)';
    }
    
    if (settings.code_ape && !/^[0-9]{4}[a-zA-Z]?$/.test(settings.code_ape)) {
      newErrors.code_ape = 'Format code APE invalide (exemple: 3811Z)';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSaving(true);
    try {
      await updateSettings(settings);
      notify("Paramètres d'entreprise mis à jour avec succès", 'success');
    } catch (error: any) {
      console.error("Erreur lors de la mise à jour des paramètres d'entreprise:", error);
      // Affichage du message d'erreur détaillé si disponible
      const backendMessage = error?.response?.data?.errors || error?.response?.data?.message;
      if (backendMessage) {
        notify(backendMessage, 'error');
      } else {
        notify("Erreur lors de la mise à jour des paramètres d'entreprise", 'error');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Paramètres de l'entreprise</h2>
      
      <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <Info className="h-5 w-5 text-[#FF7A35]" />
          </div>
          <div className="ml-3">
            <p className="text-sm text-orange-700">
              Ces informations seront affichées sur vos devis, factures et avoirs. Assurez-vous qu'elles sont correctes.
            </p>
          </div>
        </div>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Nom de l'entreprise *
            </label>
            <input
              type="text"
              name="nom"
              value={settings.nom}
              onChange={handleChange}
              className={`w-full rounded-md border ${
                errors.nom ? 'border-red-500' : 'border-gray-300'
              } px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent`}
              required
            />
            {errors.nom && (
              <p className="text-red-500 text-xs">{errors.nom}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Forme juridique
            </label>
            <input
              type="text"
              name="forme_juridique"
              value={settings.forme_juridique}
              onChange={handleChange}
              placeholder="SARL, SAS, EI, etc."
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Adresse
            </label>
            <input
              type="text"
              name="adresse"
              value={settings.adresse}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Code postal
              </label>
              <input
                type="text"
                name="code_postal"
                value={settings.code_postal}
                onChange={handleChange}
                className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              />
            </div>
            
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Ville
              </label>
              <input
                type="text"
                name="ville"
                value={settings.ville}
                onChange={handleChange}
                className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Téléphone
            </label>
            <input
              type="tel"
              name="telephone"
              value={settings.telephone}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={settings.email}
              onChange={handleChange}
              className={`w-full rounded-md border ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              } px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent`}
            />
            {errors.email && (
              <p className="text-red-500 text-xs">{errors.email}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Site web
            </label>
            <input
              type="url"
              name="site_web"
              value={settings.site_web}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              SIRET
            </label>
            <input
              type="text"
              name="siret"
              value={settings.siret}
              onChange={handleChange}
              className={`w-full rounded-md border ${
                errors.siret ? 'border-red-500' : 'border-gray-300'
              } px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent`}
            />
            {errors.siret && (
              <p className="text-red-500 text-xs">{errors.siret}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              TVA Intracommunautaire
            </label>
            <input
              type="text"
              name="tva"
              value={settings.tva}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Code APE
            </label>
            <input
              type="text"
              name="code_ape"
              value={settings.code_ape}
              onChange={handleChange}
              className={`w-full rounded-md border ${
                errors.code_ape ? 'border-red-500' : 'border-gray-300'
              } px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent`}
              placeholder="Ex: 3811Z"
            />
            {errors.code_ape && (
              <p className="text-red-500 text-xs">{errors.code_ape}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              RCS / RM
            </label>
            <input
              type="text"
              name="rcs"
              value={settings.rcs}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              placeholder="Ex: RCS Paris 123456789"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Capital social
            </label>
            <input
              type="text"
              name="capital"
              value={settings.capital}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              placeholder="Ex: 10 000 €"
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Mentions en bas des informations de l'entreprise
            </label>
            <textarea
              name="mention_pied_page"
              value={settings.mention_pied_page}
              onChange={handleChange}
              rows={3}
              className="w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent"
              placeholder="Mentions légales supplémentaires à afficher avec les informations de l'entreprise"
            />
          </div>
        </div>
        
        <div className="mt-6 flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] transition-colors flex items-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
          >
            {saving ? (
              <>
                <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                Enregistrement...
              </>
            ) : (
              <>
                <Save size={18} />
                Enregistrer
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanySettingsForm; 