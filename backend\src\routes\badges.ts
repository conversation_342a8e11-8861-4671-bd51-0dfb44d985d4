import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import { redis } from '../config/redis';
import { sendNewBadgesEmail } from '../services/emailService';
import { getUserSubscriptionLimits } from './configSubscriptions';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

const router = Router();

// Rate limiter pour les routes de badges
const badgesLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minutes
    max: 100, // limite chaque IP à 100 requêtes par fenêtre
    standardHeaders: true,
    legacyHeaders: false
});

// Constantes pour le cache
const CACHE_PREFIX = 'badges:';
const CACHE_DURATION = 3600; // 1 heure en secondes

// Service de cache pour les badges
const badgesCacheService = {
  // Invalider tous les caches relatifs aux badges d'un utilisateur
  invalidateUserCaches: async (userId: string): Promise<void> => {
    try {
      // Invalider les caches de badges
      const badgesPattern = `${CACHE_PREFIX}profile:${userId}`;
      await redis.del(badgesPattern);
      
      // Invalider le cache de date de dernière vérification
      const lastVerifPattern = `${CACHE_PREFIX}derniere-verif:${userId}`;
      await redis.del(lastVerifPattern);
      
      // Invalider le cache des statistiques
      const statsPattern = `${CACHE_PREFIX}stats:${userId}`;
      await redis.del(statsPattern);
      
      // Invalider le cache de croissance d'activité
      const activityCacheKey = `activity_growth:${userId}`;
      await redis.del(activityCacheKey);
      
      logger.info('Caches des badges invalidés pour l\'utilisateur', { userId });
    } catch (error) {
      logger.error('Erreur lors de l\'invalidation des caches de badges:', error);
    }
  }
};

// Route pour récupérer les badges d'un profil spécifique
router.get('/by-profile/:profileId', badgesLimiter, authMiddleware.authenticateToken, async (req: Request, res: Response) => {
  logger.info('Début de la requête GET badges pour un profil spécifique', { 
    profileId: req.params.profileId,
    requesterId: req.user?.userId 
  });

  try {
    const { profileId } = req.params;
    const userId = profileId; // Utiliser directement l'ID fourni comme userId
    
    // Vérifier si les badges sont dans le cache
    const cacheKey = `${CACHE_PREFIX}profile:${userId}`;
    const cachedBadges = await redis.get(cacheKey);
    
    if (cachedBadges) {
      logger.info('Badges récupérés depuis le cache', { userId });
      res.json({
        success: true,
        fromCache: true,
        data: JSON.parse(cachedBadges)
      });
      return;
    }

    // Vérifier si l'utilisateur existe
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      logger.error('Utilisateur non trouvé:', { userId, error: userError });
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }

    // Récupérer les badges de l'utilisateur
    const { data: badges, error } = await supabase
      .from('user_badges')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des badges de l\'utilisateur:', { error, userId });
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des badges'
      });
      return;
    }

    logger.info('Badges de l\'utilisateur récupérés avec succès', { 
      userId,
      badgeCount: badges?.length || 0 
    });
    
    // Mettre en cache les badges
    await redis.setex(cacheKey, CACHE_DURATION, JSON.stringify(badges));
    logger.info('Badges mis en cache', { userId, badgeCount: badges?.length || 0 });

    res.json({
      success: true,
      fromCache: false,
      data: badges
    });
    return;
  } catch (error) {
    logger.error('Exception lors de la récupération des badges de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des badges'
    });
    return;
  }
});

// Route pour récupérer la dernière date de vérification des badges d'un profil
router.get('/derniere-verif/:profileId', badgesLimiter, authMiddleware.authenticateToken, async (req: Request, res: Response) => {
  try {
    const { profileId } = req.params;
    const userId = profileId; // Utiliser directement l'ID fourni comme userId
    
    // Vérifier si l'information est dans le cache
    const cacheKey = `${CACHE_PREFIX}derniere-verif:${userId}`;
    const cachedData = await redis.get(cacheKey);
    
    if (cachedData) {
      logger.info('Dernière vérification des badges récupérée depuis le cache', { userId });
      res.json({
        success: true,
        fromCache: true,
        data: JSON.parse(cachedData)
      });
      return;
    }

    // Vérifier si l'utilisateur existe
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      logger.error('Utilisateur non trouvé:', { userId, error: userError });
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }

    // Récupérer la date de dernière vérification
    const { data, error } = await supabase
      .from('user_badges_verification')
      .select('derniere_verif')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = pas de résultat trouvé
      logger.error('Erreur lors de la récupération de la dernière vérification:', { error, userId });
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la dernière vérification des badges'
      });
      return;
    }

    // Si aucune vérification n'a jamais été effectuée
    const result = {
      derniere_verif: data ? data.derniere_verif : null,
      doit_verifier_badge: true
    };
    
    if (data) {
      // Calculer si 24 heures se sont écoulées depuis la dernière vérification
      const derniere_verif = new Date(data.derniere_verif);
      const maintenant = new Date();
      const diff = maintenant.getTime() - derniere_verif.getTime();
      const heures = Math.floor(diff / (1000 * 60 * 60));
      result.doit_verifier_badge = heures >= 24;
    }
    
    // Mettre en cache le résultat pour 5 minutes seulement car c'est une donnée qui change relativement souvent
    await redis.setex(cacheKey, 300, JSON.stringify(result));
    logger.info('Dernière vérification des badges mise en cache', { userId });

    res.json({
      success: true,
      fromCache: false,
      data: result
    });
    return;
  } catch (error) {
    logger.error('Exception lors de la récupération de la dernière vérification des badges:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière vérification des badges'
    });
    return;
  }
});

// Route pour traiter les badges d'un profil spécifique
router.post('/process-for-profile/:profileId', badgesLimiter, authMiddleware.authenticateToken, async (req: Request, res: Response) => {
  try {
    const { profileId } = req.params;
    const { badges } = req.body;
    const userId = profileId; // Utiliser directement l'ID fourni comme userId

    if (!Array.isArray(badges)) {
      res.status(400).json({
        success: false,
        message: 'La requête doit contenir un tableau de badges'
      });
      return;
    }

    // Vérifier si l'utilisateur existe
    const { data: userRaw, error: userError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', userId)
      .single();

    if (userError || !userRaw) {
      logger.error('Utilisateur non trouvé:', { userId, error: userError });
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }

    // Décrypter les données utilisateur
    const user = await decryptUserDataAsync(userRaw);

    // Récupérer les badges actuels de l'utilisateur
    const { data: existingBadges, error: fetchError } = await supabase
      .from('user_badges')
      .select('badge_id, recompense_recu, is_lifetime')
      .eq('user_id', userId);

    if (fetchError) {
      logger.error('Erreur lors de la récupération des badges existants:', { error: fetchError, userId });
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification des badges existants'
      });
      return;
    }

    // Créer un map des badges existants
    const existingBadgesMap = new Map();
    existingBadges?.forEach(badge => {
      existingBadgesMap.set(badge.badge_id, { 
        exists: true, 
        recompense_recu: badge.recompense_recu,
        is_lifetime: badge.is_lifetime
      });
    });

    const badgesToCreate = [];
    const jobiTransactions = [];
    const newBadges = [];

    // Analyser les badges pour déterminer les actions nécessaires
    for (const badge of badges) {
      const existingBadge = existingBadgesMap.get(badge.badge_id);
      
      // Badge à créer: l'utilisateur n'a pas le badge mais remplit les conditions
      if (!existingBadge && badge.meets_condition) {
        badgesToCreate.push({
          user_id: userId,
          badge_id: badge.badge_id,
          recompense_recu: true,
          is_lifetime: Boolean(badge.is_lifetime)
        });
        
        // Ajouter une transaction Jobi pour la récompense
        jobiTransactions.push({
          userId,
          montant: badge.recompense_jobi,
          operation: 'plus',
          titre: `Badge "${badge.title}" obtenu${badge.is_lifetime ? ' (à vie)' : ''}`,
          description: `${badge.recompense_jobi} Jobi offerts pour l'obtention du badge ${badge.title}${badge.is_lifetime ? ' à vie' : ''}`
        });
        
        newBadges.push({
          badge_id: badge.badge_id,
          title: badge.title,
          description: badge.description || '',
          recompense_jobi: badge.recompense_jobi,
          is_lifetime: Boolean(badge.is_lifetime)
        });
      } 
      // Badge à supprimer: l'utilisateur a le badge mais ne remplit plus les conditions
      // Vérifier que le badge n'est pas à vie
      else if (existingBadge && !badge.meets_condition && !existingBadge.is_lifetime) {
        // Au lieu de mettre à jour le badge, on le supprime complètement
        const { error: deleteError } = await supabase
          .from('user_badges')
          .delete()
          .eq('user_id', userId)
          .eq('badge_id', badge.badge_id);

        if (deleteError) {
          logger.error('Erreur lors de la suppression du badge:', { 
            error: deleteError, 
            userId, 
            badgeId: badge.badge_id 
          });
          continue; // Passer au badge suivant en cas d'erreur
        }
        
        // Invalider le cache des badges après la suppression
        await badgesCacheService.invalidateUserCaches(userId);
        
        // Créer une liste de badges perdus pour notifier l'utilisateur
        const lostBadges = [];
        
        // Si l'utilisateur avait reçu la récompense, lui retirer les Jobi
        if (existingBadge.recompense_recu) {
          jobiTransactions.push({
            userId,
            montant: badge.recompense_jobi,
            operation: 'moins',
            titre: `Perte du badge ${badge.title}`,
            description: `${badge.recompense_jobi} Jobi retirés suite à la perte du badge ${badge.title}`
          });
          
          lostBadges.push({
            badge_id: badge.badge_id,
            title: badge.title,
            description: badge.description || '',
            recompense_jobi: badge.recompense_jobi,
            is_lifetime: Boolean(badge.is_lifetime)
          });
          
          // Créer une notification dans le dashboard pour informer l'utilisateur
          try {
            await supabase
              .from('user_notifications')
              .insert({
                user_id: userId,
                type: 'system',
                title: 'Badge perdu',
                content: `Vous avez perdu le badge "${badge.title}" et ${badge.recompense_jobi} Jobi ont été retirés de votre compte.`,
                link: '/dashboard/badges',
                is_read: false,
                is_archived: false
              });
              
            // Invalider le cache Redis pour le compteur de notifications
            const cacheKey = `notifications_count:${userId}`;
            await redis.del(cacheKey);
            
            // Invalider le cache des notifications
            const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
            if (notificationsCacheKeys.length > 0) {
              await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
            }
          } catch (notificationError) {
            logger.error('Erreur lors de la création de la notification de perte de badge:', notificationError);
          }
          
          // Envoyer un email pour informer l'utilisateur
          if (lostBadges.length > 0) {
            try {
              // Récupérer le profil de l'utilisateur pour obtenir le prénom et le nom
              const { data: userProfile, error: profileError } = await supabase
                .from('user_profil')
                .select('prenom, nom')
                .eq('user_id', userId)
                .single();
        
              // Déchiffrer les données du profil
              const decryptedProfile = await decryptProfilDataAsync(userProfile);
        
              // Formater le nom d'utilisateur pour l'email
              let username = '';
              if (decryptedProfile && decryptedProfile.prenom) {
                username = decryptedProfile.nom 
                  ? `${decryptedProfile.prenom} ${decryptedProfile.nom.charAt(0).toUpperCase()}.` 
                  : decryptedProfile.prenom;
              }
        
              // Ajouter l'email à la file d'attente
              await sendNewBadgesEmail(user.email, {
                badges: [],
                lostBadges: lostBadges,
                totalJobi: -badge.recompense_jobi,
                username
              });
        
              logger.info('Email de notification de perte de badge ajouté à la file d\'attente', {
                userId,
                badgeCount: lostBadges.length
              });
            } catch (emailError) {
              logger.error('Erreur lors de l\'ajout de l\'email de notification de perte de badge à la file d\'attente:', emailError);
            }
          }
        }
      }
    }

    // Calculer le nombre de badges perdus
    let lostBadgesCount = 0;
    for (const badge of badges) {
      const existingBadge = existingBadgesMap.get(badge.badge_id);
      if (existingBadge && !badge.meets_condition) {
        lostBadgesCount++;
      }
    }

    // Traiter les créations de badges
    if (badgesToCreate.length > 0) {
      // Sécurité supplémentaire : ne garder qu'un seul badge par (user_id, badge_id)
      const uniqueBadgesToCreate = Array.from(
        new Map(badgesToCreate.map(b => [`${b.user_id}_${b.badge_id}`, b])).values()
      );
      // Déduplication des notifications (newBadges)
      const uniqueNewBadges = Array.from(
        new Map(newBadges.map(b => [`${userId}_${b.badge_id}`, b])).values()
      );
      // Déduplication des transactions Jobi
      const uniqueJobiTransactions = Array.from(
        new Map(jobiTransactions.map(t => [`${t.userId}_${t.titre}_${t.montant}_${t.operation}`, t])).values()
      );

      const { error: createError } = await supabase
        .from('user_badges')
        .insert(uniqueBadgesToCreate);

      if (createError) {
        logger.error('Erreur lors de la création des badges:', { error: createError, userId });
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la création des badges'
        });
        return;
      }

      // Remplacer les tableaux originaux par les versions dédupliquées pour la suite du traitement
      newBadges.length = 0;
      newBadges.push(...uniqueNewBadges);
      jobiTransactions.length = 0;
      jobiTransactions.push(...uniqueJobiTransactions);
    }

    // Effectuer les transactions Jobi
    let totalJobi = 0;
    
    // Utiliser un Set pour éviter les doublons de transactions
    const uniqueTransactions = new Map();
    
    // Dédupliquer les transactions en utilisant une clé unique (titre + montant + opération)
    jobiTransactions.forEach(transaction => {
      const transactionKey = `${transaction.titre}_${transaction.montant}_${transaction.operation}`;
      if (!uniqueTransactions.has(transactionKey)) {
        uniqueTransactions.set(transactionKey, transaction);
      } else {
        logger.info(`Transaction en doublon ignorée: ${transactionKey}`);
      }
    });
    
    // Utiliser les transactions dédupliquées
    for (const transaction of uniqueTransactions.values()) {
      try {
        // Récupérer le solde actuel
        const { data: jobiData, error: jobiError } = await supabase
          .from('user_jobi')
          .select('montant')
          .eq('user_id', transaction.userId)
          .single();

        if (jobiError && jobiError.code !== 'PGRST116') {
          logger.error('Erreur lors de la récupération du solde Jobi:', { error: jobiError, userId: transaction.userId });
          continue;
        }

        // Calculer le nouveau montant
        let currentAmount = jobiData?.montant || 0;
        let newAmount = currentAmount;
        
        if (transaction.operation === 'plus') {
          newAmount = currentAmount + transaction.montant;
        } else {
          newAmount = Math.max(0, currentAmount - transaction.montant);
        }

        // Si l'utilisateur n'a pas d'entrée dans user_jobi, en créer une nouvelle
        if (jobiError && jobiError.code === 'PGRST116') {
          // Insérer une nouvelle entrée
          const { error: insertError } = await supabase
            .from('user_jobi')
            .insert({
              user_id: transaction.userId,
              montant: newAmount,
            });

          if (insertError) {
            logger.error('Erreur lors de la création du solde Jobi:', { error: insertError, userId: transaction.userId });
            continue;
          }
        } else {
          // Mettre à jour le solde existant
          const { error: updateError } = await supabase
            .from('user_jobi')
            .update({ montant: newAmount })
            .eq('user_id', transaction.userId);

          if (updateError) {
            logger.error('Erreur lors de la mise à jour du solde Jobi:', { error: updateError, userId: transaction.userId });
            continue;
          }
        }

        // Enregistrer dans l'historique
        const { error: historyError } = await supabase
          .from('user_jobi_historique')
          .insert({
            user_id: transaction.userId,
            montant: transaction.operation === 'moins' ? -transaction.montant : transaction.montant,
            titre: transaction.titre,
            description: transaction.description
          });

        if (historyError) {
          logger.error('Erreur lors de l\'enregistrement dans l\'historique Jobi:', { error: historyError, userId: transaction.userId });
        }
        
        // Invalider le cache Redis pour le solde Jobi
        const cacheKey = `jobi_balance_${transaction.userId}`;
        await redis.del(cacheKey);
        
        // Mettre à jour le totalJobi
        totalJobi += transaction.montant;
      } catch (txError) {
        logger.error('Erreur lors du traitement d\'une transaction Jobi:', txError);
      }
    }

    // Si nous avons de nouveaux badges et que l'utilisateur accepte les notifications par email, envoyer un email
    if (newBadges.length > 0) {
      try {
        // Récupérer le profil de l'utilisateur pour obtenir le prénom et le nom
        const { data: userProfile, error: profileError } = await supabase
          .from('user_profil')
          .select('prenom, nom')
          .eq('user_id', userId)
          .single();

        // Déchiffrer les données du profil
        const decryptedProfile = await decryptProfilDataAsync(userProfile);

        // Formater le nom d'utilisateur pour l'email (prénom + première lettre du nom suivie d'un point)
        let username = '';
        if (decryptedProfile && decryptedProfile.prenom) {
          username = decryptedProfile.nom 
            ? `${decryptedProfile.prenom} ${decryptedProfile.nom.charAt(0).toUpperCase()}.` 
            : decryptedProfile.prenom;
        }

        // Ajouter l'email à la file d'attente
        await sendNewBadgesEmail(user.email, {
          badges: newBadges,
          totalJobi,
          username
        });

        logger.info('Email de notification de nouveaux badges ajouté à la file d\'attente', {
          userId,
          badgeCount: newBadges.length
        });
      } catch (emailError) {
        logger.error('Erreur lors de l\'ajout de l\'email de notification de badges à la file d\'attente:', emailError);
        // On continue même en cas d'erreur d'envoi d'email
      }
    }

    // Ajouter une notification dans le dashboard pour chaque badge obtenu
    if (newBadges.length > 0) {
      try {
        // Pour chaque badge, créer une notification dans la table user_notifications
        const notifications = newBadges.map(badge => ({
          user_id: userId,
          type: 'system', // Utilisation du type system car il n'y a pas de type spécifique pour les badges
          title: 'Nouveau badge obtenu !',
          content: `Félicitations ! Vous avez obtenu le badge "${badge.title}"${badge.is_lifetime ? ' (à vie)' : ''} et gagné ${badge.recompense_jobi} Jobi.`,
          link: '/dashboard/badges', // Lien vers la page de badges
          is_read: false,
          is_archived: false
        }));

        // Insérer toutes les notifications en une seule requête
        const { error: notificationError } = await supabase
          .from('user_notifications')
          .insert(notifications);

        if (notificationError) {
          logger.error('Erreur lors de la création des notifications de badge:', { error: notificationError, userId });
        } else {
          logger.info('Notifications de badge créées avec succès', { userId, count: notifications.length });
          
          // Invalider le cache Redis pour le compteur de notifications
          const cacheKey = `notifications_count:${userId}`;
          await redis.del(cacheKey);
          
          // Invalider le cache des notifications
          const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
          if (notificationsCacheKeys.length > 0) {
            await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
          }
        }
      } catch (notificationError) {
        logger.error('Exception lors de la création des notifications de badge:', notificationError);
      }
    }

    // Mettre à jour la date de dernière vérification des badges
    try {
      // Vérifier si une entrée existe déjà
      const { data: existingData, error: existingError } = await supabase
        .from('user_badges_verification')
        .select('id')
        .eq('user_id', userId)
        .single();

      const currentDate = new Date().toISOString();

      if (existingError && existingError.code === 'PGRST116') {
        // Aucune entrée existante, insérer une nouvelle
        const { error: insertError } = await supabase
          .from('user_badges_verification')
          .insert({
            user_id: userId,
            derniere_verif: currentDate
          });

        if (insertError) {
          logger.error('Erreur lors de la création de la date de dernière vérification:', { error: insertError, userId });
        } else {
          logger.info('Date de dernière vérification créée avec succès', { userId, date: currentDate });
        }
      } else if (!existingError) {
        // Entrée existante, la mettre à jour
        const { error: updateError } = await supabase
          .from('user_badges_verification')
          .update({ derniere_verif: currentDate })
          .eq('user_id', userId);

        if (updateError) {
          logger.error('Erreur lors de la mise à jour de la date de dernière vérification:', { error: updateError, userId });
        } else {
          logger.info('Date de dernière vérification mise à jour avec succès', { userId, date: currentDate });
        }
      }
    } catch (verificationError) {
      logger.error('Exception lors de la mise à jour de la date de dernière vérification:', verificationError);
      // Continuer même en cas d'erreur
    }

    // Invalider les caches des badges pour cet utilisateur
    await badgesCacheService.invalidateUserCaches(userId);

    res.json({
      success: true,
      message: 'Badges traités avec succès',
      newBadges: newBadges
    });
    return;
  } catch (error) {
    logger.error('Exception lors du traitement des badges pour un profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du traitement des badges'
    });
    return;
  }
});

// Route pour obtenir toutes les statistiques nécessaires aux badges d'un utilisateur
router.get('/profile-stats/:userId', badgesLimiter, authMiddleware.authenticateToken, async (req: Request, res: Response) => {
  logger.info('Début de la requête GET statistiques pour badges', { 
    profileId: req.params.userId,
    requesterId: req.user?.userId 
  });

  try {
    const { userId } = req.params;
    
    logger.info(`Traitement des statistiques pour l'ID utilisateur: ${userId}`);
    
    // Vérifier si les statistiques sont dans le cache
    const cacheKey = `${CACHE_PREFIX}stats:${userId}`;
    const cachedStats = await redis.get(cacheKey);
    
    if (cachedStats) {
      logger.info('Statistiques pour badges récupérées depuis le cache', { userId });
      res.json({
        success: true,
        fromCache: true,
        data: JSON.parse(cachedStats)
      });
      return;
    }
    
    // Vérifier si l'utilisateur existe
    logger.info(`Vérification de l'existence de l'utilisateur avec ID: ${userId}`);
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error(`Erreur lors de la recherche de l'utilisateur:`, { 
        userId, 
        errorCode: userError.code,
        errorMessage: userError.message,
        errorDetails: userError.details
      });
      
      // Si l'erreur est "PGRST116" (pas de résultat), c'est une 404
      if (userError.code === 'PGRST116') {
        res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé'
        });
        return;
      }
      
      // Sinon c'est une erreur serveur
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de l\'utilisateur'
      });
      return;
    }
    
    if (!user) {
      logger.error(`Utilisateur non trouvé avec l'ID: ${userId}`);
      res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
      return;
    }
    
    logger.info(`Utilisateur trouvé avec succès: ${userId}`);
    
    // Récupérer les informations d'abonnement premium en utilisant la fonction centralisée
    let premiumSince = null;
    let isCurrentlyPremium = false;
    try {
      // Utiliser la fonction getUserSubscriptionLimits pour récupérer le statut premium
      const { startDate, isPremium } = await getUserSubscriptionLimits(userId);
      
      // Si l'utilisateur est premium, récupérer sa date de début d'abonnement
      if (isPremium && startDate) {
        premiumSince = startDate;
      }
      
      isCurrentlyPremium = isPremium;
      logger.info(`Informations d'abonnement trouvées pour l'utilisateur: ${userId} - Premium depuis: ${premiumSince}, Actuellement premium: ${isCurrentlyPremium}`);
    } catch (error) {
      logger.warn(`Pas d'abonnement premium trouvé pour l'utilisateur: ${userId}`);
      // Ne pas bloquer le reste du traitement si on ne trouve pas d'abonnement
    }
    
    // Récupérer les statistiques des avis (requête simple)
    const { data: reviews, error: reviewsError } = await supabase
      .from('user_reviews')
      .select('id, note, created_at, reponse, reponse_date')
      .eq('target_user_id', userId);
      
    if (reviewsError) {
      logger.error('Erreur lors de la récupération des avis:', reviewsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des avis'
      });
      return;
    }
    
    // Calculer les statistiques des avis (calculs simples en JavaScript)
    const totalReviews = reviews?.length || 0;
    const averageRating = totalReviews > 0 
      ? Number((reviews.reduce((acc, review) => acc + review.note, 0) / totalReviews).toFixed(1)) 
      : 0;
    
    // Calculer le nombre d'avis auxquels l'utilisateur a répondu
    const reviewsWithResponse = reviews?.filter(review => review.reponse !== null && review.reponse !== '').length || 0;
    // Taux de réponse aux avis
    const responseToReviewRate = totalReviews > 0 ? reviewsWithResponse / totalReviews : 0;
    
    // Calculer le nombre consécutif d'avis 5 étoiles
    let consecutiveFiveStars = 0;
    if (reviews && reviews.length > 0) {
      // Trier les avis par date de création (plus récent au plus ancien)
      const sortedReviews = [...reviews].sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      
      for (const review of sortedReviews) {
        if (review.note === 5) {
          consecutiveFiveStars++;
        } else {
          break; // Arrêter dès qu'on trouve un avis qui n'est pas 5 étoiles
        }
      }
    }
    
    // Récupérer les offres (candidatures) acceptées
    const { data: acceptedProposals, error: proposalsError } = await supabase
      .from('user_mission_candidature')
      .select(`
        id, 
        jobbeur_id, 
        mission_id, 
        statut, 
        created_at, 
        date_contre_offre, 
        date_contre_offre_jobbeur, 
        date_refus, 
        date_acceptation,
        mission:user_missions(user_id)
      `)
      .eq('jobbeur_id', userId)
      .eq('statut', 'acceptée');
      
    if (proposalsError) {
      logger.error('Erreur lors de la récupération des candidatures acceptées:', proposalsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des candidatures'
      });
      return;
    }
    
    // Compter les missions réalisées (offres acceptées)
    const totalMissionsCount = acceptedProposals?.length || 0;
    
    // Calculer le nombre de connexions (nombre de personnes différentes à qui une offre a été proposée et acceptée)
    const uniqueClients = new Set();
    acceptedProposals?.forEach(proposal => {
      // Accéder aux propriétés de mission de manière sécurisée
      const missionData = proposal.mission as any;
      if (missionData && missionData.user_id) {
        uniqueClients.add(missionData.user_id);
      }
    });
    const connectionsCount = uniqueClients.size;
    
    // Récupérer toutes les candidatures pour calculer le délai de réponse
    const { data: allProposals, error: allProposalsError } = await supabase
      .from('user_mission_candidature')
      .select(`
        id, 
        statut, 
        created_at, 
        date_contre_offre, 
        date_contre_offre_jobbeur, 
        date_refus,
        date_acceptation
      `)
      .eq('jobbeur_id', userId);
      
    if (allProposalsError) {
      logger.error('Erreur lors de la récupération de toutes les candidatures:', allProposalsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des candidatures'
      });
      return;
    }
    
    // Calculer le délai de réponse moyen
    let totalResponseTime = 0;
    let responsesCount = 0;
    
    allProposals?.forEach(proposal => {
      // Date de création (quand l'offre a été reçue)
      const creationDate = new Date(proposal.created_at).getTime();
      
      // Trouver la première date de réponse disponible
      let responseDate = null;
      
      if (proposal.date_contre_offre_jobbeur) {
        responseDate = new Date(proposal.date_contre_offre_jobbeur).getTime();
      } else if (proposal.date_contre_offre) {
        responseDate = new Date(proposal.date_contre_offre).getTime();
      } else if (proposal.date_refus) {
        responseDate = new Date(proposal.date_refus).getTime();
      } else if (proposal.date_acceptation) {
        responseDate = new Date(proposal.date_acceptation).getTime();
      }
      
      // Si une date de réponse existe, calculer le délai
      if (responseDate) {
        const responseTime = (responseDate - creationDate) / 1000; // en secondes
        // S'assurer que le délai est positif et raisonnable (maximum 1 semaine = 604800 secondes)
        if (responseTime > 0 && responseTime < 604800) {
          totalResponseTime += responseTime;
          responsesCount++;
        }
      }
    });
    
    // Calculer le délai de réponse moyen en secondes
    const avgResponseTime = responsesCount > 0 ? Math.round(totalResponseTime / responsesCount) : 0;
    
    // Récupérer les missions de l'utilisateur
    const { data: missions, error: missionsError } = await supabase
      .from('user_missions')
      .select('id, statut, category_id, subcategory_id, created_at, ville')
      .eq('user_id', userId);
      
    if (missionsError) {
      logger.error('Erreur lors de la récupération des missions:', missionsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des missions'
      });
      return;
    }
    
    // Compter les missions par catégorie
    const completedMissions = missions?.filter(m => m.statut === 'terminee') || [];
    
    // Trouver les missions annulées et la date de dernière annulation
    const cancelledMissions = missions?.filter(m => m.statut === 'annulee') || [];
    const cancelledMissionsCount = cancelledMissions.length;
    
    // Trouver la date de dernière annulation (en triant les missions annulées par date de création décroissante)
    let lastCancellationDate = null;
    if (cancelledMissions.length > 0) {
      const sortedCancelledMissions = [...cancelledMissions].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      lastCancellationDate = sortedCancelledMissions[0].created_at;
    }
    
    // Compter les missions par catégorie
    const gardeningMissionsCount = completedMissions.filter(m => m.category_id === '1').length; // Jardinage
    const diyMissionsCount = completedMissions.filter(m => m.category_id === '2').length; // Bricolage
    const petCareMissionsCount = completedMissions.filter(m => m.category_id === '3').length; // Garde d'animaux
    const servicePersonneMissionsCount = completedMissions.filter(m => m.category_id === '4').length; // Services à la personne
    const evenementRestauMissionsCount = completedMissions.filter(m => m.category_id === '5').length; // Événementiel & Restauration
    const servicesAdminMissionsCount = completedMissions.filter(m => m.category_id === '6').length; // Services administratifs
    const transportLogistiqueMissionsCount = completedMissions.filter(m => m.category_id === '7').length; // Transport & Logistique
    const comMarketingMissionsCount = completedMissions.filter(m => m.category_id === '8').length; // Communication & Marketing
    const educationFormationMissionsCount = completedMissions.filter(m => m.category_id === '9').length; // Éducation & Formation
    const informatiqueMissionsCount = completedMissions.filter(m => m.category_id === '10').length; // Informatique
    const artsDivertissementMissionsCount = completedMissions.filter(m => m.category_id === '11').length; // Arts & Divertissement
    const bienEtreSanteMissionsCount = completedMissions.filter(m => m.category_id === '12').length; // Bien-être & Santé
    const servicesEntreprisesMissionsCount = completedMissions.filter(m => m.category_id === '13').length; // Services aux entreprises
    const artisanatCreationMissionsCount = completedMissions.filter(m => m.category_id === '14').length; // Artisanat & Création
    const sportLoisirsMissionsCount = completedMissions.filter(m => m.category_id === '15').length; // Sport & Loisirs
    const immobilierHabitatMissionsCount = completedMissions.filter(m => m.category_id === '16').length; // Immobilier & Habitat
    const automobileTransportMissionsCount = completedMissions.filter(m => m.category_id === '17').length; // Automobile & Transport
    const decorationDesignMissionsCount = completedMissions.filter(m => m.category_id === '18').length; // Décoration & Design
    const servicesFinanciersMissionsCount = completedMissions.filter(m => m.category_id === '19').length; // Services financiers
    const tourismeVoyagesMissionsCount = completedMissions.filter(m => m.category_id === '20').length; // Tourisme & Voyages
    const renovationTravauxMissionsCount = completedMissions.filter(m => m.category_id === '21').length; // Rénovation & Travaux
    
    // Autres catégories (si elles existent dans votre système)
    const piscineSpaMissionsCount = completedMissions.filter(m => m.category_id === '22').length; // Piscine & Spa
    const modeBeauteMissionsCount = completedMissions.filter(m => m.category_id === '23').length; // Mode & Beauté
    const securiteProtectionMissionsCount = completedMissions.filter(m => m.category_id === '24').length; // Sécurité & Protection
    const environnementEcologieMissionsCount = completedMissions.filter(m => m.category_id === '25').length; // Environnement & Écologie
    
    // Missions d'été (juin à septembre)
    const summerMissionsCount = completedMissions.filter(m => {
      const date = new Date(m.created_at);
      const month = date.getMonth();
      const year = date.getFullYear();
      return month >= 5 && month <= 8; // juin (5) à septembre (8)
    }).length;
    
    // Catégories uniques
    const uniqueCategories = [...new Set(completedMissions.map(m => m.category_id))];
    
    // Récupérer les services de l'utilisateur
    const { data: services, error: servicesError } = await supabase
      .from('user_services')
      .select('*')
      .eq('user_id', userId);
      
    if (servicesError) {
      logger.error('Erreur lors de la récupération des services:', servicesError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des services'
      });
      return;
    }

    // Mapper les services pour supprimer les champs user_id et id
    const formattedServices = services?.map(service => {
      const { user_id, id, ...rest } = service;
      return rest;
    }) || [];

    // Récupérer les statistiques de parrainages
    const { data: referrals, error: referralsError } = await supabase
      .from('user_referrals')
      .select('id')
      .eq('referrer_id', userId)
      .eq('status', 'rewarded');
      
    if (referralsError) {
      logger.error('Erreur lors de la récupération des parrainages:', referralsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des parrainages'
      });
      return;
    }
    
    // Récupérer les favoris
    const { data: favorites, error: favoritesError } = await supabase
      .from('user_favorites')
      .select('id')
      .eq('favorite_user_id', userId);
      
    if (favoritesError) {
      logger.error('Erreur lors de la récupération des favoris:', favoritesError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des favoris'
      });
      return;
    }
    
    // Calculer les villes uniques des missions
    const serviceCities = [...new Set(missions?.map(m => m.ville).filter(Boolean) || [])];
    
    // Récupérer les statistiques de connexion
    const { data: connections, error: connectionsError } = await supabase
      .from('user_login_history')
      .select('login_date')
      .eq('user_id', userId)
      .gte('login_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
      
    if (connectionsError) {
      logger.error('Erreur lors de la récupération des connexions:', connectionsError);
      // Ne pas bloquer même en cas d'erreur
    }
    
    // Calculer le nombre de jours de connexion par semaine
    const uniqueConnectionDays = new Set();
    connections?.forEach(conn => {
      const day = new Date(conn.login_date).toDateString();
      uniqueConnectionDays.add(day);
    });
    const weeklyConnectionDays = uniqueConnectionDays.size;
    
    // Calcul du taux de croissance d'activité sur 3 mois
    let activity_growth_percentage = 0;
    
    // Définir les périodes pour la comparaison
    const now = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);
    
    // Vérifier si un calcul de croissance d'activité est déjà en cache
    const activityCacheKey = `activity_growth:${userId}`;
    let cachedActivityGrowth = await redis.get(activityCacheKey);
    
    // Utiliser le cache si disponible et a moins de 24h
    if (cachedActivityGrowth) {
      try {
        const { percentage, timestamp } = JSON.parse(cachedActivityGrowth);
        const cacheTime = new Date(timestamp);
        const cacheAge = now.getTime() - cacheTime.getTime();
        const oneDayMs = 24 * 60 * 60 * 1000;
        
        if (cacheAge < oneDayMs) {
          logger.info(`Utilisation du cache pour le taux de croissance d'activité de l'utilisateur ${userId}`);
          activity_growth_percentage = percentage;
        } else {
          // Cache expiré, le recalculer
          logger.info(`Cache expiré pour le taux de croissance d'activité de l'utilisateur ${userId}`);
        }
      } catch (error) {
        logger.error(`Erreur lors du parsing du cache d'activité pour l'utilisateur ${userId}:`, error);
      }
    }
    
    // Si pas de cache valide, calculer le taux de croissance
    if (!cachedActivityGrowth || activity_growth_percentage === 0) {
      // Récupérer les propositions acceptées et missions réalisées pour les deux périodes
      const recentMissions = completedMissions.filter(mission => 
        new Date(mission.created_at) >= threeMonthsAgo && 
        new Date(mission.created_at) <= now
      );
      
      const previousMissions = completedMissions.filter(mission => 
        new Date(mission.created_at) >= sixMonthsAgo && 
        new Date(mission.created_at) < threeMonthsAgo
      );
      
      // Calculer le volume d'activité pour chaque période
      // Le volume d'activité inclut les missions terminées, les propositions envoyées et les interactions
      // Nous donnons différents poids à chaque type d'activité
      
      try {
        // Récupérer les propositions envoyées pour les deux périodes
        const { data: recentProposals } = await supabase
          .from('user_mission_candidature')
          .select('*')
          .eq('jobbeur_id', userId)
          .gte('created_at', threeMonthsAgo.toISOString())
          .lte('created_at', now.toISOString());
        
        const { data: previousProposals } = await supabase
          .from('user_mission_candidature')
          .select('*')
          .eq('jobbeur_id', userId)
          .gte('created_at', sixMonthsAgo.toISOString())
          .lt('created_at', threeMonthsAgo.toISOString());
        
        // Récupérer les interactions (messages, likes, commentaires)
        const { data: recentInteractions } = await supabase
          .from('user_mission_comments')
          .select('*')
          .eq('user_id', userId)
          .gte('created_at', threeMonthsAgo.toISOString())
          .lte('created_at', now.toISOString());
        
        const { data: previousInteractions } = await supabase
          .from('user_mission_comments')
          .select('*')
          .eq('user_id', userId)
          .gte('created_at', sixMonthsAgo.toISOString())
          .lt('created_at', threeMonthsAgo.toISOString());
        
        // Récupérer les likes donnés par l'utilisateur
        const { data: recentLikes } = await supabase
          .from('user_mission_likes')
          .select('*')
          .eq('user_id', userId)
          .gte('created_at', threeMonthsAgo.toISOString())
          .lte('created_at', now.toISOString());
        
        const { data: previousLikes } = await supabase
          .from('user_mission_likes')
          .select('*')
          .eq('user_id', userId)
          .gte('created_at', sixMonthsAgo.toISOString())
          .lt('created_at', threeMonthsAgo.toISOString());
        
        // Récupérer les statistiques de visite du profil
        const { data: recentProfileViews } = await supabase
          .from('user_profile_views')
          .select('*')
          .eq('profile_id', userId)
          .gte('created_at', threeMonthsAgo.toISOString())
          .lte('created_at', now.toISOString());
        
        const { data: previousProfileViews } = await supabase
          .from('user_profile_views')
          .select('*')
          .eq('profile_id', userId)
          .gte('created_at', sixMonthsAgo.toISOString())
          .lt('created_at', threeMonthsAgo.toISOString());
        
        // Récupérer les consultations de services
        const { data: recentServiceViews } = await supabase
          .from('user_service_views')
          .select('*')
          .eq('service_owner_id', userId)
          .gte('created_at', threeMonthsAgo.toISOString())
          .lte('created_at', now.toISOString());
        
        const { data: previousServiceViews } = await supabase
          .from('user_service_views')
          .select('*')
          .eq('service_owner_id', userId)
          .gte('created_at', sixMonthsAgo.toISOString())
          .lt('created_at', threeMonthsAgo.toISOString());
        
        const recentActivityVolume = 
          (recentMissions.length * 10) + 
          ((recentProposals?.length || 0) * 5) + 
          ((recentInteractions?.length || 0) * 3) +
          ((recentLikes?.length || 0) * 2) +
          ((recentProfileViews?.length || 0) * 1) +
          ((recentServiceViews?.length || 0) * 1);
        
        const previousActivityVolume = 
          (previousMissions.length * 10) + 
          ((previousProposals?.length || 0) * 5) + 
          ((previousInteractions?.length || 0) * 3) +
          ((previousLikes?.length || 0) * 2) +
          ((previousProfileViews?.length || 0) * 1) +
          ((previousServiceViews?.length || 0) * 1);
        
        // Calculer le pourcentage de croissance
        if (previousActivityVolume > 0) {
          activity_growth_percentage = Math.round(
            ((recentActivityVolume - previousActivityVolume) / previousActivityVolume) * 100
          );
        } else if (recentActivityVolume > 0) {
          activity_growth_percentage = 100;
        }
        
        // Stocker le résultat dans le cache Redis
        await redis.set(
          activityCacheKey, 
          JSON.stringify({ 
            percentage: activity_growth_percentage, 
            timestamp: now.toISOString() 
          }),
          'EX', 
          86400 // Expiration après 24h
        );
        
      } catch (error) {
        logger.error(`Erreur lors du calcul du taux de croissance pour l'utilisateur ${userId}:`, error);
        // En cas d'erreur, utiliser 0 par défaut pour ne pas bloquer le reste du traitement
        activity_growth_percentage = 0;
      }
    }
    
    // Construire l'objet final des statistiques
    const badgeStats = {
      // Infos de base
      // user_id: userId,
      
      // Stats d'avis
      avg_rating: averageRating,
      reviews_count: totalReviews,
      response_rate: responseToReviewRate, // Nouveau: taux de réponse aux avis (décimal entre 0 et 1)
      consecutive_five_star_reviews: consecutiveFiveStars,
      
      // Stats de croissance d'activité
      activity_growth_percentage,
      
      // Stats de missions
      missions_count: totalMissionsCount,
      cancelled_missions_count: cancelledMissionsCount,
      last_cancellation_date: lastCancellationDate,
      gardening_missions_count: gardeningMissionsCount,
      diy_missions_count: diyMissionsCount,
      pet_care_missions_count: petCareMissionsCount,
      service_personne_missions_count: servicePersonneMissionsCount,
      evenement_restau_missions_count: evenementRestauMissionsCount,
      services_admin_missions_count: servicesAdminMissionsCount,
      transport_logistics_missions_count: transportLogistiqueMissionsCount,
      communication_marketing_missions_count: comMarketingMissionsCount,
      education_training_missions_count: educationFormationMissionsCount,
      informatique_missions_count: informatiqueMissionsCount,
      arts_spectacles_missions_count: artsDivertissementMissionsCount,
      bien_etre_sante_missions_count: bienEtreSanteMissionsCount,
      services_entreprises_missions_count: servicesEntreprisesMissionsCount,
      artisanat_creation_missions_count: artisanatCreationMissionsCount,
      sport_loisirs_missions_count: sportLoisirsMissionsCount,
      immobilier_habitat_missions_count: immobilierHabitatMissionsCount,
      automobile_transport_missions_count: automobileTransportMissionsCount,
      decoration_design_missions_count: decorationDesignMissionsCount,
      services_financiers_missions_count: servicesFinanciersMissionsCount,
      tourisme_voyages_missions_count: tourismeVoyagesMissionsCount,
      renovation_travaux_missions_count: renovationTravauxMissionsCount,
      piscine_spa_missions_count: piscineSpaMissionsCount,
      mode_beaute_missions_count: modeBeauteMissionsCount,
      securite_protection_missions_count: securiteProtectionMissionsCount,
      environnement_ecologie_missions_count: environnementEcologieMissionsCount,
      summer_missions_count: {
        [new Date().getFullYear()]: summerMissionsCount
      },
      different_categories_count: uniqueCategories.length,
      
      // Stats de services
      services_offered: formattedServices,
      cities_worked_in: serviceCities,
      
      // Autres stats
      referral_count: referrals?.length || 0,
      favorite_count: favorites?.length || 0,
      weekly_connection_days: weeklyConnectionDays,
      
      // Nouvelles stats
      avg_response_time: avgResponseTime, // Délai de réponse moyen en secondes
      connections_count: connectionsCount, // Nombre de personnes différentes à qui une offre a été proposée et acceptée
      completed_trainings_count: 0, // À implémenter
      on_time_completion_count: 0, // À implémenter
      perfect_rating_count: reviews?.filter(r => r.note === 5)?.length || 0 // Nombre d'avis parfaits
    };
    
    // Mettre en cache les statistiques pour 1 heure
    await redis.setex(cacheKey, CACHE_DURATION, JSON.stringify(badgeStats));
    logger.info('Statistiques pour badges mises en cache', { userId });
    
    res.json({
      success: true,
      fromCache: false,
      data: badgeStats
    });
    return;
  } catch (error) {
    logger.error('Exception lors de la récupération des statistiques pour badges:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
    return;
  }
});

export default router; 