import React, { useState, useEffect, useRef } from 'react';
import { Box, CircularProgress, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import { missionsApi, Mission } from './missionsApi';
import { notify } from '../../../components/Notification';
import MissionsLayout from './MissionsLayout';
import { FilterBar } from './FilterBar/FilterBar';
import ProposalModal from './ProposalModal';
import { MissionCardRef } from './MissionCard';


const LoadingIndicator = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  padding: '20px',
});

const AllMissions: React.FC = () => {
  const [missions, setMissions] = useState<Mission[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const loadingRef = useRef<HTMLDivElement | null>(null);

  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [budgetFilters, setBudgetFilters] = useState<string[]>([]);
  const [paymentFilters, setPaymentFilters] = useState<string[]>([]);
  const [categoryFilters, setCategoryFilters] = useState<string[]>([]);
  const [subcategoryFilters, setSubcategoryFilters] = useState<string[]>([]);
  const [likedFilter, setLikedFilter] = useState<string[]>([]);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [showProposalModal, setShowProposalModal] = useState(false);
  const [currentMissionCardRef, setCurrentMissionCardRef] = useState<React.RefObject<MissionCardRef> | null>(null);

  // Ajouter un état pour suivre si la barre de filtres est ouverte
  const [isFilterBarExpanded, setIsFilterBarExpanded] = useState(false);

  const [offresEnvoyeesFilter, setOffresEnvoyeesFilter] = useState<string[]>([]);

  const toggleFilter = (
    filter: string,
    category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees'
  ) => {
    if (category === 'offres_envoyees') {
      setOffresEnvoyeesFilter(prev =>
        prev.includes(filter) ? prev.filter(f => f !== filter) : [...prev, filter]
      );
      return;
    }

    const setters: Record<string, React.Dispatch<React.SetStateAction<string[]>>> = {
      status: setStatusFilters,
      budget: setBudgetFilters,
      payment: setPaymentFilters,
      category: setCategoryFilters,
      subcategory: setSubcategoryFilters,
      liked: setLikedFilter
    };

    // Gestion spéciale pour les filtres de paiement et budget (un seul choix possible)
    if (category === 'payment' || category === 'budget') {
      const setter = category === 'payment' ? setPaymentFilters : setBudgetFilters;
      setter(prev => {
        if (prev.includes(filter)) {
          return []; // Si on clique sur un filtre déjà actif, on le désactive
        }
        return [filter]; // Sinon on active uniquement ce filtre
      });
      return;
    }

    // Gestion normale pour les autres filtres
    if (setters[category]) {
      setters[category](prev => {
        if (prev.includes(filter)) {
          return prev.filter(f => f !== filter);
        }
        return [...prev, filter];
      });
    }
  };

  const isFilterActive = (
    filter: string,
    category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees'
  ): boolean => {
    if (category === 'offres_envoyees') {
      return offresEnvoyeesFilter.includes(filter);
    }

    const filters: Record<string, string[]> = {
      status: statusFilters,
      budget: budgetFilters,
      payment: paymentFilters,
      category: categoryFilters,
      subcategory: subcategoryFilters,
      liked: likedFilter
    };
    return filters[category]?.includes(filter) || false;
  };

  useEffect(() => {
    if (statusFilters.length > 0 || budgetFilters.length > 0 ||
        paymentFilters.length > 0 || categoryFilters.length > 0 ||
        subcategoryFilters.length > 0 || likedFilter ||
        searchTerm.length > 0 || offresEnvoyeesFilter.length > 0) {
      fetchMissions(1);
    }
  }, [
    statusFilters,
    budgetFilters,
    paymentFilters,
    categoryFilters,
    subcategoryFilters,
    likedFilter,
    searchTerm,
    offresEnvoyeesFilter
  ]);

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '900px',
      threshold: 0.1, // Réduire le seuil pour déclencher plus tôt
    };

    const observer = new IntersectionObserver((entries) => {
      const target = entries[0];
      if (target.isIntersecting && hasMore && !loading && !loadingMore) {
        const nextPage = Math.ceil(missions.length / 6) + 1;
        fetchMissions(nextPage, true);
      }
    }, options);

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore, missions]);

  useEffect(() => {
    // Ajouter un écouteur d'événement pour ouvrir la modale de proposition depuis la modale de commentaires
    const handleOpenProposalModal = (event: CustomEvent) => {
      const { missionId } = event.detail;
      const mission = missions.find(m => m.id === missionId);
      if (mission) {
        setSelectedMission(mission);
        setShowProposalModal(true);
      }
    };

    window.addEventListener('open-proposal-modal', handleOpenProposalModal as EventListener);

    return () => {
      window.removeEventListener('open-proposal-modal', handleOpenProposalModal as EventListener);
    };
  }, [missions]);

  const fetchMissions = async (pageNumber: number, isLoadMore: boolean = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const limit = 6; // Limite constante par page

      // Séparer les statuts normaux et le statut urgent
      const normalStatuses = statusFilters.filter(status => ['en_cours', 'terminee', 'annulee', 'en_moderation'].includes(status));
      const isUrgentSelected = statusFilters.includes('urgent');

      // Construire les paramètres de filtrage
      const filterParams = {
        status: normalStatuses,
        search: searchTerm,
        categories: categoryFilters,
        subcategories: subcategoryFilters,
        budget_types: budgetFilters,
        payment_methods: paymentFilters,
        is_urgent: isUrgentSelected ? true : undefined,
        liked: likedFilter.includes('liked'),
        offer_status: offresEnvoyeesFilter.includes('offres_envoyees') ? ['sent'] : undefined,
        sort_by: 'date_creation' // Ajouter un tri explicite par date de création
      };

      const response = await missionsApi.getAllMissions(pageNumber, limit, filterParams);

      const newMissions = response.data || [];

      if (isLoadMore) {
        setMissions(prev => [...prev, ...newMissions]);
      } else {
        setMissions(newMissions);
      }

      // Mettre à jour hasMore en fonction du nombre total de missions
      setHasMore(response.total > (pageNumber * limit));
    } catch (error) {
      notify('Erreur lors de la récupération des missions', 'error');
      if (!isLoadMore) {
        setMissions([]);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleMissionUpdate = (updatedMission: Mission) => {
    setMissions(prev => prev.map(mission =>
      mission.id === updatedMission.id ? updatedMission : mission
    ));
  };

  const handleCategoryFilterChange = (categories: string[], subcategories: string[]) => {
    setCategoryFilters(categories);
    setSubcategoryFilters(subcategories);
  };

  // Fonction pour trier les missions par date de création (de la plus récente à la plus ancienne)
  const sortByCreatedAt = (missions: Mission[]) => {
    return [...missions].sort((a, b) => {
      // Convertir les dates en objets Date pour comparer les timestamps
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return dateB - dateA; // Ordre décroissant (plus récent en premier)
    });
  };

  // Filtrer les missions
  const filteredMissions = sortByCreatedAt(
    missions
      .filter(mission => {
        if (offresEnvoyeesFilter.length === 0) {
          return !mission.applications || mission.applications.length === 0;
        } else if (offresEnvoyeesFilter.includes('offres_envoyees')) {
          return mission.applications && mission.applications.length > 0;
        }
        return true;
      })
      .filter(mission => {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          mission.titre.toLowerCase().includes(searchLower) ||
          mission.description.toLowerCase().includes(searchLower) ||
          mission.ville.toLowerCase().includes(searchLower) ||
          mission.code_postal.includes(searchLower);

        const matchesStatus = statusFilters.length === 0 || (
          (statusFilters.includes('urgent') && mission.is_urgent)
        );

        const matchesBudget = budgetFilters.length === 0 || (
          (budgetFilters.includes('budget_defini') && mission.budget_defini) ||
          (budgetFilters.includes('budget_non_defini') && !mission.budget_defini)
        );

        const matchesPayment = paymentFilters.length === 0 || (
          (paymentFilters.includes('jobi_only') && mission.payment_method === 'jobi_only') ||
          (paymentFilters.includes('direct_only') && mission.payment_method === 'direct_only') ||
          (paymentFilters.includes('both') && mission.payment_method === 'both')
        );

        const matchesCategories = categoryFilters.length === 0 ||
          (mission.category_id && categoryFilters.includes(mission.category_id));

        const matchesSubcategories = subcategoryFilters.length === 0 ||
          (mission.subcategory_id && subcategoryFilters.includes(mission.subcategory_id));

        return matchesSearch && matchesStatus && matchesBudget &&
               matchesPayment && matchesCategories && matchesSubcategories;
      })
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    fetchMissions(1);
  };

  // Ajouter la fonction de réinitialisation des filtres
  const resetAllFilters = () => {
    setStatusFilters([]);
    setBudgetFilters([]);
    setPaymentFilters([]);
    setCategoryFilters([]);
    setSubcategoryFilters([]);
    setLikedFilter([]);
    setSearchTerm('');
  };

  // Déterminer si des filtres sont actifs
  const hasActiveFilters = statusFilters.length > 0 ||
                          budgetFilters.length > 0 ||
                          paymentFilters.length > 0 ||
                          categoryFilters.length > 0 ||
                          subcategoryFilters.length > 0 ||
                          likedFilter.length > 0 ||
                          searchTerm.length > 0;

  const headerContent = (
    <>
      <FilterBar
        statusFilters={statusFilters}
        budgetFilters={budgetFilters}
        paymentFilters={paymentFilters}
        categoryFilters={categoryFilters}
        subcategoryFilters={subcategoryFilters}
        toggleFilter={toggleFilter}
        isFilterActive={isFilterActive}
        onCategoryFilterChange={handleCategoryFilterChange}
        isExpanded={isFilterBarExpanded}
        onToggleExpand={(expanded: boolean) => setIsFilterBarExpanded(expanded)}
        searchTerm={searchTerm}
        onSearch={handleSearch}
        showSearchField={true}
        showOffresEnvoyeesFilter={true}
        offresEnvoyeesFilter={offresEnvoyeesFilter}
      />

      {/* Bouton de réinitialisation des filtres */}
      {hasActiveFilters && (
        <Box sx={{
          padding: {
            xs: '0 0 16px 0',
            sm: '0 0 16px 0'
          },
          display: 'flex',
          justifyContent: 'flex-start'
        }}>
          <Button
            onClick={resetAllFilters}
            startIcon={<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
            </svg>}
            sx={{
              color: '#FF6B2C',
              borderColor: '#FFE4BA',
              '&:hover': {
                backgroundColor: '#FFF8F3',
                borderColor: '#FF6B2C'
              }
            }}
            variant="outlined"
            size="small"
          >
            Réinitialiser les filtres
          </Button>
        </Box>
      )}
    </>
  );

  const footerContent = hasMore && !loading && (
    <LoadingIndicator ref={loadingRef}>
      {loadingMore && <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />}
    </LoadingIndicator>
  );

  // Message d'état vide personnalisé
  const getEmptyMessage = () => {
    if (hasActiveFilters) {
      return (
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          textAlign: 'center',
          padding: '20px'
        }}>
          <div>Aucune mission ne correspond à vos critères de recherche</div>
          <button
            onClick={resetAllFilters}
            style={{
              backgroundColor: '#FF6B2C',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold',
              transition: 'background-color 0.3s',
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#FF965E'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#FF6B2C'}
          >
            Réinitialiser les filtres
          </button>
        </Box>
      );
    }
    return "Aucune mission disponible pour le moment";
  };

  return (
    <>
      <MissionsLayout
        missions={filteredMissions}
        loading={loading}
        emptyMessage={getEmptyMessage()}
        onUpdate={handleMissionUpdate}
        headerContent={headerContent}
        footerContent={footerContent}
        showStatus={false}
        onMakeProposal={(mission, missionCardRef) => {
          // Ouvrir le modal de proposition
          setSelectedMission(mission);
          setCurrentMissionCardRef(missionCardRef as React.RefObject<MissionCardRef> || null);
          setShowProposalModal(true);
        }}
      />

      {selectedMission && (
        <ProposalModal
          open={showProposalModal}
          onClose={() => setShowProposalModal(false)}
          mission={selectedMission}
          onProposalSubmitted={(proposalData) => {
            setShowProposalModal(false);
            // Mettre à jour l'état userProposal dans MissionCard sans faire de requête API
            if (currentMissionCardRef && currentMissionCardRef.current && proposalData) {
              currentMissionCardRef.current.updateUserProposal(proposalData);
            }
          }}
        />
      )}
    </>
  );
};

export default AllMissions;