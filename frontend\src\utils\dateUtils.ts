/**
 * Fonctions utilitaires pour le formatage des dates
 */

/**
 * Formate une date au format jour/mois/année
 * @param dateString Chaîne de caractères représentant une date
 * @returns Date formatée (ex: 01/01/2023)
 */
export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

/**
 * Formate une date au format jour/mois/année heure:minute
 * @param dateString Chaîne de caractères représentant une date
 * @returns Date et heure formatées (ex: 01/01/2023 14:30)
 */
export const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Calcule la différence entre une date et maintenant sous forme relative
 * @param dateString Chaîne de caractères représentant une date
 * @returns Texte représentant la différence (ex: "il y a 2 jours")
 */
export const formatRelativeTime = (dateString: string | null | undefined): string => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInSec = Math.floor(diffInMs / 1000);
  
  if (diffInSec < 60) return 'à l\'instant';
  
  const diffInMin = Math.floor(diffInSec / 60);
  if (diffInMin < 60) return `il y a ${diffInMin} minute${diffInMin > 1 ? 's' : ''}`;
  
  const diffInHours = Math.floor(diffInMin / 60);
  if (diffInHours < 24) return `il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) return `il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) return `il y a ${diffInMonths} mois`;
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return `il y a ${diffInYears} an${diffInYears > 1 ? 's' : ''}`;
}; 