import React, { useState } from 'react';
import { 
  Typography, 
  Box,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  CircularProgress,
  IconButton,
  Button,
  Paper,
  Divider,
  Avatar
} from '@mui/material';
import { Calendar, Download, X, Info, CheckCircle2 } from 'lucide-react';
import ModalPortal from '../../components/ModalPortal';
import { motion } from 'framer-motion';

interface ExportOptionsModalProps {
  open: boolean;
  onClose: () => void;
  onExportIcal: () => void;
  onSyncWithGoogle: () => void;
  isSyncGoogleLoading: boolean;
}

const ExportOptionsModal: React.FC<ExportOptionsModalProps> = ({
  open,
  onClose,
  onExportIcal,
  onSyncWithGoogle,
  isSyncGoogleLoading
}) => {
  // Animation hover sur les cartes
  const [hoveredCard, setHoveredCard] = useState<'ical' | 'google' | null>(null);
  const [selectedCard, setSelectedCard] = useState<'ical' | 'google' | null>(null);

  // Variantes d'animation pour le contenu de la modale
  const contentVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.3,
        ease: 'easeOut'
      } 
    }
  };

  // Animation pour les cartes
  const cardVariants = {
    hover: { 
      y: -8,
      boxShadow: '0 12px 20px rgba(255, 107, 44, 0.1)',
      transition: { duration: 0.2 }
    }
  };

  // Fonction pour gérer le clic sur une carte
  const handleCardClick = (type: 'ical' | 'google') => {
    setSelectedCard(type);
    if (type === 'ical') {
      onExportIcal();
      // Réinitialiser après un délai pour montrer la sélection visuellement
      setTimeout(() => setSelectedCard(null), 1500);
    } else if (type === 'google') {
      onSyncWithGoogle();
    }
  };

  return (
    <ModalPortal 
      isOpen={open} 
      onBackdropClick={onClose}
      closeOnBackdropClick={true}
      zIndex={1200}
    >
      <motion.div
        initial="hidden"
        animate="visible"
        variants={contentVariants}
        style={{ width: '100%', maxWidth: '650px', margin: '0 auto' }}
      >
        <Paper 
          elevation={3}
          sx={{ 
            borderRadius: '20px',
            overflow: 'hidden',
            width: '100%',
            maxHeight: { xs: '85vh', sm: '90vh' },
            display: 'flex',
            flexDirection: 'column',
            background: 'linear-gradient(to bottom, #FFF8F3, #FFFFFF)',
            boxShadow: '0px 10px 40px rgba(0, 0, 0, 0.1)'
          }}
        >
          {/* Header - fixed at top */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: { xs: '20px 24px 16px 24px', sm: '28px 32px 20px 32px' },
            position: 'relative',
            flexShrink: 0
          }}>
            <Typography 
              variant="h5" 
              sx={{ 
                fontWeight: 700, 
                color: '#333',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: '-6px',
                  left: '0',
                  width: '40px',
                  height: '3px',
                  backgroundColor: '#FF6B2C',
                  borderRadius: '10px'
                }
              }}
            >
              Exporter votre planning
            </Typography>
            <IconButton 
              onClick={onClose} 
              size="small" 
              sx={{ 
                color: '#666',
                bgcolor: 'rgba(0,0,0,0.03)',
                '&:hover': {
                  bgcolor: 'rgba(255,107,44,0.1)',
                  color: '#FF6B2C'
                },
                transition: 'all 0.2s ease'
              }}
            >
              <X size={20} />
            </IconButton>
          </Box>

          {/* Divider */}
          <Divider sx={{ opacity: 0.6 }} />

          {/* Content - scrollable */}
          <Box sx={{ 
            padding: { xs: '20px 24px', sm: '28px 32px' },
            overflowY: 'auto',
            flex: '1 1 auto' 
          }}>
            <Typography 
              variant="body1" 
              sx={{ 
                mb: 4, 
                color: '#555',
                fontSize: '1.05rem'
              }}
            >
              Choisissez votre méthode d'exportation préférée :
            </Typography>

            <Grid container spacing={3}>
              {/* Option 1: Export iCal */}
              <Grid size={{ xs: 12, sm: 6 }}>
                <motion.div
                  whileHover="hover"
                  variants={cardVariants}
                  style={{ height: '100%' }}
                >
                  <Card 
                    elevation={0}
                    sx={{ 
                      height: '100%',
                      transition: 'all 0.3s ease',
                      borderRadius: '16px',
                      overflow: 'hidden',
                      border: selectedCard === 'ical' 
                        ? '2px solid #FF6B2C' 
                        : hoveredCard === 'ical' 
                          ? '1px solid rgba(255, 107, 44, 0.5)' 
                          : '1px solid #eee',
                      bgcolor: selectedCard === 'ical' ? 'rgba(255, 228, 186, 0.2)' : '#fff',
                      boxShadow: hoveredCard === 'ical' ? '0 8px 24px rgba(0, 0, 0, 0.08)' : 'none'
                    }}
                    onMouseEnter={() => setHoveredCard('ical')}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <CardActionArea 
                      onClick={() => handleCardClick('ical')}
                      sx={{ 
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1.5
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', position: 'relative', pb: 3 }}>
                        {selectedCard === 'ical' && (
                          <Avatar 
                            sx={{ 
                              position: 'absolute', 
                              top: -10, 
                              right: -10,
                              bgcolor: '#4CAF50',
                              width: 32,
                              height: 32,
                              boxShadow: '0 3px 6px rgba(0,0,0,0.1)'
                            }}
                          >
                            <CheckCircle2 size={18} />
                          </Avatar>
                        )}
                        <Box sx={{ 
                          background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.12), rgba(255, 150, 94, 0.12))',
                          borderRadius: '50%',
                          width: '80px',
                          height: '80px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto 24px auto',
                          boxShadow: '0 6px 16px rgba(255, 107, 44, 0.15)',
                          transition: 'all 0.3s ease',
                          transform: hoveredCard === 'ical' ? 'scale(1.05)' : 'scale(1)'
                        }}>
                          <Download size={35} color="#FF6B2C" />
                        </Box>
                        <Typography 
                          variant="h6" 
                          gutterBottom 
                          sx={{ 
                            fontWeight: 700, 
                            color: '#333',
                            mb: 2
                          }}
                        >
                          Télécharger fichier iCal
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            mb: 3, 
                            color: '#666',
                            lineHeight: 1.6,
                            fontSize: '0.9rem'
                          }}
                        >
                          Téléchargez un fichier .ics que vous pourrez importer dans n'importe quelle application de calendrier.
                        </Typography>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ 
                            mt: 'auto',
                            color: '#FF6B2C',
                            borderColor: 'rgba(255, 107, 44, 0.5)',
                            borderRadius: '30px',
                            px: 3,
                            py: 1,
                            fontSize: '0.9rem',
                            fontWeight: 600,
                            textTransform: 'none',
                            '&:hover': {
                              borderColor: '#FF6B2C',
                              backgroundColor: 'rgba(255, 107, 44, 0.08)'
                            },
                            boxShadow: hoveredCard === 'ical' ? '0 4px 12px rgba(255, 107, 44, 0.18)' : 'none',
                            transition: 'all 0.3s ease'
                          }}
                        >
                          {selectedCard === 'ical' ? 'Téléchargé !' : 'Télécharger'}
                        </Button>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </motion.div>
              </Grid>

              {/* Option 2: Google Calendar Sync */}
              <Grid size={{ xs: 12, sm: 6 }}>
                <motion.div
                  whileHover="hover"
                  variants={cardVariants}
                  style={{ height: '100%' }}
                >
                  <Card 
                    elevation={0}
                    sx={{ 
                      height: '100%',
                      transition: 'all 0.3s ease',
                      borderRadius: '16px',
                      overflow: 'hidden',
                      border: selectedCard === 'google' 
                        ? '2px solid #FF6B2C' 
                        : hoveredCard === 'google' 
                          ? '1px solid rgba(255, 107, 44, 0.5)' 
                          : '1px solid #eee',
                      bgcolor: selectedCard === 'google' ? 'rgba(255, 228, 186, 0.2)' : '#fff',
                      boxShadow: hoveredCard === 'google' ? '0 8px 24px rgba(0, 0, 0, 0.08)' : 'none'
                    }}
                    onMouseEnter={() => setHoveredCard('google')}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <CardActionArea 
                      onClick={() => handleCardClick('google')}
                      disabled={isSyncGoogleLoading}
                      sx={{ 
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1.5
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', position: 'relative', pb: 3 }}>
                        {selectedCard === 'google' && (
                          <Avatar 
                            sx={{ 
                              position: 'absolute', 
                              top: -10, 
                              right: -10,
                              bgcolor: '#4CAF50',
                              width: 32,
                              height: 32,
                              boxShadow: '0 3px 6px rgba(0,0,0,0.1)'
                            }}
                          >
                            <CheckCircle2 size={18} />
                          </Avatar>
                        )}
                        <Box sx={{ 
                          background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.12), rgba(255, 150, 94, 0.12))',
                          borderRadius: '50%',
                          width: '80px',
                          height: '80px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto 24px auto',
                          boxShadow: '0 6px 16px rgba(255, 107, 44, 0.15)',
                          transition: 'all 0.3s ease',
                          transform: hoveredCard === 'google' ? 'scale(1.05)' : 'scale(1)'
                        }}>
                          <Calendar size={35} color="#FF6B2C" />
                        </Box>
                        <Typography 
                          variant="h6" 
                          gutterBottom 
                          sx={{ 
                            fontWeight: 700, 
                            color: '#333',
                            mb: 2
                          }}
                        >
                          Synchroniser avec Google Agenda
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            mb: 3, 
                            color: '#666',
                            lineHeight: 1.6,
                            fontSize: '0.9rem'
                          }}
                        >
                          Ajoutez directement votre planning à Google Agenda avec mise à jour automatique toutes les 24 heures.
                        </Typography>
                        <Button
                          variant="outlined"
                          disabled={isSyncGoogleLoading}
                          sx={{ 
                            mt: 'auto',
                            color: '#FF6B2C',
                            borderColor: 'rgba(255, 107, 44, 0.5)',
                            borderRadius: '30px',
                            px: 3,
                            py: 1,
                            fontSize: '0.9rem',
                            fontWeight: 600,
                            textTransform: 'none',
                            '&:hover': {
                              borderColor: '#FF6B2C',
                              backgroundColor: 'rgba(255, 107, 44, 0.08)'
                            },
                            boxShadow: hoveredCard === 'google' ? '0 4px 12px rgba(255, 107, 44, 0.18)' : 'none',
                            transition: 'all 0.3s ease'
                          }}
                        >
                          {isSyncGoogleLoading ? (
                            <>
                              <CircularProgress size={16} sx={{ color: '#FF6B2C', mr: 1 }} />
                              Synchronisation...
                            </>
                          ) : (
                            selectedCard === 'google' ? 'Synchronisé !' : 'Synchroniser'
                          )}
                        </Button>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </motion.div>
              </Grid>
            </Grid>

            {/* Info box with tips */}
            <Box sx={{ 
              mt: 4, 
              p: 3, 
              borderRadius: '12px',
              background: 'linear-gradient(to right, #FFF8F3, rgba(255, 228, 186, 0.3))',
              border: '1px solid rgba(255, 107, 44, 0.15)',
              display: 'flex',
              alignItems: 'flex-start',
              gap: 1.5,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.03)'
            }}>
              <Info size={22} style={{ color: '#FF6B2C', marginTop: '2px', flexShrink: 0 }} />
              <Typography 
                variant="body2" 
                sx={{ 
                  color: '#555',
                  lineHeight: 1.7,
                  fontSize: '0.95rem'
                }}
              >
                <strong>Astuce :</strong> La synchronisation avec Google Agenda vous permet d'obtenir des mises à jour automatiques de votre planning. Vos missions seront actualisées automatiquement toutes les 24 heures sans avoir à réimporter le fichier. Une fois configurée, cette synchronisation fonctionne même si vous n'êtes pas connecté à l'application.
              </Typography>
            </Box>
          </Box>

          {/* Footer - fixed at bottom */}
          <Box 
            sx={{ 
              padding: { xs: '16px 24px 20px', sm: '20px 32px 28px' },
              borderTop: '1px solid rgba(0, 0, 0, 0.06)',
              display: 'flex',
              justifyContent: 'center',
              flexShrink: 0
            }}
          >
            <Button 
              onClick={onClose}
              variant="text"
              sx={{
                color: '#666',
                fontSize: '1rem',
                fontWeight: 500,
                textTransform: 'none',
                borderRadius: '30px',
                px: 4,
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.06)',
                  color: '#FF6B2C'
                }
              }}
            >
              Fermer
            </Button>
          </Box>
        </Paper>
      </motion.div>
    </ModalPortal>
  );
};

export default ExportOptionsModal; 