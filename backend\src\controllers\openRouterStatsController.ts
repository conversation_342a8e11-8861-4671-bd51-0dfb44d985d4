import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

// Importer la constante depuis openRouterController.ts pour garantir la cohérence
const OPENROUTER_DAILY_CALLS_CACHE_KEY = 'openrouter:daily_calls';

const CACHE_KEY_PREFIX = 'openrouter:stats:';
const CACHE_DURATION = 5 * 60; // 5 minutes en secondes

// Les endpoints principaux sont :
// - getOpenRouterUsageHistory
// - getDailyStats
// - getMonthlyStats
// - getAvailableModels
// - getOpenRouterDailyCount
// - getGlobalStats
//
// Chacun utilise uniquement la requête SQL directe, sans cache ni fallback.

/**
 * Récupère l'historique d'utilisation de l'API OpenRouter avec filtres et pagination
 */
export const getOpenRouterUsageHistory = async (req: Request, res: Response) => {
  try {
    // Paramètres de pagination et de filtrage
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;
    const serviceType = req.query.serviceType as string;
    const model = req.query.model as string;
    const userId = req.query.userId as string;
    const sortBy = (req.query.sortBy as string) || 'created_at';
    const sortOrder = (req.query.sortOrder as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // Offset pour la pagination
    const offset = (page - 1) * limit;

    // Base de la requête pour le comptage
    let countQuery = supabase
      .from('openrouter_api_usage')
      .select('*', { count: 'exact', head: true });

    if (startDate) {
      countQuery = countQuery.gte('created_at', startDate);
    }
    if (endDate) {
      countQuery = countQuery.lte('created_at', endDate);
    }
    if (serviceType) {
      countQuery = countQuery.eq('service_type', serviceType);
    }
    if (model) {
      countQuery = countQuery.eq('model', model);
    }
    if (userId) {
      countQuery = countQuery.eq('user_id', userId);
    }

    const countResult = await countQuery;
    const count = countResult.count;
    const countError = countResult.error;

    if (countError) {
      logger.error('Erreur lors du comptage des enregistrements d\'utilisation d\'OpenRouter', {
        error: countError,
        filters: { startDate, endDate, serviceType, model, userId }
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors du comptage des enregistrements',
        error: countError.message
      });
    }

    // Base de la requête pour les données
    let dataQuery = supabase
      .from('openrouter_api_usage')
      .select('*, users(email)');

    if (startDate) {
      dataQuery = dataQuery.gte('created_at', startDate);
    }
    if (endDate) {
      dataQuery = dataQuery.lte('created_at', endDate);
    }
    if (serviceType) {
      dataQuery = dataQuery.eq('service_type', serviceType);
    }
    if (model) {
      dataQuery = dataQuery.eq('model', model);
    }
    if (userId) {
      dataQuery = dataQuery.eq('user_id', userId);
    }

    dataQuery = dataQuery
      .order(sortBy, { ascending: sortOrder === 'ASC' })
      .range(offset, offset + limit - 1);

    const { data, error } = await dataQuery;

    if (error) {
      logger.error('Erreur lors de la récupération des enregistrements d\'utilisation d\'OpenRouter', {
        error,
        filters: { startDate, endDate, serviceType, model, userId },
        pagination: { page, limit, offset }
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des enregistrements',
        error: error.message
      });
    }

    // Formater les résultats
    const result = {
      success: true,
      data,
      pagination: {
        page,
        limit,
        totalItems: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des statistiques d\'utilisation d\'OpenRouter', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques d\'utilisation',
      error: error.message
    });
  }
};

/**
 * Récupère les statistiques d'utilisation quotidiennes
 */
export const getDailyStats = async (req: Request, res: Response) => {
  try {
    // Paramètres de filtrage
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;
    const serviceType = req.query.serviceType as string;
    const model = req.query.model as string;

    // Récupérer toutes les données brutes filtrées
    let query = supabase
      .from('openrouter_api_usage')
      .select('created_at, service_type, model, prompt_tokens, completion_tokens, total_tokens');

    // Appliquer les filtres
    if (startDate) {
      query = query.gte('created_at', `${startDate}T00:00:00Z`);
    }
    if (endDate) {
      query = query.lte('created_at', `${endDate}T23:59:59Z`);
    }
    if (serviceType) {
      query = query.eq('service_type', serviceType);
    }
    if (model) {
      query = query.eq('model', model);
    }

    // Exécuter la requête
    const { data: rawData, error } = await query.order('created_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des données brutes pour les statistiques quotidiennes', {
        error,
        filters: { startDate, endDate, serviceType, model }
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques quotidiennes',
        error: error.message
      });
    }

    // Transformer les données brutes en format quotidien
    interface DailyStatItem {
      day: string;
      service_type: string;
      model: string;
      request_count: number;
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    }

    interface DailyStatsMap {
      [key: string]: DailyStatItem;
    }

    const dailyStats: DailyStatsMap = {};

    if (rawData) {
      rawData.forEach(item => {
        const date = new Date(item.created_at);
        const day = date.toISOString().split('T')[0];
        const key = `${day}-${item.service_type}-${item.model}`;

        if (!dailyStats[key]) {
          dailyStats[key] = {
            day,
            service_type: item.service_type,
            model: item.model,
            request_count: 0,
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          };
        }

        dailyStats[key].request_count++;
        dailyStats[key].prompt_tokens += (item.prompt_tokens || 0);
        dailyStats[key].completion_tokens += (item.completion_tokens || 0);
        dailyStats[key].total_tokens += (item.total_tokens || 0);
      });
    }

    // Convertir en tableau et trier
    const data = Object.values(dailyStats).sort((a: any, b: any) =>
      new Date(b.day).getTime() - new Date(a.day).getTime()
    );

    // Formater les résultats
    const result = {
      success: true,
      data
    };

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des statistiques quotidiennes d\'OpenRouter', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques quotidiennes',
      error: error.message
    });
  }
};

/**
 * Récupère les statistiques d'utilisation mensuelles
 */
export const getMonthlyStats = async (req: Request, res: Response) => {
  try {
    // Paramètres de filtrage
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;
    const serviceType = req.query.serviceType as string;
    const model = req.query.model as string;

    // Récupérer toutes les données brutes filtrées
    let query = supabase
      .from('openrouter_api_usage')
      .select('created_at, service_type, model, prompt_tokens, completion_tokens, total_tokens');

    // Appliquer les filtres
    if (startDate) {
      query = query.gte('created_at', `${startDate}T00:00:00Z`);
    }
    if (endDate) {
      query = query.lte('created_at', `${endDate}T23:59:59Z`);
    }
    if (serviceType) {
      query = query.eq('service_type', serviceType);
    }
    if (model) {
      query = query.eq('model', model);
    }

    // Exécuter la requête
    const { data: rawData, error } = await query.order('created_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des données brutes pour les statistiques mensuelles', {
        error,
        filters: { startDate, endDate, serviceType, model }
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques mensuelles',
        error: error.message
      });
    }

    // Transformer les données brutes en format mensuel
    interface MonthlyStatItem {
      month: string;
      service_type: string;
      model: string;
      request_count: number;
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    }

    interface MonthlyStatsMap {
      [key: string]: MonthlyStatItem;
    }

    const monthlyStats: MonthlyStatsMap = {};

    if (rawData) {
      rawData.forEach(item => {
        const date = new Date(item.created_at);
        const month = date.toISOString().substring(0, 7); // Format YYYY-MM
        const key = `${month}-${item.service_type}-${item.model}`;

        if (!monthlyStats[key]) {
          monthlyStats[key] = {
            month,
            service_type: item.service_type,
            model: item.model,
            request_count: 0,
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          };
        }

        monthlyStats[key].request_count++;
        monthlyStats[key].prompt_tokens += (item.prompt_tokens || 0);
        monthlyStats[key].completion_tokens += (item.completion_tokens || 0);
        monthlyStats[key].total_tokens += (item.total_tokens || 0);
      });
    }

    // Convertir en tableau et trier
    const data = Object.values(monthlyStats).sort((a: any, b: any) =>
      new Date(b.month).getTime() - new Date(a.month).getTime()
    );

    // Formater les résultats
    const result = {
      success: true,
      data
    };

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des statistiques mensuelles d\'OpenRouter', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques mensuelles',
      error: error.message
    });
  }
};

/**
 * Récupère les modèles disponibles pour le filtrage
 */
export const getAvailableModels = async (_req: Request, res: Response) => {
  try {
    // Récupérer la liste des modèles distincts
    const { data, error } = await supabase
      .from('openrouter_api_usage')
      .select('model');

    if (error) {
      logger.error('Erreur lors de la récupération des modèles disponibles', {
        error
      });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des modèles disponibles',
        error: error.message
      });
    }

    // Formater les résultats pour obtenir des modèles uniques
    const uniqueModels = data ? [...new Set(data.map((item: { model: string }) => item.model))] : [];
    const result = {
      success: true,
      data: uniqueModels
    };

    return res.status(200).json(result);
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des modèles disponibles', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des modèles disponibles',
      error: error.message
    });
  }
};

// Endpoint API REST: /api/openrouter/stats/daily-count
export const getOpenRouterDailyCount = async (_req: Request, res: Response) => {
  try {
    const count = await getDailyRequestCount();
    return res.status(200).json({ success: true, count });
  } catch (error: any) {
    logger.error('Erreur endpoint daily-count', { error });
    return res.status(500).json({ success: false, message: 'Erreur serveur', error: error.message });
  }
};

// Endpoint global propre : /api/openrouter/stats/global
export const getGlobalStats = async (_req: Request, res: Response) => {
  try {
    // Déclarer les bornes de période AVANT toute utilisation
    const today = new Date();
    const dayRange = getDayRangeLocal(today);
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Lundi de la semaine courante
    weekStart.setHours(0, 0, 0, 0);
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    monthStart.setHours(0, 0, 0, 0);
    const yearStart = new Date(today.getFullYear(), 0, 1);
    yearStart.setHours(0, 0, 0, 0);

    // 1. Nombre total de requêtes (toutes périodes)
    const { count: totalRequests, error: countError } = await supabase
      .from('openrouter_api_usage')
      .select('*', { count: 'exact', head: true });
    if (countError) {
      logger.error('Erreur lors du calcul du nombre total de requêtes', { error: countError });
      return res.status(500).json({ success: false, message: 'Erreur lors du calcul des statistiques globales', error: countError.message });
    }

    // 2. Nombre de requêtes du jour (UTC, propre)
    const dailyRequests = await getDailyRequestCount();

    // 3. Somme totale des tokens
    let totalTokens = 0;
    let totalPromptTokens = 0;
    let totalCompletionTokens = 0;
    try {
      const { data, error } = await supabase
        .from('openrouter_api_usage')
        .select('total_tokens, prompt_tokens, completion_tokens');
      if (!error && data) {
        totalTokens = data.reduce((sum, item) => sum + (item.total_tokens || 0), 0);
        totalPromptTokens = data.reduce((sum, item) => sum + (item.prompt_tokens || 0), 0);
        totalCompletionTokens = data.reduce((sum, item) => sum + (item.completion_tokens || 0), 0);
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des tokens, utilisation de la valeur par défaut', { error });
    }

    // 4. Statistiques par type de service (toutes périodes)
    interface ServiceStat {
      service_type: string;
      count: number;
      sum: number;
      sum_prompt_tokens: number;
      sum_completion_tokens: number;
    }
    let serviceData: ServiceStat[] = [];
    let serviceDataDaily: ServiceStat[] = [];
    let serviceDataWeekly: ServiceStat[] = [];
    let serviceDataMonthly: ServiceStat[] = [];
    let serviceDataYearly: ServiceStat[] = [];
    try {
      // Toutes périodes
      const { data: allData, error: allError } = await supabase
        .from('openrouter_api_usage')
        .select('service_type, total_tokens, prompt_tokens, completion_tokens');
      if (!allError && allData && Array.isArray(allData)) {
        const statsMap: { [key: string]: { count: number; sum: number; sum_prompt_tokens: number; sum_completion_tokens: number } } = {};
        allData.forEach((item: any) => {
          const type = item.service_type;
          if (!statsMap[type]) {
            statsMap[type] = { count: 0, sum: 0, sum_prompt_tokens: 0, sum_completion_tokens: 0 };
          }
          statsMap[type].count++;
          statsMap[type].sum += item.total_tokens || 0;
          statsMap[type].sum_prompt_tokens += item.prompt_tokens || 0;
          statsMap[type].sum_completion_tokens += item.completion_tokens || 0;
        });
        serviceData = Object.entries(statsMap).map(([service_type, { count, sum, sum_prompt_tokens, sum_completion_tokens }]) => ({
          service_type,
          count,
          sum,
          sum_prompt_tokens,
          sum_completion_tokens
        }));
      }
      // Par période
      // 1. Jour
      const { data: dailyData, error: dailyError } = await supabase
        .from('openrouter_api_usage')
        .select('service_type, total_tokens, prompt_tokens, completion_tokens')
        .gte('created_at', dayRange.start)
        .lt('created_at', dayRange.end);
      if (!dailyError && dailyData && Array.isArray(dailyData)) {
        const statsMap: { [key: string]: { count: number; sum: number; sum_prompt_tokens: number; sum_completion_tokens: number } } = {};
        dailyData.forEach((item: any) => {
          const type = item.service_type;
          if (!statsMap[type]) {
            statsMap[type] = { count: 0, sum: 0, sum_prompt_tokens: 0, sum_completion_tokens: 0 };
          }
          statsMap[type].count++;
          statsMap[type].sum += item.total_tokens || 0;
          statsMap[type].sum_prompt_tokens += item.prompt_tokens || 0;
          statsMap[type].sum_completion_tokens += item.completion_tokens || 0;
        });
        serviceDataDaily = Object.entries(statsMap).map(([service_type, { count, sum, sum_prompt_tokens, sum_completion_tokens }]) => ({
          service_type,
          count,
          sum,
          sum_prompt_tokens,
          sum_completion_tokens
        }));
      }
      // 2. Semaine
      const { data: weeklyData, error: weeklyError, count: weeklyCount } = await supabase
        .from('openrouter_api_usage')
        .select('service_type, total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', weekStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!weeklyError && weeklyData && Array.isArray(weeklyData)) {
        const statsMap: { [key: string]: { count: number; sum: number; sum_prompt_tokens: number; sum_completion_tokens: number } } = {};
        weeklyData.forEach((item: any) => {
          const type = item.service_type;
          if (!statsMap[type]) {
            statsMap[type] = { count: 0, sum: 0, sum_prompt_tokens: 0, sum_completion_tokens: 0 };
          }
          statsMap[type].count++;
          statsMap[type].sum += item.total_tokens || 0;
          statsMap[type].sum_prompt_tokens += item.prompt_tokens || 0;
          statsMap[type].sum_completion_tokens += item.completion_tokens || 0;
        });
        serviceDataWeekly = Object.entries(statsMap).map(([service_type, { count, sum, sum_prompt_tokens, sum_completion_tokens }]) => ({
          service_type,
          count,
          sum,
          sum_prompt_tokens,
          sum_completion_tokens
        }));
      }
      // 3. Mois
      const { data: monthlyData, error: monthlyError, count: monthlyCount } = await supabase
        .from('openrouter_api_usage')
        .select('service_type, total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', monthStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!monthlyError && monthlyData && Array.isArray(monthlyData)) {
        const statsMap: { [key: string]: { count: number; sum: number; sum_prompt_tokens: number; sum_completion_tokens: number } } = {};
        monthlyData.forEach((item: any) => {
          const type = item.service_type;
          if (!statsMap[type]) {
            statsMap[type] = { count: 0, sum: 0, sum_prompt_tokens: 0, sum_completion_tokens: 0 };
          }
          statsMap[type].count++;
          statsMap[type].sum += item.total_tokens || 0;
          statsMap[type].sum_prompt_tokens += item.prompt_tokens || 0;
          statsMap[type].sum_completion_tokens += item.completion_tokens || 0;
        });
        serviceDataMonthly = Object.entries(statsMap).map(([service_type, { count, sum, sum_prompt_tokens, sum_completion_tokens }]) => ({
          service_type,
          count,
          sum,
          sum_prompt_tokens,
          sum_completion_tokens
        }));
      }
      // 4. Année
      const { data: yearlyData, error: yearlyError, count: yearlyCount } = await supabase
        .from('openrouter_api_usage')
        .select('service_type, total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', yearStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!yearlyError && yearlyData && Array.isArray(yearlyData)) {
        const statsMap: { [key: string]: { count: number; sum: number; sum_prompt_tokens: number; sum_completion_tokens: number } } = {};
        yearlyData.forEach((item: any) => {
          const type = item.service_type;
          if (!statsMap[type]) {
            statsMap[type] = { count: 0, sum: 0, sum_prompt_tokens: 0, sum_completion_tokens: 0 };
          }
          statsMap[type].count++;
          statsMap[type].sum += item.total_tokens || 0;
          statsMap[type].sum_prompt_tokens += item.prompt_tokens || 0;
          statsMap[type].sum_completion_tokens += item.completion_tokens || 0;
        });
        serviceDataYearly = Object.entries(statsMap).map(([service_type, { count, sum, sum_prompt_tokens, sum_completion_tokens }]) => ({
          service_type,
          count,
          sum,
          sum_prompt_tokens,
          sum_completion_tokens
        }));
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des statistiques par service', { error });
    }

    // 5. Statistiques par période (jour, semaine, mois, année)
    // 5.1 Statistiques du jour
    let dailyTokens = 0;
    let dailyPromptTokens = 0;
    let dailyCompletionTokens = 0;
    try {
      const { data, error } = await supabase
        .from('openrouter_api_usage')
        .select('total_tokens, prompt_tokens, completion_tokens')
        .gte('created_at', dayRange.start)
        .lt('created_at', dayRange.end);
      if (!error && data) {
        dailyTokens = data.reduce((sum, item) => sum + (item.total_tokens || 0), 0);
        dailyPromptTokens = data.reduce((sum, item) => sum + (item.prompt_tokens || 0), 0);
        dailyCompletionTokens = data.reduce((sum, item) => sum + (item.completion_tokens || 0), 0);
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des tokens quotidiens', { error });
    }

    // 5.2 Statistiques de la semaine
    let weeklyRequests = 0;
    let weeklyTokens = 0;
    let weeklyPromptTokens = 0;
    let weeklyCompletionTokens = 0;
    try {
      const { data, error, count } = await supabase
        .from('openrouter_api_usage')
        .select('total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', weekStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!error && data) {
        weeklyRequests = count || 0;
        weeklyTokens = data.reduce((sum, item) => sum + (item.total_tokens || 0), 0);
        weeklyPromptTokens = data.reduce((sum, item) => sum + (item.prompt_tokens || 0), 0);
        weeklyCompletionTokens = data.reduce((sum, item) => sum + (item.completion_tokens || 0), 0);
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des tokens hebdomadaires', { error });
    }

    // 5.3 Statistiques du mois
    let monthlyRequests = 0;
    let monthlyTokens = 0;
    let monthlyPromptTokens = 0;
    let monthlyCompletionTokens = 0;
    try {
      const { data, error, count } = await supabase
        .from('openrouter_api_usage')
        .select('total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', monthStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!error && data) {
        monthlyRequests = count || 0;
        monthlyTokens = data.reduce((sum, item) => sum + (item.total_tokens || 0), 0);
        monthlyPromptTokens = data.reduce((sum, item) => sum + (item.prompt_tokens || 0), 0);
        monthlyCompletionTokens = data.reduce((sum, item) => sum + (item.completion_tokens || 0), 0);
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des tokens mensuels', { error });
    }

    // 5.4 Statistiques de l'année
    let yearlyRequests = 0;
    let yearlyTokens = 0;
    let yearlyPromptTokens = 0;
    let yearlyCompletionTokens = 0;
    try {
      const { data, error, count } = await supabase
        .from('openrouter_api_usage')
        .select('total_tokens, prompt_tokens, completion_tokens', { count: 'exact' })
        .gte('created_at', yearStart.toISOString())
        .lt('created_at', dayRange.end);
      if (!error && data) {
        yearlyRequests = count || 0;
        yearlyTokens = data.reduce((sum, item) => sum + (item.total_tokens || 0), 0);
        yearlyPromptTokens = data.reduce((sum, item) => sum + (item.prompt_tokens || 0), 0);
        yearlyCompletionTokens = data.reduce((sum, item) => sum + (item.completion_tokens || 0), 0);
      }
    } catch (error) {
      logger.warn('Exception lors du calcul des tokens annuels', { error });
    }

    // Récupérer tous les prix des modèles
    let modelPricing: Record<string, { input_price_per_million: number; output_price_per_million: number }> = {};
    try {
      const { data: pricingData, error: pricingError } = await supabase
        .from('openrouter_model_pricing')
        .select('model, input_price_per_million, output_price_per_million');
      if (!pricingError && pricingData) {
        pricingData.forEach((row: any) => {
          modelPricing[row.model] = {
            input_price_per_million: Number(row.input_price_per_million) || 0,
            output_price_per_million: Number(row.output_price_per_million) || 0
          };
        });
      }
    } catch (e) {
      logger.warn('Impossible de récupérer les prix des modèles OpenRouter', { error: e });
    }

    // Fonction utilitaire pour calculer le coût d'un agrégat
    function calcCost(model: string, prompt_tokens: number, completion_tokens: number) {
      const pricing = modelPricing[model];
      if (!pricing) return 0;
      return ((prompt_tokens * pricing.input_price_per_million) + (completion_tokens * pricing.output_price_per_million)) / 1_000_000;
    }

    // 7. Calcul du coût global (toutes périodes)
    let total_cost = 0;
    try {
      const { data: allUsage, error: allUsageError } = await supabase
        .from('openrouter_api_usage')
        .select('model, prompt_tokens, completion_tokens');
      if (!allUsageError && allUsage) {
        total_cost = allUsage.reduce((sum, item) => sum + calcCost(item.model, item.prompt_tokens || 0, item.completion_tokens || 0), 0);
      }
    } catch (e) {
      logger.warn('Impossible de calculer le coût total', { error: e });
    }

    // 8. Calcul du coût par période
    async function sumCostByPeriod(start: string, end: string) {
      let cost = 0;
      try {
        const { data, error } = await supabase
          .from('openrouter_api_usage')
          .select('model, prompt_tokens, completion_tokens')
          .gte('created_at', start)
          .lt('created_at', end);
        if (!error && data) {
          cost = data.reduce((sum, item) => sum + calcCost(item.model, item.prompt_tokens || 0, item.completion_tokens || 0), 0);
        }
      } catch (e) {
        logger.warn('Impossible de calculer le coût de la période', { error: e });
      }
      return cost;
    }

    // Calcul des coûts par période (synchrone pour éviter de tout réécrire)
    const daily_cost = await sumCostByPeriod(dayRange.start, dayRange.end);
    const weekly_cost = await sumCostByPeriod(weekStart.toISOString(), dayRange.end);
    const monthly_cost = await sumCostByPeriod(monthStart.toISOString(), dayRange.end);
    const yearly_cost = await sumCostByPeriod(yearStart.toISOString(), dayRange.end);

    // Fonction pour calculer le coût par service_type sur un agrégat
    async function costByServiceType(serviceDataArr: any[], periodStart?: string, periodEnd?: string) {
      // On va regrouper par service_type ET modèle pour être précis
      if (!serviceDataArr || serviceDataArr.length === 0) return [];
      let costMap: Record<string, number> = {};
      try {
        let query = supabase
          .from('openrouter_api_usage')
          .select('service_type, model, prompt_tokens, completion_tokens');
        if (periodStart && periodEnd) {
          query = query.gte('created_at', periodStart).lt('created_at', periodEnd);
        }
        const { data, error } = await query;
        if (!error && data) {
          data.forEach((item: any) => {
            const stype = item.service_type;
            const model = item.model;
            const cost = calcCost(model, item.prompt_tokens || 0, item.completion_tokens || 0);
            if (!costMap[stype]) costMap[stype] = 0;
            costMap[stype] += cost;
          });
        }
      } catch (e) {
        logger.warn('Impossible de calculer le coût par service_type', { error: e });
      }
      return serviceDataArr.map((item) => ({
        ...item,
        cost: Number(costMap[item.service_type] || 0)
      }));
    }

    // On attend les résultats pour chaque période
    const by_service_type = await costByServiceType(serviceData);
    const by_service_type_daily = await costByServiceType(serviceDataDaily, dayRange.start, dayRange.end);
    const by_service_type_weekly = await costByServiceType(serviceDataWeekly, weekStart.toISOString(), dayRange.end);
    const by_service_type_monthly = await costByServiceType(serviceDataMonthly, monthStart.toISOString(), dayRange.end);
    const by_service_type_yearly = await costByServiceType(serviceDataYearly, yearStart.toISOString(), dayRange.end);

    const formattedData = {
      total_requests: totalRequests || 0,
      total_tokens: totalTokens,
      total_prompt_tokens: totalPromptTokens,
      total_completion_tokens: totalCompletionTokens,
      
      total_cost,
      daily_cost,
      weekly_cost,
      monthly_cost,
      yearly_cost,
      
      // Statistiques par période
      daily_requests: dailyRequests,
      daily_tokens: dailyTokens,
      daily_prompt_tokens: dailyPromptTokens,
      daily_completion_tokens: dailyCompletionTokens,

      weekly_requests: weeklyRequests,
      weekly_tokens: weeklyTokens,
      weekly_prompt_tokens: weeklyPromptTokens,
      weekly_completion_tokens: weeklyCompletionTokens,

      monthly_requests: monthlyRequests,
      monthly_tokens: monthlyTokens,
      monthly_prompt_tokens: monthlyPromptTokens,
      monthly_completion_tokens: monthlyCompletionTokens,

      yearly_requests: yearlyRequests,
      yearly_tokens: yearlyTokens,
      yearly_prompt_tokens: yearlyPromptTokens,
      yearly_completion_tokens: yearlyCompletionTokens,
      by_service_type,
      by_service_type_daily,
      by_service_type_weekly,
      by_service_type_monthly,
      by_service_type_yearly
    };
    return res.status(200).json({ success: true, data: formattedData });
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des statistiques globales', { error: error.message, stack: error.stack });
    return res.status(500).json({ success: false, message: 'Erreur lors de la récupération des statistiques globales', error: error.message });
  }
};

// Fonction utilitaire pour obtenir le début et la fin du jour en local
export function getDayRangeLocal(date = new Date()) {
  const start = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
  const end = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1, 0, 0, 0, 0);
  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

// Fonction utilitaire pour compter les requêtes du jour (local, sans cache, sans fallback)
export async function getDailyRequestCount(): Promise<number> {
  const now = new Date();
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0).toISOString();
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0, 0).toISOString();
  const { count, error } = await supabase
    .from('openrouter_api_usage')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', start)
    .lt('created_at', end);
  if (error) {
    logger.error('Erreur lors du comptage des requêtes du jour', { error });
    return 0;
  }
  return count || 0;
}