import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  TextField,
  Checkbox,
  Button,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Collapse,
  IconButton,
  styled,
  InputAdornment,
} from '@mui/material';
import { ChevronDown, ChevronUp, Search, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../../services/types';
import ModalPortal from '../../../../components/ModalPortal';

const ModalContent = styled(Paper)(({  }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '90%',
  maxWidth: '800px',
  maxHeight: '80vh',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'white',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  zIndex: 1000,
  '@media (max-width: 768px)': {
    width: '95%',
    maxHeight: '90vh',
    top: '50%',
  },
  '@media (max-width: 480px)': {
    width: '95%',
    maxHeight: '90vh',
  }
}));

const SearchBar = styled(Box)({
  padding: '20px',
  borderBottom: '1px solid #FFE4BA',
  backgroundColor: '#FFF8F3',
  '@media (max-width: 768px)': {
    padding: '16px',
  }
});

const CategoryList = styled(List)({
  flex: 1,
  overflow: 'auto',
  padding: '16px',
  '@media (max-width: 768px)': {
    padding: '12px',
  }
});

const CategoryItem = styled(ListItem)(({ selected }: { selected?: boolean }) => ({
  borderRadius: '8px',
  marginBottom: '8px',
  backgroundColor: selected ? '#FFF8F3' : 'transparent',
  '&:hover': {
    backgroundColor: '#FFF8F3',
  },
  '@media (max-width: 768px)': {
    padding: '8px 12px',
  }
}));

const SubcategoryList = styled(List)({
  paddingLeft: '48px',
  '@media (max-width: 768px)': {
    paddingLeft: '36px',
  },
  '@media (max-width: 480px)': {
    paddingLeft: '24px',
  }
});

const HighlightedText = styled('span')<{ highlight: boolean }>(({ highlight }) => ({
  backgroundColor: highlight ? '#FFE4BA' : 'transparent',
  padding: highlight ? '0 4px' : '0',
  borderRadius: '4px',
}));

const ButtonContainer = styled(Box)({
  padding: '16px',
  borderTop: '1px solid #FFE4BA',
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '12px',
  '@media (max-width: 768px)': {
    padding: '12px',
    flexDirection: 'column',
    '& > button': {
      width: '100%',
    }
  }
});

interface CategoryFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCategories: string[];
  selectedSubcategories: string[];
  onApplyFilter: (categories: string[], subcategories: string[]) => void;
}

// Fonction pour normaliser le texte (retirer les accents)
const normalizeText = (text: string): string => {
  return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase();
};

const CategoryFilterModal: React.FC<CategoryFilterModalProps> = ({
  isOpen,
  onClose,
  selectedCategories,
  selectedSubcategories,
  onApplyFilter,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [tempSelectedCategories, setTempSelectedCategories] = useState<string[]>(selectedCategories);
  const [tempSelectedSubcategories, setTempSelectedSubcategories] = useState<string[]>(selectedSubcategories);

  useEffect(() => {
    if (isOpen) {
      setTempSelectedCategories(selectedCategories);
      setTempSelectedSubcategories(selectedSubcategories);
    }
  }, [isOpen, selectedCategories, selectedSubcategories]);

  // Effet pour déployer automatiquement les catégories lors d'une recherche
  useEffect(() => {
    if (searchTerm) {
      const normalizedSearchTerm = normalizeText(searchTerm);
      const categoriesToExpand = SERVICE_CATEGORIES.filter(category => {
        const matchInCategory = 
          normalizeText(category.nom).includes(normalizedSearchTerm) ||
          normalizeText(category.description).includes(normalizedSearchTerm);

        const matchInSubcategories = SERVICE_SUBCATEGORIES
          .filter(sub => sub.categoryId === category.id)
          .some(sub => 
            normalizeText(sub.nom).includes(normalizedSearchTerm) ||
            normalizeText(sub.description).includes(normalizedSearchTerm) ||
            sub.synonymes?.some(syn => normalizeText(syn).includes(normalizedSearchTerm))
          );

        return matchInCategory || matchInSubcategories;
      }).map(category => category.id);

      setExpandedCategories(prev => {
        const newExpanded = [...new Set([...prev, ...categoriesToExpand])];
        return newExpanded;
      });
    } else {
      setExpandedCategories([]);
    }
  }, [searchTerm]);

  const filteredCategories = useMemo(() => {
    const categories = SERVICE_CATEGORIES;
    
    // Si pas de recherche, trier pour mettre les catégories sélectionnées en haut
    if (!searchTerm) {
      return [...categories].sort((a, b) => {
        const aSelected = tempSelectedCategories.includes(a.id);
        const bSelected = tempSelectedCategories.includes(b.id);
        if (aSelected && !bSelected) return -1;
        if (!aSelected && bSelected) return 1;
        return a.nom.localeCompare(b.nom, 'fr');
      });
    }

    // Si recherche, filtrer et trier
    const normalizedSearchTerm = normalizeText(searchTerm);
    const filtered = categories.filter(category => {
      const matchInCategory = 
        normalizeText(category.nom).includes(normalizedSearchTerm) ||
        normalizeText(category.description).includes(normalizedSearchTerm);

      const matchInSubcategories = SERVICE_SUBCATEGORIES
        .filter(sub => sub.categoryId === category.id)
        .some(sub => 
          normalizeText(sub.nom).includes(normalizedSearchTerm) ||
          normalizeText(sub.description).includes(normalizedSearchTerm) ||
          sub.synonymes?.some(syn => normalizeText(syn).includes(normalizedSearchTerm))
        );

      return matchInCategory || matchInSubcategories;
    });

    // Trier les résultats filtrés avec les sélectionnés en haut
    return filtered.sort((a, b) => {
      const aSelected = tempSelectedCategories.includes(a.id);
      const bSelected = tempSelectedCategories.includes(b.id);
      if (aSelected && !bSelected) return -1;
      if (!aSelected && bSelected) return 1;
      return a.nom.localeCompare(b.nom, 'fr');
    });
  }, [searchTerm, tempSelectedCategories]);

  const handleCategoryToggle = (categoryId: string) => {
    setTempSelectedCategories(prev => {
      const isSelected = prev.includes(categoryId);
      if (isSelected) {
        // Désélectionner la catégorie et toutes ses sous-catégories
        const subCategoriesToRemove = SERVICE_SUBCATEGORIES
          .filter(sub => sub.categoryId === categoryId)
          .map(sub => sub.id);
        setTempSelectedSubcategories(prev => 
          prev.filter(id => !subCategoriesToRemove.includes(id))
        );
        return prev.filter(id => id !== categoryId);
      } else {
        // Sélectionner la catégorie et toutes ses sous-catégories
        const subCategoriesToAdd = SERVICE_SUBCATEGORIES
          .filter(sub => sub.categoryId === categoryId)
          .map(sub => sub.id);
        setTempSelectedSubcategories(prev => 
          [...new Set([...prev, ...subCategoriesToAdd])]
        );
        return [...prev, categoryId];
      }
    });
  };

  const handleSubcategoryToggle = (subcategoryId: string, categoryId: string) => {
    setTempSelectedSubcategories(prev => {
      const isSelected = prev.includes(subcategoryId);
      if (isSelected) {
        const newSubcategories = prev.filter(id => id !== subcategoryId);
        // Si c'était la dernière sous-catégorie sélectionnée, désélectionner la catégorie
        const hasOtherSelectedSubcategories = SERVICE_SUBCATEGORIES
          .filter(sub => sub.categoryId === categoryId)
          .some(sub => newSubcategories.includes(sub.id));
        if (!hasOtherSelectedSubcategories) {
          setTempSelectedCategories(prev => prev.filter(id => id !== categoryId));
        }
        return newSubcategories;
      } else {
        return [...prev, subcategoryId];
      }
    });
  };

  const handleExpandCategory = (categoryId: string) => {
    setExpandedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  const handleCategoryClick = (categoryId: string, event: React.MouseEvent) => {
    // Empêcher la propagation si le clic vient de la checkbox ou du bouton d'expansion
    const target = event.target as HTMLElement;
    if (target.closest('.MuiCheckbox-root') || target.closest('.expand-button')) {
      return;
    }
    handleExpandCategory(categoryId);
  };

  const handleApply = () => {
    // Vérifier si les catégories et sous-catégories ont changé
    const categoriesChanged = JSON.stringify(tempSelectedCategories.sort()) !== JSON.stringify(selectedCategories.sort());
    const subcategoriesChanged = JSON.stringify(tempSelectedSubcategories.sort()) !== JSON.stringify(selectedSubcategories.sort());

    if (categoriesChanged || subcategoriesChanged) {
      onApplyFilter(tempSelectedCategories, tempSelectedSubcategories);
    }
    onClose();
  };

  const handleClear = () => {
    setTempSelectedCategories([]);
    setTempSelectedSubcategories([]);
    setSearchTerm('');
    setExpandedCategories([]);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
  };

  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm) return <span>{text}</span>;

    const normalizedSearchTerm = normalizeText(searchTerm);
    const parts = text.split(new RegExp(`(${normalizedSearchTerm})`, 'gi'));
    
    let currentIndex = 0;
    return (
      <>
        {parts.map((part, i) => {
          const isMatch = normalizeText(part).toLowerCase() === normalizedSearchTerm.toLowerCase();
          const originalText = text.slice(currentIndex, currentIndex + part.length);
          currentIndex += part.length;
          return (
            <HighlightedText
              key={i}
              highlight={isMatch}
            >
              {originalText}
            </HighlightedText>
          );
        })}
      </>
    );
  };

  // Ajout de la classe pour le flou de fond
  const backdropStyle = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 999,
    height: '100vh',
    backdropFilter: 'blur(2px)', // Ajout de l'effet de flou
  };

  if (!isOpen) return null;

  return (
    <ModalPortal>
      <motion.div
        // initial={{ opacity: 0 }}
        // animate={{ opacity: 1 }}
        // exit={{ opacity: 0 }}
      >
        <Box
          sx={backdropStyle} // Utilisation de la nouvelle classe
          onClick={onClose}
        />
        <ModalContent onClick={e => e.stopPropagation()}>
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '20px',
            borderBottom: '1px solid #FFE4BA'
          }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
              Filtrer par catégories
            </Typography>
            <IconButton onClick={onClose} size="small">
              <X size={20} />
            </IconButton>
          </Box>

          <SearchBar>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Rechercher une catégorie ou sous-catégorie..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search size={20} style={{ color: '#FF6B2C' }} />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleSearchClear}
                      size="small"
                      sx={{ color: '#FF6B2C' }}
                    >
                      <X size={16} />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                  backgroundColor: 'white',
                  '&:hover fieldset': {
                    borderColor: '#FF6B2C',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
              }}
            />
          </SearchBar>

          <CategoryList>
            <AnimatePresence>
              {filteredCategories.map((category) => {
                const subcategories = SERVICE_SUBCATEGORIES
                  .filter(sub => sub.categoryId === category.id)
                  .sort((a, b) => {
                    // D'abord trier par sélection
                    const aSelected = tempSelectedSubcategories.includes(a.id);
                    const bSelected = tempSelectedSubcategories.includes(b.id);
                    if (aSelected && !bSelected) return -1;
                    if (!aSelected && bSelected) return 1;
                    // Ensuite trier par ordre alphabétique
                    return a.nom.localeCompare(b.nom, 'fr');
                  });
                const isExpanded = expandedCategories.includes(category.id);
                const isCategorySelected = tempSelectedCategories.includes(category.id);

                return (
                  <motion.div
                    key={category.id}
                    // initial={{ opacity: 0, y: 20 }}
                    // animate={{ opacity: 1, y: 0 }}
                    // exit={{ opacity: 0, y: -20 }}
                    // transition={{ duration: 0.2 }}
                  >
                    <CategoryItem 
                      selected={isCategorySelected}
                      onClick={(e) => handleCategoryClick(category.id, e)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <ListItemIcon>
                        <Checkbox
                          checked={isCategorySelected}
                          onChange={() => handleCategoryToggle(category.id)}
                          sx={{
                            color: '#FFE4BA',
                            '&.Mui-checked': {
                              color: '#FF6B2C',
                            },
                          }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={highlightText(category.nom, searchTerm)}
                        secondary={highlightText(category.description, searchTerm)}
                      />
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          handleExpandCategory(category.id);
                        }}
                        size="small"
                        className="expand-button"
                      >
                        {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                      </IconButton>
                    </CategoryItem>

                    <Collapse in={isExpanded}>
                      <SubcategoryList>
                        {subcategories.map((subcategory) => {
                          const isSubcategorySelected = tempSelectedSubcategories.includes(subcategory.id);
                          const showSubcategory = !searchTerm || 
                            normalizeText(subcategory.nom).includes(normalizeText(searchTerm)) ||
                            normalizeText(subcategory.description).includes(normalizeText(searchTerm)) ||
                            subcategory.synonymes?.some(syn => 
                              normalizeText(syn).includes(normalizeText(searchTerm))
                            );

                          if (!showSubcategory) return null;

                          return (
                            <motion.div
                              key={subcategory.id}
                              // initial={{ opacity: 0, x: -20 }}
                              // animate={{ opacity: 1, x: 0 }}
                              // exit={{ opacity: 0, x: 20 }}
                              // transition={{ duration: 0.2 }}
                            >
                              <CategoryItem selected={isSubcategorySelected}>
                                <ListItemIcon>
                                  <Checkbox
                                    checked={isSubcategorySelected}
                                    onChange={() => handleSubcategoryToggle(subcategory.id, category.id)}
                                    sx={{
                                      color: '#FFE4BA',
                                      '&.Mui-checked': {
                                        color: '#FF6B2C',
                                      },
                                    }}
                                  />
                                </ListItemIcon>
                                <ListItemText
                                  primary={highlightText(subcategory.nom, searchTerm)}
                                  secondary={highlightText(subcategory.description, searchTerm)}
                                />
                              </CategoryItem>
                            </motion.div>
                          );
                        })}
                      </SubcategoryList>
                    </Collapse>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </CategoryList>

          <ButtonContainer>
            <Button
              variant="outlined"
              onClick={handleClear}
              sx={{
                borderColor: '#FFE4BA',
                color: '#FF6B2C',
                '&:hover': {
                  borderColor: '#FF6B2C',
                  backgroundColor: '#FFF8F3',
                },
              }}
            >
              Réinitialiser
            </Button>
            <Button
              variant="contained"
              onClick={handleApply}
              sx={{
                backgroundColor: '#FF6B2C',
                '&:hover': {
                  backgroundColor: '#FF965E',
                },
              }}
            >
              Appliquer les filtres
            </Button>
          </ButtonContainer>
        </ModalContent>
      </motion.div>
    </ModalPortal>
  );
};

export default CategoryFilterModal; 