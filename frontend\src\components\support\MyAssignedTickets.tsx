import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  Tooltip,
  Badge,
  Grid,
  TextField,
  InputAdornment,
  Fade,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  ViewList as ViewListIcon,
  Assignment as AssignmentIcon,
  PersonOutline as PersonOutlineIcon,
  AccessTime as AccessTimeIcon,
  PriorityHigh as PriorityHighIcon,
  NotificationsActive as NotificationsActiveIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterAlt as FilterAltIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import supportTicketService, { Ticket } from '../../services/supportTicketService';
import { formatDate, formatTicketPriority, formatTicketStatus } from '../../utils/formatters';
import TicketDetailsModal from './TicketDetailsModal';
import { logger } from '../../utils/logger';

/**
 * Composant pour afficher les tickets assignés à l'utilisateur connecté
 */
const MyAssignedTickets: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [success, setSuccess] = useState<string | null>(null);
  const [showNonAnswered, setShowNonAnswered] = useState<boolean>(true);
  
  // Charger les tickets assignés à l'utilisateur connecté
  const loadAssignedTickets = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Préparer les filtres
      const filters: any = {
        assigned_to: user.id
      };
      
      // Ajouter le filtre "repondu" uniquement si showNonAnswered est activé
      if (showNonAnswered) {
        filters.repondu = false;
      }
      
      // Utiliser le service pour récupérer les tickets assignés avec les filtres
      const response = await supportTicketService.getTickets(filters);
      
      setTickets(response.tickets);
      setFilteredTickets(response.tickets);
    } catch (error) {
      logger.error('Erreur lors du chargement des tickets assignés:', error);
      setError(error instanceof Error ? error : new Error('Une erreur est survenue'));
    } finally {
      setLoading(false);
    }
  };
  
  // Charger les tickets au montage du composant et quand showNonAnswered change
  useEffect(() => {
    loadAssignedTickets();
  }, [user, showNonAnswered]);
  
  // Filtrer les tickets quand le terme de recherche change
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredTickets(tickets);
      return;
    }

    const term = searchTerm.toLowerCase();
    const filtered = tickets.filter(ticket => 
      ticket.title.toLowerCase().includes(term) || 
      ticket.description?.toLowerCase().includes(term) ||
      ticket.category?.toLowerCase().includes(term) ||
      ticket.status?.toLowerCase().includes(term) ||
      ticket.priority?.toLowerCase().includes(term)
    );
    
    setFilteredTickets(filtered);
  }, [searchTerm, tickets]);
  
  // Ouvrir la modale de détails
  const handleOpenDetails = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setShowModal(true);
  };
  
  // Fermer la modale de détails
  const handleCloseDetails = () => {
    setShowModal(false);
  };
  
  // Rafraîchir les tickets après modification
  const handleTicketUpdated = () => {
    setSuccess("Le ticket a été mis à jour avec succès");
    loadAssignedTickets();
  };
  
  // Obtenir la couleur de priorité
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'basse':
      case 'faible':
        return '#28a745';
      case 'moyenne':
      case 'normale':
        return '#ffc107';
      case 'haute':
      case 'elevee':
        return '#fd7e14';
      case 'urgente':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };
  
  // Obtenir la couleur de statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'nouveau':
        return '#007bff';
      case 'en_cours':
        return '#FF6B2C';
      case 'attente_client':
      case 'en_attente':
        return '#17a2b8';
      case 'resolu':
        return '#28a745';
      case 'ferme':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };
  
  return (
    <Box sx={{ padding: '24px' }}>
      {/* En-tête avec style inspiré de AdminTicketTagsPage */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 4,
          gap: 2,
          position: 'relative',
          pl: 1
        }}
      >
        <AssignmentIcon
          sx={{
            fontSize: 36,
            color: '#FF6B2C',
            mr: 1
          }}
        />
        <Typography 
          variant="h4" 
          component="h1" 
          sx={{ 
            color: '#FF6B2C',
            fontWeight: 700,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: '40px',
              height: '3px',
              backgroundColor: '#FF965E',
              borderRadius: '8px'
            }
          }}
        >
          Mes Tickets Assignés
        </Typography>
        {loading && (
          <Fade in={loading}>
            <CircularProgress 
              size={24} 
              sx={{ ml: 2, color: '#FF6B2C' }} 
            />
          </Fade>
        )}
      </Box>
      
      {/* Messages d'alerte */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 3, 
            border: '1px solid #f44336',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
          }}
          onClose={() => setError(null)}
        >
          {error.message}
        </Alert>
      )}

      {success && (
        <Alert 
          severity="success" 
          sx={{ 
            mb: 3, 
            border: '1px solid #4caf50',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
          }}
          onClose={() => setSuccess(null)}
        >
          {success}
        </Alert>
      )}
      
      {/* Barre d'outils améliorée */}
      <Paper 
        elevation={2} 
        sx={{ 
          p: 3, 
          mb: 4, 
          borderRadius: '12px',
          background: 'linear-gradient(135deg, #ffffff 0%, #fff8f3 100%)',
          boxShadow: '0 4px 20px rgba(255, 107, 44, 0.05)'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, sm: 6, md: 5 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f1b18', display: 'flex', alignItems: 'center' }}>
              <AssignmentIcon sx={{ mr: 1, color: '#FF965E' }} /> Vos tickets de support assignés
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Gérez les tickets qui vous ont été assignés et suivez leur progression.
            </Typography>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, md: 3 }} sx={{ display: 'flex', justifyContent: 'center' }}>
            <Paper 
              elevation={0}
              sx={{
                p: 1.5,
                borderRadius: '10px',
                bgcolor: showNonAnswered ? 'rgba(255, 107, 44, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                border: showNonAnswered ? '1px solid rgba(255, 107, 44, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
                display: 'flex', 
                alignItems: 'center',
                transition: 'all 0.2s ease'
              }}
            >
              <FormControlLabel
                control={
                  <Switch
                    checked={showNonAnswered}
                    onChange={(e) => setShowNonAnswered(e.target.checked)}
                    color="primary"
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: '#FF6B2C',
                        '&:hover': {
                          backgroundColor: 'rgba(255, 107, 44, 0.08)',
                        },
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: '#FF6B2C',
                      },
                    }}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <FilterAltIcon sx={{ mr: 0.5, color: showNonAnswered ? '#FF6B2C' : 'text.secondary', fontSize: '1.1rem' }} />
                    <Typography variant="body2" sx={{ fontWeight: showNonAnswered ? 600 : 400, color: showNonAnswered ? '#FF6B2C' : 'text.secondary' }}>
                      À répondre uniquement
                    </Typography>
                  </Box>
                }
                sx={{ mb: 0 }}
              />
            </Paper>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, md: 4 }} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadAssignedTickets}
              disabled={loading}
              sx={{
                borderColor: '#FF6B2C',
                color: '#FF6B2C',
                '&:hover': {
                  borderColor: '#FF965E',
                  backgroundColor: 'rgba(255, 107, 44, 0.05)',
                },
                borderRadius: '8px',
                py: 1,
              }}
            >
              Actualiser
            </Button>
            <Button
              variant="outlined"
              startIcon={<ViewListIcon />}
              onClick={() => navigate('/admin/support/tickets')}
              sx={{
                borderColor: '#FF6B2C',
                color: '#FF6B2C',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.05)',
                  borderColor: '#FF6B2C',
                },
                borderRadius: '8px',
                py: 1,
              }}
            >
              Tous les tickets
            </Button>
          </Grid>
        </Grid>
        
        {showNonAnswered && (
          <Box 
            sx={{ 
              mt: 3, 
              pt: 2,
              borderTop: '1px solid rgba(0, 0, 0, 0.06)',
            }}
          >
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                p: 1.5,
                borderRadius: '8px',
                bgcolor: 'rgba(255, 107, 44, 0.05)',
              }}
            >
              <NotificationsActiveIcon sx={{ color: '#FF6B2C', mr: 1.5 }} />
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                <strong>{filteredTickets.length}</strong> ticket{filteredTickets.length !== 1 ? 's' : ''} en attente de votre réponse
              </Typography>
            </Box>
          </Box>
        )}
      </Paper>
      
      {/* Champ de recherche */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Rechercher un ticket..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: 'text.secondary' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#FF6B2C',
              },
            },
          }}
        />
      </Box>
      
      {loading && !tickets.length ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
          <CircularProgress size={50} sx={{ color: '#FF6B2C' }} />
        </Box>
      ) : error && !tickets.length ? (
        <Alert severity="error" sx={{ my: 2 }}>
          {error.message}
        </Alert>
      ) : filteredTickets.length === 0 ? (
        <Paper
          elevation={0}
          sx={{
            p: 4,
            textAlign: 'center',
            borderRadius: '12px',
            bgcolor: 'rgba(255, 247, 240, 0.5)',
            border: '1px solid rgba(255, 107, 44, 0.1)',
          }}
        >
          <Box 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              gap: 2
            }}
          >
            <PersonOutlineIcon sx={{ fontSize: '3rem', color: '#FF6B2C', opacity: 0.7 }} />
            <Typography variant="h6" color="text.secondary">
              {searchTerm 
                ? "Aucun ticket ne correspond à votre recherche" 
                : showNonAnswered 
                  ? "Aucun ticket n'est en attente de réponse" 
                  : "Aucun ticket ne vous est assigné"}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ maxWidth: '500px', mx: 'auto' }}>
              {searchTerm 
                ? "Essayez de modifier vos critères de recherche ou d'effacer le filtre."
                : showNonAnswered
                  ? "Super ! Vous avez répondu à tous vos tickets. Consultez la liste complète en désactivant le filtre 'À répondre uniquement'."
                  : "Les tickets qui vous seront assignés apparaîtront ici. Consultez la liste complète des tickets pour en assigner à votre compte."}
            </Typography>
            
            {showNonAnswered && tickets.length === 0 && (
              <Button
                variant="outlined"
                color="primary"
                onClick={() => setShowNonAnswered(false)}
                sx={{
                  mt: 2,
                  borderColor: '#FF6B2C',
                  color: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF965E',
                    backgroundColor: 'rgba(255, 107, 44, 0.05)',
                  },
                }}
              >
                Voir tous mes tickets assignés
              </Button>
            )}
          </Box>
        </Paper>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Paper
            elevation={2}
            sx={{
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
            }}
          >
            <Box sx={{ p: 2, bgcolor: 'rgba(255, 247, 240, 0.5)', borderBottom: '1px solid rgba(0, 0, 0, 0.05)' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#333' }}>
                {filteredTickets.length} {filteredTickets.length > 1 ? 'tickets assignés' : 'ticket assigné'}
                {searchTerm && ' correspondant à votre recherche'}
              </Typography>
            </Box>
            <List sx={{ p: 0, width: '100%' }}>
              <AnimatePresence>
                {filteredTickets.map((ticket, index) => (
                  <motion.div
                    key={ticket.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ delay: index * 0.05, duration: 0.3 }}
                  >
                    <ListItem
                      alignItems="flex-start"
                      sx={{
                        px: { xs: 2, sm: 3 },
                        py: 2.5,
                        borderLeft: `4px solid ${getStatusColor(ticket.status)}`,
                        '&:hover': {
                          bgcolor: 'rgba(255, 247, 240, 0.5)',
                          cursor: 'pointer'
                        },
                        flexDirection: { xs: 'column', sm: 'row' },
                        borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
                        '&:last-child': {
                          borderBottom: 'none'
                        }
                      }}
                      onClick={() => handleOpenDetails(ticket.id)}
                    >
                      <Box 
                        sx={{ 
                          display: 'flex', 
                          flexDirection: 'column',
                          width: { xs: '100%', sm: 'auto' },
                          pr: { xs: 0, sm: 3 },
                          mb: { xs: 2, sm: 0 }
                        }}
                      >
                        <Tooltip title={`Créé le ${formatDate(ticket.created_at)}`}>
                          <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                            <AccessTimeIcon sx={{ fontSize: '0.875rem', mr: 0.5 }} />
                            {formatDate(ticket.created_at)}
                          </Typography>
                        </Tooltip>
                        
                        <Chip
                          label={formatTicketStatus(ticket.status)}
                          size="small"
                          sx={{
                            bgcolor: getStatusColor(ticket.status),
                            color: 'white',
                            maxWidth: '100px',
                            mb: 1,
                            fontWeight: 500,
                            fontSize: '0.75rem',
                          }}
                        />
                        
                        <Chip
                          label={formatTicketPriority(ticket.priority)}
                          size="small"
                          sx={{
                            bgcolor: getPriorityColor(ticket.priority),
                            color: 'white',
                            maxWidth: '100px',
                            fontWeight: 500,
                            fontSize: '0.75rem',
                          }}
                        />
                      </Box>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography
                              variant="h6"
                              sx={{ 
                                fontWeight: 600, 
                                color: '#333',
                                fontSize: '1rem',
                                mr: 1
                              }}
                            >
                              {ticket.title}
                            </Typography>
                            
                            {'repondu' in ticket && !ticket.repondu && (
                              <Tooltip title="En attente de réponse">
                                <Badge 
                                  color="error" 
                                  variant="dot"
                                  sx={{ '& .MuiBadge-badge': { top: -2, right: -2 } }}
                                >
                                  <NotificationsActiveIcon 
                                    color="action" 
                                    fontSize="small" 
                                    sx={{ color: '#FF6B2C' }} 
                                  />
                                </Badge>
                              </Tooltip>
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography 
                              variant="body2" 
                              color="text.secondary"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                              }}
                            >
                              {ticket.description || 'Aucune description fournie.'}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, gap: 2, flexWrap: 'wrap' }}>
                              {ticket.category && (
                                <Chip
                                  label={ticket.category}
                                  size="small"
                                  variant="outlined"
                                  sx={{ 
                                    borderColor: 'rgba(0,0,0,0.12)', 
                                    fontSize: '0.75rem',
                                  }}
                                />
                              )}
                              
                              {ticket.updated_at && ticket.updated_at !== ticket.created_at && (
                                <Typography variant="caption" color="text.secondary">
                                  Mis à jour le {formatDate(ticket.updated_at)}
                                </Typography>
                              )}
                              
                              {ticket.user?.email && (
                                <Tooltip title={`Créé par: ${ticket.user.email}`}>
                                  <Typography 
                                    variant="caption" 
                                    color="text.secondary"
                                    sx={{ 
                                      display: 'flex', 
                                      alignItems: 'center',
                                      maxWidth: '200px',
                                      whiteSpace: 'nowrap',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis'
                                    }}
                                  >
                                    <PersonOutlineIcon sx={{ fontSize: '0.875rem', mr: 0.5 }} />
                                    {ticket.user.first_name 
                                      ? `${ticket.user.first_name} ${ticket.user.last_name || ''}` 
                                      : ticket.user.email.split('@')[0]}
                                  </Typography>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>
                        }
                        secondaryTypographyProps={{
                          component: 'div'
                        }}
                      />
                    </ListItem>
                  </motion.div>
                ))}
              </AnimatePresence>
            </List>
          </Paper>
        </motion.div>
      )}
      
      {/* Modal pour afficher les détails du ticket */}
      {selectedTicketId && (
        <TicketDetailsModal
          ticketId={selectedTicketId}
          isOpen={showModal}
          onClose={handleCloseDetails}
          isAdminRoute={true}
          onTicketUpdated={handleTicketUpdated}
        />
      )}
    </Box>
  );
};

export default MyAssignedTickets; 