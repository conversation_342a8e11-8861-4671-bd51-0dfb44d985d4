import React, { useState, useEffect } from 'react';
import { Box, Tab, Tabs } from '@mui/material';
import { styled } from '@mui/material/styles';
import { AnimatePresence, motion } from 'framer-motion';
import MyMissions from './MyMissions';
import AllMissions from './AllMissions';
import MatchingMissions from './MatchingMissions';
import ModalPortal from '@/components/ModalPortal';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CloseIcon from '@mui/icons-material/Close';
import { Typography, IconButton } from '@mui/material';
import { useLocation } from 'react-router-dom';

const StyledTabs = styled(Tabs)({
  borderBottom: '1px solid #FFE4BA',
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF6B2C',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    '@media (max-width: 600px)': {
      flexDirection: 'row',
      justifyContent: 'space-between',
    }
  },
  '@media (max-width: 600px)': {
    minHeight: '48px',
  }
});

const StyledTab = styled(Tab)({
  textTransform: 'none',
  fontWeight: 'bold',
  fontSize: '1rem',
  color: '#666',
  '&.Mui-selected': {
    color: '#FF6B2C',
  },
  '&:hover': {
    color: '#FF965E',
    transition: 'color 0.3s ease',
  },
  padding: '16px 24px',
  '@media (max-width: 600px)': {
    padding: '8px 12px',
    minHeight: '48px',
    fontSize: '0.875rem',
    flex: 1,
    minWidth: 'auto',
    '& .MuiTab-wrapper': {
      flexDirection: 'row'
    }
  }
});

const TabPanel = motion.create(Box);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanelContent = (props: TabPanelProps) => {
  const { children, value, index } = props;

  return value === index ? (
    <TabPanel
      role="tabpanel"
      id={`missions-tabpanel-${index}`}
      aria-labelledby={`missions-tab-${index}`}
      // initial={{ opacity: 0, y: 20 }}
      // animate={{ opacity: 1, y: 0 }}
      // exit={{ opacity: 0, y: -20 }}
      // transition={{ duration: 0.3 }}
      sx={{
        padding: { xs: '0px', md: '24px' },
        // backgroundColor: '#FFF8F3', // couleur de fond de la page
        borderRadius: '0 0 16px 16px',
        boxShadow: '0 4px 6px rgba(255, 107, 44, 0.05)',
      }}
    >
      {children}
    </TabPanel>
  ) : null;
};

// Composant pour le point d'interrogation avec modale
interface InfoTooltipProps {
  title: string;
  content: React.ReactNode;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({ title, content }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const ModalContent = styled(motion.div)({
    backgroundColor: '#FFF8F3',
    borderRadius: 8,
    padding: 24,
    maxWidth: 500,
    width: '90%',
    position: 'relative',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
    border: `1px solid #FF6B2C`,
    maxHeight: '80vh',
    display: 'flex',
    flexDirection: 'column'
  });

  const ModalHeader = styled(Box)({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  });

  const ModalTitle = styled(Typography)({
    fontWeight: 600,
    color: '#FF6B2C',
  });

  const ModalBody = styled(Box)({
    overflowY: 'auto',
    paddingRight: 4,
    '&::-webkit-scrollbar': {
      width: '6px',
    },
    '&::-webkit-scrollbar-track': {
      background: '#f1f1f1',
      borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
      background: '#FF965E',
      borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      background: '#FF6B2C',
    },
  });

  return (
    <>
      <Box 
        component="span" 
        sx={{ 
          display: 'inline-flex',
          alignItems: 'center',
          cursor: 'pointer',
          ml: 0.5,
          color: '#FF6B2C',
          '&:hover': { opacity: 0.8 }
        }}
        onClick={handleOpenModal}
      >
        <HelpOutlineIcon sx={{ fontSize: 18 }} />
      </Box>

      <ModalPortal isOpen={isModalOpen} onBackdropClick={handleCloseModal}>
        <ModalContent
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2 }}
        >
          <ModalHeader>
            <ModalTitle variant="h6">{title}</ModalTitle>
            <IconButton 
              onClick={handleCloseModal} 
              size="small"
              sx={{ color: '#FF6B2C' }}
            >
              <CloseIcon />
            </IconButton>
          </ModalHeader>
          
          <ModalBody>
            {typeof content === 'string' ? (
              <Typography variant="body1">{content}</Typography>
            ) : (
              content
            )}
          </ModalBody>
        </ModalContent>
      </ModalPortal>
    </>
  );
};

const MissionsView: React.FC = () => {
  const [value, setValue] = useState(0);
  const [showRejected, setShowRejected] = useState(false);
  const location = useLocation();

  // Vérifier le paramètre d'URL et l'état de navigation pour sélectionner l'onglet approprié
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const tabParam = searchParams.get('tab');
    const locationState = location.state as { activeTab?: number };
    
    if (locationState?.activeTab !== undefined) {
      setValue(locationState.activeTab);
    } else if (tabParam === 'all') {
      setValue(1); // Sélectionner l'onglet "Toutes les missions"
    } else if (tabParam === 'matching') {
      setValue(2); // Sélectionner l'onglet "Missions à postuler"
    }
  }, [location]);

  const handleChange = (_: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ 
      width: '100%', 
      maxWidth: '100%',
      bgcolor: 'white',
      borderRadius: { xs: '8px', sm: '16px' },
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
      overflow: 'hidden'
    }}>
      <Box sx={{ 
        borderBottom: 1, 
        borderColor: 'divider',
        '@media (max-width: 600px)': {
          px: 1
        }
      }}>
        <StyledTabs 
          value={value} 
          onChange={handleChange} 
          aria-label="missions tabs"
          variant="fullWidth"
          scrollButtons={false}
        >
          <StyledTab 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                Mes Missions
                {value === 0 && (
                  <InfoTooltip 
                    title="Mes Missions" 
                    content="Cette section affiche toutes les missions que vous avez créées. Vous pouvez suivre leur progression, gérer les offres reçues et communiquer avec les jobbeurs pour coordonner la réalisation des services."
                  />
                )}
              </Box>
            } 
          />
          <StyledTab 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                Toutes les missions
                {value === 1 && (
                  <InfoTooltip 
                    title="Toutes les missions" 
                    content="Cette section affiche toutes les demandes de services disponibles sur la plateforme. Vous pouvez filtrer par catégorie, budget, et d'autres critères pour trouver les missions qui correspondent à vos compétences et disponibilités."
                  />
                )}
              </Box>
            } 
          />
          <StyledTab 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                Missions à postuler
                {value === 2 && (
                  <InfoTooltip 
                    title="Missions à postuler" 
                    content="Cette section affiche les demandes de services auxquelles vous pouvez proposer vos compétences. Pour postuler, vous devez avoir le même service correspondant dans votre profil (par exemple coiffeur, plombier, etc). Vous pouvez envoyer des offres de travail aux clients en précisant vos conditions et tarifs pour réaliser leurs missions."
                  />
                )}
              </Box>
            } 
          />
        </StyledTabs>
      </Box>

      <AnimatePresence mode="sync">
        <TabPanelContent value={value} index={0} key="my-missions">
          <MyMissions />
        </TabPanelContent>

        <TabPanelContent value={value} index={1} key="all-missions">
          <AllMissions />
        </TabPanelContent>

        <TabPanelContent value={value} index={2} key="matching-missions">
          <MatchingMissions 
            showRejected={showRejected} 
            onToggleRejected={() => setShowRejected(!showRejected)} 
          />
        </TabPanelContent>
      </AnimatePresence>
    </Box>
  );
};

export default MissionsView; 