import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  Divider,
  CircularProgress,
  Alert,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Edit as EditIcon,
  Close as CloseIcon,
  Person as PersonIcon,
  EventNote as EventNoteIcon,
  Assignment as AssignmentIcon,
  PriorityHigh as PriorityHighIcon,
  Category as CategoryIcon,
  LocationOn as LocationOnIcon,
  AccessTime as AccessTimeIcon,
  Delete as DeleteIcon,
  EditCalendar as EditCalendarIcon,
  Search as SearchIcon,
  Psychology as PsychologyIcon,
  AutoAwesome as AutoAwesomeIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { format, differenceInMinutes } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import CommentsList from './TicketCommentsList';
import TicketAttachmentsList from './TicketAttachmentsList';
import useTickets from '../../hooks/useTickets';
import ModalPortal from '../ModalPortal';
import { motion } from 'framer-motion';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import logger from '../../utils/logger';
import supportTicketService, { User } from '../../services/supportTicketService';
import { notify } from '../../components/Notification';
import { formatTicketStatus, formatTicketPriority } from '../../utils/formatters';
import useSupportAiAssistance from '../../hooks/useSupportAiAssistance';

interface TicketDetailsModalProps {
  ticketId: string;
  isOpen: boolean;
  onClose: () => void;
  isAdminRoute?: boolean;
  onTicketUpdated?: () => void;
}

const TicketDetailsModal: React.FC<TicketDetailsModalProps> = ({
  ticketId,
  isOpen,
  onClose,
  isAdminRoute = false,
  onTicketUpdated,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Déterminer si l'utilisateur est membre du staff
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';
  const [ticket, setTicket] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [userDetails, setUserDetails] = useState<any>(null);
  const [userLoading, setUserLoading] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [statusToChange, setStatusToChange] = useState<string | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [staffUsers, setStaffUsers] = useState<User[]>([]);
  const [loadingStaffUsers, setLoadingStaffUsers] = useState(false);
  const [assigningTicket, setAssigningTicket] = useState(false);
  const [staffUserSearch, setStaffUserSearch] = useState('');
  const [showAiAssistance, setShowAiAssistance] = useState(false);
  const ticketLoadedRef = useRef(false);

  // Utiliser notre hook pour récupérer un ticket
  const { getTicket } = useTickets();

  // Hook pour l'assistance IA
  const {
    loading: aiLoading,
    assistance,
    creditsRemaining,
    generateAssistance,
    clearAssistance
  } = useSupportAiAssistance();

  // Fonction pour récupérer les détails du ticket
  const fetchTicketDetails = async () => {
    if (!ticketId || !isOpen) return;

    // Éviter de recharger le ticket si on l'a déjà
    if (ticket && ticket.id === ticketId && ticketLoadedRef.current) {
      logger.info(`Ticket ${ticketId} déjà chargé, pas besoin de le récupérer à nouveau`);
      return;
    }

    try {
      setLoading(true);
      ticketLoadedRef.current = false;

      logger.info(`Récupération des détails du ticket ${ticketId}`);
      const fetchedTicket = await getTicket(ticketId);

      if (fetchedTicket) {
        setTicket(fetchedTicket);
        ticketLoadedRef.current = true;
      } else {
        setError(new Error('Ticket non trouvé'));
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Une erreur est survenue'));
    } finally {
      setLoading(false);
    }
  };

  // Charger automatiquement la liste des utilisateurs du staff quand la modale d'assignation s'ouvre
  useEffect(() => {
    if (showAssignModal) {
      loadStaffUsers();
    }
  }, [showAssignModal]);

  // Fonction pour récupérer les détails du ticket
  useEffect(() => {
    fetchTicketDetails();
  }, [ticketId, isOpen, getTicket]);

  // Récupérer les détails de l'utilisateur si on a un ticket avec un utilisateur
  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!ticket || !ticket.user || !ticket.user.id) return;

      try {
        setUserLoading(true);

        // D'abord essayer de récupérer le slug de l'utilisateur
        const slugResponse = await axios.get(`/api/users/get-slug/${ticket.user.id}`, API_CONFIG);
        if (!slugResponse.data.success || !slugResponse.data.slug) {
          logger.warn('Impossible de récupérer le slug de l\'utilisateur');
          return;
        }

        // Ensuite récupérer le profil complet avec le slug
        const response = await axios.get(`/api/users/profil/${slugResponse.data.slug}`, API_CONFIG);
        if (response.data) {
          setUserDetails(response.data);
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération du profil utilisateur:', error);
      } finally {
        setUserLoading(false);
      }
    };

    fetchUserDetails();
  }, [ticket]);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d MMMM yyyy 'à' HH:mm", { locale: fr });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'nouveau':
        return '#3498db';
      case 'en_cours':
        return '#FF6B2C';
      case 'en_attente':
        return '#f39c12';
      case 'resolu':
        return '#2ecc71';
      case 'fermé':
        return '#95a5a6';
      default:
        return '#95a5a6';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgente':
        return '#e74c3c';
      case 'elevee':
        return '#f39c12';
      case 'normale':
        return '#FF965E';
      case 'faible':
        return '#2ecc71';
      default:
        return '#95a5a6';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technique':
        return '🔧';
      case 'facturation':
        return '💰';
      case 'compte':
        return '👤';
      case 'mission':
        return '📋';
      case 'autre':
        return '📌';
      default:
        return '📄';
    }
  };

  const handleEditClick = () => {
    // Rediriger vers la page d'édition du ticket
    navigate(isAdminRoute ? `/admin/support/ticket/${ticketId}/edit` : `/dashboard/support/ticket/${ticketId}/edit`);
    onClose();
  };

  // Vérifier si le ticket a moins de 30 minutes
  const canUserModify = useCallback(() => {
    if (!ticket || !user) return false;

    // Les admins/modos peuvent toujours modifier
    if (user.role === 'jobpadm' || user.role === 'jobmodo') return true;

    // L'utilisateur ne peut modifier que son propre ticket
    if (user.id !== ticket.user_id) return false;

    // Si le ticket est fermé ou résolu, l'utilisateur normal ne peut pas le modifier
    if (ticket.status === 'ferme' || ticket.status === 'resolu') return false;

    // Vérifier si le ticket a moins de 30 minutes
    const creationDate = new Date(ticket.created_at);
    const now = new Date();
    const minutesSinceCreation = differenceInMinutes(now, creationDate);

    return minutesSinceCreation <= 30;
  }, [ticket, user]);

  // Obtenir le temps restant en minutes
  const getRemainingTime = useCallback(() => {
    if (!ticket) return 0;

    const creationDate = new Date(ticket.created_at);
    const now = new Date();
    const minutesSinceCreation = differenceInMinutes(now, creationDate);

    return Math.max(0, 30 - minutesSinceCreation);
  }, [ticket]);

  // Gérer la suppression du ticket
  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = async () => {
    if (!ticket || !ticketId) return;

    try {
      await supportTicketService.deleteTicket(ticketId);
      notify('Ticket supprimé avec succès', 'success');
      setShowDeleteConfirmation(false);
      onClose();
      // Rediriger vers la liste des tickets
      navigate(isAdminRoute ? '/admin/support/tickets' : '/dashboard/support/tickets');
    } catch (error) {
      logger.error('Erreur lors de la suppression du ticket:', error);
      notify('Erreur lors de la suppression du ticket', 'error');
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  // Animations pour le contenu de la modal
  const modalContentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };

  // Définir les statuts de ticket
  const ticketStatuses = [
    { value: 'nouveau', label: 'Nouveau', color: '#3498db' },
    { value: 'en_cours', label: 'En cours', color: '#FF6B2C' },
    { value: 'en_attente', label: 'En attente', color: '#f39c12' },
    { value: 'resolu', label: 'Résolu', color: '#2ecc71' },
    { value: 'ferme', label: 'Fermé', color: '#95a5a6' }
  ];

  // Fonction pour mettre à jour rapidement le statut d'un ticket
  const handleQuickStatusChange = async (newStatus: string) => {
    // Ouvrir la boîte de dialogue de confirmation
    setStatusToChange(newStatus);
  };

  // Confirmer le changement de statut
  const confirmStatusChange = async () => {
    if (!ticket || !ticketId || !statusToChange) return;

    try {
      await supportTicketService.updateTicket(ticketId, { status: statusToChange });

      // Mettre à jour le ticket dans l'état local
      setTicket({
        ...ticket,
        status: statusToChange
      });

      notify(`Statut du ticket mis à jour : ${statusToChange.replace('_', ' ')}`, 'success');
      setStatusToChange(null); // Fermer la boîte de dialogue

      // Notifier le parent qu'un changement a été effectué
      if (onTicketUpdated) {
        onTicketUpdated();
      }
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du statut du ticket:`, error);
      notify('Erreur lors de la mise à jour du statut', 'error');
      setStatusToChange(null); // Fermer la boîte de dialogue même en cas d'erreur
    }
  };

  // Annuler le changement de statut
  const cancelStatusChange = () => {
    setStatusToChange(null);
  };

  // Fonction pour assigner le ticket à un utilisateur
  const handleAssignTicket = async (userId: string | null) => {
    if (!ticket) return;

    try {
      setAssigningTicket(true);

      // Données à mettre à jour dans le ticket
      const updateData: any = {
        assigned_to: userId // Envoyer explicitement null lorsque userId est null
      };

      // Si on assigne le ticket à un utilisateur, changer automatiquement le statut à "en_attente"
      if (userId) {
        updateData.status = 'en_attente';
      }

      // Mettre à jour le ticket
      const updatedTicket = await supportTicketService.updateTicket(ticket.id, updateData);

      if (updatedTicket) {
        setTicket(updatedTicket);

        // Ajouter un commentaire automatique pour l'assignation
        if (userId) {
          try {
            // Récupérer les infos de l'utilisateur assigné si disponible
            let assignedUserInfo = "un agent de support";
            if (userId === user?.id) {
              assignedUserInfo = "vous-même";
            } else if (staffUsers.length > 0) {
              const assignedUser = staffUsers.find(u => u.id === userId);
              if (assignedUser) {
                assignedUserInfo = `${assignedUser.first_name || ''} ${assignedUser.last_name || ''}`.trim() || assignedUser.email || "un agent de support";
              }
            }

            // Créer un commentaire automatique
            await supportTicketService.addTicketComment({
              ticket_id: ticket.id,
              message: `Le ticket a été assigné à ${assignedUserInfo} et le statut a été changé à "En attente".`,
              is_internal: true // Commentaire interne visible seulement par le staff
            });

            logger.info(`Commentaire automatique ajouté pour l'assignation du ticket ${ticket.id}`);
          } catch (commentError) {
            logger.error('Erreur lors de l\'ajout du commentaire automatique:', commentError);
            // Ne pas bloquer le processus si l'ajout du commentaire échoue
          }
        }

        // Mettre à jour la liste des tickets si une fonction de callback est fournie
        if (onTicketUpdated) {
          onTicketUpdated();
        }

        // Afficher une notification de succès
        notify(
          userId
            ? `Le ticket a été assigné avec succès${userId === user?.id ? ' à vous' : ''} et le statut a été changé à "En attente"`
            : 'Le ticket a été désassigné avec succès',
          'success'
        );

        // Fermer la modale d'assignation si elle est ouverte
        setShowAssignModal(false);

        // Recharger les détails du ticket pour afficher le commentaire ajouté
        fetchTicketDetails();
      }
    } catch (error) {
      logger.error('Erreur lors de l\'assignation du ticket:', error);
      notify('Une erreur est survenue lors de l\'assignation du ticket', 'error');
    } finally {
      setAssigningTicket(false);
    }
  };

  // Fonction pour s'assigner le ticket à soi-même
  const handleAssignToMe = () => {
    if (!user) return;
    handleAssignTicket(user.id);
  };

  // Fonction pour charger la liste des utilisateurs du staff
  const loadStaffUsers = async () => {
    try {
      setLoadingStaffUsers(true);
      // Appel à l'API pour récupérer les utilisateurs ayant le rôle admin ou modo
      const staffList = await supportTicketService.getStaffUsers();
      setStaffUsers(staffList);
    } catch (error) {
      logger.error('Erreur lors du chargement des utilisateurs du staff:', error);
      notify('Impossible de charger la liste des utilisateurs du staff', 'error');
    } finally {
      setLoadingStaffUsers(false);
    }
  };

  // Filtrer les utilisateurs du staff en fonction de la recherche
  const filteredStaffUsers = staffUsers.filter(staffUser =>
    staffUser.email.toLowerCase().includes(staffUserSearch.toLowerCase()) ||
    (staffUser.first_name && staffUser.first_name.toLowerCase().includes(staffUserSearch.toLowerCase())) ||
    (staffUser.last_name && staffUser.last_name.toLowerCase().includes(staffUserSearch.toLowerCase()))
  );

  // État pour tracker le mode d'assistance généré
  const [assistanceMode, setAssistanceMode] = useState<'user' | 'staff' | null>(null);

  // Fonctions pour l'assistance IA
  const handleGenerateAiAssistance = async (mode: 'user' | 'staff') => {
    if (!ticketId) return;

    const success = await generateAssistance({ ticketId, mode });
    if (success) {
      setAssistanceMode(mode); // Stocker le mode utilisé
      setShowAiAssistance(true);
    }
  };

  const handleCloseAiAssistance = () => {
    setShowAiAssistance(false);
    setAssistanceMode(null); // Réinitialiser le mode
    clearAssistance();
  };

  return (
    <>
      <ModalPortal isOpen={isOpen} onBackdropClick={onClose}>
        <motion.div
          variants={modalContentVariants}
          initial="hidden"
          animate="visible"
          style={{ width: '100%', maxWidth: '900px', maxHeight: '90vh', margin: '0 20px' }}
        >
          <Paper
            elevation={5}
            sx={{
              borderRadius: '16px',
              overflow: 'hidden',
              position: 'relative',
              maxHeight: '90vh',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                bgcolor: '#FF6B2C',
                color: 'white',
                p: 2,
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Détails du ticket
              </Typography>
              <IconButton onClick={onClose} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </Box>

            <Box sx={{ p: 0, flexGrow: 1, overflow: 'auto' }}>
              {loading ? (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px',
                    gap: 2
                  }}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      <CircularProgress
                        size={60}
                        thickness={4}
                        sx={{
                          color: '#FF6B2C',
                          opacity: 0.7
                        }}
                      />
                      <CircularProgress
                        size={60}
                        thickness={4}
                        sx={{
                          color: '#FF965E',
                          position: 'absolute',
                          left: 0,
                          animationDuration: '1.2s'
                        }}
                      />
                    </Box>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{
                        fontWeight: 500,
                        textAlign: 'center'
                      }}
                    >
                      Chargement des détails du ticket...
                    </Typography>
                  </motion.div>
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ m: 3 }}>
                  {error.message}
                </Alert>
              ) : ticket ? (
                <Box sx={{ p: 3 }}>
                  <Box
                    sx={{
                      pb: 2,
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      justifyContent: 'space-between',
                      alignItems: { xs: 'flex-start', sm: 'flex-start' },
                      gap: { xs: 2, sm: 0 }
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        color: '#333',
                        mb: { xs: 0, sm: 1.5 },
                        width: '100%'
                      }}
                    >
                      {ticket.title}
                    </Typography>

                    <Box sx={{
                      display: 'flex',
                      gap: 1,
                      alignItems: 'center',
                      flexWrap: { xs: 'wrap', sm: 'nowrap' },
                      justifyContent: { xs: 'flex-start', sm: 'flex-end' },
                      width: { xs: '100%', sm: 'auto' },
                      mt: { xs: 0, sm: 1 }
                    }}>
                      {/* Indicateur de temps restant - visible uniquement pour les utilisateurs réguliers */}
                      {user?.id === ticket.user_id && user?.role !== 'jobpadm' && user?.role !== 'jobmodo' && canUserModify() && (
                        <Tooltip
                          title="Vous avez 30 minutes après la création du ticket pour le modifier ou le supprimer"
                          arrow
                          placement="top"
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              bgcolor: getRemainingTime() < 5 ? 'rgba(229, 57, 53, 0.1)' : 'rgba(255, 107, 44, 0.1)',
                              borderRadius: '12px',
                              px: 1.5,
                              py: 0.7,
                              mr: { xs: 'auto', sm: 1 },
                              order: { xs: -1, sm: 0 },
                              border: getRemainingTime() < 5 ? '1px solid rgba(229, 57, 53, 0.3)' : 'none',
                            }}
                          >
                            <EditCalendarIcon sx={{
                              fontSize: '1rem',
                              color: getRemainingTime() < 5 ? '#e53935' : '#FF6B2C',
                              mr: 0.7
                            }} />
                            <Typography
                              variant="caption"
                              sx={{
                                color: getRemainingTime() < 5 ? '#e53935' : '#FF6B2C',
                                fontWeight: 'medium',
                                whiteSpace: 'nowrap',
                                fontSize: '0.75rem'
                              }}
                            >
                              {getRemainingTime() > 0
                                ? `Modifiable : ${getRemainingTime()} min restantes`
                                : "Délai de modification expiré"}
                            </Typography>
                          </Box>
                        </Tooltip>
                      )}

                      {/* Bouton Modifier - visible si l'utilisateur peut modifier le ticket */}
                      {canUserModify() && (
                        <Button
                          variant="contained"
                          startIcon={<EditIcon />}
                          onClick={handleEditClick}
                          sx={{
                            bgcolor: '#FF6B2C',
                            '&:hover': {
                              bgcolor: '#FF7A35',
                            },
                            borderRadius: '8px',
                            boxShadow: '0 2px 8px rgba(255, 107, 44, 0.2)',
                            flexGrow: { xs: 1, sm: 0 },
                            flexBasis: { xs: '48%', sm: 'auto' }
                          }}
                        >
                          Modifier
                        </Button>
                      )}

                      {/* Bouton Supprimer pour utilisateurs réguliers - visible uniquement si c'est leur ticket et moins de 30 min */}
                      {user?.id === ticket.user_id && user?.role !== 'jobpadm' && user?.role !== 'jobmodo' && canUserModify() && (
                        <Button
                          variant="outlined"
                          startIcon={<DeleteIcon />}
                          onClick={handleDeleteClick}
                          sx={{
                            borderColor: '#e74c3c',
                            color: '#e74c3c',
                            '&:hover': {
                              bgcolor: 'rgba(231, 76, 60, 0.05)',
                              borderColor: '#c0392b',
                            },
                            borderRadius: '8px',
                            ml: { xs: 0, sm: 1 },
                            flexGrow: { xs: 1, sm: 0 },
                            flexBasis: { xs: '48%', sm: 'auto' }
                          }}
                        >
                          Supprimer
                        </Button>
                      )}

                      {/* Bouton Supprimer pour admins/modos - toujours visible pour eux */}
                      {(user?.role === 'jobpadm' || user?.role === 'jobmodo') && (
                        <Button
                          variant="outlined"
                          startIcon={<DeleteIcon />}
                          onClick={handleDeleteClick}
                          sx={{
                            borderColor: '#e74c3c',
                            color: '#e74c3c',
                            '&:hover': {
                              bgcolor: 'rgba(231, 76, 60, 0.05)',
                              borderColor: '#c0392b',
                            },
                            borderRadius: '8px',
                            ml: { xs: 0, sm: 1 },
                            flexGrow: { xs: 1, sm: 0 },
                            flexBasis: { xs: '48%', sm: 'auto' }
                          }}
                        >
                          Supprimer
                        </Button>
                      )}
                    </Box>
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: 2,
                      mb: 3,
                      alignItems: 'center'
                    }}
                  >
                    <Chip
                      icon={<AssignmentIcon style={{ color: 'white' }} />}
                      label={formatTicketStatus(ticket.status)}
                      sx={{
                        bgcolor: getStatusColor(ticket.status),
                        color: 'white',
                        fontWeight: 500,
                        borderRadius: '8px',
                        py: 0.5,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                      }}
                    />
                    <Chip
                      icon={<PriorityHighIcon style={{ color: 'white' }} />}
                      label={formatTicketPriority(ticket.priority)}
                      sx={{
                        bgcolor: getPriorityColor(ticket.priority),
                        color: 'white',
                        fontWeight: 500,
                        borderRadius: '8px',
                        py: 0.5,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                      }}
                    />
                    <Chip
                      avatar={<Avatar>{getCategoryIcon(ticket.category)}</Avatar>}
                      label={ticket.category}
                      variant="outlined"
                      sx={{
                        borderRadius: '8px',
                        py: 0.5,
                        fontWeight: 500,
                        borderColor: 'rgba(0, 0, 0, 0.12)',
                      }}
                    />
                  </Box>

                  {/* Actions rapides pour admin/modo */}
                  {(user?.role === 'jobpadm' || user?.role === 'jobmodo') && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1.5,
                        mb: 4,
                        pb: 2.5,
                        pt: 0.5,
                        px: 2,
                        borderRadius: '12px',
                        backgroundColor: 'rgba(255, 247, 240, 0.5)',
                        border: '1px solid rgba(255, 107, 44, 0.1)',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Typography
                        variant="subtitle1"
                        sx={{
                          width: '100%',
                          mt: 1.5,
                          mb: 1.5,
                          color: '#444',
                          fontWeight: 600,
                          display: 'flex',
                          alignItems: 'center',
                          fontSize: '1rem'
                        }}
                      >
                        <AssignmentIcon sx={{ color: '#FF6B2C', mr: 1, fontSize: '1.2rem' }} />
                        Actions rapides
                        <Box
                          sx={{
                            height: '2px',
                            width: '30px',
                            backgroundColor: '#FF6B2C',
                            ml: 1.5,
                            borderRadius: '2px'
                          }}
                        />
                      </Typography>

                      <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1.5,
                        width: '100%',
                        justifyContent: 'flex-start'
                      }}>
                        {ticketStatuses.map((status) => (
                          <Button
                            key={status.value}
                            variant={ticket.status === status.value ? "contained" : "outlined"}
                            size="small"
                            disabled={ticket.status === status.value}
                            onClick={() => handleQuickStatusChange(status.value)}
                            sx={{
                              borderRadius: '20px',
                              px: 2,
                              py: 0.8,
                              fontSize: '0.85rem',
                              backgroundColor: ticket.status === status.value ? status.color : 'transparent',
                              borderColor: status.color,
                              color: ticket.status === status.value ? 'white' : status.color,
                              '&:hover': {
                                backgroundColor: `${status.color}20`,
                                borderColor: status.color,
                                transform: 'translateY(-2px)',
                                boxShadow: '0 4px 10px rgba(0,0,0,0.07)',
                                transition: 'all 0.2s ease'
                              },
                              '&.Mui-disabled': {
                                backgroundColor: ticket.status === status.value ? status.color : 'transparent',
                                color: ticket.status === status.value ? 'white' : 'rgba(0, 0, 0, 0.26)',
                              }
                            }}
                          >
                            {status.label}
                          </Button>
                        ))}
                      </Box>
                    </Box>
                  )}

                  {/* Section Assistance IA */}
                  <Box
                    sx={{
                      mb: 4,
                      pb: 2.5,
                      pt: 0.5,
                      px: 2,
                      borderRadius: '12px',
                      backgroundColor: 'rgba(255, 247, 240, 0.5)',
                      border: '1px solid rgba(255, 107, 44, 0.1)',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.03)'
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      sx={{
                        width: '100%',
                        mt: 1.5,
                        mb: 1.5,
                        color: '#444',
                        fontWeight: 600,
                        display: 'flex',
                        alignItems: 'center',
                        fontSize: '1rem'
                      }}
                    >
                      <AutoAwesomeIcon sx={{ color: '#FF6B2C', mr: 1, fontSize: '1.2rem' }} />
                      Assistance IA
                      <Box
                        sx={{
                          height: '2px',
                          width: '30px',
                          backgroundColor: '#FF6B2C',
                          ml: 1.5,
                          borderRadius: '2px'
                        }}
                      />
                    </Typography>

                    <Box sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: 1.5,
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}>
                      {/* Bouton assistance utilisateur */}
                      <Button
                        variant="outlined"
                        size="small"
                        disabled={aiLoading}
                        onClick={() => handleGenerateAiAssistance('user')}
                        sx={{
                          borderRadius: '20px',
                          px: 2,
                          py: 0.8,
                          fontSize: '0.85rem',
                          backgroundColor: 'transparent',
                          borderColor: '#FF6B2C',
                          color: '#FF6B2C',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            borderColor: '#FF6B2C',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 4px 10px rgba(0,0,0,0.07)',
                            transition: 'all 0.2s ease'
                          },
                          '&.Mui-disabled': {
                            backgroundColor: 'transparent',
                            color: 'rgba(0, 0, 0, 0.26)',
                            borderColor: 'rgba(0, 0, 0, 0.12)',
                          }
                        }}
                        startIcon={aiLoading ? <CircularProgress size={16} /> : <PsychologyIcon />}
                      >
                        {aiLoading ? 'Génération...' : 'Aide utilisateur'}
                      </Button>

                      {/* Bouton assistance staff - visible uniquement pour les admins/modos */}
                      {(user?.role === 'jobpadm' || user?.role === 'jobmodo') && (
                        <Button
                          variant="outlined"
                          size="small"
                          disabled={aiLoading}
                          onClick={() => handleGenerateAiAssistance('staff')}
                          sx={{
                            borderRadius: '20px',
                            px: 2,
                            py: 0.8,
                            fontSize: '0.85rem',
                            backgroundColor: 'transparent',
                            borderColor: '#FF965E',
                            color: '#FF965E',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 150, 94, 0.1)',
                              borderColor: '#FF965E',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 10px rgba(0,0,0,0.07)',
                              transition: 'all 0.2s ease'
                            },
                            '&.Mui-disabled': {
                              backgroundColor: 'transparent',
                              color: 'rgba(0, 0, 0, 0.26)',
                              borderColor: 'rgba(0, 0, 0, 0.12)',
                            }
                          }}
                          startIcon={aiLoading ? <CircularProgress size={16} /> : <AutoAwesomeIcon />}
                        >
                          {aiLoading ? 'Génération...' : 'Assistance équipe'}
                        </Button>
                      )}
                    </Box>

                    {/* Affichage de l'assistance générée */}
                    {showAiAssistance && assistance && (
                      <Box sx={{ mt: 2 }}>
                        <Paper
                          elevation={0}
                          sx={{
                            p: 2,
                            borderRadius: '8px',
                            bgcolor: 'rgba(255, 255, 255, 0.8)',
                            border: '1px solid rgba(255, 107, 44, 0.2)',
                          }}
                        >
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#444' }}>
                              Suggestion IA
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {creditsRemaining !== null && (
                                <Typography variant="caption" color="text.secondary">
                                  Crédits restants: {creditsRemaining}
                                </Typography>
                              )}
                              <Tooltip
                                title={
                                  assistanceMode === 'staff'
                                    ? "L'assistance équipe est destinée à l'analyse interne. Utilisez plutôt le bouton 'Générer avec IA' dans la zone de commentaire pour rédiger une réponse au client."
                                    : "Transférer ce message vers la zone de commentaire"
                                }
                                arrow
                              >
                                <span>
                                  <Button
                                    size="small"
                                    variant="outlined"
                                    disabled={assistanceMode === 'staff'}
                                    onClick={() => {
                                      if (assistanceMode !== 'staff') {
                                        // Transférer le message IA vers la zone de commentaire
                                        const event = new CustomEvent('transfer-ai-to-comment', {
                                          detail: { content: assistance }
                                        });
                                        window.dispatchEvent(event);
                                        notify('Message IA transféré vers la zone de commentaire', 'success');
                                      }
                                    }}
                                    sx={{
                                      borderColor: assistanceMode === 'staff' ? '#ccc' : '#FF6B2C',
                                      color: assistanceMode === 'staff' ? '#999' : '#FF6B2C',
                                      fontSize: '0.75rem',
                                      px: 1.5,
                                      py: 0.5,
                                      '&:hover': {
                                        backgroundColor: assistanceMode === 'staff' ? 'transparent' : 'rgba(255, 107, 44, 0.1)',
                                        borderColor: assistanceMode === 'staff' ? '#ccc' : '#FF6B2C',
                                      },
                                      '&:disabled': {
                                        borderColor: '#ccc',
                                        color: '#999',
                                      }
                                    }}
                                  >
                                    Utiliser
                                  </Button>
                                </span>
                              </Tooltip>
                              <IconButton
                                size="small"
                                onClick={handleCloseAiAssistance}
                                sx={{ color: 'text.secondary' }}
                              >
                                <CloseIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', lineHeight: 1.6 }}>
                            {assistance}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                  </Box>

                  <Grid container spacing={3}>
                    <Grid size={{ xs: 12, md: 8 }}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 3,
                          mb: 3,
                          borderRadius: '12px',
                          bgcolor: 'rgba(255, 247, 240, 0.5)',
                          border: '1px solid rgba(255, 107, 44, 0.1)',
                        }}
                      >
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 2, color: '#444' }}>
                          Description
                        </Typography>
                        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                          {ticket.description || "Aucune description fournie."}
                        </Typography>
                      </Paper>

                      {/* Pièces jointes */}
                      <TicketAttachmentsList ticketId={ticketId} />

                      <Divider sx={{ my: 3 }} />

                      <CommentsList
                        ticketId={ticketId}
                        readOnly={false}
                        showInternal={user?.role === 'jobpadm' || user?.role === 'jobmodo'}
                        onTicketUpdated={(forceRefresh) => {
                          // Si forceRefresh est true, recharger le ticket pour refléter le changement de statut
                          if (forceRefresh) {
                            // Réinitialiser l'état pour forcer un rechargement complet
                            ticketLoadedRef.current = false;
                            // Recharger les données du ticket
                            fetchTicketDetails();
                          }

                          // Notification au parent
                          if (onTicketUpdated) {
                            onTicketUpdated();
                          }
                        }}
                        ticketStatus={ticket?.status}
                        ticketCategory={ticket?.category}
                        ticketTitle={ticket?.title}
                        ticketDescription={ticket?.description}
                      />
                    </Grid>

                    <Grid size={{ xs: 12, md: 4 }}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 2,
                          borderRadius: '12px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                          mb: 2,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: 600, mb: 2, color: '#444' }}
                        >
                          Informations
                        </Typography>

                        <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                          <PersonIcon
                            sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                          />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Créé par
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 0.5 }}>
                              {userDetails?.profil?.data?.prenom && userDetails?.profil?.data?.nom
                                ? `${userDetails.profil.data.prenom} ${userDetails.profil.data.nom}`
                                : ticket.user?.first_name && ticket.user?.last_name
                                  ? `${ticket.user.first_name} ${ticket.user.last_name}`
                                  : ticket.user?.email || 'Utilisateur inconnu'}
                            </Typography>
                            {ticket.user?.email && (
                              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                {ticket.user.email}
                              </Typography>
                            )}
                          </Box>
                        </Box>

                        {/* Ville de l'utilisateur */}
                        {(userDetails?.profil?.data?.ville ||
                          ticket.user?.profil?.data?.ville) && (
                          <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                            <LocationOnIcon
                              sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                            />
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Ville
                              </Typography>
                              <Typography variant="body1">
                                {userDetails?.profil?.data?.ville || ticket.user?.profil?.data?.ville}
                                {(userDetails?.profil?.data?.code_postal || ticket.user?.profil?.data?.code_postal) &&
                                  ` (${userDetails?.profil?.data?.code_postal || ticket.user?.profil?.data?.code_postal})`}
                              </Typography>
                            </Box>
                          </Box>
                        )}

                        {/* Statut en ligne */}
                        <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                          <Box
                            component="span"
                            sx={{
                              width: '10px',
                              height: '10px',
                              borderRadius: '50%',
                              backgroundColor: (userDetails?.is_online || ticket.user?.is_online) ? '#2ecc71' : '#95a5a6',
                              boxShadow: (userDetails?.is_online || ticket.user?.is_online) ? '0 0 0 2px rgba(46, 204, 113, 0.2)' : 'none',
                              mr: 1.5,
                              ml: 0.25
                            }}
                          />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Statut
                            </Typography>
                            <Typography variant="body1">
                              {(userDetails?.is_online || ticket.user?.is_online) ? 'En ligne' : 'Hors ligne'}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Dernière connexion */}
                        {(userDetails?.last_login || ticket.user?.last_login) && (
                          <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                            <AccessTimeIcon
                              sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                            />
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Dernière connexion
                              </Typography>
                              <Typography variant="body1">
                                {formatDate(userDetails?.last_login || ticket.user?.last_login)}
                              </Typography>
                            </Box>
                          </Box>
                        )}

                        {/* Date d'inscription */}
                        {(userDetails?.date_inscription || ticket.user?.date_inscription) && (
                          <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                            <EventNoteIcon
                              sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                            />
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Date d'inscription
                              </Typography>
                              <Typography variant="body1">
                                {formatDate(userDetails?.date_inscription || ticket.user?.date_inscription)}
                              </Typography>
                            </Box>
                          </Box>
                        )}

                        <Box sx={{ display: 'flex', mb: 1.5, alignItems: 'center' }}>
                          <EventNoteIcon
                            sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                          />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Créé le
                            </Typography>
                            <Typography variant="body1">
                              {formatDate(ticket.created_at)}
                            </Typography>
                          </Box>
                        </Box>

                        {ticket.updated_at && (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <EventNoteIcon
                              sx={{ color: '#FF6B2C', mr: 1.5, fontSize: '1.2rem' }}
                            />
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Dernière mise à jour
                              </Typography>
                              <Typography variant="body1">
                                {formatDate(ticket.updated_at)}
                              </Typography>
                            </Box>
                          </Box>
                        )}
                      </Paper>

                      {/* Informations SLA et assignation pour l'admin */}
                      <Paper
                        sx={{
                          p: { xs: 2, sm: 2.5 },
                          mb: 2,
                          borderRadius: '8px'
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 500,
                            mb: 2,
                            pb: 1.5,
                            borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
                          }}
                        >
                          Administration
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                          <PersonIcon
                            sx={{
                              color: '#FF6B2C',
                              mt: 0.5
                            }}
                          />
                          <Typography
                            variant="body2"
                            sx={{ fontWeight: 500, color: 'text.secondary', mt: 0.5 }}
                          >
                            Assignation
                          </Typography>
                        </Box>

                        {ticket.assignee ? (
                          <Box
                            sx={{
                              ml: 0,
                              mt: 2,
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              gap: 2,
                              width: '100%'
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                width: '100%',
                                flexShrink: 1,
                                overflow: 'hidden'
                              }}
                            >
                              <Avatar
                                sx={{
                                  bgcolor: '#FF6B2C',
                                  color: 'white',
                                  width: 32,
                                  height: 32,
                                  fontSize: '0.875rem',
                                  flexShrink: 0
                                }}
                              >
                                {ticket.assignee.email.charAt(0).toUpperCase()}
                              </Avatar>
                              <Box sx={{ ml: 1.5, overflow: 'hidden', flexShrink: 1, width: '100%' }}>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontWeight: 500,
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                  }}
                                >
                                  {ticket.assignee.first_name && ticket.assignee.last_name
                                    ? `${ticket.assignee.first_name} ${ticket.assignee.last_name.charAt(0).toUpperCase()}.`
                                    : ticket.assignee.email.split('@')[0]}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{
                                    display: 'block',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap'
                                  }}
                                >
                                  {ticket.assignee.email}
                                </Typography>
                              </Box>
                            </Box>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleAssignTicket(null)}
                              sx={{
                                borderColor: 'rgba(0, 0, 0, 0.12)',
                                color: 'text.secondary',
                                width: '100%',
                                maxWidth: '100%',
                                flexShrink: 0,
                                whiteSpace: 'nowrap',
                                justifyContent: 'center'
                              }}
                              startIcon={<PersonIcon sx={{ fontSize: '1rem' }} />}
                            >
                              Désassigner
                            </Button>
                          </Box>
                        ) : (
                          <Box sx={{
                            mt: 2,
                            width: '100%',
                            display: 'flex',
                            flexDirection: { xs: 'column', sm: 'column', md: 'row' },
                            gap: 1.5
                          }}>
                            <Button
                              variant="outlined"
                              color="primary"
                              size="small"
                              onClick={() => handleAssignToMe()}
                              sx={{
                                borderColor: '#FF6B2C',
                                color: '#FF6B2C',
                                '&:hover': {
                                  backgroundColor: 'rgba(255, 107, 44, 0.04)',
                                  borderColor: '#FF6B2C',
                                },
                                width: '100%'
                              }}
                            >
                              M'assigner
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => setShowAssignModal(true)}
                              sx={{
                                borderColor: 'rgba(0, 0, 0, 0.12)',
                                color: 'text.secondary',
                                width: '100%'
                              }}
                            >
                              Assigner à un autre
                            </Button>
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  </Grid>
                </Box>
              ) : (
                <Box sx={{ p: 3 }}>
                  <Alert severity="info">
                    Aucune information sur ce ticket n'est disponible.
                  </Alert>
                </Box>
              )}
            </Box>
          </Paper>
        </motion.div>
      </ModalPortal>

      {/* Modale de confirmation de suppression */}
      <ModalPortal isOpen={showDeleteConfirmation} onBackdropClick={cancelDelete}>
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          style={{
            width: '100%',
            maxWidth: '450px',
            margin: '0 20px',
            position: 'relative',
            zIndex: 1500
          }}
        >
          <Paper
            elevation={8}
            sx={{
              borderRadius: '16px',
              overflow: 'hidden',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.15)'
            }}
          >
            <Box
              sx={{
                bgcolor: '#e74c3c',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DeleteIcon sx={{ mr: 1.5 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Confirmation de suppression
                </Typography>
              </Box>
              <IconButton onClick={cancelDelete} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </Box>

            <Box sx={{ p: 3 }}>
              <Typography variant="body1" sx={{ mb: 3, color: '#333' }}>
                Êtes-vous sûr de vouloir supprimer ce ticket ?
                <Typography component="span" sx={{ display: 'block', mt: 1, fontWeight: 'bold', color: '#e74c3c' }}>
                  Cette action est irréversible.
                </Typography>
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
                <Button
                  variant="outlined"
                  onClick={cancelDelete}
                  sx={{
                    borderColor: 'rgba(0, 0, 0, 0.23)',
                    color: 'text.primary',
                    '&:hover': {
                      borderColor: 'rgba(0, 0, 0, 0.5)',
                      bgcolor: 'rgba(0, 0, 0, 0.03)'
                    },
                    borderRadius: '8px',
                    px: 3
                  }}
                >
                  Annuler
                </Button>
                <Button
                  variant="contained"
                  onClick={confirmDelete}
                  sx={{
                    bgcolor: '#e74c3c',
                    '&:hover': {
                      bgcolor: '#c0392b'
                    },
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(231, 76, 60, 0.2)',
                    px: 3
                  }}
                >
                  Supprimer
                </Button>
              </Box>
            </Box>
          </Paper>
        </motion.div>
      </ModalPortal>

      {/* Modale de confirmation de changement de statut */}
      <Dialog
        open={statusToChange !== null}
        onClose={cancelStatusChange}
        aria-labelledby="status-dialog-title"
        aria-describedby="status-dialog-description"
      >
        <DialogTitle id="status-dialog-title" sx={{ fontWeight: 600, color: '#FF6B2C', pb: 1 }}>
          Confirmation du changement
        </DialogTitle>

        <DialogContent dividers>
          <DialogContentText id="status-dialog-description">
            Êtes-vous sûr de vouloir changer le statut du ticket à "{statusToChange ? ticketStatuses.find(s => s.value === statusToChange)?.label : ''}" ?
          </DialogContentText>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            variant="outlined"
            onClick={cancelStatusChange}
            sx={{
              color: 'text.primary',
              borderColor: 'rgba(0, 0, 0, 0.23)',
            }}
          >
            Annuler
          </Button>
          <Button
            variant="contained"
            onClick={confirmStatusChange}
            sx={{
              bgcolor: statusToChange ? ticketStatuses.find(s => s.value === statusToChange)?.color || '#FF6B2C' : '#FF6B2C',
              '&:hover': {
                bgcolor: statusToChange ?
                  ticketStatuses.find(s => s.value === statusToChange)?.color
                    ? `${ticketStatuses.find(s => s.value === statusToChange)?.color}d0`
                    : '#e86020'
                  : '#e86020'
              },
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
              px: 3
            }}
          >
            Confirmer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale d'assignation de ticket */}
      <Dialog
        open={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        maxWidth="sm"
        fullWidth
        aria-labelledby="assign-dialog-title"
      >
        <DialogTitle id="assign-dialog-title" sx={{
          fontWeight: 600,
          color: '#FF6B2C',
          pb: 1,
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
        }}>
          Assigner le ticket à un membre du staff
        </DialogTitle>

        <DialogContent dividers sx={{ p: 2 }}>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Rechercher un utilisateur..."
              variant="outlined"
              size="small"
              value={staffUserSearch}
              onChange={(e) => setStaffUserSearch(e.target.value)}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(255, 107, 44, 0.5)'
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C'
                  }
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: 'rgba(0, 0, 0, 0.5)' }} />
                  </InputAdornment>
                ),
              }}
            />

            {loadingStaffUsers ? (
              <Box display="flex" justifyContent="center" py={2}>
                <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />
              </Box>
            ) : staffUsers.length === 0 ? (
              <Box display="flex" justifyContent="center" py={2}>
                <Typography color="text.secondary">
                  Aucun utilisateur du staff disponible
                </Typography>
              </Box>
            ) : (
              <List sx={{
                width: '100%',
                maxHeight: '350px',
                overflow: 'auto',
                bgcolor: 'background.paper',
                borderRadius: 1
              }}>
                {filteredStaffUsers.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary="Aucun utilisateur trouvé"
                      primaryTypographyProps={{ align: 'center', color: 'text.secondary' }}
                    />
                  </ListItem>
                ) : (
                  filteredStaffUsers.map((staffUser) => (
                    <ListItem
                      key={staffUser.id}
                      component="div"
                      sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
                        '&:last-child': {
                          borderBottom: 'none'
                        },
                        '&:hover': {
                          bgcolor: 'rgba(255, 107, 44, 0.05)',
                          cursor: 'pointer'
                        },
                        py: 1
                      }}
                    >
                      <Box
                        onClick={() => !assigningTicket && handleAssignTicket(staffUser.id)}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                          opacity: assigningTicket ? 0.6 : 1,
                          pointerEvents: assigningTicket ? 'none' : 'auto'
                        }}
                      >
                        <Avatar
                          sx={{
                            bgcolor: staffUser.role === 'jobpadm' ? '#d32f2f' : '#FF6B2C',
                            width: 40,
                            height: 40,
                            mr: 2
                          }}
                        >
                          {staffUser.first_name
                            ? staffUser.first_name.charAt(0).toUpperCase()
                            : staffUser.email.charAt(0).toUpperCase()}
                        </Avatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography fontWeight={500}>
                                {staffUser.first_name && staffUser.last_name
                                  ? `${staffUser.first_name} ${staffUser.last_name.charAt(0).toUpperCase()}.`
                                  : staffUser.email}
                              </Typography>
                              {ticket && ticket.assigned_to === staffUser.id && (
                                <Chip
                                  size="small"
                                  label="Assigné"
                                  sx={{
                                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                                    color: '#FF6B2C',
                                    fontWeight: 600,
                                    height: 24,
                                    fontSize: '0.75rem'
                                  }}
                                />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <Typography variant="body2" color="text.secondary">
                                {staffUser.email}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  color: 'white',
                                  bgcolor: staffUser.role === 'jobpadm' ? '#d32f2f' : '#FF6B2C',
                                  px: 1,
                                  py: 0.25,
                                  borderRadius: '4px',
                                  ml: 1,
                                  fontSize: '0.7rem'
                                }}
                              >
                                {staffUser.role === 'jobpadm' ? 'Administrateur' : 'Modérateur'}
                              </Typography>
                            </Box>
                          }
                          primaryTypographyProps={{
                            fontWeight: 500
                          }}
                        />
                      </Box>
                    </ListItem>
                  ))
                )}
              </List>
            )}
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
          <Button
            onClick={() => setShowAssignModal(false)}
            variant="outlined"
            sx={{
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              '&:hover': {
                borderColor: '#e45a1b',
                bgcolor: 'rgba(255, 107, 44, 0.05)'
              },
              borderRadius: '8px'
            }}
            disabled={assigningTicket}
          >
            Annuler
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TicketDetailsModal;