import { Request, Response } from 'express';
import * as reportedContentService from '../services/reportedContentService';
import { restoreMaskedComment } from '../services/reportedContentService';
import { decryptUserDataAsync, decryptProfilDataAsync } from '../utils/encryption';
import logger from '../utils/logger';

// Signaler un contenu
export async function reportContent(req: Request, res: Response): Promise<void> {
  try {
    const { content_type, content_id, reason } = req.body;
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({ message: 'Utilisateur non authentifié.' });
      return;
    }
    if (!content_type || !content_id || !reason) {
      res.status(400).json({ message: 'Champs requis manquants.' });
      return;
    }
    const report = await reportedContentService.createReport({
      content_type: content_type || '',
      content_id: content_id || '',
      reason: reason || '',
      reported_by: userId || ''
    });
    res.status(201).json(report);
  } catch (err) {
    console.error('Erreur lors du signalement :', err);

    // Gérer spécifiquement l'erreur de signalement multiple
    if (err instanceof Error && err.message === 'Vous avez déjà signalé ce contenu aujourd\'hui.') {
      logger.info('DEBUG - Tentative de signalement multiple détectée dans le contrôleur', {
        userId: req.user?.userId,
        contentType: req.body.content_type,
        contentId: req.body.content_id
      });
      res.status(400).json({ message: err.message });
      return;
    }

    // Autres erreurs
    res.status(500).json({
      message: 'Erreur lors du signalement.',
      error: (err as Error).message || err,
      stack: (err as Error).stack || undefined
    });
  }
}

// Liste des signalements (admin/modo)
export async function getAllReports(req: Request, res: Response): Promise<void> {
  try {
    // Ajout du filtre par utilisateur signalé
    const userId = req.query.reported_by || req.query.reported_user_id;
    if (userId) {
      const reports = await reportedContentService.getReportsForUser(String(userId));
      res.json({ data: reports, count: reports.length });
      return;
    }
    const { page = 1, limit = 20, status } = req.query;
    const reports = await reportedContentService.getAllReports({
      page: Number(page),
      limit: Number(limit),
      status: status as string | undefined
    });
    res.json(reports);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération des signalements.', error: err });
  }
}

// Détail d'un signalement
export async function getReportById(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const report = await reportedContentService.getReportById(id);
    if (!report) {
      res.status(404).json({ message: 'Signalement non trouvé.' });
      return;
    }
    
    // Décrypter les données utilisateur si elles existent
    if (report.user) {
      // Décrypter les données de base de l'utilisateur
      report.user = await decryptUserDataAsync(report.user);
      
      // Décrypter les données du profil utilisateur
      if (report.user.user_profil && report.user.user_profil.length > 0) {
        report.user.user_profil = await Promise.all(
          report.user.user_profil.map((profil: any) => 
            decryptProfilDataAsync(profil)
          )
        );
      }
    }
    
    res.json(report);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération du signalement.', error: err });
  }
}

// Mise à jour/modération d'un signalement
export async function updateReport(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { status, admin_comment, action, notifyReporters, notifyAuthorByEmail, internal_note } = req.body;
    const adminId = req.user?.userId;
    const result = await reportedContentService.updateReport({
      id,
      status,
      admin_comment,
      admin_id: adminId,
      action,
      notifyReporters,
      notifyAuthorByEmail,
      internal_note,
    });
    res.json(result);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour du signalement.', error: err });
  }
}

// Liste des signalements individuels pour un contenu signalé
export async function getReportsForContent(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const reports = await reportedContentService.getReportsForContent(id);
    res.json(reports);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération des signalements individuels.', error: err });
  }
}

// Restaurer un commentaire masqué (admin/modo)
export async function restoreMaskedCommentController(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const adminId = req.user?.userId;
    if (!adminId) {
      res.status(401).json({ message: 'Non authentifié.' });
      return;
    }
    // Vérifier le rôle (jobpadm ou jobmodo)
    if (!req.user?.role || (req.user.role !== 'jobpadm' && req.user.role !== 'jobmodo')) {
      res.status(403).json({ message: 'Accès refusé.' });
      return;
    }
    await restoreMaskedComment(id, adminId || '');
    res.json({ success: true, message: 'Commentaire restauré avec succès.' });
  } catch (err: any) {
    res.status(400).json({ success: false, message: err.message || 'Erreur lors de la restauration.' });
  }
}

// Ajout d'une note interne (historique JSON)
export async function addInternalNote(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { message } = req.body;
    if (!message || typeof message !== 'string' || !message.trim()) {
      res.status(400).json({ message: 'Message requis.' });
      return;
    }
    const adminId = req.user?.userId;
    const adminName = req.user?.prenom && req.user?.nom ? `${req.user.prenom} ${req.user.nom}` : (req.user?.email || 'Staff');
    const now = new Date().toISOString();
    // Récupérer l'existant
    const report = await reportedContentService.getReportById(id);
    let notes = [];
    try { notes = JSON.parse(report.internal_note) || []; } catch { notes = []; }
    notes.unshift({ author_id: adminId, author_name: adminName, date: now, message });
    notes = notes.slice(0, 20);
    await reportedContentService.updateReport({ id, internal_note: JSON.stringify(notes) });
    res.json({ success: true, notes });
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de l\'ajout de la note interne.', error: err });
  }
}

// Mise à jour en masse de signalements
export async function bulkUpdateReports(req: Request, res: Response): Promise<void> {
  try {
    const { ids, status, admin_comment, action, notifyReporters, notifyAuthorByEmail } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      res.status(400).json({ message: 'Aucun signalement sélectionné.' });
      return;
    }
    
    const adminId = req.user?.userId;
    const results = [];
    const errors = [];
    
    // Traiter chaque signalement individuellement pour assurer le bon traitement des actions spécifiques
    for (const id of ids) {
      try {
        const result = await reportedContentService.updateReport({
          id,
          status,
          admin_comment: admin_comment ? `${admin_comment} (traitement groupé)` : 'Traitement groupé',
          admin_id: adminId,
          action,
          notifyReporters,
          notifyAuthorByEmail
        });
        results.push(result);
      } catch (err) {
        errors.push({ id, error: err instanceof Error ? err.message : String(err) });
      }
    }
    
    res.json({ 
      success: true, 
      results, 
      errors,
      totalProcessed: results.length,
      totalErrors: errors.length
    });
  } catch (err) {
    res.status(500).json({ 
      message: 'Erreur lors de la mise à jour groupée des signalements.', 
      error: err instanceof Error ? err.message : String(err) 
    });
  }
}