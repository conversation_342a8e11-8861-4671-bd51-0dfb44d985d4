import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  Typography,
  Chip,
  Divider,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  Paper,
  IconButton,
  Avatar,
  ListItem,
  ListItemAvatar,
  Checkbox,
  FormControlLabel,
  Tooltip
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import HistoryIcon from '@mui/icons-material/History';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import ReplyIcon from '@mui/icons-material/Reply';
import LockIcon from '@mui/icons-material/Lock';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useNavigate, useParams } from 'react-router-dom';
import { bugReportService } from '../../services/bugReportService';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import { useForm, Controller } from 'react-hook-form';
import {
  BugReport,
  BugReportUpdateRequest,
  BugReportVoteRequest,
  BugReportHistory,
  BugReportComment,
  BugReportCommentRequest
} from '../../types/bugReports';
import { useAuth } from '../../contexts/AuthContext';
import ModalPortal from '../ModalPortal';
import logger from '@/utils/logger';
import LoadingSpinner from '../LoadingSpinner';

// Styles communs pour les boutons d'action
const commonButtonStyles = {
  whiteSpace: 'nowrap',
  fontSize: '0.8125rem',
  padding: '6px 14px',
  borderRadius: '8px',
  fontWeight: 500,
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.2s ease-in-out',
  textTransform: 'none',
  minWidth: '110px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '6px',
  '&:active': {
    transform: 'translateY(1px)',
  },
};

const primaryButtonStyles = {
  ...commonButtonStyles,
  color: '#FF6B2C',
  borderColor: '#FF6B2C',
  backgroundColor: 'rgba(255, 107, 44, 0.03)',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.08)',
    borderColor: '#FF7A35',
    boxShadow: '0 3px 6px rgba(255, 107, 44, 0.15)'
  },
  '& .MuiSvgIcon-root': {
    color: '#FF6B2C',
    fontSize: '1.1rem',
  },
};

const dangerButtonStyles = {
  ...commonButtonStyles,
  color: '#F44336',
  borderColor: '#F44336',
  backgroundColor: 'rgba(244, 67, 54, 0.03)',
  '&:hover': {
    backgroundColor: 'rgba(244, 67, 54, 0.08)',
    borderColor: '#E57373',
    boxShadow: '0 3px 6px rgba(244, 67, 54, 0.15)'
  },
  '& .MuiSvgIcon-root': {
    color: '#F44336',
    fontSize: '1.1rem',
  },
};

const successButtonStyles = {
  ...commonButtonStyles,
  color: '#4CAF50',
  borderColor: '#4CAF50',
  backgroundColor: 'rgba(76, 175, 80, 0.03)',
  '&:hover': {
    backgroundColor: 'rgba(76, 175, 80, 0.08)',
    borderColor: '#66BB6A',
    boxShadow: '0 3px 6px rgba(76, 175, 80, 0.15)'
  },
  '&.Mui-disabled': {
    opacity: 0.6,
    color: '#4CAF50',
    borderColor: 'rgba(76, 175, 80, 0.5)'
  },
  '& .MuiSvgIcon-root': {
    color: '#4CAF50',
    fontSize: '1.1rem',
  },
};

// Formatage du status pour l'affichage
const getStatusChipProps = (status: string) => {
  switch (status) {
    case 'nouveau':
      return { 
        label: 'Nouveau', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 107, 44, 0.1)', color: '#FF6B2C', fontWeight: 500 }
      };
    case 'en_cours':
      return { 
        label: 'En cours', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 122, 53, 0.1)', color: '#FF7A35', fontWeight: 500 }
      };
    case 'resolu':
      return { 
        label: 'Résolu', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(46, 125, 50, 0.1)', color: '#2E7D32', fontWeight: 500 }
      };
    case 'rejete':
      return { 
        label: 'Rejeté', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    case 'attente_moderation':
      return { 
        label: 'En attente de modération', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(63, 81, 181, 0.1)', color: '#3F51B5', fontWeight: 500 }
      };
    case 'ferme':
      return { 
        label: 'Fermé', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    case 'reouvert':
      return { 
        label: 'Réouvert', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 152, 0, 0.1)', color: '#FF9800', fontWeight: 500 }
      };
      
    default:
      return { 
        label: status, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage du type pour l'affichage
const getTypeChipProps = (type: string) => {
  switch (type) {
    case 'bug':
      return { 
        label: 'Bug', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    case 'improvement':
      return { 
        label: 'Amélioration', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 150, 94, 0.1)', color: '#FF965E', fontWeight: 500 }
      };
    default:
      return { 
        label: type, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage de la priorité pour l'affichage
const getPriorityChipProps = (priority: string) => {
  switch (priority) {
    case 'faible':
      return { 
        label: 'Faible', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(46, 125, 50, 0.1)', color: '#2E7D32', fontWeight: 500 }
      };
    case 'moyenne':
      return { 
        label: 'Moyenne', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 150, 94, 0.1)', color: '#FF965E', fontWeight: 500 }
      };
    case 'elevee':
      return { 
        label: 'Élevée', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 107, 44, 0.1)', color: '#FF6B2C', fontWeight: 500 }
      };
    case 'critique':
      return { 
        label: 'Critique', 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(211, 47, 47, 0.1)', color: '#D32F2F', fontWeight: 500 }
      };
    default:
      return { 
        label: priority, 
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

// Formatage de la catégorie pour l'affichage
const getCategoryChipProps = (category: string) => {
  switch (category) {
    case 'interface':
      return {
        label: 'Interface',
        color: 'default' as const, 
        sx: { backgroundColor: 'rgba(156, 39, 176, 0.1)', color: '#9C27B0', fontWeight: 500 }
      };
    case 'fonctionnalite':
      return {
        label: 'Fonctionnalité',
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(103, 58, 183, 0.1)', color: '#673AB7', fontWeight: 500 }
      };
    case 'paiement':
      return {
        label: 'Paiement',
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(255, 87, 34, 0.1)', color: '#FF5722', fontWeight: 500 }
      };
    case 'securite':
      return {
        label: 'Sécurité',
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(121, 85, 72, 0.1)', color: '#795548', fontWeight: 500 }
      };
    case 'autre':
      return {
        label: 'Autre',
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(96, 125, 139, 0.1)', color: '#607D8B', fontWeight: 500 }
      };
    default:
      return {
        label: category,
        color: 'default' as const,
        sx: { backgroundColor: 'rgba(117, 117, 117, 0.1)', color: '#757575', fontWeight: 500 }
      };
  }
};

interface BugReportDetailProps {
  isAdmin?: boolean;
  initialEditMode?: boolean;
  reportId?: string;
  onReportUpdated?: (updatedReport: BugReport) => void;
}

const BugReportDetail: React.FC<BugReportDetailProps> = ({ isAdmin = false, initialEditMode = false, reportId, onReportUpdated }) => {
  const navigate = useNavigate();
  const params = useParams();
  const id = reportId || params.id;
  const { user } = useAuth();
  const [report, setReport] = useState<BugReport | null>(null);
  const [history, setHistory] = useState<BugReportHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState(initialEditMode);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [voteLoading, setVoteLoading] = useState(false);
  const [voteStats, setVoteStats] = useState({ pour: 0, contre: 0 });
  const [userVote, setUserVote] = useState<'pour' | 'contre' | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [comments, setComments] = useState<BugReportComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [addingComment, setAddingComment] = useState(false);
  const [hasMoreComments, setHasMoreComments] = useState(true);
  const [commentsPage, setCommentsPage] = useState(1);
  const [totalComments, setTotalComments] = useState(0);
  const observer = useRef<IntersectionObserver | null>(null);
  const lastCommentRef = useCallback((node: HTMLLIElement) => {
    if (commentsLoading) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMoreComments) {
        setCommentsPage(prev => prev + 1);
      }
    });
    if (node) observer.current.observe(node);
  }, [commentsLoading, hasMoreComments]);

  const { control, handleSubmit, reset, formState: { errors } } = useForm<BugReportUpdateRequest>();

  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingCommentText, setEditingCommentText] = useState('');
  const [deleteCommentDialogOpen, setDeleteCommentDialogOpen] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [replyingToComment, setReplyingToComment] = useState<BugReportComment | null>(null);

  // Nouvel état pour stocker les commentaires regroupés
  const [groupedComments, setGroupedComments] = useState<{
    [key: string]: {
      parent: BugReportComment;
      replies: BugReportComment[];
    }
  }>({});
  
  // États pour la sélection multiple des commentaires (admin uniquement)
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedComments, setSelectedComments] = useState<string[]>([]);
  const [deleteMultipleLoading, setDeleteMultipleLoading] = useState(false);
  
  // Nouvelle fonction pour regrouper les commentaires par parent
  const groupCommentsByParent = useCallback((commentsArray: BugReportComment[]) => {
    const grouped: {
      [key: string]: {
        parent: BugReportComment;
        replies: BugReportComment[];
      }
    } = {};
    
    const standAloneComments: BugReportComment[] = [];
    const childComments: BugReportComment[] = [];
    
    // Séparer les commentaires parents et enfants
    commentsArray.forEach(comment => {
      if (comment.parent_comment_id) {
        childComments.push(comment);
      } else {
        standAloneComments.push(comment);
      }
    });
    
    // Créer une entrée pour chaque commentaire parent
    standAloneComments.forEach(parent => {
      grouped[parent.id] = {
        parent,
        replies: []
      };
    });
    
    // Ajouter les réponses à leurs parents respectifs
    childComments.forEach(reply => {
      if (reply.parent_comment_id && grouped[reply.parent_comment_id]) {
        grouped[reply.parent_comment_id].replies.push(reply);
      } else {
        // Si le parent n'existe pas encore dans le groupe (peut arriver avec la pagination)
        // Créer un parent fictif qui sera remplacé quand le vrai parent sera chargé
        if (reply.parent && reply.parent_comment_id) {
          if (!grouped[reply.parent_comment_id]) {
            grouped[reply.parent_comment_id] = {
              parent: reply.parent as BugReportComment,
              replies: [reply]
            };
          } else {
            grouped[reply.parent_comment_id].replies.push(reply);
          }
        }
      }
    });
    
    return grouped;
  }, []);
  
  // Fonction pour trier les commentaires groupés par date de dernière activité
  const getSortedGroupedComments = useCallback(() => {
    const groupEntries = Object.entries(groupedComments);
    
    // Trier les groupes par la date du commentaire le plus récent (parent ou réponse)
    return groupEntries.sort((a, b) => {
      const aGroup = a[1];
      const bGroup = b[1];
      
      // Trouver la date la plus récente dans chaque groupe
      const aLatestReply = aGroup.replies.length > 0 
        ? new Date(Math.max(...aGroup.replies.map(r => new Date(r.created_at).getTime())))
        : null;
      
      const bLatestReply = bGroup.replies.length > 0
        ? new Date(Math.max(...bGroup.replies.map(r => new Date(r.created_at).getTime())))
        : null;
      
      const aLatest = aLatestReply && aLatestReply > new Date(aGroup.parent.created_at)
        ? aLatestReply
        : new Date(aGroup.parent.created_at);
        
      const bLatest = bLatestReply && bLatestReply > new Date(bGroup.parent.created_at)
        ? bLatestReply
        : new Date(bGroup.parent.created_at);
      
      // Trier par date décroissante (plus récent en premier)
      return bLatest.getTime() - aLatest.getTime();
    });
  }, [groupedComments]);

  const fetchReport = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await bugReportService.getById(id);
      
      // Vérifier si l'utilisateur a accès au rapport
      if (data.status === 'attente_moderation' && !isAdmin && (!user || data.user_id !== user.id)) {
        setError('Ce rapport est en attente de modération et n\'est pas encore public');
        setReport(null);
        return;
      }
      
      // S'assurer qu'aucune valeur n'est null pour les champs de formulaire
      const cleanedData = {
        ...data,
        title: data.title || '',
        description: data.description || '',
        reproduction_steps: data.reproduction_steps || '',
        admin_comment: data.admin_comment || '',
        os_info: data.os_info || '',
        browser_info: data.browser_info ? {
          name: data.browser_info.name || '',
          version: data.browser_info.version || '',
          mobile: !!data.browser_info.mobile,
          os: data.browser_info.os || ''
        } : undefined
      };
      
      setReport(cleanedData);
      
      // Déterminer si l'utilisateur a déjà voté
      if (cleanedData.votes && cleanedData.votes.length > 0 && user) {
        const userVoteObj = cleanedData.votes.find(vote => vote.user_id === user.id);
        if (userVoteObj) {
          setUserVote(userVoteObj.vote_type);
        }
      }
      
      // Calculer les statistiques de vote
      const pourCount = cleanedData.votes?.filter(vote => vote.vote_type === 'pour').length || 0;
      const contreCount = cleanedData.votes?.filter(vote => vote.vote_type === 'contre').length || 0;
      setVoteStats({ pour: pourCount, contre: contreCount });
      
      // Si admin, récupérer également l'historique
      if (isAdmin) {
        try {
          const historyData = await bugReportService.getHistory(id);
          // Le service garantit maintenant que historyData est toujours un tableau
          setHistory(historyData);
        } catch (histErr) {
          logger.error('Erreur lors de la récupération de l\'historique:', histErr);
          setHistory([]);
        }
      }
      
      reset(cleanedData);
    } catch (err: any) {
      logger.error('Erreur lors de la récupération du rapport:', err);
      setError(err.response?.data?.error || 'Erreur lors de la récupération du rapport');
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = async () => {
    try {
      if (commentsPage === 1) {
        setCommentsLoading(true);
      }
      const response = await bugReportService.getComments(id!, commentsPage, 10);
      
      let newComments;
      if (commentsPage === 1) {
        newComments = response.data;
      } else {
        newComments = [...comments, ...response.data];
      }
      
      // Stocker les commentaires bruts
      setComments(newComments);
      
      // Mettre à jour les commentaires groupés
      setGroupedComments(groupCommentsByParent(newComments));
      
      setTotalComments(response.pagination.total);
      // Vérifier s'il y a plus de commentaires à charger
      setHasMoreComments(response.data.length === 10);
    } catch (err: any) {
      logger.error('Erreur lors de la récupération des commentaires:', err);
    } finally {
      setCommentsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchReport();
      fetchComments();
    }
  }, [id]);

  useEffect(() => {
    if (id && commentsPage > 0) {
      fetchComments();
    }
  }, [commentsPage]);

  const handleAddComment = async () => {
    if (!newComment.trim() || addingComment) return;
    
    // Valider la longueur du commentaire
    if (newComment.length > 900) {
      setError('Le commentaire ne doit pas dépasser 900 caractères');
      return;
    }

    try {
      setAddingComment(true);
      setError(null); // Réinitialiser les erreurs précédentes
      
      // Préparer la requête avec le parent_comment_id si on répond à un commentaire
      const commentData: BugReportCommentRequest = { 
        message: newComment,
        ...(replyingToComment && { parent_comment_id: replyingToComment.id })
      };
      
      const response = await bugReportService.addComment(id!, commentData);
      
      // Mettre à jour la liste de commentaires bruts
      const updatedComments = [response, ...comments];
      setComments(updatedComments);
      
      // Mettre à jour les commentaires groupés
      setGroupedComments(groupCommentsByParent(updatedComments));
      
      // Mettre à jour le compteur total de commentaires
      setTotalComments(prevTotal => prevTotal + 1);
      
      setNewComment('');
      setReplyingToComment(null); // Réinitialiser l'état de réponse
      setSuccessMessage('Votre commentaire a été ajouté');
    } catch (err: any) {
      logger.error('Erreur lors de l\'ajout du commentaire:', err);
      setError(err.response?.data || 'Erreur lors de l\'ajout du commentaire');      
    } finally {
      setAddingComment(false);
    }
  };

  const handleEdit = () => {
    setEditMode(true);
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    reset(report || undefined);
  };

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await bugReportService.delete(id);
      setDeleteDialogOpen(false);
      
      // Si le rapport est en détail dans une modal, informer le parent
      if (onReportUpdated && report) {
        // Appeler la fonction sans argument pour indiquer une suppression
        onReportUpdated(report);
      }
      
      navigate(isAdmin ? '/admin/bug-reports' : '/dashboard/bug-reports');
    } catch (err: any) {
      logger.error('Erreur lors de la suppression du rapport:', err);
      setError(err.response?.data?.error || 'Erreur lors de la suppression du rapport');
      setDeleteDialogOpen(false);
    }
  };

  const handleApprove = async () => {
    if (!id) return;
    
    try {
      setUpdateLoading(true);
      setError(null);
      
      const updatedReportData = await bugReportService.update(id, { status: 'nouveau' });
      setReport(updatedReportData);
      if (onReportUpdated) {
        onReportUpdated(updatedReportData);
      }
      setSuccessMessage('Le rapport a été validé avec succès');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      logger.error('Erreur lors de la validation du rapport:', err);
      setError(err.response?.data?.error || 'Erreur lors de la validation du rapport');
    } finally {
      setUpdateLoading(false);
    }
  };

  const onSubmitUpdate = async (data: BugReportUpdateRequest) => {
    if (!id) return;
    
    try {
      setUpdateLoading(true);
      setError(null);
      
      // Filtrer les propriétés nulles avant d'envoyer la requête
      const cleanData: BugReportUpdateRequest = {};
      
      // Ne conserver que les propriétés qui ont des valeurs non nulles
      Object.entries(data).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          cleanData[key as keyof BugReportUpdateRequest] = value;
        }
      });
      
      const updatedReportData = await bugReportService.update(id, cleanData);
      setReport(updatedReportData);
      if (onReportUpdated) {
        onReportUpdated(updatedReportData);
      }
      setEditMode(false);
      setSuccessMessage('Le rapport a été mis à jour avec succès');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      logger.error('Erreur lors de la mise à jour du rapport 2:', err);
      setError(err.response?.data?.error || 'Erreur lors de la mise à jour du rapport');
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleVote = async (voteType: 'pour' | 'contre') => {
    if (!id || !user) return;
    
    try {
      setVoteLoading(true);
      setError(null);
      
      // Si l'utilisateur a déjà voté et clique sur le même bouton, on retire son vote
      if (userVote === voteType) {
        const result = await bugReportService.removeVote(id);
        setUserVote(null);
        setVoteStats({
          pour: result.vote_count.pour_count,
          contre: result.vote_count.contre_count
        });
        setSuccessMessage('Votre vote a été retiré');
      } else {
        // Sinon on ajoute/modifie son vote
        const voteData: BugReportVoteRequest = { vote_type: voteType };
        const result = await bugReportService.vote(id, voteData);
        setUserVote(voteType);
        setVoteStats({
          pour: result.vote_count.pour_count,
          contre: result.vote_count.contre_count
        });
        setSuccessMessage('Votre vote a été enregistré');
      }
      
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      logger.error('Erreur lors du vote:', err);
      setError(err.response?.data?.error || 'Erreur lors du vote');
    } finally {
      setVoteLoading(false);
    }
  };

  const handleEditComment = (comment: BugReportComment) => {
    setEditingCommentId(comment.id);
    setEditingCommentText(comment.message);
  };

  const handleCancelEditComment = () => {
    setEditingCommentId(null);
    setEditingCommentText('');
  };

  const handleUpdateComment = async (commentId: string) => {
    // Valider la longueur du commentaire
    if (editingCommentText.length > 900) {
      setError('Le commentaire ne doit pas dépasser 900 caractères');
      return;
    }
    
    try {
      setError(null); // Réinitialiser les erreurs précédentes
      const updatedComment = await bugReportService.updateComment(id!, commentId, { message: editingCommentText });
      
      // Mettre à jour la liste des commentaires bruts
      const updatedComments = comments.map(comment => 
        comment.id === commentId ? updatedComment : comment
      );
      setComments(updatedComments);
      
      // Mettre à jour les commentaires groupés
      setGroupedComments(groupCommentsByParent(updatedComments));
      
      setEditingCommentId(null);
      setEditingCommentText('');
      setSuccessMessage('Commentaire mis à jour avec succès');
    } catch (err: any) {
      logger.error('Erreur lors de la mise à jour du commentaire:', err);
      
      // Gestion spécifique pour l'erreur 403 Forbidden
      if (err.response?.status === 403) {
        setError("Vous n'avez pas les droits nécessaires pour modifier ce commentaire. Seul l'auteur du commentaire ou un administrateur avec le rôle 'jobpadm' peut le modifier.");
      } else {
        setError(err.response?.data?.error || 'Erreur lors de la mise à jour du commentaire');
      }
      
      // Annuler l'édition en cas d'erreur
      setEditingCommentId(null);
      setEditingCommentText('');
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await bugReportService.deleteComment(id!, commentId);
      
      // Mettre à jour la liste des commentaires bruts
      const updatedComments = comments.filter(comment => comment.id !== commentId);
      setComments(updatedComments);
      
      // Mettre à jour les commentaires groupés
      setGroupedComments(groupCommentsByParent(updatedComments));
      
      // Mettre à jour le compteur total de commentaires
      setTotalComments(prevTotal => Math.max(0, prevTotal - 1));
      
      setDeleteCommentDialogOpen(false);
      setCommentToDelete(null);
      setSuccessMessage('Commentaire supprimé avec succès');
    } catch (err: any) {
      logger.error('Erreur lors de la suppression du commentaire:', err);
      setError(err.response?.data?.error || 'Erreur lors de la suppression du commentaire');
    }
  };

  const canEditComment = (comment: BugReportComment) => {
    if (!user) return false;
    
    // Les administrateurs peuvent modifier tous les commentaires
    if (isAdmin) return true;
    
    // Si le commentaire provient d'un administrateur et que l'utilisateur n'est pas admin
    if (comment.is_admin && !isAdmin) return false;
    
    const commentDate = new Date(comment.created_at);
    const now = new Date();
    const diffInMinutes = (now.getTime() - commentDate.getTime()) / 1000 / 60;
    
    // Vérifier si l'utilisateur est l'auteur et que le délai de 5 minutes n'est pas dépassé
    return comment.user_id === user.id && diffInMinutes <= 5;
  };

  const handleReplyToComment = (comment: BugReportComment) => {
    // Si on répond à un sous-commentaire, on référence son parent pour maintenir une structure à deux niveaux
    let targetComment = comment;
    let replyPrefix = '';
    
    if (comment.parent_comment_id) {
      // C'est un sous-commentaire, chercher le commentaire parent dans les groupedComments
      const parentId = comment.parent_comment_id;
      if (groupedComments[parentId]) {
        targetComment = groupedComments[parentId].parent;
        // On ajoute une mention spéciale pour indiquer qu'on répond à un sous-commentaire
        replyPrefix = `@${comment.user?.email?.split('@')[0] || 'Utilisateur'} (en réponse à: "${comment.message.substring(0, 30)}${comment.message.length > 30 ? '...' : ''}) `;
      }
    } else {
      // Réponse normale à un commentaire principal
      replyPrefix = `@${comment.user?.email?.split('@')[0] || 'Utilisateur'} `;
    }
    
    setReplyingToComment(targetComment);
    // Focus sur la zone de texte du commentaire
    document.getElementById('comment-input')?.focus();
    // Préfixe pour indiquer à qui on répond
    setNewComment(replyPrefix);
  };
  
  const handleCancelReply = () => {
    setReplyingToComment(null);
    setNewComment('');
  };

  // Gestion de la suppression multiple de commentaires
  const handleToggleSelectionMode = () => {
    setSelectionMode(!selectionMode);
    // Réinitialiser la sélection quand on sort du mode
    if (selectionMode) {
      setSelectedComments([]);
    }
  };

  const handleToggleCommentSelection = (commentId: string) => {
    if (selectedComments.includes(commentId)) {
      setSelectedComments(selectedComments.filter(id => id !== commentId));
    } else {
      setSelectedComments([...selectedComments, commentId]);
    }
  };

  const handleDeleteSelectedComments = async () => {
    if (selectedComments.length === 0) return;

    try {
      setDeleteMultipleLoading(true);
      await bugReportService.deleteMultipleComments(id!, selectedComments);
      
      // Mettre à jour la liste des commentaires bruts
      const remainingComments = comments.filter(comment => !selectedComments.includes(comment.id));
      setComments(remainingComments);
      
      // Mettre à jour les commentaires groupés
      setGroupedComments(groupCommentsByParent(remainingComments));
      
      // Mettre à jour le compteur total de commentaires
      setTotalComments(prevTotal => Math.max(0, prevTotal - selectedComments.length));
      
      // Réinitialiser la sélection et le mode
      setSelectedComments([]);
      setSelectionMode(false);
      
      setSuccessMessage(`${selectedComments.length} commentaires supprimés avec succès`);
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      logger.error('Erreur lors de la suppression multiple des commentaires:', err);
      setError(err.response?.data?.error || 'Erreur lors de la suppression des commentaires');
    } finally {
      setDeleteMultipleLoading(false);
    }
  };

  // Fonction pour formater le nom de l'utilisateur (Prénom N.)
  const formatUserName = (displayUser: any) => {
    if (!displayUser) return 'Utilisateur inconnu';
    
    // Vérifier si l'utilisateur actuel est admin ou modo
    const isCurrentUserAdmin = user?.role === 'jobpadm' || user?.role === 'jobmodo';
    
    // Si c'est un administrateur ou modérateur
    if (displayUser.is_admin || displayUser.role === 'jobpadm' || displayUser.role === 'jobmodo') {
      // Si l'utilisateur actuel est admin ou modo, afficher le nom complet
      if (isCurrentUserAdmin && displayUser.user_profil && displayUser.user_profil.length > 0) {
        const prenom = displayUser.user_profil[0].prenom;
        const nom = displayUser.user_profil[0].nom;
        
        if (prenom && nom) {
          return `${prenom} ${nom}`;
        }
      }
      // Sinon afficher "Staff JobPartiel"
      return 'Support JobPartiel';
    }
    
    // Si l'utilisateur a un profil avec prénom/nom
    if (displayUser.user_profil && displayUser.user_profil.length > 0) {
      const prenom = displayUser.user_profil[0].prenom;
      const nom = displayUser.user_profil[0].nom;
      
      if (prenom || nom) {
        if (prenom && nom) {
          // Si l'utilisateur actuel est admin ou modo, afficher le nom complet
          if (isCurrentUserAdmin) {
            return `${prenom} ${nom}`;
          } else {
            return `${prenom} ${nom.charAt(0)}.`;
          }
        } else if (prenom) {
          return prenom;
        } else if (nom) {
          return nom;
        }
      }
    }
    
    // Fallback sur l'email
    // Si l'utilisateur actuel est admin ou modo, afficher l'email complet
    // Sinon, masquer partiellement l'email
    if (isCurrentUserAdmin) {
      return displayUser.email || 'Utilisateur inconnu';
    } else {
      // Masquer l'email en ne montrant que le début avant @
      if (displayUser.email && displayUser.email.includes('@')) {
        const parts = displayUser.email.split('@');
        return `${parts[0].substring(0, 3)}***@${parts[1].split('.')[0]}...`;
      }
      return 'Utilisateur inconnu';
    }
  };

  // Fonction spécifique pour obtenir la couleur à utiliser dans les TimelineDot
  const getStatusTimelineColor = (status: string) => {
    switch (status) {
      case 'nouveau':
        return 'primary' as const;
      case 'en_cours':
      case 'reouvert':
      case 'attente_moderation':
        return 'warning' as const;
      case 'resolu':
        return 'success' as const;
      case 'rejete':
      case 'ferme':
        return 'error' as const;
      default:
        return 'grey' as const;
    }
  };

  return (
    <Box sx={{ position: 'relative' }}>
      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        <Box 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            backgroundColor: 'rgba(255, 248, 243, 0.9)',
            borderRadius: '8px',
            border: '1px solid rgba(255, 107, 44, 0.2)'
          }}
        >
          <Typography 
            variant="h6" 
            sx={{ 
              color: '#FF6B2C',
              mb: 2,
              fontWeight: 500 
            }}
          >
            Détails du rapport
          </Typography>
          <Typography 
            sx={{ 
              color: 'rgba(0, 0, 0, 0.7)',
              fontWeight: 500
            }}
          >
            {error}
          </Typography>
        </Box>
      ) : report ? (
        <Box 
          sx={{ 
            mb: 4, 
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          {/* Titre et actions */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', sm: 'center' }, 
            mb: 3,
            gap: 2
          }}>
            <Box sx={{ width: '100%' }}>
              <Typography 
                variant="h5" 
                component="h1" 
                gutterBottom 
                sx={{ 
                  fontWeight: 700, 
                  color: '#333',
                  position: 'relative',
                  mb: 2,
                  fontSize: { xs: '1.25rem', md: '1.5rem' },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: 0,
                    width: '40px',
                    height: '3px',
                    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                    borderRadius: '2px'
                  }
                }}
              >
                {report.title}
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip {...getTypeChipProps(report.report_type)} size="small" />
                <Chip {...getStatusChipProps(report.status)} size="small" />
                <Chip {...getPriorityChipProps(report.priority)} size="small" />
                <Chip {...getCategoryChipProps(report.category)} size="small" />
              </Box>
            </Box>
            
            <Box sx={{ 
              display: 'flex', 
              flexDirection: { xs: 'column', sm: 'row' },
              flexWrap: { xs: 'wrap', sm: 'wrap' },
              gap: 2, 
              justifyContent: 'flex-end',
              alignItems: { xs: 'stretch', sm: 'center' },
              width: '100%',
              mt: { xs: 1, sm: 0 },
              pr: { xs: 1, sm: 3 } // Padding réduit sur mobile
            }}>
              {(isAdmin || (user && user.id === report.user_id)) && !editMode && (
                <>
                  <Button 
                    onClick={handleEdit} 
                    startIcon={<EditIcon />}
                    size="small"
                    variant="outlined"
                    sx={{
                      ...primaryButtonStyles,
                      minWidth: { xs: '100px', sm: '110px' },
                      fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                    }}
                  >
                    Modifier
                  </Button>
                  <Button
                    onClick={() => setDeleteDialogOpen(true)}
                    startIcon={<DeleteIcon />}
                    size="small"
                    variant="outlined"
                    sx={{
                      ...dangerButtonStyles,
                      minWidth: { xs: '100px', sm: '110px' },
                      fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                    }}
                  >
                    Supprimer
                  </Button>
                </>
              )}
              {isAdmin && (
                <Button
                  onClick={() => setHistoryDialogOpen(true)}
                  startIcon={<HistoryIcon />}
                  size="small"
                  variant="outlined"
                  sx={{
                    ...primaryButtonStyles,
                    minWidth: { xs: '100px', sm: '110px' },
                    fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                  }}
                >
                  Historique
                </Button>
              )}
              
              {/* Bouton de validation pour les rapports en attente de modération */}
              {isAdmin && report.status === 'attente_moderation' && (
                <Button
                  onClick={handleApprove}
                  startIcon={<CheckCircleIcon />}
                  size="small"
                  variant="outlined"
                  disabled={updateLoading}
                  sx={{
                    ...successButtonStyles,
                    minWidth: { xs: '100px', sm: '110px' },
                    fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                  }}
                >
                  Valider
                </Button>
              )}
            </Box>
          </Box>

          {/* Mode édition */}
          {editMode ? (
            <Box component="form" onSubmit={handleSubmit(onSubmitUpdate)}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="title"
                    control={control}
                    defaultValue={report.title || ''}
                    rules={{ required: 'Le titre est requis' }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Titre"
                        variant="outlined"
                        fullWidth
                        error={!!errors.title}
                        helperText={errors.title?.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <Controller
                    name="report_type"
                    control={control}
                    defaultValue={report.report_type || ''}
                    render={({ field }) => (
                      <FormControl fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      >
                        <InputLabel>Type</InputLabel>
                        <Select {...field} label="Type">
                          <MenuItem value="bug">Bug</MenuItem>
                          <MenuItem value="improvement">Amélioration</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <Controller
                    name="category"
                    control={control}
                    defaultValue={report.category || ''}
                    render={({ field }) => (
                      <FormControl fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      >
                        <InputLabel>Catégorie</InputLabel>
                        <Select {...field} label="Catégorie">
                          <MenuItem value="interface">Interface</MenuItem>
                          <MenuItem value="fonctionnalite">Fonctionnalité</MenuItem>
                          <MenuItem value="paiement">Paiement</MenuItem>
                          <MenuItem value="securite">Sécurité</MenuItem>
                          <MenuItem value="autre">Autre</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <Controller
                    name="priority"
                    control={control}
                    defaultValue={report.priority || ''}
                    render={({ field }) => (
                      <FormControl fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      >
                        <InputLabel>Priorité</InputLabel>
                        <Select {...field} label="Priorité">
                          <MenuItem value="faible">Faible</MenuItem>
                          <MenuItem value="moyenne">Moyenne</MenuItem>
                          <MenuItem value="elevee">Élevée</MenuItem>
                          <MenuItem value="critique">Critique</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                {isAdmin && (
                  <>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Controller
                        name="status"
                        control={control}
                        defaultValue={report.status || ''}
                        render={({ field }) => (
                          <FormControl fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&.Mui-focused fieldset': {
                                  borderColor: '#FF7A35',
                                },
                                '&:hover fieldset': {
                                  borderColor: '#FF965E',
                                },
                              },
                              '& .MuiInputLabel-root.Mui-focused': {
                                color: '#FF6B2C',
                              },
                            }}
                          >
                            <InputLabel>Statut</InputLabel>
                            <Select {...field} label="Statut">
                              <MenuItem value="attente_moderation">En attente de modération</MenuItem>
                              <MenuItem value="nouveau">Nouveau</MenuItem>
                              <MenuItem value="en_cours">En cours</MenuItem>
                              <MenuItem value="resolu">Résolu</MenuItem>
                              <MenuItem value="rejete">Rejeté</MenuItem>
                              <MenuItem value="reouvert">Réouvert</MenuItem>
                              <MenuItem value="ferme">Fermé</MenuItem>
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <Controller
                        name="admin_comment"
                        control={control}
                        defaultValue={report.admin_comment || ''}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Commentaire d'administration"
                            variant="outlined"
                            multiline
                            rows={3}
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&.Mui-focused fieldset': {
                                  borderColor: '#FF7A35',
                                },
                                '&:hover fieldset': {
                                  borderColor: '#FF965E',
                                },
                              },
                              '& .MuiInputLabel-root.Mui-focused': {
                                color: '#FF6B2C',
                              },
                            }}
                          />
                        )}
                      />
                    </Grid>
                  </>
                )}

                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="description"
                    control={control}
                    defaultValue={report.description || ''}
                    rules={{ required: 'La description est requise' }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Description"
                        variant="outlined"
                        multiline
                        rows={5}
                        fullWidth
                        error={!!errors.description}
                        helperText={errors.description?.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Controller
                    name="reproduction_steps"
                    control={control}
                    defaultValue={report.reproduction_steps || ''}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Étapes de reproduction"
                        variant="outlined"
                        multiline
                        rows={3}
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="os_info"
                    control={control}
                    defaultValue={report.os_info || ''}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Système d'exploitation"
                        variant="outlined"
                        fullWidth
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF7A35',
                            },
                            '&:hover fieldset': {
                              borderColor: '#FF965E',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
                
                {/* Option pour rendre le rapport privé */}
                <Grid size={{ xs: 12 }}>
                  {isAdmin || user?.id !== report.user_id ? (
                    <Box 
                      sx={{
                        mt: 1,
                        mb: 2,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'rgba(255, 107, 44, 0.05)',
                        borderRadius: '8px',
                        padding: '10px 14px',
                        border: '1px solid rgba(255, 107, 44, 0.15)'
                      }}
                    >
                      <Controller
                        name="is_private"
                        control={control}
                        defaultValue={report.is_private || false}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={field.value}
                                onChange={(e) => field.onChange(e.target.checked)}
                                sx={{
                                  color: 'rgba(255, 107, 44, 0.7)',
                                  '&.Mui-checked': {
                                    color: 'rgba(255, 107, 44, 0.7)',
                                  },
                                  padding: '4px',
                                  marginRight: '4px'
                                }}
                              />
                            }
                            label={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Avatar 
                                  sx={{ 
                                    width: 24, 
                                    height: 24, 
                                    fontSize: '0.8rem', 
                                    mr: 1,
                                    backgroundColor: 'rgba(255, 107, 44, 0.15)',
                                    color: 'rgba(255, 107, 44, 0.8)'
                                  }}
                                >
                                  <LockIcon sx={{ fontSize: '0.9rem' }} />
                                </Avatar>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontSize: '0.9rem',
                                    fontWeight: 500,
                                    color: 'rgba(0, 0, 0, 0.7)'
                                  }}
                                >
                                  Rapport privé (visible uniquement par vous et l'équipe d'administration)
                                </Typography>
                              </Box>
                            }
                          />
                        )}
                      />
                    </Box>
                  ) : (
                    <Box 
                      sx={{
                        mt: 1,
                        mb: 2,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'rgba(255, 107, 44, 0.05)',
                        borderRadius: '8px',
                        padding: '10px 14px',
                        border: '1px solid rgba(255, 107, 44, 0.15)'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar 
                          sx={{ 
                            width: 24, 
                            height: 24, 
                            fontSize: '0.8rem', 
                            mr: 1,
                            backgroundColor: 'rgba(255, 107, 44, 0.15)',
                            color: 'rgba(255, 107, 44, 0.8)'
                          }}
                        >
                          <LockIcon sx={{ fontSize: '0.9rem' }} />
                        </Avatar>
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: '0.9rem',
                            fontWeight: 500,
                            color: 'rgba(0, 0, 0, 0.7)'
                          }}
                        >
                          {report.is_private 
                            ? "Ce rapport est privé (visible uniquement par vous et l'équipe d'administration)" 
                            : "Ce rapport est public (visible par tous les utilisateurs)"}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </Grid>
              </Grid>

              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/dashboard/bug-reports')}
                  disabled={updateLoading}
                  sx={{
                    ...commonButtonStyles,
                    borderColor: '#FF6B2C',
                    color: '#FF6B2C',
                    backgroundColor: 'rgba(255, 107, 44, 0.03)',
                    '&:hover': {
                      borderColor: '#FF7A35',
                      backgroundColor: 'rgba(255, 107, 44, 0.08)',
                      boxShadow: '0 3px 6px rgba(255, 107, 44, 0.15)',
                    },
                    '& .MuiSvgIcon-root': {
                      color: '#FF6B2C',
                      fontSize: '1.1rem',
                    },
                  }}
                >
                  Retour à la liste
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleCancelEdit}
                  disabled={updateLoading}
                  sx={{
                    ...commonButtonStyles,
                    borderColor: 'rgba(0, 0, 0, 0.2)',
                    color: 'text.secondary',
                    backgroundColor: 'rgba(0, 0, 0, 0.01)',
                    '&:hover': {
                      borderColor: 'rgba(0, 0, 0, 0.5)',
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      boxShadow: '0 3px 6px rgba(0, 0, 0, 0.08)',
                    },
                    '& .MuiSvgIcon-root': {
                      color: 'rgba(0, 0, 0, 0.5)',
                      fontSize: '1.1rem',
                    },
                  }}
                >
                  Annuler
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={updateLoading}
                  startIcon={updateLoading ? <CircularProgress size={20} color="inherit" /> : null}
                  sx={{
                    ...commonButtonStyles,
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    borderColor: 'transparent',
                    padding: '6px 16px',
                    '&:hover': {
                      backgroundColor: '#FF7A35',
                      boxShadow: '0 3px 8px rgba(255, 107, 44, 0.3)',
                    },
                    boxShadow: '0 2px 6px rgba(255, 107, 44, 0.25)',
                    '&:active': {
                      transform: 'translateY(1px)',
                      boxShadow: '0 1px 4px rgba(255, 107, 44, 0.3)',
                    },
                    '& .MuiSvgIcon-root': {
                      color: 'white',
                      fontSize: '1.1rem',
                    },
                  }}
                >
                  {updateLoading ? 'Enregistrement...' : 'Enregistrer les modifications'}
                </Button>
              </Box>
            </Box>
          ) : (
            /* Mode affichage */
            <>
              {/* Alerte de modération en cours */}
              {report.status === 'attente_moderation' && !isAdmin && (
                <Alert 
                  severity="info" 
                  sx={{ 
                    mb: 3,
                    backgroundColor: 'rgba(255, 248, 243, 0.9)',
                    color: 'rgba(0, 0, 0, 0.7)',
                    borderLeft: '4px solid #FF6B2C',
                    '& .MuiAlert-icon': {
                      color: '#FF6B2C'
                    },
                    boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)',
                    borderRadius: '8px',
                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                    fontWeight: 500
                  }}
                >
                  Ce rapport est en cours de modération par le Support JobPartiel. Les commentaires seront disponibles après approbation.
                </Alert>
              )}

              <Grid container spacing={3}>
                <Grid size={{ xs: 12 }}>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontWeight: 600, 
                      mb: 1.5,
                      color: '#333',
                      display: 'flex',
                      alignItems: 'center',
                      fontSize: { xs: '1rem', sm: '1.25rem' },
                      '&::before': {
                        content: '""',
                        display: 'block',
                        width: '8px',
                        height: '20px',
                        borderRadius: '4px',
                        marginRight: '10px',
                        background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)'
                      }
                    }}
                  >
                    Description
                  </Typography>
                  <Paper 
                    elevation={0} 
                    sx={{ 
                      p: { xs: 1.5, sm: 2 }, 
                      borderRadius: '8px',
                      backgroundColor: 'rgba(0, 0, 0, 0.02)',
                      border: '1px solid rgba(0, 0, 0, 0.06)',
                      overflowX: 'auto'
                    }}
                  >
                    <Typography variant="body1" paragraph sx={{ 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word'
                    }}>
                      {report.description}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid size={{ xs: 12 }}>
              <Box 
                sx={{ 
                      p: { xs: 1.5, sm: 2 }, 
                      mb: 2,
                      borderRadius: '8px',
                      background: 'rgba(255, 248, 243, 0.7)',
                      border: '1px solid rgba(255, 107, 44, 0.15)',
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    <Typography 
                      variant="subtitle1" 
                      component="div"
                      sx={{ 
                        fontWeight: 600, 
                        mb: 1,
                        display: 'flex', 
                        alignItems: 'center',
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        '&::before': {
                          content: '""',
                          display: 'block',
                          width: '12px',
                          height: '12px',
                          borderRadius: '50%',
                          marginRight: '8px',
                          background: 'linear-gradient(135deg, #FF6B2C, #FF965E)'
                        }
                      }}
                    >
                      Informations du rapport
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', flexWrap: 'wrap', gap: 1 }}>
                      <Typography component="span" sx={{ fontWeight: 600, color: '#555' }}>
                        Créé par:
                      </Typography>
                      <Typography component="span" sx={{ wordBreak: 'break-word' }}>
                        {formatUserName(report.user) || 'Utilisateur inconnu'}
                      </Typography>
                      <Typography 
                        component="span" 
                        sx={{ 
                          fontSize: '1.2rem', 
                          color: 'rgba(0, 0, 0, 0.3)', 
                          lineHeight: 1,
                          display: { xs: 'none', sm: 'inline' }
                        }}
                      >
                        •
                      </Typography>
                      <Typography component="span">
                        {formatDateTime(report.created_at)}
                      </Typography>
                      
                      {report.is_private && (
                        <>
                          <Typography 
                            component="span" 
                            sx={{ 
                              fontSize: '1.2rem', 
                              color: 'rgba(0, 0, 0, 0.3)', 
                              lineHeight: 1,
                              display: { xs: 'none', sm: 'inline' }
                            }}
                          >
                            •
                          </Typography>
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              alignItems: 'center', 
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              color: 'rgba(255, 107, 44, 0.9)',
                              borderRadius: '4px',
                              px: 1,
                              py: 0.5,
                              fontSize: '0.8rem',
                              fontWeight: 500
                            }}
                          >
                            <Tooltip 
                              title="Ce rapport n'est visible que par vous et l'équipe d'administration" 
                              arrow
                              placement="top"
                            >
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <LockIcon sx={{ fontSize: '0.8rem', mr: 0.5 }} />
                                Rapport privé
                              </Box>
                            </Tooltip>
                          </Box>
                        </>
                      )}
                    </Box>
                  </Box>
                </Grid>

                {report.reproduction_steps && (
                  <Grid size={{ xs: 12 }}>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 600, 
                        mb: 1.5,
                        color: '#333',
                        display: 'flex',
                        alignItems: 'center',
                        fontSize: { xs: '1rem', sm: '1.25rem' },
                        '&::before': {
                          content: '""',
                          display: 'block',
                          width: '8px',
                          height: '20px',
                          borderRadius: '4px',
                          marginRight: '10px',
                          background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)'
                        }
                      }}
                    >
                      Étapes de reproduction
                    </Typography>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: { xs: 1.5, sm: 2 }, 
                        borderRadius: '8px',
                        backgroundColor: 'rgba(0, 0, 0, 0.02)',
                        border: '1px solid rgba(0, 0, 0, 0.06)',
                        overflowX: 'auto'
                      }}
                    >
                      <Typography variant="body1" paragraph sx={{ 
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word'
                      }}>
                        {report.reproduction_steps}
                      </Typography>
                    </Paper>
                  </Grid>
                )}

                {report.admin_comment && (
                  <Grid size={{ xs: 12 }}>
                    <Paper 
                      elevation={2} 
                      sx={{ 
                        p: 3, 
                        bgcolor: '#FFF8F3', 
                        borderRadius: '8px',
                        borderLeft: '4px solid #FF6B2C',
                        mt: 2
                      }}
                    >
                      <Typography 
                        variant="h6" 
                        sx={{ 
                          color: '#FF6B2C',
                          fontWeight: 600,
                          mb: 2,
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <AdminPanelSettingsIcon sx={{ mr: 1 }} />
                        Commentaire d'administration
                      </Typography>
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          whiteSpace: 'pre-line',
                          color: 'rgba(0, 0, 0, 0.7)',
                          lineHeight: 1.7
                        }}
                      >
                        {report.admin_comment}
                      </Typography>
                    </Paper>
                  </Grid>
                )}

                {report.browser_info && (
                  <Grid size={{ xs: 12, md: 6 }}>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 600, 
                        mb: 1.5,
                        color: '#333',
                        display: 'flex',
                        alignItems: 'center',
                        '&::before': {
                          content: '""',
                          display: 'block',
                          width: '8px',
                          height: '20px',
                          borderRadius: '4px',
                          marginRight: '10px',
                          background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)'
                        }
                      }}
                    >
                      Informations techniques
                    </Typography>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 2, 
                        borderRadius: '8px',
                        backgroundColor: 'rgba(0, 0, 0, 0.02)',
                        border: '1px solid rgba(0, 0, 0, 0.06)'
                      }}
                    >
                      <Box component="ul" sx={{ pl: 2, m: 0 }}>
                        {report.browser_info && (
                          <Typography 
                            component="li" 
                            variant="body1" 
                            sx={{ 
                              mb: 1,
                              '& strong': {
                                color: '#555',
                                fontWeight: 600
                              }
                            }}
                          >
                            <strong>Navigateur:</strong> {report.browser_info.name} {report.browser_info.version}
                          </Typography>
                        )}
                        {report.browser_info && (
                          <Typography 
                            component="li" 
                            variant="body1" 
                            sx={{ 
                              mb: 1,
                              '& strong': {
                                color: '#555',
                                fontWeight: 600
                              }
                            }}
                          >
                            <strong>Appareil:</strong> {report.browser_info.mobile ? 'Mobile' : 'Desktop'}
                          </Typography>
                        )}
                        {report.os_info && (
                          <Typography 
                            component="li" 
                            variant="body1" 
                            sx={{ 
                              '& strong': {
                                color: '#555',
                                fontWeight: 600
                              }
                            }}
                          >
                            <strong>Système d'exploitation:</strong> {report.os_info}
                          </Typography>
                        )}
                      </Box>
                    </Paper>
                  </Grid>
                )}
              </Grid>

              {/* Système de vote (uniquement pour les suggestions d'amélioration) */}
              {report.report_type === 'improvement' && (
                <Box sx={{ mt: 4 }}>
                  <Divider sx={{ my: 3 }} />
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      color: '#FF6B2C',
                      fontWeight: 600,
                      mb: 2
                    }}
                  >
                    Votes pour cette amélioration
                  </Typography>
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: 'stretch', 
                      gap: 2,
                      bgcolor: '#FFF8F3',
                      borderRadius: '8px',
                      p: { xs: 1.5, sm: 2 }
                    }}
                  >
                    <Button
                      variant={userVote === 'pour' ? 'contained' : 'outlined'}
                      color="success"
                      size="large"
                      startIcon={<ThumbUpIcon />}
                      onClick={() => handleVote('pour')}
                      disabled={voteLoading || !user}
                      sx={{ 
                        flex: 1,
                        py: 1.5,
                        px: 2,
                        fontWeight: 500,
                        borderRadius: '8px',
                        textTransform: 'none',
                        fontSize: '0.95rem',
                        transition: 'all 0.2s ease-in-out',
                        '&:active': {
                          transform: 'translateY(1px)',
                        },
                        ...(userVote === 'pour' 
                          ? {
                            boxShadow: '0 3px 8px rgba(46, 125, 50, 0.2)',
                            '&:hover': {
                              boxShadow: '0 4px 10px rgba(46, 125, 50, 0.25)',
                              backgroundColor: '#43a047', // Un peu plus foncé pour le hover
                            },
                          }
                          : {
                            border: '1px solid rgba(46, 125, 50, 0.5)',
                            backgroundColor: 'rgba(46, 125, 50, 0.02)',
                            '&:hover': {
                              backgroundColor: 'rgba(46, 125, 50, 0.08)',
                              borderColor: '#43a047',
                              boxShadow: '0 2px 5px rgba(46, 125, 50, 0.15)',
                            },
                          }
                        ),
                        '& .MuiSvgIcon-root': {
                          fontSize: '1.2rem',
                          marginRight: '8px',
                        },
                      }}
                    >
                      Pour ({voteStats.pour})
                    </Button>
                    <Button
                      variant={userVote === 'contre' ? 'contained' : 'outlined'}
                      color="error"
                      size="large"
                      startIcon={<ThumbDownIcon />}
                      onClick={() => handleVote('contre')}
                      disabled={voteLoading || !user}
                      sx={{ 
                        flex: 1,
                        py: 1.5,
                        px: 2,
                        fontWeight: 500,
                        borderRadius: '8px',
                        textTransform: 'none',
                        fontSize: '0.95rem',
                        transition: 'all 0.2s ease-in-out',
                        '&:active': {
                          transform: 'translateY(1px)',
                        },
                        ...(userVote === 'contre' 
                          ? {
                            boxShadow: '0 3px 8px rgba(211, 47, 47, 0.2)',
                            '&:hover': {
                              boxShadow: '0 4px 10px rgba(211, 47, 47, 0.25)',
                              backgroundColor: '#e53935', // Un peu plus foncé pour le hover
                            },
                          }
                          : {
                            border: '1px solid rgba(211, 47, 47, 0.5)',
                            backgroundColor: 'rgba(211, 47, 47, 0.02)',
                            '&:hover': {
                              backgroundColor: 'rgba(211, 47, 47, 0.08)',
                              borderColor: '#e53935',
                              boxShadow: '0 2px 5px rgba(211, 47, 47, 0.15)',
                            },
                          }
                        ),
                        '& .MuiSvgIcon-root': {
                          fontSize: '1.2rem',
                          marginRight: '8px',
                        },
                      }}
                    >
                      Contre ({voteStats.contre})
                    </Button>
                  </Box>
                  
                  {!user && (
                     <Typography 
                       variant="body2" 
                       color="text.secondary" 
                       sx={{ mt: 2, textAlign: 'center', fontStyle: 'italic' }}
                     >
                       Vous devez être connecté pour voter.
                     </Typography>
                  )}
                </Box>
              )}

              {/* Commentaires */}
              <Box sx={{ mt: 4 }}>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: 600, 
                    mb: 2,
                    fontSize: { xs: '1.1rem', sm: '1.25rem' },
                    color: '#444',
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: -8,
                      left: 0,
                      width: '30px',
                      height: '3px',
                      background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                      borderRadius: '2px'
                    }
                  }}
                >
                  Commentaires
                </Typography>

                {/* Zone d'ajout de commentaire */}
                {user ? (
                  <>
                    {/* Désactiver l'ajout de commentaires lorsque le rapport est en attente de modération pour les non-admins */}
                    {report.status === 'attente_moderation' && !isAdmin ? (
                      <Box sx={{ 
                        backgroundColor: 'rgba(255, 248, 243, 0.7)',
                        borderRadius: '8px',
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        mb: 3,
                        border: '1px dashed rgba(255, 107, 44, 0.4)'
                      }}>
                        <LockIcon sx={{ color: 'rgba(255, 107, 44, 0.8)', fontSize: '1.2rem' }} />
                        <Typography variant="body2" sx={{ color: 'rgba(0, 0, 0, 0.7)', fontWeight: 500 }}>
                          Les commentaires sont désactivés pendant la modération par le Support JobPartiel
                        </Typography>
                      </Box>
                    ) : (
                      <Box sx={{ mb: 3 }}>
                        {replyingToComment && (
                          <Box 
                            sx={{ 
                              display: 'flex', 
                              alignItems: 'flex-start', 
                              mb: 1, 
                              p: { xs: 1, sm: 1.5 },
                              borderRadius: '8px',
                              bgcolor: 'rgba(255, 107, 44, 0.1)',
                              border: '1px dashed #FF6B2C',
                              overflow: 'hidden'
                            }}
                          >
                            <Typography variant="body2" sx={{ flex: 1, fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                              Réponse à <strong>{formatUserName(replyingToComment.user) || 'Utilisateur'}</strong>
                              <Box component="span" sx={{ 
                                fontStyle: 'italic', 
                                display: 'block',
                                mt: 0.5,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}>
                                "{replyingToComment.message.substring(0, 50)}{replyingToComment.message.length > 50 ? '...' : ''}"
                              </Box>
                            </Typography>
                            <IconButton 
                              size="small" 
                              onClick={handleCancelReply}
                              sx={{ color: '#FF6B2C', ml: 1 }}
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        )}
                        
                        <TextField
                          id="comment-input"
                          fullWidth
                          multiline
                          rows={2}
                          value={newComment || ''}
                          onChange={(e) => {
                            // Capitaliser la première lettre de chaque phrase
                            const value = e.target.value;
                            if (value.length > 0) {
                              // Regex pour trouver le début de chaque phrase (après un point, point d'exclamation, etc.)
                              const capitalizedValue = value.replace(
                                /(^\s*|[.!?]\s+)([a-zàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð])/g,
                                  (match: string, separator: string, letter: string) => {
                                    return separator + letter.toUpperCase();
                                  }
                                );
                                setNewComment(capitalizedValue);
                            } else {
                              setNewComment(e.target.value);
                            }
                          }}
                          placeholder={replyingToComment ? "Écrivez votre réponse..." : "Ajouter un commentaire..."}
                          variant="outlined"
                          inputProps={{ maxLength: 900 }}
                          sx={{ 
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '8px',
                              backgroundColor: 'rgba(0, 0, 0, 0.02)',
                              fontSize: { xs: '0.9rem', sm: '1rem' },
                              '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                              },
                              '&.Mui-focused': {
                                backgroundColor: 'white',
                                '& fieldset': {
                                  borderColor: '#FF6B2C',
                                }
                              }
                            },
                            '& .MuiInputLabel-root.Mui-focused': {
                              color: '#FF6B2C',
                            }
                          }}
                        />
                        <Box sx={{ 
                          display: 'flex', 
                          flexDirection: { xs: 'column', sm: 'row' },
                          justifyContent: 'space-between', 
                          alignItems: { xs: 'flex-start', sm: 'center' },
                          mt: 1,
                          gap: { xs: 0.5, sm: 1 }
                        }}>
                          <Typography variant="caption" sx={{ 
                            color: newComment.length > 900 ? 'error.main' : 'text.secondary',
                            mb: { xs: 0.5, sm: 0 }
                          }}>
                            {newComment.length}/900 caractères
                          </Typography>
                          <Box sx={{ 
                            display: 'flex', 
                            gap: 1, 
                            width: { xs: '100%', sm: 'auto' },
                            justifyContent: { xs: 'flex-end', sm: 'flex-start' }
                          }}>
                            {replyingToComment && (
                              <Button
                                variant="text"
                                onClick={handleCancelReply}
                                sx={{ 
                                  color: '#666',
                                  flex: { xs: 1, sm: 'none' },
                                  minWidth: { xs: 'auto', sm: '80px' },
                                  '&:hover': { 
                                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                  },
                                  borderRadius: '8px',
                                  textTransform: 'none',
                                  padding: { xs: '6px 10px', sm: '6px 16px' },
                                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                                  fontWeight: 500,
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                                  transition: 'all 0.2s ease-in-out',
                                  '&:active': {
                                    transform: 'translateY(1px)',
                                  },
                                }}
                              >
                                Annuler
                              </Button>
                            )}
                            <Button
                              variant="contained"
                              onClick={handleAddComment}
                              disabled={!newComment.trim() || addingComment}
                              sx={{ 
                                backgroundColor: '#FF6B2C',
                                color: 'white',
                                flex: { xs: replyingToComment ? 2 : 1, sm: 'none' },
                                minWidth: { xs: 'auto', sm: '100px' },
                                '&:hover': { 
                                  backgroundColor: '#FF7A35',
                                  boxShadow: '0 3px 6px rgba(255, 107, 44, 0.25)',
                                },
                                boxShadow: '0 2px 4px rgba(255, 107, 44, 0.15)',
                                borderRadius: '8px',
                                textTransform: 'none',
                                padding: { xs: '6px 10px', sm: '6px 16px' },
                                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                                fontWeight: 500,
                                transition: 'all 0.2s ease-in-out',
                                '&:active': {
                                  transform: 'translateY(1px)',
                                  boxShadow: '0 1px 3px rgba(255, 107, 44, 0.15)',
                                },
                                '& .MuiSvgIcon-root': {
                                  color: 'white',
                                  marginRight: '4px',
                                  fontSize: { xs: '1rem', sm: '1.1rem' },
                                },
                              }}
                            >
                              {addingComment ? (
                                <CircularProgress size={20} sx={{ color: 'white' }} />
                              ) : (
                                <>
                                  <SendIcon />
                                  {replyingToComment ? 'Répondre' : 'Envoyer'}
                                </>
                              )}
                            </Button>
                          </Box>
                        </Box>
                      </Box>
                    )}
                  </>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Vous devez être connecté pour ajouter un commentaire.
                  </Typography>
                )}

                {/* Message de succès */}
                {successMessage && (
                  <Alert 
                    severity="success" 
                    sx={{ 
                      mb: 2,
                      '& .MuiAlert-icon': {
                        color: '#FF6B2C'
                      }
                    }}
                  >
                    {successMessage}
                  </Alert>
                )}

                {/* Liste des commentaires */}
                <Box sx={{ mt: 2 }}>
                  {/* N'afficher les commentaires que si le rapport n'est pas en attente de modération ou si l'utilisateur est admin */}
                  {report.status === 'attente_moderation' && !isAdmin ? (
                    <></>
                  ) : (
                    <>
                      {totalComments > 0 && (
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          mb: 2, 
                          justifyContent: 'space-between',
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: { xs: 1, sm: 0 }
                        }}>
                          <Typography 
                            variant="subtitle1" 
                            sx={{ 
                              color: '#666',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              fontWeight: 500,
                              width: { xs: '100%', sm: 'auto' },
                              fontSize: { xs: '0.9rem', sm: '1rem' }
                            }}
                          >
                            {totalComments} commentaire{totalComments > 1 ? 's' : ''}
                          </Typography>
      
                          {/* Boutons pour la sélection multiple (admin uniquement) */}
                          {isAdmin && (
                            <Box sx={{ 
                              display: 'flex', 
                              gap: 2,
                              width: { xs: '100%', sm: 'auto' },
                              justifyContent: { xs: 'flex-end', sm: 'flex-start' }
                            }}>
                              <Button
                                variant={selectionMode ? "contained" : "outlined"}
                                size="small"
                                onClick={handleToggleSelectionMode}
                                sx={{ 
                                  color: selectionMode ? 'white' : '#FF6B2C',
                                  borderColor: '#FF6B2C',
                                  backgroundColor: selectionMode ? '#FF6B2C' : 'transparent',
                                  fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                                  padding: { xs: '4px 8px', sm: '6px 16px' },
                                  '&:hover': {
                                    backgroundColor: selectionMode ? '#FF7A35' : 'rgba(255, 107, 44, 0.08)',
                                    borderColor: '#FF7A35'
                                  }
                                }}
                              >
                                {selectionMode ? 'Annuler' : 'Sélectionner'}
                              </Button>
                              
                              {selectionMode && (
                                <Button
                                  variant="contained"
                                  color="error"
                                  size="small"
                                  disabled={selectedComments.length === 0 || deleteMultipleLoading}
                                  onClick={handleDeleteSelectedComments}
                                  startIcon={deleteMultipleLoading ? <CircularProgress size={20} color="inherit" /> : <DeleteIcon />}
                                  sx={{
                                    fontWeight: 500,
                                    boxShadow: '0 2px 8px rgba(211, 47, 47, 0.3)'
                                  }}
                                >
                                  {deleteMultipleLoading 
                                    ? 'Suppression...' 
                                    : `Supprimer ${selectedComments.length > 0 ? `(${selectedComments.length})` : ''}`}
                                </Button>
                              )}
                            </Box>
                          )}
                        </Box>
                      )}
                      
                      {/* Afficher les commentaires groupés */}
                      {Object.keys(groupedComments).length === 0 ? (
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                          Aucun commentaire pour le moment
                        </Typography>
                      ) : (
                        getSortedGroupedComments().map(([parentId, group], groupIndex) => (
                          <Box 
                            key={parentId} 
                            sx={{ 
                              mb: 4, 
                              pb: 3,
                              borderBottom: groupIndex < getSortedGroupedComments().length - 1 ? '1px dashed rgba(0, 0, 0, 0.1)' : 'none'
                            }}
                            ref={groupIndex === getSortedGroupedComments().length - 1 ? lastCommentRef : undefined}
                          >
                            {/* Commentaire parent */}
                            <ListItem 
                              alignItems="flex-start" 
                              sx={{ 
                                px: 0,
                                mb: group.replies.length > 0 ? 2 : 0
                              }}
                            >
                              {/* Checkbox pour sélectionner le commentaire (admin uniquement) */}
                              {isAdmin && selectionMode && (
                                <Box sx={{ mr: 1, mt: 2 }}>
                                  <Checkbox
                                    checked={selectedComments.includes(group.parent.id)}
                                    onChange={() => handleToggleCommentSelection(group.parent.id)}
                                    sx={{
                                      color: '#FF6B2C',
                                      '&.Mui-checked': {
                                        color: '#FF6B2C',
                                      },
                                      padding: { xs: '4px', sm: '9px' }
                                    }}
                                  />
                                </Box>
                              )}
                              <ListItemAvatar>
                                <Avatar 
                                  sx={{ 
                                    bgcolor: group.parent.is_admin ? '#2196F3' : '#FF6B2C',
                                    width: { xs: 32, sm: 40 },
                                    height: { xs: 32, sm: 40 },
                                    fontSize: { xs: '0.9rem', sm: '1.1rem' },
                                    boxShadow: group.parent.is_admin ? 
                                      '0 2px 8px rgba(33, 150, 243, 0.3)' : 
                                      '0 2px 8px rgba(255, 107, 44, 0.2)'
                                  }}
                                >
                                  {group.parent.is_admin ? 'S' : (group.parent.user?.email?.charAt(0).toUpperCase() || 'U')}
                                </Avatar>
                              </ListItemAvatar>
                              <Box 
                                component="div" 
                                sx={{ 
                                  flex: 1, 
                                  ml: { xs: 1, sm: 2 },
                                  bgcolor: 'rgba(255, 248, 243, 0.7)',
                                  borderRadius: '12px',
                                  p: { xs: 1.5, sm: 2 },
                                  border: '1px solid rgba(255, 107, 44, 0.15)',
                                  position: 'relative',
                                  '&::before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: -8,
                                    top: 16,
                                    width: 0,
                                    height: 0,
                                    borderTop: '8px solid transparent',
                                    borderBottom: '8px solid transparent',
                                    borderRight: '8px solid rgba(255, 107, 44, 0.15)'
                                  }
                                }}
                              >
                                <Box 
                                  component="div" 
                                  sx={{ 
                                    display: 'flex', 
                                    alignItems: { xs: 'flex-start', sm: 'center' },
                                    flexDirection: { xs: 'column', sm: 'row' },
                                    gap: { xs: 0.5, sm: 1 }, 
                                    mb: 1
                                  }}
                                >
                                  <Typography 
                                    component="div" 
                                    variant="subtitle1" 
                                    sx={{ 
                                      fontWeight: 600,
                                      color: '#FF6B2C',
                                      fontSize: { xs: '0.85rem', sm: '0.9rem' },
                                      lineHeight: { xs: 1.2, sm: 1.5 }
                                    }}
                                  >
                                    {formatUserName(group.parent.user) || 'Utilisateur'}
                                  </Typography>
                                  <Typography 
                                    component="div" 
                                    variant="caption" 
                                    sx={{ 
                                      color: 'rgba(0, 0, 0, 0.5)',
                                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                                    }}
                                  >
                                    {formatDateTime(group.parent.created_at)}
                                  </Typography>
                                  
                                  {/* Boutons d'action (éditer, supprimer, répondre) */}
                                  <Box 
                                    component="div" 
                                    sx={{ 
                                      display: 'flex', 
                                      gap: 0.5, 
                                      ml: { xs: 'auto', sm: 'auto' },
                                      mt: { xs: 1, sm: 0 },
                                      position: { xs: 'absolute', sm: 'static' },
                                      top: { xs: '8px', sm: 'auto' },
                                      right: { xs: '8px', sm: 'auto' },
                                      opacity: 0.7,
                                      '&:hover': {
                                        opacity: 1
                                      }
                                    }}
                                  >
                                    {/* Bouton de réponse (pour tous les utilisateurs) */}
                                    {user && (
                                      <IconButton
                                        size="small"
                                        onClick={() => handleReplyToComment(group.parent)}
                                        sx={{ 
                                          color: '#FF6B2C',
                                          padding: { xs: '4px', sm: '8px' },
                                          '&:hover': {
                                            bgcolor: 'rgba(255, 107, 44, 0.1)'
                                          }
                                        }}
                                      >
                                        <ReplyIcon fontSize="small" />
                                      </IconButton>
                                    )}
                                    
                                    {/* Boutons d'édition et suppression (seulement pour l'auteur) */}
                                    {canEditComment(group.parent) && (
                                      <>
                                        <IconButton
                                          size="small"
                                          onClick={() => handleEditComment(group.parent)}
                                          sx={{ 
                                            color: '#FF6B2C',
                                            padding: { xs: '4px', sm: '8px' },
                                            '&:hover': {
                                              bgcolor: 'rgba(255, 107, 44, 0.1)'
                                            }
                                          }}
                                        >
                                          <EditIcon fontSize="small" />
                                        </IconButton>
                                        <IconButton
                                          size="small"
                                          onClick={() => {
                                            setCommentToDelete(group.parent.id);
                                            setDeleteCommentDialogOpen(true);
                                          }}
                                          sx={{ 
                                            color: '#FF6B2C',
                                            padding: { xs: '4px', sm: '8px' },
                                            '&:hover': {
                                              bgcolor: 'rgba(255, 107, 44, 0.1)'
                                            }
                                          }}
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </>
                                    )}
                                  </Box>
                                </Box>
                                
                                <Box component="div" sx={{ mt: 1 }}>
                                  {editingCommentId === group.parent.id ? (
                                    <Box component="div" sx={{ mt: 1 }}>
                                      <TextField
                                        id={`edit-comment-${group.parent.id}`}
                                        fullWidth
                                        multiline
                                        rows={2}
                                        value={editingCommentText}
                                        onChange={(e) => {
                                          // Capitaliser la première lettre de chaque phrase
                                          const value = e.target.value;
                                          if (value.length > 0) {
                                            // Regex pour trouver le début de chaque phrase (après un point, point d'exclamation, etc.)
                                            const capitalizedValue = value.replace(
                                              /(^\s*|[.!?]\s+)([a-zàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð])/g,
                                                (match: string, separator: string, letter: string) => {
                                                  return separator + letter.toUpperCase();
                                                }
                                              );
                                              setEditingCommentText(capitalizedValue);
                                          } else {
                                            setEditingCommentText(value);
                                          }
                                        }}
                                        variant="outlined"
                                        size="small"
                                        autoFocus
                                        inputProps={{ maxLength: 900 }}
                                        sx={{ 
                                          mb: 1,
                                          '& .MuiOutlinedInput-root': {
                                            borderRadius: '6px',
                                            '&.Mui-focused': {
                                              '& fieldset': {
                                                borderColor: '#FF6B2C',
                                              }
                                            }
                                          },
                                          '& .MuiInputLabel-root.Mui-focused': {
                                            color: '#FF6B2C',
                                          }
                                        }}
                                      />
                                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                        <Typography variant="caption" sx={{ color: editingCommentText.length > 900 ? 'error.main' : 'text.secondary' }}>
                                          {editingCommentText.length}/900 caractères
                                        </Typography>
                                        <Box>
                                          <Button
                                            size="small"
                                            variant="text"
                                            onClick={handleCancelEditComment}
                                            sx={{ 
                                              mr: 1,
                                              color: 'rgba(0, 0, 0, 0.7)',
                                              '&:hover': {
                                                backgroundColor: 'rgba(0, 0, 0, 0.04)'
                                              }
                                            }}
                                          >
                                            Annuler
                                          </Button>
                                          <Button
                                            size="small"
                                            variant="contained"
                                            onClick={() => handleUpdateComment(group.parent.id)}
                                            sx={{ 
                                              bgcolor: '#FF6B2C',
                                              '&:hover': { 
                                                bgcolor: '#FF7A35' 
                                              },
                                              boxShadow: '0 2px 8px rgba(255, 107, 44, 0.3)'
                                            }}
                                          >
                                            Mettre à jour
                                          </Button>
                                        </Box>
                                      </Box>
                                    </Box>
                                  ) : (
                                    <Typography 
                                      variant="body2" 
                                      sx={{ 
                                        fontSize: { xs: '0.875rem', sm: '0.9rem' },
                                        lineHeight: 1.6,
                                        color: 'rgba(0, 0, 0, 0.8)',
                                        wordBreak: 'break-word'
                                      }}
                                    >
                                      {group.parent.message}
                                    </Typography>
                                  )}
                                </Box>
                              </Box>
                            </ListItem>

                            {/* Réponses au commentaire */}
                            {group.replies.length > 0 && (
                              <Box sx={{ ml: { xs: 4, sm: 7 } }}>
                                {/* Étiquette nombre de réponses */}
                                <Typography 
                                  variant="caption" 
                                  sx={{ 
                                    display: 'block', 
                                    mb: 1, 
                                    ml: 1,
                                    color: 'rgba(0, 0, 0, 0.5)',
                                    fontStyle: 'italic'
                                  }}
                                >
                                  {group.replies.length} réponse{group.replies.length > 1 ? 's' : ''}
                                </Typography>
                                
                                {/* Liste des réponses */}
                                {group.replies
                                  .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                                  .map((reply) => (
                                    <ListItem 
                                      key={reply.id} 
                                      alignItems="flex-start" 
                                      sx={{ 
                                        px: 0,
                                        mb: 1.5,
                                        '&:last-child': { mb: 0 }
                                      }}
                                    >
                                      {/* Checkbox pour sélectionner la réponse (admin uniquement) */}
                                      {isAdmin && selectionMode && (
                                        <Box sx={{ mr: 1, mt: 2 }}>
                                          <Checkbox
                                            checked={selectedComments.includes(reply.id)}
                                            onChange={() => handleToggleCommentSelection(reply.id)}
                                            size="small"
                                            sx={{
                                              color: '#FF6B2C',
                                              '&.Mui-checked': {
                                                color: '#FF6B2C',
                                              },
                                            }}
                                          />
                                        </Box>
                                      )}
                                      <ListItemAvatar>
                                        <Avatar 
                                          sx={{ 
                                            bgcolor: reply.is_admin ? '#2196F3' : '#FF6B2C',
                                            width: 32,
                                            height: 32,
                                            fontSize: '0.8rem',
                                            boxShadow: reply.is_admin ? 
                                              '0 2px 4px rgba(33, 150, 243, 0.3)' : 
                                              '0 2px 4px rgba(255, 107, 44, 0.15)'
                                          }}
                                        >
                                          {reply.is_admin ? 'S' : (reply.user?.email?.charAt(0).toUpperCase() || 'U')}
                                        </Avatar>
                                      </ListItemAvatar>
                                      <Box 
                                        component="div" 
                                        sx={{ 
                                          flex: 1, 
                                          ml: 1,
                                          bgcolor: 'rgba(255, 248, 243, 0.9)',
                                          borderRadius: '12px',
                                          p: 2,
                                          border: '1px solid rgba(255, 107, 44, 0.25)',
                                          position: 'relative',
                                          '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: -8,
                                            top: 16,
                                            width: 0,
                                            height: 0,
                                            borderTop: '8px solid transparent',
                                            borderBottom: '8px solid transparent',
                                            borderRight: '8px solid rgba(255, 107, 44, 0.25)'
                                          }
                                        }}
                                      >
                                        <Box 
                                          component="div" 
                                          sx={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            gap: 1, 
                                            mb: 1
                                          }}
                                        >
                                          <Typography 
                                            component="div" 
                                            variant="subtitle2" 
                                            sx={{ 
                                              fontWeight: 600,
                                              color: '#FF6B2C'
                                            }}
                                          >
                                            {formatUserName(reply.user) || 'Utilisateur'}
                                          </Typography>
                                          <Typography 
                                            component="div" 
                                            variant="caption" 
                                            sx={{ 
                                              color: 'rgba(0, 0, 0, 0.5)',
                                              fontSize: '0.75rem'
                                            }}
                                          >
                                            {formatDateTime(reply.created_at)}
                                          </Typography>
                                          
                                          {/* Boutons d'action pour la réponse */}
                                          <Box 
                                            component="div" 
                                            sx={{ 
                                              display: 'flex', 
                                              gap: 1, 
                                              ml: 'auto',
                                              opacity: 0.7,
                                              '&:hover': {
                                                opacity: 1
                                              }
                                            }}
                                          >
                                            {/* Bouton de réponse (pour tous les utilisateurs) */}
                                            {user && (
                                              <IconButton
                                                size="small"
                                                onClick={() => handleReplyToComment(reply)}
                                                sx={{ 
                                                  color: '#FF6B2C',
                                                  '&:hover': {
                                                    bgcolor: 'rgba(255, 107, 44, 0.1)'
                                                  }
                                                }}
                                              >
                                                <ReplyIcon fontSize="small" />
                                              </IconButton>
                                            )}

                                            {canEditComment(reply) && (
                                              <>
                                                <IconButton
                                                  size="small"
                                                  onClick={() => handleEditComment(reply)}
                                                  sx={{ 
                                                    color: '#FF6B2C',
                                                    '&:hover': {
                                                      bgcolor: 'rgba(255, 107, 44, 0.1)'
                                                    }
                                                  }}
                                                >
                                                  <EditIcon fontSize="small" />
                                                </IconButton>
                                                <IconButton
                                                  size="small"
                                                  onClick={() => {
                                                    setCommentToDelete(reply.id);
                                                    setDeleteCommentDialogOpen(true);
                                                  }}
                                                  sx={{ 
                                                    color: '#FF6B2C',
                                                    '&:hover': {
                                                      bgcolor: 'rgba(255, 107, 44, 0.1)'
                                                    }
                                                  }}
                                                >
                                                  <DeleteIcon fontSize="small" />
                                                </IconButton>
                                              </>
                                            )}
                                          </Box>
                                        </Box>
        
                                        <Box component="div" sx={{ mt: 1 }}>
                                          {editingCommentId === reply.id ? (
                                            <Box component="div" sx={{ mt: 1 }}>
                                              <TextField
                                                id={`edit-comment-${reply.id}`}
                                                fullWidth
                                                multiline
                                                rows={2}
                                                value={editingCommentText}
                                                onChange={(e) => {
                                                  // Capitaliser la première lettre de chaque phrase
                                                  const value = e.target.value;
                                                  if (value.length > 0) {
                                                    // Regex pour trouver le début de chaque phrase (après un point, point d'exclamation, etc.)
                                                    const capitalizedValue = value.replace(
                                                      /(^\s*|[.!?]\s+)([a-zàáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð])/g,
                                                      (match, separator, letter) => {
                                                        return separator + letter.toUpperCase();
                                                      }
                                                    );
                                                    setEditingCommentText(capitalizedValue);
                                                  } else {
                                                    setEditingCommentText(value);
                                                  }
                                                }}
                                                variant="outlined"
                                                size="small"
                                                autoFocus
                                                inputProps={{ maxLength: 900 }}
                                                sx={{ 
                                                  mb: 1,
                                                  '& .MuiOutlinedInput-root': {
                                                    borderRadius: '6px',
                                                    '&.Mui-focused': {
                                                      '& fieldset': {
                                                        borderColor: '#FF6B2C',
                                                      }
                                                    }
                                                  },
                                                  '& .MuiInputLabel-root.Mui-focused': {
                                                    color: '#FF6B2C',
                                                  }
                                                }}
                                              />
                                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                <Typography variant="caption" sx={{ color: editingCommentText.length > 900 ? 'error.main' : 'text.secondary' }}>
                                                  {editingCommentText.length}/900 caractères
                                                </Typography>
                                                <Box>
                                                  <Button
                                                    size="small"
                                                    variant="text"
                                                    onClick={handleCancelEditComment}
                                                    sx={{ 
                                                      mr: 1,
                                                      color: 'rgba(0, 0, 0, 0.7)',
                                                      '&:hover': {
                                                        backgroundColor: 'rgba(0, 0, 0, 0.04)'
                                                      }
                                                    }}
                                                  >
                                                    Annuler
                                                  </Button>
                                                  <Button
                                                    size="small"
                                                    variant="contained"
                                                    onClick={() => handleUpdateComment(reply.id)}
                                                    sx={{ 
                                                      bgcolor: '#FF6B2C',
                                                      '&:hover': { 
                                                        bgcolor: '#FF7A35' 
                                                      },
                                                      boxShadow: '0 2px 8px rgba(255, 107, 44, 0.3)'
                                                    }}
                                                  >
                                                    Mettre à jour
                                                  </Button>
                                                </Box>
                                              </Box>
                                            </Box>
                                          ) : (
                                            <Typography 
                                              component="div" 
                                              variant="body2" 
                                              sx={{ 
                                                whiteSpace: 'pre-wrap',
                                                color: 'rgba(0, 0, 0, 0.7)',
                                                lineHeight: 1.6
                                              }}
                                            >
                                              {reply.message}
                                            </Typography>
                                          )}
                                        </Box>
                                      </Box>
                                    </ListItem>
                                  ))
                                }
                              </Box>
                            )}
                          </Box>
                        ))
                      )}
  
                      {/* Loader pour l'infinite scroll */}
                      {commentsLoading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, mb: 2 }}>
                          <CircularProgress size={30} sx={{ color: '#FF6B2C' }} />
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            </>
          )}

          {/* Dialogue de confirmation de suppression */}
          <ModalPortal
            isOpen={deleteDialogOpen}
            onBackdropClick={() => setDeleteDialogOpen(false)}
            withBlur={true}
          >
            <Paper
              sx={{
                position: 'relative',
                width: '100%',
                maxWidth: '450px',
                borderRadius: '12px',
                overflow: 'hidden',
                bgcolor: 'background.paper',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                animation: 'modalAppear 0.3s ease-out',
                margin: { xs: '0 16px', sm: 0 }
              }}
            >
              <Box
                sx={{
                  p: { xs: 1.5, sm: 2 },
                  bgcolor: '#FFF8F3',
                  borderBottom: '1px solid rgba(255, 107, 44, 0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5
                }}
              >
                <DeleteIcon color="error" />
                <Typography
                  variant="h6"
                  sx={{
                    color: '#FF6B2C',
                    fontWeight: 600,
                    fontSize: { xs: '1rem', sm: '1.2rem' }
                  }}
                >
                  Confirmer la suppression
                </Typography>
              </Box>
              
              <Box sx={{ p: { xs: 2, sm: 3 } }}>
                <Typography sx={{ color: 'rgba(0, 0, 0, 0.7)', fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  Êtes-vous sûr de vouloir supprimer ce rapport ? Cette action est irréversible.
                </Typography>
                <Typography sx={{ mt: 2, color: 'error.main', fontWeight: 500, fontSize: { xs: '0.8rem', sm: '0.9rem' } }}>
                  Toutes les données associées seront également supprimées définitivement.
                </Typography>
              </Box>

              <Box
                sx={{
                  px: { xs: 2, sm: 3 },
                  py: 2,
                  bgcolor: 'rgba(0, 0, 0, 0.02)',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: 1
                }}
              >
                <Button
                  onClick={() => setDeleteDialogOpen(false)}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      bgcolor: 'rgba(0, 0, 0, 0.05)'
                    }
                  }}
                >
                  Annuler
                </Button>
                <Button
                  onClick={handleDelete}
                  variant="contained"
                  color="error"
                  sx={{
                    fontWeight: 500,
                    boxShadow: '0 2px 8px rgba(211, 47, 47, 0.3)'
                  }}
                >
                  Supprimer définitivement
                </Button>
              </Box>
            </Paper>
          </ModalPortal>

          {/* Dialogue d'historique (uniquement pour les administrateurs) */}
          {isAdmin && (
            <ModalPortal
              isOpen={historyDialogOpen}
              onBackdropClick={() => setHistoryDialogOpen(false)}
              withBlur={true}
            >
              <Paper
                sx={{
                  position: 'relative',
                  width: '100%',
                  maxWidth: '800px',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  bgcolor: 'background.paper',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                  animation: 'modalAppear 0.3s ease-out',
                  margin: { xs: '0 16px', sm: 0 }
                }}
              >
                <Box
                  sx={{ 
                    p: { xs: 1.5, sm: 2 },
                    bgcolor: '#FFF8F3', 
                    borderBottom: '1px solid rgba(255, 107, 44, 0.2)',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1.5}>
                    <HistoryIcon sx={{ color: '#FF6B2C' }} />
                    <Typography
                      variant="h6"
                      sx={{
                        color: '#FF6B2C',
                        fontWeight: 600,
                        fontSize: { xs: '1rem', sm: '1.2rem' }
                      }}
                    >
                      Historique des modifications
                    </Typography>
                  </Box>
                  <IconButton
                    aria-label="close"
                    onClick={() => setHistoryDialogOpen(false)}
                    sx={{ 
                      color: 'rgba(0, 0, 0, 0.5)',
                      '&:hover': {
                        bgcolor: 'rgba(255, 107, 44, 0.1)',
                        color: '#FF6B2C'
                      }
                    }}
                  >
                    <CloseIcon />
                  </IconButton>
                </Box>
                <Box sx={{ p: { xs: 2, sm: 3 }, maxHeight: '70vh', overflowY: 'auto' }}>
                  {history.length === 0 ? (
                    <Alert 
                      severity="info" 
                      icon={<HistoryIcon />}
                      sx={{ 
                        borderRadius: '8px',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
                      }}
                    >
                      Aucun historique disponible pour ce rapport
                    </Alert>
                  ) : (
                    <Timeline position="alternate">
                      {history.map((item) => (
                        <TimelineItem key={item.id}>
                          <TimelineOppositeContent color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                            {formatDateTime(item.created_at)}
                          </TimelineOppositeContent>
                          <TimelineSeparator>
                            <TimelineDot color={getStatusTimelineColor(item.new_status)} sx={{ boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)' }} />
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent>
                            <Paper 
                              elevation={2} 
                              sx={{ 
                                p: 2.5, 
                                backgroundColor: 'white',
                                borderRadius: '8px',
                                boxShadow: '0 3px 10px rgba(0, 0, 0, 0.08)',
                                border: '1px solid rgba(0, 0, 0, 0.05)',
                                transition: 'transform 0.2s ease',
                                '&:hover': {
                                  transform: 'translateY(-2px)',
                                  boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)'
                                }
                              }}
                            >
                              <Typography variant="h6" component="h3" sx={{ color: '#FF6B2C', fontWeight: 600, fontSize: '1rem' }}>
                                {getStatusChipProps(item.new_status).label}
                              </Typography>
                              <Box sx={{ 
                                display: 'flex',
                                alignItems: 'center',
                                mt: 1,
                                p: 1,
                                bgcolor: 'rgba(0, 0, 0, 0.03)',
                                borderRadius: '4px'
                              }}>
                                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                  Statut précédent : <strong>{item.old_status ? getStatusChipProps(item.old_status).label : 'Aucun'}</strong>
                                </Typography>
                              </Box>
                              {item.updater && (
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1.5, fontSize: '0.85rem' }}>
                                  Modifié par : <strong>{item.updater.email}</strong>
                                </Typography>
                              )}
                              {item.comment && (
                                <Box sx={{ mt: 2, pt: 1.5, borderTop: '1px dashed rgba(0, 0, 0, 0.1)' }}>
                                  <Typography variant="body2" sx={{ color: 'rgba(0, 0, 0, 0.7)', fontStyle: 'italic' }}>
                                    "{item.comment}"
                                  </Typography>
                                </Box>
                              )}
                            </Paper>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                </Box>
              </Paper>
            </ModalPortal>
          )}

          {/* Dialogue de confirmation de suppression de commentaire */}
          <ModalPortal
            isOpen={deleteCommentDialogOpen}
            onBackdropClick={() => setDeleteCommentDialogOpen(false)}
            withBlur={true}
          >
            <Paper
              sx={{
                position: 'relative',
                width: '100%',
                maxWidth: '450px',
                borderRadius: '12px',
                overflow: 'hidden',
                bgcolor: 'background.paper',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                animation: 'modalAppear 0.3s ease-out'
              }}
            >
              <Box
                sx={{
                  p: 2,
                  bgcolor: '#FFF8F3',
                  borderBottom: '1px solid rgba(255, 107, 44, 0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5
                }}
              >
                <DeleteIcon color="error" />
                <Typography
                  variant="h6"
                  sx={{
                    color: '#FF6B2C',
                    fontWeight: 600,
                    fontSize: '1.2rem'
                  }}
                >
                  Confirmer la suppression
                </Typography>
              </Box>
              
              <Box sx={{ p: 3 }}>
                <Typography sx={{ color: 'rgba(0, 0, 0, 0.7)' }}>
                  Êtes-vous sûr de vouloir supprimer ce commentaire ? Cette action est irréversible.
                </Typography>
              </Box>

              <Box
                sx={{
                  px: 3,
                  py: 2,
                  bgcolor: 'rgba(0, 0, 0, 0.02)',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: 1
                }}
              >
                <Button
                  onClick={() => setDeleteCommentDialogOpen(false)}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      bgcolor: 'rgba(0, 0, 0, 0.05)'
                    }
                  }}
                >
                  Annuler
                </Button>
                <Button
                  onClick={() => commentToDelete && handleDeleteComment(commentToDelete)}
                  variant="contained"
                  color="error"
                  sx={{
                    fontWeight: 500,
                    boxShadow: '0 2px 8px rgba(211, 47, 47, 0.3)'
                  }}
                >
                  Supprimer
                </Button>
              </Box>
            </Paper>
          </ModalPortal>
        </Box>
      ) : (
        <LoadingSpinner />
      )}
    </Box>
  );
};

export default BugReportDetail; 