import React from 'react';
import { <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON>riangle, <PERSON>, <PERSON>, Zap } from 'lucide-react';
import { CircularProgress, LinearProgress } from '@mui/material';
import ModalPortal from '../ModalPortal';
import { motion, AnimatePresence } from 'framer-motion';
import { GenerationType } from '../../hooks/useAiGeneration';
import { useAiGenerationProcess } from '../../hooks/useAiGenerationProcess';
import useAiConsent from '../../hooks/useAiConsent';
import logger from '../../utils/logger';
import Tooltip from '@mui/material/Tooltip';

interface AiGenerationSystemProps {
  type: GenerationType;
  prompt: string;
  originalPrompt?: string;
  onComplete: (content: string | null) => void;
  onCancel?: () => void;
  autoStart?: boolean;
  maxDuration?: number;
}

/**
 * Système complet de génération IA avec modales de confirmation et de chargement
 */
const AiGenerationSystem: React.FC<AiGenerationSystemProps> = ({
  type,
  prompt,
  originalPrompt,
  onComplete,
  onCancel,
  autoStart = false
}) => {
  const { hasConsent } = useAiConsent();
  const {
    showConfirmModal,
    showLoadingModal,
    progress,
    currentStep,
    credits,
    isRateLimited,
    generating,
    startGeneration,
    handleConfirm,
    handleCancel
  } = useAiGenerationProcess({
    onSuccess: (content) => {
      logger.info("AiGenerationSystem: onSuccess appelé avec contenu:", content ? content.substring(0, 50) + "..." : "aucun contenu");
      onComplete(content);
    },
    onError: () => {
      logger.info("AiGenerationSystem: onError appelé");
      onComplete(null);
    },
    onCancel: onCancel,
    autoStart
  });

  // Démarrer la génération au montage du composant
  React.useEffect(() => {
    startGeneration(type, prompt, originalPrompt);
  }, []);

  return (
    <>
      {/* Modale de confirmation */}
      <AnimatePresence>
        {showConfirmModal && (
          <ModalPortal>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.5 }}
                className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-hidden transition-all duration-300 scale-100 m-4"
              >
                <div className="flex items-center justify-between bg-gradient-to-r from-[#FFF8F3] to-white p-4 border-b border-[#FFE4BA]">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="text-[#FF6B2C] h-5 w-5" />
                    <h3 className="text-lg font-semibold text-gray-800">Confirmation de génération IA</h3>
                    <Tooltip title="Cette fonctionnalité est en version bêta et peut contenir des bugs.">
                      <span className="ml-2 px-2 py-0.5 rounded-full text-xs font-bold bg-[#FF6B2C] text-white">beta</span>
                    </Tooltip>
                  </div>
                  <button
                    onClick={handleCancel}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
                  <div className="flex items-start space-x-3 mb-4">
                    <AlertTriangle className="text-[#FF6B2C] h-5 w-5 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-gray-600">
                        Cette action utilisera 1 crédit IA. Vous avez actuellement {credits} crédit{credits > 1 ? 's' : ''}.
                      </p>
                      <p className="text-sm text-gray-500 mt-2">
                        Le contenu généré sera basé sur vos données et le prompt sélectionné*.
                      </p>
                      <p style={{ fontSize: '10px' }} className="text-gray-500 mt-1">
                        * Vous pouvez gérer vos prompts personnalisés dans <a href="https://jobpartiel.fr/dashboard/ai-credits" target="_blank" rel="noopener noreferrer" className="text-[#FF6B2C] hover:underline">votre espace IA, onglet "Personnalisation des prompts"</a>.
                      </p>

                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                    <button
                      onClick={handleCancel}
                      className="w-full sm:w-auto px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-center"
                    >
                      Annuler
                    </button>
                    {!hasConsent ? (
                      <button
                        onClick={() => window.dispatchEvent(new CustomEvent('open-ai-consent-modal'))}
                        className="w-full sm:w-auto px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center"
                      >
                        <AlertTriangle className="mr-2 h-5 w-5" />
                        Accepter les CGU
                      </button>
                    ) : (
                      <button
                        onClick={handleConfirm}
                        disabled={generating || isRateLimited || credits <= 0}
                        className="w-full sm:w-auto px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        {generating ? (
                          <>
                            <CircularProgress size={20} thickness={5} sx={{ color: 'white', marginRight: '8px' }} />
                            Génération en cours...
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-5 w-5" />
                            Générer maintenant
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>

      {/* Modale de chargement avec animation de progression */}
      <AnimatePresence>
        {showLoadingModal && (
          <ModalPortal>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.5 }}
                className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-hidden transition-all duration-300 scale-100 m-4"
              >
                <div className="p-6">
                  <div className="flex flex-col items-center justify-center space-y-6">
                    {/* Icône animée */}
                    <div className="relative">
                      <motion.div
                        animate={{
                          scale: [1, 1.05, 1],
                          opacity: [0.8, 1, 0.8]
                        }}
                        transition={{
                          repeat: Infinity,
                          duration: 2,
                          ease: "easeInOut"
                        }}
                        className="bg-[#FFF8F3] rounded-full p-4"
                      >
                        <Brain className="h-10 w-10 text-[#FF6B2C]" />
                      </motion.div>

                      {/* Particules animées autour de l'icône */}
                      <motion.div
                        animate={{
                          rotate: 360,
                          opacity: [0.5, 0.8, 0.5]
                        }}
                        transition={{
                          repeat: Infinity,
                          duration: 8,
                          ease: "linear"
                        }}
                        className="absolute inset-0 pointer-events-none"
                        style={{ width: '100%', height: '100%' }}
                      >
                        {[...Array(3)].map((_, i) => (
                          <motion.div
                            key={i}
                            animate={{
                              x: [0, 10, 0, -10, 0],
                              y: [0, -10, 0, 10, 0],
                              opacity: [0.5, 1, 0.5]
                            }}
                            transition={{
                              repeat: Infinity,
                              duration: 3 + i,
                              delay: i * 0.5
                            }}
                            className="absolute"
                            style={{
                              left: `${50 + 30 * Math.cos(i * Math.PI * 2 / 3)}%`,
                              top: `${50 + 30 * Math.sin(i * Math.PI * 2 / 3)}%`,
                              transform: 'translate(-50%, -50%)'
                            }}
                          >
                            <Zap className="h-4 w-4 text-[#FF965E]" />
                          </motion.div>
                        ))}
                      </motion.div>
                    </div>

                    {/* Titre et étape actuelle */}
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-gray-800 mb-2">Génération IA en cours</h3>
                      <motion.p
                        key={currentStep.label}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="text-gray-600"
                      >
                        {currentStep.label}
                      </motion.p>
                    </div>

                    {/* Barre de progression */}
                    <div className="w-full space-y-2">
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>Progression</span>
                        <span>{Math.round(progress)}%</span>
                      </div>
                      <LinearProgress
                        variant="determinate"
                        value={progress}
                        sx={{
                          height: 8,
                          borderRadius: 4,
                          backgroundColor: '#FFE4BA',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            backgroundColor: '#FF6B2C',
                            backgroundImage: 'linear-gradient(45deg, #FF6B2C 0%, #FF965E 50%, #FF6B2C 100%)',
                            backgroundSize: '200% auto',
                            animation: 'gradient 2s ease infinite',
                          },
                          '@keyframes gradient': {
                            '0%': {
                              backgroundPosition: '0% 50%'
                            },
                            '50%': {
                              backgroundPosition: '100% 50%'
                            },
                            '100%': {
                              backgroundPosition: '0% 50%'
                            }
                          }
                        }}
                      />
                    </div>

                    {/* Message d'attente */}
                    <p className="text-sm text-gray-500 text-center">
                      Veuillez patienter pendant que notre IA génère votre contenu...
                    </p>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </ModalPortal>
        )}
      </AnimatePresence>
    </>
  );
};

export default AiGenerationSystem;
