import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useFormValidation, validationRules } from '../../hooks/useFormValidation';
import { FormField } from '../../components/FormField';
import LoadingBar from '../../components/LoadingBar';
import { notify } from '@/components/Notification';
import { getCookie, removeCookie, setCookie } from '../../utils/cookieUtils';
import logger from '@/utils/logger';
import DOMPurify from 'dompurify';
import { Eye, EyeOff } from 'lucide-react';
import GoogleSignInButton from '@/components/common/GoogleSignInButton';


const COOLDOWN_DURATION = 3; // 3 secondes de cooldown

// Style personnalisé pour la case à cocher
const checkboxStyle = `
  .custom-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 1rem;
    height: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    outline: none;
    cursor: pointer;
    position: relative;
  }
  
  .custom-checkbox:checked {
    background-color: #FF6B2C;
    border-color: #FF6B2C;
  }
  
  .custom-checkbox:checked::after {
    content: '';
    position: absolute;
    width: 0.3rem;
    height: 0.6rem;
    border: solid white;
    border-width: 0 2px 2px 0;
    top: 0.1rem;
    left: 0.35rem;
    transform: rotate(45deg);
  }
  
  .custom-checkbox:focus {
    box-shadow: 0 0 0 2px rgba(255, 107, 44, 0.3);
  }
`;

export const Register = () => 
{
  const navigate = useNavigate();
  const location = useLocation();
  const { inscription } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState(false);
  const [hasReferralCode, setHasReferralCode] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    referralCode: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [lastAttemptTime, setLastAttemptTime] = useState<number | null>(null);

  // Récupérer le code de parrainage depuis l'URL si présent
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const refCode = searchParams.get('ref');
    if (refCode) {
      setFormData(prev => ({ ...prev, referralCode: refCode }));
      setHasReferralCode(true);
    }
  }, [location]);

  useEffect(() => {
    const updateCountdown = () => {
      if (lastAttemptTime) {
        const now = Date.now();
        const elapsed = Math.floor((now - lastAttemptTime) / 1000);
        const remaining = Math.max(0, COOLDOWN_DURATION - elapsed);
        
        if (remaining > 0) {
          setCountdown(remaining);
        } else {
          setCountdown(0);
          setLastAttemptTime(null);
          removeCookie('registrationCooldown');
        }
      } else {
        // Vérifier le cookie au cas où il y aurait un cooldown existant
        const storedCooldown = getCookie('registrationCooldown');
        if (storedCooldown) {
          const cooldownTime = parseInt(storedCooldown);
          const now = Date.now();
          if (cooldownTime > now) {
            setLastAttemptTime(cooldownTime - (COOLDOWN_DURATION * 1000));
          } else {
            removeCookie('registrationCooldown');
          }
        }
      }
    };

    // Mettre à jour immédiatement
    updateCountdown();

    // Mettre à jour toutes les secondes
    const intervalId = setInterval(updateCountdown, 1000);

    return () => clearInterval(intervalId);
  }, [lastAttemptTime]);

  const setCooldownTimer = () => {
    const now = Date.now();
    setLastAttemptTime(now);
    const expiryTime = now + (COOLDOWN_DURATION * 1000); // Calculer l'heure d'expiration
  
    // Passer la durée en secondes (COOLDOWN_DURATION) pour maxAge
    setCookie('registrationCooldown', expiryTime.toString(), COOLDOWN_DURATION); 
  
    setCountdown(COOLDOWN_DURATION); // Mettre à jour le compte à rebours en secondes
  };
  
  const validationRulesConfig = {
    email: [
      validationRules.required(),
      validationRules.email()
    ],
    password: [
      validationRules.required(),
      validationRules.password()
    ],
    confirmPassword: [
      validationRules.required(),
      (value: string) => ({
        isValid: value === formData.password,
        message: 'Les mots de passe ne correspondent pas'
      })
    ],
    referralCode: [] // Pas de validation obligatoire pour le code de parrainage
  };

  const {
    errors,
    validateForm,
    handleBlur,
    handleChange,
    touched,
  } = useFormValidation(validationRulesConfig);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const sanitizedValue = DOMPurify.sanitize(value);
    setFormData(prev => ({ ...prev, [name]: sanitizedValue }));
    handleChange(name, sanitizedValue);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasReferralCode(e.target.checked);
    if (!e.target.checked) {
      setFormData(prev => ({ ...prev, referralCode: '' }));
    }
  };

  // Fonction pour gérer le blur des champs
  const handleFieldBlur = (fieldName: string) => (e?: React.FocusEvent<HTMLInputElement>) => {
    const value = e ? e.target.value : formData[fieldName as keyof typeof formData];
    handleBlur(fieldName, value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Vérifier si on est en cooldown
    if (countdown > 0) {
      notify('Veuillez attendre 3 secondes entre chaque tentative', 'warning');
      return;
    }

    // Vérifier que les mots de passe correspondent
    if (formData.password !== formData.confirmPassword) {
      notify('Les mots de passe ne correspondent pas', 'error');
      return;
    }

    // Validation des données
    if (!validateForm(formData)) {
      notify('Veuillez corriger les erreurs du formulaire', 'error');
      return;
    }

    setIsSubmitting(true);

    try {
      // Activer le cooldown dès la tentative
      setCooldownTimer();

      const sanitizedEmail = DOMPurify.sanitize(formData.email);
      const sanitizedReferralCode = DOMPurify.sanitize(formData.referralCode);
      const userData = { 
        email: sanitizedEmail, 
        password: formData.password, 
        userType: 'non-jobbeur' as 'jobbeur' | 'non-jobbeur',
        referralCode: sanitizedReferralCode || undefined
      };
      const result = await inscription(userData);
      logger.info('Résultat de l\'inscription :', result); // Vérifier la structure de la réponse

      if (!result.success) {
        return { success: false, message: result.retour_message || 'Une erreur est survenue lors de l\'inscription.' };
      }
  
      // Afficher un message de confirmation
      notify('Inscription réussie ! Vérifiez vos emails pour valider votre compte.', 'success');

      // Réinitialiser le formulaire
      setFormData({ email: '', password: '', confirmPassword: '', referralCode: '' });

      // Redirection
      navigate('/verify-email', { 
        state: { 
          email: sanitizedEmail,
          fromSignup: true 
        } 
      });
    } catch (error: any) {
      logger.error('Erreur lors de l\'inscription', error);
      notify('Échec de l\'inscription', 'error');
    } finally {
      setIsSubmitting(false);
      setLoading(false); 
    }
  };

  const helpTexts = {
    email: 'Nous utiliserons cet email pour vous envoyer des notifications et confirmer votre compte',
    password: 'Un mot de passe fort contient au moins 8 caractères, des majuscules, minuscules, chiffres et caractères spéciaux',
    referralCode: 'Entrez le code de parrainage d\'un ami pour obtenir 20 Jobis supplémentaires dès votre inscription'
  };

  if (loading) {
    return (
      <LoadingBar
        title="Création de votre compte"
        subtitle="Veuillez patienter pendant que nous préparons votre espace..."
        icon="UserPlus"
      />
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-24 px-4 sm:px-6 lg:px-8">
      <title>Inscription - JobPartiel.fr</title>
      <meta name="description" content="Créez un compte sur JobPartiel.fr pour proposer ou trouver des services locaux."></meta>
      <meta name="keywords" content="inscription, compte, jobpartiel, services locaux"></meta>
      <meta name="robots" content="index, follow"></meta>
      <meta property="og:title" content="Inscription - JobPartiel.fr"></meta>
      <meta property="og:description" content="Créez un compte sur JobPartiel.fr pour proposer ou trouver des services locaux."></meta>
      <meta property="og:type" content="website"></meta>
      <meta property="og:url" content="https://jobpartiel.fr/inscription"></meta>
      <meta property="og:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png"></meta>
      
      {/* Style personnalisé pour la case à cocher */}
      <style>{checkboxStyle}</style>
      
      <div className="max-w-md w-full space-y-8 p-8 bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl">
        <div>
          <h2 className="mt-2 text-center text-3xl font-extrabold text-gray-900 transition-all">
            Créez votre compte
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Ou{' '}
            <Link to="/login" className="font-medium text-[#FF7A35] hover:text-[#ff965e] transition-colors duration-300">
              connectez-vous à votre compte existant
            </Link>
          </p>
        </div>
        
        {/* Bouton d'inscription avec Google */}
        <div className="mt-4">
          <GoogleSignInButton text="S'inscrire avec Google" />
        </div>
        
        <div className="relative mt-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Ou inscrivez-vous avec votre email</span>
          </div>
        </div>
        
        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <FormField
              id="email"
              name="email"
              type="email"
              label="Email"
              autoComplete="email"
              required
              placeholder="Ex : <EMAIL>"
              value={formData.email}
              onChange={handleInputChange}
              onBlur={handleFieldBlur('email')}
              error={touched.email ? errors.email : ''}
              helpText={helpTexts.email}
            />

            <div className="relative">
              <FormField
                id="password"
                name="password"
                label="Mot de passe"
                type={showPasswords ? "text" : "password"}
                autoComplete="new-password"
                required
                placeholder="8 caractères minimum"
                value={formData.password}
                onChange={handleInputChange}
                onBlur={handleFieldBlur('password')}
                error={touched.password ? errors.password : ''}
                helpText={helpTexts.password}
              />
              <button
                type="button"
                className="absolute top-[38px] right-3 flex items-center justify-center text-gray-500 hover:text-gray-700"
                onClick={() => setShowPasswords(!showPasswords)}
              >
                {showPasswords ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className="relative">
              <FormField
                id="confirmPassword"
                name="confirmPassword"
                label="Confirmer le mot de passe"
                type={showPasswords ? "text" : "password"}
                autoComplete="new-password"
                required
                placeholder="8 caractères minimum"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                onBlur={handleFieldBlur('confirmPassword')}
                error={touched.confirmPassword ? errors.confirmPassword : ''}
              />
              <button
                type="button"
                className="absolute top-[38px] right-3 flex items-center justify-center text-gray-500 hover:text-gray-700"
                onClick={() => setShowPasswords(!showPasswords)}
              >
                {showPasswords ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className="flex items-center mb-2">
              <input
                id="hasReferralCode"
                name="hasReferralCode"
                type="checkbox"
                checked={hasReferralCode}
                onChange={handleCheckboxChange}
                className="custom-checkbox"
              />
              <label htmlFor="hasReferralCode" className="ml-2 block text-sm text-gray-700">
                Avez-vous un code de parrainage ?
              </label>
            </div>

            {hasReferralCode && (
              <FormField
                id="referralCode"
                name="referralCode"
                label="Code de parrainage"
                type="text"
                autoComplete="off"
                placeholder="Ex: ABC123 (donne 20 Jobis bonus)"
                value={formData.referralCode}
                onChange={handleInputChange}
                onBlur={handleFieldBlur('referralCode')}
                error={touched.referralCode ? errors.referralCode : ''}
                helpText={helpTexts.referralCode}
              />
            )}
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting || countdown > 0}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                isSubmitting || countdown > 0
                  ? 'bg-[#FF965E] cursor-not-allowed'
                  : 'bg-[#FF6B2C] hover:bg-[#FF7A35] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B2C]'
              } transition-all duration-300`}
            >
              {isSubmitting ? (
                <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
              ) : null}
              {countdown > 0 ? `Veuillez patienter (${countdown}s)` : 'S\'inscrire'}
            </button>

            <Link
              to="/forgot-password"
              className="mt-3 w-full flex justify-center py-2.5 px-4 text-sm font-medium text-gray-600 hover:text-gray-900 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200"
            >
              Mot de passe oublié ?
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
