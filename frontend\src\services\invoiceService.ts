import { api } from './api';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';
import { fetchCsrfToken } from '../services/csrf';

export interface InvoiceItem {
  id: string;
  description: string;
  quantite: number;
  unite: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

export interface Document {
  id: string;
  number: string;
  draft_number?: string;
  type: 'devis' | 'facture' | 'avoir';
  client_name: string;
  date_creation: string;
  date_validite?: string;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
  statut: string;
  description: string;
  invoice_items: InvoiceItem[];
  client_address?: string;
  client_email?: string;
  client_phone?: string;
  client_siret?: string;
  client_tva?: string;
  conditions_paiement?: string;
  mode_paiement?: string;
  mentions_legales?: string;
  mentions_tva?: string;
  penalite_retard?: string;
  indemnite_recouvrement?: string;
  notes?: string;
  forme_juridique?: string;
  code_ape?: string;
  user_id?: string;
  devis_origine_id?: string;
  facture_origine_id?: string;
  converti?: boolean;
  date_paiement?: string;
}

export interface Client {
  id?: string;
  nom: string;
  adresse?: string;
  email?: string;
  telephone?: string;
  siret?: string;
  tva?: string;
  forme_juridique?: string;
  code_ape?: string;
  notes?: string;
}

export interface CompanySettings {
  id?: string;
  nom: string;
  adresse?: string;
  code_postal?: string;
  ville?: string;
  pays?: string;
  telephone?: string;
  email?: string;
  site_web?: string;
  siret?: string;
  tva?: string;
  forme_juridique?: string;
  code_ape?: string;
  rcs?: string;
  capital?: string;
  logo_url?: string;
  signature_url?: string;
  iban?: string;
  bic?: string;
  banque?: string;
  mention_pied_page?: string;
}

// Interface pour les statistiques détaillées de facturation
export interface DetailedBillingStats {
  // Statistiques globales
  summary: {
    totalDocuments: number;
    totalAmount: number;
    averageAmount: number;
    pendingAmount: number;
    paidAmount: number;
    overdueAmount: number;
    rejectedAmount: number;
  };
  
  // Répartition par type de document
  byType: {
    [key: string]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };
  
  // Répartition par statut
  byStatus: {
    [key: string]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };
  
  // Répartition par client
  byClient: Array<{
    clientName: string;
    count: number;
    amount: number;
    percentage: number;
  }>;
  
  // Évolution dans le temps (6 derniers mois)
  monthly: Array<{
    month: string;
    quotes: number;
    invoices: number;
    creditNotes: number;
    amount: number;
    count: number;
  }>;
  
  // Délai moyen de paiement
  paymentStats: {
    averagePaymentDelay: number;
    paymentDelayByMonth: Array<{
      month: string;
      delay: number;
      count: number;
    }>;
  };
  
  // Statistiques sur les documents reçus (en tant que client)
  received: {
    totalDocuments: number;
    totalAmount: number;
    byType: {
      [key: string]: {
        count: number;
        amount: number;
      };
    };
    byStatus: {
      [key: string]: {
        count: number;
        amount: number;
      };
    };
    bySender: Array<{
      senderName: string;
      count: number;
      amount: number;
    }>;
  };
}

export const invoiceService = {
  // Récupérer les documents (factures, devis, avoirs)
  getDocuments: async (type: 'devis' | 'facture' | 'avoir' | 'all'): Promise<Document[]> => {
    try {
      const response = await api.get(`/api/invoices`, {
        params: { type }
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des documents');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des documents:', error);
      throw error;
    }
  },

  // Récupérer les clients
  getClients: async (): Promise<Client[]> => {
    try {
      const response = await api.get(`/api/clients`);

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des clients');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des clients:', error);
      throw error;
    }
  },

  // Créer un document
  createDocument: async (document: Partial<Document>): Promise<Document> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices`, document, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la création du document');
    } catch (error: any) {
      logger.error('Erreur lors de la création du document:', error);
      throw error;
    }
  },

  // Mettre à jour un document
  updateDocument: async (id: string, document: Partial<Document>): Promise<Document> => {
    try {
      const response = await api.put(`/api/invoices/${id}`, document);

      if (response.data.success) {
        return response.data.data;
      }
      
      // Si la réponse contient des détails d'erreur de validation
      if (response.data.errors) {
        const errorMessages = response.data.errors.map((err: any) => 
          `${err.field}: ${err.message}`
        ).join('\n');
        throw new Error(errorMessages || response.data.message || 'Erreur lors de la mise à jour du document');
      }
      
      throw new Error(response.data.message || 'Erreur lors de la mise à jour du document');
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour du document:', error);
      throw error;
    }
  },

  // Supprimer un document
  deleteDocument: async (id: string): Promise<void> => {
    try {
      const response = await api.delete(`/api/invoices/${id}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la suppression du document');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la suppression du document:', error);
      throw error;
    }
  },

  // Récupérer un document par son ID
  getDocumentById: async (id: string): Promise<Document> => {
    try {
      const response = await api.get(`/api/invoices/${id}`);

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération du document');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération du document:', error);
      throw error;
    }
  },

  // Dupliquer un document
  duplicateDocument: async (id: string): Promise<Document> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/duplicate`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la duplication du document');
    } catch (error: any) {
      logger.error('Erreur lors de la duplication du document:', error);
      throw error;
    }
  },

  // Convertir un devis en facture
  convertToInvoice: async (id: string): Promise<Document> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/convert-to-invoice`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la conversion du devis en facture');
    } catch (error: any) {
      logger.error('Erreur lors de la conversion du devis en facture:', error);
      throw error;
    }
  },

  // Créer un avoir à partir d'une facture
  createCreditNote: async (id: string): Promise<Document> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/credit-note`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la création de l\'avoir');
    } catch (error: any) {
      logger.error('Erreur lors de la création de l\'avoir:', error);
      throw error;
    }
  },

  // Récupérer le PDF comme un blob
  getPdf: async (id: string): Promise<Blob> => {
    try {
      const response = await api.get(`/api/invoices/${id}/pdf`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/pdf'
        }
      });

      return response.data;
    } catch (error: any) {
      logger.error('Erreur lors de la récupération du PDF:', error);
      throw error;
    }
  },

  // Récupérer le PDF d'un document reçu comme un blob
  getReceivedPdf: async (id: string): Promise<Blob> => {
    try {
      const response = await api.get(`/api/invoices/received/${id}/pdf`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/pdf'
        }
      });

      return response.data;
    } catch (error: any) {
      logger.error('Erreur lors de la récupération du PDF reçu:', error);
      throw error;
    }
  },

  // Envoyer un document par email
  sendByEmail: async (id: string, email: string, message: string): Promise<void> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/send`, {
        email,
        message
      }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'envoi du document');
      }
    } catch (error: any) {
      logger.error('Erreur lors de l\'envoi du document:', error);
      throw error;
    }
  },

  // Créer un client
  createClient: async (client: Client): Promise<Client> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/clients`, client, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la création du client');
    } catch (error: any) {
      logger.error('Erreur lors de la création du client:', error);
      throw error;
    }
  },

  // Mettre à jour un client
  updateClient: async (id: string, data: any) => {
    try {
      const response = await api.put(`/api/clients/${id}`, data);
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la mise à jour du client:', error);
      throw error;
    }
  },

  // Supprimer un client
  deleteClient: async (id: string) => {
    try {
      const response = await api.delete(`/api/clients/${id}`);
      return response.data;
    } catch (error) {
      logger.error('[API] Erreur lors de la suppression du client:', error);
      throw error;
    }
  },

  // Récupérer les paramètres d'entreprise
  getCompanySettings: async (): Promise<CompanySettings | null> => {
    try {
      const response = await api.get(`/api/invoices/company-settings`);

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des paramètres d\'entreprise');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des paramètres d\'entreprise:', error);
      throw error;
    }
  },

  // Mettre à jour les paramètres d'entreprise
  updateCompanySettings: async (settings: CompanySettings): Promise<CompanySettings> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/company-settings`, settings, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la mise à jour des paramètres d\'entreprise');
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour des paramètres d\'entreprise:', error);
      throw error;
    }
  },

  // Accepter un devis
  acceptQuote: async (id: string, acceptanceData: any): Promise<any> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/accept-quote`, acceptanceData, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data;
      }
      throw new Error(response.data.message || 'Erreur lors de l\'acceptation du devis');
    } catch (error: any) {
      logger.error('Erreur lors de l\'acceptation du devis:', error);
      throw error;
    }
  },

  // Refuser un devis
  rejectQuote: async (id: string, rejectionData: any): Promise<any> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await api.post(`/api/invoices/${id}/reject-quote`, rejectionData, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        return response.data;
      }
      throw new Error(response.data.message || 'Erreur lors du refus du devis');
    } catch (error: any) {
      logger.error('Erreur lors du refus du devis:', error);
      throw error;
    }
  },

  // Récupérer les devis qui ont été envoyés à l'utilisateur connecté
  getReceivedQuotes: async (): Promise<Document[]> => {
    try {
      const response = await api.get('/api/invoices/received/quotes');

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des devis reçus');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des devis reçus:', error);
      throw error;
    }
  },

  // Récupérer les factures qui ont été envoyées à l'utilisateur connecté
  getReceivedInvoices: async (): Promise<Document[]> => {
    try {
      const response = await api.get('/api/invoices/received/invoices');
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des factures reçues');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des factures reçues:', error);
      throw error;
    }
  },

  // Récupérer les avoirs qui ont été envoyés à l'utilisateur connecté
  getReceivedCreditNotes: async (): Promise<Document[]> => {
    try {
      const response = await api.get('/api/invoices/received/credit-notes');
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des avoirs reçus');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des avoirs reçus:', error);
      throw error;
    }
  },

  // Mettre à jour le statut d'un document
  updateDocumentStatus: async (id: string, newStatus: string, additionalData?: Partial<Document>): Promise<Document> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await api.patch(`/api/invoices/${id}/status`, {
        status: newStatus,
        ...additionalData
      }, {
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la mise à jour du statut');
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour du statut:', error);
      throw error;
    }
  },

  // Obtenir le numéro d'affichage selon le statut
  getDisplayNumber: (document: Document): string => {
    if (document.statut === 'brouillon' && document.draft_number) {
      // Extraire seulement la partie BROUILLON-XXXX pour simplifier l'affichage
      const match = document.draft_number.match(/BROUILLON-.*?-.*?-(\d+)$/);
      if (match) {
        return `BROUILLON-${match[1]}`;
      }
      return document.draft_number;
    }
    return document.number || 'N/A';
  },

  // Vérifier si un document est un brouillon
  isDraft: (document: Document): boolean => {
    return document.statut === 'brouillon';
  },

  // Exporter les clients en Excel
  exportClientsToExcel: async (): Promise<void> => {
    try {
      const response = await api.get('/api/clients/export/excel', {
        responseType: 'blob'
      });

      // Créer un lien de téléchargement
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Générer un nom de fichier avec la date
      const today = new Date().toISOString().split('T')[0];
      link.download = `clients_${today}.xlsx`;
      
      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();
      
      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (error: any) {
      logger.error('Erreur lors de l\'export Excel des clients:', error);
      throw error;
    }
  },

  // Récupérer les statistiques détaillées de facturation
  getBillingStats: async (filters?: {
    startDate?: Date;
    endDate?: Date;
    type?: 'devis' | 'facture' | 'avoir' | 'all';
  }): Promise<DetailedBillingStats> => {
    try {
      // Préparation des paramètres pour la requête
      const params: any = {};
      if (filters?.startDate) {
        params.startDate = filters.startDate.toISOString();
      }
      if (filters?.endDate) {
        params.endDate = filters.endDate.toISOString();
      }
      if (filters?.type && filters.type !== 'all') {
        params.type = filters.type;
      }

      const response = await api.get('/api/invoices/stats', { params });

      if (response.data.success) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Erreur lors de la récupération des statistiques');
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des statistiques de facturation:', error);
      throw error;
    }
  },

  // Exporter les statistiques de facturation en Excel
  exportBillingStatsToExcel: async (filters?: {
    startDate?: Date;
    endDate?: Date;
    type?: 'devis' | 'facture' | 'avoir' | 'all';
  }): Promise<void> => {
    try {
      // Préparation des paramètres pour la requête
      const params: any = {};
      if (filters?.startDate) {
        params.startDate = filters.startDate.toISOString();
      }
      if (filters?.endDate) {
        params.endDate = filters.endDate.toISOString();
      }
      if (filters?.type && filters.type !== 'all') {
        params.type = filters.type;
      }

      const response = await api.get('/api/invoices/stats/export/excel', {
        params,
        responseType: 'blob'
      });

      // Créer un lien de téléchargement
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Générer un nom de fichier avec la date
      const today = new Date().toISOString().split('T')[0];
      link.download = `statistiques_facturation_${today}.xlsx`;
      
      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();
      
      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (error: any) {
      logger.error('Erreur lors de l\'export Excel des statistiques:', error);
      throw error;
    }
  },

  // Exporter les statistiques de facturation en PDF
  exportBillingStatsToPDF: async (filters?: {
    startDate?: Date;
    endDate?: Date;
    type?: 'devis' | 'facture' | 'avoir' | 'all';
  }): Promise<void> => {
    try {
      // Préparation des paramètres pour la requête
      const params: any = {};
      if (filters?.startDate) {
        params.startDate = filters.startDate.toISOString();
      }
      if (filters?.endDate) {
        params.endDate = filters.endDate.toISOString();
      }
      if (filters?.type && filters.type !== 'all') {
        params.type = filters.type;
      }

      const response = await api.get('/api/invoices/stats/export/pdf', {
        params,
        responseType: 'blob'
      });

      // Créer un lien de téléchargement
      const blob = new Blob([response.data], {
        type: 'application/pdf'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Générer un nom de fichier avec la date
      const today = new Date().toISOString().split('T')[0];
      link.download = `statistiques_facturation_${today}.pdf`;
      
      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();
      
      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (error: any) {
      logger.error('Erreur lors de l\'export PDF des statistiques:', error);
      throw error;
    }
  },
};