import { useState, useEffect } from 'react';
import { notify } from '../../../components/Notification';
import { Tabs, Tab, styled } from '@mui/material';
import { useRefresh } from '../components/RefreshDashboard';
import { 
  Search, 
  X, 
  FileText, 
  Plus, 
  Info, 
  Users, 
  Settings, 
  Download, 
  Edit, 
  Copy, 
  Trash, 
  FileCheck,
  ArrowLeft,
  BarChart3
} from 'lucide-react';
import LoadingSpinner from '../../../components/LoadingSpinner';
import DocumentDetails from './DocumentDetails';
import DocumentForm from './DocumentForm';
import { invoiceService, Document } from '../../../services/invoiceService';
import { getDisplayNumber, isDraft } from '../../../services/billingService';
import { Typography } from '@mui/material';
import DeleteDocumentDialog from './DeleteDocumentDialog';
import useBillingData from '../../../hooks/invoices/useBillingData';
import logger from '@/utils/logger';
import ModalPortal from '../../../components/ModalPortal';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import DOMPurify from 'dompurify';
import DocumentPreviewModal from '../../../components/DocumentPreviewModal';
import BillingStatsPage from './BillingStatsPage';
import { useNavigate } from 'react-router-dom';
import { useCompanyValidation } from '../../../hooks/invoices/useCompanyValidation';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

// Styled components pour les tabs
const StyledTabs = styled(Tabs)({
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF7A35',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-scrollButtons': {
    color: '#FF7A35',
  },
  '@media (max-width: 600px)': {
    '& .MuiTabs-flexContainer': {
      justifyContent: 'flex-start',
    }
  }
});

const StyledTab = styled(Tab)({
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.875rem',
  color: '#666',
  minWidth: 0,
  padding: '12px 8px',
  '&.Mui-selected': {
    color: '#FF7A35',
    fontWeight: 600,
  },
  '&:hover': {
    color: '#FF7A35',
    opacity: 0.8,
  }
});

// Étendre l'interface Document pour inclure sender_info
declare module '../../../services/invoiceService' {
  interface Document {
    sender_info?: {
      nom?: string;
      prenom?: string;
      entreprise?: string;
    };
  }
}

export default function BillingPage() {
  // Utiliser le hook pour les données de facturation
  const {
    data: billingData,
    loading: billingLoading,
    error: billingError,
    updateDocument,
    addDocument,
    removeDocument,
    refreshData
  } = useBillingData();

  // États dérivés des données du hook
  const documents = billingData?.documents || [];
  const clients = billingData?.clients || [];
  const receivedQuotes = billingData?.receivedQuotes || [];
  const receivedInvoices = billingData?.receivedInvoices || [];
  const receivedCreditNotes = billingData?.receivedCreditNotes || [];
  const clientsList = Array.from(new Set(documents.map((doc: Document) => doc.client_name)))
    .filter(Boolean)
    .sort();

  const [activeTab, setActiveTab] = useState<'devis' | 'factures' | 'avoirs' | 'recus' | 'factures_recues' | 'avoirs_recus'>('devis');
  const [tabValue, setTabValue] = useState(0);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | undefined>(undefined);
  const [shouldScrollToForm, setShouldScrollToForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterClient, setFilterClient] = useState<string>('');
  const [sendEmailOpen, setSendEmailOpen] = useState(false);
  const [emailData, setEmailData] = useState({ email: '', message: '', confirmed: false });
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const { refreshCount, refresh } = useRefresh();
  const [pdfPreview, setPdfPreview] = useState<string | null>(null);
  const [subscriptionPlan, setSubscriptionPlan] = useState<'gratuit' | 'premium'>('gratuit');
  const [subscriptionLimits, setSubscriptionLimits] = useState<any>(null);
  const [showLimitModal, setShowLimitModal] = useState(false);
  const [documentTypeLimit, setDocumentTypeLimit] = useState<'devis' | 'factures' | 'avoirs'>('devis');
  const [previewDocumentOpen, setPreviewDocumentOpen] = useState(false);
  const [previewSettings, setPreviewSettings] = useState<any>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionData, setRejectionData] = useState({ reason: '', message: '' });

  // État pour les notifications de mise à jour en temps réel
  const [showUpdateNotification, setShowUpdateNotification] = useState(false);
  const [updateNotificationData, setUpdateNotificationData] = useState<{
    documentType: 'devis' | 'facture' | 'avoir';
    action: 'updated' | 'created' | 'sent' | 'converted';
  }>({ documentType: 'devis', action: 'updated' });

  // Nouvel état pour afficher/masquer la page des statistiques
  const [showStats, setShowStats] = useState(false);

  const navigate = useNavigate();
  const { isCompanyInfoComplete, getValidationMessage } = useCompanyValidation();

  // Fonction utilitaire pour afficher les notifications de mise à jour
  const showNotification = (document: Document, action: 'updated' | 'created' | 'sent' | 'converted') => {
    const safeDocumentType = (['devis', 'facture', 'avoir'].includes(document.type))
      ? document.type as 'devis' | 'facture' | 'avoir'
      : 'devis';

    setUpdateNotificationData({
      documentType: safeDocumentType,
      action
    });
    setShowUpdateNotification(true);
  };

  // Récupérer le statut d'abonnement et les limites
  useEffect(() => {
    const fetchSubscriptionStatus = async () => {
      try {
        const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions/status`, { 
          withCredentials: true,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        if (response.data.success) {
          setSubscriptionPlan(response.data.plan === 'premium' ? 'premium' : 'gratuit');
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération du statut d\'abonnement:', error);
      }
    };

    const fetchSubscriptionLimits = async () => {
      try {
        const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions`, { 
          withCredentials: true,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        if (response.data.success) {
          setSubscriptionLimits(response.data.data);
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération des limites d\'abonnement:', error);
      }
    };

    fetchSubscriptionStatus();
    fetchSubscriptionLimits();
  }, []);

  const checkDocumentLimit = (type: 'devis' | 'factures' | 'avoirs'): boolean => {
    if (subscriptionPlan === 'premium') return true; // Pas de limite pour les utilisateurs premium
    
    if (!subscriptionLimits) return true; // Si les limites ne sont pas chargées, on autorise
    
    // Compter les documents par type
    const devisCount = documents.filter(doc => doc.type === 'devis').length;
    const facturesCount = documents.filter(doc => doc.type === 'facture').length;
    const avoirsCount = documents.filter(doc => doc.type === 'avoir').length;
    
    // Vérifier les limites selon le type
    if (type === 'devis') {
      const limit = subscriptionLimits.gratuit.quotes.included;
      if (devisCount >= limit) {
        setDocumentTypeLimit('devis');
        setShowLimitModal(true);
        return false;
      }
    } else if (type === 'factures') {
      const limit = subscriptionLimits.gratuit.invoices.included;
      if (facturesCount >= limit) {
        setDocumentTypeLimit('factures');
        setShowLimitModal(true);
        return false;
      }
    } else if (type === 'avoirs') {
      // Même limite que les factures pour les avoirs
      const limit = subscriptionLimits.gratuit.invoices.included;
      if (avoirsCount >= limit) {
        setDocumentTypeLimit('avoirs');
        setShowLimitModal(true);
        return false;
      }
    }
    
    return true;
  };

  const redirectToPremium = () => {
    navigate('/dashboard/premium');
  };

  // Les données sont maintenant gérées par le hook useBillingData
  // Rafraîchir les données quand refreshCount change
  useEffect(() => {
    if (refreshCount > 0) {
      refreshData();
    }
  }, [refreshCount]); // Supprimer refreshData des dépendances pour éviter les boucles

  // Filtrer les documents selon les critères
  const filteredDocuments = documents.filter(doc => {
    // Si on est dans l'onglet "Devis reçus", "Factures reçues" ou "Avoirs reçus", on n'affiche aucun document ici
    if (activeTab === 'recus' || activeTab === 'factures_recues' || activeTab === 'avoirs_recus') return false;
    
    const matchesType = doc.type === (activeTab === 'devis' ? 'devis' : activeTab === 'factures' ? 'facture' : 'avoir');
    const matchesSearch = !searchText ||
      getDisplayNumber(doc).toLowerCase().includes(searchText.toLowerCase()) ||
      doc.client_name.toLowerCase().includes(searchText.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchText.toLowerCase()));
    
    // Ajuster la condition pour le statut pour prendre en compte les devis convertis
    const matchesStatus = !filterStatus || 
      (doc.statut === filterStatus) || 
      (filterStatus === 'facture' && doc.type === 'devis' && doc.facture_origine_id);
    
    const matchesClient = !filterClient || doc.client_name === filterClient;
    
    return matchesType && matchesSearch && matchesStatus && matchesClient;
  });

  // Filtrer les devis reçus
  const filteredReceivedQuotes = receivedQuotes.filter(doc => {
    const matchesSearch = !searchText ||
      getDisplayNumber(doc).toLowerCase().includes(searchText.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchText.toLowerCase()));

    const matchesStatus = !filterStatus || doc.statut === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Filtrer les factures reçues
  const filteredReceivedInvoices = receivedInvoices.filter(doc => {
    const matchesSearch = !searchText ||
      getDisplayNumber(doc).toLowerCase().includes(searchText.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchText.toLowerCase()));

    const matchesStatus = !filterStatus || doc.statut === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Filtrer les avoirs reçus
  const filteredReceivedCreditNotes = receivedCreditNotes.filter(doc => {
    const matchesSearch = !searchText ||
      getDisplayNumber(doc).toLowerCase().includes(searchText.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchText.toLowerCase()));

    const matchesStatus = !filterStatus || doc.statut === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Obtenir les statuts disponibles pour le filtre
  const availableStatuses = Array.from(new Set([
    ...documents.map(doc => doc.statut),
    // Ajouter manuellement le statut 'facture' pour les devis convertis si ce n'est pas déjà là
    ...(documents.some(doc => doc.type === 'devis' && doc.facture_origine_id) ? ['facture'] : [])
  ]))
    .filter(Boolean)
    .sort();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setActiveTab(['devis', 'factures', 'avoirs', 'recus', 'factures_recues', 'avoirs_recus'][newValue] as 'devis' | 'factures' | 'avoirs' | 'recus' | 'factures_recues' | 'avoirs_recus');
    setSelectedDocument(null);
    setFilterStatus('');
    setFilterClient('');
    setSearchText('');
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  const clearSearch = () => {
    setSearchText('');
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterStatus(e.target.value);
  };

  const handleClientFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterClient(e.target.value);
  };

  const clearFilters = () => {
    setFilterStatus('');
    setFilterClient('');
    setSearchText('');
  };

  const handleCreateDocument = () => {
    // Vérifier d'abord si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    // Vérifier si l'utilisateur a atteint sa limite
    if (!checkDocumentLimit(activeTab === 'devis' ? 'devis' : activeTab === 'factures' ? 'factures' : 'avoirs')) {
      return;
    }
    
    setIsCreating(true);
    setEditingDocument(undefined);
    setShowForm(true);
    setShouldScrollToForm(true);
  };

  const handleEditDocument = (document: Document) => {
    if (document.statut === 'brouillon') {
      setIsCreating(false);
      setEditingDocument(document);
      setShowForm(true);
      setShouldScrollToForm(prev => !prev);
    } else if (document.type === 'facture' && ['paye', 'partiellement_paye'].includes(document.statut)) {
      notify('Les factures payées ne peuvent plus être modifiées', 'warning');
    } else {
      notify('Seuls les documents en brouillon peuvent être modifiés', 'warning');
    }
  };

  const handleDeleteDocument = (document: Document) => {
    if (document.statut === 'brouillon') {
      setDocumentToDelete(document);
      setShowDeleteModal(true);
    } else if (document.type === 'facture' && ['paye', 'partiellement_paye'].includes(document.statut)) {
      notify('Les factures payées ne peuvent plus être supprimées', 'warning');
    } else {
      notify('Seuls les documents en brouillon peuvent être supprimés', 'warning');
    }
  };

  const confirmDelete = async () => {
    if (!documentToDelete) return;

    try {
      // Suppression immédiate de l'interface pour une UX en temps réel
      removeDocument(documentToDelete.id);

      // Fermer les modales et réinitialiser les états
      setDocumentToDelete(null);
      setShowDeleteModal(false);
      setSelectedDocument(null);

      // Appeler l'API en arrière-plan
      await invoiceService.deleteDocument(documentToDelete.id);

      notify('Document supprimé avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors de la suppression du document:', error);

      // En cas d'erreur, on pourrait restaurer le document, mais c'est complexe
      // Pour l'instant, on affiche juste l'erreur et on recommande un refresh
      notify('Erreur lors de la suppression du document. Veuillez actualiser la page.', 'error');
    }
  };

  const handleDuplicateDocument = async (document: Document) => {
    // Vérifier d'abord si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    // Vérifier si l'utilisateur a atteint sa limite selon le type de document
    if (document.type === 'devis' && !checkDocumentLimit('devis')) {
      return;
    } else if (document.type === 'facture' && !checkDocumentLimit('factures')) {
      return;
    } else if (document.type === 'avoir' && !checkDocumentLimit('avoirs')) {
      return;
    }

    try {
      const duplicatedDocument = await invoiceService.duplicateDocument(document.id);

      // Ajout immédiat du document dupliqué pour une UX en temps réel
      addDocument(duplicatedDocument);

      notify('Document dupliqué avec succès', 'success');
    } catch (error: any) {
      console.error('Erreur lors de la duplication du document:', error);
      // Afficher le message d'erreur exact du backend s'il existe
      const errorMessage = error.response?.data?.message || 'Erreur lors de la duplication du document';
      notify(errorMessage, 'error');
    }
  };

  const handleConvertToInvoice = async (document: Document) => {
    if (document.type !== 'devis') {
      notify('Seuls les devis peuvent être convertis en facture', 'warning');
      return;
    }

    if (document.statut !== 'accepte') {
      notify('Seuls les devis acceptés peuvent être convertis en facture', 'warning');
      return;
    }

    // Vérifier si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    try {
      const invoiceDocument = await invoiceService.convertToInvoice(document.id);

      // Mise à jour immédiate du statut du devis original pour une UX en temps réel
      const updatedQuote = {...document, statut: 'facture'};
      updateDocument(updatedQuote);
      addDocument(invoiceDocument);

      // Mettre à jour le document sélectionné pour afficher la nouvelle facture
      if (selectedDocument && selectedDocument.id === document.id) {
        setSelectedDocument(invoiceDocument);
      }

      notify('Devis converti en facture avec succès', 'success');
      setActiveTab('factures');
      setTabValue(1); // Update the tab value to 1 (factures)
    } catch (error: any) {
      console.error('Erreur lors de la conversion du devis:', error);
      // Afficher le message d'erreur exact du backend s'il existe
      const errorMessage = error.response?.data?.message || 'Erreur lors de la conversion du devis';
      notify(errorMessage, 'error');
    }
  };

  const handleCreateCreditNote = async (document: Document) => {
    if (document.type !== 'facture' || ['brouillon', 'annule'].includes(document.statut)) {
      notify('Impossible de créer un avoir pour une facture en brouillon ou déjà annulée', 'warning');
      return;
    }

    // Vérifier si les informations d'entreprise sont complètes
    if (!isCompanyInfoComplete()) {
      notify(getValidationMessage(), 'error');
      return;
    }

    try {
      const creditNote = await invoiceService.createCreditNote(document.id);

      // Mise à jour immédiate du statut de la facture originale pour une UX en temps réel
      if (document.statut !== 'annule') {
        const updatedInvoice = {...document, statut: 'annule'};
        updateDocument(updatedInvoice);
      }

      // Ajout immédiat de l'avoir pour une UX en temps réel
      addDocument(creditNote);

      // Mettre à jour le document sélectionné pour afficher le nouvel avoir
      if (selectedDocument && selectedDocument.id === document.id) {
        setSelectedDocument(creditNote);
      }

      notify('Avoir créé avec succès', 'success');
      setActiveTab('avoirs');
      setTabValue(2); // Update the tab value to 2 (avoirs)
    } catch (error: any) {
      console.error('Erreur lors de la création de l\'avoir:', error);
      // Afficher le message d'erreur exact du backend s'il existe
      const errorMessage = error.response?.data?.message || 'Erreur lors de la création de l\'avoir';
      notify(errorMessage, 'error');
    }
  };

  const handleDownloadPDF = async (document: Document, isReceived: boolean = false) => {
    try {
      const blob = isReceived
        ? await invoiceService.getReceivedPdf(document.id)
        : await invoiceService.getPdf(document.id);
      const url = window.URL.createObjectURL(blob);

      // Créer un lien de téléchargement temporaire
      const link = window.document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${document.type}-${getDisplayNumber(document)}.pdf`);

      // Déclencher le téléchargement
      window.document.body.appendChild(link);
      link.click();

      // Nettoyer
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      notify('Document téléchargé avec succès', 'success');
    } catch (error) {
      console.error('Erreur lors du téléchargement du PDF:', error);
      notify('Erreur lors du téléchargement du document', 'error');
    }
  };

  const handleOpenEmailDialog = async (document: Document) => {
    try {
      // Nettoyer l'URL précédente si elle existe
      if (pdfPreview) {
        window.URL.revokeObjectURL(pdfPreview);
        setPdfPreview(null);
      }
      
      // Préremplir l'email si disponible
      if (document.client_email) {
        setEmailData({ email: document.client_email, message: '', confirmed: false });
      } else {
        setEmailData({ email: '', message: '', confirmed: false });
      }
      
      // Ouvrir la boîte de dialogue
      setSendEmailOpen(true);
      setSelectedDocument(document);
    } catch (error) {
      logger.info('Erreur lors de la préparation du document:', error);
      notify('Erreur lors de la préparation du document', 'error');
    }
  };

  const handleSendEmail = async () => {
    if (!selectedDocument || isSendingEmail) return;

    setIsSendingEmail(true);

    try {
      await invoiceService.sendByEmail(
        selectedDocument.id,
        DOMPurify.sanitize(emailData.email),
        DOMPurify.sanitize(emailData.message)
      );

      // Mise à jour immédiate du statut local pour une UX en temps réel
      const updatedDocument = {
        ...selectedDocument,
        statut: 'envoye',
        // Si c'était un brouillon, supprimer le numéro de brouillon et utiliser le numéro officiel
        ...(selectedDocument.statut === 'brouillon' && {
          number: selectedDocument.draft_number || selectedDocument.number,
          draft_number: undefined
        })
      };

      // Mettre à jour le document dans la liste globale
      updateDocument(updatedDocument);

      // Mettre à jour le document sélectionné pour la vue détaillée
      setSelectedDocument(updatedDocument);

      // Afficher la notification d'envoi en temps réel
      showNotification(updatedDocument, 'sent');

      notify('Document envoyé avec succès', 'success');
      setSendEmailOpen(false);
      setEmailData({ email: '', message: '', confirmed: false });

      // Nettoyer l'URL du PDF
      if (pdfPreview) {
        window.URL.revokeObjectURL(pdfPreview);
        setPdfPreview(null);
      }
    } catch (error: any) {
      console.error('Erreur lors de l\'envoi du document:', error);

      // Afficher le message d'erreur exact du backend s'il existe
      const errorMessage = error.response?.data?.message || 'Erreur lors de l\'envoi du document';
      notify(errorMessage, 'error');
    } finally {
      setIsSendingEmail(false);
    }
  };

  const closeEmailDialog = () => {
    // Empêcher la fermeture si un envoi est en cours
    if (isSendingEmail) return;

    setSendEmailOpen(false);

    // Révoquer l'URL du blob pour nettoyer la mémoire
    if (pdfPreview) {
      window.URL.revokeObjectURL(pdfPreview);
      setPdfPreview(null);
    }
  };

  const getStatusText = (statut: string, type?: string): string => {
    // Utiliser des textes différents selon le type de document
    if (type === 'facture') {
      const statusMap: Record<string, string> = {
        'brouillon': 'Brouillon',
        'envoye': 'Envoyée',
        'accepte': 'Acceptée',
        'refuse': 'Refusée',
        'expire': 'Expirée',
        'paye': 'Payée',
        'partiellement_paye': 'Partiellement payée',
        'en_retard': 'En retard',
        'annule': 'Annulée',
        'facture': 'Facture créée'
      };
      return statusMap[statut] || statut;
    } else {
      const statusMap: Record<string, string> = {
        'brouillon': 'Brouillon',
        'envoye': 'Envoyé',
        'accepte': 'Accepté',
        'refuse': 'Refusé',
        'expire': 'Expiré',
        'paye': 'Payé',
        'partiellement_paye': 'Partiellement payé',
        'en_retard': 'En retard',
        'annule': 'Annulé',
        'facture': 'Facture créée'
      };
      return statusMap[statut] || statut;
    }
  };

  const getStatusColor = (statut: string): string => {
    const colorMap: Record<string, string> = {
      'brouillon': 'bg-gray-100 text-gray-800',
      'envoye': 'bg-blue-100 text-blue-800',
      'accepte': 'bg-green-100 text-green-800',
      'refuse': 'bg-red-100 text-red-800',
      'expire': 'bg-yellow-100 text-yellow-800',
      'paye': 'bg-emerald-100 text-emerald-800',
      'partiellement_paye': 'bg-teal-100 text-teal-800',
      'en_retard': 'bg-orange-100 text-orange-800',
      'annule': 'bg-pink-100 text-pink-800',
      'facture': 'bg-purple-100 text-purple-800 border border-purple-400'
    };
    
    return colorMap[statut] || 'bg-gray-100 text-gray-800';
  };

  const handleFormClose = () => {
    setShowForm(false);
    setEditingDocument(undefined);
  };

  // Utilisation d'une assertion de type avec as pour bypasser la vérification de type
  const handleFormSubmit = async (formData: Partial<Document>) => {
    try {
      let updatedDocument: Document;
      if (editingDocument) {
        updatedDocument = await invoiceService.updateDocument(editingDocument.id, formData as any);
        
        // CORRECTION CRITIQUE : S'assurer que l'ID est préservé
        if (!updatedDocument.id) {
          updatedDocument.id = editingDocument.id;
          logger.warn('ID manquant dans la réponse du serveur, utilisation de l\'ID original:', editingDocument.id);
        }
        
        // CORRECTION CRITIQUE : Fusionner TOUTES les données du formulaire avec la réponse du backend
        // pour s'assurer que tous les champs sont à jour, même si le backend ne les renvoie pas tous
        updatedDocument = {
          ...editingDocument, // Données originales
          ...formData, // Données du formulaire (priorité)
          ...updatedDocument, // Données du backend (priorité finale pour les champs calculés)
          id: editingDocument.id // S'assurer que l'ID est toujours préservé
        };
        
        // S'assurer que tous les champs nécessaires sont présents, même si le backend ne les renvoie pas
        if (!updatedDocument.invoice_items) {
          updatedDocument.invoice_items = formData.invoice_items || [];
        }
        // S'assurer que les totaux sont définis
        if (updatedDocument.total_ht === undefined) {
          updatedDocument.total_ht = formData.total_ht || 0;
        }
        if (updatedDocument.total_tva === undefined) {
          updatedDocument.total_tva = formData.total_tva || 0;
        }
        if (updatedDocument.total_ttc === undefined) {
          updatedDocument.total_ttc = formData.total_ttc || 0;
        }

        // Mise à jour immédiate pour une UX en temps réel
        updateDocument(updatedDocument);

        // CORRECTION CRITIQUE : Mettre à jour le document sélectionné pour la prévisualisation en temps réel
        if (selectedDocument && selectedDocument.id === updatedDocument.id) {
          setSelectedDocument(updatedDocument);
          logger.info('Document sélectionné mis à jour pour la prévisualisation en temps réel:', updatedDocument.id);
        }

        // Afficher la notification de mise à jour en temps réel
        showNotification(updatedDocument, 'updated');

        notify('Document mis à jour avec succès', 'success');
      } else {
        updatedDocument = await invoiceService.createDocument(formData as any);
        // S'assurer que tous les champs nécessaires sont présents, même si le backend ne les renvoie pas
        if (!updatedDocument.invoice_items) {
          updatedDocument.invoice_items = formData.invoice_items || [];
        }
        // S'assurer que les totaux sont définis
        if (updatedDocument.total_ht === undefined) {
          updatedDocument.total_ht = formData.total_ht || 0;
        }
        if (updatedDocument.total_tva === undefined) {
          updatedDocument.total_tva = formData.total_tva || 0;
        }
        if (updatedDocument.total_ttc === undefined) {
          updatedDocument.total_ttc = formData.total_ttc || 0;
        }

        // Ajout immédiat du nouveau document pour une UX en temps réel
        addDocument(updatedDocument);

        // Sélectionner automatiquement le nouveau document créé
        setSelectedDocument(updatedDocument);

        // Afficher la notification de création en temps réel
        showNotification(updatedDocument, 'created');

        notify('Document créé avec succès', 'success');
      }
      setShowForm(false);
      setEditingDocument(undefined);
    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde du document:', error);
      notify(error.response?.data?.message || 'Erreur lors de la sauvegarde du document', 'error');
    }
  };

  // Réinitialiser shouldScrollToForm après le défilement
  useEffect(() => {
    if (shouldScrollToForm) {
      const timer = setTimeout(() => {
        setShouldScrollToForm(false);
      }, 1000); // Attendre que le défilement soit terminé
      return () => clearTimeout(timer);
    }
  }, [shouldScrollToForm]);

  // Récupérer les paramètres de l'entreprise pour la prévisualisation
  useEffect(() => {
    if (previewDocumentOpen && !previewSettings) {
      // Utiliser directement les paramètres globaux si disponibles
      setPreviewSettings(null); // Reset pour forcer le rechargement si nécessaire
    }
  }, [previewDocumentOpen, previewSettings]);

  // CORRECTION CRITIQUE : Synchroniser automatiquement selectedDocument avec les données mises à jour
  useEffect(() => {
    if (selectedDocument && documents.length > 0) {
      // Rechercher le document mis à jour dans la liste globale
      const updatedDocument = documents.find(doc => doc.id === selectedDocument.id);
      if (updatedDocument && JSON.stringify(updatedDocument) !== JSON.stringify(selectedDocument)) {
        setSelectedDocument(updatedDocument);
        logger.info('Document sélectionné synchronisé automatiquement avec les données mises à jour:', updatedDocument.id);
      }
    }
  }, [documents, selectedDocument]);

  // Fonction pour mettre à jour le statut d'une facture avec UX en temps réel
  const handleUpdateInvoiceStatus = async (document: Document, newStatus: string) => {
    if (document.type !== 'facture') {
      notify('Cette action n\'est disponible que pour les factures', 'warning');
      return;
    }

    try {
      // Ajout de la date de paiement si le statut est payé ou partiellement payé
      const updateData: Partial<Document> = { statut: newStatus };

      // Si le statut est changé à payé ou partiellement payé, ajouter la date de paiement
      if (['paye', 'partiellement_paye'].includes(newStatus)) {
        updateData.date_paiement = new Date().toISOString();
      }

      // Mise à jour immédiate de l'interface pour une UX en temps réel
      const updatedDoc = {
        ...document,
        statut: newStatus,
        ...(updateData.date_paiement && { date_paiement: updateData.date_paiement })
      };

      // Mettre à jour immédiatement l'interface
      updateDocument(updatedDoc);

      // Mettre à jour le document sélectionné si c'est celui qu'on modifie
      if (selectedDocument && selectedDocument.id === document.id) {
        setSelectedDocument(updatedDoc);
      }

      // Appeler le service de mise à jour en arrière-plan
      await invoiceService.updateDocumentStatus(document.id, newStatus, updateData);

      notify('Statut de la facture mis à jour avec succès', 'success');
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du statut:', error);

      // En cas d'erreur, restaurer l'état précédent
      updateDocument(document);
      if (selectedDocument && selectedDocument.id === document.id) {
        setSelectedDocument(document);
      }

      // Afficher le message d'erreur exact du backend s'il existe
      const errorMessage = error.response?.data?.message || 'Erreur lors de la mise à jour du statut';
      notify(errorMessage, 'error');
    }
  };

  return (
    <div className="space-y-6 px-2 md:px-0">
      {showStats ? (
        // Affichage de la page des statistiques
        <div>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-4">
            <PageTitle variant="h1">
              Statistiques de Facturation
            </PageTitle>
            <button
              onClick={() => setShowStats(false)}
              className="bg-gray-100 text-gray-600 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
            >
              <ArrowLeft size={18} />
              Retour
            </button>
          </div>
          <BillingStatsPage />
        </div>
      ) : (
        // Affichage normal de la page de facturation
        <>
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <PageTitle variant="h1">
              Facturation
            </PageTitle>
            
            <div className="flex flex-col md:flex-row gap-2">
              {['devis', 'factures', 'avoirs'].includes(activeTab) && (
                <button
                  onClick={() => handleCreateDocument()}
                  className="bg-[#FF7A35] text-white px-4 py-2 rounded-md hover:bg-[#ff6b2c] transition-colors flex items-center justify-center gap-2"
                >
                  <Plus size={18} />
                  {activeTab === 'devis' ? 'Nouveau devis' : activeTab === 'factures' ? 'Nouvelle facture' : 'Nouvel avoir'}
                </button>
              )}

              <button
                onClick={() => navigate('/dashboard/clients')}
                className="bg-gray-100 text-gray-600 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
              >
                <Users size={18} />
                Gérer les clients
              </button>
              
              <button
                onClick={() => navigate('/dashboard/facturation/parametres')}
                className="bg-gray-100 text-gray-600 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
              >
                <Settings size={18} />
                Paramètres
              </button>
              
              <button
                onClick={() => setShowStats(true)}
                className="bg-gray-100 text-gray-600 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
              >
                <BarChart3 size={18} />
                Statistiques
              </button>
              
              <div className="flex gap-2 items-center">
                <button
                  onClick={clearFilters}
                  className="bg-gray-100 text-gray-600 px-3 py-2 rounded-md hover:bg-gray-200 transition-colors text-sm"
                  disabled={!filterStatus && !filterClient && !searchText}
                >
                  Réinitialiser
                </button>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Rechercher..."
                    value={searchText}
                    onChange={handleSearchChange}
                    className="pl-9 pr-4 py-2 border rounded-md focus:outline-none focus:border-[#FF7A35] w-full"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  {searchText && (
                    <button 
                      onClick={clearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Beta warning banner */}
          <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-[#FF7A35]" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-orange-700">
                  <strong>Version Beta :</strong> Cette fonctionnalité est actuellement en version beta. Des bugs peuvent survenir. 
                  N'hésitez pas à nous signaler tout problème rencontré pour nous aider à améliorer le service.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow mb-6">
            <StyledTabs 
              value={tabValue} 
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
              aria-label="documents tabs"
            >
              <StyledTab 
                label={`Devis (${documents.filter(d => d.type === 'devis').length})`}
              />
              <StyledTab 
                label={`Factures (${documents.filter(d => d.type === 'facture').length})`}
              />
              <StyledTab 
                label={`Avoirs (${documents.filter(d => d.type === 'avoir').length})`}
              />
              <StyledTab 
                label={`Devis reçus (${receivedQuotes.length})`}
              />
              <StyledTab 
                label={`Factures reçues (${receivedInvoices.length})`}
              />
              <StyledTab 
                label={`Avoirs reçus (${receivedCreditNotes.length})`}
              />
            </StyledTabs>
              
            {['devis', 'factures', 'avoirs', 'recus', 'factures_recues', 'avoirs_recus'].map((tabType, idx) => (
              <div key={idx} className={tabValue === idx ? 'p-3' : 'hidden'}>
                <div className="flex flex-col mb-4 md:flex-row md:items-center md:justify-between gap-2">
                  <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
                    <select
                      value={filterStatus}
                      onChange={handleStatusFilterChange}
                      className="p-2 border rounded-md focus:outline-none focus:border-[#FF7A35]"
                    >
                      <option value="">Tous les statuts</option>
                      {availableStatuses.map(status => (
                        <option key={status} value={status}>
                          {getStatusText(status, tabType === 'factures' ? 'facture' : undefined)}
                        </option>
                      ))}
                    </select>
                    
                    <select
                      value={filterClient}
                      onChange={handleClientFilterChange}
                      className="p-2 border rounded-md focus:outline-none focus:border-[#FF7A35]"
                    >
                      <option value="">Tous les clients</option>
                      {clientsList.map(client => (
                        <option key={client} value={client}>
                          {client}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {tabType === 'recus' ? filteredReceivedQuotes.length : 
                     tabType === 'factures_recues' ? filteredReceivedInvoices.length :
                     tabType === 'avoirs_recus' ? filteredReceivedCreditNotes.length :
                     tabType === 'devis' ? documents.filter(d => d.type === 'devis').length : 
                     tabType === 'factures' ? documents.filter(d => d.type === 'facture').length : 
                     documents.filter(d => d.type === 'avoir').length} document{tabType === 'devis' || tabType === 'factures' || tabType === 'avoirs' || tabType === 'avoirs_recus' ? 's' : ''} trouvé{tabType === 'devis' || tabType === 'factures' || tabType === 'avoirs' || tabType === 'avoirs_recus' ? 's' : ''}
                  </div>
                </div>
                
                {billingLoading ? (
                  <div className="flex justify-center py-20">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <>
                    {tabType === 'recus' ? (
                      filteredReceivedQuotes.length === 0 ? (
                        <div className="text-center py-10 text-gray-500">
                          <FileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                          <p>Aucun devis reçu trouvé</p>
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <table className="min-w-full bg-white rounded-lg shadow-sm">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {activeTab === 'recus' || activeTab === 'factures_recues' ? 'De' : 'Client'}
                                </th>
                                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th className="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                              {filteredReceivedQuotes.map((document) => (
                                <tr 
                                  key={document.id} 
                                  className={`hover:bg-gray-50 cursor-pointer ${selectedDocument?.id === document.id ? 'bg-blue-50' : ''}`}
                                  onClick={() => setSelectedDocument(document)}
                                >
                                  <td className="py-3 px-4 whitespace-nowrap">
                                    {getDisplayNumber(document)}
                                    {isDraft(document) && (
                                      <span className="ml-2 text-xs text-gray-500 italic">(Brouillon)</span>
                                    )}
                                  </td>
                                  <td className="py-3 px-4 whitespace-nowrap">
                                    {document.sender_info ? (
                                      <div>
                                        <div>{document.sender_info.prenom} {document.sender_info.nom}</div>
                                        {document.sender_info.entreprise && (
                                          <div className="text-xs text-gray-500">{document.sender_info.entreprise}</div>
                                        )}
                                      </div>
                                    ) : (
                                      document.user_id || 'Émetteur inconnu'
                                    )}
                                  </td>
                                  <td className="py-3 px-4 whitespace-nowrap">{new Date(document.date_creation).toLocaleDateString('fr-FR')}</td>
                                  <td className="py-3 px-4 whitespace-nowrap">{document.total_ttc.toFixed(2)} €</td>
                                  <td className="py-3 px-4 whitespace-nowrap">
                                    <span className={`px-3 py-1.5 rounded-full text-xs font-medium min-w-[90px] text-center ${
                                      document.type === 'devis' && document.facture_origine_id ? getStatusColor('facture') : getStatusColor(document.statut)
                                    }`}>
                                      {document.type === 'devis' && document.facture_origine_id ? getStatusText('facture', document.type) : getStatusText(document.statut, document.type)}
                                      {document.type === 'devis' && document.statut === 'accepte' && !document.facture_origine_id && (
                                        <span className="ml-1 inline-block">
                                          <FileCheck size={14} className="inline-block" />
                                        </span>
                                      )}
                                    </span>
                                  </td>
                                  <td className="py-3 px-4 whitespace-nowrap text-right">
                                    <div className="flex justify-end space-x-2">
                                      <button
                                        className="inline-flex items-center justify-center p-1.5 text-indigo-600 hover:text-white hover:bg-indigo-500 rounded-full transition-colors"
                                        title="Télécharger PDF"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDownloadPDF(document, true);
                                        }}
                                      >
                                        <Download size={18} />
                                      </button>
                                      
                                      {/* Ajouter le bouton Aperçu pour les devis reçus */}
                                      {activeTab === 'recus' && (
                                        <button 
                                          className="inline-flex items-center justify-center p-1.5 text-orange-500 hover:text-white hover:bg-orange-500 rounded-full transition-colors"
                                          title="Aperçu du document"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            // Sélectionner le document pour la prévisualisation
                                            setSelectedDocument(document);
                                            // Ouvrir directement la vraie modale de prévisualisation
                                            setPreviewDocumentOpen(true);
                                          }}
                                        >
                                          <FileText size={18} />
                                        </button>
                                      )}
                                      
                                      {/* N'afficher les boutons d'édition et de duplication que pour les documents émis par l'utilisateur */}
                                      {activeTab !== 'recus' && (
                                        <>
                                          {document.statut === 'brouillon' && (
                                            <button 
                                              className="inline-flex items-center justify-center p-1.5 text-blue-600 hover:text-white hover:bg-blue-500 rounded-full transition-colors"
                                              title="Éditer"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleEditDocument(document);
                                              }}
                                            >
                                              <Edit size={18} />
                                            </button>
                                          )}
                                          
                                          <button 
                                            className="inline-flex items-center justify-center p-1.5 text-emerald-600 hover:text-white hover:bg-emerald-500 rounded-full transition-colors"
                                            title="Dupliquer"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleDuplicateDocument(document);
                                            }}
                                          >
                                            <Copy size={18} />
                                          </button>
                                          
                                          {document.statut === 'brouillon' && (
                                            <button 
                                              className="inline-flex items-center justify-center p-1.5 text-red-600 hover:text-white hover:bg-red-500 rounded-full transition-colors"
                                              title="Supprimer"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleDeleteDocument(document);
                                              }}
                                            >
                                              <Trash size={18} />
                                            </button>
                                          )}
                                        </>
                                      )}
                                      
                                      {/* Pour les devis reçus et pas encore acceptés, ajouter des boutons d'action améliorés */}
                                      {activeTab === 'recus' && document.statut !== 'accepte' && document.statut !== 'facture' && document.statut !== 'refuse' && (
                                        <>
                                          <button 
                                            className="inline-flex items-center justify-center p-1.5 bg-green-50 text-green-600 hover:text-white hover:bg-green-500 rounded-full transition-colors shadow-sm"
                                            title="Accepter le devis"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              navigate(`/quote-acceptance/${document.id}`);
                                            }}
                                          >
                                            <FileCheck size={18} />
                                          </button>
                                          <button 
                                            className="inline-flex items-center justify-center p-1.5 bg-red-50 text-red-600 hover:text-white hover:bg-red-500 rounded-full transition-colors shadow-sm"
                                            title="Refuser le devis"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              setSelectedDocument(document);
                                              setShowRejectModal(true);
                                            }}
                                          >
                                            <X size={18} />
                                          </button>
                                        </>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                            {/* Légende des boutons */}
                            {activeTab === 'recus' && filteredReceivedQuotes.length > 0 && (
                              <tfoot>
                                <tr>
                                  <td colSpan={6} className="pt-4">
                                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 min-w-full whitespace-normal">
                                      <p className="text-sm font-medium text-gray-700 mb-2">Légende des boutons :</p>
                                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                        <div className="flex items-center">
                                          <span className="inline-flex items-center justify-center p-1.5 text-indigo-600 bg-indigo-50 rounded-full mr-2">
                                            <Download size={16} />
                                          </span>
                                          <span className="text-sm text-gray-600">Télécharger PDF</span>
                                        </div>
                                        <div className="flex items-center">
                                          <span className="inline-flex items-center justify-center p-1.5 text-orange-500 bg-orange-50 rounded-full mr-2">
                                            <FileText size={16} />
                                          </span>
                                          <span className="text-sm text-gray-600">Aperçu du document</span>
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              </tfoot>
                            )}
                          </table>
                        </div>
                      )
                    ) : (
                      tabType === 'factures_recues' ? (
                        filteredReceivedInvoices.length === 0 ? (
                          <div className="text-center py-10 text-gray-500">
                            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                            <p>Aucune facture reçue trouvée</p>
                          </div>
                        ) : (
                          <div className="overflow-x-auto">
                            <table className="min-w-full bg-white rounded-lg shadow-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    De
                                  </th>
                                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                  <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                  <th className="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-200">
                                {filteredReceivedInvoices.map((document) => (
                                  <tr 
                                    key={document.id} 
                                    className={`hover:bg-gray-50 cursor-pointer ${selectedDocument?.id === document.id ? 'bg-blue-50' : ''}`}
                                    onClick={() => setSelectedDocument(document)}
                                  >
                                    <td className="py-3 px-4 whitespace-nowrap">
                                      {getDisplayNumber(document)}
                                      {isDraft(document) && (
                                        <span className="ml-2 text-xs text-gray-500 italic">(Brouillon)</span>
                                      )}
                                    </td>
                                    <td className="py-3 px-4 whitespace-nowrap">
                                      {document.sender_info ? (
                                        <div>
                                          <div>{document.sender_info.prenom} {document.sender_info.nom}</div>
                                          {document.sender_info.entreprise && (
                                            <div className="text-xs text-gray-500">{document.sender_info.entreprise}</div>
                                          )}
                                        </div>
                                      ) : (
                                        document.user_id || 'Émetteur inconnu'
                                      )}
                                    </td>
                                    <td className="py-3 px-4 whitespace-nowrap">{new Date(document.date_creation).toLocaleDateString('fr-FR')}</td>
                                    <td className="py-3 px-4 whitespace-nowrap">{document.total_ttc.toFixed(2)} €</td>
                                    <td className="py-3 px-4 whitespace-nowrap">
                                      <span className={`px-3 py-1.5 rounded-full text-xs font-medium min-w-[90px] text-center ${
                                        getStatusColor(document.statut)
                                      }`}>
                                        {getStatusText(document.statut, document.type)}
                                      </span>
                                    </td>
                                    <td className="py-3 px-4 whitespace-nowrap text-right">
                                      <div className="flex justify-end space-x-2">
                                        <button
                                          className="inline-flex items-center justify-center p-1.5 text-indigo-600 hover:text-white hover:bg-indigo-500 rounded-full transition-colors"
                                          title="Télécharger PDF"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleDownloadPDF(document, true);
                                          }}
                                        >
                                          <Download size={18} />
                                        </button>
                                        
                                        <button 
                                          className="inline-flex items-center justify-center p-1.5 text-orange-500 hover:text-white hover:bg-orange-500 rounded-full transition-colors"
                                          title="Aperçu du document"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            // Sélectionner le document pour la prévisualisation
                                            setSelectedDocument(document);
                                            // Ouvrir directement la vraie modale de prévisualisation
                                            setPreviewDocumentOpen(true);
                                          }}
                                        >
                                          <FileText size={18} />
                                        </button>
                                      </div>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                              {/* Légende des boutons */}
                              {filteredReceivedInvoices.length > 0 && (
                                <tfoot>
                                  <tr>
                                    <td colSpan={6} className="pt-4">
                                      <div className="bg-gray-50 p-3 rounded-md border border-gray-200 min-w-full whitespace-normal">
                                        <p className="text-sm font-medium text-gray-700 mb-2">Légende des boutons :</p>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                          <div className="flex items-center">
                                            <span className="inline-flex items-center justify-center p-1.5 text-indigo-600 bg-indigo-50 rounded-full mr-2">
                                              <Download size={16} />
                                            </span>
                                            <span className="text-sm text-gray-600">Télécharger PDF</span>
                                          </div>
                                          <div className="flex items-center">
                                            <span className="inline-flex items-center justify-center p-1.5 text-orange-500 bg-orange-50 rounded-full mr-2">
                                              <FileText size={16} />
                                            </span>
                                            <span className="text-sm text-gray-600">Aperçu du document</span>
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                  </tr>
                                </tfoot>
                              )}
                            </table>
                          </div>
                        )
                      ) : (
                        tabType === 'avoirs_recus' ? (
                          filteredReceivedCreditNotes.length === 0 ? (
                            <div className="text-center py-10 text-gray-500">
                              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                              <p>Aucun avoir reçu trouvé</p>
                            </div>
                          ) : (
                            <div className="overflow-x-auto">
                              <table className="min-w-full bg-white rounded-lg shadow-sm">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      De
                                    </th>
                                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                    <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                    <th className="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                  {filteredReceivedCreditNotes.map((document) => (
                                    <tr 
                                      key={document.id} 
                                      className={`hover:bg-gray-50 cursor-pointer ${selectedDocument?.id === document.id ? 'bg-blue-50' : ''}`}
                                      onClick={() => setSelectedDocument(document)}
                                    >
                                      <td className="py-3 px-4 whitespace-nowrap">
                                        {getDisplayNumber(document)}
                                        {isDraft(document) && (
                                          <span className="ml-2 text-xs text-gray-500 italic">(Brouillon)</span>
                                        )}
                                      </td>
                                      <td className="py-3 px-4 whitespace-nowrap">
                                        {document.sender_info ? (
                                          <div>
                                            <div>{document.sender_info.prenom} {document.sender_info.nom}</div>
                                            {document.sender_info.entreprise && (
                                              <div className="text-xs text-gray-500">{document.sender_info.entreprise}</div>
                                            )}
                                          </div>
                                        ) : (
                                          document.user_id || 'Émetteur inconnu'
                                        )}
                                      </td>
                                      <td className="py-3 px-4 whitespace-nowrap">{new Date(document.date_creation).toLocaleDateString('fr-FR')}</td>
                                      <td className="py-3 px-4 whitespace-nowrap">{document.total_ttc.toFixed(2)} €</td>
                                      <td className="py-3 px-4 whitespace-nowrap">
                                        <span className={`px-3 py-1.5 rounded-full text-xs font-medium min-w-[90px] text-center ${
                                          getStatusColor(document.statut)
                                        }`}>
                                          {getStatusText(document.statut, document.type)}
                                        </span>
                                      </td>
                                      <td className="py-3 px-4 whitespace-nowrap text-right">
                                        <div className="flex justify-end space-x-2">
                                          <button
                                            className="inline-flex items-center justify-center p-1.5 text-indigo-600 hover:text-white hover:bg-indigo-500 rounded-full transition-colors"
                                            title="Télécharger PDF"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleDownloadPDF(document, true);
                                            }}
                                          >
                                            <Download size={18} />
                                          </button>
                                          
                                          <button 
                                            className="inline-flex items-center justify-center p-1.5 text-orange-500 hover:text-white hover:bg-orange-500 rounded-full transition-colors"
                                            title="Aperçu du document"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              // Sélectionner le document pour la prévisualisation
                                              setSelectedDocument(document);
                                              // Ouvrir directement la vraie modale de prévisualisation
                                              setPreviewDocumentOpen(true);
                                            }}
                                          >
                                            <FileText size={18} />
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                                {/* Légende des boutons */}
                                {filteredReceivedCreditNotes.length > 0 && (
                                  <tfoot>
                                    <tr>
                                      <td colSpan={6} className="pt-4">
                                        <div className="bg-gray-50 p-3 rounded-md border border-gray-200 min-w-full whitespace-normal">
                                          <p className="text-sm font-medium text-gray-700 mb-2">Légende des boutons :</p>
                                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                            <div className="flex items-center">
                                              <span className="inline-flex items-center justify-center p-1.5 text-indigo-600 bg-indigo-50 rounded-full mr-2">
                                                <Download size={16} />
                                              </span>
                                              <span className="text-sm text-gray-600">Télécharger PDF</span>
                                            </div>
                                            <div className="flex items-center">
                                              <span className="inline-flex items-center justify-center p-1.5 text-orange-500 bg-orange-50 rounded-full mr-2">
                                                <FileText size={16} />
                                              </span>
                                              <span className="text-sm text-gray-600">Aperçu du document</span>
                                            </div>
                                          </div>
                                        </div>
                                      </td>
                                    </tr>
                                  </tfoot>
                                )}
                              </table>
                            </div>
                          )
                        ) : (
                        filteredDocuments.length === 0 ? (
                          <div className="text-center py-10 text-gray-500">
                            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                            <p>Aucun document trouvé</p>
                            <button
                              onClick={() => handleCreateDocument()}
                              className="mt-4 text-[#FF7A35] hover:text-[#ff6b2c] font-medium"
                            >
                              Créer un {tabType === 'devis' ? 'devis' : tabType === 'factures' ? 'une facture' : 'un avoir'}
                            </button>
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                            <div className="space-y-4">
                              {filteredDocuments.map((doc) => (
                                <div
                                  key={doc.id}
                                  onClick={() => setSelectedDocument(doc)}
                                  className={`bg-white rounded-lg shadow-sm border p-4 cursor-pointer transition-all hover:shadow ${
                                    selectedDocument?.id === doc.id ? 'ring-2 ring-[#FF7A35] border-transparent' : 'hover:border-[#FF7A35]/20'
                                  }`}
                                >
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <h3 className="font-semibold text-gray-800">{doc.number}</h3>
                                      <p className="text-sm text-gray-600">{doc.client_name}</p>
                                    </div>
                                    <span className={`px-3 py-1.5 rounded-full text-xs font-medium min-w-[90px] text-center ${
                                      doc.type === 'devis' && doc.facture_origine_id ? getStatusColor('facture') : getStatusColor(doc.statut)
                                    }`}>
                                      {doc.type === 'devis' && doc.facture_origine_id ? getStatusText('facture', doc.type) : getStatusText(doc.statut, doc.type)}
                                      {doc.type === 'devis' && doc.statut === 'accepte' && !doc.facture_origine_id && (
                                        <span className="ml-1 inline-block">
                                          <FileCheck size={14} className="inline-block" />
                                        </span>
                                      )}
                                    </span>
                                  </div>
                                  
                                  {doc.description && (
                                    <p className="text-sm text-gray-500 my-2 line-clamp-1">
                                      {doc.description}
                                    </p>
                                  )}
                                  
                                  <div className="flex justify-between items-end mt-3">
                                    <div className="text-sm text-gray-500">
                                      {new Date(doc.date_creation).toLocaleDateString('fr-FR')}
                                    </div>
                                    <div className="text-lg font-semibold text-[#FF7A35]">{doc.total_ttc.toFixed(2)} €</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                            
                            {selectedDocument ? (
                              <DocumentDetails 
                                document={selectedDocument}
                                onEdit={handleEditDocument}
                                onDelete={handleDeleteDocument}
                                onDuplicate={handleDuplicateDocument}
                                onDownload={handleDownloadPDF}
                                onSendEmail={() => handleOpenEmailDialog(selectedDocument)}
                                onConvertToInvoice={handleConvertToInvoice}
                                onCreateCreditNote={handleCreateCreditNote}
                                getStatusText={getStatusText}
                                getStatusColor={getStatusColor}
                                onReject={(document) => {
                                  setSelectedDocument(document);
                                  setShowRejectModal(true);
                                }}
                                onUpdateInvoiceStatus={handleUpdateInvoiceStatus}
                                isReceivedQuote={activeTab === 'recus'}
                                isReceivedInvoice={activeTab === 'factures_recues'}
                                isReceivedCreditNote={activeTab === 'avoirs_recus'}
                              />
                            ) : (
                              <div className="hidden xl:flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                                <FileText className="h-12 w-12 text-gray-400 mb-2" />
                                <p className="text-gray-500">Sélectionnez un document pour afficher les détails</p>
                              </div>
                            )}
                          </div>
                        ))
                      )
                    )}
                  </>
                )}
          </div>
        ))}  
      </div>

          {/* Formulaire de création/modification */}
          {showForm && (
            <DocumentForm
              onCancel={handleFormClose}
              onSave={handleFormSubmit as any}
              document={editingDocument}
              documentType={activeTab === 'devis' ? 'devis' : activeTab === 'factures' ? 'facture' : 'avoir'}
              clients={clients}
              onRefreshClients={refreshData}
              scrollToForm={shouldScrollToForm}
            />
          )}

          {/* Modal de confirmation de suppression */}
          <DeleteDocumentDialog
            open={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            onConfirm={confirmDelete}
            documentType={documentToDelete?.type || 'devis'}
            documentNumber={documentToDelete?.number || ''}
          />

          {/* Modal d'envoi par email */}
          {selectedDocument && (
            <ModalPortal
              isOpen={sendEmailOpen}
              onBackdropClick={closeEmailDialog}
              closeOnBackdropClick={true}
            >
              <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex flex-col md:flex-row gap-6 justify-center items-stretch">
                  {/* Zone remplaçant l'affichage du PDF avec un bouton de téléchargement */}
                  <div className="w-full md:w-1/2 flex">
                    <div className="border rounded-lg bg-gray-50 p-4 md:p-6 flex flex-col items-center justify-center w-full">
                      <div className="text-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-[#FF7A35] mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 className="text-lg font-semibold text-gray-800 mt-2">
                          {selectedDocument && selectedDocument.type === 'devis' ? 'Devis ' : 
                          selectedDocument && selectedDocument.type === 'facture' ? 'Facture ' : 'Avoir '} 
                          {selectedDocument?.number}
                        </h3>
                      </div>
                      
                      <p className="text-gray-600 text-sm text-center mb-4">
                        Chrome ne peut pas afficher le PDF pour des raisons de sécurité. Le document sera envoyé en pièce jointe.
                      </p>
                      
                      <button
                        onClick={() => {
                          if (selectedDocument) {
                            handleDownloadPDF(selectedDocument);
                          }
                        }}
                        className="w-full md:w-auto px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] flex items-center justify-center gap-2"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Télécharger
                      </button>
                    </div>
                  </div>
                  
                  {/* Formulaire d'envoi */}
                  <div className="md:w-1/2 flex flex-col justify-center items-center">
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-xl font-semibold text-gray-800">
                          Envoyer par email
                        </h3>
                        <button
                          onClick={closeEmailDialog}
                          className="p-1.5 rounded-full text-gray-500 hover:text-[#FF7A35] hover:bg-orange-50 transition-colors"
                          aria-label="Fermer"
                        >
                          <X size={18} />
                        </button>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Email du destinataire
                          </label>
                          <input
                            type="email"
                            value={emailData.email}
                            onChange={(e) => setEmailData({...emailData, email: DOMPurify.sanitize(e.target.value)})}
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF7A35]"
                            placeholder="<EMAIL>"
                            required
                          />
                          {emailData.email && (
                            <p className="text-orange-600 text-sm mt-1">
                              Limitation : Un seul email peut être envoyé à la même adresse toutes les 30 minutes.
                            </p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Message (optionnel)
                          </label>
                          <textarea
                            value={emailData.message}
                            onChange={(e) => setEmailData({...emailData, message: DOMPurify.sanitize(e.target.value)})}
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF7A35]"
                            placeholder="Message à inclure dans l'email..."
                            rows={4}
                          />
                        </div>

                        {/* Avertissement et case à cocher */}
                        {selectedDocument && selectedDocument.statut === 'brouillon' && (
                          <div className="mt-4 p-4 bg-orange-50 border-l-4 border-[#FF7A35] rounded-md">
                            <p className="text-sm text-gray-700 mb-3">
                              <strong>Avertissement :</strong> L'envoi de ce document par email changera son statut de "Brouillon" à "Envoyé". Après l'envoi, vous ne pourrez plus :
                            </p>
                            <ul className="list-disc pl-5 text-sm text-gray-700 mb-3">
                              <li>Modifier le contenu du document</li>
                              <li>Supprimer le document</li>
                              <li>Changer les informations du client</li>
                            </ul>
                            <div className="flex items-start mt-2">
                              <input
                                type="checkbox"
                                id="confirmSend"
                                className="mt-1 mr-2"
                                onChange={(e) => setEmailData({...emailData, confirmed: e.target.checked})}
                              />
                              <label htmlFor="confirmSend" className="text-sm text-gray-700">
                                J'ai compris que l'envoi de ce document est définitif et qu'il ne pourra plus être modifié ou supprimé après l'envoi.
                              </label>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-end space-x-3 pt-4 mt-4">
                      <button
                        onClick={closeEmailDialog}
                        disabled={isSendingEmail}
                        className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 ${isSendingEmail ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        Annuler
                      </button>
                      <button
                        onClick={handleSendEmail}
                        disabled={!emailData.email || (selectedDocument?.statut === 'brouillon' && !emailData.confirmed) || isSendingEmail}
                        className={`px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c] flex items-center gap-2 ${(!emailData.email || (selectedDocument?.statut === 'brouillon' && !emailData.confirmed) || isSendingEmail) ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        {isSendingEmail && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        )}
                        {isSendingEmail ? 'Envoi en cours...' : 'Envoyer'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </ModalPortal>
          )}

          {/* Modal de limite atteinte */}
          <ModalPortal
            isOpen={showLimitModal}
            onBackdropClick={() => setShowLimitModal(false)}
            closeOnBackdropClick={true}
          >
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-800">
                  Limite atteinte
                </h3>
                <button
                  onClick={() => setShowLimitModal(false)}
                  className="p-1.5 rounded-full text-gray-500 hover:text-[#FF7A35] hover:bg-orange-50 transition-colors"
                  aria-label="Fermer"
                >
                  <X size={18} />
                </button>
              </div>
              
              <div className="mb-6">
                <div className="bg-orange-50 border-l-4 border-[#FF7A35] p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Info className="h-5 w-5 text-[#FF7A35]" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-orange-700">
                        <strong>Limite d'abonnement :</strong> Votre plan actuel vous permet de créer seulement {' '}
                        {documentTypeLimit === 'devis' && subscriptionLimits?.gratuit.quotes.included} {documentTypeLimit === 'devis' ? 'devis' : ''}
                        {documentTypeLimit === 'factures' && subscriptionLimits?.gratuit.invoices.included} {documentTypeLimit === 'factures' ? 'factures' : ''}
                        {documentTypeLimit === 'avoirs' && subscriptionLimits?.gratuit.invoices.included} {documentTypeLimit === 'avoirs' ? 'avoirs' : ''}
                        .
                      </p>
                    </div>
                  </div>
                </div>
                
                <p className="text-gray-600 mb-4">
                  Pour créer plus de documents, passez à l'abonnement Premium qui vous offre un nombre illimité de documents.
                </p>
                
                <ul className="list-disc pl-5 text-gray-600 mb-4">
                  <li>Nombre illimité de devis</li>
                  <li>Nombre illimité de factures</li>
                  <li>Nombre illimité d'avoirs</li>
                  <li>Et bien d'autres avantages!</li>
                </ul>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowLimitModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={redirectToPremium}
                  className="px-4 py-2 bg-[#FF7A35] text-white rounded-md hover:bg-[#ff6b2c]"
                >
                  Passer à Premium
                </button>
              </div>
            </div>
          </ModalPortal>

          {/* Avertissement pour le stockage des documents */}
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-8">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Information importante :</strong> Les documents créés (devis, factures, avoirs) sont stockés sur nos serveurs uniquement pour faciliter leur gestion. 
                  Il est de votre responsabilité de les télécharger et de les conserver pour respecter vos obligations légales. 
                  JobPartiel ne peut être tenu responsable en cas de perte de données.
                </p>
              </div>
            </div>
          </div>

          {/* Modal de prévisualisation directe */}
          {selectedDocument && (
            <DocumentPreviewModal
              document={selectedDocument}
              isOpen={previewDocumentOpen}
              onClose={() => setPreviewDocumentOpen(false)}
              onDownload={(document) => {
                // Déterminer si c'est un document reçu
                const isReceived = activeTab === 'recus' || activeTab === 'factures_recues' || activeTab === 'avoirs_recus';
                handleDownloadPDF(document, isReceived);
              }}
              isReceivedQuote={activeTab === 'recus'}
              isReceivedInvoice={activeTab === 'factures_recues'}
              isReceivedCreditNote={activeTab === 'avoirs_recus'}
            />
          )}

          {/* Modal de refus de devis */}
          <ModalPortal
            isOpen={showRejectModal}
            onBackdropClick={() => setShowRejectModal(false)}
            closeOnBackdropClick={true}
          >
            <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-gray-800">
                  Refuser le devis
                </h3>
                <button
                  onClick={() => setShowRejectModal(false)}
                  className="p-1.5 rounded-full text-gray-500 hover:text-red-500 hover:bg-red-50 transition-colors"
                  aria-label="Fermer"
                >
                  <X size={18} />
                </button>
              </div>
              
              <div className="mb-6">
                <p className="text-gray-600 mb-4">
                  Veuillez indiquer la raison du refus. Cette information sera envoyée à l'émetteur du devis.
                </p>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Motif de refus
                    </label>
                    <select
                      value={rejectionData.reason}
                      onChange={(e) => setRejectionData({...rejectionData, reason: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">Sélectionnez un motif</option>
                      <option value="Prix trop élevé">Prix trop élevé</option>
                      <option value="Délais non compatibles">Délais non compatibles</option>
                      <option value="Prestation ne correspond pas à mes attentes">Prestation ne correspond pas à mes attentes</option>
                      <option value="J'ai trouvé une autre solution">J'ai trouvé une autre solution</option>
                      <option value="Autre">Autre</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Message (optionnel)
                    </label>
                    <textarea
                      value={rejectionData.message}
                      onChange={(e) => setRejectionData({...rejectionData, message: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Précisez votre motif de refus..."
                      rows={4}
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowRejectModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={async () => {
                    if (!selectedDocument || !rejectionData.reason) return;
                    
                    try {
                      await invoiceService.rejectQuote(selectedDocument.id, rejectionData);
                      
                      // Rafraîchir les données pour mettre à jour le statut
                      refreshData();
                      
                      notify('Devis refusé avec succès', 'success');
                      setShowRejectModal(false);
                      setRejectionData({ reason: '', message: '' });
                      refresh();
                    } catch (error: any) {
                      console.error('Erreur lors du refus du devis:', error);
                      notify(error.response?.data?.message || 'Erreur lors du refus du devis', 'error');
                    }
                  }}
                  disabled={!rejectionData.reason}
                  className={`px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 ${!rejectionData.reason ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  Confirmer le refus
                </button>
              </div>
            </div>
          </ModalPortal>
        </>
      )}
    </div>
  );
}
