import React, { useState, useEffect } from 'react';
import { Line, Transformer, Image } from 'react-konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { DrawingElement } from '../../../types/cardEditor';

interface DrawingElementProps {
  element: DrawingElement;
  isSelected: boolean;
  onSelect: () => void;
  onDragStart?: (e: KonvaEventObject<DragEvent>) => void;
  onDragMove?: (e: KonvaEventObject<DragEvent>) => void;
  onDragEnd: (e: KonvaEventObject<DragEvent>) => void;
  isEditable?: boolean;
  onContextMenu?: (e: any) => void;
}

const DrawingElementComponent: React.FC<DrawingElementProps> = ({
  element,
  isSelected,
  onSelect,
  onDragStart,
  onDragMove = () => {},
  onDragEnd = () => {},
  isEditable = true,
  onContextMenu
}) => {
  const elementRef = React.useRef<any>(null);
  const transformerRef = React.useRef<any>(null);
  const [imageElement, setImageElement] = useState<HTMLImageElement | null>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Charger l'image si c'est un résultat de gomme
  useEffect(() => {
    if (element.properties.tool === 'eraser-result' && element.properties.imageSrc) {
      const img = new window.Image();
      img.src = element.properties.imageSrc;
      img.onload = () => {
        setImageElement(img);
      };
    }
  }, [element.properties.tool, element.properties.imageSrc]);

  React.useEffect(() => {
    if (isSelected && transformerRef.current && elementRef.current) {
      // Attacher le transformer à l'élément
      transformerRef.current.nodes([elementRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const { properties } = element;

  // Gérer le survol
  const handleMouseEnter = () => {
    if (isEditable && !isSelected) {
      setIsHovered(true);
      document.body.style.cursor = 'pointer';
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    document.body.style.cursor = 'default';
  };

  // Effet de survol
  const hoverEffect = isHovered ? {
    shadowColor: 'rgba(0,0,0,0.3)',
    shadowBlur: 6,
    shadowOffset: { x: 1, y: 1 },
    shadowOpacity: 0.5
  } : {};

  // Nous n'avons plus besoin de ce rendu spécial pour le résultat de la gomme

  // Rendu pour les dessins normaux (lignes)
  return (
    <>
      <Line
        ref={elementRef}
        x={element.x}
        y={element.y}
        points={properties.points}
        stroke={properties.stroke}
        strokeWidth={properties.strokeWidth}
        tension={properties.tension}
        lineCap="round"
        lineJoin="round"
        draggable={isEditable}
        onClick={onSelect}
        onTap={onSelect}
        onDragStart={onDragStart}
        onDragMove={onDragMove}
        onDragEnd={onDragEnd}
        perfectDrawEnabled={false}
        globalCompositeOperation={
          properties.tool === 'eraser' ? 'destination-out' : 'source-over'
        }
        // Augmenter l'opacité pour les gommes pour un meilleur effacement
        opacity={properties.tool === 'eraser' ? 1 : (properties.opacity || 1)}
        onContextMenu={onContextMenu}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...(isHovered ? hoverEffect : {})}
      />
      {isSelected && isEditable && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox: any, newBox: any) => {
            // Limiter la taille minimale
            if (newBox.width < 10 || newBox.height < 10) {
              return oldBox;
            }
            return newBox;
          }}
          rotateEnabled={true}
          enabledAnchors={['middle-left', 'middle-right', 'top-center', 'bottom-center', 'top-left', 'top-right', 'bottom-left', 'bottom-right']}
          borderStroke="#0096FF"
          borderStrokeWidth={2}
          anchorFill="#FFFFFF"
          anchorStroke="#0096FF"
          anchorStrokeWidth={2}
          anchorSize={8}
        />
      )}
    </>
  );
};

export default DrawingElementComponent;
