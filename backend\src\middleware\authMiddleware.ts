/*
Voici toutes les fonctions du fichier authMiddleware.ts :

authenticateToken():
Middleware d'authentification
Vérifie le token d'accès dans les cookies
Attache les informations de l'utilisateur à la requête
Gère les erreurs d'authentification
refreshTokens():
Gère le rafraîchissement des tokens d'authentification
Vérifie le refresh token
Génère de nouveaux tokens d'accès et de refresh
Définit les nouveaux cookies avec des options sécurisées
Points clés :

Utilisation de cookies HTTP-only
Gestion sécurisée des tokens
Différenciation entre environnement de production et développement
Logs d'erreurs détaillés
*/

import { Request, Response, NextFunction } from 'express';
import { tokenService } from '../services/tokenService';
import logger from '../utils/logger';

declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        email?: string;
        userType?: string;
        [key: string]: any;
      };
    }
  }
}

export const authMiddleware = {
  authenticateToken: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Vérifier si le token est présent dans les cookies
      const token = req.cookies['access_token'];

      if (!token) {
        res.status(401).json({ 
          message: 'Accès non autorisé', 
          success: false 
        });
        return;
      }

      // Vérifier la validité du token
      const result = await tokenService.verifyToken(token, 'access');

      if (!result.isValid || !result.decoded) {
        // Si le token est invalide mais qu'un refresh token est présent, ne pas renvoyer d'erreur
        // pour permettre au frontend de tenter un rafraîchissement
        if (req.cookies['refresh_token']) {
          logger.info('Token d\'accès invalide mais refresh token présent, permettre au frontend de rafraîchir');
          res.status(401).json({ 
            message: 'Token expiré, rafraîchissement nécessaire', 
            success: false,
            shouldRefresh: true
          });
          return;
        }
        
        res.status(401).json({ 
          message: result.error || 'Token invalide ou expiré', 
          success: false 
        });
        return;
      }

      // Attacher les informations de l'utilisateur à la requête
      req.user = {
        userId: result.decoded.userId,
        email: result.decoded.email,
        userType: result.decoded.userType || 'jobbeur',
        role: result.decoded.role || 'jobutil'
      };

      next();
    } catch (error) {
      logger.error('Erreur d\'authentification:', error);
      res.status(401).json({ 
        message: 'Token invalide ou expiré', 
        success: false 
      });
      return;
    }
  },

  refreshTokens: async (req: Request, res: Response) => {
    try {
      const refreshToken = req.cookies['refresh_token'];

      if (!refreshToken) {
        res.status(401).json({ 
          message: 'Aucun refresh token trouvé', 
          success: false 
        });
        return;
      }

      // Ajouter des logs pour déboguer
      // logger.info('Tentative de rafraîchissement de token avec:', { 
      //   refreshTokenLength: refreshToken.length,
      //   refreshTokenStart: refreshToken.substring(0, 10) + '...'
      // });

      const result = await tokenService.verifyToken(refreshToken, 'refresh');

      if (!result.isValid || !result.decoded) {
        logger.warn('Échec de vérification du refresh token:', { 
          error: result.error,
          isValid: result.isValid,
          hasDecoded: !!result.decoded
        });
        
        res.status(401).json({ 
          message: result.error || 'Refresh token invalide ou expiré', 
          success: false,
          expired: true
        });
        return;
      }

      // logger.info('Refresh token valide, génération de nouveaux tokens pour:', {
      //   userId: result.decoded.userId,
      //   email: result.decoded.email
      // });

      // Ne pas blacklister l'ancien refresh token immédiatement pour éviter les problèmes
      // en cas d'échec de génération des nouveaux tokens
      
      // Générer de nouveaux tokens
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } = await tokenService.generateTokens(
        result.decoded.userId,
        result.decoded.email,
        result.decoded.userType || 'jobbeur'
      );

      // Blacklister l'ancien refresh token seulement après avoir généré les nouveaux avec succès
      await tokenService.blacklistToken(refreshToken, 'token_expired');

      // logger.info('Nouveaux tokens générés avec succès');

      // Définir les nouveaux cookies avec les bons paramètres
      res.cookie('access_token', newAccessToken, getAuthCookieOptions());

      res.cookie('refresh_token', newRefreshToken, getAuthCookieOptions());

      // Ajouter le token dans la réponse pour le frontend
      return res.json({
        success: true,
        message: 'Tokens renouvelés avec succès',
        token: newAccessToken
      });
    } catch (error) {
      logger.error('Erreur lors du rafraîchissement des tokens:', error);
      return res.status(500).json({
        message: 'Erreur lors du rafraîchissement des tokens',
        success: false
      });
    }
  },

  // Middleware pour vérifier le rôle de l'utilisateur
  checkRole: (allowedRoles: string[]) => async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        res.status(401).json({ 
          message: 'Accès non autorisé', 
          success: false 
        });
        return;
      }

      const userRole = req.user.role;
      
      if (!userRole || !allowedRoles.includes(userRole)) {
        res.status(403).json({
          message: 'Vous n\'avez pas les droits nécessaires pour effectuer cette action',
          success: false
        });
        return;
      }

      next();
    } catch (error) {
      logger.error('Erreur lors de la vérification des droits:', error);
      res.status(500).json({
        message: 'Erreur serveur lors de la vérification des droits',
        success: false
      });
      return;
    }
  }
};

// Ajouter la fonction utilitaire si elle n'existe pas déjà
function getAuthCookieOptions() {
  const isProduction = process.env.NODE_ENV === 'production';
  return {
    httpOnly: true,
    secure: isProduction,
    sameSite: isProduction ? 'none' as 'none' : 'strict' as 'strict',
    path: '/',
    ...(isProduction ? { domain: '.jobpartiel.fr' } : {})
  };
}
