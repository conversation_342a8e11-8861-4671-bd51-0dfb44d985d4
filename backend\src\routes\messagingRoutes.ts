import { Router } from 'express';
import { messagingController } from '../controllers/messagingController';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateMessageAttachments } from '../middleware/upload';
import rateLimit from 'express-rate-limit';
import { checkProfileVisibility } from '../middleware/checkProfileVisibility';

const router = Router();

// Création du rate limiter pour les messages
const messagesLimiter = rateLimit({
  windowMs: 60 * 1000, // fenêtre de 1 minute
  max: 120, // 120 requêtes par minute
  message: {
    message: "Trop de requêtes pour les messages. Veuillez réessayer dans 1 minute.",
    success: false,
    toastType: "error"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Création du rate limiter pour les messages postés
const messagesPostLimiter = rateLimit({
  windowMs: 60 * 1000, // fenêtre de 1 minute
  max: 10, // 10 requêtes par minute
  message: {
    message: "Par mesure de sécurité, il n'est pas possible d'envoyer plus de 10 messages par minute, veuillez réessayer dans quelques minutes.",
    success: false,
    toastType: "error"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour obtenir le nombre de messages non lus
router.get('/unread-count', messagesLimiter, messagingController.getUnreadMessageCount);

// Routes pour les conversations
router.post('/', messagesLimiter, checkProfileVisibility, validateMessageAttachments, messagingController.createConversation);
router.get('/', messagesLimiter, messagingController.getConversations);
router.get('/count-conversations', messagesLimiter, messagingController.getConversationCount);
router.get('/:id', messagesLimiter, messagingController.getConversation);
router.patch('/:id', messagesLimiter, messagingController.updateConversation);

// Routes pour les messages
router.post('/:conversationId/messages', messagesPostLimiter, checkProfileVisibility, validateMessageAttachments, messagingController.sendMessage); // Route pour envoyer un message
router.get('/:conversationId/messages', messagesLimiter, messagingController.getMessages); // Route pour récupérer les messages d'une conversation
router.post('/:conversationId/read', messagesLimiter, messagingController.markMessagesAsRead); // Route pour marquer les messages comme lus
router.post('/:conversationId/unread', messagesLimiter, messagingController.markConversationAsUnread); // Route pour marquer la conversation comme non lue

// Route pour supprimer un message
router.delete('/:conversationId/messages/:messageId', messagesLimiter, checkProfileVisibility, messagingController.deleteMessage);

export default router;
