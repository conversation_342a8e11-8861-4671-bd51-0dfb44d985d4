.button-container {
    display: flex;
    flex-wrap: wrap; /* Permet aux boutons de se renvoyer à la ligne */
    justify-content: center; /* Centre les boutons */
    margin-top: 16px; /* Ajoute un espace au-dessus des boutons */
}

.service-button {
    margin: 8px; /* Ajoute un espace autour des boutons */
}

/* Ajout d'un espacement vertical pour les boutons lorsqu'ils sont renvoyés à la ligne */
.button-container button {
    margin-bottom: 8px; /* Ajoute un espacement en bas des boutons */
}

/* Ajoutez ceci à ServiceManagement.css */
.icon-custom-size-service {
    width: 16px!important; /* Ajustez la largeur selon vos besoins */
    height: 16px!important; /* Ajustez la hauteur selon vos besoins */
    margin-bottom: 1px!important;
}

/* Services container pour les services en mode desktop */
.jp-services-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

/* Services container pour les services en mode desktop */
@media (min-width: 2000px) {
    .jp-services-container {
        flex-direction: row !important;
    }
}

/* Services item pour les services en mode desktop */
.jp-service-item {
    width: 100% !important;
    margin-bottom: 20px; /* Ajout d'une marge en bas pour l'espacement vertical */
}

/* Style pour le conteneur de titre et boutons */
.jp-service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

/* Styles pour mobile */
@media (max-width: 640px) {
    .jp-service-header {
        flex-direction: column-reverse;
    }

    .jp-service-header h3 {
        width: 100%;
        margin-top: 0.75rem;
        margin-bottom: 0;
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
    }

    .jp-service-header .jp-gallery-actions {
        align-self: flex-end;
        margin-bottom: 0.5rem;
    }

    /* Style pour le message de service désactivé sur mobile */
    .jp-service-item .service-disabled-overlay {
        z-index: 10;
    }

    .jp-service-item .service-disabled-message {
        top: 120px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 100%;
        text-align: center;
    }

    .jp-service-item .service-disabled-message div {
        display: inline-block;
        margin: 0 auto;
    }
}

/* Services item pour les services en mode desktop */
@media (min-width: 2000px) {
    .jp-service-item {
        flex-basis: calc(50% - 10px) !important; /* Définir la largeur de base */
        margin-right: 20px !important; /* Ajout d'une marge à droite pour l'espacement horizontal */
    }
    .jp-service-item:nth-child(2n) {
        margin-right: 0 !important; /* Supprime la marge à droite pour les éléments pairs */
    }
}