import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>ing, TextField, Button, Chip, Tabs, Tab, Box, Tooltip } from '@mui/material';
import { Star, X, ThumbsUp, ThumbsDown, Camera, Trash2 } from 'lucide-react';
import ModalPortal from './ModalPortal';
import { notify } from './Notification';
import DOMPurify from 'dompurify';
import { motion } from 'framer-motion';
import { IconButton } from '@mui/material';
import { useReviews } from '../hooks/useReviews';
import useContentModeration from '../hooks/useContentModeration';
import useImageModeration from '../hooks/useImageModeration';
import ImageModerationStatus from './ImageModerationStatus';
import RejectedImageMessage from './RejectedImageMessage';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { getCommonHeaders, getMultipartHeaders } from '@/utils/headers';
import { fetchCsrfToken } from '@/services/csrf';
import { useImageCompression } from '@/utils/imageCompressor';
import logger from '@/utils/logger';

// Qualités d'avis disponibles
const REVIEW_QUALITES = [
  // Qualités professionnelles
  'Autonome',
  'Efficace',
  'Expérimenté',
  'Méthodique',
  'Organisé',
  'Précis',
  'Professionnel',
  'Responsable',
  'Rigoureux',
  'Stratégique',
  'Structuré',
  'Vigilant',

  // Qualités relationnelles
  'Accueillant',
  'À l\'écoute',
  'Bienveillant',
  'Chaleureux',
  'Communicatif',
  'Courtois',
  'Empathique',
  'Patient',
  'Respectueux',
  'Souriant',
  'Tolérant',
  'Tranquille',

  // Qualités techniques
  'Bricoleur',
  'Créatif',
  'Débrouillard',
  'Expert',
  'Ingénieux',
  'Manuel',
  'Perfectionniste',
  'Polyvalent',
  'Pratique',
  'Qualifié',
  'Soigneux',
  'Technique',

  // Qualités de service
  'Attentif',
  'Dévoué',
  'Disponible',
  'Engagé',
  'Flexible',
  'Ponctuel',
  'Proactif',
  'Rapide',
  'Réactif',
  'Zélé',
  'Dynamique',
  'Motivé',

  // Qualités personnelles
  'Digne de confiance',
  'Enthousiaste',
  'Honnête',
  'Intègre',
  'Optimiste',
  'Persévérant',
  'Positif',
  'Transparent',
  'Fiable',
  'Sérieux',
  'Mature',
  'Équilibré'
];

// Défauts d'avis disponibles
const REVIEW_DEFAUTS = [
  // Défauts professionnels
  'Désorganisé',
  'Imprécis',
  'Inattentif',
  'Inefficace',
  'Imprévisible',
  'Négligent',
  'Non-professionnel',
  'Peu méthodique',
  'Peu rigoureux',
  'Peu expérimenté',
  'Brouillon',
  'Désordonné',

  // Défauts relationnels
  'Distant',
  'Froid',
  'Impatient',
  'Impoli',
  'Irrespectueux',
  'Mauvaise communication',
  'Peu à l\'écoute',
  'Peu empathique',
  'Trop direct',
  'Antipathique',
  'Peu aimable',
  'Désagréable',

  // Défauts techniques
  'Amateur',
  'Inexpérimenté',
  'Maladroit',
  'Peu créatif',
  'Peu débrouillard',
  'Peu doué',
  'Solutions simplistes',
  'Technique limitée',
  'Compétences insuffisantes',
  'Peu qualifié',
  'Peu soigneux',
  'Négligé',

  // Défauts de service
  'En retard',
  'Indisponible',
  'Inflexible',
  'Lent',
  'Non réactif',
  'Passif',
  'Peu motivé',
  'Peu engagé',
  'Peu fiable',
  'Peu attentif',
  'Absent',
  'Peu investi',

  // Défauts personnels
  'Imprévisible',
  'Manque d\'honnêteté',
  'Manque de fiabilité',
  'Manque de sérieux',
  'Pessimiste',
  'Peu digne de confiance',
  'Peu transparent',
  'Trop émotif',
  'Immature',
  'Instable',
  'Impulsif',
  'Opaque',
];

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  mission_id: string;
  onReviewAdded?: () => void;
  reviewToEdit?: {
    id: string;
    note: number;
    commentaire: string;
    qualites: string[];
    defauts?: string[];
    photos?: string[];
  };
}

// Remplacer le style global actuel par un style d'animation plus fluide et élégant
const GlobalStyles = () => {
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
      }

      .auto-scroll-text {
        display: flex !important;
        white-space: nowrap !important;
        width: fit-content !important;
      }

      .auto-scroll-text span {
        display: inline-block;
        padding-right: 16px;
        animation: marquee 3s linear infinite;
      }

      .auto-scroll-text span::after {
        content: attr(data-content);
        padding-left: 16px;
      }

      .MuiChip-label {
        overflow: hidden !important;
        max-width: 120px !important;
        padding: 0 !important;
      }

      @media (max-width: 768px) {
        .MuiChip-label {
          max-width: 80px !important;
        }

        .auto-scroll-text span {
          animation-duration: 3s !important;
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  return null;
};

const ReviewModal = ({ isOpen, onClose, userId, mission_id, onReviewAdded, reviewToEdit }: ReviewModalProps) => {
  // Référence aux valeurs initiales pour comparaison
  const initialFormValuesRef = useRef({
    id: reviewToEdit?.id || '',
    note: reviewToEdit?.note || null,
    commentaire: reviewToEdit?.commentaire || '',
    qualites: reviewToEdit?.qualites || [],
    defauts: reviewToEdit?.defauts || []
  });

  const [rating, setRating] = useState<number | null>(reviewToEdit?.note || null);
  const [comment, setComment] = useState(reviewToEdit?.commentaire || '');
  const [selectedQualites, setSelectedQualites] = useState<string[]>(reviewToEdit?.qualites || []);
  const [selectedDefauts, setSelectedDefauts] = useState<string[]>(reviewToEdit?.defauts || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<'qualites' | 'defauts'>('qualites');


  // États pour les photos (max 4)
  const [selectedPhotos, setSelectedPhotos] = useState<File[]>([]);
  const [moderatedPhotos, setModeratedPhotos] = useState<File[]>([]); // Fichiers validés par la modération
  const [photoPreviews, setPhotoPreviews] = useState<string[]>(reviewToEdit?.photos || []);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // États pour la modération d'images (comme PostMission)
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const [currentModerationIndex, setCurrentModerationIndex] = useState(0);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();

  // Référence pour savoir si le formulaire est en cours d'édition
  const [isEditing, setIsEditing] = useState(false);

  const { addReview, updateReview } = useReviews({ userId });
  const { validateContentSafety } = useContentModeration();
  const { moderateImage, isLoading: isModerating } = useImageModeration();

  // Détecter les modifications pour définir isEditing
  useEffect(() => {
    const currentValues = {
      note: rating,
      commentaire: comment,
      qualites: selectedQualites,
      defauts: selectedDefauts
    };

    const hasChanged =
      currentValues.note !== initialFormValuesRef.current.note ||
      currentValues.commentaire !== initialFormValuesRef.current.commentaire ||
      JSON.stringify(currentValues.qualites) !== JSON.stringify(initialFormValuesRef.current.qualites) ||
      JSON.stringify(currentValues.defauts) !== JSON.stringify(initialFormValuesRef.current.defauts);

    setIsEditing(hasChanged);
  }, [rating, comment, selectedQualites, selectedDefauts]);

  // Mettre à jour les états lorsque reviewToEdit change - mais seulement si l'utilisateur n'est pas en train d'éditer
  useEffect(() => {
    if (reviewToEdit && !isEditing) {
      // Mise à jour de la référence
      initialFormValuesRef.current = {
        id: reviewToEdit.id,
        note: reviewToEdit.note,
        commentaire: reviewToEdit.commentaire || '',
        qualites: reviewToEdit.qualites || [],
        defauts: reviewToEdit.defauts || []
      };

      // Mise à jour des états
      setRating(reviewToEdit.note);
      setComment(reviewToEdit.commentaire || '');
      setSelectedQualites(reviewToEdit.qualites || []);
      setSelectedDefauts(reviewToEdit.defauts || []);
    }
  }, [reviewToEdit, isEditing]);

  const handleQualiteClick = useCallback((qualite: string) => {
    if (selectedQualites.includes(qualite)) {
      setSelectedQualites(prev => prev.filter(q => q !== qualite));
    } else if (selectedQualites.length < 5) {
      setSelectedQualites(prev => [...prev, qualite]);
    }
  }, [selectedQualites]);

  const handleDefautClick = useCallback((defaut: string) => {
    if (selectedDefauts.includes(defaut)) {
      setSelectedDefauts(prev => prev.filter(d => d !== defaut));
    } else if (selectedDefauts.length < 5) {
      setSelectedDefauts(prev => [...prev, defaut]);
    }
  }, [selectedDefauts]);

  const handleCommentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= 1200) {
      setComment(newValue);
    }
  }, []);

  // Fonction pour modérer les images (exactement comme PostMission)
  const moderateImages = useCallback(async (): Promise<boolean> => {
    if (selectedPhotos.length === 0) {
      return true; // Pas d'images à modérer
    }

    // Réinitialiser les états de rejet
    setIsImageRejected(false);
    setRejectionDescription(undefined);
    setRejectionImprovementSuggestions(undefined);

    try {
      setIsModerationModalOpen(true);
      setCurrentModerationIndex(0);

      // Modérer chaque image
      const moderatedResults = [];
      for (let i = 0; i < selectedPhotos.length; i++) {
        const file = selectedPhotos[i];
        setCurrentModerationIndex(i);
        setModerationPreviewUrl(URL.createObjectURL(file));

        // Modérer l'image
        try {
          const result = await moderateImage(file, 'review', `review-photo-${i}`);

          if (!result.isSafe) {
            // Mettre à jour les états pour afficher le message de rejet
            setIsImageRejected(true);
            setRejectionDescription(result.description);
            setRejectionImprovementSuggestions(result.improvementSuggestions);

            // Garder la modale ouverte pour afficher le message détaillé
            // Ne pas fermer la modale ici

            notify(`L'image ${i + 1} ne respecte pas nos règles de modération.`, 'error');

            // Afficher un message plus détaillé dans la console pour le débogage
            console.info('Image refusée par la modération', {
              description: result.description,
              contentType: 'review',
              imageIndex: i
            });

            return false;
          }

          moderatedResults.push(file);
        } catch (error) {
          console.error('Erreur lors de la modération de l\'image:', error);
          notify(`Erreur lors de la vérification de l'image ${i + 1}. Veuillez réessayer.`, 'error');
          setIsModerationModalOpen(false);
          setIsImageRejected(false);
          setRejectionDescription(undefined);
          return false;
        }
      }

      // Toutes les images ont été modérées avec succès
      setModeratedPhotos(moderatedResults);
      setIsModerationModalOpen(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      return true;
    } catch (error) {
      console.error('Erreur lors de la modération des images:', error);
      notify('Une erreur est survenue lors de la vérification des images', 'error');
      setIsModerationModalOpen(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      return false;
    }
  }, [selectedPhotos, moderateImage]);

  // Gestion de la sélection de photos (sans modération immédiate)
  const handlePhotosSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Vérifier le nombre total de photos (existantes + nouvelles)
    const totalPhotos = photoPreviews.length + files.length;
    if (totalPhotos > 4) {
      notify(`Maximum 4 photos autorisées. Vous avez déjà ${photoPreviews.length} photo(s).`, 'error');
      return;
    }

    // Valider chaque fichier
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (!file.type.startsWith('image/')) {
        notify(`Le fichier ${i + 1} n'est pas une image`, 'error');
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        notify(`Le fichier ${i + 1} dépasse la taille maximale de 5MB`, 'error');
        return;
      }
    }

    // Ajouter les fichiers et créer les aperçus
    const newPreviews: string[] = [];
    files.forEach(file => {
      const previewUrl = URL.createObjectURL(file);
      newPreviews.push(previewUrl);
    });

    setSelectedPhotos(prev => [...prev, ...files]);
    setPhotoPreviews(prev => [...prev, ...newPreviews]);
    // Réinitialiser les fichiers modérés car on a ajouté de nouvelles photos
    setModeratedPhotos([]);
    notify(`${files.length} photo(s) ajoutée(s). Elles seront modérées lors de l'envoi de l'avis.`, 'info');

    // Réinitialiser l'input file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [photoPreviews.length]);

  const handlePhotoRemove = useCallback((index: number) => {
    // Si c'est une photo existante (URL), la marquer pour suppression
    if (index < (reviewToEdit?.photos?.length || 0)) {
      setPhotoPreviews(prev => prev.filter((_, i) => i !== index));
    } else {
      // Si c'est une nouvelle photo (File), la retirer des sélectionnées ET des modérées
      const fileIndex = index - (reviewToEdit?.photos?.length || 0);
      setSelectedPhotos(prev => prev.filter((_, i) => i !== fileIndex));
      setModeratedPhotos(prev => prev.filter((_, i) => i !== fileIndex));
      setPhotoPreviews(prev => prev.filter((_, i) => i !== index));
    }
  }, [reviewToEdit?.photos?.length]);

  const handleRemoveAllPhotos = useCallback(() => {
    setSelectedPhotos([]);
    setPhotoPreviews([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const uploadPhotos = useCallback(async (reviewId: string) => {
    // Utiliser les fichiers modérés s'ils existent, sinon les fichiers sélectionnés
    const filesToUpload = moderatedPhotos.length > 0 ? moderatedPhotos : selectedPhotos;

    if (filesToUpload.length === 0) return [];

    setIsUploadingPhotos(true);
    try {
      await fetchCsrfToken();
      const headers = await getMultipartHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const { compressGalleryPhoto } = useImageCompression();

      // Log des types MIME avant validation
      filesToUpload.forEach((file, index) => {
        logger.info(`Type MIME du fichier ${index + 1} avant validation:`, {
          fileName: file.name,
          type: file.type,
          size: file.size
        });
      });

      // Vérifier les types de fichiers avant la compression
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      const validFiles = filesToUpload.filter(file => {
        if (!allowedTypes.includes(file.type)) {
          logger.error(`Type MIME non autorisé pour ${file.name}:`, { type: file.type });
          notify(`Le fichier ${file.name} n'est pas un format d'image valide. Formats acceptés : JPG, PNG, WEBP`, 'error');
          return false;
        }
        if (file.size > 5 * 1024 * 1024) { // 5MB
          logger.error(`Fichier trop volumineux: ${file.name}`, { size: file.size });
          notify(`Le fichier ${file.name} dépasse la taille maximale de 5MB`, 'error');
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) {
        throw new Error('Aucun fichier valide à uploader');
      }

      // Log des fichiers valides avant compression
      logger.info('Fichiers valides avant compression:', validFiles.map(f => ({
        name: f.name,
        type: f.type,
        size: f.size
      })));

      const compressedFiles = await Promise.all(
        validFiles.map(async (file) => {
          const compressed = await compressGalleryPhoto(file);
          logger.info(`Fichier compressé: ${file.name}`, {
            originalType: file.type,
            compressedType: compressed.type,
            originalSize: file.size,
            compressedSize: compressed.size
          });
          return compressed;
        })
      );

      const formData = new FormData();
      compressedFiles.forEach((file, index) => {
        formData.append('photos', file);
        logger.info(`Ajout au FormData: fichier ${index + 1}`, {
          name: file.name,
          type: file.type,
          size: file.size
        });
      });

      logger.info(`Upload de ${compressedFiles.length} photo(s) compressée(s) pour l'avis ${reviewId}`);

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}/photo`,
        formData,
        {
          headers,
          withCredentials: true,
        }
      );

      if (response.data.success) {
        notify(response.data.message || 'Photos uploadées avec succès', 'success');
        return response.data.photos || [];
      } else {
        throw new Error(response.data.message || 'Erreur lors de l\'upload des photos');
      }
    } catch (error: any) {
      logger.error('Erreur lors de l\'upload des photos:', error);
      if (error.response?.data?.message) {
        notify(error.response.data.message, 'error');
      } else {
        notify('Erreur lors de l\'upload des photos', 'error');
      }
    } finally {
      setIsUploadingPhotos(false);
    }
    return [];
  }, [moderatedPhotos, selectedPhotos]);

  const deletePhotos = useCallback(async (reviewId: string, photoUrls: string[]) => {
    if (photoUrls.length === 0) return;

    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      await axios.delete(
        `${API_CONFIG.baseURL}/api/reviews/${reviewId}/photo`,
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          data: { photoUrls },
          withCredentials: true,
        }
      );
    } catch (error: any) {
      console.error('Erreur lors de la suppression des photos:', error);
      notify('Erreur lors de la suppression des photos', 'error');
    }
  }, []);

  const handleClose = useCallback(() => {
    // Réinitialiser isEditing lors de la fermeture
    setIsEditing(false);
    // Réinitialiser les états de photos
    setSelectedPhotos([]);
    setPhotoPreviews(reviewToEdit?.photos || []);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  }, [onClose, reviewToEdit?.photos]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    if (!rating) {
      setError('Veuillez sélectionner une note');
      setIsSubmitting(false);
      return;
    }

    try {
      // 1. Modérer les photos AVANT tout le reste
      if (selectedPhotos.length > 0) {
        const photosAreValid = await moderateImages();
        if (!photosAreValid) {
          setIsSubmitting(false);
          return;
        }
      }

      // 2. Vérifier le contenu du commentaire avec la modération
      if (comment.trim()) {
        const sanitizedComment = DOMPurify.sanitize(comment);
        const isCommentSafe = await validateContentSafety(sanitizedComment, 'review');

        if (!isCommentSafe) {
          // La notification est déjà gérée par validateContentSafety
          setIsSubmitting(false);
          return;
        }
      }

      const data = {
        note: rating,
        commentaire: DOMPurify.sanitize(comment),
        qualites: selectedQualites,
        defauts: selectedDefauts,
        mission_id: mission_id
      };

      let success = false;
      let reviewId = null;

      if (reviewToEdit) {
        // Modification d'un avis existant
        success = await updateReview(reviewToEdit.id, data);
        reviewId = reviewToEdit.id;
      } else {
        // Création d'un nouvel avis
        const result = await addReview(data);
        success = !!result;
        reviewId = result?.id;
      }

      // Gérer l'upload de photos si un avis a été créé/modifié avec succès
      if (success && reviewId) {
        // Si de nouvelles photos ont été sélectionnées, les uploader
        if (selectedPhotos.length > 0) {
          await uploadPhotos(reviewId);
        }

        // Si on est en mode édition, gérer les photos supprimées
        if (isEditing && reviewToEdit?.photos) {
          const originalPhotos = reviewToEdit.photos;
          const currentExistingPhotos = photoPreviews.filter(url => originalPhotos.includes(url));
          const deletedPhotos = originalPhotos.filter(photo => !currentExistingPhotos.includes(photo));

          if (deletedPhotos.length > 0) {
            await deletePhotos(reviewId, deletedPhotos);
          }
        }
      }

      if (success) {
        // Réinitialiser isEditing avant la fermeture
        setIsEditing(false);
        onClose();
        if (onReviewAdded) {
          onReviewAdded();
        }
        // Réinitialiser les états seulement après fermeture et callback
        setRating(null);
        setComment('');
        setSelectedQualites([]);
        setSelectedDefauts([]);
        setSelectedPhotos([]);
        setPhotoPreviews([]);
        setError(null);
      }
    } catch (error: any) {
      setError(error.response?.data?.error || 'Une erreur est survenue lors de l\'envoi de l\'avis');
      notify(error.response?.data?.message || 'Une erreur est survenue lors de l\'envoi de l\'avis', "error");
    } finally {
      setIsSubmitting(false);
    }
  }, [rating, comment, selectedQualites, selectedDefauts, mission_id, reviewToEdit, updateReview, addReview, onClose, onReviewAdded, validateContentSafety]);

  return (
    <>
      <ModalPortal isOpen={isOpen}>
        <GlobalStyles />
        <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-full max-w-[600px] relative max-h-[90vh] flex flex-col"
      >
        {/* En-tête fixe */}
        <div className="flex items-center justify-between p-8 pb-4 border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] rounded-xl shadow-sm">
              <Star className="h-7 w-7 text-[#FF6B2C]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">{reviewToEdit ? 'Modifier l\'avis' : 'Déposer un avis'}</h2>
              <p className="text-sm text-gray-500 mt-1">{reviewToEdit ? 'Modifiez votre avis sur ce jobbeur' : 'Partagez votre expérience avec ce jobbeur'}</p>
            </div>
          </div>
          <IconButton
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 rounded-full"
          >
            <X size={24} />
          </IconButton>
        </div>

        {/* Contenu scrollable */}
        <div className="flex-1 overflow-y-auto p-8 pt-4">
          {/* Note globale */}
          <div className="mb-12 bg-gray-50 rounded-2xl p-8">
            <label className="block text-lg font-semibold text-gray-800 mb-6 text-center">
              Note globale
            </label>
            <div className="flex flex-col items-center space-y-8">
              <div className="relative">
                <div className="relative">
                  <Rating
                    value={rating}
                    onChange={(_, newValue) => setRating(newValue || 0)}
                    size="large"
                    sx={{
                      '& .MuiRating-icon': {
                        fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
                        margin: { xs: '0 4px', sm: '0 8px', md: '0 12px' },
                      },
                      '& .MuiRating-iconFilled': {
                        color: '#FF6B2C',
                        transform: 'scale(1.1)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        filter: 'drop-shadow(0 2px 4px rgba(255,107,44,0.3))',
                        animation: 'starPulse 1.5s ease-in-out infinite',
                        '@keyframes starPulse': {
                          '0%': {
                            transform: 'scale(1) rotate(0deg)',
                            filter: 'drop-shadow(0 2px 4px rgba(255,107,44,0.3))',
                          },
                          '25%': {
                            transform: 'scale(1.3) rotate(15deg)',
                            filter: 'drop-shadow(0 4px 8px rgba(255,107,44,0.5))',
                          },
                          '50%': {
                            transform: 'scale(1.4) rotate(-15deg)',
                            filter: 'drop-shadow(0 6px 12px rgba(255,107,44,0.6))',
                          },
                          '75%': {
                            transform: 'scale(1.3) rotate(15deg)',
                            filter: 'drop-shadow(0 4px 8px rgba(255,107,44,0.5))',
                          },
                          '100%': {
                            transform: 'scale(1) rotate(0deg)',
                            filter: 'drop-shadow(0 2px 4px rgba(255,107,44,0.3))',
                          },
                        },
                      },
                      '& .MuiRating-iconHover': {
                        color: '#FF965E',
                        transform: 'scale(1.3) rotate(15deg)',
                        filter: 'drop-shadow(0 4px 8px rgba(255,107,44,0.4))',
                      },
                      '& .MuiRating-iconEmpty': {
                        color: '#E5E7EB',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          color: '#FFE4BA',
                          transform: 'scale(1.1)',
                        },
                      },
                    }}
                  />
                </div>
              </div>
              <div className="flex items-center justify-center space-x-4">
                {rating !== null && rating > 0 && (
                  <motion.span
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{
                      scale: 1,
                      opacity: 1
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: 0.1
                    }}
                    className="text-5xl font-bold bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] bg-clip-text text-transparent"
                  >
                    {rating}/5
                  </motion.span>
                )}
                {rating !== null && rating > 0 && (
                  <motion.div
                    initial={{ opacity: 0, x: 10 }}
                    animate={{
                      opacity: 1,
                      x: 0
                    }}
                    transition={{
                      duration: 0.3,
                      delay: 0.2
                    }}
                    className="bg-white px-4 py-2 rounded-full shadow-md text-base font-medium text-gray-700"
                  >
                    {rating === 5 ? '⭐ Excellent' :
                     rating === 4 ? '✨ Très bien' :
                     rating === 3 ? '👍 Bien' :
                     rating === 2 ? '😐 Moyen' :
                     rating === 1 ? '👎 Insuffisant' : ''}
                  </motion.div>
                )}
              </div>
            </div>
          </div>

          {/* Caractéristiques */}
          {rating !== null && rating > 0 && (
            <>
              <div className="mb-8">
                <div className="flex items-center justify-between mb-6">
                  <p className="text-base font-medium text-gray-700">
                    Sélectionnez jusqu'à 5 caractéristiques par catégorie (non obligatoire)
                  </p>
                  <span className="text-sm text-gray-500">
                    {tabValue === 'qualites' ? `${selectedQualites.length}/5` : `${selectedDefauts.length}/5`} sélectionnés
                  </span>
                </div>

                {/* Tabs for switching between qualités and défauts */}
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
                  <Tabs
                    value={tabValue}
                    onChange={(_, newValue) => setTabValue(newValue)}
                    aria-label="caractéristiques tabs"
                    sx={{
                      '& .MuiTabs-indicator': {
                        backgroundColor: '#FF6B2C',
                      },
                      '& .MuiTab-root': {
                        fontWeight: 600,
                        textTransform: 'none',
                        fontSize: '0.95rem',
                        minHeight: '60px',
                        '&.Mui-selected': {
                          color: '#FF6B2C',
                        },
                      },
                    }}
                  >
                    <Tab
                      label={
                        <div className="flex items-center space-x-2">
                          <ThumbsUp size={16} />
                          <span>Qualités ({selectedQualites.length})</span>
                        </div>
                      }
                      value="qualites"
                    />
                    <Tab
                      label={
                        <div className="flex items-center space-x-2">
                          <ThumbsDown size={16} />
                          <span>Points à améliorer ({selectedDefauts.length})</span>
                        </div>
                      }
                      value="defauts"
                    />
                  </Tabs>
                </Box>

                {tabValue === 'qualites' && (
                  <>
                    {/* Qualités professionnelles */}
                    <div className="mb-8 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-[#FF6B2C] bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-[#FF6B2C] text-sm font-semibold">1</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Qualités professionnelles</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_QUALITES.slice(0, 12).map((qualite) => (
                          <Tooltip title={qualite} key={qualite} placement="top" arrow>
                            <Chip
                              label={qualite.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={qualite}>{qualite}</span>
                                </span>
                                : qualite}
                              onClick={() => handleQualiteClick(qualite)}
                              color={selectedQualites.includes(qualite) ? 'primary' : 'default'}
                              disabled={selectedQualites.length >= 5 && !selectedQualites.includes(qualite)}
                              sx={{
                                backgroundColor: selectedQualites.includes(qualite)
                                  ? '#FF6B2C'
                                  : selectedQualites.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedQualites.includes(qualite)
                                  ? 'white'
                                  : selectedQualites.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 0.5 : 1,
                                cursor: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedQualites.includes(qualite)
                                    ? '#FF965E'
                                    : selectedQualites.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Qualités relationnelles */}
                    <div className="mb-8 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-[#FF6B2C] bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-[#FF6B2C] text-sm font-semibold">2</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Qualités relationnelles</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_QUALITES.slice(12, 24).map((qualite) => (
                          <Tooltip title={qualite} key={qualite} placement="top" arrow>
                            <Chip
                              label={qualite.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={qualite}>{qualite}</span>
                                </span>
                                : qualite}
                              onClick={() => handleQualiteClick(qualite)}
                              color={selectedQualites.includes(qualite) ? 'primary' : 'default'}
                              disabled={selectedQualites.length >= 5 && !selectedQualites.includes(qualite)}
                              sx={{
                                backgroundColor: selectedQualites.includes(qualite)
                                  ? '#FF6B2C'
                                  : selectedQualites.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedQualites.includes(qualite)
                                  ? 'white'
                                  : selectedQualites.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 0.5 : 1,
                                cursor: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedQualites.includes(qualite)
                                    ? '#FF965E'
                                    : selectedQualites.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Qualités techniques */}
                    <div className="mb-8 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-[#FF6B2C] bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-[#FF6B2C] text-sm font-semibold">3</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Qualités techniques</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_QUALITES.slice(24, 36).map((qualite) => (
                          <Tooltip title={qualite} key={qualite} placement="top" arrow>
                            <Chip
                              label={qualite.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={qualite}>{qualite}</span>
                                </span>
                                : qualite}
                              onClick={() => handleQualiteClick(qualite)}
                              color={selectedQualites.includes(qualite) ? 'primary' : 'default'}
                              disabled={selectedQualites.length >= 5 && !selectedQualites.includes(qualite)}
                              sx={{
                                backgroundColor: selectedQualites.includes(qualite)
                                  ? '#FF6B2C'
                                  : selectedQualites.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedQualites.includes(qualite)
                                  ? 'white'
                                  : selectedQualites.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 0.5 : 1,
                                cursor: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedQualites.includes(qualite)
                                    ? '#FF965E'
                                    : selectedQualites.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Qualités de service */}
                    <div className="mb-8 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-[#FF6B2C] bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-[#FF6B2C] text-sm font-semibold">4</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Qualités de service</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_QUALITES.slice(36, 48).map((qualite) => (
                          <Tooltip title={qualite} key={qualite} placement="top" arrow>
                            <Chip
                              label={qualite.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={qualite}>{qualite}</span>
                                </span>
                                : qualite}
                              onClick={() => handleQualiteClick(qualite)}
                              color={selectedQualites.includes(qualite) ? 'primary' : 'default'}
                              disabled={selectedQualites.length >= 5 && !selectedQualites.includes(qualite)}
                              sx={{
                                backgroundColor: selectedQualites.includes(qualite)
                                  ? '#FF6B2C'
                                  : selectedQualites.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedQualites.includes(qualite)
                                  ? 'white'
                                  : selectedQualites.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 0.5 : 1,
                                cursor: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedQualites.includes(qualite)
                                    ? '#FF965E'
                                    : selectedQualites.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Qualités personnelles */}
                    <div className="mb-8 bg-gray-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-[#FF6B2C] bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-[#FF6B2C] text-sm font-semibold">5</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Qualités personnelles</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_QUALITES.slice(48, 60).map((qualite) => (
                          <Tooltip title={qualite} key={qualite} placement="top" arrow>
                            <Chip
                              label={qualite.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={qualite}>{qualite}</span>
                                </span>
                                : qualite}
                              onClick={() => handleQualiteClick(qualite)}
                              color={selectedQualites.includes(qualite) ? 'primary' : 'default'}
                              disabled={selectedQualites.length >= 5 && !selectedQualites.includes(qualite)}
                              sx={{
                                backgroundColor: selectedQualites.includes(qualite)
                                  ? '#FF6B2C'
                                  : selectedQualites.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedQualites.includes(qualite)
                                  ? 'white'
                                  : selectedQualites.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 0.5 : 1,
                                cursor: selectedQualites.length >= 5 && !selectedQualites.includes(qualite) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedQualites.includes(qualite)
                                    ? '#FF965E'
                                    : selectedQualites.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedQualites.length >= 5 && !selectedQualites.includes(qualite)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {tabValue === 'defauts' && (
                  <>
                    {/* Défauts professionnels */}
                    <div className="mb-8 bg-rose-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-rose-500 bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-rose-500 text-sm font-semibold">1</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Points professionnels à améliorer</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_DEFAUTS.slice(0, 12).map((defaut) => (
                          <Tooltip title={defaut} key={defaut} placement="top" arrow>
                            <Chip
                              label={defaut.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={defaut}>{defaut}</span>
                                </span>
                                : defaut}
                              onClick={() => handleDefautClick(defaut)}
                              color={selectedDefauts.includes(defaut) ? 'error' : 'default'}
                              disabled={selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)}
                              sx={{
                                backgroundColor: selectedDefauts.includes(defaut)
                                  ? '#F43F5E'
                                  : selectedDefauts.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedDefauts.includes(defaut)
                                  ? 'white'
                                  : selectedDefauts.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 0.5 : 1,
                                cursor: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedDefauts.includes(defaut)
                                    ? '#E11D48'
                                    : selectedDefauts.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Défauts relationnels */}
                    <div className="mb-8 bg-rose-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-rose-500 bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-rose-500 text-sm font-semibold">2</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Points relationnels à améliorer</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_DEFAUTS.slice(12, 24).map((defaut) => (
                          <Tooltip title={defaut} key={defaut} placement="top" arrow>
                            <Chip
                              label={defaut.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={defaut}>{defaut}</span>
                                </span>
                                : defaut}
                              onClick={() => handleDefautClick(defaut)}
                              color={selectedDefauts.includes(defaut) ? 'error' : 'default'}
                              disabled={selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)}
                              sx={{
                                backgroundColor: selectedDefauts.includes(defaut)
                                  ? '#F43F5E'
                                  : selectedDefauts.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedDefauts.includes(defaut)
                                  ? 'white'
                                  : selectedDefauts.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 0.5 : 1,
                                cursor: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedDefauts.includes(defaut)
                                    ? '#E11D48'
                                    : selectedDefauts.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Défauts techniques */}
                    <div className="mb-8 bg-rose-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-rose-500 bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-rose-500 text-sm font-semibold">3</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Points techniques à améliorer</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_DEFAUTS.slice(24, 36).map((defaut) => (
                          <Tooltip title={defaut} key={defaut} placement="top" arrow>
                            <Chip
                              label={defaut.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={defaut}>{defaut}</span>
                                </span>
                                : defaut}
                              onClick={() => handleDefautClick(defaut)}
                              color={selectedDefauts.includes(defaut) ? 'error' : 'default'}
                              disabled={selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)}
                              sx={{
                                backgroundColor: selectedDefauts.includes(defaut)
                                  ? '#F43F5E'
                                  : selectedDefauts.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedDefauts.includes(defaut)
                                  ? 'white'
                                  : selectedDefauts.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 0.5 : 1,
                                cursor: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedDefauts.includes(defaut)
                                    ? '#E11D48'
                                    : selectedDefauts.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Défauts de service */}
                    <div className="mb-8 bg-rose-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-rose-500 bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-rose-500 text-sm font-semibold">4</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Points de service à améliorer</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_DEFAUTS.slice(36, 48).map((defaut) => (
                          <Tooltip title={defaut} key={defaut} placement="top" arrow>
                            <Chip
                              label={defaut.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={defaut}>{defaut}</span>
                                </span>
                                : defaut}
                              onClick={() => handleDefautClick(defaut)}
                              color={selectedDefauts.includes(defaut) ? 'error' : 'default'}
                              disabled={selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)}
                              sx={{
                                backgroundColor: selectedDefauts.includes(defaut)
                                  ? '#F43F5E'
                                  : selectedDefauts.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedDefauts.includes(defaut)
                                  ? 'white'
                                  : selectedDefauts.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 0.5 : 1,
                                cursor: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedDefauts.includes(defaut)
                                    ? '#E11D48'
                                    : selectedDefauts.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>

                    {/* Défauts personnels */}
                    <div className="mb-8 bg-rose-50 rounded-xl p-4">
                      <div className="flex items-center mb-4">
                        <div className="w-8 h-8 bg-rose-500 bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-rose-500 text-sm font-semibold">5</span>
                        </div>
                        <h3 className="text-base font-semibold text-gray-700">Points personnels à améliorer</h3>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {REVIEW_DEFAUTS.slice(48, 60).map((defaut) => (
                          <Tooltip title={defaut} key={defaut} placement="top" arrow>
                            <Chip
                              label={defaut.length > 13 ?
                                <span className="auto-scroll-text">
                                  <span data-content={defaut}>{defaut}</span>
                                </span>
                                : defaut}
                              onClick={() => handleDefautClick(defaut)}
                              color={selectedDefauts.includes(defaut) ? 'error' : 'default'}
                              disabled={selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)}
                              sx={{
                                backgroundColor: selectedDefauts.includes(defaut)
                                  ? '#F43F5E'
                                  : selectedDefauts.length >= 5
                                    ? '#F3F4F6'
                                    : '#F3F4F6',
                                color: selectedDefauts.includes(defaut)
                                  ? 'white'
                                  : selectedDefauts.length >= 5
                                    ? '#9CA3AF'
                                    : '#4B5563',
                                fontWeight: 500,
                                padding: '8px 16px',
                                borderRadius: '12px',
                                transition: 'all 0.2s',
                                opacity: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 0.5 : 1,
                                cursor: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut) ? 'not-allowed' : 'pointer',
                                '& .MuiChip-label': {
                                  padding: 0,
                                  overflow: 'hidden'
                                },
                                '&:hover': {
                                  backgroundColor: selectedDefauts.includes(defaut)
                                    ? '#E11D48'
                                    : selectedDefauts.length >= 5
                                      ? '#F3F4F6'
                                      : '#E5E7EB',
                                  transform: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : 'translateY(-2px)',
                                  boxShadow: selectedDefauts.length >= 5 && !selectedDefauts.includes(defaut)
                                    ? 'none'
                                    : '0 4px 12px rgba(0,0,0,0.1)'
                                },
                              }}
                            />
                          </Tooltip>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Commentaire */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-base font-medium text-gray-700">Votre commentaire</p>
                  <span className="text-sm text-gray-500">
                    {comment.length}/1200 caractères
                  </span>
                </div>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Partagez votre expérience (optionnel)"
                  value={comment}
                  onChange={handleCommentChange}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      backgroundColor: '#F9FAFB',
                      transition: 'all 0.2s',
                      '&:hover': {
                        backgroundColor: '#F3F4F6',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused': {
                        backgroundColor: 'white',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                        borderWidth: '2px',
                      },
                    },
                    '& .MuiInputBase-input': {
                      padding: '16px',
                      fontSize: '0.95rem',
                    },
                  }}
                  error={comment.length >= 1200}
                  helperText={comment.length >= 1200 ? "Le commentaire ne peut pas dépasser 1200 caractères" : ""}
                />
              </div>

              {/* Section photos */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Camera className="w-5 h-5 text-[#FF6B2C] mr-2" />
                    <p className="text-base font-medium text-gray-700">
                      Ajouter des photos (optionnel) - {photoPreviews.length}/4
                    </p>
                  </div>
                  {photoPreviews.length > 0 && (
                    <button
                      type="button"
                      onClick={handleRemoveAllPhotos}
                      className="text-red-500 hover:text-red-700 text-sm font-medium"
                    >
                      Tout supprimer
                    </button>
                  )}
                </div>

                {/* Grille des photos */}
                {photoPreviews.length > 0 && (
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {photoPreviews.map((preview, index) => (
                      <div key={index} className="relative">
                        <img
                          src={preview}
                          alt={`Aperçu ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border-2 border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => handlePhotoRemove(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Zone d'upload */}
                {photoPreviews.length < 4 && (
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-[#FF6B2C] transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handlePhotosSelect}
                      className="hidden"
                    />
                    <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">
                      {photoPreviews.length === 0 ? 'Ajoutez des photos pour illustrer votre avis' : 'Ajoutez plus de photos'}
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      JPG, PNG - 5MB maximum - {4 - photoPreviews.length} photo(s) restante(s)
                    </p>
                    <Button
                      variant="outlined"
                      onClick={() => fileInputRef.current?.click()}
                      sx={{
                        borderColor: '#FF6B2C',
                        color: '#FF6B2C',
                        borderRadius: '12px',
                        padding: '8px 24px',
                        fontWeight: 500,
                        '&:hover': {
                          borderColor: '#FF965E',
                          backgroundColor: '#FFF8F3',
                        },
                      }}
                    >
                      {photoPreviews.length === 0 ? 'Choisir des photos' : 'Ajouter des photos'}
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Boutons fixes en bas */}
        <div className="flex justify-end space-x-4 p-8 pt-4 border-t border-gray-100 bg-white rounded-b-2xl">
          <Button
            onClick={handleClose}
            variant="text"
            sx={{
              color: '#6B7280',
              padding: '10px 24px',
              borderRadius: '12px',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: '#F3F4F6',
                color: '#374151',
              },
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!rating || isSubmitting || isModerating || isUploadingPhotos}
            sx={{
              backgroundColor: '#FF6B2C',
              padding: '10px 24px',
              borderRadius: '12px',
              fontWeight: 500,
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: '#FF965E',
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(255,107,44,0.3)',
              },
              '&.Mui-disabled': {
                backgroundColor: '#E5E7EB',
                color: '#9CA3AF',
              },
            }}
          >
            {isUploadingPhotos ? 'Upload des photos...' :
             isModerating ? 'Modération en cours...' :
             isSubmitting ? 'Envoi en cours...' : 'Envoyer'}
          </Button>
        </div>
      </motion.div>
    </ModalPortal>

    {/* Modal de modération d'images (exactement comme PostMission) */}
    {isModerationModalOpen && (
      <ModalPortal isOpen={isModerationModalOpen}>
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black/50" />
          <div className="relative bg-white rounded-2xl shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                Modération des images
              </h3>
              <button
                onClick={() => {
                  setIsModerationModalOpen(false);
                  if (moderationPreviewUrl) {
                    URL.revokeObjectURL(moderationPreviewUrl);
                    setModerationPreviewUrl(null);
                  }
                  setIsImageRejected(false);
                  setRejectionDescription(undefined);
                }}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
              {isModerating ? (
                <ImageModerationStatus
                  isLoading={true}
                  imageUrl={moderationPreviewUrl || undefined}
                  title={`Modération de l'image ${currentModerationIndex + 1}/${selectedPhotos.length}`}
                  onCancel={() => {
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                />
              ) : isImageRejected ? (
                <div className="p-6">
                  {/* Afficher l'image refusée */}
                  {moderationPreviewUrl && (
                    <div className="mb-6 flex justify-center">
                      <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                        <img
                          src={moderationPreviewUrl}
                          alt="Image refusée"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-red-900/20"></div>
                      </div>
                    </div>
                  )}

                  {/* Message de rejet détaillé */}
                  <RejectedImageMessage
                    contentType="review"
                    description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                    improvementSuggestions={rejectionImprovementSuggestions}
                    variant="detailed"
                  />

                  {/* Bouton pour réessayer */}
                  <div className="mt-6 flex justify-center">
                    <button
                      onClick={() => {
                        setIsModerationModalOpen(false);
                        if (moderationPreviewUrl) {
                          URL.revokeObjectURL(moderationPreviewUrl);
                          setModerationPreviewUrl(null);
                        }
                        setIsImageRejected(false);
                        setRejectionDescription(undefined);
                        setRejectionImprovementSuggestions(undefined);

                        // Ouvrir automatiquement le sélecteur de fichier
                        setTimeout(() => {
                          if (fileInputRef.current) {
                            fileInputRef.current.click();
                          }
                        }, 300);
                      }}
                      className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                    >
                      Choisir une autre image
                    </button>
                  </div>
                </div>
              ) : (
                <ImageModerationStatus
                  isLoading={false}
                  imageUrl={moderationPreviewUrl || undefined}
                  title={`Modération de l'image ${currentModerationIndex + 1}/${selectedPhotos.length}`}
                  onCancel={() => {
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                    setRejectionImprovementSuggestions(undefined);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </ModalPortal>
    )}
    </>
  );
};



export default ReviewModal;