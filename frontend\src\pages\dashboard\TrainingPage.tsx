import React, { useState } from 'react';

type Formation = {
  id: number;
  titre: string;
  niveau: string;
  duree: string;
  date: string;
  description: string;
  image: string;
  modules: string[];
  statut: string;
};

type Conseil = {
  id: number;
  categorie: string;
  titre: string;
  contenu: string;
  date: string;
  tempsLecture: string;
};

type MockData = {
  formations: Formation[];
  conseils: Conseil[];
  statistiques: {
    formationsTerminees: number;
    certificatsObtenus: number;
    tempsFormation: string;
    progression: number;
  };
};

// Données fictives
const mockData: MockData = {
  formations: [
    {
      id: 1,
      titre: "Découvrir l'Intelligence Artificielle",
      niveau: "débutant",
      duree: "1h",
      date: "2025-09-15",
      description: "Découvrez les bases de l'intelligence artificielle, son histoire, ses applications concrètes et ses enjeux pour le futur. Cette formation est idéale pour toute personne curieuse de comprendre l'IA sans prérequis technique.",
      image: "https://images.unsplash.com/photo-ia.jpg",
      modules: [
        "Introduction à l'IA et historique",
        "Exemples d'IA dans la vie quotidienne",
        "Les grands principes de fonctionnement",
        "Défis éthiques et opportunités"
      ],
      statut: "A venir"
    },
    {
      id: 2,
      titre: "Maîtriser LinkedIn pour Trouver un Emploi",
      niveau: "intermédiaire",
      duree: "2h",
      date: "2025-11-10",
      description: "Apprenez à optimiser votre profil LinkedIn, à développer votre réseau professionnel et à utiliser les outils de recherche d'emploi pour maximiser vos chances de recrutement.",
      image: "https://images.unsplash.com/photo-linkedin.jpg",
      modules: [
        "Créer un profil attractif et crédible",
        "Développer et entretenir son réseau",
        "Publier du contenu pertinent pour se démarquer",
        "Utiliser les fonctionnalités avancées de LinkedIn"
      ],
      statut: "A venir"
    },
    {
      id: 3,
      titre: "Gestion du Stress au Travail",
      niveau: "tous niveaux",
      duree: "45 min",
      date: "2026-01-20",
      description: "Des techniques concrètes et des exercices pratiques pour mieux gérer la pression professionnelle, prévenir le burn-out et améliorer son bien-être au travail.",
      image: "https://images.unsplash.com/photo-stress.jpg",
      modules: [
        "Comprendre les sources de stress",
        "Exercices de respiration et relaxation",
        "Organisation et priorisation des tâches",
        "Prévention et gestion du burn-out"
      ],
      statut: "A venir"
    },
    {
      id: 4,
      titre: "Initiation à la cybersécurité",
      niveau: "débutant",
      duree: "1h30",
      date: "2026-03-05",
      description: "Comprendre les bases de la sécurité informatique pour protéger ses données personnelles et professionnelles.",
      image: "https://images.unsplash.com/photo-cyber.jpg",
      modules: [
        "Les menaces courantes",
        "Bonnes pratiques de sécurité",
        "Gestion des mots de passe",
        "Sécuriser ses appareils"
      ],
      statut: "A venir"
    }
  ],
  conseils: [
    {
      id: 1,
      categorie: "Productivité",
      titre: "3 routines matinales pour réussir sa journée",
      contenu: "Découvrez comment bien démarrer votre journée pour être plus efficace.",
      date: "2025-09-10",
      tempsLecture: "4 min"
    },
    {
      id: 2,
      categorie: "Communication",
      titre: "Réussir ses entretiens d'embauche",
      contenu: "Préparez-vous efficacement et mettez toutes les chances de votre côté.",
      date: "2025-12-05",
      tempsLecture: "6 min"
    },
    {
      id: 3,
      categorie: "Bien-être",
      titre: "Faire des pauses pour mieux travailler",
      contenu: "L'importance des pauses régulières pour la concentration et la santé.",
      date: "2026-02-12",
      tempsLecture: "3 min"
    },
    {
      id: 4,
      categorie: "Organisation",
      titre: "Planifier sa semaine en 10 minutes",
      contenu: "Des astuces simples pour organiser efficacement votre emploi du temps.",
      date: "2026-03-25",
      tempsLecture: "5 min"
    },
    {
      id: 5,
      categorie: "Développement personnel",
      titre: "Gérer le stress avant une présentation",
      contenu: "Techniques rapides pour rester zen avant de prendre la parole.",
      date: "2026-04-08",
      tempsLecture: "4 min"
    },
    {
      id: 6,
      categorie: "Productivité",
      titre: "Automatiser ses tâches répétitives",
      contenu: "Découvrez des outils pour gagner du temps au quotidien.",
      date: "2026-05-20",
      tempsLecture: "7 min"
    }
  ],
  statistiques: {
    formationsTerminees: 0,
    certificatsObtenus: 0,
    tempsFormation: "0h",
    progression: 0
  }
};

export default function TrainingPage() {
  const [selectedFormation, setSelectedFormation] = useState<Formation | null>(null);
  const [selectedConseil, setSelectedConseil] = useState<Conseil | null>(null);
  const [activeTab, setActiveTab] = useState<'formations' | 'conseils'>('formations');

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'A venir':
        return 'bg-blue-100 text-blue-800';
      case 'disponible':
        return 'bg-green-100 text-green-800';
      case 'terminé':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6 px-2 md:px-0">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Formations et Conseils</h1>
        <div className="flex items-center space-x-4">
          <div className="bg-white px-4 py-2 rounded-lg shadow flex items-center space-x-2">
            <span className="text-gray-600">Progression globale :</span>
            <span className="text-[#FF7A35] font-bold">{mockData.statistiques.progression}%</span>
          </div>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500">Formations à venir</h3>
          <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{mockData.formations.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500">Certificats Premium</h3>
          <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{mockData.formations.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500">Total d'heures</h3>
          <p className="mt-2 text-2xl font-bold text-[#FF7A35]">{mockData.formations.reduce((acc, f) => acc + (parseFloat(f.duree) || 0), 0)}h</p>
        </div>
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-sm font-medium text-gray-500">Prochaine formation</h3>
          <p className="mt-2 text-sm font-medium text-gray-800">{mockData.formations.sort((a, b) => a.date.localeCompare(b.date))[0]?.date.split('-').reverse().join('/')}</p>
        </div>
      </div>

      {/* Onglets */}
      <div className="flex space-x-4 border-b">
        <button
          onClick={() => setActiveTab('formations')}
          className={`pb-2 px-4 ${
            activeTab === 'formations'
              ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Formations
        </button>
        <button
          onClick={() => setActiveTab('conseils')}
          className={`pb-2 px-4 ${
            activeTab === 'conseils'
              ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Conseils & Astuces
        </button>
      </div>

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Liste des formations/conseils */}
        <div className="space-y-4">
          {activeTab === 'formations' ? (
            // Formations
            mockData.formations.map((formation: Formation) => (
              <div
                key={formation.id}
                onClick={() => {
                  setSelectedFormation(formation);
                  setSelectedConseil(null);
                }}
                className={`bg-white rounded-lg shadow p-4 cursor-pointer transition-all ${
                  selectedFormation?.id === formation.id ? 'ring-2 ring-[#FF7A35]' : 'hover:shadow-md'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-semibold text-gray-800">{formation.titre}</h3>
                    <p className="text-sm text-gray-600">Niveau : {formation.niveau}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(formation.statut)}`}>
                    {formation.statut}
                  </span>
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  <p>Durée : {formation.duree}</p>
                  <p>Date : {formation.date}</p>
                </div>
              </div>
            ))
          ) : (
            // Conseils
            mockData.conseils.map((conseil: Conseil) => (
              <div
                key={conseil.id}
                onClick={() => {
                  setSelectedConseil(conseil);
                  setSelectedFormation(null);
                }}
                className={`bg-white rounded-lg shadow p-4 cursor-pointer transition-all ${
                  selectedConseil?.id === conseil.id ? 'ring-2 ring-[#FF7A35]' : 'hover:shadow-md'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <span className="text-xs font-medium text-[#FF7A35] bg-orange-50 px-2 py-1 rounded-full">
                      {conseil.categorie}
                    </span>
                    <h3 className="font-semibold text-gray-800 mt-2">{conseil.titre}</h3>
                  </div>
                  <span className="text-sm text-gray-500">{conseil.tempsLecture}</span>
                </div>
                <p className="text-sm text-gray-600 mt-2">{conseil.contenu}</p>
                <div className="mt-4 flex justify-between items-center">
                  <span className="text-sm text-gray-500">{conseil.date}</span>
                  <span className="text-xs text-[#FF7A35] font-semibold">bientôt</span>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Détails de la formation ou du conseil */}
        {activeTab === 'formations' && (
          selectedFormation ? (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">{selectedFormation.titre}</h2>
              <div className="space-y-6 px-2 md:px-0">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                  <p className="text-gray-800">{selectedFormation.description}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Modules</h3>
                  <ul className="space-y-2">
                    {selectedFormation.modules.map((module: string, index: number) => (
                      <li key={index} className="flex items-center text-gray-700">
                        <span className="w-6 h-6 flex items-center justify-center bg-[#FF7A35] text-white rounded-full text-sm mr-3">
                          {index + 1}
                        </span>
                        {module}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Informations</h3>
                  <ul className="space-y-2">
                    <li className="flex items-center text-gray-700">
                      <span className="w-6 h-6 flex items-center justify-center bg-[#FF7A35] text-white rounded-full text-sm mr-3">
                        📅
                      </span>
                      {selectedFormation.date} <span className="ml-2 text-xs text-[#FF7A35] font-semibold">bientôt</span>
                    </li>
                    <li className="flex items-center text-gray-700">
                      <span className="w-6 h-6 flex items-center justify-center bg-[#FF7A35] text-white rounded-full text-sm mr-3">
                        ⏱️
                      </span>
                      {selectedFormation.duree} de formation
                    </li>
                    <li className="flex items-center text-green-700 font-semibold">
                      <span className="w-6 h-6 flex items-center justify-center bg-green-100 text-green-700 rounded-full text-sm mr-3">
                        ⭐
                      </span>
                      Gratuit avec l'abonnement Premium
                    </li>
                  </ul>
                </div>

                <div className="pt-4 border-t">
                  <button className="w-full bg-[#FF7A35] text-white px-4 py-2 rounded-md hover:bg-[#ff6b2c] transition-colors">
                    {selectedFormation.statut === 'disponible' ? 'Commencer la formation' : 'Voir le détail'}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-6 flex items-center justify-center text-gray-500">
              Sélectionnez une formation pour voir les détails
            </div>
          )
        )}
        {activeTab === 'conseils' && (
          selectedConseil ? (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">{selectedConseil.titre}</h2>
              <div className="space-y-6 px-2 md:px-0">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Catégorie</h3>
                  <span className="text-xs font-medium text-[#FF7A35] bg-orange-50 px-2 py-1 rounded-full">{selectedConseil.categorie}</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                  <p className="text-gray-800">{selectedConseil.contenu}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Informations</h3>
                  <ul className="space-y-2">
                    <li className="flex items-center text-gray-700">
                      <span className="w-6 h-6 flex items-center justify-center bg-[#FF7A35] text-white rounded-full text-sm mr-3">
                        📅
                      </span>
                      {selectedConseil.date} <span className="ml-2 text-xs text-[#FF7A35] font-semibold">bientôt</span>
                    </li>
                    <li className="flex items-center text-gray-700">
                      <span className="w-6 h-6 flex items-center justify-center bg-[#FF7A35] text-white rounded-full text-sm mr-3">
                        ⏱️
                      </span>
                      {selectedConseil.tempsLecture} de lecture
                    </li>
                    <li className="flex items-center text-green-700 font-semibold">
                      <span className="w-6 h-6 flex items-center justify-center bg-green-100 text-green-700 rounded-full text-sm mr-3">
                        ⭐
                      </span>
                      Gratuit avec l'abonnement Premium
                    </li>
                  </ul>
                </div>
                <div className="pt-4 border-t">
                  <button className="w-full bg-[#FF7A35] text-white px-4 py-2 rounded-md cursor-default opacity-60">
                    Conseil informatif
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-6 flex items-center justify-center text-gray-500">
              Sélectionnez un conseil pour voir les détails
            </div>
          )
        )}
      </div>
    </div>
  );
} 