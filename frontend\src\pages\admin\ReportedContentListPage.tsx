import React, { useEffect, useState } from 'react';
import {
  Box, Container, Typography, Paper, Grid, Button, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Select, MenuItem, FormControl, InputLabel, CircularProgress, TextField,
  Checkbox, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, FormControlLabel, Radio, RadioGroup, Snackbar, Alert
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import axios from 'axios';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';
import { API_CONFIG } from '../../config/api';

const STATUS_COLORS: Record<string, string> = {
  'pending': '#FFA500',         // En attente
  'in_review': '#1976d2',      // En cours de traitement
  'validated': '#4CAF50',      // Traité/Validé
  'rejected': '#BDBDBD',       // Rejeté
  'content_deleted': '#D32F2F', // Contenu supprimé (rouge vif)
  'masqué': '#FF6B2C',         // Masqué (orange vif)
  'restauré': '#4FC3F7',      // Restauré (bleu clair)
};

const STATUS_LABELS: Record<string, string> = {
  'pending': 'En attente',
  'in_review': 'En cours',
  'validated': 'Validé',
  'rejected': 'Rejeté',
  'content_deleted': 'Supprimé',
  'masqué': 'Masqué',
  'restauré': 'Restauré',
};

const TYPE_LABELS: Record<string, string> = {
  'comment': 'Commentaire',
  'message': 'Message',
  'review': 'Avis',
  'mission': 'Mission',
  'profile': 'Profil',
};

const ReportedContentListPage: React.FC = () => {
  const [reports, setReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [total, setTotal] = useState(0);
  const [statusFilter, setStatusFilter] = useState('pending_and_in_review');
  const [typeFilter, setTypeFilter] = useState('');
  const [search, setSearch] = useState('');
  const [stats, setStats] = useState<any>({});
  const [orderBy, setOrderBy] = useState<string>('created_at');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [bulkActionDialogOpen, setBulkActionDialogOpen] = useState(false);
  const [bulkAction, setBulkAction] = useState<string>('in_review');
  const [bulkComment, setBulkComment] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [processingBulkAction, setProcessingBulkAction] = useState(false);
  const navigate = useNavigate();

  // Ajout d'un utilitaire pour savoir si on a un profil sélectionné
  const hasProfileSelected = React.useMemo(() => {
    return reports.filter(r => selectedReports.includes(r.id)).some(r => r.content_type === 'profile');
  }, [selectedReports, reports]);

  // Récupérer les signalements
  const fetchReports = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: page + 1,
        limit: rowsPerPage,
      };
      if (statusFilter) params.status = getStatusFilterParam();
      if (typeFilter) params.type = typeFilter;
      if (search) params.search = search;
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content`, { params, headers, withCredentials: true });
      setReports(res.data.data || []);
      setTotal(res.data.count || 0);
    } catch (e) {
      setReports([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // Récupérer les stats
  const fetchStats = async () => {
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/stats`, {
        headers,
        withCredentials: true
      });
      setStats(res.data || {});
    } catch (e) {
      setStats({});
    }
  };

  const handleSort = (column: string) => {
    if (orderBy === column) {
      setOrder(order === 'asc' ? 'desc' : 'asc');
    } else {
      setOrderBy(column);
      setOrder('asc');
    }
  };

  const sortedReports = React.useMemo(() => {
    const sorted = [...reports];
    sorted.sort((a, b) => {
      let aValue = a[orderBy];
      let bValue = b[orderBy];
      if (orderBy === 'report_count') {
        aValue = a.report_count || 1;
        bValue = b.report_count || 1;
      }
      if (aValue === undefined || aValue === null) return 1;
      if (bValue === undefined || bValue === null) return -1;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === 'asc' ? aValue - bValue : bValue - aValue;
      }
      if (typeof aValue === 'object' && typeof bValue === 'object' && aValue instanceof Date && bValue instanceof Date) {
        return order === 'asc' ? aValue.getTime() - bValue.getTime() : bValue.getTime() - aValue.getTime();
      }
      // fallback : compare as string
      return order === 'asc' ? String(aValue).localeCompare(String(bValue)) : String(bValue).localeCompare(String(aValue));
    });
    return sorted;
  }, [reports, orderBy, order]);

  useEffect(() => {
    fetchReports();
    // eslint-disable-next-line
  }, [page, rowsPerPage, statusFilter, typeFilter]);

  useEffect(() => {
    fetchStats();
  }, []);

  const getStatusFilterParam = () => {
    if (statusFilter === 'pending_and_in_review') return 'pending,in_review';
    return statusFilter;
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = reports.map((report) => report.id);
      setSelectedReports(newSelected);
    } else {
      setSelectedReports([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    event.stopPropagation();
    const selectedIndex = selectedReports.indexOf(id);
    let newSelected: string[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selectedReports, id];
    } else {
      newSelected = selectedReports.filter((reportId) => reportId !== id);
    }

    setSelectedReports(newSelected);
  };

  const isSelected = (id: string) => selectedReports.indexOf(id) !== -1;

  const handleBulkActionOpen = () => {
    if (selectedReports.length === 0) {
      setSnackbarMessage('Veuillez sélectionner au moins un signalement');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setBulkActionDialogOpen(true);
  };

  const handleBulkActionClose = () => {
    setBulkActionDialogOpen(false);
  };

  const applyBulkAction = async () => {
    setProcessingBulkAction(true);
    try {
      const headers = await getCommonHeaders();
      await fetchCsrfToken();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      let actionName = '';
      let status = bulkAction;
      
      if (bulkAction === 'mask' || bulkAction === 'validated') {
        status = 'masqué';
        actionName = bulkAction;
      } else if (bulkAction === 'rejected' || bulkAction === 'restauré') {
        status = 'rejected';
        actionName = bulkAction;
      } else if (bulkAction === 'delete') {
        status = 'content_deleted';
        actionName = 'delete';
      }

      const res = await axios.post(
        `${API_CONFIG.baseURL}/api/reported-content/bulk-update`,
        {
          ids: selectedReports,
          status: status,
          action: actionName,
          admin_comment: bulkComment || `Traitement en masse - ${status}`,
          notifyReporters: false,
          notifyAuthorByEmail: false
        },
        { headers, withCredentials: true }
      );

      setSnackbarMessage(`${res.data.totalProcessed} signalements traités avec succès (${res.data.totalErrors} erreurs)`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setSelectedReports([]);
      fetchReports();
      fetchStats();
    } catch (err) {
      setSnackbarMessage('Erreur lors du traitement des signalements');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setProcessingBulkAction(false);
      setBulkActionDialogOpen(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ background: 'linear-gradient(to bottom, #FFF8F3, white)', minHeight: '100vh', py: 4 }}>
      <Container maxWidth="lg">
        <Typography variant="h4" sx={{ fontWeight: 700, color: '#FF6B2C', mb: 2 }}>
          Gestion des signalements
        </Typography>
        <Paper sx={{ p: 3, mb: 3, borderRadius: 3, boxShadow: '0 4px 12px rgba(255,107,44,0.07)' }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', justifyContent: { xs: 'flex-start', sm: 'center' } }}>
            <Chip label={`Total: ${stats.total || 0}`} sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 600 }} />
            <Chip label={`En attente: ${stats.pending || 0}`} sx={{ bgcolor: STATUS_COLORS['pending'], color: 'white', fontWeight: 600 }} />
            <Chip label={`En cours: ${stats.in_review || 0}`} sx={{ bgcolor: STATUS_COLORS['in_review'], color: 'white', fontWeight: 600 }} />
            <Chip label={`Masqués: ${stats.masqué || 0}`} sx={{ bgcolor: STATUS_COLORS['masqué'], color: 'white', fontWeight: 600 }} />
            <Chip label={`Rejetés: ${stats.rejected || 0}`} sx={{ bgcolor: STATUS_COLORS['rejected'], color: 'white', fontWeight: 600 }} />
            <Chip label={`Supprimés: ${stats.content_deleted || 0}`} sx={{ bgcolor: STATUS_COLORS['content_deleted'], color: 'white', fontWeight: 600 }} />
            <Chip label={`Validés: ${stats.validated || 0}`} sx={{ bgcolor: STATUS_COLORS['validated'], color: 'white', fontWeight: 600 }} />
          </Box>
        </Paper>
        <Paper sx={{ p: 2, mb: 3, borderRadius: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Statut</InputLabel>
                <Select value={statusFilter} label="Statut" onChange={e => { setStatusFilter(e.target.value); setPage(0); }}>
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="pending_and_in_review">En attente et en cours</MenuItem>
                  <MenuItem value="pending">En attente</MenuItem>
                  <MenuItem value="in_review">En cours</MenuItem>
                  <MenuItem value="masqué">Masqué</MenuItem>
                  <MenuItem value="rejected">Rejeté</MenuItem>
                  <MenuItem value="content_deleted">Supprimé</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select value={typeFilter} label="Type" onChange={e => { setTypeFilter(e.target.value); setPage(0); }}>
                  <MenuItem value="">Tous</MenuItem>
                  <MenuItem value="comment">Commentaire</MenuItem>
                  <MenuItem value="message">Message</MenuItem>
                  <MenuItem value="review">Avis</MenuItem>
                  <MenuItem value="mission">Mission</MenuItem>
                  <MenuItem value="profile">Profil</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 4 }}>
              <TextField
                fullWidth
                size="small"
                label="Recherche (raison, utilisateur, contenu...)"
                value={search}
                onChange={e => setSearch(e.target.value)}
                onKeyDown={e => { if (e.key === 'Enter') { setPage(0); fetchReports(); } }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 2 }}>
              <Button variant="contained" sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 600 }} onClick={() => { setPage(0); fetchReports(); }}>
                Rechercher
              </Button>
            </Grid>
          </Grid>
        </Paper>
        
        {selectedReports.length > 0 && (
          <Paper sx={{ p: 2, mb: 3, borderRadius: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body1" sx={{ fontWeight: 600 }}>
              {selectedReports.length} signalement(s) sélectionné(s)
            </Typography>
            <Button 
              variant="contained" 
              color="primary" 
              sx={{ bgcolor: '#FF6B2C' }}
              onClick={handleBulkActionOpen}
            >
              Traiter en masse
            </Button>
          </Paper>
        )}
        
        <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: '#FFF8F3' }}>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedReports.length > 0 && selectedReports.length < reports.length}
                      checked={reports.length > 0 && selectedReports.length === reports.length}
                      onChange={handleSelectAll}
                      sx={{ 
                        color: '#FF6B2C',
                        '&.Mui-checked': { color: '#FF6B2C' },
                        '&.MuiCheckbox-indeterminate': { color: '#FF6B2C' },
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('created_at')}>
                    Date {orderBy === 'created_at' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('report_count')}>
                    Nb signalements {orderBy === 'report_count' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('content_type')}>
                    Type {orderBy === 'content_type' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('reported_by')}>
                    Signalé par {orderBy === 'reported_by' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('reason')}>
                    Raison {orderBy === 'reason' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700, cursor: 'pointer' }} onClick={() => handleSort('status')}>
                    Statut {orderBy === 'status' && (order === 'asc' ? <ArrowDropUpIcon sx={{ verticalAlign: 'middle' }} /> : <ArrowDropDownIcon sx={{ verticalAlign: 'middle' }} />)}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 700 }} align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <CircularProgress sx={{ color: '#FF6B2C' }} />
                    </TableCell>
                  </TableRow>
                ) : reports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      Aucun signalement trouvé.
                    </TableCell>
                  </TableRow>
                ) : sortedReports.map((report) => {
                  const isItemSelected = isSelected(report.id);
                  return (
                    <TableRow
                      key={report.id}
                      hover
                      sx={{ cursor: 'pointer' }}
                      onClick={e => {
                        if ((e.target as HTMLElement).closest('button, a, input')) return;
                        navigate(`/admin/reported-content/${report.id}`);
                      }}
                      selected={isItemSelected}
                    >
                      <TableCell padding="checkbox" onClick={e => e.stopPropagation()}>
                        <Checkbox
                          checked={isItemSelected}
                          onChange={(event) => handleSelectOne(event, report.id)}
                          sx={{ 
                            color: '#FF6B2C',
                            '&.Mui-checked': { color: '#FF6B2C' },
                          }}
                        />
                      </TableCell>
                      <TableCell>{new Date(report.created_at).toLocaleString('fr-FR')}</TableCell>
                      <TableCell>
                        <Chip label={report.report_count || 1} sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 700 }} />
                      </TableCell>
                      <TableCell>
                        <Chip label={TYPE_LABELS[report.content_type] || report.content_type} sx={{ bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 600 }} />
                      </TableCell>
                      <TableCell>{
                        report.users && report.users.user_profil && report.users.user_profil[0]
                          ? `${report.users.user_profil[0].prenom || ''} ${report.users.user_profil[0].nom || ''}`
                          : '-'
                      }</TableCell>
                      <TableCell>{report.reason}</TableCell>
                      <TableCell>
                        <Chip label={STATUS_LABELS[report.status] || report.status} sx={{ bgcolor: STATUS_COLORS[report.status] || '#E0E0E0', color: 'white', fontWeight: 600 }} />
                      </TableCell>
                      <TableCell>
                        <Button
                          component={Link}
                          to={`/admin/reported-content/${report.id}`}
                          size="small"
                          variant="outlined"
                          sx={{ color: '#FF6B2C', borderColor: '#FF6B2C', fontWeight: 600 }}
                          startIcon={<VisibilityIcon />}
                          onClick={e => e.stopPropagation()}
                        >
                          Voir
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={total}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={e => { setRowsPerPage(parseInt(e.target.value, 10)); setPage(0); }}
            rowsPerPageOptions={[10, 20, 50]}
            labelRowsPerPage="Lignes par page"
          />
        </Paper>
        <Paper sx={{ p: 2, mb: 3, borderRadius: 3, boxShadow: '0 4px 12px rgba(255,107,44,0.07)', mt: 4 }}>
          <Typography variant="subtitle1" sx={{ color: '#FF6B2C', mb: 1, fontWeight: 700 }}>
            Signification des statuts
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip label={STATUS_LABELS['pending']} size="small" sx={{ minWidth: 100, bgcolor: STATUS_COLORS['pending'], color: 'white', fontWeight: 600, fontSize: 13, height: 28 }} />
              <Typography variant="body2" sx={{ color: '#444' }}>Signalement en attente de traitement</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip label={STATUS_LABELS['in_review']} size="small" sx={{ minWidth: 100, bgcolor: STATUS_COLORS['in_review'], color: 'white', fontWeight: 600, fontSize: 13, height: 28 }} />
              <Typography variant="body2" sx={{ color: '#444' }}>Signalement en cours de traitement</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip label={STATUS_LABELS['masqué']} size="small" sx={{ minWidth: 100, bgcolor: STATUS_COLORS['masqué'], color: 'white', fontWeight: 600, fontSize: 13, height: 28 }} />
              <Typography variant="body2" sx={{ color: '#444' }}>Valider le signalement : le contenu est masqué.</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip label={STATUS_LABELS['rejected']} size="small" sx={{ minWidth: 100, bgcolor: STATUS_COLORS['rejected'], color: 'white', fontWeight: 600, fontSize: 13, height: 28 }} />
              <Typography variant="body2" sx={{ color: '#444' }}>Rejeter le signalement : le contenu original est restauré (si masqué).</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip label={STATUS_LABELS['content_deleted']} size="small" sx={{ minWidth: 100, bgcolor: STATUS_COLORS['content_deleted'], color: 'white', fontWeight: 600, fontSize: 13, height: 28 }} />
              <Typography variant="body2" sx={{ color: '#444' }}>Supprimer le contenu : le contenu est supprimé définitivement, il ne pourra pas être restauré.</Typography>
            </Box>
          </Box>
        </Paper>
      </Container>

      {/* Dialogue d'action en masse */}
      <Dialog
        open={bulkActionDialogOpen}
        onClose={handleBulkActionClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>
          Traitement en masse - {selectedReports.length} signalement(s)
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Choisissez l'action à appliquer à tous les signalements sélectionnés :
          </DialogContentText>
          <FormControl component="fieldset" sx={{ mb: 2, width: '100%' }}>
            <RadioGroup
              value={bulkAction}
              onChange={(e) => setBulkAction(e.target.value)}
            >
              <FormControlLabel 
                value="in_review" 
                control={<Radio sx={{ color: '#FF6B2C', '&.Mui-checked': { color: '#FF6B2C' } }} />} 
                label="Marquer comme en cours de traitement" 
              />
              <FormControlLabel 
                value="mask" 
                control={<Radio sx={{ color: '#FF6B2C', '&.Mui-checked': { color: '#FF6B2C' } }} />} 
                label="Masquer le contenu (valider les signalements)" 
              />
              <FormControlLabel 
                value="rejected" 
                control={<Radio sx={{ color: '#FF6B2C', '&.Mui-checked': { color: '#FF6B2C' } }} />} 
                label="Rejeter les signalements" 
              />
              {!hasProfileSelected && (
                <FormControlLabel 
                  value="delete" 
                  control={<Radio sx={{ color: '#FF6B2C', '&.Mui-checked': { color: '#FF6B2C' } }} />} 
                  label="Supprimer le contenu définitivement" 
                />
              )}
            </RadioGroup>
          </FormControl>
          {hasProfileSelected && (
            <DialogContentText variant="caption" sx={{ color: '#D32F2F', mb: 1 }}>
              L'option "Supprimer le contenu définitivement" n'est pas disponible pour les profils.
            </DialogContentText>
          )}
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Commentaire (optionnel)"
            variant="outlined"
            value={bulkComment}
            onChange={(e) => setBulkComment(e.target.value)}
            sx={{ mb: 2 }}
          />
          <DialogContentText variant="caption" sx={{ color: 'text.secondary' }}>
            Note: Pour les actions complexes comme la suppression, il est recommandé de vérifier individuellement les contenus.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleBulkActionClose} sx={{ color: 'gray' }}>
            Annuler
          </Button>
          <Button
            onClick={applyBulkAction}
            variant="contained"
            sx={{ bgcolor: '#FF6B2C' }}
            disabled={processingBulkAction}
          >
            {processingBulkAction ? <CircularProgress size={24} sx={{ color: 'white' }} /> : 'Appliquer'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar de notification */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ReportedContentListPage; 