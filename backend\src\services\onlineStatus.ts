import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';

const ONLINE_TIMEOUT = 3 * 60; // 3 minutes en secondes
const REDIS_ONLINE_STATUS_PREFIX = 'user_online_status:';
const REDIS_CACHE_DURATION = 2 * 60; // 2 minute en secondes
const REDIS_ACTIVITY_UPDATE_PREFIX = 'user_activity_update:';
const MIN_UPDATE_INTERVAL = 30; // 30 secondes minimum entre les mises à jour
// Préfixe pour les logs de vérification
const REDIS_LOG_CHECK_PREFIX = 'user_log_check:';
// Intervalle minimum entre les logs (5 minutes)
const MIN_LOG_INTERVAL = 5 * 60;

export const onlineStatusService = {
  async updateUserActivity(userId: string) {
    try {
      // Vérifier si une mise à jour récente existe
      const lastUpdate = await redis.get(`${REDIS_ACTIVITY_UPDATE_PREFIX}${userId}`);
      if (lastUpdate) {
        // Si une mise à jour récente existe (moins de 30 secondes), on ignore cette requête
        // Cela permet de limiter les mises à jour en base de données
        return { limited: true };
      }

      // Mettre à jour last_activity et is_online dans la base de données
      const { error } = await supabase
        .from('users')
        .update({
          last_activity: new Date().toISOString(),
          is_online: true
        })
        .eq('id', userId);

      // Log moins fréquent pour les mises à jour réussies
      const lastLogUpdate = await redis.get(`${REDIS_LOG_CHECK_PREFIX}${userId}`);
      if (!lastLogUpdate) {
        logger.info('Mise à jour de l\'activité OK pour l\'utilisateur:', userId);
        await redis.set(`${REDIS_LOG_CHECK_PREFIX}${userId}`, '1', 'EX', MIN_LOG_INTERVAL);
      }

      if (error) {
        logger.error('Erreur lors de la mise à jour de l\'activité de l\'utilisateur:', error);
        return { success: false, error };
      }

      // Définir une clé Redis qui expirera après ONLINE_TIMEOUT
      await redis.set(`user_online:${userId}`, '1', 'EX', ONLINE_TIMEOUT);
      // Mettre en cache le statut en ligne
      await redis.set(`${REDIS_ONLINE_STATUS_PREFIX}${userId}`, 'true', 'EX', REDIS_CACHE_DURATION);
      // Marquer le timestamp de la dernière mise à jour
      await redis.set(`${REDIS_ACTIVITY_UPDATE_PREFIX}${userId}`, Date.now().toString(), 'EX', MIN_UPDATE_INTERVAL);

      return { success: true };
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de l\'activité utilisateur:', error);
      return { success: false, error };
    }
  },

  async checkUserOnlineStatus(userId: string): Promise<boolean> {
    try {
      // Vérifier d'abord dans le cache Redis
      const cachedStatus = await redis.get(`${REDIS_ONLINE_STATUS_PREFIX}${userId}`);
      if (cachedStatus !== null) {
        return cachedStatus === 'true';
      }

      // Vérifier si on a déjà loggé récemment pour cet utilisateur
      const lastLogCheck = await redis.get(`${REDIS_LOG_CHECK_PREFIX}${userId}`);
      if (!lastLogCheck) {
        // Si pas de log récent, on peut logger cette vérification
        logger.info('Vérification de l\'activité de l\'utilisateur:', userId);
        // Marquer qu'on a loggé pour cet utilisateur (expire après MIN_LOG_INTERVAL)
        await redis.set(`${REDIS_LOG_CHECK_PREFIX}${userId}`, '1', 'EX', MIN_LOG_INTERVAL);
      }

      // Si pas dans le cache, vérifier si l'utilisateur a une clé Redis active
      logger.info('Vérification de l\'activité de l\'utilisateur:', userId);
      const isOnline = await redis.exists(`user_online:${userId}`);

      // Si l'utilisateur n'est pas en ligne, mettre à jour la base de données
      if (isOnline !== 1) {
        const { error } = await supabase
          .from('users')
          .update({
            is_online: false
          })
          .eq('id', userId);

        if (error) {
          logger.error('Erreur lors de la mise à jour de l\'activité hors ligne:', error);
        }
      }

      // Mettre en cache le résultat
      await redis.set(
        `${REDIS_ONLINE_STATUS_PREFIX}${userId}`,
        isOnline === 1 ? 'true' : 'false',
        'EX',
        REDIS_CACHE_DURATION
      );

      return isOnline === 1;
    } catch (error) {
      logger.error('Erreur lors de la vérification de l\'activité de l\'utilisateur:', error);
      return false;
    }
  },

  async verifyAndUpdateOnlineStatus(userId: string): Promise<boolean> {
    try {
      // Vérifier d'abord dans le cache Redis
      const cachedStatus = await redis.get(`${REDIS_ONLINE_STATUS_PREFIX}${userId}`);
      if (cachedStatus !== null) {
        return cachedStatus === 'true';
      }

      // Récupérer la dernière activité de l'utilisateur
      const { data: userData, error } = await supabase
        .from('users')
        .select('last_activity, is_online')
        .eq('id', userId)
        .single();

      if (error || !userData) {
        logger.error('Erreur lors de la récupération des données de l\'utilisateur:', error);
        return false;
      }

      const lastActivity = new Date(userData.last_activity).getTime();
      const now = Date.now();
      const isOnline = (now - lastActivity) / 1000 < ONLINE_TIMEOUT;

      // Si le statut a changé, mettre à jour la base de données
      if (userData.is_online !== isOnline) {
        const { error: updateError } = await supabase
          .from('users')
          .update({ is_online: isOnline })
          .eq('id', userId);

        if (updateError) {
          logger.error('Erreur lors de la mise à jour de l\'activité de l\'utilisateur:', updateError);
        }
      }

      // Mettre en cache le résultat
      await redis.set(
        `${REDIS_ONLINE_STATUS_PREFIX}${userId}`,
        isOnline ? 'true' : 'false',
        'EX',
        REDIS_CACHE_DURATION
      );

      return isOnline;
    } catch (error) {
      logger.error('Erreur lors de la vérification et mise à jour de l\'activité de l\'utilisateur:', error);
      return false;
    }
  }
}; 