// Types locaux pour le système de messagerie (copie de ../../types/messaging.ts)
export interface User {
  id: string;
  email?: string;
  avatar_url?: string | null;
  prenom?: string | null;
  nom?: string | null;
  date_inscription?: string | null;
  profil?: {
    type?: string;
    type_de_profil?: string;
  };
  notification_preferences?: {
    messages?: boolean;
  };
  usertag?: string;
  first_name?: string;
  last_name?: string;
}

export interface Conversation {
  id: string;
  user1_id: string;
  user2_id: string;
  created_at: Date;
  updated_at: Date;
  last_message_date?: Date;
  last_message_content?: string;
  last_message_sender_id?: string;
  unread_count: number;
  // Propriétés pour la gestion des blocages et suppressions
  user1_has_blocked: boolean;
  user2_has_blocked: boolean;
  user1_has_deleted: boolean;
  user2_has_deleted: boolean;
  // Statut général (pour la rétrocompatibilité)
  is_blocked: boolean;
  // Relations
  user1?: User;
  user2?: User;
  last_message?: Message;
  // Pour le décompte
  total_messages?: number;
  unread_count_user1?: number;
  unread_count_user2?: number;
  // Champ ajouté pour la rétrocompatibilité 
  otherUser?: User;
}

export interface MessageAttachment {
  id: string;
  message_id: string;
  file_name: string;
  file_size: number;
  file_type?: string;
  mime_type?: string;
  storage_path?: string;
  file_path?: string;
  public_url?: string;
  expires_at: Date;
  created_at: Date;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  receiver_id?: string;
  content: string;
  created_at: Date;
  updated_at?: Date;
  read_at?: Date;
  is_read?: boolean;
  is_deleted?: boolean;
  is_deleted_attachment?: boolean;
  attachments: MessageAttachment[];
  has_attachment?: boolean;
  sender?: User;
  receiver?: User;
}

export interface ConversationsResponse {
  conversations: Conversation[];
  total: number;
  page: number;
  limit: number;
}

export interface MessagesResponse {
  messages: Message[];
  total: number;
  page: number;
  limit: number;
} 