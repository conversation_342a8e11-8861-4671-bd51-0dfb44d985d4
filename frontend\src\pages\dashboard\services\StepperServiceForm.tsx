import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  DialogContent,
  IconButton,
  TextField,
  Card,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  InputAdornment,
  Chip,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Paper,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, XCircle, ArrowLeft, ArrowRight, FileText, Euro, Clock, Calendar, Check, Info, Trash2, Plus, ChevronUp, ChevronDown, Sparkles, HelpCircle } from 'lucide-react';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES, ServiceCategory, ServiceSubcategory, UserService } from './types';
import { notify } from '@/components/Notification';
import DOMPurify from 'dompurify';
import { serviceApi } from './serviceApi';
import ModalPortal from '@/components/ModalPortal';
import CloseIcon from '@mui/icons-material/Close';
import { useCreateNotification } from '@/hooks/useCreateNotification';
import ReglementationModal from './ReglementationModal';
import logger from '@/utils/logger';
import TiptapEditor from '@/components/TiptapEditor';
import useContentModeration from '@/hooks/useContentModeration';
import AiGenerationSystem from '@/components/ai/AiGenerationSystem';

// Styled components
const ModalContainer = styled('div')(({ theme }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  borderRadius: '24px',
  maxWidth: '1400px',
  width: '95%',
  background: '#FFF8F3',
  overflow: 'hidden',
  boxShadow: '0 25px 50px -12px rgba(255, 107, 44, 0.15)',
  display: 'flex',
  flexDirection: 'column',
  maxHeight: '90vh',
  zIndex: 1300,
  [theme.breakpoints.down('sm')]: {
    width: '98%',
    borderRadius: '16px',
    maxHeight: '95vh',
    margin: 0
  },
  [theme.breakpoints.down('xs')]: {
    width: '99%',
    borderRadius: '12px',
    margin: 0
  }
}));

const StyledCard = styled(Card)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  borderRadius: '16px',
  border: '1px solid transparent',
  background: '#FFFFFF',
  overflow: 'hidden',
  height: '300px',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  marginBottom: '20px',
  width: '100%',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 10px 20px rgba(255, 107, 44, 0.08)',
    borderColor: 'rgba(255, 122, 53, 0.6)',
    '& .card-image': {
      transform: 'scale(1.05)',
    },
    '& .card-overlay': {
      background: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(255, 107, 44, 0.05) 100%)',
    },
    '& .subcategories-container': {
      opacity: 1,
      transform: 'translateY(0)',
      visibility: 'visible',
    }
  },
  '&.selected': {
    borderColor: 'rgba(255, 107, 44, 0.8)',
    boxShadow: '0 10px 20px rgba(255, 107, 44, 0.12)',
  },
  '&.filtered': {
    animation: 'highlight 1s ease',
    borderColor: 'rgba(255, 107, 44, 0.8)',
    '& .card-overlay': {
      background: 'linear-gradient(180deg, rgba(255, 107, 44, 0.05) 0%, rgba(255, 107, 44, 0.1) 100%)',
    }
  },
  [theme.breakpoints.down('sm')]: {
    height: '170px',
    marginBottom: '14px',
    borderRadius: '12px',
  },
  [theme.breakpoints.down('xs')]: {
    height: '150px',
    marginBottom: '10px',
  }
}));

const CardImage = styled('div')(({ theme }) => ({
  position: 'relative',
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  borderRadius: '16px',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.3) 100%)',
    opacity: 0.7,
    transition: 'opacity 0.4s ease',
  },
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    borderRadius: '16px',
    transition: 'transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '12px',
    '& img': {
      borderRadius: '12px',
    }
  }
}));

const CardOverlay = styled('div')(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-end',
  padding: '20px',
  background: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.15) 100%)',
  transition: 'background 0.4s ease',
  [theme.breakpoints.down('sm')]: {
    padding: '14px',
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px',
  }
}));

const CategoryTitle = styled(Typography)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  fontSize: '1.25rem',
  textShadow: '0 1px 3px rgba(0,0,0,0.2)',
  transform: 'translateY(0)',
  transition: 'transform 0.4s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.1rem',
  },
  [theme.breakpoints.down('xs')]: {
    fontSize: '1rem',
  }
}));

const SubcategoriesContainer = styled('div')(({ theme }) => ({
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  gap: '6px',
  maxHeight: '240px',
  overflowY: 'auto',
  backgroundColor: '#FFFFFF',
  borderRadius: '0 0 16px 16px',
  boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.03)',
  '& .subcategory-item': {
    padding: '6px 12px',
    borderRadius: '6px',
    cursor: 'pointer',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.05)'
    },
    '&.highlight': {
      backgroundColor: 'rgba(255, 107, 44, 0.03)',
      color: '#FF865D',
      fontWeight: 500
    },
    '&.selected': {
      backgroundColor: 'rgba(255, 107, 44, 0.8)',
      color: '#FFFFFF',
      fontWeight: 500,
      '&:hover': {
        backgroundColor: 'rgba(255, 122, 53, 0.85)'
      }
    },
    '&.existing': {
      opacity: 0.6,
      cursor: 'not-allowed',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      '& .existing-label': {
        fontSize: '0.7rem',
        color: '#777',
        fontStyle: 'italic'
      }
    }
  },
  [theme.breakpoints.down('sm')]: {
    padding: '8px',
    gap: '4px',
    maxHeight: '200px',
    borderRadius: '0 0 12px 12px',
    '& .subcategory-item': {
      padding: '5px 10px',
      borderRadius: '5px',
      fontSize: '0.85rem',
      '& .existing-label': {
        fontSize: '0.65rem',
      }
    }
  },
  [theme.breakpoints.down('xs')]: {
    padding: '6px',
    gap: '3px',
    maxHeight: '160px',
    '& .subcategory-item': {
      padding: '4px 8px',
      fontSize: '0.8rem',
    }
  }
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  backgroundColor: '#FFFFFF',
  border: '1px solid rgba(255, 107, 44, 0.3)',
  color: '#FF6B2C',
  fontWeight: 500,
  fontSize: '0.875rem',
  height: '32px',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.08)',
    borderColor: '#FF6B2C',
  },
  '&.Mui-selected': {
    backgroundColor: '#FF6B2C',
    color: '#FFFFFF',
    '&:hover': {
      backgroundColor: '#FF7A35',
    }
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.8rem',
    height: '28px',
  },
  [theme.breakpoints.down('xs')]: {
    fontSize: '0.75rem',
    height: '26px',
  }
}));

const SearchContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  backgroundColor: '#FFFFFF',
  padding: '16px',
  borderRadius: '16px',
  marginBottom: '24px',
  boxShadow: '0 8px 24px rgba(255, 107, 44, 0.08)',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  backdropFilter: 'blur(20px)',
  zIndex: 2,
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: 'rgba(255, 107, 44, 0.2)',
    },
    '&:hover fieldset': {
      borderColor: 'rgba(255, 107, 44, 0.4)',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  [theme.breakpoints.down('sm')]: {
    padding: '12px',
    marginBottom: '16px',
    borderRadius: '12px',
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px',
    marginBottom: '14px',
  }
}));

const WarningMessage = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  padding: '12px 16px',
  backgroundColor: 'rgba(255, 107, 44, 0.1)',
  borderRadius: '8px',
  marginTop: '8px',
  border: '1px solid rgba(255, 107, 44, 0.15)',
  boxShadow: '0 2px 4px rgba(255, 107, 44, 0.05)',
  width: '100%',
  '& .warning-icon': {
    color: '#FF6B2C',
  },
  '& .warning-text': {
    color: '#FF6B2C',
    fontWeight: 500,
    fontSize: '0.875rem',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '8px 12px',
    gap: '6px',
    borderRadius: '6px',
    '& .warning-text': {
      fontSize: '0.8rem',
    },
    '& svg': {
      width: '18px',
      height: '18px'
    }
  }
}));

const FilteredSubcategoriesContainer = styled(Box)(({ theme }) => ({
  marginTop: '16px',
  padding: '16px',
  backgroundColor: 'rgba(255, 107, 44, 0.04)',
  borderRadius: '12px',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  display: 'flex',
  flexDirection: 'column',
  gap: '12px',
  [theme.breakpoints.down('sm')]: {
    marginTop: '12px',
    padding: '12px',
    gap: '8px',
    borderRadius: '10px',
  },
  [theme.breakpoints.down('xs')]: {
    marginTop: '10px',
    padding: '10px 8px',
    gap: '6px',
    borderRadius: '8px',
    border: '1px solid rgba(255, 107, 44, 0.08)'
  }
}));

const FilteredChipsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '8px',
  [theme.breakpoints.down('sm')]: {
    gap: '6px',
  },
  [theme.breakpoints.down('xs')]: {
    gap: '4px',
  }
}));

const MainContentWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
  padding: '24px',
  [theme.breakpoints.down('sm')]: {
    padding: '12px 8px',
    gap: '16px',
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px 6px',
    gap: '12px',
  }
}));


const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '16px',
  padding: '14px 32px',
  textTransform: 'none',
  fontWeight: 600,
  letterSpacing: '0.5px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&.MuiButton-contained': {
    background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
    color: 'white',
    boxShadow: '0 8px 24px rgba(255, 107, 44, 0.25)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 12px 30px rgba(255, 107, 44, 0.3)',
    },
    '&:active': {
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      background: 'linear-gradient(135deg, rgba(255, 107, 44, 0.3), rgba(255, 122, 53, 0.3))',
      boxShadow: 'none',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: '#FF7A35',
    color: '#FF7A35',
    borderWidth: '2px',
    '&:hover': {
      borderColor: '#FF6B2C',
      background: 'rgba(255, 107, 44, 0.04)',
      transform: 'translateY(-2px)',
    },
    '&:active': {
      transform: 'translateY(1px)',
    },
  },
  [theme.breakpoints.down('sm')]: {
    padding: '12px 24px',
    borderRadius: '12px',
    fontSize: '0.9rem'
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px 20px',
    borderRadius: '10px',
    fontSize: '0.85rem',
    borderWidth: '1px',
    letterSpacing: '0.3px'
  }
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    backgroundColor: '#FFFFFF',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: '#FF7A35',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: '#FF6B2C',
  },
  '& input[type="number"]': {
    MozAppearance: 'textfield',
    '&::-webkit-outer-spin-button, &::-webkit-inner-spin-button': {
      WebkitAppearance: 'none',
      margin: 0,
      display: 'none'
    }
  }
}));

const StyledSwitch = styled(Switch)(({ theme }) => ({
  '& .MuiSwitch-switchBase.Mui-checked': {
    color: '#FF6B2C',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.08)',
    },
  },
  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
    backgroundColor: '#FF965E',
  },
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  background: '#FFFFFF',
  borderRadius: '24px',
  padding: '15px',
  boxShadow: '0 15px 35px rgba(255, 107, 44, 0.08)',
  border: '1px solid rgba(255, 122, 53, 0.08)',
  backdropFilter: 'blur(20px)',
  margin: '8px',
}));

const StyledTimeGrid = styled(Grid)(({ theme }) => ({
  '& .MuiTextField-root': {
    background: '#FFFFFF',
    borderRadius: '12px',
    '& .MuiOutlinedInput-root': {
      '&:hover fieldset': {
        borderColor: '#FF7A35',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#FF6B2C',
      },
    },
  },
}));

const StyledModalTitle = styled('div')(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
  color: 'white',
  alignItems: 'center',
  padding: '16px 24px',
  borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
  '& .MuiIconButton-root': {
    color: 'white',
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(8px)',
    transition: 'all 0.3s ease',
    '&:hover': {
      background: 'rgba(255, 255, 255, 0.2)',
      transform: 'scale(1.05)',
    },
  },
  [theme.breakpoints.down('sm')]: {
    padding: '14px 16px',
    '& h6': {
      fontSize: '1.1rem'
    },
    '& svg': {
      width: '20px',
      height: '20px'
    }
  },
  [theme.breakpoints.down('xs')]: {
    padding: '12px 14px',
    '& h6': {
      fontSize: '1rem'
    },
    '& svg': {
      width: '18px',
      height: '18px'
    }
  }
}));

const StepperContainer = styled(Box)(({ theme }) => ({
  padding: '16px',
  borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
  background: '#FFFFFF',
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    padding: '12px 16px',
    display: 'none'
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px 14px'
  }
}));

const MobileStepIndicator = styled(Box)(({ theme }) => ({
  display: 'none',
  padding: '12px 16px',
  background: '#FFFFFF',
  borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
  [theme.breakpoints.down('sm')]: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  [theme.breakpoints.down('xs')]: {
    padding: '10px 14px',
    '& .MuiTypography-root': {
      fontSize: '0.85rem'
    }
  }
}));

const ResponsiveStepper = styled(Stepper)(({ theme }) => ({
  '& .MuiStepLabel-root': {
    flexDirection: 'row',
    alignItems: 'center',
  },
  '& .MuiStepLabel-labelContainer': {
    color: 'rgba(0, 0, 0, 0.6)',
  },
  '& .Mui-active': {
    '& .MuiStepIcon-root': {
      color: '#FF6B2C',
    },
    '& .MuiStepLabel-label': {
      color: '#FF6B2C',
      fontWeight: 600,
    }
  },
  '& .Mui-completed': {
    '& .MuiStepIcon-root': {
      color: '#FF7A35',
    }
  },
  '& .MuiStepLabel-label': {
    fontSize: '1rem',
    fontWeight: 500,
    transition: 'color 0.2s ease-in-out',
    [theme.breakpoints.down('md')]: {
      fontSize: '0.875rem',
    },
    [theme.breakpoints.down('sm')]: {
      fontSize: '0.75rem',
      marginTop: 0,
    }
  },
  '& .MuiStepIcon-root': {
    width: '32px',
    height: '32px',
    transition: 'all 0.2s ease-in-out',
    [theme.breakpoints.down('md')]: {
      width: '28px',
      height: '28px',
    },
    [theme.breakpoints.down('sm')]: {
      width: '24px',
      height: '24px',
    }
  },
  '& .MuiStepConnector-line': {
    borderColor: 'rgba(255, 107, 44, 0.2)',
  },
  '& .MuiStepIcon-text': {
    fill: '#FFFFFF',
    fontSize: '0.875rem',
    fontWeight: 500,
    [theme.breakpoints.down('md')]: {
      fontSize: '0.8rem',
    },
    [theme.breakpoints.down('sm')]: {
      fontSize: '0.75rem',
    }
  },
  [theme.breakpoints.down('sm')]: {
    '& .MuiStepConnector-root': {
      flex: '0.5',
      margin: '0 4px',
    },
    '& .MuiStep-root': {
      padding: 0,
    },
    '& .MuiStepLabel-iconContainer': {
      paddingRight: '8px',
    }
  },
  [theme.breakpoints.down(400)]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: '16px',
    '& .MuiStep-root': {
      width: '100%',
      marginBottom: '8px',
    },
    '& .MuiStepLabel-root': {
      padding: '8px 0',
    },
    '& .MuiStepConnector-root': {
      display: 'none',
    },
    '& .MuiStepLabel-label': {
      fontSize: '0.875rem',
    }
  }
}));

const ContentContainer = styled(DialogContent)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: '0px',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'rgba(255, 122, 53, 0.05)',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'rgba(255, 122, 53, 0.2)',
    borderRadius: '4px',
    '&:hover': {
      background: 'rgba(255, 122, 53, 0.3)',
    },
  },
}));

const NavigationContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: '12px 24px',
  borderTop: '1px solid rgba(255, 122, 53, 0.08)',
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(20px)',
  borderBottomLeftRadius: '24px',
  borderBottomRightRadius: '24px',
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    padding: '12px 16px',
    flexDirection: 'column',
    gap: '8px',
    '& .MuiButton-root': {
      width: '100%',
      minHeight: '44px'
    },
    '& .MuiBox-root': {
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px'
    }
  }
}));

const ServiceExistingBanner = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  padding: '6px 12px',
  backgroundColor: 'rgba(255, 107, 44, 0.75)',
  color: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '6px',
  zIndex: 3,
  '& .icon-container': {
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
  },
  '& .services-list': {
    fontSize: '0.8rem',
    fontWeight: 500,
  },
  [theme.breakpoints.down('sm')]: {
    padding: '4px 10px',
    gap: '4px',
    '& .icon-container': {
      gap: '4px',
    },
    '& .services-list': {
      fontSize: '0.75rem',
    },
    '& svg': {
      width: '16px',
      height: '16px'
    }
  },
  [theme.breakpoints.down('xs')]: {
    '& .icon-container': {
      '& span': {
        fontSize: '0.7rem'
      }
    },
    '& .services-list': {
      fontSize: '0.7rem',
      maxWidth: '60%',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis'
    },
    '& svg': {
      width: '14px',
      height: '14px'
    }
  }
}));

const HelpMessage = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  padding: '10px 16px',
  backgroundColor: 'rgba(255, 107, 44, 0.12)',
  borderRadius: '8px',
  marginTop: '4px',
  marginBottom: '8px',
  border: '1px solid rgba(255, 107, 44, 0.25)',
  boxShadow: '0 2px 4px rgba(255, 107, 44, 0.08)',
  width: '100%',
  animation: 'pulsate 2s ease-in-out infinite',
  '@keyframes pulsate': {
    '0%': { opacity: 0.9 },
    '50%': { opacity: 1 },
    '100%': { opacity: 0.9 },
  },
  '& .help-icon': {
    color: '#FF6B2C',
    width: '20px',
    height: '20px',
    flexShrink: 0
  },
  '& .help-text': {
    color: '#FF6B2C',
    fontSize: '0.9rem',
    lineHeight: '1.3',
    fontWeight: 600,
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  },
  [theme.breakpoints.down('sm')]: {
    padding: '8px 12px',
    '& .help-icon': {
      width: '18px',
      height: '18px',
    },
    '& .help-text': {
      fontSize: '0.85rem',
    }
  }
}));

const StyledNumberInput = styled('div')({
  position: 'relative',
  '& .spin-buttons': {
    display: 'flex',
    flexDirection: 'column',
    gap: '2px',
    marginRight: '-8px',
    marginLeft: '4px'
  },
  '& .spin-button': {
    background: 'none',
    border: 'none',
    padding: '4px',
    cursor: 'pointer',
    color: '#FF6B2C',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '4px',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.08)',
      color: '#FF7A35',
    },
    '&:active': {
      backgroundColor: 'rgba(255, 107, 44, 0.12)',
      transform: 'scale(0.95)',
    },
    '&:disabled': {
      color: 'rgba(0, 0, 0, 0.26)',
      cursor: 'not-allowed',
      '&:hover': {
        backgroundColor: 'transparent',
      },
    },
    '& svg': {
      width: '16px',
      height: '16px',
      strokeWidth: 2.5,
      transition: 'transform 0.2s ease',
    },
    '&:hover svg': {
      transform: 'scale(1.1)',
    },
    '&:active svg': {
      transform: 'scale(0.9)',
    }
  },
  '& .MuiInputAdornment-root': {
    marginLeft: '0',
    marginRight: '0',
  },
  '& input[type="number"]::-webkit-inner-spin-button, & input[type="number"]::-webkit-outer-spin-button': {
    WebkitAppearance: 'none',
    margin: 0,
  },
  '& input[type="number"]': {
    MozAppearance: 'textfield',
  }
});

const StyledModal = styled('div')(({ theme }) => ({
  position: 'fixed',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  borderRadius: '16px',
  padding: theme.spacing(2),
  backgroundColor: '#FFF8F3',
  maxWidth: '800px',
  width: '100%',
  margin: '16px',
  maxHeight: '90vh',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  zIndex: 1100,
}));

interface StepperServiceFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  existingServices: UserService[];
  editingService?: UserService | null;
}

const JOURS = [
  'lundi',
  'mardi',
  'mercredi',
  'jeudi',
  'vendredi',
  'samedi',
  'dimanche',
];

const JOURS_ORDER: { [key: string]: number } = {
  'lundi': 0,
  'mardi': 1,
  'mercredi': 2,
  'jeudi': 3,
  'vendredi': 4,
  'samedi': 5,
  'dimanche': 6,
};

interface Disponibilite {
  jour: string;
  debut: string;
  fin: string;
  disponible: boolean;
  id: string;
}

const StepperServiceForm: React.FC<StepperServiceFormProps> = ({
  open,
  onClose,
  onSubmit,
  existingServices = [],
  editingService = null,
}) => {
  const { createSystemNotification } = useCreateNotification();
  const { validateContentSafety } = useContentModeration();
  const [activeStep, setActiveStep] = useState(editingService ? 1 : 0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategory | null>(
    editingService
      ? SERVICE_CATEGORIES.find(cat => cat.id === editingService.category_id) || null
      : null
  );
  const [selectedSubcategory, setSelectedSubcategory] = useState<ServiceSubcategory | null>(
    editingService
      ? SERVICE_SUBCATEGORIES.find(sub => sub.id === editingService.subcategory_id) || null
      : null
  );
  const [formData, setFormData] = useState({
    titre: editingService?.titre || '',
    description: editingService?.description || '',
    tarif_horaire: editingService?.tarif_horaire.toString() || '',
  });
  const [remainingCharacters, setRemainingCharacters] = useState(1200);
  const [isModerationLoading, setIsModerationLoading] = useState(false);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);
  const [disponibilites, setDisponibilites] = useState<Disponibilite[]>(
    editingService ? Object.entries(editingService.horaires)
      .flatMap(([jour, horaire]: [string, any]) => {
        // Créer un tableau avec le créneau principal
        const creneaux = [{
          jour,
          debut: horaire.debut,
          fin: horaire.fin,
          disponible: horaire.disponible,
          id: Math.random().toString(36).substring(2, 9)
        }];

        // Ajouter les créneaux supplémentaires s'ils existent
        if (horaire.creneaux && horaire.creneaux.length > 0) {
          creneaux.push(...horaire.creneaux.map((creneau: any) => ({
            jour,
            debut: creneau.debut,
            fin: creneau.fin,
            disponible: creneau.disponible,
            id: Math.random().toString(36).substring(2, 9)
          })));
        }

        return creneaux;
      })
      .sort((a, b) => editingService ? JOURS_ORDER[a.jour] - JOURS_ORDER[b.jour] : 0)
    : []
  );
  const [selectedDay, setSelectedDay] = useState<string>('lundi');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isReglementationModalOpen, setIsReglementationModalOpen] = useState(false);
  const [reglementationAccepted, setReglementationAccepted] = useState(false);
  const modalContainerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<any>(null);

  const steps = ['Sélection du service', 'Informations', 'Disponibilités'];

  const normalizeString = (str: string) => {
    return str
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };

  // Filtrer les sous-catégories en fonction de la recherche
  const filteredSubcategories = useMemo(() => {
    if (!searchQuery) return [];

    const normalizedSearch = normalizeString(searchQuery);
    return SERVICE_SUBCATEGORIES
      .filter(sub => {
        const matchesName = normalizeString(sub.nom).includes(normalizedSearch);
        const matchesSynonyms = sub.synonymes?.some(syn => normalizeString(syn).includes(normalizedSearch));
        return matchesName || matchesSynonyms;
      })
      .sort((a, b) => normalizeString(a.nom).localeCompare(normalizeString(b.nom), 'fr', { sensitivity: 'base' }));
  }, [searchQuery]);

  // Filtrer les catégories qui contiennent des sous-catégories correspondant à la recherche
  const filteredCategories = useMemo(() => {
    if (!searchQuery) return SERVICE_CATEGORIES;

    const subcategoryIds = new Set(filteredSubcategories.map(sub => sub.categoryId));
    return SERVICE_CATEGORIES.filter(cat => subcategoryIds.has(cat.id));
  }, [searchQuery, filteredSubcategories]);

  // Vérifier si le service existe déjà dans la recherche
  const checkSearchedServiceExists = (searchTerm: string): string | null => {
    if (!searchTerm) return null;

    const normalizedSearch = normalizeString(searchTerm);
    const matchingSubcategories = SERVICE_SUBCATEGORIES.filter(sub =>
      normalizeString(sub.nom).includes(normalizedSearch)
    );

    const existingMatchingServices = existingServices.filter(service =>
      matchingSubcategories.some(sub => sub.id === service.subcategory_id)
    );

    if (existingMatchingServices.length > 0) {
      const existingServiceNames = existingMatchingServices
        .map(service => {
          const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === service.subcategory_id);
          return subcategory?.nom;
        })
        .filter(Boolean)
        .join(', ');

      return `Vous proposez déjà ${existingMatchingServices.length > 1 ? 'les services' : 'le service'} : ${existingServiceNames}`;
    }
    return null;
  };

  // Modifier la fonction getNextButtonState pour toujours retourner un objet du bon type
  const getNextButtonState = (): { disabled: boolean; message: string | null } => {
    if (activeStep === 0) {
      if (!selectedCategory) {
        return {
          disabled: true,
          message: "Veuillez sélectionner une catégorie"
        };
      }
      if (!selectedSubcategory) {
        return {
          disabled: true,
          message: "Veuillez sélectionner une sous-catégorie de service"
        };
      }
      if (existingServices.some(service =>
        service.category_id === selectedCategory.id &&
        service.subcategory_id === selectedSubcategory.id
      )) {
        return {
          disabled: true,
          message: "Vous proposez déjà ce service"
        };
      }
      return { disabled: false, message: null };
    }

    if (activeStep === 1) {
      if (!formData.titre || formData.titre.length < 4) {
        return {
          disabled: true,
          message: "Le titre doit contenir au moins 4 caractères"
        };
      }
      if (!formData.description || formData.description.length < 20) {
        return {
          disabled: true,
          message: "La description doit contenir au moins 20 caractères"
        };
      }
      // Vérifier la longueur maximale de la description
      const descriptionLength = formData.description.replace(/<[^>]*>/g, '').length;
      if (descriptionLength > 1200) {
        return {
          disabled: true,
          message: "La description ne doit pas dépasser 1200 caractères"
        };
      }
      if (!formData.tarif_horaire) {
        return {
          disabled: true,
          message: "Veuillez définir un tarif horaire"
        };
      }
      return { disabled: false, message: null };
    }

    return { disabled: false, message: null };
  };

  // Gestionnaire de sélection de catégorie modifié
  const handleCardClick = (category: ServiceCategory, index: number, event: React.MouseEvent) => {
    event.preventDefault();
    setSelectedCategory(category);
    setSelectedSubcategory(null);
  };

  // Modifier le gestionnaire de recherche
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setSelectedCategory(null);
    setSelectedSubcategory(null);
  };

  const handleTitreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const sanitizedValue = DOMPurify.sanitize(rawValue);

    if (sanitizedValue.length <= 40) {
      const formattedValue = sanitizedValue.charAt(0).toUpperCase() + sanitizedValue.slice(1).toLowerCase();
      setFormData(prev => ({
        ...prev,
        titre: formattedValue
      }));
    }
  };

  const handleDescriptionChange = (content: string) => {
    const sanitizedContent = DOMPurify.sanitize(content);
    const textLength = sanitizedContent.replace(/<[^>]*>/g, '').length;

    // Mettre à jour le contenu sans condition de longueur
    setFormData(prev => ({
      ...prev,
      description: sanitizedContent
    }));
    
    // Mettre à jour le nombre de caractères restants (peut être négatif)
    setRemainingCharacters(1200 - textLength);
  };

  // Fonction pour convertir le HTML en texte brut
  const stripHtml = (html: string) => {
    if (!html) return '';
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const handleTarifChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const sanitizedValue = DOMPurify.sanitize(rawValue);

    if (sanitizedValue === '') {
      setFormData(prev => ({ ...prev, tarif_horaire: '' }));
    } else {
      const numericValue = parseFloat(sanitizedValue);
      if (!isNaN(numericValue) && numericValue >= 0 && numericValue <= 999) {
        setFormData(prev => ({ ...prev, tarif_horaire: sanitizedValue }));
      }
    }
  };

  // Navigation
  const handleNext = async () => {
    if (activeStep === 0) {
      if (!selectedCategory) {
        notify('Veuillez sélectionner une catégorie', 'warning');
        return;
      }

      // Vérifier la réglementation après la sélection du service
      if (selectedSubcategory?.secteur_avec_reglementation && !reglementationAccepted) {
        setIsReglementationModalOpen(true);
        return;
      }
    }

    if (activeStep === 1) {
      if (!formData.titre) {
        notify('Veuillez saisir un titre pour votre service', 'warning');
        return;
      }
      if (!formData.description) {
        notify('Veuillez saisir une description pour votre service', 'warning');
        return;
      }
      if (!formData.tarif_horaire) {
        notify('Veuillez définir un tarif horaire', 'warning');
        return;
      }

      // Vérifier le contenu avec la modération
      setIsModerationLoading(true);
      try {
        // Vérifier le titre
        const isTitleSafe = await validateContentSafety(formData.titre, 'titre_service');
        if (!isTitleSafe) {
          setIsModerationLoading(false);
          return;
        }

        // Extraire le texte brut de la description (sans HTML)
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formData.description;
        const textContent = tempDiv.textContent || tempDiv.innerText;

        // Vérifier la description
        const isDescriptionSafe = await validateContentSafety(textContent, 'description_service');
        if (!isDescriptionSafe) {
          setIsModerationLoading(false);
          return;
        }
      } catch (error) {
        logger.error('Erreur lors de la modération du contenu:', error);
        notify('Une erreur est survenue lors de la vérification du contenu. Veuillez réessayer.', 'error');
        setIsModerationLoading(false);
        return;
      }
      setIsModerationLoading(false);
    }

    setActiveStep((prevStep) => prevStep + 1);

    // Faire défiler vers le haut immédiatement après le changement d'étape
    setTimeout(() => {
      try {
        // Faire défiler le conteneur de contenu avec la classe spécifique
        const contentContainer = document.querySelector('.stepper-content-container');
        if (contentContainer instanceof HTMLElement) {
          contentContainer.scrollTop = 0;
        }

        // Faire défiler le conteneur de la modal
        if (modalContainerRef.current) {
          modalContainerRef.current.scrollTop = 0;
        }

        // Faire défiler la fenêtre
        window.scrollTo(0, 0);
      } catch (error) {
        logger.error('Erreur lors du défilement de la modal:', error);
      }
    }, 50);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);

    // Faire défiler vers le haut immédiatement après le changement d'étape
    setTimeout(() => {
      try {
        // Faire défiler le conteneur de contenu avec la classe spécifique
        const contentContainer = document.querySelector('.stepper-content-container');
        if (contentContainer instanceof HTMLElement) {
          contentContainer.scrollTop = 0;
        }

        // Faire défiler le conteneur de la modal
        if (modalContainerRef.current) {
          modalContainerRef.current.scrollTop = 0;
        }

        // Faire défiler la fenêtre
        window.scrollTo(0, 0);
      } catch (error) {
        logger.error('Erreur lors du défilement de la modal:', error);
      }
    }, 50);
  };

  // Fonction pour vérifier si deux créneaux se chevauchent
  const checkOverlap = (debut1: string, fin1: string, debut2: string, fin2: string): boolean => {
    const d1 = new Date(`2000-01-01T${debut1}`);
    const f1 = new Date(`2000-01-01T${fin1}`);
    const d2 = new Date(`2000-01-01T${debut2}`);
    const f2 = new Date(`2000-01-01T${fin2}`);
    return d1 < f2 && d2 < f1;
  };

  // Fonction pour fusionner les créneaux qui se chevauchent
  const mergeOverlappingSlots = (slots: Disponibilite[]): Disponibilite[] => {
    if (slots.length <= 1) return slots;

    // Trier les créneaux par heure de début
    const sortedSlots = [...slots].sort((a, b) => {
      const timeA = new Date(`2000-01-01T${a.debut}`);
      const timeB = new Date(`2000-01-01T${b.debut}`);
      return timeA.getTime() - timeB.getTime();
    });

    const mergedSlots: Disponibilite[] = [];
    let currentSlot = sortedSlots[0];

    for (let i = 1; i < sortedSlots.length; i++) {
      const nextSlot = sortedSlots[i];
      if (checkOverlap(currentSlot.debut, currentSlot.fin, nextSlot.debut, nextSlot.fin)) {
        // Fusionner les créneaux
        const debutTime = new Date(`2000-01-01T${currentSlot.debut}`);
        const nextDebutTime = new Date(`2000-01-01T${nextSlot.debut}`);
        const finTime = new Date(`2000-01-01T${currentSlot.fin}`);
        const nextFinTime = new Date(`2000-01-01T${nextSlot.fin}`);

        currentSlot = {
          ...currentSlot,
          debut: debutTime <= nextDebutTime ? currentSlot.debut : nextSlot.debut,
          fin: finTime >= nextFinTime ? currentSlot.fin : nextSlot.fin,
          disponible: currentSlot.disponible || nextSlot.disponible
        };
      } else {
        mergedSlots.push(currentSlot);
        currentSlot = nextSlot;
      }
    }
    mergedSlots.push(currentSlot);

    return mergedSlots;
  };

  const handleAddDay = () => {
    const newDispo: Disponibilite = {
      jour: selectedDay,
      debut: '09:00',
      fin: '18:00',
      disponible: true,
      id: Math.random().toString(36).substring(2, 9)
    };

    // Ajouter le nouveau créneau au début du tableau
    setDisponibilites([newDispo, ...disponibilites]);
  };

  const handleRemoveDay = (dispoId: string) => {
    setDisponibilites(disponibilites.filter(d => d.id !== dispoId));
  };

  const handleHoraireChange = (dispoId: string, field: 'debut' | 'fin' | 'disponible', value: string | boolean) => {
    // Mise à jour simple sans fusion des créneaux
    const updatedDisponibilites = disponibilites.map(dispo => {
      if (dispo.id === dispoId) {
        return { ...dispo, [field]: value };
      }
      return dispo;
    });
    setDisponibilites(updatedDisponibilites);
  };

  const handleSetAllWeekAvailability = () => {
    const newDisponibilites = JOURS.map((jour) => ({
      jour,
      debut: '09:00',
      fin: '18:00',
      disponible: true,
      id: `${jour}-${Math.random().toString(36).substring(2, 9)}`
    }));
    setDisponibilites(newDisponibilites);
  };

  const handleRemoveAllDays = () => {
    setDisponibilites([]);
  };

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const handleSubmitForm = async () => {
    try {
      // Vérifier le contenu avec la modération avant de soumettre
      setIsModerationLoading(true);

      // Vérifier le titre
      const isTitleSafe = await validateContentSafety(formData.titre, 'titre_service');
      if (!isTitleSafe) {
        setIsModerationLoading(false);
        return;
      }

      // Extraire le texte brut de la description (sans HTML)
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = formData.description;
      const textContent = tempDiv.textContent || tempDiv.innerText;

      // Vérifier la description
      const isDescriptionSafe = await validateContentSafety(textContent, 'description_service');
      if (!isDescriptionSafe) {
        setIsModerationLoading(false);
        return;
      }

      // Regrouper et fusionner les créneaux qui se chevauchent par jour
      const mergedDisponibilites = JOURS.reduce((acc, jour) => {
        const jourSlots = disponibilites.filter(d => d.jour === jour);
        if (jourSlots.length > 0) {
          // Fusionner les créneaux qui se chevauchent pour ce jour
          const mergedSlots = mergeOverlappingSlots(jourSlots);
          // On prend le premier créneau comme créneau principal
          acc[jour] = {
            debut: mergedSlots[0].debut,
            fin: mergedSlots[0].fin,
            disponible: mergedSlots[0].disponible,
            // On ajoute les créneaux supplémentaires uniquement s'il y en a plus d'un
            creneaux: mergedSlots.length > 1 ? mergedSlots.slice(1).map(slot => ({
              debut: slot.debut,
              fin: slot.fin,
              disponible: slot.disponible
            })) : []
          };
        }
        return acc;
      }, {} as { [key: string]: { debut: string; fin: string; disponible: boolean; creneaux: Array<{ debut: string; fin: string; disponible: boolean }> } });

      const serviceData = {
        category_id: selectedCategory!.id,
        subcategory_id: selectedSubcategory!.id,
        titre: formData.titre,
        description: formData.description,
        tarif_horaire: parseFloat(formData.tarif_horaire),
        horaires: mergedDisponibilites,
        categorie: [selectedCategory!.nom, selectedSubcategory!.nom]
      };

      if (editingService) {
        await serviceApi.updateService(editingService.id, serviceData);
        notify('Service modifié avec succès', 'success');
      } else {
        await serviceApi.createService(serviceData);
        notify('Service créé avec succès', 'success');

        await createSystemNotification(
          'Nouveau service',
          `Votre service "${serviceData.titre}" a été créé avec succès, félicitations !`,
          `/dashboard/profil`
        );
      }
      setIsModerationLoading(false);
      onSubmit();
      onClose();
    } catch (error) {
      setIsModerationLoading(false);
      notify(`Erreur lors de la ${editingService ? 'modification' : 'création'} du service`, 'error');
      logger.error('Erreur:', error);
    }
  };

  // Gestionnaire de clic sur le conteneur principal
  const handleMainContentClick = (event: React.MouseEvent) => {
    // Vérifie si le clic est sur le conteneur principal et non sur une carte
    if (event.target === event.currentTarget) {
      setSelectedCategory(null);
      setSelectedSubcategory(null);
    }
  };

  // Rendu de l'étape de sélection de catégorie
  const renderCategoryStep = () => {
    const existingServiceMessage = checkSearchedServiceExists(searchQuery);

    return (
      <MainContentWrapper onClick={handleMainContentClick}>
        <SearchContainer>
          <TextField
            fullWidth
            placeholder="Rechercher un service..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={20} />
                </InputAdornment>
              ),
              endAdornment: searchQuery && (
                <InputAdornment position="end">
                  <IconButton onClick={() => handleSearch('')} size="small">
                    <XCircle size={20} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {existingServiceMessage && (
            <WarningMessage>
              <Info className="warning-icon" size={20} />
              <Typography className="warning-text">
                {existingServiceMessage}
              </Typography>
            </WarningMessage>
          )}

          {searchQuery && (
            <FilteredSubcategoriesContainer>
              <Typography variant="subtitle2" sx={{
                color: '#FF6B2C',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: '0.875rem'
              }}>
                <Check size={16} />
                {filteredSubcategories.length} résultat{filteredSubcategories.length > 1 ? 's' : ''} trouvé{filteredSubcategories.length > 1 ? 's' : ''}
              </Typography>
              <FilteredChipsContainer>
                {filteredSubcategories.map((subcategory) => {
                  const category = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId);
                  return (
                    <StyledChip
                      key={subcategory.id}
                      label={`${subcategory.nom} (dans ${category?.nom})`}
                      onClick={() => {
                        setSelectedCategory(category || null);
                        setSelectedSubcategory(subcategory);
                        // Scroll vers la sous-catégorie sélectionnée
                        setTimeout(() => {
                          const subcategoryElement = document.getElementById(`subcategory-${subcategory.id}`);
                          if (subcategoryElement) {
                            subcategoryElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                          }
                        }, 100);
                      }}
                      className={selectedSubcategory?.id === subcategory.id ? 'Mui-selected' : ''}
                    />
                  );
                })}
              </FilteredChipsContainer>
            </FilteredSubcategoriesContainer>
          )}
        </SearchContainer>

        <Grid
          container spacing={3}
        >
          {filteredCategories
            .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
            .map((category, index) => {
              const categorySubcategories = SERVICE_SUBCATEGORIES
                .filter(sub => sub.categoryId === category.id)
                .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'));

              const hasExistingService = Array.isArray(existingServices) && categorySubcategories.some(sub =>
                existingServices.some(service =>
                  service.category_id === category.id &&
                  service.subcategory_id === sub.id
                )
              );

              const isVisible = !searchQuery ||
                normalizeString(category.nom).includes(normalizeString(searchQuery)) ||
                normalizeString(category.description).includes(normalizeString(searchQuery)) ||
                categorySubcategories.length > 0;

              if (!isVisible) return null;

              return (
                <Grid
                  size={{ xs: 12, sm: 6, md: 4 }}
                  key={category.id}
                  sx={{
                    display: 'flex',
                    width: { xs: '100%', sm: 'auto' },
                    flexGrow: { xs: 1, sm: 'unset' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <StyledCard
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCardClick(category, index, e);
                    }}
                    className={`
                      ${selectedCategory?.id === category.id ? 'selected' : ''}
                      ${searchQuery && categorySubcategories.length > 0 ? 'filtered' : ''}
                      ${hasExistingService ? 'has-existing-service' : ''}
                    `}
                    data-category-id={category.id}
                  >
                    <CardImage>
                      <picture>
                        <source srcSet={category.image.webp} type="image/webp" />
                        <img
                          src={category.image.jpg}
                          alt={category.image.alt}
                          className="card-image"
                        />
                      </picture>
                      {hasExistingService && (
                        <ServiceExistingBanner>
                          <div className="icon-container">
                            <Info size={18} />
                            <span>Service(s) déjà proposé(s) :</span>
                          </div>
                          <div className="services-list">
                            {categorySubcategories
                              .filter(sub => existingServices.some(service =>
                                service.subcategory_id === sub.id
                              ))
                              .map(sub => sub.nom)
                              .join(', ')}
                          </div>
                        </ServiceExistingBanner>
                      )}
                      <CardOverlay className="card-overlay">
                        <CategoryTitle variant="h5">
                          {category.nom}
                        </CategoryTitle>
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'rgba(255, 255, 255, 0.9)',
                            mt: 1,
                            textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                            fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.875rem' },
                            display: { xs: '-webkit-box', sm: 'block' },
                            WebkitLineClamp: { xs: 2, sm: 3 },
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: { xs: 1.3, sm: 1.5 }
                          }}
                        >
                          {category.description}
                        </Typography>
                        {searchQuery && categorySubcategories.length > 0 && (
                          <Typography
                            variant="body2"
                            sx={{
                              color: '#FF6B2C',
                              mt: 1,
                              fontWeight: 600,
                              backgroundColor: 'rgba(255, 255, 255, 0.9)',
                              padding: { xs: '2px 6px', sm: '4px 8px' },
                              borderRadius: '8px',
                              display: 'inline-flex',
                              alignItems: 'center',
                              gap: 0.5,
                              fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' }
                            }}
                          >
                            <Check size={16} />
                            {categorySubcategories.length} sous-catégorie{categorySubcategories.length > 1 ? 's' : ''}
                          </Typography>
                        )}
                      </CardOverlay>
                    </CardImage>
                    {(searchQuery || selectedCategory?.id === category.id) && categorySubcategories.length > 0 && (
                      <SubcategoriesContainer className="subcategories-container" style={{ opacity: 1, visibility: 'visible', transform: 'translateY(0)' }}>
                        {SERVICE_SUBCATEGORIES.filter(sub => sub.categoryId === category.id)
                          .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                          .map((sub) => {
                            const matchesSearch = searchQuery && normalizeString(sub.nom).includes(normalizeString(searchQuery));
                            const isExisting = Array.isArray(existingServices) && existingServices.some(service =>
                              service.subcategory_id === sub.id
                            );

                            return isExisting ? (
                              <Typography
                                key={sub.id}
                                className="subcategory-item existing"
                                variant="body2"
                              >
                                {sub.nom} <span className="existing-label">Déjà proposé</span>
                              </Typography>
                            ) : (
                              <Typography
                                key={sub.id}
                                id={`subcategory-${sub.id}`}
                                className={`subcategory-item ${matchesSearch ? 'highlight' : ''} ${selectedSubcategory?.id === sub.id ? 'selected' : ''}`}
                                variant="body2"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Sélectionner la sous-catégorie
                                  setSelectedSubcategory(sub);
                                  // Sélectionner automatiquement la catégorie parente si elle n'est pas déjà sélectionnée
                                  if (!selectedCategory || selectedCategory.id !== category.id) {
                                    setSelectedCategory(category);
                                  }
                                }}
                                sx={{
                                  cursor: 'pointer',
                                  '&:hover': {
                                    backgroundColor: selectedSubcategory?.id === sub.id ? '#FF7A35' : 'rgba(255, 107, 44, 0.08)',
                                  }
                                }}
                              >
                                {sub.nom}
                              </Typography>
                            );
                          })}
                      </SubcategoriesContainer>
                    )}
                  </StyledCard>
                </Grid>
              );
            })}
        </Grid>
      </MainContentWrapper>
    );
  };

  // Rendu de l'étape des informations
  const renderInformationStep = () => (
    <Box>
      <motion.div>
        <StyledPaper elevation={0}>
          <Typography variant="h6" gutterBottom sx={{ color: '#FF6B2C', mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
            <FileText size={24} />
            {selectedCategory?.nom} - {selectedSubcategory?.nom}
          </Typography>

          <Grid container spacing={3}>
            <Grid size={12}>
              <StyledTextField
                fullWidth
                label="Titre du service"
                value={formData.titre}
                onChange={handleTitreChange}
                helperText={`${formData.titre.length}/40 caractères`}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <FileText size={20} className="text-[#FF6B2C]" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#FFFFFF',
                  }
                }}
              />
            </Grid>

            <Grid size={12}>
              <Box sx={{ position: 'relative' }}>
                <TiptapEditor
                  ref={editorRef}
                  content={formData.description}
                  onChange={handleDescriptionChange}
                  placeholder="Saisissez votre description..."
                  // maxLength={500}
                  withIcon
                />
                <Box sx={{
                  position: 'absolute',
                  left: '12px',
                  top: '83px',
                  zIndex: 1,
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <FileText size={20} className="text-[#FF6B2C]" />
                </Box>
              </Box>

              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                mt: 1,
                mb: 1
              }}>
                <Typography
                  variant="caption"
                  sx={{
                    color: remainingCharacters < 0 ? 'error.main' : remainingCharacters < 50 ? '#FF6B2C' : 'text.secondary',
                    fontWeight: remainingCharacters < 0 ? 700 : remainingCharacters < 50 ? 600 : 500,
                    fontSize: remainingCharacters < 0 ? '0.85rem' : '0.75rem',
                    backgroundColor: remainingCharacters < 0 ? 'rgba(211, 47, 47, 0.1)' : 'transparent',
                    padding: remainingCharacters < 0 ? '4px 8px' : '0',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}
                >
                  {remainingCharacters < 0 && <Info size={14} />}
                  {remainingCharacters}/1200 caractères {remainingCharacters < 0 ? 'dépassés' : 'restants'}
                </Typography>
                <Button
                  onClick={() => setIsAiConfirmModalOpen(true)}
                  disabled={isModerationLoading || !formData.titre}
                  className="flex items-center gap-2 px-4 py-2 bg-[#FFF8F3] text-[#FF6B2C] rounded-lg hover:bg-[#FFE4BA] transition-colors"
                  startIcon={<Sparkles className="h-4 w-4" />}
                  sx={{
                    backgroundColor: '#FFF8F3',
                    color: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: '#FFE4BA',
                    },
                    '&.Mui-disabled': {
                      color: 'rgba(255, 107, 44, 0.5)',
                      backgroundColor: 'rgba(255, 248, 243, 0.5)',
                    }
                  }}
                >
                  {stripHtml(formData.description).length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
                  <Tooltip
                    title={stripHtml(formData.description).length > 0
                      ? "Améliorer le contenu existant avec l'IA"
                      : "Générer du nouveau contenu avec l'IA"}
                    sx={{ ml: 1 }}
                  >
                    <HelpCircle className="h-4 w-4 ml-1" />
                  </Tooltip>
                </Button>
              </Box>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ maxWidth: '350px' }}>
                <StyledNumberInput>
                  <StyledTextField
                    fullWidth
                    type="number"
                    label="Tarif horaire (€)"
                    value={formData.tarif_horaire}
                    onChange={handleTarifChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Euro className="text-[#FF6B2C]" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <div className="spin-buttons">
                            <button
                              type="button"
                              className="spin-button"
                              onClick={() => {
                                const newValue = formData.tarif_horaire === '' ? '1' : Math.min(999, +formData.tarif_horaire + 1).toString();
                                setFormData(prev => ({
                                  ...prev,
                                  tarif_horaire: newValue
                                }));
                              }}
                            >
                              <ChevronUp />
                            </button>
                            <button
                              type="button"
                              className="spin-button"
                              onClick={() => {
                                const newValue = formData.tarif_horaire === '' ? '0' : Math.max(0, +formData.tarif_horaire - 1).toString();
                                setFormData(prev => ({
                                  ...prev,
                                  tarif_horaire: newValue
                                }));
                              }}
                            >
                              <ChevronDown />
                            </button>
                          </div>
                        </InputAdornment>
                      ),
                      inputProps: {
                        min: 0,
                        max: 999,
                        step: 1,
                        style: { textAlign: 'left', paddingLeft: '10px' }
                      },
                    }}
                    helperText="Le tarif doit être compris entre 0 et 999 €"
                  />
                </StyledNumberInput>
              </Box>
            </Grid>
          </Grid>

          <Box mt={4}>
            <Typography variant="subtitle1" sx={{ color: '#FF6B2C', mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Check size={20} />
              Aperçu de votre service
            </Typography>
            <StyledPaper elevation={0} sx={{ background: '#FFF8F3' }}>
              <Typography variant="h6" gutterBottom sx={{ color: '#FF6B2C' }}>
                {formData.titre || 'Titre du service'}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                paragraph
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(formData.description) || 'Description de votre service...'
                }}
              />
              <Typography variant="subtitle1" sx={{ color: '#FF6B2C', fontWeight: 600 }}>
                {formData.tarif_horaire ? `${formData.tarif_horaire} €/h` : 'Tarif à définir'}
              </Typography>
            </StyledPaper>
          </Box>
        </StyledPaper>
      </motion.div>
    </Box>
  );

  // Rendu de l'étape des disponibilités
  const renderDisponibilitesStep = () => (
    <Box sx={{ p: 3 }}>
      <motion.div
        // initial={{ opacity: 0, y: 20 }}
        // animate={{ opacity: 1, y: 0 }}
        // transition={{ duration: 0.3 }}
      >
        <StyledPaper>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 2,
            mb: 3,
            width: '100%'
          }}>
          </Box>

          <Box sx={{ mb: 3 }}>
            <FormControl fullWidth>
              <InputLabel
                id="jour-select-label"
                sx={{
                  color: "rgba(0, 0, 0, 0.6)",
                  "&.Mui-focused": {
                    color: "#FF7A35",
                  },
                  "&:hover": {
                    color: "#FF7A35",
                  },
                }}
              >
                Ajouter un jour
              </InputLabel>
              <Select
                variant="outlined"
                labelId="jour-select-label"
                value={selectedDay}
                label="Ajouter un jour"
                onChange={(e) => setSelectedDay(e.target.value)}
                sx={{
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 107, 44, 0.9)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#FF7A35",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#FF7A35",
                  },
                }}
              >
                {JOURS.map((jour) => (
                  <MenuItem key={jour} value={jour}>
                    {capitalizeFirstLetter(jour)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              fullWidth
              variant="contained"
              onClick={handleAddDay}
              sx={{
                mt: 1,
                backgroundColor: 'rgba(255, 107, 44, 0.9)',
                '&:hover': {
                  backgroundColor: '#FF7A35',
                },
                height: { xs: '40px', sm: '48px' },
                borderRadius: '12px',
                boxShadow: 'none'
              }}
              startIcon={<Plus size={18} />}
            >
              Ajouter ce créneau
            </Button>
          </Box>

          <Grid container spacing={2}>
            {disponibilites
              // Supprimer le tri pendant l'édition
              .map((dispo) => (
                <Grid size={12} key={dispo.id}>
                  <motion.div>
                    <StyledPaper elevation={0}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6" sx={{ color: '#FF6B2C', display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Calendar size={20} />
                          {capitalizeFirstLetter(dispo.jour)}
                        </Typography>
                        <IconButton
                          onClick={() => handleRemoveDay(dispo.id)}
                          size="small"
                          sx={{
                            color: '#d32f2f',
                            '&:hover': {
                              background: 'rgba(211, 47, 47, 0.08)'
                            }
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                      </Box>

                      <StyledTimeGrid container spacing={2} alignItems="center">
                        <Grid size={{ xs: 12, sm: 5 }}>
                          <StyledTextField
                            type="time"
                            label="Début"
                            value={dispo.debut}
                            onChange={(e) => handleHoraireChange(dispo.id, 'debut', e.target.value)}
                            InputLabelProps={{ shrink: true }}
                            fullWidth
                          />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 2 }}>
                          <Typography variant="body1" align="center" sx={{ color: '#FF6B2C' }}>à</Typography>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 5 }}>
                          <StyledTextField
                            type="time"
                            label="Fin"
                            value={dispo.fin}
                            onChange={(e) => handleHoraireChange(dispo.id, 'fin', e.target.value)}
                            InputLabelProps={{ shrink: true }}
                            fullWidth
                          />
                        </Grid>
                      </StyledTimeGrid>

                      <Box mt={2}>
                        <FormControlLabel
                          control={
                            <StyledSwitch
                              checked={dispo.disponible}
                              onChange={(e) => handleHoraireChange(dispo.id, 'disponible', e.target.checked)}
                            />
                          }
                          label={
                            <Typography variant="body2" sx={{ color: dispo.disponible ? '#FF6B2C' : 'text.secondary' }}>
                              {dispo.disponible ? 'Disponible' : 'Non disponible'}
                            </Typography>
                          }
                        />
                      </Box>
                    </StyledPaper>
                  </motion.div>
                </Grid>
              ))}
          </Grid>
        </StyledPaper>
      </motion.div>
    </Box>
  );

  // Effet pour faire remonter la modal en haut à chaque changement d'étape
  useEffect(() => {
    // Utiliser un délai plus long pour s'assurer que le contenu est rendu
    setTimeout(() => {
      // Solution pour les appareils mobiles
      const isMobile = window.innerWidth < 768;

      if (isMobile) {
        try {
          // Essayer plusieurs approches pour s'assurer que la modal remonte en haut

          // 1. Faire défiler le conteneur de contenu avec la classe spécifique
          const contentContainer = document.querySelector('.stepper-content-container');
          if (contentContainer instanceof HTMLElement) {
            contentContainer.scrollTop = 0;
          }

          // 2. Faire défiler le conteneur de la modal
          if (modalContainerRef.current) {
            modalContainerRef.current.scrollTop = 0;
          }

          // 3. Faire défiler la fenêtre
          window.scrollTo(0, 0);

          // 4. Faire défiler jusqu'à l'indicateur d'étape mobile
          const mobileStepIndicator = document.querySelector('.mobile-step-indicator');
          if (mobileStepIndicator instanceof HTMLElement) {
            mobileStepIndicator.scrollIntoView({ behavior: 'auto', block: 'start' });
          }

          // 5. Faire défiler jusqu'au titre de la modal
          const modalTitle = document.querySelector('.MuiTypography-h6');
          if (modalTitle instanceof HTMLElement) {
            modalTitle.scrollIntoView({ behavior: 'auto', block: 'start' });
          }

          logger.info('Défilement de la modal effectué');
        } catch (error) {
          logger.error('Erreur lors du défilement de la modal:', error);
        }
      } else {
        // Pour les appareils de bureau, utiliser l'approche standard
        const contentContainer = document.querySelector('.stepper-content-container');
        if (contentContainer instanceof HTMLElement) {
          contentContainer.scrollTop = 0;
        }

        if (modalContainerRef.current) {
          modalContainerRef.current.scrollTop = 0;
        }
      }
    }, 250); // Délai augmenté pour s'assurer que le contenu est rendu
  }, [activeStep]);

  return (
    <>
      {open && (
        <ModalPortal onBackdropClick={onClose}>
          <ModalContainer onClick={e => e.stopPropagation()} ref={modalContainerRef}>
            <StyledModalTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Clock size={24} />
                <Typography variant="h6">
                  {editingService ? 'Modifier le service' : 'Ajouter un nouveau service'}
                </Typography>
              </Box>
              <IconButton onClick={onClose} size="small">
                <XCircle />
              </IconButton>
            </StyledModalTitle>

            <StepperContainer>
              <ResponsiveStepper activeStep={activeStep}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </ResponsiveStepper>
            </StepperContainer>

            <MobileStepIndicator className="mobile-step-indicator">
              <Typography variant="body2" sx={{ color: '#FF6B2C' }}>
                Étape {activeStep + 1} sur {steps.length}: {steps[activeStep]}
              </Typography>
            </MobileStepIndicator>

            <ContentContainer className="stepper-content-container">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeStep}
                  // initial={{ opacity: 0, x: 10 }}
                  // animate={{ opacity: 1, x: 0 }}
                  // exit={{ opacity: 0, x: -10 }}
                  // transition={{ duration: 0.2 }}
                >
                  {activeStep === 0 && renderCategoryStep()}
                  {activeStep === 1 && renderInformationStep()}
                  {activeStep === 2 && renderDisponibilitesStep()}
                </motion.div>
              </AnimatePresence>
            </ContentContainer>

            <NavigationContainer>
              <Box sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: 1
              }}>
                {(activeStep === steps.length - 1 && disponibilites.length === 0) || getNextButtonState().message ? (
                  <HelpMessage>
                    <Info className="help-icon" />
                    <Typography className="help-text">
                      {activeStep === steps.length - 1 && disponibilites.length === 0
                        ? "Veuillez ajouter au moins une disponibilité"
                        : getNextButtonState().message}
                    </Typography>
                  </HelpMessage>
                ) : null}

                <Box sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: { xs: 1, sm: 2 },
                  width: '100%',
                  mb: { xs: 2, sm: 0 }
                }}>
                  {activeStep === steps.length - 1 && (
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <StyledButton
                        variant="contained"
                        onClick={handleSetAllWeekAvailability}
                        startIcon={<Calendar size={18} />}
                        sx={{
                          backgroundColor: 'rgba(255, 107, 44, 0.9)',
                          '&:hover': {
                            backgroundColor: '#FF7A35',
                          },
                          height: { xs: '40px', sm: '48px' },
                          borderRadius: '12px',
                          boxShadow: 'none',
                          fontSize: { xs: '0.8125rem', sm: '0.875rem' },
                          whiteSpace: 'nowrap',
                          flex: { xs: 'none', sm: 1 }
                        }}
                      >
                        Ajouter des horaires par défaut
                      </StyledButton>

                      {disponibilites.length > 3 && (
                        <StyledButton
                          variant="outlined"
                          onClick={handleRemoveAllDays}
                          startIcon={<Trash2 size={18} />}
                          sx={{
                            borderColor: '#FF6B2C',
                            color: '#FF6B2C',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              borderColor: '#FF7A35'
                            },
                            height: { xs: '40px', sm: '48px' },
                            borderRadius: '12px',
                            boxShadow: 'none',
                            fontSize: { xs: '0.8125rem', sm: '0.875rem' },
                            whiteSpace: 'nowrap',
                            flex: { xs: 'none', sm: 1 }
                          }}
                        >
                          Supprimer toutes les horaires
                        </StyledButton>
                      )}
                    </Box>
                  )}
                </Box>

                <Box sx={{
                  display: 'flex',
                  gap: { xs: 1, sm: 2 },
                  width: '100%',
                  justifyContent: 'flex-end'
                }}>
                  {activeStep > 0 && (
                    <StyledButton
                      onClick={handleBack}
                      startIcon={<ArrowLeft size={18} />}
                      variant="outlined"
                      sx={{
                        minWidth: { xs: '45%', sm: '100px' },
                        height: { xs: '40px', sm: '48px' },
                        fontSize: { xs: '0.8125rem', sm: '0.875rem' }
                      }}
                    >
                      Retour
                    </StyledButton>
                  )}

                  <StyledButton
                    variant="contained"
                    onClick={activeStep === steps.length - 1 ? handleSubmitForm : handleNext}
                    disabled={(activeStep === steps.length - 1 ? disponibilites.length === 0 : getNextButtonState().disabled) || isModerationLoading}
                    endIcon={
                      isModerationLoading ?
                        <CircularProgress size={16} color="inherit" /> :
                        (activeStep === steps.length - 1 ? <Check size={18} /> : <ArrowRight size={18} />)
                    }
                    sx={{
                      minWidth: { xs: '45%', sm: activeStep === steps.length - 1 ? '150px' : '100px' },
                      height: { xs: '40px', sm: '48px' },
                      fontSize: { xs: '0.8125rem', sm: '0.875rem' }
                    }}
                  >
                    {isModerationLoading ? 'En cours de modération' :
                      (activeStep === steps.length - 1 ? (editingService ? 'Enregistrer' : 'Créer') : 'Suivant')}
                  </StyledButton>
                </Box>
              </Box>
            </NavigationContainer>

            <ReglementationModal
              open={isReglementationModalOpen}
              onClose={() => setIsReglementationModalOpen(false)}
              onAccept={() => {
                setIsReglementationModalOpen(false);
                setActiveStep((prevStep) => prevStep + 1);
              }}
              accepted={reglementationAccepted}
              onAcceptedChange={setReglementationAccepted}
            />
          </ModalContainer>
        </ModalPortal>
      )}
      {showConfirmModal && (
        <ModalPortal onBackdropClick={() => setShowConfirmModal(false)}>
          <StyledModal onClick={e => e.stopPropagation()}>
            <StyledModalTitle>
              <Typography variant="h6" component="h2">
                Confirmation
              </Typography>
              <IconButton onClick={() => setShowConfirmModal(false)} size="small">
                <CloseIcon />
              </IconButton>
            </StyledModalTitle>
            <Box sx={{
              flex: 1,
              overflowY: 'auto',
              padding: '16px'
            }}>
              <Typography>
                Êtes-vous sûr de vouloir quitter ? Toutes les modifications non enregistrées seront perdues.
              </Typography>
            </Box>
            <Box sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: 1,
              padding: '16px',
              borderTop: '1px solid #FFE4BA',
              backgroundColor: '#FFF8F3'
            }}>
              <Button onClick={() => setShowConfirmModal(false)} sx={{ color: '#718096' }}>
                Annuler
              </Button>
              <Button
                onClick={handleSubmitForm}
                variant="contained"
                sx={{
                  backgroundColor: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF965E',
                  },
                }}
              >
                Quitter
              </Button>
            </Box>
          </StyledModal>
        </ModalPortal>
      )}

      {/* Système de génération IA pour la description de service */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="service_description"
          prompt={`
            Informations du service:
            - Catégorie: ${selectedCategory?.nom || ''}
            - Sous-catégorie: ${selectedSubcategory?.nom || ''}
            - Titre du service: ${formData.titre || ''}
            - Tarif horaire indicatif: ${formData.tarif_horaire || ''} €/heure

            ${stripHtml(formData.description).length > 0 ?
              `- Action: Améliorer le texte existant
            - Texte à améliorer: ${stripHtml(formData.description)}
            - Instructions: Conserve le sens et les informations du texte original, mais améliore le style, la clarté et le professionnalisme. Ne réinvente pas complètement le contenu. Format HTML simple : Utilise des balises comme <p>, <strong>, <em> pour structurer et mettre en valeur, mais sans excès. Pas d'astérisques ni de markdown. N'utilise pas de listes à puces avec des astérisques ou des tirets, utilise plutôt le HTML approprié.`
              :
              `- Action: Générer une nouvelle description de service
            - Instructions: Crée une description accrocheuse et convaincante pour ce service spécifique.
            - Contexte: Le titre du service "${formData.titre || ''}" doit être le point central de cette description.
            - Public cible: Clients potentiels sur la plateforme JobPartiel cherchant des services de ${selectedCategory?.nom || ''}.
            - N'oublie pas de commencer par une accroche forte liée au titre du service et de terminer par un appel à l'action.`
            }
          `}
          originalPrompt={stripHtml(formData.description).length > 0 ? stripHtml(formData.description) : undefined}
          onComplete={(content) => {
            setIsAiConfirmModalOpen(false);

            if (content) {
              // Mettre à jour le contenu de l'éditeur avec le contenu généré
              setFormData(prev => ({
                ...prev,
                description: content
              }));

              // Forcer la mise à jour de l'éditeur
              if (editorRef.current) {
                const editor = editorRef.current.getEditor();
                if (editor) {
                  editor.commands.setContent(content);
                }
              }

              // Mettre à jour le nombre de caractères restants
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = content;
              const textContent = tempDiv.textContent || tempDiv.innerText;
              setRemainingCharacters(1200 - textContent.length);
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}
    </>
  );
};

export default StepperServiceForm;