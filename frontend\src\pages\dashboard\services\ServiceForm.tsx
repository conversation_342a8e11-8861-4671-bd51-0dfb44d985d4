import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  FormControlLabel,
  Switch,
  InputAdornment,
  Typography,
  Box,
  FormHelperText,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import { serviceApi } from './serviceApi';
import { ServiceCategory, ServiceSubcategory, UserService, ServiceFormData, SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES, Horaire, HoraireBackend, ServiceFormDataBackend } from './types';
import { XCircle, Calendar, Euro, FileText, Tag, Clock } from 'lucide-react';
import { notify } from '@/components/Notification';
import DOMPurify from 'dompurify';
import logger from '@/utils/logger';

// Styled components avec des animations améliorées
const StyledDialog = styled(Dialog)(() => ({
  '& .MuiDialog-paper': {
    borderRadius: '8px',
    overflow: 'hidden',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    background: '#FFFFFF',
    border: '1px solid rgba(255, 122, 53, 0.1)',
    maxWidth: '1000px',
    maxHeight: '90vh',
    width: '100%',
    margin: '6px',
    overflowY: 'auto',
  },
}));

const StyledDialogTitle = styled(DialogTitle)(() => ({
  backgroundColor: '#FF6B2C',
  color: 'white',
  padding: '10px 32px',
  fontSize: '1.4rem',
  fontWeight: 600,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '12px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #FF7A35 0%, #FF965E 100%)',
  },
}));

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    transition: 'all 0.2s ease-in-out',
    backgroundColor: 'white',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    },
    '&.Mui-focused': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 6px -1px rgba(255, 122, 53, 0.1), 0 2px 4px -1px rgba(255, 122, 53, 0.06)',
    },
    '& fieldset': {
      borderColor: 'rgba(255, 122, 53, 0.2)',
    },
    '&:hover fieldset': {
      borderColor: '#FF7A35',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#4B5563',
    '&.Mui-focused': {
      color: '#FF6B2C',
    },
  },
  '& .MuiInputAdornment-root': {
    color: '#FF7A35',
  },
});

const StyledFormControl = styled(FormControl)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    transition: 'all 0.2s ease-in-out',
    backgroundColor: 'white',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    },
    '&.Mui-focused': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 6px -1px rgba(255, 122, 53, 0.1), 0 2px 4px -1px rgba(255, 122, 53, 0.06)',
    },
    '& fieldset': {
      borderColor: 'rgba(255, 122, 53, 0.2)',
    },
    '&:hover fieldset': {
      borderColor: '#FF7A35',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#4B5563',
    '&.Mui-focused': {
      color: '#FF6B2C',
    },
  },
});

const StyledSwitch = styled(Switch)({
  '& .MuiSwitch-switchBase': {
    '&.Mui-checked': {
      color: '#FF6B2C',
      '& + .MuiSwitch-track': {
        backgroundColor: '#FF965E',
        opacity: 0.5,
      },
      '&:hover': {
        backgroundColor: 'rgba(255, 107, 44, 0.08)',
      },
    },
  },
  '& .MuiSwitch-track': {
    borderRadius: '20px',
  },
});

const FormSection = styled(motion.div)({
  marginBottom: '24px',
  padding: '16px',
  borderRadius: '16px',
  backgroundColor: 'rgba(255, 248, 243, 0.5)',
  border: '1px solid rgba(255, 122, 53, 0.1)',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: 'rgba(255, 248, 243, 0.8)',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 6px -1px rgba(255, 122, 53, 0.1), 0 2px 4px -1px rgba(255, 122, 53, 0.06)',
  },
});

const DialogContentStyled = styled(DialogContent)({
  paddingTop: '32px !important',
  paddingBottom: '32px !important',
  paddingLeft: '1rem !important',
  paddingRight: '1rem !important',
});

const StyledButton = styled(Button)({
  borderRadius: '12px',
  padding: '12px 24px',
  textTransform: 'none',
  fontWeight: 600,
  transition: 'all 0.2s ease-in-out',
  '&.MuiButton-contained': {
    background: 'linear-gradient(90deg, #FF6B2C 0%, #FF965E 100%)',
    color: 'white',
    '&:hover': {
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 6px -1px rgba(255, 122, 53, 0.2), 0 2px 4px -1px rgba(255, 122, 53, 0.1)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: '#FF7A35',
    color: '#FF7A35',
    '&:hover': {
      backgroundColor: 'rgba(255, 122, 53, 0.04)',
      transform: 'translateY(-1px)',
    },
  },
});

const StyledDialogActions = styled(DialogActions)({
  padding: '24px 32px',
  background: 'linear-gradient(180deg, rgba(255, 248, 243, 0) 0%, rgba(255, 248, 243, 0.8) 100%)',
  borderTop: '1px solid rgba(255, 122, 53, 0.1)',
  gap: '16px',
});

interface ServiceFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  service: UserService | null;
  services: UserService[];
}

const JOURS = [
  'lundi',
  'mardi',
  'mercredi',
  'jeudi',
  'vendredi',
  'samedi',
  'dimanche',
];

interface Disponibilite {
  jour: string;
  debut: string;
  fin: string;
  disponible: boolean;
  id: string; // Pour identifier de manière unique chaque créneau
}

const ServiceForm: React.FC<ServiceFormProps> = ({
  open,
  onClose,
  onSubmit,
  service,
  services = [],
}) => {
  const [categories] = useState<ServiceCategory[]>(SERVICE_CATEGORIES);
  const [subcategories, setSubcategories] = useState<ServiceSubcategory[]>([]);
  const [formData, setFormData] = useState<ServiceFormData>({
    category_id: '',
    subcategory_id: '',
    titre: '',
    description: '',
    tarif_horaire: 0,
    horaires: {}
  });

  const [selectedDay, setSelectedDay] = useState<string>('lundi');
  const [disponibilites, setDisponibilites] = useState<Disponibilite[]>([{
    jour: 'lundi',
    debut: '09:00',
    fin: '18:00',
    disponible: true,
    id: '1'
  }]);

  const [lastNotifyTime, setLastNotifyTime] = useState<number>(0);
  const [remainingCharacters, setRemainingCharacters] = useState<number>(700);

  const JOURS_ORDRE = {
    'lundi': 0,
    'mardi': 1,
    'mercredi': 2,
    'jeudi': 3,
    'vendredi': 4,
    'samedi': 5,
    'dimanche': 6
  };

  const sortDisponibilites = (dispos: Disponibilite[]): Disponibilite[] => {
    return [...dispos].sort((a, b) => {
      // D'abord trier par jour
      const jourDiff = JOURS_ORDRE[a.jour as keyof typeof JOURS_ORDRE] - JOURS_ORDRE[b.jour as keyof typeof JOURS_ORDRE];
      if (jourDiff !== 0) return jourDiff;

      // Si même jour, trier par heure de début
      const [aHeure, aMinute] = a.debut.split(':').map(Number);
      const [bHeure, bMinute] = b.debut.split(':').map(Number);
      
      const heuresDiff = aHeure - bHeure;
      if (heuresDiff !== 0) return heuresDiff;
      
      // Si même heure de début, trier par minute de début
      const minutesDiff = aMinute - bMinute;
      if (minutesDiff !== 0) return minutesDiff;

      // Si même heure de début, trier par heure de fin
      const [aHeureF, aMinuteF] = a.fin.split(':').map(Number);
      const [bHeureF, bMinuteF] = b.fin.split(':').map(Number);
      
      const heuresDiffF = aHeureF - bHeureF;
      if (heuresDiffF !== 0) return heuresDiffF;
      
      // Si même heure de fin, trier par minute de fin
      return aMinuteF - bMinuteF;
    });
  };

  useEffect(() => {
    if (service) {
      const loadedDisponibilites: Disponibilite[] = [];
      
      // Charger tous les créneaux configurés, qu'ils soient disponibles ou non
      Object.entries(service.horaires).forEach(([jour, horaire]) => {
        // Ajouter le créneau principal s'il a été configuré (même non disponible)
        if (horaire.debut !== '09:00' || horaire.fin !== '18:00' || horaire.disponible || (horaire.creneaux && horaire.creneaux.length > 0)) {
          loadedDisponibilites.push({
            jour,
            debut: horaire.debut,
            fin: horaire.fin,
            disponible: horaire.disponible,
            id: Math.random().toString(36).substr(2, 9)
          });

          // Ajouter tous les créneaux supplémentaires
          if (horaire.creneaux) {
            horaire.creneaux.forEach(creneau => {
              loadedDisponibilites.push({
                jour,
                debut: creneau.debut,
                fin: creneau.fin,
                disponible: creneau.disponible || false,
                id: Math.random().toString(36).substr(2, 9)
              });
            });
          }
        }
      });
      
      // Si aucune disponibilité n'est chargée, ajouter un créneau par défaut
      if (loadedDisponibilites.length === 0) {
        loadedDisponibilites.push({
          jour: 'lundi',
          debut: '09:00',
          fin: '18:00',
          disponible: true,
          id: Math.random().toString(36).substr(2, 9)
        });
      }

      // Trier les disponibilités
      const sortedDispos = sortDisponibilites(loadedDisponibilites);
      setDisponibilites(sortedDispos);
      
      // Convertir les disponibilités en format horaires pour formData
      const convertedHoraires: { [key: string]: Horaire[] } = {};
      sortedDispos.forEach(dispo => {
        if (!convertedHoraires[dispo.jour]) {
          convertedHoraires[dispo.jour] = [];
        }
        convertedHoraires[dispo.jour].push({
          debut: dispo.debut,
          fin: dispo.fin,
          disponible: dispo.disponible,
          id: dispo.id
        });
      });

      setFormData({
        category_id: service.category_id,
        subcategory_id: service.subcategory_id,
        titre: service.titre,
        description: service.description,
        tarif_horaire: service.tarif_horaire,
        horaires: convertedHoraires
      });

      loadSubcategories(service.category_id);
    }
  }, [service]);

  const handleCategoryChange = (categoryId: string) => {
    setFormData(prev => ({ ...prev, category_id: categoryId, subcategory_id: '' }));
    loadSubcategories(categoryId);
  };

  const loadSubcategories = (categoryId: string) => {
    const filteredSubcategories = SERVICE_SUBCATEGORIES.filter(subcat => subcat.categoryId === categoryId);
    setSubcategories(filteredSubcategories);
  };

  // Vérifier si une sous-catégorie est déjà utilisée
  const isSubcategoryUsed = (subcategoryId: string) => {
    return services.some(s => 
      s.subcategory_id === subcategoryId && 
      (!service || s.id !== service.id)
    );
  };

  const handleSubcategoryChange = (subcategoryId: string) => {
    if (isSubcategoryUsed(subcategoryId)) {
      notify('Cette sous-catégorie est déjà utilisée dans un autre service.', 'error');
      return;
    }
    setFormData(prev => ({ ...prev, subcategory_id: subcategoryId }));
  };

  const convertHorairesForBackend = (dispos: Disponibilite[]): { [key: string]: HoraireBackend } => {
    const result: { [key: string]: HoraireBackend } = {};

    // Initialiser tous les jours comme non disponibles
    JOURS.forEach(jour => {
      result[jour] = {
        debut: '09:00',
        fin: '18:00',
        disponible: false,
        creneaux: []
      };
    });

    // Grouper les disponibilités par jour
    const groupedDispos = dispos.reduce((acc, dispo) => {
      if (!acc[dispo.jour]) {
        acc[dispo.jour] = [];
      }
      acc[dispo.jour].push(dispo);
      return acc;
    }, {} as { [key: string]: Disponibilite[] });

    // Traiter chaque jour
    Object.entries(groupedDispos).forEach(([jour, disponibilites]) => {
      if (disponibilites.length > 0) {
        // Trier les disponibilités par heure de début
        const sortedDispos = sortDisponibilites(disponibilites);
        
        // Utiliser le premier créneau disponible comme créneau principal
        const premierCreneauActif = sortedDispos.find(d => d.disponible);
        const premierCreneau = premierCreneauActif || sortedDispos[0];
        
        result[jour] = {
          debut: premierCreneau.debut,
          fin: premierCreneau.fin,
          disponible: premierCreneau.disponible,
          creneaux: sortedDispos
            .filter(d => d !== premierCreneau)
            .map(d => ({
              debut: d.debut,
              fin: d.fin,
              disponible: d.disponible
            }))
        };
      }
    });

    return result;
  };

  const handleAddDay = () => {
    const newDispo: Disponibilite = {
      jour: selectedDay,
      debut: '08:00',
      fin: '18:00',
      disponible: true,
      id: Math.random().toString(36).substr(2, 9)
    };

    // Ajouter le nouveau créneau sans vérification
    const newDisponibilites = [...disponibilites, newDispo];
    setDisponibilites(newDisponibilites);
    updateFormDataHoraires(newDisponibilites);
  };

  const updateFormDataHoraires = (dispos: Disponibilite[]) => {
    // Grouper les disponibilités par jour
    const groupedDispos = dispos.reduce((acc, dispo) => {
      if (!acc[dispo.jour]) {
        acc[dispo.jour] = [];
      }
      // Ajouter tous les créneaux, qu'ils soient disponibles ou non
      acc[dispo.jour].push({
        debut: dispo.debut,
        fin: dispo.fin,
        disponible: dispo.disponible,
        id: dispo.id
      });
      return acc;
    }, {} as { [key: string]: Horaire[] });

    // S'assurer que tous les jours sont présents dans l'objet
    const newHoraires = JOURS.reduce((acc, jour) => ({
      ...acc,
      [jour]: groupedDispos[jour] || [],
    }), {});

    setFormData(prev => ({
      ...prev,
      horaires: newHoraires
    }));
  };

  const handleHoraireChange = (dispoId: string, field: 'debut' | 'fin' | 'disponible', value: string | boolean) => {
    const updatedDispos = disponibilites.map(dispo => {
      if (dispo.id === dispoId) {
        return {
          ...dispo,
          [field]: value
        };
      }
      return dispo;
    });

    setDisponibilites(updatedDispos);
    updateFormDataHoraires(updatedDispos);
  };

  const handleRemoveDay = (dispoId: string) => {
    // Vérifier si le créneau à supprimer est le seul restant
    if (disponibilites.length === 1 && disponibilites[0].id === dispoId) {
        notify('Vous ne pouvez pas supprimer le dernier créneau disponible. Ajoutez d\'abord un autre créneau. Si vous souhaitez suspendre le service, vous pouvez le faire dans les paramètres du service.', 'error');
        return; // Ne pas supprimer si c'est le seul créneau
    }
    
    const updatedDispos = disponibilites.filter(dispo => dispo.id !== dispoId);
    setDisponibilites(updatedDispos);
    updateFormDataHoraires(updatedDispos);
  };

  const handleSetAllWeekAvailability = () => {
    const newDisponibilites = JOURS.map((jour) => ({
      jour,
      debut: '08:00',
      fin: '18:00',
      disponible: true,
      id: `${jour}-availability`
    }));
    setDisponibilites(newDisponibilites);
  };

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const handleTitreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 40) {
        // Mettre en forme le texte pour que la première lettre soit en majuscule et le reste en minuscules
        const formattedValue = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
        setFormData(prev => ({
            ...prev,
            titre: formattedValue
        }));
    }
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const newLength = value.length;

    // Vérifier que le nouveau nombre de caractères ne dépasse pas 700
    if (newLength <= 700) {
        setRemainingCharacters(700 - newLength); // Mettre à jour le nombre de caractères restants

        const sentences = value.split(/(?<=[.!?])\s+/).map(sentence => {
            // Mettre la première lettre de chaque phrase en majuscule et le reste en minuscules
            return sentence.charAt(0).toUpperCase() + sentence.slice(1).toLowerCase();
        });

        const formattedValue = sentences.join(' ')
            .replace(/([!?])([^\s])/g, ' $1 $2') // Ajoute un espace avant les points d'exclamation et d'interrogation
            .replace(/([.!?])([^\s])/g, '$1 $2'); // Ajoute un espace après un point, point d'exclamation ou point d'interrogation si nécessaire

        setFormData(prev => ({
            ...prev,
            description: formattedValue
        }));
    } else {
        // Si le nombre de caractères dépasse 700, ne pas mettre à jour la description
        notify('La description ne peut pas dépasser 700 caractères.', 'error');
    }
  };

  const handleTarifChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const currentTime = Date.now();

    // Vérifier si la valeur est vide
    if (value === '') {
        setFormData(prev => ({ ...prev, tarif_horaire: 0 })); // Remet à zéro si le champ est vidé
    } else {
        const numericValue = parseFloat(value);
        if (numericValue >= 0 && numericValue <= 999) {
            setFormData(prev => ({ ...prev, tarif_horaire: numericValue }));
        } else {
            if (currentTime - lastNotifyTime > 3000) {
                notify('Le tarif horaire doit être compris entre 0 et 999 €', 'error');
                setLastNotifyTime(currentTime);
            }
        }
    }
  };

  const handleRemoveAllDaysExceptOne = () => {
    if (disponibilites.length > 1) {
      const remainingDispo = disponibilites[0]; // Garder le premier jour
      setDisponibilites([remainingDispo]); // Ne garder que le premier jour
      updateFormDataHoraires([remainingDispo]); // Mettre à jour les horaires dans le formData
    }
  };

  const handleSubmit = async () => {
    // Validation des champs obligatoires
    if (!formData.category_id) {
      notify('Veuillez sélectionner une catégorie', 'error');
      return;
    }
    if (!formData.subcategory_id) {
      notify('Veuillez sélectionner une sous-catégorie', 'error');
      return;
    }
    if (!formData.titre || formData.titre.length < 3 || formData.titre.length > 45) {
      notify('Le titre doit contenir entre 3 et 45 caractères', 'error');
      return;
    }
    if (!formData.description || formData.description.length < 10 || formData.description.length > 700) {
      notify('La description doit contenir entre 10 et 700 caractères', 'error');
      return;
    }
    if (formData.tarif_horaire === undefined || formData.tarif_horaire < 0) {
      notify('Le tarif horaire doit être un nombre positif', 'error');
      return;
    }
    if (!disponibilites.some(d => d.disponible)) {
      notify('Veuillez ajouter au moins une disponibilité', 'error');
      return;
    }

    try {
      // Nettoyer les doublons et les chevauchements avant l'envoi
      const uniqueDispos = disponibilites.reduce((acc: Disponibilite[], current) => {
        // Convertir les heures en minutes pour faciliter la comparaison
        const currentDebut = parseInt(current.debut.split(':')[0]) * 60 + parseInt(current.debut.split(':')[1]);
        const currentFin = parseInt(current.fin.split(':')[0]) * 60 + parseInt(current.fin.split(':')[1]);

        // Vérifier s'il existe déjà un créneau qui chevauche
        const chevauchement = acc.findIndex(dispo => {
          if (dispo.jour !== current.jour) return false;

          const dispoDebut = parseInt(dispo.debut.split(':')[0]) * 60 + parseInt(dispo.debut.split(':')[1]);
          const dispoFin = parseInt(dispo.fin.split(':')[0]) * 60 + parseInt(dispo.fin.split(':')[1]);

          // Vérifier le chevauchement
          return (currentDebut <= dispoFin && currentFin >= dispoDebut);
        });

        if (chevauchement === -1) {
          // Pas de chevauchement, ajouter le créneau
          acc.push(current);
        } else {
          // En cas de chevauchement, fusionner les créneaux
          const existant = acc[chevauchement];
          const existantDebut = parseInt(existant.debut.split(':')[0]) * 60 + parseInt(existant.debut.split(':')[1]);
          const existantFin = parseInt(existant.fin.split(':')[0]) * 60 + parseInt(existant.fin.split(':')[1]);

          // Prendre le plus grand intervalle
          const nouveauDebut = Math.min(currentDebut, existantDebut);
          const nouveauFin = Math.max(currentFin, existantFin);

          // Convertir les minutes en format HH:mm
          const formatHeure = (minutes: number) => {
            const h = Math.floor(minutes / 60).toString().padStart(2, '0');
            const m = (minutes % 60).toString().padStart(2, '0');
            return `${h}:${m}`;
          };

          // Mettre à jour le créneau existant
          acc[chevauchement] = {
            ...existant,
            debut: formatHeure(nouveauDebut),
            fin: formatHeure(nouveauFin),
            disponible: existant.disponible || current.disponible // Si l'un des deux est disponible, le résultat est disponible
          };
        }
        return acc;
      }, []);

      // Mettre à jour l'état avec les disponibilités uniques
      setDisponibilites(uniqueDispos);
      
      // Nettoyer les entrées avec DOMPurify
      const sanitizedTitre = DOMPurify.sanitize(formData.titre);
      const sanitizedDescription = DOMPurify.sanitize(formData.description);

      // Récupérer les noms de catégorie et sous-catégorie
      const category = SERVICE_CATEGORIES.find(cat => cat.id === formData.category_id);
      const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === formData.subcategory_id);

      if (!category || !subcategory) {
        throw new Error('Catégorie ou sous-catégorie non trouvée');
      }

      // Trier les disponibilités avant l'envoi
      const sortedDispos = sortDisponibilites(uniqueDispos);

      const dataToSend: ServiceFormDataBackend & { categorie: string[] } = {
        category_id: formData.category_id,
        subcategory_id: formData.subcategory_id,
        titre: sanitizedTitre,
        description: sanitizedDescription,
        tarif_horaire: formData.tarif_horaire,
        horaires: convertHorairesForBackend(sortedDispos),
        categorie: [category.nom, subcategory.nom]
      };

      // Log des données avant envoi pour debug
      logger.info('Données envoyées au serveur:', dataToSend);

      if (service) {
        await serviceApi.updateService(service.id, dataToSend);
        notify('Service mis à jour avec succès', 'success');
      } else {
        await serviceApi.createService(dataToSend);
        notify('Service créé avec succès', 'success');
      }
      onSubmit();

    } catch (error) {
      logger.error('Erreur lors de la sauvegarde des services :', error);
      notify('Erreur lors de la sauvegarde du service. Vérifiez les données saisies.', 'error');
    }
  };

  return (
    <AnimatePresence>
      {open && (
        <StyledDialog
          open={open}
          onClose={onClose}
          maxWidth="md"
          fullWidth
        >
          <motion.div
            // initial={{ opacity: 0, y: 20 }}
            // animate={{ opacity: 1, y: 0 }}
            // exit={{ opacity: 0, y: 20 }}
            // transition={{ duration: 0.3 }}
          >
            <StyledDialogTitle>
              <div className="flex items-center gap-2">
                <Tag className="h-6 w-6" />
                {service ? 'Modifier le service' : 'Ajouter un service'}
              </div>
              <IconButton
                onClick={onClose}
                size="small"
                sx={{
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                <XCircle className="h-5 w-5" />
              </IconButton>
            </StyledDialogTitle>

            <DialogContentStyled>
              <FormSection
                // initial={{ opacity: 0, y: 20 }}
                // animate={{ opacity: 1, y: 0 }}
                // transition={{ delay: 0.1 }}
              >
                <Typography variant="h6" color="textSecondary" gutterBottom>
                  Informations générales
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <StyledFormControl fullWidth>
                      <InputLabel>Catégorie</InputLabel>
                      <Select
                        value={formData.category_id}
                        onChange={(e) => handleCategoryChange(e.target.value)}
                        label="Catégorie"
                        displayEmpty
                      >
                        {categories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.nom}
                          </MenuItem>
                        ))}
                      </Select>
                      <FormHelperText>
                        {formData.category_id ? '' : 'Sélectionnez une catégorie pour choisir une sous-catégorie.'}
                      </FormHelperText>
                    </StyledFormControl>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <StyledFormControl fullWidth>
                      <InputLabel>Sous-catégorie</InputLabel>
                      <Select
                        value={formData.subcategory_id}
                        onChange={(e) => handleSubcategoryChange(e.target.value)}
                        label="Sous-catégorie"
                        disabled={!formData.category_id || subcategories.length === 0}
                        displayEmpty
                      >
                        {subcategories.map((subcategory) => (
                          <MenuItem 
                            key={subcategory.id} 
                            value={subcategory.id}
                            disabled={isSubcategoryUsed(subcategory.id)}
                          >
                            {subcategory.nom}
                            {isSubcategoryUsed(subcategory.id) && (
                              <span className="ml-2 text-gray-400 text-sm">(déjà utilisée)</span>
                            )}
                          </MenuItem>
                        ))}
                      </Select>
                      <FormHelperText>
                        {!formData.category_id ? 'Sélectionnez d\'abord une catégorie.' : ''}
                      </FormHelperText>
                    </StyledFormControl>
                  </Grid>
                </Grid>
              </FormSection>

              <FormSection
                // initial={{ opacity: 0, y: 20 }}
                // animate={{ opacity: 1, y: 0 }}
                // transition={{ delay: 0.2 }}
              >
                <Typography variant="h6" color="textSecondary" gutterBottom>
                  Détails du service
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={12}>
                    <StyledTextField
                      fullWidth
                      label="Titre"
                      value={formData.titre}
                      onChange={handleTitreChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <FileText className="h-5 w-5" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid size={12}>
                    <StyledTextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Description"
                      value={formData.description}
                      onChange={handleDescriptionChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <FileText className="h-5 w-5" />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Typography variant="body2" color="textSecondary" style={{ textAlign: 'right' }}>
                      {remainingCharacters} caractères restants
                    </Typography>
                  </Grid>
                  <Grid size={{ xs: 12, md: 6 }}>
                    <StyledTextField
                      fullWidth
                      type="number"
                      label="Tarif horaire (Jobi)"
                      value={formData.tarif_horaire}
                      onChange={handleTarifChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Euro className="h-5 w-5" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>
              </FormSection>

              <FormSection
                // initial={{ opacity: 0, y: 20 }}
                // animate={{ opacity: 1, y: 0 }}
                // transition={{ delay: 0.3 }}
              >
                <Typography variant="h6" color="textSecondary" gutterBottom className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Disponibilités
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid size={12}>
                    <StyledButton
                      variant="outlined"
                      onClick={handleSetAllWeekAvailability}
                      startIcon={<Clock />}
                      fullWidth
                    >
                      Appliquer à toute la semaine
                    </StyledButton>
                  </Grid>
                  
                  {disponibilites.map((dispo) => (
                    <Grid size={12} key={dispo.id}>
                      <Box className="relative flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-white rounded-lg border border-gray-200">
                        <div className="w-full">
                          <Typography variant="subtitle1" className="font-medium mb-4 sm:mb-0">
                            {capitalizeFirstLetter(dispo.jour)}
                          </Typography>
                          <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
                            <StyledTextField
                              type="time"
                              value={dispo.debut}
                              onChange={(e) => handleHoraireChange(dispo.id, 'debut', e.target.value)}
                              size="small"
                              className="w-[120px]"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderColor: '#FF7A35',
                                  '&:hover': {
                                    borderColor: '#FF6B2C',
                                  },
                                },
                              }}
                            />
                            <Typography variant="body2" color="textSecondary" className="mx-2">à</Typography>
                            <StyledTextField
                              type="time"
                              value={dispo.fin}
                              onChange={(e) => handleHoraireChange(dispo.id, 'fin', e.target.value)}
                              size="small"
                              className="w-[120px]"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderColor: '#FF7A35',
                                  '&:hover': {
                                    borderColor: '#FF6B2C',
                                  },
                                },
                              }}
                            />
                            <FormControlLabel
                              control={
                                <StyledSwitch
                                  checked={dispo.disponible}
                                  onChange={(e) => {
                                    const availableCount = disponibilites.filter(d => d.disponible).length;
                                    // Autoriser de cocher à nouveau même si c'est le seul horaire disponible
                                    if (e.target.checked || availableCount > 1) {
                                      handleHoraireChange(dispo.id, 'disponible', e.target.checked);
                                    } else {
                                      notify('Vous devez avoir au moins un horaire disponible. Si vous souhaitez suspendre le service, vous pouvez le faire dans les paramètres du service.', 'error');
                                    }
                                  }}
                                />
                              }
                              label="Disponible"
                              className="ml-0 sm:ml-4"
                            />
                          </div>
                        </div>
                        <IconButton
                          onClick={() => handleRemoveDay(dispo.id)}
                          size="small"
                          className="!absolute right-4 top-4 sm:!relative sm:right-0 sm:top-0 sm:ml-auto"
                          sx={{
                            color: '#FF6B2C',
                            border: '1px solid rgba(255, 107, 44, 0.2)',
                            borderRadius: '8px',
                            padding: '4px',
                            minWidth: '32px',
                            height: '32px',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 107, 44, 0.04)',
                              border: '1px solid #FF6B2C',
                            }
                          }}
                        >
                          <XCircle className="h-5 w-5" />
                        </IconButton>
                      </Box>
                    </Grid>
                  ))}
                  
                  <Grid size={12}>
                    <StyledFormControl fullWidth>
                      <InputLabel>Choisir un jour</InputLabel>
                      <Select
                        value={selectedDay}
                        onChange={(e) => setSelectedDay(e.target.value)}
                        label="Choisir un jour"
                      >
                        {JOURS.map((jour) => (
                          <MenuItem key={jour} value={jour}>
                            {capitalizeFirstLetter(jour)}
                          </MenuItem>
                        ))}
                      </Select>
                    </StyledFormControl>
                  </Grid>

                  <Grid size={12}>
                    <StyledButton
                      variant="outlined"
                      onClick={handleAddDay}
                      fullWidth
                    >
                      Ajouter un créneau
                    </StyledButton>
                  </Grid>
                  {disponibilites.length > 1 && (
                    <Grid size={12}>
                      <StyledButton
                        variant="outlined"
                        onClick={handleRemoveAllDaysExceptOne}
                        startIcon={<XCircle />}
                        fullWidth
                        style={{ marginTop: '5px', backgroundColor: 'red', color: 'white', borderColor: 'red' }}
                      >
                        Supprimer toutes les disponibilités
                      </StyledButton>
                    </Grid>
                  )}
                </Grid>
              </FormSection>
            </DialogContentStyled>

            <StyledDialogActions>
              <StyledButton
                onClick={onClose}
                variant="outlined"
              >
                Annuler
              </StyledButton>
              <StyledButton
                onClick={handleSubmit}
                variant="contained"
              >
                {service ? 'Modifier' : 'Ajouter'}
              </StyledButton>
            </StyledDialogActions>
          </motion.div>
        </StyledDialog>
      )}
    </AnimatePresence>
  );
};

export default ServiceForm; 