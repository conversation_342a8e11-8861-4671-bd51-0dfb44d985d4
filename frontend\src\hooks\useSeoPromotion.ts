import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { fetchCsrfToken } from '../services/csrf';
import { getCommonHeaders } from '../utils/headers';
import logger from '../utils/logger';
import { useAuth } from '../contexts/AuthContext';

interface SeoPromotionState {
  shouldShow: boolean;
  trigger: 'onboarding' | 'profile_complete' | 'first_mission' | 'notification' | 'popup';
  lastShown: string | null;
}

interface UserStats {
  registrationDate: string;
  profileCompleteness: number;
  missionsCompleted: number; // Nombre d'offres/propositions envoyées
  seoIndexable: boolean;
  lastSeoPrompt: string | null;
}

export const useSeoPromotion = () => {
  const { isAuthenticated } = useAuth();
  const [promotionState, setPromotionState] = useState<SeoPromotionState>({
    shouldShow: false,
    trigger: 'popup',
    lastShown: null
  });

  const [userStats, setUserStats] = useState<UserStats | null>(null);

  // Récupérer les statistiques utilisateur
  const fetchUserStats = async () => {
    // Ne pas faire d'appel API si l'utilisateur n'est pas connecté
    if (!isAuthenticated) {
      return null;
    }

    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await axios.get('/api/users/seo-promotion-stats', {
        ...API_CONFIG,
        headers: {
          ...API_CONFIG.headers,
          ...headers
        }
      });

      if (response.data.success) {
        setUserStats(response.data.stats);
        return response.data.stats;
      }
    } catch (error) {
      // Réduire le niveau de log pour les utilisateurs non connectés
      if (!isAuthenticated) {
        logger.info('Stats SEO non disponibles (utilisateur non connecté)');
      } else {
        logger.error('Erreur lors de la récupération des stats SEO:', error);
      }
    }
    return null;
  };

  // Marquer la promotion comme affichée
  const markPromotionShown = async (trigger: string) => {
    // Ne pas faire d'appel API si l'utilisateur n'est pas connecté
    if (!isAuthenticated) {
      // Mettre à jour seulement le localStorage pour les utilisateurs non connectés
      localStorage.setItem('lastSeoPrompt', new Date().toISOString());
      localStorage.setItem(`seoPrompt_${trigger}`, 'shown');
      return;
    }

    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      await axios.post('/api/users/seo-promotion-shown', {
        trigger
      }, {
        ...API_CONFIG,
        headers: {
          ...API_CONFIG.headers,
          ...headers,
          'Content-Type': 'application/json'
        }
      });

      // Mettre à jour le localStorage
      localStorage.setItem('lastSeoPrompt', new Date().toISOString());
      localStorage.setItem(`seoPrompt_${trigger}`, 'shown');
    } catch (error) {
      logger.error('Erreur lors du marquage de la promotion SEO:', error);
    }
  };

  // Vérifier si on doit afficher la promotion
  const checkShouldShowPromotion = (stats: UserStats): SeoPromotionState => {
    // Si déjà activé, ne pas afficher
    if (stats.seoIndexable) {
      return { shouldShow: false, trigger: 'popup', lastShown: null };
    }

    const now = new Date();
    const registrationDate = new Date(stats.registrationDate);
    const daysSinceRegistration = Math.floor((now.getTime() - registrationDate.getTime()) / (1000 * 60 * 60 * 24));

    // Vérifier le localStorage pour éviter de spammer
    const lastPrompt = localStorage.getItem('lastSeoPrompt');
    const lastPromptDate = lastPrompt ? new Date(lastPrompt) : null;
    const daysSinceLastPrompt = lastPromptDate
      ? Math.floor((now.getTime() - lastPromptDate.getTime()) / (1000 * 60 * 60 * 24))
      : 999;

    // Ne pas afficher si déjà montré dans les 7 derniers jours
    if (daysSinceLastPrompt < 7) {
      return { shouldShow: false, trigger: 'popup', lastShown: lastPrompt };
    }

    // Logique de déclenchement basée sur les étapes clés

    // 1. Onboarding - 24h après inscription si profil > 50%
    if (daysSinceRegistration >= 1 && daysSinceRegistration <= 3 &&
        stats.profileCompleteness >= 50 &&
        !localStorage.getItem('seoPrompt_onboarding')) {
      return { shouldShow: true, trigger: 'onboarding', lastShown: lastPrompt };
    }

    // 2. Profil complet - quand profil atteint 80%
    if (stats.profileCompleteness >= 80 &&
        !localStorage.getItem('seoPrompt_profile_complete')) {
      return { shouldShow: true, trigger: 'profile_complete', lastShown: lastPrompt };
    }

    // 3. Première offre - après la première offre/proposition envoyée
    if (stats.missionsCompleted === 1 &&
        !localStorage.getItem('seoPrompt_first_mission')) {
      return { shouldShow: true, trigger: 'first_mission', lastShown: lastPrompt };
    }

    // 4. Notification périodique - tous les 14 jours après 1 semaine d'inscription
    if (daysSinceRegistration >= 7 && daysSinceLastPrompt >= 14) {
      return { shouldShow: true, trigger: 'notification', lastShown: lastPrompt };
    }

    return { shouldShow: false, trigger: 'popup', lastShown: lastPrompt };
  };

  // Initialiser et vérifier au chargement (une seule fois)
  useEffect(() => {
    // Ne pas initialiser si l'utilisateur n'est pas connecté
    if (!isAuthenticated) {
      return;
    }

    const initializePromotion = async () => {
      const stats = await fetchUserStats();
      if (stats) {
        const promotionCheck = checkShouldShowPromotion(stats);
        setPromotionState(promotionCheck);
      }
    };

    initializePromotion();
  }, [isAuthenticated]);

  // Fonction pour forcer l'affichage (pour les tests)
  const forceShowPromotion = (trigger: SeoPromotionState['trigger']) => {
    setPromotionState({
      shouldShow: true,
      trigger,
      lastShown: null
    });
  };

  // Fonction pour fermer la promotion
  const closePromotion = async () => {
    await markPromotionShown(promotionState.trigger);
    setPromotionState(prev => ({ ...prev, shouldShow: false }));
  };

  // Fonction pour accepter la promotion
  const acceptPromotion = async () => {
    await markPromotionShown(promotionState.trigger);
    setPromotionState(prev => ({ ...prev, shouldShow: false }));

    // Rafraîchir les stats depuis le serveur pour s'assurer de la cohérence
    await fetchUserStats();
  };

  return {
    promotionState,
    userStats,
    forceShowPromotion,
    closePromotion,
    acceptPromotion,
    refreshStats: fetchUserStats
  };
};

export default useSeoPromotion;
