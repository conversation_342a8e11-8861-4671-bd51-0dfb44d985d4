import React, { useState, useEffect } from 'react';
import { Check, Shield, Award, Clock, Users, Settings } from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionService from '../services/subscriptionService';
import { useAuth } from '../contexts/AuthContext';
import '../styles/faq.css';

interface AccordionItemProps {
  title: string;
  content: React.ReactNode;
  isOpen: boolean;
  onClick: () => void;
  index: number;
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, content, isOpen, onClick, index }) => {
  const contentRef = React.useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = React.useState<number>(0);

  // Mesurer la hauteur du contenu quand il change ou quand l'accordéon s'ouvre
  React.useEffect(() => {
    if (contentRef.current) {
      const height = contentRef.current.scrollHeight;
      setContentHeight(height);
    }
  }, [content, isOpen]);

  return (
    <div className={`bg-white rounded-xl transition-all duration-300 ${
      isOpen
        ? 'shadow-lg ring-2 ring-[#FF7A35]/20'
        : 'shadow-sm hover:shadow ring-1 ring-gray-100 hover:ring-[#FF7A35]/20'
    }`}>
      <button
        data-pricing-index={index}
        className={`w-full px-6 py-4 text-left flex items-center justify-between group transition-all duration-300 ${
          isOpen ? 'bg-[#FF7A35]/5' : ''
        }`}
        onClick={onClick}
        aria-expanded={isOpen}
      >
        <span className={`font-medium transition-all duration-300 ${
          isOpen
            ? 'text-[#FF7A35] translate-x-2'
            : 'text-gray-700 group-hover:text-[#FF7A35] group-hover:translate-x-1'
        }`}>
          {title}
        </span>
        <div className={`flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300 ${
          isOpen
            ? 'bg-[#FF7A35]'
            : 'bg-gray-100 group-hover:bg-[#FF7A35]/10'
        }`}>
          <svg
            className={`w-5 h-5 transition-all duration-300 ${
              isOpen ? 'text-white rotate-180' : 'text-gray-400 group-hover:text-[#FF7A35]'
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>
      <div
        className="overflow-hidden transition-all duration-500 ease-out"
        style={{
          maxHeight: isOpen ? `${contentHeight}px` : '0',
          opacity: isOpen ? 1 : 0
        }}
      >
        <div ref={contentRef} className="px-8 pb-6 pt-2">
          <div className="text-gray-600 leading-relaxed faq-content">
            {content}
          </div>
        </div>
      </div>
    </div>
  );
};



const PricingCard: React.FC<{
  price: string;
  title: string;
  description: string;
  features: string[];
  buttonText: string;
  buttonLink: string;
  popular?: boolean;
  serviceType?: "client" | "jobbeur";
}> = ({ price, title, description, features, buttonText, buttonLink, popular = false, serviceType = "client" }) => {
  return (
    <div className={`w-full ${popular ? 'relative transform scale-105 z-10' : ''}`}>
      {popular && (
        <div className="absolute -top-5 left-0 right-0 mx-auto w-52 text-center bg-[#ff7a35] text-white py-1.5 px-3 rounded-full text-sm font-bold shadow-lg z-20 animate-pulse">
          <span className="mr-1">🔥</span> Offre Limitée • 50% de réduction
        </div>
      )}
      <div className={`bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 h-full ${popular ? 'border-2 border-[#ff7a35]/30 shadow-xl' : ''}`}>
        <div className="flex flex-col h-full">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-gray-800">
                {title}
                {popular && <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-[#ff7a35]/10 text-[#ff7a35]">Populaire</span>}
              </h3>
              <div className="text-right">
                <p className="text-3xl font-bold text-[#ff7a35]">
                  {price}
                  <span className="text-gray-500 text-sm font-normal ml-1">/mois</span>
                </p>
                {price === "€0.00" && <span className="text-xs text-gray-500 block">Pas de carte de crédit</span>}
                {price === "€9.90" && (
                  <div>
                    <span className="text-xs text-gray-500 block">
                      <span className="line-through">€19.90</span> • <span className="text-[#ff7a35] font-semibold">-50%</span>
                    </span>
                    <span className="text-xs font-medium text-[#ff7a35] block">
                      Offre limitée aux 500 premiers utilisateurs
                    </span>
                  </div>
                )}
              </div>
            </div>
            <p className="text-gray-600">{description}</p>
          </div>

          <div className="space-y-4 mb-8 flex-grow">
            {features.map((feature, index) => {
              // Vérifier si la fonctionnalité contient des mots-clés indiquant une personnalisation
              const isCustomizable = feature.includes("sous-catégories") ||
                                    feature.includes("galeries") ||
                                    feature.includes("Zone d'intervention") ||
                                    feature.includes("conversations");

              // Vérifier si la fonctionnalité contient le mot "illimité"
              const isUnlimited = feature.includes("illimité");

              return (
                <div key={index} className="flex items-center">
                  <div className={`flex-shrink-0 w-6 h-6 rounded-full ${isUnlimited ? 'bg-green-100' : 'bg-[#ff7a35]/10'} flex items-center justify-center mr-3`}>
                    <Check className={`h-4 w-4 ${isUnlimited ? 'text-green-600' : 'text-[#ff7a35]'}`} />
                  </div>
                  <div className="text-gray-700">
                    {feature}
                    {isCustomizable && popular && (
                      <span className="ml-1 text-xs font-medium text-blue-600 whitespace-nowrap">
                        • <span className="underline">personnalisable</span>
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-auto">
            {popular && (
              <div className="mb-4 p-3 bg-[#FFF8F3] rounded-lg border border-[#ff7a35]/20">
                <p className="text-sm text-gray-700 flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#ff7a35] mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span>
                    <strong className="font-semibold">Toutes les limites sont extensibles</strong> selon vos besoins. Ajoutez plus de services, étendez votre zone d'intervention ou augmentez vos capacités à tout moment.
                  </span>
                </p>
              </div>
            )}

            <div className="w-full">
              <Link
                to={buttonLink}
                data-discover="true"
                className={`block w-full text-center text-white py-3.5 px-6 rounded-xl font-semibold ${popular ? 'bg-[#ff7a35] hover:bg-[#ff6b2c]' : 'btn-gradient'} transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-[1.02]`}
              >
                {buttonText}
              </Link>
              {popular && (
                <div className="text-xs mt-2 text-center">
                  <Link to="/dashboard/premium" data-discover="true" className="text-[#ff7a35] hover:underline">
                    Voir les options de personnalisation →
                  </Link>
                </div>
              )}
            </div>

            <p className="text-center text-sm text-gray-500 mt-3">
              {serviceType === "jobbeur" ? (
                <><Shield className="inline-block w-4 h-4 mr-1" /> Annulation possible à tout moment</>
              ) : (
                <><Shield className="inline-block w-4 h-4 mr-1" /> Accès illimité</>
              )}
            </p>

            {popular && (
              <div className="mt-3 text-center space-y-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                  <svg className="mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3" />
                  </svg>
                  Paiement sécurisé
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-3 w-3 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Entièrement personnalisable
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Type pour la configuration des abonnements
type SubscriptionConfigData = {
  success: boolean;
  data: {
    gratuit: Record<string, any>;
    premium: Record<string, any>;
  };
};

export default function Pricing() {
  const { isAuthenticated } = useAuth();
  const [openItems, setOpenItems] = useState<number[]>([]); // Tous les accordéons sont fermés par défaut
  const [basePrice, setBasePrice] = useState<number>(19); // Prix par défaut
  const [promoPrice, setPromoPrice] = useState<number>(9.90); // Prix promotionnel par défaut
  // Initialiser avec des tableaux vides, ils seront remplis dynamiquement
  const [gratuitFeatures, setGratuitFeatures] = useState<string[]>([]);
  const [premiumFeatures, setPremiumFeatures] = useState<string[]>([]);

  // Fonction pour extraire les fonctionnalités à partir de la configuration
  const extractFeatures = (planConfig: Record<string, any>, isPremium: boolean = false): string[] => {
    const features: string[] = [];

    // Extraire les fonctionnalités des services
    if (planConfig.services) {
      const servicesCount = planConfig.services.included;
      features.push(isPremium ? `${servicesCount}+ sous-catégories de services` : `${servicesCount} sous-catégories de services`);
    }

    // Extraire les fonctionnalités des galeries
    if (planConfig.galleries) {
      const galleriesCount = planConfig.galleries.included;
      features.push(isPremium
        ? `${galleriesCount}+ galeries portfolio`
        : `${galleriesCount} galerie${galleriesCount > 1 ? 's' : ''} portfolio`);
    }

    // Extraire les fonctionnalités des zones d'intervention
    if (planConfig.interventionAreas) {
      const zoneKm = planConfig.interventionAreas.included;
      features.push(isPremium
        ? `Zone d'intervention de ${zoneKm}+ km`
        : `Zone d'intervention de ${zoneKm} km`);
    }

    // Extraire les fonctionnalités des conversations privées
    if (planConfig.conversations_messages_prives) {
      const convCount = planConfig.conversations_messages_prives.included;
      features.push(isPremium
        ? `${convCount}+ conversations privées`
        : `${convCount} conversations privées`);
    }

    // Extraire les fonctionnalités des réponses aux missions
    if (planConfig.mission_responses) {
      const count = planConfig.mission_responses.included;
      features.push(count >= 999 ? "Réponses illimitées aux missions" : `${count} réponses aux missions par mois`);
    }

    // Extraire les fonctionnalités des devis et factures
    if (planConfig.invoices && planConfig.quotes) {
      const invoiceCount = planConfig.invoices.included;
      const quoteCount = planConfig.quotes.included;

      if (invoiceCount >= 999 && quoteCount >= 999) {
        features.push("Devis et factures illimités");
      } else {
        features.push(`${quoteCount} devis et ${invoiceCount} factures générables`);
      }
    }

    // Extraire les fonctionnalités des favoris
    if (planConfig.favorites) {
      const favCount = planConfig.favorites.included;
      features.push(isPremium
        ? `${favCount}+ profils favoris`
        : `${favCount} profils favoris maximum`);
    }

    // Extraire les fonctionnalités de l'historique
    if (planConfig.activity_history) {
      const count = planConfig.activity_history.included;
      features.push(count >= 100 ? "Historique complet des activités" : `Historique limité (${count} activités)`);
    }

    // Fonctionnalités spécifiques au plan premium
    if (planConfig.jobi_bonus && planConfig.jobi_bonus.included > 0) {
      features.push(`${planConfig.jobi_bonus.included} Jobi offerts chaque mois`);
    }

    if (planConfig.phone_visibility && planConfig.phone_visibility.enabled) {
      features.push("Visibilité du numéro de téléphone");
    }

    return features;
  };

  // Initialiser les valeurs par défaut au chargement du composant
  useEffect(() => {
    // Définir des valeurs par défaut pour les fonctionnalités
    setGratuitFeatures([
      "2 sous-catégories de services",
      "1 galerie portfolio",
      "Zone d'intervention de 15 km",
      "2 conversations privées",
      "5 réponses aux missions par mois",
      "2 devis et 2 factures générables",
      "3 profils favoris maximum",
      "Historique limité (20 activités)"
    ]);

    setPremiumFeatures([
      "6+ sous-catégories de services",
      "3+ galeries portfolio",
      "Zone d'intervention de 30+ km",
      "15+ conversations privées",
      "Réponses illimitées aux missions",
      "Devis et factures illimités",
      "60+ profils favoris",
      "20 Jobi offerts chaque mois",
      "Visibilité du numéro de téléphone"
    ]);

    // Essayer de charger la configuration depuis l'API seulement si l'utilisateur est connecté
    const fetchSubscriptionConfig = async () => {
      // Ne pas faire d'appel API si l'utilisateur n'est pas connecté
      if (!isAuthenticated) {
        return;
      }

      try {
        const config = await subscriptionService.getSubscriptionConfig() as SubscriptionConfigData;

        // Initialiser les prix et fonctionnalités à partir de la configuration
        if (config && config.data) {
          // Prix de base premium
          if (config.data.premium && config.data.premium.prixDeBase) {
            setBasePrice(config.data.premium.prixDeBase);
          }

          // Prix promotionnel (si disponible dans la configuration)
          if (config.data.premium && config.data.premium.prixPromotionnel) {
            setPromoPrice(config.data.premium.prixPromotionnel);
          }

          // Extraire les fonctionnalités pour chaque plan
          if (config.data.gratuit) {
            const gratuitFeats = extractFeatures(config.data.gratuit, false);
            if (gratuitFeats.length > 0) {
              setGratuitFeatures(gratuitFeats);
            }
          }

          if (config.data.premium) {
            const premiumFeats = extractFeatures(config.data.premium, true);
            if (premiumFeats.length > 0) {
              setPremiumFeatures(premiumFeats);
            }
          }
        }
      } catch (error) {
        // Réduire le niveau de log pour les utilisateurs non connectés
        if (!isAuthenticated) {
          console.info("Configuration des abonnements non disponible (utilisateur non connecté)");
        } else {
          console.error("Erreur lors du chargement de la configuration des abonnements:", error);
        }
        // Les valeurs par défaut sont déjà définies, donc pas besoin de les redéfinir ici
      }
    };

    fetchSubscriptionConfig();
  }, [isAuthenticated]);

  const accordionItems = [
    {
      title: "Bonus exclusif : 20 Jobi offerts chaque mois",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Système de troc innovant :</strong> Les Jobi sont notre système de troc/échange exclusive de JobPartiel.fr qui vous permet de booster votre visibilité, d'accéder à des fonctionnalités premium et de faciliter les transactions sans frais supplémentaires.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Avantage Premium exclusif :</strong> Avec l'offre Premium, vous recevez <span className="font-semibold text-[#ff7a35]">automatiquement 20 Jobi offerts chaque mois</span> au renouvellement de votre abonnement, soit une valeur ajoutée de 10€ mensuelle incluse dans votre abonnement.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Utilisations stratégiques :</strong> Utilisez vos Jobi pour :</p>
          <ul className="list-disc pl-4 sm:pl-5 mb-2 sm:mb-3 space-y-1 text-gray-600 text-sm sm:text-base">
            <li>Apparaître en tête des résultats de recherche pendant 7 jours</li>
            <li>Mettre en avant vos services avec un badge "Recommandé"</li>
            <li>Débloquer des fonctionnalités exclusives temporaires</li>
            <li>Échanger des services avec d'autres jobbeurs sans transaction financière</li>
          </ul>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2 flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#ff7a35] mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
            </svg>
            <p className="text-xs sm:text-sm flex-1"><strong>Valeur ajoutée :</strong> Sur une année, l'abonnement Premium vous offre 240 Jobi, soit une valeur de 120€ incluse dans votre abonnement. Les utilisateurs Premium qui utilisent stratégiquement leurs Jobi obtiennent en moyenne 35% de missions supplémentaires par rapport aux utilisateurs gratuits.</p>
          </div>
        </div>
      )
    },
    {
      title: "Personnalisation des limites",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Offre gratuite :</strong> L'offre gratuite vous permet de démarrer avec des limitations raisonnables : 2 sous-catégories de services, 1 galerie portfolio, zone d'intervention de 15 km, 2 conversations privées et 2 devis/factures générables.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Offre Premium sans limites fixes :</strong> L'offre Premium commence avec des valeurs de base étendues (6+ sous-catégories, 3+ galeries, zone d'intervention de 30+ km, 15+ conversations privées) et <span className="font-semibold text-[#ff7a35]">toutes ces valeurs sont entièrement personnalisables</span> sans plafond prédéfini.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Flexibilité totale :</strong> Avec le plan Premium, vous définissez vos propres limites en fonction de vos besoins réels et de l'évolution de votre activité. Besoin de 10, 15 ou même 20 sous-catégories de services ? D'une zone d'intervention France entière ? Tout est possible et ajustable à tout moment.</p>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2">
            <p className="text-xs sm:text-sm"><strong>Impact sur vos revenus :</strong> Les utilisateurs Premium qui personnalisent leurs limites génèrent en moyenne 3,5 fois plus de revenus que les utilisateurs gratuits. Plus vous adaptez votre offre à vos capacités réelles, plus votre potentiel de gains augmente.</p>
          </div>
        </div>
      )
    },
    {
      title: "Zone d'intervention extensible",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Zone de base :</strong> L'offre gratuite vous permet d'intervenir dans un rayon de 15 km autour de votre position, ce qui est idéal pour démarrer avec des services de proximité.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Zone Premium de départ :</strong> L'offre Premium commence avec une zone de 30+ km, mais <span className="font-semibold text-[#ff7a35]">sans aucune limite fixe</span>. Vous pouvez l'étendre autant que nécessaire pour votre activité.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Personnalisation complète :</strong> Avec l'offre Premium, vous définissez votre propre zone d'intervention selon vos capacités réelles :</p>
          <ul className="list-disc pl-4 sm:pl-5 mb-2 sm:mb-3 space-y-1 text-gray-600 text-sm sm:text-base">
            <li>Extension jusqu'à <strong>120 km</strong> ou plus autour de votre position pour les services nécessitant votre présence</li>
            <li>Option <strong>France entière</strong> pour les services à distance ou qui ne nécessitent pas de présence physique</li>
            <li>Possibilité de définir plusieurs zones d'intervention si vous vous déplacez régulièrement</li>
            <li>Ajustement en temps réel selon l'évolution de votre activité et de votre clientèle</li>
          </ul>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2">
            <p className="text-xs sm:text-sm"><strong>Impact sur votre chiffre d'affaires :</strong> En passant d'un rayon de 30 km à 120 km, vous couvrez une surface environ 16 fois plus grande, multipliant d'autant vos opportunités commerciales. Nos utilisateurs qui étendent leur zone d'intervention voient leur nombre de missions augmenter de 240% en moyenne.</p>
          </div>
        </div>
      )
    },
    {
      title: "Offre de lancement exceptionnelle à -50% 🔥",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Réduction exclusive :</strong> Le plan Premium <span className="font-semibold text-[#ff7a35]">sans limites</span> est actuellement proposé à <span className="font-bold">{promoPrice.toFixed(2)}€/mois</span> au lieu de <span className="line-through">{basePrice.toFixed(2)}€/mois</span> pour les 500 premiers utilisateurs, soit une réduction exceptionnelle de 50%.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Avantage à vie :</strong> Ce tarif préférentiel reste valable pour toute la durée de votre abonnement tant que celui-ci reste actif, <span className="font-semibold">sans aucune augmentation future</span>, même lorsque le prix normal sera appliqué pour les nouveaux utilisateurs.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Économies substantielles :</strong> En profitant de cette offre maintenant, vous économisez <strong>{(basePrice - promoPrice).toFixed(2)}€ chaque mois</strong>, soit <strong>{((basePrice - promoPrice) * 12).toFixed(2)}€ par an</strong> et <strong>{((basePrice - promoPrice) * 36).toFixed(2)}€ sur 3 ans</strong> - tout en bénéficiant d'un abonnement entièrement personnalisable sans limites fixes.</p>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2 flex items-start">
            <div className="text-[#ff7a35] mr-2 mt-1 flex-shrink-0">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 animate-pulse" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-xs sm:text-sm flex-1"><strong>Attention :</strong> Une fois le quota de 500 utilisateurs atteint, le prix reviendra définitivement au tarif normal de {basePrice.toFixed(2)}€/mois. Les utilisateurs qui auront souscrit pendant cette période promotionnelle conserveront leur tarif préférentiel à vie.</p>
          </div>
        </div>
      )
    },
    {
      title: "Comment personnaliser mon abonnement Premium sans limites ?",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Personnalisation illimitée :</strong> Depuis votre espace personnel, vous pouvez ajuster chaque aspect de votre abonnement Premium <span className="font-semibold text-[#ff7a35]">sans aucune restriction prédéfinie</span>, en fonction de vos besoins réels et de l'évolution de votre activité.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Services sans limite :</strong> Commencez avec 6+ sous-catégories et ajoutez-en autant que nécessaire pour couvrir toute la gamme de vos compétences. Certains de nos utilisateurs proposent jusqu'à 15-20 services différents pour maximiser leurs opportunités.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Zone d'intervention sur mesure :</strong> Définissez exactement votre périmètre d'action, qu'il s'agisse d'une zone locale étendue (jusqu'à 120 km ou plus) ou d'une couverture nationale pour les services à distance.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Galeries et conversations extensibles :</strong> Ajoutez autant de galeries portfolio et de conversations privées que nécessaire pour présenter efficacement votre travail et communiquer avec vos clients potentiels.</p>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2 flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#ff7a35] mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
            <p className="text-xs sm:text-sm flex-1"><strong>Conseil d'expert :</strong> Les jobbeurs qui personnalisent leur abonnement en fonction de leurs capacités réelles génèrent en moyenne 2,8 fois plus de revenus mensuels. Accédez à ces options depuis l'onglet "Personnaliser mon plan" dans la section Premium de votre tableau de bord.</p>
          </div>
        </div>
      )
    },
    {
      title: "Flexibilité totale : annulation sans engagement",
      content: (
        <div>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Liberté absolue :</strong> Vous pouvez annuler votre abonnement Premium à tout moment depuis votre espace personnel, <span className="font-semibold">sans aucun frais, pénalité ou justification</span>. Deux clics suffisent pour arrêter votre abonnement.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Avantages préservés :</strong> Après annulation, vous continuez à bénéficier de tous les avantages Premium (y compris les limites personnalisées) jusqu'à la fin de la période déjà payée. Aucune fonctionnalité n'est restreinte pendant cette période.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Transition en douceur :</strong> À la fin de votre période Premium, votre compte bascule automatiquement vers le plan gratuit. Vos données, profil et historique sont intégralement préservés - seules les limites d'utilisation sont ajustées.</p>
          <p className="mb-2 sm:mb-3"><strong className="text-[#ff7a35] block sm:inline">Réactivation simple :</strong> Si vous souhaitez revenir au Premium ultérieurement, vous pouvez réactiver votre abonnement en un clic. Si vous étiez parmi les 500 premiers utilisateurs, vous retrouverez automatiquement votre tarif préférentiel de {promoPrice.toFixed(2)}€/mois.</p>
          <div className="bg-[#FFF8F3] p-2 sm:p-3 rounded-lg border border-[#ff7a35]/20 mt-2 flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <p className="text-xs sm:text-sm flex-1"><strong>Notre garantie :</strong> Nous offrons une garantie "satisfait ou remboursé" de 14 jours pour tous les nouveaux abonnements Premium. Si vous n'êtes pas entièrement satisfait, contactez notre support pour un remboursement intégral, sans question ni condition.</p>
          </div>
        </div>
      )
    }
  ];

  return (
    <section id="pricing" className="py-4 bg-[#fffbf1]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Titre */}
        <div className="text-center mb-12">
          <span className="inline-block px-4 py-1.5 bg-[#ff7a35]/10 text-[#ff7a35] font-semibold rounded-full text-sm mb-4">
            Offre Premium sans limites fixes
          </span>
          <h2 className="text-3xl md:text-5xl font-bold mb-5 leading-tight">
            Des <span className="text-[#ff7a35] relative">
              abonnements personnalisables
              <svg className="absolute w-full h-3 -bottom-1 left-0 text-[#ff7a35]/20" viewBox="0 0 200 9" preserveAspectRatio="none">
                <path d="M0,9 C50,0 150,0 200,9" fill="currentColor" />
              </svg>
            </span> sans restrictions
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto text-lg">
            Chez <strong className="text-[#ff7a35]">jobpartiel.fr</strong>, nous vous laissons définir vos propres limites. Notre offre Premium s'adapte
            entièrement à vos besoins réels avec des options extensibles à l'infini. Commencez gratuitement et évoluez à votre rythme.
          </p>
          <div className="mt-6 flex justify-center">
            <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
              <svg className="mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                <circle cx="4" cy="4" r="3" />
              </svg>
              Promotion -50% pour les 500 premiers utilisateurs
            </span>
          </div>
        </div>

        {/* Garanties */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-12">
          <div className="bg-white rounded-lg p-5 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div className="w-16 h-16 rounded-full bg-[#ff7a35]/10 flex items-center justify-center mb-3">
              <Shield className="h-8 w-8 text-[#ff7a35]" />
            </div>
            <h3 className="font-semibold mb-2 text-lg">Sans engagement</h3>
            <p className="text-sm text-gray-600">Résiliez à tout moment sans frais ni justification, avec garantie satisfait ou remboursé</p>
          </div>
          <div className="bg-white rounded-lg p-5 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div className="w-16 h-16 rounded-full bg-[#ff7a35]/10 flex items-center justify-center mb-3">
              <Award className="h-8 w-8 text-[#ff7a35]" />
            </div>
            <h3 className="font-semibold mb-2 text-lg">100% des revenus</h3>
            <p className="text-sm text-gray-600">Conservez l'intégralité de vos gains, sans aucune commission sur vos transactions</p>
          </div>
          <div className="bg-white rounded-lg p-5 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-3">
              <Settings className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-semibold mb-2 text-lg">Sans limites fixes</h3>
            <p className="text-sm text-gray-600">Définissez vos propres limites et adaptez-les à tout moment selon l'évolution de votre activité</p>
          </div>
          <div className="bg-white rounded-lg p-5 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div className="w-16 h-16 rounded-full bg-[#ff7a35]/10 flex items-center justify-center mb-3">
              <Clock className="h-8 w-8 text-[#ff7a35]" />
            </div>
            <h3 className="font-semibold mb-2 text-lg">Démarrage immédiat</h3>
            <p className="text-sm text-gray-600">Commencez gratuitement et passez au Premium quand vous êtes prêt, sans perte de données</p>
          </div>
          <div className="bg-white rounded-lg p-5 flex flex-col items-center text-center shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
            <div className="w-16 h-16 rounded-full bg-[#ff7a35]/10 flex items-center justify-center mb-3">
              <Users className="h-8 w-8 text-[#ff7a35]" />
            </div>
            <h3 className="font-semibold mb-2 text-lg">Bonus mensuels</h3>
            <p className="text-sm text-gray-600">Recevez 20 Jobi offerts chaque mois avec l'abonnement Premium pour booster votre visibilité</p>
          </div>
        </div>

        {/* Section Tarifs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          <div className="space-y-8 flex">
            <div className="flex-1 flex items-center">
              <PricingCard
                price="€0.00"
                title="Offre Gratuite"
                description="Pour démarrer et explorer la plateforme en tant que jobbeur."
                buttonText="Commencer gratuitement"
                buttonLink="/inscription"
                serviceType="jobbeur"
                features={gratuitFeatures}
              />
            </div>
          </div>

          <div className="space-y-8 flex">
            <div className="flex-1 flex items-center">
              <PricingCard
                price={`€${promoPrice.toFixed(2)}`}
                title="Offre Premium"
                description="Idéal pour les professionnels qui veulent développer leur activité."
                buttonText="Choisir le Premium"
                buttonLink="/inscription"
                popular={true}
                serviceType="jobbeur"
                features={premiumFeatures}
              />
            </div>
          </div>
        </div>

        {/* Section FAQ */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold mb-4">Tout savoir sur notre offre Premium sans limites</h3>
            <div className="flex justify-center items-center gap-2 mb-4">
              <div className="h-1 w-20 bg-gradient-to-r from-transparent to-[#FF7A35] opacity-50"></div>
              <div className="w-2 h-2 rounded-full bg-[#FF7A35]"></div>
              <div className="h-1 w-20 bg-gradient-to-l from-transparent to-[#FF7A35] opacity-50"></div>
            </div>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Découvrez comment notre offre Premium entièrement personnalisable peut s'adapter parfaitement à vos besoins, quelle que soit l'ampleur de votre activité
            </p>
            <div className="mt-4 inline-block px-4 py-2 bg-green-50 text-green-800 rounded-lg border border-green-100">
              <span className="font-medium">Offre spéciale :</span> Profitez de -50% à vie pour les 500 premiers abonnés
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4 relative transition-opacity duration-200 opacity-100">
            {accordionItems.map((item, index) => {
              const isOpen = openItems.includes(index);
              return (
                <AccordionItem
                  key={index}
                  index={index}
                  title={item.title}
                  content={item.content}
                  isOpen={isOpen}
                  onClick={() => {
                    const isEven = index % 2 === 0;
                    const pairIndex = isEven ? index + 1 : index - 1;
                    const pairExists = pairIndex < accordionItems.length;

                    setOpenItems(prev => {
                      // Sur les écrans < md (768px), on gère chaque élément individuellement
                      if (window.matchMedia('(max-width: 767px)').matches) {
                        const newOpenItems = prev.includes(index)
                          ? prev.filter(item => item !== index)
                          : [...prev.filter(item => item !== index), index];

                        // Pas de défilement automatique pour éviter les conflits avec la FAQ
                        return newOpenItems;
                      } else {
                        // Sur les écrans ≥ md, on garde la synchronisation par paire
                        const bothClosed = !prev.includes(index) && !prev.includes(pairIndex);

                        if (bothClosed) {
                          // Fermer tous les accordéons des autres lignes
                          const itemsInCurrentRow = [index];
                          if (pairExists) itemsInCurrentRow.push(pairIndex);

                          // Filtrer pour ne garder que les éléments de la ligne actuelle
                          return itemsInCurrentRow;
                        } else {
                          // Si on ferme, on ferme juste la paire actuelle
                          return prev.filter(item => item !== index && item !== pairIndex);
                        }
                      }
                    });
                  }}
                />
              );
            })}
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Vous avez d'autres questions sur nos offres ?
            </p>
            <Link to="/dashboard/support/new" data-discover="true" className="inline-flex items-center text-[#FF7A35] font-medium hover:text-[#ff6b2c] transition-colors">
              Contactez notre équipe
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}