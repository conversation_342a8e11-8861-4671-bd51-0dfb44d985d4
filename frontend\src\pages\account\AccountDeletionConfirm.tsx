import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Paper, 
  Typography, 
  TextField, 
  Button, 
  Box, 
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import { Lock, Warning, ArrowBack } from '@mui/icons-material';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import logger from '../../utils/logger';

const AccountDeletionConfirm: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [submitError, setSubmitError] = useState<string>('');

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      notify('Lien de suppression invalide', 'error');
      navigate('/');
    }
  }, [token, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Effacer les erreurs quand l'utilisateur tape
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Effacer l'erreur de soumission
    if (submitError) {
      setSubmitError('');
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!formData.email) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }
    
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Effacer les erreurs précédentes
    setSubmitError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/users/delete/confirm`, {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify({
          token,
          email: formData.email,
          password: formData.password
        })
      });

      const data = await response.json();

      if (data.success) {
        notify(data.message, 'success');
        // Rediriger vers la page de confirmation finale
        navigate(`/account/delete/final?token=${token}&userId=${data.userId}`);
      } else {
        // Afficher l'erreur spécifique
        setSubmitError(data.message || 'Erreur lors de la vérification');

        // Si c'est une erreur d'identifiants, mettre en évidence les champs concernés
        if (data.message && (
          data.message.toLowerCase().includes('identifiants') ||
          data.message.toLowerCase().includes('mot de passe') ||
          data.message.toLowerCase().includes('email')
        )) {
          // Mettre en évidence les champs email et mot de passe
          setErrors({
            email: data.message.toLowerCase().includes('email') ? 'Vérifiez votre adresse email' : '',
            password: data.message.toLowerCase().includes('mot de passe') || data.message.toLowerCase().includes('identifiants') ? 'Mot de passe incorrect' : ''
          });
          notify(data.message, 'error');
        }
      }
    } catch (error: any) {
      logger.error('Erreur lors de la confirmation de suppression:', error);
      const errorMessage = error.response?.data?.message || 'Une erreur est survenue lors de la vérification';
      setSubmitError(errorMessage);
      notify(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/parametres');
  };

  if (!token) {
    return null;
  }

  return (
    <Container maxWidth="sm" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Warning sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" component="h1" gutterBottom color="error">
            Confirmation de suppression
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Pour des raisons de sécurité, veuillez confirmer votre identité
          </Typography>
        </Box>

        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Attention :</strong> Cette action est irréversible. Vos données personnelles seront anonymisées conformément au RGPD.
          </Typography>
        </Alert>

        {submitError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body2">
              {submitError}
            </Typography>
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Adresse email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={!!errors.email}
            helperText={errors.email}
            margin="normal"
            required
            autoComplete="email"
            slotProps={{
              input: {
                startAdornment: <Lock sx={{ mr: 1, color: 'action.active' }} />
              }
            }}
          />

          <TextField
            fullWidth
            label="Mot de passe"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            error={!!errors.password}
            helperText={errors.password}
            margin="normal"
            required
            autoComplete="current-password"
            slotProps={{
              input: {
                startAdornment: <Lock sx={{ mr: 1, color: 'action.active' }} />
              }
            }}
          />

          <Box sx={{ mt: 4, display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              startIcon={<ArrowBack />}
              fullWidth
              sx={{ order: { xs: 2, sm: 1 } }}
            >
              Annuler
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              color="error"
              disabled={loading}
              fullWidth
              sx={{ order: { xs: 1, sm: 2 } }}
            >
              {loading ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Vérification...
                </>
              ) : (
                'Confirmer mon identité'
              )}
            </Button>
          </Box>
        </form>

        <Divider sx={{ my: 3 }} />

        <Alert severity="info">
          <Typography variant="body2">
            <strong>Problème de connexion ?</strong>
            <br />
            Si vous avez oublié votre mot de passe, vous pouvez{' '}
            <Button
              variant="text"
              size="small"
              onClick={() => navigate('/dashboard/parametres')}
              sx={{ p: 0, textDecoration: 'underline', fontSize: 'inherit' }}
            >
              le réinitialiser ici
            </Button>
            {' '}avant de procéder à la suppression.
            <br />
            <br />
            Assurez-vous d'utiliser la même adresse email et le même mot de passe que pour votre connexion habituelle.
          </Typography>
        </Alert>
      </Paper>
    </Container>
  );
};

export default AccountDeletionConfirm;
