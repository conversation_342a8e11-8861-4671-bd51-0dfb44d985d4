import { supabase } from '../config/supabase';
import { encryptDataAsync, hashEmail } from '../utils/encryption';
import logger from '../utils/logger';

/**
 * Ajoute un email à la file d'attente (email_queue)
 */
export async function queueEmail(to: string, subject: string, html: string, attachments?: any[]) {
  // Bloquer les emails anonymisés pour le RGPD
  if (to.includes('@supprime.local')) {
    logger.warn('Email anonymisé bloqué (RGPD)', { 
      email: to.substring(0, 20) + '...', 
      subject 
    });
    return; // Ne pas ajouter à la queue
  }

  // logger.info('Ajout de l\'email à la file d\'attente:', {
  //   to,
  //   subject,
  //   html,
  //   attachments: attachments ? attachments.length : 0
  // });

  // Chiffrer l'adresse email avant de la stocker
  const encryptedEmail = await encryptDataAsync(to);
  
  // Créer le hash de l'email pour la recherche anti-spam
  const emailHashValue = hashEmail(to);

  const emailData: any = {
    to_email: encryptedEmail,
    email_hash: emailHashValue,
    subject,
    html
  };

  // Ajouter les attachments s'ils existent
  if (attachments && attachments.length > 0) {
    emailData.attachments = JSON.stringify(attachments);
  }

  const { error } = await supabase
    .from('email_queue')
    .insert([emailData]);
  if (error) throw new Error("Erreur lors de l'ajout à la file d'attente email : " + error.message);
}