import express from 'express';
import { SecurityMonitoringController } from '../controllers/securityMonitoring';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting spécifique pour les routes de monitoring de sécurité
const securityMonitoringLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: process.env.NODE_ENV === 'production' ? 30 : 100, // Limite réduite pour les routes sensibles
  message: {
    error: 'Trop de requêtes vers le monitoring de sécurité, veuillez réessayer plus tard'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Toutes les routes nécessitent une authentification admin
router.use(authMiddleware.authenticateToken);
router.use(authMiddleware.checkRole(['jobpadm']));
router.use(securityMonitoringLimiter);

// Dashboard principal de sécurité
router.get('/dashboard', SecurityMonitoringController.getDashboard);

// Logs de sécurité avec pagination et filtres
router.get('/logs', SecurityMonitoringController.getSecurityLogs);

// IPs bloquées
router.get('/blocked-ips', SecurityMonitoringController.getBlockedIPs);

// Débloquer une IP spécifique
router.delete('/blocked-ips/:ip', SecurityMonitoringController.unblockIP);

// Bloquer une IP avec durée personnalisée
router.post('/block-ip', SecurityMonitoringController.blockIP);

// Statistiques de sécurité avancées
router.get('/stats', SecurityMonitoringController.getSecurityStats);

// Alertes de sécurité en temps réel
router.get('/alerts', SecurityMonitoringController.getSecurityAlerts);

// Configuration de sécurité
router.get('/config', SecurityMonitoringController.getSecurityConfig);

// Générer un rapport de conformité PDF
router.get('/compliance-report', SecurityMonitoringController.generateComplianceReport);

// Effectuer un audit de sécurité complet
router.post('/security-audit', SecurityMonitoringController.performSecurityAudit);

export default router;
