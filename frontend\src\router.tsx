import { createBrowserRouter } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import App from './App';
import Erreur404Etc from './components/Erreur404Etc';
import { RoutePrivee } from './components/PrivateRoute';
import LoadingSpinner from './components/LoadingSpinner';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useEffect } from 'react';

// Composants de base (non lazy loaded car fréquemment utilisés)
import Hero from './components/Hero';
import Discover from './components/Discover';
import WhyChoose from './components/WhyChoose';
import HowItWorks from './components/HowItWorks';
import NeedHelp from './components/NeedHelp';
import logger from './utils/logger';

// Pages de l'éditeur de cartes
const CardTemplatesPage = lazy(() => import('./pages/dashboard/CardTemplatesPage'));
const CardEditorPage = lazy(() => import('./pages/dashboard/CardEditorPage'));

// Lazy loading des composants
const Pricing = lazy(() => import('./components/Pricing'));
const CallToAction = lazy(() => import('./components/CallToAction'));

// Lazy loading des pages d'authentification
const Register = lazy(() => import('./pages/auth/Register'));
const Login = lazy(() => import('./pages/auth/Login'));
const ForgotPassword = lazy(() => import('./pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/auth/ResetPassword'));
const VerifyEmail = lazy(() => import('./pages/auth/VerifyEmail'));
const VerifyTwoFactor = lazy(() => import('./pages/auth/VerifyTwoFactor'));
const QuoteAcceptance = lazy(() => import('./pages/QuoteAcceptance'));

// Lazy loading des composants d'administration
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const VPSMonitoring = lazy(() => import('./pages/admin/VPSMonitoring'));
const PromoCodesPage = lazy(() => import('./pages/admin/PromoCodesPage'));
const ReportedContentListPage = lazy(() => import('./pages/admin/ReportedContentListPage'));
const ReportedContentDetailPage = lazy(() => import('./pages/admin/ReportedContentDetailPage'));
const OpenRouterInfoPage = lazy(() => import('./pages/admin/OpenRouterInfoPage'));
const ContentModerationLogsPage = lazy(() => import('./pages/admin/ContentModerationLogsPage'));
const ModerationStatsPage = lazy(() => import('./pages/admin/ModerationStatsPage'));
const EntrepriseDocumentValidation = lazy(() => import('./pages/admin/EntrepriseDocumentValidation'));
const EntrepriseDocumentDetailPage = lazy(() => import('./pages/admin/EntrepriseDocumentDetailPage'));
const OpenRouterStatsPage = lazy(() => import('./pages/admin/OpenRouterStatsPage'));
const OpenRouterPricingPage = lazy(() => import('./pages/admin/OpenRouterPricingPage'));
const AiImageStats = lazy(() => import('./pages/admin/AiImageStats'));
const SecurityMonitoring = lazy(() => import('./pages/admin/SecurityMonitoring'));
const BillingStats = lazy(() => import('./pages/admin/BillingStats'));

// Lazy loading des composants dashboard
const Dashboard = lazy(() => import('./pages/dashboard/Dashboard'));
const DashboardLayout = lazy(() => import('./pages/dashboard/DashboardLayout'));
const MissionsPage = lazy(() => import('./pages/dashboard/ToutesLesMissions/MissionsView'));
const SingleMissionPage = lazy(() => import('./pages/dashboard/ToutesLesMissions/SingleMissionPage'));
const PostMission = lazy(() => import('./pages/dashboard/missions/PostMission'));
const OffresPage = lazy(() => import('./pages/dashboard/missions/OffresPage'));
const PlanningPage = lazy(() => import('./pages/dashboard/PlanningPage'));
const PaymentsPage = lazy(() => import('./pages/dashboard/PaymentsPage'));
const BillingPage = lazy(() => import('./pages/dashboard/billing/BillingPage'));
const BillingSettingsPage = lazy(() => import('./pages/dashboard/billing/BillingSettingsPage'));
const AdvancedStatsPage = lazy(() => import('./pages/dashboard/AdvancedStatsPage'));
const TrainingPage = lazy(() => import('./pages/dashboard/TrainingPage'));
const DashboardProfil = lazy(() => import('./pages/dashboard/profil/Profile'));
const Favoris = lazy(() => import('./pages/dashboard/profil/Favoris'));
const SecuritySettings = lazy(() => import('./pages/dashboard/profil/SecuritySettings'));
const MesAvis = lazy(() => import('./pages/dashboard/avis/MesAvis'));
const SettingsPage = lazy(() => import('./pages/dashboard/SettingsPage'));
const PremiumPage = lazy(() => import('./pages/dashboard/PremiumPage'));
const MarketingPage = lazy(() => import('./pages/dashboard/MarketingPage'));
const BadgesPage = lazy(() => import('./pages/dashboard/BadgesPage'));
const NetworkPage = lazy(() => import('./pages/dashboard/NetworkPage'));
const QualityPage = lazy(() => import('./pages/dashboard/QualityPage'));
const JobiPage = lazy(() => import('./pages/dashboard/JobiPage'));
const AiCreditsPage = lazy(() => import('./pages/dashboard/AiCreditsPage'));
const MatchingMissions = lazy(() => import('./pages/dashboard/ToutesLesMissions/MatchingMissions'));
const NotificationsPage = lazy(() => import('./pages/dashboard/Notifications'));
const MessagesPage = lazy(() => import('./pages/Messages/MessagesPage'));
const HistoriqueEvaluationsPage = lazy(() => import('./pages/dashboard/HistoriqueEvaluationsPage'));
const ClientList = lazy(() => import('./pages/dashboard/billing/ClientList'));

// Lazy loading des composants de rapport de bugs
const BugReportPage = lazy(() => import('./pages/BugReportPage'));
const BugReportListPage = lazy(() => import('./pages/BugReportListPage'));
const BugReportDetailPage = lazy(() => import('./pages/BugReportDetailPage'));
const AdminBugReportListPage = lazy(() => import('./pages/admin/BugReport/AdminBugReportListPage'));
const AdminBugReportDetailPage = lazy(() => import('./pages/admin/BugReport/AdminBugReportDetailPage'));

// Lazy loading des composants de support
const SupportDashboard = lazy(() => import('./pages/support/SupportDashboard'));
const SupportTicketList = lazy(() => import('./pages/support/SupportTicketList'));
const SupportTicketForm = lazy(() => import('./pages/support/SupportTicketForm'));
const TicketDetails = lazy(() => import('./pages/support/TicketDetails'));
const MyAssignedTickets = lazy(() => import('./components/support/MyAssignedTickets'));

// Lazy loading de l'import pour AdminTicketTagsPage
const AdminTicketTagsPage = lazy(() => import('./pages/admin/AdminTicketTagsPage'));

// Lazy loading de la nouvelle page ResponseTemplatesManager
const ResponseTemplatesManager = lazy(() => import('./pages/admin/ResponseTemplatesManager'));

// Lazy loading de la nouvelle page RedisAdmin
const RedisAdmin = lazy(() => import('./pages/admin/RedisAdmin'));

// Lazy loading de la nouvelle page EmailQueueAdmin
const EmailQueueAdmin = lazy(() => import('./pages/admin/EmailQueueAdmin'));

// Lazy loading de la page de gestion des abonnés à la newsletter
const NewsletterSubscribersPage = lazy(() => import('./pages/admin/NewsletterSubscribersPage'));

// Lazy loading de la page de gestion des utilisateurs
const UserManagement = lazy(() => import('./pages/admin/UserManagement'));

// Lazy loading des pages légales
const MentionsLegales = lazy(() => import('./pages/mentions-legales'));
const ConditionsGenerales = lazy(() => import('./pages/conditions-generales'));
const PolitiqueConfidentialite = lazy(() => import('./pages/politique-confidentialite'));
const CookiesPage = lazy(() => import('./pages/cookies'));

// Lazy loading des pages newsletter
const VerifyNewsletter = lazy(() => import('./pages/newsletter/VerifyNewsletter'));
const UnsubscribeNewsletter = lazy(() => import('./pages/newsletter/UnsubscribeNewsletter'));

// Lazy loading des pages de suppression de compte
const AccountDeletionConfirm = lazy(() => import('./pages/account/AccountDeletionConfirm'));
const AccountDeletionFinal = lazy(() => import('./pages/account/AccountDeletionFinal'));
const AccountDeleted = lazy(() => import('./pages/account/AccountDeleted'));

// Lazy loading de la page de profil public
const PublicProfile = lazy(() => import('./pages/dashboard/profil/PublicProfile'));

// Lazy loading des pages SEO dynamiques
const ServiceCityPage = lazy(() => import('./pages/services/ServiceCityPage'));
const ServicesPage = lazy(() => import('./pages/services/ServicesPage'));
const SearchResultsPage = lazy(() => import('./pages/services/SearchResultsPage'));

// Composant de redirection pour Support
const SupportRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    navigate('/dashboard/support/tickets', { replace: true });
  }, [navigate]);

  return <LoadingSpinner />;
};

// Composant de redirection pour les tickets spécifiques
const SupportTicketRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const ticketId = params.ticketId;

  useEffect(() => {
    logger.info("Paramètres de redirection:", params);
    // Vérifie que nous ne sommes pas déjà sur le chemin de destination pour éviter une boucle
    if (ticketId && !location.pathname.includes('/dashboard/support/ticket/')) {
      logger.info("Redirection vers:", `/dashboard/support/ticket/${ticketId}`);
      navigate(`/dashboard/support/ticket/${ticketId}`, { replace: true });
    } else if (!ticketId) {
      logger.error("Pas d'ID de ticket trouvé dans l'URL pour la redirection");
      navigate('/dashboard/support/tickets', { replace: true });
    }
  }, [navigate, ticketId, params, location]);

  return <LoadingSpinner />;
};

// Composant Home avec Suspense
const Home = () => (
  <div>
    <Hero />
    <Suspense fallback={<LoadingSpinner />}>
    <Discover />
    <HowItWorks />
    <WhyChoose />
    <Pricing />
    <CallToAction />
    <NeedHelp />
    </Suspense>
  </div>
);

// Routes configuration
export const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    errorElement: <Erreur404Etc code={404} />,
    children: [
      {
        index: true,
        element: <Home />
      },
      {
        path: 'login',
        element: <Suspense fallback={<LoadingSpinner />}><Login /></Suspense>
      },
      {
        path: 'inscription',
        element: <Suspense fallback={<LoadingSpinner />}><Register /></Suspense>
      },
      {
        path: 'forgot-password',
        element: <Suspense fallback={<LoadingSpinner />}><ForgotPassword /></Suspense>
      },
      {
        path: 'reset-password',
        element: <Suspense fallback={<LoadingSpinner />}><ResetPassword /></Suspense>
      },
      {
        path: 'verify-email',
        element: <Suspense fallback={<LoadingSpinner />}><VerifyEmail /></Suspense>
      },
      {
        path: 'verify-two-factor',
        element: <Suspense fallback={<LoadingSpinner />}><VerifyTwoFactor /></Suspense>
      },
      {
        path: 'quote-acceptance/:quoteId',
        element: <Suspense fallback={<LoadingSpinner />}><QuoteAcceptance /></Suspense>
      },
      {
        path: 'services',
        element: <Suspense fallback={<LoadingSpinner />}><ServicesPage /></Suspense>
      },
      {
        path: 'services/search',
        element: <Suspense fallback={<LoadingSpinner />}><SearchResultsPage /></Suspense>
      },
      {
        path: '/dashboard',
        element: (
          <RoutePrivee roleRequis="jobutil">
            <Suspense fallback={<LoadingSpinner />}>
              <DashboardLayout />
            </Suspense>
          </RoutePrivee>
        ),
        children: [
          {
            index: true,
            element: <Suspense fallback={<LoadingSpinner />}><Dashboard /></Suspense>
          },
          {
            path: 'missions',
            element: <Suspense fallback={<LoadingSpinner />}><MissionsPage /></Suspense>
          },
          {
            path: 'missions/:id',
            element: <Suspense fallback={<LoadingSpinner />}><SingleMissionPage /></Suspense>
          },
          {
            path: 'missions/correspondantes',
            element: <Suspense fallback={<LoadingSpinner />}><MatchingMissions /></Suspense>
          },
          {
            path: 'missions/poster-une-mission',
            element: <Suspense fallback={<LoadingSpinner />}><PostMission /></Suspense>
          },
          {
            path: 'missions/offres',
            element: <Suspense fallback={<LoadingSpinner />}><OffresPage /></Suspense>
          },
          {
            path: 'messages',
            element: <Suspense fallback={<LoadingSpinner />}><MessagesPage /></Suspense>
          },
          {
            path: 'messages/:id',
            element: <Suspense fallback={<LoadingSpinner />}><MessagesPage /></Suspense>
          },
          {
            path: 'planning',
            element: <Suspense fallback={<LoadingSpinner />}><PlanningPage /></Suspense>
          },
          {
            path: 'paiements_et_finances',
            element: <Suspense fallback={<LoadingSpinner />}><PaymentsPage /></Suspense>
          },
          {
            path: 'facturation',
            element: <Suspense fallback={<LoadingSpinner />}><BillingPage /></Suspense>
          },
          {
            path: 'facturation/parametres',
            element: <Suspense fallback={<LoadingSpinner />}><BillingSettingsPage /></Suspense>
          },
          {
            path: 'statistiques-avances',
            element: <Suspense fallback={<LoadingSpinner />}><AdvancedStatsPage /></Suspense>
          },
          {
            path: 'formations_et_conseils',
            element: <Suspense fallback={<LoadingSpinner />}><TrainingPage /></Suspense>
          },
          {
            path: 'parametres',
            element: <Suspense fallback={<LoadingSpinner />}><SettingsPage /></Suspense>
          },
          {
            path: 'card-templates',
            element: <Suspense fallback={<LoadingSpinner />}><CardTemplatesPage /></Suspense>
          },
          {
            path: 'card-editor',
            element: <Suspense fallback={<LoadingSpinner />}><CardEditorPage /></Suspense>
          },
          {
            path: 'card-editor/:id',
            element: <Suspense fallback={<LoadingSpinner />}><CardEditorPage /></Suspense>
          },
          {
            path: 'profil',
            element: <Suspense fallback={<LoadingSpinner />}><DashboardProfil /></Suspense>
          },
          {
            path: 'profil/favoris',
            element: <Suspense fallback={<LoadingSpinner />}><Favoris /></Suspense>
          },
          {
            path: 'profil/securite',
            element: <Suspense fallback={<LoadingSpinner />}><SecuritySettings /></Suspense>
          },
          {
            path: 'profil/:slug',
            element: <Suspense fallback={<LoadingSpinner />}><DashboardProfil /></Suspense>
          },
          {
            path: 'avis',
            element: <Suspense fallback={<LoadingSpinner />}><MesAvis /></Suspense>
          },
          {
            path: 'premium',
            element: <Suspense fallback={<LoadingSpinner />}><PremiumPage /></Suspense>
          },
          {
            path: 'marketing',
            element: <Suspense fallback={<LoadingSpinner />}><MarketingPage /></Suspense>
          },
          {
            path: 'badges',
            element: <Suspense fallback={<LoadingSpinner />}><BadgesPage /></Suspense>
          },
          {
            path: 'network',
            element: <Suspense fallback={<LoadingSpinner />}><NetworkPage /></Suspense>
          },
          {
            path: 'qualite-et-performance',
            element: <Suspense fallback={<LoadingSpinner />}><QualityPage /></Suspense>
          },
          {
            path: 'jobi',
            element: <Suspense fallback={<LoadingSpinner />}><JobiPage /></Suspense>
          },
          {
            path: 'ai-credits',
            element: <Suspense fallback={<LoadingSpinner />}><AiCreditsPage /></Suspense>
          },
          {
            path: 'notifications',
            element: <Suspense fallback={<LoadingSpinner />}><NotificationsPage /></Suspense>
          },
          {
            path: 'bug-reports',
            element: <Suspense fallback={<LoadingSpinner />}><BugReportListPage /></Suspense>
          },
          {
            path: 'bug-reports/nouveau',
            element: <Suspense fallback={<LoadingSpinner />}><BugReportPage /></Suspense>
          },
          {
            path: 'bug-reports/:id',
            element: <Suspense fallback={<LoadingSpinner />}><BugReportDetailPage /></Suspense>
          },
          {
            path: 'support',
            element: <Suspense fallback={<LoadingSpinner />}><SupportRedirect /></Suspense>
          },
          {
            path: 'support/tickets',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketList /></Suspense>
          },
          {
            path: 'support/new',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketForm /></Suspense>
          },
          {
            path: 'support/assigned',
            element: <Suspense fallback={<LoadingSpinner />}><MyAssignedTickets /></Suspense>
          },
          {
            path: 'support/ticket/:ticketId',
            element: <Suspense fallback={<LoadingSpinner />}><TicketDetails /></Suspense>
          },
          {
            path: 'support/ticket/:ticketId/edit',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketForm /></Suspense>
          },
          {
            path: 'support/tags',
            element: <Suspense fallback={<LoadingSpinner />}><AdminTicketTagsPage /></Suspense>
          },
          {
            path: 'support/templates',
            element: <Suspense fallback={<LoadingSpinner />}><ResponseTemplatesManager /></Suspense>
          },
          {
            path: 'historique-et-evaluations',
            element: <Suspense fallback={<LoadingSpinner />}><HistoriqueEvaluationsPage /></Suspense>
          },
          {
            path: 'clients',
            element: <Suspense fallback={<LoadingSpinner />}><ClientList /></Suspense>
          }
        ]
      },
      {
        path: '/admin',
        element: (
          <RoutePrivee roleRequis="jobpadm">
            <Suspense fallback={<LoadingSpinner />}><AdminDashboard /></Suspense>
          </RoutePrivee>
        ),
        children: [
          {
            path: 'promocodes',
            element: <Suspense fallback={<LoadingSpinner />}><PromoCodesPage /></Suspense>
          },
          {
            path: 'vps',
            element: <Suspense fallback={<LoadingSpinner />}><VPSMonitoring /></Suspense>
          },
          {
            path: 'bug-reports',
            element: <Suspense fallback={<LoadingSpinner />}><AdminBugReportListPage /></Suspense>
          },
          {
            path: 'bug-reports/:id',
            element: <Suspense fallback={<LoadingSpinner />}><AdminBugReportDetailPage /></Suspense>
          },
          {
            path: 'bug-reports/:id/edit',
            element: <Suspense fallback={<LoadingSpinner />}><AdminBugReportDetailPage isEditing={true} /></Suspense>
          },
          {
            path: 'support',
            element: <Suspense fallback={<LoadingSpinner />}><SupportDashboard /></Suspense>
          },
          {
            path: 'support/tickets',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketList /></Suspense>
          },
          {
            path: 'support/new',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketForm /></Suspense>
          },
          {
            path: 'support/assigned',
            element: <Suspense fallback={<LoadingSpinner />}><MyAssignedTickets /></Suspense>
          },
          {
            path: 'support/ticket/:ticketId',
            element: <Suspense fallback={<LoadingSpinner />}><TicketDetails /></Suspense>
          },
          {
            path: 'support/ticket/:ticketId/edit',
            element: <Suspense fallback={<LoadingSpinner />}><SupportTicketForm /></Suspense>
          },
          {
            path: 'support/tags',
            element: <Suspense fallback={<LoadingSpinner />}><AdminTicketTagsPage /></Suspense>
          },
          {
            path: 'support/templates',
            element: <Suspense fallback={<LoadingSpinner />}><ResponseTemplatesManager /></Suspense>
          },
          {
            path: 'reported-content',
            element: <Suspense fallback={<LoadingSpinner />}><ReportedContentListPage /></Suspense>
          },
          {
            path: 'reported-content/:id',
            element: <Suspense fallback={<LoadingSpinner />}><ReportedContentDetailPage /></Suspense>
          },
          {
            path: 'moderation-logs',
            element: <Suspense fallback={<LoadingSpinner />}><ContentModerationLogsPage /></Suspense>
          },
          {
            path: 'moderation-stats',
            element: <Suspense fallback={<LoadingSpinner />}><ModerationStatsPage /></Suspense>
          },
          {
            path: 'openrouter-info',
            element: <Suspense fallback={<LoadingSpinner />}><OpenRouterInfoPage /></Suspense>
          },
          {
            path: 'entreprise-documents',
            element: <Suspense fallback={<LoadingSpinner />}><EntrepriseDocumentValidation /></Suspense>
          },
          {
            path: 'entreprise-documents/:userId',
            element: <Suspense fallback={<LoadingSpinner />}><EntrepriseDocumentDetailPage /></Suspense>
          },
          {
            path: 'redis',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}><RedisAdmin /></Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'email-queue',
            element: <RoutePrivee roleRequis="jobpadm"><Suspense fallback={<LoadingSpinner />}><EmailQueueAdmin /></Suspense></RoutePrivee>
          },
          {
            path: 'newsletter-subscribers',
            element: <RoutePrivee roleRequis="jobpadm"><Suspense fallback={<LoadingSpinner />}><NewsletterSubscribersPage /></Suspense></RoutePrivee>
          },
          {
            path: 'openrouter-stats',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <OpenRouterStatsPage />
                </Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'openrouter-pricing',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <OpenRouterPricingPage />
                </Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'ai-image-stats',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <AiImageStats />
                </Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'security-monitoring',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <SecurityMonitoring />
                </Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'billing-stats',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <BillingStats />
                </Suspense>
              </RoutePrivee>
            )
          },
          {
            path: 'user-management',
            element: (
              <RoutePrivee roleRequis="jobpadm">
                <Suspense fallback={<LoadingSpinner />}>
                  <UserManagement />
                </Suspense>
              </RoutePrivee>
            )
          }
        ]
      },
      {
        path: 'error-404',
        element: <Erreur404Etc code={404} />
      },
      {
        path: 'support/ticket/:ticketId',
        element: <Suspense fallback={<LoadingSpinner />}><SupportTicketRedirect /></Suspense>
      },
      {
        path: 'support/ticket/:ticketId/edit',
        element: <Suspense fallback={<LoadingSpinner />}><SupportTicketRedirect /></Suspense>
      },
      {
        path: 'support',
        element: <Suspense fallback={<LoadingSpinner />}><SupportRedirect /></Suspense>
      },
      {
        path: 'services/:service/:ville',
        element: <Suspense fallback={<LoadingSpinner />}><ServiceCityPage /></Suspense>
      },
      {
        path: '*',
        element: <Erreur404Etc code={404} />
      }
    ]
  },
  { path: '/mentions-legales', element: <Suspense fallback={<LoadingSpinner />}><MentionsLegales /></Suspense> },
  { path: '/conditions-generales', element: <Suspense fallback={<LoadingSpinner />}><ConditionsGenerales /></Suspense> },
  { path: '/politique-confidentialite', element: <Suspense fallback={<LoadingSpinner />}><PolitiqueConfidentialite /></Suspense> },
  { path: '/cookies', element: <Suspense fallback={<LoadingSpinner />}><CookiesPage /></Suspense> },
  { path: '/newsletter/verify', element: <Suspense fallback={<LoadingSpinner />}><VerifyNewsletter /></Suspense> },
  { path: '/newsletter/unsubscribe', element: <Suspense fallback={<LoadingSpinner />}><UnsubscribeNewsletter /></Suspense> },
  { path: '/account/delete/confirm', element: <Suspense fallback={<LoadingSpinner />}><AccountDeletionConfirm /></Suspense> },
  { path: '/account/delete/final', element: <Suspense fallback={<LoadingSpinner />}><AccountDeletionFinal /></Suspense> },
  { path: '/account/deleted', element: <Suspense fallback={<LoadingSpinner />}><AccountDeleted /></Suspense> },
  { path: '/profil/:slug', element: <Suspense fallback={<LoadingSpinner />}><PublicProfile /></Suspense> }
]);

export default router;