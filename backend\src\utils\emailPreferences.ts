import { dbService } from '../services/db';
import logger from './logger';
import { decryptUserDataAsync } from './encryption';

/**
 * Vérifie si un email peut être envoyé en fonction des préférences de notification de l'utilisateur
 * @param userId - ID de l'utilisateur
 * @param emailType - Type d'email à envoyer (ex: 'connexion', 'messages', etc.)
 * @returns {Promise<boolean>} - True si l'email peut être envoyé, false sinon
 */
export const canSendEmail = async (userId: string, emailType: string): Promise<boolean> => {
  try {
    // Récupérer les préférences de notification de l'utilisateur
    const { data: userData, error } = await dbService.supabase
      .from('users')
      .select('notification_preferences')
      .eq('id', userId)
      .single();

    if (error) {
      logger.error('Erreur lors de la récupération des données utilisateur pour les préférences email:', error);
      return true; // Par défaut, autoriser l'envoi en cas d'erreur
    }

    // Déchiffrer les données utilisateur si nécessaire
    const decryptedUserData = userData ? await decryptUserDataAsync(userData) : null;

    const notificationPreferences = decryptedUserData?.notification_preferences || {};

    // Vérifier si les notifications par email sont activées globalement
    const emailEnabled = notificationPreferences.email_enabled !== false;

    // Vérifier si le type spécifique de notification est activé
    const typeNotificationsEnabled =
      notificationPreferences.email_notifications?.[emailType as keyof typeof notificationPreferences.email_notifications] !== false;

    logger.info('Vérification des préférences email:', {
      userId,
      emailType,
      emailEnabled,
      typeNotificationsEnabled
    });

    // L'email peut être envoyé si les deux options sont activées
    return emailEnabled && typeNotificationsEnabled;
  } catch (error) {
    logger.error('Erreur lors de la vérification des préférences email:', error);
    // Par défaut, on renvoie true en cas d'erreur pour ne pas bloquer les emails importants
    return true;
  }
};

/**
 * Envoie un email si les préférences utilisateur le permettent
 * @param userId - ID de l'utilisateur
 * @param emailType - Type d'email à envoyer (ex: 'connexion', 'messages', etc.)
 * @param sendEmailFn - Fonction d'envoi d'email à exécuter si les préférences le permettent
 * @returns {Promise<boolean>} - True si l'email a été envoyé, false sinon
 */
export const sendEmailWithPreferenceCheck = async (
  userId: string,
  emailType: string,
  sendEmailFn: () => Promise<void>
): Promise<boolean> => {
  try {
    // Vérifier si l'email peut être envoyé selon les préférences utilisateur
    const shouldSendEmail = await canSendEmail(userId, emailType);
    
    // Envoyer l'email seulement si les préférences le permettent
    if (shouldSendEmail) {
      await sendEmailFn();
      logger.info(`Email de type ${emailType} envoyé avec succès`, { userId });
      return true;
    } else {
      logger.info(`Email de type ${emailType} non envoyé en raison des préférences utilisateur`, { userId });
      return false;
    }
  } catch (error) {
    logger.error(`Erreur lors de l'envoi d'email de type ${emailType}:`, error);
    return false;
  }
}; 