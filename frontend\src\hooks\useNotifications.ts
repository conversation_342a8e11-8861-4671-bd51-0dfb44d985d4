import { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { notify } from '@/components/Notification';
import { getCommonHeaders } from '../utils/headers';
import { API_CONFIG } from '../config/api';
import logger from '../utils/logger';
import { fetchCsrfToken } from '../services/csrf';

// Cache pour les données des notifications
const apiCache = {
  // Données mises en cache
  notifications: null as any,
  unreadCount: null as any,
  // Timestamps des dernières requêtes
  lastNotificationsRequestTime: 0,
  lastUnreadCountRequestTime: 0,
  // Délais minimum entre les requêtes (en ms)
  notificationsThreshold: 10000, // 10 secondes
  unreadCountThreshold: 15000,   // 15 secondes
  // Drapeaux pour les requêtes en cours (partagés entre toutes les instances du hook)
  isNotificationsRequestPending: false,
  isUnreadCountRequestPending: false,
  // Promesses des requêtes en cours pour permettre d'attendre leur résolution
  pendingNotificationsPromise: null as Promise<any> | null,
  pendingUnreadCountPromise: null as Promise<any> | null
};

interface Notification {
  id: string;
  type: 'jobi' | 'message' | 'mission' | 'system';
  title: string;
  content: string;
  is_read: boolean;
  is_archived: boolean;
  link?: string;
  created_at: string;
}

interface PaginationData {
  page: number;
  pageSize: number;
  totalPages: number;
  totalCount: number;
}

interface NotificationFilters {
  page?: number;
  type?: string;
  search?: string;
  unread?: boolean;
}

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData | null>(null);
  const mountedRef = useRef(false);
  const lastRequestIdRef = useRef(0);

  // Charger les notifications avec limitation des appels
  const fetchNotifications = useCallback(async (archived = false, filters?: NotificationFilters, forceRefresh = false) => {
    const now = Date.now();
    const cacheKey = JSON.stringify({ archived, filters });
    
    // Si les données sont en cache et que le délai n'est pas dépassé, on utilise le cache
    if (!forceRefresh && 
        apiCache.notifications && 
        apiCache.notifications.key === cacheKey && 
        now - apiCache.lastNotificationsRequestTime < apiCache.notificationsThreshold) {
      
      // Utiliser les données du cache
      setNotifications(apiCache.notifications.data || []);
      setPagination(apiCache.notifications.pagination);
      setLoading(false);
      return;
    }
    
    // Si une requête est déjà en cours, on attend sa résolution au lieu d'en lancer une nouvelle
    if (apiCache.isNotificationsRequestPending) {
      if (apiCache.pendingNotificationsPromise) {
        try {
          setLoading(true);
          // Attendre que la requête en cours se termine
          await apiCache.pendingNotificationsPromise;
          
          // Vérifier si les données du cache correspondent à notre requête
          if (apiCache.notifications && apiCache.notifications.key === cacheKey) {
            setNotifications(apiCache.notifications.data || []);
            setPagination(apiCache.notifications.pagination);
          }
        } catch (err) {
          // Ignorer l'erreur, elle sera gérée par la requête d'origine
        } finally {
          setLoading(false);
        }
      }
      return;
    }
    
    // Marquer qu'une requête est en cours
    apiCache.isNotificationsRequestPending = true;
    
    try {
      const requestId = ++lastRequestIdRef.current;
      setLoading(true);
      
      // Créer une promesse pour cette requête
      apiCache.pendingNotificationsPromise = (async () => {
        try {
          await fetchCsrfToken();
          const headers = await getCommonHeaders();
          
          const params = {
            archived,
            page: filters?.page || 1,
            ...(filters?.type && filters.type !== 'all' && { type: filters.type }),
            ...(filters?.search && { search: filters.search }),
            ...(filters?.unread && { unread: true })
          };
          
          const response = await axios.get(`${API_CONFIG.baseURL}/api/notifications`, {
            headers,
            withCredentials: true,
            params
          });
          
          // Ne traiter la réponse que si c'est la dernière requête envoyée
          if (mountedRef.current && requestId === lastRequestIdRef.current) {
            // logger.info('Réponse du chargement des notifications:', response.data);
            
            if (response.data.success) {
              // Mettre à jour le cache
              apiCache.notifications = {
                key: cacheKey,
                data: response.data.data || [],
                pagination: response.data.pagination
              };
              apiCache.lastNotificationsRequestTime = now;
              
              // Mettre à jour l'état
              setNotifications(response.data.data || []);
              setPagination(response.data.pagination);
            }
          }
          
          return response;
        } catch (err) {
          if (mountedRef.current) {
            setError('Erreur lors du chargement des notifications');
            notify('Erreur lors du chargement des notifications', 'error');
          }
          throw err;
        }
      })();
      
      // Attendre la résolution de la promesse
      await apiCache.pendingNotificationsPromise;
      
    } catch (err) {
      logger.error('Erreur lors du chargement des notifications:', err);
    } finally {
      // Marquer que la requête est terminée
      apiCache.isNotificationsRequestPending = false;
      apiCache.pendingNotificationsPromise = null;
      
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  // Charger le nombre de notifications non lues avec limitation des appels
  const fetchUnreadCount = useCallback(async (forceRefresh = false) => {
    const now = Date.now();
    
    // Si les données sont en cache et que le délai n'est pas dépassé, on utilise le cache
    if (!forceRefresh && 
        apiCache.unreadCount !== null && 
        now - apiCache.lastUnreadCountRequestTime < apiCache.unreadCountThreshold) {
      
      // Utiliser les données du cache
      setUnreadCount(apiCache.unreadCount);
      return;
    }
    
    // Si une requête est déjà en cours, on attend sa résolution au lieu d'en lancer une nouvelle
    if (apiCache.isUnreadCountRequestPending) {
      if (apiCache.pendingUnreadCountPromise) {
        try {
          // Attendre que la requête en cours se termine
          await apiCache.pendingUnreadCountPromise;
          
          // Utiliser la valeur mise à jour
          setUnreadCount(apiCache.unreadCount || 0);
        } catch (err) {
          // Ignorer l'erreur, elle sera gérée par la requête d'origine
        }
      }
      return;
    }
    
    // Marquer qu'une requête est en cours
    apiCache.isUnreadCountRequestPending = true;
    
    try {
      const requestId = ++lastRequestIdRef.current;
      
      // Créer une promesse pour cette requête
      apiCache.pendingUnreadCountPromise = (async () => {
        try {
          await fetchCsrfToken();
          const headers = await getCommonHeaders();
          const response = await axios.get(`${API_CONFIG.baseURL}/api/notifications/unread-count`, {
            headers,
            withCredentials: true
          });
          
          // Ne traiter la réponse que si c'est la dernière requête envoyée
          if (mountedRef.current && requestId === lastRequestIdRef.current) {
            // logger.info('Réponse du comptage des notifications non lues:', response.data);
            
            if (response.data.success) {
              // Mettre à jour le cache
              apiCache.unreadCount = response.data.count;
              apiCache.lastUnreadCountRequestTime = now;
              
              // Mettre à jour l'état
              setUnreadCount(response.data.count);
            }
          }
          
          return response;
        } catch (err) {
          logger.error('Erreur lors du comptage des notifications non lues:', err);
          throw err;
        }
      })();
      
      // Attendre la résolution de la promesse
      await apiCache.pendingUnreadCountPromise;
      
    } catch (err) {
      logger.error('Erreur lors du comptage des notifications non lues:', err);
    } finally {
      // Marquer que la requête est terminée
      apiCache.isUnreadCountRequestPending = false;
      apiCache.pendingUnreadCountPromise = null;
    }
  }, []);

  // Marquer une notification comme lue ou non lue
  const toggleReadStatus = useCallback(async (notificationId: string, isRead: boolean) => {
    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/${notificationId}/toggle-read`,
        { is_read: isRead },
        {
          headers,
          withCredentials: true
        }
      );

      logger.info('Réponse du changement de statut de lecture:', response.data);

      if (response.data.success && mountedRef.current) {
        // Mettre à jour l'état local immédiatement
        setNotifications(prev =>
          prev.map(notif =>
            notif.id === notificationId ? { ...notif, is_read: isRead } : notif
          )
        );
        
        // Forcer le rafraîchissement du compteur
        fetchUnreadCount(true);
        
        notify(
          `Notification marquée comme ${isRead ? 'lue' : 'non lue'}`,
          'success'
        );
      }
    } catch (err) {
      notify(`Erreur lors du marquage de la notification comme ${isRead ? 'lue' : 'non lue'}`, 'error');
    }
  }, [fetchUnreadCount]);

  // Archiver ou désarchiver une notification
  const toggleArchiveStatus = useCallback(async (notificationId: string, isArchived: boolean) => {
    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const notification = notifications.find(n => n.id === notificationId);
      
      if (!notification) return;

      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/${notificationId}/archive`,
        { is_archived: isArchived },
        {
          headers,
          withCredentials: true
        }
      );

      logger.info('Réponse du changement de statut d\'archive:', response.data);

      if (response.data.success) {
        // Forcer le rafraîchissement des notifications
        await fetchNotifications(notification.is_archived, { page: 1 }, true);
        
        // Forcer le rafraîchissement du compteur
        await fetchUnreadCount(true);

        notify(
          `Notification ${isArchived ? 'archivée' : 'désarchivée'}`,
          'success'
        );
      }
    } catch (error) {
      logger.error('Erreur lors de la modification du statut d\'archive:', error);
      notify(`Erreur lors de ${isArchived ? 'l\'archivage' : 'la désarchivation'}`, 'error');
      // En cas d'erreur, recharger les notifications et le compteur
      await fetchNotifications(false, { page: 1 }, true);
      await fetchUnreadCount(true);
    }
  }, [notifications, fetchUnreadCount, fetchNotifications]);

  // Supprimer une notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.delete(`${API_CONFIG.baseURL}/api/notifications/${notificationId}`, {
        headers,
        withCredentials: true
      });

      logger.info('Réponse de la suppression de la notification:', response.data);

      if (response.data.success && mountedRef.current) {
        // Mettre à jour l'état local immédiatement
        setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
        
        // Forcer le rafraîchissement du compteur
        fetchUnreadCount(true);
        
        notify('Notification supprimée', 'success');
      }
    } catch (err) {
      notify('Erreur lors de la suppression de la notification', 'error');
    }
  }, [fetchUnreadCount]);

  // Marquer toutes les notifications comme lues
  const markAllAsRead = useCallback(async () => {
    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/mark-all-read`,
        {},
        {
          headers,
          withCredentials: true
        }
      );

      logger.info('Réponse du marquage de toutes les notifications comme lues:', response.data);

      if (response.data.success && mountedRef.current) {
        // Mettre à jour l'état local immédiatement
        setNotifications(prev =>
          prev.map(notif => ({ ...notif, is_read: true }))
        );
        
        // Forcer le rafraîchissement du compteur
        fetchUnreadCount(true);
        
        notify('Toutes les notifications ont été marquées comme lues', 'success');
      }
    } catch (err) {
      notify('Erreur lors du marquage des notifications comme lues', 'error');
    }
  }, [fetchUnreadCount]);

  // Supprimer toutes les notifications
  const deleteAllNotifications = useCallback(async (archived = false) => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/notifications/delete-all`,
        {
          headers,
          withCredentials: true,
          params: { archived }
        }
      );

      logger.info('Réponse de la suppression de toutes les notifications:', response.data);

      if (response.data.success && mountedRef.current) {
        // Mettre à jour l'état local immédiatement
        setNotifications([]);
        
        // Forcer le rafraîchissement du compteur
        fetchUnreadCount(true);
        
        notify('Toutes les notifications ont été supprimées', 'success');
      }
    } catch (err) {
      notify('Erreur lors de la suppression des notifications', 'error');
    }
  }, [fetchUnreadCount]);

  // Archiver toutes les notifications
  const archiveAllNotifications = useCallback(async () => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/archive-all`,
        {},
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success && mountedRef.current) {
        // Mettre à jour l'état local immédiatement
        setNotifications([]);
        
        // Forcer le rafraîchissement du compteur
        await fetchUnreadCount(true);
        
        notify('Toutes les notifications ont été archivées', 'success');
      }
    } catch (err) {
      notify('Erreur lors de l\'archivage des notifications', 'error');
      // En cas d'erreur, recharger les données
      await fetchNotifications(false, { page: 1 }, true);
      await fetchUnreadCount(true);
    }
  }, [fetchUnreadCount, fetchNotifications]);

  useEffect(() => {
    mountedRef.current = true;

    // Fonction pour charger les données initiales
    const initializeData = async () => {
      if (!mountedRef.current) return;
      
      try {
        await fetchCsrfToken();
        // On charge d'abord les notifications
        await fetchNotifications(false, { page: 1 });
        // Puis le compteur
        await fetchUnreadCount();
      } catch (err) {
        logger.error('Erreur lors du chargement initial:', err);
      }
    };

    initializeData();

    // Mise à jour périodique uniquement du compteur avec un intervalle plus long
    const interval = setInterval(() => {
      if (mountedRef.current) {
        fetchUnreadCount();
      }
    }, 60000); // Passage à 60 secondes pour réduire l'impact environnemental (si on modifie le compteur ne pas oublier de mettre à jour le message dans Bandeau d'information écologique)

    return () => {
      mountedRef.current = false;
      clearInterval(interval);
    };
  }, []); // On retire les dépendances pour éviter les re-rendus

  return {
    notifications,
    unreadCount,
    loading,
    error,
    pagination,
    fetchNotifications,
    fetchUnreadCount,
    toggleReadStatus,
    toggleArchiveStatus,
    deleteNotification,
    markAllAsRead,
    deleteAllNotifications,
    archiveAllNotifications
  };
}; 