import { Router, Request } from 'express';
import { AuthController } from '../controllers/auth';
import { validateResetPassword, asyncHandler } from '../utils/inputValidation';
import { authMiddleware } from '../middleware/authMiddleware';
import { setCSRFToken } from '../middleware/csrf';
import logger from '../utils/logger';
import passport from '../config/passport';
import { tokenService } from '../services/tokenService';
import rateLimit from 'express-rate-limit';

// Étendre les types de session pour inclure le code de parrainage
declare module 'express-session' {
  interface SessionData {
    referralCode?: string;
  }
}

export const authRoutes = Router();
const authController = new AuthController();

// Rate limiter pour les codes de vérification à deux facteurs
const twoFactorCodeLimiter = rateLimit({
    windowMs: 1 * 30 * 1000, // 30 secondes
    max: 2, // 2 requêtes par fenêtre
    message: {
      success: false,
      message: 'Trop de demandes de codes de vérification. Veuillez réessayer dans 30 secondes.',
      toastType: 'error'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Utiliser l'ID utilisateur comme clé pour le rate limiting
    keyGenerator: (req: Request) => {
      // Pour la vérification initiale, utiliser l'email
      if (req.body && req.body.email) {
        return req.body.email;
      }
      // Pour les autres routes, utiliser l'ID utilisateur
      return req.user?.userId || req.ip;
    }
  });
  
  // Rate limiter pour les tentatives de vérification de code
  const twoFactorVerifyLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 tentatives par fenêtre
    message: {
      success: false,
      message: 'Trop de tentatives de vérification. Veuillez réessayer dans 15 minutes.',
      toastType: 'error'
    },
    standardHeaders: true,
    legacyHeaders: false
  });

  // Rate limiter spécifique pour les tokens socket
  const socketTokenLimiter = rateLimit({
    windowMs: 2 * 60 * 1000, // 2 minutes au lieu de 1
    max: process.env.NODE_ENV === 'development' ? 50 : 30, // Réduire les limites : 50 en dev, 30 en prod
    message: {
      success: false,
      message: 'Trop de demandes de token socket. Veuillez réessayer dans 2 minutes.',
      toastType: 'error'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Utiliser uniquement l'IP pour identifier les clients (plus permissif)
    keyGenerator: (req: Request) => {
      return req.ip || 'unknown-ip';
    },
    // Ajouter un délai progressif
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  });
  
// Route pour récupérer le token CSRF (doit être avant les autres routes obligatoires)
authRoutes.get('/csrf', setCSRFToken, (req: any, res) => {
    try {
        if (process.env.NODE_ENV === 'development') {
            // logger.info('🔑 CSRF Token Request', {
            //     headers: {
            //         origin: req.headers.origin,
            //         'user-agent': req.headers['user-agent'],
            //         cookie: req.headers.cookie
            //     },
            //     ip: req.ip,
            //     method: req.method,
            //     path: req.path,
            //     sessionId: req.sessionID,
            //     hasSession: !!req.session
            // });
        }

        // Ajouter les en-têtes CORS explicitement (ici a ajouter obligatoirement pour eviter les erreurs de cors et csrf)
        const origin = req.get('origin');
        const allowedOrigins = process.env.NODE_ENV === 'production'
            ? ['https://jobpartiel.fr', 'https://www.jobpartiel.fr', 'https://api.jobpartiel.fr', 'https://dev-api.jobpartiel.fr', 'https://dev.jobpartiel.fr', 'https://www.dev.jobpartiel.fr']
            : ['http://localhost:5173', 'http://localhost:3000', 'http://localhost:3001', 'https://dev.jobpartiel.fr'];

        if (!origin) {
            logger.warn('CSRF request without origin header', {
                ip: req.ip,
                userAgent: req.headers['user-agent']
            });
            // Ne pas bloquer, mais utiliser une origine par défaut
        }

        if (origin && !allowedOrigins.includes(origin)) {
            logger.warn('CSRF request from disallowed origin', {
                origin,
                ip: req.ip,
                userAgent: req.headers['user-agent']
            });
            res.status(403).json({
                error: 'Origin not allowed',
                status: 'error'
            });
            return;
        }

        // Définir les headers CORS seulement si l'origine est valide
        if (origin) {
            res.header('Access-Control-Allow-Origin', origin);
        }
        res.header('Access-Control-Allow-Credentials', 'true');
        res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token, X-Requested-With, Accept, Origin, Cookie');
        res.header('Access-Control-Expose-Headers', 'X-CSRF-Token, Set-Cookie');

        // Le token est déjà généré par le middleware setCSRFToken
        const token = req.session.csrfToken;

        if (process.env.NODE_ENV === 'development') {
            logger.info('🔍 CSRF Token Status', {
                hasToken: !!token,
                sessionId: req.sessionID,
                tokenPreview: token ? token.substring(0, 10) + '...' : 'none'
            });
        }

        if (!token) {
            logger.error('❌ CSRF token not generated', {
                sessionId: req.sessionID,
                hasSession: !!req.session
            });
            res.status(500).json({
                error: 'CSRF token not generated',
                status: 'error'
            });
            return;
        }

        if (process.env.NODE_ENV === 'development') {
            logger.info('✅ CSRF Token envoyé avec succès');
        }

        res.status(200).json({
            csrfToken: token,
            status: 'success'
        });
    } catch (error) {
        logger.error('CSRF Token Generation Failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
        });
        res.status(500).json({
            error: 'Failed to generate CSRF token',
            details: error instanceof Error ? error.message : 'Unknown error',
            status: 'error'
        });
    }
});

// Routes d'authentification
authRoutes.post('/inscription', asyncHandler(authController.inscription));
authRoutes.post('/login', asyncHandler(authController.login));
authRoutes.post('/verify-email', asyncHandler(authController.verifyEmail));
authRoutes.get('/verify-email', asyncHandler(authController.verifyEmail));
authRoutes.post('/resend-verification', asyncHandler(authController.resendVerification));
authRoutes.post('/forgot-password', asyncHandler(authController.forgotPassword));
authRoutes.post('/reset-password', validateResetPassword, asyncHandler(authController.resetPassword));
authRoutes.post('/validate-reset-token', asyncHandler(authController.validateResetToken));
authRoutes.post('/verify-profil', asyncHandler(authController.verifyProfil));

// Routes d'authentification à deux facteurs
authRoutes.post('/verify-two-factor', twoFactorVerifyLimiter, asyncHandler(authController.verifyTwoFactorAuth));
authRoutes.post('/two-factor/enable', authMiddleware.authenticateToken, twoFactorCodeLimiter, asyncHandler(authController.enableTwoFactorAuth));
authRoutes.post('/two-factor/disable', authMiddleware.authenticateToken, asyncHandler(authController.disableTwoFactorAuth));
authRoutes.get('/two-factor/status', authMiddleware.authenticateToken, asyncHandler(authController.getTwoFactorStatus));

// Routes d'authentification Google
authRoutes.get('/google', (req, res, next) => {
    // Capturer le code de parrainage s'il est présent dans l'URL
    const referralCode = req.query.ref as string;
    if (referralCode) {
        // Stocker le code de parrainage en session pour l'utiliser après l'authentification
        req.session.referralCode = referralCode;
    }

    passport.authenticate('google', {
        scope: ['profile', 'email'],
        prompt: 'select_account'
    })(req, res, next);
});

authRoutes.get('/google/callback',
    passport.authenticate('google', {
        failureRedirect: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/login?error=google_auth_failed`,
        session: false
    }),
    authController.googleCallback
);

// Routes protégées
authRoutes.post('/logout', asyncHandler(authController.logout));
authRoutes.get('/me', authMiddleware.authenticateToken, asyncHandler(authController.me)); // Route quand l'utilisateur est authentifié à la page dashboard
authRoutes.get('/profil', authMiddleware.authenticateToken, asyncHandler(authController.me)); // Route quand l'utilisateur est authentifié à la page profil du dashboard
authRoutes.post('/refresh-token', asyncHandler(authMiddleware.refreshTokens));

// Route pour obtenir un token socket sécurisé
authRoutes.get('/socket/token', socketTokenLimiter, authMiddleware.authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    if (!user || !user.userId) {
      logger.warn('Tentative d\'accès /socket/token sans authentification', {
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
      res.status(401).json({ success: false, message: 'Non authentifié' });
      return;
    }
    // Génère un token socket court (1h)
    const token = await tokenService.generateToken(
      user.userId,
      user.email || '',
      user.userType || 'jobbeur',
      user.role || 'jobutil',
      'access'
    );
    res.json({ success: true, token });
    return;
  } catch (error) {
    logger.error('Erreur lors de la génération du token socket:', error);
    res.status(500).json({ success: false, message: 'Erreur lors de la génération du token socket' });
    return;
  }
});