import { Router, Request, Response } from 'express';
import { redis } from '../config/redis';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// Middleware d'authentification + admin
router.use(authMiddleware.authenticateToken);
router.use(authMiddleware.checkRole(['jobpadm']));

// Rate limiter pour l'administration Redis
const redisAdminLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 40, // 40 requêtes par minute par IP
  message: { error: 'Trop de requêtes sur l\'admin Redis, veuillez réessayer dans 1 minute.' },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter à toutes les routes de ce router
router.use(redisAdminLimiter);

// GET /api/admin/redis/keys?pattern=*&limit=50&offset=0
router.get('/keys', async (req: Request, res: Response) => {
  try {
    const pattern = (req.query.pattern as string) || '*';
    const limit = parseInt((req.query.limit as string) || '50', 10);
    const offset = parseInt((req.query.offset as string) || '0', 10);
    const allKeys = await redis.keys(pattern);
    const total = allKeys.length;
    const keys = allKeys.slice(offset, offset + limit);
    res.json({ success: true, keys, total });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur lors de la récupération des clés Redis', error });
  }
});

// DELETE /api/admin/redis/keys (body: { keys: string[] })
router.delete('/keys', async (req: Request, res: Response) => {
  try {
    const keys: string[] = req.body.keys;
    if (!Array.isArray(keys) || keys.length === 0) {
      res.status(400).json({ success: false, message: 'Aucune clé fournie' });
    }
    const deleted = await redis.del(...keys);
    res.json({ success: true, deleted });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur lors de la suppression des clés Redis', error });
  }
});

// POST /api/admin/redis/flush-all - ROUTE DÉSACTIVÉE POUR SÉCURITÉ
router.post('/flush-all', async (req: Request, res: Response) => {
  // SÉCURITÉ: Route désactivée en production pour éviter les attaques
  if (process.env.NODE_ENV === 'production') {
    res.status(403).json({
      success: false,
      message: 'Cette opération est désactivée en production pour des raisons de sécurité'
    });
    return;
  }

  try {
    // Ajouter une vérification supplémentaire de l'IP en développement
    const allowedIPs = ['127.0.0.1', '::1', 'localhost'];
    const clientIP = req.ip || req.socket.remoteAddress || '';

    if (!allowedIPs.includes(clientIP)) {
      res.status(403).json({
        success: false,
        message: 'Accès refusé depuis cette IP'
      });
      return;
    }

    await redis.flushdb();
    res.json({ success: true, message: 'Tous les clés Redis ont été supprimées (flushdb)' });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Erreur lors du flush Redis', error });
  }
});

export default router;