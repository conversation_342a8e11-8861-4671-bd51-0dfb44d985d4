import { useEffect, useCallback, useState, useMemo, useRef } from 'react';
import { logger } from './logger';

// Debounce function pour limiter les appels fréquents
export const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Intersection Observer Hook pour lazy loading
export const useIntersectionObserver = (
  elementRef: React.RefObject<Element>,
  callback: () => void,
  options = { threshold: 0.1 }
) => {
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        callback();
        observer.disconnect();
      }
    }, options);

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [elementRef, callback, options]);
};

// Web Worker pour les calculs lourds
export const createWorker = (workerFunction: Function) => {
  const blob = new Blob(
    [`(${workerFunction.toString()})()`],
    { type: 'application/javascript' }
  );
  return new Worker(URL.createObjectURL(blob));
};

// Optimisation de la mémoire
export const useMemoryOptimization = (cleanup: () => void) => {
  useEffect(() => {
    return () => {
      cleanup();
      if (window.gc) {
        window.gc();
      }
    };
  }, [cleanup]);
};

// Optimisation des animations
export const useAnimationOptimization = (callback: () => void) => {
  return useCallback(() => {
    requestAnimationFrame(() => {
      requestIdleCallback(() => {
        callback();
      });
    });
  }, [callback]);
};

// Optimisation du DOM
export const useDOMOptimization = () => {
  useEffect(() => {
    // Nettoyer les classes CSS non utilisées
    const removeUnusedStyles = () => {
      const stylesheets = Array.from(document.styleSheets);
      stylesheets.forEach(stylesheet => {
        try {
          const rules = Array.from(stylesheet.cssRules);
          rules.forEach(rule => {
            if (rule instanceof CSSStyleRule) {
              const selector = rule.selectorText;
              if (!document.querySelector(selector)) {
                stylesheet.deleteRule(rules.indexOf(rule));
              }
            }
          });
        } catch (e) {
          logger.warn('Cannot read stylesheet rules', e);
        }
      });
    };

    // Observer pour détecter les mutations du DOM
    const observer = new MutationObserver(debounce(() => {
      removeUnusedStyles();
    }, 1000));

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => observer.disconnect();
  }, []);
};

// Hook de virtualisation pour les listes longues
export interface VirtualizationProps {
  itemHeight: number;
  totalItems: number;
  containerHeight: number;
  overscan?: number;
}

export const useVirtualization = ({
  itemHeight,
  totalItems,
  containerHeight,
  overscan = 3
}: VirtualizationProps) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + overscan,
      totalItems
    );

    return {
      items: Array.from({ length: endIndex - startIndex }, (_, index) => startIndex + index),
      startOffset: startIndex * itemHeight,
      endOffset: (totalItems - endIndex) * itemHeight
    };
  }, [scrollTop, itemHeight, containerHeight, totalItems, overscan]);

  const onScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    onScroll,
    visibleItems,
    totalHeight: totalItems * itemHeight
  };
};

// Hook pour le code splitting dynamique des composants
export const useDynamicImport = <T,>(
  importFn: () => Promise<T>,
  threshold = 0.1
) => {
  const [component, setComponent] = useState<T | null>(null);
  const ref = useRef<HTMLDivElement>(null);

  useIntersectionObserver(
    ref,
    () => {
      importFn().then(setComponent);
    },
    { threshold }
  );

  return [ref, component] as const;
};
