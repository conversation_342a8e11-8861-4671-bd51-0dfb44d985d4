import React, { useState, useEffect } from 'react';
import {
  Box, Container, Typography, Paper, Button, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, TextField, IconButton, Dialog, DialogActions,
  DialogContent, DialogContentText, DialogTitle, CircularProgress, Tooltip
} from '@mui/material';
import { Edit, Save, Add, Refresh, Info } from '@mui/icons-material';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import { styled } from '@mui/material/styles';
import { getCommonHeaders } from '../../utils/headers';
import { fetchCsrfToken } from '../../services/csrf';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés
const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
  }
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

// Interface pour les tarifs des modèles
interface ModelPricing {
  id: string;
  model: string;
  input_price_per_million: number;
  output_price_per_million: number;
  context_window: number;
  max_output: number;
  latency_seconds: number | null;
  throughput_tokens_per_second: number | null;
  created_at: string;
  updated_at: string;
}

// Composant principal
const OpenRouterPricingPage: React.FC = () => {
  // États
  const [modelPricings, setModelPricings] = useState<ModelPricing[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editedValues, setEditedValues] = useState<Partial<ModelPricing>>({});
  const [openAddDialog, setOpenAddDialog] = useState<boolean>(false);
  const [newModel, setNewModel] = useState<Partial<ModelPricing>>({
    model: '',
    input_price_per_million: 0,
    output_price_per_million: 0,
    context_window: 0,
    max_output: 0,
    latency_seconds: 0,
    throughput_tokens_per_second: 0
  });

  // Charger les tarifs au chargement de la page
  useEffect(() => {
    fetchModelPricings();
  }, []);

  // Fonction pour récupérer les tarifs
  const fetchModelPricings = async () => {
    try {
      setIsLoading(true);

      // Récupérer les headers communs
      const headers = await getCommonHeaders();

      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/pricing`, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setModelPricings(response.data.data);
      } else {
        notify('Erreur lors de la récupération des tarifs', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des tarifs:', error);
      notify('Erreur lors de la récupération des tarifs', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour mettre à jour un tarif
  const updateModelPricing = async (model: string) => {
    try {
      setIsLoading(true);

      // Récupérer le token CSRF
      const csrfToken = await fetchCsrfToken();

      // Récupérer les headers communs
      const headers = await getCommonHeaders();

      // Encoder le nom du modèle pour l'URL (important pour les noms avec des caractères spéciaux comme '/')
      const encodedModel = encodeURIComponent(model);

      const response = await axios.put(`${API_CONFIG.baseURL}/api/openrouter/pricing/${encodedModel}`, editedValues, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        withCredentials: true
      });

      if (response.data.success) {
        notify('Tarif mis à jour avec succès', 'success');
        fetchModelPricings();
        setEditingId(null);
        setEditedValues({});
      } else {
        notify('Erreur lors de la mise à jour du tarif', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du tarif:', error);

      // Afficher un message d'erreur plus détaillé
      if (error.response) {
        const errorMessage = error.response.data?.message || 'Erreur lors de la mise à jour du tarif';
        notify(errorMessage, 'error');
      } else {
        notify('Erreur lors de la mise à jour du tarif', 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour ajouter un nouveau tarif
  const addModelPricing = async () => {
    try {
      setIsLoading(true);

      // Récupérer le token CSRF
      const csrfToken = await fetchCsrfToken();

      // Récupérer les headers communs
      const headers = await getCommonHeaders();

      const response = await axios.post(`${API_CONFIG.baseURL}/api/openrouter/pricing`, newModel, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        withCredentials: true
      });

      if (response.data.success) {
        notify('Tarif ajouté avec succès', 'success');
        fetchModelPricings();
        setOpenAddDialog(false);
        setNewModel({
          model: '',
          input_price_per_million: 0,
          output_price_per_million: 0,
          context_window: 0,
          max_output: 0,
          latency_seconds: 0,
          throughput_tokens_per_second: 0
        });
      } else {
        notify('Erreur lors de l\'ajout du tarif', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de l\'ajout du tarif:', error);
      notify('Erreur lors de l\'ajout du tarif', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer le début de l'édition
  const handleEdit = (pricing: ModelPricing) => {
    setEditingId(pricing.id);
    setEditedValues({
      input_price_per_million: pricing.input_price_per_million,
      output_price_per_million: pricing.output_price_per_million,
      context_window: pricing.context_window,
      max_output: pricing.max_output,
      latency_seconds: pricing.latency_seconds,
      throughput_tokens_per_second: pricing.throughput_tokens_per_second
    });
  };

  // Gérer les changements dans les champs d'édition
  const handleEditChange = (field: keyof ModelPricing, value: string) => {
    setEditedValues({
      ...editedValues,
      [field]: field === 'model' ? value : parseFloat(value)
    });
  };

  // Gérer les changements dans les champs du nouveau modèle
  const handleNewModelChange = (field: keyof ModelPricing, value: string) => {
    setNewModel({
      ...newModel,
      [field]: field === 'model' ? value : parseFloat(value)
    });
  };

  // Calculer le coût pour 1000 tokens
  const calculateCostPer1000 = (inputPrice: number, outputPrice: number) => {
    // Supposons un ratio de 70% input, 30% output pour un cas typique
    return ((inputPrice * 0.7) + (outputPrice * 0.3)) / 1000;
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <PageTitle variant="h4">
            Tarification OpenRouter
          </PageTitle>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <StyledButton
              variant="outlined"
              startIcon={<Refresh />}
              onClick={fetchModelPricings}
              disabled={isLoading}
            >
              Actualiser
            </StyledButton>
            <StyledButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => setOpenAddDialog(true)}
              disabled={isLoading}
            >
              Ajouter un modèle
            </StyledButton>
          </Box>
        </Box>

        <Typography variant="body1" color="text.secondary" paragraph sx={{ mb: 4 }}>
          Gérez les tarifs des modèles OpenRouter utilisés pour estimer les coûts d'utilisation de l'API.
        </Typography>

        <StyledPaper>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress sx={{ color: COLORS.primary }} />
            </Box>
          ) : (
            <StyledTableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Modèle</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Prix Input ($/M)
                        <Tooltip title="Prix par million de tokens en entrée">
                          <Info fontSize="small" sx={{ ml: 0.5, color: COLORS.info, fontSize: '16px' }} />
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Prix Output ($/M)
                        <Tooltip title="Prix par million de tokens en sortie">
                          <Info fontSize="small" sx={{ ml: 0.5, color: COLORS.info, fontSize: '16px' }} />
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        Coût moyen ($/1K)
                        <Tooltip title="Coût moyen pour 1000 tokens (70% input, 30% output)">
                          <Info fontSize="small" sx={{ ml: 0.5, color: COLORS.info, fontSize: '16px' }} />
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell align="right">Contexte (tokens)</TableCell>
                    <TableCell align="right">Max Output</TableCell>
                    <TableCell align="right">Latence (s)</TableCell>
                    <TableCell align="right">Débit (t/s)</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {modelPricings.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} align="center">
                        <Typography color="text.secondary">Aucun tarif disponible</Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    modelPricings.map((pricing) => (
                      <TableRow key={pricing.id}>
                        <TableCell>{pricing.model}</TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.input_price_per_million}
                              onChange={(e) => handleEditChange('input_price_per_million', e.target.value)}
                              inputProps={{ step: '0.01', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            `$${pricing.input_price_per_million.toFixed(5)}`
                          )}
                        </TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.output_price_per_million}
                              onChange={(e) => handleEditChange('output_price_per_million', e.target.value)}
                              inputProps={{ step: '0.01', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            `$${pricing.output_price_per_million.toFixed(5)}`
                          )}
                        </TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold', color: COLORS.primary }}>
                          {editingId === pricing.id ? (
                            `$${calculateCostPer1000(
                              editedValues.input_price_per_million || pricing.input_price_per_million,
                              editedValues.output_price_per_million || pricing.output_price_per_million
                            ).toFixed(5)}`
                          ) : (
                            `$${calculateCostPer1000(pricing.input_price_per_million, pricing.output_price_per_million).toFixed(5)}`
                          )}
                        </TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.context_window}
                              onChange={(e) => handleEditChange('context_window', e.target.value)}
                              inputProps={{ step: '1000', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            pricing.context_window.toLocaleString()
                          )}
                        </TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.max_output}
                              onChange={(e) => handleEditChange('max_output', e.target.value)}
                              inputProps={{ step: '1000', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            pricing.max_output.toLocaleString()
                          )}
                        </TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.latency_seconds}
                              onChange={(e) => handleEditChange('latency_seconds', e.target.value)}
                              inputProps={{ step: '0.01', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            pricing.latency_seconds?.toFixed(2) || '-'
                          )}
                        </TableCell>
                        <TableCell align="right">
                          {editingId === pricing.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={editedValues.throughput_tokens_per_second}
                              onChange={(e) => handleEditChange('throughput_tokens_per_second', e.target.value)}
                              inputProps={{ step: '0.01', min: 0 }}
                              sx={{ width: '100px' }}
                            />
                          ) : (
                            pricing.throughput_tokens_per_second?.toFixed(2) || '-'
                          )}
                        </TableCell>
                        <TableCell align="center">
                          {editingId === pricing.id ? (
                            <IconButton
                              color="primary"
                              onClick={() => updateModelPricing(pricing.model)}
                              disabled={isLoading}
                            >
                              <Save />
                            </IconButton>
                          ) : (
                            <IconButton
                              color="primary"
                              onClick={() => handleEdit(pricing)}
                              disabled={isLoading || !!editingId}
                            >
                              <Edit />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </StyledTableContainer>
          )}
        </StyledPaper>
      </Box>

      {/* Dialog pour ajouter un nouveau modèle */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md">
        <DialogTitle>Ajouter un nouveau modèle</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Entrez les informations de tarification pour le nouveau modèle.
          </DialogContentText>
          <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' } }}>
            <Box>
              <TextField
                fullWidth
                label="Nom du modèle"
                value={newModel.model}
                onChange={(e) => handleNewModelChange('model', e.target.value)}
                required
                margin="dense"
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Prix Input ($/million)"
                type="number"
                value={newModel.input_price_per_million}
                onChange={(e) => handleNewModelChange('input_price_per_million', e.target.value)}
                required
                margin="dense"
                inputProps={{ step: '0.01', min: 0 }}
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Prix Output ($/million)"
                type="number"
                value={newModel.output_price_per_million}
                onChange={(e) => handleNewModelChange('output_price_per_million', e.target.value)}
                required
                margin="dense"
                inputProps={{ step: '0.01', min: 0 }}
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Taille du contexte (tokens)"
                type="number"
                value={newModel.context_window}
                onChange={(e) => handleNewModelChange('context_window', e.target.value)}
                required
                margin="dense"
                inputProps={{ step: '1000', min: 0 }}
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Taille max de sortie (tokens)"
                type="number"
                value={newModel.max_output}
                onChange={(e) => handleNewModelChange('max_output', e.target.value)}
                required
                margin="dense"
                inputProps={{ step: '1000', min: 0 }}
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Latence (secondes)"
                type="number"
                value={newModel.latency_seconds}
                onChange={(e) => handleNewModelChange('latency_seconds', e.target.value)}
                margin="dense"
                inputProps={{ step: '0.01', min: 0 }}
              />
            </Box>
            <Box>
              <TextField
                fullWidth
                label="Débit (tokens/seconde)"
                type="number"
                value={newModel.throughput_tokens_per_second}
                onChange={(e) => handleNewModelChange('throughput_tokens_per_second', e.target.value)}
                margin="dense"
                inputProps={{ step: '0.01', min: 0 }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)} color="inherit">
            Annuler
          </Button>
          <StyledButton
            onClick={addModelPricing}
            variant="contained"
            disabled={isLoading || !newModel.model || !newModel.input_price_per_million || !newModel.output_price_per_million}
          >
            Ajouter
          </StyledButton>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default OpenRouterPricingPage;
