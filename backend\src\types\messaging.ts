import { User } from './index';

export interface Conversation {
  id: string;
  user1_id: string;
  user2_id: string;
  last_message_id?: string;
  updated_at: Date;
  created_at: Date;
  user1_has_blocked: boolean;
  user2_has_blocked: boolean;
  user1_has_deleted: boolean;
  user2_has_deleted: boolean;
  total_messages: number;
  unread_count_user1: number;
  unread_count_user2: number;
  user1?: User;
  user2?: User;
  last_message?: Message;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  is_read: boolean;
  created_at: Date;
  updated_at: Date;
  sender?: User;
  receiver?: User;
  attachments?: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  message_id: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  storage_path?: string;
  public_url: string;
  expires_at: Date;
  created_at: Date;
  is_expired?: boolean;
}

// DTOs pour la création et la mise à jour
export interface CreateConversationDTO {
  recipient_id: string;
  initial_message: string;
  attachments?: File[];
}

export interface SendMessageDTO {
  conversation_id: string;
  content: string;
  attachments?: File[];
}

export interface UpdateConversationDTO {
  is_blocked?: boolean;
  is_deleted?: boolean;
}

// Filtres pour la recherche
export interface ConversationFilters {
  search?: string;
  unread_only?: boolean;
  show_deleted?: boolean;
  page?: number;
  limit?: number;
  order?: string;
  direction?: 'asc' | 'desc';
} 