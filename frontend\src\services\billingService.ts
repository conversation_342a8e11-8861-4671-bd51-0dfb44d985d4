import { notify } from '@/components/Notification';
import { 
  getDocuments, 
  getDocumentById, 
  getClients, 
  saveDocument, 
  deleteDocument, 
  downloadDocument, 
  sendDocumentByEmail, 
  convertToInvoice, 
  createCreditNote 
} from './invoice';

export interface InvoiceItem {
  id: string;
  description: string;
  quantite: number;
  unite: string;
  prix_unitaire: number;
  taux_tva: number;
  montant_ht: number;
  montant_tva: number;
  montant_ttc: number;
}

export interface Document {
  id?: string;
  number?: string;
  draft_number?: string;
  type: 'devis' | 'facture' | 'avoir';
  client_name: string;
  client_id?: string;
  date_creation: string;
  date_validite?: string;
  total_ht: number;
  total_tva: number;
  total_ttc: number;
  statut: string;
  description: string;
  invoice_items: InvoiceItem[];
  client_address?: string;
  client_email?: string;
  client_phone?: string;
  client_siret?: string;
  client_tva?: string;
  forme_juridique?: string;
  code_ape?: string;
  conditions_paiement?: string;
  mode_paiement?: string;
  mentions_legales?: string;
  mentions_tva?: string;
  penalite_retard?: string;
  indemnite_recouvrement?: string;
  notes?: string;
}

export interface Client {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse?: string;
  siret?: string;
  tva?: string;
}

// Récupérer tous les documents
export const fetchAllDocuments = async (): Promise<Document[]> => {
  try {
    return await getDocuments();
  } catch (error) {
    notify('Erreur lors de la récupération des documents', 'error');
    console.error(error);
    return [];
  }
};

// Récupérer un document par son ID
export const fetchDocumentById = async (id: string): Promise<Document | null> => {
  try {
    return await getDocumentById(id);
  } catch (error) {
    notify('Erreur lors de la récupération du document', 'error');
    console.error(error);
    return null;
  }
};

// Récupérer tous les clients
export const fetchAllClients = async (): Promise<Client[]> => {
  try {
    return await getClients();
  } catch (error) {
    notify('Erreur lors de la récupération des clients', 'error');
    console.error(error);
    return [];
  }
};

// Sauvegarder un document (création ou mise à jour)
export const saveDocumentWithToast = async (document: Document): Promise<Document | null> => {
  try {
    const savedDocument = await saveDocument(document);
    notify(document.id ? 'Document mis à jour avec succès' : 'Document créé avec succès', 'success');
    return savedDocument;
  } catch (error: any) {
    notify(error.response?.data?.message || 'Erreur lors de la sauvegarde du document', 'error');
    console.error(error);
    return null;
  }
};

// Supprimer un document
export const deleteDocumentWithToast = async (id: string): Promise<boolean> => {
  try {
    await deleteDocument(id);
    notify('Document supprimé avec succès', 'success');
    return true;
  } catch (error) {
    notify('Erreur lors de la suppression du document', 'error');
    console.error(error);
    return false;
  }
};

// Télécharger un document au format PDF
export const downloadDocumentWithToast = async (id: string, documentName: string): Promise<void> => {
  try {
    const blob = await downloadDocument(id);
    
    // Créer un objet URL pour le blob
    const url = window.URL.createObjectURL(blob);
    
    // Créer un lien temporaire pour télécharger le fichier
    const a = document.createElement('a');
    a.href = url;
    a.download = `${documentName}.pdf`;
    document.body.appendChild(a);
    a.click();
    
    // Nettoyer
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }, 0);
    
    notify('Document téléchargé avec succès', 'success');
  } catch (error) {
    notify('Erreur lors du téléchargement du document', 'error');
    console.error(error);
  }
};

// Envoyer un document par email
export const sendDocumentByEmailWithToast = async (id: string, email: string, message: string): Promise<boolean> => {
  try {
    await sendDocumentByEmail(id, { email, message });
    notify('Document envoyé par email avec succès', 'success');
    return true;
  } catch (error) {
    notify('Erreur lors de l\'envoi du document par email', 'error');
    console.error(error);
    return false;
  }
};

// Convertir un devis en facture
export const convertToInvoiceWithToast = async (id: string): Promise<Document | null> => {
  try {
    const newInvoice = await convertToInvoice(id);
    notify('Devis converti en facture avec succès', 'success');
    return newInvoice;
  } catch (error ) {
    notify('Erreur lors de la conversion du devis en facture', 'error');
    console.error(error);
    return null;
  }
};

// Créer un avoir à partir d'une facture
export const createCreditNoteWithToast = async (id: string): Promise<Document | null> => {
  try {
    const creditNote = await createCreditNote(id);
    notify('Avoir créé avec succès', 'success');
    return creditNote;
  } catch (error) {
    notify('Erreur lors de la création de l\'avoir', 'error');
    console.error(error);
    return null;
  }
};

// Calculer les montants d'un article
export const calculateItemTotals = (item: InvoiceItem): InvoiceItem => {
  const montantHT = item.quantite * item.prix_unitaire;
  const montantTVA = montantHT * (item.taux_tva / 100);
  return {
    ...item,
    montant_ht: parseFloat(montantHT.toFixed(2)),
    montant_tva: parseFloat(montantTVA.toFixed(2)),
    montant_ttc: parseFloat((montantHT + montantTVA).toFixed(2))
  };
};

// Calculer les totaux d'un document
export const calculateDocumentTotals = (items: InvoiceItem[]): { totalHT: number, totalTVA: number, totalTTC: number } => {
  const totalHT = items.reduce((sum, item) => sum + item.montant_ht, 0);
  const totalTVA = items.reduce((sum, item) => sum + item.montant_tva, 0);
  const totalTTC = items.reduce((sum, item) => sum + item.montant_ttc, 0);
  
  return {
    totalHT: parseFloat(totalHT.toFixed(2)),
    totalTVA: parseFloat(totalTVA.toFixed(2)),
    totalTTC: parseFloat(totalTTC.toFixed(2))
  };
};

// Générer un article vide
export const generateEmptyItem = (): InvoiceItem => ({
  id: Math.random().toString(36).substring(2, 9),
  description: '',
  quantite: 1,
  unite: 'unité',
  prix_unitaire: 0,
  taux_tva: 20,
  montant_ht: 0,
  montant_tva: 0,
  montant_ttc: 0
});

// Générer un document vide
export const generateEmptyDocument = (type: 'devis' | 'facture' | 'avoir'): Document => ({
  type,
  client_name: '',
  date_creation: new Date().toISOString().split('T')[0],
  date_validite: type === 'devis' ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
  total_ht: 0,
  total_tva: 0,
  total_ttc: 0,
  statut: 'brouillon',
  description: '',
  invoice_items: [generateEmptyItem()]
});

// Obtenir le texte du statut
export const getStatusText = (statut: string): string => {
  switch (statut) {
    case 'brouillon': return 'Brouillon';
    case 'emis': return 'Émis';
    case 'envoye': return 'Envoyé';
    case 'accepte': return 'Accepté';
    case 'refuse': return 'Refusé';
    case 'paye': return 'Payé';
    case 'a_payer': return 'À payer';
    case 'annule': return 'Annulé';
    default: return statut;
  }
};

// Obtenir la couleur du statut
export const getStatusColor = (statut: string): string => {
  switch (statut) {
    case 'brouillon': return 'bg-gray-100 text-gray-800';
    case 'emis':
    case 'envoye': return 'bg-blue-100 text-blue-800';
    case 'accepte':
    case 'paye': return 'bg-green-100 text-green-800';
    case 'refuse': return 'bg-red-100 text-red-800';
    case 'a_payer': return 'bg-yellow-100 text-yellow-800';
    case 'annule': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

// Dupliquer un document
export const duplicateDocument = (document: Document): Document => ({
  ...document,
  id: undefined,
  number: undefined,
  draft_number: undefined, // Sera généré côté backend
  statut: 'brouillon',
  date_creation: new Date().toISOString().split('T')[0],
  invoice_items: document.invoice_items ? document.invoice_items.map(item => ({
    ...item,
    id: Math.random().toString(36).substring(2, 9)
  })) : []
});

// Obtenir le numéro d'affichage selon le statut
export const getDisplayNumber = (document: Document): string => {
  if (document.statut === 'brouillon' && document.draft_number) {
    // Extraire seulement la partie BROUILLON-XXXX pour simplifier l'affichage
    const match = document.draft_number.match(/BROUILLON-.*?-.*?-(\d+)$/);
    if (match) {
      return `BROUILLON-${match[1]}`;
    }
    return document.draft_number;
  }
  return document.number || 'N/A';
};

// Vérifier si un document est un brouillon
export const isDraft = (document: Document): boolean => {
  return document.statut === 'brouillon';
};

export default {
  fetchAllDocuments,
  fetchDocumentById,
  fetchAllClients,
  saveDocumentWithToast,
  deleteDocumentWithToast,
  downloadDocumentWithToast,
  sendDocumentByEmailWithToast,
  convertToInvoiceWithToast,
  createCreditNoteWithToast,
  calculateItemTotals,
  calculateDocumentTotals,
  generateEmptyItem,
  generateEmptyDocument,
  getStatusText,
  getStatusColor,
  duplicateDocument,
  getDisplayNumber,
  isDraft
};