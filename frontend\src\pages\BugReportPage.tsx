import React from 'react';
import { Box, Typography, Button, Paper, useTheme, useMediaQuery, Container, Breadcrumbs } from '@mui/material';
import BugReportForm from '../components/BugReport/BugReportForm';
import { Link } from 'react-router-dom';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import BugReportIcon from '@mui/icons-material/BugReport';
import HomeIcon from '@mui/icons-material/Home';
import ListAltIcon from '@mui/icons-material/ListAlt';

const BugReportPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box 
      sx={{ 
        minHeight: '100vh',
        pt: { xs: 2, sm: 3 },
        pb: { xs: 4, sm: 6 }
      }}
    >
      <Container maxWidth="lg">
        {/* Fil d'Ariane */}
        <Breadcrumbs 
          aria-label="breadcrumb"
          sx={{ 
            mb: 3, 
            background: 'rgba(255, 255, 255, 0.7)',
            py: 1.5, 
            px: 2, 
            borderRadius: 2,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Button
            component={Link}
            to="/dashboard"
            size="small"
            startIcon={<HomeIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            Tableau de bord
          </Button>
          <Button
            component={Link}
            to="/dashboard/bug-reports"
            size="small"
            startIcon={<ListAltIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            Rapports
          </Button>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
            <BugReportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
            Créer un nouveau rapport
          </Typography>
        </Breadcrumbs>

        {/* Titre et boutons d'action */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', md: 'center' }, 
            gap: { xs: 2, md: 0 },
            mb: 4
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box 
              sx={{ 
                backgroundColor: 'rgba(255, 107, 44, 0.1)', 
                borderRadius: '50%',
                p: 1.5,
                display: 'flex',
                mr: 2
              }}
            >
              <BugReportIcon 
                sx={{ 
                  color: '#FF6B2C', 
                  fontSize: { xs: '2rem', sm: '2.5rem' },
                  filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
                }} 
              />
            </Box>
            <Box>
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 700, 
                  color: '#333',
                  fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
                  lineHeight: 1.2
                }}
              >
                {isMobile ? 'Créer un rapport' : 'Créer un nouveau rapport'}
              </Typography>
              <Typography
                variant="subtitle1"
                color="text.secondary"
                sx={{ mt: 0.5, fontWeight: 400 }}
              >
                Signalez un problème ou suggérez une amélioration
              </Typography>
            </Box>
          </Box>

          <Button
            component={Link}
            to="/dashboard/bug-reports"
            variant="outlined"
            fullWidth={isMobile}
            startIcon={<FormatListBulletedIcon />}
            sx={{ 
              color: '#FF6B2C',
              borderColor: '#FF6B2C',
              fontWeight: 500,
              px: 2,
              py: 1.2,
              borderRadius: '8px',
              '&:hover': {
                borderColor: '#FF6B2C',
                color: '#FF6B2C',
                bgcolor: 'rgba(255, 107, 44, 0.05)',
              },
            }}
          >
            {isMobile ? 'Retour à la liste' : 'Liste des rapports'}
          </Button>
        </Box>

        {/* Formulaire */}
        <Paper
          elevation={isMobile ? 2 : 3}
          sx={{
            borderRadius: { xs: '12px', sm: '16px' },
            overflow: 'hidden',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
            position: 'relative',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '6px',
              background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
            },
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            '&:hover': {
              transform: 'translateY(-3px)',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          <Box sx={{ 
            p: { xs: 2, sm: 3, md: 4 },
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'visible',
          }}>
            <BugReportForm />
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default BugReportPage; 