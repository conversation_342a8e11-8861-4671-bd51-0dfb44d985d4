/**
 * Utilitaire pour générer des chemins SVG (Path) artistiques et aléatoires
 */

/**
 * Génère un chemin SVG courbe aléatoire
 * @param complexity Niveau de complexité (nombre de points de contrôle)
 * @param width Largeur maximale
 * @param height Hauteur maximale
 * @returns Chaîne de données SVG pour un élément Path
 */
export const generateRandomCurvePath = (
  complexity: number = 3,
  width: number = 100,
  height: number = 100
): string => {
  // Point de départ
  const startX = Math.random() * (width * 0.2);
  const startY = Math.random() * height;
  
  let path = `M${startX.toFixed(1)} ${startY.toFixed(1)}`;
  
  // Générer des courbes de Bézier aléatoires
  for (let i = 0; i < complexity; i++) {
    const controlX1 = Math.random() * width;
    const controlY1 = Math.random() * height;
    const controlX2 = Math.random() * width;
    const controlY2 = Math.random() * height;
    const endX = Math.random() * width;
    const endY = Math.random() * height;
    
    path += ` C${controlX1.toFixed(1)} ${controlY1.toFixed(1)}, ${controlX2.toFixed(1)} ${controlY2.toFixed(1)}, ${endX.toFixed(1)} ${endY.toFixed(1)}`;
  }
  
  return path;
};

/**
 * Génère un chemin SVG en zigzag aléatoire
 * @param points Nombre de points
 * @param width Largeur maximale
 * @param height Hauteur maximale
 * @returns Chaîne de données SVG pour un élément Path
 */
export const generateRandomZigzagPath = (
  points: number = 5,
  width: number = 100,
  height: number = 100
): string => {
  // Point de départ
  const startX = Math.random() * (width * 0.2);
  const startY = Math.random() * height;
  
  let path = `M${startX.toFixed(1)} ${startY.toFixed(1)}`;
  
  // Générer des lignes en zigzag
  for (let i = 0; i < points; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height;
    
    path += ` L${x.toFixed(1)} ${y.toFixed(1)}`;
  }
  
  return path;
};

/**
 * Génère un chemin SVG en forme d'onde
 * @param width Largeur maximale
 * @param height Hauteur maximale
 * @param waves Nombre d'ondulations
 * @returns Chaîne de données SVG pour un élément Path
 */
export const generateWavePath = (
  width: number = 100,
  height: number = 100,
  waves: number = 3
): string => {
  const startX = 0;
  const startY = height / 2;
  
  let path = `M${startX} ${startY}`;
  
  const waveWidth = width / waves;
  const amplitude = height / 3;
  
  for (let i = 0; i < waves; i++) {
    const x1 = startX + (i * waveWidth) + (waveWidth / 4);
    const y1 = startY - amplitude;
    const x2 = startX + (i * waveWidth) + (waveWidth * 3 / 4);
    const y2 = startY + amplitude;
    const x3 = startX + ((i + 1) * waveWidth);
    const y3 = startY;
    
    path += ` C${x1} ${y1}, ${x2} ${y2}, ${x3} ${y3}`;
  }
  
  return path;
};

/**
 * Génère une forme abstraite fermée
 * @param width Largeur maximale
 * @param height Hauteur maximale
 * @param points Nombre de points
 * @returns Chaîne de données SVG pour un élément Path
 */
export const generateAbstractShape = (
  width: number = 100,
  height: number = 100,
  points: number = 5
): string => {
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = Math.min(width, height) / 2.5;
  
  // Point de départ
  const startAngle = Math.random() * Math.PI * 2;
  const startX = centerX + Math.cos(startAngle) * radius;
  const startY = centerY + Math.sin(startAngle) * radius;
  
  let path = `M${startX.toFixed(1)} ${startY.toFixed(1)}`;
  
  // Générer des courbes quadratiques pour former une forme fermée
  for (let i = 1; i <= points; i++) {
    const angle = startAngle + (i * Math.PI * 2 / points);
    const endX = centerX + Math.cos(angle) * radius * (0.7 + Math.random() * 0.6);
    const endY = centerY + Math.sin(angle) * radius * (0.7 + Math.random() * 0.6);
    
    const controlAngle = startAngle + ((i - 0.5) * Math.PI * 2 / points);
    const controlDistance = radius * (1 + Math.random() * 1.5);
    const controlX = centerX + Math.cos(controlAngle) * controlDistance;
    const controlY = centerY + Math.sin(controlAngle) * controlDistance;
    
    path += ` Q${controlX.toFixed(1)} ${controlY.toFixed(1)}, ${endX.toFixed(1)} ${endY.toFixed(1)}`;
  }
  
  // Fermer le chemin
  path += ' Z';
  
  return path;
};

/**
 * Génère un chemin SVG aléatoire selon un type spécifié
 * @param type Type de chemin à générer
 * @param width Largeur maximale
 * @param height Hauteur maximale
 * @returns Chaîne de données SVG pour un élément Path
 */
export const generateRandomPath = (
  type: 'curve' | 'zigzag' | 'wave' | 'abstract' = 'curve',
  width: number = 100,
  height: number = 100
): string => {
  switch (type) {
    case 'curve':
      return generateRandomCurvePath(3, width, height);
    case 'zigzag':
      return generateRandomZigzagPath(5, width, height);
    case 'wave':
      return generateWavePath(width, height, 3);
    case 'abstract':
      return generateAbstractShape(width, height, 5);
    default:
      return generateRandomCurvePath(3, width, height);
  }
};
