import React, { useState, useEffect } from 'react';
import { Button, Avatar, Box, Typography, Tooltip } from '@mui/material';
import { Star, User, SquarePen, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../pages/dashboard/services/types';
import DOMPurify from 'dompurify';
import { useReviews } from '../hooks/useReviews';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import axios from 'axios';
import { API_CONFIG } from '@/config/api';
import { TextField, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { notify } from './Notification';
import { fetchCsrfToken } from '@/services/csrf';
import ModalPortal from './ModalPortal';

interface UserData {
  id: string;
  profil?: {
    data?: {
      prenom?: string;
      nom?: string;
    };
  };
}

interface ReviewModalListeDesAvisProps {
  userId: string;
  userData?: UserData;
  currentUser?: any;
  onEditReview?: (reviewId: string) => void;
  onDeleteReview?: (reviewId: string) => void;
  onOpenReviewerProfile?: (reviewerId: string) => void;
  onOpenMissionSelector?: () => void;
  onStatsUpdate?: (stats: { rating: number; total_reviews: number; completion_rate: number }) => void;
  refreshKey?: number;
  limit?: number;
  showHeader?: boolean;
  viewType?: 'profil' | 'default';
  whiteBackground?: boolean;
  maxCharacters?: number;
  emptyStateBackgroundColor?: string;
}

const ReviewModalListeDesAvis: React.FC<ReviewModalListeDesAvisProps> = ({ 
  userId, 
  userData,
  currentUser,
  onEditReview, 
  onDeleteReview,
  onOpenReviewerProfile,
  onOpenMissionSelector,
  onStatsUpdate,
  refreshKey = 0,
  limit,
  showHeader = true,
  viewType = 'default',
  whiteBackground = false,
  maxCharacters = 100,
  emptyStateBackgroundColor = '#FFF8F3'
}) => {
  const { 
    reviews, 
    loading, 
    error, 
    hasMore, 
    loadMore, 
    fetchReviews,
    stats,
    qualitesStats,
    defautsStats
  } = useReviews({ userId });

  const [expandedComments, setExpandedComments] = useState<string[]>([]);
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [reportingReview, setReportingReview] = useState<any>(null);
  const [reportReason, setReportReason] = useState('');
  const [reportLoading, setReportLoading] = useState(false);

  // États pour la modal de visualisation des photos
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [currentPhotos, setCurrentPhotos] = useState<string[]>([]);

  // Rafraîchir les avis quand le composant est monté ou quand refreshKey change
  useEffect(() => {
    if (userId) {
      fetchReviews(1);
    }
  }, [userId, refreshKey, fetchReviews]);

  // Mettre à jour les stats dans le parent uniquement quand elles changent
  useEffect(() => {
    if (stats && onStatsUpdate && !loading) {
      onStatsUpdate(stats);
    }
  }, [stats, onStatsUpdate, loading]);

  // Modifier handleEditReview pour ne pas recharger immédiatement
  const handleEditReview = async (reviewId: string) => {
    if (onEditReview) {
      await onEditReview(reviewId);
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (onDeleteReview) {
      await onDeleteReview(reviewId);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatName = (review: any) => {
    try {
      // Cas 1: Utilisation de client.firstName et client.lastName
      if (review.client?.firstName && review.client?.lastName) {
        return `${review.client.firstName} ${review.client.lastName.charAt(0)}.`;
      }
      
      // Cas 2: Utilisation de client.prenom et client.nom
      if (review.client?.prenom && review.client?.nom) {
        return `${review.client.prenom} ${review.client.nom.charAt(0)}.`;
      }
      
      // Cas 3: Utilisation directe de author_firstname et author_lastname
      if (review.author_firstname && review.author_lastname) {
        return `${review.author_firstname} ${review.author_lastname.charAt(0)}.`;
      }
      
      // Cas 4: Utilisation de author_name
      if (review.author_name) {
        return review.author_name;
      }
      
      // Cas par défaut
      return 'Anonyme';
    } catch (error) {
      return 'Anonyme';
    }
  };

  const toggleComment = (reviewId: string) => {
    setExpandedComments(prev => 
      prev.includes(reviewId) 
        ? prev.filter(id => id !== reviewId) 
        : [...prev, reviewId]
    );
  };

  const truncateComment = (comment: string, reviewId: string) => {
    if (!comment) return '';
    if (expandedComments.includes(reviewId)) {
      return comment;
    }
    if (comment.length > maxCharacters) {
      return comment.substring(0, maxCharacters) + ' ...';
    }
    return comment;
  };

  const stripHtml = (html: string) => {
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [] });
  };

  const handleOpenReportModal = (review: any) => {
    setReportingReview(review);
    setReportReason('');
    setReportModalOpen(true);
  };

  const handleSendReport = async () => {
    if (!reportingReview || !reportReason.trim()) return;
    setReportLoading(true);
    try {
      const headers: Record<string, string> = {};
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.post(
        `${API_CONFIG.baseURL}/api/reported-content`,
        {
          content_type: 'review',
          content_id: reportingReview.id,
          reason: reportReason
        },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );
      notify('Signalement envoyé, merci pour votre vigilance.', 'success');
      setReportModalOpen(false);
      setReportingReview(null);
      setReportReason('');
    } catch (error: any) {
      notify(error?.response?.data?.message || 'Erreur lors du signalement', 'error');
    } finally {
      setReportLoading(false);
    }
  };

  // Fonctions pour la modal de visualisation des photos
  const openPhotoModal = (photos: string[], startIndex: number = 0) => {
    setCurrentPhotos(photos);
    setCurrentPhotoIndex(startIndex);
    setPhotoModalOpen(true);
  };

  const closePhotoModal = () => {
    setPhotoModalOpen(false);
    setCurrentPhotos([]);
    setCurrentPhotoIndex(0);
  };

  const goToPreviousPhoto = () => {
    setCurrentPhotoIndex((prev) =>
      prev === 0 ? currentPhotos.length - 1 : prev - 1
    );
  };

  const goToNextPhoto = () => {
    setCurrentPhotoIndex((prev) =>
      prev === currentPhotos.length - 1 ? 0 : prev + 1
    );
  };

  // Gestion des touches clavier pour la navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!photoModalOpen) return;

    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        goToPreviousPhoto();
        break;
      case 'ArrowRight':
        e.preventDefault();
        goToNextPhoto();
        break;
      case 'Escape':
        e.preventDefault();
        closePhotoModal();
        break;
    }
  };

  // Ajouter l'écouteur d'événements clavier
  useEffect(() => {
    if (photoModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [photoModalOpen, currentPhotos.length]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6B2C]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <Box className={viewType === 'profil' ? 'max-w-[800px] mx-auto' : ''}>
      <Box className={viewType === 'profil' ? 'max-w-full lg:max-w-3xl xl:max-w-4xl mx-auto' : ''}>
        {reviews.length > 0 ? (
          <>
            {/* Section des qualités les plus fréquentes */}
            {showHeader && qualitesStats.filter(stat => stat.count >= 4).length > 0 && (
              <Box sx={{
                mb: 3,
                p: 2,
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid rgba(34, 197, 94, 0.1)',
                boxShadow: '0 2px 4px rgba(34, 197, 94, 0.05)'
              }}>
                <Typography variant="subtitle1" sx={{
                  color: '#2D3748',
                  fontWeight: 'bold',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Star size={16} color="#16A34A" />
                  Points forts les plus appréciés
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  {qualitesStats
                    .filter(stat => stat.count >= 4)
                    .slice(0, 8)
                    .map(({ qualite, count }, index) => (
                    <Box
                      key={index}
                      sx={{
                        backgroundColor: 'rgba(34, 197, 94, 0.08)',
                        color: '#16A34A',
                        fontWeight: 'medium',
                        fontSize: '0.75rem',
                        height: '28px',
                        padding: '0 12px',
                        borderRadius: '14px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        border: '1px solid rgba(34, 197, 94, 0.2)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: 'rgba(34, 197, 94, 0.12)',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 2px 4px rgba(34, 197, 94, 0.1)'
                        }
                      }}
                    >
                      <span>{qualite}</span>
                      <Typography
                        component="span"
                        sx={{
                          backgroundColor: 'white',
                          color: '#16A34A',
                          fontSize: '0.7rem',
                          fontWeight: 'bold',
                          padding: '2px 6px',
                          borderRadius: '10px',
                          border: '1px solid rgba(34, 197, 94, 0.2)'
                        }}
                      >
                        {count}
                      </Typography>
                    </Box>
                  ))}
                  {qualitesStats.filter(stat => stat.count >= 4).length > 8 && (
                    <Box
                      sx={{
                        backgroundColor: 'rgba(34, 197, 94, 0.04)',
                        color: '#16A34A',
                        fontWeight: 'medium',
                        fontSize: '0.75rem',
                        height: '28px',
                        padding: '0 12px',
                        borderRadius: '14px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        border: '1px solid rgba(34, 197, 94, 0.1)',
                      }}
                    >
                      +{qualitesStats.filter(stat => stat.count >= 4).length - 8} autres
                    </Box>
                  )}
                </Box>
              </Box>
            )}

            {/* Section des défauts les plus fréquents */}
            {showHeader && defautsStats.filter(stat => stat.count >= 4).length > 0 && (
              <Box sx={{
                mb: 3,
                p: 2,
                backgroundColor: 'white',
                borderRadius: '12px',
                border: '1px solid rgba(239, 68, 68, 0.1)',
                boxShadow: '0 2px 4px rgba(239, 68, 68, 0.05)'
              }}>
                <Typography variant="subtitle1" sx={{
                  color: '#2D3748',
                  fontWeight: 'bold',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <User size={16} color="#DC2626" />
                  Points à améliorer les plus mentionnés
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 1
                }}>
                  {defautsStats
                    .filter(stat => stat.count >= 4)
                    .slice(0, 8)
                    .map(({ defaut, count }, index) => (
                    <Box
                      key={index}
                      sx={{
                        backgroundColor: 'rgba(239, 68, 68, 0.08)',
                        color: '#DC2626',
                        fontWeight: 'medium',
                        fontSize: '0.75rem',
                        height: '28px',
                        padding: '0 12px',
                        borderRadius: '14px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        border: '1px solid rgba(239, 68, 68, 0.2)',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: 'rgba(239, 68, 68, 0.12)',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 2px 4px rgba(239, 68, 68, 0.1)'
                        }
                      }}
                    >
                      <span>{defaut}</span>
                      <Typography
                        component="span"
                        sx={{
                          backgroundColor: 'white',
                          color: '#DC2626',
                          fontSize: '0.7rem',
                          fontWeight: 'bold',
                          padding: '2px 6px',
                          borderRadius: '10px',
                          border: '1px solid rgba(239, 68, 68, 0.2)'
                        }}
                      >
                        {count}
                      </Typography>
                    </Box>
                  ))}
                  {defautsStats.filter(stat => stat.count >= 4).length > 8 && (
                    <Box
                      sx={{
                        backgroundColor: 'rgba(239, 68, 68, 0.04)',
                        color: '#DC2626',
                        fontWeight: 'medium',
                        fontSize: '0.75rem',
                        height: '28px',
                        padding: '0 12px',
                        borderRadius: '14px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        border: '1px solid rgba(239, 68, 68, 0.1)',
                      }}
                    >
                      +{defautsStats.filter(stat => stat.count >= 4).length - 8} autres
                    </Box>
                  )}
                </Box>
              </Box>
            )}

            {/* Liste des avis */}
            <div className="space-y-4">
              {(limit ? reviews.slice(0, limit) : reviews).map((review, index) => (
                <React.Fragment key={review.id}>
                  <Box sx={{ 
                    backgroundColor: whiteBackground ? '#ffffff' : '#FFF8F3',
                    borderRadius: '12px',
                    border: '1px solid #FFE4BA',
                    transition: 'all 0.3s ease',
                    mb: index < reviews.length - 1 ? 2 : 0,
                    position: 'relative',
                    p: 2,
                    ...(viewType === 'profil' && {
                      maxWidth: '800px',
                      marginBottom: index < reviews.length - 1 ? '1rem' : 0
                    }),
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                      borderColor: '#FF6B2C',
                      zIndex: 1
                    }
                  }}>
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'flex-start', 
                      mb: 2,
                      flexDirection: { xs: 'column', lg: 'row' },
                      gap: { xs: 2, lg: 0 }
                    }}>
                      <Box 
                        sx={{ 
                          display: 'flex', 
                          alignItems: { xs: 'flex-start', sm: 'center' }, 
                          gap: 2,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          flexDirection: { xs: 'column', sm: 'row' },
                          width: { xs: '100%', sm: 'auto' },
                          '&:hover': {
                            opacity: 0.8,
                            transform: 'translateX(4px)'
                          }
                        }}
                        onClick={() => review.author_id && onOpenReviewerProfile?.(review.author_id)}
                      >
                        <Tooltip title="Voir profil" arrow>
                          <Avatar
                            alt={formatName(review)}
                            sx={{ 
                              width: { xs: 40, sm: 48 }, 
                              height: { xs: 40, sm: 48 },
                              border: '2px solid #FFE4BA',
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                              transition: 'all 0.3s ease',
                              '&:hover': {
                                transform: 'scale(1.05)',
                                borderColor: '#FF6B2C'
                              }
                            }}
                          >
                            <User className="h-6 w-6 text-gray-400" />
                          </Avatar>
                        </Tooltip>
                        <Box sx={{ width: '100%' }}>
                          <Typography 
                            variant="subtitle1" 
                            sx={{ 
                              fontWeight: 'bold', 
                              color: '#2D3748',
                              fontSize: { xs: '0.9rem', sm: '1rem' },
                              cursor: 'pointer',
                              '&:hover': {
                                color: '#FF6B2C'
                              }
                            }}
                          >
                            <Tooltip title="Voir profil" arrow placement="bottom-start">
                              <span>{formatName(review)}</span>
                            </Tooltip>
                          </Typography>
                          <Box sx={{ 
                            display: 'flex', 
                            flexDirection: 'column',
                            gap: 0.5,
                            width: '100%'
                          }}>
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5,
                              flexWrap: 'wrap'
                            }}>
                              <Typography 
                                variant="caption" 
                                sx={{
                                  color: '#718096',
                                  fontSize: '0.75rem'
                                }}
                              >
                                {formatDate(review.created_at)}
                              </Typography>
                              {review.is_modified && (
                                <>
                                  <Typography 
                                    variant="caption" 
                                    sx={{
                                      color: '#718096',
                                      fontSize: '0.75rem'
                                    }}
                                  >
                                    •
                                  </Typography>
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      color: '#FF6B2C',
                                      fontSize: '0.75rem',
                                      fontStyle: 'italic'
                                    }}
                                  >
                                    Modifié le {new Date(review.modified_at || '').toLocaleDateString('fr-FR', {
                                      day: 'numeric',
                                      month: 'long',
                                      year: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </Typography>
                                </>
                              )}
                            </Box>
                            <Box sx={{ 
                              display: 'flex', 
                              alignItems: 'center', 
                              gap: 0.5,
                              flexWrap: 'wrap'
                            }}>
                              <Typography variant="caption" sx={{ 
                                color: '#718096',
                                fontSize: '0.75rem'
                              }}>
                                {SERVICE_CATEGORIES.find(cat => cat.id === review.mission_categorie)?.nom || review.mission_categorie}
                              </Typography>
                              {review.mission_sous_categorie && (
                                <>
                                  <Typography 
                                    variant="caption" 
                                    sx={{
                                      color: '#718096',
                                      fontSize: '0.75rem'
                                    }}
                                  >
                                    •
                                  </Typography>
                                  <Typography variant="caption" sx={{ 
                                    color: '#718096',
                                    fontSize: '0.75rem'
                                  }}>
                                    {SERVICE_SUBCATEGORIES.find(subcat => subcat.id === review.mission_sous_categorie)?.nom || review.mission_sous_categorie}
                                  </Typography>
                                </>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                      <Box sx={{ 
                        display: 'flex', 
                        flexDirection: 'column', 
                        alignItems: { xs: 'flex-start', sm: 'flex-end' }, 
                        gap: 1,
                        width: { xs: '100%', sm: 'auto' }
                      }}>
                        {currentUser?.id && review.author_id && currentUser.id === review.author_id && (
                          <Box sx={{ 
                            display: 'flex', 
                            gap: 1,
                            width: { xs: '100%', sm: 'auto' }
                          }}>
                            <Button
                              onClick={() => handleEditReview(review.id)}
                              variant="contained"
                              size="small"
                              startIcon={<SquarePen size={16} />}
                              sx={{
                                backgroundColor: '#FF6B2C',
                                color: 'white',
                                padding: '6px 12px',
                                borderRadius: '8px',
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                flex: { xs: 1, sm: 'none' },
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  backgroundColor: '#FF965E',
                                  transform: 'translateY(-1px)'
                                }
                              }}
                            >
                              Modifier
                            </Button>
                            <Button
                              onClick={() => handleDeleteReview(review.id)}
                              variant="contained"
                              size="small"
                              startIcon={<X size={16} />}
                              sx={{
                                backgroundColor: '#FF3B30',
                                color: 'white',
                                padding: '6px 12px',
                                borderRadius: '8px',
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                flex: { xs: 1, sm: 'none' },
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  backgroundColor: '#FF6B6B',
                                  transform: 'translateY(-1px)'
                                }
                              }}
                            >
                              Supprimer
                            </Button>
                          </Box>
                        )}
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          gap: 0.5,
                          backgroundColor: 'white',
                          padding: '6px 12px',
                          borderRadius: '8px',
                          border: '1px solid rgba(255, 107, 44, 0.2)',
                          boxShadow: '0 2px 4px rgba(255, 107, 44, 0.1)',
                          transition: 'all 0.2s ease',
                          width: { xs: '100%', lg: 'auto' },
                          justifyContent: { xs: 'center', lg: 'flex-start' },
                          '&:hover': {
                            transform: 'translateY(-1px)',
                            boxShadow: '0 4px 8px rgba(255, 107, 44, 0.15)'
                          }
                        }}>
                          {[...Array(5)].map((_, index) => (
                            <Star
                              key={index}
                              size={16}
                              color={index < review.rating ? "#FF6B2C" : "#FFE4BA"}
                              fill={index < review.rating ? "#FF6B2C" : "none"}
                              strokeWidth={2}
                            />
                          ))}
                          <Typography variant="caption" sx={{ 
                            color: '#FF6B2C',
                            fontWeight: 'bold',
                            ml: 0.5,
                            fontSize: '0.85rem'
                          }}>
                            {review.rating}/5
                          </Typography>
                        </Box>
                        {/* BOUTON SIGNALER */}
                        {currentUser?.id !== review.author_id && (
                          <Button
                            onClick={() => handleOpenReportModal(review)}
                            variant="outlined"
                            size="small"
                            startIcon={<ReportProblemIcon style={{ color: '#FF6B2C' }} />}
                            sx={{
                              color: '#FF6B2C',
                              borderColor: '#FF6B2C',
                              fontWeight: 'bold',
                              borderRadius: '8px',
                              fontSize: '0.75rem',
                              textTransform: 'none',
                              ml: 1,
                              '&:hover': {
                                backgroundColor: 'rgba(255, 107, 44, 0.08)',
                                borderColor: '#E55A1F',
                              }
                            }}
                          >
                            Signaler
                          </Button>
                        )}
                      </Box>
                    </Box>
                    {review.comment && (
                      <Typography variant="body2" sx={{ 
                        color: '#4A5568', 
                        mt: 2,
                        lineHeight: 1.6,
                        whiteSpace: 'pre-wrap',
                        backgroundColor: 'white',
                        padding: '12px 16px',
                        borderRadius: '8px',
                        border: '1px solid rgba(255, 107, 44, 0.1)',
                        boxShadow: '0 1px 2px rgba(255, 107, 44, 0.05)',
                        position: 'relative',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          left: 0,
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '3px',
                          height: '40%',
                          backgroundColor: '#FFE4BA',
                          borderRadius: '4px'
                        },
                        fontSize: '0.9rem',
                        letterSpacing: '0.01em'
                      }}>
                        {truncateComment(stripHtml(review.comment), review.id)}
                      </Typography>
                    )}
                    {!expandedComments.includes(review.id) && review.comment && review.comment.length > maxCharacters && (
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleComment(review.id);
                        }}
                        sx={{
                          color: '#FF6B2C',
                          fontSize: '0.875rem',
                          padding: '4px 8px',
                          minWidth: 'auto',
                          textTransform: 'none',
                          mt: 1,
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.08)',
                          }
                        }}
                      >
                        ... Afficher le reste du commentaire
                      </Button>
                    )}
                    {expandedComments.includes(review.id) && (
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleComment(review.id);
                        }}
                        sx={{
                          color: '#FF6B2C',
                          fontSize: '0.875rem',
                          padding: '4px 8px',
                          minWidth: 'auto',
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.08)',
                          }
                        }}
                      >
                        Afficher moins
                      </Button>
                    )}

                    {/* Affichage des photos */}
                    {review.photos && review.photos.length > 0 && (
                      <Box sx={{
                        mt: 2,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2
                      }}>
                        <div className="grid grid-cols-2 gap-2">
                          {review.photos.map((photoUrl, index) => (
                            <Box key={index} sx={{
                              position: 'relative',
                              width: '100%'
                            }}>
                              <img
                                src={photoUrl}
                                alt={`Photo ${index + 1} de l'avis`}
                                style={{
                                  width: '100%',
                                  height: '120px',
                                  objectFit: 'cover',
                                  borderRadius: '8px',
                                  border: '2px solid #FFE4BA',
                                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
                                  transition: 'all 0.3s ease',
                                  cursor: 'pointer'
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Ouvrir la modal de visualisation des photos
                                  if (review.photos && review.photos.length > 0) {
                                    openPhotoModal(review.photos, index);
                                  }
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.transform = 'scale(1.02)';
                                  e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 107, 44, 0.2)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.transform = 'scale(1)';
                                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 107, 44, 0.1)';
                                }}
                              />
                            </Box>
                          ))}
                        </div>
                        {review.photos.length > 0 && (
                          <Typography variant="caption" sx={{
                            color: '#6B7280',
                            textAlign: 'center',
                            fontSize: '0.75rem'
                          }}>
                            {review.photos.length} photo{review.photos.length > 1 ? 's' : ''} - Cliquez pour agrandir
                          </Typography>
                        )}
                      </Box>
                    )}

                    {review.qualites && review.qualites.length > 0 && (
                      <Box sx={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: 1, 
                        mt: 2,
                        pl: 3,
                        opacity: 0.9
                      }}>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: '#4A5568',
                            fontWeight: 'medium',
                            display: 'flex',
                            alignItems: 'center',
                            mr: 1
                          }}
                        >
                          Points forts :
                        </Typography>
                        {review.qualites.map((qualite, index) => (
                          <Box
                            key={index}
                            sx={{
                              backgroundColor: 'rgba(34, 197, 94, 0.08)',
                              color: '#16A34A',
                              fontWeight: 'medium',
                              fontSize: '0.75rem',
                              height: '24px',
                              padding: '0 8px',
                              borderRadius: '12px',
                              display: 'flex',
                              alignItems: 'center',
                              border: '1px solid rgba(34, 197, 94, 0.2)',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                backgroundColor: 'rgba(34, 197, 94, 0.12)',
                                transform: 'translateY(-1px)'
                              }
                            }}
                          >
                            {decodeURIComponent(qualite.replace(/&(#x27|apos|quot);/g, "'").replace(/&amp;/g, '&'))}
                          </Box>
                        ))}
                      </Box>
                    )}
                    
                    {/* Affichage des défauts */}
                    {review.defauts && review.defauts.length > 0 && (
                      <Box sx={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: 1, 
                        mt: 2,
                        pl: 3,
                        opacity: 0.9
                      }}>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: '#4A5568',
                            fontWeight: 'medium',
                            display: 'flex',
                            alignItems: 'center',
                            mr: 1
                          }}
                        >
                          Points à améliorer :
                        </Typography>
                        {review.defauts.map((defaut: string, index: number) => (
                          <Box
                            key={index}
                            sx={{
                              backgroundColor: 'rgba(239, 68, 68, 0.08)',
                              color: '#DC2626',
                              fontWeight: 'medium',
                              fontSize: '0.75rem',
                              height: '24px',
                              padding: '0 8px',
                              borderRadius: '12px',
                              display: 'flex',
                              alignItems: 'center',
                              border: '1px solid rgba(239, 68, 68, 0.2)',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                backgroundColor: 'rgba(239, 68, 68, 0.12)',
                                transform: 'translateY(-1px)'
                              }
                            }}
                          >
                            {decodeURIComponent(defaut.replace(/&(#x27|apos|quot);/g, "'").replace(/&amp;/g, '&'))}
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Box>
                </React.Fragment>
              ))}
            </div>
            
            {/* Bouton Charger plus */}
            {hasMore && (
              <Box sx={{ textAlign: 'center', mt: 2, mb: 2 }}>
                <Button
                  onClick={() => loadMore()}
                  variant="outlined"
                  sx={{
                    color: '#FF6B2C',
                    borderColor: '#FF6B2C',
                    '&:hover': {
                      borderColor: '#FF965E',
                      backgroundColor: 'rgba(255, 107, 44, 0.04)'
                    }
                  }}
                >
                  {loading ? 'Chargement...' : 'Charger plus d\'avis'}
                </Button>
              </Box>
            )}
          </>
        ) : (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
            backgroundColor: emptyStateBackgroundColor,
            borderRadius: '12px',
            border: '1px solid #FFE4BA',
            ...(viewType === 'profil' && {
              maxWidth: '800px',
              margin: '0 auto'
            })
          }}>
            <Star size={48} color="#FFE4BA" />
            <Typography variant="h6" sx={{ 
              mt: 2, 
              color: '#4A5568',
              fontWeight: 'medium',
              textAlign: 'center'
            }}>
              Aucun avis
            </Typography>
            <Typography variant="body2" sx={{ 
              mt: 1,
              color: '#718096',
              textAlign: 'center'
            }}>
              {userData?.profil?.data?.prenom ? 
                `Soyez le premier à laisser un avis sur ${userData.profil.data.prenom} !` :
                'Soyez le premier à laisser un avis !'
              }
            </Typography>
            {currentUser && currentUser.id !== userId && (
              <Button
                variant="contained"
                startIcon={<Star size={18} />}
                onClick={onOpenMissionSelector}
                sx={{
                  mt: 3,
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#FF965E'
                  }
                }}
              >
                Déposer un avis
              </Button>
            )}
          </Box>
        )}
      </Box>
      {/* MODALE SIGNALER */}
      <Dialog open={reportModalOpen} onClose={() => setReportModalOpen(false)} maxWidth="xs" fullWidth>
        <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Signaler cet avis</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, color: '#222' }}>
            Merci d'indiquer la raison de votre signalement. L'équipe de modération analysera votre demande.
          </Typography>
          <TextField
            label="Raison du signalement"
            fullWidth
            multiline
            minRows={2}
            value={reportReason}
            onChange={e => setReportReason(e.target.value)}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportModalOpen(false)} color="inherit" sx={{ fontWeight: 600 }}>Annuler</Button>
          <Button
            onClick={handleSendReport}
            color="error"
            variant="contained"
            sx={{ fontWeight: 700, bgcolor: '#FF6B2C' }}
            disabled={reportLoading || !reportReason.trim()}
          >
            {reportLoading ? 'Envoi...' : 'Signaler'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* MODAL DE VISUALISATION DES PHOTOS */}
      {photoModalOpen && currentPhotos.length > 0 && (
        <ModalPortal
          isOpen={photoModalOpen}
          onBackdropClick={closePhotoModal}
          zIndex={2000}
        >
          <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-2 sm:p-4">
            <div className="relative w-full h-full max-w-7xl bg-white rounded-none sm:rounded-2xl shadow-2xl overflow-hidden flex flex-col">

              {/* Header de la modal */}
              <div className="bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between flex-shrink-0">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <Star size={16} className="sm:w-5 sm:h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-base sm:text-lg">Photos de l'avis</h3>
                    <p className="text-white text-opacity-90 text-xs sm:text-sm">
                      {currentPhotos.length} photo{currentPhotos.length > 1 ? 's' : ''} disponible{currentPhotos.length > 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                <button
                  onClick={closePhotoModal}
                  className="w-8 h-8 sm:w-10 sm:h-10 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-200 group"
                >
                  <X size={16} className="sm:w-5 sm:h-5 text-white group-hover:scale-110 transition-transform duration-200" />
                </button>
              </div>

              {/* Contenu principal - Zone d'affichage de l'image */}
              <div className="relative flex-1 bg-gradient-to-br from-[#FFF8F3] to-white flex items-center justify-center p-2 sm:p-4 md:p-6 min-h-0">
                <div className="relative w-full h-full flex items-center justify-center">
                  <img
                    src={currentPhotos[currentPhotoIndex]}
                    alt={`Photo ${currentPhotoIndex + 1} de l'avis`}
                    className="max-w-full max-h-full object-contain rounded-lg sm:rounded-xl shadow-lg"
                  />

                  {/* Overlay avec informations */}
                  <div className="absolute top-2 sm:top-4 left-2 sm:left-4 bg-black bg-opacity-60 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">
                    {currentPhotoIndex + 1} / {currentPhotos.length}
                  </div>
                </div>

                {/* Boutons de navigation */}
                {currentPhotos.length > 1 && (
                  <>
                    <button
                      onClick={goToPreviousPhoto}
                      className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 z-10"
                    >
                      <ChevronLeft size={20} className="sm:w-6 sm:h-6" />
                    </button>

                    <button
                      onClick={goToNextPhoto}
                      className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110 z-10"
                    >
                      <ChevronRight size={20} className="sm:w-6 sm:h-6" />
                    </button>
                  </>
                )}
              </div>

              {/* Footer avec miniatures - Seulement si plusieurs photos */}
              {currentPhotos.length > 1 && (
                <div className="bg-white border-t border-gray-100 px-3 sm:px-6 py-3 sm:py-4 flex-shrink-0">
                  <div className="flex items-center justify-center gap-2 sm:gap-3 overflow-x-auto pb-2">
                    {currentPhotos.map((photo, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentPhotoIndex(index)}
                        className={`flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 hover:scale-105 ${
                          index === currentPhotoIndex
                            ? 'border-[#FF6B2C] shadow-lg'
                            : 'border-gray-200 hover:border-[#FFE4BA]'
                        }`}
                      >
                        <img
                          src={photo}
                          alt={`Miniature ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>

                  {/* Instructions - Masquées sur mobile */}
                  <div className="text-center mt-2 hidden sm:block">
                    <p className="text-gray-500 text-sm">
                      Utilisez les flèches du clavier ou cliquez sur les miniatures pour naviguer
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModalPortal>
      )}
    </Box>
  );
};

export default ReviewModalListeDesAvis;