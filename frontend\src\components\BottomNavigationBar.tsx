import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { MessageCircle, Search, Plus, Home, User, Crown } from 'lucide-react';
import { Badge } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import { motion, AnimatePresence } from 'framer-motion';
import api from '../services/api';
import { logger } from '../utils/logger';
import { useSubscription } from '../hooks/useSubscription';

interface BottomNavigationBarProps {
  className?: string;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  badge?: number;
  color: string;
  isMainAction?: boolean;
  isPremium?: boolean;
  state?: any;
}

const BottomNavigationBar: React.FC<BottomNavigationBarProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const { socket } = useSocket();
  const navigate = useNavigate();
  const location = useLocation();
  const [unreadMessagesCount, setUnreadMessagesCount] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  
  // Utiliser le hook useSubscription existant au lieu de faire une requête manuelle
  const { status: subscriptionStatus, isLoading: subscriptionLoading } = useSubscription();

  // Fonction pour récupérer le nombre de messages non lus
  const fetchUnreadMessagesCount = useCallback(async () => {
    if (user) {
      try {
        const response = await api.get<{ success: boolean; data?: { totalCount?: number } }>('/api/messages?unread_only=true&limit=1');
        if (response.data.success && response.data.data) {
          setUnreadMessagesCount(response.data.data.totalCount || 0);
        }
      } catch (error) {
        logger.info("Erreur lors de la récupération du nombre de messages non lus:", error);
      }
    }
  }, [user]);

  // Récupération initiale
  useEffect(() => {
    fetchUnreadMessagesCount();
  }, [fetchUnreadMessagesCount]);

  // Écoute des événements WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    if (socket && user) {
      const handleMessageUpdate = () => {
        fetchUnreadMessagesCount();
      };

      const eventsToListen = [
        'new_message',
        'message_read',
        'conversation_unread',
        'message_deleted'
      ];

      eventsToListen.forEach(event => {
        socket.on(event, handleMessageUpdate);
      });

      return () => {
        eventsToListen.forEach(event => {
          socket.off(event, handleMessageUpdate);
        });
      };
    }
  }, [socket, user, fetchUnreadMessagesCount]);

  // Gestion du scroll pour masquer/afficher la barre
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const isScrollingDown = currentScrollY > lastScrollY;
      const isAtTop = currentScrollY < 10;

      if (isAtTop) {
        setIsVisible(true);
      } else if (isScrollingDown && currentScrollY > 100) {
        setIsVisible(false);
      } else if (!isScrollingDown) {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  // Déterminer la page active
  const isActive = (path: string) => {
    if (path === '/dashboard/messages') {
      return location.pathname.startsWith('/dashboard/messages');
    }
    if (path === '/dashboard/missions') {
      return location.pathname === '/dashboard/missions' || location.pathname.startsWith('/dashboard/missions') && !location.pathname.includes('poster-une-mission');
    }
    if (path === '/dashboard/missions/poster-une-mission') {
      return location.pathname === '/dashboard/missions/poster-une-mission';
    }
    if (path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    if (path === '/dashboard/profil') {
      return location.pathname.startsWith('/dashboard/profil');
    }
    if (path === '/dashboard/premium') {
      return location.pathname === '/dashboard/premium';
    }
    return false;
  };

  // Navigation avec animation
  const handleNavigation = (item: NavigationItem) => {
    navigate(item.path, { state: item.state });
  };

  // Configuration des éléments de navigation
  const getNavigationItems = (): NavigationItem[] => {
    const baseItems: NavigationItem[] = [
      {
        id: 'home',
        label: 'Accueil',
        icon: Home,
        path: '/dashboard',
        badge: 0,
        color: '#FF6B2C'
      },
      {
        id: 'missions',
        label: 'Missions',
        icon: Search,
        path: '/dashboard/missions',
        state: { activeTab: 2 },
        badge: 0,
        color: '#FF7A35'
      }
    ];

    // Ajouter l'onglet Premium si l'utilisateur n'est pas premium
    // Utiliser les données du hook useSubscription
    if (!subscriptionLoading && subscriptionStatus && !subscriptionStatus.isPremium) {
      baseItems.push({
        id: 'premium',
        label: 'Premium',
        icon: Crown,
        path: '/dashboard/premium',
        badge: 0,
        color: '#FFD700',
        isPremium: true
      });
    }

    // Ajouter le bouton principal "Publier"
    baseItems.push({
      id: 'post-mission',
      label: 'Publier',
      icon: Plus,
      path: '/dashboard/missions/poster-une-mission',
      badge: 0,
      color: '#FF6B2C',
      isMainAction: true
    });

    // Ajouter les autres éléments
    baseItems.push(
      {
        id: 'messages',
        label: 'Messages',
        icon: MessageCircle,
        path: '/dashboard/messages',
        badge: unreadMessagesCount,
        color: '#FF6B2C'
      },
      {
        id: 'profile',
        label: 'Profil',
        icon: User,
        path: '/dashboard/profil',
        badge: 0,
        color: '#FF6B2C'
      }
    );

    return baseItems;
  };

  const navigationItems = getNavigationItems();

  // Vérifications conditionnelles après tous les hooks
  // Ne pas afficher si l'utilisateur n'est pas connecté
  if (!user) return null;

  // Ne pas afficher sur certaines pages
  const hiddenPaths = [
    '/admin',
    '/login',
    '/inscription',
    '/forgot-password',
    '/reset-password',
    '/verify-email',
    '/verify-two-factor'
  ];

  if (hiddenPaths.some(path => location.pathname.startsWith(path))) return null;

  // Ne pas afficher sur les pages publiques (non-dashboard)
  if (!location.pathname.startsWith('/dashboard')) return null;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: "spring", stiffness: 400, damping: 30 }}
          className={`fixed bottom-0 left-0 right-0 z-40 ${className}`}
          style={{ paddingBottom: 'env(safe-area-inset-bottom)', maxWidth: '420px', minWidth: '280px', margin: '0 auto' }}
        >
          <div className="px-2 pb-2 flex justify-center">
            <motion.div 
              className="relative bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-white/50 overflow-hidden max-w-full"
              style={{
                width: 'min(95vw, 420px)',
                minWidth: '280px'
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 30 }}
            >
              <div className="flex items-center justify-around h-16 px-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const active = isActive(item.path);
                  
                  if (item.isMainAction) {
                    return (
                      <motion.div
                        key={item.id}
                        className="relative flex justify-center items-center"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="w-12 h-12 bg-gradient-to-br from-[#FF6B2C] to-[#FF965E] rounded-full flex items-center justify-center shadow-[0_4px_12px_rgba(255,107,44,0.35)]"
                          onClick={() => handleNavigation(item)}
                        >
                          <Icon className="h-6 w-6 text-white" />
                        </motion.div>
                      </motion.div>
                    );
                  }
                  
                  return (
                    <motion.button
                      key={item.id}
                      className="relative flex flex-col items-center justify-center min-w-0 flex-1 px-1"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleNavigation(item)}
                    >
                      <div className="relative">
                        <motion.div
                          className={`flex items-center justify-center w-10 h-10 rounded-full ${
                            active ? 'bg-[#FFF8F3]' : 'bg-transparent'
                          } transition-all duration-300`}
                        >
                          {item.badge && item.badge > 0 ? (
                            <Badge
                              badgeContent={item.badge}
                              color="error"
                              sx={{
                                '& .MuiBadge-badge': {
                                  backgroundColor: '#FF6B2C',
                                  color: 'white',
                                  fontSize: '0.6rem',
                                  fontWeight: '700',
                                  minWidth: '16px',
                                  height: '16px',
                                  padding: '0 4px',
                                  right: -2,
                                  top: 0,
                                  border: '2px solid white',
                                  boxShadow: '0 2px 6px rgba(255, 107, 44, 0.3)'
                                }
                              }}
                            >
                              <Icon 
                                className={`h-5 w-5 ${
                                  active 
                                    ? item.isPremium 
                                      ? 'text-[#FFD700]' 
                                      : 'text-[#FF6B2C]'
                                    : item.isPremium 
                                      ? 'text-[#FFD700]' 
                                      : 'text-gray-500'
                                } transition-all duration-300`} 
                              />
                            </Badge>
                          ) : (
                            <Icon 
                              className={`h-5 w-5 ${
                                active 
                                  ? item.isPremium 
                                    ? 'text-[#FFD700]' 
                                    : 'text-[#FF6B2C]'
                                  : item.isPremium 
                                    ? 'text-[#FFD700]' 
                                    : 'text-gray-500'
                              } transition-all duration-300`} 
                            />
                          )}
                        </motion.div>
                      </div>
                      
                      <motion.span
                        className={`text-xs font-medium mt-1 ${
                          active 
                            ? item.isPremium 
                              ? 'text-[#FFD700]' 
                              : 'text-[#FF6B2C]'
                            : item.isPremium 
                              ? 'text-[#FFD700]' 
                              : 'text-gray-500'
                        } whitespace-nowrap overflow-hidden text-ellipsis max-w-full`}
                        style={{ fontSize: '10px' }}
                      >
                        {item.label}
                      </motion.span>
                    </motion.button>
                  );
                })}
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BottomNavigationBar;
