import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import logger from './src/utils/logger';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  try {
    logger.info('Building with env:', {
      VITE_SUPABASE_URL: env.VITE_SUPABASE_URL,
      NODE_ENV: env.NODE_ENV
    });
  } catch (error) {
    console.log('Fallback logging - Building with env:', {
      VITE_SUPABASE_URL: env.VITE_SUPABASE_URL,
      NODE_ENV: env.NODE_ENV
    });
  }

  const envWithDefaults = {
    VITE_SUPABASE_URL: env.VITE_SUPABASE_URL || 'https://stockage.jobpartiel.fr',
    VITE_SUPABASE_ANON_KEY: env.VITE_SUPABASE_ANON_KEY,
    VITE_API_URL: env.VITE_API_URL || 'http://localhost:3001'
  };

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@pages': resolve(__dirname, './src/pages'),
        '@services': resolve(__dirname, './src/services'),
        '@contexts': resolve(__dirname, './src/contexts')
      }
    },
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    server: {
      port: 5173,
      strictPort: true,
      host: true,
      fs: {
        strict: false,
        allow: ['..']
      }
    },
    define: {
      'import.meta.env': JSON.stringify({
        ...env,
        ...envWithDefaults,
        MODE: mode,
        DEV: mode === 'development',
        PROD: mode === 'production',
      })
    }
  };
});
