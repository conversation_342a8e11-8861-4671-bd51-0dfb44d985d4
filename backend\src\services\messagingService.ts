import { supabase } from '../config/supabase';
import { UploadedFile } from 'express-fileupload';
import { uploadMessageAttachment } from './storage';
import { MessageAttachment } from '../types/messaging';
import { DatabaseService } from './db';
import { io } from '../server';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { sendNewMessageEmail, sendNewConversationEmail } from './emailService';
import { getPublicUrl } from './storage';

const dbService = DatabaseService.getInstance();

interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  attachments: MessageAttachment[];
  sender?: any;
}

interface Conversation {
  id: string;
  user1_id?: string;
  user2_id?: string;
  created_at: string;
  updated_at?: string;
  last_message_date?: string;
  last_message_content?: string;
  last_message_sender_id?: string;
  unread_count?: number;
  user1_has_blocked?: boolean;
  user2_has_blocked?: boolean;
  user1_has_deleted?: boolean;
  user2_has_deleted?: boolean;
  is_blocked?: boolean;
  user1?: any;
  user2?: any;
  last_message?: Message;
  messages?: Message[];
  
  // Champs pour la rétrocompatibilité
  participants?: string[];
  creator_id?: string;
  last_activity?: string;
  last_message_preview?: string;
  isBlocked?: boolean;
  otherUser?: any;
}

interface SendMessageParams {
  conversationId: string;
  senderId: string;
  content: string;
  recipientId: string;
  attachments?: UploadedFile[];
}

interface CreateConversationParams {
  creatorId: string;
  participantId: string;
  initialMessage: string;
  attachments?: UploadedFile[];
}

interface GetConversationsParams {
  userId: string;
  limit?: number;
  offset?: number;
  search?: string;
  showDeleted?: boolean;
  unread_only?: boolean;
}

interface GetMessagesParams {
  conversationId: string;
  limit?: number;
  offset?: number;
}

interface ToggleBlockParams {
  userId: string;
  conversationId: string;
  isBlocked: boolean;
}

interface DeleteConversationParams {
  userId: string;
  conversationId: string;
}

interface MarkMessagesAsReadParams {
  userId: string;
  conversationId: string;
}

interface MarkConversationAsUnreadParams {
  userId: string;
  conversationId: string;
}

interface DeleteMessageParams {
  userId: string;
  conversationId: string;
  messageId: string;
}

class MessagingService {
  // Get all conversations for a user
  async getConversations({ userId, limit = 10, offset = 0, search = '', showDeleted, unread_only }: GetConversationsParams): Promise<ServiceResult<{ conversations: Conversation[]; totalCount: number }>> {
    try {
      const isShowDeleted = showDeleted === true;
      
      // bBase query for counting and fetching conversations
      let baseQuery = supabase
        .from('user_messages_conversations')
        .select('*', { count: 'exact' })
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null);

      // Filtrer les conversations en fonction de showDeleted
      if (!isShowDeleted) {
        // Si on ne veut pas voir les conversations masquées
        baseQuery = baseQuery.or(
          `and(user1_id.eq.${userId},user1_has_deleted.eq.false),` +
          `and(user2_id.eq.${userId},user2_has_deleted.eq.false)`
        );
      } else {
        // Si on veut voir toutes les conversations, y compris les masquées
        baseQuery = baseQuery.or(`user1_id.eq.${userId},user2_id.eq.${userId}`);
      }

      // Ajouter le filtre pour les messages non lus si demandé
      if (unread_only) {
        baseQuery = baseQuery.or(
          `and(user1_id.eq.${userId},unread_count_user1.gt.0),` +
          `and(user2_id.eq.${userId},unread_count_user2.gt.0)`
        );
      }

      // Get the total count first
      const { count, error: countError } = await baseQuery;
        
      if (countError) {
        throw new Error(`Failed to count conversations: ${countError.message}`);
      }

      // Get basic conversations info with the same base query but without count
      let queryConversations = supabase
        .from('user_messages_conversations')
        .select('*')
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null);

      // Appliquer le même filtre pour les conversations masquées
      if (!isShowDeleted) {
        queryConversations = queryConversations.or(
          `and(user1_id.eq.${userId},user1_has_deleted.eq.false),` +
          `and(user2_id.eq.${userId},user2_has_deleted.eq.false)`
        );
      } else {
        queryConversations = queryConversations.or(`user1_id.eq.${userId},user2_id.eq.${userId}`);
      }

      // Ajouter le filtre pour les messages non lus si demandé
      if (unread_only) {
        queryConversations = queryConversations.or(
          `and(user1_id.eq.${userId},unread_count_user1.gt.0),` +
          `and(user2_id.eq.${userId},unread_count_user2.gt.0)`
        );
      }

      // Ajouter l'ordre et la pagination
      const { data: conversations, error: conversationsError } = await queryConversations
        .order('updated_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (conversationsError) {
        console.error('Error getting conversations:', conversationsError);
        return { success: false, error: `Failed to get conversations: ${conversationsError.message}` };
      }

      // 2. Process each conversation to get participants and messages
      const processedConversations = await Promise.all(conversations
        // Filtrer les conversations avec des utilisateurs anonymisés au niveau du traitement
        .filter(conversation => 
          conversation.user1_id && 
          conversation.user2_id && 
          conversation.user1_id !== 'null' && 
          conversation.user2_id !== 'null'
        )
        .map(async (conversation) => {
        // Déterminer l'autre participant (pas l'utilisateur courant)
        const isUser1 = conversation.user1_id === userId;
        const otherUserId = isUser1 ? conversation.user2_id : conversation.user1_id;
        const isBlocked = isUser1 ? conversation.user1_has_blocked : conversation.user2_has_blocked;
        
        // Vérifier que otherUserId est valide avant d'appeler getUserById
        let userData = null;
        if (otherUserId && otherUserId !== 'null' && typeof otherUserId === 'string') {
          userData = await dbService.getUserById(otherUserId);
        } else {
          console.warn('ID utilisateur invalide dans la conversation:', { 
            conversationId: conversation.id, 
            otherUserId, 
            user1_id: conversation.user1_id, 
            user2_id: conversation.user2_id 
          });
        }

        // Transformer les données utilisateur pour maintenir la compatibilité avec le format existant
        const formattedUserData = userData 
          ? {
              id: userData.id,
              email: userData.email,
              avatar_url: userData.profil?.data?.photo_url || null,
              notification_preferences: userData.notification_preferences,
              prenom: userData.profil?.data?.prenom || null,
              nom: userData.profil?.data?.nom || null,
              first_name: userData.profil?.data?.prenom || null,
              last_name: userData.profil?.data?.nom || null
            }
          : { id: otherUserId };
        
        // Récupérer le dernier message
        const { data: lastMessageData } = await supabase
          .from('user_messages')
          .select('id, sender_id, content, created_at')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: false })
          .limit(1);

        const lastMessage = lastMessageData && lastMessageData.length > 0 ? lastMessageData[0] : null;
        
        // Créer des objets user1 et user2 pour le format attendu par le frontend
        const user1 = conversation.user1_id === userId 
          ? null // On ne s'embête pas avec les détails de l'utilisateur courant
          : formattedUserData;
          
        const user2 = conversation.user1_id === userId
          ? formattedUserData
          : null;

        return {
          id: conversation.id,
          user1_id: conversation.user1_id,
          user2_id: conversation.user2_id,
          participants: [conversation.user1_id, conversation.user2_id],
          creator_id: conversation.user1_id, // Utiliser user1_id comme créateur par défaut
          created_at: conversation.created_at,
          updated_at: conversation.updated_at,
          last_activity: conversation.updated_at,
          last_message_date: lastMessage ? lastMessage.created_at : null,
          last_message_content: lastMessage ? lastMessage.content : '',
          last_message_preview: lastMessage ? lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : '') : '',
          last_message_sender_id: lastMessage ? lastMessage.sender_id : null,
          unread_count: isUser1 ? conversation.unread_count_user1 || 0 : conversation.unread_count_user2 || 0,
          user1_has_blocked: conversation.user1_has_blocked || false,
          user2_has_blocked: conversation.user2_has_blocked || false,
          user1_has_deleted: conversation.user1_has_deleted || false,
          user2_has_deleted: conversation.user2_has_deleted || false,
          is_blocked: isUser1 ? conversation.user1_has_blocked : conversation.user2_has_blocked,
          isBlocked: isBlocked,
          // Ajouter user1 et user2 pour le frontend
          user1: user1 ? { ...user1 } : { id: conversation.user1_id },
          user2: user2 ? { ...user2 } : { id: conversation.user2_id },
          otherUser: formattedUserData
        };
      }));

      return {
        success: true,
        data: {
          conversations: processedConversations,
          totalCount: count || 0
        }
      };
    } catch (error) {
      console.error('Error in getConversations:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  // Get all messages for a conversation
  async getMessages({ conversationId, limit = 20, offset = 0 }: GetMessagesParams): Promise<ServiceResult<{ messages: Message[]; totalCount: number }>> {
    try {
      // Get the total count first
      const { count, error: countError } = await supabase
        .from('user_messages')
        .select('*', { count: 'exact', head: true })
        .eq('conversation_id', conversationId);
        
      if (countError) {
        throw new Error(`Failed to count messages: ${countError.message}`);
      }
      
      // 1. Get basic message info
      const { data: messagesData, error: messagesError } = await supabase
        .from('user_messages')
        .select('*, is_read, read_at')
        .eq('conversation_id', conversationId)
        // Exclure les messages d'utilisateurs anonymisés (RGPD)
        .not('sender_id', 'is', null)
        .not('receiver_id', 'is', null)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (messagesError) {
        console.error('Error getting messages:', messagesError);
        return { success: false, error: `Failed to get messages: ${messagesError.message}` };
      }

      // 2. Process each message to get sender info and attachments
      const processedMessages = await Promise.all(messagesData.map(async (message) => {
        // Vérifier que sender_id est valide avant d'appeler getUserById
        let senderData = null;
        if (message.sender_id && message.sender_id !== 'null' && typeof message.sender_id === 'string') {
          senderData = await dbService.getUserById(message.sender_id);
        } else {
          console.warn('ID expéditeur invalide dans le message:', { 
            messageId: message.id, 
            senderId: message.sender_id 
          });
        }
        
        // Transformer les données de l'expéditeur pour maintenir la compatibilité
        const formattedSenderData = senderData 
          ? {
              id: senderData.id,
              email: senderData.email,
              avatar_url: senderData.profil?.data?.photo_url || null,
              notification_preferences: senderData.notification_preferences,
              first_name: senderData.profil?.data?.prenom || null,
              last_name: senderData.profil?.data?.nom || null
            }
          : { id: message.sender_id };

        // Récupérer les pièces jointes
        const { data: attachmentsData } = await supabase
          .from('user_message_attachments')
          .select('*')
          .eq('message_id', message.id);

        // Traiter les pièces jointes pour ajouter l'URL publique complète
        const processedAttachments = attachmentsData ? attachmentsData.map(attachment => {
          // Construire l'URL publique en utilisant la fonction du service de stockage
          const publicUrl = getPublicUrl('message_attachments', attachment.file_path);
          
          return {
            ...attachment,
            public_url: publicUrl,
            storage_path: attachment.file_path // Assurer la cohérence des noms de champs
          };
        }) : [];

        return {
          id: message.id,
          conversation_id: message.conversation_id,
          sender_id: message.sender_id,
          content: message.content,
          created_at: message.created_at,
          attachments: processedAttachments,
          sender: formattedSenderData,
          is_read: message.is_read,
          read_at: message.read_at
        };
      }));

      return {
        success: true,
        data: {
          messages: processedMessages,
          totalCount: count || 0
        }
      };
    } catch (error) {
      console.error('Error in getMessages:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  // Process and upload file attachments for a message
  private async processAttachments(files: UploadedFile[], senderId: string, conversationId?: string): Promise<MessageAttachment[]> {
    const processedAttachments: MessageAttachment[] = [];

    for (const file of files) {
      try {
        // Vérifier si le fichier est valide
        if (!file || !file.name || !file.data) {
          continue;
        }

        // Upload du fichier avec l'ID de l'utilisateur et l'ID de conversation
        const result = await uploadMessageAttachment(file, senderId, conversationId);
        if (result) {
          processedAttachments.push(result);
        }
      } catch (error) {
        console.error(`Error processing attachment ${file.name}:`, error);
        // Continue with the next file if one fails
      }
    }

    return processedAttachments;
  }

  // Send a message
  async sendMessage({ conversationId, senderId, content, recipientId, attachments = [] }: SendMessageParams): Promise<Message> {
    try {
      // Vérifier que l'utilisateur n'essaie pas d'envoyer un message à lui-même
      if (senderId === recipientId) {
        throw new Error("Vous ne pouvez pas vous envoyer de messages à vous-même.");
      }
      
      // Get the recipient for later notification
      const recipient = recipientId && recipientId !== 'null' && typeof recipientId === 'string' 
        ? await dbService.getUserById(recipientId) 
        : null;
      
      // Vérifier si la conversation est masquée et la démasquer si nécessaire
      const { data: conversationData } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (!conversationData) {
        throw new Error('Conversation non trouvée');
      }

      // Vérifier si la conversation est bloquée par l'un des participants
      if (conversationData.user1_has_blocked || conversationData.user2_has_blocked) {
        throw new Error('Cette conversation est bloquée. Vous ne pouvez pas envoyer de messages.');
      }

      if (conversationData && (conversationData.user1_has_deleted || conversationData.user2_has_deleted)) {
        await supabase
          .from('user_messages_conversations')
          .update({
            user1_has_deleted: false,
            user2_has_deleted: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', conversationId);
      }
      
      // Process attachments if any
      const processedAttachments = attachments.length > 0 
        ? await this.processAttachments(attachments, senderId, conversationId) 
        : [];

      // Create the message
      const { data: message, error } = await supabase
        .from('user_messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          receiver_id: recipientId,
          content,
          has_attachment: processedAttachments.length > 0
        })
        .select('*')
        .single();

      if (error) {
        throw new Error(`Failed to send message: ${error.message}`);
      }

      // Ajouter les pièces jointes dans la table user_message_attachments
      if (processedAttachments.length > 0) {
        const attachmentsToInsert = processedAttachments.map(attachment => ({
          message_id: message.id,
          file_name: attachment.file_name,
          file_size: attachment.file_size,
          mime_type: attachment.mime_type,
          file_path: attachment.file_path, // Utiliser file_path au lieu de storage_path
          expires_at: attachment.expires_at,
        }));

        const { error: attachmentError } = await supabase
          .from('user_message_attachments')
          .insert(attachmentsToInsert);

        if (attachmentError) {
          console.error("Erreur lors de l'insertion des pièces jointes:", attachmentError);
          // Le message est envoyé mais les pièces jointes peuvent être perdues
          // On pourrait ajouter un mécanisme de retry ici
        }
      }

      // Récupérer les informations de l'expéditeur pour une utilisation ultérieure
      const sender = senderId && senderId !== 'null' && typeof senderId === 'string' 
        ? await dbService.getUserById(senderId) 
        : null;

      // Créer une clé unique pour cette vérification de notification
      const notificationKey = `message:notification:${conversationId}:${recipientId}`;
      
      // Vérifier si une vérification est déjà planifiée pour cette conversation/destinataire
      const isAlreadyQueued = await redis.get(notificationKey);
      
      if (isAlreadyQueued) {
        // Si une vérification est déjà planifiée, on ne fait rien
        console.log(`Vérification de notification déjà planifiée pour ${notificationKey}`);
      } else {
        // Marquer cette conversation comme en attente de vérification (expire après 15 minutes)
        await redis.set(notificationKey, Date.now().toString(), 'EX', 900);
        
        // Programmer une vérification asynchrone dans 10 minutes
        setTimeout(async () => {
          try {
            // Libérer la clé de verrouillage
            await redis.del(notificationKey);
            
            // Vérifier s'il y a déjà des messages non lus dans la conversation
            const { count: unreadCount } = await supabase
              .from('user_messages')
              .select('*', { count: 'exact', head: true })
              .eq('conversation_id', conversationId)
              .eq('receiver_id', recipientId)
              .eq('is_read', false);
            
            const isFirstMessage = !conversationData.last_message_id; // Si pas de last_message_id, c'est le premier message
            
            // On notifie uniquement si c'est le premier message OU s'il n'y avait pas déjà de messages non lus
            const shouldNotify = isFirstMessage || unreadCount === 1; // unreadCount === 1 car notre message actuel est compté

            let shouldNotifyDueToTime = false;

            // On vérifie le délai UNIQUEMENT si on n'a pas déjà décidé d'envoyer une notification
            if (!shouldNotify) {
              // Vérifier le délai depuis la dernière notification
              const { data: lastNotification } = await supabase
                .from('user_notifications')
                .select('created_at')
                .eq('user_id', recipientId)
                .eq('type', 'message')
                .eq('conversation_id', conversationId)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

              // Si plus de 12 heures depuis la dernière notification
              const twelveHoursInMs = 12 * 60 * 60 * 1000; // 12 heures en millisecondes
              if (lastNotification) {
                const timeSinceLastNotif = Date.now() - new Date(lastNotification.created_at).getTime();
                shouldNotifyDueToTime = timeSinceLastNotif > twelveHoursInMs;
              }

              console.log('Vérification du délai car pas de notification immédiate:', {
                lastNotificationDate: lastNotification?.created_at,
                timeSinceLastNotif: lastNotification ? Date.now() - new Date(lastNotification.created_at).getTime() : 'pas de notif précédente',
                shouldNotifyDueToTime
              });
            }

            console.log('Vérification après 10 minutes des messages non lus:', {
              messageId: message.id,
              isFirstMessage,
              unreadCount,
              shouldNotify,
              shouldNotifyDueToTime,
              explanation: shouldNotify 
                ? "Notification envoyée car : " + 
                  (isFirstMessage ? "premier message" : "pas d'autres messages non lus")
                : shouldNotifyDueToTime
                  ? "Notification envoyée car plus de 12 heures depuis la dernière"
                  : "Pas de notification car il y a déjà des messages non lus et moins de 12h"
            });

            if ((shouldNotify || shouldNotifyDueToTime) && recipient && recipient.email) {
              const shouldSendNotification = recipient.notification_preferences?.messages !== false;
              
              if (shouldSendNotification) {
                const messageUrl = `${process.env.FRONTEND_URL}/dashboard/messages/${conversationId}`;
                const senderName = sender?.profil?.data?.prenom && sender?.profil?.data?.nom
                  ? `${sender.profil.data.prenom} ${sender.profil.data.nom.charAt(0).toUpperCase() + '.'}`
                  : sender?.email || `Utilisateur ${senderId.substring(0, 8)}`;

                // Envoyer l'email
                await sendNewMessageEmail(recipient.email, { senderName, messageUrl });

                // Créer une notification dans le tableau de bord
                try {
                  const { data: notifData, error: notifError } = await supabase
                    .from('user_notifications')
                    .insert({
                      user_id: recipientId,
                      type: 'message',
                      title: `Nouveau message de ${senderName}`,
                      content: content.length > 100 ? `${content.substring(0, 100)}...` : content,
                      is_read: false,
                      is_archived: false,
                      link: `/dashboard/messages/${conversationId}`,
                      conversation_id: conversationId
                    })
                    .select();

                  if (notifError) {
                    console.error('Erreur lors de la création de la notification:', notifError);
                    
                    // Si l'insertion échoue, tenter une approche alternative
                    const { error: directError } = await supabase
                      .from('user_notifications')
                      .insert({
                        user_id: recipientId,
                        type: 'message',
                        title: `Nouveau message de ${senderName}`,
                        content: content.length > 100 ? `${content.substring(0, 100)}...` : content,
                        is_read: false,
                        is_archived: false,
                        link: `/dashboard/messages/${conversationId}`,
                        conversation_id: conversationId
                      });
                      
                    if (directError) {
                      console.error('Échec de l\'insertion directe de la notification:', directError);
                    } else {
                      console.log(`Notification créée pour l'utilisateur ${recipientId} (méthode alternative)`);
                      
                      // Invalider le cache Redis des notifications
                      await redis.del(`notifications:${recipientId}:false:1:::`);
                      await redis.del(`notifications:unread:${recipientId}`);
                    }
                  } else {
                    console.log(`Notification créée pour l'utilisateur ${recipientId}:`, notifData);
                    
                    // Invalider le cache Redis des notifications
                    await redis.del(`notifications:${recipientId}:false:1:::`);
                    await redis.del(`notifications:unread:${recipientId}`);
                  }
                } catch (notifError) {
                  console.error('Erreur lors de la création de la notification:', notifError);
                  // On continue même si la création de notification échoue
                }
              }
            }
          } catch (error) {
            console.error('Erreur lors de la vérification différée des notifications:', error);
          }
        }, 600000); // 10 minutes en millisecondes
      }

      // Get conversation with all data to determine which counter to increment
      const { data: conversation } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();
        
      // Update the conversation last activity
      if (conversation) {
        await supabase
          .from('user_messages_conversations')
          .update({ 
            updated_at: new Date().toISOString(),
            last_message_id: message.id,
            total_messages: conversation.total_messages + 1,
            // Incrémenter le compteur non lu pour le destinataire
            ...(recipientId === conversation.user1_id 
              ? { unread_count_user1: conversation.unread_count_user1 + 1 } 
              : { unread_count_user2: conversation.unread_count_user2 + 1 })
          })
          .eq('id', conversationId);
      }

      // Émettre l'événement de nouveau message
      try {
        console.log(`Émission de l'événement 'new_message' vers user_${recipientId}`, {
          conversation_id: conversationId,
          message_id: message.id,
          sender_id: senderId,
          attachments: processedAttachments // Ajouter les pièces jointes
        });
        
        // Notifier le destinataire du message
        io.to(`user_${recipientId}`).emit('new_message', {
          conversation_id: conversationId,
          message_id: message.id,
          sender_id: senderId,
          content: content,
          attachments: processedAttachments // Ajouter les pièces jointes
        });
        
        // Notifier également l'expéditeur pour mise à jour de son interface
        io.to(`user_${senderId}`).emit('new_message', {
          conversation_id: conversationId,
          message_id: message.id,
          sender_id: senderId,
          content: content,
          attachments: processedAttachments // Ajouter les pièces jointes
        });
      } catch (socketError) {
        console.error('Erreur lors de l\'émission de l\'événement socket:', socketError);
        // On continue même si l'émission socket échoue
      }

      return {
        ...message,
        attachments: processedAttachments
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  private async cleanupOldConversations(userId: string): Promise<void> {
    try {
      // Compter le nombre total de conversations de l'utilisateur
      const { data, count } = await supabase
        .from('user_messages_conversations')
        .select('*', { count: 'exact' })
        .or(`user1_id.eq.${userId},user2_id.eq.${userId}`);

      if (count && count > 50) {
        // Récupérer toutes les conversations où les deux utilisateurs ont masqué la conversation
        const { data: oldConversations } = await supabase
          .from('user_messages_conversations')
          .select('*')
          .or(`user1_id.eq.${userId},user2_id.eq.${userId}`)
          .eq('user1_has_deleted', true)
          .eq('user2_has_deleted', true)
          .order('updated_at', { ascending: true });

        if (oldConversations && oldConversations.length > 0) {
          // Supprimer la plus ancienne conversation
          await supabase
            .from('user_messages_conversations')
            .delete()
            .eq('id', oldConversations[0].id);

          // Supprimer les messages associés
          await supabase
            .from('user_messages')
            .delete()
            .eq('conversation_id', oldConversations[0].id);
        }
      }
    } catch (error) {
      console.error('Erreur lors du nettoyage des anciennes conversations:', error);
    }
  }

  // Create a new conversation
  async createConversation({ creatorId, participantId, initialMessage, attachments = [] }: CreateConversationParams): Promise<Conversation> {
    try {
      // Vérifier si l'utilisateur peut créer une nouvelle conversation
      const { canCreate, count, limit } = await this.canUserCreateNewConversation(creatorId);
      
      if (!canCreate) {
        throw new Error(`Vous avez atteint votre limite de ${limit} nouvelles conversations ce mois-ci (${count}/${limit}). Veuillez mettre à niveau votre abonnement ou attendre le mois prochain.`);
      }
      
      // Vérifier que l'utilisateur n'essaie pas de créer une conversation avec lui-même
      if (creatorId === participantId) {
        throw new Error("Vous ne pouvez pas créer une conversation avec vous-même.");
      }

      // Nettoyer les anciennes conversations avant d'en créer une nouvelle
      await this.cleanupOldConversations(creatorId);

      // Validation des paramètres
      if (!creatorId) throw new Error('creatorId est requis');
      if (!participantId) throw new Error('participantId est requis');
      if (!initialMessage) throw new Error('initialMessage est requis');
      
      console.log('Création de conversation avec les paramètres:', { 
        creatorId, 
        participantId, 
        messageLength: initialMessage?.length,
        hasAttachments: attachments?.length > 0
      });
      
      // Get both users details using dbService
      const creator = creatorId && creatorId !== 'null' && typeof creatorId === 'string' 
        ? await dbService.getUserById(creatorId) 
        : null;
      if (!creator) {
        console.error('Creator not found:', creatorId);
        throw new Error(`Creator user (${creatorId}) does not exist`);
      }
        
      const participant = participantId && participantId !== 'null' && typeof participantId === 'string' 
        ? await dbService.getUserById(participantId) 
        : null;
      if (!participant) {
        console.error('Participant not found:', participantId);
        throw new Error(`Participant user (${participantId}) does not exist`);
      }
        
      // Format user data to maintain compatibility
      const formattedCreator = {
        id: creator.id,
        email: creator.email,
        avatar_url: creator.profil?.data?.photo_url || null,
        notification_preferences: creator.notification_preferences,
        first_name: creator.profil?.data?.prenom || null,
        last_name: creator.profil?.data?.nom || null
      };
        
      const formattedParticipant = {
        id: participant.id,
        email: participant.email,
        avatar_url: participant.profil?.data?.photo_url || null,
        notification_preferences: participant.notification_preferences,
        first_name: participant.profil?.data?.prenom || null,
        last_name: participant.profil?.data?.nom || null
      };
      
      // Vérifier si une conversation existe déjà entre ces utilisateurs
      const { data: existingConversation, error: checkError } = await supabase
        .from('user_messages_conversations')
        .select('id, user1_has_blocked, user2_has_blocked')
        .or(`and(user1_id.eq.${creatorId},user2_id.eq.${participantId}),and(user1_id.eq.${participantId},user2_id.eq.${creatorId})`)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null)
        .maybeSingle();
        
      if (checkError) {
        console.error('Erreur lors de la vérification des conversations existantes:', checkError);
      } else if (existingConversation) {
        console.log('Une conversation existe déjà entre ces utilisateurs:', existingConversation.id);
        
        // Vérifier si la conversation est bloquée
        if (existingConversation.user1_has_blocked || existingConversation.user2_has_blocked) {
          throw new Error('Cette conversation est bloquée. Vous ne pouvez pas envoyer de messages.');
        }
        
        // Au lieu de créer une nouvelle conversation, utiliser celle qui existe
        // et ajouter un nouveau message
        return this.addMessageToExistingConversation({
          conversationId: existingConversation.id,
          senderId: creatorId,
          receiverId: participantId,
          content: initialMessage,
          attachments
        });
      }
      
      // Create the conversation
      console.log('Tentative de création de conversation avec les données:', {
        user1_id: creatorId,
        user2_id: participantId,
        updated_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      });
      
      // Créer la conversation
      const { data: conversation, error } = await supabase
        .from('user_messages_conversations')
        .insert({
          user1_id: creatorId,
          user2_id: participantId,
          updated_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          total_messages: 0,
          unread_count_user1: 0,
          unread_count_user2: 0
        })
        .select(`*`)
        .single();

      if (error) {
        console.error('Erreur lors de la création de la conversation:', error);
        // Analyser l'erreur Supabase pour obtenir des détails supplémentaires
        const errorDetails = {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        };
        console.error('Détails de l\'erreur Supabase:', errorDetails);
        
        throw new Error(`Failed to create conversation: ${error.message}`);
      }
      
      if (!conversation) {
        console.error('Conversation créée mais aucune donnée retournée');
        throw new Error('Failed to create conversation: no data returned');
      }
      
      console.log('Conversation créée avec succès, ID:', conversation.id);

      // Process attachments if any
      const processedAttachments = attachments.length > 0 
        ? await this.processAttachments(attachments, creatorId, conversation.id) 
        : [];

      
      const infoMessage = "⚠️ Attention : Cette conversation est limitée à 150 messages. Les messages les plus anciens seront automatiquement supprimés.";
    
      await supabase
        .from('user_messages')
        .insert({
          conversation_id: conversation.id,
          sender_id: creatorId,
          receiver_id: participantId,
          content: infoMessage,
          has_attachment: processedAttachments.length > 0
        });
        
        // Add the initial message
      const { data: message, error: messageError } = await supabase
        .from('user_messages')
        .insert({
          conversation_id: conversation.id,
          sender_id: creatorId,
          receiver_id: participantId,
          content: initialMessage,
          has_attachment: processedAttachments.length > 0
        })
        .select(`*`)
        .single();

      if (messageError) {
        console.error('Erreur lors de la création du message initial:', messageError);
        throw new Error(`Failed to create initial message: ${messageError.message}`);
      }
      
      if (!message) {
        console.error('Message créé mais aucune donnée retournée');
        throw new Error('Failed to create initial message: no data returned');
      }
      
      console.log('Message initial créé avec succès, ID:', message.id);

      // Mettre à jour la conversation avec l'ID du dernier message
      await supabase
        .from('user_messages_conversations')
        .update({
          last_message_id: message.id,
          total_messages: 1,
          unread_count_user2: 1 // Seul le destinataire a un message non lu
        })
        .eq('id', conversation.id);

      // Insert attachments if any
      if (processedAttachments.length > 0) {
        for (const attachment of processedAttachments) {
          await supabase
            .from('user_message_attachments')
            .insert({
              message_id: message.id,
              file_name: attachment.file_name,
              file_size: attachment.file_size,
              mime_type: attachment.mime_type,
              file_path: attachment.file_path,
              storage_path: attachment.storage_path,
              public_url: attachment.public_url,
              expires_at: attachment.expires_at
            });
        }
      }

      // Notify the participant about the new conversation via email
      if (participant && participant.email) {
        // Si les préférences de notification ne sont pas définies, on considère que l'utilisateur veut recevoir les notifications
        const shouldSendNotification = participant.notification_preferences?.messages !== false;
        
        if (shouldSendNotification) {
          const conversationUrl = `${process.env.FRONTEND_URL}/dashboard/messages/${conversation.id}`;
          const creatorName = creator.profil?.data?.prenom && creator.profil?.data?.nom
            ? `${creator.profil.data.prenom} ${creator.profil.data.nom.charAt(0).toUpperCase() + '.'}`
            : creator.email || `Utilisateur ${creator.id.substring(0, 8)}`;
          
          await sendNewConversationEmail(participant.email, { senderName: creatorName, conversationUrl });
          
          // Créer une notification dans le tableau de bord du destinataire
          try {
            const { data: notifData, error: notifError } = await supabase
              .from('user_notifications')
              .insert({
                user_id: participantId,
                type: 'message',
                title: `Nouvelle conversation de ${creatorName}`,
                content: initialMessage.length > 100 ? `${initialMessage.substring(0, 100)}...` : initialMessage,
                is_read: false,
                is_archived: false,
                link: `/dashboard/messages/${conversation.id}`
              })
              .select();
              
            if (notifError) {
              console.error('Erreur lors de la création de la notification:', notifError);
              
              // Si l'insertion échoue, tenter une approche alternative
              const { error: directError } = await supabase
                .from('user_notifications')
                .insert({
                  user_id: participantId,
                  type: 'message',
                  title: `Nouvelle conversation de ${creatorName}`,
                  content: initialMessage.length > 100 ? `${initialMessage.substring(0, 100)}...` : initialMessage,
                  is_read: false,
                  is_archived: false,
                  link: `/dashboard/messages/${conversation.id}`
                });
                
              if (directError) {
                console.error('Échec de l\'insertion directe de la notification:', directError);
              } else {
                console.log(`Notification de nouvelle conversation créée pour l'utilisateur ${participantId} (méthode alternative)`);
                
                // Invalider le cache Redis des notifications
                await redis.del(`notifications:${participantId}:false:1:::`);
                await redis.del(`notifications:unread:${participantId}`);
              }
            } else {
              console.log(`Notification de nouvelle conversation créée pour l'utilisateur ${participantId}:`, notifData);
              
              // Invalider le cache Redis des notifications
              await redis.del(`notifications:${participantId}:false:1:::`);
              await redis.del(`notifications:unread:${participantId}`);
            }
          } catch (notifError) {
            console.error('Erreur lors de la création de la notification de conversation:', notifError);
            // On continue même si la création de notification échoue
          }
        }
      }

      // Émettre l'événement de nouvelle conversation aux deux participants
      try {
        // Préparer les données utilisateur formatées
        const formattedCreatorData = {
          id: creator.id,
          email: creator.email,
          avatar_url: creator.profil?.data?.photo_url || null,
          prenom: creator.profil?.data?.prenom || null,
          nom: creator.profil?.data?.nom || null,
          first_name: creator.profil?.data?.prenom || null,
          last_name: creator.profil?.data?.nom || null
        };

        const formattedParticipantData = {
          id: participant.id,
          email: participant.email,
          avatar_url: participant.profil?.data?.photo_url || null,
          prenom: participant.profil?.data?.prenom || null,
          nom: participant.profil?.data?.nom || null,
          first_name: participant.profil?.data?.prenom || null,
          last_name: participant.profil?.data?.nom || null
        };

        // Préparer les données de conversation enrichies
        const conversationData = {
          id: conversation.id,
          user1_id: creatorId,
          user2_id: participantId,
          user1: formattedCreatorData,
          user2: formattedParticipantData,
          participants: [creatorId, participantId],
          creator_id: creatorId,
          last_message: {
            id: message.id,
            content: initialMessage,
            created_at: message.created_at,
            sender_id: creatorId
          },
          last_message_preview: initialMessage.substring(0, 50) + (initialMessage.length > 50 ? '...' : ''),
          created_at: conversation.created_at,
          updated_at: conversation.updated_at,
          last_activity: conversation.updated_at,
          unread_count: 1,
          is_blocked: false,
          user1_has_blocked: false,
          user2_has_blocked: false,
          user1_has_deleted: false,
          user2_has_deleted: false,
          total_messages: 1
        };

        // Notifier le destinataire avec les données adaptées
        io.to(`user_${participantId}`).emit('new_conversation', {
          ...conversationData,
          otherUser: formattedCreatorData // Pour le destinataire, l'autre utilisateur est le créateur
        });
        
        // Notifier également le créateur avec les données adaptées
        io.to(`user_${creatorId}`).emit('new_conversation', {
          ...conversationData,
          otherUser: formattedParticipantData // Pour le créateur, l'autre utilisateur est le participant
        });
        
        console.log('Événement new_conversation émis avec succès aux utilisateurs:', {
          creator: creatorId,
          participant: participantId
        });
      } catch (socketError) {
        console.error('Erreur lors de l\'émission de l\'événement new_conversation:', socketError);
        // On continue même si l'émission socket échoue
      }

      // Format the response to match the expected structure
      return { 
        id: conversation.id,
        user1_id: creatorId,
        user2_id: participantId,
        participants: [creatorId, participantId],
        creator_id: creatorId,
        last_activity: conversation.updated_at,
        updated_at: conversation.updated_at,
        last_message_preview: initialMessage.substring(0, 50) + (initialMessage.length > 50 ? '...' : ''),
        created_at: conversation.created_at,
        unread_count: 0,
        is_blocked: false,
        user1_has_blocked: false,
        user2_has_blocked: false,
        user1_has_deleted: false,
        user2_has_deleted: false,
        messages: [{
          id: message.id,
          conversation_id: conversation.id,
          sender_id: creatorId,
          content: initialMessage,
          created_at: message.created_at,
          attachments: processedAttachments
        }]
      };
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  // Nouvelle méthode pour ajouter un message à une conversation existante
  private async addMessageToExistingConversation({ 
    conversationId, 
    senderId, 
    receiverId, 
    content,
    attachments = [] 
  }: {
    conversationId: string;
    senderId: string;
    receiverId: string;
    content: string;
    attachments?: UploadedFile[];
  }): Promise<Conversation> {
    try {
      // Vérifier si la conversation existe et si elle est bloquée
      const { data: conversation, error: convFetchError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (convFetchError || !conversation) {
        throw new Error(`Conversation non trouvée: ${convFetchError?.message || 'Non trouvée'}`);
      }

      // Vérifier si la conversation est bloquée par l'un des participants
      if (conversation.user1_has_blocked || conversation.user2_has_blocked) {
        throw new Error('Cette conversation est bloquée. Vous ne pouvez pas envoyer de messages.');
      }

      // Process attachments if any
      const processedAttachments = attachments.length > 0 
        ? await this.processAttachments(attachments, senderId, conversationId) 
        : [];

      // Add the message
      const { data: message, error: messageError } = await supabase
        .from('user_messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          receiver_id: receiverId,
          content: content,
          has_attachment: processedAttachments.length > 0
        })
        .select(`*`)
        .single();

      if (messageError) {
        throw new Error(`Failed to add message to existing conversation: ${messageError.message}`);
      }

      // Get the conversation
      const { data: updatedConversation, error: convError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (convError) {
        throw new Error(`Failed to get conversation: ${convError.message}`);
      }

      // Update the conversation
      if (updatedConversation) {
        await supabase
          .from('user_messages_conversations')
          .update({
            last_message_id: message.id,
            updated_at: new Date().toISOString(),
            total_messages: updatedConversation.total_messages + 1,
            // Incrémenter le compteur non lu pour le destinataire
            ...(receiverId === updatedConversation.user1_id 
              ? { unread_count_user1: updatedConversation.unread_count_user1 + 1 } 
              : { unread_count_user2: updatedConversation.unread_count_user2 + 1 })
          })
          .eq('id', conversationId);
      }

      // Insert attachments if any
      if (processedAttachments.length > 0) {
        for (const attachment of processedAttachments) {
          await supabase
            .from('user_message_attachments')
            .insert({
              message_id: message.id,
              file_name: attachment.file_name,
              file_size: attachment.file_size,
              mime_type: attachment.mime_type,
              file_path: attachment.file_path,
              storage_path: attachment.storage_path,
              public_url: attachment.public_url,
              expires_at: attachment.expires_at
            });
        }
      }

      // Obtenir les informations sur l'expéditeur et le destinataire
      const sender = senderId && senderId !== 'null' && typeof senderId === 'string' 
        ? await dbService.getUserById(senderId) 
        : null;
      const recipient = receiverId && receiverId !== 'null' && typeof receiverId === 'string' 
        ? await dbService.getUserById(receiverId) 
        : null;
      
      // Envoyer un email de notification au destinataire
      if (recipient && recipient.email) {
        // Si les préférences de notification ne sont pas définies, on considère que l'utilisateur veut recevoir les notifications
        const shouldSendNotification = recipient.notification_preferences?.messages !== false;
        
        if (shouldSendNotification) {
          const messageUrl = `${process.env.FRONTEND_URL}/dashboard/messages/${conversationId}`;
          const senderName = sender?.profil?.data?.prenom && sender?.profil?.data?.nom
            ? `${sender.profil.data.prenom} ${sender.profil.data.nom.charAt(0).toUpperCase() + '.'}`
            : sender?.email || `Utilisateur ${senderId.substring(0, 8)}`;
            
          // Vérifier si le dernier message a été lu avant d'envoyer l'email et la notification
          const { data: lastMessages } = await supabase
            .from('user_messages')
            .select('*')
            .eq('conversation_id', conversationId)
            .eq('sender_id', senderId)
            .eq('receiver_id', receiverId)
            .order('created_at', { ascending: false })
            .limit(2);
          
          // On vérifie l'avant-dernier message s'il existe
          const previousMessage = lastMessages && lastMessages.length > 1 ? lastMessages[1] : null;
          const isFirstMessage = !previousMessage;

          // Vérifier si le message précédent a été lu et si la dernière lecture date de plus de 10 secondes
          const lastMessageWasRead = isFirstMessage || (previousMessage && previousMessage.is_read === true);
          const now = new Date().getTime();
          const lastReadTime = previousMessage?.read_at ? new Date(previousMessage.read_at).getTime() : 0;
          const timeSinceLastRead = now - lastReadTime;
          const shouldNotify = isFirstMessage || !lastMessageWasRead || timeSinceLastRead > 10000;

          console.log('Vérification des conditions de notification pour l\'envoi de l\'email et la notification de nouveau message privé :', {
            lastMessageWasRead,
            isFirstMessage,
            timeSinceLastRead: Math.round(timeSinceLastRead / 1000) + ' secondes',
            shouldNotify
          });
          
          if (shouldNotify) {
            // Envoyer l'email
            await sendNewMessageEmail(recipient.email, { senderName, messageUrl });
            
            // Créer une notification dans le tableau de bord
            try {
              const { data: notifData, error: notifError } = await supabase
                .from('user_notifications')
                .insert({
                  user_id: receiverId,
                  type: 'message',
                  title: `Nouveau message de ${senderName}`,
                  content: content.length > 100 ? `${content.substring(0, 100)}...` : content,
                  is_read: false,
                  is_archived: false,
                  link: `/dashboard/messages/${conversationId}`
                })
                .select();
                
              if (notifError) {
                console.error('Erreur lors de la création de la notification:', notifError);
                
                // Si l'insertion échoue, tenter une approche alternative
                const { error: directError } = await supabase
                  .from('user_notifications')
                  .insert({
                    user_id: receiverId,
                    type: 'message',
                    title: `Nouveau message de ${senderName}`,
                    content: content.length > 100 ? `${content.substring(0, 100)}...` : content,
                    is_read: false,
                    is_archived: false,
                    link: `/dashboard/messages/${conversationId}`
                  });
                  
                if (directError) {
                  console.error('Échec de l\'insertion directe de la notification:', directError);
                } else {
                  console.log(`Notification créée pour l'utilisateur ${receiverId} (méthode alternative)`);
                  
                  // Invalider le cache Redis des notifications
                  await redis.del(`notifications:${receiverId}:false:1:::`);
                  await redis.del(`notifications:unread:${receiverId}`);
                }
              } else {
                console.log(`Notification créée pour l'utilisateur ${receiverId}:`, notifData);
                
                // Invalider le cache Redis des notifications
                await redis.del(`notifications:${receiverId}:false:1:::`);
                await redis.del(`notifications:unread:${receiverId}`);
              }
            } catch (notifError) {
              console.error('Erreur lors de la création de la notification:', notifError);
              // On continue même si la création de notification échoue
            }
          } else {
            console.log(`Pas d'email ni de notification envoyés car le dernier message n'a pas été lu.`);
          }
        }
      }

      // Format the response to match the expected structure
      return {
        id: updatedConversation.id,
        user1_id: updatedConversation.user1_id,
        user2_id: updatedConversation.user2_id,
        participants: [updatedConversation.user1_id, updatedConversation.user2_id],
        creator_id: updatedConversation.user1_id,
        last_activity: updatedConversation.updated_at,
        updated_at: updatedConversation.updated_at,
        last_message_preview: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
        created_at: updatedConversation.created_at,
        unread_count: updatedConversation.unread_count_user1 || updatedConversation.unread_count_user2 || 0,
        is_blocked: updatedConversation.user1_has_blocked || updatedConversation.user2_has_blocked || false,
        user1_has_blocked: updatedConversation.user1_has_blocked || false,
        user2_has_blocked: updatedConversation.user2_has_blocked || false,
        user1_has_deleted: updatedConversation.user1_has_deleted || false,
        user2_has_deleted: updatedConversation.user2_has_deleted || false,
        messages: [{
          id: message.id,
          conversation_id: updatedConversation.id,
          sender_id: senderId,
          content: content,
          created_at: message.created_at,
          attachments: processedAttachments
        }]
      };
    } catch (error) {
      console.error('Error adding message to existing conversation:', error);
      throw error;
    }
  }

  // Toggle block status of a conversation
  async toggleBlock({ userId, conversationId, isBlocked }: ToggleBlockParams): Promise<ServiceResult<void>> {
    try {
      // Récupérer d'abord la conversation pour déterminer quel utilisateur bloque
      const { data: conversation, error: fetchError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (fetchError || !conversation) {
        throw new Error(`Conversation not found: ${fetchError?.message || 'Not found'}`);
      }

      // Vérifier que l'utilisateur est bien participant à la conversation
      if (conversation.user1_id !== userId && conversation.user2_id !== userId) {
        throw new Error('User is not a participant in this conversation');
      }

      // Déterminer quel champ mettre à jour en fonction de l'utilisateur
      const isUser1 = conversation.user1_id === userId;
      const updateData = isUser1 
        ? { user1_has_blocked: isBlocked }
        : { user2_has_blocked: isBlocked };

      // Mettre à jour le statut de blocage dans la conversation
      const { error: updateError } = await supabase
        .from('user_messages_conversations')
        .update(updateData)
        .eq('id', conversationId);

      if (updateError) {
        throw new Error(`Failed to update block status: ${updateError.message}`);
      }

      // Déterminer l'ID de l'autre utilisateur pour la notification socket
      const otherUserId = isUser1 ? conversation.user2_id : conversation.user1_id;
      
      try {
        console.log(`Émission de l'événement 'conversation_blocked' vers user_${otherUserId}`, {
          conversation_id: conversationId,
          is_blocked: isBlocked,
          blocker_id: userId
        });
        
        // Émettre l'événement de blocage/déblocage à l'autre utilisateur
        io.to(`user_${otherUserId}`).emit('conversation_blocked', {
          conversation_id: conversationId,
          is_blocked: isBlocked,
          blocker_id: userId
        });

        // Émettre également à l'utilisateur qui bloque pour uniformité
        io.to(`user_${userId}`).emit('conversation_blocked', {
          conversation_id: conversationId,
          is_blocked: isBlocked,
          blocker_id: userId
        });
      } catch (socketError) {
        console.error('Erreur lors de l\'émission de l\'événement socket conversation_blocked:', socketError);
        // On continue même si l'émission socket échoue
      }

      return { success: true };
    } catch (error) {
      console.error('Error toggling block:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  // Delete a conversation
  async deleteConversation({ userId, conversationId }: DeleteConversationParams): Promise<ServiceResult<void>> {
    try {
      // First check if user is a participant
      const { data: conversation, error: fetchError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (fetchError || !conversation) {
        throw new Error(`Conversation not found: ${fetchError?.message || 'Not found'}`);
      }

      // Validate the user is a participant
      if (conversation.user1_id !== userId && conversation.user2_id !== userId) {
        throw new Error('User is not a participant in this conversation');
      }

      // Determine which user is deleting the conversation
      const isUser1 = conversation.user1_id === userId;
      const updateData = isUser1 
        ? { user1_has_deleted: true }
        : { user2_has_deleted: true };

      // Update the conversation to mark it as deleted for this user
      const { error: updateError } = await supabase
        .from('user_messages_conversations')
        .update(updateData)
        .eq('id', conversationId);

      if (updateError) {
        throw new Error(`Erreur lors du masquage de la conversation: ${updateError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error('Erreur lors du masquage de la conversation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Une erreur inconnue est survenue'
      };
    }
  }

  // Démasquer une conversation précédemment masquée
  async unhideConversation({ userId, conversationId }: DeleteConversationParams): Promise<ServiceResult<void>> {
    try {
      // Vérifier d'abord si l'utilisateur est un participant
      const { data: conversation, error: fetchError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (fetchError || !conversation) {
        throw new Error(`Conversation non trouvée: ${fetchError?.message || 'Non trouvée'}`);
      }

      // Vérifier que l'utilisateur est bien participant à la conversation
      if (conversation.user1_id !== userId && conversation.user2_id !== userId) {
        throw new Error('L\'utilisateur n\'est pas un participant de cette conversation');
      }

      // Déterminer quel utilisateur démasque la conversation
      const isUser1 = conversation.user1_id === userId;
      const updateData = isUser1 
        ? { user1_has_deleted: false }
        : { user2_has_deleted: false };

      // Mettre à jour la conversation pour la rendre visible à nouveau pour cet utilisateur
      const { error: updateError } = await supabase
        .from('user_messages_conversations')
        .update(updateData)
        .eq('id', conversationId);

      if (updateError) {
        throw new Error(`Erreur lors du démasquage de la conversation: ${updateError.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error('Erreur lors du démasquage de la conversation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Une erreur inconnue est survenue'
      };
    }
  }

  // Marquer tous les messages d'une conversation comme lus
  async markMessagesAsRead({ userId, conversationId }: MarkMessagesAsReadParams): Promise<ServiceResult<void>> {
    try {
      // Vérifier d'abord que l'utilisateur est participant à la conversation
      const { data: conversation, error: fetchError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null)
        .single();

      if (fetchError || !conversation) {
        return {
          success: false,
          error: `Conversation not found: ${fetchError?.message || 'Not found'}`
        };
      }

      // Vérifier que l'utilisateur est un participant de la conversation
      if (!conversation.user1_id.includes(userId) && !conversation.user2_id.includes(userId)) {
        return {
          success: false,
          error: 'User is not a participant in this conversation'
        };
      }

      // Marquer tous les messages non envoyés par l'utilisateur comme lus
      const { data: updatedMessages, error: updateError } = await supabase
        .from('user_messages')
        .update({ 
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('conversation_id', conversationId)
        .neq('sender_id', userId)
        .eq('is_read', false)
        // Exclure les messages d'utilisateurs anonymisés (RGPD)
        .not('sender_id', 'is', null)
        .not('receiver_id', 'is', null)
        .select('id, read_at');

      if (updateError) {
        return {
          success: false,
          error: `Failed to mark messages as read: ${updateError.message}`
        };
      }

      // Réinitialiser le compteur de messages non lus pour l'utilisateur
      const isUser1 = conversation.user1_id === userId;
      const { error: counterError } = await supabase
        .from('user_messages_conversations')
        .update({
          ...(isUser1 ? { unread_count_user1: 0 } : { unread_count_user2: 0 })
        })
        .eq('id', conversationId);

      if (counterError) {
        console.error('Failed to reset unread counter:', counterError);
        // Ne pas échouer complètement si seul le compteur n'a pas été mis à jour
      }

      // Émettre l'événement message_read avec les IDs des messages et leur date de lecture
      try {
        // Obtenir l'ID de l'autre utilisateur
        const otherUserId = conversation.user1_id === userId ? conversation.user2_id : conversation.user1_id;
        
        const messageData = updatedMessages?.map(msg => ({
          id: msg.id,
          read_at: msg.read_at
        })) || [];

        // Émettre l'événement aux deux utilisateurs
        io.to(`user_${userId}`).emit('message_read', {
          conversation_id: conversationId,
          messages: messageData
        });

        io.to(`user_${otherUserId}`).emit('message_read', {
          conversation_id: conversationId,
          messages: messageData
        });
      } catch (socketError) {
        console.error('Erreur lors de l\'émission de l\'événement socket message_read:', socketError);
      }

      return { success: true };
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  // Get a specific conversation by ID
  async getConversationById(conversationId: string, userId: string): Promise<ServiceResult<Conversation>> {
    try {
      // 1. Get the conversation
      const { data: conversationData, error } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null)
        .single();

      if (error) {
        console.error('Error getting conversation:', error);
        return { success: false, error: `Failed to get conversation: ${error.message}` };
      }

      // 2. Check if user is part of the conversation
      if (conversationData.user1_id !== userId && conversationData.user2_id !== userId) {
        return { success: false, error: 'You do not have access to this conversation' };
      }

      // 3. Get user information for both participants using dbService
      const user1 = conversationData.user1_id && conversationData.user1_id !== 'null' && typeof conversationData.user1_id === 'string' 
        ? await dbService.getUserById(conversationData.user1_id) 
        : null;
      if (!user1) {
        console.error('Error getting user1 data');
        return { success: false, error: 'Failed to get user data for user1' };
      }
        
      const user2 = conversationData.user2_id && conversationData.user2_id !== 'null' && typeof conversationData.user2_id === 'string' 
        ? await dbService.getUserById(conversationData.user2_id) 
        : null;
      if (!user2) {
        console.error('Error getting user2 data');
        return { success: false, error: 'Failed to get user data for user2' };
      }
      
      // Format user data to maintain compatibility
      const formattedUser1 = {
        id: user1.id,
        email: user1.email,
        avatar_url: user1.profil?.data?.photo_url || null,
        notification_preferences: user1.notification_preferences,
        first_name: user1.profil?.data?.prenom || null,
        last_name: user1.profil?.data?.nom || null
      };
      
      const formattedUser2 = {
        id: user2.id,
        email: user2.email,
        avatar_url: user2.profil?.data?.photo_url || null,
        notification_preferences: user2.notification_preferences,
        first_name: user2.profil?.data?.prenom || null,
        last_name: user2.profil?.data?.nom || null
      };

      // 4. Get last message if it exists
      const { data: lastMessageData } = await supabase
        .from('user_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      const otherUserId = conversationData.user1_id === userId 
        ? conversationData.user2_id 
        : conversationData.user1_id;

      // 5. Format de la conversation selon l'interface définie dans ce fichier
      const conversation: Conversation = {
        id: conversationData.id,
        user1_id: conversationData.user1_id,
        user2_id: conversationData.user2_id,
        participants: [userId, otherUserId],
        creator_id: conversationData.creator_id || userId,
        last_activity: conversationData.updated_at || conversationData.created_at,
        updated_at: conversationData.updated_at,
        last_message_preview: lastMessageData?.content || "",
        created_at: conversationData.created_at,
        unread_count: userId === conversationData.user1_id ? conversationData.unread_count_user1 || 0 : conversationData.unread_count_user2 || 0,
        is_blocked: (userId === conversationData.user1_id && conversationData.user1_has_blocked) || 
                   (userId === conversationData.user2_id && conversationData.user2_has_blocked) || false,
        user1_has_blocked: conversationData.user1_has_blocked || false,
        user2_has_blocked: conversationData.user2_has_blocked || false,
        user1_has_deleted: conversationData.user1_has_deleted || false,
        user2_has_deleted: conversationData.user2_has_deleted || false,
        isBlocked: (userId === conversationData.user1_id && conversationData.user1_has_blocked) || 
                  (userId === conversationData.user2_id && conversationData.user2_has_blocked) || false,
        // Ajouter les informations complètes des utilisateurs
        user1: {
          ...formattedUser1,
          prenom: user1.profil?.data?.prenom || null,
          nom: user1.profil?.data?.nom || null,
          date_inscription: user1.date_inscription,
          profil: {
            type: user1.profil?.data?.type_de_profil || 'particulier'
          }
        },
        user2: {
          ...formattedUser2,
          prenom: user2.profil?.data?.prenom || null,
          nom: user2.profil?.data?.nom || null,
          date_inscription: user2.date_inscription,
          profil: {
            type: user2.profil?.data?.type_de_profil || 'particulier'
          }
        },
        // Ajouter l'autre utilisateur pour la rétrocompatibilité
        otherUser: userId === conversationData.user1_id ? {
          ...formattedUser2,
          prenom: user2.profil?.data?.prenom || null,
          nom: user2.profil?.data?.nom || null,
          date_inscription: user2.date_inscription,
          profil: {
            type: user2.profil?.data?.type_de_profil || 'particulier'
          }
        } : {
          ...formattedUser1,
          prenom: user1.profil?.data?.prenom || null,
          nom: user1.profil?.data?.nom || null,
          date_inscription: user1.date_inscription,
          profil: {
            type: user1.profil?.data?.type_de_profil || 'particulier'
          }
        }
      };

      // Ajouter les messages si disponibles
      if (lastMessageData) {
        conversation.messages = [{
          id: lastMessageData.id,
          conversation_id: lastMessageData.conversation_id,
          sender_id: lastMessageData.sender_id,
          content: lastMessageData.content,
          created_at: lastMessageData.created_at,
          attachments: [],
          sender: lastMessageData.sender_id === conversationData.user1_id ? formattedUser1 : formattedUser2
        }];
      }

      return { success: true, data: conversation };
    } catch (error) {
      console.error('Error in getConversationById:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  // Marquer une conversation comme non lue
  async markConversationAsUnread({ userId, conversationId }: MarkConversationAsUnreadParams): Promise<ServiceResult<void>> {
    try {
      // Vérifier si l'utilisateur a accès à la conversation
      const { data: conversation, error: conversationError } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .or(`user1_id.eq.${userId},user2_id.eq.${userId}`)
        .single();

      if (conversationError || !conversation) {
        return {
          success: false,
          message: "Conversation non trouvée ou accès non autorisé."
        };
      }

      // Déterminer quel champ mettre à jour en fonction de l'utilisateur
      const unreadField = conversation.user1_id === userId ? 'unread_count_user1' : 'unread_count_user2';

      // Mettre à jour le compteur de messages non lus
      const { error: updateError } = await supabase
        .from('user_messages_conversations')
        .update({ [unreadField]: 1 })
        .eq('id', conversationId);

      if (updateError) {
        throw updateError;
      }

      return {
        success: true,
        message: "Conversation marquée comme non lue."
      };
    } catch (error) {
      console.error('Erreur lors du marquage de la conversation comme non lue:', error);
      return {
        success: false,
        message: "Une erreur est survenue lors du marquage de la conversation comme non lue."
      };
    }
  }

  // Delete a message
  async deleteMessage({ userId, conversationId, messageId }: DeleteMessageParams): Promise<ServiceResult<void>> {
    try {
      // Vérifier si le message existe et appartient à l'utilisateur
      const { data: message, error: messageError } = await supabase
        .from('user_messages')
        .select('*')
        .eq('id', messageId)
        .eq('conversation_id', conversationId)
        .single();

      if (messageError || !message) {
        return {
          success: false,
          error: 'Message non trouvé'
        };
      }

      // Vérifier si l'utilisateur est l'expéditeur du message
      if (message.sender_id !== userId) {
        return {
          success: false,
          error: 'Vous ne pouvez supprimer que vos propres messages'
        };
      }

      // Vérifier si le message a été envoyé il y a moins de 2 minutes
      const messageTimestamp = new Date(message.created_at).getTime();
      const nowTimestamp = Date.now();
      const twoMinutesInMs = 2 * 60 * 1000;
      
      const isWithinTwoMinutes = (nowTimestamp - messageTimestamp) <= twoMinutesInMs;
      
      logger.info('Vérification du délai de suppression:', {
        messageDate: message.created_at,
        now: new Date().toISOString(),
        messageTimestamp,
        nowTimestamp,
        difference: nowTimestamp - messageTimestamp,
        twoMinutesInMs,
        isWithinTwoMinutes
      });

      if (!isWithinTwoMinutes) {
        return {
          success: false,
          error: 'Vous ne pouvez supprimer que les messages envoyés il y a moins de 2 minutes'
        };
      }

      // Récupérer les informations de l'utilisateur
      const user = userId && userId !== 'null' && typeof userId === 'string' 
        ? await dbService.getUserById(userId) 
        : null;
      if (!user || !user.profil?.data) {
        return {
          success: false,
          error: 'Informations de l\'utilisateur non trouvées'
        };
      }

      // Récupérer les pièces jointes du message pour les supprimer
      const { data: attachments, error: attachmentsError } = await supabase
        .from('user_message_attachments')
        .select('*')
        .eq('message_id', messageId);

      // Supprimer les fichiers du bucket si le message a des pièces jointes
      if (attachments && attachments.length > 0) {
        // Préparer les chemins de fichiers à supprimer
        const filePaths = attachments
          .filter(att => att.file_path)
          .map(att => att.file_path);
        
        // Supprimer les fichiers du stockage
        if (filePaths.length > 0) {
          try {
            const { error: removeError } = await supabase.storage
              .from('message_attachments')
              .remove(filePaths);
            
            if (removeError) {
              logger.error('Erreur lors de la suppression des pièces jointes du stockage:', { error: removeError, messageId, filePaths });
            } else {
              logger.info(`Pièces jointes supprimées du bucket de stockage`, { count: filePaths.length, messageId });
            }
          } catch (storageError) {
            logger.error('Exception lors de la suppression des pièces jointes du stockage:', { error: storageError, messageId });
          }
        }
        
        // Supprimer les entrées de la base de données
        const { error: deleteAttachmentsError } = await supabase
          .from('user_message_attachments')
          .delete()
          .eq('message_id', messageId);
        
        if (deleteAttachmentsError) {
          logger.error('Erreur lors de la suppression des entrées de pièces jointes:', { error: deleteAttachmentsError, messageId });
        } else {
          logger.info(`Entrées de pièces jointes supprimées de la base de données`, { count: attachments.length, messageId });
        }
      }

      // Mettre à jour le message comme supprimé
      const { data: updatedMessage, error: updateError } = await supabase
        .from('user_messages')
        .update({
          content: attachments && attachments.length > 0 
            ? `Pièce${attachments.length > 1 ? 's' : ''} jointe${attachments.length > 1 ? 's' : ''} supprimée${attachments.length > 1 ? 's' : ''} ou expirée${attachments.length > 1 ? 's' : ''}`
            : `Ce message a été supprimé par ${user.profil.data.prenom} ${user.profil.data.nom.charAt(0).toUpperCase() + '.'}`,
          is_deleted_sender: true,
          is_deleted_receiver: true
        })
        .eq('id', messageId)
        .select()
        .single();

      if (updateError) {
        logger.error('Erreur Supabase lors de la mise à jour:', { error: updateError, messageId });
        return {
          success: false,
          error: `Erreur lors de la suppression du message: ${updateError.message}`
        };
      }

      // Récupérer la conversation pour obtenir l'ID de l'autre utilisateur
      const { data: conversation } = await supabase
        .from('user_messages_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (conversation) {
        // Déterminer l'ID de l'autre utilisateur
        const otherUserId = conversation.user1_id === userId ? conversation.user2_id : conversation.user1_id;

        // Émettre l'événement de suppression aux deux utilisateurs
        try {
          const messageDeletedEvent = {
            conversation_id: conversationId,
            message_id: messageId,
            deleted_content: updatedMessage.content,
            is_deleted_attachment: attachments && attachments.length > 0 // Calculé à la volée plutôt que stocké
          };

          // Notifier l'expéditeur
          io.to(`user_${userId}`).emit('message_deleted', messageDeletedEvent);
          
          // Notifier le destinataire
          io.to(`user_${otherUserId}`).emit('message_deleted', messageDeletedEvent);

          logger.info('Événement message_deleted émis avec succès aux utilisateurs:', {
            sender: userId,
            receiver: otherUserId,
            messageId
          });
        } catch (socketError) {
          logger.error('Erreur lors de l\'émission de l\'événement socket message_deleted:', { error: socketError, messageId });
          // On continue même si l'émission socket échoue
        }
      }

      return { success: true };
    } catch (error) {
      logger.error('Erreur lors de la suppression du message:', { error, conversationId, messageId });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Une erreur inconnue est survenue'
      };
    }
  }

  // Compter le nombre de conversations d'un utilisateur
  async countUserConversations(userId: string): Promise<number> {
    try {
      // Récupérer les conversations où l'utilisateur est user1
      const { count: countUser1, error: errorUser1 } = await supabase
        .from('user_messages_conversations')
        .select('*', { count: 'exact', head: true })
        .eq('user1_id', userId)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null);
        // .eq('user1_has_deleted', false);
      
      if (errorUser1) throw errorUser1;
      
      // Récupérer les conversations où l'utilisateur est user2
      const { count: countUser2, error: errorUser2 } = await supabase
        .from('user_messages_conversations')
        .select('*', { count: 'exact', head: true })
        .eq('user2_id', userId)
        .eq('user2_has_deleted', false)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null);
      
      if (errorUser2) throw errorUser2;
      
      // Retourner la somme des deux compteurs
      return (countUser1 || 0) + (countUser2 || 0);
    } catch (error) {
      console.error('Erreur lors du comptage des conversations:', error);
      throw error;
    }
  }

  // Ajout d'une fonction pour compter les conversations créées dans le mois en cours par l'utilisateur
  async countUserConversationsCreatedThisMonth(userId: string): Promise<number> {
    try {
      // Obtenir le premier jour du mois courant
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const firstDayOfMonthISO = firstDayOfMonth.toISOString();
      
      console.log(`Comptage des conversations créées par ${userId} depuis ${firstDayOfMonthISO}`);

      // Compter les conversations où l'utilisateur est user1_id (créateur) et créées ce mois-ci
      const { count, error } = await supabase
        .from('user_messages_conversations')
        .select('*', { count: 'exact', head: true })
        .eq('user1_id', userId)
        .gte('created_at', firstDayOfMonthISO)
        // Exclure les conversations avec des utilisateurs anonymisés (RGPD)
        .not('user1_id', 'is', null)
        .not('user2_id', 'is', null);
      
      if (error) {
        console.error('Erreur lors du comptage des conversations du mois:', error);
        throw error;
      }
      
      return count || 0;
    } catch (error) {
      console.error('Erreur lors du comptage des conversations du mois:', error);
      throw error;
    }
  }

  // Ajout d'une fonction pour vérifier si l'utilisateur peut créer de nouvelles conversations
  async canUserCreateNewConversation(userId: string): Promise<{ canCreate: boolean; count: number; limit: number; }> {
    try {
      // Obtenir le nombre de conversations créées ce mois-ci
      const count = await this.countUserConversationsCreatedThisMonth(userId);
      
      // Utiliser la fonction getUserSubscriptionLimits pour obtenir les limites de l'utilisateur
      const { isPremium, conversationsLimit } = await getUserSubscriptionLimits(userId);
      
      logger.info(`Vérification de la limite de conversations pour l'utilisateur ${userId}`, {
        abonnement: isPremium,
        conversationsCreees: count,
        limiteConversations: conversationsLimit
      });
      
      return {
        canCreate: count < conversationsLimit,
        count,
        limit: conversationsLimit
      };
    } catch (error) {
      logger.error('Erreur lors de la vérification de la limite de conversations:', error);
      throw error;
    }
  }

  // Obtenir le nombre total de messages non lus pour un utilisateur
  async getUnreadMessageCount(userId: string): Promise<ServiceResult<number>> {
    try {
      const { count, error } = await supabase
        .from('user_messages')
        .select('*', { count: 'exact', head: true })
        .eq('receiver_id', userId)
        .eq('is_read', false)
        // Exclure les messages d'utilisateurs anonymisés (RGPD)
        .not('sender_id', 'is', null)
        .not('receiver_id', 'is', null);

      if (error) {
        logger.error('Erreur lors de la récupération du nombre de messages non lus:', { userId, error });
        return { success: false, error: `Erreur lors de la récupération du nombre de messages non lus: ${error.message}` };
      }

      return { success: true, data: count || 0 };
    } catch (error) {
      logger.error('Exception dans getUnreadMessageCount:', { userId, error });
      return { success: false, error: error instanceof Error ? error.message : 'Une erreur inconnue est survenue' };
    }
  }

  // Modifier la méthode processNotifications pour ajouter plus de logs et de gestion d'erreurs
  private async processNotifications(): Promise<void> {
    try {
      // Récupérer toutes les notifications en attente
      const pendingNotifications = await redis.smembers('pending_notifications');
      
      if (pendingNotifications.length > 0) {
        logger.info(`Traitement de ${pendingNotifications.length} notifications en attente`);
      }
      
      // Limiter le nombre de notifications traitées à chaque cycle pour éviter de surcharger la DB
      const MAX_NOTIFICATIONS_PER_BATCH = 50;
      const notificationsToProcess = pendingNotifications.slice(0, MAX_NOTIFICATIONS_PER_BATCH);
      
      if (pendingNotifications.length > MAX_NOTIFICATIONS_PER_BATCH) {
        logger.info(`Traitement limité à ${MAX_NOTIFICATIONS_PER_BATCH} notifications sur un total de ${pendingNotifications.length}`);
      }
      
      for (const notificationKey of notificationsToProcess) {
        try {
          const notificationData = await redis.get(notificationKey);
          if (!notificationData) {
            logger.error('Notification expirée ou supprimée', { key: notificationKey });
            await redis.srem('pending_notifications', notificationKey);
            continue;
          }

          const messageData = JSON.parse(notificationData);
          const { conversationId, recipientId, senderId, senderName } = messageData;

          // Vérifier s'il y a des messages non lus dans la conversation
          const { count: unreadCount } = await supabase
            .from('user_messages')
            .select('*', { count: 'exact', head: true })
            .eq('conversation_id', conversationId)
            .eq('receiver_id', recipientId)
            .eq('is_read', false);

          // Récupérer la dernière notification envoyée
          const { data: lastNotification } = await supabase
            .from('user_notifications')
            .select('created_at')
            .eq('user_id', recipientId)
            .eq('type', 'message')
            .eq('conversation_id', conversationId)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          const timeSinceLastNotif = lastNotification
            ? Date.now() - new Date(lastNotification.created_at).getTime()
            : Infinity;
          const shouldNotifyDueToTime = timeSinceLastNotif > (12 * 60 * 60 * 1000);

          logger.error('Analyse de la notification', {
            key: notificationKey,
            unreadCount,
            timeSinceLastNotif: Math.round(timeSinceLastNotif / (60 * 60 * 1000)) + ' heures',
            shouldNotifyDueToTime
          });

          if (shouldNotifyDueToTime) {
            // Récupérer le destinataire
            const recipient = await dbService.getUserById(recipientId);

            if (recipient?.notification_preferences?.messages !== false) {
              logger.info('Envoi de la notification', {
                recipientId,
                conversationId,
                unreadCount
              });

              const messageUrl = `${process.env.FRONTEND_URL}/dashboard/messages/${conversationId}`;

              // Envoyer l'email
              await sendNewMessageEmail(recipient.email, { senderName, messageUrl });

              // Vérifier s'il existe une notification récente (moins de 4h) du même expéditeur
              const { data: recentNotifications } = await supabase
                .from('user_notifications')
                .select('id, created_at, content')
                .eq('user_id', recipientId)
                .eq('type', 'message')
                .eq('title', `Nouveau message de ${senderName}`)
                .gte('created_at', new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString())
                .order('created_at', { ascending: false })
                .limit(1);

              // Si une notification récente existe, la mettre à jour au lieu d'en créer une nouvelle
              if (recentNotifications && recentNotifications.length > 0) {
                const notification = recentNotifications[0];
                logger.info('Mise à jour d\'une notification existante', {
                  recipientId,
                  conversationId,
                  notificationId: notification.id
                });

                try {
                  const { error: updateError } = await supabase
                    .from('user_notifications')
                    .update({
                      content: `Plusieurs nouveaux messages dans la conversation`,
                      is_read: false,
                      updated_at: new Date().toISOString()
                    })
                    .eq('id', notification.id);

                  if (updateError) {
                    logger.error('Erreur lors de la mise à jour de la notification:', {
                      error: updateError,
                      notificationId: notification.id
                    });
                  } else {
                    // Invalider le cache Redis des notifications
                    await redis.del(`notifications:${recipientId}:false:1:::`);
                    await redis.del(`notifications:unread:${recipientId}`);
                  }
                } catch (updateError) {
                  logger.error('Exception lors de la mise à jour de la notification:', {
                    error: updateError,
                    recipientId,
                    conversationId
                  });
                }
              } else {
                // Créer la notification dans le tableau de bord
                try {
                  const { error: notifError } = await supabase
                    .from('user_notifications')
                    .insert({
                      user_id: recipientId,
                      type: 'message',
                      title: `Nouveau message de ${senderName}`,
                      content: messageData.content.length > 100 
                        ? `${messageData.content.substring(0, 100)}...` 
                        : messageData.content,
                      is_read: false,
                      is_archived: false,
                      link: `/dashboard/messages/${conversationId}`,
                      conversation_id: conversationId
                    });

                  if (notifError) {
                    logger.error('Erreur lors de la création de la notification:', {
                      error: notifError,
                      recipientId,
                      conversationId
                    });
                  } else {
                    // Invalider le cache Redis des notifications
                    await redis.del(`notifications:${recipientId}:false:1:::`);
                    await redis.del(`notifications:unread:${recipientId}`);
                    
                    logger.info('Notification créée avec succès', {
                      recipientId,
                      conversationId
                    });
                  }
                } catch (notifError) {
                  logger.error('Exception lors de la création de la notification:', {
                    error: notifError,
                    recipientId,
                    conversationId
                  });
                }
              }
            }
          }

          // Supprimer la notification traitée
          await redis.del(notificationKey);
          await redis.srem('pending_notifications', notificationKey);
          
          logger.error('Notification traitée et supprimée', { key: notificationKey });
        } catch (notificationError) {
          logger.error('Erreur lors du traitement d\'une notification:', {
            error: notificationError,
            key: notificationKey
          });
          
          // Supprimer la notification en erreur pour éviter les boucles infinies
          await redis.del(notificationKey);
          await redis.srem('pending_notifications', notificationKey);
        }
      }
    } catch (error) {
      logger.error('Erreur lors du traitement des notifications:', error);
    }
  }

  // Ajouter cette méthode dans le constructeur de la classe MessagingService
  constructor() {
    // Démarrer le traitement périodique des notifications
    setInterval(() => this.processNotifications(), 60000); // Vérifier toutes les minutes
  }
}

export const messagingService = new MessagingService();
