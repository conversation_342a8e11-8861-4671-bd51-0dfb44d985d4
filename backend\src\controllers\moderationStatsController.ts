import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

// Durée du cache pour les statistiques (5 minutes)
const CACHE_DURATION = 5 * 60;

/**
 * Récupère les statistiques globales de modération avec filtres optionnels
 */
export async function getModerationStats(req: Request, res: Response) {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    // Récupérer les filtres depuis la requête
    const {
      startDate,
      endDate,
      contentType,
      category,
      method,
      isSafe
    } = req.query;

    // Vérifier que l'utilisateur est admin ou modérateur
    if (!userId || (userRole !== 'jobpadm' && userRole !== 'jobmodo')) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    // Clé de cache pour les statistiques (avec filtres)
    // On ajoute les filtres à la clé de cache pour différencier les requêtes filtrées
    let cacheKey = 'moderation_stats';
    
    if (startDate) cacheKey += `:startDate=${startDate}`;
    if (endDate) cacheKey += `:endDate=${endDate}`;
    if (contentType) cacheKey += `:contentType=${contentType}`;
    if (category) cacheKey += `:category=${category}`;
    if (method) cacheKey += `:method=${method}`;
    if (isSafe !== undefined) cacheKey += `:isSafe=${isSafe}`;

    // Vérifier si les statistiques sont en cache
    try {
      const cachedStats = await redis.get(cacheKey);
      if (cachedStats) {
        logger.info('Statistiques de modération récupérées depuis le cache');
        return res.status(200).json({
          success: true,
          data: JSON.parse(cachedStats)
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du cache Redis', { error });
      // Continuer sans utiliser le cache
    }

    // Construction des filtres communs
    const filters: Record<string, any> = {};
    if (contentType) filters['content_type'] = String(contentType);
    // Utilitaire pour convertir isSafe en booléen ou null
    function parseIsSafe(val: any): boolean | null {
      if (val === undefined || val === null || val === '') return null;
      if (val === true || val === 'true') return true;
      if (val === false || val === 'false') return false;
      return null;
    }
    const isSafeBool = parseIsSafe(isSafe);
    if (isSafeBool !== null) filters['is_safe'] = isSafeBool;

    // Préparation du filtre méthode
    let methodLike: string | null = null;
    if (method) {
      switch (String(method)) {
        case 'textIA':
          methodLike = 'gen-%';
          break;
        case 'textNonIA':
          methodLike = 'basic-%';
          break;
        case 'imageIA':
          methodLike = 'image-%';
          break;
        case 'other':
          methodLike = null; // On gère "other" à part si besoin
          break;
        default:
          methodLike = null;
      }
    }

    // Récupérer les statistiques depuis la base de données
    // Initialiser les compteurs
    let totalCount = 0;
    let unsafeCount = 0;

    // 1. Nombre total de contenus modérés
    let totalCountQuery = supabase
      .from('content_moderation_logs')
      .select('*', { count: 'exact', head: false })
      .match(filters);
    if (startDate) totalCountQuery = totalCountQuery.gte('created_at', String(startDate));
    if (endDate) totalCountQuery = totalCountQuery.lte('created_at', String(endDate));
    if (methodLike) totalCountQuery = totalCountQuery.like('moderation_id', methodLike);
    if (category) totalCountQuery = totalCountQuery.contains('categories', { [category as string]: true });
    const { count: totalCountResult, error: totalError } = await totalCountQuery;

    if (totalError) {
      logger.error('Erreur lors de la récupération du nombre total de contenus modérés', { error: totalError });
      logger.info('Continuation avec un compteur total à 0');
    } else {
      totalCount = totalCountResult || 0;
    }

    // 2. Nombre de contenus inappropriés
    let unsafeCountQuery = supabase
      .from('content_moderation_logs')
      .select('*', { count: 'exact', head: false })
      .match({ ...filters, is_safe: false });
    if (startDate) unsafeCountQuery = unsafeCountQuery.gte('created_at', String(startDate));
    if (endDate) unsafeCountQuery = unsafeCountQuery.lte('created_at', String(endDate));
    if (methodLike) unsafeCountQuery = unsafeCountQuery.like('moderation_id', methodLike);
    if (category) unsafeCountQuery = unsafeCountQuery.contains('categories', { [category as string]: true });
    const { count: unsafe, error: unsafeError } = await unsafeCountQuery;

    if (unsafeError) {
      logger.error('Erreur lors de la récupération du nombre de contenus inappropriés', { error: unsafeError });
      logger.info('Continuation avec un compteur de contenus inappropriés à 0');
    } else {
      unsafeCount = unsafe || 0;
    }

    // 3. Répartition par type de contenu
    interface ContentTypeCount {
      content_type: string;
      count: number;
    }

    let contentTypeData: ContentTypeCount[] = [];
    let contentTypeQuery = supabase
      .from('content_moderation_logs')
      .select('content_type')
      .match({ ...filters, is_safe: false });
    if (startDate) contentTypeQuery = contentTypeQuery.gte('created_at', String(startDate));
    if (endDate) contentTypeQuery = contentTypeQuery.lte('created_at', String(endDate));
    if (methodLike) contentTypeQuery = contentTypeQuery.like('moderation_id', methodLike);
    if (category) contentTypeQuery = contentTypeQuery.contains('categories', { [category as string]: true });
    const { data: contentTypeDataRaw, error: contentTypeError } = await contentTypeQuery;

    if (contentTypeError) {
      logger.error('Erreur lors de la récupération de la répartition par type de contenu', { error: contentTypeError });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques'
      });
    }

    // Calculer manuellement la répartition par type
    const typeCounts: Record<string, number> = {};
    (contentTypeDataRaw as any[])?.forEach((log: any) => {
      if (log.content_type) {
        const type = log.content_type;
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      }
    });

    contentTypeData = Object.entries(typeCounts).map(([content_type, count]) => ({
      content_type,
      count
    }));

    // 4. Répartition par catégorie
    // Initialiser les catégories avec des valeurs à 0
    const categoryStats: Record<string, number> = {
      harassment: 0,
      hateSpeech: 0,
      sexualContent: 0,
      violence: 0,
      selfHarm: 0,
      illegalActivity: 0,
      spam: 0,
      phoneSpam: 0,
      addressSpam: 0,
      unknownRisk: 0
    };

    let categoriesQuery = supabase
      .from('content_moderation_logs')
      .select('categories')
      .match({ ...filters, is_safe: false });
    if (startDate) categoriesQuery = categoriesQuery.gte('created_at', String(startDate));
    if (endDate) categoriesQuery = categoriesQuery.lte('created_at', String(endDate));
    if (methodLike) categoriesQuery = categoriesQuery.like('moderation_id', methodLike);
    if (category) categoriesQuery = categoriesQuery.contains('categories', { [category as string]: true });
    categoriesQuery = categoriesQuery.limit(1000);
    const { data: categoriesData, error: categoriesError } = await categoriesQuery;

    if (categoriesError) {
      logger.error('Erreur lors de la récupération de la répartition par catégorie', { error: categoriesError });
      // Ne pas échouer complètement, continuer avec des catégories à 0
      logger.info('Continuation avec des statistiques de catégories vides');
    } else {
      // Calculer la répartition par catégorie
      (categoriesData as any[])?.forEach((log: any) => {
        if (log.categories) {
          try {
            Object.entries(log.categories).forEach(([category, value]) => {
              if (value === true && category in categoryStats) {
                categoryStats[category]++;
              }
            });
          } catch (err) {
            // Ignorer les entrées avec des catégories mal formatées
            logger.warn('Format de catégories invalide dans les logs de modération', { categories: log.categories });
          }
        }
      });
    }

    // 5. Évolution dans le temps (derniers 30 jours ou période demandée)
    let startDateObj = new Date();
    let endDateObj = new Date();
    
    if (startDate) {
      startDateObj = new Date(String(startDate));
    } else {
      startDateObj.setDate(startDateObj.getDate() - 30); // 30 jours par défaut
    }
    
    if (endDate) {
      endDateObj = new Date(String(endDate));
    }
    
    const startDateStr = startDateObj.toISOString();

    // Initialiser les données de timeline avec les jours de la période
    const timelineStats: Record<string, { total: number, unsafe: number }> = {};

    // Calculer la différence en jours
    const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
    const diffDays = Math.min(365, Math.ceil(diffTime / (1000 * 60 * 60 * 24))); // Limiter à 365 jours max

    // Remplir avec les jours de la période (avec des valeurs à 0)
    for (let i = 0; i <= diffDays; i++) {
      const date = new Date(startDateObj);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0]; // Format YYYY-MM-DD
      timelineStats[dateStr] = { total: 0, unsafe: 0 };
    }

    let timelineQuery = supabase
      .from('content_moderation_logs')
      .select('created_at, is_safe')
      .match(filters);
    if (startDate) timelineQuery = timelineQuery.gte('created_at', String(startDate));
    if (endDate) timelineQuery = timelineQuery.lte('created_at', String(endDate));
    if (methodLike) timelineQuery = timelineQuery.like('moderation_id', methodLike);
    if (category) timelineQuery = timelineQuery.contains('categories', { [category as string]: true });
    timelineQuery = timelineQuery.order('created_at', { ascending: true });
    const { data: timelineData, error: timelineError } = await timelineQuery;

    if (timelineError) {
      logger.error('Erreur lors de la récupération de l\'évolution dans le temps', { error: timelineError });
      // Ne pas échouer complètement, continuer avec les données initialisées à 0
      logger.info('Continuation avec des données de timeline vides');
    } else {
      // Organiser les données par jour
      (timelineData as any[])?.forEach((log: any) => {
        try {
          const date = new Date(log.created_at).toISOString().split('T')[0]; // Format YYYY-MM-DD

          if (!timelineStats[date]) {
            timelineStats[date] = { total: 0, unsafe: 0 };
          }

          timelineStats[date].total++;
          if (log.is_safe === false) {
            timelineStats[date].unsafe++;
          }
        } catch (err) {
          // Ignorer les entrées avec des dates invalides
          logger.warn('Date invalide dans les logs de modération', { created_at: log.created_at });
        }
      });
    }

    // 6. Taux de détection par méthode (API vs liste de mots)
    const methodStats = {
      textIA: { total: 0, unsafe: 0 },
      textNonIA: { total: 0, unsafe: 0 },
      imageIA: { total: 0, unsafe: 0 },
      other: { total: 0, unsafe: 0 }
    };

    try {
      const queryMethod = supabase.from('content_moderation_logs').select('moderation_id, is_safe, content_type, is_image_moderation').limit(1000);
      
      const { data: methodData, error: methodError } = await queryMethod;

      if (methodError) {
        logger.error('Erreur lors de la récupération du taux de détection par méthode', { error: methodError });
        // Ne pas échouer complètement, continuer avec des statistiques vides
        logger.info('Continuation avec des statistiques de méthode vides');
      } else {
        // Calculer les statistiques par méthode
        methodData?.forEach(log => {
          const id = log.moderation_id || '';
          const contentType = log.content_type || '';
          const isImageModeration = log.is_image_moderation === true;
          
          // Déterminer la méthode en suivant la même logique que le frontend
          const imageTypes = ['gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar', 'profile_picture', 'banner_picture'];
          const isImageContent = imageTypes.includes(contentType);

          if (id.startsWith('image-') || (id.startsWith('gen-') && isImageContent)) {
            // Image (IA)
            methodStats.imageIA.total++;
            if (log.is_safe === false) methodStats.imageIA.unsafe++;
          } else if (id.startsWith('gen-') || id.startsWith('api-')) {
            // Texte (IA)
            methodStats.textIA.total++;
            if (log.is_safe === false) methodStats.textIA.unsafe++;
          } else if (id.startsWith('basic-')) {
            // Texte (SANS IA)
            methodStats.textNonIA.total++;
            if (log.is_safe === false) methodStats.textNonIA.unsafe++;
          } else {
            // Autres méthodes (local, error, etc.)
            methodStats.other.total++;
            if (log.is_safe === false) methodStats.other.unsafe++;
          }
        });
      }
    } catch (error) {
      logger.error('Exception lors du calcul des statistiques par méthode', { error });
      // Continuer avec les statistiques vides plutôt que d'échouer
    }

    // 7. Liste des types de contenu disponibles pour les filtres
    let availableContentTypes: string[] = [];
    try {
      const { data: distinctTypes, error: distinctTypesError } = await supabase
        .from('content_moderation_logs')
        .select('content_type')
        .order('content_type')
        .limit(50);
      
      if (distinctTypesError) {
        logger.error('Erreur lors de la récupération des types de contenu disponibles', { error: distinctTypesError });
      } else {
        // Extraire les types uniques
        const uniqueTypes = new Set<string>();
        distinctTypes?.forEach(item => {
          if (item.content_type) {
            uniqueTypes.add(item.content_type);
          }
        });
        availableContentTypes = [...uniqueTypes];
      }
    } catch (error) {
      logger.error('Exception lors de la récupération des types de contenu', { error });
    }

    // Assembler toutes les statistiques
    const stats = {
      total: totalCount || 0,
      unsafe: unsafeCount || 0,
      safeRate: totalCount ? Math.round(((totalCount - (unsafeCount || 0)) / totalCount) * 100) : 0,
      contentTypes: contentTypeData || [],
      categories: categoryStats,
      timeline: timelineStats,
      methods: methodStats,
      availableFilters: {
        contentTypes: availableContentTypes,
        categories: Object.keys(categoryStats),
        methods: ['textIA', 'textNonIA', 'imageIA', 'other']
      },
      appliedFilters: {
        startDate: startDate ? String(startDate) : null,
        endDate: endDate ? String(endDate) : null,
        contentType: contentType ? String(contentType) : null,
        category: category ? String(category) : null,
        method: method ? String(method) : null,
        isSafe: isSafe !== undefined ? String(isSafe) === 'true' : null
      }
    };

    try {
      // Mettre en cache les statistiques
      await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);
      logger.info('Statistiques de modération mises en cache');
    } catch (error) {
      logger.error('Erreur lors de la mise en cache des statistiques', { error });
      // Continuer sans mise en cache
    }

    return res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques de modération', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
}

/**
 * Récupère les statistiques de modération pour un utilisateur spécifique
 */
export async function getUserModerationStats(req: Request, res: Response) {
  try {
    const userId = req.user?.userId;
    const userRole = req.user?.role;
    const targetUserId = req.params.userId;

    // Récupérer les filtres depuis la requête
    const {
      startDate,
      endDate,
      contentType,
      isSafe
    } = req.query;

    // Vérifier que l'utilisateur est admin ou modérateur, ou qu'il demande ses propres statistiques
    if (!userId || ((userRole !== 'jobpadm' && userRole !== 'jobmodo') && userId !== targetUserId)) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    // Clé de cache pour les statistiques de l'utilisateur (avec filtres)
    let cacheKey = `moderation_stats:user:${targetUserId}`;
    
    if (startDate) cacheKey += `:startDate=${startDate}`;
    if (endDate) cacheKey += `:endDate=${endDate}`;
    if (contentType) cacheKey += `:contentType=${contentType}`;
    if (isSafe !== undefined) cacheKey += `:isSafe=${isSafe}`;

    // Vérifier si les statistiques sont en cache
    try {
      const cachedStats = await redis.get(cacheKey);
      if (cachedStats) {
        logger.info('Statistiques de modération utilisateur récupérées depuis le cache');
        return res.status(200).json({
          success: true,
          data: JSON.parse(cachedStats)
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération du cache Redis pour les statistiques utilisateur', { error });
      // Continuer sans utiliser le cache
    }

    // Préparer la requête de base avec les filtres
    const baseQuery = supabase.from('content_moderation_logs').select('*').eq('user_id', targetUserId);
    
    // Appliquer les filtres à la requête
    if (startDate) {
      baseQuery.gte('created_at', String(startDate));
    }
    
    if (endDate) {
      baseQuery.lte('created_at', String(endDate));
    }
    
    if (contentType) {
      baseQuery.eq('content_type', String(contentType));
    }
    
    if (isSafe !== undefined) {
      baseQuery.eq('is_safe', isSafe === 'true');
    }

    // Récupérer les statistiques depuis la base de données
    let userData = [];
    try {
      const { data, error: userError } = await baseQuery;

      if (userError) {
        logger.error('Erreur lors de la récupération des statistiques de modération utilisateur', { error: userError });
        logger.info('Continuation avec des données utilisateur vides');
      } else {
        userData = data || [];
      }
    } catch (error) {
      logger.error('Exception lors de la récupération des statistiques utilisateur', { error });
      // Continuer avec un tableau vide
    }

    // Calculer les statistiques
    const totalCount = userData?.length || 0;
    const unsafeCount = userData?.filter(log => log.is_safe === false).length || 0;

    // Répartition par type de contenu
    const contentTypeStats: Record<string, { total: number, unsafe: number }> = {
      mission: { total: 0, unsafe: 0 },
      comment: { total: 0, unsafe: 0 },
      profile: { total: 0, unsafe: 0 }
    };

    userData?.forEach(log => {
      const type = log.content_type || 'other';

      if (type in contentTypeStats) {
        contentTypeStats[type].total++;
        if (log.is_safe === false) {
          contentTypeStats[type].unsafe++;
        }
      }
    });

    // Répartition par catégorie
    const categoryStats: Record<string, number> = {
      harassment: 0,
      hateSpeech: 0,
      sexualContent: 0,
      violence: 0,
      selfHarm: 0,
      illegalActivity: 0,
      spam: 0,
      phoneSpam: 0,
      addressSpam: 0,
      unknownRisk: 0
    };

    userData?.forEach(log => {
      if (log.categories && log.is_safe === false) {
        Object.entries(log.categories).forEach(([category, value]) => {
          if (value === true && category in categoryStats) {
            categoryStats[category]++;
          }
        });
      }
    });

    // Évolution dans le temps
    let startDateObj = new Date();
    let endDateObj = new Date();
    
    if (startDate) {
      startDateObj = new Date(String(startDate));
    } else {
      startDateObj.setDate(startDateObj.getDate() - 30); // 30 jours par défaut
    }
    
    if (endDate) {
      endDateObj = new Date(String(endDate));
    }
    
    // Calculer la différence en jours
    const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
    const diffDays = Math.min(365, Math.ceil(diffTime / (1000 * 60 * 60 * 24))); // Limiter à 365 jours max

    const timelineStats: Record<string, { total: number, unsafe: number }> = {};

    // Remplir avec les jours de la période (avec des valeurs à 0)
    for (let i = 0; i <= diffDays; i++) {
      const date = new Date(startDateObj);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0]; // Format YYYY-MM-DD
      timelineStats[dateStr] = { total: 0, unsafe: 0 };
    }

    userData?.forEach(log => {
      try {
        if (log.created_at) {
          const date = new Date(log.created_at).toISOString().split('T')[0]; // Format YYYY-MM-DD

          if (timelineStats[date]) {
            timelineStats[date].total++;
            if (log.is_safe === false) {
              timelineStats[date].unsafe++;
            }
          }
        }
      } catch (err) {
        // Ignorer les entrées avec des dates invalides
        logger.warn('Date invalide dans les logs de modération utilisateur', { created_at: log.created_at });
      }
    });

    // Liste des types de contenu disponibles pour les filtres
    let availableContentTypes: string[] = [];
    try {
      const { data: distinctTypes, error: distinctTypesError } = await supabase
        .from('content_moderation_logs')
        .select('content_type')
        .eq('user_id', targetUserId)
        .order('content_type')
        .limit(50);
      
      if (distinctTypesError) {
        logger.error('Erreur lors de la récupération des types de contenu disponibles', { error: distinctTypesError });
      } else {
        // Extraire les types uniques
        const uniqueTypes = new Set<string>();
        distinctTypes?.forEach(item => {
          if (item.content_type) {
            uniqueTypes.add(item.content_type);
          }
        });
        availableContentTypes = [...uniqueTypes];
      }
    } catch (error) {
      logger.error('Exception lors de la récupération des types de contenu', { error });
    }

    // Assembler toutes les statistiques
    const stats = {
      total: totalCount,
      unsafe: unsafeCount,
      safeRate: totalCount ? Math.round(((totalCount - unsafeCount) / totalCount) * 100) : 0,
      contentTypes: contentTypeStats,
      categories: categoryStats,
      timeline: timelineStats,
      availableFilters: {
        contentTypes: availableContentTypes
      },
      appliedFilters: {
        startDate: startDate ? String(startDate) : null,
        endDate: endDate ? String(endDate) : null,
        contentType: contentType ? String(contentType) : null,
        isSafe: isSafe !== undefined ? String(isSafe) === 'true' : null
      }
    };

    try {
      // Mettre en cache les statistiques
      await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_DURATION);
      logger.info('Statistiques de modération utilisateur mises en cache');
    } catch (error) {
      logger.error('Erreur lors de la mise en cache des statistiques utilisateur', { error });
      // Continuer sans mise en cache
    }

    return res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques de modération utilisateur', { error });
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
}
