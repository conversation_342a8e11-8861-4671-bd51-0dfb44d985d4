import { Router } from 'express';
import axios from 'axios';
import { authMiddleware } from '../middleware/authMiddleware';
import logger from '../utils/logger';

const routes = Router();

routes.get('/siren/:siren', authMiddleware.authenticateToken, async (req, res) => {
  try {
    const { siren } = req.params;

    // Vérifier le format du SIREN (9 chiffres)
    if (!/^\d{9}$/.test(siren)) {
      res.status(400).json({
        success: false,
        message: 'Format de SIREN invalide'
      });
      return;
    }

    console.log(`Requête à l'API SIRENE pour le SIREN : ${siren}`);

    // Appel à l'API SIRENE
    const response = await axios.get(
      `https://api.insee.fr/api-sirene/3.11/siren/${siren}`,
      {
        headers: {
          'X-INSEE-Api-Key-Integration': process.env.INSEE_API_KEY
        }
      }
    );

    const reponse_api_sirene = response.data;

    logger.info('Réponse de l\'API SIRENE : ', reponse_api_sirene);

    // Formater la réponse pour correspondre à notre structure
    const formattedResponse = {
      uniteLegale: reponse_api_sirene.uniteLegale || {}
    };

    res.json({
      success: true,
      data: formattedResponse
    });
    return;
  } catch (error: any) {
    logger.error('Erreur lors de la requête à l\'API SIRENE:', error);
    
    if (error.response?.status === 404) {
      res.status(404).json({
        success: false,
        message: 'SIREN non trouvé'
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la recherche du SIREN'
    });
    return;
  }
});

export default routes;
