import api from '../../../services/api';
import logger from '../../../utils/logger';

/**
 * Interface pour les statistiques d'images IA
 */
export interface AiImageStats {
  totalCount: number;
  totalCost: number;
  byPurpose: Record<string, number>;
  byDate: Array<{ date: string; count: number }>;
  recentGenerations: Array<{
    id: string;
    user_id: string;
    nom: string;
    prenom: string;
    email: string;
    purpose: string;
    prompt: string;
    cost: number;
    image_id: string;
    image_url?: string;
    created_at: string;
  }>;
}

/**
 * Récupère les statistiques de génération d'images IA
 * @param filters - Filtres optionnels (type d'image, dates)
 * @returns Statistiques de génération d'images IA
 */
export const fetchAiImageStats = async (filters?: {
  purpose?: string;
  startDate?: string;
  endDate?: string;
}): Promise<AiImageStats> => {
  try {
    // Construire les paramètres de requête
    const params = new URLSearchParams();
    if (filters?.purpose && filters.purpose !== 'all') {
      params.append('purpose', filters.purpose);
    }
    if (filters?.startDate) {
      params.append('startDate', filters.startDate);
    }
    if (filters?.endDate) {
      params.append('endDate', filters.endDate);
    }

    const response = await api.get(`/api/ai-image-stats?${params.toString()}`);

    if (response.data.success) {
      return response.data.stats;
    } else {
      throw new Error(response.data.message || 'Erreur lors de la récupération des statistiques');
    }
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques d\'images IA:', error);
    throw error;
  }
};
