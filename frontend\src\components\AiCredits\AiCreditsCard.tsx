import React, { useState, useEffect } from 'react';
import { Card, CardContent, Ty<PERSON>graphy, Button, Box, CircularProgress, Divider, Tooltip, IconButton, <PERSON>lider, Stack, FormHelperText, Alert } from '@mui/material';
import { useAiCredits } from '../../hooks/useAiCredits';
import { useJobiBalance } from '../../hooks/useJobiBalance';
import { SmartToy as RobotIcon, CreditCard as CardIcon, Add as AddIcon, Remove as RemoveIcon } from '@mui/icons-material';
import { motion } from 'framer-motion';
import ModalPortal from '../../components/ModalPortal';
import { AlertTriangle, X, RefreshCw } from 'lucide-react';
import { useSubscription } from '../../hooks/useSubscription';
import { notify } from '../../components/Notification';

// Créer un composant fonctionnel pour l'icône Jobi personnalisée
const JobiIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-5 w-5 text-[#FF6B2C]" {...props}>
    <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
    <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
    <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
    <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
  </svg>
);

interface AiCreditsCardProps {
  showBuyButtons?: boolean;
  compact?: boolean;
}

const AiCreditsCard: React.FC<AiCreditsCardProps> = ({
  showBuyButtons = true,
  compact = false
}) => {
  const { credits, loading, error, isRateLimited, buyCredits, buyCreditsWithStripe, refetch } = useAiCredits();
  const { balance: jobiBalance, loading: jobiLoading, refreshBalance: refreshJobiBalance } = useJobiBalance();
  const { config, isLoading: configLoading } = useSubscription();
  const [buyingWithJobi, setBuyingWithJobi] = useState(false);
  const [buyingWithStripe, setBuyingWithStripe] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showLimitWarning, setShowLimitWarning] = useState(false);
  const [packQuantity, setPackQuantity] = useState(1);

  // Récupérer les configurations des crédits IA
  const creditsConfig = config?.gratuit?.aiCredits;
  const creditsPerPack = creditsConfig?.packs || 1; // S'assurer que ce n'est pas 0 pour éviter la division par 0
  const pricePerPackJobi = creditsConfig?.additionalCostJobi || 0;
  const pricePerPackEur = creditsConfig?.additionalCost || 0;
  const CREDIT_MAX = 500; // Définir la limite maximale de crédits

  // Afficher la notification d'erreur si une erreur se produit lors du chargement initial
  useEffect(() => {
    if (error && !loading) { // S'assurer que ce n'est pas une ancienne erreur pendant le chargement
      notify(error, 'error');
    }
  }, [error, loading]);

  // Calculer le nombre maximum de packs possibles sans dépasser la limite de crédits
  const maxPacksAllowed = creditsPerPack > 0
    ? Math.max(1, Math.floor((CREDIT_MAX - credits) / creditsPerPack))
    : 1; // Au moins 1 pack si creditsPerPack est 0 ou négatif

  // Calcul des totaux
  const totalCredits = creditsPerPack * packQuantity;
  const totalPriceJobi = pricePerPackJobi * packQuantity;
  const totalPriceEur = pricePerPackEur * packQuantity;

  const handleQuantityChange = (value: number) => {
    // Limiter la quantité au maximum de packs autorisés
    const newQuantity = Math.min(Math.max(1, value), maxPacksAllowed);
    setPackQuantity(newQuantity);
  };

  const openConfirmation = () => {
    setShowConfirmation(true);
  };

  const closeConfirmation = () => {
    setShowConfirmation(false);
  };

  const openLimitWarning = () => {
    setShowLimitWarning(true);
  };

  const closeLimitWarning = () => {
    setShowLimitWarning(false);
  };

  // Vérifier si l'achat dépasserait la limite de crédits (500)
  const checkCreditLimit = (quantity: number): boolean => {
    const CREDIT_MAX = 500;
    const totalCreditsToAdd = creditsPerPack * quantity;
    return (credits + totalCreditsToAdd) > CREDIT_MAX;
  };

  const handleBuyWithJobi = async () => {
    closeConfirmation();

    // Vérifier si l'achat dépasserait la limite
    if (checkCreditLimit(packQuantity)) {
      openLimitWarning();
      return;
    }

    setBuyingWithJobi(true);
    try {
      await buyCredits(packQuantity);
      await refreshJobiBalance();
    } finally {
      setBuyingWithJobi(false);
      setPackQuantity(1); // Réinitialiser la quantité après l'achat
    }
  };

  const handleBuyWithStripe = async () => {
    // Vérifier si l'achat dépasserait la limite
    if (checkCreditLimit(packQuantity)) {
      openLimitWarning();
      return;
    }

    setBuyingWithStripe(true);
    try {
      await buyCreditsWithStripe(packQuantity);
      setPackQuantity(1); // Réinitialiser la quantité après l'achat
    } finally {
      setBuyingWithStripe(false);
    }
  };

  // Afficher un message spécifique pour le rate limit en mode compact
  if (compact) {
    if (isRateLimited) {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            backgroundColor: '#FFF8F3',
            borderRadius: 1,
            padding: '4px 8px',
            border: '1px solid #FFE4BA'
          }}
        >
          <AlertTriangle size={20} color="#FF6B2C" />
          <Typography variant="body2" fontWeight="medium" sx={{ display: 'flex', alignItems: 'center' }}>
            Trop de requêtes
            <Tooltip title="Réessayer">
              <IconButton size="small" onClick={refetch} sx={{ ml: 0.5, p: 0.5 }}>
                <RefreshCw size={14} />
              </IconButton>
            </Tooltip>
          </Typography>
        </Box>
      );
    }

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          backgroundColor: '#FFF8F3',
          borderRadius: 1,
          padding: '4px 8px',
          border: '1px solid #FFE4BA'
        }}
      >
        <RobotIcon sx={{ color: "#FF6B2C", fontSize: 20 }} />
        <Typography variant="body2" fontWeight="medium">
          {loading ? (
            <CircularProgress size={16} thickness={5} sx={{ color: '#FF6B2C', ml: 1 }} />
          ) : (
            `${credits} crédit${credits !== 1 ? 's' : ''} IA`
          )}
        </Typography>
      </Box>
    );
  }

  // Contenu de la modale de confirmation
  const confirmationContent = (
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      transition={{ type: "spring", duration: 0.5 }}
      onClick={(e) => e.stopPropagation()}
      style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '0',
        maxWidth: '500px',
        width: '90%',
        position: 'relative',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
        border: '1px solid #FFE4BA',
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        maxHeight: '90vh',
        overflowY: 'auto',
      }}
    >
      <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Confirmation d'achat</h2>
        <button
          onClick={closeConfirmation}
          className="text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      <div className="p-6">
        <div className="flex items-start gap-4 mb-4">
          <div className="p-3 bg-orange-100 rounded-full">
            <AlertTriangle className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <div>
            <p className="text-gray-700 mb-2">
              Vous êtes sur le point d'acheter <strong>{totalCredits} crédits IA</strong> pour <strong>{totalPriceJobi} Jobi</strong>.
            </p>
            <p className="text-gray-700">
              Votre solde actuel est de <strong>{jobiBalance} Jobi</strong>.
            </p>
            <p className="text-gray-700">
              Après cet achat, il vous restera <strong>{jobiBalance - totalPriceJobi} Jobi</strong>.
            </p>
          </div>
        </div>

        <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mb-4">
          <p className="text-sm text-gray-600">
            Les crédits IA vous permettent d'utiliser l'intelligence artificielle pour générer du contenu sur la plateforme.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mt-4">
          <button
            onClick={closeConfirmation}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors w-full sm:w-1/2"
          >
            Annuler
          </button>
          <button
            onClick={handleBuyWithJobi}
            className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors w-full sm:w-1/2"
          >
            Confirmer l'achat
          </button>
        </div>
      </div>
    </motion.div>
  );

  // Contenu de la modale d'avertissement de limite
  const limitWarningContent = (
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      transition={{ type: "spring", duration: 0.5 }}
      onClick={(e) => e.stopPropagation()}
      style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '0',
        maxWidth: '500px',
        width: '90%',
        position: 'relative',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
        border: '1px solid #FFE4BA',
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}
    >
      <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Limite de crédits atteinte</h2>
        <button
          onClick={closeLimitWarning}
          className="text-gray-500 hover:text-gray-700 transition-colors"
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      <div className="p-6">
        <div className="flex items-start gap-4 mb-4">
          <div className="p-3 bg-orange-100 rounded-full">
            <AlertTriangle className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <div>
            <p className="text-gray-700 mb-2">
              Vous ne pouvez pas acheter <strong>{totalCredits} crédits IA</strong> car cela dépasserait la limite maximale de <strong>500 crédits</strong>.
            </p>
            <p className="text-gray-700">
              Vous avez actuellement <strong>{credits} crédits</strong> et l'achat vous ferait atteindre <strong>{credits + totalCredits} crédits</strong>.
            </p>
          </div>
        </div>

        <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mb-4">
          <p className="text-sm text-gray-600">
            Pour acheter plus de crédits, veuillez d'abord utiliser une partie de vos crédits existants.
          </p>
        </div>

        <div className="flex justify-center mt-4">
          <button
            onClick={closeLimitWarning}
            className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors w-full sm:w-1/2"
          >
            J'ai compris
          </button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <>
      <ModalPortal isOpen={showConfirmation} onBackdropClick={closeConfirmation}>
        {confirmationContent}
      </ModalPortal>

      <ModalPortal isOpen={showLimitWarning} onBackdropClick={closeLimitWarning}>
        {limitWarningContent}
      </ModalPortal>

      <Card
        component={motion.div}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        sx={{
          overflow: 'visible',
          borderRadius: 2,
          boxShadow: '0 8px 24px rgba(255, 107, 44, 0.08)',
          border: '1px solid #FFE4BA',
          position: 'relative'
        }}
      >
        {/* Fond décoratif avec dégradé */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '130px',
            background: 'linear-gradient(135deg, #FF6B2C 0%, #FF965E 100%)',
            borderRadius: '8px 8px 0 0',
            zIndex: 0
          }}
        />

        <CardContent sx={{ position: 'relative', zIndex: 1, padding: '24px !important' }}>
          {/* En-tête avec icône et titre */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 3,
              color: 'white'
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
              }}
            >
              <RobotIcon sx={{ fontSize: 24 }} />
            </Box>
            <Typography variant="h6" fontWeight="bold" sx={{ ml: 1.5 }}>
              Crédits IA
            </Typography>
          </Box>

          {/* Carte blanche avec nombre de crédits */}
          <Box
            sx={{
              backgroundColor: 'white',
              borderRadius: 3,
              p: 3,
              mb: 3,
              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.05)',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* Cercles décoratifs */}
            <Box
              sx={{
                position: 'absolute',
                top: -15,
                right: -15,
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: 'rgba(255, 228, 186, 0.3)'
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                bottom: -20,
                left: -20,
                width: 100,
                height: 100,
                borderRadius: '50%',
                background: 'rgba(255, 107, 44, 0.05)'
              }}
            />

            {/* Affichage Crédits / Loading / Error / Rate Limit */}
            {loading || configLoading ? (
              <CircularProgress size={40} thickness={4} sx={{ color: '#FF6B2C' }} />
            ) : isRateLimited ? (
              <Box sx={{ textAlign: 'center', color: 'warning.main' }}>
                <AlertTriangle size={40} color="#FF6B2C" className="mb-2" />
                <Typography variant="body1" gutterBottom>Trop de requêtes</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Veuillez patienter quelques minutes avant de réessayer
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={refetch}
                  startIcon={<RefreshCw size={16} />}
                  sx={{
                    borderColor: '#FF6B2C',
                    color: '#FF6B2C',
                    '&:hover': { borderColor: '#FF7A35', backgroundColor: 'rgba(255, 107, 44, 0.04)' }
                  }}
                >
                  Réessayer maintenant
                </Button>
              </Box>
            ) : error ? (
              <Box sx={{ textAlign: 'center', color: 'error.main' }}>
                <Typography variant="body1" gutterBottom>Erreur de chargement</Typography>
                <Button size="small" onClick={refetch} startIcon={<AlertTriangle size={16} />}>Réessayer</Button>
              </Box>
            ) : (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <Typography
                  variant="h3"
                  fontWeight="bold"
                  sx={{
                    color: '#FF6B2C',
                    textShadow: '0px 2px 4px rgba(255, 107, 44, 0.2)',
                    mb: 1
                  }}
                >
                  {credits}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#4B5563',
                    fontWeight: 500
                  }}
                >
                  Crédit{credits !== 1 ? 's' : ''} actuellement disponible{credits !== 1 ? 's' : ''}
                </Typography>
              </motion.div>
            )}
          </Box>

          {/* Description */}
          <Typography
            variant="body2"
            sx={{
              mb: 3,
              textAlign: 'center',
              color: '#4B5563',
              backgroundColor: 'rgba(255, 107, 44, 0.05)',
              borderRadius: 2,
              p: 1.5
            }}
          >
            Les crédits IA vous permettent d'utiliser l'intelligence artificielle pour générer du contenu.
          </Typography>

          {showBuyButtons && (
            <>
              <Divider sx={{ my: 3 }} />

              {/* Titre section achat */}
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                sx={{
                  mb: 2.5,
                  position: 'relative',
                  display: 'inline-block'
                }}
              >
                Acheter des crédits IA
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '50%',
                    height: 2,
                    backgroundColor: '#FF6B2C',
                    borderRadius: 1
                  }}
                />
              </Typography>

              {/* Alerte limite de crédits */}
              {credits >= CREDIT_MAX && (
                 <Alert
                  severity="info"
                  sx={{
                    mb: 2,
                    borderRadius: 2,
                    backgroundColor: 'rgba(255, 150, 94, 0.1)',
                    color: '#4B5563',
                    border: '1px solid rgba(255, 150, 94, 0.3)',
                    '.MuiAlert-icon': { color: '#FF6B2C' }
                  }}
                >
                   Vous avez atteint le maximum de {CREDIT_MAX} crédits IA.
                </Alert>
              )}

              {/* Sélection de packs (si limite non atteinte) */}
              {credits < CREDIT_MAX && (
                <Box
                  sx={{
                    mb: 3
                  }}
                >
                  <Typography
                    variant="body2"
                    fontWeight="medium"
                    sx={{ mb: 2 }}
                  >
                    Sélectionnez le nombre de packs à acheter
                  </Typography>

                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      gap: 3,
                      alignItems: 'stretch',
                      width: '100%'
                    }}
                  >
                    {/* Contrôles de quantité avec design amélioré */}
                    <Box
                      component={motion.div}
                      whileHover={{ scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        width: { xs: '100%', sm: '50%' },
                        background: 'linear-gradient(145deg, #ffffff, #f9f9f9)',
                        borderRadius: 3,
                        p: 3,
                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.06)',
                        border: '1px solid #f0f0f0',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      {/* Élément décoratif */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: -20,
                          left: -20,
                          width: 120,
                          height: 120,
                          borderRadius: '50%',
                          background: 'rgba(255, 107, 44, 0.03)'
                        }}
                      />

                      <Typography variant="subtitle2" fontWeight="bold" color="#4B5563" gutterBottom>
                        Nombre de packs
                      </Typography>

                      {/* Slider pour une sélection plus intuitive */}
                      <Box sx={{ px: 1, py: 2 }}>
                        <Slider
                          value={packQuantity}
                          onChange={(_, value) => handleQuantityChange(value as number)}
                          min={1}
                          max={maxPacksAllowed}
                          step={1}
                          marks={[
                            { value: 1, label: '1' },
                            { value: Math.min(25, maxPacksAllowed), label: Math.min(25, maxPacksAllowed).toString() },
                            { value: maxPacksAllowed, label: maxPacksAllowed.toString() }
                          ].filter((mark, index, self) =>
                            index === self.findIndex((m) => m.value === mark.value)
                          )}
                          sx={{
                            color: '#FF6B2C',
                            '& .MuiSlider-thumb': {
                              width: 20,
                              height: 20,
                              backgroundColor: '#FF6B2C',
                              boxShadow: '0 2px 6px rgba(255, 107, 44, 0.3)',
                              '&:hover, &.Mui-focusVisible': {
                                boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)'
                              }
                            },
                            '& .MuiSlider-rail': {
                              backgroundColor: '#FFE4BA',
                              opacity: 0.5
                            },
                            '& .MuiSlider-mark': {
                              backgroundColor: '#FF965E',
                              height: 8,
                              width: 2,
                              marginTop: -3
                            },
                            '& .MuiSlider-markLabel': {
                              color: '#6B7280',
                              fontSize: '0.75rem'
                            }
                          }}
                        />
                        <FormHelperText sx={{ textAlign: 'center', mt: 1 }}>
                          Maximum {maxPacksAllowed * creditsPerPack} crédits ({maxPacksAllowed} packs). Limite totale : {CREDIT_MAX} crédits.
                        </FormHelperText>
                      </Box>

                      {/* Contrôles + / - avec affichage du nombre */}
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mt: 1,
                          backgroundColor: 'white',
                          borderRadius: 2,
                          border: '1px solid #EEE',
                          p: 0.5
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: 40,
                            width: 40
                          }}
                        >
                          <IconButton
                            onClick={() => handleQuantityChange(packQuantity - 1)}
                            disabled={packQuantity <= 1}
                            sx={{
                              color: 'white',
                              background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                              boxShadow: '0 2px 4px rgba(255, 107, 44, 0.2)',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #FF7A35 0%, #FF965E 100%)',
                              },
                              '&.Mui-disabled': {
                                background: '#f5f5f5',
                                color: '#bdbdbd'
                              }
                            }}
                            size="small"
                          >
                            <RemoveIcon fontSize="small" />
                          </IconButton>
                        </Box>

                        <Box
                          component={motion.div}
                          key={packQuantity}
                          initial={{ scale: 0.8 }}
                          animate={{ scale: 1 }}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: 1,
                            p: 1,
                            minWidth: 70,
                          }}
                        >
                          <Typography
                            variant="subtitle1"
                            fontWeight="bold"
                            sx={{
                              color: '#FF6B2C'
                            }}
                          >
                            {packQuantity} {packQuantity > 1 ? 'packs' : 'pack'}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: 40,
                            width: 40
                          }}
                        >
                          <IconButton
                            onClick={() => handleQuantityChange(packQuantity + 1)}
                            disabled={packQuantity >= maxPacksAllowed}
                            sx={{
                              color: 'white',
                              background: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 100%)',
                              boxShadow: '0 2px 4px rgba(255, 107, 44, 0.2)',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #FF7A35 0%, #FF965E 100%)',
                              },
                              '&.Mui-disabled': {
                                background: '#f5f5f5',
                                color: '#bdbdbd'
                              }
                            }}
                            size="small"
                          >
                            <AddIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    </Box>

                    {/* Résumé et prix avec design amélioré */}
                    <Box
                      component={motion.div}
                      whileHover={{ scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                      sx={{
                        width: { xs: '100%', sm: '50%' },
                        background: 'linear-gradient(145deg, #FFF8F3, #FFEFDF)',
                        borderRadius: 3,
                        p: 3,
                        boxShadow: '0 6px 16px rgba(255, 107, 44, 0.1)',
                        border: '1px solid #FFE4BA',
                        position: 'relative',
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between'
                      }}
                    >
                      {/* Éléments décoratifs */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: -50,
                          right: -50,
                          width: 150,
                          height: 150,
                          borderRadius: '50%',
                          background: 'rgba(255, 107, 44, 0.05)'
                        }}
                      />

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" fontWeight="bold" color="#4B5563" gutterBottom>
                          Résumé
                        </Typography>

                        <motion.div
                          key={totalCredits}
                          initial={{ scale: 0.9, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Typography
                            variant="h4"
                            fontWeight="bold"
                            sx={{
                              color: '#FF6B2C',
                              textShadow: '0 1px 3px rgba(255, 107, 44, 0.1)',
                              mt: 1
                            }}
                          >
                            {totalCredits}
                            <Typography
                              component="span"
                              variant="h6"
                              sx={{
                                ml: 1,
                                color: '#FF965E',
                                fontWeight: 'medium'
                              }}
                            >
                              crédits
                            </Typography>
                          </Typography>
                        </motion.div>
                      </Box>

                      <Stack
                        direction="column"
                        spacing={1.5}
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.7)',
                          borderRadius: 2,
                          p: 2,
                          border: '1px dashed #FFE4BA'
                        }}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" color="#4B5563">
                            Prix unitaire:
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <JobiIcon style={{ fontSize: 16, color: '#FF6B2C' }} />
                            <Typography variant="body2" fontWeight="medium">
                              {pricePerPackJobi} Jobi / {pricePerPackEur}€
                            </Typography>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 0.5, borderStyle: 'dashed' }} />

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" fontWeight="bold" color="#4B5563">
                            Prix total:
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Typography variant="body2" fontWeight="bold" color="#FF6B2C">
                              {totalPriceJobi} Jobi / {totalPriceEur}€
                            </Typography>
                          </Box>
                        </Box>
                      </Stack>
                    </Box>
                  </Box>
                </Box>
              )}

              {/* Boutons d'action */}
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: 2,
                  mt: 2
                }}
              >
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<CardIcon />}
                  onClick={handleBuyWithStripe}
                  disabled={buyingWithStripe}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    '&:hover': { backgroundColor: '#FF7A35' },
                    py: 1.5,
                    borderRadius: 2,
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)'
                  }}
                >
                  {buyingWithStripe ? (
                    <CircularProgress size={24} thickness={5} sx={{ color: 'white' }} />
                  ) : (
                    <>
                      Acheter avec CB ({totalPriceEur}€)
                    </>
                  )}
                </Button>

                <Tooltip title={jobiBalance < totalPriceJobi ? "Solde Jobi insuffisant" : `Acheter ${totalCredits} crédits pour ${totalPriceJobi} Jobi`}>
                  <span style={{ width: '100%' }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<JobiIcon />}
                      onClick={openConfirmation}
                      disabled={buyingWithJobi || jobiLoading || jobiBalance < totalPriceJobi}
                      sx={{
                        borderColor: '#FF6B2C',
                        color: '#FF6B2C',
                        borderWidth: 2,
                        '&:hover': {
                          borderColor: '#FF7A35',
                          backgroundColor: 'rgba(255, 107, 44, 0.05)'
                        },
                        py: 1.5,
                        borderRadius: 2
                      }}
                    >
                      {buyingWithJobi ? (
                        <CircularProgress size={24} thickness={5} sx={{ color: '#FF6B2C' }} />
                      ) : (
                        <>
                          Acheter avec Jobi ({totalPriceJobi} Jobi)
                        </>
                      )}
                    </Button>
                  </span>
                </Tooltip>
              </Box>
            </>
          )}
        </CardContent>
      </Card>
    </>
  );
};

export default AiCreditsCard;
