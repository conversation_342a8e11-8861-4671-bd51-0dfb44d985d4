import { useState, useEffect } from 'react';
import { invoiceService, DetailedBillingStats } from '../../services/invoiceService';
import logger from '@/utils/logger';

export interface BillingStatsFilters {
  startDate?: Date;
  endDate?: Date;
  type?: 'devis' | 'facture' | 'avoir' | 'all';
}

/**
 * Hook personnalisé pour gérer les statistiques de facturation
 */
export const useBillingStats = () => {
  const [stats, setStats] = useState<DetailedBillingStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<BillingStatsFilters>({ type: 'all' });

  const fetchStats = async (currentFilters?: BillingStatsFilters) => {
    setLoading(true);
    setError(null);
    try {
      logger.info('Récupération des statistiques de facturation...', currentFilters);
      const filtersToUse = currentFilters || filters;
      const data = await invoiceService.getBillingStats(filtersToUse);
      setStats(data);
      logger.info('Statistiques de facturation récupérées avec succès');
    } catch (err: any) {
      const errorMessage = err.message || 'Erreur lors de la récupération des statistiques';
      setError(errorMessage);
      logger.error('Erreur lors de la récupération des statistiques de facturation:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const updateFilters = (newFilters: BillingStatsFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    fetchStats(updatedFilters);
  };

  const exportToExcel = async () => {
    try {
      logger.info('Export des statistiques de facturation...');
      await invoiceService.exportBillingStatsToExcel(filters);
      logger.info('Export réussi');
    } catch (err: any) {
      logger.error('Erreur lors de l\'export des statistiques:', err);
      throw err;
    }
  };

  const exportToPDF = async () => {
    try {
      logger.info('Export des statistiques de facturation en PDF...');
      await invoiceService.exportBillingStatsToPDF(filters);
      logger.info('Export PDF réussi');
    } catch (err: any) {
      logger.error('Erreur lors de l\'export PDF des statistiques:', err);
      throw err;
    }
  };

  return { 
    stats, 
    loading, 
    error, 
    filters,
    updateFilters,
    refreshStats: () => fetchStats(), 
    exportToExcel,
    exportToPDF
  };
};

export default useBillingStats; 