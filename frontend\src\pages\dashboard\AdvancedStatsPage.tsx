import { useState, useEffect } from 'react';
import { advancedStatsService, AdvancedStats } from '../../services/advancedStats';
import { notify } from '../../components/Notification';
import DOMPurify from 'dompurify';
import { CircularProgress, FormControl, InputLabel, Select, MenuItem, Tabs, Tab, Box, Typography, styled, IconButton, Tooltip, Button, Alert } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { SERVICE_CATEGORIES } from './services/types';
import ClientConcentrationMap from './components/maps/ClientConcentrationMap';
import { Link } from 'react-router-dom';

// Tooltip component
interface StatsTooltipProps {
  title: string;
}

const StatsTooltip: React.FC<StatsTooltipProps> = ({ title }) => (
  <Tooltip 
    title={title} 
    arrow 
    placement="top"
    sx={{
      '& .MuiTooltip-tooltip': {
        backgroundColor: '#2D3748',
        color: 'white',
        fontSize: '0.875rem',
        padding: '8px 12px',
        borderRadius: '6px',
        maxWidth: '300px'
      }
    }}
  >
    <IconButton 
      size="small" 
      sx={{ 
        ml: 1, 
        color: '#94A3B8',
        padding: 0,
        lineHeight: 0,
        height: '18px',
        width: '18px',
        minWidth: '18px',
        minHeight: '18px',
        transform: 'translateY(-1px)', // Ajustement précis pour l'alignement vertical
      }}
    >
      <HelpOutlineIcon sx={{ fontSize: '0.9rem' }} />
    </IconButton>
  </Tooltip>
);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`stats-tabpanel-${index}`}
      aria-labelledby={`stats-tab-${index}`}
      {...other}
      className="py-4"
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `stats-tab-${index}`,
    'aria-controls': `stats-tabpanel-${index}`,
  };
}

// Add custom styles for MUI components
const customTabsStyles = {
  '& .MuiTab-root': { 
    color: '#666',
    fontWeight: 500,
    fontSize: '0.95rem',
    textTransform: 'none',
    minHeight: '48px',
    '&:hover': {
      color: '#FF7A35',
      opacity: 1,
    }
  },
  '& .Mui-selected': { 
    color: '#FF7A35 !important',
    fontWeight: 600
  },
  '& .MuiTabs-indicator': { 
    backgroundColor: '#FF7A35',
    height: '3px',
    borderRadius: '3px 3px 0 0'
  }
};

const customSelectStyles = {
  '& .MuiOutlinedInput-root': {
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: '#FF7A35',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: '#FF7A35',
    }
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: '#FF7A35',
  }
};

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
}));

export default function AdvancedStatsPage() {
  const [stats, setStats] = useState<AdvancedStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');
  const [tabValue, setTabValue] = useState(0);
  const [isPremium, setIsPremium] = useState(false); // Par défaut on suppose que l'utilisateur est premium

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getCategoryName = (id: string) => {
    const category = SERVICE_CATEGORIES.find(cat => cat.id === id);
    return category ? category.nom : id;
  };

  // Vérifier si les données reçues sont des données par défaut (tous les compteurs à 0)
  const checkIfDefaultData = (data: AdvancedStats) => {
    return (
      data.clientStats.total === 0 &&
      data.clientStats.nouveaux === 0 &&
      data.clientStats.tauxSatisfaction === 0 &&
      data.clientStats.croissanceMensuelle === 0 &&
      data.servicesPopulaires.length === 0 &&
      data.mesMissionsPopulaires.length === 0
    );
  };

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const data = await advancedStatsService.getStats(timeRange);
        setStats(data);
        
        // Vérifier si les données sont des données par défaut
        setIsPremium(!checkIfDefaultData(data));
      } catch (error) {
        notify('Erreur lors de la récupération des statistiques', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <CircularProgress />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center text-gray-600 py-8">
        Aucune donnée disponible
      </div>
    );
  }

  // Nettoyer les données pour éviter les XSS
  const sanitizedClientStats = {
    ...stats.clientStats,
    evolution: stats.clientStats.evolution.map(e => ({
      mois: DOMPurify.sanitize(e.mois),
      clients: e.clients,
    })),
  };

  const sanitizedServicesPopulaires = stats.servicesPopulaires.map(service => ({
    ...service,
    nom: DOMPurify.sanitize(service.nom),
  }));

  const sanitizedHeuresPopulaires = stats.heuresPopulaires.map(creneau => ({
    ...creneau,
    heure: DOMPurify.sanitize(creneau.heure),
  }));

  return (
    <div className="space-y-6 px-2 md:px-0">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg sticky top-0 z-50">
        <div className="p-4 border-b border-gray-100">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
            <div className="flex flex-col sm:flex-row items-start sm:items-center sm:space-x-4 w-full sm:w-auto mb-3 sm:mb-0">
              <PageTitle variant="h1" className="mb-3 sm:mb-0">
                Statistiques Avancées
              </PageTitle>
              <div className="hidden sm:block h-8 w-[2px] bg-gray-200 mx-2"></div>
              <FormControl 
                size="small" 
                className="min-w-[120px] mt-4 sm:mt-2"
                sx={{
                  ...customSelectStyles,
                  marginTop: { xs: '20px', sm: '12px' }
                }}
              >
                <InputLabel>Période</InputLabel>
                <Select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  label="Période"
                >
                  <MenuItem value="7">7 jours</MenuItem>
                  <MenuItem value="30">30 jours</MenuItem>
                  <MenuItem value="90">90 jours</MenuItem>
                </Select>
              </FormControl>
            </div>
            <div className="flex items-center w-full sm:w-auto justify-start sm:justify-end">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Mise à jour: {new Date().toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Message pour les utilisateurs sans abonnement premium */}
        {!isPremium && (
          <Alert 
            severity="info"
            className="m-4"
            action={
              <Button 
                color="inherit" 
                size="small" 
                component={Link} 
                to="/dashboard/premium"
              >
                Passer Premium
              </Button>
            }
          >
            Les statistiques avancées sont uniquement disponibles pour les utilisateurs Premium. Mettez à niveau votre compte pour accéder à ces données.
          </Alert>
        )}

        {/* Tabs Navigation */}
        <div className="px-2 sm:px-4 overflow-x-auto">
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '& .MuiTab-root': { 
                color: '#666',
                fontWeight: 500,
                fontSize: { xs: '0.85rem', sm: '0.95rem' },
                textTransform: 'none',
                minHeight: '48px',
                minWidth: { xs: '120px', sm: 'auto' },
                padding: { xs: '6px 10px', sm: '12px 16px' },
                '&:hover': {
                  color: '#FF7A35',
                  opacity: 1,
                }
              },
              '& .Mui-selected': { 
                color: '#FF7A35 !important',
                fontWeight: 600
              },
              '& .MuiTabs-indicator': { 
                backgroundColor: '#FF7A35',
                height: '3px',
                borderRadius: '3px 3px 0 0'
              }
            }}
            TabIndicatorProps={{
              children: <span className="MuiTabs-indicatorSpan" />
            }}
          >
            <Tab 
              label="Vue d'ensemble" 
              {...a11yProps(0)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
            <Tab 
              label="Performance Financière" 
              {...a11yProps(1)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
            <Tab 
              label="Services & Missions" 
              {...a11yProps(2)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
            <Tab 
              label="Analyses & Prévisions" 
              {...a11yProps(3)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
            <Tab 
              label="Répartition Géographique" 
              {...a11yProps(4)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
            <Tab 
              label="Facturation" 
              {...a11yProps(5)}
              icon={<span className="w-1.5 h-1.5 rounded-full bg-[#FF7A35] mr-2"></span>}
              iconPosition="start"
            />
          </Tabs>
        </div>
      </div>

      {/* Tab Panels */}
      <TabPanel value={tabValue} index={0}>
        {/* Vue d'ensemble des clients */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-gray-700">Total Clients</h3>
              <StatsTooltip title="Nombre total de clients uniques ayant eu au moins une mission acceptée avec vous" />
            </div>
            <div className="mt-2">
              <p className="text-3xl font-bold text-[#FF7A35]">{sanitizedClientStats.total}</p>
              <div className="flex items-center">
                <p className="text-sm text-green-600">+{sanitizedClientStats.nouveaux} ce mois</p>
                <StatsTooltip title="Nombre de nouveaux clients uniques ayant effectué leur première mission avec vous ce mois-ci" />
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-gray-700">Satisfaction Client</h3>
              <StatsTooltip title="Moyenne des notes reçues sur vos missions, convertie en pourcentage (note/5 × 100)" />
            </div>
            <div className="mt-2">
              <p className="text-3xl font-bold text-[#FF7A35]">{sanitizedClientStats.tauxSatisfaction}%</p>
              <div className="flex items-center">
                <p className="text-sm text-gray-500">Basé sur les avis</p>
                <StatsTooltip title="Calculé à partir des notes données par vos clients sur les missions terminées" />
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-gray-700">Croissance Mensuelle</h3>
              <StatsTooltip title="Évolution globale par rapport au mois précédent, calculée sur le nombre de missions, le montant total et les clients uniques" />
            </div>
            <div className="mt-2">
              <p className="text-3xl font-bold text-[#FF7A35]">
                {sanitizedClientStats.croissanceMensuelle}%
              </p>
              <div className="flex items-center">
                <p className="text-sm text-gray-500">vs mois dernier</p>
                <StatsTooltip title="Comparaison avec le mois précédent, prenant en compte le nombre de missions, le chiffre d'affaires et les nouveaux clients" />
              </div>
              <div className="mt-2 text-xs text-gray-500">
                Basé sur : nombre de missions, montant total et clients uniques
              </div>
            </div>
          </div>
        </div>

        {/* Taux additionnels */}
        <div className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Indicateurs clés</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Taux de conversion</h4>
                  <StatsTooltip title="Pourcentage de candidatures acceptées par rapport au nombre total de candidatures reçues" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.clientStats.tauxConversion ? `${stats.clientStats.tauxConversion}%` : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Visiteurs → Missions</p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Taux de rétention</h4>
                  <StatsTooltip title="Pourcentage de clients ayant effectué plus d'une mission avec vous" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.clientStats.tauxRetention ? `${stats.clientStats.tauxRetention}%` : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Clients qui reviennent</p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Délai moyen de réponse</h4>
                  <StatsTooltip title="Temps moyen entre la réception d'une candidature et votre première réponse (limité aux réponses sous 48h)" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.delaiMoyen?.formatte || 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Pour répondre aux candidatures</p>
              </div>
            </div>
          </div>
        </div>

        {/* Comparaison avec périodes précédentes */}
        <div className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Comparaison avec périodes précédentes</h2>
              <StatsTooltip title="Comparaison des performances entre la période actuelle et la période précédente" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Cette période vs période précédente */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Cette période vs précédente</h4>
                  <StatsTooltip title="Évolution en pourcentage des indicateurs clés par rapport à la période précédente" />
                </div>
                <div className="mt-4 space-y-3">
                  <div>
                    <div className="flex justify-between text-sm">
                      <div className="flex items-center">
                        <span>Chiffre d'affaires</span>
                        <StatsTooltip title="Évolution du montant total des missions entre les deux périodes" />
                      </div>
                      <span className={`font-semibold ${
                        stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant >
                        stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.montant
                        ? 'text-green-600'
                        : 'text-red-600'
                      }`}>
                        {(() => {
                          const current = stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant || 0;
                          const previous = stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.montant || 0;
                          
                          // Gestion spéciale quand previous est 0
                          const diff = previous === 0 && current > 0 
                            ? 100  // Si on passe de 0 à quelque chose, on affiche +100%
                            : previous === 0 && current === 0
                              ? 0   // Si on reste à 0, on affiche 0%
                              : ((current - previous) / previous) * 100;
                          
                          return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`;
                        })()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <div className="flex items-center">
                        <span>Nombre de missions</span>
                        <StatsTooltip title="Évolution du nombre total de missions entre les deux périodes" />
                      </div>
                      <span className={`font-semibold ${
                        stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions >
                        stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.missions
                        ? 'text-green-600'
                        : 'text-red-600'
                      }`}>
                        {(() => {
                          const current = stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions || 0;
                          const previous = stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.missions || 0;
                          
                          // Gestion spéciale quand previous est 0
                          const diff = previous === 0 && current > 0 
                            ? 100  // Si on passe de 0 à quelque chose, on affiche +100%
                            : previous === 0 && current === 0
                              ? 0   // Si on reste à 0, on affiche 0%
                              : ((current - previous) / previous) * 100;
                          
                          return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`;
                        })()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Moyenne par mission */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Moyenne par mission</h4>
                  <StatsTooltip title="Montant moyen facturé par mission sur la période (montant total / nombre de missions)" />
                </div>
                <div className="mt-4">
                  <div className="text-2xl font-bold text-[#FF7A35]">
                    {stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions > 0
                      ? Math.round(
                          stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant /
                          stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions
                        )
                      : 0}€
                  </div>
                  <div className="text-sm text-gray-500">
                    vs {stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.missions > 0
                      ? Math.round(
                          stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.montant /
                          stats.clientStats.evolution[stats.clientStats.evolution.length - 2]?.missions
                        )
                      : 0}€ période précédente
                  </div>
                </div>
              </div>

              {/* Prévisions */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Prévisions fin de mois</h4>
                  <StatsTooltip title="Estimation du chiffre d'affaires de fin de mois basée sur les performances actuelles et le nombre de jours restants" />
                </div>
                <div className="mt-4">
                  <div className="text-2xl font-bold text-[#FF7A35]">
                    {(() => {
                      const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1];
                      const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
                      const dayOfMonth = new Date().getDate();
                      const projection = Math.round((currentMonth?.montant || 0) * (daysInMonth / dayOfMonth));
                      return `${projection}€`;
                    })()}
                  </div>
                  <div className="text-sm text-gray-500">
                    Basé sur le rythme actuel
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Performance Financière */}
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Performance Financière</h2>
              <StatsTooltip title="Analyse détaillée de vos performances financières sur la période sélectionnée concernant vos missions" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Revenu Mensuel</h4>
                  <StatsTooltip title="Montant total des missions réalisées sur la période en cours" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant || 0}€
                </p>
                <div className="flex items-center">
                  <p className="text-sm text-gray-500">Ce mois</p>
                  <StatsTooltip title="Revenus générés depuis le début du mois en cours" />
                </div>
              </div>
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Ticket Moyen</h4>
                  <StatsTooltip title="Montant moyen facturé par mission sur la période" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions > 0 
                    ? Math.round(stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant / 
                               stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions)
                    : 0}€
                </p>
                <div className="flex items-center">
                  <p className="text-sm text-gray-500">Par mission</p>
                  <StatsTooltip title="Calculé en divisant le revenu total par le nombre de missions" />
                </div>
              </div>
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Missions Réalisées</h4>
                  <StatsTooltip title="Nombre total de missions effectuées sur la période en cours" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions || 0}
                </p>
                <p className="text-sm text-gray-500">Ce mois</p>
              </div>
            </div>
          </div>

          {/* Graphique d'évolution */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Évolution sur 6 mois</h2>
              <StatsTooltip title="Visualisation mensuelle de vos revenus et du nombre de missions sur les 6 derniers mois" />
            </div>
            <div className="grid grid-cols-6 gap-4 mt-4">
              {stats.clientStats.evolution.map((month, index) => (
                <div key={index} className="text-center">
                  <div className="relative pt-1">
                    <div className="flex items-center justify-center">
                      <div className="text-xs font-semibold inline-block text-[#FF7A35]">
                        {month.montant}€
                      </div>
                    </div>
                    <div className="flex flex-col-reverse h-32">
                      <div
                        className="w-full bg-[#FF7A35] rounded-t"
                        style={{ 
                          height: `${(month.montant / Math.max(...stats.clientStats.evolution.map(m => m.montant))) * 100}%` 
                        }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">{month.mois}</p>
                    <p className="text-xs text-gray-500">{month.missions} mission{month.missions > 1 ? 's' : ''}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Prévisions Financières */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Prévisions Financières</h2>
              <StatsTooltip title="Estimations basées sur vos performances actuelles et historiques" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Prochain mois</h4>
                  <StatsTooltip title="Estimation du chiffre d'affaires pour le mois prochain basée sur la tendance actuelle" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.previsions?.prochainMois ? `${stats.previsions.prochainMois}€` : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Estimation basée sur les tendances</p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">À 3 mois</h4>
                  <StatsTooltip title="Projection du chiffre d'affaires sur les 3 prochains mois" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.previsions?.troisMois ? `${stats.previsions.troisMois}€` : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Projection trimestrielle</p>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Tendance générale</h4>
                  <StatsTooltip title="Direction globale de votre activité basée sur l'analyse des 6 derniers mois" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {stats.previsions?.tendance === 'hausse' ? '↗️ En hausse' : 
                   stats.previsions?.tendance === 'baisse' ? '↘️ En baisse' : 
                   stats.previsions?.tendance === 'stable' ? '➡️ Stable' : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Basé sur les 6 derniers mois</p>
              </div>
            </div>
          </div>
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        {/* Services et Missions */}
        <div className="space-y-6">
          {/* Services les plus populaires */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Services les Plus Populaires (Candidatures)</h2>
              <StatsTooltip title="Classement de vos services selon le nombre de candidatures reçues, avec indicateurs de performance" />
            </div>
            <div className="space-y-4">
              {sanitizedServicesPopulaires.length > 0 ? (
                sanitizedServicesPopulaires.map((service, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="font-medium text-gray-800">{getCategoryName(service.nom)}</h4>
                        <StatsTooltip title={`Taux de complétion: ${((service.reservations / sanitizedServicesPopulaires[0].reservations) * 100).toFixed(1)}% par rapport au service le plus demandé`} />
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                        <div 
                          className="bg-[#FF7A35] h-2.5 rounded-full" 
                          style={{ width: `${(service.reservations / sanitizedServicesPopulaires[0].reservations) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-4 text-right">
                      <p className="font-semibold">{service.reservations} mission{service.reservations > 1 ? 's' : ''}</p>
                      <p className="text-sm text-gray-500">{service.satisfaction ? `${service.satisfaction}% satisf.` : 'Pas de note'}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-gray-500 text-center py-2 mb-4">Aucune donnée de candidature disponible</p>
                  {[
                    { nom: 'Services à domicile', reservations: 0 },
                    { nom: 'Ménage', reservations: 0 },
                    { nom: 'Garde d\'enfants', reservations: 0 },
                    { nom: 'Bricolage', reservations: 0 }
                  ].map((service, index) => (
                    <div key={index} className="flex items-center justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="font-medium text-gray-800">{service.nom}</h4>
                          <StatsTooltip title="Aucune donnée disponible pour ce service" />
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                          <div className="bg-gray-300 h-2.5 rounded-full w-0"></div>
                        </div>
                      </div>
                      <div className="ml-4 text-right">
                        <p className="font-semibold">0 mission</p>
                        <p className="text-sm text-gray-500">Pas de note</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Services les plus populaires (Mes Missions) */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Services les Plus Populaires (Mes Missions)</h2>
              <StatsTooltip title="Services pour lesquels vous avez créé le plus de missions, avec leur taux de satisfaction" />
            </div>
            <div className="space-y-4">
              {stats.mesMissionsPopulaires.length > 0 ? (
                stats.mesMissionsPopulaires.map((service, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="font-medium text-gray-800">{getCategoryName(service.nom)}</h4>
                        <StatsTooltip title={`Taux de complétion: ${((service.reservations / stats.mesMissionsPopulaires[0].reservations) * 100).toFixed(1)}% par rapport au service le plus créé`} />
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                        <div 
                          className="bg-[#FF7A35] h-2.5 rounded-full" 
                          style={{ width: `${(service.reservations / stats.mesMissionsPopulaires[0].reservations) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="ml-4 text-right">
                      <p className="font-semibold">{service.reservations} mission{service.reservations > 1 ? 's' : ''}</p>
                      <p className="text-sm text-gray-500">{service.satisfaction ? `${service.satisfaction}% satisf.` : 'Pas de note'}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-gray-500 text-center py-2 mb-4">Aucune mission créée pour le moment</p>
                  {[
                    { nom: 'Services à domicile', reservations: 0 },
                    { nom: 'Ménage', reservations: 0 },
                    { nom: 'Garde d\'enfants', reservations: 0 },
                    { nom: 'Bricolage', reservations: 0 }
                  ].map((service, index) => (
                    <div key={index} className="flex items-center justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="font-medium text-gray-800">{service.nom}</h4>
                          <StatsTooltip title="Aucune donnée disponible pour ce service" />
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                          <div className="bg-gray-300 h-2.5 rounded-full w-0"></div>
                        </div>
                      </div>
                      <div className="ml-4 text-right">
                        <p className="font-semibold">0 mission</p>
                        <p className="text-sm text-gray-500">Pas de note</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Heures de création de missions */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Heures de Création des Missions</h2>
              <StatsTooltip title="Répartition horaire de la création de vos missions, permettant d'identifier les heures les plus actives" />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {sanitizedHeuresPopulaires.length > 0 ? (
                sanitizedHeuresPopulaires.map((creneau, index) => (
                  <div key={index} className="text-center">
                    <div className="relative pt-1">
                      <div className="flex items-center justify-center">
                        <div className="text-xs font-semibold inline-block text-[#FF7A35]">
                          {creneau.reservations} mission{creneau.reservations > 1 ? 's' : ''}
                        </div>
                      </div>
                      <div className="flex flex-col-reverse h-32">
                        <div
                          className="w-full bg-[#FF7A35] rounded-t"
                          style={{ height: `${(creneau.reservations / Math.max(...sanitizedHeuresPopulaires.map(h => h.reservations))) * 100}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">{creneau.heure}</p>
                    </div>
                  </div>
                ))
              ) : (
                <>
                  <div className="col-span-4 text-center pb-4">
                    <p className="text-gray-500">Aucune donnée disponible sur les heures de création</p>
                  </div>
                  {['08h-12h', '12h-14h', '14h-18h', '18h-22h'].map((creneau, index) => (
                    <div key={index} className="text-center">
                      <div className="relative pt-1">
                        <div className="flex items-center justify-center">
                          <div className="text-xs font-semibold inline-block text-gray-400">
                            0 mission
                          </div>
                        </div>
                        <div className="flex flex-col-reverse h-32">
                          <div className="w-full bg-gray-200 rounded-t h-0"></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">{creneau}</p>
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
            <div className="text-xs text-gray-500 mt-4 text-center">
              Les heures indiquées correspondent aux heures exactes de création des missions
            </div>
          </div>

          {/* Jours de la semaine */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Jours de Création des Missions</h2>
              <StatsTooltip title="Répartition des missions par jour de la semaine, montrant les jours les plus actifs" />
            </div>
            <div className="grid grid-cols-7 gap-2">
              {stats.joursSemaine && stats.joursSemaine.length > 0 ? (
                stats.joursSemaine.map((jour, index) => (
                  <div key={index} className="text-center">
                    <div className="relative pt-1">
                      <div className="flex items-center justify-center">
                        <div className="text-xs font-semibold inline-block text-[#FF7A35]">
                          {jour.reservations} mission{jour.reservations > 1 ? 's' : ''}
                        </div>
                      </div>
                      <div className="flex flex-col-reverse h-24">
                        <div
                          className="w-full bg-[#FF7A35] rounded-t"
                          style={{ height: `${jour.pourcentage}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">{jour.jour}</p>
                    </div>
                  </div>
                ))
              ) : (
                <>
                  <div className="col-span-7 text-center pb-4">
                    <p className="text-gray-500">Aucune donnée disponible sur les jours de création</p>
                  </div>
                  {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map((jour, index) => (
                    <div key={index} className="text-center">
                      <div className="relative pt-1">
                        <div className="flex items-center justify-center">
                          <div className="text-xs font-semibold inline-block text-gray-400">
                            0 mission
                          </div>
                        </div>
                        <div className="flex flex-col-reverse h-24">
                          <div className="w-full bg-gray-200 rounded-t h-0"></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">{jour}</p>
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>

          {/* Performance détaillée des services */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Performance Détaillée des Services</h2>
              <StatsTooltip title="Analyse approfondie des performances de chaque service avec tous les indicateurs clés" />
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        Service
                        <StatsTooltip title="Catégorie de service proposée" />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        Revenu Total
                        <StatsTooltip title="Somme totale générée par ce service sur la période" />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        Missions/Mois
                        <StatsTooltip title="Nombre moyen de missions réalisées par mois pour ce service" />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        Croissance
                        <StatsTooltip title="Évolution du revenu par rapport à la période précédente en pourcentage" />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        Satisfaction
                        <StatsTooltip title="Note moyenne de satisfaction des clients pour ce service (sur 100)" />
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.performanceServices && stats.performanceServices.length > 0 ? (
                    stats.performanceServices.map((service, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {getCategoryName(service.nomService)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {service.revenuTotal}€
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {service.missionsMoyennes.toFixed(1)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={service.croissance >= 0 ? 'text-green-600' : 'text-red-600'}>
                            {service.croissance > 0 ? '+' : ''}{service.croissance}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {service.satisfaction}%
                        </td>
                      </tr>
                    ))
                  ) : (
                    [
                      { nom: 'Services à domicile', revenu: 0, missions: 0, croissance: 0, satisfaction: 0 },
                      { nom: 'Ménage', revenu: 0, missions: 0, croissance: 0, satisfaction: 0 },
                      { nom: 'Garde d\'enfants', revenu: 0, missions: 0, croissance: 0, satisfaction: 0 },
                      { nom: 'Bricolage', revenu: 0, missions: 0, croissance: 0, satisfaction: 0 }
                    ].map((service, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {service.nom}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          0€
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          0.0
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="text-gray-500">0%</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          0%
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        {/* Analyses et Prévisions */}
        <div className="space-y-6">
          {/* Analyse des Tendances */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Analyse des Tendances</h2>
              <StatsTooltip title="Analyse détaillée des tendances de votre activité et identification des patterns" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Répartition des revenus */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Répartition des Revenus par Service</h4>
                  <StatsTooltip title="Pourcentage du revenu total généré par chaque service" />
                </div>
                <div className="space-y-4">
                  {stats.servicesPopulaires.length > 0 ? (
                    stats.servicesPopulaires.map((service, index) => (
                      <div key={index}>
                        <div className="flex justify-between text-sm mb-1">
                          <div className="flex items-center">
                            <span>{getCategoryName(service.nom)}</span>
                            <StatsTooltip title={`${service.reservations} missions réalisées, représentant ${Math.round((service.reservations / stats.servicesPopulaires.reduce((acc, s) => acc + s.reservations, 0)) * 100)}% du total`} />
                          </div>
                          <span className="font-medium">
                            {Math.round((service.reservations / stats.servicesPopulaires.reduce((acc, s) => acc + s.reservations, 0)) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-[#FF7A35] h-2 rounded-full"
                            style={{
                              width: `${(service.reservations / stats.servicesPopulaires.reduce((acc, s) => acc + s.reservations, 0)) * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div>
                      <p className="text-gray-500 text-center py-2 mb-4">Aucune donnée disponible pour le moment</p>
                      {[
                        { nom: 'Services à domicile', pourcentage: 0 },
                        { nom: 'Ménage', pourcentage: 0 },
                        { nom: 'Garde d\'enfants', pourcentage: 0 },
                        { nom: 'Bricolage', pourcentage: 0 }
                      ].map((service, index) => (
                        <div key={index}>
                          <div className="flex justify-between text-sm mb-1">
                            <div className="flex items-center">
                              <span>{service.nom}</span>
                              <StatsTooltip title="Aucune donnée disponible pour ce service" />
                            </div>
                            <span className="font-medium">0%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-gray-300 h-2 rounded-full w-0"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Taux de complétion */}
              <div>
                <h4 className="text-sm font-medium text-gray-600 mb-4">Performance sur 6 mois</h4>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <div className="flex items-center">
                        <span>Missions Moyennes/Mois</span>
                        <StatsTooltip title="Nombre moyen de missions effectuées par mois sur les 6 derniers mois" />
                      </div>
                      <span className="font-medium">
                        {(stats.clientStats.evolution.reduce((acc, month) => acc + month.missions, 0) / stats.clientStats.evolution.length).toFixed(1)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#FF7A35] h-2 rounded-full"
                        style={{
                          width: `${(stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.missions / 
                            Math.max(...stats.clientStats.evolution.map(m => m.missions + 0.0001))) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <div className="flex items-center">
                        <span>CA Moyen/Mois</span>
                        <StatsTooltip title="Chiffre d'affaires moyen mensuel calculé sur les 6 derniers mois" />
                      </div>
                      <span className="font-medium">
                        {Math.round(stats.clientStats.evolution.reduce((acc, month) => acc + month.montant, 0) / stats.clientStats.evolution.length)}€
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#FF7A35] h-2 rounded-full"
                        style={{
                          width: `${(stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant / 
                            Math.max(...stats.clientStats.evolution.map(m => m.montant + 0.0001))) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <div className="flex items-center">
                        <span>Clients Actifs Moyens</span>
                        <StatsTooltip title="Nombre moyen de clients uniques actifs chaque mois sur les 6 derniers mois" />
                      </div>
                      <span className="font-medium">
                        {(stats.clientStats.evolution.reduce((acc, month) => acc + month.clients, 0) / stats.clientStats.evolution.length).toFixed(1)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#FF7A35] h-2 rounded-full"
                        style={{
                          width: `${(stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.clients / 
                            Math.max(...stats.clientStats.evolution.map(m => m.clients + 0.0001))) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Indicateurs de Performance Détaillés */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Indicateurs de Performance Détaillés</h2>
              <StatsTooltip title="Analyse approfondie des indicateurs clés de performance de votre activité" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Missions */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Détails des Missions</h4>
                  <StatsTooltip title="Métriques importantes concernant la performance de vos missions" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Taux de Conversion</span>
                      <StatsTooltip title="Pourcentage de visiteurs qui deviennent des clients (nombre de clients / nombre de missions total)" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {stats.clientStats.evolution.reduce((acc, month) => acc + month.missions, 0) > 0 
                        ? Math.round((stats.clientStats.total / (stats.clientStats.evolution.reduce((acc, month) => acc + month.missions, 0))) * 100) 
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Missions par Client</span>
                      <StatsTooltip title="Nombre moyen de missions effectuées par chaque client (total des missions / nombre de clients)" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {stats.clientStats.total > 0 
                        ? (stats.clientStats.evolution.reduce((acc, month) => acc + month.missions, 0) / stats.clientStats.total).toFixed(1)
                        : '0.0'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Taux de Satisfaction Moyen</span>
                      <StatsTooltip title="Moyenne des taux de satisfaction client sur tous vos services" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {stats.servicesPopulaires.length > 0 
                        ? Math.round(stats.servicesPopulaires.reduce((acc, service) => acc + (service.satisfaction || 0), 0) / 
                          stats.servicesPopulaires.filter(service => service.satisfaction !== null).length || 1)
                        : 0}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Revenus */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Analyse des Revenus</h4>
                  <StatsTooltip title="Indicateurs financiers détaillés de votre activité" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Revenu par Client</span>
                      <StatsTooltip title="Montant moyen généré par chaque client (revenu total / nombre de clients)" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {stats.clientStats.total > 0 
                        ? Math.round(stats.clientStats.evolution.reduce((acc, month) => acc + month.montant, 0) / stats.clientStats.total)
                        : 0}€
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Meilleur Mois</span>
                      <StatsTooltip title="Montant du mois ayant généré le plus haut chiffre d'affaires" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {Math.max(...stats.clientStats.evolution.map(m => m.montant))}€
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-600">Progression Annuelle</span>
                      <StatsTooltip title="Évolution du chiffre d'affaires entre le premier et le dernier mois de la période" />
                    </div>
                    <span className="font-medium text-[#FF7A35]">
                      {(() => {
                        const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant || 0;
                        const firstMonth = stats.clientStats.evolution[0]?.montant || 0;
                        
                        if (currentMonth === 0 && firstMonth === 0) {
                          return '0%';
                        } else if (firstMonth === 0 && currentMonth > 0) {
                          return '+100%';
                        } else if (firstMonth > 0) {
                          return `${currentMonth >= firstMonth ? '+' : ''}${Math.round(((currentMonth - firstMonth) / firstMonth) * 100)}%`;
                        } else {
                          return '0%';
                        }
                      })()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recommandations */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Recommandations</h2>
              <StatsTooltip title="Suggestions personnalisées basées sur l'analyse de vos données pour optimiser votre activité" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Meilleur moment pour poster */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Meilleur moment pour poster</h4>
                  <StatsTooltip title="Créneau horaire où vos missions ont le plus de chances d'être vues et acceptées" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {(() => {
                    const heureMax = Object.entries(stats.heuresPopulaires.reduce((acc, h) => {
                      acc[h.heure] = (acc[h.heure] || 0) + h.reservations;
                      return acc;
                    }, {} as Record<string, number>))
                    .sort(([,a], [,b]) => b - a)[0];
                    return heureMax ? heureMax[0] : 'N/A';
                  })()}
                </p>
                <p className="text-xs text-gray-500 mt-1">Basé sur l'historique des missions</p>
              </div>

              {/* Service le plus rentable */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Service le plus rentable</h4>
                  <StatsTooltip title="Service générant le meilleur rapport entre nombre de missions et revenu" />
                </div>
                <p className="text-lg font-bold text-[#FF7A35]">
                  {stats.servicesPopulaires[0] ? getCategoryName(stats.servicesPopulaires[0].nom) : 'N/A'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.servicesPopulaires[0] ? `${stats.servicesPopulaires[0].reservations} missions` : ''}
                </p>
              </div>

              {/* Potentiel de croissance */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Potentiel de croissance</h4>
                  <StatsTooltip title="Estimation du potentiel d'augmentation de votre activité basée sur vos meilleures performances" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {(() => {
                    const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1];
                    const maxMonth = Math.max(...stats.clientStats.evolution.map(m => m.montant));
                    return currentMonth?.montant < maxMonth ? '+' + Math.round(((maxMonth - currentMonth.montant) / currentMonth.montant) * 100) + '%' : 'Max';
                  })()}
                </p>
                <p className="text-xs text-gray-500 mt-1">Par rapport à votre meilleur mois</p>
              </div>
            </div>
          </div>

          {/* Objectifs */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Objectifs</h2>
              <StatsTooltip title="Suivi de vos objectifs mensuels et annuels avec le taux de réalisation" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Objectif mensuel</h4>
                  <StatsTooltip title="Progression vers votre objectif de chiffre d'affaires mensuel" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Progression</span>
                    <span className="font-medium text-[#FF7A35]">
                      {(() => {
                        const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant || 0;
                        const objectif = Math.max(...stats.clientStats.evolution.map(m => m.montant)) * 1.1; // +10% du meilleur mois
                        return Math.min(100, Math.round((currentMonth / objectif) * 100));
                      })()}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#FF7A35] h-2 rounded-full"
                      style={{
                        width: `${(() => {
                          const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1]?.montant || 0;
                          const objectif = Math.max(...stats.clientStats.evolution.map(m => m.montant)) * 1.1;
                          return Math.min(100, Math.round((currentMonth / objectif) * 100));
                        })()}%`
                      }}
                    ></div>
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Objectif annuel</h4>
                  <StatsTooltip title="Progression vers votre objectif de chiffre d'affaires annuel" />
                </div>
                <p className="text-2xl font-bold text-[#FF7A35]">
                  {(() => {
                    const moyenne = stats.clientStats.evolution.reduce((acc, month) => acc + month.montant, 0) / stats.clientStats.evolution.length;
                    return `${Math.round(moyenne * 12)}€`;
                  })()}
                </p>
                <p className="text-xs text-gray-500 mt-1">Basé sur la moyenne mensuelle actuelle</p>
              </div>
            </div>
          </div>

          {/* Prévisions */}
          <div className="bg-white p-6 rounded-lg shadow mt-6">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Prévisions</h2>
              <StatsTooltip title="Estimations basées sur vos performances actuelles et historiques" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Prévision fin de mois</h4>
                  <StatsTooltip title="Estimation du chiffre d'affaires total à la fin du mois basée sur votre rythme actuel" />
                </div>
                <div className="mt-4">
                  <div className="text-2xl font-bold text-[#FF7A35]">
                    {(() => {
                      const currentMonth = stats.clientStats.evolution[stats.clientStats.evolution.length - 1];
                      const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
                      const dayOfMonth = new Date().getDate();
                      const projection = Math.round((currentMonth?.montant || 0) * (daysInMonth / dayOfMonth));
                      return `${projection}€`;
                    })()}
                  </div>
                  <div className="text-sm text-gray-500">
                    Basé sur le rythme actuel
                  </div>
                </div>
              </div>

              {/* Tendance */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="text-sm font-medium text-gray-600">Tendance</h4>
                <p className="text-lg font-bold text-[#FF7A35]">
                  {(() => {
                    const derniersMois = stats.clientStats.evolution.slice(-3);
                    const moyenne = derniersMois.reduce((acc, month) => acc + month.montant, 0) / derniersMois.length;
                    const moyennePrecedente = stats.clientStats.evolution.slice(-6, -3).reduce((acc, month) => acc + month.montant, 0) / 3;
                    const evolution = ((moyenne - moyennePrecedente) / moyennePrecedente) * 100;
                    return evolution > 0 ? '↗️ En hausse' : evolution < 0 ? '↘️ En baisse' : '➡️ Stable';
                  })()}
                </p>
                <p className="text-xs text-gray-500 mt-1">Comparaison sur les 3 derniers mois</p>
              </div>
            </div>
          </div>
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        {/* Répartition Géographique */}
        <div className="space-y-6">
          {/* Régions principales */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Répartition Géographique</h2>
              <StatsTooltip title="Analyse de la distribution géographique de vos clients et de leur concentration par région" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Régions Principales</h4>
                  <StatsTooltip title="Top 5 des départements où vous avez le plus de clients actifs, avec leur pourcentage respectif" />
                </div>
                <div className="space-y-4">
                  {stats.clientStats.regionsPrincipales ? stats.clientStats.regionsPrincipales.map((region, index) => (
                    <div key={index}>
                      <div className="flex justify-between text-sm mb-1">
                        <div className="flex items-center">
                          <span>{region.nom}</span>
                          <StatsTooltip title={`${region.pourcentage}% de vos clients sont situés dans cette région. Cette région représente une part significative de votre activité.`} />
                        </div>
                        <span className="font-medium">{region.pourcentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-[#FF7A35] h-2 rounded-full"
                          style={{ width: `${region.pourcentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )) : (
                    <div className="text-center text-gray-500 py-4">
                      Données non disponibles
                    </div>
                  )}
                </div>
              </div>

              {/* Cartographie des clients */}
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Concentration des Clients</h4>
                  <StatsTooltip title="Visualisation interactive de la répartition géographique de vos clients sur la carte de France, permettant d'identifier les zones de forte concentration" />
                </div>
                <div className="bg-white rounded-lg h-64">
                  {stats.clientStats.regionsPrincipales && stats.clientStats.regionsPrincipales.length > 0 ? (
                    <ClientConcentrationMap regions={stats.clientStats.regionsPrincipales} />
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <p className="text-gray-500 text-center">Aucune donnée disponible</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Taux de complétion */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Taux de Complétion des Missions</h2>
              <StatsTooltip title="Analyse détaillée du pourcentage de missions menées à terme avec succès, ventilé par région et période" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600">Taux Global</h4>
                  <StatsTooltip title="Pourcentage total de missions terminées avec succès sur l'ensemble des régions, indicateur clé de votre performance globale" />
                </div>
                <div className="relative pt-1">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-xs font-semibold inline-block text-[#FF7A35]">
                        {stats.tauxCompletion?.pourcentage ?? 'N/A'}%
                      </span>
                    </div>
                  </div>
                  <div className="overflow-hidden h-4 mb-4 text-xs flex rounded-full bg-gray-200">
                    <div
                      style={{ width: `${stats.tauxCompletion?.pourcentage ?? 0}%` }}
                      className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-[#FF7A35]"
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    Pourcentage de missions terminées avec succès
                  </p>
                </div>
              </div>

              <div>
                <div className="flex items-center">
                  <h4 className="text-sm font-medium text-gray-600 mb-4">Évolution du Taux de Complétion</h4>
                  <StatsTooltip title="Suivi mensuel du taux de complétion des missions, permettant d'identifier les tendances et les variations saisonnières" />
                </div>
                <div className="grid grid-cols-6 gap-2">
                  {stats.tauxCompletion?.evolution ? stats.tauxCompletion.evolution.map((mois, index) => (
                    <div key={index} className="text-center">
                      <div className="relative pt-1">
                        <div className="flex flex-col-reverse h-20">
                          <div
                            className="w-full bg-[#FF7A35] rounded-t"
                            style={{ height: `${mois.taux}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-600 mt-2">{mois.mois}</p>
                        <p className="text-xs font-medium">
                          <span className="flex items-center justify-center">
                            {mois.taux}%
                            <StatsTooltip title={`En ${mois.mois}, ${mois.taux}% des missions ont été complétées avec succès. ${
                              mois.taux > 80 ? 'Excellent taux de complétion!' :
                              mois.taux > 60 ? 'Bon taux de complétion.' :
                              'Des améliorations sont possibles.'
                            }`} />
                          </span>
                        </p>
                      </div>
                    </div>
                  )) : (
                    <div className="col-span-6 text-center text-gray-500 py-4">
                      Données non disponibles
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Aperçu de la Facturation</h2>
              <StatsTooltip title="Résumé de votre activité de facturation" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-500 text-sm">Nombre total de documents</p>
                <p className="text-2xl font-bold text-[#FF7A35]">{stats?.invoiceStats?.total || 0}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-500 text-sm">Montant total facturé</p>
                <p className="text-2xl font-bold text-[#FF7A35]">{stats?.invoiceStats ? (stats.invoiceStats.montantTotal).toFixed(2) : 0}€</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-500 text-sm">Factures payées</p>
                <p className="text-2xl font-bold text-[#FF7A35]">{stats?.invoiceStats?.parStatut?.paye || 0}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-500 text-sm">Devis acceptés</p>
                <p className="text-2xl font-bold text-[#FF7A35]">{stats?.invoiceStats?.parStatut?.accepte || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold mb-4">Évolution Mensuelle</h2>
              <StatsTooltip title="Évolution de votre chiffre d'affaires sur les 6 derniers mois" />
            </div>
            <div className="h-64">
              {stats?.invoiceStats?.evolution && stats.invoiceStats.evolution.length > 0 ? (
                <div className="grid grid-cols-6 h-full items-end gap-2">
                  {stats.invoiceStats.evolution.map((month, index) => (
                    <div key={index} className="flex flex-col items-center h-full justify-end">
                      <div className="flex flex-col items-center">
                        <span className="text-xs font-medium">{month.montant}€</span>
                        <span className="text-xs font-medium">({month.nombre})</span>
                      </div>
                      <div
                        className="w-full bg-[#FF7A35] rounded-t-sm"
                        style={{
                          height: `${Math.max(
                            8,
                            (month.montant / Math.max(...(stats.invoiceStats?.evolution?.map(m => m.montant || 1) || [1]))) * 70
                          )}%`
                        }}
                      ></div>
                      <span className="text-xs mt-2">{month.mois.split('-')[1]}/{month.mois.split('-')[0].slice(2)}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500">Aucune donnée disponible</p>
                </div>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <h2 className="text-xl font-semibold mb-4">Répartition par Type</h2>
                <StatsTooltip title="Distribution de vos documents par type" />
              </div>
              <div className="space-y-4">
                {stats?.invoiceStats?.parType && Object.keys(stats.invoiceStats.parType).length > 0 ? (
                  Object.entries(stats.invoiceStats.parType).map(([type, count], index) => (
                    <div key={index}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">{type === 'facture' ? 'Factures' : type === 'devis' ? 'Devis' : type === 'avoir' ? 'Avoirs' : type}</span>
                        <span className="font-medium">{count} ({stats.invoiceStats?.total && stats.invoiceStats.total > 0 ? Math.round(((count as number) / stats.invoiceStats.total) * 100) : 0}%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-[#FF7A35] h-2 rounded-full"
                          style={{
                            width: `${stats.invoiceStats?.total && stats.invoiceStats.total > 0 ? ((count as number) / stats.invoiceStats.total) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">Aucune donnée disponible</p>
                )}
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center">
                <h2 className="text-xl font-semibold mb-4">Répartition par Statut</h2>
                <StatsTooltip title="Distribution de vos documents par statut" />
              </div>
              <div className="space-y-4">
                {stats?.invoiceStats?.parStatut && Object.keys(stats.invoiceStats.parStatut).length > 0 ? (
                  Object.entries(stats.invoiceStats.parStatut).map(([statut, count], index) => (
                    <div key={index}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="capitalize">
                          {statut === 'paye' ? 'Payées' : 
                           statut === 'envoye' ? 'Envoyées' : 
                           statut === 'accepte' ? 'Acceptés' : 
                           statut === 'brouillon' ? 'Brouillons' : 
                           statut === 'refuse' ? 'Refusés' : 
                           statut === 'en_retard' ? 'En retard' : statut}
                        </span>
                        <span className="font-medium">{count} ({stats.invoiceStats?.total && stats.invoiceStats.total > 0 ? Math.round(((count as number) / stats.invoiceStats.total) * 100) : 0}%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            statut === 'paye' ? 'bg-green-500' : 
                            statut === 'en_retard' ? 'bg-red-500' : 
                            statut === 'accepte' ? 'bg-blue-500' : 
                            statut === 'refuse' ? 'bg-gray-500' : 
                            'bg-[#FF7A35]'
                          }`}
                          style={{
                            width: `${stats.invoiceStats?.total && stats.invoiceStats.total > 0 ? ((count as number) / stats.invoiceStats.total) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">Aucune donnée disponible</p>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex justify-center my-8">
            <Link to="/dashboard/facturation">
              <Button 
                variant="contained" 
                sx={{ 
                  bgcolor: '#FF7A35', 
                  '&:hover': { bgcolor: '#E16B28' },
                  fontWeight: 'bold' 
                }}
              >
                Accéder à la facturation
              </Button>
            </Link>
          </div>
        </div>
      </TabPanel>
    </div>
  );
}