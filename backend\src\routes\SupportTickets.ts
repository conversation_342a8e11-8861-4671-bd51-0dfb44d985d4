import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authMiddleware } from '../middleware/authMiddleware';
import multer from 'multer';
import * as ticketController from '../controllers/supportTickets';
import * as commentController from '../controllers/supportTicketComments';
import * as tagController from '../controllers/supportTicketTags';
import * as templateController from '../controllers/supportResponseTemplates';
import { getTicketAttachments, addAttachmentToTicket, removeAttachmentFromTicket } from '../controllers/supportTicketAttachments';
import logger from '../utils/logger';
import { Request, Response, NextFunction } from 'express';

// Configuration améliorée pour multer (en mémoire)
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { 
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // Maximum 5 fichiers
  },
  fileFilter: (req, file, cb) => {
    // Vérifier le type MIME
    const allowedMimeTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
      'application/pdf', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error(`Type de fichier non autorisé: ${file.mimetype}`));
    }

    cb(null, true);
  }
});

// Middleware pour gérer les erreurs de Multer
const handleMulterError = (err: Error | null, req: Request, res: Response, next: NextFunction) => {
  if (err) {
    logger.error('Erreur lors de l\'upload', { error: err });

    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        res.status(400).json({ 
          success: false, 
          message: 'Fichier trop volumineux. La taille maximale est de 10MB.' 
        });
      }
      
      if (err.code === 'LIMIT_FILE_COUNT') {
        res.status(400).json({ 
          success: false, 
          message: 'Trop de fichiers. Maximum 5 fichiers autorisés.' 
        });
      }

      if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        res.status(400).json({
          success: false,
          message: 'Champ de fichier inattendu. Utilisez le champ "files".'
        });
      }
      
      res.status(400).json({ 
        success: false, 
        message: 'Erreur lors de l\'upload du fichier', 
        error: err.message 
      });
    }
    
    // Erreur de type MIME ou autre
    res.status(400).json({ 
      success: false, 
      message: err.message || 'Erreur lors de l\'upload du fichier'
    });
  }
  
  next();
};

const router = Router();

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Middleware de validation des requêtes
const validate = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Erreur de validation',
      errors: errors.array()
    });
    return;
  }
  next();
};

// Fonction pour vérifier les rôles
const checkRole = (allowedRoles: string[]) => async (req: Request, res: Response, next: NextFunction) => {
  if (!req.user || !req.user.role || !allowedRoles.includes(req.user.role)) {
    res.status(403).json({
      success: false,
      message: 'Accès refusé. Vous n\'avez pas les permissions nécessaires.'
    });
    return;
  }
  next();
};

// Routes pour les tickets
router.post(
  '/',
  [
    body('title').trim().notEmpty().withMessage('Le titre est requis'),
    body('description').trim().notEmpty().withMessage('La description est requise'),
    body('priority')
      .isIn(['faible', 'normale', 'elevee', 'urgente'])
      .withMessage('Priorité invalide'),
    body('category')
      .isIn(['technique', 'facturation', 'compte', 'mission', 'autre'])
      .withMessage('Catégorie invalide'),
  ],
  validate,
  upload.array('files', 5),
  handleMulterError,
  ticketController.createTicket
);

router.get(
  '/',
  [
    query('status').optional().isIn(['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert']),
    query('priority').optional().isIn(['faible', 'normale', 'elevee', 'urgente']),
    query('category').optional().isIn(['technique', 'facturation', 'compte', 'mission', 'autre']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
  ],
  validate,
  ticketController.getTickets
);

router.get('/stats', checkRole(['jobpadm', 'jobmodo']), ticketController.getTicketStats);
router.get('/stats/custom', checkRole(['jobpadm', 'jobmodo']), ticketController.getTicketStatsCustom);

// Routes pour les tags - déplacées avant les routes avec paramètre :ticketId
router.get('/ticket-tags', tagController.getTags);

router.post(
  '/ticket-tags',
  checkRole(['jobpadm', 'jobmodo']),
  [
    body('name').trim().notEmpty().withMessage('Nom requis'),
    body('color').matches(/^#[0-9A-F]{6}$/i).withMessage('Couleur invalide (format: #RRGGBB)'),
  ],
  validate,
  tagController.createTag
);

router.patch(
  '/ticket-tags/:tagId',
  checkRole(['jobpadm', 'jobmodo']),
  [
    param('tagId').isUUID().withMessage('ID de tag invalide'),
    body('name').optional().trim().notEmpty().withMessage('Nom requis'),
    body('color')
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage('Couleur invalide (format: #RRGGBB)'),
  ],
  validate,
  tagController.updateTag
);

router.delete(
  '/ticket-tags/:tagId',
  checkRole(['jobpadm', 'jobmodo']),
  [param('tagId').isUUID().withMessage('ID de tag invalide')],
  validate,
  tagController.deleteTag
);

// Routes pour les modèles de réponse
router.get('/templates', checkRole(['jobpadm', 'jobmodo']), templateController.getTemplates);

router.get(
  '/templates/:templateId',
  checkRole(['jobpadm', 'jobmodo']),
  [param('templateId').isUUID().withMessage('ID de modèle invalide')],
  validate,
  templateController.getTemplate
);

// Route pour nettoyer le cache des templates
router.post(
  '/templates/clear-cache',
  checkRole(['jobpadm', 'jobmodo']),
  templateController.clearTemplatesCache
);

router.post(
  '/templates',
  checkRole(['jobpadm', 'jobmodo']),
  [
    body('title').trim().notEmpty().withMessage('Titre requis'),
    body('content').trim().notEmpty().withMessage('Contenu requis'),
    body('category')
      .optional()
      .isIn(['technique', 'facturation', 'compte', 'mission', 'autre'])
      .withMessage('Catégorie invalide'),
  ],
  validate,
  templateController.createTemplate
);

router.patch(
  '/templates/:templateId',
  checkRole(['jobpadm', 'jobmodo']),
  [
    param('templateId').isUUID().withMessage('ID de modèle invalide'),
    body('title').optional().trim().notEmpty().withMessage('Titre requis'),
    body('content').optional().trim().notEmpty().withMessage('Contenu requis'),
    body('category')
      .optional()
      .isIn(['technique', 'facturation', 'compte', 'mission', 'autre'])
      .withMessage('Catégorie invalide'),
  ],
  validate,
  templateController.updateTemplate
);

router.delete(
  '/templates/:templateId',
  checkRole(['jobpadm', 'jobmodo']),
  [param('templateId').isUUID().withMessage('ID de modèle invalide')],
  validate,
  templateController.deleteTemplate
);

// Routes avec paramètre :ticketId (déplacées APRÈS les routes spécifiques)
router.get(
  '/:ticketId',
  [param('ticketId').isUUID().withMessage('ID de ticket invalide')],
  validate,
  ticketController.getTicket
);

router.patch(
  '/:ticketId',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    body('title').optional().trim().notEmpty().withMessage('Titre requis'),
    body('description').optional().trim().notEmpty().withMessage('Description requise'),
    body('status')
      .optional()
      .isIn(['nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert'])
      .withMessage('Statut invalide'),
    body('priority')
      .optional()
      .isIn(['faible', 'normale', 'elevee', 'urgente'])
      .withMessage('Priorité invalide'),
    body('category')
      .optional()
      .isIn(['technique', 'facturation', 'compte', 'mission', 'autre'])
      .withMessage('Catégorie invalide'),
    body('assigned_to')
      .optional({ nullable: true })
      .custom((value) => {
        // Si la valeur est null, c'est valide
        if (value === null) return true;
        // Sinon, ce doit être un UUID valide
        return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(value);
      })
      .withMessage('ID d\'utilisateur invalide ou non null'),
    body('tags').optional().isArray().withMessage('Les tags doivent être un tableau'),
  ],
  validate,
  ticketController.updateTicket
);

// Route pour supprimer un ticket (utilisateurs: 30min, admins: toujours)
router.delete(
  '/:ticketId',
  [param('ticketId').isUUID().withMessage('ID de ticket invalide')],
  validate,
  ticketController.deleteTicket
);

// Routes pour les commentaires
router.get(
  '/:ticketId/comments',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
  ],
  validate,
  commentController.getTicketComments
);

router.post(
  '/:ticketId/comments',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    body('message').trim().notEmpty().withMessage('Message requis'),
    body('is_internal').optional().isBoolean(),
  ],
  validate,
  upload.array('files', 5),
  handleMulterError,
  commentController.createComment
);

router.patch(
  '/:ticketId/comments/:commentId',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    param('commentId').isUUID().withMessage('ID de commentaire invalide'),
    body('message').trim().notEmpty().withMessage('Message requis'),
  ],
  validate,
  commentController.updateComment
);

router.delete(
  '/:ticketId/comments/:commentId',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    param('commentId').isUUID().withMessage('ID de commentaire invalide'),
  ],
  validate,
  commentController.deleteComment
);

router.post(
  '/:ticketId/ticket-tags/:tagId',
  checkRole(['jobpadm', 'jobmodo']),
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    param('tagId').isUUID().withMessage('ID de tag invalide'),
  ],
  validate,
  tagController.addTagToTicket
);

router.delete(
  '/:ticketId/ticket-tags/:tagId',
  checkRole(['jobpadm', 'jobmodo']),
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    param('tagId').isUUID().withMessage('ID de tag invalide'),
  ],
  validate,
  tagController.removeTagFromTicket
);

// Routes pour les pièces jointes (après les routes de tickets de base)
router.get(
  '/:ticketId/attachments',
  [param('ticketId').isUUID().withMessage('ID de ticket invalide')],
  validate,
  getTicketAttachments
);

router.post(
  '/:ticketId/attachments',
  [param('ticketId').isUUID().withMessage('ID de ticket invalide')],
  validate,
  addAttachmentToTicket
);

router.delete(
  '/:ticketId/attachments/:attachmentId',
  [
    param('ticketId').isUUID().withMessage('ID de ticket invalide'),
    param('attachmentId').isUUID().withMessage('ID de pièce jointe invalide')
  ],
  validate,
  removeAttachmentFromTicket
);

export default router; 