export type ReportType = 'bug' | 'improvement';
export type ReportCategory = 'interface' | 'fonctionnalite' | 'paiement' | 'securite' | 'autre';
export type ReportPriority = 'faible' | 'moyenne' | 'elevee' | 'critique';
export type ReportStatus = 'nouveau' | 'en_cours' | 'resolu' | 'rejete' | 'ferme' | 'reouvert' | 'attente_moderation';
export type VoteType = 'pour' | 'contre';

export interface BrowserInfo {
  name: string;
  version: string;
  mobile: boolean;
  os: string;
}

export interface BugReport {
  id: string;
  user_id: string;
  title: string;
  description: string;
  report_type: ReportType;
  category: ReportCategory;
  priority: ReportPriority;
  status: ReportStatus;
  is_private: boolean;
  admin_comment?: string;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
  assigned_to?: string;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface BugReportHistory {
  id: string;
  bug_report_id: string;
  updated_by?: string;
  old_status?: ReportStatus;
  new_status: ReportStatus;
  comment?: string;
  created_at: Date;
}

export interface BugReportVote {
  id: string;
  bug_report_id: string;
  user_id: string;
  vote_type: VoteType;
  comment?: string;
  created_at: Date;
}

export interface BugReportComment {
  id: string;
  bug_report_id: string;
  user_id: string;
  parent_comment_id?: string;
  message: string;
  is_admin: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface BugReportCommentDTO {
  message: string;
  parent_comment_id?: string;
}

export interface BugReportVoteCount {
  pour_count: number;
  contre_count: number;
}

export interface BugReportCreateDTO {
  title: string;
  description: string;
  report_type: ReportType;
  category: ReportCategory;
  priority: ReportPriority;
  is_private?: boolean;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
}

export interface BugReportUpdateDTO {
  title?: string;
  description?: string;
  report_type?: ReportType;
  category?: ReportCategory;
  priority?: ReportPriority;
  status?: ReportStatus;
  is_private?: boolean;
  admin_comment?: string;
  reproduction_steps?: string;
  browser_info?: BrowserInfo;
  os_info?: string;
  assigned_to?: string;
  resolved_at?: string;
}

export interface BugReportVoteDTO {
  vote_type: VoteType;
  comment?: string;
} 