import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>r, TileLayer, Circle, Marker, useMap } from 'react-leaflet';
import L, { LatLngTuple, DragEndEvent, Map as LeafletMap } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { API_URL } from '../../../../config/api';

interface InterventionZoneMapProps {
  center: LatLngTuple;
  radius: number;
  avatarUrl?: string;
  onCenterChange?: (center: LatLngTuple) => void;
  onSaveZone?: () => void;
  onSearch?: () => void;
  isOwnProfil?: boolean;
  isSearching?: boolean;
  zoom?: number;
}

const DEFAULT_AVATAR = `${API_URL}/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg`;

const MapUpdater: React.FC<{ center: LatLngTuple }> = ({ center }) => {
  const map = useMap();
  useEffect(() => {
    map.setView(center);
  }, [center, map]);
  return null;
};

const InterventionZoneMap: React.FC<InterventionZoneMapProps> = ({
  center,
  radius,
  avatarUrl,
  onCenterChange,
  isOwnProfil = false,
  isSearching = false,
  zoom
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [iconSize, setIconSize] = useState(70);
  const [isDraggingEnabled, setIsDraggingEnabled] = useState(false); 
  const mapRef = useRef<LeafletMap | null>(null);
  const isDraggingRef = useRef<boolean>(false);
  const circleRef = useRef<L.Circle | null>(null);
  const markerRef = useRef<L.Marker | null>(null);
  const [isLocating, setIsLocating] = useState(false);
  const [isGeolocationCooldown, setIsGeolocationCooldown] = useState(false);
  const lastGeolocationRef = useRef<number>(0);

  useEffect(() => {
    const updateIconSize = () => {
      const width = window.innerWidth;
      if (width <= 640) setIconSize(60);
      else if (width <= 1024) setIconSize(70);
      else setIconSize(80);
    };

    updateIconSize();
    window.addEventListener('resize', updateIconSize);
    return () => window.removeEventListener('resize', updateIconSize);
  }, []);

  useEffect(() => {
    const map = mapRef.current;
    if (map) {
      if (isOwnProfil) {
        map.setMaxZoom(17);
        map.setMinZoom(5);
      } else {
        map.setMaxZoom(zoom ? 18 : 8);
        map.setMinZoom(4);
        
        if (!zoom) {
          map.touchZoom.disable();
          map.boxZoom.disable();
          map.keyboard.disable();
          
          map.getContainer().addEventListener('touchstart', (e: TouchEvent) => {
            if (e.touches.length > 1) {
              e.preventDefault();
            }
          }, { passive: false });
        }
      }
    }
  }, [isOwnProfil, zoom]);

  useEffect(() => {
    const map = mapRef.current;
    if (map) {
      if (isDraggingEnabled) {
        map.dragging.enable();
        map.scrollWheelZoom.enable();
        map.touchZoom.enable();
        map.doubleClickZoom.enable();
      } else {
        map.dragging.disable();
        map.scrollWheelZoom.disable();
        map.touchZoom.disable();
        map.doubleClickZoom.disable();
      }
    }
  }, [isDraggingEnabled]);

  useEffect(() => {
    const map = mapRef.current;
    if (map) {
      if (isMobile) {
        map.setMinZoom(4);
        map.setMaxZoom(isOwnProfil ? 15 : (zoom ? 18 : 8));
        if (!isDraggingEnabled) {
          map.touchZoom.disable();
          map.doubleClickZoom.disable();
        }
      } else {
        map.setMinZoom(4);
        map.setMaxZoom(isOwnProfil ? 17 : (zoom ? 18 : 8));
      }
    }
  }, [isMobile, isOwnProfil, isDraggingEnabled, zoom]);

  const customIcon = new L.Icon({
    iconUrl: avatarUrl || DEFAULT_AVATAR,
    iconSize: [iconSize, iconSize],
    iconAnchor: [iconSize/2, iconSize/2],
    className: 'custom-marker-icon',
  });

  const circleOptions = {
    color: '#FF6B2C',
    fillColor: '#FF6B2C',
    fillOpacity: 0.2,
  };

  const circleProps = {
    center,
    pathOptions: circleOptions,
    radius: radius * 1000,
  };

  // Re-add tileProps definition
  const tileProps = {
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
  };

  // Optimisation des événements de glissement du marqueur
  const handleDragStart = () => {
    isDraggingRef.current = true;
    if (markerRef.current) {
      const markerElement = markerRef.current.getElement();
      if (markerElement) {
        markerElement.classList.add('grabbing');
        markerElement.style.transition = 'none'; // Désactiver les transitions pendant le glissement
      }
    }
  };

  const handleDrag = (e: L.LeafletEvent) => {
    if (isDraggingRef.current && circleRef.current && mapRef.current) {
      const marker = e.target;
      const newPos = marker.getLatLng();
      
      // Mettre à jour le cercle en temps réel pendant le glissement
      circleRef.current.setLatLng(newPos);
      
      requestAnimationFrame(() => {
        if (mapRef.current && marker) {
          const markerElement = marker.getElement();
          if (markerElement) {
            const pixelPoint = mapRef.current.latLngToLayerPoint(newPos);
            
            markerElement.style.transform = `translate3d(${pixelPoint.x}px, ${pixelPoint.y}px, 0)`;
            markerElement.style.zIndex = '1000';
          }
        }
      });
    }
  };

  const handleDragEnd = (e: DragEndEvent) => {
    isDraggingRef.current = false;
    const marker = e.target;
    const position = marker.getLatLng();
    
    // S'assurer que le cercle est correctement positionné à la fin du glissement
    if (circleRef.current) {
      circleRef.current.setLatLng(position);
    }
    
    if (markerRef.current) {
      const markerElement = markerRef.current.getElement();
      if (markerElement) {
        markerElement.classList.remove('grabbing');
        markerElement.style.transition = 'transform 0.1s ease';
      }
    }
    
    onCenterChange?.([position.lat, position.lng]);
  };

  const markerProps = {
    position: center,
    icon: customIcon,
    draggable: isOwnProfil && isDraggingEnabled,
    eventHandlers: isOwnProfil ? {
      dragstart: handleDragStart,
      drag: handleDrag,
      dragend: handleDragEnd,
    } : {},
  };

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 980);
    };
    
    // Vérification initiale
    checkMobile();
    
    // Écouter les changements de taille d'écran
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Utiliser des refs avec les composants React-Leaflet
  const CircleWithRef = ({ circleProps }: { circleProps: L.CircleOptions & { center: L.LatLngExpression, radius: number } }) => {
    const circleInstance = useRef<L.Circle | null>(null);
    
    useEffect(() => {
      if (circleInstance.current) {
        circleRef.current = circleInstance.current;
      }
    }, []);
    
    // Mettre à jour la position du cercle lorsque le centre change
    useEffect(() => {
      if (circleRef.current && center) {
        circleRef.current.setLatLng(center);
      }
    }, [center]);
    
    return <Circle {...circleProps} ref={circleInstance} />;
  };

  // Gestionnaire de clic sur la carte
  const handleMapClick = (e: L.LeafletMouseEvent) => {
    if (!isOwnProfil || !isDraggingEnabled) return;
    
    const newPos = e.latlng;
    
    // Mettre à jour la position du marqueur
    if (markerRef.current) {
      markerRef.current.setLatLng(newPos);
    }
    
    // Mettre à jour la position du cercle
    if (circleRef.current) {
      circleRef.current.setLatLng(newPos);
    }
    
    // Notifier le composant parent du changement de centre
    if (onCenterChange) {
      onCenterChange([newPos.lat, newPos.lng]);
    }
  };

  // Composant pour ajouter l'événement de clic à la carte
  const MapClickHandler = () => {
    const map = useMap();
    
    useEffect(() => {
      if (isOwnProfil && isDraggingEnabled) {
        map.on('click', handleMapClick);
      } else {
        map.off('click', handleMapClick);
      }
      
      return () => {
        map.off('click', handleMapClick);
      };
    }, [map, isOwnProfil, isDraggingEnabled]);
    
    return null;
  };

  // Fonction pour gérer la géolocalisation avec limitation de fréquence
  const handleGeolocation = () => {
    const now = Date.now();
    const cooldownTime = 2000; // 2 secondes de cooldown
    
    // Vérifier si le cooldown est actif
    if (isGeolocationCooldown || now - lastGeolocationRef.current < cooldownTime) {
      return;
    }
    
    // Si la géolocalisation n'est pas supportée par le navigateur
    if (!navigator.geolocation) {
      alert("La géolocalisation n'est pas supportée par votre navigateur.");
      return;
    }

    // Activer le cooldown
    setIsGeolocationCooldown(true);
    lastGeolocationRef.current = now;
    
    // Désactiver le cooldown après 2 secondes
    setTimeout(() => {
      setIsGeolocationCooldown(false);
    }, cooldownTime);

    setIsLocating(true);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        // Succès de la géolocalisation
        const { latitude, longitude } = position.coords;
        const newCenter: LatLngTuple = [latitude, longitude];
        
        // Mettre à jour la position du marqueur et du cercle
        if (markerRef.current) {
          markerRef.current.setLatLng(newCenter);
        }
        
        if (circleRef.current) {
          circleRef.current.setLatLng(newCenter);
        }
        
        // Centrer la carte sur la nouvelle position
        if (mapRef.current) {
          mapRef.current.setView(newCenter, isMobile ? 13 : 14);
        }
        
        // Notifier le composant parent du changement de centre
        if (onCenterChange) {
          onCenterChange(newCenter);
        }
        
        setIsLocating(false);
      },
      (error) => {
        // Erreur de géolocalisation
        let message = "Erreur lors de la géolocalisation.";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            message = "Vous avez refusé l'accès à votre position.";
            break;
          case error.POSITION_UNAVAILABLE:
            message = "Votre position n'est pas disponible.";
            break;
          case error.TIMEOUT:
            message = "La demande de géolocalisation a expiré.";
            break;
        }
        alert(message);
        setIsLocating(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  return (
    <>
      <style>
        {`
          .custom-marker-icon {
            border: 1px solid #FF6B2C !important;
            border-radius: 8px !important;
            background-color: #FFF8F3 !important;
            box-shadow: 0 3px 14px rgba(255, 107, 44, 0.4) !important;
            will-change: transform !important;
          }
          .custom-marker-icon img {
            border-radius: 50% !important;
            object-fit: cover !important;
          }
          .custom-marker-icon.grabbing {
            // cursor: grabbing !important;
            // transform: scale(1.05) !important;
          }
          .leaflet-marker-icon {
            // will-change: transform;
            // transform: translate3d(0, 0, 0);
            // backface-visibility: hidden;
          }
          .leaflet-container {
            -webkit-font-smoothing: antialiased;
          }
          /* Ajustements pour mobile */
          @media (max-width: 980px) {
            .map-controls {
              padding: 6px 10px;
              font-size: 14px;
            }
          }
          .zoom-controls {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
          .zoom-button {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
          }
          .zoom-button:hover {
            background-color: #f8f8f8;
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
          }
          .zoom-button:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
          .zoom-button svg {
            color: #FF6B2C;
            width: 20px;
            height: 20px;
          }
          .zoom-button.locating {
            background-color: #FF6B2C;
          }
          .zoom-button.locating svg {
            color: white;
            animation: pulse-location 1.5s infinite;
          }
          .zoom-button.cooldown {
            background-color: #FF6B2C;
            overflow: hidden;
          }
          .zoom-button.cooldown svg {
            color: white;
          }
          .tooltip {
            position: absolute;
            right: 45px;
            background-color: rgba(0, 0, 0, 0.75);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s ease, visibility 0.2s ease;
            pointer-events: none;
          }
          .tooltip::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -5px;
            transform: translateY(-50%);
            border-width: 5px 0 5px 5px;
            border-style: solid;
            border-color: transparent transparent transparent rgba(0, 0, 0, 0.75);
          }
          .zoom-button:hover .tooltip {
            opacity: 1;
            visibility: visible;
          }
          .cooldown-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.3);
            transform-origin: center;
            border-radius: 4px;
            animation: cooldown-wipe 2s linear forwards;
          }
          @keyframes cooldown-wipe {
            0% {
              clip-path: circle(100% at center);
            }
            100% {
              clip-path: circle(0% at center);
            }
          }
          @keyframes pulse-location {
            0% {
              opacity: 0.6;
            }
            50% {
              opacity: 1;
            }
            100% {
              opacity: 0.6;
            }
          }
          .search-indicator {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            background-color: rgba(255, 107, 44, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            animation: pulse 1.5s infinite;
          }
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(255, 107, 44, 0.7);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(255, 107, 44, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(255, 107, 44, 0);
            }
          }
          .search-indicator svg {
            animation: spin 1.5s linear infinite;
          }
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}
      </style>
      <div className="relative h-[30vh] rounded-lg overflow-hidden z-[1]">
      {/* Contrôles de la carte */}
        <div className="absolute top-4 right-4 z-[1000]">
          <button
            onClick={() => setIsDraggingEnabled(!isDraggingEnabled)}
            className={`px-4 py-2 rounded-lg shadow-lg transition-colors map-controls ${
              isDraggingEnabled ? 'bg-white text-gray-700' : 'bg-orange-500 text-white'
            }`}
          >
            {isDraggingEnabled ? 'Verrouiller la carte' : 'Déverrouiller la carte'}
          </button>
        </div>

        {/* Boutons de zoom */}
        <div className="zoom-controls">
          <button 
            className="zoom-button" 
            onClick={() => {
              if (mapRef.current) {
                mapRef.current.zoomIn();
              }
            }}
            aria-label="Zoom avant"
          >
            <span className="tooltip">Zoom avant</span>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
          <button 
            className="zoom-button" 
            onClick={() => {
              if (mapRef.current) {
                mapRef.current.zoomOut();
              }
            }}
            aria-label="Zoom arrière"
          >
            <span className="tooltip">Zoom arrière</span>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
          {isOwnProfil && (
            <button 
              className={`zoom-button ${isLocating ? 'locating' : ''} ${isGeolocationCooldown ? 'cooldown' : ''}`}
              onClick={handleGeolocation}
              disabled={isLocating || isGeolocationCooldown}
              aria-label="Me localiser"
              title="Me localiser"
            >
              <span className="tooltip">Me localiser</span>
              {isGeolocationCooldown && <div className="cooldown-overlay"></div>}
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="3"></circle>
                <line x1="12" y1="2" x2="12" y2="4"></line>
                <line x1="12" y1="20" x2="12" y2="22"></line>
                <line x1="2" y1="12" x2="4" y2="12"></line>
                <line x1="20" y1="12" x2="22" y2="12"></line>
              </svg>
            </button>
          )}
        </div>

        {/* Indicateur de recherche */}
        {isSearching && (
          <div className="search-indicator">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
            <span>Recherche d'adresse en cours...</span>
          </div>
        )}

        {/* Indicateur de géolocalisation */}
        {isLocating && !isSearching && (
          <div className="search-indicator">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="3"></circle>
              <line x1="12" y1="2" x2="12" y2="4"></line>
              <line x1="12" y1="20" x2="12" y2="22"></line>
              <line x1="2" y1="12" x2="4" y2="12"></line>
              <line x1="20" y1="12" x2="22" y2="12"></line>
            </svg>
            <span>Localisation en cours...</span>
          </div>
        )}

        {/* Indicateur de geste uniquement sur écrans mobiles */}
        {!isDraggingEnabled && (
          <div className="absolute left-0 right-0 mx-auto top-1/2 -translate-y-1/2 z-[1000] bg-black/50 text-white px-4 py-2 rounded-lg flex items-center gap-2 pointer-events-none shadow-lg w-fit max-w-[90%] sm:max-w-none justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginBottom: '5px' }}>
              <path d="M6 13c2 2 4 2 6 0 2-2 4-2 6 0"/>
              <path d="M6 17c2 2 4 2 6 0 2-2 4-2 6 0"/>
            </svg>
            <span className="text-sm whitespace-wrap font-medium">Déverrouillez la carte pour la déplacer</span>
          </div>
        )}

        {/* z-[1] pour que la carte soit en dessous !!! */}
        <div className="h-[30vh] relative z-[1]">
        <MapContainer
          ref={mapRef}
          center={center}
          zoom={zoom || (isMobile ? 4 : 5)}
          maxZoom={isOwnProfil ? (isMobile ? 15 : 17) : (zoom ? 18 : 8)}
          minZoom={isMobile ? 4 : 5}
          zoomControl={false}
          className="h-full w-full"
          dragging={isDraggingEnabled}
          touchZoom={isDraggingEnabled && (isOwnProfil || zoom !== undefined)}
          doubleClickZoom={isDraggingEnabled && (isOwnProfil || zoom !== undefined)}
          scrollWheelZoom={isDraggingEnabled && (isOwnProfil || zoom !== undefined)}
          zoomDelta={1}
          zoomSnap={1}
          preferCanvas={true}
        >
          <TileLayer {...tileProps} />
          <MapUpdater center={center} />
          <CircleWithRef circleProps={circleProps} />
          <Marker {...markerProps} ref={markerRef} />
          <MapClickHandler />
        </MapContainer>
      </div>
      </div>
    </>
  );
};

export default InterventionZoneMap;