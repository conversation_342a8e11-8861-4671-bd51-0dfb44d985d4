import api from './api';
import { API_CONFIG } from '../config/api';
import { logger } from '../utils/logger';

export interface AdvancedStats {
  clientStats: {
    total: number;
    nouveaux: number;
    tauxSatisfaction: number;
    croissanceMensuelle: number;
    evolution: Array<{
      mois: string;
      clients: number;
      missions: number;
      montant: number;
    }>;
    tauxRetention?: number; // Taux de clients qui reviennent pour une autre mission
    tauxConversion?: number; // Taux de conversion de visiteurs en clients
    regionsPrincipales?: Array<{
      nom: string;
      pourcentage: number;
    }>;
  };
  servicesPopulaires: Array<{
    nom: string;
    reservations: number;
    satisfaction: number;
    type: 'candidature';
    dureeAverage?: number; // Durée moyenne des missions en heures
    tarifAverage?: number; // Tarif moyen de la mission
  }>;
  mesMissionsPopulaires: Array<{
    nom: string;
    reservations: number;
    satisfaction: number;
    type: 'mission';
    dureeAverage?: number; // Durée moyenne des missions en heures
    tarifAverage?: number; // Tarif moyen de la mission
  }>;
  heuresPopulaires: Array<{
    heure: string;
    reservations: number;
  }>;
  joursSemaine?: Array<{
    jour: string;
    reservations: number;
    pourcentage: number;
  }>;
  delaiReponse?: {
    moyen: number; // En minutes
    evolution: Array<{
      mois: string;
      delai: number;
    }>;
  };
  tauxCompletion?: {
    pourcentage: number;
    evolution: Array<{
      mois: string;
      taux: number;
    }>;
  };
  performanceServices?: Array<{
    nomService: string;
    revenuTotal: number;
    missionsMoyennes: number;
    croissance: number;
    satisfaction: number;
  }>;
  previsions?: {
    prochainMois: number;
    troisMois: number;
    sixMois: number;
    tendance: 'hausse' | 'stable' | 'baisse';
  };
  delaiMoyen?: {
    valeur: number; // En minutes
    formatte: string; // Format lisible (ex: "2h 30min")
  };
  // Statistiques de facturation
  invoiceStats?: {
    total: number;
    montantTotal: number;
    parStatut: {
      [key: string]: number;
    };
    parType: {
      [key: string]: number;
    };
    evolution: Array<{
      mois: string;
      montant: number;
      nombre: number;
    }>;
  };
}

export const advancedStatsService = {
  /**
   * Récupérer les statistiques avancées
   * @param timeRange Période en jours (7, 30, 90)
   */
  getStats: async (timeRange: string = '30'): Promise<AdvancedStats> => {
    try {
      const response = await api.get(`${API_CONFIG.baseURL}/api/advanced-stats`, {
        params: { timeRange }
      });
      return response.data;
    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques avancées:', error);
      throw error;
    }
  }
}; 