import React from 'react';
import ModalPortal from '../ModalPortal';
import { AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface ConfirmNameModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  previousFirstName: string;
  previousLastName: string;
  tempFirstName: string;
  tempLastName: string;
  isConfirmingFirstName: boolean;
  isConfirmingLastName: boolean;
  isConfirmationChecked: boolean;
  setIsConfirmationChecked: (checked: boolean) => void;
  firstName: string;
  lastName: string;
  isWithin48Hours: boolean;
}

const ConfirmNameModal: React.FC<ConfirmNameModalProps> = ({
  open,
  onClose,
  onConfirm,
  previousFirstName,
  previousLastName,
  tempFirstName,
  tempLastName,
  isConfirmingFirstName,
  isConfirmingLastName,
  isConfirmationChecked,
  setIsConfirmationChecked,
  firstName,
  lastName,
  isWithin48Hours,
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
          <h3 className="text-lg font-semibold mb-4">Confirmer la modification</h3>
          <div className="space-y-4 overflow-y-auto flex-grow pr-2">
            {isConfirmingFirstName && (
              <div>
                <p className="text-sm text-gray-500 mb-2">Ancien prénom :</p>
                <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through">
                  {previousFirstName || 'Aucun prénom'}
                </div>
                <p className="text-sm text-gray-500 mt-4 mb-2">Nouveau prénom :</p>
                <div className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg">
                  {tempFirstName}
                </div>
              </div>
            )}
            {isConfirmingLastName && (
              <div>
                <p className="text-sm text-gray-500 mb-2">Ancien nom :</p>
                <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through">
                  {previousLastName || 'Aucun nom'}
                </div>
                <p className="text-sm text-gray-500 mt-4 mb-2">Nouveau nom :</p>
                <div className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg">
                  {tempLastName}
                </div>
              </div>
            )}
            {(firstName === 'Prénom' || lastName === 'Nom' || isWithin48Hours) && (
              <div className="mt-2 p-3 bg-red-50 border border-red-100 rounded-lg flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                <div>
                  <p className="text-red-600 font-medium">Attention, action importante</p>
                  <p className="text-sm text-red-500 mt-1">
                    La modification de votre nom et prénom n'est autorisée que dans les 48 heures suivant la création de votre profil.
                    Vérifiez bien l'orthographe avant de confirmer.
                  </p>
                </div>
              </div>
            )}
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="mt-4 flex items-start bg-red-100 p-3 rounded-lg cursor-pointer"
              onClick={() => setIsConfirmationChecked(!isConfirmationChecked)}
            >
              <input
                type="checkbox"
                checked={isConfirmationChecked}
                onChange={e => setIsConfirmationChecked(e.target.checked)}
                className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md cursor-pointer"
              />
              <div>
                <p className="text-sm text-gray-700 font-medium">
                  Confirmer la modification
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  En cochant cette case, vous confirmez la modification de votre nom et prénom.
                </p>
              </div>
            </motion.div>
          </div>
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={onConfirm}
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
            >
              Confirmer
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmNameModal; 