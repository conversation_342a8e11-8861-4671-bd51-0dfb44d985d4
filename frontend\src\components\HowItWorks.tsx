import { Link } from 'react-router-dom';
import { <PERSON>, Wrench, Brush, Hammer, Dog, GraduationCap, Monitor, Truck, Brain, Sparkles } from 'lucide-react';

const steps = [
  {
    number: '1',
    title: 'CRÉEZ VOTRE PROFIL',
    description: 'Inscription en 2 minutes : présentez vos compétences, fixez vos tarifs et disponibilités pour attirer les clients idéaux.',
    aiHelp: 'Notre IA génère une bio professionnelle et des services optimisés pour vous'
  },
  {
    number: '2',
    title: 'RECEVEZ DES DEMANDES',
    description: 'Les clients vous trouvent facilement et vous contactent directement selon vos services et votre zone géographique.',
    aiHelp: 'L\'IA suggère les missions qui correspondent le mieux à votre profil'
  },
  {
    number: '3',
    title: 'GAGNEZ DE L\'ARGENT',
    description: 'Négociez librement, réalisez vos missions à votre rythme et recevez 100% de vos paiements sans aucune commission.',
    aiHelp: 'Optimisez vos revenus grâce aux recommandations tarifaires intelligentes'
  }
];

const services = [
  {
    icon: <Leaf className="w-8 h-8" />,
    title: 'Jardinage',
    description: 'Entretien de pelouse, taille des haies, et soins des plantes pour un extérieur impeccable.'
  },
  {
    icon: <Wrench className="w-8 h-8" />,
    title: 'Petites Réparations',
    description: 'Réparez vos fuites, remplacez vos poignées de porte, ou effectuez de petites rénovations à domicile.'
  },
  {
    icon: <Brush className="w-8 h-8" />,
    title: 'Ménage et Nettoyage',
    description: 'Services de ménage régulier, nettoyage de printemps, ou lavage des vitres pour une maison propre et accueillante.'
  },
  {
    icon: <Hammer className="w-8 h-8" />,
    title: 'Bricolage',
    description: 'Montage de meubles, installation d\'étagères, et petits travaux manuels pour votre confort.'
  },
  {
    icon: <Dog className="w-8 h-8" />,
    title: "Garde d'Animaux",
    description: 'Promenade, garde à domicile, ou soins des animaux pendant vos absences.'
  },
  {
    icon: <GraduationCap className="w-8 h-8" />,
    title: 'Cours et Soutien Scolaire',
    description: 'Cours particuliers, aide aux devoirs, et préparation aux examens pour tous les niveaux.'
  },
  {
    icon: <Monitor className="w-8 h-8" />,
    title: 'Informatique',
    description: 'Dépannage informatique, installation de logiciels, ou configuration de votre matériel électronique.'
  },
  {
    icon: <Truck className="w-8 h-8" />,
    title: 'Déménagement et Transport',
    description: 'Aide au déménagement, transport de meubles, et services de livraison.'
  },
  {
    icon: <Brain className="w-8 h-8" />,
    title: 'Coaching et Conseils',
    description: 'Coaching personnel, conseils en nutrition, ou aide à la gestion de projet.'
  }
];

const HowItWorks = () => {
  return (
    <section id="how-it-works" className="py-16 relative">
      {/* Background avec overlay */}
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: 'url(/images/fond-section-comment-ca-marche-image.webp), url(/images/fond-section-comment-ca-marche-image.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: '0.6'
        }}
      ></div>

      {/* <div className="container mx-auto px-4 relative z-10"> */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
        {/* Title */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center px-4 py-1.5 mb-4 bg-[#FF6B2C]/10 rounded-full">
            <Sparkles className="h-4 w-4 text-[#FF6B2C] mr-2" />
            <span className="text-sm font-medium text-[#FF6B2C]">Propulsé par l'intelligence artificielle</span>
          </div>
          <h2 className="text-4xl font-bold mb-4">
            Comment fonctionne <span className="text-[#FF6B2C]">JobPartiel.fr</span> ?
          </h2>
        </div>

        {/* Steps */}
        <div className="grid md:grid-cols-3 gap-8 mb-16 relative">
          {/* Connecting Lines - Only visible on md screens and up */}
          <div className="hidden md:block absolute top-[4rem] left-[20%] right-[20%] h-[2px] bg-gradient-to-r from-[#FF6B2C] to-[#FF8F59]">
            {/* Decorative dots */}
            <div className="absolute left-[33%] top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-[#FF6B2C]"></div>
            <div className="absolute right-[33%] top-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-[#FF8F59]"></div>
          </div>

          {steps.map((step) => (
            <div key={step.number} className="text-center group p-6 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 relative z-10">
              <div className="relative">
                <div className="bg-gradient-to-br from-[#FF6B2C] to-[#FF8F59] text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold group-hover:scale-110 transition-transform duration-300">
                  {step.number}
                </div>
                {/* Pulsing effect behind the number */}
                <div className="absolute inset-0 mx-auto w-16 h-16 rounded-full bg-[#FF6B2C] opacity-20 animate-ping"></div>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-[#FF6B2C] transition-colors duration-300">
                {step.title}
              </h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                {step.description}
              </p>
              
              {/* Badge IA */}
              <div className="bg-blue-50 border border-blue-100 rounded-lg p-2 flex items-start">
                <div className="bg-blue-100 rounded-full p-1 mr-2 flex-shrink-0">
                  <Brain size={16} className="text-blue-600" />
                </div>
                <p className="text-blue-700 text-sm text-left">
                  {step.aiHelp}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Eligibility Message */}
        <div className="my-8 px-2 relative">
          <div className="absolute inset-0 bg-white transform rounded-xl transition-all duration-300 shadow-md"></div>
          <div className="relative w-full mx-auto">
            <div className="bg-transparent p-3 sm:p-4 md:p-5 lg:p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-5 border-b pb-3 gap-3 md:gap-0">
                <h2 className="text-xl md:text-2xl font-bold text-gray-800">
                  <span className="text-[#FF6B2C]">JobPartiel</span> connecte talents et besoins
                </h2>
                <div className="flex gap-2 w-full md:w-auto">
                  <Link to="/inscription" className="flex-1 md:flex-none text-center bg-[#FF6B2C] text-white px-3 py-1 rounded text-sm font-medium hover:bg-[#E55A1B] transition-colors">
                    Proposer
                  </Link>
                  <Link to="/inscription" className="flex-1 md:flex-none text-center bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm font-medium hover:bg-gray-200 transition-colors">
                    Rechercher
                  </Link>
                </div>
              </div>

              <h3 className="text-xl font-bold text-[#FF6B2C] mb-4 flex items-center">
                <span className="mr-2">🔥</span>
                Services populaires
                <div className="h-[2px] flex-grow bg-gradient-to-r from-[#FF6B2C] to-transparent ml-4"></div>
              </h3>


              {/* Services Grid - Amélioré pour la conversion */}
              <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-5 px-1 sm:px-2 md:px-0">
                {services.map((service) => (
                  <div
                    key={service.title}
                    className="bg-white border border-gray-100 rounded-xl shadow-sm hover:shadow-md p-2 sm:p-3 md:p-4 relative overflow-hidden group transition-all duration-300 transform hover:-translate-y-1 max-w-full"
                  >
                    {/* Badge "Populaire" pour certains services */}
                    {['Jardinage', 'Bricolage', 'Ménage et Nettoyage'].includes(service.title) && (
                      <div className="absolute top-0 right-0 bg-[#FF6B2C] text-white text-xs font-semibold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-bl-xl rounded-tr-xl z-10 max-w-[90px] sm:max-w-none overflow-hidden text-ellipsis whitespace-nowrap">
                        Populaire
                      </div>
                    )}

                    {/* Effet de survol */}
                    <div className="absolute inset-0 bg-gradient-to-tr from-[#FF6B2C]/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                    <div className="relative flex flex-row items-start mt-2 sm:mt-3 md:mt-4">
                      {/* Icône avec effet de survol amélioré */}
                      <div className="mr-2 sm:mr-3 md:mr-4 flex-shrink-0">
                        <div className="bg-[#FF6B2C]/10 rounded-full p-2 sm:p-2.5 md:p-3 transform group-hover:scale-110 transition-all duration-300 group-hover:bg-[#FF6B2C]/20">
                          <div className="text-[#FF6B2C]" style={{ minWidth: '24px', minHeight: '24px' }}>
                            {service.icon}
                          </div>
                        </div>
                      </div>

                      {/* Contenu textuel */}
                      <div className="flex-1 min-w-0"> {/* min-w-0 ensures text truncation works properly */}
                        <h3 className="text-lg sm:text-xl font-bold mb-1 sm:mb-2 text-gray-800 group-hover:text-[#FF6B2C] transition-colors duration-300 truncate">
                          {service.title}
                        </h3>
                        <p className="text-gray-600 text-xs sm:text-sm mb-2 sm:mb-3 leading-relaxed">
                          {service.description}
                        </p>

                        {/* Appel à l'action */}
                        <Link to="/inscription" className="inline-flex items-center text-[#FF6B2C] font-medium text-xs sm:text-sm mt-1 sm:mt-2 opacity-80 group-hover:opacity-100">
                          <span className="truncate">Proposer ce service</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 flex-shrink-0 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="text-center mt-12">
          <Link
            to="/inscription"
            className="inline-block bg-[#FF6B2C] text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#e55a20] transition-colors"
          >
            INSCRIVEZ-VOUS EN QUELQUES CLICS
          </Link>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;