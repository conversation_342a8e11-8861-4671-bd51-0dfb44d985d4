import { Router } from 'express';
import { getUserPrompts, saveUserPrompt, generateContent, deleteUserPrompt, generateSupportAssistance } from '../controllers/aiGenerationController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';
import { checkAiConsent } from '../middleware/aiConsentMiddleware';

const router = Router();

// Rate limiter pour les requêtes de génération IA
const aiGenerationLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes de génération IA. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Route pour récupérer les prompts personnalisés
router.get('/prompts', aiGenerationLimiter, asyncHandler(getUserPrompts));

// Route pour enregistrer un prompt personnalisé
router.post('/prompts', aiGenerationLimiter, asyncHandler(saveUserPrompt));

// Route pour générer du contenu
router.post('/generate', aiGenerationLimiter, checkAiConsent, asyncHandler(generateContent));

// Route pour supprimer un prompt personnalisé
router.delete('/prompts/:type', aiGenerationLimiter, asyncHandler(deleteUserPrompt));

// Route pour l'assistance IA des tickets de support
router.post('/support-assistance', aiGenerationLimiter, checkAiConsent, asyncHandler(generateSupportAssistance));

export default router;
