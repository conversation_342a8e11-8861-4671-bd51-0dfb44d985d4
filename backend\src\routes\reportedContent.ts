import { Router } from 'express';
import * as reportedContentController from '../controllers/reportedContentController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import * as reportedContentService from '../services/reportedContentService';
import { decryptProfilDataAsync } from '../utils/encryption';
import { restoreMaskedCommentController } from '../controllers/reportedContentController';

const router = Router();

// Rate limiter spécifique pour les statistiques avancées
const reportedContentLimiter = rateLimit({
    windowMs: 30 * 1000, // 30 secondes
    max: 50, // 50 requêtes maximum
    message: {
      message: 'Trop de requêtes pour les statistiques. Veuillez réessayer dans 30 secondes.',
      success: false,
      toastType: 'error'
    },
    standardHeaders: true,
    legacyHeaders: false
  });
  
// Signaler un contenu
router.post('/', authMiddleware.authenticateToken, reportedContentLimiter, reportedContentController.reportContent);

// Liste des signalements (admin/modo)
router.get('/', authMiddleware.authenticateToken, reportedContentLimiter, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.getAllReports);

// Statistiques sur les signalements (admin/modo)
router.get('/stats', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), async (req, res) => {
  try {
    const { supabase } = require('../config/supabase');
    // Compter le total
    const { count: total, error: errorTotal } = await supabase
      .from('reported_content')
      .select('*', { count: 'exact', head: true });
    if (errorTotal) {
      console.error('Erreur stats total', errorTotal);
      res.status(500).json({ message: 'Erreur lors de la récupération du total', error: errorTotal.message || errorTotal });
      return;
    }
    // Compter par statut
    const statuts = ['pending', 'in_review', 'validated', 'rejected', 'content_deleted', 'masqué', 'restauré'];
    const stats: any = { total: total || 0 };
    for (const status of statuts) {
      const { count, error } = await supabase
        .from('reported_content')
        .select('*', { count: 'exact', head: true })
        .eq('status', status);
      if (error) {
        console.error('Erreur stats statut', status, error);
        res.status(500).json({ message: `Erreur lors de la récupération des stats pour le statut ${status}`, error: error.message || error });
        return;
      }
      stats[status] = count || 0;
    }
    res.json(stats);
  } catch (e) {
    console.error('Erreur globale /stats', e);
    const err = e as Error;
    res.status(500).json({ message: 'Erreur lors de la récupération des stats', error: err.message || err });
  }
});

// Détail d'un signalement
router.get('/:id', authMiddleware.authenticateToken, reportedContentLimiter, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.getReportById);

// Modifier le statut, ajouter un commentaire admin, modérer le contenu
router.patch('/:id', authMiddleware.authenticateToken, reportedContentLimiter, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.updateReport);

// Mise à jour en masse des signalements
router.post('/bulk-update', authMiddleware.authenticateToken, reportedContentLimiter, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.bulkUpdateReports);

// Action de modération sur un signalement (masquer, supprimer, etc.)
router.post('/:id/moderate', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.updateReport);

// Restaurer un commentaire masqué (admin/modo)
router.patch('/:id/restore', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), restoreMaskedCommentController);

// Historique des actions sur un signalement
router.get('/:id/history', async (req, res) => {
  try {
    const history = await reportedContentService.getReportHistory(req.params.id);
    res.json(history);
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de la récupération de l'historique." });
  }
});

// Aperçu du contenu signalé
router.get('/:id/content', async (req, res) => {
  try {
    const content = await reportedContentService.getReportedContentPreview(req.params.id);
    if (!content) {
      res.status(404).json({ error: 'Contenu introuvable.' });
      return;
    }

    // Décrypter les données de profil si c'est un profil signalé
    // Vérifier si l'objet ressemble à un profil avant de tenter le décryptage
    if (content && typeof content === 'object' && 'nom' in content && 'prenom' in content) {
      const decryptedContent = await decryptProfilDataAsync(content);
      res.json(decryptedContent);
    } else {
      res.json(content);
    }
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de la récupération du contenu." });
  }
});

// Nouvelle route : conversation complète pour un message signalé
router.get('/:id/conversation', async (req, res) => {
  try {
    const conversation = await reportedContentService.getReportedConversation(req.params.id);
    if (!conversation) {
      res.status(404).json({ error: 'Conversation introuvable.' });
      return;
    }
    res.json(conversation);
  } catch (error) {
    res.status(500).json({ error: "Erreur lors de la récupération de la conversation." });
  }
});

// Liste des signalements individuels pour un contenu signalé (admin/modo)
router.get('/:id/reports', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.getReportsForContent);

// Ajouter une note interne (historique JSON)
router.patch('/:id/internal-note', authMiddleware.authenticateToken, authMiddleware.checkRole(['jobpadm', 'jobmodo']), reportedContentController.addInternalNote);

export default router;