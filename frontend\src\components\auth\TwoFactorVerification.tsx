import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { TextField, Button, Typography, Box, Paper, CircularProgress, Alert } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import { motion } from 'framer-motion';
import api from '../../services/api';
import { notify } from '@/components/Notification';
import logger from '@/utils/logger';

interface TwoFactorVerificationProps {
  email: string;
  maskedEmail: string;
  onCancel: () => void;
}

const TwoFactorVerification: React.FC<TwoFactorVerificationProps> = ({ email, maskedEmail, onCancel }) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(600); // 10 minutes en secondes
  const navigate = useNavigate();

  useEffect(() => {
    // Démarrer le compte à rebours
    const timer = setInterval(() => {
      setCountdown((prevCountdown) => {
        if (prevCountdown <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prevCountdown - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode) {
      setError('Veuillez entrer le code de vérification');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      logger.info('Vérification du code 2FA', { code: verificationCode });
      const response = await api.post('/api/auth/verify-two-factor', { token: verificationCode });

      if (response.data.success) {
        notify('Authentification réussie', 'success');

        // Rediriger vers le dashboard
        navigate('/dashboard');
      } else {
        setError(response.data.message || 'Erreur de vérification');
      }
    } catch (error: any) {
      logger.error('Erreur lors de la vérification du code 2FA', error);
      setError(
        error.response?.data?.message ||
        'Erreur lors de la vérification. Veuillez réessayer.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          maxWidth: 400,
          mx: 'auto',
          mt: 4,
          borderRadius: 2,
          backgroundColor: 'white'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            mb: 3
          }}
        >
          <Box
            sx={{
              backgroundColor: 'rgba(255, 122, 53, 0.1)',
              borderRadius: '50%',
              p: 1.5,
              mb: 2
            }}
          >
            <LockIcon sx={{ fontSize: 40, color: '#FF7A35' }} />
          </Box>
          <Typography variant="h5" component="h1" gutterBottom sx={{ color: '#FF7A35', fontWeight: 600 }}>
            Vérification à deux facteurs
          </Typography>
          <Typography variant="body1" align="center" sx={{ mb: 2 }}>
            Un code de vérification a été envoyé à {maskedEmail}
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center">
            Temps restant: {formatTime(countdown)}
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            label="Code de vérification"
            variant="outlined"
            fullWidth
            value={verificationCode}
            onChange={(e) => {
              // Limiter à 6 caractères
              if (e.target.value.length <= 6) {
                setVerificationCode(e.target.value);
              }
            }}
            margin="normal"
            placeholder="Entrez le code à 6 chiffres"
            autoFocus
            sx={{ mb: 2 }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            disabled={loading}
            sx={{
              mt: 2,
              mb: 2,
              py: 1.5,
              backgroundColor: '#FF7A35',
              '&:hover': {
                backgroundColor: '#FF6B2C',
              },
            }}
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Vérifier'}
          </Button>

          <Button
            fullWidth
            variant="outlined"
            onClick={onCancel}
            sx={{
              py: 1.5,
              borderColor: '#FF7A35',
              color: '#FF7A35',
              '&:hover': {
                borderColor: '#FF6B2C',
                backgroundColor: 'rgba(255, 122, 53, 0.04)',
              },
            }}
          >
            Annuler
          </Button>
        </form>
      </Paper>
    </motion.div>
  );
};

export default TwoFactorVerification;
