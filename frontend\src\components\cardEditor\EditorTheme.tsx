import { createTheme, ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import React from 'react';

// Thème personnalisé pour l'éditeur de cartes
const editorTheme = createTheme({
  palette: {
    primary: {
      main: '#FF6B2C',
      light: '#FF965E',
      dark: '#E55A1B',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#FF7A35',
      light: '#FFA76B',
      dark: '#E56A25',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#FFF8F3',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
    error: {
      main: '#F44336',
    },
    warning: {
      main: '#FF9800',
    },
    info: {
      main: '#2196F3',
    },
    success: {
      main: '#4CAF50',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
      fontSize: '1.75rem',
      color: '#FF6B2C',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    subtitle1: {
      fontWeight: 500,
      fontSize: '1.1rem',
    },
    subtitle2: {
      fontWeight: 500,
      fontSize: '0.9rem',
      color: '#666666',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          padding: '8px 16px',
          fontWeight: 500,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          },
        },
        outlined: {
          borderWidth: 2,
          '&:hover': {
            borderWidth: 2,
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiSlider: {
      styleOverrides: {
        root: {
          height: 8,
        },
        thumb: {
          height: 20,
          width: 20,
          '&:focus, &:hover, &.Mui-active': {
            boxShadow: '0 0 0 8px rgba(255, 107, 44, 0.16)',
          },
        },
        track: {
          height: 8,
          borderRadius: 4,
        },
        rail: {
          height: 8,
          borderRadius: 4,
          opacity: 0.3,
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 42,
          height: 26,
          padding: 0,
        },
        switchBase: {
          padding: 1,
          '&.Mui-checked': {
            transform: 'translateX(16px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
              opacity: 1,
              backgroundColor: '#FF6B2C',
            },
          },
        },
        thumb: {
          width: 24,
          height: 24,
        },
        track: {
          borderRadius: 13,
          opacity: 1,
          backgroundColor: '#E0E0E0',
        },
      },
    },
  },
});

interface EditorThemeProviderProps {
  children: React.ReactNode;
}

// Composant fournisseur de thème pour l'éditeur
const EditorThemeProvider: React.FC<EditorThemeProviderProps> = ({ children }) => {
  return (
    <ThemeProvider theme={editorTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

export default EditorThemeProvider;
