import React from 'react';
import ModalPortal from '../ModalPortal';
import DOMPurify from 'dompurify';

interface ConfirmBioModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  previousBio: string;
  tempBio: string;
}

const ConfirmBioModal: React.FC<ConfirmBioModalProps> = ({
  open,
  onClose,
  onConfirm,
  previousBio,
  tempBio,
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
          <h3 className="text-lg font-semibold mb-4">Confirmer la modification</h3>
          <div className="space-y-4 overflow-y-auto flex-grow pr-2">
            <div>
              <p className="text-sm text-gray-500 mb-2">Ancienne bio :</p>
              <div
                className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through"
                dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(previousBio) || 'Aucune bio' }}
              />
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">Nouvelle bio :</p>
              <div
                className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg"
                dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(tempBio) }}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={onConfirm}
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
            >
              Confirmer
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmBioModal; 