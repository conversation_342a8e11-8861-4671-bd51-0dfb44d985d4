import { Request, Response, NextFunction } from 'express';
import { randomBytes } from 'crypto';
// import logger from '../utils/logger';
import session from 'express-session';
import { RequestHandler } from 'express';

declare module 'express-session' {
  interface SessionData {
    csrfToken?: string;
  }
}

interface CsrfRequest extends Request {
  session: session.Session & Partial<session.SessionData> & {
    csrfToken?: string;
  };
}

const generateToken = (): string => {
  return randomBytes(32).toString('hex');
};

export const csrfProtection: RequestHandler = (req: CsrfRequest, res: Response, next: NextFunction): void => {
  // Ignorer les méthodes sûres
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    next();
    return;
  }

  // SÉCURITÉ: Ignorer CSRF pour les webhooks (qui ont leur propre validation)
  if (req.path.includes('/webhook')) {
    next();
    return;
  }

  // Vérifier le token dans les différents endroits possibles
  const token = req.headers['x-csrf-token'] ||
                req.headers['X-CSRF-Token'] ||
                req.body._csrf ||
                req.query._csrf;

  const sessionToken = req.session?.csrfToken;

  // Logs de débogage désactivés maintenant que le problème est résolu
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('🔍 CSRF Debug Info:', {
  //     path: req.path,
  //     method: req.method,
  //     hasToken: !!token,
  //     hasSessionToken: !!sessionToken,
  //     sessionExists: !!req.session,
  //     sessionId: req.session?.id,
  //     tokenMatch: token === sessionToken,
  //     receivedToken: token ? token.substring(0, 10) + '...' : 'none',
  //     sessionToken: sessionToken ? sessionToken.substring(0, 10) + '...' : 'none'
  //   });
  // }

  // SÉCURITÉ RENFORCÉE: Toujours exiger un token CSRF valide
  if (!sessionToken) {
    console.error('❌ CSRF: Session token manquant', {
      path: req.path,
      sessionExists: !!req.session,
      sessionId: req.session?.id
    });
    res.status(403).json({
      success: false,
      message: 'Token CSRF invalide ou manquant',
      error: 'Session CSRF manquante - veuillez rafraîchir la page',
      code: 'CSRF_SESSION_MISSING'
    });
    return;
  }

  // Vérifier si le token correspond
  if (!token || token !== sessionToken) {
    console.error('❌ CSRF: Token mismatch', {
      path: req.path,
      hasToken: !!token,
      hasSessionToken: !!sessionToken,
      sessionExists: !!req.session,
      sessionId: req.session?.id
    });
    res.status(403).json({
      success: false,
      message: 'Token CSRF invalide ou manquant',
      error: 'Token CSRF invalide ou manquant',
      code: 'CSRF_TOKEN_INVALID',
      details: process.env.NODE_ENV === 'development' ? {
        hasToken: !!token,
        hasSessionToken: !!sessionToken,
        sessionExists: !!req.session,
        sessionId: req.session?.id
      } : undefined
    });
    return;
  }

  // console.log('✅ CSRF: Token valide pour', req.path);
  next();
};

export const setCSRFToken: RequestHandler = (req: CsrfRequest, res: Response, next: NextFunction): void => {
  // Générer un nouveau token si nécessaire
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateToken();
    if (process.env.NODE_ENV === 'development') {
      console.log('🔑 CSRF: Nouveau token généré pour la session', req.session.id);
    }
  }

  // Toujours envoyer le token dans l'en-tête pour toutes les requêtes
  res.setHeader('X-CSRF-Token', req.session.csrfToken);

  // if (process.env.NODE_ENV === 'development') {
  //   console.log('📤 CSRF: Token envoyé dans l\'en-tête pour', req.path, {
  //     sessionId: req.session.id,
  //     tokenPreview: req.session.csrfToken.substring(0, 10) + '...'
  //   });
  // }

  // Sauvegarder la session immédiatement
  req.session.save((err) => {
    if (err) {
      console.error('❌ Erreur lors de la sauvegarde de la session:', err);
    } else if (process.env.NODE_ENV === 'development') {
      // console.log('💾 CSRF: Session sauvegardée avec succès');
    }
    next();
  });
};

export default {
  csrfProtection,
  setCSRFToken
};
