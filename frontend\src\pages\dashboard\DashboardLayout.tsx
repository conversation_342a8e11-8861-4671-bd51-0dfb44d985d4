import React, { useEffect, useState, createContext } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import logger from '@/utils/logger';
import { Heart, LifeBuoy, Bug } from 'lucide-react';
import {
  HomeIcon,
  BuildIcon,
  CalendarMonthIcon,
  BarChartIcon,
  GroupIcon,
  SettingsIcon,
  MessageIcon,
  UserIcon,
  DocumentIcon,
  StarIcon,
  MarketingIcon,
  CertificateIcon,
  NetworkIcon,
  GrowthIcon,
  QualityIcon,
  CoinIcon,
  AddIcon,
  BellIcon,
  CardIcon
} from './icons.tsx';
import {
  Euro
} from 'lucide-react';
import { Container } from '@mui/material';

import { useRefresh } from './components/RefreshDashboard';
import AvertissementModalPremiumEtJobbeur from '../../components/AvertissementModalPremiumEtJobbeur';

interface TabItem {
  id: string;
  name: string;
  icon: React.ReactElement<{ className?: string }>;
  path: string;
  premium?: boolean;
  highlight?: boolean;
  adminOnly?: boolean;
}

interface TabGroup {
  title: string;
  items: TabItem[];
}

interface UserContextType {
  isJobbeur: boolean;
  userType: 'jobbeur' | 'non-jobbeur';
  userData: any;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

const tabs: TabGroup[] = [
  {
    title: 'Mon Espace',
    items: [
      {
        id: 'overview',
        name: 'Tableau de Bord',
        icon: <HomeIcon />,
        path: '/dashboard'
      },
      {
        id: 'profil',
        name: 'Mon Profil',
        icon: <UserIcon />,
        path: '/dashboard/profil'
      },
      {
        id: 'favoris',
        name: 'Mes Favoris',
        icon: <Heart />,
        path: '/dashboard/profil/favoris'
      },
      {
        id: 'avis',
        name: 'Mes Avis',
        icon: <StarIcon />,
        path: '/dashboard/avis'
      },
      {
        id: 'notifications',
        name: 'Notifications',
        icon: <BellIcon />,
        path: '/dashboard/notifications'
      },
      {
        id: 'messages',
        name: 'Messages Privés',
        icon: <MessageIcon />,
        path: '/dashboard/messages'
      },
      {
        id: 'parametres',
        name: 'Paramètres',
        icon: <SettingsIcon />,
        path: '/dashboard/parametres'
      }
    ]
  },
  {
    title: 'Missions & Offres',
    items: [
      {
        id: 'post-mission',
        name: 'Poster Une Mission',
        icon: <AddIcon />,
        path: '/dashboard/missions/poster-une-mission'
      },
      {
        id: 'missions',
        name: 'Voir Les Missions',
        icon: <BuildIcon />,
        path: '/dashboard/missions'
      },
      {
        id: 'offres',
        name: 'Offres Envoyées/Reçues',
        icon: <Euro />,
        path: '/dashboard/missions/offres'
      }
    ]
  },
  {
    title: 'Planning',
    items: [
      {
        id: 'planning',
        name: 'Planning Mission',
        icon: <CalendarMonthIcon />,
        path: '/dashboard/planning',
        highlight: true,
        premium: true
      }
    ]
  },
  {
    title: 'Finances & Paiements',
    items: [
      {
        id: 'jobi',
        name: 'Mes Jobi',
        icon: <CoinIcon />,
        path: '/dashboard/jobi'
      },
      {
        id: 'ai-credits',
        name: 'Intelligence Artificielle',
        icon: <span role="img" aria-label="robot">🤖</span>,
        path: '/dashboard/ai-credits'
      },
      {
        id: 'paiements_et_finances',
        name: 'Paiements & Finances',
        icon: <GroupIcon />,
        path: '/dashboard/paiements_et_finances',
        premium: true
      },
      {
        id: 'facturation',
        name: 'Factures & Devis',
        icon: <DocumentIcon />,
        path: '/dashboard/facturation',
        premium: true
      }
    ]
  },
  {
    title: 'Outils Marketing',
    items: [
      {
        id: 'card-templates',
        name: 'Cartes & Flyers',
        icon: <CardIcon />,
        path: '/dashboard/card-templates'
      }
    ]
  },
  {
    title: 'Développement Pro',
    items: [
      {
        id: 'premium',
        name: 'Premium',
        icon: <StarIcon />,
        path: '/dashboard/premium',
        highlight: true
      },
      {
        id: 'marketing',
        name: 'Marketing & Visibilité (admin seulement)',
        icon: <MarketingIcon />,
        path: '/dashboard/marketing',
        adminOnly: true
      },
      {
        id: 'badges',
        name: 'Badges',
        icon: <CertificateIcon />,
        path: '/dashboard/badges'
      },
      {
        id: 'network',
        name: 'Réseau & Partenariats (admin seulement)',
        icon: <NetworkIcon />,
        path: '/dashboard/network',
        premium: true,
        adminOnly: true
      }
    ]
  },
  {
    title: 'Statistiques & Performance',
    items: [
      {
        id: 'historique_et_evaluations',
        name: 'Historique & Évaluations',
        icon: <BarChartIcon />,
        path: '/dashboard/historique-et-evaluations'
      },
      {
        id: 'stats',
        name: 'Statistiques Avancées',
        icon: <BarChartIcon />,
        path: '/dashboard/statistiques-avances',
        premium: true
      },
      {
        id: 'qualite_et_performance',
        name: 'Qualité & Performance',
        icon: <QualityIcon />,
        path: '/dashboard/qualite-et-performance',
        premium: true
      },
      {
        id: 'formations_et_conseils',
        name: 'Formations et Conseils',
        icon: <GrowthIcon />,
        path: '/dashboard/formations_et_conseils',
        premium: true
      }
    ]
  },
  {
    title: 'Support & Aide',
    items: [
      {
        id: 'support-tickets',
        name: 'Contactez Le Support',
        icon: <LifeBuoy />,
        path: '/dashboard/support/tickets'
      },
      {
        id: 'bug-reports',
        name: 'Suggestions & Critiques',
        icon: <Bug />,
        path: '/dashboard/bug-reports'
      }
    ]
  }
];

const jobbeurPages = [ // Pages uniquement accessibles aux jobbeurs	avec une popup
    '/dashboard/statistiques-avances',
    '/dashboard/qualite-et-performance',
    '/dashboard/network',
    '/dashboard/marketing',
    '/dashboard/facturation',
    '/dashboard/paiements_et_finances',
    '/dashboard/planning',
    '/dashboard/card-templates'
];

export default function DashboardLayout() {
  const navigate = useNavigate();
  const location = useLocation();
  const [isJobbeur, setIsJobbeur] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const { refreshCount } = useRefresh();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [showJobbeurModal, setShowJobbeurModal] = useState(false);
  const [showPremiumModal, setShowPremiumModal] = useState(false);

  useEffect(() => {
    const fetchUserType = async () => {
      try {
        const response = await axios.get('/api/users/profil', API_CONFIG);
        const userData = response.data;

        if (userData && userData.user_type) {
          setIsJobbeur(userData.user_type === 'jobbeur');
          setUserData(userData);
        }
      } catch (err: any) {
        logger.error('Erreur lors de la récupération du type utilisateur:', err);
        const errorMessage = err.response?.data?.message || 'Erreur lors de la récupération du type utilisateur';
        notify(errorMessage, 'error');
      }
    };

    fetchUserType();
  }, [refreshCount]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      // Ne cache le texte que sur desktop
      setIsSidebarOpen(width <= 767 || width > 1500);
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleNavigation = (path: string, isPremium?: boolean) => {
    // Vérifier si la page est réservée aux jobbeurs
    if (!isJobbeur && jobbeurPages.includes(path)) {
      setShowJobbeurModal(true);
    }

    // Vérifier si la page est premium
    if (isPremium && (!userData?.isPremium)) {
      setShowPremiumModal(true);
    }

    // Fermer le menu sur mobile automatiquement
    if (window.innerWidth < 768) {
      setIsMenuOpen(false);
    }
    navigate(path);

    // Remonter la fenêtre en haut pour tous les appareils
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const getSelectedTabIndex = (tabItems: TabItem[]): number => {
    const currentPath = location.pathname;
    return tabItems.findIndex(tab => {
      // Gestion exacte des chemins pour éviter les conflits
      if (tab.path === currentPath) {
        return true;
      }
    });
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(prev => !prev);
  };

  const toggleMenu = () => {
    setIsMenuOpen(prev => !prev);
  };

  return (
    <UserContext.Provider value={{ isJobbeur, userType: isJobbeur ? 'jobbeur' : 'non-jobbeur', userData }}>
      <div className="flex flex-col min-h-screen">
        <style>
          {`
            .sidebar-button {
              background-color: #FF6B2C;
              border: none;
              color: white;
              padding: 10px 15px;
              font-size: 16px;
              cursor: pointer;
              transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
              width: 100%;
              text-align: center;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }
            .sidebar-button:hover {
              background-color: #FF7A35;
              color: white;
              box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            }
            .rotate {
              transition: transform 0.3s ease; /* Transition pour la rotation */
            }
            .rotate.open {
              transform: rotate(180deg); /* Rotation de 180 degrés lorsque le menu est ouvert */
            }
            .translate {
              transition: transform 0.3s ease; /* Transition pour la translation */
            }
            .sidebar-button:hover .translate {
              transform: translateX(5px); /* Déplacement de l'icône vers la droite au survol */
            }
            .pulse {
              animation: pulse 1.5s infinite; /* Animation de pulsation */
            }
            @keyframes pulse {
              0% {
                transform: scale(1);
              }
              50% {
                transform: scale(1.05);
              }
              100% {
                transform: scale(1);
              }
            }
            .sidebar-tooltip {
              position: relative;
              display: inline-block;
            }
            .sidebar-tooltip .sidebar-tooltiptext {
              visibility: hidden;
              width: 150px;
              background-color: #FFFAF0;
              color: #333;
              text-align: center;
              border-radius: 5px;
              padding: 8px;
              position: absolute;
              left: 100%;
              top: 50%;
              transform: translateY(-50%);
              opacity: 0;
              transition: opacity 0.3s;
              margin-left: 10px;
              border: 1px solid #FF6B2C;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }
            .sidebar-tooltip:hover .sidebar-tooltiptext {
              visibility: visible;
              opacity: 1;
            }
            /* Ajout d'un contexte d'empilement pour la sidebar */
            .sidebar-container {
              position: relative;
            }

            /* Ajout d'un contexte d'empilement pour le contenu principal */
            .main-content {
              position: relative;
            }
          `}
        </style>
        <div className="flex flex-col md:flex-row pt-12 md:pt-14 lg:pt-14">
          <div className="sidebar-container bg-gray-50">
            <div className={`w-full ${isSidebarOpen ? 'md:w-80' : 'md:w-16'} border-r border-gray-200 shadow-lg transition-all duration-300`}>
              <div className="sidebar-tooltip z-50">
                <button
                  onClick={toggleSidebar}
                  className="sidebar-button hidden md:block pulse"
                  style={{ marginTop: '20px' }}
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    className={`h-5 w-5 rotate ${isSidebarOpen ? 'open' : ''} translate`}
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 18l-6-6 6-6"></path>
                  </svg>
                  <span className="sidebar-tooltiptext">{isSidebarOpen ? 'Fermer la Sidebar' : 'Ouvrir la Sidebar'}</span>
                </button>
              </div>
              <button
                onClick={toggleMenu}
                className="sidebar-button md:hidden mx-auto my-4"
                style={{ marginTop: '20px' }}
              >
                {isMenuOpen ? 'Fermer Le Menu' : 'Menu Dashboard'}
              </button>
              <div className={`h-full py-6 md:py-10 ${isMenuOpen ? 'block' : 'hidden'} md:block`}>
                {tabs.map((group, groupIndex) => (
                  <div key={groupIndex} className="mb-8">
                    <div className="px-6 mb-4">
                      {isSidebarOpen && (
                        <h3 className={`text-xs font-bold text-[#FF7A35] uppercase tracking-wider flex items-center`}>
                          <div className="h-4 w-1 bg-[#FF7A35] rounded-full mr-2"></div>
                          {group.title}
                        </h3>
                      )}
                      <div className="mt-1 h-0.5 bg-gradient-to-r from-[#FF7A35]/30 via-[#FF7A35]/10 to-transparent rounded-full"></div>
                    </div>
                    <div className="space-y-1">
                      {group.items.map((tab, index) => {
                        if (tab.adminOnly && userData?.role !== 'jobpadm') {
                          return null;
                        }
                        return (
                          <button
                            key={tab.id}
                            onClick={() => handleNavigation(tab.path, tab.premium)}
                            className={`group w-full text-left px-6 py-3 transition-all duration-200 flex items-center relative ${
                              getSelectedTabIndex(group.items) === index
                                ? 'bg-gradient-to-r from-[#FF7A35]/10 to-transparent text-gray-800 font-semibold border-l-4 border-[#FF7A35]'
                                : 'text-gray-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-transparent border-l-4 border-transparent hover:border-[#FF7A35]/30'
                            } ${tab.highlight ? 'bg-gradient-to-r from-[#FFF8F3] to-transparent' : ''}
                            ${!isJobbeur && jobbeurPages.includes(tab.path) ? 'opacity-30' : ''}
                            `}
                          >
                            <div className={`flex items-center w-full`}>
                              <div className={`p-1.5 rounded-lg transition-colors ${
                                getSelectedTabIndex(group.items) === index
                                  ? 'bg-[#FF7A35]/10'
                                  : 'group-hover:bg-[#FF7A35]/5'
                              } ${!isSidebarOpen ? 'pl-0' : ''}`}>
                                {React.cloneElement(tab.icon, {
                                  className: `h-5 w-5 transition-colors ${
                                    getSelectedTabIndex(group.items) === index
                                      ? 'text-[#FF7A35]'
                                      : tab.highlight || tab.premium
                                        ? 'text-[#FF7A35]'
                                        : 'text-gray-400 group-hover:text-[#FF7A35]'
                                  }`
                                })}
                              </div>
                              {(!isSidebarOpen && window.innerWidth > 768) && (
                                <div className={`absolute left-full ml-2 hidden group-hover:block bg-white text-gray-900 px-4 py-2 text-sm font-semibold shadow-lg rounded-md transition-all duration-300 transform max-w-0 group-hover:max-w-xs overflow-hidden z-[1000]`}>
                                  <span className="text-left whitespace-nowrap">{tab.name}</span>
                                </div>
                              )}
                              {(isSidebarOpen || window.innerWidth <= 768) && (
                                <span className={`ml-3 text-sm transition-colors ${
                                  getSelectedTabIndex(group.items) === index
                                    ? 'font-semibold'
                                    : 'group-hover:text-gray-900'
                                }`}>
                                  {tab.name}
                                </span>
                              )}
                            </div>
                            {isSidebarOpen && (
                              (tab.highlight || tab.premium) && (
                                <span className={`absolute right-0 text-xs px-30 py-0.5 transition-all duration-300 ${
                                  tab.highlight
                                    ? 'bg-gradient-to-r from-[#FF7A35] to-[#FF965B] text-white shadow-sm rounded-l-md px-2 py-1'
                                    : 'bg-[#FF7A35] text-white rounded-l-md px-4 py-1'
                                }`}>
                                  {tab.highlight ? 'PRO' : 'PRO'}
                                </span>
                              )
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
            {/* Limite la taille decran à 1480px dans app.tsx et dans dashboardlayout.tsx*/}
            <main className="main-content flex-1 p-1 md:p-6 bg-gray-50 overflow-y-auto">
              <Container
                maxWidth="lg"
                disableGutters={true}
                sx={{
                  '@media (min-width: 980px)': {
                    maxWidth: '1480px',
                    margin: '0 auto',
                    width: '100%'
                  }
                }}
              >
                <Outlet />
              </Container>
            </main>
        </div>
      </div>
      <AvertissementModalPremiumEtJobbeur
        isOpen={showJobbeurModal}
        onClose={() => setShowJobbeurModal(false)}
        type="jobbeur"
      />
      <AvertissementModalPremiumEtJobbeur
        isOpen={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        type="premium"
      />
    </UserContext.Provider>
  );
}