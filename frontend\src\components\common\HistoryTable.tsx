import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Box, Typography, Paper, Tabs, Tab, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, TextField, MenuItem, Tooltip, Button, InputAdornment, Select, FormControl, Pagination, useMediaQuery, useTheme } from '@mui/material';
import { CalendarToday, LocationOn, Computer, EventNote, AccountBox, InfoOutlined, FilterList, Star, SwipeOutlined } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import DOMPurify from 'dompurify';
import { LoginHistory, ActionHistory, LogType, PaginationData, SubscriptionInfo } from '../../hooks/useHistory';
import { styled } from '@mui/material/styles';

// Styled Components
export const StyledTableContainer = styled(TableContainer)(() => ({
  borderRadius: '12px',
  overflow: 'hidden',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  border: '1px solid #E2E8F0',
  margin: '0 8px',
  '@media (max-width: 1200px)': {
    overflowX: 'auto',
    '&::-webkit-scrollbar': {
      height: '8px',
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: '#f1f1f1',
      borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: '#FF6B2C',
      borderRadius: '4px',
    },
    '&::-webkit-scrollbar-thumb:hover': {
      backgroundColor: '#FF7A35',
    },
  },
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: 0,
    '@media (max-width: 1200px)': {
      minWidth: '850px',
    },
  },
  '& .MuiTableHead-root': {
    backgroundColor: '#FFF8F3',
    '& .MuiTableCell-root': {
      borderBottom: '2px solid #FFE4BA',
      fontWeight: 600,
      color: '#4A5568',
      fontSize: '0.9rem',
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
      padding: '12px 16px',
    },
  },
  '& .MuiTableBody-root': {
    '& .MuiTableRow-root': {
      '&:hover': {
        backgroundColor: 'rgba(255, 107, 44, 0.02)',
      },
      '&:nth-of-type(even)': {
        backgroundColor: 'rgba(249, 250, 251, 0.5)',
      },
      '& .MuiTableCell-root': {
        padding: '12px 16px',
        borderBottom: '1px solid #EDF2F7',
        fontSize: '1rem',
        whiteSpace: 'nowrap',
        '@media (max-width: 1130px)': {
          fontSize: '0.95rem',
          padding: '10px 12px',
        },
      },
    },
    '& .MuiTableRow-root:last-child .MuiTableCell-root': {
      borderBottom: 'none',
    },
  },
}));

export const StyledChip = styled(Chip)(() => ({
  fontWeight: 500,
  '&.MuiChip-filledPrimary': {
    backgroundColor: '#FF7A35',
  },
  '&.MuiChip-filledSuccess': {
    backgroundColor: '#38A169',
  },
  '&.MuiChip-filledWarning': {
    backgroundColor: '#ECC94B',
  },
  '&.MuiChip-filledError': {
    backgroundColor: '#E53E3E',
  },
  '&.MuiChip-filledInfo': {
    backgroundColor: '#3182CE',
  },
  borderRadius: '4px',
  height: '20px',
  '& .MuiChip-label': {
    fontWeight: 600,
    fontSize: '0.75rem',
    padding: '0 6px',
  },
  '&.MuiChip-outlined': {
    borderColor: 'rgba(255, 107, 44, 0.3)',
    color: '#FF6B2C',
    height: '20px',
    '& .MuiChip-label': {
      padding: '0 6px',
      fontSize: '0.75rem',
    },
    '& .MuiChip-icon': {
      color: '#FF6B2C',
      fontSize: '0.75rem',
      marginLeft: '4px',
    }
  }
}));

export const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: '#E2E8F0',
      borderRadius: '8px',
    },
    '&:hover fieldset': {
      borderColor: '#FF6B2C',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
    borderRadius: '8px',
  },
  '& .MuiOutlinedInput-input': {
    padding: '10px 14px',
  },
  '& .MuiInputAdornment-root': {
    marginRight: '-4px',
  }
});

export const StyledTab = styled(Tab)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '0.9rem',
  textTransform: 'none',
  minHeight: '48px',
  borderRadius: '8px 8px 0 0',
  marginRight: '4px',
  transition: 'all 0.2s ease',
  [theme.breakpoints.up('sm')]: {
    minWidth: '160px',
  },
  '&.Mui-selected': {
    color: '#FF6B2C',
    backgroundColor: 'rgba(255, 107, 44, 0.04)',
  },
  '&:hover:not(.Mui-selected)': {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    color: '#FF6B2C',
  }
}));

export const StyledTabs = styled(Tabs)({
  '& .MuiTabs-indicator': {
    backgroundColor: '#FF6B2C',
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTabs-flexContainer': {
    borderBottom: '1px solid #E2E8F0',
  }
});

export const InfoBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: '#FFF8F3',
  padding: theme.spacing(1.5, 2.5),
  paddingLeft: theme.spacing(2.5),
  borderRadius: '8px',
  border: '1px solid #FFE4BA',
  boxShadow: '0 1px 4px rgba(255, 107, 44, 0.05)',
  marginBottom: theme.spacing(1.5),
  position: 'relative',
  overflow: 'visible',
  transition: 'all 0.2s ease-in-out',
  width: '100%',
  maxWidth: '500px',
  minHeight: '68px',
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    paddingTop: theme.spacing(1.5),
    paddingBottom: theme.spacing(1.5),
    paddingLeft: theme.spacing(2.5),
    minHeight: 'auto',
  },
  '&:hover': {
    boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)',
    borderColor: '#FFD0A1',
  },
  '&:after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)',
    borderTopLeftRadius: '8px',
    borderBottomLeftRadius: '8px',
  },
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
    '@media (max-width: 768px)': {
      fontSize: '0.9rem',
    },
  }
}));

export const EntryCountBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  paddingLeft: theme.spacing(1.5),
  marginLeft: theme.spacing(1),
  marginTop: theme.spacing(0.5),
  position: 'relative',
  width: 'auto',
  [theme.breakpoints.down('sm')]: {
    paddingLeft: 0,
    marginLeft: 0,
    marginTop: theme.spacing(0.5),
    paddingTop: theme.spacing(0),
    width: '100%',
    '&:before': {
      display: 'none',
    }
  },
  '&:before': {
    content: '""',
    position: 'absolute',
    left: 0,
    height: '60%',
    width: '1px',
    background: 'rgba(255, 107, 44, 0.2)',
  }
}));

export const IconWrapper = styled(Box)(() => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '30px',
  height: '30px',
  borderRadius: '8px',
  marginRight: '8px',
  backgroundColor: 'rgba(255, 107, 44, 0.1)',
  color: '#FF6B2C',
  '& .MuiSvgIcon-root': {
    fontSize: '1.2rem',
  }
}));

export const EmptyStateBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(5),
  borderRadius: '12px',
  backgroundColor: '#F7FAFC',
  border: '1px dashed #E2E8F0',
  textAlign: 'center',
}));

export const StyledPagination = styled(Pagination)(() => ({
  '& .MuiPaginationItem-root': {
    color: '#4A5568',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.08)',
      color: '#FF6B2C',
    },
    '&.Mui-selected': {
      backgroundColor: 'rgba(255, 107, 44, 0.12)',
      color: '#FF6B2C',
      fontWeight: 600,
      '&:hover': {
        backgroundColor: 'rgba(255, 107, 44, 0.2)',
      },
    },
    '&.Mui-disabled': {
      color: '#CBD5E0',
    },
  },
  '@media (max-width: 900px)': {
    '& .MuiPaginationItem-root': {
      padding: '0 4px',
      minWidth: '32px',
      height: '32px',
      fontSize: '0.875rem',
    },
    '& .MuiSvgIcon-root': {
      fontSize: '1.1rem',
    }
  }
}));

export const PageSizeSelect = styled(FormControl)(({ theme }) => ({
  minWidth: 70,
  marginRight: theme.spacing(2),
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& .MuiOutlinedInput-input': {
      padding: '6px 14px',
    },
    '& fieldset': {
      borderColor: '#E2E8F0',
    },
    '&:hover fieldset': {
      borderColor: '#FF6B2C',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '@media (max-width: 900px)': {
    marginRight: theme.spacing(1.5),
    minWidth: 60,
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-input': {
        padding: '4px 8px',
        fontSize: '0.8rem',
      },
      '& .MuiSvgIcon-root': {
        fontSize: '1.1rem',
        right: '0',
      }
    },
  },
}));

export const CompactResultsText = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  whiteSpace: 'nowrap',
  fontSize: '0.875rem',
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.75rem',
    marginLeft: '4px',
    display: 'block',
  },
}));

export const ActionFilterSelect = styled(StyledTextField)(() => ({
  '& .MuiOutlinedInput-input': {
    display: 'flex',
    alignItems: 'center',
  },
  '& .MuiSelect-select': {
    paddingTop: '8px',
    paddingBottom: '8px',
  },
}));

export const PremiumButton = styled(Button)(() => ({
  backgroundColor: '#FF6B2C',
  color: 'white',
  fontWeight: 600,
  textTransform: 'none',
  padding: '4px 10px',
  fontSize: '0.75rem',
  borderRadius: '4px',
  boxShadow: '0 1px 3px rgba(255, 107, 44, 0.2)',
  '&:hover': {
    backgroundColor: '#FF7A35',
    boxShadow: '0 2px 5px rgba(255, 107, 44, 0.3)',
  },
  '& .MuiSvgIcon-root': {
    fontSize: '0.875rem',
    marginRight: '4px'
  }
}));

interface HistoryTableProps {
  title?: string;
  showSubscriptionInfo?: boolean;
  onFetchLoginHistory: (page: number, limit: number) => Promise<{
    data: LoginHistory[];
    pagination: PaginationData;
    subscription: SubscriptionInfo;
  }>;
  onFetchActionHistory: (page: number, limit: number, type: string) => Promise<{
    data: ActionHistory[];
    pagination: PaginationData;
    subscription: SubscriptionInfo;
  }>;
  onFetchActionTypes: () => Promise<LogType[]>;
  clearCache?: () => void;
  defaultTab?: number;
  className?: string;
}

// Wrapping the table with a scroll indicator for mobile view
const TableWithScrollIndicator: React.FC<{ children: React.ReactNode; visible?: boolean }> = ({ 
  children, 
  visible = true 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery('(max-width: 1200px)');

  if (!visible || !isMobile) return <>{children}</>;

  return (
    <>
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        padding: '10px 16px',
        backgroundColor: 'rgba(255, 107, 44, 0.04)',
        borderBottom: '1px solid #E2E8F0'
      }}>
        <SwipeOutlined fontSize="small" sx={{ color: '#FF6B2C', mr: 1 }} />
        <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
          Faites défiler horizontalement pour voir tout le tableau
        </Typography>
      </Box>
      {children}
    </>
  );
};

const HistoryTable: React.FC<HistoryTableProps> = ({
  showSubscriptionInfo = true,
  onFetchLoginHistory,
  onFetchActionHistory,
  onFetchActionTypes,
  clearCache,
  defaultTab = 0
}) => {
  const [tabValue, setTabValue] = useState(defaultTab);
  const [loginHistory, setLoginHistory] = useState<LoginHistory[]>([]);
  const [actionHistory, setActionHistory] = useState<ActionHistory[]>([]);
  const [actionTypes, setActionTypes] = useState<LogType[]>([]);
  const [selectedActionType, setSelectedActionType] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 25,
    totalPages: 0
  });
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);

  // Fonctions utilitaires
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
  };

  const getLocationStatus = (history: LoginHistory) => {
    if (history.city && history.country) {
      return `${history.city}${history.region ? `, ${history.region}` : ''}, ${history.country}`;
    }
    return 'Localisation inconnue';
  };

  const formatActionType = (actionType: string): string => {
    const cleanedType = DOMPurify.sanitize(actionType);

    const translations: { [key: string]: string } = {
      // Connexion
      'connexion': 'Connexion',
      'connexion_reussie': 'Connexion réussie',
      'connexion_echouee': 'Échec de connexion',
      'deconnexion': 'Déconnexion',
      'user_login': 'Connexion d\'un utilisateur',
      'user_login_2fa': 'Connexion avec authentification à deux facteurs',
      'user_logout': 'Déconnexion d\'un utilisateur',
      'connexion_utilisateur': 'Connexion',
      'deconnexion_utilisateur': 'Déconnexion',

      // Inscription
      'inscription': 'Inscription',
      'inscription_reussie': 'Inscription réussie',
      'inscription_echouee': 'Échec d\'inscription',
      'email_verifie': 'Email Vérifié',
      'user_register': 'Inscription d\'un utilisateur',
      'inscription_utilisateur': 'Inscription',
      'user_verify_email': 'Vérification d\'email',

      // Mot de passe
      'mot_de_passe_oublie': 'Mot de passe oublié',
      'reinitialisation_mdp_demandee': 'Demande de réinitialisation MDP',
      'reinitialisation_mdp_reussie': 'Réinitialisation MDP réussie',
      'changement_mot_de_passe': 'Changement de mot de passe',
      'user_reset_password': 'Réinitialisation de mot de passe',
      'user_change_password': 'Changement de mot de passe',

      // Email
      'changement_email': 'Changement d\'email',
      'user_change_email': 'Changement d\'email',

      // Profil
      'mise_a_jour_profil': 'Mise à jour du profil',
      'mise_a_jour_photo_profil': 'Mise à jour de photo de profil',
      'user_change_profil': 'Mise à jour du profil',
      'user_change_photo_profil': 'Mise à jour de la photo de profil',
      'demande_suppression_compte': 'Demande de suppression de compte',
      'gallery_photos_add_multiple': 'Ajout de photos à la galerie',
      'profile_banner_update': 'Mise à jour de la bannière de profil',

      // Jobi (système de troc/échange)
      'transfert_jobi': 'Transaction Jobi',
      'reception_jobi': 'Réception de Jobi',
      'jobi_transfer': 'Transfert de Jobi',
      'jobi_transfer_update': 'Mise à jour du transfert de Jobi',
      'jobi_transfer_delete': 'Suppression du transfert de Jobi',
      'jobi_transfer_create': 'Création du transfert de Jobi',
      'jobi_transfer_read': 'Lecture du transfert de Jobi',
      'jobi_receive': 'Réception de Jobi',

      // Missions
      'creation_mission': 'Création d\'une mission',
      'mission_create': 'Création d\'une mission',
      'mise_a_jour_mission': 'Mise à jour d\'une mission',
      'annulation_mission': 'Annulation d\'une mission',
      'completion_mission': 'Finalisation d\'une mission',
      'acceptation_mission': 'Acceptation d\'une mission',
      'refus_mission': 'Refus d\'une mission',
      'evaluation_mission': 'Création d\'un avis',
      'mission_like': 'J\'aime une mission',
      'mission_unlike': 'Je n\'aime plus une mission',
      'mission_comment': 'Commentaire sur une mission',
      'mission_comment_delete': 'Suppression d\'un commentaire',
      'mission_comment_update': 'Modification d\'un commentaire',
      'mission_planning': 'Planification d\'une mission',
      'mission_planning_update': 'Mise à jour du planning',
      'mission_planning_delete': 'Suppression du planning',
      'user_change_photo_mission': 'Mise à jour de la photo d\'une mission',
      'mission_proposal_create': 'Nouvelle proposition de mission',
      'proposal_accept': 'Proposition acceptée',
      'proposal_reject': 'Proposition refusée',

      // Services
      'ajout_service': 'Ajout d\'un service',
      'mise_a_jour_service': 'Mise à jour d\'un service',
      'suppression_service': 'Suppression d\'un service',
      'user_change_photo_service': 'Mise à jour de la photo d\'un service',

      // Galerie
      'creation_galerie': 'Création d\'une galerie',
      'mise_a_jour_galerie': 'Mise à jour d\'une galerie',
      'suppression_galerie': 'Suppression d\'une galerie',
      'ajout_photo_galerie': 'Téléchargement d\'une photo',
      'suppression_photo_galerie': 'Suppression d\'une photo',
      'user_change_photo_galerie': 'Mise à jour de la photo d\'une galerie',
      'gallery_photo_add': 'Photo ajoutée à la galerie',
      'gallery_photo_delete': 'Photo supprimée de la galerie',
      'gallery_photo_update': 'Photo de galerie modifiée',
      'profile_photo_update': 'Photo de profil mise à jour',
      'gallery_activate': 'Activation d\'une galerie',
      'gallery_deactivate': 'Désactivation d\'une galerie',
      'gallery_create': 'Création d\'une galerie',
      'gallery_delete': 'Suppression d\'une galerie',

      // Avis (Reviews)
      'review_update': 'Mise à jour d\'un avis',
      'review_delete': 'Suppression d\'un avis',
      'review_create': 'Nouvelle évaluation',
      'review_response_update': 'Mise à jour d\'une réponse à un avis',
      'review_response_delete': 'Suppression d\'une réponse à un avis',
      'review_response_create': 'Création d\'une réponse à un avis',
      'review_response_read': 'Lecture d\'une réponse à un avis',
      'review_response': 'Réponse à un avis',

      // Favoris
      'favorite_add': 'Ajout d\'un favori',
      'favorite_delete': 'Suppression d\'un favori',
      'favorite_read': 'Lecture d\'un favori',
      'favorite_update': 'Mise à jour d\'un favori',
      'favorite_create': 'Création d\'un favori',
      'favorite_remove': 'Suppression d\'un favori',

      // Messages / Notifications
      'envoi_message': 'Envoi d\'un message',
      'lecture_notification': 'Notification lue',
      'creation_conversation': 'Nouvelle conversation créée',

      // Utilisateurs (Admin)
      'user_update': 'Mise à jour d\'un utilisateur',
      'user_delete': 'Suppression d\'un utilisateur',
      'user_create': 'Création d\'un utilisateur',
      'user_read': 'Lecture d\'un utilisateur',
      'ban_user': 'Bannissement d\'un utilisateur',
      'unban_user': 'Débannissement d\'un utilisateur',

      // Factures
      'invoice_created': 'Facture créée',
      'invoice_sent': 'Facture envoyée',
      'invoice_paid': 'Facture payée',
      'invoice_overdue': 'Facture en retard',
      'invoice_cancelled': 'Facture annulée',
      'invoice_updated': 'Facture mise à jour',
      'invoice_payment_failed': 'Échec paiement facture',
      'invoice_reminder_sent': 'Rappel de facture envoyé',
      'creation_facture': 'Création d\'une facture',
      'modification_facture': 'Modification d\'une facture',
      'suppression_facture': 'Suppression d\'une facture',
      'modification_statut_facture': 'Changement de statut d\'une facture',
      'envoi_facture': 'Envoi d\'une facture',

      // Devis
      'quote_created': 'Devis créé',
      'quote_sent': 'Devis envoyé',
      'quote_accepted': 'Devis accepté',
      'quote_rejected': 'Devis refusé',
      'quote_cancelled': 'Devis annulé',
      'quote_updated': 'Devis mis à jour',
      'quote_converted_to_invoice': 'Devis converti en facture',
      'creation_devis': 'Création d\'un devis',
      'modification_devis': 'Modification d\'un devis',
      'suppression_devis': 'Suppression d\'un devis',
      'modification_statut_devis': 'Changement de statut d\'un devis',
      'envoi_devis': 'Envoi d\'un devis',

      // Avoirs
      'creation_avoir': 'Création d\'un avoir',
      'modification_avoir': 'Modification d\'un avoir',
      'suppression_avoir': 'Suppression d\'un avoir',
      'modification_statut_avoir': 'Changement de statut d\'un avoir',
      'envoi_avoir': 'Envoi d\'un avoir',

      // Abonnements
      'subscription_activated': 'Abonnement activé',
      'subscription_cancelled': 'Abonnement annulé',
      'subscription_renewed': 'Abonnement renouvelé',
      'subscription_payment_failed': 'Échec paiement abonnement',
      'subscription_trial_started': 'Période d\'essai démarrée',
      'subscription_trial_ended': 'Période d\'essai terminée',
      'subscription_plan_changed': 'Changement de plan d\'abonnement',
      'subscription_upcoming_renewal_notice': 'Avis de renouvellement imminent',
      'reactivation_renouvellement_auto': 'Réactivation du renouvellement automatique',
      'desactivation_renouvellement_auto': 'Désactivation du renouvellement automatique',
      'renouvellement_abonnement': 'Renouvellement de l\'abonnement',
      'renewal_abonnement': 'Renouvellement de l\'abonnement',
      'passage_premium': 'Passage à Premium',
      'passage_gratuit': 'Retour à l\'offre gratuite',
      'downgrade_gratuit': 'Retour à l\'offre gratuite',
      'upgrade_premium': 'Passage à Premium',
      'cancel_auto_renew': 'Désactivation du renouvellement automatique',
      'reactivate_auto_renew': 'Réactivation du renouvellement automatique',
      'reactivation_after_unban': 'Réactivation d\'abonnement après débannissement',

      // Paramètres du compte
      'account_settings_updated': 'Paramètres du compte mis à jour',
      'notification_settings_updated': 'Préférences de notification mises à jour',
      'privacy_settings_updated': 'Paramètres de confidentialité mis à jour',
      'account_deletion_requested': 'Demande de suppression de compte',
      'account_deleted': 'Compte supprimé',

      // Support client
      'support_ticket_created': 'Ticket de support créé',
      'support_ticket_updated': 'Ticket de support mis à jour',
      'support_ticket_closed': 'Ticket de support fermé',
      'support_message_sent': 'Message de support envoyé',
      'support_ticket_create': 'Création d\'un ticket support',
      'support_ticket_update': 'Mise à jour d\'un ticket support',
      'support_ticket_delete': 'Suppression d\'un ticket support',

      // Sécurité
      'security_alert': 'Alerte de sécurité',
      'password_policy_updated': 'Politique de mot de passe mise à jour',
      'mfa_enabled': 'Authentification multifacteur activée',
      'mfa_disabled': 'Authentification multifacteur désactivée',
      'two_factor_enabled': 'Authentification à deux facteurs activée',
      'two_factor_disabled': 'Authentification à deux facteurs désactivée',
      'two_factor_verified': 'Authentification à deux facteurs vérifiée',
      'suspicious_activity_detected': 'Activité suspecte détectée',
      'account_locked': 'Compte verrouillé',
      'account_unlocked': 'Compte déverrouillé',

      // Autres
      'creation_rapport_bug': 'Création d\'un rapport de bug',
      'generate_random_card_template': 'Génération d\'un modèle de carte aléatoire',
      'auto_deactivate_card_template': 'Désactivation automatique du modèle de carte',
      'update_card_template': 'Mise à jour du modèle de carte',

      // Crédits IA
      'achat_credits_ia_jobi': 'Achat de crédits IA (Jobi)',
      'achat_credits_ia_stripe': 'Achat de crédits IA (Stripe)',
      'utilisation_credit_ia': 'Utilisation d\'un crédit IA',
      'credits_ia_offerts_abonnement': 'Crédits IA offerts (Abonnement)',
      'credits_ia_offerts_admin': 'Crédits IA offerts (Admin)',
      'operation_credits_ia': 'Opération sur les crédits IA',
      'ai_credits_purchase_jobi': 'Achat de crédits IA (Jobi)',
      'ai_credits_purchase_stripe': 'Achat de crédits IA (Stripe)',
      'ai_credits_used': 'Utilisation d\'un crédit IA',
      'ai_credits_subscription_gift': 'Crédits IA offerts (Abonnement)',
      'ai_credits_admin_gift': 'Crédits IA offerts (Admin)',
      'ai_credits_other': 'Opération sur les crédits IA',
      'ai_credit_used': 'Utilisation d\'un crédit IA',
      'ai_credits_purchased': 'Achat de crédits IA (Jobi)',
      'ai_credits_checkout_created': 'Création d\'un paiement pour crédits IA',
      'ai_credits_purchased_stripe': 'Achat de crédits IA (Stripe)',
      'ai_consent': 'Consentement IA',
      // Ajout des nouveaux types
      'ai_prompt_deleted': 'Prompt IA supprimé',
      'ai_prompt_saved': 'Prompt IA sauvegardé',
      'image_confirmed': 'Image IA validée',

      // Admin
      'admin_view_user_history': 'Affichage de l\'historique de l\'utilisateur',
      'admin_photo_moderation': 'Modération de la photo de profil',
    };

    return translations[cleanedType] || cleanedType;
  };

// Fonction pour obtenir la couleur du chip en fonction du type
const getChipColor = (type: string) => {
    const colorMap: { [key: string]: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' } = {
      'connexion_utilisateur': 'info',
      'deconnexion_utilisateur': 'info',
      'user_login_2fa': 'info',
      'inscription_utilisateur': 'success',
      'changement_mot_de_passe': 'warning',
      'mise_a_jour_profil': 'success',
      'changement_email': 'warning',
      'mise_a_jour_photo_profil': 'success',
      'suppression_photo_galerie': 'error',
      'email_verifie': 'info',
      'two_factor_enabled': 'success',
      'two_factor_disabled': 'warning',
      'two_factor_verified': 'success',
      'transfert_jobi': 'primary',
      'reception_jobi': 'success',
      'mise_a_jour_mission': 'info',
      'creation_mission': 'success',
      'annulation_mission': 'error',
      'completion_mission': 'success',
      'acceptation_mission': 'success',
      'refus_mission': 'warning',
      'ajout_service': 'success',
      'mise_a_jour_service': 'info',
      'suppression_service': 'error',
      'creation_galerie': 'success',
      'mise_a_jour_galerie': 'info',
      'suppression_galerie': 'error',
      'creation_conversation': 'success',
      'gallery_photo_add': 'success',
      'gallery_activate': 'success',
      'gallery_deactivate': 'warning',
      'gallery_create': 'success',
      'gallery_delete': 'error',
      'evaluation_mission': 'success',
      'envoi_message': 'info',
      'lecture_notification': 'info',
      'review_update': 'info',
      'review_delete': 'error',
      'review_create': 'success',
      'review_response_update': 'info',
      'review_response_delete': 'error',
      'review_response_create': 'success',
      'review_response_read': 'info',
      'favorite_add': 'success',
      'favorite_delete': 'error',
      'favorite_read': 'info',
      'favorite_update': 'info',
      'favorite_create': 'success',
      'favorite_remove': 'error',
      'jobi_transfer': 'primary',
      'jobi_transfer_update': 'info',
      'jobi_transfer_delete': 'error',
      'jobi_transfer_create': 'success',
      'jobi_transfer_read': 'info',
      'user_update': 'info',
      'user_delete': 'error',
      'user_create': 'success',
      'user_read': 'info',
      'user_login': 'info',
      'user_logout': 'info',
      'user_register': 'success',
      'user_reset_password': 'warning',
      'user_verify_email': 'success',
      'user_change_password': 'warning',
      'user_change_email': 'warning',
      'user_change_profil': 'info',
      'user_change_photo_profil': 'success',
      'user_change_photo_galerie': 'success',
      'user_change_photo_service': 'success',
      'user_change_photo_mission': 'success',
      'mission_like': 'primary',
      'mission_unlike': 'error',
      'mission_comment': 'info',
      'mission_comment_delete': 'error',
      'mission_comment_update': 'info',
      'mission_planning': 'success',
      'mission_planning_update': 'info',
      'mission_planning_delete': 'error',
      'demande_suppression_compte': 'error',
      'profile_banner_update': 'success',
      'conversation_create': 'success',
      'conversation_update': 'info',
      'conversation_delete': 'error',
      'conversation_read': 'info',
      'conversation_message_send': 'info',
      'conversation_message_delete': 'error', 
      'mission_proposal_create': 'success',
      'gallery_photo_delete': 'error',
      'gallery_photo_update': 'info',
      'gallery_photo_create': 'success',
      'gallery_photo_read': 'info',
      'gallery_photo_remove': 'error',
      'gallery_photos_add_multiple': 'success',
      'generate_random_card_template': 'success',
      'auto_deactivate_card_template': 'error',
      'update_card_template': 'info',

      // Factures
      'creation_facture': 'success',
      'modification_facture': 'info',
      'suppression_facture': 'error',
      'modification_statut_facture': 'warning',
      'envoi_facture': 'primary',

      // Devis
      'creation_devis': 'success',
      'modification_devis': 'info',
      'suppression_devis': 'error',
      'modification_statut_devis': 'warning',
      'envoi_devis': 'primary',

      // Avoirs
      'creation_avoir': 'success',
      'modification_avoir': 'info',
      'suppression_avoir': 'error',
      'modification_statut_avoir': 'warning',
      'envoi_avoir': 'primary',
      'reactivation_renouvellement_auto': 'info',
      'desactivation_renouvellement_auto': 'error',
      'renouvellement_abonnement': 'success',
      'renewal_abonnement': 'success',
      'passage_premium': 'primary',
      'passage_gratuit': 'success',
      'downgrade_gratuit': 'warning',
      'upgrade_premium': 'primary',
      'cancel_auto_renew': 'error',
      'reactivate_auto_renew': 'success',
      'ban_user': 'error',
      'unban_user': 'success',
      'reactivation_after_unban': 'success',
      'jobi_receive': 'success',
      'support_ticket_create': 'primary',
      'creation_rapport_bug': 'error',
      // Authentification à deux facteurs
      'connexion_utilisateur_2fa': 'info',
      'activation_2fa': 'success',
      'verification_2fa': 'success',
      'desactivation_2fa': 'warning',

      // Crédits IA
      'achat_credits_ia_jobi': 'primary',
      'achat_credits_ia_stripe': 'primary',
      'utilisation_credit_ia': 'info',
      'credits_ia_offerts_abonnement': 'success',
      'credits_ia_offerts_admin': 'success',
      'operation_credits_ia': 'info',
      'ai_credits_purchase_jobi': 'primary',
      'ai_credits_purchase_stripe': 'primary',
      'ai_credits_used': 'info',
      'ai_credits_subscription_gift': 'success',
      'ai_credits_admin_gift': 'success',
      'ai_credits_other': 'info',
      'ai_credit_used': 'info',
      'ai_credits_purchased': 'primary',
      'ai_credits_checkout_created': 'warning',
      'ai_credits_purchased_stripe': 'primary',
      'ai_consent': 'success',
      // Ajout des nouveaux types
      'ai_prompt_deleted': 'error',
      'ai_prompt_saved': 'success',
      'image_confirmed': 'primary',

      // Admin
      'admin_view_user_history': 'info',
      'admin_photo_moderation': 'error',
    };

    return colorMap[type] || 'default';
  };

  const renderActionDetails = (action: ActionHistory) => {
    let details = 'Aucun détail disponible';

    // Traitement spécial pour les actions spécifiques
    if (action.action_type === 'renewal_abonnement' || action.action_type === 'renouvellement_abonnement') {
      return 'Votre abonnement Premium a été renouvelé automatiquement';
    }

    if (action.action_type === 'unban_user') {
      return 'Votre compte a été réactivé';
    }

    if (action.action_type === 'gallery_photos_add_multiple') {
      return 'Ajout de photos à la galerie';
    }

    if (action.action_type === 'demande_suppression_compte') {
      return 'Demande de suppression de compte';
    }

    if (action.action_type === 'profile_banner_update') {
      return 'Mise à jour de la bannière de profil';
    }

    if (action.action_type === 'generate_random_card_template') {
      return 'Génération d\'un modèle de carte aléatoire';
    }

    if (action.action_type === 'auto_deactivate_card_template') {
      return 'Désactivation automatique du modèle de carte';
    }

    if (action.action_type === 'update_card_template') {
      return 'Mise à jour du modèle de carte';
    }

    if (action.action_type === 'reactivation_after_unban') {
      return 'Votre abonnement a été réactivé suite au débannissement de votre compte';
    }

    if (action.action_type === 'reactivate_auto_renew') {
      return 'Vous avez réactivé le renouvellement automatique de votre abonnement';
    }

    if (action.action_type === 'two_factor_enabled' || action.action_type === 'activation_2fa') {
      return 'Authentification à deux facteurs activée pour votre compte';
    }

    if (action.action_type === 'two_factor_verified' || action.action_type === 'verification_2fa') {
      return 'Authentification à deux facteurs vérifiée avec succès';
    }

    if (action.action_type === 'two_factor_disabled' || action.action_type === 'desactivation_2fa') {
      return 'Authentification à deux facteurs désactivée pour votre compte';
    }

    if (action.action_type === 'user_login_2fa' || action.action_type === 'connexion_utilisateur_2fa') {
      return 'Connexion sécurisée avec authentification à deux facteurs';
    }

    // Crédits IA
    if (action.action_type === 'achat_credits_ia_jobi' || action.action_type === 'ai_credits_purchase_jobi') {
      const montant = action.details?.montant || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA avec des Jobi`;
    }

    if (action.action_type === 'achat_credits_ia_stripe' || action.action_type === 'ai_credits_purchase_stripe') {
      const montant = action.details?.montant || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA via Stripe`;
    }

    if (action.action_type === 'utilisation_credit_ia' || action.action_type === 'ai_credits_used' || action.action_type === 'ai_credit_used') {
      return 'Utilisation d\'un crédit IA';
    }

    if (action.action_type === 'credits_ia_offerts_abonnement' || action.action_type === 'ai_credits_subscription_gift') {
      const montant = action.details?.montant || '';
      return `${montant} crédit${montant > 1 ? 's' : ''} IA offert${montant > 1 ? 's' : ''} avec votre abonnement`;
    }

    if (action.action_type === 'credits_ia_offerts_admin' || action.action_type === 'ai_credits_admin_gift') {
      const montant = action.details?.montant || '';
      return `${montant} crédit${montant > 1 ? 's' : ''} IA offert${montant > 1 ? 's' : ''} par l'administrateur`;
    }

    if (action.action_type === 'ai_credits_purchased' || action.action_type === 'ai_credits_purchased_stripe') {
      const montant = action.details?.credits_purchased || action.details?.total_credits_purchased || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA${action.action_type === 'ai_credits_purchased_stripe' ? ' via Stripe' : ' avec des Jobi'}`;
    }

    if (action.action_type === 'ai_credits_checkout_created') {
      const montant = action.details?.credits_to_purchase || '';
      return `Création d'une session de paiement pour ${montant} crédit${montant > 1 ? 's' : ''} IA`;
    }

    if (action.action_type === 'gallery_activate') {
      return action.details?.gallery_name ?
        `Galerie "${action.details.gallery_name}" activée` :
        'Galerie activée';
    }

    if (action.action_type === 'gallery_deactivate') {
      return action.details?.gallery_name ?
        `Galerie "${action.details.gallery_name}" désactivée` :
        'Galerie désactivée';
    }

    if (action.action_type === 'creation_galerie' || action.action_type === 'gallery_create') {
      return action.details?.gallery_name ?
        `Nouvelle galerie créée : "${action.details.gallery_name}"` :
        'Nouvelle galerie créée';
    }

    if (action.action_type === 'gallery_update' || action.action_type === 'mise_a_jour_galerie') {
      return action.details?.gallery_name ?
        `Galerie mise à jour : "${action.details.gallery_name}"` :
        'Galerie mise à jour';
    }

    if (action.action_type === 'gallery_delete' || action.action_type === 'suppression_galerie') {
      return action.details?.gallery_name ?
        `Galerie supprimée : "${action.details.gallery_name}"` :
        'Galerie supprimée';
    }

    if (action.action_type === 'gallery_photo_add') {
      const galleryName = action.details?.gallery_name;
      return galleryName ? `Photo ajoutée à la galerie "${galleryName}"` : 'Photo ajoutée à la galerie';
    }

    if (action.action_type === 'ai_consent') {
      const detailsObj = typeof action.details === 'string' ? JSON.parse(action.details) : action.details;
      if (detailsObj && (detailsObj.firstName || detailsObj.lastName)) {
        return `Consentement IA donné : ${detailsObj.firstName || ''} ${detailsObj.lastName || ''}`.trim();
      }
    }

    if (action.details) {
      try {
        const detailsObj = typeof action.details === 'string' ? JSON.parse(action.details) : action.details;

        if (detailsObj.message) {
          details = detailsObj.message;
        } else if (detailsObj.name) {
          details = detailsObj.name;
        } else if (typeof detailsObj === 'string') {
          details = detailsObj;
        } else {
          details = Object.keys(detailsObj)
            .map(key => `${key}: ${detailsObj[key]}`)
            .join(', ');
        }
      } catch (e) {
        details = String(action.details);
      }
    }

    // Prompts IA
    if (action.action_type === 'ai_prompt_saved') {
      const promptName = action.details?.prompt_name || '';
      return promptName ? `Prompt IA sauvegardé : "${promptName}"` : 'Prompt IA sauvegardé';
    }
    if (action.action_type === 'ai_prompt_deleted') {
      const promptName = action.details?.prompt_name || '';
      return promptName ? `Prompt IA supprimé : "${promptName}"` : 'Prompt IA supprimé';
    }

    if (action.action_type === 'image_confirmed') {
      return 'Image générée par IA validée';
    }

    if (action.action_type === 'inscription_utilisateur') {
      return 'Nouvelle inscription utilisateur';
    }

    if (action.action_type === 'admin_photo_moderation') {
      return 'Modération de la photo de profil';
    }

    if (action.action_type === 'admin_view_user_history') {
      return `Affichage de l'historique de l'utilisateur`;
    }

    return DOMPurify.sanitize(details);
  };

  // Gestionnaires d'événements
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    // Réinitialiser la recherche quand on change d'onglet
    setSearchQuery('');

    // Forcer un rechargement des données avec de nouvelles données
    // en invalidant explicitement le cache pour l'onglet sélectionné
    if (newValue === 0) {
      // Pour l'onglet des connexions
      clearCache && clearCache();
    } else {
      // Pour l'onglet des actions
      clearCache && clearCache();
    }
  };

  const handlePageChange = async (_event: unknown, newPage: number) => {
    try {
      setLoading(true);
      const response = await (tabValue === 0
        ? onFetchLoginHistory(newPage, pagination.limit)
        : onFetchActionHistory(newPage, pagination.limit, selectedActionType)
      );

      if (tabValue === 0) {
        setLoginHistory(response.data as LoginHistory[]);
      } else {
        setActionHistory(response.data as ActionHistory[]);
      }

      setPagination(response.pagination);
      setSubscriptionInfo(response.subscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleRowsPerPageChange = async (newLimit: number) => {
    try {
      setLoading(true);
      const response = await (tabValue === 0
        ? onFetchLoginHistory(1, newLimit)
        : onFetchActionHistory(1, newLimit, selectedActionType)
      );

      if (tabValue === 0) {
        setLoginHistory(response.data as LoginHistory[]);
      } else {
        setActionHistory(response.data as ActionHistory[]);
      }

      setPagination(response.pagination);
      setSubscriptionInfo(response.subscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleActionTypeChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newType = event.target.value;
    setSelectedActionType(newType);
    try {
      setLoading(true);
      const response = await onFetchActionHistory(1, pagination.limit, newType);
      setActionHistory(response.data as ActionHistory[]);
      setPagination(response.pagination);
      setSubscriptionInfo(response.subscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Utiliser useCallback pour memoiser les fonctions de chargement
  const loadActionTypes = useCallback(async () => {
    try {
      const types = await onFetchActionTypes();
      setActionTypes(types);
      return types;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des types d\'actions');
      return [];
    }
  }, [onFetchActionTypes]);

  const loadHistoryData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await (tabValue === 0
        ? onFetchLoginHistory(pagination.page, pagination.limit)
        : onFetchActionHistory(pagination.page, pagination.limit, selectedActionType)
      );

      if (tabValue === 0) {
        setLoginHistory(response.data as LoginHistory[]);
      } else {
        setActionHistory(response.data as ActionHistory[]);
      }

      setPagination(response.pagination);
      setSubscriptionInfo(response.subscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  }, [tabValue, pagination.page, pagination.limit, selectedActionType, onFetchLoginHistory, onFetchActionHistory]);

  // Effet d'initialisation exécuté une seule fois
  useEffect(() => {
    const init = async () => {
      try {
        setLoading(true);
        // Charger les types d'actions une seule fois
        await loadActionTypes();
        // Charger les données initiales
        await loadHistoryData();
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur lors du chargement initial');
      } finally {
        setLoading(false);
      }
    };

    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Effet optimisé pour charger les données lorsque les dépendances changent
  useEffect(() => {
    // Toujours charger les données fraîches au changement d'onglet
    loadHistoryData();
  }, [tabValue, pagination.page, pagination.limit, selectedActionType, loadHistoryData]);

  // Listes filtrées mémorisées
  const filteredLoginHistory = useMemo(() => {
    const query = searchQuery.toLowerCase();
    if (!query) return loginHistory;

    return loginHistory.filter(item =>
      item.ip_address.toLowerCase().includes(query) ||
      (item.city && item.city.toLowerCase().includes(query)) ||
      (item.country && item.country.toLowerCase().includes(query)) ||
      (item.region && item.region.toLowerCase().includes(query))
    );
  }, [loginHistory, searchQuery]);

  const filteredActionHistory = useMemo(() => {
    const query = searchQuery.toLowerCase();
    if (!query) return actionHistory;

    return actionHistory.filter(item =>
      formatActionType(item.action_type).toLowerCase().includes(query) ||
      item.ip_address.toLowerCase().includes(query) ||
      (typeof item.details === 'string' && item.details.toLowerCase().includes(query)) ||
      (typeof item.details === 'object' && JSON.stringify(item.details).toLowerCase().includes(query))
    );
  }, [actionHistory, searchQuery, formatActionType]);

  // Rendu du composant
  return (
    <Paper className="overflow-hidden">
      <div className="flex flex-col p-4 bg-white rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-orange-50">
              <svg className="w-5 h-5 text-orange-500" viewBox="0 0 24 24">
                <path fill="currentColor" d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"/>
              </svg>
            </div>
            <span className="font-semibold text-gray-800">Historique d'activité</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {subscriptionInfo?.type === 'premium' ? '150/150' : '20/20'} entrées
            </span>
            {subscriptionInfo?.type !== 'premium' && (
              <a href="/dashboard/premium" className="flex items-center gap-1 px-3 py-1 text-sm text-orange-500 bg-orange-50 rounded-full hover:bg-orange-100 transition-colors">
                <svg className="w-4 h-4" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                </svg>
                <span>Passer Premium</span>
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Onglets */}
      <Box sx={{ mb: 3 }}>
        <StyledTabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="historique tabs"
          variant="fullWidth"
        >
          <StyledTab
            icon={<CalendarToday fontSize="small" />}
            iconPosition="start"
            label="Historique des connexions"
          />
          <StyledTab
            icon={<AccountBox fontSize="small" />}
            iconPosition="start"
            label="Activités du compte"
          />
        </StyledTabs>
      </Box>

      {/* Contenu principal */}
      <Box className="mb-4">
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="error"
              sx={{ mb: 3 }}
              variant="filled"
            >
              {error}
            </Alert>
          </motion.div>
        )}

        {loading ? (
          <Box className="flex flex-col items-center justify-center p-10 mt-2">
            <CircularProgress sx={{ color: '#FF6B2C' }} />
            <Typography className="mt-4 text-gray-500">
              Chargement de l'historique...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Tableau des connexions */}
            {tabValue === 0 && (
              <>
                {filteredLoginHistory.length === 0 ? (
                  <EmptyStateBox>
                    <LocationOn sx={{ fontSize: 48, color: '#94A3B8', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      Aucune connexion trouvée
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {searchQuery ? 'Essayez avec une autre recherche.' : 'Aucun historique de connexion disponible.'}
                    </Typography>
                  </EmptyStateBox>
                ) : (
                  <TableWithScrollIndicator>
                    <StyledTableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Date</TableCell>
                            <TableCell>Adresse IP</TableCell>
                            <TableCell>Localisation</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {filteredLoginHistory.map(history => (
                            <TableRow key={history.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <CalendarToday fontSize="small" />
                                  </IconWrapper>
                                  {formatDate(history.login_date)}
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <Computer fontSize="small" />
                                  </IconWrapper>
                                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                    {history.ip_address}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <LocationOn fontSize="small" />
                                  </IconWrapper>
                                  {getLocationStatus(history)}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </StyledTableContainer>
                  </TableWithScrollIndicator>
                )}
              </>
            )}

            {/* Tableau des actions */}
            {tabValue === 1 && (
              <>
                <Box className="flex flex-col md:flex-row justify-between items-center mb-4 gap-3">
                  <ActionFilterSelect
                    select
                    label="Type d'activité"
                    value={selectedActionType}
                    onChange={handleActionTypeChange}
                    variant="outlined"
                    size="small"
                    className="md:min-w-[250px] w-full self-end"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <FilterList className="text-gray-400" fontSize="small" />
                        </InputAdornment>
                      ),
                    }}
                  >
                    {actionTypes.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.value === 'all' ? option.label : formatActionType(option.value)}
                      </MenuItem>
                    ))}
                  </ActionFilterSelect>
                </Box>

                {filteredActionHistory.length === 0 ? (
                  <EmptyStateBox>
                    <EventNote sx={{ fontSize: 48, color: '#94A3B8', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      Aucune action trouvée
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {searchQuery
                        ? 'Essayez avec une autre recherche.'
                        : selectedActionType !== 'all'
                          ? 'Aucune action de ce type n\'est disponible.'
                          : 'Aucun historique d\'action disponible.'
                      }
                    </Typography>
                  </EmptyStateBox>
                ) : (
                  <TableWithScrollIndicator>
                    <StyledTableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Date</TableCell>
                            <TableCell>Type d'action</TableCell>
                            <TableCell>Adresse IP</TableCell>
                            <TableCell>Détails</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {filteredActionHistory.map(action => (
                            <TableRow key={action.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <CalendarToday fontSize="small" />
                                  </IconWrapper>
                                  {formatDate(action.action_date)}
                                </Box>
                              </TableCell>
                              <TableCell>
                                <StyledChip
                                  label={formatActionType(action.action_type)}
                                  size="small"
                                  color={getChipColor(action.action_type)}
                                />
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <Computer fontSize="small" />
                                  </IconWrapper>
                                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                    {action.ip_address}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <IconWrapper>
                                    <EventNote fontSize="small" />
                                  </IconWrapper>
                                  <Tooltip title={renderActionDetails(action)}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        maxWidth: '300px',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                        fontSize: '0.95rem',
                                      }}
                                    >
                                      {renderActionDetails(action)}
                                    </Typography>
                                  </Tooltip>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </StyledTableContainer>
                  </TableWithScrollIndicator>
                )}
              </>
            )}

            {/* Pagination */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'center', md: 'center' },
                mt: 3,
                pt: 2,
                px: 2,
                borderTop: '1px solid #EDF2F7',
                gap: { xs: 2, md: 0 }
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', md: 'row' },
                  alignItems: { xs: 'center', md: 'center' },
                  justifyContent: { xs: 'center', md: 'flex-start' },
                  width: '100%',
                  gap: { xs: 2, md: 2 }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: { xs: '2px 0', md: '3px 0' },
                    width: { xs: '100%', md: 'auto' },
                  }}
                >
                  <PageSizeSelect size="small">
                    <Select
                      value={pagination.limit}
                      onChange={(e) => handleRowsPerPageChange(e.target.value as number)}
                      size="small"
                    >
                      {[5, 10, 25, 50].map((size) => (
                        <MenuItem key={size} value={size}>
                          {size}
                        </MenuItem>
                      ))}
                    </Select>
                  </PageSizeSelect>

                  <CompactResultsText variant="caption">
                    {pagination.total === 0
                      ? 'Aucun résultat'
                      : `${Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)}-${Math.min(pagination.page * pagination.limit, pagination.total)} sur ${pagination.total}`}
                  </CompactResultsText>
                </Box>

                {pagination.totalPages > 0 && pagination.totalPages > 1 && (
                  <Box sx={{ width: { xs: '100%', md: 'auto' }, display: 'flex', justifyContent: 'center' }}>
                    <StyledPagination
                      count={pagination.totalPages}
                      page={pagination.page}
                      onChange={handlePageChange}
                      shape="rounded"
                      size="small"
                      siblingCount={window.innerWidth < 600 ? 0 : 1}
                    />
                  </Box>
                )}
              </Box>

              {showSubscriptionInfo && (
                <>
                  {/* Version desktop */}
                  <Box
                    sx={{
                      display: { xs: 'none', md: 'flex' },
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 2,
                      minWidth: { md: '200px' },
                      justifyContent: 'flex-end'
                    }}
                  >
                    {pagination.historyLimit && (
                      <Box
                        sx={{
                          borderRadius: '20px',
                          bgcolor: 'rgba(255, 107, 44, 0.04)',
                          border: '1px solid rgba(255, 107, 44, 0.1)',
                          px: 2,
                          py: 0.5,
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <Typography variant="caption" sx={{ color: '#FF6B2C', fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                          <InfoOutlined sx={{ fontSize: '12px', mr: 0.5, color: 'rgba(255, 107, 44, 0.6)' }} />
                          Limite : {pagination.historyLimit} entrées
                        </Typography>
                      </Box>
                    )}

                    {subscriptionInfo?.type === 'gratuit' && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                      >
                        <PremiumButton
                          variant="contained"
                          size="small"
                          href="/dashboard/premium"
                          startIcon={<Star sx={{ fontSize: '12px' }} />}
                        >
                          Passer à Premium
                        </PremiumButton>
                      </motion.div>
                    )}
                  </Box>

                  {/* Version mobile */}
                  <Box
                    sx={{
                      display: { xs: 'flex', md: 'none' },
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 2,
                      width: '100%',
                      mt: 1,
                      mb: 1
                    }}
                  >
                    {pagination.historyLimit && (
                      <Box
                        sx={{
                          borderRadius: '20px',
                          bgcolor: 'rgba(255, 107, 44, 0.04)',
                          border: '1px solid rgba(255, 107, 44, 0.1)',
                          px: 2,
                          py: 0.5,
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <Typography variant="caption" sx={{ color: '#FF6B2C', fontWeight: 500, display: 'flex', alignItems: 'center' }}>
                          <InfoOutlined sx={{ fontSize: '12px', mr: 0.5, color: 'rgba(255, 107, 44, 0.6)' }} />
                          Limite : {pagination.historyLimit} entrées
                        </Typography>
                      </Box>
                    )}

                    {subscriptionInfo?.type === 'gratuit' && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                      >
                        <PremiumButton
                          variant="contained"
                          size="small"
                          href="/dashboard/premium"
                          startIcon={<Star sx={{ fontSize: '12px' }} />}
                        >
                          Passer à Premium
                        </PremiumButton>
                      </motion.div>
                    )}
                  </Box>
                </>
              )}
            </Box>
          </>
        )}
      </Box>
    </Paper>
  );
};

export default HistoryTable;