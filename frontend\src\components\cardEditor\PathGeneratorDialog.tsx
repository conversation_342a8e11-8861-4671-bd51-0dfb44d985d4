import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Slider,
  Grid,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import { Refresh, Add } from '@mui/icons-material';
import { Stage, Layer, Path } from 'react-konva';
import { ShapeElement } from '../../types/cardEditor';
import { 
  generateRandomPath, 
  generateRandomCurvePath, 
  generateRandomZigzagPath, 
  generateWavePath, 
  generateAbstractShape 
} from '../../utils/pathGenerator';

interface PathGeneratorDialogProps {
  open: boolean;
  onClose: () => void;
  onAdd: (element: ShapeElement) => void;
}

const PathGeneratorDialog: React.FC<PathGeneratorDialogProps> = ({
  open,
  onClose,
  onAdd
}) => {
  // États pour les propriétés du chemin
  const [pathType, setPathType] = useState<'curve' | 'zigzag' | 'wave' | 'abstract'>('curve');
  const [pathData, setPathData] = useState<string>('');
  const [strokeColor, setStrokeColor] = useState<string>('#000000');
  const [strokeWidth, setStrokeWidth] = useState<number>(3);
  const [fillColor, setFillColor] = useState<string>('transparent');
  const [opacity, setOpacity] = useState<number>(1);
  const [customPathData, setCustomPathData] = useState<string>('');
  const [useCustomPath, setUseCustomPath] = useState<boolean>(false);
  const [previewWidth, setPreviewWidth] = useState<number>(200);
  const [previewHeight, setPreviewHeight] = useState<number>(200);

  // Générer un nouveau chemin aléatoire
  const generateNewPath = () => {
    const newPath = generateRandomPath(pathType, previewWidth, previewHeight);
    setPathData(newPath);
    setCustomPathData(newPath);
  };

  // Générer un chemin initial au chargement
  useEffect(() => {
    generateNewPath();
  }, [pathType]);

  // Mettre à jour le chemin lorsque les données personnalisées changent
  useEffect(() => {
    if (useCustomPath) {
      setPathData(customPathData);
    }
  }, [useCustomPath, customPathData]);

  // Ajouter le chemin au canvas
  const handleAddPath = () => {
    const element: ShapeElement = {
      id: crypto.randomUUID(),
      type: 'shape',
      x: 50,
      y: 50,
      width: previewWidth,
      height: previewHeight,
      properties: {
        shape: 'path',
        data: pathData,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        opacity: opacity
      }
    };

    onAdd(element);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Générateur de Courbes SVG</DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {/* Panneau de contrôle */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Type de courbe</InputLabel>
                <Select
                  value={pathType}
                  onChange={(e) => setPathType(e.target.value as any)}
                  label="Type de courbe"
                >
                  <MenuItem value="curve">Courbe de Bézier</MenuItem>
                  <MenuItem value="zigzag">Zigzag</MenuItem>
                  <MenuItem value="wave">Onde</MenuItem>
                  <MenuItem value="abstract">Forme abstraite</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="body2" sx={{ mr: 2, minWidth: 100 }}>Couleur du trait:</Typography>
                <input
                  type="color"
                  value={strokeColor}
                  onChange={(e) => setStrokeColor(e.target.value)}
                  style={{ width: '40px', height: '40px', cursor: 'pointer' }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Épaisseur du trait:</Typography>
                <Slider
                  value={strokeWidth}
                  min={1}
                  max={20}
                  step={1}
                  onChange={(_, value) => setStrokeWidth(value as number)}
                  valueLabelDisplay="auto"
                />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="body2" sx={{ mr: 2, minWidth: 100 }}>Couleur de remplissage:</Typography>
                <input
                  type="color"
                  value={fillColor === 'transparent' ? '#ffffff' : fillColor}
                  onChange={(e) => setFillColor(e.target.value)}
                  style={{ width: '40px', height: '40px', cursor: 'pointer' }}
                />
                <Button 
                  size="small" 
                  onClick={() => setFillColor(fillColor === 'transparent' ? '#ffffff' : 'transparent')}
                  sx={{ ml: 1 }}
                >
                  {fillColor === 'transparent' ? 'Activer' : 'Transparent'}
                </Button>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Opacité:</Typography>
                <Slider
                  value={opacity}
                  min={0.1}
                  max={1}
                  step={0.1}
                  onChange={(_, value) => setOpacity(value as number)}
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  onClick={generateNewPath}
                  sx={{ mr: 1 }}
                >
                  Régénérer
                </Button>
                <Tooltip title="Ajouter au canvas">
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<Add />}
                    onClick={handleAddPath}
                  >
                    Ajouter
                  </Button>
                </Tooltip>
              </Box>
            </Box>

            <Box>
              <Typography variant="subtitle2" gutterBottom>Données SVG personnalisées:</Typography>
              <TextField
                fullWidth
                multiline
                rows={4}
                value={customPathData}
                onChange={(e) => setCustomPathData(e.target.value)}
                placeholder="M10 80 C 40 10, 65 10, 95 80"
                variant="outlined"
                size="small"
                onFocus={() => setUseCustomPath(true)}
                onBlur={() => {
                  if (customPathData.trim() === '') {
                    setUseCustomPath(false);
                    generateNewPath();
                  }
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                Utilisez la syntaxe SVG standard: M (moveTo), L (lineTo), C (curveTo), Z (closePath), etc.
              </Typography>
            </Box>
          </Grid>

          {/* Aperçu */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>Aperçu:</Typography>
            <Paper 
              elevation={2} 
              sx={{ 
                p: 2, 
                backgroundColor: '#f5f5f5',
                height: 300,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <Stage width={previewWidth} height={previewHeight}>
                <Layer>
                  <Path
                    data={pathData}
                    stroke={strokeColor}
                    strokeWidth={strokeWidth}
                    fill={fillColor}
                    opacity={opacity}
                    x={0}
                    y={0}
                  />
                </Layer>
              </Stage>
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Annuler</Button>
        <Button onClick={handleAddPath} variant="contained" color="primary">
          Ajouter au canvas
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PathGeneratorDialog;
