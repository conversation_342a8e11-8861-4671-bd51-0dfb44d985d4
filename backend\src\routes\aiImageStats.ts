import { Router } from 'express';
import { getAiImageStats } from '../controllers/aiImageStatsController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';

const router = Router();

// Rate limiter pour les requêtes de statistiques
const aiImageStatsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes de statistiques. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Middleware de vérification du rôle pour toutes les routes
router.use(authMiddleware.checkRole(['jobpadm', 'jobmodo']));

// Route pour récupérer les statistiques
router.get('/', aiImageStatsLimiter, asyncHandler(getAiImageStats));

export default router;
