import { Router } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { PaymentsController } from '../controllers/payments';
import rateLimit from 'express-rate-limit';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import { subscriptions } from '../config/ConfigSubscriptions';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';

const router = Router();
const paymentsController = new PaymentsController();

// Configuration du rate limiter pour les paiements
const paymentsLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes par IP
  message: { 
    success: false,
    message: 'Trop de requêtes pour les transactions, veuillez réessayer dans 30 secondes',
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter
router.use(paymentsLimiter);

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Fonction utilitaire pour vérifier la limite de transactions
async function checkTransactionLimit(userId: string, additionalTransactions: number = 1) {
  // Utiliser getUserSubscriptionLimits pour récupérer les informations d'abonnement
  const { isPremium, options } = await getUserSubscriptionLimits(userId);
  
  // Si l'utilisateur a des options personnalisées pour les transactions, les utiliser
  // Sinon utiliser les limites standard selon le plan
  const transactionLimit = options.transactions 
    ? options.transactions 
    : (isPremium ? subscriptions.premium.transactions.included : subscriptions.gratuit.transactions.included);

  // Compter le nombre de transactions de l'utilisateur
  const { count, error: countError } = await supabase
    .from('user_transac')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId);

  if (countError) {
    throw countError;
  }

  const currentCount = count || 0;
  const canCreate = (currentCount + additionalTransactions) <= transactionLimit;
  const plan = isPremium ? 'premium' : 'gratuit';

  return {
    plan,
    transactionLimit,
    currentCount,
    canCreate,
    remainingSlots: Math.max(0, transactionLimit - currentCount)
  };
}

// Récupérer toutes les transactions
router.get('/', async (req, res) => {
  try {
    await paymentsController.getTransactions(req, res);
  } catch (error) {
    logger.error('Erreur lors de la récupération des transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des transactions'
    });
  }
});

// Récupérer les revenus et statistiques
router.get('/revenus', async (req, res) => {
  try {
    await paymentsController.getRevenus(req, res);
  } catch (error) {
    logger.error('Erreur lors de la récupération des revenus:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des revenus'
    });
  }
});

// Récupérer les transactions à importer
router.get('/import', async (req, res) => {
  try {
    await paymentsController.getTransactionsToImport(req, res);
  } catch (error) {
    logger.error('Erreur lors de la récupération des transactions à importer:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des transactions à importer'
    });
  }
});

// Importer des transactions
router.post('/import', async (req, res) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
      return;
    }

    const transactionsToImport = req.body.transactions || [];
    if (!Array.isArray(transactionsToImport)) {
      res.status(400).json({ success: false, message: 'Format de données invalide' });
      return;
    }

    // Vérifier la limite avant l'importation
    const { canCreate, remainingSlots, plan, transactionLimit } = await checkTransactionLimit(userId, transactionsToImport.length);

    if (!canCreate) {
      res.status(400).json({
        success: false,
        message: `Impossible d'importer ${transactionsToImport.length} transactions. Votre plan ${plan} permet un maximum de ${transactionLimit} transactions. Il vous reste ${remainingSlots} emplacements disponibles.`
      });
      return;
    }

    // Si la vérification est OK, procéder à l'importation
    await paymentsController.importTransactions(req, res);
  } catch (error) {
    logger.error('Erreur lors de l\'importation des transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'importation des transactions'
    });
  }
});

// CRUD Transactions
router.post('/', async (req, res) => {
  try {
    await paymentsController.createTransaction(req, res);
  } catch (error) {
    logger.error('Erreur lors de la création de la transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la transaction'
    });
  }
});

router.put('/:id', async (req, res) => {
  try {
    await paymentsController.updateTransaction(req, res);
  } catch (error) {
    logger.error('Erreur lors de la mise à jour de la transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la transaction'
    });
  }
});

router.delete('/:id', async (req, res) => {
  try {
    await paymentsController.deleteTransaction(req, res);
  } catch (error) {
    logger.error('Erreur lors de la suppression de la transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la transaction'
    });
  }
});

// Route pour supprimer des transactions en lot
router.post('/delete-batch', async (req, res) => {
  try {
    await paymentsController.batchDeleteTransactions(req, res);
  } catch (error) {
    logger.error('Erreur lors de la suppression en masse des transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression en masse des transactions'
    });
  }
});

// Route pour exporter les transactions en PDF
router.get('/export-pdf', async (req, res) => {
  try {
    await paymentsController.exportTransactionsPDF(req, res);
  } catch (error) {
    logger.error('Erreur lors de l\'export des transactions en PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'export des transactions en PDF'
    });
  }
});

// Vérifier le nombre de transactions selon le plan d'abonnement
router.get('/check-transactions-limit', authMiddleware.authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({ success: false, message: 'Utilisateur non authentifié' });
      return;
    }

    const result = await checkTransactionLimit(userId);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erreur lors de la vérification de la limite des transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification de la limite des transactions'
    });
  }
});

export default router; 