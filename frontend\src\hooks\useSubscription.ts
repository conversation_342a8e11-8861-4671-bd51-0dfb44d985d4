import { useEffect, useState, useCallback } from 'react';
import subscriptionService from '@/services/subscriptionService';

export interface SubscriptionStatus {
  plan: 'premium' | 'gratuit';
  isPremium: boolean;
  options: Record<string, any>;
  [key: string]: any;
}

export interface SubscriptionConfig {
  premium: Record<string, any>;
  gratuit: Record<string, any>;
}

export function useSubscription() {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [config, setConfig] = useState<SubscriptionConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAll = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const [statusRes, configRes] = await Promise.all([
          subscriptionService.getSubscriptionStatus(),
          subscriptionService.getSubscriptionConfig()
        ]);
        setStatus({
          ...statusRes,
          isPremium: statusRes.plan === 'premium',
        });
        setConfig(configRes.data);
      } catch (err: any) {
        setError(err.message || 'Erreur lors du chargement de l\'abonnement');
      } finally {
        setIsLoading(false);
      }
    };
    fetchAll();
  }, []);

  // Fonction utilitaire pour récupérer la bonne valeur d'option
  const getOptionPremiumUtilisateur = useCallback((key: string) => {
    if (!status || !config) return undefined;
    if (status.isPremium) {
      if (status.options && status.options[key] !== undefined && status.options[key] !== null) {
        return status.options[key];
      }
      return config.premium?.[key]?.included;
    } else {
      return config.gratuit?.[key]?.included;
    }
  }, [status, config]);

  return {
    isLoading,
    error,
    status,
    config,
    getOptionPremiumUtilisateur,
  };
} 