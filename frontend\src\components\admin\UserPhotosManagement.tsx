import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  IconButton,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  Stack,
  Tooltip,
  Backdrop,
  CircularProgress,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Report as ReportIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Close as CloseIcon,
  PhotoLibrary as PhotoLibraryIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Info as InfoIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { useUserManagement } from '../../hooks/useUserManagement';

interface UserPhotosManagementProps {
  userId: string;
  galleries: any[];
  featuredPhotos: any[];
  onUpdate: () => void;
}

interface GalleryPhoto {
  id: string;
  photo_url: string;
  caption?: string;
  order_index: number;
  created_at: string;
}

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const StyledImageList = styled(ImageList)(({ theme }) => ({
  gap: 16,
  '& .MuiImageListItem-root': {
    borderRadius: '12px',
    overflow: 'hidden',
    boxShadow: '0 4px 10px rgba(0,0,0,0.08)',
    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 8px 20px rgba(0,0,0,0.12)',
    },
  },
}));

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '16px',
  overflow: 'hidden',
  boxShadow: '0 4px 10px rgba(0,0,0,0.08)',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 20px rgba(0,0,0,0.12)',
  },
}));

const StyledCardMedia = styled(CardMedia)(({ theme }) => ({
  height: 200,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: 'none',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
}));

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 1)',
  },
}));

const UserPhotosManagement: React.FC<UserPhotosManagementProps> = ({
  userId,
  galleries,
  featuredPhotos,
  onUpdate
}) => {
  const { manageUserPhotos } = useUserManagement();
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [galleryViewerOpen, setGalleryViewerOpen] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
  const [selectedGallery, setSelectedGallery] = useState<any>(null);
  const [selectedGalleryPhotos, setSelectedGalleryPhotos] = useState<GalleryPhoto[]>([]);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [action, setAction] = useState('');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handlePhotoAction = async (photo: any, actionType: string) => {
    setSelectedPhoto(photo);
    setAction(actionType);
    setActionDialogOpen(true);
  };

  const handleGalleryAction = async (gallery: any, actionType: string) => {
    setSelectedGallery(gallery);
    setAction(actionType);
    setActionDialogOpen(true);
  };

  const handleViewGallery = (gallery: any) => {
    setSelectedGallery(gallery);
    // Récupérer les photos de la galerie
    const photos = gallery.user_gallery_photos || [];
    setSelectedGalleryPhotos(photos);
    setCurrentPhotoIndex(0);
    setGalleryViewerOpen(true);
  };

  const executeAction = async () => {
    if (!action) return;

    try {
      setLoading(true);
      setError(null);

      const requestData: any = {
        action,
        reason
      };

      if (selectedPhoto) {
        requestData.photoId = selectedPhoto.id;
      }

      if (selectedGallery) {
        requestData.galleryId = selectedGallery.id;
      }

      const result = await manageUserPhotos(userId, requestData);

      if (result.success) {
        setActionDialogOpen(false);
        setSelectedPhoto(null);
        setSelectedGallery(null);
        setAction('');
        setReason('');
        onUpdate();
      } else {
        setError(result.message || 'Erreur lors de l\'action');
      }
    } catch (error) {
      console.error('Erreur lors de l\'action photo:', error);
      setError('Erreur lors de l\'action');
    } finally {
      setLoading(false);
    }
  };

  const getActionLabel = () => {
    switch (action) {
      case 'delete_photo':
        return 'Supprimer la photo';
      case 'delete_featured_photo':
        return 'Supprimer la photo mise en avant';
      case 'delete_gallery':
        return 'Supprimer la galerie';
      case 'moderate_photo':
        return 'Modérer la photo';
      default:
        return 'Action';
    }
  };

  const getActionDescription = () => {
    switch (action) {
      case 'delete_photo':
        return 'Cette action supprimera définitivement la photo sélectionnée. L\'utilisateur sera notifié de cette suppression.';
      case 'delete_featured_photo':
        return 'Cette action supprimera définitivement la photo mise en avant. L\'utilisateur sera notifié de cette suppression.';
      case 'delete_gallery':
        return 'Cette action supprimera définitivement la galerie et toutes ses photos. L\'utilisateur sera notifié de cette suppression.';
      case 'moderate_photo':
        return 'Cette action marquera la photo comme nécessitant une modération. L\'utilisateur sera notifié.';
      default:
        return '';
    }
  };

  const navigatePhoto = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && currentPhotoIndex > 0) {
      setCurrentPhotoIndex(currentPhotoIndex - 1);
    } else if (direction === 'next' && currentPhotoIndex < selectedGalleryPhotos.length - 1) {
      setCurrentPhotoIndex(currentPhotoIndex + 1);
    }
  };

  return (
    <Box sx={{ pb: 4 }}>
      {/* En-tête avec statistiques */}
      <StyledPaper sx={{ mb: 4, p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: 2 }}>
            <Typography variant="h5" fontWeight="700" sx={{ color: '#2D3748', position: 'relative', '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-8px',
              left: 0,
              width: '60px',
              height: '3px',
              background: COLORS.primary,
              borderRadius: '2px',
            }}}>
              Gestion des Photos et Galeries
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Chip 
                icon={<ImageIcon />} 
                label={`${featuredPhotos.length} photos mises en avant`} 
                sx={{ 
                  bgcolor: COLORS.background, 
                  color: COLORS.primary, 
                  fontWeight: 600,
                  border: `1px solid ${COLORS.borderColor}`,
                  '& .MuiChip-icon': { color: COLORS.primary }
                }}
              />
              <Chip 
                icon={<PhotoLibraryIcon />} 
                label={`${galleries.length} galeries`} 
                sx={{ 
                  bgcolor: COLORS.background, 
                  color: COLORS.primary, 
                  fontWeight: 600,
                  border: `1px solid ${COLORS.borderColor}`,
                  '& .MuiChip-icon': { color: COLORS.primary }
                }}
              />
            </Box>
          </Box>
          
          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                borderRadius: '8px', 
                '& .MuiAlert-icon': { color: COLORS.error } 
              }}
            >
              {error}
            </Alert>
          )}
        </Box>
      </StyledPaper>

      {/* Photos mises en avant */}
      <StyledPaper sx={{ mb: 4 }}>
        <SectionTitle>
          <CheckCircleIcon sx={{ color: COLORS.primary }} />
          Photos mises en avant
        </SectionTitle>
        
        {featuredPhotos.length > 0 ? (
          <StyledImageList cols={isMobile ? 1 : (featuredPhotos.length === 1 ? 1 : (featuredPhotos.length === 2 ? 2 : 3))} gap={16}>
            {featuredPhotos.map((photo) => (
              <ImageListItem key={photo.id} sx={{ overflow: 'hidden' }}>
                <img
                  src={photo.photo_url}
                  alt={photo.caption || 'Photo mise en avant'}
                  loading="lazy"
                  style={{ height: 240, objectFit: 'cover', width: '100%' }}
                />
                <ImageListItemBar
                  title={photo.caption || 'Sans titre'}
                  subtitle={new Date(photo.created_at).toLocaleDateString('fr-FR')}
                  sx={{
                    '& .MuiImageListItemBar-title': { fontWeight: 600, fontSize: '1rem' },
                    '& .MuiImageListItemBar-subtitle': { opacity: 0.8 }
                  }}
                  actionIcon={
                    <Box sx={{ display: 'flex', gap: 0.5, pr: 1 }}>
                      <Tooltip title="Supprimer la photo">
                        <StyledIconButton
                          size="small"
                          onClick={() => handlePhotoAction(photo, 'delete_featured_photo')}
                          sx={{ color: COLORS.error }}
                        >
                          <DeleteIcon />
                        </StyledIconButton>
                      </Tooltip>
                      <Tooltip title="Modérer la photo">
                        <StyledIconButton
                          size="small"
                          onClick={() => handlePhotoAction(photo, 'moderate_photo')}
                          sx={{ color: COLORS.warning }}
                        >
                          <ReportIcon />
                        </StyledIconButton>
                      </Tooltip>
                    </Box>
                  }
                />
              </ImageListItem>
            ))}
          </StyledImageList>
        ) : (
          <Alert 
            severity="info" 
            sx={{ 
              borderRadius: '8px', 
              bgcolor: `${COLORS.info}10`, 
              color: COLORS.info,
              '& .MuiAlert-icon': { color: COLORS.info } 
            }}
          >
            Aucune photo mise en avant
          </Alert>
        )}
      </StyledPaper>

      {/* Galeries */}
      <StyledPaper>
        <SectionTitle>
          <VisibilityIcon sx={{ color: COLORS.primary }} />
          Galeries
        </SectionTitle>

        {galleries.length > 0 ? (
          <Grid container spacing={3}>
            {galleries.map((gallery) => (
              <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={gallery.id}>
                <StyledCard>
                  {gallery.cover_image ? (
                    <StyledCardMedia
                      image={gallery.cover_image}
                      title={gallery.name}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        height: 200, 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center',
                        bgcolor: COLORS.lightGray 
                      }}
                    >
                      <PhotoLibraryIcon sx={{ fontSize: 60, color: COLORS.neutral }} />
                    </Box>
                  )}
                  <CardContent sx={{ flexGrow: 1, p: '14px!important' }}>
                    <Typography variant="h6" gutterBottom fontWeight="600">
                      {gallery.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 2 }}>
                      {gallery.description || 'Aucune description'}
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
                      <Chip
                        label={gallery.status}
                        color={gallery.status === 'actif' ? 'success' : 'default'}
                        size="small"
                        sx={{ fontWeight: 500 }}
                      />
                      <Chip
                        label={`${gallery.user_gallery_photos?.length || 0} photos`}
                        size="small"
                        variant="outlined"
                        sx={{ fontWeight: 500 }}
                      />
                    </Stack>
                    <Typography variant="caption" color="text.secondary">
                      Créée le {new Date(gallery.created_at).toLocaleDateString('fr-FR')}
                    </Typography>
                  </CardContent>
                  <CardActions sx={{ p: 2, pt: 0 }}>
                    <StyledButton
                      size="small"
                      startIcon={<VisibilityIcon />}
                      onClick={() => handleViewGallery(gallery)}
                      disabled={!gallery.user_gallery_photos || gallery.user_gallery_photos.length === 0}
                      variant="outlined"
                      sx={{ borderColor: COLORS.primary, color: COLORS.primary }}
                    >
                      Voir ({gallery.user_gallery_photos?.length || 0})
                    </StyledButton>
                    <StyledButton
                      size="small"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={() => handleGalleryAction(gallery, 'delete_gallery')}
                      variant="outlined"
                    >
                      Supprimer
                    </StyledButton>
                  </CardActions>
                </StyledCard>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Alert 
            severity="info" 
            sx={{ 
              borderRadius: '8px', 
              bgcolor: `${COLORS.info}10`, 
              color: COLORS.info,
              '& .MuiAlert-icon': { color: COLORS.info } 
            }}
          >
            Aucune galerie créée
          </Alert>
        )}
      </StyledPaper>

      {/* Dialog de confirmation d'action */}
      <Dialog 
        open={actionDialogOpen} 
        onClose={() => setActionDialogOpen(false)} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            bgcolor: COLORS.error,
            color: 'white',
            p: 2
          }}
        >
          <WarningIcon />
          {getActionLabel()}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <Alert 
            severity="warning" 
            sx={{ 
              mb: 2, 
              borderRadius: '8px',
              '& .MuiAlert-icon': { color: COLORS.warning }
            }}
          >
            {getActionDescription()}
          </Alert>
          
          <Box sx={{ mb: 2 }}>
            {selectedPhoto && (
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Photo sélectionnée :</strong> {selectedPhoto.caption || 'Sans titre'}
              </Typography>
            )}
            {selectedGallery && (
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Galerie sélectionnée :</strong> {selectedGallery.name}
                {selectedGallery.user_gallery_photos && (
                  <span> ({selectedGallery.user_gallery_photos.length} photos)</span>
                )}
              </Typography>
            )}
          </Box>
          
          <TextField
            fullWidth
            label="Motif de l'action"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            placeholder="Expliquez la raison de cette action (obligatoire pour informer l'utilisateur)..."
            required
            helperText="Ce motif sera envoyé à l'utilisateur dans la notification"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                '&.Mui-focused fieldset': {
                  borderColor: COLORS.primary,
                },
              },
              '& .MuiFormLabel-root.Mui-focused': {
                color: COLORS.primary,
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => setActionDialogOpen(false)} 
            disabled={loading}
            sx={{ 
              textTransform: 'none', 
              fontWeight: 600,
              borderRadius: '8px'
            }}
          >
            Annuler
          </Button>
          <StyledButton
            onClick={executeAction}
            color="error"
            disabled={loading || !reason.trim()}
            variant="contained"
            startIcon={loading ? <CircularProgress size={16} /> : <WarningIcon />}
            sx={{ 
              bgcolor: COLORS.error,
              '&:hover': {
                bgcolor: '#d32f2f',
              },
            }}
          >
            {loading ? 'En cours...' : 'Confirmer l\'action'}
          </StyledButton>
        </DialogActions>
      </Dialog>

      {/* Modal de visualisation de galerie */}
      <Dialog
        open={galleryViewerOpen}
        onClose={() => setGalleryViewerOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { 
            height: '90vh',
            borderRadius: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          bgcolor: COLORS.lightGray,
          borderBottom: `1px solid ${COLORS.borderColor}`
        }}>
          <Box>
            <Typography variant="h6" fontWeight="600">
              {selectedGallery?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {selectedGalleryPhotos.length} photo(s) - Photo {currentPhotoIndex + 1} sur {selectedGalleryPhotos.length}
            </Typography>
          </Box>
          <IconButton 
            onClick={() => setGalleryViewerOpen(false)}
            sx={{ 
              bgcolor: 'rgba(0,0,0,0.05)',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' }
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', p: 0 }}>
          {selectedGalleryPhotos.length > 0 && (
            <>
              {/* Image principale */}
              <Box sx={{ 
                position: 'relative', 
                flexGrow: 1, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                bgcolor: 'black',
                minHeight: '400px'
              }}>
                <img
                  src={selectedGalleryPhotos[currentPhotoIndex]?.photo_url}
                  alt={selectedGalleryPhotos[currentPhotoIndex]?.caption || 'Photo de galerie'}
                  style={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain'
                  }}
                />
                
                {/* Navigation */}
                {selectedGalleryPhotos.length > 1 && (
                  <>
                    <IconButton
                      sx={{
                        position: 'absolute',
                        left: 16,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        bgcolor: 'rgba(255,255,255,0.8)',
                        color: COLORS.primary,
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' },
                        '&.Mui-disabled': { bgcolor: 'rgba(255,255,255,0.4)', color: 'rgba(0,0,0,0.26)' }
                      }}
                      onClick={() => navigatePhoto('prev')}
                      disabled={currentPhotoIndex === 0}
                    >
                      <ArrowBackIcon />
                    </IconButton>
                    <IconButton
                      sx={{
                        position: 'absolute',
                        right: 16,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        bgcolor: 'rgba(255,255,255,0.8)',
                        color: COLORS.primary,
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' },
                        '&.Mui-disabled': { bgcolor: 'rgba(255,255,255,0.4)', color: 'rgba(0,0,0,0.26)' }
                      }}
                      onClick={() => navigatePhoto('next')}
                      disabled={currentPhotoIndex === selectedGalleryPhotos.length - 1}
                    >
                      <ArrowForwardIcon />
                    </IconButton>
                  </>
                )}
              </Box>

              {/* Informations de la photo */}
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderTop: `1px solid ${COLORS.borderColor}` }}>
                <Typography variant="h6" gutterBottom fontWeight="600">
                  {selectedGalleryPhotos[currentPhotoIndex]?.caption || 'Sans titre'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Ajoutée le {new Date(selectedGalleryPhotos[currentPhotoIndex]?.created_at).toLocaleDateString('fr-FR')}
                </Typography>
              </Box>

              {/* Miniatures */}
              {selectedGalleryPhotos.length > 1 && (
                <Box sx={{ p: 2, bgcolor: COLORS.lightGray, borderTop: `1px solid ${COLORS.borderColor}` }}>
                  <ImageList cols={Math.min(selectedGalleryPhotos.length, isMobile ? 4 : 8)} gap={8} sx={{ height: 80 }}>
                    {selectedGalleryPhotos.map((photo, index) => (
                      <ImageListItem 
                        key={photo.id}
                        sx={{ 
                          cursor: 'pointer',
                          border: index === currentPhotoIndex ? `2px solid ${COLORS.primary}` : '2px solid transparent',
                          borderRadius: '8px',
                          overflow: 'hidden'
                        }}
                        onClick={() => setCurrentPhotoIndex(index)}
                      >
                        <img
                          src={photo.photo_url}
                          alt={photo.caption || 'Miniature'}
                          style={{ height: 60, objectFit: 'cover' }}
                        />
                      </ImageListItem>
                    ))}
                  </ImageList>
                </Box>
              )}
            </>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2, bgcolor: COLORS.lightGray, borderTop: `1px solid ${COLORS.borderColor}` }}>
          <StyledButton
            color="error"
            startIcon={<DeleteIcon />}
            onClick={() => {
              setGalleryViewerOpen(false);
              handlePhotoAction(selectedGalleryPhotos[currentPhotoIndex], 'delete_photo');
            }}
            variant="outlined"
          >
            Supprimer cette photo
          </StyledButton>
          <StyledButton
            color="warning"
            startIcon={<ReportIcon />}
            onClick={() => {
              setGalleryViewerOpen(false);
              handlePhotoAction(selectedGalleryPhotos[currentPhotoIndex], 'moderate_photo');
            }}
            variant="outlined"
            sx={{ borderColor: COLORS.warning, color: COLORS.warning }}
          >
            Modérer cette photo
          </StyledButton>
          <StyledButton 
            onClick={() => setGalleryViewerOpen(false)}
            variant="contained"
          >
            Fermer
          </StyledButton>
        </DialogActions>
      </Dialog>

      {/* Backdrop de chargement */}
      <Backdrop open={loading} sx={{ zIndex: (theme) => theme.zIndex.modal + 1 }}>
        <CircularProgress sx={{ color: COLORS.primary }} />
      </Backdrop>
    </Box>
  );
};

export default UserPhotosManagement;
