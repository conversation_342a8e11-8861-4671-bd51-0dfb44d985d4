import React from 'react';
import { Box, Typography, Button, Paper, useTheme, useMediaQuery } from '@mui/material';
import { Link, useParams } from 'react-router-dom';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import BugReportIcon from '@mui/icons-material/BugReport';
import BugReportDetail from '../components/BugReport/BugReportDetail';

const BugReportDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box sx={{ p: { xs: 1.5, sm: 3 } }}>
      {/* Titre et boutons d'action */}
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between', 
          alignItems: { xs: 'flex-start', md: 'center' }, 
          gap: { xs: 2, md: 0 },
          mb: 3
        }}
      >
        <Box sx={{ display: 'flex', alignItems: { xs: 'flex-start', sm: 'center' }, flexDirection: { xs: 'column', sm: 'row' } }}>
          <BugReportIcon 
            sx={{ 
              color: '#FF6B2C', 
              fontSize: { xs: '1.8rem', sm: '2.2rem' }, 
              mr: { xs: 0, sm: 2 },
              mb: { xs: 1, sm: 0 },
              filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
            }} 
          />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              fontWeight: 700, 
              color: '#333',
              fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: { xs: '40px', sm: '60px' },
                height: '3px',
                background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
                borderRadius: '2px'
              }
            }}
          >
            {isMobile ? `Rapport #${id?.substring(0, 8)}...` : `Détails du rapport #${id}`}
          </Typography>
        </Box>

        <Button
          component={Link}
          to="/dashboard/bug-reports"
          variant="outlined"
          fullWidth={isMobile}
          startIcon={<FormatListBulletedIcon />}
          sx={{ 
            color: '#FF6B2C',
            borderColor: '#FF6B2C',
            fontWeight: 500,
            px: 2,
            py: 1,
            borderRadius: '8px',
            '&:hover': {
              borderColor: '#FF6B2C',
              color: '#FF6B2C',
              bgcolor: 'rgba(255, 107, 44, 0.05)',
            },
          }}
        >
          {isMobile ? 'Retour à la liste' : 'Liste des rapports'}
        </Button>
      </Box>

      {/* Détails du rapport */}
      <Paper
        elevation={3}
        sx={{
          borderRadius: { xs: '8px', sm: '12px' },
          overflow: 'hidden',
          boxShadow: '0 5px 20px rgba(0, 0, 0, 0.05)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
          }
        }}
      >
        <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
          <BugReportDetail />
        </Box>
      </Paper>
    </Box>
  );
};

export default BugReportDetailPage; 