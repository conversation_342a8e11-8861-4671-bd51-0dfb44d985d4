import { redis } from '../config/redis';
import logger from '../utils/logger';

const REVIEW_CACHE_PREFIX = 'review:';
const REVIEW_LIST_CACHE_PREFIX = 'review_list:';
const REVIEW_STATS_CACHE_PREFIX = 'review_stats:';
const CACHE_TTL = 3600; // 1 heure en secondes

export const reviewCacheService = {
  // Mise en cache d'un avis
  async cacheReview(reviewId: string, reviewData: any) {
    try {
      const key = `${REVIEW_CACHE_PREFIX}${reviewId}`;
      await redis.setex(key, CACHE_TTL, JSON.stringify(reviewData));
      logger.info(`Review cached: ${key}`);
    } catch (error) {
      logger.error('Error caching review:', error);
    }
  },

  // Récupération d'un avis du cache
  async getCachedReview(reviewId: string) {
    try {
      const key = `${REVIEW_CACHE_PREFIX}${reviewId}`;
      const cachedReview = await redis.get(key);
      return cachedReview ? JSON.parse(cachedReview) : null;
    } catch (error) {
      logger.error('Error getting cached review:', error);
      return null;
    }
  },

  // Mise en cache de la liste des avis d'un utilisateur
  async cacheUserReviews(userId: string, page: number, reviews: any) {
    try {
      const key = `${REVIEW_LIST_CACHE_PREFIX}${userId}:${page}`;
      await redis.setex(key, CACHE_TTL, JSON.stringify(reviews));
      logger.info(`User reviews cached: ${key}`);
    } catch (error) {
      logger.error('Error caching user reviews:', error);
    }
  },

  // Récupération de la liste des avis d'un utilisateur du cache
  async getCachedUserReviews(userId: string, page: number) {
    try {
      const key = `${REVIEW_LIST_CACHE_PREFIX}${userId}:${page}`;
      const cachedReviews = await redis.get(key);
      return cachedReviews ? JSON.parse(cachedReviews) : null;
    } catch (error) {
      logger.error('Error getting cached user reviews:', error);
      return null;
    }
  },

  // Mise en cache des statistiques des avis d'un utilisateur
  async cacheUserReviewStats(userId: string, stats: any) {
    try {
      const key = `${REVIEW_STATS_CACHE_PREFIX}${userId}`;
      await redis.setex(key, CACHE_TTL, JSON.stringify(stats));
      logger.info(`User review stats cached: ${key}`);
    } catch (error) {
      logger.error('Error caching user review stats:', error);
    }
  },

  // Récupération des statistiques des avis d'un utilisateur du cache
  async getCachedUserReviewStats(userId: string) {
    try {
      const key = `${REVIEW_STATS_CACHE_PREFIX}${userId}`;
      const cachedStats = await redis.get(key);
      return cachedStats ? JSON.parse(cachedStats) : null;
    } catch (error) {
      logger.error('Error getting cached user review stats:', error);
      return null;
    }
  },

  // Invalidation du cache pour un avis spécifique
  async invalidateReviewCache(reviewId: string) {
    try {
      const key = `${REVIEW_CACHE_PREFIX}${reviewId}`;
      await redis.del(key);
      logger.info(`Review cache invalidated: ${key}`);
    } catch (error) {
      logger.error('Error invalidating review cache:', error);
    }
  },

  // Invalidation du cache pour les avis d'un utilisateur
  async invalidateUserReviewsCache(userId: string) {
    try {
      const pattern = `${REVIEW_LIST_CACHE_PREFIX}${userId}:*`;
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
        logger.info(`User reviews cache invalidated for patterns: ${pattern}`);
      }

      // Invalider aussi les stats
      const statsKey = `${REVIEW_STATS_CACHE_PREFIX}${userId}`;
      await redis.del(statsKey);
      logger.info(`User review stats cache invalidated: ${statsKey}`);
    } catch (error) {
      logger.error('Error invalidating user reviews cache:', error);
    }
  }
}; 