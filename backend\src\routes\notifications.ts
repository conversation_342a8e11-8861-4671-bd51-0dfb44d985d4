import { Router } from 'express';
import { NotificationController } from '../controllers/notifications';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import { asyncHandler } from '../utils/inputValidation';
import { Request, Response } from 'express';

const router = Router();
const notificationController = new NotificationController();

// Rate limiter pour les notifications
const notificationLimiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 1 * 30 * 1000 : 1 * 30 * 1000, // 30 secondes en dev, 30 secondes également en prod
  max: process.env.NODE_ENV === 'development' ? 2000 : 200, // 2000 requêtes maximum par fenêtre en dev, 200 en prod
  message: {
    message: 'Trop de requêtes de notifications. Veuillez réessayer dans 30 secondes.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Routes pour les notifications
router.get('/',
  notificationLimiter,
  notificationController.getNotifications
);

// Route pour créer une notification
router.post('/',
  notificationLimiter,
  notificationController.createNotification
);

// Route pour créer plusieurs notifications
router.post('/batch',
  notificationLimiter,
  notificationController.createMultipleNotifications
);

router.get('/unread-count',
  notificationLimiter,
  asyncHandler((req: Request, res: Response) => notificationController.getUnreadCount(req, res))
);

// Route pour obtenir le nombre combiné de notifications et messages non lus
router.get('/combined-unread-count',
  notificationLimiter,
  asyncHandler((req: Request, res: Response) => notificationController.getCombinedUnreadCounts(req, res))
);

router.put('/:notificationId/toggle-read',
  notificationLimiter,
  notificationController.markAsRead
);

router.put('/:notificationId/archive',
  notificationLimiter,
  notificationController.archiveNotification
);

// Route pour supprimer toutes les notifications
router.delete('/delete-all',
  notificationLimiter,
  notificationController.deleteAllNotifications
);

router.delete('/:notificationId',
  notificationLimiter,
  notificationController.deleteNotification
);

// Route pour marquer toutes les notifications comme lues
router.put('/mark-all-read',
  notificationLimiter,
  notificationController.markAllAsRead
);

// Route pour archiver toutes les notifications
router.put('/archive-all',
  notificationLimiter,
  notificationController.archiveAllNotifications
);

export const notificationRoutes = router;
