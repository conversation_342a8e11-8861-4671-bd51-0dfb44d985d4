import React, { useState, useEffect } from 'react';
import NotificationPopup from './components/NotificationPopup';

const mockCampaigns = [
  {
    id: 1,
    name: "Promotion de Printemps",
    status: "active",
    reach: 1200,
    conversions: 45,
    budget: 150,
    startDate: "2023-12-01",
    endDate: "2023-12-31"
  },
  {
    id: 2,
    name: "Offre Week-end",
    status: "scheduled",
    reach: 0,
    conversions: 0,
    budget: 100,
    startDate: "2023-12-15",
    endDate: "2023-12-17"
  }
];

const mockPromotions = [
  {
    id: 1,
    code: "BIENVENUE20",
    discount: 20,
    type: "percentage",
    usageLimit: 100,
    usageCount: 45,
    expiryDate: "2023-12-31"
  },
  {
    id: 2,
    code: "NOEL2023",
    discount: 15,
    type: "percentage",
    usageLimit: 50,
    usageCount: 12,
    expiryDate: "2023-12-25"
  }
];

const MarketingPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('campaigns');


  return (
    <div>
      <div className="space-y-6 px-2 md:px-0">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-800">Outils Marketing</h1>
          <button className="bg-[#FF7A35] text-white px-4 py-2 rounded-md hover:bg-[#ff6b2c] transition-colors">
            Nouvelle Campagne
          </button>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Portée totale</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">2,450</p>
            <p className="text-sm text-green-600">+15% ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Conversions</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">128</p>
            <p className="text-sm text-green-600">+8% ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">ROI moyen</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">245%</p>
            <p className="text-sm text-green-600">+5% ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Budget utilisé</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">450€</p>
            <p className="text-sm text-gray-500">sur 1000€</p>
          </div>
        </div>

        {/* Onglets */}
        <div className="flex space-x-4 border-b">
          <button
            onClick={() => setActiveTab('campaigns')}
            className={`pb-2 px-4 ${
              activeTab === 'campaigns'
                ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Campagnes Marketing
          </button>
          <button
            onClick={() => setActiveTab('promotions')}
            className={`pb-2 px-4 ${
              activeTab === 'promotions'
                ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Codes Promo
          </button>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'campaigns' ? (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Campagne</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Portée</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Conversions</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Budget</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Période</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockCampaigns.map((campaign) => (
                  <tr key={campaign.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{campaign.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        campaign.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {campaign.status === 'active' ? 'Active' : 'Planifiée'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {campaign.reach.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {campaign.conversions}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {campaign.budget}€
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {campaign.startDate} - {campaign.endDate}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Code</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Réduction</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Expiration</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockPromotions.map((promo) => (
                  <tr key={promo.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{promo.code}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {promo.discount}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {promo.usageCount} / {promo.usageLimit}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {promo.expiryDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button className="text-[#FF7A35] hover:text-[#ff6b2c]">
                        Modifier
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Conseils marketing */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Conseils pour optimiser vos campagnes</h2>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-4 bg-orange-50 rounded-lg">
              <h3 className="font-medium text-[#FF7A35] mb-2">Timing optimal</h3>
              <p className="text-sm text-gray-600">
                Lancez vos promotions pendant les heures de pointe (10h-12h et 14h-16h) pour maximiser leur visibilité.
              </p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h3 className="font-medium text-[#FF7A35] mb-2">Ciblage efficace</h3>
              <p className="text-sm text-gray-600">
                Concentrez-vous sur les zones géographiques où vous avez le plus de succès.
              </p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h3 className="font-medium text-[#FF7A35] mb-2">Message percutant</h3>
              <p className="text-sm text-gray-600">
                Mettez en avant vos meilleures évaluations et témoignages clients.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketingPage;