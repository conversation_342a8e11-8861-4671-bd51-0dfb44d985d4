import { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { notify } from '../components/Notification';
import logger from '../utils/logger';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { useAuth } from '../contexts/AuthContext';
import { updateJobiBalance as updateJobiBalanceUtil, JobiTransaction } from '../utils/jobiUtils';

interface JobiResponse {
  montant: number;
  profil_complet: boolean;
}

// Cache en mémoire pour éviter les appels redondants
let balanceCache: { data: JobiResponse; timestamp: number } | null = null;
const CACHE_DURATION = 30000; // 30 secondes

export const useJobiBalance = () => {
  const { isAuthenticated } = useAuth();
  const [balance, setMontant] = useState<number>(0);
  const [profilComplet, setProfilComplet] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const fetchingRef = useRef<boolean>(false); // Éviter les appels simultanés

  const fetchBalance = useCallback(async () => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    // Éviter les appels simultanés
    if (fetchingRef.current) {
      return;
    }

    // Vérifier le cache
    if (balanceCache && Date.now() - balanceCache.timestamp < CACHE_DURATION) {
      setMontant(balanceCache.data.montant);
      setProfilComplet(balanceCache.data.profil_complet);
      setLoading(false);
      return balanceCache.data;
    }

    fetchingRef.current = true;
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get<JobiResponse>(`${API_CONFIG.baseURL}/api/jobi/solde`, {
        headers,
        withCredentials: true
      });

      // Mettre à jour le cache
      balanceCache = {
        data: response.data,
        timestamp: Date.now()
      };

      setMontant(response.data.montant);
      setProfilComplet(response.data.profil_complet);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 429) {
        logger.warn('Rate limit atteint pour la récupération du solde Jobi');
        return;
      }
      logger.error('Erreur lors de la récupération du solde Jobi:', error);
      throw error;
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  }, [isAuthenticated]);

  const updateBalance = useCallback(async (transaction: JobiTransaction): Promise<boolean> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour effectuer cette action', 'error');
      return false;
    }

    const success = await updateJobiBalanceUtil(transaction);
    if (success) {
      // Invalider le cache et rafraîchir le solde après la mise à jour
      balanceCache = null;
      await fetchBalance();
    }
    return success;
  }, [fetchBalance, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchBalance();
    } else {
      setMontant(0);
      setProfilComplet(false);
      setLoading(false);
    }
  }, [fetchBalance, isAuthenticated]);

  return { balance, profilComplet, loading, refreshBalance: fetchBalance, updateBalance };
};