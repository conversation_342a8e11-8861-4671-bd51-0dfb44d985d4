import { Router, Request, Response, NextFunction } from 'express';
import { getOpenRouterInfo, getOpenRouterCredits, getModelsInfo, clearDailyCallsCache as clearDailyCallsCacheController } from '../controllers/openRouterController';
import { getOpenRouterUsageHistory, getDailyStats, getMonthlyStats, getAvailableModels, getGlobalStats, getOpenRouterDailyCount } from '../controllers/openRouterStatsController';
import { getAllModelPricing, getModelPricing, updateModelPricing, addModelPricing } from '../controllers/openRouterPricingController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiter pour les requêtes OpenRouter
const openRouterLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes vers l\'API OpenRouter. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Fonction asyncHandler pour gérer les erreurs des fonctions asynchrones
function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
): (req: Request, res: Response, next: NextFunction) => void {
  return function (req: Request, res: Response, next: NextFunction): void {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// Middleware d'authentification
router.use(authMiddleware.authenticateToken);
// Middleware pour vérifier le rôle administrateur (jobpadm) pour toutes les routes suivantes de ce routeur
router.use(authMiddleware.checkRole(['jobpadm']));

// Route pour récupérer les informations de la clé API OpenRouter
router.get('/info', openRouterLimiter, asyncHandler(getOpenRouterInfo));

// Route pour récupérer les crédits OpenRouter
router.get('/credits', openRouterLimiter, asyncHandler(getOpenRouterCredits));

// Route pour récupérer les informations sur les modèles
router.get('/models', openRouterLimiter, asyncHandler(getModelsInfo));

// Routes pour les statistiques d'utilisation
// La vérification du rôle admin est déjà faite par le middleware router.use(authMiddleware.checkRole(['jobpadm']))
router.get('/stats/usage', openRouterLimiter, asyncHandler(getOpenRouterUsageHistory));
router.get('/stats/daily', openRouterLimiter, asyncHandler(getDailyStats));
router.get('/stats/monthly', openRouterLimiter, asyncHandler(getMonthlyStats));
router.get('/stats/models', openRouterLimiter, asyncHandler(getAvailableModels));
router.get('/stats/global', openRouterLimiter, asyncHandler(getGlobalStats));
router.post('/stats/clear-daily-calls-cache', openRouterLimiter, asyncHandler(clearDailyCallsCacheController));

// Route pour le nombre exact de requêtes du jour (UTC)
router.get('/stats/daily-count', openRouterLimiter, asyncHandler(getOpenRouterDailyCount));

// Routes pour la gestion des tarifs
router.get('/pricing', openRouterLimiter, asyncHandler(getAllModelPricing));
router.get('/pricing/:model', openRouterLimiter, asyncHandler(getModelPricing));
router.put('/pricing/:model', openRouterLimiter, asyncHandler(updateModelPricing));
router.post('/pricing', openRouterLimiter, asyncHandler(addModelPricing));

export default router;
