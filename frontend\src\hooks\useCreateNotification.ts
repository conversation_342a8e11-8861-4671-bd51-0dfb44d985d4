// Permet de créer des notifications pour l'utilisateur dans le dashboard et la page dashboard/notifications

import { useCallback } from 'react';
import axios from 'axios';
import { useNotifications } from './useNotifications';
import { notify } from '@/components/Notification';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { fetchCsrfToken } from '../services/csrf';
import logger from '@/utils/logger';

export type NotificationType = 'jobi' | 'message' | 'mission' | 'system' | 'mission_comment' | 'profile' | 'bug_report' | 'invoice' | 'subscription';

export interface NotificationData {
  type: NotificationType;
  title: string;
  content: string;
  link?: string;
}

export interface NotificationResponse {
  id: string;
  type: NotificationType;
  title: string;
  content: string;
  link?: string;
  is_read: boolean;
  is_archived: boolean;
  created_at: string;
}

export const useCreateNotification = () => {
  const { fetchNotifications, fetchUnreadCount } = useNotifications();

  // Créer une nouvelle notification
  const createNotification = useCallback(async (notification: NotificationData): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.post<{ success: boolean; data: NotificationResponse }>(
        `${API_CONFIG.baseURL}/api/notifications`,
        notification,
        {
          headers,
          withCredentials: true
        }
      );
      
      if (response.data.success) {
        // notify('Notification envoyée', 'success');
        // Rafraîchir les notifications et le compteur
        await Promise.all([fetchNotifications(), fetchUnreadCount()]);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors de la création de la notification:', error);
      notify('Erreur lors de l\'envoi de la notification', 'error');
      return false;
    }
  }, [fetchNotifications, fetchUnreadCount]);

  // Créer plusieurs notifications en même temps
  const createMultipleNotifications = useCallback(async (notifications: NotificationData[]): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.post<{ success: boolean }>(
        `${API_CONFIG.baseURL}/api/notifications/batch`,
        { notifications },
        {
          headers,
          withCredentials: true
        }
      );
      
      if (response.data.success) {
        notify('Notifications envoyées', 'success');
        await Promise.all([fetchNotifications(), fetchUnreadCount()]);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors de la création des notifications:', error);
      notify('Erreur lors de l\'envoi des notifications', 'error');
      return false;
    }
  }, [fetchNotifications, fetchUnreadCount]);

  // Marquer une notification comme lue
  const markAsRead = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/${notificationId}/read`,
        {},
        {
          headers,
          withCredentials: true
        }
      );
      if (response.data.success) {
        await Promise.all([fetchNotifications(), fetchUnreadCount()]);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors du marquage de la notification:', error);
      return false;
    }
  }, [fetchNotifications, fetchUnreadCount]);

  // Marquer plusieurs notifications comme lues
  const markMultipleAsRead = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/notifications/mark-read`,
        { ids },
        {
          headers,
          withCredentials: true
        }
      );
      if (response.data.success) {
        await Promise.all([fetchNotifications(), fetchUnreadCount()]);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors du marquage des notifications:', error);
      return false;
    }
  }, [fetchNotifications, fetchUnreadCount]);

  // Archiver une notification
  const archiveNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/notifications/${notificationId}/archive`,
        {},
        {
          headers,
          withCredentials: true
        }
      );
      if (response.data.success) {
        notify('Notification archivée', 'success');
        await fetchNotifications();
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors de l\'archivage de la notification:', error);
      notify('Erreur lors de l\'archivage', 'error');
      return false;
    }
  }, [fetchNotifications]);

  // Supprimer une notification
  const deleteNotification = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = csrfToken;
      headers['Content-Type'] = 'application/json';
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/notifications/${notificationId}`,
        {
          headers,
          withCredentials: true
        }
      );
      if (response.data.success) {
        notify('Notification supprimée', 'success');
        await fetchNotifications();
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors de la suppression de la notification:', error);
      notify('Erreur lors de la suppression', 'error');
      return false;
    }
  }, [fetchNotifications]);

  // Supprimer plusieurs notifications
  const deleteMultipleNotifications = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      const csrfToken = await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['Content-Type'] = 'application/json';
      headers['X-CSRF-Token'] = csrfToken;
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/notifications`,
        {
          data: { ids },
          headers,
          withCredentials: true
        }
      );
      if (response.data.success) {
        notify('Notifications supprimées', 'success');
        await fetchNotifications();
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Erreur lors de la suppression des notifications:', error);
      notify('Erreur lors de la suppression', 'error');
      return false;
    }
  }, [fetchNotifications]);

  // Helpers pour créer des types spécifiques de notifications
  const createJobiNotification = useCallback(async (
    title: string,
    content: string,
  ): Promise<boolean> => {
    return createNotification({
      type: 'jobi',
      title,
      content: `${content}`,
      link: '/dashboard/jobi'
    });
  }, [createNotification]);

  const createMissionNotification = useCallback(async (
    title: string,
    content: string,
    missionId: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'mission',
      title,
      content,
      link: `/dashboard/missions/${missionId}`
    });
  }, [createNotification]);

  const createMessageNotification = useCallback(async (
    title: string,
    content: string,
    conversationId: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'message',
      title,
      content,
      link: `/${conversationId}`
    });
  }, [createNotification]);

  const createMissionCommentNotification = useCallback(async (
    title: string,
    content: string,
    commentId: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'mission_comment',
      title,
      content,
      link: `/dashboard/missions/${commentId}?openComments=true`
    });
  }, [createNotification]);

  const createSystemNotification = useCallback(async (
    title: string,
    content: string,
    link?: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'system',
      title,
      content,
      link: link || ''
    });
  }, [createNotification]);

  // Nouvelle fonction helper pour les notifications de profil
  const createProfileNotification = useCallback(async (
    title: string,
    content: string,
    profileId: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'profile',
      title,
      content,
      link: `/profil/${profileId}`
    });
  }, [createNotification]);

  // Nouvelle fonction helper pour les notifications de bug report
  const createBugReportNotification = useCallback(async (
    title: string,
    content: string,
    bugReportId: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'bug_report',
      title,
      content,
      link: `/dashboard/bug-reports/${bugReportId}`
    });
  }, [createNotification]);

  // Nouvelle fonction helper pour les notifications de facture/devis
  const createInvoiceNotification = useCallback(async (
    title: string,
    content: string,
    invoiceId?: string
  ): Promise<boolean> => {
    return createNotification({
      type: 'invoice',
      title,
      content,
      link: invoiceId ? `/dashboard/billing?id=${invoiceId}` : '/dashboard/billing'
    });
  }, [createNotification]);

  return {
    // Fonctions de base
    createNotification,
    createMultipleNotifications,
    markAsRead,
    markMultipleAsRead,
    archiveNotification,
    deleteNotification,
    deleteMultipleNotifications,

    // Helpers pour types spécifiques
    createJobiNotification,
    createMissionNotification,
    createMessageNotification,
    createMissionCommentNotification,
    createSystemNotification,
    createProfileNotification,
    createBugReportNotification,
    createInvoiceNotification
  };
}; 