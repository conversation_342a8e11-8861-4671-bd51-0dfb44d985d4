import React, { useState, useRef } from 'react';
import { Box, Button, Typography, CircularProgress, Alert, Paper, Grid } from '@mui/material';
import { Upload, CheckCircle, X, Image as ImageIcon } from 'lucide-react';
import useImageModeration from '../hooks/useImageModeration';
import { notify } from './Notification';
import RejectedImageMessage from './RejectedImageMessage';
import ImageModerationStatusCompact from './ImageModerationStatusCompact';
import logger from '../utils/logger';

interface ImageModerationPreviewProps {
  onImageValidated: (file: File) => void;
  contentType: string;
  contentId?: string;
  maxSize?: number; // Taille maximale en Mo
  acceptedFormats?: string[]; // Formats acceptés (ex: ['image/jpeg', 'image/png'])
}

/**
 * Composant pour prévisualiser et modérer une image avant de l'envoyer au serveur
 */
const ImageModerationPreview: React.FC<ImageModerationPreviewProps> = ({
  onImageValidated,
  contentType,
  contentId,
  maxSize = 5, // 5 Mo par défaut
  acceptedFormats = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { moderateImage, isLoading, error, result } = useImageModeration();

  // Gérer la sélection d'un fichier
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Vérifier le type de fichier
    if (!acceptedFormats.includes(file.type)) {
      notify(`Format de fichier non pris en charge. Formats acceptés : ${acceptedFormats.map(f => f.replace('image/', '')).join(', ')}`, 'error');
      return;
    }

    // Vérifier la taille du fichier
    if (file.size > maxSize * 1024 * 1024) {
      notify(`L'image est trop volumineuse. Taille maximale : ${maxSize} Mo`, 'error');
      return;
    }

    // Créer une URL pour la prévisualisation
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);
    setSelectedFile(file);

    // Pas besoin de réinitialiser le résultat de modération ici
    // Le résultat sera mis à jour lors de la prochaine modération

    return () => URL.revokeObjectURL(objectUrl);
  };

  // Lancer la modération de l'image
  const handleModerateImage = async () => {
    if (!selectedFile) return;

    try {
      const moderationResult = await moderateImage(selectedFile, contentType, contentId);

      // Si l'image est sûre, la valider
      if (moderationResult.isSafe) {
        notify('Image validée avec succès', 'success');
        onImageValidated(selectedFile);
      } else {
        notify('Image refusée : ne respecte pas nos règles de modération', 'error');

        // Afficher un message plus détaillé dans la console pour le débogage
        logger.info('Image refusée par la modération', {
          description: moderationResult.description,
          contentType: contentType
        });
      }
    } catch (err) {
      // L'erreur est déjà gérée dans le hook
    }
  };

  // Réinitialiser la sélection
  const handleReset = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      <input
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />

      {!selectedFile ? (
        <Button
          variant="outlined"
          startIcon={<Upload />}
          onClick={() => fileInputRef.current?.click()}
          fullWidth
          sx={{ p: 2, border: '1px dashed', borderColor: 'primary.main' }}
        >
          Sélectionner une image
        </Button>
      ) : (
        <Paper elevation={2} sx={{ p: 2, position: 'relative' }}>
          <Button
            size="small"
            color="error"
            sx={{ position: 'absolute', top: 8, right: 8, minWidth: 'auto', p: 0.5 }}
            onClick={handleReset}
          >
            <X size={18} />
          </Button>

          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box
                sx={{
                  width: '100%',
                  height: 200,
                  backgroundImage: `url(${previewUrl})`,
                  backgroundSize: 'contain',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Typography variant="subtitle1" gutterBottom>
                {selectedFile.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {(selectedFile.size / (1024 * 1024)).toFixed(2)} Mo
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {result && (
                result.isSafe ? (
                  <Alert
                    severity="success"
                    sx={{ mb: 2 }}
                    icon={<CheckCircle />}
                  >
                    Image validée par la modération
                  </Alert>
                ) : (
                  <div className="mb-2">
                    <ImageModerationStatusCompact
                      isLoading={false}
                      result={result}
                      contentType={contentType}
                      className="mb-1"
                    />
                    <RejectedImageMessage
                      contentType={contentType}
                      description={result.description}
                      improvementSuggestions={result.improvementSuggestions}
                      variant="detailed"
                      className="mt-2"
                    />
                  </div>
                )
              )}

              <Button
                variant="contained"
                color="primary"
                onClick={handleModerateImage}
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} /> : <ImageIcon />}
                fullWidth
              >
                {isLoading ? 'Modération en cours...' : 'Vérifier l\'image'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}
    </Box>
  );
};

export default ImageModerationPreview;
