import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import logger from '@/utils/logger';
import { notify } from '../../../components/Notification';
import { X, Users, User, Copy, Check, Link } from 'lucide-react';

interface ReferralUser {
  id: string;
  email: string;
  nom: string | null;
  prenom: string | null;
  status: 'pending' | 'rewarded' | 'completed';
  created_at: string;
  completed_at?: string;
  reward_amount?: number;
  earnings?: number;
}

interface ReferrerUser {
  id: string;
  email: string;
  nom: string | null;
  prenom: string | null;
  status: 'pending' | 'rewarded' | 'completed';
  created_at: string | null;
  completed_at?: string | null;
  rewarded_at?: string | null;
  reward_amount?: number;
  total_earnings?: number;
}

interface ReferralListModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Fonction pour masquer partiellement une adresse email
const maskEmail = (email: string): string => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (!username || !domain) return email;
  
  // Masquer une partie du nom d'utilisateur
  let maskedUsername = '';
  if (username.length <= 3) {
    maskedUsername = username[0] + '*'.repeat(username.length - 1);
  } else {
    maskedUsername = username.substring(0, 2) + '*'.repeat(Math.min(username.length - 2, 3));
    if (username.length > 5) {
      maskedUsername += username.substring(username.length - 2);
    }
  }
  
  // Masquer une partie du domaine
  const domainParts = domain.split('.');
  const tld = domainParts.pop() || '';
  const domainName = domainParts.join('.');
  
  let maskedDomain = '';
  if (domainName.length <= 3) {
    maskedDomain = domainName[0] + '*'.repeat(domainName.length - 1);
  } else {
    maskedDomain = domainName[0] + '*'.repeat(Math.min(domainName.length - 1, 3));
    if (domainName.length > 4) {
      maskedDomain += domainName.substring(domainName.length - 1);
    }
  }
  
  return `${maskedUsername}@${maskedDomain}.${tld}`;
};

// Fonction pour formater le nom et prénom sous la forme "Jean D."
const formatName = (prenom: string | null, nom: string | null): string => {
  if (!prenom && !nom) return '';
  
  const prenomFormatted = prenom ? prenom.trim() : '';
  const nomFormatted = nom ? nom.trim() : '';
  
  if (!prenomFormatted && !nomFormatted) return '';
  if (!prenomFormatted) return nomFormatted;
  if (!nomFormatted) return prenomFormatted;
  
  // Format "Jean D." pour Jean Dupont
  return `${prenomFormatted} ${nomFormatted.charAt(0).toUpperCase()}.`;
};

const ReferralListModal: React.FC<ReferralListModalProps> = ({ isOpen, onClose }) => {
  const [referrals, setReferrals] = useState<ReferralUser[]>([]);
  const [referrer, setReferrer] = useState<ReferrerUser | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingReferrer, setIsLoadingReferrer] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [referrerError, setReferrerError] = useState<string | null>(null);
  const [totalEarnings, setTotalEarnings] = useState<number>(0);
  const [referralCode, setReferralCode] = useState<string>('');
  const [isLoadingCode, setIsLoadingCode] = useState<boolean>(true);
  const [codeError, setCodeError] = useState<string | null>(null);
  const [codeCopied, setCodeCopied] = useState<boolean>(false);
  const [linkCopied, setLinkCopied] = useState<boolean>(false);
  const codeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const linkTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchReferrals();
      fetchReferrer();
      fetchReferralCode();
    }
    
    // Nettoyage des timeouts lors du démontage
    return () => {
      if (codeTimeoutRef.current) clearTimeout(codeTimeoutRef.current);
      if (linkTimeoutRef.current) clearTimeout(linkTimeoutRef.current);
    };
  }, [isOpen]);

  const fetchReferrals = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.get('/api/jobi/referrals', API_CONFIG);
      
      if (response.data && response.data.success) {
        setReferrals(response.data.referrals || []);
        setTotalEarnings(response.data.totalEarnings || 0);
      } else {
        setError(response.data?.message || 'Erreur lors de la récupération des filleuls');
        notify('Erreur lors de la récupération des filleuls', 'error');
      }
    } catch (err) {
      logger.error('Erreur lors de la récupération des filleuls:', err);
      setError('Erreur lors de la récupération des filleuls');
      notify('Erreur lors de la récupération des filleuls', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchReferrer = async () => {
    setIsLoadingReferrer(true);
    setReferrerError(null);
    
    try {
      const response = await axios.get('/api/jobi/referrer', API_CONFIG);
      
      if (response.data && response.data.success) {
        setReferrer(response.data.referrer);
      } else {
        setReferrerError(response.data?.message || 'Erreur lors de la récupération du parrain');
      }
    } catch (err) {
      logger.error('Erreur lors de la récupération du parrain:', err);
      setReferrerError('Erreur lors de la récupération du parrain');
    } finally {
      setIsLoadingReferrer(false);
    }
  };

  const fetchReferralCode = async () => {
    setIsLoadingCode(true);
    setCodeError(null);
    
    try {
      const response = await axios.get('/api/jobi/referral-code', API_CONFIG);
      
      if (response.data && response.data.success) {
        setReferralCode(response.data.referralCode || '');
      } else {
        setCodeError(response.data?.message || 'Erreur lors de la récupération du code de parrainage');
      }
    } catch (err) {
      logger.error('Erreur lors de la récupération du code de parrainage:', err);
      setCodeError('Erreur lors de la récupération du code de parrainage');
    } finally {
      setIsLoadingCode(false);
    }
  };

  const copyToClipboard = (text: string, type: 'code' | 'link') => {
    navigator.clipboard.writeText(text)
      .then(() => {
        if (type === 'code') {
          setCodeCopied(true);
          if (codeTimeoutRef.current) clearTimeout(codeTimeoutRef.current);
          codeTimeoutRef.current = setTimeout(() => setCodeCopied(false), 2000);
          notify('Code de parrainage copié !', 'success');
        } else {
          setLinkCopied(true);
          if (linkTimeoutRef.current) clearTimeout(linkTimeoutRef.current);
          linkTimeoutRef.current = setTimeout(() => setLinkCopied(false), 2000);
          notify('Lien de parrainage copié !', 'success');
        }
      })
      .catch(err => {
        logger.error('Erreur lors de la copie dans le presse-papiers:', err);
        notify('Erreur lors de la copie dans le presse-papiers', 'error');
      });
  };

  const getReferralLink = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/inscription?ref=${referralCode}`;
  };

  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'rewarded':
        return 'Récompensé';
      case 'completed':
        return 'Complété';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rewarded':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Fonction pour gérer le clic sur l'overlay (fond de la modal)
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Si l'élément cliqué est l'overlay lui-même (et non un de ses enfants)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={handleOverlayClick}
    >
      <div className="bg-white rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <Users className="mr-2 text-[#FF6B2C]" size={24} />
            Mes Filleuls
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X size={24} />
          </button>
        </div>
        
        <div className="p-4 overflow-y-auto flex-grow">
          {/* Section du parrain */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <User className="mr-2 text-[#FF6B2C]" size={20} />
              Mon Parrain
            </h3>
            
            {isLoadingReferrer ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6B2C]"></div>
              </div>
            ) : referrerError ? (
              <div className="text-center text-red-500 p-2">
                {referrerError}
              </div>
            ) : !referrer ? (
              <div className="text-center text-gray-500 p-4 bg-gray-50 rounded-lg">
                <p>Vous n'avez pas de parrain</p>
              </div>
            ) : (
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-md font-medium text-gray-900">
                      {formatName(referrer.prenom, referrer.nom) || 'Utilisateur'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {maskEmail(referrer.email)}
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(referrer.status)}`}>
                      {getStatusLabel(referrer.status)}
                    </span>
                    {referrer.created_at && (
                      <div className="text-xs text-gray-500 mt-1">
                        Depuis le {formatDate(referrer.created_at)}
                      </div>
                    )}
                  </div>
                </div>
                
                {(referrer.status === 'rewarded' || referrer.total_earnings) && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Récompense de parrainage :</span>
                      <span className="text-md font-semibold text-[#FF6B2C]">
                        {referrer.total_earnings ? referrer.total_earnings.toFixed(2) : referrer.reward_amount?.toFixed(2) || '0.00'} Jobis
                      </span>
                    </div>
                    {referrer.rewarded_at && (
                      <div className="text-xs text-gray-500 mt-1 text-right">
                        Récompensé le {formatDate(referrer.rewarded_at)}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="border-t pt-6 mt-2">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <Users className="mr-2 text-[#FF6B2C]" size={20} />
              Mes Filleuls
            </h3>
            
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-[#FF6B2C]"></div>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 p-4">
                {error}
              </div>
            ) : referrals.length === 0 ? (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="mb-4 text-gray-700 font-medium">Vous n'avez pas encore de filleuls</p>
                
                {isLoadingCode ? (
                  <div className="flex justify-center items-center h-10 mb-4">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#FF6B2C]"></div>
                  </div>
                ) : codeError ? (
                  <p className="text-red-500 text-sm mb-4">{codeError}</p>
                ) : (
                  <div className="space-y-6 max-w-md mx-auto">
                    <div>
                      <p className="text-sm text-gray-600 mb-2 font-medium">Votre code de parrainage :</p>
                      <div className="flex items-center justify-center shadow-sm">
                        <div className="bg-white border border-gray-300 rounded-l-md px-4 py-3 font-mono text-sm w-72 text-center overflow-hidden flex items-center justify-center h-11">
                          {referralCode}
                        </div>
                        <button 
                          onClick={() => copyToClipboard(referralCode, 'code')}
                          className="bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-r-md px-4 transition-colors flex items-center h-11"
                          title="Copier le code"
                        >
                          {codeCopied ? <Check size={18} /> : <Copy size={18} />}
                          <span className="ml-2 hidden sm:inline">Copier</span>
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm text-gray-600 mb-2 font-medium">Votre lien de parrainage :</p>
                      <div className="flex items-center justify-center shadow-sm">
                        <div className="bg-white border border-gray-300 rounded-l-md px-4 py-3 text-sm truncate w-72 overflow-hidden flex items-center h-11">
                          {getReferralLink()}
                        </div>
                        <button 
                          onClick={() => copyToClipboard(getReferralLink(), 'link')}
                          className="bg-[#FF6B2C] hover:bg-[#FF7A35] text-white rounded-r-md px-4 transition-colors flex items-center h-11"
                          title="Copier le lien"
                        >
                          {linkCopied ? <Check size={18} /> : <Link size={18} />}
                          <span className="ml-2 hidden sm:inline">Copier</span>
                        </button>
                      </div>
                    </div>
                    
                    <div className="bg-[#FFF8F3] p-4 rounded-lg border border-[#FFE4BA] mt-4">
                      <p className="text-sm text-gray-700">
                        <span className="font-medium">Astuce :</span> Partagez votre code ou lien de parrainage pour inviter vos amis à rejoindre Job Partiel. Vous recevrez des Jobis pour chaque inscription validée !
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Utilisateur
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date d'inscription
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Gains
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {referrals.map((referral) => (
                      <tr key={referral.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col">
                            <div className="text-sm font-medium text-gray-900">
                              {formatName(referral.prenom, referral.nom) || 'Utilisateur'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {maskEmail(referral.email)}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(referral.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(referral.status)}`}>
                            {getStatusLabel(referral.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {referral.status === 'rewarded' ? (
                            <span className="text-sm font-medium text-[#FF6B2C]">
                              {referral.earnings?.toFixed(2) || '0.00'} Jobis
                            </span>
                          ) : (
                            <span className="text-sm text-gray-500">-</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        
        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Total : {referrals.length} filleul{referrals.length !== 1 ? 's' : ''}
            </div>
            <div className="flex items-center">
              {totalEarnings > 0 && (
                <div className="mr-4 text-sm font-medium">
                  <span className="text-gray-600">Total des gains :</span>
                  <span className="ml-2 text-[#FF6B2C] font-semibold">{totalEarnings.toFixed(2)} Jobis</span>
                </div>
              )}
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralListModal; 