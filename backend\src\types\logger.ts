export enum LogEventType {
    // Server events
    SERVER_START = 'SERVER_START',
    SERVER_STOP = 'SERVER_STOP',
    SERVER_ERROR = 'SERVER_ERROR',

    // Authentication events
    AUTH_SUCCESS = 'AUTH_SUCCESS',
    AUTH_FAILURE = 'AUTH_FAILURE',
    AUTH_LOGOUT = 'AUTH_LOGOUT',
    AUTH_REGISTER = 'AUTH_REGISTER',
    AUTH_VERIFY = 'AUTH_VERIFY',
    AUTH_RESET = 'AUTH_RESET',

    // Email events
    EMAIL_SENT = 'EMAIL_SENT',
    EMAIL_ERROR = 'EMAIL_ERROR',
    EMAIL_VERIFY = 'EMAIL_VERIFY',
    EMAIL_RESET = 'EMAIL_RESET',

    // Database events
    DB_ERROR = 'DB_ERROR',
    DB_QUERY = 'DB_QUERY',
    DB_CONNECT = 'DB_CONNECT',

    // Security events
    SECURITY_WARN = 'SECURITY_WARN',
    SECURITY_ERROR = 'SECURITY_ERROR',
    RATE_LIMIT = 'RATE_LIMIT',
    INVALID_TOKEN = 'INVALID_TOKEN',

    // API events
    API_REQUEST = 'API_REQUEST',
    API_RESPONSE = 'API_RESPONSE',
    API_ERROR = 'API_ERROR'
}
