import { Response } from 'express';
import { CookieOptions } from 'express-serve-static-core';
import config from '../config';

export const cookieUtils = {
  setAccessTokenCookie(res: Response, token: string): void {
    const cookieOptions: CookieOptions = {
      httpOnly: true,
      secure: config.isProduction,
      sameSite: 'none', // Modifié pour supporter les redirections cross-site
      maxAge: 60 * 60 * 1000, // 1 heure en millisecondes
      path: '/',
      domain: config.isProduction ? '.jobpartiel.fr' : undefined
    };

    res.cookie('access_token', token, cookieOptions);
  },

  setRefreshTokenCookie(res: Response, token: string): void {
    const cookieOptions: CookieOptions = {
      httpOnly: true,
      secure: config.isProduction,
      sameSite: 'none', // Modifié pour supporter les redirections cross-site
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 jours en millisecondes
      path: '/', // Chemin racine pour être accessible partout
      domain: config.isProduction ? '.jobpartiel.fr' : undefined
    };

    res.cookie('refresh_token', token, cookieOptions);
  },

  clearTokenCookies(res: Response): void {
    const cookieOptions: CookieOptions = {
      httpOnly: true,
      secure: config.isProduction,
      sameSite: 'none', // Modifié pour supporter les redirections cross-site
      path: '/',
      domain: config.isProduction ? '.jobpartiel.fr' : undefined
    };
    
    res.clearCookie('access_token', cookieOptions);
    res.clearCookie('refresh_token', cookieOptions);
  }
};
