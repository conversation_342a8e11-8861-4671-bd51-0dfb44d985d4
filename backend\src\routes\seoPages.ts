import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { asyncHandler } from '../utils/inputValidation';
import { SERVICE_SUBCATEGORIES } from '../data/serviceTypes';
import { decryptProfilDataAsync } from '../utils/encryption';

const router = Router();

/**
 * Génère automatiquement un mapping des synonymes vers les noms de services
 * à partir des données définies dans serviceTypes.ts
 */
function generateServiceMapping(): { [key: string]: string } {
  const mapping: { [key: string]: string } = {};

  SERVICE_SUBCATEGORIES.forEach(subcategory => {
    // Ajouter le nom de la sous-catégorie comme clé principale
    const normalizedName = subcategory.nom.toLowerCase()
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/[ñ]/g, 'n')
      .replace(/'/g, '')
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    mapping[normalizedName] = subcategory.nom;

    // Ajouter tous les synonymes
    if (subcategory.synonymes) {
      subcategory.synonymes.forEach(synonyme => {
        const normalizedSynonym = synonyme.toLowerCase()
          .replace(/[àáâãäå]/g, 'a')
          .replace(/[èéêë]/g, 'e')
          .replace(/[ìíîï]/g, 'i')
          .replace(/[òóôõö]/g, 'o')
          .replace(/[ùúûü]/g, 'u')
          .replace(/[ç]/g, 'c')
          .replace(/[ñ]/g, 'n')
          .replace(/'/g, '')
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');

        if (normalizedSynonym && !mapping[normalizedSynonym]) {
          mapping[normalizedSynonym] = subcategory.nom;
        }
      });
    }
  });

  return mapping;
}

/**
 * Recherche un service par terme de recherche en utilisant les synonymes
 */
function findServiceByTerm(searchTerm: string): string | null {
  const mapping = generateServiceMapping();
  const normalizedTerm = searchTerm.toLowerCase()
    .replace(/[àáâãäå]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ç]/g, 'c')
    .replace(/[ñ]/g, 'n')
    .replace(/'/g, '')
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');

  return mapping[normalizedTerm] || null;
}

/**
 * Obtient tous les noms de services disponibles
 */
function getAllServices(): string[] {
  return SERVICE_SUBCATEGORIES.map(subcategory => subcategory.nom);
}

/**
 * Recherche avancée de services par terme partiel
 */
function searchServices(searchTerm: string): string[] {
  // Si le terme de recherche est vide, retourner tous les services
  if (!searchTerm || searchTerm.trim() === '') {
    return getAllServices();
  }

  const normalizedSearch = searchTerm.toLowerCase();
  const results: string[] = [];

  SERVICE_SUBCATEGORIES.forEach(subcategory => {
    // Vérifier le nom de la sous-catégorie
    if (subcategory.nom.toLowerCase().includes(normalizedSearch)) {
      results.push(subcategory.nom);
      return;
    }

    // Vérifier les synonymes
    if (subcategory.synonymes) {
      const hasMatch = subcategory.synonymes.some(synonyme => 
        synonyme.toLowerCase().includes(normalizedSearch)
      );
      
      if (hasMatch) {
        results.push(subcategory.nom);
      }
    }
  });

  // Retourner les résultats uniques
  return [...new Set(results)];
}

// Route pour vérifier si une combinaison service/ville existe
router.get('/check/:service/:ville', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { service, ville } = req.params;

    // Vérifier le cache
    const cacheKey = `seo_check:${service}:${ville}`;
    const cachedResult = await redis.get(cacheKey);

    if (cachedResult) {
      res.json(JSON.parse(cachedResult));
      return;
    }

    // Normaliser le service en utilisant les synonymes de serviceTypes.ts
    const serviceNormalized = findServiceByTerm(service) || service.replace(/-/g, ' ');

    // Étape 1: Récupérer les profils dans la ville
    const { data: profils, error: profilError } = await supabase
      .from('user_profil')
      .select(`
        user_id,
        ville,
        users!inner(
          user_type,
          profil_actif
        )
      `)
      .eq('profil_visible', true)
      .eq('users.profil_actif', true)
      .eq('users.user_type', 'jobbeur')
      .ilike('ville', `%${ville}%`);

    if (profilError) {
      logger.error('Erreur lors de la récupération des profils:', profilError);
      res.status(500).json({ exists: false, error: 'Erreur serveur' });
      return;
    }

    if (!profils || profils.length === 0) {
      res.json({ exists: false, service: serviceNormalized, ville: ville, providersCount: 0 });
      return;
    }

    // Déchiffrer les données sensibles des profils récupérés
    const decryptedProfils = await Promise.all(profils.map(async (profil) => ({
      ...profil,
      ...await decryptProfilDataAsync(profil)
    })));

    // Étape 2: Vérifier si ces utilisateurs ont des services correspondants
    const userIds = decryptedProfils.map(p => p.user_id);
    const { data: services, error: serviceError } = await supabase
      .from('user_services')
      .select('user_id, titre, description, statut')
      .in('user_id', userIds)
      .eq('statut', 'actif');

    if (serviceError) {
      logger.error('Erreur lors de la récupération des services:', serviceError);
      res.status(500).json({ exists: false, error: 'Erreur serveur' });
      return;
    }

    // Vérifier si des services correspondent au terme de recherche
    // Utiliser la recherche avancée pour une meilleure correspondance
    const searchTerms = searchServices(service);
    const matchingServices = services?.filter(userService => {
      const serviceTitle = userService.titre.toLowerCase();
      const serviceDesc = userService.description.toLowerCase();
      
      // Vérifier si le titre ou la description contient le terme normalisé
      if (serviceTitle.includes(serviceNormalized.toLowerCase()) || 
          serviceDesc.includes(serviceNormalized.toLowerCase())) {
        return true;
      }
      
      // Vérifier si le titre ou la description contient un des termes de recherche
      return searchTerms.some(term => 
        serviceTitle.includes(term.toLowerCase()) || 
        serviceDesc.includes(term.toLowerCase())
      );
    }) || [];

    const hasProviders = matchingServices.length > 0;

    const result = {
      exists: hasProviders,
      service: serviceNormalized,
      ville: ville,
      providersCount: matchingServices.length
    };

    // Cache pour 1 heure
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 3600);

    res.json(result);
  } catch (error) {
    logger.error('Erreur lors de la vérification service/ville:', error);
    res.status(500).json({ exists: false, error: 'Erreur serveur' });
  }
}));

// Route pour récupérer les villes proches d'un département
router.get('/nearby-cities/:ville', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { ville } = req.params;
    const { limit = 10 } = req.query;

    // Vérifier le cache
    const cacheKey = `nearby_cities:${ville}:${limit}`;
    const cachedCities = await redis.get(cacheKey);

    if (cachedCities) {
      res.json(JSON.parse(cachedCities));
      return;
    }

    // Récupérer le code postal de la ville de référence
    const { data: referenceCity, error: refError } = await supabase
      .from('user_profil')
      .select('code_postal, ville')
      .ilike('ville', `%${ville}%`)
      .not('code_postal', 'is', null)
      .limit(1)
      .single();

    if (refError || !referenceCity?.code_postal) {
      res.json([]);
      return;
    }

    // Déchiffrer les données sensibles de la ville de référence
    const decryptedReferenceCity = await decryptProfilDataAsync(referenceCity);

    // Extraire le département (2 premiers chiffres du code postal)
    const departement = decryptedReferenceCity.code_postal.substring(0, 2);

    // Rechercher d'autres villes du même département
    const { data: nearbyCities, error } = await supabase
      .from('user_profil')
      .select('ville, code_postal')
      .like('code_postal', `${departement}%`)
      .not('ville', 'ilike', `%${ville}%`)
      .not('ville', 'is', null)
      .limit(parseInt(limit as string) * 2);

    if (error) {
      logger.error('Erreur lors de la récupération des villes proches:', error);
      res.json([]);
      return;
    }

    // Déchiffrer les données sensibles des villes proches
    const decryptedNearbyCities = nearbyCities ? await Promise.all(nearbyCities.map(city => decryptProfilDataAsync(city))) : [];

    // Dédupliquer et formater les villes
    const uniqueCities = Array.from(new Set(
      decryptedNearbyCities.map(city => city.ville?.toLowerCase()) || []
    ))
    .filter(city => city && city !== ville.toLowerCase())
    .slice(0, parseInt(limit as string))
    .map(city => city.charAt(0).toUpperCase() + city.slice(1));

    // Cache pour 24 heures
    await redis.set(cacheKey, JSON.stringify(uniqueCities), 'EX', 86400);

    res.json(uniqueCities);
  } catch (error) {
    logger.error('Erreur lors de la récupération des villes proches:', error);
    res.json([]);
  }
}));

// Route pour récupérer les services similaires
router.get('/similar-services/:service', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const { service } = req.params;
    const { limit = 8 } = req.query;

    // Vérifier le cache
    const cacheKey = `similar_services:${service}:${limit}`;
    const cachedServices = await redis.get(cacheKey);

    if (cachedServices) {
      res.json(JSON.parse(cachedServices));
      return;
    }

    // Utiliser getAllServices pour récupérer tous les services disponibles
    const allServices = getAllServices();
    const currentService = findServiceByTerm(service);
    
    // Filtrer pour exclure le service actuel et limiter les résultats
    const similarServices = allServices
      .filter(s => s !== currentService)
      .slice(0, parseInt(limit as string))
      .map(serviceName => serviceName.toLowerCase()
        .replace(/[àáâãäå]/g, 'a')
        .replace(/[èéêë]/g, 'e')
        .replace(/[ìíîï]/g, 'i')
        .replace(/[òóôõö]/g, 'o')
        .replace(/[ùúûü]/g, 'u')
        .replace(/[ç]/g, 'c')
        .replace(/[ñ]/g, 'n')
        .replace(/'/g, '')
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '')
      );

    // Cache pour 24 heures
    await redis.set(cacheKey, JSON.stringify(similarServices), 'EX', 86400);

    res.json(similarServices);
  } catch (error) {
    logger.error('Erreur lors de la récupération des services similaires:', error);
    res.json([]);
  }
}));

export default router;
