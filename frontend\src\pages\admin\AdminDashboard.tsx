import React, { useEffect, useState, useRef } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  IconButton,
  useTheme,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  useMediaQuery,
  Badge,
  Avatar,
  Collapse,
  styled,
} from '@mui/material';
import {
  Notifications,
  Settings,
  Person,
  AttachMoney,
  Warning,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Home as HomeIcon,
  BugReport as BugReportIcon,
  DesignServices as ServicesIcon,
  Tune as SettingsIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  SupportAgent as SupportIcon,
  Forum as ForumIcon,
  Label as LabelIcon,
  ExpandLess,
  ExpandMore,
  Add as AddIcon,
  AssignmentInd as AssignmentIndIcon,
  Storage as StorageIcon,
  LocalOffer as LocalOfferIcon,
  WarningAmber as WarningAmberIcon,
  Dns as DnsIcon,
  MailOutline as MailOutlineIcon,
  Email as EmailIcon,
  FilterList as FilterListIcon,
  Bar<PERSON>hart as BarChartIcon,
  Api as ApiIcon,
  Analytics as AnalyticsIcon,
  Image as ImageIcon,
  Security as SecurityIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import axios from 'axios';
import logger from '../../utils/logger';
import { Helmet } from 'react-helmet-async';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { DocumentIcon } from '../dashboard/icons';

// Styled components
const StyledSidebar = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: '#FFFFFF',
  boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.05)',
  overflow: 'hidden',
}));

const StyledMenuItem = styled(ListItem, {
  shouldForwardProp: (prop) => prop !== 'active',
})<{ active?: boolean }>(({ theme, active }) => ({
  borderRadius: '8px',
  margin: '4px 12px',
  padding: '8px 16px',
  backgroundColor: active ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
  transition: 'all 0.2s ease',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.05)',
  },
  '&::before': active ? {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '4px',
    height: '60%',
    backgroundColor: '#FF6B2C',
    borderRadius: '0 4px 4px 0',
  } : {},
}));

const StyledMenuIcon = styled(ListItemIcon, {
  shouldForwardProp: (prop) => prop !== 'active',
})<{ active?: boolean }>(({ theme, active }) => ({
  minWidth: '42px',
  color: active ? '#FF6B2C' : theme.palette.action.active,
}));

const StyledSubmenuContainer = styled(Collapse)(({ theme }) => ({
  background: 'rgba(249, 250, 251, 0.7)',
  borderRadius: '0 0 8px 8px',
  margin: '0 12px 4px 12px',
  overflow: 'hidden',
}));

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: '16px 20px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
}));

interface KPIs {
  totalUsers: number;
  totalRevenue: number;
  pendingReports: number;
  activeServices: number;
  monthlyRevenue: number;
  weeklyGrowth: number;
}

interface MenuGroup {
  title: string;
  items: MenuItem[];
}

interface MenuItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  submenu?: MenuItem[];
  exact?: boolean;
  badge?: number;
}

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const [kpis, setKpis] = useState<KPIs | null>(null);
  const [loading, setLoading] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    bugs: false,
    support: false,
    moderation: false,
    openRouter: false
  });
  const location = useLocation();
  const navigate = useNavigate();
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Responsive breakpoints
  const isMobile = useMediaQuery('(max-width:768px)');

  // Menu structure
  const menuGroups: MenuGroup[] = [
    {
      title: "Administration",
      items: [
        { name: "Dashboard", path: "/admin", icon: <DashboardIcon />, exact: true },
        { name: "Gestion des Utilisateurs", path: "/admin/user-management", icon: <Person />, exact: true },
        { name: "Monitoring Sécurité", path: "/admin/security-monitoring", icon: <SecurityIcon />, exact: true },
        { name: "Codes promo", path: "/admin/promocodes", icon: <LocalOfferIcon />, exact: true },
        { name: "Signalements", path: "/admin/reported-content", icon: <WarningAmberIcon />, exact: true, badge: 3 },
      ]
    },
    {
      title: "Gestion Contenu",
      items: [
        { 
          name: "Modération par IA", 
          path: "/admin/moderation", 
          icon: <FilterListIcon />, 
          submenu: [
            { name: "Logs de modération", path: "/admin/moderation-logs", icon: <FilterListIcon /> },
            { name: "Statistiques", path: "/admin/moderation-stats", icon: <BarChartIcon /> },
          ]
        },
        { name: "Documents entreprise", path: "/admin/entreprise-documents", icon: <DocumentIcon style={{ color: 'inherit', width: 24, height: 24 }} strokeWidth={1.5} /> },
        { name: "Mail & File d'attente", path: "/admin/email-queue", icon: <MailOutlineIcon /> },
        { name: "Abonnés Newsletter", path: "/admin/newsletter-subscribers", icon: <MailOutlineIcon /> },
        { 
          name: "Rapports de bugs", 
          path: "/admin/bugs", 
          icon: <BugReportIcon />,
          submenu: [
            { name: "Liste des rapports", path: "/admin/bug-reports", icon: <ForumIcon /> },
            { name: "Créer un rapport", path: "/dashboard/bug-reports/nouveau", icon: <AddIcon /> }
          ]
        },
      ]
    },
    {
      title: "Support",
      items: [
        {
          name: "Support", 
          path: "/admin/support", 
          icon: <SupportIcon />,
          submenu: [
            { name: "Statistiques", path: "/admin/support", icon: <ForumIcon />, exact: true },
            { name: "Tickets", path: "/admin/support/tickets", icon: <ForumIcon /> },
            { name: "Mes tickets assignés", path: "/admin/support/assigned", icon: <AssignmentIndIcon /> },
            { name: "Créer un ticket", path: "/admin/support/new", icon: <AddIcon /> },
            { name: "Gérer les tags", path: "/admin/support/tags", icon: <LabelIcon /> },
            { name: "Modèles de réponse", path: "/admin/support/templates", icon: <ForumIcon /> },
          ]
        },
      ]
    },
    {
      title: "API & Statistiques",
      items: [
        {
          name: "OpenRouter", 
          path: "/admin/openrouter", 
          icon: <ApiIcon />,
          submenu: [
            { name: "Configuration API", path: "/admin/openrouter-info", icon: <ApiIcon /> },
            { name: "Statistiques d'utilisation", path: "/admin/openrouter-stats", icon: <AnalyticsIcon /> },
            { name: "Tarification", path: "/admin/openrouter-pricing", icon: <AttachMoney /> },
          ]
        },
        { name: "Stats Générations Images IA", path: "/admin/ai-image-stats", icon: <ImageIcon /> },
        { name: "Stats Facturation", path: "/admin/billing-stats", icon: <ReceiptIcon /> },
        { name: "Monitoring VPS", path: "/admin/vps", icon: <StorageIcon /> },
        { name: "Gestion Redis", path: "/admin/redis", icon: <DnsIcon /> },
      ]
    }
  ];

  useEffect(() => {
    const fetchKPIs = async () => {
      try {
        const response = await axios.get('/api/admin/dashboard/kpis');
        setKpis(response.data);
      } catch (error) {
        logger.error('Error fetching KPIs:', error);
      } finally {
        setLoading(false);
      }
    };

    if (location.pathname === '/admin') {
      fetchKPIs();
    } else {
      setLoading(false);
    }
  }, [location.pathname]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuExpand = (menuName: string) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menuName]: !prev[menuName]
    }));
  };

  const isMenuActive = (item: MenuItem): boolean => {
    if (item.exact) {
      return location.pathname === item.path;
    }
    if (item.submenu) {
      return item.submenu.some(subItem => location.pathname.includes(subItem.path));
    }
    return location.pathname.includes(item.path);
  };

  const isSubmenuActive = (path: string): boolean => {
    return location.pathname === path;
  };

  const StatCard = ({ title, value, icon }: { title: string; value: string | number; icon: React.ReactNode }) => (
    <Card sx={{ height: '100%', borderRadius: '12px', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography color="textSecondary" gutterBottom>
            {title}
          </Typography>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            width: 40, 
            height: 40, 
            borderRadius: '50%', 
            bgcolor: 'rgba(255,107,44,0.1)',
            color: '#FF6B2C'
          }}>
            {icon}
          </Box>
        </Box>
        <Typography variant="h4" component="div" sx={{ mt: 1 }}>
          {value}
        </Typography>
      </CardContent>
    </Card>
  );

  const renderMenuItem = (item: MenuItem) => {
    const active = isMenuActive(item);
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const menuKey = item.name.toLowerCase().replace(/\s+/g, '');
    const isExpanded = hasSubmenu && expandedMenus[menuKey];

    return (
      <React.Fragment key={item.path}>
        <StyledMenuItem 
          active={active}
          onClick={() => {
            if (hasSubmenu) {
              handleMenuExpand(menuKey);
            } else {
              if (item.path) {
                navigate(item.path);
                if (isMobile) handleDrawerToggle();
              }
            }
          }}
          sx={{ cursor: 'pointer' }}
        >
          <StyledMenuIcon active={active}>
            {item.icon}
          </StyledMenuIcon>
          
          <ListItemText 
            primary={item.name} 
            sx={{ 
              '& .MuiTypography-root': { 
                fontWeight: active ? 600 : 500,
                color: active ? '#FF6B2C' : 'inherit'
              } 
            }} 
          />

          {item.badge !== undefined && (
            <Badge 
              badgeContent={item.badge} 
              color="error" 
              sx={{ 
                mr: hasSubmenu ? 1 : 0,
                '& .MuiBadge-badge': {
                  fontSize: '0.7rem',
                  height: '18px',
                  minWidth: '18px'
                }
              }}
            />
          )}
          
          {hasSubmenu && (
            isExpanded ? <ExpandLess color={active ? "primary" : "action"} /> : <ExpandMore color={active ? "primary" : "action"} />
          )}
        </StyledMenuItem>

        {hasSubmenu && (
          <StyledSubmenuContainer in={isExpanded}>
            <List disablePadding>
              {item.submenu?.map((subItem) => (
                <ListItem
                  key={subItem.path}
                  component={Link}
                  to={subItem.path}
                  onClick={() => {
                    if (isMobile) handleDrawerToggle();
                  }}
                  sx={{
                    borderRadius: '6px',
                    margin: '2px 0',
                    padding: '6px 12px 6px 16px',
                    backgroundColor: isSubmenuActive(subItem.path) ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.05)',
                    },
                  }}
                >
                  <ListItemIcon sx={{ 
                    minWidth: '32px',
                    color: isSubmenuActive(subItem.path) ? '#FF6B2C' : 'rgba(0, 0, 0, 0.54)'
                  }}>
                    {subItem.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={subItem.name} 
                    primaryTypographyProps={{ 
                      fontSize: '0.9rem',
                      fontWeight: isSubmenuActive(subItem.path) ? 600 : 500,
                      color: isSubmenuActive(subItem.path) ? '#FF6B2C' : 'inherit'
                    }} 
                  />
                </ListItem>
              ))}
            </List>
          </StyledSubmenuContainer>
        )}
      </React.Fragment>
    );
  };

  const renderSidebar = () => (
    <StyledSidebar ref={sidebarRef}>
      <SidebarHeader>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#FF6B2C' }}>
          JobPartiel Admin
        </Typography>
        {isMobile && (
          <IconButton
            onClick={handleDrawerToggle}
            size="small"
            sx={{ color: '#FF6B2C' }}
          >
            <CloseIcon />
          </IconButton>
        )}
      </SidebarHeader>

      <Box sx={{ overflow: 'auto', flexGrow: 1, py: 2 }}>
        {menuGroups.map((group, index) => (
          <Box key={index} sx={{ mb: 3 }}>
            <Typography 
              variant="caption" 
              sx={{ 
                px: 3, 
                py: 1.5, 
                fontWeight: 600,
                color: 'text.secondary',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                display: 'block'
              }}
            >
              {group.title}
            </Typography>
            <List disablePadding>
              {group.items.map(renderMenuItem)}
            </List>
          </Box>
        ))}
      </Box>

      <Box sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.06)' }}>
        <ListItem
          component={Link}
          to="/"
          onClick={() => {
            if (isMobile) handleDrawerToggle();
          }}
          sx={{
            borderRadius: '8px',
            margin: '4px 12px',
            padding: '8px 16px',
            backgroundColor: 'transparent',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: 'rgba(255, 107, 44, 0.05)',
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: '42px', color: 'rgba(0, 0, 0, 0.54)' }}>
            <HomeIcon />
          </ListItemIcon>
          <ListItemText primary="Retour au site" />
        </ListItem>
      </Box>
    </StyledSidebar>
  );

  const renderDashboardContent = () => {
    if (loading) {
      return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>Chargement...</Box>;
    }

    if (location.pathname !== '/admin') {
      return <Outlet />;
    }

    return (
      <>
        {/* KPI Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <StatCard
              title="Utilisateurs Total"
              value={kpis?.totalUsers || 0}
              icon={<Person color="primary" />}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <StatCard
              title="Revenus Total"
              value={`${kpis?.totalRevenue || 0}€`}
              icon={<AttachMoney color="primary" />}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <StatCard
              title="Signalements en Attente"
              value={kpis?.pendingReports || 0}
              icon={<Warning color="error" />}
            />
          </Grid>
        </Grid>

        {/* Growth Chart */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Croissance des Utilisateurs
          </Typography>
          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={[
                  { name: 'Semaine 1', users: kpis?.weeklyGrowth || 0 },
                  { name: 'Semaine 2', users: (kpis?.weeklyGrowth || 0) * 1.2 },
                  { name: 'Semaine 3', users: (kpis?.weeklyGrowth || 0) * 1.5 },
                  { name: 'Semaine 4', users: (kpis?.weeklyGrowth || 0) * 1.8 },
                ]}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Line type="monotone" dataKey="users" stroke={theme.palette.primary.main} />
              </LineChart>
            </ResponsiveContainer>
          </Box>
        </Paper>

        {/* Revenue Chart */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Évolution des Revenus
          </Typography>
          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={[
                  { name: 'Jan', revenue: kpis?.monthlyRevenue || 0 },
                  { name: 'Fév', revenue: (kpis?.monthlyRevenue || 0) * 1.1 },
                  { name: 'Mar', revenue: (kpis?.monthlyRevenue || 0) * 1.3 },
                  { name: 'Avr', revenue: (kpis?.monthlyRevenue || 0) * 1.6 },
                ]}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Line type="monotone" dataKey="revenue" stroke={theme.palette.success.main} />
              </LineChart>
            </ResponsiveContainer>
          </Box>
        </Paper>
      </>
    );
  };

  const drawerWidth = 280;

  return (
    <Box sx={{ display: 'flex' }}>
      <Helmet>
        <title>Administration - JobPartiel.fr</title>
        <meta name="description" content="Espace d'administration de JobPartiel.fr" />
      </Helmet>

      <AppBar
        position="fixed"
        sx={{
          width: isMobile ? '100%' : `calc(100% - ${drawerWidth}px)`,
          ml: isMobile ? 0 : `${drawerWidth}px`,
          bgcolor: '#FFF',
          color: '#333',
          boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: isMobile ? 'block' : 'none' }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {location.pathname === '/admin' ? 'Dashboard Administrateur' :
             location.pathname === '/admin/user-management' ? 'Gestion des Utilisateurs' :
             location.pathname.startsWith('/admin/bug-reports') ? 'Gestion des Rapports de Bugs' :
             location.pathname === '/admin/openrouter-info' ? 'Configuration API OpenRouter' :
             location.pathname === '/admin/openrouter-stats' ? 'Statistiques OpenRouter' :
             location.pathname === '/admin/openrouter-pricing' ? 'Tarification OpenRouter' : 'Administration'}
          </Typography>
          <IconButton>
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>
          <IconButton>
            <Settings />
          </IconButton>
          <Avatar 
            sx={{ ml: 1, width: 32, height: 32, cursor: 'pointer' }}
            alt="Admin User"
            src="/assets/icons/user-avatar.png"
          />
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: isMobile ? 0 : drawerWidth, flexShrink: 0 }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              borderRight: 'none',
            },
          }}
        >
          {renderSidebar()}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              borderRight: 'none',
            },
          }}
          open
        >
          {renderSidebar()}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3 },
          width: { xs: '100%', md: `calc(100% - ${drawerWidth}px)` },
          mt: '64px', // hauteur de l'AppBar
          bgcolor: '#f9f9f9',
          minHeight: 'calc(100vh - 64px)'
        }}
      >
        {renderDashboardContent()}
      </Box>
    </Box>
  );
};

export default AdminDashboard;
