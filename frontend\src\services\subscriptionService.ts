import { getCommonHeaders } from '@/utils/headers';
import { API_CONFIG } from '@/config/api';
import axios from 'axios';
import { fetchCsrfToken } from './csrf';

// Service pour gérer les abonnements
const subscriptionService = {
  // Récupérer la configuration des abonnements
  async getSubscriptionConfig() {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions/`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      // Réduire le niveau de log pour les erreurs 401 (utilisateur non connecté)
      if (error.response?.status === 401) {
        console.info("Configuration des abonnements non disponible (utilisateur non connecté)");
      } else {
        console.error("Erreur lors de la récupération de la configuration des abonnements:", error);
      }
      throw error;
    }
  },

  // Récupérer l'état de l'abonnement de l'utilisateur connecté
  async getSubscriptionStatus() {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions/status`, {
        headers,
        withCredentials: true
      });
      return response.data;
    } catch (error: any) {
      // Réduire le niveau de log pour les erreurs 401 (utilisateur non connecté)
      if (error.response?.status === 401) {
        console.info("Statut d'abonnement non disponible (utilisateur non connecté)");
      } else {
        console.error("Erreur lors de la récupération du statut d'abonnement:", error);
      }
      throw error;
    }
  },

  // Créer une session Stripe Customer Portal
  async createPortalSession() {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/subscriptions/create-portal-session`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la création de la session Stripe:", error);
      throw error;
    }
  },

  // Créer un nouvel abonnement
  async createSubscription(plan: string, options?: Record<string, any>) {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/subscriptions/create-subscription`, {
        plan,
        options
      }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la création de l'abonnement:", error);
      throw error;
    }
  },

  // Réactiver le renouvellement automatique
  async reactivateAutoRenew() {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const response = await axios.post(`${API_CONFIG.baseURL}/api/subscriptions/reactivate-auto-renew`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la réactivation du renouvellement automatique:", error);
      throw error;
    }
  }
};

export default subscriptionService; 