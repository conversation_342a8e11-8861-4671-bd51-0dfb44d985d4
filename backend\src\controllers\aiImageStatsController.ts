import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { decryptProfilDataAsync } from '../utils/encryption';

/**
 * Récupère les statistiques de génération d'images IA
 */
export const getAiImageStats = async (req: Request, res: Response) => {
  try {
    // Vérifier que l'utilisateur est admin ou modérateur
    const userRole = req.user?.role;
    if (!userRole || !['jobpadm', 'jobmodo'].includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé',
        toastType: 'error'
      });
    }

    // Paramètres de filtrage
    const { startDate, endDate, purpose } = req.query;

    // Construire la requête de base
    let query = supabase
      .from('ai_image_generation_stats')
      .select(`
        id,
        user_id,
        purpose,
        prompt,
        cost,
        image_id,
        created_at,
        image_url
      `);

    // Appliquer les filtres
    if (startDate) {
      // On prend le début de la journée
      const start = new Date(startDate as string);
      start.setHours(0, 0, 0, 0);
      query = query.gte('created_at', start.toISOString());
    }

    if (endDate) {
      // On prend la fin de la journée
      const end = new Date(endDate as string);
      end.setHours(23, 59, 59, 999);
      query = query.lte('created_at', end.toISOString());
    }

    if (purpose) {
      query = query.eq('purpose', purpose as string);
    }

    // Exécuter la requête
    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(0, 999);

    if (error) {
      logger.error('Erreur lors de la récupération des statistiques de génération d\'images IA:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques',
        toastType: 'error'
      });
    }

    // DEBUG : log des données récupérées et des filtres
    // logger.info('[AI_IMAGE_STATS] Filtres reçus:', { startDate, endDate, purpose });
    // logger.info('[AI_IMAGE_STATS] Données récentes récupérées:', data);

    // Récupérer les infos utilisateurs (nom, prénom, email) pour chaque user_id unique
    const userIds = Array.from(new Set((data || []).map((g: any) => g.user_id)));
    let userInfos: Record<string, { nom: string; prenom: string; email: string }> = {};
    if (userIds.length > 0) {
      // Récupérer les profils
      const { data: profils, error: profilError } = await supabase
        .from('user_profil')
        .select('user_id, nom, prenom')
        .in('user_id', userIds);
      // Récupérer les emails
      const { data: users, error: userError } = await supabase
        .from('users')
        .select('id, email')
        .in('id', userIds);
      if (!profilError && profils) {
        await Promise.all(profils.map(async (p: any) => {
          // Déchiffrer les données du profil
          const decryptedProfil = await decryptProfilDataAsync(p);
          userInfos[p.user_id] = { ...userInfos[p.user_id], nom: decryptedProfil.nom, prenom: decryptedProfil.prenom };
        }));
      }
      if (!userError && users) {
        users.forEach((u: any) => {
          userInfos[u.id] = { ...userInfos[u.id], email: u.email };
        });
      }
    }
    // Enrichir les générations
    const enrichedData = (data || []).map((g: any) => ({
      ...g,
      nom: userInfos[g.user_id]?.nom || '',
      prenom: userInfos[g.user_id]?.prenom || '',
      email: userInfos[g.user_id]?.email || ''
    }));

    // Récupérer les statistiques agrégées
    // Appliquer les mêmes filtres sur chaque requête d'agrégat
    const getFilteredQuery = (selectFields: string) => {
      let q = supabase.from('ai_image_generation_stats').select(selectFields);
      if (startDate) {
        const start = new Date(startDate as string);
        start.setHours(0, 0, 0, 0);
        q = q.gte('created_at', start.toISOString());
      }
      if (endDate) {
        const end = new Date(endDate as string);
        end.setHours(23, 59, 59, 999);
        q = q.lte('created_at', end.toISOString());
      }
      if (purpose) {
        q = q.eq('purpose', purpose as string);
      }
      return q;
    };

    const [
      totalCountQuery,
      totalCostQuery,
      byPurposeQuery,
      byDateQuery
    ] = await Promise.all([
      // Nombre total de générations (filtré)
      supabase
        .from('ai_image_generation_stats')
        .select('id', { count: 'exact', head: true })
        .gte('created_at', startDate ? new Date(startDate as string).setHours(0,0,0,0) && new Date(startDate as string).toISOString() : '1970-01-01T00:00:00.000Z')
        .lte('created_at', endDate ? new Date(endDate as string).setHours(23,59,59,999) && new Date(endDate as string).toISOString() : new Date().toISOString())
        .eq(purpose ? 'purpose' : '', purpose ? purpose as string : undefined),

      // Coût total (filtré)
      getFilteredQuery('cost')
        .then((result: any) => {
          const data = result.data || [];
          return data.reduce((sum: number, item: any) => sum + (item.cost || 0), 0);
        }),

      // Répartition par type (filtré)
      getFilteredQuery('purpose, id')
        .then((result: any) => {
          const data = result.data || [];
          return data.reduce((acc: Record<string, number>, item: any) => {
            acc[item.purpose] = (acc[item.purpose] || 0) + 1;
            return acc;
          }, {});
        }),

      // Répartition par date (filtré)
      getFilteredQuery('created_at')
        .then((result: any) => {
          const data = result.data || [];
          // Grouper par jour
          const byDate: Record<string, number> = {};
          data.forEach((item: any) => {
            const date = new Date(item.created_at).toISOString().split('T')[0];
            byDate[date] = (byDate[date] || 0) + 1;
          });
          // Convertir en tableau pour le graphique
          return Object.entries(byDate).map(([date, count]) => ({
            date,
            count
          })).sort((a, b) => a.date.localeCompare(b.date));
        })
    ]);

    // Assembler les statistiques
    const stats = {
      totalCount: totalCountQuery.count || 0,
      totalCost: totalCostQuery,
      byPurpose: byPurposeQuery,
      byDate: byDateQuery,
      recentGenerations: enrichedData
    };

    return res.status(200).json({
      success: true,
      stats
    });
  } catch (error: any) {
    logger.error('Erreur lors de la récupération des statistiques de génération d\'images IA:', error);
    return res.status(500).json({
      success: false,
      message: `Erreur lors de la récupération des statistiques: ${error.message}`,
      toastType: 'error'
    });
  }
};
