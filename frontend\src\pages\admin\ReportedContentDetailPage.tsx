import React, { useEffect, useState, useRef } from 'react';
import {
  Box, Container, Typography, Paper, Chip, Button, Grid, CircularProgress, Divider, TextField, Tooltip, Checkbox, FormControlLabel, DialogContentText
} from '@mui/material';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import BlockIcon from '@mui/icons-material/Block';
import DoneIcon from '@mui/icons-material/Done';
import HistoryIcon from '@mui/icons-material/History';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ListIcon from '@mui/icons-material/List';
import axios from 'axios';
import { notify } from '../../components/Notification';
import { getCommonHeaders } from '../../utils/headers';
import { API_CONFIG } from '../../config/api';
import Popover from '@mui/material/Popover';
import ModalPortal from '../../components/ModalPortal';
import { VisibilityIcon } from '@/components/icons';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { format, addDays } from 'date-fns';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../dashboard/services/types';
import WorkOutlineIcon from '@mui/icons-material/WorkOutline';
import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';

const STATUS_COLORS: Record<string, string> = {
  'pending': '#FFA500',         // orange
  'in_review': '#1976d2',      // bleu
  'validated': '#4CAF50',      // vert
  'rejected': '#BDBDBD',       // gris
  'content_deleted': '#FF6B2C', // rouge/orange vif
  'masqué': '#FF6B2C',         // orange vif pour masqué
  'restauré': '#4FC3F7',        // bleu clair pour restauré
};

const TYPE_LABELS: Record<string, string> = {
  'comment': 'Commentaire',
  'message': 'Message',
  'review': 'Avis',
  'mission': 'Mission',
  'profile': 'Profil',
};

const STATUS_LABELS: Record<string, string> = {
  'pending': 'En attente',
  'in_review': 'En cours',
  'validated': 'Validé',
  'rejected': 'Rejeté',
  'content_deleted': 'Contenu supprimé',
  'masqué': 'Masqué',
  'restauré': 'Restauré',
};

// Ajout d'un mapping pour les labels d'action
const ACTION_LABELS: Record<string, string> = {
  mask: 'Masqué',
  delete: 'Supprimé',
  validated: 'Validé',
  in_review: 'En cours',
  pending: 'En attente',
  rejected: 'Rejeté',
  content_deleted: 'Contenu supprimé',
  restauré: 'Restauré',
};

// Texte explicite pour la modale selon l'action
const ACTION_CONFIRM_TEXT: Record<string, string | ((report?: any) => string)> = {
  validated: (report?: any) => {
    if (report && report.content_type === 'profile') {
      return "Valider le signalement : le profil va être masqué aux autres utilisateurs.";
    }
    if (report && report.content_type === 'mission') {
      return "Valider le signalement : la mission va être supprimée définitivement, elle ne pourra pas être restaurée.";
    }
    if (report && report.content_type === 'review') {
      return "Valider le signalement : l'avis va être supprimé.";
    }
    return "Valider le signalement : le contenu va être masqué.";
  },
  rejected: "Rejeter le signalement : le contenu original sera restauré (si masqué).",
  delete: "Valider le signalement : le contenu va être supprimé définitivement, il ne pourra pas être restauré.",
  mask: "Masquer le contenu signalé.",
  traité: "Marquer comme traité : le contenu original sera restauré (si masqué)."
};

// Fonction utilitaire pour supprimer les balises HTML
function stripHtml(html: string): string {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&');
}

// Fonction utilitaire pour obtenir le texte de confirmation d'action
function getActionConfirmText(action: string | undefined, report: any): string {
  if (!action) return '';
  const value = ACTION_CONFIRM_TEXT[action];
  if (typeof value === 'function') {
    return value(report);
  }
  return value || '';
}

// Composant pour l'historique des échanges internes staff
const InternalNotesThread: React.FC<{ reportId: string, internalNote: string, refresh: () => void }> = ({ reportId, internalNote, refresh }) => {
  const [notes, setNotes] = useState<any[]>([]);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      const arr = JSON.parse(internalNote || '[]');
      setNotes(Array.isArray(arr) ? arr : []);
    } catch {
      setNotes([]);
    }
  }, [internalNote]);

  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [notes]);

  const handleSend = async () => {
    if (!message.trim()) return;
    setLoading(true);
    try {
      const headers = await getCommonHeaders();
      const { fetchCsrfToken } = await import('../../services/csrf');
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      await axios.patch(`${API_CONFIG.baseURL}/api/reported-content/${reportId}/internal-note`, { message }, { headers, withCredentials: true });
      setMessage('');
      refresh();
    } catch (e: any) {
      notify(e?.response?.data?.error || "Erreur lors de l'envoi de la note", 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ width: '100%', mb: 3, mt: 4 }}>
      <Paper sx={{
        p: { xs: 2, sm: 3 },
        bgcolor: '#FFF8F3',
        border: '2px solid #FF6B2C',
        borderRadius: 4,
        boxShadow: '0 4px 16px 0 rgba(255,107,44,0.08)',
        width: '100%',
        maxWidth: '100%',
        mx: 'auto',
      }}>
        <Typography variant="h6" align="center" sx={{ color: '#FF6B2C', fontWeight: 800, mb: 2, letterSpacing: 0.5 }}>
          Échanges internes staff (max 20 derniers)
        </Typography>
        <Box ref={messagesContainerRef} sx={{ maxHeight: 300, overflowY: 'auto', mb: 2, pr: 1 }}>
          {notes.length === 0 && <Typography color="text.secondary" align="center">Aucun échange pour l'instant.</Typography>}
          {notes.slice().reverse().map((n, idx) => (
            <Box key={idx} sx={{
              mb: 2,
              p: 1.5,
              bgcolor: '#FFF',
              borderRadius: 3,
              border: '1.5px solid #FFE4BA',
              boxShadow: '0 2px 8px 0 rgba(255,107,44,0.06)',
              maxWidth: 600,
              mx: 'auto',
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                <span style={{ color: '#FF6B2C', fontWeight: 700, fontSize: 15 }}>{n.author_name || 'Staff'}</span>
                <span style={{ color: '#BDBDBD', fontSize: 13 }}>{n.date ? new Date(n.date).toLocaleString('fr-FR', { hour: '2-digit', minute: '2-digit', second: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric' }) : ''}</span>
              </Box>
              <Typography variant="body2" sx={{ color: '#222', fontSize: 15, whiteSpace: 'pre-line', ml: 0.5 }}>{n.message}</Typography>
            </Box>
          ))}
        </Box>
        <Box sx={{ display: 'flex', gap: 1.5, alignItems: 'flex-end', mt: 2, maxWidth: 600, mx: 'auto' }}>
          <TextField
            multiline
            minRows={1}
            maxRows={4}
            fullWidth
            value={message}
            onChange={e => setMessage(e.target.value)}
            placeholder="Ajouter un message interne..."
            variant="outlined"
            disabled={loading}
            sx={{ bgcolor: 'white', borderRadius: 2 }}
          />
          <Button variant="contained" sx={{ backgroundColor: '#FF6B2C', color: 'white', fontWeight: 700, minWidth: 120, borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(255,107,44,0.10)' }} onClick={handleSend} disabled={loading || !message.trim()}>
            Envoyer
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

const ReportedContentDetailPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [adminComment, setAdminComment] = useState('');
  const [history, setHistory] = useState<any[]>([]);
  // Popover d'aide pour chaque bouton
  const [anchorEl1, setAnchorEl1] = useState<null | HTMLElement>(null);
  const [anchorEl2, setAnchorEl2] = useState<null | HTMLElement>(null);
  const [anchorEl3, setAnchorEl3] = useState<null | HTMLElement>(null);
  const open1 = Boolean(anchorEl1);
  const open2 = Boolean(anchorEl2);
  const open3 = Boolean(anchorEl3);
  // Restauration d'un commentaire masqué
  // Liste des signalements individuels
  const [individualReports, setIndividualReports] = useState<any[]>([]);
  const [loadingReports, setLoadingReports] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [pendingAction, setPendingAction] = useState<null | 'mask' | 'delete' | 'traité' | 'validated' | 'rejected'>(null);
  const [notifyReporters, setNotifyReporters] = useState(true);
  const [notifyAuthorByEmail, setNotifyAuthorByEmail] = useState(() => {
    // Par défaut : true pour les avis, profils et missions, sinon false
    if (report && (report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review')) {
      return true;
    }
    return false;
  });
  // Pour la modal des signalements reçus par l'utilisateur signalé
  const [userReportsModalOpen, setUserReportsModalOpen] = useState(false);
  const [userReports, setUserReports] = useState<any[]>([]);
  const [loadingUserReports, setLoadingUserReports] = useState(false);
  // Pour le popover d'affichage du contenu signalé complet
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [popoverContent, setPopoverContent] = useState<string>('');
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [suspensionReason, setSuspensionReason] = useState('');
  const [suspensionType, setSuspensionType] = useState<'temporaire' | 'definitive'>('temporaire');
  const [suspendedUntil, setSuspendedUntil] = useState<string>('');
  const [suspendLoading, setSuspendLoading] = useState(false);
  const [suspendError, setSuspendError] = useState<string | null>(null);
  const [reactivateDialogOpen, setReactivateDialogOpen] = useState(false);
  const [userExtra, setUserExtra] = useState<{ ville?: string, galleryFolders?: any[], services?: any[] }>({});
  // Après le champ adminComment, ajouter un état pour la note interne :
  const [internalNote, setInternalNote] = useState('');

  // Remplir la date par défaut à J+7 quand on passe en temporaire
  useEffect(() => {
    if (suspensionType === 'temporaire' && !suspendedUntil) {
      const defaultDate = format(addDays(new Date(), 7), 'yyyy-MM-dd');
      setSuspendedUntil(defaultDate);
    }
  }, [suspensionType]);

  // Récupérer le signalement
  const fetchReport = async () => {
    setLoading(true);
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/${id}`, {
        headers,
        withCredentials: true
      });
      setReport(res.data);
      setAdminComment(res.data.admin_comment || '');
      setInternalNote(res.data.internal_note || '');
    } catch (e) {
      setReport(null);
    } finally {
      setLoading(false);
    }
  };

  // Récupérer l'historique
  const fetchHistory = async () => {
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/${id}/history`, {
        headers,
        withCredentials: true
      });
      setHistory(res.data || []);
    } catch (e) {
      setHistory([]);
    }
  };

  // Récupérer la liste des signalements individuels
  const fetchIndividualReports = async () => {
    setLoadingReports(true);
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/${id}/reports`, {
        headers,
        withCredentials: true
      });
      setIndividualReports(res.data || []);
    } catch (e) {
      setIndividualReports([]);
    } finally {
      setLoadingReports(false);
    }
  };

  // Récupérer les signalements déjà reçus par l'utilisateur signalé
  const fetchUserReports = async (userId: string) => {
    setLoadingUserReports(true);
    try {
      const headers = await getCommonHeaders();
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content?reported_by=${userId}`, {
        headers,
        withCredentials: true
      });
      setUserReports(res.data?.data || []);
    } catch (e) {
      setUserReports([]);
    } finally {
      setLoadingUserReports(false);
    }
  };

  useEffect(() => {
    fetchReport();
    fetchHistory();
    fetchIndividualReports();
    // eslint-disable-next-line
  }, [id]);

  useEffect(() => {
    const fetchUserExtra = async () => {
      if (report?.user?.profilSlug) {
        try {
          const res = await axios.get(`${API_CONFIG.baseURL}/api/users/profil/${report.user.profilSlug}`);
          setUserExtra({
            ville: res.data?.profil?.data?.ville,
            galleryFolders: res.data?.galleryFolders,
            services: res.data?.services,
          });
        } catch (e) {
          setUserExtra({});
        }
      }
    };
    fetchUserExtra();
  }, [report?.user?.profilSlug]);

  // Actions admin
  const handleAction = async (action: 'mask' | 'delete' | 'traité' | 'validated' | 'rejected') => {
    setPendingAction(action);
    setConfirmDialogOpen(true);
  };

  const handleConfirmAction = async () => {
    if (!pendingAction) return;
    setActionLoading(true);
    try {
      const headers = await getCommonHeaders();
      const { fetchCsrfToken } = await import('../../services/csrf');
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      // Déterminer le nouveau statut selon l'action
      let status = '';
      if (pendingAction === 'mask') status = 'masqué';
      else if (pendingAction === 'delete') status = 'content_deleted';
      else if (pendingAction === 'traité') status = 'validated';
      else if (pendingAction === 'rejected') status = 'rejected';
      else if (pendingAction === 'validated') status = 'validated';

      await axios.post(`${API_CONFIG.baseURL}/api/reported-content/${id}/moderate`, {
        action: pendingAction,
        status,
        admin_comment: adminComment,
        notifyReporters,
        notifyAuthorByEmail: report.content_type === 'profile' ? true : notifyAuthorByEmail,
      }, { headers, withCredentials: true });
      await fetchReport();
      await fetchHistory();
      await fetchIndividualReports();
      notify('Action réalisée avec succès', 'success');
    } catch (e: any) {
      notify(e?.response?.data?.error || "Erreur lors de l'action", 'error');
    } finally {
      setActionLoading(false);
      setConfirmDialogOpen(false);
      setPendingAction(null);
    }
  };

  // Fonction pour suspendre l'utilisateur
  const handleSuspendUser = async () => {
    if (!report?.user?.id) return;
    setSuspendLoading(true);
    setSuspendError(null);
    try {
      const headers = await getCommonHeaders();
      const { fetchCsrfToken } = await import('../../services/csrf');
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      const profil_actif = false;
      const data: any = {
        userId: report.user.id,
        profil_actif,
        suspension_reason: suspensionReason,
        suspended_until: suspensionType === 'temporaire' && suspendedUntil ? suspendedUntil : null,
      };
      const res = await axios.post(`${API_CONFIG.baseURL}/api/users/admin/suspend-user`, data, { headers, withCredentials: true });
      if (res.data.success) {
        notify('Utilisateur suspendu/banni avec succès', 'success');
        setSuspendDialogOpen(false);
        fetchReport();
      } else {
        setSuspendError(res.data.message || 'Erreur lors de la suspension');
      }
    } catch (e: any) {
      setSuspendError(e?.response?.data?.message || 'Erreur lors de la suspension');
    } finally {
      setSuspendLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress sx={{ color: '#FF6B2C' }} />
      </Box>
    );
  }
  if (!report) {
    return (
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="error">Signalement introuvable</Typography>
          <Button variant="contained" sx={{ mt: 2, bgcolor: '#FF6B2C' }} onClick={() => navigate(-1)}>
            Retour
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Box sx={{ background: 'linear-gradient(to bottom, #FFF8F3, white)', minHeight: '100vh', py: 4 }}>
      <Container maxWidth="md">
        <Button
          component={Link}
          to="/admin/reported-content"
          startIcon={<ArrowBackIcon />}
          sx={{ mb: 2, color: '#FF6B2C', fontWeight: 600 }}
        >
          Retour à la liste
        </Button>
        <Paper sx={{ p: 3, borderRadius: 3, mb: 3 }}>
          {/* Bloc infos utilisateur signalé */}
          {report.user && (
            <Paper sx={{ p: 2, mb: 3, bgcolor: '#FFF8F3', border: '1.5px solid #FFE4BA', borderRadius: 2 }}>
              <Typography variant="subtitle1" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>
                Informations de l'utilisateur signalé
              </Typography>
              <Grid container spacing={1}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Nom&nbsp;:</b> {report.user.user_profil?.[0]?.nom || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Prénom&nbsp;:</b> {report.user.user_profil?.[0]?.prenom || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Email&nbsp;:</b> {report.user.email || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Téléphone&nbsp;:</b> {report.user.telephone || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Ville&nbsp;:</b> {userExtra.ville || report.user.user_profil?.[0]?.ville || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Rôle&nbsp;:</b> {report.user.role || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Type de compte&nbsp;:</b> {report.user.user_type || '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Date d'inscription&nbsp;:</b> {report.user.date_inscription ? new Date(report.user.date_inscription).toLocaleDateString('fr-FR') : '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Dernière connexion&nbsp;:</b> {report.user.last_login ? new Date(report.user.last_login).toLocaleString('fr-FR') : '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Premium&nbsp;:</b> {(report.user.user_abo && report.user.user_abo.length > 0 && report.user.user_abo[0].statut === 'actif' && report.user.user_abo[0].type_abonnement === 'premium') ? 'Oui' : 'Non'}
                </Grid>
                {report.user.user_abo && report.user.user_abo.length > 0 && (
                  <>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <b>Type d'abonnement&nbsp;:</b> {report.user.user_abo[0].type_abonnement}
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <b>Montant&nbsp;:</b> {report.user.user_abo[0].montant ? report.user.user_abo[0].montant + ' €' : '-'}
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <b>Début&nbsp;:</b> {report.user.user_abo[0].date_debut ? new Date(report.user.user_abo[0].date_debut).toLocaleDateString('fr-FR') : '-'}
                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <b>Fin&nbsp;:</b> {report.user.user_abo[0].date_fin ? new Date(report.user.user_abo[0].date_fin).toLocaleDateString('fr-FR') : '-'}
                    </Grid>
                  </>
                )}

                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Nombre de galeries&nbsp;:</b> {userExtra.galleryFolders ? userExtra.galleryFolders.length : '-'}
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Nombre de services&nbsp;:</b> {userExtra.services ? userExtra.services.length : '-'}
                </Grid>
                {/* Affichage simple des galeries */}
                {userExtra.galleryFolders && userExtra.galleryFolders.length > 0 && (
                  <Grid size={{ xs: 12 }}>
                    <b>Galeries :</b>
                    <ul style={{ margin: 0, paddingLeft: 18 }}>
                      {userExtra.galleryFolders.map((g: any) => (
                        <li key={g.id}>{g.name} ({g.imagesCount || 0} photos)</li>
                      ))}
                    </ul>
                  </Grid>
                )}
                {/* Affichage simple des services */}
                {userExtra.services && userExtra.services.length > 0 && (
                  <Grid size={{ xs: 12 }}>
                    <b>Services proposés :</b>
                    <ul style={{ margin: 0, paddingLeft: 18 }}>
                      {userExtra.services.map((s: any) => (
                        <li key={s.id}>{s.titre} ({s.tarif_horaire ? s.tarif_horaire + ' €/h' : 'Tarif non renseigné'})</li>
                      ))}
                    </ul>
                  </Grid>
                )}


                <Grid size={{ xs: 12 }}>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                    <span style={{ fontWeight: 600, color: '#FF6B2C', marginRight: 8 }}>Vérifications&nbsp;:</span>
                    <span style={{
                      background: report.user.profil_verifier ? '#4CAF50' : '#FFB300',
                      color: 'white', borderRadius: 8, padding: '2px 10px', fontWeight: 700, fontSize: 13
                    }}>Profil {report.user.profil_verifier ? '✔' : '✖'}</span>
                    <span style={{
                      background: report.user.identite_verifier ? '#4CAF50' : '#FFB300',
                      color: 'white', borderRadius: 8, padding: '2px 10px', fontWeight: 700, fontSize: 13
                    }}>Identité {report.user.identite_verifier ? '✔' : '✖'}</span>
                    <span style={{
                      background: report.user.entreprise_verifier ? '#4CAF50' : '#FFB300',
                      color: 'white', borderRadius: 8, padding: '2px 10px', fontWeight: 700, fontSize: 13
                    }}>Entreprise {report.user.entreprise_verifier ? '✔' : '✖'}</span>
                    <span style={{
                      background: report.user.assurance_verifier ? '#4CAF50' : '#FFB300',
                      color: 'white', borderRadius: 8, padding: '2px 10px', fontWeight: 700, fontSize: 13
                    }}>Assurance {report.user.assurance_verifier ? '✔' : '✖'}</span>
                  </Box>
                </Grid>
                {/* Lien profil public juste après vérifications */}
                {report.user.profilSlug && (
                  <Grid size={{ xs: 12 }}>
                    <Box sx={{ mt: 1, textAlign: 'left' }}>
                      <a
                        href={`/dashboard/profil/${report.user.profilSlug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: '#FF6B2C', textDecoration: 'underline', fontWeight: 600 }}
                      >
                        Voir le profil public
                      </a>
                    </Box>
                  </Grid>
                )}
                <Grid size={{ xs: 12, md: 6 }}>
                  <b>Signalements reçus&nbsp;:</b>{' '}
                  <span
                    style={{ color: '#FF6B2C', fontWeight: 700, cursor: report.user.reportCount > 0 ? 'pointer' : 'default', textDecoration: report.user.reportCount > 0 ? 'underline' : 'none' }}
                    onClick={() => {
                      if (report.user.reportCount > 0) {
                        fetchUserReports(report.user.id);
                        setUserReportsModalOpen(true);
                      }
                    }}
                  >
                    {report.user.reportCount ?? '-'}
                  </span>
                </Grid>
                {/* Bouton suspendre/banir */}
                <Box sx={{ width: '100%', mt: 3, display: 'flex', justifyContent: 'center' }}>
                  {report.user.profil_actif ? (
                    <Button
                      variant="contained"
                      color="error"
                      startIcon={<BlockIcon />}
                      sx={{
                        backgroundColor: '#FF3B2C',
                        color: 'white',
                        fontWeight: 700,
                        borderRadius: 2,
                        width: { xs: '100%', sm: 400 },
                        fontSize: 16,
                        py: 1.1,
                        boxShadow: '0 2px 8px 0 rgba(255,59,44,0.08)',
                        '&:hover': { backgroundColor: '#D32F2F' }
                      }}
                      onClick={() => setSuspendDialogOpen(true)}
                    >
                      Suspendre / Bannir l'utilisateur
                    </Button>
                  ) : (
                    <>
                      <Button
                        variant="contained"
                        color="success"
                        startIcon={<DoneIcon />}
                        sx={{
                          backgroundColor: '#4CAF50',
                          color: 'white',
                          fontWeight: 700,
                          borderRadius: 2,
                          width: { xs: '100%', sm: 400 },
                          fontSize: 16,
                          py: 1.1,
                          boxShadow: '0 2px 8px 0 rgba(76,175,80,0.08)',
                          '&:hover': { backgroundColor: '#388E3C' }
                        }}
                        onClick={() => setReactivateDialogOpen(true)}
                        disabled={suspendLoading}
                      >
                        Réactiver l'utilisateur
                      </Button>
                      {/* Modale de confirmation de réactivation */}
                      <Dialog open={reactivateDialogOpen} onClose={() => setReactivateDialogOpen(false)} maxWidth="xs" fullWidth>
                        <DialogTitle sx={{ color: '#4CAF50', fontWeight: 700 }}>Confirmer la réactivation</DialogTitle>
                        <DialogContent>
                          <Typography variant="body2" sx={{ mb: 2, color: '#222' }}>
                            Êtes-vous sûr de vouloir réactiver cet utilisateur ? Il pourra à nouveau accéder à la plateforme.
                          </Typography>
                          {suspendError && <Typography color="error" sx={{ mb: 1 }}>{suspendError}</Typography>}
                        </DialogContent>
                        <DialogActions>
                          <Button onClick={() => setReactivateDialogOpen(false)} color="inherit" sx={{ fontWeight: 600 }}>Annuler</Button>
                          <Button
                            onClick={async () => {
                              setSuspendLoading(true);
                              setSuspendError(null);
                              try {
                                const headers = await getCommonHeaders();
                                const { fetchCsrfToken } = await import('../../services/csrf');
                                headers['X-CSRF-Token'] = await fetchCsrfToken();
                                const data = {
                                  userId: report.user.id,
                                  profil_actif: true
                                };
                                const res = await axios.post(`${API_CONFIG.baseURL}/api/users/admin/suspend-user`, data, { headers, withCredentials: true });
                                if (res.data.success) {
                                  notify('Utilisateur réactivé avec succès', 'success');
                                  fetchReport();
                                  setReactivateDialogOpen(false);
                                } else {
                                  setSuspendError(res.data.message || 'Erreur lors de la réactivation');
                                }
                              } catch (e: any) {
                                setSuspendError(e?.response?.data?.message || 'Erreur lors de la réactivation');
                              } finally {
                                setSuspendLoading(false);
                              }
                            }}
                            color="success"
                            variant="contained"
                            sx={{ fontWeight: 700, bgcolor: '#4CAF50' }}
                            disabled={suspendLoading}
                          >
                            {suspendLoading ? <CircularProgress size={18} sx={{ color: 'white' }} /> : 'Confirmer'}
                          </Button>
                        </DialogActions>
                      </Dialog>
                    </>
                  )}
                </Box>
              </Grid>
              {/* Modale de suspension */}
              <Dialog open={suspendDialogOpen} onClose={() => setSuspendDialogOpen(false)} maxWidth="xs" fullWidth>
                <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700 }}>Suspendre / Bannir l'utilisateur</DialogTitle>
                <DialogContent>
                  <Typography variant="body2" sx={{ mb: 2, color: '#222' }}>
                    Choisissez le type de suspension et précisez la raison. L'utilisateur sera désactivé immédiatement.
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <FormControlLabel
                      control={<Checkbox checked={suspensionType === 'definitive'} onChange={e => setSuspensionType(e.target.checked ? 'definitive' : 'temporaire')} />}
                      label={<span style={{ color: '#FF6B2C', fontWeight: 600 }}>Suspension définitive</span>}
                    />
                  </Box>
                  {suspensionType === 'temporaire' && (
                    <TextField
                      label="Date de fin de suspension"
                      type="date"
                      fullWidth
                      value={suspendedUntil}
                      onChange={e => setSuspendedUntil(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                      sx={{ mb: 2 }}
                      inputProps={{ min: format(new Date(), 'yyyy-MM-dd') }}
                    />
                  )}
                  {/* Réponses préconçues pour la raison de suspension (liste déroulante simple, résumé court, message long, selon type, sans label pour éviter la superposition) */}
                  <Box sx={{ mb: 1 }}>
                    <TextField
                      select
                      value={suspensionReason}
                      onChange={e => setSuspensionReason(e.target.value)}
                      fullWidth
                      SelectProps={{ native: true }}
                      sx={{ mb: 1, color: '#222',
                        '& .MuiInputBase-input': { color: '#222' },
                        '& .MuiInputLabel-root': { color: '#222' },
                        '& .MuiSelect-icon': { color: '#222' }
                      }}
                    >
                      <option value="" disabled>Choisir une raison prédéfinie...</option>
                      {suspensionType === 'definitive' ? (
                        <>
                          <option value="Votre compte a été définitivement banni pour violation grave et répétée des règles de la plateforme. Cette décision est irrévocable.">Violation grave et répétée des règles</option>
                          <option value="Votre compte a été définitivement supprimé suite à des actes de fraude, d'arnaque ou d'usurpation d'identité avérés.">Fraude, arnaque ou usurpation d'identité</option>
                          <option value="Votre compte a été définitivement banni après plusieurs avertissements et suspensions temporaires restés sans effet.">Récidive malgré avertissements</option>
                          <option value="Votre compte a été supprimé pour publication répétée de contenus interdits ou dangereux.">Contenus interdits ou dangereux</option>
                          <option value="Votre compte a été supprimé à votre demande ou suite à une demande explicite de votre part.">Suppression à la demande de l'utilisateur</option>
                          <option value="Autre raison (merci de préciser dans le champ ci-dessous)">Autre raison</option>
                        </>
                      ) : (
                        <>
                          <option value="Votre compte a été temporairement suspendu pour non-respect des règles de la plateforme. Merci de prendre connaissance des conditions d'utilisation.">Non-respect des règles de la plateforme</option>
                          <option value="Votre compte a été temporairement suspendu pour comportement inapproprié ou irrespectueux envers d'autres membres. Nous vous invitons à adopter une attitude respectueuse.">Comportement inapproprié envers d'autres utilisateurs</option>
                          <option value="Votre compte a été temporairement suspendu pour publication de contenu interdit ou inadapté. Merci de respecter les règles de publication.">Publication de contenu interdit</option>
                          <option value="Votre compte a été temporairement suspendu suite à une suspicion de fraude ou d'activité non autorisée. Merci de contacter le support si vous pensez qu'il s'agit d'une erreur.">Suspicion de fraude ou d'arnaque</option>
                          <option value="Votre compte a été temporairement suspendu suite à des signalements répétés par la communauté. Une vérification approfondie est en cours.">Signalements répétés par la communauté</option>
                          <option value="Suspension effectuée à votre demande ou suite à une demande explicite de votre part.">Suspension à la demande de l'utilisateur</option>
                          <option value="Autre raison (merci de préciser dans le champ ci-dessous)">Autre raison</option>
                        </>
                      )}
                    </TextField>
                  </Box>
                  <TextField
                    label="Raison de la suspension (visible par l'utilisateur)"
                    fullWidth
                    multiline
                    minRows={2}
                    value={suspensionReason}
                    onChange={e => setSuspensionReason(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  {suspendError && <Typography color="error" sx={{ mb: 1 }}>{suspendError}</Typography>}
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setSuspendDialogOpen(false)} color="inherit" sx={{ fontWeight: 600 }}>Annuler</Button>
                  <Button
                    onClick={handleSuspendUser}
                    color="error"
                    variant="contained"
                    sx={{ fontWeight: 700, bgcolor: '#FF3B2C' }}
                    disabled={suspendLoading || (suspensionType === 'temporaire' && !suspendedUntil)}
                  >
                    {suspendLoading ? <CircularProgress size={18} sx={{ color: 'white' }} /> : 'Confirmer'}
                  </Button>
                </DialogActions>
              </Dialog>
            </Paper>
          )}
          <Typography variant="h5" sx={{ fontWeight: 700, color: '#FF6B2C', mb: 2 }}>
            Détail du signalement
          </Typography>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" color="#FF6B2C">Type de contenu signalé</Typography>
              <Chip label={TYPE_LABELS[report.content_type] || report.content_type} sx={{ bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 600, mb: 1 }} />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" color="#FF6B2C">Statut actuel du signalement</Typography>
              <Chip label={STATUS_LABELS[report.status] || report.status} sx={{ bgcolor: STATUS_COLORS[report.status] || '#E0E0E0', color: 'white', fontWeight: 600, mb: 1 }} />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" color="#FF6B2C">ID du contenu</Typography>
              <Tooltip title={report.content_id}><span>{report.content_id}</span></Tooltip>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                <Typography variant="subtitle2" color="#FF6B2C">Signalé par</Typography>
                <Chip
                  label={`${individualReports.length} utilisateurs`}
                  size="small"
                  sx={{ bgcolor: '#FF6B2C', color: 'white', fontWeight: 700, cursor: 'pointer' }}
                  onClick={() => individualReports.length > 0 && setModalOpen(true)}
                />
              </Box>
              {loadingReports ? (
                <span style={{ color: '#FF6B2C' }}>Chargement...</span>
              ) : individualReports.length === 0 ? (
                <span>Aucun signalement individuel</span>
              ) : null}
              {/* Modal des utilisateurs ayant signalé */}
              <ModalPortal isOpen={modalOpen} onBackdropClick={() => setModalOpen(false)}>
                <div style={{
                  background: 'white',
                  borderRadius: 18,
                  minWidth: 400,
                  maxWidth: 650,
                  boxShadow: '0 8px 32px 0 rgba(255,107,44,0.10)',
                  padding: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'stretch',
                  overflow: 'hidden'
                }}>
                  {/* En-tête sobre */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, bgcolor: '#FAFAFA', px: 4, py: 2.5, borderBottom: '1px solid #E0E0E0' }}>
                    <ListIcon sx={{ color: '#FF6B2C', fontSize: 24 }} />
                    <Typography variant="h6" sx={{ color: '#222', fontWeight: 700, flex: 1 }}>
                      Utilisateurs ayant signalé
                    </Typography>
                    <Chip label={`${individualReports.length}`} size="small" sx={{ bgcolor: '#F3F3F3', color: '#222', fontWeight: 700, fontSize: 15 }} />
                  </Box>
                  {/* Table */}
                  <div style={{ maxHeight: 340, overflowY: 'auto', background: '#FFF', padding: 0 }}>
                    {individualReports.length === 0 ? (
                      <Typography sx={{ p: 4 }}>Aucun signalement.</Typography>
                    ) : (
                      <Box sx={{ width: '100%', minWidth: 'max-content' }}>
                        <Box sx={{ display: 'flex', fontWeight: 700, gap: 2, color: '#FF6B2C', fontSize: 15, px: 3, pt: 2, pb: 1 }}>
                          <span style={{ minWidth: 38 }}></span>
                          <span style={{ minWidth: 110 }}>Nom</span>
                          <span style={{ minWidth: 110 }}>Prénom</span>
                          <span style={{ minWidth: 180 }}>Email</span>
                          <span style={{ minWidth: 100 }}>Téléphone</span>
                          <span style={{ minWidth: 140 }}>Raison</span>
                          <span style={{ minWidth: 110 }}>Date</span>
                          <span style={{ minWidth: 120 }}>User ID</span>
                        </Box>
                        <Divider sx={{ mb: 1, mx: 2, borderColor: '#E0E0E0' }} />
                        {individualReports.map((r, idx) => {
                          const bg = idx % 2 === 0 ? '#FFF' : '#F7F7F7';
                          const initials = `${(r.prenom || '-')[0] || ''}${(r.nom || '-')[0] || ''}`.toUpperCase();
                          return (
                            <Box
                              key={r.id || idx}
                              sx={{
                                display: 'flex', alignItems: 'center', gap: 2, fontSize: 15, px: 3, py: 1.2,
                                background: bg,
                                borderRadius: 2,
                                transition: 'background 0.2s',
                                '&:hover': { background: '#F3F3F3' },
                                width: '100%'
                              }}
                            >
                              <Box sx={{ width: 28, height: 28, bgcolor: '#F3F3F3', color: '#666', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: 13, mr: 1 }}>
                                {initials}
                              </Box>
                              <span style={{ minWidth: 110 }}>{r.nom || '-'}</span>
                              <span style={{ minWidth: 110 }}>{r.prenom || '-'}</span>
                              <span style={{ minWidth: 180 }}>{r.email || '-'}</span>
                              <span style={{ minWidth: 100 }}>{r.telephone || '-'}</span>
                              <span style={{ minWidth: 140 }}>{r.reason || 'Non précisée'}</span>
                              <span style={{ minWidth: 110 }}>{r.created_at ? new Date(r.created_at).toLocaleString('fr-FR', { day: '2-digit', month: '2-digit', year: '2-digit', hour: '2-digit', minute: '2-digit' }) : '-'}</span>
                              <span style={{ minWidth: 120, color: '#BDBDBD', fontFamily: 'monospace' }}>{r.user_id || '-'}</span>
                            </Box>
                          );
                        })}
                      </Box>
                    )}
                  </div>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 12, p: 2.5, bgcolor: '#FAFAFA', borderTop: '1px solid #E0E0E0' }}>
                    <Button onClick={() => setModalOpen(false)} sx={{ fontWeight: 700, color: 'white', bgcolor: '#FF6B2C', px: 3, borderRadius: 2, '&:hover': { bgcolor: '#FF7A35' } }}>
                      Fermer
                    </Button>
                  </Box>
                </div>
              </ModalPortal>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" color="#FF6B2C">Raison du signalement</Typography>
              <span>{report.reason}</span>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" color="#FF6B2C">Date</Typography>
              <span>{new Date(report.created_at).toLocaleString('fr-FR')}</span>
            </Grid>
            {report.admin_comment && (
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" color="#FF6B2C">Commentaire admin</Typography>
                <Paper sx={{ p: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2 }}>{report.admin_comment}</Paper>
              </Grid>
            )}
            {/* Ajouter une fonction pour enregistrer la note interne : */}
            <InternalNotesThread reportId={report.id} internalNote={report.internal_note} refresh={fetchReport} />
          </Grid>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2, mt: 4 }}>Aperçu du contenu signalé</Typography>
          {/* Aperçu dynamique selon le type de contenu */}
          <Paper sx={{ p: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2, mb: 2 }}>
            <ContentPreview report={report} />
          </Paper>
          <Divider sx={{ my: 3 }} />
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2 }}>Actions d'administration</Typography>
          {/* Zone de texte admin */}
          <TextField
            label="Commentaire (optionnel)"
            fullWidth
            multiline
            minRows={2}
            value={adminComment}
            onChange={e => setAdminComment(e.target.value)}
            sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: '0 1px 6px 0 rgba(255,107,44,0.04)', mb: 2 }}
          />
          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 2.5,
            width: '100%',
            justifyContent: { xs: 'flex-start', sm: 'flex-start' },
            alignItems: 'stretch',
            background: 'linear-gradient(90deg, #FFF8F3 60%, #FFF3E0 100%)',
            border: '1.5px solid #FFE4BA',
            borderRadius: 3,
            boxShadow: '0 4px 24px 0 rgba(255,107,44,0.07)',
            p: { xs: 2, sm: 3 },
            mb: 3,
            mt: 2
          }}>
            {/* Valider */}
            {(report.status !== 'validated' && report.status !== 'masqué' && report.status !== 'content_deleted') && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(76, 175, 80, 0.07)', p: 1.2, minWidth: 0 }}>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<DoneIcon />}
                  disabled={actionLoading}
                  onClick={() => handleAction('validated')}
                  sx={{ bgcolor: '#4CAF50', color: 'white', fontWeight: 600, minWidth: 120, boxShadow: 'none' }}
                >
                  Valider
                </Button>
                <HelpOutlineIcon sx={{ color: '#4CAF50', cursor: 'pointer' }} fontSize="small"
                  onClick={e => setAnchorEl3(e.currentTarget as unknown as HTMLElement)}
                />
                <Popover
                  open={open3}
                  anchorEl={anchorEl3}
                  onClose={() => setAnchorEl3(null)}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                >
                  <Box sx={{ p: 2, maxWidth: 260 }}>
                    {getActionConfirmText('validated', report)}
                  </Box>
                </Popover>
              </Box>
            )}
            {/* Rejeter */}
            {(report.status !== 'rejected' && report.status !== 'content_deleted') && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(189, 189, 189, 0.07)', p: 1.2, minWidth: 0 }}>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#FF6B2C',
                    color: 'white',
                    fontWeight: 600,
                    minWidth: 120,
                    boxShadow: 'none',
                    '&:hover': { bgcolor: '#FF3B2C' }
                  }}
                  startIcon={<BlockIcon />}
                  disabled={actionLoading}
                  onClick={() => handleAction('rejected')}
                >
                  Rejeter
                </Button>
                <HelpOutlineIcon sx={{ color: '#BDBDBD', cursor: 'pointer' }} fontSize="small"
                  onClick={e => setAnchorEl1(e.currentTarget as unknown as HTMLElement)}
                />
                <Popover
                  open={open1}
                  anchorEl={anchorEl1}
                  onClose={() => setAnchorEl1(null)}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                >
                  <Box sx={{ p: 2, maxWidth: 260 }}>
                    {getActionConfirmText('rejected', report)}
                  </Box>
                </Popover>
              </Box>
            )}
            {/* Supprimer */}
            {(report.content_type !== 'profile' && report.content_type !== 'mission' && report.content_type !== 'review' && report.content_type !== 'message' && report.status !== 'content_deleted') && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(189, 189, 189, 0.07)', p: 1.2, minWidth: 0 }}>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#FF3B2C',
                    color: 'white',
                    fontWeight: 600,
                    minWidth: 120,
                    boxShadow: 'none',
                    '&:hover': { bgcolor: '#D32F2F' }
                  }}
                  startIcon={<DeleteIcon />}
                  disabled={actionLoading}
                  onClick={() => handleAction('delete')}
                >
                  Supprimer
                </Button>
                <HelpOutlineIcon sx={{ color: '#BDBDBD', cursor: 'pointer' }} fontSize="small"
                  onClick={e => setAnchorEl2(e.currentTarget as unknown as HTMLElement)}
                />
                <Popover
                  open={open2}
                  anchorEl={anchorEl2}
                  onClose={() => setAnchorEl2(null)}
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                >
                  <Box sx={{ p: 2, maxWidth: 260 }}>
                    {getActionConfirmText('delete', report)}
                  </Box>
                </Popover>
              </Box>
            )}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, bgcolor: 'white', borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(25, 118, 210, 0.07)', p: 1.2, minWidth: 0 }}>
              {report.status !== 'in_review' && (
                <>
                  <Button
                    variant="contained"
                    sx={{ bgcolor: '#1976d2', color: 'white', fontWeight: 600, minWidth: 120, boxShadow: 'none', '&:hover': { bgcolor: '#1565c0' } }}
                    disabled={actionLoading}
                    onClick={async () => {
                      setActionLoading(true);
                      try {
                        const headers = await getCommonHeaders();
                        const { fetchCsrfToken } = await import('../../services/csrf');
                        headers['X-CSRF-Token'] = await fetchCsrfToken();
                        await axios.patch(`${API_CONFIG.baseURL}/api/reported-content/${id}`, {
                          status: 'in_review',
                        }, { headers, withCredentials: true });
                        await fetchReport();
                        notify('Statut mis à jour en cours', 'success');
                      } catch (e: any) {
                        notify(e?.response?.data?.error || "Erreur lors du changement de statut", 'error');
                      } finally {
                        setActionLoading(false);
                      }
                    }}
                    startIcon={<HistoryIcon />}
                  >
                    Mettre en cours
                  </Button>
                  <HelpOutlineIcon sx={{ color: '#1976d2', cursor: 'pointer' }} fontSize="small"
                    onClick={e => setAnchorEl2(e.currentTarget as unknown as HTMLElement)}
                  />
                  <Popover
                    open={open2}
                    anchorEl={anchorEl2}
                    onClose={() => setAnchorEl2(null)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                  >
                    <Box sx={{ p: 2, maxWidth: 260 }}>
                      Mettre le signalement en cours : ce statut indique que le dossier est en cours d'examen par la modération. Aucune action n'est prise, aucun email n'est envoyé.
                    </Box>
                  </Popover>
                </>
              )}
            </Box>
          </Box>
          {/* Bloc notification signaleurs + explication */}
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={notifyReporters}
                  onChange={e => setNotifyReporters(e.target.checked)}
                  color="primary"
                />
              }
              label="Avertir les membres signaleurs par notification (recommandé)"
              sx={{ mt: 1 }}
            />
            {notifyReporters && (
              <Paper sx={{ mt: 1, mb: 2, p: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2 }}>
                <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 500 }}>
                  Les membres ayant signalé ce contenu recevront une notification sur la plateforme pour les informer que leur signalement a bien été pris en compte et traité par l'équipe de modération.<br />
                  <span style={{ color: '#FF6B2C', fontWeight: 400 }}>
                    Le message envoyé est générique et ne précise pas la décision prise, afin de garantir la confidentialité de la modération.
                  </span>
                </Typography>
              </Paper>
            )}
          </Box>
          {/* Bloc notification email auteur + explication */}
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review' ? true : notifyAuthorByEmail}
                  onChange={e => setNotifyAuthorByEmail(e.target.checked)}
                  color="primary"
                  disabled={report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review'}
                />
              }
              label={report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review'
                ? "Notifier l'auteur par email (obligatoire pour ce type de contenu)"
                : "Notifier l'auteur par email (email de modération)"
              }
              sx={{ mt: 2 }}
            />
            {notifyAuthorByEmail && (
              <>
                <Paper sx={{ mt: 1, mb: 2, p: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2 }}>
                  <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 500 }}>
                    L'auteur du contenu recevra un email l'informant qu'une action de modération a été prise concernant son contenu.<br />
                    <span style={{ color: '#FF6B2C', fontWeight: 400 }}>
                      Le message explique la situation, rappelle les règles de la plateforme et, si un commentaire admin est renseigné, l'inclut dans l'email.
                    </span>
                  </Typography>
                </Paper>
                <Paper sx={{ mt: 1, p: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2 }}>
                  <Typography variant="subtitle2" sx={{ color: '#FF6B2C', mb: 1 }}>Aperçu de l'email envoyé à l'auteur :</Typography>
                  <div style={{ color: '#374151', fontSize: 15 }}>
                    <b>Objet :</b> 🚨 Modération de votre contenu sur JobPartiel<br />
                    <b>Message :</b><br />
                    Votre {TYPE_LABELS[report.content_type] || report.content_type} a été <b>{pendingAction ? ACTION_LABELS[pendingAction] : 'modéré'}</b> par l'équipe de modération.<br />
                    {adminComment && (
                      <div style={{ background: '#FFF8F3', borderLeft: '3px solid #FF7A35', padding: 10, margin: '15px 0' }}>
                        <b>Commentaire de l'équipe :</b><br />{adminComment}
                      </div>
                    )}
                    <div style={{ background: '#FFF8F3', padding: 10, borderRadius: 8, margin: '20px 0', border: '1px solid #FFE4BA' }}>
                      <b>Rappel des règles et sanctions possibles :</b>
                      <ul style={{ margin: 0, paddingLeft: 18 }}>
                        <li>Respectez les autres membres et la charte de la plateforme</li>
                        <li>Tout contenu inapproprié, insultant, discriminant ou hors sujet peut être modéré</li>
                        <li>En cas de récidive, des sanctions supplémentaires peuvent être appliquées (suspension, suppression du compte...)</li>
                      </ul>
                      Consultez les <a href="https://jobpartiel.fr/conditions-generales" target="_blank" rel="noopener noreferrer" style={{ color: '#FF7A35', textDecoration: 'underline' }}>Conditions Générales d'Utilisation</a> pour plus de détails.
                    </div>
                    <div style={{ color: '#4B5563', fontSize: 13, textAlign: 'center' }}>
                      Pour toute question, contactez notre support : <a href="mailto:<EMAIL>" style={{ color: '#FF7A35' }}><EMAIL></a>
                    </div>
                  </div>
                </Paper>
              </>
            )}
          </Box>
        </Paper>
        <Paper sx={{ p: 3, borderRadius: 3 }}>
          <Typography variant="h6" sx={{ color: '#FF6B2C', mb: 2 }}><HistoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />Historique des actions</Typography>
          {history.length === 0 ? (
            <Typography color="text.secondary">Aucune action/modération enregistrée.</Typography>
          ) : (
            <Box>
              {history.map((h, idx) => (
                <Paper key={idx} sx={{ p: 2, mb: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2 }}>
                  <Typography variant="subtitle2" sx={{ color: '#FF6B2C' }}>{ACTION_LABELS[h.action] || h.action}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {h.admin_nom !== '-' || h.admin_prenom !== '-' ? (
                      <>Par {h.admin_prenom} {h.admin_nom} {h.admin_role === 'jobpadm' ? '(Administrateur)' : h.admin_role === 'jobmodo' ? '(Modérateur)' : h.admin_role ? `(${h.admin_role})` : ''}</>
                    ) : h.admin_id ? (
                      <>Par admin {h.admin_id}</>
                    ) : null}
                    {' '}le {new Date(h.date).toLocaleString('fr-FR')}
                  </Typography>
                  {h.comment && <Typography variant="body2" sx={{ mt: 1 }}>{h.comment}</Typography>}
                </Paper>
              ))}
            </Box>
          )}
        </Paper>
        <ModalPortal isOpen={confirmDialogOpen} onBackdropClick={() => setConfirmDialogOpen(false)}>
          <div style={{
            background: 'white',
            borderRadius: 16,
            minWidth: 240,
            maxWidth: 680,
            boxShadow: '0 8px 32px 0 rgba(255,107,44,0.18)',
            padding: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'stretch',
            gap: 16
          }}>
            <Box sx={{ p: { xs: 2, sm: 4 }, width: '100%' }}>
              <Typography variant="h6" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1, textAlign: 'center' }}>
                Confirmer la décision
              </Typography>
              <DialogContentText sx={{ color: '#222', mb: 1, textAlign: 'center' }}>
                Êtes-vous sûr de vouloir effectuer cette action de modération ?
              </DialogContentText>
              <Box sx={{
                bgcolor: '#FFF7F0',
                border: '1.5px solid #FFE4BA',
                borderRadius: 3,
                p: { xs: 1.2, sm: 3 },
                mb: 2,
                boxShadow: '0 2px 12px 0 rgba(255,107,44,0.07)',
                minWidth: 0,
                maxWidth: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}>
                <Typography variant="subtitle2" sx={{ color: '#FF6B2C', mb: { xs: 1, sm: 2 }, fontWeight: 800, fontSize: { xs: 16, sm: 22 }, display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'center' }}>
                  <span style={{ fontSize: 26 }}>📝</span> Récapitulatif de l'action
                </Typography>
                <Box sx={{ mb: { xs: 1, sm: 2 }, display: 'flex', alignItems: 'center', gap: 1, minHeight: 36, flexDirection: 'column', textAlign: 'center', justifyContent: 'center' }}>
                  <span style={{ fontSize: 22 }}>{pendingAction === 'mask' ? '🚫' : pendingAction === 'delete' ? '🗑️' : pendingAction === 'validated' ? '✅' : pendingAction === 'rejected' ? '❌' : '⚡'}</span>
                  <span style={{ fontWeight: 700, color: '#FF6B2C', fontSize: 19, letterSpacing: 0.2 }}>
                    {pendingAction === 'validated' && report.content_type === 'mission'
                      ? "Valider le signalement : la mission va être supprimée définitivement, elle ne pourra pas être restaurée."
                      : getActionConfirmText(pendingAction || undefined, report) || `Modération de ${TYPE_LABELS[report.content_type] || report.content_type}`}
                  </span>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5, flexDirection: 'column', gap: 0.5, width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5, justifyContent: 'center' }}>
                    <span style={{ fontSize: 18 }}>👥</span>
                    <span style={{ fontWeight: 600, fontSize: 15 }}>Notification des membres signaleurs :</span>
                  </Box>
                  <span style={{
                    background: notifyReporters ? '#4CAF50' : '#FF3B2C',
                    color: 'white',
                    borderRadius: 10,
                    padding: '3px 18px',
                    fontWeight: 800,
                    fontSize: 15,
                    letterSpacing: 1,
                    minWidth: 48,
                    textAlign: 'center',
                    display: 'inline-block',
                    marginTop: 4,
                  }}>{notifyReporters ? 'OUI' : 'NON'}</span>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5, flexDirection: 'column', gap: 0.5, width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5, justifyContent: 'center' }}>
                    <span style={{ fontSize: 18 }}>✉️</span>
                    <span style={{ fontWeight: 600, fontSize: 15 }}>Avertir l'auteur du contenu par email :</span>
                  </Box>
                  <span style={{
                    background: ((report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review')
                      ? true
                      : notifyAuthorByEmail) ? '#4CAF50' : '#FF3B2C',
                    color: 'white',
                    borderRadius: 10,
                    padding: '3px 18px',
                    fontWeight: 800,
                    fontSize: 15,
                    letterSpacing: 1,
                    minWidth: 48,
                    textAlign: 'center',
                    display: 'inline-block',
                    marginTop: 4,
                  }}>{(report.content_type === 'profile' || report.content_type === 'mission' || report.content_type === 'review') ? 'OUI' : notifyAuthorByEmail ? 'OUI' : 'NON'}</span>
                </Box>
                {adminComment && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: { xs: 1, sm: 2 }, flexDirection: 'column', textAlign: 'center', justifyContent: 'center' }}>
                    <span style={{ fontSize: 18 }}>💬</span>
                    <span style={{ fontWeight: 600, fontSize: 15 }}>Commentaire admin :</span>
                    <span style={{ color: '#374151', fontWeight: 400, fontSize: 15, marginLeft: 4 }}>{adminComment}</span>
                  </Box>
                )}
              </Box>
              <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12, marginTop: 8 }}>
                <Button onClick={() => setConfirmDialogOpen(false)} color="inherit" sx={{ fontWeight: 600 }}>
                  Annuler
                </Button>
                <Button onClick={handleConfirmAction} color="primary" variant="contained" disabled={actionLoading} sx={{ fontWeight: 600, bgcolor: '#FF6B2C' }}>
                  Confirmer
                </Button>
              </div>
            </Box>
          </div>
        </ModalPortal>
        {/* Modal des signalements déjà reçus par l'utilisateur signalé */}
        <ModalPortal isOpen={userReportsModalOpen} onBackdropClick={() => setUserReportsModalOpen(false)}>
          <div style={{
            background: 'white',
            borderRadius: 18,
            minWidth: 400,
            maxWidth: 700,
            boxShadow: '0 8px 32px 0 rgba(255,107,44,0.13)',
            padding: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'stretch',
            overflow: 'hidden'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, bgcolor: '#FAFAFA', px: 4, py: 2.5, borderBottom: '1px solid #E0E0E0' }}>
              <Typography variant="h6" sx={{ color: '#222', fontWeight: 700, flex: 1 }}>
                Signalements déjà reçus par cet utilisateur
              </Typography>
              <Chip label={userReports.length} size="small" sx={{ bgcolor: '#F3F3F3', color: '#222', fontWeight: 700, fontSize: 15 }} />
            </Box>
            <div style={{ maxHeight: 400, overflowY: 'auto', background: '#FFF', padding: 0 }}>
              {loadingUserReports ? (
                <Typography sx={{ p: 4, color: '#FF6B2C' }}>Chargement...</Typography>
              ) : userReports.length === 0 ? (
                <Typography sx={{ p: 4 }}>Aucun signalement reçu.</Typography>
              ) : (
                <Box sx={{ width: '100%', minWidth: 'max-content' }}>
                  <Box sx={{ display: 'flex', fontWeight: 700, gap: 2, color: '#FF6B2C', fontSize: 15, px: 3, pt: 2, pb: 1 }}>
                    <span style={{ minWidth: 120 }}>Type</span>
                    <span style={{ minWidth: 220 }}>Contenu signalé</span>
                    <span style={{ minWidth: 160 }}>Raison</span>
                    <span style={{ minWidth: 120 }}>Statut</span>
                    <span style={{ minWidth: 120 }}>Date</span>
                  </Box>
                  <Divider sx={{ mb: 1, mx: 2, borderColor: '#E0E0E0' }} />
                  {userReports.map((r, idx) => {
                    const bg = idx % 2 === 0 ? '#FFF' : '#F7F7F7';
                    return (
                      <Box
                        key={r.id || idx}
                        sx={{
                          display: 'flex', alignItems: 'center', gap: 2, fontSize: 15, px: 3, py: 1.2,
                          background: bg,
                          borderRadius: 2,
                          transition: 'background 0.2s',
                          width: '100%'
                        }}
                      >
                        <span style={{ minWidth: 120 }}>{TYPE_LABELS[r.content_type] || r.content_type}</span>
                        <span
                          style={{
                            minWidth: 220,
                            maxWidth: 220,
                            color: '#374151',
                            fontSize: 14,
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: 'inline-block',
                            verticalAlign: 'middle',
                            cursor: r.content_text ? 'pointer' : 'default'
                          }}
                          onClick={e => {
                            if (r.content_text) {
                              if (anchorEl === e.currentTarget) {
                                setAnchorEl(null);
                              } else {
                                setPopoverContent(stripHtml(r.content_text));
                                setAnchorEl(e.currentTarget);
                              }
                            }
                          }}
                          title={r.content_text ? stripHtml(r.content_text) : ''}
                        >
                          {r.content_text ? stripHtml(r.content_text) : <span style={{ color: '#BDBDBD' }}>Contenu indisponible</span>}
                        </span>
                        <span style={{ minWidth: 160 }}>{r.reason || '-'}</span>
                        <span style={{ minWidth: 120 }}>{STATUS_LABELS[r.status] || r.status}</span>
                        <span style={{ minWidth: 120 }}>{r.created_at ? new Date(r.created_at).toLocaleString('fr-FR') : '-'}</span>
                      </Box>
                    );
                  })}
                </Box>
              )}
            </div>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 12, p: 2.5, bgcolor: '#FAFAFA', borderTop: '1px solid #E0E0E0' }}>
              <Button onClick={() => setUserReportsModalOpen(false)} sx={{ fontWeight: 700, color: 'white', bgcolor: '#FF6B2C', px: 3, borderRadius: 2, '&:hover': { bgcolor: '#FF7A35' } }}>
                Fermer
              </Button>
            </Box>
          </div>
        </ModalPortal>
        {/* Popover pour afficher le contenu signalé complet au clic */}
        <Popover
          open={Boolean(anchorEl)}
          anchorEl={anchorEl}
          onClose={() => setAnchorEl(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        >
          <Box sx={{ p: 2, maxWidth: 350, whiteSpace: 'pre-line', position: 'relative', pb: 5 }}>
            <div>{popoverContent}</div>
            <Button
              size="small"
              onClick={() => setAnchorEl(null)}
              sx={{
                position: 'absolute',
                bottom: 8,
                right: 8,
                minWidth: 0,
                padding: '2px 12px',
                fontSize: 13,
                color: '#FF6B2C',
                bgcolor: '#FFF8F3',
                border: '1px solid #FFE4BA',
                borderRadius: 2,
                boxShadow: 1,
                '&:hover': { bgcolor: '#FFE4BA' }
              }}
            >
              Fermer
            </Button>
          </Box>
        </Popover>
      </Container>
    </Box>
  );
};

// Aperçu dynamique du contenu signalé
const ContentPreview: React.FC<{ report: any }> = ({ report }) => {
  const [content, setContent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  // Pour la modale de conversation
  const [conversationOpen, setConversationOpen] = useState(false);
  const [conversation, setConversation] = useState<any>(null);
  const [loadingConversation, setLoadingConversation] = useState(false);

  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      try {
        // Appel API backend pour récupérer le contenu selon le type
        const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/${report.id}/content`);
        setContent(res.data);
      } catch (e) {
        setContent(null);
      } finally {
        setLoading(false);
      }
    };
    fetchContent();
    // eslint-disable-next-line
  }, [report.id]);

  // Fonction pour ouvrir la modale et charger la conversation
  const handleOpenConversation = async () => {
    setLoadingConversation(true);
    setConversationOpen(true);
    try {
      const res = await axios.get(`${API_CONFIG.baseURL}/api/reported-content/${report.id}/conversation`);
      setConversation(res.data);
    } catch (e) {
      setConversation(null);
    } finally {
      setLoadingConversation(false);
    }
  };

  if (loading) return <CircularProgress size={20} sx={{ color: '#FF6B2C' }} />;
  if (!content) return <Typography color="text.secondary">Contenu introuvable ou supprimé.</Typography>;

  // Affichage simple selon le type
  if (report.content_type === 'comment') {
    const isMasked = typeof content.comment === 'string' &&
      (content.comment.startsWith('Ce commentaire a été automatiquement masqué') || content.comment.startsWith('Ce commentaire à été modéré'));
    // Si c'est un snapshot (archive)
    if (content.is_snapshot) {
      return (
        <Box>
          <Paper sx={{ p: 2, mb: 2, bgcolor: '#FFF3E0', border: '2px solid #FF6B2C', borderRadius: 2 }}>
            <Typography sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>Archive du commentaire signalé (le commentaire original a été supprimé)</Typography>
            <Typography variant="body2" sx={{ color: '#222', fontFamily: 'monospace', fontSize: 15, whiteSpace: 'pre-wrap' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.comment }} />
            </Typography>
          </Paper>
        </Box>
      );
    }
    return (
      <Box>
        <Typography sx={{ fontWeight: 500 }}>Commentaire signalé (actuellement diffusé) :</Typography>
        <Paper sx={{ p: 1, mt: 1 }} dangerouslySetInnerHTML={{ __html: content.comment }} />
        {isMasked && content.comment_original && (
          <Paper sx={{
            p: 2.5,
            mt: 2,
            bgcolor: '#fff',
            border: '2px solid #FF6B2C',
            borderRadius: 2,
            boxShadow: '0 4px 16px 0 rgba(255, 107, 44, 0.12)',
            fontFamily: 'monospace',
            overflowX: 'auto',
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <VisibilityIcon sx={{ color: '#FF6B2C', mr: 1 }} fontSize="small" />
              <Typography variant="subtitle2" sx={{ color: '#FF6B2C', fontWeight: 700 }}>Texte original signalé :</Typography>
            </Box>
            <Typography variant="body2" sx={{ color: '#222', fontFamily: 'monospace', fontSize: 15, whiteSpace: 'pre-wrap' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.comment_original }} />
            </Typography>
          </Paper>
        )}
      </Box>
    );
  }
  if (report.content_type === 'message') {
    if (content.is_snapshot && content.content_original) {
      return (
        <Box>
          <Typography sx={{ fontWeight: 500 }}>Message (modéré) :</Typography>
          <Paper sx={{ p: 1, mt: 1, mb: 2, bgcolor: '#FFF3E0', border: '2px solid #FF6B2C', borderRadius: 2 }}>
            <Typography sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>Message actuellement affiché :</Typography>
            <Typography variant="body2" sx={{ color: '#222', fontFamily: 'monospace', fontSize: 15, whiteSpace: 'pre-wrap' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.content }} />
            </Typography>
          </Paper>
          <Paper sx={{ p: 2.5, mt: 1, bgcolor: '#fff', border: '2px solid #FF6B2C', borderRadius: 2, boxShadow: '0 4px 16px 0 rgba(255, 107, 44, 0.12)', fontFamily: 'monospace', overflowX: 'auto' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <VisibilityIcon sx={{ color: '#FF6B2C', mr: 1 }} fontSize="small" />
              <Typography variant="subtitle2" sx={{ color: '#FF6B2C', fontWeight: 700 }}>Message original signalé :</Typography>
            </Box>
            <Typography variant="body2" sx={{ color: '#222', fontFamily: 'monospace', fontSize: 15, whiteSpace: 'pre-wrap' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.content_original }} />
            </Typography>
          </Paper>
          <Button variant="outlined" sx={{ mt: 2, color: '#FF6B2C', borderColor: '#FF6B2C', fontWeight: 600 }} onClick={handleOpenConversation}>
            Voir la conversation complète
          </Button>
          {/* Modale de conversation */}
          <Dialog open={conversationOpen} onClose={() => setConversationOpen(false)} maxWidth="md" fullWidth>
            <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              Conversation complète
              <IconButton onClick={() => setConversationOpen(false)} size="small"><CloseIcon /></IconButton>
            </DialogTitle>
            <DialogContent dividers sx={{ bgcolor: '#FFF8F3' }}>
              {loadingConversation ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
                  <CircularProgress sx={{ color: '#FF6B2C' }} />
                </Box>
              ) : !conversation ? (
                <Typography color="error">Conversation introuvable.</Typography>
              ) : (
                <Box>
                  {conversation.messages.length === 0 ? (
                    <Typography>Aucun message dans cette conversation.</Typography>
                  ) : (
                    <Box>
                      {conversation.messages.map((msg: any) => (
                        <Paper key={msg.id} sx={{ p: 1.5, mb: 1.5, bgcolor: msg.id === report.content_id ? '#FFE4BA' : '#FFF', border: msg.id === report.content_id ? '2px solid #FF6B2C' : '1px solid #EEE', borderRadius: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Typography sx={{ fontWeight: 700, color: '#FF6B2C', mr: 1 }}>
                              {msg.users_sender?.email || msg.sender_id}
                            </Typography>
                            <span style={{ color: '#888', fontSize: 13, marginLeft: 8 }}>{new Date(msg.created_at).toLocaleString('fr-FR')}</span>
                          </Box>
                          <div style={{ color: '#222', fontSize: 15, whiteSpace: 'pre-line' }} dangerouslySetInnerHTML={{ __html: msg.content }} />
                        </Paper>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setConversationOpen(false)} sx={{ fontWeight: 700, color: 'white', bgcolor: '#FF6B2C', px: 3, borderRadius: 2, '&:hover': { bgcolor: '#FF7A35' } }}>
                Fermer
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      );
    }
    return <Box><Typography sx={{ fontWeight: 500 }}>Message :</Typography><Paper sx={{ p: 1, mt: 1 }} dangerouslySetInnerHTML={{ __html: content.content }} />
      <Button variant="outlined" sx={{ mt: 2, color: '#FF6B2C', borderColor: '#FF6B2C', fontWeight: 600 }} onClick={handleOpenConversation}>
        Voir la conversation complète
      </Button>
      {/* Modale de conversation */}
      <Dialog open={conversationOpen} onClose={() => setConversationOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle sx={{ color: '#FF6B2C', fontWeight: 700, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          Conversation complète
          <IconButton onClick={() => setConversationOpen(false)} size="small"><CloseIcon /></IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ bgcolor: '#FFF8F3' }}>
          {loadingConversation ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
              <CircularProgress sx={{ color: '#FF6B2C' }} />
            </Box>
          ) : !conversation ? (
            <Typography color="error">Conversation introuvable.</Typography>
          ) : (
            <Box>
              {conversation.messages.length === 0 ? (
                <Typography>Aucun message dans cette conversation.</Typography>
              ) : (
                <Box>
                  {conversation.messages.map((msg: any) => (
                    <Paper key={msg.id} sx={{ p: 1.5, mb: 1.5, bgcolor: msg.id === report.content_id ? '#FFE4BA' : '#FFF', border: msg.id === report.content_id ? '2px solid #FF6B2C' : '1px solid #EEE', borderRadius: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Typography sx={{ fontWeight: 700, color: '#FF6B2C', mr: 1 }}>
                          {msg.users_sender?.email || msg.sender_id}
                        </Typography>
                        <span style={{ color: '#888', fontSize: 13, marginLeft: 8 }}>{new Date(msg.created_at).toLocaleString('fr-FR')}</span>
                      </Box>
                      <div style={{ color: '#222', fontSize: 15, whiteSpace: 'pre-line' }} dangerouslySetInnerHTML={{ __html: msg.content }} />
                    </Paper>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConversationOpen(false)} sx={{ fontWeight: 700, color: 'white', bgcolor: '#FF6B2C', px: 3, borderRadius: 2, '&:hover': { bgcolor: '#FF7A35' } }}>
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>;
  }
  if (report.content_type === 'review') {
    // Gestion des avis supprimés ou archivés
    if (content.deleted_message || content.is_snapshot) {
      return (
        <Box>
          <Paper sx={{ p: 2, mb: 2, bgcolor: '#FFF3E0', border: '2px solid #FF6B2C', borderRadius: 2 }}>
            <Typography sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>
              {content.deleted_message || "Archive de l'avis signalé (l'avis original a été supprimé)"}
            </Typography>
            <Typography variant="body2" sx={{ color: '#222', fontFamily: 'monospace', fontSize: 15, whiteSpace: 'pre-wrap' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.commentaire }} />
            </Typography>
            {typeof content.note === 'number' && (
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <span style={{ color: '#FF6B2C', fontWeight: 700, fontSize: 18 }}>Note :</span>
                <span style={{ color: '#FF6B2C', fontWeight: 700, fontSize: 18 }}>{content.note} / 5</span>
              </Box>
            )}
          </Paper>
        </Box>
      );
    }
    // Affichage normal si non supprimé
    return (
      <Box>
        <Typography sx={{ fontWeight: 500 }}>Avis :</Typography>
        <Paper sx={{ p: 1, mt: 1 }}>
          {content.commentaire && content.commentaire.trim() ? (
            <span dangerouslySetInnerHTML={{ __html: content.commentaire }} />
          ) : (
            <Typography variant="body2" sx={{ color: '#888', fontStyle: 'italic', textAlign: 'center', py: 1 }}>
              Aucun commentaire, uniquement une note a été laissée.
            </Typography>
          )}
        </Paper>
        {typeof content.note === 'number' && (
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <span style={{ color: '#FF6B2C', fontWeight: 700, fontSize: 18 }}>Note :</span>
            <span style={{ color: '#FF6B2C', fontWeight: 700, fontSize: 18 }}>{content.note} / 5</span>
          </Box>
        )}
        {/* Affichage de la réponse à l'avis si elle existe */}
        {content.reponse && content.reponse.trim() && (
          <Paper sx={{
            mt: 2,
            p: 2,
            bgcolor: '#FFF8F3',
            border: '1.5px solid #FF6B2C',
            borderRadius: 2,
            boxShadow: '0 2px 8px 0 rgba(255,107,44,0.07)',
          }}>
            <Typography variant="subtitle2" sx={{ color: '#FF6B2C', fontWeight: 700, mb: 1 }}>Réponse à l'avis :</Typography>
            <Typography variant="body2" sx={{ color: '#222', fontSize: 15, whiteSpace: 'pre-line' }} component="div">
              <span dangerouslySetInnerHTML={{ __html: content.reponse }} />
            </Typography>
          </Paper>
        )}
      </Box>
    );
  }
  if (report.content_type === 'mission') {
    // Mapping pour les labels lisibles
    const statutLabels = {
      'en_attente': 'En attente',
      'en_cours': 'En cours',
      'terminee': 'Terminée',
      'annulee': 'Annulée',
      'en_moderation': 'En modération',
    };
    const paymentLabels = {
      'jobi_only': 'Paiement sécurisé Jobi',
      'direct_only': 'Paiement direct',
      'both': 'Jobi ou direct',
    };
    // Récupération du nom de la catégorie et sous-catégorie
    const category = SERVICE_CATEGORIES.find(cat => cat.id === String(content.category_id));
    const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === String(content.subcategory_id));
    return (
      <Box>
        <Typography sx={{ fontWeight: 500 }}>Mission :</Typography>
        <Paper sx={{
          mb: 2,
          p: 2.5,
          bgcolor: '#fff',
          border: '1.5px solid #FFE4BA',
          borderRadius: 3,
          boxShadow: '0 4px 16px 0 rgba(255,107,44,0.08)',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          minWidth: 0
        }}>
          {category && (
            <Box sx={{
              width: 56, height: 56, borderRadius: '50%', bgcolor: '#FFF8F3',
              display: 'flex', alignItems: 'center', justifyContent: 'center',
              boxShadow: '0 2px 8px 0 rgba(255,107,44,0.07)',
              border: '2px solid #FF6B2C', flexShrink: 0
            }}>
              <WorkOutlineIcon sx={{ color: '#FF6B2C', fontSize: 34 }} />
            </Box>
          )}
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="h5" sx={{ fontWeight: 800, color: '#FF6B2C', fontSize: 26, lineHeight: 1.1, mb: 0.5, wordBreak: 'break-word' }}>{content.titre || '-'}</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', mt: 0.5 }}>
              {category && (
                <Box sx={{ bgcolor: '#FFE4BA', color: '#FF6B2C', fontWeight: 700, borderRadius: 2, px: 1.2, py: 0.2, fontSize: 15 }}>{category.nom}</Box>
              )}
              {subcategory && (
                <Box sx={{ bgcolor: '#FFF3E0', color: '#FF6B2C', fontWeight: 600, borderRadius: 2, px: 1.2, py: 0.2, fontSize: 15 }}>{subcategory.nom}</Box>
              )}
              <Box sx={{ bgcolor: '#F3F3F3', color: '#666', borderRadius: 2, px: 1.2, py: 0.2, fontSize: 15 }}>{statutLabels[String(content.statut) as keyof typeof statutLabels] || content.statut || '-'}</Box>
              {content.ville && (
                <Box sx={{ bgcolor: '#F3F3F3', color: '#666', borderRadius: 2, px: 1.2, py: 0.2, fontSize: 15 }}>{content.ville}</Box>
              )}
              {content.date_mission && (
                <Box sx={{ bgcolor: '#F3F3F3', color: '#666', borderRadius: 2, px: 1.2, py: 0.2, fontSize: 15 }}>
                  {new Date(content.date_mission).toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                </Box>
              )}
            </Box>
          </Box>
        </Paper>
        <Paper sx={{ p: 1, mt: 1 }} dangerouslySetInnerHTML={{ __html: content.description }} />
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
          <VisibilityIcon sx={{ color: '#FF6B2C' }} fontSize="small" />
          <Link
            to={`/dashboard/missions/${report.content_id}`}
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#FF6B2C', fontWeight: 600, textDecoration: 'underline', fontSize: 16 }}
          >
            Voir la mission dans le dashboard
          </Link>
        </Box>
        {/* Bloc détails complémentaires mission */}
        <Paper sx={{ p: 2, mb: 2, bgcolor: '#FFF8F3', border: '1px solid #FFE4BA', borderRadius: 2, boxShadow: '0 2px 8px 0 rgba(255,107,44,0.04)' }}>
          <Grid container spacing={1}>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Code postal :</b> <span style={{ color: '#666' }}>{content.code_postal || '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Adresse :</b> <span style={{ color: '#666' }}>{content.adresse || '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Pays :</b> <span style={{ color: '#666' }}>{content.pays || '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Budget :</b> <span style={{ color: '#666' }}>{content.budget !== undefined ? content.budget.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' }) : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Budget défini :</b> <span style={{ color: '#666' }}>{content.budget_defini === true ? 'Oui' : content.budget_defini === false ? 'Non' : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Urgent :</b> <span style={{ color: '#666' }}>{content.is_urgent === true ? 'Oui' : content.is_urgent === false ? 'Non' : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Méthode de paiement :</b> <span style={{ color: '#666' }}>{paymentLabels[String(content.payment_method) as keyof typeof paymentLabels] || content.payment_method || '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Créée le :</b> <span style={{ color: '#666' }}>{content.created_at ? new Date(content.created_at).toLocaleString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' }) : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Fermée :</b> <span style={{ color: '#666' }}>{content.is_closed === true ? 'Oui' : content.is_closed === false ? 'Non' : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Nombre de candidatures :</b> <span style={{ color: '#666' }}>{content.applications_count !== undefined ? content.applications_count : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Nombre de likes :</b> <span style={{ color: '#666' }}>{content.likes_count !== undefined ? content.likes_count : '-'}</span></Grid>
            <Grid size={{ xs: 12, sm: 6, md: 4 }}><b>Nombre de commentaires :</b> <span style={{ color: '#666' }}>{content.comments_count !== undefined ? content.comments_count : '-'}</span></Grid>
          </Grid>
        </Paper>
      </Box>
    );
  }
  if (report.content_type === 'profile' || report.content_type === 'gallery' || report.content_type === 'mission') {
    // On tente de récupérer le slug pour le lien public
    const profilSlug = report?.user?.profilSlug || content?.slug;

    // Vérifier si on a une image temporaire
    const hasTempImage = report.temp_image_path && typeof report.temp_image_path === 'string';
    const tempImageUrl = hasTempImage ? `https://stockage.jobpartiel.fr/storage/v1/object/public/temp_moderation/${report.temp_image_path}` : null;

    return (
      <Box>
        <Typography sx={{ fontWeight: 500, mb: 1 }}>{report.content_type === 'profile' ? 'Profil signalé :' : report.content_type === 'gallery' ? 'Galerie signalée :' : 'Mission signalée :'}</Typography>

        {/* Afficher l'image si disponible */}
        {hasTempImage && (
          <Box sx={{ mb: 2, textAlign: 'center' }}>
            <Typography variant="subtitle2" sx={{ color: '#FF6B2C', mb: 1 }}>Image signalée :</Typography>
            <Paper sx={{ p: 2, bgcolor: '#FFF', border: '1px solid #FFE4BA', borderRadius: 2, display: 'inline-block', maxWidth: '100%', position: 'relative' }}>
              <img
                src={tempImageUrl || undefined}
                alt="Image signalée"
                style={{ maxWidth: '100%', maxHeight: '400px', objectFit: 'contain' }}
              />
              <IconButton
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  bgcolor: 'rgba(255, 107, 44, 0.1)',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.2)' }
                }}
                onClick={() => {
                  // Confirmation avant suppression
                  if (window.confirm('Êtes-vous sûr de vouloir supprimer cette image ? Cette action est irréversible.')) {
                    // Appel API pour supprimer l'image
                    axios.post(`${API_CONFIG.baseURL}/api/moderation/delete-image`, {
                      userId: report.reported_user_id,
                      filePath: report.temp_image_path
                    }, {
                      headers: {
                        Authorization: `Bearer ${localStorage.getItem('token')}`
                      }
                    })
                    .then(() => {
                      // Notification de succès
                      notify('L\'image a été supprimée avec succès','success');
                      // Rafraîchir la page
                      window.location.reload();
                    })
                    .catch(error => {
                      // Notification d'erreur
                      notify('Impossible de supprimer l\'image : ' + (error.response?.data?.message || error.message), 'error');
                    });
                  }
                }}
              >
                <DeleteIcon sx={{ color: '#FF6B2C' }} />
              </IconButton>
            </Paper>
            <Typography variant="caption" sx={{ display: 'block', mt: 1, color: '#888' }}>
              Cette image a été détectée comme potentiellement inappropriée par notre système de modération automatique.
              <br />
              <span style={{ color: '#FF6B2C', fontWeight: 500 }}>Note: Les images temporaires sont automatiquement supprimées après 30 jours.</span>
            </Typography>
          </Box>
        )}

        {report.content_type === 'profile' && (
          <Paper sx={{ p: 2, mb: 1, bgcolor: '#FFF', border: '1px solid #FFE4BA', borderRadius: 2 }}>
            <div style={{ fontWeight: 600, color: '#FF6B2C', fontSize: 17, marginBottom: 4 }}>
              {content.prenom || '-'} {content.nom || '-'}
            </div>
            <div style={{ color: '#888', fontSize: 14, marginBottom: 8 }}>
              <b>Bio :</b>
              <div style={{ marginTop: 4 }} dangerouslySetInnerHTML={{ __html: content.bio || '<span style="color:#BDBDBD">Aucune bio renseignée</span>' }} />
            </div>
          </Paper>
        )}

        <Paper sx={{ p: 2, bgcolor: '#FFF8F3', border: '1px solid #FF6B2C', borderRadius: 2, mt: 1 }}>
          <span style={{ color: '#FF6B2C', fontWeight: 600, fontSize: 15 }}>
            ⚠️ Pour comprendre le signalement, il est conseillé de visiter le profil public&nbsp;:
            {profilSlug ? (
              <a href={`/dashboard/profil/${profilSlug}`} target="_blank" rel="noopener noreferrer" style={{ color: '#FF6B2C', textDecoration: 'underline', marginLeft: 8 }}>
                Voir le profil
              </a>
            ) : (
              <span style={{ color: '#BDBDBD', marginLeft: 8 }}>(lien indisponible)</span>
            )}
          </span>
        </Paper>
      </Box>
    );
  }
  return <Typography color="text.secondary">Type de contenu non supporté.</Typography>;
};

export default ReportedContentDetailPage;