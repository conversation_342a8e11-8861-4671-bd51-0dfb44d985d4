import React from 'react';
import { Al<PERSON><PERSON><PERSON>gle, CheckCircle, Loader2 } from 'lucide-react';
import { Tooltip } from '@mui/material';
import RejectedImageMessage from './RejectedImageMessage';

interface ImageModerationStatusCompactProps {
  isLoading: boolean;
  result?: {
    isSafe: boolean;
    description?: string;
    improvementSuggestions?: string;
  };
  contentType?: string;
  className?: string;
}

/**
 * Composant compact pour afficher l'état de modération d'une image
 * Utilisé dans les listes, tableaux et autres endroits où l'espace est limité
 */
const ImageModerationStatusCompact: React.FC<ImageModerationStatusCompactProps> = ({
  isLoading,
  result,
  contentType = 'image',
  className = ''
}) => {
  return (
    <div className={`flex items-center ${className}`}>
      {isLoading ? (
        <div className="flex items-center text-blue-600">
          <div className="animate-spin mr-2">
            <Loader2 className="h-4 w-4" />
          </div>
          <span className="text-sm">Vérification de l'image...</span>
        </div>
      ) : result ? (
        result.isSafe ? (
          <div className="flex items-center text-green-600">
            <CheckCircle className="h-4 w-4 mr-2" />
            <span className="text-sm">Image validée</span>
          </div>
        ) : (
          <Tooltip
            title={
              <RejectedImageMessage
                contentType={contentType}
                description={result.description}
                improvementSuggestions={result.improvementSuggestions}
                variant="compact"
              />
            }
            arrow
            placement="top"
          >
            <div className="flex items-center text-red-600 cursor-help">
              <AlertTriangle className="h-4 w-4 mr-2" />
              <span className="text-sm">
                Image non conforme aux règles de modération
              </span>
            </div>
          </Tooltip>
        )
      ) : null}
    </div>
  );
};

export default ImageModerationStatusCompact;
