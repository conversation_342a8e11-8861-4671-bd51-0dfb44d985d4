import { Request, Response } from 'express';
import { dbService } from '../services/db';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { uploadProfilPhoto, uploadBannerPhoto, uploadGalleryPhoto, uploadEntrepriseVerificationDocument, deleteEntrepriseVerificationDocument, deleteAllEntrepriseVerificationDocuments, deleteBannerPhoto, extractFilePathFromUrl, deleteCardEditorImages } from '../services/storage';
import { UploadedFile } from 'express-fileupload';
import { FileRequest } from '../middleware/fileValidation';
import { supabase } from '../config/supabase';
import { sendVerificationEmail, sendAccountDeletionConfirmationEmail, sendAccountDeletionCompletedEmail } from '../services/emailService';
import dns from 'dns';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { getUserSubscriptionLimits } from '../routes/configSubscriptions';
import { sendAllEntrepriseDocsDeletedEmail, sendEntrepriseVerificationStatus, sendProfilVerifierEmail } from '../services/emailServiceModeration';
import contentModerationService from '../services/contentModerationService';
import { decryptProfilDataAsync, decryptUserDataAsync, encryptUserDataAsync, hashEmail } from '../utils/encryption';
import { comparePasswords } from '../utils/password';
import { randomBytes } from 'crypto';
const uuidv4 = () => randomBytes(16).toString('hex');
import fs from 'fs';

const DEFAULT_IMAGE_URL = "https://api.jobpartiel.fr/api/storage-proxy/photo_profil/avatar/avatar-defaut-jobpartiel.jpg";

// Constantes pour l'anonymisation cohérente
const ANONYMIZED_USER_DATA = {
  nom: null,
  prenom: 'Utilisateur Supprimé',
  displayName: 'Utilisateur Supprimé',
  bio: 'Ce profil a été supprimé par l\'utilisateur conformément au RGPD.',
  adresse: 'Profil supprimé',
  ville: 'Non disponible',
  codePostal: '00000',
  reviewComment: 'Avis d\'un utilisateur supprimé',
  messageContent: 'Message d\'un utilisateur supprimé'
};

export enum LogEventType {
    SERVER_ERROR = 'SERVER_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    USER_ERROR = 'USER_ERROR',
    USER_UPDATE = 'USER_UPDATE',
    AUTH_FAILURE = 'AUTH_FAILURE',
    DB_ERROR = 'DB_ERROR',
    // Ajoutez d'autres types d'événements si nécessaire
}

interface ProfilData {
    adresse?: string;
    ville?: string;
    code_postal?: string;
    pays?: string;
    nom?: string;
    prenom?: string;
    telephone?: string;
    telephone_prive?: boolean;
    bio?: string;
    photo_url?: string;
    banner_url?: string;
    banner_position?: string;
    banner_position_offset?: number;
    numero?: string;
    mode_vacance?: boolean;
    intervention_zone?: {
        center: [number, number];
        radius: number;
        adresse?: string;
    };
    // Champs entreprise
    type_de_profil?: string;
    nom_entreprise?: string;
    prenom_entreprise?: string;
    statut_entreprise?: string;
    siren_entreprise?: string;
    code_ape_entreprise?: string;
    categorie_entreprise?: string;
    effectif_entreprise?: string;
    date_insee_creation_entreprise?: string;
    date_categorie_entreprise?: string;
    date_derniere_mise_a_jour_entreprise_insee?: string;
    date_derniere_mise_a_jour_du_client_entreprise?: string;
    slug?: string;
    profil_visible?: boolean;
    date_validation_document_identite?: string;
    date_validation_document_entreprise?: string;
    date_validation_document_assurance?: string;
    slogan?: string;
}

interface GalleryData {
  name: string;
  description?: string;
  coverImage?: string;
}

interface GalleryPhotoData {
  photo_url: string;
  caption?: string;
}

// Constantes pour le cache Redis des historiques d'actions et des types d'actions
const CACHE_TTL = 1800; // 30 minutes
const CACHE_KEYS = {
  LOGIN_HISTORY: (userId: string, page: number = 0, limit: number = 0) =>
    page && limit ? `login_history:user:${userId}:page:${page}:limit:${limit}` : `login_history:user:${userId}`,
  ACTION_HISTORY: (userId: string, page: number = 0, limit: number = 0, type: string = 'all') =>
    page && limit ? `action_history:user:${userId}:page:${page}:limit:${limit}:type:${type}` : `action_history:user:${userId}:type:${type}`,
  ACTION_TYPES: (userId: string) => `action_types:user:${userId}`,
  PUBLIC_ACTIONS: (userId: string) => `public_actions:user:${userId}`
};

export class UserController
{
  async getCurrentUser(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      // Vérification de l'authentification avant toute opération
      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'accès sans authentification');

        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error',
          redirect: '/login' // Ajout d'une indication de redirection
        });
        return;
      }

      const cacheKey = `user:${userId}`;
      let cachedUser;

      // Récupération de l'utilisateur depuis le cache Redis si disponible
      cachedUser = await redis.get(cacheKey);

      if (cachedUser) {
        // Vérifier si l'utilisateur dans le cache est toujours valide
        const parsedUser = JSON.parse(cachedUser);
        if (!parsedUser.id || !parsedUser.email) {
          // Si le cache est invalide, le supprimer
          await redis.del(cacheKey);
          cachedUser = null;
          logger.info('Utilisateur NON récupéré depuis le cache getCurrentUser :', { userId });
          logger.info('Cache getCurrentUser supprimé :', { cacheKey });
        } else {
          logger.info('Utilisateur récupéré depuis le cache getCurrentUser :', { userId });
          // logger.info('Cache getCurrentUser :', { cachedUser });
          res.json(parsedUser);
          return;
        }
      }

      // Récupération de l'utilisateur (déjà décrypté par dbService.getUserById)
      const user = await dbService.getUserById(userId || '');
      logger.info('Récupération de l\'utilisateur getCurrentUser', { userId });
      if (!user) {
        logger.warn('Utilisateur non trouvé', { userId });
        res.status(404).json({
          message: 'Utilisateur non trouvé',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Vérifier si l'utilisateur a un abonnement premium
      const { isPremium, options } = await getUserSubscriptionLimits(userId || '');
      logger.info('Statut premium de l\'utilisateur', { userId, isPremium, hasOptions: !!options });

      // Récupérer les galeries actives de l'utilisateur
      const { data: galleries, error: galleriesError } = await supabase
        .from('user_gallery')
        .select(`
          id,
          name,
          description,
          cover_image,
          status,
          created_at,
          user_gallery_photos!gallery_id(count)
        `)
        .eq('user_id', userId)
        .eq('status', 'actif')
        .order('created_at', { ascending: false });

      if (galleriesError) {
        logger.error('Erreur lors de la récupération des galeries', { error: galleriesError });
        res.status(500).json({
          message: 'Erreur lors de la récupération des informations utilisateur',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Récupérer le compte exact des photos pour chaque galerie
      const galleriesWithCounts = await Promise.all(
        galleries.map(async (gallery) => {
          const { count } = await supabase
            .from('user_gallery_photos')
            .select('*', { count: 'exact', head: true })
            .eq('gallery_id', gallery.id);

          return {
            id: gallery.id,
            name: gallery.name,
            description: gallery.description,
            cover_image: gallery.cover_image,
            imagesCount: count || 0,
            createdAt: gallery.created_at
          };
        })
      );

      // logger.info('Récupération dashboard des infos de la base de données :', user);
      // Note: user contient déjà les données décryptées grâce à dbService.getUserById()

      // Stocker l'utilisateur dans le cache redis avec une expiration de 10 minutes
      await redis.setex(cacheKey, 600, JSON.stringify({
        success: true,
        id: user.id,
        profil_actif: user.profil_actif,
        profil_verifier: user.profil_verifier,
        identite_verifier: user.identite_verifier,
        entreprise_verifier: user.entreprise_verifier,
        assurance_verifier: user.assurance_verifier,
        is_online: user.is_online,
        role: user.role,
        user_type: user.user_type,
        date_inscription: user.date_inscription,
        telephone: user.profil?.data?.telephone,
        telephone_prive: user.profil?.data?.telephone_prive,
        email: user.email,
        isPremium: isPremium,
        profil: {
          data: {
            nom: user.profil?.data?.nom,
            prenom: user.profil?.data?.prenom,
            telephone: user.profil?.data?.telephone,
            telephone_prive: user.profil?.data?.telephone_prive,
            adresse: user.profil?.data?.adresse,
            ville: user.profil?.data?.ville,
            code_postal: user.profil?.data?.code_postal,
            pays: user.profil?.data?.pays,
            bio: user.profil?.data?.bio,
            photo_url: user.profil?.data?.photo_url,
            banner_url: user.profil?.data?.banner_url,
            banner_position: user.profil?.data?.banner_position || 'center',
            banner_position_offset: user.profil?.data?.banner_position_offset || 0,
            numero: user.profil?.data?.numero,
            intervention_zone: user.profil?.data?.intervention_zone,
            mode_vacance: user.profil?.data?.mode_vacance,
            type_de_profil: user.profil?.data?.type_de_profil,
            nom_entreprise: user.profil?.data?.nom_entreprise,
            prenom_entreprise: user.profil?.data?.prenom_entreprise,
            statut_entreprise: user.profil?.data?.statut_entreprise,
            siren_entreprise: user.profil?.data?.siren_entreprise,
            code_ape_entreprise: user.profil?.data?.code_ape_entreprise,
            categorie_entreprise: user.profil?.data?.categorie_entreprise,
            effectif_entreprise: user.profil?.data?.effectif_entreprise,
            date_insee_creation_entreprise: user.profil?.data?.date_insee_creation_entreprise,
            date_categorie_entreprise: user.profil?.data?.date_categorie_entreprise,
            date_derniere_mise_a_jour_entreprise_insee: user.profil?.data?.date_derniere_mise_a_jour_entreprise_insee,
            date_derniere_mise_a_jour_du_client_entreprise: user.profil?.data?.date_derniere_mise_a_jour_du_client_entreprise,
            slug: user.profil?.data?.slug,
            profil_visible: user.profil?.data?.profil_visible,
            // Champs de validation de documents ajoutés
            date_validation_document_identite: user.profil?.data?.date_validation_document_identite,
            date_validation_document_entreprise: user.profil?.data?.date_validation_document_entreprise,
            date_validation_document_assurance: user.profil?.data?.date_validation_document_assurance,
            slogan: user.profil?.data?.slogan,
            seo_indexable: user.profil?.data?.seo_indexable || false
          }
        },
        galleryFolders: galleriesWithCounts
      }));

      // Renvoie l'utilisateur avec des valeurs par défaut si nécessaire
      // Note: user contient déjà les données décryptées grâce à dbService.getUserById()
      res.json({
        success: true,
        id: user.id,
        profil_actif: user.profil_actif,
        profil_verifier: user.profil_verifier,
        identite_verifier: user.identite_verifier,
        entreprise_verifier: user.entreprise_verifier,
        assurance_verifier: user.assurance_verifier,
        is_online: user.is_online,
        role: user.role,
        user_type: user.user_type,
        date_inscription: user.date_inscription,
        telephone: user.profil?.data?.telephone,
        telephone_prive: user.profil?.data?.telephone_prive,
        email: user.email,
        isPremium: isPremium,
        profil: {
          data: {
            nom: user.profil?.data?.nom,
            prenom: user.profil?.data?.prenom,
            telephone: user.profil?.data?.telephone,
            telephone_prive: user.profil?.data?.telephone_prive,
            adresse: user.profil?.data?.adresse,
            ville: user.profil?.data?.ville,
            code_postal: user.profil?.data?.code_postal,
            pays: user.profil?.data?.pays,
            bio: user.profil?.data?.bio,
            photo_url: user.profil?.data?.photo_url,
            banner_url: user.profil?.data?.banner_url,
            banner_position: user.profil?.data?.banner_position || 'center',
            banner_position_offset: user.profil?.data?.banner_position_offset || 0,
            numero: user.profil?.data?.numero,
            intervention_zone: user.profil?.data?.intervention_zone,
            mode_vacance: user.profil?.data?.mode_vacance,
            type_de_profil: user.profil?.data?.type_de_profil,
            nom_entreprise: user.profil?.data?.nom_entreprise,
            prenom_entreprise: user.profil?.data?.prenom_entreprise,
            statut_entreprise: user.profil?.data?.statut_entreprise,
            siren_entreprise: user.profil?.data?.siren_entreprise,
            code_ape_entreprise: user.profil?.data?.code_ape_entreprise,
            categorie_entreprise: user.profil?.data?.categorie_entreprise,
            effectif_entreprise: user.profil?.data?.effectif_entreprise,
            date_insee_creation_entreprise: user.profil?.data?.date_insee_creation_entreprise,
            date_categorie_entreprise: user.profil?.data?.date_categorie_entreprise,
            date_derniere_mise_a_jour_entreprise_insee: user.profil?.data?.date_derniere_mise_a_jour_entreprise_insee,
            date_derniere_mise_a_jour_du_client_entreprise: user.profil?.data?.date_derniere_mise_a_jour_du_client_entreprise,
            slug: user.profil?.data?.slug,
            profil_visible: user.profil?.data?.profil_visible,
            // Champs de validation de documents ajoutés
            date_validation_document_identite: user.profil?.data?.date_validation_document_identite,
            date_validation_document_entreprise: user.profil?.data?.date_validation_document_entreprise,
            date_validation_document_assurance: user.profil?.data?.date_validation_document_assurance,
            slogan: user.profil?.data?.slogan,
            seo_indexable: user.profil?.data?.seo_indexable || false
          }
        },
        galleryFolders: galleriesWithCounts
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la récupération de l\'utilisateur', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        message: 'Erreur lors de la récupération des informations utilisateur',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updateUserType(req: Request, res: Response): Promise<void> {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      logger.info('Début de updateUserType', { userId, body: req.body });

      const userType: 'jobbeur' | 'non-jobbeur' | undefined = (req.body.user_type) as 'jobbeur' | 'non-jobbeur' | undefined;
      logger.info('Type d\'utilisateur reçu', { userType, originalValue: req.body.user_type });

      // Vérification de l'authentification
      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de mise à jour sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // Vérification de l'utilisateur
      // logger.info('Récupération de l\'utilisateur', { userId });
      const user = await dbService.getUserById(userId || '');
      logger.info('Utilisateur récupéré', { user });

      if (!user) {
        logger.error(LogEventType.USER_ERROR, 'Utilisateur non trouvé', { userId });
        res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé',
        });
      }

      // Vérification minimale des champs requis
      if (!user.id) {
        logger.error(LogEventType.USER_ERROR, 'Informations utilisateur incomplètes', { userId, user });
        res.status(400).json({
          message: 'Informations utilisateur incomplètes',
          success: false,
          toastType: 'error'
        });
      }

      // Vérification de la validité de userType
      if (!userType || !['jobbeur', 'non-jobbeur'].includes(userType)) {
          logger.warn(LogEventType.VALIDATION_ERROR, 'Type d\'utilisateur invalide', { userType, validTypes: ['jobbeur', 'non-jobbeur'] });
          res.status(400).json({
              message: 'Type d\'utilisateur invalide',
              success: false,
              toastType: 'error'
          });
      }

      // Vérifier si l'utilisateur est actif
      if (!user.profil_actif) {
        logger.warn(LogEventType.USER_ERROR, 'Profil utilisateur inactif', { userId, profil_actif: user.profil_actif });
        res.status(403).json({
          message: 'Votre profil est actuellement inactif',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier si le type est différent du type actuel
      if (user.user_type === userType) {
        logger.info('Type d\'utilisateur identique', { currentType: user.user_type, newType: userType });
        res.status(400).json({
          message: 'Vous êtes déjà enregistré avec ce type d\'utilisateur',
          success: false,
          toastType: 'info'
        });
      }

      // Mettre à jour le type d'utilisateur
      logger.info("Tentative de mise à jour du type d'utilisateur", { userId, userType, currentType: user.user_type });

      const updateData = {
        user_type: userType
      };
      logger.info("Données de mise à jour", { updateData });

      const updatedUser = await dbService.updateUser(userId || '', updateData);
      logger.info("Résultat de la mise à jour", { updatedUser });

      if (!updatedUser) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour du type d\'utilisateur', { userId, userType, updateData });
        res.status(500).json({
          message: 'Une erreur est survenue lors de la mise à jour du type d\'utilisateur',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis 1 supprimé, updateUserType :', { cacheKey });
      logger.info('Cache Redis 2 supprimé, updateUserType :', { cacheKey_deux });

      // Logger le changement de type
      logger.info(LogEventType.USER_UPDATE, 'Type d\'utilisateur mis à jour avec succès', {
        userId,
        oldType: user.user_type,
        newType: userType,
        updatedUser
      });

      // Retourner la réponse
      res.status(200).json({
        message: 'Type d\'utilisateur mis à jour avec succès',
        success: true,
        user: updatedUser,
        toastType: 'success'
      });

    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la mise à jour du type d\'utilisateur', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        userId,
        userType: req.body.user_type
      });

      res.status(500).json({
        message: 'Une erreur est survenue lors de la mise à jour du type d\'utilisateur',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updateUserProfil(req: Request, res: Response): Promise<void> {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      logger.info('Début de updateUserProfil', { userId, body: req.body });

      // Vérification de l'authentification
      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de mise à jour sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Filtrer les champs autorisés
      const fields = ['nom', 'prenom', 'telephone', 'adresse', 'ville', 'code_postal', 'pays', 'bio', 'photo_url', 'banner_url', 'numero', 'intervention_zone', 'type_de_profil',
        'nom_entreprise', 'prenom_entreprise', 'statut_entreprise', 'siren_entreprise', 'code_ape_entreprise', 'banner_position', 'banner_position_offset',
        'categorie_entreprise', 'effectif_entreprise', 'date_insee_creation_entreprise', 'date_categorie_entreprise',
        'date_derniere_mise_a_jour_entreprise_insee', 'date_derniere_mise_a_jour_du_client_entreprise', 'mode_vacance', 'slug', 'telephone_prive', 'slogan'
      ];
      const profilData: ProfilData = {};

      // Ne conserver que les champs autorisés et non vides
      fields.forEach(field => {
        if (req.body[field] !== undefined && req.body[field] !== null && req.body[field] !== '') {
          (profilData as any)[field] = req.body[field];
        }
      });

      // Traiter les données d'entreprise si présentes
      if (req.body.companyInfo) {
        Object.entries(req.body.companyInfo).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '' && fields.includes(key)) {
            (profilData as any)[key] = value;
          }
        });
      }

      // Vérifier qu'il y a au moins un champ à mettre à jour
      if (Object.keys(profilData).length === 0) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Aucun champ à mettre à jour');
        res.status(400).json({
          message: 'Aucun champ à mettre à jour',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Vérification de l'utilisateur
      const user = await dbService.getUserById(userId || '');
      if (!user) {
        logger.error(LogEventType.USER_ERROR, 'Utilisateur non trouvé', { userId });
        res.status(404).json({
          message: 'Utilisateur non trouvé',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Vérifier si l'utilisateur est actif
      if (!user.profil_actif) {
        logger.warn(LogEventType.USER_ERROR, 'Profil utilisateur inactif', { userId });
        res.status(403).json({
          message: 'Votre profil est actuellement inactif',
          success: false,
          toastType: 'error'
        });
        return;
      }

      // Vérifier si l'utilisateur essaie de modifier son nom ou prénom
      if ((profilData.nom !== undefined || profilData.prenom !== undefined) && user.profil?.data) {
        // Vérifier si le nom ou prénom existent déjà dans le profil
        const existingNom = user.profil.data.nom;
        const existingPrenom = user.profil.data.prenom;

        // Si les deux champs existent déjà, vérifier si inscription depuis moins de 48h
        if (existingNom && existingPrenom) {
          // Récupérer la date de création du profil depuis la base de données
          const { data: userProfilDb, error } = await supabase
            .from('users')
            .select('date_inscription')
            .eq('id', userId)
            .single();

          if (error) {
            logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération de la date de création du profil', { userId, error });
          } else if (userProfilDb) {
            const date_inscription = new Date(userProfilDb.date_inscription);
            const now = new Date();
            const hoursDiff = (now.getTime() - date_inscription.getTime()) / (1000 * 60 * 60);

            // Si plus de 48 heures se sont écoulées, interdire la modification du nom et prénom
            if (hoursDiff > 48) {
              // Filtre pour supprimer nom et prénom de la mise à jour
              if (profilData.nom !== undefined || profilData.prenom !== undefined) {
                delete profilData.nom;
                delete profilData.prenom;

                if (Object.keys(profilData).length === 0) {
                  logger.warn(LogEventType.VALIDATION_ERROR, 'Modification du nom/prénom non autorisée après 48 heures');
                  res.status(403).json({
                    message: 'La modification du nom et prénom n\'est autorisée que dans les 48 heures suivant leur définition initiale.',
                    success: false,
                    toastType: 'error'
                  });
                  return;
                }

                logger.warn(LogEventType.VALIDATION_ERROR, 'Nom et prénom non modifiés car délai de 48h dépassé');
              }
            }
          }
        }
      }

      // Si le type de profil change, réinitialiser les vérifications et les informations d'entreprise
      if (profilData.type_de_profil && user.profil?.data?.type_de_profil !== profilData.type_de_profil) {
        // Réinitialiser TOUT d'un coup
        await supabase.from('users')
          .update({
            profil_verifier: false,
            identite_verifier: false,
            entreprise_verifier: false,
            assurance_verifier: false
          })
          .eq('id', userId);

        await supabase.from('user_profil')
          .update({
            type_de_profil: profilData.type_de_profil,
            siren_entreprise: '',
            nom_entreprise: '',
            prenom_entreprise: '',
            statut_entreprise: '',
            code_ape_entreprise: '',
            categorie_entreprise: '',
            effectif_entreprise: '',
            date_insee_creation_entreprise: null,
            date_categorie_entreprise: null,
            date_derniere_mise_a_jour_entreprise_insee: null,
            date_derniere_mise_a_jour_du_client_entreprise: new Date().toISOString()
          })
          .eq('user_id', userId);
      }

      // Mise à jour du profil
      const updateFields: any = {
        ...profilData,
        updated_at: new Date().toISOString()
      };
      // Ajout de la gestion de banner_position si présent dans le body
      if (typeof req.body.banner_position === 'string') {
        updateFields.banner_position = req.body.banner_position;
      }
      // Ajout de la gestion de banner_position_offset si présent dans le body
      if (typeof req.body.banner_position_offset === 'number') {
        updateFields.banner_position_offset = req.body.banner_position_offset;
      }
      const updatedProfil = await dbService.updateUserProfil(userId || '', updateFields);

      if (!updatedProfil) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour du profil', { userId, profilData });
        res.status(500).json({
          message: 'Une erreur est survenue lors de la mise à jour du profil',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      const profilCacheKey = `profile_visibility:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      await redis.del(profilCacheKey);
      logger.info('Cache Redis invalidé pour updateUserProfil :', { userId });

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId || '',
        'profile_update',
        undefined,
        'user_profile',
        {
          updatedFields: Object.keys(profilData),
          profileType: profilData.type_de_profil || user.profil?.data?.type_de_profil
        },
        getIpFromRequest(req)
      );

      logger.info(LogEventType.USER_UPDATE, 'Profil utilisateur mis à jour avec succès', {
        userId,
        updatedFields: Object.keys(profilData)
      });

      res.json({
        message: 'Profil mis à jour avec succès',
        success: true,
        toastType: 'success',
        profil: updatedProfil
      });

    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la mise à jour du profil', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      res.status(500).json({
        message: 'Une erreur est survenue lors de la mise à jour du profil',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updateProfilPhoto(req: FileRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        logger.error('Tentative d\'upload sans utilisateur authentifié');
        res.status(401).json({ message: 'Non autorisé' });
      }

      const avatar = req.files?.avatar as UploadedFile;
      if (!avatar) {
        logger.error('Aucune photo fournie dans la requête');
        res.status(400).json({ message: 'Aucune photo fournie' });
      }

      // Vérification du type MIME
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedMimeTypes.includes(avatar.mimetype)) {
        logger.error('Type de fichier non autorisé', { mimetype: avatar.mimetype });
        return res.status(400).json({ message: 'Format de fichier non supporté. Utilisez JPG, PNG, WEBP, AVIF ou HEIC.' });
      }

      // Vérification de la taille
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (avatar.size > maxSize) {
        logger.error('Fichier trop volumineux', { size: avatar.size });
        return res.status(400).json({ message: 'La taille du fichier ne doit pas dépasser 2MB.' });
      }

      // Lire le fichier temporaire
      const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
        require('fs').readFile(avatar.tempFilePath, (err: any, data: Buffer) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      // Upload de la nouvelle photo
      const photoUrl = await uploadProfilPhoto(userId || '', fileBuffer, avatar.mimetype);

      // Mise à jour de l'URL dans la base de données
      const { data: updatedProfile, error: updateError } = await supabase
        .from('user_profil')
        .update({
          photo_url: photoUrl,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la photo de profil dans la base de données', { error: updateError, userId });
        throw new Error('Erreur lors de la mise à jour de la photo de profil dans la base de données');
      }

      // Créer un signalement différé seulement si l'image a été modérée avec un ID temporaire réel
      if (req.body?.tempImageId && typeof req.body.tempImageId === 'string' && req.body.tempImageId.trim() !== '') {
        try {
          await contentModerationService.createDeferredImageReport(
            req.body.tempImageId,
            updatedProfile.id,
            'profile_picture',
            userId || ''
          );

          logger.info('Signalement différé créé avec succès pour la photo de profil', {
            tempImageId: req.body.tempImageId,
            permanentImageId: updatedProfile.id,
            contentType: 'profile_picture',
            userId
          });
        } catch (reportError) {
          // Ne pas bloquer le processus si la création du signalement échoue
          logger.error('Erreur lors de la création du signalement différé pour la photo de profil', {
            error: reportError instanceof Error ? reportError.message : 'Unknown error',
            userId
          });
        }
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis supprimé après mise à jour de la photo', { cacheKey, cacheKey_deux });

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId || '',
        'profile_photo_update',
        undefined,
        'user_profile',
        {
          action: 'Mise à jour de la photo de profil',
          photo_url: photoUrl
        },
        getIpFromRequest(req)
      );

      res.json({
        message: 'Photo de profil mise à jour avec succès.',
        photo_url: photoUrl
      });
    } catch (uploadError) {
      logger.error('Erreur lors de l\'upload ou de la mise à jour', { error: uploadError });
      res.status(500).json({
        message: uploadError instanceof Error ? uploadError.message : 'Erreur lors de la mise à jour de la photo de profil',
        error: process.env.NODE_ENV === 'development' ? uploadError : undefined
      });
    }
  }

  async deleteBannerPhoto(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        logger.error('Tentative de suppression de bannière sans utilisateur authentifié');
        return res.status(401).json({ message: 'Non autorisé' });
      }

      // Supprimer les fichiers de bannière du stockage
      await deleteBannerPhoto(userId);

      // Mettre à jour la base de données pour supprimer la référence à la bannière et reset la position
      const { error: updateError } = await supabase
        .from('user_profil')
        .update({
          banner_url: null,
          banner_position: 'center',
          banner_position_offset: 0,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la base de données après suppression de la bannière', { error: updateError, userId });
        throw new Error('Erreur lors de la mise à jour de la base de données');
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis supprimé après suppression de la bannière', { cacheKey, cacheKey_deux });

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId,
        'profile_banner_delete',
        undefined,
        'user_profile',
        {
          action: 'Suppression de la bannière de profil'
        },
        getIpFromRequest(req)
      );

      res.json({
        message: 'Bannière de profil supprimée avec succès.',
        success: true,
        banner_position: 'center',
        banner_position_offset: 0
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la bannière', { error });
      res.status(500).json({
        message: error instanceof Error ? error.message : 'Erreur lors de la suppression de la bannière de profil',
        error: process.env.NODE_ENV === 'development' ? error : undefined,
        success: false
      });
    }
  }

  async updateBannerPhoto(req: FileRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        logger.error('Tentative d\'upload sans utilisateur authentifié');
        res.status(401).json({ message: 'Non autorisé' });
        return;
      }

      const banner = req.files?.banner as UploadedFile;
      if (!banner) {
        logger.error('Aucune bannière fournie dans la requête');
        res.status(400).json({ message: 'Aucune bannière fournie' });
        return;
      }

      // Vérification du type MIME
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedMimeTypes.includes(banner.mimetype)) {
        logger.error('Type de fichier non autorisé', { mimetype: banner.mimetype });
        return res.status(400).json({ message: 'Format de fichier non supporté. Utilisez JPG, PNG, WEBP, AVIF ou HEIC.' });
      }

      // Vérification de la taille
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (banner.size > maxSize) {
        logger.error('Fichier trop volumineux', { size: banner.size });
        return res.status(400).json({ message: 'La taille du fichier ne doit pas dépasser 5MB.' });
      }

      // Lire le fichier temporaire
      const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
        require('fs').readFile(banner.tempFilePath, (err: any, data: Buffer) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      // Upload de la nouvelle bannière
      const bannerUrl = await uploadBannerPhoto(userId || '', fileBuffer, banner.mimetype);

      // Récupérer la position demandée
      let bannerPosition: 'top' | 'center' | 'bottom' = 'center';
      if (req.body.position && ['top', 'center', 'bottom'].includes(req.body.position)) {
        bannerPosition = req.body.position;
      }

      let bannerPositionOffset = 0;
      if (typeof req.body.position_offset === 'string' && !isNaN(Number(req.body.position_offset))) {
        bannerPositionOffset = Number(req.body.position_offset);
        if (bannerPositionOffset < -200) bannerPositionOffset = -200;
        if (bannerPositionOffset > 200) bannerPositionOffset = 200;
      }

      // Mise à jour de l'URL et de la position dans la base de données
      const { data: updatedProfile, error: updateError } = await supabase
        .from('user_profil')
        .update({
          banner_url: bannerUrl,
          banner_position: bannerPosition,
          banner_position_offset: bannerPositionOffset,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (updateError) {
        logger.error('Erreur lors de la mise à jour de la bannière dans la base de données', { error: updateError, userId });
        throw new Error('Erreur lors de la mise à jour de la bannière dans la base de données');
      }

      // Créer un signalement différé si l'image a été modérée avec un ID temporaire
      try {
        // Extraire l'ID temporaire de l'image à partir des logs ou des métadonnées
        const tempImageId = req.body.tempImageId || `image-temp-${Date.now()}`;

        // Appeler directement le service de modération
        await contentModerationService.createDeferredImageReport(
          tempImageId,
          updatedProfile.id,
          'banner_picture',
          userId || ''
        );

        logger.info('Signalement différé créé avec succès pour la bannière', {
          tempImageId,
          permanentImageId: updatedProfile.id,
          contentType: 'banner_picture',
          userId
        });
      } catch (reportError) {
        // Ne pas bloquer le processus si la création du signalement échoue
        logger.error('Erreur lors de la création du signalement différé pour la bannière', {
          error: reportError instanceof Error ? reportError.message : 'Unknown error',
          userId
        });
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      logger.info('Cache Redis supprimé après mise à jour de la bannière', { cacheKey, cacheKey_deux });

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId || '',
        'profile_banner_update',
        undefined,
        'user_profile',
        {
          action: 'Mise à jour de la bannière de profil',
          banner_url: bannerUrl,
          banner_position: bannerPosition,
          banner_position_offset: bannerPositionOffset
        },
        getIpFromRequest(req)
      );

      res.json({
        message: 'Bannière de profil mise à jour avec succès.',
        banner_url: bannerUrl,
        banner_position: bannerPosition,
        banner_position_offset: bannerPositionOffset
      });
    } catch (uploadError) {
      logger.error('Erreur lors de l\'upload ou de la mise à jour de la bannière', { error: uploadError });
      res.status(500).json({
        message: uploadError instanceof Error ? uploadError.message : 'Erreur lors de la mise à jour de la bannière de profil',
        error: process.env.NODE_ENV === 'development' ? uploadError : undefined
      });
    }
  }

  // Création d'une galerie dans le profil de l'utilisateur
  async createGallery(req: FileRequest, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      logger.info('Début de createGallery', { userId, body: req.body });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de création de galerie sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { name, description } = req.body as GalleryData;
      const file = req.files?.coverImage as UploadedFile;

      if (!name) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Nom de la galerie manquant');
        res.status(400).json({
          message: 'Le nom de la galerie est obligatoire',
          success: false,
          toastType: 'error'
        });
      }

      // Capitaliser le nom et la description
      const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
      const capitalizedDescription = description ? description.charAt(0).toUpperCase() + description.slice(1) : description;

      let coverImageUrl = null;

      // Gestion de l'upload de l'image de couverture
      if (file) {
        // Vérification du type MIME
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          logger.error(LogEventType.VALIDATION_ERROR, 'Type de fichier non autorisé', { mimetype: file.mimetype });
          res.status(400).json({
            message: 'Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.',
            success: false,
            toastType: 'error'
          });
        }

        // Vérification de la taille
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          logger.error(LogEventType.VALIDATION_ERROR, 'Fichier trop volumineux', { size: file.size });
          res.status(400).json({
            message: 'La taille du fichier ne doit pas dépasser 5MB.',
            success: false,
            toastType: 'error'
          });
        }

        // Lire le fichier temporaire
        const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
          require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
            if (err) reject(err);
            else resolve(data);
          });
        });

        try {
          // Créer d'abord la galerie pour avoir l'ID
          const newGallery = {
            user_id: userId,
            name: capitalizedName,
            description: capitalizedDescription,
            status: 'actif',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { data: galleryData, error: galleryError } = await supabase
            .from('user_gallery')
            .insert([newGallery])
            .select()
            .single();

          if (galleryError) {
            logger.error(LogEventType.DB_ERROR, 'Erreur lors de la création de la galerie', { error: galleryError, userId });
            return res.status(500).json({
              message: 'Erreur lors de la création de la galerie',
              success: false,
              toastType: 'error'
            });
          }

          // Upload de la photo de couverture
          coverImageUrl = await uploadGalleryPhoto(userId || '', fileBuffer, file.mimetype, galleryData.id, name);

          // Mettre à jour la galerie avec l'URL de la photo de couverture
          const { data: updatedGallery, error: updateError } = await supabase
            .from('user_gallery')
            .update({ cover_image: coverImageUrl })
            .eq('id', galleryData.id)
            .select()
            .single();

          if (updateError) {
            logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de la photo de couverture', { error: updateError, userId });
            return res.status(500).json({
              message: 'Erreur lors de la mise à jour de la photo de couverture',
              success: false,
              toastType: 'error'
            });
          }

          logger.info(LogEventType.USER_UPDATE, 'Galerie créée avec succès', { userId, galleryId: updatedGallery.id });

        // Supprimer le cache Redis
        const cacheKey = `user:${userId}`;
        const cacheKey_deux = `user_deux:${userId}`;
        await redis.del(cacheKey);
        await redis.del(cacheKey_deux);

        // Journaliser l'action de création de galerie
        await logUserActivity(
          userId || '',
          'gallery_create',
          updatedGallery.id,
          'gallery',
          {
            gallery_name: updatedGallery.name,
            has_cover_image: !!updatedGallery.cover_image
          },
          getIpFromRequest(req)
        );

        res.status(201).json({
            message: 'Galerie créée avec succès',
            success: true,
            gallery: updatedGallery
          });

        } catch (uploadError) {
          logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de l\'upload de la photo de couverture', { error: uploadError, userId });
          return res.status(500).json({
            message: 'Erreur lors de l\'upload de la photo de couverture',
            success: false,
            toastType: 'error'
          });
        }
      } else {
        // Création de la galerie sans photo de couverture
        const newGallery = {
          user_id: userId,
          name: capitalizedName,
          description: capitalizedDescription,
          status: 'actif',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data, error } = await supabase
          .from('user_gallery')
          .insert([newGallery])
          .select()
          .single();

        if (error) {
          logger.error(LogEventType.DB_ERROR, 'Erreur lors de la création de la galerie', { error, userId });
          return res.status(500).json({
            message: 'Erreur lors de la création de la galerie',
            success: false,
            toastType: 'error'
          });
        }

        logger.info(LogEventType.USER_UPDATE, 'Galerie créée avec succès', { userId, galleryId: data.id });

        // Supprimer le cache Redis
        const cacheKey = `user:${userId}`;
        const cacheKey_deux = `user_deux:${userId}`;
        await redis.del(cacheKey);
        await redis.del(cacheKey_deux);

        // Journaliser l'action de création de galerie
        await logUserActivity(
          userId || '',
          'gallery_create',
          data.id,
          'gallery',
          {
            gallery_name: data.name,
            has_cover_image: false
          },
          getIpFromRequest(req)
        );

        res.status(201).json({
          message: 'Galerie créée avec succès',
          success: true,
          gallery: data
        });
      }
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la création de la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la création de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updateGallery(req: FileRequest, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      logger.info('Début de updateGallery dans le backend', { userId, galleryId, body: req.body });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de mise à jour de galerie sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { name, description } = req.body as GalleryData;
      const file = req.files?.coverImage as UploadedFile | undefined;
      const shouldDeleteCoverImage = req.body.deleteCoverImage === 'true';
      let coverImageUrl = null;

      if (!name) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Nom de la galerie manquant');
        res.status(400).json({
          message: 'Le nom de la galerie est obligatoire',
          success: false,
          toastType: 'error'
        });
      }

      // Capitaliser le nom et la description
      const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
      const capitalizedDescription = description ? description.charAt(0).toUpperCase() + description.slice(1) : description;

      // Récupérer l'ancienne photo de couverture
      const { data: galleryData } = await supabase
        .from('user_gallery')
        .select('cover_image')
        .eq('id', galleryId)
        .eq('user_id', userId)
        .single();

      // Si une ancienne image existe et qu'on doit la supprimer (soit pour la remplacer, soit pour la retirer)
      if (galleryData?.cover_image &&
          galleryData.cover_image !== DEFAULT_IMAGE_URL &&
          (file || shouldDeleteCoverImage)) {
        const oldCoverImagePath = galleryData.cover_image.split('galerie_realisation_client/')[1];
        if (oldCoverImagePath) {
          await supabase.storage.from('galerie_realisation_client').remove([oldCoverImagePath]);
          logger.info('Ancienne image de couverture supprimée', { oldCoverImagePath, galleryId });
        }
      }

      // Gestion de l'upload de la nouvelle image de couverture si présente
      if (file) {
        // Vérification du type MIME
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          logger.error(LogEventType.VALIDATION_ERROR, 'Type de fichier non autorisé', { mimetype: file.mimetype });
          res.status(400).json({
            message: 'Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.',
            success: false,
            toastType: 'error'
          });
        }

        // Vérification de la taille
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          logger.error(LogEventType.VALIDATION_ERROR, 'Fichier trop volumineux', { size: file.size });
          res.status(400).json({
            message: 'La taille du fichier ne doit pas dépasser 5MB.',
            success: false,
            toastType: 'error'
          });
        }

        try {
          // Lire le fichier temporaire
          const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
            require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
              if (err) reject(err);
              else resolve(data);
            });
          });

          // Upload de la nouvelle photo de couverture
          coverImageUrl = await uploadGalleryPhoto(userId || '', fileBuffer, file.mimetype, galleryId, name);
        } catch (uploadError) {
          logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de l\'upload de la photo de couverture', { error: uploadError, userId, galleryId });
          return res.status(500).json({
            message: 'Erreur lors de l\'upload de la photo de couverture',
            success: false,
            toastType: 'error'
          });
        }
      }

      const updatedGallery: {
        name: string;
        description?: string;
        cover_image?: string;
        updated_at: string;
      } = {
        name: capitalizedName,
        description: capitalizedDescription,
        updated_at: new Date().toISOString()
      };

      // Gérer l'image de couverture uniquement si elle est modifiée
      if (coverImageUrl || shouldDeleteCoverImage) {
        updatedGallery.cover_image = coverImageUrl || DEFAULT_IMAGE_URL;
      }

      const { data, error } = await supabase
        .from('user_gallery')
        .update(updatedGallery)
        .eq('id', galleryId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de la galerie', { error, userId, galleryId });
        return res.status(500).json({
          message: 'Erreur lors de la mise à jour de la galerie',
          success: false,
          toastType: 'error'
        });
      }

      logger.info(LogEventType.USER_UPDATE, 'Galerie mise à jour avec succès', { userId, galleryId });

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);

      res.json({
        message: 'Galerie mise à jour avec succès',
        success: true,
        gallery: data
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la mise à jour de la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la mise à jour de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async deleteGallery(req: FileRequest, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      logger.info('Début de deleteGallery', { userId, galleryId });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de suppression de galerie sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer les informations de la galerie et ses photos
      const { data: galleryData, error: fetchError } = await supabase
        .from('user_gallery')
        .select(`
          *,
          user_gallery_photos (
            photo_url
          )
        `)
        .eq('id', galleryId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !galleryData) {
        logger.error('Erreur lors de la récupération de la galerie', { error: fetchError });
        return res.status(404).json({
          message: 'Galerie non trouvée',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer toutes les photos du bucket
      const photosToDelete: string[] = [];

      // Ajouter les photos de la galerie
      if (galleryData.user_gallery_photos && Array.isArray(galleryData.user_gallery_photos)) {
        for (const photo of galleryData.user_gallery_photos) {
          if (photo.photo_url) {
            const photoPath = extractFilePathFromUrl(photo.photo_url, 'galerie_realisation_client');
            if (photoPath) {
              photosToDelete.push(photoPath);
              logger.info('Photo ajoutée à la liste de suppression', { photoUrl: photo.photo_url, photoPath });
            }
          }
        }
      }

      // Supprimer l'image de couverture si elle existe et n'est pas l'image par défaut
      if (galleryData.cover_image && galleryData.cover_image !== DEFAULT_IMAGE_URL) {
        const coverImagePath = extractFilePathFromUrl(galleryData.cover_image, 'galerie_realisation_client');
        if (coverImagePath) {
          photosToDelete.push(coverImagePath);
          logger.info('Image de couverture ajoutée à la liste de suppression', { coverImage: galleryData.cover_image, coverImagePath });
        }
      }

      // Supprimer toutes les photos du bucket en une seule opération
      if (photosToDelete.length > 0) {
        logger.info('Suppression des fichiers du bucket', {
          galleryId,
          userId,
          filesCount: photosToDelete.length,
          files: photosToDelete
        });

        const { error: deleteStorageError } = await supabase.storage
          .from('galerie_realisation_client')
          .remove(photosToDelete);

        if (deleteStorageError) {
          logger.error('Erreur lors de la suppression des photos du bucket', {
            error: deleteStorageError,
            galleryId,
            userId,
            photosToDelete
          });
          // Ne pas arrêter le processus, continuer avec la suppression de la base de données
        } else {
          logger.info('Photos supprimées avec succès du bucket', {
            galleryId,
            userId,
            deletedCount: photosToDelete.length
          });
        }
      } else {
        logger.info('Aucune photo à supprimer du bucket', { galleryId, userId });
      }

      // Supprimer la galerie de la base de données (les photos seront supprimées automatiquement grâce à la contrainte ON DELETE CASCADE)
      const { error: deleteError } = await supabase
        .from('user_gallery')
        .delete()
        .eq('id', galleryId)
        .eq('user_id', userId);

      if (deleteError) {
        logger.error('Erreur lors de la suppression de la galerie', { error: deleteError });
        return res.status(500).json({
          message: 'Erreur lors de la suppression de la galerie',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);

      // Journaliser l'action de suppression de galerie
      await logUserActivity(
        userId || '',
        'gallery_delete',
        galleryId,
        'gallery',
        {
          gallery_name: galleryData.name,
          photos_deleted: galleryData.user_gallery_photos?.length || 0,
          storage_files_deleted: photosToDelete.length
        },
        getIpFromRequest(req)
      );

      logger.info('Galerie supprimée avec succès', {
        galleryId,
        userId,
        galleryName: galleryData.name,
        photosDeleted: galleryData.user_gallery_photos?.length || 0,
        storageFilesDeleted: photosToDelete.length
      });

      res.json({
        message: 'Galerie supprimée avec succès',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error('Erreur lors de la suppression de la galerie', { error, userId });
      return res.status(500).json({
        message: 'Erreur lors de la suppression de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async getGalleries(req: Request, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      logger.info('Début de getGalleries', { userId });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de récupération de galeries sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { data: galleries, error: countError } = await supabase
        .from('user_gallery')
        .select(`
          *,
          user_gallery_photos!gallery_id(count)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (countError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des galeries', { error: countError, userId });
        return res.status(500).json({
          message: 'Erreur lors de la récupération des galeries',
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer le compte exact des photos pour chaque galerie
      const galleriesWithCounts = await Promise.all(
        galleries.map(async (gallery) => {
          const { count } = await supabase
            .from('user_gallery_photos')
            .select('*', { count: 'exact', head: true })
            .eq('gallery_id', gallery.id);

          return {
            id: gallery.id,
            name: gallery.name,
            description: gallery.description,
            cover_image: gallery.cover_image,
            status: gallery.status,
            imagesCount: count || 0,
            createdAt: gallery.created_at
          };
        })
      );

      logger.info('Galeries récupérées avec succès 1', {
        userId,
        count: galleriesWithCounts.length,
        photoCounts: galleriesWithCounts.map(g => ({ gallery: g.name, photos: g.imagesCount }))
      });

      res.json({
        success: true,
        galleries: galleriesWithCounts
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la récupération des galeries', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la récupération des galeries',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Nouvelle fonction pour l'upload multiple de photos dans une galerie
  async addPhotosToGallery(req: FileRequest, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      logger.info('Début de addPhotosToGallery', { userId, galleryId });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'ajout de photos sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // Gérer les fichiers multiples
      const photos = req.files?.photo;
      if (!photos) {
        logger.warn('Aucune photo fournie');
        return res.status(400).json({
          message: 'Aucune photo fournie',
          success: false,
          toastType: 'error'
        });
      }

      // Convertir en tableau si c'est un seul fichier
      const photoArray = Array.isArray(photos) ? photos : [photos];
      logger.info('Nombre de photos à traiter:', { count: photoArray.length });

      // Vérifier le nombre total de photos existantes dans la galerie
      const { count } = await supabase
        .from('user_gallery_photos')
        .select('*', { count: 'exact' })
        .eq('gallery_id', galleryId);

      const currentCount = count || 0;
      const maxPhotosPerGallery = 20; // Limite de 20 photos par galerie

      if (currentCount + photoArray.length > maxPhotosPerGallery) {
        return res.status(400).json({
          message: `Vous ne pouvez pas ajouter plus de ${maxPhotosPerGallery} photos par galerie.`,
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer les informations de la galerie
      const { data: galleryData } = await supabase
        .from('user_gallery')
        .select('name, user_id')
        .eq('id', galleryId)
        .single();

      if (!galleryData || galleryData.user_id !== userId) {
        logger.error('Galerie non trouvée ou n\'appartenant pas à l\'utilisateur', { galleryId, userId });
        return res.status(404).json({
          message: 'Galerie non trouvée',
          success: false,
          toastType: 'error'
        });
      }

      const uploadedPhotos = [];
      const failedPhotos = [];
      let orderIndex = currentCount;

      for (const [index, file] of photoArray.entries()) {
        try {
          logger.info(`Traitement de la photo ${index + 1}/${photoArray.length}`, { 
            fileName: file.name, 
            size: file.size, 
            mimetype: file.mimetype 
          });

          // Vérification du type MIME
          const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
          if (!allowedMimeTypes.includes(file.mimetype)) {
            logger.error('Type de fichier non autorisé :', { mimetype: file.mimetype, fileName: file.name });
            failedPhotos.push({ fileName: file.name, error: 'Type de fichier non autorisé' });
            continue;
          }

          // Vérification de la taille
          const maxSize = 5 * 1024 * 1024; // 5MB
          if (file.size > maxSize) {
            logger.error('Fichier trop volumineux', { size: file.size, fileName: file.name });
            failedPhotos.push({ fileName: file.name, error: 'Fichier trop volumineux (max 5MB)' });
            continue;
          }

          // Lire le fichier temporaire
          const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
            require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
              if (err) {
                logger.error('Erreur lors de la lecture du fichier temporaire', { error: err, fileName: file.name });
                reject(new Error(`Erreur lors de la lecture du fichier: ${err.message}`));
              } else {
                resolve(data);
              }
            });
          });

          // Upload de la photo
          const photoUrl = await uploadGalleryPhoto(userId, fileBuffer, file.mimetype, galleryId, galleryData.name);

          const newPhoto = {
            gallery_id: galleryId,
            user_id: userId,
            photo_url: photoUrl,
            order_index: orderIndex++,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { data, error } = await supabase
            .from('user_gallery_photos')
            .insert([newPhoto])
            .select()
            .single();

          if (error) {
            logger.error('Erreur lors de l\'ajout de la photo à la galerie', { 
              error: error.message || error, 
              userId, 
              galleryId, 
              fileName: file.name 
            });
            failedPhotos.push({ fileName: file.name, error: 'Erreur lors de l\'enregistrement en base' });
            continue;
          }

          // Créer un signalement différé seulement si l'image a été modérée avec un ID temporaire réel
          if (req.body?.tempImageId && typeof req.body.tempImageId === 'string' && req.body.tempImageId.trim() !== '') {
            try {
              await contentModerationService.createDeferredImageReport(
                req.body.tempImageId,
                data.id,
                'gallery',
                userId || ''
              );

              logger.info('Signalement différé créé avec succès pour la photo de galerie', {
                tempImageId: req.body.tempImageId,
                permanentImageId: data.id,
                contentType: 'gallery',
                userId
              });
            } catch (reportError) {
              logger.error('Erreur lors de la création du signalement différé pour la photo de galerie', {
                error: reportError instanceof Error ? reportError.message : 'Unknown error',
                userId,
                photoId: data.id
              });
            }
          }

          uploadedPhotos.push(data);
          logger.info('Photo de galerie uploadée avec succès', { 
            userId, 
            galleryId, 
            photoId: data.id,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.mimetype.split('/')[1],
            publicUrl: photoUrl,
            storageId: userId
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
          const errorStack = error instanceof Error ? error.stack : undefined;
          logger.error('Erreur lors du traitement d\'une photo', { 
            error: errorMessage, 
            errorStack,
            errorType: typeof error,
            errorConstructor: error?.constructor?.name,
            userId, 
            galleryId, 
            fileName: file.name,
            fileIndex: index
          });
          failedPhotos.push({ fileName: file.name, error: errorMessage });
          continue;
        }
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);

      // Journaliser l'action
      await logUserActivity(
        userId,
        'gallery_photos_add_multiple',
        galleryId,
        'gallery_photo',
        {
          gallery_name: galleryData.name,
          photos_count: uploadedPhotos.length,
          failed_count: failedPhotos.length
        },
        getIpFromRequest(req)
      );

      // Retourner le résultat avec les détails des succès et échecs
      if (uploadedPhotos.length === 0) {
        return res.status(400).json({
          message: 'Aucune photo n\'a pu être uploadée',
          success: false,
          toastType: 'error',
          failedPhotos
        });
      }

      const message = failedPhotos.length > 0 
        ? `${uploadedPhotos.length} photo(s) ajoutée(s) avec succès, ${failedPhotos.length} échec(s)`
        : `${uploadedPhotos.length} photo(s) ajoutée(s) avec succès`;

      return res.status(201).json({
        message,
        success: true,
        photos: uploadedPhotos,
        failedPhotos: failedPhotos.length > 0 ? failedPhotos : undefined
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      logger.error('Erreur lors de l\'ajout des photos à la galerie', { 
        error: errorMessage, 
        userId,
        stack: error instanceof Error ? error.stack : undefined
      });
      return res.status(500).json({
        message: 'Erreur lors de l\'ajout des photos à la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Fonction d'upload unique conservée pour la compatibilité
  async addPhotoToGallery(req: FileRequest, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      logger.info('Début de addPhotoToGallery', { userId, galleryId, body: req.body });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'ajout de photo sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const file = req.files?.photo as UploadedFile;

      if (!file) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Aucune photo fournie');
        return res.status(400).json({
          message: 'Aucune photo fournie',
          success: false,
          toastType: 'error'
        });
      }

      // Vérification du type MIME
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedMimeTypes.includes(file.mimetype)) {
        logger.error(LogEventType.VALIDATION_ERROR, 'Type de fichier non autorisé', { mimetype: file.mimetype });
        return res.status(400).json({
          message: 'Format de fichier non supporté. Utilisez JPG, PNG ou WEBP.',
          success: false,
          toastType: 'error'
        });
      }

      // Vérification de la taille
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        logger.error(LogEventType.VALIDATION_ERROR, 'Fichier trop volumineux', { size: file.size });
        return res.status(400).json({
          message: 'Le fichier est trop volumineux. Maximum 5MB.',
          success: false,
          toastType: 'error'
        });
      }

      try {
        const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
          fs.readFile(file.tempFilePath, (err, data) => {
            if (err) {
              logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la lecture du fichier', { error: err });
              reject(err);
              return;
            }
            resolve(data);
          });
        });

        // Upload de la photo
        const { data: galleryData } = await supabase
          .from('user_gallery')
          .select('name')
          .eq('id', galleryId)
          .single();

        if (!galleryData) {
          logger.error(LogEventType.DB_ERROR, 'Galerie non trouvée', { galleryId });
          return res.status(404).json({
            message: 'Galerie non trouvée',
            success: false,
            toastType: 'error'
          });
        }

        const photoUrl = await uploadGalleryPhoto(userId, fileBuffer, file.mimetype, galleryId, galleryData.name);

        const newPhoto = {
          gallery_id: galleryId,
          user_id: userId,
          photo_url: photoUrl,
          caption: req.body?.caption || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data, error } = await supabase
          .from('user_gallery_photos')
          .insert([newPhoto])
          .select()
          .single();

        if (error) {
          logger.error(LogEventType.DB_ERROR, 'Erreur lors de l\'ajout de la photo à la galerie', { error, userId, galleryId });
          return res.status(500).json({
            message: 'Erreur lors de l\'ajout de la photo à la galerie',
            success: false,
            toastType: 'error'
          });
        }

        // Supprimer le fichier temporaire
        try {
          await fs.promises.unlink(file.tempFilePath);
        } catch (unlinkError) {
          logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la suppression du fichier temporaire', { error: unlinkError });
        }

        // Supprimer le cache Redis
        const cacheKey = `user:${userId}`;
        const cacheKey_deux = `user_deux:${userId}`;
        await redis.del(cacheKey);
        await redis.del(cacheKey_deux);

        await logUserActivity(
          userId,
          'gallery_photo_add',
          galleryId,
          'gallery_photo',
          {
            photo_id: data.id,
            gallery_name: galleryData.name,
            has_caption: req.body?.caption ? true : false
          },
          getIpFromRequest(req)
        );

        // Créer un signalement différé seulement si l'image a été modérée avec un ID temporaire réel
        if (req.body?.tempImageId && typeof req.body.tempImageId === 'string' && req.body.tempImageId.trim() !== '') {
          try {
            await contentModerationService.createDeferredImageReport(
              req.body.tempImageId,
              data.id,
              'gallery',
              userId || ''
            );

            logger.info('Signalement différé créé avec succès pour la photo de galerie', {
              tempImageId: req.body.tempImageId,
              permanentImageId: data.id,
              contentType: 'gallery',
              userId
            });
          } catch (reportError) {
            // Ne pas bloquer le processus si la création du signalement échoue
            logger.error('Erreur lors de la création du signalement différé pour la photo de galerie', {
              error: reportError instanceof Error ? reportError.message : 'Unknown error',
              userId,
              photoId: data.id
            });
          }
        }

        logger.info('Photo ajoutée avec succès', { photoUrl });
        return res.status(201).json({
          message: 'Photo ajoutée avec succès',
          success: true,
          toastType: 'success',
          photo: data, // Obligatoire pour le frontend afin de recevoir la photo ajoutée et renvoyer l'id de la photo
          data: {
            photo_url: photoUrl,
            caption: req.body?.caption || null
          }
        });
      } catch (error) {
        throw error;
      }
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de l\'ajout de la photo à la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de l\'ajout de la photo à la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async updatePhotoInGallery(req: Request, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const photoId = req.params.photoId;
      const galleryId = req.params.id;
      logger.info('Début de updatePhotoInGallery', { userId, photoId, galleryId, body: req.body });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de mise à jour de photo sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { caption, order_index } = req.body;

      const updatedPhoto: any = {
        updated_at: new Date().toISOString()
      };

      // Mise à jour de la légende si fournie
      if (caption !== undefined) {
        updatedPhoto.caption = caption;

        const { data, error } = await supabase
          .from('user_gallery_photos')
          .update(updatedPhoto)
          .eq('id', photoId)
          .eq('gallery_id', galleryId)
          .select()
          .single();

        if (error) {
          logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de la légende', { error });
          return res.status(500).json({
            message: 'Erreur lors de la mise à jour de la légende',
            success: false,
            toastType: 'error'
          });
        }

        return res.json({
          message: 'Légende mise à jour avec succès',
          success: true,
          photo: data
        });
      }

      // Si l'ordre est modifié, réinitialiser l'ordre de toutes les photos
      if (order_index !== undefined) {
        // 1. Récupérer toutes les photos de la galerie dans l'ordre actuel
        const { data: allPhotos } = await supabase
          .from('user_gallery_photos')
          .select('id, order_index')
          .eq('gallery_id', galleryId)
          .order('order_index', { ascending: true, nullsFirst: false });

        if (!allPhotos) {
          logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des photos');
          return res.status(500).json({
            message: 'Erreur lors de la réorganisation des photos',
            success: false,
            toastType: 'error'
          });
        }

        // 2. Créer le nouvel ordre en déplaçant la photo à sa nouvelle position
        const oldIndex = allPhotos.findIndex(p => p.id === photoId);
        if (oldIndex !== -1) {
          const photo = allPhotos.splice(oldIndex, 1)[0];
          allPhotos.splice(order_index, 0, photo);
        }

        // 3. Mettre à jour l'ordre de toutes les photos
        for (let i = 0; i < allPhotos.length; i++) {
          const { error: updateError } = await supabase
            .from('user_gallery_photos')
            .update({ order_index: i + 1 })
            .eq('id', allPhotos[i].id)
            .eq('gallery_id', galleryId);

          if (updateError) {
            logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de l\'ordre', { error: updateError });
            return res.status(500).json({
              message: 'Erreur lors de la réorganisation des photos',
              success: false,
              toastType: 'error'
            });
          }
        }

        return res.json({
          message: 'Photos réorganisées avec succès',
          success: true
        });
      }

      res.status(400).json({
        message: 'Aucune modification demandée',
        success: false,
        toastType: 'error'
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la mise à jour de la photo dans la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la mise à jour de la photo dans la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async deletePhotoFromGallery(req: Request, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const photoId = req.params.photoId;
      const galleryId = req.params.id;
      logger.info('Début de deletePhotoFromGallery', { userId, photoId, galleryId });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de suppression de photo sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { data: photoData, error: selectError } = await supabase
        .from('user_gallery_photos')
          .select('photo_url')
        .eq('id', photoId)
        .eq('gallery_id', galleryId)
          .single();

      if (selectError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération de la photo à supprimer', { error: selectError, userId, photoId, galleryId });
        return res.status(500).json({
          message: 'Erreur lors de la récupération de la photo à supprimer',
          success: false,
          toastType: 'error'
        });
      }

      if (photoData?.photo_url) {
        const filePath = extractFilePathFromUrl(photoData.photo_url, 'galerie_realisation_client');
        if (filePath) {
          logger.info('Suppression de la photo du bucket', { photoUrl: photoData.photo_url, filePath, photoId, galleryId });

          const { error: storageError } = await supabase.storage
            .from('galerie_realisation_client')
            .remove([filePath]);

          if (storageError) {
            logger.error(LogEventType.DB_ERROR, 'Erreur lors de la suppression de la photo dans le storage', {
              error: storageError,
              userId,
              photoId,
              galleryId,
              photoUrl: photoData.photo_url,
              filePath
            });
            // Ne pas arrêter le processus, continuer avec la suppression de la base de données
          } else {
            logger.info('Photo supprimée avec succès du bucket', { photoId, galleryId, filePath });
          }
        } else {
          logger.warn('Impossible d\'extraire le chemin de la photo, suppression du storage ignorée', {
            photoUrl: photoData.photo_url,
            photoId,
            galleryId
          });
        }
      }

      const { error } = await supabase
        .from('user_gallery_photos')
        .delete()
        .eq('id', photoId)
        .eq('gallery_id', galleryId);

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la suppression de la photo de la galerie', { error, userId, photoId, galleryId });
        return res.status(500).json({
          message: 'Erreur lors de la suppression de la photo de la galerie',
          success: false,
          toastType: 'error'
        });
      }

      logger.info(LogEventType.USER_UPDATE, 'Photo supprimée de la galerie avec succès', { userId, photoId, galleryId });

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);

      // Journaliser l'action de suppression de photo de la galerie
      await logUserActivity(
        userId || '',
        'gallery_photo_delete',
        galleryId,
        'gallery_photo',
        {
          photo_id: photoId,
          photo_url: photoData?.photo_url
        },
        getIpFromRequest(req)
      );

      res.status(204).send();
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la suppression de la photo de la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la suppression de la photo de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async toggleGalleryStatus(req: Request, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      const { status } = req.body;

      logger.info('Début de toggleGalleryStatus', { userId, galleryId, status });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de modification du statut de galerie sans authentification');
        return res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      if (!status || !['actif', 'inactif'].includes(status)) {
        logger.warn(LogEventType.VALIDATION_ERROR, 'Statut de galerie invalide', { status });
        return res.status(400).json({
          message: 'Statut invalide. Valeurs acceptées: actif, inactif',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que la galerie appartient à l'utilisateur
      const { data: galleryData, error: galleryError } = await supabase
        .from('user_gallery')
        .select('name, status')
        .eq('id', galleryId)
        .eq('user_id', userId)
        .single();

      if (galleryError || !galleryData) {
        logger.error(LogEventType.DB_ERROR, 'Galerie non trouvée ou n\'appartenant pas à l\'utilisateur', { error: galleryError, userId, galleryId });
        return res.status(404).json({
          message: 'Galerie non trouvée ou n\'appartenant pas à l\'utilisateur',
          success: false,
          toastType: 'error'
        });
      }

      // Si le statut est déjà celui demandé, ne rien faire
      if (galleryData.status === status) {
        return res.json({
          message: `La galerie est déjà ${status === 'actif' ? 'activée' : 'désactivée'}`,
          success: true,
          toastType: 'info',
          gallery: {
            id: galleryId,
            status: status
          }
        });
      }

      // Si on essaie d'activer une galerie inactive, vérifier les limites d'abonnement
      if (status === 'actif' && galleryData.status === 'inactif') {
        // Récupérer les limites d'abonnement de l'utilisateur
        const subscriptionLimits = await getUserSubscriptionLimits(userId);
        const galleryLimit = subscriptionLimits.galleriesLimit || 1; // Valeur par défaut pour le plan gratuit

        // Compter le nombre de galeries actives
        const { data: activeGalleries, error: countError } = await supabase
          .from('user_gallery')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'actif');

        if (countError) throw countError;

        // Vérifier si l'utilisateur a atteint sa limite de galeries actives
        if (activeGalleries && activeGalleries.length >= galleryLimit) {
          return res.status(403).json({
            error: 'Limite de galeries atteinte',
            message: `Vous ne pouvez pas avoir plus de ${galleryLimit} galerie(s) active(s) avec votre abonnement actuel.`,
            galleryLimit,
            activeCount: activeGalleries.length
          });
        }
      }

      // Mettre à jour le statut de la galerie
      const { data, error } = await supabase
        .from('user_gallery')
        .update({
          status: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', galleryId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la modification du statut de la galerie', { error, userId, galleryId });
        return res.status(500).json({
          message: 'Erreur lors de la modification du statut de la galerie',
          success: false,
          toastType: 'error'
        });
      }

      // Supprimer le cache Redis
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user_deux:${userId}`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);

      // Récupérer le slug de l'utilisateur pour invalider le cache spécifique
      const { data: userProfil } = await supabase
        .from('user_profil')
        .select('slug')
        .eq('user_id', userId)
        .single();

      // Déchiffrer les données de profil
      const decryptedUserProfil = userProfil ? await decryptProfilDataAsync(userProfil) : null;

      if (decryptedUserProfil?.slug) {
        await redis.del(`user_gallery_user_specific:${decryptedUserProfil.slug}`);
      }

      // Journaliser l'action de modification du statut de la galerie
      await logUserActivity(
        userId || '',
        status === 'actif' ? 'gallery_activate' : 'gallery_deactivate',
        galleryId,
        'gallery',
        {
          gallery_name: galleryData.name,
          old_status: galleryData.status,
          new_status: status
        },
        getIpFromRequest(req)
      );

      res.json({
        message: `Galerie ${status === 'actif' ? 'activée' : 'désactivée'} avec succès`,
        success: true,
        toastType: 'success',
        gallery: data
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la modification du statut de la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la modification du statut de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async getPhotosFromGallery(req: Request, res: Response) {
    let userId: string | undefined;
    try {
      userId = req.user?.userId;
      const galleryId = req.params.id;
      logger.info('Début de getPhotosFromGallery', { userId, galleryId });

      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative de récupération de photos sans authentification');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { data, error } = await supabase
        .from('user_gallery_photos')
        .select('*')
        .eq('gallery_id', galleryId)
        .order('order_index', { ascending: true, nullsFirst: false });

      if (error) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des photos de la galerie', { error, userId, galleryId });
        return res.status(500).json({
          message: 'Erreur lors de la récupération des photos de la galerie',
          success: false,
          toastType: 'error'
        });
      }

      logger.info(LogEventType.USER_UPDATE, 'Photos de la galerie récupérées avec succès', { userId, galleryId, count: data.length });
      res.json({
        success: true,
        photos: data
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors de la récupération des photos de la galerie', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      return res.status(500).json({
        message: 'Erreur lors de la récupération des photos de la galerie',
        success: false,
        toastType: 'error'
      });
    }
  }

  async changePassword(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        res.status(400).json({
          message: 'Veuillez fournir le mot de passe actuel et le nouveau mot de passe',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que le mot de passe actuel est correct
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('password_hash')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des données utilisateur', {
          userId,
          error: userError?.message
        });

        return res.status(500).json({
          message: 'Erreur lors de la vérification du mot de passe',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que le mot de passe actuel est correct
      const bcrypt = require('bcryptjs');
      const isPasswordCorrect = await bcrypt.compare(currentPassword, userData.password_hash);

      if (!isPasswordCorrect) {
        res.status(400).json({
          message: 'Le mot de passe actuel est incorrect',
          success: false,
          toastType: 'error'
        });
      }

      // Hacher le nouveau mot de passe
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash(newPassword, salt);

      // Mettre à jour le mot de passe dans la base de données
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

      await dbService.updatePassword(userId || '', passwordHash, oneYearFromNow);

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId || '',
        'password_change',
        undefined,
        'security',
        {
          action: 'Changement de mot de passe',
          date: new Date().toISOString()
        },
        getIpFromRequest(req)
      );

      // Ajouter une notification utilisateur dans la base de données
      try {
        const { data: userNotification, error: notifError } = await supabase
          .from('user_notifications')
          .insert({
            user_id: userId,
            type: 'system',
            title: 'Mot de passe modifié',
            content: 'Votre mot de passe a été modifié avec succès. Si vous n\'êtes pas à l\'origine de cette action, veuillez contacter notre support immédiatement.',
            link: '',
            is_read: false,
            is_archived: false
          })
          .select()
          .single();

        if (notifError) {
          logger.error('Erreur lors de l\'ajout de notification utilisateur pour changement de mot de passe:', notifError);
        } else {
          logger.info('Notification ajoutée avec succès pour le changement de mot de passe');
        }
      } catch (notifError) {
        logger.error('Erreur lors de l\'ajout de notification utilisateur pour changement de mot de passe:', notifError);
        // Continue execution even if notification fails
      }

      // Envoyer un email de confirmation
      try {
        const userEmail = req.user?.email;
        if (userEmail) {
          const emailService = require('../services/emailService');
          await emailService.sendPasswordChangedEmail(userEmail);
        }
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email de confirmation:', emailError);
        // Ne pas bloquer la réponse si l'email échoue
      }

      // Réinitialiser les tentatives de connexion
      await dbService.resetLoginAttempts(userId || '');

      res.status(200).json({
        message: 'Mot de passe modifié avec succès',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors du changement de mot de passe', {
        error: error instanceof Error ? error.message : error,
        userId: req.user?.userId
      });

      return res.status(500).json({
        message: 'Une erreur est survenue lors du changement de mot de passe',
        success: false,
        toastType: 'error'
      });
    }
  }

  async changeEmail(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      const { newEmail, password } = req.body;

      if (!newEmail || !password) {
        res.status(400).json({
          message: 'Veuillez fournir le nouveau email et votre mot de passe',
          success: false,
          toastType: 'error'
        });
      }

      // Valider le format de l'email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newEmail)) {
        logger.warn('Format d\'email invalide:', { email: newEmail });
        res.status(400).json({
          message: 'Format d\'email invalide',
          success: false,
          toastType: 'error'
        });
      }

      // Extraire et vérifier le domaine email
      logger.info('Vérification du domaine email');
      const domain = newEmail.split('@')[1];
      try {
        await dns.promises.resolve(domain, 'MX');
        logger.info('Domaine email valide:', { domain });
      } catch (error) {
        logger.error('Domaine email invalide:', { domain, error });
        res.status(400).json({
          message: 'L\'adresse email fournie n\'est pas valide. Veuillez vérifier le domaine.',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que l'email n'est pas déjà utilisé
      const newEmailHash = hashEmail(newEmail);
      const { data: existingUser, error: existingError } = await supabase
        .from('users')
        .select('id')
        .eq('email_hash', newEmailHash)
        .maybeSingle();

      if (existingUser) {
        logger.warn('Tentative de changement vers un email existant:', { email: newEmail });
        res.status(400).json({
          message: 'Cette adresse email est déjà utilisée',
          success: false,
          toastType: 'error'
        });
      }

      if (existingError) {
        logger.error('Erreur lors de la vérification de l\'email existant:', {
          email: newEmail,
          error: existingError.message
        });
      }

      // Vérifier que le mot de passe est correct
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('password_hash, email')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des données utilisateur', {
          userId,
          error: userError?.message
        });

        return res.status(500).json({
          message: 'Erreur lors de la vérification du mot de passe',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que le mot de passe est correct
      const bcrypt = require('bcryptjs');
      const isPasswordCorrect = await bcrypt.compare(password, userData.password_hash);

      if (!isPasswordCorrect) {
        logger.warn('Mot de passe incorrect pour changement d\'email:', { userId });
        res.status(400).json({
          message: 'Le mot de passe est incorrect',
          success: false,
          toastType: 'error'
        });
      }

      // Générer un token de vérification
      const crypto = require('crypto');
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Le token expire dans 24 heures

      // Enregistrer le token dans la base de données
      const { error: tokenError } = await supabase
        .from('auth_tokens')
        .insert({
          user_id: userId,
          token,
          type: 'email_verification',
          expires_at: expiresAt.toISOString()
        });

      if (tokenError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la création du token', {
          userId,
          error: tokenError.message
        });

        return res.status(500).json({
          message: 'Erreur lors de la création du token de vérification',
          success: false,
          toastType: 'error'
        });
      }

      // Enregistrer le nouvel email temporairement avec chiffrement
      const encryptedTempEmail = await encryptUserDataAsync({ email: newEmail });
      const { error: updateError } = await supabase
        .from('users')
        .update({
          temp_email: encryptedTempEmail.email,
          email_verifier: false
        })
        .eq('id', userId);

      if (updateError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la mise à jour de l\'email', {
          userId,
          error: updateError.message
        });

        return res.status(500).json({
          message: 'Erreur lors de la mise à jour de l\'email',
          success: false,
          toastType: 'error'
        });
      }

      // Journalisation de l'action utilisateur
      await logUserActivity(
        userId || '',
        'email_change_request',
        undefined,
        'security',
        {
          action: 'Demande de changement d\'email',
          oldEmail: req.user?.email,
          newEmail: newEmail
        },
        getIpFromRequest(req)
      );

      // Envoyer un email de vérification à la nouvelle adresse via la file d'attente
      try {
        const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

        // Ajouter à la file d'attente en utilisant la méthode statique
        await sendVerificationEmail(newEmail, { token, verificationLink: verificationUrl });

        logger.info('Email de vérification ajouté à la file d\'attente:', { email: newEmail });
      } catch (emailError) {
        logger.error('Erreur lors de l\'ajout de l\'email de vérification à la file d\'attente:', emailError);

        return res.status(500).json({
          message: 'Erreur lors de l\'envoi de l\'email de vérification',
          success: false,
          toastType: 'error'
        });
      }

      res.status(200).json({
        message: 'Un email de vérification a été envoyé à votre nouvelle adresse. Veuillez vérifier votre boîte de réception pour confirmer le changement.',
        success: true,
        toastType: 'success'
      });
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur lors du changement d\'email', {
        error: error instanceof Error ? error.message : error,
        userId: req.user?.userId
      });

      return res.status(500).json({
        message: 'Une erreur est survenue lors du changement d\'email',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Récupère la liste des utilisateurs administrateurs et modérateurs (staff)
  async getStaffUsers(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;

      // Vérification de l'authentification avant toute opération
      if (!userId) {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'accès sans authentification à la liste du staff');
        res.status(401).json({
          message: 'Utilisateur non authentifié',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier le cache Redis
      const cacheKey = `staff_users:${userId}`;
      const cachedStaff = await redis.get(cacheKey);

      if (cachedStaff) {
        const parsedStaff = JSON.parse(cachedStaff);
        if (parsedStaff && parsedStaff.length > 0) {
          logger.info('Staff users récupérés depuis le cache Redis');
          res.status(200).json(parsedStaff);
        }
      }

      // Vérifier que l'utilisateur a les droits d'administrateur ou de modérateur
      const { data: userRole, error: userRoleError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .limit(1);

      if (userRoleError || !userRole || userRole.length === 0) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération du rôle utilisateur', { error: userRoleError });
        return res.status(500).json({
          message: 'Erreur serveur lors de la vérification des permissions',
          success: false,
          toastType: 'error'
        });
      }

      // Vérifier que l'utilisateur est bien admin ou modérateur
      if (userRole[0].role !== 'jobpadm' && userRole[0].role !== 'jobmodo') {
        logger.warn(LogEventType.AUTH_FAILURE, 'Tentative d\'accès non autorisé à la liste du staff', { userId, role: userRole[0].role });
        res.status(403).json({
          message: 'Vous n\'êtes pas autorisé à accéder à cette ressource',
          success: false,
          toastType: 'error'
        });
      }

      // Récupérer les utilisateurs administrateurs et modérateurs avec leurs profils
      const { data: staffUsers, error: staffError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          user_profil (
            nom,
            prenom
          )
        `)
        .or('role.eq.jobpadm,role.eq.jobmodo')
        .order('role', { ascending: false }) // Administrateurs d'abord, puis modérateurs
        .order('email', { ascending: true });

      if (staffError) {
        logger.error(LogEventType.DB_ERROR, 'Erreur lors de la récupération des utilisateurs staff', { error: staffError });
        return res.status(500).json({
          message: 'Erreur serveur lors de la récupération des utilisateurs',
          success: false,
          toastType: 'error'
        });
      }

      // Transformer les données pour un format plus simple à utiliser et déchiffrer les données sensibles
      const formattedStaffUsers = await Promise.all(staffUsers.map(async user => {
        // Déchiffrer les données utilisateur
        const decryptedUser = await decryptUserDataAsync(user);
        
        // Déchiffrer les données de profil si elles existent
        const decryptedProfil = user.user_profil?.[0] ? await decryptProfilDataAsync(user.user_profil[0]) : null;
        
        return {
          id: decryptedUser.id,
          email: decryptedUser.email,
          role: decryptedUser.role,
          first_name: decryptedProfil?.prenom || null,
          last_name: decryptedProfil?.nom || null
        };
      }));

      // Stocker le résultat dans le cache Redis avec une expiration de 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(formattedStaffUsers));

      // Retourner la liste des utilisateurs staff
      res.status(200).json(formattedStaffUsers);
    } catch (error) {
      logger.error(LogEventType.SERVER_ERROR, 'Erreur serveur lors de la récupération des utilisateurs staff', { error });
      return res.status(500).json({
        message: 'Une erreur inattendue est survenue',
        success: false,
        toastType: 'error'
      });
    }
  }

  // Utilitaire pour masquer l'adresse IP
  private maskIpAddress(ip: string): string {
    if (ip.includes('.')) {
      // IPv4
      const parts = ip.split('.');
      return `${parts[0]}.${parts[1]}.xxx.xxx`;
    } else if (ip.includes(':')) {
      // IPv6
      const parts = ip.split(':');
      const visibleParts = parts.slice(0, 2);
      return `${visibleParts.join(':')}:xxxx:xxxx:xxxx:xxxx`;
    }
    return 'xxx.xxx.xxx.xxx';
  }

  // Méthode pour récupérer l'historique des connexions
  async getUserLoginHistory(req: Request, res: Response) {
    try {
      const userId = req.user?.id || req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      // Vérifier le cache Redis
      const cacheKey = CACHE_KEYS.LOGIN_HISTORY(userId, page, limit);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Historique de connexion récupéré depuis le cache Redis', { userId, page, limit });
        res.status(200).json(JSON.parse(cachedData));
        return;
      }

      // Récupérer le type d'abonnement et les limites de l'utilisateur
      const { isPremium, historyLogsLimit } = await getUserSubscriptionLimits(userId);

      // Type d'abonnement
      const aboType = isPremium ? 'premium' : 'gratuit';

      // Utiliser la limite d'historique des logs directement
      const historyLimit = historyLogsLimit;

      const offset = (page - 1) * limit;

      // Calculer la limite maximale pour éviter de dépasser la limite d'abonnement
      const maxOffset = Math.max(0, historyLimit - limit);
      const adjustedOffset = Math.min(offset, maxOffset);

      // Récupérer les entrées d'historique avec la limite d'abonnement
      const { data, error, count } = await supabase
        .from('user_login_history')
        .select('id, login_date, ip_address, city, country, region', { count: 'exact' })
        .eq('user_id', userId)
        .order('login_date', { ascending: false })
        .range(adjustedOffset, adjustedOffset + limit - 1);

      if (error) {
        logger.error('Erreur lors de la récupération de l\'historique de connexion:', { error, userId });
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de l\'historique de connexion',
          toastType: 'error'
        });
      }

      // Masquer partiellement les adresses IP pour la sécurité
      function maskIpAddress(ip: string) {
        if (ip && ip.includes('.')) {
          // IPv4
          const parts = ip.split('.');
          return `${parts[0]}.${parts[1]}.xxx.xxx`;
        } else if (ip && ip.includes(':')) {
          // IPv6
          const parts = ip.split(':');
          const visibleParts = parts.slice(0, 2);
          return `${visibleParts.join(':')}:xxxx:xxxx:xxxx:xxxx`;
        }
        return 'xxx.xxx.xxx.xxx';
      }
      const secureData = (data || []).map(item => ({
        ...item,
        ip_address: item.ip_address ? maskIpAddress(item.ip_address) : 'Inconnue'
      }));

      // Calculer le nombre total réel respectant la limite d'abonnement
      const actualTotal = Math.min(count || 0, historyLimit);

      const result = {
        success: true,
        data: secureData,
        pagination: {
          total: actualTotal,
          page,
          limit,
          totalPages: Math.ceil(actualTotal / limit),
          historyLimit,
          // Ajouter les propriétés pour compatibilité
          totalCount: actualTotal,
          maxHistoryCount: historyLimit
        },
        subscription: {
          type: aboType,
          historyLimit
        }
      };

      // Stocker dans le cache Redis pour 15 minutes (900 secondes)
      await redis.setex(cacheKey, 900, JSON.stringify(result));
      logger.info('Cache mis à jour pour l\'historique des connexions', { userId, page, limit });

      res.status(200).json(result);
      return;

    } catch (error) {
      logger.error('Exception lors de la récupération de l\'historique de connexion:', { error });
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération de l\'historique de connexion',
        exception: {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          raw: error
        },
        toastType: 'error'
      });
    }
  }

  // Méthode pour récupérer l'historique des actions utilisateur
  async getUserActionsHistory(req: Request, res: Response) {
    try {
      const userId = req.user?.id || req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const type = (req.query.type as string) || 'all';

      // Vérifier le cache Redis
      const cacheKey = CACHE_KEYS.ACTION_HISTORY(userId, page, limit, type);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Historique des actions récupéré depuis le cache Redis', { userId, page, limit, type });
        res.status(200).json(JSON.parse(cachedData));
        return;
      }

      // Récupérer le type d'abonnement et les limites de l'utilisateur
      const { isPremium, historyLogsLimit } = await getUserSubscriptionLimits(userId);

      // Type d'abonnement
      const aboType = isPremium ? 'premium' : 'gratuit';

      // Utiliser la limite d'historique des logs directement
      const historyLimit = historyLogsLimit;

      const offset = (page - 1) * limit;

      // Calculer la limite maximale pour éviter de dépasser la limite d'abonnement
      const maxOffset = Math.max(0, historyLimit - limit);
      const adjustedOffset = Math.min(offset, maxOffset);

      // Compter le nombre total d'entrées pour l'utilisateur (avant limite)
      let query = supabase
        .from('user_actions_history')
        .select('id, action_type, action_date, resource_id, resource_type, details, ip_address', { count: 'exact' })
        .eq('user_id', userId)
        .order('action_date', { ascending: false });

      // Filtrer par type d'action si spécifié
      if (type !== 'all') {
        query = query.eq('action_type', type);
      }

      // Récupérer les entrées paginées
      const { data, error, count } = await query.range(adjustedOffset, adjustedOffset + limit - 1);

      if (error) {
        logger.error('Erreur lors de la récupération de l\'historique des actions:', { error, userId });
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération de l\'historique des actions',
          toastType: 'error'
        });
      }

      // Masquer partiellement les adresses IP pour la sécurité
      function maskIpAddress(ip: string) {
        if (ip && ip.includes('.')) {
          // IPv4
          const parts = ip.split('.');
          return `${parts[0]}.${parts[1]}.xxx.xxx`;
        } else if (ip && ip.includes(':')) {
          // IPv6
          const parts = ip.split(':');
          const visibleParts = parts.slice(0, 2);
          return `${visibleParts.join(':')}:xxxx:xxxx:xxxx:xxxx`;
        }
        return 'xxx.xxx.xxx.xxx';
      }
      const secureData = data.map(item => ({
        ...item,
        ip_address: item.ip_address ? maskIpAddress(item.ip_address) : 'Inconnue'
      }));

      // Calculer le nombre total réel respectant la limite d'abonnement
      const actualTotal = Math.min(count || 0, historyLimit);

      const result = {
        success: true,
        data: secureData,
        pagination: {
          total: actualTotal,
          page,
          limit,
          totalPages: Math.ceil(actualTotal / limit),
          historyLimit,
          // Ajouter les propriétés pour compatibilité
          totalCount: actualTotal,
          maxHistoryCount: historyLimit
        },
        subscription: {
          type: aboType,
          historyLimit
        }
      };

      // Stocker dans le cache Redis pour 15 minutes (900 secondes)
      await redis.setex(cacheKey, 900, JSON.stringify(result));
      logger.info('Cache mis à jour pour l\'historique des actions', { userId, page, limit, type });

      res.status(200).json(result);
      return;

    } catch (error) {
      logger.error('Exception lors de la récupération de l\'historique des actions:', { error });
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération de l\'historique des actions',
        toastType: 'error'
      });
    }
  }

  // Méthode pour récupérer les types d'actions disponibles
  async getActionTypes(req: Request, res: Response) {
    try {
      const userId = req.user?.id || req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
      }

      // Vérifier le cache Redis
      const cacheKey = CACHE_KEYS.ACTION_TYPES(userId);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Types d\'actions récupérés depuis le cache Redis', { userId });
        res.status(200).json(JSON.parse(cachedData));
        return;
      }

      // Récupérer tous les types d'actions distincts pour l'utilisateur avec une requête SQL brute
      const { data, error } = await supabase.rpc('get_distinct_action_types', { user_id_param: userId });

      if (error) {
        // Si la fonction RPC n'existe pas encore, utiliser une approche alternative
        const { data: alternativeData, error: alternativeError } = await supabase
          .from('user_actions_history')
          .select('action_type')
          .eq('user_id', userId);

        if (alternativeError) {
          logger.error('Erreur lors de la récupération des types d\'actions', { error: alternativeError, userId });
          res.status(500).json({
            success: false,
            message: 'Erreur lors de la récupération des types d\'actions',
            toastType: 'error'
          });
          return;
        }

        // Utiliser un Set pour éliminer les doublons
        const uniqueTypes = Array.from(new Set(alternativeData.map(item => item.action_type)));

        // Formater les types pour le dropdown
        const actionTypes = [
          { value: 'all', label: 'Toutes les activités' },
          ...uniqueTypes.map((type: string) => ({
            value: type,
            label: type // Pas de formatage, cela sera fait côté frontend
          }))
        ];

        const result = {
          success: true,
          data: actionTypes
        };

        // Stocker dans le cache Redis pour 1 heure
        await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(result));
        logger.info('Cache mis à jour pour les types d\'actions (méthode alternative)', { userId });

        res.status(200).json(result);
      }

      // Formater les types pour le dropdown
      const actionTypes = [
        { value: 'all', label: 'Toutes les activités' },
        ...data.map((item: { action_type: string }) => ({
          value: item.action_type,
          label: item.action_type // Pas de formatage, cela sera fait côté frontend
        }))
      ];

      const result = {
        success: true,
        data: actionTypes
      };

      // Stocker dans le cache Redis pour 1 heure
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(result));
      logger.info('Cache mis à jour pour les types d\'actions', { userId });

      res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur serveur lors de la récupération des types d\'actions', { error });
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des types d\'actions',
        toastType: 'error'
      });
    }
  }

  // Méthode pour récupérer les actions publiques récentes d'un utilisateur et masquer les informations sensibles
  async getPublicUserActions(req: Request, res: Response) {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          success: false,
          message: 'ID utilisateur requis',
          toastType: 'error'
        });
      }

      // Vérifier le cache Redis
      const cacheKey = CACHE_KEYS.PUBLIC_ACTIONS(userId);
      const cachedData = await redis.get(cacheKey);

      if (cachedData) {
        logger.info('Actions publiques récupérées depuis le cache Redis', { userId });
        return res.status(200).json(JSON.parse(cachedData));
      }

      // Récupérer les 5 dernières actions publiques de l'utilisateur
      // Exclure les actions privées comme les connexions, renouvellements d'abonnement, etc.
      const { data, error } = await supabase
        .from('user_actions_history')
        .select('id, action_type, action_date, resource_type, details')
        .eq('user_id', userId)
        .not('action_type', 'in', '(connexion_utilisateur,deconnexion_utilisateur,renewal_abonnement)')
        .order('action_date', { ascending: false })
        .limit(5);

      if (error) {
        logger.error('Erreur lors de la récupération des actions publiques:', { error, userId });
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des actions publiques',
          toastType: 'error'
        });
      }

      // Anonymisation des messages pour l'affichage public des actions publiques dans Activités récentes
      function anonymizeActivityMessage(message: string): string {
        if (!message || typeof message !== 'string') return message;
        // Masquer les emails
        message = message.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '***@***.***');
        // Masquer les numéros de devis/facture
        message = message.replace(/n°[A-Z0-9-]+/g, '#***');
        // Masquer les noms après "pour "
        message = message.replace(/pour [^\s:]+/g, 'pour un client');
        // Simplifier les messages de proposition
        message = message.replace(/Proposition acceptée \(Montant.*?\)/g, 'Nouvelle mission acceptée');
        message = message.replace(/Proposition refusée \(Montant.*?\)/g, 'Mission refusée');
        // Simplifier les sujets de tickets et autres détails
        message = message.replace(/: .+$/, '');
        return message;
      }

      const result = {
        success: true,
        data: (data || []).map(item => ({
          ...item,
          details: item.details && item.details.message ? {
            ...item.details,
            message: anonymizeActivityMessage(item.details.message)
          } : item.details
        }))
      };

      // Stocker dans le cache Redis pour 15 minutes (900 secondes)
      await redis.setex(cacheKey, 900, JSON.stringify(result));
      logger.info('Cache mis à jour pour les actions publiques', { userId });

      return res.status(200).json(result);

    } catch (error) {
      logger.error('Exception lors de la récupération des actions publiques:', { error });
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la récupération des actions publiques',
        toastType: 'error'
      });
    }
  }

  // Méthode pour demander la suppression du compte (envoi email de confirmation)
  async requestAccountDeletion(req: Request, res: Response) {
    try {
      const userId = req.user?.id || req.user?.userId;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié',
          toastType: 'error'
        });
        return;
      }

      logger.info('Demande de suppression de compte', { userId });

      // Vérifier que l'utilisateur existe et récupérer ses données déchiffrées
      const user = await dbService.getUserById(userId);
      if (!user || !user.email) {
        logger.error('Utilisateur non trouvé pour demande de suppression', { userId });
        res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé',
          toastType: 'error'
        });
        return;
      }

      // Générer un token de confirmation de suppression
      const deletionToken = randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 heures

      // Stocker le token dans la base de données
      const { error: tokenError } = await supabase
        .from('account_deletion_requests')
        .insert({
          user_id: userId,
          token: deletionToken,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString()
        });

      if (tokenError) {
        logger.error('Erreur lors de la création du token de suppression', { userId, error: tokenError });
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la demande de suppression',
          toastType: 'error'
        });
        return;
      }

      // Envoyer l'email de confirmation
      await sendAccountDeletionConfirmationEmail(user.email, deletionToken);

      // Log de l'action
      await logUserActivity(
        userId,
        'demande_suppression_compte',
        undefined,
        'user',
        {
          message: 'Demande de suppression de compte initiée',
          tokenGenerated: true
        },
        getIpFromRequest(req)
      );

      logger.info('Email de confirmation de suppression envoyé', { userId, email: user.email });

      res.status(200).json({
        success: true,
        message: 'Un email de confirmation a été envoyé à votre adresse. Veuillez cliquer sur le lien pour confirmer la suppression de votre compte.',
        toastType: 'success'
      });

    } catch (error) {
      logger.error('Erreur lors de la demande de suppression de compte', { error, userId: req.user?.userId });
      res.status(500).json({
        success: false,
        message: 'Une erreur inattendue est survenue lors de la demande de suppression',
        toastType: 'error'
      });
    }
  }

  // Méthode pour confirmer la suppression avec re-authentification
  async confirmAccountDeletion(req: Request, res: Response) {
    try {
      const { token, email, password } = req.body;

      if (!token || !email || !password) {
        res.status(400).json({
          success: false,
          message: 'Token, email et mot de passe sont requis',
          toastType: 'error'
        });
        return;
      }

      logger.info('Tentative de confirmation de suppression de compte', { token, email });

      // Vérifier le token de suppression
      const { data: deletionRequest, error: tokenError } = await supabase
        .from('account_deletion_requests')
        .select('*')
        .eq('token', token)
        .eq('used', false)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (tokenError || !deletionRequest) {
        logger.error('Token de suppression invalide ou expiré', { token, error: tokenError });
        res.status(400).json({
          success: false,
          message: 'Lien de suppression invalide ou expiré',
          toastType: 'error'
        });
        return;
      }

      // Récupérer l'utilisateur et vérifier les identifiants
      const userRaw = await dbService.getUserByEmail(email);
      if (!userRaw) {
        res.status(401).json({
          success: false,
          message: 'Identifiants incorrects',
          toastType: 'error'
        });
        return;
      }

      // Vérifier le mot de passe
      const passwordMatch = await comparePasswords(password, userRaw.password_hash);
      if (!passwordMatch) {
        res.status(401).json({
          success: false,
          message: 'Identifiants incorrects',
          toastType: 'error'
        });
        return;
      }

      // Vérifier que l'utilisateur correspond au token
      if (userRaw.id !== deletionRequest.user_id) {
        res.status(403).json({
          success: false,
          message: 'Token non valide pour cet utilisateur',
          toastType: 'error'
        });
        return;
      }

      // Marquer le token comme utilisé
      await supabase
        .from('account_deletion_requests')
        .update({ used: true, used_at: new Date().toISOString() })
        .eq('id', deletionRequest.id);

      res.status(200).json({
        success: true,
        message: 'Authentification réussie. Vous pouvez maintenant procéder à la suppression.',
        toastType: 'success',
        userId: userRaw.id,
        deletionToken: token
      });

    } catch (error) {
      logger.error('Erreur lors de la confirmation de suppression', { error });
      res.status(500).json({
        success: false,
        message: 'Une erreur inattendue est survenue',
        toastType: 'error'
      });
    }
  }

  // Méthode pour exécuter la suppression (anonymisation) finale
  async executeAccountDeletion(req: Request, res: Response) {
    try {
      logger.info('=== DÉBUT executeAccountDeletion ===', {
        timestamp: new Date().toISOString(),
        body: req.body,
        headers: req.headers['user-agent']
      });

      const { token, userId: requestUserId } = req.body;

      logger.info('Paramètres reçus', { token: token ? 'présent' : 'absent', requestUserId });

      if (!token || !requestUserId) {
        logger.error('Paramètres manquants', { token: !!token, requestUserId: !!requestUserId });
        res.status(400).json({
          success: false,
          message: 'Token et ID utilisateur requis',
          toastType: 'error'
        });
        return;
      }

      logger.info('Vérification du token de suppression', { token, requestUserId });

      // Vérifier le token de suppression
      const { data: deletionRequest, error: tokenError } = await supabase
        .from('account_deletion_requests')
        .select('*')
        .eq('token', token)
        .eq('user_id', requestUserId)
        .eq('used', true)
        .gt('expires_at', new Date().toISOString())
        .single();

      logger.info('Résultat vérification token', {
        deletionRequest: !!deletionRequest,
        tokenError: tokenError ? tokenError.message : null
      });

      if (tokenError || !deletionRequest) {
        logger.error('Token de suppression invalide pour exécution', { token, requestUserId, error: tokenError });
        res.status(400).json({
          success: false,
          message: 'Token de suppression invalide ou expiré',
          toastType: 'error'
        });
        return;
      }

      const userId = requestUserId;

      logger.info('Token validé, début de la suppression anonymisée du compte', { userId });

      // IMPORTANT: Récupérer l'email AVANT l'anonymisation pour l'email de confirmation
      logger.info('Récupération de l\'email utilisateur avant anonymisation', { userId });

      const userBeforeDeletion = await dbService.getUserById(userId);
      logger.info('Résultat getUserById', {
        userId,
        userFound: !!userBeforeDeletion,
        hasEmail: !!userBeforeDeletion?.email
      });

      const userEmail = userBeforeDeletion?.email;

      if (!userEmail) {
        logger.error('Impossible de récupérer l\'email utilisateur avant anonymisation', {
          userId,
          userBeforeDeletion: !!userBeforeDeletion
        });
        res.status(400).json({
          success: false,
          message: 'Erreur lors de la récupération des données utilisateur',
          toastType: 'error'
        });
        return;
      }

      logger.info('Email récupéré avec succès', { userId, emailLength: userEmail.length });

      // Vérifier que l'utilisateur existe
      logger.info('Vérification de l\'existence de l\'utilisateur dans Supabase', { userId });

      const { data: existingUser, error: userError } = await supabase
        .from('users')
        .select('id, email')
        .eq('id', userId)
        .single();

      logger.info('Résultat vérification utilisateur', {
        userId,
        userError: userError ? userError.message : null,
        existingUser: !!existingUser
      });

      if (userError || !existingUser) {
        logger.error('Utilisateur non trouvé pour suppression', { userId, error: userError });
        res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé',
          toastType: 'error'
        });
        return;
      }

      const anonymizedAt = new Date().toISOString();
      const anonymizedEmail = `anonyme_${Date.now()}@supprime.local`;

      logger.info('Début de l\'anonymisation des données utilisateur', {
        userId,
        anonymizedAt,
        anonymizedEmail
      });

      // Anonymiser les données utilisateur
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          email: anonymizedEmail,
          email_hash: hashEmail(anonymizedEmail),
          password_hash: null,
          google_id: null,
          google_data: null,
          temp_email: null,
          profil_actif: false,
          is_anonymized: true,
          anonymized_at: anonymizedAt,
          updated_at: anonymizedAt
        })
        .eq('id', userId);

      if (userUpdateError) {
        logger.error('Erreur lors de l\'anonymisation des données utilisateur', {
          userId,
          error: userUpdateError,
          errorMessage: userUpdateError.message || 'Erreur inconnue'
        });
        res.status(500).json({
          success: false,
          message: 'Erreur lors de la suppression du compte',
          toastType: 'error'
        });
        return;
      }

      logger.info('Anonymisation des données utilisateur réussie', { userId });

      // Anonymiser les données de profil avec des textes cohérents
      logger.info('Début de l\'anonymisation du profil utilisateur', { userId });

      const { error: profilUpdateError } = await supabase
        .from('user_profil')
        .update({
          nom: ANONYMIZED_USER_DATA.nom,
          prenom: ANONYMIZED_USER_DATA.prenom,
          telephone: null,
          adresse: ANONYMIZED_USER_DATA.adresse,
          ville: ANONYMIZED_USER_DATA.ville,
          code_postal: ANONYMIZED_USER_DATA.codePostal,
          bio: ANONYMIZED_USER_DATA.bio,
          slogan: null,
          photo_url: DEFAULT_IMAGE_URL,
          banner_url: null,
          profil_visible: false,
          seo_indexable: false,
          is_anonymized: true,
          anonymized_at: anonymizedAt,
          updated_at: anonymizedAt
        })
        .eq('user_id', userId);

      if (profilUpdateError) {
        logger.error('Erreur lors de l\'anonymisation du profil', {
          userId,
          error: profilUpdateError,
          errorMessage: profilUpdateError.message || 'Erreur inconnue'
        });
      } else {
        logger.info('Anonymisation du profil utilisateur réussie', { userId });
      }

      // Supprimer les tokens d'authentification
      logger.info('Suppression des tokens d\'authentification', { userId });
      await supabase
        .from('auth_tokens')
        .delete()
        .eq('user_id', userId);

      // Supprimer les tentatives de réinitialisation de mot de passe
      logger.info('Suppression des tentatives de réinitialisation de mot de passe', { userId });
      await supabase
        .from('password_resets')
        .delete()
        .eq('user_id', userId);

      await supabase
        .from('password_reset_attempts')
        .delete()
        .eq('user_id', userId);

      logger.info('Tokens et tentatives de réinitialisation supprimés', { userId });

      // Supprimer tous les éléments liés à l'utilisateur
      logger.info('=== DÉBUT SUPPRESSION DES DONNÉES LIÉES ===', { userId });
      await this.deleteUserRelatedData(userId);
      logger.info('=== FIN SUPPRESSION DES DONNÉES LIÉES ===', { userId });

      // Invalider les sessions Redis
      logger.info('Début de la suppression des sessions Redis', { userId });
      try {
        const sessionKeys = await redis.keys(`session:${userId}:*`);
        logger.info('Sessions Redis trouvées', { userId, sessionKeysCount: sessionKeys.length });

        if (sessionKeys.length > 0) {
          await redis.del(...sessionKeys);
          logger.info('Sessions Redis supprimées', { userId, sessionKeysCount: sessionKeys.length });
        }

        // Supprimer le cache utilisateur
        const userCacheKeys = await redis.keys(`user:${userId}:*`);
        logger.info('Clés de cache utilisateur trouvées', { userId, userCacheKeysCount: userCacheKeys.length });

        if (userCacheKeys.length > 0) {
          await redis.del(...userCacheKeys);
          logger.info('Cache utilisateur supprimé', { userId, userCacheKeysCount: userCacheKeys.length });
        }

        logger.info('Suppression des sessions Redis terminée', { userId });
      } catch (redisError) {
        logger.error('Erreur lors de la suppression des sessions Redis', {
          userId,
          error: redisError instanceof Error ? redisError.message : String(redisError)
        });
      }

      // Log de l'action
      logger.info('Enregistrement de l\'activité utilisateur', { userId });
      await logUserActivity(
        userId,
        'suppression_compte',
        undefined, // resourceId
        'user', // resourceType
        {
          message: 'Suppression anonymisée du compte utilisateur',
          anonymizedAt,
          originalEmail: existingUser.email
        },
        getIpFromRequest(req)
      );
      logger.info('Activité utilisateur enregistrée', { userId });

      // Envoyer l'email de confirmation de suppression
      logger.info('Envoi de l\'email de confirmation de suppression', { userId, userEmail });
      try {
        await sendAccountDeletionCompletedEmail(userEmail);
        logger.info('Email de confirmation de suppression envoyé avec succès', { userId, email: userEmail });
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email de confirmation', {
          userId,
          error: emailError instanceof Error ? emailError.message : String(emailError)
        });
        // Ne pas bloquer le processus si l'email échoue
      }

      logger.info('=== COMPTE ANONYMISÉ AVEC SUCCÈS ===', { userId, anonymizedAt });

      // Réponse avec instruction de déconnexion pour éviter le spam d'erreurs
      logger.info('Envoi de la réponse de succès', { userId });
      res.status(200).json({
        success: true,
        message: 'Votre compte a été supprimé avec succès. Vos données ont été anonymisées conformément au RGPD.',
        toastType: 'success',
        forceLogout: true // Indique au frontend de déconnecter l'utilisateur
      });

      logger.info('=== FIN executeAccountDeletion - SUCCÈS ===', { userId });

    } catch (error) {
      logger.error('=== ERREUR DANS executeAccountDeletion ===', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.body?.userId || 'inconnu',
        timestamp: new Date().toISOString()
      });

      res.status(500).json({
        success: false,
        message: 'Une erreur inattendue est survenue lors de la suppression du compte',
        toastType: 'error'
      });
    }
  }

  // Méthode privée pour supprimer toutes les données liées à un utilisateur
  private async deleteUserRelatedData(userId: string) {
    try {
      logger.info('Début de la suppression des données liées à l\'utilisateur', { userId });

      // 1. Supprimer les services utilisateur
      try {
        logger.info('Étape 1: Suppression des services utilisateur', { userId });
        await this.deleteUserServices(userId);
        logger.info('Étape 1: Services utilisateur supprimés avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 1: Suppression des services utilisateur', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 2. Supprimer les galeries et photos
      try {
        logger.info('Étape 2: Suppression des galeries et photos', { userId });
        await this.deleteUserGalleries(userId);
        logger.info('Étape 2: Galeries et photos supprimées avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 2: Suppression des galeries et photos', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 3. Supprimer les photos mises en avant
      try {
        logger.info('Étape 3: Suppression des photos mises en avant', { userId });
        await this.deleteUserFeaturedPhotos(userId);
        logger.info('Étape 3: Photos mises en avant supprimées avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 3: Suppression des photos mises en avant', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 4. Supprimer les fichiers de profil et bannière
      try {
        logger.info('Étape 4: Suppression des fichiers de profil et bannière', { userId });
        await this.deleteUserProfileFiles(userId);
        logger.info('Étape 4: Fichiers de profil et bannière supprimés avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 4: Suppression des fichiers de profil et bannière', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 5. Supprimer les documents de vérification entreprise
      try {
        logger.info('Étape 5: Suppression des documents de vérification entreprise', { userId });
        await this.deleteUserVerificationDocuments(userId);
        logger.info('Étape 5: Documents de vérification entreprise supprimés avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 5: Suppression des documents de vérification entreprise', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 6. Supprimer les templates de cartes de visite
      try {
        logger.info('Étape 6: Suppression des templates de cartes de visite', { userId });
        await this.deleteUserCardTemplates(userId);
        logger.info('Étape 6: Templates de cartes de visite supprimés avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 6: Suppression des templates de cartes de visite', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 7. Suppression exhaustive de TOUS les buckets
      try {
        logger.info('Étape 7: Suppression exhaustive de tous les buckets', { userId });
        await this.deleteAllUserStorageFiles(userId);
        logger.info('Étape 7: Suppression exhaustive terminée avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 7: Suppression exhaustive des buckets', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      // 8. Supprimer les autres données (missions, avis, etc.)
      try {
        logger.info('Étape 8: Suppression des autres données', { userId });
        await this.deleteUserOtherData(userId);
        logger.info('Étape 8: Autres données supprimées avec succès', { userId });
      } catch (error) {
        logger.error('Erreur étape 8: Suppression des autres données', { userId, error: error instanceof Error ? error.message : String(error) });
      }

      logger.info('Suppression des données liées terminée', { userId });
    } catch (error) {
      logger.error('Erreur lors de la suppression des données liées', {
        userId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      // Ne pas bloquer le processus de suppression principal
    }
  }

  // Supprimer les services utilisateur
  private async deleteUserServices(userId: string) {
    try {
      const { error } = await supabase
        .from('user_services')
        .delete()
        .eq('user_id', userId);

      if (error) {
        logger.error('Erreur lors de la suppression des services', { userId, error });
      } else {
        logger.info('Services utilisateur supprimés', { userId });
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des services', { userId, error });
    }
  }

  // Supprimer les galeries et leurs photos
  private async deleteUserGalleries(userId: string) {
    try {
      // Récupérer toutes les galeries de l'utilisateur
      const { data: galleries, error: galleriesError } = await supabase
        .from('user_gallery')
        .select('id')
        .eq('user_id', userId);

      if (galleriesError) {
        logger.error('Erreur lors de la récupération des galeries', { userId, error: galleriesError });
        return;
      }

      if (galleries && galleries.length > 0) {
        // Récupérer toutes les photos des galeries
        const galleryIds = galleries.map(g => g.id);
        const { data: photos, error: photosError } = await supabase
          .from('user_gallery_photos')
          .select('photo_url')
          .in('gallery_id', galleryIds);

        if (!photosError && photos) {
          // Supprimer les fichiers du storage
          for (const photo of photos) {
            try {
              const filePath = extractFilePathFromUrl(photo.photo_url, 'galerie_realisation_client');
              if (filePath && !filePath.includes('avatar-defaut-jobpartiel.jpg')) {
                logger.info('Suppression photo galerie', { userId, photoUrl: photo.photo_url, filePath });

                const { error: removeError } = await supabase.storage
                  .from('galerie_realisation_client')
                  .remove([filePath]);

                if (removeError) {
                  logger.error('Erreur suppression photo galerie', { userId, photoUrl: photo.photo_url, filePath, error: removeError });
                } else {
                  logger.info('Photo galerie supprimée avec succès', { userId, photoUrl: photo.photo_url, filePath });
                }
              }
            } catch (error) {
              logger.error('Erreur lors de la suppression d\'une photo de galerie', { userId, photoUrl: photo.photo_url, error });
            }
          }
        }

        // Supprimer les entrées de la base de données
        await supabase
          .from('user_gallery_photos')
          .delete()
          .in('gallery_id', galleryIds);

        await supabase
          .from('user_gallery')
          .delete()
          .eq('user_id', userId);

        logger.info('Galeries et photos supprimées', { userId, galleriesCount: galleries.length });
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des galeries', { userId, error });
    }
  }

  // Supprimer les photos mises en avant
  private async deleteUserFeaturedPhotos(userId: string) {
    try {
      // Récupérer toutes les photos mises en avant
      const { data: featuredPhotos, error: featuredError } = await supabase
        .from('user_featured_photos')
        .select('photo_url')
        .eq('user_id', userId);

      if (!featuredError && featuredPhotos) {
        // Supprimer les fichiers du storage
        for (const photo of featuredPhotos) {
          try {
            const filePath = extractFilePathFromUrl(photo.photo_url, 'photo_profil');
            if (filePath && !filePath.includes('avatar-defaut-jobpartiel.jpg')) {
              logger.info('Suppression photo mise en avant', { userId, photoUrl: photo.photo_url, filePath });

              const { error: removeError } = await supabase.storage
                .from('photo_profil')
                .remove([filePath]);

              if (removeError) {
                logger.error('Erreur suppression photo mise en avant', { userId, photoUrl: photo.photo_url, filePath, error: removeError });
              } else {
                logger.info('Photo mise en avant supprimée avec succès', { userId, photoUrl: photo.photo_url, filePath });
              }
            }
          } catch (error) {
            logger.error('Erreur lors de la suppression d\'une photo mise en avant', { userId, photoUrl: photo.photo_url, error });
          }
        }

        // Supprimer les entrées de la base de données
        await supabase
          .from('user_featured_photos')
          .delete()
          .eq('user_id', userId);

        logger.info('Photos mises en avant supprimées', { userId, photosCount: featuredPhotos.length });
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des photos mises en avant', { userId, error });
    }
  }

  // Supprimer les fichiers de profil et bannière
  private async deleteUserProfileFiles(userId: string) {
    try {
      // Récupérer le storage_id de l'utilisateur
      const { data: userData, error: userError } = await supabase
        .from('user_profil')
        .select('storage_id, photo_url, banner_url')
        .eq('user_id', userId)
        .single();

      if (userError || !userData) {
        logger.warn('Impossible de récupérer les données de profil pour la suppression des fichiers', { userId });
        return;
      }

      const storageId = userData.storage_id;

      // Supprimer tous les fichiers du dossier utilisateur
      if (storageId) {
        try {
          // Supprimer TOUS les dossiers de l'utilisateur
          const foldersToCheck = ['profil', 'banniere', 'card_editor'];

          for (const folder of foldersToCheck) {
            try {
              const { data: files, error: listError } = await supabase.storage
                .from('photo_profil')
                .list(`${storageId}/${folder}`);

              if (listError) {
                logger.warn(`Erreur lors de la liste des fichiers ${folder}`, { userId, storageId, folder, error: listError });
                continue;
              }

              if (files && files.length > 0) {
                const filesToDelete = files.map(file => `${storageId}/${folder}/${file.name}`);
                logger.info(`Suppression des fichiers ${folder}`, { userId, storageId, folder, filesCount: files.length, files: filesToDelete });

                const { error: removeError } = await supabase.storage
                  .from('photo_profil')
                  .remove(filesToDelete);

                if (removeError) {
                  logger.error(`Erreur lors de la suppression des fichiers ${folder}`, { userId, storageId, folder, error: removeError });
                } else {
                  logger.info(`Fichiers ${folder} supprimés avec succès`, { userId, storageId, folder, filesCount: files.length });
                }
              } else {
                logger.info(`Aucun fichier trouvé dans ${folder}`, { userId, storageId, folder });
              }
            } catch (folderError) {
              logger.error(`Erreur lors du traitement du dossier ${folder}`, { userId, storageId, folder, error: folderError });
            }
          }

          // Supprimer aussi les fichiers à la racine du dossier utilisateur
          try {
            const { data: rootFiles, error: rootListError } = await supabase.storage
              .from('photo_profil')
              .list(storageId);

            if (!rootListError && rootFiles && rootFiles.length > 0) {
              const rootFilesToDelete = rootFiles
                .filter(file => file.name !== '.emptyFolderPlaceholder') // Éviter les fichiers système
                .map(file => `${storageId}/${file.name}`);

              if (rootFilesToDelete.length > 0) {
                logger.info('Suppression des fichiers à la racine', { userId, storageId, filesCount: rootFilesToDelete.length, files: rootFilesToDelete });

                const { error: removeRootError } = await supabase.storage
                  .from('photo_profil')
                  .remove(rootFilesToDelete);

                if (removeRootError) {
                  logger.error('Erreur lors de la suppression des fichiers racine', { userId, storageId, error: removeRootError });
                } else {
                  logger.info('Fichiers racine supprimés avec succès', { userId, storageId, filesCount: rootFilesToDelete.length });
                }
              }
            }
          } catch (rootError) {
            logger.error('Erreur lors du traitement des fichiers racine', { userId, storageId, error: rootError });
          }

          logger.info('Suppression complète des fichiers utilisateur terminée', { userId, storageId });
        } catch (error) {
          logger.error('Erreur lors de la suppression des fichiers de profil', { userId, storageId, error });
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des fichiers de profil', { userId, error });
    }
  }

  // Supprimer les documents de vérification entreprise
  private async deleteUserVerificationDocuments(userId: string) {
    try {
      await deleteAllEntrepriseVerificationDocuments(userId);
      logger.info('Documents de vérification entreprise supprimés', { userId });
    } catch (error) {
      logger.error('Erreur lors de la suppression des documents de vérification', { userId, error });
    }
  }

  // Supprimer les templates de cartes de visite
  private async deleteUserCardTemplates(userId: string) {
    try {
      // Récupérer tous les templates de l'utilisateur
      const { data: templates, error: templatesError } = await supabase
        .from('card_templates')
        .select('id')
        .eq('user_id', userId);

      if (!templatesError && templates && templates.length > 0) {
        // Récupérer le storage_id de l'utilisateur
        const { data: userData } = await supabase
          .from('user_profil')
          .select('storage_id')
          .eq('user_id', userId)
          .single();

        if (userData?.storage_id) {
          // Supprimer les fichiers de chaque template
          for (const template of templates) {
            try {
              await deleteCardEditorImages(userData.storage_id, template.id);
            } catch (error) {
              logger.error('Erreur lors de la suppression des images de template', { userId, templateId: template.id, error });
            }
          }
        }

        // Supprimer les entrées de la base de données
        await supabase
          .from('card_exports')
          .delete()
          .eq('user_id', userId);

        await supabase
          .from('card_ai_generation_stats')
          .delete()
          .eq('user_id', userId);

        await supabase
          .from('card_template_elements')
          .delete()
          .in('template_id', templates.map(t => t.id));

        await supabase
          .from('card_templates')
          .delete()
          .eq('user_id', userId);

        logger.info('Templates de cartes de visite supprimés', { userId, templatesCount: templates.length });
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression des templates de cartes', { userId, error });
    }
  }

  // Supprimer les missions de l'utilisateur qui n'ont pas reçu d'offres
  private async deleteUserMissionsWithoutOffers(userId: string) {
    try {
      logger.info('Début de la suppression des missions sans offres', { userId });

      // Récupérer toutes les missions de l'utilisateur
      const { data: userMissions, error: missionsError } = await supabase
        .from('user_missions')
        .select('id, titre')
        .eq('user_id', userId);

      if (missionsError) {
        logger.error('Erreur lors de la récupération des missions utilisateur', { userId, error: missionsError });
        return;
      }

      if (!userMissions || userMissions.length === 0) {
        logger.info('Aucune mission trouvée pour l\'utilisateur', { userId });
        return;
      }

      logger.info(`${userMissions.length} missions trouvées pour l'utilisateur`, { userId, missionsCount: userMissions.length });

      const missionIds = userMissions.map(m => m.id);

      // Vérifier quelles missions ont reçu des offres (candidatures)
      const { data: missionsWithOffers, error: offersError } = await supabase
        .from('user_mission_candidature')
        .select('mission_id')
        .in('mission_id', missionIds);

      if (offersError) {
        logger.error('Erreur lors de la vérification des offres', { userId, error: offersError });
        return;
      }

      // Identifier les missions sans offres
      const missionIdsWithOffers = missionsWithOffers ? missionsWithOffers.map(o => o.mission_id) : [];
      const missionsWithoutOffers = userMissions.filter(mission => !missionIdsWithOffers.includes(mission.id));

      if (missionsWithoutOffers.length === 0) {
        logger.info('Toutes les missions ont reçu des offres, aucune suppression', { userId, totalMissions: userMissions.length });
        return;
      }

      logger.info(`${missionsWithoutOffers.length} missions sans offres à supprimer`, {
        userId,
        totalMissions: userMissions.length,
        missionsWithoutOffersCount: missionsWithoutOffers.length
      });

      const missionIdsToDelete = missionsWithoutOffers.map(m => m.id);

      // Supprimer les photos des missions sans offres
      const { data: missionPhotos, error: photosError } = await supabase
        .from('user_mission_photos')
        .select('photo_url')
        .in('mission_id', missionIdsToDelete);

      if (!photosError && missionPhotos && missionPhotos.length > 0) {
        logger.info(`Suppression de ${missionPhotos.length} photos de missions`, { userId, photosCount: missionPhotos.length });

        for (const photo of missionPhotos) {
          try {
            const filePath = extractFilePathFromUrl(photo.photo_url, 'mission_photos');
            if (filePath) {
              logger.info('Suppression photo mission', { userId, filePath });

              const { error: removeError } = await supabase.storage
                .from('mission_photos')
                .remove([filePath]);

              if (removeError) {
                logger.error('Erreur suppression photo mission', { userId, filePath, error: removeError });
              } else {
                logger.info('Photo mission supprimée', { userId, filePath });
              }
            }
          } catch (error) {
            logger.error('Erreur lors de la suppression d\'une photo de mission', { userId, photoUrl: photo.photo_url, error });
          }
        }
      }

      // Supprimer les photos des missions (table)
      await supabase
        .from('user_mission_photos')
        .delete()
        .in('mission_id', missionIdsToDelete);

      // Supprimer les plannings des missions
      await supabase
        .from('user_mission_planning')
        .delete()
        .in('mission_id', missionIdsToDelete);

      // Supprimer les missions sans offres
      const { error: deleteMissionsError } = await supabase
        .from('user_missions')
        .delete()
        .in('id', missionIdsToDelete);

      if (deleteMissionsError) {
        logger.error('Erreur lors de la suppression des missions sans offres', { userId, error: deleteMissionsError });
      } else {
        logger.info('Missions sans offres supprimées avec succès', {
          userId,
          deletedMissionsCount: missionsWithoutOffers.length,
          deletedMissionTitles: missionsWithoutOffers.map(m => m.titre)
        });
      }

    } catch (error) {
      logger.error('Erreur lors de la suppression des missions sans offres', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Supprimer les autres données utilisateur
  private async deleteUserOtherData(userId: string) {
    try {
      // Supprimer les missions sans offres (uniquement celles qui n'ont pas reçu d'offres)
      await this.deleteUserMissionsWithoutOffers(userId);

      // Anonymiser les conversations et messages (au lieu de les supprimer)
      await this.anonymizeUserMessagesAndConversations(userId);

      // Supprimer les signalements et contenus modérés
      await this.deleteUserReportsAndModeration(userId);

      // Anonymiser les avis (garder pour la communauté mais anonymiser l'auteur)
      await supabase
        .from('user_reviews')
        .update({
          author_id: null,
          commentaire: ANONYMIZED_USER_DATA.reviewComment
        })
        .eq('author_id', userId);

      // Supprimer les factures et leurs éléments liés
      await this.deleteUserInvoicesAndRelated(userId);

      // Supprimer les pièces jointes des messages et tickets avant de supprimer les tables
      await this.deleteUserAttachments(userId);

      // Supprimer les autres données personnelles
      // IMPORTANT: user_missions et user_referrals ne sont PAS supprimés (conservation pour la plateforme)
      const tablesToClean = [
        'user_jobi_historique',
        'user_jobi',
        'user_transac',
        'user_badges',
        'user_favorites',
        'user_notifications',
        'user_login_history',
        'user_mission_responses',
        'user_actions_history',
        'user_ai_credits',
        'user_ai_credits_historique',
        'user_ai_prompts',
        'ai_user_consents',
        'ai_image_generation_stats',
        'user_seo_consent',
        'user_badges_verification',
        'newsletter_subscribers',
        'bug_reports',
        'bug_report_votes',
        'bug_report_comments',
        'support_tickets',
        'support_ticket_comments',
        'support_ticket_attachments',
        'user_referrals_abuse_logs',
        'promo_code_usage',
        'openrouter_api_usage',
        'content_moderation_logs',
        'security_logs'
      ];

      for (const table of tablesToClean) {
        try {
          await supabase
            .from(table)
            .delete()
            .eq('user_id', userId);
        } catch (error) {
          logger.error(`Erreur lors de la suppression de la table ${table}`, { userId, table, error });
        }
      }

      logger.info('Autres données utilisateur supprimées', { userId });
    } catch (error) {
      logger.error('Erreur lors de la suppression des autres données', { userId, error });
    }
  }

  // Anonymiser les conversations et messages (au lieu de les supprimer)
  private async anonymizeUserMessagesAndConversations(userId: string) {
    try {
      // Anonymiser les messages envoyés par l'utilisateur
      await supabase
        .from('user_messages')
        .update({
          sender_id: null,
          content: ANONYMIZED_USER_DATA.messageContent
        })
        .eq('sender_id', userId);

      // Anonymiser les conversations où l'utilisateur était participant
      // Remplacer l'ID utilisateur par null dans les conversations
      await supabase
        .from('user_messages_conversations')
        .update({ user1_id: null })
        .eq('user1_id', userId);

      await supabase
        .from('user_messages_conversations')
        .update({ user2_id: null })
        .eq('user2_id', userId);

      logger.info('Conversations et messages anonymisés', { userId });
    } catch (error) {
      logger.error('Erreur lors de l\'anonymisation des conversations', { userId, error });
    }
  }

  // Supprimer les signalements et contenus modérés
  private async deleteUserReportsAndModeration(userId: string) {
    try {
      // Supprimer les signalements faits par l'utilisateur
      await supabase
        .from('reported_content_reports')
        .delete()
        .eq('user_id', userId);

      // Supprimer les signalements de contenu de l'utilisateur
      await supabase
        .from('reported_content')
        .delete()
        .eq('reported_user_id', userId);

      // Supprimer les signalements faits par l'utilisateur
      await supabase
        .from('reported_content')
        .delete()
        .eq('reported_by', userId);

      // Supprimer l'historique des signalements
      await supabase
        .from('reported_content_history')
        .delete()
        .eq('admin_id', userId);

      logger.info('Signalements et modération supprimés', { userId });
    } catch (error) {
      logger.error('Erreur lors de la suppression des signalements', { userId, error });
    }
  }

  // Supprimer les factures et leurs éléments liés
  private async deleteUserInvoicesAndRelated(userId: string) {
    try {
      // Récupérer toutes les factures de l'utilisateur
      const { data: invoices, error: invoicesError } = await supabase
        .from('invoices')
        .select('id')
        .eq('user_id', userId);

      if (!invoicesError && invoices && invoices.length > 0) {
        const invoiceIds = invoices.map(i => i.id);

        // Supprimer les éléments liés aux factures
        await supabase.from('invoice_items').delete().in('invoice_id', invoiceIds);
        await supabase.from('invoice_sendings').delete().in('invoice_id', invoiceIds);
        await supabase.from('invoice_history').delete().in('invoice_id', invoiceIds);

        // Supprimer les factures
        await supabase.from('invoices').delete().eq('user_id', userId);

        logger.info('Factures et éléments liés supprimés', { userId, invoicesCount: invoices.length });
      }

      // Supprimer les clients de facturation
      await supabase.from('invoices_client').delete().eq('user_id', userId);

      // Supprimer les paramètres d'entreprise
      await supabase.from('invoices_company_settings').delete().eq('user_id', userId);

    } catch (error) {
      logger.error('Erreur lors de la suppression des factures', { userId, error });
    }
  }

  // Supprimer complètement le répertoire racine d'un utilisateur dans un bucket spécifique
  private async deleteCompleteUserDirectory(storageId: string, bucketName: string, userId: string) {
    try {
      logger.info(`Suppression complète du répertoire utilisateur dans ${bucketName}`, { userId, storageId, bucketName });

      // Fonction récursive pour supprimer tous les fichiers et sous-dossiers
      const deleteRecursively = async (path: string): Promise<void> => {
        logger.info(`Listage des fichiers dans ${path}`, { userId, storageId, bucketName, path });

        const { data: items, error: listError } = await supabase.storage
          .from(bucketName)
          .list(path, {
            limit: 1000,
            sortBy: { column: 'name', order: 'asc' }
          });

        if (listError) {
          logger.error(`Erreur lors de la liste des fichiers dans ${path}`, {
            userId, storageId, bucketName, path, error: listError, errorMessage: listError.message
          });
          return;
        }

        if (!items || items.length === 0) {
          logger.info(`Aucun fichier trouvé dans ${path}`, { userId, storageId, bucketName, path });
          return;
        }

        logger.info(`${items.length} éléments trouvés dans ${path}`, {
          userId, storageId, bucketName, path, itemsCount: items.length
        });

        // Séparer les fichiers et les dossiers
        const files: string[] = [];
        const folders: string[] = [];

        for (const item of items) {
          const fullPath = path ? `${path}/${item.name}` : item.name;

          // Dans Supabase, les dossiers ont un id null et peuvent avoir un metadata spécial
          // ou se terminer par "/" ou être détectés par leur absence de taille
          if (item.id === null && (item.metadata?.size === undefined || item.name.endsWith('/'))) {
            folders.push(fullPath);
            logger.info(`Dossier détecté: ${fullPath}`, { userId, storageId, bucketName, path });
          } else {
            files.push(fullPath);
            logger.info(`Fichier détecté: ${fullPath}`, { userId, storageId, bucketName, path });
          }
        }

        // Supprimer tous les fichiers du niveau actuel
        if (files.length > 0) {
          logger.info(`Suppression de ${files.length} fichiers dans ${path}`, {
            userId, storageId, bucketName, path, filesCount: files.length,
            files: files.slice(0, 5) // Afficher les 5 premiers
          });

          const { error: removeError } = await supabase.storage
            .from(bucketName)
            .remove(files);

          if (removeError) {
            logger.error(`Erreur lors de la suppression des fichiers dans ${path}`, {
              userId, storageId, bucketName, path, error: removeError,
              errorMessage: removeError.message || 'Erreur inconnue'
            });
          } else {
            logger.info(`Fichiers supprimés avec succès dans ${path}`, {
              userId, storageId, bucketName, path, deletedCount: files.length
            });
          }
        }

        // Traiter récursivement chaque sous-dossier
        for (const folderPath of folders) {
          await deleteRecursively(folderPath);
        }
      };

      // Commencer la suppression récursive depuis le dossier racine de l'utilisateur
      await deleteRecursively(storageId);

      logger.info(`Suppression complète du répertoire utilisateur terminée pour ${bucketName}`, {
        userId, storageId, bucketName
      });

    } catch (error) {
      logger.error(`Erreur lors de la suppression complète du répertoire dans ${bucketName}`, {
        userId, storageId, bucketName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Suppression exhaustive de TOUS les fichiers utilisateur dans TOUS les buckets
  private async deleteAllUserStorageFiles(userId: string) {
    try {
      logger.info('Début de deleteAllUserStorageFiles', { userId });

      // Récupérer le storage_id de l'utilisateur
      const { data: userData, error: userError } = await supabase
        .from('user_profil')
        .select('storage_id')
        .eq('user_id', userId)
        .single();

      if (userError) {
        logger.error('Erreur lors de la récupération du storage_id', { userId, error: userError });
        return;
      }

      if (!userData?.storage_id) {
        logger.warn('Aucun storage_id trouvé pour l\'utilisateur', { userId });
        return;
      }

      const storageId = userData.storage_id;
      logger.info('Storage_id récupéré, début de la suppression exhaustive des fichiers', { userId, storageId });

      // Supprimer complètement le répertoire racine de l'utilisateur dans TOUS les buckets
      const allBuckets = [
        'photo_profil',
        'galerie_realisation_client',
        'mission_photos',
        'entreprise_verification',
        'carte_visite_et_flyer',
        'support_ticket_attachments',
        'message_attachments',
        'temp_moderation'
      ];

      for (const bucketName of allBuckets) {
        await this.deleteCompleteUserDirectory(storageId, bucketName, userId);
      }

      logger.info('Suppression exhaustive des fichiers terminée avec succès', { userId, storageId });
    } catch (error) {
      logger.error('Erreur lors de la suppression exhaustive des fichiers', {
        userId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      // Re-lancer l'erreur pour que l'appelant puisse la gérer
      throw error;
    }
  }

  // Supprimer les pièces jointes des messages et tickets de support
  private async deleteUserAttachments(userId: string) {
    try {
      // Récupérer d'abord les IDs des messages de l'utilisateur
      const { data: userMessages, error: messagesError } = await supabase
        .from('user_messages')
        .select('id')
        .eq('sender_id', userId);

      if (!messagesError && userMessages && userMessages.length > 0) {
        const messageIds = userMessages.map(m => m.id);

        // Supprimer les pièces jointes des messages
        const { data: messageAttachments, error: messageAttachmentsError } = await supabase
          .from('user_message_attachments')
          .select('file_path')
          .in('message_id', messageIds);

        if (!messageAttachmentsError && messageAttachments && messageAttachments.length > 0) {
          for (const attachment of messageAttachments) {
            try {
              const filePath = extractFilePathFromUrl(attachment.file_path, 'message_attachments');
              if (filePath) {
                logger.info('Suppression pièce jointe message', { userId, filePath });

                const { error: removeError } = await supabase.storage
                  .from('message_attachments')
                  .remove([filePath]);

                if (removeError) {
                  logger.error('Erreur suppression pièce jointe message', { userId, filePath, error: removeError });
                } else {
                  logger.info('Pièce jointe message supprimée', { userId, filePath });
                }
              }
            } catch (error) {
              logger.error('Erreur lors de la suppression d\'une pièce jointe de message', { userId, filePath: attachment.file_path, error });
            }
          }
        }
      }

      // Récupérer d'abord les IDs des tickets de l'utilisateur
      const { data: userTickets, error: ticketsError } = await supabase
        .from('support_tickets')
        .select('id')
        .eq('user_id', userId);

      if (!ticketsError && userTickets && userTickets.length > 0) {
        const ticketIds = userTickets.map(t => t.id);

        // Supprimer les pièces jointes des tickets de support
        const { data: ticketAttachments, error: ticketAttachmentsError } = await supabase
          .from('support_ticket_attachments')
          .select('storage_path')
          .in('ticket_id', ticketIds);

        if (!ticketAttachmentsError && ticketAttachments && ticketAttachments.length > 0) {
          for (const attachment of ticketAttachments) {
            try {
              const filePath = extractFilePathFromUrl(attachment.storage_path, 'support_ticket_attachments');
              if (filePath) {
                logger.info('Suppression pièce jointe ticket', { userId, filePath });

                const { error: removeError } = await supabase.storage
                  .from('support_ticket_attachments')
                  .remove([filePath]);

                if (removeError) {
                  logger.error('Erreur suppression pièce jointe ticket', { userId, filePath, error: removeError });
                } else {
                  logger.info('Pièce jointe ticket supprimée', { userId, filePath });
                }
              }
            } catch (error) {
              logger.error('Erreur lors de la suppression d\'une pièce jointe de ticket', { userId, filePath: attachment.storage_path, error });
            }
          }
        }
      }

      logger.info('Pièces jointes supprimées', { userId });
    } catch (error) {
      logger.error('Erreur lors de la suppression des pièces jointes', { userId, error });
    }
  }
}

/**
 * Upload de documents de vérification entreprise (Kbis, Assurance, Identité, etc.)
 * Reçoit des fichiers en multipart/form-data
 */
export async function uploadEntrepriseVerificationDocs(req: Request, res: Response) {
  try {
    // --- NETTOYAGE DES DOCUMENTS DE PLUS DE 2 MOIS non traités ---
    const nowCleanup = new Date();
    const deuxMoisAvantCleanup = new Date(nowCleanup.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 jours
    // Récupérer tous les documents de plus de 2 mois
    const { data: oldDocs, error: oldDocsError } = await supabase
      .from('entreprise_verification_documents')
      .select('id, user_id, file_url, upload_date')
      .lt('upload_date', deuxMoisAvantCleanup.toISOString())
      .eq('status', 'pending');
    if (oldDocsError) {
      console.error('Erreur lors de la récupération des anciens documents:', oldDocsError);
    } else if (oldDocs && oldDocs.length > 0) {
      console.log('Nombre de documents à nettoyer:', oldDocs.length);
      for (const doc of oldDocs) {
        // Extraire le chemin relatif du fichier à partir de l'URL
        let filePath = null;
        if (doc.file_url) {
          const match = doc.file_url.match(/entreprise_verification\/(.+)/);
          if (match && match[1]) filePath = match[1];
        }
        // Suppression du fichier dans le bucket
        if (filePath) {
          try {
            await deleteEntrepriseVerificationDocument(doc.user_id, filePath);
          } catch (e) {
            console.error('Erreur lors de la suppression du fichier du bucket:', e);
          }
        }
        // Suppression de l'entrée SQL
        try {
          await supabase
            .from('entreprise_verification_documents')
            .delete()
            .eq('id', doc.id);
        } catch (e) {
          console.error('Erreur lors de la suppression de l\'entrée SQL:', e);
        }
      }
    }
    // --- FIN NETTOYAGE ---

    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }
    if (!req.files) {
      res.status(400).json({ error: 'Aucun fichier reçu' });
      return;
    }

    // Vérification de l'ancienneté de l'inscription (15 jours)
    const user = await dbService.getUserById(userId);
    if (!user || !user.date_inscription) {
      res.status(400).json({ error: "Impossible de vérifier la date d'inscription de l'utilisateur." });
      return;
    }
    const dateInscription = new Date(user.date_inscription);
    const now = new Date();
    const diffJours = (now.getTime() - dateInscription.getTime()) / (1000 * 60 * 60 * 24);
    if (diffJours < 15) {
      res.status(403).json({
        error: "Vous devez être inscrit depuis au moins 15 jours pour pouvoir envoyer des documents de vérification. Merci de réessayer plus tard."
      });
      return;
    }

    // Récupérer les fichiers et les types
    let files = req.files['files[]'] || req.files.files || req.files.file || req.files.docs;
    if (!files) {
      res.status(400).json({ error: 'Aucun fichier reçu' });
      return;
    }
    if (!Array.isArray(files)) files = [files];

    let types = req.body['types[]'] || req.body.types || req.body.type;
    if (!types) types = [];
    if (!Array.isArray(types)) types = [types];

    if (files.length === 0) {
      res.status(400).json({ error: 'Aucun fichier reçu' });
      return;
    }

    const allowedTypes = ['kbis', 'assurance', 'identity', 'autre'];
    const uploadedDocs = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const type = allowedTypes.includes(types[i]) ? types[i] : 'autre';
      let fileBuffer: Buffer | undefined;
      if (file.tempFilePath) {
        fileBuffer = await new Promise<Buffer>((resolve, reject) => {
          require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
            if (err) reject(err);
            else resolve(data);
          });
        });
      } else if (file.data && Buffer.isBuffer(file.data) && file.data.length > 0) {
        fileBuffer = file.data;
      } else {
        console.error('Fichier uploadé vide ou non valide:', { name: file.name, mimetype: file.mimetype });
        res.status(400).json({ error: 'Le fichier uploadé est vide ou corrompu.' });
        return;
      }
      try {
        // Upload dans Supabase Storage
        const publicUrl = await uploadEntrepriseVerificationDocument(
          userId || '',
          fileBuffer,
          file.mimetype,
          file.name
        );
        // Insertion dans la table SQL
        const { data, error } = await supabase
          .from('entreprise_verification_documents')
          .insert({
            id: uuidv4(),
            user_id: userId,
            type: type,
            file_url: publicUrl,
            file_name: file.name,
            status: 'pending',
            upload_date: new Date().toISOString()
          })
          .select()
          .single();
        if (error) throw error;
        uploadedDocs.push(data);
        
        // Log de succès pour chaque fichier uploadé
        logger.info('Document de vérification entreprise uploadé', {
          filePath: data.file_url?.split('/').pop(),
          fileType: file.mimetype,
          publicUrl: data.file_url,
          storageId: userId,
          timestamp: new Date().toLocaleString('fr-FR'),
          userId: userId
        });
      } catch (error) {
        console.error('Erreur uploadEntrepriseVerificationDocument:', error);
        res.status(500).json({ error: 'Erreur lors de l\'upload du document', details: error instanceof Error ? error.message : String(error) });
        return;
      }
    }
    return res.json({ success: true, docs: uploadedDocs });
  } catch (error) {
    console.error('Erreur uploadEntrepriseVerificationDocs:', error);
    const errMsg = (error instanceof Error) ? error.message : String(error);
    return res.status(500).json({ error: 'Erreur upload document', details: errMsg });
  }
}

/**
 * Liste des documents de vérification entreprise à contrôler (admin/modo)
 */
export async function listEntrepriseVerificationDocs(req: Request, res: Response) {
  try {
    // Vérifier le rôle
    if (!req.user || !['jobpadm', 'jobmodo'].includes(req.user.role)) {
      res.status(403).json({ error: 'Accès refusé' });
      return;
    }
    // Récupérer les filtres
    const { status, type, search, page = 1, limit = 10 } = req.query;
    let query = supabase
      .from('entreprise_verification_documents')
      .select('id, user_id, type, file_url, file_name, status, upload_date, verification_date, comments, users:user_id(email, profil_verifier, identite_verifier, entreprise_verifier, assurance_verifier)')
      .order('upload_date', { ascending: false });
    if (status) query = query.eq('status', status);
    if (type) query = query.eq('type', type);
    // Pagination
    const pageInt = parseInt(page as string, 10) || 1;
    const limitInt = parseInt(limit as string, 10) || 10;
    const from = (pageInt - 1) * limitInt;
    const to = from + limitInt - 1;
    query = query.range(from, to);
    // Exécution de la requête
    const { data: docs, error } = await query;
    if (error) {
      logger.error('Erreur Supabase listEntrepriseVerificationDocs', { error });
      res.status(500).json({ error: 'Erreur serveur', details: error });
      return;
    }
    // 2. Récupérer les profils pour tous les user_id trouvés
    const userIds = docs.map((doc: any) => doc.user_id);
    let profils: { user_id: string, nom?: string, prenom?: string }[] = [];
    if (userIds.length > 0) {
      const { data: profilsData, error: profilError } = await supabase
        .from('user_profil')
        .select('user_id, nom, prenom')
        .in('user_id', userIds);
      if (profilError) {
        logger.error('Erreur Supabase récupération profils', { profilError });
      } else {
        // Déchiffrer les données de profil
        profils = await Promise.all(profilsData.map(async profil => await decryptProfilDataAsync(profil)));
      }
    }
    // 3. Fusionner les résultats
    let docsWithProfil = await Promise.all(docs.map(async doc => {
      // Déchiffrer les données utilisateur
      const decryptedUser = doc.users ? await decryptUserDataAsync(doc.users) : null;
      const profil = profils.find(p => p.user_id === doc.user_id) as { nom?: string, prenom?: string } | undefined;
      return {
        ...doc,
        users: decryptedUser,
        user_profil: {
          nom: profil?.nom || null,
          prenom: profil?.prenom || null
        }
      };
    }));
    // 4. Filtre recherche sur nom, prénom, email (simple)
    if (search) {
      const searchLower = (search as string).toLowerCase();
      docsWithProfil = docsWithProfil.filter(doc => {
        const nom = doc.user_profil?.nom?.toLowerCase() || '';
        const prenom = doc.user_profil?.prenom?.toLowerCase() || '';
        const email = doc.users?.email?.toLowerCase() || '';
        return nom.includes(searchLower) || prenom.includes(searchLower) || email.includes(searchLower);
      });
    }
    res.json({ success: true, docs: docsWithProfil, profils });
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur récupération docs', details: error });
    return;
  }
}

/**
 * Validation ou refus d'un document de vérification entreprise
 * body: { userId, filePath, action: 'valider' | 'refuser', motif? }
 */
export async function validateEntrepriseVerificationDoc(req: Request, res: Response) {
  try {
    if (!req.user || !['jobpadm', 'jobmodo'].includes(req.user.role)) {
      res.status(403).json({ error: 'Accès refusé' });
      return;
    }
    const { documentId, status, comments } = req.body;
    if (!documentId || !status) {
      res.status(400).json({ error: 'Données manquantes' });
      return;
    }
    // Récupérer le document pour avoir le file_url, le user_id et le type
    const { data: doc, error: fetchError } = await supabase
      .from('entreprise_verification_documents')
      .select('file_url, user_id, type')
      .eq('id', documentId)
      .single();
    if (fetchError || !doc) {
      res.status(404).json({ error: 'Document introuvable' });
      return;
    }
    // Update SQL
    const { error } = await supabase
      .from('entreprise_verification_documents')
      .update({
        status: status === 'validé' ? 'approved' : 'rejected',
        verification_date: new Date().toISOString(),
        comments: comments || '',
        admin_id: req.user?.userId || ''
      })
      .eq('id', documentId);
    if (error) {
      res.status(500).json({ error: 'Erreur lors de la mise à jour', details: error });
      return;
    }
    // Si validé, supprimer tous les autres fichiers de la même catégorie pour cet utilisateur
    if (status === 'validé') {
      // Suppression physique du fichier validé
      try {
        const publicUrl = doc.file_url;
        const match = publicUrl.match(/entreprise_verification\/([^?]+)/);
        if (match && match[1]) {
          const filePath = match[1];
          await deleteEntrepriseVerificationDocument(doc.user_id, filePath);
        }
      } catch (e) {
        console.error('Erreur suppression physique du fichier validé:', e);
      }
      // Récupérer tous les autres docs du même type pour cet utilisateur
      const TYPE_LABELS: Record<string, string> = {
        kbis: 'Kbis',
        assurance: "Attestation d'assurance",
        identity: "Carte d'identité",
        autre: 'Autre document'
      };
      const typeLabel = TYPE_LABELS[doc.type] || doc.type;
      const fileName = doc.file_url ? decodeURIComponent(doc.file_url.split('/').pop() || '') : '';
      try {
        await sendEntrepriseVerificationStatus(doc.user_id, true, `Type : ${typeLabel}\nNom du fichier : ${fileName}`);
        await supabase.from('user_notifications').insert({
          user_id: doc.user_id,
          type: 'system',
          title: `Document validé : ${typeLabel}`,
          content: `Votre document de type ${typeLabel} a été validé. Votre profil entreprise est maintenant vérifié pour ce type de document.`,
          link: null,
          is_read: false,
          is_archived: false
        });
      } catch (e) {
        console.error('Erreur envoi email/notification validation doc entreprise:', e);
      }
      const { data: otherDocs, error: otherDocsError } = await supabase
        .from('entreprise_verification_documents')
        .select('id, file_url')
        .eq('user_id', doc.user_id)
        .eq('type', doc.type)
        .neq('id', documentId);
      if (!otherDocsError && otherDocs && otherDocs.length > 0) {
        for (const d of otherDocs) {
          // Suppression physique
          if (d.file_url) {
            const match = d.file_url.match(/entreprise_verification\/([^?]+)/);
            if (match && match[1]) {
              try {
                await deleteEntrepriseVerificationDocument(doc.user_id, match[1]);
              } catch (e) { /* ignore erreur suppression */ }
            }
          }
        }
        // Suppression SQL
        await supabase
          .from('entreprise_verification_documents')
          .delete()
          .eq('user_id', doc.user_id)
          .eq('type', doc.type)
          .neq('id', documentId);
      }
    }
    // Suppression physique du fichier si refusé
    if (status !== 'validé') {
      // file_url = publicUrl, il faut le chemin relatif pour le storage
      try {
        const publicUrl = doc.file_url;
        // Ex: https://api.jobpartiel.fr/api/storage-proxy/entreprise_verification/storageId/entreprise_verif/nomfichier.pdf
        // On veut: storageId/entreprise_verif/nomfichier.pdf
        const match = publicUrl.match(/entreprise_verification\/([^?]+)/);
        if (match && match[1]) {
          const filePath = match[1];
          await deleteEntrepriseVerificationDocument(doc.user_id, filePath);
        }
      } catch (e) {
        console.error('Erreur suppression physique du fichier entreprise_verif:', e);
      }
      // Envoi email + notif utilisateur (refus)
      try {
        // Déterminer le label du type
        const TYPE_LABELS: Record<string, string> = {
          kbis: 'Kbis',
          assurance: "Attestation d'assurance",
          identity: "Carte d'identité",
          autre: 'Autre document'
        };
        const typeLabel = TYPE_LABELS[doc.type] || doc.type;
        const fileName = doc.file_url ? decodeURIComponent(doc.file_url.split('/').pop() || '') : '';
        await sendEntrepriseVerificationStatus(doc.user_id, false, `Type : ${typeLabel}\nMotif : ${comments || 'Non précisé'}`);
        await supabase.from('user_notifications').insert({
          user_id: doc.user_id,
          type: 'system',
          title: `Document refusé : ${typeLabel}`,
          content: `Votre document de type ${typeLabel} a été refusé. Motif : ${comments || 'Non précisé'}`,
          link: null,
          is_read: false,
          is_archived: false
        });
      } catch (e) {
        console.error('Erreur envoi email/notification refus doc entreprise:', e);
      }
    }
    // Après validation, mettre à jour le statut profil_verifier selon le type de profil
    try {
      // Récupérer le type de profil et les statuts actuels
      const { data: profilData, error: profilError } = await supabase
        .from('user_profil')
        .select('type_de_profil')
        .eq('user_id', doc.user_id)
        .single();
      if (!profilError && profilData) {
        const { data: userStatus, error: statusError } = await supabase
          .from('users')
          .select('identite_verifier, entreprise_verifier, assurance_verifier')
          .eq('id', doc.user_id)
          .single();
        if (!statusError && userStatus) {
          let shouldBeVerified = false;
          if (profilData.type_de_profil === 'entreprise') {
            shouldBeVerified = !!(userStatus.identite_verifier && userStatus.entreprise_verifier && userStatus.assurance_verifier);
          } else {
            shouldBeVerified = !!userStatus.identite_verifier;
          }
          await supabase
            .from('users')
            .update({ profil_verifier: shouldBeVerified })
            .eq('id', doc.user_id);
        }
      }
    } catch (e) {
      console.error('Erreur mise à jour profil_verifier:', e);
    }
    // Mettre à jour le champ de vérification correspondant lors de la validation
    if (status === 'validé') {
      let fieldToUpdate = null;
      let dateFieldToUpdate = null;
      if (doc.type === 'kbis') {
        fieldToUpdate = 'entreprise_verifier';
        dateFieldToUpdate = 'date_validation_document_entreprise';
      }
      if (doc.type === 'identity') {
        fieldToUpdate = 'identite_verifier';
        dateFieldToUpdate = 'date_validation_document_identite';
      }
      if (doc.type === 'assurance') {
        fieldToUpdate = 'assurance_verifier';
        dateFieldToUpdate = 'date_validation_document_assurance';
      }
      if (fieldToUpdate) {
        await supabase
          .from('users')
          .update({ [fieldToUpdate]: true })
          .eq('id', doc.user_id);
      }
      if (dateFieldToUpdate) {
        await supabase
          .from('user_profil')
          .update({ [dateFieldToUpdate]: new Date().toISOString() })
          .eq('user_id', doc.user_id);
      }
    }
    // Recalculer profil_verifier après chaque validation ou refus
    let profilVerifierAvant = false;
    try {
      // On récupère l'état AVANT la mise à jour
      const { data: userAvant } = await supabase
        .from('users')
        .select('profil_verifier')
        .eq('id', doc.user_id)
        .single();
      profilVerifierAvant = !!userAvant?.profil_verifier;
    } catch (e) {}
    let shouldBeVerified = false;
    try {
      const { data: profilData } = await supabase
        .from('user_profil')
        .select('type_de_profil')
        .eq('user_id', doc.user_id)
        .single();
      const { data: userStatus } = await supabase
        .from('users')
        .select('identite_verifier, entreprise_verifier, assurance_verifier')
        .eq('id', doc.user_id)
        .single();
      
      // Déchiffrer les données de profil
      const decryptedProfilData = profilData ? await decryptProfilDataAsync(profilData) : null;
      
      if (decryptedProfilData && decryptedProfilData.type_de_profil === 'entreprise') {
        shouldBeVerified = !!(userStatus?.identite_verifier && userStatus?.entreprise_verifier && userStatus?.assurance_verifier);
      } else {
        shouldBeVerified = !!userStatus?.identite_verifier;
      }
      await supabase
        .from('users')
        .update({ profil_verifier: shouldBeVerified })
        .eq('id', doc.user_id);
    } catch (e) {
      console.error('Erreur recalcul profil_verifier après validation/refus:', e);
    }
    // Si profil_verifier vient de passer à true, on envoie email + notif
    if (!profilVerifierAvant && shouldBeVerified) {
      try {
        const { sendProfilVerifierEmail } = require('../services/emailServiceModeration');
        await sendProfilVerifierEmail(doc.user_id);
      } catch (e) { console.error('Erreur envoi email profil vérifié:', e); }
      try {
        await supabase.from('user_notifications').insert({
          user_id: doc.user_id,
          type: 'system',
          title: 'Profil vérifié !',
          content: 'Félicitations, votre profil est maintenant totalement vérifié sur JobPartiel. Vous bénéficiez désormais de toutes les fonctionnalités.',
          link: null,
          is_read: false,
          is_archived: false
        });
      } catch (e) { console.error('Erreur notification profil vérifié:', e); }
    }
    res.json({ success: true });
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur validation doc', details: error });
    return;
  }
}

/**
 * Suppression de tous les documents de vérification entreprise d'un utilisateur (admin/modo)
 * Body: { userId }
 */
export async function deleteAllEntrepriseVerificationDocs(req: Request, res: Response) {
  try {
    if (!req.user || !['jobpadm', 'jobmodo'].includes(req.user.role)) {
      res.status(403).json({ error: 'Accès refusé' });
      return;
    }
    const { userId } = req.body;
    if (!userId) res.status(400).json({ error: 'userId manquant' });
    // Suppression des documents
    const nb = await deleteAllEntrepriseVerificationDocuments(userId);
    // Email
    await sendAllEntrepriseDocsDeletedEmail(userId);
    // Notification
    await supabase.from('user_notifications').insert({
      user_id: userId,
      type: 'system',
      title: 'Tous vos documents de vérification entreprise ont été supprimés',
      content: `Vous aviez envoyé trop de documents. Merci de recommencer en déposant un ou deux fichiers par catégorie (Kbis, Assurance, Identité, etc.).`,
      link: null,
      is_read: false,
      is_archived: false
    });
    res.json({ success: true, deleted: nb });
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur suppression documents', details: error });
    return;
  }
}

export async function forceUserVerificationStatus(req: Request, res: Response) {
  try {
    if (!req.user || !['jobpadm', 'jobmodo'].includes(req.user.role)) {
      res.status(403).json({ error: 'Accès refusé' });
      return;
    }
    const { userId, field, value } = req.body;
    const allowedFields = ['email_verifier', 'profil_verifier', 'identite_verifier', 'entreprise_verifier', 'assurance_verifier'];
    if (!userId || !field || typeof value !== 'boolean' || !allowedFields.includes(field)) {
      res.status(400).json({ error: 'Paramètres invalides' });
      return;
    }
    const { error } = await supabase
      .from('users')
      .update({ [field]: value })
      .eq('id', userId);
    if (error) {
      res.status(500).json({ error: 'Erreur lors de la mise à jour', details: error });
      return;
    }
    // Si on désactive une des vérifications, on désactive aussi profil_verifier
    if (!value && ['identite_verifier', 'entreprise_verifier', 'assurance_verifier'].includes(field)) {
      await supabase
        .from('users')
        .update({ profil_verifier: false })
        .eq('id', userId);
    }

    // Invalider le cache Redis pour cet utilisateur
    try {
      const cacheKey = `user:${userId}`;
      const cacheKey_deux = `user:${userId}:details`;
      await redis.del(cacheKey);
      await redis.del(cacheKey_deux);
      
      // Invalider les caches spécifiques à userManagement
      await redis.del(`user_details:${userId}`);
      const userStatsKeys = await redis.keys(`user_stats:${userId}:*`);
      if (userStatsKeys.length > 0) {
        await redis.del(...userStatsKeys);
      }
      
      // Invalider aussi les caches liés aux listes d'utilisateurs admin
      const adminCacheKeys = await redis.keys('admin:users:*');
      if (adminCacheKeys.length > 0) {
        await redis.del(...adminCacheKeys);
      }
      
      console.log('Cache Redis invalidé après modification des vérifications', { userId, field, value });
    } catch (cacheError) {
      console.error('Erreur lors de l\'invalidation du cache Redis:', cacheError);
      // On continue même si l'invalidation du cache échoue
    }

    res.json({ success: true, userId, field, value });
    return;
  } catch (e) {
    res.status(500).json({ error: 'Erreur serveur', details: e });
    return;
  }
}

/**
 * Suppression d'un document de vérification entreprise (admin/modo)
 * body: { documentId }
 */
export async function deleteEntrepriseVerificationDoc(req: Request, res: Response) {
  try {
    if (!req.user || !['jobpadm', 'jobmodo'].includes(req.user.role)) {
      res.status(403).json({ error: 'Accès refusé' });
      return;
    }
    const { documentId } = req.body;
    if (!documentId) res.status(400).json({ error: 'documentId manquant' });
    // Récupérer le document pour avoir le file_url et user_id
    const { data: doc, error: fetchError } = await supabase
      .from('entreprise_verification_documents')
      .select('id, user_id, file_url')
      .eq('id', documentId)
      .single();
    if (fetchError || !doc) return res.status(404).json({ error: 'Document introuvable' });
    // Suppression physique du fichier
    if (doc.file_url) {
      const match = doc.file_url.match(/entreprise_verification\/([^?]+)/);
      if (match && match[1]) {
        try {
          await deleteEntrepriseVerificationDocument(doc.user_id, match[1]);
        } catch (e) {
          // On continue même si une suppression échoue
        }
      }
    }
    // Suppression SQL
    const { error: deleteError } = await supabase
      .from('entreprise_verification_documents')
      .delete()
      .eq('id', documentId);
    if (deleteError) return res.status(500).json({ error: 'Erreur suppression SQL', details: deleteError });
    res.json({ success: true });
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur suppression document', details: error });
    return;
  }
}

/**
 * Récupère les documents de vérification entreprise de l'utilisateur connecté
 */
export async function getMyEntrepriseVerificationDocs(req: Request, res: Response) {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }
    // Récupérer les documents de l'utilisateur
    const { data: docs, error } = await supabase
      .from('entreprise_verification_documents')
      .select('id, type, file_url, file_name, status, upload_date, verification_date, comments')
      .eq('user_id', userId)
      .order('upload_date', { ascending: false });
    if (error) {
      res.status(500).json({ error: 'Erreur récupération docs', details: error });
      return;
    }
    res.json({ success: true, docs });
    return;
  } catch (error) {
    res.status(500).json({ error: 'Erreur récupération docs', details: error });
    return;
  }
}