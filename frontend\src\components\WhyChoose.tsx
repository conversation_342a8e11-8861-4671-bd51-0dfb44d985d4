import { Clock, Briefcase, Target, Shield, Users, Star, ChevronRight, Zap, Wallet, Award, Lightbulb, Brain } from 'lucide-react';
import { Link } from 'react-router-dom';

const WhyChoose = () => {
  const benefits = [
    {
      title: "Intelligence Artificielle",
      icon: Brain,
      forJobbers: true,
      description: "Une IA puissante à votre service",
      items: [
        "Génération automatique de descriptions de services",
        "Suggestions personnalisées de missions adaptées à votre profil",
        "Optimisation de vos offres pour maximiser vos chances de succès"
      ]
    },
    {
      title: "Flexibilité Totale",
      icon: Clock,
      forJobbers: true,
      description: "Travaillez quand vous le souhaitez, sans contraintes",
      items: [
        "Choisissez vos horaires selon vos disponibilités",
        "Sélectionnez uniquement les missions qui vous intéressent",
        "Conciliez facilement études, vie de famille ou autre emploi"
      ]
    },
    {
      title: "Revenus Complémentaires",
      icon: Wallet,
      forJobbers: true,
      description: "Augmentez vos revenus selon vos besoins",
      items: [
        "Gagnez 300€ à 1500€ chaque mois",
        "Recevez vos paiements rapidement et en toute sécurité",
        "Suivez vos gains en temps réel sur votre tableau de bord"
      ]
    },
    {
      title: "Services Sur-Mesure",
      icon: Briefcase,
      forJobbers: false,
      description: "Trouvez le prestataire idéal pour vos besoins",
      items: [
        "Large éventail de services disponibles près de chez vous",
        "Professionnels vérifiés et évalués par la communauté",
        "Tarifs transparents et compétitifs"
      ]
    },
    {
      title: "Visibilité Garantie",
      icon: Target,
      forJobbers: true,
      description: "Faites-vous remarquer par les clients locaux",
      items: [
        "Profil mis en avant auprès des clients de votre région",
        "Algorithme intelligent qui vous propose des missions adaptées",
        "Développez votre clientèle fidèle grâce aux avis positifs"
      ]
    },
    {
      title: "Rapidité d'Exécution",
      icon: Zap,
      forJobbers: false,
      description: "Obtenez de l'aide rapidement, sans attendre",
      items: [
        "Trouvez un prestataire disponible en quelques clics",
        "Service client réactif en cas de besoin",
        "Missions accomplies dans les délais convenus"
      ]
    },
    {
      title: "Sécurité & Confiance",
      icon: Shield,
      forJobbers: false,
      description: "Transactions et services 100% sécurisés",
      items: [
        "Système d'évaluation transparent et fiable",
        "Vérification d'identité pour tous les prestataires",
        "Garantie satisfaction ou remboursement"
      ]
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Éléments décoratifs */}
      <div className="absolute -top-24 -left-24 w-48 h-48">
        <div className="w-full h-full bg-[#FF7A35]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-[#FF7A35]/20 to-transparent rounded-full transform rotate-45"></div>
      </div>

      <div className="absolute -bottom-24 -right-24 w-48 h-48">
        <div className="w-full h-full bg-[#FF7A35]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute inset-0 bg-gradient-to-tl from-[#FF7A35]/20 to-transparent rounded-full transform -rotate-45"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center px-4 py-1.5 mb-4 bg-[#FF7A35]/10 rounded-full">
            <Star className="h-4 w-4 text-[#FF7A35] mr-2" />
            <span className="text-sm font-medium text-[#FF7A35]">La plateforme qui fait la différence</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Pourquoi Choisir <span className="text-[#FF7A35]">JobPartiel</span> ?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            La solution idéale pour trouver des missions flexibles ou des prestataires de qualité près de chez vous
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {benefits.map((benefit, index) => (
            <div key={index}
              className="group bg-white rounded-xl p-2 sm:p-4 md:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-100 flex flex-col h-full">
              <div className="flex items-center mb-2 sm:mb-3">
                <div className="bg-[#FF7A35] bg-opacity-10 rounded-lg p-1.5 sm:p-2 md:p-3 mr-2 group-hover:bg-[#FF7A35] group-hover:text-white transition-colors duration-300">
                  <benefit.icon className="h-6 w-6 text-[#FF7A35] group-hover:text-white" style={{ minWidth: '16px', minHeight: '16px' }} />
                </div>
                <h3 className="font-bold text-gray-900 text-lg">{benefit.title}</h3>
              </div>

              {benefit.forJobbers && (
                <div className="flex items-center mb-2 sm:mb-3 bg-blue-50 text-blue-700 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs font-medium">
                  <Users className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span>Idéal pour les jobbeurs</span>
                </div>
              )}

              {!benefit.forJobbers && (
                <div className="flex items-center mb-2 sm:mb-3 bg-green-50 text-green-700 px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs font-medium">
                  <Award className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span>Pour ceux qui cherchent des services</span>
                </div>
              )}

              <p className="text-gray-700 mb-3">{benefit.description}</p>

              <ul className="space-y-1 sm:space-y-2 mb-2 sm:mb-4 flex-grow">
                {benefit.items.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start">
                    <ChevronRight className="h-5 w-5 text-[#FF7A35] shrink-0 mt-0.5" style={{ minWidth: '16px', minHeight: '16px' }} />
                    <span className="text-gray-600 ml-1">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Nouvelle section IA */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-16 mb-16">
        <div className="relative p-6 md:p-8 bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          {/* Fond décoratif */}
          <div className="absolute -bottom-8 -right-8 w-48 h-48 opacity-10">
            <svg viewBox="0 0 24 24" fill="#FF7A35" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" />
              <path d="M12 10.5C13.1046 10.5 14 9.60457 14 8.5C14 7.39543 13.1046 6.5 12 6.5C10.8954 6.5 10 7.39543 10 8.5C10 9.60457 10.8954 10.5 12 10.5Z" />
              <path d="M12 11.5C9.33 11.5 7 13.43 7 16H17C17 13.43 14.67 11.5 12 11.5Z" />
            </svg>
          </div>

          <div className="relative z-10">
            <div className="flex items-center justify-center mb-6">
              <div className="inline-flex items-center justify-center px-4 py-1.5 bg-[#FF7A35]/10 rounded-full">
                <Lightbulb className="h-4 w-4 text-[#FF7A35] mr-2" />
                <span className="text-sm font-medium text-[#FF7A35]">Notre innovation exclusive</span>
              </div>
            </div>
            
            <h2 className="text-3xl font-bold text-center mb-8">
              <span className="relative inline-block">
                <span className="text-gray-900">L'Intelligence Artificielle qui fait la </span>
                <span className="bg-gradient-to-r from-[#FF7A35] to-[#FF965E] text-transparent bg-clip-text">différence</span>
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#FF7A35] to-[#FF965E] rounded-full"></span>
              </span>
            </h2>

            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white p-6 rounded-xl border border-gray-100 shadow-md hover:shadow-lg transition-all">
                <div className="h-12 w-12 bg-[#FF7A35]/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="h-6 w-6 text-[#FF7A35]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3 12H4M12 3V4M20 12H21M12 20V21M18.36 18.36L17.64 17.64M18.36 5.64L17.64 6.36M5.64 18.36L6.36 17.64M5.64 5.64L6.36 6.36" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Trouvez plus de missions</h3>
                <p className="text-gray-600">Notre IA analyse votre profil et vous suggère les missions les plus adaptées à vos compétences et disponibilités.</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl border border-gray-100 shadow-md hover:shadow-lg transition-all">
                <div className="h-12 w-12 bg-[#FF7A35]/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="h-6 w-6 text-[#FF7A35]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Créez du contenu parfait</h3>
                <p className="text-gray-600">Utilisez notre IA pour générer des descriptions de services attrayantes et professionnelles en quelques secondes.</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl border border-gray-100 shadow-md hover:shadow-lg transition-all">
                <div className="h-12 w-12 bg-[#FF7A35]/10 rounded-lg flex items-center justify-center mb-4">
                  <svg className="h-6 w-6 text-[#FF7A35]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">Recevez plus d'avis positifs</h3>
                <p className="text-gray-600">Notre IA vous aide à répondre aux avis clients et à améliorer votre réputation sur la plateforme.</p>
              </div>
            </div>

            <div className="flex justify-center">
              <a href="/inscription" className="flex items-center gap-2 px-6 py-3 bg-[#FF7A35] text-white rounded-lg hover:bg-[#E55A20] transition-all">
                <span>Essayer l'IA maintenant</span>
                <ChevronRight className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Rejoindre la Communauté */}
        <div className="relative mt-0">
          {/* Fond décoratif */}
          <div className="absolute -inset-4 bg-gradient-to-r from-[#FF7A35] to-[#FF9A35] opacity-5 rounded-xl transform rotate-1"></div>

          {/* Contenu principal */}
          <div className="relative ">
            <div className="flex flex-col md:flex-row items-stretch">
              {/* Contenu */}
              <div className="flex-1 p-8 md:p-10">
                <div className="flex flex-col items-center space-y-6">
                  {/* Titre */}
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center w-full">
                    Rejoignez la communauté <span className="text-[#FF7A35]">JobPartiel</span>
                  </h2>

                  {/* Description */}
                  <p className="text-gray-700 text-lg leading-relaxed max-w-3xl text-center">
                    <span className="text-[#FF7A35] font-bold">JobPartiel.fr</span> vous permet, que vous soyez{' '}
                    <span className="relative inline-block group">
                      <span className="font-semibold group-hover:text-[#FF7A35] transition-colors duration-300">retraité</span>
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF7A35] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </span>,{' '}
                    <span className="relative inline-block group">
                      <span className="font-semibold group-hover:text-[#FF7A35] transition-colors duration-300">étudiant</span>
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF7A35] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </span>,{' '}
                    <span className="relative inline-block group">
                      <span className="font-semibold group-hover:text-[#FF7A35] transition-colors duration-300">demandeur d'emploi</span>
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF7A35] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </span>,{' '}
                    <span className="relative inline-block group">
                      <span className="font-semibold group-hover:text-[#FF7A35] transition-colors duration-300">professionnel</span>
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF7A35] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </span> ou{' '}
                    <span className="relative inline-block group">
                      <span className="font-semibold group-hover:text-[#FF7A35] transition-colors duration-300">particulier</span>
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#FF7A35] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </span>,{' '}
                    de proposer vos compétences et d'être contacté directement par des clients en quête de services à domicile.
                  </p>

                  {/* Ligne décorative */}
                  <div className="flex items-center w-full max-w-xs">
                    <div className="flex-1 h-px bg-gradient-to-r from-transparent via-[#FF7A35] to-transparent opacity-30"></div>
                    <div className="w-2 h-2 rounded-full bg-[#FF7A35] mx-3"></div>
                    <div className="flex-1 h-px bg-gradient-to-r from-[#FF7A35] via-[#FF7A35] to-transparent opacity-30"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/* CTA Section */}
        <div className="text-center">
          <Link to="/inscription" className="inline-flex items-center justify-center px-8 py-4 bg-[#FF7A35] text-white font-medium rounded-lg shadow-lg hover:bg-[#E86A25] transition-all duration-300 transform hover:scale-105">
            Rejoindre JobPartiel maintenant
            <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
          <p className="mt-4 text-gray-500 text-sm">Inscription gratuite et sans engagement</p>
        </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChoose;