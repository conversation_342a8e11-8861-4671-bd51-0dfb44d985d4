import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { decryptInvoiceDataAsync } from '../utils/encryption';
import * as XLSX from 'xlsx';
import PDFDocument from 'pdfkit';

// Configuration du cache pour les statistiques de facturation
const BILLING_STATS_CACHE_PREFIX = 'billing_stats:';
const CACHE_TTL = 1800; // 30 minutes

// Interface pour les statistiques de facturation détaillées
interface DetailedBillingStats {
  // Statistiques globales
  summary: {
    totalDocuments: number;
    totalAmount: number;
    averageAmount: number;
    pendingAmount: number;
    paidAmount: number;
    overdueAmount: number;
    rejectedAmount: number;
  };
  
  // Répartition par type de document
  byType: {
    [key: string]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };
  
  // Répartition par statut
  byStatus: {
    [key: string]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };
  
  // Répartition par client
  byClient: Array<{
    clientName: string;
    count: number;
    amount: number;
    percentage: number;
  }>;
  
  // Évolution dans le temps (6 derniers mois)
  monthly: Array<{
    month: string;
    quotes: number;
    invoices: number;
    creditNotes: number;
    amount: number;
    count: number;
  }>;
  
  // Délai moyen de paiement
  paymentStats: {
    averagePaymentDelay: number;
    paymentDelayByMonth: Array<{
      month: string;
      delay: number;
      count: number;
    }>;
  };
  
  // Statistiques sur les documents reçus (en tant que client)
  received: {
    totalDocuments: number;
    totalAmount: number;
    byType: {
      [key: string]: {
        count: number;
        amount: number;
      };
    };
    byStatus: {
      [key: string]: {
        count: number;
        amount: number;
      };
    };
    bySender: Array<{
      senderName: string;
      count: number;
      amount: number;
    }>;
  };
}

/**
 * Récupère les statistiques détaillées de facturation pour un utilisateur
 */
export const getBillingStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Récupération des filtres de la requête
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const type = req.query.type as 'devis' | 'facture' | 'avoir' | undefined;
    
    // Création d'une clé de cache unique en fonction des filtres
    let cacheKey = `${BILLING_STATS_CACHE_PREFIX}${userId}`;
    if (startDate) cacheKey += `:start_${startDate.toISOString()}`;
    if (endDate) cacheKey += `:end_${endDate.toISOString()}`;
    if (type) cacheKey += `:type_${type}`;
    
    // Vérifier si les statistiques sont en cache
    const cachedStats = await redis.get(cacheKey);
    
    if (cachedStats) {
      return res.status(200).json({
        success: true,
        data: JSON.parse(cachedStats)
      });
    }
    
    // Construction de la requête Supabase avec filtres
    let documentsQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('user_id', userId);
    
    // Ajouter les filtres à la requête
    if (startDate) {
      documentsQuery = documentsQuery.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      // Ajouter un jour à la date de fin pour inclure tout le jour
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      documentsQuery = documentsQuery.lt('created_at', nextDay.toISOString());
    }
    
    if (type) {
      documentsQuery = documentsQuery.eq('type', type);
    }
    
    // Récupérer toutes les factures, devis et avoirs de l'utilisateur avec les filtres
    const { data: documents, error: documentsError } = await documentsQuery;
    
    if (documentsError) {
      logger.error('Erreur lors de la récupération des documents:', documentsError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des documents',
        error: documentsError.message
      });
    }
    
    // Construction de la requête pour les documents reçus avec filtres
    let receivedQuotesQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('type', 'devis')
      .eq('client_email', req.user?.email);
      
    let receivedInvoicesQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('type', 'facture')
      .eq('client_email', req.user?.email);
      
    let receivedCreditNotesQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('type', 'avoir')
      .eq('client_email', req.user?.email);
    
    // Ajouter les filtres de date aux requêtes pour les documents reçus
    if (startDate) {
      receivedQuotesQuery = receivedQuotesQuery.gte('created_at', startDate.toISOString());
      receivedInvoicesQuery = receivedInvoicesQuery.gte('created_at', startDate.toISOString());
      receivedCreditNotesQuery = receivedCreditNotesQuery.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      receivedQuotesQuery = receivedQuotesQuery.lt('created_at', nextDay.toISOString());
      receivedInvoicesQuery = receivedInvoicesQuery.lt('created_at', nextDay.toISOString());
      receivedCreditNotesQuery = receivedCreditNotesQuery.lt('created_at', nextDay.toISOString());
    }
    
    // Récupérer les documents reçus
    const { data: receivedQuotes, error: receivedQuotesError } = await receivedQuotesQuery;
    const { data: receivedInvoices, error: receivedInvoicesError } = await receivedInvoicesQuery;
    const { data: receivedCreditNotes, error: receivedCreditNotesError } = await receivedCreditNotesQuery;
    
    if (receivedQuotesError || receivedInvoicesError || receivedCreditNotesError) {
      logger.error('Erreur lors de la récupération des documents reçus:', { 
        receivedQuotesError, 
        receivedInvoicesError, 
        receivedCreditNotesError 
      });
    }
    
    // Déchiffrer les données des documents
    const decryptedDocuments = await Promise.all(
      (documents || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedQuotes = await Promise.all(
      (receivedQuotes || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedInvoices = await Promise.all(
      (receivedInvoices || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedCreditNotes = await Promise.all(
      (receivedCreditNotes || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    // Regrouper tous les documents reçus
    const receivedDocuments = [
      ...decryptedReceivedQuotes,
      ...decryptedReceivedInvoices,
      ...decryptedReceivedCreditNotes
    ];
    
    // Calculer les statistiques
    const stats = calculateBillingStats(decryptedDocuments, receivedDocuments);
    
    // Mettre en cache les statistiques
    await redis.set(cacheKey, JSON.stringify(stats), 'EX', CACHE_TTL);
    
    return res.status(200).json({
      success: true,
      data: stats
    });
    
  } catch (error: any) {
    logger.error('Erreur lors du calcul des statistiques de facturation:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques de facturation',
      error: error.message
    });
  }
};

/**
 * Calcule les statistiques détaillées de facturation à partir des documents
 */
function calculateBillingStats(documents: any[], receivedDocuments: any[]): DetailedBillingStats {
  // Préparer les statistiques
  const stats: DetailedBillingStats = {
    summary: {
      totalDocuments: documents.length,
      totalAmount: 0,
      averageAmount: 0,
      pendingAmount: 0,
      paidAmount: 0,
      overdueAmount: 0,
      rejectedAmount: 0
    },
    byType: {
      devis: { count: 0, amount: 0, percentage: 0 },
      facture: { count: 0, amount: 0, percentage: 0 },
      avoir: { count: 0, amount: 0, percentage: 0 }
    },
    byStatus: {},
    byClient: [],
    monthly: [],
    paymentStats: {
      averagePaymentDelay: 0,
      paymentDelayByMonth: []
    },
    received: {
      totalDocuments: receivedDocuments.length,
      totalAmount: 0,
      byType: {
        devis: { count: 0, amount: 0 },
        facture: { count: 0, amount: 0 },
        avoir: { count: 0, amount: 0 }
      },
      byStatus: {},
      bySender: []
    }
  };
  
  // Calculer les statistiques pour les documents émis
  if (documents.length > 0) {
    // Clients et leurs totaux
    const clientStats: { [clientName: string]: { count: number; amount: number } } = {};
    
    // Évolution mensuelle
    const monthlyStats: { 
      [month: string]: { 
        quotes: number; 
        invoices: number; 
        creditNotes: number; 
        amount: number; 
        count: number 
      } 
    } = {};
    
    // Initialiser les 6 derniers mois
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      monthlyStats[monthKey] = { 
        quotes: 0, 
        invoices: 0, 
        creditNotes: 0, 
        amount: 0, 
        count: 0 
      };
    }
    
    // Calcul des délais de paiement
    let totalPaymentDelay = 0;
    let paymentCount = 0;
    const paymentDelayByMonth: { [month: string]: { totalDelay: number; count: number } } = {};
    
    // Initialiser les mêmes 6 mois pour les délais de paiement
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      paymentDelayByMonth[monthKey] = { totalDelay: 0, count: 0 };
    }
    
    // Parcourir tous les documents
    documents.forEach(doc => {
      // Statistiques globales
      const amount = doc.total_ttc || 0;
      stats.summary.totalAmount += amount;
      
      // Statistiques par type
      if (stats.byType[doc.type]) {
        stats.byType[doc.type].count++;
        stats.byType[doc.type].amount += amount;
      }
      
      // Statistiques par statut
      if (!stats.byStatus[doc.statut]) {
        stats.byStatus[doc.statut] = { count: 0, amount: 0, percentage: 0 };
      }
      stats.byStatus[doc.statut].count++;
      stats.byStatus[doc.statut].amount += amount;
      
      // Montants par statut spécifique
      if (doc.statut === 'paye' || doc.statut === 'payé') {
        stats.summary.paidAmount += amount;
      } else if (doc.statut === 'en_retard' || doc.statut === 'en retard') {
        stats.summary.overdueAmount += amount;
      } else if (doc.statut === 'refuse' || doc.statut === 'refusé') {
        stats.summary.rejectedAmount += amount;
      } else if (doc.statut === 'brouillon' || doc.statut === 'émis' || doc.statut === 'envoye' || doc.statut === 'à payer') {
        stats.summary.pendingAmount += amount;
      }
      
      // Statistiques par client
      if (doc.client_name) {
        if (!clientStats[doc.client_name]) {
          clientStats[doc.client_name] = { count: 0, amount: 0 };
        }
        clientStats[doc.client_name].count++;
        clientStats[doc.client_name].amount += amount;
      }
      
      // Évolution mensuelle
      if (doc.created_at) {
        const date = new Date(doc.created_at);
        const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        
        if (monthlyStats[monthKey]) {
          monthlyStats[monthKey].amount += amount;
          monthlyStats[monthKey].count++;
          
          if (doc.type === 'devis') {
            monthlyStats[monthKey].quotes++;
          } else if (doc.type === 'facture') {
            monthlyStats[monthKey].invoices++;
          } else if (doc.type === 'avoir') {
            monthlyStats[monthKey].creditNotes++;
          }
        }
      }
      
      // Calcul du délai de paiement pour les factures payées
      if (doc.type === 'facture' && (doc.statut === 'paye' || doc.statut === 'payé') && doc.date_paiement) {
        const creationDate = new Date(doc.created_at);
        const paymentDate = new Date(doc.date_paiement);
        const delayInDays = Math.round((paymentDate.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (delayInDays >= 0) {
          totalPaymentDelay += delayInDays;
          paymentCount++;
          
          // Ajouter aux statistiques mensuelles de délai
          const monthKey = `${paymentDate.getFullYear()}-${(paymentDate.getMonth() + 1).toString().padStart(2, '0')}`;
          if (paymentDelayByMonth[monthKey]) {
            paymentDelayByMonth[monthKey].totalDelay += delayInDays;
            paymentDelayByMonth[monthKey].count++;
          }
        }
      }
    });
    
    // Finaliser les moyennes et pourcentages
    stats.summary.averageAmount = documents.length > 0 
      ? Math.round((stats.summary.totalAmount / documents.length) * 100) / 100 
      : 0;
    
    // Pourcentages par type
    Object.keys(stats.byType).forEach(type => {
      stats.byType[type].percentage = documents.length > 0 
        ? Math.round((stats.byType[type].count / documents.length) * 100) 
        : 0;
    });
    
    // Pourcentages par statut
    Object.keys(stats.byStatus).forEach(status => {
      stats.byStatus[status].percentage = documents.length > 0 
        ? Math.round((stats.byStatus[status].count / documents.length) * 100) 
        : 0;
    });
    
    // Trier et limiter le nombre de clients
    stats.byClient = Object.entries(clientStats)
      .map(([clientName, data]) => ({
        clientName,
        count: data.count,
        amount: data.amount,
        percentage: documents.length > 0 ? Math.round((data.count / documents.length) * 100) : 0
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 10); // Limiter aux 10 principaux clients
    
    // Convertir les statistiques mensuelles en tableau
    stats.monthly = Object.entries(monthlyStats)
      .map(([month, data]) => ({
        month,
        quotes: data.quotes,
        invoices: data.invoices,
        creditNotes: data.creditNotes,
        amount: Math.round(data.amount * 100) / 100,
        count: data.count
      }))
      .sort((a, b) => a.month.localeCompare(b.month));
    
    // Finaliser les statistiques de délai de paiement
    stats.paymentStats.averagePaymentDelay = paymentCount > 0 
      ? Math.round(totalPaymentDelay / paymentCount) 
      : 0;
    
    stats.paymentStats.paymentDelayByMonth = Object.entries(paymentDelayByMonth)
      .map(([month, data]) => ({
        month,
        delay: data.count > 0 ? Math.round(data.totalDelay / data.count) : 0,
        count: data.count
      }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }
  
  // Calculer les statistiques pour les documents reçus
  if (receivedDocuments.length > 0) {
    // Map pour les statistiques par expéditeur
    const senderStats: { [senderName: string]: { count: number; amount: number } } = {};
    
    receivedDocuments.forEach(doc => {
      const amount = doc.total_ttc || 0;
      stats.received.totalAmount += amount;
      
      // Statistiques par type
      if (stats.received.byType[doc.type]) {
        stats.received.byType[doc.type].count++;
        stats.received.byType[doc.type].amount += amount;
      }
      
      // Statistiques par statut
      if (!stats.received.byStatus[doc.statut]) {
        stats.received.byStatus[doc.statut] = { count: 0, amount: 0 };
      }
      stats.received.byStatus[doc.statut].count++;
      stats.received.byStatus[doc.statut].amount += amount;
      
      // Statistiques par expéditeur
      const senderName = doc.sender_info?.nom 
        ? `${doc.sender_info.prenom || ''} ${doc.sender_info.nom}`.trim() 
        : (doc.sender_info?.entreprise || 'Inconnu');
      
      if (!senderStats[senderName]) {
        senderStats[senderName] = { count: 0, amount: 0 };
      }
      senderStats[senderName].count++;
      senderStats[senderName].amount += amount;
    });
    
    // Trier et limiter le nombre d'expéditeurs
    stats.received.bySender = Object.entries(senderStats)
      .map(([senderName, data]) => ({
        senderName,
        count: data.count,
        amount: data.amount
      }))
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5); // Limiter aux 5 principaux expéditeurs
  }
  
  return stats;
}

/**
 * Exporte les statistiques de facturation au format Excel
 */
export const exportBillingStatsToExcel = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Récupération des filtres de la requête
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const type = req.query.type as 'devis' | 'facture' | 'avoir' | undefined;
    
    // Construction de la requête Supabase avec filtres
    let documentsQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('user_id', userId);
    
    // Ajouter les filtres à la requête
    if (startDate) {
      documentsQuery = documentsQuery.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      // Ajouter un jour à la date de fin pour inclure tout le jour
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      documentsQuery = documentsQuery.lt('created_at', nextDay.toISOString());
    }
    
    if (type) {
      documentsQuery = documentsQuery.eq('type', type);
    }
    
    // Récupérer les documents
    const { data: documents, error: documentsError } = await documentsQuery;
    
    if (documentsError) {
      logger.error('Erreur lors de la récupération des documents pour export Excel:', documentsError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des documents',
        error: documentsError.message
      });
    }
    
    // Déchiffrer les données
    const decryptedDocuments = await Promise.all(
      (documents || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    // Préparer les données pour Excel
    const excelData = decryptedDocuments.map(doc => ({
      'Type': doc.type === 'devis' ? 'Devis' : doc.type === 'facture' ? 'Facture' : 'Avoir',
      'Numéro': doc.number || 'N/A',
      'Date de création': new Date(doc.created_at).toLocaleDateString('fr-FR'),
      'Client': doc.client_name || '',
      'Statut': doc.statut || '',
      'Montant HT (€)': doc.total_ht || 0,
      'Montant TVA (€)': doc.total_tva || 0,
      'Montant TTC (€)': doc.total_ttc || 0,
      'Description': doc.description || '',
      'Date de validité': doc.date_validite ? new Date(doc.date_validite).toLocaleDateString('fr-FR') : '',
      'Date de paiement': doc.date_paiement ? new Date(doc.date_paiement).toLocaleDateString('fr-FR') : ''
    }));

    // Créer le workbook Excel
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Ajuster la largeur des colonnes
    const columnWidths = [
      { wch: 10 }, // Type
      { wch: 20 }, // Numéro
      { wch: 15 }, // Date de création
      { wch: 30 }, // Client
      { wch: 15 }, // Statut
      { wch: 15 }, // Montant HT
      { wch: 15 }, // Montant TVA
      { wch: 15 }, // Montant TTC
      { wch: 40 }, // Description
      { wch: 15 }, // Date de validité
      { wch: 15 }  // Date de paiement
    ];
    worksheet['!cols'] = columnWidths;

    // Ajouter la feuille au workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Statistiques Facturation');

    // Générer le buffer Excel
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Définir les headers pour le téléchargement
    const fileName = `statistiques_facturation_${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', excelBuffer.length);

    // Envoyer le fichier
    res.send(excelBuffer);
    
  } catch (error: any) {
    logger.error('Erreur lors de l\'export Excel des statistiques:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'export Excel des statistiques',
      error: error.message
    });
  }
};

/**
 * Exporte les statistiques de facturation au format PDF
 */
export const exportBillingStatsToPDF = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // Récupération des filtres de la requête
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const type = req.query.type as 'devis' | 'facture' | 'avoir' | undefined;
    
    // Récupérer les statistiques complètes en utilisant la même logique que getBillingStats
    let documentsQuery = supabase
      .from('invoices')
      .select(`
        *,
        invoice_items (*)
      `)
      .eq('user_id', userId);
    
    // Ajouter les filtres à la requête
    if (startDate) {
      documentsQuery = documentsQuery.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      documentsQuery = documentsQuery.lt('created_at', nextDay.toISOString());
    }
    
    if (type) {
      documentsQuery = documentsQuery.eq('type', type);
    }
    
    // Récupérer les documents reçus aussi
    const receivedQuotesQuery = supabase
      .from('invoices')
      .select('*')
      .eq('type', 'devis')
      .eq('client_email', req.user?.email);
      
    const receivedInvoicesQuery = supabase
      .from('invoices')
      .select('*')
      .eq('type', 'facture')
      .eq('client_email', req.user?.email);
      
    const receivedCreditNotesQuery = supabase
      .from('invoices')
      .select('*')
      .eq('type', 'avoir')
      .eq('client_email', req.user?.email);
    
    // Exécuter toutes les requêtes en parallèle
    const [
      { data: documents, error: documentsError },
      { data: receivedQuotes, error: receivedQuotesError },
      { data: receivedInvoices, error: receivedInvoicesError },
      { data: receivedCreditNotes, error: receivedCreditNotesError }
    ] = await Promise.all([
      documentsQuery,
      receivedQuotesQuery,
      receivedInvoicesQuery,
      receivedCreditNotesQuery
    ]);
    
    if (documentsError) {
      logger.error('Erreur lors de la récupération des documents pour export PDF:', documentsError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des documents',
        error: documentsError.message
      });
    }
    
    // Déchiffrer les données
    const decryptedDocuments = await Promise.all(
      (documents || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedQuotes = await Promise.all(
      (receivedQuotes || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedInvoices = await Promise.all(
      (receivedInvoices || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const decryptedReceivedCreditNotes = await Promise.all(
      (receivedCreditNotes || []).map(doc => decryptInvoiceDataAsync(doc))
    );
    
    const receivedDocuments = [
      ...decryptedReceivedQuotes,
      ...decryptedReceivedInvoices,
      ...decryptedReceivedCreditNotes
    ];
    
    // Calculer les statistiques complètes
    const stats = calculateBillingStats(decryptedDocuments, receivedDocuments);
    
    // Créer le document PDF
    const doc = new PDFDocument({
      size: 'A4',
      margin: 40,
      info: {
        Title: 'Rapport de Statistiques de Facturation',
        Author: 'JobPartiel.fr',
        Subject: 'Analyse complète des données de facturation',
        Keywords: 'facturation, statistiques, rapport, analyse'
      }
    });

    // Configurer les en-têtes pour le téléchargement
    const today = new Date().toISOString().split('T')[0];
    const fileName = `rapport_facturation_${today}.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

    // Pipe le PDF vers la réponse
    doc.pipe(res);
    
    // Couleurs du thème
    const primaryColor = '#FF7A35';
    const secondaryColor = '#FF9C5E';
    const greenColor = '#36B37E';
    const blueColor = '#4670FD';
    const grayColor = '#6b7280';
    const lightGray = '#f3f4f6';
    const darkGray = '#374151';
    
    let currentY = 40;
    let pageNumber = 1;
    
    // Fonction pour dessiner le pied de page
    const drawFooter = (pageNum: number) => {
      // Ligne de séparation
      doc.moveTo(40, 770)
         .lineTo(555, 770)
         .strokeColor('#e5e7eb')
         .lineWidth(1)
         .stroke();
      
      // Texte du pied de page
      doc.fontSize(8)
         .font('Helvetica')
         .fillColor(grayColor)
         .text('JobPartiel.fr', 40, 780)
         .text(`Rapport généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`, 40, 780, { align: 'center', width: 515 })
         .text(`Page ${pageNum}`, 40, 780, { align: 'right', width: 515 });
    };
    
    // Fonction utilitaire pour dessiner des barres de progression
    const drawProgressBar = (percentage: number, x: number, y: number, width: number, height: number, color: string) => {
      // Fond de la barre
      doc.rect(x, y, width, height)
         .fillColor('#e5e7eb')
         .fill();
      
      // Barre de progression
      const progressWidth = (width * percentage) / 100;
      doc.rect(x, y, progressWidth, height)
         .fillColor(color)
         .fill();
    };
    
    // Fonction pour dessiner un graphique en barres simple
    const drawBarChart = (data: any[], x: number, y: number, width: number, height: number, title: string) => {
      // Titre du graphique
      doc.fontSize(12)
         .font('Helvetica-Bold')
         .fillColor(darkGray)
         .text(title, x, y);
      
      y += 25;
      
      // Cadre du graphique
      doc.rect(x, y, width, height)
         .strokeColor('#e5e7eb')
         .lineWidth(1)
         .stroke();
      
      if (data.length === 0) {
        doc.fontSize(10)
           .font('Helvetica')
           .fillColor(grayColor)
           .text('Aucune donnée disponible', x + width/2 - 50, y + height/2);
        return y + height + 20;
      }
      
      const maxValue = Math.max(...data.map(d => d.value));
      const barWidth = (width - 40) / data.length;
      const colors = [primaryColor, secondaryColor, greenColor, blueColor];
      
      data.forEach((item, index) => {
        const barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 40) : 0;
        const barX = x + 20 + (index * barWidth);
        const barY = y + height - 20 - barHeight;
        
        // Dessiner la barre
        doc.rect(barX + 5, barY, barWidth - 10, barHeight)
           .fillColor(colors[index % colors.length])
           .fill();
        
        // Label
        doc.fontSize(8)
           .font('Helvetica')
           .fillColor(darkGray)
           .text(item.name.substring(0, 8), barX, y + height - 15, { width: barWidth, align: 'center' });
        
        // Valeur
        doc.fontSize(8)
           .font('Helvetica-Bold')
           .fillColor(darkGray)
           .text(item.value.toString(), barX, barY - 15, { width: barWidth, align: 'center' });
      });
      
      return y + height + 30;
    };
    
    // En-tête du document avec logo et titre
    doc.rect(0, 0, 595, 80)
       .fillColor(primaryColor)
       .fill();
    
    doc.fontSize(24)
       .font('Helvetica-Bold')
       .fillColor('white')
       .text('RAPPORT DE FACTURATION', 40, 25);
    
    doc.fontSize(12)
       .font('Helvetica')
       .fillColor('white')
       .text('JobPartiel.fr', 450, 30);
    
    doc.fontSize(10)
       .font('Helvetica')
       .fillColor('white')
       .text(`Généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`, 450, 50);
    
    currentY = 100;
    
    // Informations sur la période analysée
    doc.rect(40, currentY, 515, 60)
       .fillColor(lightGray)
       .fill()
       .strokeColor('#d1d5db')
       .lineWidth(1)
       .stroke();
    
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .fillColor(darkGray)
       .text('Période analysée', 50, currentY + 10);
    
    doc.fontSize(11)
       .font('Helvetica')
       .fillColor(grayColor)
       .text(`Du ${startDate ? startDate.toLocaleDateString('fr-FR') : 'début'} au ${endDate ? endDate.toLocaleDateString('fr-FR') : 'aujourd\'hui'}`, 50, currentY + 30);
    
    doc.text(`Type de documents: ${type ? (type === 'devis' ? 'Devis' : type === 'facture' ? 'Factures' : 'Avoirs') : 'Tous types'}`, 50, currentY + 45);
    
    currentY += 80;
    
    // Section KPI principaux
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .fillColor(primaryColor)
       .text('Indicateurs clés de performance', 40, currentY);
    
    currentY += 35;
    
    // Grille de KPI (2x2)
    const kpiData = [
      { title: 'Total documents', value: stats.summary.totalDocuments.toString(), color: primaryColor },
      { title: 'Montant total', value: `${stats.summary.totalAmount.toFixed(2)} €`, color: greenColor },
      { title: 'Montant en attente', value: `${stats.summary.pendingAmount.toFixed(2)} €`, color: blueColor },
      { title: 'Montant encaissé', value: `${stats.summary.paidAmount.toFixed(2)} €`, color: greenColor }
    ];
    
    const kpiWidth = 240;
    const kpiHeight = 80;
    
    kpiData.forEach((kpi, index) => {
      const col = index % 2;
      const row = Math.floor(index / 2);
      const x = 40 + (col * (kpiWidth + 35));
      const y = currentY + (row * (kpiHeight + 20));
      
      // Cadre du KPI
      doc.rect(x, y, kpiWidth, kpiHeight)
         .fillColor('white')
         .fill()
         .strokeColor(kpi.color)
         .lineWidth(2)
         .stroke();
      
      // Titre
      doc.fontSize(11)
         .font('Helvetica')
         .fillColor(grayColor)
         .text(kpi.title, x + 15, y + 15);
      
      // Valeur
      doc.fontSize(20)
         .font('Helvetica-Bold')
         .fillColor(kpi.color)
         .text(kpi.value, x + 15, y + 35);
    });
    
    currentY += 180;
    
    // Nouvelle page si nécessaire
    if (currentY > 650) {
      drawFooter(pageNumber);
      doc.addPage();
      pageNumber++;
      currentY = 40;
    }
    
    // Section répartition par type
    doc.fontSize(16)
       .font('Helvetica-Bold')
       .fillColor(primaryColor)
       .text('Répartition par type de document', 40, currentY);
    
    currentY += 30;
    
    const typeData = Object.entries(stats.byType).map(([type, data]) => ({
      name: type === 'devis' ? 'Devis' : type === 'facture' ? 'Factures' : 'Avoirs',
      value: data.count,
      amount: data.amount,
      percentage: data.percentage
    }));
    
    // Graphique en barres pour les types
    currentY = drawBarChart(typeData, 40, currentY, 250, 150, '');
    
    // Tableau détaillé à côté
    const tableX = 320;
    let tableY = currentY - 150;
    
    doc.fontSize(12)
       .font('Helvetica-Bold')
       .fillColor(darkGray)
       .text('Détail par type', tableX, tableY);
    
    tableY += 25;
    
    // En-tête du tableau
    doc.rect(tableX, tableY, 235, 25)
       .fillColor(lightGray)
       .fill()
       .strokeColor('#d1d5db')
       .lineWidth(1)
       .stroke();
    
    doc.fontSize(10)
       .font('Helvetica-Bold')
       .fillColor(darkGray)
       .text('Type', tableX + 5, tableY + 8)
       .text('Nombre', tableX + 80, tableY + 8)
       .text('Montant', tableX + 130, tableY + 8)
       .text('%', tableX + 200, tableY + 8);
    
    tableY += 25;
    
    typeData.forEach((item, index) => {
      const rowColor = index % 2 === 0 ? 'white' : '#f9fafb';
      
      doc.rect(tableX, tableY, 235, 20)
         .fillColor(rowColor)
         .fill()
         .strokeColor('#e5e7eb')
         .lineWidth(0.5)
         .stroke();
      
      doc.fontSize(9)
         .font('Helvetica')
         .fillColor(darkGray)
         .text(item.name, tableX + 5, tableY + 6)
         .text(item.value.toString(), tableX + 80, tableY + 6)
         .text(`${item.amount.toFixed(2)} €`, tableX + 130, tableY + 6)
         .text(`${item.percentage}%`, tableX + 200, tableY + 6);
      
      tableY += 20;
    });
    
    currentY += 50;
    
    // Nouvelle page si nécessaire
    if (currentY > 600) {
      drawFooter(pageNumber);
      doc.addPage();
      pageNumber++;
      currentY = 40;
    }
    
    // Section répartition par statut
    doc.fontSize(16)
       .font('Helvetica-Bold')
       .fillColor(primaryColor)
       .text('Répartition par statut', 40, currentY);
    
    currentY += 30;
    
    const statusData = Object.entries(stats.byStatus).map(([status, data]) => ({
      name: status,
      value: data.count,
      amount: data.amount,
      percentage: data.percentage
    }));
    
    // Tableau des statuts
    if (statusData.length > 0) {
      // En-tête
      doc.rect(40, currentY, 515, 25)
         .fillColor(lightGray)
         .fill()
         .strokeColor('#d1d5db')
         .lineWidth(1)
         .stroke();
      
      doc.fontSize(11)
         .font('Helvetica-Bold')
         .fillColor(darkGray)
         .text('Statut', 50, currentY + 8)
         .text('Nombre', 200, currentY + 8)
         .text('Montant', 300, currentY + 8)
         .text('Pourcentage', 450, currentY + 8);
      
      currentY += 25;
      
      statusData.forEach((item, index) => {
        const rowColor = index % 2 === 0 ? 'white' : '#f9fafb';
        
        doc.rect(40, currentY, 515, 25)
           .fillColor(rowColor)
           .fill()
           .strokeColor('#e5e7eb')
           .lineWidth(0.5)
           .stroke();
        
        doc.fontSize(10)
           .font('Helvetica')
           .fillColor(darkGray)
           .text(item.name, 50, currentY + 8)
           .text(item.value.toString(), 200, currentY + 8)
           .text(`${item.amount.toFixed(2)} €`, 300, currentY + 8);
        
        // Barre de progression pour le pourcentage
        drawProgressBar(item.percentage, 450, currentY + 10, 80, 8, primaryColor);
        doc.fontSize(8)
           .text(`${item.percentage}%`, 540, currentY + 8);
        
        currentY += 25;
      });
    }
    
    currentY += 30;
    
    // Nouvelle page si nécessaire
    if (currentY > 600) {
      drawFooter(pageNumber);
      doc.addPage();
      pageNumber++;
      currentY = 40;
    }
    
    // Section principaux clients
    if (stats.byClient.length > 0) {
      doc.fontSize(16)
         .font('Helvetica-Bold')
         .fillColor(primaryColor)
         .text('Principaux clients', 40, currentY);
      
      currentY += 30;
      
      // En-tête du tableau clients
      doc.rect(40, currentY, 515, 25)
         .fillColor(lightGray)
         .fill()
         .strokeColor('#d1d5db')
         .lineWidth(1)
         .stroke();
      
      doc.fontSize(11)
         .font('Helvetica-Bold')
         .fillColor(darkGray)
         .text('Client', 50, currentY + 8)
         .text('Documents', 300, currentY + 8)
         .text('Montant', 400, currentY + 8)
         .text('Part', 500, currentY + 8);
      
      currentY += 25;
      
      stats.byClient.slice(0, 10).forEach((client, index) => {
        const rowColor = index % 2 === 0 ? 'white' : '#f9fafb';
        
        doc.rect(40, currentY, 515, 25)
           .fillColor(rowColor)
           .fill()
           .strokeColor('#e5e7eb')
           .lineWidth(0.5)
           .stroke();
        
        doc.fontSize(10)
           .font('Helvetica')
           .fillColor(darkGray)
           .text(client.clientName.substring(0, 35), 50, currentY + 8)
           .text(client.count.toString(), 300, currentY + 8)
           .text(`${client.amount.toFixed(2)} €`, 400, currentY + 8)
           .text(`${client.percentage}%`, 500, currentY + 8);
        
        currentY += 25;
      });
      
      currentY += 30;
    }
    
    // Section documents reçus
    if (stats.received.totalDocuments > 0) {
      // Nouvelle page si nécessaire
      if (currentY > 600) {
        drawFooter(pageNumber);
        doc.addPage();
        pageNumber++;
        currentY = 40;
      }
      
      doc.fontSize(16)
         .font('Helvetica-Bold')
         .fillColor(primaryColor)
         .text('Documents reçus (en tant que client)', 40, currentY);
      
      currentY += 30;
      
      // KPI documents reçus
      doc.rect(40, currentY, 515, 60)
         .fillColor('#ecfdf5')
         .fill()
         .strokeColor(greenColor)
         .lineWidth(1)
         .stroke();
      
      doc.fontSize(12)
         .font('Helvetica-Bold')
         .fillColor(greenColor)
         .text(`${stats.received.totalDocuments} documents reçus`, 50, currentY + 15);
      
      doc.fontSize(14)
         .font('Helvetica-Bold')
         .fillColor(greenColor)
         .text(`${stats.received.totalAmount.toFixed(2)} € au total`, 50, currentY + 35);
      
      currentY += 80;
      
      // Répartition des documents reçus par type
      const receivedTypeData = Object.entries(stats.received.byType).map(([type, data]) => ({
        name: type === 'devis' ? 'Devis' : type === 'facture' ? 'Factures' : 'Avoirs',
        value: data.count,
        amount: data.amount
      })).filter(item => item.value > 0);
      
      if (receivedTypeData.length > 0) {
        currentY = drawBarChart(receivedTypeData, 40, currentY, 250, 120, 'Répartition par type');
      }
    }
    
    // Pied de page final
    drawFooter(pageNumber);
    
    // Finaliser le document
    doc.end();
    
  } catch (error: any) {
    logger.error('Erreur lors de l\'export PDF des statistiques:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'export PDF des statistiques',
      error: error.message
    });
  }
}; 