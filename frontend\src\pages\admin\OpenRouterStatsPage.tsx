import React, { useState, useEffect } from 'react';
import {
  Box, Container, Typography, Paper, Grid, FormControl, InputLabel,
  MenuItem, Select, Button, Tabs, Tab,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  TablePagination, CircularProgress, Chip, Alert,
  TextField, useTheme, useMediaQuery, ToggleButtonGroup, ToggleButton,
  TableSortLabel, // Importer TableSortLabel
} from '@mui/material';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { fr } from 'date-fns/locale';
import { format, startOfMonth, endOfMonth, startOfDay, endOfDay, subMonths } from 'date-fns';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import { Bar<PERSON>hart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, CartesianGrid, Line<PERSON>hart, Line, PieChart, Pie, Cell } from 'recharts';
import { styled } from '@mui/material/styles';
import { BarChart2, Calendar, Filter, Database, Activity, RefreshCcw, DollarSign, Info } from 'lucide-react';
import { fetchCsrfToken } from '../../services/csrf';
import { getCommonHeaders } from '../../utils/headers';
import { getDailyRequestCount } from '../../services/openRouterApi';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  cost: '#00C853', // Vert pour les coûts
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
  }
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontSize: '0.9rem',
  minHeight: '48px',
  padding: '12px 16px',
  color: '#64748B',
  '&.Mui-selected': {
    color: COLORS.primary,
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${COLORS.borderColor}`,
  '& .MuiTabs-indicator': {
    backgroundColor: COLORS.primary,
    height: '3px',
    borderRadius: '3px 3px 0 0',
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
  '&.MuiButton-outlined': {
    borderColor: COLORS.primary,
    color: COLORS.primary,
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
      borderColor: COLORS.secondary,
    },
  },
}));

const ChipStyled = styled(Chip)(({ theme }) => ({
  borderRadius: '6px',
  fontWeight: 500,
  height: '24px',
  '&.MuiChip-colorPrimary': {
    backgroundColor: `${COLORS.primary}30`,
    color: COLORS.primary,
  },
  '&.MuiChip-colorSecondary': {
    backgroundColor: `${COLORS.secondary}30`,
    color: COLORS.secondary,
  },
  '&.MuiChip-colorDefault': {
    backgroundColor: `${COLORS.neutral}30`,
    color: COLORS.neutral,
  },
  '&.MuiChip-colorSuccess': {
    backgroundColor: `${COLORS.success}30`,
    color: COLORS.success,
  },
  '&.MuiChip-colorInfo': {
    backgroundColor: `${COLORS.info}30`,
    color: COLORS.info,
  },
}));

// Types pour les données d'utilisation
interface OpenRouterUsage {
  id: string;
  user_id: string | null;
  service_type:
    | 'moderation'
    | 'text_moderation'
    | 'image_moderation'
    | 'generation'
    | 'biography_generation'
    | 'service_description_generation'
    | 'mission_post_generation'
    | 'review_response_generation'
    | 'mission_offer_generation'
    | 'comment_generation'
    | 'custom_prompt_generation'
    | 'mission_assistant'
    | 'avatar_moderation'
    | 'profile_picture_moderation'
    | 'midjourney_prompt'
    | 'card_editor_prompt'
    | 'slogan_generation'
    | 'support_comment_generation'
    | 'support_user_assistance'
    | 'support_staff_assistance'
    | 'other';
  model: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  response_id: string | null;
  created_at: string;
  users: {
    email: string;
  } | null;
}

interface DailyStats {
  day?: string;
  month?: never;
  service_type: string;
  model: string;
  request_count: number;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

interface MonthlyStats {
  day?: never;
  month?: string;
  service_type: string;
  model: string;
  request_count: number;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

interface ChartDataPoint {
  date: string;
  service: string;
  model: string;
  'Nombre de requêtes': number;
  'Tokens totaux': number;
  'Modération Texte': number;
  'Modération Image': number;
  'Modération Avatar': number;
  'Modération Photo de Profil': number;
  'Génération Prompt Midjourney': number;
  'Génération Biographie': number;
  'Génération Description Service': number;
  'Génération Mission': number;
  'Génération Réponse Avis': number;
  'Génération Offre Mission': number;
  'Génération Commentaire': number;
  'Génération Prompt Personnalisé': number;
  'Génération Slogan': number;
  'Génération Commentaire Support': number;
  'Assistance Utilisateur Support': number;
  'Assistance Staff Support': number;
  'Assistant Mission': number;
}

interface GlobalStats {
  total_requests: number;
  total_tokens: number;
  total_prompt_tokens?: number;
  total_completion_tokens?: number;
  total_cost?: number;
  by_service_type: Array<{
    service_type: string;
    count: number;
    sum: number;
    prompt_tokens?: number;
    completion_tokens?: number;
    cost?: number;
  }>;
  daily_requests?: number;
  weekly_requests?: number;
  monthly_requests?: number;
  yearly_requests?: number;
  daily_tokens?: number;
  weekly_tokens?: number;
  monthly_tokens?: number;
  yearly_tokens?: number;
  daily_prompt_tokens?: number;
  weekly_prompt_tokens?: number;
  monthly_prompt_tokens?: number;
  yearly_prompt_tokens?: number;
  daily_completion_tokens?: number;
  weekly_completion_tokens?: number;
  monthly_completion_tokens?: number;
  yearly_completion_tokens?: number;
  daily_cost?: number;
  weekly_cost?: number;
  monthly_cost?: number;
  yearly_cost?: number;
  by_service_type_daily?: Array<{
    service_type: string;
    count: number;
    sum: number;
    prompt_tokens?: number;
    completion_tokens?: number;
    cost?: number;
  }>;
  by_service_type_weekly?: Array<{
    service_type: string;
    count: number;
    sum: number;
    prompt_tokens?: number;
    completion_tokens?: number;
    cost?: number;
  }>;
  by_service_type_monthly?: Array<{
    service_type: string;
    count: number;
    sum: number;
    prompt_tokens?: number;
    completion_tokens?: number;
    cost?: number;
  }>;
  by_service_type_yearly?: Array<{
    service_type: string;
    count: number;
    sum: number;
    prompt_tokens?: number;
    completion_tokens?: number;
    cost?: number;
  }>;
}

interface PieChartData {
  name: string;
  value: number;
  color: string;
}

interface PaginationOptions {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
}

// Composant CustomInput pour react-datepicker avec MUI TextField
const CustomDateInput = React.forwardRef<HTMLInputElement, { value?: string; onClick?: () => void; label?: string }>(({ value, onClick, label }, ref) => (
  <TextField
    fullWidth
    size="small"
    label={label}
    value={value}
    onClick={onClick}
    ref={ref}
    InputProps={{
      readOnly: true,
      sx: {
        borderRadius: '8px',
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: 'rgba(0, 0, 0, 0.1)',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: COLORS.primary,
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: COLORS.primary,
        },
      }
    }}
    sx={{
      '& .react-datepicker-popper': {
        zIndex: 9999
      },
      '& .react-datepicker-wrapper': {
        display: 'block'
      },
      '& .react-datepicker': {
        zIndex: 9999,
        position: 'relative'
      }
    }}
  />
));

// Ajouter un style global pour le DatePicker
const GlobalDatePickerStyle = styled('style')({
  '.react-datepicker-popper': {
    zIndex: 9999,
  },
  '.react-datepicker': {
    zIndex: 9999,
    position: 'relative',
  },
  '.react-datepicker-wrapper': {
    display: 'block',
  }
});

// Ajouter un style spécifique pour le conteneur des filtres
const FilterContainer = styled(StyledPaper)(({ theme }) => ({
  position: 'relative',
  zIndex: 1100,
  marginBottom: theme.spacing(3),
  '& .react-datepicker-popper': {
    zIndex: 1100,
    position: 'absolute',
  },
  '& .react-datepicker-wrapper': {
    position: 'relative',
  },
  '& .react-datepicker': {
    position: 'relative',
    zIndex: 1100,
  },
  '& .react-datepicker__portal': {
    position: 'fixed',
    width: '100vw',
    height: '100vh',
    top: 0,
    left: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1100,
  }
}));

// Fonction pour obtenir le label du type de service
const getServiceTypeLabel = (serviceType: string): string => {
  switch (serviceType) {
    // Types de génération
    case 'generation':
      return 'Génération';
    case 'biography_generation':
      return 'Génération Biographie';
    case 'service_description_generation':
      return 'Génération Description Service';
    case 'mission_post_generation':
      return 'Génération Mission';
    case 'review_response_generation':
      return 'Génération Réponse Avis';
    case 'mission_offer_generation':
      return 'Génération Offre Mission';
    case 'comment_generation':
      return 'Génération Commentaire';
    case 'custom_prompt_generation':
      return 'Génération Prompt Personnalisé';
    case 'slogan_generation':
      return 'Génération Slogan';
    case 'support_comment_generation':
      return 'Génération Commentaire Support';
    case 'support_user_assistance':
      return 'Assistance Utilisateur Support';
    case 'support_staff_assistance':
      return 'Assistance Staff Support';
    // Types de modération
    case 'moderation':
      return 'Modération';
    case 'text_moderation':
      return 'Modération Texte';
    case 'image_moderation':
      return 'Modération Image';
    // Autres types
    case 'mission_assistant':
      return 'Assistant Mission';
    case 'avatar_moderation':
      return 'Modération Avatar';
    case 'profile_picture_moderation':
      return 'Modération Photo de Profil';
    case 'midjourney_prompt':
      return 'Génération Prompt Midjourney';
    case 'card_editor_prompt':
      return 'Génération Prompt Card Editor';
    default:
      return serviceType;
  }
};

// Fonction pour obtenir la couleur en fonction du type de service
const getServiceTypeColorForChart = (serviceType: string): string => {
  switch (serviceType) {
    case 'text_moderation':
      return '#7986cb'; // Bleu-violet
    case 'image_moderation':
      return '#4fc3f7'; // Bleu clair
    case 'biography_generation':
      return '#ff8a65'; // Orange-rouge
    case 'service_description_generation':
      return '#ff7043'; // Orange vif
    case 'mission_post_generation':
      return '#ffb74d'; // Orange clair
    case 'review_response_generation':
      return '#ffd54f'; // Jaune
    case 'mission_offer_generation':
      return '#e57373'; // Rouge clair
    case 'comment_generation':
      return '#ba68c8'; // Violet clair
    case 'custom_prompt_generation':
      return '#ce93d8'; // Violet plus clair
    case 'slogan_generation':
      return '#f48fb1'; // Rose
    case 'support_comment_generation':
      return '#26c6da'; // Cyan
    case 'support_user_assistance':
      return '#42a5f5'; // Bleu
    case 'support_staff_assistance':
      return '#5c6bc0'; // Indigo
    case 'mission_assistant':
      return '#81c784'; // Vert clair
    case 'moderation':
      return '#9575cd'; // Violet
    case 'generation':
      return '#ff9800'; // Orange
    case 'avatar_moderation':
      return '#ff9800'; // Orange
    case 'profile_picture_moderation':
      return '#ff9800'; // Orange
    case 'midjourney_prompt':
      return '#ff9800'; // Orange
    case 'card_editor_prompt':
      return '#ff9800'; // Orange
    default:
      return '#90a4ae'; // Gris-bleu
  }
};

// Composant principal
const OpenRouterStatsPage: React.FC = () => {
  // État pour les onglets
  const [tabValue, setTabValue] = useState(0);

  // États pour les filtres
  const [startDate, setStartDate] = useState<Date | null>(subMonths(new Date(), 1));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [serviceType, setServiceType] = useState<string>('');
  const [model, setModel] = useState<string>('');
  const [availableModels, setAvailableModels] = useState<string[]>([]);

  // États pour les données
  const [usageData, setUsageData] = useState<OpenRouterUsage[]>([]);
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([]);
  const [monthlyStats, setMonthlyStats] = useState<MonthlyStats[]>([]);
  const [globalStats, setGlobalStats] = useState<GlobalStats | null>(null);
  const [modelsInfo, setModelsInfo] = useState<{
    free_model: string;
    paid_model: string;
    vision_free_model: string;
    vision_paid_model: string;
    daily_calls_limit: number;
  } | null>(null);
  const [pagination, setPagination] = useState<PaginationOptions>({
    page: 0, // Page 0-indexed pour MUI
    limit: 20,
    totalItems: 0,
    totalPages: 0
  });

  // États pour le chargement
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingModels, setIsLoadingModels] = useState<boolean>(false);
  const [isClearingCache, setIsClearingCache] = useState<boolean>(false);
  const [periodView, setPeriodView] = useState<'all' | 'day' | 'week' | 'month' | 'year'>('day');

  // Ajout d'un état pour le compteur du jour
  const [dailyRequestCount, setDailyRequestCount] = useState<number>(0);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // États pour le tri du tableau
  const [orderBy, setOrderBy] = useState<keyof TableRowData>('totalTokens'); // Colonne par défaut pour le tri
  const [order, setOrder] = useState<'asc' | 'desc'>('desc'); // Ordre par défaut (descendant)

  // Interface pour les données du tableau
  interface TableRowData {
    key: string;
    label: string;
    color: string;
    requests: number;
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    cost: number | null;
    // Ajouter la moyenne de tokens pour pouvoir trier dessus
    averageTokens: number;
  }

  // Fonction de comparaison pour le tri
  const getComparator = (
    order: 'asc' | 'desc',
    orderBy: keyof TableRowData,
  ): ((a: TableRowData, b: TableRowData) => number) => {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  };

  // Helper pour le tri descendant
  const descendingComparator = (a: TableRowData, b: TableRowData, orderBy: keyof TableRowData): number => {
    if (a[orderBy] == null) return 1; // Gérer les valeurs nulles ou indéfinies
    if (b[orderBy] == null) return -1;

    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  };

  // Helper pour un tri stable (important pour maintenir l'ordre des éléments égaux)
  const stableSort = (array: TableRowData[], comparator: (a: TableRowData, b: TableRowData) => number) => {
    const stabilizedThis = array.map((el, index) => [el, index] as [TableRowData, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) return order;
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  };

  // Gestion du clic sur l'en-tête pour le tri
  const handleRequestSort = (property: keyof TableRowData) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Charger les modèles disponibles au chargement de la page
  useEffect(() => {
    fetchAvailableModels();
    fetchGlobalStats();
    fetchModelsInfo();
  }, []);

  // Charger le compteur du jour au chargement et lors du reset des filtres
  useEffect(() => {
    const fetchDailyCount = async () => {
      if (!startDate && !endDate && !serviceType && !model) {
        const count = await getDailyRequestCount();
        setDailyRequestCount(count);
        // Synchroniser la pagination
        setPagination((prev) => ({
          ...prev,
          totalItems: count,
          totalPages: Math.ceil(count / prev.limit)
        }));
      }
    };
    fetchDailyCount();
  }, [startDate, endDate, serviceType, model, pagination.limit]);

  // Fonction pour récupérer les informations sur les modèles
  const fetchModelsInfo = async () => {
    try {
      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/models`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setModelsInfo(response.data.data);
      } else {
        notify('Erreur lors de la récupération des informations sur les modèles', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des informations sur les modèles:', error);
      notify('Erreur lors de la récupération des informations sur les modèles', 'error');
    }
  };

  // Charger les données lors du changement d'onglet, des filtres ou de la période
  useEffect(() => {
    if (tabValue === 0) {
      fetchUsageData();
    } else if (tabValue === 1) {
      fetchDailyStats();
    } else if (tabValue === 2) {
      fetchMonthlyStats();
    }
  }, [tabValue, pagination.page, pagination.limit]);

  // Recharger les statistiques globales lorsque la période change
  useEffect(() => {
    // Pas besoin de recharger les données, juste de mettre à jour les graphiques
    // Les données sont déjà chargées et filtrées par période dans les fonctions de formatage
  }, [periodView]);

  // Fonction pour récupérer les modèles disponibles
  const fetchAvailableModels = async () => {
    try {
      setIsLoadingModels(true);
      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/models`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setAvailableModels(response.data.data);
      } else {
        notify('Erreur lors de la récupération des modèles disponibles', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des modèles disponibles:', error);
      notify('Erreur lors de la récupération des modèles disponibles', 'error');
    } finally {
      setIsLoadingModels(false);
    }
  };

  // Fonction pour récupérer les statistiques globales
  const fetchGlobalStats = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/global`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        // Récupérer les données globales
        const basicStats = response.data.data;

        // Stocker uniquement la valeur brute du backend
        setGlobalStats(basicStats);
      } else {
        notify('Erreur lors de la récupération des statistiques globales', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des statistiques globales:', error);
      notify('Erreur lors de la récupération des statistiques globales', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour récupérer l'historique d'utilisation
  const fetchUsageData = async () => {
    try {
      setIsLoading(true);

      let queryParams = new URLSearchParams({
        page: (pagination.page + 1).toString(), // API is 1-indexed
        limit: pagination.limit.toString()
      });

      if (startDate) {
        queryParams.append('startDate', format(startOfDay(startDate), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"));
      }
      if (endDate) {
        queryParams.append('endDate', format(endOfDay(endDate), "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"));
      }
      if (serviceType) {
        queryParams.append('serviceType', serviceType);
      }
      if (model) {
        queryParams.append('model', model);
      }

      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/usage?${queryParams.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setUsageData(response.data.data);

        // Utiliser la valeur pagination_count des statistiques globales si disponible
        // pour assurer la cohérence avec le compteur en haut de la page
        const globalStats = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/global`, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          withCredentials: true
        });

        let totalItems = response.data.pagination.totalItems;

        // Si nous sommes sur la première page sans filtres et que nous avons une valeur pagination_count
        if (pagination.page === 0 && !startDate && !endDate && !serviceType && !model &&
            globalStats.data.success &&
            globalStats.data.data.pagination_count !== undefined) {
          totalItems = globalStats.data.data.pagination_count;
        }

        // SOLUTION FINALE: Utiliser la même source de données pour les deux compteurs
        const isFilteringToday =
          (startDate && format(startDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')) ||
          (!startDate && !endDate);

        if (globalStats.data.success && isFilteringToday && !serviceType && !model) {
          // Récupérer les statistiques globales à jour pour avoir le compteur le plus récent
          try {
            // Faire une requête directe à l'API pour obtenir le nombre exact d'appels quotidiens
            const today = new Date().toISOString().split('T')[0];
            const directCountResponse = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/count?date=${today}`, {
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              withCredentials: true
            });

            if (directCountResponse.data.success && directCountResponse.data.count !== undefined) {
              totalItems = directCountResponse.data.count;
            } else if (globalStats.data.data.daily_requests !== undefined) {
              totalItems = globalStats.data.data.daily_requests;
            }
          } catch (error) {
            console.error('Erreur lors de la récupération du compteur direct:', error);
            // En cas d'erreur, utiliser le compteur des statistiques globales
            if (globalStats.data.data.daily_requests !== undefined) {
              totalItems = globalStats.data.data.daily_requests;
            }
          }
        }

        setPagination({
          ...pagination,
          totalItems: totalItems,
          totalPages: Math.ceil(totalItems / pagination.limit)
        });
      } else {
        notify('Erreur lors de la récupération des données d\'utilisation', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des données d\'utilisation:', error);
      notify('Erreur lors de la récupération des données d\'utilisation', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour récupérer les statistiques quotidiennes
  const fetchDailyStats = async () => {
    try {
      setIsLoading(true);

      let queryParams = new URLSearchParams();

      if (startDate) {
        queryParams.append('startDate', format(startOfDay(startDate), "yyyy-MM-dd"));
      }
      if (endDate) {
        queryParams.append('endDate', format(endOfDay(endDate), "yyyy-MM-dd"));
      }
      if (serviceType) {
        queryParams.append('serviceType', serviceType);
      }
      if (model) {
        queryParams.append('model', model);
      }

      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/daily?${queryParams.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setDailyStats(response.data.data);
      } else {
        notify('Erreur lors de la récupération des statistiques quotidiennes', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des statistiques quotidiennes:', error);
      notify('Erreur lors de la récupération des statistiques quotidiennes', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour récupérer les statistiques mensuelles
  const fetchMonthlyStats = async () => {
    try {
      setIsLoading(true);

      let queryParams = new URLSearchParams();

      if (startDate) {
        queryParams.append('startDate', format(startOfMonth(startDate), "yyyy-MM-dd"));
      }
      if (endDate) {
        queryParams.append('endDate', format(endOfMonth(endDate), "yyyy-MM-dd"));
      }
      if (serviceType) {
        queryParams.append('serviceType', serviceType);
      }
      if (model) {
        queryParams.append('model', model);
      }

      const response = await axios.get(`${API_CONFIG.baseURL}/api/openrouter/stats/monthly?${queryParams.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        withCredentials: true
      });

      if (response.data.success) {
        setMonthlyStats(response.data.data);
      } else {
        notify('Erreur lors de la récupération des statistiques mensuelles', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la récupération des statistiques mensuelles:', error);
      notify('Erreur lors de la récupération des statistiques mensuelles', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour appliquer les filtres
  const applyFilters = () => {
    if (tabValue === 0) {
      // Réinitialiser la pagination lors de l'application des filtres
      setPagination({
        ...pagination,
        page: 0
      });
      fetchUsageData();
    } else if (tabValue === 1) {
      fetchDailyStats();
    } else if (tabValue === 2) {
      fetchMonthlyStats();
    }
  };

  // Fonction pour réinitialiser les filtres
  const resetFilters = () => {
    setStartDate(subMonths(new Date(), 1));
    setEndDate(new Date());
    setServiceType('');
    setModel('');

    setTimeout(() => {
      applyFilters();
    }, 0);
  };

  // Fonction pour vider le cache et rafraîchir les données
  const clearCacheAndRefresh = async () => {
    try {
      setIsClearingCache(true);

      // Récupérer le token CSRF
      const csrfToken = await fetchCsrfToken();

      // Récupérer les headers communs
      const headers = await getCommonHeaders(false); // Forcer la récupération de nouveaux headers

      // Appel à l'API pour vider le cache
      const response = await axios.post(`${API_CONFIG.baseURL}/api/openrouter/stats/clear-daily-calls-cache`, {}, {
        headers: {
          ...headers,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        withCredentials: true
      });

      if (response.data.success) {
        notify('Cache vidé avec succès, actualisation des données...', 'success');

        // Rafraîchir les données globales
        await fetchGlobalStats();

        // Rafraîchir les données selon l'onglet actif
        if (tabValue === 0) {
          await fetchUsageData();
        } else if (tabValue === 1) {
          await fetchDailyStats();
        } else if (tabValue === 2) {
          await fetchMonthlyStats();
        }

        notify('Données actualisées avec succès', 'success');
      } else {
        notify('Erreur lors de la suppression du cache', 'error');
      }
    } catch (error: any) {
      console.error('Erreur lors de la suppression du cache:', error);

      // Afficher un message d'erreur plus détaillé
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 403) {
          notify('Vous n\'avez pas les droits nécessaires pour effectuer cette action', 'error');
        } else if (error.response?.status === 401) {
          notify('Vous devez être connecté pour effectuer cette action', 'error');
        } else {
          notify(error.response?.data?.message || 'Erreur lors de la suppression du cache', 'error');
        }
      } else {
        notify('Erreur lors de la suppression du cache', 'error');
      }
    } finally {
      setIsClearingCache(false);
    }
  };

  // Fonction pour formater les données pour les graphiques
  const formatChartData = (data: Array<DailyStats | MonthlyStats>): ChartDataPoint[] => {
    // Regrouper les données par date
    const groupedByDate = data.reduce((acc, item) => {
      const date = (item as DailyStats).day || (item as MonthlyStats).month || '';
      const isMonthly = !!(item as MonthlyStats).month;
      const formattedDate = format(new Date(date), isMonthly ? 'MM/yyyy' : 'dd/MM/yyyy');

      if (!acc[formattedDate]) {
        acc[formattedDate] = {
          date: formattedDate,
          'Nombre de requêtes': 0,
          'Tokens totaux': 0,
          'Modération Texte': 0,
          'Modération Image': 0,
          'Modération Avatar': 0,
          'Modération Photo de Profil': 0,
          'Génération Biographie': 0,
          'Génération Description Service': 0,
          'Génération Mission': 0,
          'Génération Réponse Avis': 0,
          'Génération Offre Mission': 0,
          'Génération Commentaire': 0,
          'Génération Prompt Personnalisé': 0,
          'Génération Slogan': 0,
          'Génération Commentaire Support': 0,
          'Assistance Utilisateur Support': 0,
          'Assistance Staff Support': 0,
          'Assistant Mission': 0,
          'Génération Prompt Midjourney': 0,
          service: '',
          model: ''
        };
      }

      // Ajouter les requêtes et tokens au total
      acc[formattedDate]['Nombre de requêtes'] += item.request_count;
      acc[formattedDate]['Tokens totaux'] += item.total_tokens;

      // Ajouter les requêtes au type de service spécifique
      switch (item.service_type) {
        case 'text_moderation':
          acc[formattedDate]['Modération Texte'] += item.request_count;
          break;
        case 'image_moderation':
          acc[formattedDate]['Modération Image'] += item.request_count;
          break;
        case 'biography_generation':
          acc[formattedDate]['Génération Biographie'] += item.request_count;
          break;
        case 'service_description_generation':
          acc[formattedDate]['Génération Description Service'] += item.request_count;
          break;
        case 'mission_post_generation':
          acc[formattedDate]['Génération Mission'] += item.request_count;
          break;
        case 'review_response_generation':
          acc[formattedDate]['Génération Réponse Avis'] += item.request_count;
          break;
        case 'mission_offer_generation':
          acc[formattedDate]['Génération Offre Mission'] += item.request_count;
          break;
        case 'comment_generation':
          acc[formattedDate]['Génération Commentaire'] += item.request_count;
          break;
        case 'custom_prompt_generation':
          acc[formattedDate]['Génération Prompt Personnalisé'] += item.request_count;
          break;
        case 'slogan_generation':
          acc[formattedDate]['Génération Slogan'] += item.request_count;
          break;
        case 'support_comment_generation':
          acc[formattedDate]['Génération Commentaire Support'] += item.request_count;
          break;
        case 'support_user_assistance':
          acc[formattedDate]['Assistance Utilisateur Support'] += item.request_count;
          break;
        case 'support_staff_assistance':
          acc[formattedDate]['Assistance Staff Support'] += item.request_count;
          break;
        case 'mission_assistant':
          acc[formattedDate]['Assistant Mission'] += item.request_count;
          break;
        case 'avatar_moderation':
          acc[formattedDate]['Modération Avatar'] += item.request_count;
          break;
        case 'profile_picture_moderation':
          acc[formattedDate]['Modération Photo de Profil'] += item.request_count;
          break;
      }

      return acc;
    }, {} as Record<string, ChartDataPoint>);

    // Convertir l'objet en tableau
    return Object.values(groupedByDate);
  };

  // Fonction pour formater les données pour le graphique en camembert des tokens
  const formatPieChartData = (): PieChartData[] => {
    if (!globalStats) return [];

    // Sélectionner le bon champ selon la période
    let byServiceType: Array<{
      service_type: string;
      count: number;
      sum: number;
      prompt_tokens?: number;
      completion_tokens?: number;
      cost?: number;
    }> = [];
    if (periodView === 'day') {
      byServiceType = globalStats.by_service_type_daily || [];
    } else if (periodView === 'week') {
      byServiceType = globalStats.by_service_type_weekly || [];
    } else if (periodView === 'month') {
      byServiceType = globalStats.by_service_type_monthly || [];
    } else if (periodView === 'year') {
      byServiceType = globalStats.by_service_type_yearly || [];
    } else {
      byServiceType = globalStats.by_service_type || [];
    }

    return byServiceType.map(stat => ({
      name: getServiceTypeLabel(stat.service_type),
      value: stat.sum,
      color: getServiceTypeColorForChart(stat.service_type)
    }));
  };

  // Fonction pour formater les données pour le graphique en camembert des requêtes
  const formatRequestsPieChartData = (): PieChartData[] => {
    if (!globalStats) return [];

    // Sélectionner le bon champ selon la période
    let byServiceType: Array<{
      service_type: string;
      count: number;
      sum: number;
      prompt_tokens?: number;
      completion_tokens?: number;
      cost?: number;
    }> = [];
    if (periodView === 'day') {
      byServiceType = globalStats.by_service_type_daily || [];
    } else if (periodView === 'week') {
      byServiceType = globalStats.by_service_type_weekly || [];
    } else if (periodView === 'month') {
      byServiceType = globalStats.by_service_type_monthly || [];
    } else if (periodView === 'year') {
      byServiceType = globalStats.by_service_type_yearly || [];
    } else {
      byServiceType = globalStats.by_service_type || [];
    }

    return byServiceType.map(stat => ({
      name: getServiceTypeLabel(stat.service_type),
      value: stat.count,
      color: getServiceTypeColorForChart(stat.service_type)
    }));
  };

  // Gérer le changement d'onglet
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Gérer le changement de page
  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPagination({
      ...pagination,
      page: newPage
    });
  };

  // Gérer le changement de nombre d'éléments par page
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPagination({
      ...pagination,
      page: 0,
      limit: parseInt(event.target.value, 10)
    });
  };

  const getServiceTypeColor = (serviceType: string): "primary" | "secondary" | "success" | "info" | "warning" | "default" => {
    switch (serviceType) {
      // Types de génération
      case 'generation':
        return 'primary';
      case 'biography_generation':
        return 'primary';
      case 'service_description_generation':
        return 'primary';
      case 'mission_post_generation':
        return 'primary';
      case 'review_response_generation':
        return 'primary';
      case 'mission_offer_generation':
        return 'primary';
      case 'avatar_moderation':
        return 'primary';
      case 'profile_picture_moderation':
        return 'primary';
      case 'midjourney_prompt':
        return 'primary';
      case 'card_editor_prompt':
        return 'primary';
      case 'slogan_generation':
        return 'primary';
      case 'support_comment_generation':
        return 'info';
      case 'support_user_assistance':
        return 'info';
      case 'support_staff_assistance':
        return 'info';

      // Types de modération
      case 'moderation':
        return 'secondary';
      case 'text_moderation':
        return 'secondary';
      case 'image_moderation':
        return 'info';

      // Autres types
      case 'mission_assistant':
        return 'success';
      default:
        return 'default';
    }
  };

  // Rendu des statistiques globales
  const renderGlobalStats = () => {
    if (!globalStats) return null;

    // Fonction pour obtenir la valeur de requêtes selon la période
    const getRequestsByPeriod = () => {
      switch (periodView) {
        case 'day':
          return globalStats?.daily_requests || 0;
        case 'week':
          return globalStats?.weekly_requests || 0;
        case 'month':
          return globalStats?.monthly_requests || 0;
        case 'year':
          return globalStats?.yearly_requests || 0;
        case 'all':
        default:
          return globalStats?.total_requests || 0;
      }
    };

    // Fonction pour obtenir la valeur de tokens selon la période
    const getTokensByPeriod = () => {
      switch (periodView) {
        case 'day':
          return globalStats?.daily_tokens || 0;
        case 'week':
          return globalStats?.weekly_tokens || 0;
        case 'month':
          return globalStats?.monthly_tokens || 0;
        case 'year':
          return globalStats?.yearly_tokens || 0;
        case 'all':
        default:
          return globalStats?.total_tokens || 0;
      }
    };

    // Fonction pour obtenir la valeur de tokens d'entrée (prompt) selon la période
    const getPromptTokensByPeriod = () => {
      switch (periodView) {
        case 'day':
          return globalStats?.daily_prompt_tokens || 0;
        case 'week':
          return globalStats?.weekly_prompt_tokens || 0;
        case 'month':
          return globalStats?.monthly_prompt_tokens || 0;
        case 'year':
          return globalStats?.yearly_prompt_tokens || 0;
        case 'all':
        default:
          return globalStats?.total_prompt_tokens || 0;
      }
    };

    // Fonction pour obtenir la valeur de tokens de sortie (completion) selon la période
    const getCompletionTokensByPeriod = () => {
      switch (periodView) {
        case 'day':
          return globalStats?.daily_completion_tokens || 0;
        case 'week':
          return globalStats?.weekly_completion_tokens || 0;
        case 'month':
          return globalStats?.monthly_completion_tokens || 0;
        case 'year':
          return globalStats?.yearly_completion_tokens || 0;
        case 'all':
        default:
          return globalStats?.total_completion_tokens || 0;
      }
    };

    // Fonction pour obtenir la valeur de coût selon la période
    const getCostByPeriod = () => {
      switch (periodView) {
        case 'day':
          return globalStats?.daily_cost;
        case 'week':
          return globalStats?.weekly_cost;
        case 'month':
          return globalStats?.monthly_cost;
        case 'year':
          return globalStats?.yearly_cost;
        case 'all':
        default:
          return globalStats?.total_cost;
      }
    };

    // Fonction pour obtenir le label selon la période
    const getPeriodLabel = () => {
      switch (periodView) {
        case 'day':
          return "Aujourd'hui";
        case 'week':
          return "Cette semaine";
        case 'month':
          return "Ce mois-ci";
        case 'year':
          return "Cette année";
        case 'all':
        default:
          return "Depuis le début";
      }
    };

    // Fonction pour créer le groupe de boutons de période
    const renderPeriodToggleButtons = () => (
      <ToggleButtonGroup
        value={periodView}
        exclusive
        onChange={(_, newPeriod) => newPeriod && setPeriodView(newPeriod)}
        size="small"
        sx={{
          '& .MuiToggleButtonGroup-grouped': {
            margin: 0.1,
            border: 0,
            fontSize: '0.7rem',
            textTransform: 'none',
            '&.Mui-selected': {
              backgroundColor: `${COLORS.primary}20`,
              color: COLORS.primary,
              fontWeight: 'bold'
            },
          },
        }}
      >
        <ToggleButton value="day" aria-label="jour">
          Jour
        </ToggleButton>
        <ToggleButton value="week" aria-label="semaine">
          Semaine
        </ToggleButton>
        <ToggleButton value="month" aria-label="mois">
          Mois
        </ToggleButton>
        <ToggleButton value="year" aria-label="année">
          Année
        </ToggleButton>
        <ToggleButton value="all" aria-label="total">
          Total
        </ToggleButton>
      </ToggleButtonGroup>
    );

    // Données pour les graphiques en camembert
    const pieDataTokens = formatPieChartData();
    const pieDataRequests = formatRequestsPieChartData();

    // Ajout : sélection du bon tableau pour la répartition par service
    let byServiceType = [];
    if (periodView === 'day') {
      byServiceType = globalStats.by_service_type_daily || [];
    } else if (periodView === 'week') {
      byServiceType = globalStats.by_service_type_weekly || [];
    } else if (periodView === 'month') {
      byServiceType = globalStats.by_service_type_monthly || [];
    } else if (periodView === 'year') {
      byServiceType = globalStats.by_service_type_yearly || [];
    } else {
      byServiceType = globalStats.by_service_type || [];
    }

    return (
      <>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard>
              <Box sx={{ position: 'relative', mb: 2 }}>
                <SectionTitle>Total des requêtes</SectionTitle>
                <IconBox color={COLORS.primary}>
                  <Database size={24} />
                </IconBox>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: COLORS.primary, mb: 1 }}>
                {getRequestsByPeriod().toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Nombre d'appels à l'API ({getPeriodLabel()})
                  </Typography>
                </Box>
                {renderPeriodToggleButtons()}
              </Box>
            </MetricCard>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard>
              <Box sx={{ position: 'relative', mb: 2 }}>
                <SectionTitle>Total des tokens</SectionTitle>
                <IconBox color={COLORS.secondary}>
                  <Activity size={24} />
                </IconBox>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: COLORS.secondary, mb: 1 }}>
                {getTokensByPeriod().toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Tokens consommés ({getPeriodLabel()})
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  pl: 1,
                  py: 0.5,
                  borderRadius: 1,
                  bgcolor: `${COLORS.info}15`
                }}>
                  <Typography variant="body2" color="text.secondary">
                    • Entrée (prompt):
                  </Typography>
                  <Typography variant="body2" fontWeight="bold" color={COLORS.info}>
                    {getPromptTokensByPeriod().toLocaleString()}
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  pl: 1,
                  py: 0.5,
                  borderRadius: 1,
                  bgcolor: `${COLORS.success}15`
                }}>
                  <Typography variant="body2" color="text.secondary">
                    • Sortie (completion):
                  </Typography>
                  <Typography variant="body2" fontWeight="bold" color={COLORS.success}>
                    {getCompletionTokensByPeriod().toLocaleString()}
                  </Typography>
                </Box>
                {renderPeriodToggleButtons()}
              </Box>
            </MetricCard>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard>
              <Box sx={{ position: 'relative', mb: 2 }}>
                <SectionTitle>Coût estimé</SectionTitle>
                <IconBox color={COLORS.cost}>
                  <DollarSign size={24} />
                </IconBox>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 700, color: COLORS.cost, mb: 1 }}>
                {getCostByPeriod()
                  ? `$${Number(getCostByPeriod()).toFixed(5)}`
                  : 'N/A'}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Coût estimé des requêtes ({getPeriodLabel()})
                </Typography>
                {renderPeriodToggleButtons()}
              </Box>
            </MetricCard>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard>
              <Box sx={{ position: 'relative', mb: 2 }}>
                <SectionTitle>Répartition par service</SectionTitle>
                <IconBox color={COLORS.primary}>
                  <BarChart2 size={24} />
                </IconBox>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {byServiceType.map((stat) => (
                  <Box key={stat.service_type} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <ChipStyled
                      size="small"
                      label={getServiceTypeLabel(stat.service_type)}
                      color={getServiceTypeColor(stat.service_type)}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {stat.count} req.
                    </Typography>
                  </Box>
                ))}
              </Box>
              {/* Déplacement du filtre de période en bas */}
              <Box sx={{ mt: 2 }}>{renderPeriodToggleButtons()}</Box>
            </MetricCard>
          </Grid>
        </Grid>

        {/* Graphiques en camembert et informations de tokens par type */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* Graphique en camembert pour les requêtes */}
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <SectionTitle>Répartition des requêtes par type ({getPeriodLabel()})</SectionTitle>
              </Box>
              <Box sx={{ width: '100%', height: isMobile ? 290 : 380 }}>
                <Box sx={{ mb: 4 }} />
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieDataRequests}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={isMobile ? 70 : 100}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={isMobile ? undefined : ({ name, percent }) => {
                        const value = (percent * 100).toFixed(0);
                        if (Number(value) < 5) return null; // Ne pas afficher les petits pourcentages
                        return `${name}: ${value}%`;
                      }}
                    >
                      {pieDataRequests.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number) => [
                        `${value} requêtes (${((value / getRequestsByPeriod()) * 100).toFixed(1)}%)`,
                        "Requêtes"
                      ]}
                      contentStyle={{
                        backgroundColor: '#FFF',
                        border: `1px solid ${COLORS.borderColor}`,
                        borderRadius: '8px',
                        fontSize: '0.8rem',
                        padding: '10px',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                      }}
                    />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      wrapperStyle={{
                        fontSize: '0.85rem',
                        textAlign: 'center',
                        width: '100%',
                        paddingTop: 32
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
              {/* Déplacement du filtre de période en bas de la card */}
              <Box sx={{ mt: 2 }}>{renderPeriodToggleButtons()}</Box>
            </StyledPaper>
          </Grid>

          {/* Graphique en camembert pour les tokens */}
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <SectionTitle>Répartition des tokens par type ({getPeriodLabel()})</SectionTitle>
              </Box>
              <Box sx={{ width: '100%', height: isMobile ? 290 : 380 }}>
                <Box sx={{ mb: 4 }} />
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieDataTokens}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={isMobile ? 70 : 100}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={isMobile ? undefined : ({ name, percent }) => {
                        const value = (percent * 100).toFixed(0);
                        if (Number(value) < 5) return null; // Ne pas afficher les petits pourcentages
                        return `${name}: ${value}%`;
                      }}
                    >
                      {pieDataTokens.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number) => [
                        `${value.toLocaleString()} tokens (${((value / getTokensByPeriod()) * 100).toFixed(1)}%)`,
                        "Tokens"
                      ]}
                      contentStyle={{
                        backgroundColor: '#FFF',
                        border: `1px solid ${COLORS.borderColor}`,
                        borderRadius: '8px',
                        fontSize: '0.8rem',
                        padding: '10px',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                      }}
                    />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      wrapperStyle={{
                        fontSize: '0.85rem',
                        textAlign: 'center',
                        width: '100%',
                        paddingTop: 32
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
              {/* Déplacement du filtre de période en bas de la card */}
              <Box sx={{ mt: 2 }}>{renderPeriodToggleButtons()}</Box>
            </StyledPaper>
          </Grid>

          {/* Tableau détaillé des tokens par type */}
          <Grid size={{ xs: 12 }}>
            <StyledPaper>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <SectionTitle>Détail des tokens par type de service ({getPeriodLabel()})</SectionTitle>
                {renderPeriodToggleButtons()}
              </Box>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell key="service_type">
                        <TableSortLabel
                          active={orderBy === 'label'}
                          direction={orderBy === 'label' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('label')}
                        >
                          Type de service
                        </TableSortLabel>
                      </TableCell>
                      <TableCell key="requests" align="right">
                        <TableSortLabel
                          active={orderBy === 'requests'}
                          direction={orderBy === 'requests' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('requests')}
                        >
                          Requêtes
                        </TableSortLabel>
                      </TableCell>
                      <TableCell key="totalTokens" align="right">
                        <TableSortLabel
                          active={orderBy === 'totalTokens'}
                          direction={orderBy === 'totalTokens' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('totalTokens')}
                        >
                          Tokens totaux
                        </TableSortLabel>
                      </TableCell>
                      <TableCell key="promptTokens" align="right" sx={{ color: COLORS.info, fontWeight: 'bold' }}>
                        <TableSortLabel
                          active={orderBy === 'promptTokens'}
                          direction={orderBy === 'promptTokens' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('promptTokens')}
                        >
                          Tokens entrée
                        </TableSortLabel>
                      </TableCell>
                      <TableCell align="right" sx={{ color: COLORS.success, fontWeight: 'bold' }}>
                        <TableSortLabel
                          active={orderBy === 'completionTokens'}
                          direction={orderBy === 'completionTokens' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('completionTokens')}
                        >
                          Tokens sortie
                        </TableSortLabel>
                      </TableCell>
                      <TableCell key="averageTokens" align="right">
                         <TableSortLabel
                          active={orderBy === 'averageTokens'}
                          direction={orderBy === 'averageTokens' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('averageTokens')}
                        >
                          Moyenne tokens/requête
                        </TableSortLabel>
                      </TableCell>
                      <TableCell key="cost" align="right">
                        <TableSortLabel
                          active={orderBy === 'cost'}
                          direction={orderBy === 'cost' ? order : undefined} // Utiliser undefined ici
                          onClick={() => handleRequestSort('cost')}
                        >
                          Coût estimé
                        </TableSortLabel>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(() => {
                      // Déterminer les données à afficher en fonction de la période
                      let tableData: TableRowData[] = []; // Spécifier le type
                      let totalRequests = 0;
                      let totalTokens = 0;
                      let totalPromptTokens = 0;
                      let totalCompletionTokens = 0;
                      let totalCost = 0;

                      // Sélectionner le bon champ selon la période
                      let byServiceType = [];
                      if (periodView === 'day') {
                        byServiceType = globalStats?.by_service_type_daily || [];
                      } else if (periodView === 'week') {
                        byServiceType = globalStats?.by_service_type_weekly || [];
                      } else if (periodView === 'month') {
                        byServiceType = globalStats?.by_service_type_monthly || [];
                      } else if (periodView === 'year') {
                        byServiceType = globalStats?.by_service_type_yearly || [];
                      } else {
                        byServiceType = globalStats?.by_service_type || [];
                      }

                      tableData = byServiceType.map((stat) => {
                        const requests = stat.count || 0;
                        const tokensTotal = stat.sum || 0;
                        const promptTokens = stat.prompt_tokens || 0;
                        const completionTokens = stat.completion_tokens || 0;

                        totalRequests += requests;
                        totalTokens += tokensTotal;
                        totalPromptTokens += promptTokens;
                        totalCompletionTokens += completionTokens;

                        let cost = null;
                        if (globalStats && globalStats.total_cost && globalStats.total_tokens > 0) {
                          const costPerToken = globalStats.total_cost / globalStats.total_tokens;
                          cost = tokensTotal * costPerToken;
                          totalCost += cost; // Accumuler le coût
                        }

                        return {
                          key: stat.service_type,
                          label: getServiceTypeLabel(stat.service_type),
                          color: getServiceTypeColorForChart(stat.service_type),
                          requests,
                          totalTokens: tokensTotal,
                          promptTokens,
                          completionTokens,
                          cost,
                          // Calculer et ajouter la moyenne de tokens ici
                          averageTokens: requests > 0 ? Math.round(tokensTotal / requests) : 0,
                        };
                      });

                      // Appliquer le tri
                      const sortedTableData = stableSort(tableData, getComparator(order, orderBy)); // Utiliser orderBy directement

                      // Rendre les lignes du tableau
                      return (
                        <>
                          {sortedTableData.map((item) => ( // Utiliser les données triées
                            <TableRow key={item.key}>
                              <TableCell>
                                <Chip
                                  size="small"
                                  label={item.label}
                                  sx={{
                                    backgroundColor: item.color,
                                    color: '#fff',
                                    '& .MuiChip-label': { color: '#fff' }
                                  }}
                                />
                              </TableCell>
                              <TableCell align="right">{item.requests.toLocaleString()}</TableCell>
                              <TableCell align="right">{item.totalTokens.toLocaleString()}</TableCell>
                              <TableCell align="right" sx={{ color: COLORS.info, fontWeight: 'medium' }}>{item.promptTokens.toLocaleString()}</TableCell>
                              <TableCell align="right" sx={{ color: COLORS.success, fontWeight: 'medium' }}>{item.completionTokens.toLocaleString()}</TableCell>
                              <TableCell align="right">{item.requests > 0 ? Math.round(item.totalTokens / item.requests).toLocaleString() : 0}</TableCell>
                              <TableCell align="right">{item.cost ? `$${Number(item.cost).toFixed(5)}` : 'N/A'}</TableCell>
                            </TableRow>
                          ))}
                          {/* Ligne de total calculée à partir des données affichées */}
                          <TableRow sx={{ backgroundColor: `${COLORS.primary}10` }}>
                            <TableCell sx={{ fontWeight: 'bold' }}>Total</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>{totalRequests.toLocaleString()}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>{totalTokens.toLocaleString()}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: COLORS.info }}>{totalPromptTokens.toLocaleString()}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold', color: COLORS.success }}>{totalCompletionTokens.toLocaleString()}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>{totalRequests > 0 ? Math.round(totalTokens / totalRequests).toLocaleString() : 0}</TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>{`$${Number(totalCost).toFixed(5)}`}</TableCell>
                          </TableRow>
                        </>
                      );
                    })()}
                  </TableBody>
                </Table>
              </TableContainer>
            </StyledPaper>
          </Grid>
        </Grid>
      </>
    );
  };

  // Rendu des filtres
  const renderFilters = () => {
    return (
      <FilterContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Filter size={18} color={COLORS.primary} />
          <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>Filtres</Typography>
        </Box>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <DatePicker
              selected={startDate}
              onChange={(date: Date | null) => setStartDate(date)}
              dateFormat="dd/MM/yyyy"
              locale={fr}
              customInput={<CustomDateInput label="Date de début" />}
              popperPlacement="bottom-start"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <DatePicker
              selected={endDate}
              onChange={(date: Date | null) => setEndDate(date)}
              dateFormat="dd/MM/yyyy"
              locale={fr}
              customInput={<CustomDateInput label="Date de fin" />}
              popperPlacement="bottom-start"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <StyledFormControl fullWidth size="small">
              <InputLabel id="service-type-label">Type de service</InputLabel>
              <Select
                labelId="service-type-label"
                value={serviceType}
                label="Type de service"
                onChange={(e) => setServiceType(e.target.value)}
              >
                <MenuItem value="">Tous</MenuItem>

                {/* Types de modération */}
                <MenuItem value="moderation">Modération</MenuItem>
                <MenuItem value="text_moderation">Modération Texte</MenuItem>
                <MenuItem value="image_moderation">Modération Image</MenuItem>

                {/* Types de génération */}
                <MenuItem value="generation">Génération</MenuItem>
                <MenuItem value="biography_generation">Génération Biographie</MenuItem>
                <MenuItem value="service_description_generation">Génération Description Service</MenuItem>
                <MenuItem value="mission_post_generation">Génération Mission</MenuItem>
                <MenuItem value="review_response_generation">Génération Réponse Avis</MenuItem>
                <MenuItem value="mission_offer_generation">Génération Offre Mission</MenuItem>
                <MenuItem value="comment_generation">Génération Commentaire</MenuItem>
                <MenuItem value="custom_prompt_generation">Génération Prompt Personnalisé</MenuItem>
                <MenuItem value="midjourney_prompt">Génération Prompt Midjourney</MenuItem>
                <MenuItem value="card_editor_prompt">Génération Prompt Card Editor</MenuItem>

                {/* Autres types */}
                <MenuItem value="mission_assistant">Assistant Mission</MenuItem>
                <MenuItem value="other">Autre</MenuItem>
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2 }}>
            <StyledFormControl fullWidth size="small">
              <InputLabel id="model-label">Modèle</InputLabel>
              <Select
                labelId="model-label"
                value={model}
                label="Modèle"
                onChange={(e) => setModel(e.target.value)}
                disabled={isLoadingModels}
              >
                <MenuItem value="">Tous</MenuItem>
                {availableModels.map((modelName) => (
                  <MenuItem key={modelName} value={modelName}>{modelName}</MenuItem>
                ))}
              </Select>
            </StyledFormControl>
          </Grid>
          <Grid size={{ xs: 12, md: 2 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <StyledButton
                variant="contained"
                color="primary"
                onClick={applyFilters}
                fullWidth
                startIcon={<Filter size={16} />}
              >
                Filtrer
              </StyledButton>
              <StyledButton
                variant="outlined"
                onClick={resetFilters}
                fullWidth
                startIcon={<RefreshCcw size={16} />}
              >
                Réinitialiser
              </StyledButton>
              <StyledButton
                variant="outlined"
                onClick={clearCacheAndRefresh}
                fullWidth
                startIcon={<RefreshCcw size={16} />}
                disabled={isClearingCache}
                sx={{
                  backgroundColor: isClearingCache ? 'rgba(255, 107, 44, 0.1)' : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)'
                  }
                }}
              >
                {isClearingCache ? 'Actualisation...' : 'Vider le cache'}
              </StyledButton>
            </Box>
          </Grid>
        </Grid>
      </FilterContainer>
    );
  };

  // Rendu du tableau d'historique détaillé
  const renderUsageTable = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      );
    }

    return (
      <StyledPaper>
        <StyledTableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Utilisateur</TableCell>
                <TableCell>Service</TableCell>
                <TableCell>Modèle</TableCell>
                <TableCell align="right">Tokens prompt</TableCell>
                <TableCell align="right">Tokens réponse</TableCell>
                <TableCell align="right">Total tokens</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {usageData.map((row) => (
                <TableRow key={row.id}>
                  <TableCell>
                    {format(new Date(row.created_at), 'dd/MM/yyyy HH:mm:ss')}
                  </TableCell>
                  <TableCell>{row.users?.email || 'N/A'}</TableCell>
                  <TableCell>
                    <ChipStyled
                      size="small"
                      label={getServiceTypeLabel(row.service_type)}
                      color={getServiceTypeColor(row.service_type)}
                    />
                  </TableCell>
                  <TableCell>{row.model}</TableCell>
                  <TableCell align="right">{row.prompt_tokens.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.completion_tokens.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.total_tokens.toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </StyledTableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 20, 50, 100]}
          component="div"
          count={pagination.totalItems}
          rowsPerPage={pagination.limit}
          page={pagination.page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`}
          sx={{
            borderTop: `1px solid ${COLORS.borderColor}`,
            '& .MuiTablePagination-select': { borderRadius: '8px' }
          }}
        />
      </StyledPaper>
    );
  };

  // Rendu des graphiques pour les statistiques quotidiennes
  const renderDailyStatsCharts = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      );
    }

    if (dailyStats.length === 0) {
      return (
        <StyledPaper sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="text.secondary">Aucune donnée disponible pour la période sélectionnée</Typography>
        </StyledPaper>
      );
    }

    const chartData = formatChartData(dailyStats);

    return (
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <StyledPaper>
            <SectionTitle>Nombre de requêtes par jour</SectionTitle>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                <XAxis
                  dataKey="date"
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  allowDuplicatedCategory={false}
                />
                <YAxis
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                />
                <Tooltip
                  contentStyle={{
                    borderRadius: '8px',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: 'none'
                  }}
                />
                <Legend />
                {/* Ligne pour le nombre total de requêtes */}
                <Line
                  type="monotone"
                  dataKey="Nombre de requêtes"
                  stroke={COLORS.primary}
                  strokeWidth={3}
                  dot={{ r: 4, fill: COLORS.white, strokeWidth: 2 }}
                  activeDot={{ r: 6, fill: COLORS.primary, strokeWidth: 0 }}
                />

                {/* Lignes pour les différents types de service */}
                <Line
                  type="monotone"
                  dataKey="Modération Texte"
                  stroke={COLORS.secondary}
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: COLORS.secondary, strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Modération Image"
                  stroke={COLORS.info}
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: COLORS.info, strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Biographie"
                  stroke="#FF965E"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#FF965E", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Description Service"
                  stroke="#FF7A35"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#FF7A35", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Mission"
                  stroke="#ffb74d"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#ffb74d", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Réponse Avis"
                  stroke="#ffd54f"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#ffd54f", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Offre Mission"
                  stroke="#e57373"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#e57373", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Commentaire"
                  stroke="#ba68c8"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#ba68c8", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Prompt Personnalisé"
                  stroke="#ce93d8"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#ce93d8", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Génération Slogan"
                  stroke="#f48fb1"
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: "#f48fb1", strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="Assistant Mission"
                  stroke={COLORS.success}
                  strokeWidth={2}
                  dot={{ r: 3, fill: COLORS.white, strokeWidth: 1 }}
                  activeDot={{ r: 5, fill: COLORS.success, strokeWidth: 0 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </StyledPaper>
        </Grid>
        <Grid size={{ xs: 12 }}>
          <StyledPaper>
            <SectionTitle>Nombre de tokens par jour</SectionTitle>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                <XAxis
                  dataKey="date"
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  allowDuplicatedCategory={false}
                />
                <YAxis
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                />
                <Tooltip
                  contentStyle={{
                    borderRadius: '8px',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: 'none'
                  }}
                />
                <Legend />
                {/* Barres empilées pour les différents types de service */}
                <Bar
                  dataKey="Modération Texte"
                  stackId="a"
                  fill={COLORS.secondary}
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Modération Image"
                  stackId="a"
                  fill={COLORS.info}
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Biographie"
                  stackId="a"
                  fill="#FF965E"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Description Service"
                  stackId="a"
                  fill="#FF7A35"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Mission"
                  stackId="a"
                  fill="#ffb74d"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Réponse Avis"
                  stackId="a"
                  fill="#ffd54f"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Offre Mission"
                  stackId="a"
                  fill="#e57373"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Commentaire"
                  stackId="a"
                  fill="#ba68c8"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Prompt Personnalisé"
                  stackId="a"
                  fill="#ce93d8"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Génération Slogan"
                  stackId="a"
                  fill="#f48fb1"
                  radius={[0, 0, 0, 0]}
                  barSize={24}
                />
                <Bar
                  dataKey="Assistant Mission"
                  stackId="a"
                  fill={COLORS.success}
                  radius={[4, 4, 0, 0]}
                  barSize={24}
                />
              </BarChart>
            </ResponsiveContainer>
          </StyledPaper>
        </Grid>
      </Grid>
    );
  };

  // Rendu des graphiques pour les statistiques mensuelles
  const renderMonthlyStatsCharts = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress sx={{ color: COLORS.primary }} />
        </Box>
      );
    }

    if (monthlyStats.length === 0) {
      return (
        <StyledPaper sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="text.secondary">Aucune donnée disponible pour la période sélectionnée</Typography>
        </StyledPaper>
      );
    }

    const chartData = formatChartData(monthlyStats);

    return (
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <StyledPaper>
            <SectionTitle>Nombre de requêtes par mois</SectionTitle>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                <XAxis
                  dataKey="date"
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  allowDuplicatedCategory={false}
                />
                <YAxis
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                />
                <Tooltip
                  contentStyle={{
                    borderRadius: '8px',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: 'none'
                  }}
                />
                <Legend />
                {/* Barres empilées pour les différents types de service */}
                <Bar
                  dataKey="Modération Texte"
                  stackId="a"
                  fill={COLORS.secondary}
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Modération Image"
                  stackId="a"
                  fill={COLORS.info}
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Biographie"
                  stackId="a"
                  fill="#FF965E"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Description Service"
                  stackId="a"
                  fill="#FF7A35"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Mission"
                  stackId="a"
                  fill="#ffb74d"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Réponse Avis"
                  stackId="a"
                  fill="#ffd54f"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Offre Mission"
                  stackId="a"
                  fill="#e57373"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Commentaire"
                  stackId="a"
                  fill="#ba68c8"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Prompt Personnalisé"
                  stackId="a"
                  fill="#ce93d8"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Slogan"
                  stackId="a"
                  fill="#f48fb1"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Assistant Mission"
                  stackId="a"
                  fill={COLORS.success}
                  radius={[4, 4, 0, 0]}
                  barSize={36}
                />
              </BarChart>
            </ResponsiveContainer>
          </StyledPaper>
        </Grid>
        <Grid size={{ xs: 12 }}>
          <StyledPaper>
            <SectionTitle>Nombre de tokens par mois</SectionTitle>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.05)" />
                <XAxis
                  dataKey="date"
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                  allowDuplicatedCategory={false}
                />
                <YAxis
                  stroke="#64748B"
                  tick={{ fontSize: 12, fill: '#64748B' }}
                />
                <Tooltip
                  contentStyle={{
                    borderRadius: '8px',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    border: 'none'
                  }}
                />
                <Legend />
                {/* Barres empilées pour les différents types de service */}
                <Bar
                  dataKey="Modération Texte"
                  stackId="a"
                  fill={COLORS.secondary}
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Modération Image"
                  stackId="a"
                  fill={COLORS.info}
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Biographie"
                  stackId="a"
                  fill="#FF965E"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Description Service"
                  stackId="a"
                  fill="#FF7A35"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Mission"
                  stackId="a"
                  fill="#ffb74d"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Réponse Avis"
                  stackId="a"
                  fill="#ffd54f"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Offre Mission"
                  stackId="a"
                  fill="#e57373"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Commentaire"
                  stackId="a"
                  fill="#ba68c8"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Prompt Personnalisé"
                  stackId="a"
                  fill="#ce93d8"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Génération Slogan"
                  stackId="a"
                  fill="#f48fb1"
                  radius={[0, 0, 0, 0]}
                  barSize={36}
                />
                <Bar
                  dataKey="Assistant Mission"
                  stackId="a"
                  fill={COLORS.success}
                  radius={[4, 4, 0, 0]}
                  barSize={36}
                />
              </BarChart>
            </ResponsiveContainer>
          </StyledPaper>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container maxWidth="xl">
      <GlobalDatePickerStyle>{`
        .react-datepicker-popper {
          z-index: 9999 !important;
        }
        .react-datepicker {
          z-index: 9999 !important;
        }
      `}</GlobalDatePickerStyle>
      <Box sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <PageTitle variant="h4">
            Statistiques d'utilisation OpenRouter
          </PageTitle>
          <StyledButton
            variant="contained"
            color="primary"
            startIcon={<RefreshCcw size={16} />}
            onClick={clearCacheAndRefresh}
            disabled={isClearingCache || isLoading}
          >
            {isClearingCache ? 'Actualisation...' : 'Rafraîchir les données'}
          </StyledButton>
        </Box>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Suivi détaillé de l'utilisation de l'API OpenRouter pour les services de modération et génération de contenu par IA.
        </Typography>

        {/* Informations sur les modèles */}
        {modelsInfo && (
          <StyledPaper sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <SectionTitle>Modèles utilisés</SectionTitle>
            </Box>
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, md: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" color={COLORS.primary}>
                    Modération de texte
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Modèle gratuit:
                      </Typography>
                      <Chip
                        size="small"
                        label={modelsInfo.free_model}
                        color="primary"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Modèle payant:
                      </Typography>
                      <Chip
                        size="small"
                        label={modelsInfo.paid_model}
                        color="secondary"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </Box>
                  </Box>
                </Box>
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" color={COLORS.info}>
                    Modération d'images
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Modèle gratuit:
                      </Typography>
                      <Chip
                        size="small"
                        label={modelsInfo.vision_free_model}
                        color="info"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Modèle payant:
                      </Typography>
                      <Chip
                        size="small"
                        label={modelsInfo.vision_paid_model}
                        color="secondary"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </Box>
                  </Box>
                </Box>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Alert
                  severity="info"
                  icon={<Info size={18} />}
                  sx={{
                    mt: 1,
                    borderRadius: '12px',
                    border: `1px solid ${COLORS.info}20`,
                    '& .MuiAlert-icon': {
                      color: COLORS.info
                    }
                  }}
                >
                  <Typography variant="body2">
                    Les modèles gratuits sont utilisés pour les {modelsInfo.daily_calls_limit || 999} premiers appels quotidiens. Au-delà, les modèles payants sont automatiquement utilisés.
                  </Typography>
                </Alert>
              </Grid>
            </Grid>
          </StyledPaper>
        )}

        {/* Statistiques globales */}
        {renderGlobalStats()}

        {/* Filtres */}
        {renderFilters()}

        {/* Onglets */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <StyledTabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="OpenRouter stats tabs"
            variant={isMobile ? "scrollable" : "standard"}
          >
            <StyledTab icon={<Database size={16} />} iconPosition="start" label="Historique détaillé" />
            <StyledTab icon={<Calendar size={16} />} iconPosition="start" label="Statistiques quotidiennes" />
            <StyledTab icon={<BarChart2 size={16} />} iconPosition="start" label="Statistiques mensuelles" />
          </StyledTabs>
        </Box>

        {/* Contenu des onglets */}
        {tabValue === 0 && renderUsageTable()}
        {tabValue === 1 && renderDailyStatsCharts()}
        {tabValue === 2 && renderMonthlyStatsCharts()}
      </Box>
    </Container>
  );
};

export default OpenRouterStatsPage;