import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { api } from '../../services/api';
import { logger } from '../../utils/logger';
import { CheckCircle, XCircle } from 'lucide-react';

const UnsubscribeNewsletter = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('Traitement de votre demande de désabonnement...');
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const unsubscribe = async () => {
      try {
        // Récupérer l'email depuis l'URL
        const params = new URLSearchParams(location.search);
        const email = params.get('email');

        if (!email) {
          setStatus('error');
          setMessage('Email manquant. Veuillez vérifier le lien que vous avez reçu.');
          return;
        }

        // Appeler l'API pour désabonner l'email
        const response = await api.get(`/api/newsletter/unsubscribe?email=${encodeURIComponent(email)}`);

        if (response.data.success) {
          setStatus('success');
          setMessage(response.data.message || 'Vous avez été désabonné avec succès de notre newsletter.');
        } else {
          setStatus('error');
          setMessage(response.data.message || 'Une erreur est survenue lors du désabonnement.');
        }
      } catch (error) {
        logger.error('Erreur lors du désabonnement:', error);
        setStatus('error');
        setMessage('Une erreur est survenue lors du désabonnement. Veuillez réessayer plus tard.');
      }
    };

    unsubscribe();
  }, [location.search]);

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center px-4 py-12">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
        <div className="text-center">
          {status === 'loading' && (
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF7A35] mx-auto mb-4"></div>
          )}
          
          {status === 'success' && (
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          )}
          
          {status === 'error' && (
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          )}
          
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {status === 'loading' ? 'Traitement en cours' : 
             status === 'success' ? 'Désabonnement réussi' : 'Erreur de désabonnement'}
          </h1>
          
          <p className="text-gray-600 mb-6">{message}</p>
          
          <button
            onClick={handleGoHome}
            className="bg-[#FF7A35] text-white py-2 px-6 rounded-lg hover:bg-[#FF8F35] transition-colors focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:ring-opacity-50"
          >
            Retour à l'accueil
          </button>
        </div>
      </div>
    </div>
  );
};

export default UnsubscribeNewsletter;
