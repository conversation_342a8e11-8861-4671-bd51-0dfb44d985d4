import React, { useState, useCallback, useEffect, useRef } from 'react';
import { getCookie } from '@/utils/cookieUtils';
import { logger } from '@/utils/logger';
import { motion, AnimatePresence } from 'framer-motion';
import { useJobi } from '@/contexts/JobiContext';
import {
  Box,
  Button,
  Typography,
  TextField,
  Paper,
  IconButton,
  InputAdornment,
  Switch,
  styled,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  Chip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  AlertTitle,
  Menu,
  MenuItem,
  Checkbox,
  ChipProps,
  Tooltip
} from '@mui/material';
import {
  Upload as UploadIcon,
  Euro as EuroIcon,
  AccessTime,
  Delete as DeleteIcon,
  DirectionsCar,
  Handyman,
  Pets,
  LocalFlorist,
  Construction,
  Home,
  CleaningServices,
  ChildCare,
  School,
  Computer,
  Brush,
  LocalShipping,
  Celebration,
  PersonOutline,
  Event,
  Business,
  Campaign,
  Spa,
  Palette,
  SportsBasketball,
  AccountBalance,
  Flight,
  Yard,
  Park,
  Face,
  Pool,
  Security,
  Nature,
  Tag,
  Description,
  LocationOn,
  Euro,
  PhotoCamera,
  Chat,
  Close as CloseIcon,
  Warning as WarningIcon,
  Warning,
  Help as HelpCircle,
  Edit
} from '@mui/icons-material';
import { Sparkles, HelpCircle as LucideHelpCircle } from 'lucide-react';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../services/types';
import { notify } from '@/components/Notification';
import DOMPurify from 'dompurify';
import { useNavigate } from 'react-router-dom';
import InterventionZoneMap from '@/pages/dashboard/missions/InterventionZoneMap';
import { missionApi } from './missionApi';
import { ServiceCategory, ServiceSubcategory } from '../services/types';
import ModalPortal from '../../../components/ModalPortal';
import { fr } from 'date-fns/locale';
import { format } from 'date-fns';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import PostMissionGuide from './PostMissionGuide';
import StepGuideModal from './StepGuideModal';
import { registerLocale, setDefaultLocale } from "react-datepicker";
import { fr as frLocale } from 'date-fns/locale';
import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import TiptapEditor, { TiptapInstance } from '../../../components/TiptapEditor'
import { useCreateNotification } from '../../../hooks/useCreateNotification';
import { useImageCompression } from '../../../utils/imageCompressor';
import useContentModeration from '../../../hooks/useContentModeration';
import useImageModeration from '../../../hooks/useImageModeration';
import ImageModerationStatus from '../../../components/ImageModerationStatus';
import RejectedImageMessage from '../../../components/RejectedImageMessage';
import AiGenerationSystem from '../../../components/ai/AiGenerationSystem';
import AiMissionAssistant from '../../../components/ai/AiMissionAssistant';
import AiMissionImageGenerator from '../../../components/ai/AiMissionImageGenerator';

registerLocale('fr', frLocale);
setDefaultLocale('fr');

// Ajout des icônes manquantes avec alias appropriés pour correspondre à l'utilisation dans le fichier
import X from '@mui/icons-material/Close'; // Utilisé pour le bouton de fermeture de modal (<X />)
import Check from '@mui/icons-material/Check';
import ArrowRight from '@mui/icons-material/ArrowRightAlt';
import ArrowLeft from '@mui/icons-material/ArrowBack';
import SearchIcon from '@mui/icons-material/Search';
import XCircle from '@mui/icons-material/Cancel'; // Utilisé comme icône "X" en bordure

const StyledSearchField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    backgroundColor: 'white',
    borderRadius: '12px',
    '& fieldset': {
      borderColor: '#e0e0e0',
    },
    '&:hover fieldset': {
      borderColor: '#FF6B2C',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiOutlinedInput-input': {
    padding: '12px 14px',
    fontSize: '1rem',
    '@media (max-width: 768px)': {
      padding: '10px 12px',
      fontSize: '0.9rem',
    },
    '@media (max-width: 480px)': {
      padding: '8px 10px',
      fontSize: '0.85rem',
    }
  },
  '& .MuiInputAdornment-root': {
    '@media (max-width: 480px)': {
      marginRight: '4px',
      '& .MuiSvgIcon-root': {
        fontSize: '1.2rem',
      }
    }
  },
  '@media (max-width: 480px)': {
    '& .MuiIconButton-root': {
      padding: '4px',
    }
  }
});

const FormContainer = styled(Box)({
  padding: '24px',
  backgroundColor: '#fff',
  borderRadius: '24px',
  boxShadow: '0 4px 6px -1px rgba(255, 107, 44, 0.1), 0 2px 4px -1px rgba(255, 107, 44, 0.06)',
  '@media (max-width: 980px)': {
    padding: '6px',
  },
});

const FormSection = styled(Box)(() => ({
  marginBottom: '24px',
  '& .post-mission-editor': {
    '& .ProseMirror': {
      minHeight: '150px',
      padding: '16px',
      backgroundColor: '#FFFFFF',
      overflowY: 'auto',
      '&:focus': {
        borderColor: '#FF6B2C',
        boxShadow: '0 0 0 2px rgba(255, 107, 44, 0.1)',
      },
    }
  }
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
  [theme.breakpoints.between('sm', 'md')]: {
    fontSize: '1.75rem',
  },
}));

const PageSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  color: 'rgba(0, 0, 0, 0.6)',
  maxWidth: '600px',
  lineHeight: 1.5,
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.875rem',
  },
}));

const FormTitle = styled(Typography)(() => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  color: '#FF6B2C',
  marginBottom: '24px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  '&::before': {
    content: '""',
    display: 'block',
    width: '4px',
    height: '24px',
    backgroundColor: '#FF6B2C',
    borderRadius: '4px',
  },
}));

const StyledMissionField = styled(TextField)(() => ({
  position: 'relative',
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#FFF8F3',
    borderRadius: '16px',
    transition: 'all 0.3s ease-in-out',
    border: '2px solid transparent',
    '&:hover': {
      backgroundColor: '#FFF1E6',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 16px -4px rgba(255, 107, 44, 0.15)',
    },
    '&.Mui-focused': {
      backgroundColor: '#fff',
      transform: 'translateY(-2px)',
      border: '2px solid #FF6B2C',
      boxShadow: '0 12px 24px -6px rgba(255, 107, 44, 0.2)',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#666',
    fontWeight: 500,
    fontSize: '0.95rem',
    backgroundColor: 'transparent',
    padding: '0 6px',
    '&.Mui-focused, &.MuiFormLabel-filled': {
      color: '#FF6B2C',
      fontWeight: 600,
      backgroundColor: '#fff',
    },
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
  '& .MuiFormHelperText-root': {
    marginLeft: '16px',
    fontSize: '0.8rem',
    color: '#666',
    fontStyle: 'italic',
    '&::before': {
      content: '"💡"',
      marginRight: '6px',
    },
  },
  '& .MuiOutlinedInput-input': {
    padding: '16px 20px',
    fontSize: '1rem',
    '&::placeholder': {
      color: '#999',
      opacity: 0.8,
    },
  },
  '& .MuiInputBase-multiline': {
    padding: '16px 20px',
  },
}));

const SearchContainer = styled(Box)(() => ({
  position: 'relative',
  backgroundColor: '#FFFFFF',
  padding: '16px',
  borderRadius: '16px',
  marginBottom: '24px',
  boxShadow: '0 8px 24px rgba(255, 107, 44, 0.08)',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  backdropFilter: 'blur(20px)',
  zIndex: 2,
  '@media (max-width: 768px)': {
    padding: '12px',
    borderRadius: '12px',
    marginBottom: '16px',
  },
  '@media (max-width: 480px)': {
    padding: '10px',
    borderRadius: '10px',
    marginBottom: '12px',
  }
}));

const FilteredSubcategoriesContainer = styled(Box)(() => ({
  marginTop: '16px',
  padding: '24px',
  backgroundColor: '#FFF8F3',
  borderRadius: '16px',
  border: '1px solid rgba(255, 107, 44, 0.2)',
  display: 'flex',
  flexDirection: 'column',
  gap: '16px',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)',
}));

const FilteredChipsContainer = styled(Box)(() => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '8px',
}));

const StyledChip = styled(Chip)(() => ({
  backgroundColor: '#FFFFFF',
  border: '1px solid rgba(255, 107, 44, 0.3)',
  color: '#FF6B2C',
  fontWeight: 500,
  fontSize: '0.875rem',
  height: '36px',
  padding: '0 8px',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.08)',
    borderColor: '#FF6B2C',
    transform: 'translateY(-1px)',
    boxShadow: '0 2px 8px rgba(255, 107, 44, 0.15)',
  },
  '&.Mui-selected': {
    backgroundColor: '#FF6B2C',
    color: '#FFFFFF',
    '& .MuiBox-root span': {
      color: '#FFFFFF',
    },
    '& .category-label': {
      color: 'rgba(255, 255, 255, 0.8)',
    },
    '& .highlight': {
      color: '#FFFFFF',
    },
    '&:hover': {
      backgroundColor: '#FF7A35',
    }
  },
  transition: 'all 0.2s ease-in-out',
}));

const CategoryCard = styled(Paper)(() => ({
  cursor: 'pointer',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  overflow: 'hidden',
  borderRadius: '24px',
  backgroundColor: '#FFFFFF',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  position: 'relative',
  maxWidth: '100%',
  margin: '0 auto',
  width: '100%',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(255, 107, 44, 0.12)',
    '& .category-image': {
      transform: 'scale(1.10)',
    },
    '& .category-overlay': {
      background: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.5) 100%)',
    },
    '& .category-content': {
      transform: 'translateY(-8px)',
    }
  },
  '&.selected': {
    border: '2px solid #FF6B2C',
    boxShadow: '0 20px 40px rgba(255, 107, 44, 0.15)',
    '& .category-overlay': {
      background: 'linear-gradient(180deg, rgba(255, 107, 44, 0.2) 0%, rgba(255, 107, 44, 0.4) 100%)',
    }
  },
  '@media (max-width: 768px)': {
    borderRadius: '16px',
  }
}));

const CategoryImageContainer = styled(Box)({
  position: 'relative',
  width: '100%',
  height: '300px',
  overflow: 'hidden',
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transition: 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  '@media (max-width: 768px)': {
    height: '220px',
  },
  '@media (max-width: 480px)': {
    height: '180px',
  }
});

const CategoryOverlay = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.7) 100%)',
  transition: 'background 0.4s ease',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-end',
  padding: '32px',
  '@media (max-width: 768px)': {
    padding: '20px',
  },
  '@media (max-width: 480px)': {
    padding: '16px',
  }
});

const CategoryContent = styled(Box)({
  transition: 'transform 0.4s ease',
  color: '#FFFFFF',
  textShadow: '0 2px 4px rgba(0,0,0,0.2)',
  '& h5': {
    fontSize: '1.5rem',
    fontWeight: 700,
    marginBottom: '2px',
    lineHeight: 1.2,
    marginTop: '3px',
  },
  '& p': {
    fontSize: '1rem',
    opacity: 0.9,
    marginBottom: '16px',
  },
  '@media (max-width: 768px)': {
    '& h5': {
      fontSize: '1.25rem',
    },
    '& p': {
      fontSize: '0.875rem',
      marginBottom: '8px',
    }
  },
  '@media (max-width: 480px)': {
    '& h5': {
      fontSize: '1.125rem',
    },
    '& p': {
      fontSize: '0.75rem',
      marginBottom: '4px',
    }
  }
});

const SelectionIndicator = styled(Box)({
  position: 'absolute',
  top: '24px',
  right: '24px',
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  backgroundColor: '#FF6B2C',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0,
  transform: 'scale(0.8)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  zIndex: 2,
  boxShadow: '0 4px 8px rgba(255, 107, 44, 0.25)',
  '& svg': {
    color: '#FFFFFF',
    fontSize: '20px',
  },
  '@media (max-width: 768px)': {
    top: '16px',
    right: '16px',
    width: '28px',
    height: '28px',
    '& svg': {
      fontSize: '16px',
    }
  },
  '@media (max-width: 480px)': {
    top: '12px',
    right: '12px',
    width: '24px',
    height: '24px',
    '& svg': {
      fontSize: '14px',
    }
  }
});

const SubcategoryCard = styled(Paper)(() => ({
  padding: '24px',
  cursor: 'pointer',
  transition: 'all 0.3s ease-in-out',
  backgroundColor: '#FFFFFF',
  borderRadius: '12px',
  border: '2px solid transparent',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    borderColor: '#FF6B2C',
  },
  '&.selected': {
    backgroundColor: '#FF6B2C',
    borderColor: '#FF6B2C',
    color: '#FFFFFF',
    transform: 'translateY(-2px)',
    boxShadow: '0 10px 15px -3px rgba(255, 107, 44, 0.2), 0 4px 6px -2px rgba(255, 107, 44, 0.1)',
    '& .subcategory-title': {
      color: '#FFFFFF',
      '&::before': {
        backgroundColor: '#FFFFFF',
      }
    },
    '& .subcategory-description': {
      color: 'rgba(255, 255, 255, 0.9)',
    },
    '& .selection-indicator': {
      opacity: 1,
      transform: 'scale(1.1)',
    }
  },
  '@media (max-width: 768px)': {
    padding: '16px',
    borderRadius: '10px',
  },
  '@media (max-width: 480px)': {
    padding: '12px',
    borderRadius: '8px',
  }
}));

const SubcategoryTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  fontSize: '1.125rem',
  marginBottom: '12px',
  transition: 'all 0.3s ease',
  position: 'relative',
  paddingLeft: '24px',
  color: '#2D3748',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '12px',
    height: '2px',
    background: '#FF6B2C',
    transition: 'width 0.3s ease',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.9rem',
    marginBottom: '8px',
    paddingLeft: '16px',
    '&::before': {
      width: '10px',
      height: '2px',
    }
  }
}));

const SubcategoryDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.9rem',
  color: '#2D3748',
  paddingLeft: '24px',
  marginTop: '4px',
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.75rem',
    paddingLeft: '16px',
  }
}));

const MainContainer = styled('div')({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  margin: '0 auto',
  padding: '16px',
  width: '100%',
  overflowY: 'auto',
  '@media (max-width: 1024px)': {
    padding: '12px',
  },
  '@media (max-width: 600px)': {
    padding: '0px',
  }
});

// New styled components for selection screen
const SelectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  maxWidth: '1200px',
  margin: '0 auto',
  padding: '24px',
  [theme.breakpoints.down('sm')]: {
    padding: '16px',
  },
}));

const SelectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '2rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '24px',
  position: 'relative',
  paddingBottom: '16px',
  textAlign: 'center',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: 'translateX(-50%)',
    width: '60px',
    height: '4px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
    marginBottom: '16px',
  },
}));

const SelectionSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.1rem',
  color: 'rgba(0, 0, 0, 0.6)',
  marginBottom: '32px',
  textAlign: 'center',
  maxWidth: '800px',
  margin: '0 auto 40px auto',
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.95rem',
    marginBottom: '24px',
  },
}));

const OptionsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  gap: '32px',
  width: '100%',
  marginBottom: '32px',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    gap: '24px',
  },
}));

const OptionCard = styled(Paper)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '24px',
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease-in-out',
  height: '100%',
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(255, 107, 44, 0.15)',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '100%',
  },
}));

const OptionHeader = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFF8F3',
  padding: '24px',
  borderBottom: '1px solid rgba(255, 107, 44, 0.1)',
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  [theme.breakpoints.down('sm')]: {
    padding: '16px',
  },
}));

const OptionIcon = styled(Box)(({ theme }) => ({
  width: '64px',
  height: '64px',
  borderRadius: '50%',
  backgroundColor: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0 4px 10px rgba(255, 107, 44, 0.1)',
  color: '#FF6B2C',
  [theme.breakpoints.down('sm')]: {
    width: '48px',
    height: '48px',
  },
}));

const OptionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  color: '#FF6B2C',
  marginBottom: '4px',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.25rem',
  },
}));

const OptionTagline = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  color: '#666',
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.875rem',
  },
}));

const OptionContent = styled(Box)(({ theme }) => ({
  padding: '24px',
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  [theme.breakpoints.down('sm')]: {
    padding: '16px',
  },
}));

const OptionDescription = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  color: '#2D3748',
  marginBottom: '24px',
  flex: 1,
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.875rem',
    marginBottom: '16px',
  },
}));

const FeatureList = styled(Box)(({ theme }) => ({
  marginBottom: '24px',
  display: 'flex',
  flexDirection: 'column',
  gap: '12px',
  [theme.breakpoints.down('sm')]: {
    marginBottom: '16px',
    gap: '8px',
  },
}));

const FeatureItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: '12px',
  [theme.breakpoints.down('sm')]: {
    gap: '8px',
  },
}));

const FeatureIcon = styled(Box)(({ theme }) => ({
  color: '#FF6B2C',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  minWidth: '24px',
  marginTop: '2px',
  [theme.breakpoints.down('sm')]: {
    minWidth: '20px',
  },
}));

const FeatureText = styled(Typography)(({ theme }) => ({
  fontSize: '0.95rem',
  color: '#666',
  [theme.breakpoints.down('sm')]: {
    fontSize: '0.85rem',
  },
}));

const OptionButton = styled(Button)(({ theme }) => ({
  borderRadius: '12px',
  padding: '12px 24px',
  fontWeight: 600,
  fontSize: '1rem',
  boxShadow: '0 4px 10px rgba(255, 107, 44, 0.2)',
  transition: 'all 0.2s ease-in-out',
  textTransform: 'none',
  width: '100%',
  marginTop: 'auto',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 14px rgba(255, 107, 44, 0.3)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '10px 20px',
    fontSize: '0.9rem',
  },
}));

const CategoryGridContainer = styled(Box)(() => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(min(100%, 400px), 1fr))',
  gap: '24px',
  maxWidth: '2400px',
  '@media (max-width: 768px)': {
    gridTemplateColumns: 'repeat(auto-fill, minmax(min(100%, 300px), 1fr))',
    gap: '16px',
  },
  '@media (max-width: 480px)': {
    gridTemplateColumns: '1fr',
    gap: '12px',
  }
}));

const ScrollIndicator = styled(motion.div)({
  position: 'absolute',
  bottom: '35%',
  padding: '8px 25px',
  display: 'flex',
  background: 'white',
  borderRadius: '0px 24px 24px 0px',
  cursor: 'pointer',
  zIndex: 10,
  color: '#FF6B2C',
  fontSize: '0.875rem',
  fontWeight: 500,
  whiteSpace: 'nowrap',
  boxShadow: '2px 2px 10px rgba(255, 107, 44, 0.15)',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  transform: 'translateX(-100%)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent)',
    animation: 'shine 4s cubic-bezier(0.4, 0, 0.2, 1) infinite',
  },
  '@keyframes shine': {
    '0%': {
      left: '-100%',
      opacity: 0,
    },
    '10%': {
      opacity: 1,
    },
    '40%': {
      left: '100%',
      opacity: 0,
    },
    '100%': {
      left: '100%',
      opacity: 0,
    },
  },
  '& .scroll-text': {
    display: 'flex',
    alignItems: 'center',
    gap: '2px',
    animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  },
  '@keyframes pulse': {
    '0%, 100%': {
      opacity: 1,
      transform: 'scale(1)',
    },
    '50%': {
      opacity: 0.85,
      transform: 'scale(0.98)',
    },
  },
  '& .scroll-icon': {
    width: '24px',
    height: '24px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '&::after': {
      content: '""',
      width: '8px',
      height: '8px',
      borderRight: '2px solid currentColor',
      borderBottom: '2px solid currentColor',
      transform: 'rotate(45deg)',
      transition: 'all 0.3s ease'
    }
  },
  '&:hover': {
    background: '#FF6B2C',
    color: 'white',
    transform: 'translateX(0)',
    boxShadow: '4px 4px 15px rgba(255, 107, 44, 0.25)',
    '& .scroll-text': {
      animation: 'none',
      transform: 'scale(1)',
    },
    '& .scroll-icon': {
      animation: 'none',
      transform: 'translateY(4px)',
      '&::after': {
        transform: 'rotate(45deg) scale(1.2)',
      }
    }
  },
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
});

const ScrollableArea = styled('div')({
  flex: 1,
  overflowY: 'auto',
  position: 'relative',
  scrollBehavior: 'smooth',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#FF6B2C',
    borderRadius: '4px',
    '&:hover': {
      background: '#FF7A35',
    },
  },
});

const ScrollableContent = styled('div')({
  padding: '14px',
  minHeight: '100%'
});

const StepSummary = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFF',
  borderRadius: '12px',
  padding: '16px',
  marginTop: '16px',
  display: 'flex',
  flexDirection: 'column',
  gap: '16px',
  border: '1px solid rgba(255, 107, 44, 0.1)',
  [theme.breakpoints.down('sm')]: {
    display: 'none'
  }
}));

const StepProgress = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  padding: '8px 16px',
  backgroundColor: '#FFF8F3',
  borderRadius: '8px',
  border: '1px solid rgba(255, 107, 44, 0.1)'
});

const StepInfo = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px'
});

const NextStep = styled(Box)({
  marginLeft: 'auto',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: '#666'
});

const StyledPaper = styled(Paper)(() => ({
  display: 'flex',
  flexDirection: 'column',
  margin: '20px 0px 0px 0px',
  borderRadius: '16px'
}));

type PaymentMethod = 'jobi_only' | 'both' | 'direct_only';

interface InterventionZone {
  center: [number, number];
  radius: number;
  adresse?: string;
}

interface TimeSlot {
  date: string;
  start: string;
  end: string;
}

interface FormData {
  titre: string;
  description: string;
  budget: number;
  budget_defini: boolean;
  duree_estimee: number;
  date_mission: string;
  has_time_preference: boolean;
  time_slots: TimeSlot[];
  adresse: string;
  code_postal: string;
  ville: string;
  pays: string;
  payment_method: PaymentMethod;
  intervention_zone: InterventionZone;
  is_urgent: boolean;
  category_id?: string;
  subcategory_id?: string;
}

const steps = [
  'Catégorie et service',
  'Description',
  'Localisation',
  'Budget',
  'Horaires',
  'Photos',
  'Résumé'
] as const;

const getIconComponent = (iconName: string) => {
  const iconMap: { [key: string]: React.ComponentType } = {
    'DirectionsCar': DirectionsCar,
    'Handyman': Handyman,
    'Pets': Pets,
    'LocalFlorist': LocalFlorist,
    'Construction': Construction,
    'Home': Home,
    'CleaningServices': CleaningServices,
    'ChildCare': ChildCare,
    'School': School,
    'Computer': Computer,
    'Brush': Brush,
    'LocalShipping': LocalShipping,
    'Celebration': Celebration,
    'PersonOutline': PersonOutline,
    'Event': Event,
    'Business': Business,
    'Campaign': Campaign,
    'Spa': Spa,
    'Palette': Palette,
    'SportsBasketball': SportsBasketball,
    'AccountBalance': AccountBalance,
    'Flight': Flight,
    'Yard': Yard,
    'Park': Park,
    'Face': Face,
    'Pool': Pool,
    'Security': Security,
    'Nature': Nature
  };

  const IconComponent = iconMap[iconName];
  return IconComponent ? <IconComponent /> : null;
};

const ButtonContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '16px',
  padding: '16px 24px',
  backgroundColor: '#fff',
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column-reverse', // Inverser l'ordre pour que le bouton principal soit en haut
    padding: '12px 16px',
    gap: '12px',
    position: 'sticky',
    bottom: 0,
    borderTop: '1px solid rgba(0, 0, 0, 0.1)',
    boxShadow: '0 -4px 10px rgba(0, 0, 0, 0.05)',
    zIndex: 10,
  }
}));

const StyledDatePickerContainer = styled('div')({
  '& .react-datepicker-wrapper': {
    width: '100%',
  },
  '& .datepicker-input': {
    backgroundColor: '#FFF8F3',
    border: '1px solid #E0E0E0',
    borderRadius: '16px',
    padding: '16.5px 14px',
    width: '100%',
    fontSize: '1rem',
    cursor: 'pointer',
    transition: 'border-color 0.2s',
    outline: 'none',
    color: '#FF6B2C',
    fontWeight: 500,
    marginBottom: '20px',
    '&::placeholder': {
      color: '#FF6B2C',
    },
    '&:hover': {
      borderColor: '#FF965E',
    },
    '&:focus': {
      borderColor: '#FF6B2C',
      boxShadow: '0 0 0 1px #FF6B2C',
    },
  },
  '& .react-datepicker-popper': {
    zIndex: 9999,
  },
  '& .react-datepicker': {
    fontFamily: '"Roboto","Helvetica","Arial",sans-serif',
    border: '1px solid #E0E0E0',
    borderRadius: '16px',
    backgroundColor: '#FFF8F3',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
  },
  '& .react-datepicker__header': {
    backgroundColor: '#FFF8F3',
    borderBottom: '1px solid #FFE4BA',
    borderTopLeftRadius: '16px',
    borderTopRightRadius: '16px',
    padding: '16px 8px 8px',
  },
  '& .react-datepicker__current-month': {
    color: '#FF6B2C',
    fontWeight: 600,
    fontSize: '1rem',
    marginBottom: '8px',
  },
  '& .react-datepicker__day-name': {
    color: '#666',
    width: '36px',
    margin: '2px',
  },
  '& .react-datepicker__day': {
    width: '36px',
    height: '36px',
    lineHeight: '36px',
    margin: '2px',
    borderRadius: '50%',
    color: '#666',
    '&:hover': {
      backgroundColor: '#FFE4BA',
    },
  },
  '& .react-datepicker__day--selected': {
    backgroundColor: '#FF6B2C !important',
    color: 'white !important',
    '&:hover': {
      backgroundColor: '#FF965E !important',
    },
  },
  '& .react-datepicker__day--keyboard-selected': {
    backgroundColor: '#FF6B2C',
    color: 'white',
  },
  '& .react-datepicker__day--today': {
    color: 'white',
    backgroundColor: '#FFE4BA',
    fontWeight: 'bold',
  },
  '& .react-datepicker__day--disabled': {
    color: '#E0E0E0',
    cursor: 'not-allowed',
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  '& .react-datepicker__navigation': {
    top: '16px',
  },
  '& .react-datepicker__navigation-icon::before': {
    borderColor: '#FF6B2C',
  },
  '& .react-datepicker__triangle': {
    display: 'none',
  },
});

const CustomTimeSelect = ({ value, onChange, label }: { value: string, onChange: (value: string) => void, label: string }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  const minutes = ['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55'];

  const [selectedHour, selectedMinute] = value ? value.split(':') : ['00', '00'];

  const formatDisplayValue = (value: string) => {
    if (!value) return '';
    const [hour, minute] = value.split(':');
    return `${hour}h${minute}`;
  };

  const [inputValue, setInputValue] = React.useState(formatDisplayValue(value));

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleTimeSelect = (type: 'hour' | 'minute', newValue: string) => {
    if (type === 'hour') {
      const newTime = `${newValue}:${selectedMinute}`;
      onChange(newTime);
      setInputValue(formatDisplayValue(newTime));
      if (value && value.includes(':')) {
        // Si on avait déjà une heure complète, on ferme
        handleClose();
      }
    } else {
      const newTime = `${selectedHour}:${newValue}`;
      onChange(newTime);
      setInputValue(formatDisplayValue(newTime));
      handleClose();
    }
  };

  const parseInputValue = (input: string): string | null => {
    // Accepter les formats: "14h30", "14:30", "14 30", "1430", "14"
    let hour = '00';
    let minute = '00';

    // Format "14h30" ou "14H30"
    const hFormat = input.match(/^(\d{1,2})[hH](\d{2})$/);
    if (hFormat) {
      hour = hFormat[1].padStart(2, '0');
      minute = hFormat[2];
    }
    // Format "14:30"
    else if (input.includes(':')) {
      const parts = input.split(':');
      if (parts.length === 2) {
        hour = parts[0].trim().padStart(2, '0');
        minute = parts[1].trim();
      }
    }
    // Format "14 30"
    else if (input.includes(' ')) {
      const parts = input.split(' ');
      if (parts.length === 2) {
        hour = parts[0].trim().padStart(2, '0');
        minute = parts[1].trim();
      }
    }
    // Format "1430"
    else if (input.length === 4 && /^\d{4}$/.test(input)) {
      hour = input.substring(0, 2);
      minute = input.substring(2, 4);
    }
    // Format "14" (juste l'heure)
    else if (/^\d{1,2}$/.test(input)) {
      hour = input.padStart(2, '0');
      minute = '00';
    }

    // Validation des valeurs
    const hourNum = parseInt(hour, 10);
    const minuteNum = parseInt(minute, 10);

    if (isNaN(hourNum) || hourNum < 0 || hourNum > 23) {
      return null;
    }

    if (isNaN(minuteNum) || minuteNum < 0 || minuteNum > 59) {
      return null;
    }

    // Normaliser au format attendu
    return `${hourNum.toString().padStart(2, '0')}:${minuteNum.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
  };

  const handleInputBlur = () => {
    const parsedTime = parseInputValue(inputValue);
    if (parsedTime) {
      onChange(parsedTime);
      setInputValue(formatDisplayValue(parsedTime));
    } else {
      // Si l'entrée est invalide, revenir à la valeur précédente
      setInputValue(formatDisplayValue(value));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const parsedTime = parseInputValue(inputValue);
      if (parsedTime) {
        onChange(parsedTime);
        setInputValue(formatDisplayValue(parsedTime));
      } else {
        // Si l'entrée est invalide, revenir à la valeur précédente
        setInputValue(formatDisplayValue(value));
      }
    }
  };

  return (
    <Box sx={{ flex: 1 }}>
      <TextField
        fullWidth
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        onKeyDown={handleKeyDown}
        label={label}
        placeholder="Ex: 14h30"
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={handleClick} edge="end">
                <AccessTime sx={{ color: '#FF6B2C' }} />
              </IconButton>
            </InputAdornment>
          ),
          sx: {
            cursor: 'text',
            backgroundColor: '#FFF8F3',
            borderRadius: '16px',
            height: '56px',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#FF965E',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#E0E0E0',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#FF6B2C',
              borderWidth: '1px',
            },
          }
        }}
        sx={{
          '& .MuiInputLabel-root': {
            color: '#666',
            '&.Mui-focused': {
              color: '#FF6B2C',
            },
          },
          '& .MuiInputBase-input': {
            color: '#FF6B2C',
            fontWeight: 500,
          },
        }}
        helperText="Formats acceptés: 14h30, 14:30, 14 30, 1430, 14"
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          '& .MuiPaper-root': {
            maxHeight: 400,
            width: 320,
            backgroundColor: '#FFF8F3',
            borderRadius: '16px',
            border: '1px solid #E0E0E0',
          },
        }}
      >
        <Box sx={{
          p: 2,
          display: 'flex',
          gap: 2,
          borderBottom: '1px solid #FFE4BA',
          backgroundColor: '#FFF8F3',
        }}>
          <Typography variant="subtitle2" sx={{ color: '#FF6B2C', fontWeight: 600 }}>
            Sélectionnez une heure
          </Typography>
        </Box>
        <Box sx={{
          display: 'flex',
          height: '300px',
        }}>
          {/* Heures */}
          <Box sx={{
            flex: 1,
            borderRight: '1px solid #FFE4BA',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#FFF8F3',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#FFE4BA',
              borderRadius: '3px',
            },
          }}>
            {hours.map((hour) => (
              <MenuItem
                key={hour}
                onClick={() => handleTimeSelect('hour', hour)}
                selected={selectedHour === hour}
                sx={{
                  justifyContent: 'center',
                  minHeight: '40px',
                  '&:hover': {
                    backgroundColor: '#FFE4BA',
                  },
                  '&.Mui-selected': {
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#FF965E',
                    },
                  },
                }}
              >
                {hour}h
              </MenuItem>
            ))}
          </Box>
          {/* Minutes */}
          <Box sx={{
            flex: 1,
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#FFF8F3',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#FFE4BA',
              borderRadius: '3px',
            },
          }}>
            {minutes.map((minute) => (
              <MenuItem
                key={minute}
                onClick={() => handleTimeSelect('minute', minute)}
                selected={selectedMinute === minute}
                sx={{
                  justifyContent: 'center',
                  minHeight: '40px',
                  '&:hover': {
                    backgroundColor: '#FFE4BA',
                  },
                  '&.Mui-selected': {
                    backgroundColor: '#FF6B2C',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#FF965E',
                    },
                  },
                }}
              >
                {minute}
              </MenuItem>
            ))}
          </Box>
        </Box>
      </Menu>
    </Box>
  );
};

// Interface pour le profil de l'utilisateur afin de vérifier si il est complet
interface UserProfileData {
  nom: string | null;
  prenom: string | null;
  telephone: string | null;
  ville: string | null;
  code_postal: string | null;
  adresse: string | null;
}

const PostMission: React.FC = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategory | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<ServiceSubcategory | null>(null);
  const [showIncompleteProfileModal, setShowIncompleteProfileModal] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfileData | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    titre: '',
    description: '',
    budget: 0,
    budget_defini: false, // Par défaut : aucun budget fixé (switch coché)
    duree_estimee: 1,
    date_mission: '',
    has_time_preference: false,
    time_slots: [],
    adresse: '',
    code_postal: '',
    ville: '',
    pays: 'France',
    payment_method: 'jobi_only',
    intervention_zone: {
      center: [46.603354, 1.888334],
      radius: 1000,
      adresse: '',
    },
    is_urgent: false
  });
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<ServiceCategory[]>(SERVICE_CATEGORIES);

  // États pour la modération d'images
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const [moderationImages, setModerationImages] = useState<File[]>([]);
  const [currentModerationIndex, setCurrentModerationIndex] = useState(0);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const { moderateImage, isLoading: isModerationLoading } = useImageModeration();
  const [moderatedFiles, setModeratedFiles] = useState<File[]>([]);
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();
  const [filteredSubcategories, setFilteredSubcategories] = useState<ServiceSubcategory[]>([]);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [showStepGuide, setShowStepGuide] = useState(false);
  const [hasSpecificTimeSlots, setHasSpecificTimeSlots] = useState(false); // Par défaut : décoché (pas de préférence horaire)
  const [showScrollIndicator, setShowScrollIndicator] = useState(true);
  const scrollableRef = useRef<HTMLDivElement>(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const [currentTimeSlot, setCurrentTimeSlot] = useState<{start: string, end: string}>({
    start: '08:00',
    end: '18:00'
  });
  const [selectedDate, setSelectedDate] = useState<string>('');
  const { updateBalance } = useJobi();
  const [isModerating, setIsModerating] = useState(false);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);
  const [isAiAssistantOpen, setIsAiAssistantOpen] = useState(false);
  const [descriptionLength, setDescriptionLength] = useState(0);
  // New state for selection screen
  const [showSelectionScreen, setShowSelectionScreen] = useState(true);

  // Fonction pour générer du contenu avec l'IA
  const handleGenerateWithAi = () => {
    if (!formData.titre || formData.titre.length < 5) {
      notify("Veuillez d'abord saisir un titre de mission d'au moins 5 caractères", "warning");
      return;
    }

    // Ouvrir le modal de confirmation pour la génération IA
    setIsAiConfirmModalOpen(true);
  };

  // Fonction pour ouvrir l'assistant IA
  const handleOpenAiAssistant = () => {
    setIsAiAssistantOpen(true);
  };

  // Fonction commune pour gérer les notifications et récompenses après la publication d'une mission
  const handleMissionPublishSuccess = async () => {
    notify('Votre mission a été publiée avec succès !', 'success');
    notify('Important : Toutes les offres doivent être répondues sous 24 heures maximum. Sans réponse, un avis négatif sera automatiquement publié sur votre profil.', 'warning');

    try {
      const success = await updateBalance({
        montant: 1,
        operation: 'plus',
        titre: 'Mission publiée',
        description: '1 Jobi offert pour avoir publié votre mission'
      });

      if (success) {
        notify('Vous avez reçu 1 Jobi pour avoir publié votre mission !', 'success');

        try {
          if (createJobiNotification) {
            await createJobiNotification(
              'Nouveau Jobi',
              `Vous avez gagné 1 Jobi pour avoir publié votre mission`
            );
          }
        } catch (notifError) {
          logger.error('Erreur lors de la création de la notification Jobi:', notifError);
        }
      }
    } catch (error: any) {
      if (error.response?.data?.error?.includes('limite de récompenses')) {
        notify('Vous avez atteint la limite de récompenses Jobi pour aujourd\'hui', 'info');
      } else {
        logger.error('Erreur lors de l\'ajout des Jobis:', error);
        notify('Erreur lors de l\'ajout des Jobis. Votre mission a bien été publiée, mais la récompense n\'a pas pu être ajoutée.', 'warning');
      }
    }
  };

  // Fonction pour appliquer les données générées par l'assistant IA et publier directement la mission
  const handleAiAssistantComplete = async (missionData: any) => {
    try {
      setIsSubmitting(true);

      // Extraire les photos de missionData si elles existent
      const photosFromAI = missionData.photos || [];

      // Log pour déboguer les photos reçues
      logger.info(`Photos reçues de l'assistant IA: ${photosFromAI.length} photo(s)`);

      // Mettre à jour les données du formulaire avec les données générées
      const updatedFormData = {
        ...formData,
        titre: missionData.titre || formData.titre,
        description: missionData.description || formData.description,
        budget: missionData.budget || formData.budget,
        budget_defini: missionData.budget_defini !== undefined ? missionData.budget_defini : formData.budget_defini,
        date_mission: missionData.date_mission || formData.date_mission,
        has_time_preference: missionData.has_time_preference !== undefined ? missionData.has_time_preference : formData.has_time_preference,
        time_slots: missionData.time_slots || formData.time_slots,
        adresse: missionData.adresse || formData.adresse,
        code_postal: missionData.code_postal || formData.code_postal,
        ville: missionData.ville || formData.ville,
        payment_method: missionData.payment_method || formData.payment_method,
        category_id: missionData.category_id || formData.category_id,
        subcategory_id: missionData.subcategory_id || formData.subcategory_id
      };

      // Identifier la catégorie et sous-catégorie
      let selectedCat = selectedCategory;
      let selectedSubcat = selectedSubcategory;

      if (missionData.category_id) {
        const category = SERVICE_CATEGORIES.find(c => c.id === missionData.category_id);
        if (category) {
          selectedCat = category;
        }
      }

      if (missionData.subcategory_id) {
        const subcategory = SERVICE_SUBCATEGORIES.find(sc => sc.id === missionData.subcategory_id);
        if (subcategory) {
          selectedSubcat = subcategory;
        }
      }

      // Vérifier que les données nécessaires sont présentes
      if (!updatedFormData.titre || !updatedFormData.description || !selectedCat || !selectedSubcat) {
        notify('Certaines informations sont manquantes pour publier la mission', 'error');
        return;
      }

      // Créer une copie triée des créneaux
      const sortedTimeSlots = updatedFormData.time_slots
        .map((slot: { date: string; start: string; end: string }) => ({
          date: slot.date,
          start: slot.start,
          end: slot.end
        }))
        .sort((a: { date: string }, b: { date: string }) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // Vérifier la cohérence entre has_time_preference et time_slots
      const hasTimePreference = updatedFormData.has_time_preference && sortedTimeSlots.length > 0;

      // Préparer les données pour l'envoi
      const sanitizedData = {
        ...updatedFormData,
        category_id: selectedCat?.id || '',
        subcategory_id: selectedSubcat?.id || '',
        titre: DOMPurify.sanitize(updatedFormData.titre),
        description: DOMPurify.sanitize(updatedFormData.description),
        adresse: DOMPurify.sanitize(updatedFormData.adresse || ''),
        ville: DOMPurify.sanitize(updatedFormData.ville),
        budget: Number(updatedFormData.budget),
        budget_defini: Boolean(updatedFormData.budget_defini),
        time_slots: hasTimePreference ? sortedTimeSlots : [],
        has_time_preference: hasTimePreference,
        date_mission: hasTimePreference ? sortedTimeSlots[0]?.date || '' : ''
      };

      // Publier la mission
      const mission = await missionApi.createMission(sanitizedData);

      // Ajouter les photos de l'assistant IA si elles existent
      if (photosFromAI.length > 0) {
        logger.info(`Téléchargement de ${photosFromAI.length} photo(s) depuis l'assistant IA`);

        // Modérer les photos de l'assistant IA avant de les uploader
        try {
          setIsModerationModalOpen(true);
          setModerationImages([...photosFromAI]);

          // Modérer chaque image
          const moderatedResults = [];
          for (let i = 0; i < photosFromAI.length; i++) {
            const file = photosFromAI[i];
            setCurrentModerationIndex(i);
            setModerationPreviewUrl(URL.createObjectURL(file));

            // Modérer l'image
            try {
              const result = await moderateImage(file, 'mission', `mission-photo-ai-${i}`);

              if (result.isSafe) {
                moderatedResults.push(file);
              } else {
                logger.warn(`L'image ${i + 1} de l'assistant IA contient du contenu inapproprié et ne sera pas utilisée.`);
              }
            } catch (error) {
              logger.error('Erreur lors de la modération de l\'image de l\'assistant IA:', error);
            }
          }

          setIsModerationModalOpen(false);

          if (moderatedResults.length > 0) {
            await missionApi.uploadMissionPhotos(mission.id, moderatedResults);
          }
        } catch (error) {
          logger.error('Erreur lors de la modération des images de l\'assistant IA:', error);
          setIsModerationModalOpen(false);
        }
      }
      // Sinon, utiliser les photos sélectionnées manuellement
      else if (selectedFiles.length > 0) {
        logger.info(`Téléchargement de ${selectedFiles.length} photo(s) sélectionnées manuellement`);

        // Utiliser les fichiers modérés s'ils existent, sinon utiliser les fichiers sélectionnés
        const filesToUpload = moderatedFiles.length > 0 ? moderatedFiles : selectedFiles;

        await missionApi.uploadMissionPhotos(mission.id, filesToUpload);
      }

      await handleMissionPublishSuccess();
      navigate('/dashboard/missions');
    } catch (error) {
      notify(error instanceof Error ? error.message : 'Une erreur est survenue lors de la publication', 'error');
      logger.error('Erreur lors de la création de la mission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const editorRef = useRef<TiptapInstance>(null);
  const { compressGalleryPhoto } = useImageCompression();
  const { validateContentSafety } = useContentModeration();

  // Fonction pour nettoyer le HTML
  const stripHtml = (html: string) => {
    if (!html) return '';
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  // Nous n'utilisons plus useAiGenerationProcess ici car nous utilisons directement AiGenerationSystem
  // avec isAiConfirmModalOpen pour contrôler l'affichage

  // Récupérer le profil de l'utilisateur et vérifier si il est complet afin d'autoriser l'utilisateur à poster une mission sinon on affiche un modal
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await axios.get('/api/users/profil', API_CONFIG);
        setUserProfile(response.data.profil.data);

        // Vérifier si le profil est complet
        const profileData = response.data?.profil?.data;
        if (!profileData?.prenom ||
            !profileData?.nom ||
            !profileData?.telephone ||
            !profileData?.ville ||
            !profileData?.code_postal) {
          setShowIncompleteProfileModal(true);
          logger.info('Résultat de la récupération du profil pour autoriser le poste d\'une mission :', profileData.prenom, profileData.nom, profileData.telephone, profileData.ville, profileData.code_postal);
        }

        // Si l'utilisateur a une ville et un code postal, on utilise ces données pour localiser la mission
        if (profileData?.ville && profileData?.code_postal) {
          try {
            // Construire une adresse à partir des informations du profil
            const address = `${profileData.adresse || ''} ${profileData.code_postal} ${profileData.ville} ${profileData.pays || 'France'}`.trim();

            // Geocoder l'adresse via l'API api-adresse.data.gouv.fr
            const geocodeResponse = await axios.get(`https://api-adresse.data.gouv.fr/search/`, {
              params: {
                q: address,
                limit: 1
              },
              timeout: 5000
            });

            if (geocodeResponse.data && geocodeResponse.data.features && geocodeResponse.data.features.length > 0) {
              const feature = geocodeResponse.data.features[0];
              const coordinates = feature.geometry.coordinates;
              const latitude = coordinates[1]; // Latitude
              const longitude = coordinates[0]; // Longitude

              // Mettre à jour les coordonnées avec celles de l'utilisateur
              setFormData(prev => ({
                ...prev,
                adresse: profileData.adresse || '',
                code_postal: profileData.code_postal || '',
                ville: profileData.ville || '',
                pays: profileData.pays || 'France',
                intervention_zone: {
                  ...prev.intervention_zone,
                  center: [latitude, longitude],
                  adresse: profileData.adresse || ''
                }
              }));

              logger.info('Localisation initialisée avec les données du profil:', latitude, longitude, profileData.ville);
            }
          } catch (error) {
            logger.error('Erreur lors du géocodage de l\'adresse:', error);
            // En cas d'erreur, on conserve les valeurs par défaut
          }
        }
      } catch (error) {
        logger.error('Erreur lors de la récupération du profil:', error);
        notify('Erreur lors de la récupération de votre profil', 'error');
      } finally {
        setIsLoadingProfile(false);
      }
    };

    fetchUserProfile();
  }, []);

  const checkScrollNeeded = useCallback(() => {
    if (!scrollableRef.current) return false;

    const container = scrollableRef.current;
    const content = container.firstElementChild as HTMLElement;
    if (!content) return false;

    // Obtenir la hauteur réelle du contenu (incluant le padding)
    const contentHeight = content.getBoundingClientRect().height;
    // Obtenir la hauteur visible du conteneur
    const containerHeight = container.clientHeight;

    // Vérifier si le contenu dépasse la hauteur du conteneur
    const hasOverflow = contentHeight > containerHeight;

    // Si on a du dépassement, vérifier si on est proche du bas
    if (hasOverflow) {
      const currentScroll = container.scrollTop;
      const maxScroll = container.scrollHeight - container.clientHeight;

      // Masquer dès qu'on scrolle de 75px
      if (currentScroll > 75) return false;

      return currentScroll < maxScroll - 100;
    }

    return false;
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollIndicator(checkScrollNeeded());
    };

    const scrollableElement = scrollableRef.current;
    if (scrollableElement) {
      scrollableElement.addEventListener('scroll', handleScroll);
      // Vérification initiale
      setShowScrollIndicator(checkScrollNeeded());
    }

    return () => {
      if (scrollableElement) {
        scrollableElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, [checkScrollNeeded]);

  // Ajouter un effet pour revérifier quand le contenu change
  useEffect(() => {
    setShowScrollIndicator(checkScrollNeeded());
  }, [activeStep, checkScrollNeeded]);

  const scrollToContent = () => {
    if (scrollableRef.current) {
      const currentScroll = scrollableRef.current.scrollTop;
      scrollableRef.current.scrollTo({
        top: currentScroll + 350,
        behavior: 'smooth'
      });
    }
  };

  const normalizeString = (str: string) => {
    return str
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };

  const calculateRelevance = (searchTerm: string, subcategory: ServiceSubcategory): number => {
    const normalizedSearch = normalizeString(searchTerm);
    const normalizedName = normalizeString(subcategory.nom);

    // Score de base
    let score = 0;

    // Vérification des correspondances partielles dans le nom
    if (normalizedName.includes(normalizedSearch)) {
      // Correspondance exacte du nom
      if (normalizedName === normalizedSearch) {
        score += 100;
      }
      // Le nom commence par le terme de recherche
      else if (normalizedName.startsWith(normalizedSearch)) {
        score += 80;
      }
      // Le terme de recherche est contenu dans le nom
      else {
        score += 60;
      }
    }

    // Vérification des synonymes
    if (subcategory.synonymes) {
      for (const syn of subcategory.synonymes) {
        const normalizedSyn = normalizeString(syn);

        // Correspondance partielle dans les synonymes
        if (normalizedSyn.includes(normalizedSearch)) {
          // Correspondance exacte du synonyme
          if (normalizedSyn === normalizedSearch) {
            score += 100;
          }
          // Le synonyme commence par le terme de recherche
          else if (normalizedSyn.startsWith(normalizedSearch)) {
            score += 80;
          }
          // Le terme de recherche est contenu dans le synonyme
          else {
            score += 60;
          }
          // On prend le meilleur score parmi les synonymes
          break;
        }
      }
    }

    return score;
  };

  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm) return <span>{text}</span>;

    const normalizedSearch = normalizeString(searchTerm).toLowerCase();
    const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));

    return (
      <span>
        {parts.map((part, index) =>
          normalizeString(part).toLowerCase() === normalizedSearch ? (
            <span
              key={index}
              className="highlight"
              style={{
                borderRadius: '2px',
                fontWeight: 600,
                display: 'inline'
              }}
            >
              {part}
            </span>
          ) : (
            <span key={index}>{part}</span>
          )
        )}
      </span>
    );
  };

  const handleSearch = (query: string) => {
    const searchTerm = query.toLowerCase();
    setSearchQuery(searchTerm);

    if (!searchTerm) {
      setFilteredCategories(SERVICE_CATEGORIES);
      setFilteredSubcategories([]);
      return;
    }

    const normalizedSearch = normalizeString(searchTerm);

    // Recherche dans les sous-catégories avec score de pertinence
    const matchingSubcategories = SERVICE_SUBCATEGORIES
      .map(sub => ({
        subcategory: sub,
        relevance: calculateRelevance(searchTerm, sub)
      }))
      .filter(({ relevance }) => relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .map(({ subcategory }) => subcategory);

    setFilteredSubcategories(matchingSubcategories);

    // Filtrer les catégories qui contiennent des sous-catégories correspondantes
    const subcategoryIds = new Set(matchingSubcategories.map(sub => sub.categoryId));
    const matchingCategories = SERVICE_CATEGORIES.filter(category => {
      const matchesName = normalizeString(category.nom).includes(normalizedSearch);
      const matchesDescription = normalizeString(category.description).includes(normalizedSearch);
      const hasMatchingSubcategories = subcategoryIds.has(category.id);
      return matchesName || matchesDescription || hasMatchingSubcategories;
    });

    setFilteredCategories(matchingCategories);
  };

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
    let newFiles = Array.from(files).filter((file: File) => {
      if (!allowedMimeTypes.includes(file.type)) {
        notify(`Le format ${file.type} n'est pas supporté. Utilisez JPG, PNG ou WEBP.`, 'error');
        return false;
      }
      return true;
    });

    if (newFiles.length === 0) return;

    if (selectedFiles.length + newFiles.length > 8) {
      notify('Vous ne pouvez pas ajouter plus de 8 photos', 'error');
      return;
    }

    // Compression des images avant ajout
    try {
      const compressedFiles = await Promise.all(newFiles.map(file => compressGalleryPhoto(file)));
      const newPreviewUrls = compressedFiles.map(file => URL.createObjectURL(file));
      setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
      setSelectedFiles(prev => [...prev, ...compressedFiles]);
    } catch (error) {
      notify('Erreur lors de la compression des images', 'error');
    }
  }, [selectedFiles, compressGalleryPhoto]);

  const handleRemoveFile = useCallback((index: number) => {
    URL.revokeObjectURL(previewUrls[index]);
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  }, [previewUrls]);

  // Fonction pour modérer les images
  const moderateImages = async () => {
    if (selectedFiles.length === 0) {
      return true; // Pas d'images à modérer
    }

    // Réinitialiser les états de rejet
    setIsImageRejected(false);
    setRejectionDescription(undefined);
    setRejectionImprovementSuggestions(undefined);

    try {
      setIsModerationModalOpen(true);
      setModerationImages([...selectedFiles]);
      setCurrentModerationIndex(0);

      // Modérer chaque image
      const moderatedResults = [];
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setCurrentModerationIndex(i);
        setModerationPreviewUrl(URL.createObjectURL(file));

        // Modérer l'image
        try {
          const result = await moderateImage(file, 'mission', `mission-photo-${i}`);

          if (!result.isSafe) {
            // Mettre à jour les états pour afficher le message de rejet
            setIsImageRejected(true);
            setRejectionDescription(result.description);
            setRejectionImprovementSuggestions(result.improvementSuggestions);

            // Garder la modale ouverte pour afficher le message détaillé
            // Ne pas fermer la modale ici

            notify(`L'image ${i + 1} ne respecte pas nos règles de modération.`, 'error');

            // Afficher un message plus détaillé dans la console pour le débogage
            logger.info('Image refusée par la modération', {
              description: result.description,
              contentType: 'mission',
              imageIndex: i
            });

            return false;
          }

          moderatedResults.push(file);
        } catch (error) {
          logger.error('Erreur lors de la modération de l\'image:', error);
          notify(`Erreur lors de la vérification de l'image ${i + 1}. Veuillez réessayer.`, 'error');
          setIsModerationModalOpen(false);
          setIsImageRejected(false);
          setRejectionDescription(undefined);
          return false;
        }
      }

      // Toutes les images ont été modérées avec succès
      setModeratedFiles(moderatedResults);
      setIsModerationModalOpen(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      return true;
    } catch (error) {
      logger.error('Erreur lors de la modération des images:', error);
      notify('Une erreur est survenue lors de la vérification des images', 'error');
      setIsModerationModalOpen(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      return false;
    }
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();

    if (isValid) {
      // Si on est à la dernière étape, on soumet le formulaire
      if (activeStep === steps.length - 1) {
        handleSubmit();
        return;
      }

      // Vérifier le budget maximum
      if (activeStep === 3 && formData.budget_defini && formData.budget > 999999) {
        notify('Le budget maximum autorisé est de 999 999', 'error');
        return;
      }

      // Si on est à l'étape des photos (5), modérer les images avant de passer à l'étape suivante
      if (activeStep === 5 && selectedFiles.length > 0) {
        const imagesAreValid = await moderateImages();
        if (!imagesAreValid) {
          return;
        }
      }

      const nextStep = activeStep + 1;

      // Puis changer d'étape
      setActiveStep(nextStep);

      // Faire défiler vers le haut après le changement d'étape
      scrollToTop();

      // On n'affiche pas le guide pour la première étape (catégorie) ni pour la dernière (résumé)
      if (nextStep > 0 && nextStep < steps.length - 1) {
        // Vérifie si l'utilisateur a choisi de ne plus voir le guide pour cette étape
        const stepGuideShown = getCookie(`stepGuideShown_${nextStep}`);
        if (!stepGuideShown) {
          setShowStepGuide(true);
        }
      }
    }
  };

  // Fonction dédiée au défilement vers le haut
  const scrollToTop = () => {
    try {
      // Faire défiler le conteneur de contenu
      if (scrollableRef.current) {
        scrollableRef.current.scrollTop = 0;
      }

      // Faire défiler le conteneur principal
      if (contentContainerRef.current) {
        contentContainerRef.current.scrollTop = 0;
      }

      // Faire défiler la fenêtre tout en haut
      window.scrollTo(0, 0);

    } catch (error) {
      logger.error('Erreur lors du défilement de la page:', error);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => Math.max(prevActiveStep - 1, 0));

    // Utiliser la fonction dédiée pour défiler vers le haut
    scrollToTop();
  };

  const validateCurrentStep = async () => {
    switch (activeStep) {
      case 0:
        if (!selectedCategory || !selectedSubcategory) {
          notify('Veuillez sélectionner une catégorie et un service', 'error');
          return false;
        }
        return true;
      case 1:
        if (!formData.titre) {
          notify('Veuillez saisir un titre pour votre mission', 'error');
          return false;
        }
        if (!formData.description || formData.description.replace(/<[^>]*>/g, '').length < 10) {
          notify('Veuillez saisir une description détaillée (minimum 10 caractères)', 'error');
          return false;
        }

        // Vérifier si la description dépasse la limite de caractères
        if (formData.description.replace(/<[^>]*>/g, '').length > 1200) {
          notify('Votre description dépasse la limite de 1200 caractères. Veuillez la raccourcir avant de continuer.', 'error');
          return false;
        }

        // Modération du contenu à l'étape de description
        try {
          setIsModerating(true);

          // Vérifier le titre
          const titleIsSafe = await validateContentSafety(
            formData.titre,
            'mission_title'
          );

          if (!titleIsSafe) {
            setIsModerating(false);
            return false;
          }

          // Nettoyer le HTML pour obtenir le texte brut pour la modération
          const descriptionText = formData.description.replace(/<[^>]*>/g, '');
          const descriptionIsSafe = await validateContentSafety(
            descriptionText,
            'mission_description'
          );

          if (!descriptionIsSafe) {
            setIsModerating(false);
            return false;
          }

          setIsModerating(false);
          return true;
        } catch (error) {
          logger.error('Erreur lors de la modération du contenu:', error);
          notify('Une erreur est survenue lors de la vérification du contenu', 'error');
          setIsModerating(false);
          return false;
        }

      case 2:
        if (!formData.ville) {
          notify('Veuillez saisir une ville', 'error');
          return false;
        }
        if (!formData.code_postal || !/^\d{5}$/.test(formData.code_postal)) {
          notify('Veuillez saisir un code postal valide (5 chiffres)', 'error');
          return false;
        }
        return true;
      case 3:
        if (formData.budget_defini && (!formData.budget || formData.budget <= 0)) {
          notify('Veuillez saisir un budget valide', 'error');
          return false;
        }
        if (!formData.payment_method) {
          notify('Veuillez sélectionner un mode de paiement', 'error');
          return false;
        }
        return true;
      case 4:
        if (hasSpecificTimeSlots && formData.time_slots.length === 0) {
          notify('Veuillez ajouter au moins un créneau horaire, ou décocher "Je définis mes créneaux horaires"', 'error');
          return false;
        }
        // Si on a des créneaux, on s'assure que has_time_preference est à true
        if (formData.time_slots.length > 0) {
          setFormData(prev => ({
            ...prev,
            has_time_preference: true
          }));
        }
        return true;
      case 5:
        // Les photos sont facultatives
        return true;
      case 6:
        // Étape de résumé, pas de validation nécessaire
        return true;
      default:
        return false;
    }
  };

  const handleCategoryExpand = (category: ServiceCategory) => {
    setSelectedCategory(category);
    setIsCategoryModalOpen(true);
  };

  const handleSubcategorySelect = (subcategory: ServiceSubcategory) => {
    setSelectedSubcategory(subcategory);
    setFormData({
      ...formData,
      category_id: selectedCategory?.id || '',
      subcategory_id: subcategory.id
    });
  };

  const handleSearchResultClick = (category: ServiceCategory, subcategory: ServiceSubcategory) => {
    // Mettre à jour le formData d'abord
    setFormData({
      ...formData,
      category_id: category.id,
      subcategory_id: subcategory.id
    });

    setSelectedCategory(category);
    setSelectedSubcategory(subcategory);
    setSearchQuery('');
    setIsCategoryModalOpen(false);
    // Passer directement à l'étape suivante
    setActiveStep((prevActiveStep) => Math.min(prevActiveStep + 1, steps.length - 1));
    // Faire défiler vers le haut
    scrollToTop();
  };

  const { createJobiNotification } = useCreateNotification();

  const handleSubmit = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const isValid = await validateCurrentStep();
      if (!isValid) {
        return;
      }

      // Créer une copie triée des créneaux
      const sortedTimeSlots = formData.time_slots
        .map(slot => ({
          date: slot.date,
          start: slot.start,
          end: slot.end
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // Vérifier la cohérence entre has_time_preference et time_slots
      // Si has_time_preference est true mais qu'il n'y a pas de créneaux, on le met à false
      const hasTimePreference = formData.has_time_preference && sortedTimeSlots.length > 0;

      const sanitizedData = {
        ...formData,
        category_id: selectedCategory?.id || '',
        subcategory_id: selectedSubcategory?.id || '',
        titre: DOMPurify.sanitize(formData.titre),
        description: DOMPurify.sanitize(formData.description),
        adresse: DOMPurify.sanitize(formData.adresse || ''),
        ville: DOMPurify.sanitize(formData.ville),
        budget: Number(formData.budget),
        budget_defini: Boolean(formData.budget_defini),
        time_slots: hasTimePreference ? sortedTimeSlots : [],
        has_time_preference: hasTimePreference,
        date_mission: hasTimePreference ? sortedTimeSlots[0]?.date || '' : ''
      } as const;

      const mission = await missionApi.createMission(sanitizedData);

      // Utiliser les fichiers modérés s'ils existent, sinon utiliser les fichiers sélectionnés
      const filesToUpload = moderatedFiles.length > 0 ? moderatedFiles : selectedFiles;

      if (filesToUpload.length > 0) {
        await missionApi.uploadMissionPhotos(mission.id, filesToUpload);
      }

      await handleMissionPublishSuccess();
      navigate('/dashboard/missions');
    } catch (error) {
      notify(error instanceof Error ? error.message : 'Une erreur est survenue lors de la publication', 'error');
      logger.error('Erreur lors de la création de la mission:', error);
      setIsSubmitting(false);
    }
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      // Ajuster la date pour tenir compte du timezone
      const adjustedDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
      const formattedDate = adjustedDate.toISOString().split('T')[0];
      setSelectedDate(formattedDate);
      setFormData(prev => ({
        ...prev,
        date_mission: formattedDate
      }));
    } else {
      setSelectedDate('');
      setFormData(prev => ({
        ...prev,
        date_mission: '',
      }));
    }
  };

  const addTimeSlot = () => {
    if (!selectedDate) {
      notify('Veuillez d\'abord sélectionner une date', 'error');
      return;
    }

    if (formData.time_slots.length >= 4) {
      notify('Vous ne pouvez pas ajouter plus de 4 créneaux', 'error');
      return;
    }

    const newTimeSlot = {
      date: selectedDate,
      start: currentTimeSlot.start,
      end: currentTimeSlot.end
    };

    const convertToMinutes = (time: string) => {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    };

    const newSlotStart = convertToMinutes(newTimeSlot.start);
    const newSlotEnd = convertToMinutes(newTimeSlot.end);

    // Vérifier que l'heure de fin est après l'heure de début
    if (newSlotEnd <= newSlotStart) {
      notify('L\'heure de fin doit être après l\'heure de début', 'error');
      return;
    }

    // Vérifier les chevauchements avec les créneaux existants
    const existingSlots = formData.time_slots.filter(slot => slot.date === newTimeSlot.date);
    let hasOverlap = false;

    for (const slot of existingSlots) {
      const existingStart = convertToMinutes(slot.start);
      const existingEnd = convertToMinutes(slot.end);

      if (
        (newSlotStart >= existingStart && newSlotStart < existingEnd) ||
        (newSlotEnd > existingStart && newSlotEnd <= existingEnd) ||
        (newSlotStart <= existingStart && newSlotEnd >= existingEnd)
      ) {
        hasOverlap = true;
        notify(`Ce créneau chevauche le créneau existant ${slot.start}-${slot.end}. Veuillez choisir un autre horaire.`, 'error');
        break;
      }
    }

    if (!hasOverlap) {
      // Créer une copie des créneaux existants
      const updatedTimeSlots = formData.time_slots.map(slot => ({
        date: slot.date,
        start: slot.start,
        end: slot.end
      }));

      // Ajouter le nouveau créneau
      updatedTimeSlots.push({
        date: selectedDate,
        start: currentTimeSlot.start,
        end: currentTimeSlot.end
      });

      // Trier les créneaux par date
      updatedTimeSlots.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
      });

      // Mettre à jour le formData
      setFormData(prev => ({
        ...prev,
        time_slots: updatedTimeSlots,
        date_mission: updatedTimeSlots[0].date
      }));

      // Réinitialiser la date sélectionnée
      setSelectedDate('');

      notify('Créneau ajouté avec succès', 'success');
    }
  };

  const removeTimeSlot = (index: number) => {
    const updatedTimeSlots = formData.time_slots.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      time_slots: updatedTimeSlots,
      date_mission: updatedTimeSlots.length > 0 ? updatedTimeSlots[0].date : ''
    }));
    notify('Créneau supprimé', 'success');
  };

  const renderCategoryModal = () => {
    if (!selectedCategory) return null;

    const subcategories = SERVICE_SUBCATEGORIES
      .filter(sub => sub.categoryId === selectedCategory.id)
      .sort((a, b) => a.nom.localeCompare(b.nom));

    return (
      <ModalPortal>
        <motion.div
          // initial={{ opacity: 0 }}
          // animate={{ opacity: 1 }}
          // exit={{ opacity: 0 }}
          className="fixed inset-0 flex items-center justify-center p-2 sm:p-4 z-[60]"
          onClick={() => setIsCategoryModalOpen(false)}
        >
          <div
            className="bg-[#FFF8F3] p-3 sm:p-4 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] relative max-h-[90vh] flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setIsCategoryModalOpen(false)}
              className="absolute top-2 right-2 sm:top-4 sm:right-4 text-[#FF6B2C] hover:text-[#FF965E] transition-colors duration-300 hover:scale-110 transform z-50"
            >
              <X sx={{ fontSize: { xs: 18, sm: 20 } }} />
            </button>

            <div className="flex-1 overflow-y-auto pr-1 sm:pr-2 custom-scrollbar">
              <div className="mb-4 sm:mb-8">
                <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-4">
                  <Box sx={{ color: '#FF6B2C', fontSize: { xs: '20px', sm: '24px' } }}>
                    {getIconComponent(selectedCategory.icon)}
                  </Box>
                  <Typography variant="h4" sx={{
                    color: '#FF6B2C',
                    fontWeight: 'bold',
                    fontSize: { xs: '1.25rem', sm: '1.5rem', md: '2rem' }
                  }}>
                    {selectedCategory.nom}
                  </Typography>
                </div>
                <Typography variant="body1" component="div" sx={{
                  color: 'rgba(0, 0, 0, 0.7)',
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                }}>
                  {selectedCategory.description}
                </Typography>
              </div>

              <div className="bg-white p-3 sm:p-4 md:p-6 rounded-xl shadow-md">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-6">
                  <Tag className="h-5 w-5 sm:h-6 sm:w-6 text-[#FF6B2C]" />
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Sous-catégories disponibles</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-4">
                  {subcategories.map((subcategory) => (
                    <motion.div
                      key={subcategory.id}
                      // initial={{ opacity: 0, y: 20 }}
                      // animate={{ opacity: 1, y: 0 }}
                      // transition={{ duration: 0.3 }}
                    >
                      <SubcategoryCard
                        elevation={0}
                        onClick={() => handleSubcategorySelect(subcategory)}
                        className={selectedSubcategory?.id === subcategory.id ? 'selected' : ''}
                        sx={{
                          padding: { xs: '16px', sm: '20px', md: '24px' }
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Box sx={{
                            color: selectedSubcategory?.id === subcategory.id ? '#FFFFFF' : '#FF6B2C',
                            fontSize: { xs: '18px', sm: '20px', md: '24px' },
                            transform: 'translateY(-5px)'
                          }}>
                            {getIconComponent(selectedCategory.icon)}
                          </Box>
                          <SubcategoryTitle className="subcategory-title" sx={{
                            fontSize: { xs: '0.9rem', sm: '1rem', md: '1.125rem' },
                            paddingLeft: { xs: '16px', sm: '20px', md: '24px' }
                          }}>
                            {subcategory.nom}
                          </SubcategoryTitle>
                        </Box>
                        <SubcategoryDescription className="subcategory-description" sx={{
                          fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.9rem' },
                          paddingLeft: { xs: '16px', sm: '20px', md: '24px' }
                        }}>
                          {subcategory.description}
                        </SubcategoryDescription>
                        <SelectionIndicator
                          className="selection-indicator"
                          sx={{
                            top: '8px',
                            right: '8px',
                            width: { xs: '20px', sm: '22px', md: '24px' },
                            height: { xs: '20px', sm: '22px', md: '24px' },
                            '& svg': {
                              fontSize: { xs: 14, sm: 16 }
                            }
                          }}
                        >
                          <Check />
                        </SelectionIndicator>
                      </SubcategoryCard>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 left-0 right-0 mt-3 sm:mt-6 pt-2 sm:pt-4 border-t border-gray-200 bg-[#FFF8F3] flex justify-end gap-2 sm:gap-3">
              <Button
                variant="outlined"
                onClick={() => setIsCategoryModalOpen(false)}
                startIcon={<X sx={{ fontSize: { xs: 14, sm: 16 } }} />}
                sx={{
                  color: '#FF6B2C',
                  borderColor: '#FF6B2C',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  padding: { xs: '4px 10px', sm: '6px 16px' },
                  '&:hover': {
                    borderColor: '#FF7A35',
                  },
                }}
              >
                Fermer
              </Button>
              <Button
                variant="contained"
                onClick={() => {
                  if (selectedSubcategory) {
                    setIsCategoryModalOpen(false);
                    // Attendre que la modal soit fermée avant de passer à l'étape suivante
                    setTimeout(() => {
                      handleNext();
                    }, 100);
                  } else {
                    notify('Veuillez sélectionner une sous-catégorie', 'error');
                  }
                }}
                endIcon={<ArrowRight sx={{ fontSize: { xs: 14, sm: 16 } }} />}
                disabled={!selectedSubcategory}
                sx={{
                  backgroundColor: '#FF6B2C',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  padding: { xs: '4px 10px', sm: '6px 16px' },
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(255, 107, 44, 0.3)',
                  },
                }}
              >
                Suivant
              </Button>
            </div>
          </div>
        </motion.div>
      </ModalPortal>
    );
  };

  const renderCategoryStep = () => {
    const sortedCategories = [...(searchQuery ? filteredCategories : SERVICE_CATEGORIES)]
      .sort((a, b) => a.nom.localeCompare(b.nom));

    const categoriesByLetter = sortedCategories.reduce((acc: { [key: string]: ServiceCategory[] }, category) => {
      const firstLetter = category.nom.charAt(0).toUpperCase();
      if (!acc[firstLetter]) {
        acc[firstLetter] = [];
      }
      acc[firstLetter].push(category);
      return acc;
    }, {});

    return (
      <MainContainer>
        <SearchContainer>
          <StyledSearchField
            fullWidth
            placeholder="Rechercher une catégorie ou sous-catégorie..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon className="h-5 w-5 text-gray-400" />
                </InputAdornment>
              ),
              endAdornment: searchQuery ? (
                <InputAdornment position="end">
                  <IconButton onClick={() => handleSearch('')} size="small">
                    <XCircle className="h-5 w-5" />
                  </IconButton>
                </InputAdornment>
              ) : null
            }}
          />

          {searchQuery && (
            <FilteredSubcategoriesContainer>
              <Typography variant="subtitle2" sx={{
                color: '#FF6B2C',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: '0.875rem',
                padding: '8px 12px',
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
                borderRadius: '8px',
                width: 'fit-content'
              }}>
                <Check sx={{ fontSize: 16 }} />
                {filteredSubcategories.length} résultat{filteredSubcategories.length > 1 ? 's' : ''} trouvé{filteredSubcategories.length > 1 ? 's' : ''}
              </Typography>
              <FilteredChipsContainer>
                {filteredSubcategories.map((subcategory) => {
                  const category = SERVICE_CATEGORIES.find(cat => cat.id === subcategory.categoryId) || null;
                  return (
                    <StyledChip
                      key={subcategory.id}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          {highlightText(subcategory.nom, searchQuery)}
                          <Typography
                            component="span"
                            className="category-label"
                            sx={{
                              fontSize: '0.75rem',
                              marginTop: '1px',
                              color: 'rgba(0, 0, 0, 0.6)',
                              ml: 0.5
                            }}
                          >
                            (dans {category?.nom})
                          </Typography>
                        </Box>
                      }
                      onClick={() => category && handleSearchResultClick(category, subcategory)}
                      className={selectedSubcategory?.id === subcategory.id ? 'Mui-selected' : ''}
                    />
                  );
                })}
              </FilteredChipsContainer>
            </FilteredSubcategoriesContainer>
          )}
        </SearchContainer>

        {Object.entries(categoriesByLetter).map(([letter, categories]) => (
          <Box key={letter} mb={6}>
            <Typography variant="h6" color="textSecondary" sx={{
              mb: 3,
              borderBottom: '2px solid #FF6B2C',
              width: 'fit-content',
              paddingBottom: 1
            }}>
              {letter}
            </Typography>
            <CategoryGridContainer>
              {categories.map((category) => (
                <CategoryCard
                  key={category.id}
                  id={`category-${category.id}`}
                  elevation={0}
                  onClick={() => handleCategoryExpand(category)}
                  className={selectedCategory?.id === category.id ? 'selected' : ''}
                >
                  <CategoryImageContainer>
                    <img
                      src={category.image.webp}
                      alt={category.image.alt}
                      className="category-image"
                    />
                    <CategoryOverlay className="category-overlay">
                      <CategoryContent className="category-content">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Box sx={{ color: '#FFFFFF', fontSize: '24px', transform: 'translateY(-5px)' }}>
                            {getIconComponent(category.icon)}
                          </Box>
                          <Typography variant="h5">{category.nom}</Typography>
                        </Box>
                        <Typography variant="body1" component="div">{category.description}</Typography>
                      </CategoryContent>
                    </CategoryOverlay>
                    <SelectionIndicator className="selection-indicator">
                      <Check />
                    </SelectionIndicator>
                  </CategoryImageContainer>
                </CategoryCard>
              ))}
            </CategoryGridContainer>
          </Box>
        ))}

        {isCategoryModalOpen && renderCategoryModal()}
      </MainContainer>
    );
  };

  const renderDescriptionStep = () => (
    <FormContainer>
      <FormSection>
        <FormTitle>
          Détails de votre mission
        </FormTitle>
        <StyledMissionField
          fullWidth
          label="Titre de la mission"
          value={formData.titre}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            titre: e.target.value.charAt(0).toUpperCase() + e.target.value.slice(1).toLowerCase()
          }))}
          helperText={`Soyez concis et précis (${70 - (formData.titre?.length || 0)} caractères restants, 5 requis minimum)`}
          inputProps={{ maxLength: 70 }}
        />
      </FormSection>

      <FormSection>
        <Box sx={{
          backgroundColor: '#FFF8F3',
          border: '2px solid transparent',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
        }}>
          <Box sx={{ position: 'relative' }}>
            {/* Bouton IA responsive - affiché au-dessus de l'éditeur sur mobile */}
            <Box sx={{
              display: { xs: 'block', md: 'none' },
              mb: 2
            }}>
              <button
                onClick={handleGenerateWithAi}
                disabled={isModerating}
                className={`flex items-center gap-2 px-4 py-2 bg-[#FFF8F3] text-[#FF6B2C] rounded-lg hover:bg-[#FFE4BA] transition-colors w-full justify-center ${isModerating ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <Sparkles className="h-4 w-4" />
                {stripHtml(formData.description).length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
                <Tooltip title={
                  stripHtml(formData.description).length > 0
                    ? "Améliorer le contenu existant avec l'IA"
                    : "Générer du nouveau contenu avec l'IA"
                }>
                  <HelpCircle className="h-4 w-4 ml-1" />
                </Tooltip>
              </button>
            </Box>

            <TiptapEditor
              ref={editorRef}
              content={formData.description}
              onChange={(content) => {
                const cleanContent = content.replace(/<[^>]*>/g, '');
                setDescriptionLength(cleanContent.length);
                // Toujours mettre à jour le contenu, même si on dépasse la limite
                setFormData(prev => ({
                  ...prev,
                  description: content
                }));
              }}
              className="post-mission-editor"
              placeholder="Décrivez précisément votre mission : tâches à effectuer, matériel nécessaire, compétences requises, contraintes particulières (accès, animaux présents...), état actuel de la situation..."
            />

            {/* Bouton IA desktop - positionné en absolu sur desktop uniquement */}
            <Box sx={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              zIndex: 10,
              display: { xs: 'none', md: 'block' }
            }}>
              <button
                onClick={handleGenerateWithAi}
                disabled={isModerating}
                className={`flex items-center gap-2 px-4 py-2 bg-[#FFF8F3] text-[#FF6B2C] rounded-lg hover:bg-[#FFE4BA] transition-colors whitespace-nowrap ${isModerating ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <Sparkles className="h-4 w-4" />
                {stripHtml(formData.description).length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
                <Tooltip title={
                  stripHtml(formData.description).length > 0
                    ? "Améliorer le contenu existant avec l'IA"
                    : "Générer du nouveau contenu avec l'IA"
                }>
                  <HelpCircle className="h-4 w-4 ml-1" />
                </Tooltip>
              </button>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                color: descriptionLength > 1200 ? '#FF6B2C' : '#666',
                fontStyle: 'italic',
                '&::before': {
                  content: '"💡"',
                  marginRight: '6px',
                }
              }}
            >
              {`${1200 - descriptionLength} caractères restants, 100 requis minimum`}
            </Typography>
            {descriptionLength > 1200 && (
              <Typography
                variant="caption"
                sx={{
                  display: 'block',
                  color: '#FF6B2C',
                  fontWeight: 'bold',
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 0.6 },
                    '50%': { opacity: 1 },
                    '100%': { opacity: 0.6 }
                  }
                }}
              >
                Limite dépassée, le bouton Suivant est désactivé
              </Typography>
            )}
          </Box>
        </Box>
      </FormSection>
    </FormContainer>
  );

  const renderLocationStep = () => (
    <Box>
      {/* Message de confidentialité sur l'adresse */}
      <Alert
        severity="info"
        sx={{
          mb: 2,
          backgroundColor: '#FFF8F3',
          border: '1px solid #FFE4BA',
          '& .MuiAlert-icon': {
            color: '#FF6B2C',
          },
        }}
      >
        <AlertTitle>Confidentialité</AlertTitle>
        Votre adresse complète ne sera jamais partagée sans votre consentement. Seules la ville et le code postal seront visibles par les jobbeurs.
      </Alert>

      <Box sx={{ height: '400px', width: '100%', borderRadius: '8px', overflow: 'hidden', mb: 3 }}>
        <InterventionZoneMap
          center={formData.intervention_zone?.center || [46.603354, 1.888334]}
          radius={formData.intervention_zone?.radius || 1000}
          onZoneChange={(zone) => {
            handleInterventionZoneChange(zone);
          }}
          onAddressChange={(address) => {
            setFormData(prev => ({
              ...prev,
              adresse: address.adresse,
              code_postal: address.code_postal,
              ville: address.ville,
              pays: address.pays
            }));
          }}
        />
      </Box>

      <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={2}>
        <StyledMissionField
          fullWidth
          label="Adresse"
          value={formData.adresse}
          disabled
          helperText="L'adresse est automatiquement mise à jour via la carte"
        />
        <StyledMissionField
          fullWidth
          label="Code postal"
          value={formData.code_postal}
          disabled
          helperText="Le code postal est automatiquement mis à jour via la carte"
        />
        <StyledMissionField
          fullWidth
          label="Ville"
          value={formData.ville}
          disabled
          helperText="La ville est automatiquement mise à jour via la carte"
        />
      </Box>
    </Box>
  );

  const JobiIcon = () => (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" style={{ width: 20, height: 20, color: '#FF6B2C' }}>
      <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
      <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
      <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
      <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
    </svg>
  );

  const TimeSlotCard = styled(Paper)(() => ({
    backgroundColor: '#FFF8F3',
    borderRadius: '16px',
    padding: '16px',
    marginBottom: '16px',
    border: '2px solid transparent',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 16px -4px rgba(255, 107, 44, 0.15)',
      borderColor: '#FF965E',
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '4px',
      height: '100%',
      backgroundColor: '#FF6B2C',
      borderRadius: '4px 0 0 4px',
    },
  }));

  const UrgentOptionBox = styled(Box)(() => ({
    backgroundColor: '#FFF8F3',
    borderRadius: '16px',
    padding: '24px',
    marginTop: '24px',
    marginBottom: '24px',
    border: '2px solid transparent',
    transition: 'all 0.3s ease',
    position: 'relative',
    '&:hover': {
      borderColor: '#FF965E',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 16px -4px rgba(255, 107, 44, 0.15)',
    },
  }));

  const renderBudgetStep = () => (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" sx={{
        mb: 3,
        color: '#FF6B2C',
        fontWeight: 600,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <EuroIcon /> Budget et mode de paiement
      </Typography>

      {/* Information sur les Jobi */}
      <Paper sx={{
        p: 3,
        mb: 4,
        border: '1px solid #FFE4BA',
        backgroundColor: '#FFF8F3',
      }}>
        <Typography variant="subtitle1" sx={{ color: '#FF6B2C', fontWeight: 600, mb: 2 }}>
          Qu'est-ce que les Jobi ?
        </Typography>
        <Typography variant="body2" sx={{ mb: 2 }}>
          Les Jobi sont le système d'échange/troc de JobPartiel, offrant plusieurs avantages :
        </Typography>
        <Box sx={{ ml: 2, mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • Protection de vos échanges en cas de désistement ou de travail insatisfaisant
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • Possibilité d'échanger des services entre membres (ex: jardinage contre bricolage)
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            • Système de fidélité avec des Jobi bonus pour les membres actifs
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#666', mb: 2 }}>
          Exemple 1 : Marc propose du jardinage contre 50 Jobi. Julie accepte et utilise ces Jobi pour faire réparer son ordinateur par Thomas.
        </Typography>
        <Typography variant="body2" sx={{ fontStyle: 'italic', color: '#666' }}>
          Exemple 2 : Sophie accumule des Jobi en donnant des cours de piano. Elle les utilise ensuite pour faire repeindre son salon par un professionnel, économisant ainsi sur le coût des travaux.
        </Typography>
      </Paper>

      {/* Choix du mode de paiement */}
      <Typography variant="subtitle1" sx={{ mb: 2, color: '#FF6B2C' }}>Mode de paiement préféré</Typography>
      <Box sx={{ mb: 4 }}>
        <RadioGroup
          value={formData.payment_method || 'jobi_only'}
          onChange={(e) => setFormData(prev => ({ ...prev, payment_method: e.target.value as PaymentMethod }))}
        >
          <FormControlLabel
            value="jobi_only"
            control={
              <Radio
                sx={{
                  '&.Mui-checked': {
                    color: '#FF6B2C',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body1" component="div">Troc/échange en Jobi uniquement (sans paiement)</Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  Troc/échange sécurisée via la plateforme avec protection maximale
                </Typography>
              </Box>
            }
            sx={{ mb: 2 }} // Marge inférieure ajoutée
          />
          <FormControlLabel
            value="direct_only"
            control={
              <Radio
                sx={{
                  '&.Mui-checked': {
                    color: '#FF6B2C',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body1" component="div">Paiement avec le jobbeur directement (chèque, virement, carte bancaire, etc.)</Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  Préfère un paiement en direct avec le jobbeur (sans protection plateforme)
                </Typography>
              </Box>
            }
            sx={{ mb: 2 }}
          />
          <FormControlLabel
            value="both"
            control={
              <Radio
                sx={{
                  '&.Mui-checked': {
                    color: '#FF6B2C',
                  },
                }}
              />
            }
            label={
              <Box>
                <Typography variant="body1" component="div">Hybride - Jobi et/ou paiement</Typography>
                <Typography variant="body2" sx={{ color: '#666' }}>
                  Accepte le troc/échange en Jobi ou/et le paiement avec le jobbeur
                </Typography>
              </Box>
            }
            sx={{ mb: 2 }}
          />
        </RadioGroup>
      </Box>

      {/* Budget */}
      <Typography variant="subtitle1" sx={{ mb: 2, color: '#FF6B2C' }}>Budget de la mission</Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Switch
          checked={!formData.budget_defini}
          onChange={(e) => setFormData(prev => ({ ...prev, budget_defini: !e.target.checked }))}
          sx={{
            '& .MuiSwitch-switchBase.Mui-checked': {
              color: '#FF6B2C',
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
              },
            },
            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
              backgroundColor: '#FF6B2C',
            },
          }}
        />
        <Typography sx={{ ml: 1 }}>
          {!formData.budget_defini
            ? "Aucun budget fixé – discutez du tarif avec les jobbeurs"
            : "Votre budget maximum est défini – vous pouvez discuter du tarif avec les jobbeurs"}
        </Typography>
      </Box>

      {formData.budget_defini && (
        <TextField
          fullWidth
          type="number"
          label={formData.payment_method === 'jobi_only' ? "Budget maximum (en Jobi)" : "Budget maximum"}
          value={formData.budget || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, budget: Number(e.target.value) }))}
          helperText="Les jobbeurs vous proposeront leurs tarifs dans la limite de ce budget"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                {formData.payment_method === 'jobi_only' ? (
                  <JobiIcon />
                ) : (
                  <EuroIcon sx={{ color: '#FF6B2C' }} />
                )}
              </InputAdornment>
            ),
          }}
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              '&:hover fieldset': {
                borderColor: '#FF965E',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#FF6B2C',
              },
            },
            '& .MuiInputLabel-root.Mui-focused': {
              color: '#FF6B2C',
            },
          }}
        />
      )}

      <Alert
        severity="info"
        sx={{
          mb: 2,
          '& .MuiAlert-icon': {
            color: '#FF6B2C',
          },
        }}
      >
        <AlertTitle>Note importante</AlertTitle>
        {formData.budget_defini ? (
          "Les jobbeurs essaieront de vous proposer leurs services dans la limite de votre budget maximum."
        ) : (
          "Les jobbeurs vous proposeront leurs tarifs en fonction de la complexité de la mission."
        )}
        {formData.payment_method === 'direct_only' && (
          <Typography variant="body2" sx={{ mt: 1, color: 'red', fontSize: 12, fontWeight: 600 }}>
            Attention : En sélectionnant le paiement direct uniquement, vous n'êtes pas couvert par la garantie sécurisée de la plateforme.
          </Typography>
        )}
        {formData.payment_method === 'both' && (
          <Typography variant="body2" sx={{ mt: 1, color: 'red', fontSize: 12, fontWeight: 600 }}>
            Attention : Vous avez opté pour le mode hybride. Notez que, dans ce cas, le paiement direct ne bénéficie pas de la protection offerte par la plateforme.
          </Typography>
        )}
      </Alert>
    </Box>
  );

  const renderTimeStep = () => (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" sx={{
        mb: 3,
        color: '#FF6B2C',
        fontWeight: 600,
        display: 'flex',
        alignItems: 'center',
        gap: 1
      }}>
        <AccessTime /> Choix des créneaux horaires
      </Typography>

      <Box sx={{ mb: 3 }}>
        <Typography variant="body1" sx={{ mb: 2, color: '#666', fontWeight: 500 }}>
          Comment souhaitez-vous gérer les horaires ?
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
          {/* Option: Avec préférence horaire */}
          <Box
            onClick={() => {
              setHasSpecificTimeSlots(true);
              setFormData(prev => ({
                ...prev,
                has_time_preference: true
              }));
            }}
            sx={{
              flex: 1,
              padding: '20px',
              borderRadius: '12px',
              border: hasSpecificTimeSlots ? '2px solid #FF6B2C' : '2px solid #E0E0E0',
              backgroundColor: hasSpecificTimeSlots ? 'rgba(255, 107, 44, 0.08)' : '#FAFAFA',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative',
              '&:hover': {
                borderColor: hasSpecificTimeSlots ? '#FF6B2C' : '#FF6B2C',
                backgroundColor: hasSpecificTimeSlots ? 'rgba(255, 107, 44, 0.12)' : 'rgba(255, 107, 44, 0.04)',
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              }
            }}
          >
            {hasSpecificTimeSlots && (
              <Box sx={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: '#FF6B2C',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Typography sx={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>✓</Typography>
              </Box>
            )}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AccessTime sx={{ color: hasSpecificTimeSlots ? '#FF6B2C' : '#666', mr: 1 }} />
              <Typography variant="subtitle1" sx={{
                fontWeight: 600,
                color: hasSpecificTimeSlots ? '#FF6B2C' : '#333'
              }}>
                Avec préférence horaire
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ color: '#666', lineHeight: 1.4 }}>
              Je définis mes créneaux horaires disponibles
            </Typography>
          </Box>

          {/* Option: Sans préférence horaire */}
          <Box
            onClick={() => {
              setHasSpecificTimeSlots(false);
              setFormData(prev => ({
                ...prev,
                date_mission: '',
                time_slots: [],
                has_time_preference: false
              }));
            }}
            sx={{
              flex: 1,
              padding: '20px',
              borderRadius: '12px',
              border: !hasSpecificTimeSlots ? '2px solid #FF6B2C' : '2px solid #E0E0E0',
              backgroundColor: !hasSpecificTimeSlots ? 'rgba(255, 107, 44, 0.08)' : '#FAFAFA',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative',
              '&:hover': {
                borderColor: !hasSpecificTimeSlots ? '#FF6B2C' : '#FF6B2C',
                backgroundColor: !hasSpecificTimeSlots ? 'rgba(255, 107, 44, 0.12)' : 'rgba(255, 107, 44, 0.04)',
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              }
            }}
          >
            {!hasSpecificTimeSlots && (
              <Box sx={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: '#FF6B2C',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Typography sx={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>✓</Typography>
              </Box>
            )}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <AccessTime sx={{ color: !hasSpecificTimeSlots ? '#FF6B2C' : '#666', mr: 1 }} />
              <Typography variant="subtitle1" sx={{
                fontWeight: 600,
                color: !hasSpecificTimeSlots ? '#FF6B2C' : '#333'
              }}>
                Sans préférence horaire
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ color: '#666', lineHeight: 1.4 }}>
              Les jobbeurs me proposeront leurs disponibilités
            </Typography>
          </Box>
        </Box>
      </Box>

      {hasSpecificTimeSlots && (
        <>
          <StyledDatePickerContainer>
            <DatePicker
              selected={selectedDate ? new Date(selectedDate) : null}
              onChange={handleDateChange}
              dateFormat="dd/MM/yyyy"
              placeholderText="Cliquer ici pour ajouter un créneau"
              className="datepicker-input"
              calendarClassName="custom-datepicker"
              minDate={new Date()}
              shouldCloseOnSelect={true}
              disabledKeyboardNavigation
              locale="fr"
              todayButton="Aujourd'hui"
            />
          </StyledDatePickerContainer>

          {/* N'afficher les sélecteurs d'heures que si une date est sélectionnée */}
          {selectedDate && (
            <motion.div
              // initial={{ opacity: 0, y: -10 }}
              // animate={{ opacity: 1, y: 0 }}
              // transition={{ duration: 0.3 }}
            >
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                mb: 3,
                mt: 2,
                '& .MuiFormControl-root': {
                  flex: 1,
                },
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '-10px',
                  left: '20px',
                  width: '20px',
                  height: '20px',
                  backgroundColor: '#FFF8F3',
                  transform: 'rotate(45deg)',
                  borderLeft: '1px solid #FFE4BA',
                  borderTop: '1px solid #FFE4BA',
                  zIndex: 0,
                },
                backgroundColor: '#FFF8F3',
                padding: '20px',
                borderRadius: '12px',
                border: '1px solid #FFE4BA',
              }}>
                <CustomTimeSelect
                  label="Heure de début"
                  value={currentTimeSlot.start}
                  onChange={(value) => {
                    setCurrentTimeSlot(prev => ({
                      ...prev,
                      start: value
                    }));
                  }}
                />
                <CustomTimeSelect
                  label="Heure de fin"
                  value={currentTimeSlot.end}
                  onChange={(value) => {
                    setCurrentTimeSlot(prev => ({
                      ...prev,
                      end: value
                    }));
                  }}
                />
              </Box>
            </motion.div>
          )}

          {/* Afficher le bouton uniquement si une date et des horaires sont sélectionnés */}
          {selectedDate && (
            <Box
              sx={{
                position: 'relative',
                animation: formData.time_slots.length === 0 ? 'pulse 3s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': {
                    transform: 'scale(1)',
                    boxShadow: '0 0 0 0 rgba(255, 107, 44, 0.4)',
                  },
                  '70%': {
                    transform: 'scale(1.02)',
                    boxShadow: '0 0 0 10px rgba(255, 107, 44, 0)',
                  },
                  '100%': {
                    transform: 'scale(1)',
                    boxShadow: '0 0 0 0 rgba(255, 107, 44, 0)',
                  },
                },
              }}
            >
              <Button
                variant="contained"
                onClick={addTimeSlot}
                startIcon={<Event />}
                disabled={formData.time_slots.length >= 4}
                sx={{
                  backgroundColor: '#FF6B2C',
                  borderRadius: '12px',
                  textTransform: 'none',
                  mb: 3,
            width: '100%',
                  padding: '12px',
                  fontSize: '1rem',
                  fontWeight: 600,
                  '&:hover': {
                    backgroundColor: '#FF965E',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.2)',
                  },
                  '&.Mui-disabled': {
                    backgroundColor: 'rgba(255, 107, 44, 0.3)',
                    color: 'white',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                {formData.time_slots.length >= 4 ? 'Nombre maximum de créneaux atteint' : 'Ajouter ce créneau'}
              </Button>
              {formData.time_slots.length < 4 && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: '60%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    color: '#FF6B2C',
                    fontSize: '1.375rem',
                    fontWeight: 500,
                    textAlign: 'center',
                    width: '100%',
                    backgroundColor: 'white',
                    mt: 1,
                    borderRadius: '12px',
                    padding: '10px',
                  }}
                >
                  {formData.time_slots.length === 0
                    ? '👆 Cliquez ici pour ajouter votre premier créneau'
                    : `👆 Cliquez ici pour ajouter un autre créneau (${4 - formData.time_slots.length} restant${4 - formData.time_slots.length > 1 ? 's' : ''})`
                  }
                </Box>
              )}
            </Box>
          )}
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 400, color: '#FF6B2C', fontSize: '1.0rem', paddingBottom: '10px' }}>
              Vos créneaux actuels (vous pouvez en supprimer ou en ajouter d'autres) :
            </Typography>
          </Box>

          {formData.time_slots.map((slot, index) => (
            <TimeSlotCard key={index} elevation={0}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Event sx={{ color: '#FF6B2C' }} />
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      {format(new Date(slot.date), 'EEEE d MMMM yyyy', { locale: fr })}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      {slot.start} - {slot.end}
                    </Typography>
                  </Box>
                </Box>
                <IconButton
                  onClick={() => removeTimeSlot(index)}
                  sx={{
                    color: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 44, 0.08)',
                    },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </TimeSlotCard>
          ))}
        </>
      )}

      {!hasSpecificTimeSlots && (
        <Alert
          severity="info"
          sx={{
            backgroundColor: 'rgba(255, 107, 44, 0.08)',
            borderRadius: '8px',
            mb: 3,
            '& .MuiAlert-icon': {
              color: '#FF6B2C',
            },
          }}
        >
          <AlertTitle>Flexibilité horaire</AlertTitle>
          Les jobbeurs pourront vous proposer leurs créneaux de disponibilité.
          Vous pourrez ensuite choisir celui qui vous convient le mieux.
        </Alert>
      )}

      <UrgentOptionBox>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Campaign sx={{ color: '#FF6B2C', fontSize: 28 }} />
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#2D3748' }}>
                Mission Urgente ?
              </Typography>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Augmentez la visibilité de votre mission
              </Typography>
            </Box>
          </Box>
          <Checkbox
            checked={formData.is_urgent}
            onChange={(e) => setFormData(prev => ({ ...prev, is_urgent: e.target.checked }))}
            sx={{
              color: '#FFE4BA',
              '&.Mui-checked': {
                color: '#FF6B2C',
              },
              '& .MuiSvgIcon-root': {
                fontSize: 28,
              },
              '&:hover': {
                backgroundColor: 'rgba(255, 107, 44, 0.08)',
              },
            }}
          />
        </Box>
        {formData.is_urgent && (
          <Alert
            severity="info"
            sx={{
              backgroundColor: 'rgba(255, 107, 44, 0.08)',
              borderRadius: '8px',
              '& .MuiAlert-icon': {
                color: '#FF6B2C',
              },
            }}
          >
            <AlertTitle>Avantages des missions urgentes</AlertTitle>
            <ul style={{ margin: '8px 0 0 20px', padding: 0 }}>
              <li>Mise en avant dans les recherches</li>
              <li>Notification prioritaire aux jobbeurs</li>
              <li>Badge "URGENT" sur votre mission</li>
            </ul>
          </Alert>
        )}
      </UrgentOptionBox>
    </Box>
  );

  const StyledImageList = styled(ImageList)(() => ({
    width: '100%',
    height: 'auto',
    gap: '8px !important',
    padding: '0',
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
    '& .add-photo-label': {
      gridColumn: '1 / -1',
      margin: '0 auto',
      width: '100%'
    },
    '@media (max-width: 600px)': {
      gap: '6px !important',
      gridTemplateColumns: 'repeat(2, 1fr) !important',
    }
  }));

  const StyledImageListItem = styled(ImageListItem)(() => ({
    overflow: 'hidden',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
    position: 'relative',
    aspectRatio: '1/1',
    '&:hover': {
      '& .delete-button': {
        opacity: 1,
      },
      '& .MuiImageListItemBar-root': {
        opacity: 1,
      }
    },
    '& img': {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
    },
    '& .MuiImageListItemBar-root': {
      background: 'linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent)',
      opacity: 0,
      transition: 'opacity 0.2s ease',
      height: '100%',
      top: 0,
    },
    '& .delete-button': {
      position: 'absolute',
      top: '4px',
      right: '4px',
      opacity: 0,
      transition: 'opacity 0.2s ease',
      backgroundColor: 'white',
      padding: '2px',
      minWidth: 'unset',
      borderRadius: '4px',
      '&:hover': {
        backgroundColor: '#f5f5f5',
      },
      '& svg': {
        fontSize: '16px',
        color: '#FF6B2C',
      }
    }
  }));

  const PhotoPlaceholder = styled(Box)(() => ({
    backgroundColor: '#fff',
    borderRadius: '12px',
    padding: '16px',
    textAlign: 'center',
    border: '2px dashed #FFE4BA',
    transition: 'all 0.2s ease',
    cursor: 'pointer',
    height: '140px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundSize: '2em 2em',
      opacity: 0.4,
      transition: 'opacity 0.2s ease',
    },
    '&:hover': {
      borderColor: '#FF6B2C',
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.1)',
      '&::before': {
        opacity: 0.6,
      },
      '& .upload-icon': {
        transform: 'scale(1.1) translateY(-2px)',
        color: '#FF6B2C',
      },
      '& .upload-text': {
        color: '#FF6B2C',
      },
      '& .browse-text': {
        backgroundColor: '#FF6B2C',
        color: 'white',
      }
    }
  }));

  const PhotoCounter = styled(Box)(() => ({
    display: 'inline-flex',
    alignItems: 'center',
    gap: '4px',
    padding: '4px 8px',
    backgroundColor: '#FFF8F3',
    borderRadius: '12px',
    border: '1px solid #FFE4BA',
    '& .MuiTypography-root': {
      fontSize: '0.75rem',
      color: '#666',
    },
    '& strong': {
      color: '#FF6B2C',
      fontWeight: 600,
    },
    '@media (max-width: 600px)': {
      flexDirection: 'row',
      alignItems: 'center',
      gap: '2px',
      padding: '2px 6px',
    }
  }));

  const renderPhotosStep = () => (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" sx={{ color: '#FF6B2C', fontSize: '1rem' }}>Photos de la mission</Typography>
        <PhotoCounter>
          <Typography variant="caption">
            <strong>{8 - previewUrls.length}</strong> ajout{8 - previewUrls.length > 1 ? 's' : ''} possible{8 - previewUrls.length > 1 ? 's' : ''}
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.7 }}>(facultatif)</Typography>
        </PhotoCounter>
      </Box>

      <Alert
        severity="info"
        sx={{
          mb: 2,
          '& .MuiAlert-icon': {
            color: '#FF6B2C',
            fontSize: '1rem',
          },
          backgroundColor: '#FFF8F3',
          border: '1px solid rgba(255, 107, 44, 0.2)',
          '& .MuiAlert-message': {
            fontSize: '0.8rem',
          }
        }}
      >
        <AlertTitle sx={{ fontSize: '0.9rem' }}>Conservation des photos</AlertTitle>
        <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
          Pour des raisons écologiques d'optimisation de l'espace de stockage, les photos seront automatiquement supprimées après 90 jours.
          Vous recevrez une notification par email lors de la suppression.
        </Typography>
      </Alert>

      <input
        type="file"
        accept="image/jpeg,image/png,image/webp"
        multiple
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        id="photo-upload"
      />

      {previewUrls.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'stretch',
            gap: { xs: 2, md: 3 },
            width: '100%',
          }}
        >
          <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <label htmlFor="photo-upload" className="add-photo-label" style={{ width: '100%' }}>
              <PhotoPlaceholder sx={{ height: '140px', width: '100%' }}>
                <UploadIcon
                  className="upload-icon"
                  sx={{
                    fontSize: 24,
                    color: '#FF965E',
                    mb: 1,
                    transition: 'all 0.2s ease',
                  }}
                />
                <Typography
                  variant="body2"
                  className="upload-text"
                  sx={{
                    color: '#2D3748',
                    mb: 0.5,
                    fontWeight: 500,
                    fontSize: '0.9rem',
                    transition: 'color 0.2s ease',
                  }}
                >
                  Déposez vos photos ici
                </Typography>
                <Typography
                  variant="body2"
                  className="browse-text"
                  sx={{
                    color: '#666',
                    backgroundColor: '#FFF8F3',
                    padding: '2px 8px',
                    borderRadius: '8px',
                    fontSize: '0.8rem',
                    transition: 'all 0.2s ease',
                  }}
                >
                  ou parcourir
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#666',
                    mt: 1,
                    fontSize: '0.7rem',
                    fontStyle: 'italic'
                  }}
                >
                  Formats acceptés : JPG, PNG, WEBP • Maximum 8 photos
                </Typography>
              </PhotoPlaceholder>
            </label>
          </Box>
          <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Box sx={{ width: '100%' }}>
              <AiMissionImageGenerator
                onImageGenerated={(imageUrl, imageBase64) => {
                  setPreviewUrls(prev => [...prev, imageUrl]);
                  fetch(imageUrl)
                    .then(res => res.blob())
                    .then(blob => {
                      const file = new File([blob], `mission-image-${Date.now()}.jpg`, { type: 'image/jpeg' });
                      setSelectedFiles(prev => [...prev, file]);
                    })
                    .catch(error => {
                      logger.error('Erreur lors de la conversion de l\'image générée:', error);
                      notify('Erreur lors de l\'ajout de l\'image générée', 'error');
                    });
                }}
                missionTitle={formData.titre}
                missionDescription={formData.description}
                isCover={false}
                className="h-[140px] w-full"
              />
            </Box>
          </Box>
        </Box>
      ) : (
        <>
          <StyledImageList cols={4}>
            {previewUrls.map((url, index) => (
              <StyledImageListItem key={index}>
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  loading="lazy"
                />
                <ImageListItemBar
                  actionIcon={
                    <IconButton
                      className="delete-button"
                      onClick={() => handleRemoveFile(index)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  }
                />
              </StyledImageListItem>
            ))}
          </StyledImageList>
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              alignItems: 'stretch',
              gap: { xs: 2, md: 3 },
              width: '100%',
              mt: 2
            }}
          >
            <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {previewUrls.length < 8 && (
                <label htmlFor="photo-upload" className="add-photo-label" style={{ width: '100%' }}>
                  <PhotoPlaceholder sx={{ height: '140px', width: '100%' }}>
                    <UploadIcon
                      className="upload-icon"
                      sx={{
                        fontSize: 24,
                        color: '#FF965E',
                        mb: 1,
                        transition: 'all 0.2s ease',
                      }}
                    />
                    <Typography
                      variant="body2"
                      className="upload-text"
                      sx={{
                        color: '#2D3748',
                        mb: 0.5,
                        fontWeight: 500,
                        fontSize: '0.9rem',
                        transition: 'color 0.2s ease',
                      }}
                    >
                      Déposez vos photos ici
                    </Typography>
                    <Typography
                      variant="body2"
                      className="browse-text"
                      sx={{
                        color: '#666',
                        backgroundColor: '#FFF8F3',
                        padding: '2px 8px',
                        borderRadius: '8px',
                        fontSize: '0.8rem',
                        transition: 'all 0.2s ease',
                      }}
                    >
                      ou parcourir
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#666',
                        mt: 1,
                        fontSize: '0.7rem',
                        fontStyle: 'italic'
                      }}
                    >
                      Formats acceptés : JPG, PNG, WEBP • Maximum 8 photos
                    </Typography>
                  </PhotoPlaceholder>
                </label>
              )}
            </Box>
            <Box sx={{ flex: 1, minWidth: 0, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box sx={{ width: '100%' }}>
                <AiMissionImageGenerator
                  onImageGenerated={(imageUrl, imageBase64) => {
                    setPreviewUrls(prev => [...prev, imageUrl]);
                    fetch(imageUrl)
                      .then(res => res.blob())
                      .then(blob => {
                        const file = new File([blob], `mission-image-${Date.now()}.jpg`, { type: 'image/jpeg' });
                        setSelectedFiles(prev => [...prev, file]);
                      })
                      .catch(error => {
                        logger.error('Erreur lors de la conversion de l\'image générée:', error);
                        notify('Erreur lors de l\'ajout de l\'image générée', 'error');
                      });
                  }}
                  missionTitle={formData.titre}
                  missionDescription={formData.description}
                  isCover={false}
                  className="h-[140px] w-full"
                />
              </Box>
            </Box>
          </Box>
        </>
      )}
    </Box>
  );

  const SummaryCard = styled(Box)(() => ({
    backgroundColor: '#FFF8F3',
    borderRadius: '24px',
    padding: '32px',
    marginBottom: '24px',
    border: '2px solid rgba(255, 107, 44, 0.1)',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 12px 32px rgba(255, 107, 44, 0.12)',
      borderColor: '#FF965E',
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '6px',
      height: '100%',
      background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)',
      borderRadius: '4px 0 0 4px',
    },
    '@media (max-width: 600px)': {
      padding: '0px',
  }
}));

  const SummarySection = styled(Box)(() => ({
    marginBottom: '32px',
    padding: '24px',
    backgroundColor: '#FFFFFF',
    borderRadius: '16px',
    border: '1px solid rgba(255, 107, 44, 0.1)',
    transition: 'all 0.3s ease',
    '&:last-child': {
      marginBottom: 0,
    },
    '&:hover': {
      borderColor: '#FF965E',
      boxShadow: '0 8px 24px rgba(255, 107, 44, 0.08)',
    },
  }));

  const SummaryTitle = styled(Typography)(() => ({
    color: '#2D3748',
    fontWeight: 600,
    fontSize: '1rem',
    marginBottom: '16px',
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    position: 'relative',
    paddingBottom: '12px',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      width: '40px',
      height: '3px',
      backgroundColor: '#FF6B2C',
      borderRadius: '2px',
    },
    '& .icon': {
      color: '#FF6B2C',
      fontSize: '20px',
      backgroundColor: 'rgba(255, 107, 44, 0.1)',
      padding: '8px',
      borderRadius: '12px',
      transition: 'all 0.3s ease',
    },
    '&:hover .icon': {
      transform: 'scale(1.1) rotate(5deg)',
      backgroundColor: '#FF6B2C',
      color: '#FFFFFF',
    },
  }));

  const SummaryContent = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    gap: '8px',
    alignItems: 'center',
    '& .summary-item': {
      display: 'inline-flex',
      alignItems: 'center',
      gap: '8px',
      backgroundColor: '#FFF',
      padding: '6px 12px',
      borderRadius: '20px',
      border: '1px solid rgba(255, 107, 44, 0.1)',
      transition: 'all 0.2s ease',
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)',
        borderColor: '#FF965E',
      },
      '& .label': {
        color: '#666',
        fontSize: '0.85rem',
        fontWeight: 500,
        '&::after': {
          content: '":"',
          marginLeft: '2px',
          marginRight: '4px',
        },
      },
      '& .value': {
        color: '#FF6B2C',
        fontSize: '0.85rem',
        fontWeight: 600,
      },
    },
    '& .empty-message': {
      color: '#666',
      fontSize: '0.875rem',
      fontStyle: 'italic',
      padding: '8px 12px',
      backgroundColor: 'rgba(255, 107, 44, 0.05)',
      borderRadius: '8px',
    width: '100%',
    },
  }));

  const PhotoGrid = styled(ImageList)(() => ({
    width: '100%',
    height: 'auto',
    gap: '8px !important',
    gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr)) !important',
    '@media (max-width: 600px)': {
      gap: '6px !important',
      gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr)) !important',
    },
  }));

  const PhotoItem = styled(ImageListItem)(() => ({
    borderRadius: '12px',
    overflow: 'hidden',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    '&:hover': {
      transform: 'scale(1.02) translateY(-2px)',
      boxShadow: '0 8px 16px rgba(255, 107, 44, 0.15)',
      '& img': {
        transform: 'scale(1.05)',
      },
      '& .MuiImageListItemBar-root': {
        height: '100%',
        background: 'linear-gradient(to top, rgba(255, 107, 44, 0.9), rgba(255, 107, 44, 0.4))',
      },
      '& .photo-number': {
        transform: 'translateY(0)',
        opacity: 1,
      },
    },
    '& img': {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
      transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    },
    '& .MuiImageListItemBar-root': {
      background: 'linear-gradient(to top, rgba(255, 107, 44, 0.8), transparent)',
      transition: 'all 0.3s ease',
      height: '40%',
    },
    '& .photo-number': {
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, 20px)',
      color: '#FFFFFF',
      fontSize: '1.2rem',
      fontWeight: 600,
      opacity: 0,
      transition: 'all 0.3s ease',
      textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
      zIndex: 2,
    },
  }));

  const BudgetDisplay = styled(Box)(() => ({
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: '12px',
    backgroundColor: '#FFFFFF',
    padding: '16px 24px',
    borderRadius: '16px',
    border: '1px solid rgba(255, 107, 44, 0.1)',
    width: '100%',
    maxWidth: '100%',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 24px rgba(255, 107, 44, 0.12)',
      borderColor: '#FF965E',
    },
    '@media (max-width: 600px)': {
      flexDirection: 'column',
      alignItems: 'flex-start',
      padding: '12px 16px',
    },
  }));

  const BudgetAmount = styled(Typography)({
    color: '#FF6B2C',
    fontWeight: 700,
    fontSize: '1.5rem',
  });

  const BudgetCurrency = styled(Typography)({
    color: '#666',
    fontWeight: 500,
    fontSize: '1rem',
  });

  interface BudgetBadgeProps extends ChipProps {
    isFixed: boolean;
  }

  const BudgetBadge = styled(({ isFixed, ...props }: BudgetBadgeProps) => (
    <Chip {...props} />
  ))<BudgetBadgeProps>(({ isFixed }) => ({
    backgroundColor: isFixed ? '#FF6B2C' : '#FFE4BA',
    color: isFixed ? '#FFFFFF' : '#FF6B2C',
    fontWeight: 600,
    fontSize: '0.85rem',
    height: '28px',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: isFixed ? '#FF965E' : '#FFD4A0',
    },
  }));

  const renderSummaryStep = () => (
    <Box sx={{ mt: 2 }}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        mb: 4,
        backgroundColor: '#FFF8F3',
        padding: '20px 24px',
        borderRadius: '16px',
        border: '2px solid rgba(255, 107, 44, 0.1)',
        boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)',
      }}>
        <Box sx={{
          backgroundColor: '#FF6B2C',
          borderRadius: '12px',
          padding: '12px',
          color: '#FFFFFF',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Check sx={{ fontSize: 24 }} />
        </Box>
        <Box>
          <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
            Résumé de votre mission
          </Typography>
          <Typography variant="body2" sx={{ color: '#666' }}>
            Vérifiez que toutes les informations sont correctes avant de publier
          </Typography>
        </Box>
      </Box>

      {/* Message d'avertissement concernant les réponses sous 24h */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        mb: 4,
        backgroundColor: '#FFF8E0',
        padding: '20px 24px',
        borderRadius: '16px',
        border: '2px solid rgba(255, 193, 7, 0.2)',
        boxShadow: '0 4px 12px rgba(255, 193, 7, 0.08)',
      }}>
        <Box sx={{
          backgroundColor: '#FFC107',
          borderRadius: '12px',
          padding: '12px',
          color: '#FFFFFF',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Warning sx={{ fontSize: 24 }} />
        </Box>
        <Box>
          <Typography variant="h6" sx={{ color: '#2D3748', fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>
            Important : Délai de réponse
          </Typography>
          <Typography variant="body2" sx={{ color: '#666' }}>
            Toutes les offres doivent être répondues sous 24 heures maximum. Sans réponse, un avis négatif sera automatiquement publié sur votre profil.
          </Typography>
        </Box>
      </Box>

      <MissionTitle>
        {formData.titre}
      </MissionTitle>

      <SummaryCard>
        {/* Catégorie et service */}
        <SummarySection>
          <SummaryTitle>
            <Tag className="icon" />
            Catégorie et service
          </SummaryTitle>
          <SummaryContent>
            <strong>{selectedCategory?.nom}</strong> - {selectedSubcategory?.nom}
          </SummaryContent>
        </SummarySection>

        {/* Description */}
        <SummarySection>
          <SummaryTitle>
            <Description className="icon" />
            Description
          </SummaryTitle>
          <SummaryContent>
            <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(formData.description) }} />
          </SummaryContent>
        </SummarySection>

        {/* Localisation */}
        <SummarySection>
          <SummaryTitle>
            <LocationOn className="icon" />
            Localisation
          </SummaryTitle>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            backgroundColor: 'rgba(255, 107, 44, 0.05)',
            padding: '12px 20px',
            borderRadius: '12px',
            width: 'fit-content',
          }}>
            <LocationOn sx={{ color: '#FF6B2C', fontSize: '20px' }} />
            <SummaryContent>
              {formData.adresse && `${formData.adresse}, `}
              <strong>{formData.ville}</strong> ({formData.code_postal})
            </SummaryContent>
          </Box>
        </SummarySection>

        {/* Budget */}
        <SummarySection>
          <SummaryTitle>
            <Euro className="icon" />
            Budget
          </SummaryTitle>
          <BudgetDisplay>
            {formData.budget_defini ? (
              <>
                <BudgetAmount>{formData.budget}</BudgetAmount>
                <BudgetCurrency>
                  {formData.payment_method === 'jobi_only'
                    ? 'Jobi'
                    : formData.payment_method === 'direct_only'
                    ? '€'
                    : '€ ou Jobi'
                  }
                </BudgetCurrency>
                <BudgetBadge
                  label="Budget max"
                  isFixed={true}
                />
              </>
            ) : (
              <>
                <Typography
                  sx={{
                    color: '#FF6B2C',
                    fontWeight: 600,
                    fontSize: '1.0rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  <Chat sx={{ fontSize: 20 }} />
                  À discuter avec les jobbeurs
                </Typography>
                <BudgetBadge
                  label="Budget à définir"
                  isFixed={false}
                />
              </>
            )}
          </BudgetDisplay>
        </SummarySection>

        {/* Horaires */}
        <SummarySection>
          <SummaryTitle>
            <AccessTime className="icon" />
            Créneaux horaires
          </SummaryTitle>
          {hasSpecificTimeSlots ? (
            formData.time_slots.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {formData.time_slots.map((slot, index) => (
                  <Box
                    key={index}
                    sx={{
                      backgroundColor: 'rgba(255, 107, 44, 0.05)',
                      padding: '16px 20px',
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateX(8px)',
                        backgroundColor: 'rgba(255, 107, 44, 0.08)',
                      },
                    }}
                  >
                    <Event sx={{ color: '#FF6B2C', fontSize: '20px' }} />
                    <Box>
                      <Typography sx={{
                        fontWeight: 600,
                        color: '#2D3748',
                        fontSize: '0.95rem',
                      }}>
                        {format(new Date(slot.date), 'EEEE d MMMM yyyy', { locale: fr })}
                      </Typography>
                      <Typography sx={{
                        color: '#FF6B2C',
                        fontWeight: 500,
                        fontSize: '0.9rem',
                      }}>
                        {slot.start} - {slot.end}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            ) : (
              <Box sx={{
                backgroundColor: 'rgba(255, 107, 44, 0.05)',
                padding: '16px 20px',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              }}>
                <AccessTime sx={{ color: '#FF6B2C', opacity: 0.5, fontSize: '20px' }} />
                <Typography sx={{ color: '#666', fontStyle: 'italic', fontSize: '0.9rem' }}>
                  Aucun créneau horaire ajouté
                </Typography>
              </Box>
            )
          ) : (
            <Box sx={{
              backgroundColor: 'rgba(255, 107, 44, 0.05)',
              padding: '16px 20px',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
            }}>
              <AccessTime sx={{ color: '#FF6B2C', fontSize: '20px' }} />
              <Box>
                <Typography sx={{
                  color: '#FF6B2C',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                }}>
                  Pas de préférence horaire
                </Typography>
                <Typography sx={{
                  color: '#666',
                  fontSize: '0.85rem',
                }}>
                  Les jobbeurs vous proposeront leurs disponibilités
                </Typography>
              </Box>
            </Box>
          )}
        </SummarySection>

        {/* Photos */}
        {previewUrls.length > 0 && (
          <SummarySection>
            <SummaryTitle>
              <PhotoCamera className="icon" sx={{ fontSize: '1.2rem' }} />
              Photos ({previewUrls.length})
            </SummaryTitle>
            <PhotoGrid cols={3} rowHeight={160}>
              {previewUrls.map((url, index) => (
                <PhotoItem key={index}>
                  <img
                    src={url}
                    alt={`Photo ${index + 1}`}
                    loading="lazy"
                  />
                  <div className="photo-number">Photo {index + 1}</div>
                  <ImageListItemBar
                    title={`Photo ${index + 1}`}
                    position="bottom"
                    sx={{
                      '& .MuiImageListItemBar-title': {
                        fontSize: '0.75rem'
                      }
                    }}
                  />
                </PhotoItem>
              ))}
            </PhotoGrid>
          </SummarySection>
        )}
      </SummaryCard>
    </Box>
  );

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return renderCategoryStep();
      case 1:
        return renderDescriptionStep();
      case 2:
        return renderLocationStep();
      case 3:
        return renderBudgetStep();
      case 4:
        return renderTimeStep();
      case 5:
        return renderPhotosStep();
      case 6:
        return renderSummaryStep();
      default:
        return 'Étape inconnue';
    }
  };

  const getStepSummary = () => {
    const nextStep = activeStep < steps.length - 1 ? steps[activeStep + 1] : null;

    const getRelevantInfo = () => {
      const items = [];

      // Étape 0 : Catégorie (visible si remplie)
      if (selectedCategory) {
        items.push({ label: 'Catégorie', value: selectedCategory.nom });
      }
      if (selectedSubcategory) {
        items.push({ label: 'Service', value: selectedSubcategory.nom });
      }

      // Étape 1 : Description (visible si étape actuelle >= 1)
      if (activeStep >= 1) {
        if (formData.titre) {
          items.push({ label: 'Titre', value: formData.titre });
        }
        if (formData.budget > 0) {
          items.push({ label: 'Budget', value: `${formData.budget} €` });
        }
      }

      // Étape 2 : Localisation (visible si étape actuelle >= 2)
      if (activeStep >= 2) {
        if (formData.ville) {
          items.push({ label: 'Ville', value: formData.ville });
        }
        if (formData.code_postal) {
          items.push({ label: 'Code postal', value: formData.code_postal });
        }
      }

      // Étape 3 : Budget (visible si étape actuelle >= 3)
      if (activeStep >= 3 && formData.budget_defini) {
        const budgetValue = formData.payment_method === 'jobi_only'
          ? `${formData.budget} Jobi`
          : formData.payment_method === 'direct_only'
          ? `${formData.budget} €`
          : `${formData.budget} € ou Jobi`;

        items.push({
          label: 'Budget',
          value: budgetValue
        });
      }

      // Étape 4 : Horaires (visible si étape actuelle >= 4)
      if (activeStep >= 4) {
        items.push({
          label: 'Horaires',
          value: hasSpecificTimeSlots
            ? `${formData.time_slots.length} créneau${formData.time_slots.length > 1 ? 'x' : ''}`
            : 'Pas de préférence horaire'
        });
      }

      // Étape 5 : Photos (visible si étape actuelle >= 5)
      if (activeStep >= 5) {
        if (previewUrls.length) {
          items.push({ label: 'Photos', value: `${previewUrls.length} photo${previewUrls.length > 1 ? 's' : ''}` });
        }
      }

      // Si aucune information n'est disponible
      if (items.length === 0) {
        return (
          <div className="empty-message">
            Commencez à remplir le formulaire pour voir le résumé de votre mission
          </div>
        );
      }

      return items.map((item, index) => (
        <div key={index} className="summary-item">
          <span className="label">{item.label}</span>
          <span className="value">{item.value}</span>
        </div>
      ));
    };

    return (
      <StepSummary>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{
              color: '#FF6B2C',
              fontWeight: 600,
              fontSize: '0.85rem',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              '&::before': {
                content: '"📝"',
                fontSize: '1rem'
              }
            }}
          >
            Résumé de votre mission en cours
          </Typography>

          <StepProgress>
            <StepInfo>
              <Typography
                sx={{
                  color: '#FF6B2C',
                  fontWeight: 600,
                  fontSize: '0.9rem'
                }}
              >
                Étape {activeStep + 1}/{steps.length}
              </Typography>
              <Typography
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  padding: '4px 12px',
                  borderRadius: '12px',
                  fontSize: '0.85rem'
                }}
              >
                {steps[activeStep]}
              </Typography>
            </StepInfo>
            {nextStep && (
              <NextStep
                onClick={undefined}
                sx={{
                  opacity: 0.7,
                  cursor: 'default'
                }}
              >
                <Typography sx={{ fontSize: '0.85rem' }}>
                  Suivant : {nextStep}
                </Typography>
                <ArrowRight sx={{ fontSize: 16 }} />
              </NextStep>
            )}
          </StepProgress>
        </Box>

        <SummaryContent>
          {getRelevantInfo()}
        </SummaryContent>
      </StepSummary>
    );
  };

  const renderMobileStepper = () => {
    const progress = ((activeStep + 1) / steps.length) * 100;
    const nextStep = activeStep < steps.length - 1 ? steps[activeStep + 1] : null;

    return (
      <Box
        className="mobile-step-indicator"
        sx={{
          display: 'none',
          '@media (max-width: 600px)': {
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#FFFFFF',
            padding: '16px',
            borderRadius: '12px',
            marginBottom: '16px',
            marginTop: '16px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
            border: '1px solid rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '8px 16px',
          backgroundColor: '#FFF8F3',
          borderRadius: '8px',
          border: '1px solid rgba(255, 107, 44, 0.1)'
        }}>
          <Typography variant="body2" sx={{ color: '#FF6B2C', fontWeight: 600 }}>
            {activeStep + 1}/{steps.length}
          </Typography>
          <Box sx={{
            width: `${progress}%`,
            height: '4px',
            backgroundColor: '#FF6B2C',
            borderRadius: '2px',
            transition: 'width 0.3s ease'
          }} />
        </Box>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '8px 16px',
          backgroundColor: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid rgba(255, 107, 44, 0.1)',
          marginBottom: '16px',
        }}>
          <Typography variant="body2" sx={{ color: '#666', fontWeight: 500 }}>
            {steps[activeStep]}
          </Typography>
          {nextStep && (
            <NextStep
              onClick={undefined}
              sx={{
                opacity: 0.7,
                cursor: 'default'
              }}
            >
              <Typography sx={{ fontSize: '0.85rem' }}>
                Suivant : {nextStep}
              </Typography>
              <ArrowRight sx={{ fontSize: 16 }} />
            </NextStep>
          )}
        </Box>
      </Box>
    );
  };

  const MissionTitle = styled(Typography)(() => ({
    color: '#2D3748',
    fontWeight: 700,
    fontSize: '1.5rem',
    marginBottom: '24px',
    position: 'relative',
    paddingLeft: '16px',
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: '50%',
      transform: 'translateY(-50%)',
      width: '6px',
      height: '70%',
      background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)',
      borderRadius: '3px',
    },
  }));

  const handleInterventionZoneChange = (newZone: InterventionZone) => {
            setFormData(prev => ({
              ...prev,
      intervention_zone: {
        ...newZone,
        adresse: newZone.adresse || ''
      }
    }));
  };

  const renderIncompleteProfileModal = () => (
    <ModalPortal>
      <div
        className="fixed inset-0 flex items-center justify-center p-4 z-[60]"
        onClick={() => navigate('/dashboard/profil')}
      >
        <div
          className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[500px] relative"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="text-center mb-6">
            <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <WarningIcon className="h-8 w-8 text-red-500" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Profil incomplet</h2>
            <p className="text-gray-600">
              Pour poster une mission, vous devez d'abord compléter votre profil avec les informations suivantes :
            </p>
          </div>

          <div className="space-y-3 mb-6">
            <div className="flex items-center text-gray-700">
              {userProfile?.prenom ? (
                <Check className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <CloseIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={userProfile?.prenom ? 'text-green-600' : 'text-gray-700'}>
                Prénom {userProfile?.prenom && `(${userProfile.prenom})`}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              {userProfile?.nom ? (
                <Check className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <CloseIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={userProfile?.nom ? 'text-green-600' : 'text-gray-700'}>
                Nom {userProfile?.nom && `(${userProfile.nom})`}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              {userProfile?.telephone ? (
                <Check className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <CloseIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={userProfile?.telephone ? 'text-green-600' : 'text-gray-700'}>
                Numéro de téléphone {userProfile?.telephone && `(${userProfile.telephone})`}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              {userProfile?.ville ? (
                <Check className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <CloseIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={userProfile?.ville ? 'text-green-600' : 'text-gray-700'}>
                Ville {userProfile?.ville && `(${userProfile.ville})`}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              {userProfile?.code_postal ? (
                <Check className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <CloseIcon className="h-5 w-5 text-red-500 mr-2" />
              )}
              <span className={userProfile?.code_postal ? 'text-green-600' : 'text-gray-700'}>
                Code postal {userProfile?.code_postal && `(${userProfile.code_postal})`}
              </span>
            </div>
          </div>

          <Button
            variant="contained"
            fullWidth
            onClick={() => navigate('/dashboard/profil')}
            sx={{
              backgroundColor: '#FF6B2C',
              '&:hover': {
                backgroundColor: '#FF7A35',
              },
              padding: '12px',
              borderRadius: '12px',
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 600
            }}
          >
            Compléter mon profil
          </Button>
        </div>
      </div>
    </ModalPortal>
  );

  // Ajouter un effet spécifique pour l'étape de localisation
  useEffect(() => {
    // Si l'utilisateur est à l'étape de localisation (étape 2)
    if (activeStep === 2 && userProfile) {
      // Si l'utilisateur a une adresse et un code postal dans son profil mais que formData n'a pas encore été mis à jour
      if (userProfile.ville && userProfile.code_postal && (!formData.ville || !formData.code_postal)) {
        try {
          // Initialiser directement les valeurs depuis le profil utilisateur
          setFormData(prev => ({
            ...prev,
            adresse: userProfile.adresse || '',
            code_postal: userProfile.code_postal || '',
            ville: userProfile.ville || ''
          }));

          // Nous avons déjà tenté de géocoder l'adresse dans fetchUserProfile, ne pas le faire à nouveau ici
        } catch (error) {
          logger.error('Erreur lors de la mise à jour des informations de localisation:', error);
        }
      }
    }
  }, [activeStep, userProfile]);

  const renderSelectionScreen = () => (
    <SelectionContainer>
      <SelectionTitle>
        Comment souhaitez-vous créer votre mission ?
      </SelectionTitle>
      <SelectionSubtitle>
        Choisissez l'option qui vous convient le mieux pour créer votre mission et trouver le jobbeur idéal
      </SelectionSubtitle>

      <OptionsContainer>
        {/* Option 1: IA Assistant */}
        <OptionCard onClick={handleOpenAiAssistant}>
          <OptionHeader>
            <OptionIcon>
              <Sparkles className="h-6 w-6" />
            </OptionIcon>
            <Box>
              <OptionTitle>Assistant IA</OptionTitle>
              <OptionTagline>Création rapide et intelligente</OptionTagline>
            </Box>
          </OptionHeader>
          <OptionContent>
            <OptionDescription>
              Laissez notre intelligence artificielle créer votre mission à partir d'une simple description de votre besoin.
            </OptionDescription>

            <FeatureList>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Création complète en quelques minutes</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Suggestions intelligentes adaptées à votre besoin</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Modification possible avant publication</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Idéal pour gagner du temps</FeatureText>
              </FeatureItem>
            </FeatureList>

            <OptionButton
              variant="contained"
              sx={{ backgroundColor: '#FF6B2C', '&:hover': { backgroundColor: '#FF7A35' } }}
              startIcon={<Sparkles className="h-5 w-5" />}
            >
              Créer avec l'IA
            </OptionButton>
          </OptionContent>
        </OptionCard>

        {/* Option 2: Manual Creation */}
        <OptionCard onClick={() => {
          setShowSelectionScreen(false);
          const guideShown = getCookie('postMissionGuideShown');
          if (!guideShown) {
            setShowGuide(true);
          }
          if (window.innerWidth < 980) {
            setTimeout(scrollToTop, 100); // Scroll en haut sur mobile après le changement d'écran
          }
        }}>
          <OptionHeader>
            <OptionIcon>
              <Edit className="h-6 w-6" />
            </OptionIcon>
            <Box>
              <OptionTitle>Création manuelle</OptionTitle>
              <OptionTagline>Personnalisation complète</OptionTagline>
            </Box>
          </OptionHeader>
          <OptionContent>
            <OptionDescription>
              Créez votre mission étape par étape avec notre assistant guidé pour une personnalisation maximale.
            </OptionDescription>

            <FeatureList>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Création guidée étape par étape</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Contrôle total sur chaque détail</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Assistance IA disponible à chaque étape</FeatureText>
              </FeatureItem>
              <FeatureItem>
                <FeatureIcon>
                  <Check className="h-5 w-5" />
                </FeatureIcon>
                <FeatureText>Idéal pour les missions spécifiques</FeatureText>
              </FeatureItem>
            </FeatureList>

            <OptionButton
              variant="outlined"
              sx={{
                color: '#FF6B2C',
                borderColor: '#FF6B2C',
                '&:hover': {
                  borderColor: '#FF7A35',
                  backgroundColor: 'rgba(255, 107, 44, 0.08)'
                }
              }}
              startIcon={<Edit className="h-5 w-5" />}
            >
              Créer manuellement
            </OptionButton>
          </OptionContent>
        </OptionCard>
      </OptionsContainer>
    </SelectionContainer>
  );

  return (
    <>
      {/* Système de génération IA */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="mission_post"
          prompt={`
            Informations sur ma mission:
            - Titre: ${formData.titre || ''}
            - Type de service: ${selectedCategory?.nom || ''} > ${selectedSubcategory?.nom || ''}
            ${stripHtml(formData.description).length > 0 ?
              `- Action: Améliorer mon texte existant
            - Mon texte actuel: ${stripHtml(formData.description)}
            - Ce que je veux: Garde les informations importantes de mon texte, mais rends-le plus naturel et sympathique, comme si je parlais à un voisin. Utilise le "je" et un ton conversationnel. Évite le jargon professionnel ou les formules impersonnelles.`
              :
              `- Action: Rédiger une nouvelle description
            - Ce que je veux: Écris comme si j'étais un particulier qui cherche de l'aide pour cette tâche. Utilise le "je", sois précis sur ce que j'attends, mentionne les compétences appréciées, et termine par une invitation amicale à me contacter. Évite tout ce qui ressemble à une annonce d'emploi ou à un texte commercial.`
            }
            - Format: Utilise des balises HTML simples (<p>, <strong>, <em>) pour la mise en forme, mais sans excès.
          `}
          originalPrompt={stripHtml(formData.description).length > 0 ? stripHtml(formData.description) : undefined}
          onComplete={(content) => {
            setIsAiConfirmModalOpen(false);

            if (content) {
              // Mettre à jour le contenu de l'éditeur avec le contenu généré
              if (editorRef.current) {
                const editor = editorRef.current.getEditor();
                if (editor) {
                  editor.commands.setContent(content);
                }
              }

              // Mettre à jour le formData
              setFormData(prev => ({
                ...prev,
                description: content
              }));

              // Mettre à jour le compteur de caractères
              const textLength = stripHtml(content).length;
              setDescriptionLength(textLength);

              notify('Description générée avec succès !', 'success');
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
          maxDuration={30000}
        />
      )}

      {/* Assistant IA pour la création complète de mission */}
      {isAiAssistantOpen && (
        <AiMissionAssistant
          open={isAiAssistantOpen}
          onClose={() => {
            setIsAiAssistantOpen(false);
            setShowSelectionScreen(true);
          }}
          onComplete={handleAiAssistantComplete}
          userProfile={userProfile}
        />
      )}

      {showIncompleteProfileModal && renderIncompleteProfileModal()}

      {/* Modal de modération d'image */}
      {isModerationModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay avec gestion du clic */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => {
                // Toujours permettre l'annulation, même pendant le chargement
                setIsModerationModalOpen(false);
                if (moderationPreviewUrl) {
                  URL.revokeObjectURL(moderationPreviewUrl);
                  setModerationPreviewUrl(null);
                }
                setIsImageRejected(false);
                setRejectionDescription(undefined);
              }}
            />
            {/* Contenu de la modale */}
            <div className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col">
              <div className="p-4 pt-5 flex justify-between items-center border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  {isModerationLoading
                    ? `Modération de l'image ${currentModerationIndex + 1}/${selectedFiles.length}`
                    : isImageRejected
                      ? "Image refusée"
                      : `Modération de l'image ${currentModerationIndex + 1}/${selectedFiles.length}`}
                </h2>
                <button
                  onClick={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
              <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
                {isModerationLoading ? (
                  <ImageModerationStatus
                    isLoading={true}
                    imageUrl={moderationPreviewUrl || undefined}
                    title={`Modération de l'image ${currentModerationIndex + 1}/${selectedFiles.length}`}
                    onCancel={() => {
                      // Toujours permettre l'annulation, même pendant le chargement
                      setIsModerationModalOpen(false);
                      if (moderationPreviewUrl) {
                        URL.revokeObjectURL(moderationPreviewUrl);
                        setModerationPreviewUrl(null);
                      }
                      setIsImageRejected(false);
                      setRejectionDescription(undefined);
                    }}
                  />
                ) : isImageRejected ? (
                  <div className="p-6">
                    {/* Afficher l'image refusée */}
                    {moderationPreviewUrl && (
                      <div className="mb-6 flex justify-center">
                        <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                          <img
                            src={moderationPreviewUrl}
                            alt="Image refusée"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-red-900/20"></div>
                        </div>
                      </div>
                    )}

                    {/* Message de rejet détaillé */}
                    <RejectedImageMessage
                      contentType="mission"
                      description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                      improvementSuggestions={rejectionImprovementSuggestions}
                      variant="detailed"
                    />

                    {/* Bouton pour réessayer */}
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => {
                          setIsModerationModalOpen(false);
                          if (moderationPreviewUrl) {
                            URL.revokeObjectURL(moderationPreviewUrl);
                            setModerationPreviewUrl(null);
                          }
                          setIsImageRejected(false);
                          setRejectionDescription(undefined);
                          setRejectionImprovementSuggestions(undefined);

                          // Ouvrir automatiquement le sélecteur de fichier
                          setTimeout(() => {
                            const input = document.getElementById('upload-photos') as HTMLInputElement;
                            if (input) {
                              input.click();
                            }
                          }, 300);
                        }}
                        className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                      >
                        Choisir une autre image
                      </button>
                    </div>
                  </div>
                ) : (
                  <ImageModerationStatus
                    isLoading={false}
                    imageUrl={moderationPreviewUrl || undefined}
                    title={`Modération de l'image ${currentModerationIndex + 1}/${selectedFiles.length}`}
                    onCancel={() => {
                      setIsModerationModalOpen(false);
                      if (moderationPreviewUrl) {
                        URL.revokeObjectURL(moderationPreviewUrl);
                        setModerationPreviewUrl(null);
                      }
                      setIsImageRejected(false);
                      setRejectionDescription(undefined);
                      setRejectionImprovementSuggestions(undefined);
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      <PostMissionGuide
        open={showGuide}
        onClose={() => setShowGuide(false)}
      />
      <StepGuideModal
        open={showStepGuide}
        onClose={() => {
          setShowStepGuide(false);
          // Défilement supplémentaire après fermeture du modal
          setTimeout(scrollToTop, 100);
        }}
        step={activeStep}
      />
      <Box sx={{ height: '100%' }}>
        {!showSelectionScreen ? (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', flexWrap: 'wrap', gap: 2 }}>
              <Box>
                <PageTitle variant="h1">
                  Poster une demande aux jobbeurs
                </PageTitle>
                <PageSubtitle>
                  Décrivez votre besoin et trouvez le jobbeur idéal pour votre mission.
                </PageSubtitle>
              </Box>
              <Button
                variant="contained"
                onClick={() => setShowSelectionScreen(true)}
                startIcon={<ArrowLeft className="h-4 w-4" />}
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: '#FFFFFF',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  padding: '10px 16px',
                  borderRadius: '12px',
                  fontWeight: 600,
                  whiteSpace: 'nowrap',
                  marginTop: { xs: 0, sm: 1 },
                  alignSelf: 'flex-start',
                  boxShadow: '0 4px 8px rgba(255, 107, 44, 0.2)',
                }}
              >
                Revenir au choix
              </Button>
            </Box>

            {renderMobileStepper()}

            <StyledPaper elevation={1} ref={contentContainerRef}>
              <ScrollableArea ref={scrollableRef} className="scrollable-container">
                <ScrollableContent>
                  <Box mt={2}>
                    {activeStep !== 6 && getStepSummary()}
                  </Box>
                  {getStepContent(activeStep)}
                </ScrollableContent>
                <AnimatePresence>
                  {showScrollIndicator && (
                    <ScrollIndicator
                      initial={{ x: '-100%', opacity: 0 }}
                      animate={{
                        x: '0%',
                        opacity: 1,
                        transition: {
                          duration: 0.3
                        }
                      }}
                      exit={{ x: '-100%', opacity: 0 }}
                      onClick={scrollToContent}
                      whileHover={{
                        scale: 1.05,
                        transition: {
                          duration: 0.2
                        }
                      }}
                      whileTap={{
                        scale: 0.95,
                        transition: {
                          duration: 0.1
                        }
                      }}
                    >
                      <span className="scroll-text">
                        <span>Faire défiler</span>
                        <span className="scroll-icon" />
                      </span>
                    </ScrollIndicator>
                  )}
                </AnimatePresence>
              </ScrollableArea>

              <ButtonContainer>
                {activeStep > 0 && (
                  <Button
                    onClick={handleBack}
                    startIcon={<ArrowLeft sx={{ fontSize: { xs: 14, sm: 16 } }} />}
                    sx={{
                      color: '#666',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)'
                      },
                      '@media (max-width: 600px)': {
                        width: '100%',
                        padding: '10px 16px',
                        fontSize: '0.875rem',
                        borderRadius: '8px',
                        border: '1px solid #E0E0E0',
                        backgroundColor: '#f5f5f5',
                      }
                    }}
                  >
                    Retour
                  </Button>
                )}
                <Button
                  variant="contained"
                  onClick={handleNext}
                  endIcon={activeStep === steps.length - 1 ? <Check sx={{ fontSize: { xs: 14, sm: 16 } }} /> : <ArrowRight sx={{ fontSize: { xs: 14, sm: 16 } }} />}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: '#ff855c'
                    },
                    '@media (max-width: 600px)': {
                      width: '100%',
                      padding: '12px 16px',
                      fontSize: '0.9rem',
                      fontWeight: 600,
                      borderRadius: '8px',
                      boxShadow: '0 4px 8px rgba(255, 107, 44, 0.2)',
                    }
                  }}
                  disabled={isSubmitting || isModerating || (activeStep === 1 && descriptionLength > 1200)}
                >
                  {isSubmitting || isModerating
                    ? (activeStep === steps.length - 1 ? 'Modération en cours...' : 'Modération en cours...')
                    : (activeStep === 1 && descriptionLength > 1200)
                      ? `Texte trop long (${descriptionLength}/1200)`
                      : (activeStep === steps.length - 1 ? 'Poster la mission' : 'Suivant')}
                </Button>
              </ButtonContainer>
            </StyledPaper>
          </>
        ) : (
          renderSelectionScreen()
        )}
      </Box>
    </>
  );
};

export default PostMission;