import React from 'react';
import { motion } from 'framer-motion';
import { Tooltip } from '@mui/material';
import DOMPurify from 'dompurify';
import { User as UserIcon, Sparkles, HelpCircle } from 'lucide-react';
import TiptapEditor, { TiptapInstance } from '../../components/TiptapEditor';

interface AboutSectionProps {
  isOwnProfil: boolean;
  isEditingBio: boolean;
  setIsEditingBio: (v: boolean) => void;
  tempBio: string;
  setTempBio: (v: string) => void;
  profil: any;
  notify: (msg: string, type?: string) => void;
  setIsFirstNameFocused: (v: boolean) => void;
  setIsLastNameFocused: (v: boolean) => void;
  validateContentSafety: (content: string, type: string) => Promise<boolean>;
  setPreviousBio: (v: string) => void;
  setIsConfirmingBio: (v: boolean) => void;
  setIsModerationLoading: (v: boolean) => void;
  isModerationLoading: boolean;
  stripHtml: (html: string) => string;
  editorRef: React.RefObject<TiptapInstance | null>;
  handleEditorChange: (content: string) => void;
  handleBioChange: (content: string) => void;
  previousBio: string;
  // Ajoute d'autres props si besoin
  setIsAiConfirmModalOpen: (v: boolean) => void;
}

const AboutSection: React.FC<AboutSectionProps> = ({
  isOwnProfil,
  isEditingBio,
  setIsEditingBio,
  tempBio,
  setTempBio,
  profil,
  notify,
  setIsFirstNameFocused,
  setIsLastNameFocused,
  validateContentSafety,
  setPreviousBio,
  setIsConfirmingBio,
  setIsModerationLoading,
  isModerationLoading,
  stripHtml,
  editorRef,
  handleEditorChange,
  handleBioChange,
  previousBio,
  setIsAiConfirmModalOpen
}) => {
  return (
    <motion.section
      className="bg-white rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="flex flex-col sm:flex-row justify-between items-start gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
            <UserIcon className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">À propos</h2>
        </div>
        {!isEditingBio ? (
          <div className="flex items-center gap-2 mt-2 sm:mt-0">
            {isOwnProfil ? (
              <Tooltip title="Modifier la bio">
                <button
                  onClick={() => {
                    if (profil?.firstName === 'Prénom' || profil?.lastName === 'Nom') {
                      notify(
                        'Vous devez définir votre prénom et nom avant de modifier votre bio.',
                        'error'
                      );
                      setIsFirstNameFocused(true);
                      setIsLastNameFocused(true);
                      return;
                    }
                    setIsEditingBio(true);
                    setTempBio(profil?.bio || '');
                  }}
                  className="flex-1 lg:flex-initial px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors whitespace-nowrap w-fit"
                  aria-label="Modifier la bio"
                >
                  {!profil?.bio ? "Saisir la bio" : "Modifier"}
                </button>
              </Tooltip>
            ) : null}
          </div>
        ) : (
          <div className="flex flex-col sm:flex-row gap-3 justify-end mt-2 sm:mt-0">
            <button
              onClick={() => {
                setIsEditingBio(false);
                setTempBio(profil?.bio || '');
                setIsModerationLoading(false); // Réinitialiser l'état de modération
              }}
              className="px-4 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors whitespace-nowrap w-full sm:w-auto"
            >
              Annuler
            </button>
            <button
              onClick={() => setIsAiConfirmModalOpen(true)}
              disabled={isModerationLoading}
              className="flex items-center gap-2 px-4 py-2 bg-[#FFF8F3] text-[#FF6B2C] rounded-lg hover:bg-[#FFE4BA] transition-colors whitespace-nowrap w-full sm:w-auto"
            >
              <Sparkles className="h-4 w-4" />
              {stripHtml(tempBio).length > 0 ? "Améliorer avec IA" : "Générer avec IA"}
              <Tooltip title={
                stripHtml(tempBio).length > 0
                  ? "Améliorer le contenu existant avec l'IA"
                  : "Générer du nouveau contenu avec l'IA"
              }>
                <HelpCircle className="h-4 w-4 ml-1" />
              </Tooltip>
            </button>
            <button
              onClick={async () => {
                const newBio = DOMPurify.sanitize(tempBio.trim());
                if (newBio === profil?.bio) {
                  notify('Aucune modification à enregistrer', 'info');
                  setIsEditingBio(false);
                  return;
                }
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newBio;
                const textContent = tempDiv.textContent || tempDiv.innerText;
                if (textContent.length > 1000) {
                  notify('La bio ne peut pas dépasser 1000 caractères, actuellement : ' + textContent.length, 'error');
                  return;
                }
                setIsModerationLoading(true);
                try {
                  const isBioSafe = await validateContentSafety(textContent, 'profile');
                  if (!isBioSafe) {
                    setIsModerationLoading(false);
                    return;
                  }
                  setPreviousBio(profil?.bio || '');
                  setIsConfirmingBio(true);
                  setIsEditingBio(false);
                } catch (error) {
                  notify('Une erreur est survenue lors de la vérification du contenu. Veuillez réessayer.', 'error');
                } finally {
                  setIsModerationLoading(false);
                }
              }}
              disabled={isModerationLoading || stripHtml(tempBio).length > 1000}
              className={`px-6 py-2 text-white rounded-lg transition-colors shadow-md whitespace-nowrap w-full sm:w-auto ${
                isModerationLoading || stripHtml(tempBio).length > 1000
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-[#FF6B2C] hover:bg-[#FF7A35] hover:shadow-lg'
              }`}
            >
              {isModerationLoading
                ? 'En cours de modération...'
                : stripHtml(tempBio).length > 1000
                  ? `Texte trop long (${stripHtml(tempBio).length}/1000)`
                  : 'Enregistrer'
              }
            </button>
          </div>
        )}
      </div>

      {!isEditingBio ? (
        <div className="text-sm max-w-none text-gray-600 leading-relaxed">
          <div className="tableWrapper" dangerouslySetInnerHTML={{
            __html: profil?.bio && !/^<p><br><\/p>$/.test(profil.bio.trim())
              ? DOMPurify.sanitize(profil.bio)
              : isOwnProfil
                ? 'Aucune bio renseignée. Cliquez sur le crayon pour ajouter votre bio.'
                : 'Cet utilisateur n\'a pas encore renseigné de bio.'
          }} />
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative bg-white rounded-lg overflow-hidden">
            <TiptapEditor
              ref={editorRef}
              content={tempBio}
              onChange={(content) => {
                handleEditorChange(content);
                handleBioChange(content);
              }}
              className="min-h-[200px]"
              placeholder="Décrivez votre expérience, vos connaissances, vos compétences ..."
            />
            <div className="flex justify-between items-center p-4 bg-[#FFF8F3] border-t border-[#FFE4BA]">
              <span className="text-sm text-gray-500">
                {stripHtml(tempBio).length} / 1000 caractères
              </span>
              {stripHtml(tempBio).length >= 1000 && (
                <span className="text-sm text-red-500 animate-pulse">
                  Limite de caractères atteinte
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Utilisez les outils de mise en forme pour structurer votre texte</span>
          </div>
        </div>
      )}
    </motion.section>
  );
};

export default AboutSection; 