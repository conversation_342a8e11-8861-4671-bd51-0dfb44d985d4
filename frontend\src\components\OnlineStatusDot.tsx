import React, { useEffect, useState } from 'react';
import { Box, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';
import { getCommonHeaders } from '@/utils/headers';
import { fetchCsrfToken } from '@/services/csrf';
import { API_CONFIG } from '@/config/api';
import logger from '@/utils/logger';

// Cache global pour stocker les statuts en ligne des utilisateurs
const onlineStatusCache = new Map<string, { status: boolean; timestamp: number }>();
// Durée de validité du cache en millisecondes (60 secondes)
const CACHE_DURATION = 60 * 1000;
// Intervalle de vérification en millisecondes (60 secondes)
const CHECK_INTERVAL = 60 * 1000;
// Registre des vérifications en cours pour éviter les requêtes parallèles
const pendingChecks = new Map<string, Promise<boolean>>();

// Utiliser une classe CSS au lieu de passer isOnline comme prop à un élément DOM
const StatusDot = styled(Box)(() => ({
  width: '10px',
  height: '10px',
  borderRadius: '50%',
  transition: 'all 0.3s ease',
}));

// Styles pour les états en ligne et hors ligne
const onlineStyles = {
  backgroundColor: '#4CAF50',
  border: '2px solid #fff',
  boxShadow: '0 0 0 2px #4CAF50',
};

const offlineStyles = {
  backgroundColor: '#9e9e9e',
  border: '2px solid #f5f5f5',
  boxShadow: '0 0 0 2px #9e9e9e',
};

interface OnlineStatusDotProps {
  userId: string;
  className?: string;
}

const OnlineStatusDot: React.FC<OnlineStatusDotProps> = ({ userId, className }) => {
  const [isOnline, setIsOnline] = useState<boolean>(false);

  // Fonction pour vérifier le statut en ligne d'un utilisateur
  const checkStatus = async (userId: string): Promise<boolean> => {
    // Si une vérification est déjà en cours pour cet utilisateur, retourner sa promesse
    if (pendingChecks.has(userId)) {
      return pendingChecks.get(userId)!;
    }

    // Vérifier si le statut est dans le cache et s'il est encore valide
    const cachedData = onlineStatusCache.get(userId);
    const now = Date.now();
    if (cachedData && (now - cachedData.timestamp < CACHE_DURATION)) {
      return cachedData.status;
    }

    // Créer une nouvelle promesse pour la vérification
    const checkPromise = (async () => {
      try {
        await fetchCsrfToken();
        const headers = await getCommonHeaders();
        const response = await fetch(`${API_CONFIG.baseURL}/api/users/online-status/${userId}`, { headers });
        const data = await response.json();
        
        // Mettre à jour le cache global avec le nouveau statut
        onlineStatusCache.set(userId, { status: data.isOnline, timestamp: now });
        
        return data.isOnline;
      } catch (error) {
        logger.error(`Erreur lors de la vérification du statut : ${error}`);
        return false;
      } finally {
        // Retirer cette vérification de la Map des vérifications en cours
        pendingChecks.delete(userId);
      }
    })();

    // Ajouter la promesse à la Map des vérifications en cours
    pendingChecks.set(userId, checkPromise);
    
    return checkPromise;
  };

  useEffect(() => {
    let isMounted = true;

    const updateStatus = async () => {
      const status = await checkStatus(userId);
      if (isMounted) {
        setIsOnline(status);
      }
    };

    // Vérifier immédiatement le statut
    updateStatus();
    
    // Configurer l'intervalle de vérification
    const interval = setInterval(updateStatus, CHECK_INTERVAL);

    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [userId]);

  return (
    <Tooltip title={isOnline ? 'En ligne' : 'Hors ligne'} arrow>
      <StatusDot 
        className={className} 
        sx={isOnline ? onlineStyles : offlineStyles}
        data-online={isOnline ? 'true' : 'false'}
      />
    </Tooltip>
  );
};

export default OnlineStatusDot; 