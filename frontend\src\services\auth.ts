import { logger } from '../utils/logger';
import { getCookie, setCookie } from '../utils/cookieUtils';
import type { NotificationType } from '../components/Notification';
import { fetchCsrfToken, clearCsrfCache } from './csrf';
import { getCommonHeaders } from '../utils/headers';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';
const FRONTEND_URL = 'http://jobpartiel.fr';

// Variables pour contrôler le renouvellement du token
let isRefreshing = false;
let lastRefreshTime = 0;
const MIN_REFRESH_INTERVAL = 30000; // 30 secondes minimum entre les renouvellements

// Exporter le type NotificationType
export type { NotificationType } from '../components/Notification';

// Type pour la fonction de notification
export type NotifyFunction = (message: string, type: NotificationType) => void;

export const createAuthService = (notify?: NotifyFunction) => {

  return ({
    requestPasswordReset: async (email: string) => {
      try {
        // Vérifier le cooldown local
        const cooldownData = JSON.parse(getCookie('passwordResetCooldown') || 'null');

        if (cooldownData) {
          const remainingTime = Math.max(0,
            cooldownData.expiresAt - Math.floor(Date.now() / 1000)
          );

          if (remainingTime > 0) {
            throw new Error(`Veuillez attendre ${Math.ceil(remainingTime / 60)} minutes avant de réessayer`);
          }
        }

        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/forgot-password`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ email }),
          credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
          // Gestion du cas où le compte n'est pas vérifié
          if (data.requiresVerification) {
            // Envoyer automatiquement l'email de vérification
            const verificationResponse = await fetch(`${API_URL}/api/auth/resend-verification`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ email }),
              credentials: 'include'
            });

            const verificationData = await verificationResponse.json();

            if (!verificationResponse.ok) {
              throw new Error(verificationData.message || 'Erreur lors de l\'envoi de l\'email de vérification');
            }
            setCookie('pendingVerificationEmail', email, 7 * 24 * 60 * 60); // 7 jours en secondes

            throw new Error('Compte non vérifié. Un email de vérification a été envoyé.');
          }

          if (notify) {
            notify(data.message, data.toastType || 'error');
          }
          throw new Error(data.message || 'Erreur lors de la réinitialisation du mot de passe');
        }

        // Gérer le cooldown si présent
        if (data.cooldownMinutes) {
          const cooldownSeconds = data.cooldownMinutes * 60;
          const expiresAt = Math.floor(Date.now() / 1000) + cooldownSeconds;

          setCookie('passwordResetCooldown', JSON.stringify({ expiresAt }), 7 * 24 * 60 * 60); // 7 jours en secondes
        }

        if (notify) {
          notify(data.message, data.toastType || 'success');
        }

        return {
          success: true,
          cooldownMinutes: data.cooldownMinutes || 0
        };
      } catch (error: any) {
        logger.info('Password reset request error:', error);
        throw error;
      }
    },

    resetPassword: async (token: string, newPassword: string) => {
      try {
        logger.info('Tentative de réinitialisation du mot de passe', { token });

        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/reset-password`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ token, password: newPassword }),
          credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
          logger.info('Erreur de réinitialisation:', data);
          throw new Error(data.message || 'Erreur lors de la réinitialisation du mot de passe');
        }

        if (notify) {
          notify(data.message, data.toastType || 'success');
        }

        return data;
      } catch (error: any) {
        logger.info('Password reset error:', error);
        if (notify) {
          notify(error.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe', 'error');
        }
        throw error;
      }
    },

    signIn: async (email: string, password: string) => {
      try {
        logger.info('🔐 Tentative de connexion', { email });

        // Forcer la récupération d'un nouveau token CSRF pour éviter les problèmes de session
        logger.info('Récupération d\'un nouveau token CSRF (forcé)');
        clearCsrfCache(); // Vider le cache pour forcer une nouvelle requête
        const csrfToken = await fetchCsrfToken();
        logger.info('Token CSRF récupéré', { csrfToken });

        if (!csrfToken) {
          logger.info('Pas de token CSRF récupéré');
          throw new Error('Impossible de récupérer le token CSRF');
        }

        logger.info('Récupération des headers (sans cache)');
        const headers = await getCommonHeaders(false); // Forcer sans cache
        headers['X-CSRF-Token'] = csrfToken;
        headers['Content-Type'] = 'application/json';
        logger.info('Headers récupérés', { headers });

        logger.info('Envoi de la requête de connexion', {
          url: `${API_URL}/api/auth/login`,
          headers,
          credentials: 'include'
        });

        const response = await fetch(`${API_URL}/api/auth/login`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ email, password }),
          credentials: 'include'
        });

        const data = await response.json();
        logger.info('📥 Réponse de connexion', {
          status: response.status,
          headers: Object.fromEntries(response.headers.entries()),
          data
        });

        // Gérer d'abord les codes d'erreur HTTP spécifiques
        if (response.status === 423) {  // Compte bloqué ou désactivé
          return {
            data: {
              success: false,
              locked: true,
              message: data.message
            }
          };
        }

        if (response.status === 403) {
          // Vérifier si c'est une erreur CSRF spécifique
          if (data.code === 'CSRF_TOKEN_INVALID') {
            return {
              data: {
                success: false,
                message: data.error || 'Token CSRF invalide. Veuillez rafraîchir la page et réessayer.',
                user: data.user || null
              }
            };
          }
          
          // Sinon, c'est probablement une erreur de vérification d'email
          return {
            data: {
              success: false,
              email_verifier: false,
              message: data.message || 'Veuillez vérifier votre email avant de continuer, nous vous avons envoyé un lien de vérification.',
              user: data.user || null
            }
          };
        }

        if (!response.ok) {
          return {
            data: {
              success: false,
              message: data.message || 'Une erreur est survenue',
              user: data.user || null
            }
          };
        }

        // Vérifier si l'authentification à deux facteurs est requise
        if (data.requiresTwoFactor) {
          logger.info('Authentification à deux facteurs requise', { email });
          return {
            data: {
              success: true,
              requiresTwoFactor: true,
              email: data.email,
              maskedEmail: data.maskedEmail,
              message: data.message || 'Un code de vérification a été envoyé à votre adresse email'
            }
          };
        }

        // Traitement en cas de succès
        return { data };

      } catch (error) {
        logger.info('🚨 Erreur de connexion', error);
        notify?.('Une erreur est survenue lors de cette connexion.', 'error');
        throw error;
      }
    },

    signOut: async () => {
      try {
        // S'assurer d'avoir un token CSRF valide
        const csrfToken = await fetchCsrfToken();
        if (!csrfToken) {
          throw new Error('Impossible de récupérer le token CSRF');
        }

        const headers = await getCommonHeaders(false);
        headers['X-CSRF-Token'] = csrfToken;
        headers['Content-Type'] = 'application/json';
        const response = await fetch(`${API_URL}/api/auth/logout`, {
          method: 'POST',
          headers,
          credentials: 'include'
        });

        const data = await response.json();

        if (!data.success) {
          notify?.(data.message || 'Échec de la déconnexion', 'error');
        } else {
          notify?.('Déconnexion réussie', 'success');
        }

        return data;
      } catch (error) {
        logger.info('🚨 Erreur de déconnexion', error);
        notify?.('Une erreur est survenue lors de la déconnexion', 'error');
        throw error;
      }
    },

    signUp: async (userData: {
      email: string,
      password: string,
      userType: 'non-jobbeur' | 'jobbeur',
      referralCode?: string
    }) => {
      try {
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/inscription`, {
          method: 'POST',
          headers,
          body: JSON.stringify(userData),
          credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
          return {
            success: false,
            message: data.message || 'Erreur lors de l\'inscription'
          };
        }

        // Log pour le suivi de l'envoi de l'email de vérification
        if (data.needsVerification) {
          logger.info('Email de vérification envoyé', { email: userData.email });
        }

        return {
          success: true,
          userId: data.user.id,
          needsVerification: data.needsVerification
        };
      } catch (error: any) {
        logger.info('Sign up error:', error);
        return {
          success: false,
          message: error.message || 'Une erreur est survenue lors de l\'inscription'
        };
      }
    },

    resendVerification: async (email?: string) => {
      try {
        // Log détaillé avant l'envoi de la requête
        logger.info('Tentative de renvoi de l\'email de vérification', {
          email,
          apiUrl: `${API_URL}/api/auth/resend-verification`
        });

        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/resend-verification`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ email }),
          credentials: 'include'
        });

        // Log de la réponse brute
        const rawData = await response.text();
        logger.info('Réponse brute du serveur', {
          status: response.status,
          rawResponse: rawData
        });

        // Parsing manuel du JSON
        let data;
        try {
          data = JSON.parse(rawData);
        } catch (parseError) {
          logger.info('Erreur de parsing de la réponse', {
            rawResponse: rawData,
            parseError
          });
          throw new Error('Impossible de parser la réponse du serveur');
        }

        if (!response.ok) {
          logger.info('Erreur lors du renvoi de l\'email', {
            status: response.status,
            errorMessage: data.message
          });
          throw new Error(data.message || 'Erreur lors du renvoi de l\'email de vérification');
        }

        // Log détaillé en cas de succès
        logger.info('Email de vérification renvoyé avec succès', {
          email,
          verificationLink: data.verificationLink
        });

        // Ajouter le host complet au lien de vérification
        const verificationLink = `${FRONTEND_URL}/${data.verificationLink}`;

        // Mettre à jour le cooldown
        const cooldownTime = Date.now() + 2 * 60 * 1000; // 2 minutes
        setCookie('verificationEmailCooldown', String(cooldownTime), 7 * 24 * 60 * 60);

        if (notify) {
          notify('Email de vérification renvoyé', 'success');
        }

        return {
          success: true,
          verificationLink
        };
      } catch (error: any) {
        // Log détaillé de l'erreur
        logger.info('Erreur complète lors du renvoi de vérification', {
          errorName: error.name,
          errorMessage: error.message,
          errorStack: error.stack
        });

        if (notify) {
          notify(error.message || 'Une erreur est survenue', 'error');
        }
        throw error;
      }
    },

    validateResetToken: async (token: string) => {
      try {
        logger.info('Validation du token de réinitialisation', { token });

        // S'assurer d'avoir un token CSRF valide
        const csrfToken = await fetchCsrfToken();
        if (!csrfToken) {
          throw new Error('Impossible de récupérer le token CSRF');
        }

        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/validate-reset-token`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ token }),
          credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
          logger.info('Erreur de validation:', data);
          throw new Error(data.message || 'Token invalide ou expiré');
        }

        return data;
      } catch (error: any) {
        logger.info('Token validation error:', error);
        throw error;
      }
    },

    verifyEmail: async (token: string): Promise<{ success: boolean; alreadyVerified?: boolean }> => {
      try {
        // Récupérer d'abord un token CSRF
        await fetchCsrfToken();
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;

        logger.info('Envoi de la requête de vérification d\'email avec token', { tokenLength: token.length });

        const response = await fetch(`${API_URL}/api/auth/verify-email`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ token }),
          credentials: 'include'
        });

        // Vérifier si la réponse est valide avant de tenter de la parser
        if (!response.ok && response.status !== 400) {
          logger.error('Erreur de réponse HTTP:', { status: response.status, statusText: response.statusText });
          throw new Error(`Erreur HTTP: ${response.status} ${response.statusText}`);
        }

        // Tenter de parser la réponse JSON
        let data;
        try {
          data = await response.json();
          logger.info('Réponse de vérification d\'email reçue:', data);
        } catch (parseError) {
          logger.error('Erreur lors du parsing de la réponse JSON:', parseError);
          throw new Error('Impossible de lire la réponse du serveur. Veuillez réessayer.');
        }

        // Gérer différents scénarios de réponse
        if (response.status === 200) {
          return {
            success: true,
            alreadyVerified: data.alreadyVerified || false
          };
        }

        // Gérer les cas d'erreur spécifiques
        if (response.status === 400) {
          switch (data.code) {
            case 'TOKEN_EXPIRED':
              if (notify) {
                notify('Le token de vérification a expiré. Veuillez demander un nouveau token.', 'error');
              }
              throw new Error('Le token de vérification a expiré. Veuillez demander un nouveau token.');
            case 'TOKEN_INVALID':
              if (notify) {
                notify('Le lien de vérification est invalide. Connectez-vous afin d\'en générer un nouveau.', 'error');
              }
              throw new Error('Le token de vérification est invalide.');
            case 'ALREADY_VERIFIED':
              if (notify) {
                notify('L\'email est déjà verifié.', 'error');
              }
              return {
                success: true,
                alreadyVerified: true
              };
            default:
              if (notify) {
                notify('Une erreur est survenue lors de la vérification de l\'email.', 'error');
              }
              throw new Error('Une erreur est survenue lors de la vérification de l\'email.');
          }
        }

        throw new Error('Une erreur inattendue est survenue.');
      } catch (error: any) {
        // Log détaillé de l'erreur côté client
        logger.error('Erreur de vérification d\'email', {
          message: error.message,
          stack: error.stack,
          name: error.name,
          type: typeof error
        });

        // Propager l'erreur avec un message personnalisé
        if (error.response) {
          throw new Error(error.response.data?.message || 'Échec de la vérification de l\'email');
        }

        // Si c'est une erreur de parsing JSON ou une erreur réseau
        if (error.name === 'SyntaxError' || error.name === 'TypeError') {
          throw new Error('Problème de communication avec le serveur. Veuillez réessayer.');
        }

        throw error;
      }
    },

    verify_profil_actif: async (userId: string) => { // Vérifier si le profil est actif lorsqu'il est authentifié sinon le déconnecter et notifier l'utilisateur
      try {
        // logger.info('🔍 Vérification du statut du profil lorsqu\'il est authentifié afin de le déconnecter si le profil est désactivé (suspendu, banni, supprimé etc pour la modération) et notifier l\'utilisateur :', { userId });

        // S'assurer d'avoir un token CSRF valide avant la requête
        await fetchCsrfToken();
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders();
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;

        const response = await fetch(`${API_URL}/api/auth/verify-profil`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ userId }),
          credentials: 'include'
        });

        const data = await response.json();
        // logger.info('📥 Réponse de vérification du profil actif à chaque requête :', data);

        if (!response.ok) {
          if (data.code === 'PROFIL_DISABLED') {
            logger.warn('❌ Profil désactivé:', data);
            if (notify) {
              notify(data.message || 'Votre profil a été désactivé', data.toastType || 'error');
            }
            return null;
          }
          throw new Error(data.message || 'Erreur lors de la vérification du profil');
        }

        // Si tout est OK, retourner les données de l'utilisateur
        return data.user;
      } catch (error: any) {
        logger.info('❌ Erreur lors de la vérification du profil actif à chaque requête :', error);

        return {
          error: true,
          shouldLogout: true,
          message: error.message || 'Une erreur est survenue lors de la vérification du profil actif'
        };
      }
    },

    updatePassword: async (currentPassword: string, newPassword: string) => {
      try {
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders();
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/auth/update-password`, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            currentPassword,
            newPassword
          }),
          credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
          if (notify) {
            notify(data.message, data.toastType || 'error');
          }
          throw new Error(data.message || 'Erreur lors de la mise à jour du mot de passe');
        }

        if (notify) {
          notify(data.message, data.toastType || 'success');
        }

        return {
          success: true,
          message: data.message
        };
      } catch (error: any) {
        logger.info('Password update error:', error);
        if (notify) {
          notify(error.message || 'Une erreur est survenue', 'error');
        }
        throw error;
      }
    },

    refreshToken: async () => {
      try {
        // Éviter les appels multiples au refresh token
        if (isRefreshing) {
          logger.info('Renouvellement du token déjà en cours, attente...');
          // Attendre que le renouvellement en cours se termine
          await new Promise(resolve => {
            const checkInterval = setInterval(() => {
              if (!isRefreshing) {
                clearInterval(checkInterval);
                resolve(true);
              }
            }, 100);
          });

          // Vérifier si le dernier renouvellement était récent
          const timeSinceLastRefresh = Date.now() - lastRefreshTime;
          if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
            logger.info('Token récemment renouvelé, pas besoin de renouveler à nouveau');
            return { success: true };
          }
        }

        // Marquer le début du renouvellement
        isRefreshing = true;
        logger.info('Tentative de renouvellement du token');

        // Récupérer un nouveau token CSRF avant de faire la requête
        await fetchCsrfToken();
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;

        // Ajouter un timestamp pour éviter le cache du navigateur
        const timestamp = Date.now();
        const response = await fetch(`${API_URL}/api/auth/refresh-token?_=${timestamp}`, {
          method: 'POST',
          headers,
          credentials: 'include'
        });

        if (!response.ok) {
          const status = response.status;
          let expired = false;
          let data = null;
          try {
            data = await response.json();
            expired = data && data.expired === true;
          } catch (e) {}
          logger.info(`Échec du renouvellement du token (${status})`);
          isRefreshing = false;
          if (status === 401 && expired) {
            notify?.('Session expirée. Veuillez vous reconnecter.', 'error');
            return { success: false, expired: true };
          } else if (status === 401) {
            notify?.('Session expirée. Veuillez vous reconnecter.', 'error');
            return { success: false };
          }
          return { success: false };
        }

        const data = await response.json();

        if (!data.success) {
          logger.info('Réponse de renouvellement non réussie:', data);
          // Si le refresh échoue, déconnecter l'utilisateur
          notify?.('Session expirée. Veuillez vous reconnecter.', 'error');
          isRefreshing = false;
          return { success: false };
        }

        // logger.info('Token renouvelé avec succès');
        // Ne plus écrire le token dans un cookie JS
        // Mettre à jour le timestamp du dernier renouvellement
        lastRefreshTime = Date.now();
        isRefreshing = false;

        return {
          success: true,
          token: data.token
        };
      } catch (error) {
        logger.info('Erreur de refresh du token', error);
        notify?.('Une erreur est survenue lors du refresh du token', 'error');
        isRefreshing = false;
        return { success: false };
      }
    },

    // Nouvelle route local vers api pour l'envoi de l'email de suspension
    sendSuspensionEmail: async (email: string, reason: string) => {
      try {
        const csrfToken = await fetchCsrfToken();
        const headers = await getCommonHeaders(false);
        headers['Content-Type'] = 'application/json';
        headers['X-CSRF-Token'] = csrfToken;
        const response = await fetch(`${API_URL}/api/email/send-suspension`, {
          method: 'POST',
          headers,
          body: JSON.stringify({ email, reason }),
          credentials: 'include'
        });
        return response.json();
      } catch (error) {
        logger.info('Erreur lors de l\'envoi de l\'email de suspension:', error);
        throw error;
      }
    },
  });
};
