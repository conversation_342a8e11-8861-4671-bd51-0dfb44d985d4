import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper,
  CircularProgress,
  Stack,
  Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import RefreshIcon from '@mui/icons-material/Refresh';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { api } from '../../services/api';
import logger from '@/utils/logger';

interface ProfileCompleteCheckProps {
  children: React.ReactNode;
  onClose?: () => void; // Fonction optionnelle pour fermer la modal
}

/**
 * Composant qui vérifie si l'utilisateur a renseigné son nom et prénom
 * avant d'accéder aux fonctionnalités de bug report
 */
const ProfileCompleteCheck: React.FC<ProfileCompleteCheckProps> = ({ children, onClose }) => {
  const { user, updateProfil } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshed, setRefreshed] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);
  const [initialLoadAttempted, setInitialLoadAttempted] = useState(false);
  const [cooldown, setCooldown] = useState(0); // Cooldown en secondes

  // Effet qui se déclenche au montage du composant pour charger les données du profil
  useEffect(() => {
    // Ne charger qu'une seule fois les données et seulement si l'utilisateur est connecté
    if (!initialLoadAttempted && user && user.id) {
      // Marquer que nous avons tenté le chargement initial
      setInitialLoadAttempted(true);
      // Charger les données du profil sans afficher l'indicateur de chargement
      loadProfileData(false);
    }
  }, [user, initialLoadAttempted]);

  // Effet pour gérer le compte à rebours du cooldown
  useEffect(() => {
    let timer: number | null = null;
    
    if (cooldown > 0) {
      timer = window.setInterval(() => {
        setCooldown(prevCooldown => Math.max(0, prevCooldown - 1));
      }, 1000);
    }
    
    return () => {
      if (timer !== null) {
        clearInterval(timer);
      }
    };
  }, [cooldown]);

  // Fonction pour charger les données du profil
  const loadProfileData = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    setError(null);
    
    try {
      // Utiliser la bonne route pour récupérer le profil COMPLET
      const response = await api.get('/api/users/profil');
      
      // Stocker les données dans l'état du composant
      if (response.data) {
        setProfileData(response.data);
      }
      
      // Marquer comme rafraîchi
      setRefreshed(true);
      
    } catch (err: any) {
      logger.error('Erreur lors du chargement du profil:', err);
      if (err.response?.status !== 401) { // Ne pas afficher d'erreur en cas de non-authentification
        setError(err.response?.data?.message || 'Erreur lors du chargement du profil');
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  // Vérifie si l'utilisateur a renseigné son nom et prénom
  const isProfileComplete = () => {
    if (!user) return false;
    
    try {
      // Vérifier si nous avons récupéré des données de profil via l'API
      if (profileData && profileData.profil && profileData.profil.data) {
        const { nom, prenom } = profileData.profil.data;
        if (nom && prenom) {
          return true;
        }
      }
      
      // Cas 1: Structure standard dans AuthContext
      if (user.profil?.data?.nom && user.profil?.data?.prenom) {
        return true;
      }
      
      // Cas 2: Structure alternative possible
      const userAny = user as any;
      
      // Vérifier les autres structures possibles
      if (userAny.profil?.nom && userAny.profil?.prenom) {
        return true;
      }
      
      if (userAny.profil?.data?.data?.nom && userAny.profil?.data?.data?.prenom) {
        return true;
      }
      
      if (userAny.nom && userAny.prenom) {
        return true;
      }
    } catch (error) {
    }
    
    return false;
  };

  const handleGoToProfile = () => {
    if (onClose) {
      onClose(); // Ferme la modal si la fonction onClose est fournie
    }
    navigate('/dashboard/profil');
  };

  const handleRefreshProfile = () => {
    // Vérifier si le bouton n'est pas en cooldown
    if (cooldown === 0) {
      // Utiliser la fonction de chargement du profil avec l'indicateur de chargement
      loadProfileData(true);
      // Activer le cooldown de 5 secondes
      setCooldown(5);
    }
  };

  if (!isProfileComplete()) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          p: { xs: 2, sm: 3 }
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3 }}
          style={{ width: '100%', maxWidth: '450px' }}
        >
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: '12px',
              textAlign: 'center',
              background: '#FFF8F3',
              border: '1px solid rgba(255, 107, 44, 0.1)',
              mx: 'auto'
            }}
          >
            <AccountCircleIcon 
              sx={{ 
                fontSize: 64, 
                color: '#FF6B2C',
                mb: 2
              }} 
            />
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#333' }}>
              Complétez votre profil
            </Typography>
            <Typography variant="body1" sx={{ mb: 2, color: '#555' }}>
              Pour pouvoir signaler un bug ou proposer une amélioration, 
              vous devez d'abord renseigner votre nom et prénom dans votre profil.
              Cette vérification est nécessaire pour assurer la qualité des retours utilisateurs.
            </Typography>
            
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            
            {profileData && profileData.profil && profileData.profil.data && 
             (profileData.profil.data.nom === null || profileData.profil.data.prenom === null) && (
              <Alert severity="info" sx={{ mb: 2 }}>
                Vos informations de profil sont incomplètes. Cliquez sur le bouton ci-dessous pour compléter votre profil.
              </Alert>
            )}
            
            <Stack spacing={2}>
              <Button
                variant="contained"
                onClick={handleGoToProfile}
                fullWidth
                sx={{
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  borderRadius: '8px',
                  py: 1.2,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.95rem'
                }}
              >
                COMPLÉTER MON PROFIL
              </Button>
              
              <Button
                variant="outlined"
                onClick={handleRefreshProfile}
                disabled={loading || cooldown > 0}
                startIcon={loading ? <CircularProgress size={20} /> : 
                          cooldown > 0 ? <AccessTimeIcon /> : <RefreshIcon />}
                sx={{
                  color: '#FF6B2C',
                  borderColor: '#FF6B2C',
                  '&:hover': {
                    borderColor: '#FF7A35',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                  },
                  borderRadius: '8px',
                  py: 1,
                  textTransform: 'none',
                }}
              >
                {loading ? 'Rafraîchissement...' : 
                 cooldown > 0 ? `Patientez (${cooldown}s)` : 
                 'Rafraîchir les données de profil'}
              </Button>
              
              {refreshed && (
                <Typography variant="body2" sx={{ mt: 1, color: '#555', fontStyle: 'italic' }}>
                  Si vous avez déjà complété votre profil et que ce message persiste,
                  veuillez vous déconnecter puis vous reconnecter pour résoudre le problème.
                </Typography>
              )}
            </Stack>
          </Paper>
        </motion.div>
      </Box>
    );
  }

  // Si le profil est complet, afficher les enfants normalement
  return <>{children}</>;
};

export default ProfileCompleteCheck; 