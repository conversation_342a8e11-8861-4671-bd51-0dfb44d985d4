import React, { useState } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  IconButton,
  Card,
  CardContent,
  Avatar,
  Divider,
  Grid,
  CircularProgress,
  Tooltip,
  Badge,
  Stack
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Message as MessageIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useUserManagement } from '../../hooks/useUserManagement';
import DOMPurify from 'dompurify';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  overflow: 'visible',
  marginBottom: theme.spacing(2),
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
  '& .MuiCardContent-root': {
    padding: '14px !important',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

const StyledBadge = styled(Badge)(({ theme }) => ({
  '& .MuiBadge-badge': {
    backgroundColor: COLORS.primary,
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: '0.75rem',
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '6px 12px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
}));

const MessagePreview = styled(Typography)(({ theme }) => ({
  maxWidth: '100%',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
}));

const EmptyStateContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(6),
  backgroundColor: COLORS.lightGray,
  borderRadius: '16px',
  border: `1px dashed ${COLORS.borderColor}`,
}));

interface UserMessagesManagementProps {
  userId: string;
  conversations: any[];
  onUpdate: () => void;
}

const UserMessagesManagement: React.FC<UserMessagesManagementProps> = ({
  userId,
  conversations,
  onUpdate
}) => {
  const { manageUserMessages } = useUserManagement();
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<any>(null);
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [action, setAction] = useState('');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [expandedConversation, setExpandedConversation] = useState<string | null>(null);

  const handleConversationAction = (conversation: any, actionType: string) => {
    setSelectedConversation(conversation);
    setSelectedMessage(null);
    setAction(actionType);
    setActionDialogOpen(true);
  };

  const handleMessageAction = (message: any, actionType: string) => {
    setSelectedMessage(message);
    setSelectedConversation(null);
    setAction(actionType);
    setActionDialogOpen(true);
  };

  const executeAction = async () => {
    if (!action) return;

    try {
      setLoading(true);
      setError(null);

      const requestData: any = {
        action,
        reason
      };

      if (selectedConversation) {
        requestData.conversationId = selectedConversation.id;
      }

      if (selectedMessage) {
        requestData.messageId = selectedMessage.id;
      }

      const result = await manageUserMessages(userId, requestData);

      if (result.success) {
        setActionDialogOpen(false);
        setSelectedConversation(null);
        setSelectedMessage(null);
        setAction('');
        setReason('');
        onUpdate();
      } else {
        setError(result.message || 'Erreur lors de l\'action');
      }
    } catch (error) {
      console.error('Erreur lors de l\'action message:', error);
      setError('Erreur lors de l\'action');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    onUpdate();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const toggleConversationExpand = (conversationId: string) => {
    setExpandedConversation(prev => prev === conversationId ? null : conversationId);
  };

  const getActionLabel = () => {
    switch (action) {
      case 'delete_conversation':
        return 'Supprimer la conversation';
      case 'delete_message':
        return 'Supprimer le message';
      case 'moderate_conversation':
        return 'Modérer la conversation';
      case 'block_user_messages':
        return 'Bloquer l\'envoi de messages';
      case 'unblock_user_messages':
        return 'Débloquer l\'envoi de messages';
      default:
        return 'Action';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOtherUserId = (conversation: any) => {
    return conversation.user1_id === userId ? conversation.user2_id : conversation.user1_id;
  };

  const getOtherUserInfo = (conversation: any) => {
    const otherUserId = getOtherUserId(conversation);
    
    // Essayer de récupérer les informations de l'autre utilisateur
    let otherUser = null;
    
    if (conversation.user1_id === userId && conversation.user2) {
      otherUser = conversation.user2;
    } else if (conversation.user2_id === userId && conversation.user1) {
      otherUser = conversation.user1;
    } else if (conversation.otherUser) {
      otherUser = conversation.otherUser;
    }

    if (otherUser) {
      const nom = otherUser.nom || otherUser.last_name || '';
      const prenom = otherUser.prenom || otherUser.first_name || '';
      const email = otherUser.email || '';
      
      let displayName = '';
      if (prenom && nom) {
        displayName = `${prenom} ${nom.charAt(0).toUpperCase()}.`;
      } else if (prenom) {
        displayName = prenom;
      } else if (nom) {
        displayName = `${nom.charAt(0).toUpperCase()}.`;
      } else if (email) {
        displayName = email.split('@')[0];
      } else {
        displayName = 'Utilisateur';
      }

      return {
        displayName,
        email: email || 'Email non disponible',
        userId: otherUserId
      };
    }

    // Si aucune donnée utilisateur n'est trouvée, essayer de construire un nom à partir de l'ID
    return {
      displayName: `Utilisateur ${otherUserId?.substring(0, 8) || 'Inconnu'}`,
      email: 'Email non disponible',
      userId: otherUserId
    };
  };

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'delete_conversation':
      case 'delete_message':
        return COLORS.error;
      case 'moderate_conversation':
        return COLORS.warning;
      case 'block_user_messages':
        return COLORS.error;
      case 'unblock_user_messages':
        return COLORS.success;
      default:
        return COLORS.primary;
    }
  };

  // Trier les conversations de la plus récente à la plus ancienne
  const sortedConversations = [...conversations].sort((a, b) => {
    const dateA = new Date(a.updated_at || a.created_at);
    const dateB = new Date(b.updated_at || b.created_at);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <Box sx={{ position: 'relative' }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3,
        borderBottom: `3px solid ${COLORS.primary}`,
        pb: 1
      }}>
        <Box>
          <SectionTitle variant="h5">
            Gestion des Messages
          </SectionTitle>
          <Typography variant="body1" color="text.secondary">
            {conversations.length} conversation{conversations.length !== 1 ? 's' : ''} trouvée{conversations.length !== 1 ? 's' : ''}
          </Typography>
        </Box>
        <StyledButton
          variant="contained"
          startIcon={refreshing ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={refreshing}
        >
          Actualiser
        </StyledButton>
      </Box>

      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mb: 2, 
            borderRadius: '12px',
            border: `1px solid ${COLORS.error}20`
          }}
        >
          {error}
        </Alert>
      )}

      {/* Liste des conversations */}
      {sortedConversations.length > 0 ? (
        <Grid container spacing={2}>
          {sortedConversations.map((conversation) => {
            const otherUserInfo = getOtherUserInfo(conversation);
            
            return (
              <Grid size={{ xs: 12, sm: 6 }} key={conversation.id}> 
                <StyledCard>
                  <CardContent sx={{ p: '14px !important' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: COLORS.primary }}>
                          <PersonIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {otherUserInfo.displayName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                            {otherUserInfo.email}
                          </Typography>
                          <Stack direction="row" spacing={1} alignItems="center" sx={{ mt: 1 }}>
                            <Chip 
                              icon={<ScheduleIcon fontSize="small" />} 
                              label={formatDate(conversation.updated_at)}
                              size="small"
                              sx={{ 
                                bgcolor: 'rgba(0,0,0,0.05)', 
                                borderRadius: '16px',
                                '& .MuiChip-label': { px: 1 },
                                '& .MuiChip-icon': { ml: 0.5 }
                              }}
                            />
                            <StyledBadge 
                              badgeContent={conversation.user_messages?.length || 0} 
                              color="primary"
                              max={99}
                              sx={{ '& .MuiBadge-badge': { fontSize: '0.7rem' } }}
                            >
                              <Chip 
                                icon={<MessageIcon fontSize="small" />} 
                                label="Messages"
                                size="small"
                                sx={{ 
                                  bgcolor: 'rgba(0,0,0,0.05)', 
                                  borderRadius: '16px',
                                  '& .MuiChip-label': { px: 1 },
                                  '& .MuiChip-icon': { ml: 0.5 }
                                }}
                              />
                            </StyledBadge>
                            {conversation.is_moderated && (
                              <Chip 
                                label="Modérée" 
                                color="warning" 
                                size="small" 
                                icon={<WarningIcon fontSize="small" />}
                                sx={{ borderRadius: '16px' }}
                              />
                            )}
                          </Stack>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="Voir tous les messages">
                          <IconButton 
                            size="small" 
                            onClick={() => toggleConversationExpand(conversation.id)}
                            sx={{ 
                              color: COLORS.info,
                              bgcolor: `${COLORS.info}15`,
                              '&:hover': { bgcolor: `${COLORS.info}25` }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Modérer la conversation">
                          <IconButton 
                            size="small" 
                            onClick={() => handleConversationAction(conversation, 'moderate_conversation')}
                            sx={{ 
                              color: COLORS.warning,
                              bgcolor: `${COLORS.warning}15`,
                              '&:hover': { bgcolor: `${COLORS.warning}25` }
                            }}
                          >
                            <ReportIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Supprimer la conversation">
                          <IconButton 
                            size="small" 
                            onClick={() => handleConversationAction(conversation, 'delete_conversation')}
                            sx={{ 
                              color: COLORS.error,
                              bgcolor: `${COLORS.error}15`,
                              '&:hover': { bgcolor: `${COLORS.error}25` }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>

                    {/* Messages de la conversation */}
                    {conversation.user_messages && conversation.user_messages.length > 0 && (
                      <Box>
                        <Divider sx={{ mb: 2 }} />
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle2" fontWeight="medium">
                            Messages récents
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {expandedConversation === conversation.id 
                              ? 'Tous les messages' 
                              : `${Math.min(3, conversation.user_messages.length)} sur ${conversation.user_messages.length}`}
                          </Typography>
                        </Box>
                        <List dense sx={{ bgcolor: 'rgba(0,0,0,0.02)', borderRadius: '8px', py: 0 }}>
                          {(expandedConversation === conversation.id 
                            ? conversation.user_messages 
                            : conversation.user_messages.slice(0, 3)
                          ).map((message: any) => (
                            <ListItem 
                              key={message.id} 
                              sx={{ 
                                pl: 2, 
                                pr: 6, 
                                py: 1,
                                borderBottom: '1px solid rgba(0,0,0,0.05)',
                                '&:last-child': { borderBottom: 'none' }
                              }}
                            >
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Chip 
                                      label={message.sender_id === userId ? 'Utilisateur' : 'Autre'}
                                      size="small"
                                      sx={{ 
                                        bgcolor: message.sender_id === userId ? `${COLORS.primary}15` : 'rgba(0,0,0,0.05)',
                                        color: message.sender_id === userId ? COLORS.primary : 'text.primary',
                                        fontWeight: 'medium',
                                        borderRadius: '16px',
                                        height: '22px'
                                      }}
                                    />
                                    <Typography variant="caption" color="text.secondary">
                                      {formatDate(message.created_at)}
                                    </Typography>
                                  </Box>
                                }
                                secondary={
                                  <MessagePreview
                                    dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(message.content) }}
                                  />
                                }
                              />
                              <ListItemSecondaryAction>
                                <Tooltip title="Supprimer ce message">
                                  <IconButton
                                    edge="end"
                                    size="small"
                                    onClick={() => handleMessageAction(message, 'delete_message')}
                                    sx={{ 
                                      color: COLORS.error,
                                      bgcolor: `${COLORS.error}15`,
                                      '&:hover': { bgcolor: `${COLORS.error}25` }
                                    }}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </ListItemSecondaryAction>
                            </ListItem>
                          ))}
                        </List>
                        {conversation.user_messages.length > 3 && expandedConversation !== conversation.id && (
                          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                            <Button 
                              size="small" 
                              onClick={() => toggleConversationExpand(conversation.id)}
                              sx={{ 
                                color: COLORS.primary, 
                                fontSize: '0.75rem',
                                '&:hover': { bgcolor: `${COLORS.primary}10` }
                              }}
                            >
                              Voir tous les messages ({conversation.user_messages.length})
                            </Button>
                          </Box>
                        )}
                        {expandedConversation === conversation.id && (
                          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                            <Button 
                              size="small" 
                              onClick={() => setExpandedConversation(null)}
                              sx={{ 
                                color: COLORS.neutral, 
                                fontSize: '0.75rem',
                                '&:hover': { bgcolor: 'rgba(0,0,0,0.05)' }
                              }}
                            >
                              Réduire
                            </Button>
                          </Box>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </StyledCard>
              </Grid>
            );
          })}
        </Grid>
      ) : (
        <EmptyStateContainer>
          <MessageIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Aucune conversation
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center">
            Cet utilisateur n'a aucune conversation active.
            <br />
            Les conversations apparaîtront ici lorsque l'utilisateur commencera à échanger des messages.
          </Typography>
        </EmptyStateContainer>
      )}

      {/* Dialog de confirmation d'action */}
      <Dialog 
        open={actionDialogOpen} 
        onClose={() => setActionDialogOpen(false)} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: `1px solid ${COLORS.borderColor}`,
          bgcolor: 'rgba(0,0,0,0.02)',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <Box 
            sx={{ 
              width: 32, 
              height: 32, 
              borderRadius: '50%', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              bgcolor: `${getActionColor(action)}15`,
              color: getActionColor(action),
              mr: 1
            }}
          >
            {action.includes('delete') && <DeleteIcon fontSize="small" />}
            {action.includes('moderate') && <ReportIcon fontSize="small" />}
            {action.includes('block') && <BlockIcon fontSize="small" />}
            {action.includes('unblock') && <CheckCircleIcon fontSize="small" />}
          </Box>
          <Typography variant="h6">{getActionLabel()}</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ mb: 3 }}>
            {selectedConversation && (
              <Alert severity="info" sx={{ mb: 2, borderRadius: '8px' }}>
                <Typography variant="body2">
                  Conversation avec {getOtherUserInfo(selectedConversation).displayName}
                </Typography>
              </Alert>
            )}
            {selectedMessage && (
              <Alert severity="info" sx={{ mb: 2, borderRadius: '8px' }}>
                <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 0.5 }}>
                  Message sélectionné :
                </Typography>
                <Typography variant="body2" sx={{ 
                  p: 1, 
                  bgcolor: 'rgba(255,255,255,0.5)', 
                  borderRadius: '4px',
                  border: '1px solid rgba(0,0,0,0.05)'
                }}>
                  {selectedMessage.content.length > 100 
                    ? `${selectedMessage.content.substring(0, 100)}...` 
                    : selectedMessage.content}
                </Typography>
              </Alert>
            )}
            {!selectedConversation && !selectedMessage && (
              <Alert severity="warning" sx={{ mb: 2, borderRadius: '8px' }}>
                <Typography variant="body2">
                  Cette action s'appliquera à tous les messages de l'utilisateur
                </Typography>
              </Alert>
            )}
          </Box>
          
          <TextField
            fullWidth
            label="Motif (optionnel)"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            placeholder="Expliquez la raison de cette action..."
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                '&:hover fieldset': {
                  borderColor: COLORS.primary,
                },
                '&.Mui-focused fieldset': {
                  borderColor: COLORS.primary,
                },
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${COLORS.borderColor}` }}>
          <Button 
            onClick={() => setActionDialogOpen(false)}
            sx={{ 
              color: COLORS.neutral,
              '&:hover': { bgcolor: 'rgba(0,0,0,0.05)' },
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 'medium'
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={executeAction}
            disabled={loading}
            variant="contained"
            sx={{
              bgcolor: getActionColor(action),
              '&:hover': { bgcolor: action.includes('delete') ? '#d32f2f' : action.includes('moderate') ? '#ed6c02' : COLORS.secondary },
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 'medium',
              px: 3
            }}
          >
            {loading ? (
              <CircularProgress size={24} sx={{ color: COLORS.white }} />
            ) : (
              getActionLabel()
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserMessagesManagement;
