import React from 'react';
import { Box, Container, Typography, Breadcrumbs, Link as MuiLink, Button, Paper, Divider } from '@mui/material';
import { Link, useParams } from 'react-router-dom';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import BugReportDetail from '../../../components/BugReport/BugReportDetail';

interface AdminBugReportDetailPageProps {
  isEditing?: boolean;
}

const AdminBugReportDetailPage: React.FC<AdminBugReportDetailPageProps> = ({ isEditing = false }) => {
  const { id } = useParams<{ id: string }>();

  return (
    <Box 
      sx={{ 
        minHeight: '80vh',
        background: 'linear-gradient(180deg, #FFF8F3 0%, rgba(255, 255, 255, 0.9) 100%)',
        py: 4
      }}
    >
      <Container maxWidth="lg">
        <Paper 
          elevation={2} 
          sx={{ 
            borderRadius: '12px', 
            overflow: 'hidden',
            mb: 4
          }}
        >
          <Box 
            sx={{ 
              p: { xs: 1.5, sm: 2.5 }, 
              background: 'linear-gradient(90deg, #FF6B2C 0%, #FF7A35 100%)',
            }}
          >
            <Breadcrumbs 
              sx={{ 
                mb: 1,
                '& .MuiBreadcrumbs-ol': { 
                  flexWrap: { xs: 'wrap', sm: 'nowrap' } 
                },
                '& .MuiBreadcrumbs-li': { 
                  minWidth: 0,
                  '& a, & p': {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    color: 'white',
                    '&:hover': {
                      color: '#FFE4BA',
                    }
                  }
                }
              }}
              separator={<Box sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>/</Box>}
            >
              <MuiLink component={Link} to="/admin" sx={{ color: 'white !important', fontWeight: 500 }}>
                Administration
              </MuiLink>
              <MuiLink component={Link} to="/admin/bug-reports" sx={{ color: 'white !important', fontWeight: 500 }}>
                Gestion des bugs
              </MuiLink>
              <Typography color="white" sx={{ fontWeight: 600 }}>
                {isEditing ? "Édition" : "Détail"} du rapport
              </Typography>
            </Breadcrumbs>

            <Box sx={{ 
              display: 'flex', 
              flexDirection: { xs: 'column', sm: 'row' },
              justifyContent: 'space-between', 
              alignItems: { xs: 'flex-start', sm: 'center' },
              gap: { xs: 2, sm: 1 }
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {isEditing ? 
                  <EditIcon sx={{ color: 'white', mr: 1.5, fontSize: '1.8rem' }} /> :
                  <VisibilityIcon sx={{ color: 'white', mr: 1.5, fontSize: '1.8rem' }} />
                }
                <Typography 
                  variant="h4" 
                  component="h1" 
                  sx={{ 
                    color: 'white',
                    fontWeight: 600,
                    fontSize: { xs: '1.2rem', sm: '1.5rem', md: '2rem' },
                    wordBreak: 'break-word'
                  }}
                >
                  {isEditing ? "Édition du rapport" : "Détails du rapport"} #{id?.substring(0, 8)}
                </Typography>
              </Box>
              <Button
                component={Link}
                to="/admin/bug-reports"
                variant="contained"
                color="inherit"
                startIcon={<ArrowBackIcon />}
                size="small"
                sx={{ 
                  bgcolor: 'white',
                  color: '#FF6B2C',
                  fontWeight: 600,
                  whiteSpace: 'nowrap',
                  '&:hover': {
                    bgcolor: '#FFE4BA',
                  },
                  boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
                }}
              >
                Retour à la liste
              </Button>
            </Box>
          </Box>
        </Paper>

        <Paper
          elevation={3}
          sx={{
            position: 'relative',
            borderRadius: '12px',
            overflow: 'hidden',
            transition: 'all 0.3s ease',
            ...(isEditing && {
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '4px',
                height: '100%',
                background: 'linear-gradient(to bottom, #FF6B2C, #FF965E)',
              }
            })
          }}
        >
          <Box sx={{ p: { xs: 2, md: 3 } }}>
            {isEditing && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" color="#FF6B2C" sx={{ mb: 1, fontWeight: 600 }}>
                  Mode édition
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Vous pouvez modifier les informations du rapport et enregistrer vos changements.
                </Typography>
                <Divider sx={{ mt: 2, borderColor: 'rgba(255, 107, 44, 0.2)' }} />
              </Box>
            )}
            
            <BugReportDetail isAdmin={true} initialEditMode={isEditing} />
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default AdminBugReportDetailPage; 