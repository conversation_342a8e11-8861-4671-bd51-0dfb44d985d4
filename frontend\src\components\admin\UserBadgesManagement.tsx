import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Chip,
  Avatar,
  Grid,
  Paper,
  Divider,
  Modal,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Award,
  AlertCircle,
  X as CloseIcon
} from 'lucide-react';
import { badges as AVAILABLE_BADGES, Badge as BadgeType } from '../../pages/dashboard/profil/badges';

interface UserBadgesManagementProps {
  userId: string;
  badges: any[];
  onUpdate: () => void;
}

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
}));

const BadgeCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: '12px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  textAlign: 'center',
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  border: `1px solid ${COLORS.borderColor}`,
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const EmptyStateBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(6),
  backgroundColor: COLORS.lightGray,
  borderRadius: '16px',
  border: `1px dashed ${COLORS.borderColor}`,
}));

const IconBox = styled(Box)<{ color?: string }>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  marginBottom: theme.spacing(2),
  width: '48px',
  height: '48px',
}));

const UserBadgesManagement: React.FC<UserBadgesManagementProps> = ({
  badges
}) => {
  const [error, setError] = useState<string | null>(null);
  const [openModal, setOpenModal] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState<BadgeType | null>(null);

  const getBadgeInfo = (badgeId: string): BadgeType | null => {
    return AVAILABLE_BADGES.find(b => b.id === badgeId) || null;
  };

  const handleOpenModal = (badgeInfo: BadgeType) => {
    setSelectedBadge(badgeInfo);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedBadge(null);
  };

  const modalStyle = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    borderRadius: '16px',
    boxShadow: 24,
    p: 4,
    outline: 'none',
  };

  return (
    <Box sx={{ width: '100%' }}>
      <StyledPaper>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Award size={24} color={COLORS.primary} style={{ marginRight: '12px' }} />
            <SectionTitle>
              Gestion des Badges ({badges.length})
            </SectionTitle>
          </Box>
        </Box>

        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center'
            }}
            icon={<AlertCircle size={24} />}
          >
            {error}
          </Alert>
        )}

        <Divider sx={{ mb: 3 }} />

        {/* Badges actuels */}
        {badges.length > 0 ? (
          <Grid container spacing={3}>
            {badges.map((badge) => {
              const badgeInfo = getBadgeInfo(badge.badge_id);
              if (!badgeInfo) return null; // Ne pas afficher si le badge n'est pas trouvé
              return (
                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={badge.id}>
                  <BadgeCard
                    onClick={() => handleOpenModal(badgeInfo)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <Box sx={{ position: 'relative', width: '100%' }}>
                      <Avatar
                        sx={{
                          bgcolor: badgeInfo.backgroundColor,
                          width: 56,
                          height: 56,
                          margin: '0 auto 16px',
                          boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
                        }}
                      >
                        {React.cloneElement(badgeInfo.icon as React.ReactElement<{ size?: number; color?: string; className?: string; }>, { size: 20, color: badgeInfo.iconColor })}
                      </Avatar>
                    </Box>
                    <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                      {badgeInfo.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {badgeInfo.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block">
                      Obtenu le {new Date(badge.date_obtention).toLocaleDateString('fr-FR')}
                    </Typography>
                    {badge.is_lifetime && (
                      <Chip
                        label="À vie"
                        size="small"
                        color="success"
                        sx={{ mt: 1, borderRadius: '6px' }}
                      />
                    )}
                  </BadgeCard>
                </Grid>
              );
            })}
          </Grid>
        ) : (
          <EmptyStateBox>
            <IconBox>
              <Award size={24} />
            </IconBox>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Aucun badge attribué
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center" sx={{ maxWidth: '400px', mb: 2 }}>
              Cet utilisateur n'a encore aucun badge. Les badges permettent de reconnaître les accomplissements et qualités des utilisateurs.
            </Typography>
          </EmptyStateBox>
        )}

        {/* Modale pour les détails du badge */}
        <Modal
          open={openModal}
          onClose={handleCloseModal}
          aria-labelledby="badge-modal-title"
          aria-describedby="badge-modal-description"
        >
          <Box sx={modalStyle}>
            {selectedBadge && (
              <>
                <IconButton
                  aria-label="close"
                  onClick={handleCloseModal}
                  sx={{
                    position: 'absolute',
                    right: 8,
                    top: 8,
                    color: (theme) => theme.palette.grey[500],
                  }}
                >
                  <CloseIcon />
                </IconButton>
                <Typography id="badge-modal-title" variant="h5" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: selectedBadge.backgroundColor,
                      width: 48,
                      height: 48,
                      marginRight: 2,
                      boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
                    }}
                  >
                    {React.cloneElement(selectedBadge.icon as React.ReactElement<{ size?: number; color?: string; className?: string; }>, { size: 24, color: selectedBadge.iconColor })}
                  </Avatar>
                  {selectedBadge.title}
                </Typography>
                <Typography id="badge-modal-description" sx={{ mt: 2, textAlign: 'center', lineHeight: 1.6 }}>
                  {selectedBadge.detailedDescription || selectedBadge.description}
                </Typography>
              </>
            )}
          </Box>
        </Modal>
      </StyledPaper>
    </Box>
  );
};

export default UserBadgesManagement;
