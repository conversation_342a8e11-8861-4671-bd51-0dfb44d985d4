import React, { useState, useEffect } from 'react';
import { <PERSON>, Edit2, Trash2, X, ChevronLeft, ChevronRight, MapPin, Tag, Info, Pencil } from 'lucide-react';
import { notify } from '../../../components/Notification';
import logger from '../../../utils/logger';
import { fetchFeaturedPhotos, uploadFeaturedPhoto, deleteFeaturedPhoto, updateFeaturedPhotoCaption, fetchUserFeaturedPhotos } from './api';
import ModalPortal from '../../../components/ModalPortal';
import DOMPurify from 'dompurify';
import { CaptionInput } from './CaptionInput';
import { motion, PanInfo } from 'framer-motion';
import imageCompression from 'browser-image-compression';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { Tooltip } from '@mui/material';
import useImageModeration from '../../../hooks/useImageModeration';
import ImageModerationStatus from '../../../components/ImageModerationStatus';
import RejectedImageMessage from '../../../components/RejectedImageMessage';
import AiGalleryImageGenerator from '../../../components/ai/AiGalleryImageGenerator';


const MAX_FEATURED_PHOTOS = 3;
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

interface FeaturedPhoto {
  id: string;
  photo_url: string;
  caption?: string;
}

const FeaturedPhotos: React.FC = () => {
  const { user } = useAuth();
  const { slug } = useParams();
  const [isOwnProfil] = useState(!slug || slug === user?.profil?.data?.slug);
  const [photos, setPhotos] = useState<FeaturedPhoto[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<FeaturedPhoto | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [editingCaption, setEditingCaption] = useState('');
  const [compressionProgress, setCompressionProgress] = useState<number>(0);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState<number>(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [moderationPreviewUrl, setModerationPreviewUrl] = useState<string | null>(null);
  const [isModerationModalOpen, setIsModerationModalOpen] = useState(false);
  const { moderateImage, isLoading: isModerationLoading } = useImageModeration();
  const [isImageRejected, setIsImageRejected] = useState(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();

  const loadPhotos = async () => {
    try {
      if (!isOwnProfil && !slug) {
        return;
      }

      const response = isOwnProfil
        ? await fetchFeaturedPhotos()
        : await fetchUserFeaturedPhotos(slug as string);

      if (response.success && Array.isArray(response.photos)) {
        setPhotos(response.photos);
      }
    } catch (err) {
      logger.error('Erreur lors du chargement des photos mises en avant:', err);
    }
  };

  useEffect(() => {
    loadPhotos();
  }, [slug, isOwnProfil]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    if (photos.length + files.length > MAX_FEATURED_PHOTOS) {
      notify(`Vous ne pouvez pas ajouter plus de ${MAX_FEATURED_PHOTOS} photos mises en avant.`, 'error');
      event.target.value = '';
      return;
    }

    // Vérifier le type et la taille du fichier
    const file = files[0];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      notify(`Le fichier ${file.name} n'est pas un format d'image valide. Formats acceptés : JPG, PNG, WEBP`, 'error');
      event.target.value = '';
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      notify(`Le fichier ${file.name} dépasse la taille maximale de 5MB`, 'error');
      event.target.value = '';
      return;
    }

    try {
      // Compresser l'image avant la modération
      setCompressionProgress(1);
      const compressed = await imageCompression(file, {
        maxSizeMB: 0.4,
        maxWidthOrHeight: 400,
        useWebWorker: true,
        onProgress: (progress) => {
          setCompressionProgress(Math.round(progress));
        }
      });
      setCompressionProgress(100);

      // Créer une URL pour la prévisualisation
      const objectUrl = URL.createObjectURL(compressed);
      setModerationPreviewUrl(objectUrl);

      // Ouvrir la modale de modération et lancer la modération automatiquement
      setIsModerationModalOpen(true);

      // Lancer la modération automatiquement avec un délai plus long
      if (compressed) {
        handleModerationWithFile(compressed);
      }

      // Réinitialiser le champ de fichier
      event.target.value = '';

    } catch (error) {
      logger.error(`Erreur lors de la compression de ${file.name}:`, error);
      notify(`Erreur lors de la compression de ${file.name}`, 'error');
      event.target.value = '';
      setCompressionProgress(0);
    }
  };

  const handleModerationWithFile = async (file: File) => {
    setIsUploading(true);
    try {
      // Modérer l'image
      const tempImageId = `image-temp-${Date.now()}`;
      const result = await moderateImage(file, 'featured', tempImageId);

      if (result.isSafe) {
        // L'image est sûre, on peut l'envoyer
        notify('Image validée par la modération', 'success');

        // Réinitialiser les états de rejet
        setIsImageRejected(false);
        setRejectionDescription(undefined);
        setRejectionImprovementSuggestions(undefined);

        // Fermer la modale de modération
        setIsModerationModalOpen(false);

        // Uploader l'image
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('tempImageId', tempImageId); // Ajouter l'ID temporaire
        const response = await uploadFeaturedPhoto(formData);

        if (response.photo) {
          setPhotos(prev => [...prev, response.photo]);
          notify('Photo ajoutée avec succès', 'success');
        }
      } else {
        // L'image est inappropriée
        notify('Image refusée : ne respecte pas nos règles de modération', 'error');

        // Afficher un message plus détaillé dans la console pour le débogage
        logger.info('Image refusée par la modération', {
          description: result.description,
          contentType: 'featured'
        });

        // Mettre à jour les états pour afficher le message de rejet
        setIsImageRejected(true);
        setRejectionDescription(result.description);
        setRejectionImprovementSuggestions(result.improvementSuggestions);

        // S'assurer que la modale reste ouverte pour afficher le message détaillé
        setIsModerationModalOpen(true);

        // Ajouter un log pour le débogage
        logger.info('Modale de rejet ouverte', {
          isImageRejected: true,
          isModerationModalOpen: true,
          description: result.description
        });
      }
    } catch (err) {
      logger.error('Erreur lors de la modération ou de l\'upload de l\'image', err);
      notify('Erreur lors de la vérification ou de l\'upload de l\'image. Veuillez réessayer.', 'error');

      // En cas d'erreur, fermer la modale et réinitialiser les états
      setIsModerationModalOpen(false);
      if (moderationPreviewUrl) {
        URL.revokeObjectURL(moderationPreviewUrl);
        setModerationPreviewUrl(null);
      }
    } finally {
      // Ne pas fermer la modale ici, car nous voulons afficher le message de rejet si l'image est refusée
      // La modale sera fermée dans le cas où l'image est acceptée ou en cas d'erreur

      // Réinitialiser la progression de compression et l'état d'upload
      setIsUploading(false);
      setCompressionProgress(0);
    }
  };

  const handleDeletePhoto = async () => {
    if (!selectedPhoto) return;

    try {
      await deleteFeaturedPhoto(selectedPhoto.id);
      setPhotos(prev => prev.filter(p => p.id !== selectedPhoto.id));
      notify('Photo supprimée avec succès', 'success');
      setIsDeleteModalOpen(false);
      setSelectedPhoto(null);
    } catch (error) {
      logger.error('Erreur lors de la suppression de la photo:', error);
      notify('Erreur lors de la suppression de la photo', 'error');
    }
  };

  const handleUpdateCaption = async () => {
    if (!selectedPhoto) return;

    // Vérifier si la légende a été modifiée
    const sanitizedCaption = DOMPurify.sanitize(editingCaption);
    if (sanitizedCaption === selectedPhoto.caption) {
      setIsEditModalOpen(false);
      setSelectedPhoto(null);
      setEditingCaption('');
      notify('Aucune modification de légende détectée', 'info');
      return;
    }

    try {
      await updateFeaturedPhotoCaption(selectedPhoto.id, sanitizedCaption);
      setPhotos(prev => prev.map(p =>
        p.id === selectedPhoto.id ? { ...p, caption: sanitizedCaption } : p
      ));
      notify('Légende mise à jour avec succès', 'success');
      setIsEditModalOpen(false);
      setSelectedPhoto(null);
      setEditingCaption('');
    } catch (error) {
      logger.error('Erreur lors de la mise à jour de la légende:', error);
      const errorMessage = error instanceof Error ? error.message : 'Une erreur inattendue est survenue';
      notify('Erreur lors de la mise à jour de la légende : ' + errorMessage, 'error');
    }
  };

  const handleModalDragEnd = (_: any, info: PanInfo) => {
    const swipeThreshold = 50;
    if (Math.abs(info.offset.x) > swipeThreshold) {
      if (info.offset.x > 0 && currentPhotoIndex > 0) {
        // Swipe vers la droite - photo précédente
        setCurrentPhotoIndex(currentPhotoIndex - 1);
        setSelectedPhoto(photos[currentPhotoIndex - 1]);
        setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
      } else if (info.offset.x < 0 && currentPhotoIndex < photos.length - 1) {
        // Swipe vers la gauche - photo suivante
        setCurrentPhotoIndex(currentPhotoIndex + 1);
        setSelectedPhoto(photos[currentPhotoIndex + 1]);
        setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
      }
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const swipeThreshold = 50;
    const distance = touchStart - touchEnd;

    if (Math.abs(distance) > swipeThreshold) {
      if (distance > 0 && currentPhotoIndex < photos.length - 1) {
        // Swipe vers la gauche - photo suivante
        setCurrentPhotoIndex(currentPhotoIndex + 1);
        setSelectedPhoto(photos[currentPhotoIndex + 1]);
        setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
      } else if (distance < 0 && currentPhotoIndex > 0) {
        // Swipe vers la droite - photo précédente
        setCurrentPhotoIndex(currentPhotoIndex - 1);
        setSelectedPhoto(photos[currentPhotoIndex - 1]);
        setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
      }
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  const renderPhotoSlot = (index: number) => {
    const photo = photos[index];

    // Slot photo existante
    if (photo) {
      return (
        <div
          key={photo.id}
          className="relative group aspect-[4/3] rounded-xl overflow-hidden cursor-pointer"
          onClick={() => {
            setSelectedPhoto(photo);
            setEditingCaption(photo.caption || '');
            setIsEditModalOpen(true);
          }}
        >
          <img
            src={photo.photo_url}
            alt={photo.caption || 'Photo mise en avant'}
            className="w-full h-full object-cover transform transition-transform duration-300 group-hover:scale-105"
          />

          {/* Overlay sombre toujours présent mais plus ou moins opaque */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent transition-opacity duration-200 opacity-40 group-hover:opacity-80">
            {/* Légende */}
            <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform transition-transform duration-200 translate-y-2 group-hover:translate-y-0">
              <div className="relative">
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-0 group-hover:h-full bg-[#FF6B2C]/80 transition-all duration-300 rounded-full" />
                <p className="text-sm font-medium line-clamp-2 pl-3">
                  {photo.caption || 'Aucune légende'}
                </p>
              </div>
            </div>

            {/* Boutons d'action */}
            {isOwnProfil && (
              <div className="absolute top-4 right-4 jp-gallery-actions flex items-center gap-1 z-10 transform transition-all duration-200 md:translate-y-[-1rem] md:opacity-0 md:group-hover:translate-y-0 md:group-hover:opacity-100">
                <Tooltip title="Modifier la légende">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedPhoto(photo);
                      setEditingCaption(photo.caption || '');
                      setIsEditModalOpen(true);
                    }}
                    className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-[#FF6B2C] rounded-lg transition-colors shadow-sm"
                  >
                    <Pencil className="h-5 w-5" />
                  </button>
                </Tooltip>
                <Tooltip title="Supprimer la photo">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedPhoto(photo);
                      setIsDeleteModalOpen(true);
                    }}
                    className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-red-500 rounded-lg transition-colors shadow-sm"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </Tooltip>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Slot upload classique (toujours le premier slot vide)
    if (isOwnProfil && index === photos.length) {
      return (
        <div key="upload-slot" className="relative group aspect-[4/3]">
          <label className="cursor-pointer h-full block">
            <div className="h-full rounded-xl border-2 border-dashed border-gray-200 hover:border-[#FF6B2C] transition-all duration-300 flex flex-col items-center justify-center bg-[#FFF8F3] hover:bg-[#FFE4BA]/20">
              <Camera className="h-8 w-8 text-gray-400 group-hover:text-[#FF6B2C] mb-2 transition-colors" />
              <span className="text-sm text-gray-500 group-hover:text-[#FF6B2C] transition-colors">
                Parcourir
              </span>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileUpload}
                disabled={isUploading}
              />
            </div>
          </label>
          {isUploading && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-xl">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-[#FF6B2C] border-t-transparent"></div>
                <span className="text-sm text-[#FF6B2C]">Upload en cours...</span>
              </div>
            </div>
          )}
          {/* Barre de progression compression image */}
          {compressionProgress > 0 && compressionProgress < 100 && (
            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
              <div className="bg-[#FF6B2C] h-2 rounded-full transition-all duration-200" style={{ width: `${compressionProgress}%` }} />
              <div className="text-xs text-center mt-1 text-gray-500">Compression en cours… {compressionProgress}%</div>
            </div>
          )}
        </div>
      );
    }

    // Slot IA (toujours après le slot upload, si on n'a pas atteint le max)
    if (isOwnProfil && index === photos.length + 1 && photos.length < MAX_FEATURED_PHOTOS) {
      return (
        <div key="ai-slot" className="relative group aspect-[4/3]">
          <AiGalleryImageGenerator
            onImageGenerated={(_imageUrl, _imageBase64, photoObj) => {
              // Utiliser directement la photo retournée par le backend IA
              if (photoObj && photoObj.photo_url) {
                // Forcer l'id à être une string (jamais undefined)
                const safePhoto: FeaturedPhoto = {
                  photo_url: photoObj.photo_url,
                  id: photoObj.id ? photoObj.id : Date.now().toString(),
                  caption: photoObj.caption || ''
                };
                setPhotos(prev => [...prev, safePhoto]);
                notify('Photo mise en avant ajoutée avec succès', 'success');
              } else {
                notify('Erreur lors de l\'ajout de l\'image générée', 'error');
              }
            }}
            isPurposeFeatured={true}
            className="h-full"
          />
        </div>
      );
    }

    // Slot vide pour les autres cas (visiteur)
    return (
      <div key={`empty-${index}`} className="h-full rounded-xl border-2 border-gray-100 flex flex-col items-center justify-center bg-gray-50 aspect-[4/3]">
        <Camera className="h-8 w-8 text-gray-300 mb-2" />
        <span className="text-sm text-gray-400">
          Aucune photo
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-6 px-2 md:px-0">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-[#FFF8F3] rounded-lg">
            <Camera className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <h3 className="text-xl font-bold text-gray-800">Photos mises en avant</h3>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {isOwnProfil ? (
          [
            ...photos.map((_, index) => renderPhotoSlot(index)),
            ...(photos.length < MAX_FEATURED_PHOTOS ? [renderPhotoSlot(photos.length)] : []),
            ...(photos.length < MAX_FEATURED_PHOTOS ? [renderPhotoSlot(photos.length + 1)] : []),
          ]
        ) : photos.length > 0 ? (
          photos.map((_, index) => renderPhotoSlot(index))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center p-8 bg-[#FFF8F3] rounded-xl text-center">
            <Camera className="h-12 w-12 text-[#FF6B2C] mb-4 opacity-50" />
            <h4 className="text-lg font-medium text-gray-800 mb-2">Aucune photo mise en avant</h4>
            <p className="text-gray-600">Cet utilisateur n'a pas encore ajouté de photos mises en avant.</p>
          </div>
        )}
      </div>
      {isOwnProfil && (
        <p className="mt-4 text-sm text-gray-600">
          Ces photos mettent en valeur vos services et offrent aux clients potentiels un aperçu de votre travail en un clin d'œil.<br />
          Assurez-vous de présenter vos meilleures réalisations pour capter leur attention.
        </p>
      )}

      {/* Modal de modification de la légende */}
      {isEditModalOpen && selectedPhoto && (
        <ModalPortal>
          <div
            className="fixed inset-0 flex items-center justify-center p-4 z-[60]"
            onClick={() => setIsEditModalOpen(false)}
          >
            <div
              className="bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] relative max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="absolute top-4 right-4 text-[#FF6B2C] hover:text-[#FF965E] transition-colors duration-300 hover:scale-110 transform z-50"
              >
                <X className="h-7 w-7" />
              </button>

              <div className="bg-white p-6 rounded-xl shadow-sm mb-8">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-[#FFF8F3] rounded-lg">
                    <Camera className="h-6 w-6 text-[#FF6B2C]" />
                  </div>
                  <h2 className="text-2xl font-bold text-[#2D3748]">Photo mise en avant</h2>
                </div>
              </div>

              <div className="relative group mb-8">
                {/* Navigation buttons */}
                {currentPhotoIndex > 0 && (
                  <button
                    onClick={() => {
                      setCurrentPhotoIndex(currentPhotoIndex - 1);
                      setSelectedPhoto(photos[currentPhotoIndex - 1]);
                      setEditingCaption(photos[currentPhotoIndex - 1].caption || '');
                    }}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-r-2xl shadow-lg transition-all duration-300 z-50 group-hover:translate-x-0 -translate-x-2 opacity-90 hover:opacity-100"
                  >
                    <ChevronLeft className="h-6 w-6 text-[#FF6B2C]" />
                  </button>
                )}
                {currentPhotoIndex < photos.length - 1 && (
                  <button
                    onClick={() => {
                      setCurrentPhotoIndex(currentPhotoIndex + 1);
                      setSelectedPhoto(photos[currentPhotoIndex + 1]);
                      setEditingCaption(photos[currentPhotoIndex + 1].caption || '');
                    }}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-l-2xl shadow-lg transition-all duration-300 z-50 group-hover:translate-x-0 translate-x-2 opacity-90 hover:opacity-100"
                  >
                    <ChevronRight className="h-6 w-6 text-[#FF6B2C]" />
                  </button>
                )}

                <motion.div
                  className="relative overflow-hidden rounded-xl"
                  drag="x"
                  dragConstraints={{ left: 0, right: 0 }}
                  onDragEnd={handleModalDragEnd}
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                >
                  <img
                    src={selectedPhoto.photo_url}
                    alt={selectedPhoto.caption || 'Photo mise en avant'}
                    className="w-full h-[400px] object-contain rounded-xl shadow-md"
                    draggable="false"
                  />
                </motion.div>
              </div>

              <div className={isOwnProfil ? "grid grid-cols-1 md:grid-cols-2 gap-6 mt-8" : "mt-8"}>
                <div className={`bg-white p-6 rounded-xl shadow-md ${!isOwnProfil ? "w-full" : ""}`}>
                  <div className="flex items-center gap-3 mb-4">
                    <MapPin className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Informations</h3>
                  </div>
                  <div className="space-y-3">
                    <p className="text-gray-600">
                      {isOwnProfil
                        ? "Photo mise en avant de votre profil"
                        : "Photo mise en avant de ce profil"
                      }
                    </p>
                    <div className="pt-2 border-t border-gray-100">
                      <p className="text-gray-600">
                        <span className="font-medium text-gray-700">Légende :</span>{' '}
                        {selectedPhoto.caption || 'Aucune légende'}
                      </p>
                    </div>
                  </div>
                </div>

                {isOwnProfil && (
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <Tag className="h-6 w-6 text-[#FF6B2C]" />
                      <h3 className="text-xl font-semibold text-gray-800">Légende</h3>
                    </div>
                    <CaptionInput
                      value={editingCaption}
                      onChange={setEditingCaption}
                    />
                  </div>
                )}
              </div>

              {isOwnProfil && (
                <div className="mt-8 bg-white p-6 rounded-xl shadow-md">
                  <div className="flex items-center gap-3 mb-4">
                    <Info className="h-6 w-6 text-[#FF6B2C]" />
                    <h3 className="text-xl font-semibold text-gray-800">Actions</h3>
                  </div>
                  <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3">
                    <button
                      onClick={() => setIsDeleteModalOpen(true)}
                      className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors flex items-center justify-center gap-2 w-full sm:w-auto"
                      >
                      <Trash2 className="h-4 w-4" />
                      <span>Supprimer la photo</span>
                    </button>
                    <button
                      onClick={handleUpdateCaption}
                      className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center gap-2 w-full sm:w-auto"
                    >
                      <Edit2 className="h-4 w-4" />
                      <span>Enregistrer les modifications</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modal de modération d'image */}
      {isModerationModalOpen && (
        <ModalPortal>
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay avec gestion du clic */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => {
                if (!isModerationLoading) {
                  setIsModerationModalOpen(false);
                  if (moderationPreviewUrl) {
                    URL.revokeObjectURL(moderationPreviewUrl);
                    setModerationPreviewUrl(null);
                  }
                }
              }}
              aria-label="Fermer la modale"
            />
            {/* Contenu de la modale */}
            <div
              className="relative bg-white rounded-2xl shadow-2xl w-[calc(100%-32px)] sm:w-full max-w-lg overflow-hidden flex flex-col"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-4 pt-5 flex justify-between items-center border-b">
                <h3 className="text-lg font-semibold">
                  {isModerationLoading
                    ? "Analyse de sécurité en cours"
                    : isImageRejected
                      ? "Image refusée"
                      : "Modération de l'image"}
                </h3>
                <button
                  onClick={() => {
                    // Toujours permettre l'annulation, même pendant le chargement
                    setIsModerationModalOpen(false);
                    if (moderationPreviewUrl) {
                      URL.revokeObjectURL(moderationPreviewUrl);
                      setModerationPreviewUrl(null);
                    }
                    setIsImageRejected(false);
                    setRejectionDescription(undefined);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-full"
                  aria-label="Fermer"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              {/* Contenu scrollable */}
              <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
                {isModerationLoading || (!isModerationLoading && !isImageRejected) ? (
                  <ImageModerationStatus
                    isLoading={isModerationLoading}
                    imageUrl={moderationPreviewUrl || undefined}
                    title={isModerationLoading ? "Analyse de sécurité en cours" : "Vérification de l'image"}
                    onCancel={() => {
                      // Toujours permettre l'annulation, même pendant le chargement
                      setIsModerationModalOpen(false);
                      if (moderationPreviewUrl) {
                        URL.revokeObjectURL(moderationPreviewUrl);
                        setModerationPreviewUrl(null);
                      }
                      setIsImageRejected(false);
                      setRejectionDescription(undefined);
                    }}
                  />
                ) : isImageRejected ? (
                  <div className="p-6">
                    {/* Afficher l'image refusée */}
                    {moderationPreviewUrl && (
                      <div className="mb-6 flex justify-center">
                        <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                          <img
                            src={moderationPreviewUrl}
                            alt="Image refusée"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-red-900/20"></div>
                        </div>
                      </div>
                    )}

                    {/* Message de rejet détaillé */}
                    <RejectedImageMessage
                      contentType="featured"
                      description={rejectionDescription || "Cette image ne respecte pas nos règles de modération."}
                      improvementSuggestions={rejectionImprovementSuggestions}
                      variant="detailed"
                    />

                    {/* Bouton pour réessayer */}
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => {
                          setIsModerationModalOpen(false);
                          if (moderationPreviewUrl) {
                            URL.revokeObjectURL(moderationPreviewUrl);
                            setModerationPreviewUrl(null);
                          }
                          setIsImageRejected(false);
                          setRejectionDescription(undefined);
                          setRejectionImprovementSuggestions(undefined);

                          // Ouvrir automatiquement le sélecteur de fichier
                          setTimeout(() => {
                            const inputs = document.querySelectorAll('input[type="file"]');
                            if (inputs.length > 0) {
                              (inputs[0] as HTMLInputElement).click();
                            }
                          }, 300);
                        }}
                        className="px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl"
                      >
                        Choisir une autre photo
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-center mt-6">
                    <p className="text-gray-600 text-sm">
                      Vérification automatique en cours...
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </ModalPortal>
      )}

      {/* Modal de confirmation de suppression */}
      {isDeleteModalOpen && selectedPhoto && (
        <ModalPortal containerId="delete-confirmation-modal">
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Confirmer la suppression</h3>
              <p className="text-gray-600">
                Êtes-vous sûr de vouloir supprimer cette photo ? Cette action est irréversible.
              </p>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setIsDeleteModalOpen(false);
                    setSelectedPhoto(null);
                  }}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  Annuler
                </button>
                <button
                  onClick={handleDeletePhoto}
                  className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
};

export default FeaturedPhotos;