import React from 'react';
import { AlertTriangle, Info } from 'lucide-react';
import { Tooltip } from '@mui/material';

interface RejectedImageMessageProps {
  contentType: string;
  description?: string;
  improvementSuggestions?: string;
  className?: string;
  showIcon?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

/**
 * Composant pour afficher un message professionnel lorsqu'une image est refusée par la modération
 */
const RejectedImageMessage: React.FC<RejectedImageMessageProps> = ({
  contentType,
  description,
  improvementSuggestions,
  className = '',
  showIcon = true,
  variant = 'default'
}) => {
  // Formater le type de contenu pour l'affichage
  const formatContentType = (type: string): string => {
    const typeMap: Record<string, string> = {
      'profile_picture': 'photo de profil',
      'banner_picture': 'bannière de profil',
      'avatar': 'photo de profil',
      'profile': 'photo de profil',
      'gallery': 'photo de galerie',
      'gallery_cover': 'couverture de galerie',
      'featured': 'photo mise en avant',
      'mission': 'photo de mission',
      'mission_assistant': 'photo de mission',
      'default': 'image'
    };

    return typeMap[type] || typeMap.default;
  };

  // Générer un message explicatif basé sur le type de contenu
  const getExplanation = (): string => {
    const formattedType = formatContentType(contentType);

    // Message de base
    let message = `Cette ${formattedType} ne respecte pas nos règles de modération.`;

    // Ajouter des détails spécifiques selon le type
    if (contentType === 'profile_picture' || contentType === 'banner_picture' || contentType === 'avatar' || contentType === 'profile') {
      message += ' Votre photo de profil doit représenter clairement votre visage ou votre logo professionnel.';
    } else if (contentType === 'gallery' || contentType === 'gallery_cover' || contentType === 'featured') {
      message += ' Les photos de galerie doivent représenter vos réalisations ou services de manière professionnelle.';
    } else if (contentType === 'mission' || contentType === 'mission_assistant') {
      message += ' Les photos de mission doivent illustrer clairement le besoin sans contenu inapproprié.';
    }

    return message;
  };

  // Générer un message de recommandation
  const getRecommendation = (): string => {
    return 'Veuillez choisir une autre image qui respecte nos conditions d\'utilisation.';
  };

  // Déterminer si une raison spécifique est fournie
  const hasSpecificReason = description &&
    !description.includes('Contenu inapproprié') &&
    !description.includes('Image à modérer');

  // Extraire la raison du rejet si elle existe
  const extractReason = (): string => {
    if (!description) return '';

    // Nettoyer la description
    let cleanDescription = description
      .replace(/Image refusée par la modération\s*:\s*/i, '')
      .replace(/Image à modérer pour \w+ d'un utilisateur/i, '')
      .replace(/proposant des services de .+ à .+\./i, '')
      .replace(/L'image contient /i, '');

    // Capitaliser la première lettre
    return cleanDescription.charAt(0).toUpperCase() + cleanDescription.toLowerCase().slice(1);
  };

  // Rendu compact (pour les notifications ou petits espaces)
  if (variant === 'compact') {
    return (
      <div className={`flex items-center text-red-600 ${className}`}>
        {showIcon && <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />}
        <span className="text-sm">Image non conforme aux règles de modération</span>
      </div>
    );
  }

  // Rendu détaillé (pour les modales ou pages dédiées)
  if (variant === 'detailed') {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start">
          {showIcon && <AlertTriangle className="h-6 w-6 text-red-500 mr-3 mt-0.5 flex-shrink-0" />}
          <div>
            <h3 className="text-lg font-medium text-red-800 mb-2">
              Image refusée par notre système de modération
            </h3>
            <p className="text-red-700 mb-3">{getExplanation()}</p>

            {hasSpecificReason && (
              <div className="bg-white bg-opacity-50 p-3 rounded-md mb-3 border border-red-100">
                <p className="text-red-800 font-medium mb-1">Analyse du motif de refus :</p>
                <p className="text-red-700">{extractReason()}</p>
              </div>
            )}

            <div className="bg-white bg-opacity-50 p-3 rounded-md border border-red-100">
              <p className="text-red-800 font-medium mb-1">Recommandations :</p>
              {improvementSuggestions ? (
                // Afficher les suggestions spécifiques de l'API si disponibles
                <p className="text-red-700">{improvementSuggestions}</p>
              ) : (
                // Afficher les recommandations génériques par défaut
                <ul className="list-disc list-inside text-red-700 space-y-1">
                  <li>Utilisez une image claire et professionnelle</li>
                  <li>Évitez tout contenu inapproprié ou offensant</li>
                  <li>Assurez-vous que l'image est en rapport avec votre service</li>
                  <li>Préférez des photos de bonne qualité</li>
                </ul>
              )}
            </div>

            <p className="text-red-700 mt-3">{getRecommendation()}</p>
          </div>
        </div>
      </div>
    );
  }

  // Rendu par défaut (pour la plupart des cas d'utilisation)
  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center">
        {showIcon && <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />}
        <div>
          <p className="text-red-700 font-medium">
            Image refusée par la modération
            {hasSpecificReason && (
              <Tooltip title={extractReason()} arrow placement="top">
                <span className="inline-flex items-center ml-1 cursor-help">
                  <Info className="h-4 w-4 text-red-400" />
                </span>
              </Tooltip>
            )}
          </p>
          <p className="text-red-600 text-sm mt-1">{getExplanation()}</p>
        </div>
      </div>
    </div>
  );
};

export default RejectedImageMessage;
