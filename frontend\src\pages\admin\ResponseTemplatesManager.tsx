import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert,
  Chip,
  Tooltip,
  Grid,
  TablePagination,
  Divider,
  Fade,
  Zoom,
  InputAdornment,
  Badge,
  Skeleton
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  RestartAlt as ResetIcon,
  LibraryBooks as TemplateIcon,
  FormatQuote as QuoteIcon,
  ClearAll as ClearAllIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import useResponseTemplates from '../../hooks/useTicketResponseTemplates';
import { CreateTemplateDto, ResponseTemplate } from '../../services/supportTicketService';
import { useAuth } from '../../contexts/AuthContext';
import { notify } from '../../components/Notification';
import { Outlet } from 'react-router-dom';
import { logger } from '@/utils/logger';
import supportTicketService from '../../services/supportTicketService';

const ResponseTemplatesManager: React.FC = () => {
  const { user } = useAuth();
  const { 
    templates, 
    loading, 
    error, 
    fetchTemplates, 
    createTemplate, 
    updateTemplate, 
    deleteTemplate,
    invalidateCache 
  } = useResponseTemplates();

  // États pour la pagination, le filtrage et la recherche
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  // État local de chargement pour débloquer le sablier si le hook reste bloqué
  const [localLoading, setLocalLoading] = useState(true);

  // États pour le dialogue de création/édition
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ResponseTemplate | null>(null);
  const [formData, setFormData] = useState<CreateTemplateDto>({
    title: '',
    content: '',
    category: 'technique'
  });

  // État pour le dialogue de confirmation de suppression
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<ResponseTemplate | null>(null);

  // Référence pour suivre si le chargement initial a été effectué
  const initialLoadPerformedRef = React.useRef(false);
  
  // Vérification des droits d'accès
  const isAdmin = user?.role === 'jobpadm';
  const isModerator = user?.role === 'jobmodo';
  const hasAccess = isAdmin || isModerator;

  // Effet pour le chargement initial - exécuté une seule fois
  useEffect(() => {
    // Ne pas recharger si nous avons déjà des templates ou si le chargement a été effectué
    if (templates.length > 0 || initialLoadPerformedRef.current) {
      setLocalLoading(false);
      return;
    }
    
    // Premier chargement immédiat
    const loadNow = async () => {
      try {
        setLocalLoading(true);
        await fetchTemplates({});
        initialLoadPerformedRef.current = true;
      } catch (err) {
        // Erreur gérée silencieusement
        logger.error('Erreur lors du chargement initial des templates:', err);
      } finally {
        // Toujours désactiver le chargement après tentative
        setLocalLoading(false);
      }
    };
    
    // Exécuter immédiatement le chargement
    loadNow();
    
    // On définit un timer de secours pour désactiver le chargement 
    // après 1 seconde même si le hook reste bloqué
    const timer = setTimeout(() => {
      setLocalLoading(false);
      initialLoadPerformedRef.current = true;
    }, 1000);
    
    return () => {
      clearTimeout(timer);
    };
  }, []); // Ne dépend de rien pour éviter les boucles

  // Log pour déboguer les templates récupérés
  useEffect(() => {
    // Désactiver le chargement dès que nous avons des templates
    if (templates.length > 0 && localLoading) {
      setLocalLoading(false);
      initialLoadPerformedRef.current = true;
    }
  }, [templates, localLoading]);

  // Effet pour mettre à jour le chargement local quand le loading du hook change
  useEffect(() => {
    if (!loading && localLoading) {
      setLocalLoading(false);
    }
  }, [loading]);

  // Filtrer localement les modèles plutôt que de faire des appels API répétés
  const filteredTemplates = templates.filter(template => {
    if (!template || !template.title) {
      logger.warn("Template invalide ignoré:", template);
      return false;
    }
    
    const matchesSearch = 
      searchQuery === '' || 
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = 
      categoryFilter === '' || 
      template.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  // Fonctions de pagination
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Fonctions pour le dialogue de création/édition
  const handleOpenCreateDialog = () => {
    setIsEditing(false);
    setSelectedTemplate(null);
    setFormData({
      title: '',
      content: '',
      category: 'technique'
    });
    setOpenDialog(true);
  };

  const handleOpenEditDialog = (template: ResponseTemplate) => {
    setIsEditing(true);
    setSelectedTemplate(template);
    setFormData({
      title: template.title,
      content: template.content,
      category: template.category
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (e: any) => {
    setFormData(prev => ({ ...prev, category: e.target.value }));
  };

  const handleSubmitTemplate = async () => {
    try {
      setLocalLoading(true);
      
      // Invalider le cache avant l'opération
      invalidateCache();
      
      if (isEditing && selectedTemplate) {
        await updateTemplate(selectedTemplate.id, formData);
        notify('Modèle mis à jour avec succès', 'success');
      } else {
        await createTemplate(formData);
        notify('Modèle créé avec succès', 'success');
      }
      handleCloseDialog();
      
      // Recharger les templates pour s'assurer d'avoir les données à jour
      await fetchTemplates({});
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde du modèle:', error);
      notify('Erreur lors de la sauvegarde du modèle', 'error');
      setLocalLoading(false);
    }
  };

  // Fonctions pour le dialogue de suppression
  const handleOpenDeleteDialog = (template: ResponseTemplate) => {
    setTemplateToDelete(template);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setTemplateToDelete(null);
  };

  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return;
    
    try {
      setLocalLoading(true);
      
      // Invalider le cache avant la suppression
      invalidateCache();
      
      await deleteTemplate(templateToDelete.id);
      notify('Modèle supprimé avec succès', 'success');
      handleCloseDeleteDialog();
      
      // Recharger les templates pour s'assurer d'avoir les données à jour
      await fetchTemplates({});
    } catch (error) {
      logger.error('Erreur lors de la suppression du modèle:', error);
      notify('Erreur lors de la suppression du modèle', 'error');
      setLocalLoading(false);
    }
  };

  // Fonction pour copier un template (duplication)
  const handleCopyTemplate = (template: ResponseTemplate) => {
    setIsEditing(false);
    setSelectedTemplate(null);
    setFormData({
      title: `Copie de ${template.title}`,
      content: template.content,
      category: template.category
    });
    setOpenDialog(true);
  };

  // Fonctions pour appliquer les filtres localement, sans déclencher d'appels API
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setPage(0); // Réinitialiser la pagination
  };

  const handleCategoryFilterChange = (e: any) => {
    setCategoryFilter(e.target.value);
    setPage(0); // Réinitialiser la pagination
  };

  const handleResetFilters = () => {
    setSearchQuery('');
    setCategoryFilter('');
    setPage(0); // Réinitialiser la pagination
  };

  // Obtenir le libellé de la catégorie
  const getCategoryLabel = (category: string): string => {
    const categories: Record<string, string> = {
      'technique': 'Technique',
      'facturation': 'Facturation',
      'compte': 'Compte',
      'mission': 'Mission',
      'autre': 'Autre'
    };
    return categories[category] || category;
  };

  // Obtenir la couleur de la catégorie
  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      'technique': 'primary',
      'facturation': 'secondary',
      'compte': 'info',
      'mission': 'success',
      'autre': 'default'
    };
    return colors[category] || 'default';
  };

  // Fonction pour vider le cache des templates
  const handleClearCache = async () => {
    try {
      setLocalLoading(true);
      
      // Appeler l'API pour vider le cache Redis
      const success = await supportTicketService.clearTemplatesCache();
      
      if (success) {
        // Vider aussi le cache local via le hook
        invalidateCache();
        
        // Recharger les templates
        await fetchTemplates({});
        
        notify('Cache des templates vidé avec succès', 'success');
      } else {
        notify('Erreur lors du vidage du cache', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors du vidage du cache:', error);
      notify('Erreur lors du vidage du cache', 'error');
    } finally {
      setLocalLoading(false);
    }
  };

  // Utiliser le loading local au lieu du loading du hook
  const isPageLoading = loading || localLoading;

  // Rendu de la page
  return (
    <Box sx={{ p: { xs: 2, md: 3 }, bgcolor: '#FFF8F3', minHeight: '100vh' }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 4,
            gap: 2,
            position: 'relative',
            pl: 1
          }}
        >
          <TemplateIcon
            sx={{
              fontSize: 36,
              color: '#FF6B2C',
              mr: 1
            }}
          />
          <Typography 
            variant="h4" 
            component="h1" 
            sx={{ 
              color: '#FF6B2C',
              fontWeight: 700,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -8,
                left: 0,
                width: '40px',
                height: '3px',
                backgroundColor: '#FF965E',
                borderRadius: '8px'
              }
            }}
          >
            Gestion des Modèles de Réponse
          </Typography>
          {isPageLoading && (
            <Fade in={isPageLoading}>
              <CircularProgress 
                size={24} 
                sx={{ ml: 2, color: '#FF6B2C' }} 
              />
            </Fade>
          )}
          <Tooltip title="Vider le cache" arrow>
            <IconButton
              onClick={handleClearCache}
              sx={{
                color: '#FF6B2C',
                ml: 'auto',
                '&:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.08)',
                },
              }}
            >
              <ClearAllIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {!hasAccess && (
          <Alert 
            severity="error" 
            sx={{ 
              mb: 3, 
              border: '1px solid #f44336',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
            }}
          >
            Vous n'avez pas les droits nécessaires pour accéder à cette page.
          </Alert>
        )}

        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mb: 3, 
              border: '1px solid #f44336',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
            }}
          >
            {error.message || 'Une erreur est survenue lors du chargement des modèles de réponse.'}
          </Alert>
        )}

        {hasAccess && (
          <>
            {/* Barre d'outils */}
            <Paper 
              elevation={2} 
              sx={{ 
                p: 3, 
                mb: 4, 
                borderRadius: '12px',
                background: 'linear-gradient(135deg, #ffffff 0%, #fff8f3 100%)',
                boxShadow: '0 4px 20px rgba(255, 107, 44, 0.05)'
              }}
            >
              <Grid container spacing={2} alignItems="center">
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <TextField
                    fullWidth
                    label="Rechercher"
                    name="search"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon sx={{ color: '#FF6B2C' }} />
                        </InputAdornment>
                      ),
                    }}
                    variant="outlined"
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#FF965E',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF6B2C',
                        },
                      },
                      '& label.Mui-focused': {
                        color: '#FF6B2C',
                      },
                      backgroundColor: '#ffffff',
                      borderRadius: '8px',
                    }}
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <FormControl fullWidth size="small" sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#FF965E',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& label.Mui-focused': {
                      color: '#FF6B2C',
                    },
                    backgroundColor: '#ffffff',
                    borderRadius: '8px',
                  }}>
                    <InputLabel id="category-filter-label">Catégorie</InputLabel>
                    <Select
                      labelId="category-filter-label"
                      id="category-filter"
                      value={categoryFilter}
                      label="Catégorie"
                      onChange={handleCategoryFilterChange}
                      startAdornment={
                        <InputAdornment position="start">
                          <FilterIcon sx={{ color: '#FF6B2C', mr: 1 }} />
                        </InputAdornment>
                      }
                    >
                      <MenuItem value="">Toutes</MenuItem>
                      <MenuItem value="technique">Technique</MenuItem>
                      <MenuItem value="facturation">Facturation</MenuItem>
                      <MenuItem value="compte">Compte</MenuItem>
                      <MenuItem value="mission">Mission</MenuItem>
                      <MenuItem value="autre">Autre</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    variant="outlined"
                    startIcon={<ResetIcon />}
                    onClick={handleResetFilters}
                    fullWidth
                    sx={{
                      borderColor: '#FF6B2C',
                      color: '#FF6B2C',
                      '&:hover': {
                        borderColor: '#FF965E',
                        backgroundColor: 'rgba(255, 107, 44, 0.05)',
                      },
                      borderRadius: '8px',
                      py: 1,
                    }}
                  >
                    Réinitialiser
                  </Button>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleOpenCreateDialog}
                    fullWidth
                    sx={{
                      backgroundColor: '#FF6B2C',
                      '&:hover': {
                        backgroundColor: '#FF7A35',
                      },
                      borderRadius: '8px',
                      color: 'white',
                      fontWeight: 600,
                      py: 1,
                      boxShadow: '0 4px 8px rgba(255, 107, 44, 0.25)',
                    }}
                  >
                    Nouveau modèle
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {/* Badge avec le nombre de modèles */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Badge 
                badgeContent={filteredTemplates.length} 
                color="primary"
                sx={{ 
                  '& .MuiBadge-badge': { 
                    backgroundColor: '#FF7A35',
                    color: 'white',
                    fontWeight: 'bold',
                  } 
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#1f1b18', display: 'flex', alignItems: 'center' }}>
                  <QuoteIcon sx={{ mr: 1, color: '#FF965E' }} /> Modèles de réponse
                </Typography>
              </Badge>
            </Box>

            {/* Liste des modèles */}
            <Paper 
              elevation={3} 
              sx={{ 
                width: '100%', 
                overflow: 'hidden',
                borderRadius: '12px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
              }}
            >
              <TableContainer sx={{ maxHeight: '70vh' }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell 
                        width="30%" 
                        sx={{ 
                          backgroundColor: '#FFE4BA', 
                          fontWeight: 'bold',
                          color: '#1f1b18' 
                        }}
                      >
                        Titre
                      </TableCell>
                      <TableCell 
                        width="40%" 
                        sx={{ 
                          backgroundColor: '#FFE4BA', 
                          fontWeight: 'bold',
                          color: '#1f1b18' 
                        }}
                      >
                        Contenu
                      </TableCell>
                      <TableCell 
                        width="15%" 
                        sx={{ 
                          backgroundColor: '#FFE4BA', 
                          fontWeight: 'bold',
                          color: '#1f1b18' 
                        }}
                      >
                        Catégorie
                      </TableCell>
                      <TableCell 
                        width="15%" 
                        align="right" 
                        sx={{ 
                          backgroundColor: '#FFE4BA', 
                          fontWeight: 'bold',
                          color: '#1f1b18' 
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {isPageLoading && (
                      // Skeletons de chargement
                      Array.from(new Array(5)).map((_, index) => (
                        <TableRow key={`skeleton-${index}`}>
                          <TableCell>
                            <Skeleton animation="wave" height={40} />
                          </TableCell>
                          <TableCell>
                            <Skeleton animation="wave" height={40} />
                          </TableCell>
                          <TableCell>
                            <Skeleton animation="wave" height={40} width={80} />
                          </TableCell>
                          <TableCell align="right">
                            <Skeleton animation="wave" height={40} width={120} />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                    
                    {!isPageLoading && filteredTemplates.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          <Box sx={{ py: 6, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                            <motion.div
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ duration: 0.3 }}
                            >
                              <TemplateIcon sx={{ fontSize: 60, color: '#FF965E', opacity: 0.7 }} />
                            </motion.div>
                            <Typography variant="h6" sx={{ color: '#666', fontWeight: 500 }}>
                              Aucun modèle de réponse trouvé.
                            </Typography>
                            <Button 
                              variant="outlined" 
                              startIcon={<AddIcon />} 
                              onClick={handleOpenCreateDialog}
                              sx={{
                                mt: 2,
                                borderColor: '#FF6B2C',
                                color: '#FF6B2C',
                                '&:hover': {
                                  borderColor: '#FF965E',
                                  backgroundColor: 'rgba(255, 107, 44, 0.05)',
                                },
                              }}
                            >
                              Créer un modèle
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    )}
                    
                    {/* Affichage des templates filtrés */}
                    {!isPageLoading && filteredTemplates
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((template, index) => (
                        <motion.tr
                          key={template.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.2, delay: index * 0.03 }}
                          style={{ display: 'table-row' }}
                        >
                          <TableCell sx={{ 
                            borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                            backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa' 
                          }}>
                            <Typography 
                              variant="subtitle1" 
                              sx={{ 
                                fontWeight: 500,
                                color: '#1f1b18'
                              }}
                            >
                              {template.title}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ 
                            borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                            backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                          }}>
                            <Typography
                              variant="body2"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                color: '#555'
                              }}
                            >
                              {template.content.replace(/<[^>]*>?/gm, '')}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ 
                            borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                            backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                          }}>
                            <Chip
                              label={getCategoryLabel(template.category)}
                              size="small"
                              color={getCategoryColor(template.category) as any}
                              variant="filled"
                              sx={{ 
                                fontWeight: '500',
                                '&.MuiChip-filledPrimary': {
                                  backgroundColor: '#FF6B2C',
                                },
                                '&.MuiChip-filledSecondary': {
                                  backgroundColor: '#FF7A35',
                                },
                                '&.MuiChip-filledInfo': {
                                  backgroundColor: '#FF965E',
                                },
                                '&.MuiChip-filledSuccess': {
                                  backgroundColor: '#FCD782',
                                  color: '#1f1b18',
                                },
                              }}
                            />
                          </TableCell>
                          <TableCell align="right" sx={{ 
                            borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                            backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                          }}>
                            <Tooltip title="Modifier" arrow>
                              <IconButton
                                onClick={() => handleOpenEditDialog(template)}
                                size="small"
                                sx={{ 
                                  color: '#FF6B2C',
                                  '&:hover': {
                                    backgroundColor: 'rgba(255, 107, 44, 0.05)',
                                  },
                                  mr: 0.5
                                }}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Dupliquer" arrow>
                              <IconButton
                                onClick={() => handleCopyTemplate(template)}
                                size="small"
                                sx={{ 
                                  color: '#FF965E',
                                  '&:hover': {
                                    backgroundColor: 'rgba(255, 150, 94, 0.05)',
                                  },
                                  mr: 0.5
                                }}
                              >
                                <CopyIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Supprimer" arrow>
                              <IconButton
                                onClick={() => handleOpenDeleteDialog(template)}
                                size="small"
                                sx={{ 
                                  color: '#f44336',
                                  '&:hover': {
                                    backgroundColor: 'rgba(244, 67, 54, 0.05)',
                                  }
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </motion.tr>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Divider />
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={filteredTemplates.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Lignes par page:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count}`}
                sx={{
                  '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                    color: '#666',
                  },
                  '.MuiTablePagination-select': {
                    color: '#1f1b18',
                  },
                  '.MuiTablePagination-actions button': {
                    color: '#FF6B2C',
                  }
                }}
              />
            </Paper>
          </>
        )}
      </motion.div>

      {/* Dialogue de création/édition */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog} 
        maxWidth="md" 
        fullWidth
        TransitionComponent={Zoom}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: '#FF6B2C', 
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          px: 3,
          py: 2
        }}>
          <TemplateIcon sx={{ mr: 1 }} />
          {isEditing ? 'Modifier le modèle de réponse' : 'Créer un nouveau modèle de réponse'}
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ mt: 1 }}>
            <TextField
              autoFocus
              margin="dense"
              id="title"
              name="title"
              label="Titre"
              type="text"
              fullWidth
              variant="outlined"
              value={formData.title}
              onChange={handleFormChange}
              sx={{ 
                mb: 3,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: '#FF965E',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& label.Mui-focused': {
                  color: '#FF6B2C',
                },
              }}
            />
            <FormControl fullWidth margin="dense" sx={{ 
              mb: 3,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: '#FF965E',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#FF6B2C',
                },
              },
              '& label.Mui-focused': {
                color: '#FF6B2C',
              },
            }}>
              <InputLabel id="category-label">Catégorie</InputLabel>
              <Select
                labelId="category-label"
                id="category"
                name="category"
                value={formData.category}
                label="Catégorie"
                onChange={handleCategoryChange}
              >
                <MenuItem value="technique">Technique</MenuItem>
                <MenuItem value="facturation">Facturation</MenuItem>
                <MenuItem value="compte">Compte</MenuItem>
                <MenuItem value="mission">Mission</MenuItem>
                <MenuItem value="autre">Autre</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="dense"
              id="content"
              name="content"
              label="Contenu"
              multiline
              rows={8}
              fullWidth
              variant="outlined"
              value={formData.content}
              onChange={handleFormChange}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: '#FF965E',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& label.Mui-focused': {
                  color: '#FF6B2C',
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: '#f9f9f9' }}>
          <Button 
            onClick={handleCloseDialog} 
            sx={{ 
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              borderRadius: '8px',
              fontWeight: 500,
            }}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleSubmitTemplate} 
            sx={{ 
              backgroundColor: '#FF6B2C',
              color: 'white',
              '&:hover': {
                backgroundColor: '#FF7A35',
              },
              borderRadius: '8px',
              px: 3,
              fontWeight: 600,
              boxShadow: '0 4px 8px rgba(255, 107, 44, 0.25)',
            }}
          >
            {isEditing ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation de suppression */}
      <Dialog 
        open={openDeleteDialog} 
        onClose={handleCloseDeleteDialog}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          color: '#f44336',
          borderBottom: '1px solid #f0f0f0',
          px: 3,
          py: 2
        }}>
          Confirmer la suppression
        </DialogTitle>
        <DialogContent sx={{ p: 3, pt: 3 }}>
          <Typography variant="body1">
            Êtes-vous sûr de vouloir supprimer le modèle <strong>"{templateToDelete?.title}"</strong> ?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Cette action est irréversible et supprimera définitivement ce modèle de réponse.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #f0f0f0' }}>
          <Button 
            onClick={handleCloseDeleteDialog} 
            sx={{ 
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              borderRadius: '8px',
              fontWeight: 500,
            }}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleDeleteTemplate} 
            sx={{ 
              backgroundColor: '#f44336',
              color: 'white',
              '&:hover': {
                backgroundColor: '#d32f2f',
              },
              borderRadius: '8px',
              px: 3,
              fontWeight: 600,
            }}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      <Outlet />
    </Box>
  );
};

export default ResponseTemplatesManager; 