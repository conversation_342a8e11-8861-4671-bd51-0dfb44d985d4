import { Router, Response } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import { asyncHandler } from '../utils/inputValidation';
import { uploadTemporaryImageForModeration } from '../services/storage';
import logger from '../utils/logger';
import { UploadedFile } from 'express-fileupload';
import { rateLimit } from 'express-rate-limit';
import { validateFileUpload, FileRequest } from '../middleware/fileValidation';

const router = Router();

// Rate limiter pour les uploads temporaires
const tempUploadLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes d\'upload. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Route pour uploader temporairement une image pour la modération
 * Cette route permet d'uploader une image temporairement, puis d'obtenir son URL publique
 * pour l'envoyer à l'API de modération d'image
 */
router.post('/temp-upload',
  authMiddleware.authenticateToken,
  tempUploadLimiter,
  validateFileUpload, // Utiliser le middleware de validation de fichier
  asyncHandler(async (req: FileRequest, res: Response) => {
    try {
      // Vérifier que l'utilisateur est authentifié
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non authentifié'
        });
      }

      // Vérifier qu'un fichier a été envoyé
      if (!req.files || Object.keys(req.files).length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Aucun fichier n\'a été envoyé'
        });
      }

      // Récupérer le fichier (le middleware validateFileUpload a déjà vérifié le type et la taille)
      const file = req.files.file as UploadedFile;

      // Lire le fichier temporaire comme le font les autres contrôleurs
      const fileBuffer = await new Promise<Buffer>((resolve, reject) => {
        require('fs').readFile(file.tempFilePath, (err: any, data: Buffer) => {
          if (err) {
            logger.error('Erreur lors de la lecture du fichier temporaire', { error: err, userId });
            reject(err);
          } else {
            resolve(data);
          }
        });
      });

      // Uploader l'image temporairement
      const { publicUrl, filePath } = await uploadTemporaryImageForModeration(
        userId,
        fileBuffer,
        file.mimetype
      );

      // Retourner l'URL publique et le chemin du fichier
      return res.status(200).json({
        success: true,
        publicUrl,
        filePath,
        message: 'Image uploadée temporairement avec succès'
      });
    } catch (error: any) {
      logger.error('Erreur lors de l\'upload temporaire de l\'image', {
        error: error.message || String(error),
        userId: req.user?.userId
      });

      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'upload temporaire de l\'image'
      });
    }
  })
);

export default router;
