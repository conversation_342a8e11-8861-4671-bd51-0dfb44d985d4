import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';

const CACHE_PREFIX = 'ticket_tag:';
const CACHE_TTL = 3600; // 1 heure

export const createTag = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId || req.user?.id;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId || !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    const { name, color, description } = req.body;

    if (!name || !color) {
      res.status(400).json({ error: 'Nom et couleur requis' });
      return;
    }

    // Vérifier si le tag existe déjà
    const { data: existingTag } = await supabase
      .from('support_ticket_tags')
      .select('*')
      .ilike('name', name)
      .single();

    if (existingTag) {
      res.status(400).json({ error: 'Un tag avec ce nom existe déjà' });
      return;
    }

    const { data: tag, error } = await supabase
      .from('support_ticket_tags')
      .insert({
        name,
        color,
        description
      })
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la création du tag:', error);
      res.status(500).json({ error: 'Erreur lors de la création du tag' });
      return;
    }

    // Invalider le cache des tags
    await redis.del(`${CACHE_PREFIX}list`);

    res.status(201).json(tag);
    return;
  } catch (error) {
    logger.error('Erreur lors de la création du tag:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const updateTag = async (req: Request, res: Response) => {
  try {
    const { tagId } = req.params;
    const userId = req.user?.userId || req.user?.id;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId || !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    const { name, color, description } = req.body;

    // Vérifier l'existence du tag
    const { data: existingTag } = await supabase
      .from('support_ticket_tags')
      .select('*')
      .eq('id', tagId)
      .single();

    if (!existingTag) {
      res.status(404).json({ error: 'Tag non trouvé' });
      return;
    }

    // Vérifier si le nouveau nom n'est pas déjà utilisé
    if (name && name !== existingTag.name) {
      const { data: duplicateTag } = await supabase
        .from('support_ticket_tags')
        .select('*')
        .ilike('name', name)
        .neq('id', tagId)
        .single();

      if (duplicateTag) {
        res.status(400).json({ error: 'Un tag avec ce nom existe déjà' });
        return;
      }
    }

    const updates: any = {};
    if (name) updates.name = name;
    if (color) updates.color = color;
    if (description !== undefined) updates.description = description;
    updates.updated_at = new Date().toISOString();

    const { data: tag, error } = await supabase
      .from('support_ticket_tags')
      .update(updates)
      .eq('id', tagId)
      .select()
      .single();

    if (error) {
      logger.error('Erreur lors de la mise à jour du tag:', error);
      res.status(500).json({ error: 'Erreur lors de la mise à jour du tag' });
      return;
    }

    // Invalider les caches
    await Promise.all([
      redis.del(`${CACHE_PREFIX}${tagId}`),
      redis.del(`${CACHE_PREFIX}list`)
    ]);

    res.json(tag);
    return;
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du tag:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const deleteTag = async (req: Request, res: Response) => {
  try {
    const { tagId } = req.params;
    const userId = req.user?.userId || req.user?.id;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId || !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Supprimer d'abord les relations
    await supabase
      .from('support_ticket_tag_relations')
      .delete()
      .eq('tag_id', tagId);

    // Supprimer le tag
    const { error } = await supabase
      .from('support_ticket_tags')
      .delete()
      .eq('id', tagId);

    if (error) {
      logger.error('Erreur lors de la suppression du tag:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du tag' });
      return;
    }

    // Invalider les caches
    await Promise.all([
      redis.del(`${CACHE_PREFIX}${tagId}`),
      redis.del(`${CACHE_PREFIX}list`)
    ]);

    res.json({ message: 'Tag supprimé avec succès' });
    return;
  } catch (error) {
    logger.error('Erreur lors de la suppression du tag:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const getTags = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId || req.user?.id;

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier le cache
    const cachedTags = await redis.get(`${CACHE_PREFIX}list`);
    if (cachedTags) {
      res.json(JSON.parse(cachedTags));
      return;
    }

    const { data: tags, error } = await supabase
      .from('support_ticket_tags')
      .select('*')
      .order('name');

    if (error) {
      logger.error('Erreur lors de la récupération des tags:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des tags' });
      return;
    }

    // Mettre en cache
    await redis.setex(
      `${CACHE_PREFIX}list`,
      CACHE_TTL,
      JSON.stringify(tags)
    );

    res.json(tags);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des tags:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const addTagToTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId, tagId } = req.params;
    const userId = req.user?.userId || req.user?.id;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId || !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Vérifier si la relation existe déjà
    const { data: existingRelation } = await supabase
      .from('support_ticket_tag_relations')
      .select('*')
      .eq('ticket_id', ticketId)
      .eq('tag_id', tagId)
      .single();

    if (existingRelation) {
      res.status(400).json({ error: 'Ce tag est déjà associé au ticket' });
      return;
    }

    const { error } = await supabase
      .from('support_ticket_tag_relations')
      .insert({
        ticket_id: ticketId,
        tag_id: tagId
      });

    if (error) {
      logger.error('Erreur lors de l\'ajout du tag au ticket:', error);
      res.status(500).json({ error: 'Erreur lors de l\'ajout du tag au ticket' });
      return;
    }

    // Invalider le cache du ticket
    await redis.del(`ticket:${ticketId}`);

    res.json({ message: 'Tag ajouté au ticket avec succès' });
    return;
  } catch (error) {
    logger.error('Erreur lors de l\'ajout du tag au ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const removeTagFromTicket = async (req: Request, res: Response) => {
  try {
    const { ticketId, tagId } = req.params;
    const userId = req.user?.userId || req.user?.id;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId || !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    const { error } = await supabase
      .from('support_ticket_tag_relations')
      .delete()
      .eq('ticket_id', ticketId)
      .eq('tag_id', tagId);

    if (error) {
      logger.error('Erreur lors de la suppression du tag du ticket:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du tag du ticket' });
      return;
    }

    // Invalider le cache du ticket
    await redis.del(`ticket:${ticketId}`);

    res.json({ message: 'Tag retiré du ticket avec succès' });
    return;
  } catch (error) {
    logger.error('Erreur lors de la suppression du tag du ticket:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
}; 