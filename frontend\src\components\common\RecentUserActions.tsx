import React from 'react';
import { Box, Typography, Paper, Skeleton, Avatar, Tooltip } from '@mui/material';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import DOMPurify from 'dompurify';
import { PublicUserAction } from '../../hooks/usePublicUserActions';
import { StyledChip, EmptyStateBox } from '../common/HistoryTable';
// Import des icônes Lucide
import {
  Calendar,
  ArrowUpCircle,
  ArrowDownCircle,
  ClipboardList,
  History,
  Coins,
  Star,
  Heart,
  Briefcase,
  MessageCircle,
  UserPlus,
  Image,
  BadgeCheck,
  CheckCircle
} from 'lucide-react';

interface RecentUserActionsProps {
  actions: PublicUserAction[];
  loading: boolean;
  error: string | null;
  className?: string;
  variant?: 'default' | 'content-only';
  title?: string;
  maxHeight?: number;
}

const RecentUserActions: React.FC<RecentUserActionsProps> = ({
  actions,
  loading,
  error,
  className,
  variant = 'default',
  title = 'Activités récentes',
  maxHeight = 250
}) => {

  // Types d'actions à exclure de l'affichage
  const excludedActionTypes = [
    'admin_view_user_history',
    // Ajoutez d'autres types d'actions à exclure ici si nécessaire
  ];

  // Fonctions utilitaires
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy à HH:mm', { locale: fr });
  };

  const formatActionType = (actionType: string): string => {
    const formattingMap: { [key: string]: string } = {
      'transfert_jobi': 'Transaction Jobi',
      'reception_jobi': 'Réception de Jobi',
      'mise_a_jour_profil': 'Mise à jour du profil',
      'admin_view_user_history': 'Affichage de l\'historique de l\'utilisateur',
      'creation_mission': 'Création d\'une mission',
      'mission_create': 'Création d\'une mission',
      'mise_a_jour_mission': 'Mise à jour d\'une mission',
      'annulation_mission': 'Annulation d\'une mission',
      'completion_mission': 'Finalisation d\'une mission',
      'acceptation_mission': 'Acceptation d\'une mission',
      'refus_mission': 'Refus d\'une mission',
      'ajout_service': 'Ajout d\'un service',
      'mise_a_jour_service': 'Mise à jour d\'un service',
      'suppression_service': 'Suppression d\'un service',
      'two_factor_enabled': 'Activation de l\'authentification à deux facteurs',
      'two_factor_disabled': 'Désactivation de l\'authentification à deux facteurs',
      'two_factor_verified': 'Vérification de l\'authentification à deux facteurs',
      'creation_galerie': 'Création d\'une galerie',
      'mise_a_jour_galerie': 'Mise à jour d\'une galerie',
      'suppression_galerie': 'Suppression d\'une galerie',
      'ajout_photo_galerie': 'Téléchargement d\'une photo',
      'suppression_photo_galerie': 'Suppression d\'une photo',
      'gallery_activate': 'Activation d\'une galerie',
      'gallery_deactivate': 'Désactivation d\'une galerie',
      'evaluation_mission': 'Création d\'un avis',
      'envoi_message': 'Envoi d\'un message',
      'lecture_notification': 'Notification lue',
      'mise_a_jour_photo_profil': 'Mise à jour de photo de profil',
      'review_update': 'Mise à jour d\'un avis',
      'review_delete': 'Suppression d\'un avis',
      'review_response_update': 'Mise à jour d\'une réponse à un avis',
      'review_response_delete': 'Suppression d\'une réponse à un avis',
      'review_response_create': 'Création d\'une réponse à un avis',
      'review_response_read': 'Lecture d\'une réponse à un avis',
      'favorite_add': 'Ajout d\'un favori',
      'favorite_delete': 'Suppression d\'un favori',
      'favorite_read': 'Lecture d\'un favori',
      'favorite_update': 'Mise à jour d\'un favori',
      'favorite_create': 'Création d\'un favori',
      'favorite_remove': 'Suppression d\'un favori',
      'jobi_transfer': 'Transfert de Jobi',
      'jobi_receive': 'Réception de Jobi',
      'reactivation_renouvellement_auto': 'Réactivation du renouvellement automatique',
      'desactivation_renouvellement_auto': 'Désactivation du renouvellement automatique',
      'renouvellement_abonnement': 'Renouvellement de l\'abonnement',
      'passage_premium': 'Passage à Premium',
      'passage_gratuit': 'Retour à l\'offre gratuite',
      'downgrade_gratuit': 'Retour à l\'offre gratuite',
      'upgrade_premium': 'Passage à Premium',
      'cancel_auto_renew': 'Désactivation du renouvellement automatique',
      'reactivate_auto_renew': 'Réactivation du renouvellement automatique',
      'proposal_accept': 'Proposition acceptée',
      'proposal_reject': 'Proposition refusée',
      'proposal_create': 'Nouvelle proposition envoyée',
      'proposal_update': 'Modification d\'une proposition',
      'proposal_delete': 'Suppression d\'une proposition',
      'creation_conversation': 'Nouvelle conversation créée',
      'mission_proposal_create': 'Nouvelle proposition de mission',
      'review_response': 'Réponse à un avis',
      'gallery_photo_add': 'Photo ajoutée à la galerie',
      'gallery_photo_delete': 'Photo supprimée de la galerie',
      'gallery_photo_update': 'Photo de galerie modifiée',
      'profile_photo_update': 'Photo de profil mise à jour',
      'gallery_create': 'Création d\'une galerie',
      'review_create': 'Nouvelle évaluation',
      'mission_comment': 'Commentaire sur la mission',
      'support_ticket_create': 'Création d\'un ticket support',
      'support_ticket_update': 'Mise à jour d\'un ticket support',
      'support_ticket_delete': 'Suppression d\'un ticket support',
      'creation_rapport_bug': 'Création d\'un rapport de bug',
      'ban_user': 'Compte suspendu',
      'unban_user': 'Compte réactivé',
      'reactivation_after_unban': 'Réactivation d\'abonnement après débannissement',
      'email_verifie': 'Email vérifié',
      'achat_credits_ia_jobi': 'Achat de crédits IA (Jobi)',
      'achat_credits_ia_stripe': 'Achat de crédits IA (Stripe)',
      'utilisation_credit_ia': 'Utilisation d\'un crédit IA',
      'credits_ia_offerts_abonnement': 'Crédits IA offerts (Abonnement)',
      'credits_ia_offerts_admin': 'Crédits IA offerts (Admin)',
      'operation_credits_ia': 'Opération sur les crédits IA',
      'ai_credits_purchase_jobi': 'Achat de crédits IA (Jobi)',
      'ai_credits_purchase_stripe': 'Achat de crédits IA (Stripe)',
      'ai_credits_used': 'Utilisation d\'un crédit IA',
      'ai_credits_subscription_gift': 'Crédits IA offerts (Abonnement)',
      'ai_credits_admin_gift': 'Crédits IA offerts (Admin)',
      'ai_credits_other': 'Opération sur les crédits IA',
      'ai_credit_used': 'Utilisation d\'un crédit IA',
      'ai_credits_purchased': 'Achat de crédits IA (Jobi)',
      'ai_credits_checkout_created': 'Création d\'un paiement pour crédits IA',
      'ai_credits_purchased_stripe': 'Achat de crédits IA (Stripe)',
      'gallery_update': 'Mise à jour d\'une galerie',
      'gallery_delete': 'Suppression d\'une galerie',
      'ai_consent': 'Consentement IA',
      'ai_prompt_saved': 'Prompt IA sauvegardé',
      'ai_prompt_deleted': 'Prompt IA supprimé',
      'image_confirmed': 'Image IA validée',
      'inscription_utilisateur': 'Inscription utilisateur',
      'generate_random_card_template': 'Génération d\'un modèle de carte aléatoire',
      'auto_deactivate_card_template': 'Désactivation automatique du modèle de carte',
      'update_card_template': 'Mise à jour du modèle de carte',
      'demande_suppression_compte': 'Demande de suppression de compte',
      'profile_banner_update': 'Mise à jour de la bannière de profil',  
      'admin_photo_moderation': 'Modération de la photo de profil',
      'password_change': 'Changement de mot de passe',
      'email_verification': 'Vérification email',
      'subscription_change': 'Changement d\'abonnement',
    };

    return formattingMap[actionType] || actionType;
  };

  // Fonction pour obtenir la couleur de l'avatar en fonction du type d'action
  const getAvatarStyle = (type: string) => {
    // Utiliser les couleurs standard MUI correspondant aux chips
    const chipColor = getChipColor(type);

    // Map des couleurs MUI vers des valeurs hexadécimales et gradients
    const colorMap = {
      'primary': {
        color: '#FF6B2C', // Couleur primaire (orange)
        bgcolor: 'rgba(255, 107, 44, 0.1)',
        borderColor: 'rgba(255, 107, 44, 0.2)',
        gradient: 'linear-gradient(135deg, #fff1eb, #ffe4d6)'
      },
      'secondary': {
        color: '#805AD5', // Couleur secondaire (violet)
        bgcolor: 'rgba(159, 122, 234, 0.1)',
        borderColor: 'rgba(159, 122, 234, 0.2)',
        gradient: 'linear-gradient(135deg, #f3eeff, #e5d8ff)'
      },
      'success': {
        color: '#38A169', // Vert pour succès
        bgcolor: 'rgba(56, 161, 105, 0.1)',
        borderColor: 'rgba(56, 161, 105, 0.2)',
        gradient: 'linear-gradient(135deg, #ebf8f2, #d1f0e4)'
      },
      'error': {
        color: '#E53E3E', // Rouge pour erreur
        bgcolor: 'rgba(229, 62, 62, 0.1)',
        borderColor: 'rgba(229, 62, 62, 0.2)',
        gradient: 'linear-gradient(135deg, #fdf2f2, #fcd9d9)'
      },
      'warning': {
        color: '#D69E2E', // Jaune pour avertissement
        bgcolor: 'rgba(214, 158, 46, 0.1)',
        borderColor: 'rgba(214, 158, 46, 0.2)',
        gradient: 'linear-gradient(135deg, #fefceb, #fdf8d6)'
      },
      'info': {
        color: '#3182CE', // Bleu pour information
        bgcolor: 'rgba(49, 130, 206, 0.1)',
        borderColor: 'rgba(49, 130, 206, 0.2)',
        gradient: 'linear-gradient(135deg, #ebf4ff, #d6e8ff)'
      },
      'default': {
        color: '#4A5568', // Gris par défaut
        bgcolor: 'rgba(113, 128, 150, 0.1)',
        borderColor: 'rgba(113, 128, 150, 0.2)',
        gradient: 'linear-gradient(135deg, #f6f7f9, #edf0f4)'
      }
    };

    return colorMap[chipColor] || colorMap.default;
  };

  // Fonction pour obtenir la couleur du chip en fonction du type
  const getChipColor = (type: string) => {
    const colorMap: { [key: string]: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' } = {
      'mise_a_jour_profil': 'success',
      'mise_a_jour_photo_profil': 'success',
      'suppression_photo_galerie': 'error',
      'transfert_jobi': 'primary',
      'reception_jobi': 'success',
      'mise_a_jour_mission': 'info',
      'creation_mission': 'success',
      'mission_create': 'success',
      'annulation_mission': 'error',
      'completion_mission': 'success',
      'acceptation_mission': 'success',
      'refus_mission': 'warning',
      'ajout_service': 'success',
      'mise_a_jour_service': 'info',
      'suppression_service': 'error',
      'creation_galerie': 'success',
      'mise_a_jour_galerie': 'info',
      'suppression_galerie': 'error',
      'ajout_photo_galerie': 'success',
      'gallery_activate': 'success',
      'gallery_deactivate': 'warning',
      'gallery_create': 'success',
      'evaluation_mission': 'success',
      'envoi_message': 'info',
      'lecture_notification': 'info',
      'review_update': 'info',
      'review_delete': 'error',
      'review_response_update': 'info',
      'review_response_delete': 'error',
      'review_response_create': 'success',
      'review_response_read': 'info',
      'favorite_add': 'success',
      'favorite_delete': 'error',
      'favorite_read': 'info',
      'favorite_update': 'info',
      'favorite_create': 'success',
      'favorite_remove': 'error',
      'jobi_transfer': 'primary',
      'jobi_receive': 'success',
      'reactivation_renouvellement_auto': 'success',
      'desactivation_renouvellement_auto': 'error',
      'renouvellement_abonnement': 'info',
      'passage_premium': 'success',
      'passage_gratuit': 'success',
      'downgrade_gratuit': 'success',
      'upgrade_premium': 'success',
      'cancel_auto_renew': 'error',
      'reactivate_auto_renew': 'success',
      'proposal_accept': 'success',
      'proposal_reject': 'error',
      'proposal_create': 'info',
      'proposal_update': 'info',
      'proposal_delete': 'error',
      'creation_conversation': 'info',
      'mission_proposal_create': 'info',
      'review_response': 'info',
      'gallery_photo_add': 'success',
      'gallery_photo_delete': 'error',
      'gallery_photo_update': 'info',
      'profile_photo_update': 'success',
      'review_create': 'success',
      'mission_comment': 'info',
      'support_ticket_create': 'info',
      'support_ticket_update': 'info',
      'support_ticket_delete': 'error',
      'creation_rapport_bug': 'error',
      'ban_user': 'error',
      'unban_user': 'success',
      'reactivation_after_unban': 'success',
      'email_verifie': 'success',

      // Crédits IA
      'achat_credits_ia_jobi': 'primary',
      'achat_credits_ia_stripe': 'primary',
      'utilisation_credit_ia': 'info',
      'credits_ia_offerts_abonnement': 'success',
      'credits_ia_offerts_admin': 'success',
      'operation_credits_ia': 'info',
      'ai_credits_purchase_jobi': 'primary',
      'ai_credits_purchase_stripe': 'primary',
      'ai_credits_used': 'info',
      'ai_credits_subscription_gift': 'success',
      'ai_credits_admin_gift': 'success',
      'ai_credits_other': 'info',
      'ai_credit_used': 'info',
      'ai_credits_purchased': 'primary',
      'ai_credits_checkout_created': 'warning',
      'ai_credits_purchased_stripe': 'primary',
      'gallery_update': 'info',
      'gallery_delete': 'error',
      'ai_consent': 'info',
      'ai_prompt_saved': 'success',
      'ai_prompt_deleted': 'error',
      'image_confirmed': 'primary',
      'inscription_utilisateur': 'success',

      // Admin
      'admin_view_user_history': 'info',
      'admin_photo_moderation': 'error',
      'demande_suppression_compte': 'error',
      'profile_banner_update': 'success',
      'generate_random_card_template': 'success',
      'auto_deactivate_card_template': 'error',
      'update_card_template': 'info',
    };

    return colorMap[type] || 'default';
  };

  // Fonction pour obtenir l'icône en fonction du type d'action
  const getActionIcon = (type: string) => {
    // Actions liées au bannissement et débannissement
    if (type === 'ban_user') {
      return <UserPlus size={18} strokeWidth={1.75} style={{ color: '#E53E3E' }} />;
    } else if (type === 'unban_user') {
      return <UserPlus size={18} strokeWidth={1.75} style={{ color: '#38A169' }} />;
    } else if (type === 'reactivation_after_unban') {
      return <BadgeCheck size={18} strokeWidth={1.75} style={{ color: '#38A169' }} />;
    } else if (type === 'email_verifie') {
      return <CheckCircle size={18} strokeWidth={1.75} style={{ color: '#38A169' }} />;
    }

    // Types d'action liés aux crédits IA
    if (type.includes('ai_credits') || type.includes('credits_ia')) {
      return <Coins size={18} strokeWidth={1.75} style={{ color: '#FF6B2C' }} />;
    }

    // Types d'action liés aux prompts IA
    if (type === 'ai_prompt_saved') {
      return <Star size={18} strokeWidth={1.75} style={{ color: '#38A169' }} />;
    } else if (type === 'ai_prompt_deleted') {
      return <Star size={18} strokeWidth={1.75} style={{ color: '#E53E3E' }} />;
    }

    // Types d'action liés à Jobi
    if (['transfert_jobi', 'jobi_transfer'].includes(type)) {
      return <ArrowUpCircle size={18} strokeWidth={1.75} />;
    } else if (['reception_jobi', 'jobi_receive'].includes(type)) {
      return <ArrowDownCircle size={18} strokeWidth={1.75} />;
    } else if (type.includes('jobi')) {
      return <Coins size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux favoris
    if (type.includes('favorite')) {
      return <Heart size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux missions
    if (type.includes('mission')) {
      return <Briefcase size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux évaluations
    if (type.includes('review') || type.includes('evaluation')) {
      return <Star size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux messages
    if (type.includes('message')) {
      return <MessageCircle size={18} strokeWidth={1.75} />;
    }

    // Actions liées au profil
    if (type.includes('profil') || type.includes('user')) {
      return <UserPlus size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux services
    if (type.includes('service')) {
      return <BadgeCheck size={18} strokeWidth={1.75} />;
    }

    // Actions liées aux photos/galeries
    if (type.includes('photo') || type.includes('galerie')) {
      return <Image size={18} strokeWidth={1.75} />;
    }

    // Par défaut
    return <ClipboardList size={18} strokeWidth={1.75} />;
  };

  const renderActionDetails = (action: PublicUserAction) => {
    let details = 'Aucun détail disponible';

    // Traitement spécial pour les actions de bannissement et débannissement
    if (action.action_type === 'ban_user') {
      return 'Le compte a été temporairement suspendu';
    } else if (action.action_type === 'unban_user') {
      return 'Le compte a été réactivé';
    } else if (action.action_type === 'reactivation_after_unban') {
      return 'L\'abonnement a été réactivé suite au débannissement du compte';
    } else if (action.action_type === 'email_verifie') {
      return 'L\'adresse email a été vérifiée avec succès';
    } else if (action.action_type === 'reactivate_auto_renew') {
      return 'Le renouvellement automatique de l\'abonnement a été réactivé';
    } else if (action.action_type === 'achat_credits_ia_jobi' || action.action_type === 'ai_credits_purchase_jobi') {
      const montant = action.details?.montant || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA avec des Jobi`;
    } else if (action.action_type === 'achat_credits_ia_stripe' || action.action_type === 'ai_credits_purchase_stripe') {
      const montant = action.details?.montant || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA via Stripe`;
    } else if (action.action_type === 'utilisation_credit_ia' || action.action_type === 'ai_credits_used' || action.action_type === 'ai_credit_used') {
      return 'Utilisation d\'un crédit IA';
    } else if (action.action_type === 'credits_ia_offerts_abonnement' || action.action_type === 'ai_credits_subscription_gift') {
      const montant = action.details?.montant || '';
      return `${montant} crédit${montant > 1 ? 's' : ''} IA offert${montant > 1 ? 's' : ''} avec votre abonnement`;
    } else if (action.action_type === 'credits_ia_offerts_admin' || action.action_type === 'ai_credits_admin_gift') {
      const montant = action.details?.montant || '';
      return `${montant} crédit${montant > 1 ? 's' : ''} IA offert${montant > 1 ? 's' : ''} par l'administrateur`;
    } else if (action.action_type === 'ai_credits_purchased' || action.action_type === 'ai_credits_purchased_stripe') {
      const montant = action.details?.credits_purchased || action.details?.total_credits_purchased || '';
      return `Achat de ${montant} crédit${montant > 1 ? 's' : ''} IA${action.action_type === 'ai_credits_purchased_stripe' ? ' via Stripe' : ' avec des Jobi'}`;
    } else if (action.action_type === 'ai_credits_checkout_created') {
      const montant = action.details?.credits_to_purchase || '';
      return `Création d'une session de paiement pour ${montant} crédit${montant > 1 ? 's' : ''} IA`;
    } else if (action.action_type === 'gallery_activate') {
      return action.details?.gallery_name ?
        `Galerie "${action.details.gallery_name}" activée` :
        'Galerie activée';
    } else if (action.action_type === 'gallery_deactivate') {
      return action.details?.gallery_name ?
        `Galerie "${action.details.gallery_name}" désactivée` :
        'Galerie désactivée';
    } else if (action.action_type === 'creation_galerie' || action.action_type === 'gallery_create') {
      return action.details?.gallery_name ?
        `Nouvelle galerie créée : "${action.details.gallery_name}"` :
        'Nouvelle galerie créée';
    } else if (action.action_type === 'ai_consent') {
      const detailsObj = typeof action.details === 'string' ? JSON.parse(action.details) : action.details;
      if (detailsObj && (detailsObj.firstName || detailsObj.lastName)) {
        return `Consentement IA donné : ${detailsObj.firstName || ''} ${detailsObj.lastName || ''}`.trim();
      }
    } else if (action.action_type === 'ai_prompt_saved') {
      return 'Prompt IA sauvegardé';
    } else if (action.action_type === 'ai_prompt_deleted') {
      return 'Prompt IA supprimé';
    } else if (action.action_type === 'image_confirmed') {
      return 'Image générée par IA validée';
    } else if (action.action_type === 'inscription_utilisateur') {
      return 'Nouvelle inscription utilisateur';
    } else if (action.action_type === 'generate_random_card_template') {
      return 'Génération d\'un modèle de carte aléatoire';
    } else if (action.action_type === 'auto_deactivate_card_template') {
      return 'Désactivation automatique du modèle de carte';
    } else if (action.action_type === 'update_card_template') {
      return 'Mise à jour du modèle de carte';
    } else if (action.action_type === 'demande_suppression_compte') {
      return 'Demande de suppression de compte';
    } else if (action.action_type === 'profile_banner_update') {
      return 'Mise à jour de la bannière de profil';
    } else if (action.action_type === 'admin_photo_moderation') {
      return 'Modération de la photo de profil';
    } else if (action.action_type === 'admin_view_user_history') {
      return 'Affichage de l\'historique de l\'utilisateur';
    } else if (action.action_type === 'review_response') {
      return 'Réponse à un avis';
    } else if (action.action_type === 'favorite_add') {
      return 'Ajout aux favoris';
    } else if (action.action_type === 'favorite_remove') {
      return 'Suppression des favoris';
    } else if (action.action_type === 'password_change') {
      return 'Changement de mot de passe';
    } else if (action.action_type === 'email_verification') {
      return 'Vérification email';  
    } else if (action.action_type === 'subscription_change') {
      return 'Changement d\'abonnement';
    } else if (action.action_type === 'ai_credits_usage') {
      return 'Utilisation crédits IA';
    } else if (action.action_type === 'photo_upload') {
      return 'Upload de photo';
    } else if (action.action_type === 'message_sent') {
      return 'Message envoyé';
    } else if (action.action_type === 'notification_read') {
      return 'Notification lue';
    } else if (action.action_type === 'conversation_create') {
      return 'Création de conversation';
    } else if (action.action_type === 'conversation_update') {
      return 'Mise à jour de conversation';
    } else if (action.action_type === 'conversation_delete') {
      return 'Suppression de conversation';
    } else if (action.action_type === 'conversation_message_send') {
      return 'Message envoyé dans conversation';
    } else if (action.action_type === 'conversation_message_delete') {
      return 'Suppression de message dans conversation';
    } else if (action.action_type === 'gallery_activate') {
      return 'Galerie activée';
    } else if (action.action_type === 'gallery_deactivate') {
      return 'Galerie désactivée';
    } else if (action.action_type === 'gallery_create') {
      return 'Nouvelle galerie créée';
    } else if (action.action_type === 'gallery_photos_add_multiple') {
      return 'Ajout de photos à la galerie';
    } else if (action.action_type === 'gallery_photo_delete') {
      return 'Suppression de photo dans galerie';
    } else if (action.action_type === 'gallery_photo_update') {
      return 'Mise à jour de photo dans galerie';
    } else if (action.action_type === 'gallery_photo_create') {
      return 'Nouvelle photo dans galerie';
    } else if (action.action_type === 'gallery_photo_read') {
      return 'Lecture de photo dans galerie';
    } else if (action.action_type === 'gallery_photo_read_multiple') {
      return 'Lecture de photos dans galerie';
    } else if (action.action_type === 'gallery_photo_read_all') {
      return 'Lecture de toutes les photos dans galerie';
    }

    if (action.details) {
      try {
        const detailsObj = typeof action.details === 'string' ? JSON.parse(action.details) : action.details;

        // Traitement spécial pour les transactions Jobi
        if (action.action_type === 'transfert_jobi' || action.action_type === 'jobi_transfer') {
          return 'Transfert de Jobi à un autre utilisateur';
        } else if (action.action_type === 'reception_jobi' || action.action_type === 'jobi_receive') {
          return 'Réception de Jobi d\'un autre utilisateur';
        }

        // Amélioration des messages pour des actions spécifiques
        if (action.action_type === 'favorite_add' || action.action_type === 'favorite_create') {
          return detailsObj.message ?
            `Profil ajouté aux favoris${detailsObj.user_name ? `: ${detailsObj.user_name}` : ''}`
            : 'Nouveau profil ajouté aux favoris';
        }

        if (action.action_type === 'favorite_remove' || action.action_type === 'favorite_delete') {
          return detailsObj.message ?
            `Profil retiré des favoris${detailsObj.user_name ? `: ${detailsObj.user_name}` : ''}`
            : 'Profil retiré des favoris';
        }

        if (action.action_type === 'evaluation_mission') {
          return detailsObj.message ?
            `Évaluation d'une mission${detailsObj.mission_title ? `: ${detailsObj.mission_title}` : ''}`
            : 'Nouvelle évaluation de mission créée';
        }

        if (action.action_type === 'acceptation_mission') {
          return detailsObj.message ?
            `Mission acceptée${detailsObj.mission_title ? `: ${detailsObj.mission_title}` : ''}`
            : 'Mission acceptée';
        }

        if (action.action_type === 'completion_mission') {
          return detailsObj.message ?
            `Mission terminée${detailsObj.mission_title ? `: ${detailsObj.mission_title}` : ''}`
            : 'Mission terminée avec succès';
        }

        if (detailsObj.message) {
          // Vérifier si le message contient une mention de montant de Jobi
          if (/Transfert de \d+ Jobi|Réception de \d+ Jobi|\d+ Jobi/.test(detailsObj.message)) {
            if (action.action_type.includes('jobi') || action.action_type.includes('transaction')) {
              return action.action_type.includes('reception') || action.action_type.includes('receive')
                ? 'Réception de Jobi d\'un autre utilisateur'
                : 'Transfert de Jobi à un autre utilisateur';
            }
          }

          // Améliorer les messages génériques
          if (detailsObj.message === "Ajout aux favoris") {
            return "Nouveau profil ajouté à vos favoris";
          }

          details = detailsObj.message;
        } else if (detailsObj.name) {
          details = detailsObj.name;
        } else if (typeof detailsObj === 'string') {
          // Vérifier si la chaîne contient une mention de montant de Jobi
          if (/Transfert de \d+ Jobi|Réception de \d+ Jobi|\d+ Jobi/.test(detailsObj)) {
            if (action.action_type.includes('jobi') || action.action_type.includes('transaction')) {
              return action.action_type.includes('reception') || action.action_type.includes('receive')
                ? 'Réception de Jobi d\'un autre utilisateur'
                : 'Transfert de Jobi à un autre utilisateur';
            }
          }
          details = detailsObj;
        } else {
          // Extraire les informations importantes et les formatter de façon plus lisible
          const keyMapping: { [key: string]: string } = {
            'expediteur_id': 'Expéditeur',
            'expediteur_slug': 'De',
            'destinataire_id': 'Destinataire',
            'destinataire_slug': 'À',
            'solde_final': 'Solde final',
            'solde_initial': 'Solde initial',
            'message': 'Message',
            'mission_id': 'Mission',
            'mission_title': 'Titre de la mission',
            'user_id': 'Utilisateur',
            'user_name': 'Nom',
            'rating': 'Note',
            'review_text': 'Commentaire'
          };

          // Filtrer les clés sensibles et améliorer l'affichage
          const formattedDetails = Object.keys(detailsObj)
            .filter(key => !['montant', 'amount', 'balance', 'solde', 'password', 'token'].includes(key.toLowerCase()))
            .map(key => {
              const displayKey = keyMapping[key] || key;
              let value = detailsObj[key];

              // Formatage spécial pour certaines valeurs
              if (key === 'expediteur_slug' || key === 'destinataire_slug') {
                value = `@${value}`;
              }

              return `${displayKey}: ${value}`;
            })
            .join(' | ');

          details = formattedDetails || 'Action effectuée avec succès';
        }
      } catch (e) {
        details = String(action.details);

        // Dernière vérification sur la chaîne brute pour les montants
        if (/Transfert de \d+ Jobi|Réception de \d+ Jobi|\d+ Jobi/.test(details)) {
          if (action.action_type.includes('jobi') || action.action_type.includes('transaction')) {
            return action.action_type.includes('reception') || action.action_type.includes('receive')
              ? 'Réception de Jobi d\'un autre utilisateur'
              : 'Transfert de Jobi à un autre utilisateur';
          }
        }
      }
    }

    return DOMPurify.sanitize(details);
  };

  // Rendu du contenu (juste les actions)
  const renderContent = () => {
    if (loading) {
      return (
        <Box className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                p: 2,
                borderRadius: 2,
                border: '1px solid #e0e0e0',
                bgcolor: 'background.paper',
                mb: 2
              }}
            >
              <Skeleton variant="circular" width={36} height={36} sx={{ mr: 2 }} />
              <Box sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Skeleton variant="text" width="40%" height={24} />
                  <Skeleton variant="text" width="20%" height={20} />
                </Box>
                <Skeleton variant="text" width="70%" height={20} />
              </Box>
            </Box>
          ))}
        </Box>
      );
    }

    if (error) {
      return (
        <Typography variant="body2" color="error" className="text-sm p-4">
          {error}
        </Typography>
      );
    }

    if (!actions.length) {
      return (
        <EmptyStateBox sx={{ py: 3 }}>
          <History size={36} color="#94A3B8" style={{ marginBottom: '0.5rem' }} />
          <Typography variant="body2" color="text.secondary">
            Aucune activité récente à afficher
          </Typography>
        </EmptyStateBox>
      );
    }

    const filteredActions = actions.filter(action => !excludedActionTypes.includes(action.action_type));

    if (!filteredActions.length) {
      return (
        <EmptyStateBox sx={{ py: 3 }}>
          <History size={36} color="#94A3B8" style={{ marginBottom: '0.5rem' }} />
          <Typography variant="body2" color="text.secondary">
            Aucune activité pertinente à afficher
          </Typography>
        </EmptyStateBox>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="w-full"
      >
        <Box className="space-y-4" sx={{ maxHeight: `${maxHeight}px`, overflowY: 'auto', pr: 1 }}>
          {filteredActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="border-b border-gray-100 pb-3 last:border-0 last:pb-0"
            >
              <Box sx={{
                display: 'flex',
                alignItems: 'flex-start',
                gap: 2,
              }}>
                <Box sx={{
                  position: 'relative',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center'
                }}>
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      background: getAvatarStyle(action.action_type).gradient,
                      color: getAvatarStyle(action.action_type).color,
                      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)',
                      border: 'none',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        borderRadius: '50%',
                        padding: '2px',
                        background: getAvatarStyle(action.action_type).gradient,
                        WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                        WebkitMaskComposite: 'xor',
                        maskComposite: 'exclude',
                      }
                    }}
                  >
                    {getActionIcon(action.action_type)}
                  </Avatar>
                </Box>

                <Box sx={{ flexGrow: 1 }}>
                  <Box className="flex items-center gap-2 mb-1">
                    <StyledChip
                      label={formatActionType(action.action_type)}
                      size="small"
                      color={getChipColor(action.action_type)}
                    />
                    <Tooltip title={formatDate(action.action_date)}>
                      <Typography variant="caption" className="text-gray-500 flex items-center">
                        <Calendar
                          size={12}
                          strokeWidth={2}
                          style={{ marginRight: '4px' }}
                        />
                        {format(new Date(action.action_date), 'dd MMM yyyy à HH:mm', { locale: fr })}
                      </Typography>
                    </Tooltip>
                  </Box>
                  <Typography variant="body2" className="text-gray-700 text-sm">
                    {renderActionDetails(action)}
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          ))}
        </Box>
      </motion.div>
    );
  };

  // Rendu complet avec Paper et titre (si variant n'est pas 'content-only')
  if (variant === 'content-only') {
    return renderContent();
  }

  return (
    <Paper
      elevation={0}
      className={className}
      sx={{
        backgroundColor: 'white',
        border: '1px solid #EDF2F7',
        borderRadius: '12px',
        boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
        overflow: 'hidden'
      }}
    >
      <Box sx={{
        p: 2,
        borderBottom: '1px solid #EDF2F7',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'rgba(255, 107, 44, 0.03)'
      }}>
        <Typography variant="h6" sx={{
          fontSize: '1.1rem',
          fontWeight: 600,
          color: '#2D3748',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <History size={18} color="#FF6B2C" strokeWidth={2} />
          {title}
        </Typography>
        {actions.length > 0 && (
          <Box
            sx={{
              backgroundColor: 'rgba(255, 107, 44, 0.1)',
              color: '#FF6B2C',
              px: 1.5,
              py: 0.5,
              borderRadius: '12px',
              fontSize: '0.7rem',
              fontWeight: 600
            }}
          >
            {actions.length} {actions.length > 1 ? 'activités' : 'activité'}
          </Box>
        )}
      </Box>
      <Box sx={{ p: 2 }}>
        {renderContent()}
      </Box>
    </Paper>
  );
};

export default RecentUserActions;