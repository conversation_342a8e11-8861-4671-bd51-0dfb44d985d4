import React from 'react';
import { AlertTriangle, X } from 'lucide-react';
import ModalPortal from '../../../components/ModalPortal';
import { motion } from 'framer-motion';

interface DeleteDocumentDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  documentType: 'devis' | 'facture' | 'avoir';
  documentNumber: string;
}

const DeleteDocumentDialog: React.FC<DeleteDocumentDialogProps> = ({
  open,
  onClose,
  onConfirm,
  documentType,
  documentNumber
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="bg-white rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] w-full max-w-[500px] p-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Supprimer le {documentType}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="mt-1">
              <AlertTriangle size={24} className="text-[#FF7A35]" />
            </div>
            <p className="text-gray-700">
              Êtes-vous sûr de vouloir supprimer définitivement {documentType === 'devis' ? 'ce' : documentType === 'facture' ? 'cette' : 'cet'} {documentType} {documentNumber} ?
            </p>
          </div>
          
          <p className="text-sm text-gray-500 pl-9">
            Cette action est irréversible. Seuls les documents en brouillon peuvent être supprimés.
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 text-white bg-[#FF7A35] rounded-lg hover:bg-[#ff6b2c] transition-colors"
          >
            Supprimer
          </button>
        </div>
      </motion.div>
    </ModalPortal>
  );
};

export default DeleteDocumentDialog; 