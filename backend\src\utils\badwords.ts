/**
 * Liste centralisée des mots et expressions interdits
 * Utilisée par le système de modération de contenu
 */

// Liste principale des mots interdits
export const badWords = [
  // Insultes et grossièretés courantes
  'connard', 'connasse', 'connerie', 'con', 'conne', 'couillon', 'couillonne',
  'pute', 'putain', 'salope', 'salaud', 'salopard', 'saloperie',
  'encule', 'enculé', 'enculée', 'enculer', 'enculade',
  'fdp', 'fils de pute', 'fils de p', 'fils de p ', 'fils de pu',
  'pd', 'pede', 'pédé', 'pédale', 'tapette', 'tafiole', 'tantouze', 'tarlouze',
  'gouine', 'gouinasse', 'gouinerie',
  'pute', 'putain', 'putasse', 'prostituée', 'prostituee',
  'bite', 'bitte', 'zob', 'zizi', 'queue', 'teub', 'chibre', 'pine',
  'couille', 'couilles', 'burnes', 'testicules', 'testiboules',
  'cul', 'fion', 'trou du cul', 'trou de cul', 'trou de balle', 'troufion',
  'chatte', 'chate', 'vagin', 'foufoune', 'foufe', 'moule', 'cramouille',
  'nichon', 'nichons', 'nibard', 'nibards', 'roberts', 'seins', 'mamelle',
  'caca', 'merde', 'chiasse', 'chiure', 'crotte', 'etron', 'étron',
  'pisse', 'pipi', 'urine', 'pisser',
  'baiser', 'baise', 'niquer', 'nique', 'ken', 'kener',
  'se branler', 'branlette', 'branler', 'masturber', 'masturbation',
  'sucer', 'suce', 'pipe', 'pomper', 'fellation', 'suceuse',
  'sodomie', 'sodomiser', 'enculade', 'enculer',
  'levrette', 'levretter', 'missionnaire',

  // Termes vulgaires et insultes
  'abruti', 'abrutie', 'andouille', 'anus', 'attardé', 'attardée',
  'batard', 'bâtard', 'batarde', 'bâtarde', 'bordel', 'branleur', 'branleuse',
  'casse couille', 'casse burnes', 'casse bonbon', 'chier', 'chiant', 'chiante',
  'clito', 'clitoris', 'debile', 'débile', 'ducon', 'emmerde', 'emmerder',
  'fouteur', 'fouteuse', 'foutre', 'gland', 'glandu', 'glandeur', 'glandeuse',
  'grognasse', 'idiot', 'idiote', 'imbecile', 'imbécile', 'kikoo', 'lmao', 'lol',
  'lopette', 'merdeux', 'merdeuse', 'michto', 'minable', 'mongol', 'mongolien',
  'morue', 'ntm', 'nique', 'niquer', 'petasse', 'pétasse', 'pouffiasse',
  'pouffe', 'pourriture', 'punaise', 'putain', 'pute', 'raclure', 'sac à merde',
  'sac a foutre', 'salop', 'schleu', 'tanche', 'tg',
  'thon', 'tocard', 'trainée', 'trainee', 'trou duc', 'va te faire', 'vtf',
  'zigoto', 'zgueg', 'zob',

  // Termes haineux et discriminatoires
  'nazi', 'nazisme', 'fasciste', 'fascisme', 'swastika', 'svastika',
  'nègre', 'negre', 'negresse', 'négresse', 'bamboula', 'bougnoule', 'bougnoul',
  'bicot', 'bougnoule', 'bougnoul', 'boucaque', 'boukak', 'noiraud',
  'youpin', 'youde', 'youtre', 'youpine', 'yid', 'feuj', 'juif', 'juive',
  'arabe', 'bougnoule', 'bicot', 'raton', 'melon', 'beur', 'beurette',
  'chintok', 'chinetoque', 'chinetoc', 'bridé', 'bride', 'niakoué', 'niakoue',

  // Termes discriminatoires
  'antisemite', 'antisémite', 'apartheid', 'boche', 'bosch', 'clochard', 'fritz',
  'gwer', 'gwère', 'islamiste', 'islamisme', 'jihadiste', 'djihadiste',
  'macaque', 'meteque', 'métèque', 'niakoue', 'niakoué', 'polack', 'polak',
  'racaille', 'rosbif', 'sale arabe', 'sale juif', 'sale noir', 'sale blanc',
  'sioniste', 'tchoin', 'toubab', 'travelo', 'xénophobe', 'xenophobe',

  // Termes liés à la violence
  'assassin', 'assassinat', 'buter', 'casser la gueule', 'crevard', 'crever',
  'décapiter', 'decapiter', 'égorger', 'egorger', 'éliminer', 'eliminer',
  'étriper', 'etriper', 'flingue', 'flinguer', 'fusiller', 'lyncher',
  'lynchage', 'massacrer', 'massacre', 'meurtre', 'meurtrier', 'occire',
  'péter la gueule', 'peter la gueule', 'shooter', 'torturer', 'torture',

  // Termes liés aux activités illégales
  'amphétamine', 'amphetamine', 'blanca', 'cartel', 'crystal', 'crystal meth',
  'defonce', 'défonce', 'dope', 'ecsta', 'extasy', 'fief', 'hash', 'hashish',
  'héro', 'hero', 'junkie', 'junky', 'keta', 'ketamine', 'kétamine', 'meth',
  'morphine', 'opium', 'overdose', 'rabla', 'schnouf', 'shoot', 'shooter',
  'speed', 'spliff', 'stup', 'tox', 'toxicomane', 'trafiquant',

  // Termes liés au terrorisme et extrémisme
  'alqaida', 'al qaida', 'al quaida', 'al qaeda', 'al-qaïda', 'antifa',
  'califat', 'daesh', 'djihad', 'fatwa', 'isis', 'jihad', 'kamikaze',
  'moudjahidine', 'talibans', 'terrorisme', 'terroriste',

  // Termes liés à l'automutilation et suicide
  'anorexie', 'boulimie', 'cutting', 'depressif', 'dépressif', 'depression',
  'dépression', 'me tuer', 'mourir', 'suicidaire', 'suicide', 'suicider',
  'scarification', 'scarifier', 'se tuer', 'overdose',

  // Spam et arnaques avancés
  'acheter des vues', 'acheter des likes', 'acheter des followers',
  'arnaque', 'arnaquer', 'bitcoin rapide', 'crypto rapide', 'devenir riche',
  'enrichissement rapide', 'escort', 'escorts', 'faux billets', 'faux papiers',
  'gagner argent', 'gagner facilement', 'investissement garanti',
  'money maker', 'passports', 'pilules', 'plan ponzi', 'ponzi', 'pyramide',
  'revenus passifs', 'trading facile', 'vente pyramidale',

  // Variantes avec espaces ou caractères spéciaux
  'f d p', 'fdp', 'f.d.p', 'f d p', 'f.d.p.', 'f-d-p',
  'p d', 'p.d', 'p.d.', 'p-d',
  's a l o p e', 's.a.l.o.p.e', 's-a-l-o-p-e',
  'c o n', 'c.o.n', 'c-o-n',
  'b i t e', 'b.i.t.e', 'b-i-t-e',
  'm e r d e', 'm.e.r.d.e', 'm-e-r-d-e',
  'n i q u e', 'n.i.q.u.e', 'n-i-q-u-e',
  't g', 't.g', 't-g',
  'n t m', 'n.t.m', 'n-t-m',
  'v t f', 'v.t.f', 'v-t-f',
  'f t g', 'f.t.g', 'f-t-g'
];

// Liste des termes manifestement inappropriés (insultes graves, termes sexuels explicites, etc.)
// Cette liste est un sous-ensemble de badWords, contenant les termes les plus graves
export const obviouslyInappropriateTerms = [
  // Insultes graves
  "connard", "connasse", "con ", " con", "conne", "couillon", "couillonne",
  "pute", "putain", "salope", "salaud", "salopard", "saloperie",
  "encule", "enculé", "enculée", "enculer", "enculade",
  "fdp", "fils de pute", "fils de p", "fils de p ", "fils de pu",
  "pd", "pede", "pédé", "pédale", "tapette", "tafiole", "tantouze", "tarlouze",
  "gouine", "gouinasse", "gouinerie",
  "ta gueule", "tg", "ftg", "ntm", "nique", "niquer",
  
  // Termes sexuels explicites
  "bite", "bitte", "zob", "queue", "teub", "chibre", "pine",
  "couille", "couilles", "burnes", "testicules",
  "cul", "fion", "trou du cul", "trou de cul", "trou de balle",
  "chatte", "chate", "vagin", "foufoune", "foufe", "moule",
  "nichon", "nichons", "nibard", "nibards", "roberts", "seins",
  "baiser", "baise", "niquer", "ken", "kener",
  "sucer", "suce", "pipe", "pomper", "fellation",
  "sodomie", "sodomiser",
  
  // Termes violents
  "tuer", "frapper", "battre", "casser la gueule", "massacrer",
  "assassin", "assassinat", "buter", "crever", "décapiter", "decapiter",
  "égorger", "egorger", "éliminer", "eliminer", "étriper", "etriper",
  
  // Termes discriminatoires
  "négro", "negre", "negresse", "négresse", "bamboula", "bougnoule", "bougnoul",
  "bicot", "boucaque", "boukak", "noiraud",
  "youpin", "youde", "youtre", "youpine", "yid", "feuj",
  "arabe", "bougnoule", "bicot", "raton", "melon", "beur",
  "chintok", "chinetoque", "chinetoc", "bridé", "bride", "niakoué", "niakoue",
  "nazi", "nazisme", "fasciste", "fascisme", "swastika", "svastika"
];

// Expressions inappropriées avec espaces (qui ne seraient pas détectées par includes)
export const inappropriateExpressions = [
  /\bva te faire\b/i, 
  /\bvtf\b/i, 
  /\bta gueule\b/i, 
  /\bta mere\b/i, 
  /\bta mère\b/i,
  /\bsac a merde\b/i, 
  /\bsac à merde\b/i, 
  /\bcasse toi\b/i, 
  /\bcasse-toi\b/i,
  /\bje te baise\b/i, 
  /\bje vais te\b.*\bfrapper\b/i, 
  /\bje vais te\b.*\btuer\b/i,
  // Ajout menaces explicites/implicites
  /\bje te casse les dents\b/i,
  /\bje vais te casser les dents\b/i,
  /\bje vais te faire du mal\b/i,
  /\bje vais te retrouver\b/i,
  /\bje vais te faire payer\b/i,
  /\btu vas le regretter\b/i,
  /\btu vas voir ce que tu vas prendre\b/i,
  /\bje vais te casser la gueule\b/i,
  /\bje vais te peter la gueule\b/i,
  /\bje vais te briser les os\b/i,
  /\bje vais te detruire\b/i,
  /\bje vais te demolir\b/i,
  /\bje vais te massacrer\b/i,
  /\bje vais te faire mal\b/i,
  /\bje vais te faire souffrir\b/i,
  /\bje vais te punir\b/i,
  /\bje vais te corriger\b/i,
  /\bje vais te tabasser\b/i,
  /\bje vais te violenter\b/i,
  /\bje vais te harceler\b/i,
  /\bje vais te menacer\b/i,
  /\bje vais te nuire\b/i,
  /\bje vais te blesser\b/i,
  /\bje vais te faire pleurer\b/i,
  /\bje vais te faire tomber\b/i,
  /\bje vais te faire peur\b/i,
  /\bje vais te faire taire\b/i,
  /\bje vais te faire disparaitre\b/i,
  /\bje vais te faire saigner\b/i,
  /\bje vais te faire crier\b/i,
  /\bje vais te faire hurler\b/i,
  /\bje vais te faire souffrir\b/i,
  /\bje vais te faire du tort\b/i,
  /\bje vais te faire du mal\b/i,
  /\bje vais te faire du mal\b/i,
  /\bje vais te faire du mal\b/i
];

// Liste des termes de contact légitimes (pour détecter les faux positifs)
export const commonSafeTerms = [
  "appel", "appeler", "appelez", "contactez", "contacter", "message", 
  "envoyer", "envoyez", "retour", "rappel", "rappeler", "téléphoner",
  "joindre", "rejoindre", "recontacter", "disponible", "disponibilité",
  "bonjour", "salut", "hello", "coucou", "hey", "merci", "thanks",
  "d'accord", "ok", "okay", "oui", "non", "yes", "no",
  "à bientôt", "au revoir", "à plus", "à+",
  "pourriez", "pouvez", "peux", "pourrait", "pourront", "pourra"
];

// Liste des expressions sûres de rencontre
export const safeEncounterPhrases = [
  'on se retrouve',
  'on se retrouvera',
  'se rencontrer',
  'se voir',
  'rendez-vous',
  'rdv',
  'on se rejoint',
  'on se rejoindra',
  'on se voit',
  'on se verra',
  'rencontrer',
  'retrouver',
  'rejoindre'
];

// Patterns sûrs pour le début des messages
export const commonSafePatterns = [
  /^(bonjour|salut|hello|coucou|hey)(\s|$)/i,
  /^(merci|thanks|thank you)(\s|$)/i,
  /^(d'accord|ok|okay|oui|non|yes|no)(\s|$)/i,
  /^(à bientôt|au revoir|à plus|à\+)(\s|$)/i,
  /^(pourriez[ -]vous|pouvez[ -]vous|peux[ -]tu)(\s|$)/i,
  /^(je vous contacte|je te contacte)(\s|$)/i,
  /^(je suis disponible|je ne suis pas disponible)(\s|$)/i,
  /^(appelez[ -]moi|contactez[ -]moi)(\s|$)/i,
];

// Fonction utilitaire pour vérifier si un texte contient des mots interdits
export function containsBadWords(text: string): boolean {
  const lowercaseText = text.toLowerCase()
    .replace(/[.,:;!?*_\-'"«»()[\]{}|/\\<>]/g, ' ') // Remplacer la ponctuation par des espaces
    .replace(/\s+/g, ' ');                          // Normaliser les espaces
  
  // Vérifier les mots avec limites de mots pour éviter les faux positifs
  for (const word of badWords) {
    // Utiliser une regex avec limites de mots pour une détection plus précise
    const wordBoundaryRegex = new RegExp(`\\b${word}\\b`, 'i');
    if (wordBoundaryRegex.test(lowercaseText)) {
      return true;
    }
    
    // Pour les mots très courts (3 lettres ou moins), vérifier aussi s'ils sont entourés de caractères spéciaux
    if (word.length <= 3) {
      const specialBoundaryRegex = new RegExp(`[^a-z0-9]${word}[^a-z0-9]|^${word}[^a-z0-9]|[^a-z0-9]${word}$|^${word}$`, 'i');
      if (specialBoundaryRegex.test(lowercaseText)) {
        return true;
      }
    }
  }
  
  // Vérifier les expressions inappropriées
  for (const expr of inappropriateExpressions) {
    if (expr.test(lowercaseText)) {
      return true;
    }
  }
  
  return false;
}

// Fonction utilitaire pour vérifier si un texte contient du contenu manifestement inapproprié
export function containsObviouslyInappropriateContent(text: string): boolean {
  // Normaliser le texte pour la détection
  const normalizedText = text.toLowerCase()
    .replace(/[.,:;!?*_\-'"«»()[\]{}|/\\<>]/g, ' ') // Remplacer la ponctuation par des espaces
    .replace(/\s+/g, ' ')                          // Normaliser les espaces
    .replace(/[0àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ1234567890]/g, function(match) {
      // Remplacer les caractères accentués et les chiffres utilisés pour contourner les filtres
      const accentMap: {[key: string]: string} = {
        'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a', 'æ': 'ae',
        'ç': 'c', 'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e', 'ì': 'i', 'í': 'i',
        'î': 'i', 'ï': 'i', 'ð': 'd', 'ñ': 'n', 'ò': 'o', 'ó': 'o', 'ô': 'o',
        'õ': 'o', 'ö': 'o', 'ø': 'o', 'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
        'ý': 'y', 'þ': 'th', 'ÿ': 'y',
        '0': 'o', '1': 'i', '3': 'e', '4': 'a', '5': 's', '7': 't', '8': 'b'
      };
      return accentMap[match] || match;
    });
  
  // Vérifier si le texte contient l'un des termes manifestement inappropriés
  for (const term of obviouslyInappropriateTerms) {
    // Pour les termes courts (3 lettres ou moins), utiliser une regex avec limites de mots
    if (term.length <= 3) {
      const regex = new RegExp(`\\b${term}\\b|[^a-z0-9]${term}[^a-z0-9]|^${term}[^a-z0-9]|[^a-z0-9]${term}$|^${term}$`, 'i');
      if (regex.test(normalizedText)) {
        return true;
      }
    } 
    // Pour les termes plus longs, vérifier s'ils sont présents dans le texte
    else if (normalizedText.includes(term)) {
      return true;
    }
  }
  
  // Vérifier les expressions avec espaces
  for (const expr of inappropriateExpressions) {
    if (expr.test(normalizedText)) {
      return true;
    }
  }
  
  return false;
}
