/*
 * IMPORTANT: Ne pas supprimer les styles suivants qui sont essentiels pour le bon fonctionnement :
 * 1. Les classes .Toastify et .notification-container pour le positionnement
 * 2. L'utilisation de !important sur les backgrounds pour surcharger react-hot-toast
 * 3. Les styles de base de .notification
 */

/* Styles globaux pour les notifications */
:root {
  --toastify-color-success: #10B981;
  --toastify-color-error: #EF4444;
  --toastify-color-warning: #F59E0B;
  --toastify-color-info: #3B82F6;
  --shadow-elegant: 0 25px 50px -12px rgba(0, 0, 0, 0.08), 
                    0 10px 15px -3px rgba(0, 0, 0, 0.05);
  --primary-color: #FF7A35;
  --secondary-color: #FF965E;
  --background-color: #FFF8F3;
  --text-color: #1F2937;
}

.Toastify__toast-container {
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 9999;
}

/* Container principal des notifications */
.notification {
  position: relative;
  display: flex;
  align-items: center;
  width: 420px;
  padding: 20px;
  margin-bottom: 15px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: var(--shadow-elegant);
  overflow: hidden;
  transform-origin: right center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(var(--primary-color), 0.2);
}

.notification.show {
  opacity: 1;
  animation: slideInFromRight 0.3s ease-out forwards;
}

.notification.hide {
  opacity: 0;
  animation: slideOutToRight 0.3s ease-in forwards;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(120%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(120%);
    opacity: 0;
  }
}

.notification-icon-wrapper {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.notification-icon {
  width: 24px;
  height: 24px;
}

.notification-content {
  flex: 1;
  margin-right: 12px;
}

.notification-message {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.notification-close {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.notification-close:hover {
  opacity: 1;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(223, 223, 223, 0.1);
}

.notification-progress-bar {
  height: 100%;
  background: currentColor;
  opacity: 0.6;
  transition: width 0.1s linear;
}

.notification.success {
  background: #ffffff;
  color: #166534;
  border-left: 4px solid #22c55e;
}

.notification.error {
  /* background: #fef2f2; */
  color: #e72828;
  border-left: 4px solid #ef4444;
}

.notification-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.notification-message {
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-color);
  font-weight: 500;
  letter-spacing: -0.02em;
}

/* Animation pour le hover */
.notification {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.notification:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Types styles */
.notification.success .notification-progress-bar {
  background-color: var(--toastify-color-success);
}

.notification.error .notification-progress-bar {
  background-color: var(--toastify-color-error);
}

.notification.warning .notification-progress-bar {
  background-color: var(--toastify-color-warning);
}

.notification.info .notification-progress-bar {
  background-color: var(--toastify-color-info);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(100%) scale(0.9); }
  to { opacity: 1; transform: translateX(0) scale(1); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateX(0) scale(1); }
  to { opacity: 0; transform: translateX(100%) scale(0.9); }
}
