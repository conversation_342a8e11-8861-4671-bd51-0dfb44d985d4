import React from 'react';
import ModalPortal from '../ModalPortal';

interface ConfirmAddressModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  previousAddress: string;
  tempAddress: string;
  isConfirmationChecked: boolean;
  setIsConfirmationChecked: (checked: boolean) => void;
}

const ConfirmAddressModal: React.FC<ConfirmAddressModalProps> = ({
  open,
  onClose,
  onConfirm,
  previousAddress,
  tempAddress,
  isConfirmationChecked,
  setIsConfirmationChecked,
}) => {
  return (
    <ModalPortal isOpen={open} onBackdropClick={onClose}>
      <div className="fixed inset-0 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
          <h3 className="text-lg font-semibold mb-4">Confirmer la modification de l'adresse</h3>
          <div className="space-y-4 overflow-y-auto flex-grow pr-2">
            <div>
              <p className="text-sm text-gray-500 mb-2">Ancienne adresse :</p>
              <div className="prose prose-sm max-w-none text-gray-700 bg-gray-50 p-3 rounded-lg line-through">
                {previousAddress || 'Aucune adresse définie'}
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-2">Nouvelle adresse :</p>
              <div className="prose prose-sm max-w-none text-gray-700 bg-[#FFF8F3] p-3 rounded-lg">
                {tempAddress}
              </div>
            </div>
            <div className="mt-4 flex items-start bg-red-100 p-3 rounded-lg">
              <input
                type="checkbox"
                checked={isConfirmationChecked}
                onChange={e => setIsConfirmationChecked(e.target.checked)}
                className="mr-2 h-5 w-5 rounded border-gray-300 text-[#FF7A35] focus:ring-[#FF965E] checked:bg-[#FF7A35] checked:border-transparent shadow-md cursor-pointer"
              />
              <div>
                <p className="text-sm text-gray-700 font-medium">
                  Confirmer la modification
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  En cochant cette case, vous confirmez la modification de votre nom et prénom.
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={onConfirm}
              className="px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
            >
              Confirmer
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmAddressModal; 