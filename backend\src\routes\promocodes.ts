import { Router, Request, Response, NextFunction } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';
import promocodesController from '../controllers/promocodes';
import rateLimit from 'express-rate-limit';
import logger from '../utils/logger';
import { supabase } from '../config/supabase';
import { decryptUserDataAsync } from '../utils/encryption';

const router = Router();

// Configuration du rate limiter
const promocodesLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 requêtes par IP
  message: { 
    success: false,
    message: 'Trop de requêtes pour les codes promo, veuillez réessayer dans 1 minute',
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter
router.use(promocodesLimiter);

// Middleware d'authentification pour toutes les routes
router.use(authMiddleware.authenticateToken);

// Middleware pour vérifier si l'utilisateur est administrateur
const isAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si l'utilisateur est administrateur
    const { data: user, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (error || !user) {
      logger.error('Erreur lors de la vérification des droits administrateur:', error);
      res.status(403).json({
        success: false,
        message: 'Accès refusé',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les données utilisateur si nécessaire
    const decryptedUser = await decryptUserDataAsync(user);

    if (decryptedUser.role !== 'jobpadm') {
      res.status(403).json({
        success: false,
        message: 'Accès refusé - Droits administrateur requis',
        toastType: 'error'
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('Erreur lors de la vérification des droits administrateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des droits administrateur',
      toastType: 'error'
    });
    return;
  }
};

// Routes administrateur (CRUD)
router.get('/admin', isAdmin, async (req, res) => {
  await promocodesController.getAllPromoCodes(req, res);
});

router.post('/admin', isAdmin, async (req, res) => {
  await promocodesController.createPromoCode(req, res);
});

router.put('/admin/:id', isAdmin, async (req, res) => {
  await promocodesController.updatePromoCode(req, res);
});

router.delete('/admin/:id', isAdmin, async (req, res) => {
  await promocodesController.deletePromoCode(req, res);
});

// Routes publiques (validation de code promo)
router.post('/validate', async (req, res) => {
  await promocodesController.validatePromoCode(req, res);
});

// Route utilisateur : historique de ses codes promo utilisés/attribués
router.get('/me', async (req, res) => {
  await promocodesController.getUserPromoCodes(req, res);
});

export default router; 