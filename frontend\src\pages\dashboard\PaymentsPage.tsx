import { useState, useEffect, useCallback } from 'react';
import { styled } from '@mui/material/styles';
import { Typography, Button, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, 
  Checkbox, FormControlLabel, TextField, MenuItem, IconButton, Alert, Menu, TablePagination } from '@mui/material';
import { api } from '../../services/api';
import { getCommonHeaders } from '../../utils/headers';
import { error, notify } from '../../components/Notification';
import DOMPurify from 'dompurify';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import ImportExportIcon from '@mui/icons-material/ImportExport';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ModalPortal from '../../components/ModalPortal';
import { LineChart, Line, ResponsiveContainer, Tooltip, CartesianGrid, XAxis, YAxis, LabelList } from 'recharts';

const PageTitle = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748', 
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },  
}));

// Types de paiement disponibles
const typesPayment = ['jobi', 'carte', 'virement', 'especes', 'cheque', 'autre'];

// Catégories d'opérations disponibles
const categoriesOperation = ['mission', 'transfert', 'abonnement', 'rechargement', 'remboursement', 'facture', 'autre'];

// Interface pour les transactions
interface Transaction {
  id: string;
  date: string;
  type: string;
  montant: number;
  client?: string;
  mission?: string;
  description?: string;
  statut: 'complete' | 'en_attente' | 'refuse';
  methode?: string;
  reference?: string;
  categorie?: string;
  display_date?: string;
}

// Interface pour les transactions à importer
interface TransactionToImport {
  id: string;
  date: string;
  type: string;
  montant: number;
  client?: string;
  mission?: string;
  description?: string;
  statut: 'complete' | 'en_attente' | 'refuse';
  methode?: string;
  reference?: string;
  categorie: string;
  selected: boolean;
  display_date?: string;
  message?: string;
}

interface TransactionFormData {
  date: string;
  type: string;
  montant: string;
  client: string;
  mission: string;
  description: string;
  statut: 'complete' | 'en_attente' | 'refuse';
  methode: string;
  reference: string;
  categorie: string;
}

// Interface pour les filtres
interface Filters {
  dateDebut: string;
  dateFin: string;
  type: string[];
  categorie: string[];
  methode: string[];
  statut: string[];
  montantMin: string;
  montantMax: string;
  searchTerm: string;
}

// Interface pour la pagination
interface PaginationState {
  page: number;
  rowsPerPage: number;
  total: number;
}

// Ajout des types pour le tri
type SortField = 'date' | 'type' | 'categorie' | 'methode' | 'description' | 'montant' | 'statut';
type SortOrder = 'asc' | 'desc';

interface SortState {
  field: SortField;
  order: SortOrder;
}

// Interface pour les données d'importation
interface ImportData {
  missions: TransactionToImport[];
  jobi: TransactionToImport[];
  factures: TransactionToImport[];
  autres: TransactionToImport[];
  [key: string]: TransactionToImport[]; // Index signature pour les clés dynamiques
}

// Types pour la pagination
type ImportPaginationState = {
  page: number;
  total: number;
  loading: boolean;
};

type ImportPagination = {
  [key in keyof ImportData]: ImportPaginationState;
};

type HasMoreState = {
  [key in keyof ImportData]: boolean;
};

// Fonction pour formater la date en format français
const formatDateToFrench = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  return new Intl.DateTimeFormat('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// Fonction pour formater la date au format YYYY-MM-DD pour l'input
const formatDateForInput = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  return date.toISOString().split('T')[0];
};

// Fonction pour formater le nom du client (ex: "Jean Dupont" -> "Jean D.")
const formatClientName = (fullName: string) => {
  if (!fullName) return '';
  const parts = fullName.trim().split(' ');
  if (parts.length === 1) return parts[0];
  const firstName = parts[0];
  const lastInitial = parts[parts.length - 1][0];
  return `${firstName} ${lastInitial}.`;
};

// Fonction pour trier les mois chronologiquement
const sortMonths = (a: { mois: string }, b: { mois: string }) => {
  // Extraire le mois et l'année de la chaîne (format: "mois année")
  const [moisA, anneeA] = a.mois.split(' ');
  const [moisB, anneeB] = b.mois.split(' ');
  
  // Comparer d'abord les années
  if (anneeA !== anneeB) {
    return parseInt(anneeA) - parseInt(anneeB);
  }
  
  // Si les années sont identiques, comparer les mois
  const moisOrdre = [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ];
  return moisOrdre.indexOf(moisA.toLowerCase()) - moisOrdre.indexOf(moisB.toLowerCase());
};

export default function PaymentsPage() {
  // États pour les transactions
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [solde, setSolde] = useState<number>(0);
  const [enAttente, setEnAttente] = useState<number>(0);
  const [revenus, setRevenus] = useState<{ mois: string, montant: number }[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<number>(6);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [userSubscription, setUserSubscription] = useState<'gratuit' | 'premium'>('gratuit');
  const [transactionLimit, setTransactionLimit] = useState<number>(15);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [activeImportTab, setActiveImportTab] = useState<keyof ImportData>('missions');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [currentEditTransaction, setCurrentEditTransaction] = useState<Transaction | null>(null);
  const [transactionsToImport, setTransactionsToImport] = useState<ImportData>({
    missions: [],
    jobi: [],
    factures: [],
    autres: []
  });
  const [importPagination, setImportPagination] = useState<ImportPagination>({
    missions: { page: 0, total: 0, loading: false },
    jobi: { page: 0, total: 0, loading: false },
    factures: { page: 0, total: 0, loading: false },
    autres: { page: 0, total: 0, loading: false }
  });
  const [hasMore, setHasMore] = useState<HasMoreState>({
    missions: true,
    jobi: true,
    factures: true,
    autres: true
  });
  const [batchDeleteConfirmOpen, setBatchDeleteConfirmOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isDataFetching, setIsDataFetching] = useState(false);
  const [formData, setFormData] = useState<TransactionFormData>({
    date: new Date().toISOString().split('T')[0],
    type: 'paiement',
    montant: '',
    client: '',
    mission: '',
    description: '',
    statut: 'en_attente',
    methode: 'jobi',
    reference: '',
    categorie: 'mission'
  });
  
  // Ajout de l'état de tri
  const [sort, setSort] = useState<SortState>({
    field: 'date',
    order: 'desc'
  });
  
  // États pour l'interface utilisateur
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [dateRange, setDateRange] = useState('mois');
  
  // État pour la gestion des filtres
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // États pour les filtres et la pagination
  const [filterModalOpen, setFilterModalOpen] = useState<boolean>(false);
  const [filters, setFilters] = useState<Filters>({
    dateDebut: '',
    dateFin: '',
    type: [],
    categorie: [],
    methode: [],
    statut: [],
    montantMin: '',
    montantMax: '',
    searchTerm: ''
  });
  // État temporaire pour les filtres dans le modal
  const [tempFilters, setTempFilters] = useState<Filters>({
    dateDebut: '',
    dateFin: '',
    type: [],
    categorie: [],
    methode: [],
    statut: [],
    montantMin: '',
    montantMax: '',
    searchTerm: ''
  });
  const [pagination, setPagination] = useState<PaginationState>({
    page: 0,
    rowsPerPage: 10,
    total: 0
  });

  // Effet pour initialiser les filtres temporaires lors de l'ouverture du modal
  useEffect(() => {
    if (filterModalOpen) {
      setTempFilters({ ...filters });
    }
  }, [filterModalOpen, filters]);

  // Effet pour mettre à jour les filtres quand searchTerm change
  useEffect(() => {
    setFilters(prev => ({ ...prev, searchTerm }));
  }, [searchTerm]);

  // Fonction combinée pour charger toutes les données
  const fetchAllData = useCallback(async (forceRefresh = false) => {
    // Si une requête est déjà en cours et qu'on ne force pas le rafraîchissement, on ignore
    if (isDataFetching && !forceRefresh) return;
    
    try {
      setIsDataFetching(true);
      setIsLoading(true);
      
      const headers = await getCommonHeaders();
      
      // Exécution en parallèle des deux requêtes
      const [transactionsResponse, revenusResponse] = await Promise.all([
        api.get('/api/payments', {
          headers,
          withCredentials: true,
          params: {
            page: pagination.page,
            limit: pagination.rowsPerPage,
            dateDebut: filters.dateDebut || undefined,
            dateFin: filters.dateFin || undefined,
            type: filters.type.length > 0 ? filters.type.join(',') : undefined,
            categorie: filters.categorie.length > 0 ? filters.categorie.join(',') : undefined,
            methode: filters.methode.length > 0 ? filters.methode.join(',') : undefined,
            statut: filters.statut.length > 0 ? filters.statut.join(',') : undefined,
            montantMin: filters.montantMin || undefined,
            montantMax: filters.montantMax || undefined,
            searchTerm: filters.searchTerm || undefined
          }
        }),
        api.get('/api/payments/revenus', {
          headers,
          withCredentials: true,
          params: {
            period: dateRange === 'annee' ? '12' : selectedPeriod.toString()
          }
        })
      ]);
      
      // Traitement des réponses
      if (transactionsResponse.data.success) {
        setTransactions(transactionsResponse.data.transactions || []);
        setPagination(prev => ({ ...prev, total: transactionsResponse.data.total || 0 }));
      }
      
      if (revenusResponse.data.success) {
        setSolde(revenusResponse.data.solde || 0);
        setEnAttente(revenusResponse.data.enAttente || 0);
        setRevenus(revenusResponse.data.revenus || []);
      }
      
    } catch (error) {
      console.error("Erreur lors du chargement des données:", error);
      notify("Impossible de charger certaines données.", "error");
    } finally {
      setIsLoading(false);
      setIsDataFetching(false);
    }
  }, [pagination.page, pagination.rowsPerPage, filters, dateRange, selectedPeriod]);

  // Effet pour charger les données
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  const handlePeriodSelect = (period: number) => {
    setSelectedPeriod(period);
    setDateRange('mois');
    handlePeriodClose();
  };

  const handlePeriodClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePeriodClose = () => {
    setAnchorEl(null);
  };

  // Fonction pour charger plus de transactions
  const loadMoreTransactions = useCallback(async (type: keyof ImportData) => {
    if (importPagination[type].loading || !hasMore[type]) {
      return;
    }

    try {
      setImportPagination(prev => ({
        ...prev,
        [type]: { ...prev[type], loading: true }
      }));

      const headers = await getCommonHeaders();
      const response = await api.get('/api/payments/import', {
        headers,
        withCredentials: true,
        params: {
          offset: transactionsToImport[type].length,
          limit: 20,
          type: type
        }
      });

      if (response.data.success) {
        const newTransactions = response.data.transactions || [];
        const total = response.data.total || 0;

        // Si aucune nouvelle transaction n'est retournée, on arrête le chargement
        if (newTransactions.length === 0) {
          setHasMore(prev => ({
            ...prev,
            [type]: false
          }));
          setImportPagination(prev => ({
            ...prev,
            [type]: {
              ...prev[type],
              loading: false,
              total
            }
          }));
          return;
        }

        // Vérifier les doublons avant d'ajouter les nouvelles transactions
        const existingIds = new Set(transactionsToImport[type].map((t: TransactionToImport) => t.id));
        const uniqueNewTransactions = newTransactions.filter((t: TransactionToImport) => !existingIds.has(t.id));

        // Si après filtrage des doublons il n'y a plus de transactions, on arrête aussi
        if (uniqueNewTransactions.length === 0) {
          setHasMore(prev => ({
            ...prev,
            [type]: false
          }));
          setImportPagination(prev => ({
            ...prev,
            [type]: {
              ...prev[type],
              loading: false,
              total
            }
          }));
          return;
        }

        setTransactionsToImport(prev => ({
          ...prev,
          [type]: [...prev[type], ...uniqueNewTransactions]
        }));

        setImportPagination(prev => ({
          ...prev,
          [type]: {
            ...prev[type],
            loading: false,
            total
          }
        }));

        // Mettre à jour hasMore en fonction du nombre total de transactions
        setHasMore(prev => ({
          ...prev,
          [type]: (transactionsToImport[type].length + uniqueNewTransactions.length) < total
        }));
      }
    } catch (error) {
      console.error("Erreur lors du chargement des transactions:", error);
      // En cas d'erreur, on arrête le chargement pour ce type
      setHasMore(prev => ({
        ...prev,
        [type]: false
      }));
      notify("Erreur lors du chargement des transactions.", "error");
    } finally {
      setImportPagination(prev => ({
        ...prev,
        [type]: { ...prev[type], loading: false }
      }));
    }
  }, [importPagination, hasMore, transactionsToImport, notify]);

  // Fonction pour gérer le scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>, type: keyof ImportData) => {
    const target = e.target as HTMLDivElement;
    const bottom = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) < 1;
    
    if (bottom && !importPagination[type].loading && hasMore[type]) {
      loadMoreTransactions(type);
    }
  }, [loadMoreTransactions, importPagination, hasMore]);

  // Fonction pour importer les transactions
  const handleImportClick = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadingMessage('Analyse des transactions à importer...');
      setImportModalOpen(true);
      setActiveImportTab('missions'); // Définir l'onglet par défaut sur missions
      
      const headers = await getCommonHeaders();
      const response = await api.get('/api/payments/import', {
        headers,
        withCredentials: true,
        params: {
          page: 0,
          limit: 20,
          type: 'missions'
        }
      });
      
      if (response.data.success) {
        setTransactionsToImport(prev => ({
          ...prev,
          missions: response.data.transactions
        }));
        setImportPagination(prev => ({
          ...prev,
          missions: {
            page: 1,
            total: response.data.total,
            loading: false
          }
        }));
        setHasMore(prev => ({
          ...prev,
          missions: response.data.transactions.length < response.data.total
        }));
      } else {
        notify(response.data.message || "Erreur lors de l'importation des transactions.", "error");
      }
      
      setIsLoading(false);
      
    } catch (error) {
      console.error("Erreur lors de l'importation des transactions:", error);
      notify("Impossible d'analyser les transactions à importer.", "error");
      setIsLoading(false);
      setImportModalOpen(false);
    }
  }, []);

  // Effet pour charger les transactions lors du changement d'onglet
  useEffect(() => {
    if (importModalOpen && transactionsToImport[activeImportTab].length === 0) {
      loadMoreTransactions(activeImportTab);
    }
  }, [importModalOpen, activeImportTab, loadMoreTransactions]);

  const getStatusText = (statut: Transaction['statut']): string => {
    switch (statut) {
      case 'complete':
        return 'Complété';
      case 'en_attente':
        return 'En attente';
      case 'refuse':
        return 'Refusé';
      default:
        return statut;
    }
  };

  // Fonction pour obtenir la couleur de la bordure selon le type de transaction
  const getBorderColor = (type: string): string => {
    switch (type) {
      case 'paiement':
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-green-500';
      case 'remboursement':
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-blue-500';
      case 'commission':
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-purple-500';
      case 'abonnement':
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-yellow-500';
      case 'transfert':
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-orange-500';
      default:
        return 'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:bg-gray-300';
    }
  };

  // Gestion de la sélection des transactions à importer
  const handleSelectTransaction = useCallback((transactionId: string, method: keyof ImportData, category: string) => {
    setTransactionsToImport(prev => {
      const updated = { ...prev };
      const transactions = updated[method];
      const index = transactions.findIndex((t: TransactionToImport) => t.id === transactionId);
      
      if (index !== -1) {
        transactions[index] = { ...transactions[index], selected: !transactions[index].selected };
      }
      
      return updated;
    });
  }, []);

  // Sélectionner toutes les transactions d'une méthode de paiement
  const handleSelectAllByMethod = useCallback((method: keyof ImportData, select: boolean) => {
    setTransactionsToImport(prev => {
      const updated = { ...prev };
      if (updated[method]) {
        updated[method] = updated[method].map((t: TransactionToImport) => ({
          ...t,
          selected: select
        }));
      }
      return updated;
    });
  }, []);

  // Valider l'importation des transactions sélectionnées
  const handleImportSelectedTransactions = useCallback(async () => {
    try {
      setIsImporting(true);
      
      // Collecter toutes les transactions sélectionnées par catégorie
      let selectedMissions = transactionsToImport.missions.filter((t: TransactionToImport) => t.selected);
      let selectedJobiTransactions = transactionsToImport.jobi.filter((t: TransactionToImport) => t.selected);
      let selectedOtherTransactions = transactionsToImport.autres.filter((t: TransactionToImport) => t.selected);
      let selectedFactureTransactions = transactionsToImport.factures.filter((t: TransactionToImport) => t.selected);
      
      // Nettoyer les données avec DOMPurify
      const sanitizeTransaction = (transaction: TransactionToImport): TransactionToImport => ({
        ...transaction,
        client: transaction.client ? DOMPurify.sanitize(transaction.client) : transaction.client,
        mission: transaction.mission ? DOMPurify.sanitize(transaction.mission) : transaction.mission,
        description: transaction.description ? DOMPurify.sanitize(transaction.description) : transaction.description
      });
      
      selectedMissions = selectedMissions.map(sanitizeTransaction);
      selectedJobiTransactions = selectedJobiTransactions.map(sanitizeTransaction);
      selectedOtherTransactions = selectedOtherTransactions.map(sanitizeTransaction);
      selectedFactureTransactions = selectedFactureTransactions.map(sanitizeTransaction);
      
      const totalSelected = selectedMissions.length + selectedJobiTransactions.length + selectedOtherTransactions.length + selectedFactureTransactions.length;
      
      if (totalSelected === 0) {
        notify("Aucune transaction sélectionnée.", "warning");
        setIsImporting(false);
        return;
      }
      
      // Vérifier la limite de 100 transactions par lot
      if (totalSelected > 100) {
        notify("Vous ne pouvez importer que 100 transactions maximum par lot. Veuillez réduire votre sélection.", "warning");
        setIsImporting(false);
        return;
      }
      
      // Appel API pour enregistrer les transactions
      const headers = await getCommonHeaders();
      const response = await api.post(
        '/api/payments/import',
        {
          missions: selectedMissions,
          jobiTransactions: selectedJobiTransactions,
          otherTransactions: selectedOtherTransactions,
          factureTransactions: selectedFactureTransactions
        },
        { 
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true 
        }
      );
      
      if (response.data.success) {
        // Mettre à jour la liste des transactions en retirant uniquement celles qui ont été importées
        setTransactionsToImport(prev => {
          const updated = {
            missions: prev.missions.filter(t => !selectedMissions.some(selected => selected.id === t.id)),
            jobi: prev.jobi.filter(t => !selectedJobiTransactions.some(selected => selected.id === t.id)),
            autres: prev.autres.filter(t => !selectedOtherTransactions.some(selected => selected.id === t.id)),
            factures: prev.factures.filter(t => !selectedFactureTransactions.some(selected => selected.id === t.id))
          };
          
          return updated;
        });

        // Rafraîchir les données principales
        await fetchAllData(true);

        setIsImporting(false);
        setImportModalOpen(false);
        notify(`${totalSelected} transaction(s) importée(s) avec succès.`, "success");

        // Si la liste actuelle est vide après l'importation, recharger plus de transactions
        // Réinitialiser les états de pagination pour forcer un rechargement
        setImportPagination(prev => ({
          missions: { ...prev.missions, page: 0, total: 0 },
          jobi: { ...prev.jobi, page: 0, total: 0 },
          factures: { ...prev.factures, page: 0, total: 0 },
          autres: { ...prev.autres, page: 0, total: 0 }
        }));

        // Réinitialiser hasMore pour permettre le rechargement complet
        setHasMore({
          missions: true,
          jobi: true,
          factures: true,
          autres: true
        });

        // Réinitialiser les transactions à importer
        setTransactionsToImport({
          missions: [],
          jobi: [],
          factures: [],
          autres: []
        });

        // Si le modal d'import est ouvert, recharger les transactions pour l'onglet actif
        if (importModalOpen) {
          loadMoreTransactions(activeImportTab);
        }
      } else {
        throw new Error(response.data.message || "Erreur lors de la suppression en masse");
      }
      
    } catch (error: any) {
      console.error("Erreur lors de l'importation des transactions en masse:", error);
      notify(error.response?.data?.message || "Erreur lors de l'importation des transactions en masse.", "error");
      setIsLoading(false);
      setIsImporting(false);
    }
  }, [transactionsToImport, fetchAllData, notify, importModalOpen, activeImportTab, loadMoreTransactions]);

  const isSelected = (transactionId: string) => selectedIds.includes(transactionId);

  // Sélectionner ou désélectionner toutes les transactions de la page actuelle
  const handleSelectAllClick = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedIds(transactions.map(transaction => transaction.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectClick = (e: React.ChangeEvent<HTMLInputElement>, transactionId: string) => {
    e.stopPropagation();
    if (isSelected(transactionId)) {
      setSelectedIds(selectedIds.filter(id => id !== transactionId));
    } else {
      setSelectedIds([...selectedIds, transactionId]);
    }
  };

  // Fonction de tri des transactions
  const handleSort = (field: SortField) => {
    setSort(prev => ({
      field,
      order: prev.field === field && prev.order === 'asc' ? 'desc' : 'asc'
    }));

    const sortedTransactions = [...transactions].sort((a, b) => {
      let comparison = 0;
      
      switch (field) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'categorie':
          comparison = (a.categorie || '').localeCompare(b.categorie || '');
          break;
        case 'methode':
          comparison = (a.methode || '').localeCompare(b.methode || '');
          break;
        case 'description':
          comparison = (a.description || '').localeCompare(b.description || '');
          break;
        case 'montant':
          comparison = a.montant - b.montant;
          break;
        case 'statut':
          comparison = a.statut.localeCompare(b.statut);
          break;
        default:
          comparison = 0;
      }
      
      return sort.order === 'asc' ? comparison : -comparison;
    });

    setTransactions(sortedTransactions);
  };

  // Fonction pour exporter les transactions en PDF
  const handleExportPDF = async () => {
    try {
      setIsLoading(true);
      setLoadingMessage('Export des transactions en cours...');

      const response = await api.get('/api/payments/export-pdf', {
        params: {
          ...filters,
          period: selectedPeriod
        },
        responseType: 'blob',
        headers: {
          Accept: 'application/pdf'
        }
      });

      // Créer un URL pour le blob
      const url = window.URL.createObjectURL(new Blob([response.data]));
      
      // Créer un lien temporaire
      const link = document.createElement('a');
      link.href = url;
      
      // Générer un nom de fichier avec la date
      const today = new Date().toISOString().split('T')[0];
      const fileName = `jobpartiel-fr-transactions-${filters.dateDebut || today}-${filters.dateFin || today}.pdf`;
      link.setAttribute('download', fileName);
      
      // Ajouter le lien au document
      document.body.appendChild(link);
      
      // Cliquer sur le lien
      link.click();
      
      // Nettoyer
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      setIsLoading(false);
      setLoadingMessage('');
      notify('Export PDF réussi', 'success');
      
    } catch (error) {
      console.error('Erreur lors de l\'export PDF:', error);
      setIsLoading(false);
      setLoadingMessage('');
      notify('Erreur lors de l\'export PDF', 'error');
    }
  };

  // Supprimer une transaction
  const handleDeleteTransaction = useCallback(async () => {
    if (!selectedTransaction) return;
    
    try {
      setIsLoading(true);
      
      const headers = await getCommonHeaders();
      const response = await api.delete(`/api/payments/${selectedTransaction.id}`, {
        headers,
        withCredentials: true
      });
      
      if (response.data.success) {
        setIsLoading(false);
        setDeleteConfirmOpen(false);
        setSelectedTransaction(null);
        notify("Transaction supprimée avec succès.", "success");
        
        // Rafraîchir les données principales
        await fetchAllData(true);

        // Réinitialiser les états de pagination pour forcer un rechargement
        setImportPagination(prev => ({
          missions: { ...prev.missions, page: 0, total: 0 },
          jobi: { ...prev.jobi, page: 0, total: 0 },
          factures: { ...prev.factures, page: 0, total: 0 },
          autres: { ...prev.autres, page: 0, total: 0 }
        }));

        // Réinitialiser hasMore pour permettre le rechargement complet
        setHasMore({
          missions: true,
          jobi: true,
          factures: true,
          autres: true
        });

        // Réinitialiser les transactions à importer
        setTransactionsToImport({
          missions: [],
          jobi: [],
          factures: [],
          autres: []
        });

        // Si le modal d'import est ouvert, recharger les transactions pour l'onglet actif
        if (importModalOpen) {
          loadMoreTransactions(activeImportTab);
        }
      } else {
        throw new Error(response.data.message || "Erreur lors de la suppression");
      }
      
    } catch (error) {
      console.error("Erreur lors de la suppression de la transaction:", error);
      notify("Erreur lors de la suppression de la transaction.", "error");
      setIsLoading(false);
    }
  }, [selectedTransaction, fetchAllData, notify, importModalOpen, activeImportTab, loadMoreTransactions]);

  // Gérer les changements dans le formulaire d'édition/création
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    
    // Nettoyer les entrées pour les champs textuels qui pourraient contenir du HTML
    if (name === 'client' || name === 'mission' || name === 'description') {
      setFormData(prev => ({ ...prev, [name as string]: DOMPurify.sanitize(value as string) }));
    } else {
      setFormData(prev => ({ ...prev, [name as string]: value }));
    }
  };

  // Ouvrir le dialogue d'édition
  const handleEditClick = (transaction: Transaction) => {
    setCurrentEditTransaction(transaction);
    setFormData({
      date: formatDateForInput(transaction.date),
      type: transaction.type,
      montant: transaction.montant.toString(),
      client: transaction.client || '',
      mission: transaction.mission || '',
      description: transaction.description || '',
      statut: transaction.statut,
      methode: transaction.methode || 'jobi',
      reference: transaction.reference || '',
      categorie: transaction.categorie || 'mission'
    });
    setEditModalOpen(true);
  };

  // Fonction pour vérifier si l'utilisateur peut créer une nouvelle transaction
  const canCreateTransaction = useCallback(async () => {
    try {
      const response = await api.get('/api/payments/check-transactions-limit', {
        headers: await getCommonHeaders(),
        withCredentials: true
      });
      
      if (response.data.success) {
        const { plan, transactionLimit, currentCount, canCreate } = response.data.data;
        setUserSubscription(plan);
        setTransactionLimit(transactionLimit);
        return canCreate;
      }
      
      return false;
    } catch (err) {
      console.error("Erreur lors de la vérification du plan d'abonnement:", err);
      notify("Erreur lors de la vérification du plan d'abonnement.", "error");
      return false;
    }
  }, []);

  // Ouvrir le dialogue de création
  const handleCreateClick = async () => {
    const canCreate = await canCreateTransaction();
    
    if (!canCreate) {
      notify(`Vous avez atteint la limite de ${transactionLimit} transactions pour votre plan ${userSubscription}. Passez au plan premium pour avoir plus de transactions.`, "error");
      return;
    }
    
    setFormData({
      date: formatDateForInput(new Date().toISOString()),
      type: 'paiement',
      montant: '',
      client: '',
      mission: '',
      description: '',
      statut: 'en_attente',
      methode: 'jobi',
      reference: '',
      categorie: 'mission'
    });
    setCreateModalOpen(true);
  };

  // Afficher le nombre total de transactions sélectionnées pour l'import
  const getSelectedCount = () => {
    return [
      ...transactionsToImport.missions,
      ...transactionsToImport.jobi,
      ...transactionsToImport.factures,
      ...transactionsToImport.autres
    ].filter((t: TransactionToImport) => t.selected).length;
  };

  // Supprimer en masse les transactions sélectionnées
  const handleBatchDeleteTransactions = useCallback(async () => {
    if (selectedIds.length === 0) return;
    
    try {
      setIsLoading(true);
      
      const headers = await getCommonHeaders();
      const response = await api.post('/api/payments/delete-batch', {
        ids: selectedIds
      }, {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      });
      
      if (response.data.success) {
        setIsLoading(false);
        setBatchDeleteConfirmOpen(false);
        setSelectedIds([]);
        notify(`${selectedIds.length} transaction(s) supprimée(s) avec succès.`, "success");
        
        // Rafraîchir les données principales
        await fetchAllData(true);

        // Réinitialiser les états de pagination pour forcer un rechargement
        setImportPagination(prev => ({
          missions: { ...prev.missions, page: 0, total: 0 },
          jobi: { ...prev.jobi, page: 0, total: 0 },
          factures: { ...prev.factures, page: 0, total: 0 },
          autres: { ...prev.autres, page: 0, total: 0 }
        }));

        // Réinitialiser hasMore pour permettre le rechargement complet
        setHasMore({
          missions: true,
          jobi: true,
          factures: true,
          autres: true
        });

        // Réinitialiser les transactions à importer
        setTransactionsToImport({
          missions: [],
          jobi: [],
          factures: [],
          autres: []
        });

        // Si le modal d'import est ouvert, recharger les transactions pour l'onglet actif
        if (importModalOpen) {
          loadMoreTransactions(activeImportTab);
        }
      } else {
        throw new Error(response.data.message || "Erreur lors de la suppression en masse");
      }
      
    } catch (error) {
      console.error("Erreur lors de la suppression en masse des transactions:", error);
      notify("Erreur lors de la suppression en masse des transactions.", "error");
      setIsLoading(false);
    }
  }, [selectedIds, fetchAllData, notify, importModalOpen, activeImportTab, loadMoreTransactions]);

  // Soumettre le formulaire d'édition/création
  const handleFormSubmit = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Préparer les données pour la soumission
      const dataToSubmit = {
        date: formData.date,
        type: formData.type,
        montant: parseFloat(formData.montant),
        client: DOMPurify.sanitize(formData.client),
        mission: DOMPurify.sanitize(formData.mission),
        description: DOMPurify.sanitize(formData.description),
        statut: formData.statut,
        methode: formData.methode,
        reference: formData.reference,
        categorie: formData.categorie
      };
      
      const headers = await getCommonHeaders();
      
      if (currentEditTransaction) {
        // Mode édition
        const response = await api.put(
          `/api/payments/${currentEditTransaction.id}`,
          dataToSubmit,
          { 
            headers: {
              ...headers,
              'Content-Type': 'application/json'
            },
            withCredentials: true 
          }
        );
        
        if (response.data.success) {
          setIsLoading(false);
          setEditModalOpen(false);
          setCurrentEditTransaction(null);
          notify("Transaction mise à jour avec succès.", "success");
          // Rafraîchir les données immédiatement
          await fetchAllData(true);
        } else {
          throw new Error(response.data.message || "Erreur lors de la mise à jour");
        }
      } else {
        // Mode création
        const response = await api.post(
          '/api/payments',
          dataToSubmit,
          { 
            headers: {
              ...headers,
              'Content-Type': 'application/json'
            },
            withCredentials: true 
          }
        );
        
        if (response.data.success) {
          setIsLoading(false);
          setCreateModalOpen(false);
          notify("Transaction créée avec succès.", "success");
          // Rafraîchir les données immédiatement
          await fetchAllData(true);
        } else {
          throw new Error(response.data.message || "Erreur lors de la création");
        }
      }
      
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de la transaction:", error);
      notify("Erreur lors de la sauvegarde de la transaction.", "error");
      setIsLoading(false);
    }
  }, [formData, currentEditTransaction, fetchAllData, notify]);

  return (
    <div className="space-y-6 px-4 sm:px-6 md:px-0 pb-6 sm:pb-8">
      {/* En-tête avec titre et boutons d'action */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <PageTitle variant="h1">
          Paiements & Finances
        </PageTitle>
      </div>

      {/* Chargement */}
      {isLoading && (
        <div className="flex flex-col items-center justify-center py-12">
          <CircularProgress sx={{ color: '#FF7A35' }} />
          <p className="mt-4 text-gray-600">{loadingMessage}</p>
        </div>
      )}

      {!isLoading && (
        <>
          {/* Graphique des revenus */}
          <div className="bg-white rounded-lg shadow-lg p-4 sm:p-8 transform transition-all duration-300 hover:shadow-xl">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-8">
              <div className="space-y-1.5">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-800">Évolution des revenus</h2>
                <p className="text-sm text-gray-500">Aperçu de vos revenus sur la période</p>
              </div>
              <div className="flex flex-wrap bg-gray-50/80 p-1 rounded-xl border border-gray-100 shadow-sm">
                <div className="relative">
                  <button
                    onClick={handlePeriodClick}
                    className={`px-3 sm:px-5 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      dateRange === 'mois'
                        ? 'bg-gradient-to-r from-[#FF7A35] to-[#FF9F6B] text-white shadow-md transform scale-102 ring-2 ring-orange-200'
                        : 'bg-transparent text-gray-600 hover:bg-white hover:shadow-sm'
                    }`}
                  >
                    {selectedPeriod} {selectedPeriod === 1 ? 'mois' : 'mois'}
                  </button>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handlePeriodClose}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'left',
                    }}
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'left',
                    }}
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((period) => (
                      <MenuItem
                        key={period}
                        onClick={() => handlePeriodSelect(period)}
                        selected={selectedPeriod === period && dateRange === 'mois'}
                      >
                        {period} {period === 1 ? 'mois' : 'mois'}
                      </MenuItem>
                    ))}
                  </Menu>
                </div>
                <button
                  onClick={() => {
                    setDateRange('annee');
                    setSelectedPeriod(6); // Réinitialiser la période sélectionnée
                  }}
                  className={`px-3 sm:px-5 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    dateRange === 'annee'
                      ? 'bg-gradient-to-r from-[#FF7A35] to-[#FF9F6B] text-white shadow-md transform scale-102 ring-2 ring-orange-200'
                      : 'bg-transparent text-gray-600 hover:bg-white hover:shadow-sm'
                  }`}
                >
                  Année
                </button>
              </div>
            </div>

            {/* Section des statistiques */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gradient-to-br from-orange-50 to-white p-4 rounded-xl border border-orange-100">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-500 truncate">Solde Jobi actuel</span>
                  <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-lg bg-orange-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#FF7A35]" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                      <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                    </svg>
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <p className="text-xl sm:text-2xl font-bold text-gray-800">{solde.toLocaleString('fr-FR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}</p>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="h-5 w-5 text-[#FF6B2C]">
                    <circle cx="12" cy="12" r="9" strokeWidth="2"></circle>
                    <path d="M14.8 8.5a3.5 3.5 0 00-5.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M9.2 15.5a3.5 3.5 0 005.6 0" strokeWidth="2" strokeLinecap="round"></path>
                    <path d="M12 7.5v9" strokeWidth="2" strokeLinecap="round"></path>
                  </svg>
                </div>
                <p className="text-xs text-gray-500 mt-1">Balance totale</p>
              </div>

              <div className="bg-gradient-to-br from-yellow-50 to-white p-4 rounded-xl border border-yellow-100">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-500 truncate">En attente</span>
                  <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-lg bg-yellow-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </span>
                </div>
                <p className="text-xl sm:text-2xl font-bold text-gray-800 truncate">{enAttente.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}€</p>
                <p className="text-xs text-gray-500 mt-1">Paiements en attente</p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-white p-4 rounded-xl border border-green-100">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-500 truncate">
                    Revenus {dateRange === 'mois' ? `sur ${selectedPeriod} ${selectedPeriod === 1 ? 'mois' : 'mois'}` : 'sur 12 mois'}
                  </span>
                  <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-lg bg-green-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                    </svg>
                  </span>
                </div>
                <p className="text-xl sm:text-2xl font-bold text-gray-800 truncate">
                  {(revenus.length > 0 ? revenus.reduce((sum, rev) => sum + rev.montant, 0) : 0).toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}€
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {dateRange === 'mois' 
                    ? selectedPeriod === 1 
                      ? 'Sur le mois dernier' 
                      : `Sur les ${selectedPeriod} derniers mois` 
                    : 'Sur les 12 derniers mois'}
                </p>
              </div>
            </div>

            <div className="h-64 sm:h-80 -mx-2 sm:mx-0 mt-2 sm:mt-4 px-2 sm:px-0">
              <ResponsiveContainer width="99%" height="100%">
                <LineChart 
                  data={revenus.sort(sortMonths)} 
                  margin={{ 
                    top: 20, 
                    right: 20, 
                    left: 12, 
                    bottom: 40 
                  }}
                >
                  <defs>
                    <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#FF7A35" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#FF7A35" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="mois" 
                    stroke="#666"
                    tick={{ fill: '#666', fontSize: '0.75rem' }}
                    interval="preserveStartEnd"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    dy={25}
                    tickMargin={5}
                    minTickGap={15}
                    padding={{ left: 10, right: 10 }}
                  />
                  <YAxis
                    stroke="#666"
                    tick={{ fill: '#666', fontSize: '0.75rem' }}
                    tickFormatter={(value) => `${value}€`}
                    width={45}
                    axisLine={false}
                    tickLine={false}
                    padding={{ top: 10, bottom: 10 }}
                    dx={-5}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF7A35',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                    formatter={(value: any) => [`${Number(value).toFixed(2)}€`, 'Revenus']}
                    labelFormatter={(label) => `${label.charAt(0).toUpperCase()}${label.slice(1)}`}
                  />
                  <Line
                    type="monotone"
                    dataKey="montant"
                    stroke="#FF7A35"
                    strokeWidth={3}
                    dot={{ fill: '#FF7A35', r: 4 }}
                    activeDot={{ r: 6, fill: '#FF7A35' }}
                  >
                    <LabelList 
                      dataKey="montant" 
                      position="top" 
                      fill="#FF7A35" 
                      fontSize={12}
                      fontWeight={600}
                      formatter={(value: any) => `${Number(value).toFixed(2)}€`}
                      offset={10}
                    />
                  </Line>
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Historique des transactions */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-4 sm:p-6 border-b border-gray-100 flex flex-col sm:flex-row gap-4 sm:justify-between sm:items-center">
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3 gap-2">
                <h2 className="text-xl font-semibold text-gray-800">Historique des transactions</h2>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                {selectedIds.length > 0 && (
                  <Button
                    variant="outlined"
                    startIcon={<DeleteIcon />}
                    onClick={() => setBatchDeleteConfirmOpen(true)}
                    size="small"
                    sx={{ 
                      borderColor: '#EF4444', 
                      color: '#EF4444', 
                      '&:hover': { borderColor: '#DC2626', backgroundColor: 'rgba(239, 68, 68, 0.04)' },
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    Supprimer ({selectedIds.length})
                  </Button>
                )}
                <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  onClick={() => setFilterModalOpen(true)}
                  size="small"
                  sx={{ 
                    borderColor: '#FF7A35', 
                    color: '#FF7A35', 
                    '&:hover': { borderColor: '#FF6B2C' },
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Filtrer
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleCreateClick}
                  size="small"
                  sx={{ 
                    borderColor: '#FF7A35', 
                    color: '#FF7A35', 
                    '&:hover': { borderColor: '#FF6B2C' },
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Nouvelle
                </Button>
                <Button
                  variant="contained"
                  startIcon={<ImportExportIcon />}
                  onClick={handleImportClick}
                  size="small"
                  sx={{ 
                    bgcolor: '#FF7A35', 
                    '&:hover': { bgcolor: '#FF6B2C' },
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Importer
                </Button>
                <button
                  onClick={handleExportPDF}
                  disabled={isLoading}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-[#FF7A35] rounded-lg shadow-sm hover:bg-[#FF965F] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                  </svg>
                  Exporter en PDF
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-orange-50 to-white">
                    <th className="px-4 py-3">
                      <Checkbox
                        indeterminate={selectedIds.length > 0 && selectedIds.length < transactions.length}
                        checked={transactions.length > 0 && selectedIds.length === transactions.length}
                        onChange={handleSelectAllClick}
                        size="small"
                        sx={{
                          color: '#FF7A35',
                          '&.Mui-checked': {
                            color: '#FF7A35',
                          },
                          '&.MuiCheckbox-indeterminate': {
                            color: '#FF7A35',
                          }
                        }}
                      />
                    </th>
                    <th 
                      className="group px-6 py-4 text-left cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('date')}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</span>
                        {sort.field === 'date' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-left cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('type')}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</span>
                        {sort.field === 'type' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-left cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('categorie')}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Catégorie</span>
                        {sort.field === 'categorie' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-left cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('methode')}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Méthode</span>
                        {sort.field === 'methode' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-left cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('description')}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</span>
                        {sort.field === 'description' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-right cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('montant')}
                    >
                      <div className="flex items-center justify-end gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Montant</span>
                        {sort.field === 'montant' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th 
                      className="group px-6 py-4 text-right cursor-pointer hover:bg-orange-50/50 transition-colors"
                      onClick={() => handleSort('statut')}
                    >
                      <div className="flex items-center justify-end gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Statut</span>
                        {sort.field === 'statut' && (
                          sort.order === 'asc' ? 
                            <ArrowUpwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} /> :
                            <ArrowDownwardIcon className="text-[#FF7A35]" sx={{ fontSize: 16 }} />
                        )}
                      </div>
                    </th>
                    <th className="group px-6 py-4 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {transactions.length === 0 ? (
                    <tr>
                      <td colSpan={9} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center space-y-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                          <p className="text-gray-500 text-lg font-medium">Aucune transaction à afficher</p>
                          <p className="text-gray-400 text-sm">Utilisez les boutons ci-dessus pour ajouter ou importer des transactions</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    transactions.map((transaction) => (
                      <tr
                        key={transaction.id}
                        className="hover:bg-orange-50/30 transition-colors duration-200 cursor-pointer border-b border-gray-100"
                        onClick={() => setSelectedTransaction(transaction)}
                      >
                        <td className="px-4 py-3" onClick={(e) => e.stopPropagation()}>
                          <Checkbox
                            checked={isSelected(transaction.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleSelectClick(e, transaction.id);
                            }}
                            size="small"
                            sx={{
                              color: '#E2E8F0',
                              '&.Mui-checked': {
                                color: '#FF7A35',
                              }
                            }}
                          />
                        </td>
                        <td className={`px-6 py-4 relative ${getBorderColor(transaction.type)}`}>
                          <div className="text-sm font-medium text-gray-900">{formatDateToFrench(transaction.date)}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-700 capitalize">{transaction.type}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-700 capitalize">{transaction.categorie}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 capitalize">
                            {transaction.methode}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm">
                            {transaction.client && (
                              <div className="font-medium text-gray-900">{formatClientName(transaction.client)}</div>
                            )}
                            <div className="text-gray-600" dangerouslySetInnerHTML={{ 
                              __html: DOMPurify.sanitize((transaction.mission || transaction.description) || '') 
                            }} />
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className={`text-sm font-semibold text-right ${
                            transaction.montant >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.montant.toLocaleString('fr-FR', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            })}€
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex justify-end">
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                              transaction.statut === 'complete' 
                                ? 'bg-green-50 text-green-700 ring-1 ring-green-600/20'
                                : transaction.statut === 'en_attente'
                                ? 'bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/20'
                                : 'bg-red-50 text-red-700 ring-1 ring-red-600/20'
                            }`}>
                              {getStatusText(transaction.statut)}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditClick(transaction);
                              }}
                              sx={{
                                color: '#FF7A35',
                                padding: '4px',
                                '&:hover': {
                                  backgroundColor: 'rgba(255, 122, 53, 0.08)'
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedTransaction(transaction);
                                setDeleteConfirmOpen(true);
                              }}
                              sx={{
                                color: '#EF4444',
                                padding: '4px',
                                '&:hover': {
                                  backgroundColor: 'rgba(239, 68, 68, 0.08)'
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
              <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                <TablePagination
                  component="div"
                  count={pagination.total}
                  page={pagination.page}
                  onPageChange={(_, newPage) => {
                    setPagination(prev => ({ ...prev, page: newPage }));
                  }}
                  rowsPerPage={pagination.rowsPerPage}
                  onRowsPerPageChange={(event) => {
                    setPagination(prev => ({
                      ...prev,
                      page: 0,
                      rowsPerPage: parseInt(event.target.value, 10)
                    }));
                  }}
                  labelRowsPerPage="Lignes par page"
                  labelDisplayedRows={({ from, to, count }) => `${from}-${to} sur ${count}`}
                  rowsPerPageOptions={[5, 10, 25, 50]}
                />
              </div>
            </div>
          </div>
        </>
      )}

      {/* Dialogue détails de transaction */}
      <ModalPortal
        isOpen={!!selectedTransaction && !deleteConfirmOpen && !editModalOpen}
        onBackdropClick={() => setSelectedTransaction(null)}
        closeOnBackdropClick={true}
      >
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
          <div className="bg-gradient-to-r from-orange-50 to-white border-b border-orange-100 px-8 py-6">
            <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <span className="h-8 w-1 bg-gradient-to-b from-[#FF7A35] to-[#FF9F6B] rounded-full"></span>
              Détails de la transaction
            </h2>
            <p className="mt-2 text-gray-600 text-sm">
              Informations complètes sur cette opération financière
            </p>
          </div>

          {selectedTransaction && (
            <>
              <div className="p-8 overflow-y-auto">
                <div className="space-y-5">
                  <div className="bg-gradient-to-r from-orange-50/50 to-white p-3 rounded-xl border border-orange-100/50">
                    <p className="text-sm font-medium text-gray-500 mb-1">ID Transaction</p>
                    <p className="font-mono text-sm bg-white py-1.5 px-3 rounded-lg border border-gray-100 shadow-sm overflow-x-auto">{selectedTransaction.id}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                      <p className="text-sm font-medium text-gray-500 mb-1">Date</p>
                      <p className="text-gray-900 font-medium">{formatDateToFrench(selectedTransaction.date)}</p>
                    </div>
                    
                    <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                      <p className="text-sm font-medium text-gray-500 mb-1">Type</p>
                      <p className="text-gray-900 font-medium capitalize flex items-center gap-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${
                          selectedTransaction.type === 'paiement' ? 'bg-green-500' :
                          selectedTransaction.type === 'remboursement' ? 'bg-blue-500' :
                          selectedTransaction.type === 'commission' ? 'bg-purple-500' :
                          selectedTransaction.type === 'abonnement' ? 'bg-yellow-500' :
                          selectedTransaction.type === 'transfert' ? 'bg-orange-500' : 'bg-gray-500'
                        }`}></span>
                        {selectedTransaction.type}
                      </p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {selectedTransaction.categorie && (
                      <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                        <p className="text-sm font-medium text-gray-500 mb-1">Catégorie</p>
                        <p className="text-gray-900 font-medium capitalize">{selectedTransaction.categorie}</p>
                      </div>
                    )}
                    
                    {selectedTransaction.methode && (
                      <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                        <p className="text-sm font-medium text-gray-500 mb-1">Méthode de paiement</p>
                        <p className="text-gray-900 font-medium capitalize">{selectedTransaction.methode}</p>
                      </div>
                    )}
                  </div>
                  
                  {selectedTransaction.client && (
                    <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                      <p className="text-sm font-medium text-gray-500 mb-1">Client</p>
                      <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(formatClientName(selectedTransaction.client) || '') }} />
                    </div>
                  )}
                  
                  {selectedTransaction.mission && (
                    <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                      <p className="text-sm font-medium text-gray-500 mb-1">Mission</p>
                      <p className="text-gray-900" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(selectedTransaction.mission || '') }} />
                    </div>
                  )}
                  
                  {selectedTransaction.description && (
                    <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                      <p className="text-sm font-medium text-gray-500 mb-1">Description</p>
                      <p className="text-gray-900" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(selectedTransaction.description || '') }} />
                    </div>
                  )}
                  
                  <div className="bg-gradient-to-r from-orange-50 to-white p-4 rounded-xl border border-orange-100 shadow-sm">
                    <p className="text-sm font-medium text-gray-500 mb-1">Montant</p>
                    <p className={`text-2xl font-bold ${
                      selectedTransaction.montant >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {selectedTransaction.montant.toFixed(2)}€
                    </p>
                  </div>
                  
                  <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                    <p className="text-sm font-medium text-gray-500 mb-1">Statut</p>
                    <span className={`mt-1 inline-block px-3 py-1.5 rounded-full text-sm font-medium ${
                      selectedTransaction.statut === 'complete' 
                        ? 'bg-green-50 text-green-700 ring-1 ring-green-600/20'
                        : selectedTransaction.statut === 'en_attente'
                        ? 'bg-yellow-50 text-yellow-700 ring-1 ring-yellow-600/20'
                        : 'bg-red-50 text-red-700 ring-1 ring-red-600/20'
                    }`}>
                      {getStatusText(selectedTransaction.statut)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-white border-t border-orange-100 px-8 py-6 flex justify-end gap-4 mt-auto">
                <Button 
                  onClick={() => setSelectedTransaction(null)}
                  variant="outlined"
                  sx={{ 
                    borderColor: '#E2E8F0',
                    color: '#64748B',
                    '&:hover': {
                      borderColor: '#94A3B8',
                      backgroundColor: 'rgba(148, 163, 184, 0.04)'
                    }
                  }}
                >
                  Fermer
                </Button>
                <Button 
                  onClick={() => handleEditClick(selectedTransaction)}
                  variant="contained"
                  sx={{ 
                    background: 'linear-gradient(to right, #FF7A35, #FF9F6B)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(to right, #FF6B2C, #FF8F5B)'
                    }
                  }}
                >
                  Modifier
                </Button>
              </div>
            </>
          )}
        </div>
      </ModalPortal>

      {/* Dialogue de confirmation de suppression */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => {
          setDeleteConfirmOpen(false);
          setSelectedTransaction(null);
        }}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          {selectedTransaction && (
            <p>
              Êtes-vous sûr de vouloir supprimer la transaction {selectedTransaction.id} d'un montant de{' '}
              <span className={selectedTransaction.montant >= 0 ? 'text-green-600' : 'text-red-600'}>
                {selectedTransaction.montant.toFixed(2)}€
              </span> ?
            </p>
          )}
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              setDeleteConfirmOpen(false);
              setSelectedTransaction(null);
            }}
            sx={{ color: '#5F6368' }}
          >
            Annuler
          </Button>
          <Button 
            onClick={handleDeleteTransaction} 
            color="error"
            variant="contained"
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogues d'édition/création de transaction */}
      <ModalPortal
        isOpen={editModalOpen || createModalOpen}
        onBackdropClick={() => editModalOpen ? setEditModalOpen(false) : setCreateModalOpen(false)}
        closeOnBackdropClick={true}
      >
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
          <div className="bg-gradient-to-r from-orange-50 to-white border-b border-orange-100 px-8 py-6">
            <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <span className="h-8 w-1 bg-gradient-to-b from-[#FF7A35] to-[#FF9F6B] rounded-full"></span>
              {editModalOpen ? 'Modifier la transaction' : 'Créer une transaction'}
            </h2>
            <p className="mt-2 text-gray-600 text-sm">
              {editModalOpen ? 'Modifiez les informations de la transaction' : 'Remplissez les informations pour créer une nouvelle transaction'}
            </p>
          </div>

          <div className="p-4 sm:p-8 overflow-y-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <TextField
                name="date"
                label="Date"
                type="date"
                value={formData.date}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                InputLabelProps={{ shrink: true }}
                size="small"
              />
              <TextField
                name="montant"
                label="Montant (€)"
                type="number"
                value={formData.montant}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                InputProps={{ 
                  inputProps: { step: '0.01' },
                  startAdornment: (
                    <span className="text-gray-400 mr-2">€</span>
                  ),
                }}
                size="small"
              />
              <TextField
                name="type"
                label="Type"
                select
                value={formData.type}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                  '& .MuiSelect-select': {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  },
                }}
                size="small"
              >
                <MenuItem value="paiement">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-green-500"></span>
                    Paiement
                  </div>
                </MenuItem>
                <MenuItem value="remboursement">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                    Remboursement
                  </div>
                </MenuItem>
                <MenuItem value="commission">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-purple-500"></span>
                    Commission
                  </div>
                </MenuItem>
                <MenuItem value="abonnement">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                    Abonnement
                  </div>
                </MenuItem>
                <MenuItem value="transfert">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                    Transfert
                  </div>
                </MenuItem>
              </TextField>
              <TextField
                name="categorie"
                label="Catégorie"
                select
                value={formData.categorie}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              >
                {categoriesOperation.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-gray-400"></span>
                      {cat.charAt(0).toUpperCase() + cat.slice(1)}
                    </div>
                  </MenuItem>
                ))}
              </TextField>
              <TextField
                name="methode"
                label="Méthode de paiement"
                select
                value={formData.methode}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              >
                {typesPayment.map((type) => (
                  <MenuItem key={type} value={type}>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-gray-400"></span>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </div>
                  </MenuItem>
                ))}
              </TextField>
              <TextField
                name="statut"
                label="Statut"
                select
                value={formData.statut}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              >
                <MenuItem value="en_attente">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                    En attente
                  </div>
                </MenuItem>
                <MenuItem value="complete">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-green-500"></span>
                    Complété
                  </div>
                </MenuItem>
                <MenuItem value="refuse">
                  <div className="flex items-center gap-2">
                    <span className="w-2 h-2 rounded-full bg-red-500"></span>
                    Refusé
                  </div>
                </MenuItem>
              </TextField>
              <TextField
                name="client"
                label="Client"
                value={formData.client}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              />
              <TextField
                name="mission"
                label="Mission"
                value={formData.mission}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              />
              <TextField
                name="reference"
                label="Référence"
                value={formData.reference}
                onChange={handleFormChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              />
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleFormChange}
                fullWidth
                multiline
                rows={2}
                sx={{
                  gridColumn: { xs: 'span 1', sm: 'span 2' },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                size="small"
              />
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-50 to-white border-t border-orange-100 px-8 py-6 flex justify-end gap-4 mt-auto">
            <Button 
              onClick={() => editModalOpen ? setEditModalOpen(false) : setCreateModalOpen(false)}
              variant="outlined"
              sx={{ 
                borderColor: '#E2E8F0',
                color: '#64748B',
                '&:hover': {
                  borderColor: '#94A3B8',
                  backgroundColor: 'rgba(148, 163, 184, 0.04)'
                }
              }}
            >
              Annuler
            </Button>
            <Button 
              onClick={handleFormSubmit}
              variant="contained"
              disabled={!formData.montant || !formData.date}
              sx={{ 
                background: 'linear-gradient(to right, #FF7A35, #FF9F6B)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(to right, #FF6B2C, #FF8F5B)'
                },
                '&.Mui-disabled': {
                  background: '#E2E8F0',
                  color: '#94A3B8'
                }
              }}
            >
              {editModalOpen ? 'Enregistrer' : 'Créer'}
            </Button>
          </div>
        </div>
      </ModalPortal>

      {/* Dialogue d'importation */}
      <ModalPortal
        isOpen={importModalOpen}
        onBackdropClick={() => !isImporting && setImportModalOpen(false)}
        closeOnBackdropClick={!isImporting}
      >
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
          <div className="bg-gradient-to-r from-orange-50 to-white border-b border-orange-100 px-8 py-6 flex justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                <span className="h-8 w-1 bg-gradient-to-b from-[#FF7A35] to-[#FF9F6B] rounded-full"></span>
                Importer les paiements
              </h2>
              <p className="mt-2 text-gray-600 text-sm">
                Sélectionnez les transactions que vous souhaitez importer dans votre historique de paiements
              </p>
            </div>
            {!isImporting && (
              <button 
                onClick={() => setImportModalOpen(false)}
                className="p-1.5 rounded-full text-gray-500 hover:text-[#FF7A35] hover:bg-orange-50 transition-colors self-start"
                aria-label="Fermer"
              >
                <CloseIcon fontSize="small" />
              </button>
            )}
          </div>
          
          <div className="flex-1 overflow-y-auto">
            <div className="border-b border-gray-200">
              <div className="px-6">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                  {(Object.keys(transactionsToImport) as Array<keyof ImportData>).map((method) => {
                    // Vérifier que method est une chaîne de caractères valide
                    const methodName = String(method);
                    return (
                      <button
                        key={methodName}
                        onClick={() => setActiveImportTab(method)}
                        className={`${
                          activeImportTab === method
                            ? 'border-[#FF7A35] text-[#FF7A35]'
                            : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                        } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                      >
                        {methodName.charAt(0).toUpperCase() + methodName.slice(1)}
                        {transactionsToImport[method].length > 0 && (
                          <span className={`${
                            activeImportTab === method ? 'bg-[#FF7A35] text-white' : 'bg-gray-100 text-gray-900'
                          } py-0.5 px-2 rounded-full text-xs font-medium`}>
                            {transactionsToImport[method].length}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>

            <div className="p-6">
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <CircularProgress sx={{ color: '#FF7A35' }} />
                  <p className="mt-4 text-gray-600">{loadingMessage}</p>
                </div>
              ) : isImporting ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <CircularProgress sx={{ color: '#FF7A35' }} />
                  <p className="mt-4 text-gray-600">Importation des transactions...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <Alert 
                    severity="info" 
                    className="mb-4 border border-blue-100 rounded-xl"
                    icon={<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>}
                  >
                    <p>Vous pouvez importer un maximum de <strong>100 transactions</strong> par lot. Si vous devez importer plus de transactions, veuillez procéder en plusieurs fois.</p>
                    {getSelectedCount() > 100 && (
                      <p className="mt-2 text-red-600 font-medium">
                        Vous avez sélectionné {getSelectedCount()} transactions. Veuillez réduire votre sélection à 100 transactions maximum.
                      </p>
                    )}
                  </Alert>

                  <div className="border border-gray-200 rounded-xl overflow-hidden">
                    <div className="bg-gradient-to-r from-orange-50 to-white px-6 py-4 border-b border-orange-100">
                      <div className="flex justify-between items-center">
                        <h3 className="font-semibold text-gray-800 capitalize">{activeImportTab}</h3>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={transactionsToImport[activeImportTab].length > 0 && 
                                transactionsToImport[activeImportTab].every((t: TransactionToImport) => t.selected)}
                              indeterminate={
                                transactionsToImport[activeImportTab].some((t: TransactionToImport) => t.selected) &&
                                !transactionsToImport[activeImportTab].every((t: TransactionToImport) => t.selected)
                              }
                              onChange={(e) => handleSelectAllByMethod(activeImportTab, e.target.checked)}
                              size="small"
                              sx={{
                                '&.Mui-checked': { color: '#FF7A35' },
                                '&.MuiCheckbox-indeterminate': { color: '#FF7A35' }
                              }}
                            />
                          }
                          label={<span className="text-sm">Tout sélectionner</span>}
                        />
                      </div>
                    </div>

                    <div 
                      className="overflow-x-auto max-h-[60vh]"
                      onScroll={(e) => handleScroll(e, activeImportTab)}
                    >
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              <span className="sr-only">Sélection</span>
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {activeImportTab === 'missions' ? 'Mission' : 'Description'}
                            </th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Montant
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {transactionsToImport[activeImportTab].map((transaction: TransactionToImport) => (
                            <tr key={transaction.id} className="hover:bg-orange-50/20">
                              <td className="px-4 py-3 whitespace-nowrap">
                                <Checkbox
                                  checked={transaction.selected || false}
                                  onChange={() => handleSelectTransaction(transaction.id, activeImportTab, '')}
                                  size="small"
                                  sx={{ 
                                    '&.Mui-checked': { color: '#FF7A35' },
                                    '& .MuiSvgIcon-root': { fontSize: '1.25rem' }
                                  }}
                                />
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                {transaction.display_date}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500">
                                {activeImportTab === 'missions' ? (
                                  <>
                                    {transaction.client && (
                                      <div className="font-medium text-gray-900">{formatClientName(transaction.client)}</div>
                                    )}
                                    <div className="text-gray-500">{transaction.mission}</div>
                                  </>
                                ) : (
                                  <>
                                    <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(transaction.description || '') }} />
                                    {transaction.message && (
                                      <div className="mt-1 text-xs text-gray-500 italic">
                                        "{transaction.message}"
                                      </div>
                                    )}
                                  </>
                                )}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-right font-medium">
                                <span className={transaction.montant >= 0 ? 'text-green-600' : 'text-red-600'}>
                                  {transaction.montant.toFixed(2)}€
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {importPagination[activeImportTab].loading && (
                        <div className="flex justify-center py-4">
                          <CircularProgress size={24} sx={{ color: '#FF7A35' }} />
                        </div>
                      )}
                      {!importPagination[activeImportTab].loading && !hasMore[activeImportTab] && transactionsToImport[activeImportTab].length > 0 && (
                        <div className="text-center py-4 text-gray-500 text-sm">
                          Toutes les transactions ont été chargées
                        </div>
                      )}
                      {!importPagination[activeImportTab].loading && transactionsToImport[activeImportTab].length === 0 && (
                        <div className="text-center py-8">
                          <p className="text-gray-500">Aucune transaction à importer</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {getSelectedCount()} transaction(s) sélectionnée(s)
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setImportModalOpen(false)}
                  disabled={isImporting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Annuler
                </button>
                <button
                  onClick={handleImportSelectedTransactions}
                  disabled={isImporting || getSelectedCount() === 0 || getSelectedCount() > 100}
                  className="px-4 py-2 text-sm font-medium text-white bg-[#FF7A35] rounded-lg shadow-sm hover:bg-[#FF965F] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Importer {getSelectedCount()} transaction{getSelectedCount() > 1 ? 's' : ''}
                  {getSelectedCount() > 100 ? ' (limite dépassée)' : ''}
                </button>
              </div>
            </div>
          </div>
        </div>
      </ModalPortal>

      {/* Modal de filtrage */}
      <ModalPortal
        isOpen={filterModalOpen}
        onBackdropClick={() => {
          setFilterModalOpen(false);
          setTempFilters({ ...filters });
        }}
        closeOnBackdropClick={true}
      >
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
          <div className="bg-gradient-to-r from-orange-50 to-white border-b border-orange-100 px-8 py-6 flex justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                <span className="h-8 w-1 bg-gradient-to-b from-[#FF7A35] to-[#FF9F6B] rounded-full"></span>
                Filtrer les transactions
              </h2>
              <p className="mt-2 text-gray-600 text-sm">
                Personnalisez les critères de filtrage pour affiner votre liste de transactions
              </p>
            </div>
            <button 
              onClick={() => {
                setFilterModalOpen(false);
                setTempFilters({ ...filters });
              }}
              className="p-1.5 rounded-full text-gray-500 hover:text-[#FF7A35] hover:bg-orange-50 transition-colors self-start"
              aria-label="Fermer"
            >
              <CloseIcon fontSize="small" />
            </button>
          </div>

          <div className="p-4 sm:p-8 overflow-y-auto">
            <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100 mb-4">
              <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                <SearchIcon className="h-4 w-4 text-[#FF7A35]" />
                Recherche
              </p>
              <TextField
                name="searchTerm"
                label="Rechercher une transaction"
                placeholder="Client, mission, description..."
                value={tempFilters.searchTerm}
                onChange={(e) => setTempFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                fullWidth
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#E2E8F0',
                      transition: 'all 0.2s',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF9F6B',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF7A35',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF7A35',
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <SearchIcon className="mr-2 text-gray-400" fontSize="small" />
                  ),
                }}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Période
                </p>
                <div className="grid grid-cols-2 gap-3">
                  <TextField
                    name="dateDebut"
                    label="Date de début"
                    type="date"
                    value={tempFilters.dateDebut}
                    onChange={(e) => setTempFilters(prev => ({ ...prev, dateDebut: e.target.value }))}
                    fullWidth
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E2E8F0',
                          transition: 'all 0.2s',
                        },
                        '&:hover fieldset': {
                          borderColor: '#FF9F6B',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF7A35',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF7A35',
                      },
                    }}
                    InputLabelProps={{ shrink: true }}
                  />
                  <TextField
                    name="dateFin"
                    label="Date de fin"
                    type="date"
                    value={tempFilters.dateFin}
                    onChange={(e) => setTempFilters(prev => ({ ...prev, dateFin: e.target.value }))}
                    fullWidth
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E2E8F0',
                          transition: 'all 0.2s',
                        },
                        '&:hover fieldset': {
                          borderColor: '#FF9F6B',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF7A35',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF7A35',
                      },
                    }}
                    InputLabelProps={{ shrink: true }}
                  />
                </div>
              </div>

              <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Montant
                </p>
                <div className="grid grid-cols-2 gap-3">
                  <TextField
                    name="montantMin"
                    label="Minimum"
                    type="number"
                    value={tempFilters.montantMin}
                    onChange={(e) => setTempFilters(prev => ({ ...prev, montantMin: e.target.value }))}
                    fullWidth
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E2E8F0',
                          transition: 'all 0.2s',
                        },
                        '&:hover fieldset': {
                          borderColor: '#FF9F6B',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF7A35',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF7A35',
                      },
                    }}
                    InputProps={{ 
                      inputProps: { step: '0.01' },
                      startAdornment: (
                        <span className="text-gray-400 mr-1">€</span>
                      ),
                    }}
                  />
                  <TextField
                    name="montantMax"
                    label="Maximum"
                    type="number"
                    value={tempFilters.montantMax}
                    onChange={(e) => setTempFilters(prev => ({ ...prev, montantMax: e.target.value }))}
                    fullWidth
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: '#E2E8F0',
                          transition: 'all 0.2s',
                        },
                        '&:hover fieldset': {
                          borderColor: '#FF9F6B',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#FF7A35',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#FF7A35',
                      },
                    }}
                    InputProps={{ 
                      inputProps: { step: '0.01' },
                      startAdornment: (
                        <span className="text-gray-400 mr-1">€</span>
                      ),
                    }}
                  />
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50/50 to-white p-4 rounded-xl border border-orange-100/50">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Types de transaction
                </p>
                <TextField
                  name="type"
                  label="Sélectionner les types"
                  select
                  value={tempFilters.type}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTempFilters(prev => ({ ...prev, type: Array.isArray(value) ? value : [] }));
                  }}
                  fullWidth
                  size="small"
                  SelectProps={{ 
                    multiple: true,
                    renderValue: (selected) => (
                      <div className="flex flex-wrap gap-1">
                        {(selected as string[]).map((value) => (
                          <span key={value} className="bg-orange-50 text-orange-700 px-2 py-0.5 rounded-full text-xs">
                            {value.charAt(0).toUpperCase() + value.slice(1)}
                          </span>
                        ))}
                      </div>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#E2E8F0',
                        transition: 'all 0.2s',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF9F6B',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF7A35',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF7A35',
                    },
                  }}
                >
                  <MenuItem value="paiement">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-green-500"></span>
                      Paiement
                    </div>
                  </MenuItem>
                  <MenuItem value="remboursement">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                      Remboursement
                    </div>
                  </MenuItem>
                  <MenuItem value="commission">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-purple-500"></span>
                      Commission
                    </div>
                  </MenuItem>
                  <MenuItem value="abonnement">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                      Abonnement
                    </div>
                  </MenuItem>
                  <MenuItem value="transfert">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                      Transfert
                    </div>
                  </MenuItem>
                </TextField>
              </div>

              <div className="bg-gradient-to-r from-orange-50/50 to-white p-4 rounded-xl border border-orange-100/50">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Catégories
                </p>
                <TextField
                  name="categorie"
                  label="Sélectionner les catégories"
                  select
                  value={tempFilters.categorie}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTempFilters(prev => ({ ...prev, categorie: Array.isArray(value) ? value : [] }));
                  }}
                  fullWidth
                  size="small"
                  SelectProps={{ 
                    multiple: true,
                    renderValue: (selected) => (
                      <div className="flex flex-wrap gap-1">
                        {(selected as string[]).map((value) => (
                          <span key={value} className="bg-orange-50 text-orange-700 px-2 py-0.5 rounded-full text-xs">
                            {value.charAt(0).toUpperCase() + value.slice(1)}
                          </span>
                        ))}
                      </div>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#E2E8F0',
                        transition: 'all 0.2s',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF9F6B',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF7A35',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF7A35',
                    },
                  }}
                >
                  {categoriesOperation.map((cat) => (
                    <MenuItem key={cat} value={cat}>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 rounded-full bg-gray-400"></span>
                        {cat.charAt(0).toUpperCase() + cat.slice(1)}
                      </div>
                    </MenuItem>
                  ))}
                </TextField>
              </div>

              <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Méthodes de paiement
                </p>
                <TextField
                  name="methode"
                  label="Sélectionner les méthodes"
                  select
                  value={tempFilters.methode}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTempFilters(prev => ({ ...prev, methode: Array.isArray(value) ? value : [] }));
                  }}
                  fullWidth
                  size="small"
                  SelectProps={{ 
                    multiple: true,
                    renderValue: (selected) => (
                      <div className="flex flex-wrap gap-1">
                        {(selected as string[]).map((value) => (
                          <span key={value} className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs">
                            {value.charAt(0).toUpperCase() + value.slice(1)}
                          </span>
                        ))}
                      </div>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#E2E8F0',
                        transition: 'all 0.2s',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF9F6B',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF7A35',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF7A35',
                    },
                  }}
                >
                  {typesPayment.map((type) => (
                    <MenuItem key={type} value={type}>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 rounded-full bg-gray-400"></span>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </div>
                    </MenuItem>
                  ))}
                </TextField>
              </div>

              <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                <p className="text-sm font-medium text-gray-500 mb-3 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#FF7A35]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Statuts
                </p>
                <TextField
                  name="statut"
                  label="Sélectionner les statuts"
                  select
                  value={tempFilters.statut}
                  onChange={(e) => {
                    const value = e.target.value;
                    setTempFilters(prev => ({ ...prev, statut: Array.isArray(value) ? value : [] }));
                  }}
                  fullWidth
                  size="small"
                  SelectProps={{ 
                    multiple: true,
                    renderValue: (selected) => (
                      <div className="flex flex-wrap gap-1">
                        {(selected as string[]).map((value) => (
                          <span key={value} className={`px-2 py-0.5 rounded-full text-xs
                            ${value === 'complete' ? 'bg-green-50 text-green-700' : 
                            value === 'en_attente' ? 'bg-yellow-50 text-yellow-700' : 
                            'bg-red-50 text-red-700'}`}>
                            {value === 'complete' ? 'Complété' : 
                              value === 'en_attente' ? 'En attente' : 'Refusé'}
                          </span>
                        ))}
                      </div>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#E2E8F0',
                        transition: 'all 0.2s',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF9F6B',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF7A35',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF7A35',
                    },
                  }}
                >
                  <MenuItem value="en_attente">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-yellow-500"></span>
                      En attente
                    </div>
                  </MenuItem>
                  <MenuItem value="complete">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-green-500"></span>
                      Complété
                    </div>
                  </MenuItem>
                  <MenuItem value="refuse">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 rounded-full bg-red-500"></span>
                      Refusé
                    </div>
                  </MenuItem>
                </TextField>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-50 to-white border-t border-orange-100 px-8 py-6 flex justify-end gap-4 mt-auto">
            <Button 
              onClick={() => {
                const emptyFilters = {
                  dateDebut: '',
                  dateFin: '',
                  type: [],
                  categorie: [],
                  methode: [],
                  statut: [],
                  montantMin: '',
                  montantMax: '',
                  searchTerm: ''
                };
                setTempFilters(emptyFilters);
              }}
              variant="outlined"
              sx={{ 
                borderColor: '#E2E8F0',
                color: '#64748B',
                '&:hover': {
                  borderColor: '#94A3B8',
                  backgroundColor: 'rgba(148, 163, 184, 0.04)'
                }
              }}
            >
              Réinitialiser
            </Button>
            <Button 
              onClick={() => {
                setFilters(tempFilters);
                setSearchTerm(tempFilters.searchTerm);
                setFilterModalOpen(false);
                setPagination(prev => ({ ...prev, page: 0 }));
              }}
              variant="contained"
              sx={{ 
                background: 'linear-gradient(to right, #FF7A35, #FF9F6B)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(to right, #FF6B2C, #FF8F5B)'
                }
              }}
            >
              Appliquer les filtres
            </Button>
          </div>
        </div>
      </ModalPortal>

      {/* Dialogue de confirmation de suppression en masse */}
      <ModalPortal
        isOpen={batchDeleteConfirmOpen}
        onBackdropClick={() => setBatchDeleteConfirmOpen(false)}
        closeOnBackdropClick={true}
      >
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
          <div className="bg-gradient-to-r from-red-100 to-white border-b border-red-200 px-8 py-6">
            <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
              <span className="h-8 w-1 bg-gradient-to-b from-red-600 to-red-400 rounded-full"></span>
              Confirmation
            </h2>
            <p className="mt-2 text-gray-600 text-sm">
              Supprimer {selectedIds.length} transaction(s) en masse
            </p>
          </div>
          
          <div className="p-8">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                <DeleteIcon fontSize="large" sx={{ color: '#EF4444' }} />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Êtes-vous sûr de vouloir supprimer ces {selectedIds.length} transaction(s) ?
              </h3>
              <p className="text-gray-600">
                Cette action est irréversible et les données supprimées ne pourront pas être récupérées.
              </p>
            </div>
          </div>
          
          <div className="bg-gray-50 border-t border-gray-100 px-8 py-4 flex justify-end gap-4">
            <Button 
              onClick={() => setBatchDeleteConfirmOpen(false)}
              variant="outlined"
              sx={{ 
                borderColor: '#E2E8F0',
                color: '#64748B',
                '&:hover': {
                  borderColor: '#94A3B8',
                  backgroundColor: 'rgba(148, 163, 184, 0.04)'
                }
              }}
            >
              Annuler
            </Button>
            <Button 
              onClick={handleBatchDeleteTransactions}
              variant="contained"
              color="error"
              sx={{ 
                bgcolor: '#EF4444',
                '&:hover': {
                  bgcolor: '#DC2626'
                }
              }}
            >
              Supprimer
            </Button>
          </div>
        </div>
      </ModalPortal>
    </div>
  );
} 