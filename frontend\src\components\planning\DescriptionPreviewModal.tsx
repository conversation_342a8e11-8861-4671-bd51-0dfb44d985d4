import React, { useState, useRef } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { X, FileText, Save, Edit } from 'lucide-react';
import DOMPurify from 'dompurify';
import ModalPortal from '../ModalPortal';
import TiptapEditor, { TiptapInstance } from '../TiptapEditor';

interface DescriptionPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  description: string;
  title?: string;
  onSave?: (newDescription: string) => void;
  editable?: boolean;
  zIndex?: number;
}

const DescriptionPreviewModal: React.FC<DescriptionPreviewModalProps> = ({
  isOpen,
  onClose,
  description,
  title = 'Description complète',
  onSave,
  editable = true,
  zIndex = 2000
}) => {
  // État pour le mode édition
  const [isEditMode, setIsEditMode] = useState(false);
  
  // État pour stocker le contenu de l'éditeur lors de l'édition
  const [editContent, setEditContent] = useState(description);
  
  // Compteur de caractères
  const [charCount, setCharCount] = useState(0);
  
  // Référence à l'éditeur TipTap
  const editorRef = useRef<TiptapInstance>(null);
  
  // Nettoyage du HTML pour l'affichage
  const sanitizedDescription = DOMPurify.sanitize(description);
  
  // Mettre à jour le compteur de caractères quand le contenu change
  const handleEditorChange = (content: string) => {
    setEditContent(content);
    
    // Extraire le texte pour le comptage des caractères
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    setCharCount(textContent.length);
  };
  
  // Sauvegarder les modifications
  const handleSave = () => {
    if (onSave) {
      // Passer le contenu HTML nettoyé
      const sanitizedContent = DOMPurify.sanitize(editContent);
      onSave(sanitizedContent);
    }
    setIsEditMode(false);
  };
  
  // Activer le mode édition
  const enableEditMode = () => {
    setEditContent(sanitizedDescription);
    setIsEditMode(true);
    
    // Extraire le texte pour initialiser le compteur de caractères
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = sanitizedDescription;
    const textContent = tempDiv.textContent || tempDiv.innerText || '';
    setCharCount(textContent.length);
  };
  
  // Annuler l'édition
  const handleCancel = () => {
    setIsEditMode(false);
  };
  
  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={onClose} zIndex={zIndex}>
      <Box
        sx={{
          backgroundColor: '#fff',
          borderRadius: '16px',
          width: '100%',
          maxWidth: '700px',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
          position: 'relative',
          m: { xs: 2, sm: 3 },
          overflow: 'auto',
          overflowY: 'scroll'
        }}
      >
        {/* Entête */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: { xs: '14px 16px', sm: '18px 24px' },
            backgroundColor: '#FF6B2C',
            color: 'white',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <FileText size={18} />
            <Typography sx={{ fontWeight: 700, fontSize: { xs: '1rem', sm: '1.1rem' } }}>
              {title}
            </Typography>
          </Box>
          <Button
            onClick={onClose}
            sx={{
              minWidth: 'auto',
              p: 0.5,
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.15)'
              }
            }}
          >
            <X size={20} />
          </Button>
        </Box>
        
        {/* Contenu */}
        <Box
          sx={{
            p: { xs: 2, sm: 3 },
            overflowY: 'auto',
            flexGrow: 1,
            position: 'relative'
          }}
        >
          {isEditMode ? (
            <Box sx={{ position: 'relative' }}>
              <TiptapEditor
                content={editContent}
                onChange={handleEditorChange}
                placeholder="Saisissez votre description..."
                maxLength={1200}
                ref={editorRef}
              />
              <Box
                sx={{
                  position: 'absolute',
                  bottom: '8px',
                  right: '8px',
                  background: 'rgba(255, 255, 255, 0.8)',
                  p: '2px 8px',
                  borderRadius: '10px',
                  fontSize: '0.75rem',
                  color: charCount > 1200 ? 'error.main' : 'text.secondary'
                }}
              >
                {charCount}/1200
              </Box>
            </Box>
          ) : (
            <Box
              sx={{
                p: 2,
                borderRadius: '8px',
                backgroundColor: '#f9f9f9',
                '& p': {
                  margin: '0.5em 0',
                },
                '& h1, & h2, & h3, & h4, & h5, & h6': {
                  margin: '0.5em 0',
                },
                '& ul, & ol': {
                  paddingLeft: '1.5em',
                },
                '& a': {
                  color: '#FF6B2C',
                },
                '& blockquote': {
                  borderLeft: '3px solid #FF6B2C',
                  paddingLeft: '1em',
                  margin: '1em 0',
                  color: '#666',
                },
                '& table': {
                  width: '100%',
                  borderCollapse: 'collapse',
                  margin: '1em 0',
                },
                '& th, & td': {
                  border: '1px solid #ddd',
                  padding: '0.5em',
                }
              }}
              dangerouslySetInnerHTML={{ __html: sanitizedDescription }}
            />
          )}
        </Box>
        
        {/* Pied de page avec boutons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 1,
            p: { xs: 2, sm: 3 },
            backgroundColor: '#f6f6f6',
            borderTop: '1px solid #eee'
          }}
        >
          {isEditMode ? (
            <>
              <Button
                variant="outlined"
                onClick={handleCancel}
                sx={{
                  borderColor: '#ddd',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#FF6B2C',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                  },
                  textTransform: 'none',
                  fontWeight: 600,
                  borderRadius: '10px'
                }}
              >
                Annuler
              </Button>
              <Button
                variant="contained"
                onClick={handleSave}
                startIcon={<Save size={16} />}
                disabled={charCount > 1200}
                sx={{
                  backgroundColor: '#FF6B2C',
                  '&:hover': {
                    backgroundColor: '#FF7A35',
                  },
                  textTransform: 'none',
                  fontWeight: 600,
                  borderRadius: '10px',
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                }}
              >
                Enregistrer
              </Button>
            </>
          ) : (
            editable && (
              <Button
                variant="outlined"
                onClick={enableEditMode}
                startIcon={<Edit size={16} />}
                sx={{
                  borderColor: '#ddd',
                  color: '#666',
                  '&:hover': {
                    borderColor: '#FF6B2C',
                    backgroundColor: 'rgba(255, 107, 44, 0.04)',
                    color: '#FF6B2C',
                  },
                  textTransform: 'none',
                  fontWeight: 600,
                  borderRadius: '10px'
                }}
              >
                Modifier
              </Button>
            )
          )}
        </Box>
      </Box>
    </ModalPortal>
  );
};

export default DescriptionPreviewModal; 