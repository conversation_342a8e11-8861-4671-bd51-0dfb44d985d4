import axios from 'axios';
import logger from '../utils/logger';
import { api } from './api';

interface CsrfCache {
  token: string;
  timestamp: number;
  promise: Promise<string> | null;
}

// Réduire la durée du cache pour éviter les problèmes d'authentification
const CACHE_DURATION = 30000; // 30 secondes
let csrfCache: CsrfCache = {
  token: '',
  timestamp: 0,
  promise: null
};

export const fetchCsrfToken = async (): Promise<string> => {
  const now = Date.now();

  // Si un token valide existe dans le cache, le retourner
  if (csrfCache.token && (now - csrfCache.timestamp) < CACHE_DURATION) {
    // logger.info('🔄 CSRF: Utilisation du token en cache');
    return csrfCache.token;
  }

  // Si une requête est déjà en cours, retourner sa promesse
  if (csrfCache.promise) {
    try {
      // logger.info('⏳ CSRF: Attente de la requête en cours...');
      return await csrfCache.promise;
    } catch (error) {
      // Si la promesse échoue, on reset le cache pour permettre une nouvelle tentative
      csrfCache.promise = null;
      throw error;
    }
  }

  // Créer une nouvelle promesse pour la requête
  csrfCache.promise = (async () => {
    try {
      // logger.info('🔄 CSRF: Récupération d\'un nouveau token...');

      // Ajouter un timestamp pour éviter le cache du navigateur
      const timestamp = Date.now();
      const response = await api.get(`/api/auth/csrf?_=${timestamp}`, {
        withCredentials: true
      });

      // logger.info('📥 CSRF: Réponse reçue', {
      //   status: response.status,
      //   hasToken: !!response.data?.csrfToken,
      //   headers: Object.fromEntries(Object.entries(response.headers))
      // });

      if (!response.data?.csrfToken) {
        throw new Error('Token CSRF non reçu du serveur');
      }

      const newToken = response.data.csrfToken;

      // Mettre à jour le cache avec le nouveau token
      csrfCache = {
        token: newToken,
        timestamp: now,
        promise: null
      };

      // Définir le token dans les headers par défaut de l'instance api
      api.defaults.headers.common['X-CSRF-Token'] = newToken;

      // logger.info('✅ CSRF: Token récupéré et mis en cache', {
        // tokenPreview: newToken.substring(0, 10) + '...'
      // });

      return newToken;
    } catch (error) {
      // En cas d'erreur, on reset le cache
      csrfCache = {
        token: '',
        timestamp: 0,
        promise: null
      };

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          logger.warn('Rate limit atteint pour la récupération du token CSRF');
          // En cas de rate limit, on retourne le dernier token valide si disponible
          if (csrfCache.token) {
            logger.info('Utilisation du dernier token CSRF valide du cache');
            return csrfCache.token;
          }
        }

        logger.error('❌ CSRF: Erreur lors de la récupération du token CSRF:', {
          status: error.response?.status,
          data: error.response?.data,
          headers: error.response?.headers
        });
      } else {
        logger.error('❌ CSRF: Erreur inattendue lors de la récupération du token CSRF:', error);
      }

      throw error;
    }
  })();

  return csrfCache.promise;
};

export const clearCsrfCache = () => {
  csrfCache = {
    token: '',
    timestamp: 0,
    promise: null
  };
  // logger.info('🗑️ Cache CSRF effacé manuellement');
};