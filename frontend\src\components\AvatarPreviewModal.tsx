import React, { useState, useEffect } from 'react';
import ModalPortal from './ModalPortal';
import { X } from 'lucide-react';
import DOMPurify from 'dompurify';
import { ArrowLeft, ArrowRight } from 'lucide-react';

interface AvatarPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  avatars: string[];
  selectedAvatar: string | null;
  onSelectAvatar?: (avatarUrl: string) => void;
  onPreviewChange: (avatarUrl: string) => void;
}

const AvatarPreviewModal: React.FC<AvatarPreviewModalProps> = ({ isOpen, onClose, avatars, selectedAvatar, onSelectAvatar, onPreviewChange }) => {
  const [currentPreviewAvatar, setCurrentPreviewAvatar] = useState<string | null>(selectedAvatar);

  useEffect(() => {
    setCurrentPreviewAvatar(selectedAvatar);
  }, [selectedAvatar]);

  if (!isOpen || !currentPreviewAvatar) return null;

  const currentIndex = avatars.indexOf(currentPreviewAvatar);

  const handleNext = () => {
    const nextIndex = (currentIndex + 1) % avatars.length;
    const nextAvatar = avatars[nextIndex];
    setCurrentPreviewAvatar(nextAvatar);
    onPreviewChange(nextAvatar);
  };

  const handlePrevious = () => {
    const previousIndex = (currentIndex - 1 + avatars.length) % avatars.length;
    const prevAvatar = avatars[previousIndex];
    setCurrentPreviewAvatar(prevAvatar);
    onPreviewChange(prevAvatar);
  };

  const modalContent = (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
      style={{ zIndex: 9999 }}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg p-6 w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">Aperçu de l'avatar</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Fermer"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <div className="relative flex items-center justify-center mb-4">
          <button
            onClick={handlePrevious}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 p-2 bg-[#FF6B2C] hover:bg-[#FF7A35] transition-colors flex items-center justify-center rounded"
            aria-label="Avatar précédent"
            style={{ width: '40px', height: '40px', border: '1px solid rgba(0,0,0,0.1)', opacity: 0.8 }}
          >
            <ArrowLeft className="h-5 w-5 text-white" />
          </button>
          <img
            src={DOMPurify.sanitize(currentPreviewAvatar)}
            alt="Aperçu de l'avatar"
            className="w-full h-auto max-h-64 object-contain rounded-lg"
          />
          <button
            onClick={handleNext}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 p-2 bg-[#FF6B2C] hover:bg-[#FF7A35] transition-colors flex items-center justify-center rounded"
            aria-label="Avatar suivant"
            style={{ width: '40px', height: '40px', border: '1px solid rgba(0,0,0,0.1)', opacity: 0.8 }}
          >
            <ArrowRight className="h-5 w-5 text-white" />
          </button>
        </div>
        <div className="flex flex-col gap-2">
          <button onClick={() => {
            if (onSelectAvatar) {
              onSelectAvatar(currentPreviewAvatar);
            }
            onClose();
          }} className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors">
            Définir cet avatar
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <ModalPortal>
      {modalContent}
    </ModalPortal>
  );
};

export default AvatarPreviewModal;