import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useFormValidation, validationRules } from '../../hooks/useFormValidation';
import { FormField } from '../../components/FormField';
import LoadingBar from '../../components/LoadingBar';
import { Lock } from 'lucide-react';
import DOMPurify from 'dompurify';
import { logger } from '../../utils/logger';
import { notify } from '@/components/Notification';


const UpdatePassword = () => {
  logger.info('🔄 UpdatePassword component mounted');
  const navigate = useNavigate();
  const { updatePassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const validationRulesConfig = {
    currentPassword: [
      validationRules.required('Le mot de passe actuel est requis'),
    ],
    newPassword: [
      validationRules.required('Le nouveau mot de passe est requis'),
      validationRules.password()
    ],
    confirmPassword: [
      validationRules.required('La confirmation du mot de passe est requise'),
      (value: string) => ({
        isValid: value === formData.newPassword,
        message: 'Les mots de passe ne correspondent pas'
      })
    ]
  };

  const {
    errors,
    validateForm,
    handleBlur,
    handleChange,
    touched,
  } = useFormValidation(validationRulesConfig);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const sanitizedValue = DOMPurify.sanitize(value);
    setFormData(prev => ({ ...prev, [name]: sanitizedValue }));
    handleChange(name, sanitizedValue);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    logger.info('🔵 Password update form submission started');
    e.preventDefault();
    
    if (!validateForm(formData)) {
      notify('Veuillez corriger les erreurs du formulaire', 'error');
      return;
    }

    const sanitizedCurrentPassword = DOMPurify.sanitize(formData.currentPassword);
    const sanitizedNewPassword = DOMPurify.sanitize(formData.newPassword);

    setLoading(true);
    try {
      logger.info('🔑 Calling update password function');
      const result = await updatePassword(sanitizedCurrentPassword, sanitizedNewPassword);
      logger.info('📬 Password update result:', result);

      notify('Mot de passe mis à jour avec succès', 'success');
      navigate('/profil');
    } catch (error: any) {
      logger.error('❌ Password update error:', error);
      logger.error('Stack trace:', error.stack);
    } finally {
      setLoading(false);
    }
  };

  const helpTexts = {
    currentPassword: 'Entrez votre mot de passe actuel',
    newPassword: 'Choisissez un nouveau mot de passe fort',
    confirmPassword: 'Confirmez votre nouveau mot de passe'
  };

  if (loading) {
    return (
      <LoadingBar
        title="Mise à jour du mot de passe"
        subtitle="Veuillez patienter pendant que nous mettons à jour votre mot de passe..."
        icon="Lock"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <title>Modifier le Mot de Passe - JobPartiel.fr</title>
      <meta name="description" content="Mettez à jour votre mot de passe sur JobPartiel.fr." />
      <meta name="keywords" content="mot de passe, mise à jour, sécurité, JobPartiel.fr" />
      <meta name="robots" content="index, follow" />
      <meta property="og:title" content="Modifier le Mot de Passe - JobPartiel.fr" />
      <meta property="og:description" content="Mettez à jour votre mot de passe sur JobPartiel.fr." />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://jobpartiel.fr/update-password" />
      <meta property="og:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Lock className="h-12 w-12 text-primary" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Modifier votre mot de passe
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Mot de passe actuel"
              name="currentPassword"
              type="password"
              value={DOMPurify.sanitize(formData.currentPassword)}
              onChange={handleInputChange}
              onBlur={(e) => handleBlur('currentPassword', e ? e.target.value : formData.currentPassword)}
              errors={errors.currentPassword}
              touched={touched.currentPassword}
              helpText={helpTexts.currentPassword}
              required
              autoComplete="current-password"
            />

            <FormField
              label="Nouveau mot de passe"
              name="newPassword"
              type="password"
              value={DOMPurify.sanitize(formData.newPassword)}
              onChange={handleInputChange}
              onBlur={(e) => handleBlur('newPassword', e ? e.target.value : formData.newPassword)}
              errors={errors.newPassword}
              touched={touched.newPassword}
              helpText={helpTexts.newPassword}
              required
              autoComplete="new-password"
            />

            <FormField
              label="Confirmer le nouveau mot de passe"
              name="confirmPassword"
              type="password"
              value={DOMPurify.sanitize(formData.confirmPassword)}
              onChange={handleInputChange}
              onBlur={(e) => handleBlur('confirmPassword', e ? e.target.value : formData.confirmPassword)}
              errors={errors.confirmPassword}
              touched={touched.confirmPassword}
              helpText={helpTexts.confirmPassword}
              required
              autoComplete="new-password"
            />

            <div>
              <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Mettre à jour le mot de passe
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UpdatePassword;
