import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Container, 
  Paper, 
  Typography, 
  Button, 
  Box, 
  Alert
} from '@mui/material';
import { CheckCircle, Home } from '@mui/icons-material';

const AccountDeleted: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 6, borderRadius: 2, textAlign: 'center' }}>
        <Box sx={{ mb: 4 }}>
          <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 3 }} />
          <Typography variant="h3" component="h1" gutterBottom color="success.main">
            Compte supprimé avec succès
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
            Vos données personnelles ont été anonymisées conformément au RGPD
          </Typography>
        </Box>

        <Alert severity="success" sx={{ mb: 4, textAlign: 'left' }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Suppression terminée :</strong>
          </Typography>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>Vos données personnelles ont été anonymisées</li>
            <li>Votre profil n'est plus visible publiquement</li>
            <li>Vous avez été déconnecté de tous vos appareils</li>
            <li>Vos sessions ont été invalidées</li>
          </ul>
        </Alert>

        <Alert severity="info" sx={{ mb: 4, textAlign: 'left' }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Données conservées (anonymisées) :</strong>
          </Typography>
          <Typography variant="body2">
            Conformément aux obligations légales, certaines données ont été conservées 
            de manière anonyme (transactions, avis) pour une durée limitée. Ces données 
            ne permettent plus de vous identifier.
          </Typography>
        </Alert>

        <Box sx={{ mb: 4 }}>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Merci d'avoir utilisé JobPartiel. Nous espérons que notre service vous a été utile.
          </Typography>
          
          <Typography variant="body2" color="text.secondary">
            Si vous souhaitez revenir, vous pourrez créer un nouveau compte à tout moment 
            avec une nouvelle adresse email.
          </Typography>
        </Box>

        <Button
          variant="contained"
          size="large"
          onClick={handleGoHome}
          startIcon={<Home />}
          sx={{ 
            mt: 2,
            px: 4,
            py: 1.5,
            fontSize: '1.1rem'
          }}
        >
          Retour à l'accueil
        </Button>

        <Box sx={{ mt: 4, pt: 3, borderTop: '1px solid #eee' }}>
          <Typography variant="caption" color="text.secondary">
            Pour toute question concernant la suppression de vos données, 
            contactez notre support à <EMAIL>
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default AccountDeleted;
