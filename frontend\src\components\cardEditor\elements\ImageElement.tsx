import React, { useEffect, useState } from 'react';
import { Image, Transformer } from 'react-konva';
import type { KonvaEventObject } from 'konva/lib/Node';
import Konva from 'konva';
import { ImageElement } from '../../../types/cardEditor';

interface ImageElementProps {
  element: ImageElement;
  isSelected: boolean;
  onSelect: () => void;
  onDragStart?: (e: KonvaEventObject<DragEvent>) => void;
  onDragMove?: (e: KonvaEventObject<DragEvent>) => void;
  onDragEnd: (e: KonvaEventObject<DragEvent>) => void;
  isEditable?: boolean;
  onContextMenu?: (e: any) => void;
  isCtrlPressed: boolean;
}

const ImageElementComponent: React.FC<ImageElementProps> = ({
  element,
  isSelected,
  onSelect,
  onDragStart,
  onDragMove = () => {},
  onDragEnd = () => {},
  isEditable = true,
  onContextMenu,
  isCtrlPressed
}) => {
  const imageRef = React.useRef<any>(null);
  const transformerRef = React.useRef<any>(null);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [noFilterKey, setNoFilterKey] = useState(0);
  const prevFilterCount = React.useRef((element.properties.filters ?? []).length);
  const [isHovered, setIsHovered] = useState(false);

  // Charger l'image
  useEffect(() => {
    if (element.properties.src) {
      // Si plus de filtre, forcer un reload natif de l'image pour casser tout cache
      if (!element.properties.filters || element.properties.filters.length === 0) {
        const img = new window.Image();
        img.crossOrigin = 'anonymous';
        // On ajoute un paramètre unique pour forcer le reload
        img.src = element.properties.src + (element.properties.src.includes('?') ? '&' : '?') + 'reload=' + Date.now();
        img.onload = () => {
          setImage(img);
        };
        img.onerror = () => {
          console.error('Erreur lors du rechargement natif de l\'image:', element.properties.src);
        };
      } else {
        const img = new window.Image();
        img.crossOrigin = 'anonymous';
        img.src = element.properties.src;
        img.onload = () => {
          setImage(img);
        };
        img.onerror = () => {
          console.error('Erreur lors du chargement de l\'image:', element.properties.src);
        };
      }
    }
  }, [element.properties.src, element.properties.filters]);

  // Attacher le transformer à l'image si elle est sélectionnée
  useEffect(() => {
    if (isSelected && transformerRef.current && imageRef.current) {
      transformerRef.current.nodes([imageRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  // Construction des filtres et de leurs props
  const konvaFilters = (element.properties.filters || []).map(filter => {
    switch (filter.type) {
      case 'blur': return Konva.Filters.Blur;
      case 'brighten': return Konva.Filters.Brighten;
      case 'contrast': return Konva.Filters.Contrast;
      case 'emboss': return Konva.Filters.Emboss;
      case 'enhance': return Konva.Filters.Enhance;
      case 'grayscale': return Konva.Filters.Grayscale;
      case 'hsl': return Konva.Filters.HSL;
      case 'hsv': return Konva.Filters.HSV;
      case 'invert': return Konva.Filters.Invert;
      case 'kaleidoscope': return Konva.Filters.Kaleidoscope;
      case 'mask': return Konva.Filters.Mask;
      case 'noise': return Konva.Filters.Noise;
      case 'pixelate': return Konva.Filters.Pixelate;
      case 'rgb': return Konva.Filters.RGB;
      case 'sepia': return Konva.Filters.Sepia;
      case 'solarize': return Konva.Filters.Solarize;
      case 'threshold': return Konva.Filters.Threshold;
      default: return null;
    }
  }).filter(Boolean);

  // Fusionner tous les paramètres de filtres en un seul objet props
  const filterProps = (element.properties.filters || []).reduce((acc, filter) => {
    if (filter.params) {
      Object.entries(filter.params).forEach(([key, value]) => {
        acc[key] = value;
      });
    }
    return acc;
  }, {} as Record<string, any>);

  const handleTransformEnd = (e: any) => {
    if (!imageRef.current) return;
    const node = imageRef.current;
    let newRotation = node.rotation();
    newRotation = ((newRotation % 360) + 360) % 360;
    if (onDragEnd) {
      onDragEnd({
        ...e,
        target: {
          ...node,
          rotation: () => newRotation,
          width: () => node.width(),
          height: () => node.height()
        }
      });
    }
  };

  // Appliquer les filtres à l'image
  useEffect(() => {
    if (imageRef.current) {
      // Toujours nettoyer le cache avant toute opération
      if (imageRef.current.clearCache) {
        imageRef.current.clearCache();
      }
      if ((element.properties.filters || []).length > 0) {
        imageRef.current.cache();
        imageRef.current.filters(konvaFilters as any);
      } else {
        // Forcer la suppression de tous les filtres et le redraw natif
        imageRef.current.filters([]);
        if (imageRef.current.image && image) {
          imageRef.current.image(image);
        }
        if (imageRef.current.clearCache) {
          imageRef.current.clearCache();
        }
        if (imageRef.current.getLayer()) {
          imageRef.current.getLayer().batchDraw();
        }
      }
      if (imageRef.current.getLayer()) {
        imageRef.current.getLayer().batchDraw();
      }
      // Avertir si l'image est agrandie au-delà de sa taille d'origine
      if (image) {
        const displayWidth = element.width ?? 0;
        const displayHeight = element.height ?? 0;
        if ((displayWidth > image.naturalWidth) || (displayHeight > image.naturalHeight)) {
          console.warn("L'image est affichée à une taille supérieure à sa résolution d'origine, elle risque d'être pixelisée.");
        }
      }
    }
  }, [image, element.properties.filters, element.properties.cornerRadius, element.width, element.height]);

  useEffect(() => {
    const currentCount = (element.properties.filters ?? []).length;
    if ((prevFilterCount.current > 0 && currentCount === 0) || (prevFilterCount.current === 0 && currentCount > 0)) {
      setNoFilterKey(Date.now());
    }
    prevFilterCount.current = currentCount;
  }, [element.properties.filters && (element.properties.filters.length === 0 || element.properties.filters.length === 1)]);

  function getImageKey(element: ImageElement) {
    const filters = element.properties.filters ?? [];
    if (filters.length === 0) {
      return 'nofilter-' + noFilterKey;
    }
    return 'withfilter-' + noFilterKey;
  }

  // Gérer le survol
  const handleMouseEnter = () => {
    if (isEditable && !isSelected && imageRef.current) {
      setIsHovered(true);
      document.body.style.cursor = 'pointer';
      // Appliquer l'effet visuel de survol (contour)
      imageRef.current.stroke('#0096FF'); // Couleur du contour
      imageRef.current.strokeWidth(2); // Épaisseur du contour
      imageRef.current.getLayer()?.batchDraw();
    }
  };

  const handleMouseLeave = () => {
    if (isEditable && !isSelected && imageRef.current) {
      setIsHovered(false);
      document.body.style.cursor = 'default';
      // Retirer l'effet visuel de survol (contour)
      imageRef.current.stroke(undefined); // Retirer la couleur du contour
      imageRef.current.strokeWidth(0); // Retirer l'épaisseur du contour
      imageRef.current.getLayer()?.batchDraw();
    }
  };

  if (!image) {
    return null;
  }

  // Propriétés de l'ombre personnalisée
  const customShadowProps = element.properties.shadow ? {
    shadowColor: element.properties.shadow.color,
    shadowBlur: element.properties.shadow.blur,
    shadowOffsetX: element.properties.shadow.offsetX,
    shadowOffsetY: element.properties.shadow.offsetY,
    shadowOpacity: element.properties.shadow.opacity,
  } : {};

  return (
    <>
      <Image
        key={getImageKey(element)}
        ref={imageRef}
        image={image}
        x={element.x}
        y={element.y}
        width={element.width}
        height={element.height}
        draggable={isEditable}
        rotation={element.rotation}
        onClick={onSelect}
        onTap={onSelect}
        onDragStart={onDragStart}
        onDragMove={onDragMove}
        onDragEnd={onDragEnd}
        perfectDrawEnabled={false}
        onTransformEnd={handleTransformEnd}
        cornerRadius={element.properties.cornerRadius || 0}
        visible={element.properties.visible !== undefined ? element.properties.visible : true}
        filters={konvaFilters as any[]}
        {...filterProps}
        {...customShadowProps}
        onContextMenu={onContextMenu}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        name={element.name || 'image'}
      />
      {isSelected && isEditable && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox: any, newBox: any) => {
            // Limiter la taille minimale
            if (newBox.width < 10 || newBox.height < 10) {
              return oldBox;
            }
            return newBox;
          }}
          rotateEnabled={true}
          enabledAnchors={
            isCtrlPressed
              ? [
                  'top-left', 'top-right', 'bottom-left', 'bottom-right',
                  'middle-left', 'middle-right', 'top-center', 'bottom-center'
                ]
              : [
                  'top-left', 'top-right', 'bottom-left', 'bottom-right'
                ]
          }
          borderStroke="#0096FF"
          borderStrokeWidth={2}
          anchorFill="#FFFFFF"
          anchorStroke="#0096FF"
          anchorStrokeWidth={2}
          anchorSize={8}
          keepRatio={isCtrlPressed ? false : true}
          onTransformEnd={handleTransformEnd}
        />
      )}
    </>
  );
};

export default ImageElementComponent;
