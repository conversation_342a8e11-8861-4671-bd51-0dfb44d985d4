import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { decryptProfilDataAsync } from '../utils/encryption';

/**
 * Middleware qui vérifie si le profil d'un utilisateur est visible (non masqué)
 * Si le profil est masqué, l'utilisateur ne peut pas effectuer d'actions sur la plateforme
 */
export const checkProfileVisibility = (req: Request, res: Response, next: NextFunction) => {
  const checkVisibility = async () => {
    try {
      // Vérifier si l'utilisateur est authentifié
      if (!req.user?.userId) {
        return next();
      }

      const userId = req.user.userId;
      const cacheKey = `profile_visibility:${userId}`;
      const CACHE_TTL = 600; // 10 minutes

      // Vérifier dans le cache Redis
      const cached = await redis.get(cacheKey);
      if (cached !== null) {
        const profil_visible = JSON.parse(cached);
        if (profil_visible === false) {
          return res.status(403).json({
            success: false,
            message: 'Votre profil est actuellement masqué. Vous devez mettre à jour votre profil et demander une revalidation via un ticket support pour pouvoir effectuer cette action.',
            profileHidden: true,
            toastType: 'error'
          });
        }
        return next();
      }

      // Récupérer le statut de visibilité du profil depuis Supabase
      const { data: userProfil, error } = await supabase
        .from('user_profil')
        .select('profil_visible')
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Erreur lors de la vérification de la visibilité du profil:', error);
        return next();
      }

      // Si le profil n'existe pas ou si profil_visible est null, on laisse passer
      if (!userProfil) {
        // On met tout de même en cache la valeur null pour éviter les requêtes répétées
        await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(null));
        return next();
      }

      // Déchiffrer les données du profil si nécessaire
      const decryptedUserProfil = await decryptProfilDataAsync(userProfil);

      // Mettre à jour le cache Redis
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(decryptedUserProfil.profil_visible));

      // Si le profil est masqué, on bloque l'action
      if (decryptedUserProfil.profil_visible === false) {
        return res.status(403).json({
          success: false,
          message: 'Votre profil est actuellement masqué. Vous devez mettre à jour votre profil et demander une revalidation via un ticket support pour pouvoir effectuer cette action.',
          profileHidden: true,
          toastType: 'error'
        });
      }

      // Si le profil est visible, on continue
      next();
    } catch (error) {
      logger.error('Erreur lors de la vérification de la visibilité du profil:', error);
      next();
    }
  };

  checkVisibility();
};

export default {
  checkProfileVisibility
};
