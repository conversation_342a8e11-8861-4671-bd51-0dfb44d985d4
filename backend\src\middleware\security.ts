import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import jwt from 'jsonwebtoken';
import { TokenBlacklist } from '../services/tokenBlacklist';
import { SessionManager } from '../services/sessionManager';
import FailedAttemptsService from '../services/failedAttempts';
import { csrfProtection, setCSRFToken } from './csrf';
import { ConnectionLoggerService } from '../services/connectionLogger';
import { SuspiciousActivityDetector } from '../services/suspiciousActivityDetector';
import hpp from 'hpp';
import { randomBytes } from 'crypto';
import { redis } from '../config/redis';
import { tokenService } from '../services/tokenService';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';


export interface SecurityEvent {
  userId?: string; // ID de l'utilisateur (optionnel)
  action: string; // Action effectuée
  status: 'success' | 'failure'; // Statut de l'action
  ip: string; // Adresse IP
  details?: any; // Détails supplémentaires
  type: string; // Assurez-vous que cette ligne est présente
  message: string; // Assurez-vous que cette ligne est présente
}
const logSecurityEvent = async (event: SecurityEvent) => {
  try {
    const { data, error } = await supabase.from('security_logs').insert({
      type: event.type,
      severity: event.status === 'failure' ? 'high' : 'low',
      user_id: event.userId,
      ip_address: event.ip,
      user_agent: event.details?.userAgent,
      message: event.message, // Ajouter le message directement
      details: {
        action: event.action,
        ...event.details
      }
    });

    if (error) {
      console.error('Erreur lors de l\'enregistrement du log de sécurité:', error);
    }
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du log de sécurité:', error);
  }
};

// Configuration avancée de Helmet avec sécurité renforcée
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'", "https://accounts.google.com", "https://*.jobpartiel.fr"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://accounts.google.com", "https://*.jobpartiel.fr"],
      imgSrc: ["'self'", 'data:', 'https:', "https://*.jobpartiel.fr", "https://*.openstreetmap.org"],
      connectSrc: [
        "'self'",
        'https://api.jobpartiel.fr',
        'https://dev-api.jobpartiel.fr',
        'wss://api.jobpartiel.fr',
        'wss://dev-api.jobpartiel.fr',
        'wss://ws.jobpartiel.fr',
        'https://api-adresse.data.gouv.fr',
        'https://developers.hostinger.com',
        'https://accounts.google.com',
        'https://*.jobpartiel.fr'
      ],
      fontSrc: ["'self'", 'data:', 'https:', "https://*.jobpartiel.fr"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "https://*.jobpartiel.fr"],
      workerSrc: ["'self'", "blob:", "data:"],
      frameSrc: ["'self'", "https://accounts.google.com"],
      frameAncestors: ["'none'"],
      formAction: ["'self'", "https://accounts.google.com", "https://*.jobpartiel.fr"],
      upgradeInsecureRequests: [],
      blockAllMixedContent: [],
      baseUri: ["'self'"],
      manifestSrc: ["'self'"],
      reportUri: "https://jobpartiel.fr/report-uri/csp-violation"
    }
  },
  // Désactivé pour permettre l'intégration avec des services tiers
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" as const },
  crossOriginResourcePolicy: { policy: "cross-origin" as const },
  dnsPrefetchControl: { allow: false },
  expectCt: {
    enforce: true,
    maxAge: 86400, // 1 jour
    reportUri: "https://jobpartiel.fr/report-uri/expect-ct"
  },
  frameguard: { action: "sameorigin" as const },
  hidePoweredBy: true,
  hsts: {
    maxAge: ********, // 1 an
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: { permittedPolicies: "none" as const },
  referrerPolicy: { policy: "strict-origin-when-cross-origin" as const },
  xssFilter: true
};

// Rate limiting global avec configuration adaptée et sécurité renforcée
export const limiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 1 * 60 * 1000 : 5 * 60 * 1000, // 1 minute en dev, 5 minutes en prod
  max: process.env.NODE_ENV === 'development' ? 3000 : 100, // SÉCURITÉ: Limite réduite en production
  message: { error: 'Trop de requêtes, veuillez réessayer plus tard' },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Ne compte pas les requêtes réussies
  // SÉCURITÉ: Utiliser l'IP réelle même derrière un proxy
  keyGenerator: (req: Request) => {
    return req.ip || req.socket.remoteAddress || 'unknown';
  },
  // SÉCURITÉ: Ignorer certaines routes critiques du rate limiting global
  skip: (req: Request) => {
    const skipPaths = ['/api/health', '/api/auth/csrf', '/api/auth/socket/token'];
    return skipPaths.includes(req.path);
  },
  handler: (req: Request, res: Response) => {
    const rateLimitEvent: SecurityEvent = {
      type: "RATE_LIMIT_EXCEEDED",
      message: "Trop de requêtes depuis cette IP",
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      action: 'rate_limit_exceeded',
      status: 'failure',
      details: {
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent']
      }
    };
    logSecurityEvent(rateLimitEvent);
    res.status(429).json({
      error: 'Trop de requêtes, veuillez réessayer plus tard',
      retryAfter: 300 // 5 minutes
    });
  }
});

// Rate limiter spécifique pour les routes CSRF
export const csrfLimiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 1 * 60 * 1000 : 15 * 60 * 1000,
  max: process.env.NODE_ENV === 'development' ? 5000 : 1000,
  message: { error: 'Trop de requêtes CSRF' },
  skipSuccessfulRequests: true
});

// Rate limiting spécifique pour l'authentification
export const authLimiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'development' ? 1 * 60 * 1000 : 60 * 60 * 1000, // 1 minute en dev, 60 minutes en prod
  max: process.env.NODE_ENV === 'development' ? 1000 : 20, // 1000 tentatives en dev, 20 en prod
  message: 'Trop de tentatives de connexion, veuillez réessayer plus tard',
  handler: (req: Request, res: Response) => {
    const rateLimitEvent: SecurityEvent = {
      type: "RATE_LIMIT_EXCEEDED",
      message: "Trop de tentatives de connexion",
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      action: 'rate_limit_exceeded',
      status: 'failure',
      details: {
        path: req.path,
        method: req.method
      }
    };
    logSecurityEvent(rateLimitEvent);
    res.status(429).json({
      error: 'Trop de tentatives de connexion, veuillez réessayer plus tard'
    });
  }
});

// Middleware de désinfection renforcé contre les injections
function sanitizeMongo(obj: any) {
  if (typeof obj !== 'object' || obj === null) return;
  for (const key of Object.keys(obj)) {
    if (key.startsWith('$') || key.includes('.')) {
      delete obj[key];
    } else {
      sanitizeMongo(obj[key]);
    }
  }
}

// SÉCURITÉ: Middleware de validation et sanitisation renforcé
function securitySanitizeMiddleware(req: Request, _res: Response, next: NextFunction) {
  try {
    // Sanitisation MongoDB
    if (req.body) sanitizeMongo(req.body);
    if (req.params) sanitizeMongo(req.params);
    if (req.query) sanitizeMongo(req.query);

    // SÉCURITÉ: Validation des tailles de payload
    const bodySize = JSON.stringify(req.body || {}).length;
    if (bodySize > 2048 * 1024) { // 2MB max
      throw new Error('Payload trop volumineux');
    }

    // SÉCURITÉ: Validation des headers suspects
    const suspiciousHeaders = ['x-forwarded-host', 'x-real-ip'];
    for (const header of suspiciousHeaders) {
      if (req.headers[header] && typeof req.headers[header] === 'string') {
        const headerValue = req.headers[header] as string;
        if (headerValue.includes('<script>') || headerValue.includes('javascript:')) {
          throw new Error('Header suspect détecté');
        }
      }
    }

    next();
  } catch (error) {
    const securityEvent: SecurityEvent = {
      type: "SECURITY_VALIDATION_FAILED",
      message: "Validation de sécurité échouée",
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      action: 'security_validation',
      status: 'failure',
      details: {
        error: error instanceof Error ? error.message : String(error),
        path: req.path,
        method: req.method
      }
    };
    logSecurityEvent(securityEvent);

    _res.status(400).json({
      error: 'Requête invalide',
      message: 'Les données envoyées ne respectent pas les critères de sécurité'
    });
  }
}

// Middleware de vérification des IPs bloquées (manuel, permanent, etc.)
export const blockedIPCheck = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Exclure certaines routes critiques du blocage IP
    const excludedPaths = ['/api/health'];
    if (excludedPaths.includes(req.path)) {
      return next();
    }

    const clientIP = req.ip || req.socket.remoteAddress || 'unknown';

    // Vérifier tous les types de blocage
    const [manualBlock, permanentBlock] = await Promise.all([
      redis.get(`blocked_manual:${clientIP}`),
      redis.get(`blocked_permanent:${clientIP}`)
    ]);

    if (permanentBlock) {
      const blockData = JSON.parse(permanentBlock);

      // Log de l'événement
      const securityEvent: SecurityEvent = {
        type: "IP_BLOCKED_PERMANENT",
        message: "Accès refusé - IP bloquée de façon permanente",
        ip: clientIP,
        action: 'ip_blocked_permanent',
        status: 'failure',
        details: {
          path: req.path,
          method: req.method,
          reason: blockData.reason || 'Blocage permanent',
          blocked_at: blockData.blockedAt,
          admin_user: blockData.admin_user
        }
      };
      logSecurityEvent(securityEvent);

      return res.status(403).json({
        error: 'Accès refusé',
        message: 'Votre adresse IP a été bloquée de façon permanente.',
        reason: blockData.reason || 'Violation des conditions d\'utilisation',
        contact: 'Contactez l\'administrateur si vous pensez qu\'il s\'agit d\'une erreur.'
      });
    }

    if (manualBlock) {
      const blockData = JSON.parse(manualBlock);

      // Log de l'événement
      const securityEvent: SecurityEvent = {
        type: "IP_BLOCKED_MANUAL",
        message: "Accès refusé - IP bloquée temporairement",
        ip: clientIP,
        action: 'ip_blocked_manual',
        status: 'failure',
        details: {
          path: req.path,
          method: req.method,
          reason: blockData.reason || 'Blocage temporaire',
          blocked_at: blockData.blockedAt,
          expires_at: blockData.expiresAt,
          admin_user: blockData.admin_user
        }
      };
      logSecurityEvent(securityEvent);

      const expiresAt = blockData.expiresAt ? new Date(blockData.expiresAt) : null;
      const remainingTime = expiresAt ? Math.max(0, Math.floor((expiresAt.getTime() - Date.now()) / 1000)) : 0;

      return res.status(403).json({
        error: 'Accès temporairement refusé',
        message: 'Votre adresse IP a été temporairement bloquée.',
        reason: blockData.reason || 'Activité suspecte détectée',
        expiresAt: expiresAt?.toISOString(),
        remainingTime: remainingTime,
        retryAfter: remainingTime
      });
    }

    next();
  } catch (error) {
    // En cas d'erreur, on laisse passer pour ne pas bloquer le service
    console.error('Erreur lors de la vérification des IPs bloquées:', error);
    next();
  }
};

// Protection contre les attaques par force brute améliorée : Trop de tentatives de connexion échouées, Trop de requêtes sur une API protégée, Comportement suspect détecté, Tentatives d'accès non autorisées répétées
export const bruteForceProtection = async (req: Request, res: Response, next: NextFunction) => {
  // Exclure la route /api/health des protections force brute pour ne pas bloquer le serveur !
  if (req.path === '/api/health') {
    return next();
  }

  try {
    // Si la réponse a déjà été envoyée, ne rien faire
    if (res.headersSent) {
      return next();
    }

    const path = req.path;
    const method = req.method;
    const key = `${req.ip}:${path}`;

    // Vérifier si l'IP est déjà bloquée
    const isBlocked = await redis.get(`blocked_brute_force_1:${key}`);
    if (isBlocked) {
      const bruteForceEvent: SecurityEvent = {
        type: "BRUTE_FORCE_BLOCKED",
        message: "Accès temporairement bloqué, veuillez réessayer plus tard.",
        ip: req.ip || req.socket.remoteAddress || 'unknown',
        action: 'brute_force_blocked',
        status: 'failure',
        details: { path, method }
      };
      logSecurityEvent(bruteForceEvent);

      return res.status(403).json({
        error: 'Accès temporairement bloqué, veuillez réessayer plus tard.',
        message: "Accès temporairement bloqué, veuillez réessayer plus tard.",
        retryAfter: 3600
      });
    }

    // Incrémenter le compteur de tentatives
    const attempts = await redis.incr(key);
    await redis.expire(key, 60); // Expire après 60 secondes

    const limit = process.env.NODE_ENV === 'development' ? 6000 : 250;
    if (attempts > limit) {
      // Bloquer l'IP pour une heure
      await redis.set(`blocked_brute_force_1:${key}`, '1', 'EX', 3600);

      const bruteForceEvent: SecurityEvent = {
        type: "BRUTE_FORCE_BLOCKED",
        message: "Accès temporairement bloqué, veuillez réessayer plus tard.",
        ip: req.ip || req.socket.remoteAddress || 'unknown',
        action: 'brute_force_blocked',
        status: 'failure',
        details: { path, method, attempts }
      };
      logSecurityEvent(bruteForceEvent);

      return res.status(403).json({
        error: 'Accès temporairement bloqué, veuillez réessayer plus tard.',
        message: "Accès temporairement bloqué, veuillez réessayer plus tard.",
        retryAfter: 3600
      });
    }

    // Vérifier l'activité suspecte seulement si aucune réponse n'a été envoyée
    if (!res.headersSent) {
      const isSuspicious = await SuspiciousActivityDetector.check(req, res);
      if (isSuspicious) {
        const suspiciousActivityEvent: SecurityEvent = {
          type: "SUSPICIOUS_ACTIVITY_DETECTED_BRUTE_FORCE",
          message: "Activité suspecte détectée",
          ip: req.ip || req.socket.remoteAddress || 'unknown',
          action: 'suspicious_activity_detected',
          status: 'failure',
          details: { path, method, pattern: isSuspicious }
        };
        logSecurityEvent(suspiciousActivityEvent);

        await supabase.from('security_logs').insert({
          type: "SUSPICIOUS_ACTIVITY_DETECTED_BRUTE_FORCE",
          severity: 'high',
          ip_address: req.ip,
          message: "Activité suspecte de brute force détectée",
          details: {
            attempts: attempts,
            userAgent: req.headers['user-agent'],
            path: req.path
          }
        });

        return res.status(403).json({
          error: 'Activité suspecte détectée, veuillez réessayer plus tard.',
          message: "Activité suspecte détectée, veuillez réessayer plus tard.",
        });
      }
    }

    next();
  } catch (error) {
    // Ne pas envoyer d'erreur si la réponse a déjà été envoyée
    if (!res.headersSent) {
      const errorEvent: SecurityEvent = {
        type: "ERROR_EVENT",
        message: "Erreur lors de la vérification de sécurité",
        ip: req.ip || req.socket.remoteAddress || 'unknown',
        action: 'security_check',
        status: 'failure',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
      logSecurityEvent(errorEvent);
      next(error);
    }
  }
};

// Middleware de sécurité amélioré
export const securityMiddleware = [
  // PRIORITÉ 1: Vérification des IPs bloquées (doit être en premier)
  blockedIPCheck,

  // Configuration de base
  helmet(helmetConfig),
  compression(),
  cookieParser(),
  securitySanitizeMiddleware, // Utiliser le nouveau middleware de sécurité
  hpp(),

  // Protection CSRF avec exclusion pour /api/health
  (req: Request, res: Response, next: NextFunction) => {
    if (req.path === '/api/health') {
      return next();
    }
    setCSRFToken(req, res, next);
  },
  (req: Request, res: Response, next: NextFunction) => {
    if (req.path === '/api/health') {
      return next();
    }
    csrfProtection(req, res, next);
  },

  // Rate limiting et protection force brute
  limiter,
  bruteForceProtection,

  // Middleware personnalisé pour la sécurité supplémentaire
  (_req: Request, res: Response, next: NextFunction) => {
    // Vérifier si les headers ont déjà été envoyés avant d'ajouter des headers de sécurité
    if (!res.headersSent) {
      res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
      res.setHeader('X-Download-Options', 'noopen');

      // Générer un nonce unique pour chaque requête
      const nonce = randomBytes(16).toString('base64');
      res.locals.nonce = nonce;

      // Ajouter des en-têtes de sécurité dynamiques
      const csp = helmetConfig.contentSecurityPolicy.directives;
      csp.scriptSrc.push(`'nonce-${nonce}'`);
    }
    next();
  }
];

// Middleware d'authentification amélioré
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.cookies['access_token'];
    if (!token) {
      throw new Error('Token manquant');
    }

    const result = await tokenService.verifyToken(token, 'access');
    if (!result.isValid || !result.decoded) {
      throw new Error(result.error || 'Token invalide');
    }

    // Mettre à jour l'activité de la session
    if (req.session?.id) {
      await SessionManager.getInstance().refreshSession(req.session.id);
    }

    // Assigner l'utilisateur avec la structure correcte
    req.user = {
      id: result.decoded.userId,
      userId: result.decoded.userId,
      email: result.decoded.email,
      role: result.decoded.role || 'jobutil',
      user_type: result.decoded.userType || 'jobbeur'
    };

    // Logger la connexion
    await ConnectionLoggerService.logConnection({
      userId: result.decoded.userId,
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      status: 'success',
      location: req.headers['x-forwarded-for']?.toString() || req.ip,
      device: req.headers['user-agent'],
      timestamp: Date.now()
    });

    // Analyser les comportements suspects
    await SuspiciousActivityDetector.analyzeConnection({
      userId: result.decoded.userId,
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      status: 'success',
      location: req.headers['x-forwarded-for']?.toString() || req.ip
    });

    next();
  } catch (error) {
    const securityEvent: SecurityEvent = {
      type: "AUTHENTICATION_FAILURE",
      message: "Échec de l'authentification",
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      action: "login",
      status: "failure",
      details: {
        path: req.path,
        method: req.method,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      },
    };
    console.info('security', securityEvent);

    // Logger la tentative échouée
    await ConnectionLoggerService.logConnection({
      userId: '',
      ip: req.ip || req.socket.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      status: 'failure',
      location: req.headers['x-forwarded-for']?.toString() || req.ip,
      device: req.headers['user-agent'],
      timestamp: Date.now()
    });

    res.status(401).json({
      message: 'Non autorisé',
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    });
  }
};

// Middleware de gestion des erreurs avec support HTTP/2
export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  // Ne pas traiter l'erreur si la réponse a déjà été envoyée
  if (res.headersSent) {
    return next(err);
  }

  console.error(err.stack);

  // Gestion spécifique des erreurs de protocole HTTP/2
  if (err.code === 'ERR_HTTP2_PROTOCOL_ERROR' || err.message?.includes('HTTP2_PROTOCOL_ERROR')) {
    logger.warn('Erreur de protocole HTTP/2 détectée, fallback vers HTTP/1.1', {
      error: err.message,
      path: req.path,
      method: req.method,
      userAgent: req.headers['user-agent']
    });

    // Forcer la fermeture de la connexion pour permettre le fallback
    res.set('Connection', 'close');
    res.status(502).json({
      error: 'Erreur de protocole, veuillez réessayer',
      status: 502,
      timestamp: new Date().toISOString(),
      retry: true
    });
    return;
  }

  if (err.name === 'ValidationError') {
    res.status(400).json({
      message: 'Erreur de validation',
      errors: err.errors
    });
    return;
  }

  if (err.name === 'UnauthorizedError') {
    res.status(401).json({
      message: 'Non autorisé'
    });
    return;
  }

  const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';
  const securityEvent: SecurityEvent = {
    type: "ERROR_EVENT",
    message: "Erreur rencontrée",
    ip: ipAddress,
    action: "someAction",
    status: "failure",
    details: {
      error: err instanceof Error ? { error: err.message } : { error: String(err) }
    }
  };
  logSecurityEvent(securityEvent);

  // Ne pas exposer les détails de l'erreur en production
  if (process.env.NODE_ENV === 'production') {
    res.status(500).json({
      message: 'Une erreur interne est survenue'
    });
    return;
  } else {
    res.status(500).json({
      message: 'Une erreur interne est survenue',
      error: err.message,
      stack: err.stack
    });
    return;
  }
};

function verifyToken(token: string) {
  return jwt.verify(token, process.env.JWT_SECRET || 'default-secret', {
    algorithms: ['HS256'],
    issuer: process.env.JWT_ISSUER || 'jobpartiel-auth',
  });
}

// Nettoyage périodique des sessions et tokens
const cleanupInterval = 1000 * 60 * 60; // 1 heure
setInterval(async () => {
    await TokenBlacklist.cleanup();
    await SessionManager.cleanup();
}, cleanupInterval);
