import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Lock } from 'lucide-react';
import { createAuthService } from '../../services/auth';
import { logger } from '../../utils/logger';
import { AnimatedCard } from '../../components/Animations';
import { DynamicIcon } from '../../components/Animations';
import LoadingBar from '../../components/LoadingBar';
import VerifierForceMotDePasse from './VerifierForceMotDePasse';
import { validatePassword as checkPasswordStrength } from '../../utils/passwordValidator';
import { useNotification, NotificationType } from '../../components/Notification';
import { getCookie, removeCookie } from '../../utils/cookieUtils';
import DOMPurify from 'dompurify';

const ResetPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string>('');
  const navigate = useNavigate();
  const { notify } = useNotification(); 
  const authService = createAuthService((message: string, type: NotificationType) => notify(message, type));  
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [cooldown, setCooldown] = useState(0);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const token = searchParams.get('token');
    setToken(token);
  }, []);

  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setValidationMessage('Erreur lors de la validation du token. Veuillez réessayer.');
        setIsLoading(false);
        navigate('/forgot-password');
        return;
      }

      try {
        // Attendre la validation du token et un délai de 3 secondes en parallèle
        const [isValid] = await Promise.all([
          authService.validateResetToken(token),
          new Promise(resolve => setTimeout(resolve, 500))
        ]);
        
        if (isValid) {
          setIsTokenValid(true);
          setValidationMessage('Token valide. Vous pouvez définir votre nouveau mot de passe.');
          // Attendre 0.5 secondes avant d'afficher la notification
          await new Promise(resolve => setTimeout(resolve, 500));
          // Afficher la notification après le délai
          notify('Lien valide. Vous pouvez maintenant définir votre nouveau mot de passe', 'success');
        }
        
        setIsLoading(false);
      } catch (err: any) {
        // Attendre quand même 3 secondes avant d'afficher l'erreur
        await new Promise(resolve => setTimeout(resolve, 3000));
        setValidationMessage('Erreur lors de la validation du token. Veuillez réessayer.');
        setIsLoading(false);
        navigate('/forgot-password');
      }
    };

    if (token) {
      setValidationMessage('Nous vérifions la validité de votre lien de réinitialisation...');
      validateToken();
    }
  }, [token, navigate]);

  useEffect(() => {
    const checkCooldown = () => {
      const cooldownData = JSON.parse(getCookie('passwordResetCooldown') || 'null');
      
      if (cooldownData) {
        const remainingTime = Math.max(0, 
          cooldownData.expiresAt - Math.floor(Date.now() / 1000)
        );

        if (remainingTime > 0) {
          setCooldown(remainingTime);
          return remainingTime;
        } else {
          removeCookie('passwordResetCooldown');
        }
      }
      return 0;
    };

    const initialCooldown = checkCooldown();
    if (initialCooldown > 0) {
      startCooldownTimer(initialCooldown);
    }
  }, []); 

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (cooldown > 0) {
      intervalId = setInterval(() => {
        setCooldown(time => {
          if (time <= 1) {
            removeCookie('passwordResetCooldown');
          }
          return time - 1;
        });
      }, 1000);
    }
    return () => clearInterval(intervalId);
  }, [cooldown]);

  const startCooldownTimer = (initialTime: number) => {
    let remainingTime = initialTime;

    const timer = setInterval(() => {
      remainingTime--;
      setCooldown(remainingTime);

      if (remainingTime <= 0) {
        clearInterval(timer);
        removeCookie('passwordResetCooldown');
      }
    }, 1000);

    return () => clearInterval(timer);
  };

  const validatePassword = (password: string): { isValid: boolean; error: string } => {
    const validationResult = checkPasswordStrength(password);
    return {
      isValid: validationResult.score >= 2,
      error: validationResult.score < 2 ? validationResult.feedback.warning || validationResult.feedback.suggestions.join(', ') : ''
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const sanitizedPassword = DOMPurify.sanitize(password);
    const sanitizedConfirmPassword = DOMPurify.sanitize(confirmPassword);
    
    if (cooldown > 0) {
      setError(`Veuillez attendre ${Math.ceil(cooldown / 60)} minutes avant de réessayer`);
      return;
    }

    setError('');
    setIsLoading(true);

    // Validation plus stricte du mot de passe
    if (!sanitizedPassword || sanitizedPassword.length < 8) {
      setError('Le mot de passe doit contenir au moins 8 caractères');
      setIsLoading(false);
      return;
    }

    const passwordValidation = validatePassword(sanitizedPassword);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.error || 'Le mot de passe ne respecte pas les critères de sécurité');
      setIsLoading(false);
      return;
    }

    if (sanitizedPassword !== sanitizedConfirmPassword) {
      setError('Les mots de passe ne correspondent pas');
      setIsLoading(false);
      return;
    }

    try {
      if (!token) {
        throw new Error('Token manquant');
      }
      
      const result = await authService.resetPassword(token, sanitizedPassword);
      
      if (result.toastType === 'success') {
        setSuccess(true);
        navigate('/login');
      } else {
        setError(result.message || 'Une erreur est survenue');
      }
    } catch (err: any) {
      logger.error('Password reset error:', err);
      setError(err.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe');
    } finally {
      setIsLoading(false);
    }
  };

  // Si le token n'est pas valide, on affiche uniquement le message d'erreur
  if (!isTokenValid && !isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center">
        <AnimatedCard className="bg-white py-8 px-4 shadow-xl rounded-lg sm:px-10">
          <div className="bg-red-100 rounded text-[#FF7A35] mx-auto flex items-center justify-center" style={{ width: '48px', height: '48px' }}>
            <DynamicIcon name="Lock" size={24} className="text-[#FF7A35]" />
          </div>
          <h2 className="text-2xl font-bold text-center mt-4 mb-4">Réinitialisation du mot de passe</h2>
          <p className="text-center text-gray-600 mb-8">Le lien de réinitialisation est invalide. Demandez un nouveau lien de réinitialisation.</p>
          <button className="mt-4 w-full py-2 px-4 bg-[#FF7A35] text-white rounded hover:bg-[#F76A3B]" onClick={() => navigate('/forgot-password')}>Demander un nouveau lien</button>
        </AnimatedCard>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA]">
        <div className="max-w-md w-full mx-auto p-4 bg-white rounded-lg shadow-md">
          <LoadingBar
            title="Patience, nous vérifions votre lien de réinitialisation..."
            subtitle={validationMessage}
            icon="Lock"
          />
        </div>
      </div>
    );
  }

  if (isTokenValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center">
        <AnimatedCard className="bg-white py-8 px-4 shadow-xl rounded-lg sm:px-10">
          <div className="bg-red-100 rounded text-[#FF7A35] mx-auto flex items-center justify-center" style={{ width: '48px', height: '48px' }}>
            <DynamicIcon name="Lock" size={24} className="text-green-500" />
          </div>
          <h2 className="text-2xl font-bold text-center mt-4 mb-8">Réinitialisation du mot de passe</h2>
          <Helmet>
            <title>Réinitialiser le Mot de Passe - JobPartiel.fr</title>
            <meta name="description" content="Définissez un nouveau mot de passe pour votre compte JobPartiel.fr." />
            <meta charSet="UTF-8" />
            <meta name="keywords" content="réinitialisation du mot de passe, JobPartiel.fr" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta property="og:title" content="Réinitialiser le Mot de Passe - JobPartiel.fr" />
            <meta property="og:description" content="Définissez un nouveau mot de passe pour votre compte JobPartiel.fr." />
            <meta property="og:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
            <meta property="og:url" content="https://jobpartiel.fr/reset-password" />
            <meta name="twitter:card" content="summary" />
            <meta name="twitter:title" content="Réinitialiser le Mot de Passe - JobPartiel.fr" />
            <meta name="twitter:description" content="Définissez un nouveau mot de passe pour votre compte JobPartiel.fr." />
            <meta name="twitter:image" content="https://jobpartiel.fr/images/logo_job_partiel_grand.png" />
            <link rel="canonical" href="https://jobpartiel.fr/reset-password" />
          </Helmet>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Nouveau mot de passe
              </label>
              <div className="relative mt-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(DOMPurify.sanitize(e.target.value))}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-[#FF7A35] focus:border-[#FF7A35]"
                  required
                />
              </div>
              {password && <VerifierForceMotDePasse motDePasse={password} />}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirmer le mot de passe
              </label>
              <div className="relative mt-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(DOMPurify.sanitize(e.target.value))}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-[#FF7A35] focus:border-[#FF7A35]"
                  required
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg flex items-center space-x-3">
                <DynamicIcon name="AlertCircle" size={24} className="text-red-500" />
                <p className="text-sm text-red-800 font-medium">{error}</p>
              </div>
            )}

            {cooldown > 0 && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg flex items-center space-x-3">
                <DynamicIcon name="AlertCircle" size={24} className="text-red-500" />
                <p className="text-sm text-red-800 font-medium">Veuillez attendre {Math.ceil(cooldown / 60)} minutes avant de réessayer</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading || cooldown > 0}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#FF7A35] hover:bg-[#F76A3B] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF7A35] transition-all duration-300 ${
                isLoading || cooldown > 0 ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? 'Réinitialisation...' : 
                cooldown > 0 ? `Réessayer dans ${Math.floor(cooldown / 60)}:${(cooldown % 60).toString().padStart(2, '0')}` : 
                'Réinitialiser le mot de passe'}
            </button>
          </form>
        </AnimatedCard>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#FFF8F3] to-[#FFE4BA] flex items-center justify-center">
        <AnimatedCard className="bg-white py-8 px-4 shadow-xl rounded-lg sm:px-10">
          <div className="bg-red-100 rounded text-[#FF7A35] mx-auto flex items-center justify-center" style={{ width: '48px', height: '48px' }}>
            <DynamicIcon name="Lock" size={24} className="text-green-500" />
          </div>
          <h2 className="text-2xl font-bold text-center mt-4 mb-4">Mot de passe mis à jour !</h2>
          <p className="text-center text-gray-600 mb-8">
            Votre mot de passe a été réinitialisé avec succès. Vous allez être redirigé vers la page de connexion...
          </p>
        </AnimatedCard>
      </div>
    );
  }
};

import { Helmet } from 'react-helmet-async';

export default ResetPassword;