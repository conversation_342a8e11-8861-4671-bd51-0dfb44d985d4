import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  IconButton,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  Fab,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { ResponseTemplate } from '../../services/supportTicketService';
import useResponseTemplates from '../../hooks/useTicketResponseTemplates';
import { logger } from '@/utils/logger';
import { useAuth } from '../../contexts/AuthContext';

interface ResponseTemplateSelectorProps {
  onTemplateSelect: (content: string) => void;
  currentCategory?: string;
}

const ResponseTemplateSelector: React.FC<ResponseTemplateSelectorProps> = ({
  onTemplateSelect,
  currentCategory,
}) => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState(currentCategory || '');
  const [selectedTemplate, setSelectedTemplate] = useState<ResponseTemplate | null>(null);
  const [showNewForm, setShowNewForm] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    title: '',
    content: '',
    category: currentCategory || 'technique',
  });
  const [loading, setLoading] = useState(false);
  
  // Vérifier si l'utilisateur est un membre du staff
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';

  // Utiliser notre hook personnalisé avec seulement les filtres initiaux
  const {
    templates,
    loading: templatesLoading,
    error,
    fetchTemplates,
    createTemplate,
  } = useResponseTemplates(
    currentCategory ? { category: currentCategory } : {}
  );

  // Log des templates reçus pour le débogage
  useEffect(() => {
    logger.info(`Templates reçus: ${templates.length}`, templates);
  }, [templates]);

  // Référence pour suivre si le chargement a déjà été effectué
  const initialLoadDoneRef = React.useRef(false);

  // Effet déclenché uniquement quand la modale s'ouvre ou quand la catégorie change manuellement
  useEffect(() => {
    // Ne charger les templates que si la modale est ouverte, l'utilisateur a les droits
    // et que nous n'avons pas déjà chargé les templates pour cette catégorie
    if (open && isStaff && (!initialLoadDoneRef.current || category !== currentCategory)) {
      logger.info(`Chargement des templates pour la catégorie: ${category}`);
      initialLoadDoneRef.current = true;
      
      // Utiliser un timeout pour éviter d'appeler fetchTemplates trop tôt dans le cycle de rendering
      const timer = setTimeout(() => {
        fetchTemplates({ category });
      }, 100);
      
      return () => {
        clearTimeout(timer);
      };
    }
  }, [open, category, isStaff]); // Retirer fetchTemplates des dépendances

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedTemplate(null);
    setSearch('');
    setShowNewForm(false);
  };

  const handleTemplateSelect = (template: ResponseTemplate) => {
    setSelectedTemplate(template);
  };

  const handleConfirm = () => {
    if (selectedTemplate) {
      onTemplateSelect(selectedTemplate.content);
      handleClose();
    }
  };

  const handleCategoryChange = (event: SelectChangeEvent) => {
    setCategory(event.target.value);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };

  const handleNewTemplateChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setNewTemplate({
      ...newTemplate,
      [field]: event.target.value,
    });
  };

  const handleNewTemplateCategoryChange = (event: SelectChangeEvent<string>) => {
    setNewTemplate({
      ...newTemplate,
      category: event.target.value,
    });
  };

  const handleCreateTemplate = async () => {
    try {
      logger.info('Tentative de création de template:', newTemplate);
      
      if (!newTemplate.title || !newTemplate.content || !newTemplate.category) {
        alert('Veuillez remplir tous les champs');
        return;
      }

      // Afficher l'activité de création
      setLoading(true);
      
      logger.info('Appel du service createTemplate avec les données:', newTemplate);
      const createdTemplate = await createTemplate(newTemplate);
      
      if (createdTemplate) {
        logger.info('Modèle créé avec succès:', createdTemplate);
        // Réinitialiser le formulaire sans fermer le formulaire
        setNewTemplate({
          title: '',
          content: '',
          category: currentCategory || 'technique',
        });
        // Recharger les templates pour inclure le nouveau
        await fetchTemplates({ category });
      } else {
        logger.error('Aucun template retourné après création');
        alert('Erreur: Aucune confirmation de création reçue');
      }
    } catch (error) {
      logger.error('Erreur détaillée lors de la création du modèle:', error);
      
      // Afficher un message d'erreur plus descriptif
      let errorMessage = 'Erreur lors de la création du modèle';
      if (error instanceof Error) {
        errorMessage += ': ' + error.message;
      }
      
      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Filtrer les modèles selon la recherche
  const filteredTemplates = templates.filter(template =>
    template.title.toLowerCase().includes(search.toLowerCase()) ||
    template.content.toLowerCase().includes(search.toLowerCase())
  );

  // Si l'utilisateur n'est pas membre du staff, ne pas afficher le bouton
  if (!isStaff) {
    return null;
  }

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        onClick={handleOpen}
        startIcon={<FilterListIcon />}
        sx={{
          color: '#FF6B2C',
          borderColor: '#FF6B2C',
          '&:hover': {
            borderColor: '#FF7A35',
            backgroundColor: 'rgba(255, 107, 44, 0.04)',
          },
          textTransform: 'none',
          fontWeight: 500
        }}
      >
        Utiliser un modèle
      </Button>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            height: '80vh',
            display: 'flex',
            flexDirection: 'column',
            borderRadius: 3,
            overflow: 'hidden',
            bgcolor: '#FFF8F3',
          },
        }}
      >
        <DialogTitle sx={{ p: 0, overflow: 'hidden' }}>
          <Box 
            sx={{
              display: "flex", 
              justifyContent: "space-between", 
              alignItems: "center",
              background: 'linear-gradient(to right, #FF6B2C, #FF7A35)',
              px: 3,
              py: 2,
              color: 'white'
            }}
          >
            <Typography variant="h5" fontWeight="600">
              Sélectionner un modèle de réponse
            </Typography>
            <IconButton 
              onClick={handleClose} 
              size="small"
              sx={{
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.2)' }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            flex: 1, 
            p: 3,
            // Forcer le padding-top pour annuler le style par défaut de Material UI
            pt: '24px !important'
          }}
        >
          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                mb: 3,
                borderRadius: 1.5,
                border: '1px solid rgba(211, 47, 47, 0.2)',
              }}
            >
              Une erreur est survenue lors du chargement des modèles. Vous n'avez peut-être pas les droits nécessaires.
            </Alert>
          )}

          <Box display="flex" gap={2} mb={3}>
            <TextField
              fullWidth
              placeholder="Rechercher un modèle..."
              value={search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'white',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  }
                }
              }}
            />
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel 
                id="category-label"
                sx={{ 
                  '&.Mui-focused': { color: '#FF6B2C' }
                }}
              >
                Catégorie
              </InputLabel>
              <Select
                value={category}
                label="Catégorie"
                onChange={handleCategoryChange}
                sx={{
                  borderRadius: 2,
                  backgroundColor: 'white',
                  '& .MuiOutlinedInput-notchedOutline': {
                    '&:hover': {
                      borderColor: '#FF6B2C',
                    }
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#FF6B2C',
                  }
                }}
              >
                <MenuItem value="">Toutes</MenuItem>
                <MenuItem value="technique">Technique</MenuItem>
                <MenuItem value="facturation">Facturation</MenuItem>
                <MenuItem value="compte">Compte</MenuItem>
                <MenuItem value="mission">Mission</MenuItem>
                <MenuItem value="autre">Autre</MenuItem>
              </Select>
            </FormControl>
            <Tooltip title="Rafraîchir les modèles">
              <IconButton 
                onClick={() => fetchTemplates({ category })}
                sx={{ 
                  color: '#FF6B2C', 
                  bgcolor: 'white',
                  border: '1px solid rgba(255, 107, 44, 0.3)',
                  '&:hover': { bgcolor: 'rgba(255, 107, 44, 0.04)' }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
          
          {/* Formulaire pour créer un nouveau modèle */}
          {showNewForm ? (
            <Paper elevation={2} sx={{ 
              p: 3, 
              mb: 3, 
              borderRadius: 2,
              backgroundColor: 'white',
              border: '1px solid rgba(255, 107, 44, 0.1)',
              boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)'
            }}>
              <Typography variant="h6" gutterBottom fontWeight="600" color="#FF6B2C">
                Créer un nouveau modèle
              </Typography>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <TextField
                  label="Titre"
                  value={newTemplate.title}
                  onChange={handleNewTemplateChange('title')}
                  fullWidth
                  sx={{ 
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      }
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF6B2C',
                    }
                  }}
                  disabled={loading}
                  error={loading === false && newTemplate.title.trim() === ''}
                  helperText={loading === false && newTemplate.title.trim() === '' ? 'Le titre est requis' : ''}
                />
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel 
                    id="category-label"
                    sx={{ '&.Mui-focused': { color: '#FF6B2C' } }}
                  >
                    Catégorie
                  </InputLabel>
                  <Select
                    value={newTemplate.category}
                    label="Catégorie"
                    onChange={handleNewTemplateCategoryChange}
                    disabled={loading}
                    sx={{
                      borderRadius: 2,
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      }
                    }}
                  >
                    <MenuItem value="technique">Technique</MenuItem>
                    <MenuItem value="facturation">Facturation</MenuItem>
                    <MenuItem value="compte">Compte</MenuItem>
                    <MenuItem value="mission">Mission</MenuItem>
                    <MenuItem value="autre">Autre</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  label="Contenu"
                  value={newTemplate.content}
                  onChange={handleNewTemplateChange('content')}
                  fullWidth
                  multiline
                  rows={6}
                  sx={{ 
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      }
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#FF6B2C',
                    }
                  }}
                  disabled={loading}
                  error={loading === false && newTemplate.content.trim() === ''}
                  helperText={loading === false && newTemplate.content.trim() === '' ? 'Le contenu est requis' : ''}
                />
                <Box display="flex" justifyContent="flex-end" gap={2}>
                  <Button 
                    onClick={() => setShowNewForm(false)}
                    disabled={loading}
                    sx={{
                      color: '#FF6B2C',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 107, 44, 0.04)',
                      },
                      textTransform: 'none',
                      fontWeight: 500
                    }}
                  >
                    Annuler
                  </Button>
                  <Button 
                    variant="contained" 
                    onClick={handleCreateTemplate}
                    disabled={loading || !newTemplate.title.trim() || !newTemplate.content.trim()}
                    sx={{
                      bgcolor: '#FF6B2C',
                      '&:hover': {
                        bgcolor: '#FF7A35',
                      },
                      position: 'relative',
                      textTransform: 'none',
                      borderRadius: 2,
                      boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
                      fontWeight: 500
                    }}
                  >
                    {loading ? 'Création...' : 'Créer'}
                    {loading && (
                      <CircularProgress
                        size={24}
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          marginTop: '-12px',
                          marginLeft: '-12px',
                          color: 'white'
                        }}
                      />
                    )}
                  </Button>
                </Box>
              </FormControl>
            </Paper>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Tooltip title="Créer un modèle">
                <Fab
                  size="medium"
                  onClick={() => setShowNewForm(true)}
                  sx={{
                    bgcolor: '#FF6B2C', 
                    color: 'white',
                    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
                    '&:hover': {
                      bgcolor: '#FF7A35',
                    }
                  }}
                >
                  <AddIcon />
                </Fab>
              </Tooltip>
            </Box>
          )}
          
          {templatesLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" flexGrow={1} flexDirection="column" p={4}>
              <CircularProgress size={40} sx={{ color: '#FF6B2C' }} />
              <Typography variant="body2" mt={2} color="text.secondary">Chargement des modèles...</Typography>
            </Box>
          ) : filteredTemplates.length === 0 ? (
            <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" flexGrow={1}>
              {templates.length === 0 ? (
                <Typography>Aucun modèle trouvé. Créez votre premier modèle.</Typography>
              ) : (
                <Typography>Aucun modèle ne correspond à votre recherche "{search}"</Typography>
              )}
              <Button 
                variant="contained" 
                color="primary" 
                sx={{ 
                  mt: 2,
                  bgcolor: '#FF6B2C',
                  '&:hover': {
                    bgcolor: '#FF7A35',
                  },
                  textTransform: 'none',
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
                  fontWeight: 500
                }}
                onClick={() => setShowNewForm(true)}
              >
                Créer un modèle
              </Button>
            </Box>
          ) : (
            <Paper elevation={1} sx={{ 
              overflow: 'auto', 
              flexGrow: 1, 
              borderRadius: 2,
              bgcolor: 'white'
            }}>
              <List sx={{ p: 0 }}>
                {filteredTemplates.map((template) => (
                  <ListItem
                    key={template.id}
                    disablePadding
                    divider
                    sx={{
                      borderLeft: selectedTemplate?.id === template.id 
                        ? '4px solid #FF6B2C' 
                        : '4px solid transparent',
                      transition: 'all 0.2s ease',
                    }}
                  >
                    <ListItemButton
                      onClick={() => handleTemplateSelect(template)}
                      selected={selectedTemplate?.id === template.id}
                      sx={{
                        py: 2,
                        transition: 'all 0.2s ease',
                        '&.Mui-selected': {
                          backgroundColor: 'rgba(255, 107, 44, 0.08)',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.12)',
                          }
                        },
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        }
                      }}
                    >
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                            <Typography fontWeight={selectedTemplate?.id === template.id ? 600 : 500} color={selectedTemplate?.id === template.id ? '#FF6B2C' : 'text.primary'}>
                              {template.title}
                            </Typography>
                            <Box 
                              component="span" 
                              sx={{ 
                                fontSize: '0.75rem', 
                                color: 'text.secondary',
                                bgcolor: 'rgba(0, 0, 0, 0.04)',
                                px: 1,
                                py: 0.5,
                                borderRadius: 1,
                                ml: 2,
                                textTransform: 'capitalize'
                              }}
                            >
                              {template.category}
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              mt: 0.5
                            }}
                          >
                            {template.content}
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Paper>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3, borderTop: '1px solid rgba(0, 0, 0, 0.08)' }}>
          <Button 
            onClick={handleClose}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
              textTransform: 'none',
              fontWeight: 500
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirm}
            variant="contained"
            disabled={!selectedTemplate}
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': {
                bgcolor: '#FF7A35',
              },
              textTransform: 'none',
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(255, 107, 44, 0.3)',
              fontWeight: 500,
              px: 3
            }}
          >
            Utiliser ce modèle
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ResponseTemplateSelector; 