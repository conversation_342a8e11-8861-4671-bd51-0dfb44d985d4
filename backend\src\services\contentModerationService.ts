import axios from 'axios';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import dotenv from 'dotenv';
import path from 'path';
import { badWords, inappropriateExpressions, commonSafeTerms, safeEncounterPhrases } from '../utils/badwords';
import { processImage } from '../utils/imageProcessor';
import { getServiceCategories } from './serviceCategoriesService';
import { selectAIModel } from '../controllers/openRouterController';

// Constantes pour les buckets de stockage
const TEMP_MODERATION_BUCKET = 'temp_moderation';

// Constante pour le cache des prompts de texte
const CACHE_TEXT_PROMPT_KEY = 'text_moderation:prompt_template';
const CACHE_TEXT_PROMPT_TTL = 24 * 60 * 60; // 24 heures

// Constantes pour le rate limiting
const RATE_LIMIT_KEY_PREFIX = 'moderation_rate_limit:';
const RATE_LIMIT_WINDOW = 60; // 60 secondes
const RATE_LIMIT_MAX_REQUESTS = 30; // 30 requêtes par minute
const RATE_LIMIT_MAX_REQUESTS_IMAGE = 15; // 15 requêtes par minute pour les images

// Constantes pour le rate limiting du modèle gratuit
const RATE_API_LIMIT_GRATUITE = 10; // 10 requêtes par seconde pour les modérations gratuites au dela de 10 requêtes par seconde on passe au payant

// Charger les variables d'environnement
const NODE_ENV = process.env.NODE_ENV || 'development';
dotenv.config({ path: path.resolve(__dirname, `../../.env.${NODE_ENV}`) });

// URL de l'API de modération (à configurer dans les variables d'environnement)
const MODERATION_API_URL = process.env.MODERATION_API_URL || 'https://api.openrouter.ai/api/v1/chat/completions';
const MODERATION_API_KEY = process.env.MODERATION_API_KEY || '';
const MODERATION_API_MODEL_FREE = process.env.MODERATION_API_MODEL_FREE || 'meta-llama/llama-3.3-70b-instruct:free';
const MODERATION_API_MODEL_PAYANT = process.env.MODERATION_API_MODEL_PAYANT || 'google/gemini-2.5-flash-preview';
const MODERATION_API_MODEL_VISION_FREE = process.env.MODERATION_API_MODEL_VISION_FREE || 'google/gemini-2.0-flash-exp:free';
const MODERATION_API_MODEL_VISION_PAYANT = process.env.MODERATION_API_MODEL_VISION_PAYANT || 'google/gemini-2.5-flash-preview';
const DAILY_CALLS_LIMIT = 999;

const TIMEOUT_API = 8000;
const TIMEOUT_API_VISION = 15000;

// Types pour les résultats de modération
export interface ModerationResult {
  isSafe: boolean;
  score: number;
  categories: {
    harassment: boolean;
    hateSpeech: boolean;
    sexualContent: boolean;
    violence: boolean;
    selfHarm: boolean;
    illegalActivity: boolean;
    spam: boolean;
    phoneSpam: boolean;
    addressSpam: boolean;
    unknownRisk?: boolean; // Ajouté pour les cas de score élevé sans catégorie
  };
  flaggedText?: string[] | string;
  moderationId: string;
}

export interface ContentToModerate {
  text: string;
  type: 'mission' | 'comment' | 'profile' | 'biography' | 'profile_picture' | 'banner_picture' |
        'titre_service' | 'description_service' | 'gallery_name' | 'gallery_description' |
        'mission_title' | 'mission_description' | 'review' |
        'gallery' | 'gallery_cover' | 'featured' | 'mission_assistant' | 'avatar' |
        'featured_photo' | 'mission_image' | 'gallery_photo' | 'card_editor';
  contentId: string;
  userId: string;
  imageBuffer?: Buffer;
  imageMimeType?: string;
  imageUrl?: string; // URL de l'image à modérer (alternative à imageBuffer)
  tempImagePath?: string; // Chemin temporaire de l'image dans le bucket (pour suppression si nécessaire)
}

export interface ImageModerationResult {
  isSafe: boolean;
  score: number;
  categories: {
    adult: boolean;
    violence: boolean;
    harassment: boolean;
    hateSpeech: boolean;
    selfHarm: boolean;
    sexual: boolean;
    dangerousContent: boolean;
    poorQuality?: boolean;
    irrelevantContent?: boolean;
    misleadingContent?: boolean;
    spam?: boolean;
    phoneSpam?: boolean;
    addressSpam?: boolean;
    unknownRisk?: boolean;
    clean?: boolean; // Ajouté : true si l'image est parfaitement appropriée et sans aucun risque
  };
  moderationId: string;
  description?: string;
  serviceType?: string;
  relevantToUserServices?: boolean;
  qualityAssessment?: {
    overall: number;
    clarity: number;
    relevance: number;
    professionalAppearance: number;
  };
  improvementSuggestions?: string;
}

// Cache des résultats de modération (pour éviter de réanalyser le même contenu)
const CACHE_EXPIRY = 10 * 60; // 10 minutes en secondes
const CACHE_EXPIRY_IMAGE = 24 * 60 * 60; // 24 heures en secondes pour les images

interface ModerationApiResponse {
  data: {
    choices: Array<{
      message: {
        content: string;
      };
    }>;
    id: string;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
}

/**
 * Convertit une image en base64 pour l'API OpenRouter
 * @param buffer Buffer de l'image
 * @param mimeType Type MIME de l'image
 * @returns Chaîne base64 de l'image
 */
async function imageToBase64(buffer: Buffer, mimeType: string): Promise<string> {
  // Optimiser l'image avant de la convertir en base64
  const processedImage = await processImage(buffer, mimeType, {
    maxWidth: 800,
    maxHeight: 800,
    quality: 73
  });

  // Convertir le buffer en base64
  const base64Image = processedImage.buffer.toString('base64');

  // Retourner l'image au format attendu par l'API
  return `data:image/${processedImage.format};base64,${base64Image}`;
}

/**
 * Calcule un hash perceptuel d'une image
 * Cette version simplifiée réduit l'image à 8x8 pixels en niveaux de gris
 * et génère une empreinte binaire basée sur les valeurs des pixels
 * @param buffer Buffer de l'image
 * @param mimeType Type MIME de l'image (non utilisé mais gardé pour compatibilité)
 * @returns Hash perceptuel de l'image
 */
async function calculatePerceptualHash(buffer: Buffer, mimeType: string): Promise<string> {
  const sharp = require('sharp');
  const crypto = require('crypto');

  try {
    // Réduire l'image à 8x8 pixels en niveaux de gris
    // Cette taille très petite permet de capturer uniquement la structure générale de l'image
    const tinyImage = await sharp(buffer)
      .resize(8, 8, { fit: 'fill' })    // Taille très petite (8x8)
      .grayscale()                      // Convertir en niveaux de gris
      .raw()                            // Obtenir les données brutes des pixels
      .toBuffer();

    // Calculer la moyenne des valeurs de pixels
    const pixels = Array.from(tinyImage) as number[];
    const avg = pixels.reduce((sum, pixel) => sum + pixel, 0) / pixels.length;

    // Créer une empreinte binaire basée sur la comparaison avec la moyenne
    // Si le pixel est supérieur à la moyenne, c'est un 1, sinon c'est un 0
    let binaryString = '';
    for (const pixel of pixels) {
      binaryString += pixel >= avg ? '1' : '0';
    }

    // Convertir la chaîne binaire en hexadécimal pour plus de compacité
    // Diviser en groupes de 4 bits et convertir chaque groupe en hexadécimal
    let hexString = '';
    for (let i = 0; i < binaryString.length; i += 4) {
      const chunk = binaryString.slice(i, i + 4);
      const hexDigit = parseInt(chunk.padEnd(4, '0'), 2).toString(16);
      hexString += hexDigit;
    }

    logger.info('Hash perceptuel simplifié calculé avec succès', {
      originalSize: buffer.length,
      binaryLength: binaryString.length,
      hexLength: hexString.length
    });

    return hexString;
  } catch (error) {
    logger.error('Erreur lors du calcul du hash perceptuel', { error });

    // En cas d'erreur, revenir à un hash standard du buffer original
    return crypto
        .createHash('md5')
        .update(buffer)
        .digest('hex');
      }
    }

    /**
     * Crée une réponse d'erreur JSON bien formatée pour les appels API échoués
     * @param errorMessage Le message d'erreur à inclure
     * @returns Chaîne JSON valide respectant le format attendu par le système
     */
    function createErrorJsonResponse(errorMessage: string): string {
      const errorResponse = {
        isSafe: true, // Par défaut, on considère le contenu comme sûr en cas d'erreur (pour éviter les faux positifs)
        score: 0.1,
        categories: {
          harassment: false,
          hateSpeech: false,
          sexualContent: false,
          violence: false,
          selfHarm: false,
          illegalActivity: false,
          spam: false,
          phoneSpam: false,
          addressSpam: false,
          unknownRisk: false
        },
        flaggedText: [`Erreur d'analyse: ${errorMessage}`]
      };

      return JSON.stringify(errorResponse, null, 2);
    }

/**
 * Enregistre les données d'utilisation de l'API OpenRouter
 * @param userId ID de l'utilisateur qui a déclenché l'appel (peut être null pour les appels système)
 * @param serviceType Type de service qui a utilisé l'API (moderation, generation, etc.)
 * @param model Modèle utilisé pour cet appel
 * @param promptTokens Nombre de tokens dans le prompt
 * @param completionTokens Nombre de tokens dans la complétion
 * @param totalTokens Nombre total de tokens
 * @param responseId ID de la réponse de l'API si disponible
 * @returns Promise<void>
 */
// Clés de cache pour les appels quotidiens et les statistiques globales
const OPENROUTER_DAILY_CALLS_CACHE_KEY = 'openrouter:daily_calls';
const OPENROUTER_GLOBAL_STATS_CACHE_KEY = 'openrouter:stats:global';

export async function logOpenRouterUsage(
  userId: string | null,
  serviceType: 'moderation' | 'text_moderation' | 'image_moderation' | 'generation' | 'biography_generation' | 'service_description_generation' | 'mission_post_generation' | 'review_response_generation' | 'mission_offer_generation' | 'comment_generation' | 'custom_prompt_generation' | 'other' | 'mission_assistant' | 'avatar_moderation' | 'profile_picture_moderation' | 'midjourney_prompt' | 'card_editor_prompt' | 'slogan_generation' | 'support_user_assistance' | 'support_staff_assistance' | 'support_comment_generation',
  model: string,
  promptTokens: number,
  completionTokens: number,
  totalTokens: number,
  responseId: string | null = null
): Promise<void> {
  try {
    // Insérer les données d'utilisation
    const { error } = await supabase
      .from('openrouter_api_usage')
      .insert({
        user_id: userId,
        service_type: serviceType,
        model: model,
        prompt_tokens: Math.round(promptTokens),
        completion_tokens: Math.round(completionTokens),
        total_tokens: Math.round(totalTokens),
        response_id: responseId,
        created_at: new Date().toISOString()
      });

    if (error) {
      logger.error('Erreur lors de l\'enregistrement des données d\'utilisation OpenRouter', {
        error,
        userId,
        serviceType,
        model
      });
    } else {
      // IMPORTANT: Vider les caches pour forcer une nouvelle requête
      // Cela garantit que tous les endpoints utilisent exactement les mêmes valeurs
      try {
        // Vider le cache des appels quotidiens
        await redis.del(OPENROUTER_DAILY_CALLS_CACHE_KEY);

        // Vider le cache des statistiques globales
        await redis.del(OPENROUTER_GLOBAL_STATS_CACHE_KEY);

        logger.info('Caches vidés après un nouvel appel API', {
          serviceType,
          model
        });
      } catch (cacheError) {
        logger.warn('Erreur lors de la suppression des caches', {
          error: cacheError
        });
      }
    }
  } catch (error) {
    logger.error('Exception lors de l\'enregistrement des données d\'utilisation OpenRouter', {
      error,
      userId,
      serviceType,
      model
    });
  }
}

/**
 * Service de modération de contenu
 */
class ContentModerationService {
  private static instance: ContentModerationService;

  private constructor() {}

  public static getInstance(): ContentModerationService {
    if (!ContentModerationService.instance) {
      ContentModerationService.instance = new ContentModerationService();
    }
    return ContentModerationService.instance;
  }

  /**
   * Invalide le cache des prompts de catégories
   * @returns Promise<void>
   */
  public async invalidateCategoriesPromptCache(): Promise<void> {
    await redis.del(CACHE_TEXT_PROMPT_KEY);
    logger.info('Modération : Cache des prompts invalidé');
  }

  /**
   * Invalide tous les caches de prompts utilisés par le service
   * @returns Promise<void>
   */
  public async invalidateAllPromptCaches(): Promise<void> {
    const keys = [
      CACHE_TEXT_PROMPT_KEY
    ];

    for (const key of keys) {
      await redis.del(key);
    }

    logger.info('Modération : Tous les caches de prompts ont été invalidés');
  }

  /**
   * Vérifie si l'utilisateur a dépassé la limite de requêtes
   * @param userId ID de l'utilisateur
   * @param isImage Si la requête concerne une image
   * @returns {Promise<boolean>} true si la limite est dépassée
   */
  private async checkRateLimit(userId: string, isImage: boolean = false): Promise<boolean> {
    const key = `${RATE_LIMIT_KEY_PREFIX}${userId}:${isImage ? 'image' : 'text'}`;
    const now = Math.floor(Date.now() / 1000);
    const maxRequests = isImage ? RATE_LIMIT_MAX_REQUESTS_IMAGE : RATE_LIMIT_MAX_REQUESTS;

    try {
      // Utiliser une transaction Redis pour garantir l'atomicité
      const multi = redis.multi();

      // Nettoyer les anciennes entrées et ajouter la nouvelle
      multi.zremrangebyscore(key, 0, now - RATE_LIMIT_WINDOW);
      multi.zadd(key, now, `${now}-${Math.random()}`);
      multi.zcard(key);
      multi.expire(key, RATE_LIMIT_WINDOW);

      const results = await multi.exec();

      if (!results) {
        logger.error('Erreur lors de la vérification du rate limit', {
          userId,
          isImage,
          error: 'Transaction Redis échouée'
        });
        return false; // En cas d'erreur, permettre la requête
      }

      const requestCount = results[2][1] as number;

      if (requestCount > maxRequests) {
        logger.warn('Rate limit dépassé pour la modération', {
          userId,
          isImage,
          requestCount,
          maxRequests
        });
        return true; // Limite dépassée
      }

      return false; // Limite non dépassée
    } catch (error) {
      logger.error('Erreur lors de la vérification du rate limit', {
        userId,
        isImage,
        error
      });
      return false; // En cas d'erreur, permettre la requête
    }
  }

  /**
   * Analyse un contenu pour détecter du contenu inapproprié
   * @param content Le contenu à analyser
   * @returns Résultat de la modération
   */
  public async moderateContent(content: ContentToModerate): Promise<ModerationResult> {
    // Vérifier le rate limit
    const isImage = !!(content.imageBuffer || content.imageUrl);
    const isRateLimited = await this.checkRateLimit(content.userId, isImage);

    if (isRateLimited) {
      return {
        isSafe: false,
        score: 1,
        categories: {
          harassment: false,
          hateSpeech: false,
          sexualContent: false,
          violence: false,
          selfHarm: false,
          illegalActivity: false,
          spam: true,
          phoneSpam: false,
          addressSpam: false,
          unknownRisk: false
        },
        flaggedText: ['Vous avez effectué trop de requêtes de modération. Veuillez réessayer dans 1 minute.'],
        moderationId: `rate-limit-${Date.now()}`
      };
    }

    // Si une image est fournie (buffer ou URL), la modérer avec le texte si présent
    if ((content.imageBuffer && content.imageMimeType) || content.imageUrl) {
      try {
        logger.info('Modération : Lancement de la modération combinée image/texte', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          hasImageBuffer: !!content.imageBuffer,
          hasImageUrl: !!content.imageUrl,
          hasText: !!content.text
        });

        const imageResult = await this.moderateImage(content);

        // Préparer des informations supplémentaires sur la qualité
        let additionalInfoText: string[] = [];

        // Ajouter des informations de qualité si disponibles
        if (imageResult.qualityAssessment) {
          const quality = imageResult.qualityAssessment;
          additionalInfoText.push(`Qualité globale: ${quality.overall}/10, Clarté: ${quality.clarity}/10, Pertinence: ${quality.relevance}/10, Aspect professionnel: ${quality.professionalAppearance}/10`);
        }

        // Ajouter le type de service identifié si disponible
        if (imageResult.serviceType) {
          additionalInfoText.push(`Type de service identifié: ${imageResult.serviceType}`);

          // Ajouter si l'image est pertinente pour les services de l'utilisateur
          if (imageResult.relevantToUserServices !== undefined) {
            additionalInfoText.push(`Image ${imageResult.relevantToUserServices ? 'pertinente' : 'non pertinente'} par rapport aux services proposés par l'utilisateur.`);
          }
        }

        // Ajouter les suggestions d'amélioration si disponibles
        if (imageResult.improvementSuggestions) {
          additionalInfoText.push(`Suggestions d'amélioration: ${imageResult.improvementSuggestions}`);
        }

        // Problèmes de qualité d'image
        if (imageResult.categories.poorQuality) {
          additionalInfoText.push("Image de qualité insuffisante (floue, pixelisée ou mal éclairée).");
        }
        if (imageResult.categories.irrelevantContent) {
          additionalInfoText.push("Image sans rapport avec les services de la plateforme.");
        }
        if (imageResult.categories.misleadingContent) {
          additionalInfoText.push("Image potentiellement trompeuse ou ne représentant pas fidèlement le service.");
        }

        // Convertir le résultat de l'image en résultat de modération standard
        const result = {
          isSafe: imageResult.isSafe,
          score: imageResult.score,
          categories: {
            harassment: imageResult.categories.harassment,
            hateSpeech: imageResult.categories.hateSpeech,
            sexualContent: imageResult.categories.sexual || imageResult.categories.adult,
            violence: imageResult.categories.violence,
            selfHarm: imageResult.categories.selfHarm,
            illegalActivity: imageResult.categories.dangerousContent,
            spam: imageResult.categories.spam || false,
            phoneSpam: imageResult.categories.phoneSpam || false,
            addressSpam: imageResult.categories.addressSpam || false,
            unknownRisk: imageResult.categories.unknownRisk
          },
          flaggedText: [
            ...(imageResult.description ? [imageResult.description] : []),
            ...additionalInfoText
          ],
          moderationId: imageResult.moderationId
        };

        // Enregistrer le résultat en base de données
        // Important: Sauvegarder le résultat de modération d'image dans la base de données
        logger.info('Sauvegarde du résultat de modération d\'image', {
          contentId: content.contentId,
          contentType: content.type,
          userId: content.userId,
          isSafe: result.isSafe,
          score: result.score,
          moderationId: result.moderationId
        });

        await this.saveModerationResult(content, imageResult);

        return result;
      } catch (error) {
        logger.error('Erreur lors de la modération combinée', {
          error,
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId
        });

        // Si nous avons du texte, faire une vérification basique du texte
        if (content.text) {
          const hasInappropriateContent = this.basicContentCheck(content.text);
          return {
            isSafe: !hasInappropriateContent,
            score: hasInappropriateContent ? 0.8 : 0.1,
            categories: {
              harassment: hasInappropriateContent,
              hateSpeech: hasInappropriateContent,
              sexualContent: false,
              violence: false,
              selfHarm: false,
              illegalActivity: false,
              spam: false,
              phoneSpam: false,
              addressSpam: false,
              unknownRisk: false
            },
            flaggedText: hasInappropriateContent ? [content.text] : [],
            moderationId: `error-${Date.now()}`
          };
        }

        // Si c'est une image seule, retourner un résultat par défaut sécurisé
        return {
          isSafe: true, // Par défaut, considérer l'image comme sûre en cas d'erreur
          score: 0.1,
          categories: {
            harassment: false,
            hateSpeech: false,
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: false,
            phoneSpam: false,
            addressSpam: false,
            unknownRisk: false
          },
          flaggedText: [],
          moderationId: `error-image-${Date.now()}`
        };
      }
    }

    // Si on n'a que du texte, continuer avec la modération de texte existante
    try {
      // Déterminer la clé de cache appropriée
      // Pour les contenus temporaires, utiliser une clé basée sur le contenu et l'utilisateur
      // pour éviter les appels répétés pour le même contenu
      let cacheKey: string;

      logger.info('Modération : Lancement de la modération du contenu', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        text: content.text
      });

      if (content.contentId.startsWith('temp-')) {
        // Pour les contenus temporaires, créer une empreinte du texte pour la clé de cache
        // Cela permet de détecter les tentatives répétées du même contenu
        const contentHash = require('crypto')
          .createHash('md5')
          .update(`${content.userId}:${content.type}:${content.text}`)
          .digest('hex');

        cacheKey = `content_moderation:temp:${contentHash}`;

        // Vérifier si l'utilisateur a récemment tenté de publier ce contenu
        const userRateLimitKey = `content_moderation:rate_limit:${content.userId}:${content.type}`;
        const userRateLimit = await redis.get(userRateLimitKey);

        if (userRateLimit) {
          const rateLimitData = JSON.parse(userRateLimit);
          // Si l'utilisateur a déjà tenté de publier un contenu similaire récemment
          if (rateLimitData.contentHashes && rateLimitData.contentHashes.includes(contentHash)) {
            logger.warn('Tentative répétée de publication de contenu inapproprié', {
              userId: content.userId,
              contentType: content.type
            });

            // Retourner le résultat mis en cache sans enregistrer une nouvelle entrée
            const cachedResult = await redis.get(cacheKey);
            if (cachedResult) {
              return JSON.parse(cachedResult);
            }
          }
        }
      } else {
        // Pour les contenus avec un ID permanent, utiliser la clé standard
        cacheKey = `content_moderation:${content.type}:${content.contentId}`;
      }

      // Vérifier si le résultat est en cache
      const cachedResult = await redis.get(cacheKey);

      if (cachedResult) {
        logger.info('Modération : Résultat de modération récupéré depuis le cache', {
          contentType: content.type,
          contentId: content.contentId
        });
        return JSON.parse(cachedResult);
      }

      // Vérification rapide avec la liste de mots interdits avant d'appeler l'API
      const hasInappropriateContent = this.basicContentCheck(content.text);

      // Si la vérification basique détecte du contenu inapproprié, on évite l'appel API coûteux
      if (hasInappropriateContent) {
        logger.info('Modération : Contenu inapproprié détecté par la vérification basique', {
          contentType: content.type,
          contentId: content.contentId
        });

        // Vérifier si c'est un numéro de téléphone ou une adresse
        const originalText = content.text;
        let hasPhoneNumber = false;
        let hasAddress = false;

        // Vérifier les numéros de téléphone
        const phonePatterns = [
          /(\+33|0033|0)[\s.-]?[1-9][\s.-]?(\d{2}[\s.-]?){4}/g,
          /(\+33|0033|0)[1-9]\d{8}/g,
          /(\+33|0033|0)[1-9][\s.-]?\d{2}[\s.-]?\d{2}[\s.-]?\d{2}[\s.-]?\d{2}/g,
          /(\d{2}[\s.-]?){5}/g
        ];

        for (const pattern of phonePatterns) {
          if (pattern.test(originalText)) {
            hasPhoneNumber = true;
            break;
          }
        }

        // Vérifier les adresses
        const addressPatterns = [
          /\d+[\s,]+(rue|avenue|av|boulevard|bvd|blvd|chemin|impasse|place|allée|allee|cours|quai)[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+[\s,]+\d{5}[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i,
          /\d{5}[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i,
          /\b(rue|avenue|av|boulevard|bvd|blvd|chemin|impasse|place|allée|allee|cours|quai)\b[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i
        ];

        for (const pattern of addressPatterns) {
          if (pattern.test(originalText)) {
            hasAddress = true;
            break;
          }
        }

        const result: ModerationResult = {
          isSafe: false,
          score: 0.85, // Score élevé pour indiquer un contenu probablement inapproprié
          categories: {
            // Si c'est un numéro de téléphone ou une adresse, on ajuste les catégories
            harassment: !(hasPhoneNumber || hasAddress),
            hateSpeech: !(hasPhoneNumber || hasAddress),
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: hasPhoneNumber || hasAddress,
            phoneSpam: hasPhoneNumber,
            addressSpam: hasAddress,
            unknownRisk: false
          },
          // S'assurer que flaggedText est toujours un tableau
          flaggedText: [content.text],
          moderationId: `basic-${Date.now()}`
        };

        // Enregistrer le résultat en base de données
        await this.saveModerationResult(content, result);

        // Mettre en cache le résultat
        await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_EXPIRY);

        // Si le contenu est inapproprié et temporaire, mettre à jour la liste des tentatives de l'utilisateur
        if (!result.isSafe && content.contentId.startsWith('temp-')) {
          const contentHash = require('crypto')
            .createHash('md5')
            .update(`${content.userId}:${content.type}:${content.text}`)
            .digest('hex');

          const userRateLimitKey = `content_moderation:rate_limit:${content.userId}:${content.type}`;
          const userRateLimit = await redis.get(userRateLimitKey);

          let rateLimitData: { contentHashes: string[], lastAttempt: number };

          if (userRateLimit) {
            rateLimitData = JSON.parse(userRateLimit);
            // Ajouter le hash à la liste s'il n'y est pas déjà
            if (!rateLimitData.contentHashes.includes(contentHash)) {
              rateLimitData.contentHashes.push(contentHash);
            }
            rateLimitData.lastAttempt = Date.now();
          } else {
            rateLimitData = {
              contentHashes: [contentHash],
              lastAttempt: Date.now()
            };
          }

          // Limiter la liste à 10 derniers hashes pour éviter une croissance infinie
          if (rateLimitData.contentHashes.length > 10) {
            rateLimitData.contentHashes = rateLimitData.contentHashes.slice(-10);
          }

          // Stocker les données de rate limit pour 24 heures
          await redis.set(userRateLimitKey, JSON.stringify(rateLimitData), 'EX', 24 * 60 * 60);
        }

        return result;
      }

      // Si la vérification basique ne détecte rien, on continue avec l'API
      // Préparer le prompt pour l'API de modération
      const prompt = await this.preparePrompt(content);

      // Appeler l'API de modération
      const result = await this.callModerationAPI(prompt, content);

      // Enregistrer le résultat en base de données
      await this.saveModerationResult(content, result);

      // Mettre en cache le résultat
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_EXPIRY);

      // Si le contenu est inapproprié et temporaire, mettre à jour la liste des tentatives de l'utilisateur
      if (!result.isSafe && content.contentId.startsWith('temp-')) {
        const contentHash = require('crypto')
          .createHash('md5')
          .update(`${content.userId}:${content.type}:${content.text}`)
          .digest('hex');

        const userRateLimitKey = `content_moderation:rate_limit:${content.userId}:${content.type}`;
        const userRateLimit = await redis.get(userRateLimitKey);

        let rateLimitData: { contentHashes: string[], lastAttempt: number };

        if (userRateLimit) {
          rateLimitData = JSON.parse(userRateLimit);
          // Ajouter le hash à la liste s'il n'y est pas déjà
          if (!rateLimitData.contentHashes.includes(contentHash)) {
            rateLimitData.contentHashes.push(contentHash);
          }
          rateLimitData.lastAttempt = Date.now();
        } else {
          rateLimitData = {
            contentHashes: [contentHash],
            lastAttempt: Date.now()
          };
        }

        // Limiter la liste à 10 derniers hashes pour éviter une croissance infinie
        if (rateLimitData.contentHashes.length > 10) {
          rateLimitData.contentHashes = rateLimitData.contentHashes.slice(-10);
        }

        // Stocker les données de rate limit pour 24 heures
        await redis.set(userRateLimitKey, JSON.stringify(rateLimitData), 'EX', 24 * 60 * 60);
      }

      logger.info('Modération : Résultat de modération enregistré', {
        contentType: content.type,
        contentId: content.contentId,
        result: result
      });

      return result;
    } catch (error: any) {
      logger.error('Erreur lors de la modération du contenu', {
        error: error.message || String(error),
        contentType: content.type,
        contentId: content.contentId
      });

      // En cas d'erreur, effectuer une vérification basique du contenu
      // plutôt que de considérer automatiquement le contenu comme sûr
      const hasInappropriateContent = this.basicContentCheck(content.text);

      // Si la vérification basique détecte du contenu inapproprié, le bloquer
      // Sinon, laisser passer le contenu mais avec un identifiant d'erreur
      return {
        isSafe: !hasInappropriateContent,
        score: hasInappropriateContent ? 0.8 : 0.1,
        categories: {
          harassment: hasInappropriateContent,
          hateSpeech: hasInappropriateContent,
          sexualContent: false,
          violence: false,
          selfHarm: false,
          illegalActivity: false,
          spam: false,
          phoneSpam: false,
          addressSpam: false,
          unknownRisk: hasInappropriateContent
        },
        // S'assurer que flaggedText est toujours un tableau
        flaggedText: hasInappropriateContent ? [content.text] : [],
        moderationId: `error-${Date.now()}`
      };
    }
  }

  /**
   * Prépare le prompt pour l'API de modération de texte
   * @param content Le contenu à modérer
   * @returns Le prompt optimisé pour l'API
   */
  private async preparePrompt(content: ContentToModerate): Promise<string> {
    // Vérifier si le template est en cache
    const cachedTemplate = await redis.get(CACHE_TEXT_PROMPT_KEY);

    // Construire le contexte spécifique au type de contenu
    const contextByType = {
      mission: "Cette description de mission sera publiée sur une plateforme de jobbing.",
      comment: "Ce commentaire sera affiché publiquement sur une plateforme de jobbing.",
      profile: "Cette biographie sera affichée sur le profil public de l'utilisateur.",
      biography: "Cette biographie détaillée sera affichée sur le profil public de l'utilisateur.",
      titre_service: "Ce titre de service sera affiché publiquement sur une plateforme de jobbing.",
      description_service: "Cette description de service sera affichée publiquement sur une plateforme de jobbing.",
      gallery_name: "Ce nom de galerie sera affiché publiquement sur une plateforme de jobbing.",
      gallery_description: "Cette description de galerie sera affichée publiquement sur une plateforme de jobbing.",
      mission_title: "Ce titre de mission sera affiché publiquement sur une plateforme de jobbing.",
      mission_description: "Cette description de mission sera affichée publiquement sur une plateforme de jobbing.",
      review: "Cet avis sera affiché publiquement sur le profil d'un jobbeur.",
      gallery: "Cette image de galerie sera affichée publiquement sur le profil de l'utilisateur.",
      gallery_cover: "Cette image de couverture de galerie sera affichée publiquement sur le profil de l'utilisateur.",
      featured: "Cette image mise en avant sera affichée publiquement sur le profil de l'utilisateur.",
      mission_assistant: "Ce contenu généré par l'assistant de mission sera affiché publiquement.",
      avatar: "Cette photo de profil sera affichée publiquement sur le profil de l'utilisateur.",
      profile_picture: "Cette photo de profil sera affichée publiquement sur le profil de l'utilisateur.",
      banner_picture: "Cette bannière de profil sera affichée publiquement sur le profil de l'utilisateur.",
      featured_photo: "Cette image mise en avant sera affichée publiquement sur le profil de l'utilisateur.",
      mission_image: "Cette image de mission sera affichée publiquement sur une plateforme de jobbing.",
      gallery_photo: "Cette image de galerie sera affichée publiquement sur le profil de l'utilisateur.",
      card_editor: "Ce contenu sera affiché dans l'éditeur de cartes de visite."
    };

    // Si le template est en cache, l'utiliser
    if (cachedTemplate) {
      try {
        // Remplacer les variables du template avec le contexte et le contenu
        const filledPrompt = cachedTemplate
          .replace('${CONTEXT_BY_TYPE}', contextByType[content.type] || "Ce contenu sera affiché publiquement.")
          .replace('${CONTENT_TEXT}', content.text || "");

        // Compresser le prompt avant de le retourner
        const compressedPrompt = this.compressPrompt(filledPrompt);

        // Logger le taux de compression obtenu
        const compressionRatio = ((filledPrompt.length - compressedPrompt.length) / filledPrompt.length * 100).toFixed(2);
        logger.info('Modération de texte : Compression du prompt (depuis cache)', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          originalSize: filledPrompt.length,
          compressedSize: compressedPrompt.length,
          compressionRatio: `${compressionRatio}%`
        });

        return compressedPrompt;
      } catch (error) {
        logger.error('Modération de texte : Erreur lors de l\'utilisation du template en cache', {
          error,
          contentType: content.type,
          contentId: content.contentId
        });
        // En cas d'erreur, on continue pour recréer le template
      }
    }

    // Si le template n'est pas en cache ou si une erreur s'est produite, construire le prompt complet
    const promptTemplate = `Modérateur texte JobPartiel - Analyse stricte:

    ⚠️ INSTRUCTIONS STRICTES DE RÉPONSE ⚠️
    1. RÉPONDS UNIQUEMENT AU FORMAT JSON
    2. NE PAS écrire de texte avant ou après le JSON
    3. NE PAS commencer par "Voici l'analyse" ou toute autre phrase
    4. NE PAS terminer par une explication ou conclusion
    5. NE PAS utiliser de délimiteurs comme \`\`\`json
    6. JUSTE LE JSON, RIEN D'AUTRE

    RÈGLES CRITIQUES:
    1. JSON uniquement sans aucun texte supplémentaire
    2. Signaler UNIQUEMENT contenu manifestement inapproprié
    3. Faux positifs = PROBLÈME MAJEUR
    4. Si DOUTE = contenu sûr
    5. Score >= 0.7 si inapproprié
    6. Analyser UNIQUEMENT le texte fourni
    7. Toute menace ou intimidation, même indirecte, doit être signalée comme inappropriée (catégorie violence ou harcèlement selon le cas), UNIQUEMENT si la phrase peut raisonnablement être perçue comme une menace ou une intimidation dans le contexte du jobbing. NE PAS signaler les phrases neutres, de politesse ou de prise de contact.
    8. Les menaces ou intimidations peuvent être explicites ("je vais te frapper") ou implicites ("tu vas le regretter", "tu vas voir ce que tu vas prendre", "je vais m'occuper de toi", "tu ne perds rien pour attendre", "on va régler ça autrement", "tu vas voir ce qui va t'arriver", "je vais te retrouver", "tu vas payer").
    9. Signaler aussi les menaces déguisées, passives-agressives, ou toute formulation qui peut faire pression, peur, ou sous-entendre une conséquence négative pour l'utilisateur.
    10. Si une phrase peut être interprétée comme une menace ou une intimidation, même sans violence physique, signaler comme inapproprié (ex : "je vais parler de toi à tout le monde", "tu vas avoir des soucis", "je vais te dénoncer", "je vais te faire virer", "je vais ruiner ta réputation").

    EXEMPLES À NE PAS SIGNALER (phrases neutres, politesse, prise de contact) :
    - "On se verra bientôt pour en discuter"
    - "On en reparle"
    - "On se retrouve sur place"
    - "On se recontacte"
    - "On en discute ensemble"
    - "On se voit demain"
    - "On se retrouve à 18h"
    - "On se tient au courant"
    - "On se donne rendez-vous"
    - "On se retrouve pour le service"
    - "On en discute au téléphone"
    - "On se revoit la semaine prochaine"
    - "On se retrouve pour échanger"
    - "On se retrouve pour parler du projet"
    - "On se retrouve pour organiser"
    - "On se retrouve pour discuter des détails"
    - "On se retrouve pour convenir d'une date"

    SERVICES SPÉCIFIQUES:
    Baby-sitting:
    - OK: horaires tardifs (2-3h)
    - OK: mention enfants/garde
    - OK: tarifs élevés nuit
    - OK: lieux/horaires

    Jardinage/Bricolage:
    - OK: outils/équipements
    - OK: détails techniques
    - OK: tarifs spécialisés

    Garde animaux:
    - OK: races spécifiques
    - OK: comportements
    - OK: tarifs variables

    ÉLÉMENTS À REJETER:
    1. Téléphones: 0612345678
    2. Adresses complètes
    3. Contenu inapproprié:
       - Insultes directes
       - Discrimination évidente
       - Contenu sexuel
       - Violence physique
       - Automutilation
       - Activités illégales
       - Spam commercial
       - Menaces explicites ou implicites (exemples : "je vais te casser la gueule", "je te casse les dents", "je vais te frapper", "je vais te faire du mal", "tu vas voir ce que tu vas prendre", "je vais te retrouver", "je vais te faire payer", "je vais te faire du mal", "tu vas le regretter", etc.)

    TEXTE: "${content.text}"

    FORMAT JSON STRICT (RETOURNE UNIQUEMENT CE JSON):
    {
      "isSafe": boolean,
      "score": number,
      "categories": {
        "harassment": boolean,
        "hateSpeech": boolean,
        "sexualContent": boolean,
        "violence": boolean,
        "selfHarm": boolean,
        "illegalActivity": boolean,
        "spam": boolean,
        "phoneSpam": boolean,
        "addressSpam": boolean,
        "unknownRisk": boolean
      },
      "flaggedText": string[] | string
    }`;

    // Stocker le template en cache
    try {
      await redis.setex(CACHE_TEXT_PROMPT_KEY, CACHE_TEXT_PROMPT_TTL, promptTemplate);
      logger.info('Modération de texte : Template de prompt mis en cache', {
        templateLength: promptTemplate.length
      });
    } catch (cacheError) {
      logger.error('Modération de texte : Erreur lors de la mise en cache du template', {
        error: cacheError
      });
    }

    // Remplacer les variables du template
    const filledPrompt = promptTemplate
      .replace('${CONTEXT_BY_TYPE}', contextByType[content.type] || "Ce contenu sera affiché publiquement.")
      .replace('${CONTENT_TEXT}', content.text || "");

    // Compresser le prompt avant de le retourner
    const compressedPrompt = this.compressPrompt(filledPrompt);

    // Logger le taux de compression obtenu
    const compressionRatio = ((filledPrompt.length - compressedPrompt.length) / filledPrompt.length * 100).toFixed(2);
    logger.info('Modération de texte : Compression du prompt', {
      userId: content.userId,
      contentType: content.type,
      contentId: content.contentId,
      originalSize: filledPrompt.length,
      compressedSize: compressedPrompt.length,
      compressionRatio: `${compressionRatio}%`
    });

    return compressedPrompt;
  }

  /**
   * Appelle l'API de modération externe
   */
  private async callModerationAPI(prompt: string, content: ContentToModerate): Promise<ModerationResult> {
    // Déterminer si c'est une modération d'image ou de texte
    const isImage = !!(content.imageBuffer || content.imageUrl);
    try {
      console.log('Appel à l\'API de modération avec contenu: ' + content.text);
      console.log('Prompt: ' + prompt);
      console.log('content: ' + content);

      // Log du prompt pour debug avec limite de taille pour éviter les logs trop volumineux
      logger.info('Modération de texte : Prompt envoyé à l\'API', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        promptPreview: prompt.substring(0, 200) + '...',
        promptLength: prompt.length
      });

      // Appel à l'API OpenRouter
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_API); // Arrêt forcé après XXX ms

      // Sélectionner le modèle approprié en fonction du type de contenu (texte ou image)
      const MODERATION_API_MODEL = await selectAIModel(isImage);

      // Récupérer les statistiques de problèmes JSON pour ce modèle
      let jsonErrorRate = 0;
      try {
        const count = await redis.get('moderation_json_failure_count');
        if (count) {
          jsonErrorRate = parseInt(count);
          // Si plus de 5 erreurs accumulées, vider automatiquement les caches de prompt pour forcer une régénération
          if (jsonErrorRate > 5) {
            await this.invalidateAllPromptCaches();
            await redis.del('moderation_json_failure_count'); // Réinitialiser le compteur
            logger.info('Le prompt de modération a été réinitialisé après détection de problèmes JSON récurrents', {
              jsonErrorCount: jsonErrorRate
            });
          }
        }
      } catch (redisError) {
        logger.error('Erreur lors de la récupération des statistiques d\'erreur JSON', {
          error: redisError
        });
      }

      let response: ModerationApiResponse;

      try {
        response = await Promise.race<ModerationApiResponse>([
          axios.post<ModerationApiResponse["data"]>(
            MODERATION_API_URL,
            {
              model: MODERATION_API_MODEL,
              messages: [
                {
                  role: "user",
                  content: prompt
                }
              ],
              response_format: { type: "json_object" },
              temperature: 0.1,
              top_p: 0.1,
              frequency_penalty: 0,
              presence_penalty: 0,
              max_tokens: 500,
              stop: ["```", "Voici", "Voilà", "L'analyse"],
              structured_outputs: true
            },
            {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MODERATION_API_KEY}`,
                'HTTP-Referer': 'https://jobpartiel.fr',
                'X-Title': content.text
                  ? 'JobPartiel Content Moderation'
                  : 'JobPartiel Image Moderation'
              },
              signal: controller.signal,
              timeout: TIMEOUT_API // XXX timeout
            }
          ),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Timeout de l\'API de modération')), TIMEOUT_API)
          )
        ]);

        clearTimeout(timeoutId);
      } catch (error: any) {
        clearTimeout(timeoutId);

        // Log de l'erreur avec le modèle gratuit
        logger.warn('Erreur avec le modèle gratuit, tentative avec le modèle payant', {
          error: error.message,
          contentType: content.type,
          contentId: content.contentId,
          model: MODERATION_API_MODEL
        });

        // Sélectionner le modèle payant pour la seconde tentative
        const MODERATION_API_MODEL_RETRY = isImage ? MODERATION_API_MODEL_VISION_PAYANT : MODERATION_API_MODEL_PAYANT;

        // Créer un nouveau controller pour la seconde tentative
        const retryController = new AbortController();
        const retryTimeoutId = setTimeout(() => retryController.abort(), TIMEOUT_API);

        try {
          // Seconde tentative avec le modèle payant
          logger.info('Seconde tentative avec le modèle payant', {
            contentType: content.type,
            contentId: content.contentId,
            model: MODERATION_API_MODEL_RETRY
          });

          response = await Promise.race<ModerationApiResponse>([
            axios.post<ModerationApiResponse["data"]>(
              MODERATION_API_URL,
              {
                model: MODERATION_API_MODEL_RETRY,
                messages: [
                  {
                    role: "user",
                    content: prompt
                  }
                ],
                response_format: { type: "json_object" },
                temperature: 0.1,
                top_p: 0.1,
                frequency_penalty: 0.1,
                presence_penalty: 0.1,
                max_tokens: 1000,
                stop: ["```"],
                structured_outputs: true
              },
              {
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${MODERATION_API_KEY}`,
                  'HTTP-Referer': 'https://jobpartiel.fr',
                  'X-Title': content.text
                    ? 'JobPartiel Content Moderation (Retry)'
                    : 'JobPartiel Image Moderation (Retry)'
                },
                signal: retryController.signal,
                timeout: TIMEOUT_API
              }
            ),
            new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('Timeout de l\'API de modération (seconde tentative)')), TIMEOUT_API)
            )
          ]);

          clearTimeout(retryTimeoutId);

          // Loguer l'utilisation du modèle payant
          logger.info('Modération réussie avec le modèle payant', {
            contentType: content.type,
            contentId: content.contentId,
            model: MODERATION_API_MODEL_RETRY
          });
        } catch (retryError: any) {
          clearTimeout(retryTimeoutId);

          // Log de l'erreur avec le modèle payant
          logger.error('Échec des deux tentatives de modération', {
            originalError: error.message,
            retryError: retryError.message,
            contentType: content.type,
            contentId: content.contentId
          });

          // Retourner une réponse par défaut en cas d'échec des deux tentatives
          response = {
            data: {
              choices: [{
                message: {
                  content: JSON.stringify({
                    isSafe: true,
                    score: 0.1,
                    categories: {
                      harassment: false,
                      hateSpeech: false,
                      sexualContent: false,
                      violence: false,
                      selfHarm: false,
                      illegalActivity: false,
                      spam: false,
                      phoneSpam: false,
                      addressSpam: false,
                      unknownRisk: false
                    },
                    flaggedText: []
                  })
                }
              }],
              id: `timeout-${Date.now()}`
            }
          };
        }
      }

      // logger.info('Réponse de l\'API de modération: ' + JSON.stringify(response.data));

      const responseData = response.data;
      // Déterminer quel modèle a été utilisé pour cette réponse
      let currentModel = MODERATION_API_MODEL;

      // Loguer l'utilisation de l'API
      try {
        // Déterminer le type de service pour le logging
        const serviceType = isImage ? 'image_moderation' : 'text_moderation';

        // Log détaillé pour le débogage
        logger.info('Logging de l\'utilisation de l\'API pour modération', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          serviceType: serviceType,
          isImage: isImage,
          model: currentModel,
          hasUsageData: !!(responseData && 'usage' in responseData && responseData.usage),
          responseId: responseData?.id || null
        });

        // Vérifier si l'objet responseData et la propriété usage existent
        if (responseData && 'usage' in responseData && responseData.usage) {
          await logOpenRouterUsage(
            content.userId,
            serviceType,
            currentModel,
            responseData.usage.prompt_tokens || 0,
            responseData.usage.completion_tokens || 0,
            responseData.usage.total_tokens || 0,
            responseData.id || null
          );
        } else {
          // Si les données d'utilisation ne sont pas disponibles, loguer avec des valeurs estimées
          await logOpenRouterUsage(
            content.userId,
            serviceType,
            currentModel,
            prompt.length / 4, // Estimation grossière: environ 4 caractères par token
            100, // Valeur estimée pour une réponse de modération typique
            (prompt.length / 4) + 100,
            responseData?.id || null
          );
        }
      } catch (logError) {
        logger.error('Erreur lors de l\'enregistrement des données d\'utilisation OpenRouter', {
          error: logError,
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          isImage: isImage
        });
      }

      if (!responseData || !responseData.choices || responseData.choices.length === 0) {
        return {
          isSafe: true,
          score: 0.1,
          categories: {
            harassment: false,
            hateSpeech: false,
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: false,
            phoneSpam: false,
            addressSpam: false,
            unknownRisk: false
          },
          // S'assurer que flaggedText est toujours un tableau
          flaggedText: [],
          moderationId: `invalid-response-${Date.now()}`
        };
      }

      const apiResponseContent = responseData.choices[0].message?.content;

      // Si le contenu de la réponse est vide ou invalide, utiliser la vérification basique
      if (!apiResponseContent || !apiResponseContent.includes('{')) {
        logger.warn('Réponse de modération invalide, utilisation de la vérification basique', {
          contentType: content.type,
          contentId: content.contentId,
          apiResponse: responseData
        });

        // Effectuer une vérification basique du contenu
        const hasInappropriateContent = this.basicContentCheck(content.text);

        return {
          isSafe: !hasInappropriateContent,
          score: hasInappropriateContent ? 0.8 : 0.1,
          categories: {
            harassment: hasInappropriateContent,
            hateSpeech: hasInappropriateContent,
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: false,
            phoneSpam: false,
            addressSpam: false,
            unknownRisk: hasInappropriateContent
          },
          // S'assurer que flaggedText est toujours un tableau
          flaggedText: hasInappropriateContent ? [content.text] : [],
          moderationId: `basic-check-${Date.now()}`
        };
      }

      // Nettoyer et extraire le JSON de la réponse de l'API
      let parsedResult: Partial<ModerationResult>;

      try {
        // Fonction pour extraire le JSON valide d'une chaîne potentiellement mal formatée
        const extractValidJson = (text: string): string | null => {
          // Nettoyer la réponse des délimiteurs Markdown potentiels
          let cleanedText = text;

          // Supprimer les délimiteurs Markdown d'ouverture (```json)
          const markdownStartMatch = cleanedText.match(/```(\w+)?\s*\n/);
          if (markdownStartMatch) {
            cleanedText = cleanedText.substring(markdownStartMatch[0].length);
          }

          // Supprimer les délimiteurs Markdown de fermeture (```)
          cleanedText = cleanedText.replace(/```(\w+)?$/g, '').trim();

          // Supprimer toutes phrases d'introduction communes
          cleanedText = cleanedText.replace(/^(voici|voilà|voici l'analyse|voilà l'analyse|l'analyse du|analyse du|résultat de l'analyse|json résultat|le résultat est|en analysant|après analyse)[^{]*/i, '').trim();

          // Trouver le premier { et le dernier }
          const firstBraceIndex = cleanedText.indexOf('{');
          if (firstBraceIndex === -1) return null;

          // Utiliser une approche plus robuste pour trouver la fin du JSON
          // en comptant les accolades ouvrantes et fermantes
          let openBraces = 0;
          let lastValidBraceIndex = -1;

          for (let i = firstBraceIndex; i < cleanedText.length; i++) {
            const char = cleanedText[i];

            // Ignorer les caractères dans les chaînes
            if (char === '"') {
              // Trouver la fin de la chaîne en tenant compte des échappements
              i++;
              while (i < cleanedText.length) {
                if (cleanedText[i] === '\\') {
                  i++; // Sauter le caractère échappé
                } else if (cleanedText[i] === '"') {
                  break;
                }
                i++;
              }
              if (i >= cleanedText.length) break;
              continue;
            }

            if (char === '{') {
              openBraces++;
            } else if (char === '}') {
              openBraces--;
              if (openBraces === 0) {
                lastValidBraceIndex = i;
                break; // Nous avons trouvé la fin du JSON valide
              }
            }
          }

          if (lastValidBraceIndex === -1) return null;

          // Extraire le JSON potentiel
          const jsonCandidate = cleanedText.substring(firstBraceIndex, lastValidBraceIndex + 1);

          // Vérification supplémentaire pour s'assurer que c'est un JSON valide
          try {
            // Tester si le JSON est valide en le parsant
            JSON.parse(jsonCandidate);
            return jsonCandidate;
          } catch (e) {
            // Si le parsing échoue, essayer de nettoyer davantage
            try {
              // Remplacer les caractères non-ASCII et les caractères de contrôle
              const cleanedJson = jsonCandidate
                .replace(/[^\x20-\x7E]/g, '') // Supprimer les caractères non-ASCII
                .replace(/[\x00-\x1F\x7F]/g, '') // Supprimer les caractères de contrôle
                .replace(/,\s*}/g, '}') // Supprimer les virgules traînantes
                .replace(/,\s*]/g, ']'); // Supprimer les virgules traînantes dans les tableaux

              // Tester à nouveau
              JSON.parse(cleanedJson);
              return cleanedJson;
            } catch (e2) {
              // Si ça échoue encore, retourner null
              return null;
            }
          }
        };

        // Essayer d'extraire le JSON valide
        const jsonString = extractValidJson(apiResponseContent);

        if (!jsonString) {
          throw new Error('Impossible d\'extraire un objet JSON valide de la réponse de l\'API');
        }

        // Vérifier si la réponse contient du texte avant ou après le JSON
        const beforeJson = apiResponseContent.substring(0, apiResponseContent.indexOf(jsonString)).trim();
        const afterJson = apiResponseContent.substring(apiResponseContent.indexOf(jsonString) + jsonString.length).trim();

        if (beforeJson || afterJson) {
          logger.warn('La réponse de l\'API contient du texte supplémentaire autour du JSON, possible hallucination', {
            beforeJson: beforeJson.substring(0, 100) + (beforeJson.length > 100 ? '...' : ''),
            afterJson: afterJson.substring(0, 100) + (afterJson.length > 100 ? '...' : '')
          });

          // Stocker cet échec pour analyse et amélioration du prompt
          try {
            // Enregistrer les cas où le modèle génère du texte superflu autour du JSON
            const failureData = {
              timestamp: new Date().toISOString(),
              modelUsed: MODERATION_API_MODEL,
              contentType: content.type,
              responsePreview: apiResponseContent.substring(0, 300),
              beforeJsonLength: beforeJson.length,
              afterJsonLength: afterJson.length
            };

            // Utiliser Redis pour stocker temporairement ces échecs
            const failureKey = `moderation_json_failure:${Date.now()}`;
            await redis.set(failureKey, JSON.stringify(failureData), 'EX', 7 * 24 * 60 * 60); // 7 jours

            // Incrémenter un compteur pour suivre la fréquence de ce problème
            await redis.incr('moderation_json_failure_count');

            logger.info('Échec JSON: données stockées pour analyse', {
              failureKey,
              contentType: content.type
            });
          } catch (redisError) {
            logger.error('Erreur lors du stockage des données d\'échec JSON', {
              error: redisError
            });
          }
        }

        // Essayer de parser le JSON extrait
        if (!jsonString) {
          throw new Error('Impossible d\'extraire un objet JSON valide de la réponse de l\'API');
        }

        try {
          parsedResult = JSON.parse(jsonString) as Partial<ModerationResult>;

          // Vérification supplémentaire pour s'assurer que les champs essentiels sont présents
          if (parsedResult.isSafe === undefined ||
              parsedResult.score === undefined ||
              !parsedResult.categories) {
            throw new Error('Le JSON parsé ne contient pas les champs requis (isSafe, score, categories)');
          }

          // Normaliser flaggedText s'il est présent mais n'est pas un tableau
          if (parsedResult.flaggedText !== undefined && !Array.isArray(parsedResult.flaggedText)) {
            if (typeof parsedResult.flaggedText === 'string') {
              parsedResult.flaggedText = [parsedResult.flaggedText];
            } else {
              // Si ce n'est ni un tableau ni une chaîne, initialiser comme tableau vide
              parsedResult.flaggedText = [];
            }
          }
        } catch (jsonError) {
          logger.error('Erreur lors du parsing ou de la validation du JSON extrait', {
            jsonError,
            jsonString: jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : '')
          });

          // Si toutes les tentatives échouent, utiliser la vérification basique
          throw new Error('Impossible de parser le JSON ou données invalides');
        }
      } catch (error: any) {
        logger.error('Erreur lors de l\'extraction ou du parsing du JSON', {
          error: error.message || String(error),
          apiResponseContent: apiResponseContent.substring(0, 300) + (apiResponseContent.length > 300 ? '...' : '')
        });

        // Utiliser la vérification basique du contenu
        const hasInappropriateContent = this.basicContentCheck(content.text);
        return {
          isSafe: !hasInappropriateContent,
          score: hasInappropriateContent ? 0.8 : 0.1,
          categories: {
            harassment: hasInappropriateContent,
            hateSpeech: hasInappropriateContent,
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: false,
            phoneSpam: false,
            addressSpam: false,
            unknownRisk: hasInappropriateContent
          },
          flaggedText: hasInappropriateContent ? [content.text] : [],
          moderationId: `json-error-${Date.now()}`
        };
      }

      // Valider strictement le format de la réponse
      if (!this.validateApiResponse(parsedResult)) {
        logger.warn('Réponse de l\'API de modération invalide, format non conforme', {
          parsedResult,
          contentType: content.type,
          contentId: content.contentId
        });

        // Utiliser la vérification basique à la place
        const hasInappropriateContent = this.basicContentCheck(content.text);
        return {
          isSafe: !hasInappropriateContent,
          score: hasInappropriateContent ? 0.8 : 0.1,
          categories: {
            harassment: hasInappropriateContent,
            hateSpeech: hasInappropriateContent,
            sexualContent: false,
            violence: false,
            selfHarm: false,
            illegalActivity: false,
            spam: false,
            phoneSpam: false,
            addressSpam: false,
            unknownRisk: false
          },
          flaggedText: hasInappropriateContent ? [content.text] : [],
          moderationId: `invalid-format-${Date.now()}`
        };
      }

      // Vérifier si le score est élevé mais qu'aucune catégorie n'est détectée
      const hasDetectedCategories = parsedResult.categories && Object.values(parsedResult.categories).some(value => value === true);
      if (parsedResult.score && parsedResult.score > 0.5 && !hasDetectedCategories) {
        logger.warn(`Modération : Incohérence avant normalisation: score élevé (${parsedResult.score}) mais aucune catégorie détectée - Ajout d'unknownRisk`);
        parsedResult.categories!.unknownRisk = true;
      }

      // Normaliser les résultats pour assurer la cohérence
      // Passer directement flaggedText à normalizeApiResult qui s'occupera de le normaliser
      const normalizedResult = this.normalizeApiResult(parsedResult, parsedResult.flaggedText || [], false);
      let isSafe = normalizedResult.isSafe;
      let score = normalizedResult.score;
      let flaggedTextArray = normalizedResult.flaggedTextArray;

      // Valider et compléter la réponse
      const finalResult: ModerationResult = {
        isSafe: isSafe,
        score: score,
        categories: {
          harassment: parsedResult.categories?.harassment ?? false,
          hateSpeech: parsedResult.categories?.hateSpeech ?? false,
          sexualContent: parsedResult.categories?.sexualContent ?? false,
          violence: parsedResult.categories?.violence ?? false,
          selfHarm: parsedResult.categories?.selfHarm ?? false,
          illegalActivity: parsedResult.categories?.illegalActivity ?? false,
          spam: parsedResult.categories?.spam ?? false,
          phoneSpam: parsedResult.categories?.phoneSpam ?? false,
          addressSpam: parsedResult.categories?.addressSpam ?? false,
          unknownRisk: parsedResult.categories?.unknownRisk ?? false
        },
        flaggedText: flaggedTextArray,
        moderationId: responseData.id || `api-${Date.now()}` // Utiliser l'id de la réponse API si disponible
      };

      // console.log('finalResult', finalResult);

      return finalResult;

    } catch (error: any) {
      logger.error('Erreur détaillée lors de l\'appel à l\'API de modération', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        stack: error.stack,
        contentType: content.type,
        contentId: content.contentId,
      });

      // Au lieu de propager l'erreur, utiliser la vérification basique
      // Cela évite d'interrompre le flux de l'application en cas d'erreur de l'API
      const hasInappropriateContent = this.basicContentCheck(content.text);

      return {
        isSafe: !hasInappropriateContent,
        score: hasInappropriateContent ? 0.8 : 0.1,
        categories: {
          harassment: hasInappropriateContent,
          hateSpeech: hasInappropriateContent,
          sexualContent: false,
          violence: false,
          selfHarm: false,
          illegalActivity: false,
          spam: false,
          phoneSpam: false,
          addressSpam: false,
          unknownRisk: false
        },
        flaggedText: hasInappropriateContent ? [content.text] : [],
        moderationId: `api-error-${Date.now()}`
      };
    }
  }

  /**
   * Normalise les résultats de l'API pour assurer la cohérence entre les différents champs
   * @param result Le résultat partiel de l'API
   * @param flaggedTextInput Le tableau des textes signalés
   * @param hasBasicViolation Indique si la vérification basique a détecté du contenu inapproprié
   * @returns Un objet contenant les valeurs normalisées
   */
  private normalizeApiResult(
    result: Partial<ModerationResult>,
    flaggedTextInput: string[] | string | undefined,
    hasBasicViolation: boolean = false
  ): { isSafe: boolean; score: number; flaggedTextArray: string[] } {
    // S'assurer que flaggedTextArray est toujours un tableau
    let flaggedTextArray: string[] = [];

    if (flaggedTextInput) {
      // Si c'est déjà un tableau, l'utiliser tel quel
      if (Array.isArray(flaggedTextInput)) {
        flaggedTextArray = flaggedTextInput;
      }
      // Si c'est une chaîne, la convertir en tableau avec un seul élément
      else if (typeof flaggedTextInput === 'string') {
        flaggedTextArray = [flaggedTextInput];
      }
    }

    // Valeurs par défaut
    let isSafe = result.isSafe ?? true;
    let score = result.score ?? 0;

    // Si la vérification basique a détecté du contenu inapproprié,
    // le contenu ne peut jamais être considéré comme sûr
    if (hasBasicViolation) {
      isSafe = false;
      // Assurer un score élevé pour les violations détectées par la vérification basique
      if (score < 0.7) score = 0.7;
      // logger.info('Modération : Contenu marqué comme non sûr car détecté par la vérification basique. Score modifié à 0.7');
    }

    // Vérifier si des catégories sont détectées
    const hasDetectedCategories = result.categories && Object.values(result.categories).some(value => value === true);

    // Vérifier spécifiquement les catégories de spam téléphonique et d'adresse
    const hasPhoneSpam = result.categories?.phoneSpam === true;
    const hasAddressSpam = result.categories?.addressSpam === true;
    const hasContactSpam = hasPhoneSpam || hasAddressSpam;

    // Vérifier les catégories critiques (harcèlement, discours haineux, violence)
    const hasCriticalCategories = result.categories && (
      result.categories.harassment === true ||
      result.categories.hateSpeech === true ||
      result.categories.violence === true
    );

    // Si des catégories critiques sont détectées, le contenu ne doit jamais être considéré comme sûr
    if (hasCriticalCategories) {
      isSafe = false;
      if (score < 0.7) score = 0.7;
      // logger.info('Modération : Contenu marqué comme non sûr car catégories critiques détectées. Score modifié à 0.7');
    }

    // Vérifier les autres catégories (hors spam de contact)
    const nonContactCategories: Record<string, boolean> = { ...result.categories };
    if ('phoneSpam' in nonContactCategories) {
      nonContactCategories['phoneSpam'] = false;
    }
    if ('addressSpam' in nonContactCategories) {
      nonContactCategories['addressSpam'] = false;
    }
    if ('spam' in nonContactCategories) {
      nonContactCategories['spam'] = false;
    }

    const hasOtherCategories = Object.values(nonContactCategories).some(value => value === true);
    // Règles de normalisation

    // 1. Si aucune catégorie n'est détectée et le score est faible, le contenu est sûr
    if (!hasDetectedCategories && score < 0.3 && !hasCriticalCategories) {
      isSafe = true;
      flaggedTextArray = [];
      // Si le score est vraiment bas, le fixer à 0.1 pour être cohérent
      if (score < 0.1) score = 0.1;
    }

    // 2. Si le score est 0 mais isSafe est false, c'est incohérent
    else if (score === 0 && !isSafe && !hasCriticalCategories) {
      isSafe = true;
      flaggedTextArray = [];
      score = 0.1; // Score minimal pour être cohérent
      // logger.info('Modération : Score 0 mais contenu non sûr. Score modifié à 0.1');
    }

    // 3. Si le score est élevé mais isSafe est true, c'est incohérent
    else if (score >= 0.7 && isSafe) {
      isSafe = false;
      // Si flaggedText est vide mais le score est élevé, ajouter un élément générique
      if (flaggedTextArray.length === 0) {
        flaggedTextArray = ['Contenu potentiellement inapproprié'];
        // logger.info('Modération : Score élevé mais contenu non sûr. Ajout d\'un élément générique');
      }
      // Si aucune catégorie n'est détectée mais le score est élevé, ajouter unknownRisk
      if (!hasDetectedCategories && result.categories) {
        result.categories.unknownRisk = true;
        logger.warn('Modération : Score élevé sans catégorie détectée, ajout de unknownRisk (normalisation)', { score, categories: result.categories });
      }
    }

    // 4. Si phoneSpam ou addressSpam est détecté, ajuster le score et isSafe en conséquence
    else if (hasContactSpam) {
      // Si c'est uniquement du spam de contact (pas d'autres catégories)
      if (!hasOtherCategories) {
        // Le spam de contact est toujours considéré comme non sûr
        isSafe = false;

        // Ajuster le score en fonction du type de spam
        if (score < 0.3) {
          // Score minimum pour le spam de contact
          score = 0.3;
        }
      }
      // Si d'autres catégories sont détectées, le score devrait être plus élevé
      else {
        isSafe = false;
        if (score < 0.5) {
          score = 0.5;
        }
      }
      // console.log('Modération : Spam de contact détecté. Score modifié à', score);
    }

    // 5. Assurer la cohérence entre score et isSafe
    if (score >= 0.5 && isSafe && !hasCriticalCategories) {
      isSafe = false;
    } else if (score < 0.3 && !isSafe && !hasDetectedCategories && !hasCriticalCategories) {
      isSafe = true;
      score = 0.1;
      flaggedTextArray = [];
      // logger.info('Modération : Score faible mais contenu sûr. Score modifié à 0.1');
    }

    // 6. Vérification des faux positifs
    // Ne pas appliquer la détection des faux positifs si des catégories critiques sont détectées
    if (!isSafe && flaggedTextArray.length > 0 && !hasBasicViolation && !hasCriticalCategories) {
      // Utilisation de la liste centralisée de termes sûrs
      const isFalsePositive = flaggedTextArray.every(text => {
        const lowercaseText = text.toLowerCase();

        // Vérifier si le texte contient des expressions courantes de rencontre
        const containsSafeEncounterPhrase = safeEncounterPhrases.some(phrase =>
          lowercaseText.includes(phrase)
        );

        // Vérifier si le texte contient des termes de contact légitimes
        const containsCommonTerms = commonSafeTerms.some((term: string) =>
          lowercaseText.includes(term)
        );

        // Vérifier si le texte ne contient pas d'insultes ou de termes vraiment inappropriés
        const containsNoReallyBadWords = !this.containsObviouslyInappropriateContent(lowercaseText);

        // Vérifier si le texte est dans la limite du système de commentaires (50 caractères)
        const isWithinLimit = text.length <= 50;

        // Vérifier si le texte est une demande de contact légitime
        const isContactRequest = /(?:pourr?iez[\s-]vous|pouvez[\s-]vous|peux[\s-]tu)[\s-](?:m[e'][\s-]?(?:contacter|appeler|joindre|rappeler)|me[\s-]?donner)/i.test(lowercaseText);

        // Vérifier si le texte mentionne simplement une ville sans adresse complète
        const isCityMention = /\b(?:à|a|sur|en)\s+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s-]+\b/i.test(lowercaseText) &&
          !/\d+\s+(?:rue|avenue|boulevard|chemin|impasse|place|allée|cours|quai)/i.test(lowercaseText);

        // Double vérification avec la fonction basicContentCheck
        const hasNoBadWords = !this.basicContentCheck(text);

        // Si c'est une mention de ville avec une expression de rencontre sûre
        // OU si c'est une demande de contact légitime
        // OU si c'est un texte dans la limite avec des termes communs
        // ET sans mots inappropriés ET sans violation de la vérification basique
        return ((isCityMention && containsSafeEncounterPhrase) ||
                isContactRequest ||
                (isWithinLimit && containsCommonTerms)) &&
               containsNoReallyBadWords &&
               hasNoBadWords;
      });

      // Si tous les textes signalés semblent être des faux positifs
      if (isFalsePositive) {
        logger.info('Modération : Faux positif détecté, contenu considéré comme sûr', {
          flaggedText: flaggedTextArray,
          originalScore: score
        });

        isSafe = true;
        score = 0.2; // Score faible mais non nul
        flaggedTextArray = []; // Nettoyer les textes signalés puisque c'est un faux positif
        logger.info('Modération : Faux positif détecté, contenu considéré comme sûr. Score modifié à 0.2');
      } else {
        logger.info('Modération : Contenu inapproprié confirmé, non considéré comme faux positif', {
          flaggedText: flaggedTextArray,
          score: score
        });
      }
    }

    // Arrondir le score à 1 décimale pour éviter les valeurs étranges
    score = Math.round(score * 10) / 10;

    return { isSafe, score, flaggedTextArray };
  }

  /**
   * Vérifie si un texte contient du contenu manifestement inapproprié
   * Cette fonction est utilisée pour détecter les vrais positifs vs les faux positifs
   * Utilise la fonction importée du module badwords.ts
   */
  private containsObviouslyInappropriateContent(text: string): boolean {
    // Utiliser la fonction importée du module centralisé
    // Renommer la fonction importée pour éviter la récursion infinie
    const { containsObviouslyInappropriateContent: checkInappropriateContent } = require('../utils/badwords');

    const result = checkInappropriateContent(text);

    if (result) {
      logger.info(`Modération : Contenu manifestement inapproprié détecté dans le texte`);
    }

    return result;
  }

  /**
   * Valide strictement le format de la réponse de l'API de modération
   * @param response La réponse de l'API à valider
   * @returns true si la réponse est valide, false sinon
   */
  /**
   * Modère une image pour détecter du contenu inapproprié
   * @param content Le contenu avec l'image à analyser (buffer ou URL)
   * @returns Résultat de la modération d'image
   */
  /**
   * Vérifie si une image a déjà été modérée en utilisant son hash
   * @param imageHash Hash de l'image à vérifier
   * @returns Résultat de modération en cache ou null si non trouvé
   */
  private async checkImageModerationCache(imageHash: string): Promise<ImageModerationResult | null> {
    try {
      // Vérifier si le résultat est en cache
      const cacheKey = `image_moderation:content:${imageHash}`;
      const cachedResult = await redis.get(cacheKey);

      if (cachedResult) {
        logger.info('✅ CACHE HIT: Modération d\'image récupérée depuis le cache par hash', {
          imageHash,
          cacheKey
        });
        return JSON.parse(cachedResult);
      }

      return null;
    } catch (error) {
      logger.error('Erreur lors de la vérification du cache de modération d\'image', {
        error,
        imageHash
      });
      return null;
    }
  }

  private async moderateImage(content: ContentToModerate): Promise<ImageModerationResult> {
    // Vérifier qu'une image est fournie (soit buffer, soit URL)
    if ((!content.imageBuffer || !content.imageMimeType) && !content.imageUrl) {
      throw new Error('Aucune image fournie pour la modération (ni buffer, ni URL)');
    }

    try {
      // Variable pour stocker le hash de l'image et la clé de cache
      let imageHash: string = '';
      let cacheKey: string = '';

      // Générer un ID de modération unique avec préfixe image-
      const moderationId = `image-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      // Vérifier si l'image a déjà un hash stocké (pour les images temporaires)
      if (content.tempImagePath) {
        const storedHash = await redis.get(`image_hash:${content.tempImagePath}`);
        if (storedHash) {
          imageHash = storedHash;
          logger.info('Modération d\'image : Hash trouvé dans le cache pour l\'image temporaire', {
            userId: content.userId,
            contentId: content.contentId,
            tempImagePath: content.tempImagePath,
            imageHash: storedHash
          });

          // Vérifier si cette image a déjà été modérée
          const cachedResult = await this.checkImageModerationCache(storedHash);
          if (cachedResult) {
            logger.info('Modération d\'image : Résultat trouvé dans le cache pour l\'image temporaire', {
              userId: content.userId,
              contentId: content.contentId,
              tempImagePath: content.tempImagePath,
              imageHash: storedHash,
              isSafe: cachedResult.isSafe
            });
            return cachedResult;
          }
        }
      }

      if (content.imageBuffer && content.imageMimeType) {
        // Si on a un buffer, utiliser un hash perceptuel comme clé de cache
        // pour détecter les images visuellement similaires
        imageHash = await calculatePerceptualHash(content.imageBuffer, content.imageMimeType);

        logger.info('Modération d\'image : Hash perceptuel calculé pour le buffer', {
          userId: content.userId,
          contentId: content.contentId,
          bufferSize: content.imageBuffer.length,
          imageHash
        });

        // Utiliser un préfixe plus spécifique pour éviter les collisions
        cacheKey = `image_moderation:content:${imageHash}`;
      } else if (content.imageUrl) {
        // Si on a une URL, télécharger l'image et calculer son hash
        try {
          // Si on a un chemin temporaire, récupérer l'image depuis Supabase
          if (content.tempImagePath) {
            logger.info('Modération d\'image : Calcul du hash de l\'image pour le cache', {
              userId: content.userId,
              contentId: content.contentId,
              tempImagePath: content.tempImagePath
            });

            // Récupérer l'image depuis Supabase
            const { data, error } = await supabase.storage
              .from(TEMP_MODERATION_BUCKET)
              .download(content.tempImagePath);

            if (error || !data) {
              throw new Error(`Erreur lors de la récupération de l'image: ${error?.message || 'Aucune donnée'}`);
            }

            // Convertir en buffer et calculer le hash
            const buffer = Buffer.from(await data.arrayBuffer());
            const mimeType = data.type || 'image/jpeg';

            // Stocker le buffer dans content pour éviter de le télécharger à nouveau
            content.imageBuffer = buffer;
            content.imageMimeType = mimeType;

            // Utiliser un hash perceptuel pour détecter les images visuellement similaires
            imageHash = await calculatePerceptualHash(buffer, mimeType);

            logger.info('Modération d\'image : Hash perceptuel calculé pour l\'image Supabase', {
              userId: content.userId,
              contentId: content.contentId,
              tempImagePath: content.tempImagePath,
              bufferSize: buffer.length,
              imageHash
            });

            // Utiliser le même préfixe que pour les buffers directs pour assurer la cohérence
            cacheKey = `image_moderation:content:${imageHash}`;

            // Stocker le hash dans un cache séparé pour pouvoir le retrouver rapidement
            await redis.set(`image_hash:${content.tempImagePath}`, imageHash, 'EX', CACHE_EXPIRY_IMAGE);

            logger.info('Modération d\'image : Utilisation d\'une clé de cache basée sur le contenu', {
              userId: content.userId,
              contentId: content.contentId,
              imageHash,
              bufferSize: buffer.length,
              mimeType
            });
          } else {
            // Si on n'a pas de chemin temporaire, essayer de télécharger l'image depuis l'URL
            logger.info('Modération d\'image : Téléchargement pour calcul du hash', {
              userId: content.userId,
              contentId: content.contentId
            });

            // Télécharger l'image
            const response = await axios.get(content.imageUrl, {
              responseType: 'arraybuffer',
              timeout: 3000 // Timeout court pour ne pas bloquer
            });

            // Calculer le hash du contenu
            const buffer = Buffer.from(response.data);
            const mimeType = response.headers['content-type'] || 'image/jpeg';

            // Stocker le buffer dans content pour éviter de le télécharger à nouveau
            content.imageBuffer = buffer;
            content.imageMimeType = mimeType;

            // Utiliser un hash perceptuel pour détecter les images visuellement similaires
            imageHash = await calculatePerceptualHash(buffer, mimeType);

            logger.info('Modération d\'image : Hash perceptuel calculé pour l\'image URL', {
              userId: content.userId,
              contentId: content.contentId,
              bufferSize: buffer.length,
              imageHash
            });

            // Utiliser le même préfixe que pour les buffers directs pour assurer la cohérence
            cacheKey = `image_moderation:content:${imageHash}`;

            logger.info('Modération d\'image : Utilisation d\'une clé de cache basée sur le contenu', {
              userId: content.userId,
              contentId: content.contentId,
              imageHash,
              bufferSize: buffer.length,
              mimeType
            });
          }
        } catch (error: any) {
          // En cas d'erreur, fallback sur l'URL
          logger.warn('Modération d\'image : Erreur lors du calcul du hash, fallback sur l\'URL', {
            userId: content.userId,
            contentId: content.contentId,
            errorMessage: error.message || String(error)
          });

          // Utiliser SHA-256 pour un hachage plus robuste et cohérent
          const crypto = require('crypto');
          const urlHash = crypto
            .createHash('sha256')
            .update(content.imageUrl)
            .digest('hex');

          // Stocker le hash pour pouvoir le réutiliser
          imageHash = urlHash;

          // Utiliser le même préfixe pour assurer la cohérence
          cacheKey = `image_moderation:content:${urlHash}`;
        }
      } else {
        throw new Error('Données d\'image invalides pour la modération');
      }

      // Log pour debug
      logger.info('Modération d\'image : Clé de cache générée', {
        userId: content.userId,
        contentId: content.contentId,
        cacheKey
      });

      // Vérifier si le résultat est en cache
      const cachedResult = await redis.get(cacheKey);

      if (cachedResult) {
        logger.info('✅ CACHE HIT: Modération d\'image récupérée depuis le cache', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          cacheKey,
          hasUrl: !!content.imageUrl,
          hasBuffer: !!(content.imageBuffer && content.imageMimeType)
        });
        return JSON.parse(cachedResult);
      }

      logger.info('❌ CACHE MISS: Modération d\'image non trouvée dans le cache', {
        userId: content.userId,
        contentId: content.contentId,
        cacheKey
      });

      // Préparer l'image pour l'API
      let imageData: string;
      let isBase64 = false;
      let imageBuffer: Buffer | null = null;
      let imageMimeType: string | null = null;

      // Vérifier si nous avons déjà un buffer d'image (récupéré lors du calcul du hash)
      if (content.imageBuffer && content.imageMimeType) {
        // Utiliser directement le buffer existant
        imageBuffer = content.imageBuffer;
        imageMimeType = content.imageMimeType;

        // Convertir en base64
        imageData = await imageToBase64(imageBuffer, imageMimeType);
        isBase64 = true;

        logger.info('Modération d\'image : Utilisation du buffer existant', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          bufferSize: imageBuffer.length,
          mimeType: imageMimeType
        });
      } else if (content.imageUrl) {
        try {
          // Si nous avons un chemin temporaire dans Supabase
          if (content.tempImagePath) {
            // Vérifier si nous avons déjà téléchargé cette image lors du calcul du hash
            // Si oui, le buffer devrait être dans content.imageBuffer
            if (content.imageBuffer && content.imageMimeType) {
              imageBuffer = content.imageBuffer;
              imageMimeType = content.imageMimeType;

              logger.info('Modération d\'image : Réutilisation du buffer calculé précédemment', {
                userId: content.userId,
                contentType: content.type,
                contentId: content.contentId,
                bufferSize: imageBuffer.length,
                mimeType: imageMimeType
              });
            } else {
              // Sinon, télécharger l'image depuis Supabase
              logger.info('Modération d\'image : Téléchargement depuis Supabase', {
                userId: content.userId,
                contentType: content.type,
                contentId: content.contentId,
                tempImagePath: content.tempImagePath
              });

              const { data, error } = await supabase.storage
                .from(TEMP_MODERATION_BUCKET)
                .download(content.tempImagePath);

              if (error || !data) {
                throw new Error(`Erreur lors de la récupération de l'image: ${error?.message || 'Aucune donnée'}`);
              }

              // Convertir en buffer
              imageBuffer = Buffer.from(await data.arrayBuffer());
              imageMimeType = data.type || 'image/jpeg';

              // Stocker le buffer dans content pour éviter de le télécharger à nouveau
              content.imageBuffer = imageBuffer;
              content.imageMimeType = imageMimeType;
            }

            // Convertir en base64
            imageData = await imageToBase64(imageBuffer as Buffer, imageMimeType as string);
            isBase64 = true;

            logger.info('Modération d\'image : Image convertie en base64', {
              userId: content.userId,
              contentType: content.type,
              contentId: content.contentId,
              bufferSize: imageBuffer.length,
              mimeType: imageMimeType
            });
          } else {
            // Si nous n'avons pas de chemin temporaire, télécharger depuis l'URL
            logger.info('Modération d\'image : Téléchargement depuis URL', {
              userId: content.userId,
              contentType: content.type,
              contentId: content.contentId,
              imageUrl: content.imageUrl
            });

            // Télécharger l'image avec des en-têtes personnalisés
            const response = await axios.get(content.imageUrl, {
              responseType: 'arraybuffer',
              headers: {
                'User-Agent': 'JobPartiel-Moderation-Service/1.0',
                'Accept': 'image/jpeg,image/png,image/webp,image/*',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              },
              timeout: 5000
            });

            imageBuffer = Buffer.from(response.data);
            imageMimeType = response.headers['content-type'] || 'image/jpeg';

            // Stocker le buffer dans content
            if (imageBuffer) {
              content.imageBuffer = imageBuffer;
              content.imageMimeType = imageMimeType || 'image/jpeg';
            }

            // Convertir en base64
            imageData = await imageToBase64(imageBuffer, imageMimeType as string);
            isBase64 = true;

            logger.info('Modération d\'image : Image téléchargée et convertie en base64', {
              userId: content.userId,
              contentType: content.type,
              contentId: content.contentId,
              bufferSize: imageBuffer.length,
              mimeType: imageMimeType
            });
          }
        } catch (error) {
          logger.error('Erreur lors de la récupération de l\'image, on accepte l\'image comme sûre', {
            error,
            userId: content.userId,
            contentType: content.type,
            contentId: content.contentId,
            imageUrl: content.imageUrl,
            tempImagePath: content.tempImagePath
          });

          // En cas d'erreur de récupération de l'image, retourner directement un résultat positif
          return {
            isSafe: true,
            score: 0.1,
            categories: {
              adult: false,
              violence: false,
              harassment: false,
              hateSpeech: false,
              selfHarm: false,
              sexual: false,
              dangerousContent: false,
              unknownRisk: false
            },
            moderationId: `error-image-${Date.now()}`,
            description: "Erreur lors de la récupération de l'image, contenu considéré comme sûr"
          };
        }
      } else {
        throw new Error('Données d\'image invalides pour la modération');
      }

      // Préparer le prompt pour l'API de modération d'image
      const prompt = await this.prepareImagePrompt(content);

      // Log du prompt pour debug
      logger.info('Modération d\'image : Prompt envoyé à l\'API', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        promptPreview: prompt.substring(0, 20000) + '...',
        promptLength: prompt.length
      });

      // Sélectionner le modèle approprié en fonction du nombre d'appels quotidiens
      const selectedModel = await selectAIModel(true); // true pour indiquer qu'il s'agit d'un modèle de vision

      // Appel à l'API OpenRouter avec le modèle de vision
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_API_VISION);

      // Construire le corps de la requête en fonction du type d'image (base64 ou URL)
      const requestBody = {
        model: selectedModel,
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              { type: "image_url", image_url: { url: imageData } }
            ]
          }
        ],
        response_format: { type: "json_object" },
        temperature: 0.1,
        max_tokens: 1000
      };

      // Log de la requête pour debug
      logger.info('Modération d\'image : Requête envoyée à l\'API', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        model: selectedModel,
        promptLength: prompt.length,
        isBase64: isBase64,
        imageDataLength: imageData.length,
        imageDataPreview: imageData.substring(0, 50) + '...' // Tronquer pour éviter des logs trop longs
      });

      // Appel à l'API OpenRouter avec le modèle de vision (controller et timeoutId sont définis plus haut)
      let response: any;
      let currentModel = selectedModel;

      try {
        const apiCallPromise = axios.post(
          MODERATION_API_URL,
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${MODERATION_API_KEY}`,
              'HTTP-Referer': 'https://jobpartiel.fr',
              'X-Title': content.text ? 'JobPartiel Content Moderation' : 'JobPartiel Image Moderation'
            },
            signal: controller.signal,
            timeout: TIMEOUT_API_VISION
          }
        );

        const timeoutPromise = new Promise<never>((_, reject) =>
          // Utilisation du timeoutId existant pour le message d'erreur
          setTimeout(() => reject(new Error('Timeout de l\'API de modération d\'image')), TIMEOUT_API_VISION)
        );

        response = await Promise.race<any>([apiCallPromise, timeoutPromise]);
        clearTimeout(timeoutId); // Annuler le timeout global si l'appel réussit ou échoue avant

        // Vérification d'erreur spécifique OpenRouter (ex: erreur de fournisseur)
        if (response.data && response.data.error) {
          logger.error('Modération d\'image : OpenRouter API a renvoyé une erreur dans le corps de la réponse.', {
            userId: content.userId,
            contentType: content.type,
            contentId: content.contentId,
            openRouterError: response.data.error
          });
          // Lever une erreur pour être attrapée par le catch externe de moderateImage
          throw new Error(`OpenRouter API Error: ${response.data.error.message || 'Unknown error'}`);
        }

      } catch (error: any) {
        clearTimeout(timeoutId); // S'assurer que le timeout est annulé en cas d'erreur ici aussi

        // Log détaillé de l'erreur pour faciliter le débogage
        logger.warn('Erreur avec le modèle gratuit pour la modération d\'image, tentative avec le modèle payant', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          errorMessage: error.message,
          errorResponse: error.response?.data,
          errorStatus: error.response?.status,
          requestModel: selectedModel
        });

        // Seconde tentative avec le modèle payant
        try {
          // Utiliser le modèle payant pour la seconde tentative
          currentModel = MODERATION_API_MODEL_VISION_PAYANT;

          // Créer un nouveau controller pour la seconde tentative
          const retryController = new AbortController();
          const retryTimeoutId = setTimeout(() => retryController.abort(), TIMEOUT_API_VISION);

          // Mettre à jour le corps de la requête avec le modèle payant
          const retryRequestBody = {
            ...requestBody,
            model: currentModel
          };

          logger.info('Seconde tentative avec le modèle payant pour la modération d\'image', {
            contentType: content.type,
            contentId: content.contentId,
            model: currentModel
          });

          const retryApiCallPromise = axios.post(
            MODERATION_API_URL,
            retryRequestBody,
            {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MODERATION_API_KEY}`,
                'HTTP-Referer': 'https://jobpartiel.fr',
                'X-Title': content.text ? 'JobPartiel Content Moderation (Retry)' : 'JobPartiel Image Moderation (Retry)'
              },
              signal: retryController.signal,
              timeout: TIMEOUT_API_VISION
            }
          );

          const retryTimeoutPromise = new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Timeout de l\'API de modération d\'image (seconde tentative)')), TIMEOUT_API_VISION)
          );

          response = await Promise.race<any>([retryApiCallPromise, retryTimeoutPromise]);
          clearTimeout(retryTimeoutId);

          // Vérification d'erreur spécifique OpenRouter (ex: erreur de fournisseur)
          if (response.data && response.data.error) {
            logger.error('Modération d\'image (seconde tentative) : OpenRouter API a renvoyé une erreur dans le corps de la réponse.', {
              userId: content.userId,
              contentType: content.type,
              contentId: content.contentId,
              openRouterError: response.data.error
            });
            throw new Error(`OpenRouter API Error (seconde tentative): ${response.data.error.message || 'Unknown error'}`);
          }

          logger.info('Modération d\'image réussie avec le modèle payant', {
            contentType: content.type,
            contentId: content.contentId,
            model: currentModel
          });
        } catch (retryError: any) {
          // Log détaillé de l'erreur pour faciliter le débogage
          logger.error('Échec des deux tentatives de modération d\'image', {
            userId: content.userId,
            contentType: content.type,
            contentId: content.contentId,
            originalError: error.message,
            retryError: retryError.message,
            originalModel: selectedModel,
            retryModel: currentModel
          });

          // Au lieu de propager l'erreur, créer un JSON d'erreur valide
          logger.warn('Création d\'un JSON d\'erreur formaté après échec des deux tentatives', {
            contentType: content.type,
            contentId: content.contentId
          });

          // Simuler une réponse avec un JSON valide pour éviter les erreurs de parsing
          response = {
            data: {
              choices: [{
                message: {
                  content: createErrorJsonResponse('Échec des deux tentatives de modération')
                }
              }],
              id: `fallback-${Date.now()}`
            }
          };
        }
      }

      // Loguer l'utilisation de l'API (seulement si on a une réponse valide sans erreur finale)
      try {
        // Déterminer le type de service pour le logging
        const serviceType = 'image_moderation';

        // Log détaillé pour le débogage
        logger.info('Logging de l\'utilisation de l\'API pour modération d\'image', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          serviceType: serviceType,
          model: currentModel, // Utiliser le modèle actuel (qui peut être le modèle payant après une seconde tentative)
          hasUsageData: !!(response.data && 'usage' in response.data && response.data.usage),
          responseId: response.data?.id || null
        });

        if (response.data && 'usage' in response.data && response.data.usage) {
           await logOpenRouterUsage(
             content.userId,
             serviceType,
             currentModel, // Utiliser le modèle actuel
             response.data.usage.prompt_tokens || 0,
             response.data.usage.completion_tokens || 0,
             response.data.usage.total_tokens || 0,
             response.data.id || null
           );
         } else {
           // Si les données d'utilisation ne sont pas disponibles (même après succès apparent?)
           await logOpenRouterUsage(
             content.userId,
             serviceType,
             currentModel, // Utiliser le modèle actuel
             500, // Estimation
             100, // Estimation
             600,
             response.data?.id || null
           );
         }
      } catch (logError) {
        logger.error('Erreur lors du logging de l\'utilisation de l\'API pour modération d\'image', {
          error: logError,
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          model: currentModel // Ajouter le modèle utilisé pour faciliter le débogage
        });
      }

      // Traiter la réponse (seulement si on a une réponse valide)
      // ... (Response processing logic using 'response' variable - checks for choices etc.) ...
       let apiResponseContent: string | undefined = undefined;

       if (response && response.data && Array.isArray(response.data.choices) && response.data.choices.length > 0) {
         // ... (logic to extract apiResponseContent) ...
         if (response.data.choices[0] && response.data.choices[0].message && typeof response.data.choices[0].message.content === 'string') {
           apiResponseContent = response.data.choices[0].message.content;
         } else {
           logger.warn('Modération d\'image : Premier choix ("choices[0]") ou son message/contenu est manquant ou invalide dans la réponse API post-retry.', { /* ... */ });
         }
       } else {
         // Cette condition ne devrait plus être atteinte si la vérification d'erreur 502 fonctionne,
         // mais on la garde comme filet de sécurité.
         logger.warn('Modération d\'image : Champ "choices" manquant, non-tableau, ou vide dans la réponse API post-retry.', {
           /* ... existing warning log structure ... */
           rawResponseData: response?.data ? JSON.stringify(response.data).substring(0, 1000) : 'response.data missing'
         });
       }

       if (!apiResponseContent) {
         throw new Error('Contenu de réponse API manquant, vide ou mal structuré pour la modération d\'image après vérifications et retries.');
       }

      // Extraire le JSON de la réponse
      let parsedResult: Partial<ImageModerationResult>;

      // Log détaillé de la réponse pour le débogage
      console.log("Résultat apiResponseContent :", apiResponseContent);

      // Log supplémentaire pour voir la réponse complète de l'API
      logger.info('Modération d\'image : Réponse complète de l\'API', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        responseId: response.data?.id,
        responseChoicesLength: response.data?.choices?.length,
        responseComplete: JSON.stringify(response.data).substring(0, 500) + '...'
      });

      try {
        // Définir la fonction d'extraction pour image, basée sur celle utilisée pour le texte
        const extractValidJsonFromImage = (text: string): string | null => {
          // Nettoyer la réponse des délimiteurs Markdown potentiels
          let cleanedText = text;

          // Supprimer les délimiteurs Markdown d'ouverture (```json)
          const markdownStartMatch = cleanedText.match(/```(\w+)?\s*\n/);
          if (markdownStartMatch) {
            cleanedText = cleanedText.substring(markdownStartMatch[0].length);
          }

          // Supprimer les délimiteurs Markdown de fermeture (```)
          cleanedText = cleanedText.replace(/```(\w+)?$/g, '').trim();

          // Supprimer toutes phrases d'introduction communes
          cleanedText = cleanedText.replace(/^(voici|voilà|voici l'analyse|voilà l'analyse|l'analyse du|analyse du|résultat de l'analyse|json résultat|le résultat est|en analysant|après analyse)[^{]*/i, '').trim();

          // Trouver le premier { et le dernier }
          const firstBraceIndex = cleanedText.indexOf('{');
          if (firstBraceIndex === -1) return null;

          // Utiliser une approche plus robuste pour trouver la fin du JSON
          // en comptant les accolades ouvrantes et fermantes
          let openBraces = 0;
          let lastValidBraceIndex = -1;

          for (let i = firstBraceIndex; i < cleanedText.length; i++) {
            const char = cleanedText[i];

            // Ignorer les caractères dans les chaînes
            if (char === '"') {
              // Trouver la fin de la chaîne en tenant compte des échappements
              i++;
              while (i < cleanedText.length) {
                if (cleanedText[i] === '\\') {
                  i++; // Sauter le caractère échappé
                } else if (cleanedText[i] === '"') {
                  break;
                }
                i++;
              }
              if (i >= cleanedText.length) break;
              continue;
            }

            if (char === '{') {
              openBraces++;
            } else if (char === '}') {
              openBraces--;
              if (openBraces === 0) {
                lastValidBraceIndex = i;
                break; // Nous avons trouvé la fin du JSON valide
              }
            }
          }

          if (lastValidBraceIndex === -1) return null;

          // Extraire le JSON potentiel
          const jsonCandidate = cleanedText.substring(firstBraceIndex, lastValidBraceIndex + 1);

          // Vérification supplémentaire pour s'assurer que c'est un JSON valide
          try {
            // Tester si le JSON est valide en le parsant
            JSON.parse(jsonCandidate);
            return jsonCandidate;
          } catch (e) {
            // Si le parsing échoue, essayer de nettoyer davantage
            try {
              // Remplacer les caractères non-ASCII et les caractères de contrôle
              const cleanedJson = jsonCandidate
                .replace(/[^\x20-\x7E]/g, '') // Supprimer les caractères non-ASCII
                .replace(/[\x00-\x1F\x7F]/g, '') // Supprimer les caractères de contrôle
                .replace(/,\s*}/g, '}') // Supprimer les virgules traînantes
                .replace(/,\s*]/g, ']'); // Supprimer les virgules traînantes dans les tableaux

              // Tester à nouveau
              JSON.parse(cleanedJson);
              return cleanedJson;
            } catch (e2) {
              // Si ça échoue encore, retourner null
              return null;
            }
          }
        };

        // Essayer d'abord avec la fonction d'extraction qui est plus robuste
        const jsonString = extractValidJsonFromImage(apiResponseContent);

        if (!jsonString) {
          // Tentative de secours avec la méthode regex simple
          const jsonMatch = apiResponseContent.match(/\{[\s\S]*\}/);
          const fallbackJsonString = jsonMatch ? jsonMatch[0] : null;

          if (!fallbackJsonString) {
            logger.error('Impossible d\'extraire un JSON valide même avec les méthodes de secours', {
              responsePreview: apiResponseContent.substring(0, 300)
            });

            // Créer un JSON valide de secours plutôt que de lever une erreur
            return {
              isSafe: true, // Par défaut sûr en cas d'erreur pour éviter les faux positifs
              score: 0.1,
              categories: {
                adult: false,
                violence: false,
                harassment: false,
                hateSpeech: false,
                selfHarm: false,
                sexual: false,
                dangerousContent: false,
                unknownRisk: false
              },
              moderationId: `extract-error-${Date.now()}`,
              description: "Erreur lors de l'extraction du JSON de la réponse API"
            };
          }

          // Utiliser le JSON de secours
          const parsedFallback = JSON.parse(fallbackJsonString);
           logger.info('JSON extrait avec méthode de secours', {
             jsonLength: fallbackJsonString.length
           });

           // Continuer avec ce JSON de secours
           parsedResult = parsedFallback;
                   } else if (jsonString) {
            // Utiliser le JSON extrait par la méthode principale seulement si le string n'est pas null
            parsedResult = JSON.parse(jsonString);

            // Log pour confirmer l'extraction réussie du JSON
            logger.info('JSON extrait avec succès', {
              contentId: content.contentId,
              jsonLength: jsonString.length
            });
         }

        // Log de la réponse brute pour debug
        logger.info('Modération d\'image : Réponse brute de l\'API', {
          userId: content.userId,
          contentType: content.type,
          contentId: content.contentId,
          apiResponseRaw: apiResponseContent
        });

        // Vérifier que jsonString n'est pas null avant de parser
        if (!jsonString) {
          throw new Error('Impossible d\'extraire un objet JSON valide de la réponse de l\'API');
        }

        parsedResult = JSON.parse(jsonString) as Partial<ImageModerationResult>;

        // Vérifier que les champs essentiels sont présents
        if (parsedResult.isSafe === undefined ||
            parsedResult.score === undefined ||
            !parsedResult.categories) {
          throw new Error('Le JSON parsé ne contient pas les champs requis');
        }
      } catch (error) {
        logger.error('Erreur lors du parsing de la réponse de modération d\'image', {
          error,
          apiResponseContent: apiResponseContent.substring(0, 300)
        });

        // En cas d'erreur, retourner un résultat par défaut
        return {
          isSafe: true, // Par défaut, considérer l'image comme sûre en cas d'erreur
          score: 0.1,
          categories: {
            adult: false,
            violence: false,
            harassment: false,
            hateSpeech: false,
            selfHarm: false,
            sexual: false,
            dangerousContent: false,
            unknownRisk: false
          },
          moderationId: `error-image-${Date.now()}`,
          description: "Erreur lors de l'analyse de l'image"
        };
      }

      // Suppression de la logique de détection de mots-clés d'arme et du log associé
      const modelReportedDescription = parsedResult.description || "";

      // Après le parsing de la réponse, ajouter cette logique
      if (parsedResult.categories?.dangerousContent === true) {
        // Si du contenu dangereux est détecté, l'image ne devrait pas être considérée comme sûre
        parsedResult.isSafe = false;
        parsedResult.score = Math.max(parsedResult.score || 0.1, 0.7); // Augmenter le score si contenu dangereux

        logger.info('Modération d\'image : Contenu dangereux détecté, modification du résultat', {
          contentId: content.contentId,
          description: parsedResult.description,
          originalScore: parsedResult.score,
          newScore: Math.max(parsedResult.score || 0.1, 0.7)
        });
      }

      // Correction : si la catégorie clean est présente, forcer le score à 0.1 et isSafe à true
      if (parsedResult.categories?.clean === true) {
        parsedResult.score = 0.1;
        parsedResult.isSafe = true;
      }

      // Construire le résultat final
      const result: ImageModerationResult = {
        isSafe: parsedResult.isSafe ?? true,
        score: parsedResult.score ?? 0.1,
        categories: {
          adult: parsedResult.categories?.adult ?? false,
          violence: parsedResult.categories?.violence ?? false,
          harassment: parsedResult.categories?.harassment ?? false,
          hateSpeech: parsedResult.categories?.hateSpeech ?? false,
          selfHarm: parsedResult.categories?.selfHarm ?? false,
          sexual: parsedResult.categories?.sexual ?? false,
          dangerousContent: parsedResult.categories?.dangerousContent ?? false,
          poorQuality: parsedResult.categories?.poorQuality ?? false,
          irrelevantContent: parsedResult.categories?.irrelevantContent ?? false,
          misleadingContent: parsedResult.categories?.misleadingContent ?? false,
          spam: parsedResult.categories?.spam ?? false,
          phoneSpam: parsedResult.categories?.phoneSpam ?? false,
          addressSpam: parsedResult.categories?.addressSpam ?? false,
          unknownRisk: parsedResult.categories?.unknownRisk ?? false,
          clean: parsedResult.categories?.clean ?? false // Ajouté
        },
        moderationId: response.data?.id ?
          (response.data.id.startsWith('gen-') ? response.data.id : `image-${response.data.id}`) :
          `image-${Date.now()}`,
        description: modelReportedDescription
      };

      // Ajouter le type de service identifié si disponible
      if (parsedResult.serviceType) {
        result.serviceType = parsedResult.serviceType;
      }

      // Ajouter la pertinence par rapport aux services de l'utilisateur si disponible
      if (parsedResult.relevantToUserServices !== undefined) {
        result.relevantToUserServices = parsedResult.relevantToUserServices;
      }

      // Ajouter l'évaluation de qualité si disponible
      if (parsedResult.qualityAssessment) {
        result.qualityAssessment = {
          overall: parsedResult.qualityAssessment.overall ?? 5,
          clarity: parsedResult.qualityAssessment.clarity ?? 5,
          relevance: parsedResult.qualityAssessment.relevance ?? 5,
          professionalAppearance: parsedResult.qualityAssessment.professionalAppearance ?? 5
        };
      }

      // Ajouter les suggestions d'amélioration si disponibles
      if (parsedResult.improvementSuggestions) {
        result.improvementSuggestions = parsedResult.improvementSuggestions;
      }

      // Seuls les problèmes de qualité vraiment graves et le contenu trompeur sont bloquants
      // La pertinence par rapport aux services utilisateur n'est plus un critère de rejet
      if (result.categories.poorQuality || result.categories.misleadingContent) {

        // Exception: si l'image est identifiée comme nettoyage/débarras mais marquée comme poorQuality,
        // être plus tolérant car ces images peuvent sembler de mauvaise qualité mais être appropriées
        if (result.categories.poorQuality && result.serviceType &&
            ['nettoyage', 'ménage', 'débarras', 'rangement', 'aide à domicile'].includes(result.serviceType.toLowerCase())) {

          // Pour les images de nettoyage/débarras, être plus tolérant sur la qualité
          if (result.qualityAssessment && result.qualityAssessment.overall >= 4) {
            result.categories.poorQuality = false;
            logger.info('Modération d\'image : Image de nettoyage/débarras, qualité acceptée malgré le score initial', {
              contentId: content.contentId,
              serviceType: result.serviceType,
              overallQuality: result.qualityAssessment.overall
            });
          }
        }

        // Vérifier si il reste des problèmes après les exceptions
        if (result.categories.poorQuality || result.categories.misleadingContent) {
          result.isSafe = false;

          // Ajuster le score en fonction du problème de qualité
          if (result.score < 0.5) {
            result.score = 0.5; // Score moyen pour problèmes de qualité d'image
          }

          logger.info('Modération d\'image : Problème de qualité ou contenu trompeur détecté, image marquée comme non sûre', {
            contentId: content.contentId,
            poorQuality: result.categories.poorQuality,
            misleadingContent: result.categories.misleadingContent,
            serviceType: result.serviceType || 'non identifié',
            score: result.score
          });
        } else {
          // Aucun problème bloquant détecté
          logger.info('Modération d\'image : Aucun problème bloquant détecté après vérifications', {
            contentId: content.contentId,
            serviceType: result.serviceType || 'non identifié'
          });
        }
      }

      // Note: irrelevantContent n'est plus un critère de rejet automatique
      // L'image peut être non pertinente par rapport aux services de l'utilisateur mais rester valide
      if (result.categories.irrelevantContent) {
        logger.info('Modération d\'image : Contenu potentiellement non pertinent détecté mais non bloquant', {
          contentId: content.contentId,
          serviceType: result.serviceType || 'non identifié',
          relevantToUserServices: result.relevantToUserServices
        });

        // Réduire légèrement le score de pertinence mais ne pas rejeter
        if (result.qualityAssessment && result.qualityAssessment.relevance > 3) {
          result.qualityAssessment.relevance = Math.max(3, result.qualityAssessment.relevance - 1);
        }
      }

      // Log du résultat final pour debug avec les nouvelles informations
      logger.info('Modération d\'image : Résultat final', {
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId,
        isSafe: result.isSafe,
        score: result.score,
        categories: result.categories,
        description: result.description,
        serviceType: result.serviceType,
        relevantToUserServices: result.relevantToUserServices,
        qualityAssessment: result.qualityAssessment,
        hasImprovementSuggestions: !!result.improvementSuggestions
      });

      // Mettre en cache le résultat
      // Utiliser une durée de vie plus longue pour les résultats de modération d'image
      await redis.set(cacheKey, JSON.stringify(result), 'EX', CACHE_EXPIRY_IMAGE);

      // Si c'est une image temporaire, stocker également le hash pour pouvoir le retrouver rapidement
      if (content.tempImagePath && imageHash) {
        await redis.set(`image_hash:${content.tempImagePath}`, imageHash, 'EX', CACHE_EXPIRY_IMAGE);
        logger.info('Hash d\'image stocké pour l\'image temporaire', {
          userId: content.userId,
          contentId: content.contentId,
          tempImagePath: content.tempImagePath,
          imageHash
        });
      }

      return result;
    } catch (error: any) {
      let errorDetails: any = { original_error_string: String(error) }; // Fallback de base

      try {
        if (error && typeof error === 'object' && Object.keys(error).length === 0 && !(error instanceof Error)) {
          // Cas spécifique: l'erreur est un objet vide littéral {}
          errorDetails = {
            message: 'Caught an empty object {} as error.',
            name: 'EmptyObjectError',
            type: 'empty_object',
            stack_trace: new Error('Stack trace for empty object error').stack
          };
        } else if (error instanceof Error) {
          // Erreur standard, extraire les propriétés communes
          errorDetails = {
            message: error.message,
            name: error.name,
            stack_trace: error.stack,
            type: 'error_instance'
            // Tenter de capturer d'autres propriétés énumérables et non énumérables de l'objet Error
            // Object.getOwnPropertyNames() inclut les propriétés non énumérables
          };
          const allProps = Object.getOwnPropertyNames(error);
          allProps.forEach(prop => {
            if (!['message', 'name', 'stack_trace', 'type'].includes(prop)) {
              // @ts-ignore
              errorDetails[prop] = error[prop];
            }
          });

        } else if (typeof error === 'object' && error !== null) {
          // Pour les autres objets non-Error, tenter une sérialisation
          errorDetails = {
            message: `Caught a non-Error object. Stringified: ${String(error)}`,
            type: 'non_error_object',
            properties: {}
          };
          try {
            // Tenter de sérialiser toutes les propriétés propres (own properties)
            // Cela peut échouer pour des objets complexes (ex: références circulaires)
            const serializedProps = JSON.stringify(error, Object.getOwnPropertyNames(error));
            errorDetails.properties = JSON.parse(serializedProps);
          } catch (serializationError) {
            // @ts-ignore
            errorDetails.properties = { serialization_failed: String(serializationError), original_keys: Object.keys(error) };
          }
        } else {
          // Pour les types primitifs (string, number, boolean, etc.)
          errorDetails = {
            message: `Caught a primitive error type: ${String(error)}`,
            type: 'primitive_error',
            value: error
          };
        }
      } catch (detailExtractionError) {
        // Si même l'extraction des détails échoue
        errorDetails = {
          message: `Failed to extract details from error. Original error string: ${String(error)}`,
          type: 'detail_extraction_failed',
          extraction_error_message: String(detailExtractionError),
          // @ts-ignore
          extraction_error_stack: detailExtractionError?.stack
        };
      }

      logger.error('Erreur lors de la modération de l\'image', {
        // Champ renommé pour éviter les conflits potentiels et assurer une journalisation détaillée
        error_info: errorDetails,
        original_error_raw_type: typeof error, // Type brut de l'erreur originale
        is_error_instance: error instanceof Error, // Vrai si l'erreur originale est une instance de Error
        was_empty_literal_object: error && typeof error === 'object' && Object.keys(error).length === 0 && !(error instanceof Error), // Vrai si c'était {}
        userId: content.userId,
        contentType: content.type,
        contentId: content.contentId
      });

      // En cas d'erreur, retourner un résultat par défaut pour la modération d'image
      return {
        isSafe: true, // Par défaut, considérer l'image comme sûre en cas d'erreur technique
        score: 0.1,
        categories: {
          adult: false,
          violence: false,
          harassment: false,
          hateSpeech: false,
          selfHarm: false,
          sexual: false,
          dangerousContent: false,
          unknownRisk: false
        },
        moderationId: `error-image-${Date.now()}`,
        description: "Erreur lors de l'analyse de l'image"
      };
    }
  }

  /**
   * Compresse le prompt pour réduire sa taille sans altérer son contenu sémantique
   * @param prompt Le prompt original à compresser
   * @returns Le prompt compressé
   */
  private compressPrompt(prompt: string): string {
    // Remplacer les multiples espaces par un seul
    let compressed = prompt.replace(/\s{2,}/g, ' ');

    // Remplacer les multiples sauts de ligne par un seul
    compressed = compressed.replace(/\n{3,}/g, '\n\n');

    // Supprimer les espaces au début des lignes tout en préservant l'indentation minimale nécessaire
    compressed = compressed.replace(/^[ \t]+/gm, '  ');

    // Supprimer les espaces à la fin des lignes
    compressed = compressed.replace(/[ \t]+$/gm, '');

    // Optimisation supplémentaire: remplacer les sauts de ligne isolés par des espaces dans certains contextes
    compressed = compressed.replace(/([^\n])\n([^\n])/g, '$1 $2');

    return compressed;
  }

  /**
   * Prépare le prompt pour l'API de modération d'image
   */
  private async prepareImagePrompt(content: ContentToModerate): Promise<string> {
    // Tenter de récupérer les services de l'utilisateur si userId est fourni
    let userServices = [];
    let userServiceTypes = '';

    if (content.userId) {
      try {
        // Récupérer les services actifs de l'utilisateur depuis Supabase
        const { data: services, error } = await supabase
          .from('user_services')
          .select('category_id, subcategory_id, titre, description')
          .eq('user_id', content.userId)
          .eq('statut', 'actif');

        if (!error && services && services.length > 0) {
          userServices = services;

          // Créer une liste des services spécifiques de l'utilisateur - sans les descriptions
          userServiceTypes = `Services spécifiques proposés par cet utilisateur:\n`;
          services.forEach((service, index) => {
            userServiceTypes += `    ${index + 1}. ${service.titre}\n`;
          });

          logger.info('Modération d\'image : Services de l\'utilisateur récupérés', {
            userId: content.userId,
            serviceCount: services.length
          });
        } else {
          logger.info('Modération d\'image : Pas de services trouvés pour l\'utilisateur', {
            userId: content.userId
          });
        }
      } catch (error) {
        logger.error('Modération d\'image : Erreur lors de la récupération des services de l\'utilisateur', {
          error,
          userId: content.userId
        });
      }
    }

    // Récupérer uniquement les catégories principales sans les sous-catégories
    let categoriesText = "";
    try {
      // Récupérer toutes les catégories
      const categories = await getServiceCategories();

      // Construire le texte des catégories pour le prompt - version concise
      categoriesText = "CATÉGORIES DE SERVICES SUR JOBPARTIEL:\n";
      categories.forEach(category => {
        categoriesText += `    ${category.id}. ${category.nom}\n`;
      });

      logger.info('Modération d\'image : Uniquement les catégories principales récupérées', {
        categoriesCount: categories.length
      });
    } catch (error) {
      logger.error('Modération d\'image : Erreur lors de la récupération des catégories', {
        error
      });

      // En cas d'erreur, utiliser un texte par défaut simplifié
      categoriesText = `PRINCIPALES CATÉGORIES DE SERVICES:
      1. Jardinage - Tonte, taille, désherbage, plantation
      2. Bricolage - Peinture, plomberie, électricité
      3. Garde d'animaux - À domicile, promenade, visites
      4. Services à la personne - Garde d'enfants, aide aux seniors
      5. Nettoyage & Ménage - Ménage, nettoyage, rangement, désencombrement
      6. Débarras & Déménagement - Vide-maison, déménagement, transport objets`;
    }

    const basePrompt = `Modérateur image JobPartiel - Analyse stricte:

    ${userServiceTypes ? `SERVICES:\n${userServiceTypes}\n` : ''}

    RÈGLES CRITIQUES:
    1. JSON uniquement
    2. Contenu violent/choquant = isSafe false
    3. Score >= 0.7 si inapproprié
    4. Animal mort/blessé = violence true
    5. Qualité technique prioritaire
    ${userServices.length > 0 ? '6. Services utilisateur = info seulement (non bloquant)' : ''}

    ATTENTION :
    - Toute image contenant une arme à feu (pistolet, revolver, fusil, mitraillette, etc.), un couteau, une arme blanche, une bombe, un explosif, ou tout objet dangereux (batte, barre de fer, etc.) doit être catégorisée comme dangerousContent=true ET violence=true ET isSafe=false.
    - Toute scène de violence, menace, bagarre, sang, blessure, ou posture menaçante doit être catégorisée comme violence=true ET isSafe=false.
    - Exemples à REJETER : photo de pistolet, fusil, couteau, arme posée sur une table, personne tenant une arme, scène de combat, sang, blessure, arme factice réaliste, etc.
    - Exemples à ACCEPTER : outils de jardinage, outils de bricolage, ustensiles de cuisine SANS contexte menaçant ou violent.
    - Si DOUTE sur la présence d'une arme ou d'une scène violente, REJETER (isSafe=false).
    - Dans le JSON tu dois toujours mettre en Français les retours des champs : description, serviceType,relevantToUserServices, improvementSuggestions.

    QUALITÉ:
    1. Clarté image
    2. Cadrage pro
    3. Aspect pro
    4. Pertinence (non bloquante)

    ${categoriesText}

    REJETER SI:
    - Armes/danger (voir ATTENTION ci-dessus)
    - Violence/mort
    - Contenu adulte
    - Harcèlement
    - Drogues/illégal
    - Qualité basse
    - Trompeur
    - Copyright

    IMPORTANT POUR LA PERTINENCE:
    - Une image peut illustrer un besoin différent des services proposés par l'utilisateur
    - Par exemple: un utilisateur proposant des services de création de site web peut poster une mission pour chercher un jobbeur pour tondre sa pelouse
    - Ne pas rejeter une image uniquement parce qu'elle ne correspond pas aux services de l'utilisateur
    - La pertinence doit être évaluée par rapport au contexte de la mission/publication, pas aux services de l'utilisateur

    SUGGESTIONS D'AMÉLIORATION:
    - Ne jamais mentionner les services spécifiques de l'utilisateur dans les suggestions
    - Donner des conseils génériques sur la qualité, la sécurité ou le contenu approprié
    - Exemple: "Veuillez télécharger une image appropriée pour la plateforme" au lieu de "image pertinente pour vos services de X"
    ${content.text ? '- Tel/adresse\n    - Spam' : ''}

    ${content.text ? `TEXTE: "${content.text}"` : ''}

    IMPORTANT : Si l'image ne présente aucun risque et est parfaitement appropriée pour la plateforme, mets la catégorie clean à true.

    FORMAT:
    {
      "isSafe": boolean,
      "score": number,
      "categories": {
        "adult": boolean,
        "violence": boolean,
        "harassment": boolean,
        "hateSpeech": boolean,
        "selfHarm": boolean,
        "sexual": boolean,
        "dangerousContent": boolean,
        "poorQuality": boolean,
        "irrelevantContent": boolean,
        "misleadingContent": boolean${content.text ? `,
        "spam": boolean,
        "phoneSpam": boolean,
        "addressSpam": boolean` : ''},
        "unknownRisk": boolean,
        "clean": boolean // true si l'image est parfaitement appropriée et sans aucun risque
      },
      "qualityAssessment": {
        "overall": number,
        "clarity": number,
        "relevance": number,
        "professionalAppearance": number
      },
      "description": string,
      "serviceType": string,
      "relevantToUserServices": boolean, // Information uniquement, ne pas utiliser pour rejeter
      "improvementSuggestions": string // Conseils génériques, ne pas mentionner les services utilisateur
    }`;

    // Compresser le prompt avant de le retourner
    const compressedPrompt = this.compressPrompt(basePrompt);

    // Logger le taux de compression obtenu
    const compressionRatio = ((basePrompt.length - compressedPrompt.length) / basePrompt.length * 100).toFixed(2);
    logger.info('Modération d\'image : Compression du prompt', {
      userId: content.userId,
      contentId: content.contentId,
      originalSize: basePrompt.length,
      compressedSize: compressedPrompt.length,
      compressionRatio: `${compressionRatio}%`
    });

    return compressedPrompt;
  }
  private validateApiResponse(response: Partial<ModerationResult>): boolean {
    // Vérifier que les champs obligatoires sont présents
    if (response.isSafe === undefined || response.isSafe === null) {
      logger.warn('Champ isSafe manquant ou invalide dans la réponse de l\'API');
      return false;
    }

    if (response.score === undefined || response.score === null || typeof response.score !== 'number' || isNaN(response.score)) {
      logger.warn('Champ score manquant ou invalide dans la réponse de l\'API');
      return false;
    }

    // Vérifier que le score est dans la plage valide [0, 1]
    if (response.score < 0 || response.score > 1) {
      logger.warn(`Score hors plage valide: ${response.score}`);
      return false;
    }

    // Vérifier que les catégories sont présentes et ont le bon format
    if (!response.categories) {
      logger.warn('Catégories manquantes dans la réponse de l\'API');
      return false;
    }

    // Vérifier que toutes les catégories attendues sont présentes et sont des booléens
    const requiredCategories = [
      'harassment', 'hateSpeech', 'sexualContent', 'violence',
      'selfHarm', 'illegalActivity', 'spam', 'phoneSpam', 'addressSpam', 'unknownRisk'
    ];

    for (const category of requiredCategories) {
      if (response.categories[category as keyof typeof response.categories] === undefined) {
        logger.warn(`Catégorie ${category} manquante dans la réponse de l'API`);
        return false;
      }

      const value = response.categories[category as keyof typeof response.categories];
      if (typeof value !== 'boolean') {
        logger.warn(`Catégorie ${category} n'est pas un booléen: ${typeof value}`);
        return false;
      }
    }

    // Vérifier que flaggedText est un tableau, une chaîne ou undefined/null
    if (response.flaggedText !== undefined && response.flaggedText !== null) {
      // Si c'est une chaîne, c'est acceptable (sera converti en tableau plus tard)
      if (typeof response.flaggedText === 'string') {
        // C'est acceptable, on le convertira en tableau plus tard
        const flaggedTextStr = response.flaggedText as string;
        logger.info(`Modération : flaggedText est une chaîne, sera converti en tableau: "${flaggedTextStr.substring(0, 50)}${flaggedTextStr.length > 50 ? '...' : ''}"`);
      }
      // Si ce n'est pas un tableau ni une chaîne, c'est invalide
      else if (!Array.isArray(response.flaggedText)) {
        logger.info(`Modération : flaggedText n'est ni un tableau ni une chaîne: ${typeof response.flaggedText}`);
        return false;
      }
      // Si c'est un tableau, vérifier que tous les éléments sont des chaînes
      else {
        for (const item of response.flaggedText as string[]) {
          if (typeof item !== 'string') {
            logger.warn(`Un élément de flaggedText n'est pas une chaîne: ${typeof item}`);
            return false;
          }
        }
      }
    }

    return true;
  }

  /**
   * Vérification basique du contenu (utilisée comme première vérification et fallback)
   * Retourne true si du contenu inapproprié est détecté
   */
  private basicContentCheck(text: string): boolean {
    const originalText = text; // Garder une copie du texte original pour la détection de numéros et adresses
    const lowercaseText = text.toLowerCase()
      .replace(/[.,:;!?*_\-\'"«»()[\]{}|/\\<>]/g, ' ') // Remplacer la ponctuation par des espaces
      .replace(/\s+/g, ' ')                          // Normaliser les espaces
      .replace(/[0àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ1234567890]/g, function(match) {
        // Remplacer les caractères accentués et les chiffres utilisés pour contourner les filtres
        const accentMap: {[key: string]: string} = {
          'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a', 'æ': 'ae',
          'ç': 'c', 'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e', 'ì': 'i', 'í': 'i',
          'î': 'i', 'ï': 'i', 'ð': 'd', 'ñ': 'n', 'ò': 'o', 'ó': 'o', 'ô': 'o',
          'õ': 'o', 'ö': 'o', 'ø': 'o', 'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
          'ý': 'y', 'þ': 'th', 'ÿ': 'y',
          '0': 'o', '1': 'i', '3': 'e', '4': 'a', '5': 's', '7': 't', '8': 'b'
        };
        return accentMap[match] || match;
      });

    // Exception : ignorer les phrases du type "chez vous", "chez moi", etc. sans nom propre ou adresse complète
    const chezSimplePattern = /\bchez (vous|moi|lui|elle|eux|nous|toi|soi)\b/i;
    // Si la phrase contient uniquement "chez vous" (ou variante) et pas de nom propre après "chez"
    if (chezSimplePattern.test(originalText) && !/\bchez [a-zàáâãäåæçèéêë\s]+/i.test(originalText.replace(/\bchez (vous|moi|lui|elle|eux|nous|toi|soi)\b/gi, ''))) {
      return false;
    }

    // Détection de numéros de téléphone français
    // Formats: 06 12 34 56 78, 0612345678, +33 6 12 34 56 78, +336 12 34 56 78, etc.
    const phonePatterns = [
      /(\+33|0033|0)[\s.-]?[1-9][\s.-]?(\d{2}[\s.-]?){4}/g,  // Format standard français
      /(\+33|0033|0)[1-9]\d{8}/g,                            // Format sans espaces
      /(\+33|0033|0)[1-9][\s.-]?\d{2}[\s.-]?\d{2}[\s.-]?\d{2}[\s.-]?\d{2}/g, // Format avec séparateurs
      /(\d{2}[\s.-]?){5}/g                                   // Séquence de 10 chiffres avec séparateurs
    ];

    // Vérifier les numéros de téléphone
    let hasPhoneNumber = false;
    for (const pattern of phonePatterns) {
      if (pattern.test(originalText)) {
        logger.info(`Modération : Numéro de téléphone détecté dans le texte : ${originalText}`);
        hasPhoneNumber = true;
        break;
      }
    }

    // Détection d'adresses postales françaises
    // Recherche de patterns comme "X rue/avenue/boulevard Y, XXXXX Ville"
    const addressPatterns = [
      /\d+[\s,]+(rue|avenue|av|boulevard|bvd|blvd|chemin|impasse|place|allée|allee|cours|quai)[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+[\s,]+\d{5}[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i,
      /\d{5}[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i,  // Code postal suivi d'une ville
      /\b(rue|avenue|av|boulevard|bvd|blvd|chemin|impasse|place|allée|allee|cours|quai)\b[\s,]+[a-zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ\s]+/i  // Type de voie suivi d'un nom
    ];

    // Expressions courantes à exclure pour éviter les faux positifs
    const commonExpressions = [
      /\bmise en place\b/i,
      /\ben place\b/i,
      /\bau cours de\b/i,
      /\bau cours du\b/i,
      /\bau cours des\b/i,
      /\bparcours\b/i,
      /\bdiscours\b/i,
      /\brecours\b/i,
      /\bconcours\b/i,
      /\bsecours\b/i
    ];

    // Vérifier d'abord si le texte contient des expressions courantes à exclure
    let hasCommonExpression = false;
    for (const commonExpr of commonExpressions) {
      if (commonExpr.test(originalText)) {
        hasCommonExpression = true;
        break;
      }
    }

    // Vérifier les adresses seulement si aucune expression courante n'est détectée
    let hasAddress = false;
    if (!hasCommonExpression) {
      for (const pattern of addressPatterns) {
        if (pattern.test(originalText)) {
          logger.info(`Modération : Adresse postale détectée dans le texte : ${originalText}`);
          hasAddress = true;
          break;
        }
      }
    }
    // Si on a détecté un numéro de téléphone ou une adresse, on considère le contenu comme inapproprié
    if (hasPhoneNumber || hasAddress) {
      // Nous retournons simplement true ici, la fonction appelante (moderateContent)
      // créera le résultat de modération approprié avec les catégories correctes
      logger.info(`Modération : Information de contact détectée (téléphone: ${hasPhoneNumber}, adresse: ${hasAddress})`);
      return true;
    }

    // Utilisation de la liste centralisée de mots inappropriés
    // importée depuis le module badwords.ts

    // Vérifier les mots avec limites de mots pour éviter les faux positifs
    for (const word of badWords) {
      // Utiliser une regex avec limites de mots pour une détection plus précise
      const wordBoundaryRegex = new RegExp(`\\b${word}\\b`, 'i');
      if (wordBoundaryRegex.test(lowercaseText)) {
        logger.info(`Modération : Mot interdit trouvé (avec limites de mots) : "${word}" dans le texte : "${text}"`);
        return true;
      }

      // Pour les mots très courts (3 lettres ou moins), vérifier aussi s'ils sont entourés de caractères spéciaux
      // car ils peuvent être utilisés pour contourner la détection par limites de mots
      if (word.length <= 3) {
        const specialBoundaryRegex = new RegExp(`[^a-z0-9]${word}[^a-z0-9]|^${word}[^a-z0-9]|[^a-z0-9]${word}$|^${word}$`, 'i');
        if (specialBoundaryRegex.test(lowercaseText)) {
          logger.info(`Modération : Mot court interdit trouvé (avec limites spéciales) : "${word}" dans le texte : "${text}"`);
          return true;
        }
      }
    }

    // Vérifier les expressions inappropriées (regex)
    for (const expr of inappropriateExpressions) {
      if (expr.test(lowercaseText)) {
        logger.info(`Modération : Expression inappropriée détectée (regex) : ${expr} dans le texte : "${text}"`);
        return true;
      }
    }

    return false;
  }

  /**
   * Enregistre le résultat de modération en base de données
   * N'enregistre que les contenus réellement problématiques (non sûrs)
   */
  private async saveModerationResult(content: ContentToModerate, result: ModerationResult | ImageModerationResult): Promise<void> {
    try {
      // Déterminer explicitement si c'est une modération d'image
      let isImageModeration = 'description' in result ||
        !!content.imageBuffer ||
        !!content.imageUrl ||
        !!content.tempImagePath ||
        content.type === 'gallery' ||
        content.type === 'gallery_cover' ||
        content.type === 'featured' ||
        content.type === 'mission_assistant' ||
        content.type === 'avatar' ||
        content.type === 'profile_picture' ||
        content.type === 'banner_picture';

      // Correction spécifique pour les modérations IA (gen-)
      if (result.moderationId && result.moderationId.startsWith('gen-')) {
        // Si le type de contenu est une image, c'est une modération d'image IA
        if ([
          'gallery',
          'gallery_cover',
          'featured',
          'mission_assistant',
          'avatar',
          'profile_picture',
          'banner_picture'
        ].includes(content.type)) {
          isImageModeration = true;
        } else {
          isImageModeration = false;
        }
      }

      // En cas d'erreur, utiliser le préfixe error-image-
      if (result.moderationId.startsWith('error-') && isImageModeration) {
        result.moderationId = `error-image-${Date.now()}`;
      }

      // Log détaillé au début de la fonction pour débogage
      logger.info('Début de saveModerationResult', {
        contentType: content.type,
        contentId: content.contentId,
        userId: content.userId,
        isImageModeration: isImageModeration,
        hasImageBuffer: !!content.imageBuffer,
        hasImageUrl: !!content.imageUrl,
        hasTempImagePath: !!content.tempImagePath,
        isDescriptionInResult: 'description' in result,
        isSafe: result.isSafe,
        score: result.score,
        hasCategories: Object.values(result.categories).some(value => value === true),
        categoriesKeys: Object.keys(result.categories),
        resultKeys: Object.keys(result)
      });

      // Normalisation finale avant enregistrement
      // Assurer la cohérence entre phoneSpam/addressSpam et spam
      if ('phoneSpam' in result.categories && result.categories.phoneSpam ||
          'addressSpam' in result.categories && result.categories.addressSpam) {
        if ('spam' in result.categories) {
          result.categories.spam = true;
        }
      }

      // Vérifier si le contenu contient des mots interdits (seulement pour les modérations de texte)
      const hasBasicViolation = 'text' in content ? this.basicContentCheck(content.text) : false;

      // Normaliser les résultats pour les modérations de texte
      if ('flaggedText' in result) {
        // C'est une modération de texte standard
        const normalizedResult = this.normalizeApiResult(result as ModerationResult, (result as ModerationResult).flaggedText || [], hasBasicViolation);
        result.isSafe = normalizedResult.isSafe;
        result.score = normalizedResult.score;
        (result as ModerationResult).flaggedText = normalizedResult.flaggedTextArray;

        logger.info('Modération de texte normalisée', {
          contentId: content.contentId,
          originalIsSafe: result.isSafe,
          normalizedIsSafe: normalizedResult.isSafe,
          originalScore: result.score,
          normalizedScore: normalizedResult.score,
          flaggedTextCount: normalizedResult.flaggedTextArray.length
        });
      } else if ('description' in result) {
        // C'est une modération d'image
        logger.info('Modération d\'image détectée', {
          contentId: content.contentId,
          description: result.description?.substring(0, 50) + (result.description && result.description.length > 50 ? '...' : ''),
          serviceType: result.serviceType,
          relevantToUserServices: result.relevantToUserServices,
          hasQualityAssessment: !!result.qualityAssessment,
          hasImprovementSuggestions: !!result.improvementSuggestions
        });
      }

      // Log détaillé pour les résultats incohérents
      const hasDetectedCategories = Object.values(result.categories).some(value => value === true);

      // Si le score est élevé mais qu'aucune catégorie n'est détectée, ajouter une catégorie par défaut
      if (result.score > 0.5 && !hasDetectedCategories) {
        logger.warn(`Modération : Incohérence dans saveModerationResult: score élevé (${result.score}) mais aucune catégorie détectée - Ajout d'unknownRisk`);
        result.categories.unknownRisk = true;
        logger.warn('Modération : Score élevé sans catégorie détectée, ajout de unknownRisk', { score: result.score, categories: result.categories });
      }

      // Vérifier à nouveau après correction potentielle
      const hasDetectedCategoriesAfterFix = Object.values(result.categories).some(value => value === true);

      // Vérifier les incohérences potentielles
      const hasPhoneOrAddressSpam =
        ('phoneSpam' in result.categories && result.categories.phoneSpam) ||
        ('addressSpam' in result.categories && result.categories.addressSpam);

      if ((result.score < 0.1 && !result.isSafe) ||
          (result.score > 0.7 && result.isSafe) ||
          (result.score < 0.3 && hasDetectedCategoriesAfterFix && !hasPhoneOrAddressSpam)) {
        logger.info('Modération : Résultat de modération potentiellement incohérent après normalisation', {
          contentType: content.type,
          contentId: content.contentId,
          score: result.score,
          isSafe: result.isSafe,
          hasDetectedCategories: hasDetectedCategoriesAfterFix,
          categories: result.categories
        });
      }
      // N'enregistrer que les contenus non sûrs pour éviter de remplir la base de données
      // OU forcer l'enregistrement pour les modérations d'images (pour le débogage)
      if (!result.isSafe || isImageModeration) {
        logger.info('Préparation des données pour insertion dans content_moderation_logs', {
          contentType: content.type,
          contentId: content.contentId,
          isImageModeration: isImageModeration,
          hasImageBuffer: !!content.imageBuffer,
          hasImageUrl: !!content.imageUrl,
          hasTempImagePath: !!content.tempImagePath,
          isDescriptionInResult: 'description' in result
        });

        // Préparer les données de base pour l'insertion
        const insertData: any = {
          content_type: content.type,
          content_id: content.contentId,
          user_id: content.userId,
          is_safe: result.isSafe,
          score: result.score,
          categories: result.categories,
          moderation_id: result.moderationId,
          created_at: new Date().toISOString(),
          is_image_moderation: isImageModeration
        };

        // Log des données de base
        logger.info('Données de base préparées', {
          contentId: content.contentId,
          insertDataKeys: Object.keys(insertData),
          contentType: insertData.content_type,
          userId: insertData.user_id,
          score: insertData.score
        });

        // Ajouter les champs spécifiques selon le type de modération
        if ('flaggedText' in result) {
          // Modération de texte standard
          insertData.flagged_text = result.flaggedText || [];
          logger.info('Champs spécifiques ajoutés pour modération de texte', {
            contentId: content.contentId,
            flaggedTextCount: insertData.flagged_text.length
          });
        } else if ('description' in result || isImageModeration) {
          // Modération d'image
          const imageResult = result as ImageModerationResult;
          logger.info('Préparation des champs spécifiques pour modération d\'image', {
            contentId: content.contentId,
            hasDescription: !!imageResult.description,
            hasServiceType: !!imageResult.serviceType,
            hasRelevance: imageResult.relevantToUserServices !== undefined,
            hasQualityAssessment: !!imageResult.qualityAssessment,
            hasImprovementSuggestions: !!imageResult.improvementSuggestions,
            isExplicitImageModeration: 'description' in result,
            isImplicitImageModeration: isImageModeration && !('description' in result)
          });

          // Ajouter les champs spécifiques aux images
          insertData.flagged_text = imageResult.description ? [imageResult.description] : [];

          if (imageResult.serviceType) {
            insertData.service_type = imageResult.serviceType;
          }

          if (imageResult.relevantToUserServices !== undefined) {
            insertData.relevant_to_user_services = imageResult.relevantToUserServices;
          }

          if (imageResult.qualityAssessment) {
            insertData.quality_assessment = imageResult.qualityAssessment;
          }

          if (imageResult.improvementSuggestions) {
            insertData.improvement_suggestions = imageResult.improvementSuggestions;
          }

          // Indiquer explicitement que c'est une modération d'image
          insertData.is_image_moderation = true;

          // Ajout du chemin temporaire de l'image pour affichage/suppression
          if (content.tempImagePath) {
            insertData.image_path = content.tempImagePath;
          }

          // Si le moderationId ne commence pas par 'image-' ou 'gen-', le préfixer
          if (insertData.moderation_id &&
              !insertData.moderation_id.startsWith('image-') &&
              !insertData.moderation_id.startsWith('gen-')) {
            insertData.moderation_id = `image-${insertData.moderation_id}`;
            logger.info('Préfixage du moderationId pour une modération d\'image', {
              contentId: content.contentId,
              originalId: result.moderationId,
              newId: insertData.moderation_id
            });
          }

          logger.info('Champs spécifiques ajoutés pour modération d\'image', {
            contentId: content.contentId,
            flaggedTextCount: insertData.flagged_text.length,
            serviceType: insertData.service_type,
            relevantToUserServices: insertData.relevant_to_user_services,
            hasQualityAssessment: !!insertData.quality_assessment,
            hasImprovementSuggestions: !!insertData.improvement_suggestions,
            isImageModeration: insertData.is_image_moderation
          });
        }

        // Log détaillé avant insertion pour débogage
        logger.info('Tentative d\'insertion dans content_moderation_logs', {
          contentType: content.type,
          contentId: content.contentId,
          isImageModeration: 'description' in result,
          insertDataKeys: Object.keys(insertData)
        });

        try {
          // Insérer dans la base de données avec gestion d'erreur améliorée
          const { error } = await supabase
            .from('content_moderation_logs')
            .insert(insertData);

          if (error) {
            // Log détaillé de l'erreur
            logger.error('Erreur lors de l\'enregistrement du résultat de modération', {
              errorCode: error.code,
              errorMessage: error.message,
              errorDetails: error.details,
              errorHint: error.hint,
              contentType: content.type,
              contentId: content.contentId,
              isImageModeration: 'description' in result,
              insertDataSummary: {
                content_type: insertData.content_type,
                content_id: insertData.content_id,
                is_image_moderation: insertData.is_image_moderation,
                service_type: insertData.service_type
              }
            });

            // Tentative alternative avec des champs minimaux en cas d'erreur
            if (error.code === '23502' || error.message?.includes('violates not-null constraint')) {
              logger.info('Tentative alternative avec champs minimaux', { contentId: content.contentId });

              // Créer un ensemble minimal de données
              const minimalData = {
                content_type: content.type,
                content_id: content.contentId,
                user_id: content.userId,
                is_safe: result.isSafe,
                score: result.score,
                categories: result.categories,
                moderation_id: 'description' in result ? `fallback-${Date.now()}` : (result as ModerationResult).moderationId,
                flagged_text: 'description' in result ? [result.description || ''] : (result as ModerationResult).flaggedText || [],
                is_image_moderation: 'description' in result
              };

              const { error: fallbackError } = await supabase
                .from('content_moderation_logs')
                .insert(minimalData);

              if (fallbackError) {
                logger.error('Échec de la tentative alternative', {
                  error: fallbackError,
                  minimalData
                });

                // Si c'est une modération d'image, essayer la méthode directe
                if ('description' in result) {
                  await this.saveImageModerationDirectly(content, result as ImageModerationResult);
                }
              } else {
                logger.info('Insertion alternative réussie avec champs minimaux', { contentId: content.contentId });
              }
            } else if ('description' in result) {
              // Si c'est une erreur pour une modération d'image, essayer la méthode directe
              await this.saveImageModerationDirectly(content, result as ImageModerationResult);
            }
          } else {
            logger.info('Résultat de modération enregistré avec succès', {
              contentType: content.type,
              contentId: content.contentId,
              isImageModeration: 'description' in result,
              isSafe: result.isSafe,
              score: result.score
            });
          }
        } catch (insertError) {
          logger.error('Exception lors de l\'insertion dans content_moderation_logs', {
            error: insertError,
            contentType: content.type,
            contentId: content.contentId
          });
        }

        // Si le contenu est jugé très dangereux (score > 0.7), créer automatiquement un signalement
        // MAIS seulement si l'ID du contenu n'est pas temporaire ET si l'ID de l'utilisateur est disponible
        if (result.score > 0.7 && !content.contentId.startsWith('temp-') && content.userId) {
          await this.createAutomaticReport(content, result as ModerationResult);
        }
      } else {
        logger.warn('Contenu jugé sûr, non enregistré dans la base de données', {
          contentType: content.type,
          contentId: content.contentId,
          score: result.score
        });
      }
    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement du résultat de modération', {
        error,
        contentType: content.type,
        contentId: content.contentId
      });
    }
  }

  /**
   * Enregistre directement une modération d'image dans la base de données
   * Cette fonction est utilisée comme fallback en cas d'échec de saveModerationResult
   */
  private async saveImageModerationDirectly(content: ContentToModerate, result: ImageModerationResult): Promise<void> {
    try {
      // Générer un ID de modération unique avec préfixe image-
      const moderationId = result.moderationId &&
                          (result.moderationId.startsWith('image-') || result.moderationId.startsWith('gen-'))
                          ? result.moderationId
                          : `image-direct-${Date.now()}`;

      // Créer un objet minimal pour l'insertion
      const insertData = {
        content_type: content.type,
        content_id: content.contentId,
        user_id: content.userId,
        is_safe: result.isSafe,
        score: result.score,
        categories: result.categories,
        moderation_id: moderationId,
        flagged_text: result.description ? [result.description] : ['Image modérée'],
        is_image_moderation: true,
        service_type: result.serviceType || null,
        relevant_to_user_services: result.relevantToUserServices || null,
        quality_assessment: result.qualityAssessment || null,
        improvement_suggestions: result.improvementSuggestions || null,
        created_at: new Date().toISOString(),
        ...(content.tempImagePath ? { image_path: content.tempImagePath } : {})
      };

      logger.info('Tentative d\'enregistrement direct de modération d\'image', {
        contentId: content.contentId,
        contentType: content.type,
        userId: content.userId
      });

      // Insérer directement dans la base de données
      const { error } = await supabase
        .from('content_moderation_logs')
        .insert(insertData);

      if (error) {
        logger.error('Échec de l\'enregistrement direct de modération d\'image', {
          error,
          contentId: content.contentId
        });
      } else {
        logger.info('Enregistrement direct de modération d\'image réussi', {
          contentId: content.contentId
        });
      }
    } catch (error) {
      logger.error('Exception lors de l\'enregistrement direct de modération d\'image', {
        error,
        contentId: content.contentId
      });
    }
  }

  /**
   * Crée un signalement automatique pour un contenu jugé inapproprié
   * @param content Le contenu à signaler (doit contenir userId)
   * @param result Le résultat de la modération
   */
  private async createAutomaticReport(content: ContentToModerate, result: ModerationResult | ImageModerationResult): Promise<void> {
    // Vérification préliminaire : si l'ID utilisateur n'est pas disponible, on ne crée pas de signalement
    if (!content.userId) {
      logger.warn('Signalement automatique ignoré : ID utilisateur manquant', {
        contentId: content.contentId,
        contentType: content.type
      });
      return;
    }
    try {
      // Vérifier si c'est une image temporaire
      const isTempImage = content.contentId.startsWith('image-temp-') || content.contentId.includes('temp');
      const isImageModeration = 'description' in result || content.type.includes('gallery') ||
                               content.type === 'featured' || content.type.includes('cover') ||
                               content.type === 'avatar' || content.type === 'profile_picture' ||
                               content.type === 'banner_picture';

      // Pour les contenus non-image, vérifier que le contentId est bien un UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!isTempImage && !uuidRegex.test(content.contentId)) {
        logger.warn('Signalement automatique ignoré : contentId non UUID et non image temporaire', {
          contentId: content.contentId,
          contentType: content.type,
          isImageModeration
        });
        return;
      }

      // Pour les images temporaires, on ne crée pas de signalement immédiatement
      // car l'image n'est pas encore enregistrée dans la base de données
      if (isTempImage && isImageModeration) {
        logger.info('Image temporaire détectée, le signalement sera créé après enregistrement dans la base de données', {
          contentId: content.contentId,
          contentType: content.type,
          score: result.score
        });

        // Stocker les informations de modération dans Redis pour traitement ultérieur
        // quand l'image sera enregistrée avec un UUID permanent
        try {
          const redisKey = `temp_moderation:${content.contentId}`;
          const moderationData = {
            content: {
              type: content.type,
              userId: content.userId,
              text: content.text || null
            },
            result: {
              score: result.score,
              isSafe: result.isSafe,
              categories: result.categories,
              description: 'description' in result ? result.description : null
            },
            temp_image_path: content.tempImagePath || null,
            timestamp: new Date().toISOString()
          };

          await redis.set(redisKey, JSON.stringify(moderationData), 'EX', 3600); // expire après 1 heure
          logger.info('Informations de modération stockées dans Redis pour traitement ultérieur', {
            redisKey,
            contentId: content.contentId,
            score: result.score
          });
          return;
        } catch (redisError) {
          logger.error('Erreur lors du stockage des informations de modération dans Redis', {
            error: redisError,
            contentId: content.contentId
          });
          // Continuer avec la méthode standard si Redis échoue
        }
      }
      // Déterminer le type de contenu pour le signalement
      const contentTypeMap: Record<string, string> = {
        'mission': 'mission',
        'comment': 'comment',
        'profile': 'profile',
        'service_title': 'service',
        'service_description': 'service',
        'gallery': 'gallery',
        'gallery_cover': 'gallery',
        'featured': 'profile',
        'mission_assistant': 'mission',
        'gallery_name': 'gallery',
        'gallery_description': 'gallery',
        'mission_title': 'mission',
        'mission_description': 'mission',
        'review': 'review'
      };

      // Récupérer l'ID de l'utilisateur qui a créé le contenu signalé
      let reported_user_id = content.userId;

      // Si l'ID de l'utilisateur n'est pas disponible dans content, essayer de le récupérer depuis la base de données
      if (!reported_user_id) {
        const mappedContentType = contentTypeMap[content.type];

        // Déterminer la table et la colonne à utiliser pour récupérer l'ID de l'utilisateur
        let table = '';
        let userIdColumn = 'user_id';

        if (mappedContentType === 'mission') {
          table = 'user_missions';
        } else if (mappedContentType === 'comment') {
          table = 'user_mission_comments';
        } else if (mappedContentType === 'profile') {
          table = 'user_profil';
        } else if (mappedContentType === 'review') {
          table = 'user_reviews';
          userIdColumn = 'author_id';
        } else if (mappedContentType === 'gallery') {
          table = 'user_galleries';
        } else if (mappedContentType === 'service') {
          table = 'user_services';
        }

        // Récupérer l'ID de l'utilisateur depuis la base de données
        if (table) {
          try {
            const { data, error } = await supabase
              .from(table)
              .select(userIdColumn)
              .eq('id', content.contentId)
              .single();

            if (data && !error) {
              // Utiliser une approche plus sûre pour accéder aux propriétés dynamiques
              if (userIdColumn === 'user_id' && 'user_id' in data) {
                reported_user_id = data.user_id as string;
              } else if (userIdColumn === 'author_id' && 'author_id' in data) {
                reported_user_id = data.author_id as string;
              }

              logger.info('ID utilisateur récupéré pour le signalement automatique', {
                contentType: mappedContentType,
                contentId: content.contentId,
                userId: reported_user_id
              });
            } else if (error) {
              logger.warn(`Impossible de récupérer l'ID utilisateur pour le signalement automatique: ${error.message}`, {
                contentType: mappedContentType,
                contentId: content.contentId,
                table,
                userIdColumn
              });
            }
          } catch (err) {
            logger.error('Erreur lors de la récupération de l\'ID utilisateur pour le signalement automatique', {
              error: err,
              contentType: mappedContentType,
              contentId: content.contentId,
              table
            });
          }
        }
      }

      // Créer un signalement automatique
      const { error } = await supabase
        .from('reported_content')
        .insert({
          content_type: contentTypeMap[content.type],
          content_id: content.contentId,
          reason: `Contenu potentiellement inapproprié détecté automatiquement (score: ${result.score})`,
          reported_by: null, // Utiliser NULL au lieu de 'system'
          reported_user_id: reported_user_id, // Ajouter l'ID de l'utilisateur qui a créé le contenu
          status: 'pending',
          content_snapshot: content.text,
          system_generated: true,
          moderation_data: {
            score: result.score,
            categories: result.categories,
            flaggedText: 'flaggedText' in result ? result.flaggedText || [] :
                         'description' in result && result.description ? [result.description] : []
          }
        });

      if (error) {
        logger.error('Erreur lors de la création du signalement automatique', {
          error,
          contentType: content.type,
          contentId: content.contentId,
          reported_user_id
        });
      } else {
        logger.info('Signalement automatique créé avec succès', {
          contentType: contentTypeMap[content.type],
          contentId: content.contentId,
          reported_user_id
        });
      }
    } catch (error) {
      logger.error('Erreur lors de la création du signalement automatique', {
        error,
        contentType: content.type,
        contentId: content.contentId
      });
    }
  }
  /**
   * Crée un signalement automatique pour une image qui a été modérée avec un ID temporaire
   * et qui est maintenant enregistrée avec un UUID permanent
   * @param tempImageId ID temporaire de l'image
   * @param permanentImageId UUID permanent de l'image
   * @param contentType Type de contenu (featured, gallery, etc.)
   * @param userId ID de l'utilisateur propriétaire de l'image
   */
  public async createDeferredImageReport(tempImageId: string, permanentImageId: string, contentType: string, userId: string): Promise<void> {
    try {
      // Vérification préliminaire : si l'ID utilisateur n'est pas disponible, on ne crée pas de signalement
      if (!userId) {
        logger.warn('Signalement différé ignoré : ID utilisateur manquant', {
          tempImageId,
          permanentImageId,
          contentType
        });
        return;
      }

      // Récupérer les informations de modération depuis Redis
      const redisKey = `temp_moderation:${tempImageId}`;
      const moderationDataStr = await redis.get(redisKey);

      if (!moderationDataStr) {
        logger.warn('Aucune information de modération trouvée pour cette image temporaire', {
          tempImageId,
          permanentImageId,
          contentType
        });
        return;
      }

      // Parser les données de modération
      const moderationData = JSON.parse(moderationDataStr);
      const score = moderationData.result.score;

      // Ne créer un signalement que si le score est élevé (dangereux)
      if (score <= 0.7) {
        logger.info('Score de modération trop faible pour créer un signalement différé', {
          tempImageId,
          permanentImageId,
          score
        });
        return;
      }

      // Vérifier que l'ID permanent est bien un UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(permanentImageId)) {
        logger.warn('Signalement différé ignoré : permanentImageId non UUID', {
          tempImageId,
          permanentImageId
        });
        return;
      }

      // Déterminer le type de contenu pour le signalement
      const contentTypeMap: Record<string, string> = {
        'featured': 'profile',
        'gallery': 'gallery',
        'gallery_cover': 'gallery',
        'avatar': 'profile',
        'profile_picture': 'profile',
        'banner_picture': 'banner_picture', // <-- garder le type natif
        'mission': 'mission'
      };

      const mappedContentType = contentTypeMap[contentType] || 'profile';

      // Récupérer le chemin de l'image temporaire s'il existe
      const tempImagePath = moderationData.temp_image_path || null;

      // Créer un signalement automatique avec l'UUID permanent
      const { error } = await supabase
        .from('reported_content')
        .insert({
          content_type: mappedContentType,
          content_id: permanentImageId,
          reason: `Image potentiellement inappropriée détectée automatiquement (score: ${score})`,
          reported_by: null, // Utiliser NULL au lieu de 'system'
          reported_user_id: userId,
          status: 'pending',
          content_snapshot: `Image modérée (ID temporaire: ${tempImageId})`,
          system_generated: true,
          temp_image_path: tempImagePath,
          moderation_data: {
            score: score,
            categories: moderationData.result.categories,
            description: moderationData.result.description
          }
        });

      if (error) {
        logger.error('Erreur lors de la création du signalement différé', {
          error,
          tempImageId,
          permanentImageId
        });
      } else {
        logger.info('Signalement différé créé avec succès', {
          tempImageId,
          permanentImageId,
          score,
          userId
        });

        // Supprimer les données de Redis une fois le signalement créé
        await redis.del(redisKey);
      }
    } catch (error) {
      logger.error('Erreur lors de la création du signalement différé', {
        error,
        tempImageId,
        permanentImageId
      });
    }
  }
}

export default ContentModerationService.getInstance();




