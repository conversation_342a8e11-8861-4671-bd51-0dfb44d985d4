import axios from 'axios';
import { notify } from '../../../components/Notification';
import { API_CONFIG } from '../../../config/api';
import { getCommonHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';

// Interface pour les avis
export interface Review {
  id: string;
  rating: number;
  comment: string;
  created_at?: string;
  modified_at?: string;
  client?: {
    firstName: string;
    lastName: string;
    photo_url?: string;
  };
  author_id?: string;
  mission_categorie?: string;
  mission_sous_categorie?: string;
  qualites?: string[];
  mission_id?: string | null;
}

// Nouvelle logique : récupération de toutes les photos du profil pour le signalement
export const getAllProfilePhotos = (profil: any) => {
  const photos: { id: string; url: string; label: string }[] = [];
  // Photo de profil principale
  if (profil?.photo_url) {
    photos.push({
      id: 'profil-photo',
      url: profil.photo_url,
      label: 'Photo de profil'
    });
  }
  // Photos mises en avant (si présentes dans le profil)
  if ((profil as any)?.featuredPhotos && Array.isArray((profil as any).featuredPhotos)) {
    (profil as any).featuredPhotos.forEach((photo: any, idx: number) => {
      photos.push({
        id: `featured-${photo.id}`,
        url: photo.photo_url,
        label: `Photo mise en avant #${idx + 1}`
      });
    });
  }
  // Photos des galeries
  if (profil?.galleryFolders && Array.isArray(profil.galleryFolders)) {
    profil.galleryFolders.forEach((gallery: any) => {
      if ((gallery as any).photos && Array.isArray((gallery as any).photos)) {
        (gallery as any).photos.forEach((photo: any, idx: number) => {
          photos.push({
            id: `gallery-${gallery.id}-${photo.id}`,
            url: photo.photo_url,
            label: `Galerie "${gallery.name}" - Photo #${idx + 1}`
          });
        });
      }
    });
  }
  return photos;
};

// Fonction pour signaler le profil
export const handleReportProfile = async (
  reason: string,
  profil: any,
  setReportLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setReportModalOpen: React.Dispatch<React.SetStateAction<boolean>>,
  isOwnProfil: boolean
) => {
  setReportLoading(true);
  try {
    let profilId = profil?.profil_id;
    // Si on n'a pas l'id du profil, on le récupère via l'API
    if (!profilId) {
      let endpoint = '/api/users/profil';
      if (!isOwnProfil && profil?.slug) {
        endpoint = `/api/users/profil/${profil.slug}`;
      }
      const response = await axios.get(endpoint, API_CONFIG);
      profilId = response.data?.profil?.data?.id;
    }
    if (!profilId) {
      notify("Impossible de récupérer l'identifiant du profil à signaler.", 'error');
      setReportLoading(false);
      return;
    }
    const headers = await getCommonHeaders();
    await fetchCsrfToken();
    headers['X-CSRF-Token'] = await fetchCsrfToken();
    await axios.post(
      `${API_CONFIG.baseURL}/api/reported-content`,
      {
        content_type: 'profile',
        content_id: profilId,
        reason
      },
      {
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        withCredentials: true
      }
    );
    notify('Signalement envoyé, merci pour votre vigilance.', 'success');
    setReportModalOpen(false);
  } catch (error: any) {
    notify(error?.response?.data?.message || 'Erreur lors du signalement', 'error');
  } finally {
    setReportLoading(false);
  }
};
