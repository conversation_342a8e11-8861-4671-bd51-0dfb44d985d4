import React, { useState, useEffect } from 'react';
import { createAuthService } from '../services/auth';
import { getCookie, removeCookie, setCookie } from '../utils/cookieUtils';
import DOMPurify from 'dompurify';

interface AuthError {
  message: string;
  code?: string;
}

const ResendVerification: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showButton, setShowButton] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const email = getCookie('pendingVerificationEmail');
  const authService = createAuthService(() => {
    // La notification est gérée par le service d'authentification
  });

  // Vérifier le cooldown au chargement
  useEffect(() => {
    const storedCooldown = getCookie('verificationEmailCooldown');
    if (storedCooldown) {
      const cooldownTime = parseInt(storedCooldown);
      const now = Date.now();
      if (cooldownTime > now) {
        setCountdown(Math.ceil((cooldownTime - now) / 1000));
        setShowButton(false);
      } else {
        removeCookie('verificationEmailCooldown');
        setCountdown(0);
        setShowButton(true);
      }
    }
  }, []);

  // Mettre à jour le countdown toutes les secondes
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (countdown > 0) {
      intervalId = setInterval(() => {
        const storedCooldown = getCookie('verificationEmailCooldown');
        if (storedCooldown) {
          const cooldownTime = parseInt(storedCooldown);
          const now = Date.now();
          if (cooldownTime > now) {
            setCountdown(Math.ceil((cooldownTime - now) / 1000));
            setShowButton(false);
          } else {
            removeCookie('verificationEmailCooldown');
            setCountdown(0);
            setShowButton(true);
          }
        }
      }, 1000);
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [countdown]);

  const handleResend = async () => {
    if (!email) {
      setError('Aucun email en attente de vérification');
      return;
    }

    // Validation basique de l'email
    const sanitizedEmail = DOMPurify.sanitize(email);
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedEmail)) {
      setError('Format d\'email invalide');
      return;
    }

    // Vérifier s'il y a un cooldown actif
    const storedCooldown = getCookie('verificationEmailCooldown');
    if (storedCooldown) {
      const cooldownTime = parseInt(storedCooldown);
      const now = Date.now();
      if (cooldownTime > now) {
        const remainingTime = Math.ceil((cooldownTime - now) / 1000);
        setError(`Veuillez attendre ${Math.ceil(remainingTime / 60)} minutes avant de renvoyer`);
        return;
      }
    }

    setIsLoading(true);
    setError(null);

    try {
      await authService.resendVerification(sanitizedEmail);
      
      // Définir un cooldown de 2 minutes
      const cooldownTime = Math.floor((Date.now() + 2 * 60 * 1000) / 1000); // Convertir en secondes
      setCookie('verificationEmailCooldown', String(cooldownTime), 2 * 60); // Mettre maxAge en secondes
            
      setCountdown(120);  // 2 minutes = 120 secondes
      setShowButton(false);
    } catch (error) {
      const authError = error as AuthError;
      setError(authError.message || 'Une erreur est survenue');
      setShowButton(true);
    } finally {
      setIsLoading(false);
    }
  };

  const buttonText = () => {
    if (isLoading) return 'Envoi en cours...';
    if (countdown > 0) {
      const minutes = Math.floor(countdown / 60);
      const seconds = countdown % 60;
      return minutes > 0 
        ? `Réessayer dans ${minutes} min ${seconds} sec` 
        : `Réessayer dans ${seconds} sec`;
    }
    return 'Renvoyer l\'email';
  };

  return (
    <div className="mt-4 space-y-4 w-full">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 w-full">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-yellow-800">Compte non vérifié</h3>
            <p className="text-sm text-gray-700 mt-1">
              Un email de vérification vient d'être envoyé à <span className="font-medium">{email}</span>. 
              Vous ne pourrez pas vous connecter tant que votre email n'est pas vérifié.
              <br />
              <br />
              Veuillez cliquer sur le lien dans l'email pour activer votre compte et pouvoir vous connecter.
            </p>
            {error && (
              <p className="text-sm text-red-700 mt-1">{error}</p>
            )}
            {showButton && (
            <button
              onClick={handleResend}
              disabled={isLoading || countdown > 0}
              className={`mt-4 w-full px-4 py-2 text-sm font-medium text-white rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FF7A35]/50 ${
                isLoading || countdown > 0
                  ? 'bg-[#FF7A35]/50 cursor-not-allowed'
                  : 'bg-[#FF7A35] hover:bg-[#ff965e] active:bg-[#ff7a35]/80'
              }`}
            >
                {buttonText()}
            </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResendVerification;
