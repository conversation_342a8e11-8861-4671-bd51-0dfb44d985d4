import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { saveAiConsent, checkAiConsent, getAiConsentDetails, deleteAiConsent } from '../controllers/aiConsent';
import { authMiddleware } from '../middleware/authMiddleware';
import { asyncHandler } from '../utils/inputValidation';
import { body, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

export const aiConsentRoutes = Router();

// Rate limiter pour les requêtes de consentement IA
const aiConsentLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 requêtes maximum par minute
  message: {
    message: 'Trop de requêtes concernant le consentement IA. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Validation pour l'enregistrement du consentement
const validateConsentData: RequestHandler[] = [
  body('firstName')
    .trim()
    .notEmpty().withMessage('Le prénom est requis')
    .isString().withMessage('Le prénom doit être une chaîne de caractères')
    .isLength({ min: 2, max: 50 }).withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .trim()
    .notEmpty().withMessage('Le nom est requis')
    .isString().withMessage('Le nom doit être une chaîne de caractères')
    .isLength({ min: 2, max: 50 }).withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  }
];

// Route pour enregistrer le consentement
aiConsentRoutes.post(
  '/',
  aiConsentLimiter,
  authMiddleware.authenticateToken,
  validateConsentData,
  asyncHandler(saveAiConsent)
);

// Route pour vérifier le statut du consentement
aiConsentRoutes.get(
  '/status',
  aiConsentLimiter,
  authMiddleware.authenticateToken,
  asyncHandler(checkAiConsent)
);

// Route pour récupérer les détails du consentement
aiConsentRoutes.get(
  '/details',
  aiConsentLimiter,
  authMiddleware.authenticateToken,
  asyncHandler(getAiConsentDetails)
);

// Route pour supprimer le consentement IA
aiConsentRoutes.delete(
  '/',
  aiConsentLimiter,
  authMiddleware.authenticateToken,
  asyncHandler(deleteAiConsent)
);

export default aiConsentRoutes;
