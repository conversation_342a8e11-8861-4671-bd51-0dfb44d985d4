import { Clock, Wallet, MapPin, Users, Sparkles } from 'lucide-react';

const Discover = () => {
  return (
    <div id="discover" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="text-left">
            <h2 className="text-4xl font-bold">
              Découvrez <span className="text-[#FF6B2C]">JobPartiel.fr</span>
            </h2>
          </div>
          <div>
            <p className="text-gray-600">
              Lancée en 2025, <span className="font-semibold text-[#FF6B2C]">JobPartiel.fr</span> est la plateforme innovante 
              <span className="font-semibold"> propulsée par l'intelligence artificielle</span> qui connecte
              ceux qui cherchent à arrondir leurs fins de mois avec ceux qui ont besoin de services de qualité.
              Notre IA révolutionnaire optimise chaque aspect de votre expérience pour maximiser vos chances de succès.
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Image and Counter */}
          <div className="relative">
            <picture>
              <source type="image/webp" srcSet="images\job-partiel-image-decoupe-et-mesure-du-bois.webp" />
              <source type="image/jpeg" srcSet="images\job-partiel-image-decoupe-et-mesure-du-bois.jpg" />
              <img
                src="images\job-partiel-image-decoupe-et-mesure-du-bois.jpg"
                alt="Professionnel au travail"
                className="rounded-lg shadow-xl"
                loading="lazy"
              />
            </picture>
            {/* Griffe icon overlay */}
            <div className="absolute -right-12 top-1/2 -translate-y-1/2 w-24 h-auto transform hover:scale-110 transition-transform duration-300">
              <picture>
                <source type="image/webp" srcSet="/images/griffe-icon-image-accueil.webp" />
                <source type="image/png" srcSet="/images/griffe-icon-image-accueil.png" />
                <img
                  src="/images/griffe-icon-image-accueil.png"
                  alt="Griffe décorative"
                  className="w-full h-auto"
                  loading="lazy"
                />
              </picture>
            </div>
            <div className="absolute -top-8 left-8 bg-[#FFD700] rounded-lg p-6 shadow-lg">
              <div className="text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-gray-800" />
                <div className="text-4xl font-bold">Nouveau</div>
                <div className="text-sm">Lancement<br />2025</div>
              </div>
            </div>
          </div>

          {/* Right Side - Features */}
          <div className="space-y-12 relative">
            {/* Élément décoratif */}
            <div className="absolute z-10 top-20 right-20 w-40 h-40 bg-[#FF6B2C]/10 rounded-full blur-3xl" />
            <div className="absolute z-10 bottom-20 left-20 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl" />

            {/* Feature 1 */}
            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              {/* Icône flottante */}
              <div className="absolute -top-6 left-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] rounded-xl blur-[2px]" />
                  <div className="relative bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-500">
                    <Wallet className="h-7 w-7 text-white" />
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <h3 className="text-2xl font-bold mb-3 text-gray-800">Économie Collaborative</h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-medium">Pour les jobbeurs :</span>
                  <br />Recevez des <span className="text-[#FF6B2C] font-medium">paiements sécurisés</span> pour vos missions.
                  <br /><br />
                  <span className="font-medium">Pour les clients :</span>
                  <br />Trouvez des professionnels qualifiés à des <span className="text-[#FF6B2C] font-medium">tarifs transparents</span> et négociez selon votre budget.
                </p>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              {/* Icône flottante */}
              <div className="absolute -top-6 left-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] rounded-xl blur-[2px]" />
                  <div className="relative bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-500">
                    <Clock className="h-7 w-7 text-white" />
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <h3 className="text-2xl font-bold mb-3 text-gray-800">Planning Personnalisé</h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-medium">Pour les jobbeurs :</span>
                  <br /><span className="text-[#FF6B2C] font-medium">Choisissez vos disponibilités</span> et recevez des missions adaptées.
                  <br /><br />
                  <span className="font-medium">Pour les clients :</span>
                  <br />Planifiez vos services selon <span className="text-[#FF6B2C] font-medium">vos besoins</span> et trouvez rapidement des professionnels disponibles.
                </p>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
              {/* Icône flottante */}
              <div className="absolute -top-6 left-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] rounded-xl blur-[2px]" />
                  <div className="relative bg-gradient-to-br from-[#FF6B2C] to-[#FF8F5C] p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-500">
                    <MapPin className="h-7 w-7 text-white" />
                  </div>
                </div>
              </div>

              <div className="pt-6">
                <h3 className="text-2xl font-bold mb-3 text-gray-800">Services de Proximité</h3>
                <p className="text-gray-600 leading-relaxed">
                  <span className="font-medium">Pour les jobbeurs :</span>
                  <br />Définissez votre <span className="text-[#FF6B2C] font-medium">zone d'intervention</span> et travaillez près de chez vous.
                  <br /><br />
                  <span className="font-medium">Pour les clients :</span>
                  <br />Trouvez des professionnels <span className="text-[#FF6B2C] font-medium">dans votre quartier</span> pour des interventions rapides et pratiques.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Section IA Innovation */}
        <div className="mt-20 relative">
          {/* Élément décoratif */}
          <div className="absolute z-0 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-br from-[#FF6B2C]/5 to-blue-500/5 rounded-full blur-3xl"></div>
          
          {/* Titre de section */}
          <div className="text-center mb-12 relative z-10">
            <div className="inline-flex items-center justify-center px-4 py-1.5 mb-4 bg-[#FF6B2C]/10 rounded-full">
              <Sparkles className="h-4 w-4 text-[#FF6B2C] mr-2" />
              <span className="text-sm font-medium text-[#FF6B2C]">Technologie exclusive</span>
            </div>
            <h2 className="text-3xl font-bold">
              Notre <span className="bg-gradient-to-r from-[#FF6B2C] to-[#FF8F5C] text-transparent bg-clip-text">Intelligence Artificielle</span> fait la différence
            </h2>
          </div>
          
          {/* Contenu principal - Grille */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 relative z-10">
            {/* Carte 1 */}
            <div className="bg-white rounded-xl shadow-md hover:shadow-lg p-6 transition-all border border-gray-100 relative overflow-hidden group">
              {/* Icône */}
              <div className="h-12 w-12 bg-[#FF6B2C]/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-[#FF6B2C]/20 transition-colors">
                <svg viewBox="0 0 24 24" fill="none" className="h-6 w-6 text-[#FF6B2C]" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 11H5M19 11C20.1046 11 21 11.8954 21 13V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V13C3 11.8954 3.89543 11 5 11M19 11V9C19 7.89543 18.1046 7 17 7M5 11V9C5 7.89543 5.89543 7 7 7M7 7V5C7 3.89543 7.89543 3 9 3H15C16.1046 3 17 3.89543 17 5V7M7 7H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              
              {/* Contenu */}
              <h3 className="text-lg font-bold mb-2 text-gray-800">Matching intelligent</h3>
              <p className="text-gray-600 text-sm">Notre IA analyse vos compétences et préférences pour vous proposer les missions les plus adaptées à votre profil.</p>
            </div>
            
            {/* Carte 2 */}
            <div className="bg-white rounded-xl shadow-md hover:shadow-lg p-6 transition-all border border-gray-100 relative overflow-hidden group">
              {/* Icône */}
              <div className="h-12 w-12 bg-[#FF6B2C]/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-[#FF6B2C]/20 transition-colors">
                <svg viewBox="0 0 24 24" fill="none" className="h-6 w-6 text-[#FF6B2C]" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M18.2218 11.4363C18.4167 11.7056 18.5 12.0302 18.5 12.3639C18.5 12.6975 18.4167 13.0221 18.2218 13.2914L17.0404 15.0896C16.8689 15.3368 16.6536 15.5473 16.4043 15.7115C16.1551 15.8757 15.8766 15.9901 15.5848 16.0483L13.5231 16.4505C13.1805 16.5176 12.8254 16.5095 12.4865 16.4269C12.1477 16.3443 11.8347 16.1891 11.5709 15.9738L10.0513 14.8058C9.83009 14.6324 9.64574 14.4152 9.51074 14.1675C9.37574 13.9198 9.29314 13.6471 9.26839 13.3671L9.07795 11.2844C9.05113 10.9568 9.08615 10.6276 9.18078 10.3148C9.2754 10.002 9.42737 9.71231 9.62772 9.46215L10.7809 8.0125C10.9456 7.81223 11.1458 7.64541 11.3722 7.52209C11.5987 7.39878 11.8468 7.32134 12.1026 7.2944L13.3888 7.16694C13.7198 7.13401 14.0557 7.16745 14.3722 7.26507C14.6886 7.36269 14.977 7.52199 15.2158 7.7326L16.5599 8.85813C16.8301 9.08921 17.0399 9.38037 17.1704 9.70751C17.3008 10.0346 17.3478 10.3879 17.3078 10.7382L17.1188 11.986" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              
              {/* Contenu */}
              <h3 className="text-lg font-bold mb-2 text-gray-800">Optimisation des descriptions</h3>
              <p className="text-gray-600 text-sm">Créez des descriptions professionnelles et attractives pour vos services et missions grâce à notre assistant IA.</p>
            </div>
            
            {/* Carte 3 */}
            <div className="bg-white rounded-xl shadow-md hover:shadow-lg p-6 transition-all border border-gray-100 relative overflow-hidden group">
              {/* Icône */}
              <div className="h-12 w-12 bg-[#FF6B2C]/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-[#FF6B2C]/20 transition-colors">
                <svg viewBox="0 0 24 24" fill="none" className="h-6 w-6 text-[#FF6B2C]" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              
              {/* Contenu */}
              <h3 className="text-lg font-bold mb-2 text-gray-800">Prédiction de disponibilité</h3>
              <p className="text-gray-600 text-sm">Notre IA analyse les tendances du marché pour vous suggérer les meilleurs créneaux horaires et maximiser vos revenus.</p>
            </div>
            
            {/* Carte 4 */}
            <div className="bg-white rounded-xl shadow-md hover:shadow-lg p-6 transition-all border border-gray-100 relative overflow-hidden group">
              {/* Icône */}
              <div className="h-12 w-12 bg-[#FF6B2C]/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-[#FF6B2C]/20 transition-colors">
                <svg viewBox="0 0 24 24" fill="none" className="h-6 w-6 text-[#FF6B2C]" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 8H19M5 8C3.89543 8 3 7.10457 3 6C3 4.89543 3.89543 4 5 4H19C20.1046 4 21 4.89543 21 6C21 7.10457 20.1046 8 19 8M5 8L5 18C5 19.1046 5.89543 20 7 20H17C18.1046 20 19 19.1046 19 18V8M10 12H14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              
              {/* Contenu */}
              <h3 className="text-lg font-bold mb-2 text-gray-800">Tarification optimale</h3>
              <p className="text-gray-600 text-sm">Déterminez le prix idéal pour vos services grâce aux recommandations tarifaires personnalisées de notre IA.</p>
            </div>
          </div>
          
          {/* Bandeau de statistiques */}
          <div className="mt-12 bg-gradient-to-r from-[#FF6B2C] to-[#FF8F5C] rounded-xl p-1 relative z-10">
            <div className="bg-white rounded-lg py-6 px-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-xl md:text-2xl font-bold text-[#FF6B2C]">+65%</p>
                  <p className="text-sm text-gray-600">Taux de réponse amélioré</p>
                </div>
                <div className="text-center">
                  <p className="text-xl md:text-2xl font-bold text-[#FF6B2C]">+40%</p>
                  <p className="text-sm text-gray-600">Missions mieux rémunérées</p>
                </div>
                <div className="text-center">
                  <p className="text-xl md:text-2xl font-bold text-[#FF6B2C]">4.8/5</p>
                  <p className="text-sm text-gray-600">Satisfaction utilisateurs</p>
                </div>
                <div className="text-center">
                  <p className="text-xl md:text-2xl font-bold text-[#FF6B2C]">24/7</p>
                  <p className="text-sm text-gray-600">Assistance IA disponible</p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default Discover;