import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { sendSupportTicketCommentEmail } from '../services/emailService';
import { CreateCommentDTO, UpdateCommentDTO } from '../types/supportTickets';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { handleTicketStateChange } from './supportTickets';
import { sendSupportTicketStatusChangeEmail } from '../services/emailService';

const CACHE_PREFIX = 'support_ticket:';

// Fonction utilitaire pour nettoyer le HTML
const sanitizeHtml = (html: string): string => {
  // Version simple qui supprime les balises HTML
  return html.replace(/<[^>]*>?/gm, '');
};

// Fonction utilitaire pour générer un nom de fichier unique
const generateUniqueFileName = (originalName: string): string => {
  const fileExt = path.extname(originalName);
  const sanitizedName = path.basename(originalName, fileExt)
    .replace(/[^a-zA-Z0-9]/g, '_')
    .substring(0, 50);
  const uniqueId = crypto.randomBytes(16).toString('hex');
  return `${sanitizedName}_${uniqueId}${fileExt}`;
};

// Fonction pour sauvegarder un fichier et retourner son chemin
const saveFile = async (file: Express.Multer.File): Promise<{
  file_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
}> => {
  // Créer un nom de fichier unique
  const fileName = generateUniqueFileName(file.originalname);
  
  // Chemin de destination
  const uploadDir = path.join(__dirname, '../../../uploads/support-attachments');
  
  // S'assurer que le répertoire existe
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  const filePath = path.join(uploadDir, fileName);
  
  // Écrire le fichier
  await fs.promises.writeFile(filePath, file.buffer);
  
  return {
    file_name: file.originalname,
    file_path: `/uploads/support-attachments/${fileName}`,
    file_size: file.size,
    mime_type: file.mimetype
  };
};

// Interface pour typer les commentaires
interface CommentWithUser {
  id: string;
  message: string;
  is_internal: boolean;
  user: {
    id: string;
    email: string;
  }
}

// Interface pour typer les tickets
interface Ticket {
  id: string;
  title: string;
  user_id: string;
}

export const createComment = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket et les permissions
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (ticketError) {
      logger.error('Erreur lors de la récupération du ticket', { ticketError });
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    if (!isStaff && ticket.user_id !== userId) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    const { message, is_internal } = req.body as CreateCommentDTO;
    
    // Nettoyer le message
    const cleanMessage = sanitizeHtml(message);

    // Créer le commentaire
    const { data: comment, error } = await supabase
      .from('support_ticket_comments')
      .insert({
        ticket_id: ticketId,
        user_id: userId,
        message: cleanMessage,
        is_internal: isStaff ? is_internal : false
      })
      .select('*, user:users(id, email)')
      .single();

    if (error) {
      logger.error('Erreur lors de la création du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de la création du commentaire' });
      return;
    }

    // Gestion des pièces jointes
    if (req.files) {
      try {
        // S'assurer que req.files est un tableau
        const files = Array.isArray(req.files) ? req.files : [req.files];
        
        if (files.length > 0) {
          logger.info(`Traitement de ${files.length} pièce(s) jointe(s) pour le commentaire ${comment.id}`);
          
          // Vérifier que chaque fichier a les propriétés nécessaires
          for (const file of files) {
            if (!file.buffer || !file.originalname || !file.mimetype) {
              logger.error('Fichier invalide dans la requête', { 
                ticketId, 
                commentId: comment.id, 
                userId, 
                file 
              });
              continue; // Ignorer ce fichier et continuer avec les autres
            }
          }
          
          const validFiles = files.filter(file => file.buffer && file.originalname && file.mimetype);
          
          if (validFiles.length > 0) {
            const attachments = await Promise.all(
              validFiles.map(async (file: Express.Multer.File) => {
                try {
                  logger.info('Début de l\'upload du fichier', { 
                    ticketId, 
                    commentId: comment.id,
                    fileName: file.originalname,
                    fileSize: file.size,
                    mimeType: file.mimetype
                  });
                  
                  return {
                    ticket_id: ticketId,
                    comment_id: comment.id,
                    file_name: file.originalname,
                    file_size: file.size,
                    file_type: file.mimetype,
                    created_at: new Date().toISOString()
                  };
                } catch (uploadError) {
                  logger.error('Erreur lors de l\'upload d\'un fichier', { 
                    error: uploadError, 
                    fileName: file.originalname,
                    ticketId,
                    commentId: comment.id
                  });
                  // Continuer avec les autres fichiers malgré l'erreur
                  return null;
                }
              })
            );
            
            // Filtrer les uploads qui ont échoué
            const successfulAttachments = attachments.filter(attachment => attachment !== null);
            
            // Insérer les informations des pièces jointes dans la base de données
            if (successfulAttachments.length > 0) {
              try {
                await supabase
                  .from('support_ticket_attachments')
                  .insert(successfulAttachments);
                
                logger.info(`${successfulAttachments.length} pièce(s) jointe(s) ajoutée(s) au commentaire ${comment.id}`);
              } catch (dbError) {
                logger.error('Erreur lors de l\'insertion des pièces jointes en base de données', { 
                  error: dbError, 
                  ticketId,
                  commentId: comment.id,
                  attachmentsCount: successfulAttachments.length
                });
                // Continuer malgré l'erreur d'insertion en base de données
              }
            }
          }
        }
      } catch (attachmentError) {
        logger.error('Erreur lors de l\'ajout des pièces jointes:', attachmentError);
        // Continuer malgré l'erreur d'upload des pièces jointes
      }
    }

    // Vérifier si le ticket est fermé ou résolu et si l'utilisateur n'est pas du staff
    const isTicketClosed = ticket.status === 'ferme' || ticket.status === 'resolu';
    let ticketReopened = false;

    // Mettre à jour le ticket
    const updateData: any = {
      last_response_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Mettre à jour repondu en fonction du rôle de l'utilisateur
      repondu: isStaff ? true : false
    };

    // Si le ticket est fermé/résolu et que l'utilisateur n'est pas du staff et que le commentaire n'est pas interne,
    // réouvrir le ticket
    if (isTicketClosed && !isStaff && !is_internal) {
      updateData.status = 'en_cours';
      ticketReopened = true;
      logger.info(`Ticket ${ticketId} réouvert automatiquement suite à un commentaire de l'utilisateur ${userId}`);
    }

    // Mettre à jour le ticket
    const { error: updateError } = await supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId);

    if (updateError) {
      logger.error('Erreur lors de la mise à jour du ticket:', updateError);
      // On continue malgré l'erreur pour ne pas bloquer l'ajout du commentaire
    } else {
      logger.info(`Ticket ${ticketId} mis à jour suite à l'ajout d'un commentaire`);
      
      // Si le ticket a été réouvert, enregistrer cette action dans l'historique
      if (ticketReopened) {
        try {
          await supabase
            .from('support_ticket_history')
            .insert({
              ticket_id: ticketId,
              user_id: userId,
              action: 'update_status',
              old_value: ticket.status,
              new_value: 'en_cours',
              comment: 'Ticket réouvert automatiquement suite à un nouveau commentaire'
            });
          
          // Notifier le changement d'état du ticket
          const oldTicket = ticket;
          const newTicket = { ...ticket, status: 'en_cours' };
          
          // Récupérer les informations complètes de l'utilisateur pour la notification
          const { data: userInfo, error: userError } = await supabase
            .from('users')
            .select('id, email')
            .eq('id', ticket.user_id)
            .single();

          if (userError) {
            logger.error(`Erreur lors de la récupération des informations utilisateur pour la notification:`, userError);
          } else {
            // Ajouter les informations utilisateur au ticket pour la notification
            const ticketWithUser = {
              ...oldTicket,
              user: userInfo
            };
            const newTicketWithUser = {
              ...newTicket,
              user: userInfo
            };
            
            // Envoyer la notification avec les informations complètes
            await handleTicketStateChange(ticketWithUser, newTicketWithUser, false, userId);
            logger.info(`Notification envoyée pour la réouverture du ticket ${ticketId}`);
            
            // Notifier également le staff de la réouverture du ticket
            try {
              const { data: staffUsers } = await supabase
                .from('users')
                .select('email')
                .in('role', ['jobpadm', 'jobmodo']);
                
              if (staffUsers?.length) {
                for (const staff of staffUsers) {
                  await sendSupportTicketStatusChangeEmail(staff.email, {
                    ticketId: ticketId,
                    title: ticket.title,
                    oldStatus: oldTicket.status,
                    newStatus: 'en_cours',
                    userEmail: userInfo.email,
                    isReopenedByComment: true
                  });
                }
                logger.info(`Notification envoyée au staff pour la réouverture du ticket ${ticketId}`);
              }
            } catch (staffNotifError) {
              logger.error(`Erreur lors de la notification du staff pour la réouverture du ticket ${ticketId}:`, staffNotifError);
            }
          }
          
          logger.info(`Historique mis à jour pour la réouverture du ticket ${ticketId}`);
        } catch (historyError) {
          logger.error('Erreur lors de la mise à jour de l\'historique:', historyError);
        }
      }
    }

    // Mise à jour du cache du ticket
    await redis.del(`${CACHE_PREFIX}${ticketId}`);

    // Notifications
    await handleCommentNotifications(comment as CommentWithUser, ticket as Ticket, isStaff);

    res.status(201).json(comment);
    return;
  } catch (error) {
    logger.error('Erreur lors de la création du commentaire:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const updateComment = async (req: Request, res: Response) => {
  try {
    const { ticketId, commentId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket et les permissions
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (ticketError) {
      logger.error('Erreur lors de la récupération du ticket', { ticketError });
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier l'existence du commentaire
    const { data: comment, error: commentError } = await supabase
      .from('support_ticket_comments')
      .select('*')
      .eq('id', commentId)
      .eq('ticket_id', ticketId)
      .single();

    if (commentError) {
      logger.error('Erreur lors de la récupération du commentaire', { commentError });
      res.status(500).json({ error: 'Erreur lors de la récupération du commentaire' });
      return;
    }

    if (!comment) {
      res.status(404).json({ error: 'Commentaire non trouvé' });
      return;
    }

    // Vérifier les permissions
    if (comment.user_id !== userId && !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    const { message, is_internal } = req.body as UpdateCommentDTO;
    
    // Nettoyer le message
    const cleanMessage = message ? sanitizeHtml(message) : comment.message;

    // Mettre à jour le commentaire
    const updateData: { message: string; is_internal?: boolean } = {
      message: cleanMessage
    };

    // Seul le staff peut modifier le statut interne
    if (isStaff && is_internal !== undefined) {
      updateData.is_internal = is_internal;
    }

    const { data: updatedComment, error } = await supabase
      .from('support_ticket_comments')
      .update(updateData)
      .eq('id', commentId)
      .select('*, user:users(id, email)')
      .single();

    if (error) {
      logger.error('Erreur lors de la mise à jour du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de la mise à jour du commentaire' });
      return;
    }

    // Mise à jour du cache du ticket
    await redis.del(`${CACHE_PREFIX}${ticketId}`);

    res.json(updatedComment);
    return;
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du commentaire:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

export const deleteComment = async (req: Request, res: Response) => {
  try {
    const { ticketId, commentId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (ticketError) {
      logger.error('Erreur lors de la récupération du ticket', { ticketError });
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier l'existence du commentaire
    const { data: comment, error: commentError } = await supabase
      .from('support_ticket_comments')
      .select('*')
      .eq('id', commentId)
      .eq('ticket_id', ticketId)
      .single();

    if (commentError) {
      logger.error('Erreur lors de la récupération du commentaire', { commentError });
      res.status(500).json({ error: 'Erreur lors de la récupération du commentaire' });
      return;
    }

    if (!comment) {
      res.status(404).json({ error: 'Commentaire non trouvé' });
      return;
    }

    // Vérifier les permissions
    if (comment.user_id !== userId && !isStaff) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Supprimer le commentaire
    const { error } = await supabase
      .from('support_ticket_comments')
      .delete()
      .eq('id', commentId);

    if (error) {
      logger.error('Erreur lors de la suppression du commentaire:', error);
      res.status(500).json({ error: 'Erreur lors de la suppression du commentaire' });
      return;
    }

    // Mise à jour du cache du ticket
    await redis.del(`${CACHE_PREFIX}${ticketId}`);

    res.json({ message: 'Commentaire supprimé avec succès' });
    return;
  } catch (error) {
    logger.error('Erreur lors de la suppression du commentaire:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
};

const handleCommentNotifications = async (
  comment: CommentWithUser,
  ticket: Ticket,
  isStaff: boolean
) => {
  try {
    let recipientEmails: string[] = [];
    let recipientIds: string[] = [];
    const NOTIFICATION_THROTTLE_MINUTES = 5; // Délai de 5 minutes entre les notifications

    // Si le commentaire n'est pas interne
    if (!comment.is_internal) {
      if (isStaff) {
        // Si le commentaire est du staff, notifier l'utilisateur
        const { data: ticketUser } = await supabase
          .from('users')
          .select('id, email')
          .eq('id', ticket.user_id)
          .single();

        if (ticketUser) {
          // Vérifier si l'utilisateur a déjà été notifié récemment pour ce ticket
          const lastNotificationKey = `notification:ticket:${ticket.id}:user:${ticketUser.id}`;
          const lastNotification = await redis.get(lastNotificationKey);
          
          // Vérifier si l'utilisateur a répondu depuis la dernière notification
          const hasUserRepliedSinceLastNotification = await checkUserReply(ticket.id, ticketUser.id, lastNotification);
          
          // Vérifier le délai ou si l'utilisateur a répondu depuis la dernière notification
          if (!lastNotification || hasUserRepliedSinceLastNotification) {
            // Autoriser la notification
            recipientIds.push(ticketUser.id);
            
            // Vérifier si l'utilisateur accepte les notifications par email
            recipientEmails.push(ticketUser.email);
            
            // Enregistrer l'horodatage de cette notification
            await redis.set(lastNotificationKey, new Date().toISOString());
            // Définir l'expiration à 1 jour (pour éviter d'encombrer Redis)
            await redis.expire(lastNotificationKey, 86400);
            
            logger.info(`Notification envoyée à l'utilisateur ${ticketUser.email} pour le ticket ${ticket.id}`);
          } else {
            logger.info(`Notification bloquée pour l'utilisateur ${ticketUser.id} (ticket ${ticket.id}) - délai de ${NOTIFICATION_THROTTLE_MINUTES} minutes non écoulé`);
          }
        }
      } else {
        // Si le commentaire est de l'utilisateur, notifier le staff assigné ou tous les admins/modos
        // D'abord vérifier si le ticket est assigné à quelqu'un
        const { data: ticketDetails } = await supabase
          .from('support_tickets')
          .select('assigned_to')
          .eq('id', ticket.id)
          .single();
          
        if (ticketDetails && ticketDetails.assigned_to) {
          // Notifier l'admin assigné
          const { data: assignedAdmin } = await supabase
            .from('users')
            .select('id, email')
            .eq('id', ticketDetails.assigned_to)
            .single();
            
          if (assignedAdmin) {
            // Vérifier si l'admin a déjà été notifié récemment pour ce ticket
            const lastNotificationKey = `notification:ticket:${ticket.id}:user:${assignedAdmin.id}`;
            const lastNotification = await redis.get(lastNotificationKey);
            
            // Si l'utilisateur vient de répondre, on doit notifier l'admin quoi qu'il arrive
            if (!lastNotification) {
              recipientEmails.push(assignedAdmin.email);
              recipientIds.push(assignedAdmin.id);
              
              // Enregistrer l'horodatage de cette notification
              await redis.set(lastNotificationKey, new Date().toISOString());
              await redis.expire(lastNotificationKey, 86400);
            }
          }
        } else {
          // Notifier tous les admins/modos
          const { data: staffUsers } = await supabase
            .from('users')
            .select('id, email')
            .in('role', ['jobpadm', 'jobmodo']);

          if (staffUsers && staffUsers.length > 0) {
            for (const staffUser of staffUsers) {
              // Vérifier si ce staff a déjà été notifié récemment pour ce ticket
              const lastNotificationKey = `notification:ticket:${ticket.id}:user:${staffUser.id}`;
              const lastNotification = await redis.get(lastNotificationKey);
              
              // Si l'utilisateur vient de répondre, on doit notifier tous les admins
              if (!lastNotification) {
                recipientEmails.push(staffUser.email);
                recipientIds.push(staffUser.id);
                
                // Enregistrer l'horodatage de cette notification
                await redis.set(lastNotificationKey, new Date().toISOString());
                await redis.expire(lastNotificationKey, 86400);
              }
            }
          }
        }
      }
    } else {
      // Si le commentaire est interne, notifier seulement les autres membres du staff
      const { data: staffUsers } = await supabase
        .from('users')
        .select('id, email')
        .in('role', ['jobpadm', 'jobmodo'])
        .neq('id', comment.user.id); // Exclure l'auteur du commentaire

      if (staffUsers && staffUsers.length > 0) {
        for (const staffUser of staffUsers) {
          // Vérifier si ce staff a déjà été notifié récemment pour ce ticket
          const lastNotificationKey = `notification:ticket:${ticket.id}:user:${staffUser.id}`;
          const lastNotification = await redis.get(lastNotificationKey);
          
          // Vérifier le délai
          if (!lastNotification) {
            recipientEmails.push(staffUser.email);
            recipientIds.push(staffUser.id);
            
            // Enregistrer l'horodatage de cette notification
            await redis.set(lastNotificationKey, new Date().toISOString());
            await redis.expire(lastNotificationKey, 86400);
          }
        }
      }
    }

    // Envoyer des emails aux destinataires
    if (recipientEmails.length > 0) {
      try {
        // Déterminer l'URL du ticket pour chaque email
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        const ticketUrl = isStaff 
          ? `${baseUrl}/dashboard/support/ticket/${ticket.id}` 
          : `${baseUrl}/admin/support/ticket/${ticket.id}`;
        await sendSupportTicketCommentEmail(
          recipientEmails.join(','),
          {
            ticketId: ticket.id,
            title: ticket.title,
            commentMessage: comment.message,
            commenterEmail: isStaff ? 'Support JobPartiel' : comment.user.email,
            isInternal: comment.is_internal,
            ticketUrl: ticketUrl
          }
        );
        logger.info(`Email de notification de commentaire envoyé pour le ticket ${ticket.id} à ${recipientEmails.join(', ')}`);
      } catch (emailError) {
        logger.error('Erreur lors de l\'envoi de l\'email de notification de commentaire:', emailError);
      }
    }
    
    // Créer des notifications internes
    if (recipientIds.length > 0) {
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const ticketUrl = isStaff 
        ? `${baseUrl}/dashboard/support/ticket/${ticket.id}` 
        : `${baseUrl}/admin/support/ticket/${ticket.id}`;
      
      // Type de notification et titre adaptés au destinataire
      const notificationType = 'system';
      
      // Préparation des données de notification
      const notificationsData = recipientIds.map(userId => ({
        user_id: userId,
        type: notificationType,
        title: isStaff ? 'Nouvelle réponse du support' : 'Nouvelle réponse à votre ticket',
        content: `${isStaff ? 'Le support' : comment.user.email} a répondu au ticket: ${ticket.title}`,
        link: ticketUrl,
        is_read: false,
        is_archived: false
      }));
      
      // Insertion des notifications
      const { error: notificationError } = await supabase
        .from('user_notifications')
        .insert(notificationsData);
        
      if (notificationError) {
        logger.error('Erreur lors de la création des notifications pour le commentaire de ticket:', notificationError);
      } else {
        logger.info(`${notificationsData.length} notifications créées pour le commentaire de ticket ${ticket.id}`);
        
        // Invalider le cache Redis pour les compteurs de notifications des utilisateurs concernés
        await Promise.all(recipientIds.map(async userId => {
          const cacheKey = `notifications_count:${userId}`;
          await redis.del(cacheKey);
          
          // Invalider le cache des notifications
          const notificationsCacheKeys = await redis.keys(`notifications:${userId}:*`);
          if (notificationsCacheKeys.length > 0) {
            await Promise.all(notificationsCacheKeys.map(key => redis.del(key)));
          }
        }));
      }
    }
  } catch (error) {
    logger.error('Erreur lors de l\'envoi des notifications de commentaire:', error);
  }
};

// Fonction pour vérifier si l'utilisateur a répondu depuis la dernière notification
const checkUserReply = async (ticketId: string, userId: string, lastNotificationTimestamp: string | null): Promise<boolean> => {
  if (!lastNotificationTimestamp) return false;
  
  try {
    // Vérifier s'il existe un commentaire de l'utilisateur après la dernière notification
    const { data, error } = await supabase
      .from('support_ticket_comments')
      .select('created_at')
      .eq('ticket_id', ticketId)
      .eq('user_id', userId)
      .gt('created_at', lastNotificationTimestamp)
      .order('created_at', { ascending: false })
      .limit(1);
      
    if (error) {
      logger.error('Erreur lors de la vérification des réponses de l\'utilisateur:', error);
      return false;
    }
    
    // S'il y a au moins un commentaire après la dernière notification, l'utilisateur a répondu
    return data && data.length > 0;
  } catch (error) {
    logger.error('Erreur lors de la vérification des réponses de l\'utilisateur:', error);
    return false;
  }
};

export const getTicketComments = async (req: Request, res: Response) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user?.userId;
    const isStaff = req.user?.role === 'jobpadm' || req.user?.role === 'jobmodo';

    if (!userId) {
      res.status(401).json({ error: 'Non authentifié' });
      return;
    }

    // Vérifier l'existence du ticket et les permissions
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId)
      .single();

    if (ticketError) {
      logger.error('Erreur lors de la récupération du ticket', { ticketError });
      res.status(500).json({ error: 'Erreur lors de la récupération du ticket' });
      return;
    }

    if (!ticket) {
      res.status(404).json({ error: 'Ticket non trouvé' });
      return;
    }

    // Vérifier les permissions (seul le propriétaire du ticket ou le staff peut voir les commentaires)
    if (!isStaff && ticket.user_id !== userId) {
      res.status(403).json({ error: 'Non autorisé' });
      return;
    }

    // Récupérer les commentaires
    let query = supabase
      .from('support_ticket_comments')
      .select('*, user:users(id, email, role)')
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true });
    
    // Si c'est un utilisateur normal, filtrer les commentaires internes
    if (!isStaff) {
      query = query.eq('is_internal', false);
    }

    const { data: comments, error } = await query;

    if (error) {
      logger.error('Erreur lors de la récupération des commentaires:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des commentaires' });
      return;
    }

    res.status(200).json(comments);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des commentaires:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
}; 