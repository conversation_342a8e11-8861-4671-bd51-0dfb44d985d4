import React, { useState, useEffect, useRef } from 'react';
import { Box, Grid, Paper, Typography, styled, SxProps, IconButton } from '@mui/material';
import ConversationList from './components/ConversationList';
import ConversationView from '../../pages/Messages/components/ConversationView';
import { useAuth } from '../../contexts/AuthContext';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { api } from '../../services/api';
import { ConversationsResponse } from './types';
import { useParams, useNavigate } from 'react-router-dom';
import { useSocket } from '../../contexts/SocketContext';
import { motion, AnimatePresence } from 'framer-motion';
import { logger } from '@/utils/logger';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { fetchCsrfToken } from '../../services/csrf';

const PageTitle = styled(Typography)(() => ({
    fontSize: '1.5rem',
    fontWeight: 700,
    color: '#2D3748',
    marginBottom: '24px',
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: '-8px',
      left: 0,
      width: '60px',
      height: '3px',
      background: '#FF6B2C',
      borderRadius: '2px',
    },
}));  

const StyledPaper = styled(Paper)(() => ({
  borderRadius: '16px',
  overflow: 'hidden',
  height: '100%',
  boxShadow: '0 10px 30px rgba(255, 107, 44, 0.08)',
  border: '1px solid rgba(255, 122, 53, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0 12px 36px rgba(255, 107, 44, 0.12)',
  }
}));

const EmptyStateBox = styled(Box)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  height: '100%',
  padding: '48px 24px',
  background: 'linear-gradient(145deg, rgba(255, 248, 243, 0.8), rgba(255, 228, 186, 0.2))',
  borderRadius: '16px',
  textAlign: 'center',
}));

interface MessageNotification {
  conversation_id: string;
  message_id: string;
  sender_id: string;
  content: string;
  attachments?: any[];
}

interface ChatBubbleIconProps {
  sx?: SxProps;
}

const MessagesPage: React.FC = () => {
  const { user } = useAuth();
  const { id: conversationId } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [unreadOnly, setUnreadOnly] = useState(false);
  const [showDeleted, setShowDeleted] = useState(false);
  const [isNewConversationOpen, setIsNewConversationOpen] = useState(false);
  const { socket, isConnected } = useSocket();
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 980);
  const [activeView, setActiveView] = useState<'list' | 'conversation'>('list');

  // Référence pour stocker les handlers et éviter de recréer les écouteurs socket
  const socketHandlersRef = useRef<{
    newMessage: (payload: MessageNotification) => void;
    messageRead: (payload: any) => void;
    conversationUnread: (payload: any) => void;
    newConversation: (payload: any) => void;
  } | null>(null);

  // Gérer le responsive
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 980);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Gérer la vue active en fonction de l'URL et du responsive
  useEffect(() => {
    if (isMobileView) {
      setActiveView(conversationId ? 'conversation' : 'list');
    }
  }, [conversationId, isMobileView]);

  // Vérifier le localStorage pour ouvrir automatiquement la modal de nouvelle conversation
  useEffect(() => {
    const newMessageInfo = localStorage.getItem('newMessageInfo');
    if (newMessageInfo) {
      setIsNewConversationOpen(true);
    }
  }, []);

  // Récupération des conversations avec refetchInterval
  const { data, refetch } = useQuery({
    queryKey: ['conversations', searchQuery, unreadOnly, showDeleted],
    queryFn: async () => {
      try {
        const response = await api.get('/api/messages', {
          params: {
            search: searchQuery || undefined,
            unread_only: unreadOnly || undefined,
            show_deleted: showDeleted
          }
        });
        
        if (response.data.success && response.data.data) {
          return response.data.data as ConversationsResponse;
        }
        
        throw new Error('Format de réponse invalide');
      } catch (error) {
        logger.info('Erreur lors de la récupération des conversations:', error);
        throw error;
      }
    },
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Forcer le chargement initial des conversations afin d'avoir les messages non lus et les compteurs
  useEffect(() => {
    refetch();
  }, []);

  const conversations = data?.conversations;

  // Initialiser les handlers une seule fois
  useEffect(() => {
    if (!socket || !isConnected) {
      logger.info("🔴 Socket non connecté dans MessagesPage:", { socket: !!socket, isConnected });
      return;
    }

    logger.info("✅ Socket connecté dans MessagesPage - ID:", socket.id);
    
    // Créer des gestionnaires stables dans un objet pour éviter les réabonnements
    const handlers = {
      newMessage: (payload: MessageNotification) => {
        logger.info("✅ Notification de nouveau message reçue:", payload);
        logger.info("🔍 État actuel - conversationId:", conversationId, "user?.id:", user?.id);
        
        // Si le message concerne la conversation actuelle, le marquer comme lu
        if (payload.conversation_id === conversationId && payload.sender_id !== user?.id) {
          logger.info("📖 Marquage automatique comme lu pour conversation active");
          (async () => {
            await fetchCsrfToken();
            const csrfToken = await fetchCsrfToken();
            api.post(`/api/messages/${payload.conversation_id}/read`, {}, {
              headers: { 'X-CSRF-Token': csrfToken },
            })
              .then(() => {
                logger.info("✅ Messages marqués comme lus automatiquement");
              })
              .catch(error => {
                logger.info("❌ Erreur lors du marquage des messages comme lus:", error);
              });
          })();
        }
        
        // Mettre à jour le cache des conversations sans recharger
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData?.conversations) return oldData;
          
          const newData = JSON.parse(JSON.stringify(oldData));
          const conversationIndex = newData.conversations.findIndex(
            (conv: any) => conv.id === payload.conversation_id
          );
          
          if (conversationIndex !== -1) {
            const conversation = newData.conversations[conversationIndex];
            conversation.last_message = {
              content: payload.content,
              created_at: new Date().toISOString()
            };
            
            if (payload.sender_id !== user?.id) {
              conversation.unread_count = (conversation.unread_count || 0) + 1;
            }
            
            const [updatedConversation] = newData.conversations.splice(conversationIndex, 1);
            newData.conversations.unshift(updatedConversation);
          } else {
            queryClient.invalidateQueries({ queryKey: ['conversations'] });
          }
          return newData;
        });
        
        // Mettre à jour le cache des messages si c'est la conversation active
        if (payload.conversation_id === conversationId) {
          queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
            if (!oldData) return oldData;
            
            const newData = JSON.parse(JSON.stringify(oldData));
            let messageExists = false;
            
            if (newData.pages) {
              // Vérifier si le message existe déjà
              for (const page of newData.pages) {
                if (page.messages) {
                  const existingMessageIndex = page.messages.findIndex((m: any) => m.id === payload.message_id);
                  
                  if (existingMessageIndex !== -1) {
                    messageExists = true;
                    logger.info("⚠️ Message déjà présent dans le cache (MessagesPage), ignoré:", payload.message_id);
                    break;
                  }
                }
              }
              
              // Ajouter le message seulement s'il n'existe pas déjà
              if (!messageExists && newData.pages.length > 0) {
                const lastPage = newData.pages[newData.pages.length - 1];
                if (lastPage.messages) {
                  let senderInfo = null;
                  
                  // Chercher les informations de l'expéditeur dans les messages existants
                  for (const page of newData.pages) {
                    if (page.messages) {
                      const existingSenderMessage = page.messages.find((msg: any) => 
                        msg.sender_id === payload.sender_id && msg.sender
                      );
                      
                      if (existingSenderMessage?.sender) {
                        senderInfo = existingSenderMessage.sender;
                        break;
                      }
                    }
                  }
                  
                  // Si pas trouvé, chercher dans les conversations
                  if (!senderInfo) {
                    const allConversations = queryClient.getQueryData(['conversations']) as any;
                    if (allConversations?.conversations) {
                      const conversation = allConversations.conversations.find(
                        (conv: any) => conv.id === payload.conversation_id
                      );
                      
                      if (conversation) {
                        const sender = payload.sender_id === conversation.user1_id 
                          ? conversation.user1 
                          : conversation.user2;
                          
                        if (sender) {
                          senderInfo = {
                            id: sender.id,
                            avatar_url: sender.avatar_url,
                            prenom: sender.prenom || sender.first_name,
                            nom: sender.nom || sender.last_name
                          };
                        }
                      }
                    }
                  }
                  
                  // Ajouter le nouveau message avec une date récente pour qu'il soit en dernier
                  const now = new Date();
                  now.setMilliseconds(now.getMilliseconds() + 1); // Garantir qu'il soit plus récent
                  
                  const newMessage = {
                    id: payload.message_id,
                    content: payload.content,
                    sender_id: payload.sender_id,
                    created_at: now, // Utiliser un objet Date au lieu d'une string
                    is_read: payload.sender_id === user?.id, // Marquer comme lu si c'est notre propre message
                    attachments: payload.attachments || [],
                    sender: senderInfo
                  };
                  
                  lastPage.messages.push(newMessage);
                  
                  logger.info("✅ Nouveau message ajouté au cache des messages:", payload.message_id);
                }
              }
            }
            
            return newData;
          });
          
          // Forcer une invalidation immédiate pour déclencher un re-render
          queryClient.invalidateQueries({ 
            queryKey: ['messages', conversationId],
            refetchType: 'none' // Ne pas refetch, juste invalider pour déclencher un re-render
          });
        }
      },

      messageRead: (payload: { conversation_id: string, messages: Array<{ id: string, read_at: string }> }) => {
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData?.conversations) {
            refetch();
            return oldData;
          }
          
          return {
            ...oldData,
            conversations: oldData.conversations.map((conv: any) => 
              conv.id === payload.conversation_id 
                ? {
                    ...conv,
                    unread_count: 0,
                    unread_count_user1: 0,
                    unread_count_user2: 0
                  }
                : conv
            )
          };
        });

        if (payload.conversation_id === conversationId) {
          queryClient.setQueryData(['messages', conversationId], (oldData: any) => {
            if (!oldData?.pages) return oldData;
            
            return {
              ...oldData,
              pages: oldData.pages.map((page: any) => ({
                ...page,
                messages: page.messages?.map((msg: any) => {
                  const updatedMessage = payload.messages.find(m => m.id === msg.id);
                  return {
                    ...msg,
                    is_read: updatedMessage ? true : msg.is_read,
                    read_at: updatedMessage ? updatedMessage.read_at : msg.read_at
                  };
                })
              }))
            };
          });
        }
      },

      conversationUnread: (payload: { conversation_id: string }) => {
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData?.conversations) return oldData;
          
          const newData = JSON.parse(JSON.stringify(oldData));
          const conversationIndex = newData.conversations.findIndex(
            (conv: any) => conv.id === payload.conversation_id
          );
          
          if (conversationIndex !== -1) {
            const conversation = newData.conversations[conversationIndex];
            conversation.unread_count = 1;
            
            const [updatedConversation] = newData.conversations.splice(conversationIndex, 1);
            newData.conversations.unshift(updatedConversation);
          }
          
          return newData;
        });
      },

      newConversation: (payload: any) => {
        logger.info("✅ Notification de nouvelle conversation reçue:", payload);
        
        queryClient.setQueryData(['conversations'], (oldData: any) => {
          if (!oldData) {
            return {
              success: true,
              data: {
                conversations: [payload],
                totalCount: 1
              }
            };
          }
          
          const newData = JSON.parse(JSON.stringify(oldData));
          const existingIndex = newData.conversations.findIndex(
            (conv: any) => conv.id === payload.id
          );
          
          if (existingIndex !== -1) {
            newData.conversations[existingIndex] = {
              ...newData.conversations[existingIndex],
              ...payload
            };
          } else {
            newData.conversations.unshift(payload);
            
            if (typeof newData.totalCount === 'number') {
              newData.totalCount += 1;
            }
          }
          
          return newData;
        });

        if (conversationId === payload.id) {
          queryClient.setQueryData(['conversation', conversationId], () => payload);
        }
      }
    };

    // Stocker les handlers dans la référence
    socketHandlersRef.current = handlers;

    // S'abonner aux événements
    socket.on('new_message', handlers.newMessage);
    socket.on('message_read', handlers.messageRead);
    socket.on('conversation_unread', handlers.conversationUnread);
    socket.on('new_conversation', handlers.newConversation);

    logger.info("✅ Écouteurs socket configurés dans MessagesPage:", {
      new_message: true,
      message_read: true,
      conversation_unread: true,
      new_conversation: true
    });

    // Test pour vérifier que les écouteurs sont bien actifs
    setTimeout(() => {
      logger.info("🔍 Vérification des écouteurs socket après 1 seconde");
      logger.info("🔍 Socket ID:", socket.id, "Connected:", socket.connected);
    }, 1000);

    // Nettoyage
    return () => {
      if (process.env.NODE_ENV === 'development') {
        logger.info("Suppression des écouteurs socket dans MessagesPage");
      }
      
      socket.off('new_message', handlers.newMessage);
      socket.off('message_read', handlers.messageRead);
      socket.off('conversation_unread', handlers.conversationUnread);
      socket.off('new_conversation', handlers.newConversation);
    };
  }, [socket, isConnected, queryClient, conversationId, user?.id, refetch]);

  // Vérifier si l'ID de conversation est valide
  useEffect(() => {
    if (conversationId && conversations) {
      const conversationExists = conversations.some(conv => conv.id === conversationId);
      if (!conversationExists) {
        // Rediriger vers la première conversation si l'ID n'existe pas
        // if (conversations.length > 0) {
          // navigate(`/dashboard/messages/${conversations[0].id}`);
        // } else {
          navigate('/dashboard/messages');
        // }
      }
    }
  }, [conversationId, conversations, navigate]);

  useEffect(() => {
    // Forcer un rechargement des conversations au montage du composant
    // Mais seulement si les données ne sont pas déjà en cache
    const conversationsData = queryClient.getQueryData(['conversations']);
    if (!conversationsData) {
      logger.info("🔄 Rechargement initial des conversations");
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    }
  }, []);

  const handleBack = () => {
    if (isMobileView) {
      setActiveView('list');
      navigate('/dashboard/messages');
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="space-y-6 px-2 md:px-0">
      <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          marginBottom: '12px'
        }}>
          {isMobileView && activeView === 'conversation' && (
            <IconButton 
              onClick={handleBack}
              sx={{ 
                color: '#FF6B2C',
                '&:hover': { backgroundColor: 'rgba(255, 107, 44, 0.1)' }
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          )}
          <PageTitle variant="h1" sx={{ marginBottom: '12px' }}>
            Messages Privés
          </PageTitle>
        </Box>

        <Grid 
          container 
          spacing={2}
          sx={{ 
            overflow: 'auto',
            paddingBottom: '35px',
            '@media (max-width: 980px)': {
              width: '100%',
              margin: 0,
              paddingLeft: 0,
              '& .MuiGrid-item': {
                flexBasis: '100% !important',
                maxWidth: '100% !important',
                paddingLeft: '0 !important'
              }
            }
          }}
        >
          {/* Liste des conversations */}
          <Grid size={{ xs: 12, md: 5, lg: 4 }} sx={{ paddingTop: '0 !important' }}>
            <AnimatePresence mode="wait">
              {(!isMobileView || activeView === 'list') && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <StyledPaper
                    elevation={0}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      height: isMobileView ? 'calc(100dvh - 180px)' : 'calc(100vh - 160px)',
                      minHeight: '400px',
                      '@media (max-width: 980px)': {
                        height: 'calc(100dvh - 160px)'
                      },
                      '@media (max-width: 768px)': {
                        height: 'calc(100dvh - 210px)'
                      }
                    }}
                  >
                    <ConversationList
                      conversations={conversations || []}
                      selectedId={conversationId}
                      isLoading={!data}
                      onSearchChange={setSearchQuery}
                      onUnreadFilterChange={setUnreadOnly}
                      unreadOnly={unreadOnly}
                      onShowDeletedChange={(value) => {
                        setShowDeleted(value);
                      }}
                      showDeleted={showDeleted}
                      isNewConversationOpen={isNewConversationOpen}
                      onNewConversationClose={() => setIsNewConversationOpen(false)}
                      setIsNewConversationOpen={setIsNewConversationOpen}
                    />
                  </StyledPaper>
                </motion.div>
              )}
            </AnimatePresence>
          </Grid>

          {/* Vue de la conversation */}
          <Grid size={{ xs: 12, md: 7, lg: 8 }} sx={{ paddingTop: '0 !important' }}>
            <AnimatePresence mode="wait">
              {(!isMobileView || activeView === 'conversation') && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <StyledPaper
                    elevation={0}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      height: isMobileView ? 'calc(100dvh - 180px)' : 'calc(100vh - 160px)',
                      overflow: 'hidden',
                      '@media (max-width: 980px)': {
                        height: 'calc(100dvh - 160px)',
                        minHeight: '500px'
                      },
                      '@media (max-width: 768px)': {
                        height: 'calc(100dvh - 210px)'
                      }
                    }}
                  >
                    {conversationId ? (
                      <ConversationView
                        conversationId={conversationId}
                        userId={user?.id || ''}
                      />
                    ) : (
                      <EmptyStateBox>
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ 
                            delay: 0.2,
                            duration: 0.5,
                            type: "spring",
                            stiffness: 200
                          }}
                        >
                          <Box 
                            sx={{ 
                              width: '100px',
                              height: '100px',
                              borderRadius: '50%',
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              margin: '0 auto 24px auto',
                            }}
                          >
                            <ChatBubbleIcon sx={{ fontSize: 48, color: '#FF6B2C' }} />
                          </Box>
                          <Typography 
                            variant="h6" 
                            color="#FF6B2C"
                            sx={{ mb: 2, fontWeight: 'bold' }}
                          >
                            Sélectionnez une conversation pour commencer
                          </Typography>
                          <Typography 
                            variant="body2" 
                            color="text.secondary"
                            sx={{ maxWidth: '400px', margin: '0 auto' }}
                          >
                            Choisissez une conversation dans la liste ou démarrez une nouvelle discussion en cliquant sur le bouton "+".
                          </Typography>
                        </motion.div>
                      </EmptyStateBox>
                    )}
                  </StyledPaper>
                </motion.div>
              )}
            </AnimatePresence>
          </Grid>
        </Grid>
      </div>
    </motion.div>
  );
};

// ChatBubbleIcon composant
const ChatBubbleIcon: React.FC<ChatBubbleIconProps> = ({ sx }) => (
  <svg 
    width="48" 
    height="48" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round"
    style={sx as React.CSSProperties}
  >
    <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
  </svg>
);

export default MessagesPage; 
