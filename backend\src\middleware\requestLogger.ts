import { Request, Response, NextFunction } from 'express';
import { logSecurity } from '../services/logger';
import { LogEventType } from '../types/logger';

// Middleware pour logger toutes les requêtes
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
    // Capture du temps de début
    const start = Date.now();
    
    // Logging de la fin de requête
    res.on('finish', () => {
        const duration = Date.now() - start;
        const logData = {
            method: req.method,
            path: req.path,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            userAgent: req.get('user-agent'),
            userId: req.user?.userId,
        };

        // Log différent selon le statut de la réponse
        if (res.statusCode >= 500) {
            logSecurity.error(LogEventType.API_ERROR, 'Erreur serveur', logData);
        } else if (res.statusCode >= 400) {
            logSecurity.warn(LogEventType.API_ERROR, 'Erreur client', logData);
        } else {
            // logSecurity.info(LogEventType.API_RESPONSE, 'Requête réussie', logData);
        }
    });

    next();
};
