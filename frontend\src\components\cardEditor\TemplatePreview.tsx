import React, { useRef, useState, useEffect } from 'react';
import { Stage, Layer, Rect, Text, Group, Image } from 'react-konva';
import {
  CardElement,
  CardTemplateData,
  TextElement,
  ImageElement,
  ShapeElement,
  QRCodeElement,
  DrawingElement
} from '../../types/cardEditor';
import TextElementComponent from './elements/TextElement';
import ImageElementComponent from './elements/ImageElement';
import ShapeElementComponent from './elements/ShapeElement';
import QRCodeElementComponent from './elements/QRCodeElement';
import DrawingElementComponent from './elements/DrawingElement';

interface TemplatePreviewProps {
  templateData: CardTemplateData;
  showProfessionalFooter?: boolean;
  showWatermark?: boolean;
}

/**
 * Composant optimisé pour la prévisualisation des templates
 * Ne charge pas de profil utilisateur pour éviter les requêtes API inutiles
 */
const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  templateData,
  showProfessionalFooter = true,
  showWatermark = false
}) => {
  const stageRef = useRef<any>(null);
  const [stageSize, setStageSize] = useState({
    width: templateData.width,
    height: templateData.height
  });
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null);

  // Charger l'image de fond si elle existe
  useEffect(() => {
    if (templateData.background_image) {
      const image = new window.Image();
      image.src = templateData.background_image;
      image.onload = () => {
        setBackgroundImage(image);
      };
    } else {
      setBackgroundImage(null);
    }
  }, [templateData.background_image]);

  // Calculer la taille du stage en fonction du conteneur
  useEffect(() => {
    const updateStageSize = () => {
      if (stageRef.current) {
        const container = stageRef.current.container().parentNode;
        if (container) {
          const containerWidth = container.offsetWidth;
          const containerHeight = container.offsetHeight;

          // Calculer le ratio pour maintenir les proportions
          const scaleX = containerWidth / templateData.width;
          const scaleY = containerHeight / templateData.height;
          const scale = Math.min(scaleX, scaleY, 1); // Ne pas agrandir au-delà de la taille originale

          setStageSize({
            width: templateData.width * scale,
            height: templateData.height * scale
          });
        }
      }
    };

    updateStageSize();
    window.addEventListener('resize', updateStageSize);
    return () => window.removeEventListener('resize', updateStageSize);
  }, [templateData.width, templateData.height]);

  // Calculer le facteur d'échelle
  const scale = Math.min(
    stageSize.width / templateData.width,
    stageSize.height / templateData.height
  );

  // Fonction pour rendre un élément
  const renderElement = (element: CardElement) => {
    const commonProps = {
      isSelected: false,
      onSelect: () => {},
      onDragStart: () => {},
      onDragMove: () => {},
      onDragEnd: () => {},
      isEditable: false,
      isCtrlPressed: false
    };

    switch (element.type) {
      case 'text':
        return (
          <TextElementComponent
            key={`text-${element.id}`}
            element={element as TextElement}
            {...commonProps}
          />
        );

      case 'image':
        return (
          <ImageElementComponent
            key={`image-${element.id}`}
            element={element as ImageElement}
            {...commonProps}
          />
        );

      case 'shape':
        return (
          <ShapeElementComponent
            key={`shape-${element.id}`}
            element={element as ShapeElement}
            {...commonProps}
          />
        );

      case 'qrcode':
        return (
          <QRCodeElementComponent
            key={`qrcode-${element.id}`}
            element={{
              id: element.id,
              type: "qrcode",
              x: element.x,
              y: element.y,
              width: element.width || 100,
              height: element.height || 100,
              rotation: element.rotation,
              properties: {
                data: (element as QRCodeElement).properties.data || `https://jobpartiel.fr`,
                fill: (element as QRCodeElement).properties.fill || "#000000",
                background: (element as QRCodeElement).properties.background || "#ffffff",
                visible: (element as QRCodeElement).properties.visible !== false
              }
            }}
            {...commonProps}
          />
        );

      case 'drawing':
        return (
          <DrawingElementComponent
            key={`drawing-${element.id}`}
            element={element as DrawingElement}
            {...commonProps}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Stage
      ref={stageRef}
      width={stageSize.width}
      height={stageSize.height}
      scaleX={scale}
      scaleY={scale}
      style={{
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        backgroundColor: templateData.background_color || '#ffffff'
      }}
    >
      <Layer>
        {/* Arrière-plan */}
        <Rect
          x={0}
          y={0}
          width={templateData.width}
          height={templateData.height}
          fill={templateData.background_color || '#ffffff'}
        />

        {/* Image de fond si elle existe */}
        {backgroundImage && (
          <Image
            x={0}
            y={0}
            width={templateData.width}
            height={templateData.height}
            image={backgroundImage}
          />
        )}

        {/* Éléments du template */}
        {templateData.elements
          .filter(element => element.properties?.visible !== false)
          .map(renderElement)}

        {/* Filigrane si activé */}
        {showWatermark && (
          <Text
            x={templateData.width / 2}
            y={templateData.height / 2}
            text="PREVIEW"
            fontSize={48}
            fontFamily="Arial"
            fill="rgba(255, 107, 44, 0.1)"
            align="center"
            offsetX={60}
            offsetY={24}
            rotation={-45}
          />
        )}
      </Layer>
    </Stage>
  );
};

export default TemplatePreview;
