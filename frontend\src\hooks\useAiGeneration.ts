import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { notify } from '../components/Notification';
import { API_CONFIG } from '../config/api';
import { getCommonHeaders } from '../utils/headers';
import { useAiCredits } from './useAiCredits';
import useAiConsent from './useAiConsent';
import logger from '../utils/logger';

// Types de génération supportés
export type GenerationType = 'biography' | 'service_description' | 'mission_post' | 'review_response' | 'mission_offer' | 'comment' | 'default_prompt' | 'gallery_description' | 'slogan' | 'support_comment';

// Interface pour les prompts personnalisés
export interface CustomPrompt {
  id: string;
  user_id: string;
  type: GenerationType;
  prompt: string;
  created_at: string;
  updated_at: string;
}

// Interface pour les données de génération IA
interface AiGenerationData {
  prompts: CustomPrompt[];
  loading: boolean;
  error: string | null;
  generating: boolean;
  apiHasResponded: boolean;
  credits: number;
  isRateLimited: boolean;
  creditError: string | null;
  hasConsent: boolean;
  isConsentRequired: boolean;
  getUserPrompts: () => Promise<void>;
  saveUserPrompt: (type: GenerationType, prompt: string) => Promise<boolean>;
  deleteUserPrompt: (type: GenerationType) => Promise<boolean>;
  generateContent: (type: GenerationType, context: string, customPrompt?: string) => Promise<string | null>;
}

interface UseAiGenerationOptions {
  onGenerationStart?: () => void;
  onGenerationComplete?: (content: string) => void;
  onGenerationError?: (error: any) => void;
}

/**
 * Hook pour gérer les prompts personnalisés et la génération de contenu IA
 */
export const useAiGeneration = (options: UseAiGenerationOptions = {}): AiGenerationData => {
  const { isAuthenticated } = useAuth();
  const { credits, isRateLimited, error: creditError, refetch: refetchCredits } = useAiCredits();
  const { hasConsent } = useAiConsent();
  const [prompts, setPrompts] = useState<CustomPrompt[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [generating, setGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [apiHasResponded, setApiHasResponded] = useState(false);

  /**
   * Récupère les prompts personnalisés de l'utilisateur
   */
  const getUserPrompts = useCallback(async () => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/ai-generation/prompts`, {
        headers,
        withCredentials: true
      });

      if (response.data.success) {
        setPrompts(response.data.prompts);
      } else {
        setError(response.data.message || 'Erreur lors de la récupération des prompts personnalisés');
        logger.error('Erreur lors de la récupération des prompts:', response.data);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erreur lors de la récupération des prompts personnalisés';
      setError(errorMessage);
      logger.error('Erreur lors de la récupération des prompts:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  /**
   * Enregistre un prompt personnalisé
   */
  const saveUserPrompt = async (type: GenerationType, prompt: string): Promise<boolean> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour enregistrer un prompt personnalisé', 'error');
      return false;
    }

    try {
      setLoading(true);
      setError(null);
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/ai-generation/prompts`,
        { type, prompt },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify(response.data.message || 'Prompt personnalisé enregistré avec succès', 'success');
        await getUserPrompts(); // Rafraîchir les prompts
        return true;
      } else {
        notify(response.data.message || 'Erreur lors de l\'enregistrement du prompt personnalisé', 'error');
        logger.error('Erreur lors de l\'enregistrement du prompt:', response.data);
        return false;
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erreur lors de l\'enregistrement du prompt personnalisé';
      notify(errorMessage, 'error');
      logger.error('Erreur lors de l\'enregistrement du prompt:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Supprime un prompt personnalisé
   */
  const deleteUserPrompt = async (type: GenerationType): Promise<boolean> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour supprimer un prompt personnalisé', 'error');
      return false;
    }

    try {
      setLoading(true);
      setError(null);
      const headers = await getCommonHeaders();
      const response = await axios.delete(
        `${API_CONFIG.baseURL}/api/ai-generation/prompts/${type}`,
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify(response.data.message || 'Prompt personnalisé supprimé avec succès', 'success');
        await getUserPrompts(); // Rafraîchir les prompts
        return true;
      } else {
        notify(response.data.message || 'Erreur lors de la suppression du prompt personnalisé', 'error');
        logger.error('Erreur lors de la suppression du prompt:', response.data);
        return false;
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Erreur lors de la suppression du prompt personnalisé';
      notify(errorMessage, 'error');
      logger.error('Erreur lors de la suppression du prompt:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const checkCredits = () => {
    if (isRateLimited) {
      notify('Vous avez atteint la limite de requêtes. Veuillez patienter quelques minutes.', 'error');
      return false;
    }

    if (credits <= 0) {
      notify('Vous n\'avez pas assez de crédits IA, veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
      return false;
    }

    if (creditError) {
      notify('Une erreur est survenue lors de la vérification des crédits.', 'error');
      return false;
    }

    return true;
  };

  /**
   * Génère du contenu avec l'IA
   * @param type Type de génération
   * @param context Contexte ou prompt personnalisé
   * @param customPrompt Prompt personnalisé à utiliser (optionnel)
   */
  const generateContent = async (type: GenerationType, context: string, customPrompt?: string): Promise<string | null> => {
    if (!isAuthenticated) {
      notify('Vous devez être connecté pour générer du contenu avec l\'IA', 'error');
      return null;
    }

    // Vérifier si l'utilisateur a donné son consentement pour l'utilisation de l'IA
    if (!hasConsent) {
      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
      // Déclencher un événement personnalisé pour ouvrir la modale de consentement
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      return null;
    }

    if (!checkCredits()) return null;

    try {
      setGenerating(true);
      setApiHasResponded(false);
      setError(null);
      options.onGenerationStart?.();

      // Note: Le crédit est déjà déduit côté backend, pas besoin de le faire ici
      // Vérifier simplement que l'utilisateur a des crédits
      if (credits <= 0) {
        notify('Vous n\'avez pas assez de crédits IA, veuillez en acheter dans le menu "Intelligence Artificielle"', 'error');
        return null;
      }

      // Si le contexte contient déjà un prompt contextuel complet (ex: réponse à un commentaire), on le passe en tant que customPrompt
      let useCustomPrompt = customPrompt;
      let useContext = context;
      if (
        type === 'comment' &&
        context.includes('Contexte: Réponse à un commentaire')
      ) {
        useCustomPrompt = context;
        useContext = context;
      }

      // Appeler l'API de génération
      const headers = await getCommonHeaders();
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/ai-generation/generate`,
        { type, context: useContext, customPrompt: useCustomPrompt },
        {
          headers,
          withCredentials: true
        }
      );

      setApiHasResponded(true);

      if (response.data && response.data.content) {
        const generatedContent = response.data.content;

        // Rafraîchir le solde de crédits après la génération
        // car le backend a déduit un crédit
        await refetchCredits();

        // Appeler le callback avec le contenu généré
        if (options.onGenerationComplete) {
          options.onGenerationComplete(generatedContent);
        }

        return generatedContent;
      }

      notify('Erreur lors de la génération du contenu.', 'error');
      options.onGenerationError?.(new Error('Réponse invalide de l\'API'));
      return null;
    } catch (error: any) {
      setApiHasResponded(true);
      logger.error('Erreur lors de la génération du contenu:', error);

      // Vérifier si c'est une erreur de consentement
      if (error.response?.data?.requiresConsent) {
        notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu', 'error');
        // Déclencher un événement personnalisé pour ouvrir la modale de consentement
        window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      } else {
        notify('Une erreur est survenue lors de la génération du contenu.', 'error');
      }

      options.onGenerationError?.(error);
      return null;
    } finally {
      setGenerating(false);
    }
  };

  // Charger les prompts au montage du composant
  useEffect(() => {
    getUserPrompts();
  }, [getUserPrompts]);

  return {
    prompts,
    loading,
    error,
    generating,
    apiHasResponded,
    credits,
    isRateLimited,
    creditError,
    hasConsent,
    isConsentRequired: !hasConsent && isAuthenticated,
    getUserPrompts,
    saveUserPrompt,
    deleteUserPrompt,
    generateContent
  };
};
