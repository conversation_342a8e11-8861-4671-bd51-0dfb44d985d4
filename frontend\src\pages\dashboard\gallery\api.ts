import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import { getCommonHeaders, getMultipartHeaders } from '../../../utils/headers';
import { fetchCsrfToken } from '../../../services/csrf';
import { PhotoUploadResponse, PhotosResponse } from './types';

const handleApiError = (error: any) => {
  if (axios.isAxiosError(error)) {
    throw new Error(error.response?.data?.message || 'Une erreur est survenue');
  }
  throw error;
};

export const createGallery = async (formData: FormData) => {
  await fetchCsrfToken();
  const headers = await getMultipartHeaders();
  headers['X-CSRF-Token'] = await fetchCsrfToken();
  const response = await axios.post(`${API_CONFIG.baseURL}/api/users/gallery`, formData, {
    headers,
    withCredentials: true
  });

  if (!response.data?.success) {
    throw new Error('Erreur lors de la création de la galerie');
  }

  return response.data;
};

export const updateGallery = async (galleryId: string, formData: FormData) => {
  const headers = await getMultipartHeaders();
  const response = await axios.put(`${API_CONFIG.baseURL}/api/users/gallery/${galleryId}`, formData, {
    headers,
    withCredentials: true
  });

  if (!response.data?.success) {
    throw new Error('Erreur lors de la mise à jour de la galerie');
  }

  return response.data;
};

export const deleteGallery = async (galleryId: string) => {
  await fetchCsrfToken();
  const headers = await getCommonHeaders();
  const response = await axios.delete(`${API_CONFIG.baseURL}/api/users/gallery/${galleryId}`, {
    headers,
    withCredentials: true
  });

  if (!response.data?.success) {
    throw new Error('Erreur lors de la suppression de la galerie');
  }

  return response.data;
};

export const toggleGalleryStatus = async (galleryId: string, status: 'actif' | 'inactif') => {
  await fetchCsrfToken();
  const headers = await getCommonHeaders();
  headers['X-CSRF-Token'] = await fetchCsrfToken();

  const response = await axios.patch(
    `${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/status`,
    { status },
    {
      headers,
      withCredentials: true
    }
  );

  if (!response.data?.success) {
    throw new Error(`Erreur lors de la ${status === 'actif' ? 'activation' : 'désactivation'} de la galerie`);
  }

  return response.data;
};

export const fetchGalleryPhotos = async (galleryId: string): Promise<PhotosResponse> => {
  await fetchCsrfToken();
  const headers = await getCommonHeaders();
  const response = await axios.get(`${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photos`, {
    headers,
    withCredentials: true
  });

  if (!response.data?.success) {
    throw new Error('Erreur lors de la récupération des photos');
  }

  return response.data;
};

// Upload multiple de photos dans une galerie
export const uploadPhotos = async (galleryId: string, files: File[]): Promise<{ success: boolean; photos: any[]; message: string; failedPhotos?: any[] }> => {
  try {
    await fetchCsrfToken();
    const headers = await getMultipartHeaders();
    headers['X-CSRF-Token'] = await fetchCsrfToken();

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('photo', file);
    });

    const response = await axios.post(
      `${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photos`,
      formData,
      {
        headers,
        withCredentials: true
      }
    );

    // Même si la réponse indique un échec partiel, on peut avoir des photos uploadées
    if (response.data?.success || (response.data?.photos && response.data.photos.length > 0)) {
      return {
        success: response.data.success || true,
        photos: response.data.photos || [],
        message: response.data.message || 'Photos uploadées',
        failedPhotos: response.data.failedPhotos
      };
    }

    throw new Error(response.data?.message || 'Erreur lors de l\'upload des photos');
  } catch (error: any) {
    // Si c'est une erreur 400 avec des détails, on peut avoir des uploads partiels
    if (error.response?.status === 400 && error.response?.data?.photos) {
      return {
        success: false,
        photos: error.response.data.photos || [],
        message: error.response.data.message || 'Upload partiel',
        failedPhotos: error.response.data.failedPhotos
      };
    }
    
    throw new Error(error.response?.data?.message || 'Erreur lors de l\'upload des photos');
  }
};

// Upload unique d'une photo dans une galerie (conservé pour compatibilité)
export const uploadPhoto = async (galleryId: string, formData: FormData): Promise<PhotoUploadResponse> => {
  await fetchCsrfToken();
  const headers = await getMultipartHeaders();
  headers['X-CSRF-Token'] = await fetchCsrfToken();

  const response = await axios.post(
    `${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photo`,
    formData,
    {
      headers,
      withCredentials: true
    }
  );

  if (!response.data?.success) {
    throw new Error('Erreur lors de l\'upload de la photo');
  }

  return response.data;
};

export const updatePhotoOrder = async (galleryId: string, photoId: string, newOrder: number) => {
  await fetchCsrfToken();
  let headers = await getCommonHeaders();
  headers = { ...headers, 'Content-Type': 'application/json' };
  const response = await axios.put(
    `${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photo/${photoId}`,
    { order_index: newOrder },
    {
      headers,
      withCredentials: true
    }
  );

  if (!response.data?.success) {
    throw new Error('Erreur lors de la mise à jour de l\'ordre des photos');
  }

  return response.data;
};

export const updatePhotoCaption = async (galleryId: string, photoId: string, caption: string) => {
  await fetchCsrfToken();
  let headers = await getCommonHeaders();
  headers = { ...headers, 'Content-Type': 'application/json' };
  const response = await axios.put(
    `${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photo/${photoId}`,
    { caption },
    {
      headers,
      withCredentials: true
    }
  );

  if (!response.data?.success) {
    throw new Error('Erreur lors de la mise à jour de la légende');
  }

  return response.data;
};

export const deletePhoto = async (galleryId: string, photoId: string): Promise<void> => {
  try {
    await fetchCsrfToken();
    const response = await axios.delete(`${API_CONFIG.baseURL}/api/users/gallery/${galleryId}/photo/${photoId}`, {
      headers: await getCommonHeaders(),
      withCredentials: true
    });

    // Si le statut est 204, c'est une réussite (pas de contenu)
    if (response.status === 204) {
      return;
    }

    // Pour les autres statuts de succès
    if (response.data?.success) {
      return;
    }

    throw new Error('Erreur lors de la suppression de la photo');
  } catch (error: any) {
    // Si le statut est 204, on considère que c'est un succès
    if (error.response?.status === 204) {
      return;
    }
    throw error;
  }
};

// Fonctions pour les photos mises en avant
export const fetchFeaturedPhotos = async () => {
  try {
    const response = await axios.get(`${API_CONFIG.baseURL}/api/users/featured-photos`, {
      headers: await getCommonHeaders(),
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
};

export const fetchUserFeaturedPhotos = async (userId: string) => {
  try {
    const response = await axios.get(`${API_CONFIG.baseURL}/api/users/featured-photos/${userId}`, {
      headers: await getCommonHeaders(),
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
};

export const uploadFeaturedPhoto = async (formData: FormData) => {
  await fetchCsrfToken();
  const headers = await getMultipartHeaders();
  headers['X-CSRF-Token'] = await fetchCsrfToken();
  try {
    const response = await axios.post(`${API_CONFIG.baseURL}/api/users/featured-photos`, formData, {
      headers,
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
};

export const deleteFeaturedPhoto = async (photoId: string) => {
  try {
    const response = await axios.delete(`${API_CONFIG.baseURL}/api/users/featured-photos/${photoId}`, {
      headers: await getCommonHeaders(),
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
};

export const updateFeaturedPhotoCaption = async (photoId: string, caption: string) => {
  try {
    const formData = new FormData();
    formData.append('caption', caption);

    const response = await axios.put(`${API_CONFIG.baseURL}/api/users/featured-photos/${photoId}/caption`, formData, {
      headers: await getMultipartHeaders(),
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
};