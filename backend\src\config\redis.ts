import Redis from 'ioredis';
import logger from '../utils/logger';
import { RedisOptions } from 'ioredis';

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  tls?: {
    rejectUnauthorized: boolean;
  };
  maxRetriesPerRequest?: number;
  retryStrategy?: (times: number) => number | null;
  enableReadyCheck: boolean;
  maxReconnectAttempts: number;
  reconnectOnError: (err: Error) => boolean;
  disconnectTimeout: number;
}

class RedisService {
  private static instance: Redis | null = null;
  private static config: RedisConfig;

  private constructor() {}

  public static getInstance(): Redis {
    if (!RedisService.instance) {
      RedisService.config = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        maxRetriesPerRequest: 3,
        retryStrategy: (times: number) => {
          if (times > 3) {
            return null;
          }
          return Math.min(times * 100, 3000);
        },
        enableReadyCheck: true,
        maxReconnectAttempts: 10,
        reconnectOnError: (err) => {
          const targetError = 'READONLY';
          if (err.message.includes(targetError)) {
            return true;
          }
          return false;
        },
        disconnectTimeout: 5000
      };

      // Ajouter TLS si nécessaire
      if (process.env.REDIS_TLS === 'true') {
        RedisService.config.tls = {
          rejectUnauthorized: false
        };
      }

      // Ajouter le mot de passe si présent
      if (process.env.REDIS_PASSWORD) {
        RedisService.config.password = process.env.REDIS_PASSWORD;
      }

      try {
        RedisService.instance = new Redis(RedisService.config as RedisOptions);

        // Augmenter la limite de listeners
        RedisService.instance.setMaxListeners(100);

        // Gérer les événements de connexion
        RedisService.instance.on('error', (error) => {
          logger.error('Redis error:', error);
        });

        RedisService.instance.on('connect', () => {
          logger.info('Connected to Redis');
        });

        // Gérer la déconnexion proprement
        RedisService.instance.on('end', () => {
          logger.info('Redis connection ended');
          RedisService.instance = null;
        });

        // Gérer les reconnexions
        RedisService.instance.on('reconnecting', () => {
          logger.info('Redis reconnecting...');
        });

        // Nettoyer les listeners lors de la fermeture
        process.on('SIGTERM', async () => {
          await RedisService.close();
        });

        process.on('SIGINT', async () => {
          await RedisService.close();
        });

      } catch (error) {
        logger.error('Failed to create Redis instance:', error);
        throw error;
      }
    }

    return RedisService.instance;
  }

  public static async close(): Promise<void> {
    if (RedisService.instance) {
      // Supprimer tous les listeners avant de fermer
      RedisService.instance.removeAllListeners();
      await RedisService.instance.quit();
      RedisService.instance = null;
    }
  }
}

// Exporter l'instance Redis et le service
export const redis = RedisService.getInstance();
export default RedisService;
