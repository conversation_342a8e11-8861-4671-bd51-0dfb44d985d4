import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  useTheme,
  Alert,
  CircularProgress,
  Button,
  ButtonGroup,
  IconButton,
  Stack,
  LinearProgress,
  Chip,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  TextField
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';
import {
  Assignment as AssignmentIcon,
  ListAlt as ListAltIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as AccessTimeIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Category as CategoryIcon,
  Flag as FlagIcon,
  Timeline as TimelineIcon,
  Group as GroupIcon,
  Speed as SpeedIcon,
  FilterList as FilterListIcon,
  LocalOffer as LocalOfferIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useTicketStats } from '../../hooks/useTicketStats';
import { useNavigate } from 'react-router-dom';

const SupportDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { 
    stats, 
    loading, 
    error, 
    refreshStats, 
    timeRange, 
    setTimeRange,
    startDate,
    endDate,
    setStartDate,
    setEndDate,
    useCustomDateRange,
    setUseCustomDateRange
  } = useTicketStats();
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';

  // Rediriger les utilisateurs non autorisés
  useEffect(() => {
    if (isAuthenticated && !isStaff) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, isStaff, navigate]);

  const COLORS = [
    '#FF6B2C', // Orange primaire
    '#FF7A35', // Orange secondaire
    '#FF965E', // Orange tertiaire
    '#FFE4BA', // Orange clair
    '#4CAF50', // Vert
    '#2196F3', // Bleu
    '#F44336', // Rouge
    '#FFC107', // Jaune
  ];

  const handleRefresh = () => {
    refreshStats();
  };

  const handleTimeRangeChange = (event: any) => {
    // Désactiver la plage personnalisée quand on sélectionne une période prédéfinie
    setUseCustomDateRange(false);
    setTimeRange(event.target.value);
  };

  const handleCustomDateRangeToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUseCustomDateRange(event.target.checked);
  };

  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(event.target.value ? new Date(event.target.value) : null);
  };

  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(event.target.value ? new Date(event.target.value) : null);
  };

  // Formater les dates pour l'entrée date
  const formatDateForInput = (date: Date | null) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Alert severity="warning">
            Veuillez vous connecter pour accéder à cette page.
          </Alert>
          <Box mt={2}>
            <Button variant="contained" color="primary" onClick={() => navigate('/login')}>
              Se connecter
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }

  if (!isStaff) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Alert severity="warning">
            Vous n'avez pas accès à cette page. Seuls les administrateurs et modérateurs peuvent y accéder.
          </Alert>
          <Box mt={2}>
            <Button variant="contained" color="primary" onClick={() => navigate('/dashboard')}>
              Retour au tableau de bord
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box py={4} display="flex" flexDirection="column" alignItems="center">
          <CircularProgress size={60} sx={{ color: '#FF6B2C' }} />
          <Typography sx={{ mt: 2 }}>
            Chargement des statistiques...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Alert severity="error">
            {error.message || 'Une erreur est survenue lors du chargement des statistiques. Veuillez réessayer plus tard.'}
          </Alert>
          <Box mt={2}>
            <Button 
              variant="contained" 
              onClick={() => refreshStats()}
              sx={{
                bgcolor: '#FF6B2C',
                '&:hover': {
                  bgcolor: '#FF7A35',
                },
              }}
            >
              Réessayer
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }

  // S'assurer que toutes les données nécessaires sont disponibles
  const ticketsByStatus = stats?.ticketsByStatus || [];
  const ticketsByPriority = stats?.ticketsByPriority || [];
  const ticketsByCategory = stats?.ticketsByCategory || [];
  const ticketsOverTime = stats?.ticketsOverTime || [];

  // Calculer le taux de résolution
  const resolutionRate = stats?.resolvedTickets 
    ? ((stats.resolvedTickets / stats.totalTickets) * 100).toFixed(1) 
    : 0;

  // Calculer le temps de réponse moyen en heures
  const avgResponseTime = stats?.averageResponseTime || 0;
  const avgResponseTimeColor = avgResponseTime <= 24 
    ? '#4CAF50' 
    : avgResponseTime <= 48 
      ? '#FFC107' 
      : '#F44336';

  return (
    <Container maxWidth="xl">
      <Box py={4}>
        {/* En-tête */}
        <Box 
          display="flex" 
          flexDirection={{ xs: 'column', md: 'row' }}
          justifyContent="space-between" 
          alignItems={{ xs: 'stretch', md: 'center' }}
          mb={4}
          sx={{
            background: 'linear-gradient(135deg, #FFFFFF 0%, #FFF8F3 100%)',
            borderRadius: 2,
            p: { xs: 2, sm: 3 },
            boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)',
            border: '1px solid rgba(255, 107, 44, 0.1)',
          }}
        >
          <Box mb={{ xs: 2, md: 0 }}>
            <Typography 
              variant="h4" 
              component="h1" 
              gutterBottom 
              fontWeight="600"
              color="#FF6B2C"
              sx={{
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.25rem' },
                borderBottom: '2px solid rgba(255, 107, 44, 0.1)',
                paddingBottom: 1,
                marginBottom: 1,
              }}
            >
              Tableau de Bord Support
            </Typography>
            <Typography 
              variant="subtitle1" 
              color="text.secondary"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
              }}
            >
              Vue d'ensemble des tickets et des performances
            </Typography>
          </Box>
          
          <Stack 
            direction={{ xs: 'column', sm: 'row' }} 
            spacing={2} 
            alignItems={{ xs: 'stretch', sm: 'center' }}
            width={{ xs: '100%', md: 'auto' }}
          >
            {!useCustomDateRange && (
              <FormControl 
                variant="outlined" 
                size="small"
                sx={{ 
                  minWidth: 120,
                  '& .MuiOutlinedInput-root': {
                    color: '#FF6B2C',
                    '& fieldset': {
                      borderColor: 'rgba(255, 107, 44, 0.3)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FF6B2C',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF6B2C',
                    },
                  },
                  '& .MuiInputLabel-root': {
                    color: '#FF6B2C',
                  },
                  '& .MuiSelect-icon': {
                    color: '#FF6B2C',
                  },
                }}
              >
                <InputLabel>Période</InputLabel>
                <Select
                  value={timeRange}
                  label="Période"
                  onChange={handleTimeRangeChange}
                >
                  <MenuItem value="7">7 jours</MenuItem>
                  <MenuItem value="30">30 jours</MenuItem>
                  <MenuItem value="90">90 jours</MenuItem>
                </Select>
              </FormControl>
            )}
            
            <FormControlLabel
              control={
                <Switch 
                  checked={useCustomDateRange}
                  onChange={handleCustomDateRangeToggle}
                  sx={{
                    '& .MuiSwitch-switchBase.Mui-checked': {
                      color: '#FF6B2C',
                    },
                    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                      backgroundColor: 'rgba(255, 107, 44, 0.5)',
                    },
                  }}
                />
              }
              label="Dates personnalisées"
              sx={{ 
                color: '#FF6B2C',
                m: 0,
                width: { xs: '100%', sm: 'auto' }
              }}
            />
            
            {useCustomDateRange && (
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={2} 
                width={{ xs: '100%', sm: 'auto' }}
              >
                <TextField
                  label="Du"
                  type="date"
                  value={formatDateForInput(startDate)}
                  onChange={handleStartDateChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  size="small"
                  fullWidth
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: '#FF6B2C',
                      '& fieldset': {
                        borderColor: 'rgba(255, 107, 44, 0.3)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: '#FF6B2C',
                    },
                  }}
                />
                
                <TextField
                  label="Au"
                  type="date"
                  value={formatDateForInput(endDate)}
                  onChange={handleEndDateChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  size="small"
                  fullWidth
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: '#FF6B2C',
                      '& fieldset': {
                        borderColor: 'rgba(255, 107, 44, 0.3)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FF6B2C',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FF6B2C',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: '#FF6B2C',
                    },
                  }}
                />
              </Stack>
            )}
            
            <Stack 
              direction={{ xs: 'column', sm: 'row' }} 
              spacing={2} 
              width={{ xs: '100%', sm: 'auto' }}
            >
              <ButtonGroup 
                variant="outlined"
                orientation={window.innerWidth < 600 ? 'vertical' : 'horizontal'}
                fullWidth
                sx={{
                  '& .MuiButton-root': {
                    color: '#FF6B2C',
                    borderColor: 'rgba(255, 107, 44, 0.3)',
                    '&:hover': {
                      borderColor: '#FF6B2C',
                      backgroundColor: 'rgba(255, 107, 44, 0.1)',
                    },
                  },
                }}
              >
                <Button 
                  startIcon={<ListAltIcon />}
                  onClick={() => navigate('/admin/support/tickets')}
                  fullWidth
                >
                  Tous les tickets
                </Button>
                <Button 
                  startIcon={<PersonIcon />}
                  onClick={() => navigate('/admin/support/assigned')}
                  fullWidth
                >
                  Mes tickets
                </Button>
              </ButtonGroup>

              <IconButton 
                onClick={handleRefresh}
                sx={{ 
                  color: '#FF6B2C',
                  alignSelf: { xs: 'center', sm: 'auto' },
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 44, 0.1)',
                  },
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Stack>
          </Stack>
        </Box>

        <Grid container spacing={4}>
          {/* KPIs */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card 
              sx={{ 
                color: '#FF6B2C',
                padding: 2,
                '& .MuiCardContent-root': {
                  padding: { xs: 1, sm: 2 },
                  '&:last-child': {
                    paddingBottom: { xs: 1, sm: 2 }
                  }
                },
                overflow: 'hidden'
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <AssignmentIcon sx={{ mr: 1.5, fontSize: '1.5rem' }} />
                  <Typography 
                    variant="h6" 
                    fontSize={{ xs: '1rem', sm: '1.25rem' }}
                    sx={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    Total des tickets
                  </Typography>
                </Box>
                <Typography variant="h3" fontWeight="600" fontSize={{ xs: '2rem', sm: '3rem' }}>
                  {stats?.totalTickets || 0}
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, opacity: 0.8 }}>
                  Sur les {timeRange} derniers jours
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ padding: 2 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <WarningIcon sx={{ mr: 1, color: theme.palette.warning.main }} />
                  <Typography variant="h6" color="warning.main">
                    Tickets ouverts
                  </Typography>
                </Box>
                <Typography variant="h3" color="warning.main" fontWeight="600">
                  {stats?.openTickets || 0}
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <Typography variant="body2" color="text.secondary">
                    {((stats?.openTickets || 0) / (stats?.totalTickets || 1) * 100).toFixed(1)}% du total
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ padding: 2 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <CheckCircleIcon sx={{ mr: 1, color: theme.palette.success.main }} />
                  <Typography variant="h6" color="success.main">
                    Tickets résolus
                  </Typography>
                </Box>
                <Typography variant="h3" color="success.main" fontWeight="600">
                  {stats?.resolvedTickets || 0}
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <Typography variant="body2" color="text.secondary">
                    Taux de résolution: {resolutionRate}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Card sx={{ padding: 2 }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <AccessTimeIcon sx={{ mr: 1, color: avgResponseTimeColor }} />
                  <Typography variant="h6" sx={{ color: avgResponseTimeColor }}>
                    Temps de réponse
                  </Typography>
                </Box>
                <Typography variant="h3" sx={{ color: avgResponseTimeColor }} fontWeight="600">
                  {avgResponseTime}h
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <Typography variant="body2" color="text.secondary">
                    Moyenne sur {timeRange} jours
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Graphiques principaux */}
          <Grid size={{ xs: 12, md: 8 }}>
            <Paper 
              sx={{ 
                p: { xs: 2, sm: 3 }, 
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 64px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={1}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 1, sm: 0 }}
                >
                  Évolution des tickets
                </Typography>
                <Chip 
                  icon={<TimelineIcon />} 
                  label={`${timeRange} derniers jours`}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart 
                  data={ticketsOverTime}
                  margin={{
                    top: 5,
                    right: 10,
                    left:  0,
                    bottom: 5,
                  }}
                >
                  <defs>
                    <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#FF6B2C" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#FF6B2C" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="date" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    interval={timeRange === '7' ? 0 : timeRange === '30' ? 2 : 5}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return value;
                    }}
                  />
                  <YAxis 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={30}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="count" 
                    stroke="#FF6B2C" 
                    fillOpacity={1} 
                    fill="url(#colorCount)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Distribution par statut */}
          <Grid size={{ xs: 12, sm: 12, md: 4 }}>
            <Paper 
              sx={{ 
                p: '24px 24px 94px 24px', 
                height: { xs: 400, sm: 400 },
                background: '#FFFFFF',
                borderRadius: 2,
                boxShadow: '0 2px 8px rgba(255, 107, 44, 0.1)',
                border: '1px solid rgba(255, 107, 44, 0.1)',
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px'
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Distribution par statut
                </Typography>
                <Chip 
                  icon={<FlagIcon />} 
                  label="Statuts"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={ticketsByStatus}
                    dataKey="count"
                    nameKey="status"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    innerRadius={40}
                    labelLine={false}
                    label={({ name, percent, x, y, cx }) => {
                      const percentage = (percent * 100).toFixed(0);
                      return (
                        <text
                          x={x}
                          y={y}
                          fill="#666"
                          textAnchor={x > Number(cx) ? 'start' : 'end'}
                          dominantBaseline="central"
                          fontSize="12px"
                        >
                          {`${percentage}%`}
                        </text>
                      );
                    }}
                  >
                    {ticketsByStatus.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={COLORS[index % COLORS.length]}
                        stroke="white"
                        strokeWidth={2}
                      />
                    ))}
                  </Pie>
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      padding: '8px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    }}
                    formatter={(value: number, name: string) => [
                      `${value} tickets`,
                      name && typeof name === 'string' && name.length > 0
                        ? name.charAt(0).toUpperCase() + name.slice(1)
                        : ''
                    ]}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36}
                    formatter={(value: string) =>
                      value && typeof value === 'string' && value.length > 0
                        ? value.charAt(0).toUpperCase() + value.slice(1)
                        : ''
                    }
                    wrapperStyle={{
                      fontSize: '0.75rem',
                      paddingTop: '16px',
                    }}
                    iconSize={8}
                    iconType="circle"
                  />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Distribution par priorité */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                padding: '24px 24px 64px 24px',
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Distribution par priorité
                </Typography>
                <Chip 
                  icon={<SpeedIcon />} 
                  label="Priorités"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart 
                  data={ticketsByPriority}
                  margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="priority" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    interval={0}
                  />
                  <YAxis 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={30}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                  />
                  <Bar 
                    dataKey="count" 
                    fill="#FF6B2C"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Distribution par catégorie */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                padding: '24px 24px 64px 24px',
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Distribution par catégorie
                </Typography>
                <Chip 
                  icon={<CategoryIcon />} 
                  label="Catégories"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart 
                  data={ticketsByCategory}
                  margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="category" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    interval={0}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={30}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                  />
                  <Bar 
                    dataKey="count" 
                    fill="#FF7A35"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Distribution par agent */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                padding: '24px 24px 64px 24px',
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Distribution par agent
                </Typography>
                <Chip 
                  icon={<GroupIcon />} 
                  label="Agents"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart 
                  data={stats.ticketsByAgent} 
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    type="number" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                  />
                  <YAxis 
                    dataKey="agent" 
                    type="category"
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={90}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                  />
                  <Bar 
                    dataKey="count" 
                    fill="#FF965E"
                    radius={[0, 4, 4, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Temps de résolution par catégorie */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                padding: '24px 24px 64px 24px',
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Temps de résolution par catégorie
                </Typography>
                <Chip 
                  icon={<AccessTimeIcon />} 
                  label="Heures"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart 
                  data={stats.resolutionByCategory}
                  margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="category" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    interval={0}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={30}
                    label={{ 
                      value: 'Heures', 
                      angle: -90, 
                      position: 'insideLeft',
                      style: {
                        fontSize: '0.75rem',
                      }
                    }}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                    formatter={(value: any) => [`${value} heures`, 'Temps moyen']}
                  />
                  <Bar 
                    dataKey="time" 
                    fill="#4CAF50"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Heures de pointe */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                padding: '24px 24px 64px 24px',
                height: { xs: 500, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2,
                '@media (max-width: 600px)': {
                  p: '24px 14px 134px 14px',
                  width: '100%',
                },
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
                sx={{
                  gap: { xs: 1, sm: 2 }
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Heures de pointe
                </Typography>
                <Chip 
                  icon={<TimelineIcon />} 
                  label="24h"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart 
                  data={stats.peakHours}
                  margin={{
                    top: 5,
                    right: 10,
                    left: 0,
                    bottom: 5,
                  }}
                >
                  <defs>
                    <linearGradient id="colorPeak" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#FF6B2C" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#FF6B2C" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                  <XAxis 
                    dataKey="hour" 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    interval={window.innerWidth < 600 ? 2 : 0}
                  />
                  <YAxis 
                    stroke="#666"
                    tick={{ 
                      fill: '#666',
                      fontSize: '0.75rem',
                    }}
                    width={30}
                  />
                  <RechartsTooltip 
                    contentStyle={{
                      backgroundColor: '#FFF',
                      border: '1px solid #FF6B2C',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                    }}
                    formatter={(value: any) => [`${value} tickets`, 'Nombre']}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="count" 
                    stroke="#FF6B2C" 
                    fillOpacity={1} 
                    fill="url(#colorPeak)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>

          {/* Distribution des tags */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper 
              sx={{ 
                p: { xs: 2.5, sm: 3.5 },
                height: { xs: 350, sm: 400 },
                // background: '#FFF8F3',
                borderRadius: 2
              }}
            >
              <Box 
                display="flex" 
                flexDirection={{ xs: 'column', sm: 'row' }} 
                justifyContent="space-between" 
                alignItems={{ xs: 'flex-start', sm: 'center' }}
                mb={4}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="600" 
                  color="#FF6B2C"
                  fontSize={{ xs: '1rem', sm: '1.25rem' }}
                  mb={{ xs: 2, sm: 0 }}
                  sx={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  Distribution des tags
                </Typography>
                <Chip 
                  icon={<LocalOfferIcon />} 
                  label="Top 10"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              <Box sx={{ height: { xs: 200, sm: 300 } }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart 
                    data={stats.tagsDistribution.slice(0, 10)}
                    layout="vertical"
                    margin={{ 
                      top: 5, 
                      right: 30, 
                      left: 0,  
                      bottom: 5 
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#E0E0E0" />
                    <XAxis 
                      type="number" 
                      stroke="#666"
                      tick={{ 
                        fill: '#666',
                        fontSize: '0.75rem',
                      }}
                    />
                    <YAxis 
                      dataKey="tag" 
                      type="category"
                      stroke="#666"
                      tick={{ 
                        fill: '#666',
                        fontSize: '0.75rem',
                      }}
                      width={window.innerWidth < 600 ? 50 : 90}
                    />
                    <RechartsTooltip 
                      contentStyle={{
                        backgroundColor: '#FFF',
                        border: '1px solid #FF6B2C',
                        borderRadius: '4px',
                        fontSize: '0.75rem',
                      }}
                    />
                    <Bar 
                      dataKey="count" 
                      fill="#FFE4BA"
                      radius={[0, 4, 4, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Grid>

          {/* Indicateurs de performance */}
          <Grid size={12}>
            <Paper 
              sx={{ 
                p: 3,
                // background: '#FFF8F3',
                borderRadius: 2,
              }}
            >
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" fontWeight="600" color="#FF6B2C">
                  Indicateurs de Performance
                </Typography>
                <Chip 
                  icon={<TrendingUpIcon />} 
                  label="KPIs"
                  sx={{
                    bgcolor: 'rgba(255, 107, 44, 0.1)',
                    color: '#FF6B2C',
                  }}
                />
              </Box>
              
              <Grid container spacing={3}>
                {/* Taux de résolution */}
                <Grid size={{ xs: 12, md: 3 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Taux de résolution
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Box flexGrow={1} mr={2}>
                        <LinearProgress
                          variant="determinate"
                          value={Number(resolutionRate)}
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#FF6B2C',
                              borderRadius: 5,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {resolutionRate}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Temps de réponse moyen */}
                <Grid size={{ xs: 12, md: 3 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Temps de réponse moyen
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Box flexGrow={1} mr={2}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((avgResponseTime / 48) * 100, 100)}
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: avgResponseTimeColor,
                              borderRadius: 5,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {avgResponseTime}h
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Premier temps de réponse */}
                <Grid size={{ xs: 12, md: 3 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Premier temps de réponse
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Box flexGrow={1} mr={2}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((stats.averageFirstResponseTime / 24) * 100, 100)}
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#FF6B2C',
                              borderRadius: 5,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {stats.averageFirstResponseTime}h
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Satisfaction client */}
                <Grid size={{ xs: 12, md: 3 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Satisfaction client
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Box flexGrow={1} mr={2}>
                        <LinearProgress
                          variant="determinate"
                          value={stats.averageSatisfaction * 20} // Convertir note sur 5 en pourcentage
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#4CAF50',
                              borderRadius: 5,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {stats.averageSatisfaction.toFixed(1)}/5
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Conformité SLA */}
                <Grid size={{ xs: 12, md: 3 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Conformité SLA
                    </Typography>
                    <Box display="flex" alignItems="center">
                      <Box flexGrow={1} mr={2}>
                        <LinearProgress
                          variant="determinate"
                          value={stats.slaComplianceRate}
                          sx={{
                            height: 10,
                            borderRadius: 5,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#9C27B0',
                              borderRadius: 5,
                            },
                          }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {stats.slaComplianceRate}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default SupportDashboard; 