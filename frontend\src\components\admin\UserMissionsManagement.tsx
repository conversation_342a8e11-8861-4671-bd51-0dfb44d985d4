import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  Avatar,
  IconButton,
  LinearProgress,
  Tooltip,
  Paper,
  styled,
  useTheme,
  useMediaQuery,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Activity,
  CheckCircle,
  Clock,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  User,
  Calendar,
  MapPin,
  Star,
  Briefcase,
  BarChart2,
  RefreshCw
} from 'lucide-react';
import { missionApi } from '../../pages/dashboard/missions/missionApi';
import { notify } from '../Notification';

interface Mission {
  id: string;
  title: string;
  description: string;
  category: string;
  status: 'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled';
  budget: number;
  created_at: string;
  start_date?: string;
  end_date?: string;
  location: string;
  client_name: string;
  client_id: string;
  applications_count: number;
  candidatures?: Array<{
    id: string;
    jobbeur_id: string;
    statut: string;
    message: string;
    montant_propose: number;
    montant_contre_offre?: number;
    created_at: string;
    jobbeur?: {
      id: string;
      email: string;
      nom?: string;
      prenom?: string;
      photo_url?: string;
      ville?: string;
      type_de_profil?: string;
    };
  }>;
  selected_freelancer?: {
    id: string;
    name: string;
    avatar?: string;
  };
  completion_percentage?: number;
  rating?: number;
  review?: string;
}

interface UserMissionsManagementProps {
  userId: string;
  missions: Mission[];
  onUpdate: () => void;
}

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés pour les composants
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  position: 'relative',
  paddingLeft: theme.spacing(1.5),
  '&::before': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '4px',
    height: '24px',
    backgroundColor: COLORS.primary,
    borderRadius: '2px',
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(2.5),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const MissionCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  height: '100%',
  padding: '14px',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
}));

const IconBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${COLORS.primary}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: COLORS.primary,
  position: 'absolute',
  top: '-15px',
  right: '20px',
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '8px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 600,
  padding: '8px 16px',
  '&.MuiButton-contained': {
    backgroundColor: COLORS.primary,
    '&:hover': {
      backgroundColor: COLORS.secondary,
      boxShadow: '0 4px 10px rgba(255, 107, 44, 0.3)',
    },
  },
}));

const UserMissionsManagement: React.FC<UserMissionsManagementProps> = ({
  userId,
  missions,
  onUpdate
}) => {
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [candidaturesDialogOpen, setCandidaturesDialogOpen] = useState(false);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [action, setAction] = useState('');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newStatus, setNewStatus] = useState<'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled'>('in_progress');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleMissionAction = (mission: Mission, actionType: string) => {
    setSelectedMission(mission);
    setAction(actionType);
    setNewStatus(mission.status); // Initialiser avec le statut actuel
    setActionDialogOpen(true);
  };

  const executeAction = async () => {
    if (!selectedMission || !action) return;

    try {
      setLoading(true);
      setError(null);

      if (action === 'moderate') {
        await missionApi.moderateMission(selectedMission.id, newStatus, reason);
        notify('Mission modérée avec succès', 'success');
      } else if (action === 'delete') {
        await missionApi.adminDeleteMission(selectedMission.id, reason);
        notify('Mission supprimée avec succès', 'success');
      }

      // Fermer le dialog
      setActionDialogOpen(false);
      setSelectedMission(null);
      setAction('');
      setReason('');
      setNewStatus('in_progress');
      onUpdate();
    } catch (error: any) {
      console.error('Erreur lors de l\'action mission:', error);
      const errorMessage = error?.response?.data?.message || 'Erreur lors de l\'action';
      setError(errorMessage);
      notify(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'primary';
      case 'draft':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Terminée';
      case 'in_progress':
        return 'En cours';
      case 'draft':
        return 'En attente';
      case 'cancelled':
        return 'Annulée';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} />;
      case 'in_progress':
        return <Clock size={16} />;
      case 'draft':
        return <Briefcase size={16} />;
      case 'cancelled':
        return <AlertTriangle size={16} />;
      default:
        return <Briefcase size={16} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  // Statistiques des missions
  const stats = {
    total: missions.length,
    completed: missions.filter(m => m.status === 'completed').length,
    inProgress: missions.filter(m => m.status === 'in_progress').length,
    published: missions.filter(m => m.status === 'draft').length,
    totalBudget: missions.reduce((sum, m) => sum + m.budget, 0),
    averageRating: missions.filter(m => m.rating).reduce((sum, m) => sum + (m.rating || 0), 0) / missions.filter(m => m.rating).length || 0
  };

  return (
    <Box sx={{ pb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BarChart2 size={24} color={COLORS.primary} />
          <Typography variant="h5" fontWeight="bold" color="#2D3748">
            Gestion des Missions
          </Typography>
        </Box>
        
        <StyledButton 
          variant="contained" 
          startIcon={<RefreshCw size={16} />}
          onClick={onUpdate}
        >
          Actualiser
        </StyledButton>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3, borderRadius: '8px' }}>
          {error}
        </Alert>
      )}

      {/* Statistiques */}
      <StyledPaper sx={{ mb: 4, position: 'relative' }}>
        <IconBox>
          <Activity size={20} />
        </IconBox>
        
        <SectionTitle>Aperçu des Missions</SectionTitle>
        
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard sx={{ bgcolor: '#f0f7ff' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Briefcase size={18} color={COLORS.primary} />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  Total
                </Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" color={COLORS.primary}>
                {stats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                missions
              </Typography>
            </MetricCard>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard sx={{ bgcolor: '#f0fff7' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CheckCircle size={18} color={COLORS.success} />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  Terminées
                </Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" color={COLORS.success}>
                {stats.completed}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                missions
              </Typography>
            </MetricCard>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard sx={{ bgcolor: '#fff8f0' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Clock size={18} color={COLORS.warning} />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  En cours
                </Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" color={COLORS.warning}>
                {stats.inProgress}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                missions
              </Typography>
            </MetricCard>
          </Grid>
          
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <MetricCard sx={{ bgcolor: '#f7f0ff' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <DollarSign size={18} color={COLORS.info} />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  Budget total
                </Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold" color={COLORS.info}>
                {formatCurrency(stats.totalBudget)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                tous statuts confondus
              </Typography>
            </MetricCard>
          </Grid>
        </Grid>
      </StyledPaper>

      {/* Liste des missions */}
      <StyledPaper sx={{ position: 'relative' }}>
        <IconBox>
          <Briefcase size={20} />
        </IconBox>
        
        <SectionTitle>Liste des Missions</SectionTitle>
        
        {missions.length > 0 ? (
          <Grid container spacing={3}>
            {missions.map((mission) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={mission.id}>
                <MissionCard>
                  <CardContent sx={{ p: 3, flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 600, color: '#2D3748' }}>
                        {mission.title}
                      </Typography>
                      <Chip
                        label={getStatusLabel(mission.status)}
                        color={getStatusColor(mission.status) as any}
                        size="small"
                        icon={getStatusIcon(mission.status)}
                        sx={{ borderRadius: '8px', fontWeight: 500 }}
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3, minHeight: 40 }}>
                      {mission.description.length > 100 
                        ? `${mission.description.substring(0, 100)}...` 
                        : mission.description}
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <DollarSign size={16} color={COLORS.success} />
                        <Typography variant="body2" fontWeight="bold" color={COLORS.success}>
                          {formatCurrency(mission.budget)}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <MapPin size={16} color={COLORS.neutral} />
                        <Typography variant="body2" color="text.secondary">
                          {mission.location}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <User size={16} color={COLORS.neutral} />
                        <Typography variant="body2" color="text.secondary">
                          Client: {mission.client_name}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Calendar size={16} color={COLORS.neutral} />
                        <Typography variant="body2" color="text.secondary">
                          Créée le {formatDate(mission.created_at)}
                        </Typography>
                      </Box>

                      {/* Candidatures - Toujours afficher */}
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <User size={16} color={COLORS.primary} />
                        <Typography variant="body2" color="text.secondary">
                          Candidatures: {mission.candidatures?.length || 0}
                        </Typography>
                        {(mission.candidatures?.length || 0) > 0 && (
                          <Chip
                            label={`${mission.candidatures?.length} offres`}
                            size="small"
                            color="primary"
                            sx={{
                              fontSize: '0.65rem',
                              height: 20,
                              fontWeight: 500,
                              bgcolor: COLORS.primary,
                              color: 'white'
                            }}
                          />
                        )}
                      </Box>
                    </Box>

                    {/* Candidatures reçues */}
                    {mission.candidatures && mission.candidatures.length > 0 && (
                      <Box sx={{ mt: 2.5, mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: '#2D3748' }}>
                          Candidatures reçues ({mission.candidatures.length})
                        </Typography>
                        <Box sx={{ maxHeight: 120, overflowY: 'auto' }}>
                          {mission.candidatures.slice(0, 3).map((candidature) => (
                            <Box key={candidature.id} sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1.5,
                              mb: 1,
                              p: 1.5,
                              bgcolor: 'rgba(255, 107, 44, 0.05)',
                              borderRadius: 2,
                              border: '1px solid rgba(255, 107, 44, 0.1)'
                            }}>
                              <Avatar sx={{
                                width: 28,
                                height: 28,
                                fontSize: '0.75rem',
                                bgcolor: COLORS.primary,
                                color: 'white'
                              }}>
                                {candidature.jobbeur?.prenom?.charAt(0) || candidature.jobbeur?.email?.charAt(0) || 'J'}
                              </Avatar>
                              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                                <Typography variant="caption" sx={{ fontWeight: 600, display: 'block' }}>
                                  {candidature.jobbeur?.prenom} {candidature.jobbeur?.nom}
                                  {candidature.jobbeur?.ville && (
                                    <Typography component="span" variant="caption" color="text.secondary">
                                      {' • '}{candidature.jobbeur.ville}
                                    </Typography>
                                  )}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                  {formatCurrency(candidature.montant_propose)} • {formatDate(candidature.created_at)}
                                </Typography>
                              </Box>
                              <Chip
                                label={candidature.statut}
                                size="small"
                                color={candidature.statut === 'acceptée' ? 'success' :
                                       candidature.statut === 'refusée' ? 'error' : 'default'}
                                sx={{
                                  fontSize: '0.65rem',
                                  height: 22,
                                  fontWeight: 500
                                }}
                              />
                            </Box>
                          ))}
                          {mission.candidatures.length > 3 && (
                            <Typography variant="caption" color="text.secondary" sx={{
                              fontStyle: 'italic',
                              display: 'block',
                              textAlign: 'center',
                              mt: 1
                            }}>
                              +{mission.candidatures.length - 3} autres candidatures...
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    )}

                    {mission.completion_percentage !== undefined && (
                      <Box sx={{ mt: 2.5, mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" fontWeight={500}>Progression</Typography>
                          <Typography variant="body2" fontWeight={600} color={COLORS.primary}>
                            {mission.completion_percentage}%
                          </Typography>
                        </Box>
                        <LinearProgress 
                          variant="determinate" 
                          value={mission.completion_percentage} 
                          sx={{ 
                            height: 8, 
                            borderRadius: 4,
                            backgroundColor: 'rgba(255, 107, 44, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: COLORS.primary
                            }
                          }}
                        />
                      </Box>
                    )}

                    {mission.rating && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                        <Star size={16} color={COLORS.warning} />
                        <Typography variant="body2" fontWeight={500}>
                          {mission.rating}/5
                        </Typography>
                      </Box>
                    )}
                  </CardContent>

                  <CardActions sx={{ justifyContent: 'space-between', px: 3, pb: 3, pt: 0, flexWrap: 'wrap', gap: 1 }}>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <StyledButton
                        variant="outlined"
                        size="small"
                        startIcon={<Eye size={16} />}
                        onClick={() => handleMissionAction(mission, 'view_details')}
                        sx={{
                          borderColor: COLORS.primary,
                          color: COLORS.primary,
                          '&:hover': {
                            backgroundColor: 'rgba(255, 107, 44, 0.04)',
                            borderColor: COLORS.primary
                          }
                        }}
                      >
                        Détails
                      </StyledButton>

                      <StyledButton
                        variant="contained"
                        size="small"
                        startIcon={<User size={16} />}
                        onClick={() => {
                          setSelectedMission(mission);
                          setCandidaturesDialogOpen(true);
                        }}
                        sx={{
                          bgcolor: COLORS.primary,
                          color: 'white',
                          '&:hover': {
                            bgcolor: COLORS.primary,
                            opacity: 0.9
                          }
                        }}
                      >
                        Candidatures ({mission.candidatures?.length || 0})
                      </StyledButton>
                    </Box>

                    <Box>
                      <Tooltip title="Modérer">
                        <IconButton
                          size="small"
                          onClick={() => handleMissionAction(mission, 'moderate')}
                          sx={{
                            color: COLORS.warning,
                            '&:hover': { backgroundColor: 'rgba(255, 193, 7, 0.1)' }
                          }}
                        >
                          <AlertTriangle size={18} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Supprimer">
                        <IconButton
                          size="small"
                          onClick={() => handleMissionAction(mission, 'delete')}
                          sx={{
                            color: COLORS.error,
                            ml: 1,
                            '&:hover': { backgroundColor: 'rgba(244, 67, 54, 0.1)' }
                          }}
                        >
                          <Trash2 size={18} />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardActions>
                </MissionCard>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Box sx={{ textAlign: 'center', py: 6 }}>
            <Briefcase size={64} color={COLORS.neutral} style={{ opacity: 0.5, marginBottom: 16 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Aucune mission trouvée
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Cet utilisateur n'a créé aucune mission
            </Typography>
          </Box>
        )}
      </StyledPaper>

      {/* Dialog d'action */}
      <Dialog 
        open={actionDialogOpen} 
        onClose={() => setActionDialogOpen(false)} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: `1px solid ${COLORS.borderColor}`,
          padding: theme.spacing(2, 3),
          fontWeight: 600
        }}>
          Action sur la mission
        </DialogTitle>
        <DialogContent sx={{ padding: theme.spacing(3) }}>
          {selectedMission && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                Mission : {selectedMission.title}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                <Chip
                  label={`Action : ${action === 'moderate' ? 'Modérer' : 'Supprimer'}`}
                  color="primary"
                  size="small"
                  sx={{ borderRadius: '8px' }}
                />
                <Chip
                  label={`Statut actuel : ${getStatusLabel(selectedMission.status)}`}
                  color={getStatusColor(selectedMission.status) as any}
                  size="small"
                  sx={{ borderRadius: '8px' }}
                />
              </Box>
            </Box>
          )}

          {action === 'moderate' && (
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Nouveau statut</InputLabel>
              <Select
                value={newStatus}
                label="Nouveau statut"
                onChange={(e) => setNewStatus(e.target.value as any)}
                sx={{
                  borderRadius: '8px',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: COLORS.primary,
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: COLORS.primary,
                  },
                }}
              >
                <MenuItem value="draft">En attente</MenuItem>
                <MenuItem value="in_progress">En cours</MenuItem>
                <MenuItem value="completed">Terminée</MenuItem>
                <MenuItem value="cancelled">Annulée</MenuItem>
              </Select>
            </FormControl>
          )}

          {action === 'delete' && (
            <Alert severity="warning" sx={{ mb: 3, borderRadius: '8px' }}>
              <Typography variant="body2">
                <strong>Attention :</strong> Cette action est irréversible. La mission sera définitivement supprimée et toutes les candidatures en attente seront automatiquement refusées.
              </Typography>
            </Alert>
          )}
          
          <TextField
            fullWidth
            label={action === 'delete' ? 'Motif de suppression (optionnel)' : 'Motif de modération (optionnel)'}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            placeholder={action === 'delete' ? 'Expliquez pourquoi cette mission doit être supprimée...' : 'Expliquez la raison de cette modération...'}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                '&:hover fieldset': {
                  borderColor: COLORS.primary,
                },
                '&.Mui-focused fieldset': {
                  borderColor: COLORS.primary,
                },
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ 
          padding: theme.spacing(2, 3),
          borderTop: `1px solid ${COLORS.borderColor}`,
        }}>
          <Button 
            onClick={() => {
              setActionDialogOpen(false);
              setSelectedMission(null);
              setAction('');
              setReason('');
              setNewStatus('in_progress');
              setError(null);
            }}
            sx={{ 
              textTransform: 'none',
              fontWeight: 500,
              color: COLORS.neutral
            }}
          >
            Annuler
          </Button>
          <StyledButton
            onClick={executeAction}
            variant="contained"
            disabled={loading}
            sx={{
              backgroundColor: action === 'delete' ? COLORS.error : COLORS.primary,
              '&:hover': {
                backgroundColor: action === 'delete' ? '#d32f2f' : COLORS.secondary,
              }
            }}
          >
            {loading ? 'En cours...' : (action === 'delete' ? 'Supprimer définitivement' : 'Confirmer la modération')}
          </StyledButton>
        </DialogActions>
      </Dialog>

      {/* Dialog des candidatures */}
      <Dialog
        open={candidaturesDialogOpen}
        onClose={() => setCandidaturesDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <DialogTitle sx={{
          borderBottom: `1px solid ${COLORS.borderColor}`,
          padding: theme.spacing(2, 3),
          fontWeight: 600
        }}>
          Candidatures pour la mission
        </DialogTitle>
        <DialogContent sx={{ padding: theme.spacing(3) }}>
          {selectedMission && (
            <Box>
              <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                Mission : {selectedMission.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 3 }}>
                Budget : {formatCurrency(selectedMission.budget)} • {selectedMission.location}
              </Typography>

              {selectedMission.candidatures && selectedMission.candidatures.length > 0 ? (
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
                    {selectedMission.candidatures.length} candidature(s) reçue(s)
                  </Typography>

                  {selectedMission.candidatures.map((candidature, index) => (
                    <Box key={candidature.id} sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: 2,
                      mb: 3,
                      p: 2,
                      bgcolor: 'rgba(255, 107, 44, 0.05)',
                      borderRadius: 2,
                      border: '1px solid rgba(255, 107, 44, 0.1)'
                    }}>
                      <Avatar sx={{
                        width: 48,
                        height: 48,
                        bgcolor: COLORS.primary,
                        color: 'white',
                        fontWeight: 600
                      }}>
                        {candidature.jobbeur?.prenom?.charAt(0) || candidature.jobbeur?.email?.charAt(0) || 'J'}
                      </Avatar>

                      <Box sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                              {candidature.jobbeur?.prenom} {candidature.jobbeur?.nom}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {candidature.jobbeur?.email}
                              {candidature.jobbeur?.ville && ` • ${candidature.jobbeur.ville}`}
                            </Typography>
                          </Box>
                          <Chip
                            label={candidature.statut}
                            size="small"
                            color={candidature.statut === 'acceptée' ? 'success' :
                                   candidature.statut === 'refusée' ? 'error' : 'default'}
                            sx={{ fontWeight: 500 }}
                          />
                        </Box>

                        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                          <Typography variant="body2">
                            <strong>Montant proposé :</strong> {formatCurrency(candidature.montant_propose)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Date :</strong> {formatDate(candidature.created_at)}
                          </Typography>
                        </Box>

                        {candidature.message && (
                          <Box sx={{
                            bgcolor: 'rgba(0, 0, 0, 0.02)',
                            p: 1.5,
                            borderRadius: 1,
                            border: '1px solid rgba(0, 0, 0, 0.1)'
                          }}>
                            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                              "{candidature.message}"
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <User size={64} color={COLORS.neutral} style={{ opacity: 0.5, marginBottom: 16 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Aucune candidature
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Cette mission n'a reçu aucune candidature pour le moment
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{
          padding: theme.spacing(2, 3),
          borderTop: `1px solid ${COLORS.borderColor}`,
        }}>
          <Button
            onClick={() => setCandidaturesDialogOpen(false)}
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              color: COLORS.neutral
            }}
          >
            Fermer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserMissionsManagement;
