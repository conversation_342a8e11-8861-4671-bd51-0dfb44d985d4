import { api } from './api';
import { fetchCsrfToken } from './csrf';
import {
  CardTemplate,
  CardTemplateType,
  CardTemplateData,
  TemplatesResponse,
  TemplateResponse,
  RandomTemplateResponse,
  MultipleTemplatesResponse,
  ErrorResponse
} from '../types/cardEditor';
import { notify } from '../components/Notification';

// Service pour l'éditeur de cartes de visite et flyers
const cardEditorService = {
  // Récupérer tous les templates de l'utilisateur
  getTemplates: async (): Promise<CardTemplate[]> => {
    try {
      const response = await api.get<TemplatesResponse>('/api/card-editor');
      return response.data.success ? response.data.data : [];
    } catch (error: any) {
      console.error('Erreur lors de la récupération des templates:', error);
      notify('Erreur lors de la récupération des templates', 'error');
      return [];
    }
  },

  // Récupérer un template par son ID
  getTemplateById: async (id: string): Promise<CardTemplate | null> => {
    try {
      const response = await api.get<TemplateResponse>(`/api/card-editor/${id}`);
      return response.data.success ? response.data.data : null;
    } catch (error: any) {
      console.error('Erreur lors de la récupération du template:', error);
      notify('Erreur lors de la récupération du template', 'error');
      return null;
    }
  },

  // Créer un nouveau template
  createTemplate: async (template: {
    name: string;
    type: CardTemplateType;
    template_data: CardTemplateData;
    is_public?: boolean;
  }): Promise<CardTemplate | null> => {
    try {
      await fetchCsrfToken();
      const response = await api.post<TemplateResponse | ErrorResponse>('/api/card-editor', template);

      if (response.data.success) {
        notify('Template créé avec succès', 'success');
        return (response.data as TemplateResponse).data;
      }
      return null;
    } catch (error: any) {
      console.error('Erreur lors de la création du template:', error);

      // Vérifier si l'erreur est due à une limite d'abonnement atteinte
      if (error?.response?.data?.limitReached) {
        const errorData = error.response.data;
        notify(`Limite atteinte : ${errorData.message}`, 'error', 15000);

        // Si l'utilisateur n'est pas premium, suggérer de passer à l'abonnement premium
        if (!errorData.isPremium) {
          // notify("Passez à l'abonnement premium pour créer plus de templates", 'info');
        }
      } else {
        notify(error?.response?.data?.message || 'Erreur lors de la création du template', 'error');
      }

      return null;
    }
  },

  // Mettre à jour un template existant
  updateTemplate: async (id: string, template: {
    name: string;
    type: CardTemplateType;
    template_data: CardTemplateData;
    is_public?: boolean;
  }): Promise<CardTemplate | null> => {
    try {
      await fetchCsrfToken();
      const response = await api.put<TemplateResponse>(`/api/card-editor/${id}`, template);

      if (response.data.success) {
        // notify('Template mis à jour avec succès', 'success');
        return response.data.data;
      }
      return null;
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du template:', error);
      notify(error?.response?.data?.message || 'Erreur lors de la mise à jour du template', 'error');
      return null;
    }
  },

  // Supprimer un template
  deleteTemplate: async (id: string): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const response = await api.delete(`/api/card-editor/${id}`);

      if (response.data.success) {
        // notify('Template supprimé avec succès', 'success');
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Erreur lors de la suppression du template:', error);
      notify(error?.response?.data?.message || 'Erreur lors de la suppression du template', 'error');
      return false;
    }
  },

  // Générer un template aléatoire avec l'IA
  generateRandomTemplate: async (type: CardTemplateType, userData?: any): Promise<{
    template: CardTemplate | null;
    requiresConsent?: boolean;
    creditsRequired?: number;
    currentCredits?: number;
    limitReached?: boolean;
    currentCount?: number;
    limit?: number;
  }> => {
    try {
      await fetchCsrfToken();
      const response = await api.post<RandomTemplateResponse | ErrorResponse>('/api/card-editor/random', {
        type,
        userData
      });

      if (response.data.success) {
        const data = response.data as RandomTemplateResponse;
        notify(`Template généré avec succès (${data.creditsUsed} crédits utilisés)`, 'success');
        return {
          template: data.data
        };
      } else {
        const error = response.data as ErrorResponse;
        if (error.requiresConsent) {
          return {
            template: null,
            requiresConsent: true
          };
        } else if (error.creditsRequired) {
          return {
            template: null,
            creditsRequired: error.creditsRequired,
            currentCredits: error.currentCredits
          };
        } else if (error.limitReached) {
          notify(error.message, 'error');
          return {
            template: null,
            limitReached: true,
            currentCount: error.currentCount,
            limit: error.limit
          };
        }
        notify(error.message, 'error');
        return { template: null };
      }
    } catch (error: any) {
      console.error('Erreur lors de la génération du template aléatoire:', error);

      // Vérifier si l'erreur est due à une limite d'abonnement atteinte
      if (error?.response?.data?.limitReached) {
        const errorData = error.response.data;
        notify(`Limite atteinte : ${errorData.message}`, 'error', 15000);

        // Si l'utilisateur n'est pas premium, suggérer de passer à l'abonnement premium
        if (!errorData.isPremium) {
          // notify("Passez à l'abonnement premium pour créer plus de templates", 'info');
        }

        return {
          template: null,
          limitReached: true,
          currentCount: errorData.currentCount,
          limit: errorData.limit
        };
      }

      notify(error?.response?.data?.message || 'Erreur lors de la génération du template aléatoire', 'error');
      return { template: null };
    }
  },

  // Exporter un template en PDF
  exportTemplatePDF: async (id: string): Promise<Blob | null> => {
    try {
      await fetchCsrfToken();
      const response = await api.post(`/api/card-editor/export/${id}`, {
        format: 'pdf'
      }, {
        responseType: 'blob'
      });

      notify('Template exporté avec succès', 'success');
      return response.data;
    } catch (error: any) {
      console.error('Erreur lors de l\'export du template:', error);
      notify(error?.response?.data?.message || 'Erreur lors de l\'export du template', 'error');
      return null;
    }
  },

  // Exporter un template en image côté client (utilise html2canvas)
  exportTemplateImage: async (stageRef: any, format: 'png' | 'jpg' = 'png'): Promise<string | null> => {
    try {
      if (!stageRef.current) {
        notify('Erreur: Aucun canvas trouvé', 'error');
        return null;
      }

      // Utiliser la méthode toDataURL de Konva Stage
      const dataURL = stageRef.current.toDataURL({
        pixelRatio: 2, // Pour une meilleure qualité
        mimeType: format === 'png' ? 'image/png' : 'image/jpeg',
        quality: 0.9 // Pour JPEG uniquement
      });

      notify('Image exportée avec succès', 'success');
      return dataURL;
    } catch (error) {
      console.error('Erreur lors de l\'export en image:', error);
      notify('Erreur lors de l\'export en image', 'error');
      return null;
    }
  },

  // Supprimer une image individuelle d'un template (card editor)
  deleteImage: async (templateId: string, imageUrl: string): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const response = await api.delete(`/api/card-editor/${templateId}/image`, {
        data: { imageUrl }
      });
      if (response.data.success) {
        notify('Image supprimée avec succès', 'success');
        return true;
      }
      notify(response.data.message || 'Erreur lors de la suppression de l\'image', 'error');
      return false;
    } catch (error: any) {
      console.error('Erreur lors de la suppression de l\'image:', error);
      notify(error?.response?.data?.message || 'Erreur lors de la suppression de l\'image', 'error');
      return false;
    }
  },

  // Supprimer toutes les images d'un template (card editor)
  deleteAllImages: async (templateId: string): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const response = await api.delete(`/api/card-editor/${templateId}/images`);
      if (response.data.success) {
        notify('Toutes les images ont été supprimées du bucket', 'success');
        return true;
      }
      notify(response.data.message || 'Erreur lors de la suppression des images', 'error');
      return false;
    } catch (error: any) {
      console.error('Erreur lors de la suppression des images:', error);
      notify('Erreur lors de la suppression des images', 'error');
      return false;
    }
  },

  // Réorganiser les templates
  reorderTemplates: async (items: {id: string, order_index: number}[]): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const response = await api.post('/api/card-editor/reorder', { items });
      if (response.data.success) {
        notify('Ordre des templates mis à jour', 'success');
        return true;
      }
      notify(response.data.message || 'Erreur lors de la réorganisation', 'error');
      return false;
    } catch (error: any) {
      console.error('Erreur lors de la réorganisation des templates:', error);
      notify(error?.response?.data?.message || 'Erreur lors de la réorganisation des templates', 'error');
      return false;
    }
  },

  // Note: L'activation/désactivation des templates est gérée automatiquement par le backend
  // en fonction du type d'abonnement de l'utilisateur

  // Générer plusieurs templates avec l'IA
  generateAiTemplates: async (type: CardTemplateType, userData?: any, count: number = 1): Promise<{
    templates: CardTemplateData[];
    requiresConsent?: boolean;
    creditsRequired?: number;
    currentCredits?: number;
    limitReached?: boolean;
    currentCount?: number;
    limit?: number;
  }> => {
    try {
      await fetchCsrfToken();
      const response = await api.post<MultipleTemplatesResponse | ErrorResponse>('/api/card-editor/generate-multiple', {
        type,
        userData,
        count: Math.min(count, 4) // Limiter à 4 templates maximum
      });

      if (response.data.success) {
        const data = response.data as MultipleTemplatesResponse;
        notify(`Templates générés avec succès (${data.creditsUsed} crédits utilisés)`, 'success');
        return {
          templates: data.templates
        };
      } else {
        const error = response.data as ErrorResponse;
        if (error.requiresConsent) {
          return {
            templates: [],
            requiresConsent: true
          };
        } else if (error.creditsRequired) {
          return {
            templates: [],
            creditsRequired: error.creditsRequired,
            currentCredits: error.currentCredits
          };
        } else if (error.limitReached) {
          notify(error.message, 'error');
          return {
            templates: [],
            limitReached: true,
            currentCount: error.currentCount,
            limit: error.limit
          };
        }
        notify(error.message, 'error');
        return { templates: [] };
      }
    } catch (error: any) {
      console.error('Erreur lors de la génération des templates:', error);

      // Vérifier si l'erreur est due à une limite d'abonnement atteinte
      if (error?.response?.data?.limitReached) {
        const errorData = error.response.data;
        notify(`Limite atteinte : ${errorData.message}`, 'error', 15000);

        // Si l'utilisateur n'est pas premium, suggérer de passer à l'abonnement premium
        if (!errorData.isPremium) {
          // notify("Passez à l'abonnement premium pour créer plus de templates", 'info');
        }

        return {
          templates: [],
          limitReached: true,
          currentCount: errorData.currentCount,
          limit: errorData.limit
        };
      }

      notify(error?.response?.data?.message || 'Erreur lors de la génération des templates', 'error');
      return { templates: [] };
    }
  },

  // Ajouter une fonction pour vérifier le statut de génération des templates
  getGenerationStatus: async (): Promise<{
    status: string;
    progress: number[];
    templates?: CardTemplateData[];
  }> => {
    try {
      await fetchCsrfToken();
      const response = await api.get('/api/card-editor/generation-status');
      
      if (response.data) {
        return response.data;
      }
      
      return {
        status: 'unknown',
        progress: []
      };
    } catch (error: any) {
      console.error('Erreur lors de la vérification du statut de génération:', error);
      return {
        status: 'error',
        progress: []
      };
    }
  }
};

export default cardEditorService;
