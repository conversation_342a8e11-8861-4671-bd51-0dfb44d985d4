import React, { useState, useMemo } from 'react';
import { Box, Button, styled } from '@mui/material';
import { motion } from 'framer-motion';
import { AlertCircle, Euro, Wallet, Coins, ArrowRightLeft, ListFilter, Tag, SortDesc, Clock } from 'lucide-react';
import CategoryFilterModal from './CategoryFilterModal';
import { SearchField } from './SearchField';
import {
  Favorite as FavoriteIcon
} from '@mui/icons-material';

const FilterBarContainer = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  marginBottom: '20px',
  padding: '12px 16px',
  backgroundColor: 'white',
  borderRadius: '16px',
  border: '1px solid #FFE4BA',
  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.08)',
  position: 'relative',
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
  },
  '@media (max-width: 768px)': {
    padding: '10px',
    gap: '6px',
  },
  '@media (max-width: 900px)': {
    margin: '10px'
  }
});

const FilterHeader = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  width: '100%',
  padding: '4px 0',
  gap: '8px',
  '@media (max-width: 768px)': {
    flexDirection: 'row',
    gap: '8px'
  }
});

const FilterTitle = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: '#FF6B2C',
  fontWeight: 'bold',
  fontSize: '0.9rem',
  whiteSpace: 'nowrap',
  '@media (max-width: 768px)': {
    fontSize: '0.85rem'
  }
});

interface FilterContentProps {
  isExpanded: boolean;
}

const FilterContent = styled(motion.div)<FilterContentProps>(({ isExpanded }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '16px',
  marginTop: isExpanded ? '15px' : '0',
  width: '100%',
  '@media (max-width: 768px)': {
    flexDirection: 'column',
  }
}));

const FilterSection = styled(Box)({
  display: 'flex',
  gap: '8px',
  alignItems: 'center',
  position: 'relative',
  '&:not(:last-child)': {
    paddingRight: '24px',
    marginRight: '24px',
    '&::after': {
      content: '""',
      position: 'absolute',
      right: 0,
      top: '50%',
      transform: 'translateY(-50%)',
      width: '2px',
      height: '24px',
      background: 'linear-gradient(180deg, #FFE4BA 0%, #FFD4A4 100%)',
      borderRadius: '2px',
    }
  },
  '@media (max-width: 768px)': {
    flexWrap: 'wrap',
    '&:not(:last-child)': {
      paddingRight: '0',
      marginRight: '0',
      paddingBottom: '12px',
      marginBottom: '12px',
      borderBottom: '1px solid #FFE4BA',
      '&::after': {
        display: 'none'
      }
    }
  }
});

const FilterLabel = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  fontSize: '0.875rem',
  color: '#666',
  marginRight: '12px',
  whiteSpace: 'nowrap',
  fontWeight: 500,
  '& svg': {
    strokeWidth: 2,
    size: 16,
    color: '#FF6B2C',
  },
  '@media (max-width: 768px)': {
    width: '100%',
    marginBottom: '8px'
  }
});

const FilterChip = styled(motion.div)(({ selected }: { selected: boolean }) => ({
  backgroundColor: selected ? '#FF6B2C' : 'white',
  color: selected ? 'white' : '#FF6B2C',
  border: `1px solid ${selected ? '#FF6B2C' : '#FFE4BA'}`,
  fontWeight: 'bold',
  borderRadius: '10px',
  padding: '8px 16px',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
  fontSize: '0.875rem',
  boxShadow: selected ? '0 2px 8px rgba(255, 107, 44, 0.2)' : 'none',
  '&:hover': {
    backgroundColor: selected ? '#FF965E' : '#FFF8F3',
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)',
  },
  '& svg': {
    strokeWidth: selected ? 2.5 : 2,
    size: 14,
  },
  '@media (max-width: 768px)': {
    flex: '1 1 auto',
    justifyContent: 'center',
    minWidth: 'calc(50% - 4px)',
    padding: '10px 12px'
  },
  '@media (max-width: 380px)': {
    minWidth: '100%'
  }
}));

const ToggleButton = styled(Button)(() => ({
  backgroundColor: '#FFF8F3',
  color: '#FF6B2C',
  border: '1px solid #FFE4BA',
  borderRadius: '10px',
  padding: '4px 16px',
  textTransform: 'none',
  fontWeight: 'bold',
  fontSize: '0.875rem',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  '&:hover': {
    backgroundColor: '#FFE4BA',
  },
  '& .filter-count': {
    backgroundColor: '#FF6B2C',
    color: 'white',
    borderRadius: '12px',
    padding: '2px 8px',
    fontSize: '0.75rem',
    marginLeft: '4px',
  },
  '@media (max-width: 768px)': {
    padding: '6px 12px',
  }
}));

interface FilterBarProps {
  statusFilters: string[];
  budgetFilters: string[];
  paymentFilters: string[];
  categoryFilters?: string[];
  subcategoryFilters?: string[];
  toggleFilter: (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees') => void;
  isFilterActive: (filter: string, category: 'status' | 'budget' | 'payment' | 'category' | 'subcategory' | 'liked' | 'profile' | 'offer_status' | 'sort' | 'offres_envoyees') => boolean;
  showStatusFilters?: boolean;
  showRejectedToggle?: boolean;
  showRejected?: boolean;
  showLikedFilter?: boolean;
  showProfileFilters?: boolean;
  showOfferStatusFilters?: boolean;
  showSortOptions?: boolean;
  onToggleRejected?: () => void;
  onCategoryFilterChange?: (categories: string[], subcategories: string[]) => void;
  profileFilters?: string[];
  offerStatusFilters?: string[];
  sortBy?: string;
  isExpanded?: boolean;
  onToggleExpand?: (expanded: boolean) => void;
  searchTerm?: string;
  onSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  showSearchField?: boolean;
  offresEnvoyeesFilter?: string[];
  showOffresEnvoyeesFilter?: boolean;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  categoryFilters = [],
  subcategoryFilters = [],
  toggleFilter,
  isFilterActive,
  showStatusFilters = false,
  showRejectedToggle = false,
  showRejected = false,
  showLikedFilter = true,
  showOfferStatusFilters = false,
  showSortOptions = false,
  onToggleRejected,
  onCategoryFilterChange,
  statusFilters,
  budgetFilters,
  paymentFilters,
  profileFilters = [],
  offerStatusFilters = [],
  sortBy = '',
  isExpanded: externalIsExpanded,
  onToggleExpand,
  searchTerm = '',
  onSearch,
  showSearchField = true,
  showOffresEnvoyeesFilter = false,
}) => {
  const [internalIsExpanded, setInternalIsExpanded] = useState(false);
  
  const isExpandedControlled = externalIsExpanded !== undefined;
  const isExpandedValue = isExpandedControlled ? externalIsExpanded : internalIsExpanded;
  
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);

  const activeFiltersCount = useMemo(() => {
    return [
      ...statusFilters,
      ...budgetFilters,
      ...paymentFilters,
      ...categoryFilters,
      ...subcategoryFilters,
      ...profileFilters,
      ...offerStatusFilters,
      ...(showRejected ? ['rejected'] : []),
      ...(isFilterActive('liked', 'liked') ? ['liked'] : []),
      ...(sortBy ? [sortBy] : [])
    ].length;
  }, [
    statusFilters,
    budgetFilters,
    paymentFilters,
    categoryFilters,
    subcategoryFilters,
    profileFilters,
    offerStatusFilters,
    showRejected,
    isFilterActive,
    sortBy
  ]);

  const handleToggleExpand = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    
    if (isExpandedControlled && onToggleExpand) {
      onToggleExpand(!isExpandedValue);
    } else {
      setInternalIsExpanded(!internalIsExpanded);
    }
  };

  const handleOpenCategoryModal = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    setIsCategoryModalOpen(true);
  };

  const handleCloseCategoryModal = () => {
    setIsCategoryModalOpen(false);
  };

  const handleApplyCategoryFilter = (categories: string[], subcategories: string[]) => {
    if (onCategoryFilterChange) {
      onCategoryFilterChange(categories, subcategories);
    }
    handleCloseCategoryModal();
  };

  return (
    <FilterBarContainer>
      <FilterHeader>
        <FilterTitle>
          <ListFilter size={18} />
          Filtres
        </FilterTitle>
        
        {showSearchField && onSearch && (
          <Box 
            sx={{ 
              flex: 1, 
              mx: 2, 
              display: { xs: 'none', md: 'block' } 
            }}
          >
            <SearchField
              value={searchTerm}
              onChange={onSearch}
              placeholder="Rechercher une mission ..."
            />
          </Box>
        )}
        
        <ToggleButton
          onClick={handleToggleExpand}
          disableRipple
        >
          <span className="filter-text">
            {isExpandedValue ? 'Masquer les filtres' : 'Afficher les filtres'}
          </span>
          <span className="filter-text-mobile" style={{ display: 'none' }}>
            {isExpandedValue ? 'Masquer' : 'Filtres'}
          </span>
          {activeFiltersCount > 0 && (
            <span className="filter-count">{activeFiltersCount}</span>
          )}
        </ToggleButton>
      </FilterHeader>

      {showSearchField && onSearch && (
        <Box sx={{ 
          mb: isExpandedValue ? 1.5 : 0,
          display: { xs: 'flex', md: 'none' },
          alignItems: 'center',
          width: '100%',
          maxWidth: '100%'
        }}>
          <SearchField
            value={searchTerm}
            onChange={onSearch}
            placeholder="Rechercher une mission ..."
          />
        </Box>
      )}

      <FilterContent
        initial={false}
        animate={{ 
          height: isExpandedValue ? 'auto' : 0,
          opacity: isExpandedValue ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        style={{ overflow: 'hidden' }}
        isExpanded={isExpandedValue}
      >
        <FilterSection>
          <FilterLabel>
            <Tag size={16} />
            Catégories
          </FilterLabel>
          <FilterChip
            selected={categoryFilters.length > 0 || subcategoryFilters.length > 0}
            onClick={handleOpenCategoryModal}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {categoryFilters.length > 0 || subcategoryFilters.length > 0
              ? `${categoryFilters.length + subcategoryFilters.length} catégorie${
                  categoryFilters.length + subcategoryFilters.length > 1 ? 's' : ''
                }`
              : 'Filtrer par catégorie'}
          </FilterChip>
        </FilterSection>

        {showStatusFilters && (
          <FilterSection>
            <FilterLabel>
              <ListFilter size={16} />
              Statut
            </FilterLabel>

            <FilterChip
              selected={isFilterActive('en_cours', 'status')}
              onClick={() => toggleFilter('en_cours', 'status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              En cours
            </FilterChip>
            <FilterChip
              selected={isFilterActive('terminee', 'status')}
              onClick={() => toggleFilter('terminee', 'status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Terminée
            </FilterChip>
            <FilterChip
              selected={isFilterActive('annulee', 'status')}
              onClick={() => toggleFilter('annulee', 'status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Annulée
            </FilterChip>
            <FilterChip
              selected={isFilterActive('en_moderation', 'status')}
              onClick={() => toggleFilter('en_moderation', 'status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              En modération
            </FilterChip>
          </FilterSection>
        )}

        {showOfferStatusFilters && (
          <FilterSection>
            <FilterLabel>
              <Clock size={16} />
              Statut offre
            </FilterLabel>
            <FilterChip
              selected={isFilterActive('acceptée', 'offer_status')}
              onClick={() => toggleFilter('acceptée', 'offer_status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Acceptée
            </FilterChip>
            <FilterChip
              selected={isFilterActive('refusée', 'offer_status')}
              onClick={() => toggleFilter('refusée', 'offer_status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Refusée
            </FilterChip>
            <FilterChip
              selected={isFilterActive('en_attente', 'offer_status')}
              onClick={() => toggleFilter('en_attente', 'offer_status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              En attente
            </FilterChip>
            <FilterChip
              selected={isFilterActive('contre_offre', 'offer_status')}
              onClick={() => toggleFilter('contre_offre', 'offer_status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Contre-offre envoyée
            </FilterChip>
            <FilterChip
              selected={isFilterActive('contre_offre_jobbeur', 'offer_status')}
              onClick={() => toggleFilter('contre_offre_jobbeur', 'offer_status')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Contre-offre reçue
            </FilterChip>
          </FilterSection>
        )}

        <FilterSection>
          <FilterLabel>
            <AlertCircle size={16} />
            Priorité
          </FilterLabel>
          <FilterChip
            selected={isFilterActive('urgent', 'status')}
            onClick={() => toggleFilter('urgent', 'status')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <AlertCircle size={14} />
            Urgentes
          </FilterChip>
        </FilterSection>

        {showLikedFilter && (
          <FilterSection>
            <FilterLabel>
              <FavoriteIcon sx={{ fontSize: 16 }} />
              Favoris
            </FilterLabel>
            <FilterChip
              selected={isFilterActive('liked', 'liked')}
              onClick={() => toggleFilter('liked', 'liked')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Missions aimées
            </FilterChip>
          </FilterSection>
        )}

        {showOffresEnvoyeesFilter && (
          <FilterSection>
            <FilterLabel>
              <ListFilter size={16} />
              Offres
            </FilterLabel>
            <FilterChip
              selected={isFilterActive('offres_envoyees', 'offres_envoyees')}
              onClick={() => toggleFilter('offres_envoyees', 'offres_envoyees')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Offres envoyées
            </FilterChip>
          </FilterSection>
        )}

        {showRejectedToggle && (
          <FilterSection>
            <FilterLabel>
              <ListFilter size={16} />
              Statut
            </FilterLabel>
            <FilterChip
              selected={showRejected}
              onClick={onToggleRejected}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {showRejected ? 'Masquer les missions rejetées' : 'Afficher les missions rejetées'}
            </FilterChip>
          </FilterSection>
        )}

        <FilterSection>
          <FilterLabel>
            <Euro size={16} />
            Budget
          </FilterLabel>
          <FilterChip
            selected={isFilterActive('budget_defini', 'budget')}
            onClick={() => toggleFilter('budget_defini', 'budget')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Coins size={14} />
            Défini
          </FilterChip>
          <FilterChip
            selected={isFilterActive('budget_non_defini', 'budget')}
            onClick={() => toggleFilter('budget_non_defini', 'budget')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            À définir
          </FilterChip>
        </FilterSection>

        <FilterSection>
          <FilterLabel>
            <Wallet size={16} />
            Paiement
          </FilterLabel>
          <FilterChip
            selected={isFilterActive('jobi_only', 'payment')}
            onClick={() => toggleFilter('jobi_only', 'payment')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Coins size={14} />
            Jobi
          </FilterChip>
          <FilterChip
            selected={isFilterActive('direct_only', 'payment')}
            onClick={() => toggleFilter('direct_only', 'payment')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Euro size={14} />
            Euro
          </FilterChip>
          <FilterChip
            selected={isFilterActive('both', 'payment')}
            onClick={() => toggleFilter('both', 'payment')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ArrowRightLeft size={14} />
            Hybride
          </FilterChip>
        </FilterSection>

        {showSortOptions && (
          <FilterSection>
            <FilterLabel>
              <SortDesc size={16} />
              Trier par
            </FilterLabel>
            <FilterChip
              selected={isFilterActive('montant_propose', 'sort')}
              onClick={() => toggleFilter('montant_propose', 'sort')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Montant proposé
            </FilterChip>
            <FilterChip
              selected={isFilterActive('date_contre_offre', 'sort')}
              onClick={() => toggleFilter('date_contre_offre', 'sort')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Date contre-offre
            </FilterChip>
            <FilterChip
              selected={isFilterActive('date_creation', 'sort')}
              onClick={() => toggleFilter('date_creation', 'sort')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Date de création
            </FilterChip>
          </FilterSection>
        )}
      </FilterContent>

      <CategoryFilterModal
        isOpen={isCategoryModalOpen}
        onClose={handleCloseCategoryModal}
        selectedCategories={categoryFilters}
        selectedSubcategories={subcategoryFilters}
        onApplyFilter={handleApplyCategoryFilter}
      />
    </FilterBarContainer>
  );
};

export default FilterBar; 