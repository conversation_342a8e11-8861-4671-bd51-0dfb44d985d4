import axios from 'axios';
import logger from '../../../utils/logger';
import { API_CONFIG } from '../../../config/api';
import { getCommonHeaders } from '../../../utils/headers';
import { ProfilData } from './profileUtils';

// Ajout d'une photo dans une galerie
export const handleAddPhotoToGallery = async (
  galleryId: string, 
  photo: any,
  setProfil: React.Dispatch<React.SetStateAction<ProfilData | null>>,
  profilComplet: boolean,
  calculateProfilCompletion: (profil: ProfilData, wasProfilComplete: boolean) => Promise<{ percentage: number; fields: any[] }>,
  profilCompletionCache: React.MutableRefObject<{
    lastProfilData: string | null;
    result: { percentage: number; fields: any[] } | null;
  }>
) => {
  profilCompletionCache.current = { lastProfilData: null, result: null };
  try {
    // Recharger les galeries à jour depuis l'API
    const headers = await getCommonHeaders();
    const response = await axios.get(`${API_CONFIG.baseURL}/api/users/galleries`, { headers, withCredentials: true });
    const galleries = response.data.galleries || [];
    setProfil(prev => {
      if (!prev) return null;
      const updated = {
        ...prev,
        galleryFolders: galleries
      };
      logger.info('[DEBUG] galleryFolders après ajout photo (API):', updated.galleryFolders);
      calculateProfilCompletion(updated, profilComplet).then(completion => {
        // Cette fonction est appelée après le calcul de la complétion
      });
      return updated;
    });
  } catch (error) {
    logger.info('Erreur lors du rechargement des galeries après ajout photo:', error);
  }
};

// Suppression d'une photo dans une galerie
export const handleRemovePhotoFromGallery = async (
  galleryId: string, 
  photoId: string,
  setProfil: React.Dispatch<React.SetStateAction<ProfilData | null>>,
  profilComplet: boolean,
  calculateProfilCompletion: (profil: ProfilData, wasProfilComplete: boolean) => Promise<{ percentage: number; fields: any[] }>,
  profilCompletionCache: React.MutableRefObject<{
    lastProfilData: string | null;
    result: { percentage: number; fields: any[] } | null;
  }>
) => {
  profilCompletionCache.current = { lastProfilData: null, result: null };
  try {
    // Recharger les galeries à jour depuis l'API
    const headers = await getCommonHeaders();
    const response = await axios.get(`${API_CONFIG.baseURL}/api/users/galleries`, { headers, withCredentials: true });
    const galleries = response.data.galleries || [];
    setProfil(prev => {
      if (!prev) return null;
      const updated = {
        ...prev,
        galleryFolders: galleries
      };
      logger.info('[DEBUG] galleryFolders après suppression photo (API):', updated.galleryFolders);
      calculateProfilCompletion(updated, profilComplet).then(completion => {
        // Cette fonction est appelée après le calcul de la complétion
      });
      return updated;
    });
  } catch (error) {
    logger.info('Erreur lors du rechargement des galeries après suppression photo:', error);
  }
};

// Suppression d'une galerie
export const handleDeleteGallery = (
  galleryId: string,
  setProfil: React.Dispatch<React.SetStateAction<ProfilData | null>>,
  profilComplet: boolean,
  calculateProfilCompletion: (profil: ProfilData, wasProfilComplete: boolean) => Promise<{ percentage: number; fields: any[] }>,
  profilCompletionCache: React.MutableRefObject<{
    lastProfilData: string | null;
    result: { percentage: number; fields: any[] } | null;
  }>
) => {
  // Vider le cache de complétion du profil
  profilCompletionCache.current = { lastProfilData: null, result: null };
  setProfil(prev => {
    if (!prev) return null;
    const updated = {
      ...prev,
      galleryFolders: (prev.galleryFolders || []).filter(gallery => gallery.id !== galleryId)
    };
    calculateProfilCompletion(updated, profilComplet).then(completion => {
      // Cette fonction est appelée après le calcul de la complétion
    });
    return updated;
  });
};
