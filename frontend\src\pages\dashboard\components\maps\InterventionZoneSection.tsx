import React, { useState, useEffect } from 'react';
import InterventionZoneMap from './InterventionZoneMap';
import AddressSearch from './AddressSearch';
import { notify } from '../../../../components/Notification';
import { useAuth } from '../../../../contexts/AuthContext';
import type { LatLngTuple } from 'leaflet';
import axios from 'axios';
import { API_CONFIG } from '../../../../config/api';
import { getCommonHeaders } from '../../../../utils/headers';
import { fetchCsrfToken } from '../../../../services/csrf';
import logger from '@/utils/logger';

interface InterventionZoneSectionProps {
  avatarUrl?: string;
  onSave?: (data: { center: [number, number]; radius: number }) => void;
  initialCenter?: [number, number];
  initialRadius?: number;
  profil?: any;
  maxRadius: number;
  isOwnProfil?: boolean;
  onProfilUpdate?: (updatedData: any) => void;
  franceEntiereIncluse?: boolean;
}

const InterventionZoneSection: React.FC<InterventionZoneSectionProps> = ({
  avatarUrl,
  onSave,
  initialCenter = [48.8566, 2.3522], // Paris par défaut
  initialRadius = 10,
  profil,
  maxRadius,
  isOwnProfil = false,
  onProfilUpdate,
  franceEntiereIncluse = false
}) => {
  const [center, setCenter] = useState<LatLngTuple>(initialCenter);
  const [radius, setRadius] = useState<number>(initialRadius);
  const [nearestCity, setNearestCity] = useState<string>('');
  const { user } = useAuth();
  const [searchAddress, setSearchAddress] = useState<string>('');
  const [isSaveCooldown, setIsSaveCooldown] = useState(false);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const isPremium = profil?.isPremium;

  const handleFranceEntiereToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    if (!isPremium) {
      notify("L'option France entière n'est pas disponible avec votre abonnement actuel. Veuillez passer à un abonnement supérieur pour accéder à cette fonctionnalité.", 'warning');
      return;
    }
    // Mettre à jour le profil parent avec la nouvelle valeur de france_entiere
    if (onProfilUpdate && profil) {
      onProfilUpdate({
        ...profil,
        intervention_zone: {
          ...profil.intervention_zone,
          france_entiere: newValue
        }
      });
    }
  };

  // Fonction pour obtenir la ville la plus proche
  const getNearestCity = async (lat: number, lon: number) => {
    try {
      const response = await axios.get(
        `https://api-adresse.data.gouv.fr/reverse/?lon=${lon}&lat=${lat}`,
        { timeout: 5000 }
      );

      if (response.data && response.data.features && response.data.features.length > 0) {
        const feature = response.data.features[0];
        const properties = feature.properties;

        // On essaie d'obtenir la ville la plus importante
        const city = properties.city || properties.name;

        if (city) {
          setNearestCity(city);
        }
      }
    } catch (error) {
      logger.error('Erreur lors de la recherche de la ville la plus proche:', error);
    }
  };

  // Mettre à jour la ville la plus proche quand le centre change
  useEffect(() => {
    if (!isOwnProfil) {
      getNearestCity(center[0], center[1]);
    }
  }, [center, isOwnProfil]);

  const handleRadiusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value)) {
      if (value > maxRadius) {
        notify(`Le rayon d'intervention est limité à ${maxRadius}km avec votre abonnement actuel. Passez à un abonnement supérieur dans le menu "Premium" pour augmenter cette limite.`, 'warning');
        setRadius(maxRadius);
      } else if (value >= 0) {
        setRadius(value);
      }
    }
  };

  const handleSearch = async () => {
    if (!searchAddress.trim()) {
      notify('Veuillez entrer une adresse', 'warning');
      return;
    }

    setIsSearching(true);
    try {
      const response = await axios.get(
        `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(searchAddress)}&limit=1`,
        { timeout: 5000 }
      );

      if (response.data && response.data.features && response.data.features.length > 0) {
        const feature = response.data.features[0];
        const coordinates = feature.geometry.coordinates;
        const newCenter: [number, number] = [coordinates[1], coordinates[0]]; // Inverser lat/lng
        setCenter(newCenter);
        setSearchAddress(feature.properties.label || feature.properties.name);
      } else {
        notify('Aucun résultat trouvé pour cette adresse', 'warning');
      }
    } catch (error) {
      logger.error('Erreur lors de la recherche d\'adresse:', error);
      notify('Erreur lors de la recherche d\'adresse', 'error');
    } finally {
      setIsSearching(false);
    }
  };

  const reverseGeocode = async (lat: number, lon: number) => {
    setIsSearching(true);
    try {
      const response = await axios.get(
        `https://api-adresse.data.gouv.fr/reverse/?lon=${lon}&lat=${lat}`,
        { timeout: 5000 }
      );
      logger.info('Réponse de la recherche inverse d\'adresse:', response.data);

      if (response.data && response.data.features && response.data.features.length > 0) {
        const feature = response.data.features[0];
        setSearchAddress(feature.properties.label || feature.properties.name);
      }
    } catch (error) {
      logger.error('Erreur lors de la géocodification inverse:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSave = async () => {
    if (isSaveCooldown) {
      return;
    }

    if (!user?.id) {
      notify('Vous devez être connecté pour sauvegarder votre zone d\'intervention', 'error');
      return;
    }

    setIsSaveCooldown(true);

    try {
      await fetchCsrfToken();
      let headers = await getCommonHeaders();
      headers = { ...headers, 'Content-Type': 'application/json' };
      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/users/updateProfil`,
        {
          intervention_zone: {
            center: profil?.intervention_zone?.france_entiere ? [46.603354, 1.888334] : [center[0], center[1]], // Centre de la France si France entière
            radius: profil?.intervention_zone?.france_entiere ? 1000 : radius, // Grand rayon pour couvrir la France
            adresse: profil?.intervention_zone?.france_entiere ? 'France entière' : searchAddress,
            france_entiere: profil?.intervention_zone?.france_entiere
          }
        },
        {
          headers,
          withCredentials: true
        }
      );

      if (response.data.success) {
        notify('Zone d\'intervention sauvegardée avec succès', 'success');
        onSave?.({ center: [center[0], center[1]], radius });

        // Mettre à jour le profil parent avec la nouvelle zone d'intervention
        if (onProfilUpdate && profil) {
          onProfilUpdate({
            ...profil,
            intervention_zone: {
              ...profil.intervention_zone,
              center: profil?.intervention_zone?.france_entiere ? [46.603354, 1.888334] : [center[0], center[1]],
              radius: profil?.intervention_zone?.france_entiere ? 1000 : radius,
              adresse: profil?.intervention_zone?.france_entiere ? 'France entière' : searchAddress,
              france_entiere: profil?.intervention_zone?.france_entiere
            }
          });
        }
      } else {
        notify('Erreur lors de la sauvegarde de la zone d\'intervention', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde de la zone d\'intervention:', error);
      notify('Erreur lors de la sauvegarde de la zone d\'intervention', 'error');
    } finally {
      setTimeout(() => {
        setIsSaveCooldown(false);
      }, 3000);
    }
  };

  const handleCenterChange = (newCenter: LatLngTuple) => {
    setCenter(newCenter);
    reverseGeocode(newCenter[0], newCenter[1]);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
      <h2 className="text-2xl font-semibold mb-6">Zone d'intervention</h2>

      {isOwnProfil && (
        <>
          <div className="mb-4">
            <div className={`flex flex-col gap-2 p-3 rounded-lg border ${
              !isPremium
                ? 'bg-[#FFF8F3] border-[#FFE4BA]'
                : 'border-gray-200'
            }`}>
              <div className="flex items-center gap-2">
                <label className="relative inline-flex items-center">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={!!profil?.intervention_zone?.france_entiere}
                    onChange={handleFranceEntiereToggle}
                    disabled={!franceEntiereIncluse}
                  />
                  <div
                    className={`w-11 h-6 rounded-full peer transition-all duration-200
                      ${!franceEntiereIncluse
                        ? 'bg-gray-200 border-2 border-gray-200 cursor-not-allowed after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-400 after:rounded-full after:h-5 after:w-5 after:transition-all'
                        : 'bg-gray-200 peer-checked:bg-[#FF6B2C] after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-full peer-checked:after:border-white'
                      }`}
                  />
                  <span className={`ml-2 text-sm font-medium ${
                    !franceEntiereIncluse
                      ? 'text-gray-500'
                      : 'text-gray-900'
                  }`}>
                    France entière
                  </span>
                </label>
              </div>
              {!franceEntiereIncluse && (
                <p className="text-xs text-[#FF6B2C]">
                  Cette option n'est pas incluse dans votre abonnement actuel. Passez à l'abonnement Premium avec l'option France entière pour l'activer.
                </p>
              )}
            </div>
          </div>

          {!profil?.intervention_zone?.france_entiere && (
            <>
              <AddressSearch
                onAddressSelect={setCenter}
                searchAddress={searchAddress}
                onSearchAddressChange={setSearchAddress}
                onSearch={handleSearch}
              />

              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rayon d'intervention (km)
                </label>
                <style>
                  {`
                    /* Masquer les flèches pour Firefox */
                    input[type="number"] {
                      -moz-appearance: textfield;
                    }
                    /* Masquer les flèches pour Chrome, Safari, Edge, Opera */
                    input::-webkit-outer-spin-button,
                    input::-webkit-inner-spin-button {
                      -webkit-appearance: none;
                      margin: 0;
                    }
                  `}
                </style>
                <div className="flex items-stretch shadow-sm rounded-md overflow-hidden">
                  <button
                    type="button"
                    onClick={() => {
                      const newValue = Math.max(2, radius - 1);
                      setRadius(newValue);
                    }}
                    className="w-10 flex items-center justify-center bg-gradient-to-r from-[#FF8F5C] to-[#FF6B2C] hover:from-[#FF6B2C] hover:to-[#FF5A15] text-white focus:outline-none focus:ring-1 focus:ring-[#FF6B2C] transition-all duration-200"
                    aria-label="Diminuer le rayon"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                  <div className="relative flex-grow">
                    <div className="flex items-center justify-center h-full">
                      <input
                        type="number"
                        min="2"
                        value={radius}
                        onChange={handleRadiusChange}
                        className="w-12 h-10 px-0 py-1 border-0 focus:ring-1 focus:ring-[#FF6B2C] focus:border-transparent text-right font-medium text-gray-700"
                      />
                      <span className="font-medium text-gray-700">km</span>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      const newValue = radius + 1;
                      if (newValue > maxRadius) {
                        notify(`Le rayon d'intervention est limité à ${maxRadius}km avec votre abonnement actuel. Passez à un abonnement supérieur dans le menu "Premium" pour augmenter cette limite.`, 'warning');
                        setRadius(maxRadius);
                      } else {
                        setRadius(newValue);
                      }
                    }}
                    className="w-10 flex items-center justify-center bg-gradient-to-r from-[#FF8F5C] to-[#FF6B2C] hover:from-[#FF6B2C] hover:to-[#FF5A15] text-white focus:outline-none focus:ring-1 focus:ring-[#FF6B2C] transition-all duration-200"
                    aria-label="Augmenter le rayon"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                </div>
                <div className="mt-0.5 flex flex-col sm:flex-row sm:justify-between px-1 text-xs text-gray-500">
                  <span>Minimum : 2 km</span>
                  <span className="mt-0.5 sm:mt-0">Maximum : jusqu'à 1000 km et + avec un abonnement premium</span>
                </div>
              </div>
            </>
          )}
        </>
      )}

      {!profil?.intervention_zone?.france_entiere ? (
        <>
          <InterventionZoneMap
            center={center}
            radius={radius}
            avatarUrl={avatarUrl}
            onCenterChange={isOwnProfil ? handleCenterChange : undefined}
            isOwnProfil={isOwnProfil}
            isSearching={isSearching}
          />
          {isOwnProfil && radius < 10 && (
            <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg text-sm text-amber-700">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-medium">Zone d'intervention limitée</p>
                  <p className="mt-1">Un rayon inférieur à 10 km peut réduire significativement vos opportunités de missions. Nous vous recommandons d'élargir votre zone pour augmenter vos chances de trouver des clients.</p>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="relative h-[40vh] rounded-lg overflow-hidden bg-gray-50">
          <div className="absolute inset-0 w-full h-full">
            <picture className="w-full h-full">
              <source type="image/webp" srcSet="/images/carte_de_france.webp" />
              <source type="image/jpeg" srcSet="/images/carte_de_france.jpg" />
              <img
                src="/images/carte_de_france.jpg"
                alt="Carte de France"
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </picture>
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-white/80 backdrop-blur-sm">
              <div className="flex items-center justify-center gap-2 text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <p className="text-lg font-medium">Zone d'intervention : France entière</p>
              </div>
              <p className="mt-2 text-sm text-gray-500 text-center">
                {isOwnProfil
                  ? "Vous intervenez sur l'ensemble du territoire français"
                  : `${profil?.firstName} propose des interventions sur tout le territoire français`}
              </p>
            </div>
          </div>
        </div>
      )}

      {isOwnProfil ? (
        <>
          <p className="mt-4 text-sm text-gray-600">
            {profil?.intervention_zone?.france_entiere
              ? "Vous avez choisi d'intervenir sur l'ensemble du territoire français. Vous recevrez des propositions pour toutes les demandes, quelle que soit leur localisation en France."
              : "Cette zone d'intervention vous permet de définir un périmètre dans lequel vous pouvez offrir vos services. Si d'autres utilisateurs postent une demande dans une zone qui se chevauche avec la vôtre, vous recevrez une proposition pour cette demande."}
          </p>
          <div className="mt-4 flex justify-end">
            <button
              onClick={handleSave}
              disabled={isSaveCooldown}
              className={`px-6 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] disabled:opacity-50 ${
                isSaveCooldown ? 'cursor-not-allowed' : ''
              }`}
            >
              {isSaveCooldown ? 'Patientez...' : 'Enregistrer la zone'}
            </button>
          </div>
        </>
      ) : (
        <p className="mt-4 text-gray-600">
          {profil?.intervention_zone?.france_entiere
            ? `${profil?.firstName} intervient sur l'ensemble du territoire français, vous assurant une couverture complète et une grande réactivité.`
            : `${profil?.firstName} intervient dans un rayon de ${radius} km autour de ${nearestCity || 'sa localisation'}.`}
        </p>
      )}
    </div>
  );
};

export default InterventionZoneSection;
