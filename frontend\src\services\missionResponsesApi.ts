import axios from 'axios';
import { API_CONFIG } from '../config/api';
import logger from '../utils/logger';
import { getCommonHeaders } from '../utils/headers';
import { notify } from '../components/Notification';
import { fetchCsrfToken } from '../services/csrf';

export interface MissionResponse {
  id: string;
  user_id: string;
  title: string;
  content: string;
  order_index: number;
  created_at: string;
  updated_at: string;
}

export interface PreMessageInfo {
  profil: {
    nom: string;
    prenom: string;
    bio: string;
    type_de_profil: string;
    nom_entreprise: string;
    statut_entreprise: string;
  };
  user_type: string;
  services: Array<{
    id: string;
    titre: string;
    category_id: string;
    subcategory_id: string;
  }>;
  verifications: {
    profil_verifier: boolean;
    identite_verifier: boolean;
    entreprise_verifier: boolean;
    assurance_verifier: boolean;
  };
}

export const missionResponsesApi = {
  // Récupérer toutes les réponses prédéfinies de l'utilisateur
  getUserResponses: async (): Promise<MissionResponse[]> => {
    try {
      const headers = await getCommonHeaders();
      logger.info('Récupération des réponses prédéfinies de l\'utilisateur');

      const response = await axios.get(`${API_CONFIG.baseURL}/api/mission-responses`, {
        headers,
        withCredentials: true
      });

      logger.info('Réponses prédéfinies récupérées avec succès', response.data);
      return response.data.data || [];
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des réponses prédéfinies:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la récupération des réponses prédéfinies',
        'error'
      );
      return [];
    }
  },

  // Créer une nouvelle réponse prédéfinie
  createResponse: async (title: string, content: string): Promise<MissionResponse | null> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      logger.info('Création d\'une nouvelle réponse prédéfinie', { title, content });

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/mission-responses`,
        { title, content },
        {
          headers,
          withCredentials: true
        }
      );

      logger.info('Réponse prédéfinie créée avec succès', response.data);
      notify(response.data.message || 'Réponse prédéfinie créée avec succès', 'success');
      return response.data.data || null;
    } catch (error: any) {
      logger.error('Erreur lors de la création de la réponse prédéfinie:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la création de la réponse prédéfinie',
        'error'
      );
      return null;
    }
  },

  // Mettre à jour une réponse prédéfinie
  updateResponse: async (id: string, title: string, content: string): Promise<MissionResponse | null> => {
    try {
      const headers = await getCommonHeaders();
      logger.info('Mise à jour d\'une réponse prédéfinie', { id, title, content });

      const response = await axios.put(
        `${API_CONFIG.baseURL}/api/mission-responses/${id}`,
        { title, content },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );

      logger.info('Réponse prédéfinie mise à jour avec succès', response.data);
      notify(response.data.message || 'Réponse prédéfinie mise à jour avec succès', 'success');
      return response.data.data || null;
    } catch (error: any) {
      logger.error('Erreur lors de la mise à jour de la réponse prédéfinie:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la mise à jour de la réponse prédéfinie',
        'error'
      );
      return null;
    }
  },

  // Supprimer une réponse prédéfinie
  deleteResponse: async (id: string): Promise<boolean> => {
    try {
      const headers = await getCommonHeaders();
      logger.info('Suppression d\'une réponse prédéfinie', { id });

      const response = await axios.delete(`${API_CONFIG.baseURL}/api/mission-responses/${id}`, {
        headers,
        withCredentials: true
      });

      logger.info('Réponse prédéfinie supprimée avec succès', response.data);
      notify(response.data.message || 'Réponse prédéfinie supprimée avec succès', 'success');
      return true;
    } catch (error: any) {
      logger.error('Erreur lors de la suppression de la réponse prédéfinie:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la suppression de la réponse prédéfinie',
        'error'
      );
      return false;
    }
  },

  // Réorganiser les réponses prédéfinies
  reorderResponses: async (items: { id: string; order_index: number }[]): Promise<boolean> => {
    try {
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();
      logger.info('Réorganisation des réponses prédéfinies', { items });

      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/mission-responses/reorder`,
        { items },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json'
          },
          withCredentials: true
        }
      );

      logger.info('Réponses prédéfinies réorganisées avec succès', response.data);
      notify(response.data.message || 'Réponses prédéfinies réorganisées avec succès', 'success');
      return true;
    } catch (error: any) {
      logger.error('Erreur lors de la réorganisation des réponses prédéfinies:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la réorganisation des réponses prédéfinies',
        'error'
      );
      return false;
    }
  },

  // Récupérer les informations pour générer un pré-message quand on fait une proposition de mission (offre de mission)
  getPreMessageInfo: async (): Promise<PreMessageInfo | null> => {
    try {
      const headers = await getCommonHeaders();
      logger.info('Récupération des informations pour le pré-message');

      const response = await axios.get(`${API_CONFIG.baseURL}/api/users/pre-message-info`, {
        headers,
        withCredentials: true
      });

      logger.info('Informations pour le pré-message récupérées avec succès', response.data);
      return response.data;
    } catch (error: any) {
      logger.error('Erreur lors de la récupération des informations pour le pré-message:', {
        error,
        stack: error instanceof Error ? error.stack : undefined,
        response: error.response?.data
      });
      notify(
        error.response?.data?.message || 'Erreur lors de la récupération des informations pour le pré-message',
        'error'
      );
      return null;
    }
  }
}; 