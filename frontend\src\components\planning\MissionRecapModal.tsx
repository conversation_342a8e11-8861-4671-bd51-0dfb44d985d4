import React, { useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, IconButton, Box, Avatar, Divider } from '@mui/material';
import { X, Clock, CalendarDays, FileText, Coins, CheckCircle, Circle, AlertCircle, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import DOMPurify from 'dompurify';
import ModalPortal from '../../components/ModalPortal';
import { Mission } from '../../types/planning';
import UserProfileModal from '../../components/UserProfileModal';
import api from '../../services/api';
import { notify } from '../../components/Notification';
import { useNavigate } from 'react-router-dom';
import { logger } from '@/utils/logger';

interface MissionRecapModalProps {
  open: boolean;
  mission: Mission;
  onClose: () => void;
}

const MissionRecapModal: React.FC<MissionRecapModalProps> = ({
  open,
  mission,
  onClose
}) => {
  // État pour gérer l'ouverture de la modale de profil utilisateur
  const [isUserProfileModalOpen, setIsUserProfileModalOpen] = useState(false);
  const [selectedUserProfile, setSelectedUserProfile] = useState<any>(null);
  const navigate = useNavigate();

  // Sanitize les données de la mission à l'affichage
  const sanitizedMission = {
    ...mission,
    title: DOMPurify.sanitize(mission.title),
    description: mission.description ? DOMPurify.sanitize(mission.description) : '',
    date: mission.date,
    start_time: mission.start_time,
    end_time: mission.end_time,
    mission: mission.mission ? {
      ...mission.mission,
      title: DOMPurify.sanitize(mission.mission.title),
      description: mission.mission.description ? DOMPurify.sanitize(mission.mission.description) : undefined
    } : mission.mission,
    user: mission.user ? {
      ...mission.user,
      prenom: DOMPurify.sanitize(mission.user.prenom),
      nom: DOMPurify.sanitize(mission.user.nom),
      photo_url: mission.user.photo_url
    } : undefined,
    proposition: mission.proposition ? {
      ...mission.proposition,
      message: mission.proposition.message ? DOMPurify.sanitize(mission.proposition.message) : undefined,
      message_contre_offre: mission.proposition.message_contre_offre ? DOMPurify.sanitize(mission.proposition.message_contre_offre) : undefined,
      message_contre_offre_jobbeur: mission.proposition.message_contre_offre_jobbeur ? DOMPurify.sanitize(mission.proposition.message_contre_offre_jobbeur) : undefined
    } : undefined
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Date non définie';
    const date = new Date(dateString);
    return format(date, 'EEEE d MMMM yyyy', { locale: fr });
  };

  // Formater l'heure pour un affichage plus propre (enlever les secondes)
  const formatTime = (time: string) => {
    return time?.substring(0, 5) || ''; // Garde seulement HH:MM
  };

  // Nettoyer le HTML pour afficher uniquement le texte
  const stripHtml = (html: string | undefined) => {
    if (!html) return '';
    
    // Utiliser DOMPurify pour sanitizer avant de récupérer le texte
    const sanitizedHtml = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [], // Ne permet aucune balise pour obtenir du texte brut
      ALLOWED_ATTR: []  // Ne permet aucun attribut
    });
    
    // Créer un élément div temporaire
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = sanitizedHtml;
    
    // Récupérer le texte sans les balises
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  const handleClose = () => {
    onClose();
  };

  // Calculer la durée de la mission
  const calculateDuration = (startTime: string, endTime: string) => {
    if (!startTime || !endTime) return '';
    
    // Convertir les heures en minutes
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    const startTotalMinutes = startHour * 60 + startMinute;
    const endTotalMinutes = endHour * 60 + endMinute;
    
    // Calculer la différence en minutes
    let diffMinutes = endTotalMinutes - startTotalMinutes;
    
    // Si la différence est négative, cela signifie que la mission se termine le jour suivant
    if (diffMinutes < 0) {
      diffMinutes += 24 * 60; // Ajouter 24 heures en minutes
    }
    
    // Convertir en heures et minutes
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    // Formater le résultat
    return `${hours}h${minutes > 0 ? ` ${minutes}min` : ''}`;
  };

  // Obtenir le montant final négocié
  const getFinalAmount = () => {
    if (!sanitizedMission.proposition) return null;
    
    const prop = sanitizedMission.proposition;
    if (prop.statut === 'contre_offre_jobbeur' && prop.montant_contre_offre_jobbeur !== undefined) {
      return prop.montant_contre_offre_jobbeur;
    } else if (prop.statut === 'contre_offre' && prop.montant_contre_offre !== undefined) {
      return prop.montant_contre_offre;
    }
    return prop.montant_propose;
  };

  // Obtenir l'état du paiement avec une couleur associée
  const getPaymentStatus = () => {
    if (!sanitizedMission.proposition || !sanitizedMission.proposition.payment_status) {
      return { label: 'En attente de paiement', color: '#FF965E', icon: <Circle size={16} /> };
    }
    
    switch (sanitizedMission.proposition.payment_status) {
      case 'completed':
        return { label: 'Paiement effectué', color: '#22C55E', icon: <CheckCircle size={16} /> };
      case 'manual':
        return { label: 'Paiement manuel effectué', color: '#22C55E', icon: <CheckCircle size={16} /> };
      default:
        return { label: 'En attente de paiement', color: '#FF965E', icon: <AlertCircle size={16} /> };
    }
  };

  // Fonction pour ouvrir le profil utilisateur
  const handleOpenUserProfile = async (userId: string) => {
    if (!userId) {
      notify('ID utilisateur manquant', 'error');
      return;
    }

    try {
      // D'abord récupérer le slug de l'utilisateur
      const slugResponse = await api.get(`/api/users/get-slug/${userId}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true
      });
      
      if (!slugResponse.data.success || !slugResponse.data.slug) {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
        return;
      }

      // Ensuite récupérer le profil complet avec le slug
      const response = await api.get(`/api/users/profil/${slugResponse.data.slug}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true
      });
      
      if (response.data) {
        setSelectedUserProfile(response.data);
        setIsUserProfileModalOpen(true);
      } else {
        notify('Impossible de récupérer le profil de l\'utilisateur', 'error');
      }
    } catch (error) {
      logger.info('Erreur lors de la récupération du profil:', error); 
      notify('Erreur lors de la récupération du profil', 'error');
    }
  };

  const handleViewFullMission = () => {
    // Utiliser l'ID de la mission JobPartiel (mission.mission.id) et non l'ID du planning (mission.id)
    if (sanitizedMission.mission?.id) {
      navigate(`/dashboard/missions/${sanitizedMission.mission.id}`);
      handleClose();
    } else {
      notify('Cette mission n\'est pas liée à un JobPartiel', 'warning');
    }
  };

  return (
    <ModalPortal isOpen={open} onBackdropClick={handleClose} zIndex={1000}>
      <Dialog 
        open={true} 
        maxWidth="sm" 
        fullWidth
        hideBackdrop={true}
        onClose={(event, reason) => {
          if (reason === 'backdropClick') {
            handleClose();
          }
        }}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            backgroundColor: '#fff',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
            overflow: 'auto',
            overflowY: 'scroll',
            m: {
              xs: '8px', // Réduire les marges sur mobile
              sm: '16px', // Marges standards sur tablette et plus grand
              md: '32px'  // Marges standards sur desktop
            },
            minWidth: '800px', // Largeur minimale fixée à 600px
            width: {
              xs: 'calc(100% - 16px)', // Largeur ajustée pour mobile
              sm: 'auto'
            },
            maxHeight: {
              xs: 'calc(100% - 16px)', // Hauteur maximale ajustée pour mobile
              sm: 'auto'
            },
            '@media (max-width: 650px)': {
              minWidth: 'calc(100% - 16px)', // Sur très petits écrans, on utilise toute la largeur disponible
              margin: '8px'
            }
          }
        }}
      >
        <DialogTitle 
          sx={{ 
            backgroundColor: '#FF6B2C', 
            color: 'white', 
            fontWeight: 700,
            fontSize: { xs: '1rem', sm: '1.1rem' },
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: { xs: '14px 16px', sm: '18px 24px' },
          }}
        >
          Détails de la mission
          <IconButton 
            onClick={handleClose} 
            sx={{ 
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.2)'
              }
            }}
            size="small"
          >
            <X size={18} />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ 
          padding: { xs: '20px 16px', sm: '28px 24px' }, 
          background: '#fff',
          overflow: 'auto',
          maxHeight: { xs: 'calc(100vh - 150px)', sm: 'calc(100vh - 200px)' } 
        }}>
          {/* Titre de la mission */}
          <Typography 
            variant="h5" 
            component="h2" 
            sx={{ 
              fontWeight: 700, 
              mt: 1,
              mb: 3,
              color: '#333',
              position: 'relative',
              fontSize: { xs: '1.25rem', sm: '1.5rem' },
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-8px',
                left: 0,
                width: '40px',
                height: '3px',
                backgroundColor: '#FF6B2C',
                borderRadius: '2px'
              }
            }}
          >
            {sanitizedMission.title}
          </Typography>

          {/* Information jobbeur */}
          {sanitizedMission.user && (
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Avatar 
                  src={sanitizedMission.user?.photo_url}
                  alt={sanitizedMission.user?.prenom || 'Avatar'}
                  sx={{ 
                    width: 56, 
                    height: 56,
                    border: '2px solid #fff',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'scale(1.05)'
                    }
                  }}
                  onClick={() => sanitizedMission.user?.id && handleOpenUserProfile(sanitizedMission.user.id)}
                />
                <Box>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontSize: '1.1rem', 
                      fontWeight: 600, 
                      color: '#333',
                      cursor: 'pointer',
                      '&:hover': {
                        color: '#FF6B2C'
                      }
                    }}
                    onClick={() => sanitizedMission.user?.id && handleOpenUserProfile(sanitizedMission.user.id)}
                  >
                    {sanitizedMission.user?.prenom} {sanitizedMission.user?.nom ? sanitizedMission.user.nom.charAt(0).toUpperCase() + '.' : ''}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>Prestataire</Typography>
                </Box>
              </Box>
            </Box>
          )}

          {/* Description section - Updated to fix text visibility issue */}
          {sanitizedMission.description && (
            <Box 
              sx={{ 
                mb: 4,
                pb: 4,
                borderBottom: '1px solid #eee' 
              }}
            >
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mb: 2,
                  gap: 1.5
                }}
              >
                <FileText size={18} style={{ color: '#FF6B2C' }} />
                <Typography sx={{ color: '#444', fontWeight: 600, fontSize: '1rem' }}>
                  Description détaillée
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                {/* Option 1: Utiliser dangerouslySetInnerHTML avec du contenu sanitisé */}
                <div 
                  className="tiptap-content" 
                  dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(sanitizedMission.description) }}
                  style={{ 
                    padding: '12px', 
                    border: '1px solid #eee', 
                    borderRadius: '8px',
                    backgroundColor: '#f9f9f9',
                    lineHeight: 1.6
                  }}
                />
              </Box>
            </Box>
          )}

          {/* Détails de la date et heure */}
          <Box sx={{ mb: 4 }}>
            <Typography 
              variant="subtitle1" 
              sx={{ 
                fontWeight: 600, 
                color: '#555',
                fontSize: '0.95rem',
                mb: 1.5,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <CalendarDays size={18} color="#FF6B2C" />
              Date et horaires
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Typography variant="body2" sx={{ color: '#666' }}>
                Date: <strong>{formatDate(sanitizedMission.date)}</strong>
              </Typography>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                backgroundColor: 'rgba(255, 107, 44, 0.05)',
                p: 1.5, 
                borderRadius: '8px'
              }}>
                <Clock size={18} color="#FF6B2C" style={{ marginRight: '8px' }} />
                <Typography variant="body2" sx={{ color: '#666' }}>
                  De <strong>{formatTime(sanitizedMission.start_time)}</strong> à <strong>{formatTime(sanitizedMission.end_time)}</strong>
                </Typography>
              </Box>
              
              {/* Calculer et afficher la durée totale */}
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                justifyContent: 'flex-end',
                mt: -0.5 
              }}>
                <Typography variant="caption" sx={{ color: '#777', fontStyle: 'italic' }}>
                  Durée: {calculateDuration(sanitizedMission.start_time, sanitizedMission.end_time)}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Informations de paiement */}
          {sanitizedMission.proposition && sanitizedMission.proposition.statut === 'acceptée' && (
            <Box sx={{ mb: 4 }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontWeight: 600, 
                  color: '#555',
                  fontSize: '0.95rem',
                  mb: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <Coins size={18} color="#FF6B2C" />
                Paiement
              </Typography>
              
              {/* Afficher le montant */}
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                backgroundColor: 'rgba(255, 107, 44, 0.05)',
                p: 1.5, 
                borderRadius: '8px',
                mb: 1.5
              }}>
                <Typography variant="body1" sx={{ color: '#333', fontWeight: 600, flex: 1 }}>
                  {getFinalAmount()} Jobis
                </Typography>
                
                {/* Status du paiement */}
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  px: 1.5,
                  py: 0.5,
                  borderRadius: '20px',
                  backgroundColor: `${getPaymentStatus().color}15`,
                  color: getPaymentStatus().color,
                  gap: 0.5
                }}>
                  {getPaymentStatus().icon}
                  <Typography variant="caption" sx={{ fontWeight: 600 }}>
                    {getPaymentStatus().label}
                  </Typography>
                </Box>
              </Box>
              
              {/* Message de la proposition */}
              {sanitizedMission.proposition.message && (
                <Box sx={{ mb: 1.5 }}>
                  <Typography variant="body2" sx={{ color: '#666', mb: 0.5 }}>
                    Message du prestataire:
                  </Typography>
                  <Box sx={{ 
                    backgroundColor: '#f9f9f9', 
                    p: 1.5, 
                    borderRadius: '8px',
                    borderLeft: '3px solid #ddd',
                    fontStyle: 'italic'
                  }}>
                    <Typography variant="body2" sx={{ color: '#555' }}>
                      "{stripHtml(sanitizedMission.proposition.message)}"
                    </Typography>
                  </Box>
                </Box>
              )}
              
              <Divider sx={{ my: 2 }} />
              
              {/* Status global du paiement */}
              <Box sx={{ 
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Box sx={{ 
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  backgroundColor: getPaymentStatus().color
                }} />
                <Typography variant="body2" sx={{ color: '#555' }}>
                  {getPaymentStatus().label}
                  {sanitizedMission.proposition.payment_status === 'completed' && sanitizedMission.proposition.payment_date && (
                    <Typography 
                      component="span" 
                      variant="body2" 
                      sx={{ 
                        color: '#777',
                        ml: 0.5
                      }}
                    >
                      le {new Date(sanitizedMission.proposition.payment_date).toLocaleDateString()}
                    </Typography>
                  )}
                </Typography>
              </Box>
            </Box>
          )}

          {/* Détails de la mission liée */}
          {sanitizedMission.mission?.id && (
            <Box sx={{ mb: 4 }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontWeight: 600, 
                  color: '#555',
                  fontSize: '0.95rem',
                  mb: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <FileText size={18} color="#FF6B2C" />
                Mission associée
              </Typography>
              <strong>{sanitizedMission.mission.title}</strong>
              {sanitizedMission.mission.description && (
                <Typography variant="body2" sx={{ color: '#666', mt: 1, lineHeight: 1.6 }}>
                  {stripHtml(sanitizedMission.mission.description)}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{
          px: 3,
          py: 2,
          backgroundColor: '#f9f9f9',
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: 'stretch',
          gap: 1,
          '& .MuiButton-root': {
            width: { xs: '100%', sm: 'auto' }
          }
        }}>
          {/* Afficher le bouton "Voir la mission" seulement si c'est une mission liée à un JobPartiel */}
          {sanitizedMission.mission?.id && (
            <Button
              onClick={handleViewFullMission}
              variant="contained"
              startIcon={<ExternalLink size={16} />}
              sx={{
                backgroundColor: '#FF7A35',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#FF965E',
                  boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)',
                },
                textTransform: 'none',
                fontSize: '0.875rem',
                fontWeight: 500,
                borderRadius: '8px',
                px: 3,
                order: { xs: 1, sm: 2 }
              }}
            >
              Voir la mission
            </Button>
          )}
          <Button 
            onClick={handleClose}
            sx={{
              borderColor: '#ddd',
              color: '#666',
              '&:hover': {
                borderColor: '#ccc',
                backgroundColor: 'rgba(0, 0, 0, 0.03)',
              },
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 500,
              borderRadius: '8px',
              px: 3,
              order: { xs: 2, sm: 1 }
            }}
          >
            Fermer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale de profil utilisateur */}
      {selectedUserProfile && (
        <UserProfileModal 
          isOpen={isUserProfileModalOpen}
          onClose={() => setIsUserProfileModalOpen(false)}
          userData={selectedUserProfile}
          zIndex={2000}
        />
      )}
    </ModalPortal>
  );
};

export default MissionRecapModal; 