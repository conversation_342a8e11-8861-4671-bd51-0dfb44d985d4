import React from 'react';
import { BadgeCheck } from 'lucide-react';
import { Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';

const BadgeContainer = styled('div')({
  position: 'absolute',
  bottom: 0,
  left: -2,
  backgroundColor: 'white',
  borderRadius: '50%',
  padding: '2px',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  cursor: 'help',
  zIndex: 2,
});

interface VerificationBadgeProps {
  isVerified: boolean;
  type?: 'professionnel' | 'particulier' | 'entreprise';
}

const VerificationBadge: React.FC<VerificationBadgeProps> = ({ isVerified, type = 'particulier' }) => {
  if (!isVerified) return null;

  const tooltipText = type === 'entreprise' 
    ? "Profil vérifié par JobPartiel : identité, documents et informations validés"
    : "Profil vérifié par JobPartiel : identité validée";

  return (
    <Tooltip 
      title={tooltipText}
      placement="top"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: '#FF6B2C',
            color: 'white',
            fontSize: '0.875rem',
            padding: '0.5rem 0.75rem',
            borderRadius: '0.5rem',
            boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
            '& .MuiTooltip-arrow': {
              color: 'rgba(255, 255, 255, 0.95)'
            }
          }
        }
      }}
    >
      <BadgeContainer>
        <BadgeCheck className="h-4 w-4 text-emerald-500" />
      </BadgeContainer>
    </Tooltip>
  );
};

export default VerificationBadge; 