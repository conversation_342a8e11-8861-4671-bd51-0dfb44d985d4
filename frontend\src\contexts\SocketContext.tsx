import React, { createContext, useContext, useEffect, useState, ReactNode, useRef } from 'react';
import socketIOClient from 'socket.io-client';
import { useAuth } from './AuthContext';
import { useMessageSound } from '../hooks/useMessageSound';
import { logger } from '@/utils/logger';
import { createAuthService } from '../services/auth';
import { SocketConnectionManager, calculateBackoffDelay, cleanupSocketFlags, isWebSocketSupported } from '../utils/socketUtils';

// Définition du type Socket pour socket.io-client v4
interface SocketType {
  disconnect: () => void;
  on: (event: string, callback: (...args: any[]) => void) => void;
  off: (event: string, callback?: (...args: any[]) => void) => void;
  emit: (event: string, ...args: any[]) => void;
  id?: string;
  connected?: boolean;
}

interface SocketContextType {
  socket: SocketType | null;
  isConnected: boolean;
  error: Error | null;
  reconnectAttempts: number;
}

// Configuration par défaut pour les reconnexions
const RECONNECTION_ATTEMPTS = 5;
const INITIAL_RECONNECTION_DELAY = 2000;
const MAX_RECONNECTION_DELAY = 60000;

// Créer le contexte
const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [socket, setSocket] = useState<SocketType | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [reconnectionTimer, setReconnectionTimer] = useState<NodeJS.Timeout | null>(null);
  const { playMessageSound } = useMessageSound();
  const socketRef = useRef<SocketType | null>(null);
  const authService = createAuthService();
  const [socketBlocked, setSocketBlocked] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const connectionManager = SocketConnectionManager.getInstance();
  const connectionIdRef = useRef<string>('');

  // Vérifier le support WebSocket au démarrage
  useEffect(() => {
    if (!isWebSocketSupported()) {
      logger.error('WebSocket non supporté par ce navigateur');
      setError(new Error('WebSocket non supporté par ce navigateur'));
      return;
    }
    
    // Nettoyer les flags obsolètes au démarrage
    cleanupSocketFlags();
  }, []);

  useEffect(() => {
    let unmounted = false;

    const setupSocket = async () => {
      if (unmounted || isConnecting) return;

      if (!user?.id || !user?.email || !isAuthenticated) {
        if (socketRef.current) {
          logger.info('Déconnexion du WebSocket (utilisateur non authentifié)');
          connectionManager.unregisterConnection(connectionIdRef.current);
          socketRef.current.disconnect();
          socketRef.current = null;
          setSocket(null);
          setIsConnected(false);
        }
        setError(null);
        setSocketBlocked(false);
        return;
      }

      if (socketBlocked) {
        logger.info('Socket bloqué suite à des erreurs serveur, pas de tentative de connexion');
        return;
      }

      if (socketRef.current?.connected) {
        logger.info('Socket déjà connecté, pas besoin de reconnecter');
        return;
      }

      setIsConnecting(true);

      if (reconnectionTimer) {
        clearTimeout(reconnectionTimer);
        setReconnectionTimer(null);
      }

      if (socketRef.current) {
        connectionManager.unregisterConnection(connectionIdRef.current);
        socketRef.current.disconnect();
      }

      // Générer un ID unique pour cette connexion
      connectionIdRef.current = `socket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      connectionManager.registerConnection(connectionIdRef.current);

      const now = Date.now();
      const socketFlag = localStorage.getItem('socket_active');

      if (socketFlag) {
        try {
          const { timestamp } = JSON.parse(socketFlag);
          if (now - timestamp < 30000) {
            logger.info('Connexion socket détectée dans un autre onglet, mais autorisation de connexion multiple');
          } else {
            localStorage.removeItem('socket_active');
          }
        } catch {
          localStorage.removeItem('socket_active');
        }
      }
      localStorage.setItem('socket_active', JSON.stringify({ timestamp: now }));

      const socketUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';

      try {
        async function fetchSocketTokenWithRefresh() {
          const MAX_TOKEN_FETCH_ATTEMPTS = 3;
          let attempts = 0;
          
          while (attempts < MAX_TOKEN_FETCH_ATTEMPTS) {
            // Utiliser le gestionnaire de connexions pour contrôler les requêtes
            if (!connectionManager.canFetchToken()) {
              const waitTime = connectionManager.getTokenFetchDelay();
              logger.info(`Attente de ${waitTime}ms avant la prochaine requête de token`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
            
            connectionManager.updateLastTokenFetch();

            const timestamp = Date.now();
            let response = await fetch(`${socketUrl}/api/auth/socket/token?_=${timestamp}`, {
              credentials: 'include'
            });

            if (response.status === 403 || response.status === 429) {
              logger.warn(`Erreur ${response.status} lors de la récupération du token socket, blocage des tentatives`);
              setError(new Error('Connexion socket bloquée suite à une erreur serveur. Veuillez recharger la page.'));
              setSocketBlocked(true);
              
              // Programmer un déblocage automatique après 5 minutes
              setTimeout(() => {
                logger.info('Déblocage automatique du socket après 5 minutes');
                setSocketBlocked(false);
                setError(null);
              }, 5 * 60 * 1000);
              
              return null;
            }

            if (response.status === 401) {
              logger.info('Token socket expiré, tentative de rafraîchissement...');
              const refreshResult = await authService.refreshToken();

              if (!refreshResult.success) {
                logger.info('Échec du rafraîchissement du token, nouvelle tentative...');
                attempts++;
                const backoffDelay = calculateBackoffDelay(attempts, 2000);
                await new Promise(resolve => setTimeout(resolve, backoffDelay));
                continue;
              }

              await new Promise(resolve => setTimeout(resolve, 1000));
              const newTimestamp = Date.now();
              response = await fetch(`${socketUrl}/api/auth/socket/token?_=${newTimestamp}`, {
                credentials: 'include'
              });
            }

            let data;
            try {
              data = await response.json();
            } catch (error) {
              logger.error('Erreur lors du parsing de la réponse socket token:', error);
              attempts++;
              const backoffDelay = calculateBackoffDelay(attempts, 2000);
              await new Promise(resolve => setTimeout(resolve, backoffDelay));
              continue;
            }

            if (data.shouldRefresh) {
              logger.info('Le serveur demande un rafraîchissement du token...');
              await authService.refreshToken();
              await new Promise(resolve => setTimeout(resolve, 1000));

              const newTimestamp = Date.now();
              response = await fetch(`${socketUrl}/api/auth/socket/token?_=${newTimestamp}`, {
                credentials: 'include'
              });

              try {
                const data2 = await response.json();
                if (data2.success && data2.token) return data2.token;
              } catch (error) {
                logger.error('Erreur lors du parsing de la réponse après rafraîchissement:', error);
              }
            } else if (data.success && data.token) {
              return data.token;
            }

            attempts++;
            const backoffDelay = calculateBackoffDelay(attempts, 2000);
            await new Promise(resolve => setTimeout(resolve, backoffDelay));
          }
          throw new Error(`Impossible de récupérer le token socket après ${MAX_TOKEN_FETCH_ATTEMPTS} tentatives. Merci de vous reconnecter.`);
        }

        const token = await fetchSocketTokenWithRefresh();

        if (!token) {
          logger.info('🔴 Aucun token JWT trouvé pour l\'authentification socket');
          setError(new Error('Aucun token d\'authentification trouvé'));
          setIsConnecting(false);
          connectionManager.unregisterConnection(connectionIdRef.current);
          return;
        }

        const newSocket = socketIOClient(socketUrl, {
          autoConnect: true,
          reconnection: false,
          transports: ['websocket'],
          auth: {
            token: token
          }
        });

        socketRef.current = newSocket;

        newSocket.on('connect', () => {
          if (unmounted) return;
          logger.info('✅ WebSocket connecté avec succès, ID:', newSocket?.id);
          setIsConnected(true);
          setError(null);
          setReconnectAttempts(0);
          setSocket(newSocket);
          setIsConnecting(false);
        });

        newSocket.on('disconnect', (reason) => {
          if (unmounted) return;
          logger.info(`🔴 WebSocket déconnecté: ${reason}`);
          setIsConnected(false);
          setSocket(null);
          setIsConnecting(false);
          connectionManager.unregisterConnection(connectionIdRef.current);

          if (reason !== 'io client disconnect' && !socketBlocked) {
            scheduleReconnect();
          }
        });

        newSocket.on('connect_error', (err: Error) => {
          if (unmounted) return;
          logger.info('🔴 Erreur de connexion WebSocket:', err.message);
          setError(err);
          setIsConnected(false);
          setSocket(null);
          setIsConnecting(false);
          connectionManager.unregisterConnection(connectionIdRef.current);
          
          if (!socketBlocked) {
            scheduleReconnect();
          }
        });

        newSocket.on('connected', (data) => {
          if (unmounted) return;
          logger.info('✅ Confirmation de connexion reçue du serveur:', data);
        });

        newSocket.on('new_message', (payload: { sender_id: string }) => {
          if (payload.sender_id !== user.id) {
            playMessageSound('received');
          }
        });

      } catch (err) {
        if (unmounted) return;
        logger.info('🔴 Erreur lors de l\'initialisation du WebSocket:', err);
        setError(err instanceof Error ? err : new Error('Erreur de connexion WebSocket'));
        setIsConnected(false);
        setSocket(null);
        setIsConnecting(false);
        connectionManager.unregisterConnection(connectionIdRef.current);
        
        const errMsg = err instanceof Error ? err.message : String(err);
        if (errMsg.includes('403') || errMsg.toLowerCase().includes('forbidden') || errMsg.toLowerCase().includes('accès interdit')) {
          logger.info('⛔ Reconnexion WebSocket stoppée suite à une erreur 403/forbidden.');
          return;
        }
        if (!(err instanceof Error && err.message.includes('token')) && !socketBlocked) {
          scheduleReconnect();
        }
      }
    };

    const scheduleReconnect = () => {
      if (unmounted || reconnectAttempts >= RECONNECTION_ATTEMPTS || socketBlocked || isConnecting) {
        if (socketBlocked) {
          logger.info('Reconnexion annulée car le socket est bloqué');
        } else if (isConnecting) {
          logger.info('Reconnexion annulée car une connexion est déjà en cours');
        } else {
          logger.info(`Nombre maximum de tentatives de reconnexion atteint (${RECONNECTION_ATTEMPTS})`);
        }
        return;
      }

      if (!user?.id || !user?.email || !isAuthenticated) {
        logger.info('Reconnexion annulée car l\'utilisateur n\'est pas authentifié');
        return;
      }

      const nextAttempt = reconnectAttempts + 1;
      const delay = calculateBackoffDelay(nextAttempt, INITIAL_RECONNECTION_DELAY, MAX_RECONNECTION_DELAY);

      logger.info(`Tentative de reconnexion prévue dans ${delay}ms (tentative ${nextAttempt}/${RECONNECTION_ATTEMPTS})`);

      const timer = setTimeout(() => {
        setReconnectAttempts(nextAttempt);
        setupSocket();
      }, delay);

      setReconnectionTimer(timer);
    };

    setupSocket();

    return () => {
      unmounted = true;

      if (reconnectionTimer) {
        clearTimeout(reconnectionTimer);
      }

      if (socketRef.current) {
        logger.info('Nettoyage de la connexion WebSocket (démontage du contexte)');
        connectionManager.unregisterConnection(connectionIdRef.current);
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      
      setIsConnecting(false);
    };
  }, [user?.id, user?.email, isAuthenticated]);

  const contextValue: SocketContextType = {
    socket,
    isConnected,
    error,
    reconnectAttempts
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

// Hook personnalisé pour utiliser le contexte
export const useSocket = () => {
  const context = useContext(SocketContext);

  if (context === undefined) {
    throw new Error('useSocket doit être utilisé à l\'intérieur d\'un SocketProvider');
  }

  return context;
};