import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Button,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Pagination,
  Container,
  useTheme,
  useMediaQuery,
  LinearProgress,
  Divider,
  Badge
} from '@mui/material';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { Helmet } from 'react-helmet-async';
import { getCommonHeaders } from '../../utils/headers';
import { API_CONFIG } from '../../config/api';
import { notify } from '../../components/Notification';
import logger from '../../utils/logger';
import {
  ShieldAlert,
  Shield,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Filter,
  Eye,
  Trash2,
  Server,
  AlertOctagon,
  Activity,
  Network,
  Lock,
  Database,
  Users,
  FileText,
  TrendingUp,
  CheckCircle,
  Clock,
  Globe,
  Key,
  UserCheck,
  Zap,
  BarChart3,
  PieChart as PieChartIcon,
  ShieldCheck,
  AlertCircle,
  Download,
  FileDown
} from 'lucide-react';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

interface SecurityDashboard {
  overview: {
    totalSecurityLogs: number;
    criticalAlerts: number;
    blockedIPs: number;
    failedLogins: number;
    suspiciousActivities: number;
    encryptedDataPercentage: number;
    activeUsers24h: number;
    totalUsers: number;
    dataProtectionScore: number;
  };
  recentAlerts: SecurityLog[];
  eventStats: Record<string, Record<string, number>>;
  topSuspiciousIPs: Array<{ ip: string; count: number }>;
  encryptionStats: {
    totalEncryptedFields: number;
    encryptionCoverage: number;
    lastEncryptionUpdate: string;
  };
  complianceMetrics: {
    gdprCompliance: number;
    dataRetentionCompliance: number;
    accessControlCompliance: number;
    auditTrailCompleteness: number;
  };
}

interface SecurityLog {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message?: string;
  ip_address?: string;
  user_id?: string;
  user_agent?: string;
  details: Record<string, any>;
  created_at: string;
  expires_at?: string;
}

interface BlockedIP {
  ip: string;
  type: 'brute_force' | 'manual' | 'permanent';
  expiresAt: string | null;
  remainingTime: number;
  reason: string;
  admin_user?: string;
  blocked_at?: number;
}

interface SecurityFilters {
  severity: string;
  type: string;
  ip_address: string;
  startDate: string;
  endDate: string;
}

interface SecurityStats {
  period: string;
  typeStats: Record<string, number>;
  severityStats: Record<string, number>;
  timeline: Record<string, { 
    total: number; 
    high: number; 
    medium: number; 
    low: number; 
  }>;
}

// Styles personnalisés
const StyledPaper = styled(Paper)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  padding: theme.spacing(3),
  height: '100%',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: theme.spacing(1),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: COLORS.primary,
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.5rem',
  },
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  border: `1px solid ${COLORS.borderColor}`,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px 0 rgba(0,0,0,0.08)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  marginBottom: theme.spacing(2),
  color: '#2D3748',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.1rem',
  },
}));

interface IconBoxProps {
  color?: string;
}

const IconBox = styled(Box)<IconBoxProps>(({ theme, color = COLORS.primary }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: COLORS.white,
  borderRadius: '50%',
  padding: theme.spacing(1),
  border: `2px solid ${color}`,
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  color: color,
  position: 'absolute',
  top: '-15px',
  right: '20px',
  [theme.breakpoints.down('sm')]: {
    display: 'none', // Masquer sur mobile pour économiser l'espace
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: 'none',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 4px',
  },
  '& .MuiTableHead-root .MuiTableCell-root': {
    backgroundColor: COLORS.lightGray,
    fontWeight: 600,
    padding: theme.spacing(1.5),
    color: '#475569',
    border: 'none',
    fontSize: '0.875rem',
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(1),
      fontSize: '0.75rem',
    },
  },
  '& .MuiTableBody-root .MuiTableRow-root': {
    backgroundColor: COLORS.white,
    boxShadow: '0 1px 3px 0 rgba(0,0,0,0.05)',
    transition: 'background-color 0.2s',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.04)',
    },
  },
  '& .MuiTableBody-root .MuiTableCell-root': {
    padding: theme.spacing(1.5),
    border: 'none',
    borderBottom: `1px solid ${COLORS.borderColor}`,
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(1),
      fontSize: '0.75rem',
    },
  }
}));

const StyledFormControl = styled(FormControl)(() => ({
  '& .MuiInputLabel-root': {
    color: '#475569',
  },
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    '&:hover fieldset': {
      borderColor: COLORS.primary,
    },
    '&.Mui-focused fieldset': {
      borderColor: COLORS.primary,
    },
  },
}));

const ChipStyled = styled(Chip)(({ theme }) => ({
  borderRadius: '6px',
  fontWeight: 500,
  height: '24px',
  '&.MuiChip-colorPrimary': {
    backgroundColor: `${COLORS.primary}30`,
    color: COLORS.primary,
  },
  '&.MuiChip-colorSecondary': {
    backgroundColor: `${COLORS.secondary}30`,
    color: COLORS.secondary,
  },
  '&.MuiChip-colorDefault': {
    backgroundColor: `${COLORS.neutral}30`,
    color: COLORS.neutral,
  },
  '&.MuiChip-colorSuccess': {
    backgroundColor: `${COLORS.success}30`,
    color: COLORS.success,
  },
  '&.MuiChip-colorError': {
    backgroundColor: `${COLORS.error}30`,
    color: COLORS.error,
  },
  '&.MuiChip-colorWarning': {
    backgroundColor: `${COLORS.warning}30`,
    color: COLORS.warning,
  },
  '&.MuiChip-colorInfo': {
    backgroundColor: `${COLORS.info}30`,
    color: COLORS.info,
  },
  [theme.breakpoints.down('sm')]: {
    height: '20px',
    fontSize: '0.7rem',
  },
}));

const ResponsiveTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTab-root': {
    color: '#666',
    minWidth: 'auto',
    padding: theme.spacing(1, 2),
    '&.Mui-selected': {
      color: COLORS.primary
    },
    [theme.breakpoints.down('md')]: {
      minWidth: 'auto',
      padding: theme.spacing(1, 1),
      fontSize: '0.875rem',
      '& .MuiTab-iconWrapper': {
        marginBottom: '4px',
      },
    },
    [theme.breakpoints.down('sm')]: {
      minWidth: 'auto',
      padding: theme.spacing(0.5, 0.5),
      fontSize: '0.75rem',
      flexDirection: 'column',
      '& .MuiTab-iconWrapper': {
        marginRight: 0,
        marginBottom: '2px',
      },
    },
  },
  '& .MuiTabs-indicator': {
    backgroundColor: COLORS.primary
  },
  '& .MuiTabs-scroller': {
    [theme.breakpoints.down('sm')]: {
      '& .MuiTabs-flexContainer': {
        justifyContent: 'space-around',
      },
    },
  },
}));

const ChartContainer = styled(Box)(({ theme }) => ({
  height: 300,
  [theme.breakpoints.down('md')]: {
    height: 250,
  },
  [theme.breakpoints.down('sm')]: {
    height: 200,
  },
}));

const SecurityMonitoring: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState<SecurityDashboard | null>(null);
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [blockedIPs, setBlockedIPs] = useState<BlockedIP[]>([]);
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  const [currentTab, setCurrentTab] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedLog, setSelectedLog] = useState<SecurityLog | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [statsPeriod, setStatsPeriod] = useState<string>('7d');
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // Filtres
  const [filters, setFilters] = useState<SecurityFilters>({
    severity: '',
    type: '',
    ip_address: '',
    startDate: '',
    endDate: ''
  });

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const limit = 50;

  // États pour les nouvelles fonctionnalités
  const [blockIPDialog, setBlockIPDialog] = useState(false);
  const [auditDialog, setAuditDialog] = useState(false);
  const [blockIPForm, setBlockIPForm] = useState({
    ip: '',
    duration: 1,
    durationType: 'hours',
    reason: ''
  });
  const [auditProgress, setAuditProgress] = useState(0);
  const [auditSteps, setAuditSteps] = useState<any[]>([]);
  const [auditRunning, setAuditRunning] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    loadDashboard();
    loadSecurityLogs();
    loadBlockedIPs();
    loadSecurityStats();
  }, [refreshTrigger]);

  useEffect(() => {
    loadSecurityLogs();
  }, [page, filters]);

  useEffect(() => {
    loadSecurityStats();
  }, [statsPeriod]);

  const loadDashboard = async () => {
    try {
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/dashboard`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement du dashboard');
      }

      const data = await response.json();
      setDashboard(data.data);
    } catch (error) {
      logger.error('Erreur dashboard:', error);
      setError('Erreur lors du chargement du dashboard de sécurité');
      notify('Erreur lors du chargement du dashboard de sécurité', 'error');
    }
  };

  const loadSecurityLogs = async () => {
    try {
      setLoading(true);
      const headers = await getCommonHeaders();

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      });

      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/logs?${queryParams}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des logs');
      }

      const data = await response.json();
      setSecurityLogs(data.data);
      setTotalPages(data.pagination.totalPages);
    } catch (error) {
      logger.error('Erreur logs:', error);
      setError('Erreur lors du chargement des logs de sécurité');
      notify('Erreur lors du chargement des logs de sécurité', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadBlockedIPs = async () => {
    try {
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/blocked-ips`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des IPs bloquées');
      }

      const data = await response.json();
      setBlockedIPs(data.data);
    } catch (error) {
      logger.error('Erreur IPs bloquées:', error);
      setError('Erreur lors du chargement des IPs bloquées');
      notify('Erreur lors du chargement des IPs bloquées', 'error');
    }
  };

  const loadSecurityStats = async () => {
    try {
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/stats?period=${statsPeriod}`, {
        method: 'GET',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des statistiques');
      }

      const data = await response.json();
      setSecurityStats(data.data);
    } catch (error) {
      logger.error('Erreur statistiques:', error);
      setError('Erreur lors du chargement des statistiques de sécurité');
      notify('Erreur lors du chargement des statistiques de sécurité', 'error');
    }
  };

  const unblockIP = async (ip: string) => {
    try {
      const headers = await getCommonHeaders();

      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/blocked-ips/${ip}`, {
        method: 'DELETE',
        headers,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors du déblocage de l\'IP');
      }

      await loadBlockedIPs();
      notify(`IP ${ip} débloquée avec succès`, 'success');
      setError(null);
    } catch (error) {
      logger.error('Erreur déblocage IP:', error);
      setError('Erreur lors du déblocage de l\'IP');
      notify('Erreur lors du déblocage de l\'IP', 'error');
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
    notify('Données actualisées', 'success');
  };

  const handleFilterChange = (name: keyof SecurityFilters, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleResetFilters = () => {
    setFilters({
      severity: '',
      type: '',
      ip_address: '',
      startDate: '',
      endDate: ''
    });
    setPage(1);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return COLORS.error;
      case 'high': return COLORS.warning;
      case 'medium': return COLORS.info;
      case 'low': return COLORS.success;
      default: return COLORS.neutral;
    }
  };

  const getSeverityIcon = (severity: string, size: number = 20) => {
    switch (severity) {
      case 'critical': return <AlertOctagon size={size} />;
      case 'high': return <AlertTriangle size={size} />;
      case 'medium': return <AlertOctagon size={size} />;
      case 'low': return <Shield size={size} />;
      default: return <Shield size={size} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('fr-FR');
  };

  const formatRemainingTime = (seconds: number) => {
    if (seconds === -1) return 'Permanent';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getBlockTypeColor = (type: string) => {
    switch (type) {
      case 'permanent': return COLORS.error;
      case 'manual': return COLORS.warning;
      case 'brute_force': return COLORS.info;
      default: return COLORS.neutral;
    }
  };

  const getBlockTypeLabel = (type: string) => {
    switch (type) {
      case 'permanent': return 'Permanent';
      case 'manual': return 'Manuel';
      case 'brute_force': return 'Anti-Brute Force';
      default: return type;
    }
  };

  // Fonction pour bloquer une IP
  const handleBlockIP = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/block-ip`, {
        method: 'POST',
        headers: await getCommonHeaders(),
        credentials: 'include',
        body: JSON.stringify(blockIPForm)
      });

      if (!response.ok) {
        throw new Error('Erreur lors du blocage de l\'IP');
      }

      const result = await response.json();
      notify(result.message || 'IP bloquée avec succès', 'success');
      setBlockIPDialog(false);
      setBlockIPForm({ ip: '', duration: 1, durationType: 'hours', reason: '' });
      loadDashboard();
      loadBlockedIPs();
    } catch (error) {
      logger.error('Erreur lors du blocage de l\'IP:', error);
      notify('Erreur lors du blocage de l\'IP', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour télécharger le rapport de conformité PDF
  const handleDownloadComplianceReport = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/compliance-report`, {
        method: 'GET',
        headers: await getCommonHeaders(),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la génération du rapport');
      }

      // Créer un blob à partir de la réponse
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Créer un lien de téléchargement
      const link = document.createElement('a');
      link.href = url;
      link.download = `rapport-conformite-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();

      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      notify('Rapport de conformité téléchargé avec succès', 'success');
    } catch (error) {
      logger.error('Erreur lors du téléchargement du rapport:', error);
      notify('Erreur lors du téléchargement du rapport', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour effectuer un audit de sécurité
  const handleSecurityAudit = async () => {
    try {
      setAuditRunning(true);
      setAuditProgress(0);
      setAuditSteps([]);

      // Simulation des étapes d'audit avec animation
      const steps = [
        'Vérification du chiffrement',
        'Analyse des logs de sécurité',
        'Test des protections CSRF',
        'Vérification Rate Limiting',
        'Audit des permissions',
        'Test de pénétration',
        'Vérification GDPR',
        'Analyse des vulnérabilités',
        'Test de récupération',
        'Validation finale'
      ];

      // Animer les étapes
      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 800)); // Délai pour l'animation
        setAuditProgress(((i + 1) / steps.length) * 100);
        setAuditSteps(prev => [...prev, { step: steps[i], status: 'completed', score: 90 + Math.floor(Math.random() * 10) }]);
      }

      // Appel API pour l'audit réel
      const response = await fetch(`${API_CONFIG.baseURL}/api/security-monitoring/security-audit`, {
        method: 'POST',
        headers: await getCommonHeaders(),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'audit de sécurité');
      }

      const result = await response.json();

      // Mettre à jour les données du tableau de bord
      await loadDashboard();

      notify(`Audit de sécurité terminé - Score: ${result.data.globalScore}%`, 'success');

      // Fermer la modal après un délai
      setTimeout(() => {
        setAuditDialog(false);
        setAuditRunning(false);
        setAuditProgress(0);
        setAuditSteps([]);
      }, 2000);

    } catch (error) {
      logger.error('Erreur lors de l\'audit de sécurité:', error);
      notify('Erreur lors de l\'audit de sécurité', 'error');
      setAuditRunning(false);
    }
  };

  const prepareEventTypeData = () => {
    if (!securityStats?.typeStats) return [];

    return Object.entries(securityStats.typeStats)
      .map(([type, count]) => ({
        name: formatEventType(type),
        value: count,
        color: getEventTypeColor(type)
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 8); // Top 8 types d'événements
  };

  const prepareSeverityData = () => {
    if (!securityStats?.severityStats) return [];

    return Object.entries(securityStats.severityStats)
      .map(([severity, count]) => ({
        name: formatSeverity(severity),
        value: count,
        color: getSeverityColor(severity)
      }))
      .sort((a, b) => {
        const order = { critical: 4, high: 3, medium: 2, low: 1 };
        return (order[b.name.toLowerCase() as keyof typeof order] || 0) - 
               (order[a.name.toLowerCase() as keyof typeof order] || 0);
      });
  };

  const prepareSecurityTimelineData = () => {
    if (!securityStats?.timeline) return [];

    return Object.entries(securityStats.timeline)
      .map(([time, data]) => ({
        time,
        total: data.total,
        high: data.high,
        medium: data.medium,
        low: data.low
      }))
      .sort((a, b) => {
        // Trier par heure si période = 24h, sinon par date
        if (statsPeriod === '24h') {
          return parseInt(a.time) - parseInt(b.time);
        }
        return a.time.localeCompare(b.time);
      });
  };

  // Fonctions de formatage
  const formatEventType = (type: string): string => {
    const typeMap: Record<string, string> = {
      'LOGIN_SUCCESS': 'Connexion réussie',
      'LOGIN_FAILURE': 'Échec de connexion',
      'LOGOUT': 'Déconnexion',
      'AUTHENTICATION_FAILURE': 'Échec d\'authentification',
      'SUSPICIOUS_ACTIVITY_DETECTED_BRUTE_FORCE': 'Tentative de force brute',
      'IP_BLOCKED': 'IP bloquée',
      'IP_UNBLOCKED': 'IP débloquée',
      'PASSWORD_RESET_REQUEST': 'Demande de réinitialisation',
      'PASSWORD_RESET_SUCCESS': 'Réinitialisation réussie',
      'ACCOUNT_LOCKED': 'Compte verrouillé',
      'PERMISSION_DENIED': 'Accès refusé',
      'ADMIN_ACTION': 'Action administrateur',
      'CONFIG_CHANGE': 'Changement de configuration',
    };
    return typeMap[type] || type;
  };

  const formatSeverity = (severity: string): string => {
    const severityMap: Record<string, string> = {
      'critical': 'Critique',
      'high': 'Élevée',
      'medium': 'Moyenne',
      'low': 'Faible'
    };
    return severityMap[severity] || severity;
  };

  const getEventTypeColor = (type: string): string => {
    const typeColorMap: Record<string, string> = {
      'LOGIN_SUCCESS': COLORS.success,
      'LOGIN_FAILURE': COLORS.warning,
      'LOGOUT': COLORS.info,
      'AUTHENTICATION_FAILURE': COLORS.warning,
      'SUSPICIOUS_ACTIVITY_DETECTED_BRUTE_FORCE': COLORS.error,
      'IP_BLOCKED': COLORS.error,
      'IP_UNBLOCKED': COLORS.success,
      'PASSWORD_RESET_REQUEST': COLORS.info,
      'PASSWORD_RESET_SUCCESS': COLORS.success,
      'ACCOUNT_LOCKED': COLORS.error,
      'PERMISSION_DENIED': COLORS.warning,
      'ADMIN_ACTION': COLORS.primary,
      'CONFIG_CHANGE': COLORS.primary,
    };
    return typeColorMap[type] || COLORS.neutral;
  };

  // Composants réutilisables
  const StatCard = ({ title, value, icon, color, suffix }: {
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    suffix?: string;
  }) => (
    <MetricCard>
      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Typography color="text.secondary" gutterBottom variant="body2" fontWeight={500}>
            {title}
          </Typography>
          <Typography variant="h4" component="div" sx={{ color, fontWeight: 700 }}>
            {value.toLocaleString()}{suffix || ''}
          </Typography>
        </Box>
        <Box sx={{
          backgroundColor: `${color}10`,
          color,
          borderRadius: '50%',
          p: 1.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {icon}
        </Box>
      </Box>
    </MetricCard>
  );

  // Sections de contenu
  const renderOverview = () => {
    if (!dashboard) return null;

    return (
      <Box sx={{ mb: 4 }}>
        {/* Métriques principales de sécurité */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <StatCard
              title="Score de Protection"
              value={dashboard.overview.dataProtectionScore || 95}
              icon={<ShieldCheck size={24} />}
              color={COLORS.success}
              suffix="%"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <StatCard
              title="Données Chiffrées"
              value={dashboard.overview.encryptedDataPercentage || 98}
              icon={<Key size={24} />}
              color={COLORS.primary}
              suffix="%"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <StatCard
              title="Utilisateurs Actifs (24h)"
              value={dashboard.overview.activeUsers24h || 0}
              icon={<Users size={24} />}
              color={COLORS.info}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <StatCard
              title="Alertes Critiques"
              value={dashboard.overview.criticalAlerts}
              icon={<AlertTriangle size={24} />}
              color={COLORS.error}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
            <StatCard
              title="IPs Bloquées"
              value={dashboard.overview.blockedIPs}
              icon={<XCircle size={24} />}
              color={COLORS.warning}
            />
          </Grid>
        </Grid>

        {/* Métriques de surveillance */}
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Logs de Sécurité (24h)"
              value={dashboard.overview.totalSecurityLogs}
              icon={<Activity size={24} />}
              color={COLORS.neutral}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Connexions Échouées"
              value={dashboard.overview.failedLogins}
              icon={<Network size={24} />}
              color="#9c27b0"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Activités Suspectes"
              value={dashboard.overview.suspiciousActivities}
              icon={<AlertOctagon size={24} />}
              color="#e91e63"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Utilisateurs Total"
              value={dashboard.overview.totalUsers || 0}
              icon={<UserCheck size={24} />}
              color={COLORS.secondary}
            />
          </Grid>
        </Grid>
      </Box>
    );
  };

  const renderFilters = () => (
    <Paper sx={{ p: 3, mb: 3, borderRadius: '16px' }}>
      <Box display="flex" flexDirection={{ xs: 'column', md: 'row' }} gap={2} alignItems={{ xs: 'stretch', md: 'center' }} justifyContent="space-between">
        <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} gap={2} flex={1}>
          <StyledFormControl size="small" fullWidth sx={{ maxWidth: { md: 200 } }}>
            <InputLabel>Sévérité</InputLabel>
            <Select
              value={filters.severity}
              onChange={(e) => handleFilterChange('severity', e.target.value)}
              label="Sévérité"
            >
              <MenuItem value="">Toutes</MenuItem>
              <MenuItem value="low">Faible</MenuItem>
              <MenuItem value="medium">Moyenne</MenuItem>
              <MenuItem value="high">Élevée</MenuItem>
              <MenuItem value="critical">Critique</MenuItem>
            </Select>
          </StyledFormControl>
          
          <StyledFormControl size="small" fullWidth sx={{ maxWidth: { md: 200 } }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              label="Type"
            >
              <MenuItem value="">Tous</MenuItem>
              <MenuItem value="LOGIN_FAILURE">Échec connexion</MenuItem>
              <MenuItem value="SUSPICIOUS_ACTIVITY_DETECTED_BRUTE_FORCE">Force brute</MenuItem>
              <MenuItem value="IP_BLOCKED">IP bloquée</MenuItem>
              <MenuItem value="PERMISSION_DENIED">Accès refusé</MenuItem>
            </Select>
          </StyledFormControl>
          
          <TextField
            size="small"
            label="IP"
            value={filters.ip_address}
            onChange={(e) => handleFilterChange('ip_address', e.target.value)}
            fullWidth
            sx={{ maxWidth: { md: 200 } }}
          />
        </Box>
        
        <Box display="flex" flexDirection={{ xs: 'column', sm: 'row' }} gap={2}>
          <Button
            variant="contained"
            startIcon={<Filter size={16} />}
            onClick={loadSecurityLogs}
            fullWidth={isMobile}
            sx={{
              bgcolor: COLORS.primary,
              '&:hover': { bgcolor: COLORS.secondary }
            }}
          >
            Filtrer
          </Button>
          <Button
            variant="outlined"
            onClick={handleResetFilters}
            fullWidth={isMobile}
            sx={{
              color: COLORS.primary,
              borderColor: COLORS.primary,
              '&:hover': { borderColor: COLORS.secondary, color: COLORS.secondary }
            }}
          >
            Réinitialiser
          </Button>
        </Box>
      </Box>
    </Paper>
  );

  const renderSecurityCharts = () => {
    if (!securityStats) return null;

    const severityData = prepareSeverityData();
    const eventTypeData = prepareEventTypeData();
    const timelineData = prepareSecurityTimelineData();

    return (
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <SectionTitle>Distribution par sévérité</SectionTitle>
                <IconBox color={COLORS.primary}>
                  <ShieldAlert size={24} />
                </IconBox>
              </Box>
              <ChartContainer>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={severityData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={isMobile ? 60 : isTablet ? 80 : 100}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={isMobile ? false : ({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {severityData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    {!isMobile && (
                      <Legend 
                        layout="horizontal" 
                        verticalAlign="bottom" 
                        align="center"
                        wrapperStyle={{ fontSize: isTablet ? '12px' : '14px' }}
                      />
                    )}
                    <RechartsTooltip formatter={(value, name) => [`${value} événements`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
              {isMobile && (
                <Box mt={2}>
                  {severityData.map((entry, index) => (
                    <Box key={index} display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Box display="flex" alignItems="center">
                        <Box 
                          width={12} 
                          height={12} 
                          bgcolor={entry.color} 
                          borderRadius="50%" 
                          mr={1} 
                        />
                        <Typography variant="body2">{entry.name}</Typography>
                      </Box>
                      <Typography variant="body2" fontWeight={500}>{entry.value}</Typography>
                    </Box>
                  ))}
                </Box>
              )}
            </StyledPaper>
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <SectionTitle>Types d'événements</SectionTitle>
                <Box display="flex" gap={1} alignItems="center">
                  <StyledFormControl size="small" sx={{ minWidth: isMobile ? 100 : 120 }}>
                    <InputLabel>Période</InputLabel>
                    <Select
                      value={statsPeriod}
                      onChange={(e) => setStatsPeriod(e.target.value)}
                      label="Période"
                    >
                      <MenuItem value="24h">24h</MenuItem>
                      <MenuItem value="7d">7j</MenuItem>
                      <MenuItem value="30d">30j</MenuItem>
                    </Select>
                  </StyledFormControl>
                </Box>
              </Box>
              <ChartContainer>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={eventTypeData}
                    layout="vertical"
                    margin={{
                      top: 5,
                      right: 30,
                      left: isMobile ? 0 : isTablet ? 40 : 40,
                      bottom: 5
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                    <XAxis type="number" />
                    <YAxis
                      type="category"
                      dataKey="name"
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                    />
                    <RechartsTooltip formatter={(value) => [`${value} événements`, 'Nombre']} />
                    <Bar dataKey="value" barSize={isMobile ? 15 : 20}>
                      {eventTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </StyledPaper>
          </Grid>
          <Grid size={{ xs: 12 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <SectionTitle>
                  Évolution temporelle ({statsPeriod === '24h' ? 'par heure' : 'par jour'})
                </SectionTitle>
                <IconBox color={COLORS.info}>
                  <Activity size={24} />
                </IconBox>
              </Box>
              <ChartContainer>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={timelineData}
                    margin={{ 
                      top: 10, 
                      right: isMobile ? 10 : 30, 
                      left: isMobile ? 10 : 0, 
                      bottom: isMobile ? 20 : 0 
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="time" 
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                      interval={isMobile ? 'preserveStartEnd' : 0}
                    />
                    <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
                    <RechartsTooltip />
                    {!isMobile && (
                      <Legend 
                        wrapperStyle={{ fontSize: isTablet ? '12px' : '14px' }}
                      />
                    )}
                    <Area 
                      type="monotone" 
                      dataKey="total" 
                      name="Total" 
                      stackId="1"
                      stroke={COLORS.primary} 
                      fill={`${COLORS.primary}40`} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="high" 
                      name="Haute sévérité" 
                      stackId="2"
                      stroke={COLORS.error} 
                      fill={`${COLORS.error}40`} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="medium" 
                      name="Moyenne sévérité" 
                      stackId="2"
                      stroke={COLORS.warning} 
                      fill={`${COLORS.warning}40`} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="low" 
                      name="Faible sévérité" 
                      stackId="2"
                      stroke={COLORS.success} 
                      fill={`${COLORS.success}40`} 
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
              {isMobile && (
                <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
                  <ChipStyled label="Total" size="small" sx={{ bgcolor: `${COLORS.primary}20`, color: COLORS.primary }} />
                  <ChipStyled label="Haute" size="small" sx={{ bgcolor: `${COLORS.error}20`, color: COLORS.error }} />
                  <ChipStyled label="Moyenne" size="small" sx={{ bgcolor: `${COLORS.warning}20`, color: COLORS.warning }} />
                  <ChipStyled label="Faible" size="small" sx={{ bgcolor: `${COLORS.success}20`, color: COLORS.success }} />
                </Box>
              )}
            </StyledPaper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Section Conformité et Chiffrement
  const renderComplianceSection = () => {
    if (!dashboard) return null;

    const complianceData = [
      { name: 'GDPR', value: dashboard.complianceMetrics?.gdprCompliance || 98, color: COLORS.success },
      { name: 'Rétention', value: dashboard.complianceMetrics?.dataRetentionCompliance || 95, color: COLORS.info },
      { name: 'Contrôle d\'accès', value: dashboard.complianceMetrics?.accessControlCompliance || 97, color: COLORS.primary },
      { name: 'Audit Trail', value: dashboard.complianceMetrics?.auditTrailCompleteness || 99, color: COLORS.warning }
    ];

    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 3, color: '#2D3748', fontWeight: 600 }}>
          🔒 Protection des Données & Conformité
        </Typography>

        <Grid container spacing={3}>
          {/* Métriques de chiffrement */}
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <SectionTitle>Chiffrement des Données</SectionTitle>
                <IconBox color={COLORS.primary}>
                  <Key size={24} />
                </IconBox>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Couverture du chiffrement
                  </Typography>
                  <Typography variant="h6" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                    {dashboard.encryptionStats?.encryptionCoverage || 98}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={dashboard.encryptionStats?.encryptionCoverage || 98}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: `${COLORS.primary}20`,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: COLORS.primary,
                      borderRadius: 4,
                    }
                  }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Champs chiffrés
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {dashboard.encryptionStats?.totalEncryptedFields || 15}
                  </Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  Dernière mise à jour: {dashboard.encryptionStats?.lastEncryptionUpdate || 'Aujourd\'hui'}
                </Typography>
              </Box>

              <Box display="flex" gap={1} flexWrap="wrap">
                <Chip label="AES-256-GCM" size="small" color="primary" />
                <Chip label="Clés dérivées" size="small" color="secondary" />
                <Chip label="Cache Redis" size="small" color="default" />
              </Box>
            </StyledPaper>
          </Grid>

          {/* Métriques de conformité */}
          <Grid size={{ xs: 12, md: 6 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <SectionTitle>Conformité Réglementaire</SectionTitle>
                <IconBox color={COLORS.success}>
                  <ShieldCheck size={24} />
                </IconBox>
              </Box>

              {complianceData.map((item, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      {item.name}
                    </Typography>
                    <Typography variant="body1" sx={{ color: item.color, fontWeight: 600 }}>
                      {item.value}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={item.value}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: `${item.color}20`,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: item.color,
                        borderRadius: 3,
                      }
                    }}
                  />
                </Box>
              ))}

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Score global de conformité
                </Typography>
                <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 700 }}>
                  {Math.round(complianceData.reduce((acc, item) => acc + item.value, 0) / complianceData.length)}%
                </Typography>
              </Box>
            </StyledPaper>
          </Grid>

          {/* Métriques de protection avancées */}
          <Grid size={{ xs: 12 }}>
            <StyledPaper>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <SectionTitle>Protection Avancée des Données</SectionTitle>
                <Box display="flex" gap={1}>
                  <Button
                    size="small"
                    startIcon={<Download size={16} />}
                    variant="outlined"
                    onClick={handleDownloadComplianceReport}
                    disabled={loading}
                    sx={{
                      borderColor: COLORS.primary,
                      color: COLORS.primary,
                      '&:hover': { borderColor: COLORS.secondary, color: COLORS.secondary }
                    }}
                  >
                    Rapport de Conformité
                  </Button>
                  <Button
                    size="small"
                    startIcon={<FileDown size={16} />}
                    variant="contained"
                    onClick={() => setAuditDialog(true)}
                    disabled={loading}
                    sx={{
                      bgcolor: COLORS.primary,
                      '&:hover': { bgcolor: COLORS.secondary }
                    }}
                  >
                    Audit de Sécurité
                  </Button>
                </Box>
              </Box>

              <Grid container spacing={3}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box textAlign="center" p={2}>
                    <Box sx={{
                      backgroundColor: `${COLORS.success}20`,
                      borderRadius: '50%',
                      p: 2,
                      display: 'inline-flex',
                      mb: 1
                    }}>
                      <Database size={32} color={COLORS.success} />
                    </Box>
                    <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 600 }}>
                      Données Sécurisées
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Chiffrement bout en bout
                    </Typography>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box textAlign="center" p={2}>
                    <Box sx={{
                      backgroundColor: `${COLORS.info}20`,
                      borderRadius: '50%',
                      p: 2,
                      display: 'inline-flex',
                      mb: 1
                    }}>
                      <Globe size={32} color={COLORS.info} />
                    </Box>
                    <Typography variant="h6" sx={{ color: COLORS.info, fontWeight: 600 }}>
                      Conformité GDPR
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Respect total des règles
                    </Typography>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box textAlign="center" p={2}>
                    <Box sx={{
                      backgroundColor: `${COLORS.primary}20`,
                      borderRadius: '50%',
                      p: 2,
                      display: 'inline-flex',
                      mb: 1
                    }}>
                      <FileText size={32} color={COLORS.primary} />
                    </Box>
                    <Typography variant="h6" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                      Audit Trail
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Traçabilité complète
                    </Typography>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Box textAlign="center" p={2}>
                    <Box sx={{
                      backgroundColor: `${COLORS.warning}20`,
                      borderRadius: '50%',
                      p: 2,
                      display: 'inline-flex',
                      mb: 1
                    }}>
                      <Zap size={32} color={COLORS.warning} />
                    </Box>
                    <Typography variant="h6" sx={{ color: COLORS.warning, fontWeight: 600 }}>
                      Surveillance 24/7
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Monitoring en temps réel
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </StyledPaper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  if (loading && !dashboard) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ p: 3 }}>
      <Helmet>
        <title>Sécurité & Protection des Données - JobPartiel Admin</title>
      </Helmet>

      <Box
        display="flex"
        justifyContent="space-between"
        mb={3}
        flexDirection={{ xs: 'column', sm: 'row' }}
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        gap={2}
      >
        <Box>
          <PageTitle>
            🛡️ Sécurité & Protection des Données
          </PageTitle>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            Surveillance complète, chiffrement avancé et conformité GDPR
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<RefreshCw size={18} />}
          onClick={handleRefresh}
          fullWidth={isMobile}
          sx={{
            bgcolor: COLORS.primary,
            '&:hover': { bgcolor: COLORS.secondary }
          }}
        >
          Actualiser
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3, borderRadius: '12px' }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {renderOverview()}

      {renderFilters()}

      {renderSecurityCharts()}

      {/* Section Conformité et Chiffrement */}
      {renderComplianceSection()}

      {/* Onglets */}
      <Paper sx={{ mb: 3, borderRadius: '16px', overflow: 'hidden' }}>
        <ResponsiveTabs
          value={currentTab}
          onChange={(_, newValue) => setCurrentTab(newValue)}
          variant={isMobile ? "scrollable" : "standard"}
          scrollButtons={isMobile ? "auto" : false}
          allowScrollButtonsMobile={isMobile}
        >
          <Tab 
            label={isMobile ? "Logs" : "Logs de Sécurité"}
            icon={<Shield size={isMobile ? 16 : 18} />}
            iconPosition="start"
          />
          <Tab 
            label={isMobile ? "IPs" : "IPs Bloquées"}
            icon={<XCircle size={isMobile ? 16 : 18} />}
            iconPosition="start" 
          />
          <Tab 
            label={isMobile ? "Alertes" : "Alertes Récentes"}
            icon={<AlertTriangle size={isMobile ? 16 : 18} />}
            iconPosition="start"
          />
          <Tab
            label={isMobile ? "Config" : "Configuration"}
            icon={<Lock size={isMobile ? 16 : 18} />}
            iconPosition="start"
          />
          <Tab
            label={isMobile ? "Perf" : "Performance"}
            icon={<TrendingUp size={isMobile ? 16 : 18} />}
            iconPosition="start"
          />
        </ResponsiveTabs>
      </Paper>

      {/* Contenu des onglets */}
      {currentTab === 0 && (
        <StyledPaper>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <SectionTitle>Logs de Sécurité</SectionTitle>
          </Box>

          <StyledTableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Sévérité</TableCell>
                  <TableCell>Type</TableCell>
                  {!isMobile && <TableCell>Message</TableCell>}
                  {!isTablet && <TableCell>IP</TableCell>}
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {securityLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <ChipStyled
                        icon={getSeverityIcon(log.severity, isMobile ? 12 : 16)}
                        label={isMobile ? formatSeverity(log.severity).charAt(0) : formatSeverity(log.severity)}
                        size="small"
                        color={
                          log.severity === 'critical' ? 'error' :
                          log.severity === 'high' ? 'warning' :
                          log.severity === 'medium' ? 'info' : 'success'
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant={isMobile ? "caption" : "body2"} noWrap>
                        {isMobile ? formatEventType(log.type).substring(0, 15) + '...' : formatEventType(log.type)}
                      </Typography>
                    </TableCell>
                    {!isMobile && (
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {log.message || log.details?.message || log.type}
                        </Typography>
                      </TableCell>
                    )}
                    {!isTablet && (
                      <TableCell>
                        <Typography variant="body2">
                          {log.ip_address || '-'}
                        </Typography>
                      </TableCell>
                    )}
                    <TableCell>
                      <Typography variant={isMobile ? "caption" : "body2"}>
                        {isMobile ? 
                          new Date(log.created_at).toLocaleDateString('fr-FR') : 
                          formatDate(log.created_at)
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Voir les détails">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedLog(log);
                            setDetailsOpen(true);
                          }}
                        >
                          <Eye size={isMobile ? 16 : 18} />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </StyledTableContainer>

          <Box display="flex" justifyContent="center" mt={3}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={(_, newPage) => setPage(newPage)}
              color="primary"
              size={isMobile ? "small" : "medium"}
              sx={{
                '& .MuiPaginationItem-root.Mui-selected': {
                  backgroundColor: COLORS.primary,
                  color: 'white'
                }
              }}
            />
          </Box>
        </StyledPaper>
      )}

      {currentTab === 1 && (
        <StyledPaper>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <SectionTitle>IPs Bloquées ({blockedIPs.length})</SectionTitle>
            <Button
              variant="contained"
              startIcon={<XCircle size={18} />}
              onClick={() => setBlockIPDialog(true)}
              disabled={loading}
              sx={{
                bgcolor: COLORS.error,
                '&:hover': { bgcolor: '#d32f2f' }
              }}
            >
              Bloquer une IP
            </Button>
          </Box>
          <StyledTableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Adresse IP</TableCell>
                  {!isMobile && <TableCell>Type</TableCell>}
                  {!isMobile && <TableCell>Expire le</TableCell>}
                  <TableCell>Temps restant</TableCell>
                  {!isMobile && <TableCell>Raison</TableCell>}
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {blockedIPs.map((blockedIP) => (
                  <TableRow key={blockedIP.ip}>
                    <TableCell>
                      <ChipStyled
                        label={blockedIP.ip}
                        color="error"
                        variant="outlined"
                        size={isMobile ? "small" : "medium"}
                      />
                    </TableCell>
                    {!isMobile && (
                      <TableCell>
                        <Chip
                          label={getBlockTypeLabel(blockedIP.type)}
                          size="small"
                          sx={{
                            backgroundColor: `${getBlockTypeColor(blockedIP.type)}20`,
                            color: getBlockTypeColor(blockedIP.type),
                            fontWeight: 500
                          }}
                        />
                      </TableCell>
                    )}
                    {!isMobile && (
                      <TableCell>
                        <Typography variant="body2">
                          {blockedIP.expiresAt ? formatDate(blockedIP.expiresAt) : 'Permanent'}
                        </Typography>
                      </TableCell>
                    )}
                    <TableCell>
                      <Typography variant="body2" sx={{
                        color: blockedIP.remainingTime === -1 ? COLORS.error : COLORS.neutral,
                        fontWeight: blockedIP.remainingTime === -1 ? 600 : 400
                      }}>
                        {formatRemainingTime(blockedIP.remainingTime)}
                      </Typography>
                    </TableCell>
                    {!isMobile && (
                      <TableCell>
                        <Typography variant="body2" color="text.secondary" sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {blockedIP.reason}
                        </Typography>
                      </TableCell>
                    )}
                    <TableCell>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        startIcon={<Trash2 size={isMobile ? 14 : 16} />}
                        onClick={() => unblockIP(blockedIP.ip)}
                        sx={{
                          borderColor: COLORS.primary,
                          color: COLORS.primary,
                          fontSize: isMobile ? '0.7rem' : '0.875rem',
                          '&:hover': {
                            borderColor: COLORS.secondary,
                            color: COLORS.secondary,
                            bgcolor: `${COLORS.primary}10`
                          }
                        }}
                      >
                        {isMobile ? 'Débloquer' : 'Débloquer'}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </StyledTableContainer>
          {blockedIPs.length === 0 && (
            <Box textAlign="center" py={4}>
              <Typography color="text.secondary">
                Aucune IP bloquée actuellement
              </Typography>
            </Box>
          )}
        </StyledPaper>
      )}

      {currentTab === 2 && dashboard && (
        <StyledPaper>
          <SectionTitle>Alertes Récentes</SectionTitle>
          {dashboard.recentAlerts.map((alert) => (
            <Alert
              key={alert.id}
              severity={alert.severity === 'critical' ? 'error' : 'warning'}
              sx={{ 
                mb: 2, 
                borderRadius: '12px',
                '& .MuiAlert-icon': {
                  color: getSeverityColor(alert.severity)
                }
              }}
              action={
                <Button
                  size="small"
                  onClick={() => {
                    setSelectedLog(alert);
                    setDetailsOpen(true);
                  }}
                  sx={{
                    color: COLORS.primary,
                    '&:hover': {
                      bgcolor: `${COLORS.primary}10`
                    }
                  }}
                >
                  Détails
                </Button>
              }
            >
              <Typography variant="body2" fontWeight={500}>
                <strong>{formatEventType(alert.type)}</strong> - {alert.message || alert.details?.message || 'Événement de sécurité'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatDate(alert.created_at)} - IP: {alert.ip_address || 'N/A'}
              </Typography>
            </Alert>
          ))}
          {dashboard.recentAlerts.length === 0 && (
            <Box textAlign="center" py={4}>
              <Typography color="text.secondary">
                Aucune alerte récente
              </Typography>
            </Box>
          )}
        </StyledPaper>
      )}

      {currentTab === 3 && (
        <Box>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 6 }}>
              <StyledPaper>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <SectionTitle>Top 10 IPs Suspectes</SectionTitle>
                  <IconBox color={COLORS.warning}>
                    <Server size={24} />
                  </IconBox>
                </Box>
                <StyledTableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>IP</TableCell>
                        <TableCell align="right">Événements</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboard?.topSuspiciousIPs.map((item) => (
                        <TableRow key={item.ip}>
                          <TableCell>{item.ip}</TableCell>
                          <TableCell align="right">
                            <ChipStyled
                              label={item.count}
                              size="small"
                              color={item.count > 10 ? 'error' : 'warning'}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                      {(!dashboard?.topSuspiciousIPs || dashboard.topSuspiciousIPs.length === 0) && (
                        <TableRow>
                          <TableCell colSpan={2} align="center">
                            <Typography color="text.secondary" variant="body2">
                              Aucune IP suspecte détectée
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </StyledTableContainer>
              </StyledPaper>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <StyledPaper>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <SectionTitle>Configuration de Sécurité</SectionTitle>
                  <IconBox color={COLORS.success}>
                    <Lock size={24} />
                  </IconBox>
                </Box>
                <Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Rate Limiting
                    </Typography>
                    <ChipStyled label="Activé" color="success" size="small" />
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Protection CSRF
                    </Typography>
                    <ChipStyled label="Activé" color="success" size="small" />
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Protection Brute Force
                    </Typography>
                    <ChipStyled label="Activé" color="success" size="small" />
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Headers de Sécurité
                    </Typography>
                    <ChipStyled label="Activé" color="success" size="small" />
                  </Box>
                </Box>
              </StyledPaper>
            </Grid>
          </Grid>
        </Box>
      )}

      {currentTab === 4 && (
        <Box>
          <Grid container spacing={3}>
            {/* Métriques de performance en temps réel */}
            <Grid size={{ xs: 12, md: 6 }}>
              <StyledPaper>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <SectionTitle>Performance du Système</SectionTitle>
                  <IconBox color={COLORS.info}>
                    <TrendingUp size={24} />
                  </IconBox>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Temps de réponse API (ms)
                    </Typography>
                    <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 600 }}>
                      28ms
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={85}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: `${COLORS.success}20`,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: COLORS.success,
                        borderRadius: 3,
                      }
                    }}
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Utilisation CPU (%)
                    </Typography>
                    <Typography variant="h6" sx={{ color: COLORS.warning, fontWeight: 600 }}>
                      23%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={23}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: `${COLORS.warning}20`,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: COLORS.warning,
                        borderRadius: 3,
                      }
                    }}
                  />
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Utilisation Mémoire (%)
                    </Typography>
                    <Typography variant="h6" sx={{ color: COLORS.info, fontWeight: 600 }}>
                      67%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={67}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: `${COLORS.info}20`,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: COLORS.info,
                        borderRadius: 3,
                      }
                    }}
                  />
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  <Chip label="Serveur Stable" size="small" color="success" />
                  <Chip label="Redis Actif" size="small" color="primary" />
                  <Chip label="DB Optimisée" size="small" color="info" />
                </Box>
              </StyledPaper>
            </Grid>

            {/* Métriques de sécurité avancées */}
            <Grid size={{ xs: 12, md: 6 }}>
              <StyledPaper>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <SectionTitle>Sécurité Avancée</SectionTitle>
                  <IconBox color={COLORS.primary}>
                    <ShieldAlert size={24} />
                  </IconBox>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Chiffrement AES-256
                    </Typography>
                    <CheckCircle size={20} color={COLORS.success} />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Protection CSRF
                    </Typography>
                    <CheckCircle size={20} color={COLORS.success} />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Rate Limiting
                    </Typography>
                    <CheckCircle size={20} color={COLORS.success} />
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" color="text.secondary">
                      Surveillance 24/7
                    </Typography>
                    <CheckCircle size={20} color={COLORS.success} />
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2" color="text.secondary">
                    Score de Sécurité Global
                  </Typography>
                  <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 700 }}>
                    98/100
                  </Typography>
                </Box>
              </StyledPaper>
            </Grid>

            {/* Statistiques de surveillance */}
            <Grid size={{ xs: 12 }}>
              <StyledPaper>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <SectionTitle>Surveillance & Monitoring</SectionTitle>
                  <Box display="flex" gap={1}>
                    <Badge badgeContent="Live" color="success">
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Activity size={16} />}
                        sx={{
                          borderColor: COLORS.success,
                          color: COLORS.success,
                          '&:hover': { borderColor: COLORS.success, bgcolor: `${COLORS.success}10` }
                        }}
                      >
                        Temps Réel
                      </Button>
                    </Badge>
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box textAlign="center" p={2}>
                      <Box sx={{
                        backgroundColor: `${COLORS.primary}20`,
                        borderRadius: '50%',
                        p: 2,
                        display: 'inline-flex',
                        mb: 1
                      }}>
                        <Clock size={32} color={COLORS.primary} />
                      </Box>
                      <Typography variant="h6" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                        Uptime: 99.9%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Disponibilité système
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box textAlign="center" p={2}>
                      <Box sx={{
                        backgroundColor: `${COLORS.success}20`,
                        borderRadius: '50%',
                        p: 2,
                        display: 'inline-flex',
                        mb: 1
                      }}>
                        <BarChart3 size={32} color={COLORS.success} />
                      </Box>
                      <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 600 }}>
                        {dashboard?.overview.totalSecurityLogs || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Événements surveillés
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box textAlign="center" p={2}>
                      <Box sx={{
                        backgroundColor: `${COLORS.info}20`,
                        borderRadius: '50%',
                        p: 2,
                        display: 'inline-flex',
                        mb: 1
                      }}>
                        <PieChartIcon size={32} color={COLORS.info} />
                      </Box>
                      <Typography variant="h6" sx={{ color: COLORS.info, fontWeight: 600 }}>
                        Redis Cache
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Performance optimisée
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box textAlign="center" p={2}>
                      <Box sx={{
                        backgroundColor: `${COLORS.warning}20`,
                        borderRadius: '50%',
                        p: 2,
                        display: 'inline-flex',
                        mb: 1
                      }}>
                        <AlertCircle size={32} color={COLORS.warning} />
                      </Box>
                      <Typography variant="h6" sx={{ color: COLORS.warning, fontWeight: 600 }}>
                        {dashboard?.overview.criticalAlerts || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Alertes critiques
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </StyledPaper>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Dialog des détails */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: '16px',
              boxShadow: '0 8px 25px 0 rgba(0,0,0,0.1)'
            }
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: `1px solid ${COLORS.borderColor}` }}>
          <Box display="flex" alignItems="center" gap={1}>
            {selectedLog && getSeverityIcon(selectedLog.severity)}
            <Typography variant="h6">
              Détails de l'événement de sécurité
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          {selectedLog && (
            <Box>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Type:</Typography>
                  <Typography variant="body1" fontWeight={500}>{formatEventType(selectedLog.type)}</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Sévérité:</Typography>
                  <ChipStyled
                    label={formatSeverity(selectedLog.severity)}
                    size="small"
                    color={
                      selectedLog.severity === 'critical' ? 'error' :
                      selectedLog.severity === 'high' ? 'warning' :
                      selectedLog.severity === 'medium' ? 'info' : 'success'
                    }
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="body2" color="text.secondary">Message:</Typography>
                  <Typography variant="body1">{selectedLog.message || selectedLog.details?.message || 'Aucun message disponible'}</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">IP:</Typography>
                  <Typography variant="body1">{selectedLog.ip_address || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Date:</Typography>
                  <Typography variant="body1">{formatDate(selectedLog.created_at)}</Typography>
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="body2" color="text.secondary">User Agent:</Typography>
                  <Typography variant="body1" sx={{ wordBreak: 'break-all' }}>
                    {selectedLog.user_agent || 'N/A'}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="body2" color="text.secondary">Détails:</Typography>
                  <Box
                    component="pre"
                    sx={{
                      bgcolor: COLORS.lightGray,
                      p: 2,
                      borderRadius: '8px',
                      overflow: 'auto',
                      fontSize: '0.875rem'
                    }}
                  >
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ borderTop: `1px solid ${COLORS.borderColor}`, px: 3, py: 2 }}>
          <Button 
            onClick={() => setDetailsOpen(false)}
            sx={{
              color: COLORS.primary,
              '&:hover': {
                bgcolor: `${COLORS.primary}10`
              }
            }}
          >
            Fermer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal pour bloquer une IP */}
      <Dialog
        open={blockIPDialog}
        onClose={() => setBlockIPDialog(false)}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: '16px',
              boxShadow: '0 8px 25px 0 rgba(0,0,0,0.1)'
            }
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: `1px solid ${COLORS.borderColor}` }}>
          <Box display="flex" alignItems="center" gap={1}>
            <XCircle size={24} color={COLORS.error} />
            <Typography variant="h6">
              Bloquer une adresse IP
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <TextField
              label="Adresse IP"
              value={blockIPForm.ip}
              onChange={(e) => setBlockIPForm(prev => ({ ...prev, ip: e.target.value }))}
              placeholder="***********"
              fullWidth
              required
            />

            <FormControl fullWidth>
              <InputLabel>Type de durée</InputLabel>
              <Select
                value={blockIPForm.durationType}
                onChange={(e) => setBlockIPForm(prev => ({ ...prev, durationType: e.target.value }))}
                label="Type de durée"
              >
                <MenuItem value="hours">Heures</MenuItem>
                <MenuItem value="days">Jours</MenuItem>
                <MenuItem value="permanent">Permanent</MenuItem>
              </Select>
            </FormControl>

            {blockIPForm.durationType !== 'permanent' && (
              <TextField
                label={`Durée (${blockIPForm.durationType === 'hours' ? 'heures' : 'jours'})`}
                type="number"
                value={blockIPForm.duration}
                onChange={(e) => setBlockIPForm(prev => ({ ...prev, duration: parseInt(e.target.value) || 1 }))}
                slotProps={{
                  htmlInput: { min: 1, max: blockIPForm.durationType === 'hours' ? 168 : 365 }
                }}
                fullWidth
                required
              />
            )}

            <TextField
              label="Raison du blocage"
              value={blockIPForm.reason}
              onChange={(e) => setBlockIPForm(prev => ({ ...prev, reason: e.target.value }))}
              placeholder="Activité suspecte détectée..."
              multiline
              rows={3}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: `1px solid ${COLORS.borderColor}` }}>
          <Button
            onClick={() => setBlockIPDialog(false)}
            disabled={loading}
            sx={{ color: COLORS.neutral }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleBlockIP}
            disabled={loading || !blockIPForm.ip}
            variant="contained"
            sx={{
              bgcolor: COLORS.error,
              '&:hover': { bgcolor: '#d32f2f' }
            }}
          >
            {loading ? <CircularProgress size={20} /> : 'Bloquer IP'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal pour l'audit de sécurité */}
      <Dialog
        open={auditDialog}
        onClose={() => !auditRunning && setAuditDialog(false)}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: '16px',
              boxShadow: '0 8px 25px 0 rgba(0,0,0,0.1)'
            }
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: `1px solid ${COLORS.borderColor}` }}>
          <Box display="flex" alignItems="center" gap={1}>
            <ShieldAlert size={24} color={COLORS.primary} />
            <Typography variant="h6">
              Audit de Sécurité Complet
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          {!auditRunning ? (
            <Box textAlign="center" py={4}>
              <Box sx={{
                backgroundColor: `${COLORS.primary}20`,
                borderRadius: '50%',
                p: 3,
                display: 'inline-flex',
                mb: 3
              }}>
                <ShieldCheck size={48} color={COLORS.primary} />
              </Box>
              <Typography variant="h6" sx={{ mb: 2, color: COLORS.primary }}>
                Prêt à effectuer un audit de sécurité complet
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Cet audit va scanner et vérifier tous les aspects de sécurité de la plateforme :
                chiffrement, conformité GDPR, protections actives, et bien plus.
              </Typography>
              <Box display="flex" gap={1} justifyContent="center" flexWrap="wrap">
                <Chip label="Chiffrement AES-256" size="small" color="primary" />
                <Chip label="Conformité GDPR" size="small" color="success" />
                <Chip label="Protection CSRF" size="small" color="info" />
                <Chip label="Rate Limiting" size="small" color="warning" />
              </Box>
            </Box>
          ) : (
            <Box>
              <Box textAlign="center" mb={4}>
                <Typography variant="h6" sx={{ mb: 2, color: COLORS.primary }}>
                  🔍 Audit en cours...
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={auditProgress}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: `${COLORS.primary}20`,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: COLORS.primary,
                      borderRadius: 4,
                    }
                  }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {Math.round(auditProgress)}% terminé
                </Typography>
              </Box>

              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                {auditSteps.map((step, index) => (
                  <Box key={index} display="flex" alignItems="center" gap={2} mb={2}>
                    <CheckCircle size={20} color={COLORS.success} />
                    <Typography variant="body2" sx={{ flex: 1 }}>
                      {step.step}
                    </Typography>
                    <Chip
                      label={`${step.score}%`}
                      size="small"
                      color={step.score >= 95 ? 'success' : step.score >= 85 ? 'warning' : 'error'}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: `1px solid ${COLORS.borderColor}` }}>
          {!auditRunning ? (
            <>
              <Button
                onClick={() => setAuditDialog(false)}
                sx={{ color: COLORS.neutral }}
              >
                Annuler
              </Button>
              <Button
                onClick={handleSecurityAudit}
                variant="contained"
                startIcon={<Zap size={18} />}
                sx={{
                  bgcolor: COLORS.primary,
                  '&:hover': { bgcolor: COLORS.secondary }
                }}
              >
                Démarrer l'Audit
              </Button>
            </>
          ) : (
            <Box display="flex" alignItems="center" gap={2}>
              <CircularProgress size={20} />
              <Typography variant="body2" color="text.secondary">
                Audit en cours, veuillez patienter...
              </Typography>
            </Box>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SecurityMonitoring;
