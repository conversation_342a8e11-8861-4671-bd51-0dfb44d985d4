import { Request, Response, NextFunction } from 'express';
import { UploadedFile } from 'express-fileupload';
import logger from '../utils/logger';

// Extend Request type to include express-fileupload files
export interface FileRequest extends Request {
  files?: {
    [key: string]: UploadedFile | UploadedFile[];
  } | null;
}

// Middleware pour valider les fichiers uploadés pour les photos mises en avant et les galeries
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  // Si pas de fichier, on continue simplement
  if (!req.files) {
    logger.warn('No file uploaded');
    return next();
  }

  // Fonction de validation pour un seul fichier
  const validateSingleFile = (file: UploadedFile) => {
    if (!file.mimetype.startsWith('image/')) {
      return {
        isValid: false,
        message: 'Seules les images sont autorisées'
      };
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      return {
        isValid: false,
        message: 'La taille du fichier ne doit pas dépasser 5MB'
      };
    }

    return { isValid: true };
  };

  // Valider chaque fichier
  for (const key in req.files) {
    const file = req.files[key];
    
    if (Array.isArray(file)) {
      // Valider chaque fichier dans le tableau
      for (const singleFile of file) {
        const validation = validateSingleFile(singleFile);
        if (!validation.isValid) {
          res.status(400).json({
            message: validation.message,
            success: false
          });
        }
      }
    } else {
      // Valider un fichier unique
      const validation = validateSingleFile(file);
      if (!validation.isValid) {
        res.status(400).json({
          message: validation.message,
          success: false
        });
      }
    }
  }

  next();
};
