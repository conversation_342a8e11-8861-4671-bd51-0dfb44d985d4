import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Container, Paper, Typography, TextField, Button, Grid, Box, Divider, Alert, CircularProgress, Snackbar } from '@mui/material';
import { styled } from '@mui/material/styles';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { invoiceService } from '../services/invoiceService';
import { useAuth } from '../contexts/AuthContext';
import { API_CONFIG } from '../config/api';
import logger from '@/utils/logger';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(4),
  boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)',
  borderRadius: '16px',
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  backdropFilter: 'blur(10px)',
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: '0px 15px 35px rgba(0, 0, 0, 0.15)',
    transform: 'translateY(-3px)',
  }
}));

const AcceptanceHeader = styled(Box)(({ theme }) => ({
  backgroundColor: '#FF7A35',
  padding: theme.spacing(3),
  marginBottom: theme.spacing(4),
  borderRadius: '8px',
  color: 'white',
}));

const QuoteAcceptance: React.FC = () => {
  const { quoteId } = useParams<{ quoteId: string }>();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const [quoteDetails, setQuoteDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showAuthMessage, setShowAuthMessage] = useState(false);
  const [countdown, setCountdown] = useState(7);
  const [formData, setFormData] = useState({
    clientName: '',
    clientAddress: '',
    clientEmail: '',
    clientPhone: '',
    signature: '',
    dataConsent: false,
  });

  useEffect(() => {
    const fetchQuoteDetails = async () => {
      if (!quoteId) {
        setError('Identifiant de devis manquant');
        setLoading(false);
        return;
      }

      try {
        const response = await invoiceService.getDocumentById(quoteId);

        // Vérifier si c'est bien un devis et non déjà accepté
        if (response.type !== 'devis') {
          setError('Ce document n\'est pas un devis');
          setLoading(false);
          return;
        }

        if (response.statut === 'accepte' || response.statut === 'facture') {
          setError('Ce devis a déjà été accepté');
          setLoading(false);
          return;
        }

        setQuoteDetails(response);

        // Pré-remplir avec les données client existantes si disponibles
        if (response.client_name) {
          setFormData(prev => ({
            ...prev,
            clientName: response.client_name,
            clientEmail: response.client_email || '',
            clientAddress: response.client_address || '',
            clientPhone: response.client_phone || '',
            signature: response.client_name,
          }));
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Erreur lors de la récupération des détails du devis:', err);

        // Si erreur 401 ou 403, c'est que l'utilisateur n'est pas connecté ou n'a pas accès
        if (err.status === 401 || err.response?.status === 401 || err.status === 403 || err.response?.status === 403) {
          setShowAuthMessage(true);
          setLoading(false);
          return;
        } else {
          setError('Impossible de récupérer les détails du devis. Veuillez vérifier le lien ou contacter l\'expéditeur.');
        }
        setLoading(false);
      }
    };

    fetchQuoteDetails();
  }, [quoteId]);

  // Effet pour pré-remplir les données utilisateur quand il est connecté
  useEffect(() => {
    if (!authLoading && isAuthenticated && user && quoteDetails) {
      logger.info('Pré-remplissage des données utilisateur:', user);

      // Récupérer les données complètes du profil via l'API
      const fetchUserProfile = async () => {
        try {
          const response = await fetch(`${API_CONFIG.baseURL}/api/users/profil`, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            }
          });

          if (response.ok) {
            const profileData = await response.json();
            logger.info('Données complètes du profil récupérées:', profileData);

            // Extraire les données du profil
            const userWithProfile = user as any;
            const prenom = profileData.profil?.data?.prenom || userWithProfile.prenom || '';
            const nom = profileData.profil?.data?.nom || userWithProfile.nom || '';
            const telephone = profileData.profil?.data?.telephone || '';
            const adresse = profileData.profil?.data?.adresse || '';
            const ville = profileData.profil?.data?.ville || '';
            const code_postal = profileData.profil?.data?.code_postal || '';

            const fullName = prenom && nom ? `${prenom} ${nom}` : '';
            const fullAddress = adresse && ville ? `${adresse}, ${code_postal} ${ville}` : adresse || '';

            logger.info('Données extraites du profil complet:', {
              prenom, nom, fullName,
              telephone, adresse, ville, code_postal, fullAddress,
              email: user.email
            });

            setFormData(prev => ({
              ...prev,
              clientName: fullName,
              clientEmail: user.email || '',
              clientAddress: fullAddress,
              clientPhone: telephone,
              signature: fullName,
            }));
          } else {
            console.error('Erreur lors de la récupération du profil complet:', response.status);
            // Fallback sur les données de base
            const userWithProfile = user as any;
            const prenom = userWithProfile.prenom || '';
            const nom = userWithProfile.nom || '';
            const fullName = prenom && nom ? `${prenom} ${nom}` : '';

            setFormData(prev => ({
              ...prev,
              clientName: fullName,
              clientEmail: user.email || '',
              signature: fullName,
            }));
          }
        } catch (error) {
          console.error('Erreur lors de la récupération du profil complet:', error);
          // Fallback sur les données de base
          const userWithProfile = user as any;
          const prenom = userWithProfile.prenom || '';
          const nom = userWithProfile.nom || '';
          const fullName = prenom && nom ? `${prenom} ${nom}` : '';

          setFormData(prev => ({
            ...prev,
            clientName: fullName,
            clientEmail: user.email || '',
            signature: fullName,
          }));
        }
      };

      fetchUserProfile();
    }
  }, [authLoading, isAuthenticated, user, quoteDetails]);

  // Effet pour gérer le countdown d'authentification
  useEffect(() => {
    if (showAuthMessage) {
      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdownInterval);
    }
  }, [showAuthMessage]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.clientName || !formData.clientEmail || !formData.signature) {
      setError('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (!formData.dataConsent) {
      setError('Veuillez confirmer votre consentement au partage des données');
      return;
    }

    setLoading(true);
    
    try {
      await invoiceService.acceptQuote(quoteId || '', formData);
      setSuccess(true);
      setError(null);
    } catch (err: any) {
      console.error('Erreur lors de l\'acceptation du devis:', err);
      setError(err.message || 'Une erreur est survenue lors de l\'acceptation du devis');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError(null);
  };

  useEffect(() => {
    // Ajouter le style d'animation au document
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(21, 128, 61, 0.4);
        }
        70% {
          box-shadow: 0 0 0 15px rgba(21, 128, 61, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(21, 128, 61, 0);
        }
      }
    `;
    document.head.appendChild(style);

    // Nettoyage lors du démontage du composant
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  if (loading && !quoteDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-12 px-4 sm:px-6 lg:px-8">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <CircularProgress sx={{ color: '#FF7A35' }} />
        </Box>
      </div>
    );
  }

  // Affichage du message d'authentification centré
  if (showAuthMessage) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-12 px-4 sm:px-6 lg:px-8">
        <Container maxWidth="md">
          <Paper
            elevation={3}
            sx={{
              p: 4,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #FFF8F3 0%, #FFE4BA 100%)',
              border: '2px solid #FF7A35',
              textAlign: 'center'
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h4"
                sx={{
                  color: '#FF6B2C',
                  fontWeight: 'bold',
                  mb: 2
                }}
              >
                🔐 Authentification requise
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#333',
                  mb: 3,
                  lineHeight: 1.6
                }}
              >
                Vous devez être connecté avec l'adresse email qui a reçu ce devis pour pouvoir l'accepter.
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: '#666',
                  mb: 3
                }}
              >
                Redirection automatique dans <strong style={{ color: '#FF6B2C', fontSize: '1.2em' }}>{countdown}</strong> seconde{countdown > 1 ? 's' : ''}...
              </Typography>
              <Box sx={{ mt: 3 }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname)}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    '&:hover': { backgroundColor: '#FF5722' },
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem'
                  }}
                >
                  Se connecter maintenant
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-12 px-4 sm:px-6 lg:px-8">
        <Container maxWidth="md">
          <StyledPaper 
            sx={{ 
              position: 'relative',
              overflow: 'hidden',
              borderTop: '4px solid #15803D',
              boxShadow: '0 10px 25px rgba(21, 128, 61, 0.15)',
              py: 6
            }}
          >
            <Box 
              sx={{ 
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '100%',
                background: 'radial-gradient(circle at top right, rgba(21, 128, 61, 0.08), transparent 60%)',
                zIndex: 0
              }}
            />
            <Box 
              sx={{ 
                textAlign: 'center', 
                position: 'relative', 
                zIndex: 1,
                px: { xs: 2, sm: 4 }
              }}
            >
              <Box 
                sx={{ 
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Box 
                  sx={{ 
                    bgcolor: 'rgba(21, 128, 61, 0.1)',
                    borderRadius: '50%',
                    p: 2,
                    mb: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    animation: 'pulse 2s infinite ease-in-out'
                  }}
                >
                  <CheckCircleOutlineIcon 
                    sx={{ 
                      fontSize: { xs: 60, sm: 80 }, 
                      color: '#15803D',
                    }} 
                  />
                </Box>
                <Typography 
                  variant="h4" 
                  component="h1"
                  gutterBottom
                  sx={{ 
                    fontWeight: 700,
                    color: '#15803D',
                    mb: 3,
                    fontSize: { xs: '1.5rem', sm: '2rem' }
                  }}
                >
                  Devis accepté avec succès !
                </Typography>
                <Typography 
                  variant="body1" 
                  paragraph
                  sx={{ 
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    maxWidth: '500px',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  Merci d'avoir accepté le devis. Une confirmation a été envoyée par email.
                </Typography>
                <Box 
                  sx={{ 
                    bgcolor: 'rgba(21, 128, 61, 0.05)',
                    border: '1px solid rgba(21, 128, 61, 0.2)',
                    borderRadius: '12px',
                    p: 3,
                    mb: 3,
                    maxWidth: '500px',
                    mx: 'auto'
                  }}
                >
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: '#2D3748',
                      fontWeight: 500,
                      lineHeight: 1.6
                    }}
                  >
                    L'expéditeur du devis va être notifié de votre acceptation et vous contactera prochainement.
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#FF7A35',
                    '&:hover': { bgcolor: '#FF6B2C' },
                    borderRadius: '8px',
                    px: 4,
                    py: 1.2,
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '1rem',
                    boxShadow: '0 4px 10px rgba(255, 122, 53, 0.25)',
                  }}
                  onClick={() => window.location.href = '/dashboard/'}
                >
                  Retourner à l'accueil
                </Button>
              </Box>
            </Box>
          </StyledPaper>
        </Container>
      </div>
    );
  }

  if (error && error.includes('déjà été accepté')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-12 px-4 sm:px-6 lg:px-8">
        <Container maxWidth="md">
          <StyledPaper 
            sx={{ 
              position: 'relative',
              overflow: 'hidden',
              borderTop: '4px solid #15803D',
              boxShadow: '0 10px 25px rgba(21, 128, 61, 0.15)',
              py: 6
            }}
          >
            <Box 
              sx={{ 
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '100%',
                background: 'radial-gradient(circle at top right, rgba(21, 128, 61, 0.08), transparent 60%)',
                zIndex: 0
              }}
            />
            <Box 
              sx={{ 
                textAlign: 'center', 
                position: 'relative', 
                zIndex: 1,
                px: { xs: 2, sm: 4 }
              }}
            >
              <Box 
                sx={{ 
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Box 
                  sx={{ 
                    bgcolor: 'rgba(21, 128, 61, 0.1)',
                    borderRadius: '50%',
                    p: 2,
                    mb: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <CheckCircleOutlineIcon 
                    sx={{ 
                      fontSize: { xs: 60, sm: 80 }, 
                      color: '#15803D',
                    }} 
                  />
                </Box>
                <Typography 
                  variant="h4" 
                  component="h1"
                  gutterBottom
                  sx={{ 
                    fontWeight: 700,
                    color: '#15803D',
                    mb: 3,
                    fontSize: { xs: '1.5rem', sm: '2rem' }
                  }}
                >
                  Ce devis a déjà été accepté
                </Typography>
                <Typography 
                  variant="body1" 
                  paragraph
                  sx={{ 
                    fontSize: { xs: '1rem', sm: '1.1rem' },
                    maxWidth: '500px',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  Vous avez déjà accepté ce devis précédemment.
                </Typography>
                <Box 
                  sx={{ 
                    bgcolor: 'rgba(21, 128, 61, 0.05)',
                    border: '1px solid rgba(21, 128, 61, 0.2)',
                    borderRadius: '12px',
                    p: 3,
                    mb: 3,
                    maxWidth: '500px',
                    mx: 'auto'
                  }}
                >
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: '#2D3748',
                      fontWeight: 500,
                      lineHeight: 1.6
                    }}
                  >
                    Si vous avez des questions, veuillez contacter directement l'émetteur du devis.
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: '#FF7A35',
                    '&:hover': { bgcolor: '#FF6B2C' },
                    borderRadius: '8px',
                    px: 4,
                    py: 1.2,
                    fontWeight: 600,
                    textTransform: 'none',
                    fontSize: '1rem',
                    boxShadow: '0 4px 10px rgba(255, 122, 53, 0.25)',
                  }}
                  onClick={() => window.location.href = '/dashboard/'}
                >
                  Retourner à l'accueil
                </Button>
              </Box>
            </Box>
          </StyledPaper>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#FFE4BA] via-white to-[#FFE4BA] py-12 px-4 sm:px-6 lg:px-8">
      <Container maxWidth="md">
        <Snackbar 
          open={!!error && !error.includes('déjà été accepté')} 
          autoHideDuration={6000} 
          onClose={handleCloseSnackbar}
        >
          <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
            {error}
          </Alert>
        </Snackbar>
        
        {quoteDetails && (
          <StyledPaper>
            <AcceptanceHeader>
              <Typography variant="h4">Acceptation du devis</Typography>
              <Typography variant="subtitle1">
                {quoteDetails.number} - {quoteDetails.description || 'Devis'}
              </Typography>
            </AcceptanceHeader>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>Résumé du devis</Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Émetteur</Typography>
                  <Typography variant="body1">{quoteDetails.emitter_name || quoteDetails.company_settings?.nom || 'JobPartiel'}</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Date d'émission</Typography>
                  <Typography variant="body1">
                    {new Date(quoteDetails.date_creation).toLocaleDateString('fr-FR')}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Montant total</Typography>
                  <Typography variant="body1" fontWeight="bold">{quoteDetails.total_ttc.toFixed(2)} €</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Validité</Typography>
                  <Typography variant="body1">
                    {quoteDetails.date_validite 
                      ? new Date(quoteDetails.date_validite).toLocaleDateString('fr-FR')
                      : 'Non spécifiée'}
                  </Typography>
                </Grid>
              </Grid>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Typography variant="h6" gutterBottom>Signature du devis</Typography>
            <Typography variant="body2" paragraph>
              Pour accepter ce devis, veuillez remplir le formulaire ci-dessous. En soumettant ce formulaire, 
              vous acceptez les conditions du devis et autorisez le prestataire à débuter les travaux.
            </Typography>

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <TextField
                    required
                    fullWidth
                    label="Nom complet"
                    name="clientName"
                    value={formData.clientName}
                    onChange={handleChange}
                    disabled={!!formData.clientName}
                    helperText={
                      formData.clientName
                        ? "Nom pré-rempli automatiquement"
                        : "Nom et prénom du signataire"
                    }
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <TextField
                    required
                    fullWidth
                    label="Email"
                    name="clientEmail"
                    type="email"
                    value={formData.clientEmail}
                    onChange={handleChange}
                    helperText="Adresse email pour la confirmation"
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    fullWidth
                    label="Adresse"
                    name="clientAddress"
                    value={formData.clientAddress}
                    onChange={handleChange}
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <TextField
                    fullWidth
                    label="Téléphone"
                    name="clientPhone"
                    value={formData.clientPhone}
                    onChange={handleChange}
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <TextField
                    required
                    fullWidth
                    label="Signature"
                    name="signature"
                    value={formData.signature}
                    onChange={handleChange}
                    helperText="Tapez votre nom complet pour signer électroniquement"
                  />
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Box sx={{ my: 2, p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      En cliquant sur "Accepter et signer", vous confirmez avoir lu et accepté les conditions de ce devis.
                      Une confirmation sera envoyée par email aux deux parties.
                    </Typography>
                  </Box>
                </Grid>
                
                {/* Section données partagées */}
                <Grid size={{ xs: 12 }}>
                  <Box sx={{ my: 2, p: 3, bgcolor: '#fff8f3', borderRadius: 1, border: '1px solid #FFE4D0' }}>
                    <Typography variant="subtitle1" fontWeight="medium" color="#FF7A35" gutterBottom>
                      Partage des données
                    </Typography>
                    <Typography variant="body2" paragraph>
                      En acceptant ce devis, vous autorisez le partage des informations suivantes avec l'émetteur du devis :
                    </Typography>
                    
                    <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                      <Typography component="li" variant="body2">Votre nom complet</Typography>
                      <Typography component="li" variant="body2">Votre adresse email</Typography>
                      <Typography component="li" variant="body2">Votre numéro de téléphone (si renseigné)</Typography>
                      <Typography component="li" variant="body2">Votre adresse postale (si renseignée)</Typography>
                      <Typography component="li" variant="body2">Votre signature électronique</Typography>
                      <Typography component="li" variant="body2">La date et l'heure de votre acceptation</Typography>
                    </Box>

                    {/* Aperçu des informations réelles qui seront partagées */}
                    <Box sx={{ 
                      mb: 3, 
                      p: 2, 
                      border: '1px dashed #FF7A35',
                      borderRadius: 1,
                      bgcolor: 'white'
                    }}>
                      <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                        Aperçu des informations qui seront partagées :
                      </Typography>
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" color="text.secondary">Nom :</Typography>
                          <Typography variant="body2" fontWeight="medium">{formData.clientName || '(Non renseigné)'}</Typography>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" color="text.secondary">Email :</Typography>
                          <Typography variant="body2" fontWeight="medium">{formData.clientEmail || '(Non renseigné)'}</Typography>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" color="text.secondary">Téléphone :</Typography>
                          <Typography variant="body2" fontWeight="medium">{formData.clientPhone || '(Non renseigné)'}</Typography>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                          <Typography variant="body2" color="text.secondary">Signature :</Typography>
                          <Typography variant="body2" fontWeight="medium">{formData.signature || '(Non renseigné)'}</Typography>
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                          <Typography variant="body2" color="text.secondary">Adresse :</Typography>
                          <Typography variant="body2" fontWeight="medium">{formData.clientAddress || '(Non renseigné)'}</Typography>
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                          <Typography variant="body2" color="text.secondary">Date et heure :</Typography>
                          <Typography variant="body2" fontWeight="medium">{new Date().toLocaleString('fr-FR')}</Typography>
                        </Grid>
                      </Grid>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                      <input
                        type="checkbox"
                        id="dataConsent"
                        name="dataConsent"
                        checked={formData.dataConsent}
                        onChange={handleChange}
                        style={{ marginRight: '8px' }}
                      />
                      <label htmlFor="dataConsent">
                        <Typography variant="body2" fontWeight="medium">
                          J'accepte le partage de mes données personnelles avec l'émetteur du devis pour finaliser mon acceptation
                        </Typography>
                      </label>
                    </Box>
                  </Box>
                </Grid>
                
                <Grid size={{ xs: 12 }}>
                  <Button 
                    type="submit" 
                    variant="contained" 
                    color="primary"
                    size="large"
                    disabled={loading || !formData.dataConsent}
                    sx={{ 
                      bgcolor: '#15803D', 
                      '&:hover': { bgcolor: '#166534' },
                      minWidth: '200px'
                    }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Accepter et signer'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </StyledPaper>
        )}
      </Container>
    </div>
  );
};

export default QuoteAcceptance;