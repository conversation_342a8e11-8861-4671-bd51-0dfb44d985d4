import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>riangle, RefreshCw, Check, Download, Wand2 } from 'lucide-react';
import { <PERSON>Field, Typography, But<PERSON>, Divider } from '@mui/material';
import ModalPortal from '../ModalPortal';
import { motion } from 'framer-motion';
import { useAiImageGeneration, ImageGenerationPurpose, IMAGE_GENERATION_COST } from '../../hooks/useAiImageGeneration';
import useAiConsent from '../../hooks/useAiConsent';
import useImageModeration from '../../hooks/useImageModeration';
import ImageModerationStatus from '../ImageModerationStatus';
import RejectedImageMessage from '../RejectedImageMessage';
import { notify } from '../Notification';
import { setCookie, getCookie } from '../../utils/cookieUtils';
import logger from '../../utils/logger';
import axios from 'axios';
import { API_CONFIG } from '../../config/api';
import { getCommonHeaders } from '../../utils/headers';
import AiImageGenerationLoading from './AiImageGenerationLoading';

interface AiImageGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImageGenerated: (imageUrl: string, imageBase64: string, photoObj?: any) => void;
  purpose: ImageGenerationPurpose;
  defaultPrompt?: string;
  galleryId?: string;
  galleryName?: string;
  galleryDescription?: string;
  isCover?: boolean;
  missionTitle?: string;
  missionDescription?: string;
  width?: number;
  height?: number;
}

/**
 * Modal pour la génération d'images IA
 */
const AiImageGenerationModal: React.FC<AiImageGenerationModalProps> = ({
  isOpen,
  onClose,
  onImageGenerated,
  purpose,
  defaultPrompt = '',
  galleryId,
  galleryName,
  galleryDescription,
  isCover = false,
  missionTitle,
  missionDescription,
  width,
  height
}) => {
  const { hasConsent } = useAiConsent();
  const {
    generating,
    error,
    imageUrl,
    imageBase64,
    prompt,
    generateImage,
    resetState,
    credits
  } = useAiImageGeneration();

  const [customPrompt, setCustomPrompt] = useState<string>(defaultPrompt);
  const [useCustomPrompt, setUseCustomPrompt] = useState<boolean>(false);
  const [showGeneratedImage, setShowGeneratedImage] = useState<boolean>(false);
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);
  const [dontAskAgain, setDontAskAgain] = useState<boolean>(false);
  const [isModerating, setIsModerating] = useState<boolean>(false);
  const [isImageRejected, setIsImageRejected] = useState<boolean>(false);
  const [rejectionDescription, setRejectionDescription] = useState<string | undefined>();
  const [rejectionImprovementSuggestions, setRejectionImprovementSuggestions] = useState<string | undefined>();
  const [showModerationModal, setShowModerationModal] = useState<boolean>(false);

  // Hook pour la modération d'image
  const { moderateImage } = useImageModeration();

  // Réinitialiser l'état lorsque le modal est fermé
  useEffect(() => {
    if (!isOpen) {
      resetState();
      setShowGeneratedImage(false);
      setShowConfirmation(false);
      setDontAskAgain(false);
      setIsModerating(false);
      setIsImageRejected(false);
      setRejectionDescription(undefined);
      setRejectionImprovementSuggestions(undefined);
      setShowModerationModal(false);
    }
  }, [isOpen, resetState]);

  // Mettre à jour l'état lorsqu'une image est générée
  useEffect(() => {
    if (imageUrl && imageBase64) {
      setShowGeneratedImage(true);
    }
  }, [imageUrl, imageBase64]);

  // Vérifier au montage si le cookie existe pour ne plus demander la confirmation IA
  useEffect(() => {
    if (showConfirmation && getCookie('skipIaCreditConfirmation') === '1') {
      // Exécuter directement l'action sans afficher la confirmation
      setShowConfirmation(false);
      executeImageGeneration();
    }
  }, [showConfirmation]);

  // Fonction pour exécuter la génération d'image
  const executeImageGeneration = async () => {
    const options: any = {
      prompt: useCustomPrompt ? customPrompt : undefined,
      purpose,
      useContextualPrompt: !useCustomPrompt,
      width: width,
      height: height
    };
    if (purpose === 'gallery_photo' || purpose === 'featured_photo') {
      options.galleryId = galleryId;
      options.galleryName = galleryName;
      options.galleryDescription = galleryDescription;
    }
    if (purpose === 'mission_image') {
      options.missionTitle = missionTitle;
      options.missionDescription = missionDescription;
    }
    const result = await generateImage(options);
    if (result) {
      logger.info('Image générée avec succès:', result.imageUrl);
    }
  };

  // Fonction pour confirmer l'utilisation des crédits
  const handleConfirmCredit = () => {
    setShowConfirmation(false);

    // Si l'option "Ne plus me demander" est cochée, on enregistre dans un cookie
    if (dontAskAgain) {
      setCookie('skipIaCreditConfirmation', '1', 10 * 60); // 10 minutes
    }

    // Exécuter la génération d'image
    executeImageGeneration();
  };

  // Fonction pour annuler l'utilisation des crédits
  const handleCancelCredit = () => {
    setShowConfirmation(false);
  };

  // Gérer la génération d'image avec confirmation
  const handleGenerateImage = () => {
    // Vérifier si l'utilisateur a donné son consentement
    if (!hasConsent) {
      notify('Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir générer du contenu.', 'error');
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
      return;
    }

    // Vérifier si l'utilisateur a assez de crédits
    if (credits < IMAGE_GENERATION_COST) {
      notify(`Vous n'avez pas assez de crédits IA. La génération d'image nécessite ${IMAGE_GENERATION_COST} crédits. Veuillez en acheter dans le menu "Intelligence Artificielle"`, 'error');
      return;
    }

    // Afficher la confirmation si le cookie n'est pas défini
    if (getCookie('skipIaCreditConfirmation') !== '1') {
      setShowConfirmation(true);
    } else {
      // Sinon, exécuter directement
      executeImageGeneration();
    }
  };

  // Gérer la validation de l'image générée avec modération
  const handleAcceptImage = async () => {
    if (isModerating) return; // Protection anti double clic
    if (!imageUrl || !imageBase64) return; // Ne rien faire si pas d'image générée
    setIsModerating(true);
    setShowModerationModal(true);
    try {
      // --- Correction : pour les images IA de mission (hors cover), on ne modère pas ici ---
      if (purpose === 'mission_image' && !isCover) {
        onImageGenerated(imageUrl, imageBase64);
        setIsModerating(false);
        setShowModerationModal(false);
        onClose();
        return;
      }
      // --- Correction : pour les images IA du card editor (cartes de visite/flyer), on ne modère pas non plus ---
      if ((purpose as string) === 'card_editor') {
        onImageGenerated(imageUrl, imageBase64);
        setIsModerating(false);
        setShowModerationModal(false);
        onClose();
        return;
      }
      // Utiliser l'image temporaire existante pour la modération IA
      const tempImagePath = imageUrl.split('/api/storage-proxy/temp_moderation/')[1];
      const fakeFile = new File([], 'ia_image.jpg', { type: 'image/jpeg' });
      let moderationTypeString: string = purpose;
      if (purpose === 'featured_photo') moderationTypeString = 'featured';
      else if (purpose === 'mission_image') moderationTypeString = 'mission';
      else if (purpose === 'gallery_photo') moderationTypeString = 'gallery';
      else if ((purpose as string) === 'card_editor') moderationTypeString = 'card_editor';
      else if (purpose === 'profile_picture') moderationTypeString = 'profile_picture';
      else if (purpose === 'banner_picture') moderationTypeString = 'banner_picture';
      if (isCover) {
        onImageGenerated(imageUrl, imageBase64);
        setIsModerating(false);
        setShowModerationModal(false);
        onClose();
        return;
      }
      const moderationResult = await moderateImage(fakeFile, moderationTypeString, undefined, { imageUrl, tempImagePath });
      if (moderationResult.isSafe) {
        // Cas création de galerie : pas d'appel API backend IA, on retourne juste l'image au parent ! important pour la galerie sinon on ne peut pas enregistrer l'image !
        // MAIS pour featured_photo, on doit TOUJOURS faire l'appel API /confirm
        if (purpose === 'gallery_photo' && (!galleryId || typeof galleryId !== 'string' || galleryId.trim() === '')) {
          onImageGenerated(imageUrl, imageBase64);
          setIsModerating(false);
          setShowModerationModal(false);
          onClose();
          return;
        }
        // Sinon, on fait l'appel API backend IA pour valider et enregistrer la photo
        let safeGalleryId = galleryId;
        if (purpose === 'gallery_photo') {
          if (!galleryId || typeof galleryId !== 'string' || galleryId.trim() === '') {
            notify("Erreur : L'identifiant de la galerie est requis pour valider une image IA de galerie.", 'error');
            setIsModerating(false);
            setShowModerationModal(false);
            return;
          }
          safeGalleryId = galleryId.trim();
        }
        const confirmRes = await axios.post(`${API_CONFIG.baseURL}/api/ai-image-generation/confirm`, {
          imageUrl,
          purpose,
          galleryId: safeGalleryId,
          galleryName
        }, {
          headers: await getCommonHeaders(),
          withCredentials: true
        });
        if (confirmRes.data && confirmRes.data.avatarUrl) {
          onImageGenerated(confirmRes.data.avatarUrl, imageBase64, confirmRes.data);
        } else if (confirmRes.data && confirmRes.data.photo && confirmRes.data.photo.photo_url) {
          onImageGenerated(confirmRes.data.photo.photo_url, imageBase64, confirmRes.data.photo);
        } else if (confirmRes.data && confirmRes.data.finalImageUrl) {
          onImageGenerated(confirmRes.data.finalImageUrl, imageBase64, confirmRes.data);
        } else {
          onImageGenerated('', imageBase64);
        }
        setIsModerating(false);
        setShowModerationModal(false);
        onClose();
      } else {
        setIsImageRejected(true);
        setRejectionDescription(moderationResult.description);
        setRejectionImprovementSuggestions(moderationResult.improvementSuggestions);
        setIsModerating(false);
        setShowModerationModal(false);
        // Ne pas fermer la modal principale, mais afficher le message de rejet
        setShowGeneratedImage(false); // Masquer l'image générée pour afficher le message de rejet
      }
    } catch (error) {
      setIsModerating(false);
      setShowModerationModal(false);
      notify('Erreur lors de la validation de l\'image IA', 'error');
    }
  };

  // Gérer la régénération d'une image IA
  const handleRegenerateImage = () => {
    resetState();
    setShowGeneratedImage(false);
    setIsImageRejected(false);
    setRejectionDescription(undefined);
    setRejectionImprovementSuggestions(undefined);
    setShowModerationModal(false);
    setIsModerating(false);
    executeImageGeneration();
  };

  // Gérer le téléchargement de l'image générée
  const handleDownloadImage = async () => {
    if (imageUrl) {
      try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `image-ia-${purpose}-${Date.now()}.jpg`;
        document.body.appendChild(link);
        link.click();
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
      } catch (e) {
        notify('Erreur lors du téléchargement de l\'image', 'error');
      }
    }
  };

  return (
    <ModalPortal isOpen={isOpen} onBackdropClick={onClose} zIndex={3000}>
      {/* Confirmation modale */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[70] p-4">
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.98 }}
            transition={{ type: 'spring', stiffness: 300, damping: 25 }}
            className="bg-white rounded-2xl shadow-xl max-w-md w-full overflow-hidden"
            tabIndex={-1}
            aria-modal="true"
            role="dialog"
          >
            {/* Image d'en-tête avec design attrayant */}
            <div className="relative h-32 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] flex items-center justify-center overflow-hidden">
              <div className="absolute inset-0 opacity-20">
                <div className="absolute -top-5 -left-5 w-32 h-32 rounded-full bg-white/20"></div>
                <div className="absolute top-10 right-10 w-20 h-20 rounded-full bg-white/20"></div>
                <div className="absolute bottom-5 left-1/3 w-16 h-16 rounded-full bg-white/20"></div>
              </div>
              <div className="relative flex flex-col items-center text-white space-y-1 p-6">
                <div className="bg-white/20 p-3 rounded-full">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold mt-2">Confirmer l'action IA</h3>
              </div>
              <button
                onClick={handleCancelCredit}
                className="absolute top-4 right-4 p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
                aria-label="Fermer la confirmation"
              >
                <X className="h-5 w-5 text-white" />
              </button>
            </div>

            {/* Corps de la modal */}
            <div className="p-6 pt-5">
              {/* Informations sur les crédits */}
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center text-[#FF6B2C]">
                    <Sparkles className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm">Crédits utilisés</p>
                    <p className="text-gray-900 font-bold text-xl">{IMAGE_GENERATION_COST}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-gray-500 text-sm">Solde restant</p>
                  <p className="text-gray-900 font-bold text-xl">{credits - IMAGE_GENERATION_COST}</p>
                </div>
              </div>

              {/* Action demandée */}
              <div className="bg-gray-50 p-4 rounded-xl mb-5">
                <p className="text-gray-800 font-medium mb-1">Action demandée :</p>
                <p className="text-gray-900 font-semibold">
                  Générer une image avec l'IA pour {getPurposeLabel(purpose)}
                </p>
              </div>

              {/* Alerte avec design moderne */}
              <div className="flex items-start gap-3 p-4 rounded-xl border border-amber-200 bg-amber-50 mb-5">
                <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-amber-800 font-medium text-sm">Cette action décomptera vos crédits IA.</p>
                  <p className="text-amber-700 text-xs mt-1">
                    Vous pourrez vérifier et modifier l'image générée avant de l'utiliser.
                  </p>
                </div>
              </div>

              {/* Option Ne plus demander */}
              <label className="flex items-center gap-2 cursor-pointer mb-6">
                <div className="relative flex items-center">
                  <input
                    type="checkbox"
                    checked={dontAskAgain}
                    onChange={(e) => setDontAskAgain(e.target.checked)}
                    className="peer sr-only"
                  />
                  <div className="w-5 h-5 border-2 border-gray-300 rounded bg-white peer-checked:bg-[#FF6B2C] peer-checked:border-[#FF6B2C] transition-colors" />
                  <svg
                    className="absolute w-5 h-5 pointer-events-none opacity-0 peer-checked:opacity-100 text-white transition-opacity"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                </div>
                <span className="text-gray-700 text-sm">Ne plus me demander pendant 10 minutes</span>
              </label>

              {/* Boutons d'action */}
              <div className="flex flex-col gap-3">
                {!hasConsent ? (
                  <button
                    onClick={() => window.dispatchEvent(new CustomEvent('open-ai-consent-modal'))}
                    className="w-full py-3.5 px-4 bg-[#FF6B2C] text-white rounded-xl hover:bg-[#FF7A35] active:bg-[#E75B1C] transition-colors font-bold flex items-center justify-center gap-2 shadow-sm text-base focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-2"
                  >
                    <AlertTriangle className="h-5 w-5" />
                    Accepter les CGU
                  </button>
                ) : (
                  <button
                    onClick={handleConfirmCredit}
                    className="w-full py-3.5 px-4 bg-[#FF6B2C] text-white rounded-xl hover:bg-[#FF7A35] active:bg-[#E75B1C] transition-colors font-bold flex items-center justify-center gap-2 shadow-sm text-base focus:outline-none focus:ring-2 focus:ring-[#FF6B2C] focus:ring-offset-2"
                    autoFocus
                  >
                    <Check className="h-5 w-5" />
                    Confirmer
                  </button>
                )}
                <button
                  onClick={handleCancelCredit}
                  className="w-full py-3.5 px-4 border border-gray-300 bg-white text-gray-700 rounded-xl hover:bg-gray-50 active:bg-gray-100 transition-colors font-medium text-base focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
                >
                  Annuler
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Modale de modération d'image */}
      {showModerationModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[70] p-4">
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.98 }}
            transition={{ type: 'spring', stiffness: 300, damping: 25 }}
            className="bg-white rounded-2xl shadow-xl max-w-md w-full overflow-hidden"
            tabIndex={-1}
            aria-modal="true"
            role="dialog"
          >
            <div className="p-4 pt-5 flex justify-between items-center border-b">
              <h3 className="text-lg font-semibold">
                {isModerating
                  ? "Analyse de sécurité en cours"
                  : "Modération de l'image"}
              </h3>
              <button
                onClick={() => {
                  setShowModerationModal(false);
                  if (isImageRejected) {
                    setIsImageRejected(false);
                  }
                }}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto max-h-[calc(90vh-80px)]">
              {isModerating ? (
                <ImageModerationStatus
                  isLoading={true}
                  imageUrl={imageUrl || undefined}
                  title="Analyse de sécurité en cours"
                  onCancel={() => {
                    setShowModerationModal(false);
                  }}
                />
              ) : isImageRejected ? (
                <div className="p-6">
                  {/* Afficher l'image refusée */}
                  {imageUrl && (
                    <div className="mb-6 flex justify-center">
                      <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-lg overflow-hidden border-4 border-white shadow-lg">
                        <img
                          src={imageUrl}
                          alt="Image refusée"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-red-900/20"></div>
                      </div>
                    </div>
                  )}

                  {/* Message de rejet détaillé */}
                  <RejectedImageMessage
                    contentType={purpose}
                    description={rejectionDescription}
                    improvementSuggestions={rejectionImprovementSuggestions}
                    variant="detailed"
                  />

                  {/* Bouton pour réessayer */}
                  <div className="mt-6 flex justify-center">
                    <Button
                      variant="contained"
                      onClick={handleRegenerateImage}
                      startIcon={<RefreshCw className="h-5 w-5" />}
                      sx={{
                        backgroundColor: '#FF6B2C',
                        '&:hover': {
                          backgroundColor: '#FF965E',
                        },
                      }}
                    >
                      Générer une nouvelle image
                    </Button>
                  </div>
                </div>
              ) : null}
            </div>
          </motion.div>
        </div>
      )}

      {/* Modale principale */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto mx-2 sm:mx-auto"
      >
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Sparkles className="h-5 w-5 text-[#FF6B2C] mr-2" />
            <h2 className="text-lg sm:text-xl font-semibold">Génération d'image IA</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="px-2 sm:px-6 py-3 sm:py-4">
          {generating ? (
            // Nouvelle animation de chargement
            <AiImageGenerationLoading purpose={getPurposeLabel(purpose)} />
          ) : isImageRejected ? (
            // Affichage du message de rejet dans la modal principale
            <div className="p-6">
              {/* Afficher l'image refusée */}
              {imageUrl && (
                <div className="mb-6 flex justify-center">
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-lg overflow-hidden border-4 border-white shadow-lg">
                    <img
                      src={imageUrl}
                      alt="Image refusée"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-red-900/20"></div>
                  </div>
                </div>
              )}

              {/* Message de rejet détaillé */}
              <RejectedImageMessage
                contentType={purpose}
                description={rejectionDescription}
                improvementSuggestions={rejectionImprovementSuggestions}
                variant="detailed"
              />

              {/* Boutons d'action */}
              <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  variant="contained"
                  onClick={handleRegenerateImage}
                  startIcon={<RefreshCw className="h-5 w-5" />}
                  sx={{
                    backgroundColor: '#FF6B2C',
                    '&:hover': {
                      backgroundColor: '#FF965E',
                    },
                  }}
                >
                  Générer une nouvelle image
                </Button>
                <Button
                  variant="outlined"
                  onClick={onClose}
                  sx={{
                    borderColor: '#FF6B2C',
                    color: '#FF6B2C',
                    '&:hover': {
                      borderColor: '#FF965E',
                      backgroundColor: 'rgba(255, 107, 44, 0.04)',
                    },
                  }}
                >
                  Fermer
                </Button>
              </div>
            </div>
          ) : !showGeneratedImage ? (
            <>
              <div className="mb-6">
                <div className="bg-[#FFF8F3] rounded-lg p-4 mb-4 border-l-4 border-[#FF6B2C]">
                  <div className="flex items-start">
                    <Wand2 className="h-5 w-5 text-[#FF6B2C] mr-3 mt-1" />
                    <div>
                      <Typography variant="body1" className="mb-1 font-medium">
                        Générez une image avec l'IA pour {getPurposeLabel(purpose)}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600">
                        Notre IA va créer une image professionnelle adaptée à vos besoins
                      </Typography>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-5 bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center mb-2 sm:mb-0">
                    <Sparkles className="h-4 w-4 text-[#FF6B2C] mr-2" />
                    <Typography variant="body2">
                      Coût de génération
                    </Typography>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-[#FFF8F3] text-[#FF6B2C] border border-[#FFE4BA] mr-0 sm:mr-2">
                      {IMAGE_GENERATION_COST} crédits IA
                    </span>
                    <Typography variant="body2" className="font-medium">
                      Solde: {credits} crédits
                    </Typography>
                  </div>
                </div>

                <Divider className="my-4" />

                <div className="mb-4">
                  <div className="flex items-center mb-3">
                    <input
                      type="checkbox"
                      id="useCustomPrompt"
                      checked={useCustomPrompt}
                      onChange={(e) => setUseCustomPrompt(e.target.checked)}
                      className="mr-2 h-4 w-4 accent-[#FF6B2C]"
                    />
                    <label htmlFor="useCustomPrompt" className="text-base font-medium cursor-pointer">
                      Utiliser un prompt personnalisé
                    </label>
                  </div>

                  {useCustomPrompt ? (
                    <div className="bg-white border border-gray-200 rounded-lg p-4 transition-all duration-300 shadow-sm">
                      <TextField
                        label="Prompt personnalisé"
                        variant="outlined"
                        fullWidth
                        multiline
                        rows={4}
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="Décrivez l'image que vous souhaitez générer..."
                        disabled={generating}
                        className="mb-2"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&.Mui-focused fieldset': {
                              borderColor: '#FF6B2C',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#FF6B2C',
                          },
                        }}
                      />
                      <div className="mt-3 bg-[#FFF8F3] p-3 rounded-md border border-[#FFE4BA]">
                        <Typography variant="caption" className="text-gray-700 flex items-start">
                          <Brain className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" />
                          <span>
                            <strong>Exemple:</strong>{" "}
                            <span className="italic">"Un jardinier souriant en train de tailler une haie, style photo professionnelle, lumière naturelle"</span>
                            <br />
                            Soyez précis sur le sujet, l'ambiance, le style ou les couleurs souhaitées.
                          </span>
                        </Typography>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-[#FFF8F3] border border-[#FFE4BA] rounded-lg p-4 transition-all duration-300">
                      <div className="flex items-start">
                        <Brain className="h-5 w-5 text-[#FF6B2C] mr-2 mt-0.5 flex-shrink-0" />
                        <Typography variant="body2">
                          Un prompt contextuel sera généré automatiquement en fonction de votre profil et de vos services.
                          <br />
                          <span className="text-sm text-gray-500 mt-1 block">
                            Option recommandée pour des résultats optimaux.
                          </span>
                        </Typography>
                      </div>
                    </div>
                  )}
                </div>

                {error && (
                  <div className="p-4 bg-red-50 text-red-700 rounded-lg mb-4 border border-red-200">
                    <div className="flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0" />
                      <Typography variant="body2">{error}</Typography>
                    </div>
                  </div>
                )}

                <div className="flex justify-end mt-6 gap-2">
                  <Button
                    variant="outlined"
                    onClick={onClose}
                    className="mr-3"
                    disabled={generating}
                    sx={{
                      borderColor: '#FF6B2C',
                      color: '#FF6B2C',
                      '&:hover': {
                        borderColor: '#FF965E',
                        backgroundColor: 'rgba(255, 107, 44, 0.04)',
                      },
                    }}
                  >
                    Annuler
                  </Button>
                  {!hasConsent ? (
                    <Button
                      variant="contained"
                      onClick={() => window.dispatchEvent(new CustomEvent('open-ai-consent-modal'))}
                      startIcon={<AlertTriangle />}
                      sx={{
                        backgroundColor: '#FF6B2C',
                        '&:hover': {
                          backgroundColor: '#FF965E',
                        },
                      }}
                    >
                      Accepter les CGU
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleGenerateImage}
                      disabled={generating || (useCustomPrompt && !customPrompt.trim())}
                      startIcon={<Sparkles className="h-5 w-5" />}
                      sx={{
                        backgroundColor: '#FF6B2C',
                        '&:hover': {
                          backgroundColor: '#FF965E',
                        },
                        '&.Mui-disabled': {
                          backgroundColor: '#FFE4BA',
                          color: '#FF965E',
                        },
                      }}
                    >
                      <span className="text-xs sm:text-sm font-semibold" style={{textTransform:'none'}}>Générer une image</span>
                    </Button>
                  )}
                </div>
              </div>
            </>
          ) : (
            <>
              {imageUrl && (
                <div className="mb-4">
                  {/* Afficher le prompt utilisé uniquement si l'utilisateur a fourni un prompt personnalisé */}
                  {useCustomPrompt && prompt && (
                    <div className="mb-5">
                      <Typography variant="body2" className="mb-2 font-semibold text-sm sm:text-base">
                        Prompt utilisé :
                      </Typography>
                      <div className="p-2 sm:p-3 bg-gray-100 rounded-lg border border-gray-200">
                        <Typography variant="body2" className="text-xs sm:text-base">{prompt}</Typography>
                      </div>
                    </div>
                  )}

                  <div className="mb-5 relative">
                    <Typography variant="body2" className="mb-2 font-semibold text-sm sm:text-base">
                      Image générée :
                    </Typography>
                    <div className="border border-gray-200 rounded-lg p-1 sm:p-2 flex justify-center bg-[#FFF8F3] relative">
                      {/* Badge IA en haut à gauche */}
                      <div className="absolute top-2 left-2 z-20">
                        <span className="bg-[#FF6B2C] text-white text-xs font-bold px-2 py-1 rounded shadow-md tracking-wide select-none" style={{letterSpacing: '0.04em'}}>IA</span>
                      </div>
                      <img
                        src={imageUrl}
                        alt="Image générée par IA"
                        className="w-full max-w-xs sm:max-w-full h-auto max-h-[250px] sm:max-h-[400px] rounded shadow-sm object-contain"
                      />
                      {/* Bouton Régénérer en bas au centre de l'image */}
                      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-10">
                        <Button
                          variant="outlined"
                          color="primary"
                          onClick={handleRegenerateImage}
                          startIcon={!generating ? <RefreshCw className="h-4 w-4" /> : undefined}
                          disabled={generating}
                          sx={{
                            border: '1.5px solid',
                            borderColor: 'rgba(220,220,220,0.5)',
                            color: '#FF6B2C',
                            background: 'rgba(255,255,255,0.82)',
                            backdropFilter: 'blur(10px)',
                            borderRadius: '999px',
                            minWidth: 0,
                            px: 1.8,
                            py: 0.6,
                            fontWeight: 600,
                            fontSize: { xs: '0.85rem', sm: '0.95rem' },
                            textTransform: 'none',
                            boxShadow: '0 2px 12px 0 rgba(0,0,0,0.10)',
                            transition: 'all 0.18s cubic-bezier(.4,0,.2,1)',
                            '& .MuiButton-startIcon': {
                              color: '#FF6B2C',
                            },
                            '&:hover': {
                              background: 'rgba(255,255,255,0.96)',
                              boxShadow: '0 6px 24px 0 rgba(255,107,44,0.13), 0 2px 8px 0 rgba(0,0,0,0.13)',
                              borderColor: 'rgba(220,220,220,0.7)',
                              transform: 'translateY(-2px) scale(1.04)',
                            },
                            '&:active': {
                              boxShadow: '0 1px 4px 0 rgba(255,107,44,0.10)',
                              background: 'rgba(255,255,255,0.92)',
                            },
                          }}
                        >
                          {generating ? "Régénération..." : "Régénérer"}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:justify-between gap-3 mt-2">
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-2 mb-2 sm:mb-0">
                      <Button
                        variant="outlined"
                        onClick={handleDownloadImage}
                        startIcon={<Download className="h-5 w-5" />}
                        disabled={generating}
                        sx={{
                          borderColor: '#FF6B2C',
                          color: '#FF6B2C',
                          '&:hover': {
                            borderColor: '#FF965E',
                            backgroundColor: 'rgba(255, 107, 44, 0.04)',
                          },
                        }}
                      >
                        Télécharger
                      </Button>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                      <Button
                        variant="outlined"
                        onClick={onClose}
                        className="mr-0 sm:mr-3"
                        disabled={generating}
                        sx={{
                          borderColor: '#FF6B2C',
                          color: '#FF6B2C',
                          '&:hover': {
                            borderColor: '#FF965E',
                            backgroundColor: 'rgba(255, 107, 44, 0.04)',
                          },
                        }}
                      >
                        Annuler
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleAcceptImage}
                        startIcon={<Check className="h-5 w-5" />}
                        disabled={generating}
                        sx={{
                          backgroundColor: '#FF6B2C',
                          '&:hover': {
                            backgroundColor: '#FF965E',
                          },
                        }}
                      >
                        Utiliser cette image
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </motion.div>
    </ModalPortal>
  );
};

/**
 * Retourne le libellé correspondant au type d'image à générer
 */
const getPurposeLabel = (purpose: ImageGenerationPurpose): string => {
  switch (purpose) {
    case 'profile_picture':
      return 'votre photo de profil';
    case 'banner_picture':
      return 'votre bannière de profil';
    case 'mission_image':
      return 'votre mission';
    case 'gallery_photo':
      return 'votre galerie';
    case 'featured_photo':
      return 'vos photos mises en avant';
    case 'card_editor':
      return 'votre carte de visite ou flyer';
    default:
      return 'votre image';
  }
};

export default AiImageGenerationModal;
