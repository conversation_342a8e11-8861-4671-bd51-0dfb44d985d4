import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { dbService } from '../services/db';
import { sendOfferExpirationEmail, sendOfferExpirationReminderEmail } from '../services/emailService';
import { decryptProfilDataAsync, decryptUserDataAsync } from '../utils/encryption';

/**
 * Script pour vérifier les offres et contre-offres qui n'ont pas reçu de réponse dans les 24 heures
 * et envoyer des notifications et emails aux utilisateurs concernés
 */
export class OfferExpirationChecker {
  // Constantes pour les délais de notification
  private static readonly EXPIRATION_HOURS = 24; // Délai d'expiration en heures
  private static readonly REMINDER_HOURS = [20, 10, 6, 2]; // Heures restantes avant expiration pour envoyer des rappels

  /**
   * Vérifie les offres et contre-offres en attente et les traite selon leur délai
   */
  public static async checkExpiredOffers(): Promise<void> {
    try {
      logger.info('Vérification des offres et contre-offres expirées...');
      
      // Récupérer la date actuelle
      const now = new Date();
      
      // Récupérer les offres en attente (statut 'en_attente')
      await this.processOffers('en_attente', now);
      
      // Récupérer les contre-offres (statut 'contre_offre')
      await this.processOffers('contre_offre', now);
      
      // Récupérer les contre-offres du jobbeur (statut 'contre_offre_jobbeur')
      await this.processOffers('contre_offre_jobbeur', now);
      
      logger.info('Vérification des offres et contre-offres terminée');
    } catch (error) {
      logger.error('Erreur lors de la vérification des offres expirées:', error);
    }
  }

  /**
   * Traite les offres d'un statut spécifique
   */
  private static async processOffers(status: string, now: Date): Promise<void> {
    try {
      // Déterminer le champ de date à utiliser selon le statut
      let dateField = 'created_at';
      if (status === 'contre_offre') {
        dateField = 'date_contre_offre';
      } else if (status === 'contre_offre_jobbeur') {
        dateField = 'date_contre_offre_jobbeur';
      }
      
      // Récupérer toutes les offres avec le statut spécifié
      const { data: offers, error } = await supabase
        .from('user_mission_candidature')
        .select(`
          id, 
          mission_id, 
          jobbeur_id, 
          statut, 
          montant_propose, 
          montant_contre_offre, 
          montant_contre_offre_jobbeur,
          message,
          message_contre_offre,
          message_contre_offre_jobbeur,
          created_at,
          date_contre_offre,
          date_contre_offre_jobbeur,
          user_missions(
            id,
            user_id,
            titre
          )
        `)
        .eq('statut', status);
      
      if (error) {
        logger.error(`Erreur lors de la récupération des offres avec statut ${status}:`, error);
        return;
      }
      
      if (!offers || offers.length === 0) {
        logger.info(`Aucune offre avec statut ${status} trouvée`);
        return;
      }
      
      logger.info(`${offers.length} offres avec statut ${status} trouvées`);
      
      // Traiter chaque offre
      for (const offer of offers) {
        const offerDate = new Date(offer[dateField as keyof typeof offer] as string);
        const expirationDate = new Date(offerDate);
        expirationDate.setHours(expirationDate.getHours() + this.EXPIRATION_HOURS);
        
        // Calculer le temps restant en heures
        const hoursRemaining = (expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60);
        
        // Si l'offre est expirée, la refuser automatiquement
        if (hoursRemaining <= 0) {
          await this.rejectExpiredOffer(offer, status);
        } 
        // Sinon, vérifier si un rappel doit être envoyé
        else {
          await this.sendReminderIfNeeded(offer, hoursRemaining, expirationDate, status);
        }
      }
    } catch (error) {
      logger.error(`Erreur lors du traitement des offres avec statut ${status}:`, error);
    }
  }

  /**
   * Rejette une offre expirée
   */
  private static async rejectExpiredOffer(offer: any, status: string): Promise<void> {
    try {
      logger.info(`Rejet automatique de l'offre ${offer.id} expirée`);
      
      // Log de l'offre pour débogage
      logger.info(`Données de l'offre: ${JSON.stringify(offer)}`);
      
      // Mettre à jour le statut de l'offre
      const { error: updateError } = await supabase
        .from('user_mission_candidature')
        .update({
          statut: 'refusée',
          date_refus: new Date().toISOString()
        })
        .eq('id', offer.id);
      
      if (updateError) {
        logger.error(`Erreur lors du rejet de l'offre ${offer.id}:`, updateError);
        return;
      }
      
      // Récupérer les informations des utilisateurs concernés
      const missionOwnerId = offer.user_missions.user_id;
      const jobbeerId = offer.jobbeur_id;
      
      // Récupérer les emails et profils des utilisateurs
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select(`
          id, 
          email,
          user_profil:user_profil(
            nom,
            prenom
          )
        `)
        .in('id', [missionOwnerId, jobbeerId]);
      
      if (usersError || !users) {
        logger.error(`Erreur lors de la récupération des utilisateurs pour l'offre ${offer.id}:`, usersError);
        return;
      }
      
      const missionOwner = users.find(u => u.id === missionOwnerId);
      const jobbeur = users.find(u => u.id === jobbeerId);
      
      if (!missionOwner || !jobbeur) {
        logger.error(`Utilisateurs non trouvés pour l'offre ${offer.id}`);
        return;
      }
      
      // Déchiffrer les données de profil du propriétaire de la mission
      const decryptedMissionOwnerProfile = missionOwner.user_profil && missionOwner.user_profil[0]
        ? await decryptProfilDataAsync(missionOwner.user_profil[0])
        : null;
      
      const missionOwnerName = decryptedMissionOwnerProfile
        ? `${decryptedMissionOwnerProfile.prenom || ''} ${decryptedMissionOwnerProfile.nom ? decryptedMissionOwnerProfile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
        : '';

      // Décrypter les emails des utilisateurs
      const decryptedMissionOwner = await decryptUserDataAsync(missionOwner);
      const decryptedJobbeur = await decryptUserDataAsync(jobbeur);
      const missionOwnerEmail = decryptedMissionOwner.email;
      const jobbeurEmail = decryptedJobbeur.email;
      
      // Déterminer le type d'offre selon le statut
      let offerType = '';
      if (status === 'en_attente') {
        offerType = 'l\'offre';
      } else if (status === 'contre_offre') {
        offerType = 'la contre-offre';
      } else if (status === 'contre_offre_jobbeur') {
        offerType = 'la contre-offre du jobbeur';
      }
      
      // Déterminer le montant et le message selon le statut
      let amount = 0;
      let message = '';
      let proposalDate = '';
      let senderName = '';
      
      // Déchiffrer les données de profil du jobbeur
      const decryptedJobbeurProfile = jobbeur.user_profil && jobbeur.user_profil[0]
        ? await decryptProfilDataAsync(jobbeur.user_profil[0])
        : null;
      
      if (status === 'en_attente') {
        amount = offer.montant_propose;
        message = offer.message || 'Aucun message';
        proposalDate = offer.created_at;
        senderName = decryptedJobbeurProfile
          ? `${decryptedJobbeurProfile.prenom || ''} ${decryptedJobbeurProfile.nom?.charAt(0) || ''}.`
          : '';
      } else if (status === 'contre_offre') {
        amount = offer.montant_contre_offre;
        message = offer.message_contre_offre || 'Aucun message';
        proposalDate = offer.date_contre_offre;
        senderName = decryptedMissionOwnerProfile
          ? `${decryptedMissionOwnerProfile.prenom || ''} ${decryptedMissionOwnerProfile.nom?.charAt(0) || ''}.`
          : '';
      } else if (status === 'contre_offre_jobbeur') {
        amount = offer.montant_contre_offre_jobbeur;
        message = offer.message_contre_offre_jobbeur || 'Aucun message';
        proposalDate = offer.date_contre_offre_jobbeur;
        senderName = decryptedJobbeurProfile
          ? `${decryptedJobbeurProfile.prenom || ''} ${decryptedJobbeurProfile.nom?.charAt(0) || ''}.`
          : '';
      }
      
      // Log des données d'email pour débogage
    //   logger.info(`Données d'email pour ${missionOwnerEmail}: ${JSON.stringify({
    //     missionTitle: offer.user_missions.titre,
    //     missionId: offer.mission_id,
    //     offerType: offerType,
    //     reason: 'refusée',
    //     senderName: senderName,
    //     amount: amount,
    //     proposalDate: proposalDate,
    //     message: message
    //   })}`);
      
      // Envoyer des emails aux utilisateurs concernés
      if (missionOwnerEmail) {
        await sendOfferExpirationEmail(missionOwnerEmail, {
          missionTitle: offer.user_missions.titre,
          missionId: offer.mission_id,
          offerType: offerType,
          reason: 'refusée',
          senderName: senderName,
          amount: amount,
          proposalDate: proposalDate,
          message: message,
          missionOwnerName: missionOwnerName
        });
      }
      
      if (jobbeurEmail) {
        await sendOfferExpirationEmail(jobbeurEmail, {
          missionTitle: offer.user_missions.titre,
          missionId: offer.mission_id,
          offerType: offerType,
          reason: 'refusée',
          senderName: senderName,
          amount: amount,
          proposalDate: proposalDate,
          message: message,
          missionOwnerName: missionOwnerName
        });
      }
      
      // Créer des notifications pour les utilisateurs
      await this.createNotifications(offer, missionOwnerId, jobbeerId, status, 'expirée');
      
      // Créer un avis négatif automatique pour le jobbeur qui n'a pas répondu
      try {
        // Vérifier qu'il n'existe pas déjà un avis pour cette mission et ce jobbeur
        const { data: existingReview } = await supabase
          .from('user_reviews')
          .select('id')
          .eq('author_id', missionOwnerId)
          .eq('target_user_id', jobbeerId)
          .eq('mission_id', offer.mission_id)
          .single();

        if (!existingReview) {
          const jobbeurName = decryptedJobbeurProfile
            ? `${decryptedJobbeurProfile.prenom || ''} ${decryptedJobbeurProfile.nom ? decryptedJobbeurProfile.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
            : '';
          // Créer un avis négatif automatique pour le jobbeur qui n'a pas répondu
          const { data: insertedReview, error: insertReviewError } = await supabase.from('user_reviews').insert([
            {
              author_id: jobbeerId,
              target_user_id: missionOwnerId,
              note: 1,
              commentaire: `Avis automatique : ${missionOwnerName} n'a pas répondu à une offre reçue dans le délai imparti (moins de 24h).`,
              mission_id: offer.mission_id,
              qualites: [],
              defauts: ["Ne répond pas aux offres reçues sous 24h"],
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ]).select().single();

          // Créditer 1 jobi à l'auteur de l'avis automatique
          await dbService.createJobiEntrySupplement(jobbeerId, {
            montant: 1,
            titre: 'Récompense avis',
            description: '1 jobi à été crédité suite à l\'envoi d\'un avis automatique'
          });

          // Créer une notification spécifique pour l'ajout de Jobi
          await supabase
            .from('user_notifications')
            .insert({
              user_id: jobbeerId,
              title: 'Récompense avis',
              content: '1 jobi à été crédité suite à l\'envoi d\'un avis automatique',
              type: 'jobi',
              is_read: false,
              is_archived: false,
              link: '/dashboard/jobi'
            });
          logger.info(`1 jobi ajoutés pour l'utilisateur ${jobbeerId} suite à l'envoi d'un avis automatique`);

          // Créer une notification à l'utilisateur qui a reçu l'avis automatique
          await supabase
            .from('user_notifications')
            .insert({
              user_id: missionOwnerId,
              title: '⭐ Avis automatique reçu',
              type: 'profile',
              content: `
                <div style="font-family: Arial, sans-serif; color: #333;">
                  <p style="margin-bottom: 10px;">
                    Vous avez reçu un avis automatique pour la mission
                    <strong style="color: #FF6B2C;">${offer.user_missions.titre}</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    Auteur : <strong>${jobbeurName}</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    Note : <strong style="color: #FF6B2C;">1/5</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    <span style="color: #F43F5E;">Ne répond pas aux offres reçues sous 24h</span>
                  </p>
                  <p style="margin: 0; font-size: 0.9em; color: #666;">
                    Cet avis a été généré automatiquement car vous n'avez pas répondu à l'offre dans le délai imparti (24h).
                  </p>
                </div>
              `,
              is_read: false,
              is_archived: false,
              link: `/dashboard/avis?review=${insertedReview?.id || ''}`
            });

          // Créer une notification à l'utilisateur qui a émis l'avis automatique (EN PLUS)
          await supabase
            .from('user_notifications')
            .insert({
              user_id: jobbeerId,
              title: '✅ Avis automatique déposé',
              type: 'profile',
              content: `
                <div style="font-family: Arial, sans-serif; color: #333;">
                  <p style="margin-bottom: 10px;">
                    Un avis automatique a été déposé pour la mission
                    <strong style="color: #FF6B2C;">${offer.user_missions.titre}</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    Destinataire : <strong>${missionOwnerName}</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    Note donnée : <strong style="color: #FF6B2C;">1/5</strong>
                  </p>
                  <p style="margin-bottom: 10px;">
                    <span style="color: #F43F5E;">Ne répond pas aux offres reçues sous 24h</span>
                  </p>
                  <p style="margin: 0; color: #FF6B2C; font-weight: bold;">
                    Vous avez gagné 1 jobi !
                  </p>
                </div>
              `,
              is_read: false,
              is_archived: false,
              link: `/dashboard/avis?review=${insertedReview?.id || ''}`
            });
        }
      } catch (reviewError) {
        logger.error(`Erreur lors de la création de l'avis automatique pour l'offre expirée ${offer.id}:`, reviewError);
      }
      
    } catch (error) {
      logger.error(`Erreur lors du rejet de l'offre ${offer.id}:`, error);
    }
  }

  /**
   * Envoie un rappel si nécessaire
   */
  private static async sendReminderIfNeeded(offer: any, hoursRemaining: number, expirationDate: Date, status: string): Promise<void> {
    try {
      // Vérifier si un rappel doit être envoyé

      logger.info(`Vérification des rappels pour l'offre ${offer.id} (${hoursRemaining}h restantes)`);
      
      for (const reminderHour of this.REMINDER_HOURS) {
        if (hoursRemaining <= reminderHour && hoursRemaining > reminderHour - 1) {
          // Vérifier si le rappel a déjà été envoyé
          // Modifier la clé pour utiliser un prefix spécifique qui sera préservé lors des redémarrages du serveur
          const reminderKey = `reminder:email:offer:${offer.id}:${reminderHour}`;
          const reminderSent = await redis.exists(reminderKey);
          
          if (!reminderSent) {
            logger.info(`Envoi d'un rappel pour l'offre ${offer.id} (${reminderHour}h restantes)`);
            
            // Marquer le rappel comme envoyé avec une TTL de 3 jours pour économiser la mémoire
            // tout en gardant une sécurité suffisante contre les duplications
            await redis.setex(reminderKey, 3 * 24 * 60 * 60, 'sent');
            
            // Récupérer les informations des utilisateurs concernés
            const missionOwnerId = offer.user_missions.user_id;
            const jobbeerId = offer.jobbeur_id;
            
            // Déterminer qui doit recevoir le rappel selon le statut
            let recipientId;
            if (status === 'en_attente' || status === 'contre_offre_jobbeur') {
              recipientId = missionOwnerId;
            } else if (status === 'contre_offre') {
              recipientId = jobbeerId;
            }
            
            if (recipientId) {
              // Récupérer l'email et le profil du destinataire
              const { data: users, error: usersError } = await supabase
                .from('users')
                .select(`
                  id, 
                  email,
                  user_profil:user_profil(
                    nom,
                    prenom
                  )
                `)
                .in('id', [missionOwnerId, jobbeerId]);
              
              if (usersError || !users) {
                logger.error(`Erreur lors de la récupération des utilisateurs pour l'offre ${offer.id}:`, usersError);
                return;
              }
              
              const missionOwner = users.find(u => u.id === missionOwnerId);
              const jobbeur = users.find(u => u.id === jobbeerId);
              
              if (!missionOwner || !jobbeur) {
                logger.error(`Utilisateurs non trouvés pour l'offre ${offer.id}`);
                return;
              }
              
              const recipient = users.find(u => u.id === recipientId);

              if (!recipient) {
                logger.error(`Destinataire non trouvé pour l'offre ${offer.id}`);
                return;
              }

              // Décrypter l'email du destinataire
              const decryptedRecipient = await decryptUserDataAsync(recipient);
              
              // Déterminer le type d'offre selon le statut
              let offerType = '';
              if (status === 'en_attente') {
                offerType = 'une offre';
              } else if (status === 'contre_offre') {
                offerType = 'une contre-offre';
              } else if (status === 'contre_offre_jobbeur') {
                offerType = 'une contre-offre du jobbeur';
              }
              
              // Déterminer le montant, le message et l'expéditeur selon le statut
              let amount = 0;
              let message = '';
              let senderName = '';
              
              // Déchiffrer les données de profil
              let decryptedMissionOwnerProfile = null;
              let decryptedJobbeurProfile = null;
              
              if (missionOwner.user_profil && missionOwner.user_profil.length > 0) {
                try {
                  decryptedMissionOwnerProfile = await decryptProfilDataAsync(missionOwner.user_profil[0]);
                } catch (error) {
                  logger.error(`Erreur lors du décryptage du profil du propriétaire de mission ${missionOwnerId}:`, error);
                }
              }
              
              if (jobbeur.user_profil && jobbeur.user_profil.length > 0) {
                try {
                  decryptedJobbeurProfile = await decryptProfilDataAsync(jobbeur.user_profil[0]);
                } catch (error) {
                  logger.error(`Erreur lors du décryptage du profil du jobbeur ${jobbeerId}:`, error);
                }
              }
              
              if (status === 'en_attente') {
                amount = offer.montant_propose;
                message = offer.message || 'Aucun message';
                senderName = decryptedJobbeurProfile
                  ? `${decryptedJobbeurProfile.prenom || ''} ${decryptedJobbeurProfile.nom?.charAt(0) || ''}.`.trim()
                  : 'Utilisateur';
              } else if (status === 'contre_offre') {
                amount = offer.montant_contre_offre;
                message = offer.message_contre_offre || 'Aucun message';
                senderName = decryptedMissionOwnerProfile
                  ? `${decryptedMissionOwnerProfile.prenom || ''} ${decryptedMissionOwnerProfile.nom?.charAt(0) || ''}.`.trim()
                  : 'Utilisateur';
              } else if (status === 'contre_offre_jobbeur') {
                amount = offer.montant_contre_offre_jobbeur;
                message = offer.message_contre_offre_jobbeur || 'Aucun message';
                senderName = decryptedJobbeurProfile
                  ? `${decryptedJobbeurProfile.prenom || ''} ${decryptedJobbeurProfile.nom?.charAt(0) || ''}.`.trim()
                  : 'Utilisateur';
              }
              
              // Log pour déboguer le senderName
              logger.info(`Offre ${offer.id} - senderName après décryptage: "${senderName}"`);
              
              // Envoyer un email de rappel
              await sendOfferExpirationReminderEmail(decryptedRecipient.email, {
                missionTitle: offer.user_missions.titre,
                missionId: offer.mission_id,
                offerType: offerType,
                hoursRemaining: reminderHour,
                expirationDate: expirationDate.toLocaleString('fr-FR'),
                amount: amount,
                message: message,
                senderName: senderName
              });
              
              // Créer une notification pour l'utilisateur
              await this.createReminderNotification(offer, recipientId, status, Math.round(hoursRemaining));
            }
          } else {
            logger.info(`Rappel déjà envoyé pour l'offre ${offer.id} (${reminderHour}h restantes)`);
          }
        }
      }
    } catch (error) {
      logger.error(`Erreur lors de l'envoi du rappel pour l'offre ${offer.id}:`, error);
    }
  }

  /**
   * Crée des notifications pour les utilisateurs concernés
   */
  private static async createNotifications(offer: any, missionOwnerId: string, jobbeerId: string, status: string, reason: string): Promise<void> {
    try {
      // Récupérer le nom du jobbeur (offreur) au format Prénom N.
      const { data: jobbeurProfil } = await supabase
        .from('user_profil')
        .select('prenom, nom')
        .eq('user_id', jobbeerId)
        .single();
      
      // Déchiffrer les données de profil du jobbeur
      const decryptedJobbeurProfil = jobbeurProfil ? await decryptProfilDataAsync(jobbeurProfil) : null;
      
      const offreurName = decryptedJobbeurProfil
        ? `${decryptedJobbeurProfil.prenom || ''} ${decryptedJobbeurProfil.nom ? decryptedJobbeurProfil.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
        : '';
      // Notification pour le propriétaire de la mission
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: missionOwnerId,
          type: 'mission',
          title: 'Offre expirée',
          content: `L'offre de ${offreurName} pour la mission "${offer.user_missions.titre}" a été automatiquement ${reason} car vous n'avez pas répondu à l'offre dans le délai imparti (24h).`,
          link: `/dashboard/missions/offres?tab=1&mission=${offer.mission_id}`,
          is_read: false,
          is_archived: false
        }]);
      
      // Récupérer le nom du propriétaire de la mission (Prénom N.)
      const { data: ownerData } = await supabase
        .from('user_profil')
        .select('prenom, nom')
        .eq('user_id', missionOwnerId)
        .single();
      
      // Déchiffrer les données de profil du propriétaire
      const decryptedOwnerData = ownerData ? await decryptProfilDataAsync(ownerData) : null;
      
      const missionOwnerName = decryptedOwnerData
        ? `${decryptedOwnerData.prenom || ''} ${decryptedOwnerData.nom ? decryptedOwnerData.nom.charAt(0).toUpperCase() + '.' : ''}`.trim()
        : '';
      // Notification pour le jobbeur
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: jobbeerId,
          type: 'mission',
          title: 'Offre expirée',
          content: `Votre offre pour la mission "${offer.user_missions.titre}" a été automatiquement ${reason} car "${missionOwnerName}" n'a pas respecté le délai de réponse de 24h.`,
          link: `/dashboard/missions/offres?tab=0&mission=${offer.mission_id}`,
          is_read: false,
          is_archived: false
        }]);
    } catch (error) {
      logger.error(`Erreur lors de la création des notifications pour l'offre ${offer.id}:`, error);
    }
  }

  /**
   * Crée une notification de rappel pour l'utilisateur
   */
  private static async createReminderNotification(offer: any, userId: string, status: string, hoursRemaining: number): Promise<void> {
    try {
      let content = '';
      let link = '';
      
      if (status === 'en_attente' || status === 'contre_offre_jobbeur') {
        content = `Vous avez une offre en attente pour la mission "${offer.user_missions.titre}" qui expirera dans ${hoursRemaining} heures. Veuillez y répondre pour éviter qu'elle ne soit automatiquement refusée.`;
        link = `/dashboard/missions/offres?tab=1&mission=${offer.mission_id}`;
      } else if (status === 'contre_offre') {
        content = `Vous avez reçu une contre-offre pour la mission "${offer.user_missions.titre}" qui expirera dans ${hoursRemaining} heures. Veuillez y répondre pour éviter qu'elle ne soit automatiquement refusée.`;
        link = `/dashboard/missions/offres?tab=0&mission=${offer.mission_id}`;
      }
      
      await supabase
        .from('user_notifications')
        .insert([{
          user_id: userId,
          type: 'mission',
          title: 'Rappel : Offre en attente',
          content: content,
          link: link,
          is_read: false,
          is_archived: false
        }]);
    } catch (error) {
      logger.error(`Erreur lors de la création de la notification de rappel pour l'offre ${offer.id}:`, error);
    }
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  OfferExpirationChecker.checkExpiredOffers()
    .then(() => {
      logger.info('Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Erreur lors de l\'exécution du script:', error);
      process.exit(1);
    });
}