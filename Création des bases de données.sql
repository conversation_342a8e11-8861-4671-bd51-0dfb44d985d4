-- DAN<PERSON>R SUPPRESSION DES BASES !!!
-- <PERSON><PERSON><PERSON><PERSON> SUPPRESSION DES BASES !!!
-- <PERSON>AN<PERSON>R SUPPRESSION DES BASES !!!
-- DANGER SUPPRESSION DES BASES !!!
-- DANGER SUPPRESSION DES BASES !!!

DO $$
DECLARE
    r RECORD;
BEGIN
    -- Supprimer toutes les vues d'abord
    FOR r IN (SELECT table_name FROM information_schema.views WHERE table_schema = 'public') LOOP
        EXECUTE 'DROP VIEW IF EXISTS public.' || r.table_name || ' CASCADE';
    END LOOP;
    -- Parcours de toutes les tables et suppression de chaque table
    FOR r IN (SELECT table_name FROM information_schema.tables WHERE table_schema = 'public') LOOP
        EXECUTE 'DROP TABLE IF EXISTS public.' || r.table_name || ' CASCADE';
    END LOOP;
END $$;

-- Créer l'extension UUID si elle n'existe pas
create extension if not exists "uuid-ossp";

-- Nouvelle table pour stocker les appareils connus :
create table if not exists public.users (
  id uuid default uuid_generate_v4() primary key,
  email text unique not null,
  email_hash text, -- Hash SHA-256 de l'email pour recherche efficace sur données chiffrées
  password_hash text,
  temp_email text, -- Email temporaire en attente de vérification lors d'un changement d'adresse

  -- Authentification Google
  google_id text,
  google_data jsonb,

  -- États de vérification et activation
  profil_verifier boolean default false, -- Vérification manuelle des documents
  identite_verifier boolean default false, -- Vérification de l'identité
  entreprise_verifier boolean default false, -- Vérification de l'entreprise
  assurance_verifier boolean default false, -- Vérification de l'assurance
  email_verifier boolean default false,  -- Vérification de l'email par token
  profil_actif boolean default false,    -- Activation après vérification de l'email
  suspension_reason text,                -- Raison de la suspension (nullable)
  suspended_until timestamp with time zone, -- Date de fin de suspension (nullable, null = suspension définitive)
  notification_preferences jsonb default '{
    "email_enabled": true,
    "email_notifications": {
      "nouvelles_offres": true,
      "connexion": true,
      "messages": true,
      "evaluations": true,
      "missions": true,
      "paiements": true,
      "systeme": true,
      "badges": true
    },
    "sms_enabled": false,
    "app_enabled": true
  }'::jsonb, -- Préférences détaillées pour toutes les notifications

  -- Champs spécifiques pour les jobbeurs
  user_type text not null check (user_type in ('jobbeur', 'non-jobbeur')),

  -- Statut en ligne
  is_online boolean default false,
  last_activity timestamp with time zone default timezone('utc'::text, now()),

  -- Date d'inscription et autres
  date_inscription timestamp with time zone default timezone('utc'::text, now()),

  -- Gestion des tentatives de connexion
  login_attempts integer default 0,
  last_login_attempt timestamp with time zone,
  account_locked_until timestamp with time zone,

  -- Audit
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  last_login timestamp with time zone,

  -- Date d'expiration du mot de passe
  password_expires_at timestamp with time zone,

  -- Système de parrainage
  referral_code uuid default uuid_generate_v4(), -- Code unique de parrainage
  referred_by uuid references public.users(id), -- ID de l'utilisateur qui a parrainé
  referral_completed boolean default false, -- Indique si le parrainage a été complété (première mission validée)

  -- Champs d'anonymisation RGPD
  is_anonymized boolean default false, -- Indique si le compte utilisateur a été anonymisé suite à une demande de suppression RGPD
  anonymized_at timestamp with time zone -- Date et heure de l'anonymisation du compte

);

-- Ajouter la colonne role si elle n'existe pas déjà
ALTER TABLE public.users
ADD COLUMN role text CHECK (role IN ('jobpadm', 'jobmodo', 'jobutil')) DEFAULT 'jobutil';

-- Ajout de la colonne password_expires_at si elle n'existe pas déjà
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'password_expires_at'
    ) THEN
        ALTER TABLE users
        ADD COLUMN password_expires_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Ajouter les colonnes pour Google si elles n'existent pas déjà
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'google_id'
    ) THEN
        ALTER TABLE users
        ADD COLUMN google_id TEXT;
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'google_data'
    ) THEN
        ALTER TABLE users
        ADD COLUMN google_data JSONB;
    END IF;
END $$;

-- Ajout d'un index sur google_id
CREATE INDEX IF NOT EXISTS idx_users_google_id ON public.users(google_id);

-- Modification du password_hash pour être NULL pour les utilisateurs Google
ALTER TABLE public.users ALTER COLUMN password_hash DROP NOT NULL;

-- Mettre à jour les mots de passe existants pour expirer dans 180 jours
UPDATE users
SET password_expires_at = CURRENT_TIMESTAMP + INTERVAL '180 days'
WHERE password_expires_at IS NULL;


-- Ajouter les colonnes pour l'authentification à deux facteurs
ALTER TABLE public.users
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS two_factor_verified BOOLEAN DEFAULT FALSE;

-- Ajouter un index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_users_two_factor_enabled ON public.users(two_factor_enabled);


-- Table des réinitialisations de mot de passe
create table if not exists public.password_resets (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  token text unique not null,
  expires_at timestamp with time zone not null,
  created_at timestamp with time zone default timezone('utc'::text, now())
);

-- Table pour suivre les tentatives de réinitialisation de mot de passe
create table if not exists public.password_reset_attempts (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  attempt_count integer default 1,
  last_attempt timestamp with time zone default timezone('utc'::text, now()),
  cooldown_until timestamp with time zone,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les recherches fréquentes
create index if not exists idx_password_reset_attempts_user_id on public.password_reset_attempts(user_id);
create index if not exists idx_password_resets_user_id on public.password_resets(user_id);
create index if not exists idx_password_resets_token on public.password_resets(token);

-- Table des tokens d'authentification
create table if not exists public.auth_tokens (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  token text not null unique,
  type text not null check (type in ('email_verification', 'password_reset', 'two_factor_auth')),
  expires_at timestamp with time zone not null,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Fonction pour mettre à jour le timestamp 'updated_at'
create or replace function public.handle_updated_at()
returns trigger as $$
begin
    -- Vérifier si l'opération est UPDATE
    IF (TG_OP = 'UPDATE') THEN
        -- Utiliser un bloc EXCEPTION pour gérer le cas où NEW.updated_at n'existerait pas
        BEGIN
            -- Mettre à jour seulement si l'enregistrement a réellement changé (en excluant updated_at lui-même)
            IF (to_jsonb(NEW) - 'updated_at') IS DISTINCT FROM (to_jsonb(OLD) - 'updated_at') THEN
                NEW.updated_at = timezone('utc'::text, now());
            END IF;
        EXCEPTION
            -- Capturer l'erreur spécifique si NEW.updated_at n'existe pas
            WHEN undefined_column THEN
                -- Ne rien faire ou logger l'erreur si nécessaire
                -- RAISE NOTICE 'La colonne updated_at n''existe pas pour la table % dans ce contexte.', TG_TABLE_NAME;
                NULL; -- Ignorer l'erreur et continuer
            WHEN others THEN
                -- Relancer les autres erreurs potentielles
                RAISE;
        END;
    END IF;
    -- Renvoyer OLD pour DELETE, NEW pour INSERT/UPDATE
    IF (TG_OP = 'DELETE') THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
end;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Créer les triggers pour la mise à jour automatique de updated_at
DO $$
BEGIN
    -- Trigger pour users
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_users_updated_at') THEN
        CREATE TRIGGER handle_users_updated_at
            BEFORE UPDATE ON users
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour auth_tokens
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_auth_tokens_updated_at') THEN
        CREATE TRIGGER handle_auth_tokens_updated_at
            BEFORE UPDATE ON auth_tokens
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour password_reset_attempts
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_password_reset_attempts_updated_at') THEN
        CREATE TRIGGER handle_password_reset_attempts_updated_at
            BEFORE UPDATE ON password_reset_attempts
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Créer les index
create index if not exists users_email_idx on public.users (email);
create index if not exists users_email_hash_idx on public.users (email_hash);
create index if not exists users_email_verifier_idx on public.users (email_verifier);
create index if not exists users_profil_verifier_idx on public.users (profil_verifier);
create index if not exists users_profil_actif_idx on public.users (profil_actif);
create index if not exists users_temp_email_idx on public.users (temp_email);

create index if not exists auth_tokens_user_id_idx on public.auth_tokens (user_id);
create index if not exists auth_tokens_token_idx on public.auth_tokens (token);
create index if not exists auth_tokens_type_idx on public.auth_tokens (type);
create index if not exists auth_tokens_expires_at_idx on public.auth_tokens(expires_at);

-- Création des index pour les nouvelles colonnes
create index if not exists users_identite_verifier_idx on public.users (identite_verifier);
create index if not exists users_entreprise_verifier_idx on public.users (entreprise_verifier);
create index if not exists users_assurance_verifier_idx on public.users (assurance_verifier);

-- Index pour les champs d'anonymisation RGPD
create index if not exists users_is_anonymized_idx on public.users (is_anonymized);
create index if not exists users_anonymized_at_idx on public.users (anonymized_at);

-- Activer RLS sur la table users
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre la lecture des utilisateurs
CREATE POLICY "Les utilisateurs peuvent être lus par l'application"
ON public.users FOR SELECT
USING (true);

-- Politique pour permettre l'insertion des utilisateurs
CREATE POLICY "Les utilisateurs peuvent être insérés par l'application"
ON public.users FOR INSERT
WITH CHECK (true);

-- Politique pour permettre la mise à jour des utilisateurs
CREATE POLICY "Les utilisateurs peuvent être mis à jour par l'application"
ON public.users FOR UPDATE
USING (true)
WITH CHECK (true);

-- Politique pour permettre la suppression des utilisateurs
CREATE POLICY "Les utilisateurs peuvent être supprimés par l'application"
ON public.users FOR DELETE
USING (true);

-- S'assurer que le service role a tous les droits
GRANT ALL PRIVILEGES ON TABLE public.users TO service_role;

-- Policies de sécurité Row Level Security (RLS)
alter table public.auth_tokens enable row level security;
alter table public.password_resets enable row level security;
alter table public.password_reset_attempts enable row level security;

-- Policies pour la table users
create policy "Users can view public profils"
  on public.users for select
  using (profil_actif = true);

create policy "Users can update their own profil"
  on public.users for update
  using (auth.uid() = id);

create policy "Users can delete their own profil"
  on public.users for delete
  using (auth.uid() = id);

create policy "Users can insert new users"
  on public.users for insert
  with check (true);

-- Policies pour la table auth_tokens
create policy "Les tokens peuvent être insérés par l'application"
  on public.auth_tokens for insert
  with check (true);

create policy "Les tokens peuvent être lus par l'application"
  on public.auth_tokens for select
  using (true);

create policy "Les tokens peuvent être mis à jour par l'application"
  on public.auth_tokens for update
  using (true);

create policy "Les tokens peuvent être supprimés par l'application"
  on public.auth_tokens for delete
  using (true);

-- Policies pour la table password_resets
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_resets' AND policyname = 'Les réinitialisations de mot de passe peuvent être insérées par l''application'
    ) THEN
        create policy "Les réinitialisations de mot de passe peuvent être insérées par l'application"
          on public.password_resets for insert
          with check (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_resets' AND policyname = 'Les réinitialisations de mot de passe peuvent être lues par l''application'
    ) THEN
        create policy "Les réinitialisations de mot de passe peuvent être lues par l'application"
          on public.password_resets for select
          using (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_resets' AND policyname = 'Les réinitialisations de mot de passe peuvent être mises à jour par l''application'
    ) THEN
        create policy "Les réinitialisations de mot de passe peuvent être mises à jour par l'application"
          on public.password_resets for update
          using (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_resets' AND policyname = 'Les réinitialisations de mot de passe peuvent être supprimées par l''application'
    ) THEN
        create policy "Les réinitialisations de mot de passe peuvent être supprimées par l'application"
          on public.password_resets for delete
          using (true);
    END IF;
END $$;

-- Policies pour la table password_reset_attempts
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_reset_attempts' AND policyname = 'Les tentatives de réinitialisation de mot de passe peuvent être insérées par l''application'
    ) THEN
        create policy "Les tentatives de réinitialisation de mot de passe peuvent être insérées par l'application"
          on public.password_reset_attempts for insert
          with check (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_reset_attempts' AND policyname = 'Les tentatives de réinitialisation de mot de passe peuvent être lues par l''application'
    ) THEN
        create policy "Les tentatives de réinitialisation de mot de passe peuvent être lues par l'application"
          on public.password_reset_attempts for select
          using (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_reset_attempts' AND policyname = 'Les tentatives de réinitialisation de mot de passe peuvent être mises à jour par l''application'
    ) THEN
        create policy "Les tentatives de réinitialisation de mot de passe peuvent être mises à jour par l'application"
          on public.password_reset_attempts for update
          using (true);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'password_reset_attempts' AND policyname = 'Les tentatives de réinitialisation de mot de passe peuvent être supprimées par l''application'
    ) THEN
        create policy "Les tentatives de réinitialisation de mot de passe peuvent être supprimées par l'application"
          on public.password_reset_attempts for delete
          using (true);
    END IF;
END $$;

-- Fonction pour vérifier l'email de l'utilisateur
CREATE OR REPLACE FUNCTION verify_user_email(user_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE users
    SET
        email_verifier = true,
        profil_actif = true,
        updated_at = NOW()
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Accorder les permissions nécessaires
GRANT EXECUTE ON FUNCTION verify_user_email(UUID) TO service_role;

-- Wrapper toutes les politiques dans un bloc DO pour éviter les erreurs
DO $$
BEGIN
    -- Politiques pour auth_tokens
    DROP POLICY IF EXISTS "Les tokens peuvent être lus par l'application" ON public.auth_tokens;
    DROP POLICY IF EXISTS "Les tokens peuvent être insérés par l'application" ON public.auth_tokens;
    DROP POLICY IF EXISTS "Les tokens peuvent être mis à jour par l'application" ON public.auth_tokens;
    DROP POLICY IF EXISTS "Les tokens peuvent être supprimés par l'application" ON public.auth_tokens;

    CREATE POLICY "Les tokens peuvent être lus par l'application"
    ON public.auth_tokens FOR SELECT
    USING (true);

    CREATE POLICY "Les tokens peuvent être insérés par l'application"
    ON public.auth_tokens FOR INSERT
    WITH CHECK (true);

    CREATE POLICY "Les tokens peuvent être mis à jour par l'application"
    ON public.auth_tokens FOR UPDATE
    USING (true)
    WITH CHECK (true);

    CREATE POLICY "Les tokens peuvent être supprimés par l'application"
    ON public.auth_tokens FOR DELETE
    USING (true);

    -- Politiques pour password_resets
    DROP POLICY IF EXISTS "Les réinitialisations peuvent être lues par l'application" ON public.password_resets;
    DROP POLICY IF EXISTS "Les réinitialisations peuvent être insérées par l'application" ON public.password_resets;
    DROP POLICY IF EXISTS "Les réinitialisations peuvent être mises à jour par l'application" ON public.password_resets;
    DROP POLICY IF EXISTS "Les réinitialisations peuvent être supprimées par l'application" ON public.password_resets;

    CREATE POLICY "Les réinitialisations peuvent être lues par l'application"
    ON public.password_resets FOR SELECT
    USING (true);

    CREATE POLICY "Les réinitialisations peuvent être insérées par l'application"
    ON public.password_resets FOR INSERT
    WITH CHECK (true);

    CREATE POLICY "Les réinitialisations peuvent être mises à jour par l'application"
    ON public.password_resets FOR UPDATE
    USING (true)
    WITH CHECK (true);

    CREATE POLICY "Les réinitialisations peuvent être supprimées par l'application"
    ON public.password_resets FOR DELETE
    USING (true);

    -- Politiques pour password_reset_attempts
    DROP POLICY IF EXISTS "Les tentatives peuvent être lues par l'application" ON public.password_reset_attempts;
    DROP POLICY IF EXISTS "Les tentatives peuvent être insérées par l'application" ON public.password_reset_attempts;
    DROP POLICY IF EXISTS "Les tentatives peuvent être mises à jour par l'application" ON public.password_reset_attempts;
    DROP POLICY IF EXISTS "Les tentatives peuvent être supprimées par l'application" ON public.password_reset_attempts;

    CREATE POLICY "Les tentatives peuvent être lues par l'application"
    ON public.password_reset_attempts FOR SELECT
    USING (true);

    CREATE POLICY "Les tentatives peuvent être insérées par l'application"
    ON public.password_reset_attempts FOR INSERT
    WITH CHECK (true);

    CREATE POLICY "Les tentatives peuvent être mises à jour par l'application"
    ON public.password_reset_attempts FOR UPDATE
    USING (true)
    WITH CHECK (true);

    CREATE POLICY "Les tentatives peuvent être supprimées par l'application"
    ON public.password_reset_attempts FOR DELETE
    USING (true);
END
$$;

-- S'assurer que le service role a tous les droits sur toutes les tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;

-- Activer RLS sur toutes les tables
ALTER TABLE public.auth_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.password_resets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.password_reset_attempts ENABLE ROW LEVEL SECURITY;

-- Créer un index sur le type de token pour optimiser les recherches
CREATE INDEX IF NOT EXISTS idx_auth_tokens_type ON public.auth_tokens(type);

-- Créer un index sur la date d'expiration pour le nettoyage des tokens expirés
CREATE INDEX IF NOT EXISTS idx_auth_tokens_expires_at ON public.auth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_resets_expires_at ON public.password_resets(expires_at);

-- Créer la nouvelle politique pour auth_tokens (la supprimer avant si elle existe)
DROP POLICY IF EXISTS "Les tokens peuvent être supprimés par l'application" ON public.auth_tokens;

CREATE POLICY "Les tokens peuvent être supprimés par l'application"
ON public.auth_tokens FOR DELETE
USING (true);

-- Table des abonnements utilisateurs
create table if not exists public.user_abo (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    type_abonnement text not null check (type_abonnement in ('gratuit', 'basic', 'premium', 'pro')),
    date_debut timestamp with time zone default timezone('utc'::text, now()),
    date_fin timestamp with time zone not null,
    statut text not null check (statut in ('actif', 'inactif', 'en_attente', 'suspendu')),
    stripe_subscription_id text,
    stripe_customer_id text,
    montant decimal(10,2) default 0,
    renouvellement_auto boolean default false,
    options jsonb default '{}',
    payment_method_id text,
    last_payment_status text,
    last_payment_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now()),
    raison_annulation_abonnement text
);

-- Création des index
create index if not exists user_abo_user_id_idx on public.user_abo(user_id);
create index if not exists user_abo_stripe_subscription_id_idx on public.user_abo(stripe_subscription_id);
create index if not exists user_abo_stripe_customer_id_idx on public.user_abo(stripe_customer_id);
create index if not exists user_abo_statut_idx on public.user_abo(statut);
create index if not exists user_abo_type_abonnement_idx on public.user_abo(type_abonnement);

-- Table des profils utilisateurs
create table if not exists public.user_profil (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    slug text unique not null,
    nom text,
    prenom text,
    telephone text,
    telephone_prive boolean default true,
    numero text,
    adresse text,
    ville text,
    code_postal text,
    pays text,
    photo_url text,
    banner_url text,
    banner_position TEXT CHECK (banner_position IN ('top', 'center', 'bottom')) DEFAULT 'center',
    banner_position_offset INTEGER DEFAULT 0 CHECK (banner_position_offset >= -200 AND banner_position_offset <= 200),
    bio text,
    slogan text,
    storage_id VARCHAR(32) UNIQUE,
    mode_vacance boolean default false,
    intervention_zone jsonb DEFAULT '{"center": [48.8566, 2.3522], "radius": 15}'::jsonb,

    -- Champs pour les informations d'entreprise
    type_de_profil text,
    nom_entreprise text,
    prenom_entreprise text,
    statut_entreprise text,
    siren_entreprise text,
    code_ape_entreprise text,
    categorie_entreprise text,
    effectif_entreprise text,
    date_insee_creation_entreprise timestamp with time zone,
    date_categorie_entreprise timestamp with time zone,
    date_derniere_mise_a_jour_entreprise_insee timestamp with time zone,
    date_derniere_mise_a_jour_du_client_entreprise timestamp with time zone,
    -- Dates de validation des documents
    date_validation_document_assurance timestamp with time zone,
    date_validation_document_entreprise timestamp with time zone,
    date_validation_document_identite timestamp with time zone,

    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now()),
    profil_visible BOOLEAN DEFAULT TRUE,
    seo_indexable BOOLEAN DEFAULT FALSE,

    -- Champs d'anonymisation RGPD
    is_anonymized boolean default false, -- Indique si le profil utilisateur a été anonymisé suite à une demande de suppression RGPD
    anonymized_at timestamp with time zone -- Date et heure de l'anonymisation du profil
);

-- Ajouter des commentaires sur les colonnes
COMMENT ON COLUMN public.user_profil.slogan IS 'Slogan personnalisé de l''utilisateur';
COMMENT ON COLUMN public.user_profil.seo_indexable IS 'Autorisation du référencement du profil sur les moteurs de recherche';
COMMENT ON COLUMN public.user_profil.is_anonymized IS 'Indique si le profil utilisateur a été anonymisé suite à une demande de suppression RGPD';
COMMENT ON COLUMN public.user_profil.anonymized_at IS 'Date et heure de l''anonymisation du profil';

-- Commentaires pour les champs d'anonymisation dans la table users
COMMENT ON COLUMN public.users.is_anonymized IS 'Indique si le compte utilisateur a été anonymisé suite à une demande de suppression RGPD';
COMMENT ON COLUMN public.users.anonymized_at IS 'Date et heure de l''anonymisation du compte';

-- Table des demandes de suppression de compte
create table if not exists public.account_deletion_requests (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    token text not null unique,
    expires_at timestamp with time zone not null,
    used boolean default false,
    used_at timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les demandes de suppression
create index if not exists account_deletion_requests_user_id_idx on public.account_deletion_requests(user_id);
create index if not exists account_deletion_requests_token_idx on public.account_deletion_requests(token);
create index if not exists account_deletion_requests_expires_at_idx on public.account_deletion_requests(expires_at);
create index if not exists account_deletion_requests_used_idx on public.account_deletion_requests(used);

-- Commentaires pour la table account_deletion_requests
COMMENT ON TABLE public.account_deletion_requests IS 'Table des demandes de suppression de compte avec tokens de confirmation';
COMMENT ON COLUMN public.account_deletion_requests.token IS 'Token unique de confirmation de suppression';
COMMENT ON COLUMN public.account_deletion_requests.expires_at IS 'Date d''expiration du token (24h)';
COMMENT ON COLUMN public.account_deletion_requests.used IS 'Indique si le token a été utilisé';
COMMENT ON COLUMN public.account_deletion_requests.used_at IS 'Date d''utilisation du token';

create index if not exists user_profil_user_id_idx on public.user_profil(user_id);
create index if not exists user_profil_seo_indexable_idx on public.user_profil(seo_indexable);
create index if not exists user_profil_sitemap_idx on public.user_profil(profil_visible, seo_indexable) WHERE profil_visible = true AND seo_indexable = true;

-- Index pour les champs d'anonymisation RGPD dans user_profil
create index if not exists user_profil_is_anonymized_idx on public.user_profil(is_anonymized);
create index if not exists user_profil_anonymized_at_idx on public.user_profil(anonymized_at);

-- Table pour le registre de consentement SEO (RGPD)
create table if not exists public.user_seo_consent (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    consent_given boolean not null,
    consent_date timestamp with time zone default timezone('utc'::text, now()),
    ip_address inet,
    user_agent text,
    consent_method text check (consent_method in ('settings_page', 'onboarding', 'notification', 'popup', 'email_campaign')),
    revoked_date timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour le registre de consentement
create index if not exists user_seo_consent_user_id_idx on public.user_seo_consent(user_id);
create index if not exists user_seo_consent_date_idx on public.user_seo_consent(consent_date);

-- Activation de RLS sur la table user_seo_consent
ALTER TABLE public.user_seo_consent ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_seo_consent
CREATE POLICY "Les consentements peuvent être lus par l'utilisateur propriétaire"
ON public.user_seo_consent FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les consentements peuvent être créés par l'application"
ON public.user_seo_consent FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les consentements peuvent être mis à jour par l'application"
ON public.user_seo_consent FOR UPDATE
USING (auth.uid() = user_id);

-- Table des transactions utilisateurs
create table if not exists public.user_transac (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    date timestamp with time zone default timezone('utc'::text, now()),
    type text not null check (type in ('paiement', 'remboursement', 'commission', 'abonnement', 'transfert')),
    montant decimal(10,2) not null,
    client text,
    mission text,
    description text,
    statut text not null check (statut in ('en_attente', 'complete', 'refuse')),
    methode text not null check (methode in ('jobi', 'carte', 'virement', 'especes', 'cheque', 'autre')),
    reference text,
    categorie text not null check (categorie in ('mission', 'transfert', 'abonnement', 'rechargement', 'remboursement', 'autre', 'facture')),
    imported boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Table des services utilisateurs
create table if not exists public.user_services (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    titre text not null,
    description text not null,
    categorie text,
    disponible boolean default true,
    statut text not null check (statut in ('actif', 'inactif')),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now()),
    tarif_horaire decimal(10,2) not null,
    horaires jsonb,
    category_id text,
    subcategory_id text
);

-- Table des missions
create table if not exists public.user_missions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    titre text not null,
    description text not null,
    category_id text not null,
    subcategory_id text not null,
    budget decimal(10,2),
    statut text not null check (statut in ('en_attente', 'en_cours', 'terminee', 'annulee', 'en_moderation')) default 'en_cours',
    intervention_zone jsonb DEFAULT '{"center": [48.8566, 2.3522], "radius": 15}'::jsonb,
    adresse text,
    ville text,
    code_postal text,
    date_mission timestamp with time zone,
    date_creation timestamp with time zone default timezone('utc'::text, now()),
    date_modification timestamp with time zone default timezone('utc'::text, now()),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now()),
    time_slots jsonb DEFAULT '[]'::jsonb,
    has_time_preference boolean default false,
    budget_defini BOOLEAN DEFAULT true,
    duree_estimee integer,
    pays text,
    payment_method text check (payment_method in ('jobi_only', 'both', 'direct_only')),
    is_closed boolean not null default false,
    is_urgent boolean default false
);

-- Table des avis utilisateurs
CREATE TABLE IF NOT EXISTS user_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    author_id UUID NOT NULL REFERENCES users(id),
    target_user_id UUID NOT NULL REFERENCES users(id),
    mission_id UUID REFERENCES user_missions(id),
    note INTEGER NOT NULL CHECK (note >= 1 AND note <= 5),
    commentaire TEXT,
    qualites TEXT[],
    defauts TEXT[] DEFAULT '{}',
    reponse TEXT,
    reponse_date TIMESTAMP WITH TIME ZONE,
    photos TEXT[], -- URLs des photos jointes à l'avis (max 4)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE,
    is_modified BOOLEAN DEFAULT FALSE,
    statut TEXT DEFAULT 'visible' CHECK (statut IN ('visible', 'attente_moderation', 'masqué', 'supprimé')),
    CONSTRAINT fk_author FOREIGN KEY (author_id) REFERENCES users(id),
    CONSTRAINT fk_target_user FOREIGN KEY (target_user_id) REFERENCES users(id),
    CONSTRAINT fk_mission FOREIGN KEY (mission_id) REFERENCES user_missions(id)
);

-- Index pour les avis
CREATE INDEX IF NOT EXISTS idx_user_reviews_author_id ON user_reviews(author_id);
CREATE INDEX IF NOT EXISTS idx_user_reviews_target_user_id ON user_reviews(target_user_id);
CREATE INDEX IF NOT EXISTS idx_user_reviews_mission_id ON user_reviews(mission_id);
CREATE INDEX IF NOT EXISTS idx_user_reviews_created_at ON user_reviews(created_at);
CREATE INDEX IF NOT EXISTS idx_user_reviews_defauts ON user_reviews(defauts);

-- Trigger pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_reviews_updated_at
    BEFORE UPDATE ON user_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Création des triggers pour updated_at
DO $$
BEGIN
    -- Trigger pour user_abo
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_abo_updated_at') THEN
        CREATE TRIGGER handle_user_abo_updated_at
            BEFORE UPDATE ON user_abo
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour user_profil
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_profil_updated_at') THEN
        CREATE TRIGGER handle_user_profil_updated_at
            BEFORE UPDATE ON user_profil
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour user_transac
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_transac_updated_at') THEN
        CREATE TRIGGER handle_user_transac_updated_at
            BEFORE UPDATE ON user_transac
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour user_services
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_services_updated_at') THEN
        CREATE TRIGGER handle_user_services_updated_at
            BEFORE UPDATE ON user_services
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour user_reviews
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_reviews_updated_at') THEN
        CREATE TRIGGER handle_user_reviews_updated_at
            BEFORE UPDATE ON user_reviews
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Création des index
create index if not exists user_abo_user_id_idx on public.user_abo(user_id);
create index if not exists user_profil_user_id_idx on public.user_profil(user_id);
create index if not exists user_transac_user_id_idx on public.user_transac(user_id);
create index if not exists user_services_user_id_idx on public.user_services(user_id);

create index if not exists user_reviews_target_user_id_idx on public.user_reviews(target_user_id);
create index if not exists user_reviews_mission_id_idx on public.user_reviews(mission_id);
CREATE INDEX IF NOT EXISTS user_reviews_author_id_idx ON public.user_reviews(author_id);
CREATE INDEX IF NOT EXISTS user_reviews_mission_id_idx ON public.user_reviews(mission_id);

-- Activation de RLS sur les nouvelles tables
ALTER TABLE public.user_abo ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profil ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_transac ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.account_deletion_requests ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_abo
CREATE POLICY "Les abonnements peuvent être lus par l'utilisateur propriétaire"
ON public.user_abo FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les abonnements peuvent être créés par l'application"
ON public.user_abo FOR INSERT
WITH CHECK (true);

CREATE POLICY "Les abonnements peuvent être mis à jour par l'application"
ON public.user_abo FOR UPDATE
USING (true);

-- Politiques de sécurité pour user_profil
CREATE POLICY "Les profils peuvent être lus par tous"
ON public.user_profil FOR SELECT
USING (true);

CREATE POLICY "Les profils peuvent être modifiés par leur propriétaire"
ON public.user_profil FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les profils peuvent être créés par l'application"
ON public.user_profil FOR INSERT
WITH CHECK (true);

-- Politiques de sécurité pour user_transac
CREATE POLICY "Les transactions peuvent être lues par l'utilisateur propriétaire"
ON public.user_transac FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les transactions peuvent être créées par l'application"
ON public.user_transac FOR INSERT
WITH CHECK (true);

-- Politiques de sécurité pour user_services
CREATE POLICY "Les services peuvent être lus par tous"
ON public.user_services FOR SELECT
USING (true);

CREATE POLICY "Les services peuvent être modifiés par leur propriétaire"
ON public.user_services FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les services peuvent être créés par l'utilisateur authentifié"
ON public.user_services FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Politiques de sécurité pour user_reviews
CREATE POLICY "Les avis peuvent être lus par tous"
ON public.user_reviews FOR SELECT
USING (true);

CREATE POLICY "Les avis peuvent être créés par les utilisateurs authentifiés"
ON public.user_reviews FOR INSERT
WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Les avis peuvent être modifiés par leur auteur"
ON public.user_reviews FOR UPDATE
USING (auth.uid() = author_id);

-- Politiques de sécurité pour account_deletion_requests
CREATE POLICY "Les demandes de suppression peuvent être lues par l'application"
ON public.account_deletion_requests FOR SELECT
USING (true);

CREATE POLICY "Les demandes de suppression peuvent être créées par l'application"
ON public.account_deletion_requests FOR INSERT
WITH CHECK (true);

CREATE POLICY "Les demandes de suppression peuvent être mises à jour par l'application"
ON public.account_deletion_requests FOR UPDATE
USING (true);

-- Table des jobis utilisateurs
create table if not exists public.user_jobi_historique (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    titre text not null,
    description text not null,
    montant decimal(10,2) not null,
    message text, -- Message optionnel pour les transferts entre utilisateurs
    date_creation timestamp with time zone default timezone('utc'::text, now()),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Création des triggers pour updated_at
DO $$
BEGIN
    -- Trigger pour user_jobi_historique
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_jobi_updated_at') THEN
        CREATE TRIGGER handle_user_jobi_updated_at
            BEFORE UPDATE ON user_jobi_historique
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Création des index
create index if not exists user_jobi_user_id_idx on public.user_jobi_historique(user_id);
create index if not exists user_jobi_date_creation_idx on public.user_jobi_historique(date_creation);

-- Activation de RLS sur la table user_jobi_historique
ALTER TABLE public.user_jobi_historique ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_jobi_historique
CREATE POLICY "Les jobis peuvent être lus par l'utilisateur propriétaire"
ON public.user_jobi_historique FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les jobis peuvent être créés par l'application"
ON public.user_jobi_historique FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les jobis peuvent être mis à jour par l'application"
ON public.user_jobi_historique FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les jobis peuvent être supprimés par l'utilisateur propriétaire"
ON public.user_jobi_historique FOR DELETE
USING (auth.uid() = user_id);

-- Table des jobis utilisateurs actifs
create table if not exists public.user_jobi (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    montant decimal(10,2) not null,
    profil_complet boolean default false,
    date_creation timestamp with time zone default timezone('utc'::text, now()),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now()),
    UNIQUE(user_id)
);

-- Création des triggers pour updated_at
DO $$
BEGIN
    -- Trigger pour user_jobi
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_jobi_main_updated_at') THEN
        CREATE TRIGGER handle_user_jobi_main_updated_at
            BEFORE UPDATE ON user_jobi
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Création des index
create index if not exists user_jobi_main_user_id_idx on public.user_jobi(user_id);
create index if not exists user_jobi_main_date_creation_idx on public.user_jobi(date_creation);

-- Activation de RLS sur la table user_jobi
ALTER TABLE public.user_jobi ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_jobi
CREATE POLICY "Les jobis actifs peuvent être lus par l'utilisateur propriétaire"
ON public.user_jobi FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les jobis actifs peuvent être créés par l'application"
ON public.user_jobi FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les jobis actifs peuvent être mis à jour par l'application"
ON public.user_jobi FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les jobis actifs peuvent être supprimés par l'utilisateur propriétaire"
ON public.user_jobi FOR DELETE
USING (auth.uid() = user_id);

-- Activer RLS sur le bucket photo_profil
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs authentifiés de télécharger des fichiers
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow authenticated users to upload files' AND tablename = 'objects') THEN
        CREATE POLICY "Allow authenticated users to upload files"
        ON storage.objects
        FOR INSERT
        WITH CHECK (bucket_id = 'photo_profil' AND auth.role() = 'authenticated');
    END IF;
END$$;

-- Politique pour permettre l'accès public en lecture
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow public read access' AND tablename = 'objects') THEN
        CREATE POLICY "Allow public read access"
        ON storage.objects
        FOR SELECT
        USING (bucket_id = 'photo_profil');
    END IF;
END$$;

-- Politique pour permettre aux utilisateurs de supprimer leurs propres fichiers
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow users to delete their own files' AND tablename = 'objects') THEN
        CREATE POLICY "Allow users to delete their own files"
        ON storage.objects
        FOR DELETE
        USING (bucket_id = 'photo_profil' AND auth.uid()::text = owner_id);
    END IF;
END$$;

-- Modification de la table user_services pour inclure les catégories
ALTER TABLE public.user_services
ADD COLUMN IF NOT EXISTS tarif_horaire decimal(10,2),
ADD COLUMN IF NOT EXISTS horaires jsonb;

-- Activation de RLS pour les tables
ALTER TABLE public.user_services ENABLE ROW LEVEL SECURITY;

-- Politiques pour user_services
CREATE POLICY "Lecture publique des services" ON public.user_services
  FOR SELECT USING (true);

CREATE POLICY "Création de services par l'utilisateur" ON public.user_services
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Modification de ses propres services" ON public.user_services
  FOR UPDATE USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Suppression de ses propres services" ON public.user_services
  FOR DELETE USING (auth.uid() = user_id);

-- Table des galeries de réalisations
create table if not exists public.user_gallery (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    name text not null,
    description text,
    cover_image text,
    status text not null default 'actif' check (status in ('actif', 'inactif')),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Table des photos de réalisations
create table if not exists public.user_gallery_photos (
    id uuid default uuid_generate_v4() primary key,
    gallery_id uuid references public.user_gallery(id) on delete cascade,
    user_id uuid references public.users(id) on delete cascade,
    photo_url text not null,
    caption text,
    order_index integer not null default 0,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Création des triggers pour updated_at
DO $$
BEGIN
    -- Trigger pour user_gallery
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_gallery_updated_at') THEN
        CREATE TRIGGER handle_user_gallery_updated_at
            BEFORE UPDATE ON user_gallery
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;

    -- Trigger pour user_gallery_photos
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_gallery_photos_updated_at') THEN
        CREATE TRIGGER handle_user_gallery_photos_updated_at
            BEFORE UPDATE ON user_gallery_photos
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Création des index
create index if not exists user_gallery_user_id_idx on public.user_gallery(user_id);
create index if not exists user_gallery_status_idx on public.user_gallery(status);
create index if not exists user_gallery_photos_gallery_id_idx on public.user_gallery_photos(gallery_id);
create index if not exists user_gallery_photos_user_id_idx on public.user_gallery_photos(user_id);
create index if not exists user_gallery_photos_order_idx on public.user_gallery_photos(order_index);

-- Activation de RLS sur les tables
ALTER TABLE public.user_gallery ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_gallery_photos ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_gallery
CREATE POLICY "Les galeries peuvent être lues par tous"
ON public.user_gallery FOR SELECT
USING (status = 'actif');

CREATE POLICY "Les galeries inactives peuvent être lues par leur propriétaire"
ON public.user_gallery FOR SELECT
USING (auth.uid() = user_id AND status = 'inactif');

CREATE POLICY "Les galeries peuvent être créées par l'utilisateur authentifié"
ON public.user_gallery FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les galeries peuvent être modifiées par leur propriétaire"
ON public.user_gallery FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les galeries peuvent être supprimées par leur propriétaire"
ON public.user_gallery FOR DELETE
USING (auth.uid() = user_id);

-- Politiques de sécurité pour user_gallery_photos
CREATE POLICY "Les photos peuvent être lues par tous"
ON public.user_gallery_photos FOR SELECT
USING (true);

CREATE POLICY "Les photos peuvent être ajoutées par le propriétaire de la galerie"
ON public.user_gallery_photos FOR INSERT
WITH CHECK (EXISTS (
    SELECT 1 FROM public.user_gallery
    WHERE id = gallery_id AND user_id = auth.uid()
));

CREATE POLICY "Les photos peuvent être modifiées par le propriétaire de la galerie"
ON public.user_gallery_photos FOR UPDATE
USING (EXISTS (
    SELECT 1 FROM public.user_gallery
    WHERE id = gallery_id AND user_id = auth.uid()
));

CREATE POLICY "Les photos peuvent être supprimées par le propriétaire de la galerie"
ON public.user_gallery_photos FOR DELETE
USING (EXISTS (
    SELECT 1 FROM public.user_gallery
    WHERE id = gallery_id AND user_id = auth.uid()
));

-- Activer RLS sur le bucket galerie_realisation_client
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow authenticated users to upload gallery photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow authenticated users to upload gallery photos"
        ON storage.objects
        FOR INSERT
        WITH CHECK (bucket_id = 'galerie_realisation_client' AND auth.role() = 'authenticated');
    END IF;
END$$;

-- Politique pour permettre l'accès public en lecture aux photos de galerie
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow public read access to gallery photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow public read access to gallery photos"
        ON storage.objects
        FOR SELECT
        USING (bucket_id = 'galerie_realisation_client');
    END IF;
END$$;

-- Politique pour permettre aux utilisateurs de supprimer leurs propres photos
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow users to delete their own gallery photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow users to delete their own gallery photos"
        ON storage.objects
        FOR DELETE
        USING (bucket_id = 'galerie_realisation_client' AND auth.uid()::text = owner_id);
    END IF;
END$$;

-- ========================================
-- CRÉATION ET POLITIQUES RLS POUR LE BUCKET avis_photos_client
-- ========================================

-- Création du bucket pour les photos d'avis clients
INSERT INTO storage.buckets (id, name, public)
VALUES ('avis_photos_client', 'avis_photos_client', true)
ON CONFLICT (id) DO NOTHING;

-- Activer RLS sur le bucket avis_photos_client
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow authenticated users to upload review photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow authenticated users to upload review photos"
        ON storage.objects
        FOR INSERT
        WITH CHECK (bucket_id = 'avis_photos_client' AND auth.role() = 'authenticated');
    END IF;
END$$;

-- Politique pour permettre l'accès public en lecture aux photos d'avis
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow public read access to review photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow public read access to review photos"
        ON storage.objects
        FOR SELECT
        USING (bucket_id = 'avis_photos_client');
    END IF;
END$$;

-- Politique pour permettre aux utilisateurs de supprimer leurs propres photos d'avis
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow users to delete their own review photos' AND tablename = 'objects') THEN
        CREATE POLICY "Allow users to delete their own review photos"
        ON storage.objects
        FOR DELETE
        USING (bucket_id = 'avis_photos_client' AND auth.uid()::text = owner_id);
    END IF;
END$$;

-- Table des photos mises en avant
create table if not exists public.user_featured_photos (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  photo_url text not null,
  storage_path text not null,
  caption text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les photos mises en avant
create index if not exists idx_user_featured_photos_user_id on public.user_featured_photos(user_id);

-- Trigger pour mettre à jour updated_at
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_featured_photos_updated_at') THEN
        CREATE TRIGGER handle_user_featured_photos_updated_at
            BEFORE UPDATE ON user_featured_photos
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- RLS pour les photos mises en avant
alter table public.user_featured_photos enable row level security;

create policy "Users can view their own featured photos"
  on public.user_featured_photos
  for select
  using (auth.uid() = user_id);

create policy "Users can insert their own featured photos"
  on public.user_featured_photos
  for insert
  with check (auth.uid() = user_id);

create policy "Users can update their own featured photos"
  on public.user_featured_photos
  for update
  using (auth.uid() = user_id)
  with check (auth.uid() = user_id);

create policy "Users can delete their own featured photos"
  on public.user_featured_photos
  for delete
  using (auth.uid() = user_id);

-- Table pour stocker l'historique des connexions
create table if not exists public.user_login_history (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    ip_address text not null,
    login_date timestamp with time zone default timezone('utc'::text, now()),
    city text,
    country text,
    region text,
    postal_code text,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour optimiser les requêtes
create index if not exists idx_user_login_history_user_id on public.user_login_history(user_id);
create index if not exists idx_user_login_history_login_date on public.user_login_history(login_date);

-- Trigger pour updated_at
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_login_history_updated_at') THEN
        CREATE TRIGGER handle_user_login_history_updated_at
            BEFORE UPDATE ON user_login_history
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Activation de RLS
ALTER TABLE public.user_login_history ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les historiques de connexion peuvent être lus par l'utilisateur propriétaire"
ON public.user_login_history FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les historiques de connexion peuvent être créés par l'application"
ON public.user_login_history FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Ajouter un index sur le slug
create index if not exists user_profil_slug_idx on public.user_profil(slug);

-- Création de la table des badges obtenus
create table if not exists public.user_badges (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    badge_id text not null,
    recompense_recu boolean default false,
    is_lifetime boolean default false,
    date_obtention timestamp with time zone default timezone('utc'::text, now()),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour optimiser les requêtes
create index if not exists idx_user_badges_user_id on public.user_badges(user_id);
create index if not exists idx_user_badges_badge_id on public.user_badges(badge_id);

-- Trigger pour updated_at
CREATE TRIGGER handle_user_badges_updated_at
    BEFORE UPDATE ON user_badges
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.user_badges ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les badges peuvent être lus par tous"
ON public.user_badges FOR SELECT
USING (true);

CREATE POLICY "Les badges peuvent être modifiés par l'application"
ON public.user_badges FOR UPDATE
USING (true);

CREATE POLICY "Les badges peuvent être créés par l'application"
ON public.user_badges FOR INSERT
WITH CHECK (true);

-- Index pour les missions
create index if not exists idx_user_missions_user_id on public.user_missions(user_id);
create index if not exists idx_user_missions_category_id on public.user_missions(category_id);
create index if not exists idx_user_missions_statut on public.user_missions(statut);
create index if not exists idx_user_missions_date_mission on public.user_missions(date_mission);

-- Trigger pour updated_at
CREATE TRIGGER handle_user_missions_updated_at
    BEFORE UPDATE ON user_missions
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.user_missions ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour les missions
CREATE POLICY "Les missions peuvent être lues par tous"
ON public.user_missions FOR SELECT
USING (true);

CREATE POLICY "Les missions peuvent être créées par l'utilisateur authentifié"
ON public.user_missions FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les missions peuvent être modifiées par leur propriétaire"
ON public.user_missions FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les missions peuvent être supprimées par leur propriétaire"
ON public.user_missions FOR DELETE
USING (auth.uid() = user_id);

-- Table des candidatures aux missions
create table if not exists public.user_mission_candidature (
    id uuid default uuid_generate_v4() primary key,
    mission_id uuid references public.user_missions(id) on delete cascade,
    jobbeur_id uuid references public.users(id) on delete cascade,
    statut text not null check (statut in ('en_attente', 'acceptée', 'refusée', 'contre_offre', 'contre_offre_jobbeur')) default 'en_attente',
    message text,
    montant_propose numeric(10,2),
    montant_contre_offre numeric(10,2),
    message_contre_offre text,
    date_contre_offre timestamp with time zone,
    montant_contre_offre_jobbeur numeric(10,2),
    message_contre_offre_jobbeur text,
    date_contre_offre_jobbeur timestamp with time zone,
    date_refus timestamp with time zone,
    date_acceptation timestamp with time zone,
    first_response_date timestamp with time zone, -- Date de la première réponse (changement depuis en_attente)
    montant_paiement numeric(10,2),
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les candidatures
create index if not exists idx_user_mission_candidature_mission_id on public.user_mission_candidature(mission_id);
create index if not exists idx_user_mission_candidature_jobbeur_id on public.user_mission_candidature(jobbeur_id);
create index if not exists idx_user_mission_candidature_statut on public.user_mission_candidature(statut);
create index if not exists idx_user_mission_candidature_first_response on public.user_mission_candidature(first_response_date);

-- Trigger pour mettre à jour first_response_date
CREATE OR REPLACE FUNCTION update_first_response_date()
RETURNS TRIGGER AS $$
BEGIN
    -- Si le statut change de 'en_attente' vers un autre statut et first_response_date est null
    IF OLD.statut = 'en_attente' AND NEW.statut != 'en_attente' AND NEW.first_response_date IS NULL THEN
        NEW.first_response_date := timezone('utc'::text, now());
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

CREATE TRIGGER set_first_response_date
    BEFORE UPDATE ON user_mission_candidature
    FOR EACH ROW
    EXECUTE FUNCTION update_first_response_date();

-- Trigger pour updated_at
CREATE TRIGGER handle_user_mission_candidature_updated_at
    BEFORE UPDATE ON user_mission_candidature
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.user_mission_candidature ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour les candidatures
CREATE POLICY "Les candidatures peuvent être lues par le propriétaire de la mission ou le candidat"
ON public.user_mission_candidature FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.user_missions
        WHERE id = mission_id AND user_id = auth.uid()
    ) OR jobbeur_id = auth.uid()
);

CREATE POLICY "Les candidatures peuvent être créées par l'utilisateur authentifié"
ON public.user_mission_candidature FOR INSERT
WITH CHECK (jobbeur_id = auth.uid());

CREATE POLICY "Les candidatures peuvent être modifiées par le propriétaire de la mission ou le candidat"
ON public.user_mission_candidature FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.user_missions
        WHERE id = mission_id AND user_id = auth.uid()
    ) OR jobbeur_id = auth.uid()
);

-- Création de la table pour les photos de mission
create table if not exists public.user_mission_photos (
    id uuid default uuid_generate_v4() primary key,
    mission_id uuid references public.user_missions(id) on delete cascade,
    user_id uuid references public.users(id) on delete cascade,
    photo_url text not null,
    order_index integer not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Index pour les photos de mission
create index if not exists idx_user_mission_photos_mission_id on public.user_mission_photos(mission_id);
create index if not exists idx_user_mission_photos_user_id on public.user_mission_photos(user_id);
create index if not exists idx_user_mission_photos_order on public.user_mission_photos(order_index);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_photos_updated_at
    BEFORE UPDATE ON user_mission_photos
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Policies de sécurité
alter table public.user_mission_photos enable row level security;

CREATE POLICY "Les utilisateurs peuvent voir leurs photos de mission"
ON public.user_mission_photos FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent ajouter leurs photos de mission"
ON public.user_mission_photos FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent modifier leurs photos de mission"
ON public.user_mission_photos FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs photos de mission"
ON public.user_mission_photos FOR DELETE
USING (auth.uid() = user_id);

-- Table pour les rejets de missions
create table if not exists public.user_mission_rejections (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    mission_id uuid references public.user_missions(id) on delete cascade,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les rejets de missions
create index if not exists idx_user_mission_rejections_user_id on public.user_mission_rejections(user_id);
create index if not exists idx_user_mission_rejections_mission_id on public.user_mission_rejections(mission_id);

-- Trigger pour updated_at
CREATE TRIGGER handle_user_mission_rejections_updated_at
    BEFORE UPDATE ON user_mission_rejections
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.user_mission_rejections ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour les rejets de missions
CREATE POLICY "Les rejets peuvent être lus par l'utilisateur propriétaire"
ON public.user_mission_rejections FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les rejets peuvent être créés par l'utilisateur authentifié"
ON public.user_mission_rejections FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les rejets peuvent être supprimés par l'utilisateur propriétaire"
ON public.user_mission_rejections FOR DELETE
USING (auth.uid() = user_id);

-- Table pour les likes des missions
create table if not exists public.user_mission_likes (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    mission_id uuid references public.user_missions(id) on delete cascade,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
    unique(user_id, mission_id)
);

-- Index pour les likes
create index if not exists idx_user_mission_likes_user_id on public.user_mission_likes(user_id);
create index if not exists idx_user_mission_likes_mission_id on public.user_mission_likes(mission_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_likes_updated_at
    BEFORE UPDATE ON user_mission_likes
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Table pour les commentaires des missions
create table if not exists public.user_mission_comments (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade not null,
    mission_id uuid references public.user_missions(id) on delete cascade not null,
    comment text not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    is_private boolean default false not null,
    parent_id uuid references public.user_mission_comments(id) on delete cascade,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
    comment_original TEXT NULL
);

-- Index pour les commentaires
create index if not exists idx_user_mission_comments_user_id on public.user_mission_comments(user_id);
create index if not exists idx_user_mission_comments_mission_id on public.user_mission_comments(mission_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_comments_updated_at
    BEFORE UPDATE ON user_mission_comments
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Table pour les recommandations des missions
create table if not exists public.user_mission_recommendations (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    mission_id uuid references public.user_missions(id) on delete cascade,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
    unique(user_id, mission_id)
);

-- Index pour les recommandations
create index if not exists idx_user_mission_recommendations_user_id on public.user_mission_recommendations(user_id);
create index if not exists idx_user_mission_recommendations_mission_id on public.user_mission_recommendations(mission_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_recommendations_updated_at
    BEFORE UPDATE ON user_mission_recommendations
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Policies de sécurité pour les likes
alter table public.user_mission_likes enable row level security;

CREATE POLICY "Les likes peuvent être lus par tous"
ON public.user_mission_likes FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent liker les missions"
ON public.user_mission_likes FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent retirer leurs likes"
ON public.user_mission_likes FOR DELETE
USING (auth.uid() = user_id);

-- Policies de sécurité pour les commentaires
alter table public.user_mission_comments enable row level security;

CREATE POLICY "Les commentaires peuvent être lus par tous"
ON public.user_mission_comments FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent commenter les missions"
ON public.user_mission_comments FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent modifier leurs commentaires"
ON public.user_mission_comments FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs commentaires"
ON public.user_mission_comments FOR DELETE
USING (auth.uid() = user_id);

-- Policies de sécurité pour les recommandations
alter table public.user_mission_recommendations enable row level security;

CREATE POLICY "Les recommandations peuvent être lues par tous"
ON public.user_mission_recommendations FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent recommander les missions"
ON public.user_mission_recommendations FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent retirer leurs recommandations"
ON public.user_mission_recommendations FOR DELETE
USING (auth.uid() = user_id);

-- Table des favoris utilisateurs
create table if not exists public.user_favorites (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  favorite_user_id uuid references public.users(id) on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now()),
  UNIQUE(user_id, favorite_user_id)
);

-- Index pour les favoris
create index if not exists idx_user_favorites_user_id on public.user_favorites(user_id);
create index if not exists idx_user_favorites_favorite_user_id on public.user_favorites(favorite_user_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_favorites_updated_at
    BEFORE UPDATE ON user_favorites
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Policies pour la table user_favorites
alter table public.user_favorites enable row level security;

create policy "Les utilisateurs peuvent voir leurs favoris"
  on public.user_favorites for select
  using (auth.uid() = user_id);

create policy "Les utilisateurs peuvent ajouter des favoris"
  on public.user_favorites for insert
  with check (auth.uid() = user_id);

create policy "Les utilisateurs peuvent supprimer leurs favoris"
  on public.user_favorites for delete
  using (auth.uid() = user_id);

-- Table des notifications utilisateur
create table if not exists public.user_notifications (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  type text not null check (type in ('jobi', 'message', 'mission', 'system', 'mission_comment', 'profile', 'bug_report', 'invoice')),
  title text not null,
  content text not null,
  is_read boolean default false,
  is_archived boolean default false,
  link text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les notifications
create index if not exists idx_user_notifications_user_id on public.user_notifications(user_id);
create index if not exists idx_user_notifications_type on public.user_notifications(type);
create index if not exists idx_user_notifications_is_read on public.user_notifications(is_read);
create index if not exists idx_user_notifications_is_archived on public.user_notifications(is_archived);
create index if not exists idx_user_notifications_created_at on public.user_notifications(created_at);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_notifications_updated_at
    BEFORE UPDATE ON user_notifications
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- RLS policies pour les notifications
alter table public.user_notifications enable row level security;

create policy "Les utilisateurs peuvent voir leurs propres notifications"
  on public.user_notifications for select
  using (auth.uid() = user_id);

create policy "L'application peut créer des notifications"
  on public.user_notifications for insert
  with check (true);

create policy "Les utilisateurs peuvent mettre à jour leurs propres notifications"
  on public.user_notifications for update
  using (auth.uid() = user_id)
  with check (auth.uid() = user_id);

create policy "Les utilisateurs peuvent supprimer leurs propres notifications"
  on public.user_notifications for delete
  using (auth.uid() = user_id);

-- Table pour stocker les logs de sécurité
create table if not exists public.security_logs (
  id uuid default uuid_generate_v4() primary key,
  type text not null, -- Type d'événement (LOGIN_ATTEMPT, SUSPICIOUS_ACTIVITY, etc.)
  severity text not null check (severity in ('low', 'medium', 'high')),
  user_id uuid references public.users(id) on delete set null,
  ip_address text,
  user_agent text,
  details jsonb,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  expires_at timestamp with time zone default timezone('utc'::text, now() + interval '365 days')
);

-- Index pour les recherches fréquentes
create index if not exists idx_security_logs_type on public.security_logs(type);
create index if not exists idx_security_logs_severity on public.security_logs(severity);
create index if not exists idx_security_logs_user_id on public.security_logs(user_id);
create index if not exists idx_security_logs_created_at on public.security_logs(created_at);
create index if not exists idx_security_logs_expires_at on public.security_logs(expires_at);

-- Trigger pour la mise à jour automatique
CREATE TRIGGER handle_security_logs_updated_at
    BEFORE UPDATE ON security_logs
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- RLS policies
alter table public.security_logs enable row level security;

create policy "Les logs peuvent être insérés par l'application"
  on public.security_logs for insert
  with check (true);

create policy "Les logs peuvent être lus par l'application"
  on public.security_logs for select
  using (true);

create policy "Les logs peuvent être mis à jour par l'application"
  on public.security_logs for update
  using (true);

create policy "Les logs peuvent être supprimés par l'application"
  on public.security_logs for delete
  using (true);


-- Mise à jour de la table security_logs existante pour ajouter les colonnes manquantes
DO $$
BEGIN
    -- Ajouter la colonne message si elle n'existe pas
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'security_logs' AND column_name = 'message'
    ) THEN
        ALTER TABLE public.security_logs ADD COLUMN message TEXT;
    END IF;

    -- Ajouter la colonne updated_at si elle n'existe pas
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'security_logs' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE public.security_logs ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now());
    END IF;

    -- Mettre à jour la contrainte de severity pour inclure 'critical'
    ALTER TABLE public.security_logs DROP CONSTRAINT IF EXISTS security_logs_severity_check;
    ALTER TABLE public.security_logs ADD CONSTRAINT security_logs_severity_check
        CHECK (severity IN ('low', 'medium', 'high', 'critical'));
END $$;

-- Trigger pour la mise à jour automatique de updated_at (si pas déjà créé)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_security_logs_updated_at') THEN
        CREATE TRIGGER handle_security_logs_updated_at
            BEFORE UPDATE ON public.security_logs
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;


-- Table des réponses prédéfinies pour les offres de mission
create table if not exists public.user_mission_responses (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete cascade,
    title text not null,
    content text not null,
    order_index integer not null default 0,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour les réponses prédéfinies
create index if not exists idx_user_mission_responses_user_id on public.user_mission_responses(user_id);
create index if not exists idx_user_mission_responses_order_index on public.user_mission_responses(order_index);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_responses_updated_at
    BEFORE UPDATE ON user_mission_responses
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.user_mission_responses ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour les réponses prédéfinies
CREATE POLICY "Les réponses prédéfinies peuvent être lues par leur propriétaire"
ON public.user_mission_responses FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Les réponses prédéfinies peuvent être créées par l'utilisateur authentifié"
ON public.user_mission_responses FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les réponses prédéfinies peuvent être modifiées par leur propriétaire"
ON public.user_mission_responses FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les réponses prédéfinies peuvent être supprimées par leur propriétaire"
ON public.user_mission_responses FOR DELETE
USING (auth.uid() = user_id);

-- Table pour suivre les parrainages
create table if not exists public.user_referrals (
    id uuid default uuid_generate_v4() primary key,
    referrer_id uuid references public.users(id) on delete cascade, -- Utilisateur qui parraine
    referred_id uuid references public.users(id) on delete cascade, -- Utilisateur parrainé
    status text not null check (status in ('pending', 'completed', 'rewarded')), -- État du parrainage
    reward_amount decimal(10,2) default 20.00, -- Montant de la récompense (20 Jobis par défaut)
    completed_at timestamp with time zone, -- Date de complétion (première mission validée)
    rewarded_at timestamp with time zone, -- Date de récompense
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Création des triggers pour updated_at
DO $$
BEGIN
    -- Trigger pour user_referrals
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_referrals_updated_at') THEN
        CREATE TRIGGER handle_user_referrals_updated_at
            BEFORE UPDATE ON user_referrals
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Création des index
create index if not exists idx_user_referrals_referrer_id on public.user_referrals(referrer_id);
create index if not exists idx_user_referrals_referred_id on public.user_referrals(referred_id);
create index if not exists idx_user_referrals_status on public.user_referrals(status);
create index if not exists idx_users_referral_code on public.users(referral_code);
create index if not exists idx_users_referred_by on public.users(referred_by);

-- Activation de RLS sur la table user_referrals
ALTER TABLE public.user_referrals ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour user_referrals
CREATE POLICY "Les parrainages peuvent être lus par les utilisateurs concernés"
ON public.user_referrals FOR SELECT
USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

CREATE POLICY "Les parrainages peuvent être créés par l'application"
ON public.user_referrals FOR INSERT
WITH CHECK (true);

CREATE POLICY "Les parrainages peuvent être mis à jour par l'application"
ON public.user_referrals FOR UPDATE
USING (true);

CREATE POLICY "Les parrainages peuvent être supprimés par l'application"
ON public.user_referrals FOR DELETE
USING (true);


-- Table pour stocker les rapports de bugs et les suggestions d'amélioration
create table if not exists public.bug_reports (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete set null,
    title text not null,
    description text not null,
    report_type text not null check (report_type in ('bug', 'improvement')),
    category text not null check (category in ('interface', 'fonctionnalite', 'paiement', 'securite', 'autre')),
    priority text not null check (priority in ('faible', 'moyenne', 'elevee', 'critique')),
    status text not null check (status in ('nouveau', 'en_cours', 'resolu', 'rejete', 'ferme', 'reouvert', 'attente_moderation')) default 'attente_moderation',
    is_private boolean default false,
    admin_comment text,
    reproduction_steps text,
    browser_info jsonb,
    os_info text,
    assigned_to uuid references public.users(id) on delete set null,
    resolved_at timestamp with time zone,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Ajout de la colonne is_private si elle n'existe pas déjà
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'bug_reports'
        AND column_name = 'is_private'
    ) THEN
        ALTER TABLE bug_reports
        ADD COLUMN is_private BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Historique des mises à jour de statut
create table if not exists public.bug_report_history (
    id uuid default uuid_generate_v4() primary key,
    bug_report_id uuid references public.bug_reports(id) on delete cascade,
    updated_by uuid references public.users(id) on delete set null,
    old_status text,
    new_status text not null,
    comment text,
    created_at timestamp with time zone default timezone('utc'::text, now())
);

-- Votes/Approbations pour les suggestions d'amélioration
create table if not exists public.bug_report_votes (
    id uuid default uuid_generate_v4() primary key,
    bug_report_id uuid references public.bug_reports(id) on delete cascade,
    user_id uuid references public.users(id) on delete cascade,
    vote_type text not null check (vote_type in ('pour', 'contre')),
    comment text,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    unique(bug_report_id, user_id)
);

-- Table des commentaires pour les rapports de bug
create table if not exists public.bug_report_comments (
  id uuid default uuid_generate_v4() primary key,
  bug_report_id uuid references public.bug_reports(id) on delete cascade,
  user_id uuid references public.users(id) on delete cascade,
  parent_comment_id uuid references public.bug_report_comments(id) on delete set null,
  message text not null,
  is_admin boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Création des index
create index if not exists idx_bug_reports_user_id on public.bug_reports(user_id);
create index if not exists idx_bug_reports_report_type on public.bug_reports(report_type);
create index if not exists idx_bug_reports_category on public.bug_reports(category);
create index if not exists idx_bug_reports_status on public.bug_reports(status);
create index if not exists idx_bug_reports_priority on public.bug_reports(priority);
create index if not exists idx_bug_reports_created_at on public.bug_reports(created_at);

create index if not exists idx_bug_report_history_bug_report_id on public.bug_report_history(bug_report_id);
create index if not exists idx_bug_report_history_updated_by on public.bug_report_history(updated_by);

create index if not exists idx_bug_report_votes_bug_report_id on public.bug_report_votes(bug_report_id);
create index if not exists idx_bug_report_votes_user_id on public.bug_report_votes(user_id);
create index if not exists idx_bug_report_comments_parent_id on public.bug_report_comments(parent_comment_id);

-- Index pour la table bug_report_comments
create index if not exists idx_bug_report_comments_bug_report_id on public.bug_report_comments(bug_report_id);
create index if not exists idx_bug_report_comments_user_id on public.bug_report_comments(user_id);
create index if not exists idx_bug_report_comments_created_at on public.bug_report_comments(created_at);

-- Trigger pour la mise à jour automatique de updated_at
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

CREATE TRIGGER handle_bug_reports_updated_at
    BEFORE UPDATE ON bug_reports
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Trigger pour bug_report_comments
CREATE TRIGGER handle_bug_report_comments_updated_at
    BEFORE UPDATE ON bug_report_comments
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activer RLS (Row Level Security)
ALTER TABLE public.bug_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bug_report_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bug_report_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bug_report_comments ENABLE ROW LEVEL SECURITY;

-- Politiques RLS pour bug_reports
CREATE POLICY "Les rapports peuvent être lus par tous les utilisateurs"
ON public.bug_reports FOR SELECT
USING (true);

CREATE POLICY "Les rapports peuvent être créés par les utilisateurs authentifiés"
ON public.bug_reports FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les rapports peuvent être mis à jour par leurs auteurs ou les administrateurs"
ON public.bug_reports FOR UPDATE
USING (auth.uid() = user_id OR
       EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

-- Politiques RLS pour bug_report_history
CREATE POLICY "L'historique des rapports peut être lu par tous les utilisateurs"
ON public.bug_report_history FOR SELECT
USING (true);

CREATE POLICY "L'historique des rapports peut être créé par les administrateurs"
ON public.bug_report_history FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

-- Politiques RLS pour bug_report_votes
CREATE POLICY "Les votes peuvent être lus par tous les utilisateurs"
ON public.bug_report_votes FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent voter une seule fois par rapport"
ON public.bug_report_votes FOR INSERT
WITH CHECK (auth.uid() = user_id AND
            NOT EXISTS (
              SELECT 1 FROM public.bug_report_votes
              WHERE bug_report_votes.bug_report_id = bug_report_id
              AND bug_report_votes.user_id = auth.uid()
            ));

CREATE POLICY "Les utilisateurs peuvent modifier leur propre vote"
ON public.bug_report_votes FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leur propre vote"
ON public.bug_report_votes FOR DELETE
USING (auth.uid() = user_id);

-- Politiques RLS pour bug_report_comments
CREATE POLICY "Les commentaires peuvent être lus par tous les utilisateurs"
ON public.bug_report_comments FOR SELECT
USING (true);

CREATE POLICY "Les utilisateurs peuvent ajouter leurs propres commentaires"
ON public.bug_report_comments FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent modifier leurs propres commentaires"
ON public.bug_report_comments FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres commentaires"
ON public.bug_report_comments FOR DELETE
USING (auth.uid() = user_id);

CREATE POLICY "Les administrateurs peuvent gérer tous les commentaires"
ON public.bug_report_comments FOR ALL
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND (role = 'jobpadm' OR role = 'jobmodo')));

-- Fonction pour récupérer le nombre de votes pour un rapport de bug
CREATE OR REPLACE FUNCTION get_bug_report_votes(report_id uuid)
RETURNS TABLE (
    pour_count bigint,
    contre_count bigint
)
AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM public.bug_report_votes WHERE bug_report_id = report_id AND vote_type = 'pour'),
        (SELECT COUNT(*) FROM public.bug_report_votes WHERE bug_report_id = report_id AND vote_type = 'contre');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Octroyer les privilèges nécessaires
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;

-- Système de tickets support
create table if not exists public.support_tickets (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references public.users(id) on delete set null,
    title text not null,
    description text not null,
    status text not null check (status in ('nouveau', 'en_attente', 'en_cours', 'resolu', 'ferme', 'reouvert')) default 'nouveau',
    priority text not null check (priority in ('faible', 'normale', 'elevee', 'urgente')) default 'normale',
    category text not null check (category in ('technique', 'facturation', 'compte', 'mission', 'autre')),
    assigned_to uuid references public.users(id) on delete set null,
    sla_due_at timestamp with time zone,
    resolved_at timestamp with time zone,
    last_response_at timestamp with time zone,
    repondu boolean default false, -- Indique si le ticket a été répondu par un admin/modo
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

create table if not exists public.support_ticket_history (
    id uuid default uuid_generate_v4() primary key,
    ticket_id uuid references public.support_tickets(id) on delete cascade,
    user_id uuid references public.users(id) on delete set null,
    action text not null,
    old_value text,
    new_value text,
    comment text,
    created_at timestamp with time zone default timezone('utc'::text, now())
);

create table if not exists public.support_ticket_comments (
    id uuid default uuid_generate_v4() primary key,
    ticket_id uuid references public.support_tickets(id) on delete cascade,
    user_id uuid references public.users(id) on delete set null,
    message text not null,
    is_internal boolean default false,
    parent_id uuid references public.support_ticket_comments(id) on delete set null,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

create table if not exists public.support_ticket_attachments (
    id uuid default uuid_generate_v4() primary key,
    ticket_id uuid references public.support_tickets(id) on delete cascade,
    comment_id uuid references public.support_ticket_comments(id) on delete cascade,
    file_name text not null,
    file_size integer not null,
    file_type text not null,
    storage_path text not null,
    created_at timestamp with time zone default timezone('utc'::text, now())
);

create table if not exists public.support_ticket_tags (
    id uuid default uuid_generate_v4() primary key,
    name text not null unique,
    color text not null,
    description text,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

create table if not exists public.support_ticket_tag_relations (
    ticket_id uuid references public.support_tickets(id) on delete cascade,
    tag_id uuid references public.support_ticket_tags(id) on delete cascade,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    primary key (ticket_id, tag_id)
);

create table if not exists public.support_response_templates (
    id uuid default uuid_generate_v4() primary key,
    title text not null,
    content text not null,
    category text not null,
    is_internal boolean default false,
    created_by uuid references public.users(id) on delete set null,
    created_at timestamp with time zone default timezone('utc'::text, now()),
    updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Création des index
create index if not exists idx_support_tickets_user_id on public.support_tickets(user_id);
create index if not exists idx_support_tickets_assigned_to on public.support_tickets(assigned_to);
create index if not exists idx_support_tickets_status on public.support_tickets(status);
create index if not exists idx_support_tickets_priority on public.support_tickets(priority);
create index if not exists idx_support_tickets_category on public.support_tickets(category);
create index if not exists idx_support_tickets_created_at on public.support_tickets(created_at);
create index if not exists idx_support_tickets_sla_due_at on public.support_tickets(sla_due_at);

create index if not exists idx_support_ticket_history_ticket_id on public.support_ticket_history(ticket_id);
create index if not exists idx_support_ticket_comments_ticket_id on public.support_ticket_comments(ticket_id);
create index if not exists idx_support_ticket_comments_parent_id on public.support_ticket_comments(parent_id);
create index if not exists idx_support_ticket_attachments_ticket_id on public.support_ticket_attachments(ticket_id);
create index if not exists idx_support_ticket_attachments_comment_id on public.support_ticket_attachments(comment_id);

-- Triggers pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_support_tickets_updated_at
    BEFORE UPDATE ON support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_support_ticket_comments_updated_at
    BEFORE UPDATE ON support_ticket_comments
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_support_ticket_tags_updated_at
    BEFORE UPDATE ON support_ticket_tags
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER handle_support_response_templates_updated_at
    BEFORE UPDATE ON support_response_templates
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_tag_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_response_templates ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité pour support_tickets
CREATE POLICY "Les tickets peuvent être lus par leur créateur ou le staff"
ON public.support_tickets FOR SELECT
USING (auth.uid() = user_id OR
       EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Les tickets peuvent être créés par les utilisateurs authentifiés"
ON public.support_tickets FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Les tickets peuvent être mis à jour par le staff"
ON public.support_tickets FOR UPDATE
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- Politiques de sécurité pour support_ticket_comments
CREATE POLICY "Les commentaires peuvent être lus par les participants au ticket"
ON public.support_ticket_comments FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.support_tickets
        WHERE id = ticket_id
        AND (user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

CREATE POLICY "Les commentaires peuvent être créés par les participants au ticket"
ON public.support_ticket_comments FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.support_tickets
        WHERE id = ticket_id
        AND (user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

-- Politiques de sécurité pour support_ticket_attachments
CREATE POLICY "Les pièces jointes peuvent être lues par les participants au ticket"
ON public.support_ticket_attachments FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.support_tickets
        WHERE id = ticket_id
        AND (user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

CREATE POLICY "Les pièces jointes peuvent être ajoutées par les participants au ticket"
ON public.support_ticket_attachments FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.support_tickets
        WHERE id = ticket_id
        AND (user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

-- Politiques de sécurité pour support_ticket_tags
CREATE POLICY "Les tags peuvent être lus par tous"
ON public.support_ticket_tags FOR SELECT
USING (true);

CREATE POLICY "Les tags peuvent être gérés par le staff"
ON public.support_ticket_tags FOR ALL
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- Politiques de sécurité pour support_response_templates
CREATE POLICY "Les modèles de réponse peuvent être lus par le staff"
ON public.support_response_templates FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Les modèles de réponse peuvent être gérés par le staff"
ON public.support_response_templates FOR ALL
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- Fonction pour mettre à jour last_response_at
CREATE OR REPLACE FUNCTION update_ticket_last_response()
RETURNS TRIGGER AS $$
DECLARE
    user_role text;
BEGIN
    -- Récupérer le rôle de l'utilisateur qui a ajouté le commentaire
    SELECT role INTO user_role FROM public.users WHERE id = NEW.user_id;

    -- Mettre à jour last_response_at et repondu
    UPDATE public.support_tickets
    SET
        last_response_at = NEW.created_at,
        -- Mettre à jour repondu en fonction du rôle de l'utilisateur
        repondu = CASE
            WHEN user_role IN ('jobpadm', 'jobmodo') THEN true
            ELSE false
        END
    WHERE id = NEW.ticket_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Trigger pour mettre à jour last_response_at
CREATE TRIGGER update_ticket_last_response_trigger
    AFTER INSERT ON support_ticket_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_ticket_last_response();

-- Création du bucket pour les pièces jointes des tickets
INSERT INTO storage.buckets (id, name, public)
VALUES ('support_ticket_attachments', 'support_ticket_attachments', false)
ON CONFLICT (id) DO NOTHING;

-- Politiques de stockage pour les pièces jointes
CREATE POLICY "Les pièces jointes peuvent être téléchargées par les participants au ticket"
ON storage.objects FOR SELECT
USING (
    bucket_id = 'support_ticket_attachments' AND
    EXISTS (
        SELECT 1 FROM public.support_ticket_attachments sa
        JOIN public.support_tickets st ON sa.ticket_id = st.id
        WHERE sa.storage_path = name
        AND (st.user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

CREATE POLICY "Les pièces jointes peuvent être uploadées par les participants au ticket"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'support_ticket_attachments' AND
    EXISTS (
        SELECT 1 FROM public.support_tickets
        WHERE id = (SELECT ticket_id FROM public.support_ticket_attachments WHERE storage_path = name)
        AND (user_id = auth.uid() OR
             EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
    )
);

-- Fonction pour mettre à jour repondu lors de la modification du statut
CREATE OR REPLACE FUNCTION update_ticket_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Si le ticket est fermé ou résolu, il est considéré comme répondu
    IF NEW.status IN ('ferme', 'resolu') THEN
        NEW.repondu = true;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Trigger pour mettre à jour repondu lors de la modification du statut
CREATE TRIGGER update_ticket_status_trigger
    BEFORE UPDATE OF status ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION update_ticket_status();

-- Index sur la colonne repondu pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_support_tickets_repondu ON public.support_tickets(repondu);

-- Mettre à jour tous les tickets existants (les tickets fermés ou résolus sont considérés comme répondus)
UPDATE public.support_tickets
SET repondu = true
WHERE status IN ('ferme', 'resolu');

-- Insertion de modèles de réponse pour la catégorie "technique"
INSERT INTO support_response_templates (title, content, category) VALUES
(
    'Problème de connexion - Solution générale',
    E'Bonjour,\n\nNous avons bien reçu votre demande concernant des problèmes de connexion à votre compte JobPartiel.\n\nVoici quelques étapes à suivre pour résoudre ce problème :\n\n1. Vérifiez que vous utilisez l''adresse email correcte associée à votre compte\n2. Essayez de réinitialiser votre mot de passe via la fonction "Mot de passe oublié"\n3. Videz le cache de votre navigateur et essayez à nouveau\n4. Essayez un autre navigateur (Chrome, Firefox, Edge)\n\nSi ces étapes ne résolvent pas votre problème, merci de nous préciser :\n- Le type d''appareil que vous utilisez (ordinateur, tablette, smartphone)\n- Le navigateur utilisé et sa version\n- Un screenshot de l''erreur si possible\n\nL''équipe JobPartiel reste à votre disposition pour toute assistance supplémentaire.\n\nCordialement,\nL''équipe support JobPartiel',
    'technique'
),
(
    'Application mobile - Problèmes de GPS',
    E'Bonjour,\n\nNous vous remercions pour votre signalement concernant les problèmes de localisation GPS dans notre application mobile.\n\nPour résoudre ce problème, veuillez suivre ces étapes :\n\n1. Vérifiez que vous avez bien accordé les permissions de localisation à l''application JobPartiel dans les paramètres de votre téléphone\n2. Assurez-vous que le GPS de votre appareil est activé\n3. Mettez à jour l''application vers la dernière version disponible\n4. Redémarrez votre téléphone\n\nSi le problème persiste, merci de nous indiquer :\n- La marque et le modèle de votre téléphone\n- La version de votre système d''exploitation (Android/iOS)\n- La version de l''application JobPartiel\n\nNotre équipe technique travaille continuellement à améliorer la précision du GPS et la stabilité de l''application.\n\nCordialement,\nL''équipe support JobPartiel',
    'technique'
),
(
    'Problème de téléchargement de documents',
    E'Bonjour,\n\nNous avons bien reçu votre signalement concernant les difficultés à télécharger des documents sur votre profil.\n\nVoici les solutions que nous vous recommandons :\n\n1. Vérifiez que le document ne dépasse pas la taille maximale autorisée (5 Mo)\n2. Assurez-vous que le format du document est accepté (PDF, JPG, PNG)\n3. Vérifiez votre connexion internet\n4. Essayez de compresser le document si sa taille est trop importante\n\nSi ces solutions ne fonctionnent pas, n''hésitez pas à nous envoyer le document par email à <EMAIL> en précisant votre identifiant utilisateur, et nous l''ajouterons manuellement à votre profil.\n\nNous vous remercions pour votre patience.\n\nCordialement,\nL''équipe support JobPartiel',
    'technique'
);

-- Insertion de modèles de réponse pour la catégorie "facturation"
INSERT INTO support_response_templates (title, content, category) VALUES
(
    'Explication facture mensuelle',
    E'Bonjour,\n\nNous vous remercions pour votre demande d''information concernant votre facture du mois dernier.\n\nVotre facture se décompose comme suit :\n- Abonnement mensuel au service JobPartiel : XX Jobis\n- Commission sur les missions effectuées : XX Jobis\n- Frais de transaction (le cas échéant) : XX Jobis\n\nLes tarifs appliqués sont conformes aux conditions générales d''utilisation que vous avez acceptées lors de votre inscription. Vous pouvez consulter l''historique complet de vos transactions dans la section "Mon compte > Historique des paiements".\n\nSi vous avez des questions spécifiques sur certains éléments de votre facture, n''hésitez pas à nous les préciser pour que nous puissions vous apporter des explications détaillées.\n\nCordialement,\nL''équipe facturation JobPartiel',
    'facturation'
),
(
    'Problème de paiement - Carte refusée',
    E'Bonjour,\n\nNous avons bien reçu votre signalement concernant le refus de paiement par carte bancaire.\n\nCela peut être dû à plusieurs raisons :\n1. Fonds insuffisants sur le compte\n2. Plafond de paiement atteint\n3. Carte expirée ou informations incorrectes\n4. Restriction géographique ou sécuritaire de votre banque\n\nNous vous recommandons de :\n- Vérifier auprès de votre banque qu''il n''y a pas de restriction sur votre carte\n- Essayer avec une autre carte bancaire si possible\n- Vérifier que les informations de votre carte sont correctement saisies\n\nVous pouvez également opter pour un autre mode de paiement disponible sur notre plateforme, comme le virement bancaire ou PayPal.\n\nNotre équipe reste à votre disposition pour toute assistance complémentaire.\n\nCordialement,\nL''équipe facturation JobPartiel',
    'facturation'
),
(
    'Conversion Jobi en Euros',
    E'Bonjour,\n\nMerci pour votre demande concernant la conversion des Jobis en euros.\n\nLe taux de conversion actuel est de 1 Jobi = 1 euro. Ce taux est fixe et permet de simplifier les échanges sur notre plateforme.\n\nLe minimum pour effectuer un retrait est de 50 Jobis, et les retraits sont traités sous 3 jours ouvrés. Veuillez noter que des frais bancaires de 0,5% (minimum 1€) peuvent s''appliquer selon votre banque.\n\nPour effectuer un retrait, rendez-vous dans la section "Mon Portefeuille > Retrait" de votre espace personnel.\n\nN''hésitez pas à nous contacter si vous avez d''autres questions concernant notre système d''échange de Jobi.\n\nCordialement,\nL''équipe facturation JobPartiel',
    'facturation'
);

-- Insertion de modèles de réponse pour la catégorie "compte"
INSERT INTO support_response_templates (title, content, category) VALUES
(
    'Changement d''adresse email',
    E'Bonjour,\n\nNous avons bien reçu votre demande de changement d''adresse email.\n\nPour des raisons de sécurité, nous devons vérifier votre identité avant de procéder à cette modification. Voici la procédure à suivre :\n\n1. Connectez-vous à votre compte JobPartiel\n2. Rendez-vous dans "Paramètres > Sécurité"\n3. Cliquez sur "Modifier mon email"\n4. Suivez les instructions pour confirmer votre identité\n5. Saisissez votre nouvelle adresse email\n6. Confirmez en cliquant sur le lien envoyé à votre nouvelle adresse\n\nSi vous n''avez plus accès à votre adresse email actuelle, veuillez nous fournir une copie de votre pièce d''identité par email à <EMAIL> pour que nous puissions procéder manuellement.\n\nCordialement,\nL''équipe support JobPartiel',
    'compte'
),
(
    'Suppression de compte - Procédure',
    E'Bonjour,\n\nNous avons bien reçu votre demande de suppression de compte et nous regrettons de vous voir partir.\n\nAvant de procéder, nous tenons à vous informer que cette action est irréversible et entraînera :\n- La suppression permanente de toutes vos données personnelles\n- La perte de votre historique de missions\n- La perte de votre solde Jobi non utilisé\n\nSi vous souhaitez toujours supprimer votre compte, voici la procédure :\n1. Connectez-vous à votre compte\n2. Rendez-vous dans "Paramètres > Confidentialité"\n3. Cliquez sur "Supprimer mon compte"\n4. Suivez les instructions et confirmez votre décision\n\nConformément au RGPD, vos données seront définitivement supprimées sous 30 jours.\n\nSi vous rencontrez des difficultés qui vous poussent à quitter notre plateforme, nous serions heureux d''en discuter pour améliorer nos services.\n\nCordialement,\nL''équipe JobPartiel',
    'compte'
),
(
    'Vérification d''identité - Documents requis',
    E'Bonjour,\n\nMerci pour votre inscription sur JobPartiel. Pour finaliser la création de votre compte et garantir la sécurité de notre communauté, nous devons procéder à la vérification de votre identité.\n\nVoici les documents requis :\n\n1. Une copie recto-verso de votre pièce d''identité en cours de validité (carte d''identité, passeport ou titre de séjour)\n2. Un justificatif de domicile de moins de 3 mois (facture d''électricité, eau, téléphone fixe, etc.)\n3. Une photo de profil récente où votre visage est clairement visible\n\nPour télécharger ces documents :\n1. Connectez-vous à votre compte\n2. Accédez à la section "Vérification d''identité"\n3. Suivez les instructions pour télécharger chaque document\n\nVos documents seront examinés sous 48h ouvrées.\n\nNous vous remercions pour votre compréhension et votre patience.\n\nCordialement,\nL''équipe vérification JobPartiel',
    'compte'
);

-- Insertion de modèles de réponse pour la catégorie "mission"
INSERT INTO support_response_templates (title, content, category) VALUES
(
    'Missions annulées - Politique de compensation',
    E'Bonjour,\n\nNous vous présentons nos excuses pour l''annulation de votre mission.\n\nConformément à notre politique de compensation, voici les règles qui s''appliquent :\n\n- Annulation par le client plus de 48h avant la mission : aucune compensation\n- Annulation par le client entre 24h et 48h avant la mission : 15% du montant de la mission\n- Annulation par le client moins de 24h avant la mission : 30% du montant de la mission\n- Annulation par le client une fois que vous êtes sur place : 50% du montant de la mission\n\nDans votre cas, l''annulation est intervenue [XX] heures avant la mission, vous recevrez donc une compensation de [XX] Jobis qui sera créditée sur votre compte sous 24h.\n\nNous faisons tout notre possible pour limiter ces situations et vous proposer rapidement de nouvelles missions correspondant à votre profil.\n\nCordialement,\nL''équipe missions JobPartiel',
    'mission'
),
(
    'Litige avec un client - Procédure',
    E'Bonjour,\n\nNous avons bien reçu votre signalement concernant un désaccord avec un client.\n\nVoici la procédure que nous suivons pour résoudre ce type de situation :\n\n1. Notre équipe de médiation va contacter les deux parties séparément pour recueillir les versions des faits\n2. Nous examinerons les preuves fournies (messages, photos, etc.)\n3. Si nécessaire, nous consulterons l''historique des missions précédentes\n4. Nous proposerons une solution équitable dans un délai de 5 jours ouvrés\n\nAfin de nous aider dans cette médiation, merci de nous fournir :\n- Une description détaillée de la situation\n- Toute preuve pouvant appuyer votre version (photos, messages)\n- La solution que vous estimez équitable\n\nNous vous remercions pour votre patience et votre collaboration dans la résolution de ce différend.\n\nCordialement,\nL''équipe médiation JobPartiel',
    'mission'
),
(
    'Comment augmenter ses chances d''obtenir des missions',
    E'Bonjour,\n\nNous vous remercions pour votre question sur les moyens d''augmenter vos chances d''obtenir des missions sur JobPartiel.\n\nVoici nos conseils pour optimiser votre profil et maximiser vos opportunités :\n\n1. Complétez intégralement votre profil avec des informations détaillées sur vos compétences et expériences\n2. Ajoutez une photo de profil professionnelle et soignée\n3. Demandez des évaluations à vos précédents clients (même hors plateforme)\n4. Élargissez votre zone d''intervention si possible\n5. Soyez réactif aux demandes et aux messages\n6. Proposez des disponibilités variées, y compris en soirée ou le weekend\n7. Acceptez les premières missions même moins rémunératrices pour obtenir des avis positifs\n\nLes utilisateurs ayant un profil complet et des évaluations positives ont 80% plus de chances d''être sélectionnés.\n\nN''hésitez pas à consulter notre guide complet dans la section "Aide > Conseils pour prestataires".\n\nCordialement,\nL''équipe JobPartiel',
    'mission'
);

-- Insertion de modèles de réponse pour la catégorie "autre"
INSERT INTO support_response_templates (title, content, category) VALUES
(
    'Suggestion d''amélioration reçue',
    E'Bonjour,\n\nNous vous remercions vivement pour votre suggestion d''amélioration concernant JobPartiel.\n\nVotre retour est précieux et contribue directement à l''évolution de notre plateforme. Nous avons transmis votre idée à notre équipe produit qui va l''étudier attentivement.\n\nNous privilégions les fonctionnalités les plus demandées par notre communauté, et chaque suggestion est évaluée selon sa faisabilité technique et sa valeur ajoutée pour l''ensemble des utilisateurs.\n\nSi votre suggestion est retenue pour un développement futur, vous en serez informé personnellement. Dans tous les cas, nous vous tiendrons au courant de l''avancement de votre demande sous 30 jours.\n\nEncore merci pour votre implication dans l''amélioration de JobPartiel.\n\nCordialement,\nL''équipe produit JobPartiel',
    'autre'
),
(
    'Programme parrainage - Explications',
    E'Bonjour,\n\nMerci pour votre intérêt pour notre programme de parrainage.\n\nVoici comment cela fonctionne :\n\n1. Partagez votre code personnel de parrainage avec vos amis, famille ou collègues\n2. Lorsqu''une personne s''inscrit avec votre code et effectue sa première mission (comme prestataire) ou réserve sa première prestation (comme client), vous recevez 20 Jobis\n3. Votre filleul reçoit également 10 Jobis en bonus de bienvenue\n4. Vous pouvez parrainer jusqu''à 20 personnes par an\n5. Les Jobis sont crédités automatiquement 7 jours après la première mission/prestation de votre filleul\n\nVotre code de parrainage personnel est disponible dans la section "Mon compte > Parrainage" de votre espace personnel.\n\nPlus d''informations sont disponibles dans nos conditions générales du programme de parrainage.\n\nCordialement,\nL''équipe marketing JobPartiel',
    'autre'
),
(
    'Collaboration et partenariats',
    E'Bonjour,\n\nNous vous remercions pour votre intérêt à collaborer avec JobPartiel.\n\nNotre programme de partenariats est ouvert aux entreprises et organisations qui partagent nos valeurs et notre vision du travail à temps partiel.\n\nPour étudier une possible collaboration, nous aurions besoin des informations suivantes :\n\n1. Présentation de votre entreprise/organisation\n2. Nature du partenariat envisagé\n3. Bénéfices mutuels attendus\n4. Portée géographique concernée\n5. Durée souhaitée du partenariat\n\nUne fois ces informations reçues, notre équipe partenariats vous contactera sous 10 jours ouvrés pour discuter des possibilités.\n\nVous pouvez également consulter notre brochure partenaires disponible sur notre site web dans la section "Partenariats".\n\nCordialement,\nL''équipe partenariats JobPartiel',
    'autre'
);

-- Table pour suivre les abus de parrainage
create table if not exists public.user_referrals_abuse_logs (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade,
  related_user_id uuid references public.users(id) on delete set null,
  type text not null check (type in ('multiple_accounts', 'deleted_account', 'suspicious_activity', 'mission_proposal_blocked')),
  description text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now()),
  severity text not null check (severity in ('low', 'medium', 'high')),
  status text not null check (status in ('pending', 'investigating', 'resolved', 'false_positive', 'detected')),
  action_taken text,
  jobi_removed integer default 0
);

-- Index pour la table user_referrals_abuse_logs
create index if not exists idx_referral_abuse_logs_user_id on public.user_referrals_abuse_logs(user_id);
create index if not exists idx_referral_abuse_logs_related_user_id on public.user_referrals_abuse_logs(related_user_id);
create index if not exists idx_referral_abuse_logs_type on public.user_referrals_abuse_logs(type);
create index if not exists idx_referral_abuse_logs_severity on public.user_referrals_abuse_logs(severity);
create index if not exists idx_referral_abuse_logs_status on public.user_referrals_abuse_logs(status);
create index if not exists idx_referral_abuse_logs_created_at on public.user_referrals_abuse_logs(created_at);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_referral_abuse_logs_updated_at
    BEFORE UPDATE ON user_referrals_abuse_logs
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Ajout des colonnes de paiement à la table des candidatures
ALTER TABLE public.user_mission_candidature
ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) CHECK (payment_status IN ('pending', 'completed', 'manual')),
ADD COLUMN IF NOT EXISTS payment_date TIMESTAMP WITH TIME ZONE;

-- Mise à jour des propositions existantes
UPDATE public.user_mission_candidature
SET payment_status = 'pending'
WHERE payment_status IS NULL;

-- Création d'un index pour le statut de paiement
CREATE INDEX IF NOT EXISTS idx_user_mission_candidature_payment_status ON public.user_mission_candidature(payment_status);

-- Création d'un index pour la date de paiement
CREATE INDEX IF NOT EXISTS idx_user_mission_candidature_payment_date ON public.user_mission_candidature(payment_date);

-- Tables pour le système de messagerie privée
create table if not exists public.user_messages_conversations (
  id uuid default uuid_generate_v4() primary key,
  user1_id uuid references public.users(id) on delete cascade,
  user2_id uuid references public.users(id) on delete cascade,
  last_message_id uuid, -- Sera mis à jour avec une référence à user_messages
  updated_at timestamp with time zone default timezone('utc'::text, now()),
  created_at timestamp with time zone default timezone('utc'::text, now()),

  -- Gestion des statuts pour chaque utilisateur
  user1_has_blocked boolean default false,
  user2_has_blocked boolean default false,
  user1_has_deleted boolean default false,
  user2_has_deleted boolean default false,

  -- Statistiques
  total_messages int default 0,
  unread_count_user1 int default 0,
  unread_count_user2 int default 0,

  -- Contrainte pour éviter les doublons
  constraint unique_conversation unique (user1_id, user2_id)
);

create table if not exists public.user_messages (
  id uuid default uuid_generate_v4() primary key,
  conversation_id uuid references public.user_messages_conversations(id) on delete cascade,
  sender_id uuid references public.users(id) on delete cascade,
  receiver_id uuid references public.users(id) on delete cascade,
  content text not null,
  is_read boolean default false,
  read_at timestamp with time zone,
  has_attachment boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now()),

  -- Gestion des statuts
  is_deleted_sender boolean default false,
  is_deleted_receiver boolean default false
);

create table if not exists public.user_message_attachments (
  id uuid default uuid_generate_v4() primary key,
  message_id uuid references public.user_messages(id) on delete cascade,
  file_name text not null,
  file_size int not null,
  mime_type text not null,
  file_path text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  expires_at timestamp with time zone default timezone('utc'::text, now()) + interval '1 month',
  is_expired boolean default false,
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Fonction pour mettre à jour la conversation quand un nouveau message est envoyé
CREATE OR REPLACE FUNCTION update_conversation_on_message_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- Mettre à jour la référence au dernier message
  UPDATE public.user_messages_conversations
  SET
    last_message_id = NEW.id,
    updated_at = NOW(),
    total_messages = total_messages + 1,
    unread_count_user1 = CASE WHEN NEW.receiver_id = user1_id THEN unread_count_user1 + 1 ELSE unread_count_user1 END,
    unread_count_user2 = CASE WHEN NEW.receiver_id = user2_id THEN unread_count_user2 + 1 ELSE unread_count_user2 END
  WHERE id = NEW.conversation_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Fonction pour mettre à jour le compteur de messages non lus quand un message est lu
CREATE OR REPLACE FUNCTION update_unread_count_on_message_read()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.is_read = FALSE AND NEW.is_read = TRUE THEN
    UPDATE public.user_messages_conversations
    SET
      unread_count_user1 = CASE WHEN NEW.receiver_id = user1_id THEN GREATEST(0, unread_count_user1 - 1) ELSE unread_count_user1 END,
      unread_count_user2 = CASE WHEN NEW.receiver_id = user2_id THEN GREATEST(0, unread_count_user2 - 1) ELSE unread_count_user2 END
    WHERE id = NEW.conversation_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Trigger pour mettre à jour la conversation à l'insertion d'un message
CREATE TRIGGER update_conversation_on_message_insert
AFTER INSERT ON public.user_messages
FOR EACH ROW
EXECUTE FUNCTION update_conversation_on_message_insert();

-- Trigger pour mettre à jour le compteur non lu quand un message est lu
CREATE TRIGGER update_unread_count_on_message_read
AFTER UPDATE OF is_read ON public.user_messages
FOR EACH ROW
EXECUTE FUNCTION update_unread_count_on_message_read();

-- Trigger pour mettre à jour le timestamp updated_at sur les conversations
CREATE TRIGGER handle_user_conversations_updated_at
BEFORE UPDATE ON user_messages_conversations
FOR EACH ROW
EXECUTE FUNCTION handle_updated_at();

-- Trigger pour mettre à jour le timestamp updated_at sur les messages
CREATE TRIGGER handle_user_messages_updated_at
BEFORE UPDATE ON user_messages
FOR EACH ROW
EXECUTE FUNCTION handle_updated_at();

-- Trigger pour mettre à jour le timestamp updated_at sur les pièces jointes
CREATE TRIGGER handle_user_message_attachments_updated_at
BEFORE UPDATE ON user_message_attachments
FOR EACH ROW
EXECUTE FUNCTION handle_updated_at();

-- Création des index pour optimiser les performances
create index if not exists idx_user_conversations_user1_id on public.user_messages_conversations(user1_id);
create index if not exists idx_user_conversations_user2_id on public.user_messages_conversations(user2_id);
create index if not exists idx_user_conversations_last_message_id on public.user_messages_conversations(last_message_id);
create index if not exists idx_user_conversations_updated_at on public.user_messages_conversations(updated_at);
create index if not exists idx_user_messages_conversation_id on public.user_messages(conversation_id);
create index if not exists idx_user_messages_sender_id on public.user_messages(sender_id);
create index if not exists idx_user_messages_receiver_id on public.user_messages(receiver_id);
create index if not exists idx_user_messages_created_at on public.user_messages(created_at);
create index if not exists idx_user_messages_is_read on public.user_messages(is_read);
create index if not exists idx_user_message_attachments_message_id on public.user_message_attachments(message_id);
create index if not exists idx_user_message_attachments_expires_at on public.user_message_attachments(expires_at);

-- Activer RLS sur les nouvelles tables
alter table public.user_messages_conversations enable row level security;
alter table public.user_messages enable row level security;
alter table public.user_message_attachments enable row level security;

-- Accorder tous les droits sur les nouvelles tables au service_role
GRANT ALL PRIVILEGES ON TABLE public.user_messages_conversations TO service_role;
GRANT ALL PRIVILEGES ON TABLE public.user_messages TO service_role;
GRANT ALL PRIVILEGES ON TABLE public.user_message_attachments TO service_role;

-- Policies pour user_messages_conversations
CREATE POLICY "Les conversations peuvent être lues par les participants"
ON public.user_messages_conversations FOR SELECT
USING (auth.uid() = user1_id OR auth.uid() = user2_id);

-- Policies pour user_messages
CREATE POLICY "Les messages peuvent être lus par les participants"
ON public.user_messages FOR SELECT
USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

-- Policies pour user_message_attachments
CREATE POLICY "Les pièces jointes peuvent être lues par les participants à la conversation"
ON public.user_message_attachments FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.user_messages
  WHERE public.user_messages.id = message_id
  AND (auth.uid() = sender_id OR auth.uid() = receiver_id)
));

-- Pour les notifications et emails envoyés lors de la création d'une conversation privée et l'envoi de messages
-- Ajout de la colonne conversation_id à la table user_notifications
ALTER TABLE user_notifications
ADD COLUMN conversation_id UUID REFERENCES user_messages_conversations(id) ON DELETE CASCADE;

-- Création d'un index pour améliorer les performances des requêtes
CREATE INDEX idx_user_notifications_conversation_id ON user_notifications(conversation_id);

-- Ajout d'une contrainte pour s'assurer que la conversation existe
ALTER TABLE user_notifications
ADD CONSTRAINT fk_user_notifications_conversation
FOREIGN KEY (conversation_id)
REFERENCES user_messages_conversations(id)
ON DELETE CASCADE;

-- Table pour le planning des missions
create table if not exists public.user_mission_planning (
  id uuid default uuid_generate_v4() primary key,
  mission_id uuid references public.user_missions(id) on delete cascade,
  title text not null,
  description text,
  start_time time not null,
  end_time time not null,
  date date not null,
  montant_propose numeric DEFAULT 0,
  payment_method text DEFAULT 'jobi_only',
  user_id uuid references public.users(id) on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now()),
  is_deleted boolean default false,
  CONSTRAINT check_payment_method CHECK (payment_method IN ('jobi_only', 'both', 'direct_only'))
);

-- Index pour la table user_mission_planning
create index if not exists idx_user_mission_planning_mission_id on public.user_mission_planning(mission_id);
create index if not exists idx_user_mission_planning_date on public.user_mission_planning(date);
create index if not exists idx_user_mission_planning_is_deleted on public.user_mission_planning(is_deleted);
create index if not exists idx_user_mission_planning_user_id on public.user_mission_planning(user_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_mission_planning_updated_at
    BEFORE UPDATE ON user_mission_planning
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Activer RLS sur la table user_mission_planning
ALTER TABLE public.user_mission_planning ENABLE ROW LEVEL SECURITY;

-- Policies pour la table user_mission_planning
CREATE POLICY "Les utilisateurs peuvent voir leurs propres missions dans le planning"
ON public.user_mission_planning
FOR SELECT
USING (
  user_id = auth.uid()
  OR EXISTS (
    SELECT 1 FROM public.user_missions
    WHERE user_missions.id = user_mission_planning.mission_id
    AND user_missions.user_id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM public.user_mission_candidature
    WHERE user_mission_candidature.mission_id = user_mission_planning.mission_id
    AND user_mission_candidature.jobbeur_id = auth.uid()
    AND user_mission_candidature.statut = 'acceptée'
  )
);

CREATE POLICY "Les utilisateurs peuvent ajouter des missions à leur planning"
ON public.user_mission_planning
FOR INSERT
WITH CHECK (
  (mission_id IS NULL AND user_id = auth.uid())
  OR (
    EXISTS (
      SELECT 1 FROM public.user_missions
      WHERE user_missions.id = mission_id
      AND user_missions.user_id = auth.uid()
    )
  )
);

CREATE POLICY "Les utilisateurs peuvent modifier leurs missions dans le planning"
ON public.user_mission_planning
FOR UPDATE
USING (
  user_id = auth.uid()
  OR EXISTS (
    SELECT 1 FROM public.user_missions
    WHERE user_missions.id = user_mission_planning.mission_id
    AND user_missions.user_id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM public.user_mission_candidature
    WHERE user_mission_candidature.mission_id = user_mission_planning.mission_id
    AND user_mission_candidature.jobbeur_id = auth.uid()
    AND user_mission_candidature.statut = 'acceptée'
  )
)
WITH CHECK (
  user_id = auth.uid()
  OR EXISTS (
    SELECT 1 FROM public.user_missions
    WHERE user_missions.id = mission_id
    AND user_missions.user_id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM public.user_mission_candidature
    WHERE user_mission_candidature.mission_id = mission_id
    AND user_mission_candidature.jobbeur_id = auth.uid()
    AND user_mission_candidature.statut = 'acceptée'
  )
);

CREATE POLICY "Les utilisateurs peuvent supprimer leurs missions du planning"
ON public.user_mission_planning
FOR DELETE
USING (
  user_id = auth.uid()
  OR EXISTS (
    SELECT 1 FROM public.user_missions
    WHERE user_missions.id = user_mission_planning.mission_id
    AND user_missions.user_id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM public.user_mission_candidature
    WHERE user_mission_candidature.mission_id = user_mission_planning.mission_id
    AND user_mission_candidature.jobbeur_id = auth.uid()
    AND user_mission_candidature.statut = 'acceptée'
  )
);

-- S'assurer que le service role a tous les droits
GRANT ALL PRIVILEGES ON TABLE public.user_mission_planning TO service_role;

-- Création de la table user_badges_verification pour stocker la date de dernière vérification des badges
CREATE TABLE IF NOT EXISTS user_badges_verification (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  derniere_verif TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE user_badges_verification IS 'Table qui stocke la date de la dernière vérification des badges pour chaque utilisateur';
COMMENT ON COLUMN user_badges_verification.id IS 'Identifiant unique de l''enregistrement';
COMMENT ON COLUMN user_badges_verification.user_id IS 'Identifiant de l''utilisateur concerné';
COMMENT ON COLUMN user_badges_verification.derniere_verif IS 'Date et heure de la dernière vérification des badges';
COMMENT ON COLUMN user_badges_verification.created_at IS 'Date de création de l''enregistrement';
COMMENT ON COLUMN user_badges_verification.updated_at IS 'Date de dernière mise à jour de l''enregistrement';

-- Création d'un déclencheur pour mettre à jour le champ updated_at automatiquement
CREATE TRIGGER handle_user_badges_verification_updated_at
BEFORE UPDATE ON user_badges_verification
FOR EACH ROW
EXECUTE FUNCTION handle_updated_at();

-- Création d'index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_user_badges_verification_user_id ON user_badges_verification(user_id);
CREATE INDEX IF NOT EXISTS idx_user_badges_verification_derniere_verif ON user_badges_verification(derniere_verif);

-- Activer RLS (Row Level Security) sur la table
ALTER TABLE user_badges_verification ENABLE ROW LEVEL SECURITY;

-- Politiques RLS
CREATE POLICY "Les utilisateurs peuvent voir leurs propres vérifications de badges"
ON user_badges_verification
FOR SELECT
USING (auth.uid() = user_id OR auth.jwt()->>'role' = 'jobpadm' OR auth.jwt()->>'role' = 'jobmodo');

CREATE POLICY "Les administrateurs et modérateurs peuvent mettre à jour les vérifications de badges"
ON user_badges_verification
FOR UPDATE
USING (auth.uid() = user_id OR auth.jwt()->>'role' = 'jobpadm' OR auth.jwt()->>'role' = 'jobmodo');

CREATE POLICY "Les administrateurs et modérateurs peuvent insérer des vérifications de badges"
ON user_badges_verification
FOR INSERT
WITH CHECK (auth.uid() = user_id OR auth.jwt()->>'role' = 'jobpadm' OR auth.jwt()->>'role' = 'jobmodo');

-- S'assurer que le service role a tous les droits
GRANT ALL PRIVILEGES ON TABLE user_badges_verification TO service_role;

-- Table pour suivre les actions des utilisateurs (favoris, réponses aux avis, offres, etc.)
create table if not exists public.user_actions_history (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.users(id) on delete cascade not null,
  action_type text not null, -- 'review_response', 'mission_response', 'favorite_add', 'jobi_transaction', etc.
  action_date timestamp with time zone default timezone('utc'::text, now()) not null,
  resource_id uuid, -- ID de la ressource concernée (review_id, mission_id, etc.)
  resource_type text, -- Type de ressource ('review', 'mission', 'profile', etc.)
  details jsonb, -- Détails supplémentaires en format JSON
  ip_address text,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Créer les index pour la nouvelle table
create index if not exists idx_user_actions_history_user_id on public.user_actions_history(user_id);
create index if not exists idx_user_actions_history_action_type on public.user_actions_history(action_type);
create index if not exists idx_user_actions_history_action_date on public.user_actions_history(action_date);
create index if not exists idx_user_actions_history_resource_id on public.user_actions_history(resource_id);
create index if not exists idx_user_actions_history_resource_type on public.user_actions_history(resource_type);

-- Ajouter le trigger pour la mise à jour du timestamp updated_at
DO $$
BEGIN
    -- Trigger pour user_actions_history
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_user_actions_history_updated_at') THEN
        CREATE TRIGGER handle_user_actions_history_updated_at
            BEFORE UPDATE ON user_actions_history
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Activer RLS sur la table user_actions_history
ALTER TABLE public.user_actions_history ENABLE ROW LEVEL SECURITY;

-- Policies pour la nouvelle table
create policy "Les utilisateurs peuvent voir leur propre historique d'actions"
  on public.user_actions_history for select
  using (auth.uid() = user_id);

create policy "L'application peut insérer des actions"
  on public.user_actions_history for insert
  with check (true);

create policy "Les administrateurs peuvent voir tous les historiques d'actions"
  on public.user_actions_history for select
  using (
    exists (
      select 1 from users
      where users.id = auth.uid()
      and users.role in ('jobpadm', 'jobmodo')
    )
  );

-- Fonction RPC pour récupérer les types d'actions distincts pour un utilisateur
CREATE OR REPLACE FUNCTION get_distinct_action_types(user_id_param uuid)
RETURNS TABLE (action_type text) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT uah.action_type
  FROM public.user_actions_history uah
  WHERE uah.user_id = user_id_param
  ORDER BY uah.action_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Activer RLS sur la table user_actions_history
ALTER TABLE public.user_actions_history ENABLE ROW LEVEL SECURITY;





-- Table pour les documents (devis et factures)
CREATE TABLE IF NOT EXISTS public.invoices (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  number TEXT NOT NULL UNIQUE,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('devis', 'facture', 'avoir')),
  client_name TEXT NOT NULL,
  client_address TEXT,
  client_email TEXT,
  client_phone TEXT,
  client_siret TEXT,
  client_tva TEXT,
  forme_juridique TEXT, -- Forme juridique du client (SARL, SAS, EI, etc.)
  code_ape TEXT,        -- Code APE du client (ex: 3811Z)

  -- Information du document
  date_creation TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  date_validite TIMESTAMP WITH TIME ZONE,
  date_paiement TIMESTAMP WITH TIME ZONE,

  -- Montants
  total_ht NUMERIC(10, 2) NOT NULL,
  total_tva NUMERIC(10, 2) NOT NULL,
  total_ttc NUMERIC(10, 2) NOT NULL,

  -- Pour les factures
  conditions_paiement TEXT,
  mode_paiement TEXT CHECK (mode_paiement IN ('virement', 'carte', 'cheque', 'jobi', 'especes')),

  -- Statut
  statut TEXT NOT NULL CHECK (statut IN (
    'brouillon',
    'envoye',
    'accepte',
    'refuse',
    'expire',
    'paye',
    'partiellement_paye',
    'en_retard',
    'annule'
  )),

  -- Mentions légales
  mentions_legales TEXT,
  mentions_tva TEXT,
  penalite_retard TEXT,
  indemnite_recouvrement TEXT,

  -- Si c'est un avoir, référence à la facture originale
  facture_origine_id uuid REFERENCES public.invoices(id),

  -- Pour les devis convertis en facture
  devis_origine_id uuid REFERENCES public.invoices(id),

  description TEXT,
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Table pour les lignes de devis/factures (détails des prestations)
CREATE TABLE IF NOT EXISTS public.invoice_items (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  invoice_id uuid REFERENCES public.invoices(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  quantite NUMERIC(10, 2) NOT NULL,
  unite TEXT,
  prix_unitaire NUMERIC(10, 2) NOT NULL,
  taux_tva NUMERIC(5, 2) NOT NULL CHECK (taux_tva IN (0, 5.5, 10, 20)),
  montant_ht NUMERIC(10, 2) NOT NULL,
  montant_tva NUMERIC(10, 2) NOT NULL,
  montant_ttc NUMERIC(10, 2) NOT NULL,
  ordre INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Table pour les historiques d'envoi
CREATE TABLE IF NOT EXISTS public.invoice_sendings (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  invoice_id uuid REFERENCES public.invoices(id) ON DELETE CASCADE,
  recipient_email TEXT NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  status TEXT NOT NULL CHECK (status IN ('success', 'failed')),
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Table pour les historiques des documents (pour suivre les modifications)
CREATE TABLE IF NOT EXISTS public.invoice_history (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  invoice_id uuid REFERENCES public.invoices(id) ON DELETE CASCADE,
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Fonction pour mettre à jour le timestamp 'updated_at'
CREATE OR REPLACE FUNCTION update_invoice_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Création des triggers pour la mise à jour automatique de updated_at
CREATE TRIGGER update_invoices_updated_at
  BEFORE UPDATE ON invoices
  FOR EACH ROW
  EXECUTE FUNCTION update_invoice_updated_at();

CREATE TRIGGER update_invoice_items_updated_at
  BEFORE UPDATE ON invoice_items
  FOR EACH ROW
  EXECUTE FUNCTION update_invoice_updated_at();

CREATE TRIGGER update_invoice_sendings_updated_at
  BEFORE UPDATE ON invoice_sendings
  FOR EACH ROW
  EXECUTE FUNCTION update_invoice_updated_at();

-- Création des index
CREATE INDEX idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX idx_invoices_type ON public.invoices(type);
CREATE INDEX idx_invoices_statut ON public.invoices(statut);
CREATE INDEX idx_invoices_date_creation ON public.invoices(date_creation);
CREATE INDEX idx_invoices_number ON public.invoices(number);
CREATE INDEX idx_invoices_client_name ON public.invoices(client_name);
CREATE INDEX idx_invoice_items_invoice_id ON public.invoice_items(invoice_id);
CREATE INDEX idx_invoice_sendings_invoice_id ON public.invoice_sendings(invoice_id);
CREATE INDEX idx_invoice_history_invoice_id ON public.invoice_history(invoice_id);
CREATE INDEX idx_invoice_history_user_id ON public.invoice_history(user_id);
CREATE INDEX idx_invoices_forme_juridique ON public.invoices(forme_juridique);
CREATE INDEX idx_invoices_code_ape ON public.invoices(code_ape);

-- Ajouter un champ pour les numéros de brouillon
ALTER TABLE public.invoices
ADD COLUMN IF NOT EXISTS draft_number TEXT;

-- Index pour les numéros de brouillon
CREATE INDEX IF NOT EXISTS idx_invoices_draft_number ON public.invoices(draft_number);

-- Fonction pour générer un numéro séquentiel pour les factures et devis
CREATE OR REPLACE FUNCTION generate_invoice_number(doc_type TEXT, is_draft BOOLEAN DEFAULT FALSE)
RETURNS TEXT AS $$
DECLARE
  year TEXT;
  counter INTEGER;
  prefix TEXT;
  last_number TEXT;
  new_number TEXT;
BEGIN
  year := TO_CHAR(CURRENT_DATE, 'YYYY');

  -- Si c'est un brouillon, utiliser un préfixe différent
  IF is_draft THEN
    IF doc_type = 'devis' THEN
      prefix := 'BROUILLON-DEV-' || year || '-';
    ELSIF doc_type = 'facture' THEN
      prefix := 'BROUILLON-FAC-' || year || '-';
    ELSIF doc_type = 'avoir' THEN
      prefix := 'BROUILLON-AVO-' || year || '-';
    ELSE
      RAISE EXCEPTION 'Type de document non reconnu: %', doc_type;
    END IF;

    -- Pour les brouillons, utiliser un compteur basé sur les draft_number
    SELECT draft_number INTO last_number FROM public.invoices
    WHERE draft_number LIKE prefix || '%'
    ORDER BY draft_number DESC
    LIMIT 1;
  ELSE
    -- Pour les documents officiels
    IF doc_type = 'devis' THEN
      prefix := 'DEV-' || year || '-';
    ELSIF doc_type = 'facture' THEN
      prefix := 'FAC-' || year || '-';
    ELSIF doc_type = 'avoir' THEN
      prefix := 'AVO-' || year || '-';
    ELSE
      RAISE EXCEPTION 'Type de document non reconnu: %', doc_type;
    END IF;

    -- Pour les documents officiels, exclure les brouillons du comptage
    SELECT number INTO last_number FROM public.invoices
    WHERE number LIKE prefix || '%'
    AND statut != 'brouillon'
    ORDER BY number DESC
    LIMIT 1;
  END IF;

  IF last_number IS NULL THEN
    counter := 1;
  ELSE
    counter := TO_NUMBER(SUBSTRING(last_number FROM LENGTH(prefix) + 1), '9999') + 1;
  END IF;

  -- Générer le nouveau numéro avec padding à 4 chiffres
  new_number := prefix || LPAD(counter::TEXT, 4, '0');

  RETURN new_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Active Row Level Security
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_sendings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_history ENABLE ROW LEVEL SECURITY;

-- Politique RLS
DROP POLICY IF EXISTS "Les utilisateurs peuvent voir leurs propres factures" ON public.invoices;
CREATE POLICY "Les utilisateurs peuvent voir leurs propres factures"
ON public.invoices FOR SELECT
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Les utilisateurs peuvent modifier leurs propres factures" ON public.invoices;
CREATE POLICY "Les utilisateurs peuvent modifier leurs propres factures"
ON public.invoices FOR UPDATE
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Les utilisateurs peuvent supprimer leurs propres factures" ON public.invoices;
CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres factures"
ON public.invoices FOR DELETE
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Les utilisateurs peuvent insérer des factures" ON public.invoices;
CREATE POLICY "Les utilisateurs peuvent insérer des factures"
ON public.invoices FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Similaires pour les autres tables...
CREATE POLICY "Les utilisateurs peuvent voir leurs propres lignes de facture"
ON public.invoice_items FOR SELECT
USING ((SELECT user_id FROM public.invoices WHERE id = invoice_id) = auth.uid());

CREATE POLICY "Les utilisateurs peuvent modifier leurs propres lignes de facture"
ON public.invoice_items FOR UPDATE
USING ((SELECT user_id FROM public.invoices WHERE id = invoice_id) = auth.uid());

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres lignes de facture"
ON public.invoice_items FOR DELETE
USING ((SELECT user_id FROM public.invoices WHERE id = invoice_id) = auth.uid());

CREATE POLICY "Les utilisateurs peuvent insérer des lignes de facture"
ON public.invoice_items FOR INSERT
WITH CHECK ((SELECT user_id FROM public.invoices WHERE id = invoice_id) = auth.uid());



-- Table pour les clients de facturation
CREATE TABLE IF NOT EXISTS public.invoices_client (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  nom TEXT NOT NULL,
  email TEXT,
  telephone TEXT,
  adresse TEXT,
  siret TEXT,
  tva TEXT,
  forme_juridique TEXT, -- Forme juridique du client (SARL, SAS, EI, etc.)
  code_ape TEXT,        -- Code APE du client (ex: 3811Z)
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Ajout d'une contrainte de clé étrangère dans la table invoices
ALTER TABLE public.invoices
ADD COLUMN IF NOT EXISTS client_id uuid REFERENCES public.invoices_client(id) ON DELETE SET NULL;

-- Ajout de la colonne client_user_id à la table invoices_client pour lier les clients aux utilisateurs de la plateforme
ALTER TABLE public.invoices_client
ADD COLUMN IF NOT EXISTS client_user_id uuid REFERENCES public.users(id) ON DELETE CASCADE;

-- Index pour la nouvelle colonne
CREATE INDEX IF NOT EXISTS idx_invoices_client_user_id ON public.invoices_client(client_user_id);

-- Index pour la table invoices_client
CREATE INDEX IF NOT EXISTS idx_invoices_client_user_id ON public.invoices_client(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_client_nom ON public.invoices_client(nom);
CREATE INDEX IF NOT EXISTS idx_invoices_client_siret ON public.invoices_client(siret);
CREATE INDEX IF NOT EXISTS idx_invoices_client_forme_juridique ON public.invoices_client(forme_juridique);
CREATE INDEX IF NOT EXISTS idx_invoices_client_code_ape ON public.invoices_client(code_ape);









-- Table pour stocker les statistiques de génération d'images IA
CREATE TABLE IF NOT EXISTS public.ai_image_generation_stats (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  purpose text NOT NULL CHECK (purpose IN ('profile_picture', 'banner_picture', 'mission_image', 'gallery_photo', 'featured_photo', 'card_editor')),
  prompt text NOT NULL,
  cost numeric(10, 6) DEFAULT 0,
  image_id text,
  image_url text,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_ai_image_generation_stats_user_id ON public.ai_image_generation_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_image_generation_stats_purpose ON public.ai_image_generation_stats(purpose);
CREATE INDEX IF NOT EXISTS idx_ai_image_generation_stats_created_at ON public.ai_image_generation_stats(created_at);

-- Trigger pour mettre à jour le timestamp updated_at
CREATE TRIGGER handle_ai_image_generation_stats_updated_at
  BEFORE UPDATE ON ai_image_generation_stats
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Activer RLS sur la table
ALTER TABLE public.ai_image_generation_stats ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les utilisateurs peuvent voir leurs propres statistiques de génération d'images"
  ON public.ai_image_generation_stats
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Les administrateurs peuvent voir toutes les statistiques de génération d'images"
  ON public.ai_image_generation_stats
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid()
      AND role IN ('jobpadm', 'jobmodo')
    )
  );

CREATE POLICY "L'application peut insérer des statistiques de génération d'images"
  ON public.ai_image_generation_stats
  FOR INSERT
  WITH CHECK (true);

-- Accorder les privilèges nécessaires
GRANT ALL PRIVILEGES ON TABLE public.ai_image_generation_stats TO service_role;







-- File d'attente d'emails générique
CREATE TABLE IF NOT EXISTS public.email_queue (
  id SERIAL PRIMARY KEY,
  to_email TEXT NOT NULL,
  email_hash TEXT, -- Hash SHA-256 de l'email pour recherche anti-spam efficace
  subject TEXT NOT NULL,
  html TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'error'
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  sent_at TIMESTAMP WITH TIME ZONE
);

ALTER TABLE public.email_queue
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_retry TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS attachments TEXT,
ADD COLUMN IF NOT EXISTS email_hash TEXT; -- Ajouter la colonne email_hash si elle n'existe pas

-- Index pour optimiser les requêtes sur les nouvelles colonnes
CREATE INDEX IF NOT EXISTS idx_email_queue_retry_count ON public.email_queue(retry_count);
CREATE INDEX IF NOT EXISTS idx_email_queue_last_retry ON public.email_queue(last_retry);

-- Index pour accélérer la récupération des emails à envoyer
CREATE INDEX IF NOT EXISTS idx_email_queue_status ON public.email_queue(status);
CREATE INDEX IF NOT EXISTS idx_email_queue_created_at ON public.email_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_email_queue_to_email ON public.email_queue(to_email);
CREATE INDEX IF NOT EXISTS idx_email_queue_email_hash ON public.email_queue(email_hash); -- Index pour la recherche anti-spam

-- Sécurité : Row Level Security
ALTER TABLE public.email_queue ENABLE ROW LEVEL SECURITY;

-- Policy : seuls les rôles de service (backend) peuvent lire/écrire
CREATE POLICY "Service role peut tout faire sur email_queue"
  ON public.email_queue
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Optionnel : empêcher tout accès utilisateur normal (si besoin)
-- REVOKE ALL ON public.email_queue FROM authenticated, anon;

-- Droits complets pour le service role
GRANT ALL PRIVILEGES ON TABLE public.email_queue TO service_role;



-- Table pour les paramètres de l'entreprise pour la facturation
CREATE TABLE IF NOT EXISTS public.invoices_company_settings (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  nom TEXT NOT NULL,
  adresse TEXT,
  code_postal TEXT,
  ville TEXT,
  pays TEXT DEFAULT 'France',
  telephone TEXT,
  email TEXT,
  site_web TEXT,
  siret TEXT,
  tva TEXT,
  forme_juridique TEXT,
  code_ape TEXT,
  rcs TEXT,
  capital TEXT,
  logo_url TEXT,
  signature_url TEXT,
  iban TEXT,
  bic TEXT,
  banque TEXT,
  mention_pied_page TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);
-- Table pour les signalements de contenus inappropriés
CREATE TABLE IF NOT EXISTS public.reported_content (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  content_type TEXT NOT NULL CHECK (
    content_type IN (
      'comment', 'message', 'profile', 'biography', 'review', 'mission', 'post',
      'comment_post', 'message_post', 'review_post', 'mission_post',
      'comment_review', 'message_review', 'review_review', 'mission_review',
      'profile_picture', 'banner_picture', 'titre_service', 'description_service',
      'gallery_name', 'gallery_description', 'mission_title', 'mission_description',
      'gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar', 'card_editor'
    )
  ),
  content_id uuid NOT NULL,
  reported_by uuid REFERENCES public.users(id) ON DELETE SET NULL,
  reported_user_id uuid REFERENCES public.users(id) ON DELETE SET NULL, -- utilisateur auteur du contenu signalé
  reason TEXT NOT NULL,
  internal_note TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'validated', 'rejected', 'content_deleted', 'masqué', 'restauré')),
  admin_id uuid REFERENCES public.users(id) ON DELETE SET NULL, -- admin/modo en charge
  admin_comment TEXT,
  content_snapshot TEXT,
  temp_image_path TEXT, -- Chemin de l'image temporaire dans le bucket temp_moderation
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

CREATE INDEX IF NOT EXISTS idx_reported_content_content_type ON public.reported_content(content_type);
CREATE INDEX IF NOT EXISTS idx_reported_content_content_id ON public.reported_content(content_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_status ON public.reported_content(status);
CREATE INDEX IF NOT EXISTS idx_reported_content_reported_by ON public.reported_content(reported_by);
CREATE INDEX IF NOT EXISTS idx_reported_content_reported_user_id ON public.reported_content(reported_user_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_admin_id ON public.reported_content(admin_id);

-- Trigger pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION handle_reported_content_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_reported_content_updated_at') THEN
    CREATE TRIGGER handle_reported_content_updated_at
      BEFORE UPDATE ON reported_content
      FOR EACH ROW
      EXECUTE FUNCTION handle_reported_content_updated_at();
  END IF;
END $$;

-- Historique des actions/modérations sur les signalements
create table if not exists public.reported_content_history (
  id uuid default uuid_generate_v4() primary key,
  report_id uuid references public.reported_content(id) on delete cascade,
  action text not null,
  admin_id uuid references public.users(id),
  comment text,
  date timestamp with time zone default timezone('utc'::text, now())
);

create index if not exists idx_reported_content_history_report_id on public.reported_content_history(report_id);
create index if not exists idx_reported_content_history_admin_id on public.reported_content_history(admin_id);
create index if not exists idx_reported_content_history_date on public.reported_content_history(date);

-- Sécurité et policies pour l'historique des signalements
ALTER TABLE public.reported_content_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Lecture de l'historique des signalements par admin/modo"
  ON public.reported_content_history FOR SELECT
  USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Ajout dans l'historique des signalements par admin/modo"
  ON public.reported_content_history FOR INSERT
  WITH CHECK (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Modification de l'historique des signalements par admin/modo"
  ON public.reported_content_history FOR UPDATE
  USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')))
  WITH CHECK (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Suppression de l'historique des signalements par admin/modo"
  ON public.reported_content_history FOR DELETE
  USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

GRANT ALL PRIVILEGES ON TABLE public.reported_content_history TO service_role;

-- Trigger pour updated_at si besoin
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_reported_content_history_updated_at') THEN
        CREATE TRIGGER handle_reported_content_history_updated_at
            BEFORE UPDATE ON reported_content_history
            FOR EACH ROW
            EXECUTE FUNCTION handle_updated_at();
    END IF;
END $$;

-- Nouvelle table pour les signalements individuels (multi-signalement)
CREATE TABLE IF NOT EXISTS public.reported_content_reports (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  reported_content_id uuid REFERENCES public.reported_content(id) ON DELETE CASCADE,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE (reported_content_id, user_id)
);

CREATE INDEX IF NOT EXISTS idx_reported_content_reports_content_id ON public.reported_content_reports(reported_content_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_reports_user_id ON public.reported_content_reports(user_id);

-- RLS pour reported_content_reports
ALTER TABLE public.reported_content_reports ENABLE ROW LEVEL SECURITY;

-- Policy : chaque utilisateur ne peut voir que ses propres reports
CREATE POLICY "Lecture de ses propres signalements individuels" ON public.reported_content_reports
  FOR SELECT USING (user_id = auth.uid());

-- Policy : admin/modo peuvent tout voir
CREATE POLICY "Lecture des signalements individuels par admin/modo" ON public.reported_content_reports
  FOR SELECT USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- Policy : chaque utilisateur peut insérer un report pour lui-même
CREATE POLICY "Ajout de signalement individuel par l'utilisateur" ON public.reported_content_reports
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Policy : suppression possible par admin/modo
CREATE POLICY "Suppression des reports par admin/modo" ON public.reported_content_reports
  FOR DELETE USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

GRANT ALL PRIVILEGES ON TABLE public.reported_content_reports TO service_role;


-- Ajout d'une contrainte de clé étrangère dans la table invoices
ALTER TABLE public.invoices
ADD COLUMN IF NOT EXISTS client_id uuid REFERENCES public.invoices_client(id) ON DELETE SET NULL;

-- Index pour la nouvelle colonne
CREATE INDEX IF NOT EXISTS idx_invoices_client_id ON public.invoices(client_id);

-- Table pour les paramètres de l'entreprise pour la facturation
CREATE TABLE IF NOT EXISTS public.invoices_company_settings (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  nom TEXT NOT NULL,
  adresse TEXT,
  code_postal TEXT,
  ville TEXT,
  pays TEXT DEFAULT 'France',
  telephone TEXT,
  email TEXT,
  site_web TEXT,
  siret TEXT,
  tva TEXT,
  forme_juridique TEXT,
  code_ape TEXT,
  rcs TEXT,
  capital TEXT,
  logo_url TEXT,
  signature_url TEXT,
  iban TEXT,
  bic TEXT,
  banque TEXT,
  mention_pied_page TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Table pour les signalements de contenus inappropriés
CREATE TABLE IF NOT EXISTS public.reported_content (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  content_type TEXT NOT NULL CHECK (content_type IN ('comment', 'message', 'profile', 'biography', 'review', 'mission', 'post',
      'comment_post', 'message_post', 'review_post', 'mission_post',
      'comment_review', 'message_review', 'review_review', 'mission_review',
      'profile_picture', 'banner_picture', 'titre_service', 'description_service',
      'gallery_name', 'gallery_description', 'mission_title', 'mission_description',
      'gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar', 'card_editor')),
  content_id uuid NOT NULL,
  reported_by uuid REFERENCES public.users(id) ON DELETE SET NULL,
  reported_user_id uuid REFERENCES public.users(id) ON DELETE SET NULL, -- utilisateur auteur du contenu signalé
  reason TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'validated', 'rejected', 'content_deleted', 'masqué', 'restauré')),
  admin_id uuid REFERENCES public.users(id) ON DELETE SET NULL, -- admin/modo en charge
  admin_comment TEXT,
  content_snapshot TEXT,
  temp_image_path TEXT, -- Chemin de l'image temporaire dans le bucket temp_moderation
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

CREATE INDEX IF NOT EXISTS idx_reported_content_content_type ON public.reported_content(content_type);
CREATE INDEX IF NOT EXISTS idx_reported_content_content_id ON public.reported_content(content_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_status ON public.reported_content(status);
CREATE INDEX IF NOT EXISTS idx_reported_content_reported_by ON public.reported_content(reported_by);
CREATE INDEX IF NOT EXISTS idx_reported_content_reported_user_id ON public.reported_content(reported_user_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_admin_id ON public.reported_content(admin_id);

-- Trigger pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION handle_reported_content_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'handle_reported_content_updated_at') THEN
    CREATE TRIGGER handle_reported_content_updated_at
      BEFORE UPDATE ON reported_content
      FOR EACH ROW
      EXECUTE FUNCTION handle_reported_content_updated_at();
  END IF;
END $$;

-- Table de gestion des documents de vérification entreprise
CREATE TABLE IF NOT EXISTS entreprise_verification_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(32) NOT NULL, -- kbis, assurance, identity, autre
    file_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    status VARCHAR(16) NOT NULL DEFAULT 'pending', -- pending, approved, rejected
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT now(),
    verification_date TIMESTAMP WITH TIME ZONE,
    comments TEXT,
    admin_id UUID REFERENCES users(id), -- qui a validé/refusé
    deleted BOOLEAN DEFAULT FALSE
);
CREATE INDEX IF NOT EXISTS idx_entreprise_verif_user ON entreprise_verification_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_entreprise_verif_status ON entreprise_verification_documents(status);

-- tables promo_codes et promo_code_usage
CREATE TABLE IF NOT EXISTS promo_codes (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  discount_type VARCHAR(20) NOT NULL, -- 'percentage' ou 'fixed'
  discount_value DECIMAL(10, 2) NOT NULL,
  max_uses INTEGER,
  remaining_uses INTEGER,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  plan_type VARCHAR(20), -- 'premium', 'gratuit', null pour tous
  description TEXT,
  duration_type VARCHAR(20) NOT NULL, -- 'lifetime' ou 'one_time'
  limit_first_users INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS promo_code_usage (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  promo_code_id uuid NOT NULL REFERENCES promo_codes(id) ON DELETE CASCADE,
  plan_type VARCHAR(20) NOT NULL,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, promo_code_id)
);

-- Index pour améliorer les performances de recherche
CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON promo_codes(code);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_user_id ON promo_code_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_promo_code_usage_promo_code_id ON promo_code_usage(promo_code_id);

-- Fonction pour récupérer les statistiques des qualités d'un utilisateur
CREATE OR REPLACE FUNCTION public.get_qualites_stats(user_id_param uuid)
RETURNS TABLE (qualite text, count bigint) AS $$
BEGIN
  RETURN QUERY
  SELECT
    unnest(qualites) as qualite,
    COUNT(*) as count
  FROM
    public.user_reviews
  WHERE
    target_user_id = user_id_param
    AND statut = 'visible' -- Ne compter que les avis visibles
    AND qualites IS NOT NULL AND array_length(qualites, 1) > 0 -- Ignorer les tableaux vides ou null
  GROUP BY
    qualite
  ORDER BY
    count DESC, qualite ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Donner les permissions nécessaires au rôle de service
GRANT EXECUTE ON FUNCTION public.get_qualites_stats(uuid) TO service_role;

-- Index pour améliorer les performances de la fonction get_qualites_stats
CREATE INDEX IF NOT EXISTS idx_user_reviews_qualites_gin ON public.user_reviews USING gin (qualites) WHERE statut = 'visible' AND qualites IS NOT NULL AND array_length(qualites, 1) > 0;


-- Table pour les abonnés à la newsletter
CREATE TABLE IF NOT EXISTS public.newsletter_subscribers (
  id uuid default uuid_generate_v4() primary key,
  email text unique not null,
  is_verified boolean default false,
  verification_token text,
  verification_token_expires_at timestamp with time zone,
  subscribed_at timestamp with time zone default timezone('utc'::text, now()),
  verified_at timestamp with time zone,
  created_at timestamp with time zone default timezone('utc'::text, now()),
  updated_at timestamp with time zone default timezone('utc'::text, now())
);

-- Index pour optimiser les recherches
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_email ON public.newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_is_verified ON public.newsletter_subscribers(is_verified);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_verification_token ON public.newsletter_subscribers(verification_token);

-- Trigger pour mettre à jour updated_at
CREATE TRIGGER handle_newsletter_subscribers_updated_at
  BEFORE UPDATE ON newsletter_subscribers
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Activation de RLS
ALTER TABLE public.newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les abonnés à la newsletter peuvent être lus par l'application"
ON public.newsletter_subscribers FOR SELECT
USING (true);

CREATE POLICY "Les abonnés à la newsletter peuvent être créés par l'application"
ON public.newsletter_subscribers FOR INSERT
WITH CHECK (true);

CREATE POLICY "Les abonnés à la newsletter peuvent être mis à jour par l'application"
ON public.newsletter_subscribers FOR UPDATE
USING (true);

CREATE POLICY "Les abonnés à la newsletter peuvent être supprimés par l'application"
ON public.newsletter_subscribers FOR DELETE
USING (true);

-- Ajouter les colonnes nécessaires pour le système de relance
ALTER TABLE newsletter_subscribers
ADD COLUMN IF NOT EXISTS reminder_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_reminder_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS next_reminder_at TIMESTAMP WITH TIME ZONE;

-- Créer un index pour optimiser les requêtes de relance
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_reminders
ON newsletter_subscribers(is_verified, next_reminder_at);

-- Commentaires sur les colonnes
COMMENT ON COLUMN newsletter_subscribers.reminder_count IS 'Nombre de relances envoyées pour la vérification de l''email';
COMMENT ON COLUMN newsletter_subscribers.last_reminder_at IS 'Date de la dernière relance envoyée';
COMMENT ON COLUMN newsletter_subscribers.next_reminder_at IS 'Date prévue pour la prochaine relance';



-- Table pour stocker les logs de modération de contenu
CREATE TABLE IF NOT EXISTS content_moderation_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  content_type TEXT NOT NULL CHECK (content_type IN (
    'mission', 'comment', 'profile', 'biography', 'profile_picture', 'banner_picture',
    'titre_service', 'description_service', 'gallery_name', 'gallery_description',
    'mission_title', 'mission_description', 'review',
    'gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar',
    'featured_photo', 'mission_image', 'gallery_photo', 'card_editor'
  )),
  content_id TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id),
  is_safe BOOLEAN NOT NULL,
  score FLOAT NOT NULL,
  categories JSONB NOT NULL,
  flagged_text TEXT[] DEFAULT '{}',
  moderation_id TEXT NOT NULL,
  image_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Index pour accélérer les recherches
  CONSTRAINT unique_content_moderation UNIQUE (content_type, content_id, moderation_id)
);

-- Mettre à jour la contrainte CHECK sur la table content_moderation_logs
-- pour inclure les nouveaux types de contenu pour les images

-- D'abord, supprimer la contrainte existante
ALTER TABLE content_moderation_logs
DROP CONSTRAINT IF EXISTS content_moderation_logs_content_type_check;

-- Ensuite, ajouter la nouvelle contrainte avec les types supplémentaires
ALTER TABLE content_moderation_logs
ADD CONSTRAINT content_moderation_logs_content_type_check
CHECK (content_type IN (
  'mission', 'comment', 'profile', 'biography', 'profile_picture', 'banner_picture',
  'titre_service', 'description_service', 'gallery_name', 'gallery_description',
  'mission_title', 'mission_description', 'review',
  'gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar',
  'featured_photo', 'mission_image', 'gallery_photo', 'card_editor'
));

-- Ajouter une colonne pour indiquer s'il s'agit d'une modération d'image si elle n'existe pas déjà
ALTER TABLE content_moderation_logs
ADD COLUMN IF NOT EXISTS is_image_moderation BOOLEAN DEFAULT FALSE;

-- Ajouter des colonnes pour stocker les informations spécifiques aux images
ALTER TABLE content_moderation_logs
ADD COLUMN IF NOT EXISTS service_type TEXT,
ADD COLUMN IF NOT EXISTS relevant_to_user_services BOOLEAN,
ADD COLUMN IF NOT EXISTS quality_assessment JSONB,
ADD COLUMN IF NOT EXISTS improvement_suggestions TEXT;

-- Commentaires explicatifs pour les nouvelles colonnes
COMMENT ON COLUMN content_moderation_logs.is_image_moderation IS 'Indique s''il s''agit d''une modération d''image';
COMMENT ON COLUMN content_moderation_logs.service_type IS 'Type de service associé à l''image (pour les modérations d''images)';
COMMENT ON COLUMN content_moderation_logs.relevant_to_user_services IS 'Indique si l''image est pertinente pour les services de l''utilisateur';
COMMENT ON COLUMN content_moderation_logs.quality_assessment IS 'Évaluation de la qualité de l''image (clarté, pertinence, etc.)';
COMMENT ON COLUMN content_moderation_logs.improvement_suggestions IS 'Suggestions d''amélioration pour l''image';

-- Index pour les recherches fréquentes
CREATE INDEX IF NOT EXISTS idx_content_moderation_logs_user_id ON content_moderation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_content_moderation_logs_content ON content_moderation_logs(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_content_moderation_logs_created_at ON content_moderation_logs(created_at);

-- Ajouter une colonne à la table reported_content pour stocker les données de modération
ALTER TABLE reported_content ADD COLUMN IF NOT EXISTS moderation_data JSONB DEFAULT NULL;
ALTER TABLE reported_content ADD COLUMN IF NOT EXISTS system_generated BOOLEAN DEFAULT FALSE;

-- Commentaire explicatif
COMMENT ON TABLE content_moderation_logs IS 'Stocke les résultats de modération de contenu pour les missions, commentaires et profils';
COMMENT ON COLUMN content_moderation_logs.content_type IS 'Type de contenu modéré (mission, comment, profile_picture, banner_picture, card_editor, etc.)';
COMMENT ON COLUMN content_moderation_logs.content_id IS 'ID du contenu modéré';
COMMENT ON COLUMN content_moderation_logs.user_id IS 'ID de l''utilisateur qui a créé le contenu';
COMMENT ON COLUMN content_moderation_logs.is_safe IS 'Indique si le contenu est considéré comme sûr';
COMMENT ON COLUMN content_moderation_logs.score IS 'Score de risque (0-1) où 1 est le plus risqué';
COMMENT ON COLUMN content_moderation_logs.categories IS 'Catégories de contenu inapproprié détectées (JSON)';
COMMENT ON COLUMN content_moderation_logs.flagged_text IS 'Extraits de texte signalés comme inappropriés';
COMMENT ON COLUMN content_moderation_logs.moderation_id IS 'ID unique de la modération';
COMMENT ON COLUMN reported_content.moderation_data IS 'Données de modération automatique pour les signalements générés par le système';
COMMENT ON COLUMN reported_content.system_generated IS 'Indique si le signalement a été généré automatiquement par le système';



-- Table pour stocker les crédits IA des utilisateurs
CREATE TABLE IF NOT EXISTS public.user_ai_credits (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  credits INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_user_id ON public.user_ai_credits(user_id);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_ai_credits_updated_at
  BEFORE UPDATE ON user_ai_credits
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Activer RLS sur la table
ALTER TABLE public.user_ai_credits ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les utilisateurs peuvent voir leurs propres crédits IA"
  ON public.user_ai_credits FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Les crédits IA peuvent être modifiés par l'application"
  ON public.user_ai_credits FOR UPDATE
  USING (true);

CREATE POLICY "Les crédits IA peuvent être créés par l'application"
  ON public.user_ai_credits FOR INSERT
  WITH CHECK (true);

-- S'assurer que le service role a tous les droits
GRANT ALL PRIVILEGES ON TABLE public.user_ai_credits TO service_role;


-- Table pour stocker l'historique des crédits IA des utilisateurs
CREATE TABLE IF NOT EXISTS public.user_ai_credits_historique (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
  operation_type TEXT NOT NULL CHECK (operation_type IN ('achat_jobi', 'achat_stripe', 'utilisation', 'offert_abonnement', 'offert_admin', 'autre')),
  montant INTEGER NOT NULL,
  solde_avant INTEGER NOT NULL,
  solde_apres INTEGER NOT NULL,
  description TEXT,
  reference TEXT, -- Pour stocker des références (ID de transaction, ID de mission, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_user_id ON public.user_ai_credits_historique(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_operation_type ON public.user_ai_credits_historique(operation_type);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_created_at ON public.user_ai_credits_historique(created_at);

-- Trigger pour la mise à jour automatique de updated_at
CREATE TRIGGER handle_user_ai_credits_historique_updated_at
  BEFORE UPDATE ON user_ai_credits_historique
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Activer RLS sur la table
ALTER TABLE public.user_ai_credits_historique ENABLE ROW LEVEL SECURITY;

-- Politiques de sécurité
CREATE POLICY "Les utilisateurs peuvent voir leur propre historique de crédits IA"
  ON public.user_ai_credits_historique FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "L'historique des crédits IA peut être créé par l'application"
  ON public.user_ai_credits_historique FOR INSERT
  WITH CHECK (true);

-- S'assurer que le service role a tous les droits
GRANT ALL PRIVILEGES ON TABLE public.user_ai_credits_historique TO service_role;



-- Table pour stocker les prompts personnalisés des utilisateurs
CREATE TABLE IF NOT EXISTS user_ai_prompts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  prompt TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, type)
);

-- Index pour accélérer les recherches par utilisateur
CREATE INDEX IF NOT EXISTS idx_user_ai_prompts_user_id ON user_ai_prompts(user_id);

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE user_ai_prompts IS 'Prompts personnalisés des utilisateurs pour la génération de contenu IA';
COMMENT ON COLUMN user_ai_prompts.id IS 'Identifiant unique du prompt';
COMMENT ON COLUMN user_ai_prompts.user_id IS 'Identifiant de l''utilisateur propriétaire du prompt';
COMMENT ON COLUMN user_ai_prompts.type IS 'Type de prompt (biography, service_description, mission_post, review_response, mission_offer, comment, default_prompt)';
COMMENT ON COLUMN user_ai_prompts.prompt IS 'Texte du prompt personnalisé';
COMMENT ON COLUMN user_ai_prompts.created_at IS 'Date de création du prompt';
COMMENT ON COLUMN user_ai_prompts.updated_at IS 'Date de dernière mise à jour du prompt';

-- Politique RLS pour restreindre l'accès aux prompts
ALTER TABLE user_ai_prompts ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir uniquement leurs propres prompts
CREATE POLICY user_ai_prompts_select_policy ON user_ai_prompts
  FOR SELECT USING (true);  -- Permettre l'accès via l'API backend

-- Politique pour permettre aux utilisateurs de créer uniquement leurs propres prompts
CREATE POLICY user_ai_prompts_insert_policy ON user_ai_prompts
  FOR INSERT WITH CHECK (true);  -- Permettre l'accès via l'API backend

-- Politique pour permettre aux utilisateurs de mettre à jour uniquement leurs propres prompts
CREATE POLICY user_ai_prompts_update_policy ON user_ai_prompts
  FOR UPDATE USING (true);  -- Permettre l'accès via l'API backend

-- Politique pour permettre aux utilisateurs de supprimer uniquement leurs propres prompts
CREATE POLICY user_ai_prompts_delete_policy ON user_ai_prompts
  FOR DELETE USING (true);  -- Permettre l'accès via l'API backend

-- Trigger pour mettre à jour le champ updated_at automatiquement
CREATE OR REPLACE FUNCTION update_user_ai_prompts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_ai_prompts_updated_at_trigger
BEFORE UPDATE ON user_ai_prompts
FOR EACH ROW
EXECUTE FUNCTION update_user_ai_prompts_updated_at();




-- Table pour stocker l'historique d'utilisation de l'API OpenRouter
CREATE TABLE IF NOT EXISTS openrouter_api_usage (    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    service_type TEXT NOT NULL CHECK (service_type IN (
      'moderation',
      'text_moderation',
      'image_moderation',
      'generation',
      'biography_generation',
      'service_description_generation',
      'mission_post_generation',
      'review_response_generation',
      'mission_offer_generation',
      'comment_generation',
      'custom_prompt_generation',
      'other',
      'mission_assistant',
      'avatar_moderation',
      'profile_picture_moderation',
      'midjourney_prompt',
      'card_editor_prompt',
      'slogan_generation',
      'support_user_assistance',
      'support_staff_assistance',
      'support_comment_generation'
    )),
    model TEXT NOT NULL,
    prompt_tokens INTEGER NOT NULL DEFAULT 0,
    completion_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    response_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user_openrouter FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Index pour accélérer les requêtes
CREATE INDEX IF NOT EXISTS idx_openrouter_api_usage_user_id ON public.openrouter_api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_openrouter_api_usage_created_at ON public.openrouter_api_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_openrouter_api_usage_service_type ON public.openrouter_api_usage(service_type);
CREATE INDEX IF NOT EXISTS idx_openrouter_api_usage_model ON public.openrouter_api_usage(model);

-- Commentaires explicatifs
COMMENT ON TABLE public.openrouter_api_usage IS 'Historique d''utilisation de l''API OpenRouter';
COMMENT ON COLUMN public.openrouter_api_usage.id IS 'Identifiant unique de l''enregistrement';
COMMENT ON COLUMN public.openrouter_api_usage.user_id IS 'Identifiant de l''utilisateur qui a effectué l''appel (peut être NULL pour les appels système)';
COMMENT ON COLUMN public.openrouter_api_usage.service_type IS 'Type de service ayant utilisé l''API (voir la contrainte CHECK pour la liste exhaustive)';
COMMENT ON COLUMN public.openrouter_api_usage.model IS 'Modèle utilisé pour cet appel';
COMMENT ON COLUMN public.openrouter_api_usage.prompt_tokens IS 'Nombre de tokens en entrée';
COMMENT ON COLUMN public.openrouter_api_usage.completion_tokens IS 'Nombre de tokens en sortie';
COMMENT ON COLUMN public.openrouter_api_usage.total_tokens IS 'Nombre total de tokens';
COMMENT ON COLUMN public.openrouter_api_usage.response_id IS 'Identifiant de la réponse fourni par l''API';
COMMENT ON COLUMN public.openrouter_api_usage.created_at IS 'Date et heure de l''appel';







-- Table pour stocker les tarifs des modèles OpenRouter
CREATE TABLE IF NOT EXISTS openrouter_model_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model TEXT NOT NULL UNIQUE,
    input_price_per_million DECIMAL(10, 5) NOT NULL, -- Prix par million de tokens en entrée
    output_price_per_million DECIMAL(10, 5) NOT NULL, -- Prix par million de tokens en sortie
    context_window INTEGER NOT NULL, -- Taille maximale du contexte en tokens
    max_output INTEGER NOT NULL, -- Taille maximale de sortie en tokens
    latency_seconds DECIMAL(10, 2), -- Latence moyenne en secondes
    throughput_tokens_per_second DECIMAL(10, 2), -- Débit en tokens par seconde
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger pour mettre à jour le champ updated_at
CREATE OR REPLACE FUNCTION update_openrouter_model_pricing_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_openrouter_model_pricing_updated_at_trigger
BEFORE UPDATE ON openrouter_model_pricing
FOR EACH ROW
EXECUTE FUNCTION update_openrouter_model_pricing_updated_at();

-- Insérer les données de tarification par défaut pour le modèle Llama 3
INSERT INTO openrouter_model_pricing
(model, input_price_per_million, output_price_per_million, context_window, max_output, latency_seconds, throughput_tokens_per_second)
VALUES
('meta-llama/llama-4-maverick:free', 0.17, 0.60, 1050000, 16000, 0.36, 84.93)
ON CONFLICT (model)
DO UPDATE SET
    input_price_per_million = EXCLUDED.input_price_per_million,
    output_price_per_million = EXCLUDED.output_price_per_million,
    context_window = EXCLUDED.context_window,
    max_output = EXCLUDED.max_output,
    latency_seconds = EXCLUDED.latency_seconds,
    throughput_tokens_per_second = EXCLUDED.throughput_tokens_per_second,
    updated_at = NOW();

-- Fonction pour exécuter des requêtes SQL brutes
CREATE OR REPLACE FUNCTION public.execute_sql(sql_query TEXT)
RETURNS SETOF json AS $$
BEGIN
    RETURN QUERY EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Donner les permissions nécessaires
GRANT EXECUTE ON FUNCTION public.execute_sql(TEXT) TO service_role;

-- Fonction pour exécuter des requêtes SQL brutes et convertir les résultats en JSON
CREATE OR REPLACE FUNCTION public.execute_sql_to_json(sql_query TEXT)
RETURNS SETOF json AS $$
DECLARE
    result_row RECORD;
BEGIN
    FOR result_row IN EXECUTE sql_query LOOP
        RETURN NEXT row_to_json(result_row);
    END LOOP;
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path='';

-- Donner les permissions nécessaires
GRANT EXECUTE ON FUNCTION public.execute_sql_to_json(TEXT) TO service_role;







-- Table pour stocker les consentements des utilisateurs pour l'utilisation de l'IA
CREATE TABLE IF NOT EXISTS ai_user_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    ip_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expiry_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '6 months'),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    deactivation_reason TEXT,
    UNIQUE(user_id)
);

-- Index pour accélérer les recherches par user_id
CREATE INDEX IF NOT EXISTS idx_ai_user_consents_user_id ON ai_user_consents(user_id);

-- Index pour accélérer les recherches par date d'expiration
CREATE INDEX IF NOT EXISTS idx_ai_user_consents_expiry_date ON ai_user_consents(expiry_date);

-- Index pour accélérer les recherches par statut d'activité
CREATE INDEX IF NOT EXISTS idx_ai_user_consents_is_active ON ai_user_consents(is_active);

-- Politique RLS pour la table ai_user_consents
ALTER TABLE ai_user_consents ENABLE ROW LEVEL SECURITY;

-- Politique pour les utilisateurs normaux (peuvent voir et modifier uniquement leurs propres consentements)
CREATE POLICY user_ai_consents_policy ON ai_user_consents
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Politique pour les administrateurs (peuvent voir tous les consentements)
CREATE POLICY admin_ai_consents_policy ON ai_user_consents
    USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE users.id = auth.uid()
            AND users.role IN ('jobadm', 'jobmodo')
        )
    );

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE ai_user_consents IS 'Stocke les consentements des utilisateurs pour l''utilisation de l''IA';
COMMENT ON COLUMN ai_user_consents.id IS 'Identifiant unique du consentement';
COMMENT ON COLUMN ai_user_consents.user_id IS 'Identifiant de l''utilisateur qui a donné son consentement';
COMMENT ON COLUMN ai_user_consents.first_name IS 'Prénom fourni par l''utilisateur lors du consentement';
COMMENT ON COLUMN ai_user_consents.last_name IS 'Nom fourni par l''utilisateur lors du consentement';
COMMENT ON COLUMN ai_user_consents.ip_address IS 'Adresse IP de l''utilisateur lors du consentement';
COMMENT ON COLUMN ai_user_consents.created_at IS 'Date et heure de création du consentement';
COMMENT ON COLUMN ai_user_consents.updated_at IS 'Date et heure de la dernière mise à jour du consentement';
COMMENT ON COLUMN ai_user_consents.expiry_date IS 'Date et heure d''expiration du consentement (6 mois après création ou mise à jour)';
COMMENT ON COLUMN ai_user_consents.is_active IS 'Indique si le consentement est actif ou non';
COMMENT ON COLUMN ai_user_consents.deactivated_at IS 'Date et heure de désactivation du consentement';
COMMENT ON COLUMN ai_user_consents.deactivation_reason IS 'Raison de la désactivation du consentement';








-- 1. Activer RLS sur openrouter_api_usage et openrouter_model_pricing
ALTER TABLE public.openrouter_api_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.openrouter_model_pricing ENABLE ROW LEVEL SECURITY;

-- Politiques pour openrouter_api_usage
CREATE POLICY "Les administrateurs peuvent voir toutes les utilisations de l'API"
ON public.openrouter_api_usage FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

CREATE POLICY "Les utilisateurs peuvent voir leur propre utilisation de l'API"
ON public.openrouter_api_usage FOR SELECT
USING (user_id = auth.uid());

-- Politiques pour openrouter_model_pricing
CREATE POLICY "Tout le monde peut voir les tarifs des modèles"
ON public.openrouter_model_pricing FOR SELECT
USING (true);

CREATE POLICY "Seuls les administrateurs peuvent modifier les tarifs des modèles"
ON public.openrouter_model_pricing FOR ALL
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

-- 2. Activer RLS sur content_moderation_logs
ALTER TABLE public.content_moderation_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Les administrateurs peuvent voir tous les logs de modération"
ON public.content_moderation_logs FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Les utilisateurs peuvent voir leurs propres logs de modération"
ON public.content_moderation_logs FOR SELECT
USING (user_id = auth.uid());

-- 3. Activer RLS sur entreprise_verification_documents
ALTER TABLE public.entreprise_verification_documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Les utilisateurs peuvent voir leurs propres documents"
ON public.entreprise_verification_documents FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Les administrateurs peuvent voir tous les documents"
ON public.entreprise_verification_documents FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

CREATE POLICY "Les utilisateurs peuvent ajouter leurs propres documents"
ON public.entreprise_verification_documents FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Les administrateurs peuvent modifier tous les documents"
ON public.entreprise_verification_documents FOR UPDATE
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- 4. Activer RLS sur promo_codes et promo_code_usage
ALTER TABLE public.promo_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.promo_code_usage ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Seuls les administrateurs peuvent gérer les codes promo"
ON public.promo_codes FOR ALL
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

CREATE POLICY "Les utilisateurs peuvent voir leurs propres utilisations de codes promo"
ON public.promo_code_usage FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Les administrateurs peuvent voir toutes les utilisations de codes promo"
ON public.promo_code_usage FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

-- 5. Activer RLS sur invoices_company_settings
ALTER TABLE public.invoices_company_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Les utilisateurs peuvent voir leurs propres paramètres d'entreprise"
ON public.invoices_company_settings FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent modifier leurs propres paramètres d'entreprise"
ON public.invoices_company_settings FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent créer leurs propres paramètres d'entreprise"
ON public.invoices_company_settings FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres paramètres d'entreprise"
ON public.invoices_company_settings FOR DELETE
USING (user_id = auth.uid());

-- 6. Améliorer la sécurité de email_queue
CREATE POLICY "Les utilisateurs normaux ne peuvent pas accéder à la file d'attente d'emails"
ON public.email_queue FOR ALL
USING (false);

-- 7. Compléter les politiques pour user_messages et user_messages_conversations
CREATE POLICY "Les utilisateurs peuvent insérer des messages dans leurs conversations"
ON public.user_messages FOR INSERT
WITH CHECK (sender_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.user_messages_conversations
    WHERE id = conversation_id AND (user1_id = auth.uid() OR user2_id = auth.uid())
  )
);

CREATE POLICY "Les utilisateurs peuvent mettre à jour leurs propres messages"
ON public.user_messages FOR UPDATE
USING (sender_id = auth.uid() OR receiver_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres messages"
ON public.user_messages FOR DELETE
USING (sender_id = auth.uid());

-- 8. Restreindre l'accès aux security_logs
DROP POLICY IF EXISTS "Les logs peuvent être lus par l'application" ON public.security_logs;
CREATE POLICY "Les logs peuvent être lus par les administrateurs"
ON public.security_logs FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

-- 9. Améliorer les politiques pour user_login_history
CREATE POLICY "Les administrateurs peuvent voir tous les historiques de connexion"
ON public.user_login_history FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role IN ('jobpadm', 'jobmodo')));

-- 10. Améliorer les politiques pour user_jobi et user_jobi_historique
CREATE POLICY "Les administrateurs peuvent voir tous les jobis"
ON public.user_jobi FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));

CREATE POLICY "Les administrateurs peuvent voir tout l'historique des jobis"
ON public.user_jobi_historique FOR SELECT
USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND role = 'jobpadm'));






-- Tables pour l'éditeur de cartes de visite et flyers

-- Table pour stocker les templates de cartes de visite et flyers
CREATE TABLE IF NOT EXISTS public.card_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('business_card', 'flyer', 'business_card_landscape', 'flyer_landscape')),
    template_data JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    is_ai_generated BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    order_index INTEGER NOT NULL DEFAULT 0
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_card_templates_user_id ON public.card_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_card_templates_type ON public.card_templates(type);
CREATE INDEX IF NOT EXISTS idx_card_templates_is_public ON public.card_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_card_templates_order_index ON public.card_templates(order_index);

-- Table pour stocker les éléments des templates (textes, images, formes, etc.)
CREATE TABLE IF NOT EXISTS public.card_template_elements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID REFERENCES public.card_templates(id) ON DELETE CASCADE,
    element_type TEXT NOT NULL CHECK (element_type IN ('text', 'image', 'shape', 'qrcode')),
    element_data JSONB NOT NULL,
    z_index INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_card_template_elements_template_id ON public.card_template_elements(template_id);

-- Table pour stocker les exports des utilisateurs
CREATE TABLE IF NOT EXISTS public.card_exports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES public.card_templates(id) ON DELETE SET NULL,
    export_type TEXT NOT NULL CHECK (export_type IN ('pdf', 'png', 'jpg')),
    file_path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_card_exports_user_id ON public.card_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_card_exports_template_id ON public.card_exports(template_id);

-- Table pour stocker les statistiques d'utilisation de l'IA pour la génération de templates
CREATE TABLE IF NOT EXISTS public.card_ai_generation_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES public.card_templates(id) ON DELETE SET NULL,
    prompt TEXT,
    cost INTEGER NOT NULL DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_card_ai_generation_stats_user_id ON public.card_ai_generation_stats(user_id);

-- Donner les permissions nécessaires
GRANT ALL PRIVILEGES ON TABLE public.card_templates TO service_role;
GRANT ALL PRIVILEGES ON TABLE public.card_template_elements TO service_role;
GRANT ALL PRIVILEGES ON TABLE public.card_exports TO service_role;
GRANT ALL PRIVILEGES ON TABLE public.card_ai_generation_stats TO service_role;

-- ========================================
-- INDEX POUR LE SYSTÈME DE GESTION DES UTILISATEURS ADMIN/MODÉRATEUR
-- ========================================

-- Index pour la table users (optimisation des requêtes admin)
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_profil_actif ON users(profil_actif);
CREATE INDEX IF NOT EXISTS idx_users_email_verifier ON users(email_verifier);
CREATE INDEX IF NOT EXISTS idx_users_profil_verifier ON users(profil_verifier);
CREATE INDEX IF NOT EXISTS idx_users_identite_verifier ON users(identite_verifier);
CREATE INDEX IF NOT EXISTS idx_users_entreprise_verifier ON users(entreprise_verifier);
CREATE INDEX IF NOT EXISTS idx_users_assurance_verifier ON users(assurance_verifier);
CREATE INDEX IF NOT EXISTS idx_users_is_online ON users(is_online);
CREATE INDEX IF NOT EXISTS idx_users_last_activity ON users(last_activity);
CREATE INDEX IF NOT EXISTS idx_users_date_inscription ON users(date_inscription);
CREATE INDEX IF NOT EXISTS idx_users_suspended_until ON users(suspended_until);
CREATE INDEX IF NOT EXISTS idx_users_is_anonymized ON users(is_anonymized);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON users(updated_at);

-- Index composites pour les filtres fréquents
CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, profil_actif);
CREATE INDEX IF NOT EXISTS idx_users_type_status ON users(user_type, profil_actif);
CREATE INDEX IF NOT EXISTS idx_users_role_type ON users(role, user_type);
CREATE INDEX IF NOT EXISTS idx_users_active_online ON users(profil_actif, is_online);

-- Index pour la table user_profil (optimisation des recherches)
CREATE INDEX IF NOT EXISTS idx_user_profil_user_id ON user_profil(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profil_ville ON user_profil(ville);
CREATE INDEX IF NOT EXISTS idx_user_profil_mode_vacance ON user_profil(mode_vacance);
CREATE INDEX IF NOT EXISTS idx_user_profil_profil_visible ON user_profil(profil_visible);
CREATE INDEX IF NOT EXISTS idx_user_profil_seo_indexable ON user_profil(seo_indexable);
CREATE INDEX IF NOT EXISTS idx_user_profil_created_at ON user_profil(created_at);
CREATE INDEX IF NOT EXISTS idx_user_profil_updated_at ON user_profil(updated_at);

-- Index pour la table user_abo (gestion des abonnements)
CREATE INDEX IF NOT EXISTS idx_user_abo_user_id ON user_abo(user_id);
CREATE INDEX IF NOT EXISTS idx_user_abo_type_abonnement ON user_abo(type_abonnement);
CREATE INDEX IF NOT EXISTS idx_user_abo_statut ON user_abo(statut);
CREATE INDEX IF NOT EXISTS idx_user_abo_date_debut ON user_abo(date_debut);
CREATE INDEX IF NOT EXISTS idx_user_abo_date_fin ON user_abo(date_fin);
CREATE INDEX IF NOT EXISTS idx_user_abo_created_at ON user_abo(created_at);
CREATE INDEX IF NOT EXISTS idx_user_abo_updated_at ON user_abo(updated_at);

-- Index composites pour les abonnements
CREATE INDEX IF NOT EXISTS idx_user_abo_user_statut ON user_abo(user_id, statut);
CREATE INDEX IF NOT EXISTS idx_user_abo_type_statut ON user_abo(type_abonnement, statut);
CREATE INDEX IF NOT EXISTS idx_user_abo_actif_dates ON user_abo(statut, date_debut, date_fin) WHERE statut = 'actif';

-- Index pour la table user_jobi (gestion des crédits)
CREATE INDEX IF NOT EXISTS idx_user_jobi_user_id ON user_jobi(user_id);
CREATE INDEX IF NOT EXISTS idx_user_jobi_montant ON user_jobi(montant);
CREATE INDEX IF NOT EXISTS idx_user_jobi_created_at ON user_jobi(created_at);
CREATE INDEX IF NOT EXISTS idx_user_jobi_updated_at ON user_jobi(updated_at);

-- Index pour la table user_jobi_historique
CREATE INDEX IF NOT EXISTS idx_user_jobi_historique_user_id ON user_jobi_historique(user_id);
CREATE INDEX IF NOT EXISTS idx_user_jobi_historique_created_at ON user_jobi_historique(created_at);
CREATE INDEX IF NOT EXISTS idx_user_jobi_historique_montant ON user_jobi_historique(montant);

-- Index composite pour l'historique Jobi par utilisateur et date
CREATE INDEX IF NOT EXISTS idx_user_jobi_historique_user_date ON user_jobi_historique(user_id, created_at DESC);

-- Index pour la table user_ai_credits (gestion des crédits IA)
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_user_id ON user_ai_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_credits ON user_ai_credits(credits);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_created_at ON user_ai_credits(created_at);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_updated_at ON user_ai_credits(updated_at);

-- Index pour la table user_ai_credits_historique
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_user_id ON user_ai_credits_historique(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_created_at ON user_ai_credits_historique(created_at);
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_operation_type ON user_ai_credits_historique(operation_type);

-- Index composite pour l'historique des crédits IA
CREATE INDEX IF NOT EXISTS idx_user_ai_credits_historique_user_date ON user_ai_credits_historique(user_id, created_at DESC);

-- Index pour la table user_missions (gestion des missions)
CREATE INDEX IF NOT EXISTS idx_user_missions_user_id ON user_missions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_missions_statut ON user_missions(statut);
CREATE INDEX IF NOT EXISTS idx_user_missions_budget ON user_missions(budget);
CREATE INDEX IF NOT EXISTS idx_user_missions_created_at ON user_missions(created_at);
CREATE INDEX IF NOT EXISTS idx_user_missions_updated_at ON user_missions(updated_at);

-- Index composites pour les missions
CREATE INDEX IF NOT EXISTS idx_user_missions_user_statut ON user_missions(user_id, statut);
CREATE INDEX IF NOT EXISTS idx_user_missions_user_date ON user_missions(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_missions_statut_date ON user_missions(statut, created_at DESC);

-- Index pour la table user_reviews (gestion des avis)
CREATE INDEX IF NOT EXISTS idx_user_reviews_user_id ON user_reviews(author_id);
CREATE INDEX IF NOT EXISTS idx_user_reviews_target_user_id ON user_reviews(target_user_id);
CREATE INDEX IF NOT EXISTS idx_user_reviews_note ON user_reviews(note);
CREATE INDEX IF NOT EXISTS idx_user_reviews_created_at ON user_reviews(created_at);

-- Index composites pour les avis
CREATE INDEX IF NOT EXISTS idx_user_reviews_target_date ON user_reviews(target_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_reviews_target_note ON user_reviews(target_user_id, note);

-- Index pour la table user_badges (gestion des badges)
CREATE INDEX IF NOT EXISTS idx_user_badges_user_id ON user_badges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_badges_date_obtention ON user_badges(date_obtention);

-- Index composite pour les badges par utilisateur
CREATE INDEX IF NOT EXISTS idx_user_badges_user_date ON user_badges(user_id, date_obtention DESC);

-- Index pour la table user_login_history (historique de connexion)
CREATE INDEX IF NOT EXISTS idx_user_login_history_user_id ON user_login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_login_history_login_date ON user_login_history(login_date);
CREATE INDEX IF NOT EXISTS idx_user_login_history_ip_address ON user_login_history(ip_address);

-- Index composites pour l'historique de connexion
CREATE INDEX IF NOT EXISTS idx_user_login_history_user_date ON user_login_history(user_id, login_date DESC);
CREATE INDEX IF NOT EXISTS idx_user_login_history_date_ip ON user_login_history(login_date DESC, ip_address);

-- Index pour la table user_notifications (gestion des notifications)
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_type ON user_notifications(type);
CREATE INDEX IF NOT EXISTS idx_user_notifications_lu ON user_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON user_notifications(created_at);

-- Index composites pour les notifications
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_lu ON user_notifications(user_id, is_read);
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_date ON user_notifications(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_type ON user_notifications(user_id, type);

-- Index pour la table user_transac (gestion des transactions)
CREATE INDEX IF NOT EXISTS idx_user_transac_user_id ON user_transac(user_id);
CREATE INDEX IF NOT EXISTS idx_user_transac_type ON user_transac(type);
CREATE INDEX IF NOT EXISTS idx_user_transac_montant ON user_transac(montant);
CREATE INDEX IF NOT EXISTS idx_user_transac_created_at ON user_transac(created_at);

-- Index composites pour les transactions
CREATE INDEX IF NOT EXISTS idx_user_transac_user_date ON user_transac(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_transac_user_type ON user_transac(user_id, type);
CREATE INDEX IF NOT EXISTS idx_user_transac_type_date ON user_transac(type, created_at DESC);

-- Index pour la table user_services (gestion des services)
CREATE INDEX IF NOT EXISTS idx_user_services_user_id ON user_services(user_id);
CREATE INDEX IF NOT EXISTS idx_user_services_created_at ON user_services(created_at);

-- Index composite pour les services par utilisateur
CREATE INDEX IF NOT EXISTS idx_user_services_user_date ON user_services(user_id, created_at DESC);

-- Index pour la table user_gallery (gestion des galeries)
CREATE INDEX IF NOT EXISTS idx_user_gallery_user_id ON user_gallery(user_id);
CREATE INDEX IF NOT EXISTS idx_user_gallery_created_at ON user_gallery(created_at);

-- Index composite pour les galeries par utilisateur
CREATE INDEX IF NOT EXISTS idx_user_gallery_user_date ON user_gallery(user_id, created_at DESC);

-- Index pour la table user_gallery_photos
CREATE INDEX IF NOT EXISTS idx_user_gallery_photos_gallery_id ON user_gallery_photos(gallery_id);
CREATE INDEX IF NOT EXISTS idx_user_gallery_photos_created_at ON user_gallery_photos(created_at);

-- Index pour la table user_featured_photos
CREATE INDEX IF NOT EXISTS idx_user_featured_photos_user_id ON user_featured_photos(user_id);
CREATE INDEX IF NOT EXISTS idx_user_featured_photos_created_at ON user_featured_photos(created_at);

-- Index composite pour les photos mises en avant
CREATE INDEX IF NOT EXISTS idx_user_featured_photos_user_date ON user_featured_photos(user_id, created_at DESC);

-- Commentaires pour documenter les index
COMMENT ON INDEX idx_users_role IS 'Index pour filtrer les utilisateurs par rôle';
COMMENT ON INDEX idx_users_role_status IS 'Index composite pour filtrer par rôle et statut actif';
COMMENT ON INDEX idx_user_profil_user_id IS 'Index pour joindre les profils aux utilisateurs';
COMMENT ON INDEX idx_user_abo_user_statut IS 'Index pour les abonnements actifs par utilisateur';
COMMENT ON INDEX idx_user_jobi_historique_user_date IS 'Index pour historique Jobi par utilisateur et date';
COMMENT ON INDEX idx_user_missions_user_date IS 'Index pour les missions par utilisateur et date';
COMMENT ON INDEX idx_user_reviews_target_date IS 'Index pour les avis reçus par utilisateur et date';
COMMENT ON INDEX idx_user_login_history_user_date IS 'Index pour historique de connexion par utilisateur';
COMMENT ON INDEX idx_user_notifications_user_date IS 'Index pour les notifications par utilisateur et date';

-- Analyse des tables pour mettre à jour les statistiques
ANALYZE users;
ANALYZE user_profil;
ANALYZE user_abo;
ANALYZE user_jobi;
ANALYZE user_jobi_historique;
ANALYZE user_ai_credits;
ANALYZE user_ai_credits_historique;
ANALYZE user_missions;
ANALYZE user_reviews;
ANALYZE user_badges;
ANALYZE user_login_history;
ANALYZE user_notifications;
ANALYZE user_transac;
ANALYZE user_services;
ANALYZE user_gallery;
ANALYZE user_gallery_photos;
ANALYZE user_featured_photos;