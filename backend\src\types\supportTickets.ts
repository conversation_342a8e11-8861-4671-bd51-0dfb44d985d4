import { User } from './index';

export type TicketStatus = 'nouveau' | 'en_attente' | 'en_cours' | 'resolu' | 'ferme' | 'reouvert';
export type TicketPriority = 'faible' | 'normale' | 'elevee' | 'urgente';
export type TicketCategory = 'technique' | 'facturation' | 'compte' | 'mission' | 'autre';

export interface SupportTicket {
  id: string;
  user_id: string;
  user?: User;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: TicketCategory;
  assigned_to?: string;
  assignee?: User;
  sla_due_at?: string;
  resolved_at?: string;
  last_response_at?: string;
  repondu: boolean;
  created_at: string;
  updated_at: string;
  comments?: SupportTicketComment[];
  attachments?: SupportTicketAttachment[];
  tags?: SupportTicketTag[];
  history?: SupportTicketHistory[];
}

export interface SupportTicketHistory {
  id: string;
  ticket_id: string;
  user_id?: string;
  user?: User;
  action: string;
  old_value?: string;
  new_value?: string;
  comment?: string;
  created_at: string;
}

export interface SupportTicketComment {
  id: string;
  ticket_id: string;
  user_id?: string;
  user?: User;
  message: string;
  is_internal: boolean;
  parent_id?: string;
  parent?: SupportTicketComment;
  created_at: string;
  updated_at: string;
  attachments?: SupportTicketAttachment[];
}

export interface SupportTicketAttachment {
  id: string;
  ticket_id: string;
  comment_id?: string;
  file_name: string;
  file_size: number;
  file_type: string;
  storage_path: string;
  created_at: string;
}

export interface SupportTicketTag {
  id: string;
  name: string;
  color: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface SupportResponseTemplate {
  id: string;
  title: string;
  content: string;
  category: string;
  is_internal: boolean;
  created_by?: string;
  creator?: User;
  created_at: string;
  updated_at: string;
}

// DTOs pour la création et la mise à jour
export interface CreateTicketDTO {
  title: string;
  description: string;
  priority: TicketPriority;
  category: TicketCategory;
  attachments?: File[];
}

export interface UpdateTicketDTO {
  title?: string;
  description?: string;
  status?: TicketStatus;
  priority?: TicketPriority;
  category?: TicketCategory;
  assigned_to?: string | null;
  sla_due_at?: string;
  tags?: SupportTicketTag[];
}

export interface CreateCommentDTO {
  message: string;
  is_internal?: boolean;
  parent_id?: string;
  attachments?: File[];
}

export interface UpdateCommentDTO {
  message?: string;
  is_internal?: boolean;
}

export interface CreateTagDTO {
  name: string;
  color: string;
  description?: string;
}

export interface CreateTemplateDTO {
  title: string;
  content: string;
  category: string;
  is_internal?: boolean;
}

// Filtres pour la recherche
export interface TicketFilters {
  status?: TicketStatus;
  priority?: TicketPriority;
  category?: TicketCategory;
  assigned_to?: string;
  user_id?: string;
  search?: string;
  tag_ids?: string[];
  start_date?: string;
  end_date?: string;
  is_resolved?: boolean;
  page?: number;
  limit?: number;
  order?: string;
  direction?: 'asc' | 'desc';
  repondu?: boolean;
  status_not_in?: TicketStatus[];
}

// Statistiques
export interface TicketStats {
  total: number;
  by_status: Record<TicketStatus, number>;
  by_priority: Record<TicketPriority, number>;
  by_category: Record<TicketCategory, number>;
  by_date: Record<string, number>;
  open_tickets: number;
  resolved_tickets: number;
  average_resolution_time: number;
  average_first_response_time: number;
  average_satisfaction: number;
  sla_compliance_rate: number;
  tickets_by_agent: Record<string, number>;
  resolution_by_category: Record<string, number>;
  peak_hours: Record<string, number>;
  tags_distribution: Record<string, number>;
} 