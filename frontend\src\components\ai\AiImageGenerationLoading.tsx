import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Brain, ImageIcon, Lightbulb, Wand2, ScanSearch } from 'lucide-react';
import '../../styles/aiAnimation.css'; // Import des styles d'animation

interface AiImageGenerationLoadingProps {
  purpose?: string;
}

// Étapes de génération de l'image par l'IA
const generationSteps = [
  { id: 'optimization', label: 'Optimisation du prompt...', icon: ScanSearch, duration: 1000 },
  { id: 'ideas', label: 'Recherche d\'idées...', icon: Lightbulb, duration: 900 },
  { id: 'analyzing', label: 'Analyse du contexte...', icon: Brain, duration: 800 },
  { id: 'generating', label: 'Génération en cours...', icon: Wand2, duration: 1200 },
  { id: 'finalizing', label: 'Finalisation...', icon: ImageIcon, duration: 500 }
];

const AiImageGenerationLoading: React.FC<AiImageGenerationLoadingProps> = ({ purpose }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [showCompletion, setShowCompletion] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    let currentIndex = 0;

    const advanceStep = () => {
      if (currentIndex < generationSteps.length - 1) {
        currentIndex++;
        setCurrentStepIndex(currentIndex);
        timer = setTimeout(advanceStep, generationSteps[currentIndex].duration);
      } else {
        // Lorsque toutes les étapes sont terminées, afficher le message de finalisation
        setShowCompletion(true);
      }
    };

    // Démarrer la séquence d'animation
    timer = setTimeout(advanceStep, generationSteps[0].duration);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div className="flex flex-col items-center justify-center p-4 sm:p-6 text-center max-w-md mx-auto">
      {/* Animation principale en haut */}
      <div className="relative w-20 h-20 sm:w-32 sm:h-32 mb-6 sm:mb-8">
        {/* Cercles animés */}
        <motion.div 
          className="absolute inset-0 bg-[#FFE4BA] rounded-full opacity-20"
          style={{ width: '100%', height: '100%' }}
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.3, 0.2]
          }}
          transition={{ 
            duration: 2.5, 
            repeat: Infinity,
            ease: "easeInOut" 
          }}
        />
        <motion.div 
          className="absolute inset-1 sm:inset-2 bg-[#FF965E] rounded-full opacity-20"
          style={{ width: 'calc(100% - 0.5rem)', height: 'calc(100% - 0.5rem)' }}
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            delay: 0.5,
            ease: "easeInOut" 
          }}
        />
        
        {/* Icône centrale */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div 
            animate={{ 
              rotate: 360,
              scale: [1, 1.05, 1]
            }}
            transition={{ 
              rotate: { duration: 8, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
            className="bg-[#FF6B2C] rounded-full p-3 sm:p-5 shadow-lg"
          >
            <Sparkles className="h-7 w-7 sm:h-10 sm:w-10 text-white" />
          </motion.div>
        </div>
        
        {/* Particules animées */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#FF6B2C] rounded-full"
            initial={{ 
              x: 0, 
              y: 0, 
              opacity: 0 
            }}
            animate={{ 
              x: Math.sin(i * 60 * (Math.PI / 180)) * (window.innerWidth < 640 ? 28 : 50),
              y: Math.cos(i * 60 * (Math.PI / 180)) * (window.innerWidth < 640 ? 28 : 50),
              opacity: [0, 1, 0]
            }}
            transition={{ 
              duration: 1.5,
              delay: i * 0.2,
              repeat: Infinity,
              repeatDelay: 1
            }}
          />
        ))}
      </div>

      {/* Titre avec dégradé */}
      <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] bg-clip-text text-transparent">
        Création d'image en cours
      </h2>
      
      {/* Sous-titre */}
      <p className="text-gray-600 mb-6">
        {purpose ? `Génération pour ${purpose}` : 'Intelligence artificielle au travail'}
      </p>

      {/* Liste des étapes avec animation */}
      <div className="w-full max-w-sm">
        {generationSteps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;
          
          return (
            <div key={step.id} className="flex items-center mb-3">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center mr-3 transition-all duration-300
                ${isCompleted 
                  ? 'bg-green-100 text-green-600' 
                  : isActive 
                    ? 'bg-[#FFF8F3] text-[#FF6B2C]' 
                    : 'bg-gray-100 text-gray-400'
                }
              `}>
                {isActive ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                  >
                    <Icon className="h-4 w-4" />
                  </motion.div>
                ) : (
                  <Icon className="h-4 w-4" />
                )}
              </div>
              <div className="flex-1">
                <div className={`
                  text-sm font-medium
                  ${isCompleted 
                    ? 'text-green-600' 
                    : isActive 
                      ? 'text-[#FF6B2C]' 
                      : 'text-gray-400'
                  }
                `}>
                  {step.label}
                </div>
                {isActive && (
                  <div className="w-full h-1 bg-gray-100 rounded-full mt-1 overflow-hidden">
                    <motion.div
                      className="h-full bg-[#FF6B2C]"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ 
                        duration: step.duration / 1000,
                        ease: "easeInOut"
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Message de fin */}
      <AnimatePresence>
        {showCompletion && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="mt-6 p-3 bg-[#FFF8F3] border border-[#FFE4BA] rounded-lg text-[#FF6B2C] text-sm"
          >
            <p className="flex items-center">
              <Sparkles className="h-4 w-4 mr-2" />
              Votre image est prête !
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AiImageGenerationLoading; 