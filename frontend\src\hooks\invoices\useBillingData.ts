import { useState, useEffect, useRef } from 'react';
import { invoiceService, Document } from '../../services/invoiceService';
import logger from '@/utils/logger';

// Types pour les données de facturation
interface BillingData {
  documents: Document[];
  clients: any[];
  receivedQuotes: Document[];
  receivedInvoices: Document[];
  receivedCreditNotes: Document[];
}

// Cache global pour éviter les appels multiples
let globalBillingData: BillingData | null = null;
let globalLoading = false;
let globalError: string | null = null;
let fetchPromise: Promise<void> | null = null;
const subscribers = new Set<() => void>();

// Hook personnalisé pour gérer les données de facturation avec cache
export const useBillingData = () => {
  const [data, setData] = useState<BillingData | null>(globalBillingData);
  const [loading, setLoading] = useState(globalLoading);
  const [error, setError] = useState<string | null>(globalError);
  const subscriberRef = useRef<() => void>(() => {});

  useEffect(() => {
    // Créer une fonction de mise à jour pour ce composant
    const updateState = () => {
      setData(globalBillingData);
      setLoading(globalLoading);
      setError(globalError);
    };

    // S'abonner aux changements
    subscriberRef.current = updateState;
    subscribers.add(updateState);

    // Si les données ne sont pas encore chargées et qu'aucun chargement n'est en cours
    if (!globalBillingData && !globalLoading && !fetchPromise) {
      fetchBillingData();
    }

    // Cleanup
    return () => {
      if (subscriberRef.current) {
        subscribers.delete(subscriberRef.current);
      }
    };
  }, []);

  const notifySubscribers = () => {
    subscribers.forEach(callback => callback());
  };

  const fetchBillingData = async () => {
    // Si un fetch est déjà en cours, attendre qu'il se termine
    if (fetchPromise) {
      return fetchPromise;
    }

    // Si les données sont déjà chargées, ne pas refaire l'appel
    if (globalBillingData) {
      return Promise.resolve();
    }

    globalLoading = true;
    globalError = null;
    notifySubscribers();

    fetchPromise = (async () => {
      try {
        logger.info('Début du chargement des données de facturation...');

        // Charger toutes les données en parallèle pour optimiser les performances
        const [
          documents,
          clients,
          receivedQuotes,
          receivedInvoices,
          receivedCreditNotes
        ] = await Promise.all([
          invoiceService.getDocuments('all'),
          invoiceService.getClients(),
          invoiceService.getReceivedQuotes(),
          invoiceService.getReceivedInvoices(),
          invoiceService.getReceivedCreditNotes()
        ]);

        globalBillingData = {
          documents,
          clients,
          receivedQuotes,
          receivedInvoices,
          receivedCreditNotes
        };

        globalError = null;
        logger.info('Données de facturation récupérées avec succès');
      } catch (err: any) {
        globalError = err.message || 'Erreur lors de la récupération des données de facturation';
        logger.error('Erreur lors de la récupération des données de facturation:', err);
      } finally {
        globalLoading = false;
        fetchPromise = null;
        notifySubscribers();
      }
    })();

    return fetchPromise;
  };

  const updateDocument = (updatedDocument: Document) => {
    if (globalBillingData) {
      // Créer un nouvel objet pour déclencher les re-renders avec une UX en temps réel
      globalBillingData = {
        ...globalBillingData,
        documents: globalBillingData.documents.map(doc =>
          doc.id === updatedDocument.id ? { ...doc, ...updatedDocument } : doc
        )
      };

      // Notifier immédiatement tous les abonnés pour une mise à jour en temps réel
      notifySubscribers();

      logger.info('Document mis à jour dans le cache global:', {
        documentId: updatedDocument.id,
        newStatus: updatedDocument.statut
      });
    }
  };

  const addDocument = (newDocument: Document) => {
    if (globalBillingData) {
      logger.info('Ajout d\'un nouveau document:', newDocument.id, newDocument.type);
      // Créer un nouvel objet pour déclencher les re-renders avec une UX en temps réel
      globalBillingData = {
        ...globalBillingData,
        documents: [newDocument, ...globalBillingData.documents]
      };

      // Notifier immédiatement tous les abonnés pour une mise à jour en temps réel
      notifySubscribers();
    }
  };

  const removeDocument = (documentId: string) => {
    if (globalBillingData) {
      // Créer un nouvel objet pour déclencher les re-renders avec une UX en temps réel
      globalBillingData = {
        ...globalBillingData,
        documents: globalBillingData.documents.filter(doc => doc.id !== documentId)
      };

      // Notifier immédiatement tous les abonnés pour une mise à jour en temps réel
      notifySubscribers();

      logger.info('Document supprimé du cache global:', { documentId });
    }
  };

  const updateClient = (updatedClient: any) => {
    if (globalBillingData) {
      // Créer un nouvel objet pour déclencher les re-renders
      globalBillingData = {
        ...globalBillingData,
        clients: globalBillingData.clients.map(client =>
          client.id === updatedClient.id ? updatedClient : client
        )
      };
      notifySubscribers();
    }
  };

  const addClient = (newClient: any) => {
    if (globalBillingData) {
      // Créer un nouvel objet pour déclencher les re-renders
      globalBillingData = {
        ...globalBillingData,
        clients: [...globalBillingData.clients, newClient]
      };
      notifySubscribers();
    }
  };

  const removeClient = (clientId: string) => {
    if (globalBillingData) {
      // Créer un nouvel objet pour déclencher les re-renders
      globalBillingData = {
        ...globalBillingData,
        clients: globalBillingData.clients.filter(client => client.id !== clientId)
      };
      notifySubscribers();
    }
  };

  const refreshData = async () => {
    // Éviter les boucles en vérifiant si un refresh est déjà en cours
    if (globalLoading || fetchPromise) {
      logger.info('Refresh déjà en cours, ignoré');
      return;
    }

    logger.info('Début du refresh des données de facturation...');

    // Réinitialiser le cache pour forcer un nouveau chargement
    globalBillingData = null;
    globalLoading = false;
    globalError = null;
    fetchPromise = null;

    await fetchBillingData();

    logger.info('Refresh des données de facturation terminé');
  };

  return {
    data,
    loading,
    error,
    updateDocument,
    addDocument,
    removeDocument,
    updateClient,
    addClient,
    removeClient,
    refreshData
  };
};

export default useBillingData;
