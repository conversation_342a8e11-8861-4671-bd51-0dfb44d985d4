import React, { useState, useEffect, useRef } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import { missionsApi } from './missionsApi';
import { notify } from '../../../components/Notification';
import MissionCard, { MissionCardRef } from './MissionCard';
import CommentsDialog from './CommentsDialog';
import ProposalModal from './ProposalModal';
import { useAuth } from '../../../contexts/AuthContext';
import { logger } from '@/utils/logger';

const LoadingContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '50vh',
});

const SingleMissionPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const [mission, setMission] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showComments, setShowComments] = useState(false);
  const [showProposalModal, setShowProposalModal] = useState(false);
  const [showProposalDetails, setShowProposalDetails] = useState(false);
  const { user } = useAuth();
  const [isOwner, setIsOwner] = useState(false);
  const missionCardRef = useRef<MissionCardRef>(null);

  useEffect(() => {
    const fetchMission = async () => {
      try {
        if (!id) return;
        const missionData = await missionsApi.getMissionDetails(id);
        setMission(missionData);
        
        // Vérifier si l'utilisateur connecté est le propriétaire de la mission
        if (user && missionData.user_id === user.id) {
          setIsOwner(true);
        }

        // Vérifier si on doit ouvrir les commentaires
        const openComments = searchParams.get('openComments') === 'true';
        if (openComments) {
          setShowComments(true);
        }

        // Vérifier si on doit ouvrir la modal d'offre
        const openOffer = searchParams.get('openOffer') === 'true';
        if (openOffer && !isOwner) {
          // Vérifie si l'utilisateur a déjà fait une offre
          try {
            const userProposal = await missionsApi.getUserProposalForMission(missionData.id);
            if (userProposal) {
              // Si l'utilisateur a déjà fait une offre, afficher les détails de l'offre
              setShowProposalDetails(true);
              // Mettre à jour le missionCardRef une fois qu'il est disponible
              setTimeout(() => {
                if (missionCardRef.current) {
                  missionCardRef.current.updateUserProposal(userProposal);
                }
              }, 100);
            } else {
              // Si l'utilisateur n'a pas encore fait d'offre, ouvrir le modal de proposition
              setShowProposalModal(true);
            }
          } catch (error) {
            logger.info("Erreur lors de la vérification de l'offre:", error);
            // En cas d'erreur, ouvrir quand même le modal de proposition
            setShowProposalModal(true);
          }
        }
      } catch (error) {
        notify('Erreur lors de la récupération de la mission', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchMission();
  }, [id, searchParams, user]);

  const handleMissionUpdate = async (updatedMission: any) => {
    setMission(updatedMission);
  };

  const handleMakeProposal = (mission: any) => {
    setShowProposalModal(true);
  };

  // Fonction pour ouvrir les détails de l'offre
  const handleOpenProposalDetails = () => {
    if (missionCardRef.current) {
      missionCardRef.current.showProposalDetails();
    }
  };

  useEffect(() => {
    // Si les détails de l'offre doivent être affichés et que la référence est disponible
    if (showProposalDetails && missionCardRef.current) {
      handleOpenProposalDetails();
      // Réinitialiser l'état pour éviter de rouvrir la modal lors des re-renders
      setShowProposalDetails(false);
    }
  }, [showProposalDetails, missionCardRef.current]);

  if (loading) {
    return (
      <LoadingContainer>
        <CircularProgress sx={{ color: '#FF6B2C' }} />
      </LoadingContainer>
    );
  }

  if (!mission) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        Mission non trouvée
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 2 }}>
      <MissionCard
        ref={missionCardRef}
        mission={mission}
        onUpdate={handleMissionUpdate}
        showStatus={true}
        onMakeProposal={handleMakeProposal}
        isOwner={isOwner}
      />
      
      {showComments && (
        <CommentsDialog
          open={showComments}
          onClose={() => setShowComments(false)}
          missionId={mission.id}
          mission={mission}
          onUpdate={handleMissionUpdate}
          isOwner={isOwner}
        />
      )}

      {mission && !isOwner && (
        <ProposalModal
          open={showProposalModal}
          onClose={() => setShowProposalModal(false)}
          mission={mission}
          onProposalSubmitted={(proposalData) => {
            setShowProposalModal(false);
            // Mettre à jour l'état userProposal dans MissionCard sans faire de requête API
            if (missionCardRef.current && proposalData) {
              missionCardRef.current.updateUserProposal(proposalData);
            }
          }}
        />
      )}
    </Box>
  );
};

export default SingleMissionPage; 