import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Lock, ArrowLeft } from 'lucide-react';
import TwoFactorSettings from '../../../components/profile/TwoFactorSettings';
import { Link } from 'react-router-dom';

const SecuritySettings: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-3 md:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-[#FFF8F3] rounded-lg">
            <Shield className="h-6 w-6 text-[#FF6B2C]" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">Sécurité du compte</h2>
        </div>
        <Link
          to="/dashboard/parametres"
          className="flex items-center space-x-1 text-[#FF6B2C] hover:text-[#FF7A35] transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Retour aux paramètres</span>
        </Link>
      </div>

      <div className="space-y-6">
        {/* Authentification à deux facteurs */}
        <TwoFactorSettings />

        {/* Autres paramètres de sécurité à ajouter ultérieurement */}
        <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-xl transition-all duration-300">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-[#FFF8F3] rounded-lg">
              <Lock className="h-5 w-5 text-[#FF6B2C]" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800">Activité récente</h3>
          </div>

          <p className="text-gray-600 mb-4">
            Consultez l'historique des connexions et des activités récentes sur votre compte dans la section "Historique" de votre profil.
          </p>

          <a
            href="/dashboard/historique-et-evaluations"
            className="inline-flex items-center px-4 py-2 bg-[#FF6B2C] text-white rounded-md hover:bg-[#FF7A35] transition-colors"
          >
            Voir l'historique
          </a>
        </div>
      </div>
    </motion.div>
  );
};

export default SecuritySettings;
