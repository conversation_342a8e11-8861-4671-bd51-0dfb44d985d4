.button-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 16px;
}

.gallery-button {
    margin: 8px;
}

.button-container button {
    margin-bottom: 8px;
}

.icon-custom-size-gallery {
    width: 16px!important;
    height: 16px!important;
    margin-bottom: 1px!important;
}

/* Galleries container pour les galleries en mode desktop */
.jp-galleries-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 20px;
}

/* Galleries container pour les galleries en mode desktop */
@media (min-width: 2000px) {
    .jp-galleries-container {
        flex-direction: row !important;
    }
}

/* Galleries item pour les galleries en mode desktop */
.jp-gallery-item {
    width: 100% !important;
    margin-bottom: 0 !important;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

@media (min-width: 640px) {
    .jp-gallery-item {
        flex-direction: row;
    }
}

.jp-gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Galleries item pour les galleries en mode desktop */
@media (min-width: 2000px) {
    .jp-gallery-item {
        flex-basis: calc(50% - 10px) !important;
        margin-right: 0 !important;
    }
}

/* Style spécifique pour l'image de la galerie */
.jp-gallery-image-container {
    width: 100%;
    height: 240px;
    position: relative;
}

@media (min-width: 640px) {
    .jp-gallery-image-container {
        width: 280px;
        min-width: 280px;
        height: 280px;
    }
}

.jp-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

@media (min-width: 640px) {
    .jp-gallery-item img {
        border-radius: 12px 0 0 12px;
    }
}

/* Style pour le contenu de la galerie */
.jp-gallery-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

/* Style pour le titre de la galerie */
.jp-gallery-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

/* Style pour le conteneur de titre et boutons */
.jp-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

/* Styles pour mobile */
@media (max-width: 640px) {
    .jp-gallery-header {
        flex-direction: column-reverse;
    }

    .jp-gallery-header h3 {
        width: 100%;
        margin-top: 0.75rem;
        margin-bottom: 0;
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
    }

    .jp-gallery-header .jp-gallery-actions {
        align-self: flex-end;
        margin-bottom: 0.5rem;
    }

    /* Style pour le message de galerie désactivée sur mobile */
    .jp-gallery-item .gallery-disabled-overlay {
        z-index: 10;
    }

    .jp-gallery-item .gallery-disabled-message {
        top: 120px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 100%;
        text-align: center;
    }

    .jp-gallery-item .gallery-disabled-message div {
        display: inline-block;
        margin: 0 auto;
    }
}

/* Style pour la description de la galerie */
.jp-gallery-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
    flex-grow: 1;
    min-height: 3rem;
}

/* Style pour le footer de la galerie */
.jp-gallery-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    margin-top: auto;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Ajout de styles responsives pour le footer sur différentes tailles d'écran */
@media (max-width: 640px) {
    .jp-gallery-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .jp-gallery-footer button {
        width: 100%;
        margin-top: 0.5rem;
    }
}

/* Assurons-nous que le bouton ne déborde pas sur très grands écrans */
@media (min-width: 2000px) {
    .jp-gallery-footer {
        flex-direction: row;
        align-items: center;
    }

    .jp-gallery-footer button {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

/* Style pour les boutons d'action */
.jp-gallery-actions {
    display: flex;
    gap: 0.5rem;
}

.jp-gallery-actions button {
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.jp-gallery-actions button:hover {
    background-color: #f3f4f6;
}