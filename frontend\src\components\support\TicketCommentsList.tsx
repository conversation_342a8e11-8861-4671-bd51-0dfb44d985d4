import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  TextField,
  Button,
  Paper,
  FormControl,
  FormControlLabel,
  Switch,
  CircularProgress,
  Chip,
  Alert,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Send as SendIcon,
  LockOutlined as LockIcon,
  InfoOutlined as InfoIcon,
  DeleteOutline as DeleteIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { motion, AnimatePresence } from 'framer-motion';
import useComments from '../../hooks/useTicketsComments';
import { CreateCommentDto } from '../../services/supportTicketService';
import { useAuth } from '../../contexts/AuthContext';
import { logger } from '../../utils/logger';
import { notify } from '../Notification';
import ResponseTemplateSelector from './ResponseTemplateSelector';
import AiGenerationSystem from '../ai/AiGenerationSystem';
import useAiConsent from '../../hooks/useAiConsent';
import { stripHtml } from '../../pages/dashboard/profil/miscUtils';

interface CommentsListProps {
  ticketId: string;
  readOnly?: boolean;
  showInternal?: boolean;
  onTicketUpdated?: (forceRefresh?: boolean) => void;
  ticketStatus?: string;
  ticketCategory?: string;
  ticketTitle?: string;
  ticketDescription?: string;
}

/**
 * Composant pour afficher et gérer les commentaires d'un ticket de support
 */
const CommentsList: React.FC<CommentsListProps> = ({
  ticketId,
  readOnly = false,
  showInternal = false,
  onTicketUpdated,
  ticketStatus = '',
  ticketCategory = '',
  ticketTitle = '',
  ticketDescription = ''
}) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { comments, loading, error, addComment, deleteComment } = useComments(ticketId);
  const [newMessage, setNewMessage] = useState('');
  const [isInternalComment, setIsInternalComment] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showReopenConfirmation, setShowReopenConfirmation] = useState(false);
  const [pendingComment, setPendingComment] = useState<string>('');
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deletingComment, setDeletingComment] = useState(false);
  const [isAiConfirmModalOpen, setIsAiConfirmModalOpen] = useState(false);

  // Hook pour le consentement IA
  const { hasConsent } = useAiConsent();

  // Déterminer si l'utilisateur est membre du staff (admin ou modérateur)
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';

  // Vérifier si le ticket est fermé
  const isTicketClosed = ticketStatus === 'ferme' || ticketStatus === 'resolu';

  // Filtrer les commentaires selon les permissions
  const filteredComments = comments.filter(comment =>
    // Si l'utilisateur n'est pas du staff et que le commentaire est interne, ne pas l'afficher
    !comment.is_internal || isStaff || showInternal
  );

  // Fonction pour appliquer un modèle de réponse
  const handleTemplateSelect = (content: string) => {
    setNewMessage(content);
  };

  // Écouter l'événement de transfert du message IA
  useEffect(() => {
    const handleTransferAiToComment = (event: CustomEvent) => {
      const { content } = event.detail;
      if (content) {
        setNewMessage(content);
      }
    };

    window.addEventListener('transfer-ai-to-comment', handleTransferAiToComment as EventListener);

    return () => {
      window.removeEventListener('transfer-ai-to-comment', handleTransferAiToComment as EventListener);
    };
  }, []);

  // Fonction pour ouvrir la modale de confirmation d'utilisation de l'IA
  const handleAiImprovement = () => {
    if (!hasConsent) {
      // Ouvrir la modale de consentement IA
      window.dispatchEvent(new CustomEvent('open-ai-consent-modal'));
    } else {
      setIsAiConfirmModalOpen(true);
    }
  };

  // Fonction pour soumettre un commentaire sans mettre à jour le statut du ticket
  // Le backend s'occupera de rouvrir le ticket si nécessaire
  const submitComment = async (message: string) => {
    try {
      setSubmitting(true);

      const commentData: CreateCommentDto = {
        ticket_id: ticketId,
        message: message.trim(),
        is_internal: isInternalComment,
      };

      await addComment(commentData);

      // Réinitialiser le formulaire
      setNewMessage('');
      setIsInternalComment(false);
      setPendingComment('');

      // Si le ticket était fermé, afficher une notification pour informer l'utilisateur
      // et demander un rechargement complet du ticket pour refléter le changement de statut
      if (isTicketClosed && !isStaff && !isInternalComment) {
        // Notifier le parent qu'un commentaire a été ajouté et que le ticket a été rouvert
        if (onTicketUpdated) {
          onTicketUpdated(true); // true = forcer le rechargement complet
        }
        notify('Votre commentaire a été ajouté et le ticket a été rouvert', 'success');
      } else if (onTicketUpdated) {
        // Mise à jour normale pour d'autres cas
        onTicketUpdated();
      }
    } catch (error) {
      logger.error('Erreur lors de l\'ajout d\'un commentaire:', error);

      if (isTicketClosed && !isStaff && !isInternalComment) {
        notify('Erreur lors de l\'ajout du commentaire. Le ticket n\'a pas pu être rouvert.', 'error');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !ticketId) return;

    // Si le ticket est fermé et que l'utilisateur n'est pas du staff, demander confirmation
    if (isTicketClosed && !isStaff && !isInternalComment) {
      setPendingComment(newMessage);
      setShowReopenConfirmation(true);
      return;
    }

    await submitComment(newMessage);
  };

  // Gérer la confirmation de réouverture
  const handleConfirmReopen = async () => {
    setShowReopenConfirmation(false);
    await submitComment(pendingComment);
  };

  const handleCancelReopen = () => {
    setShowReopenConfirmation(false);
    setPendingComment('');
  };

  const handleDeleteComment = (commentId: string) => {
    setCommentToDelete(commentId);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteComment = async () => {
    if (!commentToDelete) return;

    try {
      setDeletingComment(true);
      const success = await deleteComment(commentToDelete);

      if (success) {
        notify('Commentaire supprimé avec succès', 'success');
      } else {
        notify('Erreur lors de la suppression du commentaire', 'error');
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression du commentaire:', error);
      notify('Erreur lors de la suppression du commentaire', 'error');
    } finally {
      setDeletingComment(false);
      setShowDeleteConfirmation(false);
      setCommentToDelete(null);
    }
  };

  const cancelDeleteComment = () => {
    setShowDeleteConfirmation(false);
    setCommentToDelete(null);
  };

  // Formater la date au format français
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy à HH:mm', { locale: fr });
    } catch (error) {
      return dateString;
    }
  };

  // Variantes d'animation pour les commentaires
  const commentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.4,
        ease: "easeOut"
      }
    })
  };

  // Variantes d'animation pour le conteneur de commentaires
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  // Animation de chargement
  const loadingVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const loadingCircleVariants = {
    initial: { rotate: 0 },
    animate: {
      rotate: 360,
      transition: {
        repeat: Infinity,
        duration: 1.5,
        ease: "linear"
      }
    }
  };

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Typography variant="h6" component="h3" gutterBottom>
          Commentaires
        </Typography>
      </motion.div>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert severity="error" sx={{ mb: 2 }}>
              Une erreur est survenue lors du chargement des commentaires.
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Alerte informative si le ticket est fermé et que l'utilisateur n'est pas du staff */}
      {isTicketClosed && !isStaff && !readOnly && (
        <Alert
          severity="info"
          icon={<InfoIcon />}
          sx={{
            mb: 2,
            borderRadius: '8px',
            '& .MuiAlert-icon': {
              color: '#FF6B2C'
            }
          }}
        >
          Ce ticket est actuellement fermé. En ajoutant un commentaire, le ticket sera automatiquement rouvert.
        </Alert>
      )}

      <Divider sx={{ mb: 2 }} />

      {loading ? (
        <motion.div
          variants={loadingVariants}
          initial="initial"
          animate="animate"
        >
          <Box display="flex" justifyContent="center" alignItems="center" flexDirection="column" my={5} gap={2}>
            <motion.div variants={loadingCircleVariants} initial="initial" animate="animate">
              <Box sx={{ position: 'relative' }}>
                <CircularProgress
                  size={50}
                  thickness={4}
                  sx={{
                    color: '#FF6B2C',
                    opacity: 0.7
                  }}
                />
                <CircularProgress
                  size={50}
                  thickness={4}
                  sx={{
                    color: '#FF965E',
                    position: 'absolute',
                    left: 0,
                    animationDuration: '1.2s'
                  }}
                />
              </Box>
            </motion.div>
            <Typography color="text.secondary" sx={{ fontWeight: 500 }}>
              Chargement des commentaires...
            </Typography>
          </Box>
        </motion.div>
      ) : filteredComments.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Typography color="text.secondary" align="center" py={3}>
            Aucun commentaire pour le moment
          </Typography>
        </motion.div>
      ) : (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <List sx={{
            width: '100%',
            bgcolor: 'background.paper',
            mb: 3,
            '& .MuiListItem-root': {
              padding: 2,
            }
          }}>
            {filteredComments.map((comment, index) => {
              const isStaffComment = comment.user.role === 'jobpadm' || comment.user.role === 'jobmodo';
              const canDelete = isStaff; // Seul le staff peut supprimer les commentaires

              return (
                <motion.div
                  key={comment.id}
                  custom={index}
                  variants={commentVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <ListItem
                    alignItems="flex-start"
                    sx={{
                      bgcolor: comment.is_internal
                        ? 'rgba(255, 152, 0, 0.08)'
                        : isStaffComment
                          ? 'rgba(255, 107, 44, 0.05)'
                          : 'rgba(240, 240, 240, 0.3)',
                      border: comment.is_internal
                        ? `1px solid ${theme.palette.warning.light}`
                        : isStaffComment
                          ? `1px solid rgba(255, 107, 44, 0.2)`
                          : '1px solid rgba(0, 0, 0, 0.05)',
                      borderRadius: 2,
                      mb: 2,
                      ml: isStaffComment ? 4 : 0,
                      mr: isStaffComment ? 0 : 4,
                      position: 'relative',
                      display: 'flex',
                      flexDirection: 'row',
                      width: 'auto',
                      maxWidth: '100%',
                      '&::before': isStaffComment ? {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        bottom: 0,
                        width: '4px',
                        backgroundColor: '#FF6B2C',
                        borderTopLeftRadius: 8,
                        borderBottomLeftRadius: 8,
                      } : {},
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        alt={comment.user.email}
                        src={comment.user.avatar_url}
                        sx={{
                          bgcolor: comment.is_internal
                            ? theme.palette.warning.main
                            : isStaffComment
                              ? '#FF6B2C'
                              : theme.palette.primary.main,
                          border: isStaffComment ? '2px solid #FF965E' : 'none',
                          width: 36,
                          height: 36
                        }}
                      >
                        {(comment.user.role === 'jobpadm' || comment.user.role === 'jobmodo')
                          ? 'S'
                          : comment.user.email.charAt(0).toUpperCase()}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1} flexWrap="wrap" component="span">
                          <Typography
                            component="span"
                            variant="subtitle2"
                            sx={{
                              fontWeight: isStaffComment ? 600 : 400,
                              color: isStaffComment ? '#FF6B2C' : 'text.primary'
                            }}
                          >
                            {(comment.user.role === 'jobpadm' || comment.user.role === 'jobmodo')
                              ? 'Support JobPartiel'
                              : comment.user.email}
                          </Typography>
                          {isStaffComment && (
                            <Chip
                              size="small"
                              label="STAFF"
                              sx={{
                                backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                color: '#FF6B2C',
                                fontWeight: 600,
                                fontSize: '0.65rem',
                                height: '20px',
                                '& .MuiChip-label': {
                                  padding: '0 6px',
                                }
                              }}
                            />
                          )}
                          <Typography component="span" variant="caption" color="text.secondary">
                            {formatDate(comment.created_at)}
                          </Typography>
                          {comment.is_internal && (
                            <Chip
                              size="small"
                              icon={<LockIcon fontSize="small" />}
                              label="Interne"
                              color="warning"
                            />
                          )}
                          {canDelete && (
                            <Tooltip title="Supprimer ce commentaire" arrow>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteComment(comment.id)}
                                sx={{
                                  ml: 'auto',
                                  color: 'rgba(211, 47, 47, 0.7)',
                                  '&:hover': {
                                    color: 'rgba(211, 47, 47, 1)',
                                    bgcolor: 'rgba(211, 47, 47, 0.08)'
                                  }
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1, wordBreak: 'break-word', overflowWrap: 'break-word', maxWidth: '100%' }} component="span">
                          <Typography
                            component="span"
                            variant="body2"
                            color="text.primary"
                            sx={{
                              display: 'block',
                              whiteSpace: 'pre-wrap',
                              width: '100%'
                            }}
                          >
                            {comment.message}
                          </Typography>
                        </Box>
                      }
                      sx={{ width: '100%', overflow: 'hidden' }}
                    />
                  </ListItem>
                </motion.div>
              );
            })}
          </List>
        </motion.div>
      )}

      {!readOnly && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Paper component="form" onSubmit={handleSubmit} elevation={0} sx={{ p: 2, border: `1px solid ${theme.palette.divider}` }}>
            <TextField
              fullWidth
              multiline
              minRows={3}
              maxRows={15}
              placeholder="Ajouter un commentaire..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              variant="outlined"
              sx={{
                mb: 2,
                '& .MuiInputBase-inputMultiline': {
                  overflowY: 'auto',
                  resize: 'none',
                  fontSize: '1rem',  // Augmenter légèrement la taille de police
                  lineHeight: 1.5,   // Améliorer l'espacement des lignes
                },
                '& .MuiOutlinedInput-root': {
                  transition: 'all 0.2s ease',
                }
              }}
              InputProps={{
                sx: {
                  height: 'auto',
                }
              }}
            />

            <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
              <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
                {isStaff && (
                  <FormControl component="fieldset">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={isInternalComment}
                          onChange={(e) => setIsInternalComment(e.target.checked)}
                          color="warning"
                        />
                      }
                      label={
                        <Typography variant="body2" color={isInternalComment ? 'warning.main' : 'text.secondary'}>
                          Commentaire interne
                        </Typography>
                      }
                    />
                  </FormControl>
                )}

                {/* Ajout du sélecteur de modèles de réponse pour le staff uniquement */}
                {isStaff && (
                  <ResponseTemplateSelector
                    onTemplateSelect={handleTemplateSelect}
                    currentCategory={ticketCategory}
                  />
                )}

                {/* Bouton d'amélioration IA - Réservé au staff uniquement */}
                {isStaff && (
                  <Tooltip title={stripHtml(newMessage).trim() ? "Améliorer avec l'IA" : "Générer avec l'IA"}>
                    <span>
                      <Button
                        onClick={handleAiImprovement}
                        disabled={false}
                        sx={{
                          backgroundColor: '#FFE4BA',
                          color: '#FF6B2C',
                          padding: '6px 12px',
                          borderRadius: '8px',
                          fontSize: '0.85rem',
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: '#FFF8F3',
                          },
                          '&:disabled': {
                            backgroundColor: '#f5f5f5',
                            color: '#bdbdbd',
                          }
                        }}
                        startIcon={<AutoAwesomeIcon />}
                      >
                        {stripHtml(newMessage).trim() ? "Améliorer avec IA" : "Générer avec IA"}
                      </Button>
                    </span>
                  </Tooltip>
                )}
              </Box>

              <Button
                type="submit"
                variant="contained"
                endIcon={submitting ? null : <SendIcon />}
                disabled={!newMessage.trim() || submitting}
                sx={{
                  bgcolor: '#FF6B2C',
                  '&:hover': {
                    bgcolor: '#FF7A35',
                  },
                  boxShadow: '0 2px 8px rgba(255, 107, 44, 0.2)',
                  position: 'relative',
                  overflow: 'hidden',
                  mt: { xs: 2, sm: 0 }
                }}
              >
                {submitting ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CircularProgress size={20} sx={{ color: 'white', mr: 1 }} />
                    Envoi...
                  </Box>
                ) : 'Envoyer'}
              </Button>
            </Box>
          </Paper>
        </motion.div>
      )}

      {/* Dialogue de confirmation pour la réouverture du ticket */}
      <Dialog
        open={showReopenConfirmation}
        onClose={handleCancelReopen}
        aria-labelledby="reopen-dialog-title"
        aria-describedby="reopen-dialog-description"
      >
        <DialogTitle id="reopen-dialog-title" sx={{ color: '#FF6B2C', fontWeight: 600 }}>
          Réouverture du ticket
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="reopen-dialog-description">
            Ce ticket est actuellement {ticketStatus === 'ferme' ? 'fermé' : 'résolu'}. En ajoutant votre commentaire, le ticket sera automatiquement rouvert et l'équipe de support en sera notifiée.
            <br /><br />
            Souhaitez-vous continuer et rouvrir ce ticket ?
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCancelReopen}
            variant="outlined"
            sx={{
              color: 'text.secondary',
              borderColor: 'rgba(0, 0, 0, 0.23)',
            }}
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirmReopen}
            variant="contained"
            sx={{
              bgcolor: '#FF6B2C',
              '&:hover': {
                bgcolor: '#FF7A35',
              },
            }}
            autoFocus
          >
            Confirmer et rouvrir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogue de confirmation pour la suppression d'un commentaire */}
      <Dialog
        open={showDeleteConfirmation}
        onClose={cancelDeleteComment}
        aria-labelledby="delete-comment-dialog-title"
        aria-describedby="delete-comment-dialog-description"
      >
        <DialogTitle id="delete-comment-dialog-title" sx={{ color: '#d32f2f', fontWeight: 600 }}>
          Supprimer le commentaire
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-comment-dialog-description">
            Êtes-vous sûr de vouloir supprimer ce commentaire ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={cancelDeleteComment}
            variant="outlined"
            sx={{
              color: 'text.secondary',
              borderColor: 'rgba(0, 0, 0, 0.23)',
            }}
            disabled={deletingComment}
          >
            Annuler
          </Button>
          <Button
            onClick={confirmDeleteComment}
            variant="contained"
            color="error"
            disabled={deletingComment}
            startIcon={deletingComment ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {deletingComment ? 'Suppression...' : 'Supprimer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Système de génération IA */}
      {isAiConfirmModalOpen && (
        <AiGenerationSystem
          type="support_comment"
          prompt={(() => {
            // Créer l'historique des commentaires pour le contexte
            const commentsHistory = filteredComments.map(comment => {
              const role = (comment.user.role === 'jobpadm' || comment.user.role === 'jobmodo') ? 'Support JobPartiel' : 'Utilisateur';
              const internal = comment.is_internal ? ' [INTERNE]' : '';
              return `${role}${internal} (${formatDate(comment.created_at)}) : ${comment.message}`;
            }).join('\n\n');

            const currentMessage = stripHtml(newMessage).trim();
            const isImprovement = currentMessage.length > 0;

            return `Tu es un assistant IA spécialisé dans la rédaction de commentaires pour les tickets de support de JobPartiel.

CONTEXTE DU TICKET :
- Titre : ${ticketTitle}
- Description : ${ticketDescription}
- Catégorie : ${ticketCategory}
- Statut : ${ticketStatus}

HISTORIQUE DES COMMENTAIRES :
${commentsHistory || 'Aucun commentaire précédent'}

${isImprovement ? `MESSAGE ACTUEL À AMÉLIORER :
${currentMessage}

INSTRUCTIONS :
Améliore le message ci-dessus en gardant l'intention originale mais en le rendant plus professionnel, clair et utile. Assure-toi que le message amélioré :
- Conserve le sens et l'objectif du message original
- Soit plus structuré et professionnel
- Apporte de la valeur ajoutée au ticket
- Reste cohérent avec l'historique des échanges` : `INSTRUCTIONS :
Génère un commentaire professionnel et utile pour ce ticket de support. Le commentaire doit :
- Être pertinent par rapport au problème décrit
- Prendre en compte l'historique des échanges
- Proposer une solution concrète ou demander des clarifications nécessaires
- Être constructif et bienveillant`}

RÔLE DE L'UTILISATEUR : ${isStaff ? 'Membre de l\'équipe support JobPartiel' : 'Utilisateur/Client'}

STYLE REQUIS :
- Ton professionnel mais chaleureux
- Phrases claires et concises
- Éviter le jargon technique si l'utilisateur n'est pas du staff
- Proposer des solutions pratiques
- Faire preuve d'empathie si nécessaire

Le commentaire final doit faire entre 50 et 300 mots et être directement utilisable.`;
          })()}
          originalPrompt={stripHtml(newMessage).trim().length > 0 ? stripHtml(newMessage).trim() : undefined}
          onComplete={(content: string | null) => {
            setIsAiConfirmModalOpen(false);
            if (content) {
              // Mettre à jour le contenu du commentaire avec le contenu généré
              setNewMessage(content);
              // Notification de succès
              notify(
                stripHtml(newMessage).trim().length > 0
                  ? 'Commentaire amélioré avec succès par l\'IA'
                  : 'Commentaire généré avec succès par l\'IA',
                'success'
              );
            }
          }}
          onCancel={() => {
            setIsAiConfirmModalOpen(false);
          }}
        />
      )}
    </Box>
  );
};

export default CommentsList;