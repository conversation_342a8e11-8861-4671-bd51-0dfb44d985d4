import { useState, useEffect, useCallback, useRef } from 'react';
import supportTicketService, { Ticket, TicketFilters, User } from '../services/supportTicketService';
import { logger } from '../utils/logger';

interface UseTicketsResult {
  tickets: Ticket[];
  loading: boolean;
  error: Error | null;
  totalCount: number;
  fetchTickets: (filters?: TicketFilters) => Promise<void>;
  getTicket: (id: string) => Promise<Ticket | null>;
  createTicket: (ticketData: any) => Promise<Ticket | null>;
  updateTicket: (id: string, ticketData: Partial<Ticket>) => Promise<Ticket | null>;
  deleteTicket: (id: string) => Promise<boolean>;
  fetchUserInfo: (userIds: string[]) => Promise<Record<string, User>>;
}

/**
 * Hook pour gérer les interactions avec les tickets de support
 * @param initialFilters Filtres initiaux à appliquer
 * @returns Un objet avec les tickets, l'état de chargement, les erreurs et les méthodes pour manipuler les tickets
 */
export const useTickets = (initialFilters: TicketFilters = {}): UseTicketsResult => {
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [currentFilters, setCurrentFilters] = useState<TicketFilters>(initialFilters);
  const isInitialMount = useRef(true);
  const fetchInProgress = useRef(false);
  const getTicketInProgress = useRef(false);
  const lastFetchTime = useRef<Record<string, number>>({});

  /**
   * Récupère les tickets avec les filtres spécifiés
   */
  const fetchTickets = useCallback(async (filters?: TicketFilters) => {
    // Éviter les appels multiples rapprochés
    if (fetchInProgress.current) {
      logger.info('Appel à fetchTickets ignoré pour éviter les requêtes multiples');
      return;
    }

    try {
      fetchInProgress.current = true;
      setLoading(true);
      setError(null);
      
      // Mettre à jour les filtres si de nouveaux sont fournis
      const filtersToUse = filters || currentFilters;
      if (filters) {
        setCurrentFilters(filters);
      }
      
      // Nettoyer les filtres avant de les envoyer à l'API
      const cleanedFilters: TicketFilters = {};
      
      // Ne copier que les filtres qui ont une valeur
      Object.entries(filtersToUse).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanedFilters[key as keyof TicketFilters] = value;
        }
      });
      
      // S'assurer que page et limit sont toujours présents
      if (!cleanedFilters.page) cleanedFilters.page = 1;
      if (!cleanedFilters.limit) cleanedFilters.limit = 10;
      
      logger.info("Filtres nettoyés envoyés à l'API:", cleanedFilters);
      
      const response = await supportTicketService.getTickets(cleanedFilters);
      setTickets(response.tickets);
      setTotalCount(response.total);
    } catch (error) {
      logger.error('Erreur lors de la récupération des tickets:', error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
    } finally {
      setLoading(false);
      fetchInProgress.current = false;
    }
  }, []);

  /**
   * Récupère un ticket par son ID
   */
  const getTicket = useCallback(async (id: string): Promise<Ticket | null> => {
    try {
      getTicketInProgress.current = true;
      setLoading(true);
      setError(null);
      const ticket = await supportTicketService.getTicket(id);
      lastFetchTime.current[id] = Date.now();
      return ticket;
    } catch (error) {
      logger.error(`Erreur lors de la récupération du ticket ${id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return null;
    } finally {
      setLoading(false);
      getTicketInProgress.current = false;
    }
  }, []);

  /**
   * Récupère les informations des utilisateurs par leurs IDs
   */
  const fetchUserInfo = useCallback(async (userIds: string[]): Promise<Record<string, User>> => {
    try {
      // Utiliser le service supportTicketService pour récupérer les informations utilisateur
      const userMap = await supportTicketService.getUsersInfo(userIds);
      return userMap;
    } catch (error) {
      logger.error('Erreur lors de la récupération des utilisateurs:', error);
      return {};
    }
  }, []);

  /**
   * Crée un nouveau ticket
   */
  const createTicket = useCallback(async (ticketData: any): Promise<Ticket | null> => {
    try {
      setLoading(true);
      setError(null);
      const newTicket = await supportTicketService.createTicket(ticketData);
      return newTicket;
    } catch (error) {
      logger.error('Erreur lors de la création du ticket:', error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Met à jour un ticket existant
   */
  const updateTicket = useCallback(async (id: string, ticketData: Partial<Ticket>): Promise<Ticket | null> => {
    try {
      setLoading(true);
      setError(null);
      const updatedTicket = await supportTicketService.updateTicket(id, ticketData);
      return updatedTicket;
    } catch (error) {
      logger.error(`Erreur lors de la mise à jour du ticket ${id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Supprime un ticket
   */
  const deleteTicket = useCallback(async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      await supportTicketService.deleteTicket(id);
      return true;
    } catch (error) {
      logger.error(`Erreur lors de la suppression du ticket ${id}:`, error);
      setError(error instanceof Error ? error : new Error('Erreur inconnue'));
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Charger les tickets au montage du composant et lorsque currentFilters change
  useEffect(() => {
    // Éviter le double chargement au montage initial
    if (isInitialMount.current) {
      isInitialMount.current = false;
      fetchTickets();
    } else if (Object.keys(currentFilters).length > 0) {
      fetchTickets(currentFilters);
    }
  }, [currentFilters, fetchTickets]);

  return {
    tickets,
    loading,
    error,
    totalCount,
    fetchTickets,
    getTicket,
    createTicket,
    updateTicket,
    deleteTicket,
    fetchUserInfo
  };
};

export default useTickets; 