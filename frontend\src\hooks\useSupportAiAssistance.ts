import { useState } from 'react';
import axios from 'axios';
import { notify } from '../components/Notification';
import logger from '../utils/logger';
import { API_CONFIG } from '../config/api';
import { fetchCsrfToken } from '../services/csrf';
import { getCommonHeaders } from '../utils/headers';

interface SupportAssistanceRequest {
  ticketId: string;
  mode: 'user' | 'staff';
}

interface SupportAssistanceResponse {
  success: boolean;
  content?: string;
  creditsRemaining?: number;
  mode?: string;
  message?: string;
  requiresConsent?: boolean;
  toastType?: 'success' | 'error' | 'warning' | 'info';
}

export const useSupportAiAssistance = () => {
  const [loading, setLoading] = useState(false);
  const [assistance, setAssistance] = useState<string | null>(null);
  const [creditsRemaining, setCreditsRemaining] = useState<number | null>(null);

  const generateAssistance = async (request: SupportAssistanceRequest): Promise<boolean> => {
    setLoading(true);
    setAssistance(null);

    try {
      logger.info('Génération d\'assistance IA pour le ticket:', request);

      // Récupérer les headers avec CSRF token
      await fetchCsrfToken();
      const headers = await getCommonHeaders();
      headers['X-CSRF-Token'] = await fetchCsrfToken();

      const response = await axios.post<SupportAssistanceResponse>(
        `${API_CONFIG.baseURL}/api/ai-generation/support-assistance`,
        request,
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          withCredentials: true,
        }
      );

      if (response.data.success && response.data.content) {
        setAssistance(response.data.content);
        setCreditsRemaining(response.data.creditsRemaining || null);

        notify(
          `Assistance IA générée avec succès (Mode: ${response.data.mode === 'staff' ? 'Équipe' : 'Utilisateur'})`,
          'success'
        );

        return true;
      } else {
        const errorMessage = response.data.message || 'Erreur lors de la génération de l\'assistance';

        if (response.data.requiresConsent) {
          notify(
            'Vous devez accepter les conditions d\'utilisation de l\'IA avant de pouvoir utiliser l\'assistance',
            'warning'
          );
        } else {
          notify(errorMessage, 'error');
        }

        return false;
      }
    } catch (error: any) {
      logger.error('Erreur lors de la génération d\'assistance IA:', error);

      let errorMessage = 'Erreur lors de la génération de l\'assistance';

      if (error.response?.status === 401) {
        errorMessage = 'Vous devez être connecté pour utiliser l\'assistance IA';
      } else if (error.response?.status === 403) {
        errorMessage = error.response.data?.message || 'Accès non autorisé';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || 'Données invalides';
      } else if (error.response?.status === 404) {
        errorMessage = 'Ticket non trouvé';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      notify(errorMessage, 'error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const clearAssistance = () => {
    setAssistance(null);
    setCreditsRemaining(null);
  };

  return {
    loading,
    assistance,
    creditsRemaining,
    generateAssistance,
    clearAssistance,
  };
};

export default useSupportAiAssistance;
