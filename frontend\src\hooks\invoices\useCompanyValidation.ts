import { useCompanySettings } from './useCompanyInvoiceSettings';

export const useCompanyValidation = () => {
  const { settings } = useCompanySettings();

  const isCompanyInfoComplete = (): boolean => {
    if (!settings) {
      return false;
    }

    // Vérifier que le nom de l'entreprise est renseigné (champ obligatoire minimum)
    return !!(settings.nom && settings.nom.trim() !== '');
  };

  const getValidationMessage = (): string => {
    return 'Vous devez d\'abord compléter les informations de votre entreprise avant de créer un document. Vous pouvez le faire via le bouton "Paramètres" en haut de cette page.';
  };

  return {
    isCompanyInfoComplete,
    getValidationMessage,
    settings
  };
}; 