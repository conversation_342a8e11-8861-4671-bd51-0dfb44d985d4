/* Animations pour les composants liés à l'IA */

/* Animation de particules */
@keyframes floatParticle {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.ai-particle {
  animation: floatParticle 2s ease-in-out infinite;
}

/* Animation pour les icônes rotatives */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-spin {
  animation: spin 4s linear infinite;
}

.ai-spin-fast {
  animation: spin 2s linear infinite;
}

/* Animation de pulsation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.ai-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Animation pour les barres de progression */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.ai-progress {
  animation: progressBar 1s ease-out forwards;
}

/* Animation de fade-in */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.ai-fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

/* Animation d'apparition du bas */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.ai-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

/* Couleurs de JobPartiel */
.text-jobpartiel-primary {
  color: #FF6B2C;
}

.bg-jobpartiel-primary {
  background-color: #FF6B2C;
}

.text-jobpartiel-secondary {
  color: #FF7A35;
}

.bg-jobpartiel-secondary {
  background-color: #FF7A35;
}

.text-jobpartiel-tertiary {
  color: #FF965E;
}

.bg-jobpartiel-tertiary {
  background-color: #FF965E;
}

.bg-jobpartiel-bg {
  background-color: #FFF8F3;
}

.border-jobpartiel {
  border-color: #FFE4BA;
}

/* Dégradés */
.bg-jobpartiel-gradient {
  background: linear-gradient(to right, #FF6B2C, #FF965E);
}

.text-jobpartiel-gradient {
  background: linear-gradient(to right, #FF6B2C, #FF965E);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Animation ultra fluide pour la sauvegarde auto */
@keyframes autoSaveGlow {
  0% {
    text-shadow: 0 0 0px #FF6B2C;
    color: #FF6B2C;
    transform: scale(1);
    opacity: 1;
  }
  30% {
    text-shadow: 0 0 12px #FF965E;
    color: #FF965E;
    transform: scale(1.13);
    opacity: 1;
  }
  40% {
    text-shadow: 0 0 18px #FF965E;
    color: #4CAF50;
    transform: scale(1.13);
    opacity: 1;
  }
  70% {
    text-shadow: 0 0 18px #4CAF50;
    color: #4CAF50;
    transform: scale(1.08);
    opacity: 1;
  }
  100% {
    text-shadow: 0 0 0px #FF6B2C;
    color: #FF6B2C;
    transform: scale(1);
    opacity: 1;
  }
}

.auto-save-animate {
  animation: autoSaveGlow 0.7s cubic-bezier(0.22, 1, 0.36, 1);
  position: relative;
}

.auto-save-check {
  display: inline-block;
  position: absolute;
  right: -22px;
  top: 50%;
  transform: translateY(-50%) scale(0.7);
  opacity: 0;
  color: #4CAF50;
  font-size: 1.2em;
  pointer-events: none;
  animation: autoSaveCheckAppear 0.7s cubic-bezier(0.22, 1, 0.36, 1);
}

@keyframes autoSaveCheckAppear {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale(0.3);
  }
  40% {
    opacity: 1;
    transform: translateY(-50%) scale(1.18);
  }
  60% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-50%) scale(0.7);
  }
} 