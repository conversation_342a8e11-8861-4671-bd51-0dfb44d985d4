import React, { createContext, useContext, useState, useC<PERSON>back, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ert, AlertColor } from '@mui/material';

// Types pour les notifications
interface Notification {
  id: number;
  message: string;
  type: AlertColor;
  autoHideDuration?: number;
}

interface NotificationContextType {
  notifications: Notification[];
  notify: (message: string, type?: AlertColor, autoHideDuration?: number) => void;
  closeNotification: (id: number) => void;
}

// Créer le contexte
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

// Fournisseur de contexte
export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [lastId, setLastId] = useState(0);

  // Fonction pour ajouter une notification
  const notify = useCallback((
    message: string,
    type: AlertColor = 'info',
    autoHideDuration = 5000
  ) => {
    const id = lastId + 1;
    setLastId(id);
    setNotifications(prev => [...prev, { id, message, type, autoHideDuration }]);
  }, [lastId]);

  // Fonction pour fermer une notification
  const closeNotification = useCallback((id: number) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  return (
    <NotificationContext.Provider value={{ notifications, notify, closeNotification }}>
      {children}
      
      {/* Afficher les notifications */}
      {notifications.map(notification => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.autoHideDuration}
          onClose={() => closeNotification(notification.id)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{ mb: notifications.indexOf(notification) * 8 }}
        >
          <Alert
            onClose={() => closeNotification(notification.id)}
            severity={notification.type}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </NotificationContext.Provider>
  );
};

// Hook personnalisé pour utiliser le contexte de notification
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications doit être utilisé à l\'intérieur d\'un NotificationProvider');
  }
  return context;
};

export default NotificationProvider; 