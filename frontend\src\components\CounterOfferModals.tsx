import React, { useState, useEffect, useRef } from 'react';
import { TextField, CircularProgress, Box, Typography, IconButton } from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { notify } from './Notification';
import { missionsApi } from '@/pages/dashboard/ToutesLesMissions/missionsApi';
import ModalPortal from './ModalPortal';
import { X, CheckCircle2, AlertTriangle, Info, ChevronDown } from 'lucide-react';
import logger from '@/utils/logger';
import { motion } from 'framer-motion';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';

// Composant ScrollIndicator
const ScrollIndicator: React.FC<{ containerRef: React.RefObject<HTMLDivElement | null>, onScrollClick: () => void }> = ({ containerRef, onScrollClick }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollPosition = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      
      // On cache l'indicateur si on a défilé de plus de 100px ou si on est proche de la fin
      setIsVisible(scrollPosition < 30 && scrollPosition < scrollHeight - clientHeight - 100);
    };

    container.addEventListener('scroll', handleScroll);
    
    // Vérifier si le défilement est nécessaire
    const needsScroll = container.scrollHeight > container.clientHeight;
    setIsVisible(needsScroll);

    return () => container.removeEventListener('scroll', handleScroll);
  }, [containerRef]);

  if (!isVisible) return null;

  return (
    <div 
      onClick={onScrollClick}
      className="absolute bottom-24 right-10 bg-[#FF6B2C] text-white px-6 py-1.5 rounded-full shadow-lg cursor-pointer flex items-center gap-2 animate-bounce transition-all hover:bg-[#FF7A35] z-10"
      style={{
        transform: 'translate(-50%, -50%)',
        width: 'fit-content'
      }}
    >
      <span>Défiler</span>
      <ChevronDown className="h-5 w-5" />
    </div>
  );
};

// Fonction pour nettoyer le HTML
// const stripHtml = (html: string): string => {
//   if (!html) return '';
  
//   try {
//     // Approche compatible avec SSR
//     return html
//       .replace(/<[^>]*>/g, '') // Supprime toutes les balises HTML
//       .replace(/&nbsp;/g, ' ') // Remplace les espaces insécables
//       .replace(/&amp;/g, '&') // Remplace les &
//       .replace(/&lt;/g, '<') // Remplace les <
//       .replace(/&gt;/g, '>') // Remplace les >
//       .replace(/&quot;/g, '"') // Remplace les "
//       .replace(/&#39;/g, "'") // Remplace les '
//       .trim(); // Supprime les espaces en début et fin de chaîne
//   } catch (error) {
//     logger.warn('Erreur lors du nettoyage du HTML:', error);
//     return html; // En cas d'erreur, retourne le HTML d'origine
//   }
// };

// Schéma de validation commun
const counterOfferSchema = z.object({
  amount: z.number().min(1, 'Le montant doit être supérieur à 0'),
  message: z.string().min(10, 'Le message doit contenir au moins 10 caractères').max(1000, 'Le message ne doit pas dépasser 1000 caractères')
});

type CounterOfferFormData = z.infer<typeof counterOfferSchema>;

// Interface commune pour les props
interface BaseCounterOfferModalProps {
  open: boolean;
  onClose: () => void;
  missionId: string;
  proposalId: string;
  currentAmount: number; // Montant de la dernière contre-offre
  currentMessage?: string; // Message associé à la dernière contre-offre
  onCounterOfferSubmit: () => void;
  jobbeurName?: string; // Nom complet du jobbeur
  clientName?: string; // Nom complet du client
  missionInfo?: {
    categorie?: string;
    dureeEstimee?: string;
    localisation?: string;
    niveauRequis?: string;
    titre?: string;
    description?: string;
    payment_method?: string;
  };
  initialAmount?: number; // Montant initial de l'offre
  offerHistory?: {
    initialOffer: {
      amount: number;
      date?: string;
      message?: string;
    };
    jobbeurCounterOffers?: Array<{
      amount: number;
      date?: string;
      message?: string;
    }>;
    clientCounterOffers?: Array<{
      amount: number;
      date?: string;
      message?: string;
    }>;
  }; // Historique complet des offres avec dates et messages
}

// Props étendues pour le composant générique
interface GenericCounterOfferModalProps extends BaseCounterOfferModalProps {
  isJobbeur?: boolean;
  title?: string;
  offerLabel?: string;
}

// Composant générique de base
export const GenericCounterOfferModal: React.FC<GenericCounterOfferModalProps> = ({
  open,
  onClose,
  missionId,
  proposalId,
  currentAmount,
  onCounterOfferSubmit,
  isJobbeur = false,
  title = "Faire une contre-offre",
  initialAmount,
  offerHistory,
  jobbeurName,
  clientName,
  missionInfo
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMaxAmount, setIsMaxAmount] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showSameAmountConfirmation, setShowSameAmountConfirmation] = useState(false);
  const [formData, setFormData] = useState<CounterOfferFormData | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const confirmationContentRef = useRef<HTMLDivElement>(null);

  const smoothScroll = (element: HTMLElement, target: number, duration: number) => {
    const start = element.scrollTop;
    const change = target - start;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Fonction d'easing pour un défilement plus naturel
      const easeInOutQuad = (t: number) => {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      };

      element.scrollTop = start + change * easeInOutQuad(progress);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  };

  const handleScrollClick = () => {
    if (contentRef.current) {
      smoothScroll(contentRef.current, contentRef.current.scrollTop + 350, 800);
    }
  };

  const handleConfirmationScrollClick = () => {
    if (confirmationContentRef.current) {
      smoothScroll(confirmationContentRef.current, confirmationContentRef.current.scrollTop + 350, 800);
    }
  };

  // Log pour déboguer les valeurs reçues
  React.useEffect(() => {
    if (open) {
      logger.info('Modal ouverte avec les valeurs:', { 
        currentAmount, 
        initialAmount, 
        offerHistory,
        isJobbeur
      });
    }
  }, [open, currentAmount, initialAmount, offerHistory, isJobbeur]);

  // Fonction pour déterminer le montant correct à afficher
  const getCorrectAmount = () => {
    // logger.info('Calcul du montant correct avec:', {
    //   currentAmount, 
    //   initialAmount, 
    //   offerHistory,
    //   isJobbeur
    // });

    if (offerHistory) {
      // Si on est un client
      if (!isJobbeur) {
        // On prend la dernière contre-offre du jobbeur s'il y en a une
        if (offerHistory.jobbeurCounterOffers && offerHistory.jobbeurCounterOffers.length > 0) {
          return offerHistory.jobbeurCounterOffers[offerHistory.jobbeurCounterOffers.length - 1].amount;
        }
        // Sinon on prend l'offre initiale
        return offerHistory.initialOffer.amount;
      }
      // Si on est un jobbeur
      else {
        // On prend la dernière contre-offre du client s'il y en a une
        if (offerHistory.clientCounterOffers && offerHistory.clientCounterOffers.length > 0) {
          return offerHistory.clientCounterOffers[offerHistory.clientCounterOffers.length - 1].amount;
        }
        // Sinon on prend l'offre initiale
        return offerHistory.initialOffer.amount;
      }
    }
    
    // Si pas d'historique, on utilise le montant actuel fourni
    return currentAmount;
  };

  // Fonction pour déterminer si le montant affiché est l'offre initiale
  const isInitialOffer = () => {
    if (offerHistory) {
      // Si on est un client
      if (!isJobbeur) {
        // Si pas de contre-offre du jobbeur, c'est l'offre initiale
        return !(offerHistory.jobbeurCounterOffers && offerHistory.jobbeurCounterOffers.length > 0);
      }
      // Si on est un jobbeur
      else {
        // Si pas de contre-offre du client, c'est l'offre initiale
        return !(offerHistory.clientCounterOffers && offerHistory.clientCounterOffers.length > 0);
      }
    }
    
    // Si pas d'historique, on vérifie si le montant actuel est égal au montant initial
    return initialAmount && currentAmount === initialAmount;
  };

  // Utiliser le montant correct pour l'initialisation du formulaire
  const displayAmount = getCorrectAmount();
  const isInitial = isInitialOffer();
  // logger.info('Montant à afficher:', displayAmount);

  // Vérifier si le montant affiché est différent du montant actuel
  if (displayAmount !== currentAmount) {
    logger.info('Le montant affiché est différent du montant actuel:', { displayAmount, currentAmount });
  }

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<CounterOfferFormData>({
    resolver: zodResolver(counterOfferSchema),
    defaultValues: {
      amount: displayAmount, // Utiliser le montant correct ici
      message: ''
    }
  });

  const amount = watch('amount');
  const message = watch('message');

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numValue = parseFloat(value);
    
    if (value === '') {
      setValue('amount', 0);
      setIsMaxAmount(false);
    } else if (!isNaN(numValue)) {
      if (numValue <= 99999) {
        setValue('amount', numValue);
        setIsMaxAmount(numValue === 99999);
      } else {
        setValue('amount', 99999);
        setIsMaxAmount(true);
      }
    }
  };

  // Incrémenter le montant
  const incrementAmount = () => {
    const currentAmount = amount || 0;
    if (currentAmount < 99999) {
      const newAmount = Math.min(currentAmount + 1, 99999);
      setValue('amount', newAmount);
      setIsMaxAmount(newAmount === 99999);
    }
  };

  // Décrémenter le montant
  const decrementAmount = () => {
    const currentAmount = amount || 0;
    if (currentAmount > 0) {
      const newAmount = Math.max(currentAmount - 1, 0);
      setValue('amount', newAmount);
      setIsMaxAmount(false);
    }
  };

  // Vérifier si le formulaire est valide et afficher la modale de confirmation
  const handlePreSubmit = handleSubmit((data) => {
    setFormData(data);
    setShowConfirmation(true);
  });

  // Vérifier la validité du formulaire et afficher une notification si nécessaire
  const checkFormAndNotify = () => {
    if (!amount || amount <= 0) {
      notify('Veuillez entrer un montant valide', 'warning');
      return false;
    }
    
    if (!message || message.length < 10) {
      notify('Votre message doit contenir au moins 10 caractères', 'warning');
      return false;
    }
    
    return true;
  };

  // Soumettre définitivement la contre-offre
  const onSubmit = async (data: CounterOfferFormData) => {
    try {
      setIsSubmitting(true);

      // Vérifier que le montant est différent du montant actuel
      const displayAmount = getCorrectAmount();
      if (data.amount === displayAmount && !showSameAmountConfirmation) {
        // Au lieu de bloquer, on affiche une modale de confirmation personnalisée
        setFormData(data);
        setShowSameAmountConfirmation(true);
        setIsSubmitting(false);
        return;
      }

      // Si on arrive ici après confirmation du même montant, on ferme la modale de confirmation
      if (showSameAmountConfirmation) {
        setShowSameAmountConfirmation(false);
      }

      // Appeler l'API appropriée en fonction du type d'utilisateur
      if (isJobbeur) {
        await missionsApi.makeJobbeurCounterOffer(missionId, proposalId, data.amount, data.message);
      } else {
        await missionsApi.makeCounterOffer(missionId, proposalId, data.amount, data.message);
      }

      notify('Contre-offre envoyée avec succès', 'success');
      onCounterOfferSubmit();
      reset();
      setShowConfirmation(false);
      onClose();
    } catch (error) {
      logger.error('Erreur lors de l\'envoi de la contre-offre:', error);
      notify('Erreur lors de l\'envoi de la contre-offre', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fermer la modale de confirmation
  const handleCloseConfirmation = () => {
    setShowConfirmation(false);
  };

  // Fermer la modale de confirmation pour même montant
  const handleCloseSameAmountConfirmation = () => {
    setShowSameAmountConfirmation(false);
  };

  if (!open) return null;

  // Contenu de la modale principale
  const modalContent = (
    <motion.div
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.9, opacity: 0 }}
      transition={{ type: "spring", duration: 0.5 }}
      onClick={(e) => e.stopPropagation()}
      style={{
        backgroundColor: 'white',
        borderRadius: '16px',
        padding: '0 14px',
        maxWidth: '700px',
        width: '90%',
        position: 'relative',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
        border: `1px solid #FF6B2C`,
        maxHeight: '90vh',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* En-tête de la modale */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        borderBottom: '1px solid #FFE4BA',
        padding: '16px 8px 16px 16px'
      }}>
        <Typography variant="h5" sx={{ 
          fontWeight: 'bold', 
          color: '#FF6B2C',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <SwapHorizIcon />
          {title}
        </Typography>
        <IconButton onClick={onClose} size="small" sx={{ color: '#666' }}>
          <X size={24} />
        </IconButton>
      </Box>

      {/* Contenu scrollable */}
      <div ref={contentRef} className="flex-1 overflow-y-auto p-6">
        <form onSubmit={handlePreSubmit} className="space-y-4">
          {/* Consignes de bienveillance */}
          <Box 
            sx={{ 
              mb: 3,
              p: 2,
              backgroundColor: 'rgba(255, 107, 44, 0.08)',
              borderRadius: '8px',
              border: '1px solid rgba(255, 107, 44, 0.2)',
              display: 'flex',
              flexDirection: 'column',
              gap: 2
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: '#FF6B2C',
                fontWeight: 'bold',
                marginBottom: '8px'
              }}
            >
              Consignes importantes
            </Typography>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                <span>Proposez un tarif juste et adapté à la mission, ni trop élevé ni trop bas.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                <span>Soyez bienveillant et respectueux dans votre message.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                <span>Expliquez clairement les raisons de votre contre-offre.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                <span>Prenez en compte les compétences et l'expérience nécessaires pour la mission.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle2 className="h-5 w-5 text-[#FF6B2C] mr-2 flex-shrink-0 mt-0.5" />
                <span>Considérez le temps et les ressources que nécessite la mission.</span>
              </li>
              <li className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <span className="font-semibold text-red-600">En cas de refus de cette contre-offre, l'échange sera définitivement clôturé et il ne sera plus possible de répondre ou de négocier.</span>
              </li>
            </ul>
          </Box>

          {/* Résumé de l'offre actuelle */}
          <Box 
            sx={{ 
              mb: 3,
              backgroundColor: '#FFF8F3',
              border: '1px solid #FFE4BA',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '24px'
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: '#FF6B2C',
                fontWeight: 'bold',
                marginBottom: '12px'
              }}
            >
              Résumé de l'offre
            </Typography>
            
            {/* Historique des montants */}
              <>
                {initialAmount && !isInitial && (
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Offre initiale :</span>
                    <span className="text-sm font-medium text-[#FF6B2C]">{initialAmount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
                  </div>
                )}
                  <div className="flex flex-col">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {isInitial ? "Offre initiale" : 
                          (isJobbeur ? 
                            (clientName ? `Contre-offre de ${clientName}` : "Contre-offre du client") : 
                            (jobbeurName ? `Contre-offre de ${jobbeurName}` : "Contre-offre du jobbeur")
                          )
                        } :
                      </span>
                      <span className="text-lg font-bold text-[#FF6B2C]">{displayAmount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
                    </div>
                  </div>
              </>
          </Box>

          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              {isJobbeur ? `Votre contre-offre (${missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'})` : `Nouveau montant proposé (${missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'})`}
            </label>
            <div className="relative">
              <TextField
                id="amount"
                type="number"
                fullWidth
                value={amount}
                onChange={handleAmountChange}
                inputProps={{ 
                  min: 0, 
                  max: 99999,
                  step: "1"
                }}
                placeholder="Entrez votre tarif"
                size="small"
                className="bg-white"
                error={!!errors.amount}
                helperText={errors.amount?.message}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&.Mui-focused fieldset': {
                      borderColor: '#FF6B2C',
                    },
                    '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
                      '-webkit-appearance': 'none',
                      margin: 0,
                    },
                    '& input[type=number]': {
                      '-moz-appearance': 'textfield',
                    },
                  },
                  '& .MuiInputLabel-root.Mui-focused': {
                    color: '#FF6B2C',
                  },
                }}
              />
              <div className="absolute right-0 top-0 h-full flex flex-col border-l">
                <button 
                  type="button"
                  onClick={incrementAmount}
                  disabled={isMaxAmount}
                  className="flex-1 px-2 flex items-center justify-center hover:bg-gray-100 transition-colors border-b"
                  aria-label="Augmenter le montant"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${isMaxAmount ? 'text-gray-300' : 'text-gray-600'}`}>
                    <path d="m18 15-6-6-6 6"/>
                  </svg>
                </button>
                <button 
                  type="button"
                  onClick={decrementAmount}
                  disabled={!amount || amount <= 0}
                  className="flex-1 px-2 flex items-center justify-center hover:bg-gray-100 transition-colors"
                  aria-label="Diminuer le montant"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`${!amount || amount <= 0 ? 'text-gray-300' : 'text-gray-600'}`}>
                    <path d="m6 9 6 6 6-6"/>
                  </svg>
                </button>
              </div>
            </div>
            {isMaxAmount && (
              <p className="mt-1 text-xs text-amber-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <path d="M10.29 3.86 1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                  <line x1="12" y1="9" x2="12" y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                Montant maximum atteint (99 999 €)
              </p>
            )}
          </div>
          
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              Votre message
            </label>
            <TextField
              id="message"
              {...register('message')}
              multiline
              minRows={4}
              maxRows={8}
              fullWidth
              placeholder="Expliquez les raisons de votre contre-offre..."
              size="small"
              className="bg-white"
              error={!!errors.message || (watch('message')?.length > 0 && watch('message')?.length < 10)}
              helperText={
                errors.message?.message || 
                (watch('message')?.length > 0 && watch('message')?.length < 10 
                  ? `${watch('message')?.length}/10 caractères minimum requis` 
                  : `${watch('message')?.length || 0}/1000 caractères - Minimum 10 caractères`)
              }
              inputProps={{ maxLength: 1000 }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: '#FF6B2C',
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: '#FF6B2C',
                },
                '& .MuiFormHelperText-root': {
                  color: (watch('message')?.length > 0 && watch('message')?.length < 10) ? '#d32f2f' : 'inherit',
                  fontWeight: (watch('message')?.length > 0 && watch('message')?.length < 10) ? 'bold' : 'normal',
                }
              }}
            />
          </div>
        </form>
      </div>
      
      {/* Pied de page avec boutons toujours visibles */}
      <Box sx={{ 
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '12px', 
        padding: '24px',
        borderTop: '1px solid #FFE4BA',
        flexShrink: 0
      }}>
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          disabled={isSubmitting}
          style={{
            backgroundColor: '#FFF8F3',
            color: '#666',
            border: '2px solid #FFE4BA',
            borderRadius: '8px',
            padding: '8px 24px',
            fontWeight: 'bold'
          }}
        >
          Annuler
        </button>
        <button
          onClick={() => {
            if (checkFormAndNotify()) {
              handlePreSubmit();
            }
          }}
          className="px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center min-w-[120px]"
          disabled={isSubmitting}
          style={{
            backgroundColor: '#FF6B2C',
            borderRadius: '8px',
            padding: '8px 24px',
            fontWeight: 'bold'
          }}
        >
          {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Envoyer'}
        </button>
      </Box>

      {/* Indicateur de défilement */}
      <ScrollIndicator containerRef={contentRef} onScrollClick={handleScrollClick} />
    </motion.div>
  );

  // Contenu de la modale de confirmation
  const confirmationModalContent = formData && (
    <div 
      className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl w-full max-w-[700px] max-h-[90vh] overflow-hidden flex flex-col mx-auto shadow-xl"
      onClick={(e) => e.stopPropagation()}
    >
      {/* En-tête */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Confirmation de votre contre-offre</h2>
        <button 
          onClick={handleCloseConfirmation} 
          className="text-gray-500 hover:text-gray-700 transition-colors" 
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Contenu scrollable */}
      <div ref={confirmationContentRef} className="flex-1 overflow-y-auto p-6">

        {/* Récapitulatif de la contre-offre */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Récapitulatif et historique de la contre-offre</h3>
          
          <div className="bg-[#FFF8F3] p-4 rounded-lg mb-4">
            {offerHistory ? (
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Montant actuel de l'offre :</span>
                  <span className="text-2xl font-bold text-[#FF6B2C]">{displayAmount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
                </div>
            ) : (
              // Affichage simplifié avec initialAmount et currentAmount
              <>
                {initialAmount && initialAmount !== currentAmount && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Offre initiale :</span>
                    <span className="text-sm font-medium text-[#FF6B2C]">{initialAmount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Montant actuel de l'offre :</span>
                  <span className="text-2xl font-bold text-[#FF6B2C]">{currentAmount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
                </div>
              </>
            )}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Montant de votre contre-offre :</span>
              <span className="text-2xl font-bold text-[#FF6B2C]">{formData.amount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}</span>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Votre message :</h4>
            <p className="text-sm text-gray-800 whitespace-pre-wrap">{formData.message}</p>
          </div>
        </div>

        {/* Rappel des consignes */}
        <div className="mb-6 bg-amber-50 p-4 rounded-lg border border-amber-200">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 text-amber-500 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-amber-700 mb-2">Attention</h3>
              <p className="text-sm text-amber-700">
                Cette action est définitive. Une fois votre contre-offre envoyée, vous ne pourrez plus la modifier.
                En cas de refus de cette contre-offre par l'autre partie, l'échange sera définitivement clôturé.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Pied de page avec boutons toujours visibles */}
      <div className="flex justify-end gap-3 p-4 border-t border-gray-200 bg-white">
        <button
          type="button"
          onClick={handleCloseConfirmation}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          disabled={isSubmitting}
        >
          Revenir en arrière
        </button>
        <button
          onClick={() => formData && onSubmit(formData)}
          className="px-4 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center min-w-[120px]"
          disabled={isSubmitting}
        >
          {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Confirmer l\'envoi'}
        </button>
      </div>

      {/* Indicateur de défilement pour la confirmation */}
      <ScrollIndicator containerRef={confirmationContentRef} onScrollClick={handleConfirmationScrollClick} />
    </div>
  );

  // Contenu de la modale de confirmation pour même montant
  const sameAmountConfirmationModalContent = formData && (
    <div 
      className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl w-full max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col mx-auto shadow-xl"
      onClick={(e) => e.stopPropagation()}
    >
      {/* En-tête */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white z-10">
        <h2 className="text-xl font-semibold text-gray-800">Confirmation du montant identique</h2>
        <button 
          onClick={handleCloseSameAmountConfirmation} 
          className="text-gray-500 hover:text-gray-700 transition-colors" 
          aria-label="Fermer"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      {/* Contenu */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="mb-6 bg-amber-50 p-4 rounded-lg border border-amber-200">
          <div className="flex items-start">
            <Info className="h-6 w-6 text-amber-500 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-amber-700 mb-2">Attention</h3>
              <p className="text-sm text-amber-700">
                Le montant de votre contre-offre est identique au montant actuel ({formData.amount} {missionInfo?.payment_method === 'direct_only' ? '€' : 'Jobis'}).
                Souhaitez-vous tout de même envoyer cette contre-offre pour demander des informations supplémentaires ?
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">Votre message :</h4>
          <p className="text-sm text-gray-800 whitespace-pre-wrap">{formData.message}</p>
        </div>
      </div>
      
      {/* Pied de page avec boutons */}
      <div className="flex justify-end gap-3 p-4 border-t border-gray-200 bg-white">
        <button
          type="button"
          onClick={handleCloseSameAmountConfirmation}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          disabled={isSubmitting}
        >
          Annuler
        </button>
        <button
          onClick={() => formData && onSubmit(formData)}
          className="px-4 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors flex items-center justify-center min-w-[120px]"
          disabled={isSubmitting}
        >
          {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Confirmer l\'envoi'}
        </button>
      </div>
    </div>
  );

  // Utiliser le ModalPortal avec l'effet de flou intégré
  return (
    <>
      <ModalPortal onBackdropClick={onClose}>{modalContent}</ModalPortal>
      {showConfirmation && formData && (
        <ModalPortal onBackdropClick={handleCloseConfirmation}>{confirmationModalContent}</ModalPortal>
      )}
      {showSameAmountConfirmation && formData && (
        <ModalPortal onBackdropClick={handleCloseSameAmountConfirmation}>{sameAmountConfirmationModalContent}</ModalPortal>
      )}
    </>
  );
};

// Composant pour les contre-offres du client
export const CounterOfferModal: React.FC<BaseCounterOfferModalProps> = (props) => {
  return (
    <GenericCounterOfferModal
      {...props}
      isJobbeur={false}
      title="Faire une contre-offre"
      offerLabel="Offre actuelle"
    />
  );
};

// Composant pour les contre-offres du jobbeur
export const JobbeurCounterOfferModal: React.FC<BaseCounterOfferModalProps> = (props) => {
  return (
    <GenericCounterOfferModal
      {...props}
      isJobbeur={true}
      title="Faire une contre-offre"
      offerLabel="Contre-offre actuelle"
    />
  );
}; 