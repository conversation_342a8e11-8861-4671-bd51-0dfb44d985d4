import React, { useState, useEffect } from 'react';
import axios from 'axios';
import NotificationPopup from './components/NotificationPopup';
import { API_CONFIG } from '../../config/api';

const mockPartners = [
  {
    id: 1,
    name: "<PERSON>",
    type: "Fournisseur",
    status: "active",
    collaborations: 12,
    rating: 4.8,
    lastActivity: "2023-12-01"
  },
  {
    id: 2,
    name: "Castorama",
    type: "Fournisseur",
    status: "active",
    collaborations: 8,
    rating: 4.5,
    lastActivity: "2023-11-28"
  },
  {
    id: 3,
    name: "<PERSON>",
    type: "Artisan",
    status: "pending",
    collaborations: 0,
    rating: null,
    lastActivity: null
  }
];

const mockOpportunities = [
  {
    id: 1,
    title: "Projet Rénovation Complète",
    partner: "<PERSON>",
    type: "Collaboration",
    value: 5000,
    deadline: "2023-12-31",
    status: "open"
  },
  {
    id: 2,
    title: "Formation Nouveaux Produits",
    partner: "Castorama",
    type: "Formation",
    value: null,
    deadline: "2023-12-15",
    status: "pending"
  }
];

const NetworkPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('partners');

  return (
    <div>
      <div className="space-y-6 px-2 md:px-0">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-800">Réseau & Partenariats</h1>
          <button className="bg-[#FF7A35] text-white px-4 py-2 rounded-md hover:bg-[#ff6b2c] transition-colors">
            Nouveau Partenariat
          </button>
        </div>

        {/* Vue d'ensemble */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Partenaires actifs</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">2</p>
            <p className="text-sm text-green-600">+1 ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Collaborations</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">20</p>
            <p className="text-sm text-green-600">+5 ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Note moyenne</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">4.7</p>
            <p className="text-sm text-green-600">+0.2 ce mois</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Opportunités</h3>
            <p className="mt-2 text-2xl font-bold text-[#FF7A35]">2</p>
            <p className="text-sm text-gray-500">en attente</p>
          </div>
        </div>

        {/* Onglets */}
        <div className="flex space-x-4 border-b">
          <button
            onClick={() => setActiveTab('partners')}
            className={`pb-2 px-4 ${
              activeTab === 'partners'
                ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Partenaires
          </button>
          <button
            onClick={() => setActiveTab('opportunities')}
            className={`pb-2 px-4 ${
              activeTab === 'opportunities'
                ? 'border-b-2 border-[#FF7A35] text-[#FF7A35]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Opportunités
          </button>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'partners' ? (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Partenaire</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Collaborations</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Note</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Dernière activité</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockPartners.map((partner) => (
                  <tr key={partner.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{partner.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {partner.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        partner.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {partner.status === 'active' ? 'Actif' : 'En attente'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {partner.collaborations}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {partner.rating ? `${partner.rating}/5` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {partner.lastActivity || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Opportunité</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Partenaire</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valeur</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Échéance</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockOpportunities.map((opportunity) => (
                  <tr key={opportunity.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{opportunity.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {opportunity.partner}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {opportunity.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {opportunity.value ? `${opportunity.value}€` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {opportunity.deadline}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        opportunity.status === 'open'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {opportunity.status === 'open' ? 'Ouvert' : 'En attente'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Suggestions de partenariat */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Suggestions de partenariat</h2>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Point.P</h3>
                <span className="text-sm text-gray-500">Fournisseur</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Leader dans la distribution de matériaux de construction
              </p>
              <button className="w-full text-sm text-[#FF7A35] hover:text-[#ff6b2c] border border-[#FF7A35] rounded-md px-3 py-1">
                Contacter
              </button>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Brico Dépôt</h3>
                <span className="text-sm text-gray-500">Fournisseur</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Grande enseigne de bricolage et matériaux
              </p>
              <button className="w-full text-sm text-[#FF7A35] hover:text-[#ff6b2c] border border-[#FF7A35] rounded-md px-3 py-1">
                Contacter
              </button>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">Pierre Dubois</h3>
                <span className="text-sm text-gray-500">Artisan</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Expert en rénovation, 15 ans d'expérience
              </p>
              <button className="w-full text-sm text-[#FF7A35] hover:text-[#ff6b2c] border border-[#FF7A35] rounded-md px-3 py-1">
                Contacter
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkPage;