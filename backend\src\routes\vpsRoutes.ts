import { Router } from 'express';
import { vpsController } from '../controllers/vpsController';
import { authMiddleware } from '../middleware/authMiddleware';
import rateLimit from 'express-rate-limit';

// Rate limiter pour les routes VPS
const limiter_vps = rateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 100, // limite chaque IP à 100 requêtes par fenêtre
    standardHeaders: true,
    legacyHeaders: false
});

const router = Router();

// Toutes les routes VPS nécessitent une authentification
router.use(authMiddleware.authenticateToken);

// Routes VPS
router.get('/info', limiter_vps, vpsController.getVPSInfo);
router.get('/metrics', limiter_vps, vpsController.getVPSMetrics);
router.get('/history', limiter_vps, vpsController.getVPSHistory);
router.post('/restart', limiter_vps, vpsController.restartVPS);

export default router; 