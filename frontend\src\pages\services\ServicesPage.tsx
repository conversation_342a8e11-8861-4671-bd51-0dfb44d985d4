// Page principale des services avec recherche avancée - Version optimisée performance

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Chip,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Avatar,
  IconButton,
  useTheme,
  useMediaQuery,
  Stack,
  Tooltip,
  LinearProgress,
  Alert,
  Fade,
  Grow,
  Slide
} from '@mui/material';
import {
  Search,
  LocationOn,
  Category,
  TrendingUp,
  ArrowForward,
  Clear,
  Tune,
  LocalOffer,
  Speed,
  Verified,
  WorkOutline,
  EuroSymbol,
  KeyboardArrowDown,
  KeyboardArrowUp,
  ThumbUp,
  CheckCircle,
  Lightbulb,
  Rocket,
  AutoAwesome,
  Language,
  BusinessCenter,
  School,
  Build,
  Home,
  Pets,
  DirectionsCar,
  Restaurant,
  Palette,
  FitnessCenter,
  LocalFlorist,
  CleaningServices,
  HealthAndSafety,
  Psychology,
  TravelExplore,
  CameraAlt,
  TheaterComedy,
  EngineeringOutlined,
  DesignServicesOutlined,
  AccountBalanceOutlined,
  StorefrontOutlined,
  SpaOutlined,
  NatureOutlined,
  Favorite,
  BookmarkBorder,
  ViewModule,
  ViewList,
  Star,
  TrendingFlat,
  History,
  Autorenew,
  AccountCircle
} from '@mui/icons-material';
import { SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from '../dashboard/services/types';
import ModalPortal from '../../components/ModalPortal';
import { normalizeString, validateCity, isPostalCode, searchCitiesWithSuggestions } from '../../utils/geocoding';
import CitySearchInput from '../../components/services/CitySearchInput';
import { generateServicesPageSEO, updatePageMetadata, injectStructuredData, generateDetailedSchemas } from '../../utils/seoUtils';
import SEOHead from '../../components/services/SEOHead';

// Couleurs exactes de JobPartiel - Version optimisée
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  neutral: '#64748B', // Bleu gris
  white: '#FFFFFF',
  lightGray: '#F8FAFC',
  borderColor: 'rgba(255, 107, 44, 0.15)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowHover: 'rgba(0, 0, 0, 0.15)',
  glassBorder: 'rgba(255, 255, 255, 0.18)',
  gradient: {
    primary: 'linear-gradient(135deg, #FF6B2C 0%, #FF7A35 50%, #FF965E 100%)',
    secondary: 'linear-gradient(135deg, #FFE4BA 0%, #FFF8F3 100%)',
    glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)'
  }
};

const ServicesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // États de recherche
  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [selectedSubcategory, setSelectedSubcategory] = useState(searchParams.get('subcategory') || '');
  const [selectedCity, setSelectedCity] = useState(searchParams.get('city') || '');
  const [budgetRange, setBudgetRange] = useState(searchParams.get('budget') || '');

  // États UI simplifiés
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFavorites, setShowFavorites] = useState(false);
  const [showTrending, setShowTrending] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Forcer le mode grid sur mobile
  useEffect(() => {
    if (isMobile && viewMode === 'list') {
      setViewMode('grid');
    }
  }, [isMobile, viewMode]);

  // États pour le système de recherche de catégories
  const [categorySearchTerm, setCategorySearchTerm] = useState('');
  const [filteredCategories, setFilteredCategories] = useState(SERVICE_CATEGORIES);

  // États pour le système de recherche intelligent
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [trendingServices, setTrendingServices] = useState<string[]>([]);

  // États pour les suggestions de ville
  const [cityValidationError, setCityValidationError] = useState<string>('');

  // État pour la modal de sous-catégories
  const [subcategoryModalOpen, setSubcategoryModalOpen] = useState(false);
  const [selectedCategoryForModal, setSelectedCategoryForModal] = useState<string>('');

  // État simple pour l'animation d'entrée
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  // Données optimisées - villes principales françaises
  const popularCities = useMemo(() => [
    // Grandes métropoles
    { name: 'Paris', region: 'Île-de-France', trending: true },
    { name: 'Lyon', region: 'Auvergne-Rhône-Alpes', trending: true },
    { name: 'Marseille', region: 'Provence-Alpes-Côte d\'Azur', trending: true },
    { name: 'Toulouse', region: 'Occitanie', trending: true },
    { name: 'Nice', region: 'Provence-Alpes-Côte d\'Azur', trending: true },
    { name: 'Nantes', region: 'Pays de la Loire', trending: true },
    { name: 'Montpellier', region: 'Occitanie', trending: true },
    { name: 'Strasbourg', region: 'Grand Est', trending: true },
    { name: 'Bordeaux', region: 'Nouvelle-Aquitaine', trending: true },
    { name: 'Lille', region: 'Hauts-de-France', trending: true },

    // Villes importantes
    { name: 'Rennes', region: 'Bretagne', trending: true },
    { name: 'Reims', region: 'Grand Est', trending: false },
    { name: 'Grenoble', region: 'Auvergne-Rhône-Alpes', trending: true },
    { name: 'Dijon', region: 'Bourgogne-Franche-Comté', trending: false },
    { name: 'Angers', region: 'Pays de la Loire', trending: false },
    { name: 'Orléans', region: 'Centre-Val de Loire', trending: false },
    { name: 'Rouen', region: 'Normandie', trending: false },
    { name: 'Nancy', region: 'Grand Est', trending: false },
    { name: 'Metz', region: 'Grand Est', trending: false },
    { name: 'Clermont-Ferrand', region: 'Auvergne-Rhône-Alpes', trending: false },

    // Villes moyennes populaires
    { name: 'Tours', region: 'Centre-Val de Loire', trending: true },
    { name: 'Amiens', region: 'Hauts-de-France', trending: false },
    { name: 'Limoges', region: 'Nouvelle-Aquitaine', trending: false },
    { name: 'Villeurbanne', region: 'Auvergne-Rhône-Alpes', trending: false },
    { name: 'Besançon', region: 'Bourgogne-Franche-Comté', trending: false },
    { name: 'Caen', region: 'Normandie', trending: false },
    { name: 'Brest', region: 'Bretagne', trending: false },
    { name: 'Le Mans', region: 'Pays de la Loire', trending: false },
    { name: 'Poitiers', region: 'Nouvelle-Aquitaine', trending: false },
    { name: 'Toulon', region: 'Provence-Alpes-Côte d\'Azur', trending: false },

    // Banlieues parisiennes importantes
    { name: 'Versailles', region: 'Île-de-France', trending: false },
    { name: 'Boulogne-Billancourt', region: 'Île-de-France', trending: false },
    { name: 'Saint-Denis', region: 'Île-de-France', trending: false },
    { name: 'Argenteuil', region: 'Île-de-France', trending: false },
    { name: 'Montreuil', region: 'Île-de-France', trending: false },
    { name: 'Créteil', region: 'Île-de-France', trending: false },
    { name: 'Nanterre', region: 'Île-de-France', trending: false },
    { name: 'Courbevoie', region: 'Île-de-France', trending: false },
    { name: 'Colombes', region: 'Île-de-France', trending: false },
    { name: 'Asnières-sur-Seine', region: 'Île-de-France', trending: false },

    // Autres villes importantes
    { name: 'Perpignan', region: 'Occitanie', trending: false },
    { name: 'Mulhouse', region: 'Grand Est', trending: false },
    { name: 'Calais', region: 'Hauts-de-France', trending: false },
    { name: 'Dunkerque', region: 'Hauts-de-France', trending: false },
    { name: 'Bourges', region: 'Centre-Val de Loire', trending: false },
    { name: 'La Rochelle', region: 'Nouvelle-Aquitaine', trending: true },
    { name: 'Quimper', region: 'Bretagne', trending: false },
    { name: 'Vannes', region: 'Bretagne', trending: false },
    { name: 'Bayonne', region: 'Nouvelle-Aquitaine', trending: false },
    { name: 'Cannes', region: 'Provence-Alpes-Côte d\'Azur', trending: true },
    { name: 'Antibes', region: 'Provence-Alpes-Côte d\'Azur', trending: false },
    { name: 'Avignon', region: 'Provence-Alpes-Côte d\'Azur', trending: false }
  ], []);

  // Services populaires avec analytics avancés
  const popularServices = useMemo(() => {
    return SERVICE_SUBCATEGORIES
      .filter(service => service.synonymes && service.synonymes.length > 5)
      .map((service, index) => ({
        ...service,
        popularity: 100 - index * 5, // Valeur fixe décroissante
        growth: index % 3 === 0 ? 15 : index % 3 === 1 ? -5 : 10, // Valeurs fixes
        avgPrice: 20 + (index * 3), // Prix fixe croissant
        responseTime: 30 + (index * 10), // Temps fixe croissant
        rating: (4.5 - (index * 0.1)).toFixed(1) // Note fixe décroissante
      }))
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, 16);
  }, []);

  // Services tendances - système vraiment dynamique et aléatoire
  const trendingServicesData = useMemo(() => {
    // Fonction pour mélanger un tableau (Fisher-Yates shuffle)
    const shuffleArray = (array: any[]) => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };

    // 1. Sélectionner aléatoirement 3-5 catégories parmi toutes les catégories
    const allCategories = SERVICE_CATEGORIES.map(cat => cat.id);
    const shuffledCategories = shuffleArray(allCategories);
    const numberOfCategories = Math.floor(Math.random() * 3) + 3; // Entre 3 et 5 catégories
    const selectedCategories = shuffledCategories.slice(0, numberOfCategories);
       
    // 2. Récupérer tous les services de ces catégories
    const servicesInSelectedCategories = SERVICE_SUBCATEGORIES.filter(service => 
      selectedCategories.includes(service.categoryId)
    );
    
    // 3. Mélanger tous les services et en prendre un nombre aléatoire
    const shuffledServices = shuffleArray(servicesInSelectedCategories);
    const numberOfTrendingServices = Math.floor(Math.random() * 8) + 5; // Entre 5 et 12 services
    const selectedServices = shuffledServices.slice(0, numberOfTrendingServices);
    
    return {
      services: selectedServices,
      categories: selectedCategories
    };
  }, []); // Pas de dépendances pour que ça change à chaque refresh

  // Recherches populaires avec tendances
  const popularSearches = useMemo(() => {
    const searches: Array<{
      service: string;
      city: string;
      category: string;
      trend: 'up' | 'down' | 'stable';
      searchVolume: number;
    }> = [];

    const topServices = SERVICE_SUBCATEGORIES.slice(0, 8);
    const topCities = popularCities.slice(0, 6);

    topServices.forEach(service => {
      topCities.slice(0, 2).forEach(city => {
        const category = SERVICE_CATEGORIES.find(cat => cat.id === service.categoryId);
        const trends = ['up', 'down', 'stable'] as const;
        searches.push({
          service: service.nom,
          city: city.name,
          category: category?.nom || '',
          trend: trends[Math.floor(Math.random() * trends.length)],
          searchVolume: Math.floor(Math.random() * 1000) + 100
        });
      });
    });

    return searches
      .sort((a, b) => b.searchVolume - a.searchVolume)
      .slice(0, 12);
  }, [popularCities]);

  // Mapping des icônes par catégorie
  const categoryIcons = useMemo(() => ({
    '1': LocalFlorist, // Jardinage
    '2': Build, // Bricolage
    '3': Pets, // Garde d'animaux
    '4': HealthAndSafety, // Services à la personne
    '5': Restaurant, // Événementiel & Restauration
    '6': BusinessCenter, // Services administratifs
    '7': DirectionsCar, // Transport & Logistique
    '8': CameraAlt, // Communication & Marketing
    '9': School, // Éducation & Formation
    '10': Language, // Informatique
    '11': TheaterComedy, // Arts & Divertissement
    '12': SpaOutlined, // Bien-être & Santé
    '13': StorefrontOutlined, // Services aux entreprises
    '14': Palette, // Artisanat & Création
    '15': FitnessCenter, // Sport & Loisirs
    '16': Home, // Immobilier & Habitat
    '17': DirectionsCar, // Automobile & Transport
    '18': DesignServicesOutlined, // Décoration & Design
    '19': AccountBalanceOutlined, // Services financiers
    '20': TravelExplore, // Tourisme & Voyages
    '21': EngineeringOutlined, // Rénovation & Travaux
    '22': SpaOutlined, // Piscine & Spa
    '23': Psychology, // Mode & Beauté
    '24': CleaningServices, // Ménage & Nettoyage
    '25': NatureOutlined // Environnement & Écologie
  }), []);

  // Hooks et effets optimisés pour les performances
  useEffect(() => {
    // Optimisation SEO - Mettre à jour les métadonnées de la page
    const seoMetadata = generateServicesPageSEO();
    updatePageMetadata(seoMetadata);

    // Injecter les données structurées JSON-LD pour le SEO
    const structuredData = generateDetailedSchemas('services', 'france', []);
    injectStructuredData(structuredData.organization);

    // Animation d'entrée de page simple
    const timer = setTimeout(() => setIsPageLoaded(true), 100);

    // Simulation de chargement des données optimisée
    const loadData = async () => {
      setLoadingProgress(30);
      await new Promise(resolve => setTimeout(resolve, 100));
      setLoadingProgress(70);
      await new Promise(resolve => setTimeout(resolve, 50));
      setLoadingProgress(100);
      setTimeout(() => setIsLoading(false), 100);
    };

    loadData();

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Gestion intelligente des favoris
  const toggleFavorite = useCallback((serviceId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(serviceId)) {
        newFavorites.delete(serviceId);
      } else {
        newFavorites.add(serviceId);
      }
      // Sauvegarder dans localStorage avec une clé spécifique aux catégories
      localStorage.setItem('jobpartiel_category_favorites', JSON.stringify([...newFavorites]));
      return newFavorites;
    });
  }, []);

  // Fonction pour ajouter à l'historique de recherche
  const addToSearchHistory = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) return;

    setRecentSearches(prev => {
      const newRecent = [searchTerm, ...prev.filter(item => item !== searchTerm)].slice(0, 5);
      localStorage.setItem('jobpartiel_recent_searches', JSON.stringify(newRecent));
      return newRecent;
    });
  }, []);

  // Charger les favoris et historique depuis localStorage et initialiser les services tendances
  useEffect(() => {
    const savedFavorites = localStorage.getItem('jobpartiel_category_favorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }

    // Charger les recherches récentes
    const savedRecent = localStorage.getItem('jobpartiel_recent_searches');
    if (savedRecent) {
      setRecentSearches(JSON.parse(savedRecent));
    }

    // Initialiser les services tendances avec les plus populaires
    const trending = trendingServicesData.services.map(service => service.nom);
    setTrendingServices(trending);
  }, [trendingServicesData, SERVICE_CATEGORIES]);

  // Fonction de filtrage des catégories
  const handleCategorySearch = useCallback((searchTerm: string) => {
    setCategorySearchTerm(searchTerm);

    if (!searchTerm.trim()) {
      setFilteredCategories(SERVICE_CATEGORIES);
      return;
    }

    const filtered = SERVICE_CATEGORIES.filter(category => {
      const categoryMatch = category.nom.toLowerCase().includes(searchTerm.toLowerCase());
      const subcategoryMatch = getSubcategoriesForCategory(category.id).some(sub =>
        sub.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (sub.synonymes && sub.synonymes.some(syn => syn.toLowerCase().includes(searchTerm.toLowerCase())))
      );
      return categoryMatch || subcategoryMatch;
    });

    setFilteredCategories(filtered);
  }, []);

  // Mettre à jour les catégories filtrées quand les favoris changent
  useEffect(() => {
    if (showFavorites) {
      const favoritesFiltered = SERVICE_CATEGORIES.filter(category => favorites.has(category.id));
      if (categorySearchTerm.trim()) {
        // Appliquer aussi le filtre de recherche sur les favoris
        const searchFiltered = favoritesFiltered.filter(category => {
          const categoryMatch = category.nom.toLowerCase().includes(categorySearchTerm.toLowerCase());
          const subcategoryMatch = getSubcategoriesForCategory(category.id).some(sub =>
            sub.nom.toLowerCase().includes(categorySearchTerm.toLowerCase()) ||
            (sub.synonymes && sub.synonymes.some(syn => syn.toLowerCase().includes(categorySearchTerm.toLowerCase())))
          );
          return categoryMatch || subcategoryMatch;
        });
        setFilteredCategories(searchFiltered);
      } else {
        setFilteredCategories(favoritesFiltered);
      }
    } else if (showTrending) {
      // Filtrer par catégories tendances (sélectionnées dynamiquement)
      const trendingFiltered = SERVICE_CATEGORIES.filter(category => 
        trendingServicesData.categories.includes(category.id)
      );
      if (categorySearchTerm.trim()) {
        // Appliquer aussi le filtre de recherche sur les tendances
        const searchFiltered = trendingFiltered.filter(category => {
          const categoryMatch = category.nom.toLowerCase().includes(categorySearchTerm.toLowerCase());
          const subcategoryMatch = getSubcategoriesForCategory(category.id).some(sub =>
            sub.nom.toLowerCase().includes(categorySearchTerm.toLowerCase()) ||
            (sub.synonymes && sub.synonymes.some(syn => syn.toLowerCase().includes(categorySearchTerm.toLowerCase())))
          );
          return categoryMatch || subcategoryMatch;
        });
        setFilteredCategories(searchFiltered);
      } else {
        setFilteredCategories(trendingFiltered);
      }
    } else if (!categorySearchTerm.trim()) {
      setFilteredCategories(SERVICE_CATEGORIES);
    } else {
      handleCategorySearch(categorySearchTerm);
    }
  }, [showFavorites, showTrending, favorites, categorySearchTerm, handleCategorySearch, trendingServicesData]);

  // Obtenir toutes les sous-catégories pour la catégorie sélectionnée
  const getSubcategoriesForCategory = (categoryId: string) => {
    if (!categoryId) return [];
    return SERVICE_SUBCATEGORIES.filter(sub => sub.categoryId === categoryId);
  };

  // Fonction de normalisation du texte pour la recherche (importée depuis geocoding.ts)
  // Supprimée car maintenant importée

  // Fonction pour valider la ville ou code postal saisie
  const validateCityInput = async (cityName: string) => {
    if (!cityName.trim()) {
      setCityValidationError('');
      return true;
    }

    try {
      const trimmedInput = cityName.trim();

      // Vérifier d'abord si c'est un code postal
      if (isPostalCode(trimmedInput)) {
        // Si c'est un code postal, rechercher directement
        const postalResults = await searchCitiesWithSuggestions(trimmedInput, 1);
        if (postalResults.length > 0) {
          setCityValidationError('');
          return true;
        } else {
          setCityValidationError('Code postal non trouvé. Veuillez vérifier.');
          return false;
        }
      }

      // Sinon, validation normale par nom de ville
      const validCity = await validateCity(trimmedInput);
      if (validCity) {
        setCityValidationError('');
        return true;
      } else {
        setCityValidationError('Ville non trouvée. Veuillez vérifier l\'orthographe.');
        return false;
      }
    } catch (error) {
      console.error('Erreur lors de la validation de ville:', error);
      setCityValidationError('Erreur lors de la validation de la ville.');
      return false;
    }
  };

  // Fonction de recherche simple et fluide comme dans PostMission
  const handleSimpleSearch = (query: string) => {
    const searchTerm = query.toLowerCase().trim();

    if (!searchTerm) {
      setSearchSuggestions([]);
      return;
    }

    // Recherche dans les sous-catégories avec score de pertinence (comme PostMission)
    const matchingSubcategories = SERVICE_SUBCATEGORIES
      .map(sub => ({
        subcategory: sub,
        relevance: calculateRelevance(searchTerm, sub)
      }))
      .filter(({ relevance }) => relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 5) // Limiter à 5 suggestions
      .map(({ subcategory }) => subcategory.nom);

    setSearchSuggestions(matchingSubcategories);
  };

  // Fonction de calcul de pertinence (copiée de PostMission)
  const calculateRelevance = (searchTerm: string, subcategory: any): number => {
    const normalizedSearch = normalizeString(searchTerm);
    const normalizedName = normalizeString(subcategory.nom);

    // Score de base
    let score = 0;

    // Vérification des correspondances partielles dans le nom
    if (normalizedName.includes(normalizedSearch)) {
      // Correspondance exacte du nom
      if (normalizedName === normalizedSearch) {
        score += 100;
      }
      // Le nom commence par le terme de recherche
      else if (normalizedName.startsWith(normalizedSearch)) {
        score += 80;
      }
      // Le terme de recherche est contenu dans le nom
      else {
        score += 60;
      }
    }

    // Vérification des synonymes
    if (subcategory.synonymes) {
      for (const syn of subcategory.synonymes) {
        const normalizedSyn = normalizeString(syn);

        // Correspondance partielle dans les synonymes
        if (normalizedSyn.includes(normalizedSearch)) {
          // Correspondance exacte du synonyme
          if (normalizedSyn === normalizedSearch) {
            score += 100;
          }
          // Le synonyme commence par le terme de recherche
          else if (normalizedSyn.startsWith(normalizedSearch)) {
            score += 80;
          }
          // Le terme de recherche est contenu dans le synonyme
          else {
            score += 60;
          }
          // On prend le meilleur score parmi les synonymes
          break;
        }
      }
    }

    return score;
  };

  const handleSearch = useCallback(async (customSearchTerm?: string, customCategory?: string, customSubcategory?: string, includeSearchTerm: boolean = true) => {
    setIsLoading(true);
    setLoadingProgress(0);

    // Valider la ville si elle est fournie
    if (selectedCity.trim()) {
      const isCityValid = await validateCityInput(selectedCity.trim());
      if (!isCityValid) {
        setIsLoading(false);
        return; // Ne pas effectuer la recherche si la ville n'est pas valide
      }
    }

    // Utiliser les paramètres personnalisés ou les états actuels
    const searchTermToUse = customSearchTerm !== undefined ? customSearchTerm : searchTerm;
    let categoryToUse = customCategory !== undefined ? customCategory : selectedCategory;
    let subcategoryToUse = customSubcategory !== undefined ? customSubcategory : selectedSubcategory;

    // Si on a un terme de recherche mais pas de sous-catégorie, essayer de trouver la meilleure correspondance
    if (searchTermToUse && !subcategoryToUse) {
      const matchingSubcategories = SERVICE_SUBCATEGORIES
        .map(sub => ({
          subcategory: sub,
          relevance: calculateRelevance(searchTermToUse, sub)
        }))
        .filter(({ relevance }) => relevance > 0)
        .sort((a, b) => b.relevance - a.relevance);

      if (matchingSubcategories.length > 0) {
        const bestMatch = matchingSubcategories[0].subcategory;
        subcategoryToUse = bestMatch.id;
        categoryToUse = bestMatch.categoryId;

        // Mettre à jour les états pour la cohérence
        setSelectedCategory(bestMatch.categoryId);
        setSelectedSubcategory(bestMatch.id);
      }
    }

    // Animation de progression du chargement
    const progressSteps = [20, 40, 60, 80, 100];
    let currentStep = 0;

    const updateProgress = () => {
      if (currentStep < progressSteps.length) {
        setLoadingProgress(progressSteps[currentStep]);
        currentStep++;
        setTimeout(updateProgress, 150);
      }
    };

    updateProgress();

    // Construire l'URL de recherche basée sur les filtres avec noms au lieu d'IDs
    const params = new URLSearchParams();

    // Ajouter le terme de recherche seulement si on n'a pas de sous-catégorie spécifique
    if (includeSearchTerm && searchTermToUse && !subcategoryToUse) {
      params.append('q', searchTermToUse);
    }

    // Ajouter aux recherches récentes si on a un terme de recherche
    if (searchTermToUse) {
      setRecentSearches(prev => {
        const newSearches = [searchTermToUse, ...prev.filter(s => s !== searchTermToUse)];
        return newSearches.slice(0, 5);
      });
    }

    // Utiliser les noms des catégories au lieu des IDs
    if (categoryToUse) {
      const category = SERVICE_CATEGORIES.find(cat => cat.id === categoryToUse);
      if (category) params.append('category', category.nom);
    }

    if (subcategoryToUse) {
      const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === subcategoryToUse);
      if (subcategory) params.append('subcategory', subcategory.nom);
    }

    if (selectedCity) params.append('city', selectedCity);
    if (budgetRange) params.append('budget', budgetRange);

    // Simulation d'un délai de recherche pour l'UX avec animation fluide
    setTimeout(() => {
      setIsLoading(false);
      setLoadingProgress(100);

      // Si on a une sous-catégorie spécifique et une ville, rediriger vers la page dédiée
      if (subcategoryToUse && selectedCity) {
        const subcategory = SERVICE_SUBCATEGORIES.find(sub => sub.id === subcategoryToUse);
        if (subcategory) {
          // Créer un slug pour le service et la ville
          const serviceSlug = subcategory.nom.toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-');

          const citySlug = selectedCity.toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-');

          navigate(`/services/${serviceSlug}/${citySlug}`);
          return;
        }
      }

      // Sinon, rediriger vers la page de résultats de recherche
      navigate(`/services/search?${params.toString()}`);
    }, 1000);
  }, [searchTerm, selectedCategory, selectedSubcategory, selectedCity, budgetRange, navigate, calculateRelevance]);

  const handleQuickSearch = (service: string, city?: string) => {
    setSearchTerm(service);
    if (city) setSelectedCity(city);

    // Construire l'URL de recherche directement
    const params = new URLSearchParams();
    if (city) params.append('city', city);

    // Trouver la catégorie du service pour ajouter les filtres
    const serviceObj = SERVICE_SUBCATEGORIES.find(s => s.nom === service);
    if (serviceObj) {
      setSelectedCategory(serviceObj.categoryId);
      setSelectedSubcategory(serviceObj.id);

      const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === serviceObj.categoryId);
      if (categoryObj) params.append('category', categoryObj.nom);
      params.append('subcategory', serviceObj.nom);
    }

    // Naviguer vers la page de recherche
    navigate(`/services/search?${params.toString()}`);
  };

  const clearFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedSubcategory('');
    setSelectedCity('');
    setBudgetRange('');
    setSearchSuggestions([]);
  }, []);

  // Fonction pour gérer le clic sur un service
  const handleServiceClick = (subcategory: any) => {
    // Mettre à jour les états avec le service sélectionné
    setSearchTerm(subcategory.nom);
    setSelectedCategory(subcategory.categoryId);
    setSelectedSubcategory(subcategory.id);

    // Lancer la recherche immédiatement avec les bonnes valeurs
    handleSearch(subcategory.nom, subcategory.categoryId, subcategory.id, false);
  };

  // Générer les métadonnées SEO
  const seoMetadata = generateServicesPageSEO();

  return (
    <>
      <SEOHead
        title={seoMetadata.title}
        description={seoMetadata.description}
        keywords={seoMetadata.keywords}
        canonical={seoMetadata.canonical}
        ogImage={seoMetadata.ogImage}
        ogType={seoMetadata.ogType}
        ogLocale={seoMetadata.ogLocale}
        twitterCard={seoMetadata.twitterCard}
        twitterImage={seoMetadata.twitterImage}
        structuredData={generateDetailedSchemas('services', 'france', []).organization}
      />
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #FFE4BA 0%, #FFFFFF 50%, #FFE4BA 100%)',
        position: 'relative',
        overflow: 'hidden',
        paddingTop: { xs: '80px', md: '100px', lg: '120px' } // Espace pour le header fixe
      }}>
      {/* Styles d'animation globaux améliorés */}
      <style>
        {`
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
          }
        `}
      </style>

      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 1, py: { xs: 2, md: 4 } }}>
        {/* Barre de progression de chargement */}
        {isLoading && (
          <Fade in={isLoading}>
            <Box
              sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                zIndex: 9999,
                background: COLORS.white,
                boxShadow: `0 4px 20px ${COLORS.shadow}`
              }}
            >
              <LinearProgress
                variant="determinate"
                value={loadingProgress}
                sx={{
                  height: 4,
                  background: COLORS.lightGray,
                  '& .MuiLinearProgress-bar': {
                    background: COLORS.gradient.primary,
                    borderRadius: '2px'
                  }
                }}
              />
            </Box>
          </Fade>
        )}

        {/* Hero Section Optimisé */}
        <Fade in={isPageLoaded} timeout={800}>
          <div>
            <Box textAlign="center" sx={{ mb: { xs: 6, md: 10 }, pt: { xs: 4, md: 6 } }}>
            <Box sx={{ position: 'relative', display: 'inline-block' }}>
              <Typography
                variant={isMobile ? "h3" : "h1"}
                component="h1"
                sx={{
                  fontSize: { xs: '2.8rem', md: '4.5rem', lg: '5.5rem' },
                  fontWeight: 900,
                  background: COLORS.gradient.primary,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 2,
                  position: 'relative',
                  letterSpacing: '-0.02em',
                  lineHeight: 1.1,
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: COLORS.gradient.primary,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    filter: 'blur(20px)',
                    opacity: 0.3,
                    zIndex: -1
                  }
                }}
              >
                Trouvez le service
                <br />
                <Box component="span" sx={{
                  position: 'relative',
                  color: COLORS.primary, // Rendre le texte blanc pour qu'il soit visible
                  WebkitTextFillColor: COLORS.primary, // Assurer la visibilité pour les navigateurs Webkit
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: '-8px',
                    left: 0,
                    right: 0,
                    height: '8px',
                    background: `linear-gradient(90deg, ${COLORS.accent}80, ${COLORS.primary}40)`,
                    borderRadius: '4px',
                    animation: 'shimmer 2s ease-in-out infinite'
                  }
                }}>
                  parfait
                </Box>
              </Typography>

              {/* Étoiles décoratives optimisées */}
              {!isMobile && [...Array(2)].map((_, i) => (
                <Box
                  key={i}
                  sx={{
                    position: 'absolute',
                    top: `${25 + i * 40}%`,
                    right: `${-10 - i * 20}%`,
                    fontSize: '20px',
                    color: COLORS.accent,
                    zIndex: 1,
                    animation: `float ${4 + i * 2}s ease-in-out infinite`,
                    animationDelay: `${i * 1}s`
                  }}
                >
                  ✨
                </Box>
              ))}
            </Box>

            <Typography
              variant={isMobile ? "h6" : "h4"}
              sx={{
                color: COLORS.neutral,
                mb: 6,
                fontSize: { xs: '1.2rem', md: '1.4rem' },
                fontWeight: 500,
                maxWidth: '700px',
                mx: 'auto',
                lineHeight: 1.6,
                position: 'relative',
                '&::before': {
                  content: '"🚀"',
                  position: 'absolute',
                  left: '-40px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '24px',
                  display: { xs: 'none', md: 'block' }
                },
                '&::after': {
                  content: '"⭐"',
                  position: 'absolute',
                  right: '-40px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '24px',
                  display: { xs: 'none', md: 'block' }
                }
              }}
            >
              Découvrez des professionnels qualifiés près de chez vous
              <br />
              <Box component="span" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                pour tous vos besoins du quotidien
              </Box>
            </Typography>

            {/* Stats de confiance améliorées */}
            <Stack
              direction={isMobile ? "column" : "row"}
              spacing={isMobile ? 2 : 4}
              justifyContent="center"
              sx={{ mb: 6 }}
            >
              {[
                // Nouvelle carte : Échange par Jobi
                {
                  icon: <Autorenew />,
                  value: "Jobi",
                  label: "Échange par Jobi",
                  color: COLORS.primary, // Couleur principale
                  description: "Utilisez notre système de troc pour échanger des services"
                },
                // Nouvelle carte : Services Disponibles
                {
                  icon: <BusinessCenter />,
                  value: "500+",
                  label: "Services Disponibles",
                  color: COLORS.secondary, // Couleur secondaire
                  description: "Accédez à une large variété de services près de chez vous"
                },
                // Nouvelle carte : Gestion de Profil Complète
                {
                  icon: <AccountCircle />,
                  value: "Votre Profil",
                  label: "Gestion de Profil Complète",
                  color: COLORS.tertiary, // Couleur tertiaire
                  description: "Gérez vos informations, documents et historique en toute sécurité"
                }
              ].map((stat, index) => (
                <Paper
                  key={index}
                  elevation={0}
                  sx={{
                    p: 3,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    bgcolor: COLORS.white,
                    border: `2px solid ${COLORS.borderColor}`,
                    borderRadius: 3,
                    minWidth: isMobile ? 'auto' : '200px',
                    position: 'relative',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: stat.color,
                      boxShadow: `0 8px 30px ${stat.color}20`,
                      transform: 'translateY(-4px)'
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '4px',
                          background: `linear-gradient(90deg, ${stat.color} 0%, ${stat.color}80 100%)`,
                        }
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: `${stat.color}15`,
                          color: stat.color,
                          width: 48,
                          height: 48
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h5" sx={{ fontWeight: 800, color: stat.color, mb: 0.5 }}>
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
                          {stat.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                          {stat.description}
                        </Typography>
                      </Box>
                    </Paper>
                ))}
              </Stack>
          </Box>

          {/* Barre de recherche principale révolutionnaire */}
          <Box>
            <Paper
              elevation={0}
              sx={{
                p: { xs: 3, md: 4 },
                mb: 6,
                background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                border: `3px solid ${COLORS.borderColor}`,
                borderRadius: 5,
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                overflow: 'visible', // Permettre aux suggestions de déborder
                boxShadow: `0 8px 30px rgba(0, 0, 0, 0.08)`,
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '5px',
                  background: `linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
                  opacity: 0,
                  transition: 'opacity 0.4s ease'
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: `radial-gradient(circle at top right, ${COLORS.accent}10 0%, transparent 50%)`,
                  opacity: 0,
                  transition: 'opacity 0.4s ease',
                  pointerEvents: 'none'
                }
              }}
            >
              <Stack spacing={3}>
                {/* Recherche principale */}
                <Stack direction={isMobile ? "column" : "row"} spacing={2}>
                  <Box sx={{ flex: 1, position: 'relative' }}>
                    <TextField
                      fullWidth
                      placeholder="Que recherchez-vous ? (ex: plombier, électricien, jardinier...)"
                      value={searchTerm}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSearchTerm(value); // Mise à jour immédiate pour la fluidité
                        handleSimpleSearch(value); // Recherche de suggestions simple et fluide
                      }}
                      onFocus={() => setIsSearchFocused(true)}
                      onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch();
                        }
                        if (e.key === 'Escape') {
                          setSearchTerm('');
                          setSearchSuggestions([]);
                        }
                      }}
                      slotProps={{
                        input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search sx={{
                              color: isSearchFocused ? COLORS.primary : 'text.secondary',
                              transition: 'color 0.2s ease'
                            }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <Stack direction="row" spacing={0.5}>
                              {searchTerm && (
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setSearchTerm('');
                                    clearFilters();
                                  }}
                                  sx={{
                                    color: 'text.secondary',
                                    transition: 'all 0.2s ease',
                                    '&:hover': {
                                      color: COLORS.primary,
                                      bgcolor: `${COLORS.primary}10`,
                                      transform: 'scale(1.1)'
                                    }
                                  }}
                                >
                                  <Clear />
                                </IconButton>
                              )}
                              {searchSuggestions.length > 0 && (
                                <Tooltip title="Suggestions disponibles">
                                  <IconButton
                                    size="small"
                                    sx={{
                                      color: COLORS.secondary,
                                      animation: 'pulse 2s infinite',
                                      transition: 'transform 0.2s ease',
                                      '&:hover': {
                                        transform: 'scale(1.1)'
                                      }
                                    }}
                                  >
                                    <AutoAwesome />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Stack>
                          </InputAdornment>
                        )
                        }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          bgcolor: COLORS.white,
                          border: `2px solid ${COLORS.borderColor}`,
                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            borderColor: COLORS.primary,
                            transform: 'translateY(-1px)',
                            boxShadow: `0 4px 15px ${COLORS.primary}20`,
                          },
                          '&.Mui-focused': {
                            borderColor: COLORS.primary,
                            boxShadow: `0 0 0 3px ${COLORS.primary}20, 0 6px 20px ${COLORS.primary}15`,
                            transform: 'translateY(-2px)',
                          },
                          '& fieldset': {
                            border: 'none',
                          },
                        },
                        '& .MuiInputBase-input': {
                          fontSize: '1.1rem',
                          fontWeight: 500,
                          py: 2,
                          position: 'relative',
                          zIndex: 2
                        }
                      }}
                    />

                    {/* Suggestions intelligentes */}
                    {isSearchFocused && searchSuggestions.length > 0 && (
                      <Fade in={isSearchFocused && searchSuggestions.length > 0}>
                        <Box
                          sx={{
                            position: 'absolute',
                            top: '100%',
                            left: 0,
                            right: 0,
                            zIndex: 9999, // Z-index très élevé
                            marginTop: '4px'
                          }}
                        >
                          <Paper
                            elevation={0}
                            sx={{
                              border: `1px solid ${COLORS.borderColor}`,
                              borderRadius: 3,
                              overflow: 'hidden',
                              background: COLORS.white,
                              boxShadow: `0 8px 30px ${COLORS.shadow}`
                            }}
                          >
                            <Box sx={{ p: 2 }}>
                              <Typography variant="caption" sx={{
                                color: COLORS.neutral,
                                fontWeight: 600,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                                mb: 1
                              }}>
                                <AutoAwesome sx={{ fontSize: 14 }} />
                                Suggestions
                              </Typography>
                              <Stack spacing={0.5}>
                                {searchSuggestions.map((suggestion) => (
                                  <Box
                                    key={suggestion}
                                    onClick={() => {
                                      setSearchTerm(suggestion);
                                      setIsSearchFocused(false);
                                      addToSearchHistory(suggestion);
                                      setSearchSuggestions([]); // Effacer les suggestions
                                    }}
                                    sx={{
                                      p: 1.5,
                                      borderRadius: 2,
                                      cursor: 'pointer',
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 1,
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        bgcolor: `${COLORS.primary}08`,
                                        color: COLORS.primary,
                                        transform: 'translateX(4px)'
                                      }
                                    }}
                                  >
                                    <Search sx={{ fontSize: 16, opacity: 0.6 }} />
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                      {suggestion}
                                    </Typography>
                                  </Box>
                                ))}
                              </Stack>

                              {/* Historique de recherche */}
                              {recentSearches.length > 0 && (
                                <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${COLORS.borderColor}` }}>
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      fontWeight: 600,
                                      color: COLORS.neutral,
                                      textTransform: 'uppercase',
                                      letterSpacing: 0.5,
                                      mb: 1,
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 0.5
                                    }}
                                  >
                                    <History sx={{ fontSize: 14 }} />
                                    Recherches récentes
                                  </Typography>
                                  <Stack spacing={0.5}>
                                    {recentSearches.map((search) => (
                                      <Box
                                        key={search}
                                        onClick={() => {
                                          setSearchTerm(search);
                                          setIsSearchFocused(false);
                                          addToSearchHistory(search);
                                          setSearchSuggestions([]); // Effacer les suggestions
                                        }}
                                        sx={{
                                          p: 1.5,
                                          borderRadius: 2,
                                          cursor: 'pointer',
                                          display: 'flex',
                                          alignItems: 'center',
                                          gap: 1,
                                          transition: 'all 0.2s ease',
                                          '&:hover': {
                                            bgcolor: `${COLORS.secondary}08`,
                                            color: COLORS.secondary,
                                            transform: 'translateX(4px)'
                                          }
                                        }}
                                      >
                                        <History sx={{ fontSize: 16, opacity: 0.6 }} />
                                        <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.85rem' }}>
                                          {search}
                                        </Typography>
                                      </Box>
                                    ))}
                                  </Stack>
                                </Box>
                              )}
                            </Box>
                          </Paper>
                        </Box>
                      </Fade>
                    )}
                  </Box>

                  <Box sx={{ flex: isMobile ? 1 : 0.7, position: 'relative' }}>
                    <CitySearchInput
                      value={selectedCity}
                      onChange={setSelectedCity}
                      placeholder="Ville (ex: Paris, Lyon...)"
                      onValidationChange={(isValid) => {
                        if (!isValid) {
                          setCityValidationError('Ville non trouvée. Veuillez vérifier l\'orthographe.');
                        } else {
                          setCityValidationError('');
                        }
                      }}
                      error={cityValidationError}
                      size="medium"
                      fullWidth
                    />

                    {/* Message d'erreur de validation de ville */}
                    {cityValidationError && (
                      <Box sx={{ mt: 1 }}>
                        <Alert
                          severity="warning"
                          sx={{
                            borderRadius: 2,
                            fontSize: '0.85rem',
                            py: 0.5
                          }}
                        >
                          {cityValidationError}
                        </Alert>
                      </Box>
                    )}
                  </Box>

                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => handleSearch()}
                    disabled={isLoading}
                    endIcon={isLoading ? null : <ArrowForward />}
                    sx={{
                      minWidth: isMobile ? '100%' : '180px',
                      height: '56px',
                      borderRadius: 4,
                      fontSize: '1.1rem',
                      fontWeight: 700,
                      background: isLoading
                        ? `linear-gradient(135deg, ${COLORS.neutral} 0%, ${COLORS.neutral} 100%)`
                        : `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                      boxShadow: isLoading
                        ? 'none'
                        : `0 6px 25px ${COLORS.primary}40`,
                      position: 'relative',
                      overflow: 'hidden',
                      '&:hover': {
                        background: isLoading
                          ? `linear-gradient(135deg, ${COLORS.neutral} 0%, ${COLORS.neutral} 100%)`
                          : `linear-gradient(135deg, ${COLORS.secondary} 0%, ${COLORS.tertiary} 100%)`,
                        boxShadow: isLoading
                          ? 'none'
                          : `0 8px 30px ${COLORS.primary}50`,
                        transform: isLoading ? 'none' : 'translateY(-2px)',
                      },
                      '&:disabled': {
                        color: COLORS.white
                      },
                      transition: 'all 0.3s ease',
                      '&::before': isLoading ? {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: '-100%',
                        width: '100%',
                        height: '100%',
                        background: `linear-gradient(90deg, transparent, ${COLORS.white}30, transparent)`,
                        animation: 'shimmer 1.5s infinite'
                      } : {}
                    }}
                  >
                    {isLoading ? 'Recherche...' : 'Rechercher'}
                  </Button>

                  {/* Bouton de nettoyage des filtres */}
                  {(searchTerm || selectedCategory || selectedCity || budgetRange) && (
                    <Button
                      variant="outlined"
                      size="large"
                      onClick={clearFilters}
                      startIcon={<Clear />}
                      sx={{
                        minWidth: isMobile ? '100%' : '140px',
                        height: '56px',
                        borderRadius: 4,
                        fontSize: '1rem',
                        fontWeight: 600,
                        borderColor: COLORS.borderColor,
                        color: COLORS.neutral,
                        '&:hover': {
                          borderColor: COLORS.primary,
                          color: COLORS.primary,
                          bgcolor: `${COLORS.primary}08`
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      Effacer
                    </Button>
                  )}
                </Stack>

                {/* Filtres avancés améliorés */}
                <Box sx={{ position: 'relative', zIndex: 2 }}>
                  <Button
                    startIcon={showAdvancedFilters ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                    endIcon={<Tune />}
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    sx={{
                      color: showAdvancedFilters ? COLORS.white : COLORS.primary,
                      fontWeight: 600,
                      fontSize: '1rem',
                      px: 3,
                      py: 1.5,
                      borderRadius: 3,
                      background: showAdvancedFilters
                        ? `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`
                        : 'transparent',
                      border: `2px solid ${showAdvancedFilters ? 'transparent' : COLORS.borderColor}`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: showAdvancedFilters
                          ? `linear-gradient(135deg, ${COLORS.secondary} 0%, ${COLORS.tertiary} 100%)`
                          : `${COLORS.primary}10`,
                        borderColor: showAdvancedFilters ? 'transparent' : COLORS.primary,
                        transform: 'translateY(-1px) scale(1.02)',
                        boxShadow: showAdvancedFilters
                          ? `0 6px 20px ${COLORS.primary}30`
                          : `0 4px 15px ${COLORS.primary}20`
                      }
                    }}
                  >
                    Filtres avancés
                  </Button>

                  {showAdvancedFilters && (
                      <Slide direction="down" in={showAdvancedFilters} mountOnEnter unmountOnExit>
                        <Box sx={{ overflow: 'hidden' }}>
                        <Grid container spacing={3} sx={{ mt: 1 }}>
                          <Grid size={{ xs: 12, md: 4 }}>
                            <FormControl fullWidth>
                              <InputLabel>Catégorie</InputLabel>
                              <Select
                                value={selectedCategory}
                                label="Catégorie"
                                onChange={(e) => {
                                  setSelectedCategory(e.target.value);
                                  setSelectedSubcategory('');
                                }}
                                sx={{
                                  borderRadius: 2,
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.borderColor,
                                  },
                                  '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.secondary,
                                  },
                                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.primary,
                                  },
                                }}
                              >
                                <MenuItem value="">Toutes les catégories</MenuItem>
                                {SERVICE_CATEGORIES
                                  .slice()
                                  .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                                  .map((category) => (
                                  <MenuItem key={category.id} value={category.id}>
                                    {category.nom}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>

                          <Grid size={{ xs: 12, md: 4 }}>
                            <FormControl fullWidth>
                              <InputLabel>Service spécifique</InputLabel>
                              <Select
                                value={selectedSubcategory}
                                label="Service spécifique"
                                onChange={(e) => setSelectedSubcategory(e.target.value)}
                                disabled={!selectedCategory}
                                sx={{
                                  borderRadius: 2,
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.borderColor,
                                  },
                                  '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.secondary,
                                  },
                                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.primary,
                                  },
                                }}
                              >
                                <MenuItem value="">Tous les services</MenuItem>
                                {getSubcategoriesForCategory(selectedCategory)
                                  .slice()
                                  .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                                  .map((subcategory) => (
                                  <MenuItem key={subcategory.id} value={subcategory.id}>
                                    {subcategory.nom}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>

                          <Grid size={{ xs: 12, md: 4 }}>
                            <FormControl fullWidth>
                              <InputLabel>Budget horaire</InputLabel>
                              <Select
                                value={budgetRange}
                                label="Budget horaire"
                                onChange={(e) => setBudgetRange(e.target.value)}
                                sx={{
                                  borderRadius: 2,
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.borderColor,
                                  },
                                  '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.secondary,
                                  },
                                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    borderColor: COLORS.primary,
                                  },
                                }}
                              >
                                <MenuItem value="">Tous les budgets</MenuItem>
                                <MenuItem value="0-15">
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <EuroSymbol fontSize="small" />
                                    <span>0€ - 15€/h</span>
                                  </Stack>
                                </MenuItem>
                                <MenuItem value="15-25">
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <EuroSymbol fontSize="small" />
                                    <span>15€ - 25€/h</span>
                                  </Stack>
                                </MenuItem>
                                <MenuItem value="25-35">
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <EuroSymbol fontSize="small" />
                                    <span>25€ - 35€/h</span>
                                  </Stack>
                                </MenuItem>
                                <MenuItem value="35-50">
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <EuroSymbol fontSize="small" />
                                    <span>35€ - 50€/h</span>
                                  </Stack>
                                </MenuItem>
                                <MenuItem value="50+">
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <EuroSymbol fontSize="small" />
                                    <span>50€+/h</span>
                                  </Stack>
                                </MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                        </Grid>
                        </Box>
                      </Slide>
                    )}
                </Box>


              </Stack>
            </Paper>
          </Box>
          </div>
        </Fade>

        {/* Section des catégories de services avec design premium */}
        <Box sx={{ mb: 10, px: { xs: 2, md: 4 } }}>
          <Box textAlign="center" sx={{ mb: 8 }}>
            <Grow in={isPageLoaded} timeout={1000}>
              <Typography
                variant={isMobile ? "h3" : "h2"}
                component="h2"
                sx={{
                  fontWeight: 800,
                  background: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 3,
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -10,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '80px',
                    height: '4px',
                    background: `linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                    borderRadius: '2px'
                  }
                }}
              >
                Explorez nos services
              </Typography>
            </Grow>

            <Fade in={isPageLoaded} timeout={1200}>
              <Typography
                variant={isMobile ? "body1" : "h5"}
                color="text.secondary"
                sx={{
                  maxWidth: '700px',
                  mx: 'auto',
                  lineHeight: 1.6,
                  fontWeight: 400
                }}
              >
                Découvrez notre écosystème complet de services professionnels et trouvez le jobbeur parfait pour vos besoins
              </Typography>
            </Fade>
          </Box>

          {/* Barre d'outils révolutionnaire avec recherche */}
            <Paper
              elevation={0}
              sx={{
                p: 3,
                mb: 4,
                mx: { xs: 0, md: 2 },
                background: COLORS.gradient.glass,
                backdropFilter: 'blur(20px)',
                border: `1px solid ${COLORS.glassBorder}`,
                borderRadius: 4
              }}
            >
              <Stack spacing={3}>
                {/* Première ligne : Recherche de catégories */}
                <Box>
                  <TextField
                    fullWidth
                    placeholder="Rechercher une catégorie ou un service..."
                    value={categorySearchTerm}
                    onChange={(e) => handleCategorySearch(e.target.value)}
                    size="small"
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <Search sx={{ color: COLORS.primary }} />
                          </InputAdornment>
                        ),
                        endAdornment: categorySearchTerm && (
                          <InputAdornment position="end">
                            <IconButton
                              size="small"
                              onClick={() => handleCategorySearch('')}
                              sx={{ color: 'text.secondary' }}
                            >
                              <Clear />
                            </IconButton>
                          </InputAdornment>
                        )
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: COLORS.white,
                        border: `2px solid ${COLORS.borderColor}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          borderColor: COLORS.primary,
                        },
                        '&.Mui-focused': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 0 0 3px ${COLORS.primary}20`,
                        },
                        '& fieldset': {
                          border: 'none',
                        },
                      }
                    }}
                  />
                </Box>

                {/* Deuxième ligne : Statistiques et contrôles */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  flexWrap: 'wrap',
                  gap: 2
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.primary }}>
                      {filteredCategories.length} catégorie{filteredCategories.length > 1 ? 's' : ''}
                      {categorySearchTerm && ` trouvée${filteredCategories.length > 1 ? 's' : ''}`}
                    </Typography>
                    {favorites.size > 0 && (
                      <Chip
                        icon={<Favorite />}
                        label={`${favorites.size} favoris`}
                        size="small"
                        clickable
                        sx={{
                          bgcolor: showFavorites ? `${COLORS.error}25` : `${COLORS.error}15`,
                          color: COLORS.error,
                          fontWeight: 600,
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.05)',
                            bgcolor: `${COLORS.error}25`
                          },
                          '& .MuiChip-icon': {
                            color: COLORS.error
                          }
                        }}
                        onClick={() => {
                          if (showFavorites) {
                            setShowFavorites(false);
                          } else {
                            setShowFavorites(true);
                            setShowTrending(false); // Désactiver les tendances
                          }
                        }}
                      />
                    )}
                    {trendingServices.length > 0 && (
                      <Chip
                        icon={<TrendingUp />}
                        label={`${trendingServices.length} tendances`}
                        size="small"
                        clickable
                        sx={{
                          bgcolor: showTrending ? `${COLORS.success}25` : `${COLORS.success}15`,
                          color: COLORS.success,
                          fontWeight: 600,
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.05)',
                            bgcolor: `${COLORS.success}25`
                          },
                          '& .MuiChip-icon': {
                            color: COLORS.success
                          }
                        }}
                        onClick={() => {
                          if (showTrending) {
                            setShowTrending(false);
                          } else {
                            setShowTrending(true);
                            setShowFavorites(false); // Désactiver les favoris
                          }
                        }}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      Affichage :
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={() => setViewMode('grid')}
                      sx={{
                        color: viewMode === 'grid' ? COLORS.primary : COLORS.neutral,
                        bgcolor: viewMode === 'grid' ? `${COLORS.primary}15` : 'transparent',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          bgcolor: `${COLORS.primary}10`,
                          transform: 'scale(1.05)'
                        }
                      }}
                    >
                      <ViewModule />
                    </IconButton>
                    {/* Masquer le bouton liste sur mobile */}
                    {!isMobile && (
                      <IconButton
                        size="small"
                        onClick={() => setViewMode('list')}
                        sx={{
                          color: viewMode === 'list' ? COLORS.primary : COLORS.neutral,
                          bgcolor: viewMode === 'list' ? `${COLORS.primary}15` : 'transparent',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            bgcolor: `${COLORS.primary}10`,
                            transform: 'scale(1.05)'
                          }
                        }}
                      >
                        <ViewList />
                      </IconButton>
                    )}

                    {(showFavorites || showTrending || categorySearchTerm) && (
                      <Fade in={showFavorites || showTrending || !!categorySearchTerm}>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Clear />}
                          onClick={() => {
                            setShowFavorites(false);
                            setShowTrending(false);
                            handleCategorySearch('');
                          }}
                          sx={{
                            borderColor: COLORS.borderColor,
                            color: COLORS.neutral,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              borderColor: COLORS.primary,
                              color: COLORS.primary
                            }
                          }}
                        >
                          Réinitialiser
                        </Button>
                      </Fade>
                    )}
                  </Box>
                </Box>
              </Stack>
            </Paper>

            {/* Message si aucune catégorie trouvée */}
            {filteredCategories.length === 0 && (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 4,
                  px: 0,
                  mx: { xs: 0, md: 2 }
                }}
              >
                <Paper
                  elevation={0}
                  sx={{
                    p: 6,
                    borderRadius: 4,
                    border: `2px dashed ${COLORS.borderColor}`,
                    bgcolor: `${COLORS.background}50`
                  }}
                >
                  <Search sx={{ fontSize: 64, color: COLORS.neutral, mb: 2 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600, color: COLORS.neutral, mb: 2 }}>
                    Aucune catégorie trouvée
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    {categorySearchTerm
                      ? `Aucun résultat pour "${categorySearchTerm}"`
                      : showFavorites
                        ? "Vous n'avez pas encore de catégories favorites"
                        : showTrending
                          ? "Aucune catégorie tendance trouvée"
                          : "Aucune catégorie disponible"
                    }
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<Clear />}
                    onClick={() => {
                      setShowFavorites(false);
                      setShowTrending(false);
                      handleCategorySearch('');
                    }}
                    sx={{
                      borderColor: COLORS.primary,
                      color: COLORS.primary,
                      '&:hover': {
                        borderColor: COLORS.secondary,
                        color: COLORS.secondary,
                        bgcolor: `${COLORS.secondary}10`
                      }
                    }}
                  >
                    Réinitialiser les filtres
                  </Button>
                </Paper>
              </Box>
            )}

            <Grid container spacing={3} sx={{ px: { xs: 0, md: 2 } }}>
              {filteredCategories
                .slice()
                .sort((a, b) => a.nom.localeCompare(b.nom, 'fr'))
                .map((category) => (
                <Grid size={viewMode === 'grid' ? { xs: 12, sm: 6, md: 4, lg: 3 } : { xs: 12 }} key={category.id}>
                    <Card
                      elevation={0}
                      sx={{
                        height: viewMode === 'list' ? '200px' : '400px', // Hauteur adaptée selon le mode
                        cursor: 'pointer',
                        border: `1px solid ${hoveredCategory === category.id ? COLORS.primary : COLORS.borderColor}`,
                        borderRadius: 3,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        background: COLORS.white,
                        position: 'relative',
                        overflow: 'hidden',
                        transform: hoveredCategory === category.id ? 'translateY(-8px)' : 'translateY(0)',
                        boxShadow: hoveredCategory === category.id
                          ? `0 12px 24px ${COLORS.shadow}`
                          : `0 2px 8px ${COLORS.shadow}`,
                        '&:hover': {
                          borderColor: COLORS.primary,
                          boxShadow: `0 12px 24px ${COLORS.shadow}`
                        },
                        // Styles spécifiques pour le mode liste
                        ...(viewMode === 'list' && {
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'stretch'
                        })
                      }}
                      onMouseEnter={() => setHoveredCategory(category.id)}
                      onMouseLeave={() => setHoveredCategory(null)}
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedCategory(category.id);
                        setSelectedSubcategory('');
                        handleSearch('', category.id, '');
                      }}
                    >
                      {/* Badge populaire repositionné - en haut à cheval sur l'image */}
                      {trendingServicesData.categories.includes(category.id) && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            bgcolor: COLORS.success,
                            color: COLORS.white,
                            fontSize: '0.75rem',
                            fontWeight: 700,
                            display: 'flex',
                            alignItems: 'center',
                            borderRadius: '12px',
                            px: 1.5,
                            py: 0.5,
                            gap: 0.5,
                            zIndex: 10,
                            boxShadow: `0 2px 8px ${COLORS.success}40`,
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              bgcolor: COLORS.success,
                              boxShadow: `0 4px 12px ${COLORS.success}50`,
                              transform: 'scale(1.05)'
                            }
                          }}
                        >
                          <TrendingUp sx={{ fontSize: 14 }} />
                          Tendance
                        </Box>
                      )}

                      {/* Image de la catégorie */}
                      <Box
                        sx={{
                          height: viewMode === 'list' ? '200px' : '140px',
                          width: viewMode === 'list' ? '300px' : '100%',
                          minWidth: viewMode === 'list' ? '300px' : 'auto',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: '40px',
                            background: `linear-gradient(transparent, ${COLORS.white}90)`,
                            zIndex: 2
                          }
                        }}
                      >
                        <picture>
                          <source srcSet={category.image.webp} type="image/webp" />
                          <img
                            src={category.image.jpg}
                            alt={category.image.alt}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              transition: 'transform 0.3s ease',
                              transform: hoveredCategory === category.id ? 'scale(1.05)' : 'scale(1)'
                            }}
                          />
                        </picture>

                        {/* Overlay avec dégradé */}
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: `linear-gradient(135deg, ${COLORS.primary}20 0%, transparent 50%, ${COLORS.secondary}20 100%)`,
                            opacity: hoveredCategory === category.id ? 1 : 0,
                            transition: 'opacity 0.3s ease',
                            zIndex: 1
                          }}
                        />
                      </Box>

                      <CardContent sx={{
                        p: 3,
                        height: viewMode === 'list' ? '100%' : 'calc(100% - 140px)',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        padding: '14px!important',
                        flex: viewMode === 'list' ? 1 : 'none'
                      }}>
                        {/* Partie supérieure avec titre et icône */}
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                          <Avatar
                            sx={{
                              bgcolor: `${COLORS.primary}15`,
                              color: COLORS.primary,
                              width: 40,
                              height: 40,
                              mr: 2,
                              '&:hover': {
                                transform: 'scale(1.1)'
                              },
                              transition: 'transform 0.2s ease'
                            }}
                          >
                            {React.createElement(categoryIcons[category.id as keyof typeof categoryIcons] || Category, { sx: { fontSize: 22 } })}
                          </Avatar>

                          <Box sx={{ flex: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <Typography
                                variant="h6"
                                component="h3"
                                sx={{
                                  fontWeight: 700,
                                  color: 'text.primary',
                                  fontSize: '1.1rem',
                                  lineHeight: 1.3
                                }}
                              >
                                {category.nom}
                              </Typography>

                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleFavorite(category.id);
                                }}
                                sx={{
                                  color: favorites.has(category.id) ? COLORS.error : 'rgba(0, 0, 0, 0.3)',
                                  width: 28,
                                  height: 28,
                                  ml: 1
                                }}
                              >
                                {favorites.has(category.id) ? <Favorite fontSize="small" /> : <BookmarkBorder fontSize="small" />}
                              </IconButton>
                            </Box>

                            <Typography
                              variant="body2"
                              sx={{
                                color: COLORS.neutral,
                                fontSize: '0.8rem',
                                display: 'flex',
                                alignItems: 'center',
                                mt: 0.5
                              }}
                            >
                              <Typography
                                component="span"
                                sx={{
                                  color: COLORS.primary,
                                  fontWeight: 600,
                                  fontSize: '0.8rem',
                                  mr: 0.5
                                }}
                              >
                                {getSubcategoriesForCategory(category.id).length} services
                              </Typography>
                              • Disponible 24/7
                            </Typography>
                          </Box>
                        </Box>

                        {/* Description avec hauteur fixe */}
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'text.secondary',
                            mb: 2,
                            fontSize: '0.85rem',
                            lineHeight: 1.5,
                            height: '2.6rem',
                            overflow: 'hidden',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}
                        >
                          {category.description}
                        </Typography>

                        {/* Services populaires */}
                        <Box sx={{ mt: 'auto' }}>
                          <Typography
                            variant="caption"
                            sx={{
                              color: COLORS.primary,
                              fontWeight: 600,
                              fontSize: '0.75rem',
                              mb: 1,
                              display: 'block'
                            }}
                          >
                            SERVICES POPULAIRES
                          </Typography>

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {getSubcategoriesForCategory(category.id).slice(0, 3).map((sub) => (
                              <Chip
                                key={sub.id}
                                label={sub.nom}
                                size="small"
                                variant="outlined"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSearchTerm(sub.nom);
                                  setSelectedCategory(sub.categoryId);
                                  setSelectedSubcategory(sub.id);
                                  handleSearch(sub.nom, sub.categoryId, sub.id, false);
                                }}
                                sx={{
                                  fontSize: '0.7rem',
                                  height: '22px',
                                  borderColor: COLORS.borderColor,
                                  color: COLORS.neutral,
                                  fontWeight: 500,
                                  '&:hover': {
                                    borderColor: COLORS.primary,
                                    color: COLORS.primary,
                                    bgcolor: `${COLORS.primary}10`
                                  }
                                }}
                              />
                            ))}

                            {getSubcategoriesForCategory(category.id).length > 3 && (
                              <Chip
                                label={`+${getSubcategoriesForCategory(category.id).length - 3}`}
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedCategoryForModal(category.id);
                                  setSubcategoryModalOpen(true);
                                }}
                                sx={{
                                  fontSize: '0.7rem',
                                  height: '22px',
                                  bgcolor: `${COLORS.primary}15`,
                                  color: COLORS.primary,
                                  fontWeight: 600,
                                  '&:hover': {
                                    bgcolor: `${COLORS.primary}25`
                                  }
                                }}
                              />
                            )}
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                </Grid>
              ))}
            </Grid>
        </Box>

        {/* Section Services populaires redesignée */}
        <Box sx={{ mb: 10, px: { xs: 2, md: 4 } }}>
            {/* En-tête section services populaires */}
            <Box textAlign="center" sx={{ mb: 6 }}>
              <Typography
                variant={isMobile ? "h4" : "h3"}
                component="h2"
                sx={{
                  fontWeight: 700,
                  color: COLORS.primary,
                  mb: 2
                }}
              >
                Services les plus demandés
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{ maxWidth: '600px', mx: 'auto', mb: 4 }}
              >
                Découvrez les services les plus populaires sur notre plateforme
              </Typography>
            </Box>

            {/* Services populaires en grid moderne révolutionnaire */}
            <Paper
              elevation={0}
              sx={{
                p: { xs: 3, md: 5 },
                mb: 8,
                background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 50%, ${COLORS.white} 100%)`,
                border: `2px solid ${COLORS.borderColor}`,
                borderRadius: 5,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: `linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 20,
                  right: 20,
                  width: '100px',
                  height: '100px',
                  background: `radial-gradient(circle, ${COLORS.accent}20 0%, transparent 70%)`,
                  borderRadius: '50%',
                  animation: 'pulse 3s ease-in-out infinite'
                }
              }}
            >
              <Stack spacing={4}>
                <Box 
                  display="flex" 
                  alignItems={{ xs: 'flex-start', sm: 'center' }} 
                  justifyContent="space-between" 
                  mb={2}
                  flexDirection={{ xs: 'column', sm: 'row' }}
                  gap={{ xs: 2, sm: 0 }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar
                      sx={{
                        bgcolor: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                        width: { xs: 40, sm: 48 },
                        height: { xs: 40, sm: 48 }
                      }}
                    >
                      <TrendingUp sx={{ fontSize: { xs: 20, sm: 24 } }} />
                    </Avatar>
                    <Box>
                      <Typography 
                        variant="h4" 
                        sx={{ 
                          fontWeight: 700, 
                          color: COLORS.primary, 
                          mb: 0.5,
                          fontSize: { xs: '1.5rem', sm: '2rem' }
                        }}
                      >
                        Services populaires
                      </Typography>
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{ fontSize: { xs: '0.85rem', sm: '0.875rem' } }}
                      >
                        Les services les plus demandés par nos clients
                      </Typography>
                    </Box>
                  </Box>

                  <Chip
                    label={`${popularServices.length} services`}
                    size="small"
                    sx={{
                      bgcolor: `${COLORS.primary}15`,
                      color: COLORS.primary,
                      fontWeight: 600,
                      alignSelf: { xs: 'flex-start', sm: 'center' },
                      ml: { xs: 7, sm: 0 }
                    }}
                  />
                </Box>

                <Grid container spacing={3}>
                  {popularServices.map((service) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3, xl: 2.4 }} key={service.id}>
                        <Paper
                          elevation={0}
                          onClick={() => handleServiceClick(service)}
                          onMouseEnter={() => setHoveredService(service.id)}
                          onMouseLeave={() => setHoveredService(null)}
                          sx={{
                            p: { xs: 2, sm: 2.5, md: 3 },
                            height: '100%',
                            cursor: 'pointer',
                            borderRadius: 4,
                            border: `2px solid ${hoveredService === service.id ? COLORS.primary : COLORS.borderColor}`,
                            bgcolor: hoveredService === service.id ? `${COLORS.primary}08` : COLORS.white,
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            position: 'relative',
                            overflow: 'hidden',
                            boxShadow: hoveredService === service.id
                              ? `0 12px 40px ${COLORS.primary}25`
                              : `0 4px 15px rgba(0, 0, 0, 0.08)`,
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: 0,
                              height: '3px',
                              background: `linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                              opacity: hoveredService === service.id ? 1 : 0,
                              transition: 'opacity 0.3s ease'
                            }
                          }}
                        >
                          <Box textAlign="center">
                      <Typography 
                              variant="body1"
                              sx={{
                                fontWeight: 600,
                                color: hoveredService === service.id ? COLORS.primary : 'text.primary',
                                transition: 'color 0.3s ease',
                                lineHeight: 1.4,
                                fontSize: { xs: '1rem', sm: '0.95rem' },
                                mb: { xs: 1.5, sm: 1 }
                              }}
                            >
                              {service.nom}
                            </Typography>

                            {/* Nouvelles métriques enrichies */}
                            <Stack spacing={{ xs: 1.5, sm: 1 }} alignItems="center">
                              <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                gap: 1,
                                flexDirection: { xs: 'column', sm: 'row' },
                                width: '100%'
                              }}>
                                <Chip
                                  label={`${service.popularity}% populaire`}
                                  size="small"
                                  sx={{
                                    bgcolor: `${COLORS.success}15`,
                                    color: COLORS.success,
                                    fontSize: { xs: '0.75rem', sm: '0.7rem' },
                                    height: { xs: 24, sm: 20 },
                                    fontWeight: 600,
                                    minWidth: { xs: '120px', sm: 'auto' }
                                  }}
                                />
                                {service.growth > 0 && (
                                  <Chip
                                    icon={<TrendingUp sx={{ fontSize: { xs: 14, sm: 12 } }} />}
                                    label={`+${service.growth}%`}
                                    size="small"
                                    sx={{
                                      bgcolor: `${COLORS.info}15`,
                                      color: COLORS.info,
                                      fontSize: { xs: '0.75rem', sm: '0.7rem' },
                                      height: { xs: 24, sm: 20 },
                                      fontWeight: 600,
                                      minWidth: { xs: '80px', sm: 'auto' },
                                      '& .MuiChip-icon': {
                                        color: COLORS.info
                                      }
                                    }}
                                  />
                                )}
                              </Box>

                              <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                gap: { xs: 3, sm: 2 }, 
                                mt: 1,
                                justifyContent: 'center',
                                flexDirection: { xs: 'row', sm: 'row' }
                              }}>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: COLORS.primary,
                                    fontWeight: 600,
                                    fontSize: { xs: '0.8rem', sm: '0.75rem' },
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5
                                  }}
                                >
                                  <EuroSymbol sx={{ fontSize: { xs: 14, sm: 12 } }} />
                                  {service.avgPrice}€/h
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: COLORS.neutral,
                                    fontSize: { xs: '0.8rem', sm: '0.75rem' },
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5
                                  }}
                                >
                                  <Star sx={{ fontSize: { xs: 14, sm: 12 }, color: COLORS.warning }} />
                                  {service.rating}
                                </Typography>
                              </Box>

                              {service.synonymes && service.synonymes.length > 0 && (
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{
                                    fontSize: { xs: '0.75rem', sm: '0.7rem' },
                                    opacity: 0.8
                                  }}
                                >
                                  +{service.synonymes.length} variantes
                                </Typography>
                              )}
                            </Stack>
                          </Box>
                        </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Stack>
            </Paper>

            {/* Villes populaires */}
            <Paper
              elevation={0}
              sx={{
                p: { xs: 3, md: 4 },
                mb: 6,
                background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                border: `1px solid ${COLORS.borderColor}`,
                borderRadius: 4,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: `linear-gradient(90deg, ${COLORS.secondary} 0%, ${COLORS.tertiary} 100%)`,
                }
              }}
            >
              <Stack spacing={3}>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <LocationOn sx={{ color: COLORS.secondary, fontSize: 28 }} />
                  <Typography variant="h5" sx={{ fontWeight: 600, color: COLORS.secondary }}>
                    Villes populaires
                  </Typography>
                </Box>

                <Grid container spacing={2}>
                  {popularCities.slice(0, 10).map((city) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3, xl: 2.4 }} key={city.name}>
                        <Chip
                          label={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <span>{city.name}</span>
                              {city.trending && (
                                <TrendingUp sx={{ fontSize: { xs: 16, sm: 14 }, color: COLORS.success }} />
                              )}
                            </Box>
                          }
                          clickable
                          onClick={() => {
                            setSelectedCity(city.name);
                            // Lancer la recherche avec la ville sélectionnée
                            // Utiliser setTimeout pour que l'état soit mis à jour
                            setTimeout(() => {
                              const params = new URLSearchParams();
                              if (searchTerm) params.append('q', searchTerm);
                              if (selectedCategory) {
                                const categoryObj = SERVICE_CATEGORIES.find(cat => cat.id === selectedCategory);
                                if (categoryObj) params.append('category', categoryObj.nom);
                              }
                              if (selectedSubcategory) {
                                const subcategoryObj = SERVICE_SUBCATEGORIES.find(sub => sub.id === selectedSubcategory);
                                if (subcategoryObj) params.append('subcategory', subcategoryObj.nom);
                              }
                              params.append('city', city.name); // Utiliser directement la ville cliquée
                              navigate(`/services/search?${params.toString()}`);
                            }, 0);
                          }}
                          sx={{
                            width: '100%',
                            height: 'auto',
                            py: { xs: 2, sm: 1.5 },
                            px: { xs: 3, sm: 2 },
                            fontSize: { xs: '0.9rem', sm: '0.85rem' },
                            fontWeight: 500,
                            borderRadius: 3,
                            border: `2px solid ${COLORS.borderColor}`,
                            bgcolor: COLORS.white,
                            color: 'text.primary',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              borderColor: COLORS.secondary,
                              bgcolor: `${COLORS.secondary}08`,
                              color: COLORS.secondary,
                              transform: 'translateY(-2px)',
                              boxShadow: `0 8px 25px ${COLORS.secondary}20`
                            },
                            '& .MuiChip-label': {
                              padding: 0,
                              width: '100%'
                            }
                          }}
                        />
                    </Grid>
                  ))}
                </Grid>
              </Stack>
            </Paper>

            {/* Recherches populaires révolutionnaires */}
            <Paper
              elevation={0}
              sx={{
                p: { xs: 3, md: 5 },
                background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 50%, ${COLORS.white} 100%)`,
                border: `2px solid ${COLORS.borderColor}`,
                borderRadius: 5,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: `linear-gradient(90deg, ${COLORS.tertiary} 0%, ${COLORS.accent} 50%, ${COLORS.secondary} 100%)`,
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 20,
                  left: 20,
                  width: '80px',
                  height: '80px',
                  background: `radial-gradient(circle, ${COLORS.tertiary}15 0%, transparent 70%)`,
                  borderRadius: '50%',
                  animation: 'pulse 4s ease-in-out infinite'
                }
              }}
            >
              <Stack spacing={4}>
                <Box 
                  display="flex" 
                  alignItems={{ xs: 'flex-start', sm: 'center' }} 
                  justifyContent="space-between" 
                  mb={2}
                  flexDirection={{ xs: 'column', sm: 'row' }}
                  gap={{ xs: 2, sm: 0 }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar
                      sx={{
                        bgcolor: `linear-gradient(135deg, ${COLORS.tertiary} 0%, ${COLORS.accent} 100%)`,
                        width: { xs: 40, sm: 48 },
                        height: { xs: 40, sm: 48 }
                      }}
                    >
                      <Search sx={{ fontSize: { xs: 20, sm: 24 } }} />
                    </Avatar>
                    <Box>
                      <Typography 
                        variant="h4" 
                        sx={{ 
                          fontWeight: 700, 
                          color: COLORS.tertiary, 
                          mb: 0.5,
                          fontSize: { xs: '1.5rem', sm: '2rem' }
                        }}
                      >
                        Recherches populaires
                      </Typography>
                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{ fontSize: { xs: '0.85rem', sm: '0.875rem' } }}
                      >
                        Les combinaisons service + ville les plus recherchées
                      </Typography>
                    </Box>
                  </Box>

                  <Chip
                    label="Tendances"
                    size="small"
                    icon={<TrendingUp />}
                    sx={{
                      bgcolor: `${COLORS.tertiary}15`,
                      color: COLORS.tertiary,
                      fontWeight: 600,
                      alignSelf: { xs: 'flex-start', sm: 'center' },
                      ml: { xs: 7, sm: 0 }
                    }}
                  />
                </Box>

                <Grid container spacing={3}>
                  {popularSearches.map((search) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={`${search.service}-${search.city}`}>
                        <Paper
                          elevation={0}
                          onClick={() => handleQuickSearch(search.service, search.city)}
                          sx={{
                            p: { xs: 3, sm: 2.5 },
                            cursor: 'pointer',
                            borderRadius: 3,
                            border: `2px solid ${COLORS.borderColor}`,
                            bgcolor: COLORS.white,
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            position: 'relative',
                            overflow: 'hidden',
                            '&:hover': {
                              borderColor: COLORS.tertiary,
                              bgcolor: `${COLORS.tertiary}08`,
                              transform: 'translateY(-2px)',
                              boxShadow: `0 8px 25px ${COLORS.tertiary}20`
                            },
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: 0,
                              height: '3px',
                              background: `linear-gradient(90deg, ${COLORS.tertiary} 0%, ${COLORS.accent} 100%)`,
                              opacity: 0,
                              transition: 'opacity 0.3s ease'
                            },
                            '&:hover::before': {
                              opacity: 1
                            }
                          }}
                        >
                          <Box textAlign="center">
                            <Box sx={{ 
                              display: 'flex', 
                              alignItems: 'center', 
                              justifyContent: 'center', 
                              gap: 1, 
                              mb: { xs: 1, sm: 0.5 },
                              flexDirection: { xs: 'column', sm: 'row' }
                            }}>
                              <Typography
                                variant="body2"
                        sx={{ 
                          fontWeight: 700, 
                                  color: 'text.primary',
                                  fontSize: { xs: '1rem', sm: '0.9rem' },
                                  textAlign: 'center'
                                }}
                              >
                                {search.service}
                              </Typography>
                              {search.trend === 'up' && (
                                <TrendingUp sx={{ fontSize: { xs: 18, sm: 16 }, color: COLORS.success }} />
                              )}
                              {search.trend === 'down' && (
                                <TrendingFlat sx={{ fontSize: { xs: 18, sm: 16 }, color: COLORS.error, transform: 'rotate(180deg)' }} />
                              )}
                            </Box>

                            <Typography
                              variant="caption"
                              sx={{
                                color: COLORS.tertiary,
                                fontWeight: 600,
                                fontSize: { xs: '0.85rem', sm: '0.8rem' },
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: 0.5,
                                mb: { xs: 1.5, sm: 1 }
                              }}
                            >
                              <LocationOn sx={{ fontSize: { xs: 16, sm: 14 } }} />
                              {search.city}
                            </Typography>

                            <Chip
                              label={`${search.searchVolume} recherches`}
                              size="small"
                              sx={{
                                bgcolor: `${COLORS.neutral}15`,
                                color: COLORS.neutral,
                                fontSize: { xs: '0.75rem', sm: '0.7rem' },
                                height: { xs: 22, sm: 18 },
                                fontWeight: 500
                              }}
                            />
                          </Box>
                        </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Stack>
            </Paper>
        </Box>

        {/* Section garanties et avantages CRO */}
        <Box sx={{ mb: 10, px: { xs: 2, md: 4 } }}>
            <Box textAlign="center" sx={{ mb: 8 }}>
              <Fade in={isPageLoaded} timeout={1600}>
                <Typography
                  variant={isMobile ? "h3" : "h2"}
                  component="h2"
                  sx={{
                    fontWeight: 800,
                    background: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 3,
                    position: 'relative',
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: -10,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '100px',
                      height: '4px',
                      background: `linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                      borderRadius: '2px'
                    }
                  }}
                >
                  Pourquoi choisir JobPartiel ?
                </Typography>
              </Fade>

              <Fade in={isPageLoaded} timeout={1800}>
                <Typography
                  variant={isMobile ? "body1" : "h5"}
                  color="text.secondary"
                  sx={{
                    maxWidth: '700px',
                    mx: 'auto',
                    mb: 6,
                    lineHeight: 1.6,
                    fontWeight: 400
                  }}
                >
                  La plateforme nouvelle génération qui révolutionne la mise en relation entre particuliers et professionnels
                </Typography>
              </Fade>
            </Box>

            <Grid container spacing={4} sx={{ mb: 8 }}>
              {[
                {
                  icon: <CheckCircle sx={{ fontSize: 40 }} />,
                  title: "Processus sécurisé",
                  description: "Vérification d'identité, assurance et validation des compétences pour tous nos jobbeurs",
                  color: COLORS.success,
                  badge: "Sécurité"
                },
                {
                  icon: <Rocket sx={{ fontSize: 40 }} />,
                  title: "Technologie avancée",
                  description: "Algorithme intelligent de mise en relation basé sur vos besoins spécifiques",
                  color: COLORS.info,
                  badge: "Innovation"
                },
                {
                  icon: <Lightbulb sx={{ fontSize: 40 }} />,
                  title: "Transparence totale",
                  description: "Tarifs clairs, pas de commission cachée, négociation directe avec le jobbeur",
                  color: COLORS.warning,
                  badge: "Transparence"
                },
                {
                  icon: <ThumbUp sx={{ fontSize: 40 }} />,
                  title: "Satisfaction client",
                  description: "Support dédié et garantie de qualité pour chaque prestation réalisée",
                          color: COLORS.primary, 
                  badge: "Qualité"
                }
              ].map((feature, index) => (
                <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 4,
                        textAlign: 'center',
                        height: '100%',
                        background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
                        border: `2px solid ${COLORS.borderColor}`,
                        borderRadius: 4,
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          borderColor: feature.color,
                          boxShadow: `0 20px 60px ${feature.color}25, 0 8px 25px ${feature.color}15`,
                          '& .feature-icon': {
                            transform: 'scale(1.1) rotate(5deg)',
                            background: `linear-gradient(135deg, ${feature.color} 0%, ${feature.color}80 100%)`
                          },
                          '& .feature-badge': {
                            opacity: 1,
                            transform: 'translateY(0)'
                          }
                        },
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '4px',
                          background: `linear-gradient(90deg, ${feature.color} 0%, ${feature.color}80 100%)`,
                          opacity: 0,
                          transition: 'opacity 0.3s ease'
                        },
                        '&:hover::before': {
                          opacity: 1
                        }
                      }}
                    >
                      {/* Badge flottant */}
                      <Chip
                        label={feature.badge}
                        size="small"
                        className="feature-badge"
                        sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          bgcolor: `${feature.color}15`,
                          color: feature.color,
                          fontWeight: 600,
                          fontSize: '0.75rem',
                          opacity: 0,
                          transform: 'translateY(-10px)',
                          transition: 'all 0.3s ease'
                        }}
                      />

                      <Box
                        className="feature-icon"
                        sx={{
                          width: 90,
                          height: 90,
                          borderRadius: '50%',
                          bgcolor: `${feature.color}15`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 3,
                          color: feature.color,
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          position: 'relative',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            inset: -4,
                            borderRadius: '50%',
                            background: `linear-gradient(45deg, ${feature.color}30, transparent, ${feature.color}30)`,
                            opacity: 0,
                            transition: 'opacity 0.3s ease'
                          },
                          '&:hover::after': {
                            opacity: 1
                          }
                        }}
                      >
                        {feature.icon}
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          mb: 2,
                          color: feature.color,
                          fontSize: '1.3rem'
                        }}
                      >
                        {feature.title}
                      </Typography>

                      <Typography 
                        variant="body2" 
                        color="text.secondary"
                        sx={{
                          lineHeight: 1.7,
                          fontSize: '0.95rem',
                          fontWeight: 400
                        }}
                      >
                        {feature.description}
                      </Typography>
                    </Paper>
                </Grid>
              ))}
            </Grid>
        </Box>

        {/* Call to action moderne et optimisé */}
        <Paper
            elevation={0}
            sx={{
              mt: 8,
              p: { xs: 4, md: 8 },
              textAlign: 'center',
              background: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 50%, ${COLORS.tertiary} 100%)`,
              color: COLORS.white,
              borderRadius: 4,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                opacity: 0.3
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: COLORS.white,
                  color: COLORS.primary,
                  mx: 'auto',
                  mb: 3,
                  boxShadow: `0 8px 32px ${COLORS.white}30`
                }}
              >
                <WorkOutline sx={{ fontSize: 40 }} />
              </Avatar>

              <Typography
                variant={isMobile ? "h4" : "h3"}
                component="h2"
                gutterBottom
                sx={{ fontWeight: 700, mb: 2 }}
              >
                Rejoignez notre communauté
              </Typography>

              <Typography
                variant={isMobile ? "body1" : "h6"}
                sx={{
                  mb: 4,
                  opacity: 0.9,
                  maxWidth: '600px',
                  mx: 'auto',
                  lineHeight: 1.6
                }}
              >
                Développez votre activité en rejoignant plus de 50 000 jobbeurs qui font confiance à JobPartiel
              </Typography>

              <Stack
                direction={isMobile ? "column" : "row"}
                spacing={2}
                justifyContent="center"
                alignItems="center"
              >
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/inscription')}
                  endIcon={<ArrowForward />}
                  sx={{
                    bgcolor: COLORS.white,
                    color: COLORS.primary,
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    borderRadius: 3,
                    fontSize: '1.1rem',
                    minWidth: isMobile ? '100%' : '200px',
                    '&:hover': {
                      bgcolor: COLORS.lightGray,
                      transform: 'translateY(-2px)',
                      boxShadow: `0 8px 25px ${COLORS.white}40`
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Devenir Jobbeur
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => navigate('/services/search')}
                  sx={{
                    borderColor: COLORS.white,
                    color: COLORS.white,
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    borderRadius: 3,
                    fontSize: '1.1rem',
                    minWidth: isMobile ? '100%' : '200px',
                    '&:hover': {
                      borderColor: COLORS.white,
                      bgcolor: `${COLORS.white}20`,
                      transform: 'translateY(-2px)'
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Explorer les services
                </Button>
              </Stack>

              {/* Badges de confiance */}
              <Stack
                direction={isMobile ? "column" : "row"}
                spacing={3}
                justifyContent="center"
                sx={{ mt: 4, pt: 4, borderTop: `1px solid ${COLORS.white}30` }}
              >
                {[
                  { icon: <Verified />, text: "Jobbeurs vérifiés" },
                  { icon: <Speed />, text: "Réponse rapide" },
                  { icon: <LocalOffer />, text: "Prix transparents" }
                ].map((badge, index) => (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    gap={1}
                    sx={{ opacity: 0.9 }}
                  >
                    <Box sx={{ color: COLORS.white }}>{badge.icon}</Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {badge.text}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Box>
          </Paper>

        {/* Modal de sélection des sous-catégories avec ModalPortal */}
        <ModalPortal
          isOpen={subcategoryModalOpen}
          onBackdropClick={() => setSubcategoryModalOpen(false)}
        >
          <Box
            sx={{
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: { xs: '95%', sm: '90%', md: '80%', lg: '70%' },
              maxWidth: '800px',
              maxHeight: '90vh',
              bgcolor: 'background.paper',
              borderRadius: 4,
              background: `linear-gradient(135deg, ${COLORS.white} 0%, ${COLORS.background} 100%)`,
              border: `2px solid ${COLORS.primary}20`,
              boxShadow: `0 20px 60px ${COLORS.shadow}`,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* En-tête de la modal */}
            <Box sx={{
              p: 3,
              borderBottom: `1px solid ${COLORS.borderColor}`,
              background: `linear-gradient(90deg, ${COLORS.primary}10 0%, ${COLORS.secondary}10 100%)`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: `linear-gradient(135deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%)`,
                      width: 40,
                      height: 40
                    }}
                  >
                    {React.createElement(categoryIcons[selectedCategoryForModal as keyof typeof categoryIcons] || Category, { sx: { fontSize: 20 } })}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: COLORS.primary }}>
                      {SERVICE_CATEGORIES.find(cat => cat.id === selectedCategoryForModal)?.nom}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sélectionnez un service spécifique
                    </Typography>
                  </Box>
                </Box>
                <IconButton
                  onClick={() => setSubcategoryModalOpen(false)}
                  sx={{
                    color: COLORS.neutral,
                    '&:hover': {
                      color: COLORS.primary,
                      bgcolor: `${COLORS.primary}10`
                    }
                  }}
                >
                  <Clear />
                </IconButton>
              </Box>
            </Box>

            {/* Contenu scrollable */}
            <Box sx={{ p: 3, overflowY: 'auto', flex: 1 }}>
              <Grid container spacing={2}>
                {getSubcategoriesForCategory(selectedCategoryForModal)
                  .sort((a, b) => a.nom.localeCompare(b.nom, 'fr')) // Tri alphabétique
                  .map((subcategory) => {
                    // Vérifier si ce service est dans les tendances
                    // On vérifie le nom principal ET les synonymes
                    const isTrending = showTrending && (
                      trendingServices.includes(subcategory.nom) ||
                      (subcategory.synonymes && subcategory.synonymes.some(syn => trendingServices.includes(syn)))
                    );
                    
                    return (
                      <Grid size={{ xs: 12, sm: 6, md: 4 }} key={subcategory.id}>
                        <Paper
                          elevation={0}
                          onClick={() => {
                            setSearchTerm(subcategory.nom);
                            setSelectedCategory(subcategory.categoryId);
                            setSelectedSubcategory(subcategory.id);
                            setSubcategoryModalOpen(false);
                            handleSearch(subcategory.nom, subcategory.categoryId, subcategory.id, false);
                            addToSearchHistory(subcategory.nom);
                          }}
                          sx={{
                            p: 2.5,
                            cursor: 'pointer',
                            border: `1px solid ${isTrending ? COLORS.success : COLORS.borderColor}`,
                            borderRadius: 3,
                            transition: 'all 0.3s ease',
                            background: isTrending ? `${COLORS.success}08` : COLORS.white,
                            position: 'relative',
                            '&:hover': {
                              borderColor: COLORS.primary,
                              bgcolor: `${COLORS.primary}08`,
                              boxShadow: `0 8px 25px ${COLORS.primary}20`
                            }
                          }}
                        >
                          {/* Badge tendance */}
                          {isTrending && (
                            <Box
                              sx={{
                                position: 'absolute',
                                top: 6,
                                right: 6,
                                bgcolor: COLORS.success,
                                color: COLORS.white,
                                fontSize: '0.65rem',
                                fontWeight: 700,
                                display: 'flex',
                                alignItems: 'center',
                                borderRadius: '6px',
                                px: 0.8,
                                py: 0.3,
                                gap: 0.3,
                                zIndex: 10,
                                minWidth: 'auto',
                                height: '20px'
                              }}
                            >
                              <TrendingUp sx={{ fontSize: 10 }} />
                              Tendance
                            </Box>
                          )}
                          
                          <Typography
                            variant="body1"
                            sx={{
                              fontWeight: 600,
                              color: isTrending ? COLORS.success : 'text.primary',
                              mb: 1,
                              fontSize: '0.95rem',
                              pr: isTrending ? 8 : 0, // Plus d'espace pour le badge
                              lineHeight: 1.3,
                              wordBreak: 'break-word' // Permettre la coupure des mots longs
                            }}
                          >
                            {subcategory.nom}
                          </Typography>
                          {subcategory.synonymes && subcategory.synonymes.length > 0 && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                fontSize: '0.75rem',
                                display: 'block'
                              }}
                            >
                              +{subcategory.synonymes.length} variantes
                            </Typography>
                          )}
                        </Paper>
                      </Grid>
                    );
                  })}
              </Grid>
            </Box>
          </Box>
        </ModalPortal>
      </Container>
    </Box>
    </>
  );
};

export default ServicesPage;
