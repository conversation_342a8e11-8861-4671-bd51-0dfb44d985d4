import { useState } from 'react';
import axios from 'axios';
import { API_CONFIG } from '../config/api';
import { notify } from '../components/Notification';
import logger from '../utils/logger';
import { getCommonHeaders } from '../utils/headers';

export interface ImageModerationResult {
  isSafe: boolean;
  score: number;
  categories: {
    adult: boolean;
    violence: boolean;
    harassment: boolean;
    hateSpeech: boolean;
    selfHarm: boolean;
    sexual: boolean;
    dangerousContent: boolean;
    unknownRisk: boolean;
  };
  description?: string;
  improvementSuggestions?: string;
}

interface ModerationResponse {
  success: boolean;
  isSafe: boolean;
  score: number;
  categories: {
    harassment?: boolean;
    hateSpeech?: boolean;
    sexualContent?: boolean;
    adult?: boolean;
    violence?: boolean;
    selfHarm?: boolean;
    sexual?: boolean;
    illegalActivity?: boolean;
    dangerousContent?: boolean;
    spam?: boolean;
    phoneSpam?: boolean;
    addressSpam?: boolean;
    unknownRisk?: boolean;
    poorQuality?: boolean;
    irrelevantContent?: boolean;
    misleadingContent?: boolean;
    clean?: boolean;
  };
  flaggedText?: string[];
  moderationId: string;
  description?: string;
  serviceType?: string;
  relevantToUserServices?: boolean;
  qualityAssessment?: {
    overall: number;
    clarity: number;
    relevance: number;
    professionalAppearance: number;
  };
  improvementSuggestions?: string;
}

// Adapter la signature pour accepter un 4ème paramètre options
export type ModerateImageOptions = { imageUrl?: string; tempImagePath?: string };

/**
 * Hook pour modérer une image avant de l'envoyer au serveur
 */
export const useImageModeration = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ImageModerationResult | null>(null);

  /**
   * Upload temporaire d'une image pour la modération
   * @param file Fichier image à uploader temporairement
   * @returns URL publique de l'image et chemin temporaire
   */
  const uploadTemporaryImage = async (file: File): Promise<{ imageUrl: string, tempImagePath: string }> => {
    try {
      // Créer un FormData pour l'upload
      const formData = new FormData();
      formData.append('file', file);

      // Récupérer les headers communs (sans Content-Type pour permettre à axios de définir la boundary correctement)
      const headers = await getCommonHeaders();
      delete headers['Content-Type']; // Important: laisser axios définir le Content-Type avec la boundary

      // Appeler l'API d'upload temporaire
      const response = await axios.post(
        `${API_CONFIG.baseURL}/api/storage/temp-upload`,
        formData,
        {
          headers,
          withCredentials: true
        }
      );

      if (!response.data.success) {
        throw new Error('Erreur lors de l\'upload temporaire de l\'image');
      }

      return {
        imageUrl: response.data.publicUrl,
        tempImagePath: response.data.filePath
      };
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de l\'upload temporaire de l\'image';
      logger.error('Erreur lors de l\'upload temporaire de l\'image', {
        error: err,
        errorMessage
      });
      throw err;
    }
  };

  /**
   * Modère une image avant de l'envoyer au serveur
   * @param file Fichier image à modérer
   * @param type Type de contenu (mission, profile_picture, gallery, etc.)
   * @param contentId ID du contenu (optionnel)
   * @param options Options de modération (optionnel)
   * @note Pour les photos de profil, utiliser uniquement 'profile_picture' comme type
   */
  const moderateImage = async (
    file: File,
    type: string,
    contentId?: string,
    options?: ModerateImageOptions
  ): Promise<ImageModerationResult> => {
    try {
      setIsLoading(true);
      setError(null);

      // Vérifier que le fichier est une image
      if (!file.type.startsWith('image/')) {
        throw new Error('Le fichier doit être une image');
      }

      // Nouvelle logique : si options.imageUrl et options.tempImagePath sont fournis, on les utilise directement
      let imageUrl: string | null = null;
      let tempImagePath: string | null = null;

      if (options?.imageUrl && options?.tempImagePath) {
        imageUrl = options.imageUrl;
        tempImagePath = options.tempImagePath;
        logger.info('Utilisation directe de l\'image temporaire existante pour la modération', {
          imageUrl,
          tempImagePath
        });
      } else {
        try {
          // Uploader l'image temporairement (UN SEUL UPLOAD)
          const uploadResult = await uploadTemporaryImage(file);
          imageUrl = uploadResult.imageUrl;
          tempImagePath = uploadResult.tempImagePath;

          logger.info('Image uploadée temporairement pour modération', {
            imageUrl,
            tempImagePath
          });
        } catch (uploadError: any) {
          // Vérifier si c'est une erreur de taille de payload
          if (uploadError.response?.status === 413 ||
              (uploadError.message && uploadError.message.includes('request entity too large'))) {
            logger.error('Erreur de taille de payload lors de l\'upload temporaire', uploadError);
            setError('La taille de l\'image est trop grande. Veuillez utiliser une image plus petite (max 5MB).');
            setIsLoading(false);
            throw new Error('Image trop volumineuse');
          } else {
            logger.error('Erreur lors de l\'upload temporaire, fallback vers base64', uploadError);
            // En cas d'erreur, fallback vers la méthode base64
            imageUrl = null;
            tempImagePath = null;
          }
        }
      }

      // Récupérer les headers communs (qui incluent déjà le token CSRF)
      const headers = await getCommonHeaders();

      // Préparer les données pour l'API de modération
      const moderationData: any = {
        text: 'Image à modérer pour ' + type,
        type: type,
        contentId: contentId || `image-temp-${Date.now()}`
      };
      if (imageUrl && tempImagePath) {
        moderationData.imageUrl = imageUrl;
        moderationData.tempImagePath = tempImagePath;
      }

      // S'assurer que le type est valide selon le schéma du backend
      // Si le type n'est pas dans la liste des types acceptés, utiliser un type par défaut
      const validTypes = [
        'mission', 'comment', 'profile', 'titre_service', 'description_service',
        'gallery_name', 'gallery_description', 'mission_title', 'mission_description', 'review',
        'gallery', 'gallery_cover', 'featured', 'mission_assistant', 'avatar', 'profile_picture', 'banner_picture', 'card_editor'
      ];

      if (!validTypes.includes(moderationData.type)) {
        // Mapper les types spécifiques aux types acceptés par le backend
        if (type === 'featured_photo') {
          moderationData.type = 'featured';
        } else if (type === 'mission_image') {
          moderationData.type = 'mission';
        } else if (type === 'gallery_photo') {
          moderationData.type = 'gallery';
        } else if (type === 'banner_picture') {
          moderationData.type = 'banner_picture';
        } else {
          // Type par défaut si aucune correspondance
          moderationData.type = 'profile_picture';
        }
      }

      // Log des données envoyées pour debug
      logger.info('Données envoyées à l\'API de modération:', {
        originalType: type,
        mappedType: moderationData.type,
        contentId: contentId || `image-temp-${Date.now()}`,
        hasImageUrl: !!imageUrl,
        hasTempImagePath: !!tempImagePath,
        hasImageBuffer: !!file
      });

      // Log complet des données envoyées à l'API de modération:
      logger.info('Données complètes envoyées à l\'API de modération:', {
        text: moderationData.text,
        type: moderationData.type,
        contentId: moderationData.contentId,
        hasImageUrl: !!moderationData.imageUrl,
        hasTempImagePath: !!moderationData.tempImagePath,
        hasImage: !!moderationData.image,
        imageUrlLength: moderationData.imageUrl ? moderationData.imageUrl.length : 0,
        tempImagePathLength: moderationData.tempImagePath ? moderationData.tempImagePath.length : 0,
        imageLength: moderationData.image ? moderationData.image.length : 0,
        imageMimeType: moderationData.imageMimeType
      });

      try {
        // Appeler l'API de modération
        const response = await axios.post<ModerationResponse>(
          `${API_CONFIG.baseURL}/api/content-moderation/analyze`,
          moderationData,
          {
            headers,
            withCredentials: true
          }
        );

        if (!response.data.success) {
          throw new Error('Erreur lors de la modération de l\'image');
        }

        // Transformer le résultat pour correspondre à notre interface
        const data = (response.data as any).data || response.data;
        const moderationResult: ImageModerationResult = {
          isSafe: data.isSafe,
          score: data.score,
          categories: {
            adult: data.categories?.sexualContent || data.categories?.adult || false,
            violence: data.categories?.violence || false,
            harassment: data.categories?.harassment || false,
            hateSpeech: data.categories?.hateSpeech || false,
            selfHarm: data.categories?.selfHarm || false,
            sexual: data.categories?.sexualContent || data.categories?.sexual || false,
            dangerousContent: data.categories?.illegalActivity || data.categories?.dangerousContent || false,
            unknownRisk: data.categories?.unknownRisk || false
          },
          description: data.description || (data.flaggedText && data.flaggedText.length > 0 ? data.flaggedText[0] : undefined),
          improvementSuggestions: data.improvementSuggestions || (data.flaggedText && data.flaggedText.length > 4 ? data.flaggedText[4].replace('Suggestions d\'amélioration: ', '') : undefined)
        };

        setResult(moderationResult);
        return moderationResult;
      } catch (error: any) {
        // Log détaillé de l'erreur
        logger.error('Erreur détaillée lors de l\'appel à l\'API de modération:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          moderationData: {
            type: moderationData.type,
            contentId: moderationData.contentId,
            hasImageUrl: !!moderationData.imageUrl,
            hasTempImagePath: !!moderationData.tempImagePath,
            hasImage: !!moderationData.image
          }
        });

        throw error;
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Erreur lors de la modération de l\'image';
      logger.error('Erreur lors de la modération de l\'image', {
        error: err,
        errorMessage
      });
      setError(errorMessage);
      notify(errorMessage, 'error');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    moderateImage,
    isLoading,
    error,
    result
  };
};

export default useImageModeration;
