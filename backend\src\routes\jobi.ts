import { Router } from 'express';
import { JobiController, getJobiBalance } from '../controllers/jobi';
import { authMiddleware } from '../middleware/authMiddleware';
import { rateLimit } from 'express-rate-limit';
import { dbService } from '../services/db';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import { sendJobiExchangeEmail } from '../services/emailService';
import { logUserActivity, getIpFromRequest } from '../utils/activityLogger';
import { Request, Response, NextFunction } from 'express';
import { decryptProfilDataAsync } from '../utils/encryption';
import { decryptUserDataAsync } from '../utils/encryption';

const router = Router();
const jobiController = new JobiController();

// --- Ajout d'un asyncHandler simple pour Express 5 ---
function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any> | void
): (req: Request, res: Response, next: NextFunction) => void {
  return function (req: Request, res: Response, next: NextFunction): void {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
// ---------------------------------------------------

// Configuration du rate limiter
// Rate limiter pour les requêtes Jobi
const jobiLimiter = rateLimit({
  windowMs: 1 * 20 * 1000, // 1 minute
  max: 60, // 60 requêtes maximum par fenêtre
  message: {
    message: 'Trop de requêtes de Jobi. Veuillez réessayer dans 1 minute.',
    success: false,
    toastType: 'error'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

router.get('/historique',
  authMiddleware.authenticateToken,
  jobiLimiter,
  asyncHandler(jobiController.getHistorique.bind(jobiController))
);

// Nouvelle route pour les statistiques mensuelles
router.get('/monthly-stats',
  authMiddleware.authenticateToken,
  jobiLimiter,
  asyncHandler(jobiController.getMonthlyStats.bind(jobiController))
);

// Route pour récupérer le solde d'un utilisateur spécifique
router.get('/balance/:userId',
  authMiddleware.authenticateToken,
  jobiLimiter,
  asyncHandler(getJobiBalance)
);

// Route pour récupérer le solde Jobi de l'utilisateur
router.get('/solde',
  authMiddleware.authenticateToken,
  jobiLimiter,
  asyncHandler(jobiController.getSolde.bind(jobiController))
);

// Route pour récupérer le code de parrainage de l'utilisateur
router.get('/referral-code', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si les données sont en cache
    const cacheKey = `referral_code:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      // Retourner les données du cache
      res.status(200).json(JSON.parse(cachedData));
      return;
    }

    // Récupérer le code de parrainage de l'utilisateur
    const { data: userData, error: userError } = await dbService.supabase
      .from('users')
      .select('referral_code')
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération du code de parrainage:', userError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du code de parrainage',
        toastType: 'error'
      });
      return;
    }

    // Construire le lien de parrainage
    const referralLink = `${process.env.FRONTEND_URL}/inscription?ref=${userData.referral_code}`;

    const response = {
      success: true,
      referralCode: userData.referral_code,
      referralLink
    };

    // Mettre en cache pour 2 minutes
    await redis.setex(cacheKey, 120, JSON.stringify(response));

    res.status(200).json(response);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération du code de parrainage:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du code de parrainage',
      toastType: 'error'
    });
    return;
  }
}));

// Route pour récupérer la liste des filleuls d'un utilisateur
router.get('/referrals', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si les données sont en cache
    const cacheKey = `referrals:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      // Retourner les données du cache
      res.status(200).json(JSON.parse(cachedData));
      return;
    }

    // Définir les types pour les données de Supabase
    interface Referral {
      id: string;
      status: string;
      created_at: string;
      completed_at: string | null;
      rewarded_at: string | null;
      referred_id: string;
      reward_amount: number | null;
    }

    interface User {
      id: string;
      email: string;
    }

    interface Profile {
      user_id: string;
      nom: string | null;
      prenom: string | null;
    }

    // Récupérer les filleuls de l'utilisateur
    const { data: referrals, error: referralsError } = await dbService.supabase
      .from('user_referrals')
      .select(`
        id,
        status,
        created_at,
        completed_at,
        rewarded_at,
        referred_id,
        reward_amount
      `)
      .eq('referrer_id', userId)
      .order('created_at', { ascending: false });

    if (referralsError) {
      logger.error('Erreur lors de la récupération des filleuls:', referralsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des filleuls',
        toastType: 'error'
      });
      return;
    }

    // Si aucun filleul trouvé
    if (!referrals || referrals.length === 0) {
      const emptyResponse = {
        success: true,
        referrals: []
      };

      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(emptyResponse));

      res.status(200).json(emptyResponse);
      return;
    }

    // Récupérer les informations des utilisateurs référés
    const referredIds = (referrals as Referral[]).map(referral => referral.referred_id);

    const { data: users, error: usersError } = await dbService.supabase
      .from('users')
      .select('id, email')
      .in('id', referredIds);

    if (usersError) {
      logger.error('Erreur lors de la récupération des informations des utilisateurs:', usersError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations des utilisateurs',
        toastType: 'error'
      });
      return;
    }

    // Déchiffrer les emails des utilisateurs référés
    const decryptedUsers = await Promise.all(
      (users as User[] | null)?.map(async (u) => await decryptUserDataAsync(u)) || []
    );

    // Récupérer les profils des utilisateurs référés
    const { data: profiles, error: profilesError } = await dbService.supabase
      .from('user_profil')
      .select('user_id, nom, prenom')
      .in('user_id', referredIds);

    if (profilesError) {
      logger.error('Erreur lors de la récupération des profils des utilisateurs:', profilesError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des profils des utilisateurs',
        toastType: 'error'
      });
      return;
    }

    // Récupérer l'historique des transactions liées aux parrainages
    const { data: jobiHistorique, error: jobiHistoriqueError } = await dbService.supabase
      .from('user_jobi_historique')
      .select('montant, user_id, titre, description, date_creation')
      .eq('user_id', userId)
      .like('titre', '%parrainage%')
      .order('date_creation', { ascending: false });

    // Calculer les gains par filleul
    const earningsByReferral: Record<string, number> = {};
    if (jobiHistorique && jobiHistorique.length > 0) {
      jobiHistorique.forEach((record: any) => {
        // Extraire l'ID du filleul depuis la description (si disponible)
        const referredIdMatch = record.description.match(/filleul:([a-f0-9-]+)/i);
        const referredId = referredIdMatch ? referredIdMatch[1] : null;

        if (referredId && referredIds.includes(referredId)) {
          if (!earningsByReferral[referredId]) {
            earningsByReferral[referredId] = 0;
          }
          earningsByReferral[referredId] += Number(record.montant);
        }
      });
    }

    // Combiner les données
    const referralsWithUserInfo = await Promise.all(
      (referrals as Referral[]).map(async (referral) => {
        const user = decryptedUsers.find(u => u.id === referral.referred_id);
        const profile = (profiles as Profile[] | null)?.find(p => p.user_id === referral.referred_id);
        // Déchiffrer les données de profil
        const decryptedProfile = profile ? await decryptProfilDataAsync(profile) : null;
        const earnings = earningsByReferral[referral.referred_id] || (referral.status === 'rewarded' ? Number(referral.reward_amount) : 0);

        return {
          id: referral.id,
          email: user?.email || '',
          nom: decryptedProfile?.nom || null,
          prenom: decryptedProfile?.prenom || null,
          status: referral.status,
          created_at: referral.created_at,
          completed_at: referral.completed_at || null,
          rewarded_at: referral.rewarded_at || null,
          reward_amount: referral.reward_amount || 0,
          earnings: earnings
        };
      })
    );

    // Calculer le total des gains
    const totalEarnings = referralsWithUserInfo.reduce((sum, referral) => sum + (referral.earnings || 0), 0);

    const response = {
      success: true,
      referrals: referralsWithUserInfo,
      totalEarnings
    };

    // Mettre en cache pour 2 minutes
    await redis.setex(cacheKey, 120, JSON.stringify(response));

    res.status(200).json(response);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des filleuls:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des filleuls',
      toastType: 'error'
    });
    return;
  }
}));

// Route pour récupérer les statistiques de parrainage de l'utilisateur
router.get('/referrals', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Récupérer les parrainages de l'utilisateur
    const { data: referralsData, error: referralsError } = await dbService.supabase
      .from('user_referrals')
      .select(`
        id,
        status,
        reward_amount,
        created_at,
        completed_at,
        rewarded_at,
        referred_id,
        users:referred_id (email)
      `)
      .eq('referrer_id', userId)
      .order('created_at', { ascending: false });

    if (referralsError) {
      logger.error('Erreur lors de la récupération des parrainages:', referralsError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des parrainages',
        toastType: 'error'
      });
      return;
    }

    // Calculer les statistiques
    const totalReferrals = referralsData.length;
    const pendingReferrals = referralsData.filter((r: any) => r.status === 'pending').length;
    const completedReferrals = referralsData.filter((r: any) => r.status === 'completed').length;
    const rewardedReferrals = referralsData.filter((r: any) => r.status === 'rewarded').length;
    const totalEarnings = referralsData
      .filter((r: any) => r.status === 'rewarded')
      .reduce((sum: number, r: any) => sum + parseFloat(r.reward_amount), 0);

    res.status(200).json({
      success: true,
      referrals: referralsData,
      stats: {
        totalReferrals,
        pendingReferrals,
        completedReferrals,
        rewardedReferrals,
        totalEarnings
      }
    });
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération des parrainages:', error);
    res.status(500).json({
      success: false,
      message: 'Une erreur est survenue',
      toastType: 'error'
    });
    return;
  }
}));

// Route pour récupérer les informations du parrain d'un utilisateur
router.get('/referrer', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié',
        toastType: 'error'
      });
      return;
    }

    // Vérifier si les données sont en cache
    const cacheKey = `referrer:${userId}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      // Retourner les données du cache
      res.status(200).json(JSON.parse(cachedData));
      return;
    }

    // Récupérer l'ID du parrain de l'utilisateur
    const { data: userData, error: userError } = await dbService.supabase
      .from('users')
      .select('referred_by')
      .eq('id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la récupération du parrain:', userError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du parrain',
        toastType: 'error'
      });
      return;
    }

    // Si l'utilisateur n'a pas de parrain
    if (!userData || !userData.referred_by) {
      const emptyResponse = {
        success: true,
        referrer: null
      };

      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(emptyResponse));

      res.status(200).json(emptyResponse);
      return;
    }

    // Vérifier que userData.referred_by n'est pas null avant de l'utiliser
    if (!userData.referred_by || userData.referred_by === 'null') {
      const emptyResponse = {
        success: true,
        referrer: null
      };

      // Mettre en cache pour 2 minutes
      await redis.setex(cacheKey, 120, JSON.stringify(emptyResponse));

      res.status(200).json(emptyResponse);
      return;
    }

    // Récupérer les informations du parrain
    const { data: referrerUser, error: referrerUserError } = await dbService.supabase
      .from('users')
      .select('id, email')
      .eq('id', userData.referred_by)
      .single();

    if (referrerUserError) {
      logger.error('Erreur lors de la récupération des informations du parrain:', referrerUserError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations du parrain',
        toastType: 'error'
      });
      return;
    }

    // Récupérer le profil du parrain
    const { data: referrerProfile, error: referrerProfileError } = await dbService.supabase
      .from('user_profil')
      .select('user_id, nom, prenom')
      .eq('user_id', userData.referred_by)
      .single();

    if (referrerProfileError) {
      logger.error('Erreur lors de la récupération du profil du parrain:', referrerProfileError);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du profil du parrain',
        toastType: 'error'
      });
      return;
    }

    // Récupérer les informations du parrainage
    const { data: referralData, error: referralError } = await dbService.supabase
      .from('user_referrals')
      .select('id, status, created_at, completed_at, rewarded_at, reward_amount')
      .eq('referrer_id', userData.referred_by)
      .eq('referred_id', userId)
      .single();

    // Récupérer l'historique des transactions liées au parrainage
    const { data: jobiHistorique, error: jobiHistoriqueError } = await dbService.supabase
      .from('user_jobi_historique')
      .select('montant, titre, description, date_creation')
      .eq('user_id', userData.referred_by)
      .like('description', `%${userId}%`)
      .like('titre', '%parrainage%')
      .order('date_creation', { ascending: false });

    // Calculer le montant total gagné par le parrain grâce à ce filleul
    let totalEarnings = 0;
    if (jobiHistorique && jobiHistorique.length > 0) {
      totalEarnings = jobiHistorique.reduce((sum: number, record: any) => sum + Number(record.montant), 0);
    } else if (referralData && referralData.status === 'rewarded' && referralData.reward_amount) {
      // Si pas d'historique mais le parrainage est récompensé, utiliser le montant de récompense
      totalEarnings = Number(referralData.reward_amount);
    }

    // Déchiffrer les données de profil du parrain
    const decryptedReferrerProfile = referrerProfile ? await decryptProfilDataAsync(referrerProfile) : null;

    // Déchiffrer les données utilisateur du parrain
    const decryptedReferrerUser = referrerUser ? await decryptUserDataAsync(referrerUser) : null;

    // Combiner les données
    const referrer = {
      id: referrerUser?.id || '',
      email: decryptedReferrerUser?.email || '',
      nom: decryptedReferrerProfile?.nom || null,
      prenom: decryptedReferrerProfile?.prenom || null,
      status: referralData?.status || 'pending',
      created_at: referralData?.created_at || null,
      completed_at: referralData?.completed_at || null,
      rewarded_at: referralData?.rewarded_at || null,
      reward_amount: referralData?.reward_amount || 0,
      total_earnings: totalEarnings
    };

    const response = {
      success: true,
      referrer
    };

    // Mettre en cache pour 2 minutes
    await redis.setex(cacheKey, 120, JSON.stringify(response));

    res.status(200).json(response);
    return;
  } catch (error) {
    logger.error('Erreur lors de la récupération du parrain:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du parrain',
      toastType: 'error'
    });
    return;
  }
}));

// Route pour modifier le solde (ajout ou retrait) Jobi
router.post('/solde', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;
    logger.info('Tentative de modification du solde Jobi:', { userId, body: req.body });

    if (!userId) {
      logger.error('Utilisateur non authentifié');
      res.status(401).json({
        success: false,
        error: 'Utilisateur non authentifié'
      });
      return;
    }

    const { montant, operation, titre, description } = req.body;

    // Vérification des paramètres requis
    if (!montant || !operation || !titre || !description) {
      logger.error('Paramètres manquants:', { montant, operation, titre, description });
      res.status(400).json({
        success: false,
        error: 'Paramètres manquants'
      });
      return;
    }

    // Si c'est une récompense de mission, vérifier le total journalier afin d'eviter les abus de spam et de hack : maximum 20 Jobi par jour soit 20 missions publiées par jour
    if (titre === 'Mission publiée' && operation === 'plus') {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { data: dailyRewards, error: rewardsError } = await dbService.supabase
        .from('user_jobi_historique')
        .select('montant')
        .eq('user_id', userId)
        .eq('titre', 'Mission publiée')
        .gte('date_creation', today.toISOString())
        .lt('date_creation', new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString());

      if (rewardsError) {
        logger.error('Erreur lors de la vérification des récompenses journalières:', rewardsError);
        throw rewardsError;
      }

      interface JobiHistoriqueRecord {
        montant: number;
      }

      const totalDailyRewards = Number((dailyRewards?.reduce((sum: number, record: JobiHistoriqueRecord) => sum + record.montant, 0) || 0).toFixed(1));

      logger.info('Total des récompenses journalières:', { totalDailyRewards });

      if (totalDailyRewards >= 20) {
        return res.status(400).json({
          success: false,
          error: 'Plus de récompenses journalières disponibles pour aujourd\'hui (20 Jobi maximum par jour pour 20 missions publiées)'
        });
      }
    }

    // Vérifier l'état actuel de profil_complet et le montant
    const { data: userData, error: userError } = await dbService.supabase
      .from('user_jobi')
      .select('profil_complet, montant')
      .eq('user_id', userId)
      .single();

    if (userError) {
      logger.error('Erreur lors de la vérification du profil complet:', userError);
      return res.status(500).json({
        success: false,
        error: 'Erreur lors de la vérification du profil complet'
      });
    }

    // Si l'utilisateur n'existe pas encore dans user_jobi, le créer
    if (!userData) {
      const { error: insertError } = await dbService.supabase
        .from('user_jobi')
        .insert({
          user_id: userId,
          montant: 0,
          profil_complet: false
        });

      if (insertError) {
        logger.error('Erreur lors de la création du profil jobi:', insertError);
        throw insertError;
      }
    }

    // Pour la soustraction, vérifier le pourcentage de complétion du profil
    if (operation === 'moins') {
      // Récupérer les données du profil pour calculer le pourcentage
      const { data: profilData, error: profilError } = await dbService.supabase
        .from('user_profil')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (profilError) {
        logger.error('Erreur lors de la récupération des données du profil:', profilError);
        throw profilError;
      }
    }

    // Calculer le nouveau montant
    const currentAmount = userData?.montant || 0;
    const montantFinal = operation === 'moins' ? -montant : montant;
    const newAmount = currentAmount + montantFinal;

    // Vérifier que le solde ne devient pas négatif
    if (newAmount < 0) {
      logger.error('Solde insuffisant:', { currentAmount, montantFinal, newAmount });
      res.status(400).json({
        success: false,
        error: 'Solde insuffisant'
      });
      return;
    }

    // Déterminer si nous devons mettre à jour le statut profil_complet
    let updateProfilComplet = false;
    let isProfilComplet = userData?.profil_complet || false;

    // Si c'est une opération liée au profil complet
    if (titre === 'Profil Complet' && operation === 'plus') {
      updateProfilComplet = true;
      isProfilComplet = true;
    } else if (titre === 'Profil Incomplet' && operation === 'moins') {
      updateProfilComplet = true;
      isProfilComplet = false;
    }

    // Mettre à jour le solde et éventuellement profil_complet
    const updateData: { montant: number; profil_complet?: boolean } = {
      montant: newAmount
    };

    if (updateProfilComplet) {
      updateData.profil_complet = isProfilComplet;
    }

    // Mettre à jour le solde avec UPDATE
    const { error: updateError } = await dbService.supabase
      .from('user_jobi')
      .update(updateData)
      .eq('user_id', userId)
      .single();

    if (updateError) {
      logger.error('Erreur lors de la mise à jour:', updateError);
      throw updateError;
    }

    // Ajouter l'entrée dans l'historique
    const { error: historyError } = await dbService.supabase
      .from('user_jobi_historique')
      .insert({
        user_id: userId,
        montant: montantFinal,
        titre,
        description
      });

    if (historyError) {
      logger.error('Erreur lors de l\'ajout dans l\'historique:', historyError);
      throw historyError;
    }

    // Supprimer le cache Redis Jobi
    const cacheKey = `jobi_balance_${userId}`;
    await redis.del(cacheKey);
    logger.info('Cache Redis supprimé, post solde :', { cacheKey });

    // Supprimer tous les caches d'historique pour cet utilisateur
    // Utilisation de KEYS pour trouver tous les caches d'historique de cet utilisateur
    const historiqueKeys = await redis.keys(`historique-jobi:${userId}:*`);
    if (historiqueKeys.length > 0) {
      await redis.del(historiqueKeys);
      logger.info('Cache d\'historique Jobi supprimé:', { userId, keysCount: historiqueKeys.length });
    }

    logger.info('Solde modifié avec succès:', { userId, montantFinal, newAmount });
    return res.json({ success: true });

  } catch (error: any) {
    logger.error('Erreur lors de la modification du solde:', {
      error: error.message,
      stack: error.stack
    });
    return res.status(500).json({
      success: false,
      error: error.message || 'Erreur lors de la modification du solde'
    });
  }
}));

// Route pour l'échange de Jobi entre utilisateurs
router.post('/echange_jobi', authMiddleware.authenticateToken, asyncHandler(async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { destinataire_usertag, montant, message } = req.body;

    if (!userId || !destinataire_usertag || !montant) {
      res.status(400).json({
        success: false,
        error: 'Paramètres manquants'
      });
      return;
    }

    logger.info('Tentative d\'échange de Jobi:', { destinataire_usertag, montant, message });

    // Vérifier que le destinataire existe et récupérer ses informations
    const { data: destinataireData, error: destinataireError } = await dbService.supabase
      .from('user_profil')
      .select('user_id, slug, nom, prenom')
      .eq('slug', destinataire_usertag)
      .single();

    if (destinataireError || !destinataireData) {
      res.status(404).json({
        success: false,
        error: 'Destinataire non trouvé'
      });
      return;
    }

    // Vérifier que l'utilisateur ne s'envoie pas à lui-même
    if (userId === destinataireData.user_id) {
      res.status(400).json({
        success: false,
        error: 'Vous ne pouvez pas vous envoyer des Jobi à vous-même'
      });
      return;
    }

    // Récupérer les informations de l'expéditeur (profil) pour la notification
    const { data: expediteurData, error: expediteurError } = await dbService.supabase
      .from('user_profil')
      .select('slug, nom, prenom')
      .eq('user_id', userId)
      .single();

    if (expediteurError || !expediteurData) {
      res.status(500).json({
        success: false,
        error: 'Erreur lors de la récupération des informations de l\'expéditeur'
      });
      return;
    }

    // Récupérer l'email de l'expéditeur
    const { data: expediteurUser, error: expediteurUserError } = await dbService.supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (expediteurUserError || !expediteurUser) {
      res.status(500).json({
        success: false,
        error: 'Erreur lors de la récupération de l\'email de l\'expéditeur'
      });
      return;
    }

    // Déchiffrer l'email de l'expéditeur
    const decryptedExpediteurUser = await decryptUserDataAsync(expediteurUser);

    // Récupérer l'email du destinataire
    const { data: destinataireUser, error: destinataireUserError } = await dbService.supabase
      .from('users')
      .select('email')
      .eq('id', destinataireData.user_id)
      .single();

    if (destinataireUserError || !destinataireUser) {
      res.status(500).json({
        success: false,
        error: 'Erreur lors de la récupération de l\'email du destinataire'
      });
      return;
    }

    // Déchiffrer l'email du destinataire
    const decryptedDestinataireUser = await decryptUserDataAsync(destinataireUser);

    // Récupérer le solde de l'expéditeur depuis la table user_jobi
    const { data: expediteurSolde, error: expediteurSoldeError } = await dbService.supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', userId)
      .single();

    if (expediteurSoldeError || !expediteurSolde) {
      res.status(500).json({
        success: false,
        error: 'Erreur lors de la vérification du solde'
      });
      return;
    }

    // Vérifier le solde
    if (expediteurSolde.montant < montant) {
      res.status(400).json({
        success: false,
        error: 'Solde insuffisant'
      });
      return;
    }

    // Déchiffrer les données de profil
    const decryptedExpediteurData = await decryptProfilDataAsync(expediteurData);
    const decryptedDestinataireData = await decryptProfilDataAsync(destinataireData);

    // Formater les noms pour affichage
    const formatName = (prenom: string | null, nom: string | null) => {
      if (!prenom) return null;
      const nomInitial = nom ? nom.charAt(0).toUpperCase() + '.' : '';
      return `${prenom} ${nomInitial}`;
    };

    const expediteurNomFormate = formatName(decryptedExpediteurData.prenom, decryptedExpediteurData.nom);
    const destinataireNomFormate = formatName(decryptedDestinataireData.prenom, decryptedDestinataireData.nom);

    // Enregistrer le solde avant transaction pour les deux utilisateurs
    const expediteurSoldeAvant = expediteurSolde.montant;

    // Récupérer le solde actuel du destinataire s'il existe
    let destinataireSoldeAvant = 0;
    const { data: destinataireSoldeData, error: destinataireSoldeCheckError } = await dbService.supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', destinataireData.user_id)
      .single();

    if (!destinataireSoldeCheckError && destinataireSoldeData) {
      destinataireSoldeAvant = destinataireSoldeData.montant;
    }

    // Commencer une transaction manuelle
    // 1. Soustraire les Jobi de l'expéditeur
    const nouveauSoldeExpediteur = expediteurSolde.montant - montant;
    const { error: updateExpediteurError } = await dbService.supabase
      .from('user_jobi')
      .update({ montant: nouveauSoldeExpediteur })
      .eq('user_id', userId);

    if (updateExpediteurError) {
      logger.error('Erreur lors de la soustraction de Jobi pour l\'expéditeur:', updateExpediteurError);
      return res.status(500).json({
        success: false,
        error: 'Erreur lors de la mise à jour du solde de l\'expéditeur'
      });
    }

    // 2. Ajouter les Jobi au destinataire
    // Vérifier si le destinataire a déjà un compte Jobi
    const { data: destinataireSolde, error: destinataireSoldeError } = await dbService.supabase
      .from('user_jobi')
      .select('montant')
      .eq('user_id', destinataireData.user_id)
      .single();

    if (destinataireSoldeError && destinataireSoldeError.code !== 'PGRST116') { // PGRST116 = Enregistrement non trouvé
      logger.error('Erreur lors de la vérification du solde du destinataire:', destinataireSoldeError);
      // Annuler la transaction en remettant les Jobi à l'expéditeur
      await dbService.supabase
        .from('user_jobi')
        .update({ montant: expediteurSolde.montant })
        .eq('user_id', userId);

      return res.status(500).json({
        success: false,
        error: 'Erreur lors de la vérification du solde du destinataire'
      });
    }

    // Variable pour stocker le nouveau solde du destinataire
    let nouveauSoldeDestinataire = montant;

    // Si le destinataire n'a pas de compte Jobi, en créer un
    if (!destinataireSolde) {
      const { error: insertError } = await dbService.supabase
        .from('user_jobi')
        .insert({
          user_id: destinataireData.user_id,
          montant: montant,
          profil_complet: false
        });

      if (insertError) {
        logger.error('Erreur lors de la création du compte Jobi du destinataire:', insertError);
        // Annuler la transaction en remettant les Jobi à l'expéditeur
        await dbService.supabase
          .from('user_jobi')
          .update({ montant: expediteurSolde.montant })
          .eq('user_id', userId);

        return res.status(500).json({
          success: false,
          error: 'Erreur lors de la création du compte Jobi du destinataire'
        });
      }
    } else {
      // Mettre à jour le solde du destinataire
      nouveauSoldeDestinataire = destinataireSolde.montant + montant;
      const { error: updateDestinataireError } = await dbService.supabase
        .from('user_jobi')
        .update({ montant: nouveauSoldeDestinataire })
        .eq('user_id', destinataireData.user_id);

      if (updateDestinataireError) {
        logger.error('Erreur lors de l\'ajout de Jobi pour le destinataire:', updateDestinataireError);
        // Annuler la transaction en remettant les Jobi à l'expéditeur
        await dbService.supabase
          .from('user_jobi')
          .update({ montant: expediteurSolde.montant })
          .eq('user_id', userId);

        return res.status(500).json({
          success: false,
          error: 'Erreur lors de la mise à jour du solde du destinataire'
        });
      }
    }

    // Créer les descriptions avec nom et prénom
    const descriptionExpediteur = destinataireNomFormate
      ? `Transfert de ${montant} Jobi à ${destinataireNomFormate} (@${destinataireData.slug})`
      : `Transfert de ${montant} Jobi à @${destinataireData.slug}`;

    const descriptionDestinataire = expediteurNomFormate
      ? `Réception de ${montant} Jobi de ${expediteurNomFormate} (@${expediteurData.slug})`
      : `Réception de ${montant} Jobi de @${expediteurData.slug}`;

    // Date actuelle
    const dateTransaction = new Date().toISOString();

    // 3. Ajouter des entrées dans l'historique pour les deux utilisateurs
    // Historique pour l'expéditeur
    const { error: histoExpediteurError } = await dbService.supabase
      .from('user_jobi_historique')
      .insert({
        user_id: userId,
        montant: -montant,
        titre: 'Transfert de Jobi',
        description: descriptionExpediteur,
        message: message || null,
        date_creation: dateTransaction
      });

    if (histoExpediteurError) {
      logger.error('Erreur lors de l\'ajout de l\'historique pour l\'expéditeur:', histoExpediteurError);
    }

    // Historique pour le destinataire
    const { error: histoDestinataireError } = await dbService.supabase
      .from('user_jobi_historique')
      .insert({
        user_id: destinataireData.user_id,
        montant: montant,
        titre: 'Réception de Jobi',
        description: descriptionDestinataire,
        message: message || null,
        date_creation: dateTransaction
      });

    if (histoDestinataireError) {
      logger.error('Erreur lors de l\'ajout de l\'historique pour le destinataire:', histoDestinataireError);
    }

    // Récupérer l'historique récent pour l'expéditeur (5 dernières transactions)
    const { data: expediteurHistorique, error: expediteurHistoriqueError } = await dbService.supabase
      .from('user_jobi_historique')
      .select('montant, date_creation, description')
      .eq('user_id', userId)
      .order('date_creation', { ascending: false })
      .limit(5);

    if (expediteurHistoriqueError) {
      logger.error('Erreur lors de la récupération de l\'historique de l\'expéditeur:', expediteurHistoriqueError);
    }

    // Récupérer l'historique récent pour le destinataire (5 dernières transactions)
    const { data: destinataireHistorique, error: destinataireHistoriqueError } = await dbService.supabase
      .from('user_jobi_historique')
      .select('montant, date_creation, description')
      .eq('user_id', destinataireData.user_id)
      .order('date_creation', { ascending: false })
      .limit(5);

    if (destinataireHistoriqueError) {
      logger.error('Erreur lors de la récupération de l\'historique du destinataire:', destinataireHistoriqueError);
    }

    // Formater l'historique pour les emails
    const formatHistoriqueEmail = (historique: any[] | null) => {
      if (!historique) return [];

      return historique.map(item => ({
        montant: item.montant,
        date: item.date_creation,
        description: item.description
      }));
    };

    // Supprimer les caches Redis
    const expediteurCacheKey = `jobi_balance_${userId}`;
    const destinataireCacheKey = `jobi_balance_${destinataireData.user_id}`;
    await redis.del([expediteurCacheKey, destinataireCacheKey]);

    // Supprimer les caches d'historique
    const expediteurHistoriqueKeys = await redis.keys(`historique-jobi:${userId}:*`);
    const destinataireHistoriqueKeys = await redis.keys(`historique-jobi:${destinataireData.user_id}:*`);
    if (expediteurHistoriqueKeys.length > 0) await redis.del(expediteurHistoriqueKeys);
    if (destinataireHistoriqueKeys.length > 0) await redis.del(destinataireHistoriqueKeys);

    // Envoyer une notification au destinataire
    const { error: notifError } = await dbService.supabase
      .from('user_notifications')
      .insert({
        user_id: destinataireData.user_id,
        type: 'jobi',
        title: 'Jobi reçus',
        content: `Vous avez reçu ${montant} Jobi de ${expediteurNomFormate || `@${expediteurData.slug}`}.`,
        is_read: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (notifError) {
      logger.error('Erreur lors de l\'envoi de la notification au destinataire:', notifError);
    }

    // Envoyer des emails à l'expéditeur et au destinataire
    try {
      await sendJobiExchangeEmail(
        decryptedExpediteurUser.email,
        decryptedDestinataireUser.email,
        montant,
        expediteurData.slug,
        destinataireData.slug,
        expediteurNomFormate,
        destinataireNomFormate,
        expediteurSoldeAvant,
        nouveauSoldeExpediteur,
        destinataireSoldeAvant,
        nouveauSoldeDestinataire,
        formatHistoriqueEmail(expediteurHistorique),
        formatHistoriqueEmail(destinataireHistorique),
        message
      );
    } catch (emailError) {
      logger.error('Erreur lors de l\'envoi des emails de notification:', emailError);
      // On ne bloque pas la transaction si l'envoi d'email échoue
    }

    // Journaliser l'action utilisateur (pour l'expéditeur)
    await logUserActivity(
      userId || '',
      'jobi_transfer',
      undefined,
      'jobi',
      {
        montant: montant,
        destinataire_id: destinataireData.user_id,
        destinataire_slug: destinataireData.slug,
        solde_final: nouveauSoldeExpediteur
      },
      getIpFromRequest(req)
    );

    // Journaliser également l'action pour le destinataire
    await logUserActivity(
      destinataireData.user_id || '',
      'jobi_receive',
      undefined,
      'jobi',
      {
        montant: montant,
        expediteur_id: userId,
        expediteur_slug: expediteurData.slug,
        solde_final: nouveauSoldeDestinataire
      },
      getIpFromRequest(req)
    );

    res.status(200).json({
      success: true,
      message: 'Transfert de Jobi effectué avec succès',
      montant_transfere: montant,
      destinataire: {
        id: destinataireData.user_id,
        slug: destinataireData.slug
      },
      nouveau_solde: nouveauSoldeExpediteur
    });
  } catch (error: any) {
    logger.error('Erreur lors de l\'échange de Jobi:', error);
    res.status(500).json({
      success: false,
      error: 'Une erreur est survenue lors de l\'échange'
    });
  }
}));

export const jobiRoutes = router;