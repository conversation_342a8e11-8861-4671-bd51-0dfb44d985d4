import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { authMiddleware } from '../middleware/authMiddleware';
import { validateRequest } from '../middleware/validation';
import { z } from 'zod';
import logger from '../utils/logger';
import { redis } from '../config/redis';
import rateLimit from 'express-rate-limit';
import { getUserSubscriptionLimits } from './configSubscriptions';
import { checkProfileVisibility } from '../middleware/checkProfileVisibility';
import * as serviceCategoriesService from '../services/serviceCategoriesService';
import { decryptProfilDataAsync } from '../utils/encryption';

const router = Router();

// Configuration du rate limiter pour les services
const servicesLimiter = rateLimit({
  windowMs: 1 * 30 * 1000, // 30 secondes
  max: 100, // 100 requêtes par IP
  message: { error: 'Trop de requêtes, veuillez réessayer dans 1 minute' },
  standardHeaders: true,
  legacyHeaders: false
});

// Application du rate limiter avant l'authentification
router.use(servicesLimiter);

// Routes publiques (sans authentification)
// Route pour la recherche générale de services (publique)
// Route pour récupérer les prestataires par service et ville (pour pages SEO dynamiques)
router.get('/providers/:service/:ville', async (req, res) => {
  try {
    const { service, ville } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // Vérifier si les données sont dans le cache
    const cacheKey = `providers:${service}:${ville}:page:${page}:limit:${limit}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.info('Prestataires récupérés depuis le cache:', { service, ville, page, limit });
      res.json(JSON.parse(cachedData));
      return;
    }

    // Normaliser le nom du service pour la recherche
    const serviceNormalized = service.toLowerCase().replace(/-/g, ' ');

    // Récupérer les catégories et sous-catégories pour la correspondance
    const categoriesData = await serviceCategoriesService.getServiceCategories();
    const subcategoriesData = await serviceCategoriesService.getServiceSubcategories();

    // Trouver la sous-catégorie correspondante au service dans l'URL
    let targetSubcategory = null;

    // D'abord chercher par nom exact de sous-catégorie
    targetSubcategory = subcategoriesData.find((sub: any) =>
      sub.nom.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim() ===
      serviceNormalized.replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim()
    );

    // Si pas trouvé, chercher par synonymes
    if (!targetSubcategory) {
      targetSubcategory = subcategoriesData.find((sub: any) =>
        sub.synonymes && sub.synonymes.some((syn: string) =>
          syn.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim() ===
          serviceNormalized.replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim()
        )
      );
    }

    // Si pas trouvé, chercher par correspondance partielle dans les noms
    if (!targetSubcategory) {
      targetSubcategory = subcategoriesData.find((sub: any) => {
        const subName = sub.nom.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
        const searchWords = serviceNormalized.split(' ').filter(word => word.length > 2);
        if (searchWords.length > 0) {
          const matchingWords = searchWords.filter(word => subName.includes(word));
          return matchingWords.length / searchWords.length >= 0.6; // 60% de correspondance
        }
        return false;
      });
    }

    // Rechercher les prestataires avec services correspondants dans la ville
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    // Étape 1: Récupérer les profils dans la ville
    let profilQuery = supabase
      .from('user_profil')
      .select(`
        user_id,
        slug,
        nom,
        prenom,
        ville,
        code_postal,
        bio,
        photo_url,
        slogan,
        updated_at,
        intervention_zone,
        users!inner(
          user_type,
          profil_actif,
          profil_verifier
        )
      `)
      .eq('profil_visible', true)
      .eq('users.profil_actif', true)
      .eq('users.user_type', 'jobbeur');

    // Récupérer tous les profils (on filtrera par ville après déchiffrement)
    const { data: profils, error: profilError } = await profilQuery;

    if (profilError) {
      logger.error('Erreur lors de la récupération des profils:', profilError);
      res.status(500).json({ error: 'Erreur lors de la récupération des prestataires' });
      return;
    }

    if (!profils || profils.length === 0) {
      res.json({
        providers: [],
        pagination: { page: parseInt(page as string), limit: parseInt(limit as string), total: 0, totalPages: 0 }
      });
      return;
    }

    // Déchiffrer les profils et filtrer par ville
    let filteredProfils = await Promise.all(profils.map(profil => decryptProfilDataAsync(profil)));

    // Filtrer par ville après déchiffrement
    if (ville) {
      const villeNormalized = ville.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
        .trim();

      filteredProfils = filteredProfils.filter(profil => {
        if (!profil.ville) return false;
        const profilVille = profil.ville.toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .trim();
        return profilVille.includes(villeNormalized);
      });
    }

    if (filteredProfils.length === 0) {
      res.json({
        providers: [],
        pagination: { page: parseInt(page as string), limit: parseInt(limit as string), total: 0, totalPages: 0 }
      });
      return;
    }

    // Étape 2: Récupérer les services pour ces utilisateurs
    const userIds = filteredProfils.map(p => p.user_id);
    const { data: services, error: serviceError } = await supabase
      .from('user_services')
      .select(`
        id,
        user_id,
        titre,
        description,
        category_id,
        subcategory_id,
        tarif_horaire,
        statut
      `)
      .in('user_id', userIds)
      .eq('statut', 'actif');

    // Étape 2.5: Récupérer les avis pour ces utilisateurs
    const { data: reviews, error: reviewError } = await supabase
      .from('user_reviews')
      .select('*')
      .in('target_user_id', userIds)
      .eq('statut', 'visible')
      .order('created_at', { ascending: false });

    if (serviceError) {
      logger.error('Erreur lors de la récupération des services:', serviceError);
      res.status(500).json({ error: 'Erreur lors de la récupération des prestataires' });
      return;
    }

    // Étape 3: Combiner les données et filtrer par service
    const providers = filteredProfils.map(profil => {
      // Les données sont déjà déchiffrées dans filteredProfils
        const decryptedProfil = profil;
        
        const userServices = services?.filter(service => service.user_id === decryptedProfil.user_id) || [];
        const userReviews = reviews?.filter(review => review.target_user_id === decryptedProfil.user_id) || [];
        return {
          ...decryptedProfil,
          nom: decryptedProfil.nom ? decryptedProfil.nom.charAt(0) + '.' : null,
          user_services: userServices,
          user_reviews: userReviews
        };
    }).filter(provider => provider.user_services.length > 0);

    // Filtrer les prestataires qui ont des services correspondants
    const filteredProviders = providers.filter(provider => {
      return provider.user_services.some((service: any) => {
        // Si on a trouvé une sous-catégorie correspondante, filtrer par sous-catégorie
        if (targetSubcategory) {
          return service.subcategory_id === targetSubcategory.id;
        }

        // Sinon, utiliser la recherche par titre/description comme fallback
        const serviceTitle = service.titre.toLowerCase();
        const serviceDesc = service.description.toLowerCase();

        // Recherche exacte
        if (serviceTitle.includes(serviceNormalized) || serviceDesc.includes(serviceNormalized)) {
          return true;
        }

        // Recherche par mots-clés pour une correspondance plus flexible
        const searchWords = serviceNormalized.split(' ').filter(word => word.length > 2);
        if (searchWords.length > 0) {
          // Au moins 60% des mots doivent correspondre
          const matchingWords = searchWords.filter(word =>
            serviceTitle.includes(word) || serviceDesc.includes(word)
          );
          const matchPercentage = matchingWords.length / searchWords.length;
          if (matchPercentage >= 0.6) {
            return true;
          }
        }

        // Recherche spécifique pour les services web/informatique
        if (serviceNormalized.includes('site') || serviceNormalized.includes('web') || serviceNormalized.includes('internet')) {
          if (serviceTitle.includes('site') || serviceTitle.includes('web') || serviceTitle.includes('internet') ||
              serviceDesc.includes('site') || serviceDesc.includes('web') || serviceDesc.includes('internet')) {
            return true;
          }
        }

        return false;
      });
    });

    // Fonction pour calculer le score de profil complet (même logique que dans search)
    const calculateProfileCompleteness = (provider: any) => {
      let score = 0;

      // Photo de profil (20 points)
      if (provider.photo_url && provider.photo_url.trim() !== '') {
        score += 20;
      }

      // Slogan (15 points)
      if (provider.slogan && provider.slogan.trim() !== '') {
        score += 15;
      }

      // Bio (15 points)
      if (provider.bio && provider.bio.trim() !== '' && provider.bio.length > 50) {
        score += 15;
      }

      // Avis clients (25 points)
      const reviews = provider.user_reviews || [];
      if (reviews.length > 0) {
        score += 15; // Base pour avoir des avis
        if (reviews.length >= 3) score += 5; // Bonus pour 3+ avis
        if (reviews.length >= 5) score += 5; // Bonus pour 5+ avis
      }

      // Vérifications (15 points)
      if (provider.users && provider.users[0]) {
        if (provider.users[0].profil_verifier) score += 5;
        // Note: les autres vérifications ne sont pas disponibles dans cette route
      }

      // Services multiples (10 points)
      const servicesCount = provider.user_services ? provider.user_services.length : 0;
      if (servicesCount >= 2) score += 5;
      if (servicesCount >= 4) score += 5;

      return score;
    };

    // Trier par profil complet en premier, puis aléatoirement
    filteredProviders.sort((a: any, b: any) => {
      const completenessA = calculateProfileCompleteness(a);
      const completenessB = calculateProfileCompleteness(b);

      if (completenessA !== completenessB) {
        return completenessB - completenessA; // Plus complet en premier
      }

      // Si même niveau de complétude, tri aléatoire pour les profils complets
      if (completenessA >= 40 && completenessB >= 40) {
        return Math.random() - 0.5; // Tri aléatoire
      }

      // Sinon tri par date de mise à jour
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    });

    // Appliquer la pagination
    const paginatedProviders = filteredProviders.slice(offset, offset + parseInt(limit as string));

    const result = {
      providers: paginatedProviders,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total: filteredProviders.length,
        totalPages: Math.ceil(filteredProviders.length / parseInt(limit as string))
      }
    };

    // Mettre en cache pour 30 minutes
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 1800);

    res.json(result);
  } catch (error) {
    logger.error('Erreur lors de la récupération des prestataires:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des prestataires' });
  }
});

// Route pour récupérer les dernières demandes par service et ville
router.get('/recent-missions/:service/:ville', async (req, res) => {
  try {
    const { service, ville } = req.params;
    const { limit = 5 } = req.query;

    // Vérifier si les données sont dans le cache
    const cacheKey = `recent_requests:${service}:${ville}:limit:${limit}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.info('Demandes récentes récupérées depuis le cache:', { service, ville, limit });
      res.json(JSON.parse(cachedData));
      return;
    }

    // Normaliser le nom du service pour la recherche (avec suppression des accents)
    const serviceNormalized = service.toLowerCase()
      .replace(/-/g, ' ')
      .replace(/'/g, '')
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .trim();
    const villeNormalized = ville.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').trim();

    // Rechercher les missions récentes correspondantes
    const { data: missions, error } = await supabase
      .from('user_missions')
      .select(`
        id,
        titre,
        description,
        budget,
        ville,
        code_postal,
        date_creation,
        category_id,
        subcategory_id
      `)
      .in('statut', ['en_cours', 'terminee'])
      .ilike('ville', `%${villeNormalized}%`)
      .order('date_creation', { ascending: false })
      .limit(parseInt(limit as string) * 2); // Récupérer plus pour filtrer

    if (error) {
      logger.error('Erreur lors de la récupération des demandes récentes:', error);
      res.status(500).json({ error: 'Erreur lors de la récupération des demandes' });
      return;
    }

    // Récupérer les sous-catégories pour un filtrage plus précis
    const { getServiceSubcategories } = require('../services/serviceCategoriesService');
    const subcategoriesData = await getServiceSubcategories();

    // Trouver la sous-catégorie correspondant au service recherché
    let targetSubcategory = null;
    if (subcategoriesData) {
      // Recherche par nom exact ou synonymes
      targetSubcategory = subcategoriesData.find((sub: any) => {
        const subNameNormalized = sub.nom.toLowerCase()
          .replace(/-/g, ' ')
          .replace(/'/g, '')
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .trim();
        const serviceMatch = subNameNormalized === serviceNormalized;



        // Vérifier aussi les synonymes
        let synonymMatch = false;
        if (sub.synonymes) {
          synonymMatch = sub.synonymes.some((syn: string) => {
            const synNormalized = syn.toLowerCase()
              .replace(/-/g, ' ')
              .replace(/'/g, '')
              .normalize('NFD')
              .replace(/[\u0300-\u036f]/g, '')
              .trim();

            const exactMatch = synNormalized === serviceNormalized;
            // Éviter les correspondances trop courtes qui créent des faux positifs
            const minLength = 5; // Minimum 5 caractères pour éviter "is" dans "ebenisterie"
            const includesMatch = (synNormalized.length >= minLength && serviceNormalized.includes(synNormalized)) ||
                                 (serviceNormalized.length >= minLength && synNormalized.includes(serviceNormalized));



            return exactMatch || includesMatch;
          });
        }

        const finalMatch = serviceMatch || synonymMatch;



        return finalMatch;
      });


    }

    // Filtrer les missions qui correspondent au service
    const filteredMissions = missions?.filter(mission => {
      // Filtrage par catégorie/sous-catégorie si trouvée
      if (targetSubcategory) {
        // Conversion en string pour éviter les problèmes de type
        const categoryMatch = String(mission.category_id) === String(targetSubcategory.categoryId);
        const subcategoryMatch = String(mission.subcategory_id) === String(targetSubcategory.id);

        if (categoryMatch && subcategoryMatch) {
          return true;
        }
      }

      // Filtrage textuel en fallback
      const missionTitle = mission.titre.toLowerCase();
      const missionDesc = mission.description.toLowerCase();
      const textMatch = missionTitle.includes(serviceNormalized) || missionDesc.includes(serviceNormalized);

      return textMatch;
    }).slice(0, parseInt(limit as string)) || [];

    // Mettre en cache pour 15 minutes
    await redis.set(cacheKey, JSON.stringify(filteredMissions), 'EX', 900);

    res.json(filteredMissions);
  } catch (error) {
    logger.error('Erreur lors de la récupération des demandes récentes:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des demandes' });
  }
});

// Route pour la recherche générale de services
router.get('/search', async (req, res) => {
  try {
    const {
      q,
      category,
      subcategory,
      city,
      budget,
      page = 1,
      limit = 20,
      sortBy = 'relevance'
    } = req.query;

    // Log pour diagnostic
    logger.info('Recherche de services:', { q, category, subcategory, city, budget, page, limit, sortBy });

    // Vérifier si les données sont dans le cache
    const cacheKey = `search:${JSON.stringify(req.query)}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.info('Résultats de recherche récupérés depuis le cache');
      res.json(JSON.parse(cachedData));
      return;
    }

    // Étape 1: Récupérer les profils de base avec filtres
    let profilQuery = supabase
      .from('user_profil')
      .select(`
        user_id,
        slug,
        nom,
        prenom,
        ville,
        code_postal,
        bio,
        photo_url,
        slogan,
        updated_at,
        intervention_zone,
        users!inner(
          user_type,
          profil_actif,
          profil_verifier
        )
      `)
      .eq('profil_visible', true)
      .eq('users.profil_actif', true)
      .eq('users.user_type', 'jobbeur');

    // Récupérer tous les profils (on filtrera par ville après déchiffrement)
    const { data: profils, error: profilError } = await profilQuery;

    if (profilError) {
      logger.error('Erreur lors de la récupération des profils:', profilError);
      res.status(500).json({ error: 'Erreur lors de la recherche' });
      return;
    }

    // Déchiffrer les données des profils récupérés
    let decryptedProfils = profils ? await Promise.all(profils.map(profil => decryptProfilDataAsync(profil))) : [];

    // Filtrer par ville après déchiffrement si nécessaire
    if (city && typeof city === 'string') {
      const villeNormalized = city.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
        .trim();

      logger.info('Filtrage par ville:', {
        villeRecherchee: city,
        villeNormalized,
        totalProfilsAvantFiltre: decryptedProfils.length
      });

      decryptedProfils = decryptedProfils.filter(profil => {
        if (!profil.ville) return false;
        const profilVille = profil.ville.toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .trim();
        const match = profilVille.includes(villeNormalized);

        if (match) {
          logger.info('Profil trouvé pour la ville:', {
            profilVille: profil.ville,
            profilVilleNormalized: profilVille,
            userId: profil.user_id
          });
        }

        return match;
      });

      logger.info('Résultat filtrage par ville:', {
        totalProfilsApresFiltre: decryptedProfils.length
      });
    }

    // Si aucun profil trouvé avec une ville spécifique, retourner un résultat vide
    if (city && (!decryptedProfils || decryptedProfils.length === 0)) {
      res.json({
        providers: [],
        pagination: { page: parseInt(page as string), limit: parseInt(limit as string), total: 0, totalPages: 0 },
        filters: { q, category, subcategory, city, budget, sortBy },
        message: `Aucun jobbeur trouvé à ${city}`
      });
      return;
    }

    // Si aucun profil trouvé avec les critères spécifiques, ou recherche trop vague
    if ((!decryptedProfils || decryptedProfils.length === 0) && (!q && !city && !category && !subcategory)) {
      // Récupérer une liste aléatoire limitée de tous les jobbeurs actifs
      const { data: randomProfils, error: randomError } = await supabase
        .from('user_profil')
        .select(`
          user_id,
          slug,
          nom,
          prenom,
          ville,
          code_postal,
          bio,
          photo_url,
          slogan,
          updated_at,
          intervention_zone,
          users!inner(
            user_type,
            profil_actif,
            profil_verifier
          )
        `)
        .eq('profil_visible', true)
        .eq('users.profil_actif', true)
        .eq('users.user_type', 'jobbeur')
        .limit(50); // Récupérer 50 pour avoir plus de choix pour la randomisation

      if (randomError) {
        logger.error('Erreur lors de la récupération des profils aléatoires:', randomError);
        res.status(500).json({ error: 'Erreur lors de la recherche' });
        return;
      }

      if (!randomProfils || randomProfils.length === 0) {
        res.json({
          providers: [],
          pagination: { page: parseInt(page as string), limit: parseInt(limit as string), total: 0, totalPages: 0 },
          filters: { q, category, subcategory, city, budget, sortBy },
          isRandomSample: true
        });
        return;
      }

      // Déchiffrer les profils aléatoires
      const decryptedRandomProfils = await Promise.all(randomProfils.map(profil => decryptProfilDataAsync(profil)));

      // Mélanger aléatoirement et prendre seulement 10
      const shuffledProfils = decryptedRandomProfils.sort(() => Math.random() - 0.5).slice(0, 10);

      // Récupérer les services et avis pour ces profils
      const randomUserIds = shuffledProfils.map(p => p.user_id);

      const [randomServices, randomReviews, randomFeaturedPhotos, randomGalleries] = await Promise.all([
        supabase
          .from('user_services')
          .select('*')
          .in('user_id', randomUserIds)
          .eq('statut', 'actif'),
        supabase
          .from('user_reviews')
          .select('*')
          .in('target_user_id', randomUserIds)
          .eq('statut', 'visible')
          .order('created_at', { ascending: false }),
        supabase
          .from('user_featured_photos')
          .select(`
            id,
            user_id,
            photo_url,
            caption,
            created_at
          `)
          .in('user_id', randomUserIds)
          .order('created_at', { ascending: false }),
        supabase
          .from('user_gallery')
          .select(`
            id,
            user_id,
            name,
            description,
            cover_image,
            photos:user_gallery_photos (
              id,
              photo_url,
              caption,
              order_index
            )
          `)
          .in('user_id', randomUserIds)
          .eq('status', 'actif')
          .order('created_at', { ascending: false })
      ]);

      // Combiner les données
      const randomProviders = shuffledProfils.map(profil => {
        // Les données sont déjà déchiffrées dans shuffledProfils
        const decryptedProfil = profil;
        
        const userServices = randomServices.data?.filter(service => service.user_id === decryptedProfil.user_id) || [];
        const userReviews = randomReviews.data?.filter(review => review.target_user_id === decryptedProfil.user_id) || [];
        const userFeaturedPhotos = randomFeaturedPhotos.data?.filter(photo => photo.user_id === decryptedProfil.user_id) || [];
        const userGalleries = randomGalleries.data?.filter(gallery => gallery.user_id === decryptedProfil.user_id) || [];
        return {
          ...decryptedProfil,
          nom: decryptedProfil.nom ? decryptedProfil.nom.charAt(0) + '.' : null,
          featured_photos: userFeaturedPhotos,
          galleries: userGalleries,
          users: [{
            ...decryptedProfil.users[0],
            user_services: userServices,
            user_reviews: userReviews
          }]
        };
      }).filter(provider => provider.users[0].user_services.length > 0); // Garder seulement ceux avec des services

      res.json({
        providers: randomProviders,
        pagination: {
          page: 1,
          limit: randomProviders.length,
          total: randomProviders.length,
          totalPages: 1
        },
        filters: { q, category, subcategory, city, budget, sortBy },
        isRandomSample: true,
        message: "Voici une sélection aléatoire de jobbeurs disponibles"
      });
      return;
    }

    // Étape 2: Récupérer les services pour ces utilisateurs
    const userIds = decryptedProfils.map(p => p.user_id);
    let serviceQuery = supabase
      .from('user_services')
      .select('*')
      .in('user_id', userIds)
      .eq('statut', 'actif');

    // Étape 2.5: Récupérer les avis pour ces utilisateurs
    const { data: reviews, error: reviewError } = await supabase
      .from('user_reviews')
      .select('*')
      .in('target_user_id', userIds)
      .eq('statut', 'visible')
      .order('created_at', { ascending: false });

    if (reviewError) {
      logger.error('Erreur lors de la récupération des avis:', reviewError);
      // Ne pas bloquer la recherche si les avis ne peuvent pas être récupérés
    }

    // Étape 2.6: Récupérer les photos mises en avant pour ces utilisateurs
    const { data: featuredPhotos, error: featuredError } = await supabase
      .from('user_featured_photos')
      .select(`
        id,
        user_id,
        photo_url,
        caption,
        created_at
      `)
      .in('user_id', userIds)
      .order('created_at', { ascending: false });

    if (featuredError) {
      logger.error('Erreur lors de la récupération des photos mises en avant:', featuredError);
      // Ne pas bloquer la recherche si les photos ne peuvent pas être récupérées
    }

    // Étape 2.7: Récupérer les galeries actives pour ces utilisateurs
    const { data: galleries, error: galleriesError } = await supabase
      .from('user_gallery')
      .select(`
        id,
        user_id,
        name,
        description,
        cover_image,
        photos:user_gallery_photos (
          id,
          photo_url,
          caption,
          order_index
        )
      `)
      .in('user_id', userIds)
      .eq('status', 'actif')
      .order('created_at', { ascending: false });

    if (galleriesError) {
      logger.error('Erreur lors de la récupération des galeries:', galleriesError);
      // Ne pas bloquer la recherche si les galeries ne peuvent pas être récupérées
    }

    // Appliquer les filtres sur les services
    // Note: category et subcategory sont maintenant des noms, pas des IDs
    // On va filtrer après avoir récupéré les services

    const { data: services, error: serviceError } = await serviceQuery;

    if (serviceError) {
      logger.error('Erreur lors de la récupération des services:', serviceError);
      res.status(500).json({ error: 'Erreur lors de la recherche' });
      return;
    }

    // Étape 3: Utiliser les catégories et sous-catégories depuis le service
    const { getServiceCategories, getServiceSubcategories } = require('../services/serviceCategoriesService');
    const categoriesData = await getServiceCategories();
    const subcategoriesData = await getServiceSubcategories();

    // Étape 4: Combiner les données et filtrer par catégorie/sous-catégorie
    const providers = decryptedProfils.map(profil => {
      // Les données sont déjà déchiffrées
      const decryptedProfil = profil;
      
      let userServices = services?.filter(service => service.user_id === decryptedProfil.user_id) || [];
      let userReviews = reviews?.filter(review => review.target_user_id === decryptedProfil.user_id) || [];
      let userFeaturedPhotos = featuredPhotos?.filter(photo => photo.user_id === decryptedProfil.user_id) || [];
      let userGalleries = galleries?.filter(gallery => gallery.user_id === decryptedProfil.user_id) || [];

      // Filtrer par catégorie si spécifiée
      if (category && categoriesData) {
        const categoryObj = categoriesData.find((cat: any) => cat.nom === category);
        if (categoryObj) {
          userServices = userServices.filter(service => service.category_id === categoryObj.id);
        }
      }

      // Filtrer par sous-catégorie si spécifiée
      if (subcategory && subcategoriesData) {
        const subcategoryObj = subcategoriesData.find((sub: any) => sub.nom === subcategory);
        if (subcategoryObj) {
          userServices = userServices.filter(service => service.subcategory_id === subcategoryObj.id);
        }
      }

      return {
        ...decryptedProfil,
        nom: decryptedProfil.nom ? decryptedProfil.nom.charAt(0) + '.' : null,
        featured_photos: userFeaturedPhotos,
        galleries: userGalleries,
        users: [{
          ...decryptedProfil.users[0],
          user_services: userServices,
          user_reviews: userReviews
        }]
      };
    }).filter(provider => provider.users[0].user_services.length > 0);

    // Filtrer par terme de recherche si fourni
    let filteredProviders = providers;
    if (q && typeof q === 'string') {
      // Normaliser le terme de recherche (insensible aux accents)
      const searchTerm = q.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').trim();

      filteredProviders = filteredProviders.filter((provider: any) => {
        // Normaliser tous les champs de recherche
        const nameMatch = `${provider.prenom} ${provider.nom}`.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTerm);
        const cityMatch = provider.ville?.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTerm);
        const bioMatch = provider.bio?.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTerm);
        const sloganMatch = provider.slogan?.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').includes(searchTerm);

        // Recherche dans les services avec correspondance partielle améliorée (insensible aux accents)
        const serviceMatch = provider.users[0].user_services.some((service: any) => {
          const titreNormalized = service.titre.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
          const descriptionNormalized = service.description?.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '') || '';

          const titreMatch = titreNormalized.includes(searchTerm);
          const descriptionMatch = descriptionNormalized.includes(searchTerm);

          // Recherche par mots-clés séparés pour les termes composés avec correspondance flexible
          const searchWords = searchTerm.split(' ').filter(word => word.length > 2);
          let wordMatch = false;
          if (searchWords.length > 0) {
            // Au moins 60% des mots doivent correspondre
            const matchingWords = searchWords.filter(word =>
              titreNormalized.includes(word) || descriptionNormalized.includes(word)
            );
            const matchPercentage = matchingWords.length / searchWords.length;
            wordMatch = matchPercentage >= 0.6;
          }

          // Recherche spécifique pour les services web/informatique
          let webMatch = false;
          if (searchTerm.includes('site') || searchTerm.includes('web') || searchTerm.includes('internet')) {
            webMatch = titreNormalized.includes('site') || titreNormalized.includes('web') ||
                      titreNormalized.includes('internet') || descriptionNormalized.includes('site') ||
                      descriptionNormalized.includes('web') || descriptionNormalized.includes('internet');
          }

          // Recherche par correspondance de sous-catégorie
          let subcategoryMatch = false;
          if (service.subcategory_id && subcategoriesData) {
            const subcategory = subcategoriesData.find((sub: any) => sub.id === service.subcategory_id);
            if (subcategory) {
              // Vérifier si le terme de recherche correspond au nom de la sous-catégorie (insensible aux accents)
              const subcategoryNameNormalized = subcategory.nom.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
              const subcategoryNameMatch = subcategoryNameNormalized.includes(searchTerm);

              // Vérifier les synonymes de la sous-catégorie (insensible aux accents)
              const synonymMatch = subcategory.synonymes && subcategory.synonymes.some((syn: string) => {
                const synNormalized = syn.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '');
                return synNormalized.includes(searchTerm) || searchTerm.includes(synNormalized);
              });

              subcategoryMatch = subcategoryNameMatch || synonymMatch;
            }
          }

          return titreMatch || descriptionMatch || wordMatch || webMatch || subcategoryMatch;
        });

        return nameMatch || cityMatch || bioMatch || sloganMatch || serviceMatch;
      });
    }

    // Filtrer par budget si fourni
    if (budget) {
      const [minBudget, maxBudget] = (budget as string).includes('-')
        ? (budget as string).split('-').map(b => parseInt(b))
        : [parseInt(budget as string), Infinity];

      filteredProviders = filteredProviders.filter((provider: any) => {
        return provider.users[0].user_services.some((service: any) => {
          const tarif = service.tarif_horaire;
          return tarif >= minBudget && (maxBudget === Infinity || tarif <= maxBudget);
        });
      });
    }

    // Fonction pour calculer le score de profil complet
    const calculateProfileCompleteness = (provider: any) => {
      let score = 0;

      // Photo de profil (20 points)
      if (provider.photo_url && provider.photo_url.trim() !== '') {
        score += 20;
      }

      // Slogan (15 points)
      if (provider.slogan && provider.slogan.trim() !== '') {
        score += 15;
      }

      // Bio (15 points)
      if (provider.bio && provider.bio.trim() !== '' && provider.bio.length > 50) {
        score += 15;
      }

      // Avis clients (25 points)
      const reviews = provider.users[0].user_reviews || [];
      if (reviews.length > 0) {
        score += 15; // Base pour avoir des avis
        if (reviews.length >= 3) score += 5; // Bonus pour 3+ avis
        if (reviews.length >= 5) score += 5; // Bonus pour 5+ avis
      }

      // Vérifications (15 points)
      if (provider.users[0].profil_verifier) score += 5;
      if (provider.users[0].identite_verifier) score += 5;
      if (provider.users[0].entreprise_verifier) score += 3;
      if (provider.users[0].assurance_verifier) score += 2;

      // Services multiples (10 points)
      const servicesCount = provider.users[0].user_services.length;
      if (servicesCount >= 2) score += 5;
      if (servicesCount >= 4) score += 5;

      return score;
    };

    // Tri des résultats avec priorité aux profils complets
    if (sortBy === 'price_asc') {
      filteredProviders.sort((a: any, b: any) => {
        // D'abord par complétude du profil
        const completenessA = calculateProfileCompleteness(a);
        const completenessB = calculateProfileCompleteness(b);

        if (completenessA !== completenessB) {
          return completenessB - completenessA; // Plus complet en premier
        }

        // Ensuite par prix
        const priceA = Math.min(...a.users[0].user_services.map((s: any) => s.tarif_horaire));
        const priceB = Math.min(...b.users[0].user_services.map((s: any) => s.tarif_horaire));
        return priceA - priceB;
      });
    } else if (sortBy === 'price_desc') {
      filteredProviders.sort((a: any, b: any) => {
        // D'abord par complétude du profil
        const completenessA = calculateProfileCompleteness(a);
        const completenessB = calculateProfileCompleteness(b);

        if (completenessA !== completenessB) {
          return completenessB - completenessA; // Plus complet en premier
        }

        // Ensuite par prix
        const priceA = Math.min(...a.users[0].user_services.map((s: any) => s.tarif_horaire));
        const priceB = Math.min(...b.users[0].user_services.map((s: any) => s.tarif_horaire));
        return priceB - priceA;
      });
    } else {
      // Tri par défaut : profils complets en premier, puis par date de mise à jour
      filteredProviders.sort((a: any, b: any) => {
        const completenessA = calculateProfileCompleteness(a);
        const completenessB = calculateProfileCompleteness(b);

        if (completenessA !== completenessB) {
          return completenessB - completenessA; // Plus complet en premier
        }

        // Si même niveau de complétude, tri aléatoire pour les profils complets
        if (completenessA >= 60 && completenessB >= 60) {
          return Math.random() - 0.5; // Tri aléatoire
        }

        // Sinon tri par date de mise à jour
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      });
    }

    // Compter le total pour la pagination
    const total = filteredProviders.length;
    const totalPages = Math.ceil(total / parseInt(limit as string));

    // Appliquer la pagination
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
    const paginatedProviders = filteredProviders.slice(offset, offset + parseInt(limit as string));

    const result = {
      providers: paginatedProviders,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        totalPages
      },
      filters: {
        q,
        category,
        subcategory,
        city,
        budget,
        sortBy
      }
    };

    // Mettre en cache pour 15 minutes
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 900);

    res.json(result);
  } catch (error) {
    logger.error('Erreur lors de la recherche de services:', error);
    res.status(500).json({ error: 'Erreur lors de la recherche' });
  }
});

// Middleware d'authentification pour les routes protégées uniquement
router.use(authMiddleware.authenticateToken);

// Schema de validation pour les services
const serviceSchema = z.object({
  category_id: z.string(),
  subcategory_id: z.string(),
  titre: z.string(),
  description: z.string()
    .transform(val => {
      // Nettoyer les balises HTML pour le comptage
      const cleanText = val.replace(/<[^>]*>/g, '');
      return val;
    })
    .refine(
      (val) => {
        const cleanText = val.replace(/<[^>]*>/g, '');
        return cleanText.length <= 1200;
      },
      {
        message: "Le texte (sans HTML) doit contenir au maximum 1200 caractères"
      }
    ),
  tarif_horaire: z.number(),
  horaires: z.record(z.object({
    debut: z.string(),
    fin: z.string(),
    disponible: z.boolean(),
    creneaux: z.array(z.object({
      debut: z.string(),
      fin: z.string()
    })).optional()
  }))
});

// Schema de validation pour le changement de statut
const statusSchema = z.object({
  statut: z.enum(['actif', 'inactif'])
});

// Créer un nouveau service
router.post('/', checkProfileVisibility, validateRequest(serviceSchema), async (req, res) => {
  try {
    const userId = req.user?.userId;

    // Invalidation du cache
    const cacheKey = `user_services:${userId}`;
    await redis.del(cacheKey);

    // Vérifier le nombre de services existants
    const { data: existingServices, error: countError } = await supabase
      .from('user_services')
      .select('id')
      .eq('user_id', userId);

    if (countError) throw countError;

    // Limite de 100 service par utilisateur
    if (existingServices && existingServices.length >= 100) {
      res.status(400).json({ error: 'Nombre maximum de services atteint (100)' });
    }

    const serviceData = {
      ...req.body,
      user_id: userId,
      statut: 'actif', // Par défaut, le service est actif
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('user_services')
      .insert([serviceData])
      .select()
      .single();

    if (error) throw error;

    res.status(201).json(data);
  } catch (error) {
    logger.error('Erreur lors de la création du service:', error);
    res.status(500).json({ error: 'Erreur lors de la création du service' });
  }
});

// Récupérer les services d'un utilisateur spécifique
router.get('/user/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    // Vérifier si les services sont dans le cache
    const cacheKey = `user_services_user_specific:${slug}`;
    const cachedServices = await redis.get(cacheKey);

    if (cachedServices) {
      logger.info('🚀 Récupération des services d\'un utilisateur spécifique depuis le cache :', cachedServices);
      res.json(JSON.parse(cachedServices));
      return;
    }

    // D'abord, récupérer l'ID de l'utilisateur à partir du slug
    const { data: userData, error: userError } = await supabase
      .from('user_profil')
      .select('user_id')
      .eq('slug', slug)
      .single();

    if (userError || !userData) {
      res.status(404).json({ error: 'Utilisateur non trouvé' });
      return;
    }

    const userId = userData.user_id;

    // Récupérer les services de l'utilisateur depuis Supabase
    const { data: services, error } = await supabase
      .from('user_services')
      .select('*')
      .eq('user_id', userId)
      .eq('statut', 'actif'); // Ne récupérer que les services actifs

    if (error) throw error;

    // Mettre en cache les services
    await redis.set(cacheKey, JSON.stringify(services), 'EX', 600); // Cache pour 10 minutes

    res.json(services || []);
  } catch (error) {
    logger.error('Erreur lors de la récupération des services:', error);
    res.status(500).json({ message: 'Erreur lors de la récupération des services' });
  }
});

// Récupérer ses propres services
router.get('/user', async (req, res) => {
  try {
    const userId = req.user?.userId;

    // Vérifier si les services sont dans le cache
    const cacheKey = `user_services:${userId}`;
    const cachedServices = await redis.get(cacheKey);

    if (cachedServices) {
      logger.info('🚀 Récupération des services depuis le cache :', cachedServices);
      res.json(JSON.parse(cachedServices));
      return;
    }

    const { data, error } = await supabase
      .from('user_services')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Mettre en cache les services
    await redis.set(cacheKey, JSON.stringify(data), 'EX', 600); // Cache pour 10 minutes

    res.json(data);
  } catch (error) {
    logger.error('Erreur lors de la récupération des services:', error);
    res.status(500).json({ error: 'Erreur lors de la récupération des services' });
  }
});

// Mettre à jour un service
router.put('/:id', checkProfileVisibility, validateRequest(serviceSchema), async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    logger.info('Données reçues pour la mise à jour d\'un service :', req.body);

    // Invalidation du cache
    const cacheKey = `user_services:${userId}`;
    await redis.del(cacheKey);

    // Vérifier que le service appartient à l'utilisateur
    const { data: existingService, error: checkError } = await supabase
      .from('user_services')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (checkError || !existingService) {
      res.status(404).json({ error: 'Service non trouvé' });
      return;
    }

    const updateData = {
      ...req.body,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('user_services')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) throw error;

    res.json(data);
  } catch (error) {
    logger.error('Erreur lors de la mise à jour du service:', error);
    res.status(500).json({ error: 'Erreur lors de la mise à jour du service' });
  }
});

// Supprimer un service
router.delete('/:id', checkProfileVisibility, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    // Invalidation du cache
    const cacheKey = `user_services:${userId}`;
    await redis.del(cacheKey);

    // Vérifier que le service appartient à l'utilisateur
    const { data: existingService, error: checkError } = await supabase
      .from('user_services')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (checkError || !existingService) {
      res.status(404).json({ error: 'Service non trouvé' });
      return;
    }

    const { error } = await supabase
      .from('user_services')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (error) throw error;

    res.status(204).send();
  } catch (error) {
    logger.error('Erreur lors de la suppression du service:', error);
    res.status(500).json({ error: 'Erreur lors de la suppression du service' });
  }
});

// Changer le statut d'un service (activer/désactiver)
router.patch('/:id/status', checkProfileVisibility, validateRequest(statusSchema), (req: Request, res: Response) => {
  const handleStatusChange = async () => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId as string;
      const { statut } = req.body;

      // Invalidation du cache
      const cacheKey = `user_services:${userId}`;
      await redis.del(cacheKey);

      // Vérifier que le service appartient à l'utilisateur
      const { data: existingService, error: checkError } = await supabase
        .from('user_services')
        .select('id, statut')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (checkError || !existingService) {
        res.status(404).json({ error: 'Service non trouvé' });
        return;
      }

      // Si on essaie d'activer un service inactif, vérifier les limites d'abonnement
      if (statut === 'actif' && existingService.statut === 'inactif') {
        // Récupérer les limites d'abonnement de l'utilisateur
        const subscriptionLimits = await getUserSubscriptionLimits(userId);
        const serviceLimit = subscriptionLimits.serviceLimit || 2; // Valeur par défaut pour le plan gratuit

        // Compter le nombre de services actifs
        const { data: activeServices, error: countError } = await supabase
          .from('user_services')
          .select('id')
          .eq('user_id', userId)
          .eq('statut', 'actif');

        if (countError) throw countError;

        // Vérifier si l'utilisateur a atteint sa limite de services actifs
        if (activeServices && activeServices.length >= serviceLimit) {
          res.status(403).json({
            error: 'Limite de services atteinte',
            message: `Vous ne pouvez pas avoir plus de ${serviceLimit} service(s) actif(s) avec votre abonnement actuel.`,
            serviceLimit,
            activeCount: activeServices.length
          });
          return;
        }
      }

      const { data, error } = await supabase
        .from('user_services')
        .update({
          statut,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      res.json(data);
    } catch (error) {
      logger.error('Erreur lors du changement de statut du service:', error);
      res.status(500).json({ error: 'Erreur lors du changement de statut du service' });
    }
  };

  handleStatusChange();
});

export default router;