import React, { useEffect, useState } from 'react';
import {
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Pagination,
  Box,
  Chip,
  CircularProgress,
  Alert,
  styled,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { useAiCredits, AiCreditHistoryItem } from '../../hooks/useAiCredits';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { HistoryOutlined as HistoryIcon, SwipeOutlined as SwipeIcon } from '@mui/icons-material';

// Couleurs exactes de JobPartiel
const COLORS = {
  primary: '#FF6B2C', // Orange primaire
  secondary: '#FF7A35', // Orange secondaire
  tertiary: '#FF965E', // Orange tertiaire
  background: '#FFF8F3', // Fond clair
  accent: '#FFE4BA', // Accent doré
  success: '#4CAF50', // Vert
  error: '#F44336', // Rouge
  warning: '#FFC107', // Jaune
  info: '#2196F3', // Bleu
  borderColor: 'rgba(255, 107, 44, 0.15)'
};

// Styles personnalisés
const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  overflow: 'hidden',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  '@media (max-width: 980px)': {
    overflowX: 'auto',
    '& .MuiTable-root': {
      minWidth: '850px',
    },
  },
  '&::-webkit-scrollbar': {
    height: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: COLORS.primary,
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: COLORS.secondary,
  },
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  backgroundColor: '#F9FAFB',
  '& .MuiTableCell-root': {
    borderBottom: '1px solid #E2E8F0',
    fontWeight: 600,
    color: '#4A5568',
    fontSize: '0.9rem',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    padding: '14px 16px',
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.02)',
  },
  '&:nth-of-type(even)': {
    backgroundColor: 'rgba(249, 250, 251, 0.5)',
  },
  '& .MuiTableCell-root': {
    padding: '12px 16px',
    borderBottom: '1px solid #EDF2F7',
    fontSize: '0.95rem',
  },
  '&:last-child .MuiTableCell-root': {
    borderBottom: 'none',
  },
}));

const StyledPagination = styled(Pagination)(() => ({
  '& .MuiPaginationItem-root': {
    color: '#4A5568',
    '&:hover': {
      backgroundColor: 'rgba(255, 107, 44, 0.08)',
      color: COLORS.primary,
    },
    '&.Mui-selected': {
      backgroundColor: 'rgba(255, 107, 44, 0.12)',
      color: COLORS.primary,
      fontWeight: 600,
      '&:hover': {
        backgroundColor: 'rgba(255, 107, 44, 0.2)',
      },
    },
    '&.Mui-disabled': {
      color: '#CBD5E0',
    },
  },
  '@media (max-width: 900px)': {
    '& .MuiPaginationItem-root': {
      padding: '0 4px',
      minWidth: '32px',
      height: '32px',
      fontSize: '0.875rem',
    },
  }
}));

const CardHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: 'white',
  borderBottom: '1px solid #E2E8F0',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

// Fonction pour formater la date
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return format(date, 'dd MMMM yyyy à HH:mm', { locale: fr });
  } catch (error) {
    return dateString;
  }
};

// Fonction pour obtenir la couleur du type d'opération
const getOperationTypeColor = (type: string): string => {
  switch (type) {
    case 'achat_jobi':
      return '#4CAF5022'; // Vert très pâle
    case 'achat_stripe':
      return '#2196F322'; // Bleu très pâle
    case 'utilisation':
      return '#FF980022'; // Orange très pâle
    case 'offert_abonnement':
      return '#9C27B022'; // Violet très pâle
    case 'offert_admin':
      return '#E91E6322'; // Rose très pâle
    case 'image_confirmed':
      return COLORS.primary + '22';
    case 'inscription_utilisateur':
      return COLORS.success + '22';
    default:
      return '#75757522'; // Gris très pâle
  }
};

const getOperationTypeTextColor = (type: string): string => {
  switch (type) {
    case 'achat_jobi':
      return '#2E7D32'; // Vert foncé
    case 'achat_stripe':
      return '#1565C0'; // Bleu foncé
    case 'utilisation':
      return '#E65100'; // Orange foncé
    case 'offert_abonnement':
      return '#6A1B9A'; // Violet foncé
    case 'offert_admin':
      return '#AD1457'; // Rose foncé
    case 'image_confirmed':
      return COLORS.primary;
    case 'inscription_utilisateur':
      return COLORS.success;
    default:
      return '#424242'; // Gris foncé
  }
};

// Fonction pour obtenir le libellé du type d'opération
const getOperationTypeLabel = (type: string): string => {
  switch (type) {
    case 'achat_jobi':
      return 'Achat (Jobi)';
    case 'achat_stripe':
      return 'Achat (Stripe)';
    case 'utilisation':
      return 'Utilisation';
    case 'offert_abonnement':
      return 'Offert (Abonnement)';
    case 'offert_admin':
      return 'Offert';
    case 'autre':
      return 'Autre';
    case 'image_confirmed':
      return 'Image IA validée';
    case 'inscription_utilisateur':
      return 'Inscription utilisateur';
    default:
      return type;
  }
};

interface AiCreditsHistoryProps {
  limit?: number;
}

const AiCreditsHistory: React.FC<AiCreditsHistoryProps> = ({ limit = 10 }) => {
  const { history, fetchHistory, isRateLimited, refetch } = useAiCredits();
  const [page, setPage] = useState(1);
  const theme = useTheme();
  const isMobile = useMediaQuery('(max-width:980px)');

  useEffect(() => {
    fetchHistory(page, limit);
  }, [fetchHistory, page, limit]);

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  if (history.loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress size={40} thickness={5} sx={{ color: COLORS.primary }} />
      </Box>
    );
  }

  if (isRateLimited) {
    return (
      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 12px 0 rgba(0,0,0,0.03)',
          border: '1px solid #E2E8F0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 2
        }}
      >
        <Box sx={{ color: '#FF6B2C', mb: 1 }}>
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#2D3748', textAlign: 'center' }}>
          Trop de requêtes
        </Typography>
        <Typography variant="body2" sx={{ color: '#4A5568', textAlign: 'center', maxWidth: '400px', mb: 1 }}>
          Vous avez atteint la limite de requêtes autorisées. Veuillez patienter quelques minutes avant de réessayer.
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            backgroundColor: 'rgba(255, 107, 44, 0.08)',
            p: 1,
            borderRadius: 1,
            mb: 2
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ color: '#FF6B2C' }}>
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          <Typography variant="caption" sx={{ color: '#4A5568' }}>
            Réessayez dans 1 minute
          </Typography>
        </Box>
        <button
          onClick={refetch}
          className="flex items-center px-4 py-2 bg-white border border-[#FF6B2C] text-[#FF6B2C] rounded-md hover:bg-[#FFF8F3] transition-colors"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
            <polyline points="1 4 1 10 7 10"></polyline>
            <polyline points="23 20 23 14 17 14"></polyline>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
          </svg>
          Réessayer maintenant
        </button>
      </Paper>
    );
  }

  if (history.error) {
    return (
      <Alert severity="error" sx={{ my: 2, borderRadius: '8px' }}>
        {history.error}
      </Alert>
    );
  }

  if (!history.items || history.items.length === 0) {
    return (
      <Alert severity="info" sx={{ my: 2, borderRadius: '8px' }}>
        Aucun historique de crédits IA disponible.
      </Alert>
    );
  }

  return (
    <Paper
      elevation={0}
      sx={{
        p: 0,
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 4px 12px 0 rgba(0,0,0,0.03)',
        border: '1px solid #E2E8F0',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px 0 rgba(0,0,0,0.06)',
        },
      }}
    >
      <CardHeader>
        <HistoryIcon sx={{ color: COLORS.primary }} />
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#2D3748' }}>
          Historique des crédits IA
        </Typography>
      </CardHeader>

      {isMobile && (
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '10px 16px',
          backgroundColor: 'rgba(255, 107, 44, 0.04)',
          borderBottom: '1px solid #E2E8F0'
        }}>
          <SwipeIcon fontSize="small" sx={{ color: COLORS.primary, mr: 1 }} />
          <Typography variant="caption" sx={{ color: '#4A5568', fontWeight: 500 }}>
            Faites défiler horizontalement pour voir tout le tableau
          </Typography>
        </Box>
      )}

      <StyledTableContainer>
        <Table>
          <StyledTableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Type</TableCell>
              <TableCell align="right">Montant</TableCell>
              <TableCell align="right">Solde avant</TableCell>
              <TableCell align="right">Solde après</TableCell>
              <TableCell>Description</TableCell>
            </TableRow>
          </StyledTableHead>
          <TableBody>
            {history.items.map((item: AiCreditHistoryItem) => (
              <StyledTableRow key={item.id}>
                <TableCell>{formatDate(item.created_at)}</TableCell>
                <TableCell>
                  <Chip
                    label={getOperationTypeLabel(item.operation_type)}
                    size="small"
                    sx={{
                      backgroundColor: getOperationTypeColor(item.operation_type),
                      color: getOperationTypeTextColor(item.operation_type),
                      fontWeight: 600,
                      borderRadius: '4px',
                      height: '24px',
                      border: `1px solid ${getOperationTypeTextColor(item.operation_type)}22`,
                      '& .MuiChip-label': {
                        padding: '0 8px',
                      }
                    }}
                  />
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {item.operation_type === 'utilisation' ? (
                    <span style={{ color: COLORS.error }}>-{item.montant}</span>
                  ) : (
                    <span style={{ color: COLORS.success }}>+{item.montant}</span>
                  )}
                </TableCell>
                <TableCell align="right">{item.solde_avant}</TableCell>
                <TableCell align="right">{item.solde_apres}</TableCell>
                <TableCell>{item.description || '-'}</TableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </StyledTableContainer>

      {history.pagination && history.pagination.totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, borderTop: `1px solid ${COLORS.borderColor}` }}>
          <StyledPagination
            count={history.pagination.totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
            shape="rounded"
            size="medium"
            siblingCount={window.innerWidth < 600 ? 0 : 1}
          />
        </Box>
      )}
    </Paper>
  );
};

export default AiCreditsHistory;
