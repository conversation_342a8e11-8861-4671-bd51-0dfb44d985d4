// Cache des ressources statiques
export const CACHE_NAME = 'jobpartiel-static-v1';

// Liste des ressources à mettre en cache
const STATIC_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/images/favicon.png',
  '/robots.txt'
];

// Fonction pour mettre en cache les ressources statiques
export const cacheStaticResources = async () => {
  const cache = await caches.open(CACHE_NAME);
  await cache.addAll(STATIC_RESOURCES);
};

// Fonction pour récupérer une ressource du cache
export const getCachedResource = async (request: Request) => {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  cache.put(request, networkResponse.clone());
  return networkResponse;
};

// Fonction pour nettoyer les anciens caches
export const cleanOldCaches = async () => {
  const cacheKeys = await caches.keys();
  const oldCaches = cacheKeys.filter(key => key !== CACHE_NAME);
  await Promise.all(oldCaches.map(key => caches.delete(key)));
}; 