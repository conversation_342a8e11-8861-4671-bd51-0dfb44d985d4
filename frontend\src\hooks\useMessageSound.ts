/* Permet de jouer un son lorsqu'un message privé est reçu */

import { useState, useEffect, useRef, useCallback } from 'react';
import { getCookie, setCookie } from '../utils/cookieUtils';
import logger from '@/utils/logger';

// Créer un store simple pour partager l'état entre les instances du hook
const store = {
  isSoundEnabled: true,
  listeners: new Set<(value: boolean) => void>()
};

export const useMessageSound = () => {
  // Utiliser l'état local pour déclencher les re-renders
  const [isSoundEnabled, setIsSoundEnabled] = useState(() => {
    // Initialiser avec la valeur du cookie ou la valeur par défaut du store
    const cookieValue = getCookie('cookie_son_notification_message_prive');
    return cookieValue !== null ? cookieValue !== 'false' : store.isSoundEnabled;
  });
  
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioBuffer | null>(null);

  // S'abonner aux changements d'état
  useEffect(() => {
    const handleChange = (value: boolean) => {
      setIsSoundEnabled(value);
    };

    // Initialiser l'état du store avec la valeur du cookie au chargement
    if (store.listeners.size === 0) {
      const cookieValue = getCookie('cookie_son_notification_message_prive');
      if (cookieValue !== null) {
        store.isSoundEnabled = cookieValue !== 'false';
      }
    }

    // S'abonner aux changements
    store.listeners.add(handleChange);
    
    // Se désabonner lors du nettoyage
    return () => {
      store.listeners.delete(handleChange);
    };
  }, []);

  useEffect(() => {
    // Créer le contexte audio
    audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Charger et décoder le fichier audio
    fetch('/sounds/nouveau_message_un.aac')
      .then(response => response.arrayBuffer())
      .then(arrayBuffer => {
        if (audioContextRef.current) {
          return audioContextRef.current.decodeAudioData(arrayBuffer);
        }
        return null;
      })
      .then(audioBuffer => {
        if (audioBuffer) {
          audioBufferRef.current = audioBuffer;
        }
      })
      .catch(error => {
        logger.info('Erreur lors du chargement du son:', error); 
      });

    return () => {
      // Nettoyer le contexte audio
      if (audioContextRef.current?.state !== 'closed') {
        audioContextRef.current?.close();
      }
    };
  }, []);

  const playSound = useCallback(async () => {
    try {
      if (audioContextRef.current && audioBufferRef.current) {
        // Créer une nouvelle source à chaque lecture
        const source = audioContextRef.current.createBufferSource();
        source.buffer = audioBufferRef.current;
        source.connect(audioContextRef.current.destination);
        source.start(0);
      }
    } catch (error) {
      logger.info('Erreur lors de la lecture du son:', error);
    }
  }, []);

  const toggleSound = useCallback(() => {
    const newState = !store.isSoundEnabled;
    
    // Mettre à jour le store
    store.isSoundEnabled = newState;
    
    // Notifier tous les abonnés
    store.listeners.forEach(listener => listener(newState));
    
    // Sauvegarder dans le cookie
    setCookie('cookie_son_notification_message_prive', newState.toString(), 365 * 24 * 60 * 60);
    
    // Jouer un son de test lors de l'activation
    if (newState) {
      playSound();
    }
  }, [playSound]);

  const playMessageSound = useCallback((type: 'sent' | 'received') => {
    if (!store.isSoundEnabled) return;

    if (type === 'received') {
      playSound();
    }
  }, [playSound]);

  return {
    isSoundEnabled,
    toggleSound,
    playMessageSound
  };
}; 