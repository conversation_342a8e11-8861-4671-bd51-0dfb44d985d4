import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, IconButton, Box, Tooltip, Fade, Alert, CircularProgress, ToggleButton, ToggleButtonGroup, TextField, InputAdornment } from '@mui/material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { Refresh, InfoOutlined, Search, EmojiEvents, Payments, TrendingUp } from '@mui/icons-material';
import { api } from '../../services/api';
import { getCommonHeaders } from '../../utils/headers';
import { notify } from '../../components/Notification';
import { badges } from './profil/badges';
import { useAuth } from '../../contexts/AuthContext';

// Styled components
const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748', 
  marginBottom: '16px',
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    bottom: '-8px',
    left: 0,
    width: '60px',
    height: '3px',
    background: '#FF6B2C',
    borderRadius: '2px',
  },
  [theme.breakpoints.down('sm')]: {
    fontSize: '1.25rem',
  },
}));

const RefreshButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: 'rgba(255, 107, 44, 0.08)',
  color: '#FF6B2C',
  borderRadius: '8px',
  padding: '8px',
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '6px',
  },
}));

const InfoBox = styled(Box)(({ theme }) => ({
  margin: theme.spacing(2, 0),
  padding: theme.spacing(1.5, 2),
  borderRadius: '8px',
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  display: 'flex',
  alignItems: 'center',
  '& .MuiSvgIcon-root': {
    color: '#FF6B2C',
    marginRight: theme.spacing(1),
  },
  '& .MuiTypography-root': {
    fontSize: '0.95rem',
    '@media (max-width: 768px)': {
      fontSize: '0.9rem',
    },
  }
}));

const BadgeCard = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFFFFF',
  borderRadius: '16px',
  padding: theme.spacing(3),
  border: '1px solid #E2E8F0',
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.06)',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #FF6B2C 0%, #FF9F6B 100%)',
    opacity: 0.5,
  },
  '&.selected': {
    backgroundColor: '#FFFFFF',
    borderColor: '#FF6B2C',
    boxShadow: '0 8px 24px rgba(255, 107, 44, 0.1)',
    '&::before': {
      opacity: 1,
      height: '4px'
    }
  }
}));

const BadgeIcon = styled(Box)(({ theme }) => ({
  width: '48px',
  height: '48px',
  borderRadius: '12px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: theme.spacing(2),
  '& svg': {
    width: '24px',
    height: '24px',
  }
}));

const BadgeTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.1rem',
  fontWeight: 600,
  color: '#2D3748',
  marginBottom: '4px',
}));

const BadgeDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.9rem',
  color: '#718096',
  marginBottom: theme.spacing(2),
}));

const BadgeDetailedDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: '#4A5568',
  lineHeight: 1.6,
  marginBottom: theme.spacing(2),
}));

const StatusBadge = styled(Box)<{ status: 'obtained' | 'pending' }>(({ theme, status }) => ({
  position: 'absolute',
  top: '1rem',
  right: '1rem',
  padding: '0.4rem 0.8rem',
  borderRadius: '9999px',
  fontSize: '0.75rem',
  fontWeight: 600,
  ...(status === 'obtained' ? {
    backgroundColor: '#E6F6EE',
    color: '#059669',
    border: '1px solid #A7F3D0',
  } : {
    backgroundColor: '#FFF8F3',
    color: '#FF6B2C',
    border: '1px solid #FFE4BA',
  }),
}));

const RewardBadge = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  padding: '0.5rem 0.75rem',
  borderRadius: '8px',
  backgroundColor: '#FFF8F3',
  border: '1px solid #FFE4BA',
  '& .amount': {
    color: '#FF6B2C',
    fontWeight: 600,
    marginLeft: '4px',
  }
}));

const LifetimeBadge = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  padding: '0.4rem 0.8rem',
  borderRadius: '8px',
  backgroundColor: '#EBF8FF',
  border: '1px solid #BEE3F8',
  color: '#3182CE',
  fontSize: '0.75rem',
  fontWeight: 600,
}));

const FilterBar = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  marginBottom: theme.spacing(3),
  padding: theme.spacing(1),
  backgroundColor: '#F8FAFC',
  borderRadius: '12px',
  [theme.breakpoints.down('sm')]: {
    overflowX: 'auto',
    whiteSpace: 'nowrap',
    '&::-webkit-scrollbar': {
      display: 'none'
    }
  }
}));

const FilterButton = styled(ToggleButton)(({ theme }) => ({
  border: 'none',
  borderRadius: '8px !important',
  padding: '8px 16px',
  marginRight: '8px',
  color: '#64748B',
  backgroundColor: 'transparent',
  '&.Mui-selected': {
    backgroundColor: '#FFFFFF',
    color: '#FF6B2C',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
    '&:hover': {
      backgroundColor: '#FFFFFF',
    }
  },
  '&:hover': {
    backgroundColor: 'rgba(255, 107, 44, 0.04)',
  }
}));

const SearchBar = styled(TextField)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#FFFFFF',
    borderRadius: '12px',
    '& fieldset': {
      borderColor: '#E2E8F0',
    },
    '&:hover fieldset': {
      borderColor: '#CBD5E0',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#FF6B2C',
    },
  },
  '& .MuiInputAdornment-root .MuiSvgIcon-root': {
    color: '#94A3B8',
  },
}));

const StatsContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
  gap: theme.spacing(3),
  marginBottom: theme.spacing(4),
  [theme.breakpoints.down('sm')]: {
    gap: theme.spacing(2),
  }
}));

const StatCard = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFFFFF',
  borderRadius: '12px',
  padding: theme.spacing(2.5),
  border: '1px solid #E2E8F0',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
  }
}));

const StatIcon = styled(Box)(({ theme }) => ({
  width: 48,
  height: 48,
  borderRadius: '10px',
  backgroundColor: '#FFF8F3',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  '& .MuiSvgIcon-root': {
    color: '#FF6B2C',
    fontSize: '24px'
  }
}));

const StatContent = styled(Box)({
  flex: 1,
});

const StatValue = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: '#2D3748',
  marginBottom: '4px',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
}));

const StatLabel = styled(Typography)({
  fontSize: '0.875rem',
  color: '#64748B',
  fontWeight: 500,
});

const BadgesPage: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userBadges, setUserBadges] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [filterValue, setFilterValue] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBadgeId, setSelectedBadgeId] = useState<string | null>(null);

  const fetchBadges = async () => {
    if (!user?.id) {
      setError('Utilisateur non authentifié');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const headers = await getCommonHeaders();

      // Récupérer les badges de l'utilisateur
      const badgesResponse = await api.get(`/api/user-badges/by-profile/${user.id}`, {
        headers,
        withCredentials: true
      });

      if (badgesResponse.data.success) {
        setUserBadges(badgesResponse.data.data);
      }

      // Récupérer les statistiques pour les badges
      const statsResponse = await api.get(`/api/user-badges/profile-stats/${user.id}`, {
        headers,
        withCredentials: true
      });

      if (statsResponse.data.success) {
        setStats(statsResponse.data.data);
      }

    } catch (error) {
      console.error('Erreur lors de la récupération des badges:', error);
      setError('Une erreur est survenue lors de la récupération des badges.');
      notify('Une erreur est survenue lors de la récupération des badges.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchBadges();
    }
  }, [user]);

  const handleRefresh = () => {
    fetchBadges();
  };

  const handleFilterChange = (event: React.MouseEvent<HTMLElement>, newFilter: string) => {
    if (newFilter !== null) {
      setFilterValue(newFilter);
    }
  };

  const getFilteredBadges = (badges: any[]) => {
    const mappedBadges = badges.map((badge) => ({
      ...badge,
      isObtained: userBadges.some(ub => ub.badge_id === badge.id),
      meetsCondition: stats ? badge.condition(stats) : false
    }));

    let filteredBadges = [...mappedBadges];
    
    // Filtre par recherche
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filteredBadges = filteredBadges.filter(badge => 
        badge.title.toLowerCase().includes(query) ||
        badge.description.toLowerCase().includes(query) ||
        badge.detailedDescription.toLowerCase().includes(query)
      );
    }
    
    // Filtre par état
    switch (filterValue) {
      case 'obtained':
        filteredBadges = filteredBadges.filter(badge => badge.isObtained);
        break;
      case 'pending':
        filteredBadges = filteredBadges.filter(badge => !badge.isObtained);
        break;
      default:
        break;
    }

    return filteredBadges.sort((a, b) => {
      if (a.isObtained && !b.isObtained) return -1;
      if (!a.isObtained && b.isObtained) return 1;
      return 0;
    });
  };

  const handleBadgeClick = (badgeId: string) => {
    setSelectedBadgeId(selectedBadgeId === badgeId ? null : badgeId);
  };

  const calculateStats = () => {
    const totalBadges = badges.length;
    const obtainedBadges = userBadges.length;
    const totalJobis = userBadges.reduce((total, userBadge) => {
      const badge = badges.find(b => b.id === userBadge.badge_id);
      return total + (badge?.recompense_jobi || 0);
    }, 0);
    const progressPercentage = totalBadges > 0 ? Math.round((obtainedBadges / totalBadges) * 100) : 0;

    return {
      totalBadges,
      obtainedBadges,
      totalJobis,
      progressPercentage
    };
  };

  return (
    <div className="space-y-6 px-4 sm:px-6 md:px-0 pb-6 sm:pb-8">
      <div className="flex items-center justify-between mb-4">
        <PageTitle variant="h1">
          Badges et Récompenses
        </PageTitle>
        
        <Tooltip title="Rafraîchir les badges" arrow>
          <RefreshButton 
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <Refresh fontSize="small" />
          </RefreshButton>
        </Tooltip>
      </div>

      {!isLoading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <StatsContainer>
            <StatCard>
              <StatIcon>
                <EmojiEvents />
              </StatIcon>
              <StatContent>
                <StatValue>
                  {calculateStats().obtainedBadges}/{calculateStats().totalBadges}
                </StatValue>
                <StatLabel>Badges obtenus</StatLabel>
              </StatContent>
            </StatCard>

            <StatCard>
              <StatIcon>
                <Payments />
              </StatIcon>
              <StatContent>
                <StatValue>
                  {calculateStats().totalJobis}
                  <Typography component="span" fontSize="1rem" color="#64748B">
                    Jobis
                  </Typography>
                </StatValue>
                <StatLabel>Récompenses gagnées</StatLabel>
              </StatContent>
            </StatCard>

            <StatCard>
              <StatIcon>
                <TrendingUp />
              </StatIcon>
              <StatContent>
                <StatValue>
                  {calculateStats().progressPercentage}%
                </StatValue>
                <StatLabel>Progression globale</StatLabel>
              </StatContent>
            </StatCard>
          </StatsContainer>
        </motion.div>
      )}

      {error && (
        <Fade in={!!error}>
          <Alert 
            severity="error" 
            sx={{ mb: 3 }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        </Fade>
      )}
      
      <InfoBox>
        <InfoOutlined fontSize="small" />
        <Typography variant="body2" className="text-gray-700 ml-1">
          Cette page affiche tous vos badges obtenus sur JobPartiel. Les badges sont attribués automatiquement en fonction de vos réalisations et de votre activité sur la plateforme.
        </Typography>
      </InfoBox>

      <SearchBar
        fullWidth
        placeholder="Rechercher un badge..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
        }}
      />

      <FilterBar>
        <ToggleButtonGroup
          value={filterValue}
          exclusive
          onChange={handleFilterChange}
          aria-label="filtres des badges"
        >
          <FilterButton value="all">
            Tous les badges
          </FilterButton>
          <FilterButton value="obtained">
            Badges obtenus
          </FilterButton>
          <FilterButton value="pending">
            Non obtenus
          </FilterButton>
        </ToggleButtonGroup>
      </FilterBar>
      
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <CircularProgress sx={{ color: '#FF7A35' }} />
          <p className="mt-4 text-gray-600">Chargement des badges...</p>
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {getFilteredBadges(badges).length === 0 ? (
            <motion.div 
              className="col-span-full flex flex-col items-center justify-center py-12 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Typography variant="h6" color="textSecondary" gutterBottom>
                Aucun badge trouvé
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {searchQuery 
                  ? "Aucun badge ne correspond à votre recherche." 
                  : "Aucun badge ne correspond aux filtres sélectionnés."}
              </Typography>
            </motion.div>
          ) : (
            getFilteredBadges(badges).map(({ isObtained, meetsCondition, ...badge }) => (
              <BadgeCard 
                key={badge.id}
                onClick={() => handleBadgeClick(badge.id)}
                className={selectedBadgeId === badge.id ? 'selected' : ''}
                sx={{
                  backgroundColor: '#FFFFFF',
                  border: selectedBadgeId === badge.id
                    ? '1px solid #FF6B2C'
                    : isObtained 
                    ? '1px solid #FFE4BA'
                    : '1px solid #E2E8F0',
                  '&::before': {
                    opacity: selectedBadgeId === badge.id 
                      ? 1 
                      : isObtained 
                      ? 1 
                      : 0.15,
                    height: '4px',
                    background: 'linear-gradient(90deg, #FF6B2C 0%, #FF9F6B 100%)',
                  }
                }}
              >
                {(isObtained || meetsCondition) && (
                  <StatusBadge status={isObtained ? 'obtained' : 'pending'}>
                    {isObtained ? 'Obtenu' : 'Prêt à obtenir !'}
                  </StatusBadge>
                )}

                <div className="flex items-start mb-4">
                  <BadgeIcon
                    sx={{
                      backgroundColor: isObtained 
                        ? badge.backgroundColor 
                        : meetsCondition 
                        ? '#FFF8F3' 
                        : '#F8FAFC',
                      color: isObtained 
                        ? badge.iconColor 
                        : meetsCondition 
                        ? '#FF6B2C' 
                        : '#94A3B8',
                    }}
                  >
                    {badge.icon}
                  </BadgeIcon>
                  <div className="flex-1">
                    <BadgeTitle sx={{
                      color: isObtained 
                        ? '#2D3748' 
                        : meetsCondition 
                        ? '#2D3748' 
                        : '#64748B'
                    }}>
                      {badge.title}
                    </BadgeTitle>
                    <BadgeDescription>
                      {badge.description}
                    </BadgeDescription>
                  </div>
                </div>

                <BadgeDetailedDescription sx={{
                  color: isObtained 
                    ? '#4A5568' 
                    : meetsCondition 
                    ? '#4A5568' 
                    : '#64748B'
                }}>
                  {badge.detailedDescription}
                </BadgeDetailedDescription>

                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                  <RewardBadge sx={{
                    opacity: isObtained ? 1 : meetsCondition ? 0.9 : 0.7
                  }}>
                    <span className="text-gray-600 text-sm">Récompense:</span>
                    <span className="amount">{badge.recompense_jobi} Jobi</span>
                  </RewardBadge>
                  
                  {badge.is_lifetime && (
                    <LifetimeBadge sx={{
                      opacity: isObtained ? 1 : meetsCondition ? 0.9 : 0.7
                    }}>
                      Badge à vie
                    </LifetimeBadge>
                  )}
                </div>
              </BadgeCard>
            ))
          )}
        </motion.div>
      )}
    </div>
  );
};

export default BadgesPage;