import { Router, Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import logger from '../utils/logger';
import { asyncHandler } from '../utils/inputValidation';
import config from '../config';
import { decryptProfilDataAsync } from '../utils/encryption';

const router = Router();

// Dates de dernière modification des pages (à mettre à jour manuellement)
const PAGE_LAST_MODIFIED = {
  // Pages principales
  homepage: '2025-05-26T10:00:00.000Z', // Dernière mise à jour du contenu accueil
  auth: '2025-05-24T11:10:00.000Z', // Dernière modification des pages auth

  // Pages légales (IMPORTANT: Mettre à jour quand le contenu change!)
  legal: '2025-05-23T10:00:00.000Z' // Dernière mise à jour des mentions légales, CGU, etc.
};

// Mapping des catégories de services (autonome pour Docker)
const SERVICE_CATEGORIES: { [key: string]: string } = {
  '1': 'Jardinage',
  '2': 'Bricolage',
  '3': 'Garde d\'animaux',
  '4': 'Services à la personne',
  '5': 'Événementiel & Restauration',
  '6': 'Services administratifs',
  '7': 'Transport & Logistique',
  '8': 'Communication & Marketing',
  '9': 'Éducation & Formation',
  '10': 'Informatique',
  '11': 'Arts & Divertissement',
  '12': 'Bien-être & Santé',
  '13': 'Services aux entreprises',
  '14': 'Artisanat & Création',
  '15': 'Sport & Loisirs',
  '16': 'Immobilier & Habitat',
  '17': 'Automobile & Transport',
  '18': 'Décoration & Design',
  '19': 'Services financiers',
  '20': 'Tourisme & Voyages',
  '21': 'Rénovation & Travaux',
  '22': 'Piscine & Spa',
  '23': 'Mode & Beauté',
  '24': 'Sécurité & Protection',
  '25': 'Environnement & Écologie'
};

// Route pour le sitemap principal = INDEX (comme WordPress)
router.get('/sitemap.xml', asyncHandler(async (_req: Request, res: Response): Promise<void> => {
  try {
    // Vérifier le cache Redis
    const cacheKey = 'sitemap_index_xml';
    const cachedIndex = await redis.get(cacheKey);

    if (cachedIndex) {
      logger.info('Sitemap index récupéré depuis le cache Redis');
      res.set('Content-Type', 'application/xml');
      res.send(cachedIndex);
      return;
    }

    // Compter le nombre total de profils indexables
    const { count, error } = await supabase
      .from('user_profil')
      .select('*', { count: 'exact', head: true })
      .eq('profil_visible', true)
      .eq('seo_indexable', true)
      .not('slug', 'is', null);

    if (error) {
      logger.error('Erreur lors du comptage des profils:', error);
    }

    const totalProfiles = count || 0;
    const profilesPerSitemap = 1000; // Réduire pour une meilleure organisation
    const numberOfProfileSitemaps = Math.ceil(totalProfiles / profilesPerSitemap);

    // Construire le sitemap index organisé comme WordPress
    let sitemapIndexXml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Pages principales -->
  <sitemap>
    <loc>https://jobpartiel.fr/sitemap-pages.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`;

    // Ajouter les sitemaps de profils organisés
    if (numberOfProfileSitemaps > 0) {
      sitemapIndexXml += `
  <!-- Profils utilisateurs -->`;
      for (let i = 0; i < numberOfProfileSitemaps; i++) {
        sitemapIndexXml += `
  <sitemap>
    <loc>https://jobpartiel.fr/sitemap-profils-${i + 1}.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`;
      }
    }

    // Ajouter le sitemap des services SEO dynamiques
    sitemapIndexXml += `
  <!-- Services SEO dynamiques -->
  <sitemap>
    <loc>https://jobpartiel.fr/sitemap-services.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`;

    // Préparer pour le futur blog
    sitemapIndexXml += `
  <!-- Blog (à venir) -->
  <!-- <sitemap>
    <loc>https://jobpartiel.fr/sitemap-blog.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap> -->`;

    sitemapIndexXml += `
</sitemapindex>`;

    // Mettre en cache pour 2 heures
    await redis.set(cacheKey, sitemapIndexXml, 'EX', 7200);

    logger.info(`Sitemap index généré avec ${numberOfProfileSitemaps} sitemaps de profils`);

    res.set('Content-Type', 'application/xml');
    res.send(sitemapIndexXml);

  } catch (error) {
    logger.error('Erreur lors de la génération du sitemap:', error);
    res.status(500).send('Erreur lors de la génération du sitemap');
  }
}));

// Route pour invalider le cache du sitemap (admin uniquement)
router.post('/sitemap/refresh', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    // SÉCURITÉ: Cette route doit être protégée par authentification admin
    // Vérifier si l'utilisateur est authentifié et a le rôle admin
    const authHeader = req.headers.authorization;
    const token = req.cookies['access_token'] || (authHeader && authHeader.split(' ')[1]);

    if (!token) {
      res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
      return;
    }

    // SÉCURITÉ: En production, cette route doit être complètement désactivée ou très restreinte
    if (process.env.NODE_ENV === 'production') {
      res.status(403).json({
        success: false,
        message: 'Cette opération est désactivée en production'
      });
      return;
    }

    // Invalider tous les caches du sitemap
    const keys = await redis.keys('sitemap*');
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    logger.info('Cache du sitemap invalidé (tous les sitemaps)');

    res.json({
      success: true,
      message: 'Cache du sitemap invalidé avec succès',
      clearedKeys: keys.length
    });

  } catch (error) {
    logger.error('Erreur lors de l\'invalidation du cache sitemap:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'invalidation du cache'
    });
  }
}));

// Route pour sitemap des pages principales (comme WordPress)
router.get('/sitemap-pages.xml', asyncHandler(async (_req: Request, res: Response): Promise<void> => {
  try {
    // Vérifier le cache Redis
    const cacheKey = 'sitemap_pages_xml';
    const cachedSitemap = await redis.get(cacheKey);

    if (cachedSitemap) {
      logger.info('Sitemap pages récupéré depuis le cache Redis');
      res.set('Content-Type', 'application/xml');
      res.send(cachedSitemap);
      return;
    }

    // Pages publiques avec dates fixes (Google friendly)
    const publicPages = [
      // Pages principales (changent plus souvent)
      {
        url: '/',
        changefreq: 'daily',
        priority: '1.0',
        lastmod: PAGE_LAST_MODIFIED.homepage
      },
      {
        url: '/auth/login',
        changefreq: 'monthly',
        priority: '0.8',
        lastmod: PAGE_LAST_MODIFIED.auth
      },
      {
        url: '/auth/inscription',
        changefreq: 'monthly',
        priority: '0.8',
        lastmod: PAGE_LAST_MODIFIED.auth
      },

      // Pages légales (dates fixes importantes pour Google)
      {
        url: '/mentions-legales',
        changefreq: 'yearly',
        priority: '0.3',
        lastmod: PAGE_LAST_MODIFIED.legal
      },
      {
        url: '/politique-confidentialite',
        changefreq: 'yearly',
        priority: '0.3',
        lastmod: PAGE_LAST_MODIFIED.legal
      },
      {
        url: '/conditions-generales',
        changefreq: 'yearly',
        priority: '0.3',
        lastmod: PAGE_LAST_MODIFIED.legal
      },
      {
        url: '/cookies',
        changefreq: 'yearly',
        priority: '0.3',
        lastmod: PAGE_LAST_MODIFIED.legal
      }
    ];

    // Construire le XML du sitemap des pages
    let sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Ajouter les pages publiques avec dates fixes
    publicPages.forEach(page => {
      sitemapXml += `
  <url>
    <loc>https://jobpartiel.fr${page.url}</loc>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    <lastmod>${page.lastmod}</lastmod>
  </url>`;
    });

    sitemapXml += `
</urlset>`;

    // Mettre en cache pour 7 jours (pages statiques avec dates fixes)
    await redis.set(cacheKey, sitemapXml, 'EX', 604800);

    logger.info(`Sitemap pages généré avec ${publicPages.length} pages`);

    res.set('Content-Type', 'application/xml');
    res.send(sitemapXml);

  } catch (error) {
    logger.error('Erreur lors de la génération du sitemap pages:', error);
    res.status(500).send('Erreur lors de la génération du sitemap pages');
  }
}));

// Route API pour récupérer les profils (pour le frontend)
router.get('/api/users/sitemap/profiles', asyncHandler(async (_req: Request, res: Response): Promise<void> => {
  try {
    // Vérifier le cache Redis
    const cacheKey = 'sitemap_profiles_api';
    const cachedProfiles = await redis.get(cacheKey);

    if (cachedProfiles) {
      logger.info('Profils API pour sitemap récupérés depuis le cache Redis');
      res.json(JSON.parse(cachedProfiles));
      return;
    }

    // Récupérer tous les profils publics et indexables
    const { data: profiles, error } = await supabase
      .from('user_profil')
      .select(`
        slug,
        updated_at,
        prenom,
        nom,
        ville,
        code_postal,
        user_id
      `)
      .eq('profil_visible', true)
      .eq('seo_indexable', true)
      .not('slug', 'is', null)
      .order('updated_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération des profils pour API sitemap:', error);
      res.status(500).json({ message: 'Erreur serveur' });
      return;
    }

    // Fonction pour formater le nom (Prénom + première lettre du nom)
    const formatName = (prenom: string | null, nom: string | null): string => {
      if (!prenom && !nom) return '';
      if (!prenom) return nom ? `${nom.charAt(0).toUpperCase()}.` : '';
      if (!nom) return prenom;
      return `${prenom} ${nom.charAt(0).toUpperCase()}.`;
    };

    const formattedProfiles = await Promise.all(profiles?.map(async (profile) => {
      // Déchiffrer les données de profil
      const decryptedProfile = await decryptProfilDataAsync(profile);
      
      return {
        slug: profile.slug,
        nom: decryptedProfile.nom,
        prenom: decryptedProfile.prenom,
        ville: decryptedProfile.ville,
        updated_at: profile.updated_at
      };
    }) || []);

    // Mettre en cache pour 1 heure
    await redis.set(cacheKey, JSON.stringify(formattedProfiles), 'EX', 3600);

    logger.info(`API sitemap: ${formattedProfiles.length} profils retournés`);

    res.json(formattedProfiles);
  } catch (error) {
    logger.error('Erreur lors de la récupération des profils pour API sitemap:', error);
    res.status(500).json({ message: 'Erreur serveur' });
  }
}));

// Route pour sitemaps de profils paginés (organisé comme WordPress)
router.get('/sitemap-profils-:page.xml', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.params.page) || 1;
    const profilesPerPage = 10000;
    const offset = (page - 1) * profilesPerPage;

    // Vérifier le cache Redis
    const cacheKey = `sitemap_profils_${page}_xml`;
    const cachedSitemap = await redis.get(cacheKey);

    if (cachedSitemap) {
      logger.info(`Sitemap profils page ${page} récupéré depuis le cache Redis`);
      res.set('Content-Type', 'application/xml');
      res.send(cachedSitemap);
      return;
    }

    // Récupérer les profils pour cette page avec informations enrichies
    const { data: profiles, error } = await supabase
      .from('user_profil')
      .select(`
        slug,
        updated_at,
        prenom,
        nom,
        ville,
        code_postal,
        user_id
      `)
      .eq('profil_visible', true)
      .eq('seo_indexable', true)
      .not('slug', 'is', null)
      .order('updated_at', { ascending: false })
      .range(offset, offset + profilesPerPage - 1);

    if (error) {
      logger.error(`Erreur lors de la récupération des profils page ${page}:`, error);
      res.status(500).send('Erreur lors de la génération du sitemap');
      return;
    }

    // Fonction pour formater le nom (Prénom + première lettre du nom)
    const formatName = (prenom: string | null, nom: string | null): string => {
      if (!prenom && !nom) return '';
      if (!prenom) return nom ? `${nom.charAt(0).toUpperCase()}.` : '';
      if (!nom) return prenom;
      return `${prenom} ${nom.charAt(0).toUpperCase()}.`;
    };

    // Fonction pour récupérer les services d'un utilisateur
    const getUserServices = async (userId: string): Promise<string[]> => {
      try {
        const { data: services } = await supabase
          .from('user_services')
          .select('category_id, subcategory_id')
          .eq('user_id', userId)
          .eq('statut', 'actif');

        if (!services || services.length === 0) return [];

        const serviceNames: string[] = [];

        services.forEach(service => {
          const categoryName = SERVICE_CATEGORIES[service.category_id];

          if (categoryName) {
            serviceNames.push(`${categoryName}`);
          } else if (categoryName) {
            serviceNames.push(categoryName);
          }
        });

        return serviceNames;
      } catch (error) {
        logger.warn(`Erreur lors de la récupération des services pour l'utilisateur ${userId}:`, error);
        return [];
      }
    };

    // Construire le XML du sitemap
    let sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Ajouter les profils utilisateurs avec informations enrichies
    if (profiles && profiles.length > 0) {
      for (const profile of profiles) {
        const lastmod = profile.updated_at ? new Date(profile.updated_at).toISOString() : new Date().toISOString();

        // Déchiffrer les données de profil
        const decryptedProfile = await decryptProfilDataAsync(profile);
        
        // Formater le nom (respectueux du RGPD)
        const formattedName = formatName(decryptedProfile.prenom, decryptedProfile.nom);

        // Récupérer les services de l'utilisateur
        const userServices = await getUserServices(profile.user_id);

        // Construire la localisation (ville + code postal si disponibles)
        const location = [decryptedProfile.ville, decryptedProfile.code_postal].filter(Boolean).join(' ');

        // Construire le titre enrichi pour le SEO
        let title = formattedName;
        if (userServices.length > 0) {
          title += ` - ${userServices.slice(0, 2).join(', ')}`;
        }
        if (location) {
          title += ` - ${location}`;
        }

        sitemapXml += `
  <url>
    <loc>https://jobpartiel.fr/profil/${profile.slug}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
    <lastmod>${lastmod}</lastmod>
  </url>`;
      }
    }

    sitemapXml += `
</urlset>`;

    // Mettre en cache pour 1 heure
    await redis.set(cacheKey, sitemapXml, 'EX', 3600);

    logger.info(`Sitemap profils page ${page} généré avec ${profiles?.length || 0} profils`);

    res.set('Content-Type', 'application/xml');
    res.send(sitemapXml);

  } catch (error) {
    logger.error('Erreur lors de la génération du sitemap profils:', error);
    res.status(500).send('Erreur lors de la génération du sitemap');
  }
}));

// Route pour générer le sitemap des pages SEO dynamiques
router.get('/sitemap-services.xml', asyncHandler(async (_req: Request, res: Response): Promise<void> => {
  try {
    // Vérifier le cache Redis
    const cacheKey = 'sitemap_seo_services_xml';
    const cachedSitemap = await redis.get(cacheKey);

    if (cachedSitemap) {
      logger.info('Sitemap SEO services récupéré depuis le cache Redis');
      res.set('Content-Type', 'application/xml');
      res.send(cachedSitemap);
      return;
    }

    // Récupérer les profils avec villes et date de mise à jour
    const { data: profils, error: profilError } = await supabase
      .from('user_profil')
      .select(`
        user_id,
        ville,
        updated_at,
        users!inner(
          user_type,
          profil_actif
        )
      `)
      .eq('profil_visible', true)
      .eq('users.profil_actif', true)
      .eq('users.user_type', 'jobbeur')
      .not('ville', 'is', null);

    if (profilError) {
      logger.error('Erreur lors de la récupération des profils:', profilError);
      res.status(500).send('Erreur serveur');
      return;
    }

    if (!profils || profils.length === 0) {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;
      res.set('Content-Type', 'application/xml');
      res.send(xml);
      return;
    }

    // Récupérer les services pour ces utilisateurs
    const userIds = profils.map(p => p.user_id);
    const { data: services, error: serviceError } = await supabase
      .from('user_services')
      .select('user_id, titre, statut')
      .in('user_id', userIds)
      .eq('statut', 'actif');

    if (serviceError) {
      logger.error('Erreur lors de la récupération des services:', serviceError);
      res.status(500).send('Erreur serveur');
      return;
    }

    // Utiliser une Map pour stocker la combinaison et la date de lastmod la plus récente
    const combinationsWithLastmod = new Map<string, string>();

    // Mapper les services par user_id pour un accès rapide
    const servicesByUserId = new Map<string, Array<{ titre: string | null; statut: string }>>();
    services?.forEach(service => {
      if (!servicesByUserId.has(service.user_id)) {
        servicesByUserId.set(service.user_id, []);
      }
      servicesByUserId.get(service.user_id)?.push(service);
    });

    // Combiner les profils et services pour créer les combinaisons avec la date de lastmod
    for (const profil of profils) {
      if (profil.ville && profil.updated_at) {
        // Déchiffrer les données de profil
        const decryptedProfil = await decryptProfilDataAsync(profil);
        
        const userServices = servicesByUserId.get(profil.user_id) || [];
        const profileLastmod = new Date(profil.updated_at).toISOString().split('T')[0];

        userServices.forEach(service => {
          if (service.titre && decryptedProfil.ville) {
            // Normaliser le service et la ville pour l'URL
            const serviceSlug = service.titre.toLowerCase()
              .replace(/\s+/g, '-')
              .replace(/[àáâãäå]/g, 'a')
              .replace(/[èéêë]/g, 'e')
              .replace(/[ìíîï]/g, 'i')
              .replace(/[òóôõö]/g, 'o')
              .replace(/[ùúûü]/g, 'u')
              .replace(/[ç]/g, 'c')
              .replace(/[^a-z0-9-]/g, '');

            const citySlug = decryptedProfil.ville.toLowerCase()
              .replace(/\s+/g, '-')
              .replace(/[àáâãäå]/g, 'a')
              .replace(/[èéêë]/g, 'e')
              .replace(/[ìíîï]/g, 'i')
              .replace(/[òóôõö]/g, 'o')
              .replace(/[ùúûü]/g, 'u')
              .replace(/[ç]/g, 'c')
              .replace(/[^a-z0-9-]/g, '');

            if (serviceSlug && citySlug) {
              const combination = `${serviceSlug}/${citySlug}`;

              // Mettre à jour la date de lastmod si la date actuelle du profil est plus récente
              if (!combinationsWithLastmod.has(combination) || new Date(profileLastmod) > new Date(combinationsWithLastmod.get(combination) as string)) {
                combinationsWithLastmod.set(combination, profileLastmod);
              }
            }
          }
        });
      }
    }

    // Générer le XML du sitemap
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    // Ajouter les pages de services dynamiques en utilisant les dates de lastmod calculées
    combinationsWithLastmod.forEach((lastmod, combination) => {
      xml += `
  <url>
    <loc>${config.frontendUrl}/services/${combination}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
    });

    xml += `
</urlset>`;

    // Mettre en cache pour 4 heures
    await redis.set(cacheKey, xml, 'EX', 14400);

    logger.info(`Sitemap SEO services généré avec ${combinationsWithLastmod.size} combinaisons`);

    res.set('Content-Type', 'application/xml');
    res.send(xml);
  } catch (error) {
    logger.error('Erreur lors de la génération du sitemap SEO services:', error);
    res.status(500).send('Erreur serveur');
  }
}));

export default router;
