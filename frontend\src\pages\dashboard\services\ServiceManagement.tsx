import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { serviceApi } from './serviceApi';
import { UserService, SERVICE_CATEGORIES, SERVICE_SUBCATEGORIES } from './types';
import { ChevronLeft, ChevronRight, X, Pencil, Trash2, Info } from 'lucide-react';
import logger from '../../../utils/logger';
import './ServiceManagement.css';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Tooltip } from '@mui/material';
import { notify } from '@/components/Notification';
import axios from 'axios';
import { API_CONFIG } from '../../../config/api';
import { getCommonHeaders } from '../../../utils/headers';
import StepperServiceForm from './StepperServiceForm';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import {
  CategoryIcon,
  DescriptionIcon,
  LocationIcon,
  EuroIcon,
  AccessTimeIcon,
  ErrorIcon,
  FolderIcon,
  BriefcaseIcon,
  ClockIcon,
  YardIcon,
  HandymanIcon,
  PetsIcon,
  PersonOutlineIcon,
  EventIcon,
  BusinessIcon,
  LocalShippingIcon,
  CampaignIcon,
  SchoolIcon,
  ComputerIcon,
  CelebrationIcon,
  SpaIcon,
  PaletteIcon,
  SportsBasketballIcon,
  HomeIcon,
  DirectionsCarIcon,
  BrushIcon,
  AccountBalanceIcon,
  FlightIcon,
  ConstructionIcon
} from '@/components/icons';
import ModalPortal from '@/components/ModalPortal';
import { useCreateNotification } from '../../../hooks/useCreateNotification';
import { useSubscription } from '@/hooks/useSubscription';

const JOURS = [
    'lundi',
    'mardi',
    'mercredi',
    'jeudi',
    'vendredi',
    'samedi',
    'dimanche',
];

interface ServiceManagementProps {
  onServicesUpdate?: (services: UserService[]) => void;
}

const ServiceManagement: React.FC<ServiceManagementProps> = ({ onServicesUpdate }) => {
  const { slug } = useParams();
  const { user } = useAuth();
  const [isOwnProfil] = useState(!slug || slug === user?.profil?.data?.slug);
  const [services, setServices] = useState<UserService[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingService, setEditingService] = useState<UserService | null>(null);
  const [selectedService, setSelectedService] = useState<UserService | null>(null);
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);
  const [visibleServicesCount, setVisibleServicesCount] = useState(4);
  const [filterText, setFilterText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [filteredServices, setFilteredServices] = useState<UserService[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<string | null>(null);
  const [subscriptionConfig, setSubscriptionConfig] = useState<any>(null);
  const [toggleCooldown, setToggleCooldown] = useState<Record<string, number | null>>({});
  const [loading, setLoading] = useState(false);
  const { createSystemNotification } = useCreateNotification();
  const [serviceToToggle, setServiceToToggle] = useState<UserService | null>(null);
  const [isToggleModalOpen, setIsToggleModalOpen] = useState(false);
  const { getOptionPremiumUtilisateur } = useSubscription();

  useEffect(() => {
    loadServices();
    loadSubscriptionConfig();
  }, []);

  const loadSubscriptionConfig = async () => {
    try {
      const headers = await getCommonHeaders();
      const response = await axios.get(`${API_CONFIG.baseURL}/api/subscriptions`, {
        headers,
        withCredentials: true
      });
      if (response.data.success) {
        setSubscriptionConfig(response.data.data);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement de la configuration des abonnements:', error);
    }
  };

  useEffect(() => {
    // Filter services
    const newFilteredServices = services.filter(service => {
      const searchText = filterText.toLowerCase();
      const categoryName = SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.nom.toLowerCase() || '';
      const subcategoryName = SERVICE_SUBCATEGORIES.find(sub => sub.id === service.subcategory_id)?.nom.toLowerCase() || '';

      const matchesSearch =
        service.titre.toLowerCase().includes(searchText) ||
        service.description.toLowerCase().includes(searchText) ||
        categoryName.includes(searchText) ||
        subcategoryName.includes(searchText);

      const matchesCategory = selectedCategory === '' ||
        SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.nom === selectedCategory;

      return matchesSearch && matchesCategory;
    });

    setFilteredServices(newFilteredServices);
    if (selectedService) {
      const newIndex = newFilteredServices.findIndex(s => s.id === selectedService.id);
      setCurrentServiceIndex(newIndex > -1 ? newIndex : 0);
      if (newIndex === -1) {
        setSelectedService(newFilteredServices[0] || null);
      }
    }

    // Notifier le parent des changements
    onServicesUpdate?.(services);
  }, [services, filterText, selectedCategory, selectedService, onServicesUpdate]);

  const loadServices = useCallback(async () => {
    try {
      setLoading(true);
      const endpoint = isOwnProfil ?
        `${API_CONFIG.baseURL}/api/services/user` :
        `${API_CONFIG.baseURL}/api/services/user/${slug}`;

      const headers = await getCommonHeaders();
      const response = await axios.get(endpoint, {
        headers,
        withCredentials: true
      });

      // Transformer les services pour ajouter les URLs des images
      const servicesWithImages = response.data.map((service: UserService) => ({
        ...service,
        imageUrl: {
          webp: SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.image.webp || '',
          jpg: SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.image.jpg || '',
          alt: SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.image.alt || '',
        },
        icon: SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.icon || ''
      }));

      setServices(servicesWithImages);
      logger.info('Services chargés avec succès:', servicesWithImages);
      if (onServicesUpdate) {
        onServicesUpdate(servicesWithImages);
      }
    } catch (error) {
      logger.error('Erreur lors de la récupération des services:', error);
      notify('Erreur lors de la récupération des services', 'error');
    } finally {
      setLoading(false);
    }
  }, [slug, onServicesUpdate]);

  const handleDelete = async (serviceId: string) => {
    setServiceToDelete(serviceId);
    setIsDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (serviceToDelete) {
      try {
        await serviceApi.deleteService(serviceToDelete);
        setServices(prevServices => prevServices.filter(service => service.id !== serviceToDelete));
        setServiceToDelete(null);
        await createSystemNotification(
          'Service supprimé',
          `Vous avez supprimé un de vos services. Vous pouvez toujours en ajouter de nouveaux depuis votre profil.`,
          `/dashboard/profil`
        );
      } catch (error) {
        logger.error('Erreur lors de la suppression:', error);
      }
    }
    setIsDialogOpen(false);
  };

  const handleToggleStatus = async (service: UserService) => {
    if (toggleCooldown[service.id] && toggleCooldown[service.id]! > Date.now()) {
      return;
    }

    // Si on désactive un service, on affiche la modale explicative
    if (service.statut === 'actif') {
      setServiceToToggle(service);
      setIsToggleModalOpen(true);
      return;
    }

    // Si on active un service, on procède directement
    await toggleServiceStatus(service);
  };

  const toggleServiceStatus = async (service: UserService) => {
    try {
      setToggleCooldown(prev => ({ ...prev, [service.id]: Date.now() + 3000 }));

      const newStatus = service.statut === 'actif' ? 'inactif' : 'actif';
      const updatedService = await serviceApi.toggleServiceStatus(service.id, newStatus);

      const updatedServiceWithImage = {
        ...updatedService,
        imageUrl: {
          webp: SERVICE_CATEGORIES.find(category => category.id === updatedService.category_id)?.image.webp || '',
          jpg: SERVICE_CATEGORIES.find(category => category.id === updatedService.category_id)?.image.jpg || '',
          alt: SERVICE_CATEGORIES.find(category => category.id === updatedService.category_id)?.image.alt || '',
        },
        icon: SERVICE_CATEGORIES.find(category => category.id === updatedService.category_id)?.icon || '',
      };

      setServices(services.map(s => s.id === service.id ? updatedServiceWithImage : s));
      notify('Statut changé avec succès : ' + newStatus, 'success');

      setTimeout(() => {
        setToggleCooldown(prev => ({ ...prev, [service.id]: null }));
      }, 3000);
    } catch (error: any) {
      // Vérifier si c'est une erreur de limite d'abonnement
      if (error.response?.status === 403 && error.response?.data?.error === 'Limite de services atteinte') {
        const serviceLimit = error.response.data.serviceLimit || getOptionPremiumUtilisateur('services') || 2;
        const activeCount = error.response.data.activeCount || 0;

        notify(
          `Vous ne pouvez pas avoir plus de ${serviceLimit} service(s) actif(s) avec votre abonnement actuel. Vous avez déjà ${activeCount} service(s) actif(s). Passez à un abonnement supérieur pour activer plus de services.`,
          'error'
        );
      } else {
        notify('Erreur lors du changement de statut, ressayez dans quelques instants ...', 'error');
      }

      logger.error('Erreur lors du changement de statut:', error);
      setToggleCooldown(prev => ({ ...prev, [service.id]: null }));
    }
  };

  const handleEdit = (service: UserService) => {
    setEditingService(service);
    setIsFormOpen(true);
  };

  const handleServiceClick = (service: UserService) => {
    setSelectedService(service);
    const newIndex = filteredServices.findIndex(s => s.id === service.id);
    setCurrentServiceIndex(newIndex > -1 ? newIndex : 0);
  };

  const handlePreviousService = () => {
    if (currentServiceIndex > 0) {
      setCurrentServiceIndex(currentServiceIndex - 1);
      setSelectedService(filteredServices[currentServiceIndex - 1]);
    }
  };

  const handleNextService = () => {
    if (currentServiceIndex < filteredServices.length - 1) {
      setCurrentServiceIndex(currentServiceIndex + 1);
      setSelectedService(filteredServices[currentServiceIndex + 1]);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleLoadMore = () => {
    setVisibleServicesCount(prevCount => prevCount + 4);
  };

  const visibleServices = filteredServices.slice(0, visibleServicesCount);

  const uniqueCategories = [...new Set(services.map(service => SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.nom).filter(Boolean))];

  // Composant de dialogue de confirmation
  const ConfirmationDialog: React.FC<{
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
  }> = ({ open, onClose, onConfirm, title, message }) => {
    return (
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>{title}</DialogTitle>
        <DialogContent>
          <Typography>{message}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} color="inherit">
            Annuler
          </Button>
          <Button onClick={onConfirm} variant="contained" sx={{
            backgroundColor: '#FF6B2C',
            '&:hover': {
              backgroundColor: '#FF7A35',
            },
          }}>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const handleBulletClick = (index: number) => {
    setCurrentServiceIndex(index);
    setSelectedService(filteredServices[index]);
  };

  const handleAddService = () => {
    const serviceLimit = getOptionPremiumUtilisateur('services') || 1;
    if (services.length >= serviceLimit) {
      notify(`Vous ne pouvez pas avoir plus de ${serviceLimit} service(s) avec votre abonnement, vous en avez actuellement ${services.length}. Supprimez un service avant d'en ajouter un nouveau ou passez à un abonnement supérieur.`, 'error');
      return;
    }
    setIsFormOpen(true);
  };

  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      Yard: YardIcon,
      Handyman: HandymanIcon,
      Pets: PetsIcon,
      PersonOutline: PersonOutlineIcon,
      Event: EventIcon,
      Business: BusinessIcon,
      LocalShipping: LocalShippingIcon,
      Campaign: CampaignIcon,
      School: SchoolIcon,
      Computer: ComputerIcon,
      Celebration: CelebrationIcon,
      Spa: SpaIcon,
      Palette: PaletteIcon,
      SportsBasketball: SportsBasketballIcon,
      Home: HomeIcon,
      DirectionsCar: DirectionsCarIcon,
      Brush: BrushIcon,
      AccountBalance: AccountBalanceIcon,
      Flight: FlightIcon,
      Construction: ConstructionIcon
    };

    const IconComponent = iconMap[iconName];
    return IconComponent ? <IconComponent className="h-5 w-5" /> : <CategoryIcon className="h-5 w-5" />;
  };

  return (
    <div>
      <section className="bg-white rounded-xl">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-[#FFF8F3] rounded-lg shrink-0">
              <BriefcaseIcon className="h-6 w-6 text-[#FF6B2C]" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800">
              {isOwnProfil ? 'Mes Services' : `Services`}
            </h2>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <input
            type="text"
            placeholder="Filtrer les services..."
            value={filterText}
            onChange={handleFilterChange}
            className="w-full sm:w-1/2 px-4 py-2 border rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] transition-colors"
          />
          <select
            value={selectedCategory}
            onChange={handleCategoryChange}
            className="w-full sm:w-1/2 px-4 py-2 border rounded-md focus:ring-[#FF6B2C] focus:border-[#FF6B2C] transition-colors"
          >
            <option value="">Toutes les catégories</option>
            {uniqueCategories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div className="mb-8">
          {filteredServices.length === 0 ? (
            <motion.div
              className="mt-8 bg-gradient-to-br from-[#FFF8F3] to-white rounded-xl p-8 border border-[#FFE4BA] shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-4 bg-white rounded-full shadow-md">
                  <FolderIcon className="h-8 w-8 text-[#FF6B2C]" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800">Aucun service disponible</h3>
                <p className="text-gray-600 max-w-lg">
                  Vous n'avez pas encore ajouté de services.
                  <br /> Commencez à proposer vos services pour augmenter vos chances de trouver des clients !
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleAddService}
                  className="mt-4 px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl flex items-center space-x-3 group"
                >
                  <FolderIcon className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  <span className="font-medium">Ajouter un service</span>
                </motion.button>
              </div>
            </motion.div>
          ) : (
            <div
              className={`flex flex-col flex-wrap ${filteredServices.length > 0 ? 'jp-services-container' : ''}`}
            >
              {visibleServices.map((service) => (
                <motion.div
                  key={service.id}
                  className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-200 mb-4 jp-service-item relative group"
                  style={{
                    transform: 'translateY(-2px)',
                    width: '100%',
                  }}
                  onClick={() => handleServiceClick(service)}
                >
                  {service.statut === 'inactif' && (
                    <div className="absolute inset-0 bg-gray-200/80 z-[1] pointer-events-none service-disabled-overlay">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 service-disabled-message">
                        <div className="bg-gray-500 text-white px-4 py-2 rounded-md text-center">
                          Service désactivé
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="flex flex-col sm:flex-row">
                    <div className="relative sm:w-1/3">
                      <picture className="w-full">
                        <source type="image/webp" srcSet={service.imageUrl.webp} />
                        <source type="image/jpeg" srcSet={service.imageUrl.jpg} />
                        <img
                          src={service.imageUrl.jpg}
                          alt={service.imageUrl.alt}
                          className={`w-full mt-0 transition-all duration-300 ${service.statut === 'inactif' ? 'grayscale brightness-75' : ''}`}
                          loading="lazy"
                        />
                      </picture>
                      <div className="absolute top-0 right-0 flex flex-col items-end">
                        <div className="flex flex-col items-end">
                          <div className="flex items-center gap-2 bg-gradient-to-r from-[#FF6B2C] to-[#FF965E] text-white px-3 py-2 rounded-bl-xl shadow-lg z-[2]">
                            {(() => {
                              const category = SERVICE_CATEGORIES.find(category => category.id === service.category_id);
                              return category ? getIconComponent(category.icon) : null;
                            })()}
                            <div className="flex flex-col">
                              <span className="font-semibold text-sm">
                                {SERVICE_CATEGORIES.find(category => category.id === service.category_id)?.nom}
                              </span>
                              <div className="flex items-center gap-1 text-[14px] text-white/90 mt-0.5">
                                <span>
                                  {SERVICE_SUBCATEGORIES.find(sub => sub.id === service.subcategory_id)?.nom}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-6 sm:w-2/3">
                      <div className="jp-service-header">
                        <h3 className="font-bold text-gray-900 text-lg mb-1">{service.titre}</h3>
                        {isOwnProfil && (
                          <div
                            className="jp-gallery-actions flex items-center gap-1 z-20"
                            onClick={(e) => e.stopPropagation()}
                          >
                            {service.statut === 'inactif' ? (
                              <Tooltip title="Activer">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleToggleStatus(service);
                                  }}
                                  className="p-2 bg-white hover:bg-white text-gray-600 hover:text-green-500 rounded-lg transition-colors shadow-sm z-20"
                                  disabled={!!toggleCooldown[service.id]}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                    <path d="M12 5v14M5 12h14"></path>
                                  </svg>
                                </button>
                              </Tooltip>
                            ) : (
                              <Tooltip title="Désactiver">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleToggleStatus(service);
                                  }}
                                  className="p-2 bg-white/90 text-gray-600 hover:text-amber-500 rounded-lg transition-colors shadow-sm"
                                  disabled={!!toggleCooldown[service.id]}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                    <path d="M5 12h14"></path>
                                  </svg>
                                </button>
                              </Tooltip>
                            )}
                            <Tooltip title="Modifier">
                              <button
                                onClick={(event) => {
                                  event.stopPropagation();
                                  handleEdit(service);
                                }}
                                className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-[#FF6B2C] rounded-lg transition-colors shadow-sm"
                              >
                                <Pencil className="h-5 w-5" />
                              </button>
                            </Tooltip>
                            <Tooltip title="Supprimer le service">
                              <button
                                onClick={(event) => {
                                  event.stopPropagation();
                                  handleDelete(service.id);
                                }}
                                className="p-2 bg-white/90 hover:bg-white text-gray-600 hover:text-red-500 rounded-lg transition-colors shadow-sm"
                              >
                                <Trash2 className="h-5 w-5" />
                              </button>
                            </Tooltip>
                          </div>
                        )}
                      </div>

                      <p className="text-sm text-gray-700 line-clamp-3 mb-4 whitespace-pre-line">
                        {service.description.replace(/<[^>]*>/g, '')}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-[#FF6B2C] text-sm font-semibold">{service.tarif_horaire} €/h</span>
                        {toggleCooldown[service.id] && (
                          <div className="text-xs text-gray-500 italic">
                            Patientez {Math.ceil((Number(toggleCooldown[service.id]) - Date.now()) / 1000)}s avant de changer à nouveau le statut
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={(event) => {
                      event.stopPropagation();
                      handleServiceClick(service);
                    }}
                    className="absolute bottom-0 right-0 m-0 text-white cursor-pointer font-bold text-sm transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-[#FF965E] rounded-tl-md px-5 py-0 bg-[#FF6B2C] opacity-0 group-hover:opacity-100"
                  >
                    Voir l'aperçu
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {filteredServices.length > visibleServicesCount && (
          <div className="flex justify-center mt-4">
            <button
              onClick={handleLoadMore}
              className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors shadow-md hover:shadow-lg"
            >
              Voir vos autres services
            </button>
          </div>
        )}

        {filteredServices.length > 0 && isOwnProfil && (
          <motion.div
            className="mt-8 bg-gradient-to-br from-[#FFF8F3] to-white rounded-xl p-8 border border-[#FFE4BA] shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="p-4 bg-white rounded-full shadow-md">
                <FolderIcon className="h-8 w-8 text-[#FF6B2C]" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800">Envie de proposer plus de services ?</h3>
              <p className="text-gray-600 max-w-lg">
                Augmentez vos opportunités en proposant de nouveaux services. Plus vous diversifiez vos offres, plus vous augmentez vos chances de trouver des clients !
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleAddService}
                className="mt-4 px-6 py-3 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-all duration-300 shadow-md hover:shadow-xl flex items-center space-x-3 group"
              >
                <FolderIcon className="h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                <span className="font-medium">Ajouter un nouveau service</span>
              </motion.button>
            </div>
          </motion.div>
        )}

        {selectedService && (
          <ModalPortal>
            <div
              className="fixed inset-0"
              style={{ zIndex: 1200 }}
              onClick={() => setSelectedService(null)}
            />
            <div
              className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-[#FFF8F3] p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-[900px] max-h-[90vh] overflow-y-auto"
              style={{ zIndex: 1300 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedService(null)}
                className="absolute top-4 right-4 p-2.5 bg-white/10 hover:bg-[#FF6B2C]/10 text-[#FF6B2C] rounded-xl transition-all duration-300 border border-[#FF6B2C]/20"
              >
                <X className="h-5 w-5" />
              </button>

              <h2 className="text-3xl font-bold mb-6 text-center text-[#FF6B2C] font-heading">{selectedService.titre}</h2>

              {selectedService.statut === 'inactif' && (
                <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded">
                  <div className="flex items-center">
                    <ErrorIcon className="h-5 w-5 mr-2" />
                    <p>
                      <span className="font-bold">Service actuellement désactivé.</span>
                      <br />
                      <span className="text-sm">Ce service n'est pas visible par les utilisateurs de la plateforme. Activez-le pour le rendre à nouveau disponible.</span>
                    </p>
                  </div>
                </div>
              )}

              <AnimatePresence initial={false} mode="wait">
                <motion.div
                  key={selectedService.id}
                >
                  <div className="relative group">
                    <div className="relative overflow-hidden rounded-xl">
                      <img
                        src={selectedService.imageUrl.jpg}
                        alt={selectedService.titre}
                        className={`w-full h-[300px] object-cover rounded-xl shadow-md transform transition-transform duration-300 group-hover:scale-105 ${selectedService.statut === 'inactif' ? 'grayscale brightness-75' : ''}`}
                      />
                      {filteredServices.length > 1 && (
                        <>
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={handlePreviousService}
                            className={`absolute left-2 top-1/3 transform bg-[#FF6B2C] hover:bg-[#FF965E] text-white rounded-[8px] p-2 transition-colors duration-300 shadow-lg ${
                              currentServiceIndex === 0 ? 'opacity-0 pointer-events-none' : 'opacity-100'
                            }`}
                          >
                            <ChevronLeft className="h-6 w-6" />
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={handleNextService}
                            className={`absolute right-2 top-1/3 transform bg-[#FF6B2C] hover:bg-[#FF965E] text-white rounded-[8px] p-2 transition-colors duration-300 shadow-lg ${
                              currentServiceIndex === filteredServices.length - 1 ? 'opacity-0 pointer-events-none' : 'opacity-100'
                            }`}
                          >
                            <ChevronRight className="h-6 w-6" />
                          </motion.button>
                        </>
                      )}
                    </div>
                    {filteredServices.length > 1 && (
                      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {filteredServices.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => handleBulletClick(index)}
                            className={`block w-3 h-3 rounded-full transition-colors duration-300 focus:outline-none ${
                              index === currentServiceIndex ? 'bg-[#FF6B2C]' : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                    <div className="bg-white p-6 rounded-xl shadow-md">
                      <div className="flex items-center gap-3 mb-4">
                        <LocationIcon className="h-6 w-6 text-[#FF6B2C]" />
                        <h3 className="text-xl font-semibold text-gray-800">Catégorie</h3>
                      </div>
                      <p className="text-lg font-medium text-gray-800 mb-2">
                        {SERVICE_CATEGORIES.find(category => category.id === selectedService.category_id)?.nom}
                      </p>
                      <p className="text-gray-600">
                        {SERVICE_SUBCATEGORIES.find(sub => sub.id === selectedService.subcategory_id)?.description}
                      </p>
                    </div>

                    <div className="bg-white p-6 rounded-xl shadow-md">
                      <div className="flex items-center gap-3 mb-4">
                        <EuroIcon className="h-6 w-6 text-[#FF6B2C]" />
                        <h3 className="text-xl font-semibold text-gray-800">Tarif Horaire</h3>
                      </div>
                      <div className="flex items-center">
                        <span className="text-2xl font-bold text-[#FF6B2C]">{selectedService.tarif_horaire}€</span>
                        <span className="text-gray-600 ml-2">/heure</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <DescriptionIcon className="h-6 w-6 text-[#FF6B2C]" />
                      <h3 className="text-xl font-semibold text-gray-800">Description</h3>
                    </div>
                    <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                      {selectedService.description.replace(/<[^>]*>/g, '')}
                    </p>
                  </div>

                  <div className="mt-8 bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center gap-3 mb-4">
                      <AccessTimeIcon className="h-6 w-6 text-[#FF6B2C]" />
                      <h3 className="text-xl font-semibold text-gray-800">Disponibilités</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {JOURS.map(jour => {
                        const horaire = selectedService.horaires?.[jour] || { debut: '09:00', fin: '18:00', disponible: false, creneaux: [] };
                        const hasActiveSlots = horaire.disponible ||
                          (horaire.creneaux && horaire.creneaux.some(c => c.disponible !== false));

                        return (
                          <div key={jour} className={`border-l-4 ${hasActiveSlots ? 'border-[#FF6B2C]' : 'border-gray-300'} pl-4 py-2`}>
                            <div className={`font-medium capitalize flex items-center gap-2 ${hasActiveSlots ? 'text-gray-800' : 'text-gray-400'}`}>
                              <ClockIcon className={`h-5 w-5 ${hasActiveSlots ? 'text-[#FF6B2C]' : 'text-gray-300'}`} />
                              {jour}
                            </div>
                            {hasActiveSlots ? (
                              <>
                                {horaire.disponible && (
                                  <div className="ml-7 text-gray-600">
                                    {horaire.debut} - {horaire.fin}
                                  </div>
                                )}
                                {horaire.creneaux && horaire.creneaux
                                  .filter(creneau => creneau.disponible !== false)
                                  .map((creneau, index) => (
                                    <div key={index} className="ml-7 text-gray-600">
                                      {creneau.debut} - {creneau.fin}
                                    </div>
                                  ))}
                              </>
                            ) : (
                              <div className="ml-7 text-gray-400 italic">
                                Non disponible
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedService(null)}
                    className="mt-8 w-full bg-[#FF6B2C] text-white px-8 py-4 rounded-xl font-semibold hover:bg-[#FF965E] transition-colors duration-300 shadow-lg"
                  >
                    Fermer
                  </motion.button>
                </motion.div>
              </AnimatePresence>
            </div>
          </ModalPortal>
        )}

      {isFormOpen && (
        <StepperServiceForm
          open={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setEditingService(null);
          }}
          onSubmit={loadServices}
          existingServices={services.filter(s => s.id !== editingService?.id)}
          editingService={editingService}
        />
      )}

        <ConfirmationDialog
          open={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onConfirm={confirmDelete}
          title="Confirmation de suppression"
          message="Êtes-vous sûr de vouloir supprimer ce service ? Cette action est irréversible."
        />

        {/* Modale explicative pour la désactivation d'un service */}
        {isToggleModalOpen && serviceToToggle && (
          <ModalPortal
            closeOnBackdropClick={true}
            onBackdropClick={() => setIsToggleModalOpen(false)}
          >
            <div className="bg-white rounded-xl shadow-2xl p-6 max-w-lg w-full mx-auto relative">
              <button
                onClick={() => setIsToggleModalOpen(false)}
                className="absolute top-4 right-4 p-1.5 bg-gray-100 hover:bg-gray-200 text-gray-500 rounded-full transition-all duration-300"
              >
                <X className="h-5 w-5" />
              </button>

              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-orange-100 rounded-full">
                  <Info className="h-6 w-6 text-[#FF6B2C]" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800">Désactiver ce service ?</h3>
              </div>

              <div className="mb-6 text-gray-600">
                <p className="mb-3">En désactivant ce service, vous devez savoir que :</p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Ce service ne sera plus visible par les clients sur la plateforme</li>
                  <li>Vous ne recevrez plus d'offres ou de demandes pour ce service</li>
                  <li>Vos statistiques liées à ce service seront toujours accessibles</li>
                  <li>Vous pourrez réactiver ce service à tout moment</li>
                  <li>Les missions en cours liées à ce service ne seront pas affectées</li>
                </ul>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsToggleModalOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  Annuler
                </button>
                <button
                  onClick={() => {
                    toggleServiceStatus(serviceToToggle);
                    setIsToggleModalOpen(false);
                  }}
                  className="px-4 py-2 bg-[#FF6B2C] text-white rounded-lg hover:bg-[#FF7A35] transition-colors"
                >
                  Confirmer la désactivation
                </button>
              </div>
            </div>
          </ModalPortal>
        )}
      </section>
    </div>
  );
};

export default ServiceManagement;