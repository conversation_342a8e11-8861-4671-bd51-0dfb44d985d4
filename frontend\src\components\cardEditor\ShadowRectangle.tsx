import React from 'react';
import { Rect, Circle, Ellipse, Star, RegularPolygon, Ring, Arc, Wedge } from 'react-konva';
import { CardElement } from '../../types/cardEditor';

interface ShadowRectangleProps {
  element: CardElement | null;
  blockSnapSize: number;
  visible: boolean;
}

/**
 * Composant qui affiche une ombre pour indiquer la position de snap d'un élément
 */
const ShadowRectangle: React.FC<ShadowRectangleProps> = ({ element, blockSnapSize, visible }) => {
  if (!visible || !element) return null;

  // Calculer la position de snap
  const x = Math.round((element.x || 0) / blockSnapSize) * blockSnapSize;
  const y = Math.round((element.y || 0) / blockSnapSize) * blockSnapSize;

  if (element.type === 'shape') {
    const shape = element.properties.shape;
    const width = element.width || 100;
    const height = element.height || 100;
    const rotation = element.rotation || 0;
    switch (shape) {
      case 'circle': {
        const radius = Math.min(width, height) / 2;
        return (
          <Circle
            x={x + width / 2}
            y={y + height / 2}
            radius={radius}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'ellipse': {
        return (
          <Ellipse
            x={x + width / 2}
            y={y + height / 2}
            radiusX={width / 2}
            radiusY={height / 2}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'star': {
        const numPoints = element.properties.numPoints || 5;
        const innerRadius = element.properties.innerRadius || width / 4;
        const outerRadius = element.properties.outerRadius || width / 2;
        return (
          <Star
            x={x + width / 2}
            y={y + height / 2}
            numPoints={numPoints}
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'polygon': {
        const sides = element.properties.sides || 6;
        const radius = element.properties.radius || width / 2;
        return (
          <RegularPolygon
            x={x + width / 2}
            y={y + height / 2}
            sides={sides}
            radius={radius}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'ring': {
        const innerRadius = element.properties.innerRadius || width / 4;
        const outerRadius = element.properties.outerRadius || width / 2;
        return (
          <Ring
            x={x + width / 2}
            y={y + height / 2}
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'arc': {
        const innerRadius = element.properties.innerRadius || 0;
        const outerRadius = element.properties.outerRadius || width / 2;
        const angle = element.properties.angle || 90;
        return (
          <Arc
            x={x + width / 2}
            y={y + height / 2}
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            angle={angle}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      case 'wedge': {
        const radius = element.properties.radius || width / 2;
        const angle = element.properties.angle || 60;
        return (
          <Wedge
            x={x + width / 2}
            y={y + height / 2}
            radius={radius}
            angle={angle}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
      default: {
        // Pour les autres formes (rect, line, arrow, etc.), rectangle englobant
        return (
          <Rect
            x={x}
            y={y}
            width={width}
            height={height}
            fill="#FF6B2C"
            opacity={0.3}
            stroke="#FF6B2C"
            strokeWidth={1}
            dash={[4, 4]}
            perfectDrawEnabled={false}
            listening={false}
            rotation={rotation}
          />
        );
      }
    }
  }

  // Pour les autres types d'éléments (texte, image, etc.), rectangle englobant
  const width = element.width || 0;
  const height = element.height || 0;
  const rotation = element.rotation || 0;
  return (
    <Rect
      x={x}
      y={y}
      width={width}
      height={height}
      fill="#FF6B2C"
      opacity={0.3}
      stroke="#FF6B2C"
      strokeWidth={1}
      dash={[4, 4]}
      perfectDrawEnabled={false}
      listening={false}
      rotation={rotation}
    />
  );
};

export default ShadowRectangle;
