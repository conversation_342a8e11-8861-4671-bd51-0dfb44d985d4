import { supabase } from '../config/supabase';
import { sendNewsletterReminderEmail } from '../services/emailService';
import logger from '../utils/logger';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Script pour gérer les relances et la suppression des abonnés non vérifiés à la newsletter
 * Ce script est conçu pour être exécuté quotidiennement via un cron job
 */
async function processNewsletterReminders() {
  try {
    logger.info('Début du traitement des relances newsletter');
    
    // 1. Récupérer les abonnés non vérifiés qui n'ont pas encore reçu 3 relances
    const { data: pendingSubscribers, error: fetchError } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('is_verified', false)
      .lt('reminder_count', 3)
      .is('next_reminder_at', null)
      .order('created_at', { ascending: true });
    
    if (fetchError) {
      throw new Error(`Erreur lors de la récupération des abonnés en attente: ${fetchError.message}`);
    }
    
    logger.info(`${pendingSubscribers?.length || 0} abonnés en attente de première relance trouvés`);
    
    // 2. Récupérer les abonnés qui doivent recevoir une relance aujourd'hui
    const now = new Date();
    const { data: dueSubscribers, error: dueError } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('is_verified', false)
      .lt('reminder_count', 3)
      .lte('next_reminder_at', now.toISOString())
      .order('next_reminder_at', { ascending: true });
    
    if (dueError) {
      throw new Error(`Erreur lors de la récupération des abonnés à relancer: ${dueError.message}`);
    }
    
    logger.info(`${dueSubscribers?.length || 0} abonnés à relancer aujourd'hui trouvés`);
    
    // 3. Traiter les abonnés en attente de première relance
    if (pendingSubscribers && pendingSubscribers.length > 0) {
      for (const subscriber of pendingSubscribers) {
        try {
          // Calculer la date de la prochaine relance (demain)
          const nextReminder = new Date();
          nextReminder.setDate(nextReminder.getDate() + 1);
          
          // Mettre à jour le compteur de relances et les dates
          const { error: updateError } = await supabase
            .from('newsletter_subscribers')
            .update({
              reminder_count: 1,
              last_reminder_at: now.toISOString(),
              next_reminder_at: nextReminder.toISOString()
            })
            .eq('id', subscriber.id);
          
          if (updateError) {
            logger.error(`Erreur lors de la mise à jour de l'abonné ${subscriber.email}: ${updateError.message}`);
            continue;
          }
          
          // Envoyer l'email de relance
          await sendNewsletterReminderEmail(subscriber.email, {
            token: subscriber.verification_token,
            reminderCount: 1
          });
          
          logger.info(`Première relance envoyée à ${subscriber.email}`);
        } catch (error) {
          logger.error(`Erreur lors du traitement de l'abonné ${subscriber.email}:`, error);
        }
      }
    }
    
    // 4. Traiter les abonnés qui doivent recevoir une relance aujourd'hui
    if (dueSubscribers && dueSubscribers.length > 0) {
      for (const subscriber of dueSubscribers) {
        try {
          // Incrémenter le compteur de relances
          const newReminderCount = subscriber.reminder_count + 1;
          
          // Si c'est la 3ème relance, ne pas planifier de prochaine relance
          let nextReminderDate = null;
          
          if (newReminderCount < 3) {
            // Calculer la date de la prochaine relance (demain)
            const nextReminder = new Date();
            nextReminder.setDate(nextReminder.getDate() + 1);
            nextReminderDate = nextReminder.toISOString();
          }
          
          // Mettre à jour le compteur de relances et les dates
          const { error: updateError } = await supabase
            .from('newsletter_subscribers')
            .update({
              reminder_count: newReminderCount,
              last_reminder_at: now.toISOString(),
              next_reminder_at: nextReminderDate
            })
            .eq('id', subscriber.id);
          
          if (updateError) {
            logger.error(`Erreur lors de la mise à jour de l'abonné ${subscriber.email}: ${updateError.message}`);
            continue;
          }
          
          // Envoyer l'email de relance
          await sendNewsletterReminderEmail(subscriber.email, {
            token: subscriber.verification_token,
            reminderCount: newReminderCount
          });
          
          logger.info(`Relance #${newReminderCount} envoyée à ${subscriber.email}`);
        } catch (error) {
          logger.error(`Erreur lors du traitement de l'abonné ${subscriber.email}:`, error);
        }
      }
    }
    
    // 5. Supprimer les abonnés qui ont reçu 3 relances et n'ont toujours pas vérifié leur email
    const { data: expiredSubscribers, error: expiredError } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('is_verified', false)
      .eq('reminder_count', 3)
      .lt('last_reminder_at', new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()); // 24h après la dernière relance
    
    if (expiredError) {
      throw new Error(`Erreur lors de la récupération des abonnés expirés: ${expiredError.message}`);
    }
    
    if (expiredSubscribers && expiredSubscribers.length > 0) {
      const subscriberIds = expiredSubscribers.map(s => s.id);
      
      // Supprimer les abonnés expirés
      const { error: deleteError } = await supabase
        .from('newsletter_subscribers')
        .delete()
        .in('id', subscriberIds);
      
      if (deleteError) {
        throw new Error(`Erreur lors de la suppression des abonnés expirés: ${deleteError.message}`);
      }
      
      logger.info(`${expiredSubscribers.length} abonnés non vérifiés supprimés après 3 relances`);
    } else {
      logger.info('Aucun abonné à supprimer');
    }
    
    logger.info('Traitement des relances newsletter terminé avec succès');
  } catch (error) {
    logger.error('Erreur lors du traitement des relances newsletter:', error);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  processNewsletterReminders()
    .then(() => {
      logger.info('Script de relances newsletter terminé');
      process.exit(0);
    })
    .catch(error => {
      logger.error('Erreur dans le script de relances newsletter:', error);
      process.exit(1);
    });
}

export default processNewsletterReminders;
