import { supabase } from '../config/supabase';
import { redis } from '../config/redis';
import { missionController, invalidateMissionCache } from '../controllers/missionController';
import { invalidateUserCache, invalidateReviewCache } from '../controllers/reviews';
import logger from '../utils/logger';
import { sendModerationActionEmail } from './emailServiceModeration';
import { decryptProfilDataAsync } from '../utils/encryption';

// Types pour les paramètres
interface CreateReportPayload {
  content_type: string;
  content_id: string;
  reason: string;
  reported_by: string;
}

interface GetAllReportsPayload {
  page?: number;
  limit?: number;
  status?: string;
}

interface UpdateReportPayload {
  id: string;
  status?: string;
  admin_comment?: string;
  admin_id?: string;
  action?: 'mask' | 'delete' | 'traité' | 'rejected' | 'validated' | 'restauré';
  notifyReporters?: boolean;
  notifyAuthorByEmail?: boolean;
  internal_note?: string;
}

// Créer un signalement
export async function createReport({ content_type, content_id, reason, reported_by }: CreateReportPayload) {
  // Chercher un signalement existant pour ce contenu
  const { data: existingRows, error: findError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('content_type', content_type)
    .eq('content_id', content_id)
    .order('created_at', { ascending: false }) // Prendre le plus récent en premier
    .limit(1);

  const existing = existingRows && existingRows.length > 0 ? existingRows[0] : null;
  if (findError && findError.code !== 'PGRST116') throw findError; // PGRST116 = not found

  let reportId: string;
  let reportResult;

  // Pour les profils, vérifier si le signalement existant a déjà été traité (restauré ou masqué)
  // Si c'est le cas, on va créer un nouveau signalement plutôt que de réutiliser l'ancien
  let createNewReport = false;
  if (existing && content_type === 'profile' && ['restauré', 'masqué', 'rejected', 'validated'].includes(existing.status)) {
    logger.info('Création d\'un nouveau signalement pour un profil déjà traité', {
      contentId: content_id,
      oldReportId: existing.id,
      oldStatus: existing.status
    });
    createNewReport = true;
  }

  if (existing && !createNewReport) {
    reportId = existing.id;

    // Vérifier si l'utilisateur a déjà signalé CE JOUR
    // Utiliser une approche différente pour la vérification de date
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const { count: reportCount, error: countError } = await supabase
      .from('reported_content_reports')
      .select('*', { count: 'exact', head: true })
      .eq('reported_content_id', reportId)
      .eq('user_id', reported_by)
      .gte('created_at', startOfDay.toISOString())
      .lt('created_at', endOfDay.toISOString());

    if (countError) {
      logger.info('DEBUG - Erreur lors de la vérification des signalements existants', countError);
    }

    if (reportCount && reportCount > 0) {
      logger.info('DEBUG - Tentative de signalement multiple bloquée', {
        userId: reported_by,
        contentId: content_id,
        contentType: content_type
      });
      throw new Error('Vous avez déjà signalé ce contenu aujourd\'hui.');
    }

    // Ajouter le report individuel
    logger.info('Ajout d\'un signalement individuel pour un signalement existant', {
      reported_content_id: reportId,
      user_id: reported_by
    });

    const { data: insertData, error: insertError } = await supabase.from('reported_content_reports').insert([{
      reported_content_id: reportId,
      user_id: reported_by,
      reason
    }]).select();

    if (insertError) {
      logger.error('Erreur lors de l\'ajout du signalement individuel', {
        error: insertError.message,
        code: insertError.code,
        details: insertError.details
      });
    } else {
      logger.info('Signalement individuel ajouté avec succès', {
        id: insertData?.[0]?.id,
        reported_content_id: reportId
      });

      // Réinitialiser le statut du signalement si le statut actuel n'est pas "pending" ou "in_review"
      // Cela permet de remettre en file d'attente les signalements qui ont été traités mais qui reçoivent de nouveaux signalements
      const activeStatuses = ['pending', 'in_review'];
      if (!activeStatuses.includes(existing.status)) {
        logger.info('Réinitialisation du statut du signalement après un nouveau signalement', {
          id: reportId,
          contentType: existing.content_type,
          contentId: existing.content_id,
          oldStatus: existing.status,
          newStatus: 'pending'
        });

        await supabase
          .from('reported_content')
          .update({
            status: 'pending',
            // Réinitialiser les champs de modération pour indiquer qu'une nouvelle modération est nécessaire
            admin_id: null,
            admin_comment: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', reportId);

        // Mettre à jour le statut dans l'objet existing pour les traitements suivants
        existing.status = 'pending';
        existing.admin_id = null;
        existing.admin_comment = null;
      }
    }

    // Notifier l'utilisateur qui vient de signaler
    await supabase.from('user_notifications').insert([
      {
        user_id: reported_by,
        type: 'system',
        title: 'Merci pour votre signalement',
        content: "Nous avons bien reçu votre signalement. L'équipe de modération va l'examiner dans les plus brefs délais. Merci pour votre vigilance !",
        link: null,
        is_read: false,
        is_archived: false
      }
    ]);

    // Compter dynamiquement le nombre de reports
    const { count } = await supabase
      .from('reported_content_reports')
      .select('*', { count: 'exact', head: true })
      .eq('reported_content_id', reportId);

    reportResult = { ...existing, report_count: count || 1 };

    // Masquage automatique si 2 signalements ou plus sur un commentaire
    if (existing.content_type === 'comment' && (count || 0) >= 2) {
      const { data: autoMaskComments } = await supabase
        .from('user_mission_comments')
        .select('comment, comment_original')
        .eq('id', existing.content_id)
        .limit(1);
      const commentRow = autoMaskComments && autoMaskComments.length > 0 ? autoMaskComments[0] : null;
      const AUTO_MSG = "Ce commentaire a été automatiquement masqué après plusieurs signalements. Il est en attente de modération.";
      if (commentRow && commentRow.comment !== AUTO_MSG) {
        const updateData: any = { comment: AUTO_MSG };
        if (!commentRow.comment_original) {
          updateData.comment_original = commentRow.comment;
        }
        await supabase.from('user_mission_comments').update(updateData).eq('id', existing.content_id);
      }
    }

    // Masquage automatique si 2 signalements ou plus sur un profil et envoyer une notification à l'utilisateur
    if (existing.content_type === 'profile' && (count || 0) >= 2) {
      // Récupérer l'user_id du profil AVANT de le masquer
      const { data: profils } = await supabase
        .from('user_profil')
        .select('user_id')
        .eq('id', existing.content_id)
        .limit(1);
      const profil = profils && profils.length > 0 ? profils[0] : null;

      if (profil && profil.user_id) {
        // Envoyer un email de notification AVANT de masquer le profil
        try {
          const { sendProfilMasqueEmail } = require('../services/emailServiceModeration');
          await sendProfilMasqueEmail(profil.user_id, true); // true = masquage automatique
          logger.info('Email de notification de profil masqué automatiquement envoyé', { userId: profil.user_id, contentId: existing.content_id });
        } catch (error) {
          logger.error('Erreur lors de l\'envoi de l\'email de notification de profil masqué automatiquement', {
            error: error instanceof Error ? error.message : 'Erreur inconnue',
            userId: profil.user_id,
            contentId: existing.content_id
          });
        }

        // Créer une notification interne
        await supabase.from('user_notifications').insert({
          user_id: profil.user_id,
          type: 'profile',
          title: 'Votre profil a été masqué automatiquement',
          content: `Votre profil a été signalé plusieurs fois par la communauté et a été automatiquement masqué par le système. Il n'est plus visible par les autres utilisateurs le temps qu'une vérification soit effectuée par l'équipe de modération.`,
          link: null,
          is_read: false,
          is_archived: false
        });
      }

      // Masquer le profil APRÈS avoir envoyé l'email
      await supabase
        .from('user_profil')
        .update({ profil_visible: false })
        .eq('id', existing.content_id);
    }
    // Masquage automatique si 2 signalements ou plus sur une mission
    if (existing.content_type === 'mission' && (count || 0) >= 2) {
      await supabase
        .from('user_missions')
        .update({ statut: 'en_moderation' })
        .eq('id', existing.content_id);

      // Notifier le propriétaire de la mission
      const { data: missions } = await supabase
        .from('user_missions')
        .select('user_id, titre')
        .eq('id', existing.content_id)
        .limit(1);
      const missionRow = missions && missions.length > 0 ? missions[0] : null;
      if (missionRow && missionRow.user_id) {
        await supabase.from('user_notifications').insert({
          user_id: missionRow.user_id,
          type: 'system',
          title: 'Votre mission a été masquée automatiquement',
          content: `Votre mission "${missionRow.titre}" a été signalée plusieurs fois par la communauté et a été automatiquement masquée par le système. Elle n'est plus visible par les autres utilisateurs le temps qu'une vérification soit effectuée par l'équipe de modération.`,
          link: `/dashboard/missions/${existing.content_id}`,
          is_read: false,
          is_archived: false
        });
      }
      // Invalider le cache des missions concernées
      await invalidateMissionCache(existing.content_id);
    }
    // Mise en attente de modération si 2 signalements ou plus sur un avis (review)
    if (existing.content_type === 'review' && (count || 0) >= 2) {
      await supabase
        .from('user_reviews')
        .update({ statut: 'attente_moderation' })
        .eq('id', existing.content_id);
      // Invalider le cache de l'avis concerné
      await invalidateReviewCache(existing.content_id);
    }
  } else {
    // Cas où il n'y a pas de signalement existant ou on doit créer un nouveau signalement
    // Même pour un nouveau signalement, vérifier si l'utilisateur a déjà signalé ce contenu aujourd'hui
    // Cela peut arriver si le signalement a été supprimé puis recréé
    logger.info('Vérification pour un nouveau signalement si l\'utilisateur a déjà signalé ce contenu aujourd\'hui', {
      content_type,
      content_id,
      user_id: reported_by,
      createNewReport: createNewReport
    });

    // Rechercher dans tous les signalements existants
    const { data: existingReports } = await supabase
      .from('reported_content')
      .select('id')
      .eq('content_type', content_type)
      .eq('content_id', content_id);

    if (existingReports && existingReports.length > 0) {
      // Récupérer tous les IDs de signalements pour ce contenu
      const reportIds = existingReports.map(report => report.id);

      // Utiliser une approche différente pour la vérification de date
      const startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date();
      endOfDay.setHours(23, 59, 59, 999);

      const { count: reportCount, error: countError } = await supabase
        .from('reported_content_reports')
        .select('*', { count: 'exact', head: true })
        .in('reported_content_id', reportIds)
        .eq('user_id', reported_by)
        .gte('created_at', startOfDay.toISOString())
        .lt('created_at', endOfDay.toISOString());

      if (countError) {
        logger.info('DEBUG - Erreur lors de la vérification des signalements existants (nouveau signalement)', countError);
      }

      if (reportCount && reportCount > 0) {
        logger.info('DEBUG - Tentative de signalement multiple bloquée (nouveau signalement)', {
          userId: reported_by,
          contentId: content_id,
          contentType: content_type
        });
        throw new Error('Vous avez déjà signalé ce contenu aujourd\'hui.');
      }
    }

    // Récupérer le snapshot du commentaire si c'est un commentaire
    let content_snapshot = null;
    let reported_user_id = null;
    if (content_type === 'comment') {
      const { data: comments } = await supabase
        .from('user_mission_comments')
        .select('comment, user_id')
        .eq('id', content_id)
        .limit(1);
      const commentRow = comments && comments.length > 0 ? comments[0] : null;
      if (commentRow && commentRow.comment) {
        content_snapshot = commentRow.comment;
      }
      if (commentRow && commentRow.user_id) {
        reported_user_id = commentRow.user_id;
      }
    } else if (content_type === 'message') {
      const { data: messages } = await supabase
        .from('user_messages')
        .select('sender_id, content')
        .eq('id', content_id)
        .limit(1);
      const messageRow = messages && messages.length > 0 ? messages[0] : null;
      if (messageRow && messageRow.sender_id) {
        reported_user_id = messageRow.sender_id;
      }
      if (messageRow && messageRow.content) {
        content_snapshot = messageRow.content;
      }
    } else if (content_type === 'review') {
      // Récupérer l'auteur, le commentaire et la réponse de l'avis
      const { data: reviews } = await supabase
        .from('user_reviews')
        .select('author_id, commentaire, reponse')
        .eq('id', content_id)
        .limit(1);
      const reviewRow = reviews && reviews.length > 0 ? reviews[0] : null;

      if (reviewRow && reviewRow.author_id) {
        reported_user_id = reviewRow.author_id;
      }
      // Construction du snapshot : avis + réponse éventuelle
      if (reviewRow && reviewRow.commentaire) {
        content_snapshot = `Avis : ${reviewRow.commentaire}`;
        if (reviewRow.reponse) {
          content_snapshot += `\nRéponse : ${reviewRow.reponse}`;
        }
      }
    } else if (content_type === 'mission') {
      const { data: missions } = await supabase
        .from('user_missions')
        .select('user_id')
        .eq('id', content_id)
        .limit(1);
      const missionRow = missions && missions.length > 0 ? missions[0] : null;
      if (missionRow && missionRow.user_id) {
        reported_user_id = missionRow.user_id;
      }
    } else if (content_type === 'profile') {
      const { data: profils } = await supabase
        .from('user_profil')
        .select('user_id')
        .eq('id', content_id)
        .limit(1);
      const profilRow = profils && profils.length > 0 ? profils[0] : null;
      if (profilRow && profilRow.user_id) {
        reported_user_id = profilRow.user_id;
      }
    }
    // Créer le signalement principal
    const { data: newReports, error: createError } = await supabase
      .from('reported_content')
      .insert([{
        content_type,
        content_id,
        status: 'pending',
        reason,
        content_snapshot,
        reported_by,
        reported_user_id
      }])
      .select();
    if (createError) throw createError;

    // Vérifier que le signalement a bien été créé
    if (!newReports || newReports.length === 0) {
      throw new Error('Erreur lors de la création du signalement');
    }

    const newReport = newReports[0];

    // Ajouter le premier report individuel
    logger.info('Création du premier signalement individuel', {
      reported_content_id: newReport.id,
      user_id: reported_by
    });

    const insertResult = await supabase.from('reported_content_reports').insert([{
      reported_content_id: newReport.id,
      user_id: reported_by,
      reason
    }]);

    if (insertResult.error) {
      logger.error('Erreur lors de la création du premier signalement individuel', {
        error: insertResult.error.message,
        code: insertResult.error.code,
        details: insertResult.error.details
      });
    } else {
      logger.info('Premier signalement individuel créé avec succès', {
        reported_content_id: newReport.id
      });
    }

    // Notifier l'utilisateur qui vient de signaler
    await supabase.from('user_notifications').insert([
      {
        user_id: reported_by,
        type: 'system',
        title: 'Merci pour votre signalement',
        content: "Nous avons bien reçu votre signalement. L'équipe de modération va l'examiner dans les plus brefs délais. Merci pour votre vigilance !",
        link: null,
        is_read: false,
        is_archived: false
      }
    ]);

    reportResult = { ...newReport, report_count: 1 };
  }

  // Invalidation du cache des commentaires si c'est un commentaire de mission
  if (content_type === 'comment') {
    // On suppose que le content_id est l'id du commentaire, il faut retrouver la mission associée
    const { data: comments, error: commentError } = await supabase
      .from('user_mission_comments')
      .select('mission_id')
      .eq('id', content_id)
      .limit(1);
    const comment = comments && comments.length > 0 ? comments[0] : null;
    if (!commentError && comment && comment.mission_id) {
      // Supprimer toutes les variantes du cache des commentaires de cette mission
      const keys = await redis.keys(`mission_comments:*\"missionId\":\"${comment.mission_id}\"*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    }
  }

  return reportResult;
}

// Lister les signalements (paginé, filtrable par statut)
export async function getAllReports({ page = 1, limit = 20, status }: GetAllReportsPayload) {
  let query = supabase
    .from('reported_content')
    .select('*, reported_content_reports(count), users:reported_by (id, email, user_profil(nom, prenom))', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range((page - 1) * limit, page * limit - 1);
  if (status) {
    if (status.includes(',')) {
      const statusArray = status.split(',').map(s => s.trim());
      query = query.in('status', statusArray);
    } else {
      query = query.eq('status', status);
    }
  }
  // On récupère les signalements avec le nombre de reports individuels et les infos du signaleur
  const { data, error, count } = await query;
  if (error) throw error;
  
  // On adapte le format pour ajouter report_count à chaque signalement et déchiffrer les données de profil
  const dataWithCount = await Promise.all((data || []).map(async (item: any) => {
    const result = {
      ...item,
      report_count: (item.reported_content_reports && item.reported_content_reports[0]?.count) || 1
    };
    
    // Déchiffrer les données de profil du signaleur
    if (result.users?.user_profil && Array.isArray(result.users.user_profil)) {
      const decryptedProfils = [];
      for (const profil of result.users.user_profil) {
        const decryptedProfil = await decryptProfilDataAsync(profil);
        decryptedProfils.push(decryptedProfil);
      }
      result.users.user_profil = decryptedProfils;
    }
    
    return result;
  }));
  
  return { data: dataWithCount, count };
}

// Détail d'un signalement
export async function getReportById(id: string) {
  // On récupère le signalement avec les infos du signaleur (profil + premium + extra)
  const { data: reports, error } = await supabase
    .from('reported_content')
    .select('*')
    .eq('id', id)
    .limit(1);
  if (error) throw error;

  if (!reports || reports.length === 0) {
    throw new Error('Signalement non trouvé');
  }

  const data = reports[0];

  // Récupérer l'utilisateur signalé (reported_user_id)
  let reportedUser = null;
  if (data && data.reported_user_id) {
    const { data: users } = await supabase
      .from('users')
      .select(`*, user_profil(*), user_abo(type_abonnement, statut, montant, date_debut, date_fin)`)
      .eq('id', data.reported_user_id)
      .limit(1);
    reportedUser = users && users.length > 0 ? users[0] : null;
  }

  // Ajout du nombre de signalements reçus par cet utilisateur
  let reportCount = 0;
  let profilSlug = null;
  let telephone = null;
  let nbReviewsRecus = 0;
  if (reportedUser) {
    profilSlug = reportedUser.user_profil?.[0]?.slug || null;
    telephone = reportedUser.user_profil?.[0]?.telephone || null;
    // nombre de signalements reçus (distincts)
    const userId = reportedUser.id;
    const { data: reportedContents } = await supabase
      .from('reported_content')
      .select('content_id, content_type')
      .eq('reported_user_id', userId);
    const uniqueContent = new Set((reportedContents || []).map((r: any) => r.content_type + ':' + r.content_id));
    reportCount = uniqueContent.size;
    // nombre d'avis reçus
    const { count: nbReviews } = await supabase
      .from('user_reviews')
      .select('*', { count: 'exact', head: true })
      .eq('target_user_id', userId);
    nbReviewsRecus = nbReviews || 0;
  }
  // On enrichit la réponse
  return {
    ...data,
    user: reportedUser ? {
      ...reportedUser,
      profilSlug,
      telephone,
      reportCount,
      nbReviewsRecus,
    } : null,
    // Inclure le chemin de l'image temporaire s'il existe
    temp_image_path: data.temp_image_path || null
  };
}

// Mettre à jour/modérer un signalement
export async function updateReport({ id, status, admin_comment, admin_id, action, notifyReporters, notifyAuthorByEmail, internal_note }: UpdateReportPayload & { internal_note?: string }) {
  // Récupérer le signalement
  const { data: reports, error: reportError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('id', id)
    .limit(1);
  if (reportError) throw reportError;

  if (!reports || reports.length === 0) {
    throw new Error('Signalement non trouvé');
  }

  const report = reports[0];

  // logger.info('[DEBUG] Report complet:', report);

  // logger.info('[MODERATION] updateReport : id =', id, 'status =', status, 'admin_comment =', admin_comment, 'admin_id =', admin_id, 'action =', action, 'notifyReporters =', notifyReporters, 'notifyAuthorByEmail =', notifyAuthorByEmail);

  // Gestion explicite du statut pour les profils
  if (report.content_type === 'profile' || report.content_type === 'profil') {
    if (action === 'mask' || action === 'validated') {
      status = 'masqué';
    } else if (action === 'rejected' || action === 'restauré') {
      status = 'restauré';
    }

    // Traiter automatiquement tous les autres signalements en attente pour ce même profil
    if (action && (action === 'mask' || action === 'validated' || action === 'rejected' || action === 'restauré')) {
      try {
        // Trouver tous les autres signalements en attente pour ce même profil
        const { data: otherReports } = await supabase
          .from('reported_content')
          .select('id')
          .eq('content_type', 'profile')
          .eq('content_id', report.content_id)
          .neq('id', id) // Exclure le signalement actuel
          .in('status', ['pending', 'in_review']); // Uniquement les signalements en attente

        if (otherReports && otherReports.length > 0) {
          logger.info(`Traitement automatique de ${otherReports.length} autres signalements pour le même profil`, {
            profileId: report.content_id,
            action,
            reportIds: otherReports.map(r => r.id)
          });

          // Mettre à jour tous les autres signalements avec la même action
          for (const otherReport of otherReports) {
            await supabase
              .from('reported_content')
              .update({
                status: status,
                admin_id,
                admin_comment: admin_comment ? `${admin_comment} (traitement automatique groupé)` : 'Traitement automatique groupé',
                updated_at: new Date().toISOString()
              })
              .eq('id', otherReport.id);

            // Ajouter une entrée dans l'historique pour chaque signalement traité automatiquement
            try {
              await supabase.from('reported_content_history').insert([
                {
                  report_id: otherReport.id,
                  action: action || status,
                  admin_id,
                  comment: admin_comment ? `${admin_comment} (traitement automatique groupé)` : 'Traitement automatique groupé',
                  date: new Date().toISOString(),
                }
              ]);
            } catch (e) {
              // On ignore l'erreur si la table n'existe pas
              logger.error('Erreur lors de l\'ajout d\'une entrée dans l\'historique pour un signalement traité automatiquement', {
                error: e instanceof Error ? e.message : 'Erreur inconnue',
                reportId: otherReport.id
              });
            }
          }
        }
      } catch (error) {
        logger.error('Erreur lors du traitement automatique des signalements similaires', {
          error: error instanceof Error ? error.message : 'Erreur inconnue',
          profileId: report.content_id
        });
        // On continue le traitement du signalement principal même en cas d'erreur
      }
    }
  }

  // Action de modération sur le contenu si demandé
  let newStatus = status;
  const emailAction: 'mask' | 'delete' = action === 'validated' ? 'mask' : action === 'delete' ? 'delete' : 'mask';

  if ((action === 'mask' || action === 'validated') && report.content_type === 'comment') {
    newStatus = 'masqué'; // On utilise le statut 'masqué' pour les commentaires masqués
  }

  if (action === 'rejected') {
    newStatus = 'rejected';
  }

  let missionIdToInvalidate: string | null = null;

  if (
    action === 'mask' ||
    action === 'delete' ||
    (action === 'validated' && report.content_type === 'message') ||
    action === 'validated' ||
    ((action === 'rejected' || action === 'restauré') && (report.content_type === 'profile' || report.content_type === 'profil')) ||
    (action === 'rejected' && report.content_type && report.content_type.trim().toLowerCase() === 'mission')
  ) {
    // MODÉRATION DU MESSAGE : on remplace le contenu par un message standard
    await moderateContent('message', report.content_id, 'mask');
    let modAction: 'mask' | 'delete' = 'mask';
    if (report.content_type === 'message' && action === 'validated') {
      modAction = 'delete';
    } else {
      modAction = action === 'validated' ? 'mask' : action === 'delete' ? 'delete' : 'mask';
    }
    if (report.content_type === 'comment' && (modAction === 'delete' || modAction === 'mask')) {
      // Récupérer user_id, mission_id et le commentaire AVANT suppression/modération
      const { data: comments, error: commentError } = await supabase
        .from('user_mission_comments')
        .select('mission_id, user_id, comment, comment_original')
        .eq('id', report.content_id)
        .limit(1);
      const comment = comments && comments.length > 0 ? comments[0] : null;
      let missionTitle = '';
      let originalComment = '';
      if (comment) {
        originalComment = comment.comment_original || comment.comment || '';
      }
      if (!commentError && comment && comment.mission_id) {
        missionIdToInvalidate = comment.mission_id;
        // Récupérer le titre de la mission
        const { data: missions } = await supabase
          .from('user_missions')
          .select('titre')
          .eq('id', comment.mission_id)
          .limit(1);
        const mission = missions && missions.length > 0 ? missions[0] : null;
        if (mission && mission.titre) {
          missionTitle = mission.titre;
        }
      }
      // Appeler moderateContent APRES avoir récupéré le texte original
      await moderateContent(report.content_type, report.content_id, modAction);
      // Envoi email à l'auteur du commentaire modéré si demandé (avant suppression)
      if (notifyAuthorByEmail && comment && comment.user_id) {
        const { data: users } = await supabase
          .from('users')
          .select('email')
          .eq('id', comment.user_id)
          .limit(1);
        const user = users && users.length > 0 ? users[0] : null;
        if (user && user.email) {
          await sendModerationActionEmail(user.email, {
            contentType: 'commentaire',
            action: emailAction,
            adminComment: admin_comment || '',
            cguUrl: 'https://jobpartiel.fr/conditions-generales',
            userComment: originalComment,
            missionTitle: missionTitle || ''
          });
        }
      }
      // Envoi notification interne à l'auteur du commentaire supprimé (comme pour notifyReporters)
      if (modAction === 'delete' && comment && comment.user_id) {
        await supabase.from('user_notifications').insert({
          user_id: comment.user_id,
          type: 'system',
          title: 'Votre commentaire a été modéré/supprimé',
          content: `Votre commentaire sur la mission "${missionTitle}" a été modéré/supprimé par l'équipe de modération.`,
          link: null,
          is_read: false,
          is_archived: false
        });
      }
    } else if (report.content_type === 'message') {
      // Récupérer l'auteur du message + conversation + destinataire
      const { data: messages } = await supabase
        .from('user_messages')
        .select('sender_id, content, conversation_id')
        .eq('id', report.content_id)
        .limit(1);
      const message = messages && messages.length > 0 ? messages[0] : null;
      await moderateContent('message', report.content_id, 'mask');
      if (notifyAuthorByEmail && message && message.sender_id) {
        // Récupérer la conversation pour trouver l'autre participant
        const { data: conversations } = await supabase
          .from('user_messages_conversations')
          .select('user1_id, user2_id')
          .eq('id', message.conversation_id)
          .limit(1);
        const conversation = conversations && conversations.length > 0 ? conversations[0] : null;
        let recipientId = null;
        if (conversation) {
          recipientId = conversation.user1_id === message.sender_id ? conversation.user2_id : conversation.user1_id;
        }
        let recipientName = '';
        if (recipientId) {
          const { data: recipients } = await supabase
            .from('users')
            .select('user_profil(prenom, nom)')
            .eq('id', recipientId)
            .limit(1);
          const recipient = recipients && recipients.length > 0 ? recipients[0] : null;
          if (recipient && recipient.user_profil && recipient.user_profil.length > 0) {
            // Déchiffrer les données de profil
            const decryptedProfil = await decryptProfilDataAsync(recipient.user_profil[0]);
            const prenom = decryptedProfil.prenom || '';
            const nom = decryptedProfil.nom || '';
            recipientName = prenom + (nom ? ' ' + nom.charAt(0).toUpperCase() + '.' : '');
          }
        }
        const { data: users } = await supabase
          .from('users')
          .select('email')
          .eq('id', message.sender_id)
          .limit(1);
        const user = users && users.length > 0 ? users[0] : null;
        if (user && user.email) {
          await sendModerationActionEmail(user.email, {
            contentType: 'message',
            action: emailAction,
            adminComment: admin_comment || '',
            cguUrl: 'https://jobpartiel.fr/conditions-generales',
            messageContent: message.content,
            conversationId: message.conversation_id,
            recipientName: recipientName
        });
        }
      }
      // Ajout : notification interne à l'auteur du message modéré/supprimé
      if (message && message.sender_id) {
        let conversationLink = null;
        if (message.conversation_id) {
          conversationLink = `/dashboard/messages/${message.conversation_id}`;
        }
        // Utiliser le snapshot si le contenu est masqué
        let messageContent = message.content;
        if (messageContent === 'Ce message a été modéré' && report.content_snapshot) {
          messageContent = report.content_snapshot;
        }
        await supabase.from('user_notifications').insert({
          user_id: message.sender_id,
          type: 'system',
          title: 'Votre message a été modéré/supprimé',
          content: `Votre message a été modéré ou supprimé par l'équipe de modération.\n\nContenu du message : ${messageContent}`,
          link: conversationLink,
          is_read: false,
          is_archived: false
        });
      }
    } else if (report.content_type === 'review') {
      // Récupérer l'auteur et le destinataire de l'avis
      const { data: reviews } = await supabase
        .from('user_reviews')
        .select('author_id, target_user_id, commentaire, reponse')
        .eq('id', report.content_id)
        .limit(1);
      const review = reviews && reviews.length > 0 ? reviews[0] : null;
      // Sauvegarder le snapshot du contenu de l'avis avant suppression
      if (review && review.commentaire) {
        let snapshot = `Avis : ${review.commentaire}`;
        if (review.reponse) {
          snapshot += `\nRéponse : ${review.reponse}`;
        }
        await supabase.from('reported_content')
          .update({ content_snapshot: snapshot })
          .eq('id', id);
      }
      // Notifier l'auteur par email si demandé
      if (notifyAuthorByEmail && review && review.author_id) {
        const { data: users } = await supabase
          .from('users')
          .select('email')
          .eq('id', review.author_id)
          .limit(1);
        const user = users && users.length > 0 ? users[0] : null;
        if (user && user.email) {
          const avisEmailAction = ((action as string) === 'validated' || (action as string) === 'delete') ? 'delete' : emailAction;
          await sendModerationActionEmail(user.email, {
            contentType: 'avis',
            action: avisEmailAction as 'mask' | 'delete' | 'rejected' | 'validated' | 'restauré',
            adminComment: `Votre avis a été supprimé par l'équipe de modération suite à un signalement.`,
            userComment: review.commentaire || '',
            cguUrl: 'https://jobpartiel.fr/conditions-generales',
        });
        }
      }
      // Si l'action est 'rejected', remettre l'avis en visible
      if (action === 'rejected') {
        await supabase
          .from('user_reviews')
          .update({ statut: 'visible' })
          .eq('id', report.content_id);
        await invalidateReviewCache(report.content_id);
      }
      // Si l'action est validated (suppression de l'avis)
      if ((action === 'validated' || action === 'delete') && review) {
        // Notifier l'auteur de l'avis
        await supabase.from('user_notifications').insert({
          user_id: review.author_id,
          type: 'system',
          title: 'Votre avis a été supprimé',
          content: `Votre avis a été supprimé par la modération suite à un signalement.\n\nContenu de l'avis supprimé :\n"${review.commentaire || ''}"`,
          link: null,
          is_read: false,
          is_archived: false
        });
        // Notifier le destinataire de l'avis
        if (review.target_user_id) {
          await supabase.from('user_notifications').insert({
            user_id: review.target_user_id,
            type: 'system',
            title: 'Un avis vous concernant a été supprimé',
            content: `Un avis que vous aviez reçu a été supprimé par la modération suite à un signalement.\n\nContenu de l'avis supprimé :\n"${review.commentaire || ''}"`,
            link: null,
            is_read: false,
            is_archived: false
          });
          // Envoi d'un email au destinataire de l'avis
          const { data: targetUsers } = await supabase
            .from('users')
            .select('email')
            .eq('id', review.target_user_id)
            .limit(1);
          const targetUser = targetUsers && targetUsers.length > 0 ? targetUsers[0] : null;
          if (targetUser && targetUser.email) {
            const avisEmailAction = ((action as string) === 'validated' || (action as string) === 'delete') ? 'delete' : emailAction;
            await sendModerationActionEmail(targetUser.email, {
              contentType: 'avis',
              action: avisEmailAction as 'mask' | 'delete' | 'rejected' | 'validated' | 'restauré',
              adminComment: `Un avis que vous aviez reçu a été supprimé par l'équipe de modération suite à un signalement.`,
              userComment: review.commentaire || '',
              cguUrl: 'https://jobpartiel.fr/conditions-generales',
            });
          }
        }
        // Envoi d'un email à l'auteur de l'avis
        if (review.author_id) {
          const { data: authorUsers } = await supabase
            .from('users')
            .select('email')
            .eq('id', review.author_id)
            .limit(1);
          const authorUser = authorUsers && authorUsers.length > 0 ? authorUsers[0] : null;
          if (authorUser && authorUser.email) {
            const avisEmailAction = ((action as string) === 'validated' || (action as string) === 'delete') ? 'delete' : emailAction;
            await sendModerationActionEmail(authorUser.email, {
              contentType: 'avis',
              action: avisEmailAction as 'mask' | 'delete' | 'rejected' | 'validated' | 'restauré',
              adminComment: `Votre avis a été supprimé par l'équipe de modération suite à un signalement.`,
              userComment: review.commentaire || '',
              cguUrl: 'https://jobpartiel.fr/conditions-generales',
            });
          }
        }
        // Supprimer l'avis de la base de données (à la toute fin)
        await moderateContent('review', report.content_id, 'delete');
        // Invalider le cache des avis et des utilisateurs concernés
        if (review.author_id) {
          await invalidateUserCache(review.author_id, review.target_user_id);
        }
        await invalidateReviewCache(report.content_id);
      }
    } else if (report.content_type === 'mission') {
      // Récupérer l'auteur de la mission
      const { data: missions } = await supabase
        .from('user_missions')
        .select('user_id')
        .eq('id', report.content_id)
        .limit(1);
      const mission = missions && missions.length > 0 ? missions[0] : null;
      if (notifyAuthorByEmail && mission && mission.user_id) {
        const { data: users } = await supabase
          .from('users')
          .select('email')
          .eq('id', mission.user_id)
          .limit(1);
        const user = users && users.length > 0 ? users[0] : null;
        if (user && user.email) {
          await sendModerationActionEmail(user.email, {
            contentType: 'mission',
            action: emailAction,
            adminComment: admin_comment || '',
            cguUrl: 'https://jobpartiel.fr/conditions-generales',
          });
        }
      }
      if (action === 'rejected') {
        // Remettre la mission en statut 'en_cours' (ou autre logique si besoin)
        await supabase
          .from('user_missions')
          .update({ statut: 'en_cours' })
          .eq('id', report.content_id);
        // Invalider le cache pour la mission restaurée
        await invalidateMissionCache(report.content_id);
      }
    } else if (report.content_type === 'profile' || report.content_type === 'profil') {
      // Récupérer l'auteur du profil et le slug pour l'invalidation du cache
      const { data: profils } = await supabase
        .from('user_profil')
        .select('user_id, slug')
        .eq('id', report.content_id)
        .limit(1);
      const profil = profils && profils.length > 0 ? profils[0] : null;
      const userIdToInvalidate = profil?.user_id;
      const slugToInvalidate = profil?.slug;
      if (action === 'mask' || action === 'validated') {
        if (userIdToInvalidate) {
          // Envoyer un email de notification via le nouveau service
          try {
            const { sendProfilMasqueEmail } = require('../services/emailServiceModeration');
            await sendProfilMasqueEmail(userIdToInvalidate, false); // false = masquage manuel
            logger.info('Email de notification de profil masqué par modération manuelle envoyé', {
              userId: userIdToInvalidate,
              contentId: report.content_id,
              adminId: admin_id
            });
          } catch (error) {
            logger.error('Erreur lors de l\'envoi de l\'email de notification de profil masqué par modération manuelle', {
              error: error instanceof Error ? error.message : 'Erreur inconnue',
              userId: userIdToInvalidate,
              contentId: report.content_id
            });
          }

          // Créer une notification pour l'utilisateur
          await supabase
            .from('user_notifications')
            .insert({
              user_id: userIdToInvalidate,
              type: 'profile',
              title: 'Votre profil a été masqué par la modération',
              content: `Votre profil a été masqué car il n'est pas conforme à la charte. Il n'est plus visible par les autres utilisateurs. Merci de le mettre à jour pour qu'il soit conforme. Une fois les modifications faites, ouvrez un ticket support ici pour demander une revalidation : <a href=\"/dashboard/support/new\" style=\"color:#D32F2F;text-decoration:underline;font-weight:700;margin-left:8px;\">Ouvrir un ticket support</a>`,
              link: '/dashboard/support/new',
              is_read: false,
              is_archived: false
            });
        }
      }
      if (action === 'delete') {
        return;
      }
      if (action === 'mask' || action === 'validated') {
        // L'email est déjà envoyé plus haut dans le code, avant cette section
        // Masquer le profil (profil_visible = false)
        await supabase
          .from('user_profil')
          .update({ profil_visible: false })
          .eq('id', report.content_id);
      } else if (action === 'rejected' || action === 'restauré') {
        // Vérifier d'abord si le profil était masqué avant de le restaurer
        let profilEtaitMasque = false;

        if (userIdToInvalidate) {
          // Vérifier si le profil était masqué avant de l'envoyer
          const { data: profilData } = await supabase
            .from('user_profil')
            .select('profil_visible')
            .eq('id', report.content_id)
            .limit(1);

          // Si le profil existe et qu'il était masqué (profil_visible = false) avant la restauration
          profilEtaitMasque = !!(profilData && profilData.length > 0 && profilData[0].profil_visible === false);

          logger.info('Vérification du statut du profil avant restauration', {
            userId: userIdToInvalidate,
            contentId: report.content_id,
            profilEtaitMasque: profilEtaitMasque || false
          });
        }

        // Restaurer le profil (profil_visible = true)
        await supabase
          .from('user_profil')
          .update({ profil_visible: true })
          .eq('id', report.content_id);

        // Envoyer un email de notification à l'utilisateur SEULEMENT si son profil était masqué avant
        if (userIdToInvalidate && profilEtaitMasque) {
          try {
            const { sendProfilRestaureEmail } = require('../services/emailServiceModeration');
            await sendProfilRestaureEmail(userIdToInvalidate, admin_comment || undefined);
            logger.info('Email de notification de profil restauré envoyé', {
              userId: userIdToInvalidate,
              contentId: report.content_id,
              adminId: admin_id
            });
          } catch (error) {
            logger.error('Erreur lors de l\'envoi de l\'email de notification de profil restauré', {
              error: error instanceof Error ? error.message : 'Erreur inconnue',
              userId: userIdToInvalidate,
              contentId: report.content_id
            });
          }
        } else if (userIdToInvalidate) {
          logger.info('Email de notification de profil restauré NON envoyé car le profil n\'était pas masqué', {
            userId: userIdToInvalidate,
            contentId: report.content_id
          });
        }
      }
      // Invalider le cache du profil utilisateur (user, user_deux, user_profil_public, user_profile_by_slug, user_gallery_user_specific, user_featured_photos_user_specific)
      if (userIdToInvalidate) {
        const cacheKeys = [
          `user:${userIdToInvalidate}`,
          `user_deux:${userIdToInvalidate}`
        ];
        if (slugToInvalidate) {
          cacheKeys.push(
            `user_profil_public:${slugToInvalidate}`,
            `user_profile_by_slug:${slugToInvalidate}`,
            `user_gallery_user_specific:${slugToInvalidate}`,
            `user_featured_photos_user_specific:${slugToInvalidate}`
          );
        }
        await redis.del(...cacheKeys);
        logger.info('Cache profil utilisateur invalidé suite à modération', { userId: userIdToInvalidate, slug: slugToInvalidate, cacheKeys });
      }
    }
    // Cas spécifique : remettre la mission en cours si rejetée
    if (report.content_type && report.content_type.trim().toLowerCase() === 'mission' && action === 'rejected') {
      await supabase
        .from('user_missions')
        .update({ statut: 'en_cours' })
        .eq('id', report.content_id);
      // Invalider le cache pour la mission restaurée
      await invalidateMissionCache(report.content_id);
    }
  }

  // Si on marque comme traité ou rejeté un commentaire masqué, on restaure le texte original
  if ((action === 'traité' || action === 'rejected') && report.content_type === 'comment') {
    // On va chercher le commentaire
    const { data: comments } = await supabase
      .from('user_mission_comments')
      .select('id, comment, comment_original, mission_id')
      .eq('id', report.content_id)
      .limit(1);
    const commentRow = comments && comments.length > 0 ? comments[0] : null;
    if (commentRow) {
      const isMasked = commentRow.comment === 'Ce commentaire à été modéré' || commentRow.comment?.startsWith('Ce commentaire a été automatiquement masqué');
      if (isMasked && commentRow.comment_original) {
        await supabase.from('user_mission_comments')
          .update({ comment: commentRow.comment_original, comment_original: null })
          .eq('id', commentRow.id);
      }
      // Invalider le cache si mission_id trouvé
      if (commentRow.mission_id) {
        missionIdToInvalidate = commentRow.mission_id;
      }
    }
  }
  // Si on rejette un message modéré, on restaure le snapshot
  if (action === 'rejected' && report.content_type === 'message' && report.content_snapshot) {
    await supabase
      .from('user_messages')
      .update({ content: report.content_snapshot })
      .eq('id', report.content_id);
  }

  // Invalidation effective du cache si besoin
  if (missionIdToInvalidate) {
    const keys = await redis.keys(`mission_comments:*\"missionId\":\"${missionIdToInvalidate}"*`);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }

  // Vérification du statut autorisé
  const allowedStatus = [
    'pending', 'in_review', 'validated', 'rejected', 'content_deleted', 'masqué', 'restauré', 'attente_moderation'
  ];
  if ((status !== undefined || action !== undefined) && (typeof newStatus !== 'string' || !allowedStatus.includes(newStatus))) {
    throw new Error('Statut de signalement non autorisé: ' + newStatus);
  }

  // Mettre à jour le signalement
  const updateFields: any = {};
  if (status !== undefined) updateFields.status = status;
  if (admin_comment !== undefined) updateFields.admin_comment = admin_comment;
  if (admin_id !== undefined) updateFields.admin_id = admin_id;
  if (internal_note !== undefined) updateFields.internal_note = internal_note;
  const { data: updatedReports, error } = await supabase
    .from('reported_content')
    .update(updateFields)
    .eq('id', id)
    .select();

  if (error) {
    console.error('[DEBUG] Erreur SQL lors de la mise à jour du signalement:', error);
    throw error;
  }

  if (!updatedReports || updatedReports.length === 0) {
    throw new Error('Erreur lors de la mise à jour du signalement');
  }

  const data = updatedReports[0];

  // Ajouter une entrée dans l'historique
  try {
    await supabase.from('reported_content_history').insert([
      {
        report_id: id,
        action: action || status,
        admin_id,
        comment: admin_comment,
        date: new Date().toISOString(),
      }
    ]);
  } catch (e) {
    // On ignore l'erreur si la table n'existe pas
  }

  // Notifier tous les utilisateurs ayant signalé ce contenu si demandé
  if (notifyReporters) {
    try {
      const { data: reports } = await supabase
        .from('reported_content_reports')
        .select('user_id')
        .eq('reported_content_id', id);
      if (reports && Array.isArray(reports)) {
        const notifications = reports.map((r: any) => ({
          user_id: r.user_id,
          type: 'system',
          title: "Modération d'un contenu signalé",
          content: "Un contenu que vous avez signalé a été traité par l'équipe de modération. Merci pour votre vigilance !",
          link: null,
          is_read: false,
          is_archived: false
        }));
        if (notifications.length > 0) {
          await supabase.from('user_notifications').insert(notifications);
        }
      }
    } catch (e) {
      console.error('Erreur lors de la création des notifications de modération :', e);
    }
  }

  // Refuser toutes les candidatures si mission validée
  if (action === 'validated' && report.content_type && report.content_type.trim().toLowerCase() === 'mission') {
    await refuserToutesLesCandidaturesViaController(report.content_id, admin_id || 'system');
    // Notifier le propriétaire de la mission
    const { data: missions } = await supabase
      .from('user_missions')
      .select('user_id, titre')
      .eq('id', report.content_id)
      .limit(1);
    const missionRow = missions && missions.length > 0 ? missions[0] : null;
    if (missionRow && missionRow.user_id) {
      // Notification interne
      await supabase.from('user_notifications').insert({
        user_id: missionRow.user_id,
        type: 'system',
        title: 'Votre mission a été supprimée',
        content: `Votre mission "${missionRow.titre}" a été supprimée par la modération suite à un signalement.`,
        link: '/dashboard/missions/mes-missions',
        is_read: false,
        is_archived: false
      });
      // Email (si l'utilisateur a un email)
      const { data: users } = await supabase
        .from('users')
        .select('email')
        .eq('id', missionRow.user_id)
        .limit(1);
      const user = users && users.length > 0 ? users[0] : null;
      if (user && user.email) {
        await sendModerationActionEmail(user.email, {
          contentType: 'mission',
          action: 'delete',
          adminComment: admin_comment || '',
          cguUrl: 'https://jobpartiel.fr/conditions-generales',
          missionTitle: missionRow.titre || ''
        });
      }
    }
    // Supprimer la mission
    await moderateContent('mission', report.content_id, 'delete');
    // Invalider le cache de la mission supprimée
    await invalidateMissionCache(report.content_id);
  }

  return data;
}

// Fonction utilitaire pour masquer ou supprimer le contenu signalé
async function moderateContent(content_type: string, content_id: string, action: 'mask' | 'delete'): Promise<string | null> {
  if (content_type === 'comment') {
    if (action === 'mask') {
      // Sauvegarder le texte original avant de masquer (si pas déjà fait)
      const { data: comments } = await supabase
        .from('user_mission_comments')
        .select('comment, comment_original')
        .eq('id', content_id)
        .limit(1);
      const commentRow = comments && comments.length > 0 ? comments[0] : null;
      const MANUAL_MSG = "Ce commentaire à été modéré";
      if (commentRow && commentRow.comment !== MANUAL_MSG) {
        const updateData: any = { comment: MANUAL_MSG };
        if (!commentRow.comment_original) {
          updateData.comment_original = commentRow.comment;
        }
        await supabase.from('user_mission_comments').update(updateData).eq('id', content_id);
      }
      // Récupérer mission_id pour l'invalidation du cache
      const { data: missionComments } = await supabase
        .from('user_mission_comments')
        .select('mission_id')
        .eq('id', content_id)
        .limit(1);
      const missionComment = missionComments && missionComments.length > 0 ? missionComments[0] : null;
      return missionComment && missionComment.mission_id ? missionComment.mission_id : null;
    } else if (action === 'delete') {
      // Récupérer mission_id AVANT suppression
      const { data: deleteComments } = await supabase
        .from('user_mission_comments')
        .select('mission_id')
        .eq('id', content_id)
        .limit(1);
      const deleteComment = deleteComments && deleteComments.length > 0 ? deleteComments[0] : null;
      await supabase.from('user_mission_comments').delete().eq('id', content_id);
      return deleteComment && deleteComment.mission_id ? deleteComment.mission_id : null;
    }
  } else if (content_type === 'message') {
    if (action === 'mask') {
      await supabase.from('user_messages').update({ content: 'Ce message a été modéré' }).eq('id', content_id);
    } else if (action === 'delete') {
      await supabase.from('user_messages').delete().eq('id', content_id);
    }
  } else if (content_type === 'review') {
    if (action === 'mask') {
      await supabase.from('user_reviews').update({ commentaire: 'Cet avis est actuellement en cours de modération' }).eq('id', content_id);
    } else if (action === 'delete') {
      await supabase.from('user_reviews').delete().eq('id', content_id);
    }
  } else if (content_type === 'mission') {
    if (action === 'mask') {
      await supabase.from('user_missions').update({ description: 'Cette mission est actuellement en cours de modération' }).eq('id', content_id);
    } else if (action === 'delete') {
      await supabase.from('user_missions').delete().eq('id', content_id);
    }
  } else if (content_type === 'profile' || content_type === 'profil') {
    if (action === 'delete') {
      // L'action delete n'est pas applicable à un profil, on ignore ou on log
      return null;
    }
    if (action === 'mask' || action === 'validated') {
      // Récupérer l'user_id du profil pour envoyer l'email AVANT de masquer le profil
      const { data: profils } = await supabase
        .from('user_profil')
        .select('user_id')
        .eq('id', content_id)
        .limit(1);
      const profil = profils && profils.length > 0 ? profils[0] : null;

      if (profil && profil.user_id) {
        // Importer et appeler la fonction d'envoi d'email
        try {
          const { sendProfilMasqueEmail } = require('../services/emailServiceModeration');
          await sendProfilMasqueEmail(profil.user_id, false); // false = masquage manuel
          logger.info('Email de notification de profil masqué envoyé', { userId: profil.user_id, contentId: content_id });
        } catch (error) {
          logger.error('Erreur lors de l\'envoi de l\'email de notification de profil masqué', {
            error: error instanceof Error ? error.message : 'Erreur inconnue',
            userId: profil.user_id,
            contentId: content_id
          });
        }
      }

      // Masquer le profil (profil_visible = false) APRÈS avoir envoyé l'email
      await supabase
        .from('user_profil')
        .update({ profil_visible: false })
        .eq('id', content_id);
    } else if (action === 'rejected' || action === 'restauré') {
      // Restaurer le profil (profil_visible = true)
      await supabase
        .from('user_profil')
        .update({ profil_visible: true })
        .eq('id', content_id);
    }
  }
  return null;
}

// Fonction utilitaire pour refuser toutes les candidatures d'une mission via le controller
async function refuserToutesLesCandidaturesViaController(missionId: string, adminId: string) {
  const { data: candidatures, error } = await supabase
    .from('user_mission_candidature')
    .select('id')
    .eq('mission_id', missionId)
    .in('statut', ['en_attente', 'en_cours', 'contre_offre', 'contre_offre_jobbeur']);
  if (error) {
    return;
  }
  for (const candidature of candidatures || []) {
    // Simuler req/res pour chaque appel
    const fakeReq: any = {
      user: { userId: adminId },
      params: { id: missionId, proposalId: candidature.id },
      headers: {},
      socket: { remoteAddress: '127.0.0.1' },
    };
    const fakeRes: any = {
      status: () => fakeRes,
      json: () => {},
      send: () => {},
      end: () => {},
      set: () => fakeRes,
      get: () => {},
      headersSent: false,
    };
    try {
      await missionController.rejectProposal(fakeReq, fakeRes);
    } catch (e) {
      const err = e as Error;
      console.error('[DEBUG] Erreur lors du refus automatique de la candidature (controller):', err.stack || err.message || err);
    }
  }
}

// Historique des actions sur un signalement
export async function getReportHistory(id: string) {
  try {
    const { data: history, error } = await supabase
      .from('reported_content_history')
      .select(`
        *,
        admin:admin_id (
          id,
          email,
          role,
          user_profil (
            nom,
            prenom
          )
        )
      `)
      .eq('report_id', id)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Erreur lors de la récupération de l\'historique:', error);
      throw error;
    }

    // Déchiffrer les données des profils admin
    const decryptedHistory = await Promise.all(history.map(async (h) => {
      const profil = h.admin?.user_profil?.[0] || {};
      // Déchiffrer les données de profil de l'administrateur
      const decryptedProfil = profil ? await decryptProfilDataAsync(profil) : {};
      return {
        ...h,
        admin_nom: decryptedProfil.nom || '-',
        admin_prenom: decryptedProfil.prenom || '-',
        admin_role: h.admin?.role || '-',
        admin_email: h.admin?.email || '-',
      };
    }));

    return decryptedHistory;
  } catch (error) {
    logger.error('Erreur lors de la récupération de l\'historique:', error);
    throw error;
  }
}

// Aperçu du contenu signalé
export async function getReportedContentPreview(id: string) {
  // On récupère le signalement pour avoir le type et l'id du contenu
  const { data: reports, error: reportError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('id', id)
    .limit(1);
  if (reportError) throw reportError;

  if (!reports || reports.length === 0) {
    throw new Error('Signalement non trouvé');
  }

  const report = reports[0];

  const content_type = report.content_type;
  const content_id = report.content_id;
  const content_snapshot = report.content_snapshot;
  if (content_type === 'comment') {
    const { data: comments } = await supabase
      .from('user_mission_comments')
      .select('comment, comment_original')
      .eq('id', content_id)
      .limit(1);
    const comment = comments && comments.length > 0 ? comments[0] : null;
    if (comment) return comment;
    // Si le commentaire n'existe plus, retourner le snapshot s'il existe
    if (content_snapshot) {
      return { comment: content_snapshot, comment_original: null, is_snapshot: true };
    }
    return null;
  }
  let table = '';
  if (content_type === 'message') table = 'user_messages';
  else if (content_type === 'review') table = 'user_reviews';
  else if (content_type === 'mission') table = 'user_missions';
  else if (content_type === 'profile') table = 'user_profil';
  else return null;

  // Ajout de la note pour les avis
  let selectFields = '*';
  if (content_type === 'review') {
    selectFields = '*, note';
  }

  const { data: tableData } = await supabase
    .from(table)
    .select(selectFields)
    .eq('id', content_id)
    .limit(1);
  const data = tableData && tableData.length > 0 ? tableData[0] : null;

  if (data) {
    if (content_type === 'message' && data) {
      // Si le message est modéré et qu'on a un snapshot, on renvoie les deux
      if ((data as any).content === 'Ce message a été modéré' && content_snapshot) {
        return { content: (data as any).content, content_original: content_snapshot, is_snapshot: true };
      }
      // Sinon, on renvoie le contenu actuel
      return data;
    }
    return data;
  }

  // Correction : si l'avis n'existe plus mais qu'on a un snapshot, on le renvoie !
  if (content_type === 'review' && content_snapshot) {
    return {
      commentaire: content_snapshot,
      is_snapshot: true,
      deleted_message: "Cet avis a été supprimé par la modération. Ceci est une sauvegarde du contenu d'origine.",
      note: null
    };
  }

  return null;
}

// Nouvelle fonction : récupérer la conversation complète pour un message signalé
export async function getReportedConversation(reportId: string) {
  // 1. Récupérer le signalement
  const { data: reports, error: reportError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('id', reportId)
    .limit(1);
  if (reportError) throw reportError;

  if (!reports || reports.length === 0) {
    throw new Error('Signalement non trouvé');
  }

  const report = reports[0];
  if (report.content_type !== 'message') return null;

  // 2. Récupérer le message signalé pour obtenir la conversation_id
  const { data: messageData } = await supabase
    .from('user_messages')
    .select('conversation_id')
    .eq('id', report.content_id)
    .limit(1);

  const message = messageData && messageData.length > 0 ? messageData[0] : null;
  if (!message) return null;

  const conversationId = message.conversation_id;
  if (!conversationId) return null;

  // 3. Récupérer tous les messages de la conversation (avec infos de base sur les users)
  const { data: conversationMessages, error: convError } = await supabase
    .from('user_messages')
    .select('id, sender_id, receiver_id, content, created_at, is_read, users_sender:user_messages_sender_id_fkey(id, email), users_receiver:user_messages_receiver_id_fkey(id, email)')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: true });
  if (convError) throw convError;

  // 4. Récupérer les infos de la conversation (participants)
  const { data: conversationData } = await supabase
    .from('user_messages_conversations')
    .select('id, user1_id, user2_id')
    .eq('id', conversationId)
    .limit(1);

  const conversation = conversationData && conversationData.length > 0 ? conversationData[0] : null;
  if (!conversation) return null;

  return {
    conversation: {
      id: conversation.id,
      user1_id: conversation.user1_id,
      user2_id: conversation.user2_id,
    },
    messages: conversationMessages || [],
  };
}

// Récupérer la liste des signalements individuels pour un contenu signalé
export async function getReportsForContent(reported_content_id: string) {
  // On récupère les signalements individuels avec les infos utilisateur (nom, prénom, email, téléphone)
  const { data, error } = await supabase
    .from('reported_content_reports')
    .select('id, user_id, reason, created_at, users(email, user_profil(nom, prenom, telephone))')
    .eq('reported_content_id', reported_content_id)
    .order('created_at', { ascending: true });
  if (error) {
    console.error('Erreur Supabase getReportsForContent:', error);
    throw error;
  }
  // On adapte le format pour que le frontend ait accès directement aux champs attendus
  const decryptedReports = await Promise.all((data || []).map(async (r: any) => {
    const profil = r.users?.user_profil?.[0] || {};
    // Déchiffrer les données de profil de l'utilisateur qui a signalé
    const decryptedProfil = profil ? await decryptProfilDataAsync(profil) : {};
    return {
      id: r.id,
      user_id: r.user_id,
      reason: r.reason,
      created_at: r.created_at,
      email: r.users?.email || '-',
      nom: decryptedProfil.nom || '-',
      prenom: decryptedProfil.prenom || '-',
      telephone: decryptedProfil.telephone || '-',
    };
  }));
  
  return decryptedReports;
}

// Restaurer un commentaire masqué (remettre le texte original)
export async function restoreMaskedComment(reportId: string, adminId: string) {
  // Récupérer le signalement
  const { data: reports, error: reportError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('id', reportId)
    .limit(1);
  if (reportError) throw reportError;

  if (!reports || reports.length === 0) {
    throw new Error('Signalement non trouvé');
  }

  const report = reports[0];

  if (report.content_type !== 'comment') {
    throw new Error('Seuls les commentaires peuvent être restaurés.');
  }

  // Récupérer le commentaire
  const { data: comments, error: commentError } = await supabase
    .from('user_mission_comments')
    .select('id, comment_original, mission_id')
    .eq('id', report.content_id)
    .limit(1);

  if (commentError) throw commentError;

  if (!comments || comments.length === 0) {
    throw new Error('Commentaire non trouvé');
  }

  const commentRow = comments[0];

  if (!commentRow.comment_original) {
    throw new Error('Aucun texte original à restaurer.');
  }

  // Restaurer le texte original
  await supabase.from('user_mission_comments')
    .update({ comment: commentRow.comment_original, comment_original: null })
    .eq('id', commentRow.id);

  // Mettre à jour le signalement (statut 'restauré')
  await supabase.from('reported_content')
    .update({ status: 'restauré' })
    .eq('id', reportId);

  // Ajouter une entrée dans l'historique
  await supabase.from('reported_content_history').insert([
    {
      report_id: reportId,
      action: 'restauré',
      admin_id: adminId,
      comment: 'Commentaire restauré par un administrateur',
      date: new Date().toISOString(),
    }
  ]);

  // Vider le cache des commentaires de la mission
  if (commentRow.mission_id) {
    const keys = await redis.keys(`mission_comments:*\"missionId\":\"${commentRow.mission_id}\"*`);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }

  return { success: true };
}

// Récupérer les signalements reçus par un utilisateur (avec contenu signalé)
export async function getReportsForUser(userId: string) {
  // On récupère tous les signalements où reported_user_id = userId
  const { data, error } = await supabase
    .from('reported_content')
    .select('*')
    .eq('reported_user_id', userId)
    .order('created_at', { ascending: false });
  if (error) throw error;
  // Pour chaque signalement, on récupère le texte du contenu signalé
  const results = await Promise.all((data || []).map(async (item: any) => {
    let content_text = null;
    try {
      if (item.content_type === 'comment') {
        const { data: comments } = await supabase
          .from('user_mission_comments')
          .select('comment, comment_original')
          .eq('id', item.content_id)
          .limit(1);
        const commentRow = comments && comments.length > 0 ? comments[0] : null;
        content_text = commentRow?.comment_original || commentRow?.comment || item.content_snapshot || null;
      } else if (item.content_type === 'message') {
        const { data: messages } = await supabase
          .from('user_messages')
          .select('content')
          .eq('id', item.content_id)
          .limit(1);
        const messageRow = messages && messages.length > 0 ? messages[0] : null;
        content_text = messageRow?.content || null;
      } else if (item.content_type === 'review') {
        const { data: reviews } = await supabase
          .from('user_reviews')
          .select('commentaire, reponse')
          .eq('id', item.content_id)
          .limit(1);
        const reviewRow = reviews && reviews.length > 0 ? reviews[0] : null;
        if (reviewRow && reviewRow.commentaire) {
          content_text = `Avis : ${reviewRow.commentaire}`;
          if (reviewRow.reponse) {
            content_text += `\nRéponse : ${reviewRow.reponse}`;
          }
        } else if (item.content_snapshot) {
          content_text = item.content_snapshot;
        } else {
          content_text = null;
        }
      } else if (item.content_type === 'mission') {
        const { data: missions } = await supabase
          .from('user_missions')
          .select('description')
          .eq('id', item.content_id)
          .limit(1);
        const missionRow = missions && missions.length > 0 ? missions[0] : null;
        content_text = missionRow?.description || null;
      } else if (item.content_type === 'profile') {
        const { data: profils } = await supabase
          .from('user_profil')
          .select('bio')
          .eq('id', item.content_id)
          .limit(1);
        const profilRow = profils && profils.length > 0 ? profils[0] : null;
        content_text = profilRow?.bio || null;
      }
    } catch (e) {
      content_text = null;
    }
    return {
      ...item,
      content_text,
    };
  }));
  return results;
}