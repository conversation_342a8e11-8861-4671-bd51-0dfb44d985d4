// Fichier permettant de rafraîchir le dashboard apres le changement de type utilisateur sur le bouton de changement jobbeur à non jobbeur

import React, { createContext, useContext, useState } from 'react';

// Types
interface RefreshContextType {
  refreshCount: number;
  refresh: () => void;
}

// Context
const RefreshContext = createContext<RefreshContextType | undefined>(undefined);

// Provider Component
export const RefreshProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [refreshCount, setRefreshCount] = useState(0);

  const refresh = () => {
    setRefreshCount(prev => prev + 1);
  };

  return (
    <RefreshContext.Provider value={{ refreshCount, refresh }}>
      {children}
    </RefreshContext.Provider>
  );
};

// Hook
export const useRefresh = () => {
  const context = useContext(RefreshContext);
  if (context === undefined) {
    throw new Error('useRefresh must be used within a RefreshProvider');
  }
  return context;
};

// Button Component
interface RefreshButtonProps {
  className?: string;
}

export const RefreshButton: React.FC<RefreshButtonProps> = ({ className = '' }) => {
  const { refresh } = useRefresh();

  return (
    <button
      onClick={refresh}
      className={`inline-flex items-center justify-center p-2 rounded-md text-[#FF7A35] hover:bg-[#FF7A35]/10 focus:outline-none focus:ring-2 focus:ring-[#FF7A35] ${className}`}
      aria-label="Rafraîchir"
    >
      <svg
        className="h-5 w-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
        />
      </svg>
    </button>
  );
};
