import React, { Suspense } from 'react';
import { LucideIcon } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDynamicIcon } from '../hooks/useDynamicImport';

// Types
interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
}

interface DynamicIconProps {
  name: string;
  className?: string;
  size?: number;
}

// Composant DynamicIcon
export const DynamicIcon: React.FC<DynamicIconProps> = ({ name, className, size = 24 }) => {
  const { module } = useDynamicIcon(name);
  const Icon = module as LucideIcon | undefined;

  if (!Icon) {
    return <div className={`bg-red-100 rounded ${className}`} style={{ width: size, height: size }} />;
  }

  return <Icon className={className} size={size} />;
};

// Composant AnimatedCard
export const AnimatedCard: React.FC<AnimatedCardProps> = ({ children, className = '' }) => {
  return (
    <Suspense fallback={<div className={`${className} opacity-0`}>{children}</div>}>
      <AnimatePresence>
        <motion.div
          className={className}
          // initial={{ opacity: 0, y: 20 }}
          // animate={{ opacity: 1, y: 0 }}
          // exit={{ opacity: 0, y: -20 }}
          // transition={{ duration: 0.3 }}
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </Suspense>
  );
};

// Composant AnimatedSuccessIcon
export const AnimatedSuccessIcon = () => {
  const { module } = useDynamicIcon('CheckCircle');
  const SuccessIcon = module as LucideIcon | undefined;

  return (
    <div className="relative mb-8">
      <style>{`
        @keyframes pulseGlow {
          0%, 100% {
            transform: scale(1);
            opacity: 0.1;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.2;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        .animate-glow {
          animation: pulseGlow 2s ease-in-out infinite;
        }
      `}</style>
      
      {/* Cercles décoratifs animés */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-24 h-24 rounded-full bg-[#FF7A35]/10 animate-glow"></div>
      </div>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-20 h-20 rounded-full bg-[#FF7A35]/5 animate-pulse"></div>
      </div>
      
      {/* Icône de succès qui flotte */}
      {SuccessIcon && <SuccessIcon className="mx-auto h-16 w-16 text-[#FF7A35] relative z-10 animate-float" />}
    </div>
  );
};

// Composant AnimatedMailIcon
export const AnimatedMailIcon: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`relative ${className}`}>
      <style>{`
        @keyframes mailTop {
          0%, 100% {
            transform: scaleY(1);
          }
          50% {
            transform: scaleY(0.8);
          }
        }

        @keyframes letterMove {
          0%, 100% {
            transform: translate(-50%, -50%);
            opacity: 0;
          }
          50% {
            transform: translate(-50%, -80%);
            opacity: 1;
          }
        }

        .animate-mail-top {
          animation: mailTop 2s ease-in-out infinite;
        }

        .animate-letter-move {
          animation: letterMove 2s ease-in-out infinite;
        }
      `}</style>

      {/* Enveloppe animée */}
      <svg 
        className="animate-mail-top"
        width="64" 
        height="64" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        {/* Contenu de l'SVG de l'enveloppe */}
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
        <path d="M22 4 12 14.01l-3-3" />
      </svg>
    </div>
  );
};
