import React, { createContext, useContext, ReactNode } from 'react';
import SeoPromotionModal from './SeoPromotionModal';
import { useSeoPromotion } from '../../hooks/useSeoPromotion';

interface SeoPromotionContextType {
  forceShowPromotion: (trigger: 'onboarding' | 'profile_complete' | 'first_mission' | 'notification' | 'popup') => void;
  refreshStats: () => Promise<any>;
  userStats: any;
}

const SeoPromotionContext = createContext<SeoPromotionContextType | undefined>(undefined);

interface SeoPromotionProviderProps {
  children: ReactNode;
}

export const SeoPromotionProvider: React.FC<SeoPromotionProviderProps> = ({ children }) => {
  const {
    promotionState,
    userStats,
    forceShowPromotion,
    closePromotion,
    acceptPromotion,
    refreshStats
  } = useSeoPromotion();

  const contextValue: SeoPromotionContextType = {
    forceShowPromotion,
    refreshStats,
    userStats
  };

  return (
    <SeoPromotionContext.Provider value={contextValue}>
      {children}
      <SeoPromotionModal
        isOpen={promotionState.shouldShow}
        onClose={closePromotion}
        onAccept={acceptPromotion}
        trigger={promotionState.trigger}
        refreshStats={refreshStats}
      />
    </SeoPromotionContext.Provider>
  );
};

export const useSeoPromotionContext = () => {
  const context = useContext(SeoPromotionContext);
  if (context === undefined) {
    throw new Error('useSeoPromotionContext must be used within a SeoPromotionProvider');
  }
  return context;
};

export default SeoPromotionProvider;
