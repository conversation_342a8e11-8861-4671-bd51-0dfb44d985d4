import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Pagination,
  Button,
  Chip,
  useTheme,
  SelectChangeEvent,
  Alert,
  Paper,
  Breadcrumbs,
  useMediaQuery,
  Avatar,
  Switch,
  FormControlLabel,
  Stack,
} from '@mui/material';
import { 
  FilterList as FilterListIcon, 
  Add as AddIcon, 
  Search as SearchIcon,
  Home as HomeIcon,
  Support as SupportIcon,
  Assignment as AssignmentIcon,
  Description as DescriptionIcon,
  Comment as CommentIcon,
  NotificationsActive as NotificationsActiveIcon,
  Person as PersonIcon,
  FilterAlt as FilterAltIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import TicketStatusBadge from '../../components/support/TicketStatusBadge';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { TicketFilters, User } from '../../services/supportTicketService';
import useTickets from '../../hooks/useTickets';
import TicketDetailsModal from '../../components/support/TicketDetailsModal';
import logger from '@/utils/logger';

const SupportTicketList: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [filters, setFilters] = useState<TicketFilters>({
    status: '',
    priority: '',
    category: '',
    search: '',
    user_id: '',
  });
  const [showNonAnswered, setShowNonAnswered] = useState<boolean>(false);
  const [showActiveTickets, setShowActiveTickets] = useState<boolean>(false);
  const [users, setUsers] = useState<Record<string, User>>({});
  const _isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isAdminRoute = window.location.pathname.includes('/admin/');
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);

  // Vérifier si l'utilisateur est un administrateur ou modérateur
  const isStaff = user?.role === 'jobpadm' || user?.role === 'jobmodo';

  // Utiliser notre hook personnalisé
  const {
    tickets,
    loading,
    error,
    totalCount,
    fetchTickets,
    fetchUserInfo
  } = useTickets();

  // Si l'utilisateur accède à la route admin mais n'est pas staff, rediriger
  useEffect(() => {
    if (isAdminRoute && !isStaff) {
      navigate('/dashboard');
    }
  }, [isAdminRoute, isStaff, navigate]);

  // Récupérer les infos utilisateurs pour les tickets (uniquement pour les admins)
  useEffect(() => {
    if (isStaff && tickets.length > 0) {
      const userIds = [...new Set(tickets.map((ticket: any) => ticket.user_id))];
      const fetchUsers = async () => {
        try {
          const userMap = await fetchUserInfo(userIds);
          setUsers(userMap);
        } catch (err) {
          logger.error('Erreur lors de la récupération des utilisateurs:', err);
        }
      };
      fetchUsers();
    }
  }, [isStaff, tickets, fetchUserInfo]);

  // Mettre à jour les données lorsque les filtres ou la pagination changent
  useEffect(() => {
    // Référence pour savoir si le composant est monté
    let isMounted = true;
    
    // Préparer les filtres pour l'API
    const apiFilters: TicketFilters = {
      page: page,
      limit: limit,
    };
    
    // Ajouter les filtres si présents
    if (filters.status) {
      apiFilters.status = filters.status;
    }
    if (filters.priority) {
      apiFilters.priority = filters.priority;
    }
    if (filters.category) {
      apiFilters.category = filters.category;
    }
    if (filters.search) {
      apiFilters.search = filters.search;
    }
    if (filters.user_id && isStaff) {
      apiFilters.user_id = filters.user_id;
    }
    // Ajouter le filtre repondu s'il est présent
    if (filters.repondu !== undefined) {
      apiFilters.repondu = filters.repondu;
    }
    // Ajouter le filtre pour les statuts si showActiveTickets est activé
    if (filters.status_not_in) {
      apiFilters.status_not_in = filters.status_not_in;
    }

    // Si l'utilisateur n'est pas membre du staff, filtrer par user_id
    if (!isStaff && user?.id) {
      apiFilters.user_id = user.id;
    }

    // Utilisons setTimeout pour éviter des appels trop fréquents
    const timerId = setTimeout(() => {
      // Ne faire l'appel que si le composant est toujours monté
      if (isMounted) {
        fetchTickets(apiFilters);
      }
    }, 300);
    
    // Nettoyage
    return () => {
      clearTimeout(timerId);
      isMounted = false;
    };
  }, [page, limit, filters, isStaff, user?.id, fetchTickets]);

  // Handlers spécifiques selon le type de champ
  const handleTextFieldChange = (field: keyof typeof filters) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFilters(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    setPage(1);
  };

  const handleSelectChange = (field: keyof typeof filters) => (
    event: SelectChangeEvent
  ) => {
    setFilters(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
    setPage(1);
  };

  const handleCreateTicket = () => {
    navigate(isAdminRoute ? '/admin/support/new' : '/dashboard/support/new');
  };

  const handleTicketClick = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setIsTicketModalOpen(true);
  };

  const handleCloseTicketModal = () => {
    setIsTicketModalOpen(false);
    // Rafraîchir les tickets après fermeture de la modal
    // (cela permet de mettre à jour les statuts, notamment last_response_at)
    
    // Nettoyer les filtres avant de les envoyer à l'API
    const cleanFilters: TicketFilters = { ...filters };
    
    // Supprimer les filtres avec des valeurs vides
    // Utiliser une approche typée pour éviter les erreurs TypeScript
    if (cleanFilters.status === '') delete cleanFilters.status;
    if (cleanFilters.priority === '') delete cleanFilters.priority;
    if (cleanFilters.category === '') delete cleanFilters.category;
    if (cleanFilters.search === '') delete cleanFilters.search;
    if (cleanFilters.user_id === '') delete cleanFilters.user_id;
    
    // S'assurer que page et limit sont toujours présents
    cleanFilters.page = page;
    cleanFilters.limit = limit;
    
    // Si l'utilisateur n'est pas membre du staff, ajouter son ID
    if (!isStaff && user?.id) {
      cleanFilters.user_id = user.id;
    }
    
    fetchTickets(cleanFilters);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'technique':
        return '🔧';
      case 'facturation':
        return '💰';
      case 'compte':
        return '👤';
      case 'mission':
        return '📋';
      case 'autre':
        return '📌';
      default:
        return '📄';
    }
  };

  // Effet pour mettre à jour les filtres lorsque showNonAnswered change
  useEffect(() => {
    // Mettre à jour les filtres avec repondu: false si showNonAnswered est true
    setFilters(prev => {
      const newFilters = { ...prev };
      
      if (showNonAnswered) {
        newFilters.repondu = false;
      } else if ('repondu' in newFilters) {
        delete newFilters.repondu;
      }
      
      return newFilters;
    });
  }, [showNonAnswered]); // Suppression de la dépendance filters pour éviter une boucle infinie
  
  // Effet pour mettre à jour les filtres lorsque showActiveTickets change
  useEffect(() => {
    setFilters(prev => {
      const newFilters = { ...prev };
      
      if (showActiveTickets) {
        // Assurons-nous que nous excluons correctement les tickets fermés et résolus
        newFilters.status_not_in = ['ferme', 'resolu'];
        // Pour plus de sécurité, nous supprimons également tout filtre de statut explicite
        // qui pourrait contredire notre exclusion
        if (newFilters.status === 'ferme' || newFilters.status === 'resolu') {
          delete newFilters.status;
        }
        logger.info('Filtre de tickets actifs appliqué:', newFilters.status_not_in);
      } else if ('status_not_in' in newFilters) {
        delete newFilters.status_not_in;
        logger.info('Filtre de tickets actifs supprimé');
      }
      
      return newFilters;
    });
  }, [showActiveTickets]);
  
  // Fonction pour vérifier si un ticket n'a pas reçu de réponse
  const isTicketUnanswered = (ticket: any): boolean => {
    // Utiliser directement la propriété repondu de la base de données
    // Si repondu est false, le ticket est considéré comme non répondu
    return ticket.repondu === false;
  };

  // Compter le nombre de tickets non répondus
  const nonAnsweredCount = tickets.filter(isTicketUnanswered).length;

  // Nous n'avons plus besoin de filtrer côté client car c'est fait côté serveur via l'API
  const filteredTickets = tickets;

  return (
    <Box 
      sx={{ 
        padding: '24px'
      }}
    >
      <Box>
        {/* Fil d'Ariane */}
        <Breadcrumbs 
          aria-label="breadcrumb"
          sx={{ 
            mb: 3, 
            background: 'rgba(255, 255, 255, 0.7)',
            py: 1.5, 
            px: 2, 
            borderRadius: 2,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Button
            component={Link}
            to={isAdminRoute ? "/admin" : "/dashboard"}
            size="small"
            startIcon={<HomeIcon />}
            sx={{ 
              color: 'rgba(0, 0, 0, 0.6)',
              '&:hover': { color: '#FF6B2C' },
              textTransform: 'none',
              fontWeight: 'normal'
            }}
          >
            {isAdminRoute ? "Administration" : "Tableau de bord"}
          </Button>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem' }}>
            <SupportIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#FF6B2C' }} />
            {isAdminRoute ? "Gestion des tickets" : "Tickets de support"}
          </Typography>
        </Breadcrumbs>

        {/* Titre et boutons d'action */}
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between', 
            alignItems: { xs: 'flex-start', md: 'center' }, 
            gap: { xs: 2, md: 0 },
            mb: 4
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box 
              sx={{ 
                backgroundColor: 'rgba(255, 107, 44, 0.1)', 
                borderRadius: '50%',
                p: 1.5,
                display: 'flex',
                mr: 2
              }}
            >
              {isAdminRoute ? (
                <AssignmentIcon 
                  sx={{ 
                    color: '#FF6B2C', 
                    fontSize: { xs: '2rem', sm: '2.5rem' },
                    filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
                  }} 
                />
              ) : (
                <SupportIcon 
                  sx={{ 
                    color: '#FF6B2C', 
                    fontSize: { xs: '2rem', sm: '2.5rem' },
                    filter: 'drop-shadow(0 2px 2px rgba(255, 107, 44, 0.3))'
                  }} 
                />
              )}
            </Box>
            <Box>
              <Typography 
                variant="h4" 
                component="h1" 
                sx={{ 
                  fontWeight: 700, 
                  color: '#333',
                  fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.125rem' },
                  lineHeight: 1.2
                }}
              >
                {isAdminRoute ? "Gestion des tickets" : "Tickets de support"}
              </Typography>
              <Typography
                variant="subtitle1"
                color="text.secondary"
                sx={{ mt: 0.5, fontWeight: 400 }}
              >
                {totalCount > 0 ? `${totalCount} ticket${totalCount > 1 ? 's' : ''} trouvé${totalCount > 1 ? 's' : ''}` : 'Aucun ticket trouvé'}
              </Typography>
            </Box>
          </Box>

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            {/* Bouton pour voir les tickets assignés (uniquement pour le staff) */}
            {isStaff && (
              <Button
                variant="outlined"
                startIcon={<PersonIcon />}
                onClick={() => navigate(isAdminRoute ? '/admin/support/assigned' : '/dashboard/support/assigned')}
                sx={{
                  color: '#FF6B2C',
                  borderColor: '#FF6B2C',
                  borderRadius: '8px',
                  py: 1.2,
                  px: 3,
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'rgba(255, 107, 44, 0.04)',
                    borderColor: '#FF7A35',
                  },
                }}
              >
                Mes tickets assignés
              </Button>
            )}
            
            {/* Filtres rapides (uniquement pour les admins) */}
            {isAdminRoute && isStaff && (
              <Box sx={{ display: 'flex', gap: 2 }}>
                {/* Filtre "À répondre uniquement" avec interrupteur */}
                <Paper 
                  elevation={0}
                  sx={{
                    borderRadius: '8px',
                    bgcolor: showNonAnswered ? 'rgba(255, 107, 44, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                    border: showNonAnswered ? '1px solid rgba(255, 107, 44, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
                    display: 'flex', 
                    alignItems: 'center',
                    transition: 'all 0.2s ease',
                    py: 0.5,
                    px: 1.5
                  }}
                >
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showNonAnswered}
                        onChange={(e) => setShowNonAnswered(e.target.checked)}
                        color="primary"
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#FF6B2C',
                            '&:hover': {
                              backgroundColor: 'rgba(255, 107, 44, 0.08)',
                            },
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#FF6B2C',
                          },
                        }}
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <FilterAltIcon sx={{ mr: 0.5, color: showNonAnswered ? '#FF6B2C' : 'text.secondary', fontSize: '1.1rem' }} />
                        <Typography variant="body2" sx={{ fontWeight: showNonAnswered ? 600 : 400, color: showNonAnswered ? '#FF6B2C' : 'text.secondary' }}>
                          À répondre
                          {showNonAnswered && nonAnsweredCount > 0 && (
                            <Box
                              component="span"
                              sx={{
                                ml: 1,
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: '#FF6B2C',
                                color: 'white',
                                borderRadius: '50%',
                                width: 20,
                                height: 20,
                                fontSize: '0.75rem',
                                fontWeight: 'bold'
                              }}
                            >
                              {nonAnsweredCount}
                            </Box>
                          )}
                        </Typography>
                      </Box>
                    }
                    sx={{ mb: 0 }}
                  />
                </Paper>

                {/* Filtre "Tickets actifs uniquement" avec interrupteur */}
                <Paper 
                  elevation={0}
                  sx={{
                    borderRadius: '8px',
                    bgcolor: showActiveTickets ? 'rgba(46, 125, 50, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                    border: showActiveTickets ? '1px solid rgba(46, 125, 50, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
                    display: 'flex', 
                    alignItems: 'center',
                    transition: 'all 0.2s ease',
                    py: 0.5,
                    px: 1.5
                  }}
                >
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showActiveTickets}
                        onChange={(e) => setShowActiveTickets(e.target.checked)}
                        color="success"
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#2e7d32',
                            '&:hover': {
                              backgroundColor: 'rgba(46, 125, 50, 0.08)',
                            },
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#2e7d32',
                          },
                        }}
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CheckCircleOutlineIcon sx={{ mr: 0.5, color: showActiveTickets ? '#2e7d32' : 'text.secondary', fontSize: '1.1rem' }} />
                        <Typography variant="body2" sx={{ fontWeight: showActiveTickets ? 600 : 400, color: showActiveTickets ? '#2e7d32' : 'text.secondary' }}>
                          Tickets actifs
                        </Typography>
                      </Box>
                    }
                    sx={{ mb: 0 }}
                  />
                </Paper>
              </Box>
            )}

            {/* Afficher le bouton "Nouveau ticket" seulement si ce n'est pas la route admin ou si l'utilisateur est un client */}
            {(!isAdminRoute || !isStaff) && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateTicket}
                sx={{
                  bgcolor: '#FF6B2C',
                  borderRadius: '8px',
                  py: 1.2,
                  px: 3,
                  fontWeight: 600,
                  boxShadow: '0 4px 10px rgba(255, 107, 44, 0.2)',
                  '&:hover': {
                    bgcolor: '#FF7A35',
                    boxShadow: '0 6px 15px rgba(255, 107, 44, 0.3)',
                  },
                }}
              >
                Nouveau ticket
              </Button>
            )}
          </Stack>
        </Box>

        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              mb: 3,
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
            }}
          >
            Une erreur est survenue lors de la récupération des tickets. Veuillez réessayer plus tard.
          </Alert>
        )}

        {/* Message d'information pour les filtres actifs */}
        {(showNonAnswered || showActiveTickets) && (
          <Box sx={{ mb: 3 }}>
            {showNonAnswered && (
              <Alert 
                severity="info" 
                icon={<NotificationsActiveIcon sx={{ color: '#FF6B2C' }} />}
                sx={{ 
                  mb: showActiveTickets ? 2 : 0,
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                  bgcolor: 'rgba(255, 107, 44, 0.05)',
                  border: '1px solid rgba(255, 107, 44, 0.1)',
                  color: '#333',
                  '& .MuiAlert-icon': {
                    color: '#FF6B2C',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {nonAnsweredCount === 0 ? 
                      "Tous les tickets ont été traités. Bravo !" : 
                      `${nonAnsweredCount} ticket${nonAnsweredCount > 1 ? 's' : ''} en attente de réponse.`
                    }
                  </Typography>
                </Box>
              </Alert>
            )}
            
            {showActiveTickets && (
              <Alert 
                severity="success" 
                icon={<CheckCircleOutlineIcon sx={{ color: '#2e7d32' }} />}
                sx={{ 
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                  bgcolor: 'rgba(46, 125, 50, 0.05)',
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  color: '#333',
                  '& .MuiAlert-icon': {
                    color: '#2e7d32',
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    Affichage des tickets actifs uniquement (hors tickets fermés et résolus).
                  </Typography>
                </Box>
              </Alert>
            )}
          </Box>
        )}

        {/* Filtres */}
        <Paper 
          elevation={3}
          sx={{
            borderRadius: { xs: '12px', sm: '16px' },
            overflow: 'hidden',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
            position: 'relative',
            mb: 4,
            mx: isAdminRoute ? { xs: 1, sm: 2 } : 0,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '6px',
              background: 'linear-gradient(90deg, #FF6B2C, #FF965E)',
            },
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
            '&:hover': {
              transform: 'translateY(-3px)',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
            }
          }}
        >
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <FilterListIcon sx={{ color: '#FF6B2C', mr: 1 }} />
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 600,
                  color: '#333',
                }}
              >
                Filtres
              </Typography>
            </Box>
            
            <Grid container spacing={2} alignItems="center">
              <Grid size={{ xs: 12, sm: 6, md: isStaff ? 2 : 3 }}>
                <TextField
                  fullWidth
                  label="Rechercher"
                  value={filters.search}
                  onChange={handleTextFieldChange('search')}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ color: 'rgba(0, 0, 0, 0.54)', mr: 1 }} />,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(255, 107, 44, 0.5)',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      }
                    }
                  }}
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: isStaff ? 2 : 3 }}>
                <FormControl fullWidth sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(255, 107, 44, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#FF6B2C',
                    }
                  }
                }}>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={filters.status}
                    label="Statut"
                    onChange={handleSelectChange('status')}
                  >
                    <MenuItem value="">Tous</MenuItem>
                    <MenuItem value="nouveau">Nouveau</MenuItem>
                    <MenuItem value="en_attente">En attente</MenuItem>
                    <MenuItem value="en_cours">En cours</MenuItem>
                    <MenuItem value="resolu">Résolu</MenuItem>
                    <MenuItem value="ferme">Fermé</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: isStaff ? 2 : 3 }}>
                <FormControl fullWidth sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(255, 107, 44, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#FF6B2C',
                    }
                  }
                }}>
                  <InputLabel>Priorité</InputLabel>
                  <Select
                    value={filters.priority}
                    label="Priorité"
                    onChange={handleSelectChange('priority')}
                  >
                    <MenuItem value="">Toutes</MenuItem>
                    <MenuItem value="faible">Faible</MenuItem>
                    <MenuItem value="normale">Normale</MenuItem>
                    <MenuItem value="elevee">Élevée</MenuItem>
                    <MenuItem value="urgente">Urgente</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid size={{ xs: 12, sm: 6, md: isStaff ? 2 : 3 }}>
                <FormControl fullWidth sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(255, 107, 44, 0.5)',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#FF6B2C',
                    }
                  }
                }}>
                  <InputLabel>Catégorie</InputLabel>
                  <Select
                    value={filters.category}
                    label="Catégorie"
                    onChange={handleSelectChange('category')}
                  >
                    <MenuItem value="">Toutes</MenuItem>
                    <MenuItem value="technique">Technique</MenuItem>
                    <MenuItem value="facturation">Facturation</MenuItem>
                    <MenuItem value="compte">Compte</MenuItem>
                    <MenuItem value="mission">Mission</MenuItem>
                    <MenuItem value="autre">Autre</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              {/* Afficher le filtre utilisateur uniquement pour les admins */}
              {isStaff && (
                <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                  <FormControl fullWidth sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '8px',
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: 'rgba(255, 107, 44, 0.5)',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#FF6B2C',
                      }
                    }
                  }}>
                    <InputLabel>Utilisateur</InputLabel>
                    <Select
                      value={filters.user_id}
                      label="Utilisateur"
                      onChange={handleSelectChange('user_id')}
                    >
                      <MenuItem value="">Tous</MenuItem>
                      {Object.values(users).map((user: User) => (
                        <MenuItem key={user.id} value={user.id}>
                          {user.first_name || ''} {user.last_name || ''} {user.email ? `(${user.email})` : `(#${user.id.substring(0, 8)})`}
                        </MenuItem>
                      ))}
                      {/* Afficher les utilisateurs des tickets s'ils ne sont pas déjà dans la liste */}
                      {tickets
                        .filter(ticket => ticket.user && ticket.user.id && !users[ticket.user.id])
                        .map(ticket => ticket.user)
                        .filter((user, index, self) => 
                          user && // S'assurer que user existe
                          self.findIndex(u => u && u.id === user.id) === index // Dédupliquer
                        )
                        .map(user => (
                          <MenuItem key={user?.id} value={user?.id}>
                            {user?.first_name || ''} {user?.last_name || ''} {user?.email ? `(${user.email})` : `(#${user?.id.substring(0, 8)})`}
                          </MenuItem>
                        ))
                      }
                    </Select>
                  </FormControl>
                </Grid>
              )}
            </Grid>
          </Box>
        </Paper>

        {/* Liste des tickets */}
        <Grid container spacing={3}>
          {loading ? (
            <Grid size={12} display="flex" justifyContent="center" py={6}>
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  flexDirection: 'column',
                  p: 4
                }}
              >
                <Box 
                  className="loading-dots" 
                  sx={{ 
                    display: 'flex', 
                    gap: 1, 
                    '& .dot': {
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: '#FF6B2C',
                      animation: 'pulse 1.5s infinite ease-in-out',
                    },
                    '& .dot:nth-of-type(2)': {
                      animationDelay: '0.2s'
                    },
                    '& .dot:nth-of-type(3)': {
                      animationDelay: '0.4s'
                    },
                    '@keyframes pulse': {
                      '0%, 100%': {
                        transform: 'scale(0.7)',
                        opacity: 0.5,
                      },
                      '50%': {
                        transform: 'scale(1)',
                        opacity: 1,
                      }
                    }
                  }}
                >
                  <Box className="dot"></Box>
                  <Box className="dot"></Box>
                  <Box className="dot"></Box>
                </Box>
                <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
                  Chargement des tickets...
                </Typography>
              </Box>
            </Grid>
          ) : filteredTickets.length === 0 ? (
            <Grid size={12}>
              <Paper 
                elevation={3}
                sx={{
                  borderRadius: { xs: '12px', sm: '16px' },
                  overflow: 'hidden',
                  boxShadow: '0 8px 30px rgba(0, 0, 0, 0.05)',
                  position: 'relative',
                  py: 6,
                  textAlign: 'center'
                }}
              >
                <Box sx={{ p: 4 }}>
                  <SupportIcon 
                    sx={{ 
                      fontSize: '4rem', 
                      color: 'rgba(255, 107, 44, 0.2)',
                      mb: 2
                    }} 
                  />
                  <Typography variant="h6" gutterBottom>
                    {showNonAnswered 
                      ? "Aucun ticket non répondu trouvé" 
                      : "Aucun ticket ne correspond à vos critères"}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {showNonAnswered 
                      ? "Tous les tickets ont été traités. Bravo !"
                      : `Essayez de modifier vos filtres ${!isAdminRoute ? "ou créez un nouveau ticket" : ""}`}
                  </Typography>
                  {showNonAnswered && isAdminRoute && (
                    <Button
                      variant="outlined"
                      startIcon={<FilterListIcon />}
                      onClick={() => setShowNonAnswered(false)}
                      sx={{
                        color: '#FF6B2C',
                        borderColor: '#FF6B2C',
                        borderRadius: '8px',
                        py: 1.2,
                        px: 3,
                        fontWeight: 600,
                        mt: 3,
                        '&:hover': {
                          borderColor: '#FF7A35',
                          backgroundColor: 'rgba(255, 107, 44, 0.04)',
                        },
                      }}
                    >
                      Voir tous les tickets
                    </Button>
                  )}
                  {!isAdminRoute && (
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleCreateTicket}
                      sx={{
                        bgcolor: '#FF6B2C',
                        borderRadius: '8px',
                        py: 1.2,
                        px: 3,
                        fontWeight: 600,
                        mt: 3,
                        boxShadow: '0 4px 10px rgba(255, 107, 44, 0.2)',
                        '&:hover': {
                          bgcolor: '#FF7A35',
                          boxShadow: '0 6px 15px rgba(255, 107, 44, 0.3)',
                        },
                      }}
                    >
                      Créer un ticket
                    </Button>
                  )}
                </Box>
              </Paper>
            </Grid>
          ) : (
            filteredTickets.map((ticket: any) => (
              <Grid size={12} key={ticket.id}>
                <Paper 
                  elevation={2}
                  sx={{
                    borderRadius: '12px',
                    transition: 'all 0.3s ease',
                    overflow: 'hidden',
                    position: 'relative',
                    cursor: 'pointer',
                    border: isTicketUnanswered(ticket) ? '2px solid #e74c3c' : '1px solid rgba(0, 0, 0, 0.05)',
                    background: isTicketUnanswered(ticket) 
                      ? 'linear-gradient(145deg, #ffffff, #fff0f0)'
                      : 'linear-gradient(145deg, #ffffff, #FFF8F3)',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 10px 30px rgba(255, 107, 44, 0.1)',
                      borderColor: isTicketUnanswered(ticket) ? '#e74c3c' : 'rgba(255, 107, 44, 0.2)',
                      '&::after': {
                        opacity: 0.8,
                        width: '90%',
                        left: '5%'
                      }
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      bottom: 0,
                      width: '5px',
                      background: ticket.priority === 'urgente' 
                        ? '#e74c3c' 
                        : ticket.priority === 'elevee' 
                          ? '#f39c12' 
                          : ticket.priority === 'normale' 
                            ? '#FF965E' 
                            : '#2ecc71',
                      borderRadius: '4px 0 0 4px'
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      bottom: -6,
                      left: '10%',
                      width: '80%',
                      height: '10px',
                      background: 'radial-gradient(ellipse at center, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0) 70%)',
                      borderRadius: '50%',
                      zIndex: -1,
                      transition: 'all 0.3s ease',
                      opacity: 0.6
                    }
                  }}
                  onClick={() => handleTicketClick(ticket.id)}
                >
                  {isTicketUnanswered(ticket) && isAdminRoute && (
                    <Box 
                      sx={{
                        position: 'absolute',
                        top: 10,
                        right: 10,
                        zIndex: 5,
                        display: 'flex',
                        alignItems: 'center',
                        backgroundColor: 'rgba(231, 76, 60, 0.9)',
                        color: 'white',
                        borderRadius: '20px',
                        px: 1.5,
                        py: 0.5,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        boxShadow: '0 2px 8px rgba(231, 76, 60, 0.3)'
                      }}
                    >
                      <NotificationsActiveIcon sx={{ fontSize: '1rem', mr: 0.5 }} />
                      Non répondu
                    </Box>
                  )}
                  <Box sx={{ 
                    p: { xs: 2.5, sm: 3, md: 3.5 },
                    mx: { xs: 0.5, sm: 1 },
                    my: { xs: 0.5, sm: 1 }
                  }}>
                    <Grid container spacing={{ xs: 2.5, sm: 3 }} alignItems="center">
                      <Grid size={{ xs: 12, sm: 8 }}>
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          mb: 1,
                          position: 'relative',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: -12,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            background: ticket.priority === 'urgente' 
                              ? '#e74c3c' 
                              : ticket.priority === 'elevee' 
                                ? '#f39c12' 
                                : ticket.priority === 'normale' 
                                  ? '#FF965E' 
                                  : '#2ecc71',
                            boxShadow: `0 0 10px ${
                              ticket.priority === 'urgente' 
                                ? 'rgba(231, 76, 60, 0.5)' 
                                : ticket.priority === 'elevee' 
                                  ? 'rgba(243, 156, 18, 0.5)' 
                                  : ticket.priority === 'normale' 
                                    ? 'rgba(255, 150, 94, 0.5)' 
                                    : 'rgba(46, 204, 113, 0.5)'
                            }`
                          }
                        }}>
                          <Typography 
                            variant="h6" 
                            sx={{ 
                              ml: 1.5, 
                              fontWeight: 600,
                              fontSize: '1.1rem',
                              letterSpacing: '0.01em',
                              color: '#333',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              maxWidth: { xs: '100%', sm: '90%', md: '95%' }
                            }}
                          >
                            {ticket.title}
                          </Typography>
                        </Box>

                        {/* Description du ticket */}
                        <Box
                          sx={{
                            position: 'relative',
                            ml: 1.5,
                            mb: 3,
                            mt: 1,
                            pr: { xs: 0, sm: 1 }
                          }}
                        >
                          <Box 
                            sx={{
                              position: 'absolute',
                              top: -10,
                              left: 4,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              zIndex: 1
                            }}
                          >
                            <CommentIcon 
                              sx={{
                                fontSize: '1.1rem',
                                color: 'rgba(255, 107, 44, 0.8)',
                                transform: 'rotate(-8deg)'
                              }}
                            />
                          </Box>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontSize: '0.875rem',
                              color: '#555',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              lineHeight: 1.4,
                              maxWidth: { xs: '100%', sm: '95%', md: '95%' },
                              minHeight: '2.5rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.7)',
                              padding: '10px 12px 10px 18px',
                              borderRadius: '6px',
                              borderLeft: '3px solid rgba(255, 107, 44, 0.4)',
                              position: 'relative',
                              boxShadow: 'inset 0 1px 3px rgba(0, 0, 0, 0.03)',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                boxShadow: 'inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(255, 107, 44, 0.1)'
                              },
                              '&::after': ticket.description?.length > 120 ? {
                                content: '"..."',
                                position: 'absolute',
                                bottom: 2,
                                right: 4,
                                padding: '0 4px',
                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                borderRadius: '0 0 4px 0',
                                color: '#FF6B2C',
                                fontWeight: 'bold'
                              } : {},
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: -2,
                                left: -3,
                                height: '16px',
                                width: '3px',
                                backgroundColor: '#FF6B2C',
                                borderRadius: '2px',
                              }
                            }}
                          >
                            {ticket.description || "Aucune description disponible"}
                          </Typography>
                        </Box>

                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          flexWrap: 'wrap', 
                          gap: 1, 
                          mb: 1.5,
                          ml: 1.5
                        }}>
                          <TicketStatusBadge status={ticket.status} />
                          
                          {/* Ajout de la puce de priorité */}
                          <Chip
                            size="small"
                            label={ticket.priority}
                            sx={{ 
                              borderRadius: '8px',
                              backgroundColor: ticket.priority === 'urgente' 
                                ? '#e74c3c' 
                                : ticket.priority === 'elevee'
                                  ? '#f39c12'
                                  : ticket.priority === 'normale'
                                    ? '#3498db'
                                    : '#2ecc71',
                              color: 'white',
                              fontWeight: 600,
                              fontSize: '0.75rem',
                              height: '26px',
                              '& .MuiChip-label': {
                                px: 1.2
                              }
                            }}
                          />
                          
                          <Chip
                            size="small"
                            avatar={<Avatar sx={{
                              backgroundColor: 'rgba(255, 107, 44, 0.1)',
                              color: '#FF6B2C',
                              fontWeight: 'bold',
                              fontSize: '0.7rem'
                            }}>{getCategoryIcon(ticket.category)}</Avatar>}
                            label={ticket.category}
                            sx={{ 
                              borderRadius: '8px',
                              border: '1px solid rgba(255, 107, 44, 0.1)',
                              backgroundColor: 'rgba(255, 248, 243, 0.8)',
                              fontWeight: 500,
                              fontSize: '0.75rem',
                              height: '26px',
                              '& .MuiChip-label': {
                                px: 1.2
                              }
                            }}
                          />
                          {ticket.tags?.slice(0, 2).map((tag: any) => (
                            <Chip
                              key={tag.id}
                              size="small"
                              label={tag.name}
                              sx={{
                                borderRadius: '8px',
                                backgroundColor: tag.color || '#f0f0f0',
                                color: theme.palette.getContrastText(tag.color || '#f0f0f0'),
                                fontWeight: 500,
                                fontSize: '0.75rem',
                                height: '26px',
                                '& .MuiChip-label': {
                                  px: 1.2
                                }
                              }}
                            />
                          ))}
                          {ticket.tags?.length > 2 && (
                            <Chip
                              size="small"
                              label={`+${ticket.tags.length - 2}`}
                              sx={{
                                borderRadius: '8px',
                                backgroundColor: 'rgba(255, 107, 44, 0.1)',
                                color: '#FF6B2C',
                                fontWeight: 500,
                                fontSize: '0.75rem',
                                height: '26px',
                                '& .MuiChip-label': {
                                  px: 1.2
                                }
                              }}
                            />
                          )}
                        </Box>
                      </Grid>
                      <Grid size={{ xs: 12, sm: 4 }}>
                        <Box sx={{ 
                          textAlign: { xs: 'left', sm: 'right' },
                          mb: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: { xs: 'flex-start', sm: 'flex-end' }
                        }}>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontWeight: 500,
                              mb: 0.5,
                              display: 'flex',
                              alignItems: 'center',
                              color: '#555',
                              '& strong': {
                                fontWeight: 600,
                                color: '#333',
                                mr: 0.5
                              }
                            }}
                          >
                            <strong>Client:</strong> {ticket.user?.first_name || ''} {ticket.user?.last_name || ''} {ticket.user?.email ? `(${ticket.user.email})` : ''}
                          </Typography>
                        </Box>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            textAlign: { xs: 'left', sm: 'right' },
                            mb: { xs: 2, sm: 1.5 },
                            color: 'text.secondary',
                            fontStyle: 'italic',
                            fontSize: '0.8rem'
                          }}
                        >
                          {formatDistanceToNow(new Date(ticket.created_at), { addSuffix: true, locale: fr })}
                        </Typography>
                        <Box sx={{ 
                          textAlign: { xs: 'left', sm: 'right' }
                        }}>
                          <Button
                            variant="outlined"
                            size="small"
                            sx={{
                              borderRadius: '8px',
                              borderColor: '#FF6B2C',
                              color: '#FF6B2C',
                              fontWeight: 600,
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                borderColor: '#FF7A35',
                                backgroundColor: 'rgba(255, 107, 44, 0.04)',
                                transform: 'translateY(-2px)',
                                boxShadow: '0 4px 12px rgba(255, 107, 44, 0.15)'
                              }
                            }}
                          >
                            Voir détails
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>
              </Grid>
            ))
          )}
        </Grid>

        {/* Pagination */}
        {!loading && filteredTickets.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination
              count={(!totalCount || !limit) ? 1 : Math.ceil(totalCount / limit)}
              page={page}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
              sx={{
                '& .MuiPaginationItem-root': {
                  color: '#777',
                },
                '& .MuiPaginationItem-page.Mui-selected': {
                  backgroundColor: '#FF6B2C',
                  color: 'white',
                  fontWeight: 'bold',
                },
                '& .MuiPaginationItem-page:hover': {
                  backgroundColor: 'rgba(255, 107, 44, 0.1)',
                },
              }}
            />
          </Box>
        )}
      </Box>
      {selectedTicketId && (
        <TicketDetailsModal
          ticketId={selectedTicketId}
          isOpen={isTicketModalOpen}
          onClose={handleCloseTicketModal}
          isAdminRoute={isAdminRoute}
          onTicketUpdated={() => {
            // Cette fonction sera appelée depuis la modal quand un ticket est mis à jour
            // (par exemple quand un commentaire est ajouté)
            fetchTickets(filters);
          }}
        />
      )}
    </Box>
  );
};

export default SupportTicketList; 