import React, { useState } from 'react';
import { notify } from '../../../components/Notification';
import { X } from 'lucide-react';
import { invoiceService } from '../../../services/invoiceService';
import ModalPortal from '../../../components/ModalPortal';
import { motion } from 'framer-motion';
import DOMPurify from 'dompurify';

interface ClientFormProps {
  client?: any;
  onSave: () => void;
  onCancel: () => void;
}

const ClientForm: React.FC<ClientFormProps> = ({ client, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    nom: client?.nom || '',
    email: client?.email || '',
    telephone: client?.telephone || '',
    adresse: client?.adresse || '',
    siret: client?.siret || '',
    tva: client?.tva || '',
    notes: client?.notes || '',
    forme_juridique: client?.forme_juridique || '',
    code_ape: client?.code_ape || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: DOMPurify.sanitize(value)
    }));
    // Clear error when field is modified
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.nom.trim()) {
      newErrors.nom = 'Le nom est requis';
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email invalide';
    }
    
    if (formData.siret && !/^\d{14}$/.test(formData.siret)) {
      newErrors.siret = 'SIRET invalide (14 chiffres)';
    }

    if (formData.code_ape && !/^[0-9]{4}[a-zA-Z]?$/.test(formData.code_ape)) {
      newErrors.code_ape = 'Code APE invalide (format : 4 chiffres suivis éventuellement d\'une lettre, exemple : 3811Z)';
    }   
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (client?.id) {
        // Update existing client
        await invoiceService.updateClient(client.id, formData);
        notify('Client modifié avec succès', 'success');
      } else {
        // Create new client
        await invoiceService.createClient(formData);
        notify('Client créé avec succès', 'success');
      }
      onSave();
    } catch (error: any) {
      console.error('Erreur lors de l\'enregistrement du client:', error);
      notify(error.response.data.message || 'Erreur lors de l\'enregistrement du client', 'error');
    }
  };

  return (
    <ModalPortal isOpen={true} onBackdropClick={onCancel}>
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", duration: 0.3 }}
        className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        <div className="flex flex-col gap-2 mb-6 sticky top-0 bg-white z-10 pb-4">
          <div className="flex items-center gap-3">
            <span className="h-8 w-1 bg-gradient-to-b from-[#FF7A35] to-[#FF9F6B] rounded-full"></span>
            <h2 className="text-2xl font-bold text-gray-800">
              {client ? 'Modifier le client' : 'Nouveau client'}
            </h2>
            <button
              onClick={onCancel}
              className="ml-auto p-2 text-gray-500 hover:text-[#FF7A35] hover:bg-orange-50 rounded-full transition-colors"
            >
              <X size={24} />
            </button>
          </div>
          <p className="text-gray-600 text-sm pl-4">
            {client ? 'Modifiez les informations du client' : 'Remplissez les informations pour créer un nouveau client'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Nom ou raison sociale *
              </label>
              <input
                type="text"
                name="nom"
                value={formData.nom}
                onChange={handleChange}
                className={`w-full rounded-lg border ${
                  errors.nom ? 'border-red-500' : 'border-gray-300'
                } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]`}
                required
              />
              {errors.nom && (
                <p className="text-red-500 text-xs mt-1">{errors.nom}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Forme juridique
              </label>
              <input
                type="text"
                name="forme_juridique"
                value={formData.forme_juridique}
                onChange={handleChange}
                placeholder="SARL, SAS, EI, etc."
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full rounded-lg border ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Téléphone
              </label>
              <input
                type="tel"
                name="telephone"
                value={formData.telephone}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                SIRET
              </label>
              <input
                type="text"
                name="siret"
                value={formData.siret}
                onChange={handleChange}
                className={`w-full rounded-lg border ${
                  errors.siret ? 'border-red-500' : 'border-gray-300'
                } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]`}
              />
              {errors.siret && (
                <p className="text-red-500 text-xs mt-1">{errors.siret}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                Code APE
              </label>
              <input
                type="text"
                name="code_ape"
                value={formData.code_ape}
                onChange={handleChange}
                className={`w-full rounded-lg border ${
                  errors.code_ape ? 'border-red-500' : 'border-gray-300'
                } px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]`}
              />
              {errors.code_ape && (
                <p className="text-red-500 text-xs mt-1">{errors.code_ape}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-700">
                N° TVA
              </label>
              <input
                type="text"
                name="tva"
                value={formData.tva}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-700">
              Adresse
            </label>
            <textarea
              name="adresse"
              value={formData.adresse}
              onChange={handleChange}
              rows={3}
              className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-700">
              Notes internes
              <span className="ml-1 text-xs text-gray-500">(visible uniquement par vous)</span>
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              placeholder="Ajoutez des notes privées concernant ce client..."
              className="w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[#FF7A35] focus:border-transparent transition-all duration-200 hover:border-[#FF7A35]"
            />
          </div>

          <div className="flex justify-end space-x-4 pt-6">
            <motion.button
              type="button"
              onClick={onCancel}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-2.5 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium"
            >
              Annuler
            </motion.button>
            <motion.button
              type="submit"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-2.5 bg-[#FF7A35] text-white rounded-lg hover:bg-[#ff6b2c] transition-colors duration-200 font-medium shadow-md hover:shadow-lg"
            >
              {client ? 'Modifier' : 'Créer'}
            </motion.button>
          </div>
        </form>
      </motion.div>
    </ModalPortal>
  );
};

export default ClientForm; 